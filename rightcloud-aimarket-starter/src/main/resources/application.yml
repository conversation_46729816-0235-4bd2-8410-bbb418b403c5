spring:
  profiles: cloudstar
base-name: ${cloudstar.server.name:cloud-aimarket}
base-port: ${cloudstar.server.port:38188}
base-mgr-port: ${cloudstar.server.mgr.port:8081}
base-dbAddress: ${cloudstar.db.address:127.0.0.1}
base-dbPort: ${cloudstar.db.port:3306}
base-dbName: ${cloudstar.db.name:bss}
base-dbUserName: ${cloudstar.db.username:E<PERSON>@[PrFg0jg07iY33UyuBmQaIQ==]}
base-dbPassword: ${cloudstar.bss.db.password}
base-dbSsl: ${cloudstar.db.ssl:true}
base-dbUrl: jdbc:mariadb://${base-dbAddress}:${base-dbPort}/${base-dbName}?useUnicode=true&characterEncoding=UTF-8&allowMultiQueries=true&trustServerCertificate=${base-dbSsl}&useSSL=${base-dbSsl}&requireSSL=${base-dbSsl}&&useJDBCCompliantTimezoneShift=true&useLegacyDatetimeCode=false&serverTimezone=GMT%2B8&nullCatalogMeansCurrent=true
base-mongo-host: ${cloudstar.mongo.host:127.0.0.1}
base-mongo-dbName: ${cloudstar.mongo.dbname:rightcloud}
base-mongo-port: ${cloudstar.mongo.port:27019}
base-mongo-username: ${cloudstar.mongo.username:cloudstar}
base-mongo-password: ${cloudstar.mongodb.password:cloudstar}
base-mongo-ssl: ${cloudstar.mongodb.ssl.enabled:false}
base-mongo-algorithm: ${cloudstar.mongodb.ssl.algorithm:TLSv1.2}
base-mongo-jks-pwd: ${cloudstar.mongodb.client.keystore.password}
base-mongo-jks-path: ${cloudstar.keystore.mongodb.filepath}
base-mongo-cert-pwd: ${cloudstar.mongodb.client.p12.password}
base-mongo-cert-path: ${cloudstar.p12.mongodb.filepath}
root-logging-level: ${cloudstar.root.logging.level:info}
application-logging-level: ${cloudstar.application.logging.level:info}
base-mq-host: ${cloudstar.mq.host:127.0.0.1}
base-mq-port: ${cloudstar.mq.port:5672}
base-mq-username: ${cloudstar.mq.username:admin}
base-mq-password: ${mq.password}
base-mq-ssl-enabled: ${cloudstar.mq.ssl.enabled:false}
base-mq-ssl-key-store: ${cloudstar.mq.ssl.keyStore:classpath:mq/server.p12}
base-mq-ssl-key-store-password: ${mq.key.store.password}
base-mq-ssl-trust-store: ${cloudstar.mq.ssl.trustStore:classpath:mq/server.jks}
base-mq-ssl-trust-store-password: ${mq.trust.store.password}
base-mq-ssl-algorithm: ${cloudstar.mq.ssl.algorithm:TLSv1.2}
mybatis-logging-level: ${cloudstar.mybatis.logging.level:info}
base-redis-host: ${cloudstar.redis.host:127.0.0.1}
base-redis-port: ${cloudstar.redis.port:6379}
base-redis-password: ${cloudstar.redis.password}
base-redis-database: ${cloudstar.cloudstar.redis.database:0}
nacos-addr: ${cloudstar.nacos.addr:127.0.0.1:8848}
nacos-seata-enable: ${cloudstar.nacos.seata.enable:false}
nasoc-seata-tx-service-group: ${cloudstar.nacos.seata.tx.service.group:my_test_tx_group}
base-nacos-register: ${cloudstar.dubbo-register:true}
nacos-discovery-ephemeral: ${cloudstar.nacos.discovery.ephemeral:true}
nacos-discovery-heartbeat-interval: ${cloudstar.nacos.discovery.heartbeat.interval:30}
nacos-cache-file: ${cloudstar.nacos.cache.file:/output/dubbo}
nacos-username: ${cloudstar.nacos.username:nacos}
nacos-password: ${cloudstar.nacos.password:nacos}
file-storage-active: ${cloudstar.file.storage.active:minio}
file-storage-minio-initialize: ${cloudstar.file.storage.minio.initialize:true}
file-storage-minio-bucket-name: ${cloudstar.file.storage.minio.bucket.name:rightcloud}
file-storage-minio-intercept-url: ${cloudstar.file.storage.minio.intercept.url:minio-oss-storage}

cloudstar:
  db:
    password: ${cloudstar.bss.db.password}

---
server:
  port: ${base-port:38188}
  max-http-header-size: 128KB
  servlet:
    context-path: /api/v1/aimarket
    encoding:
      charset: UTF-8
      force: true
      enabled: true
  ssl:
    enabled: ${cloudstar.gateway.ssl.enable:false}
    enabled-protocols:
      - TLSv1.2
    ciphers:
      - TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384
      - TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384
      - TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256
      - TLS_ECDH_ECDSA_WITH_AES_256_GCM_SHA384
      - TLS_ECDH_RSA_WITH_AES_256_GCM_SHA384
      - TLS_ECDH_ECDSA_WITH_AES_128_GCM_SHA256
      - TLS_ECDH_RSA_WITH_AES_128_GCM_SHA256
      - TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256
    protocol: TLS
    key-store-type: ${cloudstar.keystore.type:JKS}
    key-store: ${cloudstar.keystore:classpath:server.jks}
    key-password: ${cloudstar.bss.customize.keystore.password}
    key-store-password: ${cloudstar.bss.customize.keystore.password}
  tomcat:
    uri-encoding: UTF-8
  address: ${cloudstar.local.podip:0.0.0.0}
spring:
  cloud:
    discovery:
      client:
        health-indicator:
          enabled: false
    nacos:
      username: ${nacos-username}
      password: ${nacos-password}
      discovery:
        server-addr: ${nacos-addr}
        register-enabled: true
        heart-beat-interval: 10
        heart-beat-timeout: 30
        naming-load-cache-at-start: true
        # 注册为https
        secure: ${cloudstar.gateway.ssl.enable:false}
      config:
        import-check:
          enabled: false
  main:
    #    懒加载置为true
    lazy-initialization: true
    banner-mode: off
    allow-circular-references: true
  application:
    name: ${base-name}
  aop:
    proxy-target-class: true
  jackson:
    default-property-inclusion: ALWAYS
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      fail_on_empty_beans: false

  datasource:
    # MySQL数据库配置
    name: ${base-dbName}
    type: com.zaxxer.hikari.HikariDataSource
    url: ${base-dbUrl}
    username: ${base-dbUserName}
    password: ${base-dbPassword}
    driver-class-name: org.mariadb.jdbc.Driver
    minIdle: 1
    maxActive: 200
    initialSize: 1
    timeBetweenEvictionRunsMillis: 3000
    minEvictableIdleTimeMillis: 300000
    validationQuery: SELECT 1
    testWhileIdle: true
    testOnBorrow: false
    testOnReturn: false
    poolPreparedStatements: false
    maxPoolPreparedStatementPerConnectionSize: 20
    connectionProperties: druid.stat.slowSqlMillis=5000
    removeAbandoned: true
    removeAbandonedTimeout: 3600

    mongodb:
      connection-string: mongodb://${base-mongo-host}:${base-mongo-port}/?&ssl=${base-mongo-ssl}&authMechanism=MONGODB-X509
      database: ${base-mongo-dbName:rightcloud}
      ssl: ${base-mongo-ssl:false}
      username: ${base-mongo-username:cloudstar}
      password: ${base-mongo-password:cloudstar}
      algorithm: ${base-mongo-algorithm}
      jks-pwd: ${base-mongo-jks-pwd}
      jks-path: ${base-mongo-jks-path}
      client-cert-pwd: ${base-mongo-cert-pwd}
      client-cert-path: ${base-mongo-cert-path}
  # Redis配置
  redis:
    host: ${base-redis-host}
    port: ${base-redis-port}
    password: ${base-redis-password}
    database: ${base-redis-database}

  servlet:
    multipart:
      enabled: true
      max-file-size: 20MB # 最大支持文件上传的大小
      max-request-size: 20MB #

  rabbitmq:
    host: ${base-mq-host}
    port: ${base-mq-port}
    username: ${base-mq-username}
    password: ${base-mq-password}
    ssl:
      enabled: ${base-mq-ssl-enabled}
      key-store: ${base-mq-ssl-key-store}
      key-store-password: ${base-mq-ssl-key-store-password}
      trust-store: ${base-mq-ssl-trust-store}
      trust-store-password: ${base-mq-ssl-trust-store-password}
      algorithm: ${base-mq-ssl-algorithm}
      validate-server-certificate: true
      verify-hostname: false


logging:
  level:
    root: ${root-logging-level:INFO}
    org.mybatis: ${mybatis-logging-level:warn}
    org.flowable.engine.impl.persistence.entity.*: debug
    org.flowable.task.service.impl.persistence.entity.*: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%X{SOFA-TraceId},%X{SOFA-SpanId}] [%X{account:-system}] %-5level %logger{36} - %msg%n"

endpoints:
  health:
    sensitive: false
  restart:
    enabled: true
  shutdown:
    enabled: true
  cors:
    allowed-origins: "*"
    allowed-headers: "*"
    allowed-methods:
      - "GET"
      - "POST"
      - "PUT"
      - "DELETE"
management:
  health:
    mongo:
      enabled: false
    nacos-discovery:
      enabled: false
    nacos-config:
      enabled: false
    rabbit:
      enabled: false
    redis:
      enabled: true
    db:
      enabled: false
    camunda:
      enabled: false
  security:
    enabled: false
  server:
    port: ${base-mgr-port:8081}
    servlet:
      context-path: /
    ssl:
      enabled: ${cloudstar.gateway.ssl.enable:false}
      enabled-protocols:
        - TLSv1.2
      ciphers:
        - TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384
        - TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384
        - TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256
        - TLS_ECDH_ECDSA_WITH_AES_256_GCM_SHA384
        - TLS_ECDH_RSA_WITH_AES_256_GCM_SHA384
        - TLS_ECDH_ECDSA_WITH_AES_128_GCM_SHA256
        - TLS_ECDH_RSA_WITH_AES_128_GCM_SHA256
        - TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256
      protocol: TLS
      key-store-type: ${cloudstar.keystore.type:JKS}
      key-store: ${cloudstar.keystore:classpath:server.jks}
      key-password: ${bss.csdr.keystore.password}
      key-store-password: ${bss.csdr.keystore.password}
  endpoints:
    health:
      enabled: true
      show-details: always
      status:
        order: up,down
        http-mapping:
          down: 500
    metrics:
      enabled: true
      tags:
        application: ${spring.application.name}
      export:
        prometheus:
          enabled: true
    web:
      base-path: /
      path-mapping:
        health: healthz
        prometheus: metrics
        metrics: bootmetrics
      exposure:
        include: health,metrics

customize:
  redis:
    enabled: true

# 宁波rabbitmq配置信息
ningbo:
  rabbitmq:
    host: ${cloudstar.ningbo.rabbitmq.host:************}
    port: ${cloudstar.ningbo.rabbitmq.port:5672}
    username: ${cloudstar.ningbo.rabbitmq.username:yszs}
    password: ${cloudstar.ningbo.rabbitmq.password:********************************}
    vhost: ${cloudstar.ningbo.rabbitmq.vhost:yszs-vhost}
    exchange: ${cloudstar.ningbo.rabbitmq.exchange:yszs-user-message-exchange}
    routingKey: ${cloudstar.ningbo.rabbitmq.routingKey:26542716c2434bc3ac6e46d8b6b2bb9a}
    queue: ${cloudstar.ningbo.rabbitmq.queue:yszs-user-message}
mybatis-plus:
  configuration:
    log-impl: ${log:org.apache.ibatis.logging.stdout.StdOutImpl}

camunda:
  bpm:
    job-execution:
      enabled: false
    auto-deployment-enabled: false

#storage
file:
  storage:
    minio:
      initialize: ${file-storage-minio-initialize}
      bucket-name: ${file-storage-minio-bucket-name}
      intercept-url: ${file-storage-minio-intercept-url}
    active: ${file-storage-active}
