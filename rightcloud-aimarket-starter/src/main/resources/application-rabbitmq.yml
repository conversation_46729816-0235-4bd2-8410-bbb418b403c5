spring:
  profiles: rabbitmq
base-mq-host: ${cloudstar.mq.host:127.0.0.1}
base-mq-port: ${cloudstar.mq.port:5672}
base-mq-username: ${cloudstar.mq.username:admin}
base-mq-password: ${cloudstar.mq.password}
base-mq-ssl-enabled: ${cloudstar.mq.ssl.enabled:false}
base-mq-ssl-key-store: ${cloudstar.mq.ssl.keyStore:classpath:mq/server.p12}
base-mq-ssl-key-store-password: ${cloudstar.mq.key.store.password}
base-mq-ssl-trust-store: ${cloudstar.mq.ssl.trustStore:classpath:mq/server.jks}
base-mq-ssl-trust-store-password: ${cloudstar.mq.trust.store.password}
base-mq-ssl-algorithm: ${cloudstar.mq.ssl.algorithm:TLSv1.2}
---

spring:
  config:
    import: classpath:application.yml
  rabbitmq:
    host: ${base-mq-host}
    port: ${base-mq-port}
    username: ${base-mq-username}
    password: ${base-mq-password}
    ssl:
      enabled: ${base-mq-ssl-enabled}
      key-store: ${base-mq-ssl-key-store}
      key-store-password: ${base-mq-ssl-key-store-password}
      trust-store: ${base-mq-ssl-trust-store}
      trust-store-password: ${base-mq-ssl-trust-store-password}
      algorithm: ${base-mq-ssl-algorithm}
      validate-server-certificate: true
      verify-hostname: false
