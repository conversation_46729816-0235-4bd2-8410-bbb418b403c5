<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:zeebe="http://camunda.org/schema/zeebe/1.0" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_1xjzuh6" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.8.0" modeler:executionPlatform="Camunda Cloud" modeler:executionPlatformVersion="8.1.0">
  <bpmn:process id="aimarket_shop_audit" name="模型集市_商品审批" isExecutable="true">
    <bpmn:extensionElements>
      <camunda:executionListener class="cn.com.cloudstar.rightcloud.common.camunda.StartEventExecutionListener" event="start" />
    </bpmn:extensionElements>
    <bpmn:startEvent id="startEventId" name="开始">
      <bpmn:outgoing>startEventOutFlowId</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="startEventOutFlowId" sourceRef="startEventId" targetRef="userTaskId" />
    <bpmn:userTask id="userTaskId" name="运营管理员审批">
      <bpmn:extensionElements>
        <camunda:executionListener class="cn.com.cloudstar.rightcloud.common.camunda.UserTaskExecutionListener" event="end" />
      </bpmn:extensionElements>
      <bpmn:incoming>startEventOutFlowId</bpmn:incoming>
      <bpmn:outgoing>userTaskOutFlowId</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:exclusiveGateway id="gatewayEventId" name="运营管理员审批">
      <bpmn:incoming>userTaskOutFlowId</bpmn:incoming>
      <bpmn:outgoing>openFlowId</bpmn:outgoing>
      <bpmn:outgoing>closedFlowId</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="userTaskOutFlowId" sourceRef="userTaskId" targetRef="gatewayEventId" />
    <bpmn:sequenceFlow id="openFlowId" name="通过" sourceRef="gatewayEventId" targetRef="executeServiceTaskId">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${audit == 'APPROVE'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask xmlns:camunda="http://camunda.org/schema/1.0/bpmn" id="executeServiceTaskId" name="执行" camunda:class="cn.com.cloudstar.rightcloud.common.camunda.PassDelegateExecution" camunda:delegateExpression="passDelegateExecution">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="=&#34;order-&#34; + priorityGroup" />
      </bpmn:extensionElements>
      <bpmn:incoming>openFlowId</bpmn:incoming>
      <bpmn:outgoing>executeOutFlowId</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="closedFlowId" name="拒绝" sourceRef="gatewayEventId" targetRef="refuseServiceTaskId">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${audit == 'REFUSE'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="endEventId" name="结束">
      <bpmn:extensionElements>
        <camunda:executionListener class="cn.com.cloudstar.rightcloud.common.camunda.EndEventExecutionListener" event="end" />
      </bpmn:extensionElements>
      <bpmn:incoming>executeOutFlowId</bpmn:incoming>
      <bpmn:incoming>refuseOutFlowId</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="executeOutFlowId" sourceRef="executeServiceTaskId" targetRef="endEventId" />
    <bpmn:sequenceFlow id="refuseOutFlowId" sourceRef="refuseServiceTaskId" targetRef="endEventId" />
    <bpmn:serviceTask xmlns:camunda="http://camunda.org/schema/1.0/bpmn" id="refuseServiceTaskId" name="拒绝并关闭" camunda:class="cn.com.cloudstar.rightcloud.common.camunda.RefuseDelegateExecution" camunda:delegateExpression="refuseDelegateExecution">
      <bpmn:extensionElements>
        <zeebe:taskDefinition type="&#34;order-&#34; + priorityGroup" />
      </bpmn:extensionElements>
      <bpmn:incoming>closedFlowId</bpmn:incoming>
      <bpmn:outgoing>refuseOutFlowId</bpmn:outgoing>
    </bpmn:serviceTask>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="aimarket_shop_audit">
      <bpmndi:BPMNEdge id="BPMNShape_refuseOutFlowId" bpmnElement="refuseOutFlowId">
        <di:waypoint x="770" y="240" />
        <di:waypoint x="940" y="240" />
        <di:waypoint x="940" y="145" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNShape_executeOutFlowId" bpmnElement="executeOutFlowId">
        <di:waypoint x="770" y="127" />
        <di:waypoint x="922" y="127" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNShape_closedFlowId" bpmnElement="closedFlowId">
        <di:waypoint x="500" y="152" />
        <di:waypoint x="500" y="240" />
        <di:waypoint x="670" y="240" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="504" y="193" width="23" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNShape_openFlowId" bpmnElement="openFlowId">
        <di:waypoint x="525" y="127" />
        <di:waypoint x="670" y="127" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="586" y="109" width="23" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNShape_userTaskOutFlowId" bpmnElement="userTaskOutFlowId">
        <di:waypoint x="370" y="127" />
        <di:waypoint x="475" y="127" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="BPMNShape_startEventOutFlowId" bpmnElement="startEventOutFlowId">
        <di:waypoint x="215" y="127" />
        <di:waypoint x="270" y="127" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNShape id="BPMNShape_startEventId" bpmnElement="startEventId">
        <dc:Bounds x="179" y="109" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="186" y="152" width="23" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_userTaskId" bpmnElement="userTaskId">
        <dc:Bounds x="270" y="87" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_gatewayEventId" bpmnElement="gatewayEventId" isMarkerVisible="true">
        <dc:Bounds x="475" y="102" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="461" y="78" width="78" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_executeServiceTaskId" bpmnElement="executeServiceTaskId">
        <dc:Bounds x="670" y="87" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_endEventId" bpmnElement="endEventId">
        <dc:Bounds x="922" y="109" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="929" y="79" width="23" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_refuseServiceTaskId" bpmnElement="refuseServiceTaskId">
        <dc:Bounds x="670" y="200" width="100" height="80" />
      </bpmndi:BPMNShape>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
