#spring:
#  profiles: mongo
#base-mongo-host: ${cloudstar.mongo.host:127.0.0.1}
#base-mongo-dbName: ${cloudstar.mongo.dbname:rightcloud}
#base-mongo-port: ${cloudstar.mongo.port:27019}
#base-mongo-username: ${cloudstar.mongo.username:cloudstar}
#base-mongo-password: ${cloudstar.mongodb.password:cloudstar}
#base-mongo-ssl: ${cloudstar.mongodb.ssl.enabled:false}
#base-mongo-algorithm: ${cloudstar.mongodb.ssl.algorithm:TLSv1.2}
#base-mongo-jks-pwd: ${cloudstar.mongodb.client.keystore.password}
#base-mongo-jks-path: ${cloudstar.keystore.mongodb.filepath}
#base-mongo-cert-pwd: ${cloudstar.mongodb.client.p12.password}
#base-mongo-cert-path: ${cloudstar.p12.mongodb.filepath}
#---
#spring:
#  config:
#    import: classpath:application.yml
#
#  # MySQL数据库配置
#  datasource:
#    mongodb:
#      connection-string: mongodb://${base-mongo-host}:${base-mongo-port}/?&ssl=${base-mongo-ssl}&authMechanism=MONGODB-X509
#      database: ${base-mongo-dbName:rightcloud}
#      ssl: ${base-mongo-ssl}
#      username: ${base-mongo-username}
#      password: ${base-mongo-password}
#      algorithm: ${base-mongo-algorithm}
#      jks-pwd: ${base-mongo-jks-pwd}
#      jks-path: ${base-mongo-jks-path}
#      client-cert-pwd: ${base-mongo-cert-pwd}
#      client-cert-path: ${base-mongo-cert-path}