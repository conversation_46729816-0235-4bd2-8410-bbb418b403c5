package cn.com.cloudstar;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * rightcloud定制模块应用程序
 *
 * <AUTHOR>
 * @date 2022/11/07
 */
//@MapperScan("cn.com.cloudstar.rightcloud.customize.dao")
@ServletComponentScan("cn.com.cloudstar")
@MapperScan(basePackages = {"cn.com.cloudstar.rightcloud.customize.dao", "cn.com.cloudstar.rightcloud.data.dao","cn.com.cloudstar.rightcloud.basic.data.dao"})
@EnableDiscoveryClient
@SpringBootApplication(exclude = DataSourceAutoConfiguration.class)
@EnableFeignClients(basePackages = "cn.com.cloudstar")
@EnableScheduling
@EnableAspectJAutoProxy(exposeProxy = true)
public class RightcloudCustomizeModuleApplication {

	public static void main(String[] args) {
		SpringApplication.run(RightcloudCustomizeModuleApplication.class, args);
	}

}
