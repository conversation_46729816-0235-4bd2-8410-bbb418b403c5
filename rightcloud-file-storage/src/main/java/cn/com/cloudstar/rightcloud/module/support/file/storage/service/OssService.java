/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.module.support.file.storage.service;


import cn.com.cloudstar.rightcloud.module.support.file.storage.config.OssConfig;

/**
 * oss配置 T：配置文件 K：客户端
 *
 * <AUTHOR>
 * @date 2022/3/15
 */
public interface OssService<T, K> {


    /**
     * 获取客户端
     *
     * <AUTHOR>
     * @date 2022/3/15
     */
    K getClient();

    /**
     * 获取配置文件
     *
     * <AUTHOR>
     * @date 2022/3/15
     */
    T getConfig();


    /**
     * 修改配置文件，重新初始化
     *
     * <AUTHOR>
     * @date 2022/3/15
     */
    void setConfig(OssConfig ossConfig);


    /**
     * 获取路径前缀-绝对路径前缀 minio: 地址 + bucket  例如：http://127.0.0.1:8080/test/
     *
     * <AUTHOR>
     * @date 2022/3/30
     */
    String getPrefix();

}
