/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.module.support.file.storage.utils;


import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.module.support.file.storage.bean.enums.StoragePathEnum;
import cn.com.cloudstar.rightcloud.module.support.file.storage.bean.vo.Base64Data;
import cn.com.cloudstar.rightcloud.module.support.file.storage.bean.vo.StorageResult;
import cn.hutool.core.io.file.FileNameUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.io.FilenameUtils;
import org.springframework.util.Base64Utils;
import org.springframework.util.ObjectUtils;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.zip.Adler32;
import java.util.zip.CheckedOutputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Slf4j
public class StorageUtil {

    private static final String COMMON_DATE = "yyyyMMdd";

    public static final String separator = "/";

    /**
     *
     */
    private static final Map<String, String> MIME_TO_SUFFIX_MAP = new HashMap<String, String>() {{
        put("data:image/jpeg;", ".jpg");
        put("data:image/png;", ".png");
        put("data:application/msword;", ".doc");
        put("data:application/vnd.openxmlformats-officedocument.wordprocessingml.document;", ".docx");
        put("data:application/pdf;", ".pdf");
        put("data:application/vnd.ms-excel;", ".xls");
        put("data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;", ".xlsx");
        put("data:image/x-icon;", ".ico");
        put("data:image/gif;", ".gif");
    }};
    /**
     * 生成根目录
     *
     * @param prefix 目录（相应业务简称/英文） example: test/picture
     * @param timeDirectory 是否添加时间目录
     *
     * @return test/picture/20220310
     *
     * <AUTHOR>
     * @date 2022/3/10
     */
    public static String createRootDirectory(String prefix, boolean timeDirectory, String dateFormat) {
        if (!timeDirectory) {
            return prefix;
        }
        long time = System.currentTimeMillis();
        SimpleDateFormat format = new SimpleDateFormat(COMMON_DATE);
        String path = format.format(time);
        if (!ObjectUtils.isEmpty(dateFormat)) {
            path = new SimpleDateFormat(dateFormat).format(time);
        }
        if (ObjectUtils.isEmpty(prefix)) {
            return path;
        }
        return prefix + separator + path;
    }

    /**
     * 生成文件名
     *
     * @param fileName 文件名
     * @param useOrigin 是否使用原名
     *
     * <AUTHOR>
     * @date 2022/3/10
     */
    public static String createFileName(String fileName, boolean useOrigin) {
        String randomStr = UUID.randomUUID().toString().replaceAll("-", "").substring(0, 20);
        if (ObjectUtils.isEmpty(fileName)) {
            return randomStr;
        }
        String suffix = FileNameUtil.getSuffix(fileName);
        if (ObjectUtils.isEmpty(suffix)) {
            return randomStr;
        }
        if (!useOrigin) {
            return randomStr + "." + suffix.toLowerCase();
        }else {
            return FileNameUtil.getPrefix(fileName) + "." + suffix.toLowerCase();
        }

    }


    /**
     * 解析base64数据
     *
     * @param base64Data base64数据字符串
     * <AUTHOR>
     * @date 2022/3/10
     */
    public static Base64Data decodeBase64(String base64Data, String path) {
        byte[] bs = null;
        String suffix = "";
        String contentType = "";
        try {
            String dataPrix = "";
            String data = "";
            if (ObjectUtils.isEmpty(base64Data)) {
                throw new Exception(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1827679030));
            }
            String[] d = base64Data.split("base64,");
            if (d.length != 2) {
                throw new Exception(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_752128954));
            }
            dataPrix = d[0];
            data = d[1];

            if ("auth".equals(path) || StoragePathEnum.ID_CARD.getPrefix().equals(path) || StoragePathEnum.PICTURE.getPrefix().equals(path)) {
                if ("data:image/jpeg;".equalsIgnoreCase(dataPrix)) {
                    suffix = ".jpg";
                } else if ("data:image/png;".equalsIgnoreCase(dataPrix)) {
                    suffix = ".png";
                } else {
                    throw new Exception(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_592303896));
                }
            }
            suffix = MIME_TO_SUFFIX_MAP.get(dataPrix.toLowerCase());
            if (suffix == null) {
                throw new Exception(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_592303896));
            }
            contentType = dataPrix.replace("data:","").replace(";","");
            //因为BASE64Decoder的jar问题，此处使用spring框架提供的工具包
            bs = Base64Utils.decodeFromString(data);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return Base64Data.builder().suffix(suffix).base64Data(bs).contentType(contentType).build();
    }

    /**
     * 压缩文件
     * @param files 文件
     * @param directory 需压缩的文件目录
     * <AUTHOR>
     * @date 2022/3/18
     */
    public static InputStream compressedFiles(List<StorageResult> files,String directory) throws Exception {
        //创建临时zip文件
        File zip = File.createTempFile(UUID.randomUUID().toString().replaceAll("-", ""), "zip");
        FileOutputStream out = null;
        CheckedOutputStream cos = null;
        ZipOutputStream zos = null;
        try {
            out = new FileOutputStream(zip);
            cos = new CheckedOutputStream(out, new Adler32());
            //用于将数据压缩成Zip文件格式
            zos = new ZipOutputStream(cos);
            for (StorageResult it : files) {
                writeToZip(directory, zos, it);
            }
            zos.finish();
            return new FileInputStream(zip);
        }catch (Exception e){
            throw new Exception(e.getMessage());
        }finally {
            IOUtils.closeQuietly(cos);
            IOUtils.closeQuietly(out);
            IOUtils.closeQuietly(zos);
            Files.delete(Paths.get(FilenameUtils.getName(zip.getPath())));
        }
    }

    private static void writeToZip(String directory, ZipOutputStream zos, StorageResult it) {
        try {
            if (ObjectUtils.isEmpty(directory)){
                zos.putNextEntry(new ZipEntry(it.getFileName()));
            }else {
                String[] split = it.getRelativeUrl().split(directory);
                zos.putNextEntry(new ZipEntry(split[split.length - 1]));
            }
            int count = 0;
            InputStream in = it.getInputStream();
            while ((count = in.read()) != -1) {
                zos.write(count);
            }
            it.getInputStream().close();
            zos.closeEntry();
        } catch (IOException e) {
            log.error(e.getMessage());
        }
    }


    /**
     * 读取流信息为字符串
     *
     * @param inputStream 文件
     *
     * <AUTHOR>
     * @date 2022/3/18
     */
    public static String readInputStreamToStr(InputStream inputStream){
        InputStreamReader in = new InputStreamReader(inputStream);
        return new BufferedReader(in)
                .lines().collect(Collectors.joining(System.lineSeparator()));
    }



}
