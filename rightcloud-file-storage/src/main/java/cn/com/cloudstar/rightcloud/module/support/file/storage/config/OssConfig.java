/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.module.support.file.storage.config;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "oss配置")
public class OssConfig {

    @ApiModelProperty("默认桶")
    private String bucketName;

    @ApiModelProperty("url")
    private String endpoint;

    private String externalEndPoint;

    @ApiModelProperty("nginx静态资源拦截路径")
    private String interceptUrl;

    @ApiModelProperty("公钥")
    private String accessKey;

    @ApiModelProperty("私钥")
    private String secretKey;

}
