/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.module.support.file.storage.bean.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Builder
@ApiModel(description = "base64数据返回类")
public class Base64Data {

    @ApiModelProperty("文件后缀")
    private String suffix;

    @ApiModelProperty("文件contentType")
    private String contentType;

    @ApiModelProperty("base64数据")
    private byte[] base64Data;

}
