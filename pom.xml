<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>rightcloud-oss</artifactId>
        <groupId>cn.com.cloudstar</groupId>
        <version>vboss.2.6.0-sec-dg-poc-SNAPSHOT</version>
    </parent>
    <groupId>cn.com.cloudstar</groupId>
    <artifactId>rightcloud-bss</artifactId>
    <name>rightcloud-bss</name>
    <description>Rightcloud Business Support System</description>

    <dependencies>
        <dependency>
            <groupId>cn.com.cloudstar</groupId>
            <artifactId>rightcloud-common-validator</artifactId>
            <version>${cmp.common.version}</version>
        </dependency>
        <dependency>
            <artifactId>rightcloud-action-filter</artifactId>
            <groupId>cn.com.cloudstar</groupId>
        </dependency>
        <dependency>
            <artifactId>rightcloud-basic-data-core-facade</artifactId>
            <groupId>cn.com.cloudstar</groupId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-expression</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-expression</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <artifactId>rightcloud-access</artifactId>
            <groupId>cn.com.cloudstar</groupId>
            <version>${cmp.module.support.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.com.cloudstar</groupId>
            <artifactId>rightcloud-core</artifactId>
            <version>${project.parent.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>okio</artifactId>
                    <groupId>com.squareup.okio</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>shiro-core</artifactId>
                    <groupId>org.apache.shiro</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>rightcloud-access</artifactId>
                    <groupId>cn.com.cloudstar</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-httpclient</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-jackson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sndyuk</groupId>
            <artifactId>logback-more-appenders</artifactId>
        </dependency>
        <dependency>
            <groupId>org.fluentd</groupId>
            <artifactId>fluent-logger</artifactId>
        </dependency>
        <dependency>
            <groupId>org.msgpack</groupId>
            <artifactId>msgpack</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
        </dependency>

        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcpkix-jdk15on</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-openfeign-core</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-fileupload</artifactId>
                    <groupId>commons-fileupload</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <!-- 排除Tomcat依赖 -->
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-core</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-tomcat</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-redis</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-oxm</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-oxm</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <!-- spring2mybatis need jar -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-annotation</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-extension</artifactId>
        </dependency>

        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-api</artifactId>
        </dependency>
        <!--hutool-->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <!--Guava-->
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>

        <!--fastjson-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

        <!--   加密     -->
        <dependency>
            <groupId>com.esotericsoftware</groupId>
            <artifactId>kryo</artifactId>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcpkix-jdk18on</artifactId>
        </dependency>

        <!-- swagger -->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
        </dependency>

        <dependency>
            <groupId>io.seata</groupId>
            <artifactId>seata-all</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>druid</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-jcl</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-beans</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>checker-qual</artifactId>
                    <groupId>org.checkerframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>error_prone_annotations</artifactId>
                    <groupId>com.google.errorprone</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jcl</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-beans</artifactId>
            <version>${spring.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- jxl -->
        <dependency>
            <groupId>org.jxls</groupId>
            <artifactId>jxls</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.com.cloudstar</groupId>
            <artifactId>rightcloud-remote-api-iam</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>javax.ws.rs</groupId>
                    <artifactId>jsr311-api</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>nacos-client</artifactId>
                    <groupId>com.alibaba.nacos</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-seata</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.yaml</groupId>
                    <artifactId>snakeyaml</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-context</artifactId>
                    <groupId>org.springframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>netty-all</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.cloudstar</groupId>
            <artifactId>rightcloud-common-dubbo</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.cloudstar</groupId>
            <artifactId>rightcloud-remote-api-resource</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.cloudstar</groupId>
            <artifactId>rightcloud-basic-data-pojo</artifactId>
            <version>${cmp.module.support.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>rightcloud-access</artifactId>
                    <groupId>cn.com.cloudstar</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.com.cloudstar</groupId>
            <artifactId>rightcloud-file-storage</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.cloudstar</groupId>
            <artifactId>rightcloud-logs</artifactId>
        </dependency>
        <!-- easyexcel -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-loadbalancer</artifactId>
        </dependency>
        <dependency>
            <groupId>org.owasp.esapi</groupId>
            <artifactId>esapi</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.cloudstar</groupId>
            <artifactId>rightcloud-biz-common</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-sleuth</artifactId>
        </dependency>
        <dependency>
            <groupId>io.zipkin.brave</groupId>
            <artifactId>brave-instrumentation-dubbo</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.cloudstar</groupId>
            <artifactId>rightcloud-remote-api-system</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.cloudstar</groupId>
            <artifactId>rightcloud-common</artifactId>
        </dependency>
        <!-- 集成支付SDK简化微信支付调用，github地址：https://github.com/Pay-Group/best-pay-sdk-->
        <dependency>
            <groupId>cn.springboot</groupId>
            <artifactId>best-pay-sdk</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.tomcat.embed</groupId>
                    <artifactId>tomcat-embed-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- netty -->
        <dependency>
            <artifactId>netty-all</artifactId>
            <groupId>io.netty</groupId>
            <version>${netty-all.version}</version>
        </dependency>
        <dependency>
            <artifactId>netty-transport</artifactId>
            <groupId>io.netty</groupId>
            <version>${netty-all.version}</version>
        </dependency>
        <dependency>
            <artifactId>netty-transport-udt</artifactId>
            <groupId>io.netty</groupId>
            <version>${netty-all.version}</version>
        </dependency>
        <dependency>
            <artifactId>netty-transport-rxtx</artifactId>
            <groupId>io.netty</groupId>
            <version>${netty-all.version}</version>
        </dependency>
        <dependency>
            <artifactId>netty-transport-sctp</artifactId>
            <groupId>io.netty</groupId>
            <version>${netty-all.version}</version>
        </dependency>
        <dependency>
            <artifactId>netty-resolver</artifactId>
            <groupId>io.netty</groupId>
            <version>${netty-all.version}</version>
        </dependency>
        <dependency>
            <artifactId>netty-resolver-dns</artifactId>
            <groupId>io.netty</groupId>
            <version>${netty-all.version}</version>
        </dependency>
        <dependency>
            <artifactId>netty-resolver-dns-classes-macos</artifactId>
            <groupId>io.netty</groupId>
            <version>${netty-all.version}</version>
        </dependency>
        <dependency>
            <artifactId>netty-buffer</artifactId>
            <groupId>io.netty</groupId>
            <version>${netty-all.version}</version>
        </dependency>
        <dependency>
            <artifactId>netty-codec-haproxy</artifactId>
            <groupId>io.netty</groupId>
            <version>${netty-all.version}</version>
        </dependency>
        <dependency>
            <artifactId>netty-codec-mqtt</artifactId>
            <groupId>io.netty</groupId>
            <version>${netty-all.version}</version>
        </dependency>
        <dependency>
            <artifactId>netty-codec-xml</artifactId>
            <groupId>io.netty</groupId>
            <version>${netty-all.version}</version>
        </dependency>
        <dependency>
            <artifactId>netty-codec</artifactId>
            <groupId>io.netty</groupId>
            <version>${netty-all.version}</version>
        </dependency>
        <dependency>
            <artifactId>netty-codec-dns</artifactId>
            <groupId>io.netty</groupId>
            <version>${netty-all.version}</version>
        </dependency>
        <dependency>
            <artifactId>netty-codec-http2</artifactId>
            <groupId>io.netty</groupId>
            <version>${netty-all.version}</version>
        </dependency>
        <dependency>
            <artifactId>netty-codec-socks</artifactId>
            <groupId>io.netty</groupId>
            <version>${netty-all.version}</version>
        </dependency>
        <dependency>
            <artifactId>netty-codec-http</artifactId>
            <groupId>io.netty</groupId>
            <version>${netty-all.version}</version>
        </dependency>
        <dependency>
            <artifactId>netty-codec-redis</artifactId>
            <groupId>io.netty</groupId>
            <version>${netty-all.version}</version>
        </dependency>
        <dependency>
            <artifactId>netty-codec-memcache</artifactId>
            <groupId>io.netty</groupId>
            <version>${netty-all.version}</version>
        </dependency>
        <dependency>
            <artifactId>netty-codec-smtp</artifactId>
            <groupId>io.netty</groupId>
            <version>${netty-all.version}</version>
        </dependency>
        <dependency>
            <artifactId>netty-codec-stomp</artifactId>
            <groupId>io.netty</groupId>
            <version>${netty-all.version}</version>
        </dependency>
        <dependency>
            <artifactId>netty-common</artifactId>
            <groupId>io.netty</groupId>
            <version>${netty-all.version}</version>
        </dependency>
        <dependency>
            <artifactId>netty-transport-classes-epoll</artifactId>
            <groupId>io.netty</groupId>
            <version>${netty-all.version}</version>
        </dependency>
        <dependency>
            <artifactId>netty-transport-classes-kqueue</artifactId>
            <groupId>io.netty</groupId>
            <version>${netty-all.version}</version>
        </dependency>
        <dependency>
            <artifactId>netty-transport-native-unix-common</artifactId>
            <groupId>io.netty</groupId>
            <version>${netty-all.version}</version>
        </dependency>
        <dependency>
            <artifactId>netty-handler</artifactId>
            <groupId>io.netty</groupId>
            <version>${netty-all.version}</version>
        </dependency>
        <dependency>
            <artifactId>netty-handler-proxy</artifactId>
            <groupId>io.netty</groupId>
            <version>${netty-all.version}</version>
        </dependency>
        <dependency>
            <artifactId>netty-handler-ssl-ocsp</artifactId>
            <groupId>io.netty</groupId>
            <version>${netty-all.version}</version>
        </dependency>
        <dependency>
            <groupId>com.huaweicloud</groupId>
            <artifactId>esdk-obs-java</artifactId>
        </dependency>
        <dependency>
            <groupId>com.opencsv</groupId>
            <artifactId>opencsv</artifactId>
        </dependency>

    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <filtering>false</filtering>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/*.yaml</include>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                    <include>**/*.json</include>
                    <include>**/*.Filter</include>
                    <include>**/*.RouterFactory</include>
                </includes>
                <excludes>
                    <exclude>smart/smart-doc.json</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <includes>
                    <include>**/*.xlsx</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <excludes>
                    <exclude>**/*.Keystore</exclude>
                    <exclude>**/*.cer</exclude>
                    <exclude>**/*.*.p12</exclude>
                    <exclude>**/*.jks</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <includes>
                    <include>**/*.Keystore</include>
                    <include>**/*.cer</include>
                    <include>**/*.*.p12</include>
                    <include>**/*.jks</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>Keystore</nonFilteredFileExtension>
                        <nonFilteredFileExtension>cer</nonFilteredFileExtension>
                        <nonFilteredFileExtension>p12</nonFilteredFileExtension>
                        <nonFilteredFileExtension>jks</nonFilteredFileExtension>
                        <nonFilteredFileExtension>xlsx</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.github.shalousun</groupId>
                <artifactId>smart-doc-maven-plugin</artifactId>
                <version>2.6.6</version>
                <configuration>
                    <!--指定生成文档的使用的配置文件,配置文件放在自己的项目中-->
                    <configFile>./src/main/resources/smart/smart-doc.json</configFile>
                    <!--指定项目名称-->
                    <projectName>bss</projectName>
                    <!--smart-doc实现自动分析依赖树加载第三方依赖的源码，如果一些框架依赖库加载不到导致报错，这时请使用excludes排除掉-->
                    <excludes>
                        <!--格式为：groupId:artifactId;参考如下-->
                        <!--也可以支持正则式如：com.alibaba:.* -->
                        <exclude>com.alibaba:fastjson</exclude>
                    </excludes>
                    <!--includes配置用于配置加载外部依赖源码,配置后插件会按照配置项加载外部源代码而不是自动加载所有，因此使用时需要注意-->
                    <!--smart-doc能自动分析依赖树加载所有依赖源码，原则上会影响文档构建效率，因此你可以使用includes来让插件加载你配置的组件-->
                    <includes>
                        <!-- 使用了mybatis-plus的Page分页需要include所使用的源码包 -->
                        <include>com.baomidou:mybatis-plus-extension</include>
                        <!-- 使用了mybatis-plus的IPage分页需要include mybatis-plus-core-->
                        <include>com.baomidou:mybatis-plus-core</include>
                        <!-- 如果配置了includes的情况下， 使用了jpa的分页需要include所使用的源码包 -->
                        <include>org.springframework.data:spring-data-commons</include>
                    </includes>
                </configuration>
                <executions>
                    <execution>
                        <!--如果不需要在执行编译时启动smart-doc，则将phase注释掉-->
<!--                        <phase>compile</phase>-->
                        <goals>
                            <!--smart-doc提供了html、openapi、markdown等goal，可按需配置-->
                            <goal>html</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>com.github.spotbugs</groupId>
                <artifactId>spotbugs-maven-plugin</artifactId>
                <configuration>
                    <htmlOutput>true</htmlOutput>
                    <timeout>12000000</timeout>
                    <maxHeap>2048</maxHeap>
                    <!-- Optional directory to put spotbugs xdoc xml report -->
                    <outputDirectory>target/site</outputDirectory>
                    <excludeFilterFile>spotbugs-exclude-filter.xml</excludeFilterFile>
                    <plugins>
                        <plugin>
                            <groupId>com.h3xstream.findsecbugs</groupId>
                            <artifactId>findsecbugs-plugin</artifactId>
                            <version>1.12.0</version>
                        </plugin>
                    </plugins>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
