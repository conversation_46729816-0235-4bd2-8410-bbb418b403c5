<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<modules>
		<module>rightcloud-aimarket-common</module>
		<module>rightcloud-aimarket-data</module>
		<module>rightcloud-aimarket-facade</module>
		<module>rightcloud-aimarket-interface</module>
		<module>rightcloud-aimarket-service</module>
		<module>rightcloud-aimarket-starter</module>
		<module>rightcloud-aimarket-web</module>
	</modules>
	<parent>
		<groupId>cn.com.cloudstar</groupId>
		<artifactId>rightcloud-parent</artifactId>
		<version>vboss.2.6.0-sec-dg-poc-SNAPSHOT</version>
	</parent>
	<packaging>pom</packaging>
	<artifactId>rightcloud-aimarket-module</artifactId>
	<version>vboss.2.6.0-sec-dg-poc-SNAPSHOT</version>
	<name>rightcloud-aimarket-module</name>
	<description>rightcloud-aimarket-module</description>
	<properties>
		<java.version>1.8</java.version>
		<maven.compiler.source>8</maven.compiler.source>
		<maven.compiler.target>8</maven.compiler.target>
	</properties>
	<dependencies>
        <!-- adapter发送消息的-->
        <dependency>
            <groupId>cn.com.cloudstar</groupId>
            <artifactId>rightcloud-adapter-core</artifactId>
            <version>${cmp.cloud.driver.version}</version>
        </dependency>
        <!-- 公共模块-->
        <dependency>
            <groupId>cn.com.cloudstar</groupId>
            <artifactId>rightcloud-biz-common</artifactId>
            <version>${cmp.module.support.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.quartz-scheduler</groupId>
                    <artifactId>quartz</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.amqp</groupId>
            <artifactId>spring-rabbit</artifactId>
        </dependency>



		<dependency>
			<groupId>cn.com.cloudstar</groupId>
			<artifactId>rightcloud-basic-data-pojo</artifactId>
			<version>${cmp.module.support.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>rightcloud-access</artifactId>
					<groupId>cn.com.cloudstar</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<artifactId>rightcloud-access</artifactId>
			<groupId>cn.com.cloudstar</groupId>
			<version>${cmp.module.support.version}</version>
		</dependency>

		<dependency>
			<groupId>cn.com.cloudstar</groupId>
			<artifactId>rightcloud-authority</artifactId>
			<version>${cmp.module.support.version}</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-validation</artifactId>
		</dependency>
		<dependency>
			<groupId>org.mybatis.spring.boot</groupId>
			<artifactId>mybatis-spring-boot-starter</artifactId>
		</dependency>

        <dependency>
            <groupId>org.mariadb.jdbc</groupId>
            <artifactId>mariadb-java-client</artifactId>
        </dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-configuration-processor</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<optional>true</optional>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>

		<!--hutool-->
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-redis</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-mongodb</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<!-- 排除Tomcat依赖 -->
			<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-tomcat</artifactId>
				</exclusion>
			</exclusions>
		</dependency>


		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-tomcat</artifactId>
		</dependency>



		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-annotation</artifactId>
		</dependency>
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-extension</artifactId>
		</dependency>



		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
			<exclusions>
				<exclusion>
					<groupId>javax.ws.rs</groupId>
					<artifactId>jsr311-api</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>nacos-client</artifactId>
					<groupId>com.alibaba.nacos</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.alibaba.nacos</groupId>
			<artifactId>nacos-client</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.yaml</groupId>
					<artifactId>snakeyaml</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-loadbalancer</artifactId>
		</dependency>

		<dependency>
			<groupId>org.owasp.esapi</groupId>
			<artifactId>esapi</artifactId>
		</dependency>

		<dependency>
			<groupId>io.micrometer</groupId>
			<artifactId>micrometer-core</artifactId>
		</dependency>
		<dependency>
			<groupId>io.micrometer</groupId>
			<artifactId>micrometer-registry-prometheus</artifactId>
		</dependency>

<!--		<dependency>-->
<!--			<groupId>io.github.mweirauch</groupId>-->
<!--			<artifactId>micrometer-jvm-extras</artifactId>-->
<!--		</dependency>-->

		<dependency>
			<groupId>org.apache.dubbo</groupId>
			<artifactId>dubbo</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>spring-context</artifactId>
					<groupId>org.springframework</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.dubbo</groupId>
			<artifactId>dubbo-spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>cn.com.cloudstar</groupId>
			<artifactId>rightcloud-common-dubbo</artifactId>
			<version>${cmp.common.version}</version>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid</artifactId>
		</dependency>

		<dependency>
			<groupId>io.github.openfeign</groupId>
			<artifactId>feign-httpclient</artifactId>
		</dependency>
		<dependency>
			<groupId>io.github.openfeign</groupId>
			<artifactId>feign-jackson</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-openfeign</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-openfeign-core</artifactId>
		</dependency>

		<dependency>
			<groupId>cn.com.cloudstar</groupId>
			<artifactId>rightcloud-logs</artifactId>
			<version>${cmp.module.support.version}</version>
		</dependency>

	</dependencies>

	<build>
		<!--<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<excludes>
						<exclude>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
						</exclude>
					</excludes>
				</configuration>
			</plugin>
		</plugins>-->
	</build>

</project>
