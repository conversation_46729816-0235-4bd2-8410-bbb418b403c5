<?xml version='1.0' encoding='utf-8'?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.com.cloudstar</groupId>
        <artifactId>rightcloud-parent</artifactId>
        <version>vboss.2.6.0-sec-dg-poc-SNAPSHOT</version>
    </parent>
    <artifactId>rightcloud-module-support</artifactId>
    <packaging>pom</packaging>
    <version>vboss.2.6.0-sec-dg-poc-SNAPSHOT</version>

    <modules>
        <module>rightcloud-access</module>
        <module>rightcloud-authority</module>
        <module>rightcloud-basic-data</module>
        <module>rightcloud-biz</module>
        <module>rightcloud-schedule-connector</module>
        <module>rightcloud-config-jasypt-starter</module>
        <module>rightcloud-config-db-util-starter</module>
        <module>rightcloud-flywaydb-core</module>
        <module>rightcloud-file-storage</module>
        <module>rightcloud-logs</module>
    </modules>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>cn.com.cloudstar</groupId>
                <artifactId>rightcloud-biz-common</artifactId>
                <version>${cmp.module.support.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.cloudstar</groupId>
                <artifactId>rightcloud-common-log</artifactId>
                <version>${cmp.common.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.cloudstar</groupId>
                <artifactId>rightcloud-access</artifactId>
                <version>${cmp.module.support.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.cloudstar</groupId>
                <artifactId>rightcloud-common-redis</artifactId>
                <version>${cmp.common.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.cloudstar</groupId>
                <artifactId>rightcloud-common-crypto</artifactId>
                <version>${cmp.common.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.cloudstar</groupId>
                <artifactId>rightcloud-basic-data-core</artifactId>
                <version>${cmp.module.support.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.cloudstar</groupId>
                <artifactId>rightcloud-basic-data-core-facade</artifactId>
                <version>${cmp.module.support.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.cloudstar</groupId>
                <artifactId>rightcloud-basic-data-core</artifactId>
                <version>${cmp.module.support.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.cloudstar</groupId>
                <artifactId>rightcloud-basic-data-pojo</artifactId>
                <version>${cmp.module.support.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.cloudstar</groupId>
                <artifactId>rightcloud-data-encrypt</artifactId>
                <version>${cmp.module.support.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.cloudstar</groupId>
                <artifactId>rightcloud-common-util</artifactId>
                <version>${cmp.common.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.cloudstar</groupId>
                <artifactId>rightcloud-common-mongodb</artifactId>
                <version>${cmp.common.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.cloudstar</groupId>
                <artifactId>rightcloud-common-certificate</artifactId>
                <version>${cmp.common.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.cloudstar</groupId>
                <artifactId>rightcloud-config-jasypt-starter</artifactId>
                <version>${cmp.module.support.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.cloudstar</groupId>
                <artifactId>rightcloud-config-db-util-starter</artifactId>
                <version>${cmp.module.support.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.cloudstar</groupId>
                <artifactId>rightcloud-common-validator</artifactId>
                <version>${cmp.common.version}</version>
            </dependency>
        </dependencies>

    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.github.spotbugs</groupId>
                <artifactId>spotbugs-maven-plugin</artifactId>
                <configuration>
                    <htmlOutput>true</htmlOutput>
                    <timeout>12000000</timeout>
                    <maxHeap>2048</maxHeap>
                    <!-- Optional directory to put spotbugs xdoc xml report -->
                    <outputDirectory>target/site</outputDirectory>
                    <excludeFilterFile>spotbugs-exclude-filter.xml</excludeFilterFile>
                    <plugins>
                        <plugin>
                            <groupId>com.h3xstream.findsecbugs</groupId>
                            <artifactId>findsecbugs-plugin</artifactId>
                            <version>1.12.0</version>
                        </plugin>
                    </plugins>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
