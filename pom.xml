<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>cn.com.cloudstar</groupId>
        <artifactId>rightcloud-parent</artifactId>
        <version>vboss.2.6.0-sec-dg-poc-SNAPSHOT</version>
    </parent>

    <groupId>cn.com.cloudstar</groupId>
    <artifactId>rightcloud-adapter</artifactId>
    <version>vboss.2.6.0-sec-dg-poc-SNAPSHOT</version>
    <packaging>pom</packaging>

    <name>${project.artifactId}</name>
    <url>http://maven.apache.org</url>

    <modules>
        <module>rightcloud-adapter-facade</module>
        <module>rightcloud-adapter-handler</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>cn.com.cloudstar</groupId>
                <artifactId>rightcloud-adapter-handler</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.com.cloudstar</groupId>
                <artifactId>rightcloud-adapter-core</artifactId>
                <version>${cmp.cloud.driver.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.com.cloudstar</groupId>
                <artifactId>hcso-driver</artifactId>
                <version>${cmp.hcso.driver.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.com.cloudstar</groupId>
                <artifactId>oceanstor-driver</artifactId>
                <version>${cmp.oceanstor.driver.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.com.cloudstar</groupId>
                <artifactId>dme-driver</artifactId>
                <version>${cmp.dme.driver.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.com.cloudstar</groupId>
                <artifactId>fd-driver</artifactId>
                <version>${cmp.fd.driver.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven.compiler.plugin.version}</version>
                    <configuration>
                        <source>${java.source.version}</source>
                        <target>${java.target.version}</target>
                        <encoding>${file.encoding}</encoding>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>${maven.jar.plugin.version}</version>
                    <configuration>
                        <archive>
                            <manifest>
                                <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                                <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
                            </manifest>
                        </archive>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>${maven.dependency.plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-war-plugin</artifactId>
                    <version>${maven.war.plugin.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-scm-plugin</artifactId>
                <version>1.10.0</version>
                <configuration>
                    <connectionType>developerConnection</connectionType>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
                <version>2.5.3</version>
                <configuration>
                    <tagNameFormat>v@{project.version}</tagNameFormat>
                    <autoVersionSubmodules>true</autoVersionSubmodules>
                </configuration>
            </plugin>

            <plugin>
                <groupId>com.github.spotbugs</groupId>
                <artifactId>spotbugs-maven-plugin</artifactId>
                <configuration>
                    <htmlOutput>true</htmlOutput>
                    <timeout>12000000</timeout>
                    <maxHeap>2048</maxHeap>
                    <!-- Optional directory to put spotbugs xdoc xml report -->
                    <outputDirectory>target/site</outputDirectory>
                    <excludeFilterFile>spotbugs-exclude-filter.xml</excludeFilterFile>
                    <plugins>
                        <plugin>
                            <groupId>com.h3xstream.findsecbugs</groupId>
                            <artifactId>findsecbugs-plugin</artifactId>
                            <version>1.12.0</version>
                        </plugin>
                    </plugins>
                </configuration>
            </plugin>

        </plugins>
    </build>

</project>
