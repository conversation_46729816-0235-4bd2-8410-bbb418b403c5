/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.remote.api.resource.service.server;

import java.util.List;
import java.util.Map;

import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResSpecTypeAttr;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResVmType;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.QueryResVmTypeByParamsRequest;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.QueryResVmTypeByPoolIdRequest;

/**
 * <AUTHOR>
 * @date 2020-11
 */
public interface ResVmTypeRemoteService {

    List<ResVmType> selectByParams(QueryResVmTypeByParamsRequest queryResVmTypeByParamsRequest);

    List<ResVmType> selectByResPoolId(QueryResVmTypeByPoolIdRequest queryResVmTypeByPoolIdRequest);

    ResVmType selectByPrimaryKey(Long instanceType);

    Map<String, Object> getNodeInfo(String shareType, String flavorRef, String resourceType, Long nodeCount, Long cloudEnvId);

    List<ResSpecTypeAttr> selectSpecTypeAttrByVmType(String name);
}
