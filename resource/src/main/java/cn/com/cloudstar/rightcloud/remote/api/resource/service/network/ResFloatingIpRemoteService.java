/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.remote.api.resource.service.network;

import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnv;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResFloatingIp;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.CountAllocFloatingIpByDfRequest;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.CountResFloatingIpByParamsRequest;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.QueryAllocFloatingIpRequest;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.QueryResFloatingIpByParamsRequest;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.ResFloatingIpParams;

/**
 * <AUTHOR>
 * @date 2020-11
 */
public interface ResFloatingIpRemoteService {

    List<ResFloatingIp> selectByParams(QueryResFloatingIpByParamsRequest resFloatingIpByParamsRequest);

    /**
     * 统计用户资源
     *
     * @param countResFloatingIpByParamsRequest 查询请求
     * @return 用户的弹性IP数量
     */
    int countByParams(CountResFloatingIpByParamsRequest countResFloatingIpByParamsRequest);

    List<ResFloatingIp> selectIdsByParams(List<String> statusNotIn);

    /**
     * Create floating ip boolean.
     *
     * @param resFloatingIp the res floating ip
     * @return the boolean
     */
    @Transactional(rollbackFor = Exception.class)
    boolean createFloatingIp(ResFloatingIp resFloatingIp, CloudEnv cloudEnv);

    /**
     * Update by primary key selective int.
     *
     * @param record the record
     * @return the int
     */
    int updateByPrimaryKeySelective(ResFloatingIp record);

    /**
     * Update by primary key int.
     *
     * @param record the record
     * @return the int
     */
    int updateByPrimaryKey(ResFloatingIp record);

    /**
     * Select by primary key res floating ip.
     *
     * @param id the id
     * @return the res floating ip
     */
    ResFloatingIp selectByPrimaryKey(Long id);

    /**
     * Delete floating ip boolean.
     *
     * @param resFloatingIpId the res floating ip id
     *
     * @return the boolean
     */
    boolean deleteFloatingIp(Long resFloatingIpId, boolean detachFirst);

    List<ResFloatingIp> selectAllByParams(ResFloatingIpParams resFloatingIpParams);

    List<ResFloatingIp> findAllocFloatingIp(QueryAllocFloatingIpRequest request);

    List<ResFloatingIp> findNotAllocFloatingIp(QueryAllocFloatingIpRequest request);

    List<ResFloatingIp> findByInstId(String instanceId);

    Integer countAllocFloatingIpByDf(CountAllocFloatingIpByDfRequest request);

    boolean reviseBandwidth(ResFloatingIp resFloatingIp);

    int updateToNewOrg(Long newOrgSid, Long oldOrgSid);

    int deleteByPrimaryKey(Long id);
}
