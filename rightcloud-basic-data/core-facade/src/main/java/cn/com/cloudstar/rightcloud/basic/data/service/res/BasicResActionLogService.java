/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.basic.data.service.res;

import java.util.List;

import cn.com.cloudstar.rightcloud.basic.data.pojo.common.ResActionLog;
import cn.com.cloudstar.rightcloud.common.enums.resource.ResourceOperateEnum;
import cn.com.cloudstar.rightcloud.common.enums.resource.ResourceTypeEnum;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria;

/**
 * <AUTHOR>
 * @date 2020-11
 */
public interface BasicResActionLogService {
    int insertSelective(ResActionLog record);

    int insertIntoActionLog(String opUser, ResourceOperateEnum operate, ResourceTypeEnum resourceType, Object resSid, String resName, Boolean status, String rawData, String newData, Long orgSid);

    int insertIntoActionLog(String opUser, ResourceOperateEnum operate, ResourceTypeEnum resourceTypeEnum, Object resSid, String resName, Boolean status, String rawData, String newData);

    int insertIntoActionLog(ResActionLog resActionLog);

    void updateActionLog(Integer id, ResourceTypeEnum resourceType, String resourceKey, ResourceOperateEnum operate);
    void updateActionLog(Integer id, ResourceTypeEnum resourceType, String resourceKey, ResourceOperateEnum operate, Boolean success);

    int insertIntoActionLog(String id, String account, ResourceTypeEnum vm, ResourceOperateEnum recycle, Boolean success);

    List<ResActionLog> selectByParams(Criteria criteria);

    int updateByPrimaryKeySelective(ResActionLog resActionLog);

    int insertMulti(List<ResActionLog> resActionLogs);

    /**
     * 插入日志
     *
     * @param opUser 操作人
     * @param resourceType 资源类型
     * @param operate 操作名称
     * @param success 状态
     * @return
     */
    int insertIntoActionLog(String opUser, ResourceTypeEnum resourceType, ResourceOperateEnum operate, Boolean success);
}
