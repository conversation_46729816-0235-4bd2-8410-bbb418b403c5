/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.basic.data.platform;

import cn.com.cloudstar.rightcloud.basic.data.pojo.base.Base;
import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnv;
import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnvAccount;
import cn.com.cloudstar.rightcloud.basic.data.service.cloud.BasicCloudEnvAccountService;
import cn.com.cloudstar.rightcloud.basic.data.service.cloud.BasicCloudEnvService;
import cn.com.cloudstar.rightcloud.common.ccsp.CCSPCacheUtil;
import cn.com.cloudstar.rightcloud.common.constants.RestConst;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.constants.rclink.EnvLinkType;
import cn.com.cloudstar.rightcloud.common.constants.rclink.RCLinkProperty;
import cn.com.cloudstar.rightcloud.common.constants.res.status.CloudEnvStatus;
import cn.com.cloudstar.rightcloud.common.constants.res.type.CloudEnvType;
import cn.com.cloudstar.rightcloud.common.constants.type.CloudEnvTenantKey;
import cn.com.cloudstar.rightcloud.common.exception.BizException;
import cn.com.cloudstar.rightcloud.common.util.*;
import cn.com.cloudstar.rightcloud.common.util.json.JsonUtil;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.User;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicInfoUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.base.Throwables;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;

/**
 * The type Cloud client factory.
 *
 * <AUTHOR>
 * @date 2016 /9/6
 */
@Slf4j
public class CloudClientFactory {

    private static final Logger logger = LoggerFactory.getLogger(CloudClientFactory.class);
    private static final String REQ_SOURCE = "REQ_SOURCE";

    private static final String CCSPSM_4_CIPHER = "--CCSPCIPHER--";

    public static boolean isRCLinkType(String attrs) {
        JsonNode jsonNode = JsonUtil.fromJson(attrs);
        return isRCLinkType(jsonNode);
    }

    public static boolean isRCLinkType(JsonNode jsonNode) {
        return Objects.nonNull(jsonNode.get("envLinkType")) && EnvLinkType.RC_LINK.equals(
                jsonNode.get("envLinkType").textValue());
    }

    /**
     * 获取云环境 rcLinkId
     **/
    public static String getRcLinkId(Long cloudEnvId) {
        BasicCloudEnvAccountService cloudEnvAccountMapper = SpringContextHolder.getBean(
                BasicCloudEnvAccountService.class);
        final CloudEnvAccount cloudEnvAccount = cloudEnvAccountMapper.selectByCloudEnvId(cloudEnvId);
        if (cloudEnvAccount == null) {
            return null;
        }
        return cloudEnvAccount.getPlatformComponentId();
    }

    /**
     * Build mq bean t.
     *
     * @param <T> the type parameter
     * @param envId the envId
     * @param clazz the clazz
     *
     * @return the t
     */
    public static <T extends Base> T buildMQBean(Long envId, Class<T> clazz) {
        BasicCloudEnvService cloudEnvMapper = SpringContextHolder.getBean(BasicCloudEnvService.class);
        final CloudEnv cloudEnv = cloudEnvMapper.selectByPrimaryKey(envId);
        BasicCloudEnvAccountService cloudEnvAccountMapper = SpringContextHolder.getBean(
                BasicCloudEnvAccountService.class);
        final CloudEnvAccount cloudEnvAccount = cloudEnvAccountMapper.selectByCloudEnvId(envId);
        if (cloudEnv == null || cloudEnvAccount == null) {
            throw new BizException(String.format(WebUtil.getMessage(MsgCd.ERROR_RES_NOT_FOUND), WebUtil.getMessage(MsgCd.ERR_MSG_BSS_510247557)));
        }
        //添加判断是否中断
        if (cloudEnv.getStatus().equals(CloudEnvStatus.DISCONNECT)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1720236067));
        }

        return buildMQBean(envId, cloudEnv.getCloudEnvType(), cloudEnv.getAttrData(), cloudEnv.getRegion(), clazz,
                           cloudEnvAccount.getPlatformComponentId());
    }


    /**
     * 根据云环境类型构造bean 该云环境类型的云环境 有且仅有有一个 否则抛异常
     *
     * @param cloudEnvType
     * @param clazz
     * @param <T>
     */
    public static <T extends Base> T buildMQBean(String cloudEnvType, Class<T> clazz) {
        BasicCloudEnvService cloudEnvMapper = SpringContextHolder.getBean(BasicCloudEnvService.class);

        Criteria example = new Criteria();
        example.put("cloudEnvType", cloudEnvType);
        List<CloudEnv> cloudEnvs = cloudEnvMapper.selectByParams(example);
        if (cloudEnvs == null || cloudEnvs.size() != 1) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1910117118));
        }
        CloudEnv cloudEnv = cloudEnvs.get(0);
        BasicCloudEnvAccountService cloudEnvAccountMapper = SpringContextHolder.getBean(
                BasicCloudEnvAccountService.class);
        final CloudEnvAccount cloudEnvAccount = cloudEnvAccountMapper.selectByCloudEnvId(cloudEnv.getId());
        if (cloudEnvAccount == null) {
            throw new BizException(String.format(WebUtil.getMessage(MsgCd.ERROR_RES_NOT_FOUND), WebUtil.getMessage(MsgCd.ERR_MSG_BSS_510247557)));
        }
        //添加判断是否中断
        if (cloudEnv.getStatus().equals(CloudEnvStatus.DISCONNECT)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1720236067));
        }

        return buildMQBean(cloudEnv.getId(), cloudEnv.getCloudEnvType(), cloudEnv.getAttrData(), cloudEnv.getRegion(),
                           clazz,
                           cloudEnvAccount.getPlatformComponentId());
    }

    /**
     * Build mq bean t.
     *
     * @param <T> the type parameter
     * @param id the id  envId or accountId
     * @param clazz the clazz
     *
     * @return the t
     */
    public static <T extends Base> T buildMQBean(Long id, Class<T> clazz, boolean buildByAccount) {
        if (!buildByAccount) {
            return buildMQBean(id, clazz);
        }

        BasicCloudEnvAccountService cloudEnvAccountMapper = SpringContextHolder.getBean(
                BasicCloudEnvAccountService.class);
        final CloudEnvAccount cloudEnvAccount = cloudEnvAccountMapper.selectByPrimaryKey(id);
        if (cloudEnvAccount == null) {
            throw new BizException(String.format(WebUtil.getMessage(MsgCd.ERROR_RES_NOT_FOUND), WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1994877546)));
        }

        return buildMQBean(null, cloudEnvAccount.getEnvType(), cloudEnvAccount.getAttrData(),
                           cloudEnvAccount.getRegion(), clazz, cloudEnvAccount.getPlatformComponentId());
    }

    public static <T extends Base> T buildMQBean(Long envId, String envType, String envAttrData, Class<T> clazz) {
        return buildMQBean(envId, envType, envAttrData, null, clazz);
    }

    public static <T extends Base> T buildMQBean(Long envId, String envType, String envAttrData, String region,
                                                 Class<T> clazz) {
        return buildMQBean(envId, envType, envAttrData, region, clazz, null);
    }

    /**
     * 构建mq
     *
     * @param envId 云环境id
     * @param cloudEnvType 云环境类型
     * @param envAttrData 云环境接入数据
     * @param region 分区
     * @param clazz 封装类
     * @param rclinkId 二级云管id, 如果设置则会使用二级云管的mq通道
     */
    public static <T extends Base> T buildMQBean(Long envId, String cloudEnvType, String envAttrData, String region,
                                                 Class<T> clazz, String rclinkId) {
        T base = null;
        Map<String, String> params;
        try {
            envAttrData = CrytoUtilSimple.decrypt(envAttrData);
            params = new Gson().fromJson(envAttrData, Map.class);
        } catch (Exception e) {
            logger.error("无法正确解析云环境配置信息 | {}", Throwables.getStackTraceAsString(e));
            throw new BizException(RestConst.BizError.BAD_PARAM, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1794848763));
        }
        try {
            base = clazz.newInstance();
            HttpServletRequest request = WebUtil.getRequest();
            if (request != null) {
                String reqSource = request.getHeader(REQ_SOURCE);
                base.setReqSource(null == reqSource ? "" : reqSource);
            }
            base.setProviderType(cloudEnvType);
            base.setRegion(region);
            String virtEnvType = CloudEnvType.isSelfCloudEnv(cloudEnvType) ? cloudEnvType.toLowerCase()
                    : CloudEnvType.PLUGIN_CLOUD.toString()
                                               .toLowerCase();
            base.setVirtEnvType(virtEnvType);

            base.setVirtEnvUuid(getMqEnvUuid());

            base.setCloudEnvId(envId);
            User authUser = BasicInfoUtil.getAuthUser();
            if (Objects.nonNull(authUser) && !StringUtil.isNullOrEmpty(authUser.getAccount())) {
                base.setOpUser(authUser.getAccount());
            }
            base.setOrgSid(NumberUtil.toStr(BasicInfoUtil.getCurrentOrgSid(), null));

            if (CloudEnvType.OPEN_STACK.equals(cloudEnvType) || CloudEnvType.CLOUDOS.equals(cloudEnvType)
                    || CloudEnvType.CLOUDOS_ADMIN.equals(cloudEnvType)) {
                base.setProviderUrl(params.get(CloudEnvTenantKey.PROVIDER_URL));
                base.setTenantId(params.get(CloudEnvTenantKey.ENV_ACCOUNT));
                base.setTenantUserName(params.get(CloudEnvTenantKey.TENANT_USER_NAME));
                base.setTenantUserPass(params.get(CloudEnvTenantKey.TENANT_USER_PASS));
                base.setApiKey(WebUtil.encrypt(params.get(CloudEnvTenantKey.PROVIDER_URL)));
                base.setDomain(params.get(CloudEnvTenantKey.COMPANY));
                base.setServerPort(params.get(CloudEnvTenantKey.SERVER_PORT));
                base.setCloudVersion(params.get(CloudEnvTenantKey.VERSION));

            } else if (CloudEnvType.VMWARE.equals(cloudEnvType)
                    || CloudEnvType.NSX_V.equals(cloudEnvType)) {
                boolean isRCLinkType =
                        params.containsKey("envLinkType") && EnvLinkType.RC_LINK.equals(params.get("envLinkType"));

                base.setRCLinkType(isRCLinkType);
                if (!isRCLinkType) {
                    base.setProviderUrl(params.get(CloudEnvTenantKey.MANAGEMENT_URL));
                    base.setManagementUser(params.get(CloudEnvTenantKey.MANAGEMENT_USER));
                    base.setManagementPassword(params.get(CloudEnvTenantKey.MANAGEMENT_CIPHER));
                    base.setApiKey(WebUtil.encrypt(params.get(CloudEnvTenantKey.MANAGEMENT_URL)));
                    base.setSecureToken(params.get(CloudEnvTenantKey.MANAGEMENT_USER));
                } else {
                    base.setApiKey(WebUtil.encrypt(params.get(RCLinkProperty.RCLINK_ID)));
                    base.setSecureToken(params.get(RCLinkProperty.RCLINK_TOKEN));
                    base.setVirtEnvUuid(params.get(RCLinkProperty.RCLINK_ID));
                }
            } else if (CloudEnvType.POWER_VC.equals(cloudEnvType)) {
                base.setProviderUrl(params.get(CloudEnvTenantKey.PROVIDER_URL));
                base.setTenantName(params.get(CloudEnvTenantKey.TENANT_NAME));
                base.setTenantUserName(params.get(CloudEnvTenantKey.TENANT_USER_NAME));
                base.setTenantUserPass(params.get(CloudEnvTenantKey.TENANT_USER_PASS));

            }  else if (CloudEnvType.OCEANSTOR_PACIFIC.equals(cloudEnvType)) {
                base.setProviderUrl(params.get(CloudEnvTenantKey.PROVIDER_URL));
                base.setTenantUserName(params.get(CloudEnvTenantKey.TENANT_USER_NAME));
                base.setTenantUserPass(params.get(CloudEnvTenantKey.TENANT_USER_PASS));

            } else if (CloudEnvType.HUAWEICLOUD.equals(cloudEnvType) || CloudEnvType.HCSO.equals(cloudEnvType)) {
                base.setProviderUrl(params.get(CloudEnvTenantKey.PROVIDER_URL));
                base.setTenantId(params.get(CloudEnvTenantKey.TENANT_ID));
                base.setApiKey(params.get(CloudEnvTenantKey.API_KEY));
                base.setSecureToken(params.get(CloudEnvTenantKey.SECURE_TOKEN));
                base.setDomain(params.get(CloudEnvTenantKey.DOMAINID));
                //运营 hcso 需要单独处理
                if (CloudEnvType.HCSO.equals(cloudEnvType)) {
                    handleHCSOParam(base);
                }
            } else if (CloudEnvType.FUSIONCOMPUTE.equals(cloudEnvType)) {
                base.setProviderUrl(params.get(CloudEnvTenantKey.PROVIDER_URL));
                base.setManagementUser(params.get(CloudEnvTenantKey.MANAGEMENT_USER));
                base.setManagementPassword(params.get(CloudEnvTenantKey.MANAGEMENT_CIPHER));
                base.setServerPort(params.get(CloudEnvTenantKey.SERVER_PORT));
                base.setCloudVersion(params.get(CloudEnvTenantKey.VERSION));

            } else if (CloudEnvType.AZURE.equals(cloudEnvType)) {
                base.setApiKey(params.get(CloudEnvTenantKey.CLIENT));
                base.setSecureToken(params.get(CloudEnvTenantKey.KEY));
                base.setTenantId(params.get(CloudEnvTenantKey.TENANT));
                base.setSubscriptionId(params.get(CloudEnvTenantKey.SUBSCRIPTION_ID));

            } else if (CloudEnvType.MAAS.equals(cloudEnvType)) {
                base.setJoinMaas(BooleanUtil.toBoolean(params.get(CloudEnvTenantKey.JOIN_MAAS)));
                base.setProviderUrl(params.get(CloudEnvTenantKey.PROVIDER_URL));
                base.setApiKey(params.get(CloudEnvTenantKey.API_KEY));

            } else if (CloudEnvType.KSYUN.equals(cloudEnvType)) {
                base.setApiKey(params.get(CloudEnvTenantKey.API_KEY));
                base.setSecureToken(params.get(CloudEnvTenantKey.SECURE_TOKEN));

            } else if (CloudEnvType.KING_STACK.equals(cloudEnvType)) {
                base.setApiKey(params.get(CloudEnvTenantKey.API_KEY));
                base.setSecureToken(params.get(CloudEnvTenantKey.SECURE_TOKEN));
                base.setProviderUrl(params.get(CloudEnvTenantKey.PROVIDER_URL));
                base.setRegion(params.get(CloudEnvTenantKey.REGION));

            } else if (CloudEnvType.FUSION_DIRECTOR.equals(cloudEnvType)) {
                base.setTenantUserName(params.get(CloudEnvTenantKey.TENANT_USER_NAME));
                base.setTenantUserPass(params.get(CloudEnvTenantKey.TENANT_USER_PASS));
                base.setProviderUrl(params.get(CloudEnvTenantKey.PROVIDER_URL));

            } else if (CloudEnvType.DME.equals(cloudEnvType)) {
                base.setTenantUserName(params.get(CloudEnvTenantKey.TENANT_USER_NAME));
                base.setTenantUserPass(params.get(CloudEnvTenantKey.TENANT_USER_PASS));
                base.setProviderUrl(params.get(CloudEnvTenantKey.PROVIDER_URL));
            } else if (CloudEnvType.HCS.equals(cloudEnvType)) {
                base.setOcUrl(params.get(CloudEnvTenantKey.OC_URL));
                base.setOcUserName(params.get(CloudEnvTenantKey.OC_USER_NAME));
                base.setOcPassword(params.get(CloudEnvTenantKey.OC_PASSWORD));
                base.setOcPort(params.get(CloudEnvTenantKey.OC_PORT));
                base.setScUrl(params.get(CloudEnvTenantKey.SC_URL));
                base.setScUserName(params.get(CloudEnvTenantKey.SC_USER_NAME));
                base.setScPassword(params.get(CloudEnvTenantKey.SC_PASSWORD));
                base.setScPort(params.get(CloudEnvTenantKey.SC_PORT));
                base.setTenantName(params.get(CloudEnvTenantKey.TENANT_NAME));
                base.setTenantUuid(params.get(CloudEnvTenantKey.TENANT_UUID));
                base.setSecureToken(params.get(CloudEnvTenantKey.SECURE_TOKEN));
                base.setTenantAuthUrl(params.get(CloudEnvTenantKey.TENANT_AUTH_URL));
            }  else if (!CloudEnvType.isSelfCloudEnv(cloudEnvType)) {
                base.setAttrData(envAttrData);
            } else {
                base.setApiKey(params.get(CloudEnvTenantKey.API_KEY));
                base.setSecureToken(params.get(CloudEnvTenantKey.SECURE_TOKEN));
            }

            // 如果是二级云管接入，设置id
            if (StringUtil.isNotBlank(rclinkId)) {
                base.setRCLinkType(true);
                base.setRcLinkId(rclinkId);
            }

            // proxy
            base.setProxyEnabled(PropertiesUtil.getProperty("proxy.enabled"));
            base.setHttpProxyHost(PropertiesUtil.getProperty("http.proxyHost"));
            base.setHttpProxyPort(PropertiesUtil.getProperty("http.proxyPort"));
            base.setHttpsProxyHost(PropertiesUtil.getProperty("https.proxyHost"));
            base.setHttpsProxyPort(PropertiesUtil.getProperty("https.proxyPort"));

            if (base.isProxyEnable()) {

            }
        } catch (InstantiationException | IllegalAccessException e) {
            logger.error(e.getMessage(), e);
        }
        return base;
    }

    private static <T extends Base> void handleHCSOParam(T base) {
        Map hcsoaksk = BasicInfoUtil.getHCSOAKSK();
        Object ak = hcsoaksk.get("ak");
        Object sk = hcsoaksk.get("sk");
        Object project_id = hcsoaksk.get("project_id");
        Object account_id = hcsoaksk.get("account_id");
        if (ak != null && sk != null) {
            if (ak.toString().startsWith(CCSPSM_4_CIPHER)) {
                base.setApiKey(CCSPCacheUtil.verifyAndCCSPDecrypt(ak.toString()));
            } else {
                base.setApiKey(CrytoUtilSimple.decrypt(ak.toString()));
            }
            if (sk.toString().startsWith(CCSPSM_4_CIPHER)) {
                base.setSecureToken(CCSPCacheUtil.verifyAndCCSPDecrypt(sk.toString()));
            } else {
                base.setSecureToken(CrytoUtilSimple.decrypt(sk.toString()));
            }
        }
        if (project_id != null) {
            base.setTenantId(project_id.toString());
        }
        if (account_id != null) {
            base.setDomain(account_id.toString());
        }
    }

    private static String getMqEnvUuid() {
        // 取得环境变量
        Properties sysProps = System.getProperties();
        return sysProps.getProperty("cloudstar.mq.queue", "dev");
    }
}
