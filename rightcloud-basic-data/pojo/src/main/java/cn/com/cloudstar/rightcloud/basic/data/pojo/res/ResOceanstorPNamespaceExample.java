/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.basic.data.pojo.res;

import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ResOceanstorPNamespaceExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ResOceanstorPNamespaceExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1110291907));
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_647205) + property + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1079430193));
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_20398661) + property + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1079430193));
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andResourceIdIsNull() {
            addCriterion("resource_id is null");
            return (Criteria) this;
        }

        public Criteria andResourceIdIsNotNull() {
            addCriterion("resource_id is not null");
            return (Criteria) this;
        }

        public Criteria andResourceIdEqualTo(Long value) {
            addCriterion("resource_id =", value, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdNotEqualTo(Long value) {
            addCriterion("resource_id <>", value, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdGreaterThan(Long value) {
            addCriterion("resource_id >", value, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdGreaterThanOrEqualTo(Long value) {
            addCriterion("resource_id >=", value, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdLessThan(Long value) {
            addCriterion("resource_id <", value, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdLessThanOrEqualTo(Long value) {
            addCriterion("resource_id <=", value, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdIn(List<Long> values) {
            addCriterion("resource_id in", values, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdNotIn(List<Long> values) {
            addCriterion("resource_id not in", values, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdBetween(Long value1, Long value2) {
            addCriterion("resource_id between", value1, value2, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdNotBetween(Long value1, Long value2) {
            addCriterion("resource_id not between", value1, value2, "resourceId");
            return (Criteria) this;
        }

        public Criteria andProtocolTypeIsNull() {
            addCriterion("protocol_type is null");
            return (Criteria) this;
        }

        public Criteria andProtocolTypeIsNotNull() {
            addCriterion("protocol_type is not null");
            return (Criteria) this;
        }

        public Criteria andProtocolTypeEqualTo(Byte value) {
            addCriterion("protocol_type =", value, "protocolType");
            return (Criteria) this;
        }

        public Criteria andProtocolTypeNotEqualTo(Byte value) {
            addCriterion("protocol_type <>", value, "protocolType");
            return (Criteria) this;
        }

        public Criteria andProtocolTypeGreaterThan(Byte value) {
            addCriterion("protocol_type >", value, "protocolType");
            return (Criteria) this;
        }

        public Criteria andProtocolTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("protocol_type >=", value, "protocolType");
            return (Criteria) this;
        }

        public Criteria andProtocolTypeLessThan(Byte value) {
            addCriterion("protocol_type <", value, "protocolType");
            return (Criteria) this;
        }

        public Criteria andProtocolTypeLessThanOrEqualTo(Byte value) {
            addCriterion("protocol_type <=", value, "protocolType");
            return (Criteria) this;
        }

        public Criteria andProtocolTypeIn(List<Byte> values) {
            addCriterion("protocol_type in", values, "protocolType");
            return (Criteria) this;
        }

        public Criteria andProtocolTypeNotIn(List<Byte> values) {
            addCriterion("protocol_type not in", values, "protocolType");
            return (Criteria) this;
        }

        public Criteria andProtocolTypeBetween(Byte value1, Byte value2) {
            addCriterion("protocol_type between", value1, value2, "protocolType");
            return (Criteria) this;
        }

        public Criteria andProtocolTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("protocol_type not between", value1, value2, "protocolType");
            return (Criteria) this;
        }

        public Criteria andStripSizeIsNull() {
            addCriterion("strip_size is null");
            return (Criteria) this;
        }

        public Criteria andStripSizeIsNotNull() {
            addCriterion("strip_size is not null");
            return (Criteria) this;
        }

        public Criteria andStripSizeEqualTo(Long value) {
            addCriterion("strip_size =", value, "stripSize");
            return (Criteria) this;
        }

        public Criteria andStripSizeNotEqualTo(Long value) {
            addCriterion("strip_size <>", value, "stripSize");
            return (Criteria) this;
        }

        public Criteria andStripSizeGreaterThan(Long value) {
            addCriterion("strip_size >", value, "stripSize");
            return (Criteria) this;
        }

        public Criteria andStripSizeGreaterThanOrEqualTo(Long value) {
            addCriterion("strip_size >=", value, "stripSize");
            return (Criteria) this;
        }

        public Criteria andStripSizeLessThan(Long value) {
            addCriterion("strip_size <", value, "stripSize");
            return (Criteria) this;
        }

        public Criteria andStripSizeLessThanOrEqualTo(Long value) {
            addCriterion("strip_size <=", value, "stripSize");
            return (Criteria) this;
        }

        public Criteria andStripSizeIn(List<Long> values) {
            addCriterion("strip_size in", values, "stripSize");
            return (Criteria) this;
        }

        public Criteria andStripSizeNotIn(List<Long> values) {
            addCriterion("strip_size not in", values, "stripSize");
            return (Criteria) this;
        }

        public Criteria andStripSizeBetween(Long value1, Long value2) {
            addCriterion("strip_size between", value1, value2, "stripSize");
            return (Criteria) this;
        }

        public Criteria andStripSizeNotBetween(Long value1, Long value2) {
            addCriterion("strip_size not between", value1, value2, "stripSize");
            return (Criteria) this;
        }

        public Criteria andAtimeUpdateModeIsNull() {
            addCriterion("atime_update_mode is null");
            return (Criteria) this;
        }

        public Criteria andAtimeUpdateModeIsNotNull() {
            addCriterion("atime_update_mode is not null");
            return (Criteria) this;
        }

        public Criteria andAtimeUpdateModeEqualTo(Long value) {
            addCriterion("atime_update_mode =", value, "atimeUpdateMode");
            return (Criteria) this;
        }

        public Criteria andAtimeUpdateModeNotEqualTo(Long value) {
            addCriterion("atime_update_mode <>", value, "atimeUpdateMode");
            return (Criteria) this;
        }

        public Criteria andAtimeUpdateModeGreaterThan(Long value) {
            addCriterion("atime_update_mode >", value, "atimeUpdateMode");
            return (Criteria) this;
        }

        public Criteria andAtimeUpdateModeGreaterThanOrEqualTo(Long value) {
            addCriterion("atime_update_mode >=", value, "atimeUpdateMode");
            return (Criteria) this;
        }

        public Criteria andAtimeUpdateModeLessThan(Long value) {
            addCriterion("atime_update_mode <", value, "atimeUpdateMode");
            return (Criteria) this;
        }

        public Criteria andAtimeUpdateModeLessThanOrEqualTo(Long value) {
            addCriterion("atime_update_mode <=", value, "atimeUpdateMode");
            return (Criteria) this;
        }

        public Criteria andAtimeUpdateModeIn(List<Long> values) {
            addCriterion("atime_update_mode in", values, "atimeUpdateMode");
            return (Criteria) this;
        }

        public Criteria andAtimeUpdateModeNotIn(List<Long> values) {
            addCriterion("atime_update_mode not in", values, "atimeUpdateMode");
            return (Criteria) this;
        }

        public Criteria andAtimeUpdateModeBetween(Long value1, Long value2) {
            addCriterion("atime_update_mode between", value1, value2, "atimeUpdateMode");
            return (Criteria) this;
        }

        public Criteria andAtimeUpdateModeNotBetween(Long value1, Long value2) {
            addCriterion("atime_update_mode not between", value1, value2, "atimeUpdateMode");
            return (Criteria) this;
        }

        public Criteria andStoragePoolIdIsNull() {
            addCriterion("storage_pool_id is null");
            return (Criteria) this;
        }

        public Criteria andStoragePoolIdIsNotNull() {
            addCriterion("storage_pool_id is not null");
            return (Criteria) this;
        }

        public Criteria andStoragePoolIdEqualTo(Long value) {
            addCriterion("storage_pool_id =", value, "storagePoolId");
            return (Criteria) this;
        }

        public Criteria andStoragePoolIdNotEqualTo(Long value) {
            addCriterion("storage_pool_id <>", value, "storagePoolId");
            return (Criteria) this;
        }

        public Criteria andStoragePoolIdGreaterThan(Long value) {
            addCriterion("storage_pool_id >", value, "storagePoolId");
            return (Criteria) this;
        }

        public Criteria andStoragePoolIdGreaterThanOrEqualTo(Long value) {
            addCriterion("storage_pool_id >=", value, "storagePoolId");
            return (Criteria) this;
        }

        public Criteria andStoragePoolIdLessThan(Long value) {
            addCriterion("storage_pool_id <", value, "storagePoolId");
            return (Criteria) this;
        }

        public Criteria andStoragePoolIdLessThanOrEqualTo(Long value) {
            addCriterion("storage_pool_id <=", value, "storagePoolId");
            return (Criteria) this;
        }

        public Criteria andStoragePoolIdIn(List<Long> values) {
            addCriterion("storage_pool_id in", values, "storagePoolId");
            return (Criteria) this;
        }

        public Criteria andStoragePoolIdNotIn(List<Long> values) {
            addCriterion("storage_pool_id not in", values, "storagePoolId");
            return (Criteria) this;
        }

        public Criteria andStoragePoolIdBetween(Long value1, Long value2) {
            addCriterion("storage_pool_id between", value1, value2, "storagePoolId");
            return (Criteria) this;
        }

        public Criteria andStoragePoolIdNotBetween(Long value1, Long value2) {
            addCriterion("storage_pool_id not between", value1, value2, "storagePoolId");
            return (Criteria) this;
        }

        public Criteria andCloudEnvIdIsNull() {
            addCriterion("cloud_env_id is null");
            return (Criteria) this;
        }

        public Criteria andCloudEnvIdIsNotNull() {
            addCriterion("cloud_env_id is not null");
            return (Criteria) this;
        }

        public Criteria andCloudEnvIdEqualTo(Long value) {
            addCriterion("cloud_env_id =", value, "cloudEnvId");
            return (Criteria) this;
        }

        public Criteria andCloudEnvIdNotEqualTo(Long value) {
            addCriterion("cloud_env_id <>", value, "cloudEnvId");
            return (Criteria) this;
        }

        public Criteria andCloudEnvIdGreaterThan(Long value) {
            addCriterion("cloud_env_id >", value, "cloudEnvId");
            return (Criteria) this;
        }

        public Criteria andCloudEnvIdGreaterThanOrEqualTo(Long value) {
            addCriterion("cloud_env_id >=", value, "cloudEnvId");
            return (Criteria) this;
        }

        public Criteria andCloudEnvIdLessThan(Long value) {
            addCriterion("cloud_env_id <", value, "cloudEnvId");
            return (Criteria) this;
        }

        public Criteria andCloudEnvIdLessThanOrEqualTo(Long value) {
            addCriterion("cloud_env_id <=", value, "cloudEnvId");
            return (Criteria) this;
        }

        public Criteria andCloudEnvIdIn(List<Long> values) {
            addCriterion("cloud_env_id in", values, "cloudEnvId");
            return (Criteria) this;
        }

        public Criteria andCloudEnvIdNotIn(List<Long> values) {
            addCriterion("cloud_env_id not in", values, "cloudEnvId");
            return (Criteria) this;
        }

        public Criteria andCloudEnvIdBetween(Long value1, Long value2) {
            addCriterion("cloud_env_id between", value1, value2, "cloudEnvId");
            return (Criteria) this;
        }

        public Criteria andCloudEnvIdNotBetween(Long value1, Long value2) {
            addCriterion("cloud_env_id not between", value1, value2, "cloudEnvId");
            return (Criteria) this;
        }

        public Criteria andRefAccountRidIsNull() {
            addCriterion("ref_account_rid is null");
            return (Criteria) this;
        }

        public Criteria andRefAccountRidIsNotNull() {
            addCriterion("ref_account_rid is not null");
            return (Criteria) this;
        }

        public Criteria andRefAccountRidEqualTo(Long value) {
            addCriterion("ref_account_rid =", value, "refAccountRid");
            return (Criteria) this;
        }

        public Criteria andRefAccountRidNotEqualTo(Long value) {
            addCriterion("ref_account_rid <>", value, "refAccountRid");
            return (Criteria) this;
        }

        public Criteria andRefAccountRidGreaterThan(Long value) {
            addCriterion("ref_account_rid >", value, "refAccountRid");
            return (Criteria) this;
        }

        public Criteria andRefAccountRidGreaterThanOrEqualTo(Long value) {
            addCriterion("ref_account_rid >=", value, "refAccountRid");
            return (Criteria) this;
        }

        public Criteria andRefAccountRidLessThan(Long value) {
            addCriterion("ref_account_rid <", value, "refAccountRid");
            return (Criteria) this;
        }

        public Criteria andRefAccountRidLessThanOrEqualTo(Long value) {
            addCriterion("ref_account_rid <=", value, "refAccountRid");
            return (Criteria) this;
        }

        public Criteria andRefAccountRidIn(List<Long> values) {
            addCriterion("ref_account_rid in", values, "refAccountRid");
            return (Criteria) this;
        }

        public Criteria andRefAccountRidNotIn(List<Long> values) {
            addCriterion("ref_account_rid not in", values, "refAccountRid");
            return (Criteria) this;
        }

        public Criteria andRefAccountRidBetween(Long value1, Long value2) {
            addCriterion("ref_account_rid between", value1, value2, "refAccountRid");
            return (Criteria) this;
        }

        public Criteria andRefAccountRidNotBetween(Long value1, Long value2) {
            addCriterion("ref_account_rid not between", value1, value2, "refAccountRid");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andOwnerIdIsNull() {
            addCriterion("owner_id is null");
            return (Criteria) this;
        }

        public Criteria andOwnerIdIsNotNull() {
            addCriterion("owner_id is not null");
            return (Criteria) this;
        }

        public Criteria andOwnerIdEqualTo(String value) {
            addCriterion("owner_id =", value, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOwnerIdNotEqualTo(String value) {
            addCriterion("owner_id <>", value, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOwnerIdGreaterThan(String value) {
            addCriterion("owner_id >", value, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOwnerIdGreaterThanOrEqualTo(String value) {
            addCriterion("owner_id >=", value, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOwnerIdLessThan(String value) {
            addCriterion("owner_id <", value, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOwnerIdLessThanOrEqualTo(String value) {
            addCriterion("owner_id <=", value, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOwnerIdLike(String value) {
            addCriterion("owner_id like", value, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOwnerIdNotLike(String value) {
            addCriterion("owner_id not like", value, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOwnerIdIn(List<String> values) {
            addCriterion("owner_id in", values, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOwnerIdNotIn(List<String> values) {
            addCriterion("owner_id not in", values, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOwnerIdBetween(String value1, String value2) {
            addCriterion("owner_id between", value1, value2, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOwnerIdNotBetween(String value1, String value2) {
            addCriterion("owner_id not between", value1, value2, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOrgSidIsNull() {
            addCriterion("org_sid is null");
            return (Criteria) this;
        }

        public Criteria andOrgSidIsNotNull() {
            addCriterion("org_sid is not null");
            return (Criteria) this;
        }

        public Criteria andOrgSidEqualTo(Long value) {
            addCriterion("org_sid =", value, "orgSid");
            return (Criteria) this;
        }

        public Criteria andOrgSidNotEqualTo(Long value) {
            addCriterion("org_sid <>", value, "orgSid");
            return (Criteria) this;
        }

        public Criteria andOrgSidGreaterThan(Long value) {
            addCriterion("org_sid >", value, "orgSid");
            return (Criteria) this;
        }

        public Criteria andOrgSidGreaterThanOrEqualTo(Long value) {
            addCriterion("org_sid >=", value, "orgSid");
            return (Criteria) this;
        }

        public Criteria andOrgSidLessThan(Long value) {
            addCriterion("org_sid <", value, "orgSid");
            return (Criteria) this;
        }

        public Criteria andOrgSidLessThanOrEqualTo(Long value) {
            addCriterion("org_sid <=", value, "orgSid");
            return (Criteria) this;
        }

        public Criteria andOrgSidIn(List<Long> values) {
            addCriterion("org_sid in", values, "orgSid");
            return (Criteria) this;
        }

        public Criteria andOrgSidNotIn(List<Long> values) {
            addCriterion("org_sid not in", values, "orgSid");
            return (Criteria) this;
        }

        public Criteria andOrgSidBetween(Long value1, Long value2) {
            addCriterion("org_sid between", value1, value2, "orgSid");
            return (Criteria) this;
        }

        public Criteria andOrgSidNotBetween(Long value1, Long value2) {
            addCriterion("org_sid not between", value1, value2, "orgSid");
            return (Criteria) this;
        }

        public Criteria andCreatedOrgSidIsNull() {
            addCriterion("created_org_sid is null");
            return (Criteria) this;
        }

        public Criteria andCreatedOrgSidIsNotNull() {
            addCriterion("created_org_sid is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedOrgSidEqualTo(String value) {
            addCriterion("created_org_sid =", value, "createdOrgSid");
            return (Criteria) this;
        }

        public Criteria andCreatedOrgSidNotEqualTo(String value) {
            addCriterion("created_org_sid <>", value, "createdOrgSid");
            return (Criteria) this;
        }

        public Criteria andCreatedOrgSidGreaterThan(String value) {
            addCriterion("created_org_sid >", value, "createdOrgSid");
            return (Criteria) this;
        }

        public Criteria andCreatedOrgSidGreaterThanOrEqualTo(String value) {
            addCriterion("created_org_sid >=", value, "createdOrgSid");
            return (Criteria) this;
        }

        public Criteria andCreatedOrgSidLessThan(String value) {
            addCriterion("created_org_sid <", value, "createdOrgSid");
            return (Criteria) this;
        }

        public Criteria andCreatedOrgSidLessThanOrEqualTo(String value) {
            addCriterion("created_org_sid <=", value, "createdOrgSid");
            return (Criteria) this;
        }

        public Criteria andCreatedOrgSidLike(String value) {
            addCriterion("created_org_sid like", value, "createdOrgSid");
            return (Criteria) this;
        }

        public Criteria andCreatedOrgSidNotLike(String value) {
            addCriterion("created_org_sid not like", value, "createdOrgSid");
            return (Criteria) this;
        }

        public Criteria andCreatedOrgSidIn(List<String> values) {
            addCriterion("created_org_sid in", values, "createdOrgSid");
            return (Criteria) this;
        }

        public Criteria andCreatedOrgSidNotIn(List<String> values) {
            addCriterion("created_org_sid not in", values, "createdOrgSid");
            return (Criteria) this;
        }

        public Criteria andCreatedOrgSidBetween(String value1, String value2) {
            addCriterion("created_org_sid between", value1, value2, "createdOrgSid");
            return (Criteria) this;
        }

        public Criteria andCreatedOrgSidNotBetween(String value1, String value2) {
            addCriterion("created_org_sid not between", value1, value2, "createdOrgSid");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andEncryptOptionIsNull() {
            addCriterion("encrypt_option is null");
            return (Criteria) this;
        }

        public Criteria andEncryptOptionIsNotNull() {
            addCriterion("encrypt_option is not null");
            return (Criteria) this;
        }

        public Criteria andEncryptOptionEqualTo(String value) {
            addCriterion("encrypt_option =", value, "encryptOption");
            return (Criteria) this;
        }

        public Criteria andEncryptOptionNotEqualTo(String value) {
            addCriterion("encrypt_option <>", value, "encryptOption");
            return (Criteria) this;
        }

        public Criteria andEncryptOptionGreaterThan(String value) {
            addCriterion("encrypt_option >", value, "encryptOption");
            return (Criteria) this;
        }

        public Criteria andEncryptOptionGreaterThanOrEqualTo(String value) {
            addCriterion("encrypt_option >=", value, "encryptOption");
            return (Criteria) this;
        }

        public Criteria andEncryptOptionLessThan(String value) {
            addCriterion("encrypt_option <", value, "encryptOption");
            return (Criteria) this;
        }

        public Criteria andEncryptOptionLessThanOrEqualTo(String value) {
            addCriterion("encrypt_option <=", value, "encryptOption");
            return (Criteria) this;
        }

        public Criteria andEncryptOptionLike(String value) {
            addCriterion("encrypt_option like", value, "encryptOption");
            return (Criteria) this;
        }

        public Criteria andEncryptOptionNotLike(String value) {
            addCriterion("encrypt_option not like", value, "encryptOption");
            return (Criteria) this;
        }

        public Criteria andEncryptOptionIn(List<String> values) {
            addCriterion("encrypt_option in", values, "encryptOption");
            return (Criteria) this;
        }

        public Criteria andEncryptOptionNotIn(List<String> values) {
            addCriterion("encrypt_option not in", values, "encryptOption");
            return (Criteria) this;
        }

        public Criteria andEncryptOptionBetween(String value1, String value2) {
            addCriterion("encrypt_option between", value1, value2, "encryptOption");
            return (Criteria) this;
        }

        public Criteria andEncryptOptionNotBetween(String value1, String value2) {
            addCriterion("encrypt_option not between", value1, value2, "encryptOption");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("version is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("version is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(Long value) {
            addCriterion("version =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(Long value) {
            addCriterion("version <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(Long value) {
            addCriterion("version >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(Long value) {
            addCriterion("version >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(Long value) {
            addCriterion("version <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(Long value) {
            addCriterion("version <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<Long> values) {
            addCriterion("version in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<Long> values) {
            addCriterion("version not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(Long value1, Long value2) {
            addCriterion("version between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(Long value1, Long value2) {
            addCriterion("version not between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("created_by like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("created_by not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedDtIsNull() {
            addCriterion("created_dt is null");
            return (Criteria) this;
        }

        public Criteria andCreatedDtIsNotNull() {
            addCriterion("created_dt is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedDtEqualTo(Date value) {
            addCriterion("created_dt =", value, "createdDt");
            return (Criteria) this;
        }

        public Criteria andCreatedDtNotEqualTo(Date value) {
            addCriterion("created_dt <>", value, "createdDt");
            return (Criteria) this;
        }

        public Criteria andCreatedDtGreaterThan(Date value) {
            addCriterion("created_dt >", value, "createdDt");
            return (Criteria) this;
        }

        public Criteria andCreatedDtGreaterThanOrEqualTo(Date value) {
            addCriterion("created_dt >=", value, "createdDt");
            return (Criteria) this;
        }

        public Criteria andCreatedDtLessThan(Date value) {
            addCriterion("created_dt <", value, "createdDt");
            return (Criteria) this;
        }

        public Criteria andCreatedDtLessThanOrEqualTo(Date value) {
            addCriterion("created_dt <=", value, "createdDt");
            return (Criteria) this;
        }

        public Criteria andCreatedDtIn(List<Date> values) {
            addCriterion("created_dt in", values, "createdDt");
            return (Criteria) this;
        }

        public Criteria andCreatedDtNotIn(List<Date> values) {
            addCriterion("created_dt not in", values, "createdDt");
            return (Criteria) this;
        }

        public Criteria andCreatedDtBetween(Date value1, Date value2) {
            addCriterion("created_dt between", value1, value2, "createdDt");
            return (Criteria) this;
        }

        public Criteria andCreatedDtNotBetween(Date value1, Date value2) {
            addCriterion("created_dt not between", value1, value2, "createdDt");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("updated_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(String value) {
            addCriterion("updated_by =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(String value) {
            addCriterion("updated_by <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(String value) {
            addCriterion("updated_by >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(String value) {
            addCriterion("updated_by >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(String value) {
            addCriterion("updated_by <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(String value) {
            addCriterion("updated_by <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLike(String value) {
            addCriterion("updated_by like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotLike(String value) {
            addCriterion("updated_by not like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<String> values) {
            addCriterion("updated_by in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<String> values) {
            addCriterion("updated_by not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(String value1, String value2) {
            addCriterion("updated_by between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(String value1, String value2) {
            addCriterion("updated_by not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedDtIsNull() {
            addCriterion("updated_dt is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedDtIsNotNull() {
            addCriterion("updated_dt is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedDtEqualTo(Date value) {
            addCriterion("updated_dt =", value, "updatedDt");
            return (Criteria) this;
        }

        public Criteria andUpdatedDtNotEqualTo(Date value) {
            addCriterion("updated_dt <>", value, "updatedDt");
            return (Criteria) this;
        }

        public Criteria andUpdatedDtGreaterThan(Date value) {
            addCriterion("updated_dt >", value, "updatedDt");
            return (Criteria) this;
        }

        public Criteria andUpdatedDtGreaterThanOrEqualTo(Date value) {
            addCriterion("updated_dt >=", value, "updatedDt");
            return (Criteria) this;
        }

        public Criteria andUpdatedDtLessThan(Date value) {
            addCriterion("updated_dt <", value, "updatedDt");
            return (Criteria) this;
        }

        public Criteria andUpdatedDtLessThanOrEqualTo(Date value) {
            addCriterion("updated_dt <=", value, "updatedDt");
            return (Criteria) this;
        }

        public Criteria andUpdatedDtIn(List<Date> values) {
            addCriterion("updated_dt in", values, "updatedDt");
            return (Criteria) this;
        }

        public Criteria andUpdatedDtNotIn(List<Date> values) {
            addCriterion("updated_dt not in", values, "updatedDt");
            return (Criteria) this;
        }

        public Criteria andUpdatedDtBetween(Date value1, Date value2) {
            addCriterion("updated_dt between", value1, value2, "updatedDt");
            return (Criteria) this;
        }

        public Criteria andUpdatedDtNotBetween(Date value1, Date value2) {
            addCriterion("updated_dt not between", value1, value2, "updatedDt");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
