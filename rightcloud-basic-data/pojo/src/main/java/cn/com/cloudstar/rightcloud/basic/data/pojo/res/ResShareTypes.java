package cn.com.cloudstar.rightcloud.basic.data.pojo.res;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

/**
 * 弹性文件类型使用量 createTime:2022-11-10 11:21
 *
 * <AUTHOR>
 * @version 1.0
 * @since JDK1.8
 */
@ApiModel(description = "弹性文件类型使用量")
@Getter
@Setter
public class ResShareTypes implements Serializable {
  private List<ResShareTypeUse> resShareTypeUseList;
  //总使用率
  private BigDecimal totalRatio;
}
