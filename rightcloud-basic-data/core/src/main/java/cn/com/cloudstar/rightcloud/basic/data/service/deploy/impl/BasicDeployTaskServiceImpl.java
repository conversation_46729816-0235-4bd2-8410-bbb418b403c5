/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.basic.data.service.deploy.impl;

import cn.com.cloudstar.rightcloud.common.util.json.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;

import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.repository.support.PageableExecutionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.basic.data.dao.deploy.BasicDeployPlaybookMapper;
import cn.com.cloudstar.rightcloud.basic.data.dao.res.BasicResVmScriptMapper;
import cn.com.cloudstar.rightcloud.basic.data.dao.service.BasicServiceInstTargetMapper;
import cn.com.cloudstar.rightcloud.basic.data.pojo.app.AppDeployModel;
import cn.com.cloudstar.rightcloud.basic.data.pojo.app.AppInstSvc;
import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnv;
import cn.com.cloudstar.rightcloud.basic.data.pojo.cluster.CloudClusterNode;
import cn.com.cloudstar.rightcloud.basic.data.pojo.code.Code;
import cn.com.cloudstar.rightcloud.basic.data.pojo.deploy.CloudDeploymentVm;
import cn.com.cloudstar.rightcloud.basic.data.pojo.deploy.DeployPlaybook;
import cn.com.cloudstar.rightcloud.basic.data.pojo.deploy.DeployTask;
import cn.com.cloudstar.rightcloud.basic.data.pojo.deploy.HostDepoyEvent;
import cn.com.cloudstar.rightcloud.basic.data.pojo.deploy.PlaybookTask;
import cn.com.cloudstar.rightcloud.basic.data.pojo.enums.DeployTaskTypeEnum;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResShareTarget;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResVm;
import cn.com.cloudstar.rightcloud.basic.data.pojo.user.Org;
import cn.com.cloudstar.rightcloud.basic.data.repository.DeployTaskRepository;
import cn.com.cloudstar.rightcloud.basic.data.service.cloud.BasicCloudEnvService;
import cn.com.cloudstar.rightcloud.basic.data.service.code.BasicCodeService;
import cn.com.cloudstar.rightcloud.basic.data.service.deploy.BasicDeployPlaybookService;
import cn.com.cloudstar.rightcloud.basic.data.service.deploy.BasicDeployTaskService;
import cn.com.cloudstar.rightcloud.basic.data.service.org.BasicOrgService;
import cn.com.cloudstar.rightcloud.common.constants.CodeCategoryConstants;
import cn.com.cloudstar.rightcloud.common.constants.ansible.AnsibleServerMethod;
import cn.com.cloudstar.rightcloud.common.constants.res.status.ResVmManageStatus;
import cn.com.cloudstar.rightcloud.common.constants.res.type.CloudEnvType;
import cn.com.cloudstar.rightcloud.common.constants.res.type.ClusterNodeType;
import cn.com.cloudstar.rightcloud.common.constants.status.DeployTaskStatus;
import cn.com.cloudstar.rightcloud.common.constants.type.DeployTaskType;
import cn.com.cloudstar.rightcloud.common.constants.type.DeploymentType;
import cn.com.cloudstar.rightcloud.common.constants.type.SelfServiceYamlDeployType;
import cn.com.cloudstar.rightcloud.common.util.MapsKit;
import cn.com.cloudstar.rightcloud.common.util.SpringContextHolder;
import cn.com.cloudstar.rightcloud.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.User;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicInfoUtil;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicWebUtil;

/**
 * <AUTHOR>
 * @date 2016/8/25
 */
@Component
@Slf4j
public class BasicDeployTaskServiceImpl implements BasicDeployTaskService {

    @Autowired(required = false)
    private DeployTaskRepository deployTaskRepository;

    @Autowired
    private BasicCodeService basicCodeService;

    @Autowired
    private BasicDeployPlaybookMapper basicDeployPlaybookMapper;

    @Autowired
    @Lazy
    private BasicDeployPlaybookService basicDeployPlaybookService;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private BasicCloudEnvService basicCloudEnvService;

    @Autowired
    private BasicResVmScriptMapper basicResVmScriptMapper;

    @Autowired
    private BasicServiceInstTargetMapper basicServiceInstTargetMapper;

    @Autowired
    private BasicOrgService basicOrgService;

    /**
     * Select by example list.
     *
     * @param deployTask the example
     *
     * @return the list
     */
    @Override
    public List<DeployTask> selectByExample(DeployTask deployTask) {
        return this.deployTaskRepository.findAll(Example.of(deployTask));
    }

    /**
     * Select tasks by union list.
     *
     * @return the list
     */
    @Override
    public List<DeployTask> selectTasksByUnion() {
        Long currentOrgSid = BasicInfoUtil.getCurrentOrgSid();
        List<Org> orgList = basicOrgService.selectAllChildOrg(currentOrgSid);
        List<Long> orgSids = orgList.stream().map(Org::getOrgSid).collect(Collectors.toList());
        orgSids.add(currentOrgSid);
        return this.deployTaskRepository.findAllByOrgSidIn(orgSids);
    }

    /**
     * Sets cloud host task.
     *
     * @param resVm the cloud host
     * @param appConfigs the app configs
     *
     * @return the cloud host task
     */
    @Override
    public List<DeployTask> setupResVmTask(ResVm resVm, List<AppDeployModel> appConfigs) {
        List<DeployTask> deployTasks = setupResVmTask(resVm);
        // deploy app
        appConfigs.forEach(appConfig -> {
            DeployTask deployTask = buildDeployTask(resVm, DeployTaskType.DEPOLY_APP, DeployTaskStatus.PENDING);
            appConfig.setHosts(Collections.singletonList(resVm.getId()));
            deployTask.setTaskDetail(JsonUtil.toJson(appConfig));
            this.deployTaskRepository.save(deployTask);

            // 建立关联关系
            String dependOnTaskType = null;
            if (DeploymentType.CONTAINER.equals(appConfig.getDeploymentType())) {
                // 容器化部署需要安装完成容器
                dependOnTaskType = DeployTaskType.INSTALL_DOCKER;
            } else if (DeploymentType.NATIVE.equals(appConfig.getDeploymentType())) {
                // 原生部署需要安装完成Agent
                dependOnTaskType = DeployTaskType.IMPORT_HOST;
            }
            // 查找到主机的Task中对应的Task
            String finalDependOnTaskType = dependOnTaskType;
            Optional<DeployTask> taskOptional = deployTasks.stream()
                                                           .filter(task -> task.getType().equals(finalDependOnTaskType))
                                                           .findFirst();
            // 设定依赖
            taskOptional.ifPresent(task -> deployTask.setDependOn(task.getId()));
            deployTasks.add(deployTask);
        });

        return deployTasks;
    }

    private DeployTask buildDeployTask(ResVm resVm, String name, String type, String status, Integer totalQuantity,
                                       Long sfServiceInstId) {
        CloudEnv cloudEnv = basicCloudEnvService.selectByPrimaryKey(resVm.getCloudEnvId());
        String taskStatusName = basicCodeService.getTaskStatusName(status);

        DeployTask deployTask = new DeployTask();
        deployTask.setType(type);
        deployTask.setTarget(resVm.getId());
        deployTask.setTargetName(resVm.getInstanceName());
        if (cloudEnv != null) {
            deployTask.setCloudEnvId(cloudEnv.getId());
            deployTask.setCloudEnvName(cloudEnv.getCloudEnvName());
            deployTask.setCloudEnvType(cloudEnv.getCloudEnvType());
        }
        deployTask.setName(name);
        deployTask.setStatus(status);
        deployTask.setStatusName(taskStatusName);
        if (DeployTaskStatus.RUNNING.equals(status)) {
            deployTask.setStartDate(DateTime.now().toDate());
        }
        deployTask.setTotalQuantity(totalQuantity);
        deployTask.setCompletedQuantity(0);
        deployTask.setUserId(resVm.getOwnerId());
        deployTask.setOrgSid(resVm.getOrgSid());
        deployTask.setSfServiceInstId(sfServiceInstId);
        User authUser = BasicInfoUtil.getAuthUser();
        String user =
                (Objects.isNull(authUser) || StringUtil.isNullOrEmpty(authUser.getAccount())) ? resVm.getCreatedBy()
                                                                                              : authUser.getAccount();
        BasicWebUtil.prepareInsertParams(deployTask, user);

        return deployTask;
    }

    private DeployTask buildDeployTask(ResVm resVm, String type, String status) {
        Code sysCode = getSysCode(type);
        String name = sysCode.getCodeDisplay() + " - " + resVm.getInstanceName();
        int totalQuantity = Integer.parseInt(sysCode.getAttribute1());
        List<Long> deployInstIds = basicServiceInstTargetMapper.getDeployInstByTargetId(resVm.getId(),
                                                                                        SelfServiceYamlDeployType.INFRA);
        Long sfServiceInstId = !CollectionUtils.isEmpty(deployInstIds) ? deployInstIds.get(0) : null;

        return buildDeployTask(resVm, name, type, status, totalQuantity, sfServiceInstId);
    }

    /**
     * Sets cloud host task.
     *
     * @param resVm the cloud host
     *
     * @return the cloud host task
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<DeployTask> setupResVmTask(ResVm resVm) {
        List<DeployTask> result = new LinkedList<>();
        DeployTask deployTask = buildDeployTask(resVm, DeployTaskType.CREATE_HOST, DeployTaskStatus.RUNNING);
        this.deployTaskRepository.save(deployTask);
        result.add(deployTask);

        // 克隆时：电源关闭 不能生成接管任务
        if (resVm.getCloneSourceId() != null) {
            if (Boolean.TRUE.equals(resVm.getClonePowerStatus())) {
                result.addAll(deployResVmTask(resVm, deployTask.getId()));
            }
        } else {
            result.addAll(deployResVmTask(resVm, deployTask.getId()));
        }
        return result;
    }

    private Code getSysCode(String taskType) {
        Criteria criteria = new Criteria("codeCategory", CodeCategoryConstants.DEPLOY_TASK_TYPE).put("codeValue",
                                                                                                     taskType);
        List<Code> codes = this.basicCodeService.selectByParams(criteria);
        return codes.get(0);
    }

    /**
     * Deploy cloud host task list.
     *
     * @param resVm the cloud host
     * @param setupHostTaskId the setup host task id
     *
     * @return the list
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<DeployTask> deployResVmTask(ResVm resVm, String setupHostTaskId) {
        List<DeployTask> result = new LinkedList<>();
        String status;
        if (Strings.isNullOrEmpty(setupHostTaskId)) {
            status = DeployTaskStatus.RUNNING;
        } else {
            status = DeployTaskStatus.PENDING;
        }
        DeployTask deployTask = buildDeployTask(resVm, DeployTaskType.IMPORT_HOST, status);
        this.deployTaskRepository.save(deployTask);
        // 任务编排
        deployTask.setDependOn(setupHostTaskId);
        deployTask.setApiPath(AnsibleServerMethod.ControlApi.AGENT);
        result.add(deployTask);

        if (CloudEnvType.MAAS.equals(resVm.getCloudEnvType())) {
            DeployTask installSnmpTask = buildDeployTask(resVm, DeployTaskType.INSTALL_SNMP, DeployTaskStatus.PENDING);
            this.deployTaskRepository.save(installSnmpTask);
            // 任务编排
            installSnmpTask.setDependOn(deployTask.getId());
            installSnmpTask.setApiPath(AnsibleServerMethod.ControlApi.AGENT);
            result.add(installSnmpTask);
        }
        return result;
    }

    /**
     * Sets host module task.
     *
     * @param resVm the cloud host
     * @param moduleType the module type
     * @param status the init status
     *
     * @return the host module task
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public DeployTask setupHostModuleTask(ResVm resVm, String moduleType, String status) {
        String apiPath = null;
        if (DeployTaskType.INSTALL_DOCKER.equals(moduleType)) {
            apiPath = AnsibleServerMethod.ControlApi.DOCKER;
        } else if (DeployTaskType.INSTALL_MONITOR.equals(moduleType)) {
            apiPath = AnsibleServerMethod.ControlApi.MONITOR;
        } else if (DeployTaskType.UNINSTALL_AGENT.equals(moduleType)) {
            apiPath = AnsibleServerMethod.ControlApi.UNINSTALL;
        }
        DeployTask deployTask = buildDeployTask(resVm, moduleType, status);
        deployTask.setApiPath(apiPath);
        this.deployTaskRepository.save(deployTask);

        return deployTask;
    }


    /**
     * Remove cloud host task deploy task.
     *
     * @param resVm the cloud host
     * @param taskDetail the taskDetail
     *
     * @return the deploy task
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public DeployTask removeResVmTask(ResVm resVm, Map<String, Object> taskDetail) {
        DeployTask deployTask = buildDeployTask(resVm, DeployTaskType.DELETE_HOST, DeployTaskStatus.RUNNING);
        // 任务必须依赖于前面任务成功
        taskDetail.put("MUST_DEPEND_ON_BEFORE_SUCCESS", false);
        User authUser = BasicInfoUtil.getAuthUser();
        String user =
                (Objects.isNull(authUser) || StringUtil.isNullOrEmpty(authUser.getAccount())) ? resVm.getCreatedBy()
                                                                                              : authUser.getAccount();

        taskDetail.put("opUser", user);
        deployTask.setTaskDetail(JsonUtil.toJson(taskDetail));
        this.deployTaskRepository.save(deployTask);

        return deployTask;
    }

    /**
     * Release cloud host task deploy task.
     *
     * @param resVm the cloud host
     *
     * @return the deploy task
     */
    @Override
    public DeployTask releaseResVmTask(ResVm resVm) {
        // release host task
        Code sysCode = getSysCode(DeployTaskType.RELEASE_COMPONENT);
        DeployTask deployTask = new DeployTask();
        deployTask.setId("0");
        deployTask.setType(DeployTaskType.RELEASE_COMPONENT);
        deployTask.setTarget(resVm.getId());
        deployTask.setTargetName(resVm.getInstanceName());
        deployTask.setCloudEnvId(resVm.getCloudEnvId());
        deployTask.setCloudEnvName(resVm.getCloudEnvName());
        deployTask.setCloudEnvType(resVm.getCloudEnvType());
        deployTask.setName(sysCode.getCodeDisplay() + " - " + resVm.getInstanceName());
        deployTask.setStartDate(DateTime.now().toDate());
        deployTask.setStatus(DeployTaskStatus.RUNNING);
        deployTask.setStatusName(basicCodeService.getTaskStatusName(deployTask.getStatus()));
        deployTask.setTotalQuantity(Integer.parseInt(sysCode.getAttribute1()));
        deployTask.setCompletedQuantity(0);
        deployTask.setUserId(resVm.getOwnerId());

        return deployTask;
    }

    @Override
    public DeployTask resetVmPasswordTask(ResVm resVm, Map<String, String> taskDetail) {
        DeployTask deployTask = buildDeployTask(resVm, DeployTaskType.RESET_VM_CIPHER, DeployTaskStatus.RUNNING);
        deployTask.setTaskDetail(JsonUtil.toJson(taskDetail));
        this.deployTaskRepository.save(deployTask);

        return deployTask;
    }

    @Override
    public DeployTask resetVmHostNameTask(ResVm resVm, Map<String, String> taskDetail) {
        DeployTask deployTask = buildDeployTask(resVm, DeployTaskType.RESET_VM_HOSTNAME, DeployTaskStatus.RUNNING);
        deployTask.setTaskDetail(JsonUtil.toJson(taskDetail));
        this.deployTaskRepository.save(deployTask);
        return deployTask;
    }

    /**
     * Manage cloud host task deploy task.
     *
     * @param resVm the cloud host
     * @param sendEvent the send event
     *
     * @return the deploy task
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public DeployTask manageResVmTask(ResVm resVm, boolean sendEvent) {
        DeployTask manageTask = manageResVmTask(resVm);

        List<DeployTask> deployTaskList = new ArrayList<>();
        deployTaskList.add(manageTask);
        CloudEnv cloudEnv = basicCloudEnvService.selectByPrimaryKey(resVm.getCloudEnvId());
        // 物理机增加安装snmp的任务
        if (CloudEnvType.MAAS.equals(cloudEnv.getCloudEnvType())) {
            DeployTask installSnmpTask = buildDeployTask(resVm, DeployTaskType.INSTALL_SNMP, DeployTaskStatus.PENDING);
            this.deployTaskRepository.save(installSnmpTask);
            installSnmpTask.setDependOn(manageTask.getId());
            installSnmpTask.setApiPath(AnsibleServerMethod.ControlApi.AGENT);
            deployTaskList.add(installSnmpTask);
        }
        this.basicDeployPlaybookService.setupPlaybook(deployTaskList);

        if (sendEvent) {
            HostDepoyEvent hostDepoyEvent = new HostDepoyEvent(resVm);
            hostDepoyEvent.setType(DeployTaskType.IMPORT_HOST);
            hostDepoyEvent.setApiPath(AnsibleServerMethod.ControlApi.AGENT);
            hostDepoyEvent.setTaskId(manageTask.getId());
            hostDepoyEvent.setManual(true);

            SpringContextHolder.publishEvent(hostDepoyEvent);
        }
        return manageTask;
    }

    /**
     * ReConfig cloud host task deploy task.
     *
     * @param resVm the cloud host
     *
     * @return the deploy task
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public DeployTask reConfigResVmTask(ResVm resVm) {
        DeployTask deployTask = buildDeployTask(resVm, DeployTaskType.RE_CONFIG_HOST, DeployTaskStatus.RUNNING);
        this.deployTaskRepository.save(deployTask);

        return deployTask;
    }

    @Override
    public DeployTask stopResVmTask(ResVm resVm, Map<String, Object> taskDetail) {
        DeployTask deployTask = buildDeployTask(resVm, DeployTaskType.STOP_HOST, DeployTaskStatus.RUNNING);
        // 任务必须依赖于前面任务成功
        taskDetail.put("MUST_DEPEND_ON_BEFORE_SUCCESS", false);
        deployTask.setTaskDetail(JsonUtil.toJson(taskDetail));
        this.deployTaskRepository.save(deployTask);

        return deployTask;
    }

    /**
     * Manage cloud host task deploy task.
     *
     * @param resVm the cloud host
     *
     * @return the deploy task
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public DeployTask manageResVmTask(ResVm resVm) {
        DeployTask deployTask = buildDeployTask(resVm, DeployTaskType.IMPORT_HOST, DeployTaskStatus.PENDING);
        this.deployTaskRepository.save(deployTask);

        return deployTask;
    }

    /**
     * Deploy app task deploy task.
     *
     * @param appInstSvc the app inst svc
     * @param taskDetail the task detail
     * @param totalQuantity the total quantity
     * @param orgSid
     *
     * @return the deploy task
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public DeployTask deployAppTask(AppInstSvc appInstSvc, String taskDetail, int totalQuantity, Long orgSid) {
        // setup host task
        Code sysCode = getSysCode(DeployTaskType.DEPOLY_APP);
        DeployTask deployTask = new DeployTask();
        deployTask.setType(DeployTaskType.DEPOLY_APP);
        deployTask.setTarget(appInstSvc.getResVmId());
        deployTask.setTargetName(sysCode.getCodeDisplay() + " - " + appInstSvc.getName());
        deployTask.setSubTarget(appInstSvc.getId().toString());
        deployTask.setName(sysCode.getCodeDisplay() + " - " + appInstSvc.getName());
        deployTask.setStatus(DeployTaskStatus.PENDING);
        deployTask.setStatusName(basicCodeService.getTaskStatusName(deployTask.getStatus()));
        deployTask.setTotalQuantity(totalQuantity);
        deployTask.setCompletedQuantity(0);
        deployTask.setTaskDetail(taskDetail);
        deployTask.setUserId(appInstSvc.getUserId().toString());
        deployTask.setOrgSid(orgSid);
        BasicWebUtil.prepareInsertParams(deployTask, appInstSvc.getCreatedBy());
        this.deployTaskRepository.save(deployTask);

        return deployTask;
    }

    /**
     * Release app task deploy task.
     *
     * @param appInstSvc the app inst svc
     * @param totalQuantity the total quantity
     * @param orgSid the orgSid
     *
     * @return the deploy task
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public DeployTask releaseAppTask(AppInstSvc appInstSvc, int totalQuantity, Long orgSid) {
        // setup host task
        Code sysCode = getSysCode(DeployTaskType.RELEASE_APP);
        DeployTask deployTask = new DeployTask();
        deployTask.setType(DeployTaskType.RELEASE_APP);
        String name = sysCode.getCodeDisplay() + " - " + appInstSvc.getName();
        deployTask.setTarget(appInstSvc.getResVmId());
        deployTask.setTargetName(name);
        deployTask.setSubTarget(appInstSvc.getId().toString());
        deployTask.setName(name);
        deployTask.setStatus(DeployTaskStatus.PENDING);
        deployTask.setStatusName(basicCodeService.getTaskStatusName(deployTask.getStatus()));
        deployTask.setTotalQuantity(totalQuantity);
        deployTask.setCompletedQuantity(0);
        deployTask.setUserId(appInstSvc.getUserId().toString());
        deployTask.setOrgSid(orgSid);
        BasicWebUtil.prepareInsertParams(deployTask, appInstSvc.getCreatedBy());
        this.deployTaskRepository.save(deployTask);

        return deployTask;
    }

    /**
     * Cancel task by host not end.
     *
     * @param hostId the host id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelTaskByHostNotEnd(String hostId) {
        List<String> statusIn = Arrays.asList(DeployTaskStatus.PENDING, DeployTaskStatus.RUNNING);
        List<DeployTask> allTask = deployTaskRepository.findAllByTargetAndStatusIn(hostId, statusIn);
        String taskStatusName = basicCodeService.getTaskStatusName(DeployTaskStatus.CANCEL);
        allTask.forEach(task -> {
            task.setStatus(DeployTaskStatus.CANCEL);
            task.setStatusName(taskStatusName);
            BasicWebUtil.prepareUpdateParams(task);
        });
        deployTaskRepository.saveAll(allTask);
    }

    /**
     * Sets join task.
     *
     * @param tasks the tasks
     * @param userSid the user sid
     * @param account the account
     *
     * @return the join task
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public DeployTask setJoinTask(List<Map<String, Object>> tasks, Long userSid, String account, Long orgSid) {
        // 设置一个Join任务
        List<Map<String, Object>> dockerTasks = new ArrayList<>();
        tasks.forEach(task -> {
            List<DeployTask> allRelateTask = this.deployTaskRepository.findAllByTargetAndType(
                    task.get("vmSid").toString(), DeployTaskType.INSTALL_DOCKER);
            if (!CollectionUtils.isEmpty(allRelateTask)) {
                String id = allRelateTask.get(0).getId();
                dockerTasks.add(MapsKit.of("vmSid", task.get("vmSid").toString(), "taskId", id));
            }
        });
        DeployTask deployTask = new DeployTask();
        deployTask.setType(DeployTaskType.CLUSTER_JOIN);
        deployTask.setName("Cluster Join");
        deployTask.setStatus(DeployTaskStatus.PENDING);
        deployTask.setStatusName(basicCodeService.getTaskStatusName(deployTask.getStatus()));
        deployTask.setTotalQuantity(dockerTasks.size());
        deployTask.setCompletedQuantity(0);
        deployTask.setUserId(userSid.toString());
        deployTask.setOrgSid(orgSid);
        deployTask.setTaskDetail(JsonUtil.toJson(dockerTasks));
        BasicWebUtil.prepareInsertParams(deployTask, account);
        this.deployTaskRepository.save(deployTask);

        // 为每一个PlayBook添加Join任务
        for (Map<String, Object> task : dockerTasks) {
            String taskId = task.get("taskId").toString();
            DeployTask hostTask = this.selectByPrimaryKey(taskId);
            JsonNode json = JsonUtil.fromJson(hostTask.getTaskDetail());
            DeployPlaybook deployPlaybook = this.basicDeployPlaybookMapper.selectByPrimaryKey(
                    json.get("pid").longValue());
            List<PlaybookTask> playbookTasks = JsonUtil.fromJson(deployPlaybook.getPlaybook(),
                                                                 new TypeReference<List<PlaybookTask>>() {
                                                                 });
            PlaybookTask playbookTask = PlaybookTask.Builder.aPlaybookTask()
                                                            .id(deployTask.getId())
                                                            .dependOn(taskId)
                                                            .type(deployTask.getType())
                                                            .build();
            playbookTasks.add(playbookTask);
            deployPlaybook.setPlaybook(JsonUtil.toJson(playbookTasks));
            this.basicDeployPlaybookMapper.updateByPrimaryKeySelective(deployPlaybook);
        }
        return deployTask;
    }

    @Override
    public DeployTask takeResVmLayoutScriptJoinTask(ResVm resVm, List<Long> deployInstIds) {
        if (deployInstIds.isEmpty()) {
            return null;
        }
        // 查询自服务相关的join task定义
        DeployTask taskParam = DeployTask.builder()
                                         .type(DeployTaskType.LAYOUT_SCRIPT_JOIN)
                                         .sfServiceInstId(deployInstIds.get(0))
                                         .build();
        List<DeployTask> tasks = this.deployTaskRepository.findAll(Example.of(taskParam));
        if (tasks.isEmpty()) {
            return null;
        }
        DeployTask oldTask = tasks.get(0);
        AuthUser user = BasicInfoUtil.getCurrentUserInfo();
        oldTask.setType(DeployTaskType.LAYOUT_SCRIPT_JOIN);
        oldTask.setStatus(DeployTaskStatus.PENDING);
        oldTask.setStatusName(basicCodeService.getTaskStatusName(oldTask.getStatus()));
        BasicWebUtil.prepareUpdateParams(oldTask, user.getAccount());
        this.deployTaskRepository.update(oldTask.getId(), oldTask);
        return oldTask;
    }

    @Override
    public List<DeployTask> findAllByTarget(String target) {
        return deployTaskRepository.findAllByTarget(target);
    }

    @Override
    public List<DeployTask> findAllByTargetAndType(String target, String type) {
        return deployTaskRepository.findAllByTargetAndType(target, type);
    }

    @Override
    public List<DeployTask> findAllByTargetAndStatusIn(String target, List<String> status) {
        return deployTaskRepository.findAllByTargetAndStatusIn(target, status);
    }

    @Override
    public List<DeployTask> findAllBySfServiceInstId(Long serviceInstId) {
        return deployTaskRepository.findAllBySfServiceInstIdOrderByStartDateDesc(serviceInstId);
    }

    @Override
    public Page<DeployTask> selectTasksByDeployment(List<CloudDeploymentVm> cloudDeploymentVms, Pageable pageable) {
        if (CollectionUtils.isEmpty(cloudDeploymentVms)) {
            return new PageImpl<>(Lists.newArrayList());
        }

        List<String> resVmIds = cloudDeploymentVms.stream()
                                                  .map(CloudDeploymentVm::getResVmId)
                                                  .collect(Collectors.toList());

        return deployTaskRepository.findAllByTargetIn(resVmIds, pageable);
    }

    @Override
    public Page<DeployTask> selectTasksByUserId(String userId, Pageable pageable) {
        return deployTaskRepository.findAllByUserId(userId, pageable);
    }

    @Override
    public void updateIncompleteTaskCompletedCount(String id) {
        Optional<DeployTask> deployTaskOptional = deployTaskRepository.findById(id);
        if (!deployTaskOptional.isPresent()) {
            return;
        }
        DeployTask deployTask = deployTaskOptional.get();
        if (deployTask.getCompletedQuantity() >= deployTask.getTotalQuantity()) {
            return;
        }
        deployTask.setUpdatedDt(DateUtil.date());
        deployTask.setCompletedQuantity(deployTask.getCompletedQuantity() + 1);
        deployTaskRepository.save(deployTask);
    }

    @Override
    public DeployTask save(DeployTask deployTask) {
        return deployTaskRepository.save(deployTask);
    }

    @Override
    public List<DeployTask> findAllBySubTargetAndTypeIn(String subTarget, List<String> types) {
        return deployTaskRepository.findAllBySubTargetAndTypeIn(subTarget, types);
    }

    @Override
    public Page<DeployTask> findAllBySubTargetInAndTypeInForPage(List<String> targets, List<String> subTargets,
                                                                 List<String> types, Pageable pageable) {
        return deployTaskRepository.findAllByTargetInAndSubTargetInAndTypeIn(targets, subTargets, types, pageable);
    }

    @Override
    public Page<DeployTask> selectByExampleForPage(DeployTask deployTask, Pageable pageable) {
        Long currentOrgSid = BasicInfoUtil.getCurrentOrgSid();
        List<Org> orgList = basicOrgService.selectAllChildOrg(currentOrgSid);
        List<Long> orgSids = orgList.stream().map(Org::getOrgSid).collect(Collectors.toList());
        if (currentOrgSid != null) {
            orgSids.add(currentOrgSid);
        }
        Query query = new Query();
        query.with(pageable).addCriteria(getCriteria(deployTask, orgSids));
        List<DeployTask> deployTasks = mongoTemplate.find(query, DeployTask.class);
        return PageableExecutionUtils.getPage(deployTasks, pageable,
                                              () -> mongoTemplate.count(Query.of(query).limit(-1).skip(-1),
                                                                        DeployTask.class));
    }

    private org.springframework.data.mongodb.core.query.Criteria getCriteria(DeployTask deployTask,
                                                                             List<Long> orgSids) {
        org.springframework.data.mongodb.core.query.Criteria criteria = org.springframework.data.mongodb.core.query.Criteria
                .where("orgSid")
                .in(orgSids);
        if (!Strings.isNullOrEmpty(deployTask.getName())) {
            Pattern pattern = Pattern.compile("^.*" + deployTask.getName() + ".*$", Pattern.CASE_INSENSITIVE);
            criteria.and("name").regex(pattern);
        }
        if (!Strings.isNullOrEmpty(deployTask.getTargetName())) {
            Pattern pattern = Pattern.compile("^.*" + deployTask.getTargetName() + ".*$", Pattern.CASE_INSENSITIVE);
            criteria.and("targetName").regex(pattern);
        }
        if (!Objects.isNull(deployTask.getStartDate())) {
            criteria.and("startDate").gte(deployTask.getStartDate());
        }
        if (!Objects.isNull(deployTask.getEndDate())) {
            criteria.and("endDate").lte(deployTask.getEndDate());
        }
        if (!Strings.isNullOrEmpty(deployTask.getStatus())) {
            criteria.and("status").is(deployTask.getStatus());
        }
        if (!Strings.isNullOrEmpty(deployTask.getTarget())) {
            criteria.and("target").is(deployTask.getTarget());
        } else {
            criteria.and("target").exists(true);
        }
        criteria.and("type").ne("deleteHost");
        //过滤orgSid为空的数据
        org.springframework.data.mongodb.core.query.Criteria criteria1 = org.springframework.data.mongodb.core.query.Criteria
                .where("orgSid")
                .ne(null);
        org.springframework.data.mongodb.core.query.Criteria criteria2 = new org.springframework.data.mongodb.core.query.Criteria()
                .andOperator(criteria, criteria1);
        return criteria2;
    }

    @Override
    public long countByExample(DeployTask deployTask) {
        Long currentOrgSid = BasicInfoUtil.getCurrentOrgSid();
        List<Org> orgList = basicOrgService.selectAllChildOrg(currentOrgSid);
        List<Long> orgSids = orgList.stream().map(Org::getOrgSid).collect(Collectors.toList());
        orgSids.add(currentOrgSid);
        Query query = new Query();
        query.addCriteria(getCriteria(deployTask, orgSids));
        return mongoTemplate.count(query, DeployTask.class);
    }

    @Override
    public DeployTask cloneResVmAsTemplateTask(ResVm resVm) {
        DeployTask deployTask = buildDeployTask(resVm, DeployTaskType.CLONE_AS_TEMPLATE, DeployTaskStatus.RUNNING);
        deployTaskRepository.save(deployTask);
        return deployTask;
    }

    @Override
    public DeployTask createSfsTask(ResVm resVm, ResShareTarget resShareTarget, String type) {
        DeployTask deployTask = buildDeployTask(resVm, type, DeployTaskStatus.RUNNING);
        deployTask.setSubTarget(resShareTarget.getId().toString());
        deployTaskRepository.save(deployTask);
        return deployTask;
    }

    @Override
    public DeployTask renewResVmTask(ResVm resVm) {
        DeployTask deployTask = buildDeployTask(resVm, DeployTaskType.RENEW, DeployTaskStatus.RUNNING);
        deployTaskRepository.save(deployTask);
        return deployTask;
    }

    @Override
    public List<DeployTask> findAllBySubTargetIn(List<String> subTargets) {
        return deployTaskRepository.findAllBySubTargetIn(subTargets);
    }

    @Override
    public List<DeployTask> findAllByTargetAndSubTarget(String target, String subTarget) {
        return deployTaskRepository.findAllByTargetAndSubTarget(target, subTarget);
    }

    /**
     * Sets  join task.
     *
     * @param tasks the tasks
     * @param authUser the user sid
     * @param taskDetail the taskDetail
     *
     * @return the join task
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public DeployTask setJoinTask(List<Map<String, Object>> tasks, AuthUser authUser, Map<String, Object> taskDetail) {
        // 添加一个join任务
        DeployTask deployTask = new DeployTask();
        deployTask.setType(DeployTaskType.LAYOUT_SCRIPT_JOIN);
        //Layout Script Join
        deployTask.setName("初始化运行环境");
        deployTask.setStatus(DeployTaskStatus.PENDING);
        deployTask.setStatusName(basicCodeService.getTaskStatusName(deployTask.getStatus()));
        deployTask.setTotalQuantity(tasks.size());
        deployTask.setCompletedQuantity(0);
        deployTask.setUserId(authUser.getUserSid().toString());
        deployTask.setOrgSid(authUser.getOrgSid());
        deployTask.setTaskDetail(JsonUtil.toJson(taskDetail));
        // 保存自服务实例id
        deployTask.setSfServiceInstId((Long) taskDetail.get("selfInstId"));
        BasicWebUtil.prepareInsertParams(deployTask, authUser);
        this.deployTaskRepository.save(deployTask);

        // 为每一个PlayBook添加Join任务
        for (Map<String, Object> task : tasks) {
            String taskId = task.get("taskId").toString();
            DeployTask hostTask = this.selectByPrimaryKey(taskId);
            JsonNode json = JsonUtil.fromJson(hostTask.getTaskDetail());
            DeployPlaybook deployPlaybook = this.basicDeployPlaybookMapper.selectByPrimaryKey(
                    json.get("pid").longValue());
            List<PlaybookTask> playbookTasks = JsonUtil.fromJson(deployPlaybook.getPlaybook(),
                                                                 new TypeReference<List<PlaybookTask>>() {
                                                                 });
            // 获取最大的taskId（取playbook最后一个任务，在后面加上一个join任务）
            Optional<PlaybookTask> max = playbookTasks.stream().max(Comparator.comparing(PlaybookTask::getId));
            String dependOn = "";
            if (max.isPresent()) {
                dependOn = max.get().getId();
            }
            PlaybookTask playbookTask = PlaybookTask.Builder.aPlaybookTask()
                                                            .id(deployTask.getId())
                                                            .dependOn(dependOn)
                                                            .type(deployTask.getType())
                                                            .build();
            playbookTasks.add(playbookTask);
            deployPlaybook.setPlaybook(JsonUtil.toJson(playbookTasks));
            this.basicDeployPlaybookMapper.updateByPrimaryKeySelective(deployPlaybook);
        }

        return deployTask;
    }

    /**
     * Select by primary key deploy task.
     *
     * @param taskId the task id
     *
     * @return the deploy task
     */
    @Override
    public DeployTask selectByPrimaryKey(String taskId) {
        Optional<DeployTask> deployTaskOptional = deployTaskRepository.findById(taskId);
        return deployTaskOptional.orElseGet(DeployTask::new);

    }

    @Override
    public int updateByPrimaryKeySelective(DeployTask record) {
        this.deployTaskRepository.update(record.getId(), record);
        return 1;
    }

    @Override
    public List<DeployTask> getClusterTask(String clusterId) {
        //'clusterSetup','clusterJoin'
        ArrayList<String> typeIn = Lists.newArrayList(DeployTaskTypeEnum.CLUSTER_SETUP.getCode(),
                                                      DeployTaskTypeEnum.CLUSTER_JOIN.getCode());
        return deployTaskRepository.findAllBySubTargetAndTypeIn(clusterId, typeIn);
    }

    /**
     * Delete by primary key int.
     *
     * @param id the id
     *
     * @return the int
     */
    @Override
    public int deleteByPrimaryKey(String id) {
        this.deployTaskRepository.deleteById(id);
        return 1;
    }

    @Override
    public int deleteAll(List<DeployTask> tasks) {
        this.deployTaskRepository.deleteAll(tasks);
        return tasks.size();
    }

    @Override
    public int deleteByPrimaryKeys(List<String> ids) {
        List<DeployTask> deployTasks = deployTaskRepository.findAllByIdIn(ids);
        this.deployTaskRepository.deleteAll(deployTasks);
        return deployTasks.size();
    }

    @Override
    public List<DeployTask> getClusterTask(List<String> subTargets) {
        //'clusterSetup','clusterJoin'
        ArrayList<String> typeIn = Lists.newArrayList(DeployTaskTypeEnum.CLUSTER_SETUP.getCode(),
                                                      DeployTaskTypeEnum.CLUSTER_JOIN.getCode());
        return deployTaskRepository.findAllBySubTargetInAndTypeIn(subTargets, typeIn);
    }

    @Override
    public DeployTask setupCreateStackTask(Long stackId, String targetName, String taskStatus) {
        DeployTask deployTask = new DeployTask();
        deployTask.setTarget(Convert.toStr(stackId));
        deployTask.setName("创建我的编排-" + targetName);
        deployTask.setTargetName(targetName);
        deployTask.setStatus(taskStatus);
        deployTask.setStatusName(basicCodeService.getTaskStatusName(deployTask.getStatus()));
        deployTask.setType(DeployTaskType.CREATE_STACK);
        deployTask.setTotalQuantity(1);
        deployTask.setStartDate(new Date());
        deployTask.setEndDate(new Date());
        deployTask.setCompletedQuantity(1);
        deployTask.setOrgSid(BasicInfoUtil.getCurrentOrgSid());
        try {
            BasicWebUtil.prepareInsertParams(deployTask);
        } catch (Exception e) {
        }
        this.deployTaskRepository.save(deployTask);
        return deployTask;
    }

    @Override
    public DeployTask restartResVmTask(ResVm resVm, Map<String, Object> taskDetail) {
        DeployTask deployTask = buildDeployTask(resVm, DeployTaskType.RESTART_HOST, DeployTaskStatus.RUNNING);
        // 任务必须依赖于前面任务成功
        taskDetail.put("MUST_DEPEND_ON_BEFORE_SUCCESS", false);
        deployTask.setTaskDetail(JsonUtil.toJson(taskDetail));
        this.deployTaskRepository.save(deployTask);

        return deployTask;
    }

    @Override
    public DeployTask startResVmTask(ResVm resVm) {
        DeployTask deployTask = buildDeployTask(resVm, DeployTaskType.START_HOST, DeployTaskStatus.RUNNING);
        // 任务必须依赖于前面任务成功
        deployTask.setTaskDetail(JsonUtil.toJson(MapsKit.of("MUST_DEPEND_ON_BEFORE_SUCCESS", false)));
        this.deployTaskRepository.save(deployTask);

        return deployTask;
    }

    @Override
    public List<DeployTask> reinstallSystemTask(ResVm resVm) {
        List<DeployTask> result = new LinkedList<>();

        DeployTask deployTask = buildDeployTask(resVm, DeployTaskType.REINSTALL_SYSTEM, DeployTaskStatus.RUNNING);
        // 任务必须依赖于前面任务成功
        deployTask.setTaskDetail(JsonUtil.toJson(MapsKit.of("MUST_DEPEND_ON_BEFORE_SUCCESS", false)));
        this.deployTaskRepository.save(deployTask);
        result.add(deployTask);

        // 接管任务
        result.addAll(deployResVmTask(resVm, deployTask.getId()));
        return result;
    }

    @Override
    public void installSnmpTask(List<ResVm> resVms) {
        resVms.forEach(resVm -> {
            if (!ResVmManageStatus.CONNECTED.equals(resVm.getManageStatus())) {
                log.warn("{}未接管，无法安装监控组件!", resVm.getHostName());
                return;
            }
            DeployTask deployTask = buildDeployTask(resVm, DeployTaskType.INSTALL_SNMP, DeployTaskStatus.PENDING);
            this.deployTaskRepository.save(deployTask);

            this.basicDeployPlaybookService.setupPlaybook(Collections.singletonList(deployTask));

            HostDepoyEvent hostDepoyEvent = new HostDepoyEvent(resVm);
            hostDepoyEvent.setType(DeployTaskType.INSTALL_SNMP);
            hostDepoyEvent.setApiPath(AnsibleServerMethod.ControlApi.AGENT);
            hostDepoyEvent.setTaskId(deployTask.getId());
            hostDepoyEvent.setManual(true);

            SpringContextHolder.publishEvent(hostDepoyEvent);
        });
    }
}
