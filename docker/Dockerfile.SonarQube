FROM image.rightcloud.com.cn/distroless/rightcloud-lib:4.0 AS compiler
LABEL maintainer="Shi<PERSON><PERSON><PERSON>iang <<EMAIL>>"

ARG ADAPTER_PROJECT
ARG PARENT_PROJECT

COPY ${ADAPTER_PROJECT} /usr/share/rightcloud-adapter/
COPY ${PARENT_PROJECT} /usr/share/parent

WORKDIR /usr/share/parent
RUN mvn clean install -T 4C -s /usr/share/maven/ref/settings.xml  -Dmaven.test.skip=true
RUN mvn sonar:sonar -Dsonar.projectKey=Computing-First-Networking -Dsonar.host.url=http://*************:9000 -Dsonar.login=****************************************

WORKDIR /usr/share/rightcloud-adapter
RUN  mvn clean package -T 4C -P dev -s /usr/share/maven/ref/settings.xml  -Dmaven.test.skip=true -Dxjar.password=rightcloud.secure -Dxjar.targetDir=/usr/share/rightcloud-adapter/rightcloud-adapter-facade/target/
RUN mvn sonar:sonar -Dsonar.projectKey=Computing-First-Networking -Dsonar.host.url=http://*************:9000 -Dsonar.login=****************************************

FROM swr.cn-east-3.myhuaweicloud.com/distroless/openjdk:jre-8.322-alpine.3.16.0

COPY --from=compiler /usr/share/rightcloud-adapter/rightcloud-adapter-facade/target/rightcloud-*.jar app.jar
COPY --from=compiler /usr/share/rightcloud-adapter/docker/env-conf /usr/bin/env-conf
COPY --from=compiler /usr/share/rightcloud-adapter/docker/entrypoint.sh entrypoint.sh
COPY --from=compiler /usr/share/rightcloud-adapter/conf/env.json env.json


RUN set -ex \
    && sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories && apk update  \
    && chmod 500 /entrypoint.sh \
    && chmod 550 /usr/bin/env-conf \
    && ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && chown cmp:cmp /app.jar entrypoint.sh env.json \
    && chown cmp:cmp /usr/bin/env-conf \
    && mkdir /opt/cmp/ \
    && mkdir /output \
    && chown cmp:cmp /output -R \
    && chown cmp:cmp /opt/cmp/ -R \
    && chown cmp:cmp /home/<USER>/ -R \
    && chmod 400 app.jar

#hook

ARG DROPUSER="sync halt shutdown operator"
ARG DROPCOMMAND="readelf objdump mirror ld rpcgen netcat make strace gdb gcc cpp tcpdump nc perl lua nm mirror javac jdb "
RUN  for delusered in $DROPUSER ;do deluser $delusered ||true ;done \
    && find / -name *.crt  |xargs rm -rf  \
    && find / -name *.pem |xargs rm -f \
    && find / -name *.gitignore |xargs rm -rf \
    && rm -rf /etc/ssl /etc/pki /usr/bin/crontab /etc/crontab \
    && for command in $DROPCOMMAND ;do command_path=`which $command`  rm -rf $command_path ||true ;done \
    &&df | awk '{if (NR!=1) print $6}' | xargs -I '{}' find -L '{}' -xdev -type l 2>/dev/null  | sort | grep -Ev '^/run|^/proc|^/sys|^/dev' |xargs -I '{}' rm -f '{}' || echo ok

USER cmp
ENTRYPOINT ["/entrypoint.sh"]
