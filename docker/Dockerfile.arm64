FROM image.rightcloud.com.cn/arm64v8/qemu-user-static:x86_64-aarch64 as qemu
LABEL maintainer="yulei <<EMAIL>>"
FROM image.rightcloud.com.cn/arm64v8/rightcloud-lib-arm64:4.0 AS compiler

COPY --from=qemu /usr/bin/qemu-aarch64-static /usr/bin

ARG ADAPTER_PROJECT
COPY ${ADAPTER_PROJECT} /usr/share/rightcloud-adapter/
WORKDIR /usr/share/rightcloud-adapter
RUN  mvn clean package -T 4C -P dev -s /usr/share/maven/ref/settings.xml  -Dmaven.test.skip=true -Dxjar.password=rightcloud.secure -Dxjar.targetDir=/usr/share/rightcloud-adapter/rightcloud-adapter-facade/target/

FROM image.rightcloud.com.cn/arm64v8/openjdk:8u212-alpine-tini-arm64
COPY --from=qemu /usr/bin/qemu-aarch64-static /usr/bin
COPY --from=compiler /usr/share/rightcloud-adapter/rightcloud-adapter-facade/target/rightcloud-*.jar app.jar
COPY --from=compiler /usr/share/rightcloud-adapter/docker/entrypoint.sh entrypoint.sh
COPY --from=compiler /usr/share/rightcloud-adapter/conf/env.json env.json
COPY --from=qemu /usr/bin/qemu-aarch64-static /usr/bin
RUN set -ex \
    && chmod +x /entrypoint.sh \
    && ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime

ENTRYPOINT ["/bin/sh","/entrypoint.sh"]
RUN  rm -f /usr/bin/qemu-aarch64-static
