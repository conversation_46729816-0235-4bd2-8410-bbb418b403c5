FROM image.rightcloud.com.cn/arm64v8/qemu-user-static:x86_64-aarch64 as qemu
FROM image.rightcloud.com.cn/arm64v8/rightcloud-lib-arm64:4.0 AS compiler
COPY --from=qemu /usr/bin/qemu-aarch64-static /usr/bin
LABEL maintainer="yulei <<EMAIL>>"

ARG SERVER_PROJECT

COPY ${SERVER_PROJECT} /usr/share/rightcloud/
WORKDIR /usr/share/rightcloud
RUN mvn clean package -T 4C  -P dev  -s /usr/share/maven/ref/settings.xml -Dmaven.test.skip=true -Dxjar.password=rightcloud.secure -Dxjar.targetDir=/usr/share/rightcloud/target

FROM image.rightcloud.com.cn/arm64v8/openjdk:8u212-alpine-tini-arm64
COPY --from=qemu /usr/bin/qemu-aarch64-static /usr/bin

COPY --from=compiler /usr/share/rightcloud/target/*.jar app.jar
COPY --from=compiler /usr/share/rightcloud/docker/env-conf-arm64 /usr/bin/env-conf
COPY --from=compiler /usr/share/rightcloud/docker/entrypoint.sh entrypoint.sh

RUN set -ex \
    && apk add curl \
    && apk add jq \
    && apk add --update font-adobe-100dpi ttf-dejavu fontconfig \
    && chmod +x /entrypoint.sh \
    && chmod +x /usr/bin/env-conf \
    && rm -rf /tmp/* /var/tmp/* \
    && rm -rf /root/.cache .build-deps \
    && ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime

ENTRYPOINT ["/entrypoint.sh"]
