ARG COMPILER_IMAGE
FROM ${COMPILER_IMAGE:-swr.cn-east-3.myhuaweicloud.com/distroless/rightcloud-lib:4.0} AS compiler


COPY ./ /usr/share/rightcloud/
WORKDIR /usr/share/rightcloud
RUN mvn clean package -T 4C  -P dev  -s /usr/share/maven/ref/settings.xml -Dmaven.test.skip=true -Dxjar.password=rightcloud.secure -Dxjar.targetDir=/usr/share/rightcloud/target

ARG RUNTIME_IMAGE
FROM ${RUNTIME_IMAGE:-swr.cn-east-3.myhuaweicloud.com/distroless/openjdk:8u212-jre-alpine3.9}

COPY --from=compiler /usr/share/rightcloud/target/*.jar app.jar
COPY --from=compiler /usr/share/rightcloud/docker/env-conf /usr/bin/env-conf
COPY --from=compiler /usr/share/rightcloud/docker/entrypoint.sh entrypoint.sh

RUN set -ex \
    && apk add curl \
    && apk add jq \
    && apk add --update font-adobe-100dpi ttf-dejavu fontconfig \
    && chmod +x /entrypoint.sh \
    && chmod +x /usr/bin/env-conf \
    && rm -rf /tmp/* /var/tmp/* \
    && rm -rf /root/.cache .build-deps \
    && ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime

ENTRYPOINT ["/entrypoint.sh"]
