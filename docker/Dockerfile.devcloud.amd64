ARG COMPILER_IMAGE
ARG RUNTIME_IMAGE

FROM ${COMPILER_IMAGE:-image.rightcloud.com.cn/distroless/rightcloud-lib:4.1} as compiler

ARG PARENT_GROUP
ARG GIT_URL
ARG BRANCH
ADD ./ /usr/share/rightcloud/

WORKDIR /usr/share

RUN git config --global http.sslVerify false && set -ex \
  && git clone --branch ${BRANCH} https://oauth2:G3Tyn_5Y2PtAzNzGhRSL@${GIT_URL}/${PARENT_GROUP}/rightcloud-parent.git

RUN cd /usr/share/rightcloud-parent && mvn clean install -T 4C -s /usr/share/maven/ref/settings.xml  -Dmaven.test.skip=true

WORKDIR /usr/share/rightcloud

RUN mvn clean package -T 4C -P dev -s /usr/share/maven/ref/settings.xml  -Dmaven.test.skip=true -Dxjar.password=rightcloud.secure -Dxjar.targetDir=/usr/share/rightcloud/rightcloud-adapter-facade/target/



FROM ${RUNTIME_IMAGE:-image.rightcloud.com.cn/distroless/openjdk:8u212-jre-alpine3.9} as runtime

COPY --from=compiler /usr/share/rightcloud/rightcloud-adapter-facade/target/rightcloud-*.jar app.jar
COPY --from=compiler /usr/share/rightcloud/docker/env-conf /usr/bin/env-conf
COPY --from=compiler /usr/share/rightcloud/docker/entrypoint.sh entrypoint.sh
COPY --from=compiler /usr/share/rightcloud/conf/env.json env.json


RUN set -ex \
    && chmod +x /entrypoint.sh \
    && chmod +x /usr/bin/env-conf \
    && ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime


#hook
ENTRYPOINT ["/entrypoint.sh"]

