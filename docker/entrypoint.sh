#!/bin/sh

#
# Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
#
umask 0027
EXEC_PARAM_COMMAND="-Djava.security.egd=file:/dev/random "
EXEC_END_COMMAND=" -jar app.jar "

if [ $SERVER_NAME ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.server.name=$SERVER_NAME "
fi

if [ $SERVER_PORT ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.server.port=$SERVER_PORT "
fi

if [ $SERVER_MGR_PORT ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.server.mgr.port=$SERVER_MGR_PORT "
fi

if [ $REDIS_HOST ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.redis.host=$REDIS_HOST "
fi

if [ $REDIS_PORT ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.redis.port=$REDIS_PORT "
fi

if [ $REDIS_IS_SENTINEL ]; then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.redis.is_sentinel=$REDIS_IS_SENTINEL "
fi

if [ $REDIS_IS_SENTINEL ]; then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.redis.is_sentinel=$REDIS_IS_SENTINEL "
fi



if [ $REDIS_MASTER_NAME ]; then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.redis.master=$REDIS_MASTER_NAME "
fi

if [ $REDIS_SENTINELS ]; then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.redis.sentinels=$REDIS_SENTINELS "
fi

if [ $MQ_HOST ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.mq.host=$MQ_HOST "
fi

if [ $MQ_PORT ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.mq.port=$MQ_PORT "
fi

if [ $MQ_USERNAME ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.mq.username=$MQ_USERNAME "
fi



if [ $MONGODB_HOST ]; then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.mongo.host=$MONGODB_HOST "
fi

if [ $MONGODB_PORT ]; then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.mongo.port=$MONGODB_PORT "
fi

if [ $MONGODB_USERNAME ]; then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.mongo.username=$MONGODB_USERNAME "
fi



if [ $FLUENTD_HOST ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.fluentd.host=$FLUENTD_HOST "
fi

if [ $FLUENTD_PORT ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.fluentd.port=$FLUENTD_PORT "
fi

if [ $FLUENTD_ENABLE ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.fluentd.enable=$FLUENTD_ENABLE "
fi

if [ $NACOS_ADDR ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.nacos.addr=$NACOS_ADDR "
fi

if [ $MQ_P12_FILE ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.mq.ssl.keyStore=$MQ_P12_FILE "
fi

if [ $MQ_JKS_FILE ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.mq.ssl.trustStore=$MQ_JKS_FILE "
fi



if [ $MQ_SSL_ENABLE ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.mq.ssl.enabled=$MQ_SSL_ENABLE "
fi

if [ $MQ_ALGORITHM ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.mq.ssl.algorithm=$MQ_ALGORITHM "
fi



if [ $CLOUDSTAR_KEYSTORE_TYPE ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.keystore.type=$CLOUDSTAR_KEYSTORE_TYPE "
fi




if [ $MONGODB_ALGORITHM ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.mongodb.ssl.algorithm=$MONGODB_ALGORITHM "
fi

if [ $MONGODB_SSL_ENABLE ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.mongodb.ssl.enabled=$MONGODB_SSL_ENABLE "
fi

if [ $MONGODB_CLIENT_KEYSTORE_PATH ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.keystore.mongodb.filepath=$MONGODB_CLIENT_KEYSTORE_PATH "
fi



if [ $MONGODB_CLIENT_P12_PATH ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.p12.mongodb.filepath=$MONGODB_CLIENT_P12_PATH "
fi



if [ $MINIO_ALGORITHM ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.minio.ssl.algorithm=$MINIO_ALGORITHM "
fi

if [ $MINIO_SSL_ENABLE ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.minio.ssl.enabled=$MINIO_SSL_ENABLE "
fi

if [ $MINIO_CLIENT_KEYSTORE_PATH ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.keystore.minio.filepath=$MINIO_CLIENT_KEYSTORE_PATH "
fi



if [ $MINIO_CLIENT_P12_PATH ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.p12.minio.filepath=$MINIO_CLIENT_P12_PATH "
fi






if [ $CMP_ADAPTER_KEYSTORE_PATH ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.keystore=$CMP_ADAPTER_KEYSTORE_PATH "
fi


if [ $LOCAL_POD_IP ]; then
   EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.local.podip=$LOCAL_POD_IP "
fi




if [ $REDIS_SSL_ENABLE ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.redis.ssl=$REDIS_SSL_ENABLE "
fi

if [ $REDIS_SSL_JKS_FILE ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.redis.trustKeyStore=$REDIS_SSL_JKS_FILE "
fi

if [ $CRYPTO_KEY_PATH ]; then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.key.path=$CRYPTO_KEY_PATH"
fi


export JVM_OPTS="$JVM_OPTS $ADAPTER_JAVA_OPTS -XX:+UnlockExperimentalVMOptions -XX:+UseCGroupMemoryLimitForHeap"
EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dspring.profiles.active=$PROFILES_ACTIVE "

#/usr/bin/env-conf
exec tini -- java ${JVM_OPTS} ${EXEC_PARAM_COMMAND} ${EXEC_END_COMMAND}

