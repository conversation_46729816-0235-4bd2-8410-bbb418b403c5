FROM swr.cn-east-3.myhuaweicloud.com/distroless/rightcloud-lib:4.2-j<PERSON><PERSON> AS compiler

ARG SERVER_PROJECT
ARG BRANCH
COPY ${SERVER_PROJECT} /usr/share/rightcloud/
WORKDIR /usr/share/


RUN git config --global http.sslVerify false && set -ex \
  && git clone --branch ${BRANCH} https://oauth2:G3Tyn_5Y2PtAzNzGhRSL@*************:8443/rightcloud-v4/rightcloud-parent \
  && git clone --branch ${BRANCH} https://oauth2:G3Tyn_5Y2PtAzNzGhRSL@*************:8443/rightcloud-v4/rightcloud-module-support \
  && git clone --branch ${BRANCH} https://oauth2:G3Tyn_5Y2PtAzNzGhRSL@*************:8443/cloudstar/cloud-boss/rightcloud-common

RUN cd /usr/share/rightcloud-parent && mvn clean install  -T 4C  -P dev  -s /usr/share/maven/ref/settings.xml  -Dmaven.test.skip=true
RUN cd /usr/share/rightcloud-module-support && mvn clean install  -T 4C  -P dev  -s /usr/share/maven/ref/settings.xml  -Dmaven.test.skip=true
RUN cd /usr/share/rightcloud-common && mvn clean install  -T 4C  -P dev  -s /usr/share/maven/ref/settings.xml  -Dmaven.test.skip=true

WORKDIR /usr/share/rightcloud
RUN mvn clean package -T 4C  -P dev  -s /usr/share/maven/ref/settings.xml -Dmaven.test.skip=true -Dxjar.password=rightcloud.secure -Dxjar.targetDir=/usr/share/rightcloud/target

FROM swr.cn-east-3.myhuaweicloud.com/distroless/openjdk:jre-8.322-alpine.3.16.0

COPY --from=compiler /usr/share/rightcloud/target/*.jar app.jar
COPY --from=compiler /usr/share/rightcloud/docker/env-conf /usr/bin/env-conf
COPY --from=compiler /usr/share/rightcloud/docker/entrypoint.sh entrypoint.sh

RUN set -ex \
    && sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories && apk update  \
    && apk add curl \
    && apk add jq \
    && chmod 500 /entrypoint.sh \
    && chmod 550 /usr/bin/env-conf \
    && rm -rf /tmp/* /var/tmp/* \
    && rm -rf /root/.cache .build-deps \
    && ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && chown cmp:cmp /app.jar entrypoint.sh \
    && chown cmp:cmp /usr/bin/env-conf \
    && mkdir /opt/cmp/ \
    && mkdir /output \
    && chown cmp:cmp /output -R \
    && chown cmp:cmp /opt/cmp/ -R \
    && chown cmp:cmp /home/<USER>/ -R \
    && chmod 400 app.jar


ARG DROPUSER="sync halt shutdown operator"
ARG DROPCOMMAND="readelf objdump mirror ld rpcgen netcat make strace gdb gcc cpp tcpdump nc perl lua nm mirror javac jdb "
RUN  for delusered in $DROPUSER ;do deluser $delusered ||true ;done \
    && find / -name *.crt  |xargs rm -rf  \
    && find / -name *.pem |xargs rm -f \
    && find / -name *.gitignore |xargs rm -rf \
    && rm -rf /etc/ssl /etc/pki /usr/bin/crontab /etc/crontab \
    && for command in $DROPCOMMAND ;do command_path=`which $command`  rm -rf $command_path ||true ;done \
    &&df | awk '{if (NR!=1) print $6}' | xargs -I '{}' find -L '{}' -xdev -type l 2>/dev/null  | sort | grep -Ev '^/run|^/proc|^/sys|^/dev' |xargs -I '{}' rm -f '{}' || echo ok

USER cmp

ENTRYPOINT ["/entrypoint.sh"]
