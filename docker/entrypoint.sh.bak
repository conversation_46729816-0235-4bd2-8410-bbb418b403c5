#!/bin/sh

umask 0027

EXEC_PARAM_COMMAND=" -Djava.security.egd=file:/dev/random "
EXEC_END_COMMAND=" -jar app.jar --jar.keyfile=/tmp/env_setting.dat"

if [ $SERVER_NAME ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.server.name=$SERVER_NAME "
fi

if [ $SERVER_PORT ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.server.core.port=$SERVER_PORT "
fi

if [ $SERVER_DUBBO_PORT ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.server.core.dubbo.port=$SERVER_DUBBO_PORT "
fi

# server_core_port 优先
if [ $SERVER_CORE_PORT ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.server.core.port=$SERVER_CORE_PORT "
fi

if [ $SERVER_MGR_PORT ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.server.mgr.port=$SERVER_MGR_PORT "
fi

if [ $DB_HOST ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.db.address=$DB_HOST "
fi

if [ $DB_PORT ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.db.port=$DB_PORT "
fi

if [ $DB_USERNAME ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.db.username=$DB_USERNAME "
fi

if [ $DB_PASSWORD ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.db.password=$DB_PASSWORD "
fi

if [ $REDIS_HOST ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.redis.host=$REDIS_HOST "
fi

if [ $REDIS_PORT ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.redis.port=$REDIS_PORT "
fi

if [ $REDIS_IS_SENTINEL ]; then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.redis.is_sentinel=$REDIS_IS_SENTINEL "
fi

if [ $REDIS_SENTINEL_PASSWORD ]; then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.redis.sentinel.password=$REDIS_SENTINEL_PASSWORD"
fi

if [ $REDIS_PASSWORD ]; then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.redis.password=$REDIS_PASSWORD"
fi

if [ $REDIS_MASTER_NAME ]; then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.redis.master=$REDIS_MASTER_NAME "
fi

if [ $REDIS_SENTINELS ]; then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.redis.sentinels=$REDIS_SENTINELS "
fi

if [ $MQ_HOST ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.mq.host=$MQ_HOST "
fi

if [ $MQ_PORT ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.mq.port=$MQ_PORT "
fi

if [ $MQ_USERNAME ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.mq.username=$MQ_USERNAME "
fi

if [ $MQ_PASSWORD ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.mq.password=$MQ_PASSWORD "
fi

if [ $MONGODB_HOST ]; then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.mongo.host=$MONGODB_HOST "
fi

if [ $MONGODB_PORT ]; then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.mongo.port=$MONGODB_PORT "
fi

if [ $MONGODB_USERNAME ]; then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.mongo.username=$MONGODB_USERNAME "
fi

if [ $MONGODB_PASSWORD ]; then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.mongo.password=$MONGODB_PASSWORD "
fi

if [ $MONGO_REPLICA_SET ];then
  PROFILES_ACTIVE=${PROFILES_ACTIVE}",mongo-ha"
  EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.mongo.replicaset=$MONGO_REPLICA_SET "
fi

if [ $FLUENTD_HOST ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.fluentd.host=$FLUENTD_HOST "
fi

if [ $FLUENTD_PORT ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.fluentd.port=$FLUENTD_PORT "
fi

if [ $FLUENTD_ENABLE ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.fluentd.enable=$FLUENTD_ENABLE "
fi

if [ $SWAGGER_ENABLE ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.swagger.enable=$SWAGGER_ENABLE "
fi

if [ $HA_DEPLOY ]; then
  EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dha.deploy=$HA_DEPLOY "
fi

if [ $NACOS_ADDR ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.nacos.addr=$NACOS_ADDR "
fi

if [ $CRON_REPAIR ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.cron.repair=$CRON_REPAIR "
fi

if [ $CRON_COLLECTOR ];then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.cron.collector=$CRON_COLLECTOR "
fi

export JAVA_OPTS="$SERVER_JAVA_OPTS -XX:+UnlockExperimentalVMOptions -XX:+UseCGroupMemoryLimitForHeap"

EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dspring.profiles.active=$PROFILES_ACTIVE "
echo "EXEC COMMAND:java " ${JAVA_OPTS} ${EXEC_PARAM_COMMAND} ${EXEC_END_COMMAND}
/usr/bin/env-conf
exec tini -- java ${JAVA_OPTS} ${EXEC_PARAM_COMMAND} ${EXEC_END_COMMAND}
