/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.core.pojo.dto.res;

import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.vo.IpVO;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmNic;
import com.google.common.base.Strings;

import org.springframework.util.ClassUtils;

import java.io.Serializable;
import java.util.Objects;
import lombok.Data;

/**
 * The type Res vm netcard.
 */
@Data
public class ResVmNetcard implements Serializable {

    private static final long serialVersionUID = 1L;

    private String ipAddress = "";
    private String resVmId;
    private String macAddress;
    private String resVsPortGroupId;
    private String resVsPortGroupName;

    /**
     * Instantiates a new Res vm netcard.
     */
    public ResVmNetcard() {
        super();
    }

    /**
     * Instantiates a new Res vm netcard.
     *
     * @param vmNic the vm nic
     */
    public ResVmNetcard(VmNic vmNic) {
        this.macAddress = vmNic.getMac();
        if (!Strings.isNullOrEmpty(vmNic.getPrivateIp())) {
            this.ipAddress = vmNic.getPrivateIp();
        } else if (!Strings.isNullOrEmpty(vmNic.getPublicIp())) {
            this.ipAddress = vmNic.getPublicIp();
        } else {
            this.ipAddress = "";
        }
    }

    /**
     * Instantiates a new Res vm netcard.
     *
     * @param ipVO the ip vo
     */
    public ResVmNetcard(IpVO ipVO) {
        this.ipAddress = Strings.nullToEmpty(ipVO.getIp());
        this.macAddress = ipVO.getMac();
        this.resVsPortGroupName = ipVO.getNetworkName();
    }

    /**
     * Instantiates a new Res vm netcard.
     *
     * @param resVmId the res vm id
     * @param macAddress the mac address
     * @param ipAddress the ip address
     * @param resVsPortGroupId the res vs port group id
     * @param resVsPortGroupName the res vs port group name
     */
    public ResVmNetcard(String resVmId, String macAddress, String ipAddress, String resVsPortGroupId,
                        String resVsPortGroupName) {
        this.ipAddress = Strings.nullToEmpty(ipAddress);
        this.resVmId = resVmId;
        this.macAddress = macAddress;
        this.resVsPortGroupId = resVsPortGroupId;
        this.resVsPortGroupName = resVsPortGroupName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || ClassUtils.getUserClass(this) != ClassUtils.getUserClass(o)) {
            return false;
        }
        ResVmNetcard that = (ResVmNetcard) o;
        return Objects.equals(ipAddress, that.ipAddress) &&
                Objects.equals(resVmId, that.resVmId) &&
                Objects.equals(macAddress, that.macAddress);
    }

    @Override
    public int hashCode() {

        return Objects.hash(ipAddress, resVmId, macAddress);
    }
}
