/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.core.pojo.vo.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * The type ResVmVO.
 * <p>
 *
 * <AUTHOR>
 * @date 2019/4/23
 */
@Getter
@Setter
@ToString
public class ResVmVO {
    @ApiModelProperty("ID")
    private String id;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("实例名称")
    private String instanceName;

    @ApiModelProperty("状态名称")
    private String statusName;

    @ApiModelProperty("云环境名称")
    private String cloudEnvName;

    @ApiModelProperty("云环境类型")
    private String cloudEnvType;

    @ApiModelProperty("所有者")
    private String ownerAccount;

    @ApiModelProperty("实例类型")
    private String serverType;

    @ApiModelProperty("内网IP")
    private String innerIp;

    @ApiModelProperty("公网IP")
    private String publicIp;

    @ApiModelProperty("模式")
    private String mode;

    @ApiModelProperty("分区ID")
    private Long resPoolId;

    @ApiModelProperty("云环境ID")
    private Long cloudEnvId;

    private Boolean readOnly;

    private String originOrgName;

    private String platformComponentId;
}
