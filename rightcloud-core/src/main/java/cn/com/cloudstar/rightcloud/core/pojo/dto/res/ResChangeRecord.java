/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.core.pojo.dto.res;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class ResChangeRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    private Long cloudEnvId;
    private String cloudEnvName;
    /**
     * '主机ID'
     */
    private String instanceId;
    private String resourceId;
    /**
     * host（主机）
     */
    private String resType;
    /**
     * 原始规格类型
     */
    private String originalType;
    /**
     * 新调整规格类型
     */
    private String newType;
    private String originalExtra;
    private String newExtra;
    /**
     * '原始价格：如包年包月 则是每月价格，按量则是按量的价格'
     */
    private BigDecimal originalPrice;
    /**
     * '新调整价格：如包年包月 则是每月价格，按量则是按量的价格',
     */
    private BigDecimal newPrice;
    /**
     * '原始计费类型PayAsYouGo（预付费）Subscription（后付费）'
     */
    private String originalBillingType;
    private String newBillingType;
    /**
     * '调整开始时间'
     */
    private Date changeStartTime;
    /**
     *  '调整结束时间'
     */
    private Date changeEndTime;
    private Date createdDt;
    private String createdBy;
    private Date updatedDt;
    private String updatedBy;
    private Long  version;
    private String configDesc;
    private String changeType;

}
