/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.core.pojo.dto.res.platform;

import java.util.Date;
import java.util.Set;

/**
 * The type Volume.
 * <p>
 * Created on 2017/6/1
 *
 * <AUTHOR>
 */
public class Volume extends Resource {
    private String zone;
    private String zoneLableName;
    private Integer size;
    private Date created;
    private String volumeType;
    private String description;
    private String snapshotId;
    private Set<VolumeAttachment> attachments;
    private String storagePurpose;
    private String resVmId;
    private String errorMsg;
    private String zoneName;

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public String getStoragePurpose() {
        return storagePurpose;
    }

    public void setStoragePurpose(String storagePurpose) {
        this.storagePurpose = storagePurpose;
    }

    public String getResVmId() {
        return resVmId;
    }

    public void setResVmId(String resVmId) {
        this.resVmId = resVmId;
    }


    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    public String getZone() {
        return zone;
    }

    public void setZone(String zone) {
        this.zone = zone;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public String getVolumeType() {
        return volumeType;
    }

    public void setVolumeType(String volumeType) {
        this.volumeType = volumeType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getSnapshotId() {
        return snapshotId;
    }

    public void setSnapshotId(String snapshotId) {
        this.snapshotId = snapshotId;
    }

    public Set<VolumeAttachment> getAttachments() {
        return attachments;
    }

    public void setAttachments(Set<VolumeAttachment> attachments) {
        this.attachments = attachments;
    }

    public String getZoneName() {
        return zoneName;
    }

    public void setZoneName(String zoneName) {
        this.zoneName = zoneName;
    }

    public String getZoneLableName() {
        return zoneLableName;
    }

    public void setZoneLableName(String zoneLableName) {
        this.zoneLableName = zoneLableName;
    }
}
