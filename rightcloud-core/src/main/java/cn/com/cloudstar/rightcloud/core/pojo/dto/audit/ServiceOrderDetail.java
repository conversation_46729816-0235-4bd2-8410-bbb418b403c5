/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.core.pojo.dto.audit;

import cn.com.cloudstar.rightcloud.adapter.pojo.price.enums.ChargeTypeEnum;
import cn.com.cloudstar.rightcloud.common.encryptdata.annotation.CCSPIntegralityHashAndVerify;
import cn.com.cloudstar.rightcloud.core.pojo.common.BaseCCSP;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;

@Data
public class ServiceOrderDetail extends BaseCCSP implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 申请单明细ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 申请单ID
     */
    private Long orderId;

    /**
     * 计费类型：包年包月、按量付费
     */
    private String chargeType;

    /**
     * 服务ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long serviceId;

    /**
     * 服务类型
     */
    private String serviceType;

    /**
     * 企业ID
     */
    private Long orgSid;


    /**
     * 状态
     */
    private String status;

    /**
     * 购买数量
     */
    private Integer quantity;

    /**
     * 申请时长
     */
    private Integer duration;

    /**
     * 单价
     */
    @CCSPIntegralityHashAndVerify(segment = 1,defineBigDecimalScale = true,bigDecimalScale = 5)
    private BigDecimal price;

    /**
     * 金额
     */
    @CCSPIntegralityHashAndVerify(segment = 2,defineBigDecimalScale = true,bigDecimalScale = 5)
    private BigDecimal amount;

    /**
     * 服务实例ID
     */
    private Long serviceInstanceId;

    /**
     * 退订时间
     */
    private Date endTime;

    /**
     * 版本号
     */
    private Long version;

    /**
     * 服务属性
     */
    private String serviceAttr;

    /**
     * 服务配置
     */
    private String serviceConfig;

    /**
     * 云服务描述
     */
    private String serviceCategoryDesc;

    /**
     * 服务描述详情
     **/
    private String serviceCategoryDetails;

    /**
     * 应用配置
     */
    private List<Map<String, Object>> appConfigs = new ArrayList<>();

    /**
     * 配置显示
     */
    private String instanceTypeDisplay;

    /**
     * 镜像ID
     */
    private String imageId;

    /**
     * 镜像名称
     */
    private String imageName;

    /**
     * 关联云环境名称
     */
    private String cloudEnvName;

    /**
     * 云环境类型
     */
    private String cloudEnvType;

    /**
     * 服务名称
     */
    private String serviceName;
    /**
     * 服务名称英文
     */
    private String serviceNameUs;

    /**
     * 实例名称
     */
    private String instanceName;

    /**
     * res vd配置
     */
    private transient List<Map<String, Object>> resVdConfigs = new ArrayList<>();

    /**
     * 显示内容
     */
    private String displayContent;

    /**
     * 之前规格
     */
    private String preSpec;

    /**
     * 自服务部署实例开始时间
     */
    private Date sfServiceDeployInstStartTime;
    /**
     * 部署实例到期时间
     */
    private Date sfServiceDeployInstEndTime;

    /**
     * res标识
     */
    private String resFlag;

    /**
     * 集群id
     */
    private Long clusterId;

    /**
     * 折扣系数
     */
    private BigDecimal discountRatio;

    /**
     * 上浮点数
     */
    private BigDecimal floatingRatio;

    /**
     * 资源价格
     */
    private BigDecimal resourcePrice;

    /**
     * 产品服务价格
     */
    private BigDecimal servicePrice;

    /**
     * 额外配置费用
     */
    private BigDecimal oncePrice;
    /**
     * 退订的服务退款
     */
    private BigDecimal serviceAmount;

    /**
     * 退订的额外配置退款
     */
    private BigDecimal extraConfigAmount;

    /**
     * 产品服务详情
     */
    private List<ProductService> services;


    /**
     * 额外信息
     */
    private Map<Object, Object> options;

    /**
     * 特殊处理优惠卷使用
     */
    private BigDecimal couponDiscount;

    /**
     * 客户自定义标志 客户定价custom,系统定价system,混合定价mix
     */
    private String customFlag;


    public BigDecimal getCouponDiscount() {
        return Objects.isNull(couponDiscount) ? BigDecimal.ZERO : couponDiscount;
    }

    /**
     * 格式化申请时长
     */
    public String getTimeLength() {
        if (duration == null || duration == -1 || ChargeTypeEnum.POSTPAID.getValue().equals(chargeType)) {
            return "--";
        }
        if ("year".equals(periodType)) {
            return duration + "年";
        }
        return duration + "个月";
    }

    /**
     * 折扣
     */
    @CCSPIntegralityHashAndVerify(segment = 4,defineBigDecimalScale = true,bigDecimalScale = 5)
    private BigDecimal discount;
    /**
     * 原价
     */
    @CCSPIntegralityHashAndVerify(segment = 3,defineBigDecimalScale = true,bigDecimalScale = 5)
    private BigDecimal originalCost;

    /**
     * 资源id
     */
    private String resourceId;

    /**
     * 资源平台主键
     */
    private String primaryKey;

    /**
     * 前台展示配置
     */
    private String productConfigDesc;
    /**
     * 询价参数
     */
    private String productParams;
    /**
     * 产品代码
     */
    private String productCode;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 关联实例id
     */
    private String refInstanceId;

    private String periodType;
    /**
     * 数量
     */
    private int period;
    /**
     * 云环境id
     */
    private Long cloudEnvId;
    /**
     * 天数
     */
    private Integer unifyDate;
    /**
     * 是否为主资源
     */
    @ApiModelProperty("是否为主资源")
    private boolean principal;
    /**
     * 变更目标类型
     */
    @ApiModelProperty("变更目标类型")
    private String targetType;
    /**
     * 变更目标大小
     */
    @ApiModelProperty("变更目标大小")
    private Integer targetSize;
    /**
     * 云环境分区
     */
    @ApiModelProperty("云环境分区")
    @TableField(exist = false)
    private String region;
    /**
     * 所有者ID
     */
    @ApiModelProperty("所有者ID")
    private String ownerId;
    /**
     * 运营实体ID
     */
    @ApiModelProperty("运营实体ID")
    private Long entityId;
    /**
     * 计算结束时间
     */
    private Date computeEndDate;

    public BigDecimal getPrice() {
        return Objects.isNull(this.price) ? BigDecimal.ZERO : this.price;
    }

    public Integer getQuantity() {
        return Objects.isNull(this.quantity) ? 1 : this.quantity;
    }

    public BigDecimal getFloatingRatio() {
        return Objects.isNull(this.floatingRatio) ? BigDecimal.ONE : this.floatingRatio;
    }


    public BigDecimal getExtraConfigAmount() {
        return Objects.isNull(extraConfigAmount) ? BigDecimal.ZERO : extraConfigAmount;
    }

    public BigDecimal getServiceAmount() {
        return Objects.isNull(serviceAmount) ? BigDecimal.ZERO : serviceAmount;
    }

    public BigDecimal getDiscountRatio() {
        return Objects.isNull(this.discountRatio) ? BigDecimal.ONE : this.discountRatio;
    }


    /**
     * 官网价
     */
    private BigDecimal officialAmount;

    public BigDecimal getOfficialAmount() {
        return Objects.isNull(this.officialAmount) ? BigDecimal.ZERO : this.officialAmount;
    }
    /**
     * 价格明细
     */
    private List<ServiceOrderPriceDetail> priceDetails = Lists.newArrayList();
    /**
     * 当前时间
     */
    private Date now;
    /**
     * 过期使用金额
     */
    private BigDecimal expiredUsedAmount = BigDecimal.ZERO;
    /**
     * 回馈
     */
    private BigDecimal giveBack = BigDecimal.ZERO;
    /**
     * 服务回馈
     */
    private BigDecimal serviceGiveBack = BigDecimal.ZERO;

    /**
     * 补扣明细金额
     */
    private List<ServiceOrderPriceDetail> expiredUsedPriceDetails = new ArrayList<>();

    /**
     * 变更容量时记录负数补差金额
     */
    private BigDecimal negativeAmount;

    /**
     * 冻结时间
     */
    private Date frozenTime;

    /**
     * 上一次结束时间
     */
    private Date preEndTime;

    /**
     * depTrain：开发环境/训练,depOnline:部署上线
     */
    private String applyType;
    /**
     * 弹性文件 原产品结束时间
     */
    private Date oldEndTime;

    /**
     * reservedResource 保留资源；releaseResource 释放资源；continueOperation 继续作业；stopOperation 停止作业
     */
    private String freezingStrategy;

    /**
     * 冻结策略缓冲期。停止作业和释放资源时配置
     */
    private Integer strategyBufferPeriod;

}
