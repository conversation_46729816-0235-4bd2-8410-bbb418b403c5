/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.core.pojo.dto.elastic;

import java.util.Date;

public class ElasticHistory {
    private Long id;

    private Long elasticGroupId;

    private String elasticMode;

    private Integer elasticNum;

    private Integer completed;

    private Integer successed;

    private Date elasticTime;

    private String status;

    private String createdBy;

    private Date createdDt;

    private String updatedBy;

    private Date updatedDt;

    private Long version;

    private String groupName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getElasticGroupId() {
        return elasticGroupId;
    }

    public void setElasticGroupId(Long elasticGroupId) {
        this.elasticGroupId = elasticGroupId;
    }

    public String getElasticMode() {
        return elasticMode;
    }

    public void setElasticMode(String elasticMode) {
        this.elasticMode = elasticMode == null ? null : elasticMode.trim();
    }

    public Integer getElasticNum() {
        return elasticNum;
    }

    public void setElasticNum(Integer elasticNum) {
        this.elasticNum = elasticNum;
    }

    public Integer getCompleted() {
        return completed;
    }

    public void setCompleted(Integer completed) {
        this.completed = completed;
    }

    public Integer getSuccessed() {
        return successed;
    }

    public void setSuccessed(Integer successed) {
        this.successed = successed;
    }

    public Date getElasticTime() {
        return elasticTime;
    }

    public void setElasticTime(Date elasticTime) {
        this.elasticTime = elasticTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreatedDt() {
        return createdDt;
    }

    public void setCreatedDt(Date createdDt) {
        this.createdDt = createdDt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy == null ? null : updatedBy.trim();
    }

    public Date getUpdatedDt() {
        return updatedDt;
    }

    public void setUpdatedDt(Date updatedDt) {
        this.updatedDt = updatedDt;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

}
