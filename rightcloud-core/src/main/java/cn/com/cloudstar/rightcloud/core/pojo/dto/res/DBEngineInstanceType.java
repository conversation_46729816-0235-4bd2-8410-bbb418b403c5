/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.core.pojo.dto.res;

/**
 * The type DBInstanceType.
 * <p>
 *
 * <AUTHOR>
 * @date 2018/1/31
 */
public class DBEngineInstanceType {

    private String engine;
    private String instanceType;
    private String cpu;
    private String memory;

    public DBEngineInstanceType(){}

    public DBEngineInstanceType(String engine, String instanceType, String cpu, String memory) {
        this.engine = engine;
        this.instanceType = instanceType;
        this.cpu = cpu;
        this.memory = memory;
    }

    public String getEngine() {
        return engine;
    }

    public void setEngine(String engine) {
        this.engine = engine;
    }

    public String getInstanceType() {
        return instanceType;
    }

    public void setInstanceType(String instanceType) {
        this.instanceType = instanceType;
    }

    public String getCpu() {
        return cpu;
    }

    public void setCpu(String cpu) {
        this.cpu = cpu;
    }

    public String getMemory() {
        return memory;
    }

    public void setMemory(String memory) {
        this.memory = memory;
    }
}
