/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.core.pojo.dto.monitor;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * Created by ShiWenQiang on 2016/9/26.
 */
@Data
public class AlarmRuleListDTO implements Serializable {

    /**
     * 告警策略id
     */
    @ApiModelProperty(value = "告警策略id")
    private Long id;

    /**
     * 告警策略名称
     */
    @ApiModelProperty(value = "告警策略名称")
    private String name;

    /**
     * 告警策略类型
     */
    @ApiModelProperty(value = "告警策略类型")
    private String strategyTypeName;

    /**
     * 策略已应用主机数
     */
    @ApiModelProperty(value = "策略已应用主机数")
    private Long targetCount;

    /**
     * 最后更新时间
     */
    @ApiModelProperty(value = "最后更新时间")
    private Date lastUpdateDt;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String orgName;

    /**
     * 描述信息
     */
    @ApiModelProperty(value = "描述信息")
    private String description;

}
