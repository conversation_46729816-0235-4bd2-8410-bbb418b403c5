package cn.com.cloudstar.rightcloud.web.controller;

import cn.com.cloudstar.rightcloud.common.constants.AuthModule;
import cn.com.cloudstar.rightcloud.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.data.entity.MarketShopTag;
import cn.com.cloudstar.rightcloud.data.entity.MarketShopTagRelevance;
import cn.com.cloudstar.rightcloud.data.request.market.MarketShopTagReq;
import cn.com.cloudstar.rightcloud.data.request.market.MarketShopTagTreeReq;
import cn.com.cloudstar.rightcloud.data.response.market.MarketShopTagResp;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicWebUtil;
import cn.com.cloudstar.rightcloud.service.shop.MarketShopTagRelevanceService;
import cn.com.cloudstar.rightcloud.service.shop.MarketShopTagService;
import cn.com.cloudstar.rightcloud.web.annotation.AuthorizeMarket;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 商品分类
 */
@Slf4j
@RestController
@Validated
@RequestMapping("/tag")
public class MarketShopTagController {


    @Resource
    private MarketShopTagService marketShopTagService;
    @Resource
    private MarketShopTagRelevanceService marketShopTagRelevanceService;

    @ApiOperation(httpMethod = "GET", value = "创建商品时查询所有二级分类")
    @GetMapping("/child")
    public RestResult getChildTag(String shopType) {
        return marketShopTagService.getLabels(shopType);
    }

    /**
     * 查询一级分类
     * @param shopType 商品分类类型
     * @return
     */
    @ApiOperation(httpMethod = "GET", value = "查询商品分类的一级分类")
    @GetMapping("/parent")
    @AuthorizeMarket(action = AuthModule.BAI.BAI02.BAI0201)
    public RestResult getParentTag(Integer shopType) {
       return marketShopTagService.getParentTag(shopType);
    }


    @ApiOperation(httpMethod = "POST", value = "添加分类")
    @PostMapping("")
    @AuthorizeMarket(action = AuthModule.BAI.BAI02.BAI0202)
    public RestResult add(@RequestBody @Validated MarketShopTag req) {
        QueryWrapper<MarketShopTag> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(MarketShopTag::getTagName,req.getTagName());
        MarketShopTag one = marketShopTagService.getOne(queryWrapper);
        Assert.isNull(one,"分类名称已存在");

        BasicWebUtil.prepareInsertParams(req);
        marketShopTagService.save(req);
        return RestResult.newSuccess();
    }

    @ApiOperation(httpMethod = "GET", value = "查看分类列表")
    @GetMapping("")
    @AuthorizeMarket(action = AuthModule.BAI.BAI02.BAI0201)
    public IPage<MarketShopTagResp> list(MarketShopTagReq req) {
        return marketShopTagService.getList(req);
    }


    @ApiOperation(httpMethod = "Put", value = "修改分类")
    @PutMapping("")
    @AuthorizeMarket(action = AuthModule.BAI.BAI02.BAI0203)
    public RestResult update(@RequestBody @Validated MarketShopTag req) {
        Assert.notNull(req.getId(),"分类Id必传");
        marketShopTagService.updateById(req);
        return RestResult.newSuccess();
    }

    /**
     * 删除分类
     * @param tagId
     * @return
     */
    @ApiOperation(httpMethod = "post", value = "删除分类")
    @DeleteMapping("{tagId}")
    @AuthorizeMarket(action = AuthModule.BAI.BAI02.BAI0204)
    public RestResult delete(@PathVariable("tagId") Long tagId) {
        QueryWrapper<MarketShopTagRelevance> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(MarketShopTagRelevance::getTagId,tagId);
        List<MarketShopTagRelevance> list = marketShopTagRelevanceService.list(queryWrapper);
        Assert.isTrue(list.isEmpty(),"该分类已被使用，请勿删除！");
        marketShopTagService.removeById(tagId);
        return RestResult.newSuccess();
    }


    @ApiOperation(httpMethod = "GET", value = "查询商品包含的标签")
    @GetMapping("/{shopId}")
    @AuthorizeMarket(action = AuthModule.BAI.BAI02.BAI0201)
    public RestResult getShopTag(@PathVariable("shopId") String shopId) {
        QueryWrapper<MarketShopTagRelevance> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(MarketShopTagRelevance::getShopId,shopId);
        List<MarketShopTagRelevance> list = marketShopTagRelevanceService.list(queryWrapper);
        List<Long> collect = list.stream().map(MarketShopTagRelevance::getTagId).collect(Collectors.toList());
        return new RestResult(collect);
    }

    /**
     * 查询标签树(运营端)
     * @param req 商品分类类型
     * @return
     */
    @ApiOperation(httpMethod = "GET", value = "查询标签树")
    @AuthorizeMarket(action = AuthModule.BAI.BAI01.BAI0101)
    @GetMapping("/tree")
    public RestResult getLabelTagTree(MarketShopTagTreeReq req) {

        return marketShopTagService.getLabelTagTree(req);
    }

    /**
     * 查询标签树(租户端)
     * @param req 商品分类类型
     * @return
     */
    @ApiOperation(httpMethod = "GET", value = "查询标签树")
    @AuthorizeMarket(action = AuthModule.CAI.CAI01.CAI0101, aiMarketFlag = true)
    @GetMapping("/tree/console")
    public RestResult getLabelTagTreeConsole(MarketShopTagTreeReq req) {

        return marketShopTagService.getLabelTagTree(req);
    }

}
