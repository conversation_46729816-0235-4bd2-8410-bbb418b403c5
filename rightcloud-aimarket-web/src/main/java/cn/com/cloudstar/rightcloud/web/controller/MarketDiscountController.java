package cn.com.cloudstar.rightcloud.web.controller;

import cn.com.cloudstar.rightcloud.common.constants.AuthModule;
import cn.com.cloudstar.rightcloud.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.data.request.discount.CreateDiscountRequest;
import cn.com.cloudstar.rightcloud.data.request.discount.DescribeDiscountRequest;
import cn.com.cloudstar.rightcloud.data.request.discount.OperateDiscountRequest;
import cn.com.cloudstar.rightcloud.data.request.discount.UpdateDiscountRequest;
import cn.com.cloudstar.rightcloud.data.response.discount.DescribeDiscountDetailResponse;
import cn.com.cloudstar.rightcloud.data.response.discount.DescribeDiscountResponse;
import cn.com.cloudstar.rightcloud.data.vo.discount.DiscountCheckVo;
import cn.com.cloudstar.rightcloud.module.support.access.cache.AuthUserHolder;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.operationlog.annotation.OperationLog;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationTypeEnum;
import cn.com.cloudstar.rightcloud.service.discount.BizDiscountService;
import cn.com.cloudstar.rightcloud.service.feign.BssServiceFeign;
import cn.com.cloudstar.rightcloud.web.annotation.AuthorizeMarket;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;


@Api(tags = "商品折扣管理")
@Slf4j
@RestController
@RequestMapping("/discount")
public class MarketDiscountController {

    @Resource
    private BssServiceFeign orderServiceFeign;

    @Resource
    private BizDiscountService bizDiscountService;


    /**
     * 创建折扣
     *
     * @param req 要求事情
     * @return {@link RestResult}
     */
    @AuthorizeMarket(action = AuthModule.CAI.CAI02.CAI0202, aiMarketFlag = true)
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "'商品折扣'",
            resource = OperationResourceEnum.DISCOUNT, param = "#request")
    @ApiOperation(httpMethod = "POST", value = "创建折扣")
    @PostMapping("")
    public RestResult<Long> createDiscount(@RequestBody @Validated CreateDiscountRequest req) {
        AuthUser authUser = AuthUserHolder.getAuthUser();
        authUser.setEntityId(1L);
        RestResult result = orderServiceFeign.createDiscount(req);
        return result;
    }

    /**
     * 更新折扣
     *
     * @param request 请求
     * @return {@link RestResult}<{@link Long}>
     */
    @AuthorizeMarket(action = AuthModule.CAI.CAI02.CAI0203, aiMarketFlag = true)
    @ApiOperation(httpMethod = "PUT", value = "编辑折扣")
    @PutMapping("")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "#request.discountName",
            resource = OperationResourceEnum.START_PLATFORM_DISCOUNT, bizId = "#request.discountSid", param = "#request")
    public RestResult<Long> updateDiscount(@Valid @RequestBody UpdateDiscountRequest request) {
        AuthUser authUser = AuthUserHolder.getAuthUser();
        authUser.setEntityId(1L);
        DiscountCheckVo checkVo = DiscountCheckVo.builder().discountSid(request.getDiscountSid()).build();
        bizDiscountService.check(checkVo);
        RestResult result = orderServiceFeign.updateDiscount(request);
        return result;
    }

    /**
     * 删除折扣(折扣管理-平台折扣)
     *
     * @param id id
     * @return {@code RestResult}
     */
    @AuthorizeMarket(action = AuthModule.CAI.CAI02.CAI0205, aiMarketFlag = true)
    @ApiOperation(httpMethod = "DELETE", value = "删除折扣")
    @DeleteMapping("/{id}")
    @OperationLog(type = OperationTypeEnum.DELETE, tagName = "'商品折扣'",
            resource = OperationResourceEnum.DELDISCOUNT, bizId = "#id")
    public RestResult<Long> deletePlatformDiscount(@PathVariable("id") Long id) {
        AuthUser authUser = AuthUserHolder.getAuthUser();
        authUser.setEntityId(1L);
        DiscountCheckVo checkVo = DiscountCheckVo.builder().discountSid(id).build();
        bizDiscountService.check(checkVo);
        return orderServiceFeign.deleteDiscount(id);
    }

    /**
     * 启用、禁用折扣(平台折扣)
     *
     * @param request 操作折扣，禁用、启用，删除客户下所有折扣请求体
     * @return {@code RestResult}
     */
    @AuthorizeMarket(action = AuthModule.CAI.CAI02.CAI0206, aiMarketFlag = true)
    @ApiOperation(httpMethod = "PUT", value = "启用、禁用折扣")
    @PutMapping("/operate")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'商品折扣'",
            resource = OperationResourceEnum.START_DISABLE_DISCOUNT, bizId = "#request.discountSid", param = "#request")
    public RestResult updateBillingStrategyStatusByPlatform(@RequestBody @Valid OperateDiscountRequest request) {
        AuthUser authUser = AuthUserHolder.getAuthUser();
        authUser.setEntityId(1L);
        DiscountCheckVo checkVo = DiscountCheckVo.builder().discountSid(request.getDiscountSid()).build();
        bizDiscountService.check(checkVo);
        return orderServiceFeign.updateBillingStrategyStatus(request);
    }

    /**
     * 获取ai折扣列表
     *
     * @param request 折扣列表查询请求体
     * @return {@code IPage<DescribeDiscountResponse>}
     */
    @AuthorizeMarket(action = AuthModule.CAI.CAI02.CAI0201, aiMarketFlag = true)
    @ApiOperation(httpMethod = "GET", value = "获取商品折扣列表")
    @GetMapping("")
    // @ListenExpireBack
    public IPage<DescribeDiscountResponse> findPlatformDiscounts(DescribeDiscountRequest request) {
        return bizDiscountService.findPlatformDiscounts(request);
    }

    /**
     * 获取折扣详情
     *
     * @param id id
     * @return {@code DescribeDiscountResponse}
     */
    @AuthorizeMarket(action = AuthModule.CAI.CAI02.CAI0204, aiMarketFlag = true)
    @ApiOperation(httpMethod = "GET", value = "获取折扣详情")
    @GetMapping("/{id}")
    // @ListenExpireBack
    public DescribeDiscountDetailResponse findPDiscountDetail(@PathVariable("id")
                                                              @ApiParam(value = "折扣ID", type = "Long", required = true) Long id) {
        return bizDiscountService.findPDiscountDetail(id);
    }

}
