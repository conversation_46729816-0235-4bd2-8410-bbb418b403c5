package cn.com.cloudstar.rightcloud.web.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.data.request.market.MarketAuditListReq;
import cn.com.cloudstar.rightcloud.data.request.market.MarketAuditProcessReq;
import cn.com.cloudstar.rightcloud.data.request.market.MarketProcessDetailsReq;
import cn.com.cloudstar.rightcloud.data.response.market.MarketAuditDetailsResp;
import cn.com.cloudstar.rightcloud.data.response.market.MarketAuditListResp;
import cn.com.cloudstar.rightcloud.data.response.market.MarketProcessDetailsResp;
import cn.com.cloudstar.rightcloud.service.shop.MarketAuditService;

/**
 * <AUTHOR>
 */
@Api(tags = "客户管理下的商品管理")
@Slf4j
@RestController
@Validated
@FieldDefaults(makeFinal = true, level = lombok.AccessLevel.PRIVATE)
@RequiredArgsConstructor
@RequestMapping("/audit")
public class MarketAuditController {

    MarketAuditService marketAuditService;

    @ApiOperation(httpMethod = "GET", value = "审批列表")
    @GetMapping("/shops")
    public IPage<MarketAuditListResp> queryShopByPage(@Validated MarketAuditListReq req) {

        return marketAuditService.listAudit(req);
    }

    @ApiOperation(httpMethod = "GET", value = "审批详情")
    @GetMapping("/details")
    public RestResult<MarketAuditDetailsResp> getAuditDetails(@RequestParam String orderNo) {

        return new RestResult<>(marketAuditService.getAuditDetails(orderNo));
    }

    @ApiOperation(httpMethod = "GET", value = "处理信息")
    @GetMapping("/process")
    public RestResult<MarketProcessDetailsResp> getProcessDetails(@Validated MarketProcessDetailsReq req) {

        return new RestResult<>(marketAuditService.getProcessDetails(req));
    }

    @ApiOperation(httpMethod = "POST", value = "审批商品")
    @PostMapping("/shop")
    public RestResult auditProcess(@RequestBody @Validated MarketAuditProcessReq req) {
        marketAuditService.auditProcess(req);
        return new RestResult<>().setMessage("审批成功");
    }
}
