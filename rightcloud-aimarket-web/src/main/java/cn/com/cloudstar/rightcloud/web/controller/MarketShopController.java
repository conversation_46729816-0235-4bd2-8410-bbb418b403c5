package cn.com.cloudstar.rightcloud.web.controller;

import cn.com.cloudstar.rightcloud.common.constants.AuthModule;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.BQ.BQ010813;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.exception.BizException;
import cn.com.cloudstar.rightcloud.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.data.entity.MarketShop;
import cn.com.cloudstar.rightcloud.data.entity.MarketShopCollect;
import cn.com.cloudstar.rightcloud.data.request.cfn.AlgorithmPageReq;
import cn.com.cloudstar.rightcloud.data.request.cfn.DataStorageResourcesPageReq;
import cn.com.cloudstar.rightcloud.data.request.market.*;
import cn.com.cloudstar.rightcloud.data.response.cfn.AlgorithmPageResp;
import cn.com.cloudstar.rightcloud.data.response.cfn.DataStorageResourcePageResp;
import cn.com.cloudstar.rightcloud.data.response.cfn.PageResult;
import cn.com.cloudstar.rightcloud.data.response.market.*;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.User;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicInfoUtil;
import cn.com.cloudstar.rightcloud.operationlog.annotation.OperationLog;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationTypeEnum;
import cn.com.cloudstar.rightcloud.service.cfn.CfnService;
import cn.com.cloudstar.rightcloud.service.shop.MarketShopCollectService;
import cn.com.cloudstar.rightcloud.service.shop.MarketShopService;
import cn.com.cloudstar.rightcloud.web.annotation.AuthorizeMarket;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.safety.Safelist;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @description: 商品操作
 * @date 2023/8/1
 */
@Api(tags = "商品管理")
@Slf4j
@Validated
@RestController
@RequestMapping("/shop")
public class MarketShopController {

    /**
     * xss标识
     */
    private static final String xssTag = " οnerrοr";

    @Resource
    private MarketShopService marketShopService;
    @Resource
    private MarketShopCollectService marketShopCollectService;

    @Resource
    private CfnService cfnService;

     /**
     * 查询算法列表
     *
     * @param request 参数
     *
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "Get", value = "查询算力平台算法")
    @GetMapping("/algorithms/cfn")
    public Page<AiAlgorithmsQueryResponse> selectAlgorithmsByCfn(AlgorithmsRequest request) {
        return getAlgorithmCfn(request, true);
    }

    @ApiOperation(httpMethod = "Get", value = "查询算力平台数据集")
    @GetMapping("/dataset/cfn")
    public Page<DatasetQueryResponse> selectAlgorithmsByCfn(DatesetRequest request) {
        return getDatesetCfn(request, true);
    }

    @ApiOperation(httpMethod = "Get", value = "查询算力平台数据集")
    @GetMapping("/dataset/cfn/console")
    public Page<DatasetQueryResponse> selectAlgorithmsByCfnConsole(DatesetRequest request) {
        return getDatesetCfn(request, false);
    }

    /**
     * 查询算法列表
     *
     * @param request 参数
     *
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "Get", value = "查询算力平台算法")
    @GetMapping("/algorithms/cfn/console")
    public Page<AiAlgorithmsQueryResponse> selectAlgorithmsByCfnConsole(AlgorithmsRequest request) {
        return getAlgorithmCfn(request, false);
    }

    /**
     * 获取cfn算法
     *
     * @param request
     * @param isAdmin
     */
    private Page<AiAlgorithmsQueryResponse> getAlgorithmCfn(AlgorithmsRequest request, Boolean isAdmin) {
        log.info("获取{}资产列表",request.getShopType());
        if ("dataset".equals(request.getShopType())) {
            DatesetRequest request1 = new DatesetRequest();
            BeanUtil.copyProperties(request, request1);
            request1.setName(request.getAlgorithmName());
            Page<DatasetQueryResponse> datesetCfn = getDatesetCfn(request1, isAdmin);
            Page<AiAlgorithmsQueryResponse> responsePage = new Page<>();
            responsePage.setTotal(datesetCfn.getTotal());
            responsePage.setPages(datesetCfn.getPages());
            responsePage.setSize(datesetCfn.getSize());
            responsePage.setCurrent(datesetCfn.getCurrent());
            responsePage.setRecords(datesetCfn.getRecords().stream().map(r -> {
                AiAlgorithmsQueryResponse a = new AiAlgorithmsQueryResponse();
                a.setId(r.getId());
                a.setName(r.getName());
                a.setCreatedDt(r.getCreatedDt());
                return a;
            }).collect(Collectors.toList()));
            return responsePage;
        }

        AlgorithmPageReq algorithmPageReq = new AlgorithmPageReq();
        algorithmPageReq.setName(request.getAlgorithmName());
        algorithmPageReq.setPageNo(Integer.parseInt(request.getPagenum()) + 1);
        algorithmPageReq.setPageSize(Integer.parseInt(request.getPagesize()));
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (ObjectUtil.isEmpty(authUser)) {
            throw new BizException("未获取到登录用户信息");
        }
        algorithmPageReq.setAccount(authUser.getAccount());
        //algorithmPageReq.setAccount("bssadmin");
        algorithmPageReq.setAdmin(isAdmin);
        PageResult<AlgorithmPageResp> myAlgorithmPage = cfnService.getMyAlgorithmPage(algorithmPageReq);
        if (ObjectUtil.isNotEmpty(myAlgorithmPage)) {
            final Page<AiAlgorithmsQueryResponse> responsePage = new Page<>();
            final List<AlgorithmPageResp> list = myAlgorithmPage.getList();
            if (CollectionUtil.isNotEmpty(list)) {
                ArrayList<AiAlgorithmsQueryResponse> resList = new ArrayList<>();
                for (Object object : list) {
                    AlgorithmPageResp algorithmPageResp = JSONUtil.toBean(object.toString(), AlgorithmPageResp.class);
                    AiAlgorithmsQueryResponse response = new AiAlgorithmsQueryResponse();
                    response.setId(algorithmPageResp.getId().toString());
                    response.setName(algorithmPageResp.getName());
                    response.setCreatedDt(algorithmPageResp.getCreatedDt());
                    if (ObjectUtil.isEmpty(algorithmPageResp.getImageName())) {
                        response.setImage(algorithmPageResp.getEngineId());
                    } else {
                        response.setImage(algorithmPageResp.getImageName());
                    }
                    resList.add(response);
                }
                responsePage.setRecords(resList);
            } else {
                responsePage.setRecords(new ArrayList<>());
            }
            responsePage.setTotal(myAlgorithmPage.getTotal());
            responsePage.setPages(myAlgorithmPage.getTotalPages());
            responsePage.setSize(myAlgorithmPage.getPageSize());
            responsePage.setCurrent(myAlgorithmPage.getPageNo());
            return responsePage;
        } else {
            return new Page<AiAlgorithmsQueryResponse>();
        }
    }

    private Page<DatasetQueryResponse> getDatesetCfn(DatesetRequest request, Boolean isAdmin) {
        DataStorageResourcesPageReq dataStorageResourcesPageReq = new DataStorageResourcesPageReq();
        dataStorageResourcesPageReq.setName(request.getName());
        dataStorageResourcesPageReq.setPageNo(Integer.parseInt(request.getPagenum()) + 1);
        dataStorageResourcesPageReq.setPageSize(Integer.parseInt(request.getPagesize()));
        dataStorageResourcesPageReq.setCategory("dataset");
        dataStorageResourcesPageReq.setAdmin(isAdmin);
        dataStorageResourcesPageReq.setClusterType("BMS");
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (ObjectUtil.isEmpty(authUser)) {
            throw new BizException("未获取到登录用户信息");
        }
        PageResult<DataStorageResourcePageResp> datasetPage = cfnService.getDatasetPage(dataStorageResourcesPageReq);
        if (ObjectUtil.isNotEmpty(datasetPage)) {
            final Page<DatasetQueryResponse> responsePage = new Page<>();
            final List<DataStorageResourcePageResp> list = datasetPage.getList();
            if (CollectionUtil.isNotEmpty(list)) {
                List<DatasetQueryResponse> resList = new ArrayList<>();
                for (Object object : list) {
                    DataStorageResourcePageResp algorithmPageResp = JSONUtil.toBean(object.toString(), DataStorageResourcePageResp.class);
                    DatasetQueryResponse response = new DatasetQueryResponse();
                    response.setId(algorithmPageResp.getId().toString());
                    response.setName(algorithmPageResp.getName());
                    response.setCreatedDt(algorithmPageResp.getCreatedDt());
                    response.setClusterName(algorithmPageResp.getClusterName());
                    resList.add(response);
                }
                responsePage.setRecords(resList);
            } else {
                responsePage.setRecords(new ArrayList<>());
            }
            responsePage.setTotal(datasetPage.getTotal());
            responsePage.setPages(datasetPage.getTotalPages());
            responsePage.setSize(datasetPage.getPageSize());
            responsePage.setCurrent(datasetPage.getPageNo());
            return responsePage;
        } else {
            return new Page<>();
        }
    }


    @ApiOperation(httpMethod = "POST", value = "商品发布")
    @PostMapping()
    @AuthorizeMarket(action = AuthModule.CAI.CAI01.CAI0102 + "," + AuthModule.BAI.BAI01.BAI0102,aiMarketFlag = true)
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "'商品发布'", resource = OperationResourceEnum.AI_MARKET)
    public RestResult addShop(@RequestBody @Validated MarketShop req) {
        //xss 攻击
        Assert.isFalse((!isValidHtml(req.getDescription()) && req.getDescription().contains(xssTag)),WebUtil.getMessage(MsgCd.EXIST_XSS));
        return marketShopService.addShop(req);
    }




    /**
     * 租户端：查询详细信息
     *
     * @param shopId 商品id
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "GET", value = "查询商品详情")
    @GetMapping("/{shopId}")
    @AuthorizeMarket(action = AuthModule.CAI.CAI01.CAI0101)
    public RestResult queryDetails(@PathVariable("shopId") String shopId) {
        return marketShopService.selectShopDetailsById(shopId);
    }


    /**
     * 修改商品
     * 需要注意根据规格删除了多余的规格需要删除对应aiHub上的资产，并且已经使用的规格不能被删除
     * @param req 数据
     * @return
     */
    @ApiOperation(httpMethod = "POST", value = "商品修改")
    @PutMapping()
    @AuthorizeMarket(action = AuthModule.CAI.CAI01.CAI0103 + "," + AuthModule.BAI.BAI01.BAI0103,aiMarketFlag = true)
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'商品修改'", resource = OperationResourceEnum.AI_MARKET)
    public RestResult updateShop(@RequestBody @Validated MarketShop req) {
        //xss 攻击
        Assert.isFalse((!isValidHtml(req.getDescription()) && req.getDescription().contains(xssTag)),WebUtil.getMessage(MsgCd.EXIST_XSS));
        return marketShopService.updateShop(req);
    }



    /**
     * 租户端：我的商品
     * @param shopPageReq 分页查询商品
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "GET", value = "分页查询商品")
    @GetMapping("/mine")
    @AuthorizeMarket(action = AuthModule.CAI.CAI01.CAI0101)
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "'用户查看我的商品'", resource = OperationResourceEnum.AI_MARKET)
    public IPage<MarketShopMgtResp> searchProductsByPage(MarketShopMgtRequest shopPageReq) {
        // 用户只能查询自己的商品
        shopPageReq.setOwnerId(BasicInfoUtil.getAuthUser().getUserSid());
        return marketShopService.selectAllShops(shopPageReq);
    }




    /**
     * 修改商品状态
     * <p>
     * (接口功能复杂)
     * 1.商品上架
     * 2.商品下架
     * //3.管理员下架用户商品
     *
     * @param req 请求
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "Patch", value = "修改商品状态")
    @PutMapping("/status")
    @AuthorizeMarket(action = AuthModule.CAI.CAI01.CAI0103 + "," + AuthModule.BAI.BAI01.BAI0103,aiMarketFlag = true)
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'修改商品状态'", resource = OperationResourceEnum.AI_MARKET)
    public RestResult reviseShopStatus(@RequestBody @Validated ShopUpdateStatusReq req) {
        return marketShopService.updateShopStatus(req);
    }

    /**
     * 验证商品名称是否重复
     *
     * @param shopName  商品名称
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "Get", value = "验证商品名称是否重复")
    @GetMapping("/checkShopName")
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "'验证商品名称是否重复'", resource = OperationResourceEnum.AI_MARKET)
    public RestResult checkShopName(@RequestParam String shopName) {
        QueryWrapper<MarketShop> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("title",shopName);
        MarketShop one = this.marketShopService.getOne(queryWrapper);
        return new RestResult(ObjectUtil.isNotNull(one));
    }


    /**
     * 收藏数增加
     *
     * @param shopId  商品id
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "Get", value = "收藏数增加")
    @PostMapping("/{shopId}/star")
    @Transactional(rollbackFor = Exception.class)
    public RestResult addCollectNum(@PathVariable("shopId") String shopId) {
        User authUser = BasicInfoUtil.getAuthUser();
        Assert.notNull(authUser,"请先登录");
        marketShopCollectService.save(new MarketShopCollect(shopId,authUser.getUserSid()));
        marketShopService.addCollectNum(shopId);
        return RestResult.newSuccess();
    }

    /**
     * 取消收藏
     *
     * @param shopId  商品id
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "Get", value = "取消收藏")
    @DeleteMapping("/{shopId}/star")
    @Transactional(rollbackFor = Exception.class)
    public RestResult delCollectNum(@PathVariable("shopId") String shopId) {
        User authUser = BasicInfoUtil.getAuthUser();
        Assert.notNull(authUser,"请先登录");

        QueryWrapper<MarketShopCollect> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(MarketShopCollect::getShopId,shopId)
                .eq(MarketShopCollect::getUserId,authUser.getUserSid());
        marketShopCollectService.remove(queryWrapper);
        marketShopService.delCollectNum(shopId);
        return RestResult.newSuccess();
    }

    /**
     * 查询算法列表
     *
     * @param request 参数
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "Get", value = "查询算法列表")
    @GetMapping("/algorithms")
    public Page<AiAlgorithmsQueryResponse> selectAlgorithmsList(AlgorithmsRequest request){

        return marketShopService.selectAlgorithmsList(request);
    }

    /**
     * 查询算法列表
     *
     * @param request 参数
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "Get", value = "查询算法列表")
    @GetMapping("/algorithms/console")
    public Page<AiAlgorithmsQueryResponse> selectAlgorithmsListConsole(AlgorithmsRequest request){

        return marketShopService.selectAlgorithmsList(request);
    }

    /**
     * 根据许可证那边发布的类型来确定该节点可以创建哪些商品类型
     *
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "Get", value = "查询算法列表")
    @GetMapping("/getShopType")
    public RestResult getShopType(){

        return marketShopService.getShopType();
    }


/*********************************客户管理下的商品管理*********************************/

    /**
     * 管理员查询客户商品
     * @param shopReq 分页查询商品
     * @return {@link RestResult}
     *
     */
    @ApiOperation(httpMethod = "GET", value = "管理员查询客户商品")
    @AuthorizeMarket(action = BQ010813.BQ01081301)
    @GetMapping("/customer")
    public IPage<MarketCustomerShopResp> queryShopByPage(@Valid MarketCustomerShopRequest shopReq) {

        MarketShopMgtRequest req = BeanConvertUtil.convert(shopReq, MarketShopMgtRequest.class);
        IPage<MarketShopMgtResp> marketShopMgtRespIPage = marketShopService.selectAllShops(req);
        IPage<MarketCustomerShopResp> respIPage=new Page<>();
        respIPage.setPages(marketShopMgtRespIPage.getPages());
        respIPage.setCurrent(marketShopMgtRespIPage.getCurrent());
        respIPage.setSize(marketShopMgtRespIPage.getSize());
        respIPage.setTotal(marketShopMgtRespIPage.getTotal());
        respIPage.setRecords(BeanUtil.copyToList(marketShopMgtRespIPage.getRecords(),MarketCustomerShopResp.class));
        return respIPage;
    }

    /**
     * 管理端：查询商品详情
     *
     * @param shopId 商品id
     * @return {@link RestResult}
     *
     */
    @ApiOperation(httpMethod = "GET", value = "查询商品详情")
    @AuthorizeMarket(action = AuthModule.BAI.BAI01.BAI0101)
    @GetMapping("/detail")
    public RestResult<MarketShopDetailResp> queryAroundDetails(@NotBlank String shopId) {

        return marketShopService.selectShopDetailsById(shopId);
    }

    /**
     * 查询客户已结算和未结算金额（汇总信息）
     *
     * @param userSid 用户id
     * @return {@link RestResult}
     *
     */
    @ApiOperation(httpMethod = "GET", value = "查询客户已结算和未结算金额")
    @AuthorizeMarket(action = AuthModule.BAI.BAI01.BAI0101)
    @GetMapping("/information/{userSid}")
    public RestResult<MarketShopPriceDetailResp> queryAggregateInformation(@PathVariable("userSid") Long userSid) {

        return marketShopService.selectShopInformation(userSid);
    }

    /**
     * 管理员查询客户是否有模型供应商权限
     * @param userSid 用户id
     * @return {@link RestResult}
     *
     */
    @ApiOperation(httpMethod = "GET", value = "管理员查询客户是否有模型供应商权限")
    @AuthorizeMarket(action = BQ010813.BQ010813)
    @GetMapping("/permission/{userSid}")
    public RestResult<Boolean> queryPermission(@PathVariable("userSid") Long userSid) {

        return marketShopService.selectPermissionByUserSid(userSid);
    }

    /**
     * 管理员授予客户模型供应商权限
     * @param userSid 客户id
     * @return {@link RestResult}
     *
     */
    @ApiOperation(httpMethod = "POST", value = "管理员授予客户模型供应商权限")
    @AuthorizeMarket(action = BQ010813.BQ01081302)
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'客户模型供应商权限'", bizId = "#userSid", resource = OperationResourceEnum.AUTHORIZE_AI_MARKET_PERMISSION)
    @PostMapping("/authorize/{userSid}")
    public RestResult authorize(@PathVariable("userSid") @NotNull Long userSid) {

        return marketShopService.authorize(userSid);
    }

    /**
     * 管理员取消授权客户模型供应商权限
     * @param userSid 客户id
     * @return {@link RestResult}
     *
     */
    @ApiOperation(httpMethod = "PUT", value = "管理员取消授予客户模型供应商权限")
    @AuthorizeMarket(action = BQ010813.BQ01081303)
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'客户模型供应商权限'", bizId = "#userSid", resource = OperationResourceEnum.CANCEL_AUTHORIZE_AI_MARKET_PERMISSION)
    @PutMapping("/authorizeCancel/{userSid}")
    public RestResult authorizeCancel(@PathVariable("userSid") @NotNull Long userSid) {

        return marketShopService.authorizeCancel(userSid);
    }


/*********************************运营控制台下的商品管理*********************************/

    /**
     * 管理员查询商品
     * @param shopReq 分页查询商品
     * @return {@link RestResult}
     *
     */
    @ApiOperation(httpMethod = "GET", value = "管理员查询商品")
    @AuthorizeMarket(action = AuthModule.BAI.BAI01.BAI0101)
    @GetMapping("/list")
    public IPage<MarketShopMgtResp> queryShopByPage(@Valid MarketShopMgtRequest shopReq) {

        return marketShopService.selectAllShops(shopReq);
    }

    /**
     * 调整商品关联的标签
     *
     * @param req 数据
     * @return  状态
     *
     */
    @ApiOperation(httpMethod = "PUT", value = "调整商品关联的标签")
    @AuthorizeMarket(action = AuthModule.BAI.BAI01.BAI0103)
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'商品标签'", bizId = "#req.shopId", param = "#req", resource = OperationResourceEnum.REVISE_SHOP_LABELS)
    @PutMapping("/label")
    public RestResult updateShop(@RequestBody @Valid UpdateShopLabelRequest req) {

        return marketShopService.updateShopLabel(req);
    }

    /**
     * 调整商品的排序(上移)
     *
     * @param shopId 数据
     * @return  状态
     *
     */
    @ApiOperation(httpMethod = "PUT", value = "商品排序上移")
    @AuthorizeMarket(action = AuthModule.BAI.BAI01.BAI0103)
    @PutMapping("/moveUp/{shopId}")
    public RestResult moveUpShopSort(@PathVariable("shopId") String shopId) {

        return marketShopService.moveUp(shopId);
    }

    /**
     * 调整商品的排序(下移)
     *
     * @param shopId 数据
     * @return  状态
     *
     */
    @ApiOperation(httpMethod = "PUT", value = "商品排序下移")
    @AuthorizeMarket(action = AuthModule.BAI.BAI01.BAI0103)
    @PutMapping("/moveDown/{shopId}")
    public RestResult moveDownShopSort(@PathVariable("shopId") String shopId) {

        return marketShopService.moveDown(shopId);
    }

    /**
     * 调整商品的排序(置顶)
     *
     * @param shopId 数据
     * @return  状态
     *
     */
    @ApiOperation(httpMethod = "PUT", value = "商品排序置顶")
    @AuthorizeMarket(action = AuthModule.BAI.BAI01.BAI0103)
    @PutMapping("/moveTop/{shopId}")
    public RestResult moveTopShopSort(@PathVariable("shopId") String shopId) {

        return marketShopService.moveTop(shopId);
    }

    /**
     * 调整商品的排序(置底)
     *
     * @param shopId 数据
     * @return  状态
     *
     */
    @ApiOperation(httpMethod = "PUT", value = "商品排序置底")
    @AuthorizeMarket(action = AuthModule.BAI.BAI01.BAI0103)
    @PutMapping("/moveBottom/{shopId}")
    public RestResult moveBottomShopSort(@PathVariable("shopId") String shopId) {

        return marketShopService.moveBottom(shopId);
    }

    public static boolean isValidHtml(String htmlStr) {
        Safelist safelist = Safelist.basicWithImages().addAttributes(":all", "title", "src", "href", "id", "class", "style", "width", "height", "alt", "target", "align"
                , "a", "img", "br", "strong", "b", "pre", "p", "div", "em", "span", "h1", "h2", "h3", "h4", "h5", "h6", "table", "ul", "ol", "tr", "th", "td", "hr", "li", "u", "dd", "dl", "dt", "em", "i", "q", "small", "strike", "sup", "sub");
        return Jsoup.isValid(htmlStr, safelist);


    }
}
