package cn.com.cloudstar.rightcloud.web.controller;

import cn.com.cloudstar.rightcloud.common.constants.AuthModule;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.data.request.market.MarketShopSkuAddReq;
import cn.com.cloudstar.rightcloud.data.request.market.MarketShopSkuNextReq;
import cn.com.cloudstar.rightcloud.data.request.market.MarketShopSkuUpdReq;
import cn.com.cloudstar.rightcloud.data.request.market.MarketSkuPageReq;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.data.response.market.ShopSkuResp;
import cn.com.cloudstar.rightcloud.operationlog.annotation.OperationLog;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationTypeEnum;
import cn.com.cloudstar.rightcloud.service.shop.MarketShopSkuService;
import cn.com.cloudstar.rightcloud.web.annotation.AuthorizeMarket;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;


/**
 * 市场sku控制器
 *
 * <AUTHOR>
 * @date 2023/08/01
 */
@Api(tags = "属性管理")
@Slf4j
@RestController
@RequestMapping("/sku")
public class MarketSkuController {

    @Resource
    private MarketShopSkuService marketShopSkuService;


    @AuthorizeMarket(action = AuthModule.CAI.CAI01.CAI0102 + "," + AuthModule.BAI.BAI01.BAI0102, aiMarketFlag = true)
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "'创建sku属性'", resource = OperationResourceEnum.CREATE_SKU_ATTRIBUTE)
    @ApiOperation(httpMethod = "POST", value = "创建sku属性")
    @PostMapping("/add")
    public RestResult addSku(@RequestBody @Validated MarketShopSkuAddReq req) {
        marketShopSkuService.addSku(req);
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_INSERT_SUCCESS));
    }

    @AuthorizeMarket(action = AuthModule.CAI.CAI01.CAI0102 + "," + AuthModule.BAI.BAI01.BAI0102, aiMarketFlag = true)
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'更新sku属性'", resource = OperationResourceEnum.UPD_SKU_ATTRIBUTE)
    @ApiOperation(httpMethod = "POST", value = "更新sku属性")
    @PutMapping("/upd")
    public RestResult updSku(@RequestBody @Validated MarketShopSkuUpdReq req) {
        marketShopSkuService.upd(req);
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_UPDATE_SUCCESS));
    }

    @AuthorizeMarket(action = AuthModule.CAI.CAI01.CAI0102 + "," + AuthModule.BAI.BAI01.BAI0102, aiMarketFlag = true)
    @OperationLog(type = OperationTypeEnum.DELETE, tagName = "'删除sku属性'", resource = OperationResourceEnum.DEL_SKU_ATTRIBUTE)
    @ApiOperation(httpMethod = "POST", value = "删除sku属性")
    @DeleteMapping("/delSku/{skuId}")
    public RestResult delShop(@PathVariable Long skuId) {
        return marketShopSkuService.delSku(skuId);
    }

    @AuthorizeMarket(action = AuthModule.CAI.CAI01.CAI0102 + "," + AuthModule.BAI.BAI01.BAI0102, aiMarketFlag = true)
    @ApiOperation(httpMethod = "GET", value = "查询sku属性")
    @GetMapping("/page")
    public Page<ShopSkuResp> page(MarketSkuPageReq req) {
        return marketShopSkuService.selectPage(req);
    }

    @AuthorizeMarket(action = AuthModule.CAI.CAI01.CAI0102 + "," + AuthModule.BAI.BAI01.BAI0102, aiMarketFlag = true)
    @OperationLog(type = OperationTypeEnum.DELETE, tagName = "'下一步'", resource = OperationResourceEnum.NEXT_SKU)
    @ApiOperation(httpMethod = "POST", value = "下一步")
    @PutMapping("/next")
    public RestResult next(@RequestBody @Validated List<MarketShopSkuNextReq> req)  {
        return marketShopSkuService.next(req);
    }


}
