/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.web.aop;

import cn.com.cloudstar.rightcloud.common.constants.AuthConstants;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.BC.BC05;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.BN.BN01;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.BN.BN02;
import cn.com.cloudstar.rightcloud.common.constants.RestConst;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.exception.BizException;
import cn.com.cloudstar.rightcloud.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.data.dao.RoleModuleMapper;
import cn.com.cloudstar.rightcloud.data.dao.SysMUserMapper;
import cn.com.cloudstar.rightcloud.data.entity.RoleModule;
import cn.com.cloudstar.rightcloud.data.entity.SysMUser;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.service.feign.BssServiceFeign;
import cn.com.cloudstar.rightcloud.web.annotation.AuthorizeMarket;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.ClassUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/5/7.
 */
@Aspect
@Component
@Slf4j
public class AuthorizeMarketAspect {

    public static final String CONSOLE = "console";
    public static final String HEADER_TAG = "feign.route.tag";

    /**
     * 授权标识
     */
    private static final String AUTHORIZATION = "true";

    @Autowired
    private RoleModuleMapper roleModuleMapper;

    @Autowired()
    @Lazy
    private StringRedisTemplate redisTemplate;

    @Autowired
    private SysMUserMapper sysMUserMapper;

    @Autowired
    private BssServiceFeign bssServiceFeign;

    @Pointcut("@annotation(cn.com.cloudstar.rightcloud.web.annotation.AuthorizeMarket)")
    public void pointCut() {

    }

    @Around(value = "pointCut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        // feign调用忽略权限判断
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        String requestType = request.getHeader("RequstType");
        if (HEADER_TAG.equals(requestType)) {
            return joinPoint.proceed();
        }

        Method targetMethod = findTargetMethod(joinPoint);
        AuthorizeMarket authorize = AnnotationUtils.findAnnotation(targetMethod, AuthorizeMarket.class);
        List<RoleModule> roleModules = new ArrayList<>();
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (authUser == null) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }

        //若是租户端且鉴权标识上需要鉴权，则检验aiMarket相关接口是否授权
        if(authorize.aiMarketFlag() && CONSOLE.equals(authUser.getRemark())){
            SysMUser sysMUser = sysMUserMapper.selectById(authUser.getUserSid());
            if(!AUTHORIZATION.equals(sysMUser.getAuthorizeTag())){
                throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
            }
        }

        //是不是租户 进行接口鉴权
        if (RequestContextUtil.whetherItIsATenant(authUser.getUserSid())) {
            Object data = Optional.ofNullable(bssServiceFeign.roleAuth(authUser.getUserSid(), request.getRequestURI(), request.getMethod()))
                    .orElse(new RestResult<>()).getData();
            log.info("接口鉴权结果:{}", data);
            if (Boolean.TRUE.equals(data)) {
                return joinPoint.proceed();
            } else {
                throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
            }
        }

        String[] moduleSids = null;
        String key = AuthConstants.USER_PERMISSION_CACHE + authUser.getUserSid();
        //优先从缓存里取,权限一有改动，则在权限改动的接口删除缓存，把最新的权限加进缓存，方便resource去取（resource跨库）
        String cacheObj = redisTemplate.opsForValue().get(key);
        List<String> cacheAuths = JSON.parseObject(cacheObj,List.class);
        if (Objects.nonNull(authorize)) {

            // 用户控制台用户信息访问运营控制台接口白名单校验
            if (CONSOLE.equals(authUser.getRemark())) {

                // 获取uri并进行标准化处理
                String requestUri = StringUtil.getUri(request);
                List<String> strings = whiteList();
                for (String s : strings) {
                    if (requestUri.equals(s)) {
                        return joinPoint.proceed();
                    }
                }
            }
            // 获取权限
            String action = authorize.action();
            action = getModuleSid(joinPoint, action);
            moduleSids = action.split(",");
            // 判断是否是系统管理员，不是则不能访问系统控制台的菜单
            if (authUser.getAdminFlag() && Arrays.stream(moduleSids).anyMatch(t -> t.startsWith("Z") || t.equals("B109"))) {
                return joinPoint.proceed();
            }


            // 从数据库获取该用户拥有的权限
            roleModules = roleModuleMapper.selectRoleModuleByUserSid(authUser.getUserSid());
            // 判断是否有权限
            if (CollectionUtil.isEmpty(roleModules) && CollectionUtil.isEmpty(cacheAuths)) {
                throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
            }
           /* if (ObjectUtils.isEmpty(authUser.getParentSid())) {
                cacheAuths = roleModules.stream().map(RoleModule::getModuleSid).distinct().collect(Collectors.toList());
                redisTemplate.opsForValue().set(key, JSON.toJSONString(cacheAuths));
            }*/
            cacheAuths = roleModules.stream().map(RoleModule::getModuleSid).distinct().collect(Collectors.toList());
            redisTemplate.opsForValue().set(key, JSON.toJSONString(cacheAuths));
        }

        final List<String> authList = cacheAuths;

        if (Arrays.stream(Objects.requireNonNull(moduleSids))
                  .anyMatch(authList::contains)) {
            return joinPoint.proceed();
        }
        throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
    }

    private String getModuleSid(ProceedingJoinPoint joinPoint, String action) {
        Object[] args = joinPoint.getArgs();
        // 工单详情
        if (BN01.BN0102.equals(action)) {
            if ("0".equals(args[2])) {
                // 如果是工单详情，则给详情权限，如果是分配回显，则分配权限
                action = BN01.BN0102;
            } else if ("1".equals(args[2])) {
                action = BN01.BN0101;
            } else {
                action = BN02.BN0202;
            }
        }
        // 上传文件权限分化
        if ("".equals(action)){
            switch ((String) args[0]) {
                case "rest":
                case "lettertemplate":
                case "favicon":
                case "login":
                case "logo":
                    action = AuthModule.ZF.ZF0101;
                    break;
                case "contract":
                    action = BC05.BC05_COMMMON;
                    break;
                case "id_card":
                case "auth":
                    action = AuthModule.COMMON.PUBLIC.C1.C104;
                    break;
                case "ticket":
                    action = AuthModule.BN.BN + "," + AuthModule.CD.CD;
                    break;
                case "files":
                case "idap":
                    action = AuthModule.CB.CB1901 + "," + BC05.BC0511 + "," + AuthModule.BD.BD05.BD05;
                    break;
                case "distributor":
                    action = BC05.BC0511+ "," + AuthModule.CB.CB1901;
                    break;
                default:
                    break;
            }
        }
        return action;
    }

    private Method findTargetMethod(ProceedingJoinPoint joinPoint) throws NoSuchMethodException {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Class[] parameterTypes = signature.getParameterTypes();
        return ClassUtils.getUserClass(joinPoint.getTarget())
                         .getMethod(joinPoint.getSignature().getName(), parameterTypes);
    }

    // 用户控制台用户信息访问运营控制台接口白名单
    public List<String> whiteList() {
        List<String> whiteList = new ArrayList<>();
        Collections.addAll(whiteList, "/api/v1/bss/message/read",
                "/api/v1/oss/message/read",
                "/api/v1/bss/access/subuser",
                "/api/v1/oss/users/password/reset",
                "/api/v1/oss/users/resetPwd/send_email",
                "/api/v1/bss/access/user_group",
                "/api/v1/bss/access/projects",
                "/api/v1/bss/access/password_policy",
                "/api/v1/bss/access/password_config",
                "/api/v1/oss/audit/approval/export",
                "/api/v1/bss/account_deals",
                "/api/v1/bss/account_deals/export",
                "/api/v1/bss/download/list",
                "/api/v1/bss/download/file",
                "/api/v1/oss/process/record",
                "/api/v1/bss/invoice",
                "/api/v1/bss/invoice/setting",
                "/api/v1/bss/contract",
                "/api/v1/bss/coupons/accounts",
                "/api/v1/oss/message/infos",
                "/api/v1/bss/products/resources",
                "/api/v1/bss/products",
                "/api/v1/bss/bills/listCycleBills",
                "/api/v1/bss/bills",
                "/api/v1/bss/cashCoupon/account",
                "/api/v1/bss/message/infos",
                "/api/v1/bss/message",
                "/api/v1/oss/message",
                "/api/v1/bss/bills/asynExportBillDetails",
                "/api/v1/oss/users/getAuthUserInfoById",
                "/api/v1/oss/audit/approval/order",
                "/api/v1/oss/users/file");
        return whiteList;
    }
}
