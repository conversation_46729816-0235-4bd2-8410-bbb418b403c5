package cn.com.cloudstar.rightcloud.web.controller;

import cn.com.cloudstar.rightcloud.adapter.core.MQException;
import cn.com.cloudstar.rightcloud.adapter.core.MQHelper;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.*;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.pojo.*;
import cn.com.cloudstar.rightcloud.basic.data.pojo.base.Base;
import cn.com.cloudstar.rightcloud.common.pojo.RestResult;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/test")
@Slf4j
public class TestController {


    @NotNull
    private static Object sendMessage(Object base) throws MQException {
        Object rpc = null;
        rpc = base;
        System.out.println(rpc.toString());
        return rpc;
    }

    private static void extracted(Base base) {
        base.setApiKey("WFMGVZ9J3US5XKOZWUAE");
        base.setSecureToken("20u2QysnHuwSPIeNeHrJW63nRdiW08dI9phcxoNd");
        base.setRegion("cn-east-325");
        base.setProviderUrl("https://iam.cn-east-325.aicc.ssc.net.cn/v3");
        base.setVirtEnvType("hcso");
        base.setVirtEnvUuid("dev");
        base.setProviderType("HCSO");
    }

    @GetMapping("/aishopcreate")
    public static RestResult deliver() throws MQException {
        List<AiShopCreate> createList = new ArrayList<>();
//        AiShopCreate base = new AiShopCreate();
//        extracted(base);
//        base.setType("algo");
//        base.setVisibility("private");
//        base.setSell_type(0);
//        base.setTitle("aip创建");
//        base.setDescription("0");
//        SkuInfo skuInfo = new SkuInfo();
//        NameRes nameRes = new NameRes();
//        nameRes.setValue_en_us("Free package usage");
//        nameRes.setValue_zh_cn("免费使用套餐包");
//        skuInfo.setResource_number(1);
//        SkuAttribute skuAttribute = new SkuAttribute();
//        skuAttribute.setLinear_value(99);
//        skuAttribute.setMeasure_unit(3);
//        NameRes me = new NameRes();
//        me.setValue_en_us("Year");
//        me.setValue_zh_cn("年");
//        skuAttribute.setMeasure_unit_name_res(me);
//        skuInfo.setSku_attribute(skuAttribute);
//        skuInfo.setName_res(nameRes);
//        base.setSku_info(skuInfo);
//        createList.add(base);
//        AiShopCreateTo aiShopCreateTo = new AiShopCreateTo(createList);
//        extracted(aiShopCreateTo);
//        sendMessage(MQHelper.rpc(aiShopCreateTo)); 实锤可以发送成功


        // 根据价格规格添加多个商品
        createList.clear();
        AiShopCreate create = new AiShopCreate();
        extracted(create);
        create.setSell_type(1);
        SkuInfo skuInfo2 = new SkuInfo();
        skuInfo2.setName_res(NameRes.getFree());
        skuInfo2.setSku_attribute(new SkuAttribute(11, 11, NameRes.getYear()));
        skuInfo2.setPurchase_limit(new PurchaseLimit(1, 1));
        create.setSku_info(skuInfo2);
        createList.add(create);
        AiShopCreateTo aiShopCreateTo1 = new AiShopCreateTo(createList);
        extracted(aiShopCreateTo1);
        sendMessage(MQHelper.rpc(aiShopCreateTo1));
        return new RestResult();
    }

    public static void main(String[] args) throws MQException {
        RestResult deliver = deliver();
    }


    @GetMapping("/aishopqueryall")
    public RestResult AiShopQueryAll() throws MQException {
        AiShopQueryAll base = new AiShopQueryAll();
        extracted(base);
        base.setContent_type("algo");


        return new RestResult(sendMessage(MQHelper.rpc(base)));
    }

    @GetMapping("/aishopdetailsquery")
    public RestResult AiShopDetailsQuery() throws MQException {
        AiShopDetailsQuery base = new AiShopDetailsQuery();
        extracted(base);
        base.setContent_id("924362ed-e532-408b-b692-8750c65a790a");

        return new RestResult(sendMessage(MQHelper.rpc(base)));
    }

    @GetMapping("/AiShopSubscriptionsCreate")
    public RestResult AiShopSubscriptionsCreate() throws MQException {
        AiShopSubscriptionsCreate base = new AiShopSubscriptionsCreate();
        extracted(base);
        base.setContent_id("7c125947-ba14-4113-894c-0913ecd2a1f6");

        return new RestResult(sendMessage(MQHelper.rpc(base)));
    }

    @GetMapping("/AiShopDelete")
    public RestResult AiShopDelete() throws MQException {
        AiShopDelete base = new AiShopDelete();
        extracted(base);
        base.setContent_id("1dd00220-c2be-4590-8b3a-e6ed84d01ed0");

        return new RestResult(sendMessage(MQHelper.rpc(base)));
    }

    @GetMapping("/AiShopLicenseCreate")
    public RestResult AiShopLicenseCreate() throws MQException {
        AiShopLicenseCreate base = new AiShopLicenseCreate();
        extracted(base);
        base.setContent_id("5a9ac4cd-16d8-4a08-ad6a-9d4913b3253f");

        return new RestResult(sendMessage(MQHelper.rpc(base)));
    }

    @GetMapping("/AiShopLifecycleUpdate")
    public RestResult AiShopLifecycleUpdate() throws MQException {
        AiShopLifecycleUpdate base = new AiShopLifecycleUpdate();
        extracted(base);
        base.setContent_id("6f6012d5-a583-40e6-b4f2-616b2fbc6fd6");
        base.setLifecycle(0);
        //TODO 404

        return new RestResult(sendMessage(MQHelper.rpc(base)));
    }

    @GetMapping("/AiShopProcess")
    public RestResult AiShopProcess() throws MQException {
        AiShopProcess base = new AiShopProcess();
        extracted(base);
        base.setContent_id("5a9ac4cd-16d8-4a08-ad6a-9d4913b3253f");
        base.setStatus(1);
        base.setMessage("审核信息");
        //TODO 待删除

        return new RestResult(sendMessage(MQHelper.rpc(base)));
    }

    @GetMapping("/AiShopRenew")
    public RestResult AiShopRenew() throws MQException {
        AiShopRenew base = new AiShopRenew();
        extracted(base);
        base.setContent_id("5a9ac4cd-16d8-4a08-ad6a-9d4913b3253f");
        //TODO 待删除

        return new RestResult(sendMessage(MQHelper.rpc(base)));
    }

    @GetMapping("/AiShopSubscribesQuery")
    public RestResult AiShopSubscribesQuery() throws MQException {
        AiShopSubscribesQuery base = new AiShopSubscribesQuery();
        extracted(base);
        base.setContent_types("");


        return new RestResult(sendMessage(MQHelper.rpc(base)));
    }

    @GetMapping("/AiShopSubscriptionsDelete")
    public RestResult AiShopSubscriptionsDelete() throws MQException {
        AiShopSubscriptionsDelete base = new AiShopSubscriptionsDelete();
        extracted(base);
        base.setSubscription_id("87b4e2bd-06f0-4d72-b4b1-dc42d60da242");


        return new RestResult(sendMessage(MQHelper.rpc(base)));
    }

    @GetMapping("/AiShopSubscriptionsRenew")
    public RestResult AiShopSubscriptionsRenew() throws MQException {
        AiShopSubscriptionsRenew base = new AiShopSubscriptionsRenew();
        extracted(base);
        base.setSubscription_id("87b4e2bd-06f0-4d72-b4b1-dc42d60da242");


        return new RestResult(sendMessage(MQHelper.rpc(base)));
    }


    @GetMapping("/AiShopUpdate")
    public RestResult AiShopUpdate() throws MQException {
        AiShopUpdate base = new AiShopUpdate();
        extracted(base);
        base.setContent_id("6f6012d5-a583-40e6-b4f2-616b2fbc6fd6");
        base.setDescription("6f6012d5-a583-40e6-b4f2-616b2fbc6fd6");


        return new RestResult(sendMessage(MQHelper.rpc(base)));
    }

    @GetMapping("/AiShopVersionCreate")
    public RestResult AiShopVersionCreate() throws MQException {
        AiShopVersionCreate base = new AiShopVersionCreate();
        extracted(base);
        base.setContent_id("5a9ac4cd-16d8-4a08-ad6a-9d4913b3253f");
        base.setProjectId("32756a1c4b514173908d54fdf03f71b4");
        VersionAlgorithm versionAlgorithm = new VersionAlgorithm("a98fbf85-36dd-4d12-9779-e30664320da6");
        base.setAlgorithm(versionAlgorithm);
        VersionContentInfo versionContentInfo = new VersionContentInfo("1.0.1", "api创建");
        base.setContent_info(versionContentInfo);


        return new RestResult(sendMessage(MQHelper.rpc(base)));
    }

    @GetMapping("/AiShopVersionDelete")
    public RestResult AiShopVersionDelete() throws MQException {
        AiShopVersionDelete base = new AiShopVersionDelete();
        extracted(base);
        base.setContent_id("6f6012d5-a583-40e6-b4f2-616b2fbc6fd6");
        base.setVersion_id("1.0.4");
        //TODO 404


        return new RestResult(sendMessage(MQHelper.rpc(base)));
    }

    @GetMapping("/AiShopVersionsQuery")
    public RestResult AiShopVersionsQuery() throws MQException {
        AiShopVersionsQuery base = new AiShopVersionsQuery();
        extracted(base);
        base.setSubscription_id("6f6012d5-a583-40e6-b4f2-616b2fbc6fd6");


        return new RestResult(sendMessage(MQHelper.rpc(base)));
    }

    @GetMapping("/AiShopVersionUpdate")
    public RestResult AiShopVersionUpdate() throws MQException {
        AiShopVersionUpdate base = new AiShopVersionUpdate();
        extracted(base);
        base.setContent_id("6f6012d5-a583-40e6-b4f2-616b2fbc6fd6");
        base.setVersion_id("1.0.4");
//        base.setStatus("0");


        return new RestResult(sendMessage(MQHelper.rpc(base)));
    }

    @GetMapping("/AiAlgorithmsQuery")
    public RestResult AiAlgorithmsQuery(AiAlgorithmsQuery base) throws MQException {

        extracted(base);
        base.setProjectId("32756a1c4b514173908d54fdf03f71b4");
        return new RestResult(sendMessage(MQHelper.rpc(base)));
    }



    private static List<String> getStrings() {
        List<String> aiShopActions = new ArrayList<>();
        aiShopActions.add("AiShopCreate");
        aiShopActions.add("AiShopCreateTo");
        aiShopActions.add("AiShopDelete");
        aiShopActions.add("AiShopDeleteTo");
        aiShopActions.add("AiShopDetailsQuery");
        aiShopActions.add("AiShopLicenseCreate");
        aiShopActions.add("AiShopLifecycleUpdate");
        aiShopActions.add("AiShopMineQueryAll");
        aiShopActions.add("AiShopProcess");
        aiShopActions.add("AiShopQueryAll");
        aiShopActions.add("AiShopRenew");
        aiShopActions.add("AiShopSubscribesQuery");
        aiShopActions.add("AiShopSubscriptionsCreate");
        aiShopActions.add("AiShopSubscriptionsDelete");
        aiShopActions.add("AiShopSubscriptionsRenew");
        aiShopActions.add("AiShopUpdate");
        aiShopActions.add("AiShopVersionCreate");
        aiShopActions.add("AiShopVersionDelete");
        aiShopActions.add("AiShopVersionsQuery");
        aiShopActions.add("AiShopVersionUpdate");
        return aiShopActions;
    }

}
