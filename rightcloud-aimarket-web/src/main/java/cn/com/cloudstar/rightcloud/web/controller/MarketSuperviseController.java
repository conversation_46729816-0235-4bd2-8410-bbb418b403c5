package cn.com.cloudstar.rightcloud.web.controller;

import cn.com.cloudstar.rightcloud.common.constants.AuthModule;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.data.request.market.MarketAcceptanceReq;
import cn.com.cloudstar.rightcloud.data.request.market.MarketDeliverReq;
import cn.com.cloudstar.rightcloud.module.support.access.constants.EnumValue;
import cn.com.cloudstar.rightcloud.operationlog.annotation.OperationLog;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationTypeEnum;
import cn.com.cloudstar.rightcloud.service.shop.MarketShopSuperviseRecordService;
import cn.com.cloudstar.rightcloud.web.annotation.AuthorizeMarket;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


/**
 * 资金监管控制器
 *
 * <AUTHOR>
 * @date 2023/08/03
 */
@Api(tags = "资金监管管理")
@Slf4j
@RestController
@RequestMapping("/supervise")
public class MarketSuperviseController {

    @Resource
    private MarketShopSuperviseRecordService shopSuperviseRecordService;


    /**
     * 交付
     *
     * @param subscribeId 订阅id
     * @return {@link RestResult}
     */
    @AuthorizeMarket(action = AuthModule.CAI.CAI03.CAI0303, aiMarketFlag = true)
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'供应商发货验收'", resource = OperationResourceEnum.DELIVER)
    @ApiOperation(httpMethod = "PUT", value = "供应商发货验收")
    @PutMapping("/deliver/{subscribeId}")
    public RestResult deliver(@PathVariable String subscribeId,
                              @RequestBody MarketDeliverReq req) {
        shopSuperviseRecordService.deliver(subscribeId, req);
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS));
    }

    /**
     * 验收
     *
     * @param subscribeId 订阅id
     * @param process     过程
     * @return {@link RestResult}
     */
    @AuthorizeMarket(action = AuthModule.CAI.CAI04.CAI0403)
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'购买方验收处理'", resource = OperationResourceEnum.ACCEPTANCE)
    @ApiOperation(httpMethod = "PUT", value = "购买方验收处理")
    @PutMapping("/acceptance/{subscribeId}/{process}")
    public RestResult acceptance(@PathVariable("subscribeId") String subscribeId,
                                 @PathVariable("process") @EnumValue(strValues = {"pass", "reject"}) String process,
                                 @RequestBody MarketAcceptanceReq req) {
        shopSuperviseRecordService.acceptance(subscribeId, process, req);
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS));
    }


    /**
     * 自营商品交付交付
     *
     * @param subscribeId 订阅id
     * @return {@link RestResult}
     */
    @AuthorizeMarket(action = AuthModule.BAI.BAI03.BAI0303)
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'自营供应商发货验收'", resource = OperationResourceEnum.DELIVER)
    @ApiOperation(httpMethod = "PUT", value = "自营供应商发货验收")
    @PutMapping("/manage/deliver/{subscribeId}")
    public RestResult mgtDeliver(@PathVariable String subscribeId,
                                 @RequestBody MarketDeliverReq req) {
        shopSuperviseRecordService.deliver(subscribeId, req);
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS));
    }

    /**
     * 结算
     *
     * @param subscribeId 订阅id
     * @return {@link RestResult}
     */
    @AuthorizeMarket(action = AuthModule.BAI.BAI03.BAI0305)
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'管理员结算'", resource = OperationResourceEnum.SETTLEMENT)
    @ApiOperation(httpMethod = "POST", value = "结算")
    @PutMapping("/settlement/{subscribeId}")
    public RestResult settlement(@PathVariable String subscribeId)  {
        shopSuperviseRecordService.settlement(subscribeId);
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS));
    }

}
