/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.web.annotation;

import java.lang.annotation.*;

/**
 * 基于接口的权限拦截
 *
 * <AUTHOR>
 * @since 2022/4/11 17:16:27
 */
@Target(ElementType.METHOD)
@Inherited
@Retention(RetentionPolicy.RUNTIME)
public @interface AuthorizeMarket {

    /**
     * 产品code:资源类型code:操作 </br>
     * 如ecs:servers:ListVms
     */
    String action() default "";

    /**
     * aiMarket相关接口鉴权标识 租户端使用需要加
     */
    boolean aiMarketFlag() default false;
}
