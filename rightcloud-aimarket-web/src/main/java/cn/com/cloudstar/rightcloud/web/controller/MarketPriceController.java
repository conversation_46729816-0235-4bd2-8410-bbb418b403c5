package cn.com.cloudstar.rightcloud.web.controller;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.common.constants.AuthModule;
import cn.com.cloudstar.rightcloud.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.data.request.market.MarketInquiryPriceBase;
import cn.com.cloudstar.rightcloud.data.response.market.MarketInquiryPriceResp;
import cn.com.cloudstar.rightcloud.operationlog.annotation.OperationLog;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationTypeEnum;
import cn.com.cloudstar.rightcloud.service.shop.MarketShopSkuPriceService;
import cn.com.cloudstar.rightcloud.web.annotation.AuthorizeMarket;
import cn.com.cloudstar.rightcloud.web.annotation.RejectCall;


/**
 * 市场询价控制器
 *
 * <AUTHOR>
 * @date 2023/08/01
 */
@Api(tags = "询价管理")
@Slf4j
@RestController
@RequestMapping("/inquiry")
public class MarketPriceController {

    @Resource
    private MarketShopSkuPriceService marketShopSkuPriceService;


    /**
     * 商品规格询价
     *
     * @param req 要求事情
     * @return {@link RestResult}
     */
    @AuthorizeMarket(action = AuthModule.CAI.CAI04.CAI0406)
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "'商品规格询价'", resource = OperationResourceEnum.SHOP_PRICE)
    @ApiOperation(httpMethod = "POST", value = "规格询价")
    @PostMapping("/price")
    public RestResult price(@RequestBody @Validated MarketInquiryPriceBase req) {
        MarketInquiryPriceResp resp = marketShopSkuPriceService.price(req);
        return new RestResult(resp);
    }

    @ApiOperation(httpMethod = "POST", value = "规格询价")
    @RejectCall
    @PostMapping("/price/feign")
    public RestResult priceFeign(@RequestBody @Validated MarketInquiryPriceBase req) {
        MarketInquiryPriceResp resp = marketShopSkuPriceService.price(req);
        return new RestResult(resp);
    }


}
