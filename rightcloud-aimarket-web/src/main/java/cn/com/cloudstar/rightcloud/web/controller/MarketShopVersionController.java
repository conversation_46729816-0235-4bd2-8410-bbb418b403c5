package cn.com.cloudstar.rightcloud.web.controller;

import cn.com.cloudstar.rightcloud.common.constants.AuthModule;
import cn.com.cloudstar.rightcloud.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.data.entity.MarketShopVersion;
import cn.com.cloudstar.rightcloud.data.request.market.MarketVersionBatchReq;
import cn.com.cloudstar.rightcloud.data.request.market.MarketVersionStatusReq;
import cn.com.cloudstar.rightcloud.operationlog.annotation.OperationLog;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationTypeEnum;
import cn.com.cloudstar.rightcloud.service.shop.MarketShopVersionService;
import cn.com.cloudstar.rightcloud.web.annotation.AuthorizeMarket;

import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 商品版本
 */
@Slf4j
@RestController
@RequestMapping("/version")
public class MarketShopVersionController {

    @Resource
    MarketShopVersionService versionService;

    @ApiOperation(httpMethod = "POST", value = "批量添加版本")
    @AuthorizeMarket(action = AuthModule.CAI.CAI01.CAI0104, aiMarketFlag = true)
    @PostMapping("/batchAdd/console")
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "'批量添加版本'",
            resource = OperationResourceEnum.CREATE_BATCH_VERSION, param = "#request")
    public RestResult batchAddConsole(@RequestBody @Validated MarketVersionBatchReq req) {

        return versionService.batchAdd(req);
    }

    @ApiOperation(httpMethod = "POST", value = "批量添加版本")
    @AuthorizeMarket(action = AuthModule.BAI.BAI01.BAI0104)
    @PostMapping("/batchAdd")
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "'批量添加版本'",
            resource = OperationResourceEnum.CREATE_BATCH_VERSION, param = "#request")
    public RestResult batchAdd(@RequestBody @Validated MarketVersionBatchReq req) {

        return versionService.batchAdd(req);
    }

    @ApiOperation(httpMethod = "POST", value = "重试批量添加版本")
    @AuthorizeMarket(action = AuthModule.CAI.CAI01.CAI0104, aiMarketFlag = true)
    @PostMapping("{shopId}/resetBatchAdd/console")
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "'重试批量添加版本'",
            resource = OperationResourceEnum.RESET_CREATE_BATCH_VERSION, param = "#request")
    public RestResult resetBatchAddConsole(@PathVariable String shopId) {

        return versionService.resetBatchAdd(shopId);
    }

    @ApiOperation(httpMethod = "POST", value = "重试批量添加版本")
    @AuthorizeMarket(action = AuthModule.BAI.BAI01.BAI0104)
    @PostMapping("{shopId}/resetBatchAdd")
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "'重试批量添加版本'",
            resource = OperationResourceEnum.RESET_CREATE_BATCH_VERSION, param = "#request")
    public RestResult resetBatchAdd(@PathVariable String shopId) {

        return versionService.resetBatchAdd(shopId);
    }

    /**
     * 新增一个商品的版本，需要把商品对应的所有资产都加版本
     * @param req
     * @return
     */
    @ApiOperation(httpMethod = "POST", value = "单独添加版本")
    @AuthorizeMarket(action = AuthModule.CAI.CAI01.CAI0104, aiMarketFlag = true)
    @PostMapping("/console")
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "'单独添加版本'",
            resource = OperationResourceEnum.CREATE_BATCH_VERSION, param = "#request")
    public RestResult addConsole(@RequestBody @Validated MarketShopVersion req) {

        return versionService.add(req);
    }

    /**
     * 新增一个商品的版本，需要把商品对应的所有资产都加版本
     * @param req
     * @return
     */
    @ApiOperation(httpMethod = "POST", value = "单独添加版本")
    @AuthorizeMarket(action = AuthModule.BAI.BAI01.BAI0104)
    @PostMapping("")
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "'单独添加版本'",
            resource = OperationResourceEnum.CREATE_BATCH_VERSION, param = "#request")
    public RestResult add(@RequestBody @Validated MarketShopVersion req) {

        return versionService.add(req);
    }


    @ApiOperation(httpMethod = "GET", value = "查看商品下的版本")
    @AuthorizeMarket(action = AuthModule.CAI.CAI01.CAI0105, aiMarketFlag = true)
    @GetMapping("/console/{shopId}")
    public RestResult getByShopIdConsole(@PathVariable("shopId") String shopId) {

        return new RestResult(versionService.getByShopId(shopId));
    }

    @ApiOperation(httpMethod = "GET", value = "查看商品下的版本")
    @AuthorizeMarket(action = AuthModule.BAI.BAI01.BAI0105)
    @GetMapping("/{shopId}")
    public RestResult getByShopId(@PathVariable("shopId") String shopId) {

        return new RestResult(versionService.getByShopId(shopId));
    }

    /**
     * 修改一个商品的版本，需要把商品对应的所有资产都需要修改
     * @param req
     * @return
     */
    @ApiOperation(httpMethod = "put", value = "修改版本")
    @AuthorizeMarket(action = AuthModule.CAI.CAI01.CAI0106, aiMarketFlag = true)
    @PutMapping("/console")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'修改版本状态",
            resource = OperationResourceEnum.UPDATE_VERSION, param = "#request")
    public RestResult updateVersionStatusConsole(@RequestBody @Validated MarketVersionStatusReq req) {

        return versionService.updateStatus(req);
    }

    /**
     * 修改一个商品的版本，需要把商品对应的所有资产都需要修改
     * @param req
     * @return
     */
    @ApiOperation(httpMethod = "put", value = "修改版本")
    @AuthorizeMarket(action = AuthModule.BAI.BAI01.BAI0106)
    @PutMapping("")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'修改版本状态",
            resource = OperationResourceEnum.UPDATE_VERSION, param = "#request")
    public RestResult updateVersionStatus(@RequestBody @Validated MarketVersionStatusReq req) {

        return versionService.updateStatus(req);
    }

}
