package cn.com.cloudstar.rightcloud.web.controller;

import cn.com.cloudstar.rightcloud.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.common.util.JedisUtil;
import cn.com.cloudstar.rightcloud.data.request.market.MarketInquiryPriceBase;
import cn.com.cloudstar.rightcloud.data.request.market.MarketShopAroundPortalRequest;
import cn.com.cloudstar.rightcloud.data.request.market.MarketShopPortalRequest;
import cn.com.cloudstar.rightcloud.data.request.market.MarketShopRankPortalRequest;
import cn.com.cloudstar.rightcloud.data.request.market.MarketShopSkuCacheReq;
import cn.com.cloudstar.rightcloud.data.request.market.MarketShopTagPortalRequest;
import cn.com.cloudstar.rightcloud.data.request.market.MarketShopViewPortalRequest;
import cn.com.cloudstar.rightcloud.data.response.market.MarketInquiryPriceResp;
import cn.com.cloudstar.rightcloud.data.response.market.MarketShopDetailPortalResp;
import cn.com.cloudstar.rightcloud.data.response.market.MarketShopPagePortalResp;
import cn.com.cloudstar.rightcloud.data.response.market.MarketShopRankPortalResp;
import cn.com.cloudstar.rightcloud.data.response.market.MarketShopTagPortalResp;
import cn.com.cloudstar.rightcloud.operationlog.annotation.OperationLog;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationTypeEnum;
import cn.com.cloudstar.rightcloud.service.shop.MarketShopService;
import cn.com.cloudstar.rightcloud.service.shop.MarketShopSkuPriceService;
import cn.com.cloudstar.rightcloud.service.shop.MarketShopTagService;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * @description: 门户商品列表
 * @author: zengxin
 * @date: 2023/8/14 10:10
 */
@Api(tags = "门户下的商品列表")
@Slf4j
@RestController
@Validated
@RequestMapping("/portal/shop")
public class MarketShopPortalController {

    @Resource
    private MarketShopService marketShopService;

    @Resource
    private MarketShopTagService marketShopTagService;

    @Resource
    private MarketShopSkuPriceService marketShopSkuPriceService;


    /**
     * 门户商品列表
     * @param shopReq 分页查询商品
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "GET", value = "门户商品列表")
    @GetMapping("")
    public RestResult<IPage<MarketShopPagePortalResp>> queryShopByPage(@Valid MarketShopPortalRequest shopReq) {

        return marketShopService.selectPortalShops(shopReq);
    }

    /**
     * 门户商品排行
     * @param shopReq 查询商品排行
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "GET", value = "门户商品排行")
    @GetMapping("/rank")
    public RestResult<List<MarketShopRankPortalResp>> queryShopByRank(@Valid MarketShopRankPortalRequest shopReq) {

        return marketShopService.selectPortalShopsByRank(shopReq);
    }

    /**
     * 查询商品详情(包含前一篇后一篇信息）
     *
     * @param request 商品id
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "GET", value = "查询商品详情")
    @GetMapping("/aroundDetail")
    public RestResult<MarketShopDetailPortalResp> queryAroundDetails(@Valid MarketShopAroundPortalRequest request) {

        return marketShopService.selectAroundDetailsById(request);
    }

    /**
     * 浏览数增加
     *
     * @param request  商品request
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "Post", value = "浏览数增加")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'商品浏览数'", resource = OperationResourceEnum.ADD_BROWSE_NUM,bizId = "#request.shopId")
    @PostMapping("/view")
    public RestResult addBrowseNum(@RequestBody @Valid MarketShopViewPortalRequest request) {
        marketShopService.addBrowseNum(request.getShopId());
        return RestResult.newSuccess();

    }

    /**
     * 查询商品标签
     *
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "GET", value = "查询商品标签")
    @GetMapping("/label")
    public RestResult<List<MarketShopTagPortalResp>> queryShopLabels(MarketShopTagPortalRequest request) {

        return marketShopTagService.queryShopLabels(request);
    }

    /**
     * 规格询价
     *
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "POST", value = "规格询价")
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "'规格询价'", resource = OperationResourceEnum.INQUIRY_PRICE,bizId = "#req.shopId")
    @PostMapping("/price")
    public RestResult price(@RequestBody @Valid MarketInquiryPriceBase req) {
        MarketInquiryPriceResp resp = marketShopSkuPriceService.price(req);
        return new RestResult(resp);
    }

    /**
     * 存储商品临时信息
     */
    @ApiOperation(httpMethod = "POST", value = "存储商品临时信息")
    @PostMapping("/sku/cache")
    public RestResult<String> temp(@RequestBody @Valid MarketShopSkuCacheReq request) {
        //获取缓存key
        String shopSkuKey = request.getShopSkuKey();
        //获取缓存
        String skuInfoCache = JedisUtil.INSTANCE.get(shopSkuKey);
        String requestSkuInfo = request.getSkuInfo();
        if (StrUtil.isNotBlank(skuInfoCache) || StrUtil.isBlank(requestSkuInfo)) {
            return new RestResult<>(skuInfoCache);
        }
        JedisUtil.INSTANCE.set(shopSkuKey, requestSkuInfo, 60);
        return new RestResult<>(skuInfoCache);
    }
}
