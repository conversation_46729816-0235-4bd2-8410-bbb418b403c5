package cn.com.cloudstar.rightcloud.web.annotation;

import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;

import java.lang.annotation.*;

/**
 * @desc 权限鉴别-数据权限
 * <AUTHOR>
 * @date 2022/12/27 -3:04 下午
 **/
@Target(ElementType.METHOD)
@Inherited
@Retention(RetentionPolicy.RUNTIME)
public @interface DataPermission {

    /**
     * 操作的资源类类型
     */
    OperationResourceEnum resource();

    String bizId() default "";

    String param() default "";
}
