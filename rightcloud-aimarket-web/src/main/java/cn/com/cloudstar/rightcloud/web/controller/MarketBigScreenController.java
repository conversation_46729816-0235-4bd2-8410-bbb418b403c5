package cn.com.cloudstar.rightcloud.web.controller;

import cn.com.cloudstar.rightcloud.common.constants.AuthModule;
import cn.com.cloudstar.rightcloud.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.data.response.bigscreen.MarketBigScreenResp;
import cn.com.cloudstar.rightcloud.data.response.bigscreen.MarketSubscribeProviderTopResp;
import cn.com.cloudstar.rightcloud.data.response.bigscreen.MarketSubscribeRecentResp;
import cn.com.cloudstar.rightcloud.data.response.bigscreen.MarketSubscribeShopTopResp;
import cn.com.cloudstar.rightcloud.data.response.bigscreen.MarketSubscribeUserTopResp;
import cn.com.cloudstar.rightcloud.data.response.bigscreen.MarketSuperviseTrendResp;
import cn.com.cloudstar.rightcloud.data.response.bigscreen.ProductDeliveryStatisticsResp;
import cn.com.cloudstar.rightcloud.data.response.bigscreen.ProductTagStatisticsResp;
import cn.com.cloudstar.rightcloud.data.response.bigscreen.UserOrderRankingResp;
import cn.com.cloudstar.rightcloud.module.support.access.constants.EnumValue;
import cn.com.cloudstar.rightcloud.service.discount.SysMUserService;
import cn.com.cloudstar.rightcloud.service.shop.MarketShopService;
import cn.com.cloudstar.rightcloud.service.shop.MarketShopTagService;
import cn.com.cloudstar.rightcloud.service.shop.MarketSubscribeService;
import cn.com.cloudstar.rightcloud.web.annotation.AuthorizeMarket;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @description: AI集市大屏
 * @author: zengxin
 * @date: 2023/9/6 15:26
 */
@Api(tags = "AI集市大屏")
@Slf4j
@RestController
@Validated
@RequestMapping("/bigScreen")
public class MarketBigScreenController {

    @Resource
    private SysMUserService sysMUserService;

    @Resource
    private MarketShopService marketShopService;

    @Resource
    private MarketShopTagService marketShopTagService;

    @Resource
    private MarketSubscribeService marketSubscribeService;

    /**
     * 查询注册供应商个数
     *
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "GET", value = "查询注册供应商个数")
    @GetMapping("/user/num")
    @AuthorizeMarket(action = AuthModule.BIG_SCREEN.PC)
    public RestResult countProviderUserNum() {

        int sum = sysMUserService.countProviderUserNum();
        return new RestResult(sum);
    }

    /**
     * 查询上架商品个数
     *
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "GET", value = "查询上架商品个数")
    @GetMapping("/shopOnline/num")
    @AuthorizeMarket(action = AuthModule.BIG_SCREEN.PC)
    public RestResult countShopOnlineNum() {

        int sum = marketShopService.countShopOnlineNum();
        return new RestResult(sum);
    }

    /**
     * 查询资源种类数量（标签个数）
     *
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "GET", value = "查询资源种类数量")
    @GetMapping("/label/num")
    @AuthorizeMarket(action = AuthModule.BIG_SCREEN.PC)
    public RestResult countLabelNum() {

        int sum = marketShopTagService.countLabelNum();
        return new RestResult(sum);
    }

    /**
     * 查询成交订单个数和金额（金额单位为万元）
     *
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "GET", value = "查询成交订单个数和金额")
    @GetMapping("/subscribe/num")
    @AuthorizeMarket(action = AuthModule.BIG_SCREEN.PC)
    public RestResult<MarketBigScreenResp> countSubscribeNum() {

        return marketSubscribeService.countSubscribeNum();
    }

    /**
     * 查询最新成交动态
     * @param top 前top条排行
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "GET", value = "查询最新成交动态")
    @GetMapping("/subscribe/recent")
    @AuthorizeMarket(action = AuthModule.BIG_SCREEN.PC)
    public RestResult<List<MarketSubscribeRecentResp>> selectSubscribeRecent(Long top) {
        top = Objects.isNull(top) ? 10L : top;
        return marketSubscribeService.selectSubscribeRecent(top);
    }

    /**
     * 查询商品成交排行
     *
     * @param timeType    时间类型（按周按月按年）
     * @param top         前top条排行
     * @param ContentType 内容类型 （visit_count访问量、transaction_count交易量）
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "GET", value = "查询商品成交排行")
    @GetMapping("/subscribe/shop")
    @AuthorizeMarket(action = AuthModule.BIG_SCREEN.PC)
    public RestResult<List<MarketSubscribeShopTopResp>> selectSubscribeShopTop(@EnumValue(strValues = {"year", "month", "week"}) String timeType,
                                                                               Long top,
                                                                               @EnumValue(strValues = {"visit_count", "transaction_count"}) String ContentType) {
        top = Objects.isNull(top) ? 10L : top;
        return marketSubscribeService.selectSubscribeShopTop(timeType, top, ContentType);
    }

    /**
     * 查询供应商成交订单排行
     * @param timeType 时间类型（按周按月按年）
     * @param top 前top条排行
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "GET", value = "查询供应商成交订单排行")
    @GetMapping("/subscribe/provider")
    @AuthorizeMarket(action = AuthModule.BIG_SCREEN.PC)
    public RestResult<List<MarketSubscribeProviderTopResp>> selectSubscribeProviderTop(@EnumValue(strValues = {"year","month","week"}) String timeType, Long top) {
        top = Objects.isNull(top) ? 10L : top;
        return marketSubscribeService.selectSubscribeProviderTop(timeType, top);
    }

    /**
     * 查询用户成交订单金额排行
     * @param timeType 时间类型（按周按月按年）
     * @param top 前top条排行
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "GET", value = "查询用户成交订单金额排行")
    @GetMapping("/subscribe/user")
    @AuthorizeMarket(action = AuthModule.BIG_SCREEN.PC)
    public RestResult<List<MarketSubscribeUserTopResp>> selectSubscribeUserTop(@EnumValue(strValues = {"year","month","week"}) String timeType, Long top) {

        top = Objects.isNull(top) ? 10L : top;
        return marketSubscribeService.selectSubscribeUserTop(timeType, top);
    }

    /**
     * 查询资金监管服务趋势
     *
     * @param timeType    时间类型（按周按月按年）
     * @param ContentType 内容类型 （amount金额、number订单数）
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "GET", value = "查询资金监管服务趋势")
    @GetMapping("/subscribe/supervise_trend")
    @AuthorizeMarket(action = AuthModule.BIG_SCREEN.PC)
    public RestResult<List<MarketSuperviseTrendResp>> selectSuperviseTrend(@EnumValue(strValues = {"daily", "weekly", "monthly"}) String timeType,
                                                                           @EnumValue(strValues = {"amount", "number"}) String ContentType) {

        return marketSubscribeService.selectSuperviseTrend(timeType, ContentType);
    }


    /**
     * 获取注册用户个数
     *
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "GET", value = "获取注册用户个数")
    @GetMapping("/user/registered/count")
    @AuthorizeMarket(action = AuthModule.BIG_SCREEN.PC)
    public RestResult<Integer> getRegisteredUserCount() {
        return new RestResult<>(sysMUserService.getRegisteredUserCount());
    }

    /**
     * 查询用户成交金额订单排行
     *
     * @param timeType 时间类型（按周按月按年）
     * @param top      前top条排行
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "GET", value = "查询用户成交金额订单排行")
    @GetMapping("/order/user/ranking")
    @AuthorizeMarket(action = AuthModule.BIG_SCREEN.PC)
    public RestResult<List<UserOrderRankingResp>> getUserOrderRanking(@EnumValue(strValues = {"year", "month", "week"}) String timeType, Long top) {
        top = Objects.isNull(top) ? 10L : top;
        return new RestResult<>(marketSubscribeService.getUserOrderRanking(timeType, top));
    }


    /**
     * 查询商品交付类型统计
     *
     * @param contentType 内容类型  (product_count商品数量、transaction_amount交易金额、subscription_count订阅数量)
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "GET", value = "查询商品交付类型统计")
    @GetMapping("/product/delivery/type/statistics")
    @AuthorizeMarket(action = AuthModule.BIG_SCREEN.PC)
    public RestResult<List<ProductDeliveryStatisticsResp>> getProductDeliveryStatistics(@EnumValue(strValues = {"product_count",
            "transaction_amount", "subscription_count"}) String contentType) {
        return new RestResult<>(marketSubscribeService.getProductDeliveryStatistics(contentType));
    }


    /**
     * 查询商品标签统计
     *
     * @param contentType 内容类型  (product_count商品数量、transaction_amount交易金额、subscription_count订阅数量)
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "GET", value = "查询商品标签统计")
    @GetMapping("/product/tag/statistics")
    @AuthorizeMarket(action = AuthModule.BIG_SCREEN.PC)
    public RestResult<List<ProductTagStatisticsResp>> getProductTagStatistics(@EnumValue(strValues = {"product_count",
            "transaction_amount", "subscription_count"}) String contentType) {
        return new RestResult<>(marketSubscribeService.getProductTagStatistics(contentType));
    }


}
