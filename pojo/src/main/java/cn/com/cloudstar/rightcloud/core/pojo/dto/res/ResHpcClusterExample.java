/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.core.pojo.dto.res;

import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ResHpcClusterExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public ResHpcClusterExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1110291907));
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_647205) + property + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1079430193));
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_20398661) + property + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1079430193));
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("res_hpc_cluster.id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("res_hpc_cluster.id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("res_hpc_cluster.id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("res_hpc_cluster.id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("res_hpc_cluster.id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("res_hpc_cluster.id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("res_hpc_cluster.id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("res_hpc_cluster.id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("res_hpc_cluster.id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("res_hpc_cluster.id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("res_hpc_cluster.id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("res_hpc_cluster.id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andResourceIdIsNull() {
            addCriterion("res_hpc_cluster.resource_id is null");
            return (Criteria) this;
        }

        public Criteria andResourceIdIsNotNull() {
            addCriterion("res_hpc_cluster.resource_id is not null");
            return (Criteria) this;
        }

        public Criteria andResourceIdEqualTo(String value) {
            addCriterion("res_hpc_cluster.resource_id =", value, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdNotEqualTo(String value) {
            addCriterion("res_hpc_cluster.resource_id <>", value, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdGreaterThan(String value) {
            addCriterion("res_hpc_cluster.resource_id >", value, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdGreaterThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.resource_id >=", value, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdLessThan(String value) {
            addCriterion("res_hpc_cluster.resource_id <", value, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdLessThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.resource_id <=", value, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdLike(String value) {
            addCriterion("res_hpc_cluster.resource_id like", value, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdNotLike(String value) {
            addCriterion("res_hpc_cluster.resource_id not like", value, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdIn(List<String> values) {
            addCriterion("res_hpc_cluster.resource_id in", values, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdNotIn(List<String> values) {
            addCriterion("res_hpc_cluster.resource_id not in", values, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.resource_id between", value1, value2, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdNotBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.resource_id not between", value1, value2, "resourceId");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("res_hpc_cluster.`name` is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("res_hpc_cluster.`name` is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("res_hpc_cluster.`name` =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("res_hpc_cluster.`name` <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("res_hpc_cluster.`name` >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.`name` >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("res_hpc_cluster.`name` <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.`name` <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("res_hpc_cluster.`name` like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("res_hpc_cluster.`name` not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("res_hpc_cluster.`name` in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("res_hpc_cluster.`name` not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.`name` between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.`name` not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNull() {
            addCriterion("res_hpc_cluster.description is null");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNotNull() {
            addCriterion("res_hpc_cluster.description is not null");
            return (Criteria) this;
        }

        public Criteria andDescriptionEqualTo(String value) {
            addCriterion("res_hpc_cluster.description =", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotEqualTo(String value) {
            addCriterion("res_hpc_cluster.description <>", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThan(String value) {
            addCriterion("res_hpc_cluster.description >", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.description >=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThan(String value) {
            addCriterion("res_hpc_cluster.description <", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.description <=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLike(String value) {
            addCriterion("res_hpc_cluster.description like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotLike(String value) {
            addCriterion("res_hpc_cluster.description not like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionIn(List<String> values) {
            addCriterion("res_hpc_cluster.description in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotIn(List<String> values) {
            addCriterion("res_hpc_cluster.description not in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.description between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.description not between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andBusinessCategoryIsNull() {
            addCriterion("res_hpc_cluster.business_category is null");
            return (Criteria) this;
        }

        public Criteria andBusinessCategoryIsNotNull() {
            addCriterion("res_hpc_cluster.business_category is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessCategoryEqualTo(String value) {
            addCriterion("res_hpc_cluster.business_category =", value, "businessCategory");
            return (Criteria) this;
        }

        public Criteria andBusinessCategoryNotEqualTo(String value) {
            addCriterion("res_hpc_cluster.business_category <>", value, "businessCategory");
            return (Criteria) this;
        }

        public Criteria andBusinessCategoryGreaterThan(String value) {
            addCriterion("res_hpc_cluster.business_category >", value, "businessCategory");
            return (Criteria) this;
        }

        public Criteria andBusinessCategoryGreaterThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.business_category >=", value, "businessCategory");
            return (Criteria) this;
        }

        public Criteria andBusinessCategoryLessThan(String value) {
            addCriterion("res_hpc_cluster.business_category <", value, "businessCategory");
            return (Criteria) this;
        }

        public Criteria andBusinessCategoryLessThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.business_category <=", value, "businessCategory");
            return (Criteria) this;
        }

        public Criteria andBusinessCategoryLike(String value) {
            addCriterion("res_hpc_cluster.business_category like", value, "businessCategory");
            return (Criteria) this;
        }

        public Criteria andBusinessCategoryNotLike(String value) {
            addCriterion("res_hpc_cluster.business_category not like", value, "businessCategory");
            return (Criteria) this;
        }

        public Criteria andBusinessCategoryIn(List<String> values) {
            addCriterion("res_hpc_cluster.business_category in", values, "businessCategory");
            return (Criteria) this;
        }

        public Criteria andBusinessCategoryNotIn(List<String> values) {
            addCriterion("res_hpc_cluster.business_category not in", values, "businessCategory");
            return (Criteria) this;
        }

        public Criteria andBusinessCategoryBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.business_category between", value1, value2, "businessCategory");
            return (Criteria) this;
        }

        public Criteria andBusinessCategoryNotBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.business_category not between", value1, value2, "businessCategory");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNull() {
            addCriterion("res_hpc_cluster.tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("res_hpc_cluster.tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(Long value) {
            addCriterion("res_hpc_cluster.tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(Long value) {
            addCriterion("res_hpc_cluster.tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(Long value) {
            addCriterion("res_hpc_cluster.tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(Long value) {
            addCriterion("res_hpc_cluster.tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(Long value) {
            addCriterion("res_hpc_cluster.tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(Long value) {
            addCriterion("res_hpc_cluster.tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<Long> values) {
            addCriterion("res_hpc_cluster.tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<Long> values) {
            addCriterion("res_hpc_cluster.tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(Long value1, Long value2) {
            addCriterion("res_hpc_cluster.tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(Long value1, Long value2) {
            addCriterion("res_hpc_cluster.tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andCloudEnvIdIsNull() {
            addCriterion("res_hpc_cluster.cloud_env_id is null");
            return (Criteria) this;
        }

        public Criteria andCloudEnvIdIsNotNull() {
            addCriterion("res_hpc_cluster.cloud_env_id is not null");
            return (Criteria) this;
        }

        public Criteria andCloudEnvIdEqualTo(Long value) {
            addCriterion("res_hpc_cluster.cloud_env_id =", value, "cloudEnvId");
            return (Criteria) this;
        }

        public Criteria andCloudEnvIdNotEqualTo(Long value) {
            addCriterion("res_hpc_cluster.cloud_env_id <>", value, "cloudEnvId");
            return (Criteria) this;
        }

        public Criteria andCloudEnvIdGreaterThan(Long value) {
            addCriterion("res_hpc_cluster.cloud_env_id >", value, "cloudEnvId");
            return (Criteria) this;
        }

        public Criteria andCloudEnvIdGreaterThanOrEqualTo(Long value) {
            addCriterion("res_hpc_cluster.cloud_env_id >=", value, "cloudEnvId");
            return (Criteria) this;
        }

        public Criteria andCloudEnvIdLessThan(Long value) {
            addCriterion("res_hpc_cluster.cloud_env_id <", value, "cloudEnvId");
            return (Criteria) this;
        }

        public Criteria andCloudEnvIdLessThanOrEqualTo(Long value) {
            addCriterion("res_hpc_cluster.cloud_env_id <=", value, "cloudEnvId");
            return (Criteria) this;
        }

        public Criteria andCloudEnvIdIn(List<Long> values) {
            addCriterion("res_hpc_cluster.cloud_env_id in", values, "cloudEnvId");
            return (Criteria) this;
        }

        public Criteria andCloudEnvIdNotIn(List<Long> values) {
            addCriterion("res_hpc_cluster.cloud_env_id not in", values, "cloudEnvId");
            return (Criteria) this;
        }

        public Criteria andCloudEnvIdBetween(Long value1, Long value2) {
            addCriterion("res_hpc_cluster.cloud_env_id between", value1, value2, "cloudEnvId");
            return (Criteria) this;
        }

        public Criteria andCloudEnvIdNotBetween(Long value1, Long value2) {
            addCriterion("res_hpc_cluster.cloud_env_id not between", value1, value2, "cloudEnvId");
            return (Criteria) this;
        }

        public Criteria andChargeTypeIsNull() {
            addCriterion("res_hpc_cluster.charge_type is null");
            return (Criteria) this;
        }

        public Criteria andChargeTypeIsNotNull() {
            addCriterion("res_hpc_cluster.charge_type is not null");
            return (Criteria) this;
        }

        public Criteria andChargeTypeEqualTo(String value) {
            addCriterion("res_hpc_cluster.charge_type =", value, "chargeType");
            return (Criteria) this;
        }

        public Criteria andChargeTypeNotEqualTo(String value) {
            addCriterion("res_hpc_cluster.charge_type <>", value, "chargeType");
            return (Criteria) this;
        }

        public Criteria andChargeTypeGreaterThan(String value) {
            addCriterion("res_hpc_cluster.charge_type >", value, "chargeType");
            return (Criteria) this;
        }

        public Criteria andChargeTypeGreaterThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.charge_type >=", value, "chargeType");
            return (Criteria) this;
        }

        public Criteria andChargeTypeLessThan(String value) {
            addCriterion("res_hpc_cluster.charge_type <", value, "chargeType");
            return (Criteria) this;
        }

        public Criteria andChargeTypeLessThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.charge_type <=", value, "chargeType");
            return (Criteria) this;
        }

        public Criteria andChargeTypeLike(String value) {
            addCriterion("res_hpc_cluster.charge_type like", value, "chargeType");
            return (Criteria) this;
        }

        public Criteria andChargeTypeNotLike(String value) {
            addCriterion("res_hpc_cluster.charge_type not like", value, "chargeType");
            return (Criteria) this;
        }

        public Criteria andChargeTypeIn(List<String> values) {
            addCriterion("res_hpc_cluster.charge_type in", values, "chargeType");
            return (Criteria) this;
        }

        public Criteria andChargeTypeNotIn(List<String> values) {
            addCriterion("res_hpc_cluster.charge_type not in", values, "chargeType");
            return (Criteria) this;
        }

        public Criteria andChargeTypeBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.charge_type between", value1, value2, "chargeType");
            return (Criteria) this;
        }

        public Criteria andChargeTypeNotBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.charge_type not between", value1, value2, "chargeType");
            return (Criteria) this;
        }

        public Criteria andClusterTypeIsNull() {
            addCriterion("res_hpc_cluster.cluster_type is null");
            return (Criteria) this;
        }

        public Criteria andClusterTypeIsNotNull() {
            addCriterion("res_hpc_cluster.cluster_type is not null");
            return (Criteria) this;
        }

        public Criteria andClusterTypeEqualTo(String value) {
            addCriterion("res_hpc_cluster.cluster_type =", value, "clusterType");
            return (Criteria) this;
        }

        public Criteria andClusterTypeNotEqualTo(String value) {
            addCriterion("res_hpc_cluster.cluster_type <>", value, "clusterType");
            return (Criteria) this;
        }

        public Criteria andClusterTypeGreaterThan(String value) {
            addCriterion("res_hpc_cluster.cluster_type >", value, "clusterType");
            return (Criteria) this;
        }

        public Criteria andClusterTypeGreaterThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.cluster_type >=", value, "clusterType");
            return (Criteria) this;
        }

        public Criteria andClusterTypeLessThan(String value) {
            addCriterion("res_hpc_cluster.cluster_type <", value, "clusterType");
            return (Criteria) this;
        }

        public Criteria andClusterTypeLessThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.cluster_type <=", value, "clusterType");
            return (Criteria) this;
        }

        public Criteria andClusterTypeLike(String value) {
            addCriterion("res_hpc_cluster.cluster_type like", value, "clusterType");
            return (Criteria) this;
        }

        public Criteria andClusterTypeNotLike(String value) {
            addCriterion("res_hpc_cluster.cluster_type not like", value, "clusterType");
            return (Criteria) this;
        }

        public Criteria andClusterTypeIn(List<String> values) {
            addCriterion("res_hpc_cluster.cluster_type in", values, "clusterType");
            return (Criteria) this;
        }

        public Criteria andClusterTypeNotIn(List<String> values) {
            addCriterion("res_hpc_cluster.cluster_type not in", values, "clusterType");
            return (Criteria) this;
        }

        public Criteria andClusterTypeBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.cluster_type between", value1, value2, "clusterType");
            return (Criteria) this;
        }

        public Criteria andClusterTypeNotBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.cluster_type not between", value1, value2, "clusterType");
            return (Criteria) this;
        }

        public Criteria andProessPhaseIsNull() {
            addCriterion("res_hpc_cluster.proess_phase is null");
            return (Criteria) this;
        }

        public Criteria andProessPhaseIsNotNull() {
            addCriterion("res_hpc_cluster.proess_phase is not null");
            return (Criteria) this;
        }

        public Criteria andProessPhaseEqualTo(String value) {
            addCriterion("res_hpc_cluster.proess_phase =", value, "proessPhase");
            return (Criteria) this;
        }

        public Criteria andProessPhaseNotEqualTo(String value) {
            addCriterion("res_hpc_cluster.proess_phase <>", value, "proessPhase");
            return (Criteria) this;
        }

        public Criteria andProessPhaseGreaterThan(String value) {
            addCriterion("res_hpc_cluster.proess_phase >", value, "proessPhase");
            return (Criteria) this;
        }

        public Criteria andProessPhaseGreaterThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.proess_phase >=", value, "proessPhase");
            return (Criteria) this;
        }

        public Criteria andProessPhaseLessThan(String value) {
            addCriterion("res_hpc_cluster.proess_phase <", value, "proessPhase");
            return (Criteria) this;
        }

        public Criteria andProessPhaseLessThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.proess_phase <=", value, "proessPhase");
            return (Criteria) this;
        }

        public Criteria andProessPhaseLike(String value) {
            addCriterion("res_hpc_cluster.proess_phase like", value, "proessPhase");
            return (Criteria) this;
        }

        public Criteria andProessPhaseNotLike(String value) {
            addCriterion("res_hpc_cluster.proess_phase not like", value, "proessPhase");
            return (Criteria) this;
        }

        public Criteria andProessPhaseIn(List<String> values) {
            addCriterion("res_hpc_cluster.proess_phase in", values, "proessPhase");
            return (Criteria) this;
        }

        public Criteria andProessPhaseNotIn(List<String> values) {
            addCriterion("res_hpc_cluster.proess_phase not in", values, "proessPhase");
            return (Criteria) this;
        }

        public Criteria andProessPhaseBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.proess_phase between", value1, value2, "proessPhase");
            return (Criteria) this;
        }

        public Criteria andProessPhaseNotBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.proess_phase not between", value1, value2, "proessPhase");
            return (Criteria) this;
        }

        public Criteria andProessStatusIsNull() {
            addCriterion("res_hpc_cluster.proess_status is null");
            return (Criteria) this;
        }

        public Criteria andProessStatusIsNotNull() {
            addCriterion("res_hpc_cluster.proess_status is not null");
            return (Criteria) this;
        }

        public Criteria andProessStatusEqualTo(String value) {
            addCriterion("res_hpc_cluster.proess_status =", value, "proessStatus");
            return (Criteria) this;
        }

        public Criteria andProessStatusNotEqualTo(String value) {
            addCriterion("res_hpc_cluster.proess_status <>", value, "proessStatus");
            return (Criteria) this;
        }

        public Criteria andProessStatusGreaterThan(String value) {
            addCriterion("res_hpc_cluster.proess_status >", value, "proessStatus");
            return (Criteria) this;
        }

        public Criteria andProessStatusGreaterThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.proess_status >=", value, "proessStatus");
            return (Criteria) this;
        }

        public Criteria andProessStatusLessThan(String value) {
            addCriterion("res_hpc_cluster.proess_status <", value, "proessStatus");
            return (Criteria) this;
        }

        public Criteria andProessStatusLessThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.proess_status <=", value, "proessStatus");
            return (Criteria) this;
        }

        public Criteria andProessStatusLike(String value) {
            addCriterion("res_hpc_cluster.proess_status like", value, "proessStatus");
            return (Criteria) this;
        }

        public Criteria andProessStatusNotLike(String value) {
            addCriterion("res_hpc_cluster.proess_status not like", value, "proessStatus");
            return (Criteria) this;
        }

        public Criteria andProessStatusIn(List<String> values) {
            addCriterion("res_hpc_cluster.proess_status in", values, "proessStatus");
            return (Criteria) this;
        }

        public Criteria andProessStatusNotIn(List<String> values) {
            addCriterion("res_hpc_cluster.proess_status not in", values, "proessStatus");
            return (Criteria) this;
        }

        public Criteria andProessStatusBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.proess_status between", value1, value2, "proessStatus");
            return (Criteria) this;
        }

        public Criteria andProessStatusNotBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.proess_status not between", value1, value2, "proessStatus");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("res_hpc_cluster.`status` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("res_hpc_cluster.`status` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("res_hpc_cluster.`status` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("res_hpc_cluster.`status` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("res_hpc_cluster.`status` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.`status` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("res_hpc_cluster.`status` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.`status` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("res_hpc_cluster.`status` like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("res_hpc_cluster.`status` not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("res_hpc_cluster.`status` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andHpcVersion(Long value) {
            addCriterion("res_hpc_cluster.hpc_version =", value, "hpcVersion");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("res_hpc_cluster.`status` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.`status` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.`status` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andScenarioIsNull() {
            addCriterion("res_hpc_cluster.scenario is null");
            return (Criteria) this;
        }

        public Criteria andScenarioIsNotNull() {
            addCriterion("res_hpc_cluster.scenario is not null");
            return (Criteria) this;
        }

        public Criteria andScenarioEqualTo(String value) {
            addCriterion("res_hpc_cluster.scenario =", value, "scenario");
            return (Criteria) this;
        }

        public Criteria andScenarioNotEqualTo(String value) {
            addCriterion("res_hpc_cluster.scenario <>", value, "scenario");
            return (Criteria) this;
        }

        public Criteria andScenarioGreaterThan(String value) {
            addCriterion("res_hpc_cluster.scenario >", value, "scenario");
            return (Criteria) this;
        }

        public Criteria andScenarioGreaterThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.scenario >=", value, "scenario");
            return (Criteria) this;
        }

        public Criteria andScenarioLessThan(String value) {
            addCriterion("res_hpc_cluster.scenario <", value, "scenario");
            return (Criteria) this;
        }

        public Criteria andScenarioLessThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.scenario <=", value, "scenario");
            return (Criteria) this;
        }

        public Criteria andScenarioLike(String value) {
            addCriterion("res_hpc_cluster.scenario like", value, "scenario");
            return (Criteria) this;
        }

        public Criteria andScenarioNotLike(String value) {
            addCriterion("res_hpc_cluster.scenario not like", value, "scenario");
            return (Criteria) this;
        }

        public Criteria andScenarioIn(List<String> values) {
            addCriterion("res_hpc_cluster.scenario in", values, "scenario");
            return (Criteria) this;
        }

        public Criteria andScenarioNotIn(List<String> values) {
            addCriterion("res_hpc_cluster.scenario not in", values, "scenario");
            return (Criteria) this;
        }

        public Criteria andScenarioBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.scenario between", value1, value2, "scenario");
            return (Criteria) this;
        }

        public Criteria andScenarioNotBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.scenario not between", value1, value2, "scenario");
            return (Criteria) this;
        }

        public Criteria andVpcPeeringInfoIsNull() {
            addCriterion("res_hpc_cluster.vpc_peering_info is null");
            return (Criteria) this;
        }

        public Criteria andVpcPeeringInfoIsNotNull() {
            addCriterion("res_hpc_cluster.vpc_peering_info is not null");
            return (Criteria) this;
        }

        public Criteria andVpcPeeringInfoEqualTo(String value) {
            addCriterion("res_hpc_cluster.vpc_peering_info =", value, "vpcPeeringInfo");
            return (Criteria) this;
        }

        public Criteria andVpcPeeringInfoNotEqualTo(String value) {
            addCriterion("res_hpc_cluster.vpc_peering_info <>", value, "vpcPeeringInfo");
            return (Criteria) this;
        }

        public Criteria andVpcPeeringInfoGreaterThan(String value) {
            addCriterion("res_hpc_cluster.vpc_peering_info >", value, "vpcPeeringInfo");
            return (Criteria) this;
        }

        public Criteria andVpcPeeringInfoGreaterThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.vpc_peering_info >=", value, "vpcPeeringInfo");
            return (Criteria) this;
        }

        public Criteria andVpcPeeringInfoLessThan(String value) {
            addCriterion("res_hpc_cluster.vpc_peering_info <", value, "vpcPeeringInfo");
            return (Criteria) this;
        }

        public Criteria andVpcPeeringInfoLessThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.vpc_peering_info <=", value, "vpcPeeringInfo");
            return (Criteria) this;
        }

        public Criteria andVpcPeeringInfoLike(String value) {
            addCriterion("res_hpc_cluster.vpc_peering_info like", value, "vpcPeeringInfo");
            return (Criteria) this;
        }

        public Criteria andVpcPeeringInfoNotLike(String value) {
            addCriterion("res_hpc_cluster.vpc_peering_info not like", value, "vpcPeeringInfo");
            return (Criteria) this;
        }

        public Criteria andVpcPeeringInfoIn(List<String> values) {
            addCriterion("res_hpc_cluster.vpc_peering_info in", values, "vpcPeeringInfo");
            return (Criteria) this;
        }

        public Criteria andVpcPeeringInfoNotIn(List<String> values) {
            addCriterion("res_hpc_cluster.vpc_peering_info not in", values, "vpcPeeringInfo");
            return (Criteria) this;
        }

        public Criteria andVpcPeeringInfoBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.vpc_peering_info between", value1, value2, "vpcPeeringInfo");
            return (Criteria) this;
        }

        public Criteria andVpcPeeringInfoNotBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.vpc_peering_info not between", value1, value2, "vpcPeeringInfo");
            return (Criteria) this;
        }

        public Criteria andAdminUserIsNull() {
            addCriterion("res_hpc_cluster.admin_user is null");
            return (Criteria) this;
        }

        public Criteria andAdminUserIsNotNull() {
            addCriterion("res_hpc_cluster.admin_user is not null");
            return (Criteria) this;
        }

        public Criteria andAdminUserEqualTo(String value) {
            addCriterion("res_hpc_cluster.admin_user =", value, "adminUser");
            return (Criteria) this;
        }

        public Criteria andAdminUserNotEqualTo(String value) {
            addCriterion("res_hpc_cluster.admin_user <>", value, "adminUser");
            return (Criteria) this;
        }

        public Criteria andAdminUserGreaterThan(String value) {
            addCriterion("res_hpc_cluster.admin_user >", value, "adminUser");
            return (Criteria) this;
        }

        public Criteria andAdminUserGreaterThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.admin_user >=", value, "adminUser");
            return (Criteria) this;
        }

        public Criteria andAdminUserLessThan(String value) {
            addCriterion("res_hpc_cluster.admin_user <", value, "adminUser");
            return (Criteria) this;
        }

        public Criteria andAdminUserLessThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.admin_user <=", value, "adminUser");
            return (Criteria) this;
        }

        public Criteria andAdminUserLike(String value) {
            addCriterion("res_hpc_cluster.admin_user like", value, "adminUser");
            return (Criteria) this;
        }

        public Criteria andAdminUserNotLike(String value) {
            addCriterion("res_hpc_cluster.admin_user not like", value, "adminUser");
            return (Criteria) this;
        }

        public Criteria andAdminUserIn(List<String> values) {
            addCriterion("res_hpc_cluster.admin_user in", values, "adminUser");
            return (Criteria) this;
        }

        public Criteria andAdminUserNotIn(List<String> values) {
            addCriterion("res_hpc_cluster.admin_user not in", values, "adminUser");
            return (Criteria) this;
        }

        public Criteria andAdminUserBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.admin_user between", value1, value2, "adminUser");
            return (Criteria) this;
        }

        public Criteria andAdminUserNotBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.admin_user not between", value1, value2, "adminUser");
            return (Criteria) this;
        }

        public Criteria andAdminPasswordIsNull() {
            addCriterion("res_hpc_cluster.admin_password is null");
            return (Criteria) this;
        }

        public Criteria andAdminPasswordIsNotNull() {
            addCriterion("res_hpc_cluster.admin_password is not null");
            return (Criteria) this;
        }

        public Criteria andAdminPasswordEqualTo(String value) {
            addCriterion("res_hpc_cluster.admin_password =", value, "adminPassword");
            return (Criteria) this;
        }

        public Criteria andAdminPasswordNotEqualTo(String value) {
            addCriterion("res_hpc_cluster.admin_password <>", value, "adminPassword");
            return (Criteria) this;
        }

        public Criteria andAdminPasswordGreaterThan(String value) {
            addCriterion("res_hpc_cluster.admin_password >", value, "adminPassword");
            return (Criteria) this;
        }

        public Criteria andAdminPasswordGreaterThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.admin_password >=", value, "adminPassword");
            return (Criteria) this;
        }

        public Criteria andAdminPasswordLessThan(String value) {
            addCriterion("res_hpc_cluster.admin_password <", value, "adminPassword");
            return (Criteria) this;
        }

        public Criteria andAdminPasswordLessThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.admin_password <=", value, "adminPassword");
            return (Criteria) this;
        }

        public Criteria andAdminPasswordLike(String value) {
            addCriterion("res_hpc_cluster.admin_password like", value, "adminPassword");
            return (Criteria) this;
        }

        public Criteria andAdminPasswordNotLike(String value) {
            addCriterion("res_hpc_cluster.admin_password not like", value, "adminPassword");
            return (Criteria) this;
        }

        public Criteria andAdminPasswordIn(List<String> values) {
            addCriterion("res_hpc_cluster.admin_password in", values, "adminPassword");
            return (Criteria) this;
        }

        public Criteria andAdminPasswordNotIn(List<String> values) {
            addCriterion("res_hpc_cluster.admin_password not in", values, "adminPassword");
            return (Criteria) this;
        }

        public Criteria andAdminPasswordBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.admin_password between", value1, value2, "adminPassword");
            return (Criteria) this;
        }

        public Criteria andAdminPasswordNotBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.admin_password not between", value1, value2, "adminPassword");
            return (Criteria) this;
        }

        public Criteria andCcpExternalAddressIsNull() {
            addCriterion("res_hpc_cluster.ccp_external_address is null");
            return (Criteria) this;
        }

        public Criteria andCcpExternalAddressIsNotNull() {
            addCriterion("res_hpc_cluster.ccp_external_address is not null");
            return (Criteria) this;
        }

        public Criteria andCcpExternalAddressEqualTo(String value) {
            addCriterion("res_hpc_cluster.ccp_external_address =", value, "ccpExternalAddress");
            return (Criteria) this;
        }

        public Criteria andCcpExternalAddressNotEqualTo(String value) {
            addCriterion("res_hpc_cluster.ccp_external_address <>", value, "ccpExternalAddress");
            return (Criteria) this;
        }

        public Criteria andCcpExternalAddressGreaterThan(String value) {
            addCriterion("res_hpc_cluster.ccp_external_address >", value, "ccpExternalAddress");
            return (Criteria) this;
        }

        public Criteria andCcpExternalAddressGreaterThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.ccp_external_address >=", value, "ccpExternalAddress");
            return (Criteria) this;
        }

        public Criteria andCcpExternalAddressLessThan(String value) {
            addCriterion("res_hpc_cluster.ccp_external_address <", value, "ccpExternalAddress");
            return (Criteria) this;
        }

        public Criteria andCcpExternalAddressLessThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.ccp_external_address <=", value, "ccpExternalAddress");
            return (Criteria) this;
        }

        public Criteria andCcpExternalAddressLike(String value) {
            addCriterion("res_hpc_cluster.ccp_external_address like", value, "ccpExternalAddress");
            return (Criteria) this;
        }

        public Criteria andCcpExternalAddressNotLike(String value) {
            addCriterion("res_hpc_cluster.ccp_external_address not like", value, "ccpExternalAddress");
            return (Criteria) this;
        }

        public Criteria andCcpExternalAddressIn(List<String> values) {
            addCriterion("res_hpc_cluster.ccp_external_address in", values, "ccpExternalAddress");
            return (Criteria) this;
        }

        public Criteria andCcpExternalAddressNotIn(List<String> values) {
            addCriterion("res_hpc_cluster.ccp_external_address not in", values, "ccpExternalAddress");
            return (Criteria) this;
        }

        public Criteria andCcpExternalAddressBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.ccp_external_address between", value1, value2, "ccpExternalAddress");
            return (Criteria) this;
        }

        public Criteria andCcpExternalAddressNotBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.ccp_external_address not between", value1, value2, "ccpExternalAddress");
            return (Criteria) this;
        }

        public Criteria andCcpInternelAddressIsNull() {
            addCriterion("res_hpc_cluster.ccp_internel_address is null");
            return (Criteria) this;
        }

        public Criteria andCcpInternelAddressIsNotNull() {
            addCriterion("res_hpc_cluster.ccp_internel_address is not null");
            return (Criteria) this;
        }

        public Criteria andCcpInternelAddressEqualTo(String value) {
            addCriterion("res_hpc_cluster.ccp_internel_address =", value, "ccpInternelAddress");
            return (Criteria) this;
        }

        public Criteria andCcpInternelAddressNotEqualTo(String value) {
            addCriterion("res_hpc_cluster.ccp_internel_address <>", value, "ccpInternelAddress");
            return (Criteria) this;
        }

        public Criteria andCcpInternelAddressGreaterThan(String value) {
            addCriterion("res_hpc_cluster.ccp_internel_address >", value, "ccpInternelAddress");
            return (Criteria) this;
        }

        public Criteria andCcpInternelAddressGreaterThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.ccp_internel_address >=", value, "ccpInternelAddress");
            return (Criteria) this;
        }

        public Criteria andCcpInternelAddressLessThan(String value) {
            addCriterion("res_hpc_cluster.ccp_internel_address <", value, "ccpInternelAddress");
            return (Criteria) this;
        }

        public Criteria andCcpInternelAddressLessThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.ccp_internel_address <=", value, "ccpInternelAddress");
            return (Criteria) this;
        }

        public Criteria andCcpInternelAddressLike(String value) {
            addCriterion("res_hpc_cluster.ccp_internel_address like", value, "ccpInternelAddress");
            return (Criteria) this;
        }

        public Criteria andCcpInternelAddressNotLike(String value) {
            addCriterion("res_hpc_cluster.ccp_internel_address not like", value, "ccpInternelAddress");
            return (Criteria) this;
        }

        public Criteria andCcpInternelAddressIn(List<String> values) {
            addCriterion("res_hpc_cluster.ccp_internel_address in", values, "ccpInternelAddress");
            return (Criteria) this;
        }

        public Criteria andCcpInternelAddressNotIn(List<String> values) {
            addCriterion("res_hpc_cluster.ccp_internel_address not in", values, "ccpInternelAddress");
            return (Criteria) this;
        }

        public Criteria andCcpInternelAddressBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.ccp_internel_address between", value1, value2, "ccpInternelAddress");
            return (Criteria) this;
        }

        public Criteria andCcpInternelAddressNotBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.ccp_internel_address not between", value1, value2, "ccpInternelAddress");
            return (Criteria) this;
        }

        public Criteria andLoginNodeExternalAddressIsNull() {
            addCriterion("res_hpc_cluster.login_node_external_address is null");
            return (Criteria) this;
        }

        public Criteria andLoginNodeExternalAddressIsNotNull() {
            addCriterion("res_hpc_cluster.login_node_external_address is not null");
            return (Criteria) this;
        }

        public Criteria andLoginNodeExternalAddressEqualTo(String value) {
            addCriterion("res_hpc_cluster.login_node_external_address =", value, "loginNodeExternalAddress");
            return (Criteria) this;
        }

        public Criteria andLoginNodeExternalAddressNotEqualTo(String value) {
            addCriterion("res_hpc_cluster.login_node_external_address <>", value, "loginNodeExternalAddress");
            return (Criteria) this;
        }

        public Criteria andLoginNodeExternalAddressGreaterThan(String value) {
            addCriterion("res_hpc_cluster.login_node_external_address >", value, "loginNodeExternalAddress");
            return (Criteria) this;
        }

        public Criteria andLoginNodeExternalAddressGreaterThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.login_node_external_address >=", value, "loginNodeExternalAddress");
            return (Criteria) this;
        }

        public Criteria andLoginNodeExternalAddressLessThan(String value) {
            addCriterion("res_hpc_cluster.login_node_external_address <", value, "loginNodeExternalAddress");
            return (Criteria) this;
        }

        public Criteria andLoginNodeExternalAddressLessThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.login_node_external_address <=", value, "loginNodeExternalAddress");
            return (Criteria) this;
        }

        public Criteria andLoginNodeExternalAddressLike(String value) {
            addCriterion("res_hpc_cluster.login_node_external_address like", value, "loginNodeExternalAddress");
            return (Criteria) this;
        }

        public Criteria andLoginNodeExternalAddressNotLike(String value) {
            addCriterion("res_hpc_cluster.login_node_external_address not like", value, "loginNodeExternalAddress");
            return (Criteria) this;
        }

        public Criteria andLoginNodeExternalAddressIn(List<String> values) {
            addCriterion("res_hpc_cluster.login_node_external_address in", values, "loginNodeExternalAddress");
            return (Criteria) this;
        }

        public Criteria andLoginNodeExternalAddressNotIn(List<String> values) {
            addCriterion("res_hpc_cluster.login_node_external_address not in", values, "loginNodeExternalAddress");
            return (Criteria) this;
        }

        public Criteria andLoginNodeExternalAddressBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.login_node_external_address between", value1, value2, "loginNodeExternalAddress");
            return (Criteria) this;
        }

        public Criteria andLoginNodeExternalAddressNotBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.login_node_external_address not between", value1, value2, "loginNodeExternalAddress");
            return (Criteria) this;
        }

        public Criteria andLoginNodeInternalAddressIsNull() {
            addCriterion("res_hpc_cluster.login_node_internal_address is null");
            return (Criteria) this;
        }

        public Criteria andLoginNodeInternalAddressIsNotNull() {
            addCriterion("res_hpc_cluster.login_node_internal_address is not null");
            return (Criteria) this;
        }

        public Criteria andLoginNodeInternalAddressEqualTo(String value) {
            addCriterion("res_hpc_cluster.login_node_internal_address =", value, "loginNodeInternalAddress");
            return (Criteria) this;
        }

        public Criteria andLoginNodeInternalAddressNotEqualTo(String value) {
            addCriterion("res_hpc_cluster.login_node_internal_address <>", value, "loginNodeInternalAddress");
            return (Criteria) this;
        }

        public Criteria andLoginNodeInternalAddressGreaterThan(String value) {
            addCriterion("res_hpc_cluster.login_node_internal_address >", value, "loginNodeInternalAddress");
            return (Criteria) this;
        }

        public Criteria andLoginNodeInternalAddressGreaterThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.login_node_internal_address >=", value, "loginNodeInternalAddress");
            return (Criteria) this;
        }

        public Criteria andLoginNodeInternalAddressLessThan(String value) {
            addCriterion("res_hpc_cluster.login_node_internal_address <", value, "loginNodeInternalAddress");
            return (Criteria) this;
        }

        public Criteria andLoginNodeInternalAddressLessThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.login_node_internal_address <=", value, "loginNodeInternalAddress");
            return (Criteria) this;
        }

        public Criteria andLoginNodeInternalAddressLike(String value) {
            addCriterion("res_hpc_cluster.login_node_internal_address like", value, "loginNodeInternalAddress");
            return (Criteria) this;
        }

        public Criteria andLoginNodeInternalAddressNotLike(String value) {
            addCriterion("res_hpc_cluster.login_node_internal_address not like", value, "loginNodeInternalAddress");
            return (Criteria) this;
        }

        public Criteria andLoginNodeInternalAddressIn(List<String> values) {
            addCriterion("res_hpc_cluster.login_node_internal_address in", values, "loginNodeInternalAddress");
            return (Criteria) this;
        }

        public Criteria andLoginNodeInternalAddressNotIn(List<String> values) {
            addCriterion("res_hpc_cluster.login_node_internal_address not in", values, "loginNodeInternalAddress");
            return (Criteria) this;
        }

        public Criteria andLoginNodeInternalAddressBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.login_node_internal_address between", value1, value2, "loginNodeInternalAddress");
            return (Criteria) this;
        }

        public Criteria andLoginNodeInternalAddressNotBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.login_node_internal_address not between", value1, value2, "loginNodeInternalAddress");
            return (Criteria) this;
        }

        public Criteria andErrorInfoIsNull() {
            addCriterion("res_hpc_cluster.error_info is null");
            return (Criteria) this;
        }

        public Criteria andErrorInfoIsNotNull() {
            addCriterion("res_hpc_cluster.error_info is not null");
            return (Criteria) this;
        }

        public Criteria andErrorInfoEqualTo(String value) {
            addCriterion("res_hpc_cluster.error_info =", value, "errorInfo");
            return (Criteria) this;
        }

        public Criteria andErrorInfoNotEqualTo(String value) {
            addCriterion("res_hpc_cluster.error_info <>", value, "errorInfo");
            return (Criteria) this;
        }

        public Criteria andErrorInfoGreaterThan(String value) {
            addCriterion("res_hpc_cluster.error_info >", value, "errorInfo");
            return (Criteria) this;
        }

        public Criteria andErrorInfoGreaterThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.error_info >=", value, "errorInfo");
            return (Criteria) this;
        }

        public Criteria andErrorInfoLessThan(String value) {
            addCriterion("res_hpc_cluster.error_info <", value, "errorInfo");
            return (Criteria) this;
        }

        public Criteria andErrorInfoLessThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.error_info <=", value, "errorInfo");
            return (Criteria) this;
        }

        public Criteria andErrorInfoLike(String value) {
            addCriterion("res_hpc_cluster.error_info like", value, "errorInfo");
            return (Criteria) this;
        }

        public Criteria andErrorInfoNotLike(String value) {
            addCriterion("res_hpc_cluster.error_info not like", value, "errorInfo");
            return (Criteria) this;
        }

        public Criteria andErrorInfoIn(List<String> values) {
            addCriterion("res_hpc_cluster.error_info in", values, "errorInfo");
            return (Criteria) this;
        }

        public Criteria andErrorInfoNotIn(List<String> values) {
            addCriterion("res_hpc_cluster.error_info not in", values, "errorInfo");
            return (Criteria) this;
        }

        public Criteria andErrorInfoBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.error_info between", value1, value2, "errorInfo");
            return (Criteria) this;
        }

        public Criteria andErrorInfoNotBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.error_info not between", value1, value2, "errorInfo");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNull() {
            addCriterion("res_hpc_cluster.task_id is null");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNotNull() {
            addCriterion("res_hpc_cluster.task_id is not null");
            return (Criteria) this;
        }

        public Criteria andTaskIdEqualTo(String value) {
            addCriterion("res_hpc_cluster.task_id =", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotEqualTo(String value) {
            addCriterion("res_hpc_cluster.task_id <>", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThan(String value) {
            addCriterion("res_hpc_cluster.task_id >", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.task_id >=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThan(String value) {
            addCriterion("res_hpc_cluster.task_id <", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.task_id <=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLike(String value) {
            addCriterion("res_hpc_cluster.task_id like", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotLike(String value) {
            addCriterion("res_hpc_cluster.task_id not like", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdIn(List<String> values) {
            addCriterion("res_hpc_cluster.task_id in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotIn(List<String> values) {
            addCriterion("res_hpc_cluster.task_id not in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.task_id between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.task_id not between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskDescriptionIsNull() {
            addCriterion("res_hpc_cluster.task_description is null");
            return (Criteria) this;
        }

        public Criteria andTaskDescriptionIsNotNull() {
            addCriterion("res_hpc_cluster.task_description is not null");
            return (Criteria) this;
        }

        public Criteria andTaskDescriptionEqualTo(String value) {
            addCriterion("res_hpc_cluster.task_description =", value, "taskDescription");
            return (Criteria) this;
        }

        public Criteria andTaskDescriptionNotEqualTo(String value) {
            addCriterion("res_hpc_cluster.task_description <>", value, "taskDescription");
            return (Criteria) this;
        }

        public Criteria andTaskDescriptionGreaterThan(String value) {
            addCriterion("res_hpc_cluster.task_description >", value, "taskDescription");
            return (Criteria) this;
        }

        public Criteria andTaskDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.task_description >=", value, "taskDescription");
            return (Criteria) this;
        }

        public Criteria andTaskDescriptionLessThan(String value) {
            addCriterion("res_hpc_cluster.task_description <", value, "taskDescription");
            return (Criteria) this;
        }

        public Criteria andTaskDescriptionLessThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.task_description <=", value, "taskDescription");
            return (Criteria) this;
        }

        public Criteria andTaskDescriptionLike(String value) {
            addCriterion("res_hpc_cluster.task_description like", value, "taskDescription");
            return (Criteria) this;
        }

        public Criteria andTaskDescriptionNotLike(String value) {
            addCriterion("res_hpc_cluster.task_description not like", value, "taskDescription");
            return (Criteria) this;
        }

        public Criteria andTaskDescriptionIn(List<String> values) {
            addCriterion("res_hpc_cluster.task_description in", values, "taskDescription");
            return (Criteria) this;
        }

        public Criteria andTaskDescriptionNotIn(List<String> values) {
            addCriterion("res_hpc_cluster.task_description not in", values, "taskDescription");
            return (Criteria) this;
        }

        public Criteria andTaskDescriptionBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.task_description between", value1, value2, "taskDescription");
            return (Criteria) this;
        }

        public Criteria andTaskDescriptionNotBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.task_description not between", value1, value2, "taskDescription");
            return (Criteria) this;
        }

        public Criteria andOwnerIdIsNull() {
            addCriterion("res_hpc_cluster.owner_id is null");
            return (Criteria) this;
        }

        public Criteria andOwnerIdIsNotNull() {
            addCriterion("res_hpc_cluster.owner_id is not null");
            return (Criteria) this;
        }

        public Criteria andOwnerIdEqualTo(String value) {
            addCriterion("res_hpc_cluster.owner_id =", value, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOwnerIdNotEqualTo(String value) {
            addCriterion("res_hpc_cluster.owner_id <>", value, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOwnerIdGreaterThan(String value) {
            addCriterion("res_hpc_cluster.owner_id >", value, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOwnerIdGreaterThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.owner_id >=", value, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOwnerIdLessThan(String value) {
            addCriterion("res_hpc_cluster.owner_id <", value, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOwnerIdLessThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.owner_id <=", value, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOwnerIdLike(String value) {
            addCriterion("res_hpc_cluster.owner_id like", value, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOwnerIdNotLike(String value) {
            addCriterion("res_hpc_cluster.owner_id not like", value, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOwnerIdIn(List<String> values) {
            addCriterion("res_hpc_cluster.owner_id in", values, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOwnerIdNotIn(List<String> values) {
            addCriterion("res_hpc_cluster.owner_id not in", values, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOwnerIdBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.owner_id between", value1, value2, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOwnerIdNotBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.owner_id not between", value1, value2, "ownerId");
            return (Criteria) this;
        }

        public Criteria andOrgSidIsNull() {
            addCriterion("res_hpc_cluster.org_sid is null");
            return (Criteria) this;
        }

        public Criteria andOrgSidIsNotNull() {
            addCriterion("res_hpc_cluster.org_sid is not null");
            return (Criteria) this;
        }

        public Criteria andOrgSidEqualTo(Long value) {
            addCriterion("res_hpc_cluster.org_sid =", value, "orgSid");
            return (Criteria) this;
        }

        public Criteria andOrgSidNotEqualTo(Long value) {
            addCriterion("res_hpc_cluster.org_sid <>", value, "orgSid");
            return (Criteria) this;
        }

        public Criteria andOrgSidGreaterThan(Long value) {
            addCriterion("res_hpc_cluster.org_sid >", value, "orgSid");
            return (Criteria) this;
        }

        public Criteria andOrgSidGreaterThanOrEqualTo(Long value) {
            addCriterion("res_hpc_cluster.org_sid >=", value, "orgSid");
            return (Criteria) this;
        }

        public Criteria andOrgSidLessThan(Long value) {
            addCriterion("res_hpc_cluster.org_sid <", value, "orgSid");
            return (Criteria) this;
        }

        public Criteria andOrgSidLessThanOrEqualTo(Long value) {
            addCriterion("res_hpc_cluster.org_sid <=", value, "orgSid");
            return (Criteria) this;
        }

        public Criteria andOrgSidIn(List<Long> values) {
            addCriterion("res_hpc_cluster.org_sid in", values, "orgSid");
            return (Criteria) this;
        }

        public Criteria andOrgSidNotIn(List<Long> values) {
            addCriterion("res_hpc_cluster.org_sid not in", values, "orgSid");
            return (Criteria) this;
        }

        public Criteria andOrgSidBetween(Long value1, Long value2) {
            addCriterion("res_hpc_cluster.org_sid between", value1, value2, "orgSid");
            return (Criteria) this;
        }

        public Criteria andOrgSidNotBetween(Long value1, Long value2) {
            addCriterion("res_hpc_cluster.org_sid not between", value1, value2, "orgSid");
            return (Criteria) this;
        }

        public Criteria andCreatedOrgSidIsNull() {
            addCriterion("res_hpc_cluster.created_org_sid is null");
            return (Criteria) this;
        }

        public Criteria andCreatedOrgSidIsNotNull() {
            addCriterion("res_hpc_cluster.created_org_sid is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedOrgSidEqualTo(Long value) {
            addCriterion("res_hpc_cluster.created_org_sid =", value, "createdOrgSid");
            return (Criteria) this;
        }

        public Criteria andCreatedOrgSidNotEqualTo(Long value) {
            addCriterion("res_hpc_cluster.created_org_sid <>", value, "createdOrgSid");
            return (Criteria) this;
        }

        public Criteria andCreatedOrgSidGreaterThan(Long value) {
            addCriterion("res_hpc_cluster.created_org_sid >", value, "createdOrgSid");
            return (Criteria) this;
        }

        public Criteria andCreatedOrgSidGreaterThanOrEqualTo(Long value) {
            addCriterion("res_hpc_cluster.created_org_sid >=", value, "createdOrgSid");
            return (Criteria) this;
        }

        public Criteria andCreatedOrgSidLessThan(Long value) {
            addCriterion("res_hpc_cluster.created_org_sid <", value, "createdOrgSid");
            return (Criteria) this;
        }

        public Criteria andCreatedOrgSidLessThanOrEqualTo(Long value) {
            addCriterion("res_hpc_cluster.created_org_sid <=", value, "createdOrgSid");
            return (Criteria) this;
        }

        public Criteria andCreatedOrgSidIn(List<Long> values) {
            addCriterion("res_hpc_cluster.created_org_sid in", values, "createdOrgSid");
            return (Criteria) this;
        }

        public Criteria andCreatedOrgSidNotIn(List<Long> values) {
            addCriterion("res_hpc_cluster.created_org_sid not in", values, "createdOrgSid");
            return (Criteria) this;
        }

        public Criteria andCreatedOrgSidBetween(Long value1, Long value2) {
            addCriterion("res_hpc_cluster.created_org_sid between", value1, value2, "createdOrgSid");
            return (Criteria) this;
        }

        public Criteria andCreatedOrgSidNotBetween(Long value1, Long value2) {
            addCriterion("res_hpc_cluster.created_org_sid not between", value1, value2, "createdOrgSid");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("res_hpc_cluster.version is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("res_hpc_cluster.version is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(Long value) {
            addCriterion("res_hpc_cluster.version =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(Long value) {
            addCriterion("res_hpc_cluster.version <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(Long value) {
            addCriterion("res_hpc_cluster.version >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(Long value) {
            addCriterion("res_hpc_cluster.version >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(Long value) {
            addCriterion("res_hpc_cluster.version <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(Long value) {
            addCriterion("res_hpc_cluster.version <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<Long> values) {
            addCriterion("res_hpc_cluster.version in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<Long> values) {
            addCriterion("res_hpc_cluster.version not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(Long value1, Long value2) {
            addCriterion("res_hpc_cluster.version between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(Long value1, Long value2) {
            addCriterion("res_hpc_cluster.version not between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNull() {
            addCriterion("res_hpc_cluster.created_by is null");
            return (Criteria) this;
        }

        public Criteria andCreatedByIsNotNull() {
            addCriterion("res_hpc_cluster.created_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedByEqualTo(String value) {
            addCriterion("res_hpc_cluster.created_by =", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotEqualTo(String value) {
            addCriterion("res_hpc_cluster.created_by <>", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThan(String value) {
            addCriterion("res_hpc_cluster.created_by >", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByGreaterThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.created_by >=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThan(String value) {
            addCriterion("res_hpc_cluster.created_by <", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLessThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.created_by <=", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByLike(String value) {
            addCriterion("res_hpc_cluster.created_by like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotLike(String value) {
            addCriterion("res_hpc_cluster.created_by not like", value, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByIn(List<String> values) {
            addCriterion("res_hpc_cluster.created_by in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotIn(List<String> values) {
            addCriterion("res_hpc_cluster.created_by not in", values, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.created_by between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedByNotBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.created_by not between", value1, value2, "createdBy");
            return (Criteria) this;
        }

        public Criteria andCreatedDtIsNull() {
            addCriterion("res_hpc_cluster.created_dt is null");
            return (Criteria) this;
        }

        public Criteria andCreatedDtIsNotNull() {
            addCriterion("res_hpc_cluster.created_dt is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedDtEqualTo(Date value) {
            addCriterion("res_hpc_cluster.created_dt =", value, "createdDt");
            return (Criteria) this;
        }

        public Criteria andCreatedDtNotEqualTo(Date value) {
            addCriterion("res_hpc_cluster.created_dt <>", value, "createdDt");
            return (Criteria) this;
        }

        public Criteria andCreatedDtGreaterThanOrEqualTo(Date value) {
            addCriterion("res_hpc_cluster.created_dt >=", value, "createdDt");
            return (Criteria) this;
        }
        public Criteria andEndTimeGreaterThan(Date value) {
            addCriterion("res_hpc_cluster.end_time is null or res_hpc_cluster.end_time >", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeBetween(Date value1, Date value2) {
            addCriterion("res_hpc_cluster.end_time between", value1, value2, "endTime");
            return (Criteria) this;
        }


        public Criteria andCreatedDtLessThan(Date value) {
            addCriterion("res_hpc_cluster.created_dt <", value, "createdDt");
            return (Criteria) this;
        }

        public Criteria andCreatedDtLessThanOrEqualTo(Date value) {
            addCriterion("res_hpc_cluster.created_dt <=", value, "createdDt");
            return (Criteria) this;
        }

        public Criteria andCreatedDtIn(List<Date> values) {
            addCriterion("res_hpc_cluster.created_dt in", values, "createdDt");
            return (Criteria) this;
        }

        public Criteria andCreatedDtNotIn(List<Date> values) {
            addCriterion("res_hpc_cluster.created_dt not in", values, "createdDt");
            return (Criteria) this;
        }

        public Criteria andCreatedDtBetween(Date value1, Date value2) {
            addCriterion("res_hpc_cluster.created_dt between", value1, value2, "createdDt");
            return (Criteria) this;
        }

        public Criteria andCreatedDtNotBetween(Date value1, Date value2) {
            addCriterion("res_hpc_cluster.created_dt not between", value1, value2, "createdDt");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNull() {
            addCriterion("res_hpc_cluster.updated_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIsNotNull() {
            addCriterion("res_hpc_cluster.updated_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedByEqualTo(String value) {
            addCriterion("res_hpc_cluster.updated_by =", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotEqualTo(String value) {
            addCriterion("res_hpc_cluster.updated_by <>", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThan(String value) {
            addCriterion("res_hpc_cluster.updated_by >", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByGreaterThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.updated_by >=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThan(String value) {
            addCriterion("res_hpc_cluster.updated_by <", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLessThanOrEqualTo(String value) {
            addCriterion("res_hpc_cluster.updated_by <=", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByLike(String value) {
            addCriterion("res_hpc_cluster.updated_by like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotLike(String value) {
            addCriterion("res_hpc_cluster.updated_by not like", value, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andPoolUuidEqualTo(String value) {
            addCriterion("res_hpc_cluster.pool_uuid =", value, "poolUuid");
            return (Criteria) this;
        }

        public Criteria andUpdatedByIn(List<String> values) {
            addCriterion("res_hpc_cluster.updated_by in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotIn(List<String> values) {
            addCriterion("res_hpc_cluster.updated_by not in", values, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.updated_by between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedByNotBetween(String value1, String value2) {
            addCriterion("res_hpc_cluster.updated_by not between", value1, value2, "updatedBy");
            return (Criteria) this;
        }

        public Criteria andUpdatedDtIsNull() {
            addCriterion("res_hpc_cluster.updated_dt is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedDtIsNotNull() {
            addCriterion("res_hpc_cluster.updated_dt is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedDtEqualTo(Date value) {
            addCriterion("res_hpc_cluster.updated_dt =", value, "updatedDt");
            return (Criteria) this;
        }

        public Criteria andUpdatedDtNotEqualTo(Date value) {
            addCriterion("res_hpc_cluster.updated_dt <>", value, "updatedDt");
            return (Criteria) this;
        }

        public Criteria andUpdatedDtGreaterThan(Date value) {
            addCriterion("res_hpc_cluster.updated_dt >", value, "updatedDt");
            return (Criteria) this;
        }

        public Criteria andUpdatedDtGreaterThanOrEqualTo(Date value) {
            addCriterion("res_hpc_cluster.updated_dt >=", value, "updatedDt");
            return (Criteria) this;
        }

        public Criteria andUpdatedDtLessThan(Date value) {
            addCriterion("res_hpc_cluster.updated_dt <", value, "updatedDt");
            return (Criteria) this;
        }

        public Criteria andUpdatedDtLessThanOrEqualTo(Date value) {
            addCriterion("res_hpc_cluster.updated_dt <=", value, "updatedDt");
            return (Criteria) this;
        }

        public Criteria andUpdatedDtIn(List<Date> values) {
            addCriterion("res_hpc_cluster.updated_dt in", values, "updatedDt");
            return (Criteria) this;
        }

        public Criteria andUpdatedDtNotIn(List<Date> values) {
            addCriterion("res_hpc_cluster.updated_dt not in", values, "updatedDt");
            return (Criteria) this;
        }

        public Criteria andUpdatedDtBetween(Date value1, Date value2) {
            addCriterion("res_hpc_cluster.updated_dt between", value1, value2, "updatedDt");
            return (Criteria) this;
        }

        public Criteria andUpdatedDtNotBetween(Date value1, Date value2) {
            addCriterion("res_hpc_cluster.updated_dt not between", value1, value2, "updatedDt");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}