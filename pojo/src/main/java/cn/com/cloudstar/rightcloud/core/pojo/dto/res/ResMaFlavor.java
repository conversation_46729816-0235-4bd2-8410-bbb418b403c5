package cn.com.cloudstar.rightcloud.core.pojo.dto.res;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * MA资源规格同步 createTime:2022-04-24 16:44
 *
 * <AUTHOR>
 * @version 1.0
 * @since JDK1.8
 */
@Data
public class ResMaFlavor implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long id ;

    /**
     * 资源规格代码
     */
    private String specCode;

    /**
     * 资源规格名称
     */
    private String specName;

    /**
     * 资源池类型
     */
    private String type;

    /**
     * 状态
     */
    private String phase;

    /**
     * 架构
     */
    private String cpuArch;

    /**
     * CPU核心数量
     */
    private Integer  cpuNum;

    /**
     * 内存大小
     */
    private String memory;

    /**
     * CPU类型
     */
    private String gpuType;

    /**
     * CPU卡数
     */
    private Integer gpuNum;

    /**
     * NPU类型
     */
    private String npuType;

    /**
     * NPU卡数
     */
    private String npuNum;

    /**
     * 数据盘
     */
    private String dataVolume;

    /**
     * 作业规格
     */
    private String jobFlavors;

    /**
     * 所有者ID
     */
    private String ownerId;

    /**
     * 组织ID
     */
    private Long orgSid;

    /**
     * 创建者组织ID
     */
    private Long createdOrgSid;

    /**
     * 云环境ID
     */
    private Long cloudEnvId;

    /**
     * 版本号
     */
    private Long version;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 修改时间
     */
    private Date updateDt;
}
