/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.core.pojo.fortress;

import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.exception.BizException;
import cn.com.cloudstar.rightcloud.common.redis.JedisUtil;
import cn.com.cloudstar.rightcloud.common.util.RestTemplateUtil;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.core.pojo.fortress.enums.FortressApiPathEnum;
import cn.com.cloudstar.rightcloud.core.pojo.fortress.request.*;
import cn.com.cloudstar.rightcloud.core.pojo.fortress.response.*;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * DESC: 堡垒机统一接口
 *
 * <AUTHOR>
 * @date 2020/08/14 10:22
 */
@Slf4j
public class FortressApi {

    public static final String API_PREFIX = "/shterm";

    // 缓存堡垒机token的key
    private static final String CACHE_FORTRESS_TOKEN = "cache:fortress:token:%s";

    private static final String ST_AUTH_TOKEN = "st-auth-token";

    private static final int EXPIRE = 10 * 60;

    /**
     * 验证账号是否正确
     *
     * @param authenticateRequest
     */
    public static boolean validateHost(FortressAuthenticateRequest authenticateRequest) {
        return StrUtil.isNotBlank(authenticate(authenticateRequest));
    }

    /**
     * 新建资产
     *
     * @param request
     */
    public static Integer addDev(FortressAddDevRequest request) {
        log.info("堡垒机新建资产：name[{}]-ip[{}]", request.getName(), request.getIp());
        return Integer.valueOf(exec(request, FortressApiPathEnum.ADD_DEV));
    }

    /**
     * 托管账号
     *
     * @param request
     */
    public static void escrowAccount(FortressEscrowAccountRequest request) {
        log.info("资产托管账号：id[{}]", request.getId());

        final String requestUrl = String.format(getRequestUrl(request, FortressApiPathEnum.MODIFY_DEV),
                                                request.getId());
        HttpEntity<FortressBaseRequest> httpEntity = new HttpEntity<>(request, getTokenHttpHeaders(request));

        exec(requestUrl, FortressApiPathEnum.MODIFY_DEV, httpEntity);
    }

    /**
     * 根据名称查询资产
     *
     * @param request
     */
    public static Optional<FortressQueryDevResponse> queryDevByName(FortressQueryDevRequest request) {
        if (Strings.isNullOrEmpty(request.getName())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1381459629));
        }

        String requestUrl = getRequestUrl(request, FortressApiPathEnum.QUERY_DEV);
        requestUrl = requestUrl + "?name=" + request.getName();

        HttpEntity<FortressBaseRequest> httpEntity = new HttpEntity<>(getTokenHttpHeaders(request));
        final String result = exec(requestUrl, FortressApiPathEnum.QUERY_DEV, httpEntity);
        if (Strings.isNullOrEmpty(result)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_104722149) + request.getName() + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1329696723));
        }

        final JSONArray jsonArray = JSONUtil.parseObj(result).getJSONArray("content");
        final List<FortressQueryDevResponse> responses = JSONUtil.toList(jsonArray, FortressQueryDevResponse.class);
        if (CollectionUtil.isEmpty(responses)) {
            log.info("查询资产[{}]在堡垒机中不存在", request.getName());
            return Optional.empty();
        }

        return responses.stream().findFirst();
    }

    /**
     * 获取指定客户端用户的一次性登录密码
     *
     * @param request
     */
    public static String oneTimePasswd(FortressOneTimePasswdRequest request) {

        String requestUrl = String.format(getRequestUrl(request, FortressApiPathEnum.ONE_TIME_PASSWD),
                                          request.getLoginName(), request.getClientIp());
        HttpEntity<FortressBaseRequest> httpEntity = new HttpEntity<>(getTokenHttpHeaders(request));

        final String result = exec(requestUrl, FortressApiPathEnum.ONE_TIME_PASSWD, httpEntity);

        return JSONUtil.parseObj(result).getStr("password");
    }

    /**
     * 移除资产
     *
     * @param request
     */
    public static boolean removeDev(FortressRemoveDevRequest request) {
        String requestUrl = String.format(getRequestUrl(request, FortressApiPathEnum.REMOVE_DEV), request.getId());
        HttpEntity<FortressBaseRequest> httpEntity = new HttpEntity<>(getTokenHttpHeaders(request));

        exec(requestUrl, FortressApiPathEnum.REMOVE_DEV, httpEntity);

        return true;
    }

    /**
     * 查询部门列表
     *
     * @param request
     */
    public static List<FortressQueryDepartmentResponse> queryDepartment(FortressBaseRequest request) {
        String requestUrl = getRequestUrl(request, FortressApiPathEnum.QUERY_DEPARTMENT);

        HttpEntity<FortressBaseRequest> httpEntity = new HttpEntity<>(getTokenHttpHeaders(request));
        final String result = exec(requestUrl, FortressApiPathEnum.QUERY_DEPARTMENT, httpEntity);

        return JSONUtil.toList(JSONUtil.parseArray(result), FortressQueryDepartmentResponse.class);
    }

    /**
     * 根据条件查询规则模板
     *
     * @param request
     */
    public static Optional<FortressQueryRuleTemplateResponse> queryRuleTemplateByName(
            FortressQueryRuleTemplateRequest request) {
        if (Strings.isNullOrEmpty(request.getName())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2146436797));
        }

        String requestUrl = getRequestUrl(request, FortressApiPathEnum.QUERY_RULE_TEMPLATE);
        requestUrl = requestUrl + "?name=" + request.getName();

        HttpEntity<FortressBaseRequest> httpEntity = new HttpEntity<>(getTokenHttpHeaders(request));
        final String result = exec(requestUrl, FortressApiPathEnum.QUERY_RULE_TEMPLATE, httpEntity);
        if (Strings.isNullOrEmpty(result)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1666874155) + request.getName() + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1329696723));
        }

        final JSONArray jsonArray = JSONUtil.parseObj(result).getJSONArray("content");
        final List<FortressQueryRuleTemplateResponse> responses = JSONUtil.toList(jsonArray,
                                                                                  FortressQueryRuleTemplateResponse.class);
        if (CollectionUtil.isEmpty(responses)) {
            log.info("查询规则模板[{}]在堡垒机中不存在", request.getName());
            return Optional.empty();
        }

        return responses.stream().findFirst();

    }

    /**
     * 根据名称查询用户
     *
     * @param request
     */
    public static Optional<FortressQueryUserResponse> queryUserByLoginName(FortressQueryUserRequest request) {
        if (Strings.isNullOrEmpty(request.getName())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1326451529));
        }

        String requestUrl = getRequestUrl(request, FortressApiPathEnum.QUERY_USER);
        requestUrl = requestUrl + "?loginName=" + request.getName().toLowerCase();

        HttpEntity<FortressBaseRequest> httpEntity = new HttpEntity<>(getTokenHttpHeaders(request));
        final String result = exec(requestUrl, FortressApiPathEnum.QUERY_USER, httpEntity);
        if (Strings.isNullOrEmpty(result)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_110498193) + request.getName() + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1329696723));
        }

        final JSONArray jsonArray = JSONUtil.parseObj(result).getJSONArray("content");
        final List<FortressQueryUserResponse> responses = JSONUtil.toList(jsonArray, FortressQueryUserResponse.class);
        if (CollectionUtil.isEmpty(responses)) {
            log.info("查询用户[{}]在堡垒机中不存在", request.getName());
            return Optional.empty();
        }

        return responses.stream().findFirst();
    }

    public static boolean removeUser(FortressRemoveUserRequest request) {
        final String requestUrl = String.format(getRequestUrl(request, FortressApiPathEnum.REMOVE_USER),
                                                request.getId());
        HttpEntity<FortressRemoveUserRequest> httpEntity = new HttpEntity<>(request, getTokenHttpHeaders(request));
        exec(requestUrl, FortressApiPathEnum.REMOVE_USER, httpEntity);
        return true;
    }

    /**
     * 根据名称查询公共密钥
     *
     * @param request
     */
    public static Optional<FortressQueryKeypairResponse> queryKeypair(FortressCommonQueryRequest request) {
        log.info("根据名称[{}]查询公共密钥：[{}]", request.getName());
        if (Strings.isNullOrEmpty(request.getName())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1224618515));
        }

        String requestUrl = getRequestUrl(request, FortressApiPathEnum.QUERY_KEYPAIR);
        requestUrl = requestUrl + "?nameLike=" + request.getName();

        HttpEntity<FortressBaseRequest> httpEntity = new HttpEntity<>(getTokenHttpHeaders(request));
        final String result = exec(requestUrl, FortressApiPathEnum.QUERY_KEYPAIR, httpEntity);
        if (Strings.isNullOrEmpty(result)) {
            return Optional.empty();
        }

        final JSONArray jsonArray = JSONUtil.parseObj(result).getJSONArray("content");
        final List<FortressQueryKeypairResponse> responses = JSONUtil.toList(jsonArray,
                                                                             FortressQueryKeypairResponse.class);
        if (CollectionUtil.isEmpty(responses)) {
            log.info("查询用户[{}]在堡垒机中不存在", request.getName());
            return Optional.empty();
        }

        return responses.stream().findFirst();
    }

    /**
     * 添加公共密钥
     *
     * @param request
     */
    public static Integer addKeypair(FortressAddKeypairRequest request) {

        final String requestUrl = String.format(getRequestUrl(request, FortressApiPathEnum.ADD_KEYPAIR),
                                                request.getLength());

        HttpEntity<FortressBaseRequest> httpEntity = new HttpEntity<>(request, getTokenHttpHeaders(request));

        String result = exec(requestUrl, FortressApiPathEnum.ADD_KEYPAIR, httpEntity);
        return JSONUtil.parseObj(result).getInt("id");
    }

    /**
     * 获取accessUrl
     *
     * @param request
     */
    public static String accessUrl(FortressAccessUrlRequest request) {
        final String requestUrl = getRequestUrl(request, FortressApiPathEnum.ACCESS_URL);
        HttpEntity<String> httpEntity = new HttpEntity<>(JSONUtil.toJsonStr(request), getTokenHttpHeaders(request));
        final String result = exec(requestUrl, FortressApiPathEnum.ACCESS_URL, httpEntity);
        if (Strings.isNullOrEmpty(result)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1733447679));
        }

        return JSONUtil.parseObj(result).getStr("url");
    }

    /**
     * 添加资产组
     *
     * @param request
     */
    public static FortressCreateResGroupResult createResGroup(FortressCreateResGroupRequest request) {
        String result = exec(request, FortressApiPathEnum.CREATE_RES_GROUP);
        return JSONUtil.toBean(result, FortressCreateResGroupResult.class);
    }

    /**
     * 根据名称查询资产组
     *
     * @param request
     */
    public static Optional<FortressCreateResGroupResult> queryResGroupByName(FortressQueryResGroupRequest request) {
        String result = exec(request, FortressApiPathEnum.QUERY_RES_GROUP);
        return Optional.ofNullable(JSONUtil.toBean(result, FortressCreateResGroupResult.class));
    }

    /**
     * 新增动态权限
     *
     * @param request
     */
    public static boolean createDynamicRule(FortressCreateDynamicRuleRequest request) {
        exec(request, FortressApiPathEnum.CREATE_DYNAMIC_RULE);
        return true;
    }

    /**
     * 移除动态权限
     *
     * @param request
     */
    public static boolean removeDynamicRule(FortressQueryDynamicRuleRequest request) {
        final Optional<FortressQueryDynamicRuleResponse> responseOptional = queryDynamicRuleByName(request);
        if (responseOptional.isPresent()) {
            String requestUrl = String.format(getRequestUrl(request, FortressApiPathEnum.REMOVE_DYNAMIC_RULE),
                                              responseOptional.get().getId());

            HttpEntity<FortressBaseRequest> httpEntity = new HttpEntity<>(getTokenHttpHeaders(request));
            exec(requestUrl, FortressApiPathEnum.REMOVE_DYNAMIC_RULE, httpEntity);
        }
        return true;
    }

    /**
     * 移除动态权限
     *
     * @param request
     */
    public static boolean removeDynamicRule(FortressRemoveDynamicRuleRequest request) {
        String requestUrl = String.format(getRequestUrl(request, FortressApiPathEnum.REMOVE_DYNAMIC_RULE),
                                          request.getId());

        HttpEntity<FortressBaseRequest> httpEntity = new HttpEntity<>(getTokenHttpHeaders(request));
        exec(requestUrl, FortressApiPathEnum.REMOVE_DYNAMIC_RULE, httpEntity);
        return true;
    }

    /**
     * 根据名称查询动态权限
     *
     * @param request
     */
    public static Optional<FortressQueryDynamicRuleResponse> queryDynamicRuleByName(
            FortressQueryDynamicRuleRequest request) {
        String requestUrl = getRequestUrl(request, FortressApiPathEnum.QUERY_DYNAMIC_RULE);
        requestUrl = requestUrl + "?name=" + request.getName();

        HttpEntity<FortressBaseRequest> httpEntity = new HttpEntity<>(getTokenHttpHeaders(request));
        final String result = exec(requestUrl, FortressApiPathEnum.QUERY_DYNAMIC_RULE, httpEntity);
        if (Strings.isNullOrEmpty(result)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1628463896) + request.getName() + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1329696723));
        }

        final JSONArray jsonArray = JSONUtil.parseObj(result).getJSONArray("content");
        final List<FortressQueryDynamicRuleResponse> responses = JSONUtil.toList(jsonArray,
                                                                                 FortressQueryDynamicRuleResponse.class);
        if (CollectionUtil.isEmpty(responses)) {
            log.info("查询动态权限[{}]在堡垒机中不存在", request.getName());
            return Optional.empty();
        }

        return responses.stream().findFirst();
    }

    /**
     * 根据名称查询动态权限
     *
     * @param request
     */
    public static List<FortressQueryDynamicRuleResponse> queryDynamicRuleByNameLike(
            FortressQueryDynamicRuleRequest request) {
        String requestUrl = getRequestUrl(request, FortressApiPathEnum.QUERY_DYNAMIC_RULE);
        requestUrl = requestUrl + "?nameLike=" + request.getName();

        HttpEntity<FortressBaseRequest> httpEntity = new HttpEntity<>(getTokenHttpHeaders(request));
        final String result = exec(requestUrl, FortressApiPathEnum.QUERY_DYNAMIC_RULE, httpEntity);
        if (Strings.isNullOrEmpty(result)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1628463896) + request.getName() + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1329696723));
        }

        final JSONArray jsonArray = JSONUtil.parseObj(result).getJSONArray("content");
        final List<FortressQueryDynamicRuleResponse> responses = JSONUtil.toList(jsonArray,
                                                                                 FortressQueryDynamicRuleResponse.class);

        return responses;
    }

    /**
     * 认证获取token
     *
     * @param request
     */
    private static String authenticate(FortressBaseRequest request) {
        String requestUrl = request.getDomain() + FortressApiPathEnum.AUTHENTICATE.getPath();

        JSONObject param = new JSONObject();
        param.put("username", request.getUsername());
        param.put("password", request.getPassword());
        HttpEntity<JSONObject> httpEntity = new HttpEntity<>(param, getBaseHttpHeaders());

        final String result = exec(requestUrl, FortressApiPathEnum.AUTHENTICATE, httpEntity);

        final String authToken = JSONUtil.parseObj(result).getStr("ST_AUTH_TOKEN");

        // 缓存token,因为只有一个token有效，所有保持这个key下面只能有一个值，
        final String cacheKey = String.format(CACHE_FORTRESS_TOKEN, DigestUtils.sha256Hex(request.getDomain()));
        JedisUtil.instance().del(cacheKey);
        JedisUtil.instance().hset(cacheKey, getKey(request), authToken, EXPIRE);

        return authToken;
    }


    /**
     * 检测执行状态
     *
     * @param responseEntity
     */
    private static void checkStatus(ResponseEntity responseEntity) {
        if (Objects.isNull(responseEntity) || !responseEntity.getStatusCode().is2xxSuccessful()) {
            throw new BizException(String.valueOf(responseEntity.getBody()));
        }
    }

    private static String getRequestUrl(FortressBaseRequest request, FortressApiPathEnum apiPathEnum) {
        return request.getDomain() + API_PREFIX + apiPathEnum.getPath();
    }

    private static String exec(FortressBaseRequest request, FortressApiPathEnum apiPathEnum) {
        final String requestUrl = getRequestUrl(request, apiPathEnum);
        HttpEntity<FortressBaseRequest> httpEntity = new HttpEntity<>(request, getTokenHttpHeaders(request));
        return exec(requestUrl, apiPathEnum, httpEntity);
    }

    private static String exec(String requestUrl, FortressApiPathEnum apiPathEnum, HttpEntity httpEntity) {
        ResponseEntity<String> responseEntity = RestTemplateUtil.instance()
                                                                .exchange(requestUrl, apiPathEnum.getMethod(),
                                                                          httpEntity, String.class);
        checkStatus(responseEntity);

        final String entityBody = responseEntity.getBody();
        return entityBody;
    }

    private static HttpHeaders getTokenHttpHeaders(FortressBaseRequest request) {
        final HttpHeaders httpHeaders = getBaseHttpHeaders();
        final String cacheKey = String.format(CACHE_FORTRESS_TOKEN, DigestUtils.sha256Hex(request.getDomain()));
        String token = JedisUtil.instance().hget(cacheKey, getKey(request));
        if (StrUtil.isBlank(token)) {
            token = authenticate(request);
        }
        httpHeaders.add(ST_AUTH_TOKEN, token);

        // 自定义header
        if (CollectionUtil.isNotEmpty(request.getHttpHeaders())) {
            request.getHttpHeaders().forEach((key, value) -> httpHeaders.add(key, value));
        }

        return httpHeaders;
    }

    private static String getKey(FortressBaseRequest request) {
        StringBuilder sb = new StringBuilder();
        sb.append(request.getDomain())
          .append("#")
          .append(request.getUsername())
          .append("#")
          .append(request.getPassword());

        return DigestUtils.sha256Hex(sb.toString());
    }

    private static HttpHeaders getBaseHttpHeaders() {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON_UTF8);
        httpHeaders.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        return httpHeaders;
    }

}
