/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.remote.api.pojo.resource.cloud.request;

import java.util.List;

import lombok.Data;
import lombok.EqualsAndHashCode;

import cn.com.cloudstar.rightcloud.remote.api.pojo.request.common.BasicRequest;

/**
 * The type IntelliJ IDEA.
 * <p>
 *
 * <AUTHOR>
 * @date 2020/11/26
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CloudEnvParams extends BasicRequest {

    /**
     * 云环境分类
     */
    private String cloudEnvCategory;

    /**
     * 账户id
     */
    private Long cloudEnvAccountId;

    /**
     * 账户id不等于
     */
    private Long cloudEnvAccountIdNotEquals;

    /**
     * 云环境id
     */
    private Long cloudEnvId;

    /**
     * 云环境名称
     */
    private String cloudEnvName;

    /**
     * id不等于
     */
    private Long notId;

    /**
     * 状态
     */
    private String status;

    /**
     * 权限过滤sql
     */
    private String df;

    /**
     * 状态不等于
     */
    private List<String> statusNotIn;

    /**
     * 账户id列表
     */
    private List<Long> accountIdIn;

    /**
     * id列表
     */
    private List<Long> idIn;

    /**
     * 云环境类型
     */
    private String cloudEnvType;

    /**
     * 区域
     */
    private String region;

    /**
     * 云环境类型列表
     */
    private List<String> cloudEnvTypes;

    /**
     * 创建者
     */
    private String createdBy;

    /**
     * 停止模式
     */
    private String stoppedMode;

    /**
     * 组织id
     */
    private Long orgSidByDf;

    /**
     * 组织id tree path
     */
    private String treePathLike;

    /**
     * platformComponentId
     */
    private String platformComponentId;

    private String id;

    private String notRcLinkGateway;

    private Boolean withoutAttrData;

    private String statusNotEqual;

    private Long orgSid;

    private String dynamicParameter;

}
