/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.remote.api.pojo.analysis;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

/**
 * 高速缓存询价
 **/
@Data
public class InquiryDcsPrice implements Serializable {

    /**
     * 云环境ID
     */
    private Long envId;

    /**
     * 计费模式
     **/
    private String chargingMode;
    /**
     * 订购周期类型：
     *
     * 0：天 1：周 2：月 3：年 4：小时 5：绝对时间
     **/
    private Integer periodType;

    /**
     * 订购周期数。
     *
     * 包周期计费，且订购周期类型非5时必填。
     **/
    private Integer periodNum;

    private String periodEndDate;

    private String relativeResourceId;

    private String relativeResourcePeriodType;

    private Integer subscriptionNum = 1;
    /**
     * 产品信息
     **/
    private List<InquiryProduct> productInfos;

    private String inquiryTime;
}
