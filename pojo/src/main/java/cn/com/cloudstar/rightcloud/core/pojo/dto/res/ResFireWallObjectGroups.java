/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.core.pojo.dto.res;

import java.io.Serializable;
import java.util.Date;

public class ResFireWallObjectGroups implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 对象组ID
     */
    private String objectGroupId;

    /**
     * 对象组名称
     */
    private String objectGroupName;

    /**
     * 描述
     */
    private String description;

    /**
     * 对象组类型:Service:服务, IPv4:IPv4地址
     */
    private String objectGroupType;

    /**
     * 是否已审核
     */
    private Boolean audited;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 云环境ID
     */
    private Long cloudEnvId;

    /**
     * 组织ID
     */
    private Long orgSid;

    private Date createdDt;

    private String createdBy;

    private Date updatedDt;

    private String updatedBy;

    private Long version;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getObjectGroupId() {
        return objectGroupId;
    }

    public void setObjectGroupId(String objectGroupId) {
        this.objectGroupId = objectGroupId;
    }

    public String getObjectGroupName() {
        return objectGroupName;
    }

    public void setObjectGroupName(String objectGroupName) {
        this.objectGroupName = objectGroupName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getObjectGroupType() {
        return objectGroupType;
    }

    public void setObjectGroupType(String objectGroupType) {
        this.objectGroupType = objectGroupType;
    }

    public Boolean getAudited() {
        return audited;
    }

    public void setAudited(Boolean audited) {
        this.audited = audited;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getTenantName() {
        return tenantName;
    }

    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }

    public Long getCloudEnvId() {
        return cloudEnvId;
    }

    public void setCloudEnvId(Long cloudEnvId) {
        this.cloudEnvId = cloudEnvId;
    }

    public Long getOrgSid() {
        return orgSid;
    }

    public void setOrgSid(Long orgSid) {
        this.orgSid = orgSid;
    }

    public Date getCreatedDt() {
        return createdDt;
    }

    public void setCreatedDt(Date createdDt) {
        this.createdDt = createdDt;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getUpdatedDt() {
        return updatedDt;
    }

    public void setUpdatedDt(Date updatedDt) {
        this.updatedDt = updatedDt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }
}
