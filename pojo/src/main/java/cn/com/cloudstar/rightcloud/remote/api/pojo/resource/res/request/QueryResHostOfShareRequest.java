/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

import cn.com.cloudstar.rightcloud.remote.api.pojo.request.common.BasicRequest;

/**
 * The type IntelliJ IDEA.
 * <p>
 *
 * <AUTHOR>
 * @date 2020/11/26
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QueryResHostOfShareRequest extends BasicRequest {
    /**
     * 云环境id
     */
    private Long parentTopologySid;

    /**
     * 关联云环境的组织id
     */
    private Long allocTargetId;

    /**
     * 关联云环境的组织类型
     */
    private String allocTargetType;
}
