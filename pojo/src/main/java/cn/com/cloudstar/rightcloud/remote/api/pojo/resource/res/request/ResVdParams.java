/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.List;

import lombok.Data;
import lombok.EqualsAndHashCode;

import cn.com.cloudstar.rightcloud.remote.api.pojo.request.common.BasicRequest;

/**
 * The type IntelliJ IDEA.
 * <p>
 *
 * <AUTHOR>
 * @date 2020/11/27
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ResVdParams extends BasicRequest {

    /**
     * 自服务实例id
     */
    private Long serviceDeployInstId;

    /**
     * 实例id
     */
    private String resVmId;

    /**
     * id
     */
    private List<String> idList;

    /**
     * 状态
     */
    private List<String> statusNotIn;

    /**
     * 云环境id
     */
    private Long cloudEnvId;

    /**
     * 是否忽略权限过滤
     */
    @JsonIgnore
    private Boolean ignoreDataFilter;

    private List<String> hostIds;

    private Long orgSidNotEqual;

    private Long orgSid;

    private List<Long> projectIds;
}
