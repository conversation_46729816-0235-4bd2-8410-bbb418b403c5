/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.remote.api.pojo.resource.elastic;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResVm;

@ApiModel(description = "弹性伸缩组")
public class ElasticGroup implements Serializable {

    @ApiModelProperty(value = "名称")
    private Long id;

    @ApiModelProperty(value = "弹性伸缩组名称")
    private String groupName;

    @ApiModelProperty(value = "是否可用")
    private Boolean enable;

    @ApiModelProperty(value = "最小数量")
    private Integer minQty;

    @ApiModelProperty(value = "最大数量")
    private Integer maxQty;

    @ApiModelProperty(value = "最大数量")
    private String elasticType;

    @ApiModelProperty(value = "伸缩阀值")
    private Integer threshold;

    @ApiModelProperty(value = "弹性标签")
    private String elasticTag;

    @ApiModelProperty(value = "增长量")
    private Integer increaseStep;

    @ApiModelProperty(value = "缩减量")
    private Integer decreaseStep;

    @ApiModelProperty(value = "伸缩间隔时间")
    private Integer intervalTime;

    @ApiModelProperty(value = "企业id")
    private Long orgSid;

    @ApiModelProperty(value = "云环境id")
    private Long cloudEnvId;

    private String cloudEnvName;

    private String cloudEnvType;

    private String region;

    private Long serverTemplateId;

    private String serverTemplateName;

    private Long cloudDeploymentId;

    private String cloudDeploymentName;

    private ResVm resVm;

    private String status;

    private String createdBy;

    private Date createdDt;

    private String updatedBy;

    private Date updatedDt;

    private Date lastElasticTime;

    private Long version;

    private Integer instanceNum;

    private String instanceConfig;

    private List<Long> strategyIds;

    private String orgName;

    public String getInstanceConfig() {
        return instanceConfig;
    }

    public void setInstanceConfig(String instanceConfig) {
        this.instanceConfig = instanceConfig;
    }

    public List<Long> getStrategyIds() {
        return strategyIds;
    }

    public void setStrategyIds(List<Long> strategyIds) {
        this.strategyIds = strategyIds;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName == null ? null : groupName.trim();
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public Integer getMinQty() {
        return minQty;
    }

    public void setMinQty(Integer minQty) {
        this.minQty = minQty;
    }

    public Integer getMaxQty() {
        return maxQty;
    }

    public void setMaxQty(Integer maxQty) {
        this.maxQty = maxQty;
    }

    public String getElasticType() {
        return elasticType;
    }

    public void setElasticType(String elasticType) {
        this.elasticType = elasticType == null ? null : elasticType.trim();
    }

    public Integer getThreshold() {
        return threshold;
    }

    public void setThreshold(Integer threshold) {
        this.threshold = threshold;
    }

    public String getElasticTag() {
        return elasticTag;
    }

    public void setElasticTag(String elasticTag) {
        this.elasticTag = elasticTag;
    }

    public Integer getIncreaseStep() {
        return increaseStep;
    }

    public void setIncreaseStep(Integer increaseStep) {
        this.increaseStep = increaseStep;
    }

    public Integer getDecreaseStep() {
        return decreaseStep;
    }

    public void setDecreaseStep(Integer decreaseStep) {
        this.decreaseStep = decreaseStep;
    }

    public Integer getIntervalTime() {
        return intervalTime;
    }

    public void setIntervalTime(Integer intervalTime) {
        this.intervalTime = intervalTime;
    }

    public Long getOrgSid() {
        return orgSid;
    }

    public void setOrgSid(Long orgSid) {
        this.orgSid = orgSid;
    }

    public ResVm getResVm() {
        return resVm;
    }

    public void setResVm(ResVm resVm) {
        this.resVm = resVm;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreatedDt() {
        return createdDt;
    }

    public void setCreatedDt(Date createdDt) {
        this.createdDt = createdDt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy == null ? null : updatedBy.trim();
    }

    public Date getUpdatedDt() {
        return updatedDt;
    }

    public void setUpdatedDt(Date updatedDt) {
        this.updatedDt = updatedDt;
    }

    public Date getLastElasticTime() {
        return lastElasticTime;
    }

    public void setLastElasticTime(Date lastElasticTime) {
        this.lastElasticTime = lastElasticTime;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public Long getServerTemplateId() {
        return serverTemplateId;
    }

    public void setServerTemplateId(Long serverTemplateId) {
        this.serverTemplateId = serverTemplateId;
    }

    public Long getCloudEnvId() {
        return cloudEnvId;
    }

    public void setCloudEnvId(Long cloudEnvId) {
        this.cloudEnvId = cloudEnvId;
    }

    public String getServerTemplateName() {
        return serverTemplateName;
    }

    public void setServerTemplateName(String serverTemplateName) {
        this.serverTemplateName = serverTemplateName;
    }

    public Long getCloudDeploymentId() {
        return cloudDeploymentId;
    }

    public void setCloudDeploymentId(Long cloudDeploymentId) {
        this.cloudDeploymentId = cloudDeploymentId;
    }

    public String getCloudDeploymentName() {
        return cloudDeploymentName;
    }

    public void setCloudDeploymentName(String cloudDeploymentName) {
        this.cloudDeploymentName = cloudDeploymentName;
    }

    public String getCloudEnvName() {
        return cloudEnvName;
    }

    public void setCloudEnvName(String cloudEnvName) {
        this.cloudEnvName = cloudEnvName;
    }

    public String getCloudEnvType() {
        return cloudEnvType;
    }

    public void setCloudEnvType(String cloudEnvType) {
        this.cloudEnvType = cloudEnvType;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public Integer getInstanceNum() {
        return instanceNum;
    }

    public void setInstanceNum(Integer instanceNum) {
        this.instanceNum = instanceNum;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }
}
