/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.module.resource.bean.request.ticket;

import cn.com.cloudstar.rightcloud.resource.bean.model.AttachmentsVO;
import cn.com.cloudstar.rightcloud.resource.bean.model.RecordVO;
import cn.com.cloudstar.rightcloud.resource.bean.model.AttachmentsVO;
import cn.com.cloudstar.rightcloud.resource.bean.model.RecordVO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.validation.constraints.NotNull;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 处理工单
 *
 * <AUTHOR>
 */
@ApiModel(description = "处理工单")
@Setter
@Getter
public class UpdateWorkTicketRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 工单id
     */
    @ApiModelProperty("工单id")
    @NotNull
    private Long id;

    /**
     * 工单编号
     */
    @ApiModelProperty("工单编号")
    private String ticketNo;

    /**
     * 工单类型
     */
    @ApiModelProperty("工单类型")
    private Long ticketCategoryId;

    /**
     * 工单等级
     */
    @ApiModelProperty("工单等级")
    private String ticketLevel;

    /**
     * 工单标题
     */
    @ApiModelProperty("工单标题")
    private String ticketTitle;

    /**
     * 工单内容
     */
    @ApiModelProperty("工单内容")
    private String ticketContent;

    /**
     * 提交人ID
     */
    @ApiModelProperty("提交人ID")
    private Long ticketUserId;

    /**
     * 处理人
     */
    @ApiModelProperty("处理人")
    private String allocationTicketUser;

    /**
     * 处理时间
     */
    @ApiModelProperty("处理时间")
    private Date allocationTicketTime;

    /**
     * 状态  01 未分配  02 处理中  03  已完成
     */
    @ApiModelProperty("状态  01 未分配  02 处理中  03  已完成")
    private String status;

    /**
     * 沟通记录
     */
    @ApiModelProperty("沟通记录")
    private String communicateRecord;

    /**
     * 分类名称
     */
    @ApiModelProperty("分类名称")
    private String ticketCategoryName;
    @ApiModelProperty("工单用户")
    private String ticketUser;
    @ApiModelProperty("工单附件")
    private List<AttachmentsVO> fileList = new ArrayList<>();
    @ApiModelProperty("工单记录")
    private List<RecordVO> recordList = new ArrayList<>();
    @ApiModelProperty("处理状态")
    private String dealStatus;

    @Override
    public String toString() {
        return ReflectionToStringBuilder.toString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
