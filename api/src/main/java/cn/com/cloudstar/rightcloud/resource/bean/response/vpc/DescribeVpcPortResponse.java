/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.bean.response.vpc;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 私有网络端口 出参
 *
 * <AUTHOR>
 */
@ApiModel(description = "私有网络端口")
@Setter
@Getter
public class DescribeVpcPortResponse implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    @ApiModelProperty(value = "id", name = "id", example = "123")
    private Long id;

    /**
     * uuid
     */
    @ApiModelProperty(value = "uuid", name = "uuid")
    private String uuid;

    /**
     * 端口名称
     */
    @ApiModelProperty(value = "端口名称", name = "portName")
    private String portName;

    /**
     * 固定ip
     */
    @ApiModelProperty(value = "固定ip", name = "fixedIp")
    private String fixedIp;

    /**
     * 连接设备
     */
    @ApiModelProperty(value = "连接设备", name = "device")
    private String device;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态", name = "status")
    private String status;

    /**
     * 设备标识
     */
    @ApiModelProperty(value = "设备标识", name = "deviceOwner")
    private String deviceOwner;

    /**
     * 设备名称
     */
    @ApiModelProperty("设备名称")
    private String deviceName;

    /**
     * 子网名称
     */
    @ApiModelProperty("子网名称")
    private String networkName;

    /**
     * 安全组名称
     */
    @ApiModelProperty("安全组名称")
    private String securityName;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createdDt;

    /**
     * mac地址
     */
    @ApiModelProperty(value = "mac地址", name = "macAddress")
    private String macAddress;

    /**
     * 私有网络id
     */
    @ApiModelProperty(value = "私有网络ID", name = "vpcId", example = "123")
    private Long vpcId;

    /**
     * 子网id
     */
    @ApiModelProperty(value = "子网ID", name = "subnetId", example = "123")
    private String subnetId;

    /**
     * 私有网络名字
     */
    @ApiModelProperty("私有网络名称")
    private String vpcName;

    /**
     * 安全组ID列表
     */
    @ApiModelProperty("安全组ID列表")
    private List<String> securityGroupIds;

    /**
     * 路由器id
     */
    @ApiModelProperty("路由器 ID")
    private String routerId;

    /**
     * 带宽
     */
    @ApiModelProperty("带宽")
    private Integer bandwidth;

    /**
     * 绑定实例ID
     */
    @ApiModelProperty("绑定实例ID")
    private String instanceId;

    /**
     * 绑定实例信息
     */
    @ApiModelProperty("绑定实例信息")
    private String instanceInfo;

    /**
     * 绑定数量
     */
    @ApiModelProperty("绑定数量")
    private Integer instanceNm;

    /**
     * 弹性ip地址
     */
    @ApiModelProperty("绑定浮动IP")
    private String floatingIpAddress;

    /**
     * 主网卡标识
     */
    @ApiModelProperty("主网卡标识")
    private String primaryInterface;

    /**
     * 用途
     */
    @ApiModelProperty("用途")
    private String purpose;

    /**
     * 绑定的虚拟IP
     */
    @ApiModelProperty("绑定的虚拟IP")
    private String virtualIps;


    @Override
    public String toString() {
        return ReflectionToStringBuilder.toString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
