/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.bean.request.firewall;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import cn.com.cloudstar.rightcloud.module.support.access.pojo.BaseRequest;
import lombok.EqualsAndHashCode;

/**
 * The type DescribeFirewallRequest.
 * <p>
 *
 * <AUTHOR>
 * @date 2019/8/19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "防火墙列表")
public class DescribeRelevantInfoRequest extends BaseRequest {

    /**
     * 云环境id
     */
    @ApiModelProperty("云环境ID")
    @NotNull
    private Long cloudEnvId;

    /**
     * 类型
     */
    @ApiModelProperty("类型，AvTemplate、ExceptionSignature、ExceptionApps")
    private String type;

    /**
     * 应用id
     */
    @ApiModelProperty("应用id")
    private String appId;

    /**
     * 应用英文名称
     */
    @ApiModelProperty("应用英文名称")
    private String appNameEn;

    /**
     * 应用中文名称
     */
    @ApiModelProperty("应用中文名称")
    private String appNameCn;

    /**
     * 病毒id
     */
    @ApiModelProperty("病毒id")
    private String signatureId;

    /**
     * 病毒名称
     */
    @ApiModelProperty("病毒名称")
    private String signatureName;

    /**
     * 病毒严重程度
     */
    @ApiModelProperty("病毒严重程度")
    private String signatureLevel;

}
