/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.bean.request;

import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.validation.constraints.NotBlank;
import javax.validation.groups.Default;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import cn.com.cloudstar.rightcloud.common.constants.res.type.KubernetesAuthType;
import cn.com.cloudstar.rightcloud.resource.bean.valid.ImportClusterByBasicValidView;
import cn.com.cloudstar.rightcloud.resource.bean.valid.ImportClusterByTokenValidView;

/**
 * The type ImportClusterRequest.
 * <p>
 *
 * <AUTHOR>
 * @date 2019/7/4
 */
@Getter
@Setter
@ApiModel(description = "导入集群")
public class ImportClusterRequest {

    /**
     * 名称
     */
    @NotBlank
    @ApiModelProperty(value = "名称", required = true)
    private String name;

    /**
     * 集群类型
     */
    @NotBlank
    @ApiModelProperty(value = "集群类型", example = "normal", required = true)
    private String type;

    /**
     * 当前创建或者接入的集群类型
     */
    @NotBlank
    @ApiModelProperty(value = "当前创建或者接入的集群类型", example = "kubernetes", required = true)
    private String clusterType;

    /**
     * 分类
     */
    @ApiModelProperty(value = "分类", example = "imported")
    private String category;

    /**
     * 认证方式
     */
    @NotBlank
    @ApiModelProperty(value = "认证方式", required = true)
    private String authType;

    /**
     * Dashboard地址
     */
    @NotBlank
    @ApiModelProperty(value = "Dashboard地址", required = true)
    private String dashboardUrl;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 集群版本
     */
    @NotBlank
    @ApiModelProperty(value = "集群版本", required = true)
    private String kubernetesVersion;

    /**
     * api
     */
    @NotBlank
    @ApiModelProperty(value = "API", required = true)
    private String api;

    /**
     * token
     */
    @NotBlank(groups = {ImportClusterByTokenValidView.class, ImportClusterByBasicValidView.class})
    @ApiModelProperty(value = "TOKEN", required = true)
    private String token;

    @JsonIgnore
    public Class<?> getValidView() {
        if (KubernetesAuthType.TOKEN.equals(this.authType)) {
            return ImportClusterByTokenValidView.class;
        }

        if (KubernetesAuthType.BASIC.equals(this.authType)) {
            return ImportClusterByBasicValidView.class;
        }

        return Default.class;
    }
}
