/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.bean.request.env;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import cn.com.cloudstar.rightcloud.module.support.access.pojo.BaseRequest;
import cn.com.cloudstar.rightcloud.validated.safe.SafeHtml;

import lombok.EqualsAndHashCode;

/**
 * The type DescribeCloudEnvAccountRequest.
 * <p>
 *
 * <AUTHOR>
 * @date 2019/7/30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "查询云环境账户")
public class DescribeCloudEnvAccountRequest extends BaseRequest {

    /**
     * 是否只查询公有云账户列表
     */
    @ApiModelProperty("是否只查询公有云账户列表")
    private boolean regionMgt;

    /**
     * 云环境类型
     */
    @ApiModelProperty("云环境类型")
    @SafeHtml
    private String cloudEnvType;

    /**
     * 是否查询已关联资源
     */
    @ApiModelProperty(value = "是否查询已关联资源")
    private Boolean allocQuery = true;
}
