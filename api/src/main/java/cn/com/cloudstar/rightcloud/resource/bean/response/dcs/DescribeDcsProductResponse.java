/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.bean.response.dcs;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import cn.com.cloudstar.rightcloud.resource.bean.model.DcsProductFlavorVO;

@Data
@ApiModel(description = "高速缓存产品")
public class DescribeDcsProductResponse implements Serializable {

    /**
     * 缓存引擎类型
     */
    @ApiModelProperty(value = "缓存引擎类型")
    private String engine;
    /**
     * 引擎版本
     */
    @ApiModelProperty(value = "支持的引擎版本号，以分号分割")
    private String engineVersions;
    /**
     * 货币单位
     */
    @ApiModelProperty(value = "货币单位")
    private String currency;
    /**
     * 有资源的可用区
     */
    @ApiModelProperty(value = "有资源的可用区")
    private List<DcsProductFlavorVO> flavors;
    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id")
    private String productId;
    /**
     * DCS的产品规格编码
     */
    @ApiModelProperty(value = "DCS的产品规格编码")
    private String specCode;
    /**
     * 缓存实例类型
     */
    @ApiModelProperty(value = "缓存实例类型")
    private String cacheMode;
    /**
     * 存实例的产品类型
     */
    @ApiModelProperty(value = "存实例的产品类型")
    private String productType;
    /**
     * CPU架构类型
     */
    @ApiModelProperty(value = "CPU架构类型")
    private String cpuType;
    /**
     * 存储类型
     */
    @ApiModelProperty(value = "存储类型")
    private String storageType;
    /**
     * 产品的规格大小
     */
    @ApiModelProperty(value = "产品的规格大小")
    private String specDetails;
    /**
     * DCS的规格详细信息
     */
    @ApiModelProperty(value = "DCS的规格详细信息")
    private String specDetails2;
    /**
     * 计费类型
     */
    @ApiModelProperty(value = "计费类型")
    private String chargingType;
    /**
     * 产品类型
     */
    @ApiModelProperty(value = "产品类型")
    private String prodType;
    /**
     * 云服务类型编码
     */
    @ApiModelProperty(value = "云服务类型编码")
    private String cloudServiceTypeCode;
    /**
     * 云资源类型编码
     */
    @ApiModelProperty(value = "云资源类型编码")
    private String cloudResourceTypeCode;
}
