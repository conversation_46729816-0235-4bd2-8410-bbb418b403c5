/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.bean.request.vm;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * DESC:${DESCRIPTION}
 *
 * <AUTHOR>
 * @date 2019-04-11 11:28
 */
@Getter
@Setter
@ApiModel(description = "标记虚拟机为模板")
public class MarkAsTemplateRequest {

    /**
     * 云环境id
     */
    @NotNull
    @ApiModelProperty(notes = "云环境 ID")
    private Long cloudEnvId;

    /**
     * 实例 ID
     */
    @NotBlank
    @ApiModelProperty(notes = "实例 ID")
    private String resVmId;

    /**
     * 模板名称
     */
    @NotBlank
    @ApiModelProperty(notes = "模板名称")
    private String templateName;

}
