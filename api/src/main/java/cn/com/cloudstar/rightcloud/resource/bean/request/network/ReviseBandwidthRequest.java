/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.bean.request.network;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * The type ReviseBandwidthRequest.
 * <p>
 *
 * <AUTHOR>
 * @date 2019/7/8
 */
@Getter
@Setter
@ApiModel(description = "修改弹性IP带宽")
public class ReviseBandwidthRequest {

    /**
     * id
     */
    @NotNull
    @ApiModelProperty(value = "弹性IP ID", required = true)
    private Long id;

    /**
     * uuid
     */
    @NotBlank
    @ApiModelProperty(value = "资源ID", required = true)
    private String uuid;

    /**
     * 带宽大小
     */
    @NotNull
    @ApiModelProperty(value = "带宽大小", required = true)
    private Integer bandWidth;

    /**
     * 带宽名称
     */
    @ApiModelProperty(value = "带宽名称", required = true)
    private String bandWidthName;

    /**
     * 云环境id
     */
    @NotNull
    @ApiModelProperty(value = "云环境ID", required = true)
    private Long envId;

    /**
     * 宽带计费方式
     */
    @ApiModelProperty(value = "宽带计费方式", name = "internetChargeType", example = "PayByBandwidth（按带宽计费）、PayByTraffic（按流量计费）")
    private String internetChargeType;

}
