package cn.com.cloudstar.rightcloud.resource.bean.request.hpc;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * CreateOffline
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CreateOffline {

    /**
     * 集群ID
     */
    private String clusterId;

    /**
     * 集群名称
     */
    private String clusterName;

    /**
     * 集群类型;专属：SAASPrivate, 共享：SAASShare
     */
    private String clusterType;

    /**
     * 集群类型;HA还是非HA：Paired高可用，Unpaired非高可用
     */
    private String scenario;

    /**
     * CPU规格
     */
    private String cpuSpecification;

    /**
     * GPU规格
     */
    private String gpuSpecification;

    /**
     * 集群标识;多租户下LDAP的集群区分标识，UUID
     */
    private String businessCategory;

    /**
     * HOME文件系统UUID
     */
    private String homeUuid;

    /**
     * DATA文件系统UUID
     */
    private String dataUuid;

    /**
     * 最大计算节点数量
     */
    private Integer maxComputeNodeNumber;

    /**
     * CLI内网IP地址
     */
    private String cliEip;

    /**
     * CLI内网IP端口
     */
    private String cliEipPort;

    /**
     * CLI外网IP地址
     */
    private String cliVip;

    /**
     * CLI外网IP端口
     */
    private String cliVipPort;

    /**
     * CLI数量
     */
    private Integer cliNum;

    /**
     * Agent数量
     */
    private Integer agentNum;

    /**
     * VNC数量
     */
    private Integer vncNum;

    /**
     * 备注
     */
    private String mark;


    /**
     * ldap-LDAP用户homeDirectory层级是否增加share(1为true，0为false)
     */
    private Integer ldapIsShare;
    /**
     * ldap-是否需要影子用户(1为true，0为false)
     */
    private Integer ldapShadowUser;
    /**
     * ldap-域名
     */
    private String ldapDomainName;
    /**
     * ldap-IP地址
     */
    private String ldapServerName;
    /**
     * 端口
     */
    private String ldapPort;
    /**
     * 用户名
     */
    private String ldapUserName;
    /**
     * ldap-密码
     */
    private String ldapPassword;
    /**
     * ldap-连接方式(1为安全连接，0为普通连接)
     */
    private String ldapConnectType;
    /**
     * ldap-证书
     */
    private String ldapCredentialsPath;


    /**
     * 基本配置-内网访问地址
     */
    private String dpEip;
    private String dpEipPort;
    /**
     * 基本配置-外网访问地址
     */
    private String dpVip;
    private String dpVipPort;
    /**
     * 基本配置-管理账号
     */
    private String username;
    /**
     * 基本配置-管理账号密码
     */
    private String password;


    /**
     * 数据信息-地址
     */
    private String dbAddress;
    /**
     * 数据信息-端口
     */
    private String dbPort;
    /**
     * 数据信息-数据库名
     */
    private String dbName;
    /**
     * 数据信息-用户名
     */
    private String dbUser;
    /**
     * 数据信息-密码
     */
    private String dbPassword;
    /**
     *
     */
    private String osArchitectureType;
}
