/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.bean.model;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 配置项拓展属性定义表，以实现配置项信息动态添加的作用
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2018-02-01
 */
@ApiModel(description = "配置项拓展属性")
public class ResEquipInfoDefVO {

    /**
     * 主键id
     */
    @ApiModelProperty(notes = "ID")
    private Long defSid;
    /**
     * 配置项类型 <br>包括：'H':机房,'C':机柜,'RC':机架,'F':刀箱,'S':服务器,'FW':防火墙,'SW':交换机,'LB':负载均衡器,'SAN':存储,'MIDDLEWARE':中间件
     */
    @ApiModelProperty(notes = "配置项类型,包括：'H':机房,'C':机柜,'RC':机架,'F':刀箱,'S':服务器,'FW':防火墙,'SW':交换机,'LB':负载均衡器,'SAN':存储,'MIDDLEWARE':中间件")
    private String equipType;
    /**
     * 属性中文名称
     */
    @ApiModelProperty(notes = "属性中文名称")
    private String attrName;
    /**
     * 属性i18n国际化编码
     */
    @ApiModelProperty(notes = "属性i18n国际化编码")
    private String attrNameI18n;
    /**
     * 属性英文名称
     */
    @ApiModelProperty(notes = "属性英文名称")
    private String attrKey;
    /**
     * 属性分区，用于分区域[组]展示
     */
    @ApiModelProperty(notes = "属性分区，用于分区域[组]展示")
    private String fieldSet;
    /**
     * 数据类型
     */
    @ApiModelProperty(notes = "数据类型")
    private String dataType;
    /**
     * 展示类型
     */
    @ApiModelProperty(notes = "展示类型")
    private String displayType;
    /**
     * 属性值单位
     */
    @ApiModelProperty(notes = "属性值单位")
    private String unit;
    /**
     * 属性值领域，包括：Rest:/url SysConfig:type or HardCode:{0:xxx,1:yyyy}
     */
    @ApiModelProperty(notes = "属性值领域，包括：Rest:/url SysConfig:type or HardCode:{0:xxx,1:yyyy}")
    private String valueDomain;
    /**
     * 取值增量
     */
    @ApiModelProperty(notes = "取值增量")
    private String valueIncrement;
    /**
     * 校验规则，形如：required;maxLength=30;email;mobile;ignoreSpecial
     */
    @ApiModelProperty(notes = "校验规则，形如：required;maxLength=30;email;mobile;ignoreSpecial")
    private String validateRule;
    /**
     * 显示顺序
     */
    @ApiModelProperty(notes = "显示顺序")
    private Integer sortRank;
    /**
     * 字段描述
     */
    @ApiModelProperty(notes = "字段描述")
    private String description;
    /**
     * 状态 0 不启用 1启用
     */
    @ApiModelProperty(notes = "状态 0 不启用 1启用")
    private Long status;

    private String createdBy;

    private Date createdDt;

    private String updatedBy;

    private Date updatedDt;

    private Long version;

    private String value;

    @ApiModelProperty("")
    @JsonIgnore
    private Integer max;

    @ApiModelProperty("")
    @JsonIgnore
    private Integer maxLength;

    @ApiModelProperty("")
    @JsonIgnore
    private Integer min;

    @ApiModelProperty("")
    @JsonIgnore
    private Integer minLength;

    @ApiModelProperty("")
    @JsonIgnore
    private String localVaildType;

    public Long getDefSid() {
        return defSid;
    }

    public void setDefSid(Long defSid) {
        this.defSid = defSid;
    }

    public String getEquipType() {
        return equipType;
    }

    public void setEquipType(String equipType) {
        this.equipType = equipType == null ? null : equipType.trim();
    }

    public String getAttrName() {
        return attrName;
    }

    public void setAttrName(String attrName) {
        this.attrName = attrName == null ? null : attrName.trim();
    }

    public String getAttrNameI18n() {
        return attrNameI18n;
    }

    public void setAttrNameI18n(String attrNameI18n) {
        this.attrNameI18n = attrNameI18n == null ? null : attrNameI18n.trim();
    }

    public String getAttrKey() {
        return attrKey;
    }

    public void setAttrKey(String attrKey) {
        this.attrKey = attrKey == null ? null : attrKey.trim();
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType == null ? null : dataType.trim();
    }

    public String getDisplayType() {
        return displayType;
    }

    public void setDisplayType(String displayType) {
        this.displayType = displayType == null ? null : displayType.trim();
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit == null ? null : unit.trim();
    }

    public String getValueDomain() {
        return valueDomain;
    }

    public void setValueDomain(String valueDomain) {
        this.valueDomain = valueDomain == null ? null : valueDomain.trim();
    }

    public String getValueIncrement() {
        return valueIncrement;
    }

    public void setValueIncrement(String valueIncrement) {
        this.valueIncrement = valueIncrement == null ? null : valueIncrement.trim();
    }

    public String getValidateRule() {
        return validateRule;
    }

    public void setValidateRule(String validateRule) {
        this.validateRule = validateRule == null ? null : validateRule.trim();
    }

    public Integer getSortRank() {
        return sortRank;
    }

    public void setSortRank(Integer sortRank) {
        this.sortRank = sortRank;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy == null ? null : createdBy.trim();
    }

    public Date getCreatedDt() {
        return createdDt;
    }

    public void setCreatedDt(Date createdDt) {
        this.createdDt = createdDt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy == null ? null : updatedBy.trim();
    }

    public Date getUpdatedDt() {
        return updatedDt;
    }

    public void setUpdatedDt(Date updatedDt) {
        this.updatedDt = updatedDt;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public String getFieldSet() {
        return fieldSet;
    }

    public void setFieldSet(String fieldSet) {
        this.fieldSet = fieldSet;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public Integer getMax() {
        return max;
    }

    public void setMax(Integer max) {
        this.max = max;
    }

    public Integer getMaxLength() {
        return maxLength;
    }

    public void setMaxLength(Integer maxLength) {
        this.maxLength = maxLength;
    }

    public Integer getMin() {
        return min;
    }

    public void setMin(Integer min) {
        this.min = min;
    }

    public Integer getMinLength() {
        return minLength;
    }

    public void setMinLength(Integer minLength) {
        this.minLength = minLength;
    }

    public String getLocalVaildType() {
        return localVaildType;
    }

    public void setLocalVaildType(String localVaildType) {
        this.localVaildType = localVaildType;
    }
}
