/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.bean.request.common;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 新增资源过期策略入参
 *
 * <AUTHOR>
 */
@ApiModel(description = "新增资源过期策略")
@Data
public class CreateExpireStrategyRequest {

    @ApiModelProperty(value = "类型名称")
    @NotBlank
    private String resourceName;

    @ApiModelProperty(value = "资源类型 vm:实例,disk:硬盘,floatingIp:弹性IP,sfInst:服务实例")
    @NotBlank
    private String resourceType;

    @ApiModelProperty(value = "通知方式 以逗号分隔,mail:邮件,sms:短信")
    @NotBlank
    private String noticeType;

    @ApiModelProperty(value = "提醒时间 提前几天提醒")
    @NotNull
    @Min(0)
    private Integer noticeTime;

    @ApiModelProperty(value = "提醒次数")
    @NotNull
    @Min(0)
    private Integer noticeCount;

    @ApiModelProperty(value = "提醒频率 h/次")
    @NotNull
    @Min(3)
    private Integer noticeFrequency;

    @ApiModelProperty(value = "过期处理 delete:删除,recycle:移入回收站,none:暂不处理")
    @NotBlank
    private String expireStrategy;

    @ApiModelProperty(value = "延迟时间 延迟多少天处理,0表示立即处理")
    @NotNull
    @Min(0)
    private Integer delayDay;

    @ApiModelProperty(value = "创建后默认启用，状态启用：true，禁用：false")
    @NotNull
    private Boolean status;

}
