/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.bean.request.vm;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import cn.com.cloudstar.rightcloud.resource.bean.model.CloneInstanceDataDiskVO;
import cn.com.cloudstar.rightcloud.resource.bean.model.CloneInstanceNicVO;
import cn.com.cloudstar.rightcloud.resource.bean.model.CloneInstanceSysDiskVO;
import cn.com.cloudstar.rightcloud.resource.bean.model.CloneInstanceVO;

/**
 * 克隆res vm请求
 * The type CloneResVmRequest.
 * <p>
 *
 * <AUTHOR>
 * @date 2019/7/9
 */
@Getter
@Setter
@ApiModel(description = "克隆实例")
public class CloneResVmRequest {

    /**
     * 分区ID
     */
    @NotNull
    @ApiModelProperty(value = "分区ID", required = true)
    private Long resPoolId;

    /**
     * 克隆源id
     */
    @NotBlank
    @ApiModelProperty(value = "克隆源ID", required = true)
    private String cloneSourceId;

    /**
     * 云环境id
     */
    @NotNull
    @ApiModelProperty(value = "云环境ID", required = true)
    private Long envId;

    /**
     * 数据盘信息
     */
    @ApiModelProperty(notes = "数据盘信息")
    private List<CloneInstanceDataDiskVO> dataDisk;

    /**
     * 系统盘信息
     */
    @ApiModelProperty(notes = "系统盘信息")
    private CloneInstanceSysDiskVO systemDisk;

    /**
     * 网卡信息
     */
    @ApiModelProperty(notes = "网卡信息")
    private List<CloneInstanceNicVO> nics;

    /**
     * 配置信息
     */
    @NotNull
    @ApiModelProperty(value = "配置信息", required = true)
    private CloneInstanceVO instance;
}
