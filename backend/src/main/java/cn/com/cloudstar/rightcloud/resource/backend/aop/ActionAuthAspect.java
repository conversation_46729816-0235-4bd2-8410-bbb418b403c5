/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.backend.aop;

import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.core.annotation.auth.auth.annotation.ActionAuth;
import cn.com.cloudstar.rightcloud.core.annotation.auth.auth.annotation.ActionParam;
import cn.com.cloudstar.rightcloud.core.annotation.auth.auth.annotation.JoinTable;
import cn.com.cloudstar.rightcloud.core.annotation.auth.auth.common.ActionAuthcException;
import cn.com.cloudstar.rightcloud.core.annotation.auth.constant.TableAlias;
import cn.com.cloudstar.rightcloud.core.annotation.auth.constant.TableEnum;
import cn.com.cloudstar.rightcloud.core.annotation.auth.dao.ActionAuthMapper;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicInfoUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.SimpleEvaluationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.ClassUtils;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.List;

/**
 * 资源Action 权限控制.
 *
 * @Description 与DataFilter的控制保持一致
 * <AUTHOR>
 * @Date 2020/4/8
 */
@Component
@Aspect
@Slf4j
public class ActionAuthAspect {

    private final static String EXPRESSION_PREFIX = "#";
    private final static String EXPRESSION_SPLIT = ".";

    private final ExpressionParser parser = new SpelExpressionParser();
    private final String[] alias = {"A", "B", "C", "D", "E", "F", "G"}; //表别名

    @Autowired
    private ActionAuthMapper actionAuthMapper;

    @Before(value = "@annotation(cn.com.cloudstar.rightcloud.core.annotation.auth.auth.annotation.ActionAuth) "
            + "&& @annotation(actionAuth)", argNames = "joinPoint,actionAuth")
    public void onExecute(JoinPoint joinPoint, ActionAuth actionAuth) {

        try {
            // 通过注解获取参数中的ID
            Method targetMethod = findTargetMethod(joinPoint);
            Parameter[] parameters = targetMethod.getParameters();
            String id = null;
            List idList = null;
            for (int i = 0; i < parameters.length; i++) {
                ActionParam actionParam = parameters[i].getAnnotation(ActionParam.class);
                if (actionParam != null) {
                    Object arg = joinPoint.getArgs()[i];
                    switch (actionParam.paramType()) {
                        case BASIC:
                            id = arg.toString();
                            checkActionAuth(actionAuth, id);
                            break;
                        case OBJECT:
                            id = getArg(arg, actionParam);
                            checkActionAuth(actionAuth, id);
                            break;
                        case LIST:
                            idList = getArgList(arg, actionParam);
                            if (!CollectionUtils.isEmpty(idList)) {
                                for (Object idStr : idList) {
                                    checkActionAuth(actionAuth, String.valueOf(idStr));
                                }
                            }
                            break;
                        case BASIC_LIST:
                        case SP_EL:
                            idList = getArgEL(arg, actionParam);
                            if (!CollectionUtils.isEmpty(idList)) {
                                for (Object idStr : idList) {
                                    checkActionAuth(actionAuth, String.valueOf(idStr));
                                }
                            }
                            break;
                        default:
                            break;
                    }
                    break;
                }
            }
        } catch (ActionAuthcException ex) {
            throw ex;
        } catch (Exception e) {
            log.warn("Auth Filter error: ", e);
        }
    }

    /**
     *
     * @param actionAuth
     * @param id
     */
    private void checkActionAuth(ActionAuth actionAuth, String id) {
        String tableName = actionAuth.table().value();
        String orgField = actionAuth.orgField();
        String tablePk = actionAuth.tablePk();
        TableAlias orgTableAlias = actionAuth.orgTableAlias();
        String userField = actionAuth.userField();

        // 如果资源本身不存在，则不做处理，留待业务层处理
        int withoutAuthCnt = actionAuthMapper.selectResCount(tableName, tablePk, id, StrUtil.EMPTY);
        if (withoutAuthCnt == 0) {
            return;
        }

        // 资源存在，且权限过滤不到，抛出异常
        String sqlFilter = BasicInfoUtil.getSQLFilter(orgTableAlias.value(), orgField, userField, false);
        int authCnt;
        if (TableEnum.VOID.equals(actionAuth.joinTable().table())) {
            if (actionAuth.joinTables().length == 0) {
                //单表查询
                authCnt = actionAuthMapper.selectResCount(tableName, tablePk, id, sqlFilter);
            } else {
                //三表及以上关联查询
                sqlFilter = BasicInfoUtil.getSQLFilter(alias[actionAuth.joinTables().length], orgField, userField,
                                                       false);
                authCnt = actionAuthMapper.selectResCountJoinTable(tableName, tablePk, id,
                                                                   getMultJoinTablesSQL(actionAuth.joinTables()),
                                                                   sqlFilter);
            }
        } else {
            //两表查询
            authCnt = actionAuthMapper.selectResCountJoinTable(tableName, tablePk, id,
                                                               getJoinTableSQL(actionAuth.joinTable()), sqlFilter);
        }
        if (authCnt == 0) {
            throw new ActionAuthcException(Strings.isNullOrEmpty(actionAuth.errorMessage()) ? WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2041878799)
                                                                                            : actionAuth.errorMessage());
        }
    }

    /**
     * @param joinTable
     */
    private String getJoinTableSQL(JoinTable joinTable) {
        return new StringBuilder().append(" INNER JOIN ")
                                  .append(joinTable.table().value())
                                  .append(" B")
                                  .append(" ON A.")
                                  .append(joinTable.mainTableField())
                                  .append(" = B.")
                                  .append(joinTable.joinField())
                                  .toString();
    }

    /**
     * 关联多表SQL
     * @param joinTables
     */
    private String getMultJoinTablesSQL(JoinTable[] joinTables) {
        StringBuilder stringBuilder = new StringBuilder();
        for (int index = 0; index < joinTables.length; index++) {
            JoinTable joinTable = joinTables[index];
            stringBuilder.append(" INNER JOIN ")
                         .append(joinTable.table().value())
                         .append(" ")
                         .append(alias[index + 1])
                         .append(" ON ")
                         .append(alias[index])
                         .append(".")
                         .append(joinTable.mainTableField())
                         .append(" = ")
                         .append(alias[index + 1])
                         .append(".")
                         .append(joinTable.joinField());
        }
        return stringBuilder.toString();
    }


    private Method findTargetMethod(JoinPoint jp) throws NoSuchMethodException {
        MethodSignature signature = (MethodSignature) jp.getSignature();
        Class<?>[] parameterTypes = signature.getParameterTypes();
        return ClassUtils.getUserClass(jp.getTarget()).getMethod(jp.getSignature().getName(), parameterTypes);
    }

    private String getArg(Object arg, ActionParam actionParam) {
        SimpleEvaluationContext context = SimpleEvaluationContext.forReadOnlyDataBinding().build();
        context.setVariable(actionParam.value(), arg);

        // Sp_EL: #arg.field
        Expression exp = parser.parseExpression(
                EXPRESSION_PREFIX + actionParam.value() + EXPRESSION_SPLIT + actionParam.idField());
        return String.valueOf(exp.getValue(context));
    }

    private List getArgList(Object arg, ActionParam actionParam) {
        SimpleEvaluationContext context = SimpleEvaluationContext.forReadOnlyDataBinding().build();
        context.setVariable(actionParam.value(), arg);

        // Sp_EL: #arg.field
        Expression exp = parser.parseExpression(
                EXPRESSION_PREFIX + actionParam.value() + EXPRESSION_SPLIT + actionParam.idField());
        return (List) exp.getValue(context);
    }

    private List getArgEL(Object arg, ActionParam actionParam) {
        SimpleEvaluationContext context = SimpleEvaluationContext.forReadOnlyDataBinding().build();
        context.setVariable(actionParam.value(), arg);

        // Sp_EL
        Expression exp = parser.parseExpression(actionParam.elExp());
        return exp.getValue(context, List.class);
    }
}
