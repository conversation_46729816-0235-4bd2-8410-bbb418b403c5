/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.adapter.facade.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import lombok.Data;

@Component
@Data
@PropertySource("classpath:interface.properties")
public class Conf {

    @Value("${env.json.path}")
    private String envJsonPath;

    @Value("${env.reg.queue}")
    private String envRegQueue;

}
