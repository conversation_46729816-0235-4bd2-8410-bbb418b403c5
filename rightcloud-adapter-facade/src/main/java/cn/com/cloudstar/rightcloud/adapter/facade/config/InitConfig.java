/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.adapter.facade.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.retry.annotation.RetryConfiguration;

/**
 * The type InitConfig.
 *
 * @Description
 * <AUTHOR>
 * @Date 2020/6/9
 */
@Configuration
public class InitConfig {

    @Bean
    public RetryConfiguration retryConfiguration() {
        return new RetryConfiguration();
    }

}
