/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.adapter.facade.message;


import brave.messaging.MessagingTracing;
import brave.spring.rabbit.SpringRabbitTracing;
import cn.com.cloudstar.rightcloud.adapter.core.MQHelper;
import cn.com.cloudstar.rightcloud.adapter.facade.config.Conf;
import cn.com.cloudstar.rightcloud.adapter.facade.config.EnvConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class Consumer {

    @Autowired
    private HeavyListener heavyListener;
    @Autowired
    private LightListener lightListener;
    @Autowired
    private SyncListener syncListener;
    @Autowired
    private RegisterListener registerListener;
    @Autowired
    private Conf conf;
    @Autowired
    private EnvConfiguration envConfiguration;
    @Autowired
    private SpringRabbitTracing springRabbitTracing;
    @Autowired
    private MessagingTracing messagingTracing;

    @PostConstruct
    public void start() {

        List<Map<String, String>> envs = null;
        try {
            envs = envConfiguration.jacksonRead();
        } catch (IOException e) {
            log.error(e.getMessage());
        }
        MQHelper.injectTemplate(springRabbitTracing,messagingTracing);
        if (envs != null) {
            for (Map<String, String> env : envs) {
                log.info("start listenr for virtual environment");
                MQHelper.startListenerForConsumer(env.get("env"),
                                                  env.get("uuid"),
                                                  "heavy",
                                                  heavyListener,
                                                  new Integer(env.get("heavy"))
                );
                MQHelper.startListenerForConsumer(env.get("env"),
                                                  env.get("uuid"),
                                                  "light",
                                                  lightListener,
                                                  new Integer(env.get("light"))
                );
                MQHelper.startListenerForConsumer(env.get("env"),
                                                  env.get("uuid"),
                                                  "sync",
                                                  syncListener,
                                                  new Integer(env.get("sync"))
                );
            }
        }
        MQHelper.startListenerForRegister(registerListener, conf.getEnvRegQueue());
    }
}
