/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.adapter.facade;

import cn.com.cloudstar.rightcloud.module.support.jasypt.configuration.detector.RightCloudEncryptablePropertyDetector;
import cn.com.cloudstar.rightcloud.module.support.jasypt.configuration.encryptor.DefaultEncryptor;
import cn.hutool.core.util.StrUtil;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;

import java.util.Map;

@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@ComponentScan(basePackages = {"cn.com.cloudstar.rightcloud"})
public class RightcloudAdapterApplication {

    public static void main(final String... args) {
        initCerVerify();
        SpringApplication.run(RightcloudAdapterApplication.class, args);
    }

    /**
     * 初始化CA证书验证
     */
    private static void initCerVerify() {
        String caTrustorePath = System.getenv("CA_TRUSTORE_PATH");
        String caTrustorePassword = System.getenv("CA_TRUSTORE_PASSWORD");

        // 验证 CA 是否合法
        if (StrUtil.isNotEmpty(caTrustorePath)) {
            System.setProperty("javax.net.ssl.trustStore", caTrustorePath);
            RightCloudEncryptablePropertyDetector propertyDetector = new RightCloudEncryptablePropertyDetector();
            DefaultEncryptor encryptor = new DefaultEncryptor(propertyDetector);
            if (propertyDetector.isEncrypted(caTrustorePassword)) {
                System.setProperty("javax.net.ssl.trustStorePassword", encryptor.decrypt(caTrustorePassword));
            } else {
                System.setProperty("javax.net.ssl.trustStorePassword", caTrustorePassword);
            }
        }

        // 验证 CRL
        Map<String, String> envMap = System.getenv();
        String sslCheckRevocation = envMap.getOrDefault("SSL_CHECK_REVOCATION", "false");
        if (StrUtil.isNotEmpty(sslCheckRevocation) && "true".equalsIgnoreCase(sslCheckRevocation)) {
            System.setProperty("com.sun.net.ssl.checkRevocation", "true");
            System.setProperty("com.sun.security.enableCRLDP", "true");
        }
    }
}
