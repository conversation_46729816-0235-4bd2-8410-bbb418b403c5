package cn.com.cloudstar.rightcloud.adapter.facade.log;


import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.PatternLayout;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.classic.spi.LoggingEvent;
import cn.com.cloudstar.rightcloud.module.support.access.util.JSONDesensitizationUtil;
import cn.com.cloudstar.rightcloud.module.support.access.util.PlaintextShieldUtil;
import org.apache.commons.lang3.StringEscapeUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.ResourceBundle;
import java.util.stream.Collectors;

/**
 * <Description> 过滤日志信息  <br>
 *
 * <AUTHOR>
 * @createDate 2022/9/14
 */
public class LogFilterPatternLayout extends PatternLayout {

    private static final String CONFIG = "logConfig";

    /**
     * 日志过滤配置
     * <h2>其内容不可随意删减</h2>
     */
    private static final String PLAINTEXT_PROPERTIES = "plaintext.properties";

    @Override
    public String doLayout(ILoggingEvent event) {
        String message = super.doLayout(event);
        // 比INFO日志级别高的直接返回
        if (Level.INFO.isGreaterOrEqual(event.getLevel())) {
            try {
                return processOne(event);
            } catch (Exception e) {
                // 这里不做任何操作,直接返回原来message
                return message;
            }
        } else {
            return message;
        }
    }

    private String processOne(ILoggingEvent event) {
        ResourceBundle bundle = ResourceBundle.getBundle(CONFIG);
        String plaintextProperties = bundle.getString(PLAINTEXT_PROPERTIES);
        String msg = JSONDesensitizationUtil.invokeMsg(event.getFormattedMessage(), plaintextProperties);
        // 去掉转义符
        msg = StringEscapeUtils.unescapeJava(msg);

        LoggingEvent loggingEvent = new LoggingEvent();
        loggingEvent.setLevel(event.getLevel());
        loggingEvent.setLoggerName(event.getLoggerName());
        loggingEvent.setMessage(msg);
        loggingEvent.setTimeStamp(event.getTimeStamp());
        loggingEvent.setThreadName(event.getThreadName());
        return super.doLayout(loggingEvent);
    }

    private String process(ILoggingEvent event) {
        List<Object> argumentArray = new ArrayList<>();
        ResourceBundle bundle = ResourceBundle.getBundle(CONFIG);
        String plaintextProperties = bundle.getString(PLAINTEXT_PROPERTIES);
        for (Object param : event.getArgumentArray()) {
            // 替换敏感字段
            argumentArray.add(PlaintextShieldUtil.eliminatePlaintext(param));
        }
        LoggingEvent loggingEvent = new LoggingEvent();
        loggingEvent.setLevel(event.getLevel());
        loggingEvent.setLoggerName(event.getLoggerName());
        loggingEvent.setMessage(event.getMessage());
        loggingEvent.setArgumentArray(argumentArray.toArray());
        loggingEvent.setTimeStamp(event.getTimeStamp());
        loggingEvent.setThreadName(event.getThreadName());

        return super.doLayout(loggingEvent);
    }
}
