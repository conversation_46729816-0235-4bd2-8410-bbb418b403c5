package cn.com.cloudstar.rightcloud.adapter.facade.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.i18n.AcceptHeaderLocaleResolver;

import java.util.Locale;

/**
 * 国际化配置
 * <AUTHOR>
 * @date 2024/4/19
 */
@Configuration
public class MyLocaleResolver {

    @Bean
    public LocaleResolver localeResolver() {
        AcceptHeaderLocaleResolver slr = new AcceptHeaderLocaleResolver();
        //设置默认语言，中文
        slr.setDefaultLocale(Locale.SIMPLIFIED_CHINESE);
        return slr;

    }
}
