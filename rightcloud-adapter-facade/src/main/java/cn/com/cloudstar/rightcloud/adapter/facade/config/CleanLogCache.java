/*
 * Copyright (c) 2018-2023. CloudStar.Co.Ltd. All rights reserved.
 */
package cn.com.cloudstar.rightcloud.adapter.facade.config;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import java.io.File;

/**
 * 清理日志缓存
 *
 * <AUTHOR>
 * @date 2023-07-26
 */
@EnableScheduling
@Configuration
@Slf4j
public class CleanLogCache {

    /**
     * 日志目录
     */
    @Value("${log.home}")
    private String logHome;

    /**
     * GZ文件后缀
     */
    private final static String GZ_FILE_SUFFIX = ".gz";

    /**
     * 清理日志缓存，每小时清理一次，半点执行
     */
    @Scheduled(cron = "0 30 * * * ?", zone = "GMT+8")
    public void cleanLogCache() {
        log.info("Start clean log cache.");
        File logPath = FileUtil.file(logHome);
        if (logPath != null && logPath.isDirectory()) {
            File[] files = FileUtil.ls(logHome);
            if (files != null && CollUtil.isNotEmpty(CollUtil.newArrayList(files))) {
                for (File file : files) {
                    if (file.getName().endsWith(GZ_FILE_SUFFIX)) {
                        log.info("Delete log cache file: {}", file.getName());
                        FileUtil.del(file);
                    }
                }
            }
        }
    }
}
