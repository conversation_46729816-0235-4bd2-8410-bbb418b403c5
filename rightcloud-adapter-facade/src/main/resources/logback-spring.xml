<?xml version="1.0" encoding="UTF-8"?>

<configuration scan="true" scanPeriod="30 seconds" debug="false">
    <springProperty scope="context" name="applicationName" source="spring.application.name"
        defaultValue="cmp-adapter"/>
    <!-- 日志滚动方式，动清理功能，配合 fluentd 的收集，保证日志缓存被定时清理-->
    <springProperty scope="context" name="LOG_HOME" source="log.home" defaultValue="/cmplog"/>
    <springProperty scope="context" name="ROOT_LOGGING_LEVEL" source="logging.level.root" defaultValue="info"/>

    <!-- 排除异常换行符以及不可见字符 -->
    <conversionRule conversionWord="nlf"
        converterClass="cn.com.cloudstar.rightcloud.common.security.EscapeCharThrowableConverter"/>

    <!-- 日志消息输出到控制台配置 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder charset="UTF-8">
            <pattern>[${applicationName}] %d{yyyy-MM-dd HH:mm:ss} [%X{traceId:-},%X{spanId:-}] %-5level %logger{36} - %replace(%msg){'\p{C}', ''} %nlf%n
            </pattern>
        </encoder>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="cn.com.cloudstar.rightcloud.adapter.facade.log.LogFilterPatternLayout">
                <pattern>[${applicationName}] %d{yyyy-MM-dd HH:mm:ss} [%X{traceId},%X{spanId}] [%X{account:-system}] %-5level %logger{36} - %replace(%msg){'\p{C}', ''} %nlf%n</pattern>
            </layout>
        </encoder>
    </appender>


    <appender name="INFO_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="cn.com.cloudstar.rightcloud.adapter.facade.log.LogFilterPatternLayout">
                <pattern>[${applicationName}] %d{yyyy-MM-dd HH:mm:ss} [%X{traceId},%X{spanId}] [%X{account:-system}] %-5level %logger{36} - %replace(%msg){'\p{C}', ''} %nlf%n</pattern>
            </layout>
        </encoder>
        <file>${LOG_HOME}/${applicationName}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <!--日志文件输出的文件名-->
            <FileNamePattern>${LOG_HOME}/${applicationName}.%i.gz</FileNamePattern>
            <MaxIndex>1</MaxIndex>
            <MinIndex>1</MinIndex>
        </rollingPolicy>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
            <pattern>[${applicationName}] %d{yyyy-MM-dd HH:mm:ss} [%X{traceId:-},%X{spanId:-}] %-5level %logger{36} - %replace(%msg){'\p{C}', ''} %nlf%n
            </pattern>
        </layout>
        <!--日志文件最大的大小-->
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <MaxFileSize>50MB</MaxFileSize>
        </triggeringPolicy>
    </appender>

    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <file>${LOG_HOME}/${applicationName}_error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <!--日志文件输出的文件名-->
            <FileNamePattern>${LOG_HOME}/${applicationName}_error.%i.gz</FileNamePattern>
            <MaxIndex>1</MaxIndex>
            <MinIndex>1</MinIndex>
        </rollingPolicy>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
            <!--排除不可见控制符，   - %replace(%msg){'\p{C}', ''} %nlf   请勿更改-->
            <pattern>[${applicationName}] %d{yyyy-MM-dd HH:mm:ss} [%X{traceId:-},%X{spanId:-}] %-5level %logger{36} - %replace(%msg){'\p{C}', ''} %nlf%n
            </pattern>
        </layout>
        <!--日志文件最大的大小-->
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <MaxFileSize>50MB</MaxFileSize>
        </triggeringPolicy>
    </appender>

    <!--相应模块日志输入等级，请勿随意更改-->
    <logger name="org.springframework" level="WARN"/>
    <logger name="io.netty" level="WARN"/>
    <logger name="com.alibaba.nacos.common.remote.client" level="OFF" />
    <logger name="org.apache.http.wire" level="OFF"/>
    <logger name="org.apache.http.headers" level="OFF"/>
    <logger name="org.apache.tomcat.util.http" level="OFF"/>
    <logger name="org.apache.coyote.http11.Http11InputBuffer" level="OFF"/>
    <logger name="org.mongodb.driver" level="WARN"/>
    <logger name="org.keycloak.adapters" level="INFO"/>
    <logger name="com.zaxxer.hikari.HikariConfig" level="INFO"/>
    <logger name="com.alibaba.dubbo" level="ERROR"/>
    <logger name="sun.net.www.protocol.http" level="OFF"/>
    <logger name="com.obs.services.internal.RestStorageService" level="WARN"/>
    <logger name="com.obs.services.AbstractClient" level="INFO"/>
    <logger name="com.obs.log.AccessLogger" level="ERROR"/>
    <logger name="org.apache.dubbo.remoting.transport.netty4" level="warn"/>
    <logger name="org.apache.dubbo.registry.support" level="warn"/>
    <logger name="org.apache.catalina.valves" level="warn"/>
    <logger name="org.apache.catalina.util" level="info"/>
    <logger name="org.apache.dubbo.registry" level="OFF"/>
    <logger name="org.springframework.amqp.rabbit.listener" level="info"/>

    <!-- 3rdparty Loggers -->
    <Logger name="org.apache.commons.beanutils" level="off" additivity="false"></Logger>
    <Logger name="redis.clients.jedis.Connection" level="off" additivity="false"></Logger>
    <Logger name="org.eclipse.jetty.io.WriteFlusher" level="off" additivity="false"></Logger>
    <Logger name="org.eclipse.jetty.io.ChannelEndPoint" level="off" additivity="false"></Logger>
    <Logger name="org.eclipse.jetty.io.ssl.SslConnection" level="off" additivity="false"></Logger>
    <Logger name="org.eclipse.jetty.client.http.HttpReceiverOverHTTP" level="off" additivity="false"></Logger>
    <Logger name="org.eclipse.jetty.http.HttpParser" level="off" additivity="false"></Logger>
    <Logger name="org.eclipse.jetty.http.HttpGenerator" level="off" additivity="false"></Logger>
    <Logger name="org.eclipse.jetty.client.HttpReceiver" level="off" additivity="false"></Logger>
    <Logger name="org.eclipse.jetty.client.HttpSender" level="off" additivity="false"></Logger>
    <logger name="org.springframework.core" level="off" additivity="false"></logger>
    <logger name="org.springframework.beans" level="off" additivity="false"></logger>
    <logger name="org.springframework.context" level="off" additivity="false"></logger>
    <logger name="org.springframework.web" level="off" additivity="false"></logger>
    <logger name="org.jboss.netty" level="off" additivity="false"></logger>
    <logger name="org.apache.http" level="off" additivity="false"></logger>
    <logger name="com.mchange.v2" level="off" additivity="false"></logger>
    <Logger name="redis.clients.jedis.Connection" level="off" additivity="false"></Logger>
    <Logger name="org.springframework.core.env.AbstractEnvironment" level="off" additivity="false"></Logger>
    <Logger name="org.springframework.core.env.StandardEnvironment" level="off" additivity="false"></Logger>
    <Logger name="org.springframework.scheduling.quartz.SchedulerFactoryBean" level="off" additivity="false"></Logger>
    <Logger name="org.springframework.core.env.PropertySourcesPropertyResolver" level="off" additivity="false"></Logger>
    <Logger name="org.springframework.util.PropertyPlaceholderHelper" level="off" additivity="false"></Logger>
    <Logger name="org.apache.coyote.http11.InternalNioInputBuffer" level="off" additivity="false"></Logger>
    <Logger name="org.apache.coyote.http11" level="off" additivity="false"></Logger>
    <Logger name="org.apache.tomcat.util.http.Cookies" level="off" additivity="false"></Logger>
    <Logger name="org.apache.tomcat.util.http.Parameters" level="off" additivity="false"></Logger>
    <Logger name="org.apache.tomcat.util.net.NioEndpoint" level="off" additivity="false"></Logger>
    <Logger name="org.apache.http.headers" level="off" additivity="false"></Logger>
    <Logger name="org.apache.http.wire" level="off" additivity="false"></Logger>
    <Logger name="org.apache.tomcat.util.http.LegacyCookieProcessor" level="off" additivity="false"></Logger>
    <Logger name="org.apache.hc.client5" level="OFF" additivity="false"/>
    <logger name="cn.com.cloudstar.rightcloud" level="INFO" additivity="false">
        <appender-ref ref="INFO_FILE"/>
        <appender-ref ref="ERROR_FILE"/>
        <appender-ref ref="STDOUT"/>
    </logger>

    <!--输出到日志文件-->
    <root level="INFO">
        <appender-ref ref="ERROR_FILE"/>
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="STDOUT"/>
    </root>

</configuration>
