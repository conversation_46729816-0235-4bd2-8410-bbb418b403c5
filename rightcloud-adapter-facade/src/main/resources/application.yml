spring:
  profiles: cloudstar
base-name: ${cloudstar.server.name:cmp-adapter}
fluentd-host: ${cloudstar.fluentd.host:**********}
fluentd-port: ${cloudstar.fluentd.port:24224}
fluentd-enable: ${cloudstar.fluentd.enable:true}
base-mgr-port: ${cloudstar.server.mgr.port:8081}
base-port: ${cloudstar.server.port:8888}

base-mq-host: ${cloudstar.mq.host:127.0.0.1}
base-mq-port: ${cloudstar.mq.port:5672}
base-mq-username: ${cloudstar.mq.username:admin}
base-mq-password: ${mq.password}
base-mq-ssl-enabled: ${cloudstar.mq.ssl.enabled:false}
base-mq-ssl-key-store: ${cloudstar.mq.ssl.keyStore:classpath:mq/server.p12}
base-mq-ssl-key-store-password: ${mq.key.store.password}
base-mq-ssl-trust-store: ${cloudstar.mq.ssl.trustStore:classpath:mq/server.jks}
base-mq-ssl-trust-store-password: ${mq.trust.store.password}
base-mq-ssl-algorithm: ${cloudstar.mq.ssl.algorithm:TLSv1.2}

base-redis-host: ${cloudstar.redis.host:127.0.0.1}
base-redis-port: ${cloudstar.redis.port:6379}
base-redis-password: ${redis.password}
root-logging-level: ${cloudstar.logging.level:info}
cloudstar:
  db:
    password: ${db.password}
---

spring:
  application:
    name: ${base-name}
  aop:
    proxy-target-class: true
  rabbitmq:
    host: ${base-mq-host}
    port: ${base-mq-port}
    username: ${base-mq-username}
    password: ${base-mq-password}
    requested-heartbeat: 60s
    ssl:
      enabled: ${base-mq-ssl-enabled}
      key-store: ${base-mq-ssl-key-store}
      key-store-password: ${base-mq-ssl-key-store-password}
      trust-store: ${base-mq-ssl-trust-store}
      trust-store-password: ${base-mq-ssl-trust-store-password}
      algorithm: ${base-mq-ssl-algorithm}
      validate-server-certificate: true
      verify-hostname: false
  redis:
    host: ${base-redis-host}
    port: ${base-redis-port}
    password: ${base-redis-password}
    jedis:
      pool:
        max-active: 32
        max-idle: 32
        min-idle: 1
  sleuth:
    async:
      enabled: true
    messaging:
      rabbit:
        remote-service-name: "rabbitMqRPC"
        enabled: true
---

mq-log-exchange: log_exchange
mq-log-queue: log_queue
mq-log-routingKey: log.#

server:
  port: ${base-port:8888}
  undertow:
    io-threads: 8
    worker-threads: 64
    direct-buffers: true
  servlet:
    context-path: /
  #证书相关配置，请勿更改
  ssl:
    enabled: ${cloudstar.gateway.ssl.enable:false}
    enabled-protocols:
      - TLSv1.2
    ciphers:
      - TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384
      - TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384
      - TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256
      - TLS_ECDH_ECDSA_WITH_AES_256_GCM_SHA384
      - TLS_ECDH_RSA_WITH_AES_256_GCM_SHA384
      - TLS_ECDH_ECDSA_WITH_AES_128_GCM_SHA256
      - TLS_ECDH_RSA_WITH_AES_128_GCM_SHA256
      - TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256
    protocol: TLS
    key-store-type: ${cloudstar.keystore.type:JKS}
    key-store: ${cloudstar.keystore:classpath:server.jks}
    key-password: ${cmp.adapter.keystore.password}
    key-store-password: ${cmp.adapter.keystore.password}
  address: ${cloudstar.local.podip:0.0.0.0}

management:
  server:
    port: ${base-mgr-port:8081}
    servlet:
      context-path: /
    #证书相关配置，请勿更改
    ssl:
      enabled: ${cloudstar.gateway.ssl.enable:false}
      enabled-protocols:
        - TLSv1.2
      ciphers:
        - TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384
        - TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384
        - TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256
        - TLS_ECDH_ECDSA_WITH_AES_256_GCM_SHA384
        - TLS_ECDH_RSA_WITH_AES_256_GCM_SHA384
        - TLS_ECDH_ECDSA_WITH_AES_128_GCM_SHA256
        - TLS_ECDH_RSA_WITH_AES_128_GCM_SHA256
        - TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256
      protocol: TLS
      key-store-type: ${cloudstar.keystore.type:JKS}
      key-store: ${cloudstar.keystore:classpath:server.jks}
      key-password: ${cmp.adapter.keystore.password}
      key-store-password: ${cmp.adapter.keystore.password}
    address: ${cloudstar.local.podip:0.0.0.0}
  health:
    mongo:
      enabled: false
  endpoint:
    health:
      show-details: always
      status:
        order: up,down
        http-mapping:
          down: 500
  endpoints:
    metrics:
      enabled: true
      tags:
        application: ${spring.application.name}
      export:
        prometheus:
          enabled: true
    web:
      base-path: /
      path-mapping:
        health: healthz
        prometheus: metrics
        metrics: bootmetrics
      exposure:
        include: health,metrics,prometheus
      cors:
        allowed-origins: "*"
        allowed-headers: "*"
        allowed-methods:
          - "GET"

#fluentd数据库配置
fluentd:
  host: ${fluentd-host}
  port: ${fluentd-port}
  enable: ${fluentd-enable}

#日志打印等级
#不可随意更改，更改需请示负责人
logging:
  level:
    root: ${root-logging-level:info}
    cn.com.cloudstar.rightcloud: info
    org.springframework: warn
    org.apache.http: OFF
    org.apache.http.headers: OFF
    org.apache.tomcat.util.http: OFF
    org.apache.http.wire: OFF
    org.apache.coyote.http11.Http11InputBuffer: OFF
    sun.net.www.protocol.http: OFF
    reactor.netty.http: info
    org.mongodb.driver: warn
    com.zaxxer.hikari.HikariConfig: info
    com.alibaba.dubbo: error
    com.obs.services.internal.RestStorageService: warn
    com.obs.services.AbstractClient: INFO
    com.obs.log.AccessLogger: ERROR
    org.apache.dubbo.remoting.transport.netty4: warn
    org.apache.dubbo.registry.support: warn
    org.apache.catalina.valves: warn
    org.apache.catalina.util: INFO
    org.apache.dubbo.registry: OFF
    org.springframework.amqp.rabbit.listener: INFO
    org.apache.commons.beanutils: OFF
    redis.clients.jedis.Connection: OFF
    org.eclipse.jetty.io.WriteFlusher: OFF
    org.eclipse.jetty.io.ChannelEndPoint: OFF
    org.eclipse.jetty.io.ssl.SslConnection: OFF
    org.eclipse.jetty.client.http.HttpReceiverOverHTTP: OFF
    org.eclipse.jetty.http.HttpParser: OFF
    org.eclipse.jetty.http.HttpGenerator: OFF
    org.eclipse.jetty.client.HttpReceiver: OFF
    org.eclipse.jetty.client.HttpSender: OFF
    org.springframework.core: OFF
    org.springframework.beans: OFF
    org.springframework.context: OFF
    org.springframework.web: OFF
    org.jboss.netty: OFF
    com.mchange.v2: OFF
    org.springframework.core.env.AbstractEnvironment: OFF
    org.springframework.core.env.StandardEnvironment: OFF
    org.springframework.scheduling.quartz.SchedulerFactoryBean: OFF
    org.springframework.core.env.PropertySourcesPropertyResolver: OFF
    org.springframework.util.PropertyPlaceholderHelper: OFF
    org.apache.coyote.http11.InternalNioInputBuffer: OFF
    org.apache.coyote.http11: OFF
    org.apache.tomcat.util.http.Cookies: OFF
    org.apache.tomcat.util.http.Parameters: OFF
    org.apache.tomcat.util.net.NioEndpoint: OFF
    org.apache.tomcat.util.http.LegacyCookieProcessor: OFF
    org.apache.hc.client5: OFF
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%X{traceId},%X{spanId}] [%X{account:-system}] %-5level %logger{36} - %msg%n"


# 日志目录
log:
  home: ${cloudstar.logging.home:/cmplog}
