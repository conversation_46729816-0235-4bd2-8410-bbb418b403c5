[{"description": "微型实例是低成本的实例，可提供少量的 CPU 资源。微型实例很适合吞吐量较低的应用程序和需要定期增加计算周期的网站，但不适合需要持久 CPU 性能的应用程序。微型实例的常见用途包含: 低流量网站或博客、较小的管理应用程序、堡垒主机和开发 EC2 功能的免费试用。", "name": "微型实例", "types": [{"architectures": ["x86_64", "i386"], "cpu": {"cores": 1, "units": "最多 2 个"}, "description": "T1 微型", "ebsEncryptionSupported": false, "ebsOnly": true, "family": "微型实例", "freeTierEligible": true, "ipv6Support": false, "legacy": true, "memory": 0.613, "networkPerformance": "Very Low", "physicalProcessor": "Variable", "typeName": "t1.micro", "virtualizationTypes": ["paravirtual"], "vpc": true, "windows": true}]}, {"description": "通用型实例可实现计算、内存和网络资源的平衡，是很多应用程序的良好选择。建议将通用型实例用于小型和中型数据库、需要附加内存和缓存集群的数据处理作业，以及运行 SAP 的后端服务器、Microsoft SharePoint 和其他企业应用程序。", "name": "通用型", "types": [{"architectures": ["x86_64", "i386"], "cpu": {"cores": 1, "units": "变量"}, "description": "T2 微型", "ebsEncryptionSupported": true, "ebsOnly": true, "family": "通用型", "freeTierEligible": true, "ipv6Support": true, "memory": 1, "networkPerformance": "Low to Moderate", "physicalProcessor": "Intel Xeon Family", "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.5, "spotSupported": false, "typeName": "t2.micro", "virtualizationTypes": ["hvm"], "vpc": true, "vpcOnly": true, "windows": true}, {"architectures": ["x86_64", "i386"], "cpu": {"cores": 1, "units": "变量"}, "description": "T2 小型", "ebsEncryptionSupported": true, "ebsOnly": true, "family": "通用型", "ipv6Support": true, "memory": 2, "networkPerformance": "Low to Moderate", "physicalProcessor": "Intel Xeon Family", "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.5, "spotSupported": false, "typeName": "t2.small", "virtualizationTypes": ["hvm"], "vpc": true, "vpcOnly": true, "windows": true}, {"architectures": ["x86_64", "i386"], "cpu": {"cores": 2, "units": "变量"}, "description": "T2 中型", "ebsEncryptionSupported": true, "ebsOnly": true, "family": "通用型", "ipv6Support": true, "memory": 4, "networkPerformance": "Low to Moderate", "physicalProcessor": "Intel Xeon Family", "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.5, "spotSupported": false, "typeName": "t2.medium", "virtualizationTypes": ["hvm"], "vpc": true, "vpcOnly": true, "windows": true}, {"architectures": ["x86_64"], "cpu": {"cores": 2, "units": "变量"}, "description": "T2 大型", "ebsEncryptionSupported": true, "ebsOnly": true, "family": "通用型", "ipv6Support": true, "memory": 8, "networkPerformance": "Low to Moderate", "physicalProcessor": "Intel Xeon Family", "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.4, "spotSupported": false, "typeName": "t2.large", "virtualizationTypes": ["hvm"], "vpc": true, "vpcOnly": true, "windows": true}, {"architectures": ["x86_64"], "cpu": {"cores": 4, "units": "变量"}, "description": "T2 超大型", "ebsEncryptionSupported": true, "ebsOnly": true, "family": "通用型", "ipv6Support": true, "memory": 16, "networkPerformance": "Moderate", "physicalProcessor": "Intel Xeon Family", "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.4, "spotSupported": false, "typeName": "t2.xlarge", "virtualizationTypes": ["hvm"], "vpc": true, "vpcOnly": true, "windows": true}, {"architectures": ["x86_64"], "cpu": {"cores": 8, "units": "变量"}, "description": "T2 双倍超大型", "ebsEncryptionSupported": true, "ebsOnly": true, "family": "通用型", "ipv6Support": true, "memory": 32, "networkPerformance": "Moderate", "physicalProcessor": "Intel Xeon Family", "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.4, "spotSupported": false, "typeName": "t2.2xlarge", "virtualizationTypes": ["hvm"], "vpc": true, "vpcOnly": true, "windows": true}, {"architectures": ["x86_64"], "cpu": {"cores": 2, "units": "6.5"}, "dedicatedHostsSupported": true, "description": "M4 大型", "dual": true, "ebsEncryptionSupported": true, "ebsOnly": true, "ebsOptimizedByDefault": true, "ebsOptimizedSupported": true, "family": "通用型", "ipv6Support": true, "memory": 8, "networkPerformance": "Moderate", "physicalProcessor": "Intel Xeon E5-2676v3", "placementGroupsSupported": true, "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.4, "spotSupported": true, "typeName": "m4.large", "virtualizationTypes": ["hvm"], "vpc": true, "vpcOnly": true, "windows": true}, {"architectures": ["x86_64"], "cpu": {"cores": 4, "units": "13"}, "dedicatedHostsSupported": true, "description": "M4 超大型", "dual": true, "ebsEncryptionSupported": true, "ebsOnly": true, "ebsOptimizedByDefault": true, "ebsOptimizedSupported": true, "family": "通用型", "ipv6Support": true, "memory": 16, "networkPerformance": "High", "physicalProcessor": "Intel Xeon E5-2676v3", "placementGroupsSupported": true, "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.4, "spotSupported": true, "typeName": "m4.xlarge", "virtualizationTypes": ["hvm"], "vpc": true, "vpcOnly": true, "windows": true}, {"architectures": ["x86_64"], "cpu": {"cores": 8, "units": "26"}, "dedicatedHostsSupported": true, "description": "M4 双倍超大型", "dual": true, "ebsEncryptionSupported": true, "ebsOnly": true, "ebsOptimizedByDefault": true, "ebsOptimizedSupported": true, "family": "通用型", "ipv6Support": true, "memory": 32, "networkPerformance": "High", "physicalProcessor": "Intel Xeon E5-2676v3", "placementGroupsSupported": true, "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.4, "spotSupported": true, "typeName": "m4.2xlarge", "virtualizationTypes": ["hvm"], "vpc": true, "vpcOnly": true, "windows": true}, {"architectures": ["x86_64"], "cpu": {"cores": 16, "units": "53.5"}, "dedicatedHostsSupported": true, "description": "M4 四倍超大型", "dual": true, "ebsEncryptionSupported": true, "ebsOnly": true, "ebsOptimizedByDefault": true, "ebsOptimizedSupported": true, "family": "通用型", "ipv6Support": true, "memory": 64, "networkPerformance": "High", "physicalProcessor": "Intel Xeon E5-2676v3", "placementGroupsSupported": true, "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.4, "spotSupported": true, "typeName": "m4.4xlarge", "virtualizationTypes": ["hvm"], "vpc": true, "vpcOnly": true, "windows": true}, {"architectures": ["x86_64"], "cpu": {"cores": 40, "units": "124.5"}, "dedicatedHostsSupported": true, "description": "M4 十倍超大型", "dual": true, "ebsEncryptionSupported": true, "ebsOnly": true, "ebsOptimizedByDefault": true, "ebsOptimizedSupported": true, "family": "通用型", "ipv6Support": true, "memory": 160, "networkPerformance": "10 Gigabit", "physicalProcessor": "Intel Xeon E5-2676v3", "placementGroupsSupported": true, "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.4, "spotSupported": true, "typeName": "m4.10xlarge", "virtualizationTypes": ["hvm"], "vpc": true, "vpcOnly": true, "windows": true}, {"amiFlags": [{"name": "ENA", "supported": true}], "architectures": ["x86_64"], "cpu": {"cores": 64, "units": "188"}, "dedicatedHostsSupported": false, "description": "M4 十六倍超大型", "dual": true, "ebsEncryptionSupported": true, "ebsOnly": true, "ebsOptimizedByDefault": true, "ebsOptimizedSupported": true, "family": "通用型", "ipv6Support": true, "memory": 256, "networkPerformance": "25 Gigabit", "physicalProcessor": "Intel Xeon E5-2686v4", "placementGroupsSupported": true, "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.3, "spotSupported": true, "typeName": "m4.16xlarge", "virtualizationTypes": ["hvm"], "vpc": true, "vpcOnly": true, "windows": true}, {"architectures": ["x86_64"], "cpu": {"cores": 1, "units": "3"}, "dedicatedHostsSupported": true, "description": "M3 中型", "dual": true, "ebsEncryptionSupported": true, "ebsOnly": false, "family": "通用型", "ipv6Support": false, "memory": 3.75, "networkPerformance": "Moderate", "physicalProcessor": "Intel Xeon E5-2670v2", "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.5, "storage": {"count": 1, "size": 4, "ssd": true}, "typeName": "m3.medium", "virtualizationTypes": ["paravirtual", "hvm"], "vpc": true, "windows": true}, {"architectures": ["x86_64"], "cpu": {"cores": 2, "units": "6.5"}, "dedicatedHostsSupported": true, "description": "M3 大型", "dual": true, "ebsEncryptionSupported": true, "ebsOnly": false, "ebsOptimizedSupported": false, "family": "通用型", "ipv6Support": false, "memory": 7.5, "networkPerformance": "Moderate", "physicalProcessor": "Intel Xeon E5-2670v2", "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.5, "storage": {"count": 1, "size": 32, "ssd": true}, "typeName": "m3.large", "virtualizationTypes": ["paravirtual", "hvm"], "vpc": true, "windows": true}, {"architectures": ["x86_64"], "cpu": {"cores": 4, "units": "13"}, "dedicatedHostsSupported": true, "description": "M3 超大型", "dual": true, "ebsEncryptionSupported": true, "ebsOnly": false, "ebsOptimizedSupported": true, "family": "通用型", "ipv6Support": false, "memory": 15, "networkPerformance": "High", "physicalProcessor": "Intel Xeon E5-2670v2", "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.5, "storage": {"count": 2, "size": 40, "ssd": true}, "typeName": "m3.xlarge", "virtualizationTypes": ["paravirtual", "hvm"], "vpc": true, "windows": true}, {"architectures": ["x86_64"], "cpu": {"cores": 8, "units": "26"}, "dedicatedHostsSupported": true, "description": "M3 双倍超大型", "dual": true, "ebsEncryptionSupported": true, "ebsOnly": false, "ebsOptimizedSupported": true, "family": "通用型", "ipv6Support": false, "memory": 30, "networkPerformance": "High", "physicalProcessor": "Intel Xeon E5-2670v2", "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.5, "storage": {"count": 2, "size": 80, "ssd": true}, "typeName": "m3.2xlarge", "virtualizationTypes": ["paravirtual", "hvm"], "vpc": true, "windows": true}, {"architectures": ["x86_64", "i386"], "cpu": {"cores": 1, "units": "1"}, "description": "M1 小型", "ebsEncryptionSupported": false, "family": "通用型", "ipv6Support": false, "legacy": true, "memory": 1.7, "networkPerformance": "Low", "physicalProcessor": "Intel Xeon Family", "riNotOfferedInRegions": ["cn-north-1", "cn-northwest-1"], "storage": {"count": 1, "size": 160, "ssd": false}, "typeName": "m1.small", "virtualizationTypes": ["paravirtual"], "vpc": true, "windows": true}]}, {"description": "计算优化型实例的 vCPU 与内存比率比其他系列高，且每个 vCPU 的成本是所有 Amazon EC2 实例类型中最低的。我们建议用计算优化型实例运行 CPU 限制型横向扩展应用程序。此类应用的示例包括，高流量前端集群、按需批量处理、分布式分析、Web 服务器、批量处理以及高性能科学与工程应用程序。", "name": "计算优化", "types": [{"architectures": ["x86_64"], "cpu": {"cores": 2, "units": "8"}, "dedicatedHostsSupported": true, "description": "C4 大型", "ebsEncryptionSupported": true, "ebsOnly": true, "ebsOptimizedByDefault": true, "ebsOptimizedSupported": true, "family": "计算优化", "ipv6Support": true, "memory": 3.75, "networkPerformance": "Moderate", "physicalProcessor": "Intel Xeon E5-2666v3", "placementGroupsSupported": true, "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.9, "typeName": "c4.large", "virtualizationTypes": ["hvm"], "vpc": true, "vpcOnly": true, "windows": true}, {"architectures": ["x86_64"], "cpu": {"cores": 4, "units": "16"}, "dedicatedHostsSupported": true, "description": "C4 超大型", "ebsEncryptionSupported": true, "ebsOnly": true, "ebsOptimizedByDefault": true, "ebsOptimizedSupported": true, "family": "计算优化", "ipv6Support": true, "memory": 7.5, "networkPerformance": "High", "physicalProcessor": "Intel Xeon E5-2666v3", "placementGroupsSupported": true, "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.9, "typeName": "c4.xlarge", "virtualizationTypes": ["hvm"], "vpc": true, "vpcOnly": true, "windows": true}, {"architectures": ["x86_64"], "cpu": {"cores": 8, "units": "31"}, "dedicatedHostsSupported": true, "description": "C4 双倍超大型", "ebsEncryptionSupported": true, "ebsOnly": true, "ebsOptimizedByDefault": true, "ebsOptimizedSupported": true, "family": "计算优化", "ipv6Support": true, "memory": 15, "networkPerformance": "High", "physicalProcessor": "Intel Xeon E5-2666v3", "placementGroupsSupported": true, "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.9, "typeName": "c4.2xlarge", "virtualizationTypes": ["hvm"], "vpc": true, "vpcOnly": true, "windows": true}, {"architectures": ["x86_64"], "cpu": {"cores": 16, "units": "62"}, "dedicatedHostsSupported": true, "description": "C4 四倍超大型", "ebsEncryptionSupported": true, "ebsOnly": true, "ebsOptimizedByDefault": true, "ebsOptimizedSupported": true, "family": "计算优化", "ipv6Support": true, "memory": 30, "networkPerformance": "High", "physicalProcessor": "Intel Xeon E5-2666v3", "placementGroupsSupported": true, "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.9, "typeName": "c4.4xlarge", "virtualizationTypes": ["hvm"], "vpc": true, "vpcOnly": true, "windows": true}, {"architectures": ["x86_64"], "cpu": {"cores": 36, "units": "132"}, "dedicatedHostsSupported": true, "description": "C4 八倍超大型", "ebsEncryptionSupported": true, "ebsOnly": true, "ebsOptimizedByDefault": true, "ebsOptimizedSupported": true, "family": "计算优化", "ipv6Support": true, "memory": 60, "networkPerformance": "10 Gigabit", "physicalProcessor": "Intel Xeon E5-2666v3", "placementGroupsSupported": true, "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.9, "typeName": "c4.8xlarge", "virtualizationTypes": ["hvm"], "vpc": true, "vpcOnly": true, "windows": true}, {"architectures": ["x86_64", "i386"], "cpu": {"cores": 2, "units": "7"}, "dedicatedHostsSupported": true, "description": "C3 大型", "ebsEncryptionSupported": true, "ebsOptimizedSupported": false, "family": "计算优化", "ipv6Support": true, "memory": 3.75, "networkPerformance": "Moderate", "physicalProcessor": "Intel Xeon E5-2680v2", "placementGroupsSupported": true, "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.8, "storage": {"count": 2, "size": 16, "ssd": true}, "typeName": "c3.large", "virtualizationTypes": ["paravirtual", "hvm"], "vpc": true, "windows": true}, {"architectures": ["x86_64"], "cpu": {"cores": 4, "units": "14"}, "dedicatedHostsSupported": true, "description": "C3 超大型", "ebsEncryptionSupported": true, "ebsOptimizedSupported": true, "family": "计算优化", "ipv6Support": true, "memory": 7.5, "networkPerformance": "Moderate", "physicalProcessor": "Intel Xeon E5-2680v2", "placementGroupsSupported": true, "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.8, "storage": {"count": 2, "size": 40, "ssd": true}, "typeName": "c3.xlarge", "virtualizationTypes": ["paravirtual", "hvm"], "vpc": true, "windows": true}, {"architectures": ["x86_64"], "cpu": {"cores": 8, "units": "28"}, "dedicatedHostsSupported": true, "description": "C3 双倍超大型", "ebsEncryptionSupported": true, "ebsOptimizedSupported": true, "family": "计算优化", "ipv6Support": true, "memory": 15, "networkPerformance": "High", "physicalProcessor": "Intel Xeon E5-2680v2", "placementGroupsSupported": true, "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.8, "storage": {"count": 2, "size": 80, "ssd": true}, "typeName": "c3.2xlarge", "virtualizationTypes": ["paravirtual", "hvm"], "vpc": true, "windows": true}, {"architectures": ["x86_64"], "cpu": {"cores": 16, "units": "55"}, "dedicatedHostsSupported": true, "description": "C3 四倍超大型", "ebsEncryptionSupported": true, "ebsOptimizedSupported": true, "family": "计算优化", "ipv6Support": true, "memory": 30, "networkPerformance": "High", "physicalProcessor": "Intel Xeon E5-2680v2", "placementGroupsSupported": true, "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.8, "storage": {"count": 2, "size": 160, "ssd": true}, "typeName": "c3.4xlarge", "virtualizationTypes": ["paravirtual", "hvm"], "vpc": true, "windows": true}, {"architectures": ["x86_64"], "cpu": {"cores": 32, "units": "108"}, "dedicatedHostsSupported": true, "description": "C3 八倍超大型", "ebsEncryptionSupported": true, "ebsOptimizedSupported": false, "family": "计算优化", "ipv6Support": true, "memory": 60, "networkPerformance": "10 Gigabit", "physicalProcessor": "Intel Xeon E5-2680v2", "placementGroupsSupported": true, "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.8, "storage": {"count": 2, "size": 320, "ssd": true}, "typeName": "c3.8xlarge", "virtualizationTypes": ["paravirtual", "hvm"], "vpc": true, "windows": true}]}, {"description": "GPU 图形实例同时为需要高性能图形加速的应用程序 (例如: 3D 可视化效果、图形密集型远程工作站、3D 渲染、视频编码和虚拟现实) 提供了 GPU 和高 CPU 性能、超大内存和高网络速度。", "name": "GPU 图形", "types": [{"amiFlags": [{"name": "ENA", "supported": true}], "architectures": ["x86_64"], "cpu": {"cores": 16, "units": "47"}, "dedicatedHostsSupported": true, "description": "图形四倍超大型", "ebsEncryptionSupported": true, "ebsOnly": true, "ebsOptimizedByDefault": true, "ebsOptimizedSupported": true, "family": "GPU 图形", "freeTierEligible": false, "ipv6Support": true, "memory": 122, "networkPerformance": "Up to 10 Gigabit", "physicalProcessor": "Intel Xeon E5-2686 v4", "placementGroupsSupported": true, "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.7, "spotSupported": true, "typeName": "g3.4xlarge", "virtualizationTypes": ["hvm"], "vpc": true, "vpcOnly": true, "windows": true}, {"amiFlags": [{"name": "ENA", "supported": true}], "architectures": ["x86_64"], "cpu": {"cores": 32, "units": "94"}, "dedicatedHostsSupported": true, "description": "图形八倍超大型", "ebsEncryptionSupported": true, "ebsOnly": true, "ebsOptimizedByDefault": true, "ebsOptimizedSupported": true, "family": "GPU 图形", "freeTierEligible": false, "ipv6Support": true, "memory": 244, "networkPerformance": "10 Gigabit", "physicalProcessor": "Intel Xeon E5-2686 v4", "placementGroupsSupported": true, "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.7, "spotSupported": true, "typeName": "g3.8xlarge", "virtualizationTypes": ["hvm"], "vpc": true, "vpcOnly": true, "windows": true}, {"amiFlags": [{"name": "ENA", "supported": true}], "architectures": ["x86_64"], "cpu": {"cores": 64, "units": "188"}, "dedicatedHostsSupported": true, "description": "图形十六倍超大型", "ebsEncryptionSupported": true, "ebsOnly": true, "ebsOptimizedByDefault": true, "ebsOptimizedSupported": true, "family": "GPU 图形", "freeTierEligible": false, "ipv6Support": true, "memory": 488, "networkPerformance": "25 Gigabit", "physicalProcessor": "Intel Xeon E5-2686 v4", "placementGroupsSupported": true, "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.7, "spotSupported": true, "typeName": "g3.16xlarge", "virtualizationTypes": ["hvm"], "vpc": true, "vpcOnly": true, "windows": true}]}, {"description": "GPU 计算实例提供了具有高 CPU 性能，大容量内存和高网络速度的多用途 GPU，给需要极大量浮点处理能力的应用程序，例如，机器学习，高性能数据库，计算流体动力学，计算金融，地震分析，分子建模，基因组学和渲染。", "name": "GPU 计算", "types": [{"amiFlags": [{"name": "ENA", "supported": true}], "architectures": ["x86_64"], "cpu": {"cores": 4, "units": "11.75"}, "dedicatedHostsSupported": true, "description": "GPU Compute Extra Large", "ebsEncryptionSupported": true, "ebsOnly": true, "ebsOptimizedSupported": true, "family": "GPU 计算", "ipv6Support": true, "memory": 61, "networkPerformance": "High", "physicalProcessor": "E5-2686v4", "placementGroupsSupported": true, "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.7, "spotSupported": true, "typeName": "p2.xlarge", "virtualizationTypes": ["hvm"], "vpc": true, "vpcOnly": true, "windows": true}, {"amiFlags": [{"name": "ENA", "supported": true}], "architectures": ["x86_64"], "cpu": {"cores": 32, "units": "94"}, "dedicatedHostsSupported": true, "description": "GPU Compute 8 Extra Large", "ebsEncryptionSupported": true, "ebsOnly": true, "ebsOptimizedSupported": true, "family": "GPU 计算", "ipv6Support": true, "memory": 488, "networkPerformance": "10 Gigabit", "physicalProcessor": "E5-2686v4", "placementGroupsSupported": true, "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.7, "spotSupported": true, "typeName": "p2.8xlarge", "virtualizationTypes": ["hvm"], "vpc": true, "vpcOnly": true, "windows": true}, {"amiFlags": [{"name": "ENA", "supported": true}], "architectures": ["x86_64"], "cpu": {"cores": 64, "units": "188"}, "dedicatedHostsSupported": true, "description": "GPU Compute 16 Extra Large", "ebsEncryptionSupported": true, "ebsOnly": true, "ebsOptimizedSupported": true, "family": "GPU 计算", "ipv6Support": true, "memory": 732, "networkPerformance": "25 Gigabit", "physicalProcessor": "E5-2686v4", "placementGroupsSupported": true, "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.7, "spotSupported": true, "typeName": "p2.16xlarge", "virtualizationTypes": ["hvm"], "vpc": true, "vpcOnly": true, "windows": true}]}, {"description": "内存优化型实例的每 GB RAM 的成本是 Amazon EC2 实例类型中最低的。我们建议将内存优化型实例用于很多数据库应用程序、内存缓存和其他分布式缓存以及较大的企业应用程序部署，如 SAP 和 Microsoft SharePoint。", "name": "内存优化", "types": [{"amiFlags": [{"name": "ENA", "supported": true}], "architectures": ["x86_64"], "cpu": {"cores": 2, "units": "6.9"}, "dedicatedHostsSupported": false, "description": "R4 大型", "ebsEncryptionSupported": true, "ebsOnly": true, "ebsOptimizedByDefault": true, "ebsOptimizedSupported": true, "family": "内存优化", "ipv6Support": true, "memory": 15.25, "networkPerformance": "Up to 10 Gigabit", "physicalProcessor": "Intel Broadwell E5-2686v4", "placementGroupsSupported": true, "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.3, "typeName": "r4.large", "virtualizationTypes": ["hvm"], "vpc": true, "vpcOnly": true, "windows": true}, {"amiFlags": [{"name": "ENA", "supported": true}], "architectures": ["x86_64"], "cpu": {"cores": 4, "units": "13.4"}, "dedicatedHostsSupported": false, "description": "R4 超大型", "ebsEncryptionSupported": true, "ebsOnly": true, "ebsOptimizedByDefault": true, "ebsOptimizedSupported": true, "family": "内存优化", "ipv6Support": true, "memory": 30.5, "networkPerformance": "Up to 10 Gigabit", "physicalProcessor": "Intel Broadwell E5-2686v4", "placementGroupsSupported": true, "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.3, "typeName": "r4.xlarge", "virtualizationTypes": ["hvm"], "vpc": true, "vpcOnly": true, "windows": true}, {"amiFlags": [{"name": "ENA", "supported": true}], "architectures": ["x86_64"], "cpu": {"cores": 8, "units": "26.8"}, "dedicatedHostsSupported": false, "description": "R4 双倍超大型", "ebsEncryptionSupported": true, "ebsOnly": true, "ebsOptimizedByDefault": true, "ebsOptimizedSupported": true, "family": "内存优化", "ipv6Support": true, "memory": 61, "networkPerformance": "Up to 10 Gigabit", "physicalProcessor": "Intel Broadwell E5-2686v4", "placementGroupsSupported": true, "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.3, "typeName": "r4.2xlarge", "virtualizationTypes": ["hvm"], "vpc": true, "vpcOnly": true, "windows": true}, {"amiFlags": [{"name": "ENA", "supported": true}], "architectures": ["x86_64"], "cpu": {"cores": 16, "units": "52.8"}, "dedicatedHostsSupported": false, "description": "R4 四倍超大型", "ebsEncryptionSupported": true, "ebsOnly": true, "ebsOptimizedByDefault": true, "ebsOptimizedSupported": true, "family": "内存优化", "ipv6Support": true, "memory": 122, "networkPerformance": "Up to 10 Gigabit", "physicalProcessor": "Intel Broadwell E5-2686v4", "placementGroupsSupported": true, "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.3, "typeName": "r4.4xlarge", "virtualizationTypes": ["hvm"], "vpc": true, "vpcOnly": true, "windows": true}, {"amiFlags": [{"name": "ENA", "supported": true}], "architectures": ["x86_64"], "cpu": {"cores": 32, "units": "99.1"}, "dedicatedHostsSupported": false, "description": "R4 八倍超大型", "ebsEncryptionSupported": true, "ebsOnly": true, "ebsOptimizedByDefault": true, "ebsOptimizedSupported": true, "family": "内存优化", "ipv6Support": true, "memory": 244, "networkPerformance": "10 Gigabit", "physicalProcessor": "Intel Broadwell E5-2686v4", "placementGroupsSupported": true, "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.3, "typeName": "r4.8xlarge", "virtualizationTypes": ["hvm"], "vpc": true, "vpcOnly": true, "windows": true}, {"amiFlags": [{"name": "ENA", "supported": true}], "architectures": ["x86_64"], "cpu": {"cores": 64, "units": "195"}, "dedicatedHostsSupported": false, "description": "R4 16 超大型", "ebsEncryptionSupported": true, "ebsOnly": true, "ebsOptimizedByDefault": true, "ebsOptimizedSupported": true, "family": "内存优化", "ipv6Support": true, "memory": 488, "networkPerformance": "25 Gigabit", "physicalProcessor": "Intel Broadwell E5-2686v4", "placementGroupsSupported": true, "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.3, "typeName": "r4.16xlarge", "virtualizationTypes": ["hvm"], "vpc": true, "vpcOnly": true, "windows": true}, {"architectures": ["x86_64"], "cpu": {"cores": 2, "units": "6.5"}, "dedicatedHostsSupported": true, "description": "R3 大型", "ebsEncryptionSupported": true, "ebsOptimizedSupported": false, "family": "内存优化", "ipv6Support": true, "memory": 15, "networkPerformance": "Moderate", "physicalProcessor": "Intel Xeon E5-2670v2", "placementGroupsSupported": true, "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.5, "storage": {"count": 1, "size": 32, "ssd": true}, "typeName": "r3.large", "virtualizationTypes": ["hvm"], "vpc": true, "windows": true}, {"architectures": ["x86_64"], "cpu": {"cores": 4, "units": "13"}, "dedicatedHostsSupported": true, "description": "R3 超大型", "ebsEncryptionSupported": true, "ebsOptimizedSupported": true, "family": "内存优化", "ipv6Support": true, "memory": 30.5, "networkPerformance": "Moderate", "physicalProcessor": "Intel Xeon E5-2670v2", "placementGroupsSupported": true, "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.5, "storage": {"count": 1, "size": 80, "ssd": true}, "typeName": "r3.xlarge", "virtualizationTypes": ["hvm"], "vpc": true, "windows": true}, {"architectures": ["x86_64"], "cpu": {"cores": 8, "units": "26"}, "dedicatedHostsSupported": true, "description": "R3 双倍超大型", "ebsEncryptionSupported": true, "ebsOptimizedSupported": true, "family": "内存优化", "ipv6Support": true, "memory": 61, "networkPerformance": "High", "physicalProcessor": "Intel Xeon E5-2670v2", "placementGroupsSupported": true, "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.5, "storage": {"count": 1, "size": 160, "ssd": true}, "typeName": "r3.2xlarge", "virtualizationTypes": ["hvm"], "vpc": true, "windows": true}, {"architectures": ["x86_64"], "cpu": {"cores": 16, "units": "52"}, "dedicatedHostsSupported": true, "description": "R3 四倍超大型", "ebsEncryptionSupported": true, "ebsOptimizedSupported": true, "family": "内存优化", "ipv6Support": true, "memory": 122, "networkPerformance": "High", "physicalProcessor": "Intel Xeon E5-2670v2", "placementGroupsSupported": true, "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.5, "storage": {"count": 1, "size": 320, "ssd": true}, "typeName": "r3.4xlarge", "virtualizationTypes": ["hvm"], "vpc": true, "windows": true}, {"architectures": ["x86_64"], "cpu": {"cores": 32, "units": "104"}, "dedicatedHostsSupported": true, "description": "R3 八倍超大型", "ebsEncryptionSupported": true, "ebsOptimizedSupported": false, "family": "内存优化", "ipv6Support": true, "memory": 244, "networkPerformance": "10 Gigabit", "physicalProcessor": "Intel Xeon E5-2670v2", "placementGroupsSupported": true, "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.5, "storage": {"count": 2, "size": 320, "ssd": true}, "typeName": "r3.8xlarge", "virtualizationTypes": ["hvm"], "vpc": true, "windows": true}, {"amiFlags": [{"name": "ENA", "supported": true}], "architectures": ["x86_64"], "cpu": {"cores": 64, "units": "349"}, "dedicatedHostsSupported": true, "defaultEphemerals": ["b", "c"], "description": "X1 16 超大型", "ebsEncryptionSupported": true, "ebsOnly": false, "ebsOptimizedByDefault": true, "ebsOptimizedSupported": true, "family": "内存优化", "ipv6Support": true, "memory": 976, "networkPerformance": "10 Gigabit", "physicalProcessor": "Intel Xeon E7 8880 v3", "placementGroupsSupported": true, "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.3, "spotSupported": true, "storage": {"count": 1, "size": 1920, "ssd": true}, "typeName": "x1.16xlarge", "virtualizationTypes": ["hvm"], "vpc": true, "vpcOnly": true, "windows": true}, {"amiFlags": [{"name": "ENA", "supported": true}], "architectures": ["x86_64"], "cpu": {"cores": 128, "units": "349"}, "dedicatedHostsSupported": true, "defaultEphemerals": ["b", "c"], "description": "X1 32 超大型", "ebsEncryptionSupported": true, "ebsOptimizedByDefault": true, "ebsOptimizedSupported": true, "family": "内存优化", "ipv6Support": true, "memory": 1952, "networkPerformance": "25 Gigabit", "physicalProcessor": "Intel Xeon E7 8880 v3", "placementGroupsSupported": true, "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.3, "spotSupported": true, "storage": {"count": 2, "size": 1920, "ssd": true}, "typeName": "x1.32xlarge", "virtualizationTypes": ["hvm"], "vpc": true, "vpcOnly": true, "windows": true}]}, {"description": "存储优化型实例能向您提供经过优化，且适用于具有特定磁盘 I/O 和存储容量要求的应用程序的直连式存储选项。我们建议将 I2 实例用于可从极高的随机 I/O 性能与较低的直连式 SSD 请求延迟中获益的 NoSQL 数据库。我们建议使用 D2 实例运行较大规模的数据仓库或并行文件系统。", "name": "存储优化", "types": [{"architectures": ["x86_64"], "cpu": {"cores": 4, "units": "14"}, "dedicatedHostsSupported": true, "defaultEphemerals": ["b", "c", "d"], "description": "D2 超大型", "ebsEncryptionSupported": true, "ebsOptimizedByDefault": true, "ebsOptimizedSupported": true, "family": "存储优化", "ipv6Support": true, "memory": 30.5, "networkPerformance": "Moderate", "physicalProcessor": "Intel Xeon E52676v3", "placementGroupsSupported": true, "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.4, "storage": {"count": 3, "size": 2048, "ssd": false}, "typeName": "d2.xlarge", "virtualizationTypes": ["hvm"], "vpc": true, "windows": true}, {"architectures": ["x86_64"], "cpu": {"cores": 8, "units": "28"}, "dedicatedHostsSupported": true, "defaultEphemerals": ["b", "c", "d", "e", "f", "g"], "description": "D2 双倍超大型", "ebsEncryptionSupported": true, "ebsOptimizedByDefault": true, "ebsOptimizedSupported": true, "family": "存储优化", "ipv6Support": true, "memory": 61, "networkPerformance": "High", "physicalProcessor": "Intel Xeon E52676v3", "placementGroupsSupported": true, "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.4, "storage": {"count": 6, "size": 2048, "ssd": false}, "typeName": "d2.2xlarge", "virtualizationTypes": ["hvm"], "vpc": true, "windows": true}, {"architectures": ["x86_64"], "cpu": {"cores": 16, "units": "56"}, "dedicatedHostsSupported": true, "defaultEphemerals": ["b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m"], "description": "D2 四倍超大型", "ebsEncryptionSupported": true, "ebsOptimizedByDefault": true, "ebsOptimizedSupported": true, "family": "存储优化", "ipv6Support": true, "memory": 122, "networkPerformance": "High", "physicalProcessor": "Intel Xeon E52676v3", "placementGroupsSupported": true, "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.4, "storage": {"count": 12, "size": 2048, "ssd": false}, "typeName": "d2.4xlarge", "virtualizationTypes": ["hvm"], "vpc": true, "windows": true}, {"architectures": ["x86_64"], "cpu": {"cores": 36, "units": "116"}, "dedicatedHostsSupported": true, "defaultEphemerals": ["b", "c", "d", "e", "f", "g", "h", "I", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y"], "description": "D2 八倍超大型", "ebsEncryptionSupported": true, "ebsOptimizedByDefault": true, "ebsOptimizedSupported": true, "family": "存储优化", "ipv6Support": true, "memory": 244, "networkPerformance": "10 Gigabit", "physicalProcessor": "Intel Xeon E52676v3", "placementGroupsSupported": true, "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.4, "storage": {"count": 24, "size": 2048, "ssd": false}, "typeName": "d2.8xlarge", "virtualizationTypes": ["hvm"], "vpc": true, "windows": true}, {"architectures": ["x86_64"], "cpu": {"cores": 4, "units": "14"}, "dedicatedHostsSupported": true, "defaultEphemerals": ["f"], "description": "高 I/O 超大型", "ebsEncryptionSupported": true, "ebsOptimizedSupported": true, "family": "存储优化", "ipv6Support": true, "memory": 30.5, "networkPerformance": "Moderate", "physicalProcessor": "Intel Xeon E5-2670v2", "placementGroupsSupported": true, "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.5, "spotSupported": true, "storage": {"count": 1, "size": 800, "ssd": true}, "typeName": "i2.xlarge", "virtualizationTypes": ["hvm"], "vpc": true, "windows": true}, {"architectures": ["x86_64"], "cpu": {"cores": 8, "units": "27"}, "dedicatedHostsSupported": true, "defaultEphemerals": ["f", "g"], "description": "高 I/O 双倍超大型", "ebsEncryptionSupported": true, "ebsOptimizedSupported": true, "family": "存储优化", "ipv6Support": true, "memory": 61, "networkPerformance": "High", "physicalProcessor": "Intel Xeon E5-2670v2", "placementGroupsSupported": true, "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.5, "spotSupported": true, "storage": {"count": 2, "size": 800, "ssd": true}, "typeName": "i2.2xlarge", "virtualizationTypes": ["hvm"], "vpc": true, "windows": true}, {"architectures": ["x86_64"], "cpu": {"cores": 16, "units": "53"}, "dedicatedHostsSupported": true, "defaultEphemerals": ["f", "g", "h", "i"], "description": "高 I/O 四倍超大型", "ebsEncryptionSupported": true, "ebsOptimizedSupported": true, "family": "存储优化", "ipv6Support": true, "memory": 122, "networkPerformance": "High", "physicalProcessor": "Intel Xeon E5-2670v2", "placementGroupsSupported": true, "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.5, "spotSupported": true, "storage": {"count": 4, "size": 800, "ssd": true}, "typeName": "i2.4xlarge", "virtualizationTypes": ["hvm"], "vpc": true, "windows": true}, {"architectures": ["x86_64"], "cpu": {"cores": 32, "units": "104"}, "dedicatedHostsSupported": true, "defaultEphemerals": ["f", "g", "h", "i", "j", "k", "l", "m"], "description": "高 I/O 八倍超大型", "ebsEncryptionSupported": true, "family": "存储优化", "ipv6Support": true, "memory": 244, "networkPerformance": "10 Gigabit", "physicalProcessor": "Intel Xeon E5-2670v2", "placementGroupsSupported": true, "processorFeatures": {"AES-NI": true, "AVX": true, "Turbo": true}, "processorSpeed": 2.5, "spotSupported": true, "storage": {"count": 8, "size": 800, "ssd": true}, "typeName": "i2.8xlarge", "virtualizationTypes": ["hvm"], "vpc": true, "windows": true}]}]