/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.common.util;

import cn.com.cloudstar.rightcloud.module.support.access.pojo.BaseRequest;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.netty.util.internal.StringUtil;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2019/10/17.
 */

public class PageUtil {

    private static final String ASC = "asc";
    private static final String DESC = "desc";

    public static <T> Page<T> preparePageParams(BaseRequest request, String defaultDataField , String defaultOrderByClause){
        Page<T> page = new Page<>();
        // 设置分页信息
        setPaginationParam(page, Long.valueOf(request.getPagenum()), Long.valueOf(request.getPagesize()));

        // 设置排序信息
        String sortDataField = request.getSortdatafield();
        String sortOrder = request.getSortorder();
        if (StringUtil.isNullOrEmpty(sortDataField) && StringUtil.isNullOrEmpty(sortOrder)) {
            sortOrder = defaultOrderByClause;
            sortDataField = defaultDataField;
        }
        setOrderParams(page, sortDataField, sortOrder);

        return page;
    }

    public static <T> Page<T> preparePageParams(BaseRequest request){
        return preparePageParams(request, null, null);
    }

    private static void setPaginationParam(Page<?> page, Long strPageNum, Long strPageSize) {
        if (Objects.nonNull(strPageNum) && Objects.nonNull(strPageSize)) {
            // 默认从0开始，这里加1
            page.setCurrent(strPageNum + 1);
            page.setSize(strPageSize);
        }
    }

    private static void setOrderParams(Page<?> page, String sortDatafield,
                                       String sortOrder) {
        if (!StrUtil.isBlank(sortDatafield) && !StrUtil.isBlank(sortOrder)) {
            if (ASC.equals(sortOrder)) {
                page.setAsc(toClumn(sortDatafield));
            } else {
                page.setDesc(toClumn(sortDatafield));
            }
        }
    }

    public static String toClumn(String field) {

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < field.length(); i++) {
            char c = field.charAt(i);
            if (Character.isUpperCase(c) && i > 0) {
                sb.append("_").append(Character.toUpperCase(c));
            } else {
                sb.append(Character.toUpperCase(c));
            }
        }
        return sb.toString();
    }

    public static <T> IPage<T> emptyPage(){
        return new Page<>();
    }

    /**
     * 是否可以页面查询
     *
     * @param baseRequest 基本请求
     * @return boolean
     */
    public static boolean isPageQuery(BaseRequest baseRequest) {
        if (Objects.isNull(baseRequest)){
            return false;
        }
        return !(StringUtil.isNullOrEmpty(baseRequest.getPagenum()) && StringUtil.isNullOrEmpty(baseRequest.getPagesize()));
    }

    public static void setOrderBy(BaseRequest request, QueryWrapper<?> queryWrapperPage, String defaultOrderByClause) {
        String sortdatafield = request.getSortdatafield();
        String sortorder = request.getSortorder();

        String descField = "desc".equalsIgnoreCase(sortorder) ? PageUtil.toClumn(sortdatafield) : null;
        if (StrUtil.isNotBlank(descField)) {
            queryWrapperPage.orderByDesc(descField);
        }

        String ascField = "asc".equalsIgnoreCase(sortorder) ? PageUtil.toClumn(sortdatafield) : null;
        if (StrUtil.isNotBlank(ascField)) {
            queryWrapperPage.orderByAsc(ascField);
        }

        if (StrUtil.isBlank(descField) && StrUtil.isBlank(ascField) && StrUtil.isNotBlank(defaultOrderByClause)) {
            String[] split = defaultOrderByClause.split(" ");
            if (split[1].equalsIgnoreCase("desc")) {
                queryWrapperPage.orderByDesc(split[0]);
            } else {
                queryWrapperPage.orderByAsc(split[0]);
            }
        }
    }
}
