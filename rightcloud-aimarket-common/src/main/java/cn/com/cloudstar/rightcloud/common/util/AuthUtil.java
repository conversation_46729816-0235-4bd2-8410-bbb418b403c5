/*
* Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
*/

package cn.com.cloudstar.rightcloud.common.util;


import cn.com.cloudstar.rightcloud.common.constants.type.UserType;
import cn.com.cloudstar.rightcloud.common.dto.User;
import cn.com.cloudstar.rightcloud.common.pojo.AuthAsyncVo;
import cn.com.cloudstar.rightcloud.common.util.json.JsonUtil;
import cn.com.cloudstar.rightcloud.module.support.access.cache.AuthUserHolder;
import cn.com.cloudstar.rightcloud.module.support.access.constants.DataScopeEnum;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.Org;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.Role;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicInfoUtil;
import cn.com.cloudstar.rightcloud.module.support.db.util.DBUtils;
import cn.hutool.core.util.StrUtil;
import com.google.common.base.CaseFormat;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Supplier;

/**
* 存取登录用户信息
*
* <AUTHOR>
*/
@Slf4j
public class AuthUtil {

   private static final String BASE_ORG_SQL =
           "SELECT\n" + "A.ORG_SID,\n" + "A.ORG_NAME,\n" + "A.ORG_CODE,\n" + "A.ORG_TYPE,\n" +
                   "A.TREE_PATH,\n" + "A.PARENT_ID,\n" + "A.ORG_ICON,\n" + "A.STATUS\n,A.ldap_ou \n" +
                   "FROM sys_m_org A\n" +
                   "WHERE A.ORG_SID = ?";

   private static final String ROLE_SQL =
           "SELECT A.ROLE_SID as roleSid, A.ROLE_NAME as roleName, A.STATUS as status, A.ROLE_TYPE as roleType, " +
                   "A.DATA_SCOPE as dataScope, A.MODULE_CATEGORY as moduleCategory \n" +
                   "FROM sys_m_role A, sys_m_user_role B\n" +
                   "WHERE A.ROLE_SID = B.ROLE_SID AND B.USER_SID = ?";

   private static final String USER_ORG_ROLE_SQL =
           "SELECT A.ROLE_SID as roleSid, A.ROLE_NAME as roleName, A.STATUS as status, A.ROLE_TYPE as roleType, " +
                   "A.DATA_SCOPE as dataScope, A.MODULE_CATEGORY as moduleCategory \n" +
                   "FROM sys_m_role A, sys_m_user_role B\n" +
                   "WHERE A.ROLE_SID = B.ROLE_SID AND B.USER_SID = ? AND B.org_sid = ?";


   private static final String PERMISIONS_SQL =
           "SELECT\n" + "  A.ROLE_SID,\n" + "  C.MODULE_SID\n" + "FROM sys_m_user_role A\n" +
                   "  INNER JOIN sys_m_role B\n" + "    ON A.ROLE_SID = B.ROLE_SID\n" +
                   "  INNER JOIN sys_m_role_module C ON C.ROLE_SID = A.ROLE_SID\n" + "WHERE A.USER_SID = ? \n" +
                   "GROUP BY C.MODULE_SID";

   private static final String USER_BASE_INFO = "SELECT A.USER_SID, A.USER_TYPE, A.ACCOUNT, A.REAL_NAME, A.SEX, A.EMAIL," +
           " A.MOBILE, A.STATUS, A.COMPANY_ID, A.ORG_SID FROM sys_m_user A where A.ACCOUNT = ? and A.status = 1";

   private static final String ROLE_PERMISSION = "SELECT 1 FROM sys_m_role_module WHERE role_sid = ? AND module_sid = ?";


   /**
    * 获取当前主公司的ID
    */
   public static Long getCurrentMainOrgSid() {
       Org org = getCurrentOrg();
       try {
           String treePath = org.getTreePath();
           if("/".equals(treePath)){
               return org.getOrgSid();
           }
           String mainOrgSid = treePath.split("/")[1];
           return Long.valueOf(mainOrgSid);
       } catch (Exception e) {
           log.error(e.getMessage());
       }
       return null;
   }

   public static Long getCurrentOrgSid() {
       AuthUser authUser = RequestContextUtil.getAuthUserInfo();
       if (authUser == null) {
           return null;
       }
       return authUser.getOrgSid();
   }


   /**
    * 获取登录用户实体
    */
   public static User getAuthUser() {
       User user;
       try {
           log.info("开始获取登录用户实体");
           AuthUser authUser = RequestContextUtil.getAuthUserInfo();

           Map<String, Object> dbUserMap = DBUtils.INSTANCE.queryMap(USER_BASE_INFO, authUser.getAccount());
           Map<String, String> userMap = new HashMap<>();
           for (Object column : dbUserMap.keySet()) {
               String key = column.toString();
               String camelKey = CaseFormat.UPPER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, key);
               userMap.put(camelKey, StringUtil.nullToEmpty(dbUserMap.get(key)));
           }

           user = JsonUtil.fromJson(JsonUtil.toJson(userMap), User.class);
           user.setModuleType(authUser.getRemark());
           user.setAdminFlag(authUser.getAdminFlag());
           user.setParentSid(authUser.getParentSid());
       } catch (Exception e) {
           user = null;
       }

       return user;
   }

   /**
    * 获取当前组织
    */
   public static Org getCurrentOrg() {
       AuthUser authUser = RequestContextUtil.getAuthUserInfo();
       if (null == authUser) {
           return null;
       }
       if(null == authUser.getCompanyId()){
           return null;
       }
       return queryOrg(authUser.getCompanyId());
   }

   public static Org queryOrg(Long orgSid) {
       Map<String, Object> dbOrgMap = DBUtils.INSTANCE.queryMap(BASE_ORG_SQL, orgSid);
       Map<String, String> orgMap = new HashMap<>();
       if (!CollectionUtils.isEmpty(dbOrgMap)) {
           for (Object column : dbOrgMap.keySet()) {
               String key = column.toString();
               String camelKey = CaseFormat.UPPER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, key);
               orgMap.put(camelKey, StringUtil.nullToEmpty(dbOrgMap.get(key)));
           }
       }

       return JsonUtil.fromJson(JsonUtil.toJson(orgMap), Org.class);
   }

   /**
    * 获取当前用户的角色列表
    */
   private static List<Role> getRoleList() {
       AuthUser authUser = RequestContextUtil.getAuthUserInfo();
       return DBUtils.INSTANCE.queryBeanList(ROLE_SQL, Role.class, authUser.getUserSid());
   }

   /**
    * 获取用户在指定组织的角色列表
    */
   private static List<Role> getRoleList(Long orgSid) {
       AuthUser authUser = RequestContextUtil.getAuthUserInfo();
       return DBUtils.INSTANCE.queryBeanList(USER_ORG_ROLE_SQL, Role.class, authUser.getUserSid(), orgSid);
   }

   /**
    * 获取数据过滤的SQL（符合业务表字段不同的时候使用，采用exists方法）
    */
   public static String getSQLFilter(String tableAlias, String orgField, String userField) {
       // 解决取不到当前用户报错的情况（1. 回调的时候 2.直接用前台访问小概率会出现取不到当前用户的情况）
       AuthUser user;
       try {
           //如果是系统菜单相关接口
           if(RequestContextUtil.getIsSystemMenu()){
               return null;
           }
           user = RequestContextUtil.getAuthUserInfo();
       } catch (Exception e) {
           return null;
       }

       if (Objects.isNull(user)) {
           return null;
       }

       Org org = getCurrentOrg();
       String dataScopeString = getMaxDataScope();

       if (org == null) {
           //运营、运维角色无组织
           return null;
       }

       if ("console".equals(user.getRemark())) {
           return null;
       }

       //获取表的别名
       if (StringUtils.isNotBlank(tableAlias)) {
           tableAlias += ".";
       }

       StringBuilder sqlFilter = new StringBuilder();
       //分销商 组织及以下数据
       if (UserType.DISTRIBUTOR_USER.equals(user.getUserType())) {
           // 仅查看当前组织及以下的数据
           sqlFilter.append(" EXISTS (SELECT 1 FROM sys_m_org");
           sqlFilter.append(" WHERE (org_sid = '" + org.getOrgSid() + "'");
           sqlFilter.append(" OR tree_path LIKE '%/" + org.getOrgSid() + "/%')");
           // 对业务数据的组织ID不需要比较org_id的，传空
           if (StrUtil.isNotBlank(tableAlias)) {
               sqlFilter.append(" AND org_sid = " + tableAlias + orgField);
           }
           sqlFilter.append(")");
           return " AND (" + sqlFilter.toString() + ")";
       }

       // 生成组织权限SQL语句
       if (DataScopeEnum.DATA_SCOPE_COMPANY_AND_CHILD.getScope().equals(dataScopeString)) {
           // 仅查看当前组织及以下的数据
           sqlFilter.append(" EXISTS (SELECT 1 FROM sys_m_org");
           sqlFilter.append(" WHERE (org_sid = '" + org.getOrgSid() + "'");
           sqlFilter.append(" OR tree_path LIKE '" + org.getTreePath() + org.getOrgSid() + "/%')");
           // 对业务数据的组织ID不需要比较org_id的，传空
           if (StrUtil.isNotBlank(tableAlias)) {
               sqlFilter.append(" AND org_sid = " + tableAlias + orgField);
           }
           sqlFilter.append(")");

       } else if (DataScopeEnum.DATA_SCOPE_COMPANY.getScope().equals(dataScopeString)) {
           // 仅查看当前组织的数据
           sqlFilter.append(" EXISTS (SELECT 1 FROM sys_m_org");
           sqlFilter.append(" WHERE org_sid = '" + org.getOrgSid() + "'");
           sqlFilter.append(" AND org_sid = " + tableAlias + orgField + ")");
       } else if (DataScopeEnum.DATA_SCOPE_SELF.getScope().equals(dataScopeString)) {
           // 仅查看当前组织的个人数据
           sqlFilter.append(" EXISTS (SELECT 1 FROM sys_m_org");
           sqlFilter.append(" WHERE org_sid = '" + org.getOrgSid() + "'");
           sqlFilter.append(" AND org_sid = " + tableAlias + orgField + ")");
           if (StringUtils.isNotBlank(userField)) {
               sqlFilter.append(" AND");
               sqlFilter.append(" EXISTS (SELECT 1 FROM sys_m_user");
               sqlFilter.append(" WHERE user_sid = '" + user.getUserSid() + "'");
               sqlFilter.append(" AND account = " + tableAlias + userField + ")");
           }
       } else if (DataScopeEnum.DATA_SCOPE_CUSTOM.getScope().equals(dataScopeString)) {
          /* sqlFilter.append(" AND EXISTS (SELECT 1 FROM sys_m_role_org ro123456, sys_m_org o123456");
           sqlFilter.append(" WHERE ro123456.org_sid = o123456.org_sid");
           sqlFilter.append(" AND ro123456.role_sid = '" + roleId + "'");*/

       } else if (DataScopeEnum.DATA_SCOPE_ALL.getScope().equals(dataScopeString) && !Objects.isNull(org)) {
           // 查看当前指定组织及以下的数据
           sqlFilter.append(" EXISTS (SELECT 1 FROM sys_m_org");
           sqlFilter.append(" WHERE (org_sid = '" + org.getOrgSid() + "'");
           sqlFilter.append(" OR tree_path LIKE '" + org.getTreePath() + org.getOrgSid() + "/%')");
           // 对业务数据的组织ID不需要比较org_id的，传空
           if (StrUtil.isNotBlank(orgField)) {
               sqlFilter.append(" AND org_sid = " + tableAlias + orgField);
           }
           sqlFilter.append(")");
       }

       if (StringUtils.isNotBlank(sqlFilter.toString())) {
           return " AND (" + sqlFilter.toString() + ")";
       }

       return null;

   }

   public static String getSQLFilterWithGivingOrgSid(String tableAlias, String orgField, Long orgSid) {

       Org org = queryOrg(orgSid);
       if (Objects.isNull(org)) {
           return null;
       }

       //获取表的别名
       if (StringUtils.isNotBlank(tableAlias)) {
           tableAlias += ".";
       }

       StringBuilder sqlFilter = new StringBuilder();
       // 生成组织权限SQL语句,仅查看当前组织及以下的数据
       sqlFilter.append(" EXISTS (SELECT 1 FROM sys_m_org");
       sqlFilter.append(" WHERE (org_sid = '" + org.getOrgSid() + "'");
       sqlFilter.append(" OR tree_path LIKE '" + org.getTreePath() + org.getOrgSid() + "/%')");
       sqlFilter.append(" AND org_sid = " + tableAlias + orgField + ")");

       if (StringUtils.isNotBlank(sqlFilter.toString())) {
           return " AND (" + sqlFilter.toString() + ")";
       }

       return null;
   }

   /**
    * 根据数据权限获取查询条件
    * 仅根据组织查询，如果数据权限不是组织及以下数据，则只能查询当前组织的数据
    *
    * @param tableAlias 表别名
    * @param orgField org_sid对应的字段
    * @param orgSid 当前org_sid
    * @param existInOrg 是否查询存在于组织中的数据（true： 存在于组织中的数据， false：不存在于组织中的数据）
    * @return 权限过滤条件
    */
   public static String getSQLFilterOnlyByOrgSid(String tableAlias, String orgField, Long orgSid, boolean existInOrg) {
       Org org = queryOrg(orgSid);
       String dataScopeString = getMaxDataScope();

       //获取表的别名
       if (StringUtils.isNotBlank(tableAlias) && !tableAlias.contains(".")) {
           tableAlias += ".";
       }

       StringBuilder sqlFilter = new StringBuilder();

       // 查询在当前用户可查询范围内，不在指定项目下的数据
       if (!existInOrg) {
           // 当前组织下可查询的数据
           String existDf = AuthUtil.getSQLFilterOnlyByOrgSid(tableAlias, orgField,
                   AuthUtil.getCurrentOrgSid(), true);
           sqlFilter.append(existDf);

           sqlFilter.append("AND NOT");
       }
       sqlFilter.append(" EXISTS (SELECT 1 FROM sys_m_org");

       // 生成组织权限SQL语句
       if (DataScopeEnum.DATA_SCOPE_COMPANY_AND_CHILD.getScope().equals(dataScopeString)) {
           // 仅查看当前组织及以下的数据
           sqlFilter.append(" WHERE (org_sid = '" + org.getOrgSid() + "'");
           sqlFilter.append(" OR tree_path LIKE '" + org.getTreePath() + org.getOrgSid() + "/%')");
           sqlFilter.append(" AND org_sid = " + tableAlias + orgField + ")");
       } else if (DataScopeEnum.DATA_SCOPE_ALL.getScope().equals(dataScopeString) && !Objects.isNull(org)) {
           // 超级管理员查看指定组织及以下的数据
           sqlFilter.append(" WHERE (org_sid = '" + org.getOrgSid() + "'");
           sqlFilter.append(" OR tree_path LIKE '" + org.getTreePath() + org.getOrgSid() + "/%')");
           sqlFilter.append(" AND org_sid = " + tableAlias + orgField + ")");
       } else {
           // 仅查看当前组织的数据
           sqlFilter.append(" WHERE org_sid = '" + org.getOrgSid() + "'");
           sqlFilter.append(" AND org_sid = " + tableAlias + orgField + ")");
       }

       if (!existInOrg) {
           return sqlFilter.toString();
       }

       if (StringUtils.isNotBlank(sqlFilter.toString())) {
           return " AND (" + sqlFilter.toString() + ")";
       }

       return null;
   }


   /**
    * 获取数据过滤的SQL
    */
   public static String getSQLJoinFilter(String orgAlias, String userAlias, String userId) {
       AuthUser user;
       try {
           user = RequestContextUtil.getAuthUserInfo();
       } catch (Exception e) {
           return null;
       }

       // 超级管理员，跳过权限过滤
       if (user.isAdmin()) {
           return null;
       }

       Org org = getCurrentOrg();
       List<Role> roleList = getRoleList();

       StringBuilder sqlFilter = new StringBuilder();

       boolean isDataScopeAll = false;
       for (Role r : roleList) {
           if (DataScopeEnum.DATA_SCOPE_ALL.getScope().equals(r.getDataScope())) {
               isDataScopeAll = true;
           } else if (DataScopeEnum.DATA_SCOPE_COMPANY_AND_CHILD.getScope().equals(r.getDataScope())) {
               sqlFilter.append(" OR " + orgAlias + ".org_sid = '" + org.getOrgSid() + "'");
               sqlFilter.append(" OR " + orgAlias + ".tree_path LIKE '" + org.getTreePath() + org.getOrgSid() + "/%'");
           } else if (DataScopeEnum.DATA_SCOPE_COMPANY.getScope().equals(r.getDataScope())) {
               sqlFilter.append(" OR " + orgAlias + ".org_sid = '" + org.getOrgSid() + "'");
               sqlFilter.append(" OR (" + orgAlias + ".parent_id = '" + org.getOrgSid());
           } else if (DataScopeEnum.DATA_SCOPE_CUSTOM.getScope().equals(r.getDataScope())) {
               sqlFilter.append(" OR EXISTS (SELECT 1 FROM sys_role_org WHERE role_sid = '" + r.getRoleSid() + "'");
               sqlFilter.append(" AND org_sid = " + orgAlias + ".org_sid)");
           } else if (DataScopeEnum.DATA_SCOPE_ALL.getScope().equals(r.getDataScope()) && !Objects.isNull(org)) {
               // 超级管理员查看指定组织及以下的数据
               sqlFilter.append(" OR " + orgAlias + ".org_sid = '" + org.getOrgSid() + "'");
               sqlFilter.append(" OR " + orgAlias + ".tree_path LIKE '" + org.getTreePath() + org.getOrgSid() + "/%'");
           }

       }

       // 如果包含全部权限，则直接返回null
       if (isDataScopeAll) {
           return null;
       }

       // 如果没有全部数据权限，并设置了用户表别名，则当前权限为本人；如果未设置别名，当前无权限为已植入权限
       if (StringUtils.isNotBlank(userAlias)) {
           sqlFilter.append(" OR " + userAlias + ".user_sid = '" + user.getUserSid() + "'");
       } else {
           sqlFilter.append(" OR " + orgAlias + ".org_sid IS NULL");
       }

       if (StringUtils.isNotBlank(sqlFilter.toString())) {
           return " AND (" + sqlFilter.substring(4) + ")";
       }

       return null;
   }

   public static String getMaxDataScope() {
       List<Role> roleList = Lists.newArrayList();
       AuthUser authUser = RequestContextUtil.getAuthUserInfo();
       // 如果是超级管理员，数据权限范围为全部
       if (authUser.getAdminFlag()) {
           return DataScopeEnum.DATA_SCOPE_ALL.getScope();
       }

       if(null == authUser.getOrgSid()){
           roleList = getRoleList();
       }else{
           roleList = getRoleList(authUser.getOrgSid());
       }

       // 获取到最大的数据权限范围
       int dataScopeInteger = 8;
       for (Role r : roleList) {
           int ds = Integer.parseInt(r.getDataScope());
           if (ds == 9) {
               dataScopeInteger = ds;
               break;
           } else if (ds < dataScopeInteger) {
               dataScopeInteger = ds;
           }
       }

       return String.valueOf(dataScopeInteger);
   }

   /**
    * 该方法为了适应很多地方需要判断角色的情况，按数据权限范围判断角色
    */
   public static String getCurrentUserType() {
       String maxDataScope = getMaxDataScope();
       // 企业管理员
       if (DataScopeEnum.DATA_SCOPE_COMPANY_AND_CHILD.getScope().equals(maxDataScope)) {
           return "02";
           // 项目管理员
       } else if (DataScopeEnum.DATA_SCOPE_COMPANY.getScope().equals(maxDataScope)) {
           return "03";
           // 项目用户
       } else if (DataScopeEnum.DATA_SCOPE_SELF.getScope().equals(maxDataScope)) {
           return "04";
       }

       return "99";
   }

   public static boolean hasPermission(String moduleSid) {
       Long orgSid = AuthUtil.getCurrentOrgSid();
       Long maxRole = AuthUtil.getMaxRole(orgSid);

       Map map = DBUtils.INSTANCE.queryMap(ROLE_PERMISSION, maxRole, moduleSid);

       return !CollectionUtils.isEmpty(map);
   }

   /**
    * 获取用户在当前组织的最大角色
    *
    * @param orgSid 组织ID
    * @return 角色ID
    */
   private static Long getMaxRole(Long orgSid) {
       List<Role> roleList = AuthUtil.getRoleList(orgSid);

       Long maxRoleSid = 0L;
       int dataScopeInteger = 8;
       for (Role r : roleList) {
           int ds = Integer.parseInt(r.getDataScope());
           if (ds == 9) {
               maxRoleSid = r.getRoleSid();
               break;
           } else if (ds < dataScopeInteger) {
               dataScopeInteger = ds;
               maxRoleSid = r.getRoleSid();
           }
       }

       return maxRoleSid;
   }

   public static Long getCurrentOrgSidNoException(AuthAsyncVo authAsyncVo) {
       try {
           return getCurrentOrgSid();
       } catch (Exception e) {
           if (authAsyncVo != null) {
               return authAsyncVo.getOrgSid();
           }
       }
       return null;
   }

   public static <T> T replaceUserToInvoke(Supplier<T> supplier, Long userSid) {
       AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
       Long loginUserSid = null;
       Long loginOrgSid = null;
       if (Objects.nonNull(authUserInfo)) {
           loginUserSid = authUserInfo.getUserSid();
           loginOrgSid = authUserInfo.getOrgSid();
       }
       AuthUser authUser = BeanConvertUtil.convert(BasicInfoUtil.getUserInfoByUserSid(userSid), AuthUser.class);
       AuthUserHolder.setAuthUser(authUser);
       Object var6;
       try {
           T t = supplier.get();
           var6 = t;
       } finally {
           if (Objects.nonNull(loginUserSid)) {
               AuthUser authUserRoll = BeanConvertUtil.convert(BasicInfoUtil.getUserInfoByUserSid(loginUserSid), AuthUser.class);
               AuthUserHolder.setAuthUser(authUserRoll);
           }
           if (Objects.nonNull(loginOrgSid)) {
               AuthUserHolder.setOrg(BeanConvertUtil.convert(BasicInfoUtil.getCurrentOrgInfo(loginOrgSid), Org.class));
           }
       }

       return (T) var6;
   }
}
