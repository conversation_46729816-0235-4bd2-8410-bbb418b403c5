///*
// * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
// */
//
//package cn.com.cloudstar.rightcloud.common.mybatis.interceptor;
//
//import org.apache.ibatis.mapping.BoundSql;
//import org.apache.ibatis.mapping.MappedStatement;
//import org.apache.ibatis.mapping.SqlSource;
//import org.apache.ibatis.plugin.Interceptor;
//
//import java.io.Serializable;
//
///**
// * Mybatis拦截器基类
// * <AUTHOR>
// * @version  V1.0
// * @since 2018-09-07
// */
//public abstract class AbstractInterceptor implements Interceptor, Serializable {
//
//	private static final long serialVersionUID = 1L;
//
//	//see: MapperBuilderAssistant
//    protected MappedStatement copyFromMappedStatement(MappedStatement ms,
//            SqlSource newSqlSource) {
//		MappedStatement.Builder builder = new MappedStatement.Builder(ms.getConfiguration(),
//		ms.getId(), newSqlSource, ms.getSqlCommandType());
//		builder.resource(ms.getResource());
//		builder.fetchSize(ms.getFetchSize());
//		builder.statementType(ms.getStatementType());
//		builder.keyGenerator(ms.getKeyGenerator());
//		if (ms.getKeyProperties() != null && ms.getKeyProperties().length !=0) {
//			StringBuffer keyProperties = new StringBuffer();
//            for(String keyProperty : ms.getKeyProperties()){
//                keyProperties.append(keyProperty).append(",");
//            }
//            keyProperties.delete(keyProperties.length()-1, keyProperties.length());
//			builder.keyProperty(keyProperties.toString());
//		}
//
//		builder.timeout(ms.getTimeout());
//
//		builder.parameterMap(ms.getParameterMap());
//
//		builder.resultMaps(ms.getResultMaps());
//
//		builder.resultSetType(ms.getResultSetType());
//
//		builder.cache(ms.getCache());
//		builder.flushCacheRequired(ms.isFlushCacheRequired());
//		builder.useCache(ms.isUseCache());
//
//		return builder.build();
//	}
//
//    public static class BoundSqlSqlSource implements SqlSource {
//    	BoundSql boundSql;
//
//    	public BoundSqlSqlSource(BoundSql boundSql) {
//			this.boundSql = boundSql;
//		}
//
//		@Override
//		public BoundSql getBoundSql(Object parameterObject) {
//			return boundSql;
//		}
//
//    }
//}
