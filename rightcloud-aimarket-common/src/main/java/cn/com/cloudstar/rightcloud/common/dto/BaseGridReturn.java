/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.common.dto;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.io.Serializable;
import java.util.List;

/**
 * Grid返回对象
 *
 * <AUTHOR>
 */
public class BaseGridReturn<T> implements Serializable {

    /**
     * 总数据条数
     */
    private long totalRows;

    /**
     * 显示数据对象
     */
    private Object data;

    /**
     * 显示数据列表
     */
    private List<T> dataList;

    public BaseGridReturn() {
    }

    public BaseGridReturn(long totalRows, List<T> dataList) {
        this.totalRows = totalRows;
        this.dataList = dataList;
    }

    public BaseGridReturn(IPage<T> page) {
        this.totalRows = page.getTotal();
        this.dataList = page.getRecords();
    }



    public BaseGridReturn(com.github.pagehelper.PageInfo<T> page) {
        this.totalRows = page.getTotal();
        this.dataList = page.getList();
    }

    public BaseGridReturn(Page<T> page, Object data) {
        this.totalRows = page.getTotal();
        this.data = data;
        this.dataList = page.getRecords();
    }

    /**
     * 取得总数据条擿
     *
     * @return 总数据条擿
     */
    public long getTotalRows() {
        return totalRows;
    }

    /**
     * 设置总数据条擿
     */
    public void setTotalRows(long totalRows) {
        this.totalRows = totalRows;
    }

    /**
     * 取得显示数据列表
     *
     * @return data
     */
    public List<?> getDataList() {
        return dataList;
    }

    /**
     * 设置显示数据列表
     */
    public void setDataList(List<T> dataList) {
        this.dataList = dataList;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }
}
