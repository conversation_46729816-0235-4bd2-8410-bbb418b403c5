package cn.com.cloudstar.rightcloud.common.camunda;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;

import org.camunda.bpm.engine.impl.cfg.IdGenerator;
import org.springframework.stereotype.Component;

/**
 * id生成格则
 *
 * <AUTHOR>
 */
@Component
public class CustomIdGeneratorConfiguration implements IdGenerator {

    @Override
    public String getNextId() {
        return String.valueOf(IdWorker.getId());
    }
}
