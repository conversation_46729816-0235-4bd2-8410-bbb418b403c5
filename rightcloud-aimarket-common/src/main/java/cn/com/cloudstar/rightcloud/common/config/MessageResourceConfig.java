/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.common.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.ReloadableResourceBundleMessageSource;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2019/10/17.
 */
@Configuration
public class MessageResourceConfig {

    @Bean
    public ReloadableResourceBundleMessageSource messageSource(){
        ReloadableResourceBundleMessageSource messageSource = new ReloadableResourceBundleMessageSource();
        messageSource.setDefaultEncoding(StandardCharsets.UTF_8.toString());
        messageSource.setUseCodeAsDefaultMessage(Boolean.TRUE);
        messageSource.setCacheSeconds(60);
        messageSource.setBasenames("classpath:messages");
        return messageSource;
    }
}
