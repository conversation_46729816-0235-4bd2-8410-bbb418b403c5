package cn.com.cloudstar.rightcloud.common.dto;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * minio同策略生成参数
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MinioPolicyParam {

    /**
     * 桶名
     */
    String bucketName;
    
    /**
     * 只写前缀
     */
    List<String> writeOnly;

    /**
     * 只读前缀
     */
    List<String> readOnly;

    /**
     * 可读可写前缀
     */
    List<String> readAndWrite;

}
