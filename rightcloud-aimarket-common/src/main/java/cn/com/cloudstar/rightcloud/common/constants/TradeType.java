/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.common.constants;

/**
 * 贸易类型
 *
 * <AUTHOR>
 * @date 2023/08/09
 */
public interface TradeType {

    /**
     * 充值
     */
    String CHARGE = "charge";

    /**
     * 消费
     */
    String PAY = "pay";

    /**
     * 模型集市收入
     */
    String INCOME = "income";

    /**
     * AI退款
     */
    String REFUND = "refund";

    /**
     * 清理
     */
    String CLEARANCE = "clearance";
}
