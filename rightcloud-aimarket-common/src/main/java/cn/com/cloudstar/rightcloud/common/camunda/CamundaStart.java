package cn.com.cloudstar.rightcloud.common.camunda;

import org.camunda.bpm.engine.ProcessEngine;
import org.camunda.bpm.engine.repository.Deployment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class CamundaStart implements CommandLineRunner {

    @Autowired
    private ProcessEngine processEngine;
    private static final String shopDeployName = "aimarket_shop_audit";

    @Override
    public void run(String... args) {
        CamundaHelper.engine = processEngine;
        CamundaHelper.repositoryService = processEngine.getRepositoryService();
        CamundaHelper.historyService = processEngine.getHistoryService();
        CamundaHelper.taskService = processEngine.getTaskService();
        CamundaHelper.runtimeService = processEngine.getRuntimeService();

        log.info("ForceClearDeploy---{}", System.getenv(CamundaHelper.defaultClearDeployKey));
        if (Objects.equals("true", System.getenv(CamundaHelper.defaultClearDeployKey))) {
            clearDeploy();
        }

        startShopAudit();
    }

    private void clearDeploy() {
        log.warn("clearDeploy--清空所有deploy!!!");
        List<Deployment> list = CamundaHelper.repositoryService.createDeploymentQuery().deploymentName(shopDeployName).list();
        for (Deployment deployment : list) {
            CamundaHelper.repositoryService.deleteDeployment(deployment.getId(), true);
        }
    }

    public void startShopAudit() {
        List<Deployment> list = CamundaHelper.repositoryService.createDeploymentQuery()
                                                               .deploymentName(shopDeployName)
                                                               .list();
        if (!list.isEmpty()) {
            for (Deployment deployment : list) {
                if (Objects.equals(deployment.getName(), shopDeployName)) {
                    return;
                }
            }
        }
        log.info("加载bpmn文件:{}", shopDeployName);
        Deployment deploy = CamundaHelper.repositoryService.createDeployment()
                                                           .addClasspathResource(
                                                                   "bpmn/aimarket_shop_audit.bpmn")
                                                           .name(shopDeployName)
                                                           .deploy();
        log.info("加载bpmn文件结果:{}", deploy);
    }
}
