/*
 * Copyright (c) 2018 Cloud-Star, Inc. All Rights Reserved..
 */

package cn.com.cloudstar.rightcloud.common.util;

import freemarker.template.Configuration;

import java.io.File;
import java.io.IOException;

import javax.annotation.concurrent.ThreadSafe;

import lombok.extern.slf4j.Slf4j;

/**
 * FreeMarkerUtil
 *
 * <AUTHOR> create by Shi<PERSON><PERSON>Qiang
 */
@Slf4j
@ThreadSafe
public class FreeMarkerUtil {

    private static FreeMarkerUtil freeMarkerUtil;

    private Configuration configuration;


    public static FreeMarkerUtil getInstance() {
        if (freeMarkerUtil == null) {
            freeMarkerUtil = new FreeMarkerUtil();
        }
        return freeMarkerUtil;
    }

    /**
     * withClassResourceLoaderClassBasePackagePath
     *
     * @param resourceLoaderClass resourceLoader
     * @param basePackagePath     path
     * @return
     */
    public FreeMarkerUtil withClassResourceLoaderClassBasePackagePath(Class resourceLoaderClass,
            String basePackagePath) {
        Configuration cfg = new Configuration(Configuration.VERSION_2_3_23);
        cfg.setClassForTemplateLoading(resourceLoaderClass, basePackagePath);
        this.setConfiguration(cfg);
        return this;
    }

    /**
     * 与文件dir WithFileDir
     *
     * @param dir dir
     * @return {@link FreeMarkerUtil}
     * @throws IOException 异常
     */
    public FreeMarkerUtil withFileDir(File dir) throws IOException {
        Configuration cfg = new Configuration(Configuration.VERSION_2_3_23);
        cfg.setDirectoryForTemplateLoading(dir);
        this.setConfiguration(cfg);
        return this;
    }

    public Configuration getConfiguration() {
        return configuration;
    }

    public void setConfiguration(Configuration configuration) {
        this.configuration = configuration;
    }
}
