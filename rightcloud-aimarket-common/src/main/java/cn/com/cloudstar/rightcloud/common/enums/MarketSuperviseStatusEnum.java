package cn.com.cloudstar.rightcloud.common.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

import java.util.Objects;

/**
 * 市场监管状态枚举
 *
 * <AUTHOR>
 * @date 2023/08/03
 */
@Getter
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@AllArgsConstructor
public enum MarketSuperviseStatusEnum {

    UNDELIVERED("undelivered", "代交付", "创建订阅", "用户"),
    WAIT_ACCEPTANCE("wait_acceptance", "待验收", "供应商交付", "供应商"),
    REJECTED("rejected", "已驳回", "用户驳回", "用户"),
    CANCELLED("cancelled", "已取消", "用户已取消", "用户"),
    RELEASE("release", "订阅中", "商品订阅中", "用户"),
    COMPLETED("completed", "已完成", "确定验收", "用户");
    /**
     * 状态
     */
    private String status;

    /**
     * 描述
     */
    private String desc;

    /**
     * 交付描述
     */
    private String deliverDesc;

    /**
     * 操作角色
     */
    private String operateRole;

    public static String transformDeliverDesc(String status) {
        if (status == null) {
            return "";
        }
        for (MarketSuperviseStatusEnum statusEnum : MarketSuperviseStatusEnum.values()) {
            if (Objects.equals(status, statusEnum.status)) {
                return statusEnum.getDeliverDesc();
            }
        }
        return "";
    }

    public static String transformDeliverRole(String status) {
        if (status == null) {
            return "";
        }
        for (MarketSuperviseStatusEnum statusEnum : MarketSuperviseStatusEnum.values()) {
            if (Objects.equals(status, statusEnum.status)) {
                return statusEnum.getOperateRole();
            }
        }
        return "";
    }
}
