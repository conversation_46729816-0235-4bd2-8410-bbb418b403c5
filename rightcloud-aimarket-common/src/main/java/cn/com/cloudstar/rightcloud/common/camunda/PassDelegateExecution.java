package cn.com.cloudstar.rightcloud.common.camunda;


import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.JavaDelegate;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.common.enums.CamundaExecutionEnum;

/**
 * <AUTHOR>
 */
@Slf4j
@Component("passDelegateExecution")
public class PassDelegateExecution extends BaseProcessExecution implements JavaDelegate {


    @Override
    public void execute(DelegateExecution execution) {
        log.info("businessKey:{},审批通过", execution.getBusinessKey());
        execution(execution);
    }

    @Override
    CamundaExecutionEnum currentTriggerExecution() {
        return CamundaExecutionEnum.AUDIT_PASS;
    }
}
