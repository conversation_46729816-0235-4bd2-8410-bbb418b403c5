package cn.com.cloudstar.rightcloud.common.dto;


import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Throwables;

import org.springframework.beans.BeanUtils;

import cn.hutool.json.JSONUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import cn.com.cloudstar.rightcloud.common.util.json.JsonUtil;

/**
 * 标准 公用条件查询类
 *
 * <AUTHOR>
 * @date 2022/08/05
 */
@Data
@Slf4j
public class Criteria implements Serializable {

    /**
     * 是否相异
     */
    protected boolean distinct;

    /**
     * 排序字段
     */
    protected String orderByClause;

    /**
     * 存放条件查询值
     */
    private Map<String, Object> condition;

    private Integer pageSize;

    private Integer pageNum;

    /**
     * 是否跳过数据过滤, true: 不进行过滤, false: 进行
     **/
    private boolean ignoreDataFilter;

    /**
     * 标准
     *
     * @param example 例子
     */
    protected Criteria(Criteria example) {
        this.orderByClause = example.orderByClause;
        this.condition = example.condition;
        this.distinct = example.distinct;
        this.pageSize = example.pageSize;
        this.pageNum = example.pageNum;
    }

    /**
     * 标准
     *
     * @param key   关键
     * @param value 价值
     */
    public Criteria(String key, Object value) {
        this();
        condition.put(key, value);
    }

    public Criteria() {
        condition = new HashMap<String, Object>();
    }

    /**
     * 清除
     */
    public void clear() {
        condition.clear();
        orderByClause = null;
        distinct = false;
        this.pageSize = null;
        this.pageNum = null;
    }

    /**
     * 把
     *
     * @param condition 查询的条件名称
     * @param value     查询的值
     * @return {@link Criteria}
     */
    public Criteria put(String condition, Object value) {
        this.condition.put(condition, value);
        return this;
    }

    /**
     * 得到键值，C层和S层的参数传递时取值所用<br> 自行转换对象
     *
     * @param key 键值
     * @return 返回指定键所映射的值
     */
    public Object get(String key) {
        return this.condition.get(key);
    }

    /**
     * 设定状态对象 从对象设置 condition参数
     *
     * @param t t
     */
    public <T> void setConditionObject(T t) {
        this.condition = JSONUtil.toBean(JSONUtil.toJsonStr(t), Map.class);
    }

    /**
     * 复制为新的查询条件
     **/
    public Criteria copy() {
        return JSONUtil.toBean(JSONUtil.toJsonStr(this), Criteria.class);
    }

    /**
     * 转换指定对象
     */
    public <T> T getConditionObject(Class<T> clazz) {
        try {
            return JsonUtil.fromJson(JsonUtil.toJson(this.condition), clazz);
        } catch (Exception e) {
            try {
                return clazz.newInstance();
            } catch (InstantiationException | IllegalAccessException exception) {
                log.error(Throwables.getStackTraceAsString(exception));
            }
        }

        return null;
    }

    public static <T> Criteria prepareNewCriteria(T t) {
        Criteria criteria = new Criteria();
        if (t == null) {
            return criteria;
        }

        BeanUtils.copyProperties(t, criteria);
        try {
            criteria.condition = JsonUtil.fromJson(JsonUtil.toJson(t), new TypeReference<Map<String, Object>>() {
            });
        } catch (Exception e) {
        }

        return criteria;
    }
}
