/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.common.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;

import java.util.Collection;
import java.util.List;
import java.util.Map.Entry;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2019/10/18 13:33
 */
public class WrapperUtil {

    private static final List<String> EXCLUDE_PROPERTIES = Lists
        .newArrayList("pagenum", "pagesize", "sortdatafield", "sortorder");

    private static List<String> SUPPORT_METHODS = Lists
        .newArrayList("like", "in", "notIn", "ne", "lt", "gt", "le", "ge", "or");

    private WrapperUtil() {
    }

    public static <T, R> QueryWrapper<R> wrapQuery(T t, String... ignores) {
        QueryWrapper<R> query = Wrappers.query();
        for (Entry<String, Object> entry : BeanUtil.beanToMap(t).entrySet()) {
            String fieldName = entry.getKey();
            Object fieldValue = entry.getValue();
            boolean isSkip =
                ArrayUtil.contains(ignores, fieldName) || EXCLUDE_PROPERTIES.contains(fieldName)
                    || Objects.isNull(fieldValue) || (fieldValue instanceof Collection
                    && ((Collection) fieldValue).isEmpty());
            if (isSkip) {
                continue;
            }

            int keywordIndex = findKeywordIndex(fieldName);
            String columnName = StrUtil.subPre(fieldName, keywordIndex);
            String methodName = StrUtil.lowerFirst(StrUtil.subSuf(fieldName, keywordIndex));

            if (SUPPORT_METHODS.contains(methodName)) {
                ReflectUtil.invoke(query, methodName, true, StrUtil.toUnderlineCase(columnName),
                    fieldValue);
                continue;
            }

            query.eq(StrUtil.toUnderlineCase(fieldName), fieldValue);
        }
        return query;
    }

    private static int findKeywordIndex(CharSequence charSequence) {
        if (Objects.isNull(charSequence)) {
            return -1;
        }

        int length = charSequence.length() - 1;
        StringBuilder searched = new StringBuilder();
        int inIndex = -1;
        for (int i = length; i >= 0; i--) {
            char ch = charSequence.charAt(i);
            searched.append(ch);
            String m = StrUtil.lowerFirst(StrUtil.reverse(searched.toString()));
            if ("in".equals(m)) {
                inIndex = i;
                continue;
            }
            if (SUPPORT_METHODS.contains(m)) {
                return i;
            }
            if (i == 0) {
                return inIndex;
            }
        }

        return -1;
    }

}
