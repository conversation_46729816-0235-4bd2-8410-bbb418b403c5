package cn.com.cloudstar.rightcloud.common.camunda;


import org.camunda.bpm.engine.delegate.DelegateTask;

import java.util.HashMap;
import java.util.Map;

import cn.hutool.extra.spring.SpringUtil;

/**
 * <AUTHOR>
 */
public abstract class BaseProcessTask {

    private Map<String, TaskProcessEngine> engines = new HashMap<>();

    protected void execution(DelegateTask variable) {
        engines.values().forEach(e -> e.execution(variable));
    }

    public void setEngines() {
        this.engines = SpringUtil.getBeansOfType(TaskProcessEngine.class);
    }

    public BaseProcessTask(){
        setEngines();
    }
}
