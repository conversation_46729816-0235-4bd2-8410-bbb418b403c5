package cn.com.cloudstar.rightcloud.common.config;

import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import redis.clients.jedis.HostAndPort;

import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManagerFactory;
import java.security.KeyStore;
import java.security.SecureRandom;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Slf4j
public class SslUtil {

    private static final String AL_TYPE_JKS = "JKS";
    private static final String AL_TYPE_TLS = "TLS";

    /**
     * 它接受一串逗号分隔的主机：端口对并返回一组 HostAndPort 对象
     *
     * @param servers redis服务器的地址，可以是单个地址，也可以是逗号分隔的多个地址。
     * @return 一组 HostAndPort 对象。
     */
    public static Set<HostAndPort> hosts(String servers) {
        final List<String> serverWithPorts = StrUtil.split(servers.trim(), ",");
        Set<HostAndPort> set = new HashSet<>();
        for (String serverWithPort : serverWithPorts) {
            final String[] split = serverWithPort.trim().split(":");
            String host = split[0];
            int port = Integer.parseInt(split[1].trim());
            set.add(new HostAndPort(host, port));
        }
        return set;
    }

    /**
     * > 它使用给定的密钥库和信任库创建一个 `SSLContext`，并返回 `SSLContext` 的 `SSLSocketFactory`
     *
     * @param keyStorePath 密钥库文件的路径。
     * @param keyPw 密钥库的密码。
     * @param trustStorePath 信任存储文件的路径。
     * @param trustPw 信任库的密码。
     * @return 可用于创建 SSL 套接字的套接字工厂。
     */
    public static SSLSocketFactory getSSLSocketFactory(String keyStorePath, String keyPw, String trustStorePath,
                                                       String trustPw) {
        if (StrUtil.isEmpty(trustStorePath)) {
            return null;
        }
        try {
            TrustManagerFactory trustManagerFactory = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
            KeyStore trustStore = getKeyStore(trustStorePath, trustPw);
            trustManagerFactory.init(trustStore);
            SSLContext ctx = SSLContext.getInstance(AL_TYPE_TLS);

            KeyManagerFactory keyManagerFactory = null;
            if (StrUtil.isNotBlank(keyStorePath)) {
                keyManagerFactory = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
                KeyStore keyStore = getKeyStore(keyStorePath, keyPw);
                keyManagerFactory.init(keyStore, keyPw.toCharArray());
            } else {
                ctx.init(null, trustManagerFactory.getTrustManagers(), new SecureRandom());
            }

            return ctx.getSocketFactory();
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return null;
    }

    /**
     * 从 JKS 文件加载密钥库。
     *
     * @param keyStorePath 密钥库文件的路径。
     * @param password 密钥库的密码。
     *
     * @return 一个 KeyStore 对象。
     */
    private static KeyStore getKeyStore(String keyStorePath, String password) throws Exception {
        KeyStore ks = KeyStore.getInstance(AL_TYPE_JKS);
        ks.load(ResourceUtil.getStream(keyStorePath), password.toCharArray());
        return ks;
    }
}
