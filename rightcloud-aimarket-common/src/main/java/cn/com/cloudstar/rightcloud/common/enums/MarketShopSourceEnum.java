package cn.com.cloudstar.rightcloud.common.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR>
 * @description: 商品来源枚举
 * @date 2023/3/15 15:21
 */
@Getter
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@AllArgsConstructor
public enum MarketShopSourceEnum {

    PLATFORM("platform", "平台自营"),
    SUPPLIER("supplier", "供应商"),
    ;

    /**
     * 状态
     */
    private String type;

    /**
     * 描述
     */
    private String desc;

     /**
     * 根据类型获取枚举值
     * @param type 类型
      * @return 枚举值
     */
     public static MarketShopSourceEnum getType(String type) {
         for (MarketShopSourceEnum e : values()) {
             if (e.getType().equals(type)) {
                 return e;
             }
         }
         return null;
     }

}
