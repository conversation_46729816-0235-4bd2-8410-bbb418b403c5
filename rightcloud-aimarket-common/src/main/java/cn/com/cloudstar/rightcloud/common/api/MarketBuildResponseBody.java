/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.common.api;

import cn.com.cloudstar.rightcloud.common.dto.BaseGridReturn;
import cn.com.cloudstar.rightcloud.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.common.util.StringUtil;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.util.ClassUtils;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import java.net.URI;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * The type CustomBuildResponseBody.
 * <p>
 *
 * <AUTHOR>
 * @date 2019/7/1
 */
@ControllerAdvice
@Slf4j
public class MarketBuildResponseBody implements ResponseBodyAdvice {

    /**
     * 定义需要过滤的路径的关键字
     */
    private static final List<String> FILTER_URL_KEY = Lists.newArrayList("/swagger", "/v1/v2/", "/check_signature","/api-docs", "/metrics");

    private static final List<String> CLASS_WHITE_LIST = Lists.newArrayList("IndexController");

    @Override
    public boolean supports(MethodParameter methodParameter, Class clazz) {
        return !CLASS_WHITE_LIST.contains(methodParameter.getDeclaringClass().getSimpleName());
    }

    @Override
    public Object beforeBodyWrite(Object returnValue, MethodParameter methodParameter, MediaType mediaType,
                                  Class clazz,
                                  ServerHttpRequest serverHttpRequest, ServerHttpResponse serverHttpResponse) {

        if (isFilterUrl(serverHttpRequest)) {
            return returnValue;
        }

        if (Objects.isNull(returnValue)) {
            return new RestResult<>();
        }

        if (returnValue instanceof RestResult || StringUtil.equalsIgnoreCase("RestResult", ClassUtils.getUserClass(returnValue).getSimpleName())) {
            return returnValue;
        }

        if (returnValue instanceof Page) {
            return new RestResult(new BaseGridReturn<>((Page<?>) returnValue));
        }

        if (returnValue instanceof IPage) {
            return new RestResult(new BaseGridReturn<>((IPage<?>) returnValue));
        }

        if (returnValue instanceof com.github.pagehelper.PageInfo) {
            return new RestResult(new BaseGridReturn((com.github.pagehelper.PageInfo<?>) returnValue));
        }

        if (returnValue instanceof Map) {
            Map map = (Map) returnValue;
            if (map.get("path") != null){
                map.remove("path");
                return new RestResult(map);
            }
        }
        return new RestResult(returnValue);
    }

    private boolean isFilterUrl(ServerHttpRequest serverHttpRequest) {
        URI uri = serverHttpRequest.getURI();
        String path = uri.getPath();

        return FILTER_URL_KEY.stream().anyMatch(path::contains);
    }
}
