/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.common.config;

import cn.com.cloudstar.rightcloud.common.util.CrytoUtilSimple;
import cn.com.cloudstar.rightcloud.common.constants.AuthConstants;
import cn.com.cloudstar.rightcloud.common.util.JedisUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.jsontype.impl.LaissezFaireSubTypeValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisNode;
import org.springframework.data.redis.connection.RedisSentinelConfiguration;
import org.springframework.data.redis.connection.jedis.JedisClientConfiguration;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import javax.annotation.PostConstruct;
import javax.net.ssl.SSLSocketFactory;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Configuration
@Lazy(value = false)
public class RedisConfig {

    private static final String CLOUDSTAR_REDIS_IS_SENTINEL = "cloudstar.redis.is_sentinel";
    private static final String CLOUDSTAR_REDIS_MASTER = "cloudstar.redis.master";
    private static final String CLOUDSTAR_REDIS_SENTINELS = "cloudstar.redis.sentinels";
    private static final String CLOUDSTAR_REDIS_SENTINEL_CIPHER = "redis.sentinel.password";
    private static final String CLOUDSTAR_REDIS_CIPHER = "redis.password";
    private static final String CLOUDSTART_REDIS_SSL="cloudstar.redis.ssl";
    private static final String CLOUDSTART_REDIS_TRUSTKEYSTORE="cloudstar.redis.trustKeyStore";
    private static final String CLOUDSTART_REDIS_TRUSTPW="redis.trust.store.password";
    //自定义前缀
    private final String prefix = "RC_EN(";
    //自定义后缀
    private final String suffix = ")";
    @Autowired
    private Environment env;

    @Autowired
    @Lazy
    private RedisConnectionFactory connectionFactory;

    @PostConstruct
    public void init() {
        RedisTemplate<Object, Object> objectObjectRedisTemplate = redisTemplate(connectionFactory);
        JedisUtil.INSTANCE.init(objectObjectRedisTemplate);

        // 清空权限缓存
        Set<String> keys =  JedisUtil.INSTANCE.keys(AuthConstants.USER_ACCESS_CACHE, "*");
        JedisUtil.INSTANCE.delete(keys);
    }

    /**
     * 根据配置来判断 单点 还是哨兵
     * @return
     * @throws Exception
     */
    @Bean
    @ConditionalOnProperty(name = CLOUDSTAR_REDIS_IS_SENTINEL, havingValue = "true", matchIfMissing = false)
    public RedisConnectionFactory redisConnectionFactory() throws Exception {
        String master = env.getProperty(CLOUDSTAR_REDIS_MASTER);
        String sentinels = env.getProperty(CLOUDSTAR_REDIS_SENTINELS);
        RedisSentinelConfiguration rsc = new RedisSentinelConfiguration();
        rsc.setMaster(master);
        List<String> redisNodes = Arrays.asList(StrUtil.splitToArray(sentinels, StrUtil.COMMA));
        List<RedisNode> nodes = redisNodes.stream().map(str ->{
            String[] hostPorts = str.split(StrUtil.COLON);
            return new RedisNode(hostPorts[0],Integer.parseInt(hostPorts[1]));
        }).collect(Collectors.toList());
        rsc.setSentinels(nodes);
        rsc.setPassword(env.getProperty(CLOUDSTAR_REDIS_CIPHER));
        String sentinelPassword = StrUtil.emptyToNull(env.getProperty(CLOUDSTAR_REDIS_SENTINEL_CIPHER));
        if(!StrUtil.isEmpty(sentinelPassword)){
            if(isEncrypted(sentinelPassword)){
                sentinelPassword=decrypt(sentinelPassword);
            }
        }
        rsc.setSentinelPassword(sentinelPassword);
        JedisClientConfiguration jedisClientConfig;
        String isSsl = StrUtil.emptyToNull(env.getProperty(CLOUDSTART_REDIS_SSL));
        boolean enSsl=false;
        if (!StrUtil.isEmpty(isSsl)) {
            if (isSsl.equals("true")) {
                enSsl = true;
            }
        }
        if(enSsl){
            String trustPw=env.getProperty(CLOUDSTART_REDIS_TRUSTPW);
            if(!StrUtil.isEmpty(trustPw)){
                if(isEncrypted(trustPw)){
                    trustPw=decrypt(trustPw);
                }
            }
            SSLSocketFactory socketFactory = SslUtil.getSSLSocketFactory(null, null, env.getProperty(CLOUDSTART_REDIS_TRUSTKEYSTORE), trustPw);
            jedisClientConfig = JedisClientConfiguration.builder().useSsl().sslSocketFactory(socketFactory).build();
        }else {
            jedisClientConfig = JedisClientConfiguration.builder().build();
        }
        JedisConnectionFactory jedisConnectionFactory = new JedisConnectionFactory(rsc, jedisClientConfig);
        return jedisConnectionFactory;
    }

    @Bean
    public RedisTemplate<Object, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<Object, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);

        //使用Jackson2JsonRedisSerializer来序列化和反序列化redis的value值
        Jackson2JsonRedisSerializer serializer = new Jackson2JsonRedisSerializer(Object.class);

        ObjectMapper mapper = new ObjectMapper();
        mapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        mapper.activateDefaultTyping(LaissezFaireSubTypeValidator.instance,
                ObjectMapper.DefaultTyping.NON_FINAL, JsonTypeInfo.As.PROPERTY);
        serializer.setObjectMapper(mapper);

        template.setValueSerializer(serializer);
        //使用stringSerializer来序列化和反序列化redis的key值
        RedisSerializer stringSerializer = new StringRedisSerializer();
        template.setKeySerializer(stringSerializer);
        template.afterPropertiesSet();

        return template;
    }

    public boolean isEncrypted(String message) {
        if (StrUtil.isEmpty(message)) {
            return false;
        } else {
            String trimmedValue = message.trim();
            return trimmedValue.startsWith(this.prefix) && trimmedValue.endsWith(this.suffix);
        }
    }
    public String decrypt(String decryptMessage) {

        int prefixIndex = decryptMessage.indexOf(prefix);
        int suffixIndex = decryptMessage.indexOf(suffix);
        //还原密文
        decryptMessage = decryptMessage.substring(prefixIndex+prefix.length(),suffixIndex);
        //还原密码。注意如果需要密钥的这里添加
        return CrytoUtilSimple.decrypt(decryptMessage).trim();
    }

    @Bean(name = "stringRedisTemplate")
    public StringRedisTemplate stringRedisTemplate(RedisConnectionFactory connectionFactory) {

        return new StringRedisTemplate(connectionFactory);
    }

}
