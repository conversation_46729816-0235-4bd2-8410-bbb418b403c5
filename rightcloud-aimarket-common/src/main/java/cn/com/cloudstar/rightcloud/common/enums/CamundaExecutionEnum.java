package cn.com.cloudstar.rightcloud.common.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

@Getter
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@AllArgsConstructor
public enum CamundaExecutionEnum {

    USER_TASK_1("审批节点1"),
    AUDIT_PASS("审批通过"),
    AUDIT_REFUSE("审批拒绝"),
    START_EVENT("开始节点"),
    END_EVENT("结束节点"),
    ;

    /**
     * 执行器类型
     */
    private String executionType;
}
