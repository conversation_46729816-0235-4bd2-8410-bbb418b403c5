/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.common.constants;

import java.util.Arrays;
import java.util.List;

/**
 * @auther <PERSON><PERSON>.Mao
 */
public class AuthConstants {
    public static final String CLAIMS_KEY = "claims";
    public static final String ENTITY_ID = "entity_id";
    public static final int PERIED_TIME = 60 * 60;
    public static final String CACHE_KEY_USER_PREFIX = "portal:user:";
    public static final String CACHE_KEY_ACCOUNT_PREFIX = "portal:account:id";
    public static final String CACHE_KEY_USERID = "portal:account:userid";
    public static final String CACHE_KEY_ROLE_PREFIX = "portal:role:";
    public static final String CACHE_USER_TOKEN = "user:token:";
    public static final String CACHE_USER_TOKEN_EXPIRE = "user:token:expire:";
    public static final String CACHE_USER_TOKEN_LOCK = "user:token:lock:";
    public static final String CACHE_USER_RECENT_ORG = "user:recentOrg:";
    public static final String USER_INFO_PASS = "user:info:pass";
    public static final String CACHE_KEY_ROLE = "system:role:";
    public static long TTL_MILLIS = ********;
    public static String SUBJECT = "inner";
    public static final Long ADMIN_USER_SID = 100L;
    public static final String USER_ACCESS_CACHE = "user:access:";
    public static final List<String> TENANT_ACCESS = Arrays.asList("AdministratorAccess", "TenantAdminAccess", "TenantViewAccess");
    /**
     * header中取user和org的参数
     */
    public static final String HEADER_USER = "header_user_id";
    public static final String HEADER_ORG = "header_org_id";
    public static final String HEADER_ACCOUNT = "account";
    public static final String HEADER_MODULE_TYPE = "moduleType";
    public static final String USER_PERMISSION_CACHE = "user:permission:";
}
