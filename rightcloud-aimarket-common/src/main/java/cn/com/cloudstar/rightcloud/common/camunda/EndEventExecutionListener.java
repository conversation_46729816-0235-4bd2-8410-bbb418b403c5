package cn.com.cloudstar.rightcloud.common.camunda;


import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.ExecutionListener;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.common.enums.CamundaExecutionEnum;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class EndEventExecutionListener extends BaseProcessExecution implements ExecutionListener {

    @Override
    public void notify(DelegateExecution delegateExecution) {
        log.info("businessKey:{},审批结束,审批结果:{}", delegateExecution.getBusinessKey(),
                 delegateExecution.getVariable("audit"));
        execution(delegateExecution);
    }

    @Override
    CamundaExecutionEnum currentTriggerExecution() {
        return CamundaExecutionEnum.END_EVENT;
    }
}
