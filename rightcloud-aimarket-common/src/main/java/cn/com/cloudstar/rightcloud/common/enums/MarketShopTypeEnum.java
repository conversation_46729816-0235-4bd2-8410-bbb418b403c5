package cn.com.cloudstar.rightcloud.common.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023/3/15 15:23
 */
@Getter
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@AllArgsConstructor
public enum MarketShopTypeEnum {

    ALGO("algo", "算法"),
    DATASET("dataset", "数据集"),
    MODEL("model", "模型"),
    APPLICATION("application", "应用");

    /**
     * 状态
     */
    private String type;

    /**
     * 描述
     */
    private String desc;
}
