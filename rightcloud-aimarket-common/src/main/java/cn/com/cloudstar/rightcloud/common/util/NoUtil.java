/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.common.util;

import cn.hutool.core.util.StrUtil;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @since 2019/10/17 11:16
 */
public class NoUtil {

    private static final String DEFAULT_PREFIX = "YHJ";
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter
        .ofPattern("yyyyMMddHHmmssSSS");

    private NoUtil() {
    }

    public static String generateNo(String prefix) {
        String timestamp = getTimestamp();
        return StrUtil.isNotEmpty(prefix) ? prefix + timestamp : generateNo();
    }

    public static String generateNo() {
        return DEFAULT_PREFIX + getTimestamp();
    }

    public static String getTimestamp() {
        LocalDateTime dateTime = LocalDateTime.now();
        return DATE_TIME_FORMATTER.format(dateTime);
    }

    public static String getFlowId(String prefix) {
        return prefix + LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    public static String getNanoTimeId(String prefix) {
        return prefix + System.nanoTime();
    }

    public static Long str2Long(String str) {
        Long num = 0L;
        try {
            num = Long.valueOf(str);
        } catch (Exception e) {
            // do nothing
        }
        return num;
    }
}
