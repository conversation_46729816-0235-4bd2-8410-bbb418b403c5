/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.common.config;

import cn.com.cloudstar.rightcloud.common.certificate.CustomizeTrustStrategy;
import cn.com.cloudstar.rightcloud.common.constants.ModuleTypeConstants;
import cn.com.cloudstar.rightcloud.common.dubbo.route.CMPDubboTag;
import cn.com.cloudstar.rightcloud.common.enums.ReqSource;
import cn.com.cloudstar.rightcloud.common.util.IPAddressUtil;
import cn.com.cloudstar.rightcloud.common.util.PlaintextShieldUtil;
import cn.com.cloudstar.rightcloud.module.support.access.constants.AuthConstants;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.User;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicInfoUtil;
import cn.com.cloudstar.rightcloud.module.support.access.util.StringUtil;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import feign.Client;
import feign.Feign;
import feign.Logger;
import feign.RequestInterceptor;
import feign.codec.Decoder;
import feign.jackson.JacksonEncoder;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.ssl.SSLContextBuilder;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.openfeign.support.SpringDecoder;
import org.springframework.context.annotation.Bean;
import org.springframework.http.MediaType;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.net.ssl.SSLContext;
import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.TimeZone;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class FeignConfig {
//    private static final String AUTH_KEY = "authorization";
//
//    private static final String TOKEN_PREFIX = "Bearer ";

    @Bean
    public Logger.Level level(){
        // 设置Feign的日志级别为FULL
        return Logger.Level.FULL;
    }

    @Bean
    public Feign.Builder feignBuilder(Decoder feignDecoder, RequestInterceptor requestInterceptor) {
        return Feign.builder().decoder(feignDecoder)
                .encoder(new JacksonEncoder())
            .requestInterceptor(requestInterceptor);
    }

    @Bean
    @ConditionalOnProperty(name = "cloudstar.gateway.ssl.enable", havingValue = "true", matchIfMissing = false)
    public Client feignClient() {
        Client client;
        try {
            SSLContext context =
                    new SSLContextBuilder()
                            .loadTrustMaterial(null, CustomizeTrustStrategy.INSTANCE)
                            .build();
            client = new Client.Default(context.getSocketFactory(), NoopHostnameVerifier.INSTANCE);
        } catch (Exception e) {
            client = new Client.Default(null, null);
        }
        return client;
    }


    @Bean
    public RequestInterceptor requestInterceptor() {
        return requestTemplate -> {
            //如果uri等于支付成功通知就不进行权限校验
            if (requestTemplate.url().contains("/notifySuccess/")) {
                return;
            }
            log.info("开始调用,{}", requestTemplate.url());
            User authUser = BasicInfoUtil.getAuthUser();;
            log.debug("调用{},用户信息{}", requestTemplate.url(), PlaintextShieldUtil.eliminatePlaintext(authUser));
            if (authUser == null) {
                return;
            }
            requestTemplate.header("Content-Type", "application/json; charset=UTF-8");
            requestTemplate.header("Accept", "application/json");
            requestTemplate.header(AuthConstants.HEADER_USER, String.valueOf(authUser.getUserSid()));
            requestTemplate.header(AuthConstants.HEADER_REQ_SOURCE, ReqSource.CLOUD_BOSS.name());
            requestTemplate.header(AuthConstants.DUBBO_TAG, CMPDubboTag.getRouteTag());
            requestTemplate.header(AuthConstants.HEADER_ORG, authUser.getOrgSid() == null ? "0"
                    : String.valueOf(authUser.getOrgSid()));
            requestTemplate.header(AuthConstants.ENTITY_ID, (authUser == null || authUser.getEntityId() == null) ? "0"
                    : String.valueOf(authUser.getEntityId()));
            requestTemplate.header("RequstType", AuthConstants.HEADER_TAG);
            try {
                ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
                HttpServletRequest request = requestAttributes.getRequest();
                requestTemplate.header(AuthConstants.HEADER_MODULE_TYPE, request.getHeader(AuthConstants.HEADER_MODULE_TYPE));
                String ip = request.getHeader("RequstIp");
                if (StringUtil.isEmpty(ip)) {
                    ip = IPAddressUtil.getRemoteHostIp(request);
                }
                requestTemplate.header("RequstIp", ip);
            } catch (Exception e) {
                requestTemplate.header(AuthConstants.HEADER_MODULE_TYPE, ModuleTypeConstants.FROM_COMMON);
                log.info("OSS Feign 异常 [{}] ", e.getMessage());
            }
        };
    }


    @Bean
    public Decoder feignDecoder(){
        MappingJackson2HttpMessageConverter jackson2HttpMessageConverter = new MappingJackson2HttpMessageConverter();
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
        objectMapper.setTimeZone(TimeZone.getTimeZone("GMT+8"));
        jackson2HttpMessageConverter.setObjectMapper(objectMapper);

        StringHttpMessageConverter stringHttpMessageConverter = new StringHttpMessageConverter();
        stringHttpMessageConverter.setWriteAcceptCharset(false);

        List<MediaType> supportedMediaTypes = new ArrayList<>();
        supportedMediaTypes.add(MediaType.APPLICATION_JSON);
        supportedMediaTypes.add(MediaType.APPLICATION_JSON_UTF8);
        supportedMediaTypes.add(MediaType.APPLICATION_ATOM_XML);
        supportedMediaTypes.add(MediaType.APPLICATION_FORM_URLENCODED);
        supportedMediaTypes.add(MediaType.APPLICATION_OCTET_STREAM);
        supportedMediaTypes.add(MediaType.APPLICATION_PDF);
        supportedMediaTypes.add(MediaType.APPLICATION_RSS_XML);
        supportedMediaTypes.add(MediaType.APPLICATION_XHTML_XML);
        supportedMediaTypes.add(MediaType.APPLICATION_XML);
        supportedMediaTypes.add(MediaType.IMAGE_GIF);
        supportedMediaTypes.add(MediaType.IMAGE_JPEG);
        supportedMediaTypes.add(MediaType.IMAGE_PNG);
        supportedMediaTypes.add(MediaType.TEXT_EVENT_STREAM);
        supportedMediaTypes.add(MediaType.TEXT_HTML);
        supportedMediaTypes.add(MediaType.TEXT_MARKDOWN);
        supportedMediaTypes.add(MediaType.TEXT_PLAIN);
        supportedMediaTypes.add(MediaType.TEXT_XML);
        jackson2HttpMessageConverter.setSupportedMediaTypes(supportedMediaTypes);

        ObjectFactory<HttpMessageConverters> objectFactory = () -> new HttpMessageConverters(jackson2HttpMessageConverter, stringHttpMessageConverter);
        return new SpringDecoder(objectFactory);
    }


}
