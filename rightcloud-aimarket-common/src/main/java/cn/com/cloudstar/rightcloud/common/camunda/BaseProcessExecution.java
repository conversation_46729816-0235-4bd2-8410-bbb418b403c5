package cn.com.cloudstar.rightcloud.common.camunda;


import org.camunda.bpm.engine.delegate.DelegateExecution;

import java.util.HashMap;
import java.util.Map;

import cn.hutool.extra.spring.SpringUtil;

import cn.com.cloudstar.rightcloud.common.camunda.service.ExecutionProcessEngine;
import cn.com.cloudstar.rightcloud.common.enums.CamundaExecutionEnum;

/**
 * <AUTHOR>
 */
public abstract class BaseProcessExecution {

    private Map<String, ExecutionProcessEngine> engines = new HashMap<>();

    protected void execution(DelegateExecution variable) {
        engines.values().forEach(e -> {
            if (e.currentTriggerExecution() == null || !e.currentTriggerExecution().equals(currentTriggerExecution())) {
                return;
            }
            e.execution(variable);
        });
    }

    public void setEngines() {
        this.engines = SpringUtil.getBeansOfType(ExecutionProcessEngine.class);
    }

    public BaseProcessExecution() {
        setEngines();
    }

    abstract CamundaExecutionEnum currentTriggerExecution();
}
