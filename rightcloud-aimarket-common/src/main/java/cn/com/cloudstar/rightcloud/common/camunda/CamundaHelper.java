package cn.com.cloudstar.rightcloud.common.camunda;

import org.camunda.bpm.engine.HistoryService;
import org.camunda.bpm.engine.ProcessEngine;
import org.camunda.bpm.engine.ProcessEngines;
import org.camunda.bpm.engine.RepositoryService;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.TaskService;
import org.camunda.bpm.engine.repository.Deployment;
import org.camunda.bpm.engine.repository.ProcessDefinition;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
public class CamundaHelper {

    public static ProcessEngine engine;
    public static RepositoryService repositoryService;
    public static HistoryService historyService;
    public static TaskService taskService;
    public static RuntimeService runtimeService;
    public static final String defaultClearDeployKey = "ForceClearDeploy";
    public static final String defaultProcessDefinitionKey = "aimarket_shop_audit";
    public static final String defaultConditionVariable = "audit";
    public static final String defaultLastAssigneeVariable = "lastAssignee";

    public static ProcessDefinition deployment(String filePath, String deployName) {
        Deployment deploy = ProcessEngines.getDefaultProcessEngine().getRepositoryService().createDeployment()
                                          .addClasspathResource(filePath)
                                          .name(deployName)
                                          .deploy();
        log.info("刚创建的deploy:{}", deploy.getName());
        return getDefinitionByDeployId(deploy.getId());
    }


    public static ProcessDefinition getDefinitionByDeployId(String deployId) {
        return ProcessEngines.getDefaultProcessEngine().getRepositoryService().createProcessDefinitionQuery()
                             .deploymentId(deployId)
                             .singleResult();
    }

}
