//package cn.com.cloudstar.rightcloud.common.util;
//
//
//
//import net.lingala.zip4j.ZipFile;
//import net.lingala.zip4j.model.FileHeader;
//import net.lingala.zip4j.model.ZipParameters;
//import net.lingala.zip4j.model.enums.AesKeyStrength;
//import net.lingala.zip4j.model.enums.CompressionLevel;
//import net.lingala.zip4j.model.enums.CompressionMethod;
//import net.lingala.zip4j.model.enums.EncryptionMethod;
//
//import org.apache.commons.compress.utils.IOUtils;
//import org.springframework.util.ObjectUtils;
//
//import java.io.BufferedOutputStream;
//import java.io.ByteArrayInputStream;
//import java.io.ByteArrayOutputStream;
//import java.io.File;
//import java.io.IOException;
//import java.io.InputStream;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.Map.Entry;
//import java.util.UUID;
//
//import cn.hutool.core.io.FileUtil;
//import lombok.extern.slf4j.Slf4j;
//
//import cn.com.cloudstar.rightcloud.common.exception.BizException;
//
//
///**
// * 压缩，解压工具类
// *
// * <AUTHOR>
// */
//@Slf4j
//public class ZipUtil {
//
//    /**
//     * 压缩文件
//     *
//     * @param files      文件，key:文件名 val:文件
//     * @param password   密码
//     * @param encryption 是否加密
//     * <AUTHOR>
//     */
//    public static InputStream compress(Map<String, InputStream> files, String password, boolean encryption) {
//        File tempFile = null;
//        ZipFile zipFile = null;
//        InputStream tempIn = null;
//        try {
//            //初始化zip参数
//            ZipParameters parameters = initZipParameters(password, encryption);
//            //临时生成zip文件
//            tempFile = new File(System.getProperty("java.io.tmpdir"), UUID.randomUUID() + ".zip");
//            //是否设置密码
//            if (!ObjectUtils.isEmpty(parameters) && encryption) {
//                zipFile = new ZipFile(tempFile, password.toCharArray());
//            } else {
//                zipFile = new ZipFile(tempFile);
//            }
//            //压缩文件至zip
//            for (Entry<String, InputStream> entry : files.entrySet()) {
//                String key = entry.getKey();
//                InputStream val = entry.getValue();
//                if (ObjectUtils.isEmpty(key) || ObjectUtils.isEmpty(val)) {
//                    continue;
//                }
//                parameters.setFileNameInZip(key);
//                zipFile.addStream(val, parameters);
//
//                IOUtils.closeQuietly(val);
//            }
//            tempIn = FileUtil.getInputStream(tempFile);
//        } catch (Exception e) {
//            throw new BizException(e);
//        } finally {
//            IOUtils.closeQuietly(zipFile);
//            if (!ObjectUtils.isEmpty(tempFile)) {
//                tempFile.deleteOnExit();
//            }
//        }
//        return tempIn;
//    }
//
//
//    /**
//     * 压缩文件
//     *
//     * @param inputStream 文件
//     * @param fileName    被压缩的文件名
//     * @param password    密码
//     * @param encryption  是否加密
//     * <AUTHOR>
//     */
//    public static InputStream compress(InputStream inputStream, String fileName, String password, boolean encryption)
//            throws Exception {
//        Map<String, InputStream> map = new HashMap<>();
//        map.put(fileName, inputStream);
//        return compress(map, password, encryption);
//    }
//
//
//    /**
//     * 解压文件
//     *
//     * @param inputStream 文件输入流
//     * @param password    解压密码
//     * <AUTHOR>
//     */
//    public static Map<String, InputStream> decompress(InputStream inputStream, String password) {
//        Map<String, InputStream> fileMap = new HashMap<>();
//        File tempFile = null;
//        BufferedOutputStream bos = null;
//        ByteArrayOutputStream outputStream = null;
//        ZipFile zipFile;
//        try {
//            //临时文件
//            tempFile = FileUtil.createTempFile();
//            bos = FileUtil.getOutputStream(tempFile);
//            outputStream = ZipUtil.toOutputStream(inputStream);
//            bos.write(outputStream.toByteArray());
//            //压缩文件
//            zipFile = new ZipFile(tempFile);
//            if (zipFile.isEncrypted()) {
//                if (ObjectUtils.isEmpty(password)) {
//                    throw new BizException("解压密码不能为空");
//                }
//                zipFile.setPassword(password.toCharArray());
//            }
//            List<FileHeader> fileHeaders = zipFile.getFileHeaders();
//            for (FileHeader it : fileHeaders) {
//                try {
//                    String fileName = it.getFileName();
//                    InputStream in = zipFile.getInputStream(it);
//                    fileMap.put(fileName, in);
//                } catch (IOException e) {
//                    log.error("", e);
//                }
//            }
//        } catch (Exception e) {
//            throw new BizException(e);
//        } finally {
//            IOUtils.closeQuietly(inputStream);
//            IOUtils.closeQuietly(bos);
//            IOUtils.closeQuietly(outputStream);
//            FileUtil.del(tempFile);
//        }
//        return fileMap;
//    }
//
//    /**
//     * 克隆inputStream
//     *
//     * @param inputStream 需被克隆的inputStream
//     * <AUTHOR>
//     */
//    public static InputStream cloneInputStream(InputStream inputStream) {
//        ByteArrayOutputStream bos = new ByteArrayOutputStream();
//        try {
//            byte[] buffer = new byte[1024];
//            int len;
//            while ((len = inputStream.read(buffer)) > -1) {
//                bos.write(buffer, 0, len);
//            }
//            bos.flush();
//        } catch (IOException e) {
//            throw new BizException(e);
//        }
//        return new ByteArrayInputStream(bos.toByteArray());
//    }
//
//    /**
//     * 向输出流
//     *
//     * @param in 在
//     * @return {@link ByteArrayOutputStream}
//     * @throws IOException ioexception
//     */
//    public static ByteArrayOutputStream toOutputStream(InputStream in) throws IOException {
//        ByteArrayOutputStream out = new ByteArrayOutputStream();
//        int len;
//        byte[] bytes = new byte[1024];
//        while ((len = in.read(bytes)) != -1) {
//            out.write(bytes, 0, len);
//        }
//        return out;
//    }
//
//
//    /**
//     * 初始化zip参数
//     *
//     * @param password   压缩密码
//     * @param encryption 是否需要密码
//     * <AUTHOR>
//     */
//    private static ZipParameters initZipParameters(String password, boolean encryption) {
//        ZipParameters param = new ZipParameters();
//        //设置压缩方法
//        param.setCompressionMethod(CompressionMethod.DEFLATE);
//        //设置压缩级别
//        param.setCompressionLevel(CompressionLevel.NORMAL);
//        //不需要加密，或密码为空，则直接返回
//        if (!encryption || ObjectUtils.isEmpty(password)) {
//            return param;
//        }
//        //设置需要加密
//        param.setEncryptFiles(true);
//        //设置加密方法
//        param.setEncryptionMethod(EncryptionMethod.AES);
//        //设置AES机密强度
//        param.setAesKeyStrength(AesKeyStrength.KEY_STRENGTH_256);
//        return param;
//    }
//
//
//}
