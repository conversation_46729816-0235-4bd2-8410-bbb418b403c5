package cn.com.cloudstar.rightcloud.common.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

/**
 * 产品统计范围枚举
 *
 * <AUTHOR>
 * @date 2024/11/12
 */
@Getter
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@AllArgsConstructor
public enum ProductStatisticsEnum {

    PRODUCT_COUNT("product_count", "商品数量"),
    TRANSACTION_AMOUNT("transaction_amount", "成交金额"),
    SUBSCRIPTION_COUNT("subscription_count", "订阅次数");

    /**
     * 类型
     */
    private String type;

    /**
     * 描述
     */
    private String desc;


    public static ProductStatisticsEnum getType(String type) {
        for (ProductStatisticsEnum e : ProductStatisticsEnum.values()) {
            if (e.getType().equals(type)) {
                return e;
            }
        }
        return null;
    }
}