<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>rightcloud-aimarket-module</artifactId>
        <groupId>cn.com.cloudstar</groupId>
        <version>vboss.2.6.0-sec-dg-poc-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>rightcloud-aimarket-common</artifactId>

    <properties>
        <flowable.version>6.8.0</flowable.version>
    </properties>

    <dependencies>

        <dependency>
            <groupId>org.springframework.amqp</groupId>
            <artifactId>spring-rabbit</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.com.cloudstar</groupId>
            <artifactId>rightcloud-adapter-core</artifactId>
            <version>${cmp.cloud.driver.version}</version>
        </dependency>

        <!--Camunda -->
        <dependency>
            <groupId>org.camunda.bpm</groupId>
            <artifactId>camunda-engine</artifactId>
        </dependency>
        <dependency>
            <groupId>org.camunda.bpm</groupId>
            <artifactId>camunda-engine-spring</artifactId>
        </dependency>
        <dependency>
            <groupId>org.camunda.bpm</groupId>
            <artifactId>camunda-engine-cdi</artifactId>
        </dependency>
        <dependency>
            <groupId>org.camunda.bpm.dmn</groupId>
            <artifactId>camunda-engine-dmn</artifactId>
        </dependency>
        <dependency>
            <groupId>org.camunda.bpm.javaee</groupId>
            <artifactId>camunda-ejb-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.camunda.bpm</groupId>
            <artifactId>camunda-engine-plugin-spin</artifactId>
        </dependency>
        <dependency>
            <groupId>org.camunda.bpm.model</groupId>
            <artifactId>camunda-bpmn-model</artifactId>
        </dependency>
        <dependency>
            <groupId>org.camunda.bpm.springboot</groupId>
            <artifactId>camunda-bpm-spring-boot-starter-rest</artifactId>
        </dependency>
        <dependency>
            <groupId>org.camunda.bpm.springboot</groupId>
            <artifactId>camunda-bpm-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.camunda.bpm.springboot</groupId>
            <artifactId>camunda-bpm-spring-boot-starter-webapp</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.cloudstar</groupId>
            <artifactId>rightcloud-file-storage</artifactId>
            <version>${cmp.module.support.version}</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <groupId>io.minio</groupId>
                    <artifactId>minio</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

    </dependencies>

</project>
