package cn.com.cloudstar.rightcloud.service.shop.impl;

import cn.com.cloudstar.rightcloud.adapter.core.MQHelper;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiAlgorithmsQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopCreateTo;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopDeleteTo;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopUpdateTo;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.pojo.AlgorithmResponse;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.pojo.NameRes;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.pojo.PurchaseLimit;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.pojo.SkuAttribute;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.pojo.SkuInfo;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiAlgorithmsQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiShopCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiShopCreateToResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiShopDeleteToResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiShopUpdateToResult;
import cn.com.cloudstar.rightcloud.basic.data.pojo.base.Base;
import cn.com.cloudstar.rightcloud.basic.data.pojo.config.SysConfig;
import cn.com.cloudstar.rightcloud.common.constants.RestConst;
import cn.com.cloudstar.rightcloud.common.constants.RestConst.HttpConst;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.dto.Criteria;
import cn.com.cloudstar.rightcloud.common.dto.User;
import cn.com.cloudstar.rightcloud.common.enums.MarketShopSourceEnum;
import cn.com.cloudstar.rightcloud.common.enums.MarketShopStatusEnum;
import cn.com.cloudstar.rightcloud.common.enums.MarketSkuTypeEnum;
import cn.com.cloudstar.rightcloud.common.exception.BizException;
import cn.com.cloudstar.rightcloud.common.pojo.CloudEnv;
import cn.com.cloudstar.rightcloud.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.common.pojo.RestResult.Status;
import cn.com.cloudstar.rightcloud.common.util.AuthUtil;
import cn.com.cloudstar.rightcloud.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.common.util.CrytoUtilSimple;
import cn.com.cloudstar.rightcloud.common.util.DateUtil;
import cn.com.cloudstar.rightcloud.common.util.PageUtil;
import cn.com.cloudstar.rightcloud.common.util.PropertiesUtil;
import cn.com.cloudstar.rightcloud.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.common.util.UuidUtil;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.data.dao.MarketShopMapper;
import cn.com.cloudstar.rightcloud.data.dao.MarketShopSkuMapper;
import cn.com.cloudstar.rightcloud.data.dao.MarketShopSubscribeMapper;
import cn.com.cloudstar.rightcloud.data.dao.MarketShopTagMapper;
import cn.com.cloudstar.rightcloud.data.dao.MarketShopTagRelevanceMapper;
import cn.com.cloudstar.rightcloud.data.dao.SysMOrgMapper;
import cn.com.cloudstar.rightcloud.data.dao.SysMUserMapper;
import cn.com.cloudstar.rightcloud.data.dao.SystemConfigMapper;
import cn.com.cloudstar.rightcloud.data.entity.HcsoUser;
import cn.com.cloudstar.rightcloud.data.entity.MarketShop;
import cn.com.cloudstar.rightcloud.data.entity.MarketShopData;
import cn.com.cloudstar.rightcloud.data.entity.MarketShopPriceJoin;
import cn.com.cloudstar.rightcloud.data.entity.MarketShopSku;
import cn.com.cloudstar.rightcloud.data.entity.MarketShopSkuPrice;
import cn.com.cloudstar.rightcloud.data.entity.MarketShopSkuRelevance;
import cn.com.cloudstar.rightcloud.data.entity.MarketShopSubscribe;
import cn.com.cloudstar.rightcloud.data.entity.MarketShopTag;
import cn.com.cloudstar.rightcloud.data.entity.MarketShopTagRelevance;
import cn.com.cloudstar.rightcloud.data.entity.SysMOrg;
import cn.com.cloudstar.rightcloud.data.entity.SysMUser;
import cn.com.cloudstar.rightcloud.data.request.market.AlgorithmsRequest;
import cn.com.cloudstar.rightcloud.data.request.market.MarketShopAroundPortalRequest;
import cn.com.cloudstar.rightcloud.data.request.market.MarketShopMgtRequest;
import cn.com.cloudstar.rightcloud.data.request.market.MarketShopPortalRequest;
import cn.com.cloudstar.rightcloud.data.request.market.MarketShopRankPortalRequest;
import cn.com.cloudstar.rightcloud.data.request.market.MarketVersionBatchReq;
import cn.com.cloudstar.rightcloud.data.request.market.ShopUpdateStatusReq;
import cn.com.cloudstar.rightcloud.data.request.market.UpdateShopLabelRequest;
import cn.com.cloudstar.rightcloud.data.request.market.UpdateShopStatusRequest;
import cn.com.cloudstar.rightcloud.data.response.market.AiAlgorithmsQueryResponse;
import cn.com.cloudstar.rightcloud.data.response.market.MarketShopDetailPortalResp;
import cn.com.cloudstar.rightcloud.data.response.market.MarketShopDetailResp;
import cn.com.cloudstar.rightcloud.data.response.market.MarketShopMgtResp;
import cn.com.cloudstar.rightcloud.data.response.market.MarketShopPagePortalResp;
import cn.com.cloudstar.rightcloud.data.response.market.MarketShopPriceDetailResp;
import cn.com.cloudstar.rightcloud.data.response.market.MarketShopRankPortalResp;
import cn.com.cloudstar.rightcloud.data.vo.market.MarketShopSkuPriceVo;
import cn.com.cloudstar.rightcloud.data.vo.market.MarketShopSkuVo;
import cn.com.cloudstar.rightcloud.data.vo.market.SkuTreeVO;
import cn.com.cloudstar.rightcloud.driver.pojo.base.BaseResult;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicWebUtil;
import cn.com.cloudstar.rightcloud.service.feign.ResourceServiceFeign;
import cn.com.cloudstar.rightcloud.service.msg.IMarketMessageService;
import cn.com.cloudstar.rightcloud.service.shop.MarketAuditService;
import cn.com.cloudstar.rightcloud.service.shop.MarketShopPriceJoinService;
import cn.com.cloudstar.rightcloud.service.shop.MarketShopService;
import cn.com.cloudstar.rightcloud.service.shop.MarketShopSkuPriceService;
import cn.com.cloudstar.rightcloud.service.shop.MarketShopSkuRelevanceService;
import cn.com.cloudstar.rightcloud.service.shop.MarketShopTagRelevanceService;
import cn.com.cloudstar.rightcloud.service.shop.MarketShopTagService;
import cn.com.cloudstar.rightcloud.service.shop.MarketShopVersionService;
import cn.com.cloudstar.rightcloud.service.util.BaseMockUtil;
import cn.com.cloudstar.rightcloud.service.util.BaseUtil;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * MQHelper.rpc(maPoolsQueryVo); 发送mq消息 BasicWebUtil.prepareInsertParams(req); 赋值 return new RestResult(RestResult.Status.SUCCESS, "添加成功");
 */
@Service
@Slf4j
public class MarketShopServiceImpl extends ServiceImpl<MarketShopMapper, MarketShop> implements MarketShopService {

    /**
     * 按创建时间排序
     */
    private static final String CREATED_DT = "created_dt";

    /**
     * 按访问量排序
     */
    private static final String BROWSE_NUM = "browse_num";

    /**
     * 按访问量排序
     */
    private static final String PUBLISH_DT = "publish_dt";

    /**
     * 授权标识
     */
    private static final String AUTHORIZATION = "true";


    /**
     * 已上架状态
     */
    private static final String ONLINE = "online";

    /**
     * 待审核状态
     */
    private static final String PENDING = "pending";


    /**
     * 资金监管状态两种最终态(取消）
     */
    private static final String CANCELLED = "cancelled";


    /**
     * 资金监管状态两种最终态(完成）
     */
    private static final String COMPLETED = "completed";

    /**
     * 已结算
     */
    private static final String SETTLED = "settled";

    /**
     * 未结算
     */
    private static final String UNSETTLED = "unsettled";

    /**
     * AI模型市场 xw
     */
    private final static Integer shopTypeFlag = 0;

    /**
     * AI集市 ly
     */
    private final static Integer shopTypeFlag1 = 1;

    /**
     * 按月
     */
    private final static Integer UNIT_VALUE = 2;


    /**
     * 算法来源是bms
     */
    private final static String ALGORITHM_SOURCE_BMS = "BMS";


    @Resource
    private MarketShopTagRelevanceService marketShopTagRelevanceService;

    @Resource
    private MarketShopTagService marketShopTagService;

    @Resource
    private MarketShopPriceJoinService marketShopPriceJoinService;

    @Resource
    private MarketShopSkuRelevanceService shopSkuRelevanceService;

    @Resource
    private MarketShopSkuPriceService shopSkuPriceService;

    @Resource
    private MarketShopMapper marketShopMapper;

    @Resource
    private MarketShopSkuMapper shopSkuMapper;

    @Resource
    private SysMUserMapper sysMUserMapper;

    @Resource
    private SysMOrgMapper sysMOrgMapper;

    @Resource
    private MarketShopTagMapper marketShopTagMapper;

    @Resource
    private MarketShopTagRelevanceMapper marketShopTagRelevanceMapper;

    @Resource
    private IMarketMessageService MessageService;

    @Resource
    private MarketAuditService marketAuditService;

    @Resource
    private MarketShopSubscribeMapper marketShopSubscribeMapper;

    @Resource
    private MarketShopVersionService marketShopVersionService;

    @Resource
    private ResourceServiceFeign resourceServiceFeign;

    @Resource
    private SystemConfigMapper systemConfigMapper;

    @SneakyThrows
    @Override
    @Transactional(rollbackFor = Exception.class)
    public RestResult addShop(MarketShop req) {
        // 敏感词验证
        impressiveWordVerification(req);
        // 发布商品根据类型判断是否需要同步到aiHub
        this.setEntity(req);
        this.save(req);

        String shopId = req.getShopId();
        List<Long> labelIds = req.getLabelIds();
        //如果是我的算法商品，默认设置一个交付方式
        if (shopTypeFlag.equals(req.getShopType()) || "3".equals(String.valueOf(req.getShopType()))) {
            String tagId = System.getenv("MARKET_SHOP_TAG");
            if (StrUtil.isNotBlank(tagId)) {
                labelIds.add(Long.valueOf(tagId));
            }
        }
        marketShopTagRelevanceService.saveBatchTag(shopId, labelIds);

        // 保存各种商品类型差别
        List<String> result = this.saveSpec(req);

        // 如果是发布并且供应商，需要生成审批单
        if (MarketShopStatusEnum.PENDING.getType().equals(req.getStatus()) && req.getShopSource().equals(MarketShopSourceEnum.SUPPLIER.getType())) {
            marketAuditService.startShopProcess(shopId);
        }

        return new RestResult(HttpConst.OK, Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS), result);
    }

    /**
     * 敏感词验证
     *
     * @param req 请求
     */
    private void impressiveWordVerification(MarketShop req) {
        Set<String> sensitiveWordList = getSensitiveWordList();
        sensitiveWordList.stream().forEach(word -> {
            if (req.getTitle().contains(word)) {
                throw new BizException("商品名称不能包含敏感词");
            }
            if (req.getIntroduce().contains(word)) {
                throw new BizException("商品简介不能包含敏感词");
            }
            if (req.getDescription().contains(word)) {
                throw new BizException("商品描述不能包含敏感词");
            }
        });
    }

    /**
     * 获取敏感词列表
     *
     * @return {@link Set }<{@link String }>
     */
    private Set<String> getSensitiveWordList() {
        Criteria criteria = new Criteria();
        criteria.put("configType", "sensitive_word_config");
        List<SysConfig> configs = Optional.ofNullable(systemConfigMapper.displaySystemConfigList(criteria)).orElse(new ArrayList<>());
        Set<String> sensitiveWordList = new HashSet<>();
        configs.forEach(sysConfig -> {
            String configValue = Optional.ofNullable(sysConfig.getConfigValue()).orElse("");
            if (StrUtil.isBlank(configValue)) {
                return;
            }
            List<String> strings = Arrays.stream(configValue.replace("；", ";").split(";")).collect(Collectors.toList());
            sensitiveWordList.addAll(strings);
        });
        return sensitiveWordList;
    }

    /**
     * 设置商品入库属性
     *
     * @param req
     */
    private void setEntity(MarketShop req) {
        User authUser = AuthUtil.getAuthUser();
        req.setOwnerId(authUser.getUserSid());
        req.setOrgSid(authUser.getOrgSid());
        if (StrUtil.isBlank(req.getPayType())) {
            if ("2".equals(req.getSellType())) {
                req.setPayType("month");
            } else if ("3".equals(req.getSellType())) {
                req.setPayType("year");
            } else {
                req.setPayType("free");
            }
        }
        BasicWebUtil.prepareInsertParams(req);

        // 模型商品
        if (shopTypeFlag.equals(req.getShopType()) || "3".equals(String.valueOf(req.getShopType()))) {
            if (req.getAlgorithmSource().equals(ALGORITHM_SOURCE_BMS)) {
                req.setAiHubSource(ALGORITHM_SOURCE_BMS);
                req.setAiHubArea(ALGORITHM_SOURCE_BMS);
            } else {
                req.setAiHubSource(PropertiesUtil.getProperty("market_shop_source"));
                req.setAiHubArea(PropertiesUtil.getProperty("market_shop_area"));
            }
        }

        //如果是aihub商品且为发布，则设置状态为发布中，AI模型市场，添加商品后还需要添加商品版本，版本添加成功才算商品发布成功
        if (MarketShopStatusEnum.PENDING.getType().equals(req.getStatus()) && (shopTypeFlag.equals(req.getShopType()) || "3".equals(
                String.valueOf(req.getShopType())))) {
            req.setStatus(MarketShopStatusEnum.RELEASE.getType());
        }

        //如果是aihub商品且为发布，则设置状态为保存中，AI模型市场，添加商品后还需要添加商品版本，版本添加成功才算商品发布成功
        if (MarketShopStatusEnum.UNPUBLISHED.getType().equals(req.getStatus()) && (shopTypeFlag.equals(req.getShopType()) || "3".equals(
                String.valueOf(req.getShopType())))) {
            req.setStatus(MarketShopStatusEnum.SAVING.getType());
        }

        // 管理员发布普通商品不需要审核
        if (req.getShopSource().equals(MarketShopSourceEnum.PLATFORM.getType()) && MarketShopStatusEnum.PENDING.getType().equals(req.getStatus())) {
            req.setStatus(MarketShopStatusEnum.ONLINE.getType());
        }

        //设置排序字段
        MarketShop marketShop = marketShopMapper.selectSortTopShop();
        req.setSortOrder(Objects.isNull(marketShop) ? 1 : marketShop.getSortOrder() + 1);
    }

    @SneakyThrows
    @Override
    @Transactional(rollbackFor = Exception.class)
    public RestResult updateShop(MarketShop req) {
        MarketShop marketShop = this.getById(req.getShopId());
        Assert.notNull(marketShop, WebUtil.getMessage(MsgCd.ERROR_UPDATE_FAILURE));
        Assert.isTrue(MarketShopStatusEnum.checkUpdateStatus(marketShop.getStatus()), WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));

        String shopId = req.getShopId();
        if (MarketShopStatusEnum.PENDING.getType().equals(req.getStatus()) && marketShop.getShopSource()
                                                                                        .equals(MarketShopSourceEnum.PLATFORM.getType())) {
            req.setStatus(MarketShopStatusEnum.ONLINE.getType());
        }
        this.updateById(req);

        // 如果是发布并且是供应商，需要生成审批单
        if (MarketShopStatusEnum.PENDING.getType().equals(req.getStatus()) && marketShop.getShopSource()
                                                                                        .equals(MarketShopSourceEnum.SUPPLIER.getType())) {
            marketAuditService.startShopProcess(shopId);
        }

        // 标签
        marketShopTagRelevanceService.deleteByShopId(shopId);
        marketShopTagRelevanceService.saveBatchTag(shopId, req.getLabelIds());

        /*
         * 修改  已经被使用过的规格不能删除 规格不能修改只能新增或删除
         *  1.删除规格
         *  2.添加新的规格
         *  3.批量修改商品
         *  怎么修改商品，查询出所有的商品，然后过滤掉被删除的，再去批量修改商品信息
         */

        // 修改
        if (shopTypeFlag.equals(marketShop.getShopType()) || 3 == marketShop.getShopType()) {
            if (!req.getAlgorithmSource().equals(ALGORITHM_SOURCE_BMS)) {
                AiShopUpdateTo aiShopUpdateTo = this.getBatchAiShopCreate(req);
                try {
                    log.info("修改资产参数信息：{}", aiShopUpdateTo);
                    AiShopUpdateToResult shopUpdateToResult = (AiShopUpdateToResult) MQHelper.rpc(aiShopUpdateTo);
                    log.info("修改资产返回信息：{}", aiShopUpdateTo);
                    Assert.isTrue(shopUpdateToResult.isSuccess(), shopUpdateToResult.getErrMsg());
                } catch (Exception e) {
                    log.error("修改商品中-修改资产失败：", e);
                    throw new BizException(WebUtil.getMessage(MsgCd.ERROR_OPERATE_FAILURE));
                }
            }
        }

        // 根据规格、添加商品
        List<MarketShopPriceJoin> savePriceJoinList = req.getSavePriceJoinList();
        if (CollUtil.isNotEmpty(savePriceJoinList)) {
            if (!req.getAlgorithmSource().equals(ALGORITHM_SOURCE_BMS)) {
                List<String> contentIds = saveSpec(req);
                // TODO 如果后面加上重试机制，商品版本可能不在这里添加
                marketShopVersionService.batchAdd(
                        new MarketVersionBatchReq(contentIds, marketShop.getArithmetic(), marketShop.getVersionNum(), marketShop.getVersionDesc(),
                                                  marketShop.getShopId(), ""));
            } else {
                final List<MarketShopPriceJoin> marketShopPriceJoins = marketShopPriceJoinService.selectByShopId(req.getShopId());
                List<String> contentIds = marketShopPriceJoins.stream().map(MarketShopPriceJoin::getContentId).collect(Collectors.toList());
                marketShopVersionService.batchAdd(
                        new MarketVersionBatchReq(contentIds, marketShop.getArithmetic(), marketShop.getVersionNum(), marketShop.getVersionDesc(),
                                                  marketShop.getShopId(), ""));
            }

        }

        //  根据规格、删除商品（删除了没法还原）
        List<String> delPriceAttrsList = req.getDelPriceJoinList();
        if (CollUtil.isNotEmpty(delPriceAttrsList)) {
            if (!req.getAlgorithmSource().equals(ALGORITHM_SOURCE_BMS)) {
                try {
                    log.info("删除资产参数信息：{}", delPriceAttrsList);
                    AiShopDeleteTo aiShopDeleteTo = new AiShopDeleteTo(delPriceAttrsList);
                    String flag = System.getenv("LARGE_MODEL_MOCK");
                    if ("true".equals(flag)) {
                        BaseMockUtil.extracted(aiShopDeleteTo);
                    } else {
                        BaseUtil.extracted(aiShopDeleteTo);
                    }
                    AiShopDeleteToResult shopDeleteResult = (AiShopDeleteToResult) MQHelper.rpc(aiShopDeleteTo);
                    log.info("删除资产返回信息：{}", shopDeleteResult);
                    Assert.isTrue(shopDeleteResult.isSuccess(), shopDeleteResult.getErrMsg());
                    marketShopPriceJoinService.removeByContentIds(delPriceAttrsList);
                    marketShopVersionService.removeByContentIds(delPriceAttrsList);
                } catch (Exception e) {
                    log.error("修改商品中-删除资产失败：", e);
                    throw new BizException(WebUtil.getMessage(MsgCd.ERROR_OPERATE_FAILURE));
                }
            } else {
                marketShopPriceJoinService.removeByContentIds(delPriceAttrsList);
                marketShopVersionService.removeByContentIds(delPriceAttrsList);
            }
        }

        List<MarketShopSkuPriceVo> skuPriceVos = req.getShopSkuPriceVos();
        if (CollectionUtil.isNotEmpty(skuPriceVos)) {
            skuPriceVos.forEach(vo -> {
                // sku名称静止修改
                vo.setSkuName(null);
            });
            List<MarketShopSkuPrice> prices = BeanUtil.copyToList(skuPriceVos, MarketShopSkuPrice.class);
            shopSkuPriceService.updateBatchById(prices);
        }
        return RestResult.newSuccess();
    }

    /**
     * 修改单个商品信息
     *
     * @param shopData 可修改信息
     *
     * @return 是否成功
     */
    @SneakyThrows
    @Override
    public void updateShopData(MarketShopData shopData) {
        final MarketShop shop = this.marketShopMapper.selectById(shopData.getShopId());
        if (!shop.getAiHubSource().equals(ALGORITHM_SOURCE_BMS)) {
            AiShopUpdate aiShopUpdate = BeanUtil.toBean(shopData, AiShopUpdate.class);
            aiShopUpdate.setContent_id(shopData.getContentId());
            aiShopUpdate.setDescription(shopData.getDescription());
            aiShopUpdate.setShort_desc(shopData.getIntroduce());
            aiShopUpdate.setVisibility(shopData.getVisibility());
            aiShopUpdate.setWhitelist_users(shopData.getGroupUsers());
            String flag = System.getenv("LARGE_MODEL_MOCK");
            if ("true".equals(flag)) {
                BaseMockUtil.extracted(aiShopUpdate);
            } else {
                BaseUtil.extracted(aiShopUpdate, shop.getOwnerId());
            }
            log.info("修改资产属性参数信息：{}", aiShopUpdate);
            BaseResult result = (BaseResult) MQHelper.rpc(aiShopUpdate);
            log.info("修改资产属性返回信息：{}", result);
            Assert.isTrue(result.isSuccess(), result.getErrMsg());
        }
    }


    @Override
    public RestResult updateShopStatus(ShopUpdateStatusReq req) {
        MarketShop marketShop = this.getById(req.getShopId());
        Assert.notNull(marketShop, WebUtil.getMessage(MsgCd.WARNING_QUERY_FAILURE));
        String reqStatus = req.getStatus();
        String status = marketShop.getStatus();
        if (MarketShopStatusEnum.OFFLINE.getType().equals(reqStatus)
                && !MarketShopStatusEnum.ONLINE.getType().equals(status)) {
            return RestResult.newFail("当前状态下不可修改，请稍后重试");
        }
        if (MarketShopStatusEnum.ONLINE.getType().equals(reqStatus)
                && !MarketShopStatusEnum.OFFLINE.getType().equals(status)) {
            return RestResult.newFail("当前状态下不可修改，请稍后重试");
        }

        // 上架改成待审批
        if (MarketShopStatusEnum.ONLINE.getType().equals(reqStatus) && MarketShopSourceEnum.SUPPLIER.getType().equals(marketShop.getShopSource())) {
            req.setStatus(MarketShopStatusEnum.PENDING.getType());
            marketAuditService.startShopProcess(req.getShopId());
        }
        this.updateById(BeanUtil.toBean(req, MarketShop.class));

        return RestResult.newSuccess();
    }

    @Override
    public void addCollectNum(String shopId) {
        marketShopMapper.addCollectNum(shopId);
    }

    @Override
    public void addSubscribeNum(String shopId) {
        marketShopMapper.addSubscribeNum(shopId);
    }

    @Override
    public void delCollectNum(String shopId) {
        marketShopMapper.delCollectNum(shopId);
    }

    @Override
    public RestResult<IPage<MarketShopPagePortalResp>> selectPortalShops(MarketShopPortalRequest shopReq) {
        if (StrUtil.isBlank(shopReq.getPagenum()) || StrUtil.isBlank(shopReq.getPagesize())) {
            shopReq.setPagesize("6");
            shopReq.setPagenum("0");
        }
        // 默认以创建时间倒序
        if (StrUtil.isBlank(shopReq.getSortdatafield()) || StrUtil.isBlank(shopReq.getSortorder())) {
            shopReq.setSortdatafield("sort_order");
            shopReq.setSortorder("desc");
        }
        //下面这段逻辑由于需求几次变更导致逻辑反复修改
        List<Long> parentIdList = new ArrayList<>();
        List<Long> parentIds = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(shopReq.getLabelIds())) {
            QueryWrapper<MarketShopTag> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().in(MarketShopTag::getParentId, shopReq.getLabelIds());
            List<MarketShopTag> marketShopTags = marketShopTagMapper.selectList(queryWrapper);
            if (CollectionUtil.isNotEmpty(marketShopTags)) {
                parentIds = marketShopTags.stream().map(MarketShopTag::getParentId).distinct().collect(Collectors.toList());
                parentIdList = marketShopTags.stream().map(MarketShopTag::getId).distinct().collect(Collectors.toList());
            }
            parentIdList.addAll(parentIds);
            //移除拥有子标签的父标签id(and连接）
            shopReq.getLabelIds().removeAll(parentIds);
        }
        //labelList:拥有子标签的标签id集合(包括它本身），由于父标签也可以关联（or连接）
        List<Long> labelList = parentIdList.stream().distinct().collect(Collectors.toList());
        IPage<MarketShop> page = PageUtil.preparePageParams(shopReq);
        Criteria criteria = new Criteria();
        List<String> shopTypeList = new ArrayList<>();
        if (StrUtil.isNotBlank(shopReq.getShopTypes())) {
            shopTypeList = Arrays.asList(shopReq.getShopTypes().split(","));
            shopReq.setShopTypes(null);
        }
        criteria.setConditionObject(shopReq);
        criteria.put("shopTypeList", shopTypeList);
        criteria.put("status", "online");
        criteria.put("parentIds", labelList);
        IPage<MarketShop> pageResult = marketShopMapper.selectShopPage(page, criteria.getCondition());

        return new RestResult(BeanConvertUtil.convertPage(pageResult, MarketShopPagePortalResp.class));
    }

    @Override
    public RestResult<List<MarketShopRankPortalResp>> selectPortalShopsByRank(MarketShopRankPortalRequest shopReq) {
        int num = Objects.isNull(shopReq.getNum()) ? 5 : shopReq.getNum();
        String orderTag = StrUtil.isBlank(shopReq.getOrderTag()) ? PUBLISH_DT : shopReq.getOrderTag();
        Criteria criteria = new Criteria();
        criteria.put("num", num);
        criteria.put("status", "online");
        if (!Objects.isNull(shopReq.getShopType())) {
            criteria.put("shopType", shopReq.getShopType());
        }
        List<String> shopTypeList = new ArrayList<>();
        if (StrUtil.isNotBlank(shopReq.getShopTypes())) {
            shopTypeList = Arrays.asList(shopReq.getShopTypes().split(","));
        }
        criteria.put("shopTypeList", shopTypeList);
        if (CREATED_DT.equals(orderTag)) {
            criteria.setOrderByClause(" created_dt desc ");
            // 查询近7天的标识
            criteria.put("time", true);
        }
        if (BROWSE_NUM.equals(orderTag)) {
            criteria.setOrderByClause(" browse_num desc ");
        }
        if (PUBLISH_DT.equals(orderTag)) {
            criteria.setOrderByClause(" publish_dt desc ");
            // 查询近7天的标识
            criteria.put("time", true);
        }
        List<MarketShop> marketShops = marketShopMapper.selectShopByRank(criteria);
        List<MarketShopRankPortalResp> marketShopRankResps = BeanUtil.copyToList(marketShops, MarketShopRankPortalResp.class);
        return new RestResult(marketShopRankResps);
    }

    @Override
    public RestResult<MarketShopDetailPortalResp> selectAroundDetailsById(MarketShopAroundPortalRequest request) {
        String shopId = request.getShopId();
        MarketShop marketShop = this.getById(shopId);
        Assert.notNull(marketShop, WebUtil.getMessage(MsgCd.WARNING_QUERY_FAILURE));

        MarketShopDetailPortalResp result = new MarketShopDetailPortalResp();

        BeanUtil.copyProperties(marketShop, result);

        List<MarketShopTag> list = marketShopTagService.selectByShopId(shopId);
        result.setLabels(list);

        if (shopTypeFlag.equals(marketShop.getShopType()) || 3 == marketShop.getShopType()) {
            result.setPriceJoins(marketShopPriceJoinService.selectByShopId(shopId));
            result.setShopVersions(marketShopVersionService.getByShopId(shopId));
        }
        if (shopTypeFlag1.equals(marketShop.getShopType()) || 4 == marketShop.getShopType()) {
            List<MarketShopSku> shopSkus = shopSkuMapper.selectListByShopId(marketShop.getShopId());
            List<MarketShopSkuVo> marketShopSkuVos = new ArrayList<>();
            shopSkus.forEach(shopSku -> {
                MarketShopSkuVo vo = new MarketShopSkuVo();
                vo.setId(shopSku.getId());
                vo.setAttrName(shopSku.getAttrName());
                vo.setType(shopSku.getType());
                vo.setUnit(shopSku.getUnit());
                if (MarketSkuTypeEnum.ENUM.getType().equals(shopSku.getType())) {
                    vo.setEnums(Arrays.asList(shopSku.getEnumValues().split(",")));
                }

                marketShopSkuVos.add(vo);
            });
            result.setShopSkuVos(marketShopSkuVos);

            QueryWrapper<MarketShopSkuPrice> wrapper = new QueryWrapper<>();
            wrapper.lambda().eq(MarketShopSkuPrice::getShopId, marketShop.getShopId());
            List<MarketShopSkuPrice> prices = shopSkuPriceService.list(wrapper);
            result.setShopSkuPriceVos(BeanUtil.copyToList(prices, MarketShopSkuPriceVo.class));
            SkuTreeVO skuTreeVO = CombinationToTree(prices, marketShopSkuVos);
            result.setSkuTreeVO(skuTreeVO);

        }
        return new RestResult(result);

    }

    @Override
    public void addBrowseNum(String shopId) {
        MarketShop marketShop = marketShopMapper.selectById(shopId);
        if (Objects.isNull(marketShop)) {
            throw new BizException(WebUtil.getMessage(MsgCd.SHOP_NOT_EXISTS));
        }
        marketShop.setBrowseNum(marketShop.getBrowseNum() + 1);
        marketShopMapper.updateById(marketShop);
    }

    @Override
    public RestResult<MarketShopDetailResp> selectShopDetailsById(String shopId) {
        MarketShop marketShop = this.getById(shopId);
        Assert.notNull(marketShop, WebUtil.getMessage(MsgCd.WARNING_QUERY_FAILURE));

        MarketShopDetailResp result = new MarketShopDetailResp();
        BeanUtil.copyProperties(marketShop, result);

        List<MarketShopTag> list = marketShopTagService.selectByShopId(shopId);
        result.setLabels(list);

        if (shopTypeFlag.equals(marketShop.getShopType()) || 3 == marketShop.getShopType()) {
            result.setPriceJoins(marketShopPriceJoinService.selectByShopId(shopId));
        }
        if (shopTypeFlag1.equals(marketShop.getShopType()) || 4 == marketShop.getShopType()) {
            List<MarketShopSku> shopSkus = shopSkuMapper.selectListByShopId(marketShop.getShopId());
            List<MarketShopSkuVo> marketShopSkuVos = new ArrayList<>();
            shopSkus.forEach(shopSku -> {
                MarketShopSkuVo vo = new MarketShopSkuVo();
                vo.setId(shopSku.getId());
                vo.setAttrName(shopSku.getAttrName());
                vo.setType(shopSku.getType());
                vo.setUnit(shopSku.getUnit());
                if (MarketSkuTypeEnum.ENUM.getType().equals(shopSku.getType())) {
                    vo.setEnums(Arrays.asList(shopSku.getEnumValues().split(",")));
                }

                marketShopSkuVos.add(vo);
            });
            result.setShopSkuVos(marketShopSkuVos);

            QueryWrapper<MarketShopSkuPrice> wrapper = new QueryWrapper<>();
            wrapper.lambda().eq(MarketShopSkuPrice::getShopId, marketShop.getShopId());
            List<MarketShopSkuPrice> prices = shopSkuPriceService.list(wrapper);
            result.setShopSkuPriceVos(BeanUtil.copyToList(prices, MarketShopSkuPriceVo.class));
        }
        return new RestResult(result);
    }

    @Override
    public RestResult<Boolean> selectPermissionByUserSid(Long userSid) {

        SysMUser sysMUser = sysMUserMapper.selectById(userSid);
        Boolean permission = AUTHORIZATION.equals(sysMUser.getAuthorizeTag());
        return new RestResult(permission);
    }

    @Override
    public RestResult authorize(Long userSid) {
        SysMUser sysMUser = sysMUserMapper.selectById(userSid);
        if (AUTHORIZATION.equals(sysMUser.getAuthorizeTag())) {
            throw new BizException(WebUtil.getMessage(MsgCd.DONOT_AUTHORIZE_AGAIN));
        } else {
            sysMUser.setAuthorizeTag(AUTHORIZATION);
            sysMUserMapper.updateById(sysMUser);
        }
        return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS));
    }

    @Override
    public IPage<MarketShopMgtResp> selectAllShops(MarketShopMgtRequest shopReq) {
        if (StrUtil.isBlank(shopReq.getPagenum()) || StrUtil.isBlank(shopReq.getPagesize())) {
            shopReq.setPagesize("6");
            shopReq.setPagenum("0");
        }
        //默认以排序字段倒序
        if (StrUtil.isBlank(shopReq.getSortdatafield()) || StrUtil.isBlank(shopReq.getSortorder())) {
            shopReq.setSortdatafield("sort_order");
            shopReq.setSortorder("desc");
        }
        Page<MarketShop> page = PageUtil.preparePageParams(shopReq);
        QueryWrapper<MarketShop> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc("sort_order");
        if (!Objects.isNull(shopReq.getOwnerId())) {
            queryWrapper.lambda().eq(MarketShop::getOwnerId, shopReq.getOwnerId());
        }
        if (StringUtils.isNotBlank(shopReq.getShopName())) {
            queryWrapper.lambda().like(MarketShop::getTitle, shopReq.getShopName());
        }
        if (!Objects.isNull(shopReq.getShopSource())) {
            queryWrapper.lambda().eq(MarketShop::getShopSource, shopReq.getShopSource());
        }
        if (StringUtils.isNotBlank(shopReq.getStatus())) {
            queryWrapper.lambda().like(MarketShop::getStatus, shopReq.getStatus());
        }
        if (StringUtils.isNotBlank(shopReq.getIntroduce())) {
            queryWrapper.lambda().eq(MarketShop::getIntroduce, shopReq.getIntroduce());
        }
        if (!Objects.isNull(shopReq.getShopType())) {
            queryWrapper.lambda().eq(MarketShop::getShopType, shopReq.getShopType());
        }
        Page<MarketShop> pageResult = marketShopMapper.selectPage(page, queryWrapper);
        IPage<MarketShopMgtResp> marketCustomerShopRespIPage = BeanConvertUtil.convertPage(pageResult, MarketShopMgtResp.class);
        return marketCustomerShopRespIPage;
    }

    @Override
    public RestResult updateMgtShopStatus(UpdateShopStatusRequest req) {
        MarketShop marketShop = this.getById(req.getShopId());
        Assert.notNull(marketShop, WebUtil.getMessage(MsgCd.WARNING_QUERY_FAILURE));

        String reqStatus = req.getStatus();
        String status = marketShop.getStatus();
        if (!MarketShopStatusEnum.OFFLINE.getType().equals(reqStatus)) {
            return RestResult.newFail("当前状态下不可修改，请稍后重试");
        } else {
            if (!MarketShopStatusEnum.ONLINE.getType().equals(status)) {
                return RestResult.newFail("当前状态下不可修改，请稍后重试");
            } else {
                this.updateById(BeanUtil.toBean(req, MarketShop.class));
                return RestResult.newSuccess();
            }
        }

    }

    @Override
    public RestResult updateShopLabel(UpdateShopLabelRequest req) {
        MarketShop marketShop = this.getById(req.getShopId());
        Assert.notNull(marketShop, "商品不存在！");

        // 标签
        List<Long> labelIds = req.getLabelIds();
        String shopId = req.getShopId();
        marketShopTagRelevanceMapper.deleteById(shopId);
        List<MarketShopTagRelevance> shopTagList = new ArrayList<>();
        for (Long labelId : labelIds) {
            MarketShopTagRelevance tagRelevance = new MarketShopTagRelevance();
            tagRelevance.setShopId(shopId);
            tagRelevance.setTagId(labelId);
            shopTagList.add(tagRelevance);
        }
        marketShopTagRelevanceService.saveBatch(shopTagList);
        return RestResult.newSuccess();
    }

    @Override
    public RestResult authorizeCancel(Long userSid) {

        //校验客户是否存在已上架商品
        QueryWrapper<MarketShop> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(MarketShop::getOwnerId, userSid);
        List<MarketShop> marketShops = marketShopMapper.selectList(queryWrapper);
        if (CollectionUtil.isNotEmpty(marketShops)) {
            List<MarketShop> onlineShops = marketShops.stream()
                                                      .filter(it -> ONLINE.equals(it.getStatus()) || PENDING.equals(it.getStatus()))
                                                      .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(onlineShops)) {
                throw new BizException(WebUtil.getMessage(MsgCd.CANNOT_CANCEL_AUTHORIZE));
            }
        }

        //校验客户是否存在未完成的订单
        QueryWrapper<MarketShopSubscribe> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(MarketShopSubscribe::getShopOwnerId, userSid);
        List<MarketShopSubscribe> marketShopSubscribes = marketShopSubscribeMapper.selectList(wrapper);
        if (CollectionUtil.isNotEmpty(marketShopSubscribes)) {
            List<MarketShopSubscribe> marketShopSubscribeList = marketShopSubscribes.stream()
                                                                                    .filter(it -> !CANCELLED.equals(it.getSuperviseStatus())
                                                                                            && !COMPLETED.equals(
                                                                                            it.getSuperviseStatus()))
                                                                                    .collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(marketShopSubscribeList)) {
                throw new BizException(WebUtil.getMessage(MsgCd.CANNOT_CANCEL_AUTHORIZE_OTHER));
            }
        }

        //取消授权
        SysMUser sysMUser = sysMUserMapper.selectById(userSid);
        sysMUser.setAuthorizeTag("");
        sysMUserMapper.updateById(sysMUser);

        return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS));
    }

    @Override
    public RestResult moveUp(String shopId) {
        MarketShop marketShop = this.getById(shopId);
        Assert.notNull(marketShop, "商品不存在！");

        //查询上一条记录
        MarketShop shop = marketShopMapper.selectUpShop(marketShop.getSortOrder());

        //最上面的记录不能上移
        if (Objects.isNull(shop)) {
            return new RestResult(false);
        }

        //交换两条记录的sort值
        Integer temp = marketShop.getSortOrder();
        marketShop.setSortOrder(shop.getSortOrder());
        shop.setSortOrder(temp);

        //更新到数据库
        marketShopMapper.updateById(marketShop);
        marketShopMapper.updateById(shop);

        return RestResult.newSuccess();
    }

    @Override
    public RestResult moveDown(String shopId) {
        MarketShop marketShop = this.getById(shopId);
        Assert.notNull(marketShop, "商品不存在！");

        //查询下一条记录
        MarketShop shop = marketShopMapper.selectDownShop(marketShop.getSortOrder());

        //最下面的记录不能下移
        if (Objects.isNull(shop)) {
            return new RestResult(false);
        }

        //交换两条记录的sort值
        Integer temp = marketShop.getSortOrder();
        marketShop.setSortOrder(shop.getSortOrder());
        shop.setSortOrder(temp);

        //更新到数据库
        marketShopMapper.updateById(marketShop);
        marketShopMapper.updateById(shop);

        return RestResult.newSuccess();
    }

    @Override
    public RestResult moveTop(String shopId) {

        MarketShop marketShop = this.getById(shopId);
        Assert.notNull(marketShop, "商品不存在！");
        //查询排序最顶层商品
        MarketShop shop = marketShopMapper.selectSortTopShop();
        marketShop.setSortOrder(shop.getSortOrder() + 1);
        marketShopMapper.updateById(marketShop);
        return RestResult.newSuccess();
    }

    @Override
    public RestResult moveBottom(String shopId) {
        MarketShop marketShop = this.getById(shopId);
        Assert.notNull(marketShop, "商品不存在！");
        //查询排序最底层商品
        MarketShop shop = marketShopMapper.selectSortBottomShop();
        marketShop.setSortOrder(shop.getSortOrder() - 1);
        marketShopMapper.updateById(marketShop);
        return RestResult.newSuccess();
    }

    @Override
    public RestResult<MarketShopPriceDetailResp> selectShopInformation(Long userSid) {
        SysMUser sysMUser = sysMUserMapper.selectById(userSid);
        if (Objects.isNull(sysMUser)) {
            throw new BizException(WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        MarketShopPriceDetailResp marketShopPriceDetailResp = new MarketShopPriceDetailResp();

        //供应商名称(组织名称）
        SysMOrg sysMOrg = sysMOrgMapper.selectById(sysMUser.getOrgSid());
        marketShopPriceDetailResp.setUserName(sysMOrg.getOrgName());

        //查询上架商品数量
        Long onlineNum = 0L;
        QueryWrapper<MarketShop> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(MarketShop::getOwnerId, userSid);
        List<MarketShop> marketShops = marketShopMapper.selectList(queryWrapper);
        if (CollectionUtil.isNotEmpty(marketShops)) {
            List<MarketShop> onlineShops = marketShops.stream().filter(it -> ONLINE.equals(it.getStatus())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(onlineShops)) {
                onlineNum = Long.valueOf(onlineShops.size());
            }
        }
        marketShopPriceDetailResp.setOnlineNum(onlineNum);

        //查询订阅单数量和总金额
        Long subscribeNum = 0L;
        BigDecimal subscribeTotalAmount = BigDecimal.ZERO;
        QueryWrapper<MarketShopSubscribe> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(MarketShopSubscribe::getShopOwnerId, userSid);
        List<MarketShopSubscribe> marketShopSubscribes = marketShopSubscribeMapper.selectList(wrapper);
        if (CollectionUtil.isNotEmpty(marketShopSubscribes)) {
            subscribeNum = Long.valueOf(marketShopSubscribes.size());
            subscribeTotalAmount = marketShopSubscribes.stream()
                                                       .map(MarketShopSubscribe::getPrice)
                                                       .filter(Objects::nonNull)
                                                       .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        marketShopPriceDetailResp.setSubscribeNum(subscribeNum);
        marketShopPriceDetailResp.setSubscribeTotalAmount(subscribeTotalAmount.setScale(5, BigDecimal.ROUND_HALF_UP));

        //查询未结算金额和已结算金额
        BigDecimal unsettleAmount = BigDecimal.ZERO;
        BigDecimal settledAmount = BigDecimal.ZERO;
        if (CollectionUtil.isNotEmpty(marketShopSubscribes)) {
            unsettleAmount = marketShopSubscribes.stream()
                                                 .filter(it -> UNSETTLED.equals(it.getSettlementStatus()) && COMPLETED.equals(
                                                         it.getSuperviseStatus()))
                                                 .map(MarketShopSubscribe::getPrice)
                                                 .filter(Objects::nonNull)
                                                 .reduce(BigDecimal.ZERO, BigDecimal::add);

            settledAmount = marketShopSubscribes.stream()
                                                .filter(it -> SETTLED.equals(it.getSettlementStatus()) && COMPLETED.equals(
                                                        it.getSuperviseStatus()))
                                                .map(MarketShopSubscribe::getPrice)
                                                .filter(Objects::nonNull)
                                                .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        marketShopPriceDetailResp.setUnsettleAmount(unsettleAmount.setScale(5, BigDecimal.ROUND_HALF_UP));
        marketShopPriceDetailResp.setSettledAmount(settledAmount.setScale(5, BigDecimal.ROUND_HALF_UP));

        //查询监管中订阅单数量和金额
        Long subscribeNumInRegulation = 0L;
        BigDecimal subscribeAmountInRegulation = BigDecimal.ZERO;
        if (CollectionUtil.isNotEmpty(marketShopSubscribes)) {
            List<MarketShopSubscribe> marketShopSubscribeList = marketShopSubscribes.stream()
                                                                                    .filter(it -> !CANCELLED.equals(it.getSuperviseStatus())
                                                                                            && !COMPLETED.equals(
                                                                                            it.getSuperviseStatus()))
                                                                                    .collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(marketShopSubscribeList)) {
                subscribeNumInRegulation = Long.valueOf(marketShopSubscribeList.size());
                subscribeAmountInRegulation = marketShopSubscribeList.stream()
                                                                     .map(MarketShopSubscribe::getPrice)
                                                                     .filter(Objects::nonNull)
                                                                     .reduce(BigDecimal.ZERO, BigDecimal::add);
            }
        }
        marketShopPriceDetailResp.setSubscribeNumInRegulation(subscribeNumInRegulation);
        marketShopPriceDetailResp.setSubscribeAmountInRegulation(subscribeAmountInRegulation.setScale(5, BigDecimal.ROUND_HALF_UP));

        return new RestResult(marketShopPriceDetailResp);
    }

    @Override
    public int countShopOnlineNum() {

        return marketShopMapper.countShopOnlineNum();
    }

    @Override
    public Page<AiAlgorithmsQueryResponse> selectAlgorithmsList(AlgorithmsRequest request) {
        List<AiAlgorithmsQueryResponse> result = new ArrayList<>();
        AiAlgorithmsQueryResult aiAlgorithmsQueryResult;
        try {
            AiAlgorithmsQuery aiAlgorithmsQuery = new AiAlgorithmsQuery();
            aiAlgorithmsQuery.setLimit(Objects.isNull(request.getLimit()) ? 50 : request.getLimit());
            aiAlgorithmsQuery.setOffset(Objects.isNull(request.getOffset()) ? 0 : request.getOffset());
            aiAlgorithmsQuery.setOrder(StrUtil.isBlank(request.getOrder()) ? "desc" : request.getOrder());
            aiAlgorithmsQuery.setSort_by(StrUtil.isBlank(request.getSort_by()) ? "create_time" : request.getSort_by());
            aiAlgorithmsQuery.setFilter_by(StrUtil.isBlank(request.getFilter_by()) ? "name" : request.getFilter_by());
            aiAlgorithmsQuery.setCustom(Objects.isNull(request.getCustom()) ? true : request.getCustom());
            if (StrUtil.isNotBlank(request.getAlgorithmName())) {
                String search = "source:custom,name:";
                aiAlgorithmsQuery.setSearches(new StringBuilder().append(search).append(request.getAlgorithmName()).toString());
            }
            log.info("查询算法列表参数信息：{}", aiAlgorithmsQuery);
            String flag = System.getenv("LARGE_MODEL_MOCK");
            if ("true".equals(flag)) {
                BaseMockUtil.extracted(aiAlgorithmsQuery);
            } else {
                BaseUtil.extracted(aiAlgorithmsQuery);
            }
            aiAlgorithmsQueryResult = (AiAlgorithmsQueryResult) MQHelper.rpc(aiAlgorithmsQuery);
            log.info("查询算法列表返回信息：{}", aiAlgorithmsQueryResult);
            Assert.isTrue(aiAlgorithmsQueryResult.isSuccess(), aiAlgorithmsQueryResult.getErrMsg());
        } catch (Exception e) {
            log.error("查询算法列表失败：", e);
            throw new BizException(WebUtil.getMessage(MsgCd.ERROR_OPERATE_FAILURE));
        }
        if (CollectionUtil.isNotEmpty(aiAlgorithmsQueryResult.getItems())) {
            for (AlgorithmResponse algorithmResponse : aiAlgorithmsQueryResult.getItems()) {
                AiAlgorithmsQueryResponse aiAlgorithmsQueryResponse = new AiAlgorithmsQueryResponse();
                aiAlgorithmsQueryResponse.setId(algorithmResponse.getMetadata().getId());
                aiAlgorithmsQueryResponse.setName(algorithmResponse.getMetadata().getName());
                if (Objects.nonNull(algorithmResponse.getMetadata().getCreate_time())) {
                    aiAlgorithmsQueryResponse.setCreatedDt(
                            DateUtil.parseDate(DateUtil.formatDate(algorithmResponse.getMetadata().getCreate_time(), "yyyy-MM-dd HH:mm:ss"),
                                               "yyyy-MM-dd HH:mm:ss"));
                }
                aiAlgorithmsQueryResponse.setImage(new StringBuilder().append(algorithmResponse.getJob_config().getEngine().getEngine_name())
                                                                      .append(" | ")
                                                                      .append(algorithmResponse.getJob_config().getEngine().getEngine_version())
                                                                      .toString());
                result.add(aiAlgorithmsQueryResponse);
            }

            int totalPage = (int) Math.ceil((double) result.size() / Long.valueOf(request.getPagesize()));
            int start = (Integer.valueOf(request.getPagenum())) * Integer.valueOf(request.getPagesize());
            int end = Math.min(start + Integer.valueOf(request.getPagesize()), result.size());
            List<AiAlgorithmsQueryResponse> currentPageData = result.subList(start, end);
            Page<AiAlgorithmsQueryResponse> page = new Page<>(Long.valueOf(request.getPagenum()), Long.valueOf(request.getPagesize()), totalPage);
            page.setTotal(result.size());
            page.setRecords(currentPageData);
            return page;
        } else {
            return new Page<>();
        }

    }

    @Override
    public RestResult getShopType() {
        String marketShopType = PropertiesUtil.getProperty("market_shop_type");
        Assert.notBlank(marketShopType, "许可证未授权商品类型");
        //open指代开源项目，开源项目权限是固定拥有的
        marketShopType += ",open";
        marketShopType += ",autoDeployment";
        return new RestResult(Arrays.stream(marketShopType.split(",")).collect(Collectors.toList()));
    }

/*********************   以下封装方法   *****************************/
    /**
     * 组装创建AiHub资产的数据
     *
     * @param req 原始数据
     *
     * @return 数据
     */
    private List<AiShopCreate> getAiShopCreate(MarketShop req) {
        List<AiShopCreate> createList = new ArrayList<>();
        List<MarketShopPriceJoin> savePriceJoinList = req.getSavePriceJoinList();
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (authUser == null) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        List<String> whiteUser = new ArrayList<>();
        String defaultWhiteUser = System.getenv("AIMARKET_DEFAULT_WHITE_USER");
        if (StrUtil.isNotBlank(defaultWhiteUser)) {
            whiteUser.add(defaultWhiteUser);
        } else if ("bss".equals(authUser.getRemark())) {
            RestResult result = resourceServiceFeign.getCloudEnvId();
            CloudEnv cloudEnv = BeanConvertUtil.convert(result.getData(), CloudEnv.class);
            String domainId = JSON.parseObject(CrytoUtilSimple.decrypt(cloudEnv.getAttrData())).getString("domainId");
            whiteUser.add(domainId);
        } else {
            RestResult result = resourceServiceFeign.findHcsoUser(null);
            HcsoUser hcsoUser = BeanConvertUtil.convert(result.getData(), HcsoUser.class);
            whiteUser.add(hcsoUser.getAccountId());
        }

        // 免费只有一个
        if (shopTypeFlag.equals(req.getSellType()) || "3".equals(String.valueOf(req.getShopType()))) {
            AiShopCreate create = BeanUtil.toBean(req, AiShopCreate.class);
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(req.getTitle()).append("-").append("99年");
            create.setTitle(stringBuilder.toString());
            create.setVisibility("group");
            // todo 设置白名单
            create.setGroup_users(whiteUser);
            BeanUtil.copyProperties(savePriceJoinList.get(0), create);
            SkuInfo skuInfo = new SkuInfo();
            skuInfo.setName_res(NameRes.getFree());
            skuInfo.setSku_attribute(new SkuAttribute(99, 3, NameRes.getYear()));
            create.setSku_info(skuInfo);
            createList.add(create);
            return createList;
        }

        // 根据价格规格添加多个商品
        for (MarketShopPriceJoin shopPriceAttr : savePriceJoinList) {
            AiShopCreate create = BeanUtil.toBean(req, AiShopCreate.class);
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(req.getTitle()).append("-").append(shopPriceAttr.getUnitValue()).append(shopPriceAttr.getUnit());
            create.setTitle(stringBuilder.toString());
            create.setVisibility("group");
            //todo 设置白名单
            create.setGroup_users(whiteUser);
            SkuInfo skuInfo = new SkuInfo();
            skuInfo.setName_res(NameRes.getFree());
            skuInfo.setSku_attribute(new SkuAttribute(shopPriceAttr.getUnitValue(), req.getSellType(),
                                                      UNIT_VALUE.equals(req.getSellType()) ? NameRes.getMonth() : NameRes.getYear()));
            skuInfo.setPurchase_limit(new PurchaseLimit(req.getSellType(), shopPriceAttr.getUnitValue()));
            create.setSku_info(skuInfo);
            BeanUtil.copyProperties(shopPriceAttr, create);
            createList.add(create);
        }
        return createList;
    }

    /**
     * 保存规格
     *
     * @param req 商品信息
     *
     * @return AI模型市场才会有返回值，返回的是aihub那边的商品id
     */
    @Transactional(rollbackFor = Exception.class)
    public List<String> saveSpec(MarketShop req) {
        String shopId = req.getShopId();
        Integer shopType = req.getShopType();
        if (shopTypeFlag.equals(shopType) || 3 == shopType) {
            AiShopCreateToResult shopCreateResult;
            Assert.notNull(req.getArithmetic(), "选择的算法不能为空");
            Assert.notNull(req.getSellType(), "商品是否免费不能为空");
            if (req.getAlgorithmSource().equals(ALGORITHM_SOURCE_BMS)) {
                // 添加规格与商品的对应关系
                List<AiShopCreate> shopCreateList = this.getAiShopCreate(req);
                List<MarketShopPriceJoin> marketShopPriceJoinList = new ArrayList<>(shopCreateList.size());
                for (AiShopCreate aiShopCreate : shopCreateList) {
                    MarketShopPriceJoin marketShopPriceJoin = new MarketShopPriceJoin();
                    marketShopPriceJoin.setShopId(shopId);
                    marketShopPriceJoin.setContentUri("");
                    marketShopPriceJoin.setContentId(UuidUtil.getUuid());
                    marketShopPriceJoin.setUnit(aiShopCreate.getUnit());
                    marketShopPriceJoin.setUnitValue(aiShopCreate.getUnitValue());
                    marketShopPriceJoin.setPrice(aiShopCreate.getPrice());
                    marketShopPriceJoinList.add(marketShopPriceJoin);
                }
                marketShopPriceJoinService.saveBatch(marketShopPriceJoinList);
                return marketShopPriceJoinList.stream().map(MarketShopPriceJoin::getContentId).collect(Collectors.toList());
            } else {
                // 构建对象
                List<AiShopCreate> shopCreateList = this.getAiShopCreate(req);
                try {
                    log.info("创建资产参数信息：{}", shopCreateList);
                    AiShopCreateTo aiShopCreateTo = new AiShopCreateTo();
                    String flag = System.getenv("LARGE_MODEL_MOCK");
                    if ("true".equals(flag)) {
                        BaseMockUtil.extracted(aiShopCreateTo);
                    } else {
                        BaseUtil.extracted(aiShopCreateTo);
                    }
                    aiShopCreateTo.setCreateList(shopCreateList);
                    shopCreateResult = (AiShopCreateToResult) MQHelper.rpc(aiShopCreateTo);
                    log.info("创建资产返回信息：{}", shopCreateResult);
                    Assert.isTrue(shopCreateResult.isSuccess(), shopCreateResult.getErrMsg());
                } catch (Exception e) {
                    log.error("创建商品失败：", e);
                    throw new BizException(WebUtil.getMessage(MsgCd.ERROR_CREATE_SHOP));
                }
                List<AiShopCreateResult> finalShopCreateResult = shopCreateResult.getAiShopCreateResults();
                // 添加规格与商品的对应关系
                List<MarketShopPriceJoin> marketShopPriceJoinList = new ArrayList<>(finalShopCreateResult.size());
                for (AiShopCreateResult aiShopCreateResult : finalShopCreateResult) {
                    MarketShopPriceJoin marketShopPriceJoin = new MarketShopPriceJoin();
                    marketShopPriceJoin.setShopId(shopId);
                    marketShopPriceJoin.setContentUri(aiShopCreateResult.getContent_uri());
                    marketShopPriceJoin.setContentId(aiShopCreateResult.getContent_id());
                    BeanUtil.copyProperties(aiShopCreateResult, marketShopPriceJoin);
                    marketShopPriceJoinList.add(marketShopPriceJoin);
                }
                marketShopPriceJoinService.saveBatch(marketShopPriceJoinList);
                return finalShopCreateResult.stream().map(AiShopCreateResult::getContent_id).collect(Collectors.toList());
            }
        }

        if (shopTypeFlag1.equals(shopType) || 4 == shopType) {
            List<MarketShopSkuVo> shopSkuVos = req.getShopSkuVos();
            List<MarketShopSku> shopSkus = new ArrayList<>();
            List<MarketShopSkuRelevance> skuList = new ArrayList<>();
            shopSkuVos.forEach(sku -> {
                MarketShopSkuRelevance skuRelevance = new MarketShopSkuRelevance();
                skuRelevance.setSkuId(sku.getId());
                skuRelevance.setSkuType(sku.getType());
                skuRelevance.setShopId(req.getShopId());
                skuList.add(skuRelevance);

                if (MarketSkuTypeEnum.ENUM.getType().equals(sku.getType())) {
                    MarketShopSku shopSku = new MarketShopSku();
                    shopSku.setId(sku.getId());
                    shopSku.setAvailableStatus(0);
                    shopSkuMapper.updateById(shopSku);
                }
            });
            shopSkuRelevanceService.saveBatch(skuList);

            List<MarketShopSkuPriceVo> skuPriceVos = req.getShopSkuPriceVos();
            if (CollectionUtil.isEmpty(skuPriceVos)) {
                throw new BizException(WebUtil.getMessage(MsgCd.SHOP_SPEC_PRICE_ERROR));
            }
            List<MarketShopSkuPrice> priceList = new ArrayList<>();
            skuPriceVos.forEach(price -> {
                MarketShopSkuPrice skuRelevance = new MarketShopSkuPrice();
                skuRelevance.setSkuName(price.getSkuName());
                skuRelevance.setMonthlyPrice(price.getMonthlyPrice());
                skuRelevance.setYearlyPrice(price.getYearlyPrice());
                skuRelevance.setOneTimePrice(price.getOneTimePrice());
                skuRelevance.setShopId(req.getShopId());
                priceList.add(skuRelevance);
            });
            shopSkuPriceService.saveBatch(priceList);
        }
        return Arrays.asList();
    }

    /**
     * 组装批量修改商品接口
     *
     * @param req
     */
    private AiShopUpdateTo getBatchAiShopCreate(MarketShop req) {
        AiShopUpdateTo result = new AiShopUpdateTo();
        List<AiShopUpdate> updateList = new ArrayList<>();

        List<MarketShopPriceJoin> marketShopPriceJoinList = marketShopPriceJoinService.selectByShopId(req.getShopId());
        List<String> contentIdList = marketShopPriceJoinList.stream().map(MarketShopPriceJoin::getContentId).collect(Collectors.toList());
        Map<String, MarketShopPriceJoin> collect = marketShopPriceJoinList.stream()
                                                                          .collect(Collectors.toMap(MarketShopPriceJoin::getContentId,
                                                                                                    Function.identity(),
                                                                                                    (key1, key2) -> key2));
        for (String contentId : contentIdList) {
            AiShopUpdate update = new AiShopUpdate();
            update.setContent_id(contentId);
            update.setShort_desc(req.getIntroduce());
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(req.getTitle()).append("-").append(collect.get(contentId).getUnitValue()).append(collect.get(contentId).getUnit());
            update.setContent_title(stringBuilder.toString());
            update.setDescription(req.getDescription());
            String flag = System.getenv("LARGE_MODEL_MOCK");
            if ("true".equals(flag)) {
                BaseMockUtil.extracted(update);
            } else {
                BaseUtil.extracted(update);
            }
            updateList.add(update);
        }

        String flag = System.getenv("LARGE_MODEL_MOCK");
        if ("true".equals(flag)) {
            BaseMockUtil.extracted(result);
        } else {
            BaseUtil.extracted(result);
        }
        result.setUpdateList(updateList);
        return result;
    }


    /**
     * @param prices
     * @param marketShopSkuVos
     *
     * @return {@link SkuTreeVO}
     */
    private SkuTreeVO CombinationToTree(List<MarketShopSkuPrice> prices, List<MarketShopSkuVo> marketShopSkuVos) {
        List<String> combinations = prices.stream().map(MarketShopSkuPrice::getSkuName).collect(Collectors.toList());
        List<MarketShopSkuVo> skuVos = marketShopSkuVos.stream()
                                                       .filter(vo -> MarketSkuTypeEnum.ENUM.getType().equals(vo.getType()))
                                                       .collect(Collectors.toList());
        SkuTreeVO root = new SkuTreeVO("Root");

        for (String combination : combinations) {
            String[] parts = combination.split("\\|");
            SkuTreeVO currentNode = root;

            for (String part : parts) {
                SkuTreeVO childNode = findChildNode(currentNode, part);

                if (childNode == null) {
                    childNode = new SkuTreeVO(part);
                    currentNode.getChildren().add(childNode);
                }

                currentNode = childNode;
            }
        }
        NodeNumWrapper nodeNumWrapper = new NodeNumWrapper(0);
        fillTree(root, skuVos, nodeNumWrapper);

        return root;
    }

    private void fillTree(SkuTreeVO node, List<MarketShopSkuVo> skuVos, NodeNumWrapper nodeNumWrapper) {
        List<SkuTreeVO> children = node.getChildren();
        if (CollectionUtil.isEmpty(children)) {
            return;
        }

        for (SkuTreeVO child : children) {
            MarketShopSkuVo marketShopSkuVo = skuVos.get(nodeNumWrapper.getNodeNum());
            child.setAttrName(marketShopSkuVo.getAttrName());

            NodeNumWrapper childNodeNumWrapper = new NodeNumWrapper(nodeNumWrapper.getNodeNum());
            childNodeNumWrapper.increment();
            fillTree(child, skuVos, childNodeNumWrapper);
        }
    }

    private class NodeNumWrapper {

        private int nodeNum;

        public NodeNumWrapper(int nodeNum) {
            this.nodeNum = nodeNum;
        }

        public int getNodeNum() {
            return nodeNum;
        }

        public void increment() {
            nodeNum++;
        }
    }

    private static SkuTreeVO findChildNode(SkuTreeVO node, String label) {
        for (SkuTreeVO child : node.getChildren()) {
            if (child.getLabel().equals(label)) {
                return child;
            }
        }
        return null;
    }


    private static void extracted(Base base) {
        base.setApiKey("WFMGVZ9J3US5XKOZWUAE");
        base.setSecureToken("20u2QysnHuwSPIeNeHrJW63nRdiW08dI9phcxoNd");
        base.setRegion("cn-east-325");
        base.setProviderUrl("https://iam.cn-east-325.aicc.ssc.net.cn/v3");
        base.setVirtEnvType("hcso");
        base.setVirtEnvUuid("dev");
        base.setProviderType("HCSO");
    }

}




