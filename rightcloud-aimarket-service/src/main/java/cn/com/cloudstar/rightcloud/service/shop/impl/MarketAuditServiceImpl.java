package cn.com.cloudstar.rightcloud.service.shop.impl;

import cn.com.cloudstar.rightcloud.basic.data.dao.org.BasicOrgMapper;
import cn.com.cloudstar.rightcloud.basic.data.dao.user.BasicUserMapper;
import cn.com.cloudstar.rightcloud.basic.data.pojo.user.Org;
import cn.com.cloudstar.rightcloud.common.camunda.CamundaHelper;
import cn.com.cloudstar.rightcloud.common.constants.NotificationConsts;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.enums.CamundaShopNodeEnum;
import cn.com.cloudstar.rightcloud.common.enums.ProcessAuditStatusEnum;
import cn.com.cloudstar.rightcloud.common.enums.ProcessStatusEnum;
import cn.com.cloudstar.rightcloud.common.enums.ShopTypeEnum;
import cn.com.cloudstar.rightcloud.common.exception.BizException;
import cn.com.cloudstar.rightcloud.common.util.AuthUtil;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.data.dao.MarketShopMapper;
import cn.com.cloudstar.rightcloud.data.entity.MarketShop;
import cn.com.cloudstar.rightcloud.data.request.market.MarketAuditListReq;
import cn.com.cloudstar.rightcloud.data.request.market.MarketAuditProcessReq;
import cn.com.cloudstar.rightcloud.data.request.market.MarketProcessDetailsReq;
import cn.com.cloudstar.rightcloud.data.response.market.MarketAuditDetailsResp;
import cn.com.cloudstar.rightcloud.data.response.market.MarketAuditListResp;
import cn.com.cloudstar.rightcloud.data.response.market.MarketProcessDetailsResp;
import cn.com.cloudstar.rightcloud.data.response.market.MarketProcessDetailsResp.HighlightNode;
import cn.com.cloudstar.rightcloud.data.response.market.MarketProcessDetailsResp.ProcessSegment;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.User;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicInfoUtil;
import cn.com.cloudstar.rightcloud.service.msg.IMarketMessageService;
import cn.com.cloudstar.rightcloud.service.shop.MarketAuditService;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.PageUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.XmlUtil;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;

import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.compress.utils.IOUtils;
import org.camunda.bpm.engine.history.HistoricProcessInstance;
import org.camunda.bpm.engine.history.HistoricProcessInstanceQuery;
import org.camunda.bpm.engine.history.HistoricTaskInstanceQuery;
import org.camunda.bpm.engine.history.HistoricVariableInstance;
import org.camunda.bpm.engine.repository.Deployment;
import org.camunda.bpm.engine.runtime.ProcessInstance;
import org.camunda.bpm.engine.task.Comment;
import org.camunda.bpm.engine.task.Task;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ClassUtils;
import org.w3c.dom.Document;

import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@FieldDefaults(makeFinal = true, level = lombok.AccessLevel.PRIVATE)
@RequiredArgsConstructor
public class MarketAuditServiceImpl implements MarketAuditService {

    MarketShopMapper marketShopMapper;
    BasicOrgMapper basicOrgMapper;
    IMarketMessageService MessageService;
    BasicUserMapper userMapper;

    @Override
    public IPage<MarketAuditListResp> listAudit(MarketAuditListReq req) {
        HistoricProcessInstanceQuery historicProcessInstanceQuery = CamundaHelper.historyService.createHistoricProcessInstanceQuery()
                                                                                                .processDefinitionKey(
                                                                                                        CamundaHelper.defaultProcessDefinitionKey);
        if (StrUtil.isNotBlank(req.getOrderNo())) {
            historicProcessInstanceQuery.processInstanceId(req.getOrderNo());
        }
        historicProcessInstanceQuery = Objects.equals(ProcessAuditStatusEnum.FINISHED.name(), req.getAuditStatus())
                ? historicProcessInstanceQuery.finished() : historicProcessInstanceQuery.unfinished();

        int pageNum = Integer.parseInt(req.getPagenum());
        int pageSize = Integer.parseInt(req.getPagesize());
        pageNum = PageUtil.getStart(pageNum, pageSize);

        if (StrUtil.isNotBlank(req.getPayType())) {
            historicProcessInstanceQuery.variableValueLike("payType", StrUtil.join(req.getPayType(), "%", "%"));
        }
        if (StrUtil.isNotBlank(req.getProcessStatus())) {
            historicProcessInstanceQuery.variableValueEquals(CamundaHelper.defaultConditionVariable,
                                                             req.getProcessStatus());
        }
        if (StrUtil.isNotBlank(req.getShopName())) {
            historicProcessInstanceQuery.variableValueLike("shopName", StrUtil.join(req.getShopName(), "%", "%"));
        }

        if ("applyTime".equals(req.getSortdatafield()) && "asc".equals(req.getSortorder())) {
            historicProcessInstanceQuery.orderByProcessInstanceStartTime()
                                        .asc();
        } else {
            historicProcessInstanceQuery.orderByProcessInstanceStartTime()
                                        .desc();
        }

        List<HistoricProcessInstance> historicProcessInstances = historicProcessInstanceQuery
                .listPage(pageNum, pageSize);
        IPage<MarketAuditListResp> page = new Page<>(pageNum, pageSize, historicProcessInstanceQuery.count());
        page.setRecords(historicProcessInstances.stream().map(historicProcessInstance -> {
            //业务数据放在variables中
            Map<String, Object> m = new HashMap<>();
            CamundaHelper.historyService.createHistoricVariableInstanceQuery()
                                        .processInstanceId(historicProcessInstance.getId()).list().forEach(
                                 v -> m.put(v.getName(), v.getValue()));
            MarketAuditListResp resp = BeanUtil.toBean(m, MarketAuditListResp.class);

            resp.setOrderNo(historicProcessInstance.getId());
            if (Objects.isNull(historicProcessInstance.getEndTime())) {
                resp.setAuditStatus(ProcessAuditStatusEnum.UNFINISHED.name());
                CamundaHelper.taskService.createTaskQuery()
                                         .processInstanceId(historicProcessInstance.getId())
                                         .active()
                                         .orderByTaskCreateTime()
                                         .desc()
                                         .listPage(0, 1)
                                         .forEach(task -> resp.setProcessStatus(task.getName()));
            } else {
                resp.setAuditStatus(ProcessAuditStatusEnum.FINISHED.name());
                Object variable = CamundaHelper.historyService.createHistoricVariableInstanceQuery()
                                                              .processInstanceId(historicProcessInstance.getId())
                                                              .variableName(CamundaHelper.defaultConditionVariable)
                                                              .singleResult()
                                                              .getValue();
                resp.setProcessStatus(ProcessStatusEnum.valueOf(variable.toString()).getDesc());
            }
            resp.setApplyTime(historicProcessInstance.getStartTime());
            resp.setShopId(historicProcessInstance.getBusinessKey());
            resp.setApplyTime(historicProcessInstance.getStartTime());

            return resp;
        }).collect(Collectors.toList()));
        return page;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.SUPPORTS)
    public String startShopProcess(@NonNull String shopId) {
        long count = CamundaHelper.runtimeService.createProcessInstanceQuery().processDefinitionKey(
                CamundaHelper.defaultProcessDefinitionKey).processInstanceBusinessKey(shopId).count();
        if (count > 0) {
            throw new BizException(WebUtil.getMessage(MsgCd.NOT_SUBMIT_AGAIN));
        }

        MarketShop marketShop = marketShopMapper.selectById(shopId);
        String shopType = ShopTypeEnum.transformDesc(marketShop.getShopType());
        User authUser = BasicInfoUtil.getAuthUser();
        Org org = basicOrgMapper.selectByPrimaryKey(authUser.getCompanyId());
        Map<String, Object> m = MapUtil.ofEntries(
                MapUtil.entry("shopName", marketShop.getTitle()),
                MapUtil.entry("shopId", marketShop.getShopId()),
                MapUtil.entry("shopType", shopType),
                MapUtil.entry("payType", marketShop.getPayType()),
                MapUtil.entry("applyName", marketShop.getTitle()),
                MapUtil.entry("applyDescribe", marketShop.getIntroduce()),
                MapUtil.entry("applyType", "商品上架申请"),

                MapUtil.entry("userEmail", authUser.getEmail()),
                MapUtil.entry("userPhone", authUser.getMobile()),
                MapUtil.entry("userName", authUser.getAccount()),
                MapUtil.entry("companyName", Objects.nonNull(org) ? org.getOrgName() : null)
                /*MapUtil.entry("userEmail", "a@123"),
                MapUtil.entry("userPhone", 13212121212L),
                MapUtil.entry("userName", "张三"),
                MapUtil.entry("companyName", "佳杰云星")*/
        );

        ProcessInstance processInstance;
        try {
            processInstance = CamundaHelper.runtimeService.startProcessInstanceByKey(
                    CamundaHelper.defaultProcessDefinitionKey, shopId, m);
        } catch (NullPointerException e) {
            log.error("未找到商品审核deployment",e);
            checkExistDeploy(CamundaHelper.defaultProcessDefinitionKey);
            throw new BizException(WebUtil.getMessage(MsgCd.SYSTEM_ERROR));
        }
        //通知
        pendingMsg(marketShop, processInstance.getId());
        return processInstance.getId();
    }

    private void checkExistDeploy(String processKey) {
        List<Deployment> list = CamundaHelper.repositoryService.createDeploymentQuery()
                                                               .deploymentName(processKey)
                                                               .list();
        log.info("之前的商品审批流程,count:{}", list.size());
        for (Deployment deployment : list) {
            log.info("deploy:{}", deployment);
        }
        Deployment deploy = CamundaHelper.repositoryService.createDeployment()
                                                           .addClasspathResource(
                                                                   "bpmn/aimarket_shop_audit.bpmn")
                                                           .name(processKey)
                                                           .deploy();
        log.info("发布deploy:{}",deploy);
    }

    /**
     * 待审核发送消息给管理员
     *
     * @param req 要求事情
     */
    private void pendingMsg(MarketShop req, String auditId) {

        // 商品待审核通知
        cn.com.cloudstar.rightcloud.common.dto.User authUser = AuthUtil.getAuthUser();
        Map<String, String> messageContent = Maps.newHashMap();
        messageContent.put("subscribeAccount", authUser.getAccount());
        messageContent.put("subId", req.getShopId());
        messageContent.put("url", "#/appmodel/aimarket/check/" + auditId + "?shopId=" + req.getShopId());
        messageContent.put("shopTitle", req.getTitle());
        messageContent.put("time", cn.hutool.core.date.DateUtil.format(req.getCreatedDt(), "yyyy-MM-dd HH:mm:ss"));
        messageContent.put("shopId", req.getShopId());
        MessageService.sendMessage(authUser.getUserSid(), messageContent, null, NotificationConsts.ConsoleMsg.MarketMsg.BSSMGT_MARKET_PENDING_APPROVAL);
    }

    @Override
    public MarketAuditDetailsResp getAuditDetails(@NonNull String orderNo) {
        Map<String, Object> m = new HashMap<>();
        CamundaHelper.historyService.createHistoricVariableInstanceQuery()
                                    .processInstanceId(orderNo).list().forEach(
                             v -> m.put(v.getName(), v.getValue()));
        MarketAuditDetailsResp resp = BeanUtil.toBean(m, MarketAuditDetailsResp.class);
        HistoricProcessInstance historicProcessInstance = CamundaHelper.historyService.createHistoricProcessInstanceQuery()
                                                                                      .processInstanceId(orderNo)
                                                                                      .singleResult();
        if (Objects.isNull(historicProcessInstance.getEndTime())) {
            resp.setAuditStatus(ProcessAuditStatusEnum.UNFINISHED.name());
        } else {
            resp.setAuditStatus(ProcessAuditStatusEnum.FINISHED.name());
            List<Comment> comments = CamundaHelper.taskService.getProcessInstanceComments(orderNo);
            if (comments.size() > 0) {
                resp.setAuditOpinion(comments.get(0).getFullMessage());
            }

            resp.setProcessStatus(
                    ProcessStatusEnum.valueOf(m.get(CamundaHelper.defaultConditionVariable).toString()).name());
        }
        resp.setOrderNo(historicProcessInstance.getId());
        return resp;
    }

    @Override
    public MarketProcessDetailsResp getProcessDetails(MarketProcessDetailsReq req) {
        MarketProcessDetailsResp resp = new MarketProcessDetailsResp();
        resp.setProcessXml(getXml("bpmn/aimarket_shop_audit.bpmn"));

        List<MarketProcessDetailsResp.ProcessSegment> list = new ArrayList<>();
        int pageNum = Integer.parseInt(req.getPagenum());
        int pageSize = Integer.parseInt(req.getPagesize());
        pageNum = PageUtil.getStart(pageNum, pageSize);
        HistoricProcessInstance processInstance = CamundaHelper.historyService.createHistoricProcessInstanceQuery()
                                                                              .processInstanceId(
                                                                                      req.getOrderNo())
                                                                              .singleResult();
        if (pageNum == 0) {
            MarketProcessDetailsResp.ProcessSegment segment = new ProcessSegment();
            segment.setAuditTime(processInstance.getStartTime());
            HistoricVariableInstance variableInstance = CamundaHelper.historyService.createHistoricVariableInstanceQuery()
                                                                                    .processInstanceId(req.getOrderNo())
                                                                                    .variableName("userName")
                                                                                    .singleResult();
            if (Objects.nonNull(variableInstance)) {
                segment.setAuditAssignee(variableInstance.getValue().toString());
            } else {
                log.warn("未找到userName变量,processInstanceId:{}", req.getOrderNo());
            }
            segment.setSegmentName("提交审批流程");
            segment.setAuditOpinion("提交了一个流程");
            list.add(segment);
        }

        HistoricTaskInstanceQuery taskInstanceQuery = CamundaHelper.historyService.createHistoricTaskInstanceQuery()
                                                                                  .processInstanceId(req.getOrderNo())
                                                                                  .finished()
                                                                                  .orderByHistoricTaskInstanceEndTime()
                                                                                  .asc();
        long count = taskInstanceQuery.count();
        Page<MarketProcessDetailsResp.ProcessSegment> page = new Page<>(pageNum, pageSize, count);
        taskInstanceQuery.listPage(pageNum, pageSize).forEach(task -> {
            MarketProcessDetailsResp.ProcessSegment segment1 = new ProcessSegment();
            segment1.setAuditTime(task.getEndTime());
            segment1.setSegmentName(task.getName());
            List<Comment> comments = CamundaHelper.taskService.getTaskComments(task.getId());
            if (comments.size() > 0) {
                segment1.setAuditOpinion(comments.get(0).getFullMessage());
            }
            segment1.setAuditAssignee(task.getAssignee());
            list.add(segment1);
        });
        page.setRecords(list);
        resp.setProcessList(page);

        List<HighlightNode> highlightNodes = new ArrayList<>();
        resp.setHighlightNodes(highlightNodes);
        highlightNodes.add(new HighlightNode(CamundaShopNodeEnum.START_EVENT.getNodeName(), "success"));
        if (Objects.isNull(processInstance.getEndTime())) {
            highlightNodes.add(new HighlightNode(CamundaShopNodeEnum.USER_TASK.getNodeName(), "current"));
            return resp;
        }

        highlightNodes.add(new HighlightNode(CamundaShopNodeEnum.USER_TASK.getNodeName(), "success"));
        highlightNodes.add(new HighlightNode(CamundaShopNodeEnum.GATEWAY_EVENT.getNodeName(), "success"));
        Object auditResult = CamundaHelper.historyService.createHistoricVariableInstanceQuery()
                                                         .processInstanceId(req.getOrderNo())
                                                         .variableName(CamundaHelper.defaultConditionVariable)
                                                         .singleResult()
                                                         .getValue();
        if (ProcessStatusEnum.APPROVE == ProcessStatusEnum.valueOf(auditResult.toString())) {
            highlightNodes.add(new HighlightNode(CamundaShopNodeEnum.EXECUTE_SERVICE_TASK.getNodeName(), "success"));
        } else {
            highlightNodes.add(new HighlightNode(CamundaShopNodeEnum.REFUSE_SERVICE_TASK.getNodeName(), "error"));
        }
        highlightNodes.add(new HighlightNode(CamundaShopNodeEnum.END_EVENT.getNodeName(), "current"));
        return resp;
    }

    @Override
    public String getXml(String resourceId) {
        InputStream is = ClassUtils.getUserClass(this).getClassLoader().getResourceAsStream(resourceId);
        Document document = XmlUtil.readXML(is);
        String docStr = "";
        ByteArrayOutputStream outputStream = null;
        if (document != null) {
            try {
                TransformerFactory transformerFactory = TransformerFactory.newInstance();
                Transformer transformer = transformerFactory.newTransformer();
                transformer.setOutputProperty("encoding", "UTF-8");
                outputStream = new ByteArrayOutputStream();
                transformer.transform(new DOMSource(document), new StreamResult(outputStream));
                docStr = outputStream.toString("UTF-8");
            } catch (Exception e) {
                log.error("exception message:", e);
            } finally {
                IOUtils.closeQuietly(outputStream);
            }
        }
        return docStr;
    }

    @Override
    public void auditProcess(MarketAuditProcessReq req) {
        List<Task> tasks = CamundaHelper.taskService.createTaskQuery()
                                                    .active()
                                                    .processDefinitionKey(CamundaHelper.defaultProcessDefinitionKey)
                                                    .processInstanceId(req.getOrderNo())
                                                    .active().orderByTaskCreateTime().desc()
                                                    .listPage(0, 1);
        if (tasks.isEmpty()) {
            throw new BizException(WebUtil.getMessage(MsgCd.NO_TASK_FOUND));
        }

        Task task = tasks.get(0);
        String account = BasicInfoUtil.getAuthUser().getAccount();
        CamundaHelper.taskService.setAssignee(task.getId(), account);
        CamundaHelper.taskService.createComment(task.getId(), task.getProcessInstanceId(),
                                                req.getAuditOpinion());
        CamundaHelper.taskService.complete(task.getId(), MapUtil.ofEntries(
                MapUtil.entry(CamundaHelper.defaultConditionVariable,
                              ProcessStatusEnum.valueOf(req.getProcessStatus()).name()),
                MapUtil.entry(CamundaHelper.defaultLastAssigneeVariable, account)
        ));
        //  商品审核通过通知 商品审核拒绝通知
        if ("APPROVE".equals(req.getProcessStatus())){
            messageNotification(req.getOrderNo(), NotificationConsts.ConsoleMsg.MarketMsg.TENANT_MARKET_APPROVAL_SUCCESS);
        }else {
            messageNotification(req.getOrderNo(), NotificationConsts.ConsoleMsg.MarketMsg.TENANT_MARKET_APPROVAL_FAIL);
        }

    }

    /**
     * 消息通知
     *
     * @param orderNo 订单号
     * @param msg     消息
     */
    private void messageNotification(String orderNo, String msg) {
        MarketShop marketShop = marketShopMapper.selectShopByProcessId(orderNo);
        if (Objects.isNull(marketShop)) {
            throw new BizException(String.format(WebUtil.getMessage(MsgCd.ERROR_RES_NOT_FOUND), "商品"));
        }
        cn.com.cloudstar.rightcloud.basic.data.pojo.user.User authUser = userMapper.selectByPrimaryKey(marketShop.getOwnerId());
        if (Objects.isNull(authUser)) {
            throw new BizException(WebUtil.getMessage(MsgCd.SHOP_SUPPLIER_DOES_NOT_EXIST));
        }
        Map<String, String> messageContent = Maps.newHashMap();
        messageContent.put("subscribeAccount", authUser.getAccount());
        messageContent.put("supplierAccount", authUser.getAccount());
        messageContent.put("subId", marketShop.getShopId());
        messageContent.put("url", "#/appmodel/mygoods/" + marketShop.getShopId());
        messageContent.put("shopTitle", marketShop.getTitle());
        messageContent.put("time", DateUtil.format(marketShop.getCreatedDt(), "yyyy-MM-dd HH:mm:ss"));
        messageContent.put("shopId", marketShop.getShopId());
        MessageService.sendMessage(authUser.getUserSid(), messageContent, msg, null);
    }
}
