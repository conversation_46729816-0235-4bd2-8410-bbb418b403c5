package cn.com.cloudstar.rightcloud.service.shop.impl;

import cn.com.cloudstar.rightcloud.basic.data.dao.user.BasicUserMapper;
import cn.com.cloudstar.rightcloud.common.constants.NotificationConsts;
import cn.com.cloudstar.rightcloud.common.constants.TradeType;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.dto.SysMFilePath;
import cn.com.cloudstar.rightcloud.common.dto.User;
import cn.com.cloudstar.rightcloud.common.enums.MarketShopSourceEnum;
import cn.com.cloudstar.rightcloud.common.enums.MarketSuperviseStatusEnum;
import cn.com.cloudstar.rightcloud.common.enums.SysMFileTypeEnum;
import cn.com.cloudstar.rightcloud.common.exception.BizException;
import cn.com.cloudstar.rightcloud.common.mq.request.BaseNotificationMqBean;
import cn.com.cloudstar.rightcloud.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.common.util.AuthUtil;
import cn.com.cloudstar.rightcloud.common.util.CrytoUtilSimple;
import cn.com.cloudstar.rightcloud.common.util.NoUtil;
import cn.com.cloudstar.rightcloud.common.util.PropertiesUtil;
import cn.com.cloudstar.rightcloud.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.data.dao.MarketShopSuperviseRecordMapper;
import cn.com.cloudstar.rightcloud.data.dao.MarketSubscribeMapper;
import cn.com.cloudstar.rightcloud.data.dao.ServiceOrderMapper;
import cn.com.cloudstar.rightcloud.data.dao.SysMFilePathMapper;
import cn.com.cloudstar.rightcloud.data.entity.BizAccountDeal;
import cn.com.cloudstar.rightcloud.data.entity.MarketShop;
import cn.com.cloudstar.rightcloud.data.entity.MarketShopSubscribe;
import cn.com.cloudstar.rightcloud.data.entity.MarketShopSuperviseRecord;
import cn.com.cloudstar.rightcloud.data.entity.ServiceOrder;
import cn.com.cloudstar.rightcloud.data.request.market.MarketAcceptanceReq;
import cn.com.cloudstar.rightcloud.data.request.market.MarketDeliverReq;
import cn.com.cloudstar.rightcloud.data.request.market.MarketOrderPostingRequest;
import cn.com.cloudstar.rightcloud.data.vo.market.MarketAcceptanceVo;
import cn.com.cloudstar.rightcloud.module.support.access.cache.AuthUserHolder;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicWebUtil;
import cn.com.cloudstar.rightcloud.service.feign.BssServiceFeign;
import cn.com.cloudstar.rightcloud.service.msg.IMarketMessageService;
import cn.com.cloudstar.rightcloud.service.order.BizAccountDealService;
import cn.com.cloudstar.rightcloud.service.shop.MarketShopService;
import cn.com.cloudstar.rightcloud.service.shop.MarketShopSuperviseRecordService;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @description 针对表【market_shop_supervise_record】的数据库操作Service实现
 * @createDate 2023-08-03 17:04:17
 */
@Service
@Slf4j
public class MarketShopSuperviseRecordServiceImpl extends ServiceImpl<MarketShopSuperviseRecordMapper, MarketShopSuperviseRecord>
        implements MarketShopSuperviseRecordService {

    @Autowired
    private ServiceOrderMapper serviceOrderMapper;

    @Autowired
    private MarketShopSuperviseRecordMapper shopSuperviseRecordMapper;

    @Autowired
    private MarketShopService marketShopService;

    @Autowired
    private IMarketMessageService marketMessageService;

    @Autowired
    private BizAccountDealService bizAccountDealService;

    @Autowired
    private MarketShopService shopService;

    @Autowired
    private BssServiceFeign orderServiceFeign;

    @Autowired
    private BasicUserMapper userMapper;

    @Autowired
    private MarketSubscribeMapper subscribeMapper;

    @Autowired
    private SysMFilePathMapper sysMFilePathMapper;

    @Resource
    private RabbitTemplate rabbitTemplate;

    private static final String PASS = "pass";

    private static final String REJECT = "reject";

    private static final String COMPLETED = "completed";

    private static final String REFUSED = "lev2_refused";

    private static final String SETTLED = "settled";

    @Override
    @Transactional
    public void deliver(String subscribeId, MarketDeliverReq req) {
        User authUser = AuthUtil.getAuthUser();
        MarketShopSubscribe marketShopSubscribe = subscribeMapper.selectById(subscribeId);
        if (Objects.isNull(marketShopSubscribe)) {
            throw new BizException(String.format(WebUtil.getMessage(MsgCd.ERROR_RES_NOT_FOUND), "订单"));
        }
        String superviseStatus = marketShopSubscribe.getSuperviseStatus();
        if (!(MarketSuperviseStatusEnum.UNDELIVERED.getStatus().equals(superviseStatus)
                || MarketSuperviseStatusEnum.REJECTED.getStatus().equals(superviseStatus))) {
            throw new BizException(WebUtil.getMessage(MsgCd.SHOP_STATUS_CANNOT_DELIVERED));
        }

        String shopId = marketShopSubscribe.getShopId();
        MarketShop shop = marketShopService.getById(shopId);
        if (!authUser.getUserSid().equals(shop.getOwnerId())) {
            throw new BizException(WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }

        MarketShopSubscribe subscribe = new MarketShopSubscribe();
        subscribe.setSubscribeId(marketShopSubscribe.getSubscribeId());
        subscribe.setSuperviseStatus(MarketSuperviseStatusEnum.WAIT_ACCEPTANCE.getStatus());
        marketShopSubscribe.setSuperviseStatus(MarketSuperviseStatusEnum.WAIT_ACCEPTANCE.getStatus());
        subscribeMapper.updateById(subscribe);

        MarketShopSuperviseRecord superviseRecord = new MarketShopSuperviseRecord();
        superviseRecord.setSubscribeId(marketShopSubscribe.getSubscribeId());
        superviseRecord.setCreatedUserId(authUser.getUserSid());
        superviseRecord.setOriginalStatus(marketShopSubscribe.getSuperviseStatus());
        superviseRecord.setUpdateStatus(MarketSuperviseStatusEnum.WAIT_ACCEPTANCE.getStatus());
        superviseRecord.setRemark(req.getRemark());
        BasicWebUtil.prepareInsertParams(superviseRecord);
        shopSuperviseRecordMapper.insert(superviseRecord);
        //商品订阅待验收通知 通知订阅人
        if (CollectionUtil.isNotEmpty(req.getSuperviseFiles())) {
            storageSuperviseRecordPath(superviseRecord, req.getSuperviseFiles());
        }
        notifySuppliers(marketShopSubscribe, NotificationConsts.ConsoleMsg.MarketMsg.TENANT_MARKET_SUBSCRIBE_CHECK);
    }

    @Override
    @Transactional
    public void acceptance(String subscribeId, String process, MarketAcceptanceReq req) {
        User authUser = AuthUtil.getAuthUser();
        MarketShopSubscribe marketShopSubscribe = subscribeMapper.selectById(subscribeId);
        ServiceOrder serviceOrder = serviceOrderMapper.selectByPrimaryKey(marketShopSubscribe.getOrderId());
        if (Objects.isNull(serviceOrder)) {
            throw new BizException(String.format(WebUtil.getMessage(MsgCd.ERROR_RES_NOT_FOUND), "订单"));
        }
        String superviseStatus = marketShopSubscribe.getSuperviseStatus();
        if (!MarketSuperviseStatusEnum.WAIT_ACCEPTANCE.getStatus().equals(superviseStatus)) {
            throw new BizException(WebUtil.getMessage(MsgCd.SHOP_STATUS_CANNOT_ACCEOPT));
        }
        String ownerId = serviceOrder.getOwnerId();
        if (!authUser.getUserSid().toString().equals(ownerId)) {
            throw new BizException(WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        MarketAcceptanceVo acceptanceVo = MarketAcceptanceVo.builder().serviceOrder(serviceOrder).shopSubscribe(marketShopSubscribe).process(process).userSid(authUser.getUserSid()).acceptanceReq(req).build();
        if (PASS.equals(process)) {
            passAcceptance(acceptanceVo);
        } else if (REJECT.equals(process)) {
            rejectAcceptance(acceptanceVo);
        } else {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
    }

    /**
     * 取消订单
     *
     * @param acceptanceVo 接受签证官
     */
    @Override
    public void cancelOrder(MarketAcceptanceVo acceptanceVo) {
        Long userSid = acceptanceVo.getUserSid();
        ServiceOrder serviceOrder = acceptanceVo.getServiceOrder();
        MarketShopSubscribe shopSubscribe = acceptanceVo.getShopSubscribe();
        MarketAcceptanceReq req = acceptanceVo.getAcceptanceReq();
        String orderSn = serviceOrder.getOrderSn();

        QueryWrapper<BizAccountDeal> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(BizAccountDeal::getType, "out")
                .eq(BizAccountDeal::getOrderNo, orderSn);
        List<BizAccountDeal> list = bizAccountDealService.list(queryWrapper);

        MarketOrderPostingRequest request = MarketOrderPostingRequest.builder()
                .orderSn(orderSn)
                .entityId(list.get(0).getEntityId())
                .adminSid(userSid)
                .tradeType(TradeType.REFUND)
                .build();
        RestResult restResult = orderServiceFeign.orderPosting(request);
        if (!restResult.getStatus()) {
            throw new BizException(restResult.getMessage().toString());
        }

        UpdateWrapper<BizAccountDeal> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .eq(BizAccountDeal::getType, "out")
                .eq(BizAccountDeal::getOrderNo, orderSn)
                .set(BizAccountDeal::getSuperviseStatus, MarketSuperviseStatusEnum.CANCELLED.getStatus());
        bizAccountDealService.update(updateWrapper);

        MarketShopSuperviseRecord superviseRecord = new MarketShopSuperviseRecord();
        superviseRecord.setSubscribeId(shopSubscribe.getSubscribeId());
        superviseRecord.setCreatedUserId(userSid);
        superviseRecord.setOriginalStatus(shopSubscribe.getSuperviseStatus());
        superviseRecord.setUpdateStatus(MarketSuperviseStatusEnum.CANCELLED.getStatus());
        superviseRecord.setRemark(req.getRemark());
        BasicWebUtil.prepareInsertParams(superviseRecord);
        shopSuperviseRecordMapper.insert(superviseRecord);

        if (CollectionUtil.isNotEmpty(req.getSuperviseFiles())) {
            storageSuperviseRecordPath(superviseRecord, req.getSuperviseFiles());
        }

        String updateStatus = MarketSuperviseStatusEnum.CANCELLED.getStatus();
        MarketShopSubscribe subscribe = new MarketShopSubscribe();
        subscribe.setSubscribeId(shopSubscribe.getSubscribeId());
        subscribe.setSuperviseStatus(updateStatus);
        shopSubscribe.setSuperviseStatus(updateStatus);
        subscribeMapper.updateById(subscribe);

        ServiceOrder updOrder = new ServiceOrder();
        updOrder.setId(serviceOrder.getId());
        updOrder.setStatus(REFUSED);
        serviceOrderMapper.updateById(updOrder);

        // TODO 发送模型集市商品订单已取消，金额已退还逻辑

        notifySuppliers(shopSubscribe, NotificationConsts.ConsoleMsg.MarketMsg.TENANT_MARKET_SUBSCRIBE_CANCEL);

    }



    /**
     * 360天未完成资金监管的订单，自动退订
     */
    @Override
    public void IncompleteOrders() {
        Date now = new Date();
        String cancelDate = PropertiesUtil.getProperty("supervise.cancel.date");
        int parseInt = Integer.parseInt(cancelDate);
        DateTime dateTime = DateUtil.offsetDay(now, -parseInt);

        QueryWrapper<MarketShopSubscribe> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .isNotNull(MarketShopSubscribe::getSuperviseStatus)
                .notIn(MarketShopSubscribe::getSuperviseStatus, Arrays.asList(MarketSuperviseStatusEnum.COMPLETED.getStatus(), MarketSuperviseStatusEnum.CANCELLED.getStatus()))
                .lt(MarketShopSubscribe::getCreatedDt, dateTime);
        List<MarketShopSubscribe> marketShopSubscribes = subscribeMapper.selectList(queryWrapper);
        log.info("扫描到360天未完成需要自动退订的订单数量【{}】", marketShopSubscribes.size());
        if (CollectionUtil.isEmpty(marketShopSubscribes)) {
            return;
        }
        marketShopSubscribes.forEach(subscribe -> {
            log.info("取消订阅【{}】开始...", subscribe.getSubscribeId());
            ServiceOrder order = serviceOrderMapper.selectByPrimaryKey(subscribe.getOrderId());
            cn.com.cloudstar.rightcloud.basic.data.pojo.user.User user = userMapper.selectByPrimaryKey(Long.parseLong(order.getOwnerId()));
            AuthUser authUser = BeanUtil.copyProperties(user, AuthUser.class);
            AuthUserHolder.setAuthUser(authUser);
            MarketAcceptanceReq req = new MarketAcceptanceReq();
            req.setRemark("未完成资金监管的订单，自动退订");
            MarketAcceptanceVo acceptanceVo = MarketAcceptanceVo.builder().serviceOrder(order).userSid(Long.parseLong(order.getOwnerId())).acceptanceReq(req).build();
            cancelOrder(acceptanceVo);
            log.info("取消订阅【{}】结束...", subscribe.getSubscribeId());
        });
    }

    @Override
    public void settlement(String subscribeId) {
        User authUser = AuthUtil.getAuthUser();
        MarketShopSubscribe marketShopSubscribe = subscribeMapper.selectById(subscribeId);
        ServiceOrder serviceOrder = serviceOrderMapper.selectByPrimaryKey(marketShopSubscribe.getOrderId());
        if (Objects.isNull(serviceOrder)) {
            throw new BizException(String.format(WebUtil.getMessage(MsgCd.ERROR_RES_NOT_FOUND), "订单"));
        }
        String superviseStatus = marketShopSubscribe.getSuperviseStatus();
        if (!MarketSuperviseStatusEnum.COMPLETED.getStatus().equals(superviseStatus)) {
            throw new BizException(WebUtil.getMessage(MsgCd.SUB_STATUS_CANNOT_ACCEOPT));
        }
        marketShopSubscribe.setSettlementStatus(SETTLED);
        subscribeMapper.updateById(marketShopSubscribe);
    }


    /**
     * 拒绝验收
     *
     * @param acceptanceVo 接受签证官
     */
    private void rejectAcceptance(MarketAcceptanceVo acceptanceVo) {
        Long userSid = acceptanceVo.getUserSid();
        ServiceOrder serviceOrder = acceptanceVo.getServiceOrder();
        MarketShopSubscribe shopSubscribe = acceptanceVo.getShopSubscribe();
        MarketAcceptanceReq req = acceptanceVo.getAcceptanceReq();
        QueryWrapper<MarketShopSuperviseRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(MarketShopSuperviseRecord::getSubscribeId, shopSubscribe.getSubscribeId())
                .eq(MarketShopSuperviseRecord::getUpdateStatus, MarketSuperviseStatusEnum.REJECTED.getStatus())
                .eq(MarketShopSuperviseRecord::getCreatedUserId, userSid);
        Integer integer = shopSuperviseRecordMapper.selectCount(queryWrapper);
        // 驳回3次关闭订单，退钱给购买用户
        String updateStatus = MarketSuperviseStatusEnum.REJECTED.getStatus();
        if (integer >= 2) {
            // 取消订单
            cancelOrder(acceptanceVo);
            return;
        }

        MarketShopSubscribe subscribe = new MarketShopSubscribe();
        subscribe.setSubscribeId(shopSubscribe.getSubscribeId());
        subscribe.setSuperviseStatus(updateStatus);
        shopSubscribe.setSuperviseStatus(updateStatus);
        subscribeMapper.updateById(subscribe);

        MarketShopSuperviseRecord superviseRecord = new MarketShopSuperviseRecord();
        superviseRecord.setSubscribeId(shopSubscribe.getSubscribeId());
        superviseRecord.setCreatedUserId(userSid);
        superviseRecord.setOriginalStatus(shopSubscribe.getSuperviseStatus());
        superviseRecord.setUpdateStatus(updateStatus);
        superviseRecord.setRemark(req.getRemark());
        BasicWebUtil.prepareInsertParams(superviseRecord);
        shopSuperviseRecordMapper.insert(superviseRecord);

        if (CollectionUtil.isNotEmpty(req.getSuperviseFiles())) {
            storageSuperviseRecordPath(superviseRecord, req.getSuperviseFiles());
        }
        // 商品订阅已被驳回通知 商品供应商
        notifySuppliers(shopSubscribe, NotificationConsts.ConsoleMsg.MarketMsg.TENANT_MARKET_SUBSCRIBE_FAIL);

    }

    /**
     * 通知供应商
     *
     * @param marketShopSubscribe 商店订阅
     * @param msg           消息模板
     */
    private void notifySuppliers(MarketShopSubscribe marketShopSubscribe, String msg) {
        MarketShopSubscribe shopSubscribe = subscribeMapper.selectById(marketShopSubscribe.getSubscribeId());
        MarketShop shop = marketShopService.getById(shopSubscribe.getShopId());
        if (Objects.isNull(shop)){
            throw new BizException(WebUtil.getMessage(String.format(MsgCd.ERROR_RES_NOT_FOUND,"商品")));
        }
        cn.com.cloudstar.rightcloud.basic.data.pojo.user.User supplierUser = userMapper.selectByPrimaryKey(shop.getOwnerId());
        cn.com.cloudstar.rightcloud.basic.data.pojo.user.User subscribeUser = userMapper.selectByPrimaryKey(shopSubscribe.getOwnerId());
        if (Objects.isNull(subscribeUser) || Objects.isNull(supplierUser)) {
            throw new BizException(WebUtil.getMessage(String.format(MsgCd.SHOP_SUPPLIER_DOES_NOT_EXIST)));
        }

        Map<String, String> messageContent;
        Long noticeUserSid = 0L;

        //如果是待验收就通知订阅人
        //不是就通知供应商
        if (MarketSuperviseStatusEnum.WAIT_ACCEPTANCE.getStatus().equals(shopSubscribe.getSuperviseStatus())) {
            noticeUserSid = subscribeUser.getUserSid();
            messageContent = getMessageContent(noticeUserSid, subscribeUser.getAccount(), subscribeUser.getAccount(), shopSubscribe, shop.getTitle());
        } else {
            noticeUserSid = supplierUser.getUserSid();
            messageContent = getMessageContent(noticeUserSid, supplierUser.getAccount(), subscribeUser.getAccount(), shopSubscribe, shop.getTitle());
        }

        BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
        baseNotificationMqBean.setMsgId(msg);
        baseNotificationMqBean.getToUserIds().add(noticeUserSid);
        baseNotificationMqBean.setMap(messageContent);
        baseNotificationMqBean.setEntityId(1L);
        rabbitTemplate.convertAndSend(NotificationConsts.SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT, baseNotificationMqBean);
    }


    private Map<String, String> getMessageContent(Long noticeUserSid, String supplierAccount, String account, MarketShopSubscribe subscribe, String title) {
        Map<String, String> messageContent = Maps.newHashMap();
        messageContent.put("subscribeAccount", account);
        messageContent.put("supplierAccount", supplierAccount);
        messageContent.put("subId", subscribe.getSubscribeId());
        messageContent.put("shopTitle", title);
        messageContent.put("time",cn.hutool.core.date.DateUtil.format(subscribe.getCreatedDt(), "yyyy-MM-dd HH:mm:ss"));
        messageContent.put("shopId", subscribe.getShopId());
        //区分管理端与客户端
        Set<String> roleId = userMapper.selectRoleByuserId(noticeUserSid);
        if (roleId.contains("301")) {
            messageContent.put("url", "#/appmodel/aimarket/subscribe/" + subscribe.getSubscribeId());
        } else {
            messageContent.put("url", "#/appmodel/mysubscribe/" + subscribe.getSubscribeId());
        }

        return messageContent;
    }

    /**
     * 通过验收
     *
     * @param acceptanceVo 接受签证官
     */
    private void passAcceptance(MarketAcceptanceVo acceptanceVo) {
        Long userSid = acceptanceVo.getUserSid();
        MarketShopSubscribe shopSubscribe = acceptanceVo.getShopSubscribe();
        ServiceOrder serviceOrder = acceptanceVo.getServiceOrder();
        String orderSn = serviceOrder.getOrderSn();

        // 通过验收，供应商收钱逻辑
        QueryWrapper<BizAccountDeal> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(BizAccountDeal::getType, "out")
                .eq(BizAccountDeal::getOrderNo, orderSn);
        List<BizAccountDeal> list = bizAccountDealService.list(queryWrapper);

        // 完成验收给商品提供者加钱
        String shopId = shopSubscribe.getShopId();
        MarketShop shop = shopService.getById(shopId);
        MarketOrderPostingRequest request = MarketOrderPostingRequest.builder()
                .orderSn(orderSn)
                .entityId(list.get(0).getEntityId())
                .adminSid(shop.getOwnerId())
                .tradeType(TradeType.INCOME)
                .build();
        RestResult restResult = orderServiceFeign.orderPosting(request);
        if (!restResult.getStatus()) {
            throw new BizException(restResult.getMessage().toString());
        }

        ServiceOrder updOrder = new ServiceOrder();
        updOrder.setId(serviceOrder.getId());
        updOrder.setStatus(COMPLETED);
        serviceOrderMapper.updateById(updOrder);

        MarketShopSubscribe subscribe = new MarketShopSubscribe();
        subscribe.setSubscribeId(shopSubscribe.getSubscribeId());
        subscribe.setSuperviseStatus(MarketSuperviseStatusEnum.COMPLETED.getStatus());
        shopSubscribe.setSuperviseStatus(MarketSuperviseStatusEnum.COMPLETED.getStatus());
        if (MarketShopSourceEnum.PLATFORM.getType().equals(shop.getShopSource())) {
            subscribe.setSettlementStatus(SETTLED);
        }
        subscribeMapper.updateById(subscribe);

        //修改为可开发票状态
        restResult = orderServiceFeign.allowInvoicing(shopSubscribe.getBillingCycleId());
        if (!restResult.getStatus()) {
            throw new BizException(restResult.getMessage().toString());
        }

        UpdateWrapper<BizAccountDeal> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .eq(BizAccountDeal::getType, "out")
                .eq(BizAccountDeal::getOrderNo, orderSn)
                .set(BizAccountDeal::getSuperviseStatus, MarketSuperviseStatusEnum.COMPLETED.getStatus());
        bizAccountDealService.update(updateWrapper);

        MarketShopSuperviseRecord superviseRecord = new MarketShopSuperviseRecord();
        superviseRecord.setSubscribeId(shopSubscribe.getSubscribeId());
        superviseRecord.setCreatedUserId(userSid);
        superviseRecord.setOriginalStatus(shopSubscribe.getSuperviseStatus());
        superviseRecord.setUpdateStatus(MarketSuperviseStatusEnum.COMPLETED.getStatus());
        superviseRecord.setRemark(MarketSuperviseStatusEnum.COMPLETED.getDeliverDesc());
        BasicWebUtil.prepareInsertParams(superviseRecord);
        shopSuperviseRecordMapper.insert(superviseRecord);
        // 商品订阅已完成通知 商品供应商
        notifySuppliers(shopSubscribe,NotificationConsts.ConsoleMsg.MarketMsg.TENANT_MARKET_SUBSCRIBE_SUCCESS);
    }

    /**
     * 存储订阅记录路径
     *
     * @param superviseRecord
     * @param contractFiles
     */
    private void storageSuperviseRecordPath(MarketShopSuperviseRecord superviseRecord, List<SysMFilePath> contractFiles) {
        AuthUser userAccount = RequestContextUtil.getAuthUserInfo();
        int index = 1;
        for (SysMFilePath file : contractFiles) {
            file.setFileNum(NoUtil.getNanoTimeId(SysMFileTypeEnum.SUPERVISE_RECORD.getCodeNumber()));
            if (userAccount == null) {
                throw new BizException(WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
            }
            file.setAccountId(userAccount.getUserSid());
            String filePath = CrytoUtilSimple.encrypt(file.getFilePath());
            file.setFilePath(StrUtil.isEmpty(filePath) ? null : filePath.trim());
            file.setSortOrder(index);
            file.setOperationType(SysMFileTypeEnum.SUPERVISE_RECORD.getCode());
            file.setOperationId(superviseRecord.getId().toString());
            BasicWebUtil.prepareInsertParams(file);
            sysMFilePathMapper.insert(file);
            index++;
        }
    }

}
