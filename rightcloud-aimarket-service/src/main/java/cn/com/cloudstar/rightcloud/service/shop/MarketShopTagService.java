package cn.com.cloudstar.rightcloud.service.shop;

import cn.com.cloudstar.rightcloud.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.data.entity.MarketShopTag;
import cn.com.cloudstar.rightcloud.data.request.market.MarketShopTagPortalRequest;
import cn.com.cloudstar.rightcloud.data.request.market.MarketShopTagReq;
import cn.com.cloudstar.rightcloud.data.request.market.MarketShopTagTreeReq;
import cn.com.cloudstar.rightcloud.data.response.market.MarketShopTagPortalResp;
import cn.com.cloudstar.rightcloud.data.response.market.MarketShopTagResp;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 *
 */
public interface MarketShopTagService extends IService<MarketShopTag> {
    /**
     * 根据商品Id查询商品标签
     * @param shopId 商品id
     * @return 标签数据
     */
    List<MarketShopTag> selectByShopId(String shopId);

    /**
     * 查看分类列表
     * @param req 参数
     * @return 返回值
     */
    IPage<MarketShopTagResp> getList(MarketShopTagReq req);

    /**
     * 查询标签
     * @param request 参数
     * @return 返回值
     */
    RestResult<List<MarketShopTagPortalResp>> queryShopLabels(MarketShopTagPortalRequest request);

    /**
     * 查询标签
     * @return 返回值
     */
    RestResult<List<MarketShopTag>> getLabels(String shopType);

    /**
     * 查询商品分类的一级分类
     * @return 分类信息
     */
    RestResult getParentTag(Integer shopType);

    /**
     * 查询标签树
     * @return 分类信息
     */
    RestResult getLabelTagTree(MarketShopTagTreeReq req);

    /**
     * 查询资源种类数量（大屏）
     * @return 资源种类数量
     */
    int countLabelNum();
}
