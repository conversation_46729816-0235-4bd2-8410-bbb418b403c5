package cn.com.cloudstar.rightcloud.service.discount;

import cn.com.cloudstar.rightcloud.data.entity.BizDiscount;
import cn.com.cloudstar.rightcloud.data.request.discount.DescribeDiscountRequest;
import cn.com.cloudstar.rightcloud.data.response.discount.DescribeDiscountDetailResponse;
import cn.com.cloudstar.rightcloud.data.response.discount.DescribeDiscountResponse;
import cn.com.cloudstar.rightcloud.data.vo.discount.DiscountCheckVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【biz_discount(折扣表)】的数据库操作Service
* @createDate 2023-08-11 15:13:52
*/
public interface BizDiscountService extends IService<BizDiscount> {

    void check(DiscountCheckVo discountCheckVo);

    IPage<DescribeDiscountResponse> findPlatformDiscounts(DescribeDiscountRequest request);

    DescribeDiscountDetailResponse findPDiscountDetail(Long id);
}
