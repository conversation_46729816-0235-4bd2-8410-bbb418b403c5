package cn.com.cloudstar.rightcloud.service.shop;

import cn.com.cloudstar.rightcloud.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.data.entity.MarketShopSubscribe;
import cn.com.cloudstar.rightcloud.data.request.market.ApplyMarketRequest;
import cn.com.cloudstar.rightcloud.data.response.bigscreen.MarketBigScreenResp;
import cn.com.cloudstar.rightcloud.data.response.bigscreen.MarketSubscribeProviderTopResp;
import cn.com.cloudstar.rightcloud.data.response.bigscreen.MarketSubscribeRecentResp;
import cn.com.cloudstar.rightcloud.data.response.bigscreen.MarketSubscribeShopTopResp;
import cn.com.cloudstar.rightcloud.data.response.bigscreen.MarketSubscribeUserTopResp;
import cn.com.cloudstar.rightcloud.data.response.bigscreen.MarketSuperviseTrendResp;
import cn.com.cloudstar.rightcloud.data.response.bigscreen.ProductDeliveryStatisticsResp;
import cn.com.cloudstar.rightcloud.data.response.bigscreen.ProductTagStatisticsResp;
import cn.com.cloudstar.rightcloud.data.response.bigscreen.UserOrderRankingResp;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 *
 */
public interface MarketSubscribeService extends IService<MarketShopSubscribe> {

    /**
     * 申请订阅
     * @param request
     */
    RestResult apply(ApplyMarketRequest request);

    /**
     * 查询成交订单个数和金额（大屏）
     * @return 成交订单个数和金额
     */
    RestResult<MarketBigScreenResp> countSubscribeNum();

    /**
     * 查询最新成交动态（大屏）
     * @return 最新成交动态(10条）
     */
    RestResult<List<MarketSubscribeRecentResp>> selectSubscribeRecent(Long top);

    /**
     * 查询商品成交排行
     *
     * @param timeType    时间类型（按周按月按年）
     * @param top         前top条排行(默认为10条）
     * @param contentType
     * @return {@link RestResult}
     */
    RestResult<List<MarketSubscribeShopTopResp>> selectSubscribeShopTop(String timeType, Long top, String contentType);

    /**
     * 查询供应商成交订单排行
     * @param timeType 时间类型（按周按月按年）
     * @param top 前top条排行(默认为10条）
     * @return {@link RestResult}
     */
    RestResult<List<MarketSubscribeProviderTopResp>> selectSubscribeProviderTop(String timeType, Long top);

    /**
     * 查询用户成交订单金额排行
     * @param timeType 时间类型（按周按月按年）
     * @param top 前top条排行(默认为10条）
     * @return {@link RestResult}
     */
    RestResult<List<MarketSubscribeUserTopResp>> selectSubscribeUserTop(String timeType, Long top);

    /**
     * 查询资金监管服务趋势
     *
     * @param timeType    时间类型（按周按月按年）
     * @param contentType
     * @return {@link RestResult}
     */
    RestResult<List<MarketSuperviseTrendResp>> selectSuperviseTrend(String timeType, String contentType);


    /**
     * 获取用户订单排名
     *
     * @param timeType 时间类型
     * @param top      顶部
     * @return {@link List }<{@link UserOrderRankingResp }>
     */
    List<UserOrderRankingResp> getUserOrderRanking(String timeType, Long top);

    /**
     * 获取产品交付统计数据
     *
     * @param timeType 时间类型
     * @return {@link ProductDeliveryStatisticsResp }
     */
    List<ProductDeliveryStatisticsResp> getProductDeliveryStatistics(String timeType);

    /**
     * 获取产品标签统计信息
     *
     * @param timeType 时间类型
     * @return {@link ProductTagStatisticsResp }
     */
    List<ProductTagStatisticsResp> getProductTagStatistics(String timeType);
}
