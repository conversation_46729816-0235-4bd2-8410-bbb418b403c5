package cn.com.cloudstar.rightcloud.service.shop.impl;

import cn.com.cloudstar.rightcloud.data.dao.MarketShopTagRelevanceMapper;
import cn.com.cloudstar.rightcloud.data.entity.MarketShopTagRelevance;
import cn.com.cloudstar.rightcloud.service.shop.MarketShopTagRelevanceService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 *
 */
@Service
public class MarketShopTagRelevanceServiceImpl extends ServiceImpl<MarketShopTagRelevanceMapper, MarketShopTagRelevance>
    implements MarketShopTagRelevanceService {

    @Override
    public void deleteByShopId(String shopId) {
        QueryWrapper<MarketShopTagRelevance> deleteWrapper = new QueryWrapper<>();
        deleteWrapper.eq("shop_id",shopId);
        this.remove(deleteWrapper);
    }

    @Override
    public void saveBatchTag(String shopId, List<Long> labelIds) {
        List<MarketShopTagRelevance> shopTagList = new ArrayList<>(labelIds.size());
        for (Long labelId : labelIds) {
            MarketShopTagRelevance tagRelevance = new MarketShopTagRelevance();
            tagRelevance.setShopId(shopId);
            tagRelevance.setTagId(labelId);
            shopTagList.add(tagRelevance);
        }
        this.saveBatch(shopTagList);
    }
}




