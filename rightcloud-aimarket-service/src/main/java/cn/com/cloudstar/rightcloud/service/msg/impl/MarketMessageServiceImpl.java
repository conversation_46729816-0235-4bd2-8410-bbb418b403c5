package cn.com.cloudstar.rightcloud.service.msg.impl;

import cn.com.cloudstar.rightcloud.basic.data.dao.user.BasicUserMapper;
import cn.com.cloudstar.rightcloud.basic.data.pojo.user.User;
import cn.com.cloudstar.rightcloud.common.constants.NotificationConsts;
import cn.com.cloudstar.rightcloud.common.mq.request.ImsgNotificationMq;
import cn.com.cloudstar.rightcloud.common.mq.request.MailNotificationMq;
import cn.com.cloudstar.rightcloud.common.mq.request.SmsNotificationMq;
import cn.com.cloudstar.rightcloud.common.util.NotificationUtil;
import cn.com.cloudstar.rightcloud.data.dao.SysMMsgReceiveContactMapper;
import cn.com.cloudstar.rightcloud.data.dao.SysMMsgUserConfigMapper;
import cn.com.cloudstar.rightcloud.data.vo.msg.SysMMsgReceiveContact;
import cn.com.cloudstar.rightcloud.data.vo.msg.SysMMsgReceiveContactParam;
import cn.com.cloudstar.rightcloud.data.vo.msg.SysMMsgUserConfig;
import cn.com.cloudstar.rightcloud.service.msg.IMarketMessageService;
import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static cn.com.cloudstar.rightcloud.common.constants.NotificationConsts.SEND_NOTIFICATION_EXCHANGE;

@Service
@Slf4j
public class MarketMessageServiceImpl implements IMarketMessageService {

    /**
     * 启用
     */
    private static Integer ENABLE = 1;
    @Autowired
    private BasicUserMapper userMapper;

    @Autowired
    RabbitTemplate rabbitTemplate;

    /**
     * 产品消息类型
     */
    private static final String PRODUCT_TYPE = "product";
    /**
     * 财务消息类型
     */
    private static final String FINANCE_TYPE = "finance";
    /**
     * 账户的消息
     */
    private static final String ACCOUNT_TYPE = "account";

    @Autowired
    private SysMMsgReceiveContactMapper sysMMsgReceiveContactMapper;

    @Autowired
    private SysMMsgUserConfigMapper sysMMsgUserConfigMapper;


    @Override
    public void sendMessage(Long userSid, Map<String, String> messageContent, String msg1, String msg2) {
        // 根据userId,查询user
        User user = userMapper.selectByPrimaryKey(userSid);
        if (Objects.isNull(user)) {
            log.info("消息发送失败,用户为空!");
        } else {
            messageContent.put("userAccount", user.getAccount());
        }
        NotificationUtil.assembleBaseMessageContent(messageContent);
        // 给租户管理员消息设置配置的接受人发送短信
        if (msg1 != null) {
            this.sendMessageByMsgConfig(user, messageContent, msg1, 1L, false);
        }
        // 运营管理员发送消息
        if (msg2 != null) {
            this.sendMessageByMsgConfig(user, messageContent, msg2, 1L, true);
        }
    }

    private void sendMessageByMsgConfig(User user, Map<String, String> messageContent, String msg, Long entityId, Boolean isAdmin) {
        try {
            // 根据配置消息发送消息
            SysMMsgUserConfig sysMMsgUserConfig = null;
            // 短信发送电话号码
            Set<String> mobiles = new LinkedHashSet<>();
            // 邮件发送邮件
            Set<String> mails = new LinkedHashSet<>();
            // 站内信发送人
            LinkedHashSet<Long> userIds = new LinkedHashSet<>();
            List<SysMMsgUserConfig> configs = null;
            // 非管理员消息的时候，默认给租户管理员发送消息
            if (!isAdmin) {
                mobiles.add(user.getMobile());
                mails.add(user.getEmail());
                userIds.add(user.getUserSid());
            }
            if (isAdmin) {
                configs = sysMMsgUserConfigMapper.queryMsgConfigByOrgSid(-1L, msg);
            } else {
                configs = sysMMsgUserConfigMapper.queryMsgConfigByOrgSid(user.getOrgSid(), msg);
            }

            if (CollectionUtil.isNotEmpty(configs)) {
                sysMMsgUserConfig = configs.get(0);
                // configId查询配置得人
                SysMMsgReceiveContactParam sysMMsgReceiveContactParam = new SysMMsgReceiveContactParam();
                sysMMsgReceiveContactParam.setConfigId(sysMMsgUserConfig.getId());
                // 获取发送人，如果配置了就取配置的，如果没有配置就
                List<SysMMsgReceiveContact> receiveList = sysMMsgReceiveContactMapper.queryAll(sysMMsgReceiveContactParam);

                if (CollectionUtil.isNotEmpty(receiveList)) {
                    Set<String> configMobiles = receiveList.stream().filter(r -> ENABLE.equals(r.getPhoneValidate())).map(SysMMsgReceiveContact::getPhone).collect(Collectors.toSet());
                    Set<String> configMails = receiveList.stream().filter(r -> ENABLE.equals(r.getEmailValidate())).map(SysMMsgReceiveContact::getEmail).collect(Collectors.toSet());
                    Set<Long> configUseSids = receiveList.stream().map(SysMMsgReceiveContact::getUserSid).collect(Collectors.toSet());
                    mobiles.addAll(configMobiles);
                    mails.addAll(configMails);
                    userIds.addAll(configUseSids);
                }

                // 如果短信开启，发送短信发送
                if (ENABLE.equals(sysMMsgUserConfig.getSmsStatus())) {
                    SmsNotificationMq smsNotificationMq = new SmsNotificationMq();
                    smsNotificationMq.setMsgId(msg);
                    smsNotificationMq.setMap(messageContent);
                    smsNotificationMq.setMobiles(mobiles);
                    smsNotificationMq.setEntityId(entityId);
                    if (FINANCE_TYPE.equalsIgnoreCase(sysMMsgUserConfig.getMsgType())) {
                        rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.TENANT,
                                smsNotificationMq);
                    } else if (ACCOUNT_TYPE.equalsIgnoreCase(sysMMsgUserConfig.getMsgType())) {
                        rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.ACCOUNT,
                                smsNotificationMq);
                    } else {
                        rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT,
                                smsNotificationMq);
                    }
                }
                // 如果邮件启用发送邮件
                if (ENABLE.equals(sysMMsgUserConfig.getEmailStatus())) {
                    MailNotificationMq mailNotificationMq = new MailNotificationMq();
                    mailNotificationMq.setMsgId(msg);
                    mailNotificationMq.setMap(messageContent);
                    mailNotificationMq.setMails(mails);
                    mailNotificationMq.setEntityId(entityId);
                    if (FINANCE_TYPE.equalsIgnoreCase(sysMMsgUserConfig.getMsgType())) {
                        rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.TENANT,
                                mailNotificationMq);
                    } else if (ACCOUNT_TYPE.equalsIgnoreCase(sysMMsgUserConfig.getMsgType())) {
                        rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.ACCOUNT,
                                mailNotificationMq);
                    } else {
                        rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT,
                                mailNotificationMq);
                    }
                }
                // 默认发送站内信
                ImsgNotificationMq imsgNotificationMq = new ImsgNotificationMq();
                imsgNotificationMq.setMsgId(msg);
                imsgNotificationMq.setMap(messageContent);
                imsgNotificationMq.setImsgUserIds(userIds);
                imsgNotificationMq.setEntityId(entityId);
                if (FINANCE_TYPE.equalsIgnoreCase(sysMMsgUserConfig.getMsgType())) {
                    rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.TENANT,
                            imsgNotificationMq);
                } else if (ACCOUNT_TYPE.equalsIgnoreCase(sysMMsgUserConfig.getMsgType())) {
                    rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.ACCOUNT,
                            imsgNotificationMq);
                } else {
                    rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT,
                            imsgNotificationMq);
                }
            }
        } catch (Exception e) {
            log.error("发送信息异常：", e);
        }
    }


}
