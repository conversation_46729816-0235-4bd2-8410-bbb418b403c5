package cn.com.cloudstar.rightcloud.service.shop;

import cn.com.cloudstar.rightcloud.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.data.entity.MarketShopSuperviseRecord;
import cn.com.cloudstar.rightcloud.data.request.market.MarketAcceptanceReq;
import cn.com.cloudstar.rightcloud.data.request.market.MarketDeliverReq;
import cn.com.cloudstar.rightcloud.data.vo.market.MarketAcceptanceVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【market_shop_supervise_record】的数据库操作Service
* @createDate 2023-08-03 17:04:17
*/
public interface MarketShopSuperviseRecordService extends IService<MarketShopSuperviseRecord> {

    void deliver(String subscribeId, MarketDeliverReq req);

    void acceptance(String subscribeId, String process, MarketAcceptanceReq req);

    void cancelOrder(MarketAcceptanceVo acceptanceVo);

    void IncompleteOrders();

    void settlement(String subscribeId);
}
