package cn.com.cloudstar.rightcloud.service.shop;

import cn.com.cloudstar.rightcloud.data.entity.MarketShopTagRelevance;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 *
 */
public interface MarketShopTagRelevanceService extends IService<MarketShopTagRelevance> {

    /**
     * 根据商品id和标签id删除关联
     * @param shopId 商品id
     */
    void deleteByShopId(String shopId);


    /**
     * 批量新增标签
     * @param shopId 商品id
     * @param labelIds 标签id集合
     */
    void saveBatchTag(String shopId,List<Long> labelIds);

}
