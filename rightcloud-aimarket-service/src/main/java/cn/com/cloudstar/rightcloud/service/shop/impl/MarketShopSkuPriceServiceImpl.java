package cn.com.cloudstar.rightcloud.service.shop.impl;

import cn.com.cloudstar.rightcloud.common.constants.DiscountConstants;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.dto.SkuEnumDto;
import cn.com.cloudstar.rightcloud.common.dto.User;
import cn.com.cloudstar.rightcloud.common.enums.MarketShopSourceEnum;
import cn.com.cloudstar.rightcloud.common.enums.MarketShopStatusEnum;
import cn.com.cloudstar.rightcloud.common.enums.ScopeTypeEnum;
import cn.com.cloudstar.rightcloud.common.exception.BizException;
import cn.com.cloudstar.rightcloud.common.util.AuthUtil;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.data.dao.BizBillingAccountMapper;
import cn.com.cloudstar.rightcloud.data.dao.MarketShopSkuPriceMapper;
import cn.com.cloudstar.rightcloud.data.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.data.entity.BizDiscount;
import cn.com.cloudstar.rightcloud.data.entity.BizDiscountPolicy;
import cn.com.cloudstar.rightcloud.data.entity.MarketShop;
import cn.com.cloudstar.rightcloud.data.entity.MarketShopPriceJoin;
import cn.com.cloudstar.rightcloud.data.entity.MarketShopSkuPrice;
import cn.com.cloudstar.rightcloud.data.request.market.MarketInquiryPriceBase;
import cn.com.cloudstar.rightcloud.data.response.market.MarketInquiryPriceResp;
import cn.com.cloudstar.rightcloud.data.vo.market.MarketInquiryPriceVo;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicWebUtil;
import cn.com.cloudstar.rightcloud.service.discount.BizDiscountPolicyService;
import cn.com.cloudstar.rightcloud.service.discount.BizDiscountService;
import cn.com.cloudstar.rightcloud.service.shop.MarketShopPriceJoinService;
import cn.com.cloudstar.rightcloud.service.shop.MarketShopService;
import cn.com.cloudstar.rightcloud.service.shop.MarketShopSkuPriceService;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【market_shop_sku_price(商品属性规格表)】的数据库操作Service实现
 * @createDate 2023-08-01 16:23:05
 */
@Service
@Slf4j
public class MarketShopSkuPriceServiceImpl extends ServiceImpl<MarketShopSkuPriceMapper, MarketShopSkuPrice>
        implements MarketShopSkuPriceService {

    @Autowired
    private MarketShopService marketShopService;

    @Autowired
    private MarketShopSkuPriceMapper shopSkuPriceMapper;

    @Autowired
    private BizDiscountService discountService;

    @Autowired
    private BizDiscountPolicyService bizDiscountPolicyService;

    @Autowired
    private BizBillingAccountMapper bizBillingAccountMapper;

    @Resource
    private MarketShopPriceJoinService priceJoinService;

    /**
     * AI模型市场 xw
     */
    private final static Integer shopTypeFlag = 0;

    @Override
    public MarketInquiryPriceResp price(MarketInquiryPriceBase req) {
        MarketInquiryPriceVo inquiryPriceVo = BeanUtil.copyProperties(req, MarketInquiryPriceVo.class);
        inquiryPriceVo.setUnitValue(req.getAmount());
        checkShopById(inquiryPriceVo.getShopId());

        generateUnitPrice(inquiryPriceVo);
        return generateAmount(inquiryPriceVo);

    }

    /**
     * 生成原始金额
     *
     * @param inquiryPriceVo 签证官询问价格
     * @return
     */
    private MarketInquiryPriceResp generateAmount(MarketInquiryPriceVo inquiryPriceVo) {
        BigDecimal discount;
        BigDecimal amount;
        BigDecimal originalAmount;
        BigDecimal discountAmount;
        MarketInquiryPriceResp priceResp = new MarketInquiryPriceResp();
        if (Objects.nonNull(inquiryPriceVo.getPriceJoinId())) {
            //aihub商品数量定死了是1
            originalAmount = inquiryPriceVo.getUnitPrice().setScale(2, RoundingMode.DOWN);
            log.info("生成商品原始金额【{}】", originalAmount);
            priceResp.setAmount(originalAmount);
        } else {
            BigDecimal unitPrice = inquiryPriceVo.getUnitPrice();
            Integer periodNum = inquiryPriceVo.getPeriodNum();
            Integer unitValue = inquiryPriceVo.getUnitValue();
            BigDecimal multiply = unitPrice.multiply(new BigDecimal(periodNum)).multiply(new BigDecimal(unitValue));
            log.info("生成商品原始金额【{}】", multiply);
            originalAmount = multiply.setScale(2, RoundingMode.DOWN);
            priceResp.setAmount(originalAmount);
        }

        // 查询商品折扣
        inquiryPriceVo.setOriginalAmount(originalAmount);
        discount = queryDiscounts(inquiryPriceVo);
        amount = originalAmount.multiply(discount).setScale(2, RoundingMode.DOWN);
        discountAmount = originalAmount.subtract(amount).setScale(2, RoundingMode.DOWN);

        priceResp.setPlatformDiscount(discount);
        priceResp.setAmount(amount);
        priceResp.setOriginalAmount(originalAmount);
        priceResp.setDiscountAmount(discountAmount);
        priceResp.setSkuName(inquiryPriceVo.getSkuName());
        return priceResp;

    }

    /**
     * 查询折扣
     * 租户发布的商品用自己创建的折扣
     * 管理员发布的商品用之前的逻辑
     *
     * @param inquiryPriceVo 签证官询问价格
     * @return
     */
    private BigDecimal queryDiscounts(MarketInquiryPriceVo inquiryPriceVo) {
        MarketShop shop = marketShopService.getById(inquiryPriceVo.getShopId());
        Date date = new Date();
        BigDecimal discount = BigDecimal.ONE;
        if (MarketShopSourceEnum.SUPPLIER.getType().equals(shop.getShopSource())) {
            // 供应商折扣
            QueryWrapper<BizDiscount> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda()
                    .eq(BizDiscount::getDiscountType, DiscountConstants.MARKET)
                    .eq(BizDiscount::getCreatedBy, shop.getCreatedBy())
                    .eq(BizDiscount::getStatus, DiscountConstants.ENABLE)
                    .like(BizDiscount::getProductScope, DiscountConstants.PRODUCT_SCOPE)
                    .lt(BizDiscount::getStartTime, date)
                    .gt(BizDiscount::getEndTime, date);
            List<BizDiscount> list = discountService.list(queryWrapper);
            for (BizDiscount bizDiscount : list) {
                Integer unitValue = inquiryPriceVo.getUnitValue();
                Integer periodNum = inquiryPriceVo.getPeriodNum();
                if (checkDiscountScope(BigDecimal.valueOf(unitValue), periodNum, bizDiscount, inquiryPriceVo.getOriginalAmount())) {
                    discount = NumberUtil.mul(discount, bizDiscount.getDiscountRatio());
                }
            }
        } else if (MarketShopSourceEnum.PLATFORM.getType().equals(shop.getShopSource())) {
            // 平台折扣
            User authUser = AuthUtil.getAuthUser();
            if (Objects.isNull(authUser)) {
                // 未登录直接使用平台折扣
                QueryWrapper<BizDiscount> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda()
                        .eq(BizDiscount::getDiscountType, DiscountConstants.PLATFORM)
                        .eq(BizDiscount::getStatus, DiscountConstants.ENABLE)
                        .like(BizDiscount::getProductScope, DiscountConstants.PRODUCT_SCOPE)
                        .lt(BizDiscount::getStartTime, date)
                        .gt(BizDiscount::getEndTime, date);
                List<BizDiscount> list = discountService.list(queryWrapper);
                    for (BizDiscount bizDiscount : list) {
                        Integer unitValue = inquiryPriceVo.getUnitValue();
                        Integer periodNum = inquiryPriceVo.getPeriodNum();
                        if (checkDiscountScope(BigDecimal.valueOf(unitValue), periodNum, bizDiscount, inquiryPriceVo.getOriginalAmount())) {
                            discount = NumberUtil.mul(discount, bizDiscount.getDiscountRatio());
                        }
                    }
            } else {
                /*
                 * 登录用户，需判断折扣策略。
                 * 1.判断客户策略是否启动,默认使用客户策略（客户策略和默认策略是同一条数据，客户策略和默认策略是互斥的）
                 * 2.先查询该客户询价的产品是否有策略启动的,如果没有使用默认策略，如果有使用该策略
                 * 如果折扣策略有数据，而具体的折扣没有数据则不管；如果折扣策略么有数据，而具体的折扣有数据则具体折扣会有效
                 */
                Long entityId = 1L;
                QueryWrapper<BizDiscountPolicy> policyQueryWrapper = new QueryWrapper<>();
                policyQueryWrapper.eq("category", DiscountConstants.PLATFORM);
                policyQueryWrapper.eq("entity_id", entityId);
                BizDiscountPolicy defaultPolicy = bizDiscountPolicyService.getOne(policyQueryWrapper);
                if (Objects.isNull(defaultPolicy)) {
                    // 增加默认折扣策略
                    defaultPolicy = new BizDiscountPolicy();
                    defaultPolicy.setCategory(DiscountConstants.PLATFORM);
                    defaultPolicy.setPolicyType(DiscountConstants.SHARE);
                    defaultPolicy.setPolicyLevel("customer,platform");
                    defaultPolicy.setStatus(1);
                    defaultPolicy.setVersion(1);
                    defaultPolicy.setEntityId(1L);
                    BasicWebUtil.prepareInsertParams(defaultPolicy);
                    bizDiscountPolicyService.save(defaultPolicy);
                }

                QueryWrapper<BizBillingAccount> billQuery = new QueryWrapper<>();
                billQuery.lambda()
                        .eq(BizBillingAccount::getAdminSid, authUser.getUserSid())
                        .eq(BizBillingAccount::getEntityId, 1L);
                BizBillingAccount billingAccount = bizBillingAccountMapper.selectOne(billQuery);
                // 最终使用的折扣策略
                BizDiscountPolicy policy = defaultPolicy;
                if (defaultPolicy.getStatus().equals(1)) {
                    // 2.先查询该客户询价的产品是否有策略启动的,如果没有直接返回，如果有使用该策略
                    policyQueryWrapper.clear();
                    policyQueryWrapper.eq("entity_id", entityId);
                    policyQueryWrapper.eq("status", 1);
                    policyQueryWrapper.eq("user_account_id", billingAccount.getId());
                    policyQueryWrapper.like("product_scope", DiscountConstants.PRODUCT_SCOPE);
                    BizDiscountPolicy bizDiscountPolicy = bizDiscountPolicyService.getOne(policyQueryWrapper);
                    policy = bizDiscountPolicy;
                    // 没有客户策略还是会走平台策略  这里还需要判断like查询，如果product为DRP，会将HPC-DRP的折扣查询出来。
                    if (Objects.isNull(bizDiscountPolicy)) {
                        policy = defaultPolicy;
                    }
                    if (DiscountConstants.NO.equals(policy.getPolicyType())) {
                        return discount;
                    }
                }

                // 查询当前适用环境和适用产品且在有效范围内的折扣
                QueryWrapper<BizDiscount> queryWrapper = new QueryWrapper<>();

                queryWrapper.lambda().eq(BizDiscount::getStatus, 1).and(wrapper ->
                        wrapper.eq(BizDiscount::getEntityId, billingAccount.getEntityId())
                                .ge(BizDiscount::getEndTime, date)
                                .le(BizDiscount::getStartTime, date));

                queryWrapper.lambda().and(wrapper ->
                        wrapper.like(BizDiscount::getProductScope, DiscountConstants.PRODUCT_SCOPE));
                List<BizDiscount> discounts = discountService.list(queryWrapper);

                Integer unitValue = inquiryPriceVo.getUnitValue();
                Integer periodNum = inquiryPriceVo.getPeriodNum();
                BigDecimal originalAmount = inquiryPriceVo.getOriginalAmount();
                if (DiscountConstants.SHARE.equals(policy.getPolicyType())) {
                    discount = computeDiscountOrderByLevel(BigDecimal.valueOf(unitValue), periodNum, billingAccount.getId(), policy,
                            discount, originalAmount, discounts);

                } else if (DiscountConstants.CUSTOMER.equals(policy.getPolicyType())) {
                    // 客户折扣独享
                    discount = computeCustomerDiscount(discount, discounts, billingAccount.getId(), BigDecimal.valueOf(unitValue), periodNum, originalAmount);
                } else if (DiscountConstants.PLATFORM.equals(policy.getPolicyType())) {
                    // 平台折扣独享
                    discount = computePlatformDiscount(discount, discounts, billingAccount.getId(), BigDecimal.valueOf(unitValue), periodNum, originalAmount);
                }
            }
        }

        if (discount.compareTo(BigDecimal.ZERO) > 0 && discount.compareTo(BigDecimal.ONE) <= 0) {
            return discount;
        } else {
            return BigDecimal.ONE;
        }

    }

    /**
     * 计算客户折扣
     *
     * @param discount         折扣
     * @param discounts        折扣
     * @param billingAccountId 计费帐户id
     * @param unitValue        单位价值:包年包月
     * @param periodNum        单位数据:个数
     * @return {@link BigDecimal}
     */
    private BigDecimal computeCustomerDiscount(BigDecimal discount, List<BizDiscount> discounts, Long billingAccountId, BigDecimal unitValue, Integer periodNum, BigDecimal originalAmount) {
        if (CollectionUtil.isEmpty(discounts)) {
            return discount;
        }
        Long accountId = billingAccountId;
        if (Objects.nonNull(accountId)) {
            List<BizDiscount> userDiscounts = discounts.stream()
                    .filter(item -> Objects.equals(item.getUserSid(), accountId)
                            && DiscountConstants.CUSTOMER.equals(item.getDiscountType()))
                    .collect(Collectors.toList());

            if (CollectionUtil.isEmpty(userDiscounts)) {
                return discount;
            }

            for (BizDiscount bizDiscount : userDiscounts) {
                if (checkDiscountScope(unitValue, periodNum, bizDiscount, originalAmount)) {
                    discount = NumberUtil.mul(discount, bizDiscount.getDiscountRatio());
                }
            }
        }
        return discount;
    }

    /**
     * 共享折扣类型 计算折扣价
     *
     * @param accountId      账户id
     * @param policy         折扣策略
     * @param ratio          折扣率
     * @param discounts      具体折扣信息
     * @param period         期
     * @param amount         量
     * @param originalAmount 原始金额
     */
    private BigDecimal computeDiscountOrderByLevel(BigDecimal period, Integer amount, Long accountId,
                                                   BizDiscountPolicy policy, BigDecimal ratio,
                                                   BigDecimal originalAmount, List<BizDiscount> discounts) {
        // 如果没有策略优先级，使用默认策略优先级
        String policyLevel = Optional.ofNullable(policy.getPolicyLevel()).orElse("customer,platform");
        String[] levels = policyLevel.split(StrUtil.COMMA);
        List<Function<BigDecimal, BigDecimal>> toComputeDiscounts = Lists.newArrayList();
        for (String level : levels) {
            if (DiscountConstants.CUSTOMER.equals(level)) {
                Function<BigDecimal, BigDecimal> customer = (r) -> computeCustomerDiscount(r,
                        discounts, accountId, period, amount, originalAmount);
                toComputeDiscounts.add(customer);
            }
            if (DiscountConstants.PLATFORM.equals(level)) {
                Function<BigDecimal, BigDecimal> platform = (r) -> computePlatformDiscount(r,
                        discounts, accountId, period, amount, originalAmount);
                toComputeDiscounts.add(platform);
            }
        }
        Iterator<Function<BigDecimal, BigDecimal>> iterator = toComputeDiscounts.iterator();
        while (iterator.hasNext()) {
            ratio = iterator.next().apply(ratio);
            iterator.remove();
        }
        return ratio;
    }

    /**
     * 检查购买产品参数是否在折扣应用范围内
     *
     * @param period      期
     * @param amount      量
     * @param discount    折扣
     * @param originPrice 起源价格
     * @return boolean
     */
    private boolean checkDiscountScope(BigDecimal period, Integer amount, BizDiscount discount, BigDecimal originPrice) {
        // 范围格式 50-100
        String[] scopeArr = StrUtil.splitToArray(discount.getScopeValue(), StrUtil.DASHED);
        if (scopeArr.length == 0) {
            if (log.isDebugEnabled()) {
                log.debug("应用范围值为空！折扣id:[{}]", discount.getDiscountSid());
            }
            return false;
        }
        if ("null".equalsIgnoreCase(scopeArr[1])) {
            scopeArr[1] = "";
        }
        ScopeTypeEnum scopeTypeEnum = ScopeTypeEnum.getEnum(discount.getScopeType());
        if (scopeTypeEnum == null) {
            if (log.isDebugEnabled()) {
                log.debug("应用范围类型未匹配！折扣id:{}", discount.getDiscountSid());
            }
            return true;
        }
        if (amount == null) {
            amount = 1;
        }
        switch (scopeTypeEnum) {
            case MONEY:
                return checkMoneyScope(originPrice, scopeArr);
            case TIME:
                return checkTimeScope(period, scopeArr);
            case QUANTITY:
                return checkQuantityScope(amount, scopeArr);
            case UNLIMITED:
                return true;
            default:
                return false;
        }
    }


    /**
     * 检查金额范围
     *
     * @param originPrice 起源价格
     * @param scope       范围
     * @return boolean
     */
    private boolean checkMoneyScope(BigDecimal originPrice, String... scope) {
        // 金额范围
        if (StrUtil.isNotBlank(scope[1])) {
            return originPrice.compareTo(new BigDecimal(scope[0])) >= 0
                    && originPrice.compareTo(new BigDecimal(scope[1])) <= 0;
        } else {
            return originPrice.compareTo(new BigDecimal(scope[0])) >= 0;
        }
    }

    /**
     * 检查数量范围
     *
     * @param amount
     * @param scope
     * @return
     */
    private boolean checkQuantityScope(Integer amount, String... scope) {
        // 数量范围
        if (StrUtil.isNotBlank(scope[1])) {
            return amount >= Convert.toInt(scope[0], 1)
                    && amount <= Convert.toInt(scope[1]);
        } else {
            return amount >= Convert.toInt(scope[0], 1);
        }
    }

    /**
     * 检查时间范围
     *
     * @param period
     * @param scope
     * @return
     */
    private boolean checkTimeScope(BigDecimal period, String... scope) {
        if (StrUtil.isNotBlank(scope[1])) {
            return period.compareTo(Convert.toBigDecimal(scope[0], BigDecimal.ONE)) > -1
                    && period.compareTo(Convert.toBigDecimal(scope[1])) < 1;
        } else {
            return period.compareTo(Convert.toBigDecimal(scope[0], BigDecimal.ONE)) > -1;
        }
    }

    /**
     * 计算平台折扣
     *
     * @param discount         折扣
     * @param discounts        折扣
     * @param billingAccountId 计费帐户id
     * @param unitValue        单位价值
     * @param periodNum        期num
     * @param originalAmount   原始金额
     * @return {@link BigDecimal}
     */
    private BigDecimal computePlatformDiscount(BigDecimal discount, List<BizDiscount> discounts, Long billingAccountId, BigDecimal unitValue, Integer periodNum, BigDecimal originalAmount) {
        if (CollectionUtil.isEmpty(discounts)) {
            return discount;
        }
        List<BizDiscount> platformDiscounts = discounts.stream()
                .filter(item -> DiscountConstants.PLATFORM.equals(item.getDiscountType()))
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(platformDiscounts)) {
            return discount;
        }
        for (BizDiscount bizDiscount : platformDiscounts) {
            // 在应用范围
            if (checkDiscountScope(unitValue, periodNum, bizDiscount, originalAmount)) {
                discount = NumberUtil.mul(discount, bizDiscount.getDiscountRatio());
            }
        }

        return discount;
    }

    /**
     * 获取到商品单价
     *
     * @param inquiryPriceVo 签证官询问价格
     * @return {@link BigDecimal}
     */
    private void generateUnitPrice(MarketInquiryPriceVo inquiryPriceVo) {
        BigDecimal monthlyPrice;
        BigDecimal yearlyPrice;
        BigDecimal oneTimePrice = BigDecimal.ZERO;
        if (Objects.nonNull(inquiryPriceVo.getPriceJoinId())) {
            MarketShopPriceJoin shopPriceJoin = priceJoinService.getById(inquiryPriceVo.getPriceJoinId());
            MarketShop shop = marketShopService.getById(inquiryPriceVo.getShopId());
            inquiryPriceVo.setSellType(shop.getSellType());
            monthlyPrice = shopPriceJoin.getPrice();
            yearlyPrice = shopPriceJoin.getPrice();

            switch (inquiryPriceVo.getSellType()) {
                case 2:
                    inquiryPriceVo.setUnitPrice(monthlyPrice);
                    break;
                case 3:
                    inquiryPriceVo.setUnitPrice(yearlyPrice);
                    break;
                case 0:
                    inquiryPriceVo.setUnitPrice(oneTimePrice);
                    break;
                default:
                    break;
            }
        } else {
            List<SkuEnumDto> skuInfo = inquiryPriceVo.getSkuInfo();
            String skuName = skuInfo.stream().filter(e -> StrUtil.isNotBlank(e.getEnumValue())).map(e -> e.getEnumValue()).collect(Collectors.joining("|"));
            List<SkuEnumDto> collect = skuInfo.stream().filter(e -> Objects.nonNull(e.getPeriodNum())).collect(Collectors.toList());

            // 如果是空设置默认值
            inquiryPriceVo.setPeriodNum(CollectionUtil.isNotEmpty(collect) ? collect.get(0).getPeriodNum() : 1);
            if (Objects.isNull(inquiryPriceVo.getUnitValue())) {
                inquiryPriceVo.setUnitValue(1);
            }

            QueryWrapper<MarketShopSkuPrice> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda()
                    .eq(MarketShopSkuPrice::getShopId, inquiryPriceVo.getShopId())
                    .eq(MarketShopSkuPrice::getSkuName, skuName);
            MarketShopSkuPrice marketShopSkuPrice = shopSkuPriceMapper.selectOne(queryWrapper);
            if (Objects.isNull(marketShopSkuPrice)) {
                throw new BizException(WebUtil.getMessage(MsgCd.SHOP_SPEC_PRICE_ERROR));
            }
            inquiryPriceVo.setSkuName(marketShopSkuPrice.getSkuName());
            monthlyPrice = marketShopSkuPrice.getMonthlyPrice();
            yearlyPrice = marketShopSkuPrice.getYearlyPrice();
            oneTimePrice = marketShopSkuPrice.getOneTimePrice();

            switch (inquiryPriceVo.getPeriodType()) {
                case "month":
                    inquiryPriceVo.setUnitPrice(monthlyPrice);
                    break;
                case "year":
                    inquiryPriceVo.setUnitPrice(yearlyPrice);
                    break;
                case "one-time":
                    inquiryPriceVo.setUnitPrice(oneTimePrice);
                    break;
                default:
                    break;
            }
        }


        if (Objects.isNull(inquiryPriceVo.getUnitPrice())) {
            throw new BizException(WebUtil.getMessage(MsgCd.SHOP_SPEC_PRICE_ERROR));
        }
    }

    /**
     * 检查商品正确性
     *
     * @param shopId 商店id
     */
    private void checkShopById(String shopId) {
        MarketShop shop = marketShopService.getById(shopId);
        if (Objects.isNull(shop)) {
            throw new BizException(String.format(WebUtil.getMessage(MsgCd.ERROR_RES_NOT_FOUND), "商品"));
        }
        if (!MarketShopStatusEnum.ONLINE.getType().equals(shop.getStatus())) {
            throw new BizException(WebUtil.getMessage(MsgCd.SHOP_NOT_APPROVAL));
        }
    }


}
