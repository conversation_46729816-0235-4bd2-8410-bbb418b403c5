package cn.com.cloudstar.rightcloud.service.shop.impl;

import cn.com.cloudstar.rightcloud.data.dao.MarketShopPriceJoinMapper;
import cn.com.cloudstar.rightcloud.data.entity.MarketShopPriceJoin;
import cn.com.cloudstar.rightcloud.service.shop.MarketShopPriceJoinService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 *
 */
@Service
public class MarketShopPriceJoinServiceImpl extends ServiceImpl<MarketShopPriceJoinMapper, MarketShopPriceJoin>
    implements MarketShopPriceJoinService {

    @Resource
    private MarketShopPriceJoinMapper marketShopPriceJoinMapper;


    @Override
    public List<MarketShopPriceJoin> selectByShopId(String shopId) {
        return marketShopPriceJoinMapper.selectByShopId(shopId);
    }

    @Override
    public void removeByContentIds(List<String> delPriceAttrsList) {
        QueryWrapper<MarketShopPriceJoin> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("content_id",delPriceAttrsList);
        this.remove(queryWrapper);
    }
}




