package cn.com.cloudstar.rightcloud.service.feign;

import cn.com.cloudstar.rightcloud.common.config.FeignConfig;
import cn.com.cloudstar.rightcloud.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.data.request.discount.CreateDiscountRequest;
import cn.com.cloudstar.rightcloud.data.request.discount.OperateDiscountRequest;
import cn.com.cloudstar.rightcloud.data.request.discount.UpdateDiscountRequest;
import cn.com.cloudstar.rightcloud.data.request.market.ApplyMarketRequest;
import cn.com.cloudstar.rightcloud.data.request.market.DescribeSubscribeRequest;
import cn.com.cloudstar.rightcloud.data.request.market.MarketOrderPostingRequest;
import feign.Response;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

// @FeignClient(name = "http://127.0.0.1:8082", configuration = FeignConfig.class, path = "/api/v1/bss")
@FeignClient(name = "https://cmp-bss:8082", configuration = FeignConfig.class, path = "/api/v1/bss")
public interface BssServiceFeign {

    @ApiOperation(httpMethod = "POST", value = "订阅AI集市或者市场")
    @PostMapping("/service/apply/market")
    RestResult apply(@RequestBody ApplyMarketRequest request);

    @ApiOperation(httpMethod = "POST", value = "订单入账")
    @PostMapping("/billing_account/order_posting")
    RestResult orderPosting(@RequestBody MarketOrderPostingRequest request);

     @ApiOperation(httpMethod = "PUT", value = "允许开票")
     @PutMapping("/billing_account/allow_invoicing/{id}")
     RestResult allowInvoicing(@PathVariable("id") String billingCycleId);

    @PutMapping("/discount")
    RestResult updateDiscount(@Valid @RequestBody UpdateDiscountRequest request);

    @PostMapping("/discount")
    RestResult createDiscount(@Valid @RequestBody CreateDiscountRequest request);

    @DeleteMapping("/discount/{id}")
    RestResult deleteDiscount(@PathVariable("id") Long id);

    @PutMapping("/discount/operate")
    RestResult updateBillingStrategyStatus(@RequestBody OperateDiscountRequest request);

    @GetMapping("/subscribe/export/feign")
    Response expertSubscribeList(@SpringQueryMap DescribeSubscribeRequest request);

    /**
     * 用户接口权限认证
     *
     * @param userSid 用户sid
     * @return {@link RestResult }<{@link Boolean }>
     */
    @PostMapping("/authorize/role_auth/feign")
    RestResult<Boolean> roleAuth(@RequestParam(value = "userSid") Long userSid,
                                 @RequestParam(value = "requestURI") String requestURI,
                                 @RequestParam(value = "method") String method);
}
