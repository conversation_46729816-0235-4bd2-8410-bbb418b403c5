package cn.com.cloudstar.rightcloud.service.shop.impl;

import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

import cn.hutool.core.convert.Convert;

import cn.com.cloudstar.rightcloud.common.camunda.CamundaHelper;
import cn.com.cloudstar.rightcloud.common.camunda.service.ExecutionProcessEngine;
import cn.com.cloudstar.rightcloud.common.enums.CamundaExecutionEnum;
import cn.com.cloudstar.rightcloud.common.enums.MarketShopStatusEnum;
import cn.com.cloudstar.rightcloud.common.enums.ProcessStatusEnum;
import cn.com.cloudstar.rightcloud.data.dao.MarketShopMapper;
import cn.com.cloudstar.rightcloud.data.entity.MarketShop;

/**
 * <AUTHOR>
 */
@Component
public class MarketAuditEndImpl implements ExecutionProcessEngine {

    @Autowired
    private MarketShopMapper marketShopMapper;

    @Override
    public void execution(DelegateExecution delegateExecution) {
        MarketShop shop = new MarketShop();
        Date now = new Date();
        shop.setShopId(delegateExecution.getBusinessKey());
        shop.setUpdatedDt(now);
        shop.setUpdatedBy(Convert.toStr(delegateExecution.getVariable(CamundaHelper.defaultLastAssigneeVariable)));
        Object auditResult = delegateExecution.getVariable(CamundaHelper.defaultConditionVariable);
        String auditStatus = MarketShopStatusEnum.REFUSE.getType();
        if (Objects.equals(auditResult, ProcessStatusEnum.APPROVE.name())) {
            auditStatus = MarketShopStatusEnum.ONLINE.getType();
            shop.setPublishDt(now);
            shop.setPublishBy(Convert.toStr(delegateExecution.getVariable(CamundaHelper.defaultLastAssigneeVariable)));
        }
        shop.setStatus(auditStatus);
        marketShopMapper.updateById(shop);
    }

    @Override
    public CamundaExecutionEnum currentTriggerExecution() {
        return CamundaExecutionEnum.END_EVENT;
    }
}
