package cn.com.cloudstar.rightcloud.service.task;

import cn.com.cloudstar.rightcloud.service.shop.MarketShopSuperviseRecordService;
import lombok.extern.log4j.Log4j2;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
* 定时任务的使用
* <AUTHOR>
**/
@Component
@Log4j2
public class ShopSubscriptionTask {

   @Resource
   private MarketShopSuperviseRecordService shopSuperviseRecordService;
   /**
    * 处决,每小时一次
    */
   @Scheduled(cron = "0 0 * * * ? ")
   // @Scheduled(cron = "0 * 0/1 * * ? ")
   public void execute(){
       log.info("未完成资金监管扫描开始");
       shopSuperviseRecordService.IncompleteOrders();
       log.info("未完成资金监管扫描结束");
   }
}
