package cn.com.cloudstar.rightcloud.service.cfn;


import java.util.List;

import cn.com.cloudstar.rightcloud.data.request.cfn.AlgorithmOperateReq;
import cn.com.cloudstar.rightcloud.data.request.cfn.AlgorithmPageReq;
import cn.com.cloudstar.rightcloud.data.request.cfn.CreatePrivateModelReq;
import cn.com.cloudstar.rightcloud.data.request.cfn.DataStorageOperateReq;
import cn.com.cloudstar.rightcloud.data.request.cfn.DataStorageResourcesPageReq;
import cn.com.cloudstar.rightcloud.data.response.cfn.AlgorithmPageResp;
import cn.com.cloudstar.rightcloud.data.response.cfn.ClusterInfoResp;
import cn.com.cloudstar.rightcloud.data.response.cfn.DataStorageResourcePageResp;
import cn.com.cloudstar.rightcloud.data.response.cfn.PageResult;

/**
 * <AUTHOR>
 * @date 2024/8/23 14:15
 */
public interface CfnService {

    PageResult<DataStorageResourcePageResp> getDatasetPage(DataStorageResourcesPageReq req);

    /**
     * 我的算法 分页查询
     *
     * @param req req
     */
    PageResult<AlgorithmPageResp> getMyAlgorithmPage(AlgorithmPageReq req);

    /**
     * 发布算法
     *
     * @param req req
     */
    Long algorithmPublish(AlgorithmOperateReq req);

    Long datasetPublish(DataStorageOperateReq req);

    /**
     * 订阅算法
     *
     * @param req req
     */
    Boolean algorithmSubscribe(AlgorithmOperateReq req);

    Boolean datasetSubscribe(DataStorageOperateReq req);

    /**
     * 退订算法
     *
     * @param req req
     */
    Boolean algorithmUnsubscribe(AlgorithmOperateReq req);

    Boolean datasetUnsubscribe(DataStorageOperateReq req);

    List<ClusterInfoResp> getPrivateModelCluster(String account);

    Boolean createPrivateModel(CreatePrivateModelReq req);
}
