package cn.com.cloudstar.rightcloud.service.discount.impl;

import cn.com.cloudstar.rightcloud.data.dao.SysMUserMapper;
import cn.com.cloudstar.rightcloud.data.entity.SysMUser;
import cn.com.cloudstar.rightcloud.service.discount.SysMUserService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
* <AUTHOR>
* @description 针对表【sys_m_user(用户表)】的数据库操作Service实现
* @createDate 2023-08-16 18:46:30
*/
@Service
public class SysMUserServiceImpl extends ServiceImpl<SysMUserMapper, SysMUser>
implements SysMUserService {

    @Resource
    private SysMUserMapper sysMUserMapper;

    /**
     * 查询注册供应商个数
     * @return int
     */
    @Override
    public int countProviderUserNum() {

        return sysMUserMapper.countProviderUserNum();
    }

    @Override
    public Integer getRegisteredUserCount() {
        return  sysMUserMapper.queryNumberAllTenants();
    }
}
