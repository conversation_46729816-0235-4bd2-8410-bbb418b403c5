package cn.com.cloudstar.rightcloud.service.discount;

import cn.com.cloudstar.rightcloud.data.entity.SysMUser;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【sys_m_user(用户表)】的数据库操作Service
* @createDate 2023-08-16 18:46:30
*/
public interface SysMUserService extends IService<SysMUser> {
    /**
     * 查询注册供应商个数
     * @return int
     */
    int countProviderUserNum();

    Integer getRegisteredUserCount();
}
