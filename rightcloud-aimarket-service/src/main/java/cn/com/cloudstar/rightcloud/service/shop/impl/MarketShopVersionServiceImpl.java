package cn.com.cloudstar.rightcloud.service.shop.impl;

import cn.com.cloudstar.rightcloud.adapter.core.MQException;
import cn.com.cloudstar.rightcloud.adapter.core.MQHelper;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopVersionCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopVersionCreateTo;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopVersionUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopVersionUpdateTo;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.pojo.VersionAlgorithm;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.pojo.VersionContentInfo;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiShopVersionCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiShopVersionCreateResultTo;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiShopVersionUpdateResultTo;
import cn.com.cloudstar.rightcloud.common.constants.RestConst;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.enums.MarketShopSourceEnum;
import cn.com.cloudstar.rightcloud.common.enums.MarketShopStatusEnum;
import cn.com.cloudstar.rightcloud.common.exception.BizException;
import cn.com.cloudstar.rightcloud.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.common.util.PropertiesUtil;
import cn.com.cloudstar.rightcloud.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.common.util.UuidUtil;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.data.dao.MarketShopMapper;
import cn.com.cloudstar.rightcloud.data.dao.MarketShopPriceJoinMapper;
import cn.com.cloudstar.rightcloud.data.dao.MarketShopSubscribeMapper;
import cn.com.cloudstar.rightcloud.data.dao.MarketShopVersionMapper;
import cn.com.cloudstar.rightcloud.data.dao.SysMUserMapper;
import cn.com.cloudstar.rightcloud.data.entity.MarketShop;
import cn.com.cloudstar.rightcloud.data.entity.MarketShopPriceJoin;
import cn.com.cloudstar.rightcloud.data.entity.MarketShopVersion;
import cn.com.cloudstar.rightcloud.data.request.cfn.AlgorithmOperateReq;
import cn.com.cloudstar.rightcloud.data.request.cfn.DataStorageOperateReq;
import cn.com.cloudstar.rightcloud.data.request.market.MarketVersionBatchReq;
import cn.com.cloudstar.rightcloud.data.request.market.MarketVersionStatusReq;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicWebUtil;
import cn.com.cloudstar.rightcloud.service.cfn.CfnService;
import cn.com.cloudstar.rightcloud.service.shop.MarketAuditService;
import cn.com.cloudstar.rightcloud.service.shop.MarketShopVersionService;
import cn.com.cloudstar.rightcloud.service.util.BaseMockUtil;
import cn.com.cloudstar.rightcloud.service.util.BaseUtil;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import lombok.extern.slf4j.Slf4j;

import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 *
 */
@Service
@Slf4j
public class MarketShopVersionServiceImpl extends ServiceImpl<MarketShopVersionMapper, MarketShopVersion>
        implements MarketShopVersionService {

    @Resource
    private MarketShopPriceJoinMapper priceJoinMapper;
    @Resource
    private MarketShopMapper shopMapper;

    @Resource
    private MarketAuditService marketAuditService;

    @Resource
    private MarketShopVersionMapper versionMapper;

    @Resource
    private CfnService cfnService;

    @Resource
    private SysMUserMapper userMapper;

    @Resource
    private MarketShopSubscribeMapper marketShopSubscribeMapper;


    /**
     * 算法来源是bms
     */
    private final static String ALGORITHM_SOURCE_BMS = "BMS";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RestResult batchAdd(MarketVersionBatchReq req) {
        MarketShopVersionServiceImpl marketShopVersionService = (MarketShopVersionServiceImpl) AopContext.currentProxy();
        MarketShop shop = priceJoinMapper.selectByContentId(req.getContentIds().get(0));
        String shopId = shop.getShopId();
        Assert.notNull(shopId, "商品数据未找到！");
        req.setShopId(shopId);

       try {

            marketShopVersionService.bathCreateAiHuhShopVersion(req);
            // 修改商品状态 并且发起审批单
            modifyStatusProduct(shop, false);

        } catch (Exception e) {
            log.error("创建商品版本失败：", e);
            marketShopVersionService.updateShopStatus(req);
            throw new BizException(WebUtil.getMessage(MsgCd.ERROR_CREATE_SHOP_VERSION));
        }

        return RestResult.newSuccess();
    }

    /**
     * 修改状态产品
     *
     * @param shop    商店
     * @param isRetry 正在重试
     */
    private void modifyStatusProduct(MarketShop shop, boolean isRetry) {
        // 如果商品状态为已发布或发布错误，并且不是重试操作，则将商品状态设置为已上架
        if ((MarketShopStatusEnum.RELEASE.getType().equals(shop.getStatus()) || MarketShopStatusEnum.ONERROR.getType().equals(shop.getStatus())) && !isRetry) {
            shop.setStatus(MarketShopStatusEnum.ONLINE.getType());
            // 如果是发布并且供应商，需要生成审批单
            if (shop.getShopSource().equals(MarketShopSourceEnum.SUPPLIER.getType())) {
                shop.setStatus(MarketShopStatusEnum.PENDING.getType());
                marketAuditService.startShopProcess(shop.getShopId());
            }
            shopMapper.updateById(shop);
        }

        // 如果商品状态为保存中、保存错误或商品发布中（仅当是重试操作时），则将商品状态设置为未发布
        if (MarketShopStatusEnum.SAVING.getType().equals(shop.getStatus()) || MarketShopStatusEnum.SAVERROR.getType().equals(shop.getStatus())
                || (MarketShopStatusEnum.RELEASE.getType().equals(shop.getStatus()) && isRetry)) {
            shop.setStatus(MarketShopStatusEnum.UNPUBLISHED.getType());
            shopMapper.updateById(shop);
        }
    }


    @Override
    public RestResult resetBatchAdd(String shopId) {
        MarketShop marketShop = shopMapper.selectById(shopId);
        List<MarketShopPriceJoin> marketShopPriceJoinList = priceJoinMapper.selectByShopId(shopId);
        List<String> contentIdList = marketShopPriceJoinList.stream().map(MarketShopPriceJoin::getContentId).collect(Collectors.toList());
        this.batchAdd(new MarketVersionBatchReq(contentIdList, marketShop.getArithmetic(), marketShop.getVersionNum(), marketShop.getVersionDesc(),
                                                marketShop.getShopId(),""));
        return RestResult.newSuccess();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public RestResult add(MarketShopVersion req) {
        // 版本号不能重复
        QueryWrapper<MarketShopVersion> queryWrapper = new QueryWrapper<>();
                 queryWrapper.lambda().eq(MarketShopVersion::getShopId, req.getShopId())
                .eq(MarketShopVersion::getVersionNum, req.getVersionNum());
        Assert.isNull(this.getOne(queryWrapper), "资产版本已存在");

        List<MarketShopPriceJoin> marketShopPriceJoinList = priceJoinMapper.selectByShopId(req.getShopId());

        try {
            MarketShopVersionServiceImpl marketShopVersionService = (MarketShopVersionServiceImpl) AopContext.currentProxy();
            marketShopVersionService.bathCreateAiHuhShopVersion(
                    new MarketVersionBatchReq(marketShopPriceJoinList.stream().map(MarketShopPriceJoin::getContentId).collect(Collectors.toList()),
                                              req.getAlgorithm(), req.getVersionNum(), req.getVersionDesc(), req.getShopId(),""));
        } catch (Exception e) {
            log.error("创建商品版本失败：", e);
            return RestResult.newFail();
        }
        // 查询商品
        MarketShop shop = Optional.ofNullable(shopMapper.selectById(req.getShopId())).orElseThrow(
                () -> new BizException("商品不存在!")
        );

        // 修改商品状态 并且发起审批单
        modifyStatusProduct(shop, true);
        return RestResult.newSuccess();
    }

    @Override
    public List<MarketShopVersion> getByShopId(String shopId) {
        QueryWrapper<MarketShopVersion> queryWrapper = new QueryWrapper<>();

        queryWrapper.lambda().orderByDesc(MarketShopVersion::getCreatedDt)
                    .eq(MarketShopVersion::getShopId, shopId)
//                .eq(MarketShopVersion::getStatus, MarketShopStatusEnum.ONLINE)
                    .groupBy(MarketShopVersion::getVersionNum);
        return this.list(queryWrapper);
    }

    @Override
    public RestResult updateStatus(MarketVersionStatusReq req) {

        try {
            this.bathUpdateAiHuhShopVersion(req);
        } catch (Exception e) {
            log.error("修改商品版本状态失败：", e);
            return RestResult.newFail();
        }
        return RestResult.newSuccess();
    }

    @Override
    public void removeByContentIds(List<String> delPriceAttrsList) {
        QueryWrapper<MarketShopVersion> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("ai_hub_content_id", delPriceAttrsList);
        this.remove(queryWrapper);
    }


    /**
     * 该方法不能有事务，因为创建商品版本报错的话需要修改商品状态 版本创建失败的话改成发布中
     *
     * @param req
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.NOT_SUPPORTED)
    public void updateShopStatus(MarketVersionBatchReq req) {
        // 版本创建失败的话改成发布中
        MarketShop shop = priceJoinMapper.selectByContentId(req.getContentIds().get(0));
        if (MarketShopStatusEnum.SAVING.getType().equals(shop.getStatus())) {
            shop.setStatus(MarketShopStatusEnum.SAVERROR.getType());
        }
        if (MarketShopStatusEnum.RELEASE.getType().equals(shop.getStatus())) {
            shop.setStatus(MarketShopStatusEnum.ONERROR.getType());
        }
        shopMapper.updateById(shop);
    }


    /**
     * 批量添加商品版本
     *
     * @param req
     *
     * @throws MQException
     */
    @Transactional(rollbackFor = Exception.class)
    public void bathCreateAiHuhShopVersion(MarketVersionBatchReq req) throws MQException {
        log.info("添加{}版本发布订阅",req.getShopType());
        final MarketShop shop = shopMapper.selectById(req.getShopId());
        if (Objects.nonNull(shop.getShopType()) && shop.getShopType() == 3) {
            req.setShopType("dataset");
        }
        if (!StrUtil.equals(shop.getAiHubSource(), ALGORITHM_SOURCE_BMS)) {
            AiShopVersionCreateTo versionCreate = this.getAiShopVersionBatchCreate(req);
            AiShopVersionCreateResultTo shopVersionCreateResult;
            log.info("创建资产版本参数信息：{}", versionCreate);
            shopVersionCreateResult = (AiShopVersionCreateResultTo) MQHelper.rpc(versionCreate);
            log.info("创建资产版本返回信息：{}", shopVersionCreateResult);
            Assert.isTrue(shopVersionCreateResult.isSuccess(), shopVersionCreateResult.getErrMsg());

            List<AiShopVersionCreateResult> resultList = shopVersionCreateResult.getResultList();
            List<MarketShopVersion> list = new ArrayList<>(resultList.size());
            BasicWebUtil.prepareInsertParams(req);
            for (AiShopVersionCreateResult createResult : resultList) {
                MarketShopVersion marketShopVersion = BeanUtil.toBean(req, MarketShopVersion.class);
                marketShopVersion.setAiHubSource(PropertiesUtil.getProperty("market_shop_source"));
                marketShopVersion.setAiHubArea(PropertiesUtil.getProperty("market_shop_area"));
                marketShopVersion.setStatus(MarketShopStatusEnum.ONLINE.getType());
                marketShopVersion.setAiHubVersionId(createResult.getVersion_id());
                marketShopVersion.setAiHubContentId(createResult.getContent_id());
                list.add(marketShopVersion);
            }
            this.saveBatch(list);
        } else {
            AuthUser authUser = RequestContextUtil.getAuthUserInfo();
            if (authUser == null) {
                throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
            }
            //查询登录用户是否是管理员账号
            final List<Long> roleList = userMapper.selectUserRole(authUser.getUserSid());
            log.info("当前用户的角色ID:{}", roleList);
            boolean isAdmin = false;
            if (roleList.contains("302") || roleList.contains("306")) {
                isAdmin = false;
            } else {
                isAdmin = true;
            }
            log.info("当前用户是否管理员:{}", isAdmin);
            List<MarketShopVersion> list = new ArrayList<>();
            BasicWebUtil.prepareInsertParams(req);
            //只创建一个版本
            final String contentId = req.getContentIds().get(0);
            final List<MarketShopVersion> versionList = this.versionMapper.selectList(
                    new LambdaQueryWrapper<MarketShopVersion>().eq(MarketShopVersion::getAiHubContentId, contentId)
                                                               .eq(MarketShopVersion::getAlgorithm, req.getAlgorithm()));
            if (CollectionUtil.isNotEmpty(versionList)) {
                log.info("算法版本已经发布过，请勿重复发布。aiHubContentId:{},algorithm:{}", contentId, req.getAlgorithm());
                return;
            }

            Long id = null;
            if ("dataset".equals(req.getShopType())) {
                DataStorageOperateReq req1 = new DataStorageOperateReq();
                req1.setIsAdmin(isAdmin);
                req1.setAccount(authUser.getAccount());
                req1.setDataStorageId(req.getAlgorithm());
                id = cfnService.datasetPublish(req1);
                if (Objects.isNull(id)) {
                    throw new BizException("添加数据集版本失败");
                }
            } else {
                //算力平台发布算法
                AlgorithmOperateReq algorithmOperateReq = new AlgorithmOperateReq();
                algorithmOperateReq.setIsAdmin(isAdmin);
                algorithmOperateReq.setAccount(authUser.getAccount());
                algorithmOperateReq.setAlgorithmId(req.getAlgorithm());
                id = cfnService.algorithmPublish(algorithmOperateReq);
                if (ObjectUtil.isEmpty(id)) {
                    throw new BizException("添加算法版本失败");
                }
            }

            MarketShopVersion marketShopVersion = BeanUtil.toBean(req, MarketShopVersion.class);
            marketShopVersion.setShopId(shop.getShopId());
            marketShopVersion.setAiHubSource(ALGORITHM_SOURCE_BMS);
            marketShopVersion.setAiHubArea(ALGORITHM_SOURCE_BMS);
            marketShopVersion.setStatus(MarketShopStatusEnum.ONLINE.getType());
            marketShopVersion.setAiHubVersionId(UuidUtil.getUuid());
            marketShopVersion.setAiHubContentId(contentId);
            marketShopVersion.setAlgorithm(id.toString());
            list.add(marketShopVersion);
            this.saveBatch(list);
            syncSubscribe(shop.getShopId(), id, "dataset".equals(req.getShopType()) ? "dataset" : "");

        }

    }

    /**
     * 异步订阅算法
     *
     * @param shopId 商品id
     */
    private void syncSubscribe(String shopId, Long algorithmId,String shopType) {
        new Thread(() -> {
            //睡三分钟 等算法完全发布
            try {
                Thread.sleep(3 * 60 * 1000);
                //查询商品的订阅人 已经订阅过得 自动给商品添加订阅
                final List<String> accountList = marketShopSubscribeMapper.getCompleteSubAccount(shopId);
                if (CollectionUtil.isNotEmpty(accountList)) {
                    accountList.forEach(account -> {
                        if ("dataset".equals(shopType)) {
                            DataStorageOperateReq subReq = new DataStorageOperateReq();
                            subReq.setIsAdmin(false);
                            subReq.setAccount(account);
                            subReq.setPublishId(algorithmId.toString());
                            cfnService.datasetSubscribe(subReq);
                        } else {
                            //算力平台发布算法
                            AlgorithmOperateReq subReq = new AlgorithmOperateReq();
                            subReq.setIsAdmin(false);
                            subReq.setAccount(account);
                            subReq.setPublishId(algorithmId.toString());
                            cfnService.algorithmSubscribe(subReq);
                        }
                    });
                }
            } catch (Exception e) {
                log.error("自动订阅算法失败:", e);
            }

        }).start();
    }


    /**
     * 组装数据
     *
     * @param req 数据
     *
     * @return 数据
     */
    private AiShopVersionCreateTo getAiShopVersionBatchCreate(MarketVersionBatchReq req) {
        List<String> contentIds = req.getContentIds();
        List<AiShopVersionCreate> result = new ArrayList<>(contentIds.size());
        for (String contentId : contentIds) {
            AiShopVersionCreate versionCreate = new AiShopVersionCreate();
            versionCreate.setContent_id(contentId);
            versionCreate.setContent_info(new VersionContentInfo(req.getVersionNum(), req.getVersionDesc()));
            versionCreate.setAlgorithm(new VersionAlgorithm(req.getAlgorithm()));
            String flag = System.getenv("LARGE_MODEL_MOCK");
            if ("true".equals(flag)) {
                BaseMockUtil.extracted(versionCreate);
            } else {
                BaseUtil.extracted(versionCreate);
            }
            result.add(versionCreate);
        }
        AiShopVersionCreateTo aiShopVersionCreateTo = new AiShopVersionCreateTo(result);
        String flag = System.getenv("LARGE_MODEL_MOCK");
        if ("true".equals(flag)) {
            BaseMockUtil.extracted(aiShopVersionCreateTo);
        } else {
            BaseUtil.extracted(aiShopVersionCreateTo);
        }
        return aiShopVersionCreateTo;
    }


    /**
     * 批量修改商品版本
     *
     * @param req 商品id
     *
     * @throws MQException
     */
    @Transactional(rollbackFor = Exception.class)
    public void bathUpdateAiHuhShopVersion(MarketVersionStatusReq req) throws MQException {
        final MarketShop shop = shopMapper.selectById(req.getShopId());
        if (!shop.getAiHubSource().equals(ALGORITHM_SOURCE_BMS)) {
            AiShopVersionUpdateTo versionUpdateTo = this.getAiShopVersionBatchUpdate(req);
            AiShopVersionUpdateResultTo shopVersionCreateToResult;
            log.info("修改资产版本参数信息：{}", versionUpdateTo);
            shopVersionCreateToResult = (AiShopVersionUpdateResultTo) MQHelper.rpc(versionUpdateTo);
            log.info("修改资产版本返回信息：{}", shopVersionCreateToResult);
            Assert.isTrue(shopVersionCreateToResult.isSuccess(), shopVersionCreateToResult.getErrMsg());
        }
        versionMapper.updateVersion(req);
    }

    /**
     * 组装数据
     *
     * @param req 数据
     *
     * @return 数据
     */
    private AiShopVersionUpdateTo getAiShopVersionBatchUpdate(MarketVersionStatusReq req) {
        QueryWrapper<MarketShopVersion> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(MarketShopVersion::getShopId, req.getShopId())
                    .eq(MarketShopVersion::getVersionNum, req.getVersionNum());
        List<MarketShopVersion> versionList = this.list(queryWrapper);

        String reqStatus = req.getStatus();
        String status = versionList.get(0).getStatus();
        if ((MarketShopStatusEnum.OFFLINE.getType().equals(reqStatus) && !MarketShopStatusEnum.ONLINE.getType().equals(status))
                || (MarketShopStatusEnum.ONLINE.getType().equals(reqStatus) && !MarketShopStatusEnum.OFFLINE.getType().equals(status))) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }

        List<AiShopVersionUpdate> list = new ArrayList<>(versionList.size());
        for (MarketShopVersion version : versionList) {
            AiShopVersionUpdate versionUpdate = new AiShopVersionUpdate();
            versionUpdate.setContent_id(version.getAiHubContentId());
            versionUpdate.setVersion_id(version.getAiHubVersionId());
            if (StrUtil.isNotBlank(req.getStatus())) {
                versionUpdate.setStatus(MarketShopStatusEnum.toStatus(req.getStatus()));
            }
            if (StrUtil.isNotBlank(req.getVersionDesc())) {
                versionUpdate.setVersion_desc(req.getVersionDesc());
            }

            String flag = System.getenv("LARGE_MODEL_MOCK");
            if ("true".equals(flag)) {
                BaseMockUtil.extracted(versionUpdate);
            } else {
                BaseUtil.extracted(versionUpdate);
            }
            list.add(versionUpdate);
        }
        AiShopVersionUpdateTo result = new AiShopVersionUpdateTo(list);
        String flag = System.getenv("LARGE_MODEL_MOCK");
        if ("true".equals(flag)) {
            BaseMockUtil.extracted(result);
        } else {
            BaseUtil.extracted(result);
        }
        return result;
    }
}




