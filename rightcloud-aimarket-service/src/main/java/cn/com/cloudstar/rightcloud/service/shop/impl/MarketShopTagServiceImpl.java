package cn.com.cloudstar.rightcloud.service.shop.impl;

import cn.com.cloudstar.rightcloud.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.common.util.PageUtil;
import cn.com.cloudstar.rightcloud.data.dao.MarketShopTagMapper;
import cn.com.cloudstar.rightcloud.data.entity.MarketShopTag;
import cn.com.cloudstar.rightcloud.data.request.market.MarketShopTagPortalRequest;
import cn.com.cloudstar.rightcloud.data.request.market.MarketShopTagReq;
import cn.com.cloudstar.rightcloud.data.request.market.MarketShopTagTreeReq;
import cn.com.cloudstar.rightcloud.data.response.market.MarketShopTagPortalResp;
import cn.com.cloudstar.rightcloud.data.response.market.MarketShopTagResp;
import cn.com.cloudstar.rightcloud.data.response.market.MarketShopTagTreeResp;
import cn.com.cloudstar.rightcloud.service.shop.MarketShopTagService;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import org.checkerframework.checker.units.qual.C;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 *
 */
@Service
public class MarketShopTagServiceImpl extends ServiceImpl<MarketShopTagMapper, MarketShopTag>
    implements MarketShopTagService {

    /**
     * 子标签标识(三级标签）
     */
    private static final String CHILD_TAG = "child";

    /**
     * 二级标签标识
     */
    private static final String LABEL_TAG = "ALL";

    /**
     * 商品分类
     */
    private static final String TAG_CATEGORY_ONE = "1";

    /**
     * 商品标签
     */
    private static final String TAG_CATEGORY_ONE_NAME = "商品标签";

    /**
     * 交付方式
     */
    private static final String TAG_CATEGORY_TWO = "2";

    /**
     * 交付方式
     */
    private static final String TAG_CATEGORY_TWO_NAME = "交付方式";

    @Resource
    private MarketShopTagMapper marketShopTagMapper;

    @Override
    public List<MarketShopTag> selectByShopId(String shopId) {
        return marketShopTagMapper.selectByShopId(shopId);
    }

    @Override
    public IPage<MarketShopTagResp> getList(MarketShopTagReq req) {
        Page<MarketShopTagResp> page = PageUtil.preparePageParams(req);
        IPage<MarketShopTagResp> result = marketShopTagMapper.getList(page,req);
        return result;
    }

    @Override
    public RestResult<List<MarketShopTagPortalResp>> queryShopLabels(MarketShopTagPortalRequest request) {
        QueryWrapper<MarketShopTag> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().orderByAsc(MarketShopTag::getTagCategory);
        queryWrapper.lambda().orderByAsc(MarketShopTag::getParentId);
        if(StrUtil.isNotBlank(request.getTagCategory())){
            queryWrapper.lambda().eq(MarketShopTag::getTagCategory, request.getTagCategory());
        }
        if(!Objects.isNull(request.getShopType())){
            queryWrapper.lambda().eq(MarketShopTag::getShopType, request.getShopType());
        }
        if(StrUtil.isNotBlank(request.getShopTypes())){
            String[] shopTypeList = request.getShopTypes().split(",");
            queryWrapper.lambda().in(MarketShopTag::getShopType, shopTypeList);
        }
        if(CHILD_TAG.equals(request.getTagCategory())){
            queryWrapper.lambda().ne(MarketShopTag::getParentId, 0L);
        }
        if(LABEL_TAG.equals(request.getTagCategory())){
            queryWrapper.lambda().eq(MarketShopTag::getParentId, 0L);
        }
        List<MarketShopTag> result = marketShopTagMapper.selectList(queryWrapper);
        List<MarketShopTagPortalResp> marketShopTagPortalResps = BeanUtil.copyToList(result, MarketShopTagPortalResp.class);
        for(MarketShopTagPortalResp tag : marketShopTagPortalResps){
            if(tag.getParentId() == 0 ? true : false){
                List<MarketShopTagPortalResp> child = new ArrayList();
                for(MarketShopTagPortalResp childTag : marketShopTagPortalResps){
                    if(tag.getId().equals(childTag.getParentId())){
                        child.add(childTag);
                    }
                }
                tag.setMarketShopTag(child);
            }
        }

        return new RestResult(marketShopTagPortalResps);
    }

    @Override
    public RestResult<List<MarketShopTag>> getLabels(String shopType) {

        QueryWrapper<MarketShopTag> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(MarketShopTag::getShopType, shopType);
        List<MarketShopTag> marketShopTag = marketShopTagMapper.selectList(queryWrapper);

        return new RestResult(marketShopTag);
    }

    @Override
    public RestResult getParentTag(Integer shopType) {
        QueryWrapper<MarketShopTag> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(MarketShopTag::getParentId, 0);
        wrapper.lambda().eq(MarketShopTag::getTagCategory, TAG_CATEGORY_ONE);
        // wrapper.lambda().eq(MarketShopTag::getShopType, shopType);
        wrapper.lambda().in(MarketShopTag::getShopType, Arrays.asList(shopType,5 ));
        return new RestResult(this.list(wrapper));
    }

    @Override
    public RestResult getLabelTagTree(MarketShopTagTreeReq req) {
        QueryWrapper<MarketShopTag> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().orderByAsc(MarketShopTag::getTagCategory);
        queryWrapper.lambda().orderByAsc(MarketShopTag::getParentId);
        if(StrUtil.isNotBlank(req.getTagCategory())){
            queryWrapper.lambda().eq(MarketShopTag::getTagCategory, req.getTagCategory());
        }
        if(!Objects.isNull(req.getShopType())){
            //queryWrapper.lambda().eq(MarketShopTag::getShopType, req.getShopType());
            //默认携带全部的标签 5
            queryWrapper.lambda().in(MarketShopTag::getShopType, Arrays.asList(req.getShopType(),5 ));
        }
        List<MarketShopTag> result = marketShopTagMapper.selectList(queryWrapper);
        List<MarketShopTagTreeResp> marketShopTagResps = BeanUtil.copyToList(result, MarketShopTagTreeResp.class);

        //前端回显拥有的标签
        if(Objects.nonNull(req.getShopId())){
            List<MarketShopTag> marketShopTags = marketShopTagMapper.selectByShopId(req.getShopId());
            List<Long> tagList = new ArrayList<>();
            if(CollectionUtil.isNotEmpty(marketShopTags)){
                tagList = marketShopTags.stream().map(MarketShopTag::getId).collect(Collectors.toList());
            }
            if(CollectionUtil.isNotEmpty(marketShopTagResps) && CollectionUtil.isNotEmpty(tagList)){
                List<Long> finalTagList = tagList;
                marketShopTagResps.stream().forEach(tag ->{
                    if(finalTagList.contains(tag.getId())){
                        tag.setSelect(true);
                    }
                });
            }
        }

        //建立标签树
        for(MarketShopTagTreeResp tag : marketShopTagResps){
            if(tag.getParentId() == 0 ? true : false){
                List<MarketShopTagTreeResp> child = new ArrayList();
                for(MarketShopTagTreeResp childTag : marketShopTagResps){
                    if(tag.getId().equals(childTag.getParentId())){
                        child.add(childTag);
                    }
                }
                if(CollectionUtil.isNotEmpty(child)){
                    tag.setMarketShopTag(child);
                }
            }
        }
        List<MarketShopTagTreeResp> marketShopTagRespList = marketShopTagResps.stream().filter(tag -> tag.getParentId() == 0).collect(Collectors.toList());
        List<MarketShopTagTreeResp> marketShopTagTree = new ArrayList<>();
        if(CollectionUtil.isNotEmpty(marketShopTagRespList)){
            //商品分类标签树
            List<MarketShopTagTreeResp> marketShopTagOne = marketShopTagRespList.stream()
                                                                    .filter(it -> TAG_CATEGORY_ONE.equals(it.getTagCategory()))
                                                                    .collect(Collectors.toList());
            //交付方式标签树
            List<MarketShopTagTreeResp> marketShopTagTwo = marketShopTagRespList.stream()
                                                                             .filter(it -> TAG_CATEGORY_TWO.equals(it.getTagCategory()))
                                                                             .collect(Collectors.toList());
            //顶层商品分类特殊处理
            MarketShopTagTreeResp marketShopTagOneTop = new MarketShopTagTreeResp();
            if(CollectionUtil.isNotEmpty(marketShopTagOne)){
                marketShopTagOneTop.setId(-2L);
                marketShopTagOneTop.setTagName(TAG_CATEGORY_ONE_NAME);
                marketShopTagOneTop.setMarketShopTag(marketShopTagOne);
                marketShopTagTree.add(marketShopTagOneTop);
            }
            //顶层交付方式特殊处理
            MarketShopTagTreeResp marketShopTagTwoTop = new MarketShopTagTreeResp();
            if(CollectionUtil.isNotEmpty(marketShopTagTwo)){
                marketShopTagTwoTop.setId(-1L);
                marketShopTagTwoTop.setTagName(TAG_CATEGORY_TWO_NAME);
                marketShopTagTwoTop.setMarketShopTag(marketShopTagTwo);
                marketShopTagTree.add(marketShopTagTwoTop);
            }
            if(Objects.isNull(marketShopTagTree)){
                return new RestResult<>();
            }
        }
        return new RestResult(marketShopTagTree);
    }

    @Override
    public int countLabelNum() {

        return marketShopTagMapper.countLabelNum();
    }

}




