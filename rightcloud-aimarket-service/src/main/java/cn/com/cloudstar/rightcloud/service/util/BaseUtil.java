package cn.com.cloudstar.rightcloud.service.util;

import cn.com.cloudstar.rightcloud.basic.data.dao.user.BasicUserMapper;
import cn.com.cloudstar.rightcloud.basic.data.pojo.base.Base;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.exception.BizException;
import cn.com.cloudstar.rightcloud.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.common.util.CrytoUtilSimple;
import cn.com.cloudstar.rightcloud.common.util.SpringContextHolder;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.common.util.json.JsonUtil;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.User;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicInfoUtil;
import cn.com.cloudstar.rightcloud.module.support.access.util.BeanUtil;
import cn.com.cloudstar.rightcloud.service.feign.ResourceServiceFeign;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.Objects;
import java.util.Properties;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/9/6
 */
@Slf4j
public class BaseUtil {

    private static final  BasicUserMapper basicUserMapper;
    private static final  ResourceServiceFeign resourceServiceFeign;

    static {
        resourceServiceFeign = SpringContextHolder.getBean(ResourceServiceFeign.class);
        basicUserMapper = SpringContextHolder.getBean(BasicUserMapper.class);
    }



    public static void extracted(Base base) {
        //填充运营云环境信息
        publicInformation(base);

        //租户填充自己的ak sk
        if (!isAdmin()) {
            tenantInformation(base, null);
        }

        //解密ak sk
        decryption(base);
    }

    /**
     * @param base    填充公共信息
     * @param userSid 指定用户id
     */
    public static void extracted(Base base, Long userSid) {
        //填充运营云环境信息
        publicInformation(base);

        //租户填充自己的ak sk
        if (userSid != null) {
            tenantInformation(base, userSid);
        }

        //解密ak sk
        decryption(base);
    }

    private static void decryption(Base base) {
        base.setApiKey(CrytoUtilSimple.decrypt(base.getApiKey()));
        base.setSecureToken(CrytoUtilSimple.decrypt(base.getSecureToken()));
    }

    private static boolean isAdmin() {
        User authUser = BasicInfoUtil.getAuthUser();
        if (Objects.isNull(authUser)){
            throw new BizException(WebUtil.getMessage(MsgCd.CLOUD_ENV_NOT_EXIST));
        }
        Set<String> role = basicUserMapper.selectRoleByuserId(authUser.getUserSid());
        boolean isAdmin = role.contains("301");
        return isAdmin;
    }

    private static void tenantInformation(Base base, Long userSid) {
        RestResult hcso = resourceServiceFeign.findHcsoUser(userSid);
        if (Objects.isNull(hcso.getData())){
            log.warn("用户[{}]Hcso不存在", userSid);
            return;
        }
        Map<String, Object> hcsoData = (Map<String, Object>) hcso.getData();
        base.setApiKey(hcsoData.get("ak")+"");
        base.setSecureToken(hcsoData.get("sk")+"");
        base.setProjectId(hcsoData.get("projectId")+"");
        base.setTenantId(hcsoData.get("accountId")+"");
    }

    private static void publicInformation(Base base) {
        RestResult cloudEnvId = resourceServiceFeign.getCloudEnvId();
        if (Objects.isNull(cloudEnvId.getData())){
            throw new BizException(WebUtil.getMessage(MsgCd.CLOUD_ENV_NOT_EXIST));
        }
        Map<String, Object> envData = (Map<String, Object>) cloudEnvId.getData();
        Base envBase = JsonUtil.fromJson(CrytoUtilSimple.decrypt(envData.get("attrData").toString()), Base.class);

        BeanUtil.transformBeanObj(envBase, base);
        String cloudEnvType = String.valueOf(envData.get("cloudEnvType"));
        base.setVirtEnvType(cloudEnvType.toLowerCase());
        base.setVirtEnvUuid(getMqEnvUuid());
        base.setProviderType(cloudEnvType);
    }

    private static String getMqEnvUuid() {
        // 取得环境变量
        Properties sysProps = System.getProperties();
        return sysProps.getProperty("cloudstar.mq.queue", "dev");
    }

}
