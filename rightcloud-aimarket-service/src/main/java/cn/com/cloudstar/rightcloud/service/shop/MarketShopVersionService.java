package cn.com.cloudstar.rightcloud.service.shop;

import cn.com.cloudstar.rightcloud.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.data.entity.MarketShopVersion;
import cn.com.cloudstar.rightcloud.data.request.market.MarketVersionBatchReq;
import cn.com.cloudstar.rightcloud.data.request.market.MarketVersionStatusReq;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 *
 */
public interface MarketShopVersionService extends IService<MarketShopVersion> {

    /**
     * 批量添加商品版本
     * @param req 数据
     * @return
     */
    RestResult batchAdd(MarketVersionBatchReq req);

    /**
     * 重试批量添加商品版本
     * @return 无
     */
    RestResult resetBatchAdd(String shopId);

    /**
     * 新增版本
     * @param req 版本信息
     * @return 无
     */
    RestResult add(MarketShopVersion req);

    /**
     * 根据商品id查询版本信息
     * @param shopId 商品id
     * @return 版本信息
     */
    List<MarketShopVersion> getByShopId(String shopId);



    /**
     * 修改版本信息
     * @param req 数据
     * @return 无
     */
    RestResult updateStatus(MarketVersionStatusReq req);

    /**
     * 根据资产id集合删除版本
     * @param delPriceAttrsList 集合
     */
    void removeByContentIds(List<String> delPriceAttrsList);
}
