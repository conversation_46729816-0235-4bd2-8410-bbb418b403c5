package cn.com.cloudstar.rightcloud.service.shop.impl;

import cn.com.cloudstar.rightcloud.adapter.core.MQException;
import cn.com.cloudstar.rightcloud.adapter.core.MQHelper;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopDetailsQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopSubscriptionsCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiShopDetailsQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiShopSubscribesCreatResult;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.enums.CommodityTransactionEnum;
import cn.com.cloudstar.rightcloud.common.enums.MarketShopSourceEnum;
import cn.com.cloudstar.rightcloud.common.enums.MarketShopVisibilityEnum;
import cn.com.cloudstar.rightcloud.common.enums.MarketSuperviseStatusEnum;
import cn.com.cloudstar.rightcloud.common.enums.ProductPopularityEnum;
import cn.com.cloudstar.rightcloud.common.enums.ProductStatisticsEnum;
import cn.com.cloudstar.rightcloud.common.exception.BizException;
import cn.com.cloudstar.rightcloud.common.pojo.CloudEnv;
import cn.com.cloudstar.rightcloud.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.common.util.CrytoUtilSimple;
import cn.com.cloudstar.rightcloud.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.data.dao.MarketSubscribeMapper;
import cn.com.cloudstar.rightcloud.data.entity.HcsoUser;
import cn.com.cloudstar.rightcloud.data.entity.MarketShop;
import cn.com.cloudstar.rightcloud.data.entity.MarketShopData;
import cn.com.cloudstar.rightcloud.data.entity.MarketShopPriceJoin;
import cn.com.cloudstar.rightcloud.data.entity.MarketShopSubscribe;
import cn.com.cloudstar.rightcloud.data.entity.MarketShopVersion;
import cn.com.cloudstar.rightcloud.data.request.cfn.AlgorithmOperateReq;
import cn.com.cloudstar.rightcloud.data.request.cfn.DataStorageOperateReq;
import cn.com.cloudstar.rightcloud.data.request.market.ApplyMarketRequest;
import cn.com.cloudstar.rightcloud.data.response.bigscreen.MarketBigScreenResp;
import cn.com.cloudstar.rightcloud.data.response.bigscreen.MarketSubscribeProviderTopResp;
import cn.com.cloudstar.rightcloud.data.response.bigscreen.MarketSubscribeRecentResp;
import cn.com.cloudstar.rightcloud.data.response.bigscreen.MarketSubscribeShopTopResp;
import cn.com.cloudstar.rightcloud.data.response.bigscreen.MarketSubscribeUserTopResp;
import cn.com.cloudstar.rightcloud.data.response.bigscreen.MarketSuperviseTrendResp;
import cn.com.cloudstar.rightcloud.data.response.bigscreen.ProductDeliveryStatisticsResp;
import cn.com.cloudstar.rightcloud.data.response.bigscreen.ProductTagStatisticsResp;
import cn.com.cloudstar.rightcloud.data.response.bigscreen.UserOrderRankingResp;
import cn.com.cloudstar.rightcloud.data.vo.market.MarketProductInfoVO;
import cn.com.cloudstar.rightcloud.data.vo.market.MarketSuperviseTrendVo;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.Period;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicInfoUtil;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicWebUtil;
import cn.com.cloudstar.rightcloud.module.support.access.util.DateUtil;
import cn.com.cloudstar.rightcloud.service.cfn.CfnService;
import cn.com.cloudstar.rightcloud.service.feign.BssServiceFeign;
import cn.com.cloudstar.rightcloud.service.feign.ResourceServiceFeign;
import cn.com.cloudstar.rightcloud.service.shop.MarketShopPriceJoinService;
import cn.com.cloudstar.rightcloud.service.shop.MarketShopService;
import cn.com.cloudstar.rightcloud.service.shop.MarketShopVersionService;
import cn.com.cloudstar.rightcloud.service.shop.MarketSubscribeService;
import cn.com.cloudstar.rightcloud.service.util.BaseMockUtil;
import cn.com.cloudstar.rightcloud.service.util.BaseUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 *
 */
@Service
@Slf4j
public class MarketSubscribeServiceImpl extends ServiceImpl<MarketSubscribeMapper, MarketShopSubscribe>
        implements MarketSubscribeService {

    @Resource
    private MarketSubscribeMapper marketSubscribeMapper;
    @Resource
    private BssServiceFeign orderServiceFeign;
    @Resource
    private MarketShopService shopService;
    @Resource
    private MarketShopPriceJoinService priceJoinService;

    @Resource
    private ResourceServiceFeign resourceServiceFeign;

    @Resource
    private CfnService cfnService;

    @Resource
    private MarketShopVersionService versionService;


    /**
     * 算法来源是bms
     */
    private final static String ALGORITHM_SOURCE_BMS = "BMS";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RestResult apply(ApplyMarketRequest request) {
        //订阅前判断订阅者不能订阅自己发布的商品
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        MarketProductInfoVO marketProductInfoVO = request.getProductInfo().get(0);
        MarketShop marketShop = shopService.getById(marketProductInfoVO.getShopId());
        if (authUser.getUserSid().equals(marketShop.getOwnerId())) {
            throw new BizException(WebUtil.getMessage(MsgCd.NOT_SUBSCRIBE_MYSELF_SHOP));
        }
        //aihub商品进行下单限制校验，一个商品只能购买异常，过期后才能再次购买
        if (Objects.nonNull(request.getPriceJoinId())) {
            //每个用户只能订阅一次aihub商品，结束后才能继续订阅,如果订阅了之后，再次订阅,需要限制不能再次下单
            QueryWrapper<MarketShopSubscribe> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(MarketShopSubscribe::getOwnerId, authUser.getUserSid());
            queryWrapper.lambda().eq(MarketShopSubscribe::getShopId, request.getProductInfo().get(0).getShopId());
            List<MarketShopSubscribe> marketShopSubscribes = marketSubscribeMapper.selectList(queryWrapper);
            if (CollectionUtil.isNotEmpty(marketShopSubscribes)) {
                Optional<Date> max = marketShopSubscribes.stream()
                        .map(MarketShopSubscribe::getEndTime)
                        .filter(Objects::nonNull)
                        .max(Date::compareTo);
                if (!max.isPresent() || max.get().after(new Date())) {
                    throw new BizException(WebUtil.getMessage(MsgCd.NOT_SUBSCRIBE_AGAIN));
                }
            }
        }

        /*
         * 订阅aiHub商品的话
         * 1.把订阅用户设置到被订阅商品的白名单内（不然无法订阅）
         * 2.订阅
         * 3.下单
         */

        List whiteUser = new ArrayList();
        try {
            if (Objects.nonNull(request.getPriceJoinId())) {
                MarketShopData marketShopData = new MarketShopData();
                MarketShopPriceJoin shopPriceJoin = priceJoinService.getById(request.getPriceJoinId());
                if (!marketShop.getAiHubSource().equals(ALGORITHM_SOURCE_BMS)) {
                    //订阅前先查询该商品的白名单用户记录下来
                    AiShopDetailsQuery aiShopDetailsQuery = new AiShopDetailsQuery();
                    aiShopDetailsQuery.setContent_id(shopPriceJoin.getContentId());
                    String flag = System.getenv("LARGE_MODEL_MOCK");
                    if ("true".equals(flag)) {
                        BaseMockUtil.extracted(aiShopDetailsQuery);
                    } else {
                        BaseUtil.extracted(aiShopDetailsQuery, marketShop.getOwnerId());
                    }
                    log.info("资产详细信息参数信息：{}", aiShopDetailsQuery);
                    AiShopDetailsQueryResult shopDetailsResult = BeanConvertUtil.convert(MQHelper.rpc(aiShopDetailsQuery),
                                                                                         AiShopDetailsQueryResult.class);
                    log.info("资产详细信息返回信息：{}", shopDetailsResult);
                    Assert.isTrue(shopDetailsResult.isSuccess(), shopDetailsResult.getErrMsg());
                    if (CollectionUtil.isNotEmpty(shopDetailsResult.getContent_detail().getWhitelist_users())) {

                        whiteUser.addAll(shopDetailsResult.getContent_detail()
                                                          .getWhitelist_users()
                                                          .stream()
                                                          .map(it -> JSONObject.parseObject(it).get("domain_id"))
                                                          .collect(Collectors.toList()));
                    }

                    marketShopData.setVisibility(MarketShopVisibilityEnum.GROUP.getType());
                    // 需要获取订阅人的userId hcso_user表的account_id(没引入dubbo,用的feign)
                    RestResult result = resourceServiceFeign.findHcsoUser(null);
                    Assert.notNull(result.getData(), "获取HCSO用户信息失败");
                    HcsoUser hcsoUser = BeanConvertUtil.convert(result.getData(), HcsoUser.class);
                    //将订阅人的账号加入白名单
                    List whiteGroup = new ArrayList();
                    whiteGroup.addAll(whiteUser);
                    whiteGroup.add(hcsoUser.getAccountId());
                    //
                    if (whiteGroup.size() < 2) {
                        if (marketShop.getShopSource().equals(MarketShopSourceEnum.PLATFORM.getType())) {
                            StringBuffer envStr = new StringBuffer();
                            RestResult envResult = resourceServiceFeign.getCloudEnvId();
                            CloudEnv cloudEnv = BeanConvertUtil.convert(envResult.getData(), CloudEnv.class);
                            String domainId = JSON.parseObject(CrytoUtilSimple.decrypt(cloudEnv.getAttrData())).getString("domainId");
                            if (!whiteUser.contains(domainId)) {
                                whiteGroup.add(domainId);
                                whiteUser.add(domainId);
                            }

                        } else {
                            StringBuffer hcsoStr = new StringBuffer();
                            RestResult hcsoResult = resourceServiceFeign.findHcsoUser(marketShop.getOwnerId());
                            HcsoUser user = BeanConvertUtil.convert(hcsoResult.getData(), HcsoUser.class);
                            if (!whiteUser.contains(user.getAccountId())) {
                                whiteGroup.add(user.getAccountId());
                                whiteUser.add(user.getAccountId());
                            }
                        }
                    }

                    marketShopData.setGroupUsers(whiteGroup);
                    marketShopData.setShopId(shopPriceJoin.getShopId());
                    marketShopData.setContentId(shopPriceJoin.getContentId());
                    marketShopData.setDescription(marketShop.getDescription());
                    marketShopData.setIntroduce(marketShop.getIntroduce());
                    shopService.updateShopData(marketShopData);
                } else {
                    final List<MarketShopVersion> versionList = versionService.list(
                            new LambdaQueryWrapper<MarketShopVersion>().eq(MarketShopVersion::getShopId, shopPriceJoin.getShopId()));
                    if (CollectionUtil.isNotEmpty(versionList)) {
                        versionList.forEach(version -> {
                            Boolean flag;
                            if (3 == marketShop.getShopType()) {
                                DataStorageOperateReq subReq = new DataStorageOperateReq();
                                subReq.setIsAdmin(false);
                                subReq.setAccount(authUser.getAccount());
                                subReq.setPublishId(version.getAlgorithm());
                                flag = cfnService.datasetSubscribe(subReq);
                            } else {
                                AlgorithmOperateReq algorithmOperateReq = new AlgorithmOperateReq();
                                algorithmOperateReq.setAccount(authUser.getAccount());
                                algorithmOperateReq.setIsAdmin(false);
                                algorithmOperateReq.setPublishId(version.getAlgorithm());
                                flag = cfnService.algorithmSubscribe(algorithmOperateReq);
                            }
                            if (ObjectUtil.isEmpty(flag) || ObjectUtil.equals(flag, false)) {
                                throw new BizException("订阅失败");
                            }
                        });
                    }
                }
            }
            RestResult restResult = orderServiceFeign.apply(request);
            if (restResult.getStatus()) {
                shopService.addSubscribeNum(marketProductInfoVO.getShopId());
            } else {
                rollBack(request, whiteUser, marketShop);
                return new RestResult(RestResult.Status.FAILURE, String.valueOf(restResult.getMessage()));
            }
        } catch (Exception e) {
            log.error("exception message:", e);
            rollBack(request, whiteUser, marketShop);
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERROR_OPERATE_FAILURE));
        }
        return RestResult.newSuccess();

    }

    private void rollBack(ApplyMarketRequest request, List whiteUser, MarketShop marketShop) {
        if (Objects.isNull(request.getPriceJoinId())) {
            return;
        }
        if (!marketShop.getAiHubSource().equals(ALGORITHM_SOURCE_BMS)) {
            MarketShopData marketShopData = new MarketShopData();
            //如果订阅失败，回滚将用户加入白名单操作
            marketShopData.setVisibility(MarketShopVisibilityEnum.GROUP.getType());
            marketShopData.setGroupUsers(whiteUser);
            MarketShopPriceJoin shopPriceJoin = priceJoinService.getById(request.getPriceJoinId());
            marketShopData.setShopId(shopPriceJoin.getShopId());
            marketShopData.setContentId(shopPriceJoin.getContentId());
            marketShopData.setDescription(marketShop.getDescription());
            marketShopData.setIntroduce(marketShop.getIntroduce());
            shopService.updateShopData(marketShopData);
        } else {
            log.info("裸金属算法退订");
            AuthUser authUser = RequestContextUtil.getAuthUserInfo();
            final List<MarketShopVersion> versionList = versionService.list(
                    new LambdaQueryWrapper<MarketShopVersion>().eq(MarketShopVersion::getShopId, marketShop.getShopId()));
            if (CollectionUtil.isNotEmpty(versionList)) {
                versionList.forEach(version -> {
                    Boolean flag;
                    if (3 == marketShop.getShopType()) {
                        DataStorageOperateReq subReq = new DataStorageOperateReq();
                        subReq.setIsAdmin(false);
                        subReq.setAccount(authUser.getAccount());
                        subReq.setPublishId(version.getAlgorithm());
                        flag = cfnService.datasetUnsubscribe(subReq);
                    } else {
                        AlgorithmOperateReq algorithmOperateReq = new AlgorithmOperateReq();
                        algorithmOperateReq.setAccount(authUser.getAccount());
                        algorithmOperateReq.setIsAdmin(false);
                        algorithmOperateReq.setPublishId(version.getAlgorithm());
                        flag = cfnService.algorithmUnsubscribe(algorithmOperateReq);
                    }
                    if (ObjectUtil.isEmpty(flag) || ObjectUtil.equals(flag, false)) {
                        throw new BizException("退订回滚失败");
                    }
                });
            }
        }
    }


    @Override
    public RestResult<MarketBigScreenResp> countSubscribeNum() {
        Long subscribeDealNum = 0L;
        BigDecimal subscribeDealAmount = BigDecimal.ZERO;
        MarketBigScreenResp marketBigScreenResp = new MarketBigScreenResp();
        QueryWrapper<MarketShopSubscribe> queryWrapper = new QueryWrapper();
        queryWrapper.lambda().ne(MarketShopSubscribe::getSuperviseStatus, "cancelled");
        List<MarketShopSubscribe> marketShopSubscribes = marketSubscribeMapper.selectList(queryWrapper);
        if (CollectionUtil.isNotEmpty(marketShopSubscribes)) {
            subscribeDealNum = Long.valueOf(marketShopSubscribes.size());
            subscribeDealAmount = marketShopSubscribes.stream()
                                                      .map(MarketShopSubscribe::getPrice)
                                                      .filter(Objects::nonNull)
                                                      .reduce(BigDecimal.ZERO, BigDecimal::add);
            subscribeDealAmount = subscribeDealAmount.divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP);

        }
        marketBigScreenResp.setSubscribeDealNum(subscribeDealNum);
        marketBigScreenResp.setSubscribeDealAmount(subscribeDealAmount);
        return new RestResult(marketBigScreenResp);

    }

    @Override
    public RestResult<List<MarketSubscribeRecentResp>> selectSubscribeRecent(Long top) {

        List<MarketSubscribeRecentResp> subscribeRecent = marketSubscribeMapper.selectSubscribeRecent(top);
        if (CollectionUtil.isNotEmpty(subscribeRecent)) {
            subscribeRecent.forEach(it -> it.setSubscribeDealAmount(it.getSubscribeDealAmount().setScale(2, BigDecimal.ROUND_HALF_UP)));
        }
        return new RestResult(subscribeRecent);
    }

    @Override
    public RestResult<List<MarketSubscribeShopTopResp>> selectSubscribeShopTop(String timeType, Long top, String contentType) {
        if (ProductPopularityEnum.TRANSACTION_COUNT.getType().equals(contentType)) {
            List<MarketSubscribeShopTopResp> subscribeShopTop = marketSubscribeMapper.selectSubscribeShopTop(timeType, top);
            return new RestResult(subscribeShopTop);
        }
        if (ProductPopularityEnum.VISIT_COUNT.getType().equals(contentType)) {

            List<MarketSubscribeShopTopResp> subscribeShopTop = marketSubscribeMapper.selectSubscribeShopVisitTop(timeType, top);
            return new RestResult(subscribeShopTop);
        }
        throw new BizException("参数错误");
    }

    @Override
    public RestResult<List<MarketSubscribeProviderTopResp>> selectSubscribeProviderTop(String timeType, Long top) {

        List<MarketSubscribeProviderTopResp> subscribeProviderTop = marketSubscribeMapper.selectSubscribeProviderTop(timeType, top);
        return new RestResult(subscribeProviderTop);
    }

    @Override
    public RestResult<List<MarketSubscribeUserTopResp>> selectSubscribeUserTop(String timeType, Long top) {

        List<MarketSubscribeUserTopResp> subscribeUserTop = marketSubscribeMapper.selectSubscribeUserTop(timeType, top);
        if (CollectionUtil.isNotEmpty(subscribeUserTop)) {
            subscribeUserTop.forEach(it -> it.setUserSubscribeAmount(it.getUserSubscribeAmount().setScale(2, BigDecimal.ROUND_HALF_UP)));
        }
        return new RestResult(subscribeUserTop);
    }

    @Override
    public RestResult<List<MarketSuperviseTrendResp>> selectSuperviseTrend(String timeType, String contentType) {
        List<Period> periods = new ArrayList<>();
        // 按每天每周每月查询
        boolean monthly = Period.PeriodType.Monthly.is(timeType);
        if (Period.PeriodType.Daily.is(timeType)) {
            periods = DateUtil.getPeriod(LocalDate.now(), Period.PeriodType.Daily, 7);
        } else if (Period.PeriodType.Weekly.is(timeType)) {
            periods = DateUtil.getPeriod(LocalDate.now(), Period.PeriodType.Daily, 30);
        } else if (monthly) {
            periods = DateUtil.getPeriod(LocalDate.now(), Period.PeriodType.Monthly, 12);
        }
        List<MarketSuperviseTrendVo> totalAmountTrendVo;
        List<MarketSuperviseTrendVo> completedAmountTrendVo;
        // 根据类型查询数据
        if (CommodityTransactionEnum.NUMBER.getType().equals(contentType)) {
            totalAmountTrendVo = Optional.ofNullable(marketSubscribeMapper.queryAllOrdersInModelMarket(timeType,
                    periods.get(0).getPeriodStart())).orElse(Collections.emptyList());
            completedAmountTrendVo = Optional.ofNullable(marketSubscribeMapper.queryNumberCompletedOrders(timeType,
                    periods.get(0).getPeriodStart())).orElse(Collections.emptyList());
        } else if (CommodityTransactionEnum.AMOUNT.getType().equals(contentType)) {
            totalAmountTrendVo = marketSubscribeMapper.selectTotalAmountTrend(timeType, periods.get(0).getPeriodStart());
            completedAmountTrendVo = marketSubscribeMapper.selectCompletedAmountTrend(timeType, periods.get(0).getPeriodStart());
        } else {
            throw new BizException("参数错误");
        }
        // 时间补偿
        if (monthly) {
            timeCompensation(totalAmountTrendVo);
            timeCompensation(completedAmountTrendVo);
        }

        List<MarketSuperviseTrendResp> marketSuperviseTrendResp = new ArrayList<>();


        for (Period period : periods){
            MarketSuperviseTrendResp superviseTrend = new MarketSuperviseTrendResp();
            superviseTrend.setPeriod(period.getPeriodName());
            if (CollectionUtil.isNotEmpty(totalAmountTrendVo)) {
                List<MarketSuperviseTrendVo> totalAmount =  totalAmountTrendVo.stream()
                                                                              .filter(it -> period.isInPeriod(it.getDateTime()))
                                                                              .collect(
                                                                                      Collectors.toList());

                if(CollectionUtil.isNotEmpty(totalAmount)){
                    BigDecimal reduce = totalAmount.stream()
                                                   .map(MarketSuperviseTrendVo::getTotalAmount)
                                                   .filter(Objects::nonNull)
                                                   .reduce(BigDecimal.ZERO, BigDecimal::add);
                    superviseTrend.setTotalAmount(reduce.setScale(2, BigDecimal.ROUND_HALF_UP));
                }else{
                    superviseTrend.setTotalAmount(BigDecimal.ZERO.setScale(2));
                }
            }else{
                superviseTrend.setTotalAmount(BigDecimal.ZERO.setScale(2));
            }

            if (CollectionUtil.isNotEmpty(completedAmountTrendVo)) {
                List<MarketSuperviseTrendVo> completedAmount =  completedAmountTrendVo.stream()
                                                                              .filter(it -> period.isInPeriod(it.getDateTime()))
                                                                              .collect(
                                                                                      Collectors.toList());

                if(CollectionUtil.isNotEmpty(completedAmount)){
                    BigDecimal reduce = completedAmount.stream()
                                                   .map(MarketSuperviseTrendVo::getCompletedAmount)
                                                   .filter(Objects::nonNull)
                                                   .reduce(BigDecimal.ZERO, BigDecimal::add);
                    superviseTrend.setCompletedAmount(reduce.setScale(2, BigDecimal.ROUND_HALF_UP));
                }else{
                    superviseTrend.setCompletedAmount(BigDecimal.ZERO.setScale(2));
                }
            }else{
                superviseTrend.setCompletedAmount(BigDecimal.ZERO.setScale(2));
            }
            marketSuperviseTrendResp.add(superviseTrend);
        }
        return new RestResult(marketSuperviseTrendResp);
    }

    /**
     * 时间补偿
     *
     * @param totalAmountTrendVo 总量趋势vo
     */
    private static void timeCompensation(List<MarketSuperviseTrendVo> totalAmountTrendVo) {
        if (CollectionUtil.isEmpty(totalAmountTrendVo)) {
            return;
        }
        for (MarketSuperviseTrendVo trendVo : totalAmountTrendVo) {
            trendVo.setDateTime(new Date(trendVo.getDateTime().getTime() + TimeUnit.DAYS.toMillis(1)));
        }
    }

    /**
     * 组装数据
     *
     * @param marketProductInfoVO 规格数据
     * @param marketShop 商品数据
     *
     * @return 数据
     *
     * @throws MQException
     */
    private MarketShopSubscribe getMarketShopSubscribe(MarketProductInfoVO marketProductInfoVO, MarketShop marketShop) throws MQException {
        Long priceJoinId = marketProductInfoVO.getPriceJoinId();
        MarketShopPriceJoin shopPriceJoin = priceJoinService.getById(priceJoinId);
        Assert.notNull(shopPriceJoin, "参数错误");
        AiShopSubscriptionsCreate subscriptionsCreate = new AiShopSubscriptionsCreate();
        subscriptionsCreate.setContent_id(shopPriceJoin.getContentId());
        String flag = System.getenv("LARGE_MODEL_MOCK");
        if ("true".equals(flag)) {
            BaseMockUtil.extracted(subscriptionsCreate);
        } else {
            BaseUtil.extracted(subscriptionsCreate);
        }
        log.info("订阅资产参数信息：{}", subscriptionsCreate);
        AiShopSubscribesCreatResult result = BeanConvertUtil.convert(MQHelper.rpc(subscriptionsCreate), AiShopSubscribesCreatResult.class);
        log.info("订阅资产返回信息：{}", result);
        Assert.isTrue(result.isSuccess(), result.getErrMsg());
        // 保存
        MarketShopSubscribe shopSubscribe = BeanUtil.toBean(shopPriceJoin, MarketShopSubscribe.class);
        shopSubscribe.setAiHubSubscribeId(result.getSubscription_id());
        shopSubscribe.setShopId(marketProductInfoVO.getShopId());
        shopSubscribe.setAiHubContentId(shopPriceJoin.getContentId());
        shopSubscribe.setSellType(marketShop.getSellType());
        shopSubscribe.setOwnerId(BasicInfoUtil.getAuthUser().getUserSid());
        shopSubscribe.setShopOwnerId(marketShop.getOwnerId());
        BasicWebUtil.prepareInsertParams(shopSubscribe);
        shopSubscribe.setStartTime(new Date());

        Date date = new Date();
        Calendar endTime = Calendar.getInstance();
        endTime.setTime(date);
        switch (shopPriceJoin.getUnit()) {
            case "年":
                endTime.add(Calendar.YEAR, shopPriceJoin.getUnitValue());
                break;
            case "月":
                endTime.add(Calendar.MONTH, shopPriceJoin.getUnitValue());
                break;
            default:
                break;
        }
        // 需要计算
        shopSubscribe.setEndTime(endTime.getTime());
        // TODO 需要计算
        shopSubscribe.setOrderId(null);
        shopSubscribe.setSuperviseStatus(MarketSuperviseStatusEnum.COMPLETED.getStatus());
        return shopSubscribe;
    }
 @Override
    public List<UserOrderRankingResp> getUserOrderRanking(String timeType, Long top) {

        return Optional.ofNullable(marketSubscribeMapper.selectSubscribeUserAmountTop(timeType, top)).orElse(new ArrayList<>());
    }


    @Override
    public List<ProductDeliveryStatisticsResp> getProductDeliveryStatistics(String contentType) {
        ProductStatisticsEnum type = Optional.ofNullable(ProductStatisticsEnum.getType(contentType)).orElseThrow(() -> new BizException("参数错误"));
        List<ProductDeliveryStatisticsResp> resps = new ArrayList<>();
        switch (type) {
            case PRODUCT_COUNT:
                resps = marketSubscribeMapper.selectProductCount();
                break;
            case TRANSACTION_AMOUNT:
                resps = marketSubscribeMapper.selectTransactionAmount();
                break;
            case SUBSCRIPTION_COUNT:
                resps = marketSubscribeMapper.selectSubscriptionCount();
                break;
        }
        for (ProductDeliveryStatisticsResp resp : resps) {
            if (Objects.nonNull(resp.getTypeName())) {
                resp.setTypeName(Optional.ofNullable(MarketShopSourceEnum.getType(resp.getTypeName())).orElse(MarketShopSourceEnum.PLATFORM).getDesc());
            }
        }
        return resps;
    }

    @Override
    public List<ProductTagStatisticsResp> getProductTagStatistics(String contentType) {
         ProductStatisticsEnum type = Optional.ofNullable(ProductStatisticsEnum.getType(contentType)).orElseThrow(() -> new BizException("参数错误"));
        List<ProductTagStatisticsResp> resps = new ArrayList<>();
        switch (type) {
            case PRODUCT_COUNT:
                resps = marketSubscribeMapper.selectTagProductCount();
                break;
            case TRANSACTION_AMOUNT:
                resps = marketSubscribeMapper.selectTagTransactionAmount();
                break;
            case SUBSCRIPTION_COUNT:
                resps = marketSubscribeMapper.selectTagSubscriptionCount();
                break;
        }
        return resps;
    }

}




