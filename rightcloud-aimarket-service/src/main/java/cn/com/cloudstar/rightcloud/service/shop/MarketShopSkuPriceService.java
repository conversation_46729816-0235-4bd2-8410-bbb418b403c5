package cn.com.cloudstar.rightcloud.service.shop;


import cn.com.cloudstar.rightcloud.data.entity.MarketShopSkuPrice;
import cn.com.cloudstar.rightcloud.data.request.market.MarketInquiryPriceBase;
import cn.com.cloudstar.rightcloud.data.response.market.MarketInquiryPriceResp;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【market_shop_sku_price(商品属性规格表)】的数据库操作Service
* @createDate 2023-08-01 16:23:05
*/
public interface MarketShopSkuPriceService extends IService<MarketShopSkuPrice> {

    MarketInquiryPriceResp price(MarketInquiryPriceBase req);
}
