package cn.com.cloudstar.rightcloud.service.shop;

import com.baomidou.mybatisplus.core.metadata.IPage;

import cn.com.cloudstar.rightcloud.data.request.market.MarketAuditListReq;
import cn.com.cloudstar.rightcloud.data.request.market.MarketAuditProcessReq;
import cn.com.cloudstar.rightcloud.data.request.market.MarketProcessDetailsReq;
import cn.com.cloudstar.rightcloud.data.response.market.MarketAuditDetailsResp;
import cn.com.cloudstar.rightcloud.data.response.market.MarketAuditListResp;
import cn.com.cloudstar.rightcloud.data.response.market.MarketProcessDetailsResp;

/**
 * <AUTHOR>
 */
public interface MarketAuditService {

    IPage<MarketAuditListResp> listAudit(MarketAuditListReq req);

    /**
     * 开始购物过程
     *
     * @param shopId 商店id
     *
     * @return {@link String}
     */
    String startShopProcess(String shopId);

    MarketAuditDetailsResp getAuditDetails(String orderNo);


    MarketProcessDetailsResp getProcessDetails(MarketProcessDetailsReq req);

    String getXml(String resourceId);

    void auditProcess(MarketAuditProcessReq req);
}
