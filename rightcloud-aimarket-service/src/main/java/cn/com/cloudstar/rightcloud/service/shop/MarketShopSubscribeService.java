package cn.com.cloudstar.rightcloud.service.shop;

import cn.com.cloudstar.rightcloud.data.entity.MarketShopSubscribe;
import cn.com.cloudstar.rightcloud.data.request.cfn.CreatePrivateModelReq;
import cn.com.cloudstar.rightcloud.data.request.market.SubscriptionManageReq;
import cn.com.cloudstar.rightcloud.data.response.cfn.ClusterInfoResp;
import cn.com.cloudstar.rightcloud.data.response.market.MarketSubDetailResp;
import cn.com.cloudstar.rightcloud.data.response.market.MarketSubPageResp;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @description 针对表【market_shop_subscribe(商品订阅表)】的数据库操作Service
 * @createDate 2023-08-16 10:51:30
 */
public interface MarketShopSubscribeService extends IService<MarketShopSubscribe> {

    IPage<MarketSubPageResp> managePage(SubscriptionManageReq req);

    IPage<MarketSubPageResp> purchaserPage(SubscriptionManageReq req);

    MarketSubDetailResp selectSubscribeById(String subscribeId, Integer from);

    /**
     * 分页查询我的订阅-算力平台用 -查询我的订阅算法
     *
     * @param req
     */
    IPage<MarketSubPageResp> getMySubscribeCfnPage(SubscriptionManageReq req);

    List<ClusterInfoResp> getPrivateModelCluster();

    Boolean createPrivateModel(CreatePrivateModelReq req);
}
