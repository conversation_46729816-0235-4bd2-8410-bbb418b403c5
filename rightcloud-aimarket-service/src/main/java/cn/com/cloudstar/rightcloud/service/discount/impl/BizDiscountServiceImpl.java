package cn.com.cloudstar.rightcloud.service.discount.impl;

import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.dto.User;
import cn.com.cloudstar.rightcloud.common.enums.ScopeTypeEnum;
import cn.com.cloudstar.rightcloud.common.exception.BizException;
import cn.com.cloudstar.rightcloud.common.util.AuthUtil;
import cn.com.cloudstar.rightcloud.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.common.util.PageUtil;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.common.util.WrapperUtil;
import cn.com.cloudstar.rightcloud.data.entity.BizDiscount;
import cn.com.cloudstar.rightcloud.data.request.discount.DescribeDiscountRequest;
import cn.com.cloudstar.rightcloud.data.response.discount.DescribeDiscountDetailResponse;
import cn.com.cloudstar.rightcloud.data.response.discount.DescribeDiscountResponse;
import cn.com.cloudstar.rightcloud.data.vo.discount.DiscountCheckVo;
import cn.com.cloudstar.rightcloud.service.discount.BizDiscountService;
import cn.com.cloudstar.rightcloud.data.dao.BizDiscountMapper;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 针对表【biz_discount(折扣表)】的数据库操作Service实现
 * @createDate 2023-08-11 15:13:52
 */
@Service
public class BizDiscountServiceImpl extends ServiceImpl<BizDiscountMapper, BizDiscount>
        implements BizDiscountService {

    private static final String MARKET = "market";

    @Override
    public void check(DiscountCheckVo discountCheckVo) {
        User authUser = AuthUtil.getAuthUser();
        BizDiscount discount = this.getById(discountCheckVo.getDiscountSid());
        if (!MARKET.equals(discount.getDiscountType())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        if (!discount.getCreatedBy().equals(authUser.getAccount())) {
            throw new BizException(WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
    }

    @Override
    public IPage<DescribeDiscountResponse> findPlatformDiscounts(DescribeDiscountRequest request) {
        if (("all").equals(request.getProductScopeLike())) {
            request.setProductScopeLike(null);
        }
        User authUser = AuthUtil.getAuthUser();
        IPage<BizDiscount> page = PageUtil.preparePageParams(request, "created_dt", "desc");
        QueryWrapper<BizDiscount> queryWrapper = WrapperUtil.wrapQuery(request);
        queryWrapper.lambda()
                .eq(BizDiscount::getDiscountType, MARKET)
                .eq(BizDiscount::getCreatedBy,authUser.getAccount());
        if (StrUtil.isNotBlank(request.getDiscountNameLike())) {
            queryWrapper.lambda().like(BizDiscount::getDiscountName, request.getDiscountNameLike());
        }

        IPage<BizDiscount> result = this.page(page, queryWrapper);
        result.getRecords().forEach(this::convertToDesc);
        return BeanConvertUtil.convertPage(result, DescribeDiscountResponse.class);
    }

    @Override
    public DescribeDiscountDetailResponse findPDiscountDetail(Long id) {
        BizDiscount bizDiscount = this.getById(id);
        if (bizDiscount == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        bizDiscount.setProductScopes(Arrays.asList(StrUtil.splitToArray(bizDiscount.getProductScope(), StrUtil.COMMA)));
        bizDiscount.setCloudEnvScopes(Arrays.asList("HCSO"));
        return BeanConvertUtil.convert(bizDiscount, DescribeDiscountDetailResponse.class);
    }


    /**
     * 转换描述
     *
     * @param bizDiscount
     */
    private BizDiscount convertToDesc(BizDiscount bizDiscount) {
        bizDiscount.setScopeType(ScopeTypeEnum.codeFromName(bizDiscount.getScopeType()));
        bizDiscount.setProductScopes(Arrays.asList("模型集市"));
        bizDiscount.setCloudEnvScopes(Arrays.asList("HCSO"));
        return bizDiscount;
    }
}
