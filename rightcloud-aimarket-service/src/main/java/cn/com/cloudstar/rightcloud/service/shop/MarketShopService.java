package cn.com.cloudstar.rightcloud.service.shop;

import cn.com.cloudstar.rightcloud.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.data.entity.MarketShop;
import cn.com.cloudstar.rightcloud.data.entity.MarketShopData;
import cn.com.cloudstar.rightcloud.data.request.market.*;
import cn.com.cloudstar.rightcloud.data.response.market.*;
import cn.com.cloudstar.rightcloud.data.response.market.AiAlgorithmsQueryResponse;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 商品
 */
public interface MarketShopService extends IService<MarketShop> {

    /**
     * 创建
     * @param bean 数据
     * @return 无
     */
    RestResult addShop(MarketShop bean);


    /**
     * 修改商品
     * @param req 修改数据
     * @return
     */
    RestResult updateShop(MarketShop req);

    /**
     * 修改单个商品属性
     * @param marketShopData 数据
     * @return 是否成功
     */
     void updateShopData(MarketShopData marketShopData);

    /**
     * 修改商品状态
     * @param req
     */
    RestResult updateShopStatus(ShopUpdateStatusReq req);

    /**
     * 收藏数加1
     * @param shopId 商品id
     */
    void addCollectNum(String shopId);

    /**
     * 订阅数加1
     * @param shopId 商品id
     */
    void addSubscribeNum(String shopId);

    /**
     * 取消收藏
     * @param shopId 商品id
     */
    void delCollectNum(String shopId);


    /**
     * 查询门户客户商品
     * @param shopReq 商品req
     * @return 商品数据
     */
    RestResult<IPage<MarketShopPagePortalResp>> selectPortalShops(MarketShopPortalRequest shopReq);

    /**
     * 查询门户商品按排行显示
     * @param shopReq 商品req
     * @return 商品数据
     */
    RestResult<List<MarketShopRankPortalResp>> selectPortalShopsByRank(MarketShopRankPortalRequest shopReq);

    /**
     * 查询门户商品上下篇
     * @param request 商品req
     * @return 商品数据
     */
    RestResult<MarketShopDetailPortalResp> selectAroundDetailsById(MarketShopAroundPortalRequest request);

    /**
     * 增加浏览量
     * @param shopId 商品id
     * @return 商品数据
     */
    void addBrowseNum(@Param("shopId") String shopId);

    /**
     * 查询商品详情
     * @param shopId 商品id
     * @return 商品数据
     */
    RestResult<MarketShopDetailResp> selectShopDetailsById(String shopId);

    /**
     * 管理员查询客户是否有模型供应商权限
     * @param userSid 用户id
     * @return 是否授权
     */
    RestResult<Boolean> selectPermissionByUserSid(Long userSid);

    /**
     * 管理员授予客户模型供应商权限
     * @param userSid 用户id
     * @return 授权
     */
    RestResult authorize(Long userSid);

    /**
     * 查询商品
     * @param shopReq 商品req
     * @return 商品数据
     */
    IPage<MarketShopMgtResp> selectAllShops(MarketShopMgtRequest shopReq);

    /**
     * 修改商品状态  TODO 删除
     * @param req 商品req
     * @return 商品数据
     */
    RestResult updateMgtShopStatus(UpdateShopStatusRequest req);

    /**
     * 调整商品关联的标签
     * @param req 商品req
     * @return 商品数据
     */
    RestResult updateShopLabel(UpdateShopLabelRequest req);

    /**
     * 管理员取消授予客户模型供应商权限
     * @param userSid 用户id
     * @return 取消授权
     */
    RestResult authorizeCancel(Long userSid);

    /**
     * 调整商品的排序(上移)
     * @param shopId 商品id
     * @return 状态
     */
    RestResult moveUp(String shopId);

    /**
     * 调整商品的排序(下移)
     * @param shopId 商品id
     * @return 状态
     */
    RestResult moveDown(String shopId);

    /**
     * 调整商品的排序(置顶)
     * @param shopId 商品id
     * @return 状态
     */
    RestResult moveTop(String shopId);

    /**
     * 调整商品的排序(置底)
     * @param shopId 商品id
     * @return 状态
     */
    RestResult moveBottom(String shopId);

    /**
     * 查询客户已结算和未结算金额（汇总信息）
     * @param userSid 用户id
     * @return 汇总信息
     */
    RestResult<MarketShopPriceDetailResp> selectShopInformation(Long userSid);

    /**
     * 查询上架商品个数（大屏）
     * @return 上架商品个数
     */
    int countShopOnlineNum();

    /**
     * 查询算法列表
     * @param request 参数
     * @return 算法列表
     */
    Page<AiAlgorithmsQueryResponse> selectAlgorithmsList(AlgorithmsRequest request);

    /**
     * 根据许可证那边发布的类型来确定该节点可以创建哪些商品类型
     * @return 数据
     */
    RestResult getShopType();
}
