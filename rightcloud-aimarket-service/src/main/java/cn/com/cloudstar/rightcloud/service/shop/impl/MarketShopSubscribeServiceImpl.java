package cn.com.cloudstar.rightcloud.service.shop.impl;

import cn.com.cloudstar.rightcloud.basic.data.dao.org.BasicOrgMapper;
import cn.com.cloudstar.rightcloud.basic.data.pojo.user.Org;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.dto.SysMFilePath;
import cn.com.cloudstar.rightcloud.common.dto.User;
import cn.com.cloudstar.rightcloud.common.encryptdata.encrypt.DesensitizationUtil;
import cn.com.cloudstar.rightcloud.common.enums.MarketSuperviseStatusEnum;
import cn.com.cloudstar.rightcloud.common.enums.ShopTypeEnum;
import cn.com.cloudstar.rightcloud.common.enums.SysMFileTypeEnum;
import cn.com.cloudstar.rightcloud.common.exception.BizException;
import cn.com.cloudstar.rightcloud.common.util.AuthUtil;
import cn.com.cloudstar.rightcloud.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.common.util.PageUtil;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.data.dao.MarketShopMapper;
import cn.com.cloudstar.rightcloud.data.dao.MarketShopSubscribeMapper;
import cn.com.cloudstar.rightcloud.data.dao.MarketShopSuperviseRecordMapper;
import cn.com.cloudstar.rightcloud.data.dao.MarketShopTagRelevanceMapper;
import cn.com.cloudstar.rightcloud.data.dao.ServiceOrderMapper;
import cn.com.cloudstar.rightcloud.data.dao.SysMFilePathMapper;
import cn.com.cloudstar.rightcloud.data.dao.SysMUserMapper;
import cn.com.cloudstar.rightcloud.data.entity.MarketShop;
import cn.com.cloudstar.rightcloud.data.entity.MarketShopSubscribe;
import cn.com.cloudstar.rightcloud.data.entity.MarketShopSuperviseRecord;
import cn.com.cloudstar.rightcloud.data.entity.MarketShopVersion;
import cn.com.cloudstar.rightcloud.data.entity.ServiceOrder;
import cn.com.cloudstar.rightcloud.data.entity.SysMUser;
import cn.com.cloudstar.rightcloud.data.request.cfn.CreatePrivateModelReq;
import cn.com.cloudstar.rightcloud.data.request.market.SubscriptionManageReq;
import cn.com.cloudstar.rightcloud.data.response.cfn.ClusterInfoResp;
import cn.com.cloudstar.rightcloud.data.response.market.MarketSubDetailResp;
import cn.com.cloudstar.rightcloud.data.response.market.MarketSubPageResp;
import cn.com.cloudstar.rightcloud.data.vo.market.CustomInfoVo;
import cn.com.cloudstar.rightcloud.data.vo.market.MarketSubDetailVo;
import cn.com.cloudstar.rightcloud.data.vo.market.MarketSubPageVo;
import cn.com.cloudstar.rightcloud.data.vo.market.MarketSubVo;
import cn.com.cloudstar.rightcloud.data.vo.market.ShopInfoVo;
import cn.com.cloudstar.rightcloud.data.vo.market.SuperviseInfoVo;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria;
import cn.com.cloudstar.rightcloud.service.cfn.CfnService;
import cn.com.cloudstar.rightcloud.service.shop.MarketShopSubscribeService;
import cn.com.cloudstar.rightcloud.service.shop.MarketShopVersionService;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【market_shop_subscribe(商品订阅表)】的数据库操作Service实现
 * @createDate 2023-08-16 10:51:30
 */
@Service
public class MarketShopSubscribeServiceImpl extends ServiceImpl<MarketShopSubscribeMapper, MarketShopSubscribe>
        implements MarketShopSubscribeService {

    @Resource
    private MarketShopTagRelevanceMapper shopTagRelevanceMapper;

    @Resource
    private MarketShopMapper marketShopMapper;

    @Resource
    private MarketShopSuperviseRecordMapper shopSuperviseRecordMapper;

    @Resource
    private SysMUserMapper userMapper;

    @Resource
    private ServiceOrderMapper serviceOrderMapper;

    @Resource
    private SysMFilePathMapper sysMFilePathMapper;

    @Resource
    private BasicOrgMapper orgMapper;

    @Resource
    private MarketShopVersionService marketShopVersionService;

    @Resource
    private CfnService cfnService;


    @Override
    public IPage<MarketSubPageResp> managePage(SubscriptionManageReq req) {
        Page<MarketSubPageVo> page = PageUtil.preparePageParams(req, "sub.created_dt", "desc");
        setOrder(page);
        Criteria criteria = new Criteria();
        User authUser = AuthUtil.getAuthUser();
        if (Objects.nonNull(authUser.getOrgSid())) {
            criteria.put("shopOwnerId", authUser.getUserSid());
        }
        criteria.put("superviseStatus", req.getSuperviseStatus());
        criteria.put("shopTitle", req.getShopTitle());
        criteria.put("unit", req.getUnit());
        criteria.put("shopType", req.getShopType());
        criteria.put("custOrgName", req.getCustOrgName());
        criteria.put("settlementStatus", req.getSettlementStatus());
        if (StrUtil.isNotBlank(req.getOrgName()) && "平台自营".equals(req.getOrgName())) {
            criteria.put("orgNameIsNull", 1);
        } else {
            criteria.put("orgName", req.getOrgName());
        }
        IPage<MarketSubPageVo> iPage = this.baseMapper.selectPSubscripage(page, criteria);
        iPage.getRecords().forEach(vo -> {
            vo.setOrgName(Objects.isNull(vo.getOrgName()) ? "平台自营" : vo.getOrgName());
            vo.setCategoryName(ShopTypeEnum.transformDesc(Integer.parseInt(vo.getShopType())));
            vo.setFail(!vo.getOwnerId().equals(authUser.getUserSid()));
        });
        return BeanConvertUtil.convertPage(iPage, MarketSubPageResp.class);
    }

    @Override
    public IPage<MarketSubPageResp> purchaserPage(SubscriptionManageReq req) {
        Page<MarketSubPageVo> page = PageUtil.preparePageParams(req, "sub.created_dt", "desc");
        setOrder(page);
        Criteria criteria = new Criteria();
        User authUser = AuthUtil.getAuthUser();
        criteria.put("subOwnerId", authUser.getUserSid());
        criteria.put("superviseStatus", req.getSuperviseStatus());
        criteria.put("shopTitle", req.getShopTitle());
        criteria.put("unit", req.getUnit());
        criteria.put("shopType", req.getShopType());
        criteria.put("custOrgName", req.getCustOrgName());
        criteria.put("settlementStatus", req.getSettlementStatus());
        if (StrUtil.isNotBlank(req.getSettlementStatus()) && "平台自营".equals(req.getSettlementStatus())) {
            criteria.put("orgNameIsNull", 1);
        } else {
            criteria.put("orgName", req.getOrgName());
        }
        IPage<MarketSubPageVo> iPage = this.baseMapper.selectPSubscripage(page, criteria);
        iPage.getRecords().forEach(vo -> {
            String shopId = vo.getShopId();
            vo.setOrgName(Objects.isNull(vo.getOrgName()) ? "平台自营" : vo.getOrgName());
            vo.setCategoryName(ShopTypeEnum.transformDesc(Integer.parseInt(vo.getShopType())));
        });
        return BeanConvertUtil.convertPage(iPage, MarketSubPageResp.class);
    }

    /**
     * 选择订阅通过id
     *
     * @param subscribeId 订阅id
     * @param from 查询来源：0租户-买方：1租户-卖方；2：管理员
     *
     * @return {@link MarketSubDetailResp}
     */
    @Override
    public MarketSubDetailResp selectSubscribeById(String subscribeId, Integer from) {
        MarketSubDetailResp resp = new MarketSubDetailResp();
        MarketSubVo marketSubVo = new MarketSubVo();
        ShopInfoVo shopInfoVo = new ShopInfoVo();
        CustomInfoVo customInfoVo = new CustomInfoVo();
        List<SuperviseInfoVo> superviseInfoVos = new ArrayList<>();
        resp.setMarketSubVo(marketSubVo);
        resp.setShopInfoVo(shopInfoVo);
        resp.setCustomInfoVo(customInfoVo);
        resp.setSuperviseInfoVos(superviseInfoVos);

        MarketSubDetailVo marketSubDetailVo = this.baseMapper.selectDetailById(subscribeId);
        if (Objects.isNull(marketSubDetailVo)) {
            throw new BizException(StrUtil.format(WebUtil.getMessage(MsgCd.ERROR_RES_NOT_FOUND), "订阅"));
        }
        User authUser = AuthUtil.getAuthUser();
        Long userSid = null;
        if (from == 0) {
            userSid = marketSubDetailVo.getOwnerId();
        } else if (from == 1) {
            userSid = marketSubDetailVo.getShopOwnerId();
        } else if (from == 2) {
            userSid = authUser.getUserSid();
        }
        if (!authUser.getUserSid().equals(userSid)) {
            throw new BizException(WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }

        ServiceOrder serviceOrder = serviceOrderMapper.selectByPrimaryKey(marketSubDetailVo.getOrderId());
        String extraAttr = serviceOrder.getExtraAttr();
        JSONArray objects = JSONUtil.parseArray(extraAttr);
        JSONObject entries = (JSONObject) objects.get(0);
        Integer amount = (Integer) entries.get("amount");

        marketSubVo.setOrderId(String.valueOf(marketSubDetailVo.getOrderId()));
        marketSubVo.setSubscribeId(marketSubDetailVo.getSubscribeId());
        marketSubVo.setOrderPrice(serviceOrder.getOriginalCost().setScale(2));
        marketSubVo.setPayPrice(marketSubDetailVo.getPrice());
        marketSubVo.setOrgName(StrUtil.isBlank(marketSubDetailVo.getOrgName()) ? "平台自营" : marketSubDetailVo.getOrgName());
        marketSubVo.setPayOrgName(marketSubDetailVo.getCustOrgName());
        marketSubVo.setSuperviseStatus(marketSubDetailVo.getSuperviseStatus());
        marketSubVo.setSettlementStatus(marketSubDetailVo.getSettlementStatus());
        marketSubVo.setCreateDt(marketSubDetailVo.getCreatedDt());

        shopInfoVo.setTitle(marketSubDetailVo.getShopTitle());
        shopInfoVo.setShopId(marketSubDetailVo.getShopId());
        shopInfoVo.setSkuName(marketSubDetailVo.getUnit());
        shopInfoVo.setAmount(amount);
        List<String> tags = shopTagRelevanceMapper.selectTagNameByShopId(marketSubDetailVo.getShopId());
        shopInfoVo.setTags(tags);
        shopInfoVo.setShopType(marketSubDetailVo.getShopType());
        MarketShop marketShop = marketShopMapper.selectById(marketSubDetailVo.getShopId());
        shopInfoVo.setLogoPath(marketShop.getLogoPath());
        QueryWrapper<MarketShopSuperviseRecord> recordWrapper = new QueryWrapper<>();
        recordWrapper.lambda().eq(MarketShopSuperviseRecord::getSubscribeId, marketSubDetailVo.getSubscribeId());
        List<MarketShopSuperviseRecord> superviseRecords = shopSuperviseRecordMapper.selectList(recordWrapper);
        superviseRecords.forEach(record -> {
            SuperviseInfoVo superviseInfoVo = new SuperviseInfoVo();
            superviseInfoVo.setOperationalState(MarketSuperviseStatusEnum.transformDeliverDesc(record.getUpdateStatus()));
            superviseInfoVo.setTime(record.getCreatedDt());
            superviseInfoVo.setRemark(record.getRemark());
            superviseInfoVo.setOperateRole(MarketSuperviseStatusEnum.transformDeliverRole(record.getUpdateStatus()));
            Criteria criteria = new Criteria();
            criteria.put("owner", record.getCreatedUserId());
            List<Org> orgs = orgMapper.selectByParams(criteria);
            if (CollectionUtil.isNotEmpty(orgs)) {
                superviseInfoVo.setOperateUser(orgs.get(0).getOrgName());
            } else {
                superviseInfoVo.setOperateUser("平台管理员");
            }

            // 查询交付附件
            QueryWrapper<SysMFilePath> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda()
                        .eq(SysMFilePath::getOperationType, SysMFileTypeEnum.SUPERVISE_RECORD.getCode())
                        .eq(SysMFilePath::getOperationId, record.getId())
                        .orderByAsc(SysMFilePath::getSortOrder);
            List<SysMFilePath> sysMFilePaths = sysMFilePathMapper.selectList(queryWrapper);
            if (CollectionUtil.isNotEmpty(sysMFilePaths)) {
                superviseInfoVo.setSuperviseFiles(sysMFilePaths);
            }
            superviseInfoVos.add(superviseInfoVo);
        });

        customInfoVo = userMapper.selectCustById(marketSubDetailVo.getOwnerId());
        customInfoVo.setPayOrgName(marketSubDetailVo.getCustOrgName());
        resp.setCustomInfoVo(customInfoVo);
        DesensitizationUtil.desensitization(customInfoVo);
        return resp;
    }

    @Override
    public IPage<MarketSubPageResp> getMySubscribeCfnPage(SubscriptionManageReq req) {
        Page<MarketSubPageVo> page = PageUtil.preparePageParams(req, "sub.created_dt", "desc");
        setOrder(page);
        Criteria criteria = new Criteria();
        if (ObjectUtil.isEmpty(req.getCustOrgName())) {
            throw new BizException("账号名称未传递");
        }
        final SysMUser sysMUser = userMapper.selectOne(new LambdaQueryWrapper<SysMUser>().eq(SysMUser::getAccount, req.getCustOrgName()));
        if (ObjectUtil.isEmpty(sysMUser)) {
            throw new BizException("账号不存在");
        }
        criteria.put("shopTitle", req.getShopTitle());
        criteria.put("subOwnerId", sysMUser.getUserSid());
        criteria.put("shopType", Objects.nonNull(req.getShopType()) && 3 == req.getShopType() ? 3 : 0);
        criteria.put("settlementStatus", "completed");
        criteria.put("algorithmSource", "BMS");
        IPage<MarketSubPageVo> iPage = this.baseMapper.getMySubscribeCfnPage(page, criteria);
        iPage.getRecords().forEach(vo -> {
            String shopId = vo.getShopId();
            vo.setOrgName(Objects.isNull(vo.getOrgName()) ? "平台自营" : vo.getOrgName());
            vo.setCategoryName(ShopTypeEnum.transformDesc(Integer.parseInt(vo.getShopType())));
        });
        final List<String> shopIds = iPage.getRecords().stream().map(MarketSubPageVo::getShopId).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(shopIds)) {
            return BeanConvertUtil.convertPage(iPage, MarketSubPageResp.class);
        }
        final List<MarketShopVersion> versionList = marketShopVersionService.list(
                new LambdaQueryWrapper<MarketShopVersion>().in(MarketShopVersion::getShopId, shopIds));
        final Map<String, List<MarketShopVersion>> versionMap = versionList.stream().collect(Collectors.groupingBy(MarketShopVersion::getShopId));
        IPage<MarketSubPageResp> marketSubPageRespIPage = BeanConvertUtil.convertPage(iPage, MarketSubPageResp.class);
        marketSubPageRespIPage.getRecords().forEach(marketSubPageResp -> {
            marketSubPageResp.setShopVersions(versionMap.get(marketSubPageResp.getShopId()));
        });
        return marketSubPageRespIPage;
    }

    @Override
    public List<ClusterInfoResp> getPrivateModelCluster() {
        User authUser = AuthUtil.getAuthUser();
        if (ObjectUtil.isEmpty(authUser)) {
            throw new BizException("用户未登录");
        }
        return cfnService.getPrivateModelCluster(authUser.getAccount());
    }

    @Override
    public Boolean createPrivateModel(CreatePrivateModelReq req) {
        User authUser = AuthUtil.getAuthUser();
        if (ObjectUtil.isEmpty(authUser)) {
            throw new BizException("用户未登录");
        }
        req.setAccount(authUser.getAccount());
        return cfnService.createPrivateModel(req);
    }


    /**
     * 设置顺序
     *
     * @param page 页面
     */
    private void setOrder(Page page) {
        List<OrderItem> orders = page.getOrders();
        if (CollectionUtil.isEmpty(orders)) {
            return;
        }
        for (OrderItem orderItem : orders) {
            String column = orderItem.getColumn();
            boolean asc = orderItem.isAsc();
            switch (column) {
                case "price":
                    orderItem.setColumn("sub.price");
                    break;
                case "CREATED_DT":
                    orderItem.setColumn("sub.CREATED_DT");
                    break;
                default:
                    break;
            }
        }
    }

}
