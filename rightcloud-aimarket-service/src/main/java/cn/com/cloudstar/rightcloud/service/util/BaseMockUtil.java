package cn.com.cloudstar.rightcloud.service.util;

import cn.com.cloudstar.rightcloud.basic.data.pojo.base.Base;

/**
 * <AUTHOR>
 * @date 2023/9/20
 * 临时使用
 */
public class BaseMockUtil {

    public static void extracted(Base base) {
        base.setApiKey(System.getenv("MOCK_API_KEY"));
        base.setSecureToken(System.getenv("MOCK_SECURE_TOKEN"));
        base.setProjectId(System.getenv("MOCK_PROJECT_ID"));
        base.setRegion(System.getenv("MOCK_REGION"));
        base.setProviderUrl(System.getenv("MOCK_PROVIDER_URL"));
        base.setVirtEnvType(System.getenv("MOCK_VIRT_ENV_TYPE"));
        base.setVirtEnvUuid(System.getenv("MOCK_VIRT_ENV_UUID"));
        base.setProviderType(System.getenv("MOCK_PROVIDER_TYPE"));
    }

}
