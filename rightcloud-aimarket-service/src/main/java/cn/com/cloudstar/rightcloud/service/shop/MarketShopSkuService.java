package cn.com.cloudstar.rightcloud.service.shop;


import cn.com.cloudstar.rightcloud.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.data.request.market.MarketShopSkuAddReq;
import cn.com.cloudstar.rightcloud.data.request.market.MarketShopSkuNextReq;
import cn.com.cloudstar.rightcloud.data.request.market.MarketShopSkuUpdReq;
import cn.com.cloudstar.rightcloud.data.request.market.MarketSkuPageReq;
import cn.com.cloudstar.rightcloud.data.entity.MarketShopSku;
import cn.com.cloudstar.rightcloud.data.response.market.ShopSkuResp;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【market_shop_sku(商品属性表)】的数据库操作Service
* @createDate 2023-08-01 16:21:49
*/
public interface MarketShopSkuService extends IService<MarketShopSku> {

    void addSku(MarketShopSkuAddReq req);

    RestResult delSku(Long skuId);

    Page<ShopSkuResp> selectPage(MarketSkuPageReq req);

    RestResult next(List<MarketShopSkuNextReq> req);

    void upd(MarketShopSkuUpdReq req);
}
