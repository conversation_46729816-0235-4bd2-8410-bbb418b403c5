/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.module.ticket.service.impl;

import cn.com.cloudstar.rightcloud.oss.common.enums.TicketConstant;
import cn.com.cloudstar.rightcloud.oss.common.util.*;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Maps;
import org.apache.commons.io.FileUtils;
import org.camunda.bpm.engine.TaskService;
import org.camunda.bpm.engine.task.Task;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TreeMap;
import java.util.stream.Collectors;

import cn.com.cloudstar.rightcloud.common.constants.NotificationConsts;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.mq.request.MailNotificationMq;
import cn.com.cloudstar.rightcloud.common.util.CrytoUtilSimple;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.core.pojo.dto.file.SysMFilePath;
import cn.com.cloudstar.rightcloud.core.pojo.dto.process.Process;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.Attachments;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.Code;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.UserBssEntity;
import cn.com.cloudstar.rightcloud.core.pojo.dto.ticket.*;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.User;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.module.support.file.storage.bean.enums.MimeTypeEnum;
import cn.com.cloudstar.rightcloud.module.support.file.storage.bean.enums.StoragePathEnum;
import cn.com.cloudstar.rightcloud.module.support.file.storage.bean.vo.StorageResult;
import cn.com.cloudstar.rightcloud.module.support.file.storage.service.StorageService;
import cn.com.cloudstar.rightcloud.module.support.file.storage.utils.FileVerifyUtil;
import cn.com.cloudstar.rightcloud.module.support.file.storage.utils.StorageUtil;
import cn.com.cloudstar.rightcloud.oss.common.constants.Constants;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.oss.common.constants.status.CommunicateRecordMessageType;
import cn.com.cloudstar.rightcloud.oss.common.constants.status.TicketAppraiseStatusType;
import cn.com.cloudstar.rightcloud.oss.common.constants.status.WorkTicketDealStatus;
import cn.com.cloudstar.rightcloud.oss.common.constants.status.WorkTicketStatus;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.WorkTicketFeedBackType;
import cn.com.cloudstar.rightcloud.oss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.oss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.oss.common.util.*;
import cn.com.cloudstar.rightcloud.oss.common.util.FileUtil.FileSizeEnum;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.oss.module.account.service.role.RoleService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.user.SysBssEntityService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.user.UserService;
import cn.com.cloudstar.rightcloud.oss.module.export.bean.BizDownload;
import cn.com.cloudstar.rightcloud.oss.module.export.dao.BizDownloadMapper;
import cn.com.cloudstar.rightcloud.oss.module.export.enums.ExportTypeEnum;
import cn.com.cloudstar.rightcloud.oss.module.export.service.ExportService;
import cn.com.cloudstar.rightcloud.oss.module.export.util.ExportThreadUtil;
import cn.com.cloudstar.rightcloud.oss.module.file.dao.SysMFilePathMapper;
import cn.com.cloudstar.rightcloud.oss.module.operationcore.activiti.util.ProcessConstants;
import cn.com.cloudstar.rightcloud.oss.module.operationcore.dao.header.AttachmentMapper;
import cn.com.cloudstar.rightcloud.oss.module.operationcore.dao.process.ProcessMapper;
import cn.com.cloudstar.rightcloud.oss.module.operationcore.service.process.ProcessService;
import cn.com.cloudstar.rightcloud.oss.module.order.service.SidService;
import cn.com.cloudstar.rightcloud.oss.module.others.dao.ticket.AttachmentRelationMapper;
import cn.com.cloudstar.rightcloud.oss.module.others.dao.ticket.WorkTicketMapper;
import cn.com.cloudstar.rightcloud.oss.module.pricing.service.priceconfig.BizBillingAccountService;
import cn.com.cloudstar.rightcloud.oss.module.system.service.message.BusinessNotificationService;
import cn.com.cloudstar.rightcloud.oss.module.system.service.message.ISysOssMessageService;
import cn.com.cloudstar.rightcloud.oss.module.ticket.dao.TicketCommunicateRecordMapper;
import cn.com.cloudstar.rightcloud.oss.module.ticket.request.CommunicateRecordRequest;
import cn.com.cloudstar.rightcloud.oss.module.ticket.request.DescribeTicketRequest;
import cn.com.cloudstar.rightcloud.oss.module.ticket.service.TicketCategoryService;
import cn.com.cloudstar.rightcloud.oss.module.ticket.service.TicketTemplateService;
import cn.com.cloudstar.rightcloud.oss.module.ticket.service.WorkTicketService;
import cn.com.cloudstar.rightcloud.oss.module.ticket.vo.CommunicateRecordVo;
import cn.com.cloudstar.rightcloud.oss.util.PropertiesUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Maps;
import org.apache.commons.io.FileUtils;
import org.camunda.bpm.engine.TaskService;
import org.camunda.bpm.engine.task.Task;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static cn.com.cloudstar.rightcloud.common.constants.NotificationConsts.SEND_NOTIFICATION_EXCHANGE;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class WorkTicketServiceImpl implements WorkTicketService {

    private static final Logger logger = LoggerFactory.getLogger(WorkTicketServiceImpl.class);

    @Autowired
    private SysBssEntityService sysBssEntityService;

    @Autowired
    private WorkTicketMapper workTicketMapper;

    @Autowired
    private SidService sidService;

    @Autowired
    private AttachmentRelationMapper attachmentRelationMapper;

    @Autowired
    private AttachmentMapper attachmentMapper;

    @Autowired
    private UserService userService;

    @Autowired
    private RoleService roleService;

    @Autowired
    private ProcessService processService;

    @Autowired
    private ProcessMapper processMapper;

    @Autowired
    private TaskService taskService;

    @Autowired
    private SysMFilePathMapper sysMFilePathMapper;

    @Autowired
    private TicketTemplateService ticketTemplateService;

    @Autowired
    private TicketCommunicateRecordMapper ticketCommunicateRecordMapper;

    @Autowired
    private BusinessNotificationService businessNotificationService;

    @Autowired
    private ISysOssMessageService sysOssMessageService;

    @Autowired
    private StorageService storageService;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private TicketCategoryService ticketCategoryService;

    @Autowired
    private BizBillingAccountService bizBillingAccountService;

    @Autowired
    private BizDownloadMapper bizDownloadMapper;

    @Autowired
    @Lazy
    private ExportService exportService;

    @Override
    public int countByParams(Criteria example) {
        int count = this.workTicketMapper.countByParams(example);
        logger.debug("count: {}", count);
        return count;
    }

    @Override
    public WorkTicket selectByPrimaryKey(Long id) {
        return this.workTicketMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<WorkTicket> selectByParams(Criteria example) {
        return this.workTicketMapper.selectByParams(example);
    }

    @Override
    public List<WorkTicket> selectByParamsAllEntity(Criteria example) {
        return this.workTicketMapper.selectByParamsAllEntity(example);
    }


    @Override
    public int deleteByPrimaryKey(Long id) {
        return this.workTicketMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(WorkTicket record) {
        return this.workTicketMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(WorkTicket record) {
        return this.workTicketMapper.updateByPrimaryKey(record);
    }

    @Override
    public int deleteByParams(Criteria example) {
        return this.workTicketMapper.deleteByParams(example);
    }

    @Override
    public int updateByParamsSelective(WorkTicket record, Criteria example) {
        return this.workTicketMapper.updateByParamsSelective(record, example.getCondition());
    }

    @Override
    public int updateByParams(WorkTicket record, Criteria example) {
        return this.workTicketMapper.updateByParams(record, example.getCondition());
    }

    @Override
    public int insert(WorkTicket record) {
        return this.workTicketMapper.insert(record);
    }

    @Override
    public int insertSelective(WorkTicket record) {
        return this.workTicketMapper.insertSelective(record);
    }

    /**
     * 文件格式
     */
    private final List<String> contentTypes = Arrays.asList(MimeTypeEnum.JPEG.getContentType(), MimeTypeEnum.JPG.getContentType(),
                                                                    MimeTypeEnum.PNG.getContentType(), MimeTypeEnum.XLS.getContentType(), MimeTypeEnum.XLSX.getContentType(),
                                                                    MimeTypeEnum.DOC.getContentType(), MimeTypeEnum.DOCX.getContentType(), MimeTypeEnum.TXT.getContentType());

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createTicket(WorkTicket workTicket, String source) {
        //获取订单编号
        String ticketNo = sidService.getMaxSid("ORDER_ID");
        workTicket.setTicketNo(ticketNo);
        //设置工单状态为未分配
        workTicket.setStatus(WorkTicketStatus.UN_ASSIGNED);
        workTicket.setDealStatus(WorkTicketDealStatus.BASE);
        //初始的工单 默认未评价
        workTicket.setAppraiseStatus(TicketAppraiseStatusType.NOEVALUATION);
        workTicketMapper.insertSelective(workTicket);
        List<User> users = userService.findAdminstrators();
        User ticketUser = userService.findUserById(workTicket.getTicketUserId());
        String username = getUsername(TicketConstant.ANONYMITY.equals(source), ticketUser, workTicket);
        Map<String, String> content = Maps.newHashMap();
        content.put("tickedId", workTicket.getTicketNo());
        content.put("url", "#/appmain/bss/workOrders/processing/" + workTicket.getId());
        content.put("ticketTitle", workTicket.getTicketTitle());
        content.put("submitUser", username);
        String createdTime = getFormatDate(workTicket.getCreatedDt());
        content.put("submitTime", createdTime);

        List<Long> userIds = new ArrayList<>();
        for (User user : users) {
            userIds.add(user.getUserSid());
        }
        //通知对应运营实体的管理员
        if (userIds.size() > 0) {
            List<UserBssEntity> userBssEntities = sysBssEntityService.sysBssEntityByCurrentUsers(userIds);
            Map<Long, List<UserBssEntity>> collectMap = userBssEntities.stream()
                    .sorted(Comparator.comparing(iteam -> iteam.getUserSid()))
                    .collect(Collectors.groupingBy(iteam -> iteam.getBssEntityId(), TreeMap::new, Collectors.toList()));
            for (Map.Entry entry : collectMap.entrySet()) {
                Long mapKey = (Long) entry.getKey();
                //仅仅只发有权限的实体管理员
                if (!mapKey.equals(workTicket.getEntityId())) {
                    continue;
                }
                if (mapKey.equals(workTicket.getEntityId())) {
                    List<UserBssEntity> mapValue = (List<UserBssEntity>) entry.getValue();
                    List<Long> userIdsForMsg = mapValue.stream().map(u -> u.getUserSid()).collect(Collectors.toList());
                    sysOssMessageService.sendOssMessage(workTicket.getTicketUserId(),content,null,NotificationConsts.PlatformMsg.AccountMsg.BSSMGT_TICKET_UNASSIGNED,workTicket.getEntityId());
                }
                if (TicketConstant.ANONYMITY.equals(source)) {
                    sysOssMessageService.sendAnonymousWorkOrderMessage(content,
                                                                       NotificationConsts.PlatformMsg.AccountMsg.BSSMGT_TICKET_UNASSIGNED,
                                                                       workTicket.getEntityId());
                }
            }
        }

        AuthUser account = RequestContextUtil.getAuthUserInfo();
        if (Objects.isNull(account) && TicketConstant.ANONYMITY.equals(source)) {
            account = new AuthUser();
            account.setAccount(getTemplateInfoNodeValue(workTicket, TicketConstant.ACCOUNT));
            account.setRealName(getTemplateInfoNodeValue(workTicket, TicketConstant.ACCOUNT));
            account.setUserSid(TicketConstant.ANONYMITY_ID);
            account.setOrgSid(TicketConstant.ANONYMITY_ID);
        }


        // 存储交流记录
        TicketCommunicateRecord ticketCommunicateRecord = new TicketCommunicateRecord();
        ticketCommunicateRecord.setTicketNo(ticketNo);
        ticketCommunicateRecord.setTicketContent(workTicket.getTicketContent());
        ticketCommunicateRecord.setMessageType(CommunicateRecordMessageType.OUTSIDE);
        ticketCommunicateRecord.setType(WorkTicketFeedBackType.USER);
        if (account == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.WARNING_QUERY_FAILURE));
        }
        WebUserUtil.prepareInsertParams(ticketCommunicateRecord, account.getAccount());
        ticketCommunicateRecord.setCreatedByRealName(CrytoUtilSimple.encrypt(account.getRealName(), true));
        ticketCommunicateRecordMapper.insertCommunicateRecord(ticketCommunicateRecord);

        // 存储工单附件
        List<SysMFilePath> ticketFiles = workTicket.getTicketFiles();
        if (!CollectionUtils.isEmpty(ticketFiles)) {
            for (SysMFilePath file : ticketFiles) {
                //校验文件路径
                FileVerifyUtil.verifyName(file.getFileName(), StoragePathEnum.TICKET);
                storageService.validateFilePath(file.getFilePath(), StoragePathEnum.TICKET, file.getFileName());
                file.setFileNum(NoUtil.getNanoTimeId("DT"));
                file.setAccountId(account.getUserSid());
                if (!ObjectUtils.isEmpty(file.getFilePath())) {
                    file.setFilePath(file.getFilePath());
                    if (!CrytoUtilSimple.isEncryptedData(file.getFilePath())) {
                        file.setFilePath(CrytoUtilSimple.encrypt(file.getFilePath()));
                    }
                }
                file.setOperationType("01");
                file.setOperationId(ticketCommunicateRecord.getId().toString());
                WebUserUtil.prepareInsertParams(file, account.getAccount());
                sysMFilePathMapper.insertSysMFilePath(file);
            }
        }

        // 当存在工单流程且有审批节点时，走工单审批流程
        if (TicketConstant.PROPRIETARY.equals(source)) {
            String ticketAuditStatus = PropertiesUtil.getProperty(
                    ProcessConstants.TICKET_AUDIT_STATUS);
            if (ProcessConstants.AUDIT_OPEN.equals(ticketAuditStatus)
                    && processService.hasBusinessAuditNode(ProcessConstants.TICKET)) {
                AuthUser authUser = RequestContextUtil.getAuthUserInfo();
                Long compayId = roleService.getCompayIdByOrgSid(authUser.getOrgSid());
                Process businessProcess = processMapper.selectByBusinessCode(ProcessConstants.TICKET, compayId);

                // 工单流程
                Map<String, Object> variables = Maps.newHashMap();
                variables.put("_business_code", ProcessConstants.TICKET);
                variables.put("_service_order_id", workTicket.getId().toString());
                variables.put("_service_order_content", workTicket.getTicketContent());
                variables.put("_service_order_created_time", DateUtil.formatDateTime(workTicket.getCreatedDt()));
                variables.put("_apply_uname", authUser.toNameInfo());
                variables.put("_apply_usersid", authUser.getUserSid().toString());
                // 1.流程定义ID  2.业务ID  3.业务流程变量
                processService.startProcess(businessProcess.getProcessCode(), workTicket.getTicketNo(), variables);
            }
        }

        return true;
    }

    @Override
    public String getTemplateInfoNodeValue(WorkTicket workTicket, String key) {
        String result = "";
        if (Objects.isNull(workTicket)) {
            return result;
        }
        JsonNode jsonNode = JsonUtil.fromJson(workTicket.getTemplateInfo());
        for (int i = 0; jsonNode != null && i < jsonNode.size(); i++) {
            JsonNode node = jsonNode.get(i);
            JsonNode value = node.get("value");
            if (Objects.isNull(value)) {
                continue;
            }
            String name = node.get("name").toString();
            name = name.substring(1, name.length() - 1);
            if (key.equals(name)) {
                result = value.toString();
                result = result.substring(1, result.length() - 1);
                break;
            }
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean distributionTicket(Long ticketId, Long userSid, String msg, String distributionType,
                                      List<SysMFilePath> ticketFiles) {
        User user = userService.selectByPrimaryKey(userSid);
        if (ticketId != null && user != null) {
            WorkTicket workTicket = this.workTicketMapper.selectByPrimaryKey(ticketId);
            if (Objects.isNull(workTicket)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1385551884));
            }
            if (!WorkTicketStatus.UN_ASSIGNED.equals(workTicket.getStatus()) && !"委派".equals(distributionType)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1339980509));
            }

            // 当存在工单流程且有审批节点时，走工单审批流程
            String ticketAuditStatus = PropertiesUtil.getProperty(
                    ProcessConstants.TICKET_AUDIT_STATUS);
            AuthUser authUser = RequestContextUtil.getAuthUserInfo();
            if (ProcessConstants.AUDIT_OPEN.equals(ticketAuditStatus)
                    && processService.hasBusinessAuditNode(ProcessConstants.TICKET)) {
                Map<String, Object> variables = Maps.newHashMap();
                variables.put("_audit_uname", authUser.toNameInfo());
                variables.put("_ticket_id", ticketId.toString());
                variables.put("_ticket_usersid", userSid.toString());
                variables.put("_audit_account", authUser.getAccount());
                //修复问题 47648
                String approveDesc = String.format("工单由[%s]" + distributionType + "给[%s]处理:",
                                                   authUser.toNameInfo(),
                                                   user.toNameInfo());
                if (msg != null) {
                    approveDesc = approveDesc + approveDesc;
                }
                if (msg != null) {
                    approveDesc = approveDesc + approveDesc;
                }
                variables.put(ProcessConstants.AUDIT_COMMENT, approveDesc);

                Task task = taskService.createTaskQuery()
                                       .processInstanceBusinessKey(authUser.getUserSid().toString())
                                       .singleResult();
                if (Objects.nonNull(task)) {
                    //1.任务处理人ID  2.业务ID  3.业务流程变量
                    processService.taskPass(authUser.getUserSid().toString(), workTicket.getTicketNo(), variables);
                    return true;
                }
            }

            WebUserUtil.prepareUpdateParams(workTicket);
            workTicket.setStatus(WorkTicketStatus.PROCESSING);
            workTicket.setDealStatus(WorkTicketDealStatus.DEAL);
            workTicket.setAllocationTicketUserId(user.getUserSid());
            workTicket.setAllocationTicketUser(user.getAccount());
            workTicketMapper.updateByPrimaryKeySelective(workTicket);

            User ticketUser = userService.findUserById(workTicket.getTicketUserId());
            boolean isAnonymous = isAnonymous(ticketUser, workTicket);
            String username = getUsername(isAnonymous, ticketUser, workTicket);
            Map<String, String> content = Maps.newHashMap();
            content.put("tickedId", workTicket.getTicketNo());
            content.put("url", "#/appmain/bss/workOrders/processing/" + workTicket.getId());
            content.put("ticketTitle", workTicket.getTicketTitle());
            content.put("submitUser", username);
            String createdTime = getFormatDate(workTicket.getCreatedDt());
            content.put("submitTime", createdTime);
            //运营管理员分配的工单 通知处理人
            businessNotificationService.sendCommonPlatformNotification(workTicket.getAllocationTicketUserId(),
                                                                       NotificationConsts.PlatformMsg.AccountMsg.BSSMGT_TICKET_PADDING,
                                                                       content, workTicket.getEntityId());
            //如果是被拒绝的单子，内部指派不用显示外部消息
            if ((workTicket.getRejectedNumber() != null) && (workTicket.getRejectedNumber().intValue() == 1)) {
                // 管理员分配时，默认增加工单交流记录
                TicketCommunicateRecord ticketCommunicateRecord = new TicketCommunicateRecord();
                ticketCommunicateRecord.setTicketNo(workTicket.getTicketNo());
                ticketCommunicateRecord.setType(WorkTicketFeedBackType.SALES);
                ticketCommunicateRecord.setMessageType(CommunicateRecordMessageType.INNER);
                WebUserUtil.prepareInsertParams(ticketCommunicateRecord, user.getAccount());
                String InnerMsg = String.format("工单由[%s]" + distributionType + "给[%s]处理:",
                                                authUser.toNameInfo(),
                                                user.toNameInfo());
                if (msg != null) {
                    InnerMsg = InnerMsg + msg;
                }
                ticketCommunicateRecord.setTicketContent(InnerMsg);
//
                ticketCommunicateRecord.setCreatedByRealName(CrytoUtilSimple.encrypt(user.getRealName(), true));
                ticketCommunicateRecordMapper.insertCommunicateRecord(ticketCommunicateRecord);

                if (!CollectionUtils.isEmpty(ticketFiles)) {
                    for (SysMFilePath file : ticketFiles) {
                        file.setFileNum(NoUtil.generateNo("DT"));
                        file.setAccountId(authUser.getUserSid());
                        file.setOperationType("01");
                        file.setFilePath(StringUtil.isEmpty(CrytoUtilSimple.decrypt(file.getFilePath())) ? null
                                                 : CrytoUtilSimple.encrypt(CrytoUtilSimple.decrypt(file.getFilePath())));
                        file.setOperationId(ticketCommunicateRecord.getId().toString());
                        WebUserUtil.prepareInsertParams(file, authUser.getAccount());
                        sysMFilePathMapper.insertSysMFilePath(file);
                    }
                }

            } else {
                // 管理员分配时，默认增加工单交流记录 租户消息
                TicketCommunicateRecord ticketCommunicateOuter = new TicketCommunicateRecord();
                ticketCommunicateOuter.setTicketNo(workTicket.getTicketNo());
                ticketCommunicateOuter.setTicketContent("您好，我们已经收到您提交的问题，正在为您查看，请稍等");
                ticketCommunicateOuter.setType(WorkTicketFeedBackType.SALES);
                ticketCommunicateOuter.setMessageType(CommunicateRecordMessageType.OUTSIDE);
                WebUserUtil.prepareInsertParams(ticketCommunicateOuter, user.getAccount());
                ticketCommunicateOuter.setCreatedByRealName(CrytoUtilSimple.encrypt(user.getRealName(), true));
                ticketCommunicateRecordMapper.insertCommunicateRecord(ticketCommunicateOuter);
                //内部消息
                TicketCommunicateRecord ticketCommunicateInner = new TicketCommunicateRecord();
                ticketCommunicateInner.setTicketNo(workTicket.getTicketNo());
                String inMsg = String.format("工单由[%s]" + distributionType + "给[%s]处理",
                                             authUser.toNameInfo(),
                                             user.toNameInfo());
                if (msg != null) {
                    inMsg = inMsg + msg;
                }
                ticketCommunicateInner.setTicketContent(inMsg);
                ticketCommunicateInner.setType(WorkTicketFeedBackType.SALES);
                ticketCommunicateInner.setMessageType(CommunicateRecordMessageType.INNER);
                WebUserUtil.prepareInsertParams(ticketCommunicateInner, user.getAccount());
                ticketCommunicateInner.setCreatedByRealName(CrytoUtilSimple.encrypt(user.getRealName(), true));
                ticketCommunicateRecordMapper.insertCommunicateRecord(ticketCommunicateInner);
                if (!CollectionUtils.isEmpty(ticketFiles)) {
                    for (SysMFilePath file : ticketFiles) {
                        file.setFileNum(NoUtil.generateNo("DT"));
                        file.setAccountId(authUser.getUserSid());
                        file.setOperationType("01");
                        file.setFilePath(StringUtil.isEmpty(CrytoUtilSimple.decrypt(file.getFilePath())) ? null
                                                 : CrytoUtilSimple.encrypt(CrytoUtilSimple.decrypt(file.getFilePath())));
                        file.setOperationId(ticketCommunicateInner.getId().toString());
                        WebUserUtil.prepareInsertParams(file, authUser.getAccount());
                        sysMFilePathMapper.insertSysMFilePath(file);
                    }
                }
            }
            return true;
        }
        return false;
    }

    @Override
    public void processDistributionTicket(Long ticketId, Long userSid) {
        User user = userService.selectByPrimaryKey(userSid);
        if (ticketId != null && user != null) {
            WorkTicket workTicket = this.workTicketMapper.selectByPrimaryKey(ticketId);
            if (Objects.isNull(workTicket)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1385551884));
            }
            WebUserUtil.prepareUpdateParams(workTicket);
            workTicket.setStatus(WorkTicketStatus.PROCESSING);
            workTicket.setDealStatus(WorkTicketDealStatus.DEAL);
            workTicket.setAllocationTicketUser(user.getAccount());
            Record record = new Record();
            record.setTime(new Date());
            record.setUsername(user.getRealName());
            record.setContent("您好，我们已经收到您提交的问题，正在为您查看，请稍等");
            record.setType(WorkTicketFeedBackType.SALES);
            List<Record> recordList = new ArrayList<>();
            recordList.add(record);
            workTicket.setCommunicateRecord(JSON.toJSONString(recordList));
            this.workTicketMapper.updateByPrimaryKeySelective(workTicket);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteTicket(Long ticketId) {
        // 限制 只允许已关闭的工单可删除
        Optional.ofNullable(this.workTicketMapper.selectByPrimaryKey(ticketId)).ifPresent(t -> {
            if (WorkTicketStatus.PROCESSING.equalsIgnoreCase(t.getStatus())) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1856956711));
            }
        });
        this.workTicketMapper.deleteByPrimaryKeyLogic(ticketId);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean closeTicket(Long ticketId) {
        userService.checkUserTicket(ticketId);
        this.workTicketMapper.closeByPrimaryKeyLogic(ticketId);

        WorkTicket workTicket = workTicketMapper.selectByPrimaryKey(ticketId);

        User ticketUser = userService.findUserById(workTicket.getTicketUserId());
        boolean isAnonymous = isAnonymous(ticketUser, workTicket);

        String username = getUsername(isAnonymous, ticketUser, workTicket);
        Map<String, String> content = Maps.newHashMap();

        content.put("tickedId", workTicket.getTicketNo());
        content.put("url", "#/appmain/workorder/details/" + workTicket.getId());
        content.put("ticketTitle", workTicket.getTicketTitle());
        content.put("submitUser", username);
        String createdTime = getFormatDate(workTicket.getCreatedDt());
        content.put("submitTime", createdTime);

        // 如果父用户不为空，提交人是子用户，运营管理员关闭工单后，通知子用户和租户工单已关闭
        AuthUser account = RequestContextUtil.getAuthUserInfo();
        User user = userService.selectByPrimaryKey(workTicket.getTicketUserId());
        if (isAnonymous) {
            content.put("url", "#/appmain/bss/workOrders/processing/" + workTicket.getId());
            sysOssMessageService.sendAnonymousWorkOrderMessage(content, NotificationConsts.PlatformMsg.AccountMsg.BSSMGT_TICKET_CLOSED, workTicket.getEntityId());
        } else if (account.getUserSid().equals(workTicket.getTicketUserId())) {
            //如果关闭的人是提单人，通知管理员
            content.put("url", "#/appmain/bss/workOrders/processing/" + workTicket.getId());
            List<Code> codes = sysBssEntityService.sysBssEntityByCurrentUser(workTicket.getAllocationTicketUserId());
            if (CollectionUtil.isNotEmpty(codes)) {
                codes.stream().forEach(c -> {
                    //仅仅只发有权限的实体管理员
                    if (c.getCodeSid().equals(workTicket.getEntityId())) {
                          sysOssMessageService.sendOssMessage(workTicket.getTicketUserId(),content,NotificationConsts.ConsoleMsg.AccountMsg.TENANT_TICKET_CLOSED,NotificationConsts.PlatformMsg.AccountMsg.BSSMGT_TICKET_CLOSED,workTicket.getEntityId());
                    }
                });
            }
        } else if (account.getUserSid().equals(user.getParentSid()) && user.getParentSid() != null) {
            //如果关闭的人是租户，通知管理员和子用户
            List<Code> codes = sysBssEntityService.sysBssEntityByCurrentUser(workTicket.getAllocationTicketUserId());
            if (CollectionUtil.isNotEmpty(codes)) {
                content.put("url", "#/appmain/bss/workOrders/processing/" + workTicket.getId());
                codes.stream().forEach(c -> {
                    //仅仅只发有权限的实体管理员
                    if (c.getCodeSid().equals(workTicket.getEntityId())) {
                        sysOssMessageService.sendOssMessage(user.getParentSid(),content,null,NotificationConsts.PlatformMsg.AccountMsg.BSSMGT_TICKET_CLOSED,workTicket.getEntityId());
                    }
                });
            }
            content.put("url", "#/appmain/workorder/details/" + workTicket.getId());
            sysOssMessageService.sendOssMessage(user.getParentSid(),content,NotificationConsts.ConsoleMsg.AccountMsg.TENANT_TICKET_CLOSED,null,workTicket.getEntityId());

        } else {
            //如果关闭的是管理员，通知租户和提单人
            content.put("url", "#/appmain/workorder/details/" + workTicket.getId());
            if (user.getParentSid() != null) {
                // 子账户，添加租户
                User parentUser = userService.selectByPrimaryKey(user.getParentSid());
                sysOssMessageService.sendOssMessage(user.getUserSid(),content,NotificationConsts.ConsoleMsg.AccountMsg.TENANT_TICKET_CLOSED,null,workTicket.getEntityId());
            }else{
                sysOssMessageService.sendOssMessage(user.getUserSid(),content,NotificationConsts.ConsoleMsg.AccountMsg.TENANT_TICKET_CLOSED,null,workTicket.getEntityId());
            }
        }

        //关闭后默认十分好评
        workTicket.setProductScore(Long.valueOf(10));
        workTicket.setServiceScore(Long.valueOf(10));
        //关闭工单后，更新关闭时间
        workTicket.setCloseTime(new Date());
        workTicketMapper.updateByPrimaryKey(workTicket);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean completeTicket(Long ticketId) {
        WorkTicket workTicket = workTicketMapper.selectByPrimaryKey(ticketId);
        if (Objects.isNull(workTicket)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        if ("03".equals(workTicket.getStatus())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_849752285));
        }
        if (!WorkTicketStatus.PROCESSING.equals(workTicket.getStatus())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_581845916));
        }
        this.workTicketMapper.completeByPrimaryKeyLogic(ticketId);
        Map<String, String> content = Maps.newHashMap();
        content.put("tickedId", workTicket.getTicketNo());
        content.put("url", "#/appmain/bss/workOrders/processing/" + workTicket.getId());
        content.put("ticketTitle", workTicket.getTicketTitle());
        String createdTime = getFormatDate(workTicket.getCreatedDt());
        content.put("submitTime", createdTime);
        //区分匿名工单通知
        if (isAnonymous(workTicket)) {
            content.put("submitUser", getTemplateInfoNodeValue(workTicket, TicketConstant.ACCOUNT));
            sysOssMessageService.sendAnonymousWorkOrderMessage(content, NotificationConsts.PlatformMsg.AccountMsg.BSSMGT_TICKET_COMPLETED,
                                                               workTicket.getEntityId());
        } else {
            User ticketUser = userService.findUserById(workTicket.getTicketUserId());
            String username = ticketUser.getAccount() + "(" + ticketUser.getRealName() + ")";
            content.put("submitUser", username);
            sysOssMessageService.sendOssMessage(workTicket.getTicketUserId(), content, null,
                                                NotificationConsts.PlatformMsg.AccountMsg.BSSMGT_TICKET_COMPLETED,
                                                workTicket.getEntityId());
            content.put("url", "#/appmain/workorder/details/" + workTicket.getId());
            sysOssMessageService.sendOssMessage(workTicket.getTicketUserId(), content,
                                                NotificationConsts.ConsoleMsg.AccountMsg.TENANT_TICKET_COMPLETE, null,
                                                workTicket.getEntityId());
        }
        return true;
    }

    @Override
    public RestResult downLoadTicketInfo(DescribeTicketRequest request, HttpServletRequest httpServletRequest) {
        logger.info("工单数据下载开始！");
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        String moduleType = httpServletRequest.getHeader("moduleType");
        String period = httpServletRequest.getParameter("exportType");
        if (Constants.MONTH.equals(period)) {
            period = httpServletRequest.getParameter("specifiMonth");
        }
        BizDownload download = new BizDownload();
        download.setOperationType(ExportTypeEnum.TICKET_ALL.getCode());
        //添加下载任务数据
        download = getBizDownload(download, request, moduleType, authUserInfo);
        if (Objects.isNull(download.getDownloadId())) {
            new RestResult(RestResult.Status.FAILURE,  WebUtil.getMessage(MsgCd.ERR_MSG_BSS_780393057));
        }

        new ExportThreadUtil(exportService,
                             null,
                             moduleType,
                             ExportTypeEnum.TICKET_ALL.getCode(),
                             download.getDownloadId(),
                             authUserInfo,
                             period
        ).submit();

        logger.info("工单数据正在下载，请稍后查询下载任务！");
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1561150597));
    }

    private BizDownload getBizDownload(BizDownload download, DescribeTicketRequest request, String moduleType,
                                       AuthUser authUserInfo) {
        download.setAccountId(authUserInfo.getUserSid());
        download.setOrgSid(authUserInfo.getOrgSid());
        download.setDownloadNum(NoUtil.generateNo("DT"));
        download.setParam("request【" + JSON.toJSONString(request) + "】---moduleType【" + moduleType + "】");
        download.setStatus(2);
        download.setCreatedBy(authUserInfo.getAccount());
        download.setCreatedDt(new Date());
        download.setVersion(1);
        download.setEntityId(authUserInfo.getEntityId());
        bizDownloadMapper.insert(download);
        return download;
    }

    @Override
    public void handleUserInfo(List<WorkTicket> workTickets) {
        List<Long> userIds = new ArrayList<>();
        for (WorkTicket ticket : workTickets) {
            userIds.add(ticket.getTicketUserId());
            //未分配就不要给分配人
            if (WorkTicketStatus.UN_ASSIGNED.equals(ticket.getStatus())) {
                ticket.setTicketUser(null);
            }
        }
        if (userIds.size() > 0) {
            Criteria criteria = new Criteria();
            criteria.put("userIdList", userIds);
            List<User> users = userService.findByUserIds(criteria);
            if (users != null && users.size() > 0) {
                for (WorkTicket ticket : workTickets) {
                    if (isAnonymous(ticket)){
                        ticket.setTicketUserEmail(getTemplateInfoNodeValue(ticket, TicketConstant.EMAIL));
                        ticket.setTicketUserMobile(getTemplateInfoNodeValue(ticket, TicketConstant.MOBILE));
                        ticket.setOrgName(getTemplateInfoNodeValue(ticket, TicketConstant.ORG_NAME));
                        ticket.setTicketUserRealName(getTemplateInfoNodeValue(ticket, TicketConstant.ACCOUNT));
                        ticket.setDistributorName(TicketConstant.DISTRIBUTOR_NAME);
                        continue;
                    }
                    if (CollectionUtil.isNotEmpty(users)) {
                        for (User user : users) {
                            if ((ticket.getTicketUserId() != null)
                                    && user.getUserSid().intValue() == ticket.getTicketUserId().intValue()) {
                                String email = null;
                                String mobile = null;
                                String realName = null;
                                if (user.getEmail() != null) {
                                    email = CrytoUtilSimple.decrypt(user.getEmail(), true);
                                }
                                if (user.getMobile() != null) {
                                    mobile = CrytoUtilSimple.decrypt(user.getMobile(), true);
                                }
                                if (user.getRealName() != null) {
                                    realName = CrytoUtilSimple.decrypt(user.getRealName(), true);
                                }
                                ticket.setTicketUserMobile(mobile);
                                ticket.setTicketUserEmail(email);
                                ticket.setTicketUserRealName(realName);
                            }
                        }
                    }
                }
            }
        }
    }

    @Override
    public void setCategoryName(List<WorkTicket> workTickets) {
        List<Long> ticketIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(workTickets)) {
            workTickets.forEach(workTicket -> {
                ticketIds.add(workTicket.getTicketCategoryId());
            });
        }
        if (ticketIds.size() > 0) {
            Criteria criteria = new Criteria();
            criteria.put("categoryIdList", ticketIds);
            List<TicketCategory> ticketCategories = this.ticketCategoryService.selectCategoryByIdList(criteria);
            if (ticketCategories != null && ticketCategories.size() > 0) {
                for (WorkTicket workTicket : workTickets) {
                    for (TicketCategory ticketCategory : ticketCategories) {
                        if ((workTicket.getTicketCategoryId() != null) && (workTicket.getTicketCategoryId().intValue()
                                == ticketCategory.getId().intValue())) {
                            workTicket.setTicketCategoryName(ticketCategory.getName());
                        }
                    }
                }
            }
        }
    }

    @Override
    public void setTemplateName(List<TicketCategory> ticketCategoryList) {
        List<Long> idList = new ArrayList<>();

        if (!CollectionUtils.isEmpty(ticketCategoryList)) {
            ticketCategoryList.forEach(ticketCategory -> {
                idList.add(ticketCategory.getTemplateId());
            });
        }
        if (idList.size() > 0) {
            Criteria criteria = new Criteria();
            criteria.put("templateIdList", idList);
            List<TicketTemplate> templates = ticketTemplateService.selectTemplateByIdList(criteria);
            if (templates != null && templates.size() > 0) {
                for (TicketCategory ticketCategory : ticketCategoryList) {
                    for (TicketTemplate ticketTemplate : templates) {
                        if ((ticketCategory.getTemplateId() != null) && (ticketCategory.getTemplateId().intValue()
                                == ticketTemplate.getId().intValue())) {
                            ticketCategory.setTemplateName(ticketTemplate.getTemplateName());
                        }
                    }
                }
            }
        }
    }

    @NotNull
    private static Map<String, String> buildMap(User user, String content) {
        Map<String, String> mailMap = new HashMap(8);
        mailMap.put("content", content);
        mailMap.put("account", user.getAccount());
        mailMap.put("owner", "--".equals(user.getRealName()) ? user.getAccount() : user.getRealName());
        mailMap.put("companyName", PropertiesUtil.getProperty("company.name"));
        mailMap.put("systemName", PropertiesUtil.getProperty("system.name"));
        return mailMap;
    }

    @Override
    public void sendEmail(User user, String content) {
        Map<String, String> mailMap = buildMap(user, content);
        MailNotificationMq mailNotificationMq = new MailNotificationMq();
        mailNotificationMq.setMsgId(NotificationConsts.PlatformMsg.AccountMsg.BSSMGT_TICKET_COMMON_NOTIFICATION);
        mailNotificationMq.getMails().add(CrytoUtilSimple.decrypt(user.getEmail(), true));
        mailNotificationMq.setMap(mailMap);
        rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.ACCOUNT, mailNotificationMq);
    }

    @Override
    public int scoreTicket(WorkTicket workTicket) {
        userService.checkUserTicket(workTicket.getId());
        workTicket.setAppraiseStatus(TicketAppraiseStatusType.EVALUATED);
        WorkTicket workTicketMsg = workTicketMapper.selectByPrimaryKey(workTicket.getId());
        if(TicketAppraiseStatusType.EVALUATED.equalsIgnoreCase(workTicketMsg.getAppraiseStatus())){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_728330409));
        }
        User ticketUser = userService.findUserById(workTicketMsg.getTicketUserId());
        String username = getUsername(isAnonymous(ticketUser, workTicket), ticketUser, workTicket);
        Map<String, String> content = Maps.newHashMap();
        content.put("tickedId", workTicketMsg.getTicketNo());
        content.put("url", "#/appmain/bss/workOrders/processing/" + workTicketMsg.getId());
        content.put("ticketTitle", workTicketMsg.getTicketTitle());
        content.put("submitUser", username);
        String createdTime = getFormatDate(workTicketMsg.getCreatedDt());
        content.put("submitTime", createdTime);
        sysOssMessageService.sendOssMessage(workTicketMsg.getTicketUserId(), content, null,
                                            NotificationConsts.PlatformMsg.AccountMsg.BSSMGT_TICKET_EVALUATION,
                                            workTicketMsg.getEntityId());
        content.put("url", "#/appmain/workorder/details/" + workTicketMsg.getId());
        sysOssMessageService.sendOssMessage(workTicketMsg.getTicketUserId(), content,
                                            NotificationConsts.ConsoleMsg.AccountMsg.TENANT_TICKET_EVALUATION, null,
                                            workTicketMsg.getEntityId());
        return workTicketMapper.updateByPrimaryKeySelective(workTicket);
    }

    @Override
    public Boolean rejectTicket(CommunicateRecordRequest request) {
        WorkTicket workTicket = workTicketMapper.selectByTicketNo(request.getTicketNo());
        if (workTicket != null) {
            if (Objects.isNull(workTicket)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1385551884));
            }
            logger.info("current ticket was rejected number is : {}", workTicket.getRejectedNumber());
            if (workTicket.getRejectedNumber() != null) {
                Long rejectetNumber = workTicket.getRejectedNumber();
                if (rejectetNumber.intValue() == 1) {
                    //已经被拒绝过1次，不允许被拒绝了
                    return false;
                }
            }
            WebUserUtil.prepareUpdateParams(workTicket);
            AuthUser account = RequestContextUtil.getAuthUserInfo();
            //拒绝后单子又可以再分配，修改单子的状态为初始化的状态
            workTicket.setStatus(WorkTicketStatus.UN_ASSIGNED);
            workTicket.setDealStatus(WorkTicketDealStatus.BASE);
            workTicket.setRejectedNumber(1L);
            workTicketMapper.updateByPrimaryKeySelective(workTicket);
            List<User> users = userService.findAdminstrators();
            User ticketUser = userService.findUserById(workTicket.getTicketUserId());
            boolean isAnonymous = isAnonymous(ticketUser, workTicket);
            String username = getUsername(isAnonymous, ticketUser, workTicket);
            Map<String, String> content = Maps.newHashMap();

            content.put("tickedId", workTicket.getTicketNo());
            content.put("url", "#/appmain/bss/workOrders/processing/" + workTicket.getId());
            content.put("ticketTitle", workTicket.getTicketTitle());
            content.put("submitUser", username);
            String createdTime = getFormatDate(workTicket.getCreatedDt());
            content.put("submitTime", createdTime);

            List<Long> userIds = new ArrayList<>();
            for (User user : users) {
                userIds.add(user.getUserSid());
            }
            if (userIds.size() > 0) {
                List<UserBssEntity> userBssEntities = sysBssEntityService.sysBssEntityByCurrentUsers(userIds);
                Map<Long, List<UserBssEntity>> collectMap = userBssEntities.stream()
                                                                           .sorted(Comparator.comparing(
                                                                                   UserBssEntity::getUserSid))
                                                                           .collect(Collectors.groupingBy(
                                                                                   UserBssEntity::getBssEntityId,
                                                                                   TreeMap::new, Collectors.toList()));
                for (Map.Entry entry : collectMap.entrySet()) {
                    Long mapKey = (Long) entry.getKey();
                    //仅仅只发有权限的实体管理员
                    if (!mapKey.equals(workTicket.getEntityId())) {
                        continue;
                    }
                    if (isAnonymous) {
                        sysOssMessageService.sendAnonymousWorkOrderMessage(content,
                                                                           NotificationConsts.PlatformMsg.AccountMsg.BSSMGT_TICKET_UNASSIGNED,
                                                                           workTicket.getEntityId());
                    } else {
                        List<UserBssEntity> mapValue = (List<UserBssEntity>) entry.getValue();
                        List<Long> userIdsForMsg = mapValue.stream()
                                                           .map(UserBssEntity::getUserSid)
                                                           .collect(Collectors.toList());
                        businessNotificationService.sendCommonPlatformAdminNotification(userIdsForMsg,
                                                                                        NotificationConsts.PlatformMsg.AccountMsg.BSSMGT_TICKET_REJECTED,
                                                                                        content, mapKey);
                    }
                }
            }

            // 存储交流记录
            TicketCommunicateRecord ticketCommunicateRecord = new TicketCommunicateRecord();
            ticketCommunicateRecord.setTicketNo(workTicket.getTicketNo());
            if (request.getContent() != null) {
                ticketCommunicateRecord.setTicketContent(
                        account.getAccount() + "(" + account.getRealName() + "):" + "拒绝了工单：" + request.getContent());
            } else {
                ticketCommunicateRecord.setTicketContent(
                        account.getAccount() + "(" + account.getRealName() + "):" + "拒绝了工单：");
            }
            ticketCommunicateRecord.setMessageType(CommunicateRecordMessageType.INNER);
            ticketCommunicateRecord.setType(request.getType());
            WebUserUtil.prepareInsertParams(ticketCommunicateRecord, account.getAccount());
            ticketCommunicateRecord.setCreatedByRealName(CrytoUtilSimple.encrypt(account.getRealName(), true));
            ticketCommunicateRecordMapper.insertCommunicateRecord(ticketCommunicateRecord);

            // 存储工单附件
            List<SysMFilePath> ticketFiles = request.getTicketFiles();
            if (!CollectionUtils.isEmpty(ticketFiles)) {
                for (SysMFilePath file : ticketFiles) {
                    file.setFileNum(NoUtil.generateNo("DT"));
                    file.setAccountId(account.getUserSid());
                    file.setOperationType("01");
                    file.setFilePath(StringUtil.isEmpty(CrytoUtilSimple.decrypt(file.getFilePath())) ? null
                                             : CrytoUtilSimple.encrypt(CrytoUtilSimple.decrypt(file.getFilePath())));
                    file.setOperationId(ticketCommunicateRecord.getId().toString());
                    WebUserUtil.prepareInsertParams(file, account.getAccount());
                    sysMFilePathMapper.insertSysMFilePath(file);
                }
            }

            return true;
        }
        return false;
    }


    /**
     * 获取工单用户名
     *
     * @param isAnonymous 是匿名
     * @param ticketUser  票证用户
     * @return {@link String}
     */
    private String getUsername(boolean isAnonymous, User ticketUser, WorkTicket workTicket) {
        String username;
        if (isAnonymous) {
            username = getTemplateInfoNodeValue(workTicket, TicketConstant.ACCOUNT);
        } else {
            username = ticketUser.getAccount() + "(" + ticketUser.getRealName() + ")";
        }
        return username;
    }

    private boolean isAnonymous(WorkTicket workTicket) {
        return isAnonymous(null, workTicket);
    }

    /**
     * 是否为匿名工单
     *
     * @param ticketUser 用户
     * @param workTicket 工单详情
     * @return boolean
     */
    private boolean isAnonymous(User ticketUser, WorkTicket workTicket) {
        return Objects.isNull(ticketUser) &&
                TicketConstant.ANONYMITY_ID.equals(workTicket.getOrgSid()) &&
                TicketConstant.ANONYMITY_ID.equals(workTicket.getTicketUserId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean feedback(List<MultipartFile> files, WorkTicket workTicket) {
        //验证文件类型
        boolean isContentType = files.stream().allMatch(v -> contentTypes.contains(v.getContentType()));
        if (!isContentType){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_100905708));
        }
        //文件大小限制，最大200MB
        boolean isSize = files.stream().allMatch(v -> FileUtil.checkFileSize(v.getSize(), 200, FileSizeEnum.MB));
        if (!isSize){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_526452970));
        }
        cn.com.cloudstar.rightcloud.oss.common.pojo.User authUser = AuthUtil.getAuthUser();
        WebUserUtil.prepareUpdateParams(workTicket);
        String userAccount = workTicket.getCreatedBy();
        //处理上传的文件
        if (!CollectionUtils.isEmpty(files)) {
            List<RecordFile> recordFiles = new ArrayList<>();
            for (MultipartFile file : files) {
                //开始上传文件

                StorageResult result = storageService.saveFile(file, StoragePathEnum.GENERAL.getPrefix());
                Attachments attachment = new Attachments();
                attachment.setOriginalName(file.getOriginalFilename());
                attachment.setAttachmentName(result.getFileName());
                attachment.setExtName(FileUtil.extName(file.getOriginalFilename()));
                String attachmentUrl = result.getRelativeNginxUrl()
                                             .replace(StorageUtil.separator + result.getFileName(), "");
                attachment.setAttachmentUrl(attachmentUrl);
                attachment.setUploadDate(new Date());
                attachment.setAttachmentSize(file.getSize());
                WebUserUtil.prepareInsertParams(attachment, userAccount);

                RecordFile recordFile = new RecordFile();
                recordFile.setUrl(attachmentUrl);
                recordFile.setUrl(PropertiesUtil.getProperty(FileUtil.FILE_PATH_PROPER_KEY));
                recordFile.setAttachmentName(result.getFileName());
                recordFile.setOriginalName(file.getOriginalFilename());
                recordFiles.add(recordFile);

                //将文件信息存到表中
                attachmentMapper.insertSelective(attachment);
                AttachmentRelation attachmentRelation = new AttachmentRelation();
                attachmentRelation.setObjId(workTicket.getId());
                attachmentRelation.setAttachmentId(attachment.getAttachmentSid());
                attachmentRelation.setRelationType("ticket");
                attachmentRelationMapper.insertSelective(attachmentRelation);
                //将文件上传到服务器
                try {
                    File newFile = FileUtils.getFile(
                            attachment.getAttachmentUrl() + "/" + attachment.getAttachmentName());
                    //如果目录不存在，则创建目录
                    if (!newFile.getParentFile().exists()) {
                        newFile.getParentFile().mkdirs();
                    }
                    file.transferTo(newFile);
                } catch (IOException e) {
                    logger.error(e.getMessage());
                }
            }
            // 取出交流记录
            List<Record> recordList = JSONArray.parseArray(workTicket.getCommunicateRecord(), Record.class);
            Record record = recordList.get(recordList.size() - 1);
            record.setRecordFiles(recordFiles);
            recordList.remove(recordList.size() - 1);
            recordList.add(record);
            workTicket.setCommunicateRecord(JSON.toJSONString(recordList));
        }
        this.workTicketMapper.updateByPrimaryKeySelective(workTicket);
        workTicket = workTicketMapper.selectByPrimaryKey(workTicket.getId());

        Map<String, String> content = Maps.newHashMap();

        content.put("tickedId", workTicket.getTicketNo());
        content.put("url", "#/appmain/bss/workOrders/processing/" + workTicket.getId());
        content.put("ticketTitle", workTicket.getTicketTitle());
        String createdTime = getFormatDate(workTicket.getCreatedDt());
        content.put("submitTime", createdTime);
        Long ticketId = workTicket.getId();
        content.put("url", "#/appmain/bss/workOrders/processing/"
                + ticketId);
        Long tickEntityId = workTicket.getEntityId();
        if (authUser.getUserSid().equals(workTicket.getTicketUserId())) {
            //用户发送的消息 通知管理员
            Long allocationTicketUserId = workTicket.getAllocationTicketUserId();
            content.put("url", "#/appmain/bss/workOrders/processing/" + workTicket.getId());
            content.put("submitUser", workTicket.getTicketUser());
            List<Code> codes = sysBssEntityService.sysBssEntityByCurrentUser(allocationTicketUserId);
            if (CollectionUtil.isNotEmpty(codes)) {
                codes.stream().forEach(c -> {
                    //仅仅只发有权限的实体管理员
                    if (c.getCodeSid().equals(tickEntityId)) {
                        sysOssMessageService.sendOssMessage(allocationTicketUserId,content,null,NotificationConsts.PlatformMsg.AccountMsg.BSSMGT_TICKET_PADDING,tickEntityId);
                    }
                });
            }
        } else {
            content.put("url", "#/appmain/workorder/details/" + workTicket.getId());
            content.put("submitUser", workTicket.getAllocationTicketUser());
            businessNotificationService.sendCommonPlatformNotification(workTicket.getTicketUserId(), NotificationConsts.ConsoleMsg.AccountMsg.TENANT_TICKET_PADDING,
                    content, workTicket.getEntityId());
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean finishTicket(Long ticketId) {
        WorkTicket workTicket = this.workTicketMapper.selectByPrimaryKey(ticketId);
        workTicket.setDealStatus(WorkTicketDealStatus.BASE);
        workTicket.setStatus(WorkTicketStatus.PROCESSED);
        workTicket.setAllocationTicketTime(new Date());
        this.workTicketMapper.updateByPrimaryKeySelective(workTicket);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean record(CommunicateRecordVo record) {

        // 如果工单状态为 03已完成
        WorkTicket workTicket = workTicketMapper.selectByTicketNo(record.getTicketNo());
        if (WorkTicketStatus.PROCESSED.equals(workTicket.getStatus())) {
            workTicket.setStatus(WorkTicketStatus.PROCESSING);
            workTicketMapper.updateByPrimaryKeySelective(workTicket);
        }

        // 存储交流记录
        TicketCommunicateRecord ticketCommunicateRecord = new TicketCommunicateRecord();
        ticketCommunicateRecord.setTicketNo(record.getTicketNo());
        ticketCommunicateRecord.setTicketContent(record.getContent());
        ticketCommunicateRecord.setType(record.getType());

        //消息类型
        if (record.getMsgUserId() != null) {
            User user = userService.selectByPrimaryKey(record.getMsgUserId());
            ticketCommunicateRecord.setMsgUserId(record.getMsgUserId());
            ticketCommunicateRecord.setMsgUserName(user.getRealName());
        }

        ticketCommunicateRecord.setMessageType(CommunicateRecordMessageType.OUTSIDE);

        AuthUser account = RequestContextUtil.getAuthUserInfo();
        if (account == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_531007114));
        }
        //非匿名消息并且在处理中就可以发消息
        if (WorkTicketStatus.PROCESSING.equals(workTicket.getStatus()) && !isAnonymous(workTicket)) {
            //工单提交人信息
            User user = userService.selectByPrimaryKey(workTicket.getTicketUserId());
            List<User> users = new ArrayList<>();
            Map<String, String> content = Maps.newHashMap();

            content.put("tickedId", workTicket.getTicketNo());
            content.put("url", "#/appmain/workorder/details/" + workTicket.getId());
            content.put("ticketTitle", workTicket.getTicketTitle());
            content.put("submitUser", user.getAccount());
            String createdTime = getFormatDate(workTicket.getCreatedDt());
            content.put("submitTime", createdTime);
            //当前登录用户为工单提交人
            if (account.getUserSid().equals(workTicket.getTicketUserId())) {
                //子用户给处理人发送的留言，通知处理人；租户给处理人发送的留言 通知处理人；
                List<Code> codes = sysBssEntityService.sysBssEntityByCurrentUser(workTicket.getAllocationTicketUserId());
                if (CollectionUtil.isNotEmpty(codes)) {
                    content.put("url", "#/appmain/bss/workOrders/processing/" + workTicket.getId());
                    codes.stream().forEach(c -> {
                        //仅仅只发有权限的实体管理员
                        if (c.getCodeSid().equals(workTicket.getEntityId())) {
                            sysOssMessageService.sendOssMessage(workTicket.getAllocationTicketUserId(),content,null,NotificationConsts.PlatformMsg.AccountMsg.BSSMGT_TICKET_PADDING,workTicket.getEntityId());
                        }
                    });
                }
                //工单提交人为子用户，当前登录用户为工单提交人的租户管理员
            } else if (account.getUserSid().equals(user.getParentSid()) && user.getParentSid() != null) {
                //租户在子用户创建的工单上给处理人发送的留言 通知处理人；
                List<Code> codes = sysBssEntityService.sysBssEntityByCurrentUser(workTicket.getAllocationTicketUserId());
                if (CollectionUtil.isNotEmpty(codes)) {
                    content.put("url", "#/appmain/bss/workOrders/processing/" + workTicket.getId());
                    codes.stream().forEach(c -> {
                        //仅仅只发有权限的实体管理员
                        if (c.getCodeSid().equals(workTicket.getEntityId())) {
                            sysOssMessageService.sendOssMessage(workTicket.getAllocationTicketUserId(),content,null,NotificationConsts.PlatformMsg.AccountMsg.BSSMGT_TICKET_PADDING,workTicket.getEntityId());
                        }
                    });
                }
                //租户在子用户创建的工单上给处理人发送的留言 通知子用户；
                sysOssMessageService.sendOssMessage(user.getUserSid(),content,NotificationConsts.ConsoleMsg.AccountMsg.TENANT_TICKET_PADDING,null,workTicket.getEntityId());
            } else {
                //如果处理一次后就不允许被拒绝了，该值是1
                if (workTicket.getRejectedNumber() == null) {
                    workTicket.setRejectedNumber(1L);
                    workTicketMapper.updateByPrimaryKeySelective(workTicket);
                }
                //如果是处理人内部处理，消息不需要发送给用户
                if (record.getMsgUserId() != null) {
                    ticketCommunicateRecord.setMessageType(CommunicateRecordMessageType.INNER);

                } else {
                    content.put("url", "#/appmain/workorder/details/" + workTicket.getId());
                    //当前登录用户为工单处理人
                    User workTicketUser = userService.selectByPrimaryKey(workTicket.getTicketUserId());
                    users.add(workTicketUser);
                    if (user.getParentSid() != null) {
                        // 子账户，添加租户
                        User parentUser = userService.selectByPrimaryKey(user.getParentSid());
                        users.add(parentUser);
                    }
                    for (User user1 : users) {
                        //子用户创建的工单，处理人给子用户发送的留言，通知子用户和租户；处理人给租户发送的留言 通知租户
                        businessNotificationService.sendCommonPlatformNotification(user1.getUserSid(),
                                                                                   NotificationConsts.ConsoleMsg.AccountMsg.TENANT_TICKET_PADDING,
                                                                                   content, workTicket.getEntityId());
                    }
                }

            }
        }
        //针对非内部委派的时候，消息还是需要的
        if (record.getMsgUserId() == null) {
            WebUserUtil.prepareInsertParams(ticketCommunicateRecord, account.getAccount());
            ticketCommunicateRecord.setCreatedByRealName(CrytoUtilSimple.encrypt(account.getRealName(), true));
            ticketCommunicateRecordMapper.insertCommunicateRecord(ticketCommunicateRecord);

            // 存储工单附件
            List<SysMFilePath> ticketFiles = record.getTicketFiles();
            if (!CollectionUtils.isEmpty(ticketFiles)) {
                for (SysMFilePath file : ticketFiles) {
                    //校验文件
                    FileVerifyUtil.verifyName(file.getFileName(), StoragePathEnum.TICKET);
                    storageService.validateFilePath(file.getFilePath(), StoragePathEnum.TICKET, file.getFileName());
                    file.setFileNum(NoUtil.generateNo("DT"));
                    file.setAccountId(account.getUserSid());
                    file.setOperationType("01");
                    if (!ObjectUtils.isEmpty(file.getFilePath())) {
                        file.setFilePath(file.getFilePath());
                        if (!CrytoUtilSimple.isEncryptedData(file.getFilePath())) {
                            file.setFilePath(CrytoUtilSimple.encrypt(file.getFilePath()));
                        }
                    }
                    file.setOperationId(ticketCommunicateRecord.getId().toString());
                    WebUserUtil.prepareInsertParams(file, account.getAccount());
                    sysMFilePathMapper.insertSysMFilePath(file);
                }
            }
        }
        //如果内部委派，就把单子转出去既可不用重复发消息
        if (record.getMsgUserId() != null) {
            distributionTicket(workTicket.getId(), record.getMsgUserId(), record.getContent(), "委派",
                               record.getTicketFiles());
        }
        return true;
    }

    private String getFormatDate(Date date) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String createdTime = df.format(date);
        return createdTime;
    }

}
