/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.module.selfservice.bean.request;

import cn.com.cloudstar.rightcloud.oss.common.constants.type.DeploymentType;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.bean.model.AppDeployConfigModelVO;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.bean.valid.DeployContainerAppValidView;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;
import javax.validation.constraints.NotNull;
import javax.validation.groups.Default;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * The type DeployAppRequest.
 * <p>
 *
 * <AUTHOR>
 * @date 2019/7/2
 */
@Getter
@Setter
@ApiModel(description = "部署应用")
public class DeployAppRequest {

    @NotBlank
    @ApiModelProperty(value = "名称", required = true)
    private String name;

    @NotBlank(groups = {DeployContainerAppValidView.class})
    @ApiModelProperty(value = "应用ID", required = true)
    private String appId;

    @NotBlank(groups = {DeployContainerAppValidView.class})
    @ApiModelProperty(value = "应用类型", required = true)
    private String appType;

    @NotBlank(groups = {DeployContainerAppValidView.class})
    @ApiModelProperty(value = "应用版本", required = true)
    private String appVersion;

    @NotBlank(groups = {DeployContainerAppValidView.class})
    @ApiModelProperty(value = "应用分类", required = true)
    private String appClass;

    @ApiModelProperty(value = "应用镜像", required = true)
    private String appImage;

    @NotBlank
    @ApiModelProperty(value = "部署类型", required = true)
    private String deploymentType;

    @ApiModelProperty(notes = "部署的主机")
    private List<String> hosts = new ArrayList<>();

    @NotEmpty
    @ApiModelProperty(value = "部署的集群", required = true)
    private List<String> clusters = new ArrayList<>();

    @ApiModelProperty(notes = "部署")
    private String deployment;

    @ApiModelProperty(notes = "描述")
    private String description;

    @NotNull
    @ApiModelProperty(value = "配置信息", required = true)
    private AppDeployConfigModelVO config;

    public Class<?> getValidView() {
        if (DeploymentType.CONTAINER.equals(this.deploymentType)) {
            return DeployContainerAppValidView.class;
        }

        return Default.class;
    }
}
