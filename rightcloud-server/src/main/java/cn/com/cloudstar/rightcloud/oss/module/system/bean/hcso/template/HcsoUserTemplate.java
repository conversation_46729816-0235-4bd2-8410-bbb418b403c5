/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.module.system.bean.hcso.template;

import com.fasterxml.jackson.annotation.JsonIgnore;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 * HCSO导入模板对象
 * </p>
 *
 * <AUTHOR>
 * @since 2021/4/20
 */
@NoArgsConstructor
@Data
public class HcsoUserTemplate implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "HCSO账户ID*")
    private String accountId;

    @ExcelProperty(value = "HCSO账户名*")
    private String accountName;

    @ExcelProperty(value = "验证类型*")
    private String type;

    @ExcelProperty(value = "HCSO账户密码")
    private String password;

    @ExcelProperty(value = "HCSO AK*")
    private String ak;

    @ExcelProperty(value = "HCSO SK*")
    private String sk;

    @ExcelProperty(value = "项目ID*")
    private String projectId;

    @ExcelProperty(value = "OBS项目ID*")
    private String obsProjectId;
}
