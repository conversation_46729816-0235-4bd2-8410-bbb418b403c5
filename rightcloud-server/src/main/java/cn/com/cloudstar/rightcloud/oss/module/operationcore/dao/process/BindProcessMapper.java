package cn.com.cloudstar.rightcloud.oss.module.operationcore.dao.process;

import org.springframework.stereotype.Repository;

import java.util.List;

import cn.com.cloudstar.rightcloud.core.pojo.dto.process.BindingProcess;
import cn.com.cloudstar.rightcloud.core.pojo.dto.process.SysProcessBusiness;

@Repository
public interface BindProcessMapper {

    /**
     * 查询是否存在相同业务的流程绑定关系
     * @return
     */
    int selectByBusinessName(String businessName);

    /**
     * 创建绑定关系
     *
     * @param bindingProcess
     *
     * @return int
     */
    int insert(BindingProcess bindingProcess);

    /**
     * 修改绑定关系
     *
     * @param bindingProcess
     *
     * @return int
     */
    int update(BindingProcess bindingProcess);

    List<BindingProcess> select();

    void deleteById(Long id);

    List<BindingProcess> selectByBusinessCode(String businessCode);

    SysProcessBusiness selectById(Long id);
}
