/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.module.analysis.bean.model;

import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.stats.CloudEnvInstanceStat;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.stats.OrgInstanceStat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

@Data
@ApiModel(description = "组织统计")
public class OrgStatVO {

    /**
     * 组织名称
     */
    @ApiModelProperty("组织名称")
    private String name;

    /**
     * 数量
     */
    @ApiModelProperty("数量")
    private Long value;

    /**
     * 云环境分布
     */
    @ApiModelProperty("云环境分布")
    private List<CloudEnvInstanceStat> envData;

    /**
     * 项目分布
     */
    @ApiModelProperty("项目分布")
    private List<OrgInstanceStat> projectData;
}
