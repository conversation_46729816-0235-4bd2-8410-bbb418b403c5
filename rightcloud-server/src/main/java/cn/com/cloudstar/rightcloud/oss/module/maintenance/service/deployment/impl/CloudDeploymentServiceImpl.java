/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.module.maintenance.service.deployment.impl;

import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResFloatingIp;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResVd;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResVm;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.common.util.json.JsonUtil;
import cn.com.cloudstar.rightcloud.core.pojo.dto.cloud.CloudDeployment;
import cn.com.cloudstar.rightcloud.core.pojo.dto.cloud.CloudDeploymentScript;
import cn.com.cloudstar.rightcloud.core.pojo.dto.cloud.CloudDeploymentScriptParam;
import cn.com.cloudstar.rightcloud.core.pojo.dto.deploy.CloudDeploymentVm;
import cn.com.cloudstar.rightcloud.oss.common.constants.WebConstants;
import cn.com.cloudstar.rightcloud.oss.common.constants.res.status.ResVmManageStatus;
import cn.com.cloudstar.rightcloud.oss.common.constants.res.status.ResVmStatus;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.ServerType;
import cn.com.cloudstar.rightcloud.oss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.oss.common.util.AuthUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.WebUserUtil;
import cn.com.cloudstar.rightcloud.oss.module.maintenance.dao.deployment.CloudDeploymentMapper;
import cn.com.cloudstar.rightcloud.oss.module.maintenance.dao.deployment.CloudDeploymentScriptMapper;
import cn.com.cloudstar.rightcloud.oss.module.maintenance.dao.deployment.CloudDeploymentScriptParamMapper;
import cn.com.cloudstar.rightcloud.oss.module.maintenance.service.deployment.CloudDeploymentService;
import cn.com.cloudstar.rightcloud.oss.module.maintenance.service.tag.CloudTagService;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.*;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.network.ResFloatingIpRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.server.ResVmRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.storage.ResVdRemoteService;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * The type CloudDeploymentServiceImpl.
 * <p>
 * Created on 2017/5/10
 *
 * <AUTHOR>
 */
@Component
public class CloudDeploymentServiceImpl implements CloudDeploymentService {

    private static final Logger logger = LoggerFactory.getLogger(CloudDeploymentServiceImpl.class);

    @Autowired
    private CloudTagService cloudTagService;

    @Autowired
    private CloudDeploymentMapper cloudDeploymentMapper;

    @DubboReference
    private ResVdRemoteService resVdService;

    @DubboReference
    private ResVmRemoteService resVmService;

    @Autowired
    private CloudDeploymentScriptMapper cloudDeploymentScriptMapper;

    @Autowired
    private CloudDeploymentScriptParamMapper cloudDeploymentScriptParamMapper;

    @DubboReference
    private ResFloatingIpRemoteService resFloatingIpService;


    @Override
    public List<CloudDeployment> selectDeploymentByParams(Criteria params) {
        return this.cloudDeploymentMapper.selectByExample(params);
    }

    @Override
    public int countByExample(Criteria criteria) {
        return this.cloudDeploymentMapper.countByExample(criteria);
    }

    @Override
    public CloudDeployment findByPrimaryKey(Long id) {
        return this.cloudDeploymentMapper.selectByPrimaryKey(id);
    }

    @Override
    public int deleteByPrimaryKey(Long id) {
        return this.cloudDeploymentMapper.deleteByPrimaryKey(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByPrimaryKeySelective(CloudDeployment record) {
        CloudDeployment orgCloudDeployment = this.cloudDeploymentMapper.selectByPrimaryKey(record.getId());

        int count = this.cloudDeploymentMapper.updateByPrimaryKeySelective(record);

        // 存入审计日志
        if (StringUtil.isBlank(record.getName())) {
            record.setName(orgCloudDeployment.getName());
        }
        return count;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertSelective(CloudDeployment cloudDeployment) {
        if (this.cloudDeploymentMapper.checkResourcesGroupByName(cloudDeployment.getName()) > 0) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_915604544));
        }
        int count = this.cloudDeploymentMapper.insertSelective(cloudDeployment);

        return count;
    }

    /**
     * Insert selective int.
     *
     * @return the int
     */
    @Override
    public List<ResVd> selectDeployDisk(Criteria criteria) {
        QueryResVdByDeployRequest queryResVdByDeployRequest = JsonUtil.fromJson(
                JsonUtil.toJson(criteria.getCondition()), QueryResVdByDeployRequest.class);
        return this.resVdService.selectBydeployId(queryResVdByDeployRequest);
    }

    /**
     * 删除部署及相关信息
     *
     * @param id the id
     *
     * @return the int
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteDeployAndRelevantInfo(Long id) {
        //判断是否关实例
        if (this.cloudDeploymentMapper.checkResourcesGroup(id) > 0) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_310520687));
        }

        Criteria criteria = new Criteria();

        // 删除脚本、脚本参数
        criteria.put("deploymentId", id);
        List<CloudDeploymentScript> cdsList = this.cloudDeploymentScriptMapper.selectByParams(criteria);
        if (cdsList != null && cdsList.size() > 0) {
            cdsList.forEach(script -> {
                Criteria example = new Criteria();
                example.put("stScriptId", script.getId());
                this.cloudDeploymentScriptParamMapper.deleteByExample(example);
            });
            this.cloudDeploymentScriptMapper.deleteByParams(criteria);
        }

        //将部署组内已删除的主机部署组ID清空
        this.resVmService.resetResVmDeploymentId(id);

        //将部署组内硬盘的部署组ID清空
        this.resVdService.resetDeploymentId(id);

        return this.cloudDeploymentMapper.deleteByPrimaryKey(id);
    }

    /**
     * 克隆部署及相关信息
     *
     * @param id the id
     *
     * @return the int
     */
    @Override
    public Long cloneDeploymentInfo(Long id, Long orgSid) {

        // 获取当前部署
        CloudDeployment cloudDeploy = this.cloudDeploymentMapper.selectByPrimaryKey(id);

        CloudDeployment orgCloudDeploy = new CloudDeployment();
        BeanUtils.copyProperties(cloudDeploy, orgCloudDeploy);

        Criteria cloneCrieria = new Criteria();
        cloneCrieria.put("orgSid", cloudDeploy.getOrgSid());
        String[] names = cloudDeploy.getName().split(" ");
        String cloneLikeName = StrUtil
                .join(" ", Stream.of(names).limit(names.length == 1 ? 1 : names.length - 1).collect(Collectors.toList())
                );
        cloneCrieria.put("cloneLikeName", StringUtil.isNullOrEmpty(cloneLikeName) ? names[0] : cloneLikeName);

        int versionNum = 1;
        List<CloudDeployment> clones = this.cloudDeploymentMapper.selectByExample(cloneCrieria);
        for (CloudDeployment st : clones) {
            if (st.getName().matches("^.*\\sv\\d+$")) {
                String[] name = st.getName().split(" v");
                int realCurrNum = Integer.parseInt(name[name.length - 1]);
                if (realCurrNum >= versionNum) {
                    versionNum = realCurrNum + 1;
                }
            }
        }
        String cloneName = " " + WebConstants.CLONE_DEFAULT + versionNum;
        String newName = cloneLikeName;
        int maxLength = 64 - cloneName.length();
        cloudDeploy.setName(
                newName.substring(0, (newName.length() - 1 > maxLength ? maxLength : newName.length())) + cloneName);

        WebUserUtil.prepareInsertParams(cloudDeploy);
        cloudDeploy.setOrgSid(orgSid);
        this.cloudDeploymentMapper.insertSelective(cloudDeploy);

        // 复制实例，将实例类型改为server
        ResVmByCriteria resVmByCriteria = new ResVmByCriteria();
        resVmByCriteria.setCloudDeploymentId(id);
        resVmByCriteria.setStatusNotEquals(ResVmStatus.DELETED);
        resVmByCriteria.setServerTypeNotEquals(ServerType.INSTANCE);
        List<ResVm> hostList = this.resVmService.selectByExample(resVmByCriteria);
        if (hostList != null && hostList.size() > 0) {
            for (ResVm resVm : hostList) {
                // 保存原始ID
                String originID = resVm.getId();

                resVmByCriteria = new ResVmByCriteria();
                resVmByCriteria.setOwnerId(Long.valueOf(resVm.getOwnerId()));
                String[] hostNames = resVm.getInstanceName().split(" ");
                String cloneHostLikeName = StringUtils
                        .join(Stream.of(hostNames).limit(hostNames.length == 1 ? 1 : hostNames.length - 1)
                                    .collect(Collectors.toList()), " ");
                resVmByCriteria.setCloneLikeName( (StringUtil.isNullOrEmpty(cloneHostLikeName) ? hostNames[0] : cloneHostLikeName));

                int versionHostNum = 1;
                List<ResVm> hostClones = this.resVmService.selectByExample(resVmByCriteria);
                for (ResVm st : hostClones) {
                    String[] name = st.getInstanceName().split(" ");
                    if (name[name.length - 1].contains("v")) {
                        String[] currVer = name[name.length - 1].split("v");
                        if (currVer.length > 0) {
                            boolean bol = StringUtils.isNumeric(currVer[currVer.length - 1]);
                            if (bol) {
                                int realCurrNum = Integer.parseInt(currVer[currVer.length - 1]);
                                if (realCurrNum >= versionHostNum) {
                                    versionHostNum = realCurrNum + 1;
                                }
                            }
                        }

                    }
                }
                String cloneHostName = " " + WebConstants.CLONE_DEFAULT + versionHostNum;
                String newHostName = cloneHostLikeName;
                resVm.setInstanceName(
                        newHostName.substring(0, (newHostName.length() - 1 > 64 ? 64 : newHostName.length())) +
                                cloneHostName);

                resVm.setStatus(ResVmStatus.PENDING);
                resVm.setManageStatus(ResVmManageStatus.UNUNITED);
                resVm.setServerType(ServerType.SERVER);
                resVm.setCloudDeploymentId(cloudDeploy.getId());

                resVm.setId(null);
                resVm.setInstanceId(null);
                WebUserUtil.prepareInsertParams(resVm);
                resVm.setOrgSid(AuthUtil.getCurrentOrgSid());
                resVm.setPublicIp(null);
                resVm.setInnerIp(null);

                String originParam = resVm.getOriginParam();
                JsonNode jsonNode = JsonUtil.fromJson(originParam);
                if (jsonNode.has("nics")) {
                    jsonNode.get("nics").elements().forEachRemaining(nic -> {
                        ((ObjectNode) nic).put("autoIp", true);
                        ((ObjectNode) nic).put("fixedIp", "");
                        ((ObjectNode) nic).put("fixedIpId", "");
                    });
                }
                resVm.setOriginParam(JsonUtil.toJson(jsonNode));
                this.resVmService.insertSelective(resVm);

                // 实例脚本、参数 —> 新实例脚本、参数
                ResVmScriptParams resVmScriptParams = new ResVmScriptParams();
                resVmScriptParams.setInstanceId(originID);

            }
        }

        // 复制资源组的脚本、参数
        Criteria criteria = new Criteria();
        criteria.put("deploymentId", id);
        List<CloudDeploymentScript> deployScriptList = this.cloudDeploymentScriptMapper.selectByParams(criteria);
        for (CloudDeploymentScript cdScript : deployScriptList) {
            cdScript.setDeploymentId(cloudDeploy.getId());
            // 获取部署脚本对应的部署脚本参数，并复制到新的关联实例上面
            criteria = new Criteria();
            criteria.put("stScriptId", cdScript.getId());
            // 新增新的部署脚本
            WebUserUtil.prepareInsertParams(cdScript);
            this.cloudDeploymentScriptMapper.insertSelective(cdScript);
            List<CloudDeploymentScriptParam> scriptParamsList = this.cloudDeploymentScriptParamMapper
                    .selectByExample(criteria);
            for (CloudDeploymentScriptParam cdScriptParam : scriptParamsList) {
                cdScriptParam.setStScriptId(cdScript.getId());
                WebUserUtil.prepareInsertParams(cdScriptParam);
                this.cloudDeploymentScriptParamMapper.insertSelective(cdScriptParam);
            }
        }

        // 存入审计日志
        Map<String, Object> logMap = JsonUtil.fromJson(JsonUtil.toJson(orgCloudDeploy),
                                                       new TypeReference<Map<String, Object>>() {
                                                       });
        logMap.put("cloneName", cloudDeploy.getName());
        return cloudDeploy.getId();
    }

    @Override
    public boolean addInstanceListToDeploy(Long deployId, List<ResVm> hosts) {
        CloudDeployment cloudDeployment = findByPrimaryKey(deployId);
        int result = 0;
        if (!CollectionUtil.isEmpty(hosts)) {
            for (ResVm ins : hosts) {
                ins.setCloudDeploymentId(deployId);
                ins.setOrgSid(cloudDeployment.getOrgSid());
                result += this.resVmService.updateByPrimaryKeySelective(ins);

                // 级联关联硬盘
                ResVdParams resVdParams = new ResVdParams();
                resVdParams.setResVmId(ins.getId());
                List<ResVd> volumes = resVdService.selectByParams(resVdParams);
                for (ResVd volume : volumes) {
                    volume.setCloudDeploymentId(deployId);
                    volume.setOrgSid(cloudDeployment.getOrgSid());
                    resVdService.updateByPrimaryKeySelective(volume);
                }

                // 级联关联弹性IP
                if (org.apache.commons.lang3.StringUtils.isNotBlank(ins.getInstanceId())) {
                    QueryResFloatingIpByParamsRequest queryResFloatingIpByParamsRequest = new QueryResFloatingIpByParamsRequest();
                    queryResFloatingIpByParamsRequest.setInstanceId(ins.getInstanceId());
                    List<ResFloatingIp> floatingIps = resFloatingIpService.selectByParams(queryResFloatingIpByParamsRequest);
                    for (ResFloatingIp floatingIp : floatingIps) {
                        floatingIp.setCloudDeploymentId(deployId);
                        floatingIp.setOrgSid(cloudDeployment.getOrgSid());
                        resFloatingIpService.updateByPrimaryKeySelective(floatingIp);
                    }
                }

            }
        }

        return Objects.equals(hosts.size(), result);
    }

    private String getResVmNames(Long deployId) {
        if (deployId == null) {
            return null;
        }
        ResVmParams resVmParams = new ResVmParams();
        resVmParams.setCloudDeploymentId(deployId+"");
        resVmParams.setServerTypeNotEquals( ServerType.ELASTIC);
        resVmParams.setStatusNotEquals(ResVmStatus.DELETED);
        List<ResVm> resVms = this.resVmService.selectBaseByParams(resVmParams);
        if (CollectionUtil.isNotEmpty(resVms)) {
            StringBuilder str = new StringBuilder();
            for (int i = 0; i < resVms.size(); i++) {
                str.append(i > 0 ? "," : "").append(resVms.get(i).getInstanceName());
            }
            return str.toString();
        }
        return null;
    }

    @Override
    public boolean moveOutDeployment(List<String> resVmIds) {
        if (!CollectionUtil.isEmpty(resVmIds)) {
            for (String resVmId : resVmIds) {
                ResVm resVm = this.resVmService.selectByPrimaryKey(resVmId);

                if (resVm == null) {
                    logger.warn("移除资源组时 未找到该主机 resVmId : {}, skip", resVmId);
                    break;
                }
                Long cloudDeploymentId = resVm.getCloudDeploymentId();
                CloudDeployment cloudDeployment = this.findByPrimaryKey(cloudDeploymentId);
                String resVmNames = getResVmNames(cloudDeploymentId);

                ResVm updResVm = new ResVm();
                updResVm.setId(resVmId);
                updResVm.setCloudDeploymentId(null);
                this.resVmService.updateDeploymentIdByPrimaryKey(updResVm);

                // 级联关联硬盘
                ResVdParams resVdParams = new ResVdParams();
                resVdParams.setResVmId(resVmId);
                List<ResVd> volumes = resVdService.selectByParams(resVdParams);
                for (ResVd volume : volumes) {
                    volume.setCloudDeploymentId(null);
                    resVdService.updateByPrimaryKey(volume);
                }

                // 级联关联弹性IP
                if (StringUtil.isNotBlank(resVm.getInstanceId())) {
                    QueryResFloatingIpByParamsRequest queryResFloatingIpByParamsRequest = new QueryResFloatingIpByParamsRequest();
                    queryResFloatingIpByParamsRequest.setInstanceId(resVm.getInstanceId());
                    List<ResFloatingIp> floatingIps = resFloatingIpService.selectByParams(queryResFloatingIpByParamsRequest);
                    for (ResFloatingIp floatingIp : floatingIps) {
                        floatingIp.setCloudDeploymentId(null);
                        resFloatingIpService.updateByPrimaryKey(floatingIp);
                    }
                }

                if (cloudDeployment != null) {
                    // 存入审计日志
                    Map<String, Object> oldDataMap = JsonUtil.fromJson(JsonUtil.toJson(cloudDeployment),
                                                                       new TypeReference<Map<String, Object>>() {
                                                                       });
                    oldDataMap.put("resVmNames", resVmNames);
                    Map<String, Object> newDataMap = JsonUtil.fromJson(JsonUtil.toJson(cloudDeployment),
                                                                       new TypeReference<Map<String, Object>>() {
                                                                       });
                    newDataMap.put("resVmNames", getResVmNames(cloudDeploymentId));
                }
            }
        }
        return true;
    }

    @Override
    public int countByVmId(String id) {
        return cloudDeploymentMapper.countByVmId(id);
    }

    @Override
    public boolean removeInstanceListFromDeploy(Long deployId, List<String> hosts) {
        return resVmService.removeCloudDeploymentRelation(deployId, hosts) > 0;
    }

    @Override
    public List<CloudDeploymentVm> selectHostRelationDeployByExample(Criteria criteria) {
        return cloudDeploymentMapper.selectHostRelationDeployByExample(criteria);
    }

    @Override
    public void insertHostRelationDeploy(List<CloudDeploymentVm> cloudDeploymentVms) {
        cloudDeploymentMapper.insertHostRelationDeploy(cloudDeploymentVms);
    }

    @Override
    public void assertCloudDeploymentNonNull(Long deployId) {
        CloudDeployment cloudDeployment = findByPrimaryKey(deployId);
        if (cloudDeployment == null) {
            BizException.throwException(String.format(WebUtil.getMessage(MsgCd.ERROR_RES_NOT_FOUND), "资源组"));
        }

    }
}
