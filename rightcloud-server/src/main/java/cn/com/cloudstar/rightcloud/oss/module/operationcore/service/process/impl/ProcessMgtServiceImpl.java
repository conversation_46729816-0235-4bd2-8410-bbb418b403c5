/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.module.operationcore.service.process.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.IdentityService;
import org.camunda.bpm.engine.RepositoryService;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.TaskService;
import org.camunda.bpm.engine.repository.Deployment;
import org.camunda.bpm.engine.repository.ProcessDefinition;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.BpmnModelElementInstance;
import org.camunda.bpm.model.bpmn.instance.ConditionExpression;
import org.camunda.bpm.model.bpmn.instance.Definitions;
import org.camunda.bpm.model.bpmn.instance.EndEvent;
import org.camunda.bpm.model.bpmn.instance.ExclusiveGateway;
import org.camunda.bpm.model.bpmn.instance.ExtensionElements;
import org.camunda.bpm.model.bpmn.instance.FlowElement;
import org.camunda.bpm.model.bpmn.instance.FlowNode;
import org.camunda.bpm.model.bpmn.instance.ParallelGateway;
import org.camunda.bpm.model.bpmn.instance.SequenceFlow;
import org.camunda.bpm.model.bpmn.instance.ServiceTask;
import org.camunda.bpm.model.bpmn.instance.StartEvent;
import org.camunda.bpm.model.bpmn.instance.UserTask;
import org.camunda.bpm.model.bpmn.instance.bpmndi.BpmnDiagram;
import org.camunda.bpm.model.bpmn.instance.bpmndi.BpmnPlane;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaTaskListener;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ClassUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.OptionalDouble;
import java.util.OptionalInt;
import java.util.OptionalLong;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.servlet.http.HttpServletRequest;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.*;
import cn.com.cloudstar.rightcloud.core.pojo.constant.ServiceProcessStatus;
import cn.com.cloudstar.rightcloud.core.pojo.dto.audit.ResourceInfo;
import cn.com.cloudstar.rightcloud.core.pojo.dto.audit.ServiceOrder;
import cn.com.cloudstar.rightcloud.core.pojo.dto.bss.BizCoupon;
import cn.com.cloudstar.rightcloud.core.pojo.dto.process.Process;
import cn.com.cloudstar.rightcloud.core.pojo.dto.process.*;
import cn.com.cloudstar.rightcloud.core.pojo.dto.process.ProcessNode;
import cn.com.cloudstar.rightcloud.core.pojo.dto.process.ProcessNodeAddDto;
import cn.com.cloudstar.rightcloud.core.pojo.dto.process.ProcessNodeConfig;
import cn.com.cloudstar.rightcloud.core.pojo.dto.process.ProcessNodeEditDto;
import cn.com.cloudstar.rightcloud.core.pojo.dto.process.ProcessNodeMessageDto;
import cn.com.cloudstar.rightcloud.core.pojo.dto.process.ProcessNodeRoleDto;
import cn.com.cloudstar.rightcloud.core.pojo.dto.process.ProcessVersion;
import cn.com.cloudstar.rightcloud.core.pojo.dto.process.ServiceProcess;
import cn.com.cloudstar.rightcloud.core.pojo.dto.process.SysProcessBusiness;
import cn.com.cloudstar.rightcloud.core.pojo.dto.st.ProcessTemplate;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.Company;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.Role;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.User;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.UserRole;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.oss.common.constants.RestConst;
import cn.com.cloudstar.rightcloud.oss.common.enums.NodeTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.ProcessTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.oss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.oss.common.mybatis.enums.RequirePermissionEnum;
import cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.oss.common.pojo.LicenseVo;
import cn.com.cloudstar.rightcloud.oss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.oss.common.pojo.RestResult.Status;
import cn.com.cloudstar.rightcloud.oss.common.util.LicenseUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.WebUserUtil;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.user.UserMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.service.role.RoleService;
import cn.com.cloudstar.rightcloud.oss.module.contract.service.BizContractService;
import cn.com.cloudstar.rightcloud.oss.module.coupon.bean.CashCoupon;
import cn.com.cloudstar.rightcloud.oss.module.coupon.dao.CashCouponMapper;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.DistributeCashCouponRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.RechargeBillingAccountRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.UpdateCreditLineRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.service.FeignService;
import cn.com.cloudstar.rightcloud.oss.module.maintenance.dao.templates.ProcessTemplateMapper;
import cn.com.cloudstar.rightcloud.oss.module.operationcore.activiti.util.DrawUtils;
import cn.com.cloudstar.rightcloud.oss.module.operationcore.activiti.util.IdGen;
import cn.com.cloudstar.rightcloud.oss.module.operationcore.activiti.util.ProcessConstants;
import cn.com.cloudstar.rightcloud.oss.module.operationcore.bean.process.request.ApproveProcessRequest;
import cn.com.cloudstar.rightcloud.oss.module.operationcore.bean.process.request.BindingProcessCreateRequest;
import cn.com.cloudstar.rightcloud.oss.module.operationcore.bean.process.request.BindingProcessUpdateRequest;
import cn.com.cloudstar.rightcloud.oss.module.operationcore.bean.process.request.ServiceProcessRequest;
import cn.com.cloudstar.rightcloud.oss.module.operationcore.dao.process.*;
import cn.com.cloudstar.rightcloud.oss.module.operationcore.service.process.ProcessMgtService;
import cn.com.cloudstar.rightcloud.oss.module.operationcore.service.process.ServiceProcessService;
import cn.com.cloudstar.rightcloud.oss.module.order.dao.BizCouponMapper;
import cn.com.cloudstar.rightcloud.oss.module.order.service.ServiceOrderService;
import cn.com.cloudstar.rightcloud.oss.module.pricing.dao.priceconfig.SfServiceCategoryMapper;
import cn.com.cloudstar.rightcloud.oss.module.pricing.service.priceconfig.BizBillingAccountService;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.bean.request.ApproveOrderRequest;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.dao.SfProductResourceMapper;
import cn.com.cloudstar.rightcloud.oss.module.system.service.config.SysConfigService;
import cn.com.cloudstar.rightcloud.oss.module.system.service.message.BusinessNotificationService;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.IdentityService;
import org.camunda.bpm.engine.RepositoryService;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.TaskService;
import org.camunda.bpm.engine.repository.Deployment;
import org.camunda.bpm.engine.repository.ProcessDefinition;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.*;
import org.camunda.bpm.model.bpmn.instance.bpmndi.BpmnDiagram;
import org.camunda.bpm.model.bpmn.instance.bpmndi.BpmnPlane;
import org.camunda.bpm.model.bpmn.instance.camunda.CamundaTaskListener;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ClassUtils;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 流程定义管理
 *
 * <AUTHOR>
 * @date 2018/8/8
 */
@Slf4j
@Component
public class ProcessMgtServiceImpl implements ProcessMgtService {

    @Autowired
    private ProcessMapper processMapper;

    @Autowired
    private ProcessVersionMapper processVersionMapper;

    @Autowired
    private ProcessNodeMapper processNodeMapper;

    private final Long MANGERROLEID = 301l;
    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private RoleService roleService;

    @Autowired
    private ProcessTemplateMapper processTemplateMapper;

    @Autowired
    private SysConfigService sysConfigService;

    @Autowired
    @Lazy
    private ServiceProcessService serviceProcessService;

    @Autowired
    private BindProcessMapper bindProcessMapper;

    @Autowired
    private SysProcessBusinessMapper sysProcessBusinessMapper;

    @Autowired
    private FeignService feignService;

    @Autowired
    @Lazy
    private ServiceOrderService serviceOrderService;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private BizBillingAccountService bizBillingAccountService;

    @Autowired
    private BusinessNotificationService businessNotificationService;

    @Autowired
    private SfProductResourceMapper sfProductResourceMapper;

    @Autowired
    private SfServiceCategoryMapper sfServiceCategoryMapper;

    @Autowired
    private BizContractService bizContractService;

    @Autowired
    private BizCouponMapper bizCouponMapper;

    @Autowired
    private CashCouponMapper cashCouponMapper;
    @Autowired
    private RuntimeService runtimeService;
    @Autowired
    private TaskService taskService;
    @Autowired
    private IdentityService identityService;

    @Autowired
    private UserMapper userMapper;

    static Map<String, String> BINDING_MAP = new HashMap<>();

    static List<Map<String, String>> BUSINESS = ProcessConstants.BUSINESS;
    static Map<String, String> BUSINESS_MAP = ProcessConstants.BUSINESS_MAP;

    /**
     * 流程状态 - 编辑未发布的状态
     */
    private static final Integer PROCESS_STATUS_EDITED = 1;

    /**
     * 流程状态 - 正常状态
     */
    private static final Integer PROCESS_STATUS_NORMAL = 0;

    /**
     * 多重审核开关
     */
    private static final String MULTIPLE_AUDITS_ENABLE = "multiple.audits.enable";

    /**
     * 开启
     */
    private static final String OPEN = "1";

    /**
     * 关闭
     */
    private static final String CLOSE = "0";

    /**
     * 内置
     */
    private static final String BUILT_IN = "built-in";

    /**
     * hpc共享资源池
     */
    private static final String HPC_SHARE = "hpc-share";

    /**
     * hpc专属资源池
     */
    private static final String HPC_DRP = "hpc-drp";

    /**
     * ma共享资源池
     */
    private static final String MA_SHARE = "ma-share";

    /**
     * ma专属资源池
     */
    private static final String MA_DRP = "ma-drp";

    /**
     * 现金充值
     */
    private static final String CASH_RECHARGE = "cash-recharge";

    /**
     * 额度调整
     */
    private static final String QUOTA_ADJUSTMENT = "quota-adjustment";

    /**
     * 优惠券分发
     */
    private static final String COUPONS_DISTAIBUTE = "coupons-distribute";

    /**
     * 充值现金券分发
     */
    private static final String CASH_COUPON_DISTRIBUTE = "cashCoupon-distribute-deposit";
    /**
     * 抵扣现金券分发
     */
    private static final String CASH_COUPON_DISTRIBUTE_DEDUCT = "cashCoupon-distribute-deduct";

    /**
     * 执行中
     */
    private static final String EXECUTING = "executing";

    /**
     * 已完成
     */
    private static final String SUCCEED = "succeed";

    /**
     * 已拒绝
     */
    private static final String REFUSE = "refuse";

    /**
     * 通过
     */
    private static final String PASS = "已通过";

    /**
     * 驳回
     */
    private static final String REJECT = "已驳回";

    /**
     * 拒绝
     */
    private static final String BLOCK = "已拒绝";



    static {
        BUSINESS.add(ImmutableMap.of("processCode", "service-apply", "businessName", "资源手动审批"));
        BUSINESS.add(ImmutableMap.of("processCode", "service-changegrade", "businessName", "服务变更"));
        BUSINESS.add(ImmutableMap.of("processCode", "service-renew", "businessName", "服务续订"));
        BUSINESS.add(ImmutableMap.of("processCode", "service-release", "businessName", "服务退订"));
        BUSINESS.add(ImmutableMap.of("processCode", "service-execScript", "businessName", "执行脚本"));
        BUSINESS.add(ImmutableMap.of("processCode", "ticket", "businessName", "提交工单"));
        BUSINESS.add(ImmutableMap.of("processCode", "service-publish", "businessName", "服务发布"));
        BUSINESS.add(ImmutableMap.of("processCode", "other_service-apply", "businessName", "第三方服务开通"));
        BUSINESS.add(ImmutableMap.of("processCode", "hpc-service-apply", "businessName", "HPC服务开通"));
        BUSINESS.add(ImmutableMap.of("processCode", "hpc-drp-service-apply", "businessName", "HPC专属资源池服务开通"));
        BUSINESS.add(ImmutableMap.of("processCode", "hpc-drp-service-upgrade", "businessName", "HPC专属资源池服务扩容"));
        BUSINESS.add(ImmutableMap.of("processCode", "ma-drp-service-apply", "businessName", "MA专属资源池服务开通"));
        BUSINESS.add(ImmutableMap.of("processCode", "product-process", "businessName", "产品流程"));
        BUSINESS.add(ImmutableMap.of("processCode", "billing-process", "businessName", "计费账单流程"));
        BUSINESS.add(ImmutableMap.of("processCode", "contract-process", "businessName", "合同流程"));
        BUSINESS.add(ImmutableMap.of("processCode", "customer-management-process", "businessName", "客户管理流程"));
        BUSINESS.add(ImmutableMap.of("processCode", "ma-drp-service-upgrade", "businessName", "MA专属资源池扩容"));
        BUSINESS.add(ImmutableMap.of("processCode", "ma-service-release", "businessName", "MA专属资源池退订"));
        BUSINESS.add(ImmutableMap.of("processCode", "ma-drp-service-degrade", "businessName", "MA专属资源池缩容"));
        BUSINESS.add(ImmutableMap.of("processCode", "ma-drp-service-release", "businessName", "MA专属资源池退订"));
        BUSINESS.add(ImmutableMap.of("processCode", "ma-logical-drp-service-release", "businessName", "MA专属资源池退订"));
        BUSINESS.add(ImmutableMap.of("processCode", "quota-process", "businessName", "存储配额流程"));
        BUSINESS.add(ImmutableMap.of("processCode", HPC_SHARE, "businessName", "hpc共享资源池"));
        BUSINESS.add(ImmutableMap.of("processCode", HPC_DRP, "businessName", "hpc专属资源池"));
        BUSINESS.add(ImmutableMap.of("processCode", MA_SHARE, "businessName", "ma共享资源池"));
        BUSINESS.add(ImmutableMap.of("processCode", MA_DRP, "businessName", "ma专属资源池"));
        BUSINESS.add(ImmutableMap.of("processCode", CASH_RECHARGE, "businessName", "现金充值"));
        BUSINESS.add(ImmutableMap.of("processCode", QUOTA_ADJUSTMENT, "businessName", "信用额度调整"));
        BUSINESS.add(ImmutableMap.of("processCode", COUPONS_DISTAIBUTE, "businessName", "优惠券分发"));
        BUSINESS.add(ImmutableMap.of("processCode", CASH_COUPON_DISTRIBUTE, "businessName", "充值现金券分发"));
        BUSINESS.add(ImmutableMap.of("processCode", CASH_COUPON_DISTRIBUTE_DEDUCT, "businessName", "抵扣现金券分发"));
        BUSINESS.add(ImmutableMap.of("processCode", "auto-approval", "businessName", "资源自动审批"));
        BUSINESS.add(ImmutableMap.of("processCode", BUILT_IN, "businessName", "内置"));
        for (Map<String, String> business : BUSINESS) {
            BUSINESS_MAP.put(business.get("processCode"), business.get("businessName"));
        }
        BINDING_MAP.put("hpc共享资源池", HPC_SHARE);
        BINDING_MAP.put("hpc专属资源池", HPC_DRP);
        BINDING_MAP.put("ma共享资源池", MA_SHARE);
        BINDING_MAP.put("ma专属资源池", MA_DRP);
        BINDING_MAP.put("现金充值", CASH_RECHARGE);
        BINDING_MAP.put("信用额度调整", QUOTA_ADJUSTMENT);
        BINDING_MAP.put("优惠券分发", COUPONS_DISTAIBUTE);
        BINDING_MAP.put("充值现金券分发", CASH_COUPON_DISTRIBUTE);
        BINDING_MAP.put("抵扣现金券分发",CASH_COUPON_DISTRIBUTE_DEDUCT);
    }



    /**
     * 验证并创建
     *
     * @param request 要求
     *
     * @return {@code RestResult}
     */
    @Override
    public RestResult verifyAndCreate(Object... request) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (ObjectUtils.isEmpty(authUserInfo)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_531007114));
        }
        String flag = null;
        SysProcessBusiness sysProcessBusiness = null;
        Object convertRequest = null;
        String targetSt = null;
        String targetNd = null;
        // 添加日志基础属性
        HttpServletRequest httpServletRequest = SpringContextHolder.getHttpServletRequest();
        String userClient = httpServletRequest.getHeader("User-Agent");
        String remoteIp = httpServletRequest.getHeader("RequstIp");
        if (StringUtil.isEmpty(remoteIp)) {
            remoteIp = IPAddressUtil.getRemoteHostIp(httpServletRequest);
        }
        // 判断是否开启多重审批
        String configValue = sysConfigService.getValueByConfigKey(MULTIPLE_AUDITS_ENABLE);
        try {
            if (!OPEN.equals(configValue)) {
                // 双重审核未开启，走原来代码逻辑
                return callSource(request);
            }
            Criteria criteria = new Criteria();
            Long entityId = authUserInfo.getEntityId();
            criteria.put("status", ServiceProcessStatus.PROCESS);
            // 流程审批完成，部分业务直接执行
            if (request.length == 1 && request[0] instanceof ApproveProcessRequest) {
                ApproveProcessRequest approveProcessRequest = BeanConvertUtil.convert(request[0], ApproveProcessRequest.class);
                String approveAdvice = approveProcessRequest.getApproveAdvice();
                ServiceProcess serviceProcess = serviceProcessService.queryById(approveProcessRequest.getServiceProcessId());
                sysProcessBusiness = sysProcessBusinessMapper.selectByProcessId(approveProcessRequest.getServiceProcessId());
                criteria.put("id", approveProcessRequest.getServiceProcessId());
                flag = sysProcessBusiness.getBusinessCode();
                switch (approveProcessRequest.getApproveType()) {
                    case "01":
                        log.info("单审批节点流程审批通过");
                        if (ServiceProcessStatus.COMPLETE.equals(serviceProcess.getStatus())) {
                            criteria.put("status", ServiceProcessStatus.COMPLETE);
                            if (ObjectUtils.isEmpty(sysProcessBusiness)) {
                                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_630090301));
                            }
                            sysProcessBusiness.setStatus(SUCCEED);
                            sysProcessBusinessMapper.update(sysProcessBusiness);
                            break;
                        } else {
                            log.info("多审批节点流程审批通过");
                            // 流程审批存在多个审批节点，前一个审批节点审批完后给下一个审批节点审批人发消息
                            businessNotificationService.processPendingApprovalReminder(BUSINESS_MAP.get(flag),
                                                                                       approveProcessRequest.getServiceProcessId(), entityId);
                            break;
                        }
                    case "02":
                        log.info("流程审批驳回(仅限多节点审批)");
                        businessNotificationService.processApprovalResultNotificationToOpera(BUSINESS_MAP.get(sysProcessBusiness.getBusinessCode()),
                                                                                      REJECT, sysProcessBusiness,approveAdvice,entityId);
                        log.info("重新给上节点审批者发送消息");
                        businessNotificationService.processPendingApprovalReminder(BUSINESS_MAP.get(flag),
                                                                                   approveProcessRequest.getServiceProcessId(), entityId);
                        return null;
                    case "03":
                        log.info("流程审批拒绝");
                        criteria.put("status", ServiceProcessStatus.REJECTED);
                        sysProcessBusiness = sysProcessBusinessMapper.selectByProcessId(approveProcessRequest.getServiceProcessId());
                        if (ObjectUtils.isEmpty(sysProcessBusiness)) {
                            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_630090301));
                        }
                        sysProcessBusiness.setStatus(REFUSE);
                        sysProcessBusinessMapper.update(sysProcessBusiness);
                        break;
                    default:
                        break;
                }
            }
            else if (request.length == 1 && request[0] instanceof RechargeBillingAccountRequest) {
                log.info("进入现金充值逻辑");
                targetSt = ((RechargeBillingAccountRequest) request[0]).getId().toString();
                flag = CASH_RECHARGE;
            }
            else if (request.length == 1 && request[0] instanceof UpdateCreditLineRequest) {
                log.info("进入信用额度逻辑");
                targetSt = ((UpdateCreditLineRequest) request[0]).getAccountId().toString();
                flag = QUOTA_ADJUSTMENT;
            }
            else if (request.length == 3 && request[0] instanceof Long && request[1] instanceof List) {
                log.info("进入分发优惠券逻辑");
                targetSt = request[0].toString();
                targetNd = ((List<Long>) request[1]).stream().map(String::valueOf).collect(Collectors.joining(","));
                flag = COUPONS_DISTAIBUTE;
            }
            else if (request.length == 1 && request[0] instanceof DistributeCashCouponRequest) {
                log.info("进入分发现金券逻辑");
                targetSt = String.join(",", ((DistributeCashCouponRequest) request[0]).getCouponNo());
                if (targetSt.contains("CZ")){
                    flag = CASH_COUPON_DISTRIBUTE;
                }else if (targetSt.contains("DK")){
                 flag  = CASH_COUPON_DISTRIBUTE_DEDUCT;
                }
            }
            else if (request.length == 1 && request[0] instanceof Long) {
                log.info("进入开通/续订/退订逻辑");
                targetSt = request[0].toString();
                // 查询订单详情，区分具体资源池类型及操作类型
                ServiceOrder serviceOrder = serviceOrderService.selectByPrimaryKey((Long) request[0]);
                entityId = serviceOrder.getEntityId();
                flag = getFlag(serviceOrder);
            }
            else if (request.length == 1 && request[0] instanceof ApproveOrderRequest) {
                targetSt = ((ApproveOrderRequest) request[0]).getId().toString();
                ServiceOrder serviceOrder = serviceOrderService.selectByPrimaryKey(((ApproveOrderRequest) request[0]).getId());
                flag = getFlag(serviceOrder);
            }
            criteria.put("targetSt", targetSt);
            criteria.put("targetNd", targetNd);
            criteria.put("type", flag);
            log.info("校验当前数据是否已存在的查询条件----->[{}]", JSON.toJSONString(criteria));
            // 获取已绑定业务流程
            List<BindingProcess> bindingProcessList = bindProcessMapper.selectByBusinessCode(flag);
            // 双重审核开启但未绑定业务流程，走原代码逻辑
            if (CollectionUtils.isEmpty(bindingProcessList)) {
                return callSource(request);
            }
            BindingProcess bindingProcess = bindingProcessList.get(0);
            if (serviceProcessService.getCountByCriteria(criteria) < 1 && !(request[0] instanceof ApproveProcessRequest)){
                if (request[0] instanceof ApproveOrderRequest) {
                    return new RestResult();
                }
                log.info("当前资源还未创建流程审批，开始创建审批流程");
                ServiceProcessRequest serviceProcessRequest = new ServiceProcessRequest();
                serviceProcessRequest.setType(flag);
                serviceProcessRequest.setName(bindingProcess.getProcessName());

                log.info("ProcessMgtServiceImpl-verifyAndCreate-selectProcess-entityId:" + entityId);
                Process process = processMapper.selectByProcessName(bindingProcess.getProcessName(), entityId);
                serviceProcessRequest.setProcessId(process.getId());
                serviceProcessRequest.setTemplateId(null);
                serviceProcessRequest.setTemplateContent("{\\\"panelDataList\\\":[],\\\"configs\\\":null}");
                ServiceProcess serviceProcess = BeanConvertUtil.convert(serviceProcessRequest, ServiceProcess.class);
                serviceProcess.setEntryId(entityId);
                Object result = serviceProcessService.create(serviceProcess, serviceProcessRequest);
                if (!(result instanceof Long)) {
                    return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERROR_INSERT_FAILURE));
                }
                // 记录业务相关数据
                log.info("记录业务数据");
                String attrData;
                if (request.length == 1 && request[0] instanceof RechargeBillingAccountRequest) {
                    RechargeBillingAccountRequest rechargeBillingAccountRequest = BeanConvertUtil.convert(request[0], RechargeBillingAccountRequest.class);
                    rechargeBillingAccountRequest.setAccountName(bizBillingAccountService.selectById(rechargeBillingAccountRequest.getId()).getAccountName());
                    attrData = JSONObject.toJSONString(rechargeBillingAccountRequest);
                } else if (request.length == 1 && request[0] instanceof UpdateCreditLineRequest) {
                    UpdateCreditLineRequest updateCreditLineRequest = BeanConvertUtil.convert(request[0], UpdateCreditLineRequest.class);
                    updateCreditLineRequest.setAccountName(bizBillingAccountService.selectById(updateCreditLineRequest.getAccountId()).getAccountName());
                    if (!ObjectUtils.isEmpty(updateCreditLineRequest.getContractSid())){
                        updateCreditLineRequest.setContractName(bizContractService.selectByPrimaryKey(Long.valueOf(updateCreditLineRequest.getContractSid())).getContractTitle());
                    }
                    attrData = JSONObject.toJSONString(updateCreditLineRequest);
                } else if (request.length == 3 && request[0] instanceof Long && request[1] instanceof List && request[2] instanceof String) {
                    AttrData data = new AttrData();
                    BizCoupon bizCoupon = bizCouponMapper.selectByPrimaryKey((Long) request[0]);
                    data.setCoupongSn(bizCoupon.getCouponNo());
                    data.setStartTime(bizCoupon.getStartTime());
                    data.setEndTime(bizCoupon.getEndTime());
                    data.setDiscountAmount(bizCoupon.getDiscountAmount());
                    data.setReductionCondition(bizCoupon.getReductionCondition());
                    data.setRemark((String) request[2]);
                    List<String> accountName = new ArrayList<>();
                    ((List<Long>) request[1]).forEach(accountId->{
                        accountName.add(bizBillingAccountService.selectById(accountId).getAccountName());
                    });
                    data.setAccountNames(accountName);
                    attrData = JSONObject.toJSONString(data);
                } else if (request.length == 1 && request[0] instanceof DistributeCashCouponRequest) {
                    DistributeCashCouponRequest distributeCashCouponRequest = BeanConvertUtil.convert(request[0], DistributeCashCouponRequest.class);
                    List<String> accountName = new ArrayList<>();
                    distributeCashCouponRequest.getAccountId().forEach(accountId->{
                        accountName.add(bizBillingAccountService.selectById(accountId).getAccountName());
                    });
                    distributeCashCouponRequest.setAccountNames(accountName);
                    List<BigDecimal> amout = new ArrayList<>();
                    List<String> productScope = new ArrayList<>();
                    distributeCashCouponRequest.getCouponNo().forEach(no->{
                        CashCoupon cashCoupon = cashCouponMapper.selectTeam(no);
                        // 更新状态
                        CashCoupon cashCoupon1 = new CashCoupon();
                        cashCoupon1.setStatus("approvaling");
                        cashCoupon1.setCouponNo(no);
                        cashCouponMapper.updateCash(cashCoupon1);
                        amout.add(cashCoupon.getAmount());
                        if (!ObjectUtils.isEmpty(cashCoupon.getProductScope())){
                            productScope.add(cashCoupon.getProductScope());
                        }
                        distributeCashCouponRequest.setCouponName(cashCoupon.getCouponName());
                        distributeCashCouponRequest.setStartTime(cashCoupon.getStartTime());
                        distributeCashCouponRequest.setEndTime(cashCoupon.getEndTime());
                    });
                    distributeCashCouponRequest.setAmout(amout);
                    distributeCashCouponRequest.setProductScope(productScope);
                    attrData = JSONObject.toJSONString(distributeCashCouponRequest);
                } else {
                    attrData = JSONObject.toJSONString(request[0]);
                }
                SysProcessBusiness sysProcessBusinessInsert =
                        SysProcessBusiness.builder().processId((Long) result).processName(bindingProcess.getProcessName()).businessCode(
                                                  bindingProcess.getBusinessCode()).attrData(attrData)
                                          .submitUserSid(authUserInfo.getUserSid()).targetSt(targetSt).targetNd(targetNd)
                                          .status(EXECUTING).build();
                WebUserUtil.prepareInsertParams(sysProcessBusinessInsert);
                sysProcessBusinessMapper.insert(sysProcessBusinessInsert);
                businessNotificationService.processPendingApprovalReminder(BUSINESS_MAP.get(flag), (Long) result, entityId);
                switch (flag) {
                    case CASH_RECHARGE:
                    case QUOTA_ADJUSTMENT:
                    case COUPONS_DISTAIBUTE:
                    case CASH_COUPON_DISTRIBUTE:
                    case CASH_COUPON_DISTRIBUTE_DEDUCT:
                        return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1993777303) + result);
                    default:
                        return null;
                }
            }
            else {
                if (!(request.length == 1 && request[0] instanceof ApproveProcessRequest)) {
                    return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1082716371) + serviceProcessService.getByCriteria(criteria));
                }
                ApproveProcessRequest approveProcessRequest = BeanConvertUtil.convert(request[0], ApproveProcessRequest.class);
                String approveAdvice = approveProcessRequest.getApproveAdvice();
                String attrData = sysProcessBusiness.getAttrData();
                if ("01".equals(((ApproveProcessRequest) request[0]).getApproveType())) {
                    log.info("审批通过，开始执行对应方法");
                    RestResult restResult = new RestResult(Status.FAILURE);
                    switch (sysProcessBusiness.getBusinessCode()) {
                        case CASH_RECHARGE:
                            convertRequest = (Object) JSON.parseObject(attrData, RechargeBillingAccountRequest.class);
                            restResult = callSource(convertRequest);
                            break;
                        case QUOTA_ADJUSTMENT:
                            convertRequest = (Object) JSON.parseObject(attrData, UpdateCreditLineRequest.class);
                            restResult = callSource(convertRequest);
                            break;
                        case COUPONS_DISTAIBUTE:
                            restResult =  callSource(Long.valueOf(sysProcessBusiness.getTargetSt()),
                                                     Arrays.asList(sysProcessBusiness.getTargetNd().split(",")).stream().map(Long::valueOf)
                                                           .collect(Collectors.toList()));
                            break;
                        case CASH_COUPON_DISTRIBUTE_DEDUCT:
                        case CASH_COUPON_DISTRIBUTE:
                            convertRequest = (Object) JSON.parseObject(attrData, DistributeCashCouponRequest.class);
                            restResult = callSource(convertRequest);
                            break;
                        case MA_SHARE:
                        case MA_DRP:
                        case HPC_SHARE:
                        case HPC_DRP:
                            restResult = new RestResult(Status.SUCCESS,WebUtil.getMessage(MsgCd.RESOURCE_PROCESS_AUDIT_PASS));
                            break;
                        default:
                            break;
                    }
                    log.info("发送审批成功消息获取的restResult:{}",restResult);
                    if (restResult.getStatus() && !"资源类审批已通过".equals(restResult.getMessage())) {
                        businessNotificationService.processApprovalResultNotification(
                                BUSINESS_MAP.get(sysProcessBusiness.getBusinessCode()),
                                PASS, sysProcessBusiness,approveAdvice,entityId);
                    }
                    if (restResult.getStatus() && "资源类审批已通过".equals(restResult.getMessage())){
                        businessNotificationService.processApprovalResultNotificationToOpera(
                                BUSINESS_MAP.get(sysProcessBusiness.getBusinessCode()),
                                PASS, sysProcessBusiness,approveAdvice,entityId);
                    }
                }
                else if ("03".equals(((ApproveProcessRequest) request[0]).getApproveType())) {
                    log.info("审批拒绝，开始执行对应方法");
                    RestResult restResult = new RestResult(Status.FAILURE);
                    switch (sysProcessBusiness.getBusinessCode()) {
                        case CASH_COUPON_DISTRIBUTE:
                        case CASH_COUPON_DISTRIBUTE_DEDUCT:
                            Arrays.asList(sysProcessBusiness.getTargetSt().split(",")).forEach(no -> {
                                CashCoupon cashCoupon = new CashCoupon();
                                cashCoupon.setStatus("undistributed");
                                cashCoupon.setCouponNo(no);
                                cashCouponMapper.updateCash(cashCoupon);
                            });
                            break;
                        case MA_SHARE:
                        case MA_DRP:
                        case HPC_SHARE:
                        case HPC_DRP:
                            ServiceOrder serviceOrder =
                                    serviceOrderService.selectByPrimaryKey(Long.valueOf(sysProcessBusiness.getTargetSt()));
                            if (ObjectUtils.isEmpty(serviceOrder)) {
                                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1570221985));
                            }
                            ApproveOrderRequest approveOrderRequest = new ApproveOrderRequest();
                            approveOrderRequest.setId(serviceOrder.getId());
                            approveOrderRequest.setOrderId(serviceOrder.getId());
                            approveOrderRequest.setOrderSn(serviceOrder.getOrderSn());
                            approveOrderRequest.setUserSid(authUserInfo.getUserSid().toString());
                            approveOrderRequest.setStepName(serviceOrder.getStepName());
                            approveOrderRequest.setApproveType("03");
                            approveOrderRequest.setApproveAdvice("流程审核拒绝同步完成申请单拒绝");
                            approveOrderRequest.setVerify(false);
                            ResourceInfo resourceInfo = new ResourceInfo();
                            resourceInfo.setInfo("");
                            resourceInfo.setAttachments(new ArrayList<>());
                            approveOrderRequest.setResourceInfo(resourceInfo);
                            convertRequest = (Object) approveOrderRequest;
                            restResult = callSource(convertRequest);
                            break;
                        default:
                            break;
                    }
                    if (restResult.getStatus()) {
                        log.info("发送审批拒绝消息");
                        businessNotificationService.processApprovalResultNotificationToOpera(
                                BUSINESS_MAP.get(sysProcessBusiness.getBusinessCode()),
                                BLOCK, sysProcessBusiness,approveAdvice,entityId);
                    }
                }
            }
        } catch (BizException e) {
            throw e;
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new BizException(RestConst.BizError.BIZ_ERROR, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1070996688));
        }
        System.out.println("------------------------------完成------------------------------");
        return null;
    }

    /**
     * 获取标志
     *
     * @param serviceOrder 服务订单
     *
     * @return {@code String}
     */
    private String getFlag(ServiceOrder serviceOrder) {
        ServiceOrder parentServiceOrder = serviceOrderService.selectByOrderSn(serviceOrder.getOrderSourceSn());
        String serviceType = sfServiceCategoryMapper.getById(Long.valueOf(parentServiceOrder.getServiceId()));
        // ma共享
        if (ProductCodeEnum.MODEL_ARTS.getProductCode().equalsIgnoreCase(serviceType)) {
            return MA_SHARE;
            // ma专属
        } else if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode().equalsIgnoreCase(serviceType)) {
            return MA_DRP;
            // hpc共享一期
        } else if (ProductCodeEnum.HPC.getProductCode().equalsIgnoreCase(serviceType)) {
            return HPC_SHARE;
            // hpc共享二期
        } else if (ProductCodeEnum.HPC_SAAS.getProductCode().equalsIgnoreCase(serviceType)) {
            return HPC_SHARE;
            // hpc专属二期
        } else if (ProductCodeEnum.HPC_DRP.getProductCode().equalsIgnoreCase(serviceType)) {
            return HPC_DRP;
        }
        return "";
    }

    /**
     * 调用业务逻辑
     *
     * @param request 要求
     *
     * @return {@code RestResult}
     */
    private RestResult callSource(Object... request) {
        boolean coupons = request.length == 2 || request.length == 3;
        if (request.length == 1 && request[0] instanceof RechargeBillingAccountRequest) {
            RechargeBillingAccountRequest rechargeBillingAccountRequest = BeanConvertUtil.convert(request[0], RechargeBillingAccountRequest.class);
            log.info("执行充值现金方法");
            return feignService.recharge(rechargeBillingAccountRequest);
        } else if (request.length == 1 && request[0] instanceof UpdateCreditLineRequest) {
            UpdateCreditLineRequest updateCreditLineRequest = BeanConvertUtil.convert(request[0], UpdateCreditLineRequest.class);
            log.info("执行调整信余额方法");
            return feignService.creditLine(updateCreditLineRequest);
        } else if (coupons && request[0] instanceof Long && request[1] instanceof List) {
            Long sid = (Long) request[0];
            List<Long> accountIds = (List<Long>) request[1];
            log.info("执行分发优惠券方法");
            return feignService.distributeCoupon(sid, accountIds);
        } else if (request.length == 1 && request[0] instanceof DistributeCashCouponRequest) {
            DistributeCashCouponRequest distributeCashCouponRequest = BeanConvertUtil.convert(request[0], DistributeCashCouponRequest.class);
            log.info("执行分发现金券方法");
            return feignService.distributeCashCoupon(distributeCashCouponRequest);
        } else if (request.length == 1 && request[0] instanceof ApproveOrderRequest) {
            ApproveOrderRequest approveOrderRequest = BeanConvertUtil.convert(request[0], ApproveOrderRequest.class);
            if ("03".equals(approveOrderRequest.getApproveType()) && "流程审核拒绝同步完成申请单拒绝".equals(
                    approveOrderRequest.getApproveAdvice())) {
                approveOrderRequest.setVerify(false);
                log.info("执行拒绝审批方法");
                return serviceOrderService.approvel(approveOrderRequest);
            }
        }
        return null;
    }

    @Override
    public int insertBindingProcess(BindingProcessCreateRequest bindingProcessRequest) {
        if (bindProcessMapper.selectByBusinessName(bindingProcessRequest.getBusinessName()) > 1) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_473997209));
        }
        BindingProcess bindingProcess = BeanConvertUtil.convert(bindingProcessRequest, BindingProcess.class);
        bindingProcess.setBusinessCode(BINDING_MAP.get(bindingProcess.getBusinessName()));
        if (ObjectUtils.isEmpty(bindingProcess.getBusinessCode())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1149454407));
        }
        WebUserUtil.prepareInsertParams(bindingProcess);
        return bindProcessMapper.insert(bindingProcess);
    }

    @Override
    public int updateBindingProcess(BindingProcessUpdateRequest bindingProcessRequest) {
        BindingProcess bindingProcess = BeanConvertUtil.convert(bindingProcessRequest, BindingProcess.class);
        bindingProcess.setBusinessCode(BINDING_MAP.get(bindingProcess.getBusinessName()));
        if (ObjectUtils.isEmpty(bindingProcess.getBusinessCode())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1149454407));
        }
        return bindProcessMapper.update(bindingProcess);
    }

    @Override
    public List<BindingProcess> selectBindingProcess() {
        List<BindingProcess> result = bindProcessMapper.select();
        result.forEach(process -> {
            process.setProcessName(ProcessTypeEnum.codeFromName(process.getProcessName()));
            process.setBusinessName(ProcessTypeEnum.codeFromName(process.getBusinessName()));
        });
        return result;
    }

    @Override
    public void deleteBindingProcess(Long id) {
        SysProcessBusiness processBusiness = bindProcessMapper.selectById(id);
        Assert.notNull(processBusiness,WebUtil.getMessage(MsgCd.RES_PARAM_STATUS_ERROR));
        bindProcessMapper.deleteById(id);
    }

    @Override
    public SysProcessBusiness getProcessBusiness(Long id) {
        return sysProcessBusinessMapper.selectByProcessId(id);
    }

    /**
     * 判断内置流程是否有候选用户
     * @return
     */
    @Override
    public boolean checkBuildInProgressCandidate() {
        List<String> filter = new ArrayList<>();
        // 标准版不包含hpc
        LicenseVo licenseVo = LicenseUtil.queryLicenseInfoFromDb();
        if (Objects.nonNull(licenseVo)) {
            String versionType = licenseVo.getVersionType();
            if (LicenseUtil.STAND.equals(versionType)) {
                filter.addAll(Arrays.asList("双重审核流程-资源审批-hpc", "双重审核流程-非资源审批-hpc"));
            }
        }
        List<Process> processes = processMapper.selectByCode("built-in");
        List<Process> collect = processes.stream().filter(e -> !filter.contains(e.getProcessName())).collect(Collectors.collectingAndThen(
            Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getProcessName()))), ArrayList::new));

        if (CollectionUtil.isEmpty(collect)) {
            return false;
        }
        AtomicBoolean flag = new AtomicBoolean(false);
        //存在流程未发布
        if (collect.stream().anyMatch(pro -> Integer.valueOf("1").equals(pro.getStatus()))) {
            return false;
        }

        List<ProcessNode> allNodeList = new ArrayList<>();
        for (Process process : collect) {
            List<ProcessVersion> processVersions = this.versionList(process.getId());
            if (processVersions.size() < 1) {
                return false;
            }
            ProcessVersion processVersion = processVersions.get(0);
            List<ProcessNode> processNodes = this.auditNodeList(processVersion.getId());
            //流程节点为空
            if (CollectionUtil.isEmpty(processNodes)) {
                return false;
            }
            allNodeList.addAll(processNodes);
        }

        for (ProcessNode node : allNodeList) {
            ProcessNodeConfig config = JSON.parseObject(node.getConfigData(), ProcessNodeConfig.class);
            if (CollectionUtil.isEmpty(config.getCandidateThirds())) {
                return false;
            }

            List<Long> refIds = config.getCandidateThirds().stream()
                                          .filter(c -> Objects.nonNull(c) && Objects.nonNull(c.getRefId()))
                                          .map(e -> Long.valueOf(e.getRefId()))
                                          .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(refIds)) {
                return false;
            }
            if (userMapper.selectBatchUser(refIds).size() != refIds.size()) {
                return false;
            }
        }
        return true;
    }

    @Override
    public List<Process> processList() {
        List<Process> processes = processMapper.selectAll();
        return processes;
    }

    @Override
    public List<Process> selectProcessByParams(Process process) {
        List<String> businessCodesOr = new ArrayList<>();
        businessCodesOr.add("service-apply");
        businessCodesOr.add("auto-approval");
        String businessName = process.getBusinessName();
        if (StringUtils.isNotBlank(businessName)) {
            List<String> businessCodes = new ArrayList<>();
            // 通过服务名称找到服务code如果未找到直接返回空数据
            BUSINESS_MAP.forEach((key, value) -> {
                if (value.contains(businessName)) {
                    businessCodes.add(key);
                }
            });
            if (businessCodes.isEmpty()) {
                return Lists.newArrayList();
            }
            process.setBusinessCodes(businessCodes);
        }
        String processName = process.getProcessName();
        if (StringUtils.isEmpty(processName) && StringUtils.isEmpty(businessName)) {
            process.setBusinessCodesOr(businessCodesOr);
        }
        
        
        if (Objects.isNull(process.getOrderByClause())){
            process.setOrderByClause("created_dt desc");
        }
        List<Process> processes = processMapper.selectByParams(process);
        return processes;
    }

    @Override
    public Process selectByPrimaryKey(Long id) {
        return processMapper.selectByPrimaryKey(id);
    }


    @Override
    public List<ProcessVersion> versionList(Long processId) {
        List<ProcessVersion> processVersions = processVersionMapper.selectByProcessId(processId);
        return processVersions;
    }


    @Override
    public List<ProcessVersion> versionList(List<Long> processIds) {
        return processVersionMapper.selectByProcessIds(processIds);
    }

    @Override
    public ProcessVersion selectVersionByPrimaryKey(Long id) {
        return processVersionMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<ProcessNode> nodeList(Long versionId) {
        List<ProcessNode> processNodes = processNodeMapper.selectByVersionId(versionId);
        return processNodes;
    }

    @Override
    public List<ProcessNode> auditNodeList(Long versionId) {
        return processNodeMapper.selectAuditNodeByVersionId(versionId);
    }

    @Override
    public ProcessNode selectNodeByPrimaryKey(Long id) {
        return processNodeMapper.selectByPrimaryKey(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initProcessDefine() {

        processMapper.clear();
        processVersionMapper.clear();
        processNodeMapper.clear();

        List<ProcessDefinition> processDefinitions = repositoryService
                .createProcessDefinitionQuery().orderByProcessDefinitionVersion().asc().list();

        Map<String, ProcessDefinition> map = Maps.newHashMap();

        if (processDefinitions != null && processDefinitions.size() > 0) {
            for (ProcessDefinition pd : processDefinitions) {
                map.put(pd.getKey(), pd);
            }
        }
        List<ProcessDefinition> pdList = new ArrayList<>(map.values());

        pdList.stream().forEach(pd -> {
            Process p = new Process();
            p.setProcessName(pd.getName());
            p.setProcessCode(pd.getKey());
            p.setDescription(pd.getName());
            p.setCreatedBy("admin");
            p.setCreatedDt(DateTime.now().toDate());
            p.setUpdatedBy("admin");
            p.setUpdatedDt(DateTime.now().toDate());
            processMapper.insertSelective(p);

            ProcessVersion pv = new ProcessVersion();
            pv.setVersionName(pd.getName());
            pv.setProcessId(p.getId());
            pv.setProcessIdentify(pd.getId());
            pv.setDeploymentId(pd.getDeploymentId());
            pv.setDescription(pd.getName());
            pv.setVersion(1);
            pv.setIsdefault(true);
            pv.setStatus(1);
            pv.setCreatedBy("admin");
            pv.setCreatedDt(DateTime.now().toDate());
            pv.setUpdatedBy("admin");
            pv.setUpdatedDt(DateTime.now().toDate());
            processVersionMapper.insertSelective(pv);

            BpmnModelInstance modelInstance = repositoryService.getBpmnModelInstance(pd.getId());


            if (modelInstance != null) {
                Collection<FlowElement> flowElements = modelInstance.getModelElementsByType(FlowElement.class);

                List<FlowElement> acts = flowElements.stream()
                                                     .filter(e -> !"SequenceFlow".equals(
                                                             ClassUtils.getUserClass(e).getSimpleName())
                                                             && !"ExclusiveGateway".equals(
                                                             ClassUtils.getUserClass(e).getSimpleName()))
                                                     .collect(Collectors.toList());

                acts.sort((o1, o2) -> {
                    int val1 = values.get(ClassUtils.getUserClass(o1).getSimpleName());
                    int val2 = values.get(ClassUtils.getUserClass(o2).getSimpleName());
                    return val1 - val2;
                });

                List<ProcessNode> nodes = acts.stream().map(e -> {
                    ProcessNode pn = new ProcessNode();

                    int typeValue = values.get(ClassUtils.getUserClass(e).getSimpleName());
                    NodeTypeEnum typeEnum = NodeTypeEnum.getEnumByType(typeValue);
                    if (StringUtils.isNotBlank(e.getName())) {
                        pn.setNodeName(e.getName());
                    } else {
                        pn.setNodeName(typeEnum.getValue());
                    }
                    pn.setNodeType(typeEnum.getType());
                    pn.setVersionId(pv.getId());
                    pn.setProcessId(pv.getProcessId());
                    pn.setDescription(pn.getNodeName());
                    pn.setConfigData("{}");
                    pn.setCreatedBy("admin");
                    pn.setCreatedDt(DateTime.now().toDate());
                    pn.setUpdatedBy("admin");
                    pn.setUpdatedDt(DateTime.now().toDate());
                    processNodeMapper.insertSelective(pn);

                    return pn;
                }).collect(Collectors.toList());
            }
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createProcessDefine(Process process, AuthUser authUser) {

        // 检查流程定义是重名
        Long entryId = RequestContextUtil.getEntityId();
        Process existsed = processMapper.selectByProcessName(process.getProcessName(), entryId);
        if (Objects.nonNull(existsed)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1149419381));
        }
        IdGen idGen = IdGen.get();

        process.setProcessCode("approval-" + idGen.nextId());
        process.setEntryId(entryId);
        process.setCreatedBy(authUser.getAccount());
        process.setCreatedDt(DateTime.now().toDate());
        process.setUpdatedBy(authUser.getAccount());
        process.setUpdatedDt(DateTime.now().toDate());
        process.setStatus(0);

        processMapper.insertSelective(process);

        ProcessVersion processVersion = new ProcessVersion();
        processVersion.setProcessId(process.getId());
        processVersion.setVersionName(process.getProcessName());
        processVersion.setDescription(process.getDescription());
        processVersion.setIsdefault(true);
        processVersion.setStatus(0);
        processVersion.setVersion(1);
        processVersion.setCreatedBy(authUser.getAccount());
        processVersion.setCreatedDt(DateTime.now().toDate());
        processVersion.setUpdatedBy(authUser.getAccount());
        processVersion.setUpdatedDt(DateTime.now().toDate());
        processVersionMapper.insertSelective(processVersion);

        List<ProcessNode> nodes = Lists.newArrayList();

        ProcessNode start = new ProcessNode();
        start.setProcessId(process.getId());
        start.setVersionId(processVersion.getId());
        start.setNodeName("开始");
        start.setNodeType(NodeTypeEnum.START.getType());
        start.setDescription("流程开始");
        start.setSortNum(0.0f);
        start.setStatus(0);
        start.setConfigData("{}");
        start.setNodeLevel("1");
        start.setCreatedBy(authUser.getAccount());
        start.setCreatedDt(DateTime.now().toDate());
        start.setUpdatedBy(authUser.getAccount());
        start.setUpdatedDt(DateTime.now().toDate());
        processNodeMapper.insertSelective(start);
        nodes.add(start);

        ProcessNode approvalNode = new ProcessNode();
        approvalNode.setNodeName("申请");
        approvalNode.setDescription("申请[默认]");

        approvalNode.setProcessId(process.getId());
        approvalNode.setVersionId(processVersion.getId());
        approvalNode.setStatus(3);
        approvalNode.setNodeType(NodeTypeEnum.APPLYTASK.getType());
        approvalNode.setCreatedBy(authUser.getAccount());
        approvalNode.setCreatedDt(DateTime.now().toDate());
        approvalNode.setUpdatedBy(authUser.getAccount());
        approvalNode.setUpdatedDt(DateTime.now().toDate());
        approvalNode.setSortNum(10.0f);
        approvalNode.setNodeLevel("1");
        approvalNode.setNodeFeature("serial");

        ProcessNodeConfig auditConfigFirst = new ProcessNodeConfig();
        ArrayList<ProcessNodeRoleDto> processNodeRoleDtos=new ArrayList<>();
        ProcessNodeRoleDto processNodeRoleDto=new ProcessNodeRoleDto();
        processNodeRoleDto.setRefId("301");
        processNodeRoleDto.setName("运营管理员(内置)");
        processNodeRoleDto.setType("bss");
        processNodeRoleDtos.add(processNodeRoleDto);
        auditConfigFirst.setCandidates(processNodeRoleDtos.stream()
                                                          .filter(c -> Objects.nonNull(c)).collect(Collectors.toList()));
        auditConfigFirst.setNotifyWays(Arrays.asList("mail"));
        auditConfigFirst.setApprovalLocation(ProcessConstants.APPROVAL_LOCATION_CURRENT);

        //设置默认用户
        ArrayList<ProcessNodeRoleDto> processNodeRoleDtos2=new ArrayList<>();
        ProcessNodeRoleDto processNodeRoleDto2=new ProcessNodeRoleDto();
        processNodeRoleDto2.setRefId(authUser.getUserSid().toString());
        processNodeRoleDto2.setName(authUser.getAccount());
        processNodeRoleDtos2.add(processNodeRoleDto2);
        auditConfigFirst.setCandidateThirds(processNodeRoleDtos2.stream()
                                                                .filter(c -> Objects.nonNull(c)).collect(Collectors.toList()));

        approvalNode.setConfigData(JSON.toJSONString(auditConfigFirst));
        processNodeMapper.insertSelective(approvalNode);
        nodes.add(approvalNode);

        ProcessNode openService = new ProcessNode();
        openService.setProcessId(process.getId());
        openService.setVersionId(processVersion.getId());
        openService.setNodeName("执行");
        openService.setNodeType(NodeTypeEnum.SERVICE.getType());
        openService.setDescription("同步流程申请");
        openService.setSortNum(200.0f);
        openService.setStatus(4);
        openService.setConfigData("{}");
        openService.setNodeLevel("1");
        openService.setCreatedBy(authUser.getAccount());
        openService.setCreatedDt(DateTime.now().toDate());
        openService.setUpdatedBy(authUser.getAccount());
        openService.setUpdatedDt(DateTime.now().toDate());

        if ("product-process".equals(process.getBusinessCode())) {
            // 产品流程设置状态为1，走 MonitorJavaDelegate 类 open回调
            openService.setStatus(1);
        }

        processNodeMapper.insertSelective(openService);
        nodes.add(openService);

        ProcessNode end = new ProcessNode();
        end.setProcessId(process.getId());
        end.setVersionId(processVersion.getId());
        end.setNodeName("结束");
        end.setNodeType(NodeTypeEnum.END.getType());
        end.setDescription("流程结束");
        end.setSortNum(400.0f);
        end.setStatus(0);
        end.setConfigData("{}");
        end.setNodeLevel("1");
        end.setCreatedBy(authUser.getAccount());
        end.setCreatedDt(DateTime.now().toDate());
        end.setUpdatedBy(authUser.getAccount());
        end.setUpdatedDt(DateTime.now().toDate());
        processNodeMapper.insertSelective(end);
        nodes.add(end);

        processMgtDeployProcess(process, processVersion, nodes);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProcessDefine(Long id, Process process, AuthUser authUser) {
        Process processUpdate = processMapper.selectByPrimaryKey(id);
        if (processUpdate == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_408194781));
        }

        Long proceEntityId = processUpdate.getEntryId();
        if (proceEntityId != null && authUser.getEntityId() != null && proceEntityId - authUser.getEntityId() != 0) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
        }

        if (Objects.nonNull(authUser.getOrgSid())&&Objects.nonNull(processUpdate.getOrgSid())
                &&!authUser.getOrgSid().equals(processUpdate.getOrgSid())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1887881448));
        }

        if (process.getProcessName() != null) {
            processUpdate.setProcessName(process.getProcessName());
        }
        if (process.getDescription() != null) {
            processUpdate.setDescription(process.getDescription());
        }
        processUpdate.setUpdatedBy(authUser.getAccount());
        processUpdate.setUpdatedDt(DateTime.now().toDate());

        processMapper.updateByPrimaryKeySelective(processUpdate);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteProcessDefine(Process processDelete,
                                    cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser authUser) {
        processDelete.setStatus(104);
        processDelete.setUpdatedBy(authUser.getAccount());
        processDelete.setUpdatedDt(DateTime.now().toDate());

        processMapper.updateByPrimaryKeySelective(processDelete);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deployProcessDefine(Long processId, AuthUser authUser) {
        Process process = processMapper.selectByPrimaryKey(processId);
        if (process == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_408194781));
        }

//        if (!authUser.getOrgSid().equals(process.getOrgSid())) {
//            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1738370990));
//        }

        List<ProcessVersion> processVersions = processVersionMapper.selectByProcessId(processId);
        if (processVersions.size() < 1) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_408194781));
        }
        ProcessVersion processVersion = processVersions.get(0);

        List<ProcessNode> nodes = processNodeMapper.selectByVersionId(processVersion.getId());

        processMgtDeployProcess(process, processVersion, nodes);

        updateProcessStatus(processId, PROCESS_STATUS_NORMAL);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addProcessNode(Long processId, ProcessNodeAddDto addDto, AuthUser authUser) {
        Process process = processMapper.selectByPrimaryKey(processId);
        if (process == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_408194781));
        }
        Long proceEntityId = process.getEntryId();
        if (proceEntityId != null && authUser.getEntityId() != null && proceEntityId - authUser.getEntityId() != 0) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
        }

        updateProcessStatus(processId, PROCESS_STATUS_EDITED);

        List<ProcessVersion> processVersions = processVersionMapper.selectByProcessId(processId);
        if (processVersions.size() < 1) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_408194781));
        }
        ProcessVersion processVersion = processVersions.get(0);
        Long versionId = processVersion.getId();

        List<ProcessNode> nodes = processNodeMapper.selectByVersionId(versionId);

        boolean nodeNameExist = nodes.stream()
                                     .anyMatch(processNode -> StringUtils.equalsIgnoreCase(processNode.getNodeName(),
                                                                                           addDto.getNodeName()));
        if (nodeNameExist) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1897518684));
        }

        ProcessNode processNode = new ProcessNode();
        processNode.setNodeName(addDto.getNodeName());
        processNode.setDescription(addDto.getDescription());

        processNode.setProcessId(processId);
        processNode.setVersionId(versionId);
        processNode.setStatus(3);
        processNode.setNodeType(NodeTypeEnum.USERTASK.getType());
        processNode.setNodeLevel(addDto.getNodeLevel());
        processNode.setNodeFeature(addDto.getNodeFeature());
        processNode.setParentId(addDto.getParentId());
        processNode.setNodeGroup(addDto.getNodeGroup());
        processNode.setNodeGroupName(addDto.getNodeGroupName());
        processNode.setNodeGroupOrder(addDto.getNodeGroupOrder());
        processNode.setCreatedBy(authUser.getAccount());
        processNode.setCreatedDt(DateTime.now().toDate());
        processNode.setUpdatedBy(authUser.getAccount());
        processNode.setUpdatedDt(DateTime.now().toDate());

        if (StringUtils.isBlank(processNode.getNodeLevel())) {
            processNode.setNodeLevel("1");
        }
        if (StringUtils.isBlank(processNode.getNodeFeature())) {
            processNode.setNodeFeature("serial");
        }

        ProcessNodeConfig config = new ProcessNodeConfig();
        config.setCandidates(addDto.getCandidates().stream()
                                   .filter(c -> Objects.nonNull(c)).collect(Collectors.toList()));
        config.setCandidateThirds(addDto.getCandidateThirds().stream()
                                        .filter(c -> Objects.nonNull(c)).collect(Collectors.toList()));
        config.setConfigEnable(addDto.getConfigEnable());

        if (addDto.getNotifyWays() != null) {
            config.setNotifyWays(addDto.getNotifyWays());
        }
        config.setApprovalWay(addDto.getApprovalWay());
        config.setMessageConfig(null);
        config.setCandidateAuditable(addDto.getCandidateAuditable());
        config.setCandidateGoback(addDto.getCandidateGoback());
        config.setFormTemplate(addDto.getFormTemplate());
        config.setEditableFormTemplates(addDto.getEditableFormTemplates());

        processNode.setConfigData(JSON.toJSONString(config));

        float rangeStart = 0.0f;
        float rangeEnd = 1000.0f;
        ProcessNode curNode = null;
        for (int i = 0; i < nodes.size(); i++) {
            ProcessNode node = nodes.get(i);

            if (node.getNodeType().equals(NodeTypeEnum.START.getType())
                    || node.getNodeType().equals(NodeTypeEnum.USERTASK.getType())
                    || node.getNodeType().equals(NodeTypeEnum.APPLYTASK.getType())) {
                rangeStart = node.getSortNum();
                rangeEnd = nodes.get(i + 1).getSortNum();
                curNode = node;
            }
        }

        List<ProcessNode> rejectNodes = processNodeMapper.selectCloseNodeByVersionId(versionId);

        if (CollectionUtils.isEmpty(rejectNodes)) {
            if (includeNodeType(curNode)
                    && candidateAutitable(config)) {
                ProcessNode rejectNode = new ProcessNode();
                rejectNode.setProcessId(process.getId());
                rejectNode.setVersionId(versionId);
                rejectNode.setNodeName("拒绝并关闭");
                rejectNode.setNodeType(NodeTypeEnum.SERVICE.getType());
                rejectNode.setDescription("用户审批拒绝");
                rejectNode.setSortNum(nodes.get(nodes.size() - 1).getSortNum() - 10.0F);
                rejectNode.setNodeLevel("1");
                rejectNode.setNodeFeature("serial");
                rejectNode.setStatus(2);
                rejectNode.setConfigData("{}");
                rejectNode.setCreatedBy(authUser.getAccount());
                rejectNode.setCreatedDt(DateTime.now().toDate());
                rejectNode.setUpdatedBy(authUser.getAccount());
                rejectNode.setUpdatedDt(DateTime.now().toDate());

                processNodeMapper.insertSelective(rejectNode);
            }
        }

        processNode.setSortNum((rangeStart + rangeEnd) / 2);

        if (StringUtils.equals(processNode.getNodeLevel(), "2")) {
            if (Objects.isNull(processNode.getParentId())) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1454618267));
            }

            List<ProcessNode> childNodes = processNodeMapper.selectByParams(
                    new Criteria("parentId", processNode.getParentId()));

            Map<Long, List<ProcessNode>> groupMap = childNodes.stream()
                                                              .collect(
                                                                      Collectors.groupingBy(ProcessNode::getNodeGroup));

            if (Objects.isNull(processNode.getNodeGroup()) || !groupMap.containsKey(processNode.getNodeGroup())) {
                processNode.setNodeGroupOrder(1);
                processNode.setSortNum(100.0F);
                // 节点分组为空，新增分组
                if (CollectionUtils.isEmpty(childNodes)) {
                    processNode.setNodeGroup(1L);
                } else {
                    OptionalLong max = childNodes.stream().mapToLong(ProcessNode::getNodeGroup).max();
                    if (max.isPresent()) {
                        long maxGroupId = max.getAsLong();
                        processNode.setNodeGroup(maxGroupId + 1L);
                        OptionalInt orderMax = childNodes.stream().mapToInt(ProcessNode::getNodeGroupOrder).max();
                        if (orderMax.isPresent()) {
                            processNode.setNodeGroupOrder(orderMax.getAsInt() + 1);
                        }
                    } else {
                        processNode.setNodeGroup(1L);
                    }
                }
            } else {
                List<ProcessNode> groupNodes = groupMap.get(processNode.getNodeGroup());
                processNode.setNodeGroupOrder(groupNodes.get(0).getNodeGroupOrder());
                OptionalDouble sortMax = groupNodes.stream().mapToDouble(ProcessNode::getSortNum).max();
                if (sortMax.isPresent()) {
                    processNode.setSortNum((float) sortMax.getAsDouble() + 100.0F);
                } else {
                    processNode.setSortNum(100.0F);
                }
            }
        }

        processNodeMapper.insertSelective(processNode);
        return processNode.getId();
    }

    private static boolean candidateAutitable(ProcessNodeConfig config) {
        return !ObjectUtils.isEmpty(config)
            && !ObjectUtils.isEmpty(config.getCandidateAuditable())
            && config.getCandidateAuditable();
    }

    private static boolean includeNodeType(ProcessNode curNode) {
        if (Objects.isNull(curNode)) {
            return false;
        }

        return Stream.of(
            NodeTypeEnum.START.getType(),
            NodeTypeEnum.APPLYTASK.getType(),
            NodeTypeEnum.USERTASK.getType()
        ).anyMatch(nodeType -> nodeType.equals(curNode.getNodeType()));
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProcessNode(Long id, ProcessNode processNode, AuthUser authUser) {
        ProcessNode nodeUpdate = processNodeMapper.selectByPrimaryKey(id);
        if (nodeUpdate == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_462777243));
        }

        if (processNode.getNodeName() != null) {
            nodeUpdate.setNodeName(processNode.getNodeName());
        }
        if (processNode.getDescription() != null) {
            nodeUpdate.setDescription(processNode.getDescription());
        }
        if (processNode.getConfigData() != null) {
            nodeUpdate.setConfigData(processNode.getConfigData());
        }
        nodeUpdate.setUpdatedBy(authUser.getAccount());
        nodeUpdate.setUpdatedDt(DateTime.now().toDate());

        processNodeMapper.updateByPrimaryKeySelective(nodeUpdate);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processNodeUp(Long id, AuthUser authUser) {
        ProcessNode processNode = processNodeMapper.selectByPrimaryKey(id);
        if (processNode == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_462777243));
        }

        if (!processNode.getNodeType().equals(NodeTypeEnum.USERTASK.getType())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_574575453));
        }

        updateProcessStatus(processNode.getProcessId(), PROCESS_STATUS_EDITED);

        List<ProcessNode> nodes = processNodeMapper.selectByVersionId(processNode.getVersionId());
        ProcessNode previousNode = null;

        for (int i = 0; i < nodes.size(); i++) {
            ProcessNode node = nodes.get(i);

            if (node.getId().equals(processNode.getId())) {
                previousNode = nodes.get(i - 1);
            }
        }

        if (previousNode != null && previousNode.getNodeType().equals(NodeTypeEnum.USERTASK.getType())) {
            // 和前一个节点交换
            float sortNumSwap = previousNode.getSortNum();
            previousNode.setSortNum(processNode.getSortNum());
            processNode.setSortNum(sortNumSwap);

            processNodeMapper.updateByPrimaryKeySelective(previousNode);
            processNodeMapper.updateByPrimaryKeySelective(processNode);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processNodeDown(Long id, AuthUser authUser) {
        ProcessNode processNode = processNodeMapper.selectByPrimaryKey(id);
        if (processNode == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_462777243));
        }

        if (!processNode.getNodeType().equals(NodeTypeEnum.USERTASK.getType())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_574575453));
        }

        updateProcessStatus(processNode.getProcessId(), PROCESS_STATUS_EDITED);

        List<ProcessNode> nodes = processNodeMapper.selectByVersionId(processNode.getVersionId());
        ProcessNode nextNode = null;

        for (int i = 0; i < nodes.size(); i++) {
            ProcessNode node = nodes.get(i);

            if (node.getId().equals(processNode.getId())) {
                nextNode = nodes.get(i + 1);
            }
        }

        if (nextNode != null && nextNode.getNodeType().equals(NodeTypeEnum.USERTASK.getType())) {
            // 和后面一个节点交换
            float sortNumSwap = nextNode.getSortNum();
            nextNode.setSortNum(processNode.getSortNum());
            processNodeMapper.updateByPrimaryKeySelective(nextNode);
            processNode.setSortNum(sortNumSwap);
            processNodeMapper.updateByPrimaryKeySelective(processNode);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteProcessNode(Long id, AuthUser authUser) {
        ProcessNode processNode = processNodeMapper.selectByPrimaryKey(id);
        if (processNode == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_462777243));
        }
        if (Objects.equals(104, processNode.getStatus())) {
            throw new BizException("流程节点已被删除，请勿重复操作");
        }
        updateProcessStatus(processNode.getProcessId(), PROCESS_STATUS_EDITED);

        processNode.setStatus(104);
        processNode.setUpdatedBy(authUser.getAccount());
        processNode.setUpdatedDt(DateTime.now().toDate());

        processNodeMapper.updateByPrimaryKeySelective(processNode);

        List<ProcessNode> auditNodes = processNodeMapper.selectAuditNodeByVersionId(processNode.getVersionId());
        if (auditNodes.size() < 1) {
            List<ProcessNode> closeNodes = processNodeMapper.selectCloseNodeByVersionId(processNode.getVersionId());
            for (ProcessNode closeNode : closeNodes) {
                closeNode.setUpdatedBy(authUser.getAccount());
                closeNode.setUpdatedDt(DateTime.now().toDate());
                closeNode.setStatus(104);

                processNodeMapper.updateByPrimaryKeySelective(closeNode);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveProcessNodeConfig(Long id, ProcessNodeEditDto editDto, AuthUser authUser) {
        ProcessNode processNode = processNodeMapper.selectByPrimaryKey(id);
        if (processNode == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_462777243));
        }
        if (!StringUtils.equals(processNode.getNodeLevel(), editDto.getNodeLevel())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1025170802));
        }
        if (StringUtils.isEmpty(editDto.getFormTemplate())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_86652741));
        }
        if(CollectionUtil.isEmpty(editDto.getCandidates())){
            throw new BizException("处理角色不能为空");
        }
        if(CollectionUtil.isEmpty(editDto.getCandidateThirds())){
            throw new BizException("处理人不能为空");
        }
        if(CollectionUtil.isNotEmpty(editDto.getCandidateThirds())){
            editDto.getCandidateThirds().stream().forEach(c->{
                if(StringUtils.isEmpty(c.getRefId())){
                    throw new BizException("候选人Id不能为空");
                }
            });
        }
        Criteria criteria = new Criteria();
        criteria.put("entryId", RequestContextUtil.getEntityId());
        criteria.put("id", editDto.getFormTemplate());
        List<ProcessTemplate> processTemplates = processTemplateMapper.queryByCriteria(criteria);
        if (CollectionUtils.isEmpty(processTemplates)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1935158559));
        }

        updateProcessStatus(processNode.getProcessId(), PROCESS_STATUS_EDITED);

        ProcessNodeConfig config = JSON.parseObject(processNode.getConfigData(), ProcessNodeConfig.class);

        if (editDto.getNodeName() != null) {
            processNode.setNodeName(editDto.getNodeName());
        }
        if (editDto.getDescription() != null) {
            processNode.setDescription(editDto.getDescription());
        }

        config.setConfigEnable(editDto.getConfigEnable());
        if (editDto.getCandidates() != null) {
            config.setCandidates(editDto.getCandidates().stream()
                                        .filter(c -> Objects.nonNull(c)).collect(Collectors.toList()));
        }
        if (editDto.getCandidateThirds() != null) {
            config.setCandidateThirds(editDto.getCandidateThirds().stream()
                                             .filter(c -> Objects.nonNull(c)).collect(Collectors.toList()));
        }
        if (editDto.getNotifyWays() != null) {
            config.setNotifyWays(editDto.getNotifyWays());
        }
        config.setMessageConfig(null);
        config.setFormTemplate(editDto.getFormTemplate());
        if (StringUtils.isNotEmpty(editDto.getApprovalWay())) {
            config.setApprovalWay(editDto.getApprovalWay());
        }
        if (Objects.nonNull(editDto.getCandidateAuditable())) {
            config.setCandidateAuditable(editDto.getCandidateAuditable());
        }
        if (Objects.nonNull(editDto.getCandidateGoback())) {
            config.setCandidateGoback(editDto.getCandidateGoback());
        }
        if (CollectionUtils.isNotEmpty(editDto.getEditableFormTemplates())) {
            config.setEditableFormTemplates(editDto.getEditableFormTemplates());
        }

        processNode.setConfigData(JSON.toJSONString(config));
        processNode.setUpdatedBy(authUser.getAccount());
        processNode.setUpdatedDt(DateTime.now().toDate());

        processNodeMapper.updateByPrimaryKeySelective(processNode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveProcessNodeMessageMeta(Long id, ProcessNodeMessageDto messageDto, AuthUser authUser) {

        ProcessNode processNode = processNodeMapper.selectByPrimaryKey(id);
        if (processNode == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_462777243));
        }

        ProcessNodeConfig config = JSON.parseObject(processNode.getConfigData(), ProcessNodeConfig.class);

        config.setMessageConfig(messageDto);

        processNode.setConfigData(JSON.toJSONString(config));
        processNode.setUpdatedBy(authUser.getAccount());
        processNode.setUpdatedDt(DateTime.now().toDate());

        processNodeMapper.updateByPrimaryKeySelective(processNode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Async("cloudExecutor")
    public void processDefineInit(Company company) {
        if (Objects.isNull(company) || Objects.isNull(company.getOrgSid())) {
            return;
        }

        Process processApply = processMapper.selectByBusinessCodeWithDeleted(ProcessConstants.SERVICE_APPLY,
                                                                             company.getOrgSid());
        if (Objects.isNull(processApply)) {
            initBusinessProcess(ProcessConstants.SERVICE_APPLY, company);
        }

        Process processChangegrade = processMapper.selectByBusinessCodeWithDeleted(ProcessConstants.SERVICE_CHANGEGRADE,
                                                                                   company.getOrgSid());
        if (Objects.isNull(processChangegrade)) {
            initBusinessProcess(ProcessConstants.SERVICE_CHANGEGRADE, company);
        }

        Process processRenew = processMapper.selectByBusinessCodeWithDeleted(ProcessConstants.SERVICE_RENEW,
                                                                             company.getOrgSid());
        if (Objects.isNull(processRenew)) {
            initBusinessProcess(ProcessConstants.SERVICE_RENEW, company);
        }

        Process processRelease = processMapper.selectByBusinessCodeWithDeleted(ProcessConstants.SERVICE_RELEASE,
                                                                               company.getOrgSid());
        if (Objects.isNull(processRelease)) {
            initBusinessProcess(ProcessConstants.SERVICE_RELEASE, company);
        }

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    @Async("cloudExecutor")
    public void systemBuildInProcessInit() {
        //暂时保留   删除流程的操作  有需要可修改时间
        Date date = null;
        try {
            date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse("2022-07-01 00:00:00");
        } catch (ParseException e) {

        }

        Process processPublish = processMapper.selectByBusinessCodeWithDeleted(ProcessConstants.SERVICE_PUBLISH, 0L);
        //需要重新发布定义流程
        if (Objects.isNull(processPublish)) {
            log.info("服务发布审批流程定义未定义，添加定义...");
            initServerPublishBusinessProcess(ProcessConstants.SERVICE_PUBLISH);
        }else{
            //发布的创建日期
            if(processPublish.getCreatedDt().before(date)){
                //删除历史的发布的服务
                processMapper.deleteByBusinessCode(ProcessConstants.SERVICE_PUBLISH, 0L);
                initServerPublishBusinessProcess(ProcessConstants.SERVICE_PUBLISH);
            }
        }
        processPublish = processMapper.selectByBusinessCodeWithDeleted(ProcessConstants.HPC_SERVICE_APPLY, 0L);
        if (Objects.isNull(processPublish)) {
            log.info("HPC服务申请审批流程定义未定义，添加定义...");
            initHPCServerApplyBusinessProcess(ProcessConstants.HPC_SERVICE_APPLY);
        }else{
            if(processPublish.getCreatedDt().before(date)){
                //删除历史的发布额定义
                processMapper.deleteByBusinessCode(ProcessConstants.HPC_SERVICE_APPLY, 0L);
                initHPCServerApplyBusinessProcess(ProcessConstants.HPC_SERVICE_APPLY);
            }
        }
        processPublish = processMapper.selectByBusinessCodeWithDeleted(ProcessConstants.HPC_DRP_SERVICE_APPLY, 0L);
        if (Objects.isNull(processPublish)) {
            log.info("HPC服务申请审批流程定义未定义，添加定义...");
            initHPCServerApplyBusinessProcess(ProcessConstants.HPC_DRP_SERVICE_APPLY);
        }else{
            if(processPublish.getCreatedDt().before(date)){
                processMapper.deleteByBusinessCode(ProcessConstants.HPC_DRP_SERVICE_APPLY,0L);
                initHPCServerApplyBusinessProcess(ProcessConstants.HPC_DRP_SERVICE_APPLY);
            }
        }
        processPublish = processMapper.selectByBusinessCodeWithDeleted(ProcessConstants.HPC_DRP_SERVICE_UPGRADE, 0L);
        if (Objects.isNull(processPublish)) {
            log.info("HPC专属资源池服务扩容申请审批流程定义未定义，添加定义...");
            initHPCServerApplyBusinessProcess(ProcessConstants.HPC_DRP_SERVICE_UPGRADE);
        }else{
            if(processPublish.getCreatedDt().before(date)){
                processMapper.deleteByBusinessCode(ProcessConstants.HPC_DRP_SERVICE_UPGRADE,0L);
                initHPCServerApplyBusinessProcess(ProcessConstants.HPC_DRP_SERVICE_UPGRADE);
            }
        }

        processPublish = processMapper.selectByBusinessCodeWithDeleted(ProcessConstants.SERVICE_APPLY, 0L);
        if (Objects.isNull(processPublish)) {
            log.info("内置服务申请审批流程定义未定义，添加定义...");
            initServerPublishBusinessProcess(ProcessConstants.SERVICE_APPLY);
        }else{
            if(processPublish.getCreatedDt().before(date)){
                processMapper.deleteByBusinessCode(ProcessConstants.SERVICE_APPLY,0L);
                initServerPublishBusinessProcess(ProcessConstants.SERVICE_APPLY, processPublish.getId());
            }
        }

        // 初始化系统自动审批
        processPublish = processMapper.selectByBusinessCodeWithDeleted(ProcessConstants.AUTO_APPROVAL, 0L);
        if (Objects.isNull(processPublish)) {
            log.info("内置【自动审批】审批流程定义未定义，添加定义...");
            initAutoServerPublishBusinessProcess(ProcessConstants.AUTO_APPROVAL);
        }else{
            if(processPublish.getCreatedDt().before(date)){
                processMapper.deleteByBusinessCode(ProcessConstants.AUTO_APPROVAL,0L);
                initAutoServerPublishBusinessProcess(ProcessConstants.AUTO_APPROVAL);
            }
        }

        processPublish = processMapper.selectByBusinessCodeWithDeleted(ProcessConstants.MA_LOGICAL_DRP_SERVICE_RELEASE, 0L);
        if (Objects.isNull(processPublish)) {
            log.info("MA逻辑专属资源池退订审批流程定义未定义，添加定义...");
            initServerPublishBusinessProcess(ProcessConstants.MA_LOGICAL_DRP_SERVICE_RELEASE);
        }else{
            if(processPublish.getCreatedDt().before(date)){
                processMapper.deleteByBusinessCode(ProcessConstants.MA_LOGICAL_DRP_SERVICE_RELEASE,0L);
                initServerPublishBusinessProcess(ProcessConstants.MA_LOGICAL_DRP_SERVICE_RELEASE);
            }
        }

        processPublish = processMapper.selectByBusinessCodeWithDeleted(ProcessConstants.MA_DRP_SERVICE_APPLY, 0L);
        if (Objects.isNull(processPublish)) {
            log.info("MA服务申请审批流程定义未定义，添加定义...");
            initMAServerApplyBusinessProcess(ProcessConstants.MA_DRP_SERVICE_APPLY);
        }else{
            if(processPublish.getCreatedDt().before(date)){
                processMapper.deleteByBusinessCode(ProcessConstants.MA_DRP_SERVICE_APPLY,0L);
                initMAServerApplyBusinessProcess(ProcessConstants.MA_DRP_SERVICE_APPLY);
            }
        }

        processPublish = processMapper.selectByBusinessCodeWithDeleted(ProcessConstants.MA_DRP_SERVICE_UPGRADE, 0L);
        if (Objects.isNull(processPublish)){
            log.info("MA专属资源池扩容审批流程定义未定义，添加定义...");
            initHPCServerApplyBusinessProcess(ProcessConstants.MA_DRP_SERVICE_UPGRADE);
        }else{
            if(processPublish.getCreatedDt().before(date)){
                processMapper.deleteByBusinessCode(ProcessConstants.MA_DRP_SERVICE_UPGRADE,0L);
                initHPCServerApplyBusinessProcess(ProcessConstants.MA_DRP_SERVICE_UPGRADE);
            }
        }

        processPublish = processMapper.selectByBusinessCodeWithDeleted(ProcessConstants.MA_DRP_SERVICE_DEGRADE, 0L);
        if (Objects.isNull(processPublish)){
            log.info("MA专属资源池缩容审批流程定义未定义，添加定义...");
            initHPCServerApplyBusinessProcess(ProcessConstants.MA_DRP_SERVICE_DEGRADE);
        }else{
            if(processPublish.getCreatedDt().before(date)){
                processMapper.deleteByBusinessCode(ProcessConstants.MA_DRP_SERVICE_DEGRADE,0L);
                initHPCServerApplyBusinessProcess(ProcessConstants.MA_DRP_SERVICE_DEGRADE);
            }
        }

        processPublish = processMapper.selectByBusinessCodeWithDeleted(ProcessConstants.MA_DRP_SERVICE_RELEASE, 0L);
        if (Objects.isNull(processPublish)){
            log.info("MA专属资源池退订审批流程定义未定义，添加定义...");
            initMAServerApplyBusinessProcess(ProcessConstants.MA_DRP_SERVICE_RELEASE);
        }else{
            if(processPublish.getCreatedDt().before(date)){
                processMapper.deleteByBusinessCode(ProcessConstants.MA_DRP_SERVICE_RELEASE,0L);
                initMAServerApplyBusinessProcess(ProcessConstants.MA_DRP_SERVICE_RELEASE);
            }
        }

        processPublish = processMapper.selectByBusinessCodeWithDeleted(ProcessConstants.SERVICE_RELEASE, 0L);
        if (Objects.isNull(processPublish)) {
            log.info("MA专属资源池退订审批流程定义未定义，添加定义...");
            initServerReleaseBusinessProcess(ProcessConstants.SERVICE_RELEASE);
        } else {
            if (processPublish.getCreatedDt().before(date)) {
                processMapper.deleteByBusinessCode(ProcessConstants.SERVICE_RELEASE, 0L);
                initServerReleaseBusinessProcess(ProcessConstants.SERVICE_RELEASE);
            }
        }

        processPublish = processMapper.selectByBusinessCodeWithDeleted(ProcessConstants.QUOTA_PROCESS, 0L);
        if (Objects.isNull(processPublish)){
            log.info("配额申请流程定义未定义，添加定义...");
            initQuotaProcessProcess(ProcessConstants.QUOTA_PROCESS);
        }else{
            if(processPublish.getCreatedDt().before(date)){
                processMapper.deleteByBusinessCode(ProcessConstants.QUOTA_PROCESS,0L);
                initQuotaProcessProcess(ProcessConstants.QUOTA_PROCESS);
            }
        }

        processPublish = processMapper.selectByBusinessCodeAndProcessNameWithDeleted(BUILT_IN, ProcessConstants.REAUDIT_NO_RESOURCE, 0L);
        if (Objects.isNull(processPublish)){
            log.info("双重审核流程-非资源审批流程定义未定义，添加定义...");
            initReauditNoResourceProcess();
        }else{
            if(processPublish.getCreatedDt().before(date)){
                processMapper.deleteByBusinessCodeAndProcessName(BUILT_IN, ProcessConstants.REAUDIT_NO_RESOURCE,0L);
                initReauditNoResourceProcess();
            }
        }
        processPublish = processMapper.selectByBusinessCodeAndProcessNameWithDeleted(BUILT_IN, ProcessConstants.REAUDIT_RESOURCE, 0L);
        if (Objects.isNull(processPublish)){
            log.info("双重审核流程-资源审批流程定义未定义，添加定义...");
            initReauditResourceProcess();
        }else{
            if(processPublish.getCreatedDt().before(date)){
                processMapper.deleteByBusinessCodeAndProcessName(BUILT_IN, ProcessConstants.REAUDIT_RESOURCE,0L);
                initReauditResourceProcess();
            }
        }
        processPublish = processMapper.selectByBusinessCodeAndProcessNameWithDeleted(BUILT_IN, ProcessConstants.REAUDIT_NO_RESOURCE_HPC, 0L);
        if (Objects.isNull(processPublish)){
            log.info("双重审核流程-非资源审批hpc流程定义未定义，添加定义...");
            initReauditNoResourceHpcProcess();
        }else{
            if(processPublish.getCreatedDt().before(date)){
                processMapper.deleteByBusinessCodeAndProcessName(BUILT_IN, ProcessConstants.REAUDIT_NO_RESOURCE_HPC,0L);
                initReauditNoResourceHpcProcess();
            }
        }
        processPublish = processMapper.selectByBusinessCodeAndProcessNameWithDeleted(BUILT_IN, ProcessConstants.REAUDIT_RESOURCE_HPC, 0L);
        if (Objects.isNull(processPublish)){
            log.info("双重审核流程-资源审批hpc流程定义未定义，添加定义...");
            initReauditResourceHpcProcess();
        }else{
            if(processPublish.getCreatedDt().before(date)){
                processMapper.deleteByBusinessCodeAndProcessName(BUILT_IN, ProcessConstants.REAUDIT_RESOURCE_HPC,0L);
                initReauditResourceHpcProcess();
            }
        }
    }


    public void initQuotaProcessProcess(String processType) {
        // 标准版不包含hpc
        LicenseVo licenseVo = LicenseUtil.queryLicenseInfoFromDb();
        if (Objects.nonNull(licenseVo)) {
            String versionType = licenseVo.getVersionType();
            if (LicenseUtil.STAND.equals(versionType)) {
                return;
            }
        }
        String opby = "admin";
        Process process = new Process();
        process.setProcessName("存储配额固定模板");
        process.setBusinessCode(processType);
        process.setDescription(process.getProcessName() + "[默认流程]");
        IdGen idGen = IdGen.get();
        process.setProcessCode("approval-" + idGen.nextId());
        process.setCreatedBy(opby);
        process.setCreatedDt(DateTime.now().toDate());
        process.setUpdatedBy(opby);
        process.setUpdatedDt(DateTime.now().toDate());
        process.setOrgSid(0L);
        process.setStatus(0);

        processMapper.insertSelective(process);

        ProcessVersion processVersion = new ProcessVersion();
        processVersion.setProcessId(process.getId());
        processVersion.setVersionName(process.getProcessName());
        processVersion.setDescription(process.getDescription());
        processVersion.setIsdefault(true);
        processVersion.setStatus(0);
        processVersion.setVersion(1);
        processVersion.setCreatedBy(opby);
        processVersion.setCreatedDt(DateTime.now().toDate());
        processVersion.setUpdatedBy(opby);
        processVersion.setUpdatedDt(DateTime.now().toDate());
        processVersionMapper.insertSelective(processVersion);

        List<ProcessNode> nodes = Lists.newArrayList();

        ProcessNode start = new ProcessNode();
        start.setProcessId(process.getId());
        start.setVersionId(processVersion.getId());
        start.setNodeName("开始");
        start.setNodeType(NodeTypeEnum.START.getType());
        start.setDescription("流程开始");
        start.setSortNum(0.0f);
        start.setStatus(0);
        start.setConfigData("{}");
        start.setNodeLevel("1");
        start.setCreatedBy(opby);
        start.setCreatedDt(DateTime.now().toDate());
        start.setUpdatedBy(opby);
        start.setUpdatedDt(DateTime.now().toDate());
        processNodeMapper.insertSelective(start);
        nodes.add(start);

        ProcessNode approvalNode = new ProcessNode();
        approvalNode.setNodeName("申请");
        approvalNode.setDescription("申请[默认]");

        approvalNode.setProcessId(process.getId());
        approvalNode.setVersionId(processVersion.getId());
        approvalNode.setStatus(3);
        approvalNode.setNodeType(NodeTypeEnum.APPLYTASK.getType());
        approvalNode.setCreatedBy(opby);
        approvalNode.setCreatedDt(DateTime.now().toDate());
        approvalNode.setUpdatedBy(opby);
        approvalNode.setUpdatedDt(DateTime.now().toDate());
        approvalNode.setSortNum(10.0f);
        approvalNode.setNodeLevel("1");
        approvalNode.setNodeFeature("serial");

        ProcessNodeConfig auditConfigFirst = new ProcessNodeConfig();
        auditConfigFirst.setCandidates(Lists.newArrayList());
        auditConfigFirst.setNotifyWays(Lists.newArrayList());
        auditConfigFirst.setApprovalLocation(ProcessConstants.APPROVAL_LOCATION_CURRENT);
        approvalNode.setConfigData(JSON.toJSONString(auditConfigFirst));

        processNodeMapper.insertSelective(approvalNode);
        nodes.add(approvalNode);

        ProcessNode openService = new ProcessNode();
        openService.setProcessId(process.getId());
        openService.setVersionId(processVersion.getId());
        openService.setNodeName("执行");
        openService.setNodeType(NodeTypeEnum.SERVICE.getType());
        openService.setDescription("同步流程申请");
        openService.setSortNum(200.0f);
        openService.setStatus(4);
        openService.setConfigData("{}");
        openService.setNodeLevel("1");
        openService.setCreatedBy(opby);
        openService.setCreatedDt(DateTime.now().toDate());
        openService.setUpdatedBy(opby);
        openService.setUpdatedDt(DateTime.now().toDate());
        processNodeMapper.insertSelective(openService);
        nodes.add(openService);

        ProcessNode end = new ProcessNode();
        end.setProcessId(process.getId());
        end.setVersionId(processVersion.getId());
        end.setNodeName("结束");
        end.setNodeType(NodeTypeEnum.END.getType());
        end.setDescription("流程结束");
        end.setSortNum(400.0f);
        end.setStatus(0);
        end.setConfigData("{}");
        end.setNodeLevel("1");
        end.setCreatedBy(opby);
        end.setCreatedDt(DateTime.now().toDate());
        end.setUpdatedBy(opby);
        end.setUpdatedDt(DateTime.now().toDate());
        processNodeMapper.insertSelective(end);
        nodes.add(end);

        processMgtDeployProcess(process, processVersion, nodes);
    }

    @Override
    public List<Map<String, String>> listBusiness() {
        return BUSINESS;
    }

    private void initServerPublishBusinessProcess(String businessCode) {
        //添加流程
        String opby = "admin";
        Process process = new Process();
        process.setProcessName(ProcessConstants.BUSINESS_MAP.get(businessCode));
        process.setBusinessCode(businessCode);
        process.setDescription(process.getProcessName() + "[默认流程]");
        IdGen idGen = IdGen.get();
        process.setProcessCode("approval-" + idGen.nextId());
        process.setCreatedBy(opby);
        process.setCreatedDt(DateTime.now().toDate());
        process.setUpdatedBy(opby);
        process.setUpdatedDt(DateTime.now().toDate());
        process.setOrgSid(0L);
        process.setStatus(0);
        //插入流程的基本信息
        processMapper.insertSelective(process);

        //流程版本
        ProcessVersion processVersion = new ProcessVersion();
        processVersion.setProcessId(process.getId());
        processVersion.setVersionName(process.getProcessName());
        processVersion.setDescription(process.getDescription());
        processVersion.setIsdefault(true);
        processVersion.setStatus(0);
        processVersion.setVersion(1);
        processVersion.setCreatedBy(opby);
        processVersion.setCreatedDt(DateTime.now().toDate());
        processVersion.setUpdatedBy(opby);
        processVersion.setUpdatedDt(DateTime.now().toDate());
        //插入流程版本的基本信息
        processVersionMapper.insertSelective(processVersion);

        //添加流程节点
        List<ProcessNode> nodes = Lists.newArrayList();
        ProcessNode start = new ProcessNode();
        start.setProcessId(process.getId());
        start.setVersionId(processVersion.getId());
        start.setNodeName("开始");
        start.setNodeType(NodeTypeEnum.START.getType());
        start.setDescription("流程开始");
        start.setSortNum(0.0f);
        start.setStatus(0);
        start.setConfigData("{}");
        start.setCreatedBy(opby);
        start.setCreatedDt(DateTime.now().toDate());
        start.setUpdatedBy(opby);
        start.setUpdatedDt(DateTime.now().toDate());
        processNodeMapper.insertSelective(start);
        nodes.add(start);

        ProcessNode companyApprovalNode = new ProcessNode();
        companyApprovalNode.setNodeName("运营管理员审批");
        companyApprovalNode.setDescription("运营管理员审批[默认]");

        companyApprovalNode.setProcessId(process.getId());
        companyApprovalNode.setVersionId(processVersion.getId());
        companyApprovalNode.setStatus(3);
        companyApprovalNode.setNodeType(NodeTypeEnum.USERTASK.getType());
        companyApprovalNode.setCreatedBy(opby);
        companyApprovalNode.setCreatedDt(DateTime.now().toDate());
        companyApprovalNode.setUpdatedBy(opby);
        companyApprovalNode.setUpdatedDt(DateTime.now().toDate());
        companyApprovalNode.setSortNum(100.0f);
        //设置节点审批候选人/组的角色
        ProcessNodeConfig auditConfigFirst = new ProcessNodeConfig();
        auditConfigFirst.setCandidates(Arrays.asList(new ProcessNodeRoleDto(
                                                             "10-platform", "运营管理员", "301", 0, null),
                                                     new ProcessNodeRoleDto(
                                                             "10-platform", "运维管理员", "305", 0, null)));
        auditConfigFirst.setNotifyWays(Arrays.asList("station", "mail"));
        companyApprovalNode.setConfigData(JSON.toJSONString(auditConfigFirst));

        processNodeMapper.insertSelective(companyApprovalNode);
        nodes.add(companyApprovalNode);

        ProcessNode openService = new ProcessNode();
        openService.setProcessId(process.getId());
        openService.setVersionId(processVersion.getId());
        openService.setNodeName("执行");
        openService.setNodeType(NodeTypeEnum.SERVICE.getType());
        openService.setDescription("服务开通");
        openService.setSortNum(200.0f);
        openService.setStatus(1);
        openService.setConfigData("{}");
        openService.setCreatedBy(opby);
        openService.setCreatedDt(DateTime.now().toDate());
        openService.setUpdatedBy(opby);
        openService.setUpdatedDt(DateTime.now().toDate());
        processNodeMapper.insertSelective(openService);
        nodes.add(openService);

        ProcessNode rejectNode = new ProcessNode();
        rejectNode.setProcessId(process.getId());
        rejectNode.setVersionId(processVersion.getId());
        rejectNode.setNodeName("拒绝并关闭");
        rejectNode.setNodeType(NodeTypeEnum.SERVICE.getType());
        rejectNode.setDescription("用户审批拒绝");
        rejectNode.setSortNum(300.0f);
        rejectNode.setStatus(2);
        rejectNode.setConfigData("{}");
        rejectNode.setCreatedBy(opby);
        rejectNode.setCreatedDt(DateTime.now().toDate());
        rejectNode.setUpdatedBy(opby);
        rejectNode.setUpdatedDt(DateTime.now().toDate());
        processNodeMapper.insertSelective(rejectNode);
        nodes.add(rejectNode);

        ProcessNode end = new ProcessNode();
        end.setProcessId(process.getId());
        end.setVersionId(processVersion.getId());
        end.setNodeName("结束");
        end.setNodeType(NodeTypeEnum.END.getType());
        end.setDescription("流程结束");
        end.setSortNum(400.0f);
        end.setStatus(0);
        end.setConfigData("{}");
        end.setCreatedBy(opby);
        end.setCreatedDt(DateTime.now().toDate());
        end.setUpdatedBy(opby);
        end.setUpdatedDt(DateTime.now().toDate());
        processNodeMapper.insertSelective(end);
        nodes.add(end);

        deployProcess(process, processVersion, nodes);
    }

    private void initServerPublishBusinessProcess(String businessCode, Long processId) {
        //添加流程
        String opby = "admin";
        Process process = new Process();
        process.setId(processId);
        process.setProcessName(ProcessConstants.BUSINESS_MAP.get(businessCode));
        process.setBusinessCode(businessCode);
        process.setDescription(process.getProcessName() + "[默认流程]");
        IdGen idGen = IdGen.get();
        process.setProcessCode("approval-" + idGen.nextId());
        process.setCreatedBy(opby);
        process.setCreatedDt(DateTime.now().toDate());
        process.setUpdatedBy(opby);
        process.setUpdatedDt(DateTime.now().toDate());
        process.setOrgSid(0L);
        process.setStatus(0);
        //插入流程的基本信息
        processMapper.insertSelective(process);

        //流程版本
        ProcessVersion processVersion = new ProcessVersion();
        processVersion.setProcessId(process.getId());
        processVersion.setVersionName(process.getProcessName());
        processVersion.setDescription(process.getDescription());
        processVersion.setIsdefault(true);
        processVersion.setStatus(0);
        processVersion.setVersion(1);
        processVersion.setCreatedBy(opby);
        processVersion.setCreatedDt(DateTime.now().toDate());
        processVersion.setUpdatedBy(opby);
        processVersion.setUpdatedDt(DateTime.now().toDate());
        //插入流程版本的基本信息
        processVersionMapper.insertSelective(processVersion);

        //添加流程节点
        List<ProcessNode> nodes = Lists.newArrayList();
        ProcessNode start = new ProcessNode();
        start.setProcessId(process.getId());
        start.setVersionId(processVersion.getId());
        start.setNodeName("开始");
        start.setNodeType(NodeTypeEnum.START.getType());
        start.setDescription("流程开始");
        start.setSortNum(0.0f);
        start.setStatus(0);
        start.setConfigData("{}");
        start.setCreatedBy(opby);
        start.setCreatedDt(DateTime.now().toDate());
        start.setUpdatedBy(opby);
        start.setUpdatedDt(DateTime.now().toDate());
        processNodeMapper.insertSelective(start);
        nodes.add(start);

        ProcessNode companyApprovalNode = new ProcessNode();
        companyApprovalNode.setNodeName("运营管理员审批");
        companyApprovalNode.setDescription("运营管理员审批[默认]");

        companyApprovalNode.setProcessId(process.getId());
        companyApprovalNode.setVersionId(processVersion.getId());
        companyApprovalNode.setStatus(3);
        companyApprovalNode.setNodeType(NodeTypeEnum.USERTASK.getType());
        companyApprovalNode.setCreatedBy(opby);
        companyApprovalNode.setCreatedDt(DateTime.now().toDate());
        companyApprovalNode.setUpdatedBy(opby);
        companyApprovalNode.setUpdatedDt(DateTime.now().toDate());
        companyApprovalNode.setSortNum(100.0f);
        //设置节点审批候选人/组的角色
        ProcessNodeConfig auditConfigFirst = new ProcessNodeConfig();
        auditConfigFirst.setCandidates(Arrays.asList(new ProcessNodeRoleDto(
                        "10-platform", "运营管理员", "301", 0, null),
                new ProcessNodeRoleDto(
                        "10-platform", "运维管理员", "305", 0, null)));
        auditConfigFirst.setNotifyWays(Arrays.asList("station", "mail"));
        companyApprovalNode.setConfigData(JSON.toJSONString(auditConfigFirst));

        processNodeMapper.insertSelective(companyApprovalNode);
        nodes.add(companyApprovalNode);

        ProcessNode openService = new ProcessNode();
        openService.setProcessId(process.getId());
        openService.setVersionId(processVersion.getId());
        openService.setNodeName("执行");
        openService.setNodeType(NodeTypeEnum.SERVICE.getType());
        openService.setDescription("服务开通");
        openService.setSortNum(200.0f);
        openService.setStatus(1);
        openService.setConfigData("{}");
        openService.setCreatedBy(opby);
        openService.setCreatedDt(DateTime.now().toDate());
        openService.setUpdatedBy(opby);
        openService.setUpdatedDt(DateTime.now().toDate());
        processNodeMapper.insertSelective(openService);
        nodes.add(openService);

        ProcessNode rejectNode = new ProcessNode();
        rejectNode.setProcessId(process.getId());
        rejectNode.setVersionId(processVersion.getId());
        rejectNode.setNodeName("拒绝并关闭");
        rejectNode.setNodeType(NodeTypeEnum.SERVICE.getType());
        rejectNode.setDescription("用户审批拒绝");
        rejectNode.setSortNum(300.0f);
        rejectNode.setStatus(2);
        rejectNode.setConfigData("{}");
        rejectNode.setCreatedBy(opby);
        rejectNode.setCreatedDt(DateTime.now().toDate());
        rejectNode.setUpdatedBy(opby);
        rejectNode.setUpdatedDt(DateTime.now().toDate());
        processNodeMapper.insertSelective(rejectNode);
        nodes.add(rejectNode);

        ProcessNode end = new ProcessNode();
        end.setProcessId(process.getId());
        end.setVersionId(processVersion.getId());
        end.setNodeName("结束");
        end.setNodeType(NodeTypeEnum.END.getType());
        end.setDescription("流程结束");
        end.setSortNum(400.0f);
        end.setStatus(0);
        end.setConfigData("{}");
        end.setCreatedBy(opby);
        end.setCreatedDt(DateTime.now().toDate());
        end.setUpdatedBy(opby);
        end.setUpdatedDt(DateTime.now().toDate());
        processNodeMapper.insertSelective(end);
        nodes.add(end);

        deployProcess(process, processVersion, nodes);
    }

    private void initAutoServerPublishBusinessProcess(String businessCode) {
        //添加流程
        String opby = "admin";
        Process process = new Process();
        process.setProcessName(ProcessConstants.BUSINESS_MAP.get(businessCode));
        process.setBusinessCode(businessCode);
        process.setDescription(process.getProcessName() + "[默认流程]");
        IdGen idGen = IdGen.get();
        process.setProcessCode("approval-" + idGen.nextId());
        process.setCreatedBy(opby);
        process.setCreatedDt(DateTime.now().toDate());
        process.setUpdatedBy(opby);
        process.setUpdatedDt(DateTime.now().toDate());
        process.setOrgSid(0L);
        process.setStatus(0);
        process.setEntryId(1L);
        //插入流程的基本信息
        processMapper.insertSelective(process);

        //流程版本
        ProcessVersion processVersion = new ProcessVersion();
        processVersion.setProcessId(process.getId());
        processVersion.setVersionName(process.getProcessName());
        processVersion.setDescription(process.getDescription());
        processVersion.setIsdefault(true);
        processVersion.setStatus(0);
        processVersion.setVersion(1);
        processVersion.setCreatedBy(opby);
        processVersion.setCreatedDt(DateTime.now().toDate());
        processVersion.setUpdatedBy(opby);
        processVersion.setUpdatedDt(DateTime.now().toDate());
        //插入流程版本的基本信息
        processVersionMapper.insertSelective(processVersion);

        //添加流程节点
        List<ProcessNode> nodes = Lists.newArrayList();
        ProcessNode start = new ProcessNode();
        start.setProcessId(process.getId());
        start.setVersionId(processVersion.getId());
        start.setNodeName("开始");
        start.setNodeType(NodeTypeEnum.START.getType());
        start.setDescription("流程开始");
        start.setSortNum(0.0f);
        start.setStatus(0);
        start.setConfigData("{}");
        start.setCreatedBy(opby);
        start.setCreatedDt(DateTime.now().toDate());
        start.setUpdatedBy(opby);
        start.setUpdatedDt(DateTime.now().toDate());
        processNodeMapper.insertSelective(start);
        nodes.add(start);

        ProcessNode companyApprovalNode = new ProcessNode();
        companyApprovalNode.setNodeName("系统自动审批");
        companyApprovalNode.setDescription("系统自动审批[默认]");

        companyApprovalNode.setProcessId(process.getId());
        companyApprovalNode.setVersionId(processVersion.getId());
        companyApprovalNode.setStatus(3);
        companyApprovalNode.setNodeType(NodeTypeEnum.USERTASK.getType());
        companyApprovalNode.setCreatedBy(opby);
        companyApprovalNode.setCreatedDt(DateTime.now().toDate());
        companyApprovalNode.setUpdatedBy(opby);
        companyApprovalNode.setUpdatedDt(DateTime.now().toDate());
        companyApprovalNode.setSortNum(100.0f);
        //设置节点审批候选人/组的角色
        ProcessNodeConfig auditConfigFirst = new ProcessNodeConfig();
        auditConfigFirst.setCandidates(Arrays.asList(new ProcessNodeRoleDto(
                        "10-platform", "运营管理员", "301", 0, null),
                new ProcessNodeRoleDto(
                        "10-platform", "运维管理员", "305", 0, null)));
        auditConfigFirst.setNotifyWays(Arrays.asList("station", "mail"));
        companyApprovalNode.setConfigData(JSON.toJSONString(auditConfigFirst));

        processNodeMapper.insertSelective(companyApprovalNode);
        nodes.add(companyApprovalNode);

        ProcessNode openService = new ProcessNode();
        openService.setProcessId(process.getId());
        openService.setVersionId(processVersion.getId());
        openService.setNodeName("执行");
        openService.setNodeType(NodeTypeEnum.SERVICE.getType());
        openService.setDescription("服务开通");
        openService.setSortNum(200.0f);
        openService.setStatus(1);
        openService.setConfigData("{}");
        openService.setCreatedBy(opby);
        openService.setCreatedDt(DateTime.now().toDate());
        openService.setUpdatedBy(opby);
        openService.setUpdatedDt(DateTime.now().toDate());
        processNodeMapper.insertSelective(openService);
        nodes.add(openService);

        ProcessNode rejectNode = new ProcessNode();
        rejectNode.setProcessId(process.getId());
        rejectNode.setVersionId(processVersion.getId());
        rejectNode.setNodeName("拒绝并关闭");
        rejectNode.setNodeType(NodeTypeEnum.SERVICE.getType());
        rejectNode.setDescription("用户审批拒绝");
        rejectNode.setSortNum(300.0f);
        rejectNode.setStatus(2);
        rejectNode.setConfigData("{}");
        rejectNode.setCreatedBy(opby);
        rejectNode.setCreatedDt(DateTime.now().toDate());
        rejectNode.setUpdatedBy(opby);
        rejectNode.setUpdatedDt(DateTime.now().toDate());
        processNodeMapper.insertSelective(rejectNode);
        nodes.add(rejectNode);

        ProcessNode end = new ProcessNode();
        end.setProcessId(process.getId());
        end.setVersionId(processVersion.getId());
        end.setNodeName("结束");
        end.setNodeType(NodeTypeEnum.END.getType());
        end.setDescription("流程结束");
        end.setSortNum(400.0f);
        end.setStatus(0);
        end.setConfigData("{}");
        end.setCreatedBy(opby);
        end.setCreatedDt(DateTime.now().toDate());
        end.setUpdatedBy(opby);
        end.setUpdatedDt(DateTime.now().toDate());
        processNodeMapper.insertSelective(end);
        nodes.add(end);

        deployProcess(process, processVersion, nodes);
    }

    private void updateProcessStatus(Long processId, Integer status) {
        Process process = new Process();
        process.setId(processId);
        process.setStatus(status);

        WebUserUtil.prepareUpdateParams(process);
        processMapper.updateByPrimaryKeySelective(process);
    }

    private void initServerReleaseBusinessProcess(String businessCode) {
        //添加流程
        String opby = "admin";
        Process process = new Process();
        process.setProcessName(ProcessConstants.BUSINESS_MAP.get(businessCode));
        process.setBusinessCode(businessCode);
        process.setDescription(process.getProcessName() + "[默认流程]");
        IdGen idGen = IdGen.get();
        process.setProcessCode("approval-" + idGen.nextId());
        process.setCreatedBy(opby);
        process.setCreatedDt(DateTime.now().toDate());
        process.setUpdatedBy(opby);
        process.setUpdatedDt(DateTime.now().toDate());
        process.setOrgSid(0L);
        process.setStatus(0);
        //插入流程的基本信息
        processMapper.insertSelective(process);

        //流程版本
        ProcessVersion processVersion = new ProcessVersion();
        processVersion.setProcessId(process.getId());
        processVersion.setVersionName(process.getProcessName());
        processVersion.setDescription(process.getDescription());
        processVersion.setIsdefault(true);
        processVersion.setStatus(0);
        processVersion.setVersion(1);
        processVersion.setCreatedBy(opby);
        processVersion.setCreatedDt(DateTime.now().toDate());
        processVersion.setUpdatedBy(opby);
        processVersion.setUpdatedDt(DateTime.now().toDate());
        processVersionMapper.insertSelective(processVersion);

        List<ProcessNode> nodes = Lists.newArrayList();

        ProcessNode start = new ProcessNode();
        start.setProcessId(process.getId());
        start.setVersionId(processVersion.getId());
        start.setNodeName("开始");
        start.setNodeType(NodeTypeEnum.START.getType());
        start.setDescription("流程开始");
        start.setSortNum(0.0f);
        start.setStatus(0);
        start.setConfigData("{}");
        start.setCreatedBy(opby);
        start.setCreatedDt(DateTime.now().toDate());
        start.setUpdatedBy(opby);
        start.setUpdatedDt(DateTime.now().toDate());
        processNodeMapper.insertSelective(start);
        nodes.add(start);

        ProcessNode companyApprovalNode = new ProcessNode();
        companyApprovalNode.setNodeName("运营管理员审批");
        companyApprovalNode.setDescription("运营管理员审批[默认]");

        companyApprovalNode.setProcessId(process.getId());
        companyApprovalNode.setVersionId(processVersion.getId());
        companyApprovalNode.setStatus(3);
        companyApprovalNode.setNodeType(NodeTypeEnum.USERTASK.getType());
        companyApprovalNode.setCreatedBy(opby);
        companyApprovalNode.setCreatedDt(DateTime.now().toDate());
        companyApprovalNode.setUpdatedBy(opby);
        companyApprovalNode.setUpdatedDt(DateTime.now().toDate());
        companyApprovalNode.setSortNum(100.0f);
        //设置节点审批候选人/组的角色
        ProcessNodeConfig auditConfigFirst = new ProcessNodeConfig();
        auditConfigFirst.setCandidates(Arrays.asList(new ProcessNodeRoleDto(
                        "10-platform", "运营管理员", "301", 0, null),
                new ProcessNodeRoleDto(
                        "10-platform", "运维管理员", "305", 0, null)));
        auditConfigFirst.setNotifyWays(Arrays.asList("station", "mail"));
        companyApprovalNode.setConfigData(JSON.toJSONString(auditConfigFirst));

        processNodeMapper.insertSelective(companyApprovalNode);
        nodes.add(companyApprovalNode);

        ProcessNode openService = new ProcessNode();
        openService.setProcessId(process.getId());
        openService.setVersionId(processVersion.getId());
        openService.setNodeName("执行");
        openService.setNodeType(NodeTypeEnum.SERVICE.getType());
        openService.setDescription("服务退订");
        openService.setSortNum(200.0f);
        openService.setStatus(1);
        openService.setConfigData("{}");
        openService.setCreatedBy(opby);
        openService.setCreatedDt(DateTime.now().toDate());
        openService.setUpdatedBy(opby);
        openService.setUpdatedDt(DateTime.now().toDate());
        processNodeMapper.insertSelective(openService);
        nodes.add(openService);

        ProcessNode rejectNode = new ProcessNode();
        rejectNode.setProcessId(process.getId());
        rejectNode.setVersionId(processVersion.getId());
        rejectNode.setNodeName("拒绝并关闭");
        rejectNode.setNodeType(NodeTypeEnum.SERVICE.getType());
        rejectNode.setDescription("用户审批拒绝");
        rejectNode.setSortNum(300.0f);
        rejectNode.setStatus(2);
        rejectNode.setConfigData("{}");
        rejectNode.setCreatedBy(opby);
        rejectNode.setCreatedDt(DateTime.now().toDate());
        rejectNode.setUpdatedBy(opby);
        rejectNode.setUpdatedDt(DateTime.now().toDate());
        processNodeMapper.insertSelective(rejectNode);
        nodes.add(rejectNode);

        ProcessNode end = new ProcessNode();
        end.setProcessId(process.getId());
        end.setVersionId(processVersion.getId());
        end.setNodeName("结束");
        end.setNodeType(NodeTypeEnum.END.getType());
        end.setDescription("流程结束");
        end.setSortNum(400.0f);
        end.setStatus(0);
        end.setConfigData("{}");
        end.setCreatedBy(opby);
        end.setCreatedDt(DateTime.now().toDate());
        end.setUpdatedBy(opby);
        end.setUpdatedDt(DateTime.now().toDate());
        processNodeMapper.insertSelective(end);
        nodes.add(end);

        deployProcess(process, processVersion, nodes);
    }

    /**
     * ma 服务申请流程定义
     */
    private void initMAServerApplyBusinessProcess(String processType) {
        String opby = "admin";
        Process process = new Process();
        process.setProcessName(ProcessConstants.BUSINESS_MAP.get(processType));
        process.setBusinessCode(processType);
        process.setDescription(process.getProcessName() + "[默认流程]");
        IdGen idGen = IdGen.get();
        process.setProcessCode("approval-" + idGen.nextId());
        process.setCreatedBy(opby);
        process.setCreatedDt(DateTime.now().toDate());
        process.setUpdatedBy(opby);
        process.setUpdatedDt(DateTime.now().toDate());
        process.setOrgSid(0L);
        process.setStatus(0);

        processMapper.insertSelective(process);

        ProcessVersion processVersion = new ProcessVersion();
        processVersion.setProcessId(process.getId());
        processVersion.setVersionName(process.getProcessName());
        processVersion.setDescription(process.getDescription());
        processVersion.setIsdefault(true);
        processVersion.setStatus(0);
        processVersion.setVersion(1);
        processVersion.setCreatedBy(opby);
        processVersion.setCreatedDt(DateTime.now().toDate());
        processVersion.setUpdatedBy(opby);
        processVersion.setUpdatedDt(DateTime.now().toDate());
        processVersionMapper.insertSelective(processVersion);

        List<ProcessNode> nodes = Lists.newArrayList();

        ProcessNode start = new ProcessNode();
        start.setProcessId(process.getId());
        start.setVersionId(processVersion.getId());
        start.setNodeName("开始");
        start.setNodeType(NodeTypeEnum.START.getType());
        start.setDescription("流程开始");
        start.setSortNum(0.0f);
        start.setStatus(0);
        start.setConfigData("{}");
        start.setCreatedBy(opby);
        start.setCreatedDt(DateTime.now().toDate());
        start.setUpdatedBy(opby);
        start.setUpdatedDt(DateTime.now().toDate());
        processNodeMapper.insertSelective(start);
        nodes.add(start);

        ProcessNode companyApprovalNode = new ProcessNode();
        companyApprovalNode.setNodeName("运营管理员预审批");
        companyApprovalNode.setDescription("运营管理员预审批[默认]");

        companyApprovalNode.setProcessId(process.getId());
        companyApprovalNode.setVersionId(processVersion.getId());
        companyApprovalNode.setStatus(3);
        companyApprovalNode.setNodeType(NodeTypeEnum.USERTASK.getType());
        companyApprovalNode.setCreatedBy(opby);
        companyApprovalNode.setCreatedDt(DateTime.now().toDate());
        companyApprovalNode.setUpdatedBy(opby);
        companyApprovalNode.setUpdatedDt(DateTime.now().toDate());
        companyApprovalNode.setSortNum(100.0f);

        ProcessNodeConfig auditConfigFirst = new ProcessNodeConfig();
        List<String> roleIds = roleService.findRoleIdsByDataScope(RequirePermissionEnum.DATA_SCOPE_ENTITY.getScope());
        List<ProcessNodeRoleDto> configData = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(roleIds)){
            roleIds.stream().forEach(r->{
                configData.add(new ProcessNodeRoleDto(
                        "10-platform", "运营管理员", r, 0, null));
            });
        }
        auditConfigFirst.setCandidates(configData);

        auditConfigFirst.setNotifyWays(Arrays.asList("station"));
        companyApprovalNode.setConfigData(JSON.toJSONString(auditConfigFirst));

        processNodeMapper.insertSelective(companyApprovalNode);
        nodes.add(companyApprovalNode);

        //预审批通过
        companyApprovalNode = new ProcessNode();
        companyApprovalNode.setNodeName("运营管理员审批");
        companyApprovalNode.setDescription("运营管理员审批[默认]");

        companyApprovalNode.setProcessId(process.getId());
        companyApprovalNode.setVersionId(processVersion.getId());
        companyApprovalNode.setStatus(3);
        companyApprovalNode.setNodeType(NodeTypeEnum.USERTASK.getType());
        companyApprovalNode.setCreatedBy(opby);
        companyApprovalNode.setCreatedDt(DateTime.now().toDate());
        companyApprovalNode.setUpdatedBy(opby);
        companyApprovalNode.setUpdatedDt(DateTime.now().toDate());
        companyApprovalNode.setSortNum(200.0f);

        auditConfigFirst = new ProcessNodeConfig();
        List<String> roleIds1 = roleService.findRoleIdsByDataScope(RequirePermissionEnum.DATA_SCOPE_ENTITY.getScope());
        List<ProcessNodeRoleDto> configData1 = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(roleIds1)){
            roleIds1.stream().forEach(r->{
                configData1.add(new ProcessNodeRoleDto(
                        "10-platform", "运营管理员", r, 0, null));
            });
        }
        auditConfigFirst.setCandidates(configData1);
        auditConfigFirst.setNotifyWays(Arrays.asList("station", "mail"));
        companyApprovalNode.setConfigData(JSON.toJSONString(auditConfigFirst));

        processNodeMapper.insertSelective(companyApprovalNode);
        nodes.add(companyApprovalNode);


        ProcessNode openService = new ProcessNode();
        openService.setProcessId(process.getId());
        openService.setVersionId(processVersion.getId());
        openService.setNodeName("执行");
        openService.setNodeType(NodeTypeEnum.SERVICE.getType());
        openService.setDescription("服务开通");
        openService.setSortNum(300.0f);
        openService.setStatus(1);
        openService.setConfigData("{}");
        openService.setCreatedBy(opby);
        openService.setCreatedDt(DateTime.now().toDate());
        openService.setUpdatedBy(opby);
        openService.setUpdatedDt(DateTime.now().toDate());
        processNodeMapper.insertSelective(openService);
        nodes.add(openService);

        ProcessNode  rejectNode = new ProcessNode();
        rejectNode.setProcessId(process.getId());
        rejectNode.setVersionId(processVersion.getId());
        rejectNode.setNodeName("拒绝并关闭");
        rejectNode.setNodeType(NodeTypeEnum.SERVICE.getType());
        rejectNode.setDescription("用户审批拒绝");
        rejectNode.setSortNum(400.0f);
        rejectNode.setStatus(2);
        rejectNode.setConfigData("{}");
        rejectNode.setCreatedBy(opby);
        rejectNode.setCreatedDt(DateTime.now().toDate());
        rejectNode.setUpdatedBy(opby);
        rejectNode.setUpdatedDt(DateTime.now().toDate());
        processNodeMapper.insertSelective(rejectNode);
        nodes.add(rejectNode);

        ProcessNode end = new ProcessNode();
        end.setProcessId(process.getId());
        end.setVersionId(processVersion.getId());
        end.setNodeName("结束");
        end.setNodeType(NodeTypeEnum.END.getType());
        end.setDescription("流程结束");
        end.setSortNum(500.0f);
        end.setStatus(0);
        end.setConfigData("{}");
        end.setCreatedBy(opby);
        end.setCreatedDt(DateTime.now().toDate());
        end.setUpdatedBy(opby);
        end.setUpdatedDt(DateTime.now().toDate());
        processNodeMapper.insertSelective(end);
        nodes.add(end);

        deployProcess(process, processVersion, nodes);
    }


    /**
     * hpc 服务申请流程定义
     */
    private void initHPCServerApplyBusinessProcess(String processType) {
        //添加流程
        String opby = "admin";
        Process process = new Process();
        process.setProcessName(ProcessConstants.BUSINESS_MAP.get(processType));
        process.setBusinessCode(processType);
        process.setDescription(process.getProcessName() + "[默认流程]");
        IdGen idGen = IdGen.get();
        process.setProcessCode("approval-" + idGen.nextId());
        process.setCreatedBy(opby);
        process.setCreatedDt(DateTime.now().toDate());
        process.setUpdatedBy(opby);
        process.setUpdatedDt(DateTime.now().toDate());
        process.setOrgSid(0L);
        process.setStatus(0);
        //插入流程信息
        processMapper.insertSelective(process);
        //流程版本
        ProcessVersion processVersion = new ProcessVersion();
        processVersion.setProcessId(process.getId());
        processVersion.setVersionName(process.getProcessName());
        processVersion.setDescription(process.getDescription());
        processVersion.setIsdefault(true);
        processVersion.setStatus(0);
        processVersion.setVersion(1);
        processVersion.setCreatedBy(opby);
        processVersion.setCreatedDt(DateTime.now().toDate());
        processVersion.setUpdatedBy(opby);
        processVersion.setUpdatedDt(DateTime.now().toDate());
        processVersionMapper.insertSelective(processVersion);

        //添加流程节点
        List<ProcessNode> nodes = Lists.newArrayList();

        ProcessNode start = new ProcessNode();
        start.setProcessId(process.getId());
        start.setVersionId(processVersion.getId());
        start.setNodeName("开始");
        start.setNodeType(NodeTypeEnum.START.getType());
        start.setDescription("流程开始");
        start.setSortNum(0.0f);
        start.setStatus(0);
        start.setConfigData("{}");
        start.setCreatedBy(opby);
        start.setCreatedDt(DateTime.now().toDate());
        start.setUpdatedBy(opby);
        start.setUpdatedDt(DateTime.now().toDate());
        processNodeMapper.insertSelective(start);
        nodes.add(start);

        ProcessNode companyApprovalNode = new ProcessNode();
        companyApprovalNode.setNodeName("运营管理员预审批");
        companyApprovalNode.setDescription("运营管理员预审批[默认]");

        companyApprovalNode.setProcessId(process.getId());
        companyApprovalNode.setVersionId(processVersion.getId());
        companyApprovalNode.setStatus(3);
        companyApprovalNode.setNodeType(NodeTypeEnum.USERTASK.getType());
        companyApprovalNode.setCreatedBy(opby);
        companyApprovalNode.setCreatedDt(DateTime.now().toDate());
        companyApprovalNode.setUpdatedBy(opby);
        companyApprovalNode.setUpdatedDt(DateTime.now().toDate());
        companyApprovalNode.setSortNum(100.0f);
        //设置节点候选人/组的角色id
        ProcessNodeConfig auditConfigFirst = new ProcessNodeConfig();
        auditConfigFirst.setCandidates(Arrays.asList(new ProcessNodeRoleDto(
                                                             "10-platform", "运营管理员", "301", 0, null),
                                                     new ProcessNodeRoleDto(
                                                             "10-platform", "运维管理员", "305", 0, null)));
        auditConfigFirst.setNotifyWays(Arrays.asList("station"));
        companyApprovalNode.setConfigData(JSON.toJSONString(auditConfigFirst));

        processNodeMapper.insertSelective(companyApprovalNode);
        nodes.add(companyApprovalNode);

        //预审批通过
        companyApprovalNode = new ProcessNode();
        companyApprovalNode.setNodeName("运营管理员审批");
        companyApprovalNode.setDescription("运营管理员审批[默认]");

        companyApprovalNode.setProcessId(process.getId());
        companyApprovalNode.setVersionId(processVersion.getId());
        companyApprovalNode.setStatus(3);
        companyApprovalNode.setNodeType(NodeTypeEnum.USERTASK.getType());
        companyApprovalNode.setCreatedBy(opby);
        companyApprovalNode.setCreatedDt(DateTime.now().toDate());
        companyApprovalNode.setUpdatedBy(opby);
        companyApprovalNode.setUpdatedDt(DateTime.now().toDate());
        companyApprovalNode.setSortNum(200.0f);

        auditConfigFirst = new ProcessNodeConfig();
        auditConfigFirst.setCandidates(Arrays.asList(new ProcessNodeRoleDto(
                                                             "10-platform", "运营管理员", "301", 0, null),
                                                     new ProcessNodeRoleDto(
                                                             "10-platform", "运维管理员", "305", 0, null)));
        auditConfigFirst.setNotifyWays(Arrays.asList("station", "mail"));
        companyApprovalNode.setConfigData(JSON.toJSONString(auditConfigFirst));

        processNodeMapper.insertSelective(companyApprovalNode);
        nodes.add(companyApprovalNode);


        ProcessNode openService = new ProcessNode();
        openService.setProcessId(process.getId());
        openService.setVersionId(processVersion.getId());
        openService.setNodeName("执行");
        openService.setNodeType(NodeTypeEnum.SERVICE.getType());
        openService.setDescription("服务开通");
        openService.setSortNum(300.0f);
        openService.setStatus(1);
        openService.setConfigData("{}");
        openService.setCreatedBy(opby);
        openService.setCreatedDt(DateTime.now().toDate());
        openService.setUpdatedBy(opby);
        openService.setUpdatedDt(DateTime.now().toDate());
        processNodeMapper.insertSelective(openService);
        nodes.add(openService);

        ProcessNode rejectNode = new ProcessNode();
        rejectNode.setProcessId(process.getId());
        rejectNode.setVersionId(processVersion.getId());
        rejectNode.setNodeName("拒绝并关闭");
        rejectNode.setNodeType(NodeTypeEnum.SERVICE.getType());
        rejectNode.setDescription("用户审批拒绝");
        rejectNode.setSortNum(400.0f);
        rejectNode.setStatus(2);
        rejectNode.setConfigData("{}");
        rejectNode.setCreatedBy(opby);
        rejectNode.setCreatedDt(DateTime.now().toDate());
        rejectNode.setUpdatedBy(opby);
        rejectNode.setUpdatedDt(DateTime.now().toDate());
        processNodeMapper.insertSelective(rejectNode);
        nodes.add(rejectNode);

        ProcessNode end = new ProcessNode();
        end.setProcessId(process.getId());
        end.setVersionId(processVersion.getId());
        end.setNodeName("结束");
        end.setNodeType(NodeTypeEnum.END.getType());
        end.setDescription("流程结束");
        end.setSortNum(500.0f);
        end.setStatus(0);
        end.setConfigData("{}");
        end.setCreatedBy(opby);
        end.setCreatedDt(DateTime.now().toDate());
        end.setUpdatedBy(opby);
        end.setUpdatedDt(DateTime.now().toDate());
        processNodeMapper.insertSelective(end);
        nodes.add(end);
        //部署流程
        deployProcess(process, processVersion, nodes);
    }



    /**
     * hpc 服务申请流程定义
     */
    @Override
    public void initTestServerApplyBusinessProcess() {
        String opby = "admin";
        Process process = new Process();
        process.setProcessName("测试流程");
        process.setBusinessCode("test-process");
        process.setDescription(process.getProcessName() + "[默认流程]");
        IdGen idGen = IdGen.get();
        process.setProcessCode("approval-" + idGen.nextId());
        process.setCreatedBy(opby);
        process.setCreatedDt(DateTime.now().toDate());
        process.setUpdatedBy(opby);
        process.setUpdatedDt(DateTime.now().toDate());
        process.setOrgSid(0L);
        process.setStatus(0);

        processMapper.insertSelective(process);

        ProcessVersion processVersion = new ProcessVersion();
        processVersion.setProcessId(process.getId());
        processVersion.setVersionName(process.getProcessName());
        processVersion.setDescription(process.getDescription());
        processVersion.setIsdefault(true);
        processVersion.setStatus(0);
        processVersion.setVersion(1);
        processVersion.setCreatedBy(opby);
        processVersion.setCreatedDt(DateTime.now().toDate());
        processVersion.setUpdatedBy(opby);
        processVersion.setUpdatedDt(DateTime.now().toDate());
        processVersionMapper.insertSelective(processVersion);

        List<ProcessNode> nodes = Lists.newArrayList();

        ProcessNode start = new ProcessNode();
        start.setProcessId(process.getId());
        start.setVersionId(processVersion.getId());
        start.setNodeName("开始");
        start.setNodeType(NodeTypeEnum.START.getType());
        start.setDescription("流程开始");
        start.setSortNum(0.0f);
        start.setStatus(0);
        start.setConfigData("{}");
        start.setCreatedBy(opby);
        start.setCreatedDt(DateTime.now().toDate());
        start.setUpdatedBy(opby);
        start.setUpdatedDt(DateTime.now().toDate());
        processNodeMapper.insertSelective(start);
        nodes.add(start);

        ProcessNode companyApprovalNode = new ProcessNode();
        companyApprovalNode.setNodeName("运营管理员预审批");
        companyApprovalNode.setDescription("运营管理员预审批[默认]");

        companyApprovalNode.setProcessId(process.getId());
        companyApprovalNode.setVersionId(processVersion.getId());
        companyApprovalNode.setStatus(3);
        companyApprovalNode.setNodeType(NodeTypeEnum.USERTASK.getType());
        companyApprovalNode.setCreatedBy(opby);
        companyApprovalNode.setCreatedDt(DateTime.now().toDate());
        companyApprovalNode.setUpdatedBy(opby);
        companyApprovalNode.setUpdatedDt(DateTime.now().toDate());
        companyApprovalNode.setSortNum(100.0f);

        ProcessNodeConfig auditConfigFirst = new ProcessNodeConfig();
        auditConfigFirst.setCandidates(Arrays.asList(new ProcessNodeRoleDto(
                                                             "10-platform", "运营管理员", "301", 0, null),
                                                     new ProcessNodeRoleDto(
                                                             "10-platform", "运维管理员", "305", 0, null)));
        auditConfigFirst.setNotifyWays(Arrays.asList("station", "mail"));
        companyApprovalNode.setConfigData(JSON.toJSONString(auditConfigFirst));

        processNodeMapper.insertSelective(companyApprovalNode);
        nodes.add(companyApprovalNode);

        //预审批通过
        companyApprovalNode = new ProcessNode();
        companyApprovalNode.setNodeName("运营管理员审批");
        companyApprovalNode.setDescription("运营管理员审批[默认]");

        companyApprovalNode.setProcessId(process.getId());
        companyApprovalNode.setVersionId(processVersion.getId());
        companyApprovalNode.setStatus(3);
        companyApprovalNode.setNodeType(NodeTypeEnum.USERTASK.getType());
        companyApprovalNode.setCreatedBy(opby);
        companyApprovalNode.setCreatedDt(DateTime.now().toDate());
        companyApprovalNode.setUpdatedBy(opby);
        companyApprovalNode.setUpdatedDt(DateTime.now().toDate());
        companyApprovalNode.setSortNum(200.0f);

        auditConfigFirst = new ProcessNodeConfig();
        auditConfigFirst.setCandidates(Arrays.asList(new ProcessNodeRoleDto(
                                                             "10-platform", "运营管理员", "301", 0, null),
                                                     new ProcessNodeRoleDto(
                                                             "10-platform", "运维管理员", "305", 0, null)));
        auditConfigFirst.setNotifyWays(Arrays.asList("station"));
        companyApprovalNode.setConfigData(JSON.toJSONString(auditConfigFirst));

        processNodeMapper.insertSelective(companyApprovalNode);
        nodes.add(companyApprovalNode);


        ProcessNode openService = new ProcessNode();
        openService.setProcessId(process.getId());
        openService.setVersionId(processVersion.getId());
        openService.setNodeName("执行");
        openService.setNodeType(NodeTypeEnum.SERVICE.getType());
        openService.setDescription("服务开通");
        openService.setSortNum(300.0f);
        openService.setStatus(1);
        openService.setConfigData("{}");
        openService.setCreatedBy(opby);
        openService.setCreatedDt(DateTime.now().toDate());
        openService.setUpdatedBy(opby);
        openService.setUpdatedDt(DateTime.now().toDate());
        processNodeMapper.insertSelective(openService);
        nodes.add(openService);

        ProcessNode rejectNode = new ProcessNode();
        rejectNode.setProcessId(process.getId());
        rejectNode.setVersionId(processVersion.getId());
        rejectNode.setNodeName("拒绝并关闭");
        rejectNode.setNodeType(NodeTypeEnum.SERVICE.getType());
        rejectNode.setDescription("用户审批拒绝");
        rejectNode.setSortNum(400.0f);
        rejectNode.setStatus(2);
        rejectNode.setConfigData("{}");
        rejectNode.setCreatedBy(opby);
        rejectNode.setCreatedDt(DateTime.now().toDate());
        rejectNode.setUpdatedBy(opby);
        rejectNode.setUpdatedDt(DateTime.now().toDate());
        processNodeMapper.insertSelective(rejectNode);
        nodes.add(rejectNode);

        ProcessNode end = new ProcessNode();
        end.setProcessId(process.getId());
        end.setVersionId(processVersion.getId());
        end.setNodeName("结束");
        end.setNodeType(NodeTypeEnum.END.getType());
        end.setDescription("流程结束");
        end.setSortNum(500.0f);
        end.setStatus(0);
        end.setConfigData("{}");
        end.setCreatedBy(opby);
        end.setCreatedDt(DateTime.now().toDate());
        end.setUpdatedBy(opby);
        end.setUpdatedDt(DateTime.now().toDate());
        processNodeMapper.insertSelective(end);
        nodes.add(end);

        deployProcess(process, processVersion, nodes);
    }


    private void initBusinessProcess(String businessCode, Company company) {
        Process process = new Process();
        process.setProcessName(ProcessConstants.BUSINESS_MAP.get(businessCode));
        process.setBusinessCode(businessCode);
        process.setDescription(process.getProcessName() + "[默认流程]");

        IdGen idGen = IdGen.get();

        process.setProcessCode("approval-" + idGen.nextId());
        process.setCreatedBy(company.getCreatedBy());
        process.setCreatedDt(DateTime.now().toDate());
        process.setUpdatedBy(company.getCreatedBy());
        process.setUpdatedDt(DateTime.now().toDate());
        process.setOrgSid(company.getOrgSid());
        process.setStatus(0);

        processMapper.insertSelective(process);

        ProcessVersion processVersion = new ProcessVersion();
        processVersion.setProcessId(process.getId());
        processVersion.setVersionName(process.getProcessName());
        processVersion.setDescription(process.getDescription());
        processVersion.setIsdefault(true);
        processVersion.setStatus(0);
        processVersion.setVersion(1);
        processVersion.setCreatedBy(company.getCreatedBy());
        processVersion.setCreatedDt(DateTime.now().toDate());
        processVersion.setUpdatedBy(company.getCreatedBy());
        processVersion.setUpdatedDt(DateTime.now().toDate());
        processVersionMapper.insertSelective(processVersion);

        List<ProcessNode> nodes = Lists.newArrayList();

        ProcessNode start = new ProcessNode();
        start.setProcessId(process.getId());
        start.setVersionId(processVersion.getId());
        start.setNodeName("开始");
        start.setNodeType(NodeTypeEnum.START.getType());
        start.setDescription("流程开始");
        start.setSortNum(0.0f);
        start.setStatus(0);
        start.setConfigData("{}");
        start.setNodeLevel("1");
        start.setCreatedBy(company.getCreatedBy());
        start.setCreatedDt(DateTime.now().toDate());
        start.setUpdatedBy(company.getCreatedBy());
        start.setUpdatedDt(DateTime.now().toDate());
        processNodeMapper.insertSelective(start);
        nodes.add(start);



        ProcessNode companyApprovalNode = new ProcessNode();
        companyApprovalNode.setNodeName("组织管理员审批");
        companyApprovalNode.setDescription("组织管理员审批[默认]");

        companyApprovalNode.setProcessId(process.getId());
        companyApprovalNode.setVersionId(processVersion.getId());
        companyApprovalNode.setStatus(3);
        companyApprovalNode.setNodeType(NodeTypeEnum.USERTASK.getType());
        companyApprovalNode.setCreatedBy(company.getCreatedBy());
        companyApprovalNode.setCreatedDt(DateTime.now().toDate());
        companyApprovalNode.setUpdatedBy(company.getCreatedBy());
        companyApprovalNode.setUpdatedDt(DateTime.now().toDate());
        companyApprovalNode.setSortNum(100.0f);

        ProcessNodeConfig auditConfigFirst = new ProcessNodeConfig();
        auditConfigFirst.setCandidates(Arrays.asList(new ProcessNodeRoleDto(
                "10-platform", "组织管理员", "101", 0, null)));
        auditConfigFirst.setNotifyWays(Arrays.asList("station"));
        companyApprovalNode.setConfigData(JSON.toJSONString(auditConfigFirst));

        processNodeMapper.insertSelective(companyApprovalNode);
        nodes.add(companyApprovalNode);

        ProcessNode openService = new ProcessNode();
        openService.setProcessId(process.getId());
        openService.setVersionId(processVersion.getId());
        openService.setNodeName("执行");
        openService.setNodeType(NodeTypeEnum.SERVICE.getType());
        openService.setDescription("服务开通");
        openService.setSortNum(200.0f);
        openService.setStatus(1);
        openService.setConfigData("{}");
        openService.setCreatedBy(company.getCreatedBy());
        openService.setCreatedDt(DateTime.now().toDate());
        openService.setUpdatedBy(company.getCreatedBy());
        openService.setUpdatedDt(DateTime.now().toDate());
        processNodeMapper.insertSelective(openService);
        nodes.add(openService);

        ProcessNode rejectNode = new ProcessNode();
        rejectNode.setProcessId(process.getId());
        rejectNode.setVersionId(processVersion.getId());
        rejectNode.setNodeName("拒绝并关闭");
        rejectNode.setNodeType(NodeTypeEnum.SERVICE.getType());
        rejectNode.setDescription("用户审批拒绝");
        rejectNode.setSortNum(300.0f);
        rejectNode.setStatus(2);
        rejectNode.setConfigData("{}");
        rejectNode.setCreatedBy(company.getCreatedBy());
        rejectNode.setCreatedDt(DateTime.now().toDate());
        rejectNode.setUpdatedBy(company.getCreatedBy());
        rejectNode.setUpdatedDt(DateTime.now().toDate());
        processNodeMapper.insertSelective(rejectNode);
        nodes.add(rejectNode);

        ProcessNode end = new ProcessNode();
        end.setProcessId(process.getId());
        end.setVersionId(processVersion.getId());
        end.setNodeName("结束");
        end.setNodeType(NodeTypeEnum.END.getType());
        end.setDescription("流程结束");
        end.setSortNum(400.0f);
        end.setStatus(0);
        end.setConfigData("{}");
        end.setCreatedBy(company.getCreatedBy());
        end.setCreatedDt(DateTime.now().toDate());
        end.setUpdatedBy(company.getCreatedBy());
        end.setUpdatedDt(DateTime.now().toDate());
        processNodeMapper.insertSelective(end);
        nodes.add(end);

        deployProcess(process, processVersion, nodes);
    }

    /**
     * 服务定义
     * @param process
     * @param processVersion
     * @param originNodes
     * @return
     */
    Deployment deployProcess(Process process, ProcessVersion processVersion, List<ProcessNode> originNodes) {
        IdGen idGen = IdGen.get();

        // 拷贝用户任务对象
        List<ProcessNode> nodes = originNodes.stream().map(node -> {
            if (node.getNodeType() == NodeTypeEnum.USERTASK.getType()) {
                try {
                    ProcessNode cloneNode = (ProcessNode) node.clone();

                    cloneNode.setStatus(103);
                    cloneNode.setId(null);

                    return cloneNode;
                } catch (CloneNotSupportedException e) {
                    return node;
                }
            }

            return node;
        }).filter(node -> {
            if (node.getNodeType().equals(NodeTypeEnum.SERVICE.getType())) {
                if (ProcessConstants.TICKET.equals(process.getBusinessCode())) {
                    if (!node.getStatus().equals(1)) {
                        return false;
                    }
                }
            }

            return true;
        }).collect(Collectors.toList());

        // 持久化用户任务对象 (当用户更新或删除原节点后，任务配置信息保持不变)
        for (ProcessNode node : nodes) {
            if (node.getNodeType() == NodeTypeEnum.USERTASK.getType()) {
                processNodeMapper.insertSelective(node);
            }
        }

        Map<Long, FlowElement> flows = Maps.newHashMap();
        Map<Long, FlowElement> flowsAdditionals = Maps.newHashMap();

        //1、创建一个空的 BPMN 模型实例
        BpmnModelInstance model = Bpmn.createEmptyModel();

        //2、创建BPMN定义元素，设置目标名称空间，并将其添加到新创建的模型实例中
        Definitions definitions = model.newInstance(Definitions.class);
        definitions.setTargetNamespace(process.getBusinessCode());
        model.setDefinitions(definitions);
        // di-对于图，需要创建一个图和一个平面元素。平面被设置在一个图对象中，图被添加为子元素
        BpmnDiagram bpmnDiagram = model.newInstance(BpmnDiagram.class);
        BpmnPlane plane = model.newInstance(BpmnPlane.class);
        bpmnDiagram.setBpmnPlane(plane);
        definitions.addChildElement(bpmnDiagram);

        //添加流程
        org.camunda.bpm.model.bpmn.instance.Process actProcess =
                model.newInstance(org.camunda.bpm.model.bpmn.instance.Process.class);
        definitions.addChildElement(actProcess);
        //设置流程的ID
        actProcess.setId(process.getProcessCode());
        //设置流程的名称
        actProcess.setName(process.getProcessName());
        actProcess.setExecutable(true);
        // di-平面元素添加process
        plane.setBpmnElement(actProcess);

        for (ProcessNode node : nodes) {
            if (node.getNodeType().equals(NodeTypeEnum.START.getType())) {
                //创建开始事件
                StartEvent startEvent = createElement(actProcess, idGen.nextStringId("act-"), StartEvent.class);
                FlowElement startFlow = startEvent(startEvent, idGen.nextStringId("act-"), node);
                flows.put(node.getId(), startFlow);
            } else if (node.getNodeType().equals(NodeTypeEnum.GATEWAY.getType())) {
                // 预留(角色网关)
            } else if (node.getNodeType().equals(NodeTypeEnum.USERTASK.getType())) {
                //创建用户任务
                UserTask userTask = createElement(actProcess, idGen.nextStringId("act-"), UserTask.class);
                FlowElement taskFlow = taskEvent(userTask, idGen.nextStringId("act-"), node);
                flows.put(node.getId(), taskFlow);

                if (!ProcessConstants.TICKET.equals(process.getBusinessCode())) {
                    //创建工单任务
                    ExclusiveGateway exclusiveGateway = createElement(actProcess, idGen.nextStringId("act-"),
                                                                      ExclusiveGateway.class);
                    FlowElement whetherFlow = gatewayEvent(exclusiveGateway, idGen.nextStringId("act-"), node);
                    flowsAdditionals.put(node.getId(), whetherFlow);
                }
            } else if (node.getNodeType().equals(NodeTypeEnum.SERVICE.getType())) {
                //创建服务任务
                ServiceTask serviceTask = createElement(actProcess, idGen.nextStringId("act-"), ServiceTask.class);
                FlowElement openFlow;
                if (node.getStatus().equals(1)) {
                    openFlow = serviceOpenEvent(serviceTask, idGen.nextStringId("act-"), node);
                    flows.put(node.getId(), openFlow);
                } else {
                    if (!ProcessConstants.TICKET.equals(process.getBusinessCode())) {
                        openFlow = serviceCloseEvent(serviceTask, idGen.nextStringId("act-"), node);
                        flows.put(node.getId(), openFlow);
                    }
                }
            } else if (node.getNodeType().equals(NodeTypeEnum.END.getType())) {
                //创建结束事件
                EndEvent element = createElement(actProcess, idGen.nextStringId("act-"), EndEvent.class);
                FlowElement endFlow = endEvent(element, idGen.nextStringId("act-"), node);
                flows.put(node.getId(), endFlow);
            }
        }
        //链接流程元素和序列流
        for (int i = 0; i < nodes.size(); i++) {
            ProcessNode node = nodes.get(i);

            if (node.getNodeType().equals(NodeTypeEnum.START.getType())) {
                //开始时间
                FlowNode startFlow = (FlowNode) flows.get(node.getId());
                FlowNode nextFlow = (FlowNode) flows.get(nodes.get(i + 1).getId());
                createSequenceFlow(actProcess, startFlow, nextFlow);
            } else if (node.getNodeType().equals(NodeTypeEnum.GATEWAY.getType())) {
                // 预留(角色网关)
            } else if (node.getNodeType().equals(NodeTypeEnum.USERTASK.getType())) {
                if (!ProcessConstants.TICKET.equals(process.getBusinessCode())) {
                    FlowNode userFlow = (FlowNode) flows.get(node.getId());
                    FlowNode wheatherFlow = (FlowNode) flowsAdditionals.get(node.getId());
                    createSequenceFlow(actProcess, userFlow, wheatherFlow);

                    //设置条件
                    FlowNode passFlow = (FlowNode) flows.get(nodes.get(i + 1).getId());
                    addCondition(createSequenceFlow(actProcess, wheatherFlow, passFlow),
                                 "通过", "${audit=='pass'}", model);

                    if (i == 1) {
                        ProcessNode rejectNode = findRejectNode(nodes);
                        FlowNode rejectFlow = (FlowNode) flows.get(rejectNode.getId());
                        addCondition(createSequenceFlow(actProcess, wheatherFlow, rejectFlow),
                                     "拒绝", "${audit=='reject'}", model);
                    }

                    if (i > 1) {
                        // 第一个节点不设置驳回
                        for (int j = 1; j < i; j++) {
                            // 添加驳回条件 goback
                            FlowNode turndownFlow = (FlowNode) flows.get(nodes.get(j).getId());
                            addCondition(createSequenceFlow(actProcess, wheatherFlow, turndownFlow),
                                         "回退", String.format("${audit=='goback-%s'}", j), model);
                        }
                    }

                } else {
                    FlowNode userFlow = (FlowNode) flows.get(node.getId());
                    FlowNode nextFlow = (FlowNode) flows.get(nodes.get(i + 1).getId());
                    createSequenceFlow(actProcess, userFlow, nextFlow);
                }

            } else if (node.getNodeType().equals(NodeTypeEnum.SERVICE.getType())) {
                FlowNode serviceFlow = (FlowNode) flows.get(node.getId());
                FlowNode nextFlow = (FlowNode) flows.get(nodes.get(nodes.size() - 1).getId());
                createSequenceFlow(actProcess, serviceFlow, nextFlow);
            } else if (node.getNodeType().equals(NodeTypeEnum.END.getType())) {
                FlowNode endFlow = (FlowNode) flows.get(node.getId());
                FlowNode nextFlow = (FlowNode) flows.get(nodes.get(nodes.size() - 1).getId());
                createSequenceFlow(actProcess, endFlow, nextFlow);
            }
        }

        //验证流程模型
        Bpmn.validateModel(model);

        //部署流程
        Deployment deployment = repositoryService.createDeployment()
                                                 .addModelInstance(process.getProcessCode() + ".bpmn", model).deploy();

        processVersion.setDeploymentId(deployment.getId());
        //根据部署的id查询流程定义
        ProcessDefinition processDefinition = repositoryService
                .createProcessDefinitionQuery()
                .deploymentId(deployment.getId())
                .singleResult();
        processVersion.setProcessIdentify(processDefinition.getId());
        processVersionMapper.updateByPrimaryKeySelective(processVersion);

        for (ProcessNode node : nodes) {
            if (node.getNodeType() == NodeTypeEnum.USERTASK.getType()) {
                node.setProcessIdentify(processDefinition.getId());
                processNodeMapper.updateByPrimaryKeySelective(node);
            }
        }

        return deployment;
    }

    /**
     * 查找拒绝并关闭的流程节点
     *
     * @param processNodes
     */
    ProcessNode findRejectNode(List<ProcessNode> processNodes) {
        for (ProcessNode node : processNodes) {
            if (node.getStatus().equals(2)) {
                return node;
            }
        }

        return null;
    }

    StartEvent startEvent(StartEvent startEvent, String id, ProcessNode processNode) {
        startEvent.setId(id);
        startEvent.setName(processNode.getNodeName());
        return startEvent;
    }

    UserTask taskEvent(UserTask userTask, String id, ProcessNode processNode) {
        userTask.setId(id);
        userTask.setName(processNode.getNodeName());
        //设置默认的权限角色
        userTask.setCamundaCandidateUsersList(Arrays.asList("zhangsan", "lisi", "100"));
        ProcessNodeConfig config = JSON.parseObject(
                processNode.getConfigData(), ProcessNodeConfig.class);
        List<String> candidateList = Lists.newArrayList();
        List<ProcessNodeRoleDto> candidates = config.getCandidates();
        if (candidates != null && candidates.size() > 0) {
            List<String> roleList = candidates.stream().map(role -> role.getType()
                    + "-" + role.getRefId()).collect(Collectors.toList());
            candidateList.addAll(roleList);
        }
        List<ProcessNodeRoleDto> candidateThirds = config.getCandidateThirds();
        if (candidateThirds != null && candidateThirds.size() > 0) {
            List<String> userList = candidateThirds.stream()
                                                   .map(role ->
                                                                "201" + "-" + role.getRefId())
                                                   .collect(Collectors.toList());
            candidateList.addAll(userList);
        }

        if (candidateList.size() > 0) {
            String roleIds = candidateList.stream().collect(Collectors.joining(","));
            //回调根据角色查询有权限的候选人
            String condition = String.format("${processService.auditCandidateInlineMgts(execution, '%s')}", roleIds);
            userTask.setCamundaCandidateUsersList(Arrays.asList(condition));
            //回调根据角色查询有权限的候选组
            String conditionGroup = String.format("${processService.auditGroupInlineMgts(execution, '%s')}", roleIds);
            userTask.setCamundaCandidateGroupsList(Collections.singletonList(conditionGroup));
        } else {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1859455333));
        }

        if (Objects.nonNull(config.getNotifyWays()) && config.getNotifyWays().size() > 0) {
            // 站内信和提醒 监听器
            ExtensionElements extensionElements = this.createElement(userTask, "", ExtensionElements.class);
            CamundaTaskListener create = this.createElement(extensionElements, "", CamundaTaskListener.class);
            create.setCamundaDelegateExpression("#{TaskMessageListener}");
            create.setCamundaEvent("create");
            create.setTextContent(processNode.getId().toString());
            CamundaTaskListener complete = this.createElement(extensionElements, "", CamundaTaskListener.class);
            complete.setCamundaDelegateExpression("#{TaskMessageListener}");
            complete.setCamundaEvent("complete");
            userTask.setExtensionElements(extensionElements);
        }

        return userTask;
    }

    ExclusiveGateway gatewayEvent(ExclusiveGateway exclusiveGateway, String id, ProcessNode processNode) {
        exclusiveGateway.setId(id);
        exclusiveGateway.setName(processNode.getNodeName());

        return exclusiveGateway;
    }

    ServiceTask serviceOpenEvent(ServiceTask serviceTask, String id, ProcessNode processNode) {
        serviceTask.setId("open" + id);
        serviceTask.setName(processNode.getNodeName());
     /*   serviceTask.setCamundaDelegateExpression("#{expression}");
        serviceTask.setImplementation("#{processService.doOpen(execution)}");*/
        serviceTask.setCamundaDelegateExpression("#{MonitorJavaDelegate}");
        serviceTask.setImplementation("Delegate Expression");
        return serviceTask;
    }

    ServiceTask serviceCloseEvent(ServiceTask serviceTask, String id, ProcessNode processNode) {
        serviceTask.setId("close" + id);
        serviceTask.setName(processNode.getNodeName());
        /*serviceTask.setCamundaDelegateExpression("#{expression}");
        serviceTask.setImplementation("#{processService.doClose(execution)}");*/
        serviceTask.setCamundaDelegateExpression("#{MonitorJavaDelegate}");
        serviceTask.setImplementation("Delegate Expression");
        return serviceTask;
    }

    EndEvent endEvent(EndEvent endEvent, String id, ProcessNode processNode) {
        endEvent.setId(id);
        endEvent.setName(processNode.getNodeName());

        return endEvent;
    }

    SequenceFlow sequenceFlow(BpmnModelInstance model, FlowElement from, FlowElement to, String name,
                              String condition) {
        SequenceFlow flow = sequenceFlow(from, to, name, model);
        ConditionExpression conditionExpression = model.newInstance(ConditionExpression.class);
        conditionExpression.setTextContent(condition);
        flow.setConditionExpression(conditionExpression);
        return flow;
    }

    SequenceFlow sequenceFlow(FlowElement from, FlowElement to, String name, BpmnModelInstance model) {
        SequenceFlow flow = sequenceFlow(from, to, model);
        flow.setName(name);
        return flow;
    }

    SequenceFlow sequenceFlow(FlowElement from, FlowElement to, BpmnModelInstance model) {
        SequenceFlow flow = model.newInstance(SequenceFlow.class);
        flow.setSource((FlowNode) from);
        flow.setTarget((FlowNode) to);
        return flow;
    }

    final static Map<String, Integer> values = Maps.newHashMap();

    static {
        values.put("StartEvent", 1);
        values.put("ExclusiveGateway", 2);
        values.put("UserTask", 3);
        values.put("ServiceTask", 4);
        values.put("EndEvent", 5);
    }

    @Override
    public List<ProcessNode> deployedAuditNodeList(String processIdentify) {
        return processNodeMapper.selectAuditNodeByProcessIdentify(processIdentify);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cloneProcess(Process process,
                             cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser authUser) {
        // 克隆流程定义
//        Long companyId = roleService.getCompayIdByOrgSid(authUser.getOrgSid());

        // 检查流程定义是重名
        Process existsed = processMapper.selectByProcessName(process.getProcessName(),
                                                             RequestContextUtil.getEntityId());
        if (Objects.nonNull(existsed)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1149419381));
        }

        List<ProcessVersion> processVersions = processVersionMapper.selectByProcessId(process.getId());
        if (processVersions.size() < 1) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_408194781));
        }
        ProcessVersion originalVersion = processVersions.get(0);

        List<ProcessNode> originalNodes = processNodeMapper.selectByVersionId(originalVersion.getId());

        IdGen idGen = IdGen.get();

        process.setId(null);
        process.setProcessCode("approval-" + idGen.nextId());
        process.setCreatedBy(authUser.getAccount());
        process.setCreatedDt(DateTime.now().toDate());
        process.setUpdatedBy(authUser.getAccount());
        process.setUpdatedDt(DateTime.now().toDate());
//        process.setOrgSid(companyId);
        process.setStatus(0);

        processMapper.insertSelective(process);

        ProcessVersion processVersion = new ProcessVersion();
        processVersion.setProcessId(process.getId());
        processVersion.setVersionName(process.getProcessName());
        processVersion.setDescription(process.getDescription());
        processVersion.setIsdefault(true);
        processVersion.setStatus(0);
        processVersion.setVersion(1);
        processVersion.setCreatedBy(authUser.getAccount());
        processVersion.setCreatedDt(DateTime.now().toDate());
        processVersion.setUpdatedBy(authUser.getAccount());
        processVersion.setUpdatedDt(DateTime.now().toDate());
        processVersionMapper.insertSelective(processVersion);

        List<ProcessNode> nodes = Lists.newArrayList();
        for (ProcessNode node : originalNodes) {
            Long nodeId = node.getId();

            node.setId(null);
            node.setProcessId(process.getId());
            node.setVersionId(processVersion.getId());
            node.setCreatedBy(authUser.getAccount());
            node.setCreatedDt(DateTime.now().toDate());
            node.setUpdatedBy(authUser.getAccount());
            node.setUpdatedDt(DateTime.now().toDate());
            processNodeMapper.insertSelective(node);
            nodes.add(node);

            if (StringUtils.equals(node.getNodeFeature(), "parallel")) {
                // 克隆并行环节
                Criteria childCriteria = new Criteria("parentId", nodeId);
                childCriteria.put("status", 3);
                List<ProcessNode> childNodes = processNodeMapper.selectByParams(childCriteria);
                if (CollectionUtil.isEmpty(childNodes)) {
                    continue;
                }

                List<ProcessNode> cloneChildNodes = childNodes.stream().map(n -> {
                    ProcessNode childCloneNode;
                    try {
                        childCloneNode = (ProcessNode) n.clone();
                    } catch (Exception e) {
                        childCloneNode = n;
                    }

                    childCloneNode.setId(null);
                    childCloneNode.setParentId(node.getId());
                    childCloneNode.setProcessId(process.getId());
                    childCloneNode.setVersionId(processVersion.getId());
                    childCloneNode.setCreatedBy(authUser.getAccount());
                    childCloneNode.setCreatedDt(DateTime.now().toDate());
                    childCloneNode.setUpdatedBy(authUser.getAccount());
                    childCloneNode.setUpdatedDt(DateTime.now().toDate());
                    return childCloneNode;
                }).collect(Collectors.toList());

                for (ProcessNode cloneChildNode : cloneChildNodes) {
                    processNodeMapper.insertSelective(cloneChildNode);
                }
            }
        }

        processMgtDeployProcess(process, processVersion, nodes);
    }


    /**
     * 服务定义
     *
     * @param process
     * @param processVersion
     * @param originNodes
     */
    Deployment processMgtDeployProcess(Process process, ProcessVersion processVersion, List<ProcessNode> originNodes) {
        IdGen idGen = IdGen.get();

        Map<Long, List<ProcessNode>> childListMap = Maps.newHashMap();

        // 拷贝用户任务对象
        List<ProcessNode> nodes = originNodes.stream().map(node -> {
            if (node.getNodeType() == NodeTypeEnum.USERTASK.getType()
                    || node.getNodeType() == NodeTypeEnum.APPLYTASK.getType()) {
                try {
                    ProcessNode cloneNode = (ProcessNode) node.clone();
                    if (StringUtils.equals(node.getNodeFeature(), "parallel")) {
                        Criteria criteria = new Criteria("parentId", node.getId());
                        criteria.put("status", 3);
                        List<ProcessNode> childNodes = processNodeMapper.selectByParams(criteria);
                        childNodes = childNodes.stream().map(childNode -> {
                            try {
                                ProcessNode cloneChildNode = (ProcessNode) childNode.clone();

                                cloneChildNode.setId(null);
                                cloneChildNode.setStatus(103);
                                cloneChildNode.setOriginalNodeId(childNode.getId());

                                return cloneChildNode;
                            } catch (Exception ec) {
                                return childNode;
                            }
                        }).collect(Collectors.toList());

                        childListMap.put(node.getId(), childNodes);
                    }

                    cloneNode.setOriginalNodeId(node.getId());
                    cloneNode.setStatus(103);
                    cloneNode.setId(null);

                    return cloneNode;
                } catch (CloneNotSupportedException e) {
                    return node;
                }
            }

            return node;
        }).filter(node -> {
            if (node.getNodeType().equals(NodeTypeEnum.SERVICE.getType())) {
                if (ProcessConstants.TICKET.equals(process.getBusinessCode())) {
                    if (!node.getStatus().equals(1)) {
                        return false;
                    }
                }
            }

            return true;
        }).collect(Collectors.toList());

        // 持久化用户任务对象 (当用户更新或删除原节点后，任务配置信息保持不变)
        ProcessNode prevNode = null;
        for (ProcessNode node : nodes) {
            if (!(node.getNodeType().equals(NodeTypeEnum.USERTASK.getType())
                    || node.getNodeType() == NodeTypeEnum.APPLYTASK.getType())) {
                continue;
            }

            processNodeMapper.insertSelective(node);

            if (StringUtils.equals(node.getNodeFeature(), "parallel")) {
                List<ProcessNode> childNodes = childListMap.get(node.getOriginalNodeId());
                if (CollectionUtil.isNotEmpty(childNodes)) {
                    for (ProcessNode childNode : childNodes) {
//                        childNode.setCurrentDeployId(currentDeployId);
                        childNode.setParentId(node.getId());
                        processNodeMapper.insertSelective(childNode);
                    }
                }
            }

            prevNode = node;
        }

        Map<Long, FlowElement> flows = Maps.newHashMap();
        Map<Long, FlowElement> flowsAdditionals = Maps.newHashMap();
        Map<Long, FlowElement> parallelFlowsEndAdditionals = Maps.newHashMap();

        //1、创建一个空的 BPMN 模型实例
        BpmnModelInstance model = Bpmn.createEmptyModel();

        //2、创建BPMN定义元素，设置目标名称空间，并将其添加到新创建的模型实例中
        Definitions definitions = model.newInstance(Definitions.class);
        definitions.setTargetNamespace("http://camunda.org/examples");
        definitions.getDomElement().registerNamespace("camunda");
        model.setDefinitions(definitions);
        // di-对于图，需要创建一个图和一个平面元素。平面被设置在一个图对象中，图被添加为子元素
        BpmnDiagram bpmnDiagram = model.newInstance(BpmnDiagram.class);
        BpmnPlane plane = model.newInstance(BpmnPlane.class);
        bpmnDiagram.setBpmnPlane(plane);
        definitions.addChildElement(bpmnDiagram);

        //添加流程
        org.camunda.bpm.model.bpmn.instance.Process actProcess =
                model.newInstance(org.camunda.bpm.model.bpmn.instance.Process.class);

        definitions.addChildElement(actProcess);
        //设置流程的ID
        actProcess.setId(process.getProcessCode());
        //设置流程的名称
        actProcess.setName(process.getProcessName());
        actProcess.setExecutable(true);
        // di-平面元素添加process
        plane.setBpmnElement(actProcess);
        for (ProcessNode node : nodes) {
            ProcessNodeConfig config = JSON.parseObject(node.getConfigData(), ProcessNodeConfig.class);
            if (node.getNodeType().equals(NodeTypeEnum.START.getType())) {
                StartEvent startEvent = createElement(actProcess, idGen.nextStringId("act-"), StartEvent.class);
                FlowElement startFlow = startEvent(startEvent, idGen.nextStringId("act-"), node);
                flows.put(node.getId(), startFlow);
            } else if (node.getNodeType().equals(NodeTypeEnum.GATEWAY.getType())) {
                // 预留(角色网关)
            } else if (node.getNodeType().equals(NodeTypeEnum.USERTASK.getType())) {
                UserTask userTask = createElement(actProcess, idGen.nextStringId("act-"), UserTask.class);
                FlowElement taskFlow = processMgtTaskEvent(userTask, idGen.nextStringId("act-"), node);
                flows.put(node.getId(), taskFlow);
                List<ProcessNode> childNodes = childListMap.get(node.getOriginalNodeId());
                if (StringUtils.equals(node.getNodeFeature(), "parallel") && CollectionUtil.isNotEmpty(childNodes)) {
                    ParallelGateway parallelGateway = createElement(actProcess, idGen.nextStringId("act-"),
                                                                    ParallelGateway.class);
                    FlowElement whetherFlow = parallelGatewayEvent(parallelGateway, idGen.nextStringId("act-"), node);
                    flowsAdditionals.put(node.getId(), whetherFlow);

                    for (ProcessNode childNode : childNodes) {
                        FlowElement childTaskFlow = processMgtTaskEvent(userTask, idGen.nextStringId("act-"),
                                                                        childNode);
                        flows.put(childNode.getId(), childTaskFlow);
                    }
                    // 并行环节结束网管
                    FlowElement whetherFlowEnd = parallelGatewayEvent(parallelGateway, idGen.nextStringId("act-"),
                                                                      node);
                    parallelFlowsEndAdditionals.put(node.getId(), whetherFlowEnd);
                } else {
                    //如果有驳回和拒绝才建立网关节点
                    if (config.getCandidateAuditable() || config.getCandidateGoback()) {
                        if (!ProcessConstants.TICKET.equals(process.getBusinessCode())) {
                            //创建工单任务
                            ExclusiveGateway exclusiveGateway = createElement(actProcess, idGen.nextStringId("act-"),
                                                                              ExclusiveGateway.class);
                            FlowElement whetherFlow = gatewayEvent(exclusiveGateway, idGen.nextStringId("act-"), node);
                            flowsAdditionals.put(node.getId(), whetherFlow);
                        }
                    }
                }
            } else if (node.getNodeType().equals(NodeTypeEnum.APPLYTASK.getType())) {
                UserTask userTask = createElement(actProcess, idGen.nextStringId("act-"), UserTask.class);
                FlowElement taskFlow = processMgtTaskEvent(userTask, idGen.nextStringId("act-"), node);
                flows.put(node.getId(), taskFlow);
            } else if (node.getNodeType().equals(NodeTypeEnum.SERVICE.getType())) {
                FlowElement serviceFlow;
                ServiceTask serviceTask = createElement(actProcess, idGen.nextStringId("act-"), ServiceTask.class);
                if (node.getStatus().equals(4)) {
                    //tode
                    serviceFlow = serviceSyncEvent(serviceTask, idGen.nextStringId("act-"), node);
                    flows.put(node.getId(), serviceFlow);
                } else if (node.getStatus().equals(1)) {
                    serviceFlow = serviceOpenEvent(serviceTask, idGen.nextStringId("act-"), node);
                    flows.put(node.getId(), serviceFlow);
                } else {
                    if (!ProcessConstants.TICKET.equals(process.getBusinessCode())) {
                        serviceFlow = serviceCloseEvent(serviceTask, idGen.nextStringId("act-"), node);
                        flows.put(node.getId(), serviceFlow);
                    }
                }
            } else if (node.getNodeType().equals(NodeTypeEnum.END.getType())) {
                //创建结束事件
                EndEvent element = createElement(actProcess, idGen.nextStringId("act-"), EndEvent.class);
                FlowElement endFlow = endEvent(element, idGen.nextStringId("act-"), node);
                flows.put(node.getId(), endFlow);
            }
        }

        for (int i = 0; i < nodes.size(); i++) {
            ProcessNode node = nodes.get(i);
            ProcessNodeConfig config = JSON.parseObject(node.getConfigData(), ProcessNodeConfig.class);

            if (node.getNodeType().equals(NodeTypeEnum.START.getType())) {
                //开始事件
                FlowNode startFlow = (FlowNode) flows.get(node.getId());
                FlowNode nextFlow = (FlowNode) flows.get(nodes.get(i + 1).getId());
                createSequenceFlow(actProcess, startFlow, nextFlow);
            } else if (node.getNodeType().equals(NodeTypeEnum.GATEWAY.getType())) {
                // 预留(角色网关)
            } else if (node.getNodeType().equals(NodeTypeEnum.APPLYTASK.getType())) {
                FlowNode userFlow = (FlowNode) flows.get(node.getId());
                FlowNode nextFlow = (FlowNode) flows.get(nodes.get(i + 1).getId());
                createSequenceFlow(actProcess, userFlow, nextFlow);
            } else if (node.getNodeType().equals(NodeTypeEnum.USERTASK.getType())) {
                List<ProcessNode> childNodes = childListMap.get(node.getOriginalNodeId());
                if (StringUtils.equals(node.getNodeFeature(), "parallel") && CollectionUtil.isNotEmpty(childNodes)) {
                    // 并行环节
                    FlowElement userFlow = flows.get(node.getId());
                    FlowElement wheatherFlow = flowsAdditionals.get(node.getId());
                    FlowElement wheatherParallelEndFlow = parallelFlowsEndAdditionals.get(node.getId());
                    actProcess.addChildElement(sequenceFlow(userFlow, wheatherFlow, model));

                    // 获取并行环节结束后的下一个任务
                    FlowElement passFlow = flows.get(nodes.get(i + 1).getId());
                    actProcess.addChildElement(sequenceFlow(wheatherParallelEndFlow, passFlow, model));

                    // 连接并行任务
                    Map<Long, List<ProcessNode>> nodeGroupMap = childNodes.stream()
                                                                          .collect(Collectors.groupingBy(
                                                                                  ProcessNode::getNodeGroup));
                    Set<Entry<Long, List<ProcessNode>>> entries = nodeGroupMap.entrySet();
                    for (Map.Entry<Long, List<ProcessNode>> entry : entries) {
                        List<ProcessNode> groupNodes = entry.getValue();

                        groupNodes.sort((a, b) -> {
                            if (a.getSortNum().equals(b.getSortNum())) {
                                return 0;
                            } else {
                                return a.getSortNum() > b.getSortNum() ? 1 : -1;
                            }
                        });

                        for (int j = 0; j < groupNodes.size(); j++) {
                            ProcessNode childNode = groupNodes.get(j);

                            FlowElement parallelUserFlow = flows.get(childNode.getId());
                            if (j == 0) {
                                actProcess.addChildElement(sequenceFlow(wheatherFlow, parallelUserFlow, model));
                            }
                            if (j == (groupNodes.size() - 1)) {
                                actProcess.addChildElement(
                                        sequenceFlow(parallelUserFlow, wheatherParallelEndFlow, model));
                            } else if (j < (groupNodes.size() - 1)) {
                                ProcessNode childNextNode = groupNodes.get(j + 1);
                                FlowElement parallelNextUserFlow = flows.get(childNextNode.getId());
                                actProcess.addChildElement(
                                        sequenceFlow(parallelUserFlow, parallelNextUserFlow, model));
                            }
                        }
                    }
                } else if (!ProcessConstants.TICKET.equals(process.getBusinessCode())) {
                    FlowElement userFlow = flows.get(node.getId());
                    FlowElement wheatherFlow = flowsAdditionals.get(node.getId());

                    if (ObjectUtils.isEmpty(wheatherFlow)) {
                        FlowElement nextFlow = flows.get(nodes.get(i + 1).getId());
                        actProcess.addChildElement(sequenceFlow(userFlow, nextFlow, model));
                        continue;
                    }
                    if (!ObjectUtils.isEmpty(wheatherFlow)) {
                        actProcess.addChildElement(sequenceFlow(userFlow, wheatherFlow, model));
                        //设置条件
                        FlowElement passFlow = flows.get(nodes.get(i + 1).getId());
                        actProcess.addChildElement(sequenceFlow(model, wheatherFlow,
                                                                passFlow, "通过", "${audit=='pass'}"));
                    }
                    if (config.getCandidateAuditable()) {
                        ProcessNode rejectNode = findRejectNode(nodes);
                        FlowElement rejectFlow = flows.get(rejectNode.getId());
                        actProcess.addChildElement(sequenceFlow(model, wheatherFlow,
                                                                rejectFlow, "拒绝", "${audit=='reject'}"));
                    }

                    if (config.getCandidateGoback()) {
                        // 第一个节点不设置驳回
                        for (int j = 1; j < i; j++) {
                            // 添加驳回条件 goback
                            FlowElement turndownFlow = flows.get(nodes.get(j).getId());
                            actProcess.addChildElement(sequenceFlow(model, wheatherFlow,
                                                                    turndownFlow, "驳回",
                                                                    String.format("${audit=='goback-%s'}", j)));
                        }
                    }
                } else {
                    FlowNode userFlow = (FlowNode) flows.get(node.getId());
                    FlowNode nextFlow = (FlowNode) flows.get(nodes.get(i + 1).getId());
                    createSequenceFlow(actProcess, userFlow, nextFlow);
                }
            } else if (node.getNodeType().equals(NodeTypeEnum.SERVICE.getType())) {
                FlowNode serviceFlow = (FlowNode) flows.get(node.getId());
                FlowNode nextFlow = (FlowNode) flows.get(nodes.get(nodes.size() - 1).getId());
                createSequenceFlow(actProcess, serviceFlow, nextFlow);
            } else if (node.getNodeType().equals(NodeTypeEnum.END.getType())) {
                FlowNode endFlow = (FlowNode) flows.get(node.getId());
                FlowNode nextFlow = (FlowNode) flows.get(nodes.get(nodes.size() - 1).getId());
                createSequenceFlow(actProcess, endFlow, nextFlow);
            }
        }

        ProcessConstants.CURRENT_NODES.set(nodes);
        ProcessConstants.CHILD_NODES_MAP.set(childListMap);
        ProcessConstants.ACT_FLOWS_MAP.set(flows);
        ProcessConstants.GATE_FLOWS_MAP.set(flowsAdditionals);
        ProcessConstants.GATE_END_FLOWS_MAP.set(parallelFlowsEndAdditionals);
        //绘画BMBP流程图
        DrawUtils.createBmbp(model,plane);

        Deployment deployment = repositoryService.createDeployment()
                                                 .addModelInstance(process.getProcessCode() + ".bpmn", model).deploy();

     //   String xmlPng = Bpmn.convertToString(model);

        ProcessDefinition processDefinition = repositoryService
                .createProcessDefinitionQuery()
                .deploymentId(deployment.getId())
                .singleResult();

        processVersion.setDeploymentId(deployment.getId());

        processVersion.setProcessIdentify(processDefinition.getId());
        processVersionMapper.updateByPrimaryKeySelective(processVersion);

        for (ProcessNode node : nodes) {
            if (node.getNodeType() == NodeTypeEnum.USERTASK.getType()
                    || node.getNodeType() == NodeTypeEnum.APPLYTASK.getType()) {
                node.setProcessIdentify(processDefinition.getId());
                processNodeMapper.updateByPrimaryKeySelective(node);
                if (org.apache.commons.lang.StringUtils.equals(node.getNodeFeature(), "parallel")) {
                    List<ProcessNode> childNodes = childListMap.get(node.getOriginalNodeId());
                    if (CollectionUtil.isNotEmpty(childNodes)) {
                        for (ProcessNode childNode : childNodes) {
//                            childNode.setCurrentDeployId(currentDeployId);
                            childNode.setProcessIdentify(processDefinition.getId());
                            processNodeMapper.updateByPrimaryKeySelective(childNode);
                        }
                    }
                }
            }
        }

//        process.setCurrentDeployId(currentDeployId);
        processMapper.updateByPrimaryKeySelective(process);

        ProcessConstants.CURRENT_NODES.remove();
        ProcessConstants.CHILD_NODES_MAP.remove();
        ProcessConstants.ACT_FLOWS_MAP.remove();
        ProcessConstants.GATE_FLOWS_MAP.remove();
        ProcessConstants.GATE_END_FLOWS_MAP.remove();

        return deployment;
    }

    UserTask processMgtTaskEvent(UserTask userTask, String id, ProcessNode processNode) {
        userTask.setId(id);
        userTask.setName(processNode.getNodeName());
        ProcessNodeConfig config = JSON.parseObject(
                processNode.getConfigData(), ProcessNodeConfig.class);

        List<String> candidateList = Lists.newArrayList();
        List<ProcessNodeRoleDto> candidates = config.getCandidates();
        if (candidates != null && candidates.size() > 0) {
            List<String> roleList = candidates.stream().map(role -> role.getType()
                    + "-" + role.getRefId()).collect(Collectors.toList());
            candidateList.addAll(roleList);
        }
        List<ProcessNodeRoleDto> candidateThirds = config.getCandidateThirds();
        if (candidateThirds != null && candidateThirds.size() > 0) {
            List<String> userList = candidateThirds.stream()
                                                   .map(role ->
                                                                "201" + "-" + role.getRefId())
                                                   .collect(Collectors.toList());
            candidateList.addAll(userList);
        }
        if (processNode.getNodeType().equals(NodeTypeEnum.APPLYTASK.getType())) {
            // 申请节点逻辑
            String auditIds = String.join(",", candidateList);
            String approvalLocation = ProcessConstants.APPROVAL_LOCATION_CURRENT;
            // TODO: 2022/5/27 处理申请节点
            String condition = String.format(
                    "${processService.auditApplyInlineMgts(execution, '%s', '%s', '%s', '%s')}",
                    auditIds, null, approvalLocation, processNode.getId());
            userTask.setCamundaCandidateUsersList(Collections.singletonList(condition));
        } else {
            String auditIds = String.join(",", candidateList);
            String approvalLocation = StringUtils.isNotBlank(config.getApprovalLocation())
                    ? config.getApprovalLocation()
                    : ProcessConstants.APPROVAL_LOCATION_CURRENT;
            if (StringUtils.equalsIgnoreCase(ProcessConstants.APPROVAL_LOCATION_PARENT, approvalLocation)) {
                String condition = String.format(
                        "${processService.auditParentAssignMgts(execution, '%s', '%s', '%s', '%s')}",
                        auditIds, null, approvalLocation, processNode.getId());

                if (StringUtils.equalsIgnoreCase("whole", config.getApprovalWay())) {
                    //todo
                    //userTask.setAssignee("${user}");
                    // 父级指定处理人
                    // 获取多实例配置
                    //MultiInstanceLoopCharacteristics characteristics = new MultiInstanceLoopCharacteristics();
                    // 设置集合变量，统一设置成users
                    //characteristics.setInputDataItem(condition);
                    // 设置变量
                    //characteristics.setElementVariable("user");
                    // 设置为同时接收（false 表示不按顺序执行）
                    //characteristics.setSequential(false);
                    // 设置条件（暂时处理成，全部会签完转下步）
                    //characteristics.setCompletionCondition("${nrOfCompletedInstances==nrOfInstances || audit!='pass'}");

                    //userTask.setLoopCharacteristics(characteristics);
                } else {
                    userTask.setCamundaCandidateGroupsList(Collections.singletonList(condition));
                }
            } else if (StringUtils.equalsIgnoreCase(ProcessConstants.APPROVAL_LOCATION_APPLYER, approvalLocation)) {
                // 申请者处理
                String condition = String.format(
                        "${processService.auditApplyInlineMgts(execution, '%s', '%s', '%s', '%s')}",
                        auditIds, null, approvalLocation, processNode.getId());
                userTask.setCamundaCandidateGroupsList(Collections.singletonList(condition));

                //userTask.setOwner(String.join(":", auditIds, approvalLocation, "nodeid-" + processNode.getId()));
            } else {
                if (CollectionUtil.isEmpty(candidateList) && !StringUtils.equals(processNode.getNodeFeature(),
                                                                                 "parallel")) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1477596089));
                }

                if (StringUtils.equals(processNode.getNodeFeature(), "parallel")) {
                    // 并行起始节点不需要审批
                    auditIds = "";
                }

                String condition = String.format(
                        "${processService.auditCandidateOnlyUserIds(execution, '%s')}", auditIds);
                if (StringUtils.equalsIgnoreCase("whole", config.getApprovalWay())) {

                    //userTask.setAssignee("${user}");
                    // 获取多实例配置
                    //MultiInstanceLoopCharacteristics characteristics = new MultiInstanceLoopCharacteristics();
                    // 设置集合变量，统一设置成users
                    //characteristics.setInputDataItem(condition);
                    // 设置变量
                    //characteristics.setElementVariable("user");
                    // 设置为同时接收（false 表示不按顺序执行）
                    //characteristics.setSequential(false);
                    // 设置条件（暂时处理成，全部会签完转下步）
                    //characteristics.setCompletionCondition("${nrOfCompletedInstances==nrOfInstances || audit!='pass'}");

                    //userTask.setLoopCharacteristics(characteristics);
                } else {
                    userTask.setCamundaCandidateUsersList(Collections.singletonList(condition));
                }
            }
        }
        //  userTask.setOwner( "nodeid-" + processNode.getId());
        if (CollectionUtil.isNotEmpty(config.getNotifyWays()) && !StringUtils.equals(processNode.getNodeFeature(),
                                                                                     "parallel")) {
            // 站内信和提醒 监听器
            ExtensionElements extensionElements = this.createElement(userTask, "", ExtensionElements.class);
            CamundaTaskListener create = this.createElement(extensionElements, "", CamundaTaskListener.class);
            create.setCamundaDelegateExpression("#{TaskMessageListener}");
            create.setCamundaEvent("create");
            create.setTextContent(processNode.getId().toString());
            CamundaTaskListener complete = this.createElement(extensionElements, "", CamundaTaskListener.class);
            complete.setCamundaDelegateExpression("#{TaskMessageListener}");
            complete.setCamundaEvent("complete");
            userTask.setExtensionElements(extensionElements);
        }

        return userTask;
    }


    ParallelGateway parallelGatewayEvent(ParallelGateway parallelGateway, String id, ProcessNode processNode) {
        parallelGateway.setId(id);
        parallelGateway.setName(processNode.getNodeName());
        return parallelGateway;
    }

    ServiceTask serviceSyncEvent(ServiceTask serviceTask, String id, ProcessNode processNode) {
        serviceTask.setId("sync" + id);
        serviceTask.setName(processNode.getNodeName());
        // todo  设置监听
    /*    serviceTask.setCamundaDelegateExpression("expression");
        serviceTask.setImplementation("#{processService.doSync(execution)}");*/
        serviceTask.setCamundaDelegateExpression("#{MonitorJavaDelegate}");
        serviceTask.setImplementation("Delegate Expression");
        return serviceTask;
    }

    @Override
    public void processNodeChildUp(Long id, AuthUser authUser) {
        ProcessNode processNode = processNodeMapper.selectByPrimaryKey(id);
        if (processNode == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_462777243));
        }

        if (!processNode.getNodeType().equals(NodeTypeEnum.USERTASK.getType())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_574575453));
        }

        updateProcessStatus(processNode.getProcessId(), PROCESS_STATUS_EDITED);

        Criteria criteria = new Criteria("parentId", processNode.getParentId());
//        criteria.put("nodeGroup", processNode.getNodeGroup());
        criteria.setOrderByClause(" sort_num asc ");
        List<ProcessNode> nodes = processNodeMapper.selectByParams(criteria);
        ProcessNode previousNode = null;

        for (int i = 0; i < nodes.size(); i++) {
            ProcessNode node = nodes.get(i);

            if (node.getId().equals(processNode.getId()) && i > 0) {
                previousNode = nodes.get(i - 1);
            }
        }
        if (Objects.isNull(previousNode)) {
            return;
        }

        if (previousNode.getNodeType().equals(NodeTypeEnum.USERTASK.getType())) {
            // 和前一个节点交换
            float sortNumSwap = previousNode.getSortNum();
            previousNode.setSortNum(processNode.getSortNum());
            processNode.setSortNum(sortNumSwap);

            processNodeMapper.updateByPrimaryKeySelective(previousNode);
            processNodeMapper.updateByPrimaryKeySelective(processNode);
        }
    }

    @Override
    public void processNodeChildDown(Long id, AuthUser authUser) {
        ProcessNode processNode = processNodeMapper.selectByPrimaryKey(id);
        if (processNode == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_462777243));
        }

        if (!processNode.getNodeType().equals(NodeTypeEnum.USERTASK.getType())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_574575453));
        }

        updateProcessStatus(processNode.getProcessId(), PROCESS_STATUS_EDITED);

        Criteria criteria = new Criteria("parentId", processNode.getParentId());
//        criteria.put("nodeGroup", processNode.getNodeGroup());
        criteria.setOrderByClause(" sort_num asc ");
        List<ProcessNode> nodes = processNodeMapper.selectByParams(criteria);
        ProcessNode nextNode = null;

        for (int i = 0; i < nodes.size(); i++) {
            ProcessNode node = nodes.get(i);

            if (node.getId().equals(processNode.getId()) && (i + 1) < nodes.size()) {
                nextNode = nodes.get(i + 1);
            }
        }
        if (Objects.isNull(nextNode)) {
            return;
        }

        if (nextNode.getNodeType().equals(NodeTypeEnum.USERTASK.getType())) {
            // 和后面一个节点交换
            float sortNumSwap = nextNode.getSortNum();
            nextNode.setSortNum(processNode.getSortNum());
            processNode.setSortNum(sortNumSwap);

            processNodeMapper.updateByPrimaryKeySelective(nextNode);
            processNodeMapper.updateByPrimaryKeySelective(processNode);
        }
    }


    protected <T extends BpmnModelElementInstance> T createElement(BpmnModelElementInstance parentElement, String id,
                                                                   Class<T> elementClass) {
        T element = parentElement.getModelInstance().newInstance(elementClass);
        if (StrUtil.isNotBlank(id)) {
            element.setAttributeValue("id", id, true);
        }
        parentElement.addChildElement(element);
        return element;
    }

    public SequenceFlow createSequenceFlow(org.camunda.bpm.model.bpmn.instance.Process process, FlowNode from,
                                           FlowNode to) {
        String identifier = from.getId() + "-" + to.getId();
        SequenceFlow sequenceFlow = createElement(process, identifier, SequenceFlow.class);
        process.addChildElement(sequenceFlow);
        sequenceFlow.setSource(from);
        from.getOutgoing().add(sequenceFlow);
        sequenceFlow.setTarget(to);
        to.getIncoming().add(sequenceFlow);
        return sequenceFlow;
    }

    public void addCondition(SequenceFlow sequenceFlow, String name, String condition, BpmnModelInstance model) {
        sequenceFlow.setName(name);
        ConditionExpression conditionExpression = model.newInstance(ConditionExpression.class);
        conditionExpression.setTextContent(condition);
        sequenceFlow.setConditionExpression(conditionExpression);
    }

    public void initReauditNoResourceProcess() {
        String opby = "admin";
        Process process = new Process();
        process.setProcessName(ProcessConstants.REAUDIT_NO_RESOURCE);
        process.setBusinessCode("built-in");
        process.setDescription("双重审核流程-非资源审批内置勿删");
        IdGen idGen = IdGen.get();
        process.setProcessCode("approval-" + idGen.nextId());
        process.setEntryId(1L);
        process.setCreatedBy(opby);
        process.setCreatedDt(DateTime.now().toDate());
        process.setUpdatedBy(opby);
        process.setUpdatedDt(DateTime.now().toDate());
        process.setOrgSid(0L);
        process.setStatus(0);
        processMapper.insertSelective(process);

        ProcessVersion processVersion = new ProcessVersion();
        processVersion.setProcessId(process.getId());
        processVersion.setVersionName(process.getProcessName());
        processVersion.setDescription(process.getDescription());
        processVersion.setIsdefault(true);
        processVersion.setStatus(0);
        processVersion.setVersion(1);
        processVersion.setCreatedBy(opby);
        processVersion.setCreatedDt(DateTime.now().toDate());
        processVersion.setUpdatedBy(opby);
        processVersion.setUpdatedDt(DateTime.now().toDate());
        processVersionMapper.insertSelective(processVersion);

        List<ProcessNode> nodes = Lists.newArrayList();
        ProcessNode start = new ProcessNode();
        start.setNodeName("开始");
        start.setNodeType(NodeTypeEnum.START.getType());
        start.setDescription("流程开始");
        start.setSortNum(0.0f);
        start.setStatus(0);
        start.setConfigData("{}");
        start.setNodeLevel("1");
        initNode(start, process, processVersion, opby);
        processNodeMapper.insertSelective(start);
        nodes.add(start);

        ProcessNode openService = new ProcessNode();
        openService.setNodeName("执行");
        openService.setNodeType(NodeTypeEnum.SERVICE.getType());
        openService.setDescription("同步流程申请");
        openService.setSortNum(200.0f);
        openService.setStatus(4);
        openService.setConfigData("{}");
        openService.setNodeLevel("1");
        initNode(openService, process, processVersion, opby);
        processNodeMapper.insertSelective(openService);
        nodes.add(openService);

        ProcessNode rejectService = new ProcessNode();
        rejectService.setNodeName("拒绝并关闭");
        rejectService.setNodeType(NodeTypeEnum.SERVICE.getType());
        rejectService.setDescription("用户审批拒绝");
        rejectService.setSortNum(390.0f);
        rejectService.setStatus(2);
        rejectService.setConfigData("{}");
        rejectService.setNodeLevel("1");
        rejectService.setNodeFeature("serial");
        initNode(rejectService, process, processVersion, opby);
        processNodeMapper.insertSelective(rejectService);
        nodes.add(rejectService);

        ProcessNode approveService = new ProcessNode();
        approveService.setNodeName("领导审批");
        approveService.setNodeType(NodeTypeEnum.USERTASK.getType());
        approveService.setDescription("领导审批");
        approveService.setSortNum(100.0f);
        approveService.setStatus(3);
        approveService.setConfigData("{\"approvalWay\":\"once\",\"candidateAuditable\":true,\"candidateGoback\":false,\"candidateThirds\":[{\"name\":\"wxh\",\"refId\":\"31891\"}],\"candidates\":[{\"name\":\"运营管理员(内置)\",\"refId\":\"301\",\"type\":\"bss\"}],\"editableFormTemplates\":[],\"formTemplate\":\"16\",\"notifyWays\":[\"sms\"]}");
        approveService.setNodeLevel("1");
        approveService.setNodeFeature("serial");
        initNode(approveService, process, processVersion, opby);
        processNodeMapper.insertSelective(approveService);
        nodes.add(approveService);

        ProcessNode approveService2 = new ProcessNode();
        approveService2.setNodeName("领导审批");
        approveService2.setNodeType(NodeTypeEnum.USERTASK.getType());
        approveService2.setDescription("领导审批");
        approveService2.setSortNum(100.0f);
        approveService2.setStatus(103);
        approveService2.setConfigData("{\"approvalWay\":\"once\",\"candidateAuditable\":true,\"candidateGoback\":false,\"candidateThirds\":[{\"name\":\"wxh\",\"refId\":\"31891\"}],\"candidates\":[{\"name\":\"运营管理员(内置)\",\"refId\":\"301\",\"type\":\"bss\"}],\"editableFormTemplates\":[],\"formTemplate\":\"16\",\"notifyWays\":[\"sms\"]}");
        approveService2.setNodeLevel("1");
        approveService2.setNodeFeature("serial");
        initNode(approveService2, process, processVersion, opby);
        processNodeMapper.insertSelective(approveService2);
        nodes.add(approveService2);

        ProcessNode endService = new ProcessNode();
        endService.setNodeName("结束");
        endService.setNodeType(NodeTypeEnum.END.getType());
        endService.setDescription("流程结束");
        endService.setSortNum(400.0f);
        endService.setStatus(0);
        endService.setConfigData("{}");
        endService.setNodeLevel("1");
        initNode(endService, process, processVersion, opby);
        processNodeMapper.insertSelective(endService);
        nodes.add(endService);

        processMgtDeployProcess(process, processVersion, nodes);
    }

    public void initReauditResourceProcess() {
        String opby = "admin";
        Process process = new Process();
        process.setProcessName(ProcessConstants.REAUDIT_RESOURCE);
        process.setBusinessCode("built-in");
        process.setDescription("双重审核流程-资源审批内置勿删");
        IdGen idGen = IdGen.get();
        process.setProcessCode("approval-" + idGen.nextId());
        process.setEntryId(1L);
        process.setCreatedBy(opby);
        process.setCreatedDt(DateTime.now().toDate());
        process.setUpdatedBy(opby);
        process.setUpdatedDt(DateTime.now().toDate());
        process.setOrgSid(0L);
        process.setStatus(0);
        processMapper.insertSelective(process);

        ProcessVersion processVersion = new ProcessVersion();
        processVersion.setProcessId(process.getId());
        processVersion.setVersionName(process.getProcessName());
        processVersion.setDescription(process.getDescription());
        processVersion.setIsdefault(true);
        processVersion.setStatus(0);
        processVersion.setVersion(1);
        processVersion.setCreatedBy(opby);
        processVersion.setCreatedDt(DateTime.now().toDate());
        processVersion.setUpdatedBy(opby);
        processVersion.setUpdatedDt(DateTime.now().toDate());
        processVersionMapper.insertSelective(processVersion);

        List<ProcessNode> nodes = Lists.newArrayList();
        ProcessNode start = new ProcessNode();
        start.setNodeName("开始");
        start.setNodeType(NodeTypeEnum.START.getType());
        start.setDescription("流程开始");
        start.setSortNum(0.0f);
        start.setStatus(0);
        start.setConfigData("{}");
        start.setNodeLevel("1");
        initNode(start, process, processVersion, opby);
        processNodeMapper.insertSelective(start);
        nodes.add(start);

        ProcessNode openService = new ProcessNode();
        openService.setNodeName("执行");
        openService.setNodeType(NodeTypeEnum.SERVICE.getType());
        openService.setDescription("同步流程申请");
        openService.setSortNum(200.0f);
        openService.setStatus(4);
        openService.setConfigData("{}");
        openService.setNodeLevel("1");
        initNode(openService, process, processVersion, opby);
        processNodeMapper.insertSelective(openService);
        nodes.add(openService);

        ProcessNode endService = new ProcessNode();
        endService.setNodeName("结束");
        endService.setNodeType(NodeTypeEnum.END.getType());
        endService.setDescription("流程结束");
        endService.setSortNum(400.0f);
        endService.setStatus(0);
        endService.setConfigData("{}");
        endService.setNodeLevel("1");
        initNode(endService, process, processVersion, opby);
        processNodeMapper.insertSelective(endService);
        nodes.add(endService);

        ProcessNode rejectService = new ProcessNode();
        rejectService.setNodeName("拒绝并关闭");
        rejectService.setNodeType(NodeTypeEnum.SERVICE.getType());
        rejectService.setDescription("用户审批拒绝");
        rejectService.setSortNum(390.0f);
        rejectService.setStatus(104);
        rejectService.setConfigData("{}");
        rejectService.setNodeLevel("1");
        rejectService.setNodeFeature("serial");
        initNode(rejectService, process, processVersion, opby);
        processNodeMapper.insertSelective(rejectService);
        nodes.add(rejectService);

        ProcessNode approveService = new ProcessNode();
        approveService.setNodeName("领导审批");
        approveService.setNodeType(NodeTypeEnum.USERTASK.getType());
        approveService.setDescription("领导审批");
        approveService.setSortNum(105.0f);
        approveService.setStatus(104);
        approveService.setConfigData("{\"approvalWay\":\"once\",\"candidateAuditable\":true,\"candidateGoback\":false,\"candidateThirds\":[{\"name\":\"wxh\",\"refId\":\"31891\"}],\"candidates\":[{\"name\":\"运营管理员(内置)\",\"refId\":\"301\",\"type\":\"bss\"}],\"editableFormTemplates\":[],\"formTemplate\":\"16\",\"notifyWays\":[\"sms\"]}");
        approveService.setNodeLevel("1");
        approveService.setNodeFeature("serial");
        initNode(approveService, process, processVersion, opby);
        processNodeMapper.insertSelective(approveService);
        nodes.add(approveService);

        ProcessNode approveService2 = new ProcessNode();
        approveService2.setNodeName("领导审批");
        approveService2.setNodeType(NodeTypeEnum.USERTASK.getType());
        approveService2.setDescription("");
        approveService2.setSortNum(105.0f);
        approveService2.setStatus(103);
        approveService2.setConfigData("{\"approvalWay\":\"once\",\"candidateAuditable\":true,\"candidateGoback\":false,\"candidateThirds\":[{\"name\":\"wxh\",\"refId\":\"31891\"}],\"candidates\":[{\"name\":\"运营管理员(内置)\",\"refId\":\"301\",\"type\":\"bss\"}],\"editableFormTemplates\":[],\"formTemplate\":\"16\",\"notifyWays\":[\"sms\"]}");
        approveService2.setNodeLevel("1");
        approveService2.setNodeFeature("serial");
        approveService2.setProcessIdentify("approval-703398693201510400:2:0000000101021");
        approveService2.setOriginalNodeId(1333L);
        initNode(approveService2, process, processVersion, opby);
        processNodeMapper.insertSelective(approveService2);
        nodes.add(approveService2);

        ProcessNode rejectService2 = new ProcessNode();
        rejectService2.setNodeName("拒绝并关闭");
        rejectService2.setNodeType(NodeTypeEnum.SERVICE.getType());
        rejectService2.setDescription("用户审批拒绝");
        rejectService2.setSortNum(390.0f);
        rejectService2.setStatus(2);
        rejectService2.setConfigData("{}");
        rejectService2.setNodeLevel("1");
        rejectService2.setNodeFeature("serial");
        initNode(rejectService2, process, processVersion, opby);
        processNodeMapper.insertSelective(rejectService2);
        nodes.add(rejectService2);

        ProcessNode adminAuditService1 = new ProcessNode();
        adminAuditService1.setNodeName("运营管理员审批");
        adminAuditService1.setNodeType(NodeTypeEnum.USERTASK.getType());
        adminAuditService1.setDescription("运营管理员审批");
        adminAuditService1.setSortNum(100.0f);
        adminAuditService1.setStatus(3);
        adminAuditService1.setConfigData("{\"approvalWay\":\"once\",\"candidateAuditable\":true,\"candidateGoback\":false,\"candidateThirds\":[{\"name\":\"wxh\",\"refId\":\"31891\"}],\"candidates\":[{\"name\":\"运营管理员(内置)\",\"refId\":\"301\",\"type\":\"bss\"}],\"editableFormTemplates\":[],\"formTemplate\":\"16\",\"notifyWays\":[\"sms\"]}");
        adminAuditService1.setNodeLevel("1");
        adminAuditService1.setNodeFeature("serial");
        initNode(adminAuditService1, process, processVersion, opby);
        processNodeMapper.insertSelective(adminAuditService1);
        nodes.add(adminAuditService1);

        ProcessNode adminAuditService2 = new ProcessNode();
        adminAuditService2.setNodeName("运营管理员审批");
        adminAuditService2.setNodeType(NodeTypeEnum.USERTASK.getType());
        adminAuditService2.setDescription("运营管理员审批");
        adminAuditService2.setSortNum(100.0f);
        adminAuditService2.setStatus(103);
        adminAuditService2.setConfigData("{\"approvalWay\":\"once\",\"candidateAuditable\":true,\"candidateGoback\":false,\"candidateThirds\":[{\"name\":\"wxh\",\"refId\":\"31891\"}],\"candidates\":[{\"name\":\"运营管理员(内置)\",\"refId\":\"301\",\"type\":\"bss\"}],\"editableFormTemplates\":[],\"formTemplate\":\"16\",\"notifyWays\":[\"sms\"]}");
        adminAuditService2.setNodeLevel("1");
        adminAuditService2.setNodeFeature("serial");
        adminAuditService2.setProcessIdentify("approval-703398693201510400:3:0000000101051");
        adminAuditService2.setOriginalNodeId(1354L);
        initNode(adminAuditService2, process, processVersion, opby);
        processNodeMapper.insertSelective(adminAuditService2);
        nodes.add(adminAuditService2);

        ProcessNode adminAuditService3 = new ProcessNode();
        adminAuditService3.setNodeName("运营管理员审批");
        adminAuditService3.setNodeType(NodeTypeEnum.USERTASK.getType());
        adminAuditService3.setDescription("运营管理员审批");
        adminAuditService3.setSortNum(100.0f);
        adminAuditService3.setStatus(103);
        adminAuditService3.setConfigData("{\"approvalWay\":\"once\",\"candidateAuditable\":true,\"candidateGoback\":false,\"candidateThirds\":[{\"name\":\"wxh\",\"refId\":\"31891\"}],\"candidates\":[{\"name\":\"运营管理员(内置)\",\"refId\":\"301\",\"type\":\"bss\"}],\"editableFormTemplates\":[],\"formTemplate\":\"16\",\"notifyWays\":[\"sms\"]}");
        adminAuditService3.setNodeLevel("1");
        adminAuditService3.setNodeFeature("serial");
        adminAuditService3.setProcessIdentify("approval-703398693201510400:4:0000000101054");
        adminAuditService3.setOriginalNodeId(1354L);
        initNode(adminAuditService3, process, processVersion, opby);
        processNodeMapper.insertSelective(adminAuditService3);
        nodes.add(adminAuditService3);

        ProcessNode adminAuditService4 = new ProcessNode();
        adminAuditService4.setNodeName("运营管理员审批");
        adminAuditService4.setNodeType(NodeTypeEnum.USERTASK.getType());
        adminAuditService4.setDescription("运营管理员审批");
        adminAuditService4.setSortNum(100.0f);
        adminAuditService4.setStatus(103);
        adminAuditService4.setConfigData("{\"approvalWay\":\"once\",\"candidateAuditable\":true,\"candidateGoback\":false,\"candidateThirds\":[{\"name\":\"wxh\",\"refId\":\"31891\"}],\"candidates\":[{\"name\":\"运营管理员(内置)\",\"refId\":\"301\",\"type\":\"bss\"}],\"editableFormTemplates\":[],\"formTemplate\":\"16\",\"notifyWays\":[\"sms\"]}");
        adminAuditService4.setNodeLevel("1");
        adminAuditService4.setNodeFeature("serial");
        adminAuditService4.setProcessIdentify("approval-703398693201510400:5:0000000101069");
        adminAuditService4.setOriginalNodeId(1354L);
        initNode(adminAuditService4, process, processVersion, opby);
        processNodeMapper.insertSelective(adminAuditService4);
        nodes.add(adminAuditService4);

        ProcessNode approveService3 = new ProcessNode();
        approveService3.setNodeName("领导审批1");
        approveService3.setNodeType(NodeTypeEnum.USERTASK.getType());
        approveService3.setDescription("");
        approveService3.setSortNum(150.0f);
        approveService3.setStatus(104);
        approveService3.setConfigData("{\"approvalWay\":\"once\",\"candidateAuditable\":true,\"candidateGoback\":false,\"candidateThirds\":[{\"name\":\"周俊贤\",\"refId\":\"2596\"}],\"candidates\":[{\"name\":\"运营管理员(内置)\",\"refId\":\"301\",\"type\":\"bss\"}],\"editableFormTemplates\":[],\"formTemplate\":\"16\",\"notifyWays\":[\"sms\"]}");
        approveService3.setNodeLevel("1");
        approveService3.setNodeFeature("serial");
        initNode(approveService3, process, processVersion, opby);
        processNodeMapper.insertSelective(approveService3);
        nodes.add(approveService3);

        ProcessNode approveService4 = new ProcessNode();
        approveService4.setNodeName("领导审批");
        approveService4.setNodeType(NodeTypeEnum.USERTASK.getType());
        approveService4.setDescription("领导审批");
        approveService4.setSortNum(175.0f);
        approveService4.setStatus(3);
        approveService4.setConfigData("{\"approvalWay\":\"once\",\"candidateAuditable\":true,\"candidateGoback\":true,\"candidateThirds\":[{\"name\":\"dada\",\"refId\":\"31894\"}],\"candidates\":[{\"name\":\"运营管理员(内置)\",\"refId\":\"301\",\"type\":\"bss\"}],\"editableFormTemplates\":[16],\"formTemplate\":\"16\",\"notifyWays\":[\"sms\"]}");
        approveService4.setNodeLevel("1");
        approveService4.setNodeFeature("serial");
        initNode(approveService4, process, processVersion, opby);
        processNodeMapper.insertSelective(approveService4);
        nodes.add(approveService4);

        ProcessNode adminAuditService5 = new ProcessNode();
        adminAuditService5.setNodeName("运营管理员审批");
        adminAuditService5.setNodeType(NodeTypeEnum.USERTASK.getType());
        adminAuditService5.setDescription("运营管理员审批");
        adminAuditService5.setSortNum(100.0f);
        adminAuditService5.setStatus(103);
        adminAuditService5.setConfigData("{\"approvalWay\":\"once\",\"candidateAuditable\":true,\"candidateGoback\":false,\"candidateThirds\":[{\"name\":\"wxh\",\"refId\":\"31891\"}],\"candidates\":[{\"name\":\"运营管理员(内置)\",\"refId\":\"301\",\"type\":\"bss\"}],\"editableFormTemplates\":[],\"formTemplate\":\"16\",\"notifyWays\":[\"sms\"]}");
        adminAuditService5.setNodeLevel("1");
        adminAuditService5.setNodeFeature("serial");
        adminAuditService5.setProcessIdentify("approval-703398693201510400:6:0000000101084");
        adminAuditService5.setOriginalNodeId(1354L);
        initNode(adminAuditService5, process, processVersion, opby);
        processNodeMapper.insertSelective(adminAuditService5);
        nodes.add(adminAuditService5);

        ProcessNode approveService5 = new ProcessNode();
        approveService5.setNodeName("领导审批1");
        approveService5.setNodeType(NodeTypeEnum.USERTASK.getType());
        approveService5.setDescription("");
        approveService5.setSortNum(150.0f);
        approveService5.setStatus(103);
        approveService5.setConfigData("{\"approvalWay\":\"once\",\"candidateAuditable\":true,\"candidateGoback\":false,\"candidateThirds\":[{\"name\":\"周俊贤\",\"refId\":\"2596\"}],\"candidates\":[{\"name\":\"运营管理员(内置)\",\"refId\":\"301\",\"type\":\"bss\"}],\"editableFormTemplates\":[],\"formTemplate\":\"16\",\"notifyWays\":[\"sms\"]}");
        approveService5.setNodeLevel("1");
        approveService5.setNodeFeature("serial");
        approveService5.setProcessIdentify("approval-703398693201510400:6:0000000101084");
        approveService5.setOriginalNodeId(1366L);
        initNode(approveService5, process, processVersion, opby);
        processNodeMapper.insertSelective(approveService5);
        nodes.add(approveService5);

        ProcessNode approveService6 = new ProcessNode();
        approveService6.setNodeName("领导审批");
        approveService6.setNodeType(NodeTypeEnum.USERTASK.getType());
        approveService6.setDescription("领导审批");
        approveService6.setSortNum(175.0f);
        approveService6.setStatus(103);
        approveService6.setConfigData("{\"approvalWay\":\"once\",\"candidateAuditable\":true,\"candidateGoback\":true,\"candidateThirds\":[{\"name\":\"dada\",\"refId\":\"31894\"}],\"candidates\":[{\"name\":\"运营管理员(内置)\",\"refId\":\"301\",\"type\":\"bss\"}],\"editableFormTemplates\":[16],\"formTemplate\":\"16\",\"notifyWays\":[\"sms\"]}");
        approveService6.setNodeLevel("1");
        approveService6.setNodeFeature("serial");
        approveService6.setProcessIdentify("approval-703398693201510400:6:0000000101084");
        approveService6.setOriginalNodeId(1369L);
        initNode(approveService6, process, processVersion, opby);
        processNodeMapper.insertSelective(approveService6);
        nodes.add(approveService6);

        ProcessNode adminAuditService6 = new ProcessNode();
        adminAuditService6.setNodeName("运营管理员审批");
        adminAuditService6.setNodeType(NodeTypeEnum.USERTASK.getType());
        adminAuditService6.setDescription("运营管理员审批");
        adminAuditService6.setSortNum(100.0f);
        adminAuditService6.setStatus(103);
        adminAuditService6.setConfigData("{\"approvalWay\":\"once\",\"candidateAuditable\":true,\"candidateGoback\":false,\"candidateThirds\":[{\"name\":\"wxh\",\"refId\":\"31891\"}],\"candidates\":[{\"name\":\"运营管理员(内置)\",\"refId\":\"301\",\"type\":\"bss\"}],\"editableFormTemplates\":[],\"formTemplate\":\"16\",\"notifyWays\":[\"sms\"]}");
        adminAuditService6.setNodeLevel("1");
        adminAuditService6.setNodeFeature("serial");
        adminAuditService6.setProcessIdentify("approval-703398693201510400:7:0000000101087");
        adminAuditService6.setOriginalNodeId(1354L);
        initNode(adminAuditService6, process, processVersion, opby);
        processNodeMapper.insertSelective(adminAuditService6);
        nodes.add(adminAuditService6);

        ProcessNode approveService7 = new ProcessNode();
        approveService7.setNodeName("领导审批");
        approveService7.setNodeType(NodeTypeEnum.USERTASK.getType());
        approveService7.setDescription("领导审批");
        approveService7.setSortNum(175.0f);
        approveService7.setStatus(103);
        approveService7.setConfigData("{\"approvalWay\":\"once\",\"candidateAuditable\":true,\"candidateGoback\":true,\"candidateThirds\":[{\"name\":\"dada\",\"refId\":\"31894\"}],\"candidates\":[{\"name\":\"运营管理员(内置)\",\"refId\":\"301\",\"type\":\"bss\"}],\"editableFormTemplates\":[16],\"formTemplate\":\"16\",\"notifyWays\":[\"sms\"]}");
        approveService7.setNodeLevel("1");
        approveService7.setNodeFeature("serial");
        approveService7.setProcessIdentify("approval-703398693201510400:7:0000000101087");
        approveService7.setOriginalNodeId(1369L);
        initNode(approveService7, process, processVersion, opby);
        processNodeMapper.insertSelective(approveService7);
        nodes.add(approveService7);

        process = processMapper.selectByPrimaryKey(process.getId());
        if (process == null) {
            throw new BizException("流程定义未找到");
        }
        List<ProcessVersion> processVersions = processVersionMapper.selectByProcessId(process.getId());
        if (processVersions.size() < 1) {
            throw new BizException("流程定义未找到");
        }
        processVersion = processVersions.get(0);

        nodes = processNodeMapper.selectByVersionId(processVersion.getId());
        processMgtDeployProcess(process, processVersion, nodes);
        updateProcessStatus(process.getId(), PROCESS_STATUS_NORMAL);
    }

    private static void initNode(ProcessNode node, Process process, ProcessVersion processVersion, String opby) {
        node.setProcessId(process.getId());
        node.setVersionId(processVersion.getId());
        node.setCreatedBy(opby);
        node.setCreatedDt(DateTime.now().toDate());
        node.setUpdatedBy(opby);
        node.setUpdatedDt(DateTime.now().toDate());
    }

    public void initReauditNoResourceHpcProcess() {
        // 标准版不包含hpc
        LicenseVo licenseVo = LicenseUtil.queryLicenseInfoFromDb();
        if (Objects.nonNull(licenseVo)) {
            String versionType = licenseVo.getVersionType();
            if (LicenseUtil.STAND.equals(versionType)) {
                return;
            }
        }
    }

    public void initReauditResourceHpcProcess() {
        // 标准版不包含hpc
        LicenseVo licenseVo = LicenseUtil.queryLicenseInfoFromDb();
        if (Objects.nonNull(licenseVo)) {
            String versionType = licenseVo.getVersionType();
            if (LicenseUtil.STAND.equals(versionType)) {
                return;
            }
        }
    }


}
