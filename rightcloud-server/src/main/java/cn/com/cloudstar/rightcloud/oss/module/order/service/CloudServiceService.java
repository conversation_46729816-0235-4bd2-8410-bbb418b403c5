/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.module.order.service;

import java.util.List;

import cn.com.cloudstar.rightcloud.core.pojo.dto.cloudmarket.CloudServiceApply;
import cn.com.cloudstar.rightcloud.core.pojo.vo.cloudmarket.MarketCatalogVO;
import cn.com.cloudstar.rightcloud.core.pojo.vo.cloudmarket.MarketEnvVO;

/**
 * <AUTHOR>
 * @date: 16:40 2019/10/30
 */
public interface CloudServiceService {
    List<MarketEnvVO> selectEnvService();

    /**
     * 动态云环境和服务
     **/
    List<MarketCatalogVO> selectDynamicEnvService();

    void applyService(CloudServiceApply cloudServiceApply);

    void applyServiceV2(CloudServiceApply cloudServiceApply);

    List<MarketCatalogVO> selectEnvMarketCatalog();
}