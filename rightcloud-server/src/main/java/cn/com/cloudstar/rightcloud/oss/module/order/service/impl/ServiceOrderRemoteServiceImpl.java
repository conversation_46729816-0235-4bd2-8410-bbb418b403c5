
package cn.com.cloudstar.rightcloud.oss.module.order.service.impl;

import cn.com.cloudstar.rightcloud.adapter.core.MQHelper;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.HPCClusterInfoIDResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.MAPoolsListQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.MAPoolsQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.result.MAPoolsListQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.result.MAPoolsQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.result.ServiceOrderQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.vo.HPCComputeNodeInfoVO;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.vo.HPCManagementNodeInfoVO;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.vo.HPCVNCNodeInfoVO;
import cn.com.cloudstar.rightcloud.basic.data.pojo.base.Base;
import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnv;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResBms;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResShare;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResVm;
import cn.com.cloudstar.rightcloud.common.constants.NotificationConsts;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.constants.res.status.ResHpcClusterNodeStatus;
import cn.com.cloudstar.rightcloud.common.enums.resource.HPCClusterTypeEnum;
import cn.com.cloudstar.rightcloud.common.mq.request.BaseNotificationMqBean;
import cn.com.cloudstar.rightcloud.common.mq.request.MailNotificationMq;
import cn.com.cloudstar.rightcloud.common.redis.JedisUtil;
import cn.com.cloudstar.rightcloud.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.common.util.MapsKit;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.core.pojo.dto.audit.ApproveOrderDTO;
import cn.com.cloudstar.rightcloud.core.pojo.dto.audit.ServiceOrder;
import cn.com.cloudstar.rightcloud.core.pojo.dto.audit.ServiceOrderDetail;
import cn.com.cloudstar.rightcloud.core.pojo.dto.audit.ServiceOrderPriceDetail;
import cn.com.cloudstar.rightcloud.core.pojo.dto.audit.ServiceOrderRecord;
import cn.com.cloudstar.rightcloud.core.pojo.dto.charge.BizBillingAccount;
import cn.com.cloudstar.rightcloud.core.pojo.dto.process.StationMessageMeta;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.Org;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.User;
import cn.com.cloudstar.rightcloud.module.support.access.cache.AuthUserHolder;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicInfoUtil;
import cn.com.cloudstar.rightcloud.oss.common.constants.MailTemplateConstants;
import cn.com.cloudstar.rightcloud.oss.common.constants.ModuleTypeConstants;
import cn.com.cloudstar.rightcloud.oss.common.constants.res.type.HpcPointType;
import cn.com.cloudstar.rightcloud.oss.common.constants.status.ContractStatus;
import cn.com.cloudstar.rightcloud.oss.common.constants.status.OrderStatus;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.OrderType;
import cn.com.cloudstar.rightcloud.oss.common.enums.CcmTaskStatusEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.ClusterTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.oss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.oss.common.pojo.LdapSyncRequest;
import cn.com.cloudstar.rightcloud.oss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.oss.common.util.*;
import cn.com.cloudstar.rightcloud.oss.module.access.service.PolicyService;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.model.BizContract;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.model.BizContractDetail;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.org.BizContractDetailMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.user.UserMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.service.org.OrgService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.user.UserService;
import cn.com.cloudstar.rightcloud.oss.module.contract.service.BizContractService;
import cn.com.cloudstar.rightcloud.oss.module.contract.service.IBizContractDetailService;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.ActionParam;
import cn.com.cloudstar.rightcloud.oss.module.feign.service.ResourceDcFeignService;
import cn.com.cloudstar.rightcloud.oss.module.operationcore.activiti.util.ProcessConstants;
import cn.com.cloudstar.rightcloud.oss.module.operationcore.service.process.OrderRecordService;
import cn.com.cloudstar.rightcloud.oss.module.operationcore.service.process.ProcessService;
import cn.com.cloudstar.rightcloud.oss.module.order.dao.*;
import cn.com.cloudstar.rightcloud.oss.module.order.entity.HPCDrpDeleteNodeSendMessageDTO;
import cn.com.cloudstar.rightcloud.oss.module.order.service.ServiceOrderService;
import cn.com.cloudstar.rightcloud.oss.module.pricing.dao.priceconfig.BizBillingAccountMapper;
import cn.com.cloudstar.rightcloud.oss.module.resource.entity.HpcBizContractDTO;
import cn.com.cloudstar.rightcloud.oss.module.resource.entity.HpcDrpDegradeCompensation;
import cn.com.cloudstar.rightcloud.oss.module.resource.entity.SfProductResourceCompensation;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.bean.model.SfProductResource;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.dao.SfProductResourceCompensationMapper;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.dao.SfProductResourceMapper;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.server.DrpReleaseResourceListener;
import cn.com.cloudstar.rightcloud.oss.module.system.service.message.BusinessNotificationService;
import cn.com.cloudstar.rightcloud.oss.module.system.service.message.NotificationService;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.cloud.request.CloudEnvParams;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.*;
import cn.com.cloudstar.rightcloud.remote.api.pojo.system.message.SendMessage;
import cn.com.cloudstar.rightcloud.remote.api.pojo.system.order.UpdateOrderStatusRequest;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.cloud.CloudEnvRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.hpc.HPCRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.hpc.ResHpcClusterDeleteNodeTaskRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.ma.MaRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.server.ResBmsRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.server.ResVmRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.share.ShareRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.system.service.system.ServiceOrderStatusRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.system.service.system.SysConfigRemoteService;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.util.DateUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.camunda.bpm.engine.TaskService;
import org.camunda.bpm.engine.impl.persistence.entity.IdentityLinkEntity;
import org.camunda.bpm.engine.task.IdentityLink;
import org.camunda.bpm.engine.task.Task;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static cn.com.cloudstar.rightcloud.common.constants.NotificationConsts.SEND_NOTIFICATION_EXCHANGE;

/**
 * CodeCityServiceImpl
 *
 * <AUTHOR>
 * @date 2021/12
 */
@Component
@DubboService
@Slf4j
public class ServiceOrderRemoteServiceImpl implements ServiceOrderStatusRemoteService {

    public static final String SUCCESS_MESSAGE = "成功";
    public static final String FAIL_MESSAGE = "失败";
    private static final String  PORT = "22";
    @Autowired
    private ServiceOrderService serviceOrderService;
    @Autowired
    private ServiceOrderMapper serviceOrderMapper;
    @Autowired
    private ServiceOrderRecordMapper serviceOrderRecordMapper;
    @Autowired
    private ServiceOrderDetailMapper serviceOrderDetailMapper;
    @Autowired
    private BizContractService bizContractService;
    @Autowired
    private IBizContractDetailService bizContractDetailService;
    @Autowired
    private BizContractDetailMapper bizContractDetailMapper;
    @DubboReference
    private HPCRemoteService hpcRemoteService;
    @DubboReference
    private ShareRemoteService shareRemoteService;
    @Autowired
    private ServiceOrderPriceDetailMapper serviceOrderPriceDetailMapper;
    @Autowired
    private ServiceOrderResourceRefMapper serviceOrderResourceRefMapper;
    @Autowired
    private SfProductResourceCompensationMapper sfProductResourceCompensationMapper;
    @Autowired
    NotificationService notificationService;
    @Autowired
    UserService userService;
    @Autowired
    private BizBillingAccountMapper bizBillingAccountMapper;
    @Autowired
    private TaskService taskService;

    @Autowired
    private OrderRecordService orderRecordService;
    @Autowired
    private ProcessService processService;
    @Autowired
    private BusinessNotificationService businessNotificationService;
    @DubboReference
    private CloudEnvRemoteService cloudEnvRemoteService;
    @Autowired
    private PolicyService policyService;
    @DubboReference
    private ResVmRemoteService resVmRemoteService;
    @DubboReference
    private ResBmsRemoteService resBmsRemoteService;
    @DubboReference
    private MaRemoteService maRemoteService;
    @DubboReference
    private ResHpcClusterDeleteNodeTaskRemoteService resHpcClusterDeleteNodeTaskRemoteService;
    @Autowired
    private OrgService orgService;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    RabbitTemplate rabbitTemplate;
    @Autowired
    private SysConfigRemoteService sysConfigRemoteService;
    @Autowired
    private SfProductResourceMapper sfProductResourceMapper;
    @Autowired
    private DrpReleaseResourceListener drpReleaseResourceListener;
    @Autowired
    private ResourceDcFeignService resourceDcFeignService;

    /**
     * 正在处理资源池列表
     */
    public static List<String> clusterKeys = Lists.newArrayList();

    @Override
    public ServiceOrderQueryResult selectByClusterId(Long clusterId) {
        List<ServiceOrder> clusters = serviceOrderMapper.selectByAllParams(new Criteria("clusterId", clusterId));
        if (CollUtil.isNotEmpty(clusters)) {
            return BeanConvertUtil.convert(clusters.get(0), ServiceOrderQueryResult.class);
        }
        return new ServiceOrderQueryResult();
    }

    @Override
    public ServiceOrderQueryResult selectReleaseOrderByClusterId(Long clusterId,String productCode) {
        Criteria criteria = new Criteria("clusterId", clusterId);
        criteria.put("type",OrderType.RELEASE);
        criteria.put("serviceTypeInDetail",Arrays.asList(productCode));
        List<ServiceOrder> releaseOrder = serviceOrderMapper.selectByAllParams(criteria);
        if (CollUtil.isNotEmpty(releaseOrder)) {
            return BeanConvertUtil.convert(releaseOrder.get(0), ServiceOrderQueryResult.class);
        }
        return new ServiceOrderQueryResult();
    }


    @Override
    public void undoServiceOrder(Long clusterId, String key) {
        Criteria criteria = new Criteria();
        criteria.put("clusterId", clusterId);
        criteria.put("serviceTypeInDetail", Arrays.asList(ProductCodeEnum.HPC.getProductCode()
                , ProductCodeEnum.HPC_DRP.getProductCode()
                , ProductCodeEnum.HPC_SAAS.getProductCode()));
        List<ServiceOrder> serviceOrders = serviceOrderMapper.selectByAllParams(criteria);
        ResHpcClusterRemoteModule resHpcClusterRemoteModule = hpcRemoteService.selectByPrimaryKey(clusterId);
        String currKey = resHpcClusterRemoteModule.getProessPhase() + resHpcClusterRemoteModule.getProessStatus();

        if (CollectionUtil.isEmpty(serviceOrders) || Objects.isNull(serviceOrders.get(0))) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1729141032));
        }

        // entity获取
        BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectByPrimaryKey(serviceOrders.get(0).getBizBillingAccountId());

        //执行方法的用户
        AuthUser admin = setAuthUser(serviceOrders.get(0).getUpdatedBy(), bizBillingAccount.getEntityId());

        ApproveOrderDTO approveOrderDTO = new ApproveOrderDTO();
        if (HPCClusterTypeEnum.getHPCPrivateTypes().contains(resHpcClusterRemoteModule.getClusterType())) {
            approveOrderDTO.setApproveAdvice("HPC专属资源池开通失败,系统自动回退");
        } else {
            approveOrderDTO.setApproveAdvice("HPC共享资源池开通失败,系统自动回退");
            ServiceOrder serviceOrderDetail = serviceOrderService.getServiceOrderDetail(serviceOrders.get(0).getId());
            List<ServiceOrderDetail> details = serviceOrderDetail.getDetails();
            if (CollectionUtil.isNotEmpty(details) && details.size() == 2) {
                //回退删除SFS ref
                ServiceOrderDetail sfsDetail = details.get(1);
                criteria = new Criteria();
                criteria.put("orderDetailId", sfsDetail.getId());
                int i = serviceOrderResourceRefMapper.deleteByParams(criteria);
                log.info("删除{}条ref", i);
            }
        }
        approveOrderDTO.setApproveType("02");
        approveOrderDTO.setUserSid(admin.getUserSid() + "");
        approveOrderDTO.setUserName("系统");

        approveOrderDTO.setId(serviceOrders.get(0).getId());
        approveOrderDTO.setHpcGoBack(true);
        approveOrderDTO.setInvokeBySchedule(Boolean.TRUE);

        String dealKey = "HPC-DRP-UndoServiceOrder-" + clusterId;
        if (clusterKeys.contains(dealKey)
                || (!Objects.equals(currKey, key) && "SAASPrivate".equals(resHpcClusterRemoteModule.getClusterType()))) {
            log.info("HPC专属资源池创建失败开始回退 正在处理资源池列表 已经处理[{}]、[{}]、[{}]", dealKey, currKey, key);
            AuthUserHolder.clear();
            return;
        }

        try {
            log.info("HPC专属资源池创建失败开始回退 正在处理资源池列表 加入新资源[{}]", dealKey);
            clusterKeys.add(dealKey);
            ServiceOrder serviceOrder = serviceOrderService.approveServiceOrder(approveOrderDTO, admin.getAccount(), admin);
            log.debug("HPC专属资源池创建失败回退成功:[{}]", JSONUtil.toJsonStr(serviceOrder));
        } finally {
            log.info("HPC专属资源池创建失败开始回退 正在处理资源池列表 列表[{}] 待删除资源[{}]", clusterKeys.toString(), dealKey);
            clusterKeys.remove(dealKey);
            log.info("HPC专属资源池创建失败开始回退 正在处理资源池列表 列表[{}]", clusterKeys.toString());
            AuthUserHolder.clear();
        }
    }

    /**
     * 专属资源池扩容失败处理
     * @param clusterId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleHpcDrpUpgradeFail(Long clusterId) {
        String assignee = null;
        Date current = new Date();
        Criteria criteria = new Criteria();
        criteria.put("clusterId", clusterId);
        criteria.put("type", OrderType.UPGRADE);
        criteria.put("serviceTypeInDetail", Arrays.asList(ProductCodeEnum.HPC_DRP.getProductCode()));
        criteria.setOrderByClause(" id desc");
        List<ServiceOrder> serviceOrders = serviceOrderMapper.selectByAllParams(criteria);

        ServiceOrder upgradeServiceOrder = serviceOrders.get(0);
        if (CollectionUtil.isEmpty(serviceOrders) || Objects.isNull(upgradeServiceOrder)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1729141032));
        }
        // entity获取
        BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectByPrimaryKey(serviceOrders.get(0).getBizBillingAccountId());

        //执行方法的用户
        AuthUser admin = setAuthUser(serviceOrders.get(0).getUpdatedBy(), bizBillingAccount.getEntityId());

        String orderSn = upgradeServiceOrder.getOrderSn();
        Task task = taskService.createTaskQuery().processInstanceBusinessKey(orderSn).singleResult();
        // 插入申请记录
        ServiceOrderRecord orderRecord = new ServiceOrderRecord();
        Long orderId = upgradeServiceOrder.getId();
        orderRecord.setOrderId(orderId);
        orderRecord.setOrderSn(orderSn);
        orderRecord.setAuditTime(current);
        orderRecord.setHandler("100");
        if(task!=null){
            orderRecord.setStep(task.getName());
            // 站内信
            assignee = task.getAssignee();
        }
        WebUserUtil.prepareInsertParams(orderRecord, "admin");
        orderRecord.setInfo("扩容申请失败");
        orderRecord.setHandlerMsg("");
        orderRecord.setCreatedBy("admin");
        orderRecord.setCreatedDt(current);
        orderRecord.setUpdatedBy("admin");
        orderRecord.setUpdatedDt(current);
        orderRecord.setHandlerMsg("");
        orderRecordService.insertSelective(orderRecord);
        List<Long> candidates = new ArrayList<>();
        if(StringUtils.isNotEmpty(assignee)){
            candidates.add(Long.valueOf(assignee));
        }else{
            List<IdentityLink> identityLinksForTask = taskService.getIdentityLinksForTask(task.getId());
            Set<IdentityLink> potentialOwners = new HashSet();
            Iterator<IdentityLink> iterator = identityLinksForTask.iterator();
            while(iterator.hasNext()) {
                IdentityLinkEntity identityLinkEntity = (IdentityLinkEntity)iterator.next();
                if ("candidate".equals(identityLinkEntity.getType())) {
                    potentialOwners.add(identityLinkEntity);
                }
            }
            candidates = potentialOwners
                    .stream()
                    .filter(c -> null != c.getUserId())
                    .map(c ->
                                 Long.parseLong(c.getUserId()))
                    .collect(Collectors.toList());

        }
        List<StationMessageMeta> stationMessageMetas = Lists.newArrayList();
        String createdBy = upgradeServiceOrder.getCreatedBy();
        String serviceOrderName = upgradeServiceOrder.getName();
        for (Long candidate : candidates) {
            Map<String, String> content = Maps.newHashMap();
            content.put("serviceType", ProductCodeEnum.HPC_DRP.getProductCode());
            content.put("orderSn", orderSn);
            content.put("id", orderId.toString());

            content.put("userName", createdBy);
            content.put("userAccount", createdBy);
            content.put("createdTime", DateUtils.format(upgradeServiceOrder.getCreatedDt(), DateUtil.COMMON_DATE_PATTERN));
            content.put("serviceName", serviceOrderName);

            stationMessageMetas.add(new StationMessageMeta(candidate,
                            NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_HPC_PRIVATE_SCALE_UP_ERROR,
                                                           content));
        }
        //异步发送站内信
        CompletableFuture.runAsync(() -> {
            try {
                sendStationMessages(stationMessageMetas,bizBillingAccount.getEntityId());
            }catch (Exception e){
                log.error("发送站内信异常:",e);
            }

        });
        AuthUserHolder.clear();
    }


    private void sendStationMessages(List<StationMessageMeta> stationMessageMetas,Long entityId) {
        for (StationMessageMeta messageMeta : stationMessageMetas) {
            BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
            baseNotificationMqBean.getToUserIds().add(messageMeta.getUserSid());
            baseNotificationMqBean.setEntityId(entityId);
            baseNotificationMqBean.setMsgId(messageMeta.getMessageId());
            baseNotificationMqBean.setMap(messageMeta.getContent());
            rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.OTHER, baseNotificationMqBean);
        }
    }

    @Override
    public void approveServiceOrder(Long clusterId, String key) {
        Criteria criteria = new Criteria();
        criteria.put("clusterId", clusterId);
        criteria.put("serviceTypeInDetail", Arrays.asList(ProductCodeEnum.HPC.getProductCode()
                , ProductCodeEnum.HPC_OFFLINE.getProductCode()
                , ProductCodeEnum.HPC_DRP.getProductCode()
                , ProductCodeEnum.HPC_SAAS.getProductCode()));
        criteria.setOrderByClause(" id desc");
        List<ServiceOrder> serviceOrders = serviceOrderMapper.selectByAllParams(criteria);
        ResHpcClusterRemoteModule resHpcClusterRemoteModule = hpcRemoteService.selectByPrimaryKey(clusterId);
        String currKey = resHpcClusterRemoteModule.getProessPhase() + resHpcClusterRemoteModule.getProessStatus();

        if (CollectionUtil.isEmpty(serviceOrders) || Objects.isNull(serviceOrders.get(0))) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1729141032));
        }


        // 从缓存获取审批意见
        String approveAdvice = JedisUtil.instance().get(serviceOrders.get(0).getRedisKeyHPCApproveRemark());
        if (StringUtils.isEmpty(approveAdvice)) {
            approveAdvice = "HPC共享资源池激活";
            if (HPCClusterTypeEnum.getHPCPrivateTypes().contains(resHpcClusterRemoteModule.getClusterType())) {
                approveAdvice = "HPC专属资源池激活";
            }
        }

        // entity获取
        BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectByPrimaryKey(serviceOrders.get(0).getBizBillingAccountId());

        //执行方法的用户
        AuthUser admin = setAuthUser(serviceOrders.get(0).getUpdatedBy(), bizBillingAccount.getEntityId());

        ApproveOrderDTO approveOrderDTO = new ApproveOrderDTO();
        approveOrderDTO.setApproveAdvice(approveAdvice);
        approveOrderDTO.setApproveType("01");
        approveOrderDTO.setUserSid(admin.getUserSid() + "");
        approveOrderDTO.setInvokeBySchedule(Boolean.TRUE);
        approveOrderDTO.setId(serviceOrders.get(0).getId());
        admin.setLicenceExpire(false);
        log.info("approveServiceOrder HPC专属资源池激活");
        ServiceOrder serviceOrder = null;
        String dealKey = "HPC-DRP-ApproveServiceOrder-" + clusterId;
        if (clusterKeys.contains(dealKey)
                || (!Objects.equals(currKey, key) && "SAASPrivate".equals(resHpcClusterRemoteModule.getClusterType()))) {

            AuthUserHolder.clear();
            return;
        }

        try {

            clusterKeys.add(dealKey);
            serviceOrder = serviceOrderService.approveServiceOrder(approveOrderDTO, admin.getAccount(), admin);
        } catch (BizException e){
            String exceptionMessage = e.getMessage();
            log.error("HPC专属资源池定时任务-ServiceOrderRemoteServiceImpl.approveServiceOrder-BizException异常；{}",exceptionMessage);
            resHpcClusterRemoteModule.setErrorInfo(exceptionMessage);
            hpcRemoteService.updateByPrimaryKeySelective(resHpcClusterRemoteModule);
        } catch (Exception e){
            log.error("HPC专属资源池定时任务-ServiceOrderRemoteServiceImpl.approveServiceOrder-异常；{}",e.getMessage());
        }finally {
            clusterKeys.remove(dealKey);

            AuthUserHolder.clear();
            JedisUtil.instance().del(serviceOrders.get(0).getRedisKeyHPCApproveRemark());
        }
    }


    /**
     * MA资源流程审批
     * @param clusterId
     */
    @Override
    public void approveMaServiceOrder(Long clusterId, String approveType) {
        log.info("MA资源流程审批");
        Criteria criteria = new Criteria();
        criteria.put("clusterId", clusterId);
        criteria.put("productName","昇腾Modelarts专属资源池");
        criteria.setOrderByClause(" id desc");
        List<ServiceOrder> serviceOrders = serviceOrderMapper.selectByAllParams(criteria);
        if (CollectionUtil.isEmpty(serviceOrders) || Objects.isNull(serviceOrders.get(0))) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1729141032));
        }
        // entity获取
        BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectByPrimaryKey(serviceOrders.get(0).getBizBillingAccountId());

        //执行方法的用户
        AuthUser admin = setAuthUser(serviceOrders.get(0).getUpdatedBy(), bizBillingAccount.getEntityId());

        String approveAdvice = JedisUtil.instance().get(serviceOrders.get(0).getRedisKeyMAApproveRemark());
        approveAdvice = "01".equals(approveType) ? approveAdvice : "失败，自动回退";
        ApproveOrderDTO approveOrderDTO = new ApproveOrderDTO();
        approveOrderDTO.setApproveType(approveType);
        approveOrderDTO.setUserSid(admin.getUserSid() + "");
        approveOrderDTO.setApproveAdvice(approveAdvice);
        approveOrderDTO.setId(serviceOrders.get(0).getId());
        admin.setLicenceExpire(false);
        log.info("MA自动化流程[{}]审批", "01".equals(approveType) ? "激活" : "回退");

        ServiceOrder serviceOrder = serviceOrderService.approveServiceOrder(approveOrderDTO, admin.getAccount(), admin);


        AuthUserHolder.clear();
        JedisUtil.instance().del(serviceOrders.get(0).getRedisKeyMAApproveRemark());
    }

    /**
     * MA资源流程审批
     * @param orderId
     * @param approveType 审批类型
     * @param code code
     */
    @Override
    public void approveResServiceOrder(Long orderId, String approveType,String code) {
        log.info("{}资源流程审批", code);
        ServiceOrder serviceOrder = serviceOrderMapper.selectByPrimaryKey(orderId);
        // entity获取
        BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectByPrimaryKey(serviceOrder.getBizBillingAccountId());
        //执行方法的用户
        AuthUser admin = setAuthUser(serviceOrder.getUpdatedBy(), bizBillingAccount.getEntityId());
        String approveAdvice = JedisUtil.instance().get(serviceOrder.getRedisKeyMAApproveRemark());
        approveAdvice = "01".equals(approveType) ? approveAdvice : "失败，自动回退";
        ApproveOrderDTO approveOrderDTO = new ApproveOrderDTO();
        approveOrderDTO.setApproveType(approveType);
        approveOrderDTO.setUserSid(admin.getUserSid() + "");
        approveOrderDTO.setApproveAdvice(approveAdvice);
        approveOrderDTO.setId(serviceOrder.getId());
        admin.setLicenceExpire(false);
        log.info("{}自动化流程[{}]审批", code, "01".equals(approveType) ? "激活" : "回退");
        serviceOrderService.approveServiceOrder(approveOrderDTO, admin.getAccount(), admin);
        AuthUserHolder.clear();
        JedisUtil.instance().del("Query"+ code +"StatusTask-" + orderId);
    }

    /**
     * 设置Auth信息
     *  @param account
     * @param entityId
     * @return
     */
    private AuthUser setAuthUser(String account, Long entityId) {
        cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser supportAuth = BasicInfoUtil.getUserInfoByAccount(account);
        supportAuth.setEntityId(entityId);
        supportAuth.setRemark(ModuleTypeConstants.FROM_COMMON);
        AuthUserHolder.setAuthUser(supportAuth);
        AuthUserHolder.setOrg(
                BeanConvertUtil.convert(BasicInfoUtil.getCurrentOrgInfo(supportAuth.getOrgSid()),
                        cn.com.cloudstar.rightcloud.module.support.access.pojo.Org.class));

        AuthUser ossUser = BeanConvertUtil.convert(supportAuth, AuthUser.class);
        return ossUser;
    }

    @Override
    public void updateServiceOrderErrorInfo(Long clusterId, String msg) {
        Criteria criteria = new Criteria();
        criteria.put("clusterId", clusterId);
        ServiceOrder serviceOrder = new ServiceOrder();
        serviceOrder.setErrorInfo(msg);
        serviceOrderService.updateByParamsSelective(serviceOrder, criteria);
    }


    @Override
    public void updateServiceOrderStatus(Long clusterId, String status) {
        Criteria criteria = new Criteria();
        criteria.put("clusterId", clusterId);
        criteria.put("serviceTypeInDetail", Arrays.asList(ProductCodeEnum.HPC.getProductCode()
                , ProductCodeEnum.HPC_DRP.getProductCode()
                , ProductCodeEnum.HPC_SAAS.getProductCode()));
        ServiceOrder serviceOrder = new ServiceOrder();
        serviceOrder.setStatus(status);
        serviceOrderService.updateByParamsSelective(serviceOrder, criteria);
    }


    @Override
    public void sendMessage(SendMessage sendMessage) {

        if (SendMessage.HPC_UNSUBSCRIBE_ERROR.equals(sendMessage.getType())) {
            log.info("开始发送退订hpc 失败邮件");
            String clusterId = sendMessage.getContent().get("clusterId");
            Long id = Long.valueOf(clusterId);
            ResHpcClusterRemoteModule resHpcClusterRemoteModule = hpcRemoteService.selectByPrimaryKey(id);
            List<ResShare> shareHPCSFS = hpcRemoteService.getShareHPCSFS(id);
            HashMap<String, String> messageContent = new HashMap<>();
            User user = userService.selectByPrimaryKey(resHpcClusterRemoteModule.getOwnerId());
            messageContent.put("applyUser", user.getAccount());
            messageContent.put("clusterId", resHpcClusterRemoteModule.getResourceId());
            messageContent.put("businessCategory", resHpcClusterRemoteModule.getBusinessCategory());
            messageContent.put("taskId", resHpcClusterRemoteModule.getUnsubscribeTaskId());
            messageContent.put("hasPrivateSFS", (shareHPCSFS.size() == 2) + "");
            if (shareHPCSFS.size() == 2) {
                ResShare pSfs = null;
                for (ResShare shareHPCSF : shareHPCSFS) {
                    if (!shareHPCSF.getIsVirtual()) {
                        pSfs = shareHPCSF;
                        messageContent.put("shareID", pSfs.getUuid());
                        messageContent.put("storageDir", pSfs.getExportLocation());
                        messageContent.put("sfsName", pSfs.getName());
                        break;
                    }
                }
            }
            //查找对应角色的用户
            //更新 accountName
            Criteria criteria = new Criteria();
            criteria.setCondition(MapsKit.of("roleSid", 301));
            List<User> userByRoleId = userService.findUserByRoleId(criteria);
            criteria.setCondition(MapsKit.of("roleSid", 305));
            userByRoleId.addAll(userService.findUserByRoleId(criteria));
            Set<Long> mailSet = userByRoleId.stream().map(User::getUserSid).collect(Collectors.toSet());
            if (CollectionUtil.isNotEmpty(mailSet) && mailSet.size() > 0) {
                BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
                baseNotificationMqBean.getImsgUserIds().addAll(mailSet);
                baseNotificationMqBean.setEntityId(1L);
                baseNotificationMqBean.setMsgId(NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_HPC_PRIVATE_UNSUBSCRIBE_ERROR);
                baseNotificationMqBean.setMap(messageContent);
                rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.OTHER, baseNotificationMqBean);
            }
        }
        if(SendMessage.HPC_DRP_DELETE_NODE_FAIL.equals(sendMessage.getType())){
            String clusterId = sendMessage.getContent().get("clusterId");
            if(StringUtils.isNotEmpty(clusterId)){
                hpcDrpDeleteNodeFailMail(Long.valueOf(clusterId));
            }
        }
    }


    private void hpcDrpDeleteNodeFailMail(Long clusterId) {


        ResHpcClusterRemoteModule resHpcClusterRemoteModule = hpcRemoteService.selectByPrimaryKey(clusterId);
        Criteria criteria = new Criteria();
        criteria.put("clusterUuid", resHpcClusterRemoteModule.getResourceId());
        criteria.put("clusterId", clusterId);
        List<ServiceOrder> serviceOrders = serviceOrderMapper.selectByAllParams(criteria);
        Long entityId = serviceOrders.get(0).getEntityId();

        Long orgSid = resHpcClusterRemoteModule.getOrgSid();
        Org org = orgService.selectByPrimaryKey(orgSid);

        Map sendAddr = new HashMap();
        Map messageContent = new HashMap();
        messageContent.put("applyUser", org.getOrgName());
        messageContent.put("clusterUuid", resHpcClusterRemoteModule.getResourceId());
        sendAddr.put("email", new ArrayList<>(sysConfigRemoteService.getSendMailUrl()));

        try {
            // 发送邮件给运维管理员
            notificationService.sendNotification(new int[]{1}, false,
                                                 sendAddr, messageContent,
                                                 cn.com.cloudstar.rightcloud.common.constants.MailTemplateConstants.HPC_DRP_NODE_DELETE_FAIL,
                                                 -1L);
            List<User> adminstrators = userMapper.findAdminstratorsByEntityId(entityId);
            // 发送站内信给运营管理员
            for (User adminUser:adminstrators) {
                HashMap<String, String> adminMessageContent = new HashMap<>(3);
                adminMessageContent.put("applyUser", org.getOrgName());
                adminMessageContent.put("clusterUuid", resHpcClusterRemoteModule.getResourceId());
                adminMessageContent.put("userAccount", adminUser.getAccount());
                notificationService.sendNotification(new int[]{0}, false,
                                                     sendAddr, adminMessageContent,
                                                     cn.com.cloudstar.rightcloud.common.constants.MailTemplateConstants.HPC_DRP_NODE_DELETE_FAIL,
                                                     entityId);
            }
        }catch (Exception e){
            log.error("HPCServiceImpl_releaseHPC_sendNotification_unsubscribe_hpc_error_{}",
                      e.getMessage());
        }

    }




    /**
     * 定时任务查看专属资源池创建
     * @param param
     * @param clusterId
     * @return MAPoolsQueryResult
     */
    @Override
    public MAPoolsQueryResult maTaskPoolsQuery(MAPoolsQuery param, Long clusterId) {
        log.info("查看专属资源池创建资源ID,{}",clusterId);
        Criteria criteria = new Criteria();
        criteria.put("clusterId", clusterId);
        criteria.put("productName","昇腾Modelarts专属资源池");
        criteria.setOrderByClause(" id desc");
        List<ServiceOrder> serviceOrders = serviceOrderMapper.selectByAllParams(criteria);
        MAPoolsQueryResult maPoolsQueryResult=new MAPoolsQueryResult();
        if(serviceOrders.size()>0){

            maPoolsQueryResult = AuthUtil.replaceUserToInvoke(
                    () -> serviceOrderService.maTaskPoolsQuerys(param), serviceOrders.get(0).getOwnerId());
            if (Objects.isNull(maPoolsQueryResult.getStatus())) {
                return maPoolsQueryResult;
            }
        }
        return maPoolsQueryResult;
    }

    @Override
    public MAPoolsListQueryResult mataskPoolsListQuery(HashMap<String, String> example) {
        MAPoolsListQueryResult maPoolsListQueryResult = AuthUtil.replaceUserToInvoke(
                () -> serviceOrderService.maPoolsListQuerys(example, false), Long.parseLong(example.get("userId")));
        return maPoolsListQueryResult;
    }

    @Override
    public void updateServiceOrderSelective(Long clusterId, String errorMessage) {
        Criteria criteria = new Criteria();
        criteria.put("clusterId", clusterId);
        criteria.put("productName","昇腾Modelarts专属资源池");
        criteria.put("type", OrderType.UPGRADE);
        criteria.setOrderByClause(" id desc");
        List<ServiceOrder> serviceOrders = serviceOrderMapper.selectByAllParams(criteria);
        ServiceOrder serviceOrder = serviceOrders.get(0);
        serviceOrder.setErrorInfo("更新专属资源池失败，请咨询运维管理员查看具体原因");

        serviceOrderService.updateByPrimaryKeySelective(serviceOrder);
    }

    /**
     * 同步集群信息
     *
     * @param clusterId
     */
    @Override
    public void syncRes(Long clusterId) {
        ResHpcClusterRemoteModule resHpcCluster = hpcRemoteService.selectByPrimaryKey(clusterId);
        log.info("HPC缩容同步开始");
        HpcSyncCloudEnvRequest hpcSyncCloudEnvRequest = new HpcSyncCloudEnvRequest();
        hpcSyncCloudEnvRequest.setCloudEnvId(getFDCloudEnv());
        hpcSyncCloudEnvRequest.setKey("HPC_CLUSTER");
        hpcSyncCloudEnvRequest.setUserSid(resHpcCluster.getOwnerId());
        hpcSyncCloudEnvRequest.setOrgSid(resHpcCluster.getOrgSid());
        hpcRemoteService.syncRes(hpcSyncCloudEnvRequest);
        log.info("HPC缩容同步开始");

    }

    /**
     * ldap同步
     *
     * @param clusterId
     */
    @Override
    public void syncLdap(Long clusterId) {
        ResHpcClusterRemoteModule resHpcCluster = hpcRemoteService.selectByPrimaryKey(clusterId);
        LdapSyncRequest ldapSyncRequest = new LdapSyncRequest();
        ldapSyncRequest.setOrgId(resHpcCluster.getOrgSid());
        List businessCategoryList = new ArrayList<>();
        businessCategoryList.add(resHpcCluster.getBusinessCategory());
        ldapSyncRequest.setRemoveBusinessCategoryList(businessCategoryList);
        policyService.synHpcToLdapByLdapSyncRequest(ldapSyncRequest);
    }

    @Override
    public Boolean checkPreAuditHpc(Long clusterId) {
        Boolean isAudited = true;
        Criteria criteria = new Criteria();
        criteria.put("clusterId", clusterId);
        criteria.put("serviceTypeInDetail", Collections.singletonList(ProductCodeEnum.HPC_SAAS.getProductCode()));
        criteria.setOrderByClause(" id desc");
        List<ServiceOrder> serviceOrders = serviceOrderMapper.selectByAllParams(criteria);
        if (CollectionUtil.isEmpty(serviceOrders) || Objects.isNull(serviceOrders.get(0))) {
            return isAudited;
        }
        Criteria criteria1 = new Criteria();
        criteria1.put("orderSn", serviceOrders.get(0).getOrderSn());
        List<ServiceOrderRecord> serviceOrderRecords = serviceOrderRecordMapper.selectByParams(criteria1);
        if (!CollectionUtils.isEmpty(serviceOrderRecords)) {
            ServiceOrderRecord serviceOrderRecord = serviceOrderRecords.get(serviceOrderRecords.size() - 1);
            if (!"运营管理员预审批".equals(serviceOrderRecord.getStep())) {
                isAudited = false;
            }
        }
        return isAudited;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleContinueUp(Long clusterId, boolean status) {
        Criteria criteria = new Criteria();
        criteria.put("clusterId", clusterId);
        criteria.put("serviceTypeInDetail", Arrays.asList(ProductCodeEnum.HPC.getProductCode()
                , ProductCodeEnum.HPC_DRP.getProductCode()));
        criteria.setOrderByClause(" id desc");
        List<ServiceOrder> serviceOrders = serviceOrderMapper.selectByAllParams(criteria);
        if (CollectionUtil.isEmpty(serviceOrders) || Objects.isNull(serviceOrders.get(0))) {
            throw new BizException("订单异常，请检查！");
        }

        ServiceOrder serviceOrder = serviceOrders.get(0);
        Task task = taskService.createTaskQuery().processInstanceBusinessKey(serviceOrder.getOrderSn()).singleResult();
        if (Objects.isNull(task)) {
            throw new BizException("审批任务不存在或者已经被其他人处理");
        }

        BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectByPrimaryKey(serviceOrders.get(0).getBizBillingAccountId());
        log.info("ServiceOrderRemoteServiceImpl.handleContinueUp 账户信息：[{}]", JSONUtil.toJsonStr(bizBillingAccount));

        //执行方法的用户
        AuthUser admin = setAuthUser(serviceOrder.getUpdatedBy(), bizBillingAccount.getEntityId());


        String userName = admin.getAccount();

        String approveDesc = String.format("由[%s]审批重新扩容回退-%s", userName, status ? SUCCESS_MESSAGE : FAIL_MESSAGE);
        Map<String, Object> variables = Maps.newHashMap();
        variables.put("_audit_uname", userName);
        variables.put("_audit_account", userName);
        variables.put(ProcessConstants.AUDIT_COMMENT, approveDesc);

        // 从缓存获取审批意见
        String approveAdvice = JedisUtil.instance().get(serviceOrders.get(0).getRedisKeyHPCApproveRemark());
        if (StringUtils.isEmpty(approveAdvice)) {
            approveAdvice = "专属资源池重新扩容回退";
        }

        // 插入申请记录
        ServiceOrderRecord orderRecord = new ServiceOrderRecord();
        orderRecord.setOrderId(serviceOrder.getId());
        orderRecord.setOrderSn(serviceOrder.getOrderSn());
        orderRecord.setAuditTime(new Date());
        orderRecord.setHandler(admin.getUserSid().toString());
        orderRecord.setStep(task.getName());
        WebUserUtil.prepareInsertParams(orderRecord, userName);

        orderRecord.setStep("运营管理员审批重新扩容回退" + (status ? "【成功】" : "【失败】"));
        orderRecord.setInfo(approveDesc);
        orderRecord.setHandlerMsg(approveAdvice);
        orderRecordService.insertSelective(orderRecord);

        if (status) { /* 缩容成功才回退 */
            try {
                processService.taskGoback(null, serviceOrder.getOrderSn(), variables);

            } catch (Exception e) {
                log.error("------------------> 重新扩容回退节点流程失败。 error: ", e);
                throw new BizException("重新扩容回退节点流程失败");
            }
        }

        ResHpcClusterRemoteModule resHpcClusterRemoteModule = new ResHpcClusterRemoteModule();
        resHpcClusterRemoteModule.setId(clusterId);
        if (status) {
            resHpcClusterRemoteModule.setProessPhase(CcmTaskStatusEnum.CONTINUE_UPGRADE_RESOURCE.getValue());
            resHpcClusterRemoteModule.setProessStatus(CcmTaskStatusEnum.WAITING.getValue());
        } else {
            resHpcClusterRemoteModule.setErrorInfo("HPC专属资源重新扩容-缩容节点申请失败，错误详情请登录CCM查看");
            resHpcClusterRemoteModule.setProessPhase(CcmTaskStatusEnum.CONTINUE_UPGRADE_SHRINKAGE.getValue());
            resHpcClusterRemoteModule.setProessStatus(CcmTaskStatusEnum.FAILED.getValue());
        }

        hpcRemoteService.updateHpcCluster(resHpcClusterRemoteModule);
    }

    /**
     * HPC专属资源池缩容成功处理
     *
     * @param clusterId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleHpcDrpDegradeSuccess(Long clusterId) {

        Date current = new Date();

        //获取待删除的节点信息
        List<NodeInfo> manageList = new ArrayList<>();
        List<ResVmNodeInfo> resVmNodeInfoList = resVmRemoteService.getNodeInfoListByClusterId(clusterId).stream()
                .filter(node -> StringUtils.equalsIgnoreCase(ResHpcClusterNodeStatus.REMOVING, node.getStatus())).collect(Collectors.toList());
        manageList.addAll(resVmNodeInfoList);
        List<NodeInfo> computeList = new ArrayList<>();
        List<ResBmsNodeInfo> bmsNodeList = resBmsRemoteService.getNodeInfoListByClusterId(clusterId).stream()
                .filter(node -> StringUtils.equalsIgnoreCase(ResHpcClusterNodeStatus.REMOVING, node.getStatus())).collect(Collectors.toList());
        computeList.addAll(bmsNodeList);

        Criteria criteria = new Criteria();
        criteria.put("cluster_id", clusterId);
        criteria.put("product_type", ProductCodeEnum.HPC_DRP.getProductCode());
        List<SfProductResource> sfProductResources = sfProductResourceMapper.selectByParams(criteria);
        SfProductResource sfProductResource = CollectionUtil.getFirst(sfProductResources);

        Long sfProductResourceId = sfProductResource.getId();

        //获取所有申请，续订的基础节点的价格详情
        List<ServiceOrderPriceDetail> priceDetails = getApplyAndRenewOrderPriceDetails(current, sfProductResourceId);
        //获取缩容订单
        ServiceOrder degradeServiceOrder = getDegradeServiceOrder(clusterId);
        //计算出补偿
        HpcDrpDegradeCompensation totalCompesation =
                this.totalCompensation(degradeServiceOrder, computeList, manageList, priceDetails);

        //更新集群结束时间
        long compensationDays = totalCompesation.getCompensationDays();
        this.changeResourceEndTime(clusterId, current, sfProductResource, compensationDays);

        //更新合同明细缩容数据
        List<HpcBizContractDTO> contractList = totalCompesation.getBizContractList();
        List<BizContractDetail> detailList = contractList.stream().map(HpcBizContractDTO::getContractDetail).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(detailList)) {
            for (BizContractDetail bizContractDetail : detailList) {
                bizContractDetailMapper.updateByPrimaryKeySelective(bizContractDetail);
            }

        }
        //更新未使用的合同明细节点数
        bizContractDetailService.updateUnusedDetail(contractList);

        List<Long> bizContractIdList = detailList.stream().map(BizContractDetail::getContractSid).collect(Collectors.toList());
        String bizContractIdStr = bizContractIdList.toString();

        degradeServiceOrder.setCompensationContractId(bizContractIdStr);
        degradeServiceOrder.setCompensationDays(compensationDays);
        degradeServiceOrder.setStatus(OrderStatus.COMPLETED);
        serviceOrderMapper.updateByPrimaryKeySelective(degradeServiceOrder);
        //保存变更记录


        hpcRemoteService.clearRemovingNodeAndRecord(clusterId);
        this.syncRes(clusterId);
        //发邮件，站内信等
        HPCDrpDeleteNodeSendMessageDTO hpcDrpDeleteNodeSendMessageDTO = new HPCDrpDeleteNodeSendMessageDTO();
        hpcDrpDeleteNodeSendMessageDTO.setClusterId(clusterId);
        hpcDrpDeleteNodeSendMessageDTO.setMessageTemplate(
                NotificationConsts.ConsoleMsg.ProductMsg.TENANT_HPC_PRIVATE_SCALE_DOWN);
        hpcDrpDeleteNodeSendMessageDTO.setStatus(SUCCESS_MESSAGE);
        hpcDrpDeleteNodeSendMessageDTO.setManageList(manageList.size());
        hpcDrpDeleteNodeSendMessageDTO.setVncNodeList(manageList);
        hpcDrpDeleteNodeSendMessageDTO.setCliNodeList(manageList);
        hpcDrpDeleteNodeSendMessageDTO.setComputeList(computeList.size());
        hpcDrpDeleteNodeSendMessageDTO.setCompensateDays(compensationDays);
        hpcDrpDeleteNodeSendMessageDTO.setEndTime(sfProductResource.getEndTime());
        hpcDrpDeleteNodeSendMessageDTO.setOrderSn(degradeServiceOrder.getOrderSn());
        hpcDrpDeleteNodeSendMessage(hpcDrpDeleteNodeSendMessageDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleHpcDrpDegradeFailByAdmin(Long clusterId) {
        HPCClusterInfoIDResult currentHpcClusterInfo = hpcRemoteService.getHPCClusterInfoById(clusterId);
        List<HPCManagementNodeInfoVO> managementNodeInfoList = currentHpcClusterInfo.getHPCClusterInfo().getManagementNodeInfo();
        List<HPCComputeNodeInfoVO> computeNodeInfoList = currentHpcClusterInfo.getHPCClusterInfo().getComputeNodeInfo();
        List<HPCVNCNodeInfoVO> vncNodeInfoList = currentHpcClusterInfo.getHPCClusterInfo().getVncNodeInfo();
        Map<String, HPCManagementNodeInfoVO> managementNodeInfoVOMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(managementNodeInfoList)) {
            managementNodeInfoVOMap = managementNodeInfoList.stream().collect(Collectors.toMap(HPCManagementNodeInfoVO::getResourceID, node -> node));
        }
        Map<String, HPCComputeNodeInfoVO> computeNodeInfoVOMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(computeNodeInfoList)) {
            computeNodeInfoVOMap =
                    computeNodeInfoList.stream().collect(Collectors.toMap(HPCComputeNodeInfoVO::getResourceID, node -> node));
        }
        Map<String, HPCVNCNodeInfoVO> hpcVNCNodeInfoVOMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(vncNodeInfoList)) {
            hpcVNCNodeInfoVOMap = vncNodeInfoList.stream().collect(Collectors.toMap(HPCVNCNodeInfoVO::getResourceID, node -> node));
        }

        //删除失败的节点数组
        List<NodeInfo> failDelteNodeList = new ArrayList<>();
        //待删除的管理节点
        List<NodeInfo> manageNodeList = new ArrayList<>();
        List<ResVmNodeInfo> resVmNodeInfoList = resVmRemoteService.getNodeInfoListByClusterId(clusterId);
        if (CollectionUtil.isNotEmpty(resVmNodeInfoList)) {
            for (ResVmNodeInfo node : resVmNodeInfoList) {
                if (StringUtils.equalsIgnoreCase(ResHpcClusterNodeStatus.REMOVING, node.getStatus())) {
                    String instanceId = node.getInstanceId();
                    if (managementNodeInfoVOMap.containsKey(instanceId) || hpcVNCNodeInfoVOMap.containsKey(instanceId)) {
                        HPCManagementNodeInfoVO hpcManagementNodeInfoVO = managementNodeInfoVOMap.get(instanceId);
                        if (hpcManagementNodeInfoVO != null) {
                            node.setStatus(hpcManagementNodeInfoVO.getNodeStatus());
                        }
                        HPCVNCNodeInfoVO hpcvncNodeInfoVO = hpcVNCNodeInfoVOMap.get(instanceId);
                        if (hpcvncNodeInfoVO != null) {
                            node.setStatus(hpcvncNodeInfoVO.getNodeStatus());
                        }
                        failDelteNodeList.add(node);
                    } else {
                        manageNodeList.add(node);
                    }
                }
            }
        }
        //待删除的计算节点
        List<NodeInfo> computeNodeList = new ArrayList<>();
        List<ResBmsNodeInfo> resBmsNodeList = resBmsRemoteService.getNodeInfoListByClusterId(clusterId);
        if (CollectionUtil.isNotEmpty(resBmsNodeList)) {
            for (ResBmsNodeInfo node : resBmsNodeList) {
                if (StringUtils.equalsIgnoreCase(ResHpcClusterNodeStatus.REMOVING, node.getStatus())) {
                    String instanceId = node.getInstanceId();
                    if (computeNodeInfoVOMap.containsKey(instanceId)) {
                        HPCComputeNodeInfoVO hpcComputeNodeInfoVO = computeNodeInfoVOMap.get(instanceId);
                        if (hpcComputeNodeInfoVO != null) {
                            node.setStatus(hpcComputeNodeInfoVO.getNodeStatus());
                        }
                        failDelteNodeList.add(node);
                    } else {
                        computeNodeList.add(node);
                    }
                }
            }
        }
        //更新删除失败的节点状态
        this.changeFailDeleteNodeStatus(failDelteNodeList, clusterId);
        ServiceOrder degradeServiceOrder = getDegradeServiceOrder(clusterId);
        if (CollectionUtil.isNotEmpty(computeNodeList) || CollectionUtil.isNotEmpty(manageNodeList)) {
            //处理过期合同
            this.handlingExpiredContracts(clusterId, computeNodeList, manageNodeList);
            //发邮件，站内信等
            HPCDrpDeleteNodeSendMessageDTO hpcDrpDeleteNodeSendMessageDTO = new HPCDrpDeleteNodeSendMessageDTO();
            hpcDrpDeleteNodeSendMessageDTO.setClusterId(clusterId);
            hpcDrpDeleteNodeSendMessageDTO.setMessageTemplate(
                    NotificationConsts.ConsoleMsg.ProductMsg.TENANT_HPC_PRIVATE_SCALE_DOWN);
            hpcDrpDeleteNodeSendMessageDTO.setStatus(SUCCESS_MESSAGE);
            hpcDrpDeleteNodeSendMessageDTO.setManageList(manageNodeList.size());
            hpcDrpDeleteNodeSendMessageDTO.setVncNodeList(manageNodeList);
            hpcDrpDeleteNodeSendMessageDTO.setCliNodeList(manageNodeList);
            hpcDrpDeleteNodeSendMessageDTO.setComputeList(computeNodeInfoList.size());
            hpcDrpDeleteNodeSendMessageDTO.setCompensateDays(0);
            hpcDrpDeleteNodeSendMessageDTO.setFailDelteNodeList(failDelteNodeList);
            hpcDrpDeleteNodeSendMessageDTO.setOrderSn(degradeServiceOrder.getOrderSn());
            hpcDrpDeleteNodeSendMessage(hpcDrpDeleteNodeSendMessageDTO);

            hpcRemoteService.clearRemovingNode(clusterId);
        } else {
            //发送邮件，站内信等
            HPCDrpDeleteNodeSendMessageDTO hpcDrpDeleteNodeSendMessageDTO = new HPCDrpDeleteNodeSendMessageDTO();
            hpcDrpDeleteNodeSendMessageDTO.setMessageTemplate(
                    NotificationConsts.ConsoleMsg.ProductMsg.TENANT_HPC_PRIVATE_SCALE_DOWN);
            hpcDrpDeleteNodeSendMessageDTO.setClusterId(clusterId);
            hpcDrpDeleteNodeSendMessageDTO.setStatus(FAIL_MESSAGE);
            hpcDrpDeleteNodeSendMessageDTO.setFailDelteNodeList(failDelteNodeList);
            hpcDrpDeleteNodeSendMessageDTO.setOrderSn(degradeServiceOrder.getOrderSn());
            hpcDrpDeleteNodeSendMessage(hpcDrpDeleteNodeSendMessageDTO);
            resHpcClusterDeleteNodeTaskRemoteService.deleteByClusterId(clusterId);

        }
        this.refreshCli(clusterId, managementNodeInfoList);
        this.syncRes(clusterId);

    }

    private void refreshCli(Long clusterId, List<HPCManagementNodeInfoVO> managementNodeInfoList) {
        String port = cn.com.cloudstar.rightcloud.common.util.PropertiesUtil.getProperty("login.node.port");
        if (Objects.isNull(port)){
            port = PORT;
        }
        ResHpcClusterRemoteModule resHpcClusterRemoteModule = hpcRemoteService.selectByPrimaryKey(clusterId);
        if (CollectionUtil.isNotEmpty(managementNodeInfoList)) {
            StringBuilder externalSb = new StringBuilder();
            StringBuilder internalSb = new StringBuilder();
            for (HPCManagementNodeInfoVO hpcManagementNodeInfoVO : managementNodeInfoList) {
                if (StringUtils.equalsIgnoreCase(hpcManagementNodeInfoVO.getHPCNodeType(), HpcPointType.CCS_CLI)) {
                    log.info("HPC集群信息更新-updateHPCClusterCreateTask-CLI处理");
                    if (externalSb.length() > 0) {
                        externalSb.append(StrUtil.COMMA);
                    }
                    externalSb.append(hpcManagementNodeInfoVO.getEIP()).append(StrUtil.COLON + port);
                    if (internalSb.length() > 0) {
                        internalSb.append(StrUtil.COMMA);
                    }
                    internalSb.append(hpcManagementNodeInfoVO.getVIP()).append(StrUtil.COLON + port);
                }
            }
            if (externalSb.length() > 0) {
                resHpcClusterRemoteModule.setLoginNodeExternalAddress(externalSb.toString());
            }
            if (internalSb.length() > 0) {
                resHpcClusterRemoteModule.setLoginNodeInternalAddress(internalSb.toString());
            }
        }
        hpcRemoteService.updateByPrimaryKeySelective(resHpcClusterRemoteModule);
    }

    /**
     * 更新删除失败的节点状态
     *
     * @param failDelteNodeList
     */
    private void changeFailDeleteNodeStatus(List<NodeInfo> failDelteNodeList, Long clusterId) {
        failDelteNodeList.stream().forEach(node -> {
            if (StringUtils.equalsIgnoreCase(ProductCodeEnum.ECS.getProductCode(), node.getResourceType())) {
                ResVm resVm = new ResVm();
                resVm.setId(node.getId());
                resVm.setStatus(node.getStatus());
                resVmRemoteService.updateByPrimaryKeySelective(resVm);
            }
            if (StringUtils.equalsIgnoreCase(ProductCodeEnum.BMS.getProductCode(), node.getResourceType())) {
                ResBms resBms = new ResBms();
                resBms.setId(node.getId());
                resBms.setStatus(node.getStatus());
                resBmsRemoteService.updateByPrimaryKeySelective(resBms);
            }
            ResHpcClusterDeleteNodeTaskDeleteRequest
                    resHpcClusterDeleteNodeTaskDeleteRequest = new ResHpcClusterDeleteNodeTaskDeleteRequest();
            resHpcClusterDeleteNodeTaskDeleteRequest.setClusterId(clusterId);
            resHpcClusterDeleteNodeTaskDeleteRequest.setResourceId(Long.valueOf(node.getId()));
            resHpcClusterDeleteNodeTaskDeleteRequest.setResourceType(node.getResourceType());
            resHpcClusterDeleteNodeTaskRemoteService.deleteByResourceId(resHpcClusterDeleteNodeTaskDeleteRequest);
        });
    }

    @Override
    public void updateOrderStatusByClusterId(UpdateOrderStatusRequest updateOrderStatusRequest) {
        Criteria serviceOrderCri = new Criteria();
        serviceOrderCri.put("clusterId", updateOrderStatusRequest.getClusterId());
        serviceOrderCri.put("serviceTypeInDetail", Arrays.asList(updateOrderStatusRequest.getProductType()));
        serviceOrderCri.put("type", updateOrderStatusRequest.getOrderType());
        serviceOrderCri.setOrderByClause(" created_dt desc ");
        List<ServiceOrder> serviceOrderList = serviceOrderService.selectByParams(serviceOrderCri);
        ServiceOrder lastOrder = CollectionUtil.getFirst(serviceOrderList);
        if(OrderStatus.COMPLETED.equals(updateOrderStatusRequest.getStatus()) && OrderType.RELEASE.equals(updateOrderStatusRequest.getOrderType())){
            lastOrder.setStepName("退订完成");
        }
        lastOrder.setStatus(updateOrderStatusRequest.getStatus());
        lastOrder.setUpdatedDt(new Date());
        serviceOrderMapper.updateByPrimaryKeySelective(lastOrder);
    }

    @Override
    public void updateOrderDetailProductConfigDesc(Long shareId, String uuid) {
        ServiceOrderDetail serviceOrderDetail = serviceOrderDetailMapper.selectServiceOrderDetailByShareId(shareId);
        if (serviceOrderDetail == null) {
            log.error("弹性文件修改订单资源ID失败【订单详情不存在】 ServiceOrderRemoteServiceImpl.updateOrderDetailProductConfigDesc OUTPUT: {}", shareId);
            return;
        }

        String productConfigDesc = serviceOrderDetail.getProductConfigDesc();
        JSONArray array = JSON.parseArray(productConfigDesc);
        if (CollectionUtil.isNotEmpty(array)) {
            for (int i = 0; i < array.size(); i++) {
                JSONObject object = array.getJSONObject(i);
                String label = object.getString("label");
                String attrKey = object.getString("attrKey");
                if (StringUtils.isNotBlank(label) && StringUtil.isNotBlank(attrKey)
                        && "id".equals(attrKey) && "资源ID".equals(label)) {
                    object.put("value", uuid);
                    break;
                }
            }
        }
        productConfigDesc = JSON.toJSONString(array);
        serviceOrderDetail.setProductConfigDesc(productConfigDesc);

        serviceOrderDetailMapper.updateByPrimaryKeySelective(serviceOrderDetail);

    }
    /**
     * 修改资源池结束时间
     *
     * @param clusterId
     * @param current
     * @param sfProductResource
     * @param compensationDays
     */
    private void changeResourceEndTime(Long clusterId, Date current, SfProductResource sfProductResource, long compensationDays) {
        Date sfProductResourceEndTime = sfProductResource.getEndTime();
        Long sfProductResourceId = sfProductResource.getId();
        Date lastEndTime = org.apache.commons.lang3.time.DateUtils.addDays(sfProductResourceEndTime, (int) compensationDays);
        sfProductResource.setUpdatedDt(current);
        sfProductResource.setEndTime(lastEndTime);
        sfProductResourceMapper.updateByPrimaryKey(sfProductResource);
        //记录补偿时间
        if (compensationDays > 0) {
            SfProductResourceCompensation sfProductResourceCompensation = new SfProductResourceCompensation();
            sfProductResourceCompensation.setSfProductResourceId(sfProductResourceId);
            sfProductResourceCompensation.setStartTime(sfProductResourceEndTime);
            sfProductResourceCompensation.setEndTime(lastEndTime);
            sfProductResourceCompensation.setCreatedDt(current);
            sfProductResourceCompensation.setUpdatedDt(current);
            sfProductResourceCompensation.setCompensationDays((int) compensationDays);
            sfProductResourceCompensationMapper.insert(sfProductResourceCompensation);
        }
        //修改集群时间
        ResHpcClusterRemoteModule hpcClusterRemoteModule = new ResHpcClusterRemoteModule();
        hpcClusterRemoteModule.setId(clusterId);
        hpcClusterRemoteModule.setEndTime(lastEndTime);
        hpcRemoteService.updateByPrimaryKeySelective(hpcClusterRemoteModule);
        ResHpcClusterRemoteModule resHpcCluster = hpcRemoteService.selectByPrimaryKey(clusterId);
        if (ClusterTypeEnum.PRE_SAAS_SHARE.code().equals(resHpcCluster.getClusterType())) {
            //预部署资源池修改内置弹性文件的结束时间，补偿天数
            List<ResShare> shareHPCSFS = hpcRemoteService.getDefaultShareHPCSFS(clusterId);
            if (shareHPCSFS.size() > 0) {
                log.info("关联的内置弹性文件-ServiceOrderServiceImpl.createDegradeServiceOrderDetail-shareHPCSFS:[{}]", JSON.toJSONString(shareHPCSFS));
                for (ResShare shareSfs : shareHPCSFS) {
                    shareSfs.setEndTime(lastEndTime);
                    shareRemoteService.updateByPrimaryKey(shareSfs);
                }

            }
        }
    }


    private List<ServiceOrderPriceDetail> getApplyAndRenewOrderPriceDetails(Date payTime, Long productResourceId) {
        Criteria criteria = new Criteria();
        criteria.put("refInstanceIdLike", "\"" + productResourceId + "\"");
        criteria.put("endTime", payTime);
        criteria.put("orderTypes", Lists.newArrayList("apply", "renew"));
        criteria.put("orderStatus", "completed");
        criteria.put("productCodeIn", Arrays.asList(ProductCodeEnum.HPC_DRP.getProductCode(),
                ProductCodeEnum.BMS.getProductCode(),
                ProductCodeEnum.ECS.getProductCode(),
                ProductCodeEnum.SFS2.getProductCode()));
        List<ServiceOrderPriceDetail> priceDetails = serviceOrderPriceDetailMapper.selectSerivceOrderPriceDetailByCriteria(criteria);
        priceDetails = priceDetails.stream()
                .sorted(Comparator.comparing(ServiceOrderPriceDetail::getEndTime).reversed())
                .collect(Collectors.toList());
        return priceDetails;
    }

    private ServiceOrder getDegradeServiceOrder(Long clusterId) {
        Criteria serviceOrderCri = new Criteria();
        serviceOrderCri.put("clusterId", clusterId);
        serviceOrderCri.put("serviceTypeInDetail", Arrays.asList(ProductCodeEnum.HPC_DRP.getProductCode()));
        serviceOrderCri.put("type", OrderType.DEGRADE);
        serviceOrderCri.put("status", OrderStatus.PENDING);
        serviceOrderCri.setOrderByClause(" created_dt desc ");
        List<ServiceOrder> serviceOrderList = serviceOrderService.selectByParams(serviceOrderCri);
        ServiceOrder degradeServiceOrder = CollectionUtil.getFirst(serviceOrderList);
        return degradeServiceOrder;
    }

    /**
     * 计算总补偿天数
     *
     * @param degradeServiceOrder
     * @param computeNodeList
     * @param managerNodeInfoList
     * @param priceDetails
     */
    private HpcDrpDegradeCompensation totalCompensation(ServiceOrder degradeServiceOrder, List<NodeInfo> computeNodeList,
                                                        List<NodeInfo> managerNodeInfoList, List<ServiceOrderPriceDetail> priceDetails) {
        //
        Long clusterId = degradeServiceOrder.getClusterId();
        Criteria criteria = new Criteria();
        criteria.put("orderTypeIn", Arrays.asList(OrderType.UPGRADE, OrderType.UPGRADE_RENEW));
        criteria.put("status", OrderStatus.COMPLETED);
        criteria.put("clusterId", clusterId);
        List<ServiceOrder> serviceOrderList = serviceOrderMapper.selectByParams(criteria);


        //查询集群扩容相关的合同
        List<Long> contractIdList = serviceOrderList.stream().filter(order -> {

            Criteria orderDetailCri = new Criteria();
            orderDetailCri.put("orderId", order.getId());
            orderDetailCri.put("serviceType", ProductCodeEnum.HPC_DRP.getProductCode());
            int countByParams = serviceOrderDetailMapper.countByParams(orderDetailCri);
            return countByParams > 0 && Objects.nonNull(order.getContractId());
        }).map(order -> Long.valueOf(order.getContractId())).collect(Collectors.toList());

        List<HpcBizContractDTO> hpcBizContractDTOS = bizContractService.queryHpcContractTreeByIdList(contractIdList,
                Arrays.asList(ContractStatus.AUDIT_EXECUTING, ContractStatus.EXPIRED));
        log.debug("扩容合同：{}", hpcBizContractDTOS);
        HpcDrpDegradeCompensation totalCompensation = new HpcDrpDegradeCompensation();
        //查询所有执行中的续订合同,计算出补偿金额
        HpcDrpDegradeCompensation computeCompensation =
                computeCompensation(degradeServiceOrder, priceDetails, computeNodeList, hpcBizContractDTOS, 0);

        List<NodeInfo> cliNodeList = managerNodeInfoList.stream()
                .filter(nodeInfo -> HpcPointType.CCS_CLI.equalsIgnoreCase(nodeInfo.getHpcPointType()))
                .collect(Collectors.toList());
        HpcDrpDegradeCompensation cliCompensation = computeCompensation(degradeServiceOrder, priceDetails, cliNodeList, hpcBizContractDTOS, 1);
        List<NodeInfo> vncNodeList = managerNodeInfoList.stream()
                .filter(nodeInfo -> HpcPointType.VNC.equalsIgnoreCase(nodeInfo.getHpcPointType()))
                .collect(Collectors.toList());

        HpcDrpDegradeCompensation vncCompensation = computeCompensation(degradeServiceOrder, priceDetails, vncNodeList, hpcBizContractDTOS, 2);
        //原始的管理节点不会被缩容
        log.info("计算节点补偿金额{}", computeCompensation.getCompensationAmount());
        log.info("登录节点补偿金额{}", cliCompensation.getCompensationAmount());
        log.info("vnc节点补偿金额{}", vncCompensation.getCompensationAmount());
        //总的补偿金额
        BigDecimal totalCompesationAmount =
                computeTotalCompensationAmount(computeCompensation, cliCompensation, vncCompensation);
        //最新一个周期集群每日价格
        BigDecimal baseClusterPricePerDay =
                computeBaseClusterPricePerDay(clusterId, priceDetails, computeCompensation, cliCompensation, vncCompensation);
        log.info("集群每日价格{}", baseClusterPricePerDay);

        //总补偿天数
        long totalCompensationDays = 0L;
        if (totalCompesationAmount != null && NumberUtil.isGreater(baseClusterPricePerDay, BigDecimal.ZERO)) {
            totalCompensationDays = NumberUtil.div(totalCompesationAmount, baseClusterPricePerDay).longValue();
        }
        totalCompensation.setCompensationDays(totalCompensationDays);
        //涉及到的合同
        List<HpcBizContractDTO> totalModifyContractList =
                hpcBizContractDTOS.stream().filter(HpcBizContractDTO::isModifyNodeFlag).collect(Collectors.toList());
        totalCompensation.setBizContractList(totalModifyContractList);
        return totalCompensation;
    }

    /**
     * 计算总的补偿金额
     *
     * @param compensations
     */
    private BigDecimal computeTotalCompensationAmount(HpcDrpDegradeCompensation... compensations) {
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (HpcDrpDegradeCompensation compensation : compensations) {
            totalAmount = NumberUtil.add(totalAmount, compensation.getCompensationAmount());
        }
        return totalAmount;
    }


    /**
     * 最新一个周期集群每日价格
     *
     * @param clusterId
     * @param priceDetails                  集群申请单和续订订单
     * @param hpcdrpdegradecompensationList 补偿数据
     * @return
     */
    private BigDecimal computeBaseClusterPricePerDay(Long clusterId, List<ServiceOrderPriceDetail> priceDetails,
                                                     HpcDrpDegradeCompensation... hpcdrpdegradecompensationList) {
        BigDecimal basePrice = BigDecimal.ZERO;
        if (CollectionUtil.isNotEmpty(priceDetails)) {
            //查询原始的节点剩余数
            List<NodeInfo> remainNodeList = new ArrayList<>();
            List<ResVmNodeInfo> resVmNodeInfoList = resVmRemoteService.getNodeInfoListByClusterId(clusterId);
            remainNodeList.addAll(resVmNodeInfoList);
            List<ResBmsNodeInfo> resBmsNodeInfoList = resBmsRemoteService.getNodeInfoListByClusterId(clusterId);
            remainNodeList.addAll(resBmsNodeInfoList);


            List<NodeInfo> baseNodeList = new ArrayList<>();
            //合同剩余的节点用于计算剩余原始的节点数
            List<NodeInfo> contractNodeList = new ArrayList<>();
            Arrays.stream(hpcdrpdegradecompensationList).forEach(hpcDrpDegradeCompensation -> {
                baseNodeList.addAll(hpcDrpDegradeCompensation.getDegradeBaseNodeInfoList());
                contractNodeList.addAll(hpcDrpDegradeCompensation.getAllContractNodeList());
            });

            long days = 0;
            String orderSn = null;
            log.info("当前节点{},缩容的原始节点{}", remainNodeList, baseNodeList);

            for (ServiceOrderPriceDetail priceDetail : priceDetails) {
                if (orderSn != null && !io.seata.common.util.StringUtils.equals(priceDetail.getOrderSn(), orderSn)) {
                    break;
                }
                //排除补扣的金额
                if (OrderType.EXPIRED.equals(priceDetail.getType())) {
                    continue;
                }
                log.info("订单详情：{}", priceDetail);
                //获取被缩容的原始节点数
                long degradeNodeNum = baseNodeList.stream()
                        .filter(node -> io.seata.common.util.StringUtils.equalsIgnoreCase(node.getResourceType(), priceDetail.getProductCode())
                                && io.seata.common.util.StringUtils.equalsIgnoreCase(node.getTypeName(), priceDetail.getServiceType())).count();
                //合同剩余的节点数
                long contractNodeNum = contractNodeList.stream()
                        .filter(node -> io.seata.common.util.StringUtils.equalsIgnoreCase(node.getResourceType(), priceDetail.getProductCode())
                                && io.seata.common.util.StringUtils.equalsIgnoreCase(node.getTypeName(), priceDetail.getServiceType())).count();
                //实际剩余的节点数
                long remainQuantity = remainNodeList.stream()
                        .filter(node -> io.seata.common.util.StringUtils.equalsIgnoreCase(node.getResourceType(), priceDetail.getProductCode())
                                && io.seata.common.util.StringUtils.equalsIgnoreCase(node.getTypeName(), priceDetail.getServiceType())).count() -
                        contractNodeNum;

                Integer quantity = priceDetail.getQuantity();
                if (ProductCodeEnum.HPC_DRP.getProductCode().equals(priceDetail.getProductCode())
                        || ProductCodeEnum.SFS2.getProductCode().equals(priceDetail.getProductCode())
                        || quantity <= remainQuantity) {
                    remainQuantity = quantity;
                }
                log.info("remainQuantity：{}", remainQuantity);
                log.info("degradeNodeNum：{}", degradeNodeNum);
                orderSn = priceDetail.getOrderSn();
                Date startTime = priceDetail.getStartTime();
                Date endTime = priceDetail.getEndTime();
                if (days == 0) {
                    days = cn.hutool.core.date.DateUtil.betweenDay(startTime, endTime, true);
                }
                log.info("days：{}", days);
                //每日价格
                if (quantity > 0) {
                    basePrice = NumberUtil.add(basePrice,
                        NumberUtil.div(NumberUtil.mul(priceDetail.getOriginalCost(), remainQuantity - degradeNodeNum), quantity));
                }
                log.info("basePrice：{}", basePrice);
            }
            basePrice = NumberUtil.div(basePrice, days);
        }
        return basePrice;
    }


    /**
     * @param serviceOrder
     * @param priceDetails
     * @param nodeInfoList
     * @param type         0 计算节点，1登录节点，2，可视化节点
     */
    private HpcDrpDegradeCompensation computeCompensation(ServiceOrder serviceOrder,
                                                          List<ServiceOrderPriceDetail> priceDetails,
                                                          List<NodeInfo> nodeInfoList, List<HpcBizContractDTO> hpcBizContractDTOS, int type) {
        Long clusterId = serviceOrder.getClusterId();
        Date current = serviceOrder.getPayTime();
        HpcDrpDegradeCompensation hpcDrpDegradeCompensation = new HpcDrpDegradeCompensation();
        int nodeNum = nodeInfoList.size();
        hpcDrpDegradeCompensation.setNodeType(type);
        //type 0 计算节点 1登录节点 2可视化节点 3其他管理节点
        BigDecimal compensationAmount = hpcDrpDegradeCompensation.getCompensationAmount();
        if (nodeNum == 0) {
            return hpcDrpDegradeCompensation;
        }
        //执行中的合同

        hpcBizContractDTOS = hpcBizContractDTOS.stream().filter(dto -> !dto.isZeroNode()).collect(Collectors.toList());
        if (type < 3) {
            //总的剩余合同节点数
            int allContractNodeNum = this.getAllContractNode(hpcBizContractDTOS, type);
            //缩容节点数小于等于合同节点数
            hpcDrpDegradeCompensation.addContractNodeList(allContractNodeNum, CollectionUtil.getFirst(nodeInfoList));
            if (allContractNodeNum >= nodeNum) {
                for (NodeInfo nodeInfo : nodeInfoList) {
                    //合同补偿金额
                    BigDecimal contractAmount = computeBizContractAmount(nodeInfo, hpcBizContractDTOS, current, type);
                    if (contractAmount == null) {
                        continue;
                    }
                    compensationAmount = NumberUtil.add(compensationAmount, contractAmount);
                    nodeInfo.setEndTime(current);
                }
                hpcDrpDegradeCompensation.setCompensationAmount(compensationAmount);
            } else {
                // 缩容 涉及原始节点计算原始节点补偿天数
                for (NodeInfo nodeInfo : nodeInfoList) {
                    if (allContractNodeNum > 0) {
                        BigDecimal contractAmount = computeBizContractAmount(nodeInfo, hpcBizContractDTOS, current, type);
                        if (contractAmount == null) {
                            contractAmount = BigDecimal.ZERO;
                        }
                        allContractNodeNum--;
                        compensationAmount = NumberUtil.add(compensationAmount, contractAmount);
                    } else {
                        //基础节点补偿金额
                        BigDecimal baseCompensationAmount = computeBaseCompensation(nodeInfo, priceDetails, current, clusterId);
                        if (baseCompensationAmount == null) {
                            baseCompensationAmount = BigDecimal.ZERO;
                        }
                        compensationAmount = NumberUtil.add(compensationAmount, baseCompensationAmount);
                        hpcDrpDegradeCompensation.getDegradeBaseNodeInfoList().add(nodeInfo);
                    }
                    //基础节点补偿金额
                    nodeInfo.setEndTime(current);

                }
                hpcDrpDegradeCompensation.setCompensationAmount(compensationAmount);
            }
        } else {
            //缩容 原始节点计算原始节点补偿金额
            for (NodeInfo nodeInfo : nodeInfoList) {
                BigDecimal baseCompensationAmount = computeBaseCompensation(nodeInfo, priceDetails, current, clusterId);
                compensationAmount = NumberUtil.add(compensationAmount, baseCompensationAmount);
            }
            hpcDrpDegradeCompensation.setCompensationAmount(compensationAmount);
        }
        return hpcDrpDegradeCompensation;
    }

    /**
     * 计算合同补偿
     *
     * @param nodeInfo
     * @param hpcBizContractDTOS
     * @param current
     * @param type
     */
    private BigDecimal computeBizContractAmount(NodeInfo nodeInfo, List<HpcBizContractDTO> hpcBizContractDTOS, Date current, int type) {
        BigDecimal totalCompensationAmount = null;
        //扩容合同ID
        Long openContractId = null;

        if (CollectionUtil.isNotEmpty(hpcBizContractDTOS)) {
            for (HpcBizContractDTO hpcBizContractDTO : hpcBizContractDTOS) {
                //如果存在扩容合同,就不取其他合同了
                if (openContractId != null) {
                    if (!openContractId.equals(hpcBizContractDTO.getHpcOpenContractId())) {
                        continue;
                    }
                }
                //计算补偿金额
                BigDecimal compensationAmount = hpcBizContractDTO.computeCompensation(current, type);
                if (compensationAmount == null) {
                    continue;
                }
                openContractId = hpcBizContractDTO.getHpcOpenContractId();

                if (totalCompensationAmount == null) {
                    totalCompensationAmount = compensationAmount;
                } else {
                    totalCompensationAmount = NumberUtil.add(totalCompensationAmount, compensationAmount);
                }
                //缩减节点数
                hpcBizContractDTO.addNodeRenewNum(type);
            }
            if (openContractId != null) {
                BizContract bizContract = bizContractService.selectByPrimaryKey(openContractId);
                if (bizContract != null) {
                    nodeInfo.setStarTime(bizContract.getStartTime());
                }
            }
        }
        return totalCompensationAmount;
    }

    /**
     * 原始节点补偿金额
     *
     * @param nodeInfo
     * @param priceDetails
     * @param current
     * @param clusterId
     */
    private BigDecimal computeBaseCompensation(NodeInfo nodeInfo, List<ServiceOrderPriceDetail> priceDetails,
                                               Date current, Long clusterId) {
        BigDecimal basePrice = BigDecimal.ZERO;
        Criteria criteria = new Criteria();
        criteria.put("product_type", ProductCodeEnum.HPC_DRP.getProductCode());
        criteria.put("cluster_id", clusterId);
        List<SfProductResource> sfProductResources = sfProductResourceMapper.selectByParams(criteria);
        SfProductResource sfProductResource = CollectionUtil.getFirst(sfProductResources);

        if (CollectionUtil.isNotEmpty(priceDetails)) {
            //获取当前集群的剩余的补偿天数
            Integer remainCompensationDays = getRemainCompensationDays(sfProductResource, current);
            Date calResourceStarTime = current;
            if (remainCompensationDays > 0) {
                calResourceStarTime = org.apache.commons.lang.time.DateUtils.addDays(current, -remainCompensationDays);
            }
            for (ServiceOrderPriceDetail priceDetail : priceDetails) {
                Date startTime = priceDetail.getStartTime();
                Date endTime = priceDetail.getEndTime();
                if (io.seata.common.util.StringUtils.equals(priceDetail.getServiceType(), nodeInfo.getTypeName()) &&
                        io.seata.common.util.StringUtils.equals(priceDetail.getProductCode(), nodeInfo.getResourceType())) {
                    //未超期返回节点总价格
                    BigDecimal oneNodeAmount = NumberUtil.div(priceDetail.getAmount(), priceDetail.getQuantity());
                    if (calResourceStarTime.before(startTime)) {
                        basePrice = NumberUtil.add(oneNodeAmount, basePrice);
                    } else if (calResourceStarTime.before(endTime) && !calResourceStarTime.before(startTime)) {
                        //节点总价格*剩余天数/总天数
                        BigDecimal amountPerDay =
                                NumberUtil.div(NumberUtil.mul(oneNodeAmount, DateUtil.calculateOffDay(calResourceStarTime, endTime, false)),
                                        DateUtil.calculateOffDay(startTime, endTime, true));
                        basePrice = NumberUtil.add(amountPerDay, basePrice);
                    }
                }
                //如果是申请单设置节点开始时间
                if (OrderType.APPLY.equals(priceDetail.getType())) {
                    nodeInfo.setStarTime(startTime);
                }
            }
        }
        if (nodeInfo.getStarTime() == null) {
            nodeInfo.setStarTime(sfProductResource.getStartTime());
        }
        return basePrice;
    }

    /**
     * 获取当前集群实例的剩余补偿天数
     *
     * @param current
     * @param sfProductResource
     * @return
     */
    private Integer getRemainCompensationDays(SfProductResource sfProductResource, Date current) {
        Integer remainCompensationDays = 0;
        Criteria criteria = new Criteria();
        criteria.put("sfProductResourceId", sfProductResource.getId());
        criteria.put("gtEndtime", current);
        List<SfProductResourceCompensation> resourceCompensations = sfProductResourceCompensationMapper.selectByCriteria(criteria);
        if (CollectionUtil.isNotEmpty(resourceCompensations)) {
            for (SfProductResourceCompensation resourceCompensation : resourceCompensations) {
                remainCompensationDays += resourceCompensation.getCompensationDays();
            }
        }
        return remainCompensationDays;
    }

    /**
     * HPC专属资源池缩容失败处理
     *
     * @param clusterId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleHpcDrpDegradeFail(Long clusterId) {

        Date current = new Date();

        HPCClusterInfoIDResult currentHpcClusterInfo = hpcRemoteService.getHPCClusterInfoById(clusterId);
        List<HPCManagementNodeInfoVO> managementNodeInfoList = currentHpcClusterInfo.getHPCClusterInfo().getManagementNodeInfo();
        List<HPCComputeNodeInfoVO> computeNodeInfoList = currentHpcClusterInfo.getHPCClusterInfo().getComputeNodeInfo();
        List<HPCVNCNodeInfoVO> vncNodeInfoList = currentHpcClusterInfo.getHPCClusterInfo().getVncNodeInfo();
        Map<String, HPCManagementNodeInfoVO> managementNodeInfoVOMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(managementNodeInfoList)) {
            managementNodeInfoVOMap = managementNodeInfoList.stream().collect(Collectors.toMap(HPCManagementNodeInfoVO::getResourceID, node -> node));
        }
        Map<String, HPCComputeNodeInfoVO> computeNodeInfoVOMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(computeNodeInfoList)) {
            computeNodeInfoVOMap =
                    computeNodeInfoList.stream().collect(Collectors.toMap(HPCComputeNodeInfoVO::getResourceID, node -> node));
        }
        Map<String, HPCVNCNodeInfoVO> hpcVNCNodeInfoVOMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(vncNodeInfoList)) {
            hpcVNCNodeInfoVOMap = vncNodeInfoList.stream().collect(Collectors.toMap(HPCVNCNodeInfoVO::getResourceID, node -> node));
        }

        //删除失败的节点数组
        List<NodeInfo> failDelteNodeList = new ArrayList<>();
        //待删除的管理节点
        List<NodeInfo> manageNodeList = new ArrayList<>();
        List<ResVmNodeInfo> resVmNodeInfoList = resVmRemoteService.getNodeInfoListByClusterId(clusterId);
        if (CollectionUtil.isNotEmpty(resVmNodeInfoList)) {
            for (ResVmNodeInfo node : resVmNodeInfoList) {
                if (StringUtils.equalsIgnoreCase(ResHpcClusterNodeStatus.REMOVING, node.getStatus())) {
                    String instanceId = node.getInstanceId();
                    if (managementNodeInfoVOMap.containsKey(instanceId) || hpcVNCNodeInfoVOMap.containsKey(instanceId)) {
                        HPCManagementNodeInfoVO hpcManagementNodeInfoVO = managementNodeInfoVOMap.get(instanceId);
                        if (hpcManagementNodeInfoVO != null) {
                            node.setStatus(hpcManagementNodeInfoVO.getNodeStatus());
                        }
                        HPCVNCNodeInfoVO hpcvncNodeInfoVO = hpcVNCNodeInfoVOMap.get(instanceId);
                        if (hpcvncNodeInfoVO != null) {
                            node.setStatus(hpcvncNodeInfoVO.getNodeStatus());
                        }
                        failDelteNodeList.add(node);
                    } else {
                        manageNodeList.add(node);
                    }
                }
            }
        }
        //待删除的计算节点
        List<NodeInfo> computeNodeList = new ArrayList<>();
        List<ResBmsNodeInfo> resBmsNodeList = resBmsRemoteService.getNodeInfoListByClusterId(clusterId);
        if (CollectionUtil.isNotEmpty(resBmsNodeList)) {
            for (ResBmsNodeInfo node : resBmsNodeList) {
                if (StringUtils.equalsIgnoreCase(ResHpcClusterNodeStatus.REMOVING, node.getStatus())) {
                    String instanceId = node.getInstanceId();
                    if (computeNodeInfoVOMap.containsKey(instanceId)) {
                        HPCComputeNodeInfoVO hpcComputeNodeInfoVO = computeNodeInfoVOMap.get(instanceId);
                        if (hpcComputeNodeInfoVO != null) {
                            node.setStatus(hpcComputeNodeInfoVO.getNodeStatus());
                        }
                        failDelteNodeList.add(node);
                    } else {
                        computeNodeList.add(node);
                    }
                }
            }
        }

        //更新删除失败的节点状态
        this.changeFailDeleteNodeStatus(failDelteNodeList, clusterId);

        Criteria criteria = new Criteria();
        criteria.put("cluster_id", clusterId);
        criteria.put("product_type", ProductCodeEnum.HPC_DRP.getProductCode());
        List<SfProductResource> sfProductResources = sfProductResourceMapper.selectByParams(criteria);
        SfProductResource sfProductResource = CollectionUtil.getFirst(sfProductResources);
        Long sfProductResourceId = sfProductResource.getId();

        //获取缩容订单
        ServiceOrder degradeServiceOrder = getDegradeServiceOrder(clusterId);


        if (CollectionUtil.isNotEmpty(computeNodeList) || CollectionUtil.isNotEmpty(manageNodeList)) {
            //修改缩容订单详情
            this.resetOrderDetail(degradeServiceOrder, failDelteNodeList);

            //获取所有申请，续订的基础节点的价格详情
            List<ServiceOrderPriceDetail> priceDetails = getApplyAndRenewOrderPriceDetails(current, sfProductResourceId);
            //计算出补偿
            HpcDrpDegradeCompensation totalCompesation =
                    this.totalCompensation(degradeServiceOrder, computeNodeList, manageNodeList, priceDetails);

            //更新集群结束时间
            long compensationDays = totalCompesation.getCompensationDays();
            this.changeResourceEndTime(clusterId, current, sfProductResource, compensationDays);

            //更新合同明细缩容数据
            List<HpcBizContractDTO> contractList = totalCompesation.getBizContractList();
            List<BizContractDetail> detailList = contractList.stream().map(HpcBizContractDTO::getContractDetail).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(detailList)) {
                for (BizContractDetail bizContractDetail : detailList) {
                    bizContractDetailMapper.updateByPrimaryKeySelective(bizContractDetail);
                }

            }
            //更新未使用的合同明细节点数
            bizContractDetailService.updateUnusedDetail(contractList);

            List<Long> bizContractIdList = detailList.stream().map(BizContractDetail::getContractSid).collect(Collectors.toList());
            String bizContractIdStr = bizContractIdList.toString();

            degradeServiceOrder.setCompensationContractId(bizContractIdStr);
            degradeServiceOrder.setCompensationDays(compensationDays);
            degradeServiceOrder.setStatus(OrderStatus.COMPLETED);
            serviceOrderMapper.updateByPrimaryKeySelective(degradeServiceOrder);

            hpcRemoteService.clearRemovingNodeAndRecord(clusterId);
            //发邮件，站内信等
            HPCDrpDeleteNodeSendMessageDTO hpcDrpDeleteNodeSendMessageDTO = new HPCDrpDeleteNodeSendMessageDTO();
            hpcDrpDeleteNodeSendMessageDTO.setClusterId(clusterId);
            hpcDrpDeleteNodeSendMessageDTO.setMessageTemplate(
                    NotificationConsts.ConsoleMsg.ProductMsg.TENANT_HPC_PRIVATE_SCALE_DOWN);
            hpcDrpDeleteNodeSendMessageDTO.setStatus(SUCCESS_MESSAGE);
            hpcDrpDeleteNodeSendMessageDTO.setManageList(manageNodeList.size());
            hpcDrpDeleteNodeSendMessageDTO.setVncNodeList(manageNodeList);
            hpcDrpDeleteNodeSendMessageDTO.setCliNodeList(manageNodeList);
            hpcDrpDeleteNodeSendMessageDTO.setComputeList(computeNodeList.size());
            hpcDrpDeleteNodeSendMessageDTO.setCompensateDays(compensationDays);
            hpcDrpDeleteNodeSendMessageDTO.setEndTime(sfProductResource.getEndTime());
            hpcDrpDeleteNodeSendMessageDTO.setFailDelteNodeList(failDelteNodeList);
            hpcDrpDeleteNodeSendMessageDTO.setOrderSn(degradeServiceOrder.getOrderSn());
            hpcDrpDeleteNodeSendMessage(hpcDrpDeleteNodeSendMessageDTO);

        } else {
            degradeServiceOrder.setCompensationDays(0L);
            degradeServiceOrder.setStatus(OrderStatus.DEGRADE_ERROR);
            degradeServiceOrder.setStepName("缩容失败");
            serviceOrderMapper.updateByPrimaryKeySelective(degradeServiceOrder);

            resHpcClusterDeleteNodeTaskRemoteService.deleteByClusterId(clusterId);

            //发送邮件，站内信等
            HPCDrpDeleteNodeSendMessageDTO hpcDrpDeleteNodeSendMessageDTO = new HPCDrpDeleteNodeSendMessageDTO();
            hpcDrpDeleteNodeSendMessageDTO.setMessageTemplate(
                    NotificationConsts.ConsoleMsg.ProductMsg.TENANT_HPC_PRIVATE_SCALE_DOWN);
            hpcDrpDeleteNodeSendMessageDTO.setClusterId(clusterId);
            hpcDrpDeleteNodeSendMessageDTO.setStatus(FAIL_MESSAGE);
            hpcDrpDeleteNodeSendMessageDTO.setOrderSn(degradeServiceOrder.getOrderSn());
            hpcDrpDeleteNodeSendMessageDTO.setFailDelteNodeList(failDelteNodeList);
            hpcDrpDeleteNodeSendMessage(hpcDrpDeleteNodeSendMessageDTO);
        }
        //刷新登录ip
        this.refreshCli(clusterId, managementNodeInfoList);
        this.syncRes(clusterId);
    }

    /**
     * 从新设置订单详情
     *
     * @param degradeServiceOrder
     * @param failDeleteNodeList
     */
    private void resetOrderDetail(ServiceOrder degradeServiceOrder, List<NodeInfo> failDeleteNodeList) {
        if (CollectionUtil.isEmpty(failDeleteNodeList)) {
            return;
        }
        Criteria criteria = new Criteria();
        criteria.put("orderId", degradeServiceOrder.getId());
        List<ServiceOrderDetail> serviceOrderDetails = serviceOrderDetailMapper.selectByParams(criteria);
        Map<Long, ServiceOrderDetail> detailMap = serviceOrderDetails.stream().collect(Collectors.toMap(ServiceOrderDetail::getId, detail -> detail));

        Criteria priceDetailCri = new Criteria();
        priceDetailCri.put("orderSn", degradeServiceOrder.getOrderSn());
        List<ServiceOrderPriceDetail> priceDetails = serviceOrderPriceDetailMapper.selectByParams(priceDetailCri);
        //节点类型数统计
        Map<String, Long> nodeNumMap = failDeleteNodeList.stream()
                .collect(Collectors.groupingBy(nodeInfo -> nodeInfo.getResourceType() + nodeInfo.getTypeName(), Collectors.counting()));
        priceDetails.stream().forEach(priceDetail -> {
            String specKey = priceDetail.getProductCode() + priceDetail.getServiceType();
            //待删除的数量
            Long subQuantity = nodeNumMap.get(specKey);
            if (subQuantity != null && subQuantity > 0) {
                long remainQuantity = priceDetail.getQuantity() - subQuantity;
                Integer quantity = 0;
                if (remainQuantity <= 0L) {
                    nodeNumMap.put(specKey, subQuantity - priceDetail.getQuantity());
                } else {
                    quantity = (int) remainQuantity;
                }
                priceDetail.setQuantity(quantity);

                ServiceOrderDetail serviceOrderDetail = detailMap.get(priceDetail.getOrderDetailId());
                if (serviceOrderDetail != null) {
                    serviceOrderDetail.setQuantity(quantity);
                    //更新订单明细
                    serviceOrderDetailMapper.updateByPrimaryKeySelective(serviceOrderDetail);
                }
                serviceOrderPriceDetailMapper.updateByPrimaryKeySelective(priceDetail);
            }
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleHpcDrpDegradeSuccessByAdmin(Long clusterId) {

        Date current = new Date();
        //获取待删除的节点信息
        List<NodeInfo> manageList = new ArrayList<>();
        List<ResVmNodeInfo> resVmNodeInfoList = resVmRemoteService.getNodeInfoListByClusterId(clusterId).stream()
                .filter(node -> StringUtils.equalsIgnoreCase(ResHpcClusterNodeStatus.REMOVING, node.getStatus())).collect(Collectors.toList());
        manageList.addAll(resVmNodeInfoList);
        List<NodeInfo> computeList = new ArrayList<>();
        List<ResBmsNodeInfo> bmsNodeList = resBmsRemoteService.getNodeInfoListByClusterId(clusterId).stream()
                .filter(node -> StringUtils.equalsIgnoreCase(ResHpcClusterNodeStatus.REMOVING, node.getStatus())).collect(Collectors.toList());
        computeList.addAll(bmsNodeList);

        //处理过期合同
        this.handlingExpiredContracts(clusterId, computeList, manageList);
        hpcRemoteService.clearRemovingNode(clusterId);
        ServiceOrder degradeServiceOrder = getDegradeServiceOrder(clusterId);
        this.syncRes(clusterId);
        //发邮件，站内信等
        HPCDrpDeleteNodeSendMessageDTO hpcDrpDeleteNodeSendMessageDTO = new HPCDrpDeleteNodeSendMessageDTO();
        hpcDrpDeleteNodeSendMessageDTO.setClusterId(clusterId);
        hpcDrpDeleteNodeSendMessageDTO.setMessageTemplate(
                NotificationConsts.ConsoleMsg.ProductMsg.TENANT_HPC_PRIVATE_SCALE_DOWN);
        hpcDrpDeleteNodeSendMessageDTO.setStatus(SUCCESS_MESSAGE);
        hpcDrpDeleteNodeSendMessageDTO.setManageList(manageList.size());
        hpcDrpDeleteNodeSendMessageDTO.setVncNodeList(manageList);
        hpcDrpDeleteNodeSendMessageDTO.setCliNodeList(manageList);
        hpcDrpDeleteNodeSendMessageDTO.setComputeList(computeList.size());
        hpcDrpDeleteNodeSendMessageDTO.setCompensateDays(0);
        if (degradeServiceOrder != null) {
            hpcDrpDeleteNodeSendMessageDTO.setOrderSn(degradeServiceOrder.getOrderSn());
        } else {
            hpcDrpDeleteNodeSendMessageDTO.setOrderSn("");
        }
        hpcDrpDeleteNodeSendMessage(hpcDrpDeleteNodeSendMessageDTO);
    }


    private void hpcDrpDeleteNodeSendMessage(HPCDrpDeleteNodeSendMessageDTO hpcDrpDeleteNodeSendMessageDTO) {
        log.info("HPC专属资源池节点删除-ServiceOrderRemoteServiceImpl-hpcDrpDeleteNodeSendMessage-INPUT:[{}]",
                JSON.toJSONString(hpcDrpDeleteNodeSendMessageDTO));
        try {
            Long clusterId = hpcDrpDeleteNodeSendMessageDTO.getClusterId();
            String messageTemplate = hpcDrpDeleteNodeSendMessageDTO.getMessageTemplate();
            ResHpcClusterRemoteModule resHpcClusterRemoteModule = hpcRemoteService.selectByPrimaryKey(clusterId);
            Criteria criteria = new Criteria();
            criteria.put("clusterUuid", resHpcClusterRemoteModule.getResourceId());
            criteria.put("clusterId", clusterId);
            List<ServiceOrder> serviceOrders = serviceOrderMapper.selectByAllParams(criteria);
            ServiceOrder serviceOrder = serviceOrders.get(0);
            Long entityId = serviceOrder.getEntityId();

            Long orgSid = resHpcClusterRemoteModule.getOrgSid();
            Org org = orgService.selectByPrimaryKey(orgSid);
            User owner = userMapper.selectByPrimaryKey(resHpcClusterRemoteModule.getOwnerId());

            Map messageContent = new HashMap();
            messageContent.put("userAccount", owner.getAccount());
            messageContent.put("clusterUuid", resHpcClusterRemoteModule.getResourceId());
            messageContent.put("status", hpcDrpDeleteNodeSendMessageDTO.getStatus());
            messageContent.put("productName", serviceOrder.getProductName());
            messageContent.put("poolName", resHpcClusterRemoteModule.getName());
            messageContent.put("orderSn", hpcDrpDeleteNodeSendMessageDTO.getOrderSn());
            messageContent.put("managerList", hpcDrpDeleteNodeSendMessageDTO.getManageList());
            messageContent.put("cliNodeList", hpcDrpDeleteNodeSendMessageDTO.getCliNodeList());
            messageContent.put("vncNodeList", hpcDrpDeleteNodeSendMessageDTO.getVncNodeList());
            messageContent.put("computeList", hpcDrpDeleteNodeSendMessageDTO.getComputeList());
            messageContent.put("compensateDays", hpcDrpDeleteNodeSendMessageDTO.getCompensateDays());
            messageContent.put("failDelteNodeList", hpcDrpDeleteNodeSendMessageDTO.getFailDelteNodeList());
            if (hpcDrpDeleteNodeSendMessageDTO.getEndTime() != null) {
                messageContent.put("endTime", DateFormatUtils.format(hpcDrpDeleteNodeSendMessageDTO.getEndTime(), DateUtil.COMMON_DATE_PATTERN));
            } else {
                messageContent.put("endTime", "");
            }
            messageContent.put("applyUser", "");
            messageContent.put("clusterUuid", resHpcClusterRemoteModule.getResourceId());
            log.debug("发送信息,{}", messageContent);
            BaseNotificationMqBean baseNotificationMqBeanToUser = new BaseNotificationMqBean();
            LinkedHashSet<Long> userIdSet = new LinkedHashSet<>();
            userIdSet.add(owner.getUserSid());
            baseNotificationMqBeanToUser.setFreeMarker(true);
            baseNotificationMqBeanToUser.setToUserIds(userIdSet);
            baseNotificationMqBeanToUser.setMsgId(hpcDrpDeleteNodeSendMessageDTO.getMessageTemplate());
            baseNotificationMqBeanToUser.setMap(messageContent);
            baseNotificationMqBeanToUser.setFreeMarker(true);
            baseNotificationMqBeanToUser.setEntityId(entityId);
            rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT, baseNotificationMqBeanToUser);

            messageContent.put("applyUser", org.getOrgName());
            //给运维人员发送邮件
            if (FAIL_MESSAGE.equals(hpcDrpDeleteNodeSendMessageDTO.getStatus())) {
                messageContent.put("userAccount", "管理员");
                MailNotificationMq mailNotificationMq = new MailNotificationMq();
                mailNotificationMq.setMsgId(NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_HPC_PRIVATE_SCALE_DOWN);
                mailNotificationMq.setMails(sysConfigRemoteService.getSendMailUrl());
                mailNotificationMq.setMap(messageContent);
                rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT, mailNotificationMq);
            }

            BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
            baseNotificationMqBean.setEntityId(entityId);
            baseNotificationMqBean.setMsgId(NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_HPC_PRIVATE_SCALE_DOWN);
            baseNotificationMqBean.setMap(messageContent);
            baseNotificationMqBean.setFreeMarker(true);
            List<User> adminstrators = userMapper.findAdminstratorsByEntityId(entityId);
            Set<Long> ids = adminstrators.stream().map(it -> it.getUserSid()).collect(Collectors.toSet());
            // 发送站内信给运营管理员
            baseNotificationMqBean.getImsgUserIds().addAll(ids);
            rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT, baseNotificationMqBean);

        } catch (Exception e) {
            log.error("HPC专属资源池节点删除-ServiceOrderRemoteServiceImpl-hpcDrpDeleteNodeSendMessage-失败：[{}]",
                    e.getMessage());
        }

    }

    /**
     * 处理过期合同
     *
     * @param clusterId
     * @param computeNodeList
     * @param manageNodeList
     */
    private void handlingExpiredContracts(Long clusterId, List<NodeInfo> computeNodeList, List<NodeInfo> manageNodeList) {

        Criteria criteria = new Criteria();
        criteria.put("orderTypeIn", Arrays.asList(OrderType.UPGRADE, OrderType.UPGRADE_RENEW));
        criteria.put("status", OrderStatus.COMPLETED);
        criteria.put("clusterId", clusterId);
        List<ServiceOrder> serviceOrderList = serviceOrderMapper.selectByParams(criteria);
        if (CollectionUtil.isNotEmpty(serviceOrderList)) {
            //查询集群扩容相关的合同
            List<Long> contractIdList = serviceOrderList.stream().filter(order -> {

                Criteria orderDetailCri = new Criteria();
                orderDetailCri.put("orderId", order.getId());
                orderDetailCri.put("serviceType", ProductCodeEnum.HPC_DRP.getProductCode());
                int countByParams = serviceOrderDetailMapper.countByParams(orderDetailCri);
                return countByParams > 0 && Objects.nonNull(order.getContractId());
            }).map(order -> Long.valueOf(order.getContractId())).collect(Collectors.toList());

            //查询过期合同
            List<HpcBizContractDTO> hpcBizContractList =
                    bizContractService.queryHpcContractTreeByIdList(contractIdList, Arrays.asList(ContractStatus.EXPIRED));
            hpcBizContractList =
                    hpcBizContractList.stream().filter(hpcBizContractDTO -> !hpcBizContractDTO.isZeroNode()).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(hpcBizContractList)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_561800932));
            }

            //修改合同节点数
            if (CollectionUtil.isNotEmpty(computeNodeList)) {
                changeContractNodeNum(computeNodeList, hpcBizContractList, 0);
            }
            List<NodeInfo> cliNodeList = manageNodeList.stream()
                    .filter(nodeInfo -> HpcPointType.CCS_CLI.equalsIgnoreCase(nodeInfo.getHpcPointType()))
                    .collect(Collectors.toList());
            //修改合同节点数
            if (CollectionUtil.isNotEmpty(cliNodeList)) {
                changeContractNodeNum(cliNodeList, hpcBizContractList, 1);
            }

            List<NodeInfo> vncNodeList = manageNodeList.stream()
                    .filter(nodeInfo -> HpcPointType.VNC.equalsIgnoreCase(nodeInfo.getHpcPointType()))
                    .collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(vncNodeList)) {
                changeContractNodeNum(vncNodeList, hpcBizContractList, 2);
            }
            //更新修改的合同
            List<BizContractDetail> bizContractDetailList =
                    hpcBizContractList.stream().filter(dto -> dto.isModifyNodeFlag()).map(HpcBizContractDTO::getContractDetail)
                            .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(bizContractDetailList)) {
                bizContractDetailService.updateBatch(bizContractDetailList);
            }
        }
    }

    /**
     * 修改合同节点数
     *
     * @param nodeInfoList
     * @param hpcBizContractList
     * @param type
     */
    private void changeContractNodeNum(List<NodeInfo> nodeInfoList, List<HpcBizContractDTO> hpcBizContractList, int type) {
        Long openContractId = null;

        int allContractNode = this.getAllContractNode(hpcBizContractList, type);
        if (nodeInfoList.size() > allContractNode) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1375795666));
        }
        for (NodeInfo nodeInfo : nodeInfoList) {
            if (CollectionUtil.isNotEmpty(hpcBizContractList)) {
                for (HpcBizContractDTO hpcBizContractDTO : hpcBizContractList) {
                    Integer remNodeNum = hpcBizContractDTO.getRemNodeNum(type);
                    if (remNodeNum <= 0) {
                        continue;
                    }
                    //
                    if (openContractId != null) {
                        if (!openContractId.equals(hpcBizContractDTO.getHpcOpenContractId())) {
                            continue;
                        }
                    }
                    openContractId = hpcBizContractDTO.getHpcOpenContractId();
                    //缩减节点数
                    hpcBizContractDTO.addNodeRenewNum(type);
                }
            }
        }
    }

    private int getAllContractNode(List<HpcBizContractDTO> hpcBizContractDTOS, int type) {
        int contractNode = 0;
        //统计扩容计算节点数
        List<Long> openBizContractIdList = new ArrayList<>();
        for (HpcBizContractDTO hpcBizContractDTO : hpcBizContractDTOS) {
            Long contractSid = hpcBizContractDTO.getContractSid();
            if (contractSid == null) {
                contractSid = hpcBizContractDTO.getContractId();
            }
            if (openBizContractIdList.contains(contractSid)) {
                continue;
            }
            openBizContractIdList.add(contractSid);
            Integer remNodeNum = hpcBizContractDTO.getRemNodeNum(type);
            contractNode += remNodeNum;
        }
        return contractNode;
    }

    /**
     * 获取fd云环境
     */
    private Long getFDCloudEnv() {
        CloudEnvParams cloudEnvParams = new CloudEnvParams();
        cloudEnvParams.setCloudEnvType(
                cn.com.cloudstar.rightcloud.common.constants.res.type.CloudEnvType.FUSION_DIRECTOR.getValue().get(0));
        List<CloudEnv> cloudEnvs = cloudEnvRemoteService.selectByParams(cloudEnvParams);
        return Objects.nonNull(cloudEnvs) && Objects.nonNull(cloudEnvs.get(0)) ? cloudEnvs.get(0).getId() : 0;
    }

    @Override
    public void upgradeRenewResource(cn.com.cloudstar.rightcloud.remote.api.pojo.system.order.ServiceOrder serviceOrder) {
        ServiceOrder convert = BeanConvertUtil.convert(serviceOrder, ServiceOrder.class);
        List<cn.com.cloudstar.rightcloud.remote.api.pojo.system.order.ServiceOrderDetail> details = serviceOrder.getDetails();
        List<ServiceOrderDetail> convertDetailList = BeanConvertUtil.convert(details, ServiceOrderDetail.class);
        convert.setDetails(convertDetailList);
        serviceOrderService.upgradeRenewResource(convert);
    }

    private MAPoolsListQueryResult maTaskPoolsListQuerys(HashMap<String, String> example) {
        MAPoolsListQuery maPoolsListQuery = CloudClientFactory.buildMQBean(cloudEnvId(), MAPoolsListQuery.class);
        if (StringUtils.isNotBlank(example.get("fieldSelector"))){
            maPoolsListQuery.setFieldSelector(example.get("fieldSelector"));
        }
        if (StringUtils.isNotBlank(example.get("workspaceId"))){
            maPoolsListQuery.setWorkspaceId(example.get("workspaceId"));
        }
        if (StringUtils.isNotBlank(example.get("labelSelector"))){
            maPoolsListQuery.setLabelSelector(example.get("labelSelector"));
        }
        if (StringUtils.isNotBlank(example.get("limit"))){
            maPoolsListQuery.setLimit(Integer.parseInt(example.get("limit")));
        }
        if (StringUtils.isNotBlank(example.get("continueStr"))){
            maPoolsListQuery.setContinueStr(example.get("continueStr"));
        }
        if (StringUtils.isNotBlank(example.get("parent"))){
            maPoolsListQuery.setParent(example.get("parent"));
        }
        log.info("MA租户专属资源池列表查询，请求参数：[]");
        MAPoolsListQueryResult result = new MAPoolsListQueryResult();
        try {
            result = (MAPoolsListQueryResult) MQHelper.rpc(maPoolsListQuery);

            if (!result.isSuccess()){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1993388768));
            }
        } catch (Exception e) {
            log.error("exception message:", e);
        }
        return result;
    }

    private MAPoolsQueryResult maTaskPoolsQuerys(MAPoolsQuery param) {
        MAPoolsQuery maPoolsQuery = CloudClientFactory.buildMQBean(cloudEnvId(), MAPoolsQuery.class);
        maPoolsQuery.setPoolName(param.getPoolName());
        Base clearPass = BaseClearPassUtil.clearPass(maPoolsQuery);
        MAPoolsQueryResult  result =maRemoteService.maPoolsQuery(maPoolsQuery);

        return result;
    }


    /**
     * 环境ID取得
     */
    private Long cloudEnvId() {
        CloudEnvParams cloudEnvParams = new CloudEnvParams();
        cloudEnvParams.setCloudEnvType("HCSO");
        List<CloudEnv> cloudEnvs = cloudEnvRemoteService.selectByParams(cloudEnvParams);
        return Objects.nonNull(cloudEnvs) && Objects.nonNull(cloudEnvs.get(0)) ? cloudEnvs.get(0).getId() : 0;
    }



    @Override
    public void sendApprovalFailMessage(Long clusterId) {
        Criteria criteria = new Criteria();
        criteria.put("clusterId", clusterId);
        criteria.put("productName", "昇腾Modelarts专属资源池");
        criteria.setOrderByClause(" id desc");
        List<ServiceOrder> serviceOrders = serviceOrderMapper.selectByAllParams(criteria);
        if(serviceOrders.size()>0){
            //AI开通失败发送站内信]
            log.info("AI审批失败发送站类型..");
            Map<String, String> content = Maps.newHashMap();
            serviceOrderService.sendErrorMsg(serviceOrders.get(0),content);
            log.info("AI审批失败发送站类型..结束");
        }
    }

    @Override
    public void sendApprovalFailMessageByOrderId(Long orderId) {
        ServiceOrder serviceOrder = serviceOrderMapper.selectByPrimaryKey(orderId);
        if(Objects.nonNull(serviceOrder)){
            //AI开通失败发送站内信]
            log.info("{}Send notification of approval failure..orderId:{}", serviceOrder.getProductCode(), orderId);
            Map<String, String> content = Maps.newHashMap();
            serviceOrderService.sendErrorMsg(serviceOrder,content);
            log.info("Send notification of approval failure..end");
        }
    }

    @Override
    public void approveReleaseDrp(Long clusterId) {
        log.info("MA专属释放资源-DrpReleaseResourceListener-审批");
        drpReleaseResourceListener.approveOrder(clusterId);
    }
    @Override
    public ServiceOrderQueryResult selectByHpcDrpClusterId(Long id) {
        List<ServiceOrder> clusters = serviceOrderMapper.selectByHpcDrpClusterId(id);
        if (CollUtil.isNotEmpty(clusters)) {
            return BeanConvertUtil.convert(clusters.get(0), ServiceOrderQueryResult.class);
        }
        return new ServiceOrderQueryResult();
    }
    @Override
    public void rollbackEcs(Long orderId, List<Long> ids) {
        ServiceOrder order = serviceOrderService.selectByPrimaryKey(orderId);
        ActionParam actionParam = JSON.parseObject(order.getResourceInfo(), ActionParam.class);
        actionParam.setResIds(ids);
        RestResult<Boolean> rollback = resourceDcFeignService.vmInsRollback(actionParam);
        if (rollback.getStatus()) {
            ServiceOrder serviceOrder = new ServiceOrder();
            serviceOrder.setId(orderId);
            serviceOrder.setStatus(OrderStatus.FAILURE);
            serviceOrderService.updateByPrimaryKeySelective(serviceOrder);
            sendApprovalFailMessageByOrderId(orderId);
        }
    }

    @Override
    public void rollbackBms(Long orderId, List<Long> ids, boolean isCreate) {
        ServiceOrder order = serviceOrderService.selectByPrimaryKey(orderId);
        if (isCreate) {
            ActionParam actionParam = JSON.parseObject(order.getResourceInfo(), ActionParam.class);
            actionParam.setResIds(ids);
            RestResult<Boolean> rollback = resourceDcFeignService.rollbackBms(actionParam);
            if (rollback.getStatus()) {
                ServiceOrder serviceOrder = new ServiceOrder();
                serviceOrder.setId(orderId);
                serviceOrder.setStatus(OrderStatus.FAILURE);
                serviceOrderService.updateByPrimaryKeySelective(serviceOrder);
                sendApprovalFailMessageByOrderId(orderId);
            }
        }else {
            ServiceOrder serviceOrder = new ServiceOrder();
            serviceOrder.setId(orderId);
            serviceOrder.setStatus(OrderStatus.FAILURE);
            serviceOrderService.updateByPrimaryKeySelective(serviceOrder);
            sendApprovalFailMessageByOrderId(orderId);
        }
    }

    @Override
    public void rollbackProcessCommon(Long orderId, String backStatus) {
        ServiceOrder serviceOrder = new ServiceOrder();
        serviceOrder.setId(orderId);
        serviceOrder.setStatus(backStatus);
        serviceOrderService.updateByPrimaryKeySelective(serviceOrder);
        sendApprovalFailMessageByOrderId(orderId);
    }


    @Override
    public void rejectApproveServiceOrder(Long clusterId, String key) {
        Criteria criteria = new Criteria();
        criteria.put("clusterId", clusterId);
        criteria.put("serviceTypeInDetail", Collections.singletonList(ProductCodeEnum.HPC_OFFLINE.getProductCode()));
        criteria.setOrderByClause(" id desc");
        List<ServiceOrder> serviceOrders = serviceOrderMapper.selectByAllParams(criteria);
        ResHpcClusterRemoteModule resHpcClusterRemoteModule = hpcRemoteService.selectByPrimaryKey(clusterId);
        String currKey = resHpcClusterRemoteModule.getProessPhase() + resHpcClusterRemoteModule.getProessStatus();

        if (CollectionUtil.isEmpty(serviceOrders) || Objects.isNull(serviceOrders.get(0))) {
            throw new BizException("订单异常，请检查！");
        }


        // 从缓存获取审批意见
        String approveAdvice = JedisUtil.instance().get(serviceOrders.get(0).getRedisKeyHPCApproveRemark());
        if (StringUtils.isEmpty(approveAdvice)) {
            approveAdvice = "HPC共享资源池拒绝并关闭";
        }

        // entity获取
        BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectByPrimaryKey(serviceOrders.get(0).getBizBillingAccountId());

        //执行方法的用户
        AuthUser admin = setAuthUser(serviceOrders.get(0).getUpdatedBy(), bizBillingAccount.getEntityId());

        ApproveOrderDTO approveOrderDTO = new ApproveOrderDTO();
        approveOrderDTO.setApproveAdvice(approveAdvice);
        approveOrderDTO.setApproveType("03");
        approveOrderDTO.setUserSid(admin.getUserSid() + "");
        approveOrderDTO.setInvokeBySchedule(Boolean.TRUE);
        approveOrderDTO.setId(serviceOrders.get(0).getId());
        admin.setLicenceExpire(false);
        log.info("approveServiceOrder HPC专属资源池激活");
        ServiceOrder serviceOrder = null;
        String dealKey = "HPC-OFFLINE-RejectApproveServiceOrder-" + clusterId;
        if (clusterKeys.contains(dealKey)
            || (!Objects.equals(currKey, key) && "SAASPrivate".equals(resHpcClusterRemoteModule.getClusterType()))) {

            AuthUserHolder.clear();
            return;
        }

        try {
            clusterKeys.add(dealKey);
            serviceOrder = serviceOrderService.approveServiceOrder(approveOrderDTO, admin.getAccount(), admin);
            log.debug("rejectApproveServiceOrder HPC-OFFLINE拒绝并关闭成功:[{}]", JSONUtil.toJsonStr(serviceOrder));
        } catch (BizException e){
            String exceptionMessage = e.getMessage();
            log.error("rejectApproveServiceOrder-ServiceOrderRemoteServiceImpl.approveServiceOrder-BizException异常；{}",exceptionMessage);
            resHpcClusterRemoteModule.setErrorInfo(exceptionMessage);
            hpcRemoteService.updateByPrimaryKeySelective(resHpcClusterRemoteModule);
        } catch (Exception e){
            log.error("rejectApproveServiceOrder-ServiceOrderRemoteServiceImpl.approveServiceOrder-异常；{}",e.getMessage());
        }finally {
            log.info("rejectApproveServiceOrder正在处理资源池列表 列表[{}] 待删除资源[{}]", clusterKeys.toString(), dealKey);
            clusterKeys.remove(dealKey);
            log.info("rejectApproveServiceOrder正在处理资源池列表 列表[{}]", clusterKeys.toString());
            AuthUserHolder.clear();
            JedisUtil.instance().del(serviceOrders.get(0).getRedisKeyHPCApproveRemark());
        }
    }
}
