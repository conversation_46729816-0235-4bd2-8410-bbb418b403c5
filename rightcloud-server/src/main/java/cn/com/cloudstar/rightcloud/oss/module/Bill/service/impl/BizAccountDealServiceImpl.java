/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.module.Bill.service.impl;

import cn.com.cloudstar.rightcloud.adapter.pojo.price.enums.ChargeTypeEnum;
import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnv;
import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnvAccount;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResShare;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.IdWorker;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.BizAccountDeal;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost;
import cn.com.cloudstar.rightcloud.core.pojo.dto.audit.ServiceOrder;
import cn.com.cloudstar.rightcloud.core.pojo.dto.audit.ServiceOrderDetail;
import cn.com.cloudstar.rightcloud.core.pojo.dto.audit.ServiceOrderPriceDetail;
import cn.com.cloudstar.rightcloud.core.pojo.dto.audit.ServiceOrderResourceRef;
import cn.com.cloudstar.rightcloud.core.pojo.dto.charge.BizBillingAccount;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.Org;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.User;
import cn.com.cloudstar.rightcloud.oss.common.constants.SysConfigConstants;
import cn.com.cloudstar.rightcloud.oss.common.constants.res.type.CloudEnvType;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.*;
import cn.com.cloudstar.rightcloud.oss.common.enums.BusinessTagEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.ProductEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.RechargeTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.oss.common.pojo.*;
import cn.com.cloudstar.rightcloud.oss.common.util.*;
import cn.com.cloudstar.rightcloud.oss.module.Bill.bean.IdName;
import cn.com.cloudstar.rightcloud.oss.module.Bill.service.IBizAccountDealService;
import cn.com.cloudstar.rightcloud.oss.module.Bill.service.IBizInquiryPriceFeignSerivce;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.model.BizDistributor;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.org.BizDistributorMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.org.OrgMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.user.UserMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.service.user.UserService;
import cn.com.cloudstar.rightcloud.oss.module.coupon.bean.CashCoupon;
import cn.com.cloudstar.rightcloud.oss.module.coupon.dao.CashCouponMapper;
import cn.com.cloudstar.rightcloud.oss.module.order.dao.ServiceOrderDetailMapper;
import cn.com.cloudstar.rightcloud.oss.module.order.dao.ServiceOrderMapper;
import cn.com.cloudstar.rightcloud.oss.module.order.dao.ServiceOrderPriceDetailMapper;
import cn.com.cloudstar.rightcloud.oss.module.order.dao.ServiceOrderResourceRefMapper;
import cn.com.cloudstar.rightcloud.oss.module.order.util.BizBillingPriceVO;
import cn.com.cloudstar.rightcloud.oss.module.order.util.InquiryPriceResponse;
import cn.com.cloudstar.rightcloud.oss.module.pricing.dao.priceconfig.BizAccountDealMapper;
import cn.com.cloudstar.rightcloud.oss.module.pricing.dao.priceconfig.BizBillingAccountMapper;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.bean.model.SfProductResource;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.dao.SfProductResourceMapper;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResChangeRecordDTO;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.ResChangeRecordParams;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.cloud.CloudEnvAccountRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.cloud.CloudEnvRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.res.ResChangeRecordRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.share.ShareRemoteService;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.MONTH_PATTERN;

/**
 * 收支明细相关服务
 *
 * <AUTHOR>
 * @Date 2021/3/16 15:52
 */
@Service
@Slf4j
public class BizAccountDealServiceImpl implements IBizAccountDealService {

    //明细前缀
    private static final String BIZ_ACCOUNT_DEAL_PREFIX = "SZ";
    //账单前缀
    private static final String BILL_NO_PREFIX = "ZD";

    private static final String CURRENCY_CNY = "CNY";

    private static final IdWorker ID_WORKER = new IdWorker();
    public static final String DATA = "data";
    private static final String CAPACITY = "capacity";

    @Autowired
    private BizAccountDealMapper bizAccountDealMapper;

    @Autowired
    private BizBillingAccountMapper bizBillingAccountMapper;
    @Autowired
    private ServiceOrderResourceRefMapper serviceOrderResourceRefMapper;
    @DubboReference
    private CloudEnvAccountRemoteService cloudEnvAccountRemoteService;
    @DubboReference
    private CloudEnvRemoteService cloudEnvRemoteService;
    @Resource
    private SfProductResourceMapper sfProductResourceMapper;
    @Autowired
    private ServiceOrderDetailMapper serviceOrderDetailMapper;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private OrgMapper orgMapper;
    @Autowired
    private ServiceOrderPriceDetailMapper serviceOrderPriceDetailMapper;
    @DubboReference
    private ShareRemoteService shareRemoteService;
    @DubboReference
    private ResChangeRecordRemoteService resChangeRecordRemoteService;
    @Autowired
    private ServiceOrderMapper serviceOrderMapper;

    @Resource
    private BizDistributorMapper bizDistributorMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private UserService userService;
    @Autowired
    private IBizInquiryPriceFeignSerivce bizInquiryPriceFeignSerivce;

    @Autowired
    private CashCouponMapper cashCouponMapper;

    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 专属资源池做结算。 此处逻辑与 shedule项目 DedicatedResPoolCostServiceImpl.handleOrderToBill()逻辑相似，需同时调整逻辑
     */
    @Transactional
    @Override
    public void settleDedicatedResourcePool(ServiceOrder serviceOrder) {

        defaultSettle(serviceOrder, ProductCodeEnum.DEDICATED_RESOURCE_POOL);
    }

    /**
     * 昇腾IAAS云服务 退订结算
     *
     * @param serviceOrder
     */
    @Override
    @Transactional
    public void settleIAAS(ServiceOrder serviceOrder) {
        defaultSettle(serviceOrder, ProductCodeEnum.IAAS);
    }

    private void defaultSettle(ServiceOrder serviceOrder, ProductCodeEnum productCodeEnum) {
        log.info("{}退订后付费出账 start", productCodeEnum.getProductName());
        Criteria detailCri = new Criteria();
        detailCri.put("orderId", serviceOrder.getId());
        detailCri.put("serviceType", productCodeEnum.getProductCode());
        List<ServiceOrderDetail> serviceOrderDetails = serviceOrderDetailMapper.selectByParams(detailCri);
        ServiceOrderDetail orderDetail = serviceOrderDetails.get(0);

        if (!ChargeTypeEnum.POSTPAID.getValue().equals(orderDetail.getChargeType())) {
            log.info("{}计费类型{}", productCodeEnum.getProductName(), orderDetail.getChargeType());
            log.info("{}退订后付费出账 stop", productCodeEnum.getProductName());
            return;
        }

        String releaseServiceConfig = orderDetail.getServiceConfig();
        JSONObject configObj = JSONObject.parseObject(releaseServiceConfig);
        String productResourceId = configObj.getString("productResourceId");
        if (StringUtils.isEmpty(productResourceId)) {
            throw new BizException(StrUtil.format(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1987828179), serviceOrder.getId()));
        }

        Criteria criteria = new Criteria();
        Long ownerId = serviceOrder.getOwnerId();
        criteria.put("adminSid", ownerId);
        List<BizBillingAccount> bizBillingAccountList = bizBillingAccountMapper.selectByParams(criteria);
        if (CollectionUtil.isEmpty(bizBillingAccountList)) {
            throw new BizException(StrUtil.format(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2139406693), ownerId));
        }
        BizBillingAccount bizBillingAccount = bizBillingAccountList.get(0);
        BizDistributor distributor = bizDistributorMapper.getById(bizBillingAccount.getDistributorId());
        String distributorName = Objects.isNull(distributor) ? null : distributor.getName();
        Criteria params = new Criteria();
        params.put("resourceId", productResourceId);
        params.put("type", productCodeEnum.getProductCode());
        List<ServiceOrderResourceRef> serviceOrderResourceRefs = serviceOrderResourceRefMapper.selectByParams(params);
        if (CollectionUtil.isNotEmpty(serviceOrderResourceRefs)) {

            ServiceOrderResourceRef resourceRef = serviceOrderResourceRefs.get(0);

            String resourceId = resourceRef.getResourceId();
            if (StringUtils.isEmpty(resourceId)) {
                throw new BizException(StrUtil.format(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1987828179), serviceOrder.getId()));
            }
            SfProductResource sfProductResource = sfProductResourceMapper.selectByPrimaryKey(Long.valueOf(resourceId));
            if (sfProductResource == null) {
                throw new BizException(StrUtil.format(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_958771702), resourceId));
            }
            String resourceInfo = sfProductResource.getResourceInfo();
            JSONObject jsonObject = JSONObject.parseObject(resourceInfo);
            String idNamesJsonStr = jsonObject.getString("idNames");
            List<IdName> idNames = JSONObject.parseArray(idNamesJsonStr, IdName.class);

            Long orderDetailId = resourceRef.getOrderDetailId();
            ServiceOrderDetail applyOrderDetail = serviceOrderDetailMapper.selectByPrimaryKey(orderDetailId);
            ServiceOrder applyOrder = serviceOrderMapper.selectByPrimaryKey(applyOrderDetail.getOrderId());

            //查询云环境相关信息
            String serviceConfig = applyOrderDetail.getServiceConfig();
            JSONObject data = JSONObject.parseObject(serviceConfig);
            Long cloudEnvId = data.getLong("cloudEnvId");
            CloudEnvAccount cloudEnvAccount = null;
            CloudEnv cloudEnv = null;
            if (cloudEnvId != null) {
                cloudEnv = cloudEnvRemoteService.selectByPrimaryKey(cloudEnvId);
            }

            if (cloudEnvAccount == null && cloudEnv == null) {
                log.info("无云环境");
            }

            List<ServiceOrderPriceDetail> serviceOrderPriceDetails = getServiceOrderPriceDetails(orderDetailId);
            if (CollectionUtil.isNotEmpty(serviceOrderPriceDetails)) {
                //当前时间
                DateTime now = DateUtil.date();
                //数量
                Integer quantity = orderDetail.getQuantity();

                Date startTime = now;
                //订单对应资源

                //最近一次结算时间
                Date lastBillTime = resourceRef.getLastBillTime();
                //没有最后一次结算时间，按开始时间开始计算
                BigDecimal durHour = BigDecimal.ONE;
                Date endTime = orderDetail.getEndTime();
                //费用
                BigDecimal totalTradePrice = BigDecimal.ZERO;
                if (lastBillTime == null) {
                    //计算资源使用时间
                    startTime = orderDetail.getStartTime();

                } else {
                    startTime = lastBillTime;
                }

                Long durTime = (endTime.getTime() - startTime.getTime()) / 1000 * 1000;


                //计算资源使用时间 小于1小时按一小时算
                if (durTime < 60 * 60 * 1000) {
                    durTime = 60 * 60 * 1000L;
                    endTime = DateUtils.addHours(startTime, 1);
                }
                long hou = millsToHour(durTime);
                long min = millsToMinute(durTime);
                long sec = millsToSecond(durTime);
                durHour = convertHour(hou, min, sec);



                //待扣减价格
                BigDecimal subPrice = getSubPrice(serviceOrderPriceDetails);

                //账单集合
                List<InstanceGaapCost> costs = Lists.newArrayList();

                String minuTime = min > 0 ? min + "分" : "";

                //结算账单,收支明细
                DurationBill durationBill = DurationBill.builder()
                        .distributorName(distributorName)
                        .account(bizBillingAccount)
                        .envAccount(cloudEnvAccount)
                        .cloudEnv(cloudEnv)
                        .productType(productCodeEnum.getProductCode())
                        .productName(applyOrder.getProductName())
                        .duration(durHour)
                        .serviceOrder(serviceOrder)
                        .start(startTime)
                        .usageCount((hou > 0 ? hou + "小时" + min + "分" : minuTime) + sec + "秒")
                        .costs(costs)
                        .end(endTime)
                        .resourceRef(resourceRef)
                        .discountRate(orderDetail.getDiscountRatio())
                        .now(now).build();

                for (ServiceOrderPriceDetail serviceOrderPriceDetail : serviceOrderPriceDetails) {

                    String priceType = serviceOrderPriceDetail.getPriceType();
                    for (int i = 0; i < quantity; i++) {

                        IdName idName = idNames.get(i);
                        durationBill.setUuid(idName.getId());
                        durationBill.setIntanceName(idName.getName());


                        if (PriceType.SERVICE.equals(priceType)) {
                            generateServiceCost(durationBill, durHour, serviceOrderPriceDetail);
                        } else if (PriceType.EXTRA_CONFIG.equals(priceType)) {
                            //按量计算配置费用
                            generateConfigCost(durationBill, durHour, lastBillTime, serviceOrderPriceDetail);
                        } else {
                            //数量*单价*时间
                            BigDecimal fixedHourPrice = serviceOrderPriceDetail.getFixedHourPrice() != null
                                    ? serviceOrderPriceDetail.getFixedHourPrice() : BigDecimal.ZERO;
                            BigDecimal unitHourPrice =
                                    serviceOrderPriceDetail.getUnitHourPrice() != null ? NumberUtil.sub(
                                            serviceOrderPriceDetail.getUnitHourPrice(), subPrice) : BigDecimal.ZERO;

                            //折扣率
//                            BigDecimal tradeUnitHourPrice = serviceOrderPriceDetail.getTradeUnitHourPrice() != null ? serviceOrderPriceDetail.getTradeUnitHourPrice() : BigDecimal.ZERO;
//                            BigDecimal orgUnitHourPice = serviceOrderPriceDetail.getUnitHourPrice() != null ? serviceOrderPriceDetail.getUnitHourPrice() : BigDecimal.ZERO;
//                            BigDecimal discountRate = BigDecimal.ONE;
//                            if (!BigDecimal.ZERO.equals(tradeUnitHourPrice) && !BigDecimal.ZERO.equals(orgUnitHourPice)) {
//                                discountRate = NumberUtil.div(tradeUnitHourPrice, orgUnitHourPice);
//                            }
                            totalTradePrice = NumberUtil.add(fixedHourPrice, NumberUtil.mul(unitHourPrice, durHour));

                            BigDecimal discountRate =
                                    orderDetail.getDiscountRatio() != null ? orderDetail.getDiscountRatio()
                                            : BigDecimal.ONE;
                            BigDecimal totalDisCount = NumberUtil.sub(totalTradePrice,
                                    NumberUtil.mul(totalTradePrice, discountRate));

                            String finalCueValue = NumberUtil.mul(sfProductResource.getCueValue(), durHour)
                                    .setScale(0, BigDecimal.ROUND_HALF_UP)
                                    .toString();
                            durationBill.setCueValue(finalCueValue);


                            if (NumberUtil.isGreater(totalTradePrice, BigDecimal.ZERO)) {
                                durationBill.setPriceType(PriceType.RESOURCE);
                                durationBill.setTradePrice(totalTradePrice);
                                durationBill.setTotalDiscount(totalDisCount);
                                durationBill.setCouponAmount(serviceOrderPriceDetail.getCouponAmount());
                                durationBill.setBillSpec(serviceOrderPriceDetail.getBillingSpec());
                                durationBill.setChargeType(serviceOrderPriceDetail.getChargeType());

                                addResourceCost(durationBill, null);

                            }
                        }
                    }

                }

                //更新账户余额
                bizBillingAccountMapper.updateByPrimaryKeySelective(bizBillingAccount);
                // 修改用户业务标识Tag
                updateUserBusinessTag(bizBillingAccount);
                //更新结算时间
                resourceRef.setLastBillTime(endTime);
                resourceRef.setStatus("release");
                serviceOrderResourceRefMapper.updateByPrimaryKeySelective(resourceRef);

            }

        } else {
            log.info("{}退订后付费出账：OrderResourceRef为空", productCodeEnum.getProductName());
        }

        log.info("{}退订后付费出账 stop", productCodeEnum.getProductName());
    }

    private void updateUserBusinessTag(BizBillingAccount bizBillingAccount) {
        User user = userMapper.selectByPrimaryKey(bizBillingAccount.getAdminSid());
        String businessTag = user.getBusinessTag();
        if (bizBillingAccount.getBalance().compareTo(BigDecimal.ZERO) < 0) {
            if (StringUtil.isNotEmpty(businessTag)) {
                List<String> tagList = new ArrayList<>(Arrays.asList(businessTag.split(";")));
                if (businessTag.indexOf(BusinessTagEnum.ARREARAGE.getTag()) >= 0) {
                    tagList = tagList.stream().map(s -> {
                        if (s.indexOf(BusinessTagEnum.ARREARAGE.getTag()) >= 0) {
                            String[] split = s.replaceAll(BusinessTagEnum.ARREARAGE.getTag(), "")
                                    .replaceAll("\\[", "")
                                    .replaceAll("]", "")
                                    .split(",");
                            List<String> accountIdList = new ArrayList<>(Arrays.asList(split));
                            if (!accountIdList.contains(bizBillingAccount.getId().toString())) {
                                accountIdList.add(bizBillingAccount.getId().toString());
                                s = BusinessTagEnum.ARREARAGE.getTag() + "[" + StringUtils.join(accountIdList, ",")
                                        + "]";
                            }
                        }
                        return s;
                    }).collect(Collectors.toList());
                } else {
                    tagList.add(BusinessTagEnum.ARREARAGE.getTag() + "[" + bizBillingAccount.getId() + "]");
                }
                user.setBusinessTag(StringUtils.join(tagList, ";"));
            } else {
                user.setBusinessTag(BusinessTagEnum.ARREARAGE.getTag() + "[" + bizBillingAccount.getId() + "]");
            }
            userService.updateByPrimaryKeySelective(user);
        } else {
            if (StringUtil.isNotEmpty(businessTag)) {
                List<String> tagList = new ArrayList<>(Arrays.asList(businessTag.split(";")));
                if (businessTag.indexOf(BusinessTagEnum.ARREARAGE.getTag()) >= 0) {
                    tagList = tagList.stream().map(s -> {
                        if (s.indexOf(BusinessTagEnum.ARREARAGE.getTag()) >= 0) {
                            String[] split = s.replaceAll(BusinessTagEnum.ARREARAGE.getTag(), "")
                                    .replaceAll("\\[", "")
                                    .replaceAll("]", "")
                                    .split(",");
                            List<String> accountIdList = new ArrayList<>(Arrays.asList(split));
                            if (accountIdList.contains(bizBillingAccount.getId().toString())) {
                                accountIdList.remove(bizBillingAccount.getId().toString());
                                if (CollectionUtils.isEmpty(accountIdList)) {
                                    return null;
                                }
                                s = BusinessTagEnum.ARREARAGE.getTag() + "[" + StringUtils.join(accountIdList, ",")
                                        + "]";
                            }
                        }
                        return s;
                    }).filter(Objects::nonNull).collect(Collectors.toList());
                    user.setBusinessTag(StringUtils.join(tagList, ";"));
                    userService.updateByPrimaryKeySelective(user);
                }
            }
        }
    }

    private BigDecimal convertHour(long hou, long min, long sec) {
        BigDecimal div = NumberUtil.div(String.valueOf(min), "60");
        BigDecimal div2 = NumberUtil.div(String.valueOf(sec), 60 * 60 + "");
        return NumberUtil.add(hou, div, div2);
    }

    /**
     * 待扣减的价格
     *
     * @param priceDetailList
     * @return
     * <AUTHOR>
     * @Date 2021/3/20 10:28
     */
    private BigDecimal getSubPrice(List<ServiceOrderPriceDetail> priceDetailList) {
        return priceDetailList.stream()
                .filter(o -> !PriceType.RESOURCE.equals(o.getPriceType()) && o.getOncePrice() == null)
                .map(ServiceOrderPriceDetail::getPrice)
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
    }

    /**
     * 生成配置费用账单
     *
     * @param durationBill
     * @param usage
     * @param lastBillTime
     * @param serviceOrderPriceDetail
     */
    private void generateConfigCost(DurationBill durationBill, BigDecimal usage, Date lastBillTime,
                                    ServiceOrderPriceDetail serviceOrderPriceDetail) {
        durationBill.setPriceType(PriceType.EXTRA_CONFIG);
        //第一次计量结算 单次
        BigDecimal onePrice = serviceOrderPriceDetail.getOncePrice();
        if (onePrice != null && NumberUtil.isGreater(onePrice, BigDecimal.ZERO)) {
            if (lastBillTime == null) {
                durationBill.setBillSpec(serviceOrderPriceDetail.getBillingSpec());
                durationBill.setTradePrice(onePrice);
                durationBill.setChargeType(serviceOrderPriceDetail.getChargeType());
                DurationBill bill = DurationBill.builder().build();
                BeanUtil.copyProperties(durationBill, bill);
                bill.setDescription("单次计费");
                bill.setTotalDiscount(BigDecimal.ZERO);
                addResourceCost(bill, null);
            }
        } else if (ChargeTypeEnum.POSTPAID.getValue().equals(serviceOrderPriceDetail.getChargeType())) {

            BigDecimal originalPrice = serviceOrderPriceDetail.getPrice();
            BigDecimal discountAmount = BigDecimal.ZERO;
            if (NumberUtil.isGreaterOrEqual(originalPrice, BigDecimal.ZERO)) {
                originalPrice = NumberUtil.mul(originalPrice, usage);
//                discountAmount = NumberUtil.mul(serviceOrderPriceDetail.getDiscount() != null ? serviceOrderPriceDetail.getDiscount() : BigDecimal.ZERO, usage);
            }

//            durationBill.setUsageCount(usage + "h");
            durationBill.setChargeType(serviceOrderPriceDetail.getChargeType());
            durationBill.setBillSpec(serviceOrderPriceDetail.getBillingSpec());
            durationBill.setCouponAmount(
                    serviceOrderPriceDetail.getCouponAmount() != null ? serviceOrderPriceDetail.getCouponAmount()
                            : BigDecimal.ZERO);

            durationBill.setTradePrice(originalPrice);

            DurationBill bill = DurationBill.builder().build();
            BeanUtil.copyProperties(durationBill, bill);
            bill.setDescription("周期计费");
            bill.setTotalDiscount(discountAmount);
            addResourceCost(bill, null);
        }

    }

    /**
     * 生成服务费用账单
     *
     * @param durationBill
     * @param durHour
     * @param serviceOrderPriceDetail
     */
    private void generateServiceCost(DurationBill durationBill, BigDecimal durHour,
                                     ServiceOrderPriceDetail serviceOrderPriceDetail) {
        BigDecimal originalPrice = serviceOrderPriceDetail.getPrice();
        BigDecimal discountAmount = BigDecimal.ZERO;
        BigDecimal discountRate =
                durationBill.getDiscountRate() != null ? durationBill.getDiscountRate() : BigDecimal.ONE;
        if (NumberUtil.isGreaterOrEqual(originalPrice, BigDecimal.ZERO)) {
            originalPrice = NumberUtil.mul(originalPrice, durHour);
//            discountAmount = NumberUtil.mul(serviceOrderPriceDetail.getDiscount() != null ? serviceOrderPriceDetail.getDiscount() : BigDecimal.ZERO, durHour);
            discountAmount = NumberUtil.mul(originalPrice, NumberUtil.sub(BigDecimal.ONE, discountRate));
        }
        durationBill.setChargeType(serviceOrderPriceDetail.getChargeType());
        durationBill.setBillSpec(serviceOrderPriceDetail.getBillingSpec());
        durationBill.setTradePrice(originalPrice);
        durationBill.setCouponAmount(
                serviceOrderPriceDetail.getCouponAmount() != null ? serviceOrderPriceDetail.getCouponAmount()
                        : BigDecimal.ZERO);
        durationBill.setPriceType(PriceType.SERVICE);

        DurationBill bill = DurationBill.builder().build();
        BeanUtil.copyProperties(durationBill, bill);
        bill.setTotalDiscount(discountAmount);
        addResourceCost(bill, null);
    }

    /**
     * 订单价格明细
     *
     * @param detailId
     * @return
     */
    private List<ServiceOrderPriceDetail> getServiceOrderPriceDetails(Long detailId) {
        Criteria criteria = new Criteria();
        criteria.put("orderDetailId", detailId);
        return serviceOrderPriceDetailMapper.selectByParams(criteria);
    }

    /**
     * 毫秒，秒转小时
     *
     * @param value
     * @param type
     * @return
     */
    private BigDecimal millsToHours(String value, String type) {
        BigDecimal size = new BigDecimal(value);
        BigDecimal m = new BigDecimal(60 * 60);
        BigDecimal ms = new BigDecimal(60 * 60 * 1000);
        /*divide(BigDecimal divisor, int scale, int roundingMode) 返回一个BigDecimal，其值为（this/divisor），其标度为指定标度*/
        if ("second".equalsIgnoreCase(type)) {

            //秒换算
            return NumberUtil.div(size, m);
        } else if ("millisecond".equalsIgnoreCase(type)) {
            //毫秒换算
            return NumberUtil.div(size, ms);
        } else {
            return BigDecimal.ZERO;
        }
    }

    private void addResourceCost(DurationBill durationBill, List<CashCoupon> cashCoupons) {
        BigDecimal tradePrice = durationBill.getTradePrice();
        BizBillingAccount account = durationBill.getAccount();
        ServiceOrder serviceOrder = durationBill.getServiceOrder();
        log.info("订单编号=[{}]退订出账,原始金额=[{}]", serviceOrder.getOrderSn(), tradePrice);
//        if (NumberUtil.isGreater(tradePrice, BigDecimal.ZERO)) {

        SfProductResource sfProductResource = new SfProductResource();
        String resourceId = durationBill.getResourceRef().getResourceId();
        if (StrUtil.isNotEmpty(resourceId)) {
            sfProductResource = sfProductResourceMapper.selectByPrimaryKey(Long.valueOf(resourceId));
        }

        //本次金额大于0
        Date now = durationBill.getNow();
        Date useStartTime = durationBill.getStart() != null ? durationBill.getStart() : now;
        Date useEndTime = durationBill.getEnd() != null ? durationBill.getEnd() : now;

        InstanceGaapCost cost = new InstanceGaapCost();
        cost.setOrgSid(account.getOrgSid());
        cost.setCashAmount(BigDecimal.ZERO);

        Org org = orgMapper.selectByPrimaryKey(account.getOrgSid());
        cost.setOrgName(org.getOrgName());
        cost.setBillNo(NoUtil.generateNo(BILL_NO_PREFIX));
        cost.setBillingCycle(DateUtil.format(now, MONTH_PATTERN));
        cost.setBillSource("platform");
        cost.setOwnerId(account.getAdminSid().toString());
        cost.setConfiguration(durationBill.getBillSpec());
//            cost.setRegion(detail.getRegion_code());
        cost.setOrderId(serviceOrder.getId().toString());
        cost.setOrderSn(serviceOrder.getOrderSn());
        cost.setInstanceName(durationBill.getIntanceName());
        cost.setUserAccountId(account.getId());
        cost.setUserAccountName(account.getCreatedBy());
        //查询云环境
        CloudEnvAccount envAccount = durationBill.getEnvAccount();
        CloudEnv cloudEnv = durationBill.getCloudEnv();
        if (cloudEnv != null) {
            cost.setCloudEnvId(cloudEnv.getId());
            cost.setCloudEnvName(cloudEnv.getCloudEnvName());
            cost.setCloudEnvType(cloudEnv.getCloudEnvType());
            cost.setCloudEnvAccountId(cloudEnv.getCloudEnvAccountId());
            cost.setCloudEnvAccountName(cloudEnv.getCloudEnvAccountName());
            cost.setRegion(cloudEnv.getRegion());
        } else if (envAccount != null) {
            List<CloudEnv> cloudEnvs = cloudEnvRemoteService.selectByCloudEnvAccountId(envAccount.getId());
            if (cloudEnvs != null && cloudEnvs.size() > 0) {
                cloudEnv = cloudEnvs.get(0);
                cost.setCloudEnvId(cloudEnv.getId());
                cost.setCloudEnvName(cloudEnv.getCloudEnvName());
                cost.setCloudEnvType(cloudEnv.getCloudEnvType());
                cost.setCloudEnvAccountId(cloudEnv.getCloudEnvAccountId());
                cost.setCloudEnvAccountName(cloudEnv.getCloudEnvAccountName());
                cost.setRegion(cloudEnv.getRegion());
            }
        }

        cost.setCreditAmount(BigDecimal.ZERO);
        cost.setProductCode(durationBill.getProductType());
        cost.setProductName(durationBill.getProductName());
        cost.setResourceId(durationBill.getUuid());
        cost.setCreatedDt(durationBill.getNow());
        cost.setBillType(BillType.fromChargeTypeEnum(durationBill.getChargeType()));
        cost.setPayTime(now);
        cost.setPriceType(durationBill.getPriceType());
        // 原始金额
        tradePrice = tradePrice.setScale(3, BigDecimal.ROUND_HALF_UP);
        cost.setPretaxGrossAmount(tradePrice);
        // 优惠金额
        BigDecimal discount = durationBill.getTotalDiscount() != null ? durationBill.getTotalDiscount()
                .setScale(3,
                        BigDecimal.ROUND_HALF_UP)
                : BigDecimal.ZERO;
        cost.setPricingDiscount(discount);
//            BigDecimal couponAmount = durationBill.getCouponAmount() != null ? durationBill.getCouponAmount() : BigDecimal.ZERO;
//            cost.setCouponAmount(couponAmount);
        cost.setCouponDiscount(BigDecimal.ZERO);
        // 优惠后金额
        BigDecimal pretaxAmount = BigDecimal.ZERO;
        if (NumberUtil.isGreater(tradePrice, BigDecimal.ZERO)) {
            pretaxAmount = NumberUtil.sub(tradePrice, discount);
            if (NumberUtil.isLess(pretaxAmount, BigDecimal.ZERO)) {
                pretaxAmount = BigDecimal.ZERO;
            }
            pretaxAmount = pretaxAmount.setScale(3, BigDecimal.ROUND_HALF_UP);
        }
        cost.setEraseZeroAmount(BigDecimalUtil.getEraseZeroColumn(pretaxAmount));
        cost.setPretaxAmount(BigDecimalUtil.remainTwoPointAmount(pretaxAmount));

        cost.setUsageStartDate(useStartTime);
        cost.setUsageEndDate(useEndTime);
        cost.setEntityId(account.getEntityId());
        cost.setEntityName(account.getEntityName());
        cost.setOrderSourceSn(serviceOrder.getOrderSn());
        cost.setType(OrderType.RELEASE);
        cost.setDistributorName(durationBill.getDistributorName());
//
        // 使用时间类：modelarts持续时间单位是秒
        cost.setUsageCount(durationBill.getUsageCount());
        //币种
        cost.setCurrency(CURRENCY_CNY);

        cost.setDescription(durationBill.getDescription());

        cost.setCueValue(durationBill.getCueValue());

        //设置询价单价
        cost.setInquiryFixedHourPrice(durationBill.getInquiryFixedHourPrice());
        cost.setInquiryUnitHourPrice(durationBill.getInquiryUnitHourPrice());
        cost.setInquiryDiscountRate(durationBill.getInquiryDiscountRate());

        //资源ID 对应话单中资源ID
        log.info("资源id=[{}],名称=[{}], 入账=[{}], 使用开始时间=[{}], 结束时间=[{}], 用量=[{}]",
                cost.getResourceId(), cost.getInstanceName(), pretaxAmount,
                DateUtil.formatDateTime(cost.getUsageStartDate()),
                DateUtil.formatDateTime(cost.getUsageEndDate()), cost.getUsageCount());

        durationBill.setTradePrice(pretaxAmount);
        durationBill.setDeals(new ArrayList<>());

        //收支明细
        makeAccountDeal(durationBill, cost, cashCoupons);
        //批量添收支明细
        List<BizAccountDeal> deals = durationBill.getDeals();
        if (CollectionUtil.isNotEmpty(deals)) {
            bizAccountDealMapper.batchInsert(deals);
        }

        //出账
        mongoTemplate.insert(cost);

        recordHpcCostId(serviceOrder.getActionType(),cost);
//        }
    }

    /**
     * 收支明细
     *
     * @param durationBill 账单明细
     * @param cost         账单
     * @param cashCoupons
     */
    private void makeAccountDeal(DurationBill durationBill, InstanceGaapCost cost, List<CashCoupon> cashCoupons) {

        BizBillingAccount account = durationBill.getAccount();

        BizAccountDeal accountDeal = new BizAccountDeal();
        accountDeal.setDealSid(ID_WORKER.nextId());
        accountDeal.setFlowNo(NoUtil.generateNo(BIZ_ACCOUNT_DEAL_PREFIX));
        accountDeal.setType(DealType.OUT);
        accountDeal.setTradeType(TradeType.PAY);
        accountDeal.setTradeNo(NoUtil.generateNo(BILL_NO_PREFIX));
        accountDeal.setEnvType(cost.getCloudEnvType());
        accountDeal.setEnvName(StrUtil.isNotEmpty(cost.getCloudEnvType()) ?
                CloudEnvType.from(cost.getCloudEnvType()).getDesc() : "");
        accountDeal.setEntityId(account.getEntityId());
        accountDeal.setEntityName(account.getEntityName());

        accountDeal.setBillNo(cost.getBillNo());
        accountDeal.setOrderNo(
                durationBill.getServiceOrder() != null ? durationBill.getServiceOrder().getOrderSn() : "");

        String priceType = durationBill.getPriceType();
        String priceTypeDes = "";
        if (PriceType.RESOURCE.equals(priceType)) {
            priceTypeDes = "-资源费用";
        } else if (PriceType.SERVICE.equals(priceType)) {
            priceTypeDes = "-服务费用";
        } else if (PriceType.EXTRA_CONFIG.equals(priceType)) {
            priceTypeDes = "-配置费用";
        }

//        String envName = accountDeal.getEnvName();
//        if(StringUtil.isNotEmpty(envName)){
//            envName = "["+envName+"]";
//        }

        accountDeal.setRemark(StrUtil.concat(true, durationBill.getProductName(), priceTypeDes));
        accountDeal.setBillingCycle(LocalDate.now().format(DateTimeFormatter.ofPattern(MONTH_PATTERN)));
        accountDeal.setAccountSid(account.getId());
        accountDeal.setAccountName(account.getAccountName());
        accountDeal.setOrgSid(account.getOrgSid());
        accountDeal.setUserSid(account.getId());
        accountDeal.setDealTime(System.currentTimeMillis());
        accountDeal.setAmount(BigDecimal.ZERO);

        BigDecimal tradePrice = cost.getPretaxAmount();

        BigDecimal balance = account.getBalance();
        BigDecimal balanceCash = account.getBalanceCash() == null ? BigDecimal.ZERO : account.getBalanceCash();
        BigDecimal creditLine = account.getCreditLine() == null ? BigDecimal.ZERO : account.getCreditLine();

        BigDecimal needTradePrice = tradePrice;

        //创建人,创建时间等信息
        WebUserUtil.prepareInsertParams(accountDeal, account.getAccountName());
        // 优先使用抵扣现金券
        needTradePrice = useCashCoupons(durationBill, cost, cashCoupons, account, accountDeal, balance, creditLine, needTradePrice);
        //有现金券扣除现金券
        if (NumberUtil.isGreater(balanceCash, BigDecimal.ZERO)) {

            accountDeal.setTradeChannel(RechargeTypeEnum.ACC_BCASH.getCode());
            //现金券余额
            BigDecimal remainBalanceCash = balanceCash;
            if (NumberUtil.isGreaterOrEqual(balanceCash, needTradePrice)) {
                remainBalanceCash = NumberUtil.sub(balanceCash, needTradePrice);
                accountDeal.setBalanceCash(remainBalanceCash);
                accountDeal.setAmount(needTradePrice);
                needTradePrice = BigDecimal.ZERO;
            } else {
                //现金余额归零
                accountDeal.setBalanceCash(BigDecimal.ZERO);
                accountDeal.setAmount(balanceCash);
                needTradePrice = NumberUtil.sub(needTradePrice, balanceCash);
            }
            accountDeal.setBalance(balance);
            accountDeal.setBalanceCredit(creditLine);
            durationBill.getDeals().add(accountDeal);

            cost.setCouponAmount(accountDeal.getAmount());
            //账户现金券余额
            account.setBalanceCash(accountDeal.getBalanceCash());
        }

        //现金券余额支付后剩余部分由余额继续支付
        BizAccountDeal balanceDeal = BeanConvertUtil.convert(accountDeal, BizAccountDeal.class);
        if (NumberUtil.isGreater(balance, BigDecimal.ZERO) && NumberUtil.isGreater(needTradePrice, BigDecimal.ZERO)) {
            balanceDeal.setDealSid(ID_WORKER.nextId());
            assert balanceDeal != null;
            balanceDeal.setFlowNo(NoUtil.generateNo(BIZ_ACCOUNT_DEAL_PREFIX));
            if (NumberUtil.isGreaterOrEqual(balance, needTradePrice)) {
                balanceDeal.setAmount(needTradePrice);
                balance = NumberUtil.sub(balance, needTradePrice);
                needTradePrice = BigDecimal.ZERO;
            } else {
                balanceDeal.setAmount(balance);
                needTradePrice = NumberUtil.sub(needTradePrice, balance);
                balance = BigDecimal.ZERO;
            }
            balanceDeal.setBalance(balance);
            balanceDeal.setBalanceCredit(creditLine);
            balanceDeal.setTradeChannel(RechargeTypeEnum.ACC_TCASH.getCode());
            balanceDeal.setTradeNo(NoUtil.generateNo(BILL_NO_PREFIX));
            balanceDeal.setFlowNo(NoUtil.generateNo(BIZ_ACCOUNT_DEAL_PREFIX));
            durationBill.getDeals().add(balanceDeal);

            cost.setCashAmount(balanceDeal.getAmount());

            account.setBalance(balance);

        }

        //余额支付后剩余部分由信用额度扣除
        BizAccountDeal creditDeal = BeanConvertUtil.convert(balanceDeal, BizAccountDeal.class);
        if (NumberUtil.isGreater(creditLine, BigDecimal.ZERO) && NumberUtil.isGreater(needTradePrice,
                BigDecimal.ZERO)) {
            creditDeal.setDealSid(ID_WORKER.nextId());
            creditDeal.setFlowNo(NoUtil.generateNo(BIZ_ACCOUNT_DEAL_PREFIX));
            if (NumberUtil.isGreaterOrEqual(creditLine, needTradePrice)) {
                creditDeal.setAmount(needTradePrice);
                cost.setCreditAmount(needTradePrice);
                creditLine = NumberUtil.sub(creditLine, needTradePrice);
                needTradePrice = BigDecimal.ZERO;
            } else {
                creditDeal.setAmount(creditLine);
                cost.setCreditAmount(creditLine);
                needTradePrice = NumberUtil.sub(needTradePrice, creditLine);
                creditLine = BigDecimal.ZERO;
            }
            creditDeal.setBalance(balance);
            creditDeal.setBalanceCredit(creditLine);
            creditDeal.setTradeChannel(RechargeTypeEnum.ACC_CREDIT.getCode());
            creditDeal.setTradeNo(NoUtil.generateNo(BILL_NO_PREFIX));
            creditDeal.setFlowNo(NoUtil.generateNo(BIZ_ACCOUNT_DEAL_PREFIX));
            durationBill.getDeals().add(creditDeal);

            account.setCreditLine(creditLine);
        }

        //最后剩余未支付,扣减余额
        if (NumberUtil.isGreater(needTradePrice, BigDecimal.ZERO)) {
            BizAccountDeal lastbalanceDeal = BeanConvertUtil.convert(creditDeal, BizAccountDeal.class);
            if (lastbalanceDeal == null) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_780030056));
            }
            lastbalanceDeal.setDealSid(ID_WORKER.nextId());
            assert lastbalanceDeal != null;
            lastbalanceDeal.setFlowNo(NoUtil.generateNo(BIZ_ACCOUNT_DEAL_PREFIX));
            balance = NumberUtil.sub(balance, needTradePrice);

            lastbalanceDeal.setBalance(balance);
            lastbalanceDeal.setAmount(needTradePrice);
            lastbalanceDeal.setBalanceCredit(creditLine);
            lastbalanceDeal.setTradeChannel(RechargeTypeEnum.ACC_TCASH.getCode());
            lastbalanceDeal.setTradeNo(NoUtil.generateNo(BILL_NO_PREFIX));
            lastbalanceDeal.setFlowNo(NoUtil.generateNo(BIZ_ACCOUNT_DEAL_PREFIX));
            durationBill.getDeals().add(lastbalanceDeal);

            cost.setCashAmount(cost.getCashAmount().add(needTradePrice));

            account.setBalance(balance);
        }
    }

    /**
     * 使用抵扣现金券
     *
     * @param durationBill
     * @param cost
     * @param cashCoupons
     * @param account
     * @param accountDeal
     * @param balance
     * @param creditLine
     * @param needTradePrice
     * @return
     */
    private BigDecimal useCashCoupons(DurationBill durationBill, InstanceGaapCost cost, List<CashCoupon> cashCoupons, BizBillingAccount account,
                                      BizAccountDeal accountDeal, BigDecimal balance, BigDecimal creditLine, BigDecimal needTradePrice) {
        if (NumberUtil.isGreater(needTradePrice, BigDecimal.ZERO) && !CollectionUtils.isEmpty(cashCoupons)) {
            // 抵扣现金券， 查询是否包含, 先按照productCode和accountId进行过滤
            List<CashCoupon> individualCashCoupons = cashCoupons.stream()
                    .filter(pred -> Objects.nonNull(pred.getAccountId())
                            && pred
                            .getAccountId()
                            .equals(account.getId()))
                    .filter(pred -> {
                        if (Objects.isNull(pred.getProductScope())) {
                            return true;
                        }
                        if (Arrays.asList(pred.getProductScope()
                                .toLowerCase()
                                .split(","))
                                .contains(durationBill.getProductType().toLowerCase())) {
                            return true;
                        }
                        return false;
                    })
                    .collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(individualCashCoupons)) {
                //  多张抵扣现金券， 优先使用接近到期的一张
                //  到期时间一致， 优先使用适用范围更小的一张
                //  适用范围一致， 最终优先使用最早发放的一张
                individualCashCoupons = individualCashCoupons.stream()
                        .sorted(Comparator.comparing(CashCoupon::getEndTime, Comparator.nullsLast(Comparable::compareTo))
                                .thenComparing(cashCoupon -> cashCoupon.getProductScope()
                                        .split(",").length, Comparator.nullsLast(Comparable::compareTo))
                                .thenComparing(CashCoupon::getUpdatedDt, Comparator.nullsLast(Comparable::compareTo)))
                        .collect(Collectors.toList());
            }

            //  抵扣第一张余额不足以覆盖，则继续抵扣下一张
            int i = 0;
            while (i < individualCashCoupons.size() && NumberUtil.isGreater(needTradePrice, BigDecimal.ZERO)) {
                BizAccountDeal deductCashDeal = BeanConvertUtil.convert(accountDeal, BizAccountDeal.class);
                deductCashDeal.setDealSid(ID_WORKER.nextId());
                deductCashDeal.setTradeChannel(RechargeTypeEnum.ACC_DCASH.getCode());
                CashCoupon coupon = individualCashCoupons.get(i);

                // 抵扣现金券支付金额
                BigDecimal deductCashAmount = BigDecimal.ZERO;
                BigDecimal couponBalanceBefore = BigDecimalUtil.getTwoPointAmount(coupon.getBalance());
                BigDecimal couponBalanceAfter = BigDecimal.ZERO;

                if (NumberUtil.isGreaterOrEqual(BigDecimalUtil.getTwoPointAmount(coupon.getBalance()), needTradePrice)) {
                    // 抵扣现金券抵扣后剩余金额
                    couponBalanceAfter = NumberUtil.sub(coupon.getBalance(), needTradePrice);
                    coupon.setBalance(couponBalanceAfter);
                    // 抵扣现金券抵扣金额
                    deductCashAmount = needTradePrice;
                    deductCashDeal.setDeductCashNo(coupon.getCouponNo());
                    deductCashDeal.setAmount(needTradePrice);
                    needTradePrice = BigDecimal.ZERO;
                } else {
                    if (BigDecimalUtil.getTwoPointAmount(coupon.getBalance()).compareTo(BigDecimal.ZERO) == 0) {
                        // 跳过余额耗尽的现金券
                        i++;
                        continue;
                    }
                    // 抵扣现金券余额归零
                    deductCashAmount = BigDecimalUtil.getTwoPointAmount(coupon.getBalance());
                    coupon.setBalance(NumberUtil.sub(coupon.getBalance(), deductCashAmount));

                    deductCashDeal.setDeductCashNo(coupon.getCouponNo());
                    deductCashDeal.setAmount(deductCashAmount);
                    needTradePrice = NumberUtil.sub(needTradePrice, deductCashAmount);
                }
                // 设置抵扣金额和 抵扣现金券余额
                deductCashDeal.setDeductBalanceCash(couponBalanceAfter);
//                deductCashDeal.setDeductCashAmount(deductCashAmount);
                if (BigDecimal.ZERO.compareTo(BigDecimalUtil.getTwoPointAmount(coupon.getBalance())) == 0) {
                    // 抵扣现金券耗尽
                    coupon.setStatus("exhausted");
                }
                deductCashDeal.setBalance(balance);
                deductCashDeal.setBalanceCredit(creditLine);
                deductCashDeal.setTradeNo(NoUtil.generateNo(BILL_NO_PREFIX));
                deductCashDeal.setFlowNo(NoUtil.generateNo(BIZ_ACCOUNT_DEAL_PREFIX));
                durationBill.getDeals().add(deductCashDeal);
                if (Objects.isNull(cost.getDeductCouponDiscount())) {
                    cost.setDeductCouponDiscount(BigDecimal.ZERO);
                }
                if (CollectionUtils.isEmpty(cost.getDeductionCashs())) {
                    cost.setDeductionCashs(new ArrayList<>());
                }
                // 为cost设置抵扣记录
                DeductionCashRecord deductionCashRecord = new DeductionCashRecord();
                cost.getDeductionCashs().add(deductionCashRecord);
                deductionCashRecord.setDeductCouponDiscount(deductCashAmount);
                deductionCashRecord.setDeductionCouponBalanceAfter(couponBalanceAfter);
                deductionCashRecord.setDeductionCouponBalanceBefore(couponBalanceBefore);
                deductionCashRecord.setDeductionCouponNo(coupon.getCouponNo());

                cost.setDeductCouponDiscount(cost.getDeductCouponDiscount().add(deductCashAmount));
                //账户现金券余额
                account.setBalanceCash(deductCashDeal.getBalanceCash());
                i++;
            }
            // 更新抵扣现金券状态
            cashCouponMapper.updateCashCoupon(cashCoupons);
        }
        return needTradePrice;
    }

    private static long NH = 1000 * 60 * 60;
    private static long NM = 1000 * 60;

    /**
     * 毫秒，秒转小时
     *
     * @param value
     * @return
     */
    private long millsToHour(long value) {
        long hour = value / NH;
        return hour;
    }

    /**
     * 毫秒，秒转小时
     *
     * @param value
     * @return
     */
    private long millsToMinute(long value) {
        long min = (value % NH) / NM;
        return min;
    }

    /**
     * 毫秒，秒转小时
     *
     * @param value
     * @return
     */
    private long millsToSecond(long value) {
        long sec = ((value % NH) % NM) / 1000;
        return sec;
    }

    @Transactional
    @Override
    public void settleSFS(ServiceOrder serviceOrder) {

        log.info("弹性文件服务退订后付费出账 start");
        List<ServiceOrderDetail> details = serviceOrder.getDetails();
        ServiceOrderDetail orderDetail = null;
        ServiceOrderResourceRef resourceRef = null;
        //当前时间
        DateTime now = DateUtil.date();
        for (ServiceOrderDetail detail : details) {

            Criteria params = new Criteria();
            params.put("orderDetailId", detail.getId());
            List<ServiceOrderResourceRef> serviceOrderResourceRefs = serviceOrderResourceRefMapper.selectByParams(
                    params);
            if (!CollectionUtils.isEmpty(serviceOrderResourceRefs)) {
                ServiceOrderResourceRef ref = serviceOrderResourceRefs.get(0);
                String serviceType = detail.getServiceType();
                if ("release".equals(ref.getStatus())) {
                    log.info("弹性文件服务订单ID=[{}],serviceType=[{}}]已退订", orderDetail.getOrderId(), serviceType);
                } else {
                    if (ProductCodeEnum.SFS.getProductCode().equals(serviceType)
                        || ProductCodeEnum.SFS2.getProductCode().equals(serviceType)
                        || ProductCodeEnum.DME_OSP.getProductCode().equals(serviceType)
                    ) {
                        orderDetail = detail;
                        resourceRef = ref;
                        //更新SFS结算时间
                        // ref.setLastBillTime(now);
                        ref.setStatus("release");
                        serviceOrderResourceRefMapper.updateByPrimaryKeySelective(ref);
                    } else {
                        //更新HPC结算时间
                        ref.setLastBillTime(now);
                        ref.setStatus("release");
                        serviceOrderResourceRefMapper.updateByPrimaryKeySelective(ref);
                    }
                }
            }


        }

        if (orderDetail == null) {
            log.info("弹性文件服务orderDetail为空");
            log.info("弹性文件服务退订后付费出账 stop");
            return;
        } else if (!ChargeTypeEnum.POSTPAID.getValue().equals(orderDetail.getChargeType())) {
            log.info("弹性文件服务退订计费类型{}", orderDetail.getChargeType());
            log.info("弹性文件服务退订后付费出账 stop");
            return;
        }


        Criteria criteria = new Criteria();
        Long ownerId = serviceOrder.getOwnerId();
        //修改bug 43919 增加当前账户的查询 用户的账户和运营实体，包括账户id一一对应,防止扣除其它的运营实体账户的钱
//        criteria.put("adminSid", ownerId);
//        criteria.put("entityId", serviceOrder.getEntityId());
        // criteria.put("id",serviceOrder.getBizBillingAccountId());
        BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectByPrimaryKey(serviceOrder.getBizBillingAccountId());
        if (bizBillingAccount == null) {
            throw new BizException(StrUtil.format(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2139406693), ownerId));
        }
        // BizBillingAccount bizBillingAccount = bizBillingAccountList.get(0);
        BizDistributor distributor = bizDistributorMapper.getById(bizBillingAccount.getDistributorId());
        String distributorName = Objects.isNull(distributor) ? null : distributor.getName();
        if (resourceRef != null) {

            String resourceId = resourceRef.getResourceId();
            if (StringUtils.isEmpty(resourceId)) {
                throw new BizException(StrUtil.format(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1987828179), serviceOrder.getId()));
            }


            // 查出对应账号的可用的抵扣现金券
            Criteria criteria1 = new Criteria();
            criteria1.put("status", "available");
            criteria1.put("type", "deduct");
            criteria1.put("endTime", DateUtil.formatDateTime(new Date()));
            criteria1.put("startTime", DateUtil.formatDateTime(new Date()));
            criteria1.put("accountId", serviceOrder.getBizBillingAccountId());
            List<CashCoupon> cashCoupons = cashCouponMapper.selectCashCoupon(criteria1);


            //查询云环境相关信息
            String serviceConfig = orderDetail.getServiceConfig();
            JSONObject data = JSONObject.parseObject(serviceConfig);
            Long cloudEnvId = data.getLong("cloudEnvId");
            CloudEnvAccount cloudEnvAccount = null;
            CloudEnv cloudEnv = null;
            if (cloudEnvId != null) {
                cloudEnv = cloudEnvRemoteService.selectByPrimaryKey(cloudEnvId);
            }

            if (cloudEnvAccount == null && cloudEnv == null) {
                log.info("无云环境");
            }
            //账单集合
            List<InstanceGaapCost> costs = Lists.newArrayList();

            Long detailId = orderDetail.getId();
            //当前时间
            Date startTime = now;
            //最近一次结算时间
            Date lastBillTime = resourceRef.getLastBillTime();
            //没有最后一次结算时间，按开始时间开始计算
            BigDecimal durHour = BigDecimal.ONE;
            //费用
            BigDecimal totalTradePrice = BigDecimal.ZERO;
            if (lastBillTime == null) {
                //计算资源使用时间
                startTime = orderDetail.getStartTime();
            } else {
                startTime = lastBillTime;
            }

            Long durTime = (now.getTime() - startTime.getTime()) / 1000 * 1000;
            durHour = this.millsToHours(durTime.toString(), "millisecond");


            //是否实时获取价格计算费用
            if (this.checkPriceFromInquiry()) {
                this.sfsCostFromInquiry(serviceOrder, costs, distributorName, cashCoupons, bizBillingAccount, orderDetail, cloudEnvAccount, cloudEnv,
                        startTime,
                        resourceRef, durHour, now);
            } else {
                this.sfsCostFromOrder(serviceOrder, costs, distributorName, cashCoupons, bizBillingAccount, orderDetail, cloudEnvAccount, cloudEnv,
                        startTime,
                        resourceRef, durHour, now);
            }


            //更新账户余额
            bizBillingAccountMapper.updateByPrimaryKeySelective(bizBillingAccount);
            // 修改用户业务标识Tag
            updateUserBusinessTag(bizBillingAccount);
            //更新结算时间

//                Date newLastBillTime = DateUtils.addMinutes(endTime, 1);
//                String newLastBillTimeStr = DateUtil.dateFormat(newLastBillTime, "yyyy-MM-dd HH:00:00");
//                newLastBillTime = DateUtil.parseDate(newLastBillTimeStr, "yyyy-MM-dd HH:mm:ss");

            resourceRef.setLastBillTime(now);
            resourceRef.setStatus("release");
            serviceOrderResourceRefMapper.updateByPrimaryKeySelective(resourceRef);


        } else {
            log.info("弹性文件服务退订后付费出账：OrderResourceRef为空");
        }

        log.info("弹性文件服务退订后付费出账 stop");
    }


    /**
     * 实时获取价格 计算费用
     *
     * @param serviceOrder
     * @param costs
     * @param cashCoupons
     * @param bizBillingAccount
     * @param serviceOrderDetail
     * @param cloudEnvAccount
     * @param cloudEnv
     * @param startTime
     * @param resourceRef
     * @param durHour
     * @param now
     */
    private void sfsCostFromInquiry(ServiceOrder serviceOrder, List<InstanceGaapCost> costs, String distributorName, List<CashCoupon> cashCoupons,
                                    BizBillingAccount bizBillingAccount, ServiceOrderDetail serviceOrderDetail, CloudEnvAccount cloudEnvAccount,
                                    CloudEnv cloudEnv, Date startTime, ServiceOrderResourceRef resourceRef,
                                    BigDecimal durHour,
                                    Date now) {
        Long durTime = (now.getTime() - startTime.getTime()) / 1000 * 1000;
        String serviceConfig = serviceOrderDetail.getServiceConfig();
        JSONObject serviceConfigJOB = JSONObject.parseObject(serviceConfig);
        JSONObject dataJOB = serviceConfigJOB.getJSONObject(DATA);
        //设置询价请求
        ApplyServiceRequest applyServiceRequest = createApplyServiceRequst(serviceOrder,serviceOrderDetail, bizBillingAccount);
        applyServiceRequest.getProductInfo().get(0).setData(dataJOB);
        applyServiceRequest.setEntityId(serviceOrder.getEntityId());
        log.debug("BizAccountDealServiceImpl.sfsCostFromInquiry applyServiceRequest= {}", JSON.toJSONString(applyServiceRequest));
        RestResult restResult = bizInquiryPriceFeignSerivce.inquiryPrice(applyServiceRequest);
        String data = JSONObject.toJSONString(restResult.get(DATA));
        List<InquiryPriceResponse> inquiryPriceResponses = JSONObject.parseArray(data, InquiryPriceResponse.class);
        InquiryPriceResponse priceResponse = CollectionUtil.getFirst(inquiryPriceResponses);
        if (priceResponse != null && CollectionUtil.isNotEmpty(priceResponse.getBillingPrices())) {
            List<BizBillingPriceVO> billingPrices = priceResponse.getBillingPrices();
            //待扣减价格
            BigDecimal subPrice = getSubPriceByBizBillingPriceVO(billingPrices);
            //结算账单,收支明细
            DurationBill durationBill = DurationBill.builder()
                    .account(bizBillingAccount)
                    .envAccount(cloudEnvAccount)
                    .cloudEnv(cloudEnv)
                    .duration(durHour)
                    .costs(costs)
                    .serviceOrder(serviceOrder)
                    .start(startTime)
                    .productType(resourceRef.getType())
                    .productName(ProductCodeEnum.getByCode(resourceRef.getType()) + " " + resourceRef.getType())
                    .intanceName(ProductCodeEnum.getByCode(resourceRef.getType()) + " " + resourceRef.getType())
                    .end(now)
                    .resourceRef(resourceRef)
                    .chargingType(serviceOrder.getChargingType())
                    .chargeType(serviceOrderDetail.getChargeType())
                    .now(now).build();

            for (BizBillingPriceVO billingPrice : billingPrices) {

                String priceType = billingPrice.getPriceType();
                if (PriceType.RESOURCE.equals(priceType) && CAPACITY.equals(billingPrice.getSpecType())) {
                    durationBill.setDiscountRate(billingPrice.getPlatformDiscount());
                    durationBill.setBillSpec(billingPrice.getBillingSpec());
                    //sfs租户用量信息
                    ResShare resShare = shareRemoteService.selectByPrimaryKey(Long.parseLong(resourceRef.getResourceId()));
                    if (resShare != null) {

                        durationBill.setUuid(resShare.getUuid());
                        //是否有扩缩容
                        ResChangeRecordParams resChangeRecordParams = new ResChangeRecordParams();
                        resChangeRecordParams.setCreatedDtGt(startTime);
                        resChangeRecordParams.setOrgSid(bizBillingAccount.getOrgSid());
                        resChangeRecordParams.setResType("HPC_SFS");
                        resChangeRecordParams.setResourceId(resShare.getId() + "");
                        resChangeRecordParams.setOrderByClause(" created_dt asc ");
                        List<ResChangeRecordDTO> resChangeRecordDTOS = resChangeRecordRemoteService.selectByParams(resChangeRecordParams);
                        if (!CollectionUtil.isEmpty(resChangeRecordDTOS)) {
                            //结算扩缩容前的容量
                            for (ResChangeRecordDTO resChangeRecordDTO : resChangeRecordDTOS) {
                                Date createdDt = resChangeRecordDTO.getCreatedDt();
                                if (!createdDt.after(now)) {
                                    durTime = (createdDt.getTime() - startTime.getTime()) / 1000 * 1000;
                                    durHour = this.millsToHours(durTime.toString(), "millisecond");

                                    Integer originalSize = Integer.valueOf(resChangeRecordDTO.getOriginalType());

                                    durationBill.setStart(startTime);
                                    durationBill.setEnd(createdDt);
                                    durationBill.setDuration(durHour);
                                    calcPriceAndGenCostByInquiry(durHour, subPrice, durationBill, billingPrice, originalSize, cashCoupons);
                                    startTime = createdDt;
                                }
                            }
                        }

                        Integer size = resShare.getSize();
                        durTime = (now.getTime() - startTime.getTime()) / 1000 * 1000;
                        durHour = this.millsToHours(durTime.toString(), "millisecond");

                        durationBill.setStart(startTime);
                        durationBill.setEnd(now);
                        durationBill.setDuration(durHour);

                        calcPriceAndGenCostByInquiry(durHour, subPrice, durationBill, billingPrice, size, cashCoupons);
                    }
                }
            }

        } else {
            log.info("BizAccountDealServiceImpl.sfsCostFromInquiry  InquiryPriceResponse is empty ");
        }
    }


    private BigDecimal getSubPriceByBizBillingPriceVO(List<BizBillingPriceVO> billingPrices) {
        return billingPrices.stream()
                .filter(o -> !PriceType.RESOURCE.equals(o.getPriceType()) && BigDecimal.ZERO.equals(
                        o.getOncePrice()))
                .map(BizBillingPriceVO::getHourPrice)
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

    }


    private ApplyServiceRequest createApplyServiceRequst(ServiceOrder serviceOrder,ServiceOrderDetail orderDetail, BizBillingAccount account) {
        ApplyServiceRequest applyServiceRequest = new ApplyServiceRequest();

        applyServiceRequest.setCouponId(null);
        applyServiceRequest.setProjectId(serviceOrder.getOrgSid());
        applyServiceRequest.setOrderType("apply-other_service");


        String serviceConfig = orderDetail.getServiceConfig();
        JSONObject data = JSONObject.parseObject(serviceConfig);
        Long cloudEnvId = data.getLong("cloudEnvId");

        List<ProductInfoVO> productInfoVOList = new ArrayList<>();
        ProductInfoVO productInfoVO = new ProductInfoVO();
        productInfoVOList.add(productInfoVO);
        productInfoVO.setBillingAccountId(account.getId());
        productInfoVO.setAmount(1);
        productInfoVO.setChargeType(ChargeTypeEnum.POSTPAID.getValue());
        productInfoVO.setServiceId(orderDetail.getServiceId());
        productInfoVO.setOrderId(serviceOrder.getId().toString());
        productInfoVO.setPeriod(BigDecimal.valueOf(1));
        productInfoVO.setPeriodUnit("1");
        productInfoVO.setCloudEnvId(cloudEnvId);
        productInfoVO.setProductCode(orderDetail.getServiceType());

        applyServiceRequest.setProductInfo(productInfoVOList);
        applyServiceRequest.setUserSid(account.getAdminSid());
        return applyServiceRequest;
    }


    /**
     * 从订单获取价格 计算
     *
     * @param serviceOrder
     * @param costs
     * @param cashCoupons
     * @param bizBillingAccount
     * @param orderDetail
     * @param cloudEnvAccount
     * @param cloudEnv
     * @param startTime
     * @param resourceRef
     * @param durHour
     * @param now
     */
    private void sfsCostFromOrder(ServiceOrder serviceOrder, List<InstanceGaapCost> costs, String distributorName, List<CashCoupon> cashCoupons,
                                  BizBillingAccount bizBillingAccount, ServiceOrderDetail orderDetail, CloudEnvAccount cloudEnvAccount,
                                  CloudEnv cloudEnv, Date startTime, ServiceOrderResourceRef resourceRef,
                                  BigDecimal durHour,
                                  Date now) {

        List<ServiceOrderPriceDetail> serviceOrderPriceDetails = getServiceOrderPriceDetails(orderDetail.getId());
        if (CollectionUtil.isNotEmpty(serviceOrderPriceDetails)) {
            Long durTime = (now.getTime() - startTime.getTime()) / 1000 * 1000;
            //待扣减价格
            BigDecimal subPrice = getSubPrice(serviceOrderPriceDetails);
            //结算账单,收支明细
            DurationBill durationBill = DurationBill.builder()
                    .distributorName(distributorName)
                    .account(bizBillingAccount)
                    .envAccount(cloudEnvAccount)
                    .cloudEnv(cloudEnv)
                    .duration(durHour)
                    .costs(costs)
                    .serviceOrder(serviceOrder)
                    .start(startTime)
                    .productType(orderDetail.getServiceType())
                    .chargeType(orderDetail.getChargeType())
                    .resourceRef(resourceRef)
                    .productName(ProductEnum.SFS.getProductName() + " "
                            + ProductEnum.SFS.getProductType())
                    .intanceName(ProductEnum.SFS.getProductName() + " "
                            + ProductEnum.SFS.getProductType())
                    .discountRate(orderDetail.getDiscountRatio())
                    .end(now)
                    .now(now).build();

            for (ServiceOrderPriceDetail serviceOrderPriceDetail : serviceOrderPriceDetails) {

                String priceType = serviceOrderPriceDetail.getPriceType();

                if (PriceType.RESOURCE.equals(priceType) && "capacity".equals(
                        serviceOrderPriceDetail.getSpecType())) {
                    durationBill.setBillSpec(serviceOrderPriceDetail.getBillingSpec());
                    //sfs租户用量信息
                    ResShare resShare = shareRemoteService.selectByPrimaryKey(
                            Long.parseLong(resourceRef.getResourceId()));
                    if (resShare != null) {

                        durationBill.setUuid(resShare.getUuid());
                        //是否有扩缩容
                        ResChangeRecordParams resChangeRecordParams = new ResChangeRecordParams();
                        resChangeRecordParams.setCreatedDtGt(startTime);
                        resChangeRecordParams.setOrgSid(bizBillingAccount.getOrgSid());
                        resChangeRecordParams.setResType("HPC_SFS");
                        resChangeRecordParams.setResourceId(resShare.getId() + "");
                        resChangeRecordParams.setOrderByClause(" created_dt asc ");
                        List<ResChangeRecordDTO> resChangeRecordDTOS = resChangeRecordRemoteService.selectByParams(
                                resChangeRecordParams);
                        if (!CollectionUtil.isEmpty(resChangeRecordDTOS)) {
                            //结算扩缩容前的容量
                            for (ResChangeRecordDTO resChangeRecordDTO : resChangeRecordDTOS) {

                                Date createdDt = resChangeRecordDTO.getCreatedDt();
                                if (!createdDt.after(now)) {
                                    durTime = (createdDt.getTime() - startTime.getTime()) / 1000 * 1000;
                                    durHour = this.millsToHours(durTime.toString(), "millisecond");

                                    Integer originalSize = Integer.valueOf(resChangeRecordDTO.getOriginalType());

                                    durationBill.setStart(startTime);
                                    durationBill.setEnd(createdDt);
                                    durationBill.setDuration(durHour);

                                    calcPriceAndGenCostByOrder(durHour, subPrice, durationBill, serviceOrderPriceDetail,
                                            originalSize, cashCoupons);
                                    startTime = createdDt;
                                }
                            }


                        }

                        Integer size = resShare.getSize();
                        durTime = (now.getTime() - startTime.getTime()) / 1000 * 1000;
                        durHour = this.millsToHours(durTime.toString(), "millisecond");

                        durationBill.setStart(startTime);
                        durationBill.setEnd(now);
                        durationBill.setDuration(durHour);

                        calcPriceAndGenCostByOrder(durHour, subPrice, durationBill, serviceOrderPriceDetail, size, cashCoupons);
                    }
                }
            }

        }
    }

    /**
     * 是否实时询价计算费用
     *
     * @return
     */
    private boolean checkPriceFromInquiry() {
        return StringUtils.equals("1", PropertiesUtil.getPropertyAndCache(SysConfigConstants.BILL_PREPAID_FROM_INQUIRYPRICE, "0", 30));
    }

    //根据订单计算价格并生成账单
    private void calcPriceAndGenCostByOrder(BigDecimal durHour, BigDecimal subPrice, DurationBill durationBill,
                                            ServiceOrderPriceDetail serviceOrderPriceDetail, Integer size, List<CashCoupon> cashCoupons) {

        BigDecimal fixedHourPrice =
                serviceOrderPriceDetail.getFixedHourPrice() != null ? serviceOrderPriceDetail.getFixedHourPrice() : BigDecimal.ZERO;
        BigDecimal unitHourPrice =
                serviceOrderPriceDetail.getUnitHourPrice() != null ? NumberUtil.sub(serviceOrderPriceDetail.getUnitHourPrice(), subPrice) :
                        BigDecimal.ZERO;


        //设置实时询价单价,折扣
        durationBill.setInquiryFixedHourPrice(fixedHourPrice);
        durationBill.setInquiryUnitHourPrice(unitHourPrice);
        durationBill.setInquiryDiscountRate(durationBill.getDiscountRate());

        calcPriceAndGenCost(durHour, durationBill, size, cashCoupons, fixedHourPrice, unitHourPrice);

    }

    //更具实时价格计算价格并生成账单
    private void calcPriceAndGenCostByInquiry(BigDecimal durHour, BigDecimal subPrice, DurationBill durationBill,
                                              BizBillingPriceVO priceVO, Integer size, List<CashCoupon> cashCoupons) {

        BigDecimal fixedHourPrice =
                priceVO.getFixedHourPrice() != null ? priceVO.getFixedHourPrice() : BigDecimal.ZERO;
        BigDecimal unitHourPrice =
                priceVO.getUnitHourPrice() != null ? NumberUtil.sub(priceVO.getUnitHourPrice(), subPrice) :
                        BigDecimal.ZERO;

        //设置实时询价单价,折扣
        durationBill.setInquiryFixedHourPrice(fixedHourPrice);
        durationBill.setInquiryUnitHourPrice(unitHourPrice);
        durationBill.setInquiryDiscountRate(durationBill.getDiscountRate());

        calcPriceAndGenCost(durHour, durationBill, size, cashCoupons, fixedHourPrice, unitHourPrice);

    }

    //计算价格并生成账单
    private void calcPriceAndGenCost(BigDecimal durHour, DurationBill durationBill, Integer size, List<CashCoupon> cashCoupons,
                                     BigDecimal fixedHourPrice, BigDecimal unitHourPrice) {


        BigDecimal totalTradePrice = NumberUtil.add(fixedHourPrice, NumberUtil.mul(unitHourPrice, size, durHour));

        BigDecimal discountRate =
                durationBill.getDiscountRate() != null ? durationBill.getDiscountRate() : BigDecimal.ONE;
        BigDecimal totalDiscount = NumberUtil.sub(totalTradePrice, NumberUtil.mul(totalTradePrice, discountRate));

        DurationBill newBillInfo = DurationBill.builder().build();
        BeanUtils.copyProperties(durationBill, newBillInfo);

        newBillInfo.setTradePrice(totalTradePrice);
        newBillInfo.setTotalDiscount(totalDiscount);
        newBillInfo.setCouponAmount(BigDecimal.ZERO);
        newBillInfo.setPriceType(PriceType.RESOURCE);
        newBillInfo.setBillSpec(durationBill.getBillSpec());
        newBillInfo.setChargeType(durationBill.getChargeType());

        newBillInfo.setUsageCount(size + "GB");

        addResourceCost(newBillInfo, cashCoupons);
    }


    /**
     * hpc退订记录mongoid
     *
     * @param cost 账单
     */
    private void recordHpcCostId(String actionType,InstanceGaapCost cost) {
        if (StringUtil.isBlank(actionType) && !"RELEASE".equals(actionType)){
            return;
        }
        String productCode = cost.getProductCode();
        String code = cost.getProductCode();
        if (Objects.nonNull(productCode) && Objects.nonNull(code)
                && (ProductCodeEnum.SFS.getProductCode().equalsIgnoreCase(code))) {
            String key = "HPC_RELEASE_REMARK_" + cost.getOwnerId();
            redisTemplate.opsForValue().set(key, cost.getMongoId(), 5, TimeUnit.SECONDS);
        }
    }


    @Data
    @Builder
    private static class DurationBill {

        //上次计费时间
        private Date start;
        //结算时间
        private Date end;
        //当前时间
        private Date now;
        private CloudEnvAccount envAccount;

        private CloudEnv cloudEnv;
        //账户
        private BizBillingAccount account;
        //订单详情
        private ServiceOrder serviceOrder;
        //时长，单位小时
        private BigDecimal duration;

        //最终价格
        private BigDecimal tradePrice = BigDecimal.ZERO;
        //收支明细
        private List<BizAccountDeal> deals;
        //账单
        private List<InstanceGaapCost> costs;
        //用量
        private String usageCount;
        //订单资源映射
        private ServiceOrderResourceRef resourceRef;
        //规格配置
        private String billSpec;
        //优惠券金额
        private BigDecimal couponAmount;
        //折扣
        private BigDecimal totalDiscount;
        //费用类型 ： 服务费用  配置费用  资源费用
        private String priceType;

        //描述
        private String description;
        //付费类型 预付费  后付费
        private String chargeType;

        private String uuid;

        private String productType;

        private String productName;

        private String intanceName;
        //折扣率
        private BigDecimal discountRate;
        /**
         * 有效算力(CUE)
         */
        private String cueValue;
        /**
         * 分销商名称
         */
        private String distributorName;

        private String chargingType;


        /**
         * 实时询价 基础价格
         */
        private BigDecimal inquiryFixedHourPrice;
        /**
         * 实时询价 单位价格
         */
        private BigDecimal inquiryUnitHourPrice;
        /**
         * 实时询价 折扣率
         */
        private BigDecimal inquiryDiscountRate;


    }

}
