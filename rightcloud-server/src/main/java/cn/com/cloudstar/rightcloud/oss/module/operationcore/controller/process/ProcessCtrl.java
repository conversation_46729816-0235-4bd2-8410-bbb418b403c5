/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.module.operationcore.controller.process;

import cn.com.cloudstar.rightcloud.common.constants.AuthModule;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.BG.BG04;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.ZF.ZF12;
import cn.com.cloudstar.rightcloud.common.constants.AuthModuleOss;
import cn.com.cloudstar.rightcloud.common.constants.AuthModuleOss.BZ.BZ01;
import cn.com.cloudstar.rightcloud.common.constants.AuthModuleOss.BZ.BZ02;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.constants.type.UserType;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.core.pojo.constant.ProcessTemplateStatus;
import cn.com.cloudstar.rightcloud.core.pojo.constant.ServiceProcessStatus;
import cn.com.cloudstar.rightcloud.core.pojo.dto.audit.ServiceOrder;
import cn.com.cloudstar.rightcloud.core.pojo.dto.audit.ServiceOrderRecord;
import cn.com.cloudstar.rightcloud.core.pojo.dto.process.Process;
import cn.com.cloudstar.rightcloud.core.pojo.dto.process.*;
import cn.com.cloudstar.rightcloud.core.pojo.dto.st.ProcessTemplate;
import cn.com.cloudstar.rightcloud.core.pojo.dto.st.ProcessTemplateDetail;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.Role;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.User;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.UserRole;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.operationlog.annotation.OperationLog;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.annotation.AuthorizeOss;
import cn.com.cloudstar.rightcloud.oss.common.annotation.DataPermission;
import cn.com.cloudstar.rightcloud.oss.common.annotation.RejectCall;
import cn.com.cloudstar.rightcloud.oss.common.api.ApiGroup;
import cn.com.cloudstar.rightcloud.oss.common.api.ApiGroupEnum;
import cn.com.cloudstar.rightcloud.oss.common.constants.ModuleTypeConstants;
import cn.com.cloudstar.rightcloud.oss.common.constants.RestConst;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.oss.common.encryptdata.encrypt.DesensitizationUtil;
import cn.com.cloudstar.rightcloud.oss.common.enums.BssRoleEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.NodeTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.oss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.oss.common.pojo.RestResult.Status;
import cn.com.cloudstar.rightcloud.oss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.annotation.ListenExpireBack;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.response.role.DescribeRoleResponse;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.response.user.DescribeUsersByRoleIdResponse;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.user.RoleMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.user.RoleModuleMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.user.UserMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.user.UserRoleMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.service.role.RoleService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.user.SysBssEntityService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.user.UserService;
import cn.com.cloudstar.rightcloud.oss.module.maintenance.service.templates.ProcessTemplateService;
import cn.com.cloudstar.rightcloud.oss.module.operationcore.activiti.util.ProcessConstants;
import cn.com.cloudstar.rightcloud.oss.module.operationcore.bean.process.request.*;
import cn.com.cloudstar.rightcloud.oss.module.operationcore.bean.process.response.*;
import cn.com.cloudstar.rightcloud.oss.module.operationcore.dao.process.ProcessNodeMapper;
import cn.com.cloudstar.rightcloud.oss.module.operationcore.dao.process.SysProcessBusinessMapper;
import cn.com.cloudstar.rightcloud.oss.module.operationcore.service.process.OrderRecordService;
import cn.com.cloudstar.rightcloud.oss.module.operationcore.service.process.ProcessMgtService;
import cn.com.cloudstar.rightcloud.oss.module.operationcore.service.process.ProcessService;
import cn.com.cloudstar.rightcloud.oss.module.operationcore.service.process.ServiceProcessService;
import cn.com.cloudstar.rightcloud.oss.module.order.dao.ServiceOrderMapper;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.seata.spring.annotation.GlobalTransactional;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.camunda.bpm.engine.task.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;

/**
 * 流程管理
 *
 * <AUTHOR>
 * @date 2018/8/2
 */
@ApiGroup(ApiGroupEnum.AUDIT_GROUP)
@Api(value = "/process", tags = "流程管理Ctrl")
@RestController
@RequestMapping("/process")
@Slf4j
public class ProcessCtrl {

    private static final List<Map<String, String>> BUSINESS = ProcessConstants.BUSINESS;
    private static final Map<String, String> BUSINESS_MAP = ProcessConstants.BUSINESS_MAP;

    /**
     * 01-通过；
     */
    private static final String OPER_TYPE_01 = "01";
    /**
     * 驳回
     */
    private static final String OPER_TYPE_02 = "02";
    /**
     * 拒绝
     */
    private static final String OPER_TYPE_03 = "03";
    private static final String BUILT_IN = "built-in";


    @Autowired
    private ProcessService processService;

    @Autowired
    private ProcessMgtService processMgtService;

    @Autowired
    private RoleService roleService;

    @Autowired
    private UserService userService;

    @Autowired
    private ProcessNodeMapper processNodeMapper;


    @Autowired
    private ProcessTemplateService processTemplateService;

    @Autowired
    private ServiceProcessService serviceProcessService;

    @Autowired
    private SysBssEntityService entityService;

    @Autowired
    private RoleModuleMapper roleModuleMapper;


    @Autowired
    private OrderRecordService orderRecordService;

    @Autowired
    private ServiceOrderMapper serviceOrderMapper;

    @Autowired
    private UserRoleMapper userRoleMapper;

    @Autowired
    private RoleMapper roleMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private ThreadPoolTaskExecutor cloudExecutor;

    @Autowired
    private SysProcessBusinessMapper sysProcessBusinessMapper;

    /**
     * 查询流程处理记录
     *
     * @param orderId 订单id
     * @return {@link ProcessHistoryRecordResponse}
     */
    @GetMapping("/record/{orderId}")
    @ApiOperation(httpMethod = "GET", value = "查询流程处理记录", notes = "查询流程处理记录")
    @AuthorizeOss(action = BG04.BG04)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderId", value = "订单编号", paramType = "body", dataType = "string", required = true)})
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "'查询流程处理记录'", param = "#params", resource = OperationResourceEnum.GET_HISTORY_RECORD, tagNameUs ="'Query Process Processing Record'")
    @ListenExpireBack
    public ProcessHistoryRecordResponse getHistoryRecord(
            @ApiParam(name = "orderId", value = "订单ID", required = true) @PathVariable String orderId) {

        // 租户侧不调用
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (ModuleTypeConstants.FROM_CONSOLE.equals(authUser.getRemark())) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
        }
        ProcessDto processDto = processService.selectByBusinessId(orderId);
        if (processDto == null) {
            Map<String, Object> result = new HashMap<>(4);
            result.put("process", new ProcessDto());
            result.put("records", Lists.newArrayList());

            return BeanConvertUtil.convert(result, ProcessHistoryRecordResponse.class);
        }
        List<ServiceOrderRecord> serviceOrderRecords = orderRecordService.selectByOrderSn(orderId);
        ServiceOrder serviceOrder = serviceOrderMapper.getById(serviceOrderRecords.get(0).getOrderId());
        if (Objects.nonNull(serviceOrder) && !serviceOrder.getEntityId().equals(RequestContextUtil.getEntityId())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1384912769));
        }
        Criteria criteria = new Criteria();
        criteria.put("orderSn", orderId);
        criteria.setOrderByClause("created_dt desc");
        List<ServiceOrderRecord> records = orderRecordService.selectByParams(criteria);
        Map<String, Object> result = new HashMap<>(4);
        result.put("process", processDto);
        result.put("records", serviceOrderRecords);
        result.put("showRecords", records);

        return BeanConvertUtil.convert(result, ProcessHistoryRecordResponse.class);
    }


    /**
     * [INNER API] 查询流程处理记录
     *
     * @param orderId 订单id
     * @return {@link ProcessHistoryRecordResponse}
     */
    @GetMapping("/record/feign/{orderId}")
    @ApiOperation(httpMethod = "GET", value = "查询流程处理记录", notes = "查询流程处理记录")
    @RejectCall
    @ListenExpireBack
    public ProcessHistoryRecordResponse getHistoryRecordByFeign(
            @ApiParam(name = "orderId", value = "订单ID", required = true) @PathVariable String orderId) {
        ProcessDto processDto = processService.selectByBusinessId(orderId);
        if (processDto == null) {
            Map<String, Object> result = new HashMap<>(4);
            result.put("process", new ProcessDto());
            result.put("records", Lists.newArrayList());

            return BeanConvertUtil.convert(result, ProcessHistoryRecordResponse.class);
        }
        Criteria criteria = new Criteria();
        criteria.put("orderSn", orderId);
        criteria.setOrderByClause("created_dt desc");
        List<ServiceOrderRecord> serviceOrderRecords = orderRecordService.selectByParams(criteria);
        Map<String, Object> result = new HashMap<>(4);
        result.put("process", processDto);
        result.put("records", serviceOrderRecords);

        return BeanConvertUtil.convert(result, ProcessHistoryRecordResponse.class);
    }


    /**
     * 【Since v2.5.0】流程定义列表
     *
     * @param condition 条件
     * @return {@link List}<{@link ProcessDefineResponse}>
     */
    @AuthorizeOss(action = BZ01.BZ01+","+BZ02.BZ0201)
    @GetMapping("/mgt/defines")
    @ApiOperation(httpMethod = "GET", value = "查询流程定义记录", notes = "查询流程定义记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "DescribeProcessDefineRequest", value = "流程定义查询", paramType = "body", dataType = "string", required = true)})
    @ListenExpireBack
    public List<ProcessDefineResponse> processDefines(DescribeProcessDefineRequest condition) {
        Process process = BeanConvertUtil.convert(condition, Process.class);
        Long entityId = RequestContextUtil.getEntityId();
        if (Objects.isNull(entityId) || entityId == 0) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1985222152));
        }
        process.setEntryId(entityId);
        process.setOrderByClause(WebUtil.getOrderByClause(condition.getSortdatafield(), condition.getSortorder(), null));
        List<Process> processes = processMgtService.selectProcessByParams(process);
        List<Process> result = processes.stream()
                                        .filter(this::processFilter)
                                        .peek(p -> {
                                            p.setBusinessName(BUSINESS_MAP.get(p.getBusinessCode()));
                                            if (StringUtils.isEmpty(p.getDescription())) {
                                                p.setDescription("--");
                                            }
                                            p.setAvailable(false);
                                            List<ProcessVersion> processVersions = processMgtService.versionList(
                                                    p.getId());
                                            if (CollectionUtil.isNotEmpty(processVersions)) {
                                                ProcessVersion processVersion = processVersions.get(0);
                                                List<ProcessNode> processNodes = processMgtService.deployedAuditNodeList(
                                                        processVersion.getProcessIdentify());
                                                boolean anyMatch = processNodes.stream()
                                                                               .anyMatch(node -> node.getNodeType()
                                                                                                     .equals(NodeTypeEnum.USERTASK
                                                                                                                     .getType()));
                                                if (anyMatch) {
                                                    p.setAvailable(true);
                                                }
                                            }
                                        }).collect(Collectors.toList());

        boolean isUs = WebUtil.getHeaderAcceptLanguage();
        List<String> codes = Arrays.asList("auto-approval", "service-apply", "built-in");
        if (CollectionUtil.isNotEmpty(result) && isUs) {
            result.forEach(e -> {
                e.setProcessName(this.toUs(e.getProcessName()));
                e.setDescription(this.toUs(e.getDescription()));
                if (codes.contains(e.getBusinessCode())) {
                    e.setBusinessName(e.getProcessName() + "[内置产品流程]");
                }
            });
        }

        return BeanConvertUtil.convert(result, ProcessDefineResponse.class);
    }

    private String toUs(String processName) {
        processName = processName
                .replaceAll("双重审核流程-非资源审批内置勿删", "Dual review process - Non resource approval built-in do not delete")
                .replaceAll("双重审核流程-资源审批内置勿删", "Dual review process - resource approval built-in do not delete")
                .replaceAll("双重审核流程-资源审批", "Dual review process - resource approval")
                .replaceAll("双重审核流程-非资源审批", "Dual review process - Non resource approval");
        return processName;
    }

    private boolean processFilter(Process process) {
        List<String> businessCode = new ArrayList<>();
        businessCode.add("product-process");
        businessCode.add("billing-process");
        businessCode.add("contract-process");
        businessCode.add("customer-management-process");
        businessCode.add("quota-process");
        businessCode.add("built-in");
        businessCode.add("service-apply");
        businessCode.add("auto-approval");
        return businessCode.stream()
                           .anyMatch(s -> StringUtils.equalsIgnoreCase(process.getBusinessCode(), s));
    }

    /**
     * 【Since v2.5.0】流程定义-审批节点列表
     *
     * @param processId 流程定义ID
     * @return {@link ProcessVersionResponse}
     */
    @AuthorizeOss(action = BZ01.BZ01200606)
    @GetMapping("/mgt/defines/{processId}/nodes")
    @ApiOperation(httpMethod = "GET", value = "流程定义-审批节点列表", notes = "流程定义-审批节点列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processId", value = "流程ID", paramType = "body", dataType = "long", required = true)})
//    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "'流程定义-审批节点列表'", param = "#params", resource = OperationResourceEnum.PROCESS_VERSION, tagNameUs ="'Process Definition'")
    @ListenExpireBack
    public ProcessVersionResponse processVersion(@PathVariable Long processId) {

        List<ProcessVersion> processVersions = processMgtService.versionList(processId);
        if (processVersions.size() < 1) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1366126529));
        }

        ProcessVersion processVersion = processVersions.get(0);

        Process process = processMgtService.selectByPrimaryKey(processId);
        if (Objects.isNull(process)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1366126529));
        }
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        Long proceEntityId = process.getEntryId();
        if (proceEntityId != null && authUser.getEntityId() != null && proceEntityId - authUser.getEntityId() != 0) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
        }

        List<ProcessNode> processNodes = processMgtService.auditNodeList(processVersion.getId());

        List<ProcessNodeDto> processNodeDtos = processNodes.stream().map(node -> {
            ProcessNodeDto processNodeDto = new ProcessNodeDto(node);

            processNodeDto.setNodeType(node.getNodeType());
            processNodeDto.setNodeTypeName(NodeTypeEnum.getEnumByType(node.getNodeType()).getValue());

            ProcessNodeConfig config = JSON.parseObject(node.getConfigData(), ProcessNodeConfig.class);
            processNodeDto.setConfigEnable(config.getConfigEnable());
            if (config.getCandidates() != null) {
                processNodeDto.setCandidates(config.getCandidates().stream()
                                                   .filter(c -> Objects.nonNull(c) && Objects.nonNull(c.getRefId()))
                                                   .collect(Collectors.toList()));
            }
            if (config.getCandidateThirds() != null) {
                processNodeDto.setCandidateThirds(config.getCandidateThirds().stream()
                                                        .filter(c -> Objects.nonNull(c) && Objects.nonNull(c.getRefId()))
                                                        .collect(Collectors.toList()));
            }
            processNodeDto.setMessageConfig(config.getMessageConfig());

            List<String> candidateNames = Lists.newArrayList();
            if (!CollectionUtils.isEmpty(processNodeDto.getCandidates())) {
                candidateNames.addAll(processNodeDto.getCandidates()
                                                    .stream().map(ProcessNodeRoleDto::getName).collect(Collectors.toList()));
            }
            if (!CollectionUtils.isEmpty(processNodeDto.getCandidateThirds())) {
                List<Long> userSids = processNodeDto.getCandidateThirds()
                                                    .stream()
                                                    .map(c -> Long.parseLong(c.getRefId()))
                                                    .collect(Collectors.toList());

                List<User> users = userService.findAllUsersWithoutAuth(new Criteria("userSidList", userSids));
                candidateNames.addAll(users.stream().map(User::getRealName).collect(Collectors.toList()));
                processNodeDto.setCandidateThirds(users.stream().map(user -> {
                    ProcessNodeRoleDto processNodeRoleDto = new ProcessNodeRoleDto();
                    processNodeRoleDto.setRefId(user.getUserSid().toString());
                    processNodeRoleDto.setName(user.getAccount());
                    return processNodeRoleDto;
                }).collect(Collectors.toList()));
            }

            processNodeDto.setCandidateName(String.join(",", candidateNames));
            processNodeDto.setCandidateAuditable(config.getCandidateAuditable());
            processNodeDto.setCandidateGoback(config.getCandidateGoback());
            processNodeDto.setApprovalWay(config.getApprovalWay());
            processNodeDto.setNotifyWays(config.getNotifyWays());
            processNodeDto.setEditableFormTemplates(config.getEditableFormTemplates());
            if (StringUtils.isNotBlank(config.getFormTemplate())) {
                ProcessTemplate processTemplate = processTemplateService.queryById(
                        Long.parseLong(config.getFormTemplate()));
                if (Objects.nonNull(processTemplate)) {
                    processNodeDto.setFormTemplateId(processTemplate.getId());
                    processNodeDto.setFormTemplateName(processTemplate.getTemplateName());
                }
            }

            return processNodeDto;
        }).collect(Collectors.toList());

        int nodNumber = 0;
        for (ProcessNodeDto nodeDto : processNodeDtos) {
            nodeDto.setNodeNumber(++nodNumber);
        }

        Map<String, Object> defineMap = Maps.newHashMapWithExpectedSize(2);
        // 审批节点
        defineMap.put("records", processNodeDtos);

        ProcessDto processDto = processService.selectByProcessMgtDefineId(processId);
        defineMap.put("process", processDto);

        defineMap.put("processStatus", process.getStatus());

        return BeanConvertUtil.convert(defineMap, ProcessVersionResponse.class);
    }

    /**
     * 【Since v2.5.0】当前发布的流程定义-审批节点列表
     *
     * @param processId 流程定义ID
     * @return {@link ProcessVersionResponse}
     */
    @AuthorizeOss(action = BZ01.BZ01200603)
    @GetMapping("/mgt/defines/{processId}/nodes/deployed")
    @ApiOperation(httpMethod = "GET", value = "流程定义-审批节点列表", notes = "流程定义-审批节点列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processId", value = "流程ID", paramType = "body", dataType = "long", required = true)})
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "'当前发布的流程定义-审批节点列表'", param = "#params", resource = OperationResourceEnum.PROCESS_VERSION_DEPLOYED, tagNameUs ="'Currently published process definition'")
    @ListenExpireBack
    public ProcessVersionResponse processVersionDeployed(@PathVariable Long processId) {

        List<ProcessVersion> processVersions = processMgtService.versionList(processId);
        if (processVersions.size() < 1) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1366126529));
        }

        ProcessVersion processVersion = processVersions.get(0);

        Process process = processMgtService.selectByPrimaryKey(processId);
        if (Objects.isNull(process)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1366126529));
        }

        List<ProcessNode> processNodes = processMgtService.deployedAuditNodeList(processVersion.getProcessIdentify());

        List<ProcessNodeDto> processNodeDtos = processNodes.stream().map(node -> {
            ProcessNodeDto processNodeDto = new ProcessNodeDto(node);

            processNodeDto.setNodeType(node.getNodeType());
            processNodeDto.setNodeTypeName(NodeTypeEnum.getEnumByType(node.getNodeType()).getValue());

            ProcessNodeConfig config = JSON.parseObject(node.getConfigData(), ProcessNodeConfig.class);
            processNodeDto.setConfigEnable(config.getConfigEnable());
            if (config.getCandidates() != null) {
                processNodeDto.setCandidates(config.getCandidates().stream()
                                                   .filter(c -> Objects.nonNull(c) && Objects.nonNull(c.getRefId()))
                                                   .collect(Collectors.toList()));
            }
            if (config.getCandidateThirds() != null) {
                processNodeDto.setCandidateThirds(config.getCandidateThirds().stream()
                                                        .filter(c -> Objects.nonNull(c) && Objects.nonNull(c.getRefId()))
                                                        .collect(Collectors.toList()));
            }
            processNodeDto.setMessageConfig(config.getMessageConfig());

            List<String> candidateNames = Lists.newArrayList();
            if (!CollectionUtils.isEmpty(processNodeDto.getCandidates())) {
                candidateNames.addAll(processNodeDto.getCandidates()
                                                    .stream().map(ProcessNodeRoleDto::getName).collect(Collectors.toList()));
            }
            if (!CollectionUtils.isEmpty(processNodeDto.getCandidateThirds())) {
                List<Long> userSids = processNodeDto.getCandidateThirds()
                                                    .stream()
                                                    .map(c -> Long.parseLong(c.getRefId()))
                                                    .collect(Collectors.toList());

                List<User> users = userService.findAllUsersWithoutAuth(new Criteria("userSidList", userSids));
                candidateNames.addAll(users.stream().map(User::getRealName).collect(Collectors.toList()));
                processNodeDto.setCandidateThirds(users.stream().map(user -> {
                    ProcessNodeRoleDto processNodeRoleDto = new ProcessNodeRoleDto();
                    processNodeRoleDto.setRefId(user.getUserSid().toString());
                    processNodeRoleDto.setName(user.getRealName());
                    return processNodeRoleDto;
                }).collect(Collectors.toList()));
            }

            processNodeDto.setCandidateName(String.join(",", candidateNames));
            processNodeDto.setCandidateAuditable(config.getCandidateAuditable());
            processNodeDto.setCandidateGoback(config.getCandidateGoback());
            processNodeDto.setApprovalWay(config.getApprovalWay());
            processNodeDto.setNotifyWays(config.getNotifyWays());
            processNodeDto.setEditableFormTemplates(config.getEditableFormTemplates());
            if (StringUtils.isNotBlank(config.getFormTemplate())) {
                ProcessTemplate processTemplate = processTemplateService.queryById(
                        Long.parseLong(config.getFormTemplate()));
                if (Objects.nonNull(processTemplate)) {
                    processNodeDto.setFormTemplateId(processTemplate.getId());
                    processNodeDto.setFormTemplateName(processTemplate.getTemplateName());
                }
            }

            return processNodeDto;
        }).collect(Collectors.toList());

        int nodNumber = 0;
        for (ProcessNodeDto nodeDto : processNodeDtos) {
            nodeDto.setNodeNumber(++nodNumber);
        }

        Map<String, Object> defineMap = Maps.newHashMapWithExpectedSize(2);
        // 审批节点
        defineMap.put("records", processNodeDtos);

        ProcessDto processDto = processService.selectByProcessMgtDefineId(processId);
        defineMap.put("process", processDto);

        defineMap.put("processStatus", process.getStatus());

        return BeanConvertUtil.convert(defineMap, ProcessVersionResponse.class);
    }

    /**
     * 【Since v2.5.0】节点子节点
     *
     * @param nodeId 节点ID
     * @return {@link RestResult}
     */
    @GetMapping("/mgt/nodes/{nodeId}/childs")
    @ApiOperation(httpMethod = "GET", value = "流程定义-审批子节点列表", notes = "流程定义-审批子节点列表")
    @ListenExpireBack
    public RestResult nodeChilds(@PathVariable Long nodeId) {
        Criteria childCriteria = new Criteria("parentId", nodeId);
        childCriteria.put("status", 3);
        List<ProcessNode> childNodes = processNodeMapper.selectByParams(childCriteria);

        Criteria criteria = new Criteria();
        criteria.put("type", "custom");
        criteria.put("statusNotEqual", ProcessTemplateStatus.DELETED);

        List<ProcessNodeDto> processNodeDtos = childNodes.stream().map(node -> {
            ProcessNodeDto processNodeDto = new ProcessNodeDto(node);

            processNodeDto.setNodeType(node.getNodeType());
            processNodeDto.setNodeTypeName(NodeTypeEnum.getEnumByType(node.getNodeType()).getValue());
            processNodeDto.setSortNum(node.getSortNum());

            ProcessNodeConfig config = JSON.parseObject(node.getConfigData(), ProcessNodeConfig.class);
            processNodeDto.setConfigEnable(config.getConfigEnable());
            if (config.getCandidates() != null) {
                processNodeDto.setCandidates(config.getCandidates()
                                                   .stream()
                                                   .filter(c -> Objects.nonNull(c) && Objects.nonNull(c.getRefId()))
                                                   .collect(Collectors.toList()));
            }
            if (config.getCandidateThirds() != null) {
                processNodeDto.setCandidateThirds(config.getCandidateThirds()
                                                        .stream()
                                                        .filter(c -> Objects.nonNull(c) && Objects.nonNull(c.getRefId()))
                                                        .collect(Collectors.toList()));
            }
            processNodeDto.setMessageConfig(config.getMessageConfig());
//            if (Objects.nonNull(config.getOrgSid())) {
//                processNodeDto.setOrgSid(config.getOrgSid());
//            } else {
//                processNodeDto.setOrgSid(compayId);
//            }
//
//            Org org = orgService.selectByPrimaryKey(
//                    processNodeDto.getOrgSid());
//            if (Objects.nonNull(org)) {
//                processNodeDto.setOrgName(org.getOrgName());
//            }
//
//            if (StringUtils.isNotBlank(config.getApprovalLocation())) {
//                processNodeDto.setApprovalLocation(
//                        config.getApprovalLocation());
//            } else {
//                processNodeDto.setApprovalLocation(
//                        cn.com.cloudstar.rightcloud.operationcore.activiti.util.ProcessConstants.APPROVAL_LOCATION_CURRENT);
//            }

            List<String> candidateNames = Lists.newArrayList();
            if (!CollectionUtils.isEmpty(processNodeDto.getCandidates())) {
                candidateNames.addAll(processNodeDto.getCandidates()
                                                    .stream()
                                                    .map(ProcessNodeRoleDto::getName)
                                                    .collect(
                                                            Collectors.toList()));
            }
            if (!CollectionUtils.isEmpty(processNodeDto.getCandidateThirds())) {
                List<Long> userSids = processNodeDto.getCandidateThirds()
                                                    .stream()
                                                    .map(c -> Long.parseLong(
                                                            c.getRefId()))
                                                    .collect(
                                                            Collectors.toList());

                List<User> users = userService.findAllUsersWithoutAuth(new Criteria("userSidList", userSids));
                candidateNames.addAll(users.stream()
                                           .map(User::getRealName)
                                           .collect(Collectors.toList()));
                processNodeDto.setCandidateThirds(users.stream()
                                                       .map(user -> {
                                                           ProcessNodeRoleDto processNodeRoleDto = new ProcessNodeRoleDto();
                                                           processNodeRoleDto.setRefId(user.getUserSid().toString());
                                                           processNodeRoleDto.setName(user.getRealName());
                                                           return processNodeRoleDto;
                                                       })
                                                       .collect(Collectors.toList())
                );
            }

            processNodeDto.setCandidateName(String.join(",", candidateNames));
            processNodeDto.setNotifyWays(config.getNotifyWays());
            processNodeDto.setCandidateAuditable(config.getCandidateAuditable());

            processNodeDto.setCandidateGoback(config.getCandidateGoback());
            processNodeDto.setApprovalWay(config.getApprovalWay());

            processNodeDto.setEditableFormTemplates(config.getEditableFormTemplates());
            if (StringUtils.isNotBlank(config.getFormTemplate())) {
                ProcessTemplate processTemplate = processTemplateService.queryById(
                        Long.parseLong(config.getFormTemplate()));
                if (Objects.nonNull(processTemplate)) {
                    processNodeDto.setFormTemplateId(processTemplate.getId());
                    processNodeDto.setFormTemplateName(processTemplate.getTemplateName());
                }
            }

            return processNodeDto;
        }).collect(Collectors.toList());

        List<Map<String, Object>> groupNodeList = Lists.newArrayList();

        Map<Long, List<ProcessNodeDto>> nodeGroupMap = processNodeDtos.stream()
                                                                      .collect(Collectors.groupingBy(ProcessNodeDto::getNodeGroup));

        Set<Entry<Long, List<ProcessNodeDto>>> entries = nodeGroupMap.entrySet();
        for (Map.Entry<Long, List<ProcessNodeDto>> entry : entries) {
            Long groupId = entry.getKey();
            Map<String, Object> group = Maps.newHashMap();
            group.put("nodeGroup", groupId);
            List<ProcessNodeDto> nodes = entry.getValue();

            nodes.sort((a, b) -> {
                if (a.getSortNum().equals(b.getSortNum())) {
                    return 0;
                } else {
                    return a.getSortNum() > b.getSortNum() ? 1 : -1;
                }
            });

            group.put("nodeGroupNome", nodes.get(0).getNodeGroupName());
            group.put("childNodes", nodes);

            groupNodeList.add(group);
        }

        return new RestResult(groupNodeList);
    }

    /**
     * 【Since v2.5.0】流程定义发布
     *
     * @param processId 流程定义ID
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = BZ01.BZ01200602)
    @ApiOperation(httpMethod = "POST", value = "流程定义发布", notes = "流程定义发布")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processId", value = "流程ID", paramType = "body", dataType = "long", required = true)})
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'流程发布'", param = "#params", resource = OperationResourceEnum.PROCESS_DEPLOY,bizId = "#processId", tagNameUs ="'Process Release'")
    @PostMapping("/mgt/defines/{processId}/deploy")
    @ListenExpireBack
    public RestResult updateProcessDeploy(@PathVariable Long processId) {
        cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser authUser = RequestContextUtil.getAuthUserInfo(
                WebUtil.getRequest());

        processMgtService.deployProcessDefine(processId, authUser);

        return new RestResult();
    }

    /**
     * 【Since v2.5.0】添加流程定义
     *
     * @param createProcessRequest 流程定义
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = BZ01.BZ012002)
    @PostMapping("/mgt/defines")
    @ApiOperation(httpMethod = "POST", value = "添加流程定义", notes = "添加流程定义")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "createProcessRequest", value = "流程申请对象", paramType = "body", dataType = "string", required = true)})
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "'创建流程定义'", param = "#createProcessRequest", resource = OperationResourceEnum.PROCESS_DEFINES, tagNameUs ="'Create Process Definition'")
    @ListenExpireBack
    public RestResult saveProcess(
            @Validated @RequestBody CreateProcessRequest createProcessRequest) {
        if (!BUSINESS_MAP.containsKey(createProcessRequest.getBusinessCode())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1855433262));
        }

        Process process = BeanConvertUtil.convert(createProcessRequest, Process.class);

        cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser authUser = RequestContextUtil.getAuthUserInfo(
                WebUtil.getRequest());
        try {
            processMgtService.createProcessDefine(process, authUser);
        } catch (Exception e) {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERROR_INSERT_FAILURE));
        }

        return new RestResult().setData(process.getId());
    }

    /**
     * 【Since v2.5.0】克隆流程定义
     *
     * @param processId 流程定义ID
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = BZ01.BZ012004)
    @ApiOperation(httpMethod = "POST", value = "克隆流程定义", notes = "克隆流程定义")
    @PostMapping("/mgt/defines/{processId}/clone")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processId", value = "流程ID", paramType = "body", dataType = "long", required = true)})
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "'克隆流程定义'", bizId = "#processId", param = "#params", resource = OperationResourceEnum.CLONE_PROCESS, tagNameUs ="'Clone Process Definition'")
    @ListenExpireBack
    public RestResult cloneProcess(@PathVariable Long processId) {
        cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser authUser = RequestContextUtil.getAuthUserInfo(
                WebUtil.getRequest());

        Process process = processMgtService.selectByPrimaryKey(processId);
        int nameLength=255;
        if (Objects.isNull(process)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_230928958));
        }

        Long proceEntityId = process.getEntryId();
        if (proceEntityId != null && authUser.getEntityId() != null && proceEntityId - authUser.getEntityId() != 0) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
        }

        boolean isUs = WebUtil.getHeaderAcceptLanguage();
        String name = process.getProcessName() + "-" + (isUs ? "copy" : "复制") + "—" + System.currentTimeMillis()/1000;
        if (nameLength < name.length()){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2091470371));
        }

        process.setProcessName(name);

        processMgtService.cloneProcess(process, authUser);

        return new RestResult();
    }

    /**
     * 【Since v2.5.0】更新流程定义信息
     *
     * @param processId            进程ID
     * @param updateProcessRequest 更新过程请求
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = BZ01.BZ012003)
    @ApiOperation(httpMethod = "POST", value = "更新流程定义信息", notes = "更新流程定义信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processId", value = "流程定义ID", paramType = "path", dataType = "Long"),
    })
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'更新流程定义信息'", param = "#params", resource = OperationResourceEnum.UPDATE_PROCESS,bizId = "#processId", tagNameUs ="'Update Process Definition Information'")
    @PutMapping("/mgt/defines/{processId}")
    @ListenExpireBack
    public RestResult updateProcess(@PathVariable Long processId,
                                    @Validated @RequestBody UpdateProcessRequest updateProcessRequest) {

        Process process = BeanConvertUtil.convert(updateProcessRequest, Process.class);
        cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser authUser = RequestContextUtil.getAuthUserInfo(
                WebUtil.getRequest());
        processMgtService.updateProcessDefine(processId, process, authUser);

        return new RestResult().setData(processId);
    }

    /**
     * 【Since v2.5.0】删除流程定义
     *
     * @param processId 进程ID
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = BZ01.BZ012005)
    @ApiOperation(httpMethod = "DELETE", value = "删除流程定义", notes = "删除流程定义")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processId", value = "流程定义ID", paramType = "path", dataType = "Long"),
    })
    @OperationLog(type = OperationTypeEnum.DELETE, tagName = "'删除流程定义'", bizId = "#processId", param = "#params", resource = OperationResourceEnum.DELETE_PROCESS, tagNameUs ="'Delete Process Definition'")
    @DeleteMapping("/mgt/defines/{processId}")
    @ListenExpireBack
    public RestResult deleteProcess(@PathVariable Long processId) {
        cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser authUser = RequestContextUtil.getAuthUserInfo(
                WebUtil.getRequest());
        Process processDelete = processMgtService.selectByPrimaryKey(processId);
        if (processDelete == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_408194781));
        }
        if (BUILT_IN.equals(processDelete.getProcessCode())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }

        Long proceEntityId = processDelete.getEntryId();
        if (proceEntityId != null && authUser.getEntityId() != null && proceEntityId - authUser.getEntityId() != 0) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
        }

        Criteria criteria = new Criteria();
        criteria.put("name",processDelete.getProcessName());
        List<ServiceProcess> serviceProcesses = serviceProcessService.queryByCriteria(criteria);
        boolean anyMatch = serviceProcesses.stream()
                                           .anyMatch(serviceProcess -> org.apache.commons.lang3.StringUtils.equalsIgnoreCase(
                                                   serviceProcess.getStatus(),
                                                   ServiceProcessStatus.PROCESS));
        if (anyMatch){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1730214761));
        }
        processMgtService.deleteProcessDefine(processDelete, authUser);
        return new RestResult().setData(processId);
    }

    /**
     * 【Since v2.5.0】添加流程定义审核节点
     *
     * @param processId     流程定义ID
     * @param createRequest 流程节点信息
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = BZ01.BZ012101)
    @ApiOperation(httpMethod = "POST", value = "添加流程定义审核节点", notes = "添加流程定义审核节点")
    @PostMapping("/mgt/defines/{processId}/nodes")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "processId", value = "流程ID", paramType = "body", dataType = "long", required = true)})
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "'添加流程节点'", bizId = "#processId", param = "#params", resource = OperationResourceEnum.UPDATE_APPLY_NODE, tagNameUs ="'Add Process Node'")
    @ListenExpireBack
    public RestResult addApplyNode(@ApiParam(value = "流程定义ID", required = true) @PathVariable Long processId,
                                   @Valid @RequestBody CreateProcessNodeRequest createRequest) {
        ProcessNodeAddDto addDto = BeanConvertUtil.convert(createRequest, ProcessNodeAddDto.class);
        cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser authUser = RequestContextUtil.getAuthUserInfo(
                WebUtil.getRequest());
        if(addDto.getCandidates() != null){
            addDto.getCandidates().stream().forEach(r->{
                if(!BssRoleEnum.OPERATION_ADMIN.getRoleSid().toString().equals(r.getRefId())){
                    throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
                }
            });

        }
        if(addDto.getCandidateThirds() != null){
            addDto.getCandidateThirds().stream().forEach(u->{
                List<Role>  roles = roleService.findRolesByUserSid(Long.parseLong(u.getRefId()));
                if(roles != null){
                    List<Long>  roleIds = roles.stream().map(Role::getRoleSid).collect(Collectors.toList());
                    if(!roleIds.contains(BssRoleEnum.OPERATION_ADMIN.getRoleSid())){
                        throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
                    }
                }



            });

        }
        Long nodeId = processMgtService.addProcessNode(processId, addDto, authUser);
        return new RestResult().setData(nodeId);
    }

    /**
     * 【Since v2.5.0】修改审核节点信息
     *
     * @param id                       节点ID
     * @param updateProcessNodeRequest 节点信息
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = BZ01.BZ01200604)
    @ApiOperation(httpMethod = "PUT", value = "修改审核节点信息", notes = "修改审核节点信息")
    @PutMapping("/mgt/defines/nodes/{id}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "updateProcessNodeRequest", value = "节点信息参数", paramType = "path", dataType = "string"),
    })
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'修改审核节点信息'", bizId = "#id", param = "#params", resource = OperationResourceEnum.UPDATE_NODE_TEMPLATE_DETAIL, tagNameUs ="'Modify Audit Node Information'")
    @ListenExpireBack
    public RestResult updateApplyNode(@PathVariable Long id,
                                      @Validated @RequestBody UpdateProcessNodeRequest updateProcessNodeRequest) {
        ProcessNodeEditDto editDto = BeanConvertUtil.convert(updateProcessNodeRequest, ProcessNodeEditDto.class);
        cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser authUser = RequestContextUtil.getAuthUserInfo(
                WebUtil.getRequest());
        if(editDto.getCandidates() != null){
            editDto.getCandidates().stream().forEach(r->{
                if(!BssRoleEnum.OPERATION_ADMIN.getRoleSid().toString().equals(r.getRefId())){
                    throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
                }
            });

        }
        if(editDto.getCandidateThirds() != null){
            editDto.getCandidateThirds().stream().forEach(u->{
                List<Role>  roles = roleService.findRolesByUserSid(Long.parseLong(u.getRefId()));
                if(roles != null){
                    List<Long>  roleIds = roles.stream().map(Role::getRoleSid).collect(Collectors.toList());
                    if(!roleIds.contains(BssRoleEnum.OPERATION_ADMIN.getRoleSid())){
                        throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
                    }
                }



            });

        }
        processMgtService.saveProcessNodeConfig(id, editDto, authUser);

        return new RestResult().setData(id);
    }

    /**
     * 【Since v2.5.0】审核节点上移
     *
     * @param id 节点ID
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "POST", value = "审核节点上移", notes = "审核节点上移")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "节点ID", paramType = "path", dataType = "Long"),
    })
    @ListenExpireBack
    public RestResult nodeUp(@PathVariable Long id) {
        cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser authUser = RequestContextUtil.getAuthUserInfo(
                WebUtil.getRequest());
        processMgtService.processNodeUp(id, authUser);
        return new RestResult().setData(id);
    }

    /**
     * 【Since v2.5.0】审核子节点上移
     *
     * @param id 节子点ID
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "POST", value = "审核子节点上移", notes = "审核子节点上移")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "节点ID", paramType = "path", dataType = "Long"),
    })
    @PostMapping("/mgt/nodes/child/{id}/up")
    @ListenExpireBack
    public RestResult childNodeUp(@PathVariable Long id) {
        cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser authUser = RequestContextUtil.getAuthUserInfo(
                WebUtil.getRequest());
        ProcessNode processNode = processNodeMapper.selectByPrimaryKey(id);
        if (Objects.isNull(processNode)) {
            throw new BizException("节点未找到！");
        }

        processMgtService.processNodeChildUp(id, authUser);

        return new RestResult().setData(id);
    }

    /**
     * 【Since v2.5.0】审核节点下移
     *
     * @param id 节点ID
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "POST", value = "审核节点下移", notes = "审核节点下移")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "节点ID", paramType = "path", dataType = "Long"),
    })
    @ListenExpireBack
    public RestResult nodeDown(@PathVariable Long id) {
        cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser authUser = RequestContextUtil.getAuthUserInfo(
                WebUtil.getRequest());
        processMgtService.processNodeDown(id, authUser);
        return new RestResult().setData(id);
    }

    /**
     * 【Since v2.5.0】审核子节点下移
     *
     * @param id 节子点ID
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "POST", value = "审核子节点下移", notes = "审核子节点下移")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "节点ID", paramType = "path", dataType = "Long"),
    })
    @PostMapping("/mgt/nodes/child/{id}/down")
    @ListenExpireBack
    public RestResult childNodeDown(@PathVariable Long id) {
        cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser authUser = RequestContextUtil.getAuthUserInfo(
                WebUtil.getRequest());
        ProcessNode processNode = processNodeMapper.selectByPrimaryKey(id);
        if (Objects.isNull(processNode)) {
            throw new BizException("节点未找到！");
        }

        processMgtService.processNodeChildDown(id, authUser);

        return new RestResult().setData(id);
    }

    /**
     * 【Since v2.5.0】删除审核节点
     *
     * @param id 节点ID
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = BZ01.BZ01200605)
    @ApiOperation(httpMethod = "DELETE", value = "删除审核节点", notes = "删除审核节点")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "节点ID", paramType = "path", dataType = "Long"),
    })
    @DeleteMapping("/mgt/defines/nodes/{id}")
    @ListenExpireBack
    @OperationLog(type = OperationTypeEnum.DELETE, tagName = "'删除审核节点信息'", bizId = "#id", param = "#id", resource = OperationResourceEnum.DELETE_APPLY_NODE, tagNameUs ="'Delete Audit Node Information'")
    public RestResult deleteApplyNode(@PathVariable Long id) {
        cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser authUser = RequestContextUtil.getAuthUserInfo(
                WebUtil.getRequest());
        processMgtService.deleteProcessNode(id, authUser);
        return new RestResult().setData(id);
    }

    /**
     * 【Since v2.5.0】审批节点候选角色列表
     *
     * @return 审批节点候选角色列表
     */
    @AuthorizeOss(action = BZ01.BZ012006)
    @ApiOperation(httpMethod = "GET", value = "审批节点候选角色列表", notes = "审批节点候选角色列表")
    @GetMapping("/mgt/defines/nodes/roles")
    @ListenExpireBack
    public List<DescribeRoleResponse> nodeRoles() {
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (authUser == null) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }


        Criteria criteria = new Criteria("userSid", authUser.getUserSid());
        List<UserRole> userRole = userRoleMapper.selectRoleByUserSid(criteria);
        if (CollectionUtil.isEmpty(userRole) || (userRole.size() == 1 && userRole.get(0).getRoleSid() - 304 == 0)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105998536));
        }
        //BG03为流程审批
        List<Role> roles = roleMapper.selectRoleByModuleSid("BG03")
                .stream()
                .filter(role -> role.getRoleSid() != 306L)
                .collect(Collectors.toList());
        return BeanConvertUtil.convert(roles, DescribeRoleResponse.class);
    }

    /**
     * 【Since v2.5.0】审批节点消息发送元数据
     *
     * @return {@link ProcessAuditMessageMetaResponse}
     */
    @AuthorizeOss(action = BZ01.BZ012006)
    @ApiOperation(httpMethod = "GET", value = "审批节点消息发送元数据", notes = "审批节点消息发送元数据")
    @GetMapping("/mgt/defines/nodes/messages/meta")
    @ListenExpireBack
    public ProcessAuditMessageMetaResponse auditMessageMeta() {
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (authUser == null) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }

        Criteria criteria = new Criteria("userSid", authUser.getUserSid());
        List<UserRole> userRole = userRoleMapper.selectRoleByUserSid(criteria);
        if (CollectionUtil.isEmpty(userRole) || (userRole.size() == 1 && userRole.get(0).getRoleSid() - 304 == 0)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105998536));
        }

        Map<String, Object> messageMeta = Maps.newHashMap();

        List<MessageReceiverDto> receiverDtos = Lists.newArrayList();
        receiverDtos.add(new MessageReceiverDto("applicant", "申请人"));
        receiverDtos.add(new MessageReceiverDto("assignee", "处理人"));

        List<MessageTypeDto> typeDtos = Lists.newArrayList();
        typeDtos.add(new MessageTypeDto("mail", "邮件"));
        typeDtos.add(new MessageTypeDto("sms", "短信"));
        typeDtos.add(new MessageTypeDto("station", "站内消息"));

        messageMeta.put("receivers", receiverDtos);
        messageMeta.put("sendTypes", typeDtos);

        return BeanConvertUtil.convert(messageMeta, ProcessAuditMessageMetaResponse.class);
    }


    /**
     * 【Since v2.5.0】审批流程
     *
     * @param request approveOrderRequest
     * @return {@link RestResult}
     */
    @PutMapping("/approval")
    @GlobalTransactional
    @AuthorizeOss(action = AuthModuleOss.BG.BG03.BG030202)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "request", value = "审批参数", paramType = "body", dataType = "string", required = true)})
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'审批流程'", param = "#params", resource = OperationResourceEnum.APPROVE_ORDER, bizId = "#request.serviceProcessId", tagNameUs ="'Approval Process'")

    @ApiOperation(httpMethod = "PUT", value = "审批流程", notes = "审批流程")
    @ListenExpireBack
    public RestResult approvalProcess(@Validated @RequestBody ApproveProcessRequest request) {

        cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser authUser =RequestContextUtil.getAuthUserInfo(WebUtil.getRequest());
        Task task = processService.checkApprovalProcess(request, authUser);

        Objects.requireNonNull(authUser);
        Map<String, Object> variables = new HashMap<>();
        variables.put("auditAdvice",request.getApproveAdvice());
        variables.put("_audit_uname",authUser.getAccount());

        if (OPER_TYPE_01.equals(request.getApproveType())) {
            ProcessTemplateDetail processTemplateDetail = processService.handleTemplateContent(task, request);
            processService.processMgtTaskPass(task,variables,request,processTemplateDetail.getTemplateContent());
        } else {
            ProcessNode processNode = processNodeMapper.selectByPrimaryKey(request.getNodeId());
            if (Objects.isNull(processNode)){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_456947453));
            }
            if (processNode.getNodeType().equals(NodeTypeEnum.APPLYTASK.getType())){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_889360627));
            }
            if (OPER_TYPE_02.equals(request.getApproveType())){
                if (StringUtils.isEmpty(request.getGoback())){
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_481504184));
                }
                processService.taskGoback(authUser.getUserSid().toString(), task,variables, request.getGoback());
            } else {
                processService.taskReject(task,variables);
            }
        }

        processMgtService.verifyAndCreate(request);

        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_723832711), request.getNodeId());
    }

    /**
     * 【Since v2.5.0】获取流程实例当前和历史审批节点
     *
     * @param serviceProcessId 服务进程ID
     * @param moduleType       模块类型
     * @return List<AuditProcessNodeResponse>
     */
    @AuthorizeOss(action = BZ01.BZ01200606 + "," + AuthModuleOss.BG.BG03.CB2009 + "," + AuthModuleOss.BZ.BZ02.BZ0202)
    @ApiOperation(httpMethod = "GET", value = "获取流程实例当前和历史审批节点")
    @GetMapping("/{serviceProcessId}/not/approval/nodes")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "serviceProcessId", value = "服务流程ID", paramType = "path", dataType = "String"),
    })
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "'获取流程实例当前和历史审批节点'", param = "#params", resource = OperationResourceEnum.PROCESS_VERSION, tagNameUs ="'Get the current and historical approval nodes of the process instance'")
    @ListenExpireBack
    public List<AuditProcessNodeResponse> getCurrentAndHistoryAuditNode(@PathVariable String serviceProcessId, @RequestHeader String moduleType) {
        return processService.getCurrentAndHistoryAuditNode(serviceProcessId, moduleType);
    }

    /**
     * 【Since v2.5.0】获取流程节点模板内容
     *
     * @param serviceProcessId 服务进程ID
     * @param nodeId           节点ID
     * @return List<ProcessTemplateDetail>
     */
    @AuthorizeOss(action = BZ01.BZ012101)
    @GetMapping("/{serviceProcessId}/{nodeId}/template/detail")
    @ApiOperation(httpMethod = "GET", value = "获取流程节点模板内容")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "serviceProcessId", value = "服务流程ID", paramType = "path", dataType = "String"),
    })
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "'获取流程节点模板内容'", param = "#params", resource = OperationResourceEnum.NODE_TEMPLATE_DETAIL)
    @ListenExpireBack
    public List<ProcessTemplateDetail> getNodeTemplateDetail(@PathVariable String serviceProcessId,
                                                             @PathVariable Long nodeId){
        return processService.getNodeTemplateDetail(serviceProcessId,nodeId);
    }

    /**
     * 【Since v2.5.0】修改流程节点模板内容
     * <Br> 该接口无使用，被弃用删除
     * <Br>接口url: /template/detail
     * @param request 请求
     * @return Integer
     */
    @Deprecated
    @AuthorizeOss(action = BZ01.BZ012101)
    @PutMapping("/template/detail")
    @ApiOperation(httpMethod = "GET", value = "修改流程节点模板内容")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "updateProcessTemplateDetailRequest", value = "模板内容", paramType = "path", dataType = "String"),
    })
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'修改流程节点模板内容'", param = "#params", resource = OperationResourceEnum.UPDATE_NODE_TEMPLATE_DETAIL, tagNameUs ="'Modify process node template content'")
    @ListenExpireBack
    public Integer updateNodeTemplateDetail(@Validated @RequestBody UpdateProcessTemplateDetailRequest request){
        ProcessTemplateDetail templateDetail = BeanConvertUtil.convert(request, ProcessTemplateDetail.class);
        return processService.updateNodeTemplateDetail(templateDetail);
    }

    /**
     * 【Since v2.5.0】获取用户
     *
     * @param describeUsersRequest 描述用户请求
     * @return {@link List}<{@link DescribeUsersByRoleIdResponse}>
     */
    @ApiOperation(httpMethod = "GET", value = "查询角色关联的用户")
    @GetMapping("/users")
    @AuthorizeOss(action = BZ01.BZ012006)
    public List<DescribeUsersByRoleIdResponse> findUsers(DescribeUsersRequest describeUsersRequest) {
        if (CollectionUtils.isEmpty(describeUsersRequest.getRoleIds())){
            return Collections.emptyList();
        }
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (authUser == null) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        Criteria criteria2 = new Criteria("userSid", authUser.getUserSid());
        List<UserRole> userRole = userRoleMapper.selectRoleByUserSid(criteria2);
        if (CollectionUtil.isEmpty(userRole) || (userRole.size() == 1 && userRole.get(0).getRoleSid() - 304 == 0)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105998536));
        }
        List<Role> roles = roleMapper.selectRoleByModuleSid("BG03")
                .stream()
                .filter(role -> role.getRoleSid() != 306L)
                .collect(Collectors.toList());
        List<Long> roleSids = roles.stream().map(Role::getRoleSid).collect(Collectors.toList());
        List<Long> roleReqSids = describeUsersRequest.getRoleIds();
        if (roleReqSids.stream().anyMatch(roleSid -> !roleSids.contains(roleSid))) {
            BizException.e(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        List<User> users = userMapper.selectUserByRoleSids(roleReqSids);
        if (CollectionUtil.isNotEmpty(users)) {
            users.forEach(DesensitizationUtil::doDesensitization);
        }
        return BeanConvertUtil.convert(users, DescribeUsersByRoleIdResponse.class);
    }

    /**
     * 【Since v2.5.0】获取business
     *
     * @return {@link RestResult}
     */
    @GetMapping("/business")
    @ApiOperation(httpMethod = "GET", value = "获取business")
    public RestResult businessList() {

        return new RestResult(processMgtService.listBusiness());
    }


    /**
     * 【Since v2.5.0】查询流程处理记录
     *
     * @param serviceProcessId 服务进程ID
     * @param moduleType       模块类型
     * @return {@link ProcessActivityHistoryRecordResponse}
     */
    @AuthorizeOss(action = BZ01.BZ01200606 + "," + AuthModuleOss.BG.BG03.CB2009 + "," + AuthModuleOss.BZ.BZ02.BZ0202)
    @GetMapping("/service/record/{serviceProcessId}")
    @ApiOperation(httpMethod = "GET", value = "查询流程处理记录", notes = "查询流程处理记录")
    @ListenExpireBack
    public ProcessActivityHistoryRecordResponse getHistoryProcessRecord(
            @ApiParam(name = "orderId", value = "订单ID", required = true) @PathVariable String serviceProcessId, @RequestHeader String moduleType) {

        processService.chedkPermissions(serviceProcessId, moduleType);
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (UserType.DISTRIBUTOR_USER.equals(authUser.getUserType())) {
            Integer processCount = serviceProcessService.countByCriteria(new Criteria()
                    .put("ownerId", authUser.getUserSid())
                    .put("id", serviceProcessId));
            if (processCount == 0) {
                BizException.e("无权查看该申请单");
            }
        }
        ProcessDto processDto = processService.selectByServiceProcessId(serviceProcessId);
        if (processDto == null) {
            Map<String, Object> result = new HashMap<>(4);
            result.put("process", new ProcessDto());
            result.put("records", Lists.newArrayList());

            return BeanConvertUtil.convert(result, ProcessActivityHistoryRecordResponse.class);
        }
        List<ProcessActivityDto> records = processService.instanceRecordsByServiceProcessId(serviceProcessId);

        ProcessActivityDto actived = null;
        for (ProcessActivityDto activityDto : records) {
            if (activityDto.isActived()) {
                actived = activityDto;
            }
        }
        if (actived == null && records.size() > 0) {
            actived = records.get(records.size() - 1);
        }

        if (actived != null) {
            processDto.setActLeft(actived.getActLeft());
            processDto.setActTop(actived.getActTop());
            processDto.setActWidth(actived.getActWidth());
            processDto.setActHeight(actived.getActHeight());
        }

        Collections.reverse(records);

        Map<String, Object> result = new HashMap<>(4);
        result.put("process", processDto);
        result.put("records", records);

        return BeanConvertUtil.convert(result, ProcessActivityHistoryRecordResponse.class);
    }

    @AuthorizeOss(action = ZF12.ZF1201)
    @PostMapping("/binding/process")
    @ApiOperation(httpMethod = "POST", value = "绑定流程关系", notes = "绑定流程关系")
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "'绑定流程关系'",bizId = "#bindingProcessRequest.businessName",param = "#bindingProcessRequest.businessName", resource = OperationResourceEnum.ADD_PROCESS_BIND, tagNameUs ="'Binding Process Relationship'")
    public RestResult bindingProcessRelationships(@Validated @RequestBody BindingProcessCreateRequest bindingProcessRequest) {
        if (processMgtService.insertBindingProcess(bindingProcessRequest) != 1) {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERROR_INSERT_FAILURE));
        }
        return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_INSERT_SUCCESS));
    }

    @AuthorizeOss(action = ZF12.ZF1201)
    @PutMapping("/binding/process")
    @ApiOperation(httpMethod = "PUT", value = "修改流程关系", notes = "修改流程关系")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'修改流程关系'", param = "#bindingProcessRequest", bizId = "#bindingProcessRequest.id", resource = OperationResourceEnum.UPDATE_PROCESS_BIND, tagNameUs ="'Modify process relationships'")
    public RestResult updateProcessRelationships(@Validated @RequestBody BindingProcessUpdateRequest bindingProcessRequest){
        if (processMgtService.updateBindingProcess(bindingProcessRequest) != 1) {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_810932304));
        }
        return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_UPDATE_SUCCESS));
    }

    @AuthorizeOss(action = ZF12.ZF12)
    @GetMapping("/binding/process")
    @ApiOperation(httpMethod = "GET", value = "查询流程关系", notes = "查询流程关系")
    public RestResult selectBindingProcess(){
        return new RestResult(processMgtService.selectBindingProcess());
    }

    @AuthorizeOss(action = ZF12.ZF12)
    @GetMapping("/built/in")
    @ApiOperation(httpMethod = "GET", value = "查询内置流程", notes = "查询内置流程")
    public RestResult selectBuiltInProcess(){
        return new RestResult(processService.builtInlist());
    }

    @AuthorizeOss(action = ZF12.ZF12)
    @DeleteMapping("/binding/process/{id}")
    @ApiOperation(httpMethod = "DELETE", value = "删除流程关系", notes = "删除流程关系")
    @OperationLog(type = OperationTypeEnum.DELETE, tagName = "'删除流程关系'", param = "#params", bizId = "#id", resource = OperationResourceEnum.DELETE_PROCESS_BIND, tagNameUs ="'Delete Process Relationship'")
    public RestResult deleteBindingProcess(@PathVariable Long id) {
        processMgtService.deleteBindingProcess(id);
        return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_DELETE_SUCCESS));
    }

    @AuthorizeOss(action = AuthModule.BG.BG03 + AuthModule.COMMA + AuthModuleOss.BZ.BZ02.BZ0202)
    @GetMapping("/binding/process/{id}")
    @ApiOperation(httpMethod = "DELETE", value = "获取业务流程数据", notes = "获取业务流程数据")
    public RestResult getProcessBusiness(@PathVariable Long id) {
        return new RestResult(processMgtService.getProcessBusiness(id));
    }


    @GetMapping("/xist")
    @AuthorizeOss(action = AuthModule.COMMON.PUBLIC.B1.B114)
    @ApiOperation(httpMethod = "GET", value = "获取对应流程审批", notes = "获取对应流程审批")
    public RestResult isThereProcess(@ApiParam("目标id") @RequestParam(value = "targetSt", required = false) String targetSt,
                                     @ApiParam("流程类型") @RequestParam(value = "type", required = false) String type){
        Criteria criteria = new Criteria();
        criteria.put("status", ServiceProcessStatus.PROCESS);
        criteria.put("targetSt", targetSt);
        criteria.put("type", type);
        Long processId = serviceProcessService.getByCriteria(criteria);
        if (ObjectUtils.isEmpty(processId)){
            return new RestResult(null);
        } else {
            return new RestResult(sysProcessBusinessMapper.selectByProcessId(processId));
        }
    }

    /**
     * bss 分发优惠券使用的feign 调用接口
     * @param targetSt
     * @param type
     * @return
     */
    @RejectCall
    @GetMapping("/xistList")
    @ApiOperation(httpMethod = "GET", value = "获取对应流程审批", notes = "获取对应流程审批")
    public RestResult getProcessList(@ApiParam("目标id") @RequestParam(value = "targetSt", required = false) String targetSt,
                                     @ApiParam("流程类型") @RequestParam(value = "type", required = false) String type){
        Criteria criteria = new Criteria();
        criteria.put("status", ServiceProcessStatus.PROCESS);
        criteria.put("targetSt", targetSt);
        criteria.put("type", type);
        List<Long> processId = serviceProcessService.getListByCriteria(criteria);
        if (ObjectUtils.isEmpty(processId)){
            return new RestResult(null);
        } else {
            StringBuilder s = new StringBuilder();
            for (Long aLong : processId) {
                SysProcessBusiness business = sysProcessBusinessMapper.selectByProcessId(aLong);
                if (Objects.nonNull(business)){
                    if (s.length() == 0){
                        s.append(business.getTargetNd());
                    }else {
                        s.append(",").append(business.getTargetNd());
                    }
                }
            }
            return new RestResult(s);
        }
    }

}
