/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.module.analysis.impl;

import cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.org.BizDepositMapper;
import cn.com.cloudstar.rightcloud.oss.module.analysis.service.IBizDepositService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Service
public class BizDepositServiceImpl implements IBizDepositService {

    @Autowired
    private BizDepositMapper bizDepositeMapper;


    @Override
    public BigDecimal totalDepositAmount(Criteria criteria) {
        return bizDepositeMapper.sumAmount(criteria);
    }
}
