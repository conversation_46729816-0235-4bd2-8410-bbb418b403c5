<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2018 Cloud-Star, Inc. All Rights Reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.cloudstar.rightcloud.oss.module.others.dao.deploy.DeployPlaybookMapper">
    <resultMap id="BaseResultMap"
               type="cn.com.cloudstar.rightcloud.core.pojo.dto.deploy.DeployPlaybook">
        <id column="pid" property="pid" jdbcType="BIGINT"/>
        <result column="playbook" property="playbook" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="on_step" property="onStep" jdbcType="INTEGER"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.pid != null">
                and pid = #{condition.pid}
            </if>
            <if test="condition.playbook != null">
                and playbook = #{condition.playbook}
            </if>
            <if test="condition.status != null">
                and status = #{condition.status}
            </if>
            <if test="condition.onStep != null">
                and on_step = #{condition.onStep}
            </if>
        </trim>
    </sql>
    <sql id="Base_Column_List">
        pid, playbook, status, on_step
    </sql>
    <select id="selectByExample" resultMap="BaseResultMap"
            parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from deploy_playbook
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from deploy_playbook
        where pid = #{pid,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from deploy_playbook
        where pid = #{pid,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByExample" parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria">
        delete from deploy_playbook
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" keyProperty="pid" useGeneratedKeys="true"
            parameterType="cn.com.cloudstar.rightcloud.core.pojo.dto.deploy.DeployPlaybook">
        insert into deploy_playbook (pid, playbook, status,
        on_step)
        values (#{pid,jdbcType=BIGINT}, #{playbook,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR},
        #{onStep,jdbcType=INTEGER})
    </insert>
    <insert id="insertSelective" keyProperty="pid" useGeneratedKeys="true"
            parameterType="cn.com.cloudstar.rightcloud.core.pojo.dto.deploy.DeployPlaybook">
        insert into deploy_playbook
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="pid != null">
                pid,
            </if>
            <if test="playbook != null">
                playbook,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="onStep != null">
                on_step,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="pid != null">
                #{pid,jdbcType=BIGINT},
            </if>
            <if test="playbook != null">
                #{playbook,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="onStep != null">
                #{onStep,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria"
            resultType="java.lang.Integer">
        select count(*) from deploy_playbook
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update deploy_playbook
        <set>
            <if test="record.pid != null">
                pid = #{record.pid,jdbcType=BIGINT},
            </if>
            <if test="record.playbook != null">
                playbook = #{record.playbook,jdbcType=VARCHAR},
            </if>
            <if test="record.status != null">
                status = #{record.status,jdbcType=VARCHAR},
            </if>
            <if test="record.onStep != null">
                on_step = #{record.onStep,jdbcType=INTEGER},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update deploy_playbook
        set pid = #{record.pid,jdbcType=BIGINT},
        playbook = #{record.playbook,jdbcType=VARCHAR},
        status = #{record.status,jdbcType=VARCHAR},
        on_step = #{record.onStep,jdbcType=INTEGER}
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective"
            parameterType="cn.com.cloudstar.rightcloud.core.pojo.dto.deploy.DeployPlaybook">
        update deploy_playbook
        <set>
            <if test="playbook != null">
                playbook = #{playbook,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="onStep != null">
                on_step = #{onStep,jdbcType=INTEGER},
            </if>
        </set>
        where pid = #{pid,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="cn.com.cloudstar.rightcloud.core.pojo.dto.deploy.DeployPlaybook">
        update deploy_playbook
        set playbook = #{playbook,jdbcType=VARCHAR},
        status = #{status,jdbcType=VARCHAR},
        on_step = #{onStep,jdbcType=INTEGER}
        where pid = #{pid,jdbcType=BIGINT}
    </update>
</mapper>