<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2018 Cloud-Star, Inc. All Rights Reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.cloudstar.rightcloud.oss.module.operationcore.dao.process.ProcessNodeMapper">
    <resultMap id="BaseResultMap" type="cn.com.cloudstar.rightcloud.core.pojo.dto.process.ProcessNode">
        <result column="id" property="id" jdbcType="BIGINT" />
        <result column="node_name" property="nodeName" />
        <result column="process_id" property="processId" jdbcType="BIGINT" />
        <result column="version_id" property="versionId" jdbcType="BIGINT" />
        <result column="description" property="description" />
        <result column="created_by" property="createdBy" />
        <result column="created_dt" property="createdDt" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_dt" property="updatedDt" />
        <result column="node_type" property="nodeType" />
        <result column="config_data" property="configData" />
        <result column="sort_num" property="sortNum" />
        <result column="status" property="status" />
        <result column="process_identify" property="processIdentify" />
        <result column="node_level" property="nodeLevel" />
        <result column="node_feature" property="nodeFeature" />
        <result column="parent_id" property="parentId" />
        <result column="node_group" property="nodeGroup" />
        <result column="node_group_name" property="nodeGroupName" />
        <result column="node_group_order" property="nodeGroupOrder" />
        <result column="original_node_id" property="originalNodeId" />
    </resultMap>
    <sql id="Base_Column_List">
        id,node_name,process_id,version_id,description,created_by,created_dt,
        updated_by,updated_dt,node_type,config_data,sort_num,`status`,process_identify,
        node_level, node_feature, parent_id, node_group, node_group_name, node_group_order,
        original_node_id
    </sql>
    <sql id="Example_Where_Clause">
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.id != null">
                and id = #{condition.id}
            </if>
            <if test="condition.nodeName != null">
                and node_name = #{condition.nodeName}
            </if>
            <if test="condition.processId != null">
                and process_id = #{condition.processId}
            </if>
            <if test="condition.currentDeployId != null">
                and current_deploy_id = #{condition.currentDeployId}
            </if>
            <if test="condition.versionId != null">
                and version_id = #{condition.versionId}
            </if>
            <if test="condition.nodeType != null">
                and node_type = #{condition.nodeType}
            </if>
            <if test="condition.status != null">
                and `status` = #{condition.status}
            </if>
            <if test="condition.processIdentify != null">
                and process_identify = #{condition.processIdentify}
            </if>
            <if test="condition.procedureCode != null">
                and procedure_code = #{condition.procedureCode}
            </if>
            <if test="condition.nodeLevel != null">
                and node_level = #{condition.nodeLevel}
            </if>
            <if test="condition.nodeFeature != null">
                and node_feature = #{condition.nodeFeature}
            </if>
            <if test="condition.parentId != null">
                and parent_id = #{condition.parentId}
            </if>
            <if test="condition.nodeGroup != null">
                and node_group = #{condition.nodeGroup}
            </if>
            <if test="condition.originalNodeId != null">
                and original_node_id = #{condition.originalNodeId}
            </if>
            <if test="condition.nodeIdList != null and condition.nodeIdList.size() > 0">
                and id in
                <foreach collection="condition.nodeIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.processIds != null and condition.processIds.size() > 0">
                and process_id in
                <foreach collection="condition.processIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </trim>
    </sql>
    <insert id="insertSelective"
        parameterType="cn.com.cloudstar.rightcloud.core.pojo.dto.process.ProcessNode"
        useGeneratedKeys="true" keyProperty="id">
        insert into process_node
        <trim prefix="(" suffix=")" suffixOverrides=",">
            id,
            <if test="nodeName != null">
                node_name,
            </if>
            <if test="processId != null">
                process_id,
            </if>
            <if test="versionId != null">
                version_id,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="createdDt != null">
                created_dt,
            </if>
            <if test="updatedBy != null">
                updated_by,
            </if>
            <if test="updatedDt != null">
                updated_dt,
            </if>
            <if test="nodeType != null">
                node_type,
            </if>
            <if test="configData != null">
                config_data,
            </if>
            <if test="sortNum != null">
                sort_num,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="processIdentify != null">
                process_identify,
            </if>
            <if test="nodeLevel != null">
                node_level,
            </if>
            <if test="nodeFeature != null">
                node_feature,
            </if>
            <if test="parentId != null">
                parent_id,
            </if>
            <if test="nodeGroup != null">
                node_group,
            </if>
            <if test="nodeGroupName != null">
                node_group_name,
            </if>
            <if test="nodeGroupOrder != null">
                node_group_order,
            </if>
            <if test="originalNodeId != null">
                original_node_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id},
            <if test="nodeName != null">
                #{nodeName},
            </if>
            <if test="processId != null">
                #{processId},
            </if>
            <if test="versionId != null">
                #{versionId},
            </if>
            <if test="description != null">
                #{description},
            </if>
            <if test="createdBy != null">
                #{createdBy},
            </if>
            <if test="createdDt != null">
                #{createdDt},
            </if>
            <if test="updatedBy != null">
                #{updatedBy},
            </if>
            <if test="updatedDt != null">
                #{updatedDt},
            </if>
            <if test="nodeType != null">
                #{nodeType},
            </if>
            <if test="configData != null">
                #{configData},
            </if>
            <if test="sortNum != null">
                #{sortNum},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="processIdentify != null">
                #{processIdentify},
            </if>
            <if test="nodeLevel != null">
                #{nodeLevel},
            </if>
            <if test="nodeFeature != null">
                #{nodeFeature},
            </if>
            <if test="parentId != null">
                #{parentId},
            </if>
            <if test="nodeGroup != null">
                #{nodeGroup},
            </if>
            <if test="nodeGroupName != null">
                #{nodeGroupName},
            </if>
            <if test="nodeGroupOrder != null">
                #{nodeGroupOrder},
            </if>
            <if test="originalNodeId != null">
                #{originalNodeId},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective">
        update process_node
        <set>
            <if test="nodeName != null">
                node_name = #{nodeName},
            </if>
            <if test="processId != null">
                process_id = #{processId},
            </if>
            <if test="versionId != null">
                version_id = #{versionId},
            </if>
            <if test="description != null">
                description = #{description},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy},
            </if>
            <if test="createdDt != null">
                created_dt = #{createdDt},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy},
            </if>
            <if test="updatedDt != null">
                updated_dt = #{updatedDt},
            </if>
            <if test="nodeType != null">
                node_type = #{nodeType},
            </if>
            <if test="configData != null">
                config_data = #{configData},
            </if>
            <if test="sortNum != null">
                sort_num = #{sortNum},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="processIdentify != null">
                process_identify = #{processIdentify},
            </if>
            <if test="nodeLevel != null">
                node_level = #{nodeLevel},
            </if>
            <if test="nodeFeature != null">
                node_feature = #{nodeFeature},
            </if>
            <if test="parentId != null">
                parent_id = #{parentId},
            </if>
            <if test="nodeGroup != null">
                node_group = #{nodeGroup},
            </if>
            <if test="nodeGroupName != null">
                node_group_name = #{nodeGroupName},
            </if>
            <if test="nodeGroupOrder != null">
                node_group_order = #{nodeGroupOrder},
            </if>
            <if test="originalNodeId != null">
                original_node_id = #{originalNodeId},
            </if>
        </set>
        where id=#{id}
    </update>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from process_node where id=#{id}
    </delete>
    <delete id="deleteByVersionId" parameterType="java.lang.Long">
        delete from process_node where version_id=#{versionId}
    </delete>
    <delete id="deleteByProcessId" parameterType="java.lang.Long">
        delete from process_node where process_id=#{processId}
    </delete>
    <delete id="clear">
        delete from process_node where 1=1
    </delete>
    <select id="selectByVersionId" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from process_node where version_id=#{versionId} and status !=104 and status != 103 and node_level='1' order by sort_num asc
    </select>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from process_node where id=#{id}
    </select>
    <select id="selectAuditNodeByVersionId" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from process_node where version_id=#{versionId} and status=3 and node_level='1' order by sort_num asc
    </select>
    <select id="selectCloseNodeByVersionId" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from process_node where version_id=#{versionId} and status=2
    </select>
    <select id="selectAuditNodeByProcessIdentify" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from process_node where process_identify=#{processIdentify}
        order by sort_num asc
    </select>
    <select id="selectAuditNode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from process_node where process_identify in
        <foreach collection="processIdentifies"  open="(" separator="," close=")" item="processIdentify">
            #{processIdentify}
        </foreach>
        order by process_id asc
    </select>
    <select id="selectByParams" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria">
        select
        <include refid="Base_Column_List"/>
        from process_node
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>

    </select>

</mapper>
