package cn.com.cloudstar.rightcloud.oss.module.system.bean.config.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

@Data
@ApiModel(description = "批量更新产品限额配置请求参数")
public class UpdateProductLimitsConfigRequest implements Serializable {
    /**
     * 配置SID
     */
    @NotNull
    @ApiModelProperty(value = "配置SID")
    private Long configSid;

    /**
     * 配置类型
     */
    @NotBlank
    @ApiModelProperty(value = "配置类型")
    private String configType;

    /**
     * 配置名称
     */
    @NotBlank
    @ApiModelProperty(value = "配置名称")
    private String configName;

    /**
     * 配置Key
     */
    @NotBlank
    @ApiModelProperty(value = "配置Key")
    private String configKey;

    /**
     * 配置值
     */
    @ApiModelProperty(value = "配置值")
    @Length(max = 8192, message = "内容超长")
    @Pattern(regexp = "[a-zA-Z0-9\\u4e00-\\u9fa5.-]*$", message = "配置值请勿输入特殊字符！")
    private String configValue;

    /**
     * 数据类型
     */
    @ApiModelProperty(value = "数据类型")
    private String dataType;

    /**
     * 性能单位
     */
    @ApiModelProperty(value = "性能单位")
    private String unit;
}
