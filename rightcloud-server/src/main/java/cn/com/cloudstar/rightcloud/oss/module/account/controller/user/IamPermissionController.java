package cn.com.cloudstar.rightcloud.oss.module.account.controller.user;

import cn.com.cloudstar.rightcloud.common.enums.SysRoleEnum;
import cn.com.cloudstar.rightcloud.core.pojo.dto.sfs.ServiceCategory;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.Org;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.oss.common.annotation.RejectCall;
import cn.com.cloudstar.rightcloud.oss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.org.OrgMapper;
import cn.com.cloudstar.rightcloud.oss.module.operate.dao.EntityUserMapper;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.dao.ServiceCategoryMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.common.constants.AuthModule;
import cn.com.cloudstar.rightcloud.common.constants.AuthModuleOss.BD.BD06.BD0601;
import cn.com.cloudstar.rightcloud.common.exception.BizException;
import cn.com.cloudstar.rightcloud.operationlog.annotation.OperationLog;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.annotation.AuthorizeOss;
import cn.com.cloudstar.rightcloud.oss.common.constants.Constants;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.oss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.oss.common.pojo.RestResult.Status;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.oss.module.account.service.user.IamPermissionService;

/**
 * 统一身份认证服务 IAM
 *
 * <AUTHOR>
 * @Date: 2022/10/25
 */
@Api(tags = "统一身份认证服务 IAM")
@RestController
@RequestMapping("/iam/permission")
@Slf4j
public class IamPermissionController {

    @Autowired
    private IamPermissionService iamPermissionService;

    @Autowired
    private OrgMapper orgMapper;

    @Autowired
    private ServiceCategoryMapper serviceCategoryMapper;

    @Autowired
    private EntityUserMapper entityUserMapper;

    /**
     * MA共享资源池停止作业
     *
     * @param orgSid orgSid
     */
    @PutMapping("/sharePool/stopJob/{orgSid}")
    @ApiOperation(httpMethod = "PUT", value = "MA共享资源池停止作业")
    @AuthorizeOss(action = BD0601.BD060104)
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'Modelarts共享资源池'", bizId = "#orgSid", resource = OperationResourceEnum.MODELARTS_COMMON_SHARE_POOL_STOP_JOB, tagNameUs ="'Shared resource pool'")
    public RestResult<Boolean> modelartsCommonSharePoolStopJob(@PathVariable Long orgSid) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (Objects.isNull(authUserInfo)) {
            throw new BizException(WebUtil.getMessage(MsgCd.THE_CURRENT_LOGGED_IN_USER_INFORMATION_WAS_NOT_OBTAINED));
        }
        ServiceCategory serviceCategory = serviceCategoryMapper.selectByServiceType(
                ProductCodeEnum.MODEL_ARTS.getProductCode());
        List<Long> longs = entityUserMapper.selectUserSidByEntityId(serviceCategory.getEntityId());
        if (Objects.nonNull(authUserInfo.getOrgSid()) || !longs.contains(authUserInfo.getUserSid())) {
            throw new BizException(WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        Org org = orgMapper.selectTenantOrgByOrgSid(orgSid);
        if (Objects.isNull(org) || Objects.equals(org.getOwner(), SysRoleEnum.ORG_ADMIN.getRoleSid())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        boolean result = iamPermissionService.modelartsCommonSharePoolStopJob(org.getOwner());
        if (result) {
            return new RestResult<>(Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS), true);
        } else {
            return new RestResult<>(Status.FAILURE, WebUtil.getMessage(MsgCd.ERROR_OPERATE_FAILURE), false);
        }
    }


    /**
     * modelarts共同分享池停止播放作业通过日程表
     * [INNER API] 试用期账号MA共享资源池停止作业
     *
     * @param orgSid 组织ID
     * @return {@link RestResult}<{@link Boolean}>
     */
    @RejectCall
    @PutMapping("/schedule/sharePool/stopJob/{orgSid}")
    @ApiOperation(httpMethod = "PUT", value = "MA共享资源池停止作业-schedule")
    public RestResult<Boolean> modelartsCommonSharePoolStopJobBySchedule(@PathVariable Long orgSid) {
        Org org = orgMapper.selectTenantOrgByOrgSid(orgSid);
        boolean result = iamPermissionService.modelartsCommonSharePoolStopJobBySchedule(org.getOwner());
        if (result) {
            return new RestResult<>(Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS), true);
        } else {
            return new RestResult<>(Status.FAILURE, WebUtil.getMessage(MsgCd.ERROR_OPERATE_FAILURE), false);
        }
    }

    /**
     * 自定义共享资源池限制策略
     *
     * @param userSid 用户id
     * @param state 是否开启
     */
    @PutMapping("/sharePool/{userSid}/{state}")
    @ApiOperation(httpMethod = "PUT", value = "自定义共享资源池限制策略")
    @AuthorizeOss(action = AuthModule.CB.CB1913)
    @OperationLog(type = OperationTypeEnum.UPDATE,
                    tagName = "'自定义共享资源池限制策略'",
                    resource = OperationResourceEnum.MODELARTS_COMMON_SHARE_POOL,
                    bizId = "#userId",
                    param = "#state"
    )
    public RestResult modelartsCommonSharePool(@PathVariable Long userSid, @PathVariable Boolean state) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        verificationOperator(userSid, authUserInfo);
        boolean result = iamPermissionService.modelartsCommonSharePool(userSid, state);
        if (result) {
            return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS), true);
        } else {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERROR_OPERATE_FAILURE), false);
        }
    }

    /**
     * 查询用户隐藏/启用/关闭自定义共享资源池限制策略按钮
     *
     * @param userSid 用户id
     *
     * @return open开启/close关闭/hide隐藏/ashopen开启置灰/ashclose关闭置灰
     */
    @GetMapping("/sharePool/{userSid}")
    @AuthorizeOss(action = AuthModule.CB.CB19)
    @ApiOperation(httpMethod = "GET", value = "查询该用户是否拥有自定义共享资源池限制策略")
    public RestResult isModelartsCommonSharePool(@PathVariable Long userSid) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        verificationOperator(userSid, authUserInfo);
        String result = Constants.HIDE;
        if (Constants.CONSOLE.equals(authUserInfo.getRemark()) && Objects.isNull(authUserInfo.getParentSid())) {
            result = iamPermissionService.isModelartsCommonSharePool(userSid);
        }
        return new RestResult<>(result);
    }

    /**
     * 校验操作人
     *
     * @param userSid 用户id
     * @param authUserInfo 登录用户信息
     */
    private void verificationOperator(Long userSid, AuthUser authUserInfo) {
        if (Objects.isNull(authUserInfo)) {
            throw new BizException(WebUtil.getMessage(MsgCd.THE_CURRENT_LOGGED_IN_USER_INFORMATION_WAS_NOT_OBTAINED));
        }
        if (!authUserInfo.getUserSid().equals(userSid)) {
            throw new BizException(WebUtil.getMessage(MsgCd.NOT_HAVE_PERMISSION_OPERATE));
        }
    }

}
