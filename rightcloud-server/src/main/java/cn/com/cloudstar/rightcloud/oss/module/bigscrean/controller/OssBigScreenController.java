/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.module.bigscrean.controller;

import cn.com.cloudstar.rightcloud.common.constants.res.type.VirtType;
import cn.com.cloudstar.rightcloud.oss.common.pojo.BizUserAiBmsRecord;
import cn.com.cloudstar.rightcloud.oss.module.bigscrean.dao.BizUserAiBmsRecordMapper;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResVmType;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.QueryResVmTypeByParamsRequest;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.mongodb.client.MongoCursor;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.bson.Document;
import org.docx4j.wml.Numbering.Num;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Properties;
import java.util.Set;
import java.util.TreeMap;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.adapter.core.MQHelper;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.TrainingActiveJobsRequest;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.TrainingActiveJobsV2Request;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.result.*;
import cn.com.cloudstar.rightcloud.basic.data.pojo.base.Base;
import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnv;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.BIG_SCREEN;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.constants.res.type.VirtType;
import cn.com.cloudstar.rightcloud.common.constants.type.CloudEnvTenantKey;
import cn.com.cloudstar.rightcloud.common.constants.type.ModelartsPoolAllocateType;
import cn.com.cloudstar.rightcloud.common.enums.CommonPropertyKeyEnum;
import cn.com.cloudstar.rightcloud.common.exception.BizException;
import cn.com.cloudstar.rightcloud.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.common.util.CrytoUtilSimple;
import cn.com.cloudstar.rightcloud.common.util.MapsKit;
import cn.com.cloudstar.rightcloud.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.common.util.json.JsonUtil;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.SysConfig;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.Org;
import cn.com.cloudstar.rightcloud.driver.pojo.base.BaseResult;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.User;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicInfoUtil;
import cn.com.cloudstar.rightcloud.oss.common.annotation.AuthorizeOss;
import cn.com.cloudstar.rightcloud.oss.common.api.ApiGroup;
import cn.com.cloudstar.rightcloud.oss.common.api.ApiGroupEnum;
import cn.com.cloudstar.rightcloud.oss.common.constants.Constants;
import cn.com.cloudstar.rightcloud.oss.common.constants.ma.MaPoolStatus;
import cn.com.cloudstar.rightcloud.oss.module.account.service.org.OrgService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.user.UserService;
import cn.com.cloudstar.rightcloud.oss.module.bigscrean.bean.BigScreenLineChartResult;
import cn.com.cloudstar.rightcloud.oss.module.bigscrean.bean.BigScreenOccRatioRequest;
import cn.com.cloudstar.rightcloud.oss.module.bigscrean.bean.BigScreenPieResult;
import cn.com.cloudstar.rightcloud.oss.module.bigscrean.bean.BigScreenResult;
import cn.com.cloudstar.rightcloud.oss.module.bigscrean.bean.BigScreenSelectResult;
import cn.com.cloudstar.rightcloud.oss.module.bigscrean.bean.BigScreenTableResult;
import cn.com.cloudstar.rightcloud.oss.module.bigscrean.bean.ComTopResult;
import cn.com.cloudstar.rightcloud.oss.module.bigscrean.bean.CompanyJobsResult;
import cn.com.cloudstar.rightcloud.oss.module.bigscrean.bean.ResMonUsageTopResult;
import cn.com.cloudstar.rightcloud.oss.module.bigscrean.bean.ResUsageResult;
import cn.com.cloudstar.rightcloud.oss.module.bigscrean.bean.ResUsageTopResult;
import cn.com.cloudstar.rightcloud.oss.module.bigscrean.bean.SysMPreOccRecord;
import cn.com.cloudstar.rightcloud.oss.module.bigscrean.bean.VisualDTO;
import cn.com.cloudstar.rightcloud.oss.module.bigscrean.bean.model.ModelArtsPoolAllocate;
import cn.com.cloudstar.rightcloud.oss.module.bigscrean.bean.model.ModelArtsTrainingJob;
import cn.com.cloudstar.rightcloud.oss.module.bigscrean.bean.model.NpuCardInfo;
import cn.com.cloudstar.rightcloud.oss.module.bigscrean.bean.model.PrivatePoolTrainingJob;
import cn.com.cloudstar.rightcloud.oss.module.bigscrean.bean.model.PrivateResourceInfo;
import cn.com.cloudstar.rightcloud.oss.module.bigscrean.service.IAIBigscreenJobCountViewService;
import cn.com.cloudstar.rightcloud.oss.module.bigscrean.service.IAIBigscreenUsageViewService;
import cn.com.cloudstar.rightcloud.oss.module.bigscrean.service.IBizResUsageDayReportService;
import cn.com.cloudstar.rightcloud.oss.module.bigscrean.service.IBizResUsageHourReportService;
import cn.com.cloudstar.rightcloud.oss.module.bigscrean.service.IBizResUsageMonReportService;
import cn.com.cloudstar.rightcloud.oss.module.bigscrean.service.ISysMPreOccRecordService;
import cn.com.cloudstar.rightcloud.oss.module.bigscrean.service.VisualService;
import cn.com.cloudstar.rightcloud.oss.module.system.bean.config.response.DescribeBigScreenNpuConfigParentResponse;
import cn.com.cloudstar.rightcloud.oss.module.bigscrean.bean.*;
import cn.com.cloudstar.rightcloud.oss.module.bigscrean.bean.model.*;
import cn.com.cloudstar.rightcloud.oss.module.bigscrean.dao.BizUserAiBmsRecordMapper;
import cn.com.cloudstar.rightcloud.oss.module.bigscrean.service.*;
import cn.com.cloudstar.rightcloud.oss.module.system.bean.config.response.DescribeBigScreenNpuConfigResponse;
import cn.com.cloudstar.rightcloud.oss.module.system.dao.config.AiBigscreenNpuConfigMapper;
import cn.com.cloudstar.rightcloud.oss.module.system.dao.config.SysConfigMapper;
import cn.com.cloudstar.rightcloud.oss.util.PropertiesUtil;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.ma.ResMaPoolVO;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.cloud.CloudEnvAccountRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.cloud.CloudEnvRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.ma.MaRemoteService;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.mongodb.client.MongoCursor;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.util.*;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.com.cloudstar.rightcloud.remote.api.resource.service.server.ResVmTypeRemoteService;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.mongodb.client.MongoCursor;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * oss大屏幕控制器
 * <AUTHOR>
 */
@ApiGroup(ApiGroupEnum.BIGSCREEN_GROUP)
@Api(value = "/bigScreen", tags = "大屏")
@RestController
@RequestMapping("/bigScreen")
@Slf4j
public class OssBigScreenController {


    private final String yyyyMM = "yyyyMM";
    private final String yyyyMMdd = "yyyyMMdd";
    private final String yyyy_MM_dd = "yyyy-MM-dd";
    private final String yyyy_MM_dd_HH_mm_ss = "yyyy-MM-dd HH:mm:ss";
    private static final String REQ_SOURCE = "REQ_SOURCE";

    public static final int HOUR_OF_DAY = 24;

    private static final int TIME_NODE = 6 * HOUR_OF_DAY;

    private static final int CONVERT_TO_P_NUM = 4;

    private static final String EMPTY_STRING_SHOW = "--";

    /**
     * 算力卡转换除系数
     */
    private static final String SCREEN_AI_RESOURCE_COEFFICIENT_DIVIDE = "screen.airesource.coefficient.divide";
    /**
     * 算力卡转换乘系数
     */
    private static final String SCREEN_AI_RESOURCE_COEFFICIENT_MULTIPLY = "screen.airesource.coefficient.multiply";

    @Autowired
    private OrgService orgService;

    @Autowired
    private UserService userService;

    @Autowired
    private SysConfigMapper sysConfigMapper;
    @Autowired
    private IBizResUsageHourReportService bizResUsageHourReportService;

    @Autowired
    private IBizResUsageDayReportService bizResUsageDayReportService;
    @Autowired
    private IBizResUsageMonReportService bizResUsageMonReportService;
    @DubboReference
    private CloudEnvRemoteService cloudEnvRemoteService;
    @DubboReference
    private CloudEnvAccountRemoteService cloudEnvAccountRemoteService;
    @DubboReference
    private MaRemoteService maRemoteService;
    @Autowired
    private ISysMPreOccRecordService sysMPreOccRecordService;
    @Autowired
    private IAIBigscreenUsageViewService aiBigscreenUsageViewService;
    @Autowired
    private AiBigscreenNpuConfigMapper aiBigscreenNpuConfigMapper;

    @Autowired
    private  IAIBigscreenJobCountViewService  aiBigscreenJobCountViewService;
    @Autowired
    private VisualService visualService;

    @Value("${TENTANT_USER_NAME:xuchangaienvint}")
    private String tentantUserName;

    @Value("${TENTANT_USER_PASS:UA1io4ugK1t}")
    private String tenantUserPass;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private BizUserAiBmsRecordMapper bizUserAiBmsRecordMapper;

    @DubboReference
    private ResVmTypeRemoteService resVmTypeRemoteService;

    /**
     * 设计峰值算力
     *
     * @return {@link BigScreenResult}
     * @since 2.4.1
     */
    @AuthorizeOss(action = BIG_SCREEN.PA)
    @ApiOperation("设计峰值算力")
    @GetMapping("aiResComputingPower")
    public BigScreenResult aiResComputingPower() {
        String property = PropertiesUtil.getProperty("screen.airesource.power");
        int maxPower = Integer.parseInt(property);
        return new BigScreenResult(maxPower);
    }

    /**
     * resnet50模型数据
     *
     * @return {@link BigScreenResult}
     * @since 2.4.1
     */
    @AuthorizeOss(action = BIG_SCREEN.PA)
    @ApiOperation("resnet")
    @GetMapping("resnet")
    public BigScreenResult resnet() {
        String resnetProperty = PropertiesUtil.getProperty("screen.airesource.resnet50");
        Integer resnetValue = getValue(resnetProperty);
        return new BigScreenResult(resnetValue);
    }

    /**
     * Bert-Large模型数据
     *
     * @return {@link BigScreenResult}
     * @since 2.4.1
     */
    @AuthorizeOss(action = BIG_SCREEN.PA)
    @ApiOperation("bertLarge")
    @GetMapping("bertLarge")
    public BigScreenResult bertLarge() {
        String bertProperty = PropertiesUtil.getProperty("screen.airesource.bert-large");
        Integer bertValue = getValue(bertProperty);
        return new BigScreenResult(bertValue);
    }

    /**
     * 查询行业分布
     *
     * @return {@link BigScreenResult}
     * @since 2.4.1
     */
    @AuthorizeOss(action = BIG_SCREEN.PA)
    @ApiOperation("查询行业分布")
    @GetMapping("industryType/num")
    public BigScreenResult getIndustryType() {
        List<String> industry = orgService.queryIndustry();
        Map<String, Long> collect = industry.stream().collect(Collectors.groupingBy(e -> {
            if (StringUtils.isNotEmpty(e)) {
                return e;
            } else {
                return "其他";
            }
        }, Collectors.counting()));
        return new BigScreenResult(collect);
    }

    /**
     * 上线企业
     *
     * @return {@link BigScreenResult}
     * @since 2.4.1
     */
    @AuthorizeOss(action = BIG_SCREEN.PA)
    @ApiOperation("上线企业")
    @GetMapping("companies/onlineNum")
    public BigScreenResult onlineNum() {
        String property = PropertiesUtil.getProperty("screen.airesource.online-company");
        Integer num = Integer.valueOf(property);
        if (num == 0) {
            Integer integer = bizResUsageDayReportService.countOnlineCompany();
            return new BigScreenResult(integer);
        }
        return new BigScreenResult(num);
    }

    /**
     * 实时算力
     *
     * @return {@code BigScreenResult}
     * @since 2.4.1
     */
    @AuthorizeOss(action = BIG_SCREEN.P)
    @ApiOperation("实时算力")
    @GetMapping("currentComputingPower")
    public BigScreenResult currentComputingPower() {
        long fiveMinutes = 60L * 1000L * 5L;
        long endTime = System.currentTimeMillis();


        long startTime = endTime - fiveMinutes;
        List<ModelArtsPoolAllocate> publicModelArts = getModelArts(null, startTime, endTime);
        //未查询到数据查询，上个5分钟数据，BUG：#66782 优化显示
        if (CollectionUtils.isEmpty(publicModelArts)) {
            publicModelArts = getModelArts(null, startTime-fiveMinutes, startTime);
        }
       NpuCardInfo npuCardInfo = getNpuCardInfoAll(publicModelArts);
        Double allocate = npuCardInfo.getAllocate();
        SysConfig sysConfigDivide = sysConfigMapper.selectByConfigKey(SCREEN_AI_RESOURCE_COEFFICIENT_DIVIDE);
        SysConfig sysConfigMultiply = sysConfigMapper.selectByConfigKey(SCREEN_AI_RESOURCE_COEFFICIENT_MULTIPLY);
        BigDecimal divide = BigDecimal.valueOf(8);
        BigDecimal multiply = BigDecimal.valueOf(2.5);
        if (Objects.nonNull(sysConfigDivide)) {
            divide = Objects.nonNull(sysConfigDivide.getConfigValue())
                && !NumberUtil.equals(new BigDecimal(sysConfigDivide.getConfigValue()),BigDecimal.ZERO)? new BigDecimal(
                    sysConfigDivide.getConfigValue()) : divide;
        }
        if (Objects.nonNull(sysConfigMultiply)) {
            multiply = Objects.nonNull(sysConfigMultiply.getConfigValue()) ? new BigDecimal(
                    sysConfigMultiply.getConfigValue()) : multiply;
        }
        BigDecimal bigDecimal = BigDecimal.valueOf(allocate)
                                          .divide(divide, 2, RoundingMode.HALF_UP)
                                          .multiply(multiply).setScale(2,BigDecimal.ROUND_DOWN);

        // ModelArts BMS实时算力
        BigDecimal bms = getModelArtsBms(startTime, endTime);
        bigDecimal = bigDecimal.add(bms);
        return new BigScreenResult(bigDecimal.doubleValue());
    }

    /**
     * ModelArts BMS实时算力
     * @param startTime startTime
     * @param endTime endTime
     * @return BigDecimal
     */
    private BigDecimal getModelArtsBms(long startTime, long endTime) {
        AtomicReference<BigDecimal> result = new AtomicReference<>(BigDecimal.ZERO);
        List<BizUserAiBmsRecord> bizUserAiBmsRecords = getBizUserAiBmsRecords(startTime, endTime);
        if (bizUserAiBmsRecords.size() > 0) {
            QueryResVmTypeByParamsRequest queryResVmTypeByParamsRequest = new QueryResVmTypeByParamsRequest();
            queryResVmTypeByParamsRequest.setVirtType(VirtType.BareMetal);
            List<ResVmType> resVmTypes = resVmTypeRemoteService.selectByParams(queryResVmTypeByParamsRequest);
            Map<String, BigDecimal> resVmTypeMaps = resVmTypes.stream()
                    .collect(Collectors.toMap(ResVmType::getUuid,
                            ResVmType::getNpuRatio,
                            (k1, k2) -> k1));
            Map<String, Integer> flavorIdNpuAllocationMaps = bizUserAiBmsRecords.stream()
                    .collect(Collectors.groupingBy(
                            BizUserAiBmsRecord::getFlavorId,
                            Collectors.summingInt(
                                    BizUserAiBmsRecord::getNpuAllocation)));
            flavorIdNpuAllocationMaps.forEach((k, v) -> {
                BigDecimal npuRatio = resVmTypeMaps.get(k);
                result.set(npuRatio.multiply(BigDecimal.valueOf(v)).add(result.get()));
            });
        }
        return result.get().setScale(2, BigDecimal.ROUND_DOWN);
    }

    /**
     * getBizUserAiBmsRecords
     * @param startTime startTime
     * @param endTime endTime
     * @return List<BizUserAiBmsRecord>
     */
    private List<BizUserAiBmsRecord> getBizUserAiBmsRecords(long startTime, long endTime) {
        cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria criteria = new cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria();
        criteria.put("startTime", new Date(startTime));
        criteria.put("endTime", new Date(endTime));
        List<BizUserAiBmsRecord> bizUserAiBmsRecords = bizUserAiBmsRecordMapper.selectByParams(criteria);
        if (CollectionUtils.isEmpty(bizUserAiBmsRecords)) {
            //当前五分钟内没有数据查询上一个五分钟
            long fiveMinutes = 60L * 1000L * 5L;
            startTime = startTime - fiveMinutes;
            criteria = new cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria();
            criteria.put("startTime", new Date(startTime));
            criteria.put("endTime", new Date(endTime));
            bizUserAiBmsRecords = bizUserAiBmsRecordMapper.selectByParams(criteria);
        }
        return bizUserAiBmsRecords;
    }

    /**
     * 【Since v2.5.0】 查询注册公司数
     *
     * <br> 兼容老大屏
     *
     * @return {@link BigScreenResult}
     */
    @AuthorizeOss(action = BIG_SCREEN.PA)
    @ApiOperation("查询注册公司数")
    @GetMapping("companies/num")
    public BigScreenResult companiesNum(){
        List<Org> companies = orgService.registCompany();
        if(!CollectionUtils.isEmpty(companies)){
            return new BigScreenResult(companies.size());
        }
        return new BigScreenResult(0);
    }

    /**
     * 【Since v2.5.0】 查询公司情况
     *
     * <br> 兼容老大屏
     *
     * @return {@link List}<{@link BigScreenSelectResult}>
     */
    @AuthorizeOss(action = BIG_SCREEN.PA)
    @ApiOperation("查询公司情况")
    @GetMapping("companies")
    public List<BigScreenSelectResult> companies(){
        List<BigScreenSelectResult> options = new ArrayList<>();

        options.add(new BigScreenSelectResult("全部","-1"));

        List<Org> companies = orgService.registCompany();

        if(CollectionUtil.isNotEmpty(companies)){
            companies.stream().forEach(comp -> options.add(new BigScreenSelectResult(comp.getOrgName(),comp.getOrgSid().toString())));
        }
        return options;
    }

    /**
     * 注册用户数
     *
     * @return {@code BigScreenResult}
     * @since 2.4.1
     */
    @AuthorizeOss(action = BIG_SCREEN.PA)
    @ApiOperation("注册用户数")
    @GetMapping("users")
    public BigScreenResult users() {

        int users = userService.countRegistUsers();
        return new BigScreenResult(users);
    }

    /**
     * 当前使用企业数
     * @since 2.4.1
     */
    @AuthorizeOss(action = BIG_SCREEN.PA)
    @ApiOperation("当前使用企业数")
    @GetMapping("companies/currentNum")
    public BigScreenResult currentNum() {
        Date current = new Date();
        current = DateUtils.addHours(current, -3);
        List<BigScreenTableResult> bigScreenTableResults = getBigScreenTableResultL(current);
        return new BigScreenResult(bigScreenTableResults.size());
    }

    /**
     * 企业资源占用排行
     *
     * @param top 前
     * @return {@code List<ComTopResult>}
     * @since 2.4.1
     */
    @AuthorizeOss(action = BIG_SCREEN.PA)
    @ApiOperation("企业资源占用排行")
    @GetMapping("comResUsageTop")
    public List<ComTopResult> comResUsageTop(Integer top){
        if(top == null){
           throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_664952932));
        }
        List<ResUsageTopResult>  resUsageTopList= aiBigscreenUsageViewService.resOccupancy(top);

        List<ComTopResult> resultList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(resUsageTopList)){
            resUsageTopList.forEach(resUsage->{
                Integer cards = resUsage.getCards();
                if(cards ==null){
                    return;
                }
                BigDecimal pNumBigDecimal = NumberUtil.div(cards, BigDecimal.valueOf(CONVERT_TO_P_NUM)).setScale(0, BigDecimal.ROUND_UP);
                if(!NumberUtil.isGreater(pNumBigDecimal,BigDecimal.ZERO)){
                    return;
                }
                String pNum = pNumBigDecimal +"P";

                String solution = StringUtils.isNotEmpty(resUsage.getSolution())?resUsage.getSolution():EMPTY_STRING_SHOW;

                ComTopResult comTopResult = new ComTopResult(resUsage.getOrgName(), pNum, solution);
                resultList.add(comTopResult);

            });
        }
        return resultList;
    }

    /**
     * 企业训练次数排行
     *
     * @param top 前
     * @return {@code List<ComTopResult>}
     * @since 2.4.1
     */
    @AuthorizeOss(action = BIG_SCREEN.PA)
    @ApiOperation("企业训练次数排行")
    @GetMapping("companies/comJobsTop")
    public List<ComTopResult> comJobsTop(Integer top){
        if(top == null){
            top = 10;
        }
        List<CompanyJobsResult>  jobsResultList= aiBigscreenJobCountViewService.selectList(top);
        List<ComTopResult> resultList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(jobsResultList)){
            jobsResultList.forEach(companyJob->{
                String solution = StringUtils.isNotEmpty(companyJob.getSolution())?companyJob.getSolution():EMPTY_STRING_SHOW;
                ComTopResult comTopResult = new ComTopResult(companyJob.getOrgName(), companyJob.getJobs().toString()+"次",solution);
                resultList.add(comTopResult);

            });
        }
        return resultList;
    }

    /**
     * NPU资源池占比
     *
     * @return {@code BigScreenResult}
     * @since 2.4.1
     */
    @AuthorizeOss(action = BIG_SCREEN.PA)
    @ApiOperation("NPU资源池占比")
    @GetMapping("NPUCard/ratio")
    public BigScreenResult getNpuCardRatio() {
        long fiveMinutes = 60L * 1000L * 5L;
        long endTime = System.currentTimeMillis();
        long startTime = endTime - fiveMinutes;
        List<ModelArtsPoolAllocate> publicModalarts = getModelArts("public", startTime, endTime);
        List<ModelArtsPoolAllocate> privateModalarts = getModelArts("private", startTime, endTime);

        //未查询到数据查询，上个5分钟数据，BUG：#66782 优化显示
        if (CollectionUtils.isEmpty(publicModalarts) && CollectionUtils.isEmpty(privateModalarts)) {
            publicModalarts = getModelArts("public", startTime-fiveMinutes, startTime);
            privateModalarts = getModelArts("private", startTime-fiveMinutes, startTime);
        }


        BigDecimal publicCapacity = BigDecimal.ZERO;
        BigDecimal privateCapacity = BigDecimal.ZERO;
        for (ModelArtsPoolAllocate modelArtsPoolAllocate : publicModalarts) {

            Double capacity = Double.parseDouble(modelArtsPoolAllocate.getCapacity());
            if ( capacity < 0 || ObjectUtil.isEmpty(capacity)) {
                continue;
            } else  {
                publicCapacity = publicCapacity.add(BigDecimal.valueOf(capacity));
            }

        }
        for (ModelArtsPoolAllocate modelArtsPoolAllocate : privateModalarts) {
            //逻辑池不统计
            if (ModelartsPoolAllocateType.LOGICAL.equalsIgnoreCase(modelArtsPoolAllocate.getType())) {
                continue;
            }
            Double capacity = Double.parseDouble(modelArtsPoolAllocate.getCapacity());
            if (capacity < 0 || ObjectUtil.isEmpty(capacity)) {
                continue;
            } else {
                privateCapacity =  privateCapacity.add(BigDecimal.valueOf(capacity));
            }

        }
        Map<String, BigDecimal> npuMap = new HashMap<>(50);
        npuMap.put("公共卡总量", publicCapacity);
        npuMap.put("专属卡总量", privateCapacity);
        //ModelArts BMS NPU资源池占比
        List<BizUserAiBmsRecord> bizUserAiBmsRecords = getBizUserAiBmsRecords(startTime, endTime);
        int bmsNpu = bizUserAiBmsRecords.stream().mapToInt(BizUserAiBmsRecord::getNpuAllocation).sum();
        npuMap.put("裸金属卡总量", BigDecimal.valueOf(bmsNpu));
        return new BigScreenResult(npuMap);
    }

    /**
     * 24小时最大资源使用率
     *
     * @return {@code BigScreenResult}
     * @since 2.4.1
     */
    @AuthorizeOss(action = BIG_SCREEN.PA)
    @ApiOperation("24小时最大资源使用率")
    @GetMapping("day/maxAllocateRatio")
    public BigScreenResult getDayAllocateRatio() {
        Map<String, Map<String, BigDecimal>> resourceMap = getResourceMap();
        Map<String, BigDecimal> allocateRatioMap = resourceMap.get("24小时资源使用率趋势");
        Optional<Entry<String, BigDecimal>> maxOptional = allocateRatioMap.entrySet()
            .stream()
            .max(Comparator.comparing(Entry::getValue));
        BigDecimal maxAllocateRatio = BigDecimal.ZERO;
        if (maxOptional.isPresent()) {
            maxAllocateRatio = maxOptional.get().getValue();
        }
        return new BigScreenResult(maxAllocateRatio);
    }

    /**
     * 24小时最大资源使用量
     *
     * @return {@code BigScreenResult}
     * @since 2.4.1
     */
    @AuthorizeOss(action = BIG_SCREEN.PA)
    @ApiOperation("24小时最大资源使用量")
    @GetMapping("day/maxAllocate")
    public BigScreenResult getDayAllocate() {
        Map<String, Map<String, BigDecimal>> resourceMap = getResourceMap();
        Map<String, BigDecimal> allocateMap = resourceMap.get("24小时资源使用趋势");
        Optional<Entry<String, BigDecimal>> entryOptional = allocateMap.entrySet()
            .stream()
            .max(Comparator.comparing(Entry::getValue));
        BigDecimal maxAllocate= BigDecimal.ZERO;
        if (entryOptional.isPresent()) {
            maxAllocate = entryOptional.get().getValue();
        }
        return new BigScreenResult(maxAllocate);
    }

    /**
     * 24小时资源使用量/使用率趋势
     *
     * @return {@code BigScreenResult}
     * @since 2.4.1
     */
    @AuthorizeOss(action = BIG_SCREEN.PA)
    @ApiOperation("24小时资源使用趋势&24小时资源使用率趋势")
    @GetMapping("day/allocate/trend")
    public BigScreenResult getDayAllocateRatioTrend() {
        Map<String, Map<String, BigDecimal>> resourceMap = getResourceMap();
        return new BigScreenResult(resourceMap);
    }

    /**
     * 24小时最大作业量查询
     *
     * @return {@code BigScreenResult}
     * @since 2.4.1
     */
    @AuthorizeOss(action = BIG_SCREEN.PA)
    @ApiOperation("24小时最大作业量查询")
    @GetMapping("maxJobCount")
    public BigScreenResult getMaxJobCount() {
        long endTime = System.currentTimeMillis();
        long startTime = endTime - 60L * 60L * 24L * 1000L;
        Date endDate = new Date(endTime);
        Date startDate = new Date(startTime);
        Criteria criteria = new Criteria().andOperator(Criteria.where("searchTime").gte(startDate),
                Criteria.where("searchTime").lte(endDate));
        Query query = new Query(criteria);

        MongoCursor<Document> modelArtTrainingJobCursor = mongoTemplate.getCollection("modelarts_training_jobs")
                .find(query.getQueryObject())
                .noCursorTimeout(true)
                .cursor();
        List<ModelArtsTrainingJob> modelArtsTrainingJobList = new ArrayList<>();
        while (modelArtTrainingJobCursor.hasNext()) {
            ModelArtsTrainingJob modelArtsTrainingJob = BeanUtil.toBean(modelArtTrainingJobCursor.next(),
                    ModelArtsTrainingJob.class);
            modelArtsTrainingJobList.add(modelArtsTrainingJob);
        }
        Map<Date, List<ModelArtsTrainingJob>> collect = modelArtsTrainingJobList.stream()
                .collect(Collectors.groupingBy(
                        ModelArtsTrainingJob::getCreateStartTime));
        List<Integer> countList = new ArrayList<>();
        for (Map.Entry entry : collect.entrySet()) {
            List<ModelArtsTrainingJob> modelArtsTrainingJobs = (List<ModelArtsTrainingJob>) entry.getValue();
            int totalJobCount = 0;
            for (ModelArtsTrainingJob modelArtsTrainingJob : modelArtsTrainingJobs) {
                if (StringUtil.isAllNotEmpty(modelArtsTrainingJob.getJobCount())) {
                    int jobCount = Integer.parseInt(modelArtsTrainingJob.getJobCount());
                    totalJobCount += jobCount;
                }
            }
            countList.add(totalJobCount);
        }
        if (CollectionUtil.isNotEmpty(countList)){
            Optional<Integer> integerOptional = countList.stream().max(Comparator.comparing(Integer::intValue));
            int maxJobCount = 0;
            if (integerOptional.isPresent()) {
                maxJobCount = integerOptional.get().intValue();
            }
            return new BigScreenResult(maxJobCount);
        } else {
            return new BigScreenResult(0);
        }
    }

    /**
     * 当前排队任务数
     * @since 2.4.1
     */
    @AuthorizeOss(action = BIG_SCREEN.PA)
    @ApiOperation("当前排队任务数")
    @GetMapping("companies/jobs/waitingNum")
    public BigScreenResult waitingJobNum() {
        int pendingJobNum = getJobNum(7, "Pending");
        return new BigScreenResult(pendingJobNum);
    }

    /**
     * 当前运行任务数
     *
     * @return {@code BigScreenResult}
     * @since 2.4.1
     */
    @AuthorizeOss(action = BIG_SCREEN.PA)
    @ApiOperation("当前运行任务数")
    @GetMapping("companies/jobs/runningNum")
    public BigScreenResult runningJobNum() {
        int runningJobNum = getJobNum(8, "Running");
        return new BigScreenResult(runningJobNum);
    }

    /**
     * 专属资源池作业信息列表
     *
     * @return {@code BigScreenResult}
     * @since 2.4.1
     */
    @AuthorizeOss(action = BIG_SCREEN.PA)
    @ApiOperation("专属资源池作业信息列表")
    @GetMapping("privatePool/job/list")
    public BigScreenResult getJobInfo() {
        Map<String, PrivatePoolTrainingJob> jobMap = new HashMap<>(50);
        List<DescribeBigScreenNpuConfigResponse> responseList = aiBigscreenNpuConfigMapper.selectList(new cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria());

        List<String> specList = responseList.stream().map(DescribeBigScreenNpuConfigResponse::getNpuFlavor).distinct().collect(Collectors.toList());

        Map<String, DescribeBigScreenNpuConfigResponse> resFlavorMap = new HashMap<>(8);
        Map<String, DescribeBigScreenNpuConfigResponse> npuFlavorIdMap = new HashMap<>(8);
        if(CollectionUtil.isNotEmpty(responseList)){
            resFlavorMap = responseList.stream()
                .collect(Collectors.toMap(
                    DescribeBigScreenNpuConfigResponse::getResFlavor,
                    response -> response, (k1, k2) -> k1));
            npuFlavorIdMap =  responseList.stream()
                .collect(Collectors.toMap(
                    DescribeBigScreenNpuConfigResponse::getId,
                    response -> response, (k1, k2) -> k1));

        }

        TrainingActiveJobsRequest base = new TrainingActiveJobsRequest();
        TrainingActiveJobsV2Request baseV2 = new TrainingActiveJobsV2Request();
        TrainingActiveJobsResult trainingActiveJobsResult = (TrainingActiveJobsResult) getActiveTrainingJobs(base);
        TrainingActiveJobsV2Result trainingActiveJobsV2Result = (TrainingActiveJobsV2Result) getActiveTrainingJobs(baseV2);
        if (Objects.nonNull(trainingActiveJobsResult) && Objects.nonNull(trainingActiveJobsResult.getJobs())) {
            Map<String, List<JobsDTO>> v1JobMap = trainingActiveJobsResult.getJobs()
                    .stream()
                    .collect(Collectors.groupingBy(
                            JobsDTO::getPool_id));
            for (Map.Entry entry : v1JobMap.entrySet()) {
                String poolId = (String) entry.getKey();
                List<JobsDTO> v1JobList = (List<JobsDTO>) entry.getValue();
                PrivatePoolTrainingJob privatePoolTrainingJob = new PrivatePoolTrainingJob();
                BigDecimal totalAllocate = BigDecimal.ZERO;
                BigDecimal totalJobNum = BigDecimal.ZERO;
                for (JobsDTO jobsDTO : v1JobList){
                    String flavorInfo = jobsDTO.getFlavor_info();
                    Integer status = jobsDTO.getStatus();
                    FlavorDTO flavorDTO = JSON.parseObject(flavorInfo, FlavorDTO.class);
                    NpuDTO npu = flavorDTO.getFlavor_info().getNpu();
                    Integer unitNum = npu.getUnit_num();
                    String productName = npu.getProduct_name();
                    if (specList.contains(productName) && status == 8) {
                        totalAllocate = totalAllocate.add(BigDecimal.valueOf(unitNum).divide(BigDecimal.valueOf(1000),RoundingMode.HALF_UP));
                        totalJobNum = totalJobNum.add(BigDecimal.ONE);
                    }
                }
                privatePoolTrainingJob.setJobNum(String.valueOf(totalJobNum));
                privatePoolTrainingJob.setAllocate(String.valueOf(totalAllocate));
                jobMap.put(poolId,privatePoolTrainingJob);
            }

        }
        if (Objects.nonNull(trainingActiveJobsV2Result) && Objects.nonNull(trainingActiveJobsV2Result.getItems())) {
            Map<String, List<ItemsDTOXX>> v2JobMap = trainingActiveJobsV2Result.getItems()
                    .stream()
                    .collect(Collectors.groupingBy(
                            itemsDTOXX -> {
                                String spec = itemsDTOXX.getSpec();
                                SpecDTO specDTO = JsonUtil.fromJson(
                                        spec, SpecDTO.class);
                                return specDTO.getResource()
                                        .getPoolInfo()
                                        .getPoolId();
                            }));


            for (Map.Entry entry : v2JobMap.entrySet()) {
                String poolId = (String) entry.getKey();
                List<ItemsDTOXX> itemsDTOXXList = (List<ItemsDTOXX>) entry.getValue();
                PrivatePoolTrainingJob privatePoolTrainingJob = new PrivatePoolTrainingJob();
                BigDecimal totalAllocate = BigDecimal.ZERO;
                BigDecimal totalJobNum = BigDecimal.ZERO;

                ConcurrentHashMap<String,BigDecimal> parentAndVirCoreNumMap = new ConcurrentHashMap<>();
                for (ItemsDTOXX itemsDTOXX : itemsDTOXXList) {
                    StatusDTO statusDTO = JSON.parseObject(itemsDTOXX.getStatus(), StatusDTO.class);
                    String phase = statusDTO.getPhase();
                    if (MaPoolStatus.RUNNING.equals(phase)) {
                        SpecDTO specDTO = JsonUtil.fromJson(itemsDTOXX.getSpec(), SpecDTO.class);
                        String poolResorceFlavor = specDTO.getResource().getPoolResourceFlavor();
                        if (StrUtil.isNotBlank(poolResorceFlavor)) {
                            DescribeBigScreenNpuConfigResponse npuConfigResponse = resFlavorMap.get(poolResorceFlavor);
                            if (npuConfigResponse != null) {
                                String parentId = npuConfigResponse.getParentId();
                                BigDecimal totalVirCoreNum = parentAndVirCoreNumMap.get(parentId);
                                if (totalVirCoreNum == null) {
                                    totalVirCoreNum = BigDecimal.ZERO;
                                }
                                //虚拟卡核心数
                                Integer vcoreNum = npuConfigResponse.getCoreNum();
                                if (null != specDTO.getResource().getPoolInfo().getAcceleratorNum()) {
                                    //虚拟卡npu卡数
                                    Integer num = specDTO.getResource().getPoolInfo().getAcceleratorNum();
                                    //虚拟卡乘虚拟卡核心数
                                    totalVirCoreNum = (BigDecimal.valueOf(num)).multiply(BigDecimal.valueOf(vcoreNum)).add(totalVirCoreNum);
                                }
                                parentAndVirCoreNumMap.put(parentId, totalVirCoreNum);
                            }
                        } else {

                            PoolInfoDTO pool_info = specDTO.getResource().getPoolInfo();
                            String accelerator = pool_info.getAccelerator();
                            Integer accelerator_num = pool_info.getAcceleratorNum();
                            if (specList.contains(accelerator) && MaPoolStatus.RUNNING.equals(phase)) {
                                totalAllocate = totalAllocate.add(NumberUtil.mul(specDTO.getResource().getNodeCount(),BigDecimal.valueOf(accelerator_num)));
                            }
                        }
                        totalJobNum = totalJobNum.add(BigDecimal.ONE);
                    }
                }

                //虚拟卡转换实体卡
                for (Entry<String, BigDecimal> virCoreNumEntry : parentAndVirCoreNumMap.entrySet()) {
                    String parentId = virCoreNumEntry.getKey();
                    DescribeBigScreenNpuConfigResponse npuParentConfigResponse = npuFlavorIdMap.get(parentId);
                    if (npuParentConfigResponse != null) {
                        Integer parentCoreNum = npuParentConfigResponse.getCoreNum();
                        if (parentCoreNum != 0) {
                            totalAllocate = totalAllocate.add(NumberUtil.div(virCoreNumEntry.getValue(),parentCoreNum).setScale(0,BigDecimal.ROUND_UP));
                        }
                    }
                }
                //v2可能存在v1相同的poolId,需要加起来
                privatePoolTrainingJob.setJobNum(String.valueOf(totalJobNum));
                privatePoolTrainingJob.setAllocate(String.valueOf(totalAllocate));
                jobMap.put(poolId,privatePoolTrainingJob);
            }
        }

        log.info("OssBigScreenController.getJobInfo jobMap: {}", JSON.toJSONString(jobMap));

//      查询专属池
        long fiveMinutes = 60 * 1000 * 5L;
        long endTime = System.currentTimeMillis();
        long startTime = endTime - fiveMinutes;
        List<ModelArtsPoolAllocate> privateModalarts = getModelArts("private", startTime, endTime);
        //未查询到数据查询，上个5分钟数据，BUG：#66782 优化显示
        if (CollectionUtils.isEmpty(privateModalarts)) {
            privateModalarts = getModelArts("private", startTime-fiveMinutes, startTime);
        }
        Map<String, String> privateModalArtsPoolMap = privateModalarts.stream()
                .collect(Collectors.toMap(ModelArtsPoolAllocate::getPoolId,
                        ModelArtsPoolAllocate::getPoolName));

        Map<String,PrivatePoolTrainingJob> jobResultMap = new HashMap<>(50);
        for (Map.Entry entry : jobMap.entrySet()) {
            String poolId = (String) entry.getKey();
            PrivatePoolTrainingJob privatePoolTrainingJob = (PrivatePoolTrainingJob) entry.getValue();
            if (privateModalArtsPoolMap.containsKey(poolId)){
                String poolName = privateModalArtsPoolMap.get(poolId);
                privatePoolTrainingJob.setPoolName(poolName);
                jobResultMap.put(poolId,privatePoolTrainingJob);
            }
        }

        log.info("OssBigScreenController.getJobInfo jobResultMap: {}", JSON.toJSONString(jobResultMap));

        List<PrivatePoolTrainingJob> privatePoolTrainingJobList = jobResultMap.values().stream()
                .filter(privatePoolTrainingJob ->
                        !("0".equals(
                                privatePoolTrainingJob.getAllocate())
                                && "0".equals(
                                privatePoolTrainingJob.getJobNum())))
                .sorted(new Comparator<PrivatePoolTrainingJob>() {
                    @Override
                    public int compare(
                            PrivatePoolTrainingJob o1,
                            PrivatePoolTrainingJob o2) {
                        return Integer.parseInt(o1.getAllocate()) - Integer.parseInt(o2.getAllocate());


                    }
                }.thenComparing(
                                new Comparator<PrivatePoolTrainingJob>() {
                                    @Override
                                    public int compare(
                                            PrivatePoolTrainingJob o1,
                                            PrivatePoolTrainingJob o2) {
                                        return Integer.parseInt(o1.getJobNum()) - Integer.parseInt(o2.getJobNum());
                                    }
                                })
                        .reversed())
                .collect(Collectors.toList());
        privatePoolTrainingJobList.forEach(privatePoolTrainingJob -> {
            privatePoolTrainingJob.setAllocate(privatePoolTrainingJob.getAllocate() + "卡");
            privatePoolTrainingJob.setJobNum(privatePoolTrainingJob.getJobNum() + "个");
        });
        return new BigScreenResult(privatePoolTrainingJobList);
    }

    /**
     * 【Since v2.5.0】 意向企业
     *
     * <br> 兼容老大屏
     *
     * @return {@link BigScreenResult}
     * @since 2.4.1
     */
    @AuthorizeOss(action = BIG_SCREEN.PA)
    @ApiOperation("意向企业")
    @GetMapping("companies/intendedNum")
    public BigScreenResult intendedNum(){
        String property = PropertiesUtil.getProperty("screen.airesource.intended-company");
        return new BigScreenResult(property);
    }

    /**
     * 【Since v2.5.0】 bertlatrg模型数据
     *
     * <br> 兼容老大屏
     *
     * @return {@link BigScreenResult}
     */
    @AuthorizeOss(action = BIG_SCREEN.PA)
    @ApiOperation("bertlatrg")
    @GetMapping("bertlatrg")
    public BigScreenResult bertlatrg(){
        String property = PropertiesUtil.getProperty("screen.airesource.bert-latry");
        String[] split = property.split(",");
        if(split.length>1){

            Integer min = Integer.valueOf(split[0]);
            Integer max = Integer.valueOf(split[1]);
            if(min>max){
                Integer temp = min;
                min=max;
                max=temp;
            }
            return new BigScreenResult(RandomUtils.nextInt(min, max + 1));

        }else {
            return new BigScreenResult(property);
        }
    }

    /**
     * 【Since v2.5.0】 月AI资源占用
     *
     * <br> 兼容老大屏
     *
     * @return {@link BigScreenResult}
     */
    @AuthorizeOss(action = BIG_SCREEN.PA)
    @ApiOperation("月AI资源占用")
    @GetMapping("monthAiResources")
    public BigScreenResult monthAiResources(){
        Date current = new Date();
        String monthResUsage = bizResUsageMonReportService.getMonthResUsage(DateFormatUtils.format(current, yyyyMM));
        //有效算力评分
        return new BigScreenResult(monthResUsage);
    }
    /**
     * 【Since v2.5.0】 企业资源使用趋势
     *
     * <br> 兼容老大屏
     *
     * @param timeType 时间类型
     * @param orgSid   org sid
     * @return {@link BigScreenLineChartResult}
     */
    @AuthorizeOss(action = BIG_SCREEN.PA)
    @ApiOperation("企业资源使用趋势")
    @GetMapping("resUsageTend")
    public BigScreenLineChartResult resUsageTend(String timeType,Long orgSid){
        BigScreenLineChartResult bigScreenLineChartResult = new BigScreenLineChartResult();
        List<String> categories = new ArrayList<>();
        List<Date> dateList = new ArrayList<>();
        Date current = new Date();
        List<String> elements = new ArrayList<>();

        if (StringUtils.equalsIgnoreCase(timeType, Constants.YEAR)) {

            List<String> monList = new ArrayList<>();
            for (int i = 1; i <= Constants.TWELVE; i++) {
                Date date = DateUtils.addMonths(current, i-12);
                String format = DateFormatUtils.format(date, "yyyy-MM");
                categories.add(format);
                monList.add(DateFormatUtils.format(date, yyyyMM));
                try {
                    dateList.add(DateUtils.parseDate(format + "-01 00:00:00", yyyy_MM_dd_HH_mm_ss));
                } catch (ParseException e) {
                    log.error("OssBigScreenController.resUsageTend 时间解析异常",e);
                    log.error(e.getMessage());
                }
            }
            List<ResUsageResult> bizResUsageMonReports = bizResUsageMonReportService.groupByUsageTimeAndOrgSid(monList, orgSid);
            if(!CollectionUtils.isEmpty(bizResUsageMonReports)){
                Map<String, ResUsageResult> monMap = bizResUsageMonReports.stream().collect(Collectors.toMap(ResUsageResult::getUsageTime, o -> o));
                for (int i = 1; i <= Constants.TWELVE; i++) {
                    Date date = DateUtils.addMonths(current, i-12);
                    ResUsageResult monReport = monMap.get(DateFormatUtils.format(date, yyyyMM));
                    if(monReport !=null){
                        //卡时
                        elements.add(monReport.getResUsage());
                    }else{
                        elements.add("0");
                    }

                }
            }
        } else if (StringUtils.equalsIgnoreCase(timeType, Constants.WEEK) || StringUtils.equalsIgnoreCase(timeType,
                Constants.MONTH)) {
            int days = 7;
            if (StringUtils.equalsIgnoreCase(timeType, Constants.MONTH)) {
                days = 30;
            }
            List<String> dayList = new ArrayList<>();
            for (int i = 1; i <= days; i++) {
                Date date = DateUtils.addDays(current, i-days);
                String format = DateFormatUtils.format(date, yyyy_MM_dd);
                categories.add(format);
                dayList.add(DateFormatUtils.format(date, yyyyMMdd));
                try {
                    dateList.add(DateUtils.parseDate(format + " 00:00:00", yyyy_MM_dd_HH_mm_ss));
                } catch (ParseException e) {
                    log.error("OssBigScreenController.resUsageTend 时间解析异常",e);
                    log.error(e.getMessage());
                }
            }
            List<ResUsageResult> bizResUsageDayReports = bizResUsageDayReportService.groupByUsageTimeAndOrgSid(dayList, orgSid);
            if(!CollectionUtils.isEmpty(bizResUsageDayReports)){
                Map<String, ResUsageResult> dateMap = bizResUsageDayReports.stream().collect(Collectors.toMap(ResUsageResult::getUsageTime, o -> o));
                for (int i = 1; i <= days; i++) {
                    Date date = DateUtils.addDays(current, i-days);
                    ResUsageResult dayReport = dateMap.get(DateFormatUtils.format(date, yyyyMMdd));
                    if(dayReport !=null){

                        //CUE
                        elements.add(dayReport.getResUsage());
                    }else{
                        elements.add("0");
                    }
                }

            }
        }else{
            String currentDate = DateFormatUtils.format(current, yyyyMMdd);
            String nowDay = DateFormatUtils.format(current, yyyy_MM_dd);
            for (int i = 0; i < Constants.TWENTY_FOUR; i++) {
                String time = (i>9?i:"0"+i)+":00";
                DateFormatUtils.format(current, yyyy_MM_dd);
                categories.add(time);
                try {
                    dateList.add(DateUtils.parseDate(nowDay + " " + time + ":00", yyyy_MM_dd_HH_mm_ss));
                } catch (ParseException e) {
                    log.error("OssBigScreenController.resUsageTend 时间解析异常",e);
                    log.error(e.getMessage());
                }
            }
            List<ResUsageResult> bizResUsageHourReports = bizResUsageHourReportService.groupByUsageTimeAndOrgSid(currentDate, orgSid);
            if(!CollectionUtils.isEmpty(bizResUsageHourReports)){

                Map<String, ResUsageResult> timeMap = bizResUsageHourReports.stream().collect(Collectors.toMap(ResUsageResult::getUsageTime, o -> o));
                for (int i = 0; i < Constants.TWENTY_FOUR; i++) {

                    ResUsageResult bizResUsageHourReport = timeMap.get(currentDate + (i > 9 ? i : "0" + i));
                    if(bizResUsageHourReport !=null){

                        elements.add(bizResUsageHourReport.getResUsage());
                    }else{
                        elements.add("0");
                    }

                }
            }else{
                for (int i = 0; i < Constants.TWENTY_FOUR; i++) {
                    elements.add("0.00");
                }
            }

        }

        if(CollectionUtils.isEmpty(elements)){
            categories.forEach(o->elements.add("0"));
        }

        bigScreenLineChartResult.putElement("有效算力(CUE)",elements);

        //资源占比
        BigScreenOccRatioRequest ratioRequest = new BigScreenOccRatioRequest();
        ratioRequest.setDateList(dateList);
        ratioRequest.setOrgSid(orgSid);
        List<String> ratioEleList = bizResUsageDayReportService.resOccRatioList(ratioRequest);
        bigScreenLineChartResult.putElement("占用百分比",ratioEleList);


        bigScreenLineChartResult.setCategories(categories);
        return bigScreenLineChartResult;
    }


    /**
     * 【Since v2.5.0】 月企业作业提交排行
     *
     * <br> 兼容老大屏
     *
     * @param top 前
     * @return {@link List}<{@link BigScreenTableResult}>
     */
    @AuthorizeOss(action = BIG_SCREEN.PA)
    @ApiOperation("月企业作业提交排行")
    @GetMapping("companies/jobs")
    public List<BigScreenTableResult> jobs( Integer top){

        Date current = new Date();
        String currentMonth = DateFormatUtils.format(current, yyyyMM);
        List<CompanyJobsResult>  jobsResultList= bizResUsageMonReportService.topJobsGroupByOrgSid(currentMonth,top);

        List<BigScreenTableResult> resultList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(jobsResultList)){
            jobsResultList.forEach(companyJob->{

                BigScreenTableResult bigScreenTableResult = new BigScreenTableResult(companyJob.getOrgName(), companyJob.getJobs().toString()+"个");
                resultList.add(bigScreenTableResult);

            });
        }

        return resultList;
    }

    /**
     * 【Since v2.5.0】 AI算力占用率
     *
     *   <br> 兼容老大屏
     *
     * @param orgSid org sid
     * @return {@link BigScreenPieResult}
     */
    @AuthorizeOss(action = BIG_SCREEN.PA)
    @ApiOperation("AI算力占用率")
    @GetMapping("resOccupancy")
    public BigScreenPieResult resOccupancy(Long orgSid){
        BigScreenPieResult result = new BigScreenPieResult();

        Integer airesourceNum = Integer.valueOf(PropertiesUtil.getProperty("screen.airesource.num"));

        Integer usagedCards = 0;

        List<ResUsageTopResult>  resOccupancyList = bizResUsageMonReportService.resOccupancy(null,null, null);

        if(orgSid ==null || orgSid ==-1) {
            //预占用(3个小时前)
            Date current = new Date();
            current = DateUtils.addHours(current,-3);
            SysMPreOccRecord sysMPreOccRecord = sysMPreOccRecordService.getPreOccRecord(current);
            usagedCards = sysMPreOccRecord.getCard();

        }
        if(!CollectionUtils.isEmpty(resOccupancyList)){
            if(orgSid !=null && orgSid !=-1){
                Integer otherCompanyCards = 0;
                for (ResUsageTopResult res : resOccupancyList) {

                    Integer cards = res.getCards();
                    if(cards == null){
                        cards = 0;
                    }
                    if(orgSid.equals(res.getOrgSid())){
                        String ratio = calcRatio(cards, airesourceNum);
                        result.putData(res.getOrgName(), cards.toString(),"", ratio);
                        result.setResNum(cards);
                        result.setResRatio(ratio);
                        usagedCards+=cards;
                    }else{
                        otherCompanyCards+=cards;
                    }

                }
            }else{
                for (ResUsageTopResult res : resOccupancyList) {
                    Integer cards = res.getCards();

                    if(cards !=null && !cards.equals(0)){
                        usagedCards+=cards;
                    }

                }
                if(usagedCards<airesourceNum){
                    result.setResNum(usagedCards);
                    result.setResRatio(calcRatio(usagedCards,airesourceNum));
                }else{
                    result.setResNum(airesourceNum);
                    result.setResRatio("100");
                }
                if(usagedCards>0){
                    result.putData("占用",String.valueOf(usagedCards),"",calcRatio(usagedCards,airesourceNum));
                }
            }

        }

        if(usagedCards<airesourceNum){
            int remain = airesourceNum - usagedCards;
            result.putData("未占用",String.valueOf(remain),"",calcRatio(remain,airesourceNum));
        }
        return result;
    }

    /**
     * 【Since v2.5.0】 实时企业资源占用排行（延时3小时）
     *
     * <br> 兼容老大屏
     *
     * @param top top
     * @return {@link List}<{@link BigScreenTableResult}>
     */
    @AuthorizeOss(action = BIG_SCREEN.PA)
    @ApiOperation("实时企业资源占用排行（延时3小时）")
    @GetMapping("resUsageTop")
    public List<BigScreenTableResult> resUsageTop(Integer top){

        List<ResUsageTopResult>  resUsageTopList= bizResUsageMonReportService.resOccupancy(top,null, null);

        List<BigScreenTableResult> resultList = new ArrayList<>();
        if(!CollectionUtils.isEmpty(resUsageTopList)){
            resUsageTopList.forEach(resUsage->{

                BigScreenTableResult bigScreenTableResult = new BigScreenTableResult(resUsage.getOrgName(), resUsage.getCards().toString()+"卡");
                resultList.add(bigScreenTableResult);

            });
        }

        return resultList;
    }

        /**
     * 【Since v2.5.0】 月企业资源使用排行
     *
     * <br> 兼容老大屏
     *
     * @param top top
     * @return {@link List}<{@link BigScreenTableResult}>
     */
    @AuthorizeOss(action = BIG_SCREEN.PA)
    @ApiOperation("月企业资源使用排行")
    @GetMapping("resMonUsageTop")
    public List<BigScreenTableResult> resMonUsageTop(Integer top) {

        Date current = new Date();
        String currentMonth = DateFormatUtils.format(current, yyyyMM);
        List<ResMonUsageTopResult> resMonUsageTopList = bizResUsageMonReportService.resMonUsageTop(currentMonth, top);

        List<BigScreenTableResult> resultList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(resMonUsageTopList)) {
            resMonUsageTopList.forEach(resMonUsage -> {

                BigScreenTableResult bigScreenTableResult = new BigScreenTableResult(resMonUsage.getOrgName(),
                        "CUE " + resMonUsage.getResUsage());
                resultList.add(bigScreenTableResult);

            });
        }

        return resultList;
    }
    /**
     * 详情
     *
     * @param id id
     * @return {@link RestResult}<{@link VisualDTO}>
     * @since 2.4.1
     */
    @GetMapping("/detail")
    @ApiOperation(value = "详情", notes = "传入visual")
    @AuthorizeOss(action = BIG_SCREEN.PA)
    public RestResult<VisualDTO> detail(@RequestParam Long id) {
        return new RestResult<>(visualService.detail(id));
    }


    /**
     * 获得公众转卡容量
     *
     * @return {@code BigScreenResult}
     * @since 2.4.1
     */
    @AuthorizeOss(action = BIG_SCREEN.PA)
    @ApiOperation("资源池信息")
    @GetMapping("NPUCard/info")
    public BigScreenResult getPublicNpuCardCapacity() {
        long endTime = System.currentTimeMillis();
        long startTime = endTime - 60L * 1000L * 5L;
        List<ModelArtsPoolAllocate> modelArtsPoolAllocateList = getModelArts(null, startTime, endTime);
        NpuCardInfo npuCardInfo = getNpuCardInfo(modelArtsPoolAllocateList);
        //ModelArts BMS NPU卡使用占比
        List<BizUserAiBmsRecord> bizUserAiBmsRecords = getBizUserAiBmsRecords(startTime, endTime);
        int bmsNpu = bizUserAiBmsRecords.stream().mapToInt(BizUserAiBmsRecord::getNpuAllocation).sum();
        BigDecimal totalCapacity = BigDecimal.valueOf(npuCardInfo.getCapacity());
        totalCapacity = totalCapacity.add(BigDecimal.valueOf(bmsNpu));
        BigDecimal totalAllocate = BigDecimal.valueOf(npuCardInfo.getAllocate());
        totalAllocate = totalAllocate.add(BigDecimal.valueOf(bmsNpu));
        if (totalCapacity.compareTo(BigDecimal.ZERO) > 0) {
            npuCardInfo.setAllocateRatio(
                    totalAllocate.divide(totalCapacity, 4, BigDecimal.ROUND_HALF_UP).doubleValue());
            npuCardInfo.setCapacity(totalCapacity.doubleValue());
            npuCardInfo.setAllocate(totalAllocate.doubleValue());
        }
        return new BigScreenResult(npuCardInfo);
    }


    private String calcRatio(Integer cards,Integer allResNum){
        return NumberUtil.mul(NumberUtil.div(cards,allResNum),100).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
    }

    private Integer getValue(String property) {
        String[] split = property.split(",");
        int value = 0;
        if (split.length > 1) {
            Integer min = Integer.valueOf(split[0]);
            Integer max = Integer.valueOf(split[1]);
            if (min > max) {
                Integer temp = min;
                min = max;
                max = temp;
            }
            value = RandomUtils.nextInt(min, max + 1);
        } else {
            value = Integer.parseInt(split[0]);
        }
        return value;
    }

    private List<ModelArtsPoolAllocate> getModelArts(String poolType, Long startTime, Long endTime) {
        Date startDate = new Date(startTime);
        Date endDate = new Date(endTime);
        Criteria criteria1 = Criteria.where("createDt").gt(startDate);
        Criteria criteria2 = Criteria.where("createDt").lt(endDate);
        Criteria criteria3 = new Criteria();
        if (StringUtil.isNotEmpty(poolType)) {
            criteria3 = Criteria.where("poolType").is(poolType);
        }
        Criteria criteria = new Criteria().andOperator(criteria1, criteria2, criteria3);
        Query query = new Query().addCriteria(criteria);
        List<ModelArtsPoolAllocate> modelArtsPoolAllocates = new ArrayList<>();
        MongoCursor<Document> modelArtsCursor = mongoTemplate.getCollection("modelarts_pool_allocate")
                .find(query.getQueryObject())
                .noCursorTimeout(true)
                .cursor();
        while (modelArtsCursor.hasNext()) {
            ModelArtsPoolAllocate modelArtsPoolAllocate = BeanUtil.toBean(modelArtsCursor.next(),
                    ModelArtsPoolAllocate.class);
            modelArtsPoolAllocates.add(modelArtsPoolAllocate);
        }
        modelArtsCursor.close();
        return modelArtsPoolAllocates;
    }

    private NpuCardInfo getNpuCardInfo(List<ModelArtsPoolAllocate> modelArtsPoolAllocateList) {
        BigDecimal totalCapacity = BigDecimal.ZERO;
        BigDecimal totalAllocate = BigDecimal.ZERO;
        BigDecimal allocateRatio = BigDecimal.ZERO;
        //      找到notebook和推理的公共资源池id
        String configPubPoolIds = cn.com.cloudstar.rightcloud.oss.common.util.PropertiesUtil.getProperty(
                "screen.airesource.public.poolids");
        List<String> poolIds = Arrays.stream(configPubPoolIds.split(StrUtil.COMMA)).collect(Collectors.toList());
        for (ModelArtsPoolAllocate modelArtsPoolAllocate : modelArtsPoolAllocateList) {
            //逻辑池不统计
            if (ModelartsPoolAllocateType.LOGICAL.equalsIgnoreCase(modelArtsPoolAllocate.getType())) {
                continue;
            }
            double capacity = Double.parseDouble(modelArtsPoolAllocate.getCapacity());
            double allocate = Double.parseDouble(modelArtsPoolAllocate.getAllocate());
            boolean condition = (capacity >= 0 && allocate >= 0);
            boolean otherCondition = (capacity >= 0 && allocate < 0);
            if (condition || otherCondition) {
                totalCapacity = totalCapacity.add(BigDecimal.valueOf(capacity));
                if ("private".equals(modelArtsPoolAllocate.getPoolType())) {
                    totalAllocate = totalAllocate.add(BigDecimal.valueOf(capacity));
                } else {
                    if (!poolIds.contains(modelArtsPoolAllocate.getPoolId())) {
                        if (allocate > capacity) {
                            totalAllocate = totalAllocate.add(BigDecimal.valueOf(capacity));
                        } else {
                            totalAllocate = totalAllocate.add(BigDecimal.valueOf(allocate));
                        }
                    } else {
                        if (allocate < 0) {
                            continue;
                        } else  {
                            totalAllocate = totalAllocate.add(BigDecimal.valueOf(capacity));
                        }

                    }
                }
            }
        }
        BigDecimal totalFree = totalCapacity.subtract(totalAllocate);
        if (totalCapacity.compareTo(BigDecimal.ZERO) == 1) {
            allocateRatio = totalAllocate.divide(totalCapacity, 4, BigDecimal.ROUND_HALF_UP);
        }
        NpuCardInfo npuCardInfo = new NpuCardInfo();
        npuCardInfo.setAllocate(totalAllocate.doubleValue());
        npuCardInfo.setCapacity(totalCapacity.doubleValue());
        npuCardInfo.setFreeNum(totalFree.doubleValue());
        npuCardInfo.setAllocateRatio(allocateRatio.doubleValue());
        return npuCardInfo;
    }

    private NpuCardInfo getNpuCardInfoAll(List<ModelArtsPoolAllocate> modelArtsPoolAllocateList) {
        BigDecimal totalCapacity = BigDecimal.ZERO;
        BigDecimal totalAllocate = BigDecimal.ZERO;
        //      找到notebook和推理的公共资源池id
        String configPubPoolIds = cn.com.cloudstar.rightcloud.oss.common.util.PropertiesUtil.getProperty(
                "screen.airesource.public.poolids");
        List<String> poolIds = Arrays.stream(configPubPoolIds.split(StrUtil.COMMA)).collect(Collectors.toList());
        for (ModelArtsPoolAllocate modelArtsPoolAllocate : modelArtsPoolAllocateList) {
            //逻辑池不统计
            if (ModelartsPoolAllocateType.LOGICAL.equalsIgnoreCase(modelArtsPoolAllocate.getType())) {
                continue;
            }
            double capacity = Double.parseDouble(modelArtsPoolAllocate.getCapacity());
            double allocate = Double.parseDouble(modelArtsPoolAllocate.getAllocate());
            boolean condition = (capacity >= 0 && allocate >= 0);
            boolean otherCondition = (capacity >= 0 && allocate < 0);
            if (condition || otherCondition) {
                totalCapacity = totalCapacity.add(BigDecimal.valueOf(capacity));
                if ("private".equals(modelArtsPoolAllocate.getPoolType())) {
                    //大屏定时任务修改增加了算力转换系数和npu规格
                    if(Objects.nonNull(modelArtsPoolAllocate.getConvertRatio())){
                        totalAllocate = totalAllocate.add(BigDecimal.valueOf(capacity).multiply(modelArtsPoolAllocate.getConvertRatio()));
                    }else{
                        totalAllocate = totalAllocate.add(BigDecimal.valueOf(capacity));
                    }
                } else {
                    if (!poolIds.contains(modelArtsPoolAllocate.getPoolId())) {
                        if (allocate > capacity) {
                            //大屏定时任务修改增加了算力转换系数和npu规格
                            if(Objects.nonNull(modelArtsPoolAllocate.getConvertRatio())){
                                totalAllocate = totalAllocate.add(BigDecimal.valueOf(capacity).multiply(modelArtsPoolAllocate.getConvertRatio()));
                            }else{
                                totalAllocate = totalAllocate.add(BigDecimal.valueOf(capacity));
                            }
                        } else {
                            //大屏定时任务修改增加了算力转换系数和npu规格
                            if(Objects.nonNull(modelArtsPoolAllocate.getConvertRatio())){
                                totalAllocate = totalAllocate.add(BigDecimal.valueOf(allocate).multiply(modelArtsPoolAllocate.getConvertRatio()));
                            }else{
                                totalAllocate = totalAllocate.add(BigDecimal.valueOf(allocate));
                            }
                        }
                    } else {
                        if (allocate < 0) {
                            continue;
                        } else  {
                            //大屏定时任务修改增加了算力转换系数和npu规格
                            if(Objects.nonNull(modelArtsPoolAllocate.getConvertRatio())){
                                totalAllocate = totalAllocate.add(BigDecimal.valueOf(capacity).multiply(modelArtsPoolAllocate.getConvertRatio()));
                            }else{
                                totalAllocate = totalAllocate.add(BigDecimal.valueOf(capacity));
                            }
                        }

                    }
                }
            }

        }

        NpuCardInfo npuCardInfo = new NpuCardInfo();
        npuCardInfo.setAllocate(totalAllocate.doubleValue());
        return npuCardInfo;
    }

    private Map<String, Map<String, BigDecimal>> getResourceMap() {
        long endTime = System.currentTimeMillis();
        long startTime = endTime - 60L * 60L * 24L * 1000L;
//      找到notebook和推理的资源池id
        String configPubPoolIds = cn.com.cloudstar.rightcloud.oss.common.util.PropertiesUtil.getProperty(
                "screen.airesource.public.poolids");
        List<String> poolIds = Arrays.stream(configPubPoolIds.split(StrUtil.COMMA)).collect(Collectors.toList());
        Map<String, BigDecimal> allocateRatioTrendMap = new TreeMap<>();
        Map<String, BigDecimal> allocateTrendMap = new TreeMap<>();
        List<ModelArtsPoolAllocate> modelArts = getModelArts(null, startTime, endTime);
        Map<String, List<ModelArtsPoolAllocate>> collect = modelArts.stream()
                .collect(Collectors.groupingBy(
                        modelArtsPoolAllocate -> DateFormatUtils
                                .format(
                                        modelArtsPoolAllocate.getCreateDt()
                                                .getTime(),
                                        yyyy_MM_dd_HH_mm_ss)));
        // ModelArts BMS资源趋势
        List<BizUserAiBmsRecord> bizUserAiBmsRecords = getBizUserAiBmsRecords(startTime, endTime);
        Map<String, List<BizUserAiBmsRecord>> bmsMaps = bizUserAiBmsRecords.stream()
                                                                           .collect(Collectors.groupingBy(
                                                                                   bizUserAiBmsRecord -> DateFormatUtils
                                                                                           .format(
                                                                                                   bizUserAiBmsRecord.getCreatedDt()
                                                                                                                     .getTime(),
                                                                                                   yyyy_MM_dd_HH_mm_ss)));
        Long flag = endTime / (60L * 10L * 1000L);
        for (int i = 0; i < TIME_NODE; i++) {
            long timestamp = flag * 60L * 10L * 1000L;
            String date = DateFormatUtils.format(timestamp, yyyy_MM_dd_HH_mm_ss);
            flag = flag - 1L;
            List<ModelArtsPoolAllocate> modelArtsPoolAllocates = collect.get(date);
            BigDecimal ratio = BigDecimal.ZERO;
            BigDecimal totalAllocate = BigDecimal.ZERO;
            BigDecimal totalCapacity = BigDecimal.ZERO;
            if (!CollectionUtils.isEmpty(modelArtsPoolAllocates)) {
                for (ModelArtsPoolAllocate modelArtsPoolAllocate : modelArtsPoolAllocates) {
                    //逻辑池不统计
                    if (ModelartsPoolAllocateType.LOGICAL.equalsIgnoreCase(modelArtsPoolAllocate.getType())) {
                        continue;
                    }
                    Double capacity = Double.parseDouble(modelArtsPoolAllocate.getCapacity());
                    Double allocate = Double.parseDouble(modelArtsPoolAllocate.getAllocate());
                    boolean condition = (capacity >= 0 && allocate >= 0);
                    boolean otherCondition = (capacity >= 0 && allocate < 0);
                    if (condition || otherCondition) {
                        totalCapacity = totalCapacity.add(BigDecimal.valueOf(capacity));
                        if ("private".equals(modelArtsPoolAllocate.getPoolType())) {
                            totalAllocate = totalAllocate.add(BigDecimal.valueOf(capacity));
                        } else {
                            if (!poolIds.contains(modelArtsPoolAllocate.getPoolId())) {
                                if (allocate > capacity) {
                                    totalAllocate = totalAllocate.add(BigDecimal.valueOf(capacity));
                                } else {
                                    totalAllocate = totalAllocate.add(BigDecimal.valueOf(allocate));
                                }
                            } else {

                                if (allocate < 0) {
                                    continue;
                                } else  {
                                    totalAllocate = totalAllocate.add(BigDecimal.valueOf(capacity));
                                }
                            }
                        }

                    }
                }
                if (totalCapacity.compareTo(BigDecimal.ZERO) == 1) {
                    ratio = totalAllocate.divide(totalCapacity, 4, BigDecimal.ROUND_HALF_UP);
                    allocateTrendMap.put(date, totalAllocate);
                    allocateRatioTrendMap.put(date, ratio);
                } else {
                    allocateTrendMap.put(date, BigDecimal.ZERO);
                    allocateRatioTrendMap.put(date, BigDecimal.ZERO);
                }

                List<BizUserAiBmsRecord> bizUserAiBmsRecordsDate = bmsMaps.get(date);
                if (!CollectionUtils.isEmpty(bizUserAiBmsRecordsDate)) {
                    int bmsNpu = bizUserAiBmsRecordsDate.stream().mapToInt(BizUserAiBmsRecord::getNpuAllocation).sum();
                    totalAllocate = totalAllocate.add(BigDecimal.valueOf(bmsNpu));
                    totalCapacity = totalCapacity.add(BigDecimal.valueOf(bmsNpu));
                    ratio = totalAllocate.divide(totalCapacity, 4, BigDecimal.ROUND_HALF_UP);
                }

                allocateTrendMap.put(date, totalAllocate);
                allocateRatioTrendMap.put(date, ratio);
            }

        }
        Map<String, Map<String, BigDecimal>> resourceMap = new HashMap<>(50);
        resourceMap.put("24小时资源使用趋势", allocateTrendMap);
        resourceMap.put("24小时资源使用率趋势", allocateRatioTrendMap);
        return resourceMap;
    }

    private int getJobNum(int status, String phase) {
        TrainingActiveJobsRequest base = new TrainingActiveJobsRequest();
        TrainingActiveJobsV2Request baseV2 = new TrainingActiveJobsV2Request();
        base.setStatus(status);
        baseV2.setPhase(phase);
        int totalCount = 0;
        TrainingActiveJobsResult trainingActiveJobsResult = (TrainingActiveJobsResult) getActiveTrainingJobs(base);
        TrainingActiveJobsV2Result trainingActiveJobsV2Result = (TrainingActiveJobsV2Result) getActiveTrainingJobs(
                baseV2);
        if ((!ObjectUtils.isEmpty(trainingActiveJobsResult)) && (!ObjectUtils.isEmpty(
                trainingActiveJobsResult.getCount()))) {
            totalCount += trainingActiveJobsResult.getCount();
        }
        if ((!ObjectUtils.isEmpty(trainingActiveJobsV2Result)) && (!ObjectUtils.isEmpty(
                trainingActiveJobsV2Result.getCount()))) {
            totalCount += trainingActiveJobsV2Result.getCount();
        }
        return totalCount;
    }

    private BaseResult getActiveTrainingJobs(Base base) {
        List<CloudEnv> cloudEnvs = cloudEnvRemoteService.selectAllCloudEnv();
        Optional<CloudEnv> envOptional = cloudEnvs.stream()
            .filter(env -> StringUtils.containsIgnoreCase("hcso", env.getCloudEnvType()))
            .findFirst();
        CloudEnv cloudEnv = null;
        if (envOptional.isPresent()) {
            cloudEnv = envOptional.get();
        }
        if (cloudEnv == null) {
            log.info("hcso cloudEnv is empty");
            return null;
        }
        try {
            String attrData = cloudEnv.getAttrData();
            Map<String, String> params;
            Map<String, Object> paramObjects = JsonUtil.fromJson(CrytoUtilSimple.decrypt(attrData, true),
                                                                 new TypeReference<Map<String, Object>>() {
                                                                 });
            params = paramObjects.entrySet().stream().collect(Collectors.toMap(
                    Map.Entry::getKey,
                    entry -> entry.getValue() == null ? null : entry.getValue().toString()));
            base.setProviderUrl(params.get(CloudEnvTenantKey.PROVIDER_URL));
            base.setTenantId(params.get(CloudEnvTenantKey.TENANT_ID));
            base.setApiKey(params.get(CloudEnvTenantKey.API_KEY));
            base.setSecureToken(params.get(CloudEnvTenantKey.SECURE_TOKEN));
            base.setDomain(params.get(CloudEnvTenantKey.DOMAINID));
            base.setRegion(cloudEnv.getRegion());
            base.setVirtEnvType("hcso");
            base.setProviderType("HCSO");
            Properties sysProps = System.getProperties();
            base.setVirtEnvUuid(sysProps.getProperty("cloudstar.mq.queue", "dev"));
            HttpServletRequest request = WebUtil.getRequest();
            if (request != null) {
                String reqSource = request.getHeader(REQ_SOURCE);
                base.setReqSource(null == reqSource ? "" : reqSource);
            }
            User authUser = BasicInfoUtil.getAuthUser();
            if (Objects.nonNull(authUser) && !StringUtil.isNullOrEmpty(authUser.getAccount())) {
                base.setOpUser(authUser.getAccount());
            }
            // proxy
            base.setProxyEnabled(PropertiesUtil.getProperty("proxy.enabled"));
            base.setHttpProxyHost(PropertiesUtil.getProperty("http.proxyHost"));
            base.setHttpProxyPort(PropertiesUtil.getProperty("http.proxyPort"));
            base.setHttpsProxyHost(PropertiesUtil.getProperty("https.proxyHost"));
            base.setHttpsProxyPort(PropertiesUtil.getProperty("https.proxyPort"));
            base.setOptions(MapsKit.of("ROOT_URL", PropertiesUtil.getProperty("screen.airesource.apiurl")
                    , CommonPropertyKeyEnum.BIGSCREEN_INTERFACE.getCode()
                    , PropertiesUtil.getProperty(CommonPropertyKeyEnum.BIGSCREEN_INTERFACE.getCode(), StringUtil.EMPTY)));
            base.setTenantUserName(CrytoUtilSimple.decrypt(PropertiesUtil.getProperty(CommonPropertyKeyEnum.BIGSCREEN_ACCOUNT.getCode(), StringUtil.EMPTY)));
            base.setTenantUserPass(CrytoUtilSimple.decrypt(PropertiesUtil.getProperty(CommonPropertyKeyEnum.BIGSCREEN_PWD.getCode(), StringUtil.EMPTY)));
            if (base instanceof TrainingActiveJobsRequest) {
                TrainingActiveJobsResult result = (TrainingActiveJobsResult) MQHelper.rpc(base);
                log.info("OssBigScreenController.getActiveTrainingJobs-V1 资源池任务返回: {}", result == null ? "null" : JSON.toJSONString(result));
                return result;
            } else {
                TrainingActiveJobsV2Result result = (TrainingActiveJobsV2Result) MQHelper.rpc(base);
                log.info("OssBigScreenController.getActiveTrainingJobs-V2 资源池任务返回: {}", result == null ? "null" : JSON.toJSONString(result));
                return result;
            }
        } catch (Exception e) {
            log.error("查询当前排队任务数,调用第三方接口异常:", e);
            return null;
        }
    }

    private List<BigScreenTableResult> getBigScreenTableResultL(Date current) {
        List<PrivateResourceInfo> privateResourceInfoList = bizResUsageMonReportService.getPrivateResourceInfo(current);
        Map<String, List<PrivateResourceInfo>> privateResourceInfoMap = privateResourceInfoList.stream()
                .collect(Collectors.groupingBy(
                        PrivateResourceInfo::getOrgName));
        List<BigScreenTableResult> bigScreenTableResultList = new ArrayList<>();
        for (Map.Entry entry : privateResourceInfoMap.entrySet()) {
            String orgName = (String) entry.getKey();
            List<PrivateResourceInfo> privateResourceInfos = (List<PrivateResourceInfo>) entry.getValue();
            Map<Long, PrivateResourceInfo> clusterResourceMap = privateResourceInfos.stream()
                    .collect(Collectors.toMap(
                            PrivateResourceInfo::getClusterId,
                            Function.identity(), (k1, k2) -> k1));
            List<Long> clusterIdList = new ArrayList<>();
            for (PrivateResourceInfo privateResourceInfo : privateResourceInfos){
                Long clusterId = privateResourceInfo.getClusterId();
                clusterIdList.add(clusterId);
            }
//          训练池的总的节点数量
            int totalAvailableCount = 0;
            if (CollectionUtil.isNotEmpty(clusterIdList)) {
                List<ResMaPoolVO> resMaPoolVOList = maRemoteService.getResMaPoolByClusterIdList(clusterIdList);
                for (ResMaPoolVO resMaPoolVO : resMaPoolVOList){
                    Long clusterId = resMaPoolVO.getId();
                    clusterIdList.remove(clusterId);
                    Integer availableCount = resMaPoolVO.getAvailableCount();
                    if (ObjectUtil.isNotEmpty(availableCount)) {
                        totalAvailableCount += availableCount;
                    }
                }
            }
            //推理池的总的节点数量
            int totalQuantity = 0;
            if (CollectionUtil.isNotEmpty(clusterIdList)) {
                for (Long clusterId : clusterIdList) {
                    PrivateResourceInfo privateResourceInfo = clusterResourceMap.get(clusterId);
                    Integer quantity = privateResourceInfo.getQuantity();
                    if (ObjectUtil.isNotEmpty(quantity)) {
                        totalQuantity += quantity;
                    }
                }

            }
            totalAvailableCount = (totalAvailableCount + totalQuantity) * 8;
            BigScreenTableResult bigScreenTableResult = new BigScreenTableResult(orgName,String.valueOf(totalAvailableCount));
            bigScreenTableResultList.add(bigScreenTableResult);
        }
        //计算共享资源池使用量，加入bigScreenTableResultList
        String currentDay = DateFormatUtils.format(current, "yyyyMMddHHmmss");
        List<ResUsageTopResult> publicResUsageList = bizResUsageMonReportService.getPublicResUsage(currentDay);
        if (CollectionUtil.isNotEmpty(publicResUsageList)) {
            publicResUsageList.forEach(publicResuSage -> {
                if (publicResuSage.getCards() > 0 ) {
                    BigScreenTableResult bigScreenTableResult = new BigScreenTableResult(publicResuSage.getOrgName(),String.valueOf(publicResuSage.getCards()));
                    bigScreenTableResultList.add(bigScreenTableResult);
                }
            });

        }
        //聚合，排序
        List<BigScreenTableResult> bigScreenTableResults = getBigScreenTableResults(bigScreenTableResultList);
        return bigScreenTableResults;
    }

    private List<BigScreenTableResult> getBigScreenTableResults(List<BigScreenTableResult> bigScreenTableResultList) {
        Map<String, List<BigScreenTableResult>> collect = bigScreenTableResultList.stream()
                .collect(Collectors.groupingBy(
                        BigScreenTableResult::getName));
        List<BigScreenTableResult> resultList = new ArrayList<>();
        for (Map.Entry entry : collect.entrySet()) {
            String orgName = (String) entry.getKey();
            List<BigScreenTableResult> bigScreenTableResults = (List<BigScreenTableResult>) entry.getValue();
            BigDecimal total = BigDecimal.ZERO;
            for (BigScreenTableResult bigScreenTableResult : bigScreenTableResults) {
                String value = bigScreenTableResult.getValue();
                total = total.add(BigDecimal.valueOf(Double.parseDouble(value))).setScale(2,RoundingMode.HALF_UP);
            }
            BigScreenTableResult bigScreenTableResult = new BigScreenTableResult(orgName,total.toString());
            resultList.add(bigScreenTableResult);
        }
        List<BigScreenTableResult>  bigScreenTableResults = resultList.stream()
                .filter(bigScreenTableResult -> !"0.00".equals(
                        bigScreenTableResult.getValue()))
                .sorted(Comparator.comparing(BigScreenTableResult::getValue,
                                Comparator.comparing(
                                        Double::parseDouble))
                        .reversed())
                .collect(Collectors.toList());
        return bigScreenTableResults;
    }

}
