package cn.com.cloudstar.rightcloud.oss.module.contract.controller;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.operationlog.annotation.OperationLog;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.annotation.RejectCall;
import cn.com.cloudstar.rightcloud.oss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.oss.common.util.DataProcessingUtil;
import cn.com.cloudstar.rightcloud.oss.module.contract.service.ContractNotificationService;
import cn.com.cloudstar.rightcloud.oss.module.coupon.bean.CashCouponSendRequest;

/**
 * 合同通知管理
 *
 * <AUTHOR>
 * @date  2022-05-06 13:08
 */
@RestController
@RequestMapping("/contractNotification")
@Slf4j
public class ContractNotificationController {

    @Autowired
    private ContractNotificationService contractNotificationService;

    /**
     * 【Since v2.5.0】[INNER API]发送通知
     *
     * @param cashCouponSendRequest 现金优惠券发送请求
     * @return {@link Boolean}
     */
    @PostMapping("/send_notification")
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "发送提示信息",bizId = "#cashCouponSendRequest.accountId", resource = OperationResourceEnum.SEND_PROMPT_MESSAGE, tagNameUs ="'Send a prompt message'")
    @RejectCall
    public Boolean sendNotification(@RequestBody CashCouponSendRequest cashCouponSendRequest) {

        try {
            Boolean notification = contractNotificationService.sendCashNotification(cashCouponSendRequest);
            //日志记录处理铭感信息
            cashCouponSendRequest.setEmail(DataProcessingUtil.processing(cashCouponSendRequest.getEmail(),DataProcessingUtil.EMAIL));
            cashCouponSendRequest.setMobile(DataProcessingUtil.processing(cashCouponSendRequest.getMobile(),DataProcessingUtil.PHONE));
            return notification;
        } catch (Exception e) {
            //日志记录处理铭感信息
            cashCouponSendRequest.setEmail(DataProcessingUtil.processing(cashCouponSendRequest.getEmail(),DataProcessingUtil.EMAIL));
            cashCouponSendRequest.setMobile(DataProcessingUtil.processing(cashCouponSendRequest.getMobile(),DataProcessingUtil.PHONE));
            if (e instanceof BizException) {
                e.printStackTrace();
                throw new BizException(e.getMessage());
            } else {
                e.printStackTrace();
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_609387497));
            }
        }
    }
}
