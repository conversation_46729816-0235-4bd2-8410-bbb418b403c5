package cn.com.cloudstar.rightcloud.oss.module.feign.bean.response;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * <p>
 * 云主机表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-18
 */
@Data
public class ResVmResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 组织ID
     */
    private Long orgId;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 云环境ID
     */
    private Long cloudEnvId;

    /**
     * 宿主机ID
     */
    private Long hostId;

    /**
     * 可用区ID
     */
    private Long zoneId;

    /**
     * 可用区名称
     */
    @TableField(exist = false)
    private String zoneName;
    /**
     * 主机模板ID
     */
    private Long serverTemplateId;

    /**
     * 凭证ID
     */
    private Long credentialId;

    /**
     * 关联的集群ID
     */
    private Long virtualClusterId;

    /**
     * 镜像ID
     */
    private Long imageId;

    /**
     * 镜像名称
     */
    private String imageName;
    /**
     * 云主机规格ID
     */
    private Long vmTypeId;

    /**
     * 云主机规格UUID
     */
    private String vmTypeUuid;

    /**
     * 区域ID
     */
    private Long regionId;

    /**
     * 实例UUID
     */
    private String uuid;

    /**
     * 资源集UUID
     */
    private String projectUuid;

    /**
     * 实例名称
     */
    private String name;

    /**
     * 账号
     */
    private String managementUser;

    /**
     * 密码
     */
    private String managementPassword;

    /**
     * 登录类型 自定义密码登录：ByCustomPassward、镜像密码登录：ByImagePassward、密钥登录：ByKeypair
     */
    private String loginType;

    /**
     * 镜像类型 image 镜像，snapshot 快照
     */
    private String imageType;

    /**
     * cpu
     */
    private Integer cpu;

    /**
     * 内存
     */
    private Integer memory;

    /**
     * 描述
     */
    private String description;

    /**
     * 所有者account
     */
    private String ownerAccount;

    /**
     * 实例网络类型
     */
    private String instanceNetworkType;

    /**
     * 网络最大出口带宽
     */
    private Integer internetMaxBandwidthOut;

    /**
     * 网络最大入口带宽
     */
    private Integer internetMaxBandwidthIn;

    /**
     * 内网IP
     */
    private String innerIp;

    /**
     * 公共ip
     */
    private String publicIp;

    /**
     * ssh端口
     */
    private Integer sshPort;

    /**
     * 接入状态
     */
    private String manageStatus;

    /**
     * 状态信息
     */
    private String statusInfo;

    /**
     * 状态
     */
    private String status;

    /**
     * 创建原始参数
     */
    private String originParam;

    /**
     * 主机&实例类型
     */
    private String serverType;

    /**
     * 锁定用户
     */
    private Long lockUser;

    /**
     * 锁定状态
     */
    private String lockStatus;

    /**
     * 资源创建时间
     */
    private Date startTime;

    /**
     * 资源到期时间
     */
    private Date endTime;

    /**
     * 操作系统类型
     */
    private String osType;

    /**
     * 镜像类型
     */
    private String osPlatform;

    /**
     * 系统版本
     */
    private String osVersion;

    /**
     * vnc密码
     */
    private String vncPassword;

    /**
     * 克隆源ID
     */
    private String cloneSourceId;

    /**
     * 停止模式
     */
    private String stoppedMode;

    /**
     * 是否回收
     */
    private Boolean isRecycle;

    /**
     * 回收时间
     */
    private Date recycleDate;

    /**
     * urn
     */
    private String urn;

    /**
     * urn
     */
    private String uri;

    /**
     * 云监控插件安装状态;主要用于阿里云等公有云主机
     */
    private String monitorPluginStatus;

    /**
     * 是否安装了vmTools 0: 未安装, 1：已安装（VMware特有）
     */
    private Boolean hasVmTools;

    /**
     * 接管成功IP
     */
    private String managedIp;

    /**
     * 主机名
     */
    private String hostname;

    /**
     * CloudOS额外类型 BareMetal:裸金属 Trove:数据服务组件
     */
    private String extraType;

    /**
     * 安装snmp
     */
    private String installSnmp;

    /**
     * vnc信息
     */
    private String vncInfo;

    /**
     * 原状态
     */
    private String originalStatus;

    /**
     * 回收者
     */
    private String recycleUser;

    /**
     * 最后计费时间
     */
    private Date lastBillingTime;

    /**
     * 客户自定义远程信息
     */
    private String remoteInfo;

    /**
     * 计费开始时间
     */
    private Date chargeStartDate;

    /**
     * 资源交付时间
     */
    private Date resourceDeliverDate;

    /**
     * 计费模式(CHARGE_TYPE)：包年包月prePaid，按需postPaid，按次oncePaid，不付费noPaid
     * {@link com.cloudstar.rightcloud.sdk.common.em.ChargeTypeEnum#getCode()}
     */
    private String chargeType;

    /**
     * 计费周期(CHARGE_CYCLE)：按年 year，按月 month，按日 day，按小时 hour，按次 once
     * {@link com.cloudstar.rightcloud.sdk.common.em.ChargeCycleEnum#getCode()}
     */
    private String priceUnit;

    /**
     * 免费天数
     */
    private Integer freePeriodDay;


    /**
     * 数据来源（0:平台,1:其他资源录入）
     */
    private String dataSource;

    /**
     * 标签
     */
    private String tag;

    /**
     * 高级属性
     */
    private String extra;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedDt;

    /**
     * 版本
     */
    private Long version;

    /**
     * 回收站进入时间
     */
    private Date recycleTime;
    /**
     * 服务器组id
     */
    private Long vmGroupId;
    /**
     * 属主
     */
    private String owner;

}
