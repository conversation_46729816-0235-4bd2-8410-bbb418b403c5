<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.cloudstar.rightcloud.oss.module.msg.dao.SysMMsgUserConfigMapper">

    <sql id="Base_Column_List">A.id, A.msg_type, A.platform_category, A.msg_type_name, A.msg_name, A.msg_name_us, A.msg_ids, A.msg_description, A.msg_description_us, A.sort_no, A.sms_support, A.sms_status, A.sms_is_editable, A.msg_support, A.msg_status, A.msg_is_editable, A.email_support, A.email_status, A.email_is_editable, A.owner_id, A.org_sid, A.created_org_sid, A.version, A.created_by, <PERSON>.created_dt, A.updated_by, A.updated_dt</sql>
    <sql id="Example_Update_Clause">
        <set>
            <if test="msgType != null and msgType != ''">
                msg_type = #{msgType},
            </if>
            <if test="platformCategory != null and platformCategory != ''">
                platform_category = #{platformCategory},
            </if>
            <if test="msgTypeName != null and msgTypeName != ''">
                msg_type_name = #{msgTypeName},
            </if>
            <if test="msgName != null and msgName != ''">
                msg_name = #{msgName},
            </if>
            <if test="msgIds != null and msgIds != ''">
                msg_ids = #{msgIds},
            </if>
            <if test="msgDescription != null and msgDescription != ''">
                msg_description = #{msgDescription},
            </if>
            <if test="sortNo != null">
                sort_no = #{sortNo},
            </if>
            <if test="smsSupport != null">
                sms_support = #{smsSupport},
            </if>
            <if test="smsStatus != null">
                sms_status = #{smsStatus},
            </if>
            <if test="smsIsEditable != null">
                sms_is_editable = #{smsIsEditable},
            </if>
            <if test="msgSupport != null">
                msg_support = #{msgSupport},
            </if>
            <if test="msgStatus != null">
                msg_status = #{msgStatus},
            </if>
            <if test="msgIsEditable != null">
                msg_is_editable = #{msgIsEditable},
            </if>
            <if test="emailSupport != null">
                email_support = #{emailSupport},
            </if>
            <if test="emailStatus != null">
                email_status = #{emailStatus},
            </if>
            <if test="emailIsEditable != null">
                email_is_editable = #{emailIsEditable},
            </if>
            <if test="ownerId != null and ownerId != ''">
                owner_id = #{ownerId},
            </if>
            <if test="orgSid != null">
                org_sid = #{orgSid},
            </if>
            <if test="createdOrgSid != null">
                created_org_sid = #{createdOrgSid},
            </if>
            <if test="version != null">
                version = #{version},
            </if>
            <if test="createdBy != null and createdBy != ''">
                created_by = #{createdBy},
            </if>
            <if test="createdDt != null">
                created_dt = #{createdDt},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                updated_by = #{updatedBy},
            </if>
            <if test="updatedDt != null">
                updated_dt = #{updatedDt},
            </if>
        </set>
    </sql>
    <sql id="Example_Where_Clause">
        <trim prefixOverrides="and|or">
            <if test="id != null">
                and A.id = #{id}
            </if>
            <if test="ids != null and ids.size()>0">
                AND A.id IN
                <foreach collection="ids" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="msgType != null and msgType != ''">
                and A.msg_type = #{msgType}
            </if>
            <if test="platformCategory != null and platformCategory != ''">
                and A.platform_category = #{platformCategory}
            </if>
            <if test="msgTypeName != null and msgTypeName != ''">
                and A.msg_type_name = #{msgTypeName}
            </if>
            <if test="msgName != null and msgName != ''">
                and A.msg_name = #{msgName}
            </if>
            <if test="msgIds != null and msgIds != ''">
                and A.msg_ids = #{msgIds}
            </if>
            <if test="msgDescription != null and msgDescription != ''">
                and A.msg_description = #{msgDescription}
            </if>
            <if test="sortNo != null">
                and A.sort_no = #{sortNo}
            </if>
            <if test="smsSupport != null">
                and A.sms_support = #{smsSupport}
            </if>
            <if test="smsStatus != null">
                and A.sms_status = #{smsStatus}
            </if>
            <if test="smsIsEditable != null">
                and A.sms_is_editable = #{smsIsEditable}
            </if>
            <if test="msgSupport != null">
                and A.msg_support = #{msgSupport}
            </if>
            <if test="msgStatus != null">
                and A.msg_status = #{msgStatus}
            </if>
            <if test="msgIsEditable != null">
                and A.msg_is_editable = #{msgIsEditable}
            </if>
            <if test="emailSupport != null">
                and A.email_support = #{emailSupport}
            </if>
            <if test="emailStatus != null">
                and A.email_status = #{emailStatus}
            </if>
            <if test="emailIsEditable != null">
                and A.email_is_editable = #{emailIsEditable}
            </if>
            <if test="ownerId != null and ownerId != ''">
                and A.owner_id = #{ownerId}
            </if>
            <if test="orgSid != null">
                and A.org_sid = #{orgSid}
            </if>
            <if test="createdOrgSid != null">
                and A.created_org_sid = #{createdOrgSid}
            </if>
            <if test="version != null">
                and A.version = #{version}
            </if>
            <if test="createdBy != null and createdBy != ''">
                and A.created_by = #{createdBy}
            </if>
            <if test="createdDt != null">
                and A.created_dt = #{createdDt}
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                and A.updated_by = #{updatedBy}
            </if>
            <if test="updatedDt != null">
                and A.updated_dt = #{updatedDt}
            </if>
            <if test="msgIdLike != null">
                and A.msg_ids like CONCAT('%',#{msgIdLike},'%')
            </if>

        </trim>
    </sql>
    <!--查询单个-->
    <select id="queryById" resultType="cn.com.cloudstar.rightcloud.oss.module.msg.bean.SysMMsgUserConfig">
        select
        <include refid="Base_Column_List"/>
        from sys_m_msg_user_config A
        where A.id = #{id}
    </select>
    <!--查询单个-->
    <select id="queryByParam" resultType="cn.com.cloudstar.rightcloud.oss.module.msg.bean.SysMMsgUserConfig">
        select
        <include refid="Base_Column_List"/>
        from sys_m_msg_user_config A
        where  <include refid="Example_Where_Clause"/>
    </select>

    <!--查询指定行数据-->
    <select id="queryAll"
            resultType="cn.com.cloudstar.rightcloud.oss.module.msg.bean.SysMMsgUserConfig" >
        select
        <include refid="Base_Column_List"/>
        from sys_m_msg_user_config A
        <where>
            <include refid="Example_Where_Clause"/>
        </where>
    </select>
    <!--查询指定行数据-->
    <select id="getMsgUserContact"
            resultType="cn.com.cloudstar.rightcloud.oss.module.msg.bean.vo.SysMMsgUserConfigVo" >
        select
        <include refid="Base_Column_List"/>,
        ( select GROUP_CONCAT( name) from sys_m_msg_receive_contact mrc where mrc.config_id = A.id group by config_id) contactNames
        from sys_m_msg_user_config A
        <where>
            <include refid="Example_Where_Clause"/>
        </where>
    </select>
    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into sys_m_msg_user_config(msg_type, platform_category, msg_type_name, msg_name, msg_ids, msg_description, sort_no, sms_support, sms_status, sms_is_editable, msg_support, msg_status, msg_is_editable, email_support, email_status, email_is_editable, owner_id, org_sid, created_org_sid, version, created_by, created_dt, updated_by, updated_dt)
        values (#{msgType}, #{platformCategory}, #{msgTypeName}, #{msgName}, #{msgIds}, #{msgDescription}, #{sortNo}, #{smsSupport}, #{smsStatus}, #{smsIsEditable}, #{msgSupport}, #{msgStatus}, #{msgIsEditable}, #{emailSupport}, #{emailStatus}, #{emailIsEditable}, #{ownerId}, #{orgSid}, #{createdOrgSid}, #{version}, #{createdBy}, #{createdDt}, #{updatedBy}, #{updatedDt})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into sys_m_msg_user_config(msg_type, platform_category, msg_type_name, msg_name, msg_ids, msg_description, sort_no, sms_support, sms_status, sms_is_editable, msg_support, msg_status, msg_is_editable, email_support, email_status, email_is_editable, owner_id, org_sid, created_org_sid, version, created_by, created_dt, updated_by, updated_dt)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.msgType}, #{entity.platformCategory}, #{entity.msgTypeName}, #{entity.msgName}, #{entity.msgIds}, #{entity.msgDescription}, #{entity.sortNo}, #{entity.smsSupport}, #{entity.smsStatus}, #{entity.smsIsEditable}, #{entity.msgSupport}, #{entity.msgStatus}, #{entity.msgIsEditable}, #{entity.emailSupport}, #{entity.emailStatus}, #{entity.emailIsEditable}, #{entity.ownerId}, #{entity.orgSid}, #{entity.createdOrgSid}, #{entity.version}, #{entity.createdBy}, #{entity.createdDt}, #{entity.updatedBy}, #{entity.updatedDt})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into sys_m_msg_user_config(msg_type, platform_category, msg_type_name, msg_name, msg_ids, msg_description, sort_no, sms_support, sms_status, sms_is_editable, msg_support, msg_status, msg_is_editable, email_support, email_status, email_is_editable, owner_id, org_sid, created_org_sid, version, created_by, created_dt, updated_by, updated_dt)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.msgType}, #{entity.platformCategory}, #{entity.msgTypeName}, #{entity.msgName}, #{entity.msgIds}, #{entity.msgDescription}, #{entity.sortNo}, #{entity.smsSupport}, #{entity.smsStatus}, #{entity.smsIsEditable}, #{entity.msgSupport}, #{entity.msgStatus}, #{entity.msgIsEditable}, #{entity.emailSupport}, #{entity.emailStatus}, #{entity.emailIsEditable}, #{entity.ownerId}, #{entity.orgSid}, #{entity.createdOrgSid}, #{entity.version}, #{entity.createdBy}, #{entity.createdDt}, #{entity.updatedBy}, #{entity.updatedDt})
        </foreach>
        on duplicate key update
        msg_type = values(msg_type),
        platform_category = values(platform_category),
        msg_type_name = values(msg_type_name),
        msg_name = values(msg_name),
        msg_ids = values(msg_ids),
        msg_description = values(msg_description),
        sort_no = values(sort_no),
        sms_support = values(sms_support),
        sms_status = values(sms_status),
        sms_is_editable = values(sms_is_editable),
        msg_support = values(msg_support),
        msg_status = values(msg_status),
        msg_is_editable = values(msg_is_editable),
        email_support = values(email_support),
        email_status = values(email_status),
        email_is_editable = values(email_is_editable),
        owner_id = values(owner_id),
        org_sid = values(org_sid),
        created_org_sid = values(created_org_sid),
        version = values(version),
        created_by = values(created_by),
        created_dt = values(created_dt),
        updated_by = values(updated_by),
        updated_dt = values(updated_dt)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update sys_m_msg_user_config
        <include refid="Example_Update_Clause"/>
        where id = #{id}
    </update>
    <update id="updateByParam">
        update sys_m_msg_user_config
        <include refid="Example_Update_Clause"/>
        where
        <trim prefixOverrides="and|or">
            <if test="ids != null and ids.size()>0">
                AND id IN
                <foreach collection="ids" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </trim>
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from sys_m_msg_user_config where id = #{id}
    </delete>
    <!--通过主键删除-->
    <delete id="deleteByParam">
        delete from sys_m_msg_user_config where
        <trim prefixOverrides="and|or">
            <if test="orgSids != null and orgSids.size()>0">
                AND org_sid IN
                <foreach collection="orgSids" item="sid" open="(" close=")" separator=",">
                    #{sid}
                </foreach>
            </if>
        </trim>

    </delete>

    <insert id="insertByDefault">
        INSERT INTO sys_m_msg_user_config (
            msg_type,
            platform_category,
            msg_type_name,
            msg_name,
            msg_name_us,
            msg_ids,
            msg_description,
            msg_description_us,
            sort_no,
            sms_support,
            sms_status,
            sms_is_editable,
            msg_support,
            msg_status,
            msg_is_editable,
            email_support,
            email_status,
            email_is_editable,
            owner_id,
            org_sid,
            created_org_sid,
            version,
            created_by,
            created_dt,
            updated_by,
            updated_dt
        ) SELECT
              msg_type,
              platform_category,
              msg_type_name,
              msg_name,
              msg_name_us,
              msg_ids,
              msg_description,
              msg_description_us,
              sort_no,
              1 sms_support,
              0 sms_status,
              1 sms_is_editable,
              msg_support,
              msg_status,
              msg_is_editable,
              1 email_support,
              0 email_status,
              1 email_is_editable,
              NULL owner_id,
              #{orgSid} org_sid,
              NULL created_org_sid,
              1 version,
              'admin' created_by,
              now() created_dt,
              NULL updated_by,
              NULL updated_dt
        FROM sys_m_msg_user_config_default
        where if(#{orgSid} = -1, platform_category = 'platform', platform_category = 'console')
          and display = 1
    </insert>
    <delete id="deleteByMsgIds">
        delete from sys_m_msg_user_config where msg_ids = #{msgIds}
    </delete>

    <select id="queryMsgConfigByOrgSid"
            resultType="cn.com.cloudstar.rightcloud.oss.module.msg.bean.SysMMsgUserConfig" >
        select
        <include refid="Base_Column_List"/>
        from sys_m_msg_user_config A
        <where>
            <if test="orgSid != null">
                and A.org_sid = #{orgSid}
            </if>
            <if test="msgIdLike != null">
                and A.msg_ids like CONCAT('%',#{msgIdLike},'%')
            </if>
        </where>
    </select>

</mapper>

