package cn.com.cloudstar.rightcloud.oss.module.operate.service.impl;

import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.UuidUtil;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.core.pojo.dto.bss.BizDiscountPolicy;
import cn.com.cloudstar.rightcloud.core.pojo.dto.charge.BizBillingAccount;
import cn.com.cloudstar.rightcloud.core.pojo.dto.operate.*;
import cn.com.cloudstar.rightcloud.core.pojo.dto.sfs.ServiceCategory;
import cn.com.cloudstar.rightcloud.core.pojo.dto.sfs.SfProductTemplate;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.SysConfig;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.oss.common.constants.status.ServiceCategoryStatus;
import cn.com.cloudstar.rightcloud.oss.common.enums.PolicyStatus;
import cn.com.cloudstar.rightcloud.oss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.oss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.oss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.WebUserUtil;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.user.SysBssEntityMapper;
import cn.com.cloudstar.rightcloud.oss.module.operate.bean.request.CreateEntityRequest;
import cn.com.cloudstar.rightcloud.oss.module.operate.bean.request.DescribeEntityRequest;
import cn.com.cloudstar.rightcloud.oss.module.operate.bean.request.RelatedProductsRequest;
import cn.com.cloudstar.rightcloud.oss.module.operate.bean.respone.DescribeEntityResponse;
import cn.com.cloudstar.rightcloud.oss.module.operate.bean.respone.DescribePayChannelResponse;
import cn.com.cloudstar.rightcloud.oss.module.operate.dao.EntityMapper;
import cn.com.cloudstar.rightcloud.oss.module.operate.dao.PayChannelMapper;
import cn.com.cloudstar.rightcloud.oss.module.operate.service.IEntityService;
import cn.com.cloudstar.rightcloud.oss.module.order.dao.BizDiscountPolicyMapper;
import cn.com.cloudstar.rightcloud.oss.module.order.dao.ServiceOrderDetailMapper;
import cn.com.cloudstar.rightcloud.oss.module.order.dao.ServiceOrderMapper;
import cn.com.cloudstar.rightcloud.oss.module.pricing.bean.priceconfig.model.BizBillingRegionCharge;
import cn.com.cloudstar.rightcloud.oss.module.pricing.dao.priceconfig.*;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.dao.ServiceCategoryMapper;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.dao.SfProductTemplateMapper;
import cn.com.cloudstar.rightcloud.oss.module.system.dao.config.SysConfigMapper;
import cn.com.cloudstar.rightcloud.oss.module.system.service.config.impl.SystemConfigServiceImpl;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.hpc.HPCRemoteService;
import cn.hutool.core.collection.CollectionUtil;
import io.seata.common.util.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 运营实体实现类
 *
 * @Auther: 张淇囿
 * @Date: 2022/05/18
 */
@Service
@Slf4j
public class IEntityServiceImpl implements IEntityService {

    private static final Logger logger = LoggerFactory.getLogger(IEntityServiceImpl.class);



    private static final String WECHAT_PAY = "wechatpay";

    private static final String ALIPAY = "alipay";

    private static final String UNIONPAY = "unionpay";

    private static final String ZERO = "0";

    private static final String ONE = "1";

    private static final String PRODUCT_QUOTA_CONFIG = "product_quota_config_";

    private static final String MINIMUM_PURCHASE_AMOUNT = "minimum.purchase.amount.";

    private static final String MINIMUM_FROZEN_AMOUNT = "minimum.frozen.amount.";

    private static final String MINIMUM_PURCHASE_AMOUNT_OPEN = "minimum.purchase.amount.open.";

    private static final String MINIMUM_FROZEN_AMOUNT_OPEN = "minimum.frozen.amount.open.";

    private static final String OPEN_STATUS = "开启状态";

    private static final String FALSE = "false";

    private static final String TRUE = "true";

    private static final String BOOLEAN = "boolean";

    private static final String MINIMUM_FROZEN_AMOUNT_CH = "最小冻结金额";

    private static final String MINIMUM_PURCHASE_AMOUNT_CH = "最小购买金额";

    private static final String INT = "int";

    private static final String YUAN = "元";

    /**
     * 内置组件
     */
    private static final String BUILT_IN_COMPONENTS = "内置组件";

    /**
     * 自定义服务
     */
    private static final String CUSTOM_SERVICE = "自定义服务";

    @Autowired
    private EntityMapper entityMapper;

    @Autowired
    private BizBillingAccountMapper bizBillingAccountMapper;

    @Autowired
    private ServiceCategoryMapper serviceCategoryMapper;

    @Autowired
    private BizAccountDealMapper bizAccountDealMapper;

    @Autowired
    private ServiceOrderMapper serviceOrderMapper;

    @Autowired
    private ServiceOrderDetailMapper serviceOrderDetailMapper;

    @Autowired
    private PayChannelMapper payChannelMapper;

    @Autowired
    private SystemConfigServiceImpl systemConfigService;

    @DubboReference
    private HPCRemoteService hpcRemoteService;

    @Autowired
    private SysConfigMapper sysConfigMapper;

    @Autowired
    private SysBssEntityMapper sysBssEntityMapper;

    @Autowired
    private BizBillingRegionResourceMapper bizBillingRegionResourceMapper;

    @Autowired
    private BizBagUserMapper bizBagUserMapper;

    @Autowired
    private BizBillingStrategyMapper bizBillingStrategyMapper;

    @Autowired
    private SfProductTemplateMapper sfProductTemplateMapper;

    @Autowired
    private BizDiscountPolicyMapper bizDiscountPolicyMapper;

    @Autowired
    private BizBillingStrategyServingMapper bizBillingStrategyServingMapper;
    @Autowired
    private BizBillingSpecGroupMapper bizBillingSpecGroupMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createEntity(CreateEntityRequest request) {
        log.info("字符集为：{}", WebUtil.getHeaderAcceptLanguage());
        Entity entity = Objects
                .requireNonNull(BeanConvertUtil.convert(request, Entity.class),
                                WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        // 业务约束
        // 1.实体名称不允许重复。
        int sameName = entityMapper.countEntity(new Criteria("name", entity.getName()));
        if (sameName > 0) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERROR_ENTITY_00019));
        }
        // 2.目前最多只支持三个运营实体。
        int countEntity = entityMapper.countEntity(new Criteria());
        if (countEntity > 2) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERROR_ENTITY_00001));
        }
        String uuid = UuidUtil.getUuid();
        entity.setEntityTag(uuid);
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (ObjectUtils.isEmpty(authUser)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_531007114));
        }
        entity.setOwnerId(authUser.getUserSid().toString());
        entity.setOrgSid(authUser.getOrgSid());
        entity.setCreatedOrgSid(authUser.getOrgSid());
        WebUserUtil.prepareInsertParams(entity);
        boolean result = entityMapper.insertEntity(entity);
        // 3. 运营实体创建成功，给平台所有用户增加对应账户
        if (result) {
            List<BizBillingAccount> bizBillingAccounts = bizBillingAccountMapper.selectByParams(
                    new Criteria("entityId", ONE));
            if (CollectionUtils.isNotEmpty(bizBillingAccounts)) {
                bizBillingAccounts.forEach(t -> {
                    //TODO 临时方案，防止依赖循环
                    t.setId(null);
                    t.setEntityId(entity.getId());
                    t.setEntityName(entity.getName());
                    t.setId(null);
                    t.setBalance(new BigDecimal("0"));
                    t.setDiscount(new BigDecimal("1.000"));
                    t.setCreatedBy("admin");
                    t.setCreatedDt(new Date());
                    t.setUpdatedBy("admin");
                    t.setUpdatedDt(new Date());
                    t.setVersion(1L);
                    t.setCreditLine(new BigDecimal("0"));
                    t.setBalanceCash(new BigDecimal("0"));
                    t.setStatus("normal");
                    t.setBalanceCash(BigDecimal.ZERO);
                    t.setCreditLine(BigDecimal.ZERO);
                    bizBillingAccountMapper.insertSelective(t);

                });
                bizBillingAccountMapper.insertMultiByEntity(bizBillingAccounts);
            }


        }
        Long entityId = entity.getId();
        // 4. 创建默认支付方式
        payChannelMapper.insertDefaultByEntity(entity.getId());
        // 5. 创建默认平台限额配置
        SysConfig sysConfig = new SysConfig();
        sysConfig.setConfigType(PRODUCT_QUOTA_CONFIG + entityId);
        sysConfig.setConfigName(MINIMUM_PURCHASE_AMOUNT_CH);
        sysConfig.setConfigKey(MINIMUM_PURCHASE_AMOUNT + entityId);
        sysConfig.setConfigValue(ZERO);
        sysConfig.setDataType(INT);
        sysConfig.setUnit(YUAN);
        sysConfig.setSortRank(1);
        sysConfig.setCreatedBy(authUser.getAccount());
        sysConfig.setUpdatedBy(authUser.getAccount());
        sysConfig.setCreatedDt(new Date());
        sysConfig.setUpdatedDt(new Date());
        sysConfig.setVersion(1L);
        sysConfig.setDisplay(true);
        // 最小购买金额
        sysConfigMapper.insert(sysConfig);
        // 最小冻结金额
        sysConfig.setConfigName(MINIMUM_FROZEN_AMOUNT_CH);
        sysConfig.setConfigKey(MINIMUM_FROZEN_AMOUNT + entityId);
        sysConfig.setSortRank(3);
        sysConfigMapper.insert(sysConfig);
        // 最小购买金额，开关
        sysConfig.setConfigName(OPEN_STATUS);
        sysConfig.setConfigKey(MINIMUM_PURCHASE_AMOUNT_OPEN + entityId);
        sysConfig.setConfigValue(FALSE);
        sysConfig.setDataType(BOOLEAN);
        sysConfig.setUnit(null);
        sysConfig.setSortRank(2);
        sysConfigMapper.insert(sysConfig);
        // 最小冻结金额，开关
        sysConfig.setConfigName(OPEN_STATUS);
        sysConfig.setConfigKey(MINIMUM_FROZEN_AMOUNT_OPEN + entityId);
        sysConfig.setConfigValue(TRUE);
        sysConfig.setDataType(BOOLEAN);
        sysConfig.setSortRank(4);
        sysConfigMapper.insert(sysConfig);
        // 增加默认折扣策略
        BizDiscountPolicy policy = new BizDiscountPolicy();
        policy.setPolicySid(System.currentTimeMillis());
        policy.setCategory(PolicyStatus.PLATFORM);
        policy.setPolicyType(PolicyStatus.SHARE);
        policy.setPolicyLevel("customer,platform");
        policy.setEntityId(entityId);
        policy.setStatus(1);
        policy.setVersion(1L);
        WebUserUtil.prepareInsertParams(policy);
        bizDiscountPolicyMapper.insert(policy);


        return result;
    }

    @Override
    public List<DescribeEntityResponse> listEntity(DescribeEntityRequest request) {
        Criteria criteria = new Criteria();
        criteria.setConditionObject(request);
        WebUserUtil.preparePageParams(request, criteria, "created_dt desc");
        List<Entity> entities = entityMapper.selectEntityByParams(criteria);
        return BeanConvertUtil.convert(entities, DescribeEntityResponse.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteEntity(Long id) {
        if (Objects.isNull(entityMapper.selectByPrimaryKey(id))) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERROR_DELETE_FAILURE));
        }
        if (1 == id) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERROR_ENTITY_00004));
        }
        List<BizBillingAccount> bizBillingAccounts = bizBillingAccountMapper.selectByParams(
                new Criteria("entityId", id));
        List<Long> accountSid = bizBillingAccounts.stream().map(BizBillingAccount::getId).collect(Collectors.toList());
        // 查询biz_account_deal service_order
        if (CollectionUtils.isNotEmpty(accountSid)) {
            int bizAccountDealResult = bizAccountDealMapper.countByParams(new Criteria("accountSids", accountSid));
            int serviceOrderResult = serviceOrderMapper.countByParams(new Criteria("accountSids", accountSid));
            if (bizAccountDealResult + serviceOrderResult > 0) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERROR_ENTITY_00002));
            }
        }
        // 存在产品关联，不允许删除运用实体
        Criteria criteria = new Criteria();
        criteria.put("publish_status", "succeed");
        criteria.put("status", "using");
        criteria.put("publishDtIsNotNull", true);
        criteria.put("entityId", id);
        // 关联资源
        List<ServiceCategory> serviceCategoryList = serviceCategoryMapper.selectByParams(criteria);
        if (CollectionUtils.isNotEmpty(serviceCategoryList)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERROR_ENTITY_00010));
        }
        // 1.删除biz_billing_account
        bizBillingAccountMapper.deleteByParams(new Criteria("entityId", id));
        // 2.解绑sf_service_category
        serviceCategoryMapper.updateByEntityId(id);
        // 3.删除sf_service_category_hpc_cluster_pool
        serviceCategoryMapper.deleteHPCByEntityId(id);
        // 4.删除sys_pay_channel
        payChannelMapper.deleteByPrimaryKey(id);
        // 5.删除sys_m_config
        sysConfigMapper.deleteByParams(new Criteria("configType", PRODUCT_QUOTA_CONFIG + id));
        // 6.删除sys_bss_entity_user
        sysBssEntityMapper.deleteByPrimaryKey(id);
        // 7.删除sys_bss_entity
        entityMapper.deleteByPrimaryKey(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void relatedProducts(RelatedProductsRequest request) {
        Entity entity = entityMapper.selectByPrimaryKey(request.getEntityId());
        ServiceCategory serviceCategory = serviceCategoryMapper.selectByPrimaryKey(request.getServiceId());
        if (Objects.isNull(entity) || Objects.isNull(serviceCategory)) {
            throw new BizException(WebUtil.getMessage(MsgCd.WARNING_QUERY_FAILURE));
        }
        // HPC初始化关联到默认运营实体
        if (!ProductCodeEnum.HPC.getProductCode().equals(serviceCategory.getServiceType()) &&
                ONE.equals(request.getStatus()) && Objects.nonNull(serviceCategory.getEntityId())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERROR_ENTITY_00003));
        }
        // res_hpc_cluster_pool检查底层是否多种HPC共享资源池
        Boolean checkMultipleHpcPool = hpcRemoteService.checkMultipleHpcPool();

        // 判断产品是否定价，未定价就下架
        int count = bizBillingStrategyServingMapper.selectBizBillingStrategyServingByServiceId(request.getServiceId());
        if (count <= 0 && !ServiceCategoryStatus.NOUSING.equals(serviceCategory.getStatus())) {
            serviceCategory.setStatus(ServiceCategoryStatus.NOUSING);
            serviceCategoryMapper.updateByPrimaryKey(serviceCategory);
        }
        // HPC共享资源关联sf_service_category_hpc_cluster_pool表
        if (ProductCodeEnum.HPC.getProductCode().equals(serviceCategory.getServiceType()) && Objects.nonNull(
                request.getClusterId())) {
            // 同种version不同clusterId
            List<String> clusterLists = Arrays.asList(request.getClusterId().split(StringUtil.COMMA));
            String clusterId = clusterLists.get(0);
            SfServiceCategoryHpcClusterPool hpcClusterPool = serviceCategoryMapper.selectServiceCategoryHPCByClusterId(
                    clusterId);
            // 关联产品
            if (ONE.equals(request.getStatus())) {
                if (Objects.nonNull(hpcClusterPool)) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERROR_ENTITY_00003));
                } else {
                    // 插入service_category
                    Long serviceId = serviceCategory.getId();
                    String serviceType = serviceCategory.getServiceType();
                    // 1武汉版本 2西安版本 3武汉二期版本 4线下版本
                    Integer hpcVersion = request.getHpcVersion();
                    // 关联先后顺序
                    List<SfServiceCategoryHpcClusterPool> index = serviceCategoryMapper.selectServiceCategoryByHPC();
                    // 如果未空，则表示首次关联，关联历史数据
                    if (CollectionUtils.isEmpty(index)) {
                        if (checkMultipleHpcPool) {
                            if (2 < hpcVersion) {
                                throw new BizException(WebUtil.getMessage(MsgCd.ERROR_ENTITY_00015));
                            }
                            relatedHPC(serviceCategory, request, entity);
                        } else {
                            if (3 > hpcVersion) {
                                relatedHPC(serviceCategory, request, entity);
                            } else {
                                if (4 == hpcVersion) {
                                    serviceId = serviceCategory.getId();
                                }else if (3 == hpcVersion) {
                                    serviceId = relatedHPC_SAAS(serviceCategory, request, entity);
                                    serviceType = ProductCodeEnum.HPC_SAAS.getProductCode();
                                } else {
                                    throw new BizException(WebUtil.getMessage(MsgCd.ERROR_ENTITY_00011));
                                }
                            }
                        }
                        // 更新套餐包
                        bizBagUserMapper.updateClusterIdByEntity(clusterId);
                    } else {
                        if (4 == hpcVersion) {
                            serviceId = serviceCategory.getId();
                        }else if (3 == hpcVersion) {
                            serviceId = relatedHPC_SAAS(serviceCategory, request, entity);
                            serviceType = ProductCodeEnum.HPC_SAAS.getProductCode();
                        } else {
                            throw new BizException(WebUtil.getMessage(MsgCd.ERROR_ENTITY_00011));
                        }
                    }
                    // 插入sf_service_category_hpc_cluster_pool
                    for (String data : clusterLists) {
                        SfServiceCategoryHpcClusterPool serviceHpcClusterPool = new SfServiceCategoryHpcClusterPool(
                                serviceId, serviceType, data,
                                request.getProductName(),
                                entity.getId(),
                                entity.getName(),
                                hpcVersion);
                        serviceCategoryMapper.insertHPC(serviceHpcClusterPool);
                    }
                    // 关联HPC/HPC-SAAS模板
                    List<SfProductTemplate> sfProductTemplates = sfProductTemplateMapper.selectByParams(
                            new Criteria("templateType", serviceType));
                    for (SfProductTemplate date : sfProductTemplates) {
                        serviceCategoryMapper.updateTemplateByEntityId(date.getId(), entity.getId());
                    }
                }
                // 取消关联
            } else {
                if (Objects.nonNull(hpcClusterPool)) {
                    if (CollectionUtils.isNotEmpty(hpcRemoteService.selectByByPoolUuid(request.getClusterId()))) {
                        throw new BizException(WebUtil.getMessage(MsgCd.ERROR_ENTITY_00007));
                    }
                    // HPC有套餐包不允许取消关联
                    if (0 < serviceCategoryMapper.getBizBagUser(request.getClusterId())) {
                        throw new BizException(WebUtil.getMessage(MsgCd.ERROR_ENTITY_00009));
                    }
                    serviceCategoryMapper.deleteHPCByClusterId(request.getClusterId());
                    // 删除service_category
                    if (ProductCodeEnum.HPC.getProductName().equals(request.getProductName())) {
                        serviceCategory.setEntityId(null);
                        serviceCategory.setEntityName(null);
                        serviceCategory.setProductName(ProductCodeEnum.HPC.getProductName());
                        serviceCategoryMapper.updateByPrimaryKey(serviceCategory);
                        related(ProductCodeEnum.SFS.getProductCode(), null, null);
                    } else {
                        Criteria criteria = new Criteria();
                        criteria.put("entityId", entity.getId());
                        criteria.put("serviceType", ProductCodeEnum.HPC_SAAS.getProductCode());
                        criteria.put("productName", request.getProductName());
                        serviceCategoryMapper.deleteByParams(criteria);
                    }
                    // 取消关联HPC/HPC-SAAS模板
                    List<SfProductTemplate> sfProductTemplates = sfProductTemplateMapper.selectByParams(
                            new Criteria("templateType",
                                         ProductCodeEnum.HPC.getProductName().equals(request.getProductName())
                                                 ? ProductCodeEnum.HPC.getProductCode()
                                                 : ProductCodeEnum.HPC_SAAS.getProductCode()));
                    for (SfProductTemplate date : sfProductTemplates) {
                        serviceCategoryMapper.updateTemplateByEntityId(date.getId(), null);
                    }
                }
            }
            return;
        }
        // 关联产品
        List<SfProductTemplate> sfProductTemplates = sfProductTemplateMapper.selectByParams(
                new Criteria("templateType", serviceCategory.getServiceType()));
        // 产品对应的区域资源类型配置
        List<BizBillingRegionResource> bizBillingRegionResources = bizBillingRegionResourceMapper.selectByResourceType(
                serviceCategory.getServiceType());
        if (ONE.equals(request.getStatus())) {
            serviceCategory.setEntityId(entity.getId());
            serviceCategory.setEntityName(entity.getName());
            // 区域资源类型配置
            for (BizBillingRegionResource data : bizBillingRegionResources) {
                data.setEntityId(entity.getId());
                data.setEntityName(entity.getName());
            }
            // 关联模板
            for (SfProductTemplate date : sfProductTemplates) {
                serviceCategoryMapper.updateTemplateByEntityId(date.getId(), entity.getId());
            }
            // Modelarts共享资源池、Modelarts专属资源池(DRP)需在统一运营实体下
            if (ProductCodeEnum.MODEL_ARTS.getProductCode().equals(serviceCategory.getServiceType())
                    || ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode()
                                                              .equals(serviceCategory.getServiceType())) {
                Criteria criteria = new Criteria();
                criteria.put("publish_status", "succeed");
                criteria.put("status", "using");
                criteria.put("editable", ONE);
                criteria.put("publishDtIsNotNull", ONE);
                List<String> productCodeList = new ArrayList<>();
                productCodeList.add(ProductCodeEnum.MODEL_ARTS.getProductCode());
                productCodeList.add(ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode());
                criteria.put("serviceTypeList", productCodeList);
                List<ServiceCategory> serviceCategoryList = serviceCategoryMapper.selectByParams(criteria);
                for (ServiceCategory data : serviceCategoryList) {
                    if (Objects.nonNull(data.getEntityId()) && !data.getEntityId().equals(entity.getId())) {
                        throw new BizException(WebUtil.getMessage(MsgCd.ERROR_ENTITY_00008));
                    }
                }
            }
            // Modelarts共享资源池-关联OBS
            if (ProductCodeEnum.MODEL_ARTS.getProductCode().equals(serviceCategory.getServiceType())) {
                related(ProductCodeEnum.OBS.getProductCode(), entity.getId(), entity.getName());
                // 区域资源类型配置OBS
                List<BizBillingRegionResource> bizBillingRegionResourcesOBS = bizBillingRegionResourceMapper.selectByResourceType(
                        ProductCodeEnum.OBS.getProductCode());
                for (BizBillingRegionResource data : bizBillingRegionResourcesOBS) {
                    data.setEntityId(entity.getId());
                    data.setEntityName(entity.getName());
                }
                bizBillingRegionResources.addAll(bizBillingRegionResourcesOBS);
            }
            // HPC专属资源池-关联ECS、BMS
            if (ProductCodeEnum.HPC_DRP.getProductCode().equals(serviceCategory.getServiceType())) {
                related(ProductCodeEnum.ECS.getProductCode(), entity.getId(), entity.getName());
                related(ProductCodeEnum.BMS.getProductCode(), entity.getId(), entity.getName());
                // 区域资源类型配置OBS、BMS
                List<BizBillingRegionResource> bizBillingRegionResourcesECS = bizBillingRegionResourceMapper.selectByResourceType(
                        ProductCodeEnum.ECS.getProductCode());
                List<BizBillingRegionResource> bizBillingRegionResourcesBMS = bizBillingRegionResourceMapper.selectByResourceType(
                        ProductCodeEnum.BMS.getProductCode());
                bizBillingRegionResourcesECS.addAll(bizBillingRegionResourcesBMS);
                for (BizBillingRegionResource data : bizBillingRegionResourcesECS) {
                    data.setEntityId(entity.getId());
                    data.setEntityName(entity.getName());
                }
                bizBillingRegionResources.addAll(bizBillingRegionResourcesECS);
            }
            // HPC共享资源池线下版本-关联DME-OSP
            if (ProductCodeEnum.HPC_OFFLINE.getProductCode().equals(serviceCategory.getServiceType())) {
                related(ProductCodeEnum.DME_OSP.getProductCode(), entity.getId(), entity.getName());
                // 区域资源类型配置OBS、BMS
                List<BizBillingRegionResource> bizBillingRegionResourcesDMEOSP = bizBillingRegionResourceMapper.selectByResourceType(
                    ProductCodeEnum.DME_OSP.getProductCode());
                for (BizBillingRegionResource data : bizBillingRegionResourcesDMEOSP) {
                    data.setEntityId(entity.getId());
                    data.setEntityName(entity.getName());
                }
                bizBillingRegionResources.addAll(bizBillingRegionResourcesDMEOSP);
            }
        } else {
            int serviceId = serviceOrderDetailMapper.countByParams(new Criteria("serviceId", request.getServiceId()));
            if (serviceId > 0) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERROR_ENTITY_00007));
            }
            serviceCategory.setEntityId(null);
            serviceCategory.setEntityName(null);
            // 区域资源类型配置
            for (BizBillingRegionResource data : bizBillingRegionResources) {
                data.setEntityId(null);
                data.setEntityName(null);
            }
            // 取消关联产品
            for (SfProductTemplate date : sfProductTemplates) {
                serviceCategoryMapper.updateTemplateByEntityId(date.getId(), null);
            }
            // Modelarts共享资源池-取消关联OBS
            if (ProductCodeEnum.MODEL_ARTS.getProductCode().equals(serviceCategory.getServiceType())) {
                related(ProductCodeEnum.OBS.getProductCode(), null, null);
                // 区域资源类型配置OBS
                List<BizBillingRegionResource> bizBillingRegionResourcesOBS = bizBillingRegionResourceMapper.selectByResourceType(
                        ProductCodeEnum.OBS.getProductCode());
                for (BizBillingRegionResource data : bizBillingRegionResourcesOBS) {
                    data.setEntityId(null);
                    data.setEntityName(null);
                }
                bizBillingRegionResources.addAll(bizBillingRegionResourcesOBS);
            }
            // HPC专属资源池-取消关联ECS、BMS
            if (ProductCodeEnum.HPC_DRP.getProductCode().equals(serviceCategory.getServiceType())) {
                related(ProductCodeEnum.ECS.getProductCode(), null, null);
                related(ProductCodeEnum.BMS.getProductCode(), null, null);
                // 区域资源类型配置OBS、BMS
                List<BizBillingRegionResource> bizBillingRegionResourcesECS = bizBillingRegionResourceMapper.selectByResourceType(
                        ProductCodeEnum.ECS.getProductCode());
                List<BizBillingRegionResource> bizBillingRegionResourcesBMS = bizBillingRegionResourceMapper.selectByResourceType(
                        ProductCodeEnum.BMS.getProductCode());
                bizBillingRegionResourcesECS.addAll(bizBillingRegionResourcesBMS);
                for (BizBillingRegionResource data : bizBillingRegionResourcesECS) {
                    data.setEntityId(null);
                    data.setEntityName(null);
                }
                bizBillingRegionResources.addAll(bizBillingRegionResourcesECS);
            }
            // HPC共享资源池线下版本-取消关联DME-OSP
            if (ProductCodeEnum.HPC_OFFLINE.getProductCode().equals(serviceCategory.getServiceType())) {
                related(ProductCodeEnum.DME_OSP.getProductCode(), null, null);
                // 区域资源类型配置OBS、BMS
                List<BizBillingRegionResource> bizBillingRegionResourcesDMEOSP = bizBillingRegionResourceMapper.selectByResourceType(
                    ProductCodeEnum.DME_OSP.getProductCode());
                for (BizBillingRegionResource data : bizBillingRegionResourcesDMEOSP) {
                    data.setEntityId(null);
                    data.setEntityName(null);
                }
                bizBillingRegionResources.addAll(bizBillingRegionResourcesDMEOSP);
            }
        }
        serviceCategoryMapper.updateByPrimaryKey(serviceCategory);
        // 更新区域资源类型配置
        for (BizBillingRegionResource data : bizBillingRegionResources) {
            bizBillingRegionResourceMapper.updateByPrimaryKey(data);
            // 查询区域资源类型下的计费配置，计费策略关联上运营实体
            List<BizBillingRegionCharge> bizBillingRegionCharges = bizBillingRegionResourceMapper.selectChargeByRegionResourceId(
                    data.getId());
            if (CollectionUtils.isNotEmpty(bizBillingRegionCharges)) {
                for (BizBillingRegionCharge bizBillingRegionCharge : bizBillingRegionCharges) {
                    Long strategyId = bizBillingRegionCharge.getStrategyId();
                    BizBillingStrategy bizBillingStrategy = bizBillingStrategyMapper.selectByPrimaryKey(
                        strategyId);
                    bizBillingStrategy.setEntityId(data.getEntityId());
                    bizBillingStrategyMapper.updateByPrimaryKey(bizBillingStrategy);

                }
                //计费规格族需要迁移
                bizBillingSpecGroupMapper.updateBatchEntityId(bizBillingRegionCharges.stream().map(BizBillingRegionCharge::getStrategyId).collect(Collectors.toList()), data.getEntityId());

            }
        }
    }

    private static boolean isHpcCategory(ServiceCategory serviceCategory) {
        return ProductCodeEnum.HPC.getProductCode().equals(serviceCategory.getServiceType())
            || ProductCodeEnum.HPC_SAAS.getProductCode().equals(serviceCategory.getServiceType())
            || ProductCodeEnum.HPC_OFFLINE.getProductCode().equals(serviceCategory.getServiceType());
    }

    /**
     * 关联HPC
     */
    public void relatedHPC(ServiceCategory serviceCategory, RelatedProductsRequest request, Entity entity) {
        serviceCategory.setProductName(request.getProductName());
        serviceCategory.setEntityId(entity.getId());
        serviceCategory.setEntityName(entity.getName());
        serviceCategoryMapper.updateByPrimaryKey(serviceCategory);
        // 关联SFS
        related(ProductCodeEnum.SFS.getProductCode(), entity.getId(), entity.getName());
    }

    /**
     * 关联HPC-SAAS
     */
    public Long relatedHPC_SAAS(ServiceCategory serviceCategory, RelatedProductsRequest request, Entity entity) {
        ServiceCategory serviceCategoryInsert = BeanConvertUtil.convert(serviceCategory,
                                                                        ServiceCategory.class);
        if (ObjectUtils.isEmpty(serviceCategoryInsert)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1746033110));
        }
        serviceCategoryInsert.setProductName(request.getProductName());
        serviceCategoryInsert.setEntityId(entity.getId());
        serviceCategoryInsert.setEntityName(entity.getName());
        WebUserUtil.prepareInsertParams(serviceCategoryInsert);
        serviceCategoryInsert.setServiceType(ProductCodeEnum.HPC_SAAS.getProductCode());
        serviceCategoryInsert.setServiceForm(ProductCodeEnum.HPC_SAAS.getProductCode());
        serviceCategoryInsert.setShowType(ProductCodeEnum.HPC_SAAS.getProductCode());
        serviceCategoryInsert.setProductCode(ProductCodeEnum.HPC_SAAS.getProductCode());
        serviceCategoryMapper.insert(serviceCategoryInsert);
        return serviceCategoryInsert.getId();
    }

    /**
     * 关联产品对应资源
     *
     * @param productCode 资源code
     * @param entityId 实例ID
     * @param entityName 实例名称
     */
    public void related(String productCode, Long entityId, String entityName) {
        Criteria criteria = new Criteria();
        criteria.put("publish_status", "succeed");
        criteria.put("editable", ZERO);
        if (ProductCodeEnum.ECS.getProductCode().equals(productCode) ) {
            criteria.put("editable", ONE);
        }
        criteria.put("publishDtIsNotNull", true);
        criteria.put("serviceType", productCode);
        // 关联资源
        List<ServiceCategory> serviceCategoryList = serviceCategoryMapper.selectByParams(criteria);
        ServiceCategory serviceCategoryHPCDRP = serviceCategoryList.get(0);
        if (Objects.nonNull(entityId) && Objects.nonNull(entityName)) {
            serviceCategoryHPCDRP.setEntityId(entityId);
            serviceCategoryHPCDRP.setEntityName(entityName);
            serviceCategoryMapper.updateByPrimaryKey(serviceCategoryHPCDRP);
            // 关联资源对应模板
            List<SfProductTemplate> sfProductTemplates = sfProductTemplateMapper.selectByParams(
                    new Criteria("templateType", productCode));
            for (SfProductTemplate date : sfProductTemplates) {
                serviceCategoryMapper.updateTemplateByEntityId(date.getId(), entityId);
            }
            // 取消关联资源
        } else {
            serviceCategoryHPCDRP.setEntityId(null);
            serviceCategoryHPCDRP.setEntityName(null);
            serviceCategoryMapper.updateByPrimaryKey(serviceCategoryHPCDRP);
            // 取消关联资源对应模板
            List<SfProductTemplate> sfProductTemplates = sfProductTemplateMapper.selectByParams(
                    new Criteria("templateType", productCode));
            for (SfProductTemplate date : sfProductTemplates) {
                serviceCategoryMapper.updateTemplateByEntityId(date.getId(), null);
            }
        }
    }

    @Override
    public List<DescribePayChannelResponse> listPayChannel(Long entityId) {
        List<PayChannel> payChannels = payChannelMapper.selectByEntityId(entityId);
        //公钥私钥不直接返回
        if(CollectionUtil.isNotEmpty(payChannels)){
            for(PayChannel payChannel : payChannels){
                if(StringUtil.isNotBlank(payChannel.getAppPrivateKey())){
                    payChannel.setAppPrivateKey("******");
                }
                if(StringUtil.isNotBlank(payChannel.getAppPublicKey())){
                    payChannel.setAppPublicKey("******");
                }
            }
        }
        return BeanConvertUtil.convert(payChannels, DescribePayChannelResponse.class);
    }

    @Override
    public Entity getByCategoryId(Long categoryId) {
        return entityMapper.getBycategoryId(categoryId);
    }

}
