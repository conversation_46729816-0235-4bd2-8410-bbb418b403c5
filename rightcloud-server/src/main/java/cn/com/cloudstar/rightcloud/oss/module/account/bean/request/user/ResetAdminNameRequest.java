package cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 重置密码失败 createTime:2022-11-02 18:09
 *
 * <AUTHOR>
 * @version 1.0
 * @since JDK1.8
 */
@Data
public class ResetAdminNameRequest implements Serializable {
    private static final long serialVersionUID = 1L;


    @ApiModelProperty("密码")
    @NotBlank
    private String password;

    @ApiModelProperty("用户名")
    @NotBlank
    private String account;

    @ApiModelProperty(value = "用户ID")
    private String userSid;



    @Override
    public String toString() {
        return ReflectionToStringBuilder.toString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
