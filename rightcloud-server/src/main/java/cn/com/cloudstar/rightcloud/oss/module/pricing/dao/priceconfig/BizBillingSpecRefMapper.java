/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.module.pricing.dao.priceconfig;

import cn.com.cloudstar.rightcloud.oss.common.mybatis.annotation.BusinessEntityPermission;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

import cn.com.cloudstar.rightcloud.oss.module.pricing.bean.priceconfig.model.BizBillingSpecRef;
import cn.com.cloudstar.rightcloud.oss.module.pricing.bean.priceconfig.model.BizBillingSpecRefExample;
import cn.com.cloudstar.rightcloud.oss.module.pricing.bean.priceconfig.response.BizBillingSpecRefResponse;

@Repository
public interface BizBillingSpecRefMapper {

    long countByExample(BizBillingSpecRefExample example);

    int deleteByExample(BizBillingSpecRefExample example);

    int deleteByPrimaryKey(Long dynamicSid);

    int insert(BizBillingSpecRef record);

    int insertSelective(BizBillingSpecRef record);

    List<BizBillingSpecRef> selectByExampleWithBLOBs(BizBillingSpecRefExample example);

    List<BizBillingSpecRef> selectByExample(BizBillingSpecRefExample example);

    BizBillingSpecRef selectByPrimaryKey(Long dynamicSid);

    int updateByExampleSelective(@Param("record") BizBillingSpecRef record, @Param("example") BizBillingSpecRefExample example);

    int updateByExampleWithBLOBs(@Param("record") BizBillingSpecRef record, @Param("example") BizBillingSpecRefExample example);

    int updateByExample(@Param("record") BizBillingSpecRef record, @Param("example") BizBillingSpecRefExample example);

    int updateByPrimaryKeySelective(BizBillingSpecRef record);

    int updateByPrimaryKeyWithBLOBs(BizBillingSpecRef record);

    int updateByPrimaryKey(BizBillingSpecRef record);

    @BusinessEntityPermission(tableAlias = "ssc")
    List<BizBillingSpecRefResponse> pageList(BizBillingSpecRefExample example);

    @Select("select specRef.dynamic_sid, specRef.`value`, spec.category, spec.spec_name " +
            "from biz_billing_spec_ref specRef " +
            "left join biz_billing_spec spec on spec.id = specRef.spec_id " +
            "where specRef.resource_type =#{resourceType}")
    List<BizBillingSpecRef> selectByResourceType(@Param("resourceType") String resourceType);

    @Update("update biz_billing_spec_ref set `value` =#{value} where resource_type =#{resourceType}")
    int updateValueByResourceType(@Param("value") String value, @Param("resourceType") String resourceType);
}