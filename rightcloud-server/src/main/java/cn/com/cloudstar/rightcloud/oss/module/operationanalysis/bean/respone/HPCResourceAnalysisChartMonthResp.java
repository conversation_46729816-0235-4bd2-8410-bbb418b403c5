package cn.com.cloudstar.rightcloud.oss.module.operationanalysis.bean.respone;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 月度列表
 *
 * <AUTHOR>
 * @date 2023-03-20 14:20
 */
@Data
public class HPCResourceAnalysisChartMonthResp {

    /**
     * 灌装周期
     */
    private String fillingCycle;

    /**
     * 月平均算力填充率
     */
    private BigDecimal rate = BigDecimal.ZERO;

    /**
     * 月累计资源使用时长（小时数）
     */
    private BigDecimal monthTotalUsed = BigDecimal.ZERO;
    /**
     * 资源总量
     */
    private BigDecimal resourceTotal = BigDecimal.ZERO;
    /**
     * 当月天数
     */
    private Long monthDayNum;
    /**
     * 每日计算时间范围小时数，取值范围1-24，默认15
     */
    private Long computeHours = 15L;

}
