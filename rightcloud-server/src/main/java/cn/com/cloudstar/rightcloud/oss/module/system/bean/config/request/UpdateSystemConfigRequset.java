/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.module.system.bean.config.request;

import org.hibernate.validator.constraints.NotBlank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @createDate 2020/03/17 18:23
 */
@Data
@ApiModel(description = "根据key更新sysconfig的value")
public class UpdateSystemConfigRequset {

    /**
     * 关键
     */
    @NotBlank
    @ApiModelProperty("key")
    private String key;

    /**
     * 值
     */
    @NotBlank
    @ApiModelProperty("value")
    private String value;
}
