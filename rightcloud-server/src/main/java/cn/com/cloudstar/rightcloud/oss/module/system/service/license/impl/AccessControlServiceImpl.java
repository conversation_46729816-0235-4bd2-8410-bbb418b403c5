package cn.com.cloudstar.rightcloud.oss.module.system.service.license.impl;

import com.alibaba.fastjson.JSONObject;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.common.constants.AuthModule;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.BQ;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.BQ.BQ03;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.CB;
import cn.com.cloudstar.rightcloud.common.constants.SysAuthModuleAuthKey.D.DD;
import cn.com.cloudstar.rightcloud.common.constants.SysAuthModuleAuthKey.E.E09;
import cn.com.cloudstar.rightcloud.common.constants.SysAuthModuleAuthKey.E.E17;
import cn.com.cloudstar.rightcloud.common.constants.SysAuthModuleAuthKey.F.F07;
import cn.com.cloudstar.rightcloud.common.util.CrytoUtilSimple;
import cn.com.cloudstar.rightcloud.core.pojo.dto.sfs.ServiceCategory;
import cn.com.cloudstar.rightcloud.oss.common.constants.Constants;
import cn.com.cloudstar.rightcloud.oss.common.pojo.CloudEnvVO;
import cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.oss.common.pojo.LicenseVo;
import cn.com.cloudstar.rightcloud.oss.common.pojo.PaasVO;
import cn.com.cloudstar.rightcloud.oss.common.util.LicenseUtil;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.dao.ServiceCategoryMapper;
import cn.com.cloudstar.rightcloud.oss.module.system.dao.license.AccessControlMapper;
import cn.com.cloudstar.rightcloud.oss.module.system.dao.license.InterimMapper;
import cn.com.cloudstar.rightcloud.oss.module.system.service.license.AccessControlService;

/**
 * 权限控制实现
 *
 * <AUTHOR>
 * @Date: 2023/09/21
 */
@Slf4j
@Service
public class AccessControlServiceImpl implements AccessControlService {

    /**
     * 支付方式
     */
    private static final List<String> PAYMENT_METHOD = Arrays.asList("alipay", "wechatpay", "unionpay");

    @Autowired
    private AccessControlMapper accessControlMapper;

    @Autowired
    private InterimMapper interimMapper;

    @Autowired
    private ServiceCategoryMapper serviceCategoryMapper;

    @Override
    public void versionType(LicenseVo licens) {
        // 标准版隐藏hpc相关产品和信息
        // closeAI隐藏AI相关产品
        log.info("AccessControlServiceImpl.versionType: {}", licens.getVersionType());
        if (LicenseUtil.STAND.equals(licens.getVersionType())) {
            accessControlMapper.hideHPC();
            interimMapper.updateConfig("license_auth_hide", LicenseUtil.CLOSE);
        } else if (LicenseUtil.CLOSE_AI.equals(licens.getVersionType())) {
            accessControlMapper.hideAI();
            interimMapper.updateConfig("license_auth_hide", LicenseUtil.CLOSE);
        } else if (LicenseUtil.OPEN_AI.equals(licens.getVersionType())) {
            accessControlMapper.openAI();
            interimMapper.updateConfig("license_auth_hide", LicenseUtil.CLOSE);
        } else if (LicenseUtil.ENHANCE.equals(licens.getVersionType())) {
            accessControlMapper.openHPC();
            accessControlMapper.openAI();
            interimMapper.updateConfig("license_auth_hide", LicenseUtil.OPEN);
        }

        String[] modules = licens.getModules().split(StrUtil.COMMA);
        Set<String> licenseModules = new HashSet<>(Arrays.asList(modules));

        // 通过许可证是否勾选CB1710-创建专属资源池权限启用HPC-DRP相关权限
        if (licenseModules.contains(CB.CB1710)) {
            accessControlMapper.openHPCDRP();
        }

        // 910B算力资源池高级扩展套餐包
        if (licens.isAi1980BAdvancedExpansionPackage()) {
            licens.setAi1980BLogicalResourcePool(true);
            licens.setAi1980BChipSupport(true);
        }

        // 是否勾选F0703-国密服务设置权限
        accessControlMapper.controlZK03(licenseModules.contains(F07.F0703) ? Constants.ONE_INT : Constants.ZERO_INT);
        // 是否勾选E1703/DD02-镜像容器服务权限
        accessControlMapper.controlBH08(licenseModules.contains(E17.E1703) ? Constants.ONE_INT : Constants.ZERO_INT);
        accessControlMapper.controlCB21(licenseModules.contains(DD.DD02) ? Constants.ONE_INT : Constants.ZERO_INT);
        // 是否勾选BQ0112-清理权限
        accessControlMapper.controlBQ0112(licenseModules.contains(BQ.BQ0112) ? Constants.ONE_INT : Constants.ZERO_INT);
        // 是否勾选BQ0311、BQ0113-试用期
        accessControlMapper.controlBQ0311AndBQ0113(notContains(licenseModules) ? Constants.TWO_INT : Constants.ZERO_INT,
                                                   notContains(licenseModules) ? Constants.ZERO_INT : Constants.ONE_INT);
        // 是否勾选E0903-套餐包权限
        accessControlMapper.controlBL03(licenseModules.contains(E09.E0903) ? Constants.ONE_INT : Constants.ZERO_INT);

        if (!LicenseUtil.STAND.equals(licens.getVersionType())) {
            // HPC月均算力统计hpcMonthlyAverageComputing
            accessControlMapper.hpcMonthlyAverageComputing(licens.isHpcMonthlyAverageComputing() ? Constants.TWO_INT : Constants.ZERO_INT);
            accessControlMapper.orderTypeAndStatus(licens.isHpcMonthlyAverageComputing() ? Constants.ZERO_INT : Constants.ONE_INT);
            accessControlMapper.contractDiscountScope(licens.isHpcMonthlyAverageComputing() ? Constants.ZERO_INT : Constants.ONE_INT);
        }
        if (!LicenseUtil.CLOSE_AI.equals(licens.getVersionType())) {
            // MA支持evs计费maSupportsEvsBilling
            accessControlMapper.maSupportsEvsBilling(licens.isMaSupportsEvsBilling() ? Constants.ZERO_INT : Constants.ONE_INT);
            // 对象存储隔离安全
            accessControlMapper.objectStorageIsolationSecurity(licens.isObjectStorageIsolationSecurity() ? Constants.ZERO_INT : Constants.ONE_INT);
        }
        // 1980B算力逻辑专属资源池模块全生命周期管理
        if (licens.isAi1980BLogicalResourcePool()) {
            accessControlMapper.ai1980BLogicalResourcePoolZero();
        } else {
            accessControlMapper.ai1980BLogicalResourcePoolOne();
        }

        // HPC月均算力统计hpcMonthlyAverageComputing
        // AI1980B芯片支持ai1980BChipSupport
        accessControlMapper.orderTypeAndStatus((licens.isHpcMonthlyAverageComputing() || licens.isAi1980BChipSupport()) ? Constants.ZERO_INT : Constants.ONE_INT);
        accessControlMapper.contractDiscountScope((licens.isHpcMonthlyAverageComputing() || licens.isAi1980BChipSupport()) ? Constants.ZERO_INT : Constants.ONE_INT);
        // 通过许可证是否勾选CB1710-创建专属资源池权限禁用HPC-DRP相关权限
        if (!licenseModules.contains(CB.CB1710)) {
            accessControlMapper.hideHPCDRP();
        }

        // 弹性裸金属服务高级扩展套餐包
        accessControlMapper.controlBmsPremiumExpansionPackage(licens.isBmsPremiumExpansionPackage() ? Constants.ONE_INT : Constants.ZERO_INT);

        accessControlMapper.openResource();

        // 修改sys_m_code
        List<CloudEnvVO> cloudEnvVOS = JSONObject.parseArray(licens.getCloudEnvResourceControl(), CloudEnvVO.class);
        HashSet<String> paas = new HashSet<>();
        for (CloudEnvVO cloudEnvVO : cloudEnvVOS) {
            paas.addAll(
                    cloudEnvVO.getPaas()
                              .stream()
                              .filter(t -> String.valueOf(Constants.ONE_INT).equals(t.getChecked()))
                              .map(PaasVO::getName)
                              .collect(
                                      Collectors.toList()));
        }
        List<ServiceCategory> paasNameList = serviceCategoryMapper.selectByParams(new Criteria("paasNameList", paas));
        accessControlMapper.codeControl(
                paasNameList.stream().map(ServiceCategory::getServiceType).collect(Collectors.toList()));

    }

    private static boolean notContains(Set<String> licenseModules) {
        return !licenseModules.contains(BQ03.BQ0311) && !licenseModules.contains(BQ.BQ0113);
    }

    @Override
    public void isInitModule(LicenseVo license) {
        //涉及权限ZF10，ZF1001
        //ture：幂等性添加以上权限  false：初始化时删除以上权限;
        if (license.isInitModule()) {
            if (interimMapper.countModuleByModuleSid(AuthModule.ZF.ZF10.ZF10) == 0) {
                interimMapper.insertModuleZF10();
            }
            if (interimMapper.countModuleByModuleSid(AuthModule.ZF.ZF10.ZF1001) == 0) {
                interimMapper.insertModuleZF1001();
            }
        } else {
            interimMapper.deleteBatchModule(Arrays.asList(AuthModule.ZF.ZF10.ZF10, AuthModule.ZF.ZF10.ZF1001));
        }
        //没有则关闭此支付；有则打开
        String turnOff = CrytoUtilSimple.encrypt("turnOff");
        String turnOn = CrytoUtilSimple.encrypt("turnOn");
        if (!ObjectUtils.isEmpty(license.getPaymentMethod())) {
            List<String> payments = StrUtil.splitTrim(license.getPaymentMethod(), ",");
            //取交集，则是需要打开的支付方式
            List<String> turnOns = new ArrayList<>(CollectionUtil.intersection(PAYMENT_METHOD, payments));
            //取差集，则是需要关闭的支付方式
            List<String> turnOffs = CollectionUtil.subtractToList(PAYMENT_METHOD, payments);
            if (!ObjectUtils.isEmpty(turnOns)) {
                interimMapper.updateBatchConfigVal(changePayments(turnOns), turnOn);
                interimMapper.updateBatchConfigDisplay(getConfigTypes(turnOns), "1");
            }
            if (!ObjectUtils.isEmpty(turnOffs)) {
                interimMapper.updateBatchConfigVal(changePayments(turnOffs), turnOff);
                interimMapper.updateBatchConfigDisplay(getConfigTypes(turnOffs), "0");
            }
        } else {
            interimMapper.updateBatchConfigVal(changePayments(PAYMENT_METHOD), turnOff);
            interimMapper.updateBatchConfigDisplay(getConfigTypes(PAYMENT_METHOD), "0");
        }
    }

    private List<String> changePayments(List<String> payments) {
        return payments.stream().map(v -> {
            switch (v) {
                case "alipay":
                    return "alipay.switch";
                case "wechatpay":
                    return "wechatpay.swithch";
                case "unionpay":
                    return "unionpay.switch";
                default:
                    return v;
            }
        }).collect(Collectors.toList());
    }

    private List<String> getConfigTypes(List<String> payments) {
        return payments.stream().map(v -> {
            switch (v) {
                case "alipay":
                    return "alipay_config";
                case "wechatpay":
                    return "wechatpay_config";
                case "unionpay":
                    return "unionpay_config";
                default:
                    return v;
            }
        }).collect(Collectors.toList());
    }

    @Override
    public boolean checkModuleSid(String moduleSid) {
        LicenseVo licenseVo = LicenseUtil.queryLicenseInfoFromDb();
        String[] modules = licenseVo.getModules().split(StrUtil.COMMA);
        Set<String> licenseModuleStrs = new HashSet<>(Arrays.asList(modules));
        return licenseModuleStrs.contains(moduleSid);
    }
}
