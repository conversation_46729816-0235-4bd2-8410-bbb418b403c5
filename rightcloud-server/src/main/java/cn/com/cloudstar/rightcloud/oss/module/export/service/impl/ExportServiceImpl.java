
package cn.com.cloudstar.rightcloud.oss.module.export.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.SpreadsheetVersion;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.aggregation.ConvertOperators;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.ReflectionUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.common.common.PageResult;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.exception.BizException;
import cn.com.cloudstar.rightcloud.common.util.CrytoUtilSimple;
import cn.com.cloudstar.rightcloud.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.common.util.excel.ExcelUtil;
import cn.com.cloudstar.rightcloud.common.util.excel.builder.ExcelExportInfo;
import cn.com.cloudstar.rightcloud.common.util.excel.service.ExcelPageExportService;
import cn.com.cloudstar.rightcloud.core.pojo.constant.DiscountPolicyEnum;
import cn.com.cloudstar.rightcloud.core.pojo.dto.audit.ServiceOrderDetail;
import cn.com.cloudstar.rightcloud.core.pojo.dto.bss.BizDiscountOrder;
import cn.com.cloudstar.rightcloud.core.pojo.dto.charge.BizBillingAccount;
import cn.com.cloudstar.rightcloud.core.pojo.dto.file.SysMFilePath;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.ActionLogInfo;
import cn.com.cloudstar.rightcloud.core.pojo.dto.ticket.WorkTicket;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.Company;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.Role;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.User;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.UserExcel;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.UserExcelUs;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.UserOrg;
import cn.com.cloudstar.rightcloud.core.pojo.vo.audit.InstanceGaapCostSumVO;
import cn.com.cloudstar.rightcloud.core.pojo.vo.audit.ServiceOrderDTO;
import cn.com.cloudstar.rightcloud.core.pojo.vo.system.SysTActionLogVo;
import cn.com.cloudstar.rightcloud.module.support.access.constants.DataScopeEnum;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.module.support.file.storage.bean.enums.StoragePathEnum;
import cn.com.cloudstar.rightcloud.module.support.file.storage.bean.vo.StorageResult;
import cn.com.cloudstar.rightcloud.oss.common.ccsp.CCSPCacheUtil;
import cn.com.cloudstar.rightcloud.oss.common.constants.status.CertificationStatus;
import cn.com.cloudstar.rightcloud.oss.common.constants.status.OrderStatus;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.OrderType;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.OrgType;
import cn.com.cloudstar.rightcloud.oss.common.enums.CertificationStatusEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.CertificationTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.OssExportTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.StatusTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.SysMFileTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.TicketConstant;
import cn.com.cloudstar.rightcloud.oss.common.enums.UserStatusEnum;
import cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.oss.common.util.AuthUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.DataScopeUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.ZipUtil;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user.DescribeUserRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.org.OrgMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.user.UserMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.user.UserOrgMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.service.company.CompanyService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.role.RoleService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.user.UserService;
import cn.com.cloudstar.rightcloud.oss.module.auth.response.RightCloudResult;
import cn.com.cloudstar.rightcloud.oss.module.bigscrean.bean.resscreen.ResScreenTopResult;
import cn.com.cloudstar.rightcloud.oss.module.export.bean.BizDownload;
import cn.com.cloudstar.rightcloud.oss.module.export.dao.BizDownloadMapper;
import cn.com.cloudstar.rightcloud.oss.module.export.service.ExportBaseService;
import cn.com.cloudstar.rightcloud.oss.module.export.service.ExportService;
import cn.com.cloudstar.rightcloud.oss.module.feign.service.ResourceDcExportFeignService;
import cn.com.cloudstar.rightcloud.oss.module.feign.service.SystemFeignService;
import cn.com.cloudstar.rightcloud.oss.module.file.service.SysMFilePathService;
import cn.com.cloudstar.rightcloud.oss.module.operationanalysis.bean.request.FillingRateForMonthReq;
import cn.com.cloudstar.rightcloud.oss.module.operationanalysis.bean.respone.FillingRateForMonthResp;
import cn.com.cloudstar.rightcloud.oss.module.operationanalysis.bean.respone.HPCResourceAnalysisChartResp;
import cn.com.cloudstar.rightcloud.oss.module.operationanalysis.service.impl.HPCResourceAnalysisServiceImpl;
import cn.com.cloudstar.rightcloud.oss.module.order.dao.ServiceOrderDetailMapper;
import cn.com.cloudstar.rightcloud.oss.module.order.service.ServiceOrderService;
import cn.com.cloudstar.rightcloud.oss.module.others.dao.ticket.WorkTicketMapper;
import cn.com.cloudstar.rightcloud.oss.module.pricing.dao.priceconfig.BizBillingAccountMapper;
import cn.com.cloudstar.rightcloud.oss.module.resource.request.ResViewQueryRequest;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.bean.request.DescribeOrderRequest;
import cn.com.cloudstar.rightcloud.oss.module.system.bean.userlog.ExportUserActionLogRequest;
import cn.com.cloudstar.rightcloud.oss.module.system.bean.userlog.ResTraceLog;
import cn.com.cloudstar.rightcloud.oss.module.system.bean.userlog.TraceLogRequest;
import cn.com.cloudstar.rightcloud.oss.module.system.service.config.ActionLogService;
import cn.com.cloudstar.rightcloud.oss.module.system.service.message.BusinessNotificationService;
import cn.com.cloudstar.rightcloud.oss.module.ticket.service.WorkTicketService;
import cn.com.cloudstar.rightcloud.oss.util.ConsoleAuthUtil;
import cn.com.cloudstar.rightcloud.remote.api.pojo.system.cmp.system.form.ResTraceLogForm;

/**
 * <p>
 * 导出 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-06
 */
@Service
@Slf4j
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
public class ExportServiceImpl extends ExportBaseService implements ExportService {


    /**
     * .zip
     */
    private static final String ZIP = ".zip";


    /**
     * orgSid
     */
    private static final String ORG_SID = "orgSid";

    /**
     * ownerId
     */
    private static final String OWNER_ID = "ownerId";

    /**
     * statusName
     */
    private static final String STATUS_NAME = "statusName";

    /**
     * type
     */
    private static final String TYPE = "type";

    private static final Map<String, String> CHARGE_TYPE = Maps.newHashMap();
    private static final Map<String, String> CHARGING_TYPE = Maps.newHashMap();

    private static final Map<String, String> STATUS = Maps.newHashMap();

    private static final Map<String, String> CERTIFICATION_STATUS = Maps.newHashMap();

    private static final Map<String, String> TYPE_MAP = Maps.newHashMap();

    static {
        CHARGE_TYPE.put("PrePaid", "包年包月");
        CHARGE_TYPE.put("PostPaid", "按量付费");
        CHARGE_TYPE.put("None", "不计费");
        CHARGING_TYPE.put("01","正常计费");
        CHARGING_TYPE.put("02","销售计费");


        STATUS.put("1", "启用");
        STATUS.put("0", "禁用");
        STATUS.put("2", "待审核");
        STATUS.put("4", "已拒绝");

        CERTIFICATION_STATUS.put("noAuth", "待认证");
        CERTIFICATION_STATUS.put("authing", "认证中");
        CERTIFICATION_STATUS.put("authSucceed", "认证成功");
        CERTIFICATION_STATUS.put("authFiled", "认证失败");

        TYPE_MAP.put("user", "个人认证");
        TYPE_MAP.put("company", "企业认证");




    }

    @Autowired
    private ResourceDcExportFeignService dcExportFeignService;

    @Autowired
    private BizDownloadMapper bizDownloadMapper;

    @Autowired
    @Lazy
    private ServiceOrderService serviceOrderService;

    @Autowired
    private OrgMapper orgMapper;

    @Autowired
    private UserOrgMapper userOrgMapper;

    @Autowired
    private ServiceOrderDetailMapper serviceOrderDetailMapper;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private WorkTicketService workTicketService;

    @Autowired
    private SysMFilePathService sysMFilePathService;

    @Autowired
    private BusinessNotificationService businessNotificationService;

    @Autowired
    private HPCResourceAnalysisServiceImpl resourceAnalysisServiceImpl;

    @Autowired
    private ActionLogService actionLogService;

    @Autowired
    private UserMapper userMapper;
    @Autowired
    private UserService userService;

    @Autowired
    private WorkTicketMapper workTicketMapper;
    @Autowired
    private RoleService roleService;
    @Autowired
    private BizBillingAccountMapper bizBillingAccountMapper;
    @Autowired
    private CompanyService companyService;

    @Autowired
    private SystemFeignService systemFeignService;

    @Override
    @Async("exportExecutor")
    public void doAsynExportOrderService(DescribeOrderRequest req, String moduleType, Long taskId,
                                         AuthUser authUserInfo) {
        String acceptLanguage = authUserInfo.getAcceptLanguage();
        String taskName = OssExportTypeEnum.ORDER_LIST.getNameIsUs(acceptLanguage) ;
        Date start = new Date();
        startLog(taskName, taskId, moduleType, start);

        boolean flag = "bss".equals(req.getModuleType());
        boolean en = WebUtil.getHeaderAcceptLanguage(acceptLanguage);
        String fileName = (flag ? taskName  : OssExportTypeEnum.MY_ORDER.getNameIsUs(acceptLanguage)) + "_" + createFileNameSuffix();
        String xlsTemplateFileName = flag ? "template/order-deal.xlsx" : "template/order-deal-console.xlsx";
        if (flag && en) {
            xlsTemplateFileName = "template/order-deal-us.xlsx";
        }
        if (!flag && en) {
            xlsTemplateFileName = "template/order-deal-console-us.xlsx";
        }
        //构建查询条件
        Criteria criteria = createOrderCriteria(req, authUserInfo);
        criteria.put("entityId", RequestContextUtil.getEntityId());
        //分页导出
        String finalXlsTemplateFileName = xlsTemplateFileName;
        ExcelUtil.pageWrite(new ExcelPageExportService<ServiceOrderDTO, ServiceOrderDTO>() {
            @Override
            public ExcelExportInfo info() {
                return ExcelExportInfo.builder()
                        .template(finalXlsTemplateFileName)
                        .pageSize(10000)
                        .build();
            }

            @Override
            public List<ServiceOrderDTO> data(int pageNum, int pageSize) {
                PageHelper.startPage(pageNum, pageSize);
                return serviceOrderService.selectOrders(criteria);
            }

            @Override
            public List<ServiceOrderDTO> processData(List<ServiceOrderDTO> data) {
                processOrder(data, en);
                return data;
            }

            @Override
            public void processStream(InputStream in) {
                //minio上传
                String zipFileName = fileName.replace(XLSX, ZIP);
                //压缩密码
                String password = UUID.randomUUID().toString().substring(0, 8);
                String encrypt = CrytoUtilSimple.encrypt(password);
                String obfuscatePassword = ZipUtil.obfuscatePassword(password, encrypt);
                //压缩文件
                try (InputStream inZip = ZipUtil.compress(in, fileName, obfuscatePassword, true)){
                    StorageResult storageResult = uploadFile(inZip, moduleType, zipFileName);
                    updateDownloadSuccess(taskId, zipFileName, storageResult.getRelativeNginxUrl());
                    SysMFilePath sysMFilePath = new SysMFilePath();
                    sysMFilePath.setFileName(fileName);
                    sysMFilePath.setCompressPassword(encrypt);
                    sysMFilePath.setFilePath(CrytoUtilSimple.encrypt(storageResult.getRelativeNginxUrl()));
                    sysMFilePathService.saveSysMFilePath(sysMFilePath, SysMFileTypeEnum.ORDER, taskId);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                } finally {
                    IOUtils.closeQuietly(in);
                }
            }

            @Override
            public void errorAction(Exception e) {
                updateDownloadFailed(taskId, "下载异常，异常原因：" + e.getMessage());
                errorLog(taskName, taskId, moduleType, e.getMessage());
            }

            @Override
            public void nullAction() {
                updateDownloadFailed(taskId, "订单数据为空");
            }

            @Override
            public void endAction() {
                endLog(taskName, taskId, moduleType, start);
            }
        });
    }

    /**
     * 构建查询订单查询条件
     */
    private Criteria createOrderCriteria(DescribeOrderRequest req, AuthUser authUser) {
        Criteria criteria = new Criteria();
        criteria.setOrderByClause("a.created_dt DESC");
        criteria.setConditionObject(req);
        if ("bss".equals(req.getModuleType())) {
            if (Objects.nonNull(authUser.getOrgSid())) {
                criteria.put("orgSidIn", orgMapper.selectCustomerOrgSids(authUser.getOrgSid()));
            }
        } else {
            if (Objects.isNull(authUser.getOrgSid())) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERROR_MSG_00025));
            }
            if (ObjectUtils.isEmpty(req.getHasTenantAccess())) {
                // 租户管理员权限
                criteria.put(ORG_SID, authUser.getOrgSid());
            } else {
                // 非租户权限,只能查看自己所属项目的资源
                List<UserOrg> userOrgs = userOrgMapper.selectByParams(
                        new Criteria("userSid", authUser.getUserSid()));
                if (CollectionUtil.isNotEmpty(userOrgs)) {
                    criteria.put("projectIds", userOrgs.stream().map(UserOrg::getOrgSid).distinct()
                                                       .collect(Collectors.toList()));
                }
            }
            criteria.put(OWNER_ID, authUser.getUserSid());
        }
        //根据状态构建拆线呢条件
        conversionStatusName(req.getStatusName(), criteria);
        ConsoleAuthUtil.put(criteria,  AuthUtil.getAuthUser(), authUser.getEntityId());
        return criteria;
    }

    @Override
    public void doAsynExportTicketService(String moduleType, Long taskId,
                                          AuthUser authUserInfo, String perdiod) {
        {
            BizDownload up = new BizDownload();
            up.setUpdatedDt(new Date());
            up.setDownloadId(taskId);
            up.setAccountId(authUserInfo.getUserSid());
            InputStream inZip = null;
            boolean console = StringUtils.equalsIgnoreCase("console", moduleType);
            String type = console ? "UserDeal" : "ManagementDeal";
            SysMFilePath sysMFilePath = new SysMFilePath();
            String encrypt = "";
            String acceptLanguage = authUserInfo.getAcceptLanguage();
            boolean isUs = WebUtil.getHeaderAcceptLanguage(acceptLanguage);
            try {
                String xlsTemplateFileName = "template/ticket-all.xlsx";
                if (isUs) {
                    xlsTemplateFileName = "template/ticket-all-us.xlsx";
                }
                String dateFormat = fileNameSuffix();

                String taskName = OssExportTypeEnum.ORDER_LIST.getNameIsUs(acceptLanguage) ;
                // 文件名
                String destFileName =taskName + dateFormat + XLSX;
                String zipFileName = taskName + dateFormat + ZIP;

                String fileName = zipFileName;
                ByteArrayOutputStream outExcel = new ByteArrayOutputStream();
                Criteria criteria = new Criteria();
                criteria.setOrderByClause("created_dt DESC");
                if ("current".equals(perdiod)) {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    Calendar cal = Calendar.getInstance();
                    Date currentDate = new Date();
                    cal.setTime(currentDate);
                    cal.set(Calendar.HOUR_OF_DAY, 0);
                    cal.set(Calendar.MINUTE, 0);
                    cal.set(Calendar.SECOND, 0);
                    cal.set(Calendar.MILLISECOND, 0);
                    cal.set(Calendar.DAY_OF_MONTH, 1);
                    criteria.put("createdDtFromDate", cal.getTime());
                    //月底
                    cal.roll(Calendar.DAY_OF_MONTH, -1);
                    //下个月第一天
                    Calendar c = Calendar.getInstance();
                    c.setTime(cal.getTime());
                    c.add(Calendar.DAY_OF_MONTH, 1);
                    Date nextMonth = c.getTime();
                    criteria.put("createdDtToDate", nextMonth);
                } else {
                    LocalDate date = LocalDate.parse(perdiod.replace("-", "") + "01", DateTimeFormatter.BASIC_ISO_DATE);
                    // 指定年月的第一天
                    LocalDate dateFirst = date.with(TemporalAdjusters.firstDayOfMonth());
                    Instant instantStart = dateFirst.atTime(LocalTime.MIDNIGHT).atZone(ZoneId.systemDefault()).toInstant();
                    Date dateStart = Date.from(instantStart);
                    criteria.put("createdDtFromDate", dateStart);
                    // 指定年月的最后一天
                    LocalDate localDateEnd = date.with(TemporalAdjusters.lastDayOfMonth());
                    Instant instantEnd = localDateEnd.atTime(LocalTime.MIDNIGHT).atZone(ZoneId.systemDefault()).toInstant();
                    Date dateEnd = Date.from(instantEnd);
                    //下月初第一天
                    Calendar c = Calendar.getInstance();
                    c.setTime(dateEnd);
                    c.add(Calendar.DAY_OF_MONTH, 1);
                    Date nextMonth = c.getTime();
                    criteria.put("createdDtToDate", nextMonth);
                }
                log.info("doAsynExportOrderService exportExcel now:[{}], fileName:[{}]", DateUtil.formatDateTime(new Date()), destFileName);
                List<WorkTicket> workTickets = workTicketService.selectByParams(criteria);
                if (isUs && !CollectionUtils.isEmpty(workTickets)) {
                    workTickets.forEach(e -> {
                        if (TicketConstant.ANONYMITY_ID.equals(e.getOrgSid()) &&
                                TicketConstant.ANONYMITY_ID.equals(e.getTicketUserId())) {
                            e.setOrgName(workTicketService.getTemplateInfoNodeValue(e, TicketConstant.ORG_NAME));
                            e.setDistributorName(TicketConstant.DISTRIBUTOR_NAME);
                        }
                        e.setTicketCategoryName(StringUtils.replaceEach(e.getTicketCategoryName(), new String[]{"申诉补偿","故障类","技术支持","费用处理","用户投诉","售前咨询","其他"}, new String[]{"Appeal compensation","Fault class","technical support","Expense processing","User complaint","Pre sales consultation","Other"}));
                        e.setProductName(StringUtils.replaceEach(e.getProductName(), new String[]{"昇腾Modelarts共享资源池","昇腾Modelarts专属资源池"}, new String[]{"Ascending Modelarts Shared Resource Pool ModelArts", "Ascend Modelarts Exclusive Resource Pool"}));
                        e.setDistributorName(StringUtils.replaceEach(e.getDistributorName(), new String[]{"直营"}, new String[]{"be run directly by a manufacturer"}));
                        e.setStatus(StringUtils.replaceEach(e.getStatus(), new String[]{"未分配","处理中","处理完成","已关闭","已删除"}, new String[]{"unassigned","processing","processed","closed","deleted"}));
                    });
                }




                ExcelUtil.write()
                        .buildWriter(outExcel, xlsTemplateFileName)
                        .buildSheet()
                        .fill(workTickets)
                        .finish();
                InputStream inExcel = new ByteArrayInputStream(outExcel.toByteArray());

                //压缩密码
                String password = WebUtil.randomPwd(8);
                encrypt = CrytoUtilSimple.encrypt(password);
                String obfuscatePassword = ZipUtil.obfuscatePassword(password, encrypt);

                //压缩文件
                inZip = ZipUtil.compress(inExcel, destFileName, obfuscatePassword, true);
                //minio上传
                StorageResult result = storageService.saveFile(inZip, StoragePathEnum.TICKET.getPath(type), fileName, true,
                        true, null);
                up.setStatus(1);
                up.setDownloadPath(CrytoUtilSimple.encrypt(result.getRelativeNginxUrl()));
                up.setFileName(fileName);
                this.bizDownloadMapper.updateByPrimaryKeySelective(up);

                sysMFilePath.setFileName(fileName);
                sysMFilePath.setCompressPassword(encrypt);

                sysMFilePath.setFilePath(CrytoUtilSimple.encrypt(result.getRelativeNginxUrl()));
                sysMFilePathService.saveSysMFilePath(sysMFilePath, SysMFileTypeEnum.TICKET, up.getDownloadId());
                log.info("doAsynExportTicketService bizDownloadMapper now:[{}]", DateUtil.formatDateTime(new Date()));
            } catch (Exception e) {
                up.setStatus(4);
                up.setRemark("下载异常，异常原因：" + e.getMessage());
                this.bizDownloadMapper.updateByPrimaryKeySelective(up);
                log.error("ExportServiceImpl_doAsynExportTicketService_error_{}" + e.getMessage());
            } finally {
                IOUtils.closeQuietly(inZip);
            }
        }
    }

    private void processOrder(List<ServiceOrderDTO> serviceOrders, boolean en) {
        //获取订单详情
        List<Long> orderIds = serviceOrders.stream().map(ServiceOrderDTO::getId).collect(Collectors.toList());
        List<ServiceOrderDetail> orderDetails = serviceOrderDetailMapper.selectByParams(
                new Criteria("orderIds", orderIds));
        //根据详情获取类型map
        Map<Long, String> typeMap = orderDetails.stream()
                                                .filter(detail -> StrUtil.isNotEmpty(detail.getChargeType()))
                                                .collect(Collectors.toMap(ServiceOrderDetail::getOrderId,
                                                                          ServiceOrderDetail::getChargeType,
                                                                          (k1, k2) -> k1));
        //查询mongo账单数据
        List<String> collect = orderIds.stream().map(Object::toString).collect(Collectors.toList());
        org.springframework.data.mongodb.core.query.Criteria criteria = org.springframework.data.mongodb.core.query.Criteria.where(
                "orderId").in(collect);
        //采用聚合查询
        Aggregation aggregation = Aggregation.newAggregation(
                Aggregation.match(criteria),
                Aggregation.group("orderId").first("orderId").as("orderId")
                        .sum(ConvertOperators.valueOf("couponAmount").convertToDecimal()).as("couponAmount")
                        .sum(ConvertOperators.valueOf("deductCouponDiscount").convertToDecimal()).as("deductCouponDiscount")
                        .sum(ConvertOperators.valueOf("creditAmount").convertToDecimal()).as("creditAmount")
                        .sum(ConvertOperators.valueOf("cashAmount").convertToDecimal()).as("cashAmount"),
                Aggregation.project( "orderId", "couponAmount", "deductCouponDiscount", "creditAmount", "cashAmount")
        );
        AggregationResults<InstanceGaapCostSumVO> bizBillUsageItem = mongoTemplate.aggregate(aggregation, "biz_bill_usage_item", InstanceGaapCostSumVO.class);
        //根据orderId分组，所以可以直接转map
        Map<String, InstanceGaapCostSumVO> costMap = bizBillUsageItem.getMappedResults()
                                                                    .stream()
                                                                    .collect(Collectors.toMap(InstanceGaapCostSumVO::getOrderId, cost -> cost, (k1, k2) -> k1));
        //处理优惠金额为空
        serviceOrders.forEach(order -> {
            if (!ObjectUtils.isEmpty(order.getDiscounts())) {
                BigDecimal ratio = BigDecimal.ONE;
                for (BizDiscountOrder discountOrder : order.getDiscounts()) {
                    ratio = NumberUtil.mul(ratio, new BigDecimal(discountOrder.getDiscountRatio()));
                    discountOrder.setOriginType(DiscountPolicyEnum.codeFromName(discountOrder.getOriginType()));
                }
                order.setDiscountRatio(ratio.setScale(2, BigDecimal.ROUND_HALF_UP));
            }
            order.setChargeType(ObjectUtil.defaultIfBlank(typeMap.get(order.getId()), order.getChargeType()));
            order.setChargeTypeName(CHARGE_TYPE.get(order.getChargeType()));

            if (!ObjectUtils.isEmpty(order.getStepName()) && !OrderStatus.COMPLETED.equals(order.getStatus())) {
                order.setStatusName(order.getStepName());
            }
            //过滤此订单的账单数据
            String orderId = order.getId().toString();
            //获取订单金额数据
            InstanceGaapCostSumVO cost = costMap.get(orderId);

            order.setCreditAmount(BigDecimal.ZERO);
            order.setCouponAmount(BigDecimal.ZERO);
            order.setDeductCouponDiscount(BigDecimal.ZERO);
            order.setDiscountAmount(BigDecimal.ZERO);
            order.setCashAmount(BigDecimal.ZERO);

            if (!ObjectUtils.isEmpty(cost) && !"PostPaid".equals(order.getChargeType())) {
                order.setCreditAmount(cost.getCreditAmount());
                order.setCouponAmount(cost.getCouponAmount());
                order.setDeductCouponDiscount(cost.getDeductCouponDiscount());
                order.setCashAmount(cost.getCashAmount());

                BigDecimal orgDiscount = defaultDecimal(order.getOrgDiscount(), BigDecimal.ZERO);
                BigDecimal couponDiscount = defaultDecimal(order.getCouponDiscount(), BigDecimal.ZERO);
                //保留5位数
                BigDecimal discountAmountTotal = scaleDecimal(
                        orgDiscount.add(couponDiscount)
                                .add(cost.getCouponAmount())
                                .add(cost.getDeductCouponDiscount()), 5);
                order.setDiscountAmount(discountAmountTotal);
            }
            String statusName = getStatusName(order.getStatus(), order.getType());
            if (!ObjectUtils.isEmpty(statusName)) {
                order.setStatusName(statusName);
            }
            if (StringUtil.isNullOrEmpty(order.getDistributorName())) {
                order.setDistributorName("直营");
            }
            if (ObjectUtils.isEmpty(order.getPayTime())) {
                order.setPayTime(order.getCreatedDt());
            }
            order.setOrgDiscount(defaultDecimal(order.getOrgDiscount(), BigDecimal.ZERO));
            order.setCouponDiscount(defaultDecimal(order.getCouponDiscount(), BigDecimal.ZERO));
            order.setOriginalCost(defaultDecimal(order.getOriginalCost(), BigDecimal.ZERO));
            order.setEraseZeroAmount(defaultDecimal(order.getEraseZeroAmount(), BigDecimal.ZERO));
            //合同名称，为空默认为"--"
            order.setContractTitle(ObjectUtil.defaultIfBlank(order.getContractTitle(), " -- "));
            if (!ObjectUtils.isEmpty(order.getAccountId())) {
                order.setAccountIdStr(order.getAccountId().toString());
            }
            order.setDiscountAmountTotal(order.getOrgDiscount()
                                              .add(order.getCouponDiscount())
                                              .add(order.getCouponAmount())
                                              .add(order.getDeductCouponDiscount()));
            if (CHARGING_TYPE.get(order.getChargingType()) != null) {
                order.setChargingType(CHARGING_TYPE.get(order.getChargingType()));
            }else{
                order.setChargingType(CHARGING_TYPE.get("01"));
            }
            if (en) {
                order.setEntityName(StringUtils.replaceEach(order.getEntityName(), new String[]{"默认运营实体"}, new String[]{"Default operating entity"}));
                order.setName(StringUtils.replaceEach(order.getName(), new String[]{"昇腾Modelarts共享资源池", "套餐包"}, new String[]{"Ascending Modelarts Shared Resource Pool ModelArts", "Package package"}));
                order.setProductName(StringUtils.replaceEach(order.getProductName(), new String[]{"昇腾Modelarts共享资源池", "套餐包", "昇腾Modelarts专属资源池"}, new String[]{"Ascending Modelarts Shared Resource Pool ModelArts", "Package package", "Ascend Modelarts Exclusive Resource Pool"}));
                order.setChargeTypeName(StringUtils.replaceEach(order.getChargeTypeName(), new String[]{"包年包月", "按量付费", "不计费"}, new String[]{"Yearly Subscription", "Pay as you go", "No Billing"}));
                order.setTypeName(StringUtils.replaceEach(order.getTypeName(), new String[]{"订购", "退订", "缩容", "扩容", "续订"}, new String[]{"Order", "Unsubscribe", "Reduction", "Dilatation", "Renew"}));
                order.setStatusName(order.getStatus());
                order.setChargingType(StringUtils.replaceEach(order.getChargingType(), new String[]{"正常计费", "销售计费"}, new String[]{"Normal billing", "Sales billing"}));
                order.setSettlementType(StringUtils.replaceEach(order.getSettlementType(), new String[]{"标准价", "合同价"}, new String[]{"Standard price", "Contract price"}));
            }

        });
    }

    @Override
    public List<BizDownload> selectExportData() {
        return bizDownloadMapper.selectExportData();
    }

    @Override
    public int updateBizDownLoadByPrimaryKeySelective(BizDownload download) {
        return bizDownloadMapper.updateByPrimaryKeySelective(download);
    }

    @Override
    public void doAsynExportHpcResourceTrendChart(String moduleType, Long taskId, AuthUser authUserInfo,
                                                  String perdiod) {

        BizDownload up = new BizDownload();
        up.setUpdatedDt(new Date());
        up.setDownloadId(taskId);
        up.setAccountId(authUserInfo.getUserSid());
        ByteArrayOutputStream outExcel = null;
        InputStream inZip = null;
        InputStream inExcel = null;
        SysMFilePath sysMFilePath = new SysMFilePath();
        String encrypt = "";
        boolean console = StringUtils.equalsIgnoreCase("console", moduleType);
        String type = console ? "UserDeal" : "ManagementDeal";
        try {
            String xlsTemplateFileName = "template/hpc_resource_analysis_char.xlsx";
            if (WebUtil.getHeaderAcceptLanguage()) {
                xlsTemplateFileName = "template/hpc_resource_analysis_char-us.xlsx";
            }
            String dateFormat = fileNameSuffix();
            String destFileName = "HPC资源分析填充图" + dateFormat + XLSX;
            String zipFileName = "HPC资源分析填充图" + dateFormat + ZIP;
            String fileName = zipFileName;
            outExcel = new ByteArrayOutputStream();
            String startDate = null;
            String endDate = null;
            if (StrUtil.isNotBlank(perdiod)) {
                String[] split = perdiod.split("~");
                if (split.length > 2) {
                    return;
                }
                if (perdiod.contains("~")) {
                    if (split.length == 1){
                        endDate = split[0];
                    }
                    if (split.length == 2){
                        startDate = split[0];
                        endDate = split[1];
                    }
                }else {
                    startDate = split[0];
                }
            }
            List<HPCResourceAnalysisChartResp> trendChart =
                    resourceAnalysisServiceImpl.getTrendChart(startDate, endDate, authUserInfo.getEntityId());

            if (CollectionUtils.isEmpty(trendChart)) {
                up.setStatus(4);
                up.setRemark("实时算力填充率为空，请核查数据!");
                this.bizDownloadMapper.updateByPrimaryKeySelective(up);
                return;
            }
            ExcelUtil.write()
                    .buildWriter(outExcel, xlsTemplateFileName)
                    .buildSheet()
                    .fill(trendChart)
                    .finish();

            inExcel = new ByteArrayInputStream(outExcel.toByteArray());
            //压缩密码
            String password = WebUtil.randomPwd(8);
            encrypt = CrytoUtilSimple.encrypt(password);
            String obfuscatePassword = ZipUtil.obfuscatePassword(password, encrypt);

            //压缩文件
            inZip = ZipUtil.compress(inExcel, destFileName, obfuscatePassword, true);
            //minio上传
            StorageResult result = storageService.saveFile(inZip, StoragePathEnum.EXCEL.getPath(type), fileName, true,
                                                           true, null);

            up.setStatus(1);
            up.setDownloadPath(CrytoUtilSimple.encrypt(result.getRelativeNginxUrl()));
            up.setFileName(fileName);
            this.bizDownloadMapper.updateByPrimaryKeySelective(up);

            sysMFilePath.setFileName(fileName);
            sysMFilePath.setCompressPassword(encrypt);
            sysMFilePath.setFilePath(CrytoUtilSimple.encrypt(result.getRelativeNginxUrl()));
            sysMFilePathService.saveSysMFilePath(sysMFilePath, SysMFileTypeEnum.HPC_RESOURCE_ANALYSIS_CHAR,
                                                 up.getDownloadId());
            log.info("doAsynExportHpcResourceTrendChart bizDownloadMapper now:[{}]",
                     DateUtil.formatDateTime(new Date()));
        } catch (Exception e) {
            up.setStatus(4);
            up.setRemark("下载异常，异常原因：" + JSON.toJSONString(e));
            this.bizDownloadMapper.updateByPrimaryKeySelective(up);
            e.printStackTrace();
            log.error("ExportServiceImpl_doAsynExportHpcResourceTrendChart_error_{}_{}" + e.getMessage(),
                      JSON.toJSONString(e));
        } finally {
            IOUtils.closeQuietly(inZip);
            IOUtils.closeQuietly(outExcel);
            IOUtils.closeQuietly(inExcel);
        }
    }


    /**
     * 根据状态构建条件
     */
    private void conversionStatusName(String statusName, Criteria criteria) {
        if (ObjectUtils.isEmpty(statusName)) {
            return;
        }
        switch (statusName) {
            // 开通中 开通完成 开通失败
            case OrderStatus.PENDING:
            case OrderStatus.COMPLETED:
            case OrderStatus.LEV2_REFUSED:
                criteria.put(TYPE, OrderType.APPLY);
                break;
            // 已审核
            case OrderStatus.AUDITED:
                //已审核 包含状态 【已完成，开通中，拒绝并关闭】
                criteria.put("statusNameIn", Arrays.asList(OrderStatus.SUCCEED, OrderStatus.COMPLETED,
                                                           OrderStatus.LEV2_REFUSED));
                criteria.put(STATUS_NAME, null);
                break;
            // 退订中
            case OrderStatus.RELEASE_PENDING:
                criteria.put(TYPE, OrderType.RELEASE);
                criteria.put(STATUS_NAME, OrderStatus.PENDING);
                break;
            // 退订失败
            case OrderStatus.RELEASE_LEV2_REFUSED:
                criteria.put(TYPE, OrderType.RELEASE);
                criteria.put(STATUS_NAME, OrderStatus.LEV2_REFUSED);
                break;
            // 退订完成
            case OrderStatus.RELEASE_SUCCESS:
                criteria.put(TYPE, OrderType.RELEASE);
                criteria.getCondition().remove(STATUS_NAME);
                criteria.put("statusNameIn",
                             CollectionUtil.newArrayList(OrderStatus.RELEASE_SUCCESS, OrderStatus.COMPLETED));
                break;
            // 续订中
            case OrderStatus.RENEW_PENDING:
                criteria.put(TYPE, OrderType.RENEW);
                criteria.put(STATUS_NAME, OrderStatus.PENDING);
                break;
            // 续订失败
            case OrderStatus.RENEW_LEV2_REFUSED:
                criteria.put(TYPE, OrderType.RENEW);
                criteria.put(STATUS_NAME, OrderStatus.LEV2_REFUSED);
                break;
            // 续订完成
            case OrderStatus.RENEW_SUCCESS:
                criteria.put(TYPE, OrderType.RENEW);
                criteria.put(STATUS_NAME, OrderStatus.COMPLETED);
                break;
            // 变更完成
            case OrderStatus.MODIFY_COMPLETE:
                criteria.put(TYPE, OrderType.MODIFY);
                criteria.put(STATUS_NAME, OrderStatus.COMPLETED);
                break;
        }
    }


    /**
     * 获取状态名称
     */
    private String getStatusName(String status, String type) {
        switch (status) {
            case OrderStatus.COMPLETED:
                switch (type) {
                    case OrderType.RELEASE:
                        return "退订完成";
                    case OrderType.CHANGEGRADE:
                    case OrderType.MODIFY:
                        return "变更完成";
                    case OrderType.RENEW:
                        return "续订完成";
                    case OrderType.DEGRADE:
                        return "缩容完成";
                    case OrderType.UPGRADE:
                        return "扩容完成";
                    case OrderType.UPGRADE_RENEW:
                        return "扩容续订完成";
                }
                break;
            case OrderStatus.PENDING:
                switch (type) {
                    case OrderType.APPLY:
                        return "开通中";
                    case OrderType.RELEASE:
                        return "退订中";
                    case OrderType.RENEW:
                        return "续订中";
                    case OrderType.MODIFY:
                        return "变更中";
                    case OrderType.DEGRADE:
                        return "缩容中";
                    case OrderType.UPGRADE:
                        return "扩容中";
                }
            case OrderStatus.LEV2_REFUSED:
                switch (type) {
                    case OrderType.APPLY:
                        return "开通失败";
                    case OrderType.RELEASE:
                        return "退订失败";
                    case OrderType.RENEW:
                        return "续订失败";
                    case OrderType.MODIFY:
                        return "变更失败";
                    case OrderType.DEGRADE:
                        return "缩容失败";
                    case OrderType.UPGRADE:
                        return "扩容失败";
                }
        }
        return null;
    }


    @Override
    public void doAsynExportHpcResourceTrendList(String moduleType, Long taskId, AuthUser authUserInfo,
                                                 String perdiod) {
        BizDownload up = new BizDownload();
        up.setUpdatedDt(new Date());
        up.setDownloadId(taskId);
        up.setAccountId(authUserInfo.getUserSid());
        InputStream inZip = null;
        InputStream inExcel = null;
        ByteArrayOutputStream outExcel = null;
        SysMFilePath sysMFilePath = new SysMFilePath();
        String encrypt = "";
        boolean console = StringUtils.equalsIgnoreCase("console", moduleType);
        String type = console ? "UserDeal" : "ManagementDeal";
        try {
            String xlsTemplateFileName = "template/hpc_resource_analysis_list.xlsx";
            if (WebUtil.getHeaderAcceptLanguage()) {
                xlsTemplateFileName = "template/hpc_resource_analysis_list-us.xlsx";
            }
            String dateFormat = fileNameSuffix();
            String destFileName = "HPC资源分析列表" + dateFormat + XLSX;
            String zipFileName = "HPC资源分析列表" + dateFormat + ZIP;
            String fileName = zipFileName;
            outExcel = new ByteArrayOutputStream();
            FillingRateForMonthReq fillingRateForMonthReq = new FillingRateForMonthReq();
            if (StrUtil.isNotBlank(perdiod)) {
                String[] split = perdiod.split("~");
                if (split.length > 2) {
                    return;
                }
                if (perdiod.contains("~")) {
                    if (split.length == 1){
                        fillingRateForMonthReq.setEndTime(split[0]);
                    }
                    if (split.length == 2){
                        fillingRateForMonthReq.setStartTime(split[0]);
                        fillingRateForMonthReq.setEndTime(split[1]);
                    }
                }else {
                    fillingRateForMonthReq.setStartTime(split[0]);
                }
            }
            fillingRateForMonthReq.setType("export");
            fillingRateForMonthReq.setEntityId(authUserInfo.getEntityId());

            List<FillingRateForMonthResp> dataList =
                    (List<FillingRateForMonthResp>) resourceAnalysisServiceImpl.monthlyAverage(fillingRateForMonthReq).getDataList();
            if (CollectionUtils.isEmpty(dataList)) {
                up.setStatus(4);
                up.setRemark("月平均算力填充率为空，请核查数据!");
                this.bizDownloadMapper.updateByPrimaryKeySelective(up);
                return;
            }
            ExcelUtil.write()
                    .buildWriter(outExcel, xlsTemplateFileName)
                    .buildSheet()
                    .fill(dataList)
                    .finish();

            inExcel = new ByteArrayInputStream(outExcel.toByteArray());
            //压缩密码
            String password = WebUtil.randomPwd(8);
            encrypt = CrytoUtilSimple.encrypt(password);
            String obfuscatePassword = ZipUtil.obfuscatePassword(password, encrypt);

            //压缩文件
            inZip = ZipUtil.compress(inExcel, destFileName, obfuscatePassword, true);
            //minio上传
            StorageResult result = storageService.saveFile(inZip, StoragePathEnum.EXCEL.getPath(type), fileName, true,
                    true, null);

            up.setStatus(1);
            up.setDownloadPath(CrytoUtilSimple.encrypt(result.getRelativeNginxUrl()));
            up.setFileName(fileName);
            this.bizDownloadMapper.updateByPrimaryKeySelective(up);

            sysMFilePath.setFileName(fileName);
            sysMFilePath.setCompressPassword(encrypt);
            sysMFilePath.setFilePath(CrytoUtilSimple.encrypt(result.getRelativeNginxUrl()));
            sysMFilePathService.saveSysMFilePath(sysMFilePath, SysMFileTypeEnum.HPC_RESOURCE_ANALYSIS_LIST,
                    up.getDownloadId());
            log.info("doAsynExportHpcResourceTrendList [{}]",
                    DateUtil.formatDateTime(new Date()));
        } catch (Exception e) {
            up.setStatus(4);
            up.setRemark("下载异常，异常原因：" + e.getMessage());
            this.bizDownloadMapper.updateByPrimaryKeySelective(up);
            e.printStackTrace();
            log.error("ExportServiceImpl_doAsynExportHpcResourceTrendList_error_{}" + e.getMessage());
        } finally {
            IOUtils.closeQuietly(inZip);
            IOUtils.closeQuietly(outExcel);
            IOUtils.closeQuietly(inExcel);
        }
    }

    @Override
    public void doAsynExportActionLogsTrendList(String moduleType , ExportUserActionLogRequest exportUserActionLogRequest, Long taskId, AuthUser authUserInfo) {
        BizDownload up = new BizDownload();
        up.setUpdatedDt(new Date());
        up.setDownloadId(taskId);
        up.setAccountId(authUserInfo.getUserSid());

        InputStream inZip = null;
        InputStream inExcel = null;
        ByteArrayOutputStream outExcel = null;
        SysMFilePath sysMFilePath = new SysMFilePath();
        String encrypt = "";
        boolean console = StringUtils.equalsIgnoreCase("console", moduleType);
        String type = console ? "UserDeal" : "ManagementDeal";
        String acceptLanguage = authUserInfo.getAcceptLanguage();
        String taskName = OssExportTypeEnum.ACTION_LOGS.getNameIsUs(acceptLanguage);
        boolean isUs = WebUtil.getHeaderAcceptLanguage(acceptLanguage);
        try {
            String xlsTemplateFileName = "template/action-log-template.xlsx";
            if (isUs) {
                xlsTemplateFileName = "template/action-log-template-us.xlsx";
            }
            String dateFormat = fileNameSuffix();
            String destFileName = taskName + dateFormat + XLSX;
            String zipFileName = taskName + dateFormat + ZIP;
            String fileName = zipFileName;

            Criteria criteria = new Criteria();
            criteria.setConditionObject(exportUserActionLogRequest);
            // 如果不是系统管理员
            if (!authUserInfo.isAdmin()) {
                criteria.put("companyId", AuthUtil.getCurrentOrgSid());
            }
            criteria.put("noPage", true);
            criteria.put("success", null);
            criteria.put("action_method", null);
            List<ActionLogInfo> sysTActionLogList = actionLogService.selectMongoByParams(criteria);
            List<SysTActionLogVo> sysTActionLogVos = preparePageVo(sysTActionLogList, isUs);
            log.info("用户操作日志导出行数："+sysTActionLogVos.size());

            //写入sheet
            outExcel = new ByteArrayOutputStream();
            SpreadsheetVersion excel2007 = SpreadsheetVersion.EXCEL2007;
            if (Integer.MAX_VALUE != excel2007.getMaxTextLength()) {
                Field field;
                try {
                    field = excel2007.getClass().getDeclaredField("_maxTextLength");
                    ReflectionUtils.makeAccessible(field);
                    ReflectionUtils.setField(field,excel2007,Integer.MAX_VALUE);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            ExcelUtil.write()
                    .buildWriter(outExcel, xlsTemplateFileName)
                    .buildSheet()
                    .fill(sysTActionLogVos)
                    .finish();

            inExcel = new ByteArrayInputStream(outExcel.toByteArray());
            //压缩密码
            String password = WebUtil.randomPwd(8);
            encrypt = CrytoUtilSimple.encrypt(password);
            String obfuscatePassword = ZipUtil.obfuscatePassword(password, encrypt);

            //压缩文件
            inZip = ZipUtil.compress(inExcel, destFileName, obfuscatePassword, true);
            //minio上传
            StorageResult result = storageService.saveFile(inZip, StoragePathEnum.EXCEL.getPath(type), fileName, true,
                    true, null);

            up.setStatus(1);
            up.setDownloadPath(CrytoUtilSimple.encrypt(result.getRelativeNginxUrl()));
            up.setFileName(fileName);
            this.bizDownloadMapper.updateByPrimaryKeySelective(up);

            sysMFilePath.setFileName(fileName);
            sysMFilePath.setCompressPassword(encrypt);
            sysMFilePath.setFilePath(CrytoUtilSimple.encrypt(result.getRelativeNginxUrl()));
            sysMFilePathService.saveSysMFilePath(sysMFilePath, SysMFileTypeEnum.ACTION_LOGS,
                    up.getDownloadId());
            log.info("doAsynExportHpcResourceTrendList [{}]", DateUtil.formatDateTime(new Date()));
        } catch (Exception e) {
            up.setStatus(4);
            up.setRemark((isUs ? "Download exception, abnormal cause:" : "下载异常，异常原因：") + e.getMessage());
            this.bizDownloadMapper.updateByPrimaryKeySelective(up);
            e.printStackTrace();
            log.error("ExportServiceImpl_doAsynExportActionLogsTrendList_error_{}" + e.getMessage());
        } finally {
            IOUtils.closeQuietly(inZip);
            IOUtils.closeQuietly(inExcel);
            IOUtils.closeQuietly(outExcel);
        }
    }

    @Override
    public void doAsynExportUsers(String moduleType, DescribeUserRequest request, Long taskId, AuthUser authUserInfo) {
        boolean isUs = WebUtil.getHeaderAcceptLanguage(authUserInfo.getAcceptLanguage());

        Criteria criteria = packageQueryUserInfo(request);
        criteria.setOrderByClause("A.created_dt desc ");
        String fileName = (isUs ? "user_list_" : "用户列表_") + createFileNameSuffix();
        Class<?> entity = isUs ? UserExcelUs.class : UserExcel.class;
        ExcelUtil.pageWrite(new ExcelPageExportService<UserExcel, UserExcel>() {
            @Override
            public ExcelExportInfo info() {
                return ExcelExportInfo.builder()
                        .tClass(entity)
                        .pageSize(2000)
                        .build();
            }

            @Override
            public List<UserExcel> data(int pageNum, int pageSize) {
                PageHelper.startPage(pageNum, pageSize);
                return userMapper.exportUsers(criteria);
            }

            @Override
            public List<UserExcel> processData(List<UserExcel> data) {
                if (isUs && !CollectionUtils.isEmpty(data)) {
                    for (UserExcel userExcel : data) {
                        userExcel.setEntityName(StringUtils.replaceEach(userExcel.getEntityName(), new String[]{"默认运营实体"}, new String[]{"Default operating entity"}));
                        userExcel.setIamSubUser(StringUtils.replaceEach(userExcel.getIamSubUser(), new String[]{"是", "否"}, new String[]{"YES", "NO"}));
                        userExcel.setStatus(StringUtils.replaceEach(userExcel.getStatus(), new String[]{"启用", "待审核", "禁用"}, new String[]{"Enabled", "To be reviewed", "Disabled"}));
                        userExcel.setIndustryName(StringUtils.replaceEach(userExcel.getIndustryName(), new String[]{"安全","互联网","制造","交通","能源","运营商","金融","数字政府","军工","教育","医疗","科研机构","其他"}, new String[]{"Safety","Internet","Manufacturing","Traffic","Energy","Carrier","Finance","Digital government","Military industry","Education","Medical treatment","Scientific research institution","Others"}));
                        userExcel.setRole(StringUtils.replaceEach(userExcel.getRole(), new String[]{"系统管理员(内置)","运营管理员(内置)","客户管理员(内置)","普通运营(内置)","在线客服(内置)","分销商管理员(内置)"}, new String[]{"System administrator (built-in)","Operations administrator (built-in)","Customer administrator (built-in)","Normal operation (built-in)","Online customer service (built-in)","Distributor administrator (built-in)"}));
                    }
                }
                return data;
            }

            @Override
            public void processStream(InputStream in) {
                //minio上传
                String zipFileName = fileName.replace(XLSX, ZIP);
                //压缩密码是参照原来代码写的
                String password = UUID.randomUUID().toString().substring(0, 8);
                String encrypt = CrytoUtilSimple.encrypt(password);
                String obfuscatePassword = ZipUtil.obfuscatePassword(password, encrypt);
                //压缩文件
                try (InputStream inZip = ZipUtil.compress(in, fileName, obfuscatePassword, true)){
                    StorageResult storageResult = uploadFile(inZip, moduleType, zipFileName);
                    updateDownloadSuccess(taskId, zipFileName, storageResult.getRelativeNginxUrl());
                    SysMFilePath sysMFilePath = SysMFilePath.builder()
                            .fileName(fileName)
                            .compressPassword(encrypt)
                            .filePath(CrytoUtilSimple.encrypt(storageResult.getRelativeNginxUrl()))
                            .build();
                    sysMFilePathService.saveSysMFilePath(sysMFilePath, SysMFileTypeEnum.SYS_USERS, taskId);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                } finally {
                    IOUtils.closeQuietly(in);
                }
            }
        });
    }

    /**
     * 导出账户信息
     * @param moduleType
     * @param request
     * @param taskId
     * @param authUserInfo
     */
    @Override
    @Async("exportExecutor")
    public void doAsynExportUserAccount(String moduleType, DescribeUserRequest request, Long taskId, AuthUser authUserInfo) {
        //安全随机数
        String format = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.dateFormat(new Date(), "yyyyMMddHHmmss")
            + Arrays.stream(NumberUtil.generateRandomNumber(0, 9, 5)).mapToObj(String::valueOf).collect(Collectors.joining());
        String acceptLanguage = authUserInfo.getAcceptLanguage();
        String taskName = OssExportTypeEnum.ACCOUNT_INFO_LIST.getNameIsUs(acceptLanguage);
        boolean isUs = WebUtil.getHeaderAcceptLanguage(acceptLanguage);
        String fileName = taskName + format + ".xlsx";

        ExcelUtil.pageWrite(new ExcelPageExportService<User, User>() {
            @Override
            public ExcelExportInfo info() {
                String template = "template/user-deal.xlsx";
                if (isUs) {
                    template = "template/user-deal-us.xlsx";
                }
                return ExcelExportInfo.builder().
                    template(template)
                    .pageSize(5000)
                    .build();
            }

            @Override
            public List<User> data(int pageNum, int pageSize) {
                Criteria criteria = packageQueryUserInfo(request);
                criteria.setConditionObject(request);
                if (StringUtils.isNotBlank(request.getKeyword()) && !CCSPCacheUtil.ccspServiceOpen()) {
                    criteria.put("keywordHash", DigestUtils.sha256Hex(request.getKeyword()));
                }
                List<Long>  adminSids = new ArrayList<>();
                request.setEntityId(RequestContextUtil.getEntityId());
                List<Role> currentRoleList =  roleService.selectRoleByUserSid(RequestContextUtil.getAuthUserInfo().getUserSid());
                List<cn.com.cloudstar.rightcloud.common.pojo.Role> convertsRolesList = BeanConvertUtil.convert(currentRoleList, cn.com.cloudstar.rightcloud.common.pojo.Role.class);
                String maxScope = DataScopeUtil.getMaxDataScope(convertsRolesList);
                if(DataScopeEnum.DATA_SCOPE_COMPANY.getScope().equals(maxScope)){
                    Criteria bizBillingAccountCriteria = new Criteria();
                    bizBillingAccountCriteria.put("entityId",request.getEntityId());
                    bizBillingAccountCriteria.put("salesmenId",RequestContextUtil.getAuthUserInfo().getUserSid());
                    List<BizBillingAccount> accounts = bizBillingAccountMapper.selectByParams(bizBillingAccountCriteria);
                    if(CollectionUtil.isNotEmpty(accounts)){
                        for(BizBillingAccount account : accounts){
                            adminSids.add(account.getAdminSid());
                        }
                    }
                }
                if(CollectionUtil.isNotEmpty(adminSids)){
                    criteria.put("userSidIn",adminSids);
                }
                //关键词搜索
                String keyword = request.getKeyword();
                createKeyword(keyword, criteria);
                criteria.setPageNum(pageNum);
                criteria.setPageSize(pageSize);
                List<User> users = userService.findCertificationStatusUsers(criteria);
                List<Company> companyList = companyService.findOrgWithParentOrgName(new Criteria("orgType", OrgType.COMPANY));

                for (User user : users) {
                    //查询用户的角色列表,并拼接成字符串
                    List<Role> roleList;
                    if (Objects.nonNull(request.getOrgSid())) {
                        roleList = roleService.findRolesByUserSidAndOrgSid(user.getUserSid(), request.getOrgSid());
                    } else {
                        roleList = roleService.findRolesByUserSidForOrgSid(user.getUserSid());
                    }
                    String roleStr = "";
                    Set<Long> roleIdSet = new HashSet<>();
                    if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(roleList)) {
                        //利用set去重
                        Set<String> roleNameSet = new HashSet<>();
                        for (Role aRoleList : roleList) {
                            roleNameSet.add(aRoleList.getRoleName());
                            roleIdSet.add(aRoleList.getRoleSid());
                        }
                        roleStr = StringUtils.join(roleNameSet.toArray(), ",");

                        // 判定用户是否有指定组织的角色
                        if (request.getJoinFlag() != null && request.getJoinFlag() && Objects.nonNull(request.getOrgSid())) {
                            user.setJoinFlag(Boolean.TRUE);
                        }
                    }
                    if (isUs) {
                        roleStr = StringUtils.replaceEach(roleStr, new String[]{"系统管理员(内置)","运营管理员(内置)","客户管理员(内置)","普通运营(内置)","在线客服(内置)","分销商管理员(内置)"}, new String[]{"System administrator (built-in)","Operations administrator (built-in)","Customer administrator (built-in)","Normal operation (built-in)","Online customer service (built-in)","Distributor administrator (built-in)"});
                    }
                    user.setRoleName(roleStr);
                    if (CollectionUtil.isNotEmpty(roleIdSet)) {
                        user.setRoleIds(Lists.newArrayList(roleIdSet));
                    }
                    if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(companyList)) {
                        user.setInnerFlag(false);
                        for (Company company : companyList) {
                            if (Objects.equals(company.getCompanyId(), user.getCompanyId())) {
                                user.setInnerFlag(true);
                                user.setType(CertificationTypeEnum.USER.getCode());
                                //判断是个人认证还是企业认证
                                if (!ObjectUtils.isEmpty(company)
                                    && !CertificationStatus.NOAUTH.equals(company.getCertificationStatus())
                                    && !CertificationStatus.AUTHFILED.equals(company.getCertificationStatus())) {
                                    user.setType(CertificationTypeEnum.COMPANY.getCode());
                                    user.setCertificationStatus(company.getCertificationStatus());
                                    //如果企业认证成功,就显示为企业认证。
                                    user.setStatus(user.getStatus());
                                    user.setCompanyName(company.getCompanyName());
                                }
                                break;
                            }
                        }
                    }
                    //默认设置直营
                    if (StringUtil.isNullOrEmpty(user.getDistributorName())) {
                        user.setDistributorName(isUs ? "be run directly by a manufacturer" : "直营");
                    }
                    user.setStatusName(UserStatusEnum.status2DescByI18n(user.getStatus(), isUs));
                    user.setCertificationStatusName(CertificationStatusEnum.status2DescByI18n(user.getCertificationStatus(), isUs));
                    user.setType(CertificationTypeEnum.code2DescByI18n(user.getType(), isUs));
                }

                Collections.reverse(users);
                return users;
            }

            @Override
            public List<User> processData(List<User> data) {
                return data;
            }

            @Override
            public void processStream(InputStream in) {
                //minio上传
                String zipFileName = fileName.replace(XLSX, ZIP);
                //压缩密码是参照原来代码写的
                String password = UUID.randomUUID().toString().substring(0, 8);
                String encrypt = CrytoUtilSimple.encrypt(password);
                String obfuscatePassword = ZipUtil.obfuscatePassword(password, encrypt);
                //压缩文件
                try (InputStream inZip = ZipUtil.compress(in, fileName, obfuscatePassword, true)){
                    StorageResult storageResult = uploadFile(inZip, moduleType, zipFileName);
                    updateDownloadSuccess(taskId, zipFileName, storageResult.getRelativeNginxUrl());
                    SysMFilePath sysMFilePath = SysMFilePath.builder()
                        .fileName(fileName)
                        .compressPassword(encrypt)
                        .filePath(CrytoUtilSimple.encrypt(storageResult.getRelativeNginxUrl()))
                        .build();
                    sysMFilePathService.saveSysMFilePath(sysMFilePath, SysMFileTypeEnum.USER_ACCOUNT, taskId);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                } finally {
                    IOUtils.closeQuietly(in);
                }
            }
        });
    }

    @Override
    @Async("exportExecutor")
    public void doAsyncExportEcs(String moduleType, ResViewQueryRequest exportUserRequest, Long taskId, AuthUser authUserInfo) {
        //安全随机数
        String format = cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.dateFormat(new Date(), "yyyyMMddHHmmss")
                + Arrays.stream(NumberUtil.generateRandomNumber(0, 9, 5)).mapToObj(String::valueOf).collect(Collectors.joining());
        String acceptLanguage = authUserInfo.getAcceptLanguage();
        String taskName = OssExportTypeEnum.codeFromName(exportUserRequest.getProductCode());
        boolean isUs = WebUtil.getHeaderAcceptLanguage(acceptLanguage);
        String fileName = taskName + format + ".xlsx";
        ExcelUtil.pageWrite(new ExcelPageExportService<ResScreenTopResult, ResScreenTopResult>() {
            @Override
            public ExcelExportInfo info() {
                String template = "template/ecs-export.xlsx";
//                if (isUs) {
//                    template = "template/user-deal-us.xlsx";
//                }
                return ExcelExportInfo.builder().
                                      template(template)
                                      .pageSize(5000)
                                      .build();
            }

            @Override
            public List<ResScreenTopResult> data(int pageNum, int pageSize) {
                RightCloudResult<List<ResScreenTopResult>> resultVm = dcExportFeignService.exportVm(exportUserRequest);
                if (!resultVm.isSuccess()) {
                    throw new RuntimeException("查询数据失败");
                }
                return resultVm.getData();
            }

            @Override
            public List<ResScreenTopResult> processData(List<ResScreenTopResult> data) {
                return data;
            }

            @Override
            public void processStream(InputStream in) {
                //minio上传
                String zipFileName = fileName.replace(XLSX, ZIP);
                //压缩密码是参照原来代码写的
                String password = UUID.randomUUID().toString().substring(0, 8);
                String encrypt = CrytoUtilSimple.encrypt(password);
                String obfuscatePassword = ZipUtil.obfuscatePassword(password, encrypt);
                //压缩文件
                try (InputStream inZip = ZipUtil.compress(in, fileName, obfuscatePassword, true)){
                    StorageResult storageResult = uploadFile(inZip, moduleType, zipFileName);
                    updateDownloadSuccess(taskId, zipFileName, storageResult.getRelativeNginxUrl());
                    SysMFilePath sysMFilePath = SysMFilePath.builder()
                                                            .fileName(fileName)
                                                            .compressPassword(encrypt)
                                                            .filePath(CrytoUtilSimple.encrypt(storageResult.getRelativeNginxUrl()))
                                                            .build();
                    sysMFilePathService.saveSysMFilePath(sysMFilePath, SysMFileTypeEnum.searchExportCode(exportUserRequest.getProductCode()), taskId);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                } finally {
                    IOUtils.closeQuietly(in);
                }
            }
        });
    }

    @Override
    public void doAsynExportTraceLogsTrendList(TraceLogRequest traceLogRequest, Long taskId, AuthUser authUserInfo) {
        BizDownload up = new BizDownload();
        up.setUpdatedDt(new Date());
        up.setDownloadId(taskId);
        InputStream inZip = null;
        ByteArrayOutputStream outExcel = null;
        SysMFilePath sysMFilePath = new SysMFilePath();
        String encrypt = "";
        String type = "ManagementDeal";
        try {
            SimpleDateFormat fileFormat = new SimpleDateFormat("yyyy-MM-dd");
            String xlsTemplateFileName = "template/trace-log-template.xlsx";
            fileFormat = new SimpleDateFormat("yyyyMMddHHmmss");
            String dateFormat = fileFormat.format(new Date());
            String destFileName = "资源日志" + dateFormat + XLSX;
            String zipFileName = "资源日志" + dateFormat + ZIP;
            String fileName = zipFileName;

            Criteria criteria = new Criteria();
            criteria.setConditionObject(traceLogRequest);
            // 如果不是系统管理员
            if (!authUserInfo.isAdmin()) {
                criteria.put("orgSid", AuthUtil.getCurrentOrgSid());
            }
            criteria.put("noPage", true);
            List<ResTraceLog> resTraceLogs;
            if (traceLogRequest.getIsMongodbEnable()) {
                resTraceLogs = actionLogService.selectTraceLog(criteria);
            }else {
                ResTraceLogForm resTraceLogForm = cn.com.cloudstar.rightcloud.common.util.BeanConvertUtil.convert(traceLogRequest, ResTraceLogForm.class);
                if (!authUserInfo.isAdmin()) {
                    resTraceLogForm.setOrgId(AuthUtil.getCurrentOrgSid());
                }
                resTraceLogForm.setPagenum(null);
                resTraceLogForm.setPagesize(null);
                PageResult<ResTraceLog> pageResult = systemFeignService.selectTraceLog(resTraceLogForm).getData();
                resTraceLogs = pageResult.getList();
            }
            resTraceLogs.forEach(resTraceLog -> {
                resTraceLog.setOperateTimeString(StringUtil.dateFormat(resTraceLog.getOperateTime(), StringUtil.DF_YMD_24));
                resTraceLog.setCreateDtString(StringUtil.dateFormat(resTraceLog.getCreateDt(), StringUtil.DF_YMD_24));
                resTraceLog.setResultString(resTraceLog.getResult() ? "成功" : "失败");
            });
            log.info("资源日志导出行数："+resTraceLogs.size());

            //写入sheet
            outExcel = new ByteArrayOutputStream();
            SpreadsheetVersion excel2007 = SpreadsheetVersion.EXCEL2007;
            if (Integer.MAX_VALUE != excel2007.getMaxTextLength()) {
                Field field;
                try {
                    field = excel2007.getClass().getDeclaredField("_maxTextLength");
                    ReflectionUtils.makeAccessible(field);
                    ReflectionUtils.setField(field,excel2007,Integer.MAX_VALUE);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            EasyExcel.write(outExcel)
                     .excelType(ExcelTypeEnum.XLSX)
                     .autoCloseStream(Boolean.FALSE)
                     .needHead(Boolean.FALSE)
                     .withTemplate(ResourceUtil.getStream(xlsTemplateFileName))
                     .sheet()
                     .doFill(resTraceLogs);

            InputStream inExcel = new ByteArrayInputStream(outExcel.toByteArray());
            //压缩密码
            String password = cn.com.cloudstar.rightcloud.oss.common.util.WebUtil.randomPwd(8);
            encrypt = CrytoUtilSimple.encrypt(password);
            String obfuscatePassword = ZipUtil.obfuscatePassword(password, encrypt);

            //压缩文件
            inZip = ZipUtil.compress(inExcel, destFileName, null, false);
            //minio上传
            StorageResult result = storageService.saveFile(inZip, StoragePathEnum.EXCEL.getPath(type), fileName, true,
                                                           true, null);

            up.setStatus(1);
            up.setDownloadPath(result.getRelativeNginxUrl());
            up.setFileName(fileName);
            this.bizDownloadMapper.updateByPrimaryKeySelective(up);

            sysMFilePath.setFileName(fileName);
            sysMFilePath.setCompressPassword(encrypt);
            sysMFilePath.setFilePath(CrytoUtilSimple.encrypt(result.getRelativeNginxUrl()));
            sysMFilePathService.saveSysMFilePath(sysMFilePath, SysMFileTypeEnum.TRACEE_LOGS,
                                                 up.getDownloadId());
            log.info("doAsynExportTraceLogsTrendList [{}]",
                     DateUtil.formatDateTime(new Date()));
        } catch (Exception e) {
            up.setStatus(4);
            up.setRemark("下载异常，异常原因：" + JSON.toJSONString(e));
            this.bizDownloadMapper.updateByPrimaryKeySelective(up);
            e.printStackTrace();
            log.error("ExportServiceImpl_doAsynExportTraceLogsTrendList_error_{}_{}" + e.getMessage(),
                      JSON.toJSONString(e));
        } finally {
            IOUtils.closeQuietly(inZip);
        }
    }









    private void createKeyword(String keyword, Criteria criteria){
        List<String> statusList = new ArrayList<>();
        List<String> certificationStatusList = new ArrayList<>();
        if(StrUtil.isNotBlank(keyword)){
            criteria.put("keyword", keyword);
            if(StatusTypeEnum.ENABLE.getName().contains(keyword)){
                statusList.add(StatusTypeEnum.ENABLE.getCode());
            }
            if(StatusTypeEnum.DISABLE.getName().contains(keyword)){
                statusList.add(StatusTypeEnum.DISABLE.getCode());
            }
            if(StatusTypeEnum.REVIEW.getName().contains(keyword)){
                statusList.add(StatusTypeEnum.REVIEW.getCode());
            }
            if(StatusTypeEnum.LOCK.getName().contains(keyword)){
                statusList.add(StatusTypeEnum.LOCK.getCode());
            }
            if(StatusTypeEnum.REJECTED.getName().contains(keyword)){
                statusList.add(StatusTypeEnum.REJECTED.getCode());
            }
            if(CollectionUtil.isNotEmpty(statusList)){
                criteria.put("statusList",statusList);
            }
            if(StatusTypeEnum.NOAUTH.getName().contains(keyword)){
                certificationStatusList.add(StatusTypeEnum.NOAUTH.getCode());
            }
            if(StatusTypeEnum.AUTHING.getName().contains(keyword)){
                certificationStatusList.add(StatusTypeEnum.AUTHING.getCode());
            }
            if(StatusTypeEnum.AUTHSUCCEED.getName().contains(keyword)){
                certificationStatusList.add(StatusTypeEnum.AUTHSUCCEED.getCode());
            }
            if(StatusTypeEnum.AUTHFILED.getName().contains(keyword)){
                certificationStatusList.add(StatusTypeEnum.AUTHFILED.getCode());
            }
            if(CollectionUtil.isNotEmpty(certificationStatusList)){
                criteria.put("certificationStatusList",certificationStatusList);
            }
        }
    }



    /**
     * 设置查询用户参数
     *
     * @param request the query request
     */
    private Criteria packageQueryUserInfo(DescribeUserRequest request) {
        Criteria criteria = new Criteria();
        // 如果需要用户是否在当前组织的标识， orgSid用来判断是否属于当前组织，不做查询筛选条件
        if (request.getJoinFlag() == null || !request.getJoinFlag()) {
            criteria.put("orgSid", request.getOrgSid());
        }
        if (!Strings.isNullOrEmpty(request.getSearchFilter())) {
            criteria.put("searchFilter", request.getSearchFilter());
        }
        if (!Strings.isNullOrEmpty(request.getStartDate())) {
            criteria.put("startDate", request.getStartDate());
            criteria.put("endDate", request.getEndDate());
        }
        if (StringUtil.isNotBlank(request.getStatus())) {
            criteria.put("status", Integer.parseInt(request.getStatus()));
        }
        if (!Strings.isNullOrEmpty(request.getAccount())) {
            criteria.put("account", request.getAccount());
        }
        if (!Strings.isNullOrEmpty(request.getRealName())) {
            criteria.put("realName", CCSPCacheUtil.verifyAndCCSPEncrypt(request.getRealName()));
        }
        if (!Strings.isNullOrEmpty(request.getEmail())) {
            criteria.put("email", CCSPCacheUtil.verifyAndCCSPEncrypt(request.getEmail()));
        }
        if (!Strings.isNullOrEmpty(request.getMobile())) {
            criteria.put("mobile", CCSPCacheUtil.verifyAndCCSPEncrypt(request.getMobile()));
        }
        if (!Strings.isNullOrEmpty(request.getOrgName())) {
            criteria.put("orgName", request.getOrgName());
        }
        if (!Strings.isNullOrEmpty(request.getCertificationStatus())) {
            criteria.put("certificationStatus", request.getCertificationStatus());
        }
        if (!Strings.isNullOrEmpty(request.getAccountNameLike())) {
            criteria.put("accountNameLike", request.getAccountNameLike());
        }
        criteria.put("hiddenChild", request.getHiddenChild());
        return criteria;
    }

    /**
     * 页面参数转化
     *
     * @param sysTActionLogList 系统操作日志列表
     * @return {@link List}<{@link SysTActionLogVo}>
     */
    private List<SysTActionLogVo> preparePageVo(List<ActionLogInfo> sysTActionLogList, boolean en) {

        List<SysTActionLogVo> sysTActionLogVos = new ArrayList<>();
        if (StringUtil.isNullOrEmpty(sysTActionLogList) || sysTActionLogList.isEmpty()) {
            return sysTActionLogVos;
        }
        for (ActionLogInfo obj : sysTActionLogList) {
            if (en) {
                obj.setTagName(StrUtil.isNotBlank(obj.getTagNameUs()) ? obj.getTagNameUs() : obj.getTagName());
                obj.setResource(StrUtil.isNotBlank(obj.getResourceUs()) ? obj.getResourceUs() : obj.getResource());
                obj.setRoleName(StrUtil.isNotBlank(obj.getRoleNameUs()) ? obj.getRoleNameUs() : obj.getRoleName());
                obj.setSuccess(StringUtils.replaceEach(obj.getSuccess(), new String[]{"成功", "失败"}, new String[]{"Success", "Failure"}));
            }

            SysTActionLogVo sysTActionLogVo = new SysTActionLogVo();
            sysTActionLogVo.setRoleName(obj.getRoleName());
            sysTActionLogVo.setCompanyName(obj.getCompanyName());
            sysTActionLogVo.setResource(obj.getResource());
            sysTActionLogVo.setRemoteIp(obj.getRemoteIp());
            sysTActionLogVo.setClient(obj.getClient());
            sysTActionLogVo.setAccount(obj.getAccount());
            sysTActionLogVo.setTagName(obj.getTagName());
            sysTActionLogVo.setActionTime(StringUtil.dateFormat(obj.getActionTime(), StringUtil.DF_YMD_24));
            sysTActionLogVo.setActionMethod(obj.getActionMethod());
            sysTActionLogVo.setResultSign(obj.isResultSign());
            sysTActionLogVo.setRequest(obj.getRequest());
            sysTActionLogVo.setResponse(obj.getResponse());
            sysTActionLogVo.setSuccess(obj.getSuccess());
            sysTActionLogVo.setBizId(String.valueOf(ObjectUtil.isNotNull(obj.getBizId())?obj.getBizId():""));
            sysTActionLogVo.setOperationDetails(obj);

            sysTActionLogVos.add(sysTActionLogVo);
        }
        return sysTActionLogVos;
    }
}

