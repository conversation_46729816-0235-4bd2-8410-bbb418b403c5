package cn.com.cloudstar.rightcloud.oss.module.selfservice.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;

import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.common.constants.NotificationConsts;
import cn.com.cloudstar.rightcloud.common.constants.NotificationConsts.PlatformMsg.ProductMsg;
import cn.com.cloudstar.rightcloud.common.mq.request.BaseNotificationMqBean;
import cn.com.cloudstar.rightcloud.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.core.pojo.dto.audit.ServiceOrder;
import cn.com.cloudstar.rightcloud.core.pojo.dto.audit.ServiceOrderDetail;
import cn.com.cloudstar.rightcloud.core.pojo.dto.audit.ServiceOrderPriceDetail;
import cn.com.cloudstar.rightcloud.core.pojo.dto.audit.ServiceOrderResourceRef;
import cn.com.cloudstar.rightcloud.core.pojo.dto.charge.BizBillingAccount;
import cn.com.cloudstar.rightcloud.core.pojo.dto.sfs.ServiceCategory;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.Org;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.OrderType;
import cn.com.cloudstar.rightcloud.oss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.oss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.oss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.oss.common.util.DateUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.model.User;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.org.OrgMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.user.UserMapper;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.ProductInfoVO;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.ApplyServiceRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.response.InquiryPriceResponse;
import cn.com.cloudstar.rightcloud.oss.module.feign.service.FeignService;
import cn.com.cloudstar.rightcloud.oss.module.operate.dao.EntityMapper;
import cn.com.cloudstar.rightcloud.oss.module.order.dao.ServiceOrderDetailMapper;
import cn.com.cloudstar.rightcloud.oss.module.order.dao.ServiceOrderResourceRefMapper;
import cn.com.cloudstar.rightcloud.oss.module.order.service.ServiceOrderService;
import cn.com.cloudstar.rightcloud.oss.module.order.service.SidService;
import cn.com.cloudstar.rightcloud.oss.module.pricing.service.priceconfig.BizBillingAccountService;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.bean.model.BillingDetail;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.bean.model.SfProductResource;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.bean.model.SfProductResourceVO;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.bean.request.PriceRequest;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.bean.request.SendMessageRequest;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.bean.request.ServiceOrderForm;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.bean.request.SfProductResourceForm;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.bean.request.UpdateOrderForm;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.bean.response.PriceResponse;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.dao.ServiceCategoryMapper;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.dao.SfProductResourceMapper;

import static cn.com.cloudstar.rightcloud.common.constants.NotificationConsts.SEND_NOTIFICATION_EXCHANGE;
import static cn.com.cloudstar.rightcloud.common.constants.type.LdapPropertyKey.CONNECT_MODE.NORMAL;
import static cn.com.cloudstar.rightcloud.common.enums.CloudProductEnum.AliyunProductEnum.BMS;
import static cn.com.cloudstar.rightcloud.common.enums.CloudProductEnum.AliyunProductEnum.ECS;
import static cn.com.cloudstar.rightcloud.common.enums.CloudProductEnum.AliyunProductEnum.EIP;
import static cn.com.cloudstar.rightcloud.common.enums.CloudProductEnum.AliyunProductEnum.ELB;
import static cn.com.cloudstar.rightcloud.common.enums.CloudProductEnum.AliyunProductEnum.EVS;
import static cn.com.cloudstar.rightcloud.common.enums.CloudProductEnum.AliyunProductEnum.OBS;

/**
 * Description:
 *
 * <AUTHOR>
 * @Date 2024/3/12 11:10
 * @Version 1.0
 */
@RestController
@RequestMapping()
@Api(tags = "cmp调用oss")
@Slf4j
public class OssController {

    @Autowired
    private ServiceOrderService serviceOrderService;

    @Autowired
    private ServiceOrderDetailMapper serviceOrderDetailMapper;

    @Autowired
    private SidService sidService;

    @Autowired
    private ServiceCategoryMapper serviceCategoryMapper;

    @Autowired
    private BizBillingAccountService bizBillingAccountService;

    @Autowired
    private FeignService feignService;

    @Autowired
    private ServiceOrderResourceRefMapper serviceOrderResourceRefMapper;

    @Autowired
    private EntityMapper entityMapper;

    @Autowired
    private OrgMapper orgMapper;
    
    @Autowired
    private UserMapper userMapper;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private SfProductResourceMapper sfProductResourceMapper;

    private final static String EVS_PRICE = "ebsTypeModel";
    private final static String VM_TYPE_ID = "vmTypeId";
    private final static String BMS_TYPE_ID = "bmsTypeId";
    private final static String ELB_TYPE_ID = "loadBalancerName";
    private final static String EIP_TYPE_ID = "bandwidth";

    private final static String SYSTEM_DISK = "systemDisk";
    private final static String STORAGE_CLASS = "storageClass";

    private static final String SALE_TYPE = "02";
    private static final String NORMAL_TYPE = "01";

    @Data
    public static class ConfigData {
        private String label;
        private String attrKey;
        private String value;
    }

    enum StorageClassAndAclEnum {
        STANDARD("STANDARD","标准存储"),
        WARM("WARM","低频访问存储"),
        COLD("COLD","归档存储"),
        PRIVATE("PRIVATE","私有"),
        PUBLIC_READ("PUBLIC-READ","公共读"),
        PUBLIC_READ_WRITE("PUBLIC-READ-WRITE","公共读写");

        private String key;
        private String value;

        StorageClassAndAclEnum(String key,String value){
            this.key=key;
            this.value=value;
        }

        public String getKey(){
            return key;
        };

        public String getValue(){
            return value;
        };

        public static String getValue(String key){
            for (StorageClassAndAclEnum storageClassAndAclEnum : StorageClassAndAclEnum.values()) {
                if (storageClassAndAclEnum.key.equalsIgnoreCase(key)){
                    return storageClassAndAclEnum.value;
                }
            }
            return "";
        }
    }

    @PostMapping("/insert/order")
    public RestResult<Long> insertOrder(@RequestBody ServiceOrderForm form){
        try {
            List<String> needFields = new ArrayList<>();
            List<ConfigData> configDatas = new ArrayList<>();
            String productConfigDesc = "";
            // 订单数据插入
            ServiceOrder serviceOrder = BeanConvertUtil.convert(form, ServiceOrder.class);
            ServiceCategory serviceCategory = serviceCategoryMapper.selectByServiceType(form.getServiceCode());
            BizBillingAccount bizBillingAccount = bizBillingAccountService.getBizBillingAccount(form.getOrgSid());
            serviceOrder.setOrderSn(sidService.getMaxSid("ORDER_ID"));
            serviceOrder.setServiceId(serviceCategory.getId().toString());
            serviceOrder.setBizBillingAccountId(bizBillingAccount.getId());
            serviceOrder.setStatus("completed");
            serviceOrder.setEntityId(bizBillingAccount.getEntityId());
            serviceOrder.setEntityName(bizBillingAccount.getEntityName());
            serviceOrder.setPayTime(new Date());
            serviceOrder.setOrderSourceSn(serviceOrder.getOrderSn());
            serviceOrder.setProductName(serviceCategory.getServiceName());
            serviceOrder.setAccountName(bizBillingAccount.getAccountName());
            serviceOrder.setClusterUuid(form.getUuid());
            WebUtil.prepareInsertParams(serviceOrder, RequestContextUtil.getCurrentUserName());
            serviceOrderService.insertSelective(serviceOrder);
            // 订单明细数据插入
            if (serviceCategory.getServiceType().equals(ECS.getDesc())) {
                needFields = Arrays.asList("vmType", "image", "systemDisk");
                productConfigDesc = makeProductConfigDesc(form, needFields, configDatas);
            } else if (serviceCategory.getServiceType().equals(BMS.getDesc())) {
                needFields = Arrays.asList("vmType", "image");
                productConfigDesc = makeProductConfigDesc(form, needFields, configDatas);
            } else if (serviceCategory.getServiceType().equals(EVS.getDesc())) {
                needFields = Arrays.asList("disk");
                productConfigDesc = makeProductConfigDesc(form, needFields, configDatas);
            } else if (serviceCategory.getServiceType().equals(EIP.getDesc())){
                needFields = Arrays.asList("bandwidth");
                productConfigDesc = makeProductConfigDesc(form, needFields, configDatas);
            } else if (serviceCategory.getServiceType().equals(OBS.getDesc())) {
                needFields = Arrays.asList("storageClass", "acl");
                productConfigDesc = makeProductConfigDesc(form, needFields, configDatas);
            }
            insertOrderAndResource(serviceOrder, form, productConfigDesc);
            // 插入ECS/BMS订单明细时，需要插入EVS订单明细数据
            if (serviceCategory.getServiceType().equals(ECS.getDesc())) {
                // EVS
                needFields = Arrays.asList("disk");
                productConfigDesc = makeProductConfigDesc(form, needFields, configDatas);
                form.setServiceCode(EVS.getDesc());
                insertOrderAndResource(serviceOrder, form, productConfigDesc);
            }
            return new RestResult<>(serviceOrder.getId());
        } catch (Exception e) {
            if (e instanceof BizException) {
                throw e;
            } else {
                throw new BizException("同步创建订单失败");
            }
        }
    }

    @PostMapping("/insert/{resourceName}")
    public RestResult<Long> insertOrder(@PathVariable("resourceName") String resourceName) {
        try {
            List<ServiceOrder> serviceOrders = serviceOrderService.selectByParams(new Criteria().put("name", resourceName).put("version",1));
            if (CollectionUtils.isEmpty(serviceOrders)) {
                throw new BizException("订单不存在");
            }
            ServiceOrder serviceOrder = serviceOrders.stream().filter(s -> !OrderType.RELEASE.equals(s.getType())).findFirst().get();
            serviceOrder.setVersion(2L);
            serviceOrderService.updateByPrimaryKey(serviceOrder);
            Long serviceOrderId = serviceOrder.getId();
            serviceOrder.setOrderSn(sidService.getMaxSid("ORDER_ID"));
            serviceOrder.setOrderSourceSn(serviceOrder.getOrderSn());
            serviceOrder.setCreatedDt(new Date());
            serviceOrder.setUpdatedDt(new Date());
            serviceOrder.setType("release");
            serviceOrder.setId(null);
            serviceOrderService.insertSelective(serviceOrder);
            ServiceOrderDetail serviceOrderDetail = serviceOrderDetailMapper.selectByOrderId(serviceOrderId);
            // 获取开通订单id
            Long serviceOrderDetailId = serviceOrderDetail.getId();
            serviceOrderDetail.setOrderId(serviceOrder.getId());
            serviceOrderDetail.setId(null);
            serviceOrderDetail.setStartTime(new Date());
            serviceOrderDetail.setEndTime(new Date());
            serviceOrderDetailMapper.insertSelective(serviceOrderDetail);
            log.info("修改产品对应资源信息");
            SfProductResource sfProductResource = sfProductResourceMapper.selectByApplyOrderId(serviceOrderDetailId);
            if (Objects.nonNull(sfProductResource)) {
                sfProductResource.setStatus("unsubscribed");
                sfProductResource.setServiceOrderId(serviceOrderDetail.getId());
                serviceOrder.setUpdatedDt(new Date());
                sfProductResourceMapper.updateByPrimaryKey(sfProductResource);
            }
            return new RestResult<>(serviceOrder.getId());
        } catch (Exception e) {
            if (e instanceof BizException) {
                throw e;
            } else {
                throw new BizException("同步创建订单失败");
            }
        }
    }

    @PutMapping("/update/order")
    public RestResult<Object> insertOrder(@RequestBody UpdateOrderForm form){
        try {
            ServiceCategory serviceCategory = serviceCategoryMapper.selectByServiceType(form.getResourceType());
            if (Objects.isNull(serviceCategory)) {
                log.error("资源信息不存在");
                return null;
            }
            Criteria criteria = new Criteria();
            criteria.put("name", form.getName());
            criteria.put("serviceId", serviceCategory.getId());
            criteria.put("version",1);
            List<ServiceOrder> serviceOrders = serviceOrderService.selectByParams(criteria);
            if ("nameUpdate".equals(form.getType())) {
                criteria.clear();
                criteria.put("uuid", form.getUuid());
                serviceOrders = serviceOrderService.selectByParams(criteria);
            }
            if (CollectionUtils.isEmpty(serviceOrders)) {
                log.error("订单不存在");
                return null;
            }
            ServiceOrder serviceOrder = serviceOrders.stream().filter(s -> !OrderType.RELEASE.equals(s.getType())).findFirst().get();
            ServiceOrderDetail serviceOrderDetail = serviceOrderDetailMapper.selectByOrderIdAndCode(serviceOrder.getId(), form.getResourceType());
            if (form.getType() != null) {
                List<ConfigData> configDatas = JSONArray.parseArray(serviceOrderDetail.getProductConfigDesc(), ConfigData.class);
                ConfigData configData;
                String oldValueString = "";
                String oldValue = "";
                switch (form.getCode()){
                    case "EVS":
                        if ("nameUpdate".equals(form.getType())) {
                            serviceOrder.setName(form.getName());
                        } else if ("sizeUpdate".equals(form.getType())) {
                            configData = configDatas.stream().filter(c -> "disk".equals(c.getAttrKey())).findFirst().orElse(null);
                            oldValueString = configData.getValue();
                            oldValue = oldValueString.substring(oldValueString.indexOf("（") + 1, oldValueString.indexOf("GB"));
                            configData.setValue(oldValueString.replace(oldValue, form.getValue().toString()));
                            serviceOrderDetail.setProductConfigDesc(JSON.toJSONString(configDatas));
                        }
                        break;
                    case "ECS":
                        if ("vmTypeUpdate".equals(form.getType())){
                            configData = configDatas.stream().filter(c -> "vmType".equals(c.getAttrKey())).findFirst().orElse(null);
                            configData.setValue(form.getValue().toString());
                            serviceOrderDetail.setProductConfigDesc(JSON.toJSONString(configDatas));
                        } else if ("nameUpdate".equals(form.getType())) {
                            serviceOrder.setName(form.getName());
                        }
                        break;
                    case "BMS":
                        configData = configDatas.stream().filter(c -> "vmType".equals(c.getAttrKey())).findFirst().orElse(null);
                        configData.setValue(form.getValue().toString());
                        serviceOrderDetail.setProductConfigDesc(JSON.toJSONString(configDatas));
                        break;
                    case "EIP":
                        configData = configDatas.stream().filter(c -> "bandwidth".equals(c.getAttrKey())).findFirst().orElse(null);
                        oldValueString = configData.getValue();
                        oldValue = oldValueString.substring(oldValueString.indexOf("（") + 1, oldValueString.indexOf("Mbps"));
                        configData.setValue(oldValueString.replace(oldValue, form.getValue().toString()));
                        serviceOrderDetail.setProductConfigDesc(JSON.toJSONString(configDatas));
                        break;
                    case "OBS":
                        if ("storageClassUpdate".equals(form.getType())){
                            configData = configDatas.stream().filter(c -> "storageClass".equals(c.getAttrKey())).findFirst().orElse(null);
                            configData.setValue(StorageClassAndAclEnum.getValue(form.getValue().toString()));
                            serviceOrderDetail.setProductConfigDesc(JSON.toJSONString(configDatas));
                        } else if ("aclUpdate".equals(form.getType())) {
                            configData = configDatas.stream().filter(c -> "acl".equals(c.getAttrKey())).findFirst().orElse(null);
                            configData.setValue(StorageClassAndAclEnum.getValue(form.getValue().toString()));
                            serviceOrderDetail.setProductConfigDesc(JSON.toJSONString(configDatas));
                        }
                        break;
                }
                log.info("更新订单详情");
                serviceOrderDetailMapper.updateByPrimaryKey(serviceOrderDetail);
            }
            // uuid 参数不为空时，说明是创建接口回调导致得订单修改，同步创建产品实例
            if (Objects.nonNull(form.getUuid()) && !"nameUpdate".equals(form.getType())) {
                serviceOrder.setClusterUuid(form.getUuid());
                Map m = (Map) com.alibaba.fastjson.JSONObject.parse(serviceOrderDetail.getServiceConfig());
                SfProductResource sfProductResource = new SfProductResource();
                sfProductResource.setProductName(serviceCategory.getServiceName());
                sfProductResource.setProductType(serviceCategory.getServiceType());
                sfProductResource.setStatus(NORMAL);
                sfProductResource.setResourceInfo(serviceOrderDetail.getProductConfigDesc());
                sfProductResource.setStartTime(serviceOrder.getCreatedDt());
                sfProductResource.setOrgSid(serviceOrder.getOrgSid());
                sfProductResource.setCloudEnvId(Long.valueOf(m.get("cloudEnvId") + ""));
                sfProductResource.setServiceOrderId(serviceOrder.getId());
                WebUtil.prepareInsertParams(sfProductResource, RequestContextUtil.getCurrentUserName());
                log.info("插入产品对应资源信息");
                sfProductResourceMapper.insertSelective(sfProductResource);
            }
            log.info("更新订单信息");
            serviceOrderService.updateByPrimaryKey(serviceOrder);
            return new RestResult<>();
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof BizException) {
                throw e;
            } else {
                throw new BizException("修改订单/插入产品对应资源信息失败");
            }
        }
    }

    @DeleteMapping ("/dorder/{resourceName}")
    public RestResult<Object> deleteOrder(@PathVariable("resourceName")String resourceName){
        try {
            List<ServiceOrder> serviceOrders = serviceOrderService.selectByParams(new Criteria().put("name", resourceName));
            if (CollectionUtils.isEmpty(serviceOrders)) {
                throw new BizException("订单不存在");
            }
            ServiceOrder serviceOrder = serviceOrders.get(0);
            serviceOrderService.deleteByPrimaryKey(serviceOrder.getId());
            ServiceOrderDetail serviceOrderDetail = serviceOrderDetailMapper.selectByOrderId(serviceOrder.getId());
            serviceOrderResourceRefMapper.deleteByParams(new Criteria().put("orderDetailId", serviceOrderDetail.getId()));
            serviceOrderDetailMapper.deleteByPrimaryKey(serviceOrderDetail.getId());
            return new RestResult();
        } catch (Exception e) {
            e.printStackTrace();
            if (e instanceof BizException) {
                throw e;
            } else {
                throw new BizException("删除订单失败");
            }
        }
    }


    /**
     * 删除订单
     *
     * @param orderId 类型
     *
     * @return {@code RightCloudResult<Object>}
     */
    @DeleteMapping("/order/{orderId}")
    public RestResult deleteOrder(@PathVariable("orderId")Long orderId){
        try{
            serviceOrderService.deleteByPrimaryKey(orderId);
            ServiceOrderDetail serviceOrderDetail = serviceOrderDetailMapper.selectByOrderId(orderId);
            serviceOrderResourceRefMapper.deleteByParams(new Criteria().put("orderDetailId", serviceOrderDetail.getId()));
            serviceOrderDetailMapper.deleteByPrimaryKey(serviceOrderDetail.getId());
            return new RestResult();
        } catch (Exception e){
            throw new BizException("删除订单失败");
        }
    }

    @PostMapping("/inquiry/price")
    public RestResult<List<PriceResponse>> price(@RequestBody List<PriceRequest> priceRequestList) {
        try {
            PriceResponse priceResponse = new PriceResponse();
            List<BillingDetail> billingDetailList = new ArrayList<>();
            List<InquiryPriceResponse> convert = getInquiryPriceResponses(priceRequestList);
            priceResponse.setBillingPlanShortName("计费");
            convert.forEach(c -> {
                BillingDetail billingDetail = new BillingDetail();
                billingDetail.setBillingItemName(c.getProductCode());
                billingDetail.setOriginalPrice(c.getOriginalPrice());
                billingDetail.setTradeAmount(c.getTradePrice());
                billingDetailList.add(billingDetail);
            });
            priceResponse.setBillingPriceList(billingDetailList);
            priceResponse.setTradePrice(billingDetailList.stream().map(BillingDetail::getTradeAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            return new RestResult<>(Arrays.asList(priceResponse));
        } catch (Exception e) {
            if (e instanceof BizException){
                throw e;
            } else {
                throw new BizException("询价失败");
            }
        }
    }

    @Nullable
    private List<InquiryPriceResponse> getInquiryPriceResponses(List<PriceRequest> priceRequestList) {
        PriceRequest priceRequest = priceRequestList.get(0);
        ApplyServiceRequest applyServiceRequest = new ApplyServiceRequest();
        applyServiceRequest.setOrderType("apply");
        applyServiceRequest.setProjectId(RequestContextUtil.getAuthUserInfo().getOrgSid());
        List<ProductInfoVO> productInfo = new ArrayList<>();
        Map<String, Object> resources = BeanUtil.beanToMap(priceRequest.getData());
        Set<String> resourceCodes = resources.keySet();
        for (String code : resourceCodes) {
            ProductInfoVO productInfoVO = new ProductInfoVO();
            productInfoVO.setCloudEnvId(Long.valueOf(priceRequest.getEnvId()));
            productInfoVO.setChargeType(priceRequest.getChargeType());
            productInfoVO.setPeriod(BigDecimal.valueOf(priceRequest.getPeriod()));
            productInfoVO.setAmount(priceRequest.getQuantity());
            Map data = new HashMap();
            switch (code) {
                case VM_TYPE_ID:
                    productInfoVO.setProductCode(ECS.getDesc());
                    productInfoVO.setServiceId(serviceCategoryMapper.selectByServiceType(ECS.getDesc()).getId());
                    // 创建JSON对象
                    Map ecs = new HashMap();
                    ecs.put("chargeItemCategory", "compute");
                    ecs.put("category", resources.get(VM_TYPE_ID));
                    ecs.put("spec", resources.get(VM_TYPE_ID));
                    ecs.put("productCode", ECS.getDesc());
                    // 向JSON对象中添加数据
                    data.put(ECS.getDesc(), ecs);
                    productInfoVO.setData(data);
                    productInfo.add(productInfoVO);
                    break;
                case BMS_TYPE_ID:
                    productInfoVO.setProductCode(BMS.getDesc());
                    productInfoVO.setServiceId(serviceCategoryMapper.selectByServiceType(BMS.getDesc()).getId());
                    // 创建JSON对象
                    Map bms = new HashMap();
                    bms.put("chargeItemCategory", "compute");
                    bms.put("category", resources.get(BMS_TYPE_ID));
                    bms.put("spec", resources.get(BMS_TYPE_ID));
                    bms.put("productCode", BMS.getDesc());
                    // 向JSON对象中添加数据
                    data.put(BMS.getDesc(), bms);
                    productInfoVO.setData(data);
                    productInfo.add(productInfoVO);
                    break;
                case EVS_PRICE:
                    productInfoVO.setProductCode(EVS.getDesc());
                    productInfoVO.setServiceId(serviceCategoryMapper.selectByServiceType(EVS.getDesc()).getId());
                    // 创建JSON对象
                    Map evs = new HashMap();
                    evs.put("chargeItemCategory", "blockStorage");
                    evs.put("size", resources.get("size"));
                    evs.put("productCode", EVS.getDesc());
                    evs.put("category",  resources.get(EVS_PRICE));
                    evs.put("spec",  resources.get(EVS_PRICE));
                    evs.put("unitPriceDisplay", true);
                    // 向JSON对象中添加数据
                    data.put(EVS.getDesc(), evs);
                    productInfoVO.setData(data);
                    productInfo.add(productInfoVO);
                    break;
                case SYSTEM_DISK:
                    productInfoVO.setProductCode(EVS.getDesc());
                    productInfoVO.setServiceId(serviceCategoryMapper.selectByServiceType(EVS.getDesc()).getId());
                    // 创建JSON对象
                    Map evs1 = new HashMap();
                    evs1.put("chargeItemCategory", "blockStorage");
                    evs1.put("size", BeanUtil.beanToMap(resources.get(SYSTEM_DISK)).get("size"));
                    evs1.put("productCode", EVS.getDesc());
                    evs1.put("category",  BeanUtil.beanToMap(resources.get(SYSTEM_DISK)).get("vdTypeUuid"));
                    evs1.put("spec",  BeanUtil.beanToMap(resources.get(SYSTEM_DISK)).get("vdTypeUuid"));
                    evs1.put("unitPriceDisplay", true);
                    // 向JSON对象中添加数据
                    data.put(EVS.getDesc(), evs1);
                    productInfoVO.setData(data);
                    productInfo.add(productInfoVO);
                    break;
                case STORAGE_CLASS:
                    productInfoVO.setProductCode("OBS");
                    productInfoVO.setServiceId(serviceCategoryMapper.selectByServiceType("OBS").getId());
                    // 创建JSON对象
                    Map obs = new HashMap();
                    obs.put("category", resources.get(STORAGE_CLASS));
                    obs.put("chargeItemCategory", "blockStorage");
                    obs.put("size", 1);
                    obs.put("productCode", "OBS");
                    obs.put("unitPriceDisplay", true);
                    // 向JSON对象中添加数据
                    data.put("obs", obs);
                    productInfoVO.setData(data);
                    productInfo.add(productInfoVO);
                    break;
                case ELB_TYPE_ID:
                    productInfoVO.setProductCode(ELB.getDesc());
                    productInfoVO.setServiceId(serviceCategoryMapper.selectByServiceType(ELB.getDesc()).getId());
                    // 创建JSON对象
                    Map elb = new HashMap();
                    elb.put("allSpecDisplay", true);
                    elb.put("unitPriceDisplay", true);
                    elb.put("chargeItemCategory", "network");
                    elb.put("productCode", ELB.getDesc());
                    elb.put("size", BeanUtil.beanToMap(resources.get(ELB_TYPE_ID)).get("size"));
                    // 向JSON对象中添加数据
                    data.put(ELB.getDesc(), elb);
                    productInfoVO.setData(data);
                    productInfo.add(productInfoVO);
                    break;
                case EIP_TYPE_ID:
                    productInfoVO.setProductCode(EIP.getDesc());
                    productInfoVO.setServiceId(serviceCategoryMapper.selectByServiceType(EIP.getDesc()).getId());
                    // 创建JSON对象
                    Map eip = new HashMap();
                    eip.put("allSpecDisplay", true);
                    eip.put("unitPriceDisplay", true);
                    eip.put("chargeItemCategory", "network");
                    eip.put("productCode", EIP.getDesc());
                    eip.put("size", BeanUtil.beanToMap(resources.get(EIP_TYPE_ID)).get("size"));
                    Object size = BeanUtil.beanToMap(resources.get(EIP_TYPE_ID)).get("size");
                    Object bandwidth = BeanUtil.beanToMap(resources.get(EIP_TYPE_ID)).get("bandwidth");
                    if (Objects.nonNull(size) && size instanceof Number && Objects.nonNull(bandwidth) && bandwidth instanceof Number) {
                        eip.put("bandwidth", ((Number) size).longValue() * ((Number) bandwidth).longValue());
                    } else {
                        eip.put("bandwidth", BeanUtil.beanToMap(resources.get(EIP_TYPE_ID)).get("bandwidth"));
                    }

                    // 向JSON对象中添加数据
                    data.put(EIP.getDesc(), eip);
                    productInfoVO.setData(data);
                    productInfo.add(productInfoVO);
                    break;
            }
        }
        applyServiceRequest.setProductInfo(productInfo);
        RestResult restResult = feignService.inquiryPrice(applyServiceRequest);
        if (!restResult.getStatus()) {
            throw new BizException(String.valueOf(restResult.getMessage()));
        }
        List<InquiryPriceResponse> convert = BeanUtil.copyToList((Collection<?>) restResult.getData(), InquiryPriceResponse.class);
        return convert;
    }

    @PostMapping("/send/message")
    public RestResult<Void> sendMessage(@RequestBody SendMessageRequest sendMessageRequest){
        Map content = new HashMap();
        // 获取用户名
        Long userSid = recursionGetUserId(sendMessageRequest.getOrgSid());
        User user = userMapper.selectUser(userSid);
        if (Objects.isNull(user)) {
            throw new BizException("用户未找到");
        }
        ServiceCategory serviceCategory = serviceCategoryMapper.selectByServiceType(sendMessageRequest.getCodeType());
        if (Objects.isNull(serviceCategory)){
            throw new BizException("产品未找到");
        }
        List<Long> adminUserIds = userMapper.findAdminstratorsByEntityId(1L).stream().map(u -> u.getUserSid()).collect(Collectors.toList());
        content.put("userAccount", user.getAccount());
        content.put("cloudEnvName", sendMessageRequest.getCloudEnvName());
        content.put("productName", serviceCategory.getServiceType());
        content.put("actionType", sendMessageRequest.getActionType());
        content.put("resourceName", sendMessageRequest.getResourceName());
        // 发送管理员
        BaseNotificationMqBean baseToAdmin = new BaseNotificationMqBean();
        baseToAdmin.setMsgId(ProductMsg.BSSMGT_UNIVERSAL_COMPUTING_POWER_ADMIN);
        baseToAdmin.getToUserIds().addAll(adminUserIds);
        baseToAdmin.getImsgUserIds().addAll(adminUserIds);
        baseToAdmin.setMap(content);
        rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT,
                                      baseToAdmin);
        // 发送租户
        BaseNotificationMqBean baseToUser = new BaseNotificationMqBean();
        baseToUser.setMsgId(ProductMsg.BSSMGT_UNIVERSAL_COMPUTING_POWER_USER);
        baseToUser.getToUserIds().add(user.getUserSid());
        baseToUser.getImsgUserIds().add(user.getUserSid());
        baseToUser.setMap(content);
        rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT,
                                      baseToUser);
        return new RestResult<>();
    }

    private Long recursionGetUserId(Long orgSid) {
        Org org = orgMapper.selectByPrimaryKey(orgSid);
        if (Objects.nonNull(org.getParentId())) {
            recursionGetUserId(org.getParentId());
        }
        return org.getOwner();
    }

    private void insertOrderAndResource(ServiceOrder serviceOrder, ServiceOrderForm form, String productConfigDesc) {
        ServiceCategory serviceCategory = serviceCategoryMapper.selectByServiceType(form.getServiceCode());
        ServiceOrderDetail serviceOrderDetail = new ServiceOrderDetail();
        serviceOrderDetail.setOrderId(serviceOrder.getId());
        serviceOrderDetail.setServiceType(form.getServiceCode());
        // 创建服务配置JSON对象
        JSONObject ServiceConfig = new JSONObject();
        // 添加数据
        ServiceConfig.put("cloudEnvId", form.getCloudEnvId());
        serviceOrderDetail.setServiceConfig(ServiceConfig.toString());
        ConfigData configData = new ConfigData();
        // 构造配置展示json
        serviceOrderDetail.setProductConfigDesc(productConfigDesc);
        serviceOrderDetail.setChargeType("PostPaid");
        serviceOrderDetail.setOrgSid(form.getOrgSid());
        serviceOrderDetail.setStartTime(new Date());
        serviceOrderDetail.setServiceId(serviceCategory.getId());
        serviceOrderService.insertSelective(serviceOrderDetail);
        ServiceOrderResourceRef serviceOrderResourceRef = new ServiceOrderResourceRef();
        serviceOrderResourceRef.setOrderDetailId(serviceOrderDetail.getId());
        serviceOrderResourceRef.setResourceId(serviceCategory.getId().toString());
        serviceOrderResourceRef.setType(form.getServiceCode());
        ServiceOrderPriceDetail orderPriceDetail = new ServiceOrderPriceDetail();

        serviceOrderResourceRefMapper.insert(serviceOrderResourceRef);
    }

    private String makeProductConfigDesc(ServiceOrderForm form, List<String> needFields,List<ConfigData> configDatas) {
        // 默认添加
        try {
            configDatas.clear();
            Field[] fields = form.getClass().getDeclaredFields();
            ConfigData configData1 = new ConfigData();
            configData1.setLabel("计费类型");
            configData1.setAttrKey("chargeType");
            configData1.setValue("按量计费");
            configDatas.add(configData1);
            for (Field field : fields) {
                if (needFields.contains(field.getName()) && field.getAnnotation(ApiModelProperty.class) != null) {
                    ConfigData configData = new ConfigData();
                    configData.setLabel(field.getAnnotation(ApiModelProperty.class).value());
                    configData.setAttrKey(field.getName());
                    field.setAccessible(true);
                    configData.setValue(String.valueOf(field.get(form)));
                    configDatas.add(configData);
                }
            }
            ConfigData configData_1 = new ConfigData();
            configData_1.setLabel("备注");
            configData_1.setAttrKey("textarea-");
            configData_1.setValue("--");
            configDatas.add(configData_1);
            return JSON.toJSONString(configDatas);
        } catch (Exception e) {
            throw new BizException("构造配置展示json错误");
        }
    }


    @PostMapping("/query/resource")
    public RestResult<List<SfProductResourceVO>> queryResource(@RequestBody SfProductResourceForm productResourceForm) {
        List<Long> clusterIds = productResourceForm.getClusterIds();
        if (CollectionUtils.isEmpty(clusterIds)) {
            return new RestResult<>(new ArrayList<>());
        }
        String code = productResourceForm.getCode();
        List<SfProductResource> resources = sfProductResourceMapper.selectByClusterIds(clusterIds);
        List<SfProductResourceVO> convert = BeanConvertUtil.convert(resources, SfProductResourceVO.class);
        ServiceCategory serviceCategory = serviceCategoryMapper.selectByServiceType(code);
        if (Objects.nonNull(serviceCategory)) {
            convert.stream().forEach(e -> e.setServiceId(serviceCategory.getId()));
        }
        if(CollectionUtil.isNotEmpty(resources)){
            List<Long> resourceIds = convert.stream().map(SfProductResourceVO::getId).collect(Collectors.toList());
            List<ServiceOrderDetail> serviceOrderDetailList = serviceOrderDetailMapper.selectServiceOrderDetailByResourceIds(resourceIds);
            Map<String, List<ServiceOrderDetail>> resourceOrderDetailMap =
                    serviceOrderDetailList.stream().collect(Collectors.groupingBy(ServiceOrderDetail::getResourceId));
            log.info("OssController-resourceOrderDetailMap:{}", JSON.toJSONString(resourceOrderDetailMap));
            //根据orderid 拉取配置
            final List<Long> orderIds = resources.stream().map(SfProductResource::getServiceOrderId).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(orderIds)){
                List<ServiceOrderDetail> serviceOrderDetails = serviceOrderDetailMapper.selectByParams(
                        new Criteria("orderIds", orderIds));
               if(CollectionUtil.isNotEmpty(serviceOrderDetails)){
                   Map<Long, String> productMap = serviceOrderDetails.stream().collect(HashMap::new, (k, v) -> k.put(v.getOrderId(), v.getProductConfigDesc()),HashMap::putAll);
                   convert.stream().forEach(e -> {
                       List<ServiceOrderDetail> orderDetailList = resourceOrderDetailMap.get(String.valueOf(e.getId()));
                       log.info("OssController-orderDetailList-1:{}", orderDetailList);
                       if (CollectionUtils.isEmpty(orderDetailList)) {
                            orderDetailList = resourceOrderDetailMap.get(e.getId());
                           log.info("OssController-orderDetailList-2:{}", orderDetailList);
                       }
                       if (CollectionUtils.isNotEmpty(orderDetailList)) {
                           this.completeConfig(orderDetailList, e);
                            e.setProductConfigDesc(orderDetailList.get(0).getProductConfigDesc());
                        } else {
                            e.setProductConfigDesc(productMap.get(e.getServiceOrderId()));
                        }
                       // 取最新订单配置
                       if (ProductCodeEnum.ECS.getProductCode().equals(e.getProductType())) {
                           ServiceOrder serviceOrder = serviceOrderService.selectByPrimaryKey(
                                   orderDetailList.get(0).getOrderId());
                           List<ServiceOrderDetail> sourceSnserviceOrderDetails = serviceOrderDetailMapper.selectByOrderSourceSn(
                                   serviceOrder.getOrderSourceSn(), serviceOrder.getProductCode());
                           if (CollectionUtil.isNotEmpty(sourceSnserviceOrderDetails)) {
                               e.setProductConfigDesc(sourceSnserviceOrderDetails.get(0).getProductConfigDesc());
                           }
                       }
                   });
               }

            }
        }
        return new RestResult<>(convert);
    }

    private void completeConfig(List<ServiceOrderDetail> orderDetailList,
                                SfProductResourceVO sfProductResourceVO) {
        for (ServiceOrderDetail serviceOrderDetail : orderDetailList) {
            if (ProductCodeEnum.RS_BMS.getProductCode().equals(serviceOrderDetail.getServiceType())
                    || ProductCodeEnum.DCS.getProductCode().equals(serviceOrderDetail.getServiceType())) {
                try {
                    cn.hutool.json.JSONObject parseObj = JSONUtil.parseObj(serviceOrderDetail.getProductConfigDesc());
                    cn.hutool.json.JSONArray jsonArray = parseObj.getJSONArray("productConfigDesc");
                    cn.hutool.json.JSONObject removeObj = null;

                    for (int i = 0; i < jsonArray.size(); i++) {
                        cn.hutool.json.JSONObject jsonObject = jsonArray.getJSONObject(i);
                        if ("时长".equals(jsonObject.getStr("label"))) {
                            int offMonth = DateUtil.monthsBetweenDates(sfProductResourceVO.getStartTime(), sfProductResourceVO.getEndTime());
                            jsonObject.set("value", offMonth + "个月");
                        }else if ("申请数量".equals(jsonObject.getStr("label"))) {
                            removeObj = jsonObject;
                        }
                    }
                    if (Objects.nonNull(removeObj)) {
                        jsonArray.remove(removeObj);
                    }
                    serviceOrderDetail.setProductConfigDesc(parseObj.toString());
                }catch (Exception e) {
                    log.error("OssController-bms-订单解析异常-productConfigDesc:",e);
                }
            }
        }
    }

//    /**
//     * 创建订单、订单详情、订单价格详情
//     * @param serviceVO
//     * @param applyEntity
//     */
//    public void buildOrder(ServiceOrderForm form) {
//        List<InquiryPriceResponse> priceResult = getInquiryPriceResponses(form.getPriceRequestList());
//        BigDecimal couponDiscount = applyEntity.getBizCoupon() == null ? BigDecimal.ZERO : applyEntity.getBizCoupon().getDiscountAmount();
//        Map<String, List<InquiryPriceResponse>> priceMap = priceResult.stream().collect(Collectors.groupingBy(InquiryPriceResponse::getProductCode));
//        // 询出的总价
//        BigDecimal resultOriginalPrice = priceResult.stream().map(InquiryPriceResponse::getOriginalPrice).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
//        // 询出的折扣
//        BigDecimal orgDiscount = priceResult.stream().map(InquiryPriceResponse::getDiscountPrice).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
//        // 询出的固定收费
//        BigDecimal resultFixOncePrice = priceResult.stream().map(InquiryPriceResponse::getTradeOncePrice).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
//
//        BigDecimal priceWithoutCoupon = resultOriginalPrice.subtract(orgDiscount).add(resultFixOncePrice);
//        if (NumberUtil.isGreater(couponDiscount, priceWithoutCoupon)) {
//            couponDiscount = priceWithoutCoupon;
//            applyEntity.getBizCoupon().setDiscountAmount(couponDiscount);
//        }
//        BigDecimal finalCost = priceWithoutCoupon.subtract(couponDiscount);
//
//        applyEntity.setTradePrice(finalCost);
//        applyEntity.setOriginalPrice(resultOriginalPrice);
//        applyEntity.setOncePrice(resultFixOncePrice);
//        applyEntity.setDiscountPrice(orgDiscount);
//
//        ServiceOrder serviceOrder = BeanConvertUtil.convert(serviceVO, ServiceOrder.class);
//        // 订单、服务实例名称追加时间戳，区分重名
//        serviceOrder.setName(ProductCodeEnum.toDesc(form.getServiceCode()));
//        serviceOrder.setType(form.getType());
//        serviceOrder.setOwnerId(form.getOwnerId());
//        serviceOrder.setProcessFlag("01");
//        serviceOrder.setOrderSn(sidService.getMaxSid("ORDER_ID"));
//        serviceOrder.setStatus(OrderStatus.PENDING);
//        serviceOrder.setEntityId(1L);
//        serviceOrder.setEntityName("");
//        serviceOrder.setClusterUuid(form.getUuid());
//        if (ChargeTypeEnum.POSTPAID.getValue().equalsIgnoreCase(form.getPriceRequestList().get(0).getChargeType())){
//            serviceOrder.setFinalCost(BigDecimal.ZERO);
//            serviceOrder.setCurrAmount(BigDecimal.ZERO);
//            serviceOrder.setOriginalCost(BigDecimal.ZERO);
//            serviceOrder.setOrgDiscount(BigDecimal.ZERO);
//        } else {
//            serviceOrder.setFinalCost(finalCost);
//            log.info("生成审批订单的价格,...{}",finalCost);
//            serviceOrder.setCurrAmount(finalCost);
//            serviceOrder.setOriginalCost(NumberUtil.add(resultOriginalPrice, resultFixOncePrice));
//            serviceOrder.setOrgDiscount(orgDiscount);
//        }
//
//        //查询是否关联不计费产品
////        IsNonBillProductRequest request = new IsNonBillProductRequest();
////        Long serviceId = serviceVO.getProductInfo().get(0).getServiceId();
////        request.setSfServiceId(serviceId);
////        request.setChargeType(applyEntity.getChargeType());
////        Boolean isNonBillProduct = distributorProductService.selectIsNonBillProduct(request);
//        Boolean isNonBillProduct = false;
//        if (isNonBillProduct){
//            log.info("不计费产品，实际金额赋值为0");
//            serviceOrder.setFinalCost(BigDecimal.ZERO);
//            serviceOrder.setCurrAmount(BigDecimal.ZERO);
//            serviceOrder.setChargingType(SALE_TYPE);
//            serviceOrder.setOrgDiscount(BigDecimal.ZERO);
//        }else {
//            serviceOrder.setChargingType(NORMAL_TYPE);
//        }
//        //设置服务id到订单中
//        ServiceCategory serviceCategory = serviceCategoryMapper.selectByServiceType(form.getServiceCode());
//        BizBillingAccount bizBillingAccount = bizBillingAccountService.getBizBillingAccount(form.getOrgSid());
//        serviceOrder.setServiceId(serviceCategory.getId().toString());
//
//        serviceOrder.setOrgSid(form.getOrgSid());
//        serviceOrder.setCouponDiscount(couponDiscount);
//        serviceOrder.setBizBillingAccountId(bizBillingAccount.getId());
//        List<ProductInfoVO> productInfoVOList = serviceVO.getProductInfo();
//        if (productInfoVOList.size() > 0) {
//            productInfoVOList.get(0).setProjectId(serviceVO.getProjectId());
//        }
//        serviceOrder.setExtraAttr(JSON.toJSONString(productInfoVOList));
//
//        WebUserUtil.prepareInsertParams(serviceOrder, applyEntity.getAuthUser().getAccount());
//        serviceOrder.setProductCode(form.getServiceCode());
//        log.info("所属产品Code：[{}]", serviceOrder.getProductCode());
//        if (ProductCodeEnum.HPC_DRP.getProductType().equals(serviceOrder.getProductCode())) {
//            ServiceCategory serviceCategory = serviceCategoryMapper.selectByPrimaryKey(serviceId);
//            productName = Objects.nonNull(serviceCategory) && StrUtil.isNotBlank(serviceCategory.getProductName()) ? String.format("%s %s",serviceCategory.getProductName(), serviceCategory.getServiceType()) : productName;
//            log.info("HPC_DRP 所属产品名称：[{}]", productName);
//        }
//        serviceOrder.setProductName(productName);
//        serviceOrder.setAccountName(applyEntity.getAccount().getAccountName());
//        // 结算类型
//        if (Objects.nonNull(serviceVO.getProductInfo().get(0).getContractId())) {
//            serviceOrder.setSettlementType("合同价");
//            // 合同ID
//            serviceOrder.setContractId(serviceVO.getProductInfo().get(0).getContractId());
//        } else {
//            serviceOrder.setSettlementType("标准价");
//        }
//        // 代客下单管理员ID
//        if (Objects.nonNull(serviceVO.getBehalfUserSid())) {
//            serviceOrder.setBehalfUserSid(serviceVO.getBehalfUserSid());
//        }
//        // 订单来源
//        serviceOrder.setOrderSourceSn(serviceOrder.getOrderSn());
//        log.info("生成审批订单的价格wxh,...{}",serviceOrder.getFinalCost());
//        this.serviceOrderMapper.insert(serviceOrder);
//        applyEntity.setOrder(serviceOrder);
//        // 新增订单明细
//        int n = 0,sfsIndex=0;
//        for (ProductInfoVO productInfo : serviceVO.getProductInfo()) {
//            ServiceOrderDetail orderDetail = new ServiceOrderDetail();
//            orderDetail.setOrderId(serviceOrder.getId());
//            orderDetail.setServiceId(productInfo.getServiceId());
//            orderDetail.setChargeType(productInfo.getChargeType());
//            orderDetail.setServiceConfig(JSON.toJSONString(productInfo));
//
//            orderDetail.setVersion(1L);
//            orderDetail.setOrgSid(serviceOrder.getOrgSid());
//            String productCode = productInfo.getProductCode();
//            orderDetail.setServiceType(productCode);
//            orderDetail.setQuantity(productInfo.getAmount());
//            BigDecimal period = productInfo.getPeriod();
//            orderDetail.setDuration(Optional.ofNullable(period).orElse(BigDecimal.ONE).intValue());
//            orderDetail.setStartTime(Calendar.getInstance().getTime());
//            orderDetail.setDiscountRatio(priceResult.get(n).getPlatformDiscount());
//            orderDetail.setFloatingRatio(priceResult.get(n).getFloatingRatio());
//            orderDetail.setCustomFlag(productInfo.getCustomFlag());
//            if (Objects.nonNull(productInfo.getProductConfigDesc())) {
//                orderDetail.setProductConfigDesc(productInfo.getProductConfigDesc().getCurrentConfigDesc());
//            }
//
//            // 不同规格的同类产品需要通过规格区分（当前只用到了HPC专属资源池）
//            String productCategory = getProductCategoryFromServiceConfig(productInfo.getData(), hpcDrpFlg);
//
//            //设置额外配置费用,服务费用，资源费用
//            orderDetail.setOncePrice(priceResult.get(n).getBillingPrices().stream().filter(priceDetail ->
//                                                                                                   PriceType.EXTRA_CONFIG.equals(priceDetail.getPriceType())).map(BizBillingPriceVO::getOncePrice)
//                                                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
//            if (ChargeTypeEnum.PostPaid.getType().equalsIgnoreCase(applyEntity.getChargeType())) {
//                orderDetail.setServicePrice(priceResult.get(n).getBillingPrices().stream().filter(priceDetail ->
//                                                                                                          PriceType.SERVICE.equals(priceDetail.getPriceType())).map(BizBillingPriceVO::getHourPrice)
//                                                       .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
//                orderDetail.setResourcePrice(priceResult.get(n).getBillingPrices().stream().filter(priceDetail ->
//                                                                                                           PriceType.RESOURCE.equals(priceDetail.getPriceType())).map(BizBillingPriceVO::getHourPrice)
//                                                        .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
//            } else {
//                orderDetail.setServicePrice(priceResult.get(n).getBillingPrices().stream().filter(priceDetail ->
//                                                                                                          PriceType.SERVICE.equals(priceDetail.getPriceType())).map(BizBillingPriceVO::getMonthPrice)
//                                                       .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
//                orderDetail.setResourcePrice(priceResult.get(n).getBillingPrices().stream().filter(priceDetail ->
//                                                                                                           PriceType.RESOURCE.equals(priceDetail.getPriceType())).map(BizBillingPriceVO::getMonthPrice)
//                                                        .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
//            }
//            // 获取当前产品价格信息
//            List<InquiryPriceResponse> priceResponses = priceMap.get(
//                    hpcDrpFlg ? StrUtil.concat(true, productCode, productCategory) : productCode);
//            // 单价
//            BigDecimal price =
//                    finalCost != null ? NumberUtil.div(finalCost, orderDetail.getQuantity())
//                            : BigDecimal.ZERO;
//            if (hpcDrpFlg) {
//                price = NumberUtil.div(priceResponses.get(0).getOriginalPrice(), orderDetail.getQuantity());
//                //处理内置弹性文件sfs2.0(包含多条数据)
//                if(ProductCodeEnum.SFS2.getProductType().equals(priceResponses.get(sfsIndex).getProductCode())){
//                    price = NumberUtil.div(priceResponses.get(sfsIndex).getOriginalPrice(), orderDetail.getQuantity());
//                }
//            }
//
//            orderDetail.setPrice(price);
//            //关联不计费产品金额置为0
//            if (isNonBillProduct) {
//                orderDetail.setAmount(BigDecimal.ZERO);
//            }else{
//                orderDetail.setAmount(price.multiply(new BigDecimal(orderDetail.getQuantity())));
//            }
//            Integer duration = Convert.toInt(orderDetail.getDuration(), 0);
//            if (orderDetail.getDuration() != null && duration > 0) {
//                orderDetail.setEndTime(DateUtil.offsetMonth(orderDetail.getStartTime(), duration));
//            }
//            if (ProductCodeEnum.isInnerProduct(productInfo.getProductCode())) {
//                orderDetail.setStartTime(null);
//                orderDetail.setEndTime(null);
//            }
//            if (ProductCodeEnum.AI_MARKET.getProductType().equals(productInfo.getProductCode())) {
//                orderDetail.setDuration(productInfo.getAmount());
//                orderDetail.setQuantity(1);
//                if (YEAR.equals(productInfo.getPeriodType())) {
//                    orderDetail.setEndTime(DateUtil.offset(orderDetail.getStartTime(), DateField.YEAR, productInfo.getAmount()));
//                } else if (MONTH.equals(productInfo.getPeriodType())) {
//                    orderDetail.setEndTime(DateUtil.offset(orderDetail.getStartTime(), DateField.MONTH, productInfo.getAmount()));
//                }
//            }
//            //新增一个申请类型
//            orderDetail.setApplyType(productInfo.getApplyType());
//            serviceOrderDetailMapper.insert(orderDetail);
//            serviceOrder.getOrderDetails().add(orderDetail);
//            if (CollectionUtil.isNotEmpty(priceResponses)) {
//                //处理内置弹性文件sfs2.0(包含多条数据)
//                if(ProductCodeEnum.SFS2.getProductType().equals(priceResponses.get(sfsIndex).getProductCode())){
//                    makeOrderPriceDetail(priceResponses.get(sfsIndex), serviceOrder, orderDetail, applyEntity, hpcDrpFlg);
//                    sfsIndex++;
//                }else{
//                    makeOrderPriceDetail(priceResponses.get(0), serviceOrder, orderDetail, applyEntity, hpcDrpFlg);
//                }
//            }
//            n++;
//        }
//
//        applyEntity.setOrderDetails(serviceOrder.getOrderDetails());
//    }
}
