<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.cloudstar.rightcloud.oss.module.account.dao.user.UserMapper">
    <resultMap id="BaseResultMap" type="cn.com.cloudstar.rightcloud.core.pojo.dto.user.User">
        <id column="user_sid" property="userSid" jdbcType="BIGINT"/>
        <result column="user_type" property="userType" jdbcType="VARCHAR"/>
        <result column="user_code" property="userCode" jdbcType="VARCHAR"/>
        <result column="account" property="account" jdbcType="VARCHAR"/>
        <result column="password" property="password" jdbcType="VARCHAR"/>
        <result column="real_name" property="realName" jdbcType="VARCHAR"/>
        <result column="sex" property="sex" jdbcType="INTEGER"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="mobile" property="mobile" jdbcType="VARCHAR"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="company_id" property="companyId" jdbcType="BIGINT"/>
        <result column="company_name" property="companyName" jdbcType="BIGINT"/>
        <result column="org_sid" property="orgSid" jdbcType="BIGINT"/>
        <result column="project_id" property="projectId" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="error_count" property="errorCount" jdbcType="INTEGER"/>
        <result column="last_login_time" property="lastLoginTime" jdbcType="TIMESTAMP"/>
        <result column="last_login_ip" property="lastLoginIp" jdbcType="VARCHAR"/>
        <result column="service_limit_quantity" property="serviceLimitQuantity" jdbcType="INTEGER"/>
        <result column="apply_reason" property="applyReason" jdbcType="VARCHAR"/>
        <result column="sms_max" property="smsMax" jdbcType="INTEGER"/>
        <result column="uuid" property="uuid" jdbcType="VARCHAR"/>
        <result column="skin_theme" property="skinTheme" jdbcType="VARCHAR"/>
        <result column="auth_id" property="authId" jdbcType="VARCHAR"/>
        <result column="auth_type" property="authType" jdbcType="VARCHAR"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="created_dt" property="createdDt" jdbcType="TIMESTAMP"/>
        <result column="updated_by" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="updated_dt" property="updatedDt" jdbcType="TIMESTAMP"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
        <result column="org_name" property="orgName" jdbcType="VARCHAR"/>
        <result column="open_id" property="openId" jdbcType="VARCHAR"/>
        <result column="avatar_url" property="avatarUrl" jdbcType="VARCHAR"/>
        <result column="province" property="province" jdbcType="VARCHAR"/>
        <result column="city" property="city" jdbcType="VARCHAR"/>
        <result column="country" property="country" jdbcType="VARCHAR"/>
        <result column="wechat_name" property="wechatName" jdbcType="VARCHAR"/>
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="parent_sid" property="parentSid" jdbcType="BIGINT"/>
        <result column="iam" property="iam" jdbcType="TINYINT"/>
        <result column="forceResetPwd" property="forceResetPwd" jdbcType="VARCHAR"/>
        <result column="account_name" property="accountName" jdbcType="VARCHAR"/>
        <result column="distributor_name" property="distributorName" jdbcType="VARCHAR"/>
        <result column="certification_status" property="certificationStatus" jdbcType="VARCHAR"/>
        <result column="id_card_front" property="idCardFront" jdbcType="VARCHAR"/>
        <result column="id_card_reverse" property="idCardReverse" jdbcType="VARCHAR"/>
        <result column="accountId" property="accountId" jdbcType="BIGINT"/>
        <result column="salesmen_id" property="salesmenId" jdbcType="BIGINT"/>
        <result column="salesmen" property="salesmen" jdbcType="VARCHAR"/>
        <result column="subUserCount" property="subUserCount" jdbcType="BIGINT"/>
        <result column="unfreeze_type" property="unfreezeType" jdbcType="VARCHAR"/>
        <result column="auth_name" property="authName" jdbcType="VARCHAR"/>
        <result column="navigation_confirm" property="navigationConfirm" jdbcType="VARCHAR"/>
        <result column="business_tag" property="businessTag" jdbcType="TIMESTAMP"/>
        <result column="entityName" property="entityName" jdbcType="VARCHAR"/>
        <result column="entityIds" property="entityIds" jdbcType="VARCHAR"/>
        <result column="ccsp_mac" property="ccspMac" jdbcType="VARCHAR"/>
        <result column="solution" property="solution" jdbcType="VARCHAR"/>
        <result column="authorize_tag" property="authorizeTag" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <trim prefix="where" prefixOverrides="and|or">
            and status != '8'
            <if test="condition.realNameHash != null">
                and real_name_hash = #{condition.realNameHash}
            </if>
            <if test="condition.mobileHash != null">
                and mobile_hash = #{condition.mobileHash}
            </if>
            <if test="condition.emailHash != null">
                and email_hash = #{condition.emailHash}
            </if>


            <if test="condition.openId != null">
                and open_id = #{condition.openId}
            </if>
            <if test="condition.wechatName != null">
                and wechat_name = #{condition.wechatName}
            </if>

            <if test="condition.userType != null">
                and user_type = #{condition.userType}
            </if>
            <if test="condition.parentSid != null">
                and parent_sid = #{condition.parentSid}
            </if>
            <if test="condition.userCode != null">
                and user_code = #{condition.userCode}
            </if>
            <if test="condition.account != null">
                and account = #{condition.account}
            </if>
            <if test="condition.password != null">
                and password = #{condition.password}
            </if>
            <if test="condition.realName != null">
                and real_name like concat ('%', #{condition.realName}, '%')
            </if>
            <if test="condition.sex != null">
                and sex = #{condition.sex}
            </if>
            <if test="condition.email != null">
                and email = #{condition.email}
            </if>
            <if test="condition.mobile != null">
                and mobile = #{condition.mobile}
            </if>
            <if test="condition.title != null">
                and title = #{condition.title}
            </if>
            <if test="condition.companyId != null">
                and company_id = #{condition.companyId}
            </if>
            <if test="condition.orgSid != null">
                and org_sid = #{condition.orgSid}
            </if>
            <if test="condition.orgSidIsNull != null">
                and org_sid is null
            </if>
            <if test="condition.orgSidInNotNull == true">
                and org_sid is not null
            </if>
            <if test="condition.projectId != null">
                and project_id = #{condition.projectId}
            </if>
            <if test="condition.status != null">
                and status = #{condition.status}
            </if>
            <if test="condition.remark != null">
                and remark = #{condition.remark}
            </if>
            <if test="condition.errorCount != null">
                and error_count = #{condition.errorCount}
            </if>
            <if test="condition.lastLoginTime != null">
                and last_login_time = #{condition.lastLoginTime}
            </if>
            <if test="condition.lastLoginIp != null">
                and last_login_ip = #{condition.lastLoginIp}
            </if>
            <if test="condition.serviceLimitQuantity != null">
                and service_limit_quantity = #{condition.serviceLimitQuantity}
            </if>
            <if test="condition.applyReason != null">
                and apply_reason = #{condition.applyReason}
            </if>
            <if test="condition.smsMax != null">
                and sms_max = #{condition.smsMax}
            </if>
            <if test="condition.uuid != null">
                and uuid = #{condition.uuid}
            </if>
            <if test="condition.skinTheme != null">
                and skin_theme = #{condition.skinTheme}
            </if>
            <if test="condition.authId != null">
                and auth_id = #{condition.authId}
            </if>
            <if test="condition.authType != null">
                and auth_type = #{condition.authType}
            </if>
            <if test="condition.createdBy != null">
                and created_by = #{condition.createdBy}
            </if>
            <if test="condition.createdDt != null">
                and created_dt = #{condition.createdDt}
            </if>
            <if test="condition.updatedBy != null">
                and updated_by = #{condition.updatedBy}
            </if>
            <if test="condition.updatedDt != null">
                and updated_dt = #{condition.updatedDt}
            </if>
            <if test="condition.version != null">
                and version = #{condition.version}
            </if>
            <if test="condition.userSid != null">
                and user_sid = #{condition.userSid}
            </if>
            <if test="condition.userSidList != null and condition.userSidList.size() > 0">
                and user_sid in
                <foreach collection="condition.userSidList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.accountList != null and condition.accountList.size() > 0">
                and account in
                <foreach collection="condition.accountList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.statusNotIn != null and condition.statusNotIn.size() > 0">
                and `status` NOT IN
                <foreach collection="condition.statusNotIn" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.orgSidList != null and condition.orgSidList.size() > 0">
                and org_sid in
                <foreach collection="condition.orgSidList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.companyIdIsNull != null">
                and company_id is null
            </if>
            <if test="condition.parentSidIsNull == true">
                and parent_sid is null
            </if>
            <if test="condition.parentSidIsNull == false">
                and parent_sid is not null
            </if>
            <if test="condition.startTime != null">
                and start_time = #{condition.startTime}
            </if>
            <if test="condition.endTime != null">
                and end_time = #{condition.endTime}
            </if>
            <if test="condition.freezeStatus != null">
                and freeze_status = #{condition.freezeStatus}
            </if>
            <if test="condition.unfreezeType != null">
                and unfreeze_type = #{condition.unfreezeType}
            </if>
            <if test="condition.authName != null">
                and auth_name = #{condition.authName}
            </if>
            <if test="condition.certificationStatus != null">
                and certification_status = #{condition.certificationStatus}
            </if>
            <if test="condition.moduleType == 'bss'">
                AND ISNULL(org_sid)
            </if>
            <if test="condition.moduleType == 'console'">
                AND !ISNULL(org_sid)
            </if>
        </trim>
    </sql>
    <sql id="Example_Where_Clause2">
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.realNameHash != null">
                and real_name_hash = #{condition.realNameHash}
            </if>
            <if test="condition.mobileHash != null">
                and mobile_hash = #{condition.mobileHash}
            </if>
            <if test="condition.emailHash != null">
                and email_hash = #{condition.emailHash}
            </if>
            <if test="condition.openId != null">
                and open_id = #{condition.openId}
            </if>
            <if test="condition.wechatName != null">
                and wechat_name = #{condition.wechatName}
            </if>

            <if test="condition.userType != null">
                and user_type = #{condition.userType}
            </if>
            <if test="condition.parentSid != null">
                and parent_sid = #{condition.parentSid}
            </if>
            <if test="condition.userCode != null">
                and user_code = #{condition.userCode}
            </if>
            <if test="condition.account != null">
                and account = #{condition.account}
            </if>
            <if test="condition.password != null">
                and password = #{condition.password}
            </if>
            <if test="condition.realName != null">
                and real_name like concat ('%', #{condition.realName}, '%')
            </if>
            <if test="condition.accountLike != null">
                and account like concat ('%', #{condition.accountLike}, '%')
            </if>
            <if test="condition.sex != null">
                and sex = #{condition.sex}
            </if>
            <if test="condition.email != null">
                and email = #{condition.email}
            </if>
            <if test="condition.mobile != null">
                and mobile = #{condition.mobile}
            </if>
            <if test="condition.title != null">
                and title = #{condition.title}
            </if>
            <if test="condition.companyId != null">
                and company_id = #{condition.companyId}
            </if>
            <if test="condition.orgSid != null">
                and org_sid = #{condition.orgSid}
            </if>
            <if test="condition.orgSidIsNull != null">
                and org_sid is null
            </if>
            <if test="condition.projectId != null">
                and project_id = #{condition.projectId}
            </if>
            <if test="condition.status != null">
                and status = #{condition.status}
            </if>
            <if test="condition.remark != null">
                and remark = #{condition.remark}
            </if>
            <if test="condition.errorCount != null">
                and error_count = #{condition.errorCount}
            </if>
            <if test="condition.lastLoginTime != null">
                and last_login_time = #{condition.lastLoginTime}
            </if>
            <if test="condition.lastLoginIp != null">
                and last_login_ip = #{condition.lastLoginIp}
            </if>
            <if test="condition.serviceLimitQuantity != null">
                and service_limit_quantity = #{condition.serviceLimitQuantity}
            </if>
            <if test="condition.applyReason != null">
                and apply_reason = #{condition.applyReason}
            </if>
            <if test="condition.smsMax != null">
                and sms_max = #{condition.smsMax}
            </if>
            <if test="condition.uuid != null">
                and uuid = #{condition.uuid}
            </if>
            <if test="condition.skinTheme != null">
                and skin_theme = #{condition.skinTheme}
            </if>
            <if test="condition.authId != null">
                and auth_id = #{condition.authId}
            </if>
            <if test="condition.authType != null">
                and auth_type = #{condition.authType}
            </if>
            <if test="condition.createdBy != null">
                and created_by = #{condition.createdBy}
            </if>
            <if test="condition.createdDt != null">
                and created_dt = #{condition.createdDt}
            </if>
            <if test="condition.updatedBy != null">
                and updated_by = #{condition.updatedBy}
            </if>
            <if test="condition.updatedDt != null">
                and updated_dt = #{condition.updatedDt}
            </if>
            <if test="condition.version != null">
                and version = #{condition.version}
            </if>
            <if test="condition.userSid != null">
                and user_sid = #{condition.userSid}
            </if>
            <if test="condition.userSidList != null and condition.userSidList.size() > 0">
                and user_sid in
                <foreach collection="condition.userSidList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.statusNotIn != null and condition.statusNotIn.size() > 0">
                and `status` NOT IN
                <foreach collection="condition.statusNotIn" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.orgSidList != null and condition.orgSidList.size() > 0">
                and org_sid in
                <foreach collection="condition.orgSidList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.companyIdIsNull != null">
                and company_id is null
            </if>
            <if test="condition.startTime != null">
                and start_time = #{condition.startTime}
            </if>
            <if test="condition.endTime != null">
                and end_time = #{condition.endTime}
            </if>
            <if test="condition.freezeStatus != null">
                and freeze_status = #{condition.freezeStatus}
            </if>
            <if test="condition.unfreezeType != null">
                and unfreeze_type = #{condition.unfreezeType}
            </if>
            <if test="condition.authName != null">
                and auth_name = #{condition.authName}
            </if>
            <if test="condition.isPwdEndTime != null">
                <if test="condition.isPwdEndTime == true">
                    and pwd_end_time is not null
                </if>
                <if test="condition.isPwdEndTime == false">
                    and pwd_end_time is null
                </if>
            </if>
            <if test="condition.isParentSid != null">
                <if test="condition.isParentSid == true">
                    and parent_sid is not null
                </if>
                <if test="condition.isParentSid == false">
                    and parent_sid is null
                </if>
            </if>
        </trim>
    </sql>
    <sql id="Base_Column_List">
    user_sid, user_type, user_code, account, password, real_name, sex, email,
    mobile, title, company_id, org_sid, project_id, status, remark, error_count, last_login_time,
    last_login_ip, service_limit_quantity, apply_reason, sms_max, uuid, skin_theme, auth_id,
    auth_type, created_by, created_dt, updated_by, updated_dt, version, open_id, wechat_name, avatar_url,
    province, city, country, start_time, end_time,parent_sid,iam_id,certification_status,id_card_front,id_card_reverse,freeze_status,unfreeze_type,
    auth_name, navigation_confirm,pwd_end_time,policy_agree_sign,policy_agree_time,forceResetPwd,business_tag,ccsp_mac,ref_user_id
    </sql>
    <sql id="Base_Column_List_Alias">
    A.user_sid, A.user_type, A.user_code, A.account, A.password,  A.real_name, A.sex, A.email,
    A.mobile, A.title, A.company_id, A.org_sid, A.project_id,A. status, A.remark, A.error_count, A.last_login_time,
    A.last_login_ip, A.service_limit_quantity, A.apply_reason, A.sms_max, A.uuid, A.skin_theme, A.auth_id,
    A.auth_type, A.created_by, A.created_dt, A.updated_by, A.updated_dt, A.version, A.open_id, A.wechat_name, A.avatar_url,
    A.province, A.city, A.country, A.start_time, A.end_time,A.parent_sid,A.certification_status,A.id_card_front,A.id_card_reverse,A.freeze_status,A.unfreeze_type,
    A.auth_name,A.navigation_confirm,A.policy_agree_sign,A.policy_agree_time,A.business_tag,A.ccsp_mac
    </sql>
    <select id="selectByParams" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from sys_m_user
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>

    </select>
    <select id="selectSkipCCSPUserByParams" resultType="cn.com.cloudstar.rightcloud.core.pojo.dto.user.SkipCCSPUser"
            parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from sys_m_user
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>

    </select>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from sys_m_user
        where user_sid = #{userSid}
    </select>
    <select id="selectByUserSid" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        , (
        SELECT GROUP_CONCAT(ro.role_sid SEPARATOR ',')
        FROM sys_m_user_role ro
        WHERE A.user_sid = ro.user_sid
        ) AS roleString
        from sys_m_user as A
        where user_sid = #{userSid}
    </select>

    <select id="selectByAccount" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        , (
        SELECT GROUP_CONCAT(ro.role_sid SEPARATOR ',')
        FROM sys_m_user_role ro
        WHERE A.user_sid = ro.user_sid
        ) AS roleString
        from sys_m_user as A
        where A.account = #{account}
        limit 1 offset 0
    </select>
    <select id="selectListByOrgSid" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        , (
        SELECT GROUP_CONCAT(ro.role_sid SEPARATOR ',')
        FROM sys_m_user_role ro
        WHERE A.user_sid = ro.user_sid
        ) AS roleString
        from sys_m_user as A
        where A.org_sid = #{orgSid}
        limit 1 offset 0
    </select>
    <select id="selectSkipCCSPUserByPrimaryKey" resultType="cn.com.cloudstar.rightcloud.core.pojo.dto.user.SkipCCSPUser" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from sys_m_user
        where user_sid = #{userSid}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from sys_m_user
        where user_sid = #{userSid}
    </delete>
    <delete id="deleteByParams" parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria">
        delete from sys_m_user
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="cn.com.cloudstar.rightcloud.core.pojo.dto.user.User">
        insert into sys_m_user (user_sid, user_type, user_code, account, password,
                                real_name, sex, email, mobile, title, company_id, org_sid, project_id,
                                status, remark, error_count, last_login_time, last_login_ip, service_limit_quantity,
                                apply_reason, sms_max, uuid, skin_theme, auth_id, auth_type, created_by,
                                created_dt, updated_by, updated_dt, version, open_id, wechat_name, avatar_url,
                                province, city, country, start_time, end_time,ccsp_mac,real_name_hash,mobile_hash,email_hash)
        values (#{userSid}, #{userType}, #{userCode}, #{account}, #{password},
                #{realName}, #{sex}, #{email}, #{mobile}, #{title}, #{companyId}, #{orgSid}, #{projectId},
                #{status}, #{remark}, #{errorCount}, #{lastLoginTime}, #{lastLoginIp}, #{serviceLimitQuantity},
                #{applyReason}, #{smsMax}, #{uuid}, #{skinTheme}, #{authId,typeHandler=cn.com.cloudstar.rightcloud.oss.common.util.handler.AesTypeHandler}}, #{authType}, #{createdBy},
                #{createdDt}, #{updatedBy}, #{updatedDt}, #{version}, #{openId}, #{wechatName}, #{avatarUrl},
                #{province}, #{city}, #{country}, #{startTime}, #{endTime},#{ccspMac},#{realNameHash},#{mobileHash},#{emailHash})
    </insert>
    <insert id="insertSelective" parameterType="cn.com.cloudstar.rightcloud.core.pojo.dto.user.User"
        useGeneratedKeys="true" keyProperty="userSid">
        insert into sys_m_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userSid != null">
                user_sid,
            </if>
            <if test="userType != null">
                user_type,
            </if>
            <if test="userCode != null">
                user_code,
            </if>
            <if test="account != null">
                account,
            </if>
            <if test="password != null">
                password,
            </if>
            <if test="realName != null">
                real_name,
            </if>
            <if test="realNameHash != null">
                real_name_hash,
            </if>
            <if test="sex != null">
                sex,
            </if>
            <if test="email != null">
                email,
            </if>
            <if test="emailHash != null">
                email_hash,
            </if>
            <if test="mobile != null">
                mobile,
            </if>
            <if test="mobileHash != null">
                mobile_hash,
            </if>
            <if test="title != null">
                title,
            </if>
            <if test="companyId != null">
                company_id,
            </if>
            <if test="orgSid != null">
                org_sid,
            </if>
            <if test="projectId != null">
                project_id,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="errorCount != null">
                error_count,
            </if>
            <if test="lastLoginTime != null">
                last_login_time,
            </if>
            <if test="lastLoginIp != null">
                last_login_ip,
            </if>
            <if test="serviceLimitQuantity != null">
                service_limit_quantity,
            </if>
            <if test="applyReason != null">
                apply_reason,
            </if>
            <if test="smsMax != null">
                sms_max,
            </if>
            <if test="uuid != null">
                uuid,
            </if>
            <if test="skinTheme != null">
                skin_theme,
            </if>
            <if test="authId != null">
                auth_id,
            </if>

            <if test="authType != null">
                auth_type,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="createdDt != null">
                created_dt,
            </if>
            <if test="updatedBy != null">
                updated_by,
            </if>
            <if test="updatedDt != null">
                updated_dt,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="openId != null">
                open_id,
            </if>
            <if test="wechatName != null">
                wechat_name,
            </if>
            <if test="avatarUrl != null">
                avatar_url,
            </if>
            <if test="province != null">
                province,
            </if>
            <if test="city != null">
                city,
            </if>
            <if test="country != null">
                country,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="policyAgreeSign != null">
                policy_agree_sign,
            </if>
            <if test="policyAgreeTime != null">
                policy_agree_time,
            </if>
            <if test="forceResetPwd != null">
                forceResetPwd,
            </if>
            <if test="pwdEndTime != null">
                pwd_end_time,
            </if>
            <if test="ccspMac != null">
                ccsp_mac,
            </if>
            <if test="businessTag != null">
                business_tag
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userSid != null">
                #{userSid},
            </if>
            <if test="userType != null">
                #{userType},
            </if>
            <if test="userCode != null">
                #{userCode},
            </if>
            <if test="account != null">
                #{account},
            </if>
            <if test="password != null">
                #{password},
            </if>
            <if test="realName != null">
                #{realName},
            </if>
            <if test="realNameHash != null">
                #{realNameHash},
            </if>
            <if test="sex != null">
                #{sex},
            </if>
            <if test="email != null">
                #{email},
            </if>
            <if test="emailHash != null">
                #{emailHash},
            </if>
            <if test="mobile != null">
                #{mobile},
            </if>
            <if test="mobileHash != null">
                #{mobileHash},
            </if>
            <if test="title != null">
                #{title},
            </if>
            <if test="companyId != null">
                #{companyId},
            </if>
            <if test="orgSid != null">
                #{orgSid},
            </if>
            <if test="projectId != null">
                #{projectId},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="errorCount != null">
                #{errorCount},
            </if>
            <if test="lastLoginTime != null">
                #{lastLoginTime},
            </if>
            <if test="lastLoginIp != null">
                #{lastLoginIp},
            </if>
            <if test="serviceLimitQuantity != null">
                #{serviceLimitQuantity},
            </if>
            <if test="applyReason != null">
                #{applyReason},
            </if>
            <if test="smsMax != null">
                #{smsMax},
            </if>
            <if test="uuid != null">
                #{uuid},
            </if>
            <if test="skinTheme != null">
                #{skinTheme},
            </if>
            <if test="authId != null">
                #{authId},
            </if>

            <if test="authType != null">
                #{authType},
            </if>
            <if test="createdBy != null">
                #{createdBy},
            </if>
            <if test="createdDt != null">
                #{createdDt},
            </if>
            <if test="updatedBy != null">
                #{updatedBy},
            </if>
            <if test="updatedDt != null">
                #{updatedDt},
            </if>
            <if test="version != null">
                #{version},
            </if>
            <if test="openId != null">
                #{openId},
            </if>
            <if test="wechatName != null">
                #{wechatName},
            </if>
            <if test="avatarUrl != null">
                #{avatarUrl},
            </if>
            <if test="province != null">
                #{province},
            </if>
            <if test="city != null">
                #{city},
            </if>
            <if test="country != null">
                #{country},
            </if>
            <if test="startTime != null">
                #{startTime},
            </if>
            <if test="endTime != null">
                #{endTime},
            </if>

            <if test="policyAgreeSign != null">
                #{policyAgreeSign},
            </if>
            <if test="policyAgreeTime != null">
                #{policyAgreeTime},
            </if>
            <if test="forceResetPwd != null">
                #{forceResetPwd},
            </if>
            <if test="pwdEndTime != null">
                #{pwdEndTime},
            </if>
            <if test="ccspMac != null">
                #{ccspMac},
            </if>
            <if test="businessTag !=null">
                #{businessTag}
            </if>
        </trim>
    </insert>
    <select id="countByParams" parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria"
        resultType="java.lang.Integer">
        select count(*) from sys_m_user
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <select id="findByAccount" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sys_m_user
        where
        account = #{account}
    </select>
    <select id="findByAccountLike" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sys_m_user
        where
        account like concat ('%', #{accountLike}, '%') and status != 8;
    </select>
    <update id="updateBatchUser" parameterType="java.util.List">
        <foreach collection="users" item="record" index="index" separator=";">
            UPDATE sys_m_user
            <set>
                <if test="record.userSid != null">
                    user_sid = #{record.userSid},
                </if>
                <if test="record.userType != null">
                    user_type = #{record.userType},
                </if>
                <if test="record.userCode != null">
                    user_code = #{record.userCode},
                </if>
                <if test="record.account != null">
                    account = #{record.account},
                </if>
                <if test="record.password != null">
                    password = #{record.password},
                </if>

                <if test="record.sex != null">
                    sex = #{record.sex},
                </if>
                <if test="record.realName != null">
                    real_name = #{record.realName},
                </if>
                <if test="record.email != null">
                    email = #{record.email},
                </if>
                <if test="record.mobile != null">
                    mobile = #{record.mobile},
                </if>

                <if test="record.realNameHash != null">
                    real_name_hash = #{record.realNameHash},
                </if>
                <if test="record.emailHash != null">
                    email_hash = #{record.emailHash},
                </if>
                <if test="record.mobileHash != null">
                    mobile_hash = #{record.mobileHash},
                </if>

                <if test="record.title != null">
                    title = #{record.title},
                </if>
                <if test="record.companyId != null">
                    company_id = #{record.companyId},
                </if>
                <if test="record.orgSid != null">
                    org_sid = #{record.orgSid},
                </if>
                <if test="record.projectId != null">
                    project_id = #{record.projectId},
                </if>
                <if test="record.status != null">
                    status = #{record.status},
                </if>
                <if test="record.remark != null">
                    remark = #{record.remark},
                </if>
                <if test="record.errorCount != null">
                    error_count = #{record.errorCount},
                </if>
                <if test="record.lastLoginTime != null">
                    last_login_time = #{record.lastLoginTime},
                </if>
                <if test="record.lastLoginIp != null">
                    last_login_ip = #{record.lastLoginIp},
                </if>
                <if test="record.serviceLimitQuantity != null">
                    service_limit_quantity = #{record.serviceLimitQuantity},
                </if>
                <if test="record.applyReason != null">
                    apply_reason = #{record.applyReason},
                </if>
                <if test="record.smsMax != null">
                    sms_max = #{record.smsMax},
                </if>
                <if test="record.uuid != null">
                    uuid = #{record.uuid},
                </if>
                <if test="record.skinTheme != null">
                    skin_theme = #{record.skinTheme},
                </if>
                <if test="record.authId != null">
                    auth_id = #{record.authId},
                </if>

                <if test="record.authType != null">
                    auth_type = #{record.authType},
                </if>
                <if test="record.createdBy != null">
                    created_by = #{record.createdBy},
                </if>
                <if test="record.createdDt != null">
                    created_dt = #{record.createdDt},
                </if>
                <if test="record.updatedBy != null">
                    updated_by = #{record.updatedBy},
                </if>
                <if test="record.updatedDt != null">
                    updated_dt = #{record.updatedDt},
                </if>
                <if test="record.version != null">
                    version = #{record.version},
                </if>
                <if test="record.openId != null">
                    open_id = #{record.openId},
                </if>
                <if test="record.avatarUrl != null">
                    avatar_url = #{record.avatarUrl},
                </if>
                <if test="record.wechatName != null">
                    wechat_name = #{record.wechatName},
                </if>
                <if test="record.province != null">
                    province = #{record.province},
                </if>
                <if test="record.city != null">
                    city = #{record.city},
                </if>
                <if test="record.country != null">
                    country = #{record.country},
                </if>
                <if test="record.startTime != null">
                    start_time = #{record.startTime},
                </if>
                <if test="record.endTime != null">
                    end_time = #{record.endTime},
                </if>
                <if test="record.pwdEndTime != null">
                    pwd_end_time = #{record.pwdEndTime},
                </if>
            </set>
            where user_sid = #{record.userSid}
        </foreach>
    </update>

    <update id="updateByParamsSelective" parameterType="map">
        update sys_m_user
        <set>
            <if test="record.userSid != null">
                user_sid = #{record.userSid},
            </if>
            <if test="record.userType != null">
                user_type = #{record.userType},
            </if>
            <if test="record.userCode != null">
                user_code = #{record.userCode},
            </if>
            <if test="record.account != null">
                account = #{record.account},
            </if>
            <if test="record.password != null">
                password = #{record.password},
            </if>
            <if test="record.sex != null">
                sex = #{record.sex},
            </if>

            <if test="record.realName != null">
                real_name = #{record.realName},
            </if>
            <if test="record.sex != null">
                sex = #{record.sex},
            </if>
            <if test="record.email != null">
                email = #{record.email},
            </if>
            <if test="record.mobile != null">
                mobile = #{record.mobile},
            </if>

            <if test="record.realNameHash != null">
                real_name_hash = #{record.realNameHash},
            </if>
            <if test="record.emailHash != null">
                email_hash = #{record.emailHash},
            </if>
            <if test="record.mobileHash != null">
                mobile_hash = #{record.mobileHash},
            </if>

            <if test="record.title != null">
                title = #{record.title},
            </if>
            <if test="record.companyId != null">
                company_id = #{record.companyId},
            </if>
            <if test="record.orgSid != null">
                org_sid = #{record.orgSid},
            </if>
            <if test="record.projectId != null">
                project_id = #{record.projectId},
            </if>
            <if test="record.status != null">
                status = #{record.status},
            </if>
            <if test="record.remark != null">
                remark = #{record.remark},
            </if>
            <if test="record.errorCount != null">
                error_count = #{record.errorCount},
            </if>
            <if test="record.lastLoginTime != null">
                last_login_time = #{record.lastLoginTime},
            </if>
            <if test="record.lastLoginIp != null">
                last_login_ip = #{record.lastLoginIp},
            </if>
            <if test="record.serviceLimitQuantity != null">
                service_limit_quantity = #{record.serviceLimitQuantity},
            </if>
            <if test="record.applyReason != null">
                apply_reason = #{record.applyReason},
            </if>
            <if test="record.smsMax != null">
                sms_max = #{record.smsMax},
            </if>
            <if test="record.uuid != null">
                uuid = #{record.uuid},
            </if>
            <if test="record.skinTheme != null">
                skin_theme = #{record.skinTheme},
            </if>
            <if test="record.authId != null">
                auth_id = #{record.authId},
            </if>

            <if test="record.authType != null">
                auth_type = #{record.authType},
            </if>
            <if test="record.createdBy != null">
                created_by = #{record.createdBy},
            </if>
            <if test="record.createdDt != null">
                created_dt = #{record.createdDt},
            </if>
            <if test="record.updatedBy != null">
                updated_by = #{record.updatedBy},
            </if>
            <if test="record.updatedDt != null">
                updated_dt = #{record.updatedDt},
            </if>
            <if test="record.version != null">
                version = #{record.version},
            </if>

            <if test="record.openId != null">
                open_id = #{record.openId},
            </if>
            <if test="record.avatarUrl != null">
                avatar_url = #{record.avatarUrl},
            </if>
            <if test="record.wechatName != null">
                wechat_name = #{record.wechatName},
            </if>
            <if test="record.province != null">
                province = #{record.province},
            </if>
            <if test="record.city != null">
                city = #{record.city},
            </if>
            <if test="record.country != null">
                country = #{record.country},
            </if>
            <if test="record.startTime != null">
                start_time = #{record.startTime},
            </if>
            <if test="record.endTime != null">
                end_time = #{record.endTime},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByParams" parameterType="map">
        update sys_m_user
        set user_sid = #{record.userSid},
        user_type = #{record.userType},
        user_code = #{record.userCode},
        account = #{record.account},
        password = #{record.password},
        sex = #{record.sex},
        real_name = #{record.realName},
        email = #{record.email},
        mobile = #{record.mobile},
        real_name_hash = #{record.realNameHash},
        email_hash = #{record.emailHash},
        mobile_hash = #{record.mobileHash},
        title = #{record.title},
        company_id = #{record.companyId},
        org_sid = #{record.orgSid},
        project_id = #{record.projectId},
        status = #{record.status},
        remark = #{record.remark},
        error_count = #{record.errorCount},
        last_login_time = #{record.lastLoginTime},
        last_login_ip = #{record.lastLoginIp},
        service_limit_quantity = #{record.serviceLimitQuantity},
        apply_reason = #{record.applyReason},
        sms_max = #{record.smsMax},
        uuid = #{record.uuid},
        skin_theme = #{record.skinTheme},
        auth_id = #{record.authId},
        auth_type = #{record.authType},
        created_by = #{record.createdBy},
        created_dt = #{record.createdDt},
        updated_by = #{record.updatedBy},
        updated_dt = #{record.updatedDt},
        version = #{record.version},
        wechat_name = #{record.wechatName},
        open_id =#{record.openId},
        avatar_url = #{record.avatarUrl},
        province = #{record.province},
        city =#{record.city},
        country = #{record.country},
        start_time = #{record.startTime},
        end_time = #{record.endTime},
        navigation_confirm = #{record.navigationConfirm},
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="cn.com.cloudstar.rightcloud.core.pojo.dto.user.User">
        update sys_m_user
        <set>
            <if test="userType != null">
                user_type = #{userType},
            </if>
            <if test="userCode != null">
                user_code = #{userCode},
            </if>
            <if test="account != null">
                account = #{account},
            </if>
            <if test="password != null">
                password = #{password},
            </if>
            <if test="realName != null">
                real_name = #{realName},
            </if>
            <if test="sex != null">
                sex = #{sex},
            </if>
            <if test="email != null">
                email = #{email},
            </if>
            <if test="mobile != null">
                mobile = #{mobile},
            </if>
            <if test="realNameHash != null">
                real_name_hash = #{realNameHash},
            </if>
            <if test="emailHash != null">
                email_hash = #{emailHash},
            </if>
            <if test="mobileHash != null">
                mobile_hash = #{mobileHash},
            </if>
            <if test="title != null">
                title = #{title},
            </if>
            <if test="companyId != null">
                company_id = #{companyId},
            </if>
            <if test="orgSid != null">
                org_sid = #{orgSid},
            </if>
            <if test="projectId != null">
                project_id = #{projectId},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="errorCount != null">
                error_count = #{errorCount},
            </if>
            <if test="lastLoginTime != null">
                last_login_time = #{lastLoginTime},
            </if>
            <if test="lastLoginIp != null">
                last_login_ip = #{lastLoginIp},
            </if>
            <if test="serviceLimitQuantity != null">
                service_limit_quantity = #{serviceLimitQuantity},
            </if>
            <if test="applyReason != null">
                apply_reason = #{applyReason},
            </if>
            <if test="smsMax != null">
                sms_max = #{smsMax},
            </if>
            <if test="uuid != null">
                uuid = #{uuid},
            </if>
            <if test="skinTheme != null">
                skin_theme = #{skinTheme},
            </if>
            <if test="authId != null">
                auth_id = #{authId},
            </if>

            <if test="authType != null">
                auth_type = #{authType},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy},
            </if>
            <if test="createdDt != null">
                created_dt = #{createdDt},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy},
            </if>
            <if test="updatedDt != null">
                updated_dt = #{updatedDt},
            </if>
            <if test="version != null">
                version = #{version},
            </if>
            <if test="openId != null">
                open_id = #{openId},
            </if>
            <if test="avatarUrl != null">
                avatar_url = #{avatarUrl},
            </if>
            <if test="wechatName != null">
                wechat_name = #{wechatName},
            </if>
            <if test="city != null">
                city = #{city},
            </if>
            <if test="province != null">
                province = #{province},
            </if>
            <if test="country != null">
                country = #{country},
            </if>
            <if test="iamId != null">
                iam_id = #{iamId},
            </if>
            <if test="freezeStatus != null">
                freeze_status = #{freezeStatus},
            </if>
            <if test="unfreezeType != null">
                unfreeze_type = #{unfreezeType},
            </if>
            <if test="pwdEndTime !=null">
                pwd_end_time = #{pwdEndTime},
            </if>
            <if test="businessTag !=null">
                business_tag = #{businessTag},
            </if>
            <if test="policyAgreeSign !=null">
                policy_agree_sign = #{policyAgreeSign},
            </if>
            <if test="policyAgreeTime !=null">
                policy_agree_time = #{policyAgreeTime},
            </if>
            <if test="forceResetPwd !=null">
                forceResetPwd = #{forceResetPwd},
            </if>
            <if test="startTime !=null">
                start_time = #{startTime},
            </if>
            <if test="endTime !=null">
                end_time = #{endTime},
            </if>
            <if test="ccspMac !=null">
                ccsp_mac = #{ccspMac},
            </if>
        </set>
        where user_sid = #{userSid}
    </update>
    <update id="updateByPrimaryKey" parameterType="cn.com.cloudstar.rightcloud.core.pojo.dto.user.User">
        update sys_m_user
        set user_type              = #{userType},
            user_code              = #{userCode},
            account                = #{account},
            password               = #{password},
            sex                    = #{sex},
            real_name              = #{realName},
            email                  = #{email},
            mobile                 = #{mobile},
            real_name_hash         = #{realNameHash},
            email_hash             = #{emailHash},
            mobile_hash            = #{mobileHash},
            title                  = #{title},
            company_id             = #{companyId},
            org_sid                = #{orgSid},
            project_id             = #{projectId},
            status                 = #{status},
            remark                 = #{remark},
            error_count            = #{errorCount},
            last_login_time        = #{lastLoginTime},
            last_login_ip          = #{lastLoginIp},
            service_limit_quantity = #{serviceLimitQuantity},
            apply_reason           = #{applyReason},
            sms_max                = #{smsMax},
            uuid                   = #{uuid},
            skin_theme             = #{skinTheme},
            auth_id                = #{authId},
            auth_type              = #{authType},
            created_by             = #{createdBy},
            created_dt             = #{createdDt},
            updated_by             = #{updatedBy},
            updated_dt             = #{updatedDt},
            version                = #{version},
            wechat_name            = #{wechatName},
            open_id                =#{openId},
            avatar_url             = #{avatarUrl},
            city                   = #{city},
            province               = #{province},
            country                = #{country},
            start_time             = #{startTime},
            end_time               = #{endTime},
            navigation_confirm     = #{navigationConfirm},
            ccsp_mac               = #{ccspMac},
            freeze_status          = #{freezeStatus},
            unfreeze_type          = #{unfreezeType},
            ref_user_id          = #{refUserId}
        where user_sid = #{userSid}
    </update>

    <!-- 根据登录帐号查询用户信息 -->
    <select id="findByLoginAccount" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria">
        select
        <include refid="Base_Column_List"/>
        from sys_m_user A
        <trim prefix="where" prefixOverrides="and|or">
            and A.status != '8' and A.auth_type IN ('ad','local')
            <if test="condition.account != null">
                and (A.account = #{condition.account} or A.email = #{condition.account} or A.mobile =
                #{condition.account})
            </if>
            <if test="condition.userType != null">
                and A.user_type = #{condition.userType}
            </if>
        </trim>
    </select>

    <select id="findList" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List_Alias"/>
        ,group_concat(C.org_name) AS org_name
        ,if(ifnull(A.parent_sid,0) > 0,1,0) iam
        ,bba.id AS accountId
        ,bba.account_name
        ,bd.name distributor_name
        ,bba.salesmen_id
        ,t2.real_name as salesmen
        ,allUser.subUserCount as subUserCount
        from sys_m_user A
        LEFT JOIN sys_m_org O ON O.org_sid = A.company_id
        LEFT JOIN sys_m_user_org B ON A.user_sid = B.user_sid
        LEFT JOIN sys_m_org C ON C.org_sid = B.org_sid
        left join sys_m_user_role ro on A.user_sid = ro.user_sid
        left join biz_billing_account bba on bba.org_sid = A.org_sid
        left join (select user_sid, real_name, email from sys_m_user) t2 on bba.salesmen_id = t2.user_sid
        left join biz_distributor bd on bd.id = bba.distributor_id
        inner join (
        select t2.usersid, sum(t2.subUserCount) as subUserCount
        from (
        select if(ifnull(t.parent_sid, 0) > 0, t.parent_sid, t.user_sid) as usersid, if(ifnull(t.parent_sid, 0) > 0, 1,
        0) as subUserCount
        from (select a.user_sid, a.parent_sid
        from sys_m_user a
        left join sys_m_user_role ro on A.user_sid = ro.user_sid
        where A.user_sid != 100 and A.status != '8'
        <if test="condition.account != null">
            AND A.account LIKE concat('%', #{condition.account}, '%')
        </if>
        <if test="condition.realName != null">
            AND A.real_name LIKE concat('%', #{condition.realName},'%')
        </if>
        <if test="condition.mobile != null">
            AND A.mobile LIKE concat('%', #{condition.mobile}, '%')
        </if>
        <if test="condition.email != null">
            AND A.email LIKE concat('%', #{condition.email}, '%')
        </if>
        <if test="condition.status != null">
            AND A.status = #{condition.status}
        </if>
        <if test="condition.beTaken != null">
            AND (A.status = 2 or A.certification_status = 'authing')
        </if>
        <if test="condition.roleSid != null and condition.roleSid.size() > 0">
            AND ro.role_sid in
            <foreach collection="condition.roleSid" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="condition.certificationStatus != null">
            AND certification_status = #{condition.certificationStatus}
        </if>) t) t2
        group by t2.usersid
        ) allUser on allUser.usersid = A.user_sid
        where 1 = 1
        <if test="condition.status != null">
            AND A.status =#{condition.status}
        </if>
        <if test="condition.authType != null">
            AND A.auth_type =#{condition.authType}
        </if>
        <if test="condition.orgName != null">
            AND C.org_name LIKE concat('%', #{condition.orgName}, '%')
        </if>
        <if test="condition.distributorName != null">
            and bd.name like concat('%', #{condition.distributorName}, '%')
        </if>
        <if test="condition.orgSid != null">
            AND C.org_sid = #{condition.orgSid}
        </if>
        <if test="condition.accountNameLike != null">
            AND bba.account_name LIKE concat('%', #{condition.accountNameLike}, '%')
        </if>
        group by A.user_sid
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>

    </select>

    <select id="findUserList" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria">
        select
        <if test="distinct">
            distinct
        </if>
        <choose>
            <when test="condition.dynamicParameter != null">
                A.user_sid,A.created_dt,C.org_name AS org_name,itCode.CODE_VALUE AS industry
            </when>
            <otherwise>
                <include refid="Base_Column_List_Alias"/>
                ,C.org_name AS org_name,
                IF( ifnull( A.parent_sid, 0 ) > 0, 1, 0 ) iam,
                bba.id AS accountId,
                bba.account_name,
                bd.NAME distributor_name,
                bba.entity_name AS entityName,
                IFNULL( allUser.subUserCount, 0 ) AS subUserCount,
                itCode.CODE_VALUE AS industry
            </otherwise>
        </choose>
        from sys_m_user A
        LEFT JOIN sys_m_org C ON C.org_sid = A.org_sid
        LEFT JOIN sys_m_user_role ro ON A.user_sid = ro.user_sid
        LEFT JOIN sys_m_code itCode ON C.industry_type = itCode.CODE_VALUE
        AND itCode.CODE_CATEGORY = 'INDUSTRY_TYPE'
        LEFT JOIN biz_billing_account bba ON bba.org_sid = A.org_sid
        LEFT JOIN biz_distributor bd ON bd.id = bba.distributor_id
        LEFT JOIN ( SELECT
        a.parent_sid AS usersid,
        count( a.parent_sid ) subUserCount
        FROM
        sys_m_user a
        LEFT JOIN sys_m_user_role ro ON A.user_sid = ro.user_sid
        LEFT JOIN sys_m_org C ON c.org_sid = a.org_sid
        WHERE
        A.user_sid != 100
        AND A.STATUS != '8'
        GROUP BY
        a.parent_sid
        )allUser on allUser.usersid = A.user_sid
        <where>
            A.user_sid != 100
            <if test="condition.keyword != null">
                and(
                A.account LIKE concat('%', #{condition.keyword}, '%')
                or bba.account_name LIKE concat('%', #{condition.keyword}, '%')
                or A.real_name LIKE concat('%', #{condition.keyword},'%')
                or A.mobile LIKE concat('%', #{condition.keyword}, '%')
                or A.email LIKE concat('%', #{condition.keyword}, '%')
                <choose>
                    <when test="condition.distributorTag != null">
                        or ISNULL(bd.name)
                    </when>
                    <otherwise>
                        or bd.name like concat('%', #{condition.keyword}, '%')
                    </otherwise>
                </choose>
                <if test="condition.statusList != null and condition.statusList.size() > 0">
                    or A.status in
                    <foreach collection="condition.statusList" item="id" index="index" open="(" close=")" separator=",">
                        #{id}
                    </foreach>
                </if>
                <if test="condition.certificationStatusList != null and condition.certificationStatusList.size() > 0">
                    or (C.certification_status in
                    <foreach collection="condition.certificationStatusList" item="id" index="index" open="(" close=")" separator=",">
                        #{id}
                    </foreach>
                    and C.certification_status not in('authFiled','noAuth'))
                    or (A.certification_status in
                    <foreach collection="condition.certificationStatusList" item="id" index="index" open="(" close=")" separator=",">
                        #{id}
                    </foreach>
                    and C.certification_status in('authFiled','noAuth'))
                </if>
                <if test="condition.businessList != null and condition.businessList.size() > 0">
                    or o.industry_type in
                    <foreach collection="condition.businessList" item="id" index="index" open="(" close=")" separator=",">
                        #{id}
                    </foreach>
                </if>
                )
            </if>
            <if test="condition.account != null">
                AND A.account LIKE concat('%', #{condition.account}, '%')
            </if>
            <if test="condition.realName != null">
                AND A.real_name LIKE concat('%', #{condition.realName},'%')
            </if>
            <if test="condition.mobile != null">
                AND A.mobile LIKE concat('%', #{condition.mobile}, '%')
            </if>
            <if test="condition.email != null">
                AND A.email LIKE concat('%', #{condition.email}, '%')
            </if>
            <if test="condition.beTaken != null">
                AND ((A.status = 2 or A.certification_status = 'authing')
                or (C.certification_status ='authing'))
            </if>
            <if test="condition.roleSid != null and condition.roleSid.size() > 0">
                AND ro.role_sid in
                <foreach collection="condition.roleSid" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.certificationStatus != null">
                AND A.certification_status = #{condition.certificationStatus}
            </if>
            <if test="condition.status != null">
                AND A.status =#{condition.status}
            </if>
            <if test="condition.certificationStatus != null">
                AND A.certification_status = #{condition.certificationStatus}
            </if>
            <if test="condition.authType != null">
                AND A.auth_type =#{condition.authType}
            </if>
            <if test="condition.orgName != null">
                AND C.org_name LIKE concat('%', #{condition.orgName}, '%')
            </if>
            <if test="condition.distributorName != null">
                <choose>
                    <when test="condition.distributorName == '直营'">
                        and ISNULL(bd.name)
                    </when>
                    <otherwise>
                        and bd.name like concat('%', #{condition.distributorName}, '%')
                    </otherwise>
                </choose>
            </if>
            <if test="condition.orgSid != null">
                AND C.org_sid = #{condition.orgSid}
            </if>
            <if test="condition.accountNameLike != null">
                AND bba.account_name LIKE concat('%', #{condition.accountNameLike}, '%')
            </if>
            <if test="condition.userSidIn != null">
                AND A.user_sid in
                <foreach item="item" index="index" collection="condition.userSidIn" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.hiddenChild!=null and condition.hiddenChild==1">
                AND A.parent_sid IS NULL
            </if>
            <if test="condition.flag != null">
                AND A.STATUS != '8'
            </if>
        </where>
        group by A.user_sid
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>

    </select>

    <select id="findUserList2" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria">
        select
            A.created_dt AS created_dt
        from sys_m_user A
        where A.user_sid != 100 AND A.STATUS != '8'
    </select>


    <select id="exportUsers" resultType="cn.com.cloudstar.rightcloud.core.pojo.dto.user.UserExcel"
            parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria">
        SELECT
            A.user_sid,
            A.account,
            A.real_name,
            A.email,
            A.mobile,
            A.certification_status,
            A.created_dt,
            A.org_sid,
            IFNULL(A.start_time, ' --') AS start_time,
            IFNULL(A.end_time, ' --') AS end_time,
            GROUP_CONCAT(ROL.role_name) AS role,
            (SELECT COUNT(*) FROM sys_m_user a WHERE a.parent_sid = A.user_sid) AS sub_user_count,
            IFNULL((SELECT GROUP_CONCAT(C.org_name) WHERE C.org_type = 'project'), ' --') AS project_name,
            IFNULL(GROUP_CONCAT(SBE.name), ' --') AS entity_name,
            (CASE
                WHEN A.parent_sid IS NULL THEN '否'
                ELSE '是'
                END) AS iam_sub_user,
            (CASE
                WHEN A.status = '2' THEN '待审核'
                WHEN A.status = '1' THEN '启用'
                ELSE '禁用'
                END) AS status,
            IFNULL(itCode.CODE_DISPLAY, '--') AS industry_name,
            IFNULL(C.org_name, ' --') AS company_names,
            IFNULL(C.org_name, ' --') AS org_name
        FROM sys_m_user A
        LEFT JOIN sys_m_org C ON C.org_sid = A.org_sid
        LEFT JOIN sys_m_user_role ro ON A.user_sid = ro.user_sid
        LEFT JOIN sys_m_role rol ON rol.role_sid = ro.role_sid
        LEFT JOIN sys_bss_entity_user SBU ON SBU.user_sid = A.user_sid
        LEFT JOIN sys_bss_entity SBE ON SBE.id = SBU.bss_entity_id
        LEFT JOIN sys_m_code itCode ON C.industry_type = itCode.CODE_VALUE
             AND itCode.CODE_CATEGORY = 'INDUSTRY_TYPE'
        LEFT JOIN biz_billing_account bba ON bba.org_sid = A.org_sid
        LEFT JOIN biz_distributor bd ON bd.id = bba.distributor_id
        <where>
            A.user_sid != 100 AND  A.STATUS != '8'
            <if test="condition.keyword != null">
                and(
                A.account LIKE concat('%', #{condition.keyword}, '%')
                or bba.account_name LIKE concat('%', #{condition.keyword}, '%')
                or A.real_name LIKE concat('%', #{condition.keyword},'%')
                or A.mobile LIKE concat('%', #{condition.keyword}, '%')
                or A.email LIKE concat('%', #{condition.keyword}, '%')
                <choose>
                    <when test="condition.distributorTag != null">
                        or ISNULL(bd.name)
                    </when>
                    <otherwise>
                        or bd.name like concat('%', #{condition.keyword}, '%')
                    </otherwise>
                </choose>
                <if test="condition.statusList != null and condition.statusList.size() > 0">
                    or A.status in
                    <foreach collection="condition.statusList" item="id" index="index" open="(" close=")" separator=",">
                        #{id}
                    </foreach>
                </if>
                <if test="condition.certificationStatusList != null and condition.certificationStatusList.size() > 0">
                    or (C.certification_status in
                    <foreach collection="condition.certificationStatusList" item="id" index="index" open="(" close=")" separator=",">
                        #{id}
                    </foreach>
                    and C.certification_status not in('authFiled','noAuth'))
                    or (A.certification_status in
                    <foreach collection="condition.certificationStatusList" item="id" index="index" open="(" close=")" separator=",">
                        #{id}
                    </foreach>
                    and C.certification_status in('authFiled','noAuth'))
                </if>
                <if test="condition.businessList != null and condition.businessList.size() > 0">
                    or o.industry_type in
                    <foreach collection="condition.businessList" item="id" index="index" open="(" close=")" separator=",">
                        #{id}
                    </foreach>
                </if>
                )
            </if>
            <if test="condition.account != null">
                AND A.account LIKE concat('%', #{condition.account}, '%')
            </if>
            <if test="condition.realName != null">
                AND A.real_name LIKE concat('%', #{condition.realName},'%')
            </if>
            <if test="condition.mobile != null">
                AND A.mobile LIKE concat('%', #{condition.mobile}, '%')
            </if>
            <if test="condition.email != null">
                AND A.email LIKE concat('%', #{condition.email}, '%')
            </if>
            <if test="condition.beTaken != null">
                AND ((A.status = 2 or A.certification_status = 'authing')
                or (C.certification_status ='authing'))
            </if>
            <if test="condition.roleSid != null and condition.roleSid.size() > 0">
                AND ro.role_sid in
                <foreach collection="condition.roleSid" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.certificationStatus != null">
                AND A.certification_status = #{condition.certificationStatus}
            </if>
            <if test="condition.status != null">
                AND A.status =#{condition.status}
            </if>
            <if test="condition.certificationStatus != null">
                AND A.certification_status = #{condition.certificationStatus}
            </if>
            <if test="condition.authType != null">
                AND A.auth_type =#{condition.authType}
            </if>
            <if test="condition.orgName != null">
                AND C.org_name LIKE concat('%', #{condition.orgName}, '%')
            </if>
            <if test="condition.distributorName != null">
                <choose>
                    <when test="condition.distributorName == '直营'">
                        and ISNULL(bd.name)
                    </when>
                    <otherwise>
                        and bd.name like concat('%', #{condition.distributorName}, '%')
                    </otherwise>
                </choose>
            </if>
            <if test="condition.orgSid != null">
                AND C.org_sid = #{condition.orgSid}
            </if>
            <if test="condition.accountNameLike != null">
                AND bba.account_name LIKE concat('%', #{condition.accountNameLike}, '%')
            </if>
            <if test="condition.userSidIn != null">
                AND A.user_sid in
                <foreach item="item" index="index" collection="condition.userSidIn" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.hiddenChild!=null and condition.hiddenChild==1">
                AND A.parent_sid IS NULL
            </if>
        </where>
        group by A.user_sid
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>

    </select>

    <select id="countFindAllPlatformUsers" resultType="java.lang.Integer"
        parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria">
        select
        count(distinct A.user_sid)
        from sys_m_user A
        where A.user_sid != 100 and A.status != '8' and A.parent_sid is null
    </select>

    <select id="countFindAllPlatformUsers2" resultType="java.lang.Integer"
        parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria">
        select
            count(distinct A.user_sid)
        from sys_m_user A
        where A.user_sid != 100 and A.status != '8';
    </select>

    <select id="findByOrgIds" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria">
        select
        <include refid="Base_Column_List_Alias"/>
        from sys_m_user A, sys_m_user_org B
        where A.user_sid != 100 and A.status != '8' and A.user_sid = B.user_sid
        and B.org_sid in
        <foreach collection="condition.orgSidList" open="(" separator="," close=")" item="item">
            #{item,jdbcType=BIGINT}
        </foreach>
        <if test="condition.searchFilter != null">
            and (
            A.real_name like concat ('%', #{condition.searchFilter} ,'%')
            or
            A.account like concat ('%', #{condition.searchFilter} ,'%')
            or
            A.email like concat ('%', #{condition.searchFilter} ,'%')
            or
            A.mobile like concat ('%', #{condition.searchFilter} ,'%')
            )
        </if>
        <if test="condition.status != null">
            and status = #{condition.status}
        </if>
        group by A.user_sid

    </select>
    <select id="countByOrgIds" resultType="java.lang.Integer"
        parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria">
        select
        count(DISTINCT A.user_sid)
        from sys_m_user A, sys_m_user_org B
        where A.user_sid != 100 and A.status != '8' and A.user_sid = B.user_sid
        and B.org_sid in
        <foreach collection="condition.orgSidList" open="(" separator="," close=")" item="item">
            #{item,jdbcType=BIGINT}
        </foreach>
        <if test="condition.searchFilter != null">
            and (
            A.real_name like concat ('%', #{condition.searchFilter} ,'%')
            or
            A.account like concat ('%', #{condition.searchFilter} ,'%')
            or
            A.email like concat ('%', #{condition.searchFilter} ,'%')
            or
            A.mobile like concat ('%', #{condition.searchFilter} ,'%')
            )
        </if>
    </select>
    <select id="findJoinedUserByOrgId" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria">
        select
        <include refid="Base_Column_List_Alias"/>
        from sys_m_user A, sys_m_user_org B
        where A.user_sid != 100 and A.status != '8' and A.user_sid = B.user_sid and B.org_sid=#{condition.orgSid}
        <if test="condition.searchFilter != null">
            and (
            A.real_name like concat ('%', #{condition.searchFilter} ,'%')
            or
            A.account like concat ('%', #{condition.searchFilter} ,'%')
            or
            A.email like concat ('%', #{condition.searchFilter} ,'%')
            or
            A.mobile like concat ('%', #{condition.searchFilter} ,'%')
            )
        </if>
        <if test="condition.accountLike != null">
            and A.account like concat ('%', #{condition.accountLike} ,'%')
        </if>
        <if test="condition.emailLike != null">
            and A.email like concat ('%', #{condition.emailLike} ,'%')
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <select id="selectUserByOrgIds" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria">
        select
        <include refid="Base_Column_List_Alias"/>
        from sys_m_user A, sys_m_user_org o
        where A.user_sid=o.user_sid and A.status != '8'
        <if test="condition.orgSidList != null">
            and o.org_sid in
            <foreach collection="condition.orgSidList" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="condition.orgSid != null">
            and o.org_sid = #{condition.orgSid}
        </if>
    </select>
    <select id="findUserById" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List_Alias"/>
        , B.org_name as company_name
        , B.application_scenario as application_scenario
        , B.personnel_size as personnel_size
        , B.industry_type as industry_type
        , B.solution
        from sys_m_user A
        LEFT JOIN sys_m_org B on A.company_id = B.org_sid
        where A.user_sid = #{userSid}
    </select>
    <update id="resetOrgSid" parameterType="list">
        update sys_m_user set org_sid = null
        where user_sid in
        <foreach collection="userIds" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
    </update>


    <select id="findUserByMobile" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List_Alias"/>
        from sys_m_user A
        where A.mobile = #{mobile} and A.status != 8 and A.status != 4 and (A.business_tag is null or A.business_tag NOT LIKE '%preOpen%');
    </select>
    <select id="findUserByMobileHash" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List_Alias"/>
        from sys_m_user A
        where A.mobile_hash = #{mobile} and A.status != 8 and A.status != 4 and (A.business_tag is null or A.business_tag NOT LIKE '%preOpen%');
    </select>
    <select id="findUserByEmail" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List_Alias"/>
        from sys_m_user A
        where A.email = #{email} and A.status != 8 and A.status != 4 and (A.business_tag is null or A.business_tag NOT LIKE '%preOpen%');
    </select>
    <select id="findUserByEmailHash" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List_Alias"/>
        from sys_m_user A
        where A.email_hash = #{email} and A.status != 8 and A.status != 4 and (A.business_tag is null or A.business_tag NOT LIKE '%preOpen%');
    </select>
    <select id="findUserByAccount" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List_Alias"/>
        from sys_m_user A
        where A.account = #{account} and status not in(8,4)
    </select>
    <select id="findUserByAccountV" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List_Alias"/>
        from sys_m_user A
        where A.account = #{account};
    </select>


    <select id="findUserByAccountNormal" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List_Alias"/>
        from sys_m_user A
        where A.account = #{account} and status = 1;
    </select>

    <select id="findAdUser" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Alias"/>
        from sys_m_user A
        where A.auth_type = 'ad'
    </select>

    <select id="findUserByRoleId" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria">
        select
        <include refid="Base_Column_List_Alias"/>
        from sys_m_user A, sys_m_user_role B
        where A.user_sid = B.user_sid and B.role_sid = #{condition.roleSid} and A.status != '8' and A.user_sid != 100
        <if test="condition.orgSid != null">
            and B.org_sid = #{condition.orgSid}
        </if>
        <if test="condition.searchFilter != null">
            and (
            A.real_name_hash = #{condition.searchHash}
            or
            A.account like concat ('%', #{condition.searchFilter} ,'%')
            or
            A.email_hash = #{condition.searchHash}
            or
            A.mobile_hash = #{condition.searchHash}
            )
        </if>
        group by A.user_sid
    </select>

    <select id="findAll" resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from sys_m_user limit 10
    </select>

    <select id="findUserUnderOrg" resultMap="BaseResultMap" parameterType="long">
        select
        <include refid="Base_Column_List_Alias"/>
        from sys_m_user user
        where 1=1
          AND (EXISTS (SELECT 1 FROM sys_m_org WHERE (org_sid = #{orgSid}
           OR tree_path LIKE CONCAT('/'
            , #{orgSid}
            , '/%'))
          AND org_sid = user.company_id))

    </select>

    <select id="findUserByDataScope" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria">
        select
        <include refid="Base_Column_List_Alias"/>
        from sys_m_user A, sys_m_user_role B, sys_m_role C
        where A.user_sid = B.user_sid and A.status != '8' and B.role_sid = C.role_sid and C.data_scope =
        #{condition.dataScope}
        <if test="condition.orgSid != null">
            and B.org_sid = #{condition.orgSid}
        </if>
        <if test="condition.searchFilter != null">
            and (
            A.real_name like concat ('%', #{condition.searchFilter} ,'%')
            or
            A.account like concat ('%', #{condition.searchFilter} ,'%')
            or
            A.email like concat ('%', #{condition.searchFilter} ,'%')
            or
            A.mobile like concat ('%', #{condition.searchFilter} ,'%')
            )
        </if>
        group by A.user_sid
    </select>
    <select id="findAllUsersWithoutAuth" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from sys_m_user
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>

    </select>

    <select id="findAdminstrators" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_Alias"/>
        from sys_m_user A
        where 1=1
          AND A.user_sid in (select user_sid from sys_m_user_role where role_sid=301)
    </select>

    <select id="findAdminstratorsByEntityId" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List_Alias"/>
        ,su.bss_entity_id entityIds
        FROM
        sys_m_user A  left join sys_bss_entity_user su on A.user_sid = su.user_sid
        WHERE
        1 = 1
        AND A.user_sid IN (
        SELECT
        user_sid
        FROM
        sys_m_user_role
        WHERE
        role_sid IN ( SELECT role_sid FROM sys_m_role WHERE data_scope = '1' ))
        <if test="entityId != null and entityId != ''">
            and su.bss_entity_id = #{entityId}
        </if>
    </select>

    <select id="assertIamForceResetPwd" resultType="boolean" parameterType="long">
        select ifnull(forceResetPwd, 0) from sys_m_user where user_sid = #{userSid}
    </select>
    <update id="updateIamForceResetPwd" parameterType="long">
        update sys_m_user
        set forceResetPwd = 0
        where user_sid = #{userSid};
    </update>
    <select id="assertIam" resultType="boolean" parameterType="long">
        select distinct ifnull((select parent_sid is not null from sys_m_user where user_sid = #{userSid}), 0)
        from sys_m_user;
    </select>
<!--    <select id="selectPswPolicyByOrgSid" resultType="map" parameterType="long">-->
<!--        select *-->
<!--        from sys_m_password_policy-->
<!--        where org_sid = #{orgSid};-->
<!--    </select>-->

    <select id="selectUserAccessPolicy" resultType="java.lang.Long" parameterType="long">
        SELECT distinct b.policy_sid
        FROM (SELECT user_sid, group_sid FROM sys_m_user_group WHERE user_sid = #{userSid}) a
                 INNER JOIN sys_m_policy_group b ON a.group_sid = b.group_sid
        UNION
        SELECT policy_sid
        FROM sys_m_policy_user
        WHERE user_sid = #{userSid}
    </select>

    <select id="selectUserProjectIds" resultType="java.lang.Long" parameterType="long">
        SELECT distinct org_sid
        FROM sys_m_user_org
        WHERE user_sid = #{userSid}
    </select>
    <update id="updateAuthInfo"
        parameterType="cn.com.cloudstar.rightcloud.core.pojo.dto.user.User">
        update sys_m_user
        <set>
            <if test="realName != null and realName !=''">
                real_name = #{realName},
            </if>
            <if test="realNameHash != null">
                real_name_hash = #{realNameHash},
            </if>
            <if test='updateFlag != null and updateFlag == "Y"'>
                real_name = '',
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="version != null ">
                version = #{version},
            </if>
            <if test="idCardReverse != null and idCardReverse !=''">
                id_card_reverse = #{idCardReverse},
            </if>
            <if test="idCardFront != null and idCardFront !=''">
                id_card_front = #{idCardFront},
            </if>
            <if test="certificationStatus != null and certificationStatus !=''">
                certification_status = #{certificationStatus},
            </if>
            <if test="ccspMac != null and ccspMac !=''">
                ccsp_mac = #{ccspMac},
            </if>
            <if test="authId != null and authId !=''">
                auth_id = #{authId},
            </if>
            <if test="authName != null and authName !=''">
                auth_name = #{authName},
            </if>
            <if test="authIdHash != null and authIdHash !=''">
                auth_id_hash = #{authIdHash},
            </if>
            updated_by = #{updatedBy},
            updated_dt = #{updatedDt}
        </set>
        where user_sid = #{userSid}
    </update>
    <select id="countInvitation" resultType="java.lang.Integer" parameterType="long">
        SELECT count(extend_sid)
        FROM sys_m_mgt_obj_ext
        WHERE attr_value = #{userSid}
    </select>
    <select id="validateUserExist" resultType="java.lang.Integer" parameterType="long">
        SELECT count(a.user_sid)
        FROM `sys_m_user` a
                 LEFT JOIN sys_m_user_role b
                           ON a.user_sid = b.user_sid AND role_sid IN (401, 402, 403)
        WHERE a.user_sid = #{userSid}
          AND a.`status` = 1;
    </select>
    <select id="selectByOrgList" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List_Alias"/>
        from sys_m_user A left join sys_m_user_role ro on A.user_sid = ro.user_sid
        left join sys_m_org smo on A.org_sid=smo.org_sid and smo.org_type='company'
        where
        ((A.certification_status ='authing' or A.status= 2)
        or (smo.certification_status ='authing' or smo.status= 2))
        and ro.role_sid in ('302', '306') and A.status != '8'
        <if test="condition.ogrSidList!=null and condition.ogrSidList.size() > 0">
            and A.org_sid in
            <foreach collection="condition.ogrSidList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="selectAllUserByUserSid" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select distinct
            <include refid="Base_Column_List"/>
        from (
                 select
                    <include refid="Base_Column_List"/>
                 from sys_m_user w
                 where w.user_sid = #{userSid}
                 union all
                 select
                    <include refid="Base_Column_List"/>
                 from sys_m_user o
                 where o.parent_sid = #{userSid}
                 union all
                 select
                    <include refid="Base_Column_List"/>
                 from sys_m_user q
                 where q.parent_sid = (
                     select e.parent_sid
                     from sys_m_user e
                     where e.user_sid = #{userSid})
                 union all
                 select
                    <include refid="Base_Column_List"/>
                 from sys_m_user q
                 where q.user_sid = (
                     select e.parent_sid
                     from sys_m_user e
                     where e.user_sid = #{userSid})) a
    </select>
    <select id="countRegistUsers" resultType="java.lang.Integer">
        select count(1)
        from sys_m_user smu
        where smu.status = 1
          and smu.user_type = "03"
          and smu.org_sid is not NULL
          and smu.parent_sid is NULL
    </select>
    <select id="findByRoleType" resultMap="BaseResultMap">
        select distinct
        <include refid="Base_Column_List_Alias"/>
        from sys_m_user A
        inner join sys_m_user_role ur on A.user_sid = ur.user_sid
        inner join sys_m_role r on ur.role_sid = r.role_sid
        where r.role_type = #{roleType} and A.status = 1
    </select>

    <select id="findRolesByAccount"  resultType="cn.com.cloudstar.rightcloud.common.pojo.Role">
        SELECT
            r.role_sid,
            r.role_code,
            r.role_desc,
            r.role_name,
            r.data_scope,
            r.role_type,
            r.module_category,
            r.STATUS
        FROM
            sys_m_user_role ur
                LEFT JOIN sys_m_role r ON ur.role_sid = r.role_sid
                left join sys_m_user u on ur.user_sid = u.user_sid
        WHERE 1=1
        <if test="account != null and account != ''">
            and u.account = #{account}
        </if>

    </select>

    <select id="findUsersByModuleSids" resultMap="BaseResultMap">

        select distinct
        <include refid="Base_Column_List_Alias"/>
        from sys_m_user A
        inner join sys_m_user_role ur on A.user_sid = ur.user_sid
        inner join sys_m_role_module rm on ur.role_sid = rm.role_sid
        inner join sys_bss_entity_user u on A.user_sid = u.user_sid
        where rm.module_sid in
        <foreach collection="modules" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        <if test="entityId != null and entityId != ''">
            and u.bss_entity_id = #{entityId}
        </if>
        AND  <![CDATA[A.user_type  <> 4]]>
        order by convert(A.real_name using gbk) asc
    </select>

    <select id="findAdministratorByOrgSid" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from sys_m_user
        where org_sid = #{orgSid} and parent_sid is null
    </select>
    <select id="selectAll" resultType="java.util.Map">
        select *
        from sys_m_user
    </select>

    <select id="findCertificationStatusUsers" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List_Alias"/>
        ,C.org_name AS org_name,
        IF( ifnull( A.parent_sid, 0 ) > 0, 1, 0 ) iam,
        bba.id AS accountId,
        bba.account_name,
        bd.NAME distributor_name,
        bba.entity_name AS entityName,
        IFNULL( allUser.subUserCount, 0 ) AS subUserCount,
        itCode.CODE_VALUE AS industry
        from sys_m_user A
        LEFT JOIN sys_m_org C ON C.org_sid = A.org_sid
        LEFT JOIN sys_m_user_role ro ON A.user_sid = ro.user_sid
        LEFT JOIN sys_m_code itCode ON C.industry_type = itCode.CODE_VALUE
        AND itCode.CODE_CATEGORY = 'INDUSTRY_TYPE'
        LEFT JOIN biz_billing_account bba ON bba.org_sid = A.org_sid
        LEFT JOIN biz_distributor bd ON bd.id = bba.distributor_id
        LEFT JOIN ( SELECT
        a.parent_sid AS usersid,
        count( a.parent_sid ) subUserCount
        FROM
        sys_m_user a
        LEFT JOIN sys_m_user_role ro ON A.user_sid = ro.user_sid
        LEFT JOIN sys_m_org C ON c.org_sid = a.org_sid
        WHERE
        A.user_sid != 100
        AND A.STATUS != '8'
        GROUP BY
        a.parent_sid
        )allUser on allUser.usersid = A.user_sid
        where A.user_sid != 100
        <if test="condition.keyword != null">
            and(
            A.account LIKE concat('%', #{condition.keyword}, '%')
            or bba.account_name LIKE concat('%', #{condition.keyword}, '%')
            <choose>
                <when test="condition.keywordHash != null">
                    or A.real_name_hash = #{condition.keywordHash}
                    or A.mobile_hash = #{condition.keywordHash}
                    or A.email_hash = #{condition.keywordHash}
                </when>
                <otherwise>
                    or A.real_name LIKE concat('%', #{condition.keyword},'%')
                    or A.mobile LIKE concat('%', #{condition.keyword}, '%')
                    or A.email LIKE concat('%', #{condition.keyword}, '%')
                </otherwise>
            </choose>
            <choose>
                <when test="condition.distributorTag != null">
                    or ISNULL(bd.name)
                </when>
                <otherwise>
                    or bd.name like concat('%', #{condition.keyword}, '%')
                </otherwise>
            </choose>
            <if test="condition.statusList != null and condition.statusList.size() > 0">
                or A.status in
                <foreach collection="condition.statusList" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="condition.certificationStatusList != null and condition.certificationStatusList.size() > 0">
                or (C.certification_status in
                <foreach collection="condition.certificationStatusList" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
                and C.certification_status not in('authFiled','noAuth'))
                or (A.certification_status in
                <foreach collection="condition.certificationStatusList" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
                and C.certification_status in('authFiled','noAuth'))
            </if>
            <if test="condition.businessList != null and condition.businessList.size() > 0">
                or o.industry_type in
                <foreach collection="condition.businessList" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            )
        </if>
        <if test="condition.account != null">
            AND A.account LIKE concat('%', #{condition.account}, '%')
        </if>
        <if test="condition.accountNameLike != null">
            AND bba.account_name LIKE concat('%', #{condition.accountNameLike}, '%')
        </if>
        <if test="condition.realName != null">
            AND A.real_name LIKE concat('%', #{condition.realName},'%')
        </if>
        <if test="condition.realNameHash != null">
            AND A.real_name_hash = #{condition.realNameHash}
        </if>
        <if test="condition.mobile != null">
            AND A.mobile LIKE concat('%', #{condition.mobile}, '%')
        </if>
        <if test="condition.mobileHash != null">
            AND A.mobile_hash = #{condition.mobileHash}
        </if>
        <if test="condition.email != null">
            AND A.email LIKE concat('%', #{condition.email}, '%')
        </if>
        <if test="condition.emailHash != null">
            AND A.email_hash = #{condition.emailHash}
        </if>
        <if test="condition.userSidList != null and condition.userSidList.size() > 0">
            AND A.user_sid in
            <foreach collection="condition.userSidList" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="condition.beTaken != null">
            AND ((A.status = 2 or A.certification_status = 'authing')
            or (C.certification_status ='authing'))
        </if>
        <if test="condition.roleSid != null and condition.roleSid.size() > 0">
            AND ro.role_sid in
            <foreach collection="condition.roleSid" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="condition.certificationStatus != null">
            AND ((C.certification_status=#{condition.certificationStatus} and C.certification_status
            not in('authFiled','noAuth')
            )

            or (A.certification_status=#{condition.certificationStatus}
            and C.certification_status='noAuth'))

        </if>
        <if test="condition.status != null">
            AND A.status =#{condition.status}
        </if>
        <if test="condition.statusNotIn != null">
            AND A.status not in <foreach item="item" index="index" collection="condition.statusNotIn"
            open="(" separator="," close=")">
            #{item}
        </foreach>
        </if>

        <if test="condition.authType != null">
            AND A.auth_type =#{condition.authType}
        </if>
        <if test="condition.orgName != null">
            AND C.org_name LIKE concat('%', #{condition.orgName}, '%')
        </if>
        <if test="condition.distributorName != null">
            <choose>
                <when test="condition.distributorName == '直营'">
                    and ISNULL(bd.name)
                </when>
                <otherwise>
                    and bd.name like concat('%', #{condition.distributorName}, '%')
                </otherwise>
            </choose>
        </if>
        <if test="condition.orgSid != null">
            AND C.org_sid = #{condition.orgSid}
        </if>
        <if test="condition.orgSidIsNull != null and condition.orgSidIsNull == true">
                AND C.org_sid is null
            </if>
            <if test="condition.statusNotIn != null">
                AND A.status not in
                <foreach item="item" index="index" collection="condition.statusNotIn" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.orgSidIsNull != null and condition.orgSidIsNull == true">
            AND C.org_sid is null
        </if>
        <if test="condition.hiddenChild!=null and condition.hiddenChild==1">
            AND A.parent_sid IS NULL
        </if>
        <if test="condition.delStatus !=null">
            and A.status!=#{condition.delStatus}
        </if>
        <if test="condition.userSid !=null">
            and A.user_sid =#{condition.userSid}
        </if>
        <if test="condition.industry != null">
            and itCode.CODE_VALUE like concat('%', #{condition.industry}, '%')
        </if>
        <if test="condition.accountIds != null and condition.accountIds.size() > 0">
            AND bba.id in
            <foreach collection="condition.accountIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="condition.entityId != null and condition.entityId != ''">
            AND bba.entity_id = #{condition.entityId}
        </if>
        group by A.user_sid
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>

    <select id="selectUserByRoleId" resultType="cn.com.cloudstar.rightcloud.core.pojo.dto.user.SkipCCSPUser">
        select
        distinct
        <include refid="Base_Column_List_Alias"/>
        from sys_m_user_role r
        inner join sys_m_user A on r.user_sid = A.user_sid
        where r.role_sid = #{roleId} and A.status=1
    </select>

    <update id="updateById" parameterType="cn.com.cloudstar.rightcloud.core.pojo.dto.user.User">
        update sys_m_user
        set updated_by         = #{updatedBy},
            updated_dt         = #{updatedDt},
            version            = #{version},
            navigation_confirm = #{navigationConfirm}
        where user_sid = #{userSid}
    </update>

    <resultMap type="cn.com.cloudstar.rightcloud.oss.common.pojo.SysMUserPasswordHistory" id="PasswordHistoryBaseResultMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="INTEGER"/>
        <result property="password" column="password" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdDt" column="created_dt" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedDt" column="updated_dt" jdbcType="TIMESTAMP"/>
        <result property="version" column="version" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Password_History_Base_Column_List">
        id,
        user_id,
        password,
        created_by,
        created_dt,
        updated_by,
        updated_dt,
        version
    </sql>

    <sql id="Password_HistoryExample_Where_Clause">
        <where>
            <if test="condition.id != null">
                and id = #{condition.id}
            </if>
            <if test="condition.userId != null">
                and user_id = #{condition.userId}
            </if>
            <if test="condition.password != null">
                and password = #{condition.password}
            </if>
            <if test="condition.createdBy != null">
                and created_by = #{condition.createdBy}
            </if>
            <if test="condition.createdDt != null">
                and created_dt = #{condition.createdDt}
            </if>
            <if test="condition.updatedBy != null">
                and updated_by = #{condition.updatedBy}
            </if>
            <if test="condition.updatedDt != null">
                and updated_dt = #{condition.updatedDt}
            </if>
            <if test="condition.version != null">
                and version = #{condition.version}
            </if>
            <if test="condition.startTime != null and condition.endTime != null">
                and created_dt between #{condition.startTime} and #{condition.endTime}
            </if>
        </where>
    </sql>

    <select id="selectByCriteria" resultMap="BaseResultMap"
            parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from sys_m_user
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>

    <insert id="insertSelectivePasswordHistory" keyProperty="id" useGeneratedKeys="true"
        parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.SysMUserPasswordHistory">
        insert into sys_m_user_password_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="password != null">
                password,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="createdDt != null">
                created_dt,
            </if>
            <if test="updatedBy != null">
                updated_by,
            </if>
            <if test="updatedDt != null">
                updated_dt,
            </if>
            <if test="version != null">
                version
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="userId != null">
                #{userId},
            </if>
            <if test="password != null">
                #{password},
            </if>
            <if test="createdBy != null">
                #{createdBy},
            </if>
            <if test="createdDt != null">
                #{createdDt},
            </if>
            <if test="updatedBy != null">
                #{updatedBy},
            </if>
            <if test="updatedDt != null">
                #{updatedDt},
            </if>
            <if test="version != null">
                #{version}
            </if>
        </trim>
    </insert>

    <select id="selectPasswordHistoryByParams" resultMap="PasswordHistoryBaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Password_History_Base_Column_List"/>
        from sys_m_user_password_history
        <if test="_parameter != null">
            <include refid="Password_HistoryExample_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>

    <select id="selectPasswordHistoryByPrimaryKey" resultMap="PasswordHistoryBaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Password_History_Base_Column_List"/>
        from sys_m_user_password_history
        where id = #{id}
    </select>
    <select id="selectAdminUser" resultType="cn.com.cloudstar.rightcloud.oss.common.pojo.User">
        SELECT
        <include refid="Base_Column_List_Alias"/>
        FROM
        sys_m_user A
        LEFT JOIN sys_m_user_role B ON B.user_sid = A.user_sid
        WHERE
        (B.role_sid = 301 or A.user_sid = 100)
    </select>

    <select id="toCheckUserCount" resultType="int">
        select count(1)
        from sys_m_user where( user_sid=#{rootUser}
            or parent_sid=(SELECT parent_sid from sys_m_user WHERE user_sid=#{rootUser})
            OR parent_sid=#{rootUser}
            ) and user_sid = #{userSid}
    </select>

    <select id="checkTicketByTicketNo" resultType="int">
        SELECT count(1)
        FROM sys_m_ticket WHERE ticket_user_id in (SELECT user_sid FROM sys_m_user WHERE org_sid=#{orgSid})
                            AND `status` !='05'  and  ticket_no=#{ticketNo};
    </select>

    <select id="selectAccountByUserSidList" resultType="java.lang.String" parameterType="long">
        select distinct
        account
        from sys_m_user
        where user_sid in
        <foreach collection="userSidList" open="(" close=")" item="userSid" separator=",">
            #{userSid}
        </foreach>
    </select>

    <select id="checkTicketByTicketNoAndAllocUser" resultType="int">
        SELECT count(1) from sys_m_ticket WHERE allocation_ticket_user_id=#{userSid} AND ticket_no=#{ticketNo}
    </select>

    <select id="selectPassword" resultType="cn.com.cloudstar.rightcloud.core.pojo.dto.user.User">
        SELECT * FROM sys_m_user WHERE account = #{account}
    </select>

    <select id="selectUser" resultType="cn.com.cloudstar.rightcloud.oss.module.account.bean.model.User">
        select
            distinct
            <include refid="Base_Column_List_Alias"/>
            ,A.authorize_tag
        from sys_m_user A
                 left join sys_m_user_role C on A.user_sid = C.user_sid
        where A.user_sid=#{userSid}
    </select>

    <select id="selectUserByUserId" resultType="cn.com.cloudstar.rightcloud.core.pojo.dto.user.User">
        select
        distinct
        <include refid="Base_Column_List_Alias"/>
        ,A.authorize_tag
        from sys_m_user A
        where A.user_sid=#{userSid}
    </select>
    <update id="updateUseDay" parameterType="int">
        update sys_m_user set pwd_end_time = date_add(pwd_end_time,interval #{num} day)
    </update>
    <update id="updateUserDayBy" parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria">
        update sys_m_user
        set pwd_end_time = date_add(pwd_end_time, interval #{condition.num} day)
        where user_sid = #{condition.userSid}
    </update>

    <select id="selectSubUser" resultType="cn.com.cloudstar.rightcloud.core.pojo.dto.user.User">
        select
            distinct
            A.user_sid, A.user_type, A.user_code, A.account, A.password,  A.real_name, A.sex, A.email,
            A.mobile, A.title, A.company_id, A.project_id, A.org_sid, A.status, A.remark, A.error_count, A.last_login_time,
            A.last_login_ip, A.start_time, A.end_time, A.service_limit_quantity, A.apply_reason, A.sms_max,
            A.uuid, A.skin_theme, A.auth_id, A.auth_type, A.created_by, A.created_dt, A.updated_by, A.updated_dt,
            A.version, A.open_id, A.avatar_url, A.province, A.city, A.country, A.wechat_name, A.domain_id, iam_id,
            A.password_expires_at, A.forceResetPwd, A.default_project_id, A.last_project_id, A.pwd_strength, A.parent_sid, A.certification_status
                ,C.role_sid
        from sys_m_user A
                 left join sys_m_user_role C on A.user_sid = C.user_sid
        where A.parent_sid=#{parentSid}
    </select>


    <select id="selectMobile" resultType="java.lang.String">
        SELECT DISTINCT mobile FROM sys_m_user where mobile is not null
    </select>

    <select id="selectUserByFileId" resultType="cn.com.cloudstar.rightcloud.core.pojo.dto.user.User">
        select
        <include refid="Base_Column_List_Alias"/>
        from sys_m_user A
        where A.id_card_front = #{fileNum} or A.id_card_reverse = #{fileNum}
    </select>

    <select id="countByFileId" resultType="java.lang.Long">
        select
            count(A.user_sid)
        from sys_m_user A
        where A.id_card_front = #{fileNum} or A.id_card_reverse = #{fileNum}
    </select>

    <select id="selectSystemUser" resultType="cn.com.cloudstar.rightcloud.core.pojo.dto.user.User">
        SELECT
        <include refid="Base_Column_List_Alias"/>
        FROM sys_m_user A
        WHERE A.user_sid != 100
            AND A.status != '8'
            AND A.parent_sid is null
            AND A.status = '2'
            AND A.user_sid in
        <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <select id="selectSystemUserCount" resultType="java.lang.Long">
        SELECT
        COUNT(A.user_sid)
        FROM sys_m_user A
        where A.user_sid != 100
        AND A.status != '8'
        AND A.parent_sid is null
        AND A.status = '2'
        AND A.user_sid in
        <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectBatchUser" resultType="cn.com.cloudstar.rightcloud.core.pojo.dto.user.User">
        SELECT
        <include refid="Base_Column_List_Alias"/>
        FROM sys_m_user A
        WHERE A.user_sid in
        <foreach collection="userIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <update id="updateByUserIds" parameterType="list">
        UPDATE sys_m_user
        SET policy_agree_sign = 0,
        policy_agree_time = now()
        where user_sid in
        <foreach collection="userIds"  item="userId" index="index" separator="," open="(" close=")">
            #{userId}
        </foreach>
    </update>

    <select id="findAllUser" resultType="cn.com.cloudstar.rightcloud.core.pojo.dto.user.User">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from sys_m_user
    </select>
    <select id="selectUserRoleByUserSid" resultType="cn.com.cloudstar.rightcloud.oss.common.pojo.Role">
        SELECT
            smr.*
        FROM
            sys_m_role smr
                LEFT JOIN sys_m_user_role sur ON smr.role_sid  = sur.role_sid
        WHERE
            sur.user_sid = #{userSid}
    </select>
    <select id="getCertificationStatusByUserSid" resultType="cn.com.cloudstar.rightcloud.oss.module.account.bean.model.UserCertificationStatusVO">
        SELECT u.user_sid ,
               u.account ,
               u.certification_status as auth_certification_status ,
               o.certification_status as company_certification_status
        FROM sys_m_user u
        left join sys_m_user_org  uo on uo.user_sid = u.user_sid
        left join sys_m_org o on o.org_sid = uo.org_sid
        where u.user_sid = #{userSid}
        GROUP BY u.user_sid
    </select>

    <select id="findByStatus" parameterType="map" resultMap="BaseResultMap">
        select * from sys_m_user where status=#{status}
    </select>

    <select id="findByUserIds" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria">
        select
        <include refid="Base_Column_List_Alias"/>
        from sys_m_user A where A.status != '8'
        <if test="condition.userIdList != null">
            and A.user_sid in
            <foreach collection="condition.userIdList" open="(" separator="," close=")" item="item">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
        group by A.user_sid

    </select>

    <select id="checkTicket" resultType="int">
        SELECT
        count(1)
        FROM
        sys_m_ticket A
        WHERE
        A.ticket_user_id IN ( SELECT user_sid FROM sys_m_user B WHERE B.user_sid = #{rootUserSid}
        <if test="#{parentSid} != null">
            OR B.parent_sid = #{parentSid} or B.user_sid=#{rootUserSid}
        </if>
        OR B.parent_sid = #{parentSid} )

        AND A.id=#{ticketId}
    </select>

    <select id="findUserExcludeCurrentUserByMobile" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List_Alias"/>
        from sys_m_user A
        where A.mobile = #{mobile} and A.account != #{account}  and A.status not in(8,4);
    </select>

    <select id="findUserExcludeCurrentUserByEmail" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List_Alias"/>
        from sys_m_user A
        where A.email = #{email} and A.account != #{account} and A.status not in(8,4);
    </select>
    <select id="selectAllByParams" resultMap="BaseResultMap"
            parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from sys_m_user
        <if test="_parameter != null">
            <include refid="Example_Where_Clause2"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>

    </select>
    <update id="updateByStatusCount">
        update sys_m_user
        <set>
            <if test="status!=null">
                status = #{status},
            </if>
            <if test="count!=null">
                error_count = #{count}
            </if>
        </set>
        where user_sid = #{userId}
    </update>
    <update id="updateAuthIdHash">
        update sys_m_user
        <if test="hashAuthId != null">
            set auth_id_hash = #{hashAuthId}
        </if>
        <if test="hashAuthId == null">
            set auth_id_hash =null
        </if>
        where user_sid =#{userSid}
    </update>


    <select id="findAllUserAccount" resultType="cn.com.cloudstar.rightcloud.oss.ccsp.CCSPRegisterUser">
        select account,created_dt from sys_m_user
    </select>

    <select id="selectUserSidByDataScope" resultType="java.lang.Long">
        select
        distinct
            A.user_sid
        from sys_m_user_role r
        inner join sys_m_user A on r.user_sid = A.user_sid
        where A.status=1 AND r.role_sid in(select role_sid from sys_m_role where data_scope = 1)
    </select>
    <select id="selectSampleUser" resultMap="BaseResultMap" parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria">
            select
            <if test="distinct">
                distinct
            </if>
            user_sid,org_sid,status,account,company_id,parent_sid
            from sys_m_user
            <if test="_parameter != null">
                <include refid="Example_Where_Clause"/>
            </if>
            <if test="orderByClause != null">
                order by ${orderByClause}
            </if>
    </select>
    <select id="countUserByUserId" resultType="java.lang.Integer">
        select count(*) from sys_m_user where account =#{account} and (status =1 or status =2)
    </select>
    <select id="getUserSidByOrgSid" resultType="java.lang.Long">
        SELECT user_sid
        FROM sys_m_user
        WHERE org_sid = #{orgSid}
    </select>

    <select id="selectUserByRoleSids" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List_Alias"/>
        FROM sys_m_user A
        LEFT JOIN sys_m_user_role B ON B.user_sid = A.user_sid
        WHERE A.status = 1
        AND B.role_sid in
        <foreach collection="roleSids" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
        GROUP BY A.user_sid
    </select>
    <select id="getAccountMobileList" resultType="cn.com.cloudstar.rightcloud.core.pojo.dto.user.User">
        SELECT account,mobile FROM sys_m_user WHERE !ISNULL(mobile) AND mobile != '' AND `status` != 8
        <choose>
            <when test="moduleType == 'bss'">
                AND ISNULL(org_sid)
            </when>
            <otherwise>
                AND !ISNULL(org_sid)
            </otherwise>
        </choose>
    </select>

    <select id="selectAllUser" resultType="cn.com.cloudstar.rightcloud.core.pojo.dto.user.User">
        SELECT
            user_sid,
            account,
            email,
            real_name,
            mobile,
            auth_id
        FROM
            sys_m_user
    </select>
    <select id="countAuthIdHash" resultType="java.lang.Integer">
        select count(0) from sys_m_user A
        where A.auth_id_hash = #{hashAuthId} and A.status != 8 and A.status != 4
        and (A.business_tag is null or A.business_tag NOT LIKE '%preOpen%') and user_sid != #{userSid}
    </select>
    <update id="updateUserCmpRoleName">
        UPDATE sys_m_user
            SET cmp_role_name = #{cmpRoleName}
        WHERE user_sid = #{userSid}
    </update>

    <select id="getCmpRoleName" resultType="java.lang.String">
        SELECT cmp_role_name
        FROM sys_m_user
        WHERE user_sid = #{userSid}
    </select>

    <select id="selectAllUserByRightCloud" resultType="cn.com.cloudstar.rightcloud.core.pojo.dto.user.User">
        SELECT
            user_sid,
            email,
            real_name,
            mobile,
            auth_id
        FROM
            rightcloud.sys_m_user
    </select>



    <update id="updateBatchUser2" parameterType="java.util.List">
        <foreach collection="users" item="record" index="index" separator=";">
            UPDATE
            <if test="flag != 'false'">
                rightcloud.
            </if>
                sys_m_user

            <set>
                user_sid = #{record.userSid},
                <if test="record.realName != null">
                    real_name = #{record.realName},
                </if>
                <if test="record.email != null">
                    email = #{record.email},
                </if>
                <if test="record.mobile != null">
                    mobile = #{record.mobile},
                </if>
                <if test="record.authId != null">
                    auth_id = #{record.authId},
                </if>

                <if test="record.realNameHash != null">
                    real_name_hash = #{record.realNameHash},
                </if>
                <if test="record.emailHash != null">
                    email_hash = #{record.emailHash},
                </if>
                <if test="record.mobileHash != null">
                    mobile_hash = #{record.mobileHash},
                </if>
                <if test="record.authIdHash != null">
                    auth_id_hash = #{record.authIdHash},
                </if>
            </set>
            where user_sid = #{record.userSid}
        </foreach>
    </update>
    <select id="queryByRoleId" resultType="cn.com.cloudstar.rightcloud.core.pojo.dto.user.User">
        select distinct
        <include refid="Base_Column_List_Alias"/>
        from sys_m_user A, sys_m_user_role B
        where A.user_sid = B.user_sid and B.role_sid = #{roleSid} and A.status = 1
    </select>


    <select id="selectUserByRefId" resultType="cn.com.cloudstar.rightcloud.core.pojo.dto.user.User" parameterType="string">
        select
        <include refid="Base_Column_List_Alias"/>
        from sys_m_user A
        where A.ref_user_id = #{refUserId}  and A.status not in(8,4);
    </select>

    <select id="selectUserByRefIdAndUserPlatform" resultType="cn.com.cloudstar.rightcloud.core.pojo.dto.user.User">
        select
        <include refid="Base_Column_List_Alias"/>
        from sys_m_user A
        where A.ref_user_id = #{refUserId}  and A.status not in(8,4) and A.user_platform = #{userPlatform};
    </select>

    <select id="findIdByRefId" resultType="java.lang.Long">
        select
            A.user_id
        from rightcloud.sys_user A
        where A.ref_user_id = #{refUserId}
    </select>
    <select id="findRefUserByIds" resultType="cn.com.cloudstar.rightcloud.core.pojo.dto.user.SysUser">
        select
        A.user_id, A.ref_user_id
        from rightcloud.sys_user A
        <where>
            user_id in
            <foreach collection="userIds" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </where>
    </select>
    <select id="countSameMobileByOrgIds" resultType="int">
        select
        count(distinct user_sid)
        from sys_m_user where mobile = #{mobile} and org_sid in
        <foreach collection="orgIds" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="findUserByAccountAndOrgId" resultType="cn.com.cloudstar.rightcloud.core.pojo.dto.user.User">
        select
        <include refid="Base_Column_List_Alias"/>
        from sys_m_user A LEFT JOIN sys_m_user_org B ON A.user_sid = B.user_sid
        where A.account = #{account} and status not in(8,4)
        AND (A.org_sid = #{orgSid} OR B.org_sid = #{orgSid})
    </select>



</mapper>
