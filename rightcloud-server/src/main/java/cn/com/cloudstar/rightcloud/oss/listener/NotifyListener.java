/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.listener;

import cn.com.cloudstar.rightcloud.basic.data.pojo.enums.ShareSupportClusterTypeEnum;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResOceanstorPQuota;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.MapsKit;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.core.pojo.dto.bss.BizCouponAccount;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.SysConfig;
import cn.com.cloudstar.rightcloud.oss.common.util.*;
import com.google.common.collect.Lists;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.adapter.pojo.block.result.BlockSnapshotCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.block.result.BlockSnapshotDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.result.DiskCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.result.DiskDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.mq.QueryOrderDetail;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.BandwidthReviseResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.FloatingIpCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.FloatingIpDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.DBInstanceCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.ShareCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.ShareDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.ShareModifyResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmRemoveResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmSnapshotCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmTypeChangeResult;
import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnv;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResFloatingIp;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResRds;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResShare;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResVd;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResVm;
import cn.com.cloudstar.rightcloud.basic.data.pojo.snapshot.ResSnapshot;
import cn.com.cloudstar.rightcloud.common.constants.NotificationConsts;
import cn.com.cloudstar.rightcloud.common.constants.res.status.ResHpcClusterStatus;
import cn.com.cloudstar.rightcloud.common.constants.res.status.ShareStatus;
import cn.com.cloudstar.rightcloud.common.constants.type.OrderType;
import cn.com.cloudstar.rightcloud.common.enums.CommonPropertyKeyEnum;
import cn.com.cloudstar.rightcloud.common.mq.request.BaseNotificationMqBean;
import cn.com.cloudstar.rightcloud.common.schedule.helper.ScheduleHelper;
import cn.com.cloudstar.rightcloud.common.util.MapsKit;
import cn.com.cloudstar.rightcloud.common.util.cache.AuthUserHolder;
import cn.com.cloudstar.rightcloud.common.util.json.JsonUtil;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost;
import cn.com.cloudstar.rightcloud.core.pojo.dto.audit.ApproveOrderDTO;
import cn.com.cloudstar.rightcloud.core.pojo.dto.audit.ServiceOrder;
import cn.com.cloudstar.rightcloud.core.pojo.dto.audit.ServiceOrderDetail;
import cn.com.cloudstar.rightcloud.core.pojo.dto.audit.ServiceOrderPriceDetail;
import cn.com.cloudstar.rightcloud.core.pojo.dto.audit.ServiceOrderResourceRef;
import cn.com.cloudstar.rightcloud.core.pojo.dto.charge.BizBillingAccount;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.Message;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.User;
import cn.com.cloudstar.rightcloud.core.pojo.resource.CouponStatusEnum;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.Org;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicInfoUtil;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicWebUtil;
import cn.com.cloudstar.rightcloud.oss.common.cache.JedisUtil;
import cn.com.cloudstar.rightcloud.oss.common.constants.BillingConstants.ChargeType;
import cn.com.cloudstar.rightcloud.oss.common.constants.ExpireConstants;
import cn.com.cloudstar.rightcloud.oss.common.constants.ModuleTypeConstants;
import cn.com.cloudstar.rightcloud.oss.common.constants.res.type.CloudEnvType;
import cn.com.cloudstar.rightcloud.oss.common.constants.status.OrderStatus;
import cn.com.cloudstar.rightcloud.oss.common.enums.BusinessTagEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.CcmTaskStatusEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.oss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.oss.common.util.*;
import cn.com.cloudstar.rightcloud.oss.common.websocket.annonation.MessageParam;
import cn.com.cloudstar.rightcloud.oss.module.access.service.PolicyService;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.user.UserMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.service.user.UserService;
import cn.com.cloudstar.rightcloud.oss.module.coupon.bean.BizCouponResource;
import cn.com.cloudstar.rightcloud.oss.module.coupon.service.Impl.CouponService;
import cn.com.cloudstar.rightcloud.oss.module.order.dao.*;
import cn.com.cloudstar.rightcloud.oss.module.order.entity.ReleaseOrderResult;
import cn.com.cloudstar.rightcloud.oss.module.order.handler.OrderHandler;
import cn.com.cloudstar.rightcloud.oss.module.order.service.ServiceOrderService;
import cn.com.cloudstar.rightcloud.oss.module.others.dao.deploy.DeployTaskMapper;
import cn.com.cloudstar.rightcloud.oss.module.pricing.dao.priceconfig.BizAccountDealMapper;
import cn.com.cloudstar.rightcloud.oss.module.pricing.dao.priceconfig.BizBillingAccountMapper;
import cn.com.cloudstar.rightcloud.oss.module.system.dao.config.PolicyUserMapper;
import cn.com.cloudstar.rightcloud.oss.module.system.service.config.SystemConfigService;
import cn.com.cloudstar.rightcloud.oss.module.system.service.message.MessageService;
import cn.com.cloudstar.rightcloud.oss.module.system.service.message.NotificationService;
import cn.com.cloudstar.rightcloud.remote.api.iam.service.HcsoUserRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.ResHpcClusterRemoteModule;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.ResHpcClusterResourceRemote;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResChangeRecordDTO;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResResource;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.cloud.CloudEnvRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.hpc.HPCRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.network.ResFloatingIpRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.oceanstor.OceanstorPQuotaRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.rds.ResRdsRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.res.ResChangeRecordRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.server.ResVmRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.share.ShareRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.snapshot.ResSnapshotRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.storage.ResVdRemoteService;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;

import static cn.com.cloudstar.rightcloud.common.constants.NotificationConsts.SEND_NOTIFICATION_EXCHANGE;


/**
 * <AUTHOR>
 * @date 2021/1/12 1:58 下午
 */
@Component
@Slf4j
public class NotifyListener {

    private static final String ACTIVE = "Active";
    @DubboReference
    ResVmRemoteService resVmRemoteService;
    @Autowired
    private ServiceOrderService serviceOrderService;
    @DubboReference
    ResVdRemoteService resVdRemoteService;
    @DubboReference
    ResFloatingIpRemoteService resFloatingIpRemoteService;
    @DubboReference
    CloudEnvRemoteService cloudEnvRemoteService;
    @DubboReference
    ResSnapshotRemoteService resSnapshotRemoteService;
    @DubboReference
    ResRdsRemoteService resRdsRemoteService;
    @Autowired
    UserService userService;

    @Autowired
    private BizCouponAccountMapper bizCouponAccountMapper;
    @Autowired
    OrderHandler orderHandler;
    @Autowired
    DeployTaskMapper deployTaskMapper;
    @Autowired
    ServiceOrderDetailMapper serviceOrderDetailMapper;
    @Autowired
    ServiceOrderResourceRefMapper serviceOrderResourceRefMapper;
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    NotificationService notificationService;
    @DubboReference
    HcsoUserRemoteService hcsoUserRemoteService;
    @Autowired
    PolicyUserMapper policyUserMapper;
    @Autowired
    private ServiceOrderRecordMapper orderRecordMapper;
    @Autowired
    private CouponService couponService;
    @Autowired
    private BizBillingAccountMapper bizBillingAccountMapper;
    @Autowired
    private ServiceOrderPriceDetailMapper serviceOrderPriceDetailMapper;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private BizAccountDealMapper bizAccountDealMapper;
    @Autowired
    private ShareRemoteService shareRemoteService;
    @DubboReference
    private HPCRemoteService hpcRemoteService;
    @Autowired
    PolicyService policyService;
    @Autowired
    private MessageService messageService;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private SystemConfigService systemConfigService;
    @Autowired
    @Qualifier("cloudExecutor")
    private ThreadPoolTaskExecutor executorService;
    @DubboReference
    private OceanstorPQuotaRemoteService oceanstorPQuotaRemoteService;

    @DubboReference
    private ResChangeRecordRemoteService resChangeRecordRemoteService;

    public static final Map<String, String> INTERNET_CHARGE_TYPE_MAP = MapsKit.of("PayByBandwidth", "PayByBandwidth",
                                                                                  "PayByTraffic", "PayByTraffic",
                                                                                  "sta per", "5_sbgp",
                                                                                  "sta all", "sta all",
                                                                                  "mov per", "5_bgp",
                                                                                  "mov all", "mov all",
                                                                                  "un per", "un per");

    private static final String SALE_TYPE = "02";
    private static final String NORMAL_TYPE = "01";
    private static final String ADD = "add";

    /**
     * 处理不需要处理的消息  接收到 resource 发送的消息未找到对应的方法参数就会走这个函数
     *
     * @param request
     */
    public void handleMessage(Object request) {

    }


    public void handleMessage(VmCreateResult vmCreateResult) {

        //存放资源，用于价格关联资源ID
        List<ResResource> subResources = Lists.newArrayList();
        List<String> diskSids = vmCreateResult.getDiskSids();
        diskSids.stream().forEach(dsid -> {
            ResVd resVd = resVdRemoteService.selectByPrimaryKey(dsid);
            if (resVd != null) {
                subResources.add(new ResResource(resVd));
            }
        });
        ResFloatingIp resFloatingIp = resFloatingIpRemoteService.selectByPrimaryKey(
                vmCreateResult.getResFloatingIpSid());
        if (resFloatingIp != null) {
            subResources.add(new ResResource(resFloatingIp));
        }
        ResVm resVm = resVmRemoteService.findByPrimaryKey(
                vmCreateResult.getId());
        if (resVm == null) {
            resVm = new ResVm();
            resVm.setId(vmCreateResult.getSid());
        }
        // 创建vm完成
        ResResource resResource = new ResResource(resVm);
        resResource.setPlatformOrderId(vmCreateResult.getPlatformOrderId());
        serviceOrderService.crateResourceComplete(subResources, resResource);
    }


    public void handleMessage(VmRemoveResult vmRemoveResult) {

        ResVm resVm = this.resVmRemoteService.selectByPrimaryKey(vmRemoveResult.getSid());
        try {
            if (vmRemoveResult.isSuccess()) {
                CloudEnv cloudEnv = cloudEnvRemoteService.selectByPrimaryKey(resVm.getCloudEnvId());
                QueryOrderDetail queryOrderDetail = CloudClientFactory.buildMQBean(resVm.getCloudEnvId(),
                                                                                   QueryOrderDetail.class);
                queryOrderDetail.setResourceId(resVm.getInstanceId());
                queryOrderDetail.setResourceName(resVm.getInstanceName());
                queryOrderDetail.setPlatformOrderId(vmRemoveResult.getPlatformOrderId());
                queryOrderDetail.setChargeType(resVm.getInstanceChargeType());
                queryOrderDetail.setPrimaryKey(resVm.getId());
                queryOrderDetail.setCreateTime(new Date());
                if (CloudEnvType.QCLOUD.equalValue(cloudEnv.getCloudEnvType())) {
                    queryOrderDetail.setOrderType("6");
                }
                serviceOrderService.runOrderTask(resVm.getId(),
                                                 queryOrderDetail, cloudEnv);
            } else {
                serviceOrderService.cancelOder(resVm.getId());
            }
        } catch (Exception e) {
            serviceOrderService.cancelOder(resVm.getId());
        }

    }

    public void handleMessage(@MessageParam("vmTypeChangeResult") VmTypeChangeResult vmTypeChangeResult) {
        log.info("主机变更实例类型回调 | 回调参数 ： {}", JsonUtil.toJson(vmTypeChangeResult));
        ResVm resVm = resVmRemoteService.selectByPrimaryKey(vmTypeChangeResult.getResVmId());
        if (vmTypeChangeResult.isSuccess()) {
            CloudEnv cloudEnv = cloudEnvRemoteService.selectByPrimaryKey(resVm.getCloudEnvId());
            QueryOrderDetail queryOrderDetail = CloudClientFactory.buildMQBean(resVm.getCloudEnvId(),
                                                                               QueryOrderDetail.class);
            queryOrderDetail.setResourceId(resVm.getInstanceId());
            queryOrderDetail.setPlatformOrderId(vmTypeChangeResult.getPlatformOrderId());
            queryOrderDetail.setChargeType(resVm.getInstanceChargeType());
            queryOrderDetail.setPrimaryKey(resVm.getId());
            Map<String, Object> map = vmTypeChangeResult.getOptions();
            if (Objects.nonNull(map)) {
                queryOrderDetail.setTaskId(map.get("orderTaskId").toString());
            }
            serviceOrderService.runModifyOrderTask(resVm.getId(),
                                                   queryOrderDetail, cloudEnv);
        } else {
            serviceOrderService.cancelOder(resVm.getId());
        }
    }


    public void handleMessage(VmSnapshotCreateResult vmSnapshotCreateResult) {
        log.info("创建云主机快照回调 | 回调参数 ： {}", JsonUtil.toJson(vmSnapshotCreateResult));
        ResSnapshot resSnapshot = resSnapshotRemoteService.selectByPrimaryKey(vmSnapshotCreateResult.getSnapshotId());
        ResResource resResource = new ResResource(resSnapshot, "VBS-VD");
        resResource.setPlatformOrderId(vmSnapshotCreateResult.getPlatformOrderId());
        serviceOrderService.crateResourceComplete(null, resResource);
    }


    public void handleMessage(DBInstanceCreateResult dbInstanceCreateResult) {
        log.info("创建RDS实例 | 回调参数 ： {}", JsonUtil.toJson(dbInstanceCreateResult));
        Long resRdsId = Long.valueOf(dbInstanceCreateResult.getOptions().get("id").toString());
        ResRds rds = resRdsRemoteService.selectByPrimaryKey(resRdsId);
        ResResource resResource = new ResResource(rds);
        resResource.setPlatformOrderId(dbInstanceCreateResult.getPlatformOrderId());
        serviceOrderService.crateResourceComplete(null, resResource);
    }

    public void handleMessage(FloatingIpCreateResult floatingIpCreateResult) {
        log.info("创建弹性ip回调 | 回调参数 ： {}", JsonUtil.toJson(floatingIpCreateResult));
        Map<String, Object> options = floatingIpCreateResult.getOptions();
        ResFloatingIp resFloatingIp = resFloatingIpRemoteService.selectByPrimaryKey(
                Long.valueOf(options.get("id").toString()));
        resFloatingIp.setInternetChargeType(INTERNET_CHARGE_TYPE_MAP.getOrDefault(resFloatingIp.getInternetChargeType(),
                                                                                  resFloatingIp.getInternetChargeType()));
        ResResource resResource = new ResResource(resFloatingIp);
        resResource.setPlatformOrderId(floatingIpCreateResult.getPlatformOrderId());
        serviceOrderService.crateResourceComplete(null, resResource);
    }


    public void handleMessage(BandwidthReviseResult bandwidthReviseResult) {
        log.info("弹性ip带宽修改回调 | 回调参数 ： {}", JsonUtil.toJson(bandwidthReviseResult));
        Map<String, Object> options = bandwidthReviseResult.getOptions();
        try {
            // 修改成功
            if (bandwidthReviseResult.isSuccess()) {
                ResFloatingIp resFloatingIp = resFloatingIpRemoteService.selectByPrimaryKey(
                        Long.valueOf(options.get("floatingIpId").toString()));
                CloudEnv cloudEnv = cloudEnvRemoteService.selectByPrimaryKey(resFloatingIp.getEnvId());
                QueryOrderDetail queryOrderDetail = CloudClientFactory.buildMQBean(cloudEnv.getId(),
                                                                                   QueryOrderDetail.class);
                queryOrderDetail.setResourceId(resFloatingIp.getUuid());
                queryOrderDetail.setPlatformOrderId(bandwidthReviseResult.getPlatformOrderId());
                queryOrderDetail.setChargeType(resFloatingIp.getChargeType());
                queryOrderDetail.setPrimaryKey(resFloatingIp.getId().toString());
                Map<String, Object> map = bandwidthReviseResult.getOptions();
                if (Objects.nonNull(map)) {
                    queryOrderDetail.setTaskId(map.get("orderTaskId").toString());
                }
                serviceOrderService.runModifyOrderTask(resFloatingIp.getId().toString(),
                                                       queryOrderDetail, cloudEnv);
            } else { // 修改失败
                log.error(bandwidthReviseResult.getErrMsg());
                serviceOrderService.cancelOder(options.get("floatingIpId").toString());
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            serviceOrderService.cancelOder(options.get("floatingIpId").toString());
        }
    }


    public void handleMessage(FloatingIpDeleteResult floatingIpDeleteResult) {
        log.info("删除弹性ip回调 | 回调参数 ： {}", JsonUtil.toJson(floatingIpDeleteResult));
        Map<String, Object> options = floatingIpDeleteResult.getOptions();
        try {
            // 删除成功
            if (floatingIpDeleteResult.isSuccess()) {
                ResFloatingIp resFloatingIp = resFloatingIpRemoteService.selectByPrimaryKey(
                        Long.valueOf(options.get("id").toString()));
                CloudEnv cloudEnv = cloudEnvRemoteService.selectByPrimaryKey(resFloatingIp.getEnvId());
                QueryOrderDetail queryOrderDetail = CloudClientFactory.buildMQBean(resFloatingIp.getEnvId(),
                                                                                   QueryOrderDetail.class);
                queryOrderDetail.setResourceId(resFloatingIp.getUuid());
                queryOrderDetail.setPlatformOrderId(floatingIpDeleteResult.getPlatformOrderId());
                queryOrderDetail.setCreateTime(new Date());
                queryOrderDetail.setOrderType(floatingIpDeleteResult.getOrderType());
                if (CloudEnvType.QCLOUD.equalValue(cloudEnv.getCloudEnvType())) {
                    queryOrderDetail.setOrderType("6");
                }
                queryOrderDetail.setChargeType(resFloatingIp.getChargeType());
                queryOrderDetail.setPrimaryKey(resFloatingIp.getId().toString());
                serviceOrderService.runOrderTask(options.get("id").toString(),
                                                 queryOrderDetail, cloudEnv);

            } else { // 删除失败
                log.error(floatingIpDeleteResult.getErrMsg());
                serviceOrderService.cancelOder(options.get("id").toString());
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            serviceOrderService.cancelOder(options.get("id").toString());
        }
    }


    public void handleMessage(BlockSnapshotCreateResult blockSnapshotCreateResult) {
        log.info("创建快照回调 | 回调参数 ： {}", JsonUtil.toJson(blockSnapshotCreateResult));
        ResSnapshot resSnapshot = resSnapshotRemoteService.selectByPrimaryKey(
                Long.valueOf(blockSnapshotCreateResult.getId()));
        ResResource resResource = new ResResource(resSnapshot, "VBS-VD");
        resResource.setPlatformOrderId(blockSnapshotCreateResult.getPlatformOrderId());
        serviceOrderService.crateResourceComplete(null, resResource);
    }



    public void handleMessage(BlockSnapshotDeleteResult blockSnapshotDeleteResult) {
        log.info("删除快照回调 | 回调参数 ： {}", JsonUtil.toJson(blockSnapshotDeleteResult));
        ResSnapshot resSnapshot = resSnapshotRemoteService.selectByPrimaryKey(blockSnapshotDeleteResult.getId());
        try {
            if (blockSnapshotDeleteResult.isSuccess()) {
                ReleaseOrderResult releaseOrderResult = new ReleaseOrderResult();
                releaseOrderResult.setId(resSnapshot.getId().toString());
                releaseOrderResult.setPublic(false);
                orderHandler.handleMessage(releaseOrderResult);
            } else {
                serviceOrderService.cancelOder(resSnapshot.getId().toString());
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            serviceOrderService.cancelOder(resSnapshot.getId().toString());
        }

    }


    public void handleMessage(DiskCreateResult diskCreateResult) {
        log.info("创建块存储回调 | 回调参数 ： {}", JsonUtil.toJson(diskCreateResult));
        ResVd resVd = resVdRemoteService.selectByPrimaryKey(diskCreateResult.getSid());
        ResResource resResource = new ResResource(resVd);
        resResource.setPlatformOrderId(diskCreateResult.getPlatformOrderId());
        serviceOrderService.crateResourceComplete(null, resResource);
    }


    public void handleMessage(DiskDeleteResult diskDeleteResult) {
        log.info("删除块存储回调 | 回调参数 ： {}", JsonUtil.toJson(diskDeleteResult));
        Map<String, Object> options = diskDeleteResult.getOptions();
        String resVdSid = options.get("resVdSid").toString();
        ResVd resVd = this.resVdRemoteService.selectByPrimaryKeyIgnoreStatus(resVdSid);
        try {
            // 删除成功
            if (diskDeleteResult.isSuccess()) {
                CloudEnv cloudEnv = cloudEnvRemoteService.selectByPrimaryKey(resVd.getCloudEnvId());
                QueryOrderDetail queryOrderDetail = CloudClientFactory.buildMQBean(resVd.getCloudEnvId(), QueryOrderDetail.class);
                queryOrderDetail.setResourceId(resVd.getInstanceId());
                queryOrderDetail.setResourceName(resVd.getVdName());
                queryOrderDetail.setPlatformOrderId(diskDeleteResult.getPlatformOrderId());
                queryOrderDetail.setChargeType(resVd.getChargeType());
                queryOrderDetail.setPrimaryKey(resVd.getResVdSid());
                if (CloudEnvType.QCLOUD.equalValue(cloudEnv.getCloudEnvType())) {
                    queryOrderDetail.setOrderType("6");
                }
                queryOrderDetail.setCreateTime(new Date());
                serviceOrderService.runOrderTask(resVd.getResVdSid(),
                                                 queryOrderDetail, cloudEnv);
            } else { // 删除失败e
                log.error(diskDeleteResult.getErrMsg());
                serviceOrderService.cancelOder(resVd.getResVdSid());
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            serviceOrderService.cancelOder(resVd.getResVdSid());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void handleMessage(ShareCreateResult shareCreateResult) {
        log.info("创建弹性文件回调 | 回调参数 ： {}", JsonUtil.toJson(shareCreateResult));
        boolean hasOrder = shareCreateResult.getServiceOrderId() != null;
        if (hasOrder) {
            //根据产品id查询到对应的运营实体
            //根据订单查询订单信息
            ServiceOrder serviceOrder = serviceOrderService.getById(shareCreateResult.getServiceOrderId());
            log.info("订单信息：{}",JsonUtil.toJson(serviceOrder));
            if(serviceOrder == null){
                serviceOrder =  serviceOrderService.selectByOrderSn(shareCreateResult.getServiceOrderId().toString());
            }
            ServiceOrder serviceOrderDetail = serviceOrderService.getServiceOrderDetail(
                    shareCreateResult.getServiceOrderId(),serviceOrder.getOwnerId(), serviceOrder.getEntityId());
            String serviceType = Optional.ofNullable(serviceOrderDetail.getDetails())
                .map(CollectionUtil::getFirst).map(ServiceOrderDetail::getServiceType).orElse(null);
            if (is(serviceType, ProductCodeEnum.HPC.getProductCode())) {
                handleHpc(shareCreateResult, serviceOrderDetail);
            }
            if (is(serviceType, ProductCodeEnum.SFS2.getProductCode())) {
                handleSfs(shareCreateResult, serviceOrderDetail);
            }
        }
    }
    private boolean is(String serverType, String expectType) {
        return StrUtil.equalsIgnoreCase(serverType, expectType);
    }

    private void handleHpc(ShareCreateResult shareCreateResult, ServiceOrder serviceOrderDetail) {
        log.info("HPC V1/V2回调成功");
        // hpc 存储服务   发送邮件通知管理员
        if (shareCreateResult.isSuccess()) {
            Long orgSid = serviceOrderDetail.getOrgSid();
            Org currentOrgInfo = BasicInfoUtil.getCurrentOrgInfo(orgSid);
            List<String> hpcSubusers = policyUserMapper.containsHpcSubusers(serviceOrderDetail.getOwnerId());
            User user = userService.selectByPrimaryKey(serviceOrderDetail.getOwnerId());
            hpcSubusers.add(user.getAccount());
            ResHpcClusterRemoteModule resHpcClusterRemoteModule = hpcRemoteService.selectByPrimaryKey(
                    serviceOrderDetail.getClusterId());
            resHpcClusterRemoteModule.setStatus(ResHpcClusterStatus.CONFIGED);
            hpcRemoteService.updateByPrimaryKeySelective(resHpcClusterRemoteModule);
            ServiceOrderDetail detail = serviceOrderDetail.getDetails().get(1);

            ServiceOrderResourceRef resourceRef = new ServiceOrderResourceRef();
            resourceRef.setOrderDetailId(detail.getId());
            resourceRef.setType("SFS");
            resourceRef.setResourceId(shareCreateResult.getId() + "");
            serviceOrderResourceRefMapper.insert(resourceRef);
            ResHpcClusterResourceRemote resHpcClusterResource = new ResHpcClusterResourceRemote();
            resHpcClusterResource.setClusterId(serviceOrderDetail.getClusterId());
            resHpcClusterResource.setResourceId(shareCreateResult.getId());
            resHpcClusterResource.setResourceType("SFS");
            resHpcClusterResource.setCreatedOrgSid(currentOrgInfo.getOrgSid() + "");
            resHpcClusterResource.setOwnerId(serviceOrderDetail.getOwnerId() + "");
            BasicWebUtil.prepareInsertParams(resHpcClusterResource);
            hpcRemoteService.insertClusterResource(resHpcClusterResource);
            try {
                policyService.synHpcToLdap(currentOrgInfo.getOrgSid());
            }catch (Exception e){
                e.printStackTrace();
                throw e;
            }

            Map<String, List> sendAddr = new HashMap<>(1);
            Map<String, String> messageContent = new HashMap<>(4);
            String applyUser = user.getAccount();
            messageContent.put("applyUser", applyUser);
            messageContent.put("userAccount", applyUser);
            String exportLocation = shareCreateResult.getExportLocation();
            messageContent.put("exportLocation", exportLocation);
            //租户管理员和有HPC 权限的子用户
            String subPath = String.join(",", hpcSubusers.toArray(new String[0]));
            messageContent.put("subPath", subPath);
            String ldapOu = currentOrgInfo.getLdapOu();
            messageContent.put("ldapOu", ldapOu);

            //查找对应角色的用户
            Criteria criteria = new Criteria();
            criteria.setCondition(MapsKit.of("roleSid", 301));
            List<User> userByRoleId = userService.findUserByRoleId(criteria);
            criteria.setCondition(MapsKit.of("roleSid", 305));
            userByRoleId.addAll(userService.findUserByRoleId(criteria));

            Set<String> mailSet = userByRoleId.stream().map(User::getEmail).collect(Collectors.toSet());
            //发站内信
            Set<String> accountSet = userByRoleId.stream().map(User::getAccount).collect(Collectors.toSet());
            for (String account : accountSet) {
                Map platMessageContent = new HashMap<>();
                platMessageContent.put("applyUser",applyUser);
                platMessageContent.put("exportLocation", exportLocation);
                //租户管理员和有HPC 权限的子用户
                platMessageContent.put("subPath", subPath);
                platMessageContent.put("ldapOu", ldapOu);
                platMessageContent.put("userAccount", account);

                notificationService.sendNotification(new int[]{0},false,sendAddr,platMessageContent,"approval_hpc",null);
            }

            sendAddr.put("email", new ArrayList<>(mailSet));

            notificationService.sendNotification(new int[]{1},false,sendAddr,messageContent,"approval_hpc",null);
        }else  if (!shareCreateResult.isSuccess()&&shareCreateResult.getServiceOrderId() != null) {
            Boolean aBoolean = shareRemoteService.deleteShareById(shareCreateResult.getId());
            log.info("handleHpc-弹性文件回退:{},id:[{}]",aBoolean, shareCreateResult.getId());
            String opUser = shareCreateResult.getOpUser();
            cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser admin = null;
            if (StringUtils.isNotEmpty(opUser)) {
                admin = BasicInfoUtil.getUserInfoByAccount(opUser);
            } else {
                admin = BasicInfoUtil.getUserInfoByAccount("admin");
            }

            ApproveOrderDTO approveOrderDTO = new ApproveOrderDTO();
            approveOrderDTO.setApproveAdvice("SFS资源开通失败,系统自动回退,失败原因:【" + shareCreateResult.getErrMsg() + "】");
            approveOrderDTO.setApproveType("02");
            approveOrderDTO.setUserSid(admin.getUserSid() + "");
            approveOrderDTO.setId(serviceOrderDetail.getId());
            approveOrderDTO.setHpcGoBack(true);
            AuthUser authUser = new AuthUser();
            admin.setLicenceExpire(false);

            BeanUtils.copyProperties(admin, authUser);
            log.info("弹性文件创建失败开始回退");
            ServiceOrder serviceOrder = serviceOrderService.approveServiceOrder(approveOrderDTO, admin.getAccount(), authUser);
            log.info("回退成功");
        }
    }

    private void handleSfs(ShareCreateResult shareCreateResult, ServiceOrder serviceOrderDetail) {
        boolean success = shareCreateResult.isSuccess();
        if (success) {
            // 修改订单
            serviceOrderDetail.setStatus(OrderStatus.COMPLETED);
            // 计费 ProcessServiceImpl@processProduct
            ResShare resShare = shareRemoteService.selectByPrimaryKey(shareCreateResult.getId());
            try {
                billing(shareCreateResult, serviceOrderDetail, resShare);
            } catch (BizException e) {
                log.error(e.getMessage(), e);
                shareRemoteService.deleteShareByIdAndOrderId(shareCreateResult.getId(),
                                                             serviceOrderDetail.getId());
                serviceOrderDetail.setStatus(OrderStatus.LEV2_REFUSED);
            } catch (Exception e){
                log.error("创建弹性文件回调-NotifyListener-handleSfs-异常：{}",e.getMessage());
                throw e;
            }
            serviceOrderDetail.setPayTime(new Date());
            serviceOrderService.updateByPrimaryKeySelective(serviceOrderDetail);

            //发送站内信
            if ("apply".equals(serviceOrderDetail.getType()) && serviceOrderDetail.getProductName().contains("SFS")){
                try{
                    Message message = new Message();
                    message.setFromUser(RequestContextUtil.getCurrentUserName());
                    message.setMsgTitle("["+serviceOrderDetail.getName()+"-弹性文件服务]开通结果");
                    message.setMsgType("04");
                    message.setToUser(serviceOrderDetail.getCreatedBy());
                    message.setToUserSid(serviceOrderDetail.getOwnerId());
                    message.setMsgContent("\n弹性文件服务"+serviceOrderDetail.getName()+"已经开通，请登录控制台使用。<br/>\n感谢您的使用。\n");
                    message.setSendDate(new Date());
                    message.setReadFlag("02");
                    messageService.insert(message);
                }catch (Exception e){
                    log.info("开通弹性文件服务发送站内信失败");
                }
            }
        } else {
            // 修改订单
            serviceOrderDetail.setStatus(OrderStatus.LEV2_REFUSED);
            serviceOrderService.updateByPrimaryKeySelective(serviceOrderDetail);
            // 将优惠券状态重置为未使用
            BizCouponResource bizCouponResource = couponService.getCouponResourceByOrderId(
                    serviceOrderDetail.getId());
            if (Objects.nonNull(bizCouponResource)) {
                couponService.updateCouponStatus(bizCouponResource.getCouponSid(),
                                                 CouponStatusEnum.UNUSED.getCode());
            }
        }
    }

    private void billing(ShareCreateResult shareCreateResult, ServiceOrder serviceOrder, ResShare resShare) {
        ServiceOrderDetail detail = CollectionUtil.getFirst(serviceOrder.getDetails());
        if (ChargeType.POST_PAID.equals(detail.getChargeType())) {
            return;
        }
        BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectByPrimaryKey(
            serviceOrder.getBizBillingAccountId());
        if (ObjectUtils.isEmpty(bizBillingAccount)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_607095064));
        }
        Criteria criteria = new Criteria();
        criteria.put("orderDetailId", detail.getId());
        List<ServiceOrderPriceDetail> serviceOrderPriceDetails = serviceOrderPriceDetailMapper.selectByParams(
                criteria);
        List<InstanceGaapCost> costs = Lists.newArrayList();

        List<BizCouponAccount> bizCouponAccounts = bizCouponAccountMapper.selectByOrderId(serviceOrder.getId(), bizBillingAccount.getId());
        BizCouponAccount bizCouponAccount = null;
        if (CollectionUtil.isNotEmpty(bizCouponAccounts)) {
            bizCouponAccount = bizCouponAccounts.get(0);
        }
        // detail.getQuantity() = 1
        for (ServiceOrderPriceDetail priceDetail : serviceOrderPriceDetails) {
            InstanceGaapCost cost = serviceOrderService.getBaseCost(serviceOrder, priceDetail,
                                                                    Convert.toStr(resShare.getUuid()),
                                                                    resShare.getName());
            cost.setRegion(resShare.getAvailabilityZone());
            cost.setConfiguration(
                    StrUtil.concat(true, resShare.getShareProto(), "(", Convert.toStr(resShare.getSize()), "GB)"));
            // originalCost 原价
            BigDecimal originalCost = priceDetail.getOriginalCost();
            // tradeFinalCost 最终价格
            BigDecimal finalCost = priceDetail.getAmount();
            cost.setPricingDiscount(priceDetail.getDiscount());
            BigDecimal couponAmount = priceDetail.getCouponAmount();
            cost.setCouponDiscount(couponAmount);
            if (Objects.nonNull(bizCouponAccount)) {
                bizCouponAccount.setDiscountUsed(couponAmount.add(bizCouponAccount.getDiscountUsed()));
            }

            //关联不计费产品设置实付金额为0
            if(Objects.nonNull(serviceOrder.getChargingType()) && SALE_TYPE.equals(serviceOrder.getChargingType())){
                cost.setCashAmount(BigDecimal.ZERO);
                cost.setCreditAmount(BigDecimal.ZERO);
                cost.setCouponAmount(BigDecimal.ZERO);
                finalCost = BigDecimal.ZERO;
                cost.setChargingType(SALE_TYPE);
            }else {
                cost.setChargingType(NORMAL_TYPE);
                serviceOrderService.setUsedCost(bizBillingAccount, cost, finalCost);
                log.info("扣款费用：{}",NumberUtil.sub(bizBillingAccount.getBalance(), cost.getCashAmount()));
                bizBillingAccount.setBalance(
                        NumberUtil.sub(bizBillingAccount.getBalance(), cost.getCashAmount()));
                bizBillingAccount.setCreditLine(
                        NumberUtil.sub(bizBillingAccount.getCreditLine(), cost.getCreditAmount()));
                bizBillingAccount.setBalanceCash(
                        NumberUtil.sub(bizBillingAccount.getBalanceCash(), cost.getCouponAmount()));
            }
            cost.setPretaxGrossAmount(originalCost);
            cost.setPretaxAmount(BigDecimalUtil.remainTwoPointAmount(finalCost));
            cost.setEraseZeroAmount(BigDecimalUtil.getEraseZeroColumn(finalCost));
            cost.setBizBillingAccount(
                    BeanUtil.toBean(bizBillingAccount, BizBillingAccount.class));
            cost.setEntityId(serviceOrder.getEntityId());
            cost.setEntityName(serviceOrder.getEntityName());
            cost.setOrderSourceSn(serviceOrder.getOrderSourceSn());
            log.info("所属运营实体id：{}，name：{}",serviceOrder.getEntityId(),serviceOrder.getEntityName());
            costs.add(cost);
            priceDetail.setPayBalance(cost.getCashAmount());
            priceDetail.setPayBalanceCash(cost.getCouponAmount());
            priceDetail.setPayCreditLine(cost.getCreditAmount());
            serviceOrderPriceDetailMapper.updateByPrimaryKeySelective(priceDetail);
        }
        log.info("扣费账户：{},账户余额：{}",bizBillingAccount,bizBillingAccount.getBalance());
        bizBillingAccountMapper.updateByPrimaryKeySelective(bizBillingAccount);
        //修改用户业务标识Tag
        updateUserBusinessTag(bizBillingAccount);
        serviceOrderService.insertCostAndCycleAndDeal(serviceOrder, costs);

        if (Objects.nonNull(bizCouponAccount)) {
            bizCouponAccountMapper.updateByPrimaryKeySelective(bizCouponAccount);
        }
    }

    private void updateUserBusinessTag(BizBillingAccount bizBillingAccount) {
        User user = userMapper.selectByPrimaryKey(bizBillingAccount.getAdminSid());
        String businessTag = user.getBusinessTag();
        if (bizBillingAccount.getBalance().compareTo(BigDecimal.ZERO) < 0) {
            if (StringUtil.isNotEmpty(businessTag)) {
                List<String> tagList = new ArrayList<>(Arrays.asList(businessTag.split(";")));
                if (businessTag.contains(BusinessTagEnum.ARREARAGE.getTag())){
                    tagList = tagList.stream().map(s -> {
                        if (s.contains(BusinessTagEnum.ARREARAGE.getTag())){
                            String[] split = s.replaceAll(BusinessTagEnum.ARREARAGE.getTag(), "")
                                              .replaceAll("\\[", "")
                                              .replaceAll("]", "")
                                              .split(",");
                            List<String> accountIdList = new ArrayList<>(Arrays.asList(split));
                            if (!accountIdList.contains(bizBillingAccount.getId().toString())){
                                accountIdList.add(bizBillingAccount.getId().toString());
                                s = BusinessTagEnum.ARREARAGE.getTag() + "[" + StringUtils.join(accountIdList, ",")
                                        + "]";
                            }
                        }
                        return s;
                    }).collect(Collectors.toList());
                }else {
                    tagList.add(BusinessTagEnum.ARREARAGE.getTag() + "[" + bizBillingAccount.getId() + "]");
                }
                user.setBusinessTag(StringUtils.join(tagList,";"));
            }else {
                user.setBusinessTag(BusinessTagEnum.ARREARAGE.getTag() + "[" + bizBillingAccount.getId() + "]");
            }
            userService.updateByPrimaryKeySelective(user);
        }else {
            if (StringUtil.isNotEmpty(businessTag)) {
                List<String> tagList = new ArrayList<>(Arrays.asList(businessTag.split(";")));
                if (businessTag.contains(BusinessTagEnum.ARREARAGE.getTag())){
                    tagList = tagList.stream().map(s -> {
                        if (s.contains(BusinessTagEnum.ARREARAGE.getTag())){
                            String[] split = s.replaceAll(BusinessTagEnum.ARREARAGE.getTag(), "")
                                              .replaceAll("\\[", "")
                                              .replaceAll("]", "")
                                              .split(",");
                            List<String> accountIdList = new ArrayList<>(Arrays.asList(split));
                            if (accountIdList.contains(bizBillingAccount.getId().toString())){
                                accountIdList.remove(bizBillingAccount.getId().toString());
                                if (CollectionUtils.isEmpty(accountIdList)){
                                    return null;
                                }
                                s = BusinessTagEnum.ARREARAGE.getTag() + "[" + StringUtils.join(accountIdList, ",")
                                        + "]";
                            }
                        }
                        return s;
                    }).filter(Objects::nonNull).collect(Collectors.toList());
                    user.setBusinessTag(StringUtils.join(tagList,";"));
                    userService.updateByPrimaryKeySelective(user);
                }
            }
        }
    }
    @Transactional(rollbackFor = Exception.class)
    public void handleMessage(ShareDeleteResult shareDeleteResult) {
        log.info("删除弹性文件回调 | 回调参数 ： {}", JsonUtil.toJson(shareDeleteResult));
        Long orderId = MapUtil.getLong(shareDeleteResult.getOptions(), "orderId");
        boolean hasOrder = Objects.nonNull(orderId);
        if (hasOrder) {
            ServiceOrder serviceOrderDetail = serviceOrderService.getServiceOrderDetail(orderId);
            String serviceType = Optional.ofNullable(serviceOrderDetail.getDetails())
                                         .map(CollectionUtil::getFirst)
                                         .map(ServiceOrderDetail::getServiceType)
                                         .orElse(null);
            if (is(serviceType, ProductCodeEnum.SFS2.getProductCode())) {
                handleDeleteSfs(shareDeleteResult, serviceOrderDetail);
            }
        }else{
            ServiceOrder serviceOrder = serviceOrderService.selectOrderDetailByResourceId(shareDeleteResult.getId().toString(), ProductCodeEnum.SFS2.getProductCode());
            List<User> adminstrators = userMapper.findAdminstratorsByEntityId(serviceOrder.getEntityId());
            HashMap<String, String> messageContent1 = new HashMap<>(3);
            messageContent1.put("orgName", shareDeleteResult.getOpUser());
            messageContent1.put("productName", ProductCodeEnum.SFS.getProductName());
            messageContent1.put("orderSn", serviceOrder.getOrderSn());
            messageContent1.put("userAccount", shareDeleteResult.getOpUser());

            BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
            baseNotificationMqBean.getImsgUserIds().addAll(adminstrators.stream().map(User::getUserSid).collect(Collectors.toList()));
            baseNotificationMqBean.setMap(messageContent1);
            baseNotificationMqBean.setEntityId(serviceOrder.getEntityId());

            if (shareDeleteResult.isSuccess() && !CcmTaskStatusEnum.FAILED.getCode().equals(shareDeleteResult.getFdTag())) {
                baseNotificationMqBean.setMsgId(NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_RES_RELEASE_SUCCESS);
            }else {
                baseNotificationMqBean.setMsgId(NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_RES_RELEASE_FAIL);
            }

            rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT, baseNotificationMqBean);
        }
    }
    private void handleDeleteSfs(ShareDeleteResult shareDeleteResult,
                                 ServiceOrder serviceOrderDetail) {
        boolean success = shareDeleteResult.isSuccess();
        Map<String, Object> options = shareDeleteResult.getOptions();
        ResShare resShare = shareRemoteService.selectByPrimaryKey(shareDeleteResult.getId());

        if (success) {
            billingDelete(serviceOrderDetail, options, shareDeleteResult.getId());
            if (CcmTaskStatusEnum.FAILED.getCode().equals(shareDeleteResult.getFdTag())) {
                this.sfsNotification(resShare, serviceOrderDetail
                        , NotificationConsts.ConsoleMsg.ProductMsg.TENANT_SFS_UNSUBSCRIBE, NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_SFS_UNSUBSCRIBE_ERROR);
            }else {
                this.sfsNotification(resShare, serviceOrderDetail
                        , NotificationConsts.ConsoleMsg.ProductMsg.TENANT_SFS_UNSUBSCRIBE, NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_SFS_UNSUBSCRIBE);
            }

            if (ShareSupportClusterTypeEnum.PREDEPLOY.getCode().equals(resShare.getSupportClusterType())) {
                this.syncShare(resShare);
            }
        } else {
            // 修改订单
            serviceOrderDetail.setStatus(OrderStatus.LEV2_REFUSED);
            serviceOrderService.updateByPrimaryKeySelective(serviceOrderDetail);
            resShare.setStatus(MapUtil.getStr(options, "originalStatus"));
            shareRemoteService.updateByPrimaryKey(resShare);

            this.sfsNotification(resShare, serviceOrderDetail
                    , NotificationConsts.ConsoleMsg.ProductMsg.TENANT_SFS_UNSUBSCRIBE_ERROR, NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_SFS_UNSUBSCRIBE_ERROR);
        }
    }

    private void syncShare(ResShare resShare) {

        executorService.execute(() -> {
            Map<String, String> params = new HashMap<>(2);
            params.put("key", "share");

            String companyId = Objects.isNull(BasicInfoUtil.getCurrentOrgSid()) ? "0" : BasicInfoUtil.getCurrentOrgSid().toString();
            log.info("资源同步，当前companyId：[{}]", companyId);
            params.put("companyId", companyId);

            ScheduleHelper.manualSyncTask(resShare.getCloudEnvId(), params, Long.parseLong(resShare.getOwnerId()));
        });
    }

    private void billingDelete(ServiceOrder serviceOrder, Map<String, Object> options, Long id) {
        ServiceOrderDetail detail = CollectionUtil.getFirst(serviceOrder.getDetails());
        if (ChargeType.POST_PAID.equals(detail.getChargeType())) {
            serviceOrder.setStatus(OrderStatus.COMPLETED);
            serviceOrderService.updateByPrimaryKeySelective(serviceOrder);
            return;
        }
        Date now = cn.hutool.core.date.DateUtil.date();
        ResShare resShare = shareRemoteService.selectByPrimaryKey(id);
        if (Objects.isNull(resShare)) {
            serviceOrder.setPayTime(now);
            serviceOrder.setStatus(OrderStatus.LEV2_REJECTED);
            serviceOrderService.updateByPrimaryKeySelective(serviceOrder);
            return;
        }
        BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectByPrimaryKey(
                serviceOrder.getBizBillingAccountId());
        if (ObjectUtils.isEmpty(bizBillingAccount)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_607095064));
        }
        List<InstanceGaapCost> costs = Lists.newArrayList();
        JSONObject jsonObject = JSONUtil.parseObj(detail.getServiceConfig());
        List<ServiceOrderPriceDetail> priceDetails = Lists.newArrayList();
        // 1.检查SFS弹性文件系统续订时所属期间。
        Date endTime = resShare.getEndTime();
        boolean isExpired = now.after(endTime);
        // 2.未过期
        if (!isExpired) {
            BigDecimal resource = jsonObject.getBigDecimal("releaseResource", BigDecimal.ZERO);
            if (!NumberUtil.equals(resource, BigDecimal.ZERO)) {
                priceDetails.add(
                        getPriceDetail("resource", resource, detail.getStartTime(), endTime));
            }
            BigDecimal service = jsonObject.getBigDecimal("releaseService", BigDecimal.ZERO);
            if (!NumberUtil.equals(service, BigDecimal.ZERO)) {
                priceDetails.add(
                        getPriceDetail("service", service, detail.getStartTime(), endTime));
            }
            priceDetails.forEach(pd -> {
                InstanceGaapCost cost = serviceOrderService.getBaseCost(serviceOrder, pd,
                                                                        Convert.toStr(resShare.getUuid()),
                                                                        resShare.getName());
                cost.setRegion(resShare.getAvailabilityZone());
                cost.setConfiguration(StrUtil.concat(true, resShare.getShareProto(), "(",
                                                     Convert.toStr(resShare.getSize()), "GB)"));
                BigDecimal amount = pd.getAmount();
                if (Objects.nonNull(serviceOrder.getChargingType()) && SALE_TYPE.equalsIgnoreCase(serviceOrder.getChargingType())) {
                    cost.setChargingType(SALE_TYPE);
                    amount = BigDecimal.ZERO;
                } else {
                    cost.setChargingType(NORMAL_TYPE);
                }
                serviceOrderService.setUsedCost(bizBillingAccount, cost, amount);
                bizBillingAccount.setBalance(
                        NumberUtil.sub(bizBillingAccount.getBalance(), cost.getCashAmount()));
                bizBillingAccount.setCreditLine(
                        NumberUtil.sub(bizBillingAccount.getCreditLine(), cost.getCreditAmount()));
                bizBillingAccount.setBalanceCash(
                        NumberUtil.sub(bizBillingAccount.getBalanceCash(), cost.getCouponAmount()));
                cost.setPretaxGrossAmount(amount);
                cost.setPretaxAmount(BigDecimalUtil.remainTwoPointAmount(amount));
                cost.setEraseZeroAmount(BigDecimalUtil.getEraseZeroColumn(amount));
                cost.setBizBillingAccount(
                        BeanUtil.toBean(bizBillingAccount, BizBillingAccount.class));
                cost.setEntityId(serviceOrder.getEntityId());
                cost.setEntityName(serviceOrder.getEntityName());
                costs.add(cost);
            });
            bizBillingAccountMapper.updateByPrimaryKeySelective(bizBillingAccount);
            // 修改用户业务标识Tag
            updateUserBusinessTag(bizBillingAccount);
            serviceOrderService.insertCostAndCycleAndDeal(serviceOrder, costs, getOrderIds(resShare.getId(),
                                                                                           ProductCodeEnum.SFS2.getProductCode()));
            serviceOrder.setStatus(OrderStatus.COMPLETED);
            serviceOrder.setPayTime(new Date());
            serviceOrderService.updateByPrimaryKeySelective(serviceOrder);
            return;
        }
        // 3.已过期 未冻结
        boolean isFrozen = "frozen".equalsIgnoreCase(MapUtil.getStr(options, "originalStatus"));
        // if (!isFrozen) {
        Criteria criteria = new Criteria();
        criteria.put("refInstanceIdLike", "\"" + resShare.getId() + "\"");
        criteria.put("orderTypes", Lists.newArrayList("apply", "renew", "modify"));
        criteria.put("orderStatus", "completed");
        criteria.put("productCode", detail.getServiceType());
        criteria.put("isValid", true);
        List<ServiceOrderPriceDetail> lastOrderPriceDetails = serviceOrderPriceDetailMapper.selectByCriteria(
                criteria);
        Set<Long> orderDetailIds = lastOrderPriceDetails.stream()
                                                        .map(ServiceOrderPriceDetail::getOrderDetailId)
                                                        .collect(Collectors.toCollection(LinkedHashSet::new));
        Long maxOrderDetailId = orderDetailIds.stream().max(Long::compare).orElse(-1L);
        lastOrderPriceDetails = lastOrderPriceDetails.stream()
                                                     .filter(d -> Objects.equals(d.getOrderDetailId(), maxOrderDetailId))
                                                     .collect(Collectors.toList());
        Integer overDays = DateUtil.calculateOffDay(endTime,
                                                    isFrozen ? resShare.getFreezingTime() : jsonObject.getDate("releaseNow"));
        for (ServiceOrderPriceDetail priceDetail : lastOrderPriceDetails) {
            InstanceGaapCost cost = serviceOrderService.getBaseCost(serviceOrder,
                                                                    priceDetail, Convert.toStr(resShare.getUuid()), resShare.getName());
            cost.setRegion(resShare.getAvailabilityZone());
            cost.setConfiguration(
                    StrUtil.concat(true, resShare.getShareProto(), "(", Convert.toStr(resShare.getSize()), "GB)"));
            BigDecimal tradePrice = priceDetail.getPrice();
            Calendar calendar = Calendar.getInstance();
            Date priceEndTime = priceDetail.getEndTime();
            Date lastMonthStart = DateUtils.addMonths(priceEndTime, -1);
            calendar.setTime(lastMonthStart);
            BigDecimal finalCost = NumberUtil.div(tradePrice,
                                                  calendar.getActualMaximum(Calendar.DAY_OF_MONTH))
                                             .multiply(BigDecimal.valueOf(overDays)).setScale(5, RoundingMode.HALF_UP);
            serviceOrderService.setUsedCost(bizBillingAccount, cost, finalCost);
            bizBillingAccount.setBalance(
                    NumberUtil.sub(bizBillingAccount.getBalance(), cost.getCashAmount()));
            bizBillingAccount.setCreditLine(
                    NumberUtil.sub(bizBillingAccount.getCreditLine(), cost.getCreditAmount()));
            bizBillingAccount.setBalanceCash(
                    NumberUtil.sub(bizBillingAccount.getBalanceCash(), cost.getCouponAmount()));
                cost.setPretaxGrossAmount(finalCost);
                cost.setPretaxAmount(BigDecimalUtil.remainTwoPointAmount(finalCost));
                cost.setEraseZeroAmount(BigDecimalUtil.getEraseZeroColumn(finalCost));
                cost.setPricingDiscount(BigDecimal.ZERO);
                cost.setBizBillingAccount(
                    BeanUtil.toBean(bizBillingAccount, BizBillingAccount.class));

            if ("resource".equals(priceDetail.getPriceType())) {
                cost.setUsageCount(overDays + "天");
            }
            cost.setEntityId(serviceOrder.getEntityId());
            cost.setEntityName(serviceOrder.getEntityName());
            costs.add(cost);
        }
        bizBillingAccountMapper.updateByPrimaryKeySelective(bizBillingAccount);
        updateUserBusinessTag(bizBillingAccount);
        serviceOrderService.insertCostAndCycleAndDeal(serviceOrder, costs, getOrderIds(resShare.getId(), ProductCodeEnum.SFS2.getProductCode()));
        // }
        serviceOrder.setStatus(OrderStatus.COMPLETED);
        serviceOrder.setPayTime(new Date());
        serviceOrderService.updateByPrimaryKeySelective(serviceOrder);
    }

    private ServiceOrderPriceDetail getPriceDetail(String priceType, BigDecimal price,
        Date starTime, Date endTime) {
        ServiceOrderPriceDetail priceDetail = new ServiceOrderPriceDetail();
        priceDetail.setPriceType(priceType);
        priceDetail.setPrice(price);
        priceDetail.setTradePrice(price);
        priceDetail.setStartTime(starTime);
        priceDetail.setEndTime(endTime);
        priceDetail.setDiscount(BigDecimal.ZERO);
        priceDetail.setCouponAmount(BigDecimal.ZERO);
        priceDetail.setOriginalCost(price);
        priceDetail.setAmount(price);
        return priceDetail;
    }

    private String[] getOrderIds(Long id, String productCode) {
        Criteria criteria = new Criteria();
        criteria.put("refInstanceIdLike", "\"" + id + "\"");
        criteria.put("orderTypes", Lists.newArrayList("apply", "renew", "modify", "expired"));
        criteria.put("orderStatus", "completed");
        criteria.put("productCode", productCode);
        return serviceOrderPriceDetailMapper.selectByCriteria(
                                                    criteria)
                                            .stream()
                                            .map(ServiceOrderPriceDetail::getOrderId)
                                            .map(Convert::toStr)
                                            .distinct()
                                            .toArray(String[]::new);
    }

    @Transactional(rollbackFor = Exception.class)
    public void handleMessage(ShareModifyResult shareModifyResult) {
        String raCallKey = "sfs2:resize:recall:" + shareModifyResult.getUuid();
        String reCallValue = JedisUtil.INSTANCE.get(raCallKey);
        log.info("弹性文件扩缩容 | 回调参数 ： [{}]、[{}]", JsonUtil.toJson(shareModifyResult), reCallValue);
        if (StrUtil.isEmpty(reCallValue)) {
            try {
                log.info("弹性文件扩缩容 开始");
                JedisUtil.INSTANCE.set(raCallKey,
                        cn.hutool.core.date.DateUtil.now());
                ResShare resShare = shareRemoteService.selectByPrimaryKey(shareModifyResult.getId());
                log.info("弹性文件扩缩容 弹性文件：[{}]",JsonUtil.toJson(resShare));
                if (Objects.nonNull(shareModifyResult.getServiceOrderId())) {
                    //根据订单查询订单信息
                    ServiceOrder serviceOrder = serviceOrderService.getById(shareModifyResult.getServiceOrderId());
                    //执行方法的用户
                    setAuthUser(serviceOrder.getUpdatedBy(), serviceOrder.getEntityId());
                    log.info("approveServiceOrder 用户信息：[{}]、[{}]", JSONUtil.toJsonStr(AuthUserHolder.getAuthUser()), JSONUtil.toJsonStr(cn.com.cloudstar.rightcloud.module.support.access.cache.AuthUserHolder.getAuthUser()));

                    if (shareModifyResult.isSuccess()) {
                        log.info("弹性文件扩缩容 订单信息：[{}]",JsonUtil.toJson(serviceOrder));
                        if (ADD.equals(shareModifyResult.getOperate())
                                || BigDecimal.ZERO.compareTo(serviceOrder.getFinalCost()) == 0) {
                            ResChangeRecordDTO resChangeRecordDTO = new ResChangeRecordDTO();
                            resChangeRecordDTO.setOrgSid(serviceOrder.getOrgSid());
                            resChangeRecordDTO.setResType(ProductCodeEnum.SFS2.getProductCode());
                            resChangeRecordDTO.setOwnerId(Convert.toLong(serviceOrder.getOwnerId()));
                            resChangeRecordDTO.setResourceId(resShare.getId() + "");
                            resChangeRecordDTO.setCloudEnvId(resShare.getCloudEnvId());
                            resChangeRecordDTO.setInstanceId(resShare.getUuid());
                            //新大小
                            resChangeRecordDTO.setNewType(shareModifyResult.getSize() + "");
                            //原始大小
                            resChangeRecordDTO.setOriginalType(shareModifyResult.getOldSize() + "");
                            BasicWebUtil.prepareInsertParams(resChangeRecordDTO);
                            log.info("弹性文件扩缩容 | resChangeRecordRemoteService ： {}", JsonUtil.toJson(resChangeRecordDTO));
                            resChangeRecordRemoteService.insertSelective(resChangeRecordDTO);
                            log.info("弹性文件扩缩容 10");
                            billing(serviceOrder, resShare);
                            serviceOrder.setPayTime(new Date());
                            serviceOrder.setStatus(OrderStatus.COMPLETED);
                            JedisUtil.INSTANCE.set("sfs2:resize:day:" + resShare.getId(),
                                    cn.hutool.core.date.DateUtil.now(), 24 * 60 * 60);
                            // 弹性文件服务扩缩容发送站内信
                            this.sendModifyMsg(resShare, shareModifyResult, serviceOrder);
                        }else {
                            rabbitTemplate.convertAndSend(ExpireConstants.EXCHANGE_NAME
                                    , ExpireConstants.SFS_BILLING_ROUTING_KEY,
                                    shareModifyResult, (message) -> {
                                        Long minute = 60L;
                                        Criteria criteria = new Criteria();
                                        criteria.put("configKey", CommonPropertyKeyEnum.SHARE_DEGRAGE_BILLING_WAIT_MINUTES.getCode());
                                        List<SysConfig> sysConfigs = systemConfigService.displaySystemConfigList(criteria);
                                        if (!CollectionUtils.isEmpty(sysConfigs) && StringUtils.isNotEmpty(sysConfigs.get(0).getConfigValue())) {
                                            minute = Long.parseLong(sysConfigs.get(0).getConfigValue());
                                        }
                                        // 给消息设置延迟毫秒值
                                        message.getMessageProperties().setHeader("x-delay", minute * 60000);
                                        return message;
                                    });
                        }
                    } else {
                        if (ADD.equals(shareModifyResult.getOperate())) {
                            serviceOrder.setStatus(OrderStatus.UPGRADE_ERROR);
                        }else {
                            serviceOrder.setStatus(OrderStatus.DEGRADE_ERROR);
                        }
                        String extraAttr = serviceOrder.getExtraAttr();
                        if (StringUtils.isNotEmpty(extraAttr)) {
                            this.rollBackPriceDetails(extraAttr);
                        }
                        //用户订单申请弹性文件操作
                        this.sendModifyErrorMsg(resShare, shareModifyResult, serviceOrder);
                    }
                    log.info("弹性文件扩缩容 | 更新service order ： [{}]", JsonUtil.toJson(serviceOrder));
                    serviceOrderService.updateByPrimaryKeySelective(serviceOrder);
                } else {
                    String productType = ProductCodeEnum.SFS.getProductCode();
                    if (ProductCodeEnum.DME_OSP.getProductCode().equals(resShare.getType())) {
                        productType = ProductCodeEnum.DME_OSP.getProductCode();
                        updateDMEQuota(resShare, shareModifyResult.isSuccess());
                    }
                    Long clusterId = shareRemoteService.getByResourceIdAndClusterType(resShare.getId(), productType);
                    Criteria criteria = new Criteria();
                    criteria.put("clusterId", clusterId);
                    List<ServiceOrder> serviceOrders = serviceOrderService.selectByParams(criteria);
                    resShare.setOwnerId(shareModifyResult.getOpUser());
                    if (shareModifyResult.isSuccess()) {
                        ResChangeRecordDTO resChangeRecordDTO = new ResChangeRecordDTO();
                        resChangeRecordDTO.setOrgSid(Convert.toLong(shareModifyResult.getOrgSid()));
                        resChangeRecordDTO.setResType("HPC_SFS");
                        resChangeRecordDTO.setOwnerId(Convert.toLong(shareModifyResult.getOpUser()));
                        resChangeRecordDTO.setResourceId(resShare.getId() + "");
                        resChangeRecordDTO.setCloudEnvId(resShare.getCloudEnvId());
                        resChangeRecordDTO.setInstanceId(resShare.getUuid());
                        //新大小
                        resChangeRecordDTO.setNewType(shareModifyResult.getSize() + "");
                        //原始大小
                        resChangeRecordDTO.setOriginalType(shareModifyResult.getOldSize() + "");
                        BasicWebUtil.prepareInsertParams(resChangeRecordDTO);
                        log.info("弹性文件扩缩容 | resChangeRecordRemoteService ： {}", JsonUtil.toJson(resChangeRecordDTO));
                        resChangeRecordRemoteService.insertSelective(resChangeRecordDTO);

                        this.sendModifyMsg(resShare, shareModifyResult, serviceOrders.get(0));
                    }else {
                        this.sendModifyErrorMsg(resShare, shareModifyResult, serviceOrders.get(0));
                    }
                }
            } catch (Exception e) {
                log.error("弹性文件扩缩容 异常 ： [{}]", e.getMessage());
            } finally {
                JedisUtil.INSTANCE.del(raCallKey);
                cn.com.cloudstar.rightcloud.module.support.access.cache.AuthUserHolder.clear();
                cn.com.cloudstar.rightcloud.module.support.access.cache.AuthUserHolder.clear();
            }
        }

    }

    private void updateDMEQuota(ResShare resShare,boolean success) {
        Date currentDate = new Date();
        ResOceanstorPQuota resOceanstorPQuota = oceanstorPQuotaRemoteService.selectByPrimaryKey(resShare.getQuotaId());
        if (success) {
            resShare.setStatus(ShareStatus.AVAILABLE);
            resShare.setSize(resOceanstorPQuota.getSpaceHardQuota().intValue());
            resShare.setUpdatedDt(currentDate);
            shareRemoteService.updateByPrimaryKey(resShare);
            resOceanstorPQuota.setStatus(ACTIVE);
            resOceanstorPQuota.setUpdatedDt(currentDate);
            oceanstorPQuotaRemoteService.updateByPrimaryKey(resOceanstorPQuota);
        } else {
            resShare.setStatus(ShareStatus.AVAILABLE);
            resShare.setUpdatedDt(currentDate);
            shareRemoteService.updateByPrimaryKey(resShare);
            resOceanstorPQuota.setStatus(ACTIVE);
            resOceanstorPQuota.setSpaceHardQuota(Long.valueOf(resShare.getSize()));
            resOceanstorPQuota.setUpdatedDt(currentDate);
            oceanstorPQuotaRemoteService.updateByPrimaryKey(resOceanstorPQuota);
        }
    }

    /**
     * 回滚变配失败的priceDetail endTime
     * @param extraAttr
     */
    private void rollBackPriceDetails(String extraAttr) {
        log.info("NotifyListener-rollBackPriceDetails-extraAttr:[{}]", extraAttr);
        com.alibaba.fastjson.JSONObject jsonObject = JSON.parseObject(extraAttr);
        Set<Entry<String, Object>> entrySet = jsonObject.entrySet();
        entrySet.forEach(entry -> {
            ServiceOrderPriceDetail serviceOrderPriceDetail = serviceOrderPriceDetailMapper.selectByPrimaryKey(Long.parseLong(entry.getKey()));
            Date endTime = new Date(Long.parseLong(String.valueOf(entry.getValue())));
            serviceOrderPriceDetail.setEndTime(endTime);
            serviceOrderPriceDetailMapper.updateByPrimaryKeySelective(serviceOrderPriceDetail);
        });
    }

    private void sendModifyErrorMsg(ResShare resShare, ShareModifyResult shareModifyResult, ServiceOrder serviceOrder) {
        User user = userMapper.selectByPrimaryKey(Long.valueOf(resShare.getOwnerId()));
        HashMap<String, String> messageContent = new HashMap<>(5);
        messageContent.put("sfsName", resShare.getName());
        messageContent.put("userAccount", user.getAccount());
        messageContent.put("orderSn", serviceOrder.getOrderSn());
        try {
            Thread.sleep(10000);
        } catch (InterruptedException e) {
            e.printStackTrace();
            Thread.currentThread().interrupt();
        }
        String msg1, msg2 = null;
        if (!"add".equals(shareModifyResult.getOperate())) {
            msg1 = NotificationConsts.ConsoleMsg.ProductMsg.TENANT_SFS_SCALE_DOWN_ERROR;
            msg2 = NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_SFS_SCALE_DOWN_ERROR;
        } else {
            msg1 = NotificationConsts.ConsoleMsg.ProductMsg.TENANT_SFS_SCALE_UP_ERROR;
            msg2 = NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_SFS_SCALE_UP_ERROR;
        }
        sendMessage(user, messageContent, msg1, msg2,serviceOrder.getEntityId());
    }

    private void sendModifyMsg(ResShare resShare, ShareModifyResult shareModifyResult, ServiceOrder serviceOrder) {
        executorService.execute(() -> {
            User user = userMapper.selectByPrimaryKey(Long.valueOf(resShare.getOwnerId()));
            HashMap<String, String> messageContent = new HashMap<>(5);
            messageContent.put("sfsName", resShare.getName());
            messageContent.put("userAccount", user.getAccount());
            messageContent.put("preSize", String.valueOf(shareModifyResult.getOldSize()));
            messageContent.put("size", String.valueOf(shareModifyResult.getSize()));
            messageContent.put("orderSn", serviceOrder.getOrderSn());
            try {
                Thread.sleep(10000);
            } catch (InterruptedException e) {
                e.printStackTrace();
                Thread.currentThread().interrupt();
            }
            String msg1, msg2 = null;
            if (!"add".equals(shareModifyResult.getOperate())) {
                msg1 = NotificationConsts.ConsoleMsg.ProductMsg.TENANT_SFS_SCALE_DOWN_SUCCESS;
                msg2 = NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_SFS_SCALE_DOWN_SUCCES;
            } else {
                msg1 = NotificationConsts.ConsoleMsg.ProductMsg.TENANT_SFS_SCALE_UP_SUCCESS;
                msg2 = NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_SFS_SCALE_UP_SUCCES;
            }

            sendMessage(user, messageContent, msg1, msg2,serviceOrder.getEntityId());
        });
    }

    /**
     * 发送信息
     * @param
     */
    private void sendMessage(User user, Map<String, String> messageContent, String msg1, String msg2,Long entityId) {

        messageContent.put("userAccount", user.getAccount());
        NotificationUtil.assembleBaseMessageContent(messageContent);

        // 系统名称
        if (msg1!=null) {
            try {
                BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
                baseNotificationMqBean.setMsgId(msg1);
                baseNotificationMqBean.setMap(messageContent);
                baseNotificationMqBean.getImsgUserIds().add(user.getUserSid());
                baseNotificationMqBean.setEntityId(entityId);
                rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT, baseNotificationMqBean);
            }catch (Exception e){
                log.error("共享资源池发送信息异常：",e);
            }
        }
        //运营管理员发送消息

        if (msg2!=null) {
            List<User> adminstrators = userMapper.findAdminstratorsByEntityId(entityId);
            if(adminstrators !=null){

                try {
                    BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
                    baseNotificationMqBean.getImsgUserIds().addAll(adminstrators.stream().map(User::getUserSid).collect(Collectors.toSet()));

                    baseNotificationMqBean.setMsgId(msg2);
                    baseNotificationMqBean.setEntityId(entityId);
                    baseNotificationMqBean.setMap(messageContent);
                    rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.OTHER, baseNotificationMqBean);
                }catch (Exception e){
                    log.error("共享资源池发送信息异常：",e);
                }
            }
        }
    }
    private void billing(ServiceOrder serviceOrder, ResShare resShare) {
        List<ServiceOrderDetail> details = serviceOrderDetailMapper.selectByParams(new Criteria("orderId", serviceOrder.getId()));
        if (CollectionUtil.isEmpty(details)) {
            log.error("弹性文件扩缩容 异常，详情为空！");
            return;
        }
        serviceOrder.setDetails(details);
        BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectByPrimaryKey(
                serviceOrder.getBizBillingAccountId());
        List<InstanceGaapCost> costs = Lists.newArrayList();
        List<ServiceOrderPriceDetail> priceDetails = serviceOrderPriceDetailMapper.selectByParams(new Criteria("orderSn", serviceOrder.getOrderSn()));

        if (CollectionUtil.isEmpty(priceDetails)) {
            log.error("弹性文件扩缩容 异常，价格详情为空！");
            return;
        }
        priceDetails.forEach(priceDetail -> {
            InstanceGaapCost cost = serviceOrderService.getBaseCost(serviceOrder, priceDetail,
                    Convert.toStr(resShare.getUuid()),
                    resShare.getName());
            cost.setRegion(resShare.getAvailabilityZone());
            cost.setChargingType(serviceOrder.getChargingType());
            cost.setConfiguration(
                    StrUtil.concat(true, resShare.getShareProto(), "(", Convert.toStr(resShare.getSize()), "GB)"));
            BigDecimal amount = priceDetail.getAmount();
            serviceOrderService.setUsedCost(bizBillingAccount, cost, amount);
            bizBillingAccount.setBalance(
                    NumberUtil.sub(bizBillingAccount.getBalance(), cost.getCashAmount()));
            bizBillingAccount.setCreditLine(
                    NumberUtil.sub(bizBillingAccount.getCreditLine(), cost.getCreditAmount()));
            bizBillingAccount.setBalanceCash(
                    NumberUtil.sub(bizBillingAccount.getBalanceCash(), cost.getCouponAmount()));
            cost.setPretaxGrossAmount(serviceOrder.getOriginalCost());
            //四舍五入
            cost.setEraseZeroAmount(BigDecimalUtil.getEraseZeroColumn(amount));
            cost.setPretaxAmount(BigDecimalUtil.remainTwoPointAmount(amount));
            // 优惠金额 = 原始金额 - 优惠后金额 - 抹零金额
            cost.setPricingDiscount(cost.getPretaxGrossAmount().subtract(cost.getPretaxAmount()).subtract(cost.getEraseZeroAmount()));
            cost.setBizBillingAccount(
                    BeanUtil.toBean(bizBillingAccount, BizBillingAccount.class));
            cost.setEntityId(serviceOrder.getEntityId());
            cost.setEntityName(serviceOrder.getEntityName());
            costs.add(cost);
            priceDetail.setPayBalance(cost.getCashAmount());
            priceDetail.setPayBalanceCash(cost.getCouponAmount());
            priceDetail.setPayCreditLine(cost.getCreditAmount());
            serviceOrderPriceDetailMapper.updateByPrimaryKeySelective(priceDetail);
        });
        bizBillingAccountMapper.updateByPrimaryKeySelective(bizBillingAccount);
        //修改用户业务标识Tag
        updateUserBusinessTag(bizBillingAccount);
        serviceOrderService.insertCostAndCycleAndDeal(serviceOrder, costs);
        log.info("弹性文件扩缩容 | 更新billing");
    }

    /**
     * 设置Auth信息
     * @param account
     * @param entityId
     */
    private cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser setAuthUser(String account, Long entityId) {
        cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser supportAuth = BasicInfoUtil.getUserInfoByAccount(account);
        supportAuth.setEntityId(entityId);
        supportAuth.setRemark(ModuleTypeConstants.FROM_COMMON);
        cn.com.cloudstar.rightcloud.module.support.access.cache.AuthUserHolder.setAuthUser(supportAuth);

        cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser admin = cn.com.cloudstar.rightcloud.common.util.BeanConvertUtil.convert(supportAuth, cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser.class);
        admin.setEntityId(entityId);
        admin.setRemark(ModuleTypeConstants.FROM_COMMON);
        cn.com.cloudstar.rightcloud.module.support.access.cache.AuthUserHolder.setAuthUser(admin);
        cn.com.cloudstar.rightcloud.module.support.access.cache.AuthUserHolder.setOrg(
                cn.com.cloudstar.rightcloud.common.util.BeanConvertUtil.convert(BasicInfoUtil.getCurrentOrgInfo(admin.getOrgSid()),
                        cn.com.cloudstar.rightcloud.module.support.access.pojo.Org.class));
        return admin;
    }

    /**
     * 弹性文件发送站内信
     * @param resShare
     * @param serviceOrder
     * @param userNotification 发给用户的模板名称
     * @param operationNotification 发给运营管理员的模板名称
     */
    private void sfsNotification(ResShare resShare, ServiceOrder serviceOrder
            , String userNotification, String operationNotification) {
        executorService.execute(() -> {
            User user = userMapper.selectByPrimaryKey(Long.valueOf(resShare.getOwnerId()));
            HashMap<String, String> messageContent = new HashMap<>(2);
            messageContent.put("sfsName", resShare.getName());
            messageContent.put("userAccount", user.getAccount());
            messageContent.put("orderSn", serviceOrder.getOrderSn());
            try {
                Thread.sleep(10000);
            } catch (InterruptedException e) {
                e.printStackTrace();
                Thread.currentThread().interrupt();
            }

            BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
            baseNotificationMqBean.setMsgId(userNotification);
            baseNotificationMqBean.getImsgUserIds().add(user.getUserSid());
            baseNotificationMqBean.setMap(messageContent);
            baseNotificationMqBean.setEntityId(serviceOrder.getEntityId());
            rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT, baseNotificationMqBean);
            //发送站内信给租户管理员


            List<User> adminstrators = userMapper.findAdminstratorsByEntityId(serviceOrder.getEntityId());

            baseNotificationMqBean = new BaseNotificationMqBean();
            baseNotificationMqBean.setMsgId(operationNotification);
            baseNotificationMqBean.getImsgUserIds().addAll(adminstrators.stream().map(User::getUserSid).collect(Collectors.toList()));
            baseNotificationMqBean.setMap(messageContent);
            baseNotificationMqBean.setEntityId(serviceOrder.getEntityId());
            rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT, baseNotificationMqBean);


        });
    }
}
