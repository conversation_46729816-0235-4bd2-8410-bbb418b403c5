package cn.com.cloudstar.rightcloud.oss.module.customer.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.mongodb.BasicDBObject;
import com.mongodb.client.ListIndexesIterable;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.model.IndexModel;

import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import io.seata.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.common.constants.type.ModelartsPoolAllocateType;
import cn.com.cloudstar.rightcloud.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.common.util.PropertiesUtil;

import cn.com.cloudstar.rightcloud.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.core.pojo.dto.audit.ServiceOrder;
import cn.com.cloudstar.rightcloud.core.pojo.dto.audit.ServiceOrderDetail;
import cn.com.cloudstar.rightcloud.core.pojo.dto.audit.ServiceOrderPriceDetail;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.SysConfig;
import cn.com.cloudstar.rightcloud.oss.common.constants.status.OrderStatus;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.OrderType;
import cn.com.cloudstar.rightcloud.oss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.SfProductEnum;
import cn.com.cloudstar.rightcloud.oss.common.pojo.BizAiResourceOverview;
import cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.oss.module.analysis.service.IBizAiResourceOverviewService;
import cn.com.cloudstar.rightcloud.oss.module.bigscrean.bean.ResUsageResult;
import cn.com.cloudstar.rightcloud.oss.module.bigscrean.bean.model.ModelArtsPoolAllocate;
import cn.com.cloudstar.rightcloud.oss.module.bigscrean.bean.model.NpuCardInfo;
import cn.com.cloudstar.rightcloud.oss.module.bigscrean.dao.BizResUsageHourReportMapper;
import cn.com.cloudstar.rightcloud.oss.module.customer.bean.ComputationalPowerResponse;
import cn.com.cloudstar.rightcloud.oss.module.customer.bean.PowerResult;
import cn.com.cloudstar.rightcloud.oss.module.customer.bean.ResourceResponse;
import cn.com.cloudstar.rightcloud.oss.module.customer.service.BizResourceUsageService;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.response.DescribeProductResourceResponse;
import cn.com.cloudstar.rightcloud.oss.module.order.dao.ServiceOrderDetailMapper;
import cn.com.cloudstar.rightcloud.oss.module.order.dao.ServiceOrderMapper;
import cn.com.cloudstar.rightcloud.oss.module.order.dao.ServiceOrderPriceDetailMapper;
import cn.com.cloudstar.rightcloud.oss.module.order.dao.ServiceOrderResourceRefMapper;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.bean.model.SfProductResource;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.dao.SfProductResourceMapper;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.ma.ResMaFlavorVO;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.ma.ResMaPoolVO;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.ma.MaRemoteService;


/**
 * @Classname BizResourceUsageServiceImpl
 * @Description:
 * @Date 2023/8/28 16:37
 * @Author:mwy
 */
@Service
@Slf4j
public class BizResourceUsageServiceImpl implements BizResourceUsageService {
    /**
     * 算力卡转换除系数
     */
    private static final String SCREEN_AI_RESOURCE_COEFFICIENT_DIVIDE = "screen.airesource.coefficient.divide";
    /**
     * 算力卡转换乘系数
     */
    private static final String SCREEN_AI_RESOURCE_COEFFICIENT_MULTIPLY = "screen.airesource.coefficient.multiply";



    @Autowired
    private SfProductResourceMapper sfProductResourceMapper;


    @Autowired
    private ServiceOrderResourceRefMapper serviceOrderResourceRefMapper;

    @Autowired
    private ServiceOrderDetailMapper serviceOrderDetailMapper;

    @Autowired
    private BizResUsageHourReportMapper bizResUsageHourReportMapper;

    @Autowired
    private ServiceOrderMapper serviceOrderMapper;
    @Autowired
    private IBizAiResourceOverviewService bizAiResourceOverviewService;
    @DubboReference
    private MaRemoteService maRemoteService;
    @Autowired
    private ServiceOrderPriceDetailMapper serviceOrderPriceDetailMapper;
    @Autowired
    private MongoTemplate mongoTemplate;


    /**
     * @param
     */
    @Override
    public ComputationalPowerResponse resOccListP() {

        ComputationalPowerResponse response = computeAllocatedNpu();
        //如果大屏接口获取算力为空，则计算当前资源使用
        if (response == null) {
            response = computeCurrentResourceUsage();
        }
        //获取资源总量P
        BigDecimal totalP = BigDecimal.ZERO;
        String total = PropertiesUtil.getProperty("screen.airesource.power");
        if (StringUtil.isNotEmpty(total)) {
            totalP = new BigDecimal(total);
        }
        response.setTotal(totalP);
        return response;
    }

    /**
     * 大屏接口获取算力
     * @return
     */
    private ComputationalPowerResponse computeAllocatedNpu() {
        try {
            long fiveMinutes = 60L * 1000L * 5L;
            long endTime = System.currentTimeMillis();


            long startTime = endTime - fiveMinutes;
            List<ModelArtsPoolAllocate> publicModelArts = getModelArts(null, startTime, endTime);
            //未查询到数据查询，上个5分钟数据，BUG：#66782 优化显示
            if (CollectionUtils.isEmpty(publicModelArts)) {
                publicModelArts = getModelArts(null, startTime-fiveMinutes, startTime);
            }
            if (CollectionUtils.isEmpty(publicModelArts)) {
                return null;
            }
            ComputationalPowerResponse response = getNpuCardInfoAll(publicModelArts);

            int exlusiveNum = 0;
            int shareNum = 0;
            List<BizAiResourceOverview> bizAiResourceOverviews = bizAiResourceOverviewService.listAll();
            BizAiResourceOverview resourceOverview = CollectionUtil.getFirst(bizAiResourceOverviews);
            if (resourceOverview != null) {
                exlusiveNum = resourceOverview.getCurrentPrivateJobCount().intValue();
                shareNum = resourceOverview.getCurrentPublicJobCount().intValue();
            }
            //构造返回值
            response.setExclusiveNum(exlusiveNum);
            response.setShareNum(shareNum);
            return response;
        } catch (Exception e) {
            log.error("总览当前算力-异常:",e);
            return null;
        }
    }

    private ComputationalPowerResponse getNpuCardInfoAll(List<ModelArtsPoolAllocate> modelArtsPoolAllocateList) {
        //算力转换系数
        String sysConfigDivide = serviceOrderMapper.selectConfig(SCREEN_AI_RESOURCE_COEFFICIENT_DIVIDE);
        String sysConfigMultiply = serviceOrderMapper.selectConfig(SCREEN_AI_RESOURCE_COEFFICIENT_MULTIPLY);
        BigDecimal divide = BigDecimal.valueOf(8);
        BigDecimal multiply = BigDecimal.valueOf(2.5);
        if (StringUtils.isNotEmpty(sysConfigDivide)) {
            divide = Objects.nonNull(sysConfigDivide)
                && !NumberUtil.equals(new BigDecimal(sysConfigDivide),BigDecimal.ZERO)? new BigDecimal(sysConfigDivide) : divide;
        }
        if (StringUtils.isNotEmpty(sysConfigMultiply)) {
            multiply = Objects.nonNull(sysConfigMultiply) ? new BigDecimal(
                sysConfigMultiply) : multiply;
        }


        BigDecimal privateAllocate = BigDecimal.ZERO;
        BigDecimal publicAllocate = BigDecimal.ZERO;
        //      找到notebook和推理的公共资源池id
        String configPubPoolIds = cn.com.cloudstar.rightcloud.oss.common.util.PropertiesUtil.getProperty(
            "screen.airesource.public.poolids");
        List<String> poolIds = Arrays.stream(configPubPoolIds.split(StrUtil.COMMA)).collect(Collectors.toList());
        for (ModelArtsPoolAllocate modelArtsPoolAllocate : modelArtsPoolAllocateList) {
            //逻辑池不统计
            if (ModelartsPoolAllocateType.LOGICAL.equalsIgnoreCase(modelArtsPoolAllocate.getType())) {
                continue;
            }
            double capacity = Double.parseDouble(modelArtsPoolAllocate.getCapacity());
            double allocate = Double.parseDouble(modelArtsPoolAllocate.getAllocate());
            boolean condition = (capacity >= 0 && allocate >= 0);
            boolean otherCondition = (capacity >= 0 && allocate < 0);
            if (condition || otherCondition) {
                if ("private".equals(modelArtsPoolAllocate.getPoolType())) {
                    //大屏定时任务修改增加了算力转换系数和npu规格
                    if(Objects.nonNull(modelArtsPoolAllocate.getConvertRatio())){
                        privateAllocate = privateAllocate.add(BigDecimal.valueOf(capacity).multiply(modelArtsPoolAllocate.getConvertRatio()));
                    }else{
                        privateAllocate = privateAllocate.add(BigDecimal.valueOf(capacity));
                    }
                } else {
                    if (!poolIds.contains(modelArtsPoolAllocate.getPoolId())) {
                        if (allocate > capacity) {
                            //大屏定时任务修改增加了算力转换系数和npu规格
                            if(Objects.nonNull(modelArtsPoolAllocate.getConvertRatio())){
                                publicAllocate = publicAllocate.add(BigDecimal.valueOf(capacity).multiply(modelArtsPoolAllocate.getConvertRatio()));
                            }else{
                                publicAllocate = publicAllocate.add(BigDecimal.valueOf(capacity));
                            }
                        } else {
                            //大屏定时任务修改增加了算力转换系数和npu规格
                            if(Objects.nonNull(modelArtsPoolAllocate.getConvertRatio())){
                                publicAllocate = publicAllocate.add(BigDecimal.valueOf(allocate).multiply(modelArtsPoolAllocate.getConvertRatio()));
                            }else{
                                publicAllocate = publicAllocate.add(BigDecimal.valueOf(allocate));
                            }
                        }
                    } else {
                        if (allocate < 0) {
                            continue;
                        } else  {
                            //大屏定时任务修改增加了算力转换系数和npu规格
                            if(Objects.nonNull(modelArtsPoolAllocate.getConvertRatio())){
                                publicAllocate = publicAllocate.add(BigDecimal.valueOf(capacity).multiply(modelArtsPoolAllocate.getConvertRatio()));
                            }else{
                                publicAllocate = publicAllocate.add(BigDecimal.valueOf(capacity));
                            }
                        }

                    }
                }
            }

        }
        privateAllocate = privateAllocate.divide(divide, 2, RoundingMode.HALF_UP).multiply(multiply).setScale(2,BigDecimal.ROUND_DOWN);
        publicAllocate = publicAllocate.divide(divide, 2, RoundingMode.HALF_UP).multiply(multiply).setScale(2,BigDecimal.ROUND_DOWN);
        //构造返回值
        ComputationalPowerResponse response = ComputationalPowerResponse.builder()
            .exclusive(privateAllocate.longValue())
            .share(publicAllocate.longValue())
            .build();
        return response;
    }


    private List<ModelArtsPoolAllocate> getModelArts(String poolType, Long startTime, Long endTime) {
        Date startDate = new Date(startTime);
        Date endDate = new Date(endTime);
        org.springframework.data.mongodb.core.query.Criteria criteria1 = org.springframework.data.mongodb.core.query.Criteria.where("createDt").gt(startDate);
        org.springframework.data.mongodb.core.query.Criteria criteria2 = org.springframework.data.mongodb.core.query.Criteria.where("createDt").lt(endDate);
        org.springframework.data.mongodb.core.query.Criteria criteria3 = new org.springframework.data.mongodb.core.query.Criteria();
        if (StringUtil.isNotEmpty(poolType)) {
            criteria3 = org.springframework.data.mongodb.core.query.Criteria.where("poolType").is(poolType);
        }
        CompletableFuture.runAsync(() -> {
            //判断索引是否存在
            List<String> indexList = Arrays.asList("createDt");
            createItemIndex(indexList);
        });

        org.springframework.data.mongodb.core.query.Criteria criteria = new org.springframework.data.mongodb.core.query.Criteria().andOperator(criteria1, criteria2, criteria3);
        Query query = new Query().addCriteria(criteria);
        List<ModelArtsPoolAllocate> modelArtsPoolAllocates = new ArrayList<>();
        MongoCursor<Document> modelArtsCursor = mongoTemplate.getCollection("modelarts_pool_allocate")
            .find(query.getQueryObject())
            .noCursorTimeout(true)
            .cursor();
        while (modelArtsCursor.hasNext()) {
            ModelArtsPoolAllocate modelArtsPoolAllocate = BeanUtil.toBean(modelArtsCursor.next(),
                ModelArtsPoolAllocate.class);
            modelArtsPoolAllocates.add(modelArtsPoolAllocate);
        }
        modelArtsCursor.close();
        return modelArtsPoolAllocates;
    }

    private void createItemIndex(List<String> indexList) {
        log.info("创建索引-BizResourceUsageServiceImpl.createItemIndex-开始");
        long startTime = System.currentTimeMillis();
        //创建索引
        try {
            Map<String,Boolean> indexExsitMap = new HashMap<>();
            for (String index : indexList) {
                indexExsitMap.put(index,Boolean.FALSE);
            }
            ListIndexesIterable<Document> itemIndexIter = mongoTemplate.getCollection("modelarts_pool_allocate").listIndexes();
            for (Document document : itemIndexIter) {
                if (document.get("key") != null) {
                    Document keyDoc = (Document) document.get("key");
                    if (keyDoc.size() < 2) {
                        for (Entry<String, Boolean> entry : indexExsitMap.entrySet()) {
                            Object o = keyDoc.get(entry.getKey());
                            if (o != null) {
                                entry.setValue(Boolean.TRUE);
                            }
                        }
                    }
                }
            }
            log.info("创建索引-BizResourceUsageServiceImpl.createItemIndex-[{}]", JSONObject.toJSONString(indexExsitMap));
            if (indexExsitMap.values().stream().anyMatch(v ->!Boolean.TRUE.equals(v))) {
                // 创建索引
                List<IndexModel> ims = new ArrayList<>();
                for (Entry<String, Boolean> entry : indexExsitMap.entrySet()) {
                    if (!Boolean.TRUE.equals(entry.getValue())) {
                        BasicDBObject indexModel = new BasicDBObject();
                        indexModel.put(entry.getKey(),-1);
                        ims.add(new IndexModel(indexModel));
                    }
                }

                mongoTemplate.getCollection("modelarts_pool_allocate").createIndexes(ims);
            }

        } catch (Exception e) {
            log.error("创建索引异常-BizResourceUsageServiceImpl.createItemIndex-异常[{}]", e.getMessage());
        }
        log.info("创建索引-BizResourceUsageServiceImpl.createItemIndex-执行时间[{}]s", (System.currentTimeMillis() - startTime) / 1000);
    }

    /**
     * 通过话单计算当前使用算力
     * @return
     */
    private ComputationalPowerResponse computeCurrentResourceUsage() {
        Date current = new Date();
        current = DateUtils.addHours(current, -3);
        String currentDay = DateFormatUtils.format(current, "yyyyMMddHHmmss");

        String sysConfigDivide = serviceOrderMapper.selectConfig(SCREEN_AI_RESOURCE_COEFFICIENT_DIVIDE);
        String sysConfigMultiply = serviceOrderMapper.selectConfig(SCREEN_AI_RESOURCE_COEFFICIENT_MULTIPLY);
        BigDecimal divide = BigDecimal.valueOf(8);
        BigDecimal multiply = BigDecimal.valueOf(2.5);
        if (StringUtils.isNotEmpty(sysConfigDivide)) {
            divide = Objects.nonNull(sysConfigDivide)
                && !NumberUtil.equals(new BigDecimal(sysConfigDivide),BigDecimal.ZERO)? new BigDecimal(sysConfigDivide) : divide;
        }
        if (StringUtils.isNotEmpty(sysConfigMultiply)) {
            multiply = Objects.nonNull(sysConfigMultiply) ? new BigDecimal(
                sysConfigMultiply) : multiply;
        }
        //通过话单获取共享当前使用情况（卡）
        List<PowerResult> shareResult = sfProductResourceMapper.getShareResult(currentDay, "MODELARTS");
        Long shareCards = shareResult.stream().mapToLong(PowerResult::getCards).sum();
        shareCards = BigDecimal.valueOf(shareCards).multiply(multiply).divide(divide)
                               .setScale(0, BigDecimal.ROUND_HALF_UP)
                               .longValue();

        //通过订单获取专属使用情况（卡）
        List<PowerResult> exclusiveResult = sfProductResourceMapper.getExclusiveResult(current);
        Long exclusiveCards = exclusiveResult.stream().mapToLong(PowerResult::getCards).sum();
        exclusiveCards = BigDecimal.valueOf(exclusiveCards).multiply(multiply).divide(divide)
            .setScale(0, BigDecimal.ROUND_HALF_UP)
            .longValue();
        int exlusiveNum = 0;
        int shareNum = 0;
        List<BizAiResourceOverview> bizAiResourceOverviews = bizAiResourceOverviewService.listAll();
        BizAiResourceOverview resourceOverview = CollectionUtil.getFirst(bizAiResourceOverviews);
        if (resourceOverview == null) {
            List<PowerResult> result = sfProductResourceMapper.getShareResult(currentDay, "DRP");
            List<PowerResult> collect =
                result.stream().filter(a -> a.getCards() != 0 || a.getCounts() != 0).collect(Collectors.toList());
            exlusiveNum = collect.size();
            //通过话单获取共享作业数
            List<PowerResult> powerResults =
                shareResult.stream().filter(a -> a.getCards() != 0 || a.getCounts() != 0).collect(Collectors.toList());
            shareNum = powerResults.size();
        } else {
            exlusiveNum = resourceOverview.getCurrentPrivateJobCount().intValue();
            shareNum = resourceOverview.getCurrentPublicJobCount().intValue();
        }
        //构造返回值
        ComputationalPowerResponse response = ComputationalPowerResponse.builder()
            .exclusive(exclusiveCards)
            .ExclusiveNum(exlusiveNum)
            .share(shareCards)
            .shareNum(shareNum)
            .build();
        return response;
    }

    @Override
    public ResourceResponse totalDRP() {
        Criteria criteria = new Criteria();
        criteria.put("product_type", "DRP");
        criteria.put("notInStatus", Arrays.asList("pending"));
        List<SfProductResource> sfProductResources = sfProductResourceMapper.selectByParams(criteria);
        List<DescribeProductResourceResponse> responseList = BeanConvertUtil.convert(sfProductResources, DescribeProductResourceResponse.class);
        Date now = new Date();
        for (DescribeProductResourceResponse record : responseList) {
            Date endTime = record.getEndTime();
            ServiceOrderDetail orderDetail = serviceOrderDetailMapper.selectByOrderId(record.getServiceOrderId());
            Assert.notNull(orderDetail);
            record.setChargeType(orderDetail.getChargeType());
            //如果是按量计费，状态不能是已过期，应该是已退订
            if (SfProductEnum.NORMAL.getStatus().equals(record.getStatus()) && Objects.nonNull(endTime)
                    && endTime.before(now)) {
                //计费模式按量计费,不是已过期
                if (!"PostPaid".equals(record.getChargeType())) {
                    record.setStatus(SfProductEnum.EXPIRED.getStatus());
                } else {
                    record.setStatus(SfProductEnum.UNSUBSCRIBED.getStatus());
                }
            }
            record.setQuantity(orderDetail.getQuantity());

        }

        return setResponse(responseList, now);
    }

    @Override
    public ResourceResponse totalShare() {
        BigDecimal moduleArtsList = bizResUsageHourReportMapper.findModuleArtsList();
        if (Objects.isNull(moduleArtsList)){
            moduleArtsList = BigDecimal.ZERO;
        }
        BigDecimal bigDecimal =moduleArtsList.setScale(0, BigDecimal.ROUND_HALF_UP);
        int moduleArtsListSize = 0;
        List<BizAiResourceOverview> bizAiResourceOverviews = bizAiResourceOverviewService.listAll();
        BizAiResourceOverview resourceOverview = CollectionUtil.getFirst(bizAiResourceOverviews);
        if (resourceOverview == null) {
            moduleArtsListSize = bizResUsageHourReportMapper.findModuleArtsListSize();
        } else {
            moduleArtsListSize = (int)(resourceOverview.getTotalTrainingPublicJobCount()+resourceOverview.getTotalNotebookPublicJobCount());
        }
        return ResourceResponse.builder().cards(bigDecimal).counts(moduleArtsListSize).build();
    }


    private ResourceResponse setResponse(List<DescribeProductResourceResponse> record, Date now) {
        BigDecimal exclusive = BigDecimal.ZERO;

        int drpJobCount = 0;
        List<BizAiResourceOverview> bizAiResourceOverviews = bizAiResourceOverviewService.listAll();
        BizAiResourceOverview resourceOverview = CollectionUtil.getFirst(bizAiResourceOverviews);
        if (resourceOverview == null) {
            List<ResUsageResult> drpList = bizResUsageHourReportMapper.findDRPList();
            ArrayList<ResUsageResult> collect = drpList
                .stream()
                .collect(
                    Collectors.collectingAndThen(
                        Collectors.toCollection(
                            () -> new TreeSet<>(
                                Comparator.comparing(tc -> tc.getResourceId())
                            )
                        ), ArrayList::new
                    )
                );
            drpJobCount = collect.size();
        } else {
            drpJobCount = (int)(resourceOverview.getTotalTrainingPrivateJobCount()+resourceOverview.getTotalNotebookPrivateJobCount());
        }
        List<ResMaFlavorVO> resMaFlavorVOS = maRemoteService.maFlaerQuery();
        Map<String, String> npuCardMap = resMaFlavorVOS.stream().filter(resMaFlavorVO -> Objects.nonNull(resMaFlavorVO.getNpuNum())).collect(Collectors.toMap(ResMaFlavorVO::getSpecCode, ResMaFlavorVO::getNpuNum, (oldValue, newValue) -> oldValue));

        for (DescribeProductResourceResponse product : record) {

            ResMaPoolVO resMaPoolVO = maRemoteService.getResMaPoolById(product.getClusterId());
            String npuCard = npuCardMap.get(resMaPoolVO.getFlavor());
            if (StringUtils.isEmpty(npuCard)) {
                npuCard = "0";
            }
            Date startTime = product.getStartTime();
            Date endTime = product.getEndTime();
            if (startTime == null || endTime == null) {
                continue;
            }
            Integer quantity = product.getQuantity();

            //查询扩缩容订单计算扩缩容节点卡时
            List<ServiceOrderPriceDetail> priceDetails = retrieveUpgradeAndDegradeOrderPriceDetails(product);


            if (SfProductEnum.EXPIRED.getStatus().equals(product.getStatus()) ||
                    SfProductEnum.UNSUBSCRIBED.getStatus().equals(product.getStatus())) {
                Long dif = endTime.getTime() - startTime.getTime();
                BigDecimal hour = BigDecimal.valueOf(dif).divide(BigDecimal.valueOf(1000 * 60 * 60), 6, BigDecimal.ROUND_HALF_UP);
                BigDecimal multiply = hour.multiply(BigDecimal.valueOf(quantity)).multiply(new BigDecimal(npuCard));
                exclusive = exclusive.add(multiply);
                //计算扩缩容卡时
                exclusive = calculateExclusiveNpuCardHour(exclusive, npuCard, endTime, priceDetails);

            } else {
                Long dif = now.getTime() - startTime.getTime();
                BigDecimal hour = BigDecimal.valueOf(dif).divide(BigDecimal.valueOf(1000 * 60 * 60), 6, BigDecimal.ROUND_HALF_UP);
                BigDecimal multiply = hour.multiply(BigDecimal.valueOf(quantity)).multiply(new BigDecimal(npuCard));
                exclusive = exclusive.add(multiply);
                //计算扩缩容卡时
                exclusive = calculateExclusiveNpuCardHour(exclusive, npuCard, now, priceDetails);
            }
        }
        return ResourceResponse.builder().cards(exclusive.setScale(0, BigDecimal.ROUND_HALF_UP)).counts(drpJobCount).build();
    }

    /**
     *  计算扩缩容卡时
     * @param exclusive
     * @param npuCard
     * @param endTime
     * @param priceDetails
     * @return
     */
    private BigDecimal calculateExclusiveNpuCardHour(BigDecimal exclusive, String npuCard, Date endTime, List<ServiceOrderPriceDetail> priceDetails) {
        if (CollectionUtil.isNotEmpty(priceDetails)) {
            for (ServiceOrderPriceDetail priceDetail : priceDetails) {
                Long difTime = endTime.getTime() - priceDetail.getStartTime().getTime();
                BigDecimal difTimeHour = BigDecimal.valueOf(difTime).divide(BigDecimal.valueOf(1000 * 60 * 60), 6, BigDecimal.ROUND_HALF_UP);
                BigDecimal cardHour = difTimeHour.multiply(BigDecimal.valueOf(priceDetail.getQuantity()).multiply(new BigDecimal(npuCard)));
                if (OrderType.DEGRADE.equals(priceDetail.getType())) {
                    //cardHour 取负数
                    cardHour = cardHour.negate();
                }
                exclusive= exclusive.add(cardHour);
            }
        }
        return exclusive;
    }

    /**
     * 查询扩缩容订单
     * @param product
     * @return
     */
    private List<ServiceOrderPriceDetail> retrieveUpgradeAndDegradeOrderPriceDetails(DescribeProductResourceResponse product) {
        Criteria criteria = new Criteria();
        criteria.put("serviceTypeIn", ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode());
        criteria.put("clusterIdList", Arrays.asList(product.getClusterId()));
        criteria.put("status", OrderStatus.COMPLETED);
        List<ServiceOrder> orderList = serviceOrderMapper.selectServiceOrder(criteria);

        Set<String> orderSnSet = orderList.stream()
            .filter(o -> OrderType.UPGRADE.equals(o.getType()) || OrderType.DEGRADE.equals(o.getType()))
            .map(ServiceOrder::getOrderSn).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(orderSnSet)) {
            return new ArrayList<>();
        }
        return serviceOrderPriceDetailMapper.selectByParams(new Criteria().put("orderSns", orderSnSet).put("prodcutCode", ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode()));
    }
}
