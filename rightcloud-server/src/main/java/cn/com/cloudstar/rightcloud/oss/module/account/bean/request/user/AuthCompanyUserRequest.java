/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user;

import cn.com.cloudstar.rightcloud.oss.common.safe.EnumValue;
import cn.com.cloudstar.rightcloud.validated.validation.NotIllegalString;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import cn.com.cloudstar.rightcloud.validated.safe.SafeHtml;

/**
 * <AUTHOR>
 */
@Data
public class AuthCompanyUserRequest {

    /**
     * 机构SID
     */
    @ApiModelProperty("机构SID")
    @NotNull(message = "orgSid不能为空")
    private Long orgSid;

    /**
     * 企业营业执照类型
     *
     * 企业营业执照	business.license 组织机构代码证	organization.code 事业单位法人证书	legal.person 其他	other
     * 社会团体法人登记证书	so.registration.certificate 行政执法主体资格证	subject.qualification.certificate
     */
    @NotBlank(message = "businessLicenseType不能为空")
    @ApiModelProperty("企业营业执照类型")
    @EnumValue(strValues = {"business.license", "organization.code", "legal.person", "other","so.registration.certificate", "subject.qualification.certificate"},message = "非法企业营业执照类型")
    private String businessLicenseType;

    /**
     * 企业营业执照地址
     */
    @ApiModelProperty("企业营业执照地址")
    @SafeHtml
    @Pattern(regexp = "^((?!%0d|%0a|%20).)*$",message = "输入可能存在CRLF攻击！")
    @NotIllegalString
    private String businessLicenseUrl;

    /**
     * 身份类型
     * legalRepresentative --法人代表
     *
     * authorizedPerson--授权人
     */
    @NotBlank(message = "identityType不能为空")
    @ApiModelProperty("身份类型")
    @EnumValue(strValues = {"legalRepresentative", "authorizedPerson"},message = "非法身份类型")
    private String identityType;

    /**
     * 法定代表人
     */
    @ApiModelProperty("法定代表人")
    @NotBlank(message = "legalPerson不能为空")
    @NotIllegalString
    @Pattern(regexp = "^((?!%0d|%0a|%20).)*$",message = "输入可能存在CRLF攻击！")
    private String legalPerson;

    /**
     * 法定代表人身份证
     */
    @ApiModelProperty("法定代表人身份证")
    @NotIllegalString
    private String legalPersonCard;

    /**
     * 法定代表人身份证正面图片路径
     */
    @ApiModelProperty("法定代表人身份证正面图片路径")
    private String legalIdCardFront;

    /**
     * 法定代表人身份证正面图片路径
     */
    @ApiModelProperty("法定代表人身份证正面图片路径")
    private String legalIdCardReverse;

    /**
     * 被授权人姓名
     */
    @ApiModelProperty("被授权人姓名")
    private String empowerPerson;

    /**
     * 被授权人身份证号码
     */
    @ApiModelProperty("被授权人身份证号码")
    private String empowerPersonCard;

    /**
     * 被授权人身份证正面图片路径
     */
    @ApiModelProperty("被授权人身份证正面图片路径")
    private String empowerIdCardFront;

    /**
     * 法定代表人身份证正面图片路径
     */
    @ApiModelProperty("被授权人身份证正面图片路径")
    private String empowerIdCardReverse;

    /**
     * 授权书
     */
    @ApiModelProperty("授权书")
    @NotIllegalString
    @Pattern(regexp = "^((?!%0d|%0a|%20).)*$",message = "输入可能存在CRLF攻击！")
    private String powerAttorneyUrl;

    /**
     * 审批意见
     */
    @SafeHtml
    @Length(max = 256)
    @ApiModelProperty("审批意见")
    @Pattern(regexp = "^((?!%0d|%0a|%20).)*$",message = "输入可能存在CRLF攻击！")
    private String remark;

    /**
     * 分发表示 通过，驳回
     */
    @ApiModelProperty("分发表示 通过，驳回")
    @NotIllegalString
    @Pattern(regexp = "^((?!%0d|%0a|%20).)*$",message = "输入可能存在CRLF攻击！")
    private String flag;
}
