/*
 * Copyright (c) 2018 Cloud-Star, Inc. All Rights Reserved.
 */

package cn.com.cloudstar.rightcloud.oss.module.maintenance.service.templates.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.google.common.base.Strings;
import com.google.common.base.Throwables;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ThreadFactoryBuilder;

import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.Nonnull;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;

import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnv;
import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnvAccount;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResVpc;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.MapsKit;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.common.util.json.JsonUtil;
import cn.com.cloudstar.rightcloud.core.pojo.dto.st.ServerTemplate;
import cn.com.cloudstar.rightcloud.core.pojo.dto.st.ServerTemplateListDTO;
import cn.com.cloudstar.rightcloud.core.pojo.dto.operate.SfServiceCategoryHpcClusterPool;
import cn.com.cloudstar.rightcloud.core.pojo.dto.st.ServerTemplateRevision;
import cn.com.cloudstar.rightcloud.core.pojo.dto.st.ServerTemplateRevisionDTO;
import cn.com.cloudstar.rightcloud.core.pojo.dto.st.ServerTemplateScript;
import cn.com.cloudstar.rightcloud.core.pojo.dto.st.ServerTemplateScriptParam;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.Code;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.SysConfig;
import cn.com.cloudstar.rightcloud.core.pojo.vo.st.ServerTemplateResourceQuery;
import cn.com.cloudstar.rightcloud.oss.common.constants.CodeCategoryConstants;
import cn.com.cloudstar.rightcloud.oss.common.constants.res.status.CloudEnvStatus;
import cn.com.cloudstar.rightcloud.oss.common.constants.res.type.CloudEnvType;
import cn.com.cloudstar.rightcloud.oss.common.constants.res.type.ExtraType;
import cn.com.cloudstar.rightcloud.oss.common.constants.res.type.ResZoneType;
import cn.com.cloudstar.rightcloud.oss.common.constants.status.MarketPublishStatus;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.ResourceType;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.ServiceManage;
import cn.com.cloudstar.rightcloud.oss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.oss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.oss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.oss.common.pojo.specdefine.request.ProductSpecDefineFeignForm;
import cn.com.cloudstar.rightcloud.oss.common.pojo.specdefine.result.ProductSpecDefineFeignResult;
import cn.com.cloudstar.rightcloud.oss.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.WebUserUtil;
import cn.com.cloudstar.rightcloud.oss.module.feign.service.ServiceFeignClient;
import cn.com.cloudstar.rightcloud.oss.module.maintenance.bean.templates.request.DescribeAvailableResourceRequest;
import cn.com.cloudstar.rightcloud.oss.module.maintenance.common.ServerTemplateAvailableResourceFactory;
import cn.com.cloudstar.rightcloud.oss.module.maintenance.dao.templates.ServerTemplateMapper;
import cn.com.cloudstar.rightcloud.oss.module.maintenance.dao.templates.ServerTemplateRepoMapper;
import cn.com.cloudstar.rightcloud.oss.module.maintenance.dao.templates.ServerTemplateRevisionMapper;
import cn.com.cloudstar.rightcloud.oss.module.maintenance.dao.templates.ServerTemplateScriptMapper;
import cn.com.cloudstar.rightcloud.oss.module.maintenance.dao.templates.ServerTemplateScriptParamMapper;
import cn.com.cloudstar.rightcloud.oss.module.maintenance.service.templates.ServerTemplateScriptService;
import cn.com.cloudstar.rightcloud.oss.module.maintenance.service.templates.ServerTemplateService;
import cn.com.cloudstar.rightcloud.oss.module.pricing.bean.priceconfig.model.BizBillingSpecRef;
import cn.com.cloudstar.rightcloud.oss.module.pricing.bean.priceconfig.model.BizBillingSpecRefExample;
import cn.com.cloudstar.rightcloud.oss.module.pricing.dao.priceconfig.BizBillingSpecRefMapper;
import cn.com.cloudstar.rightcloud.oss.module.resource.service.CodeService;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.dao.ServiceCategoryMapper;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.service.ServiceCategoryAdditionalService;
import cn.com.cloudstar.rightcloud.oss.module.system.dao.config.SystemConfigMapper;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.cloud.request.CloudEnvParams;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.cloud.request.QueryAllCloudEnvRequest;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.ma.ResMaFlavorVO;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResPool;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResShareType;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResStorage;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResVmType;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResZone;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.QueryResShareTypeByParamsRequest;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.QueryResStorageByParamsRequest;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.QueryResVmTypeByParamsRequest;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.ResPoolParams;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.ResVpcByParams;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.ResZoneParams;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.cloud.CloudEnvAccountRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.cloud.CloudEnvRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.ma.MaRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.network.ResVpcRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.server.ResShareTypeRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.server.ResVmTypeRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.server.ResZoneRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.storage.ResStorageRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.storage.ResVolumeTypeRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.zone.ResPoolRemoteService;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.google.common.base.Strings;
import com.google.common.base.Throwables;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.ss.formula.functions.T;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;

import static cn.com.cloudstar.rightcloud.oss.common.constants.WebConstants.CLONE_DEFAULT;
import static cn.com.cloudstar.rightcloud.oss.common.constants.WebConstants.VERSION_HEAD;


@Service
public class ServerTemplateServiceImpl implements ServerTemplateService {

    private static final Logger logger = LoggerFactory.getLogger(ServerTemplateServiceImpl.class);

    /**
     * 实例类型 【核数 内存】 eg:1核 1GB
     */
    private static final String VM_TYPE_NAME_FORMAT = "%s核 %sGB";

    @Autowired
    private ServerTemplateMapper serverTemplateMapper;

    @Autowired
    private ServerTemplateRepoMapper serverTemplateRepoMapper;

    @Autowired
    private ServerTemplateScriptService serverTemplateScriptService;

    @Autowired
    private ServerTemplateRevisionMapper serverTemplateRevisionMapper;

    @Autowired
    private ServerTemplateScriptMapper serverTemplateScriptMapper;

    @Autowired
    private ServerTemplateScriptParamMapper serverTemplateScriptParamMapper;

    @DubboReference
    private CloudEnvRemoteService cloudEnvService;

    @Autowired
    private ServerTemplateAvailableResourceFactory serverTemplateAvailableResourceFactory;

    @Autowired
    @Lazy
    private ServiceCategoryAdditionalService serviceCategoryAdditionalService;

    @DubboReference
    private ResZoneRemoteService resZoneService;

    @DubboReference
    private ResPoolRemoteService resPoolService;

    @DubboReference
    private CloudEnvAccountRemoteService cloudEnvAccountService;

    @DubboReference
    private ResVmTypeRemoteService resVmTypeRemoteService;

    @DubboReference
    private ResVolumeTypeRemoteService resVolumeTypeRemoteService;

    @DubboReference
    private ResVpcRemoteService resVpcRemoteService;

    @Autowired
    private CodeService codeService;

    @DubboReference
    private ResStorageRemoteService resStorageService;

    @DubboReference
    private ResShareTypeRemoteService resShareTypeRemoteService;
    @DubboReference
    private MaRemoteService maRemoteService;

    @Autowired
    private BizBillingSpecRefMapper bizBillingSpecRefMapper;
    @Autowired
    private SystemConfigMapper systemConfigMapper;
    @Autowired
    ServiceFeignClient serviceFeignClient;
    @Autowired
    private ServiceCategoryMapper serviceCategoryMapper;

    @Override
    public int countByParams(Criteria example) {
        int count = this.serverTemplateMapper.countByParams(example);
        logger.debug("count: {}", count);
        return count;
    }


    @Override
    public List<ServerTemplate> selectByParams(Criteria example) {
        return this.serverTemplateMapper.selectByParams(example);
    }

    @Override
    public int deleteByPrimaryKey(Long id) {
        return this.serverTemplateMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKey(ServerTemplate record) {
        return this.serverTemplateMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateByPrimaryKeySelective(ServerTemplate record) {
        return this.serverTemplateMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int deleteByParams(Criteria example) {
        return this.serverTemplateMapper.deleteByParams(example);
    }

    @Override
    public int updateByParamsSelective(ServerTemplate record, Criteria example) {
        return this.serverTemplateMapper.updateByParamsSelective(record, example.getCondition());
    }

    @Override
    public int updateByParams(ServerTemplate record, Criteria example) {
        return this.serverTemplateMapper.updateByParams(record, example.getCondition());
    }

    @Override
    public int insert(ServerTemplate record) {
        return this.serverTemplateMapper.insert(record);
    }

    @Override
    public ServerTemplate selectByPrimaryKey(Long id) {
        return this.serverTemplateMapper.selectByPrimaryKey(id);
    }

    @Override
    public int insertSelective(ServerTemplate record) {
        return this.serverTemplateMapper.insertSelective(record);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteServerTemplateById(Long id) {
        //validate the server template is used
        Criteria example = new Criteria();
        example.put("templateId", id);
        example.put("serviceTypes", Arrays.asList(ServiceManage.COMPUTE));
        int number = serviceCategoryAdditionalService.countRelationService(example);
        if (number > 0) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1634306856));
        }

        // 判断当前主机模版有几个版本
        List<ServerTemplateRevision> revisions = serverTemplateRevisionMapper.selectByParams(
                new Criteria("mainTemplateId", id));
        if (!CollectionUtils.isEmpty(revisions)) {
            // 如果reversion中不止一个，证明提交过数据，那么删除时也需要删除关联镜像的关系
            List<Long> getServerTemplateIds = revisions.stream()
                                                       .map(ServerTemplateRevision::getServerTemplateId)
                                                       .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(getServerTemplateIds)) {
                Criteria criteria = new Criteria("serverTemplateIds", getServerTemplateIds);
                // 删除模板
                serverTemplateMapper.deleteByParams(criteria);

                // 删除历史版本
                serverTemplateRevisionMapper.deleteByParams(criteria);

                // 删除repo
                serverTemplateRepoMapper.deleteByParams(criteria);

                // 删除脚本关联表
                List<ServerTemplateScript> serverTemplateScripts = serverTemplateScriptService.selectByParams(criteria);
                List<Long> serverTemplateScriptIds = serverTemplateScripts.stream()
                                                                          .map(ServerTemplateScript::getId)
                                                                          .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(serverTemplateScriptIds)) {
                    serverTemplateScriptParamMapper.deleteByParams(
                            new Criteria("stScriptIds", serverTemplateScriptIds));
                }
                serverTemplateScriptMapper.deleteByParams(criteria);

                // 删除告警策略相关
                //yyhtodo 改为远程调用
//                deleteResAlarmRuleTarget(id.toString());
            }
        }

        return true;
    }

    @Override
    public List<ServerTemplateListDTO> getServerTemplates(Criteria criteria, Map<String, String> params) {
        if ("cloudEnvNames".equals(params.get("sortdatafield"))) {
            criteria.setOrderByClause(
                    "cast(cloud_env_names as CHAR) " + params.get("sortorder") + ",A.updated_dt desc");
        }
        List<ServerTemplateListDTO> serverTemplates = this.serverTemplateMapper.getServerTemplates(criteria);
        serverTemplates.forEach(serverTemplateListDTO -> {
            //获取版本信息
            Criteria revisionCriteria = new Criteria();
            revisionCriteria.put("mainTemplateId", serverTemplateListDTO.getId());
            revisionCriteria.put("versionNameNotEqual", VERSION_HEAD);
            revisionCriteria.setOrderByClause("id desc");
            List<ServerTemplateRevision> serverTemplateRevisions = serverTemplateRevisionMapper
                    .selectByParams(revisionCriteria);

            serverTemplateListDTO.setRevisions(serverTemplateRevisions.stream()
                                                                      .map(ServerTemplateRevisionDTO::new)
                                                                      .collect(Collectors.toList()));

            if (serverTemplateListDTO != null && !StringUtil.isNullOrEmpty(serverTemplateListDTO.getCloudEnvNames())) {
                List<String> names = JSONArray.parseArray(serverTemplateListDTO.getCloudEnvNames(), String.class);
                if (!StringUtil.isNullOrEmpty(names) && !names.isEmpty()) {
                    serverTemplateListDTO.setCloudEnvNames(StringUtils.join(names.stream().distinct()
                                                                                 .collect(Collectors.toList()), ","));
                }
            }
        });

        return serverTemplates;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long cloneServerTemplate(Long serverTemplateId) {
        return commonCloneServerTemplate(VERSION_HEAD,
                                         serverTemplateId,
                                         ""
        );
    }

    /**
     * TODO optimize
     */
    @Transactional(rollbackFor = Exception.class)
    public Long commonCloneServerTemplate(String version, Long templateId, String commitComments) {
        ServerTemplate serverTemplate = selectByPrimaryKey(templateId);
        if (!StringUtil.isNullOrEmpty(serverTemplate)) {
            ServerTemplate newServerTemplate = new ServerTemplate();
            org.springframework.beans.BeanUtils.copyProperties(serverTemplate, newServerTemplate);
            newServerTemplate.setFixed(serverTemplate.getFixed());
            if (VERSION_HEAD.equals(version)) {
                //set clone name
                newServerTemplate.setClonedName(serverTemplate.getName());
                newServerTemplate.setClonedId(serverTemplate.getId());
                newServerTemplate.setClonedDt(Calendar.getInstance().getTime());
                newServerTemplate.setPublishStatus(MarketPublishStatus.UNPUBLISH);
                Criteria cloneCrieria = new Criteria();
                String[] names = serverTemplate.getName().split(" ");
                String cloneLikeName = StringUtils.join(Stream.of(names).limit(names.length == 1 ? 1 : names.length - 1)
                                                              .collect(Collectors.toList()), " ");
                cloneCrieria.put("cloneLikeName", (StringUtil.isNullOrEmpty(cloneLikeName) ? names[0] : cloneLikeName));
                int versionNum = 1;
                List<ServerTemplate> matchClones = this.serverTemplateMapper.selectByParams(cloneCrieria)
                                                                            .stream()
                                                                            .filter(st -> st.getName()
                                                                                            .split(" v")[0].equals(
                                                                                    cloneLikeName))
                                                                            .collect(Collectors.toList());
                for (ServerTemplate st : matchClones) {
                    //is matched
                    if (st.getName().matches("^.*\\sv\\d+$")) {
                        String[] name = st.getName().split(" v");
                        int realCurrNum = Integer.parseInt(name[name.length - 1]);
                        if (realCurrNum >= versionNum) {
                            versionNum = realCurrNum + 1;
                        }
                    }

                }
                String cloneName = " " + CLONE_DEFAULT + versionNum;
                String newName = cloneLikeName;
                newServerTemplate
                        .setName(newName.substring(0, newName.length() - 1 > 64 ? 64 : newName.length()) + cloneName);
            }
            //save new server template
            newServerTemplate.setTemplateClass("private");
            newServerTemplate.setEnvConfig(serverTemplate.getEnvConfig());
            WebUserUtil.prepareInsertParams(newServerTemplate);
            newServerTemplate.setCloudEnvNames(serverTemplate.getCloudEnvNames());
            newServerTemplate.setCloudEnvTypes(serverTemplate.getCloudEnvTypes());
            this.serverTemplateMapper.insertSelective(newServerTemplate);

            //save alarm strategy
            Criteria copyOtherCri = new Criteria();
            copyOtherCri.put("serverTemplateId", templateId);

            //save script info
            List<ServerTemplateScript> sts = this.serverTemplateScriptMapper.selectByParams(copyOtherCri);
            if (CollectionUtil.isNotEmpty(sts)) {
                sts.forEach(sobj -> {
                    Criteria scriptPara = new Criteria();
                    scriptPara.put("stScriptId", sobj.getId());
                    sobj.setId(null);
                    sobj.setServerTemplateId(newServerTemplate.getId());
                    sobj.setCreatedDt(Calendar.getInstance().getTime());
                    this.serverTemplateScriptMapper.insertSelective(sobj);
                    //save script ref param info
                    List<ServerTemplateScriptParam> serverTemplateScriptParams = this.serverTemplateScriptParamMapper
                            .selectByParams(scriptPara);
                    if (CollectionUtil.isNotEmpty(serverTemplateScriptParams)) {
                        serverTemplateScriptParams.forEach(stsp -> {
                            stsp.setId(null);
                            stsp.setStScriptId(sobj.getId());
                            stsp.setCreatedDt(Calendar.getInstance().getTime());
                            this.serverTemplateScriptParamMapper.insertSelective(stsp);
                        });
                    }

                });
            }

            //TODO need optimize
            //save new revision
            Criteria criteria = new Criteria();
            criteria.put("serverTemplateId", templateId);
            List<ServerTemplateRevision> serverTemplateRevisions = serverTemplateRevisionMapper.selectByParams(
                    criteria);
            ServerTemplateRevision serverTemplateRevision = new ServerTemplateRevision();
            serverTemplateRevision.setVersionName(version);
            if (version.equals(VERSION_HEAD)) {
                serverTemplateRevision.setMainTemplateId(newServerTemplate.getId());
            } else {
                if (!CollectionUtils.isEmpty(serverTemplateRevisions)) {
                    templateId = serverTemplateRevisions.get(0).getMainTemplateId();
                }
                serverTemplateRevision.setMainTemplateId(templateId);
            }

            //new version id
            serverTemplateRevision.setServerTemplateId(newServerTemplate.getId());
            serverTemplateRevision.setCreatedDt(Calendar.getInstance().getTime());
            serverTemplateRevision.setCommitComments(commitComments);
            serverTemplateRevisionMapper.insertSelective(serverTemplateRevision);
            return newServerTemplate.getId();
        }
        return 0L;
    }

    /**
     * 处理非固定云环境资源
     **/
    private void handleUnfixedEnvResource(Map<String, List<Map>> cacheMap, Map<String, Object> resMap) {
        for (String key : resMap.keySet()) {
            List<Map> listContainer = cacheMap.get(key);
            if (null == listContainer) {
                listContainer = new ArrayList<>();
                cacheMap.put(key, listContainer);
            }

            List<Map> targetValues = (List<Map>) resMap.get(key);
            if (CollectionUtils.isEmpty(targetValues)) {
                continue;
            }
            for (Map dl : listContainer) {
                Iterator<Map> iterator = targetValues.iterator();
                while (iterator.hasNext()) {
                    Map next = iterator.next();
                    if (null != next.get("value") && next.get("value").equals(dl.get("value"))) {
                        iterator.remove();
                    } else if (null != next.get("uuid") && next.get("uuid").equals(dl.get("uuid"))) {
                        iterator.remove();
                    }
                }
            }
            if (!CollectionUtils.isEmpty(targetValues)) {
                listContainer.addAll(targetValues);
            }
        }
        return;
    }

    /**
     * 过滤指定云环境下模板中具有的资源
     **/
    private List<Map<String, Object>> filterUnfixedEnvResource(List<Map> configList,
                                                               Map<Long, Map<String, Object>> resMap,
                                                               CloudEnv cloudEnv) {
        //cloudEnvId
        List<Map<String, Object>> list = new ArrayList<>();

        for (Long zoneId : resMap.keySet()) {
            Map<String, Object> templateConfigMap = new HashMap<>();
            // 查找到对应分区信息
            Map zoneMap = new HashMap();
            if (CloudEnvType.VMWARE.equalValue(cloudEnv.getCloudEnvType())) {
                ResPool resPool = resPoolService.selectByPrimaryKey(zoneId);
                zoneMap.put("id", resPool.getId());
                zoneMap.put("name", resPool.getName());
                zoneMap.put("uuid", resPool.getId());
            } else {
                ResZone resZone = resZoneService.selectByPrimaryKey(zoneId);
                zoneMap.put("id", resZone.getUuid());
                zoneMap.put("name", resZone.getName());
                zoneMap.put("uuid", resZone.getUuid());
            }

            Map<String, Object> totalConfigMap = resMap.get(zoneId);
            for (Map<String, Object> config : configList) {
                for (String key : config.keySet()) {
                    Object targetObj = config.get(key);
                    if (targetObj instanceof Map) {
                        templateConfigMap.put(key, targetObj);
                        continue;
                    }
                    if (targetObj instanceof List) {
                        List<Map> targetList = (List<Map>) targetObj; // 过滤值
                        boolean find = false;
                        if (totalConfigMap.containsKey(key)) {
                            find = true;
                            List<Map> values = (List<Map>) totalConfigMap.get(key); // 过滤对象
                            List<Map> fl = values.stream().filter(m -> targetList.stream().anyMatch(c -> {
                                String[] fs = {"uuid", "value"};
                                for (String f : fs) {
                                    if (m.containsKey(f) && c.containsKey(f)) {
                                        return Objects.equals(m.get(f), c.get(f));
                                    }
                                }
                                return false;
                            })).collect(Collectors.toList());
                            templateConfigMap.put(key, fl);
                        }
                        if (!find) {
                            templateConfigMap.put(key, targetList);
                        }
                    }
                }
            }
            templateConfigMap.put("zone", Arrays.asList(zoneMap));
            list.add(templateConfigMap);
        }
        return list;
    }

    /**
     * 是否是同一个属性
     **/
    private boolean isSameFloatingIpInfo(Map source, Map check) {
        Set keys = source.keySet();
        if (!keys.equals(check.keySet())) {
            return false;
        }
        for (Object key : keys) {
            List list1 = (List) source.get(key);
            List list2 = (List) check.get(key);

            for (Object v1 : list1) {
                Map m1 = (Map) v1;
                boolean f = false;
                for (Object v2 : list2) {
                    Map m2 = (Map) v2;
                    if (m1.containsKey("value")) {
                        if (m1.get("value").equals(m2.get("value"))) {
                            f = true;
                        }
                    } else if (m1.containsKey("uuid")) {
                        if (m1.get("uuid").equals(m2.get("uuid"))) {
                            f = true;
                        }
                    }
                }
                if (!f) {
                    return false;
                }
            }

        }
        return true;
    }

    @Override
    public Map<String, Object> getResourceFamilyType(DescribeAvailableResourceRequest request) {
        Long envAccountId = request.getEnvAccountId();
        String type = request.getType();
        Long limitCount = request.getLimitCount();
        if (StrUtil.isEmpty(type)) {
            return null;
        }
        CloudEnvAccount envAccount = cloudEnvAccountService.selectByPrimaryKey(envAccountId);
        if (Objects.isNull(envAccount)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2092645546));
        }

        Map<String, Object> result = Maps.newHashMap();
        CloudEnvParams cloudEnvParams = new CloudEnvParams();
        cloudEnvParams.setCloudEnvAccountId(envAccount.getId());
        List<CloudEnv> envs = cloudEnvService.selectByParams(cloudEnvParams);
        if (CollectionUtil.isEmpty(envs)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1332118329));
        }
        List<Long> envIds = envs.stream().map(CloudEnv::getId).collect(Collectors.toList());
        if (type.contains(ResourceType.RES_VM)) {
            ProductSpecDefineFeignForm productSpecDefineFeignForm = new ProductSpecDefineFeignForm();
            productSpecDefineFeignForm.setProductCode(ProductCodeEnum.ECS.getProductCode());
            RestResult<List<ProductSpecDefineFeignResult>> specResult = serviceFeignClient.getProductSpecDefine(productSpecDefineFeignForm);
            Object specList = specResult.getData();
            if (Objects.isNull(specList)) {
                return result;
            }
            List<ProductSpecDefineFeignResult> productSpecDefine = JSON.parseArray(JSON.toJSONString(specList), ProductSpecDefineFeignResult.class);
            List<Map<String, Object>> vmTypes = Lists.newArrayList();
            productSpecDefine.stream().forEach(spec -> {
                vmTypes.add(MapsKit.of("family", spec.getProductSpecCode(),
                                       "familyName", spec.getProductSpecName(),
                                       "familyDesc", "default"));
            });
            result.put("vmType", vmTypes);
            return result;
        }
        else if (type.contains(ResourceType.RES_VD)) {
            if (CloudEnvType.AWS.equalValue(envAccount.getEnvType())) {
                List<Code> codeList = this.codeService
                        .selectByParams(new Criteria("codeCategory", CodeCategoryConstants.AWS_DISK_CATEGORY));
                // 存储类型
                Code code = codeList.get(0);
                result.put("disk", JSON.parseObject(code.getAttribute3()).getJSONArray("dataDisk").toJavaList(Map.class));
            }
            else if (CloudEnvType.FUSIONCOMPUTE.equalValue(envAccount.getEnvType())) {
                QueryResStorageByParamsRequest queryResStorageByParamsRequest = new QueryResStorageByParamsRequest();
                queryResStorageByParamsRequest.setCloudEnvIds(envIds);
                List<ResStorage> resStorageList = resStorageService.selectInfoOfShare(queryResStorageByParamsRequest);

                List<ImmutableMap<String, ? extends Serializable>> diskCategory = resStorageList.stream()
                                                                                                .map(resVolumeType -> ImmutableMap
                                                                                                        .of("value",
                                                                                                            resVolumeType.getResStorageSid(),
                                                                                                            "name",
                                                                                                            resVolumeType.getStorageName()))
                                                                                                .collect(Collectors.toList());
                result.put("", diskCategory);
            } else {
                ProductSpecDefineFeignForm productSpecDefineFeignForm = new ProductSpecDefineFeignForm();
                productSpecDefineFeignForm.setProductCode(ProductCodeEnum.DISK.getProductCode());
                RestResult<List<ProductSpecDefineFeignResult>> specResult = serviceFeignClient.getProductSpecDefine(productSpecDefineFeignForm);
                Object specList = specResult.getData();
                if (Objects.isNull(specList)) {
                    return result;
                }
                List<ProductSpecDefineFeignResult> productSpecDefine = JSON.parseArray(JSON.toJSONString(specList), ProductSpecDefineFeignResult.class);
                List<Map<String, Object>> vmTypes = Lists.newArrayList();
                productSpecDefine.stream().forEach(spec -> {
                    vmTypes.add(MapsKit.of("family", spec.getProductSpecCode(),
                                           "familyName", spec.getProductSpecName(),
                                           "familyDesc", "default"));
                });
                result.put("disk", vmTypes);
                return result;

//                ResVolumeTypeParams resVolumeTypeParams = new ResVolumeTypeParams();
//                boolean matchPublicCloud = CloudEnvType.getPublicCloud().stream().map(CloudEnvType::getValue)
//                                                       .anyMatch(t -> t.contains(envs.get(0).getCloudEnvType()));
//                if (matchPublicCloud) {
//                    resVolumeTypeParams.setCloudEnvId(-1L);
//                    resVolumeTypeParams.setCloudEnvType(envs.get(0).getCloudEnvType());
//                } else {
//                    resVolumeTypeParams.setEnvIdList(envIds);
//                }
//                List<ResVolumeType> resVolumeTypes = resVolumeTypeRemoteService.selectByParamsOpenStack(resVolumeTypeParams);
//                if (CollectionUtil.isEmpty(resVolumeTypes)) {
//                    result.put("disk", MapsKit.of("name", "default", "value", "default"));
//                }
//                List<Map<String, ? extends Serializable>> diskCategory
//                        = resVolumeTypes.stream()
//                                        .map(resVolumeType -> MapsKit.of(
//                                                "value", StrUtil.isEmpty(resVolumeType.getUuid()) ?  resVolumeType\.getId() : resVolumeType.getUuid(),
//                                                "name", resVolumeType.getTypeName()))
//                                        .collect(Collectors.toList());
//                // 存储类型
//                result.put("disk", diskCategory);
            }

        } else if (type.contains(ResourceType.RES_FLOATING_IP)) {
            if (CloudEnvType.ALIYUN.equalValue(envAccount.getEnvType())) {
                // 付费类型
                result.put("network",
                           new ArrayList<>(Arrays.asList(MapsKit.of("name", "按固定带宽计费",
                                                                    "value", "PayByBandwidth"),
                                                         MapsKit.of("name", "按流量计费",
                                                                    "value", "PayByTraffic")
                           )));
            } else if (CloudEnvType.HUAWEICLOUD.equalValue(envAccount.getEnvType())) {
                // 网络类型
                List list = new ArrayList();
                list.add(MapsKit.of("name", "静态BGP", "value", "5_sbgp"));
                list.add(MapsKit.of("name", "动态BGP", "value", "5_bgp"));
                result.put("network", list);
            } else if (CloudEnvType.OPEN_STACK.equalValue(envAccount.getEnvType())) {
                // 浮动ip资源
                ResVpcByParams resVpcByParams = new ResVpcByParams();
                resVpcByParams.setCloudEnvIdIn(envIds);
                List<ResVpc> resNetVpcs = resVpcRemoteService.selectBaseByParams(resVpcByParams);
                List<Map<String, String>> floatingIpPool
                        = resNetVpcs.stream()
                                    .filter(resNetVpc -> "TRUE".equalsIgnoreCase(resNetVpc.getExternal()))
                                    .map(resNetVpc -> MapsKit.of("name", resNetVpc.getName(),
                                                                 "value", resNetVpc.getUuid()))
                                    .collect(Collectors.toList());
                result.put("network", floatingIpPool);
            } else {
                result.put("network", MapsKit.of("name", "default", "value", "default"));
            }
        }
        else if (type.contains(ResourceType.RES_SFS)) {
            if (CloudEnvType.HCSO.equalValue(envAccount.getEnvType()) || CloudEnvType.HPCOFFLINE.equalValue(envAccount.getEnvType())) {
                QueryResShareTypeByParamsRequest queryResShareTypeByParamsRequest = new QueryResShareTypeByParamsRequest();
                queryResShareTypeByParamsRequest.setEnvIds(envIds);
                List<ResShareType> shareTypes = resShareTypeRemoteService.selectByParams(queryResShareTypeByParamsRequest);
                List<Map<String, String>> protocolTypes = Lists.newArrayList();
                if (Objects.equals(request.getVersion(), "2.0")) {
                    protocolTypes.add(MapsKit.of("name", "DPC",
                                                 "value", "dpc"));
                    protocolTypes.add(MapsKit.of("name", "NFS",
                                                 "value", "nfs"));
                    protocolTypes.add(MapsKit.of("name", "DPC & NFS",
                                                 "value", "nfs&dpc"));
                } else {
                    protocolTypes.add(MapsKit.of("name", "SFS_HPCMIX",
                                                 "value", "sfs.hpcmix.performance"));
                }
                // 弹性文件类型
                List<Map<String, String>> list = new ArrayList();
                if (CollectionUtil.isEmpty(shareTypes)) {
                    result.put("disk", protocolTypes);
                } else {
                    for (ResShareType shareType : shareTypes) {
                        if (Strings.isNullOrEmpty(shareType.getName())) {
                            continue;
                        }
                        for (Map<String, String> protocolType : protocolTypes) {
                            list.add(MapsKit.of(
                                    "name", shareType.getName() + "(" + protocolType.get("name") + ")"
                                    , "value", shareType.getName() + "-" + protocolType.get("value")));
                        }
                    }

                    result.put("disk", list);
                }
            }
        }
        else if (type.contains(ResourceType.RES_DME_OSP)) {
            if (CloudEnvType.HCSO.equalValue(envAccount.getEnvType()) || CloudEnvType.HPCOFFLINE.equalValue(envAccount.getEnvType())) {
                List<Map<String, String>> protocolTypes = Lists.newArrayList();

                protocolTypes.add(MapsKit.of("name", "SFS_HPCMIX","value", "sfs.hpcmix.performance"));

                // 弹性文件类型
                List<Map<String, String>> list = new ArrayList();
                result.put("disk", protocolTypes);

            }
        }
        else if (type.contains(ResourceType.RES_BMS)) {
            ProductSpecDefineFeignForm productSpecDefineFeignForm = new ProductSpecDefineFeignForm();
            productSpecDefineFeignForm.setProductCode(ProductCodeEnum.BMS.getProductCode());
            RestResult<List<ProductSpecDefineFeignResult>> specResult = serviceFeignClient.getProductSpecDefine(productSpecDefineFeignForm);
            Object specList = specResult.getData();
            if (Objects.isNull(specList)) {
                return result;
            }
            List<ProductSpecDefineFeignResult> productSpecDefine = JSON.parseArray(JSON.toJSONString(specList), ProductSpecDefineFeignResult.class);
            List<Map<String, Object>> vmTypes = Lists.newArrayList();
            productSpecDefine.stream().forEach(spec -> {
                vmTypes.add(MapsKit.of("family", spec.getProductSpecCode(),
                                       "familyName", spec.getProductSpecName(),
                                       "familyDesc", "default"));
            });
            result.put("vmType", vmTypes);
        }
        else if (type.contains(ResourceType.RES_HPC_OFFLINE)) {
            if (CloudEnvType.HCSO.equalValue(envAccount.getEnvType())) {
                Criteria criteria = new Criteria();
                criteria.put("serviceType", ProductCodeEnum.HPC_OFFLINE.getProductCode());
                List<SfServiceCategoryHpcClusterPool> hpcClusterPools = serviceCategoryMapper.selectServiceCategoryHpcClusterPoolByParams(criteria);
                List<Map<String,String>> dataList = new ArrayList<>();
                for (SfServiceCategoryHpcClusterPool hpcClusterPool : hpcClusterPools) {
                        dataList.add(MapsKit.of(
                            "name", hpcClusterPool.getClusterName()
                            , "value", hpcClusterPool.getClusterId()));
                }
                result.put("other", dataList);
            }
        }
        else if (type.contains(ResourceType.RES_HPC_DRP)) {
            if (CloudEnvType.HCSO.equalValue(envAccount.getEnvType())) {

                QueryResShareTypeByParamsRequest queryResShareTypeByParamsRequest = new QueryResShareTypeByParamsRequest();
                queryResShareTypeByParamsRequest.setEnvIds(envIds);
                List<ResShareType> shareTypes = resShareTypeRemoteService.selectByParams(queryResShareTypeByParamsRequest);
                List<Map<String, String>> drpList = Lists.newArrayList();
                drpList.add(MapsKit.of("name", "HA", "value", "Paired"));
                drpList.add(MapsKit.of("name", "非 HA", "value", "Unpaired"));
                // 弹性文件类型
                List<Map<String, String>> list = new ArrayList();
                if (CollectionUtil.isEmpty(shareTypes)) {
                    result.put("other", drpList);
                } else {
                    for (ResShareType shareType : shareTypes) {
                        if (Strings.isNullOrEmpty(shareType.getName())) {
                            continue;
                        }
                        for (Map<String, String> protocolType : drpList) {
                            list.add(MapsKit.of(
                                "name", shareType.getName() + "(" + protocolType.get("name") + ")"
                                , "value", shareType.getName() + "-" + protocolType.get("value")));
                        }
                    }

                    result.put("other", list);
                }
            }
        }
        else if (type.contains(ResourceType.MA_DRP)) {
            if (CloudEnvType.HCSO.equalValue(envAccount.getEnvType())) {
                List<ResMaFlavorVO> shareTypes = maRemoteService.maFlaerQuery();
                BizBillingSpecRefExample specRefExample = new BizBillingSpecRefExample();
                specRefExample.createCriteria().andResourceTypeEqualTo("DRP").andTypeEqualTo("dynamic");;
                //获取静态配置的数据
                List<BizBillingSpecRef> bizBillingSpecRefs = bizBillingSpecRefMapper.selectByExample(specRefExample);

                List<Map<String, String>> list = new ArrayList();
                for (ResMaFlavorVO shareType : shareTypes) {
                    if (Strings.isNullOrEmpty(shareType.getSpecName())) {
                        continue;
                    }
                    String specName = shareType.getSpecName();
                    boolean snt9b = specName.toLowerCase().indexOf("snt9b") > 0 || specName.indexOf("910b") > 0;
                    list.add(MapsKit.of(
                            "name", shareType.getCpuArch() +"架构"+"、"+shareType.getCpuNum() +"vCPU"+"、"+shareType.getMemory()+"内存" + "、" + shareType.getNpuNum() + "*Ascend " +
                                    (snt9b ? "snt9b" : "snt9")
                            , "value", specName));
                }
                //新增静态资源
                List<Map<String, String>> specRefList = new ArrayList<>();
                for(BizBillingSpecRef bizBillingSpecRefList :bizBillingSpecRefs){
                    if(Strings.isNullOrEmpty(bizBillingSpecRefList.getValue())){
                        continue;
                    }
                    JSONArray  parseArray = JSONArray.parseArray(bizBillingSpecRefList.getValue());
                    for(int i = 0; i < parseArray.size(); i++) {
                        JSONObject jsonObject = parseArray.getJSONObject(i);
                        String cueValue="";
                        if(Strings.isNullOrEmpty(jsonObject.getString("cueValue"))){
                            cueValue=jsonObject.getString("cueValue");
                        }
                        specRefList.add(MapsKit.of("cueValue", cueValue,
                                "name", jsonObject.getString("name")
                                , "value", jsonObject.getString("value")));
                    }
                }

                // 过滤静态资源重复的计费规格
                specRefList = specRefList.stream().collect(
                        Collectors.collectingAndThen(
                                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.get("value")))),
                                ArrayList::new)

                );
                Map<String, Map<String, String>> specMap = list.stream().filter(dm -> dm.get("value") != null).collect(Collectors.toMap(dm -> dm.get("value"), dm -> dm));
                specRefList.stream().forEach(map -> {
                    Map<String, String> specNameMap = specMap.get(map.get("value"));
                    if (specNameMap != null && map.get("cueValue") !=null) {
                        specNameMap.put("cueValue", map.get("cueValue"));
                    }
                });

                specRefList.removeIf(map -> list.stream().anyMatch(dm -> StringUtil.equals(dm.get("value"),map.get("value"))));
                list.addAll(specRefList);

                result.put("other", list);

            }
        }
        else if (type.contains(ResourceType.RES_DCS)) {
            ProductSpecDefineFeignForm productSpecDefineFeignForm = new ProductSpecDefineFeignForm();
            productSpecDefineFeignForm.setProductCode(ProductCodeEnum.DCS.getProductCode());
            RestResult<List<ProductSpecDefineFeignResult>> specResult = serviceFeignClient.getProductSpecDefine(productSpecDefineFeignForm);
            Object specList = specResult.getData();
            if (Objects.isNull(specList)) {
                return result;
            }
            List<ProductSpecDefineFeignResult> productSpecDefine = JSON.parseArray(JSON.toJSONString(specList), ProductSpecDefineFeignResult.class);
            List<Map<String, Object>> vmTypes = Lists.newArrayList();
            productSpecDefine.stream().forEach(spec -> {
                vmTypes.add(MapsKit.of("family", spec.getProductSpecCode(),
                        "familyName", spec.getProductSpecName(),
                        "familyDesc", "default"));
            });
            result.put("vmType", vmTypes);
            return result;
        }
        else if (type.contains(ResourceType.RES_RDS)) {
            ProductSpecDefineFeignForm productSpecDefineFeignForm = new ProductSpecDefineFeignForm();
            productSpecDefineFeignForm.setProductCode(ProductCodeEnum.RDS.getProductCode());
            RestResult<List<ProductSpecDefineFeignResult>> specResult = serviceFeignClient.getProductSpecDefine(productSpecDefineFeignForm);
            Object specList = specResult.getData();
            if (Objects.isNull(specList)) {
                return result;
            }
            List<ProductSpecDefineFeignResult> productSpecDefine = JSON.parseArray(JSON.toJSONString(specList), ProductSpecDefineFeignResult.class);
            List<Map<String, Object>> vmTypes = Lists.newArrayList();
            productSpecDefine.stream().forEach(spec -> {
                vmTypes.add(MapsKit.of("family", spec.getProductSpecCode(),
                                       "familyName", spec.getProductSpecName(),
                                       "familyDesc", "default"));
            });
            result.put("application", vmTypes);
            return result;
        }
        this.checkCUEHide(result);

        return result;
    }

    private void checkCUEHide(Map<String, Object> result) {
        if (result == null) {
            return;
        }

        Object other = result.get("other");
        if (other == null) {
            return;
        }

        Criteria criteria = new Criteria();
        criteria.put("configType","other_config");
        criteria.put("configKey","cue.show.or.hide");
        List<SysConfig> configs = systemConfigMapper.displaySystemConfigList(criteria);
        String configValue = "hide";
        if (CollectionUtil.isNotEmpty(configs)) {
            configValue = configs.get(0).getConfigValue();
        }

        if ("show".equals(configValue)) {
            return;
        }

        List<Map<String, String>> list = (List<Map<String, String>>) other;
        for (Map<String, String> map : list) {
            String name = map.get("name");
            if (name.contains("CUE评分")) {
                map.put("name", this.getFilterCUE(name));
            }
        }
    }

    /**
     *  过滤CUE
     * @param value 模板配置
     * @return 过滤后的配置
     */
    private String getFilterCUE(String value) {
        int index = value.indexOf("（CUE");
        if (index > 0) {
            int end = value.indexOf('）', index);
            String filterValue = value.substring(0, index) + value.substring(end + 1);
            return getFilterCUE(filterValue);
        } else {
            return value;
        }
    }

    @Override
    public List<Map<String, String>> getServiceByTemplateId(Criteria criteria) {
        return serverTemplateMapper.getServiceByTemplateId(criteria);
    }

    @Override
    public boolean uniqueNameCheck(String templateName) {
        Criteria criteria = new Criteria();
        criteria.put("templateName", templateName);
        return serverTemplateMapper.countByParamsWithoutDf(criteria) == 0;
    }

    private void assertEnvExists(CloudEnv cloudEnv) {
        if (Objects.isNull(cloudEnv)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_832756465));
        }
    }

    /**
     * 非固定云环境的可用资源获取
     *
     * @param query
     * @param clazz
     * @param <T>
     */
    private <T extends Object> T getAvailableEnvResForUnFix(ServerTemplateResourceQuery query, Class<T> clazz) {
        T r = null;
        try {
            r = clazz.newInstance();
        } catch (Exception e) {
            logger.error(e.getMessage());
            return r;
        }
        final T result = r;
        String cloudEnvType = query.getCloudEnvType();
        //1、根据类型得到所有的云环境
        Criteria criteria = new Criteria();
        CloudEnvParams cloudEnvParams = new CloudEnvParams();
        if (Objects.nonNull(query.getCloudEnvId())) {
            cloudEnvParams.setCloudEnvId(query.getCloudEnvId());
        } else {
            cloudEnvParams.setCloudEnvType(cloudEnvType);
        }
        cloudEnvParams.setWithoutAttrData(Boolean.TRUE);
        cloudEnvParams.setStatusNotEqual(CloudEnvStatus.INACTIVE);
        List<CloudEnv> envList = this.cloudEnvService.selectCloudEnvByOrgId(cloudEnvParams);
        //2、根据云环境得到各自的可用资源
        ExecutorService threadPool = new ThreadPoolExecutor(10, 10,
                                                            10L, TimeUnit.MILLISECONDS,
                                                            new LinkedBlockingQueue<>(1024),
                                                            new ThreadFactoryBuilder()
                                                                    .setNameFormat("resource-template-pool-%d")
                                                                    .build(),
                                                            new ThreadPoolExecutor.AbortPolicy());
        logger.info("读取云环境数量: {}", envList.size());
        RequestContextHolder.setRequestAttributes(RequestContextHolder.currentRequestAttributes(), true);
        CountDownLatch countDownLatch = new CountDownLatch(envList.size());
        if (!CollectionUtils.isEmpty(envList)) {
            for (CloudEnv cloudEnv : envList) {
                Runnable runnable = () -> {
                    try {
                        // 可用区
                        if (CloudEnvType.VMWARE.equalValue(cloudEnv.getCloudEnvType())) {
                            ResPoolParams resPoolParams = new ResPoolParams();
                            resPoolParams.setCloudEnvId(cloudEnv.getId());
                            List<ResPool> resPools = resPoolService.selectByParams(resPoolParams);
                            for (ResPool pool : resPools) {
                                ServerTemplateResourceQuery resQuery = ServerTemplateResourceQuery.builder()
                                                                                                  .cloudEnvId(
                                                                                                          cloudEnv.getId())
                                                                                                  .cloudEnvType(
                                                                                                          cloudEnvType)
                                                                                                  .zone(pool.getId()
                                                                                                            .toString())
                                                                                                  .type(query.getType())
                                                                                                  .build();
                                resQuery.setCloudEnv(cloudEnv);
                                Map<String, Object> execute = serverTemplateAvailableResourceFactory
                                        .getInstance(cloudEnv.getCloudEnvType())
                                        .execute(resQuery);
                                if (result instanceof List) {
                                    ((List) result).add(execute);
                                } else if (result instanceof Map) {
                                    ((Map) result).put(pool.getId(), execute);
                                }
                            }
                        } else {
                            ResZoneParams resZoneParams = new ResZoneParams();
                            resZoneParams.setVirtTypeNotEq("BareMetal");
                            resZoneParams.setCloudEnvId(cloudEnv.getId());
                            // escloud， openstack， cloudos只查询计算可用区
                            if (CloudEnvType.OPEN_STACK.equalValue(cloudEnv.getCloudEnvType())
                                    || CloudEnvType.CLOUDOS.equalValue(cloudEnv.getCloudEnvType())) {
                                resZoneParams.setZoneType(ResZoneType.COMPUTE);
                            }
                            List<ResZone> resZones = resZoneService.selectByParams(cloudEnv.getId(), resZoneParams);
                            for (ResZone zone : resZones) {
                                ServerTemplateResourceQuery resQuery
                                        = ServerTemplateResourceQuery.builder()
                                                                     .cloudEnvId(cloudEnv.getId())
                                                                     .cloudEnvType(cloudEnvType)
                                                                     .zone(zone.getUuid())
                                                                     .type(query.getType())
                                                                     .build();
                                resQuery.setCloudEnv(cloudEnv);
                                Map<String, Object> execute = serverTemplateAvailableResourceFactory
                                        .getInstance(cloudEnv.getCloudEnvType())
                                        .execute(resQuery);
                                if (result instanceof List) {
                                    ((List) result).add(execute);
                                } else if (result instanceof Map) {
                                    ((Map) result).put(zone.getId(), execute);
                                }
                            }
                        }
                    } catch (Exception e) {
                        logger.error(e.getMessage(), e);
                    } finally {
                        countDownLatch.countDown();
                        logger.info("{} 剩余云环境:{}", query.getCloudEnvType(), countDownLatch.getCount());
                    }
                };
                threadPool.execute(runnable);
            }
            threadPool.shutdown();
            try {
                countDownLatch.await();
            } catch (InterruptedException e) {
                logger.info(e.getMessage(), e);
                Thread.currentThread().interrupt();
            }
        }
        return result;
    }

    public List<CloudEnv> getAvailableEnvByTemplateConfig(ServerTemplate serverTemplate) {
        ServerTemplate template = getNewTemplateConfig(serverTemplate, null);
        List<JsonNode> envConfig = JsonUtil.fromJson(template.getEnvConfig(), new TypeReference<List<JsonNode>>() {
        });
        if (!CollectionUtils.isEmpty(envConfig)) {
            List<Long> cloudEnvIds = envConfig.stream()
                                              .map(data -> Long.parseLong(data.get("cloudEnvId").toString()))
                                              .collect(Collectors.toList());
            QueryAllCloudEnvRequest queryAllCloudEnvRequest = new QueryAllCloudEnvRequest();
            queryAllCloudEnvRequest.setCloudEnvIds(cloudEnvIds);
            return cloudEnvService.selectAllCloudEnvByCompanyId(queryAllCloudEnvRequest);
        }
        return new ArrayList<>();
    }

    private ServerTemplate getNewTemplateConfig(ServerTemplate serverTemplate, @Nonnull Long cloudEnvId) {
        List<Map> newConfigMapList = Collections.synchronizedList(new ArrayList<>());
        // 1. 解析env_config
        List<JsonNode> envConfig = JsonUtil.fromJson(serverTemplate.getEnvConfig(),
                                                     new TypeReference<List<JsonNode>>() {
                                                     });

        // 如果云环境ID不为空，只取出相同云环境类型的配置信息，减少循环次数
        final CloudEnv inputCloudEnv = cloudEnvService.selectByPrimaryKey(cloudEnvId);
        cloudEnvService.assertEnvNonNull(inputCloudEnv);

        envConfig = envConfig.stream()
                             .filter(config -> Objects.equals(inputCloudEnv.getCloudEnvType(),
                                                              config.get("cloudEnvType").textValue()))
                             .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(envConfig)) {
            RequestContextHolder.setRequestAttributes(RequestContextHolder.currentRequestAttributes(), true);
            ExecutorService executorService = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
            CountDownLatch countDownLatch = new CountDownLatch(envConfig.size());
            //多个云环境
            for (JsonNode jsonNode : envConfig) {
                executorService.submit(() -> {
                    try {
                        //云环境的类型
                        String cloudEnvType = jsonNode.get("cloudEnvType").textValue();
                        ServerTemplateResourceQuery query = ServerTemplateResourceQuery.builder()
                                                                                       .cloudEnvType(cloudEnvType)
                                                                                       .cloudEnvId(cloudEnvId)
                                                                                       .type(serverTemplate.getType())
                                                                                       .build();
                        //云环境各个zone的资源详情
                        Map<Long, Map<String, Object>> resMap = getAvailableEnvResForUnFix(query, HashMap.class);
                        //多个分区
                        ArrayNode nodes = (ArrayNode) jsonNode.get("config");
                        if (nodes != null && nodes.size() > 0) {
                            List<Map> configList = JsonUtil.fromJson(JsonUtil.toJson(nodes), List.class);
                            if (ResourceType.RES_VD.equalsIgnoreCase(query.getType())
                            ) {
                                List<Map<String, Object>> templateConfigMaps = filterUnfixedEnvResource(configList,
                                                                                                        resMap,
                                                                                                        inputCloudEnv);
                                if (!CollectionUtils.isEmpty(templateConfigMaps)) {
                                    newConfigMapList.add(MapsKit.of("cloudEnvId", cloudEnvId,
                                                                    "config", templateConfigMaps));
                                }
                                return;
                            } else if (ResourceType.RES_FLOATING_IP.equalsIgnoreCase(query.getType())) {
                                // 没有可用区
                                List<Map<String, Object>> templateConfigMaps = filterUnfixedEnvResource(configList,
                                                                                                        resMap,
                                                                                                        inputCloudEnv);
                                if (!CollectionUtils.isEmpty(templateConfigMaps)) {
                                    Map<String, Object> map = templateConfigMaps.get(0);
                                    map.remove("zone");
                                    newConfigMapList.add(MapsKit.of("cloudEnvId", cloudEnvId,
                                                                    "config", Arrays.asList(map)));
                                }
                                return;
                            }
                            for (Map node : configList) {
                                List<Map> imageNodes = (List) node.get("image");
                                List<Map> sysDisks = (List) ((Map) node.get("sysDiskSize")).get("category");
                                List<Map> vmTypeNodes = (List) node.get("vmType");
                                List<Map> dataDiskNodes = (List) ((Map) node.get("dataDiskSize")).get("category");
                                //每个分区的配置详情
                                for (Long key : resMap.keySet()) {
                                    List<Map> imageList = new ArrayList<>();
                                    List<Map> vmTypeList = new ArrayList<>();
                                    List<Map> sysDiskList = new ArrayList<>();
                                    List<Map> dataDiskList = new ArrayList<>();

                                    List<Map> images = (List) resMap.get(key).get("image");
                                    List<String> cloudImageIds = images.stream()
                                            .map(image -> image.get("uuid").toString())
                                            .collect(Collectors.toList());
                                    List<String> configImageIds = imageNodes.stream()
                                            .map(image -> image.get("uuid").toString())
                                            .collect(Collectors.toList());
                                    cloudImageIds.retainAll(configImageIds);
                                    if (!CollectionUtils.isEmpty(cloudImageIds)) {
                                        List<Map> collect = imageNodes.stream()
                                                                      .filter(image -> cloudImageIds.contains(
                                                                              image.get("uuid")))
                                                                      .collect(Collectors.toList());
                                        imageList.addAll(collect);
                                    }

                                    List<Map> vmTypes = (List) resMap.get(key).get("vmType");
                                    for (Map vmType : vmTypes) {
                                        Iterator<Map> iterator = vmTypeNodes.iterator();
                                        while (iterator.hasNext()) {
                                            Map next = iterator.next();
                                            if (incloudeCloudEnvType(query.getCloudEnvType())) {
                                                // 如果都有family，比较family是否相同
                                                boolean hasFamily =
                                                        next.containsKey("family") && vmType.containsKey("family");
                                                boolean familyEqual = hasFamily && next.get("family")
                                                                                       .toString()
                                                                                       .equals(vmType.get("family")
                                                                                                     .toString());
                                                // 没有family字段，但是有familyName的话，比较familyName是否相同
                                                boolean hasFamilyName =
                                                        next.containsKey("familyName") && vmType.containsKey(
                                                                "familyName");
                                                boolean familyNameEqual = hasFamilyName && next.get("familyName")
                                                                                               .toString()
                                                                                               .equals(vmType.get(
                                                                                                       "familyName")
                                                                                                             .toString());
                                                if (familyEqual || familyNameEqual) {
                                                    List<Map> nextData = (List<Map>) next.get("data");
                                                    List<String> typeIds = nextData.stream()
                                                                                   .map(type -> type.get("uuid")
                                                                                                    .toString())
                                                                                   .collect(Collectors.toList());
                                                    List<Map> typeData = (List<Map>) vmType.get("data");
                                                    List<String> uuids = typeData.stream()
                                                                                 .map(m -> m.get("uuid").toString())
                                                                                 .collect(Collectors.toList());
                                                    typeIds.retainAll(uuids);
                                                    if (!CollectionUtils.isEmpty(typeIds)) {
                                                        List<Map> collect = nextData.stream()
                                                                                    .filter(type -> typeIds.contains(
                                                                                            type.get("uuid")))
                                                                                    .collect(Collectors.toList());
                                                        next.put("data", collect);
                                                        vmTypeList.add(next);
                                                    }
                                                }
                                            } else {
                                                if (next.get("uuid").equals(vmType.get("uuid"))) {
                                                    vmTypeList.add(next);
                                                }
                                            }
                                        }
                                    }

                                    List<Map> systemDisks = (List) resMap.get(key).get("systemDisk");
                                    if (!CollectionUtils.isEmpty(systemDisks)) {
                                        List<String> cloudSysDisks = systemDisks.stream()
                                                                                .filter(sys -> sys.containsKey("value"))
                                                                                .map(sys -> sys.get("value").toString())
                                                                                .collect(Collectors.toList());
                                        List<String> configSysDisks = sysDisks.stream()
                                                                              .filter(sys -> sys.containsKey("value"))
                                                                              .map(sys -> sys.get("value").toString())
                                                                              .collect(Collectors.toList());
                                        List<String> cloudSysUuids = systemDisks.stream()
                                                                                .filter(sys -> sys.containsKey("uuid"))
                                                                                .map(sys -> sys.get("uuid") + "")
                                                                                .collect(Collectors.toList());
                                        List<String> configSysUuids = sysDisks.stream()
                                                                              .filter(sys -> sys.containsKey("uuid"))
                                                                              .map(sys -> sys.get("uuid") + "")
                                                                              .collect(Collectors.toList());
                                        cloudSysDisks.retainAll(configSysDisks);
                                        cloudSysUuids.retainAll(configSysUuids);
                                        if (!CollectionUtils.isEmpty(cloudSysUuids)) {
                                            List<Map> collect = sysDisks.stream()
                                                                        .filter(sysDisk -> cloudSysUuids.contains(
                                                                                sysDisk.get("uuid").toString()))
                                                                        .collect(Collectors.toList());
                                            sysDiskList.addAll(collect);
                                        } else if (!CollectionUtils.isEmpty(cloudSysDisks)) {
                                            List<Map> collect = sysDisks.stream()
                                                                        .filter(sysDisk -> cloudSysDisks.contains(
                                                                                sysDisk.get("value").toString()))
                                                                        .collect(Collectors.toList());
                                            sysDiskList.addAll(collect);
                                        }
                                    }

                                    List<Map> dataDisks = (List) resMap.get(key).get("dataDisk");
                                    if (!CollectionUtils.isEmpty(dataDisks)) {
                                        List<String> cloudDataDisks = dataDisks.stream()
                                                                               .filter(data -> data.containsKey(
                                                                                       "value"))
                                                                               .map(data -> data.get("value")
                                                                                                .toString())
                                                                               .collect(Collectors.toList());
                                        List<String> configDisks = dataDiskNodes.stream()
                                                                                .filter(data -> data.containsKey(
                                                                                        "value"))
                                                                                .map(data -> data.get("value")
                                                                                                 .toString())
                                                                                .collect(Collectors.toList());
                                        List<String> cloudDataUuids = dataDisks.stream()
                                                                               .filter(data -> data.containsKey("uuid"))
                                                                               .map(data -> data.get("uuid") + "")
                                                                               .collect(Collectors.toList());
                                        List<String> configUuids = dataDiskNodes.stream()
                                                                                .filter(data -> data.containsKey(
                                                                                        "uuid"))
                                                                                .map(data -> data.get("uuid") + "")
                                                                                .collect(Collectors.toList());
                                        cloudDataDisks.retainAll(configDisks);
                                        cloudDataUuids.retainAll(configUuids);
                                        if (!CollectionUtils.isEmpty(cloudDataUuids)) {
                                            List<Map> collect = dataDiskNodes.stream()
                                                                             .filter(dataDisk -> cloudDataUuids.contains(
                                                                                     dataDisk.get("uuid").toString()))
                                                                             .collect(Collectors.toList());
                                            dataDiskList.addAll(collect);
                                        } else if (!CollectionUtils.isEmpty(cloudDataDisks)) {
                                            List<Map> collect = dataDiskNodes.stream()
                                                                             .filter(dataDisk -> cloudDataDisks.contains(
                                                                                     dataDisk.get("value").toString()))
                                                                             .collect(Collectors.toList());
                                            dataDiskList.addAll(collect);
                                        }
                                    }

                                    List<Object> resourceGroup = Lists.newArrayList();
                                    if (CloudEnvType.AZURE.equalValue(cloudEnvType)) {
                                        resourceGroup = (List) resMap.get(key).get("resourceGroup");
                                    }

                                    if (!CollectionUtils.isEmpty(imageList) && !CollectionUtils.isEmpty(vmTypeList)) {
                                        Map<String, Object> templateConfigMap = new HashMap<>();
                                        Map<String, Object> envConfigMap = new HashMap<>();
                                        Map<String, Object> zoneMap = new HashMap<>();
                                        Long envId = null;
                                        if (CloudEnvType.VMWARE.equalValue(cloudEnvType)) {
                                            ResPool resPool = resPoolService.selectByPrimaryKey(key);
                                            envId = resPool.getCloudEnvId();
                                            zoneMap.put("id", resPool.getId());
                                            zoneMap.put("name", resPool.getName());
                                            zoneMap.put("uuid", resPool.getId());
                                        } else {
                                            ResZone resZone = resZoneService.selectByPrimaryKey(key);
                                            envId = resZone.getCloudEnvId();
                                            zoneMap.put("id", resZone.getUuid());
                                            zoneMap.put("name", resZone.getName());
                                            zoneMap.put("uuid", resZone.getUuid());
                                        }
                                        if (null != envId) {
                                            CloudEnv cloudEnv = cloudEnvService.selectByPrimaryKey(envId);
                                            templateConfigMap.put("cloudEnvId", cloudEnv.getId());
                                            templateConfigMap.put("cloudEnvType", cloudEnv.getCloudEnvType());
                                            templateConfigMap.put("cloudEnvName", cloudEnv.getCloudEnvName());
                                            if (CloudEnvType.AZURE.equalValue(cloudEnvType)) {
                                                envConfigMap.put("resourceGroup", resourceGroup);
                                            }

                                            if (resMap.get(key).containsKey("securityGroup")) {
                                                envConfigMap.put("securityGroup", resMap.get(key).get("securityGroup"));
                                            }

                                            if (resMap.get(key).containsKey("serverGroup")) {
                                                envConfigMap.put("serverGroup", resMap.get(key).get("serverGroup"));
                                            }

                                            if (resMap.get(key).containsKey("floatingIpPool")) {
                                                envConfigMap.put("floatingIpPool", resMap.get(key).get("floatingIpPool"));
                                            }

                                            envConfigMap.put("cloudEnvId", cloudEnv.getId());
                                            envConfigMap.put("cloudEnvType", cloudEnv.getCloudEnvType());
                                            envConfigMap.put("zone", zoneMap);
                                            envConfigMap.put("vmType", vmTypeList);
                                            envConfigMap.put("image", imageList);
                                            Map sysDisk = new HashMap((Map) node.get("sysDiskSize"));
                                            sysDisk.put("category", sysDiskList);
                                            envConfigMap.put("sysDiskSize", sysDisk);
                                            Map dataDisk = new HashMap((Map) node.get("dataDiskSize"));
                                            dataDisk.put("category", dataDiskList);
                                            envConfigMap.put("dataDiskSize", dataDisk);
                                            List network = (List) resMap.get(key).get("network");
                                            if (!CollectionUtils.isEmpty(network)) {
                                                //获取网络
                                                envConfigMap.put("network", network);
                                                //默认登录方式
                                                List<Map> remoteLoginTypeList = new ArrayList<>();
                                                Map<String, Object> loginTypeMap = new HashMap<>();
                                                loginTypeMap.put("name", "密码");
                                                loginTypeMap.put("value", "ByPassword");
                                                loginTypeMap.put("checked", true);
                                                remoteLoginTypeList.add(loginTypeMap);
                                                envConfigMap.put("remoteLoginType", remoteLoginTypeList);
                                                //默认收费方式
                                                List<Map> chargeTypeList = new ArrayList<>();
                                                if (node.get("chargeType") != null) {
                                                    List<Map> chargeType = JsonUtil.fromJson(
                                                            JsonUtil.toJson(node.get("chargeType")),
                                                            new TypeReference<List<Map>>() {
                                                            });
                                                    chargeTypeList.addAll(chargeType);
                                                } else {
                                                    Map<String, Object> chargeTypeMap = new HashMap<>();
                                                    chargeTypeMap.put("name", "按量付费");
                                                    chargeTypeMap.put("value", "PostPaid");
                                                    chargeTypeMap.put("checked", false);
                                                    chargeTypeList.add(chargeTypeMap);
                                                    chargeTypeMap.put("name", "包年包月");
                                                    chargeTypeMap.put("value", "PrePaid");
                                                    chargeTypeMap.put("checked", false);
                                                    chargeTypeList.add(chargeTypeMap);
                                                }
                                                envConfigMap.put("chargeType", chargeTypeList);
                                                List<Map> configMapList = new ArrayList<>();
                                                configMapList.add(envConfigMap);
                                                templateConfigMap.put("config", configMapList);

                                                newConfigMapList.add(templateConfigMap);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        logger.error(e.getMessage(), e);
                    } finally {
                        countDownLatch.countDown();
                    }
                });
            }

            try {
                countDownLatch.await();
            } catch (InterruptedException e) {
                logger.error(Throwables.getStackTraceAsString(e));
                Thread.currentThread().interrupt();
            }
        }

        //相同云环境进行合并
        Set<Long> cloudEnvIds = newConfigMapList.stream()
                                                .map(data -> Long.parseLong(data.get("cloudEnvId").toString()))
                                                .collect(Collectors.toSet());
        List<Map> newConfigNodes = new ArrayList<>();
        QueryAllCloudEnvRequest queryAllCloudEnvRequest = new QueryAllCloudEnvRequest();
        queryAllCloudEnvRequest.setCloudEnvIds(new ArrayList<>(cloudEnvIds));
        List<CloudEnv> cloudEnvs = cloudEnvService.selectAllCloudEnvByCompanyId(queryAllCloudEnvRequest);

        for (CloudEnv cloudEnv : cloudEnvs) {
            Map<String, Object> templateConfigMap = new HashMap<>();
            templateConfigMap.put("cloudEnvId", cloudEnv.getId());
            templateConfigMap.put("cloudEnvType", cloudEnv.getCloudEnvType());
            templateConfigMap.put("cloudEnvName", cloudEnv.getCloudEnvName());
            List<Map> envConfigMapList = new ArrayList<>();
            for (Map configNodeMap : newConfigMapList) {
                if (cloudEnv.getId().equals(Long.parseLong(configNodeMap.get("cloudEnvId").toString()))) {
                    List<Map> configMapList = (List) configNodeMap.get("config");
                    envConfigMapList.addAll(configMapList);
                }
            }
            templateConfigMap.put("config", envConfigMapList);

            newConfigNodes.add(templateConfigMap);
        }
        serverTemplate.setEnvConfig(JsonUtil.toJson(newConfigNodes));
        return serverTemplate;
    }

    private static boolean incloudeCloudEnvType(String cloudEnvType) {
        return Stream.of(CloudEnvType.ALIYUN.getValue(),
            CloudEnvType.QCLOUD.getValue(),
            CloudEnvType.AZURE.getValue(),
            CloudEnvType.AWS.getValue(),
            CloudEnvType.HUAWEICLOUD.getValue()).anyMatch(type -> type.contains(cloudEnvType));
    }
}
