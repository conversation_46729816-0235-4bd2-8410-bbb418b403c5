<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.cloudstar.rightcloud.oss.module.order.dao.BizCouponResourceMapper" >
  <resultMap id="BaseResultMap" type="cn.com.cloudstar.rightcloud.core.pojo.dto.bss.BizCouponResource" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="coupon_sid" property="couponSid" jdbcType="BIGINT" />
    <result column="coupon_no" property="couponNo" jdbcType="VARCHAR" />
    <result column="resource_type" property="resourceType" jdbcType="VARCHAR" />
    <result column="resource_id" property="resourceId" jdbcType="BIGINT" />
    <result column="resource_uuid" property="resourceUuid" jdbcType="VARCHAR" />
    <result column="order_id" property="orderId" jdbcType="BIGINT" />
    <result column="status" property="status" jdbcType="VARCHAR" />
    <result column="version" property="version" jdbcType="INTEGER" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="created_dt" property="createdDt" jdbcType="TIMESTAMP" />
    <result column="updated_by" property="updatedBy" jdbcType="VARCHAR" />
    <result column="updated_dt" property="updatedDt" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <trim prefix="where" prefixOverrides="and|or" >
      <if test="condition.couponSid != null" >
         and coupon_sid = #{condition.couponSid}
      </if>
      <if test="condition.couponNo != null" >
         and coupon_no = #{condition.couponNo}
      </if>
      <if test="condition.resourceType != null" >
         and resource_type = #{condition.resourceType}
      </if>
      <if test="condition.resourceId != null" >
         and resource_id = #{condition.resourceId}
      </if>
      <if test="condition.resourceUuid != null" >
         and resource_uuid = #{condition.resourceUuid}
      </if>
      <if test="condition.orderId != null" >
         and order_id = #{condition.orderId}
      </if>
      <if test="condition.status != null" >
         and status = #{condition.status}
      </if>
      <if test="condition.version != null" >
         and version = #{condition.version}
      </if>
      <if test="condition.createdBy != null" >
         and created_by = #{condition.createdBy}
      </if>
      <if test="condition.createdDt != null" >
         and created_dt = #{condition.createdDt}
      </if>
      <if test="condition.updatedBy != null" >
         and updated_by = #{condition.updatedBy}
      </if>
      <if test="condition.updatedDt != null" >
         and updated_dt = #{condition.updatedDt}
      </if>
    </trim>
  </sql>
  <sql id="Base_Column_List" >
    id, coupon_sid, coupon_no, resource_type, resource_id, resource_uuid, order_id, status, 
    version, created_by, created_dt, updated_by, updated_dt
  </sql>
  <select id="selectByParams" resultMap="BaseResultMap" parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from biz_coupon_resource
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>

  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from biz_coupon_resource
    where id = #{id}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from biz_coupon_resource
    where id = #{id}
  </delete>
  <delete id="deleteByParams" parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria" >
    delete from biz_coupon_resource
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="cn.com.cloudstar.rightcloud.core.pojo.dto.bss.BizCouponResource" >
    insert into biz_coupon_resource (id, coupon_sid, coupon_no, resource_type, resource_id, resource_uuid, 
      order_id, status, version, created_by, created_dt, updated_by, updated_dt
      )
    values (#{id}, #{couponSid}, #{couponNo}, #{resourceType}, #{resourceId}, #{resourceUuid}, 
      #{orderId}, #{status}, #{version}, #{createdBy}, #{createdDt}, #{updatedBy}, #{updatedDt}
      )
  </insert>
  <insert id="insertSelective" parameterType="cn.com.cloudstar.rightcloud.core.pojo.dto.bss.BizCouponResource" >
    insert into biz_coupon_resource
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="couponSid != null" >
        coupon_sid,
      </if>
      <if test="couponNo != null" >
        coupon_no,
      </if>
      <if test="resourceType != null" >
        resource_type,
      </if>
      <if test="resourceId != null" >
        resource_id,
      </if>
      <if test="resourceUuid != null" >
        resource_uuid,
      </if>
      <if test="orderId != null" >
        order_id,
      </if>
      <if test="status != null" >
        status,
      </if>
      <if test="version != null" >
        version,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="createdDt != null" >
        created_dt,
      </if>
      <if test="updatedBy != null" >
        updated_by,
      </if>
      <if test="updatedDt != null" >
        updated_dt,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id},
      </if>
      <if test="couponSid != null" >
        #{couponSid},
      </if>
      <if test="couponNo != null" >
        #{couponNo},
      </if>
      <if test="resourceType != null" >
        #{resourceType},
      </if>
      <if test="resourceId != null" >
        #{resourceId},
      </if>
      <if test="resourceUuid != null" >
        #{resourceUuid},
      </if>
      <if test="orderId != null" >
        #{orderId},
      </if>
      <if test="status != null" >
        #{status},
      </if>
      <if test="version != null" >
        #{version},
      </if>
      <if test="createdBy != null" >
        #{createdBy},
      </if>
      <if test="createdDt != null" >
        #{createdDt},
      </if>
      <if test="updatedBy != null" >
        #{updatedBy},
      </if>
      <if test="updatedDt != null" >
        #{updatedDt},
      </if>
    </trim>
  </insert>
  <select id="countByParams" parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria" resultType="java.lang.Integer" >
    select count(*) from biz_coupon_resource
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByParamsSelective" parameterType="map" >
    update biz_coupon_resource
    <set >
      <if test="record.id != null" >
        id = #{record.id},
      </if>
      <if test="record.couponSid != null" >
        coupon_sid = #{record.couponSid},
      </if>
      <if test="record.couponNo != null" >
        coupon_no = #{record.couponNo},
      </if>
      <if test="record.resourceType != null" >
        resource_type = #{record.resourceType},
      </if>
      <if test="record.resourceId != null" >
        resource_id = #{record.resourceId},
      </if>
      <if test="record.resourceUuid != null" >
        resource_uuid = #{record.resourceUuid},
      </if>
      <if test="record.orderId != null" >
        order_id = #{record.orderId},
      </if>
      <if test="record.status != null" >
        status = #{record.status},
      </if>
      <if test="record.version != null" >
        version = #{record.version},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy},
      </if>
      <if test="record.createdDt != null" >
        created_dt = #{record.createdDt},
      </if>
      <if test="record.updatedBy != null" >
        updated_by = #{record.updatedBy},
      </if>
      <if test="record.updatedDt != null" >
        updated_dt = #{record.updatedDt},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByParams" parameterType="map" >
    update biz_coupon_resource
    set id = #{record.id},
      coupon_sid = #{record.couponSid},
      coupon_no = #{record.couponNo},
      resource_type = #{record.resourceType},
      resource_id = #{record.resourceId},
      resource_uuid = #{record.resourceUuid},
      order_id = #{record.orderId},
      status = #{record.status},
      version = #{record.version},
      created_by = #{record.createdBy},
      created_dt = #{record.createdDt},
      updated_by = #{record.updatedBy},
      updated_dt = #{record.updatedDt}
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="cn.com.cloudstar.rightcloud.core.pojo.dto.bss.BizCouponResource" >
    update biz_coupon_resource
    <set >
      <if test="couponSid != null" >
        coupon_sid = #{couponSid},
      </if>
      <if test="couponNo != null" >
        coupon_no = #{couponNo},
      </if>
      <if test="resourceType != null" >
        resource_type = #{resourceType},
      </if>
      <if test="resourceId != null" >
        resource_id = #{resourceId},
      </if>
      <if test="resourceUuid != null" >
        resource_uuid = #{resourceUuid},
      </if>
      <if test="orderId != null" >
        order_id = #{orderId},
      </if>
      <if test="status != null" >
        status = #{status},
      </if>
      <if test="version != null" >
        version = #{version},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy},
      </if>
      <if test="createdDt != null" >
        created_dt = #{createdDt},
      </if>
      <if test="updatedBy != null" >
        updated_by = #{updatedBy},
      </if>
      <if test="updatedDt != null" >
        updated_dt = #{updatedDt},
      </if>
    </set>
    where id = #{id}
  </update>
  <update id="updateByPrimaryKey" parameterType="cn.com.cloudstar.rightcloud.core.pojo.dto.bss.BizCouponResource" >
    update biz_coupon_resource
    set coupon_sid = #{couponSid},
      coupon_no = #{couponNo},
      resource_type = #{resourceType},
      resource_id = #{resourceId},
      resource_uuid = #{resourceUuid},
      order_id = #{orderId},
      status = #{status},
      version = #{version},
      created_by = #{createdBy},
      created_dt = #{createdDt},
      updated_by = #{updatedBy},
      updated_dt = #{updatedDt}
    where id = #{id}
  </update>
</mapper>