<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.cloudstar.rightcloud.oss.module.selfservice.dao.ResFederationInstMapper">

    <insert id="saveResFederationInst" useGeneratedKeys="true" keyProperty="id"
            parameterType="cn.com.cloudstar.rightcloud.oss.module.selfservice.bean.model.ResFederationInst">
        insert into res_federation_inst
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="resourceId != null">
                resource_id,
            </if>
            <if test="productCode != null">
                product_code,
            </if>
            <if test="federationUrl != null">
                federation_url,
            </if>
            <if test="chargeType != null">
                charge_type,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="freezedTime != null">
                freezed_time,
            </if>
            <if test="orgSid != null">
                org_sid,
            </if>
            <if test="ownerSid != null">
                owner_sid,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="createdDt != null">
                created_dt,
            </if>
            <if test="updatedBy != null">
                updated_by,
            </if>
            <if test="updatedDt != null">
                updated_dt,
            </if>
            <if test="version != null">
                version,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="resourceId != null">
                #{resourceId},
            </if>
            <if test="productCode != null">
                #{productCode},
            </if>
            <if test="federationUrl != null">
                #{federationUrl},
            </if>
            <if test="chargeType != null">
                #{chargeType},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="startTime != null">
                #{startTime},
            </if>
            <if test="endTime != null">
                #{endTime},
            </if>
            <if test="freezedTime != null">
                #{freezedTime},
            </if>
            <if test="orgSid != null">
                #{orgSid},
            </if>
            <if test="ownerSid != null">
                #{ownerSid},
            </if>
            <if test="createdBy != null">
                #{createdBy},
            </if>
            <if test="createdDt != null">
                #{createdDt},
            </if>
            <if test="updatedBy != null">
                #{updatedBy},
            </if>
            <if test="updatedDt != null">
                #{updatedDt},
            </if>
            <if test="version != null">
                #{version},
            </if>
        </trim>
    </insert>

    <update id="updateResFederationInst" parameterType="cn.com.cloudstar.rightcloud.oss.module.selfservice.bean.model.ResFederationInst">
        update res_federation_inst
        <set>
            <if test="productCode != null">
                product_code = #{productCode},
            </if>
            <if test="federationUrl != null">
                federation_url = #{federationUrl},
            </if>
            <if test="chargeType != null">
                charge_type = #{chargeType},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="startTime != null">
                start_time = #{startTime},
            </if>
            <if test="endTime != null">
                end_time = #{endTime},
            </if>
            <if test="freezedTime != null">
                freezed_time = #{freezedTime},
            </if>
            <if test="orgSid != null">
                org_sid = #{orgSid},
            </if>
            <if test="ownerSid != null">
                owner_sid = #{ownerSid},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy},
            </if>
            <if test="createdDt != null">
                created_dt = #{createdDt},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy},
            </if>
            <if test="updatedDt != null">
                updated_dt = #{updatedDt},
            </if>
            <if test="version != null">
                version = #{version},
            </if>
        </set>
        where resource_id =#{resourceId}
    </update>
    <select id="view" resultType="cn.com.cloudstar.rightcloud.oss.module.resource.ResCountInfoDto">
        select product_code as resType, count(*) AS
              resCount from res_federation_inst
           where `status` not in( 'unsubscribed', 'rejected')
        <if test="orgSids != null and orgSids.size() > 0">
            and `org_sid` in
            <foreach collection="orgSids" open="(" close=")" item="orgSid" separator=",">
                #{orgSid}
            </foreach>
        </if>
        group by product_code
    UNION
        select IFNULL(product_type,'ModelArts') AS resType, count(*) AS resCount
        from sf_product_resource
        where product_type = 'ModelArts'  and `status` not in( 'unsubscribed', 'rejected')
        <if test="orgSids != null and orgSids.size() > 0">
            and `org_sid` in
            <foreach collection="orgSids" open="(" close=")" item="orgSid" separator=",">
                #{orgSid}
            </foreach>
        </if>
   UNION
        select "SWR" as resType, count(*) AS resCount from image_warehouse
        where 1=1
        <if test="orgSids != null and orgSids.size() > 0">
            and `org_sid` in
            <foreach collection="orgSids" open="(" close=")" item="orgSid" separator=",">
                #{orgSid}
            </foreach>
        </if>


       <!--
        select product_code as resType, count(*) AS resCount from res_federation_inst where product_code = 'ROMA-CONNECT' and `status` not in( 'unsubscribed', 'rejected')
        <if test="orgSids != null and orgSids.size() > 0">
            and `org_sid` in
            <foreach collection="orgSids" open="(" close=")" item="orgSid" separator=",">
                #{orgSid}
            </foreach>
        </if>

        UNION
        select product_code as resType, count(*) AS resCount from res_federation_inst where product_code = 'CES' and `status` not in( 'unsubscribed', 'rejected')
        <if test="orgSids != null and orgSids.size() > 0">
            and `org_sid` in
            <foreach collection="orgSids" open="(" close=")" item="orgSid" separator=",">
                #{orgSid}
            </foreach>
        </if>

        UNION
        select product_code as resType, count(*) AS resCount from res_federation_inst where product_code = 'HSS' and `status` not in( 'unsubscribed', 'rejected')
        <if test="orgSids != null and orgSids.size() > 0">
            and `org_sid` in
            <foreach collection="orgSids" open="(" close=")" item="orgSid" separator=",">
                #{orgSid}
            </foreach>
        </if>

        UNION
        select product_code as resType, count(*) AS resCount from res_federation_inst where product_code = 'SEC-MASTER' and `status` not in( 'unsubscribed', 'rejected')
        <if test="orgSids != null and orgSids.size() > 0">
            and `org_sid` in
            <foreach collection="orgSids" open="(" close=")" item="orgSid" separator=",">
                #{orgSid}
            </foreach>
        </if>

        UNION
        select product_code as resType, count(*) AS resCount from res_federation_inst where product_code = 'DNS' and `status` not in( 'unsubscribed', 'rejected')
        <if test="orgSids != null and orgSids.size() > 0">
            and `org_sid` in
            <foreach collection="orgSids" open="(" close=")" item="orgSid" separator=",">
                #{orgSid}
            </foreach>
        </if>

        UNION
        select product_code as resType, count(*) AS resCount from res_federation_inst where product_code = 'WAF' and `status` not in( 'unsubscribed', 'rejected')
        <if test="orgSids != null and orgSids.size() > 0">
            and `org_sid` in
            <foreach collection="orgSids" open="(" close=")" item="orgSid" separator=",">
                #{orgSid}
            </foreach>
        </if>

        UNION
        select product_code as resType, count(*) AS resCount from res_federation_inst where product_code = 'DEW' and `status` not in( 'unsubscribed', 'rejected')
        <if test="orgSids != null and orgSids.size() > 0">
            and `org_sid` in
            <foreach collection="orgSids" open="(" close=")" item="orgSid" separator=",">
                #{orgSid}
            </foreach>
        </if>


        UNION
        select product_code as resType, count(*) AS resCount from res_federation_inst where product_code = 'CFW' and `status` not in( 'unsubscribed', 'rejected')
        <if test="orgSids != null and orgSids.size() > 0">
            and `org_sid` in
            <foreach collection="orgSids" open="(" close=")" item="orgSid" separator=",">
                #{orgSid}
            </foreach>
        </if>


        UNION
        select product_code as resType, count(*) AS resCount from res_federation_inst where product_code = 'CGS' and `status` not in( 'unsubscribed', 'rejected')
        <if test="orgSids != null and orgSids.size() > 0">
            and `org_sid` in
            <foreach collection="orgSids" open="(" close=")" item="orgSid" separator=",">
                #{orgSid}
            </foreach>
        </if>

        UNION
        select product_code as resType, count(*) AS resCount from res_federation_inst where product_code = 'DBSS' and `status` not in( 'unsubscribed', 'rejected')
        <if test="orgSids != null and orgSids.size() > 0">
            and `org_sid` in
            <foreach collection="orgSids" open="(" close=")" item="orgSid" separator=",">
                #{orgSid}
            </foreach>
        </if>

        UNION
        select product_code as resType, count(*) AS resCount from res_federation_inst where product_code = 'ModelArts' and `status` not in( 'unsubscribed', 'rejected')
        <if test="orgSids != null and orgSids.size() > 0">
            and `org_sid` in
            <foreach collection="orgSids" open="(" close=")" item="orgSid" separator=",">
                #{orgSid}
            </foreach>
        </if>

        UNION
        select product_code as resType, count(*) AS resCount from res_federation_inst where product_code = 'GIT' and `status` not in( 'unsubscribed', 'rejected')
        <if test="orgSids != null and orgSids.size() > 0">
            and `org_sid` in
            <foreach collection="orgSids" open="(" close=")" item="orgSid" separator=",">
                #{orgSid}
            </foreach>
        </if>

        UNION
        select product_code as resType, count(*) AS resCount from res_federation_inst where product_code = 'DWS' and `status` not in( 'unsubscribed', 'rejected')
        <if test="orgSids != null and orgSids.size() > 0">
            and `org_sid` in
            <foreach collection="orgSids" open="(" close=")" item="orgSid" separator=",">
                #{orgSid}
            </foreach>
        </if>

        UNION
        select product_code as resType, count(*) AS resCount from res_federation_inst where product_code = 'MRS' and `status` not in( 'unsubscribed', 'rejected')
        <if test="orgSids != null and orgSids.size() > 0">
            and `org_sid` in
            <foreach collection="orgSids" open="(" close=")" item="orgSid" separator=",">
                #{orgSid}
            </foreach>
        </if>

        UNION
        select product_code as resType, count(*) AS resCount from res_federation_inst where product_code = 'CSS' and `status` not in( 'unsubscribed', 'rejected')
        <if test="orgSids != null and orgSids.size() > 0">
            and `org_sid` in
            <foreach collection="orgSids" open="(" close=")" item="orgSid" separator=",">
                #{orgSid}
            </foreach>
        </if>

        UNION
        select product_code as resType, count(*) AS resCount from res_federation_inst where product_code = 'DS' and `status` not in( 'unsubscribed', 'rejected')
         <if test="orgSids != null and orgSids.size() > 0">
            and `org_sid` in
            <foreach collection="orgSids" open="(" close=")" item="orgSid" separator=",">
                #{orgSid}
            </foreach>
        </if>-->

    </select>
    <select id="selectListByParam"
            parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria"
            resultType="cn.com.cloudstar.rightcloud.oss.module.selfservice.dto.FederationInstRequestResponse">

        select t1.* ,t2.cloud_env_id,t4.org_name as companyName from res_federation_inst t1
            left join sf_product_resource t2 on t1.resource_id = t2.id
            left join sys_m_user t3 on t1.created_by = t3.account
            left join sys_m_org t4 on t3.org_sid = t4.org_sid
            where product_code = #{condition.productCode}
            and t1.status not in('unsubscribed','rejected')
            <if test="condition.companyName != null and condition.companyName != ''">
                and t4.`org_name` LIKE concat('%', #{condition.companyName}, '%')
            </if>
            <if test="condition.orgSids != null and condition.orgSids.size() > 0">
                and t1.`org_sid` in
                <foreach collection="condition.orgSids" open="(" close=")" item="orgSid" separator=",">
                    #{orgSid}
                </foreach>
            </if>

    </select>
</mapper>
