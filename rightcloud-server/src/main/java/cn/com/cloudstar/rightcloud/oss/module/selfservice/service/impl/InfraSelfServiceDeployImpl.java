/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.module.selfservice.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.ImmutableMap;

import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Objects;

import cn.com.cloudstar.rightcloud.common.additional.ResInstResult;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.Org;
import cn.com.cloudstar.rightcloud.core.pojo.models.selfservice.SelfServiceDeploymentModel;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.OrgType;
import cn.com.cloudstar.rightcloud.oss.module.account.service.org.OrgService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.project.ProjectService;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.service.SelfServiceDeployService;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.instance.CloudCommonInst;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.server.ResVmRemoteService;

@Service("infraSelfServiceDeployService")
public class InfraSelfServiceDeployImpl implements SelfServiceDeployService {

    private static ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 批量创建中，是否不是第一台机器的标识
     */
    private static final String NOT_FIRST = "notFirst";

    @DubboReference
    private ResVmRemoteService resVmRemoteService;

    @Autowired
    @Lazy
    private ProjectService projectService;

    @Autowired
    private OrgService orgService;

    @Override
    public ResInstResult deployCloudService(SelfServiceDeploymentModel selfServiceDeploymentModel, AuthUser authUser) {
        CloudCommonInst cloudVmInst = new CloudCommonInst();
        cloudVmInst.setUserAccount(authUser.getAccount());
        cloudVmInst.setUserId(authUser.getUserSid().toString());
        cloudVmInst.setDuration(selfServiceDeploymentModel.getDuration());
        cloudVmInst.setOrgSid(authUser.getOrgSid());
        Map<String, Object> extraParams = selfServiceDeploymentModel.getExtraParams();
        JsonNode parameters = objectMapper.valueToTree(extraParams);

        //余额
        Org org = orgService.selectByPrimaryKey(selfServiceDeploymentModel.getOrgSid());
        if (Objects.nonNull(org) && OrgType.PROJECT.equals(org.getOrgType())) {
            JsonNode hostSpec = parameters.deepCopy();
            // 默认创建一台
            ((ObjectNode) hostSpec).put("hostCount", 1);
            projectService.checkQuota(org.getOrgSid(), hostSpec, BigDecimal.ZERO, null);
        }

        cloudVmInst.setCloudSpec(parameters);
        ObjectNode node = (ObjectNode) parameters.findValue("instance");

        if (node.get(NOT_FIRST) != null && node.get(NOT_FIRST).asBoolean()) {
            cloudVmInst.setOptions(ImmutableMap.of(NOT_FIRST, true));
        }
        cloudVmInst.setSfServiceDeployInstId(selfServiceDeploymentModel.getServiceInstanceId());
        cloudVmInst.setEndTime(selfServiceDeploymentModel.getEndTime());
        cloudVmInst.setChargeType(selfServiceDeploymentModel.getChargeType());
        ResInstResult serverToPaas = this.resVmRemoteService.createServerToPaas(
                cloudVmInst);

        return serverToPaas;
    }
}
