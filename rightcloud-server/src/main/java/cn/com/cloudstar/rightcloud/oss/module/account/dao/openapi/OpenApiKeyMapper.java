/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.module.account.dao.openapi;

import cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.core.pojo.dto.openapi.OpenApiKey;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 *
 * <AUTHOR>
 * @date 2018/11/16
 */
@Repository
public interface OpenApiKeyMapper {
    /**
     * 条件查询openapikey
     * @param criteria
     * @return
     */
    List<OpenApiKey> selectByUserSid(Criteria criteria);

    /**
     * 根据id 查询openapikey
     * @param id
     * @return
     */
    OpenApiKey selectByPrimaryKey(Long id);

    /**
     * 创建
     * @param apiKey
     * @return
     */
    int insertSelective(OpenApiKey apiKey);

    /**
     * 更新
     * @param apiKey
     * @return
     */
    int updateSelectiveByPrimaryKey(OpenApiKey apiKey);

    /**
     * 更新 状态
     * @param criteria
     * @param status
     * @return
     *
     */
    int updateStatusByParams(@Param("criteria") Criteria criteria, @Param("status") boolean status);

    /**
     * 更新openapikey的状态
     * @param id
     * @param status
     * @return
     */
    int updateStatusByPrimaryKey(@Param("id") Long id, @Param("status") boolean status);

    /**
     * 删除openapikey
     * @param id
     * @return
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 删除
     * @param criteria
     * @return
     */
    int deleteByParams(Criteria criteria);

    /**
     * 查询openapikey数量
     * @param criteria
     * @return
     */
    int countByParam(Criteria criteria);

    /**
     * 根据openkeyId获取openapikey
     * @param apiKeyId
     * @return
     */
    OpenApiKey selectByApiKeyId(String apiKeyId);
}
