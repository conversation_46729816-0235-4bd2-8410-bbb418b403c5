/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.module.system.controller.config;

import cn.com.cloudstar.rightcloud.basic.data.pojo.system.ActionLog;
import cn.com.cloudstar.rightcloud.common.constants.AuthConstants;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.BD07;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.CONFIG;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.ZF;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.ZF.ZF05;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.ZF.ZF10;
import cn.com.cloudstar.rightcloud.common.constants.NotificationConsts;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.enums.CommonPropertyKeyEnum;
import cn.com.cloudstar.rightcloud.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.common.util.*;
import cn.com.cloudstar.rightcloud.core.pojo.common.ShareConfig;
import cn.com.cloudstar.rightcloud.core.pojo.constant.ProtocolConstant;
import cn.com.cloudstar.rightcloud.core.pojo.dto.charge.BizBillingAccount;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.Message;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.SysConfig;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.RoleModule;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.User;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.UserRole;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.module.support.file.storage.bean.enums.StoragePathEnum;
import cn.com.cloudstar.rightcloud.module.support.file.storage.service.StorageService;
import cn.com.cloudstar.rightcloud.operationlog.annotation.OperationLog;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationTypeEnum;
import cn.com.cloudstar.rightcloud.operationlog.util.OperationLogMdcUtil;
import cn.com.cloudstar.rightcloud.oss.ccsp.CCSPDataUpgrade;
import cn.com.cloudstar.rightcloud.oss.common.annotation.AuthorizeOss;
import cn.com.cloudstar.rightcloud.oss.common.annotation.RejectCall;
import cn.com.cloudstar.rightcloud.oss.common.api.ApiGroup;
import cn.com.cloudstar.rightcloud.oss.common.api.ApiGroupEnum;
import cn.com.cloudstar.rightcloud.oss.common.cache.JedisUtil;
import cn.com.cloudstar.rightcloud.oss.common.constants.Constants;
import cn.com.cloudstar.rightcloud.oss.common.constants.RestConst;
import cn.com.cloudstar.rightcloud.oss.common.constants.SysConfigConstants;
import cn.com.cloudstar.rightcloud.oss.common.constants.SysConfigConstants.*;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.UserType;
import cn.com.cloudstar.rightcloud.oss.common.enums.*;
import cn.com.cloudstar.rightcloud.oss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.oss.common.pojo.CustomBusinessTag;
import cn.com.cloudstar.rightcloud.oss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.oss.common.pojo.RestResult.Status;
import cn.com.cloudstar.rightcloud.oss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.SaasUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.*;
import cn.com.cloudstar.rightcloud.oss.common.util.cache.AuthUserHolder;
import cn.com.cloudstar.rightcloud.oss.common.util.sms.CheckSmsCodeUtil;
import cn.com.cloudstar.rightcloud.oss.config.SMSUtil;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.model.CustomizationInfoTemplate;
import cn.com.cloudstar.rightcloud.oss.module.account.controller.user.KeycloakConstant.RealmEnum;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.org.OrgMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.user.RoleModuleMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.user.UserMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.enums.IamDomainPermissionsEnum;
import cn.com.cloudstar.rightcloud.oss.module.account.service.org.OrgService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.role.UserRoleService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.user.IamPermissionService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.user.LdapUserService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.user.UserSecurityService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.user.UserService;
import cn.com.cloudstar.rightcloud.oss.module.bigscrean.bean.model.BigScreenMessage;
import cn.com.cloudstar.rightcloud.oss.module.bigscrean.service.ISysMPreOccRecordService;
import cn.com.cloudstar.rightcloud.oss.module.feign.service.FeignService;
import cn.com.cloudstar.rightcloud.oss.module.notice.bean.entity.SysMNotice;
import cn.com.cloudstar.rightcloud.oss.module.notice.dao.SysMNoticeMapper;
import cn.com.cloudstar.rightcloud.oss.module.operate.dao.EntityUserMapper;
import cn.com.cloudstar.rightcloud.oss.module.operationcore.service.process.ProcessMgtService;
import cn.com.cloudstar.rightcloud.oss.module.pricing.dao.priceconfig.BizBillingAccountMapper;
import cn.com.cloudstar.rightcloud.oss.module.resource.service.HPCService;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.dao.SfProductResourceMapper;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.service.ServiceCategoryService;
import cn.com.cloudstar.rightcloud.oss.module.system.bean.config.enums.IpConfigEnum;
import cn.com.cloudstar.rightcloud.oss.module.system.bean.config.request.*;
import cn.com.cloudstar.rightcloud.oss.module.system.bean.config.response.DescribeBigScreenNpuConfigParentResponse;
import cn.com.cloudstar.rightcloud.oss.module.system.bean.config.response.DescribeBigScreenNpuConfigResponse;
import cn.com.cloudstar.rightcloud.oss.module.system.bean.config.response.DescribeSysConfigResponse;
import cn.com.cloudstar.rightcloud.oss.module.system.dao.config.SysConfigMapper;
import cn.com.cloudstar.rightcloud.oss.module.system.dao.message.MessageMapper;
import cn.com.cloudstar.rightcloud.oss.module.system.service.config.AiBigscreenNpuConfigService;
import cn.com.cloudstar.rightcloud.oss.module.system.service.config.SysConfigService;
import cn.com.cloudstar.rightcloud.oss.module.system.service.config.SystemConfigService;
import cn.com.cloudstar.rightcloud.oss.module.system.service.message.BusinessNotificationService;
import cn.com.cloudstar.rightcloud.oss.module.system.service.message.ISysOssMessageService;
import cn.com.cloudstar.rightcloud.oss.module.system.service.message.NotificationService;
import cn.com.cloudstar.rightcloud.oss.module.system.service.syslog.IBizCustomerActionLogService;
import cn.com.cloudstar.rightcloud.oss.util.ConfigValidateUtil;
import cn.com.cloudstar.rightcloud.oss.util.PropertiesUtil;
import cn.com.cloudstar.rightcloud.remote.api.iam.service.KeycloakRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.ResRoleRequest;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.user.ResRole;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.cloud.CloudEnvRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.config.ConfigRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.res.ResRoleService;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONException;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.Page;
import com.google.common.util.concurrent.ListeningExecutorService;
import com.google.common.util.concurrent.MoreExecutors;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.sleuth.Span;
import org.springframework.cloud.sleuth.Tracer;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;

/**
 * 系统配置控制器(/configs)
 *
 * <AUTHOR>
 */
@ApiGroup(ApiGroupEnum.SYSTEM_GROUP)
@Api(value = "/configs", tags = "系统配置Ctrl")
@RestController
@RequestMapping("/configs")
@Slf4j
@Validated
public class SystemConfigCtrl {

    private static final String MOBILE_CONSTRAINT = "^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\\d{8}$";

    private static final String PHONE_CONSTRAINT = "(^0\\d{2,3}-[1-9]\\d{6,7}$)|(^400-((\\d{7,8}$)|(\\d{3}-\\d{4,5}$)))";
    private static final String URL_CONSTRAINT = "http";
    private static final String TWICE_VALIDATE = "LOCK_PASSWORD";
    private static final long AUTH_AGAIN_MINUTE = 30L;
    private static final String NEED_TWICE_VALIDATE = "needPasswordValidate";
    public static final String KEYCLOAK_ADMIN_SKIP_2_FA = "keycloak.admin.skip2FA";

    public static final String CACHE_PROPERTY_KEY = "system:config";
    /**
     * 临时解决方案 @时文强 cmp需要是用的config  需要再设置完后get一下  放入redis缓存
     */
    public static final String[] COMMON_CONFIG = {"upcoming_expired_days"};
    /**
     * 平台限额
     */
    private static final String PRODUCT_QUOTA_CONFIG = "product_quota_config";
    private static final String BIG_SCREEN_ROUTING_KEY = "bigscreen.expire.*";
    private static final String EXCHANGE_NAME = "bss.expire";


    @Value("${BSS_INTEGRATE_CMP:false}")
    private Boolean bss_integrate_cmp;



    private static final String SYSTEM_CONTACT_NUMBER = "system.contact.number";
    private static final String SYSTEM_CONTACT_EMAIL = "system.contact.email";


    private static final String MAIL_CIPHER = "mail.password";

    private static final String LDAP_CIPHER = "ldap.password";
    private static final String MAIL_USERNAME = "mail.username";
    private static final String SMS_HUAWEIYUN_APP_KEY = "sms.huaweiyun.app.key";
    private static final String SMS_HUAWEIYUN_APP_SECRET = "sms.huaweiyun.app.secret";
    private static final String SMS_ALIYUN_ACCESS_KEY = "sms.aliyun.access.key.id";
    private static final String SMS_ALIYUN_ACCESS_KEY_SECRET = "sms.aliyun.access.key.id";


    /**
     * 支付网关
     */
    private static final String ALIPAY_PRIVATEKEY = "alipay.privateKey";
    private static final String ALIPAY_PUBLICKEY = "alipay_publicKey";
    private static final String WECHATPAY_MCHKEY = "wechatpay.mchKey";


    /**
     * 国密配置
     */
    private static final String SERVICE_APP_CIPHER = "service.app.pwd";
    private static final String SERVICE_APP_KEY = "service.app.key";

    /**
     * hpc.sfsAk
     */
    private static final String HPC_SFSAK = "hpc.sfsAk";

    /**
     * bigScreen.pwd
     */
    private static final String BIGSCREEN_CIPHER = "bigScreen.pwd";

    /**
     * "productsQuotaWhiteList:"
     */
    private static final String PRODUCTS_QUOTA_WHITE_LIST_KEY = "productsQuotaWhiteList:";

    /**
     * "productsQuota:"
     */
    private static final String PRODUCTS_QUOTA_KEY = "productsQuota:";

    /**
     * "productsQuotaAccount:"
     */
    private static final String PRODUCTS_QUOTA_ACCOUNT_KEY = "productsQuotaAccount:";

    private static final List<String> MA_VERSION_VALUE = Arrays.asList("V1", "V2");


    /**
     * 无需登录就可获取的config
     */
    private static final List<String> UN_LOGIN_CONFIG_KEYS = Arrays.asList("logintips.privacy", "online", "logintips.register", "system.version", "system.contact.number","system.contact.email");

    /**
     * multi支持的更新configKeys
     */
    private static final List<String> UPDATE_MULTI = Arrays.asList("system.name", "company.name", "system.version", "system.contact.number", "company.prepared.number", "login.picture", "system.contact.email", "org.level.count", "platform.small.logo", "platform.large.logo"
            , "rightcloud.portal.url", "rightcloud.console.url", "regist.url", "rightcloud.mgt.url", "letter.template","screen.airesource.resnet50","screen.airesource.bert-large","screen.airesource.apiurl"
            ,"screen.airesource.public.poolids","screen.airesource.npu.speckey","screen.airesource.hide-company","screen.airesource.online-company","cpu_weight","","screen.airesource.coefficient.multiply","screen.airesource.coefficient.divide","screen.airesource.intended-company"
            ,"screen.airesource.pubnum", "screen.power.factor","screen.airesource.drpnum","screen.airesource.url","screen.airesource.power","screen.airesource.num","screen.airesource.precue","screen.airesource.preoccupancy"
            , SysConfigConstants.RES_SCREEN_BMS, SysConfigConstants.RES_SCREEN_ECS, SysConfigConstants.RES_SCREEN_RDS, SysConfigConstants.RES_SCREEN_CCE
            , SysConfigConstants.RES_SCREEN_CPU, SysConfigConstants.RES_SCREEN_DCS, SysConfigConstants.RES_SCREEN_ELB, SysConfigConstants.RES_SCREEN_FIP
            , SysConfigConstants.RES_SCREEN_MEMORY, SysConfigConstants.RES_SCREEN_SFS, SysConfigConstants.RES_SCREEN_STORAGE, SysConfigConstants.RES_SCREEN_URL);

    private static final List<String> LDAP_MULTI = Arrays.asList("ldap.servername", "is_share", "ldap.home.phone", "ldap.enable", "ldap.domainname", "ldap.servername", "ldap.username", "ldap.password", "ldap.port", "ldap.connecttype"
            , "ldap.credentials.path", "ldap.shadow.user");

    /**
     * msg/multi支持的更新configKeys
     */
    private static final List<String> MSG_MULTI = Arrays.asList("email.enable", "mail.smtp.host", "mail.nickname","mail.account", "mail.username", "mail.password", "sms.enable", "sms.platform.selected", "sms.huaweiyun.sign.number", "sms.huaweiyun.sign", "sms.aliyun.sign", "sms.aliyun.url", "sms.huaweiyun.url", "sms.aliyun.access.key.id", "sms.huaweiyun.app.key", "sms.aliyun.access.key.secret", "sms.huaweiyun.app.secret");
    /**
     * business/multi支持的更新configKeys
     */
    private static final List<String> BUSINESS_MULTI = Arrays.asList("upcoming_expired_days", "customer_max_num", "iam.auto.node.min.num",
            "releaseCommonPoolWaitTime", "sensitiveWord", "bill.retention.time", "platform.account_and_ou", "verification.code.per.day",
            "management.side.startup", "expire.drp.discount.switch", "complaint.file", "business.tag.config", "custom.business.tag",
            "multiple.audits.enable", "obs_free_capacity", "minio.external.net.endpoint");

    /**
     * 支付配置
     */
    private static final List<String> PAY_MULTI = Arrays.asList("alipay.switch","alipay.appid","alipay.privateKey","alipay.publicKey","alipay.notifyUrl","alipay.returnUrl","wechatpay.swithch","wechatpay.appid","wechatpay.mchid","wechatpay.mchKey","wechatpay.notifyUrl","wechatpay.returnUrl","unionpay.switch","unionpay.notifyUrl","unionpay.returnUrl","unionpay.mchid");

    /**
     * screen/multi支持的更新configKeys
     */
    private static final List<String> SCREEN_MULTI = Arrays.asList("screen.airesource.preoccupancy", "screen.airesource.precue", "screen.airesource.power","screen.airesource.resnet50", "screen.airesource.url", "screen.airesource.drpnum", "screen.airesource.pubnum", "screen.airesource.num", "screen.airesource.bert-large", "screen.airesource.intended-company", "screen.airesource.apiurl", "screen.airesource.npu.speckey", "screen.airesource.hide-company", "screen.airesource.public.poolids", "screen.airesource.online-company", "screen.airesource.coefficient.divide", "screen.airesource.coefficient.multiply");
    /**
     * upgrade/multi支持的更新configKeys
     */
    private static final List<String> UPGRADE_MULTI = Arrays.asList("platform.upgrade.starttime", "platform.upgrade.endtime", "platform.upgrade.usersid","platform.upgrade.noticeid");
    /**
     * safety/multi支持的更新configKeys
     */
    private static final List<String> SAFETY_MULTI = Arrays.asList("cert.expr.cycle", "cert.expr.warn", "credential.length.min","credential.character.limit",
            "credential.repeat.num", "credential.least.used.day", "credential.expire.time", "credential.expire.time.validity", "credential.rule",
            "credential.rule.out", "loginfailure_config.open", "loginfailure_config.count", "loginfailure_config.time", "loginlimit_config.open",
            "loginlimit_config.count", "loginaccount_config.open", "loginaccount_config.expire.time", "session.expire.time", "keycloak.SmsConfig",
            "keycloak.admin.skip2FA", "syslog.open", "syslog.host", "syslog.port", "syslog.protocol", "minio.external.net.endpoint");
    /**
     * multi/verify支持的更新configKeys
     */
    private static final List<String> MULTI_VERIFY = Arrays.asList("iam.identity.url.client.file", "iam.product.url", "iam.identityprovider.url","iam.auto.entrust.name","iam.auto.entrust.share.id", "iam.auto.entrust.account", "model.arts.edition", "modelarts.public.poolids", "bigScreen.call.interface", "bigScreen.account", "bigScreen.pwd", "mirror.center.namespace","iam.create.user.flag","iam.create.user.limit", "iam.hcso.console.flag");
    /**
     * /quoto/multi支持的更新configKeys
     */
    private static final List<String> QUOTO_MULTI = Arrays.asList("minimum.purchase.amount.1", "minimum.purchase.amount.open.1", "minimum.frozen.amount.1","minimum.frozen.amount.open.1");

    private static final List<String> CCSP_CONFIG = Arrays.asList("service.open", "service.app.name", "service.app.pwd", "service.app.key", "service.file.path");
    private static final String ZERO = "0";
    private static final String ONE = "1";

    private static final String serviceOpenConfigKey = "service.open";
    private static final String serviceAppNameConfigKey = "service.app.name";
    private static final String serviceAppCipherConfigKey = "service.app.pwd";
    private static final String serviceAppKeyConfigKey = "service.app.key";
    private static final String serviceFilePathsConfigKey = "service.file.path";
    private static final String trueStr = "true";
    private static final String falseStr = "false";

    @Autowired
    private SystemConfigService systemConfigService;
    @Autowired
    private AmqpTemplate amqpTemplate;

    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private LdapUserService ldapUserService;
    @Autowired
    private OrgService orgService;

    @Autowired
    private RedisTemplate redisTemplate;

    @DubboReference
    private CloudEnvRemoteService cloudEnvService;
    @Autowired
    private UserService userService;

    @Autowired
    private RoleModuleMapper roleModuleMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private AiBigscreenNpuConfigService aiBigscreenNpuConfigService;

    @Autowired
    private ISysMPreOccRecordService sysMPreOccRecordService;

    @Resource
    private SysConfigMapper sysConfigMapper;

    @Autowired
    private StorageService storageService;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private BizBillingAccountMapper bizBillingAccountMapper;

    @Autowired
    NotificationService notificationService;

    @Autowired
    ServiceCategoryService serviceCategoryService;

    @DubboReference
    private KeycloakRemoteService keycloakRemoteService;
    @Autowired
    private FeignService feignService;


    private final String SYSTEM_UPGRADE = "system:upgrade";

    private static final String FREEZE_ACCOUNT_KEY = "lock:freeze_account:1";

    @Autowired
    private OrgMapper orgMapper;

    @Value("${config.user.types-range}")
    private String userConfigTypes;

    @Value("${config.non-auth.type}")
    private String nonAuthConfigType;

    @Value("${config.non-auth.keys}")
    private String nonAuthConfigKeys;

    @Autowired
    private SfProductResourceMapper sfProductResourceMapper;
    @DubboReference
    private ConfigRemoteService configRemoteService;
    @Autowired
    private HPCService hpcService;

    @Autowired
    private BusinessNotificationService businessNotificationService;
    @Autowired
    private SysConfigService sysConfigService;

    @Autowired
    private IamPermissionService iamPermissionService;

    @DubboReference
    private ResRoleService resRoleService;

    @Autowired
    EntityUserMapper entityUserMapper;

    @Autowired
    MessageMapper messageMapper;

    @Autowired
    private Tracer tracer;

    @Autowired
    private UserSecurityService userSecurityService;

    @Autowired
    private ProcessMgtService processMgtService;

    /**
     * 账号长度配置
     */
    private static final String ACCOUNT_CONFIG="account_config";

    /**
     * 账号最大长度
     */
    private static final String ACCOUNT_CONFIG_MAX_LENGTH="account_config.max.length";

    /**
     * 账号最小长度
     */
    private static final String ACCOUNT_CONFIG_MIN_LENGTH="account_config.min.length";

    private static final List<String> GREEMENTCONFIGSKEYS = Arrays.asList("logintips.privacy", "logintips.register", "logintips.service.open", "logintips.market.sell", "logintips.market.buy");

    @Autowired
    private CCSPDataUpgrade ccspDataUpgrade;

    @Autowired
    private ISysOssMessageService sysOssMessageService;

    @Autowired
    private UserRoleService userRoleService;

    @Resource
    private SysMNoticeMapper noticeMapper;

    private final static ListeningExecutorService executorService = MoreExecutors.listeningDecorator(Executors.newCachedThreadPool());
    @Autowired
    private IBizCustomerActionLogService bizCustomerActionLogService;


    /**
     * 显示所有信息(系统配置-基础配置)
     *
     * @param request 请求
     * @return {@link List}<{@link DescribeSysConfigResponse}>
     */
    @AuthorizeOss(action = AuthModule.COMMON.PUBLIC.C1.C110 + "," + AuthModule.COMMON.PUBLIC.B1.B114 + "," + AuthModule.COMMON.PUBLIC.Z1.Z102)
    @GetMapping
    @ApiOperation(httpMethod = "GET", value = "显示所有信息")
    public List<DescribeSysConfigResponse> displayAllMessage(@Validated DescribeSysConfigRequest request) {
        List<String> supportConfigTypes = Arrays.asList("company_auth", "customer_max_config", "iam_auto_config", "alipay_config", "wechatpay_config", "unionpay_config",
                              "logintips_config", "logintips.register", "screen_config", "account_expire_config","expired_config","bill_retention_time_config"
        ,"platform_account_and_ou","verification_code_config", "ncustom_system_config", "obs_capacity_config", "minio_config");
        if (!supportConfigTypes.contains(request.getConfigType())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        Criteria criteria = new Criteria();
        WebUserUtil.preparePageParams(request, criteria, "config_type ASC");
        criteria.setConditionObject(request);
        List<SysConfig> list = systemConfigService.displaySystemConfigList(criteria);
        if  ("minio_config".equals(request.getConfigType())) {
            list = list.stream().filter(e -> e.getConfigKey().equals("minio.external.net.endpoint")).collect(Collectors.toList());
        }

        return BeanConvertUtil.convert(list, DescribeSysConfigResponse.class);
    }

    /**
     * 显示所有信息(系统配置-消息通知)
     *
     * @param request 请求
     * @return {@link List}<{@link DescribeSysConfigResponse}>
     */
    @GetMapping("/msg")
    @ApiOperation(httpMethod = "GET", value = "显示所有信息")
    @AuthorizeOss(action = ZF.ZF02.ZF02)
    public List<DescribeSysConfigResponse> displayAllMessageByMsg(@Validated DescribeSysConfigRequest request) {
        if (!Arrays.asList("email_server_config", "sms_config").contains(request.getConfigType())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        Criteria criteria = new Criteria();
        WebUserUtil.preparePageParams(request, criteria, "config_type ASC");
        criteria.setConditionObject(request);
        List<SysConfig> list = systemConfigService.SystemConfigListDeal(criteria);
        return BeanConvertUtil.convert(list, DescribeSysConfigResponse.class);
    }
    /**
     * 显示所有信息(系统配置-业务配置)
     *
     * @param request 请求
     * @return {@link List}<{@link DescribeSysConfigResponse}>
     */
    @GetMapping("/bus")
    @ApiOperation(httpMethod = "GET", value = "显示所有信息")
    @AuthorizeOss(action = ZF.ZF12.ZF12)
    public List<DescribeSysConfigResponse> displayAllMessageByBus(@Validated DescribeSysConfigRequest request) {
        if (!Arrays.asList("expired_config", "customer_max_config").contains(request.getConfigType())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        Criteria criteria = new Criteria();
        WebUserUtil.preparePageParams(request, criteria, "config_type ASC");
        criteria.setConditionObject(request);
        List<SysConfig> list = systemConfigService.SystemConfigListDeal(criteria);
        return BeanConvertUtil.convert(list, DescribeSysConfigResponse.class);
    }


    /**
     * [INNER API] 显示所有信息
     *
     * @param request 请求
     * @return {@link List}<{@link DescribeSysConfigResponse}>
     */
    @RejectCall
    @GetMapping("/feign")
    @ApiOperation(httpMethod = "GET", value = "显示所有信息")
    public List<DescribeSysConfigResponse> displayAllMessageByFeign(@Validated DescribeSysConfigRequest request) {
        List<String>  configTypes = new ArrayList<>();
        Criteria criteria = new Criteria();
        String undefined = "!@#$%^&";
        configTypes.add(undefined);
        configTypes.add(request.getConfigType());
        if(StringUtils.isNotBlank(request.getConfigKey())){
            configTypes.add(request.getConfigKey());
        }
        criteria.put("configTypeIn", configTypes);
        String configKey = request.getConfigKey();
        if (StringUtils.isNotBlank(configKey)) {
            criteria.put("configKey", configKey);
        }
        criteria.setOrderByClause("SORT_RANK desc");
        List<SysConfig> sysconfigs = systemConfigService.displaySystemConfigList(criteria);

        return BeanConvertUtil.convert(sysconfigs, DescribeSysConfigResponse.class);
    }
    /** 需要维护的接口
     * /config_data
     * /configs/{configType}
     * /configs/multi
     * @param configType 配置类型
     */
    private void validAuth(String configType) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo(WebUtil.getRequest());

        String key = AuthConstants.USER_PERMISSION_CACHE + authUserInfo.getUserSid();
        String cacheAuth = JedisUtil.INSTANCE.get(key);
        if (Objects.isNull(cacheAuth)) {
            // 获取该用户拥有的权限
            List<RoleModule> roleModules = roleModuleMapper.selectRoleModuleByUserSid(authUserInfo.getUserSid());

            // 把权限存进缓存
            List<String> collect = roleModules.stream().map(RoleModule::getModuleSid).collect(Collectors.toList());
            cacheAuth = JSON.toJSONString(collect.stream().distinct().collect(Collectors.toList()));
            redisTemplate.opsForValue().set(key, cacheAuth);
        }
        boolean flag = true;
        if ("screen_config".equals(configType)
                ||"hpc_screen_config".equals(configType)
                ||"screen_config".equals(configType)
                ||"res_screen_config".equals(configType)
                ||"ai_market_screen_config".equals(configType)) {
            // 如果是销售组织类用户，直接放行,或者具有系统控制台权限的
            if (UserType.DISTRIBUTOR_USER.equals(authUserInfo.getUserType())) {
                flag = false;
            } else if (cacheAuth.contains("Z")) {
                flag = false;
            } else if (!cacheAuth.contains("PB") && !cacheAuth.contains(ZF05.ZF05)) {
                flag = false;
            } else {
                // 大屏
                flag = !cacheAuth.contains("P");
            }
        } else if (includeConfigType(configType)) {
            // 系统配置
            flag = !cacheAuth.contains("Z");
        }else if ("other_config".equals(configType)
                ||"hpc_config".equals(configType)
                ||"sfs_config".equals(configType)){
            // 这是82控制台也在用
            if(Objects.isNull(authUserInfo.getParentSid())){
                flag = !cacheAuthContainsZ(cacheAuth);
            }
        } else if ("product_quota_config_1".equals(configType)){
            // 产品
            flag = !cacheAuth.contains("BD");
        }else if ("contract_config".equals(configType)){
            // 合同管理
            flag = !cacheAuth.contains("BC05");
        } else if ("model_arts_config".equals(configType) || "bigScreen_config".equals(configType)){
            flag = !(cacheAuth.contains("ZI0304") || cacheAuth.contains("ZF05"));
        } else if ("credential_strategy_config".equals(configType)){
            flag = false;
        } else if ("mirror_center_config".equals(configType)){
            flag = !cacheAuth.contains("BH08") && !cacheAuth.contains("CB21") && !cacheAuth.contains("ZI0304");
        } else if ("ncustom_system_config".equals(configType)) {
            Criteria criteria = new Criteria();
            criteria.put("userSid",authUserInfo.getUserSid());
            List<UserRole> userRoles = userRoleService.selectByParams(criteria);
            boolean anyMatch = userRoles.stream().anyMatch(role -> SysRoleEnum.OPERATION_ADMIN.getRoleSid().equals(role.getRoleSid())
                || SysRoleEnum.DISTRIBUTOR_ADMIN.getRoleSid().equals(role.getRoleSid()));

            flag = !(authUserInfo.getAdminFlag() || anyMatch);
        }

        if (flag) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
        }
    }

    /**
     * 判断缓存中是否包含Z,"CB17", "CB20", "BD05", "BC02"
     * @param cacheAuth
     * @return
     */
    private static boolean cacheAuthContainsZ(String cacheAuth) {
        return Stream.of("Z", "CB17", "CB20", "BD05", "BC02").anyMatch(s -> cacheAuth.contains(s));
    }

    private static boolean includeConfigType(String configType) {
        return Stream.of("email_server_config",
            "sms_config",
            "ldap_config",
            "oauth_config",
            "expired_config",
            "iam_auto_config",
            "releaseCommonPoolWaitTime",
            "customer_max_config",
            "system_upgrade",
            "loginfailure_config",
            "loginaccount_config",
            "loginlimit_config",
            "keycloak_admin_config",
            "keycloak_config",
            "session_config",
            "pwdprescription_config",
            "sensitive_word_config",
            "recycle_config",
            "cert_expire_warn_config",
            "syslog_config").anyMatch(config -> config.equals(configType));
    }

    /**
     * 获取用户权限
     * @param authUserInfo 用户信息
     * @return 返回权限
     */
    private String getUserCacheAuth(AuthUser authUserInfo) {
        // 获取该用户拥有的权限
        List<RoleModule> roleModules = roleModuleMapper.selectRoleModuleByUserSid(authUserInfo.getUserSid());
        // 把权限存进缓存
        List<String> collect = roleModules.stream().map(RoleModule::getModuleSid).collect(Collectors.toList());
        return JSON.toJSONString(collect.stream().distinct().collect(Collectors.toList()));
    }

    /**
     * 【Since v2.5.0】获取配置(登陆页面)
     *
     * @param configType 配置类型
     * @return {@link List}<{@link DescribeSysConfigResponse}>
     */
    @ApiOperation(httpMethod = "GET", value = "获取配置", notes = "通过传入configType获取配置")
    @ApiImplicitParam(name = "configType", value = "配置类型", paramType = "path", dataType = "string", required = true)
    public List<DescribeSysConfigResponse> getConfigsByTypeByLogin(@PathVariable("configType") String configType) {
        if(!"system_config".equalsIgnoreCase(configType)
                &&!"recycle_config".equalsIgnoreCase(configType)
                &&!"contract_config".equalsIgnoreCase(configType)){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1134559));
        }
        // 处理登录用户权限可获取配置
        if(!"system_config".equals(configType)){
            validAuth(configType);
        }
        Criteria criteria = new Criteria();
        criteria.put("configType", configType);
        criteria.setOrderByClause("SORT_RANK desc");
        List<SysConfig> sysconfigs = systemConfigService.SystemConfigListDeal(criteria);
        sysconfigs.forEach(sysConfig -> {
            if (SysConfigConstants.DISPLAY_TYPE.equals(sysConfig.getDisplayType())) {
                sysConfig.setConfigValue("***");
            }
        });
        return BeanConvertUtil.convert(sysconfigs, DescribeSysConfigResponse.class);
    }

    /**
     * 获取配置
     *
     * @param configType 配置类型
     * @return {@link List}<{@link DescribeSysConfigResponse}>
     */
    @AuthorizeOss(action = AuthModule.COMMON.PUBLIC.B1.B114 + "," + AuthModule.COMMON.PUBLIC.Z1.Z102)
    @ApiOperation(httpMethod = "GET", value = "获取配置", notes = "通过传入configType获取配置")
    @ApiImplicitParam(name = "configType", value = "配置类型", paramType = "path", dataType = "string", required = true)
    @GetMapping("/configType/{configType}")
    public List<DescribeSysConfigResponse> getConfigsByType(@PathVariable("configType") String configType) {
        String systemConfig =  "system_config";
        if(!systemConfig.equalsIgnoreCase(configType)
                &&!"recycle_config".equalsIgnoreCase(configType)
                &&!"contract_config".equalsIgnoreCase(configType)){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1134559));
        }
        // 处理登录用户权限可获取配置
        if(!systemConfig.equals(configType)){
            validAuth(configType);
        }
        Criteria criteria = new Criteria();
        criteria.put("configType", configType);
        criteria.setOrderByClause("SORT_RANK desc");
        List<SysConfig> sysconfigs = systemConfigService.SystemConfigListDeal(criteria);
        if (systemConfig.equalsIgnoreCase(configType)) {
            List<String> keys = Arrays.asList("rightcloud.portal.url","rightcloud.console.url","rightcloud.mgt.url");
            sysconfigs = sysconfigs.stream().filter(f -> !keys.contains(f.getConfigKey())).collect(Collectors.toList());
        }

        sysconfigs.forEach(sysConfig -> {
            if (SysConfigConstants.DISPLAY_TYPE.equals(sysConfig.getDisplayType())) {
                sysConfig.setConfigValue("***");
            }
        });

        return BeanConvertUtil.convert(sysconfigs, DescribeSysConfigResponse.class);
    }

    /**
     * 批量获取hpc配置(运营控制台)
     *
     * @param request 请求
     * @return {@link List}<{@link DescribeSysConfigResponse}>
     */
    @GetMapping("/multi")
    @ApiOperation(httpMethod = "GET", value = "批量获取hpc配置", notes = "通过传入configType列表获取配置")
    public List<DescribeSysConfigResponse> getConfigsByTypeList(@Validated DescribeSysConfigByTypeListRequest request) {
        Long entityId = getEntityId();
        List<String> configTypes = request.getConfigTypes();
        // 处理登录用户权限可获取配置
        for (String configType : request.getConfigTypes()) {
            if("state_secret_service".equals(configType)){
                continue;
            }
            if(configType.contains("product_quota_config")){
                if(!configType.equalsIgnoreCase("product_quota_config_"+ entityId)){
                    throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
                }
            }
            validAuth(configType);
        }

        // 多运营实体获取平台限额逻辑处理
        if (configTypes.size() == 1 && PRODUCT_QUOTA_CONFIG.equals(configTypes.get(0))) {
            configTypes.add(0, PRODUCT_QUOTA_CONFIG + "_" + RequestContextUtil.getEntityId());
        }
        Criteria criteria = new Criteria();
        String undefined = "!@#$%^&";
        configTypes.add(undefined);
        criteria.put("configTypeIn", configTypes);
        String configKey = request.getConfigKey();
        if (StringUtils.isNotBlank(configKey)) {
            criteria.put("configKey", configKey);
        }
        criteria.setOrderByClause("SORT_RANK desc");
        List<SysConfig> sysconfigs = systemConfigService.SystemConfigListDeal(criteria);
        sysconfigs.forEach(sysConfig -> {
            if (SysConfigConstants.DISPLAY_TYPE.equals(sysConfig.getDisplayType())) {
                sysConfig.setConfigValue("***");
            }
        });
        if (request.getConfigTypes().contains("other_config")) {
            sysconfigs = sysconfigs.stream()
                    .filter(s -> !"platform.logo.root.path".equals(s.getConfigKey()) &&
                            !"security.key".equals(s.getConfigKey()) &&
                            !"platform.licence.key".equals(s.getConfigKey()) )
                    .collect(Collectors.toList());
        }
        return BeanConvertUtil.convert(sysconfigs, DescribeSysConfigResponse.class);
    }

    /**
     * 【Since v2.5.0】获取组织名称 也就是华为那边namespace
     *
     * @return {@link List}<{@link DescribeSysConfigResponse}>
     */
    @ApiOperation(httpMethod = "GET", value = "获取组织配置", notes = "获取组织名称 也就是华为那边namespace")
    public List<DescribeSysConfigResponse> getMirrorNamespace() {
        Criteria criteria = new Criteria();
        criteria.put("configType", "mirror_center_config");
        criteria.put("configKey", "mirror.center.namespace");
        criteria.setOrderByClause("SORT_RANK desc");
        List<SysConfig> sysconfigs = systemConfigService.SystemConfigListDeal(criteria);
        return BeanConvertUtil.convert(sysconfigs, DescribeSysConfigResponse.class);
    }



    /**
     * 批量获取配置(系统控制台)
     *
     * @param request 请求
     * @return {@link List}<{@link DescribeSysConfigResponse}>
     */
    @GetMapping("/sys/multi")
    @ApiOperation(httpMethod = "GET", value = "批量获取配置", notes = "通过传入configType列表获取配置")
    @AuthorizeOss(action = AuthModule.COMMON.PUBLIC.Z1.Z101)
    public List<DescribeSysConfigResponse> getConfigsByTypeListBySys(@Validated DescribeSysConfigByTypeListRequest request) {
        return getConfigsByTypeList(request);
    }

    /**
     * [INNER API] 批量获取配置
     *
     * @param request 请求
     * @return {@link List}<{@link DescribeSysConfigResponse}>
     */
    @RejectCall
    @GetMapping("/multi/feign")
    @ApiOperation(httpMethod = "GET", value = "批量获取配置", notes = "通过传入configType列表获取配置")
    public List<DescribeSysConfigResponse> getConfigsByTypeListByFeign(@Validated DescribeSysConfigByTypeListRequest request) {
        return getConfigsByTypeList(request);
    }

    /**
     * 判断是否是弱密码规则
     *
     * @param request 请求
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = AuthModule.ZK.ZK0101 + "," + AuthModule.CH.CH06)
    @PostMapping("/multi/weak_rule")
    @ApiOperation(httpMethod = "POST", value = "判断是否是弱密码规则", notes = "通过提交SysConfig的json数组判断")
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "'判断是否是弱密码规则'", resource = OperationResourceEnum.VALID_WEEK_PAWSSWORD_POLICY, tagNameUs ="'Determine whether it is a weak password rule'")
    public RestResult judgWeekPwdRule(@Valid @RequestBody List<UpdateSysConfigsRequest> configsRequestList, HttpServletRequest request) {
        String moduleType = request.getHeader("moduleType");
        Boolean result ;
        //更新的配置
        ConfigValidateUtil.validateConfig(configsRequestList);
        List<SysConfig> updateSysConfigs = BeanConvertUtil.convert(configsRequestList, SysConfig.class);
        //如果是用户控制台调用接口
        if(StringUtils.isNotEmpty(moduleType) && IpConfigEnum.CONSOLE_MODULE_TYPE_VALUE.getValue().equals(moduleType)){
            //接口权限判断
            userSecurityService.checkUserAssertion("iam:authorize:ModifyAuthorize");
            result = systemConfigService.judgWeakPwdRuleForConsole(updateSysConfigs);
        }else {
            Criteria criteria = new Criteria();
            criteria.put("configType", "credential_strategy_config");
            //获取默认配置
            List<SysConfig> originSysconfigs = sysConfigService.selectByParams(criteria);
            result = systemConfigService.judgWeakPwdRule(originSysconfigs, updateSysConfigs);
        }
        if(Boolean.TRUE.equals(result)){
            return new RestResult(RestResult.Status.SUCCESS);
        }
        return new RestResult(RestResult.Status.FAILURE);
    }

    /**
     * 批量更新配置(基础配置)
     *
     * @param updateRequestList 更新请求列表
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = ZF.ZF0101)
    @PutMapping("/multi")
    @ApiOperation(httpMethod = "PUT", value = "批量更新配置", notes = "通过提交SysConfig的json数组批量更新配置")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'配置'", resource = OperationResourceEnum.UPDATE_CONFIGS, tagNameUs ="'Configuration'")
    public RestResult updateConfigs(@Valid @RequestBody List<UpdateSysConfigsRequest> updateRequestList) {
        if (CollectionUtils.isEmpty(updateRequestList)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        //if (updateRequestList.stream().anyMatch(e -> !UPDATE_MULTI.contains(e.getConfigKey()))) {
        //    throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
        //}
        RestResult  restResult = doUpdate(updateRequestList);
        // 如果更新成功，运营与云管整合需要同步配置到云管
        if(restResult.getStatus() && bss_integrate_cmp){
            rabbitTemplate.convertAndSend("sync.sys.config","sync.sys.config.event",updateRequestList);
        }
        return restResult;
    }

    /**
     * 修改协议配置
     *
     * @param updateRequestList 修改协议配置
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = ZF.ZF0101)
    @PutMapping("/updateaGreementConfigs")
    @ApiOperation(httpMethod = "PUT", value = "更新协议配置", notes = "通过提交SysConfig的json数组批量更新配置")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'修改隐私协议,最终用户使用协议'", resource = OperationResourceEnum.UPDATE_GREEMENT_CONFIGS, tagNameUs ="'Modify Privacy Agreement'")
    public RestResult updateaGreementConfigs(@Valid @RequestBody List<UpdateSysConfigsRequest> updateRequestList) {
        List<String> keys = updateRequestList.stream().map(UpdateSysConfigsRequest::getConfigKey).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(keys) || keys.size() > GREEMENTCONFIGSKEYS.size() || !GREEMENTCONFIGSKEYS.containsAll(keys)) {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.UPDATE_CONFIG_TYPE_ERROR));
        }
        return doUpdate(updateRequestList);
    }

    /**
     * 批量更新配置(产品限额)
     *
     * @param updateRequestList 更新请求列表
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = BD07.BD0701)
    @PutMapping("/quoto/multi")
    @ApiOperation(httpMethod = "PUT", value = "批量更新配置", notes = "通过提交SysConfig的json数组批量更新配置")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'平台限额'", resource = OperationResourceEnum.UPDATE_CONFIGS, tagNameUs ="'Platform limits'")
    public RestResult updateConfigsQuoto(@Valid @RequestBody List<UpdateProductLimitsConfigRequest> updateRequestList) {
        if (CollectionUtils.isEmpty(updateRequestList)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        if (updateRequestList.stream().anyMatch(e -> !QUOTO_MULTI.contains(e.getConfigKey()))) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
        }
        for (UpdateProductLimitsConfigRequest updateSysConfigsRequest : updateRequestList) {
            SysConfig sysConfig = sysConfigMapper.selectByConfigKey(updateSysConfigsRequest.getConfigKey());
            if (Objects.isNull(sysConfig)) {
                throw new BizException(WebUtil.getMessage(MsgCd.WARNING_QUERY_FAILURE));
            } else {
                if (!sysConfig.getConfigSid().equals(updateSysConfigsRequest.getConfigSid())) {
                    throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
                }
                if (updateSysConfigsRequest.getConfigKey().contains("open")) {
                    if (!"开启状态".equals(updateSysConfigsRequest.getConfigName())) {
                        throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
                    }
                    if (cn.com.cloudstar.rightcloud.common.util.StringUtil.isNotBlank(updateSysConfigsRequest.getUnit())) {
                        throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
                    }
                } else {
                    if (!"最小购买金额".equals(updateSysConfigsRequest.getConfigName())
                            && updateSysConfigsRequest.getConfigKey().contains("purchase")) {
                        throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
                    }
                    if (!"最小冻结金额".equals(updateSysConfigsRequest.getConfigName())
                            && updateSysConfigsRequest.getConfigKey().contains("frozen")) {
                        throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
                    }
                    if (!"元".equals(updateSysConfigsRequest.getUnit())) {
                        throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
                    }
                }
            }
        }
        List<UpdateSysConfigsRequest> updateConfigList = BeanConvertUtil.convert(updateRequestList,UpdateSysConfigsRequest.class);
        return doUpdate(updateConfigList);
    }

    /**
     * 批量更新配置(消息通知)
     *
     * @param updateRequestList 更新请求列表
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = ZF.ZF02.ZF0201)
    @PutMapping("/msg/multi")
    @ApiOperation(httpMethod = "PUT", value = "批量更新配置", notes = "通过提交SysConfig的json数组批量更新配置")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'配置'", resource = OperationResourceEnum.UPDATE_CONFIGS, tagNameUs ="'Configuration'")
    public RestResult updateConfigsMsg(@Valid @RequestBody List<UpdateSysConfigsRequest> updateRequestList) {
        if (CollectionUtils.isEmpty(updateRequestList)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        if (updateRequestList.stream().anyMatch(e -> !MSG_MULTI.contains(e.getConfigKey()))) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
        }
        RestResult  restResult = doUpdate(updateRequestList);
        // 如果更新成功，运营与云管整合需要同步配置到云管
        if(restResult.getStatus() && bss_integrate_cmp){
            rabbitTemplate.convertAndSend("sync.sys.config","sync.sys.config.event",updateRequestList);
        }
        return restResult;
    }

    /**
     * 批量更新配置(业务配置)
     *
     * @param updateRequestList 更新请求列表
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = ZF.ZF12.ZF1201)
    @PutMapping("/business/multi")
    @ApiOperation(httpMethod = "PUT", value = "批量更新业务配置", notes = "通过提交SysConfig的json数组批量更新配置")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "", resource = OperationResourceEnum.UPDATE_CONFIGS4)
    public RestResult updateConfigsBusiness(@Valid @RequestBody List<UpdateSysConfigsRequest> updateRequestList) {
        OperationLogMdcUtil.saveContent(StringUtils.join(updateRequestList.stream()
                                                                          .map(UpdateSysConfigsRequest::getConfigName)
                                                                          .distinct()
                                                                          .collect(Collectors.toList()),
                                                         StrUtil.COMMA));
        if (CollectionUtils.isEmpty(updateRequestList)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        if (updateRequestList.stream().anyMatch(e -> !BUSINESS_MULTI.contains(e.getConfigKey()))) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
        }
        return doUpdate(updateRequestList);
    }

    /**
     * 批量更新配置(业务配置)
     *
     * @param updateRequestList 更新请求列表
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = ZF10.ZF1001)
    @PutMapping("/pay/multi")
    @ApiOperation(httpMethod = "PUT", value = "批量更新支付配置", notes = "通过提交SysConfig的json数组批量更新配置")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'支付网关配置'", resource = OperationResourceEnum.UPDATE_CONFIGS9, tagNameUs ="'Payment Gateway Configuration'")
    public RestResult updateConfigsPay(@Valid @RequestBody List<UpdateSysConfigsRequest> updateRequestList) {
        OperationLogMdcUtil.saveContent(StringUtils.join(updateRequestList.stream()
                                                                          .map(e -> e.getConfigName() + "[" + e.getConfigSid() + "]")
                                                                          .distinct()
                                                                          .collect(Collectors.toList()),
                                                         StrUtil.COMMA));
        if (CollectionUtils.isEmpty(updateRequestList)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        if (updateRequestList.stream().anyMatch(e -> !PAY_MULTI.contains(e.getConfigKey()))) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED,WebUtil.getMessage(MsgCd.ULTRA_VIRES_OPERATE));
        }
        return doUpdate(updateRequestList);
    }

    /**
     * 【Since v2.5.0】批量更新配置(业务配置)，带短信验证
     *
     * @param req 更新请求,包含验证码、更新的配置列表
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = ZF.ZF12.ZF1201)
    @PutMapping("/business/multi/verify")
    @ApiOperation(httpMethod = "PUT", value = "批量更新配置", notes = "通过提交SysConfig的json数组批量更新配置")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'配置'", resource = OperationResourceEnum.UPDATE_CONFIGS8, tagNameUs ="'Configuration'")
    public RestResult updateConfigsBusinessWithVerification(@Valid @RequestBody UpdateSysConfigsWithVerificationRequest req) {
        // 日志追加
        OperationLogMdcUtil.saveContent(req.getConfigs().stream().map(UpdateSysConfigsRequest::getConfigName)
                                           .collect(Collectors.toList()).toString());
        //验证短信验证码
        CheckSmsCodeUtil.checkCode(null, req.getSmsCode());
        if (CollectionUtils.isEmpty(req.getConfigs())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        if (req.getConfigs().stream().anyMatch(e -> !BUSINESS_MULTI.contains(e.getConfigKey()))) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
        }
        return doUpdate(req.getConfigs());
    }

    /**
     * 批量更新配置(大屏配置)
     *
     * @param updateRequestList 更新请求列表
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = ZF.ZF05.ZF0501)
    @PutMapping("/screen/multi")
    @ApiOperation(httpMethod = "PUT", value = "更新大屏配置", notes = "通过提交SysConfig的json数组批量更新配置")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'大屏配置'", resource = OperationResourceEnum.UPDATE_CONFIGS5, tagNameUs ="'Large screen configuration'")
    public RestResult updateConfigsScreen(@Valid @RequestBody List<UpdateSysConfigsRequest> updateRequestList) {
        if (CollectionUtils.isEmpty(updateRequestList)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        if (updateRequestList.stream().anyMatch(e -> !SCREEN_MULTI.contains(e.getConfigKey()))) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
        }
        return doUpdate(updateRequestList);
    }

    /**
     * 批量更新配置(升级配置)
     *
     * @param updateRequestList 更新请求列表
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = ZF.ZF11.ZF1101)
    @PutMapping("/upgrade/multi")
    @ApiOperation(httpMethod = "PUT", value = "批量更新配置", notes = "通过提交SysConfig的json数组批量更新配置")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'配置'", resource = OperationResourceEnum.UPDATE_CONFIGS, tagNameUs ="'Configuration'")
    public RestResult updateConfigsUpgrade(@Valid @RequestBody List<UpdateSysConfigsRequest> updateRequestList) {
        if (CollectionUtils.isEmpty(updateRequestList)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        if (updateRequestList.stream().anyMatch(e -> !UPGRADE_MULTI.contains(e.getConfigKey()))) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
        }
        return doUpdate(updateRequestList);
    }

    /**
     * 批量更新配置(安全基础配置)
     *
     * @param updateRequestList 更新请求列表
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = AuthModule.ZK.ZK0101)
    @PutMapping("/safety/multi")
    @ApiOperation(httpMethod = "PUT", value = "更新安全基础配置", notes = "通过提交SysConfig的json数组批量更新配置")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'安全设置-基础配置'", resource = OperationResourceEnum.UPDATE_CONFIGS7, tagNameUs ="'Security Settings'")
    public RestResult updateConfigsSafety(@Valid @RequestBody List<UpdateSysConfigsRequest> updateRequestList) {
        if (CollectionUtils.isEmpty(updateRequestList)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        if (updateRequestList.stream().anyMatch(e -> !SAFETY_MULTI.contains(e.getConfigKey()))) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
        }
        return doUpdate(updateRequestList);
    }

    /**
     * 批量更新配置(安全国密配置)
     *
     * @param updateRequestList 更新请求列表
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = AuthModule.ZK.ZK03)
    @PutMapping("/CCSP/multi")
    @ApiOperation(httpMethod = "PUT", value = "更新安全国密配置", notes = "通过提交SysConfig的json数组批量更新配置")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'安全设置-国密服务配置'", resource = OperationResourceEnum.UPDATE_CONFIGS10, tagNameUs ="'Security Settings'")
    public RestResult updateConfigsCCSP(@Valid @RequestBody List<UpdateSysConfigsRequest> updateRequestList) {
        if (CollectionUtils.isEmpty(updateRequestList)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        // 日志追加
        OperationLogMdcUtil.saveContent(updateRequestList.stream().map(UpdateSysConfigsRequest::getConfigName).collect(Collectors.toList()).toString());
        if (updateRequestList.stream().anyMatch(e -> !CCSP_CONFIG.contains(e.getConfigKey()))) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
        }
        return doUpdate(updateRequestList);
    }

    /**
     * 【Since v2.5.0】批量更新配置(其他设置)
     *
     * <Br> 前端未使用，弃用
     * <Br> 接口url： /other/multi
     * @param updateRequestList 更新请求列表
     * @return {@link RestResult}
     */
    @Deprecated
    @AuthorizeOss(action = ZF.ZF06.ZF06)
    @PutMapping("/other/multi")
    @ApiOperation(httpMethod = "PUT", value = "批量更新配置", notes = "通过提交SysConfig的json数组批量更新配置")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'配置'", resource = OperationResourceEnum.UPDATE_CONFIGS, tagNameUs ="'Configuration'")
    public RestResult updateConfigsOther(@Valid @RequestBody List<UpdateSysConfigsRequest> updateRequestList) {
        return doUpdate(updateRequestList);
    }

    /**
     * 批量更新配置
     *
     * @param req 要求事情
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = CONFIG.EDIT_CONFIG)
    @PutMapping("/multi/verify")
    @ApiOperation(httpMethod = "PUT", value = "批量更新配置", notes = "通过提交SysConfig的json数组批量更新配置")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'更新平台配置'", resource = OperationResourceEnum.UPDATE_PLATFORM_CONFIGS, tagNameUs ="'Update platform configuration'")
    public RestResult updateConfigsWithVerification(@Valid @RequestBody UpdateSysConfigsWithVerificationRequest req) {
        //验证短信验证码
        OperationLogMdcUtil.saveContent(req.getConfigs().stream().map(UpdateSysConfigsRequest::getConfigName)
                                            .collect(Collectors.toList()).toString());
        CheckSmsCodeUtil.checkCode(null, req.getSmsCode());
        if (CollectionUtils.isEmpty(req.getConfigs())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        if (req.getConfigs().stream().anyMatch(e -> !MULTI_VERIFY.contains(e.getConfigKey()))) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
        }
        return doUpdate(req.getConfigs());
    }


    /**
     * 创建AI大屏NPU配置表
     *
     * @param req 要求事情
     *
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = AuthModule.ZF.ZF05.ZF0503)
    @PostMapping("/bigScreen/npuConfig")
    @ApiOperation(httpMethod = "POST", value = "创建AI大屏NPU配置表")
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "'AI大屏NPU配置'", resource = OperationResourceEnum.CREATE_NPU_CONFIG, tagNameUs ="'Large screen'")
    public RestResult createNpuConfig(@Valid @RequestBody BigScreenNpuConfigCreateRequest req) {
        boolean success = aiBigscreenNpuConfigService.createNpuConfig(req);
        if (success) {
            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_INSERT_SUCCESS));
        } else {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERROR_INSERT_FAILURE));
        }
    }

    /**
     * 编辑AI大屏NPU配置表
     *
     * @param req 要求事情
     *
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = AuthModule.ZF.ZF05.ZF0504)
    @PutMapping("/bigScreen/npuConfig")
    @ApiOperation(httpMethod = "PUT", value = "编辑AI大屏NPU配置表")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'AI大屏NPU配置'", bizId = "#req.id", resource = OperationResourceEnum.EDIT_NPU_CONFIG, tagNameUs ="'Large screen'")
    public RestResult editNpuConfig(@Valid @RequestBody BigScreenNpuConfigEditRequest req) {
        boolean success = aiBigscreenNpuConfigService.editNpuConfig(req);
        if (success) {
            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_UPDATE_SUCCESS));
        } else {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERROR_UPDATE_FAILURE));
        }
    }

    /**
     * 查询AI大屏NPU配置表
     *
     * @param req 要求事情
     *
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = AuthModule.ZF.ZF05.ZF0502)
    @GetMapping("/bigScreen/npuConfig")
    @ApiOperation(httpMethod = "Get", value = "查询AI大屏NPU配置表")
    public Page<DescribeBigScreenNpuConfigResponse> queryNpuConfig(@Valid BigScreenNpuConfigListRequest req) {
        Criteria criteria=new Criteria();
        Page<DescribeBigScreenNpuConfigResponse> describeBigScreenNpuConfigResponseIPage = aiBigscreenNpuConfigService.queryList(req,criteria);
        return describeBigScreenNpuConfigResponseIPage;
    }

    /**
     * 查询AI大屏NPU配置表
     *
     * @param req 要求事情
     *
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = AuthModule.ZF.ZF05.ZF0502)
    @GetMapping("/bigScreen/npuConfig/parent")
    @ApiOperation(httpMethod = "Get", value = "查询AI大屏NPU配置表")
    public List<DescribeBigScreenNpuConfigParentResponse> queryNpuConfigParent(@Valid BigScreenNpuConfigQueryRequest req) {

        List<DescribeBigScreenNpuConfigParentResponse> describeBigScreenNpuConfigParentResponse = aiBigscreenNpuConfigService.query(req);
        return describeBigScreenNpuConfigParentResponse;
    }

    /**
     * 删除AI大屏NPU配置表
     * @param id
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = AuthModule.ZF.ZF05.ZF0505)
    @DeleteMapping("/bigScreen/npuConfig/{id}")
    @ApiOperation(httpMethod = "DELETE", value = "删除AI大屏NPU配置")
    @OperationLog(type = OperationTypeEnum.DELETE, tagName = "'AI大屏NPU配置'", bizId = "#id", resource = OperationResourceEnum.DELETE_NPU_CONFIG, tagNameUs ="'Large screen'")
    public RestResult deleteNpuConfig(@PathVariable("id") String id) {
        if (!id.matches("^[0-9]*$")) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        boolean success = aiBigscreenNpuConfigService.deleteNpuConfig(id);
        if (success) {
            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_DELETE_SUCCESS));
        } else {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERROR_DELETE_FAILURE));
        }
    }

    /**
     * 算力切分任务
     * 提供给upgrade模块处理历史数据
     */
    @GetMapping("/npuConfig")
    @RejectCall
    public void upgradeOfBills() {
        aiBigscreenNpuConfigService.npuConfig();
    }

    private RestResult doUpdate(List<UpdateSysConfigsRequest> updateRequestList) {
        //ConfigValidateUtil.validateConfig(updateRequestList);
        //bug74706
        ConfigValidateUtil.validateConfig(
                updateRequestList.stream().filter(u -> !StrUtil.equals("公司电话", u.getConfigName())).collect(Collectors.toList())
        );
        Long entityId = getEntityId();
        List<String> notEmptyValueKeyList = Arrays.asList(MailConfig.mailCipher, MailConfig.mailUsername,  MailConfig.mailAccount,
                MailConfig.mailHost, SysConfigConstants.SMS_HUAWEIYUN_APP_KEY, SysConfigConstants.SMS_HUAWEIYUN_APP_SECRET);

        updateRequestList.forEach(it -> {
            String configKey = it.getConfigKey();
            if(it.getConfigType().contains("product_quota_config")){
                if(!it.getConfigType().equalsIgnoreCase("product_quota_config_"+ entityId)){
                    throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
                }
            }
            if ("platform.upgrade.usersid".equals(it.getConfigKey())) {
                List<String> userSids = StrUtil.splitTrim(it.getConfigValue(), ",");
                if (!userSids.contains("100")) {
                   BizException.e(WebUtil.getMessage(MsgCd.ERR_MSG_23));
                }
            }
            if("platform.upgrade.starttime".equals(configKey))
            {
                if(it.getConfigValue()==null)
                {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_258237792));
                }
            }

            if("iam.create.user.flag".equals(configKey)) {
                String valueByConfigKey = sysConfigService.getValueByConfigKey(SysConfigConstants.IAM_CREATE_USER_FLAG);
                if (BooleanUtil.toBoolean(valueByConfigKey) && !BooleanUtil.toBoolean(it.getConfigValue())) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1039016444));
                }
            }

            if (Objects.isNull(it.getConfigValue())) {
                throw new BizException(WebUtil.getMessage(MsgCd.VALID_SPEC_REF_VALUE_VALUE_NOT_EMPTY));
            }

            //参数去前后空格
            String configValue = it.getConfigValue().trim();
            it.setConfigValue(configValue);

            //
            if (SysConfigConstants.IAM_CREATE_USER_FLAG.equals(configKey)) {
                if (configValue.equals("1")) {
                    //hash格式放入 redis
                    JedisUtil.INSTANCE.hset(PropertiesUtil.CACHE_PROPERTY_KEY, SysConfigConstants.IAM_CREATE_USER_FLAG, configValue,30*60);

                    String valueByConfigKey = sysConfigService.getValueByConfigKey(SysConfigConstants.IAM_IDENTITY_URL_CLIENT_FILE);
                    if (StringUtils.isEmpty(valueByConfigKey)) {
                        throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_371753704));
                    }
                    // Keycloak Mapper映射
                    keycloakRemoteService.createXuserIdMapper();

                    //异步运营用户映射IAM子用户
                    CompletableFuture.runAsync(() -> userService.reMapIamUser());

                } else if (!configValue.equals("0")) {
                    throw new BizException(StrUtil.format(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2140273362),it.getConfigName()));
                }
            }
            if (SysConfigConstants.IAM_CREATE_USER_LIMIT.equals(configKey)) {
                Pattern compile = Pattern.compile("\\d+");
                if (!compile.matcher(configValue).matches() || Long.valueOf(configValue) - 100000L > 0) {
                    throw new BizException(StrUtil.format(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2140273362),it.getConfigName()));
                }
            }

            if ("hpc.shrinkage.max.count".equals(configKey)) {
                Pattern compile = Pattern.compile("\\d+");
                if (!compile.matcher(configValue).matches() || Long.valueOf(configValue) - 9999999999L > 0) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_586279052));
                }
            }
            if ("syslog.client.private.password".equals(configKey)) {
                it.setConfigValue(CrytoUtilSimple.encrypt(configValue));
            }
            if ("syslog.host".equals(configKey)) {
                Pattern compile = Pattern.compile("(\\d{1,3})\\.(\\d{1,3})\\.(\\d{1,3})\\.(\\d{1,3})");
                if (!compile.matcher(configValue).matches()) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1923980618));
                }
            }

            if ("syslog.protocol".equals(configKey)) {
                if (!configValue.equals(ProtocolConstant.UDP) && !configValue.equals(ProtocolConstant.TLS)) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1294647938));
                }
            }
            if ("company.name".equals(it.getConfigKey()) || "system.name".equals(it.getConfigKey())) {
                if(StrUtil.isBlank(it.getConfigValue())){
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_691466418));
                }else{
                    int length = it.getConfigValue().length();
                    if (length < 2 || length > 64) {
                        throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1611074317));
                    }
                    if (Stream.of("test","admin").anyMatch(p->it.getConfigValue().startsWith(p))) {
                        throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1822775256));
                    }
                }
            }
            if ("system.contact.number".equals(it.getConfigKey())) {
                Pattern mobile = Pattern.compile(MOBILE_CONSTRAINT);
                Pattern phone = Pattern.compile(PHONE_CONSTRAINT);
                boolean mobileMatches = mobile.matcher(it.getConfigValue()).matches();
                boolean phoneMatches = phone.matcher(it.getConfigValue()).matches();
                if (!mobileMatches && !phoneMatches) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1621619612));
                }
            }
            if ("system.contact.email".equals(it.getConfigKey())) {
                if (!Validator.isEmail(it.getConfigValue())) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_981668819));
                }
            }
            if ("session.expire.time".equals(it.getConfigKey())) {
                if (!Validator.isNumber(it.getConfigValue())) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_843335026));
                }
                int value;
                if (!Arrays.asList("分钟", "小时").contains(it.getUnit())) {
                    throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
                }
                if ("小时".equals(it.getUnit())){
                    BigDecimal multiply = new BigDecimal(it.getConfigValue()).multiply(new BigDecimal(60));
                    value = multiply.intValue();
                } else {
                    value = Integer.parseInt(it.getConfigValue());
                }
                if (value < 5 || 30 < value) {
                    throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
                }
            }
            if ("model.arts.edition".equals(it.getConfigKey())) {
                if (!MA_VERSION_VALUE.contains(it.getConfigValue())) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2094485473));
                }
                //hash格式放入 redis
                JedisUtil.INSTANCE.hset(PropertiesUtil.CACHE_PROPERTY_KEY, SysConfigConstants.MODEL_ARTS_EDITION, it.getConfigValue());
            }
            // 50282 针对于产品实例即将到期配置、分销商配置最大值的校验
            if ("upcoming_expired_days".equals(configKey)) {
                int value = Integer.parseInt(it.getConfigValue());
                if (value > 60 || value < 1) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1971356666));
                }
            }
            if ("customer_max_num".equals(configKey)) {
                int value = Integer.parseInt(it.getConfigValue());
                if (value > 21 || value < 1) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1005303035));
                }
            }
            if ("login.picture".equals(configKey)
                    || "platform.small.logo".equals(configKey)
                    || "platform.large.logo".equals(configKey)
                    || "platform.large.logo.en".equals(configKey)
                    || "platform.large.logo.portal".equals(configKey)
                    || "platform.large.logo.portal.en".equals(configKey)) {
                //校验文件路径
                storageService.validateFilePath(it.getConfigValue(), StoragePathEnum.PICTURE, it.getConfigName());
            }
            if (notEmptyValueKeyList.contains(it.getConfigKey())) {
                checkConfigValIsEmpty(it);
            }
            if ("org.level.count".equals(configKey)) {
                int value = Integer.parseInt(it.getConfigValue());
                if (60 < value || value < 1) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_48179003));
                }
            }
            if ("platform.upgrade.noticeid".equals(it.getConfigKey())) {
                if (StringUtils.isNotBlank(it.getConfigValue())) {
                    SysMNotice sysMNotice = noticeMapper.selectOneById(Long.valueOf(it.getConfigValue()));
                    if (Objects.isNull(sysMNotice) || !Objects.equals(sysMNotice.getNoticeTypeId(), 2L)) {
                        throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
                    }
                }
            }
            if ("credential.rule".equals(it.getConfigKey())) {
                List<String> rules = Arrays.asList("lowercase", "uppercase", "number", "special_charactor");
                try {
                    JSONArray jsonArray = JSON.parseArray(it.getConfigValue());
                    if (jsonArray.size() != 0) {
                        for (Object e : jsonArray) {
                            if (rules.contains(e.toString())) {
                                continue;
                            }
                            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_442879469));
                        }
                    }
                } catch (JSONException e) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_442879469));
                }
            }
            if ("credential.rule.out".equals(it.getConfigKey())) {
                try {
                    JSONArray jsonArray = JSON.parseArray(it.getConfigValue());
                } catch (JSONException e) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_442879469));
                }
            }
            if ("modelarts.public.poolids".equals(configKey)){
                if (!Pattern.compile("[|a-z|A-Z|\\-|\\d|,]+").matcher(configValue).matches()){
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1625969267));
                }
            }

            // 自定义用户信息配置校验
            if (SysConfigConstants.CUSTOM_INFO_TEMPLATE.equals(it.getConfigKey())) {
                this.validateCustomConfig(it.getConfigValue(), CustomizationInfoTemplate.class);
            }

            // 自定义业务标识配置校验
            if (SysConfigConstants.CUSTOM_BUSINESS_TAG.equals(it.getConfigKey())) {
                this.validateCustomConfig(it.getConfigValue(), CustomBusinessTag.class);
            }

            // top信息过滤标识配置
            if (SysConfigConstants.TOP_INFO_FILTER_TAG.equals(it.getConfigKey())) {
                this.validateCustomConfig(it.getConfigValue(), String.class);
            }

            else if(SysConfigConstants.MINIO_EXTERNAL_ENDPOINT.equalsIgnoreCase(it.getConfigKey())){
                //若果是修改minio外网地址，则需要重置minio外网client
                boolean flag = storageService.setExternalMinioClient(it.getConfigValue());
                if (!flag) {
                    throw new BizException(WebUtil.getMessage(MsgCd.MINIO_URL_ERROR));
                }
                feignService.setExternalMinioClient(it.getConfigValue());
            }

            // 双重审批开启，验证流程审批审批人
            if (SysConfigConstants.MULTIPLE_AUDITS_ENABLE.equals(it.getConfigKey()) && "1".equals(it.getConfigValue())) {
                if (!processMgtService.checkBuildInProgressCandidate()) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERROR_MULTIPLE_AUDITS_ENABLE));
                }
            }

        });

        //国密开关
        boolean enableUpgradeCCSP = enableUpgradeCCSP(updateRequestList);
        if (enableUpgradeCCSP) {
            return new RestResult(RestResult.Status.SUCCESS,WebUtil.getMessage(MsgCd.CCSP_UPGRADING));
        }

        // 配置表数据存储未加密暂时性处理方案,需要对数据加密时放入将对应configKey放入list中
        List<String> cryList = Arrays.asList("mail.password", "mail.smtp.host", "system.contact.number"
                , "system.contact.email", "mail.account", "platform.licence.key", "mail.username", "sms.huaweiyun.app.key", "sms.huaweiyun.app.secret"
                , "sms.aliyun.access.key.secret", "security.key", "platform.licence.key","alipay.privateKey","alipay.publicKey","wechatpay.mchKey");
        List<SysConfig> sysConfigs = BeanConvertUtil.convert(updateRequestList, SysConfig.class);
        if (sysConfigs.stream().anyMatch(sysConfig -> !BIGSCREEN_CIPHER.equals(sysConfig.getConfigKey())
                && sysConfig.getConfigValue().contains("*"))){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1601878692));
        }

        for (SysConfig sysConfig : sysConfigs) {
            String regex = null;
            switch (sysConfig.getConfigKey()) {
                // 账单文件保留时间
                case SysConfigConstants.BILL_RETENTION_TIME:
                    regex = "^[3-9]|1[0-2]\\d*$";
                    break;
                // 安全证书到期告警
                case SysConfigConstants.CERT_EXPR_WARN:
                    regex = "([1-9]|[1-2]\\d|3[0-1])";
                    break;
                // 安全证书到期告警检查周期
                case SysConfigConstants.CERT_EXPR_CYCLE:
                    regex = "^([1-9][0-9]?|[12][0-9][0-9]|3[0-5][0-9]|36[0-5])$";
                    break;
                // 嵌入HCSO控制台页面
                case SysConfigConstants.IAM_HCSO_CONSOLE_FLAG:
                    regex = "[0-1]";
                    break;
            }
            if (StringUtil.isNotBlank(regex)) {
                Matcher m = Pattern.compile(regex).matcher(sysConfig.getConfigValue());
                if (!m.matches()) {
                    return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
                }
            }
            // MA共享资源池版本切换
            RestResult restResult = this.validateMAConfigs(sysConfig);
            if (Objects.nonNull(restResult) && !restResult.getStatus()) {
                return restResult;
            }
            // AI共享资源池版本切换：版本选择 V1需移除租户拥有Modelarts自定义禁用权限
            if (SysConfigConstants.MODEL_ARTS_EDITION.equals(sysConfig.getConfigKey()) && Constants.V1.equals(
                    sysConfig.getConfigValue())) {
                operationModelartsCommonSharePool();
            }
        }

        sysConfigs.stream().forEach(sysConfig1 ->
                sysConfig1.setConfigValue(cryList.contains(sysConfig1.getConfigKey())
                        ? CrytoUtilSimple.encrypt(sysConfig1.getConfigValue())
                        : sysConfig1.getConfigValue()));
        if (!validationLdap(sysConfigs)) {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.LDAP_CONNECT_FAILURE));
        }
        for (SysConfig sysConfig : sysConfigs) {
            // MA共享资源池版本切换
            RestResult restResult = this.validateMAConfigs(sysConfig);
            if (Objects.nonNull(restResult) && !restResult.getStatus()) {
                return restResult;
            }
            if (SysConfigConstants.BIGSCREEN_CIPHER.equals(sysConfig.getConfigKey()) || SysConfigConstants.BIGSCREEN_ACCOUNT.equals(sysConfig.getConfigKey())) {
                sysConfig.setConfigValue(CrytoUtilSimple.encrypt(sysConfig.getConfigValue()));
                JedisUtil.INSTANCE.del(CACHE_PROPERTY_KEY + sysConfig.getConfigKey());
            }
            if (SysConfigConstants.BIGSCREEN_INTERFACE.equals(sysConfig.getConfigKey())) {
                sysConfig.setConfigValue(CrytoUtilSimple.encrypt(sysConfig.getConfigValue()));
                JedisUtil.INSTANCE.del(CACHE_PROPERTY_KEY + sysConfig.getConfigKey());
            }
            // AI共享资源池版本切换
            if (SysConfigConstants.MODEL_ARTS_EDITION.equals(sysConfig.getConfigKey())) {
                configRemoteService.updateConfig(sysConfig.getConfigKey(), sysConfig.getConfigValue());
            }
            // AI共享资源池版本切换：版本选择 V1需移除租户拥有Modelarts自定义禁用权限
            if (SysConfigConstants.MODEL_ARTS_EDITION.equals(sysConfig.getConfigKey()) && Constants.V1.equals(
                    sysConfig.getConfigValue())) {
                operationModelartsCommonSharePool();
            }
            //大屏参数校验
            if (SysConfigConstants.SCREEN_AI_HIDE_COMPANY.equals(sysConfig.getConfigKey())) {
                if(sysConfig.getConfigValue().length()>150){
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1090723566));
                }
            }


        }

        String checkHpcJob = null;
        //登录限制
        Map<String, SysConfig> sysMap = sysConfigs.stream()
                                                  .collect(Collectors.toMap(SysConfig::getConfigKey,
                                                                            SysConfig -> SysConfig,(oldValue,newValue)->newValue));
        if ( sysConfigs.stream().anyMatch(a -> a.getConfigType() != null && a.getConfigType().equals("sms_config"))) {
            SysConfig sysConfig = sysMap.get("sms.enable");
            //启用短信则更新通道
            if (sysConfig != null) {
                if ("1".equals(sysConfig.getConfigValue())) {
                    //将 keycloak 的短信通道更新
                    userService.refresh2FASmsConfig();
                } else {
                    //关闭双因子
                    for (RealmEnum value : RealmEnum.values()) {
                        userService.updateKeyCloak2FAConfig(value.getRealm(), null, true);
                    }
                    orgMapper.disable2FA();
                    systemConfigService.updateValueByKey(KEYCLOAK_ADMIN_SKIP_2_FA, "true",
                                                         AuthUtil.getAuthUser().getAccount());
                }
            }

            // 短信提供商校验
            SysConfig platform = sysMap.get("sms.platform.selected");
            if (platform != null) {
                if (!"aliyun".equals(platform.getConfigValue()) && !"huaweiyun".equals(platform.getConfigValue())) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_520359848));
                }
            }
        }



        //判断是否为最小冻结金额
        List<String> frozenKeys = sysMap.keySet().stream()
                                        .filter(sys -> sys.contains(ProductQuotaConfig.MINIMUM_PURCHASE_AMOUNT)
                                                || sys.contains(ProductQuotaConfig.MINIMUM_FROZEN_AMOUNT))
                                        .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(frozenKeys)) {
            // 最小购买金额范围[0,100000]，最小冻结金额范围[-100000,10000]
            SysConfig minAmountSysConfig = sysMap.get(
                    ProductQuotaConfig.MINIMUM_PURCHASE_AMOUNT + RequestContextUtil.getEntityId());
            SysConfig minFrozenAmountSysConfig = sysMap.get(
                    ProductQuotaConfig.MINIMUM_FROZEN_AMOUNT + RequestContextUtil.getEntityId());
            if (ObjectUtil.isNotNull(minAmountSysConfig) || ObjectUtil.isNotNull(minFrozenAmountSysConfig)) {
                String minAmount = ObjectUtil.isNotNull(minAmountSysConfig) ? minAmountSysConfig.getConfigValue()
                        : PropertiesUtil.getProperty(
                                ProductQuotaConfig.MINIMUM_PURCHASE_AMOUNT + RequestContextUtil.getEntityId());
                String minFrozenAmount =
                        ObjectUtil.isNotNull(minFrozenAmountSysConfig) ? minFrozenAmountSysConfig.getConfigValue()
                                : PropertiesUtil.getProperty(
                                        ProductQuotaConfig.MINIMUM_FROZEN_AMOUNT + RequestContextUtil.getEntityId());
                if (!(new BigDecimal(minAmount).compareTo(new BigDecimal(minFrozenAmount)) > 0)) {
                    throw new BizException(WebUtil.getMessage(MsgCd.MINIMUM_PURCHASE_FROZEN));
                }
                // 最小购买金额范围[0,100000]
                if (new BigDecimal(minAmount).compareTo(BigDecimal.ZERO) < 0
                        || new BigDecimal(minAmount).compareTo(new BigDecimal("100000")) > 0) {
                    throw new BizException(WebUtil.getMessage(MsgCd.MIN_AMOUNT_RANGE));
                }
                // 最小冻结金额范围[-100000,10000]
                if (new BigDecimal(minFrozenAmount).compareTo(new BigDecimal("-100000")) < 0
                        || new BigDecimal(minFrozenAmount).compareTo(new BigDecimal("10000")) > 0) {
                    throw new BizException(WebUtil.getMessage(MsgCd.MIN_FROZEN_AMOUNT_RANGE));
                }
            }
            log.info("冻结账户异步处理");
            Boolean lock = redisTemplate.opsForValue().setIfAbsent(FREEZE_ACCOUNT_KEY, 1, 60, TimeUnit.MINUTES);
            if (lock){
                Date date = new Date();
                long time = date.getTime();
                String breakKey = FREEZE_ACCOUNT_KEY + "_" + time;
                redisTemplate.opsForValue().set(breakKey,0,59, TimeUnit.MINUTES);
                HttpServletRequest request = cn.com.cloudstar.rightcloud.oss.common.util.SpringContextHolder.getHttpServletRequest();
                //异步调用冻结操作
                try{
                    AuthUser authUser = RequestContextUtil.getAuthUserInfo(WebUtil.getRequest());
                    new Thread(() ->{
                        AuthUserHolder.setAuthUser(authUser);
                        freezeAccount(sysMap, authUser, request);
                    }).start();
                }catch (Exception e){
                    log.error("冻结异常:{}",e);
                }finally {
                    redisTemplate.delete(FREEZE_ACCOUNT_KEY);
                }
            }else {
                throw new BizException(WebUtil.getMessage(MsgCd.ERROR_ENTITY_00017));
            }

        }


        if (ObjectUtil.isNotNull(sysMap.get(LoginLock.open))
                || ObjectUtil.isNotNull(sysMap.get(LoginLock.errNum))
                || ObjectUtil.isNotNull(sysMap.get(LoginLock.lockTime))) {
            boolean openFlg = true;
            SysConfig openConfig = sysMap.get(LoginLock.open);
            if (ObjectUtil.isNull(openConfig)) {

                Criteria criteria = new Criteria();
                criteria.put("configType", ConfigLogCodeEnum.LOGIN_FAILURE_CONFIG.getProductType());
                criteria.put("configKey", LoginLock.open);
                List<SysConfig> openConfigLst = systemConfigService.displaySystemConfigList(criteria);
                if (CollectionUtil.isEmpty(openConfigLst) || !BooleanUtil.toBoolean(openConfigLst.get(0).getConfigValue())) {
                    openFlg = false;
                } else {
                    openConfig = new SysConfig();
                    openConfig.setConfigValue("1");
                }
            }
            if (openFlg) {
                log.debug("基础配置-临时登录失败锁定更新:[{}], [{}], [{}]", JSONUtil.toJsonStr(openConfig)
                        , JSONUtil.toJsonStr(sysMap.get(LoginLock.errNum))
                        , JSONUtil.toJsonStr(sysMap.get(LoginLock.lockTime)));
            }
        }
        if (ObjectUtil.isNotNull(sysMap.get(SfsConfig.SFS_MAX_STORAGE)) &&
                StringUtils.isNotBlank(sysMap.get(SfsConfig.SFS_MAX_STORAGE).getConfigValue())) {
            //校验
            BigDecimal all = new BigDecimal(hpcService.usedSfsStorage());
            if (all.compareTo(new BigDecimal(sysMap.get(SfsConfig.SFS_MAX_STORAGE).getConfigValue())) > 0) {
                throw new cn.com.cloudstar.rightcloud.common.exception.BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2104102643));
            }
        }
        //开关 2FA 调用 keycloak
        String oldKEYCLOAK_ADMIN_SKIP_2_FA = PropertiesUtil.getProperty(KEYCLOAK_ADMIN_SKIP_2_FA);
        SysConfig adminSkip2FA = sysMap.get(KEYCLOAK_ADMIN_SKIP_2_FA);
        //想要开启 2FA 但是短信关闭 报错
        if (adminSkip2FA != null && "false".equals(adminSkip2FA.getConfigValue()) && "0".equals(
                PropertiesUtil.getProperty("sms.enable"))) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_823320488));
        }
        if (adminSkip2FA != null && "false".equals(adminSkip2FA.getConfigValue()) && "false".equals(
                PropertiesUtil.getProperty("keycloak.SmsConfig"))) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1025000482));
        }
        if (ObjectUtil.isNotNull(adminSkip2FA) && StringUtils.isNotBlank(
                adminSkip2FA.getConfigValue()) && !oldKEYCLOAK_ADMIN_SKIP_2_FA.equals(adminSkip2FA.getConfigValue())) {

            userService.disabled2FA(Boolean.parseBoolean(adminSkip2FA.getConfigValue()));
        }

        SysConfig sessionTimeout = sysMap.get(SysConfigConstants.SESSION_EXPIRE_TIME);
        if (ObjectUtil.isNotNull(sessionTimeout) && StringUtils.isNotBlank(sessionTimeout.getConfigValue())) {
            if (sessionTimeout.getUnit() == null) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_390557213));
            }
            if ("小时".equals(sessionTimeout.getUnit())) {
                BigDecimal multiply = new BigDecimal(sessionTimeout.getConfigValue()).multiply(new BigDecimal(60));
                JedisUtil.INSTANCE.set("session_idle_time", multiply.longValue() + "");
            } else {
                //默认为分钟
                JedisUtil.INSTANCE.set("session_idle_time", sessionTimeout.getConfigValue());
            }
        }
        //证书告警检查周期
        SysConfig cycleDay = sysMap.get("cert.expr.cycle");
        if (ObjectUtil.isNotNull(cycleDay)){

            JedisUtil.INSTANCE.hset(PropertiesUtil.CACHE_PROPERTY_KEY, "cert.expr.cycle", cycleDay.getConfigValue(),30*60);
            rabbitTemplate.convertAndSend("cert.expr.cycle", "cert.expr.cycle.event", "cert_expr_msg");
        }

        String loginTips = null;
        for (SysConfig sysConfig : sysConfigs) {
            if ("credential_strategy_config".equals(sysConfig.getConfigType())) {
                if ("credential.repeat.num".equals(sysConfig.getConfigKey()) || "credential.least.used.day".equals(sysConfig.getConfigKey())) {
                    Integer num = null;
                    try {
                        num = Integer.valueOf(sysConfig.getConfigValue());
                    } catch (Exception ignored) {};
                    if (num == null || num < 0) {
                        throw new BizException(sysConfig.getConfigName() + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1479184201));
                    }
                }
            }
            if ("credential.expire.time".equals(sysConfig.getConfigKey())){
                SysConfig sysConfigBean = sysConfigMapper.selectByConfigKey("credential.expire.time");
                Integer compareExpireDay = Integer.parseInt(sysConfig.getConfigValue()) - Integer.parseInt(sysConfigBean.getConfigValue());
                //批量更新用户密码有效期，异步
                //注：之前sql统一修改密码有效期写法，会导致锁表，故改为异步批量更新用户
                new Thread(()-> updateUserPwdEndTime(compareExpireDay)).start();
            }
            if (HpcConfig.checkJob.equals(sysConfig.getConfigKey())) {
                checkHpcJob = sysConfig.getConfigValue();
                // 排除登录提示页面设置关闭的状态
            } else if (LoginTipsConfig.open.equals(sysConfig.getConfigKey())) {
                loginTips = sysConfig.getConfigValue();
            }
        }
        for (SysConfig sysConfig : sysConfigs) {
            if (StringUtils.isBlank(sysConfig.getConfigValue()) || ("ㅤ").equals(sysConfig.getConfigValue())) {
                //如果是关闭作业检查 下边这几个选项可以为空
                if ("0".equals(checkHpcJob)) {
                    if (HpcConfig.ccadminName.equals(sysConfig.getConfigKey()) || HpcConfig.ccadminPass.equals(
                            sysConfig.getConfigKey()) || HpcConfig.ccApi.equals(sysConfig.getConfigKey())) {
                        continue;
                    }
                    // 登录提示页面设置关闭的状态,下边这几个选项可以为空
                } else if ("0".equals(loginTips)) {
                    if (LoginTipsConfig.title.equals(sysConfig.getConfigKey()) || LoginTipsConfig.contente.equals(
                            sysConfig.getConfigKey())) {
                        continue;
                    }
                }else if(SysConfigConstants.SystemUpgradeConfig.starttime.equals(sysConfig.getConfigKey())
                        || SysConfigConstants.SystemUpgradeConfig.endtime.equals(sysConfig.getConfigKey())
                        || SysConfigConstants.SystemUpgradeConfig.userSid.equals(sysConfig.getConfigKey())
                        || SysConfigConstants.SystemUpgradeConfig.noticeId.equals(sysConfig.getConfigKey())){
                    sysConfig.setConfigValue("");
                    continue;
                }
                throw new BizException(sysConfig.getConfigName() + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_627639312));
            }
        }
        //支付配置采用AES对称加密
        AtomicBoolean payConfig = new AtomicBoolean(false);
        sysConfigs.stream().forEach(e -> {
            if (SysConfigConstants.WechatpayConfig.MCH_KEY.equals(e.getConfigKey())
                    || SysConfigConstants.AlipayConfig.PRIVATE_KEY.equals(e.getConfigKey())
                    || SysConfigConstants.AlipayConfig.PUBLIC_KEY.equals(e.getConfigKey())) {
                payConfig.set(true);
            }
        });
        //判断是否是支付相关配置，如果是则测试相关配置是否能正确拉起支付
        if (payConfig.get() && !systemConfigService.payConfigTest(sysConfigs)) {
            return new RestResult(RestResult.Status.FAILURE,WebUtil.getMessage(MsgCd.PAY_CONFIG_ERROR));
        }
        //判断是否是支付相关配置，如果是则测试相关配置是否能正确拉起支付
        Optional<SysConfig> optional = sysConfigs.stream()
                                                 .filter(config -> SysConfigConstants.DCIM_API_PREFIX.equals(
                                                         config.getConfigKey()))
                                                 .findFirst();
        Optional<SysConfig> baseMonitorApi = sysConfigs.stream()
                                                       .filter(config -> SysConfigConstants.BASIC_MONITOR_API_PREFIX.equals(
                                                               config.getConfigKey()))
                                                       .findFirst();
        //大屏预占用配置
        //如果修改得大屏配置是“预占用算力数”或“预占用有效算力（CUE）”，则更新到sys_m_pre_occ_record表
        List<String> occKeys = Arrays.asList(SysConfigConstants.SCREEN_AIRESOURCE_PRECUE,
                                             SysConfigConstants.SCREEN_AIRESOURCE_PREOCCUPANCY);
        if (sysConfigs.stream().anyMatch(v -> occKeys.contains(v.getConfigKey()))) {
            List<SysConfig> occConfig = sysConfigMapper.selectByParams(new Criteria().put("configKeys", occKeys));
            SysConfig preOccConfigOpt = sysConfigs.stream()
                                                  .filter(v -> v.getConfigKey().equals(SysConfigConstants.SCREEN_AIRESOURCE_PREOCCUPANCY))
                                                  .findFirst()
                                                  .orElse(occConfig.stream()
                                                                   .filter(v -> v.getConfigKey().equals(SysConfigConstants.SCREEN_AIRESOURCE_PREOCCUPANCY))
                                                                   .findAny()
                                                                   .orElse(null));
            SysConfig preCueConfigOpt = sysConfigs.stream()
                                            .filter(v -> v.getConfigKey().equals(SysConfigConstants.SCREEN_AIRESOURCE_PRECUE))
                                            .findFirst()
                                            .orElse(occConfig.stream()
                                                             .filter(v -> v.getConfigKey().equals(SysConfigConstants.SCREEN_AIRESOURCE_PRECUE))
                                                             .findAny()
                                                             .orElse(null));
            String preOccValue = ObjectUtils.isEmpty(preOccConfigOpt) || ObjectUtils.isEmpty(preOccConfigOpt.getConfigValue()) ? "0" : preOccConfigOpt.getConfigValue();
            String preCueValue = ObjectUtils.isEmpty(preCueConfigOpt) || ObjectUtils.isEmpty(preCueConfigOpt.getConfigValue()) ? "0" : preCueConfigOpt.getConfigValue();
            sysMPreOccRecordService.savePreOccChange(preOccValue, preCueValue);
        }

        String oldAlarmCollectFrequency = PropertiesUtil.getProperty(
                SysConfigConstants.BASIC_MONITOR_ALARM_COLLECT_FREQUENCY);
        String oldDataCollectFrequency = PropertiesUtil.getProperty(
                SysConfigConstants.BASIC_MONITOR_DATA_COLLECT_FREQUENCY);
        String oldDays = PropertiesUtil.getProperty(SaasUtil.EXPERIENCE_DAYS);
        Long experienceOrgSid = Convert.toLong(PropertiesUtil.getProperty(SaasUtil.EXPERIENCE_ORG));
        sysConfigs.forEach(e -> {
            //目前仅发现该配置存在多种单位，且在页面上变更，如有遗漏，请添加相应校验后在此处放行
            if (!"session.expire.time".equals(e.getConfigKey())) {
                e.setUnit(null);
            }
        });
        boolean result = systemConfigService.updateConfigs(sysConfigs);


        //即将到期配置设置到jedis中
        if(ObjectUtil.isNotNull(sysMap.get("upcoming_expired_days"))){
            SysConfig expiredConfig = sysMap.get("upcoming_expired_days");
            String expiredConfigKey = expiredConfig.getConfigKey();
            String expiredConfigValue = expiredConfig.getConfigValue();
            JedisUtil.INSTANCE.hset(PropertiesUtil.CACHE_PROPERTY_KEY_NOEXPIRED,
                    expiredConfigKey,
                    expiredConfigValue);
        }

        //升级配置放到redis中，gateWay从
        if(ObjectUtil.isNotNull(sysMap.get(SysConfigConstants.SystemUpgradeConfig.starttime))
                && ObjectUtil.isNotNull(sysMap.get(SysConfigConstants.SystemUpgradeConfig.endtime))){
            sysMap.get(SysConfigConstants.SystemUpgradeConfig.starttime).getConfigValue();
            sysMap.entrySet().stream().forEach(entry->{
                JedisUtil.INSTANCE.hset(SYSTEM_UPGRADE,
                        entry.getKey(),
                        entry.getValue().getConfigValue());
            });

        }else{
            Criteria criteria = new Criteria();
            criteria.put("configType", "system_upgrade");
            criteria.setOrderByClause("SORT_RANK desc");
            List<SysConfig> sysConfigsList = systemConfigService.displaySystemConfigList(criteria);
            if(!CollectionUtil.isEmpty(sysConfigsList)){
                sysConfigsList.stream().forEach(sysConfig -> {
                    JedisUtil.INSTANCE.hset(SYSTEM_UPGRADE,
                            sysConfig.getConfigKey(),
                            sysConfig.getConfigValue());
                });
            }
        }

        if (result) {
            for (String s : COMMON_CONFIG) {
                PropertiesUtil.getProperty(s);
            }
            removeUserIfNeed(sysConfigs, oldDays, experienceOrgSid);
            SMSUtil.updateSMSConfig();
            // 如果开启了高级监控
            boolean syncCompanyFlag = false;
            boolean syncAllFlag = false;
            boolean cleanMonitorStatusFlag = false;
            boolean updateMonitorServerInventory = false;
            boolean updateBasicMonitor = false;
            for (SysConfig sysConfig : sysConfigs) {
                if (SysConfigConstants.SYSTEM_NAME.equals(sysConfig.getConfigKey())
                        || SysConfigConstants.PLATFORM_LARGE_LOGO.equals(sysConfig.getConfigKey())
                        || SysConfigConstants.PLATFORM_SMALL_LOGO.equals(sysConfig.getConfigKey())) {
                    // 如果有上述系统配置项，则需要重新同步公司信息到监控系统
                    syncCompanyFlag = true;
                } else if (SysConfigConstants.MONITOR_CONFIG.equals(sysConfig.getConfigKey())) {
                    syncAllFlag = true;
                    cleanMonitorStatusFlag = true;
                } else if (SysConfigConstants.DCIM_API_PREFIX.equals(sysConfig.getConfigKey())
                        || SysConfigConstants.DCIM_MONITOR_INTEGRATION_SERVER_USERNAME.equals(sysConfig.getConfigKey())
                        || SysConfigConstants.DCIM_MONITOR_INTEGRATION_SERVER_CIPHER.equals(sysConfig.getConfigKey())
                        || SysConfigConstants.DCIM_MONITOR_INTEGRATION_SERVER_SSHPORT.equals(
                        sysConfig.getConfigKey())) {
                    updateMonitorServerInventory = true;
                } else if (SysConfigConstants.BASIC_MONITOR_API_PREFIX.equalsIgnoreCase(sysConfig.getConfigKey())
                        || SysConfigConstants.BASIC_MONITOR_ALARM_COLLECT_FREQUENCY.equals(sysConfig.getConfigKey())
                        || SysConfigConstants.BASIC_MONITOR_DATA_COLLECT_FREQUENCY.equals(sysConfig.getConfigKey())) {
                    updateBasicMonitor = true;
                }else if(SysConfigConstants.LOGINT_IPS_PRIVACY.equalsIgnoreCase(sysConfig.getConfigKey())){
                    //如果隐私协议有更改的时候，对所有用户隐私声明同意记录做初始化为不同意
                    List<User> users = userService.findAllUser();
                    if(CollectionUtil.isNotEmpty(users)){
                        List<Long> userIds = users.stream().map(User::getUserSid).collect(Collectors.toList());
                        userMapper.updateByUserIds(userIds);
                    }
                }

            }

            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS));
        }
        return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERROR_OPERATE_FAILURE));
    }

    private <T> void validateCustomConfig(String configValue, Class<T> clazz) {
        List<T> configList;
        if (clazz.equals(String.class)) {
            configList = (List<T>) Arrays.asList(configValue.split(";"));
        }else {
            try {
                configList = JSON.parseArray(configValue, clazz);
            }catch (Exception e) {
                log.error(e.getMessage());
                throw new BizException("模板格式错误，请正确提交JSON格式模板。");
            }
        }

        systemConfigService.validateCustomConfigObj(configList);
    }

    /**
     * 检查参数是否为空
     * @param config
     */
    private static void checkConfigValIsEmpty(UpdateSysConfigsRequest config) {
        if (StringUtils.isBlank(config.getConfigValue())) {
            throw new BizException(config.getConfigName()+WebUtil.getMessage(MsgCd.ERR_MSG_BSS_627639312));
        }
    }

    /**
     * 批量更新用户的密码过期时间
     */
    private void updateUserPwdEndTime(Integer compareExpireDay) {
        //只修改当前能控制的用户
        List<User> users = userService.findAllUsers(new Criteria("isPwdEndTime", true)
                                                            .put("isParentSid", false));
        Calendar instance = Calendar.getInstance();
        users = users.stream().map(user -> {
            instance.setTime(user.getPwdEndTime());
            instance.add(Calendar.DATE, compareExpireDay);
            return User.builder()
                       .userSid(user.getUserSid())
                       .pwdEndTime(instance.getTime())
                       .build();
        }).collect(Collectors.toList());
        //批量更新
        userMapper.updateBatchUser(users);
    }

    /**
     * 国密服务升级
     * @param updateRequestList
     */
    private boolean enableUpgradeCCSP(List<UpdateSysConfigsRequest> updateRequestList) {
        Object ccspUpgradeStatus = redisTemplate.opsForValue().get(CCSPConstants.CCSP_UPGRADE_STATUS);
        if (!Objects.isNull(ccspUpgradeStatus)) {
            if (CCSPUpgradeStatus.UPGRADING.name().equals(ccspUpgradeStatus.toString())) {
                throw new BizException(CCSPUpgradeStatus.UPGRADING.getMessage());
            }
        }

        CCSP ccsp = new CCSP();
        boolean ccspUpgrade =false;
        boolean ccspUpdate = false;
        for (UpdateSysConfigsRequest ccspConfig : updateRequestList) {
            switch (ccspConfig.getConfigKey()) {
                case serviceOpenConfigKey:
                    ccspUpgrade = true;
                    ccspUpdate = true;
                    ccsp.setServiceOpen(true);
                    break;
                case serviceAppNameConfigKey:
                    ccspUpdate = true;
                    ccsp.setServiceAppName(ccspConfig.getConfigValue());
                    break;
                case serviceAppCipherConfigKey:
                    ccspUpdate = true;
                    ccsp.setServiceAppPwd(ccspConfig.getConfigValue());
                    break;
                case serviceAppKeyConfigKey:
                    ccspUpdate = true;
                    ccsp.setServiceAppKey(ccspConfig.getConfigValue());
                    break;
                case serviceFilePathsConfigKey:
                    ccspUpdate = true;
                    String decryptServiceFilePath = CrytoUtilSimple.decrypt(ccspConfig.getConfigValue());
                    ccspConfig.setConfigValue(decryptServiceFilePath);
                    ccsp.setServiceFilePath(decryptServiceFilePath);
                    break;
                default:
            }
        }
        if (ccspUpgrade) {
            Criteria criteria = new Criteria();
            criteria.put("configKey", serviceOpenConfigKey);
            List<SysConfig> sysConfigOpen = systemConfigService.displaySystemConfigList(criteria);
            if (!sysConfigOpen.isEmpty()) {
                String configValue = sysConfigOpen.get(0).getConfigValue();
                if (BooleanUtil.toBoolean(configValue)) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_880218477));
                }
            }

            List<SysConfig> sysConfigs = sysConfigMapper.selectAllConfigs();
            for (SysConfig sysConfig : sysConfigs) {
                sysConfig.setSkipCCSPHandle(true);
                switch (sysConfig.getConfigKey()) {
                    case serviceOpenConfigKey:
                        sysConfig.setConfigValue("true");
                        sysConfig.setUpdatedDt(new Date());
                        break;
                    case serviceAppNameConfigKey:
                        sysConfig.setConfigValue(ccsp.getServiceAppName());
                        break;
                    case serviceAppCipherConfigKey:
                        sysConfig.setConfigValue(CrytoUtilSimple.encrypt(ccsp.getServiceAppPwd()));
                        break;
                    case serviceAppKeyConfigKey:
                        sysConfig.setConfigValue(CrytoUtilSimple.encrypt(ccsp.getServiceAppKey()));
                        break;
                    case serviceFilePathsConfigKey:
                        sysConfig.setConfigValue(CrytoUtilSimple.encrypt(ccsp.getServiceFilePath()));
                        break;
                    default:
                }
            }

            sysConfigMapper.updateBatchByPrimaryKey(sysConfigs);

            executorService.execute(() -> {

                redisTemplate.opsForValue().set(CCSPConstants.CCSP_UPGRADE_STATUS, CCSPUpgradeStatus.UPGRADING.name());

                ccspDataUpgrade.upgrade(ccsp);

                redisTemplate.opsForValue().set(CCSPConstants.CCSP_UPGRADE_STATUS, CCSPUpgradeStatus.COMPLETED.name());
            });
        } else {
            if (ccspUpdate) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1785173285));
            }
        }
        return ccspUpgrade;
    }


    /**
     * 移除Modelarts自定义禁用限制
     */
    private void operationModelartsCommonSharePool() {
        ResRoleRequest resRoleRequest = new ResRoleRequest();
        resRoleRequest.setDisplayName(IamDomainPermissionsEnum.MODELARTS_COMMON_SHARE_POOL.getValue());
        List<ResRole> resRoleList = resRoleService.getResRoleByName(resRoleRequest);
        for (ResRole resRole : resRoleList) {
            iamPermissionService.operationModelartsCommonSharePool(resRole.getOwnerId(),
                                                                   true, false,ProductCodeEnum.MODEL_ARTS.getProductCode(),new ArrayList<String>());
        }
        resRoleService.deleteByParams(new cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria("displayName",
                                                                                                          IamDomainPermissionsEnum.MODELARTS_COMMON_SHARE_POOL
                                                                                                                  .getValue()));
    }

    /**
     * 校验配置管理-平台配置-modelarts相关配置
     * @param sysConfig sysConfig
     * @return RestResult
     */
    private RestResult validateMAConfigs(SysConfig sysConfig) {
        boolean matches = true;
        if (SysConfigConstants.MODEL_ARTS_EDITION.equals(sysConfig.getConfigKey())) {
            String regex = "^[V][1-2]";
            matches = Pattern.matches(regex, sysConfig.getConfigValue());
        }
        if (CommonPropertyKeyEnum.IAM_AUTO_ENTRUST_ACCOUNT.getCode().equals(sysConfig.getConfigKey())
                || CommonPropertyKeyEnum.IAM_AUTO_ENTRUST_NAME.getCode().equals(sysConfig.getConfigKey())
                || CommonPropertyKeyEnum.IAM_AUTO_ENTRUST_SHARE_ID.getCode().equals(sysConfig.getConfigKey())) {
            String regex = "[a-zA-Z0-9_-]*$";
            matches = Pattern.matches(regex, sysConfig.getConfigValue());
        }
        if (CommonPropertyKeyEnum.BIGSCREEN_INTERFACE.getCode().equals(sysConfig.getConfigKey())) {
            matches = Arrays.asList("aksk", "password").contains(sysConfig.getConfigValue());
        }

        if (!matches) {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        return null;
    }

    /**
     * 验证大屏基础参数
     * @param it 参数
     */
    private void validationScreenConfig(UpdateSysConfigsRequest it) {
        String configKey = it.getConfigKey();
        if ("screen.airesource.apiurl".equals(configKey)) {
            // 可改成ping命令等
            if (!it.getConfigValue().startsWith(URL_CONSTRAINT)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1997355146));
            }
        }
        boolean flag = includeConfigkey(configKey);
        if (flag) {
            if (!Validator.isNumber(it.getConfigValue())) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_832539344));
            }
        }


    }

    private static boolean includeConfigkey(String configKey) {
        return Stream.of("screen.airesource.power",
                  "screen.airesource.online-company",
                  "cooperative_users",
                  "number_of_partners",
                  "number_of_trainees",
                  "number_of_joint_venture_personnel",
                  "number_of_salon_activities",
                  "industry_summit",
                  "total_power",
                  "total_GPU",
                  "total_CPU",
                  "total_save",
                  "gpu_weight",
                  "cpu_weight",
                  "hpc_energy_consumption")
            .anyMatch(key -> key.equals(configKey));
    }

    private void validationVerificationCodeConfig(UpdateSysConfigsRequest it) {
        String configKey = it.getConfigKey();
        if (SysConfigKeyEnum.VERIFICATION_CODE_PER_DAY.getKey().equals(configKey)) {
            int parseInt = Integer.parseInt(it.getConfigValue());
            if (parseInt < 1 || 50 < parseInt) {
                throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
            }
        }
        if (SysConfigKeyEnum.MANAGEMENT_SIDE_STARTUP.getKey().equals(configKey)) {
            if (!(BooleanStrEnum.FALSE.getCode().equals(it.getConfigValue()) || BooleanStrEnum.TRUE.getCode().equals(it.getConfigValue()))) {
                throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
            }
        }
    }

    private RestResult checkAccountConfig(List<UpdateSysConfigsRequest> updateRequestList){
        UpdateSysConfigsRequest max = updateRequestList.stream().filter(config -> Objects.equals(config.getConfigKey(), ACCOUNT_CONFIG_MAX_LENGTH)).findFirst().orElse(new UpdateSysConfigsRequest());
        UpdateSysConfigsRequest min = updateRequestList.stream().filter(config -> Objects.equals(config.getConfigKey(), ACCOUNT_CONFIG_MIN_LENGTH)).findFirst().orElse(new UpdateSysConfigsRequest());
        if (Objects.nonNull(min) && Integer.parseInt(min.getConfigValue()) < 4) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_780616784));
        }

        if (Objects.nonNull(max) && Integer.parseInt(max.getConfigValue()) > 64) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_395865822));
        }
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS));
    }

    public void freezeAccount(Map<String, SysConfig> sysConfigs, AuthUser authUser, HttpServletRequest request) {
        log.info("冻结逻辑处理开始");
        try {
            // 最小购买金额大于最小冻结金额
            List<Long> errorUserSid = minimumFrozenAmountOfPlatform(sysConfigs, authUser, request);
            if (CollectionUtil.isNotEmpty(errorUserSid)) {
                List<String> strings = userMapper.selectAccountByUserSidList(errorUserSid);
                String userAccount = String.join("，", strings);

                Long entityId = authUser.getEntityId();
                List<Long> userSidByEntityId = entityUserMapper.selectUserSidByEntityId(entityId);
                HashMap<String, String> platMessageContent = new HashMap<>();
                platMessageContent.put("content", userAccount);
                platMessageContent.put("status", "true".equals(
                        sysConfigs.get(ProductQuotaConfig.MINIMUM_FROZEN_AMOUNT_OPEN + authUser.getEntityId())
                                  .getConfigValue()) ? "冻结"
                        : "解冻");
                // 现在时间
                Date nowDate = new Date();
                // 5、10、30、60分钟间隔发送
                boolean isSend = false;
                for (Long userSid : userSidByEntityId) {

                    Criteria criteria = new Criteria();
                    criteria.setOrderByClause("SEND_DATE DESC");
                    criteria.put("toUserSid", userSid);
                    criteria.put("msgContentLike", userAccount);
                    criteria.put("msgTitleLike", "账户异常");
                    List<Message> messages = messageMapper.selectSimpleByParams(criteria);
                    if (isSend) {
                        break;
                    }
                    if (CollectionUtil.isEmpty(messages)) {
                        isSend = true;
                    } else {
                        Date sendTime = messages.get(0).getSendDate();
                        if (messages.size() == 1) {
                            if (cn.hutool.core.date.DateUtil.offsetMinute(sendTime, 5).isBefore(nowDate)) {
                                isSend = true;
                            }
                        } else if (messages.size() == 2) {
                            if (cn.hutool.core.date.DateUtil.offsetMinute(sendTime, 10).isBefore(nowDate)) {
                                isSend = true;
                            }
                        } else if (messages.size() == 3) {
                            if (cn.hutool.core.date.DateUtil.offsetMinute(sendTime, 30).isBefore(nowDate)) {
                                isSend = true;
                            }
                        } else {
                            if (DateUtil.offsetMinute(sendTime, 60).isBefore(nowDate)) {
                                isSend = true;
                            }
                        }
                    }
                    if (isSend) {
                        sysOssMessageService.sendOssMessage(userSid,platMessageContent,null
                                ,NotificationConsts.PlatformMsg.AccountMsg.BSSMGT_ERROR_ACCOUNT, entityId);
                    }
                }
            }
            log.info("账户冻结逻辑处理，删除FREEZE_ACCOUNT_KEY锁");
            redisTemplate.delete(FREEZE_ACCOUNT_KEY);
        } catch (Exception e) {
            log.info("限额异常:{}", e);
            log.info("账户冻结逻辑处理，删除FREEZE_ACCOUNT_KEY锁");
            redisTemplate.delete(FREEZE_ACCOUNT_KEY);
        }
    }


    private void removeUserIfNeed(List<SysConfig> sysConfigs, String oldDays, Long experienceOrgSid) {
        if (SaasUtil.isSaasEnable()) {
            SysConfig experienceOrgEnable = CollectionUtil.findOne(sysConfigs, config -> Objects.equals(
                    SaasUtil.EXPERIENCE_ORG_ENABLE, config.getConfigKey()));
            if (Objects.nonNull(experienceOrgEnable)) {
                userService.deleteSaasOrgUser(experienceOrgSid);
                if (Objects.equals(SaasUtil.ENABLE, experienceOrgEnable.getConfigValue())) {
                    userService.updateSaasOrgAndRole();
                }
            }
            SysConfig experienceDays = CollectionUtil.findOne(sysConfigs,
                                                              config -> Objects.equals(SaasUtil.EXPERIENCE_DAYS,
                                                                                       config.getConfigKey()));
            if (Objects.nonNull(experienceDays) && !Objects.equals(oldDays, experienceDays.getConfigValue())) {
                userService.updateSaasExperienceDays(Convert.toInt(experienceDays.getConfigValue()));
            }
        }
    }

    /**
     * 获取配置
     * <br>
     * 注：nonAuth接口根据安全需求现需要登录认证，不再是不需要登录即可获取的接口
     *
     * @return {@link List}<{@link DescribeSysConfigResponse}>
     */
    @AuthorizeOss(action = AuthModule.COMMON.PUBLIC.C1.C110 + "," + AuthModule.COMMON.PUBLIC.B1.B114 + "," + AuthModule.COMMON.PUBLIC.Z1.Z102)
    @ApiOperation(httpMethod = "GET", value = "获取配置", notes = "通过传入configType获取配置")
    @GetMapping("/nonAuth")
    public List<DescribeSysConfigResponse> getLoginConfigs(@RequestParam(required = false) String keys) {
        Criteria criteria = new Criteria();
        if (StrUtil.isNotBlank(keys)) {
            List<String> allNoAuthConfigKeys = Arrays.asList(nonAuthConfigKeys.split(","));
            String[] configKeys = keys.split(",");
            ArrayList<String> queryConfigKeys = new ArrayList<>();
            for (String configKey : configKeys) {
                if (allNoAuthConfigKeys.contains(configKey)) {
                    queryConfigKeys.add(configKey);
                }
            }
            if (CollectionUtils.isEmpty(queryConfigKeys)) {
                return new ArrayList<>();
            }
            criteria.put("configKeys", queryConfigKeys);
            criteria.put("configTypes", new ArrayList<>());
        } else {
            criteria.put("configKeys", Arrays.asList(nonAuthConfigKeys.split(",")));
            criteria.put("configTypes", Arrays.asList(nonAuthConfigType.split(",")));
        }
        criteria.setOrderByClause("SORT_RANK desc");
        return BeanConvertUtil.convert(systemConfigService.nonAuthSystemConfigList(criteria)
                , DescribeSysConfigResponse.class);
    }


    /**
     * 获取不用鉴权配置
     *
     * @param keys keys
     * @return {@code RestResult}
     */
    @ApiOperation(httpMethod = "GET", value = "获取配置（无需登录认证）", notes = "支持logintips.privacy,online,logintips.register")
    @GetMapping("/nonAuth/configs")
    public List<DescribeSysConfigResponse> getUnLoginConfigs(@RequestParam String keys) {
        List<String> configKeys = StrUtil.split(keys, ",").stream().distinct().collect(Collectors.toList());
        if(configKeys.stream().anyMatch(key -> !UN_LOGIN_CONFIG_KEYS.contains(key))) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1129952380));
        }
        Criteria criteria = new Criteria().put("configKeys", configKeys.toArray());
        criteria.setOrderByClause("SORT_RANK desc");
        return BeanUtil.copyToList(systemConfigService.displaySystemConfigList(criteria), DescribeSysConfigResponse.class);
    }

    /**
     * ldap验证
     * @param sysConfigs
     * @return
     */
    private boolean validationLdap(List<SysConfig> sysConfigs) {
        OperationLdapRequest operationLdapRequest = new OperationLdapRequest();
        sysConfigs.forEach(sys -> {
            assembleLdapRequest(operationLdapRequest, sys);
        });
        if (StrUtil.isEmpty(operationLdapRequest.getUserName())) {
            return true;
        }
        if(Objects.nonNull(operationLdapRequest.getDomainName())){
            if (!operationLdapRequest.getUserName()
                                     .substring(operationLdapRequest.getUserName().indexOf(",") + 1)
                                     .equals(operationLdapRequest.getDomainName())) {
                log.error("连接失败！请检查域名,和账号保持一致!");
                return false;
            }
        }
        Criteria criteria = new Criteria();
        criteria.getCondition().put("configType","ldap_config");
        List<SysConfig> oldSysConfigs = systemConfigService.displaySystemConfigList(criteria);
        List<String> sysKeyList = sysConfigs.stream().map(SysConfig::getConfigKey).collect(Collectors.toList());
        oldSysConfigs.stream().forEach(oldSysConfig->{
                    if (!sysKeyList.contains(oldSysConfig.getConfigKey())) {
                        assembleLdapRequest(operationLdapRequest,oldSysConfig);
                    }
            });

        return ldapUserService.getLdapContext(operationLdapRequest);
    }

    private void assembleLdapRequest(OperationLdapRequest operationLdapRequest, SysConfig sys) {
        switch (sys.getConfigKey()) {
            case "ldap.connecttype":
                operationLdapRequest.setConnectType(sys.getConfigValue());
                return;
            case "ldap.credentials.path":
                operationLdapRequest.setCredentials(sys.getConfigValue());
                return;
            case "ldap.domainname":
                operationLdapRequest.setDomainName(sys.getConfigValue());
                return;
            case "ldap.home.phone":
                operationLdapRequest.setHomePhone(sys.getConfigValue());
                return;
            case "ldap.password":
                operationLdapRequest.setPassword(sys.getConfigValue());
                return;
            case "ldap.port":
                operationLdapRequest.setPort(sys.getConfigValue());
                return;
            case "ldap.servername":
                operationLdapRequest.setServerName(sys.getConfigValue());
                return;
            case "ldap.username":
                operationLdapRequest.setUserName(sys.getConfigValue());
                return;
            default:
                return;
        }
    }

    /**
     * 校验sfs基本配置参数
     * @param shareConfig
     */
    private void checkParam(ShareConfig shareConfig) {
        //参数基本校验
        if(checkCapacity(shareConfig)){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_879190036));
        }
        //已使用量校验
        if(shareConfig.getPrivateMaxCapacity() < shareConfig.getPrivateMinCapacity()){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_783382929));
        }
        //已使用量校验
        if(shareConfig.getSfsTotalCapacity()<shareConfig.getUseCount()){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1004130644));
        }
        //范围值限制
        if(shareConfig.getDefaultShareCapacity()>10000){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_634311251));
        }

        if (!Arrays.asList(ProductCodeEnum.SFS.getProductCode(), ProductCodeEnum.SFS2.getProductCode(),ProductCodeEnum.DME_OSP.getProductCode()).contains(shareConfig.getServiceType())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_879190036));
        }
    }

    private static boolean checkCapacity(ShareConfig shareConfig) {
        if (checkTotalCapacity(shareConfig)) {
            return true;
        }
        if (checkDefaultShareCapacity(shareConfig)) {
            return true;
        }
        if (checkPrivateMaxCapacity(shareConfig)) {
            return true;
        }
        if (checkPrivateMinCapacity(shareConfig)) {
            return true;
        }
        if (checkMaxUpgradeCount(shareConfig)) {
            return true;
        }
        if (checkMaxDegradeCount(shareConfig)) {
            return true;
        }
        if (checkChangeMaxCapacity(shareConfig)) {
            return true;
        }
        return false;
    }

    /**
     * 校验扩容数
     * @param shareConfig
     * @return
     */
    private static boolean checkChangeMaxCapacity(ShareConfig shareConfig) {
        return shareConfig.getChangeMaxCapacity() <= 0 || 10000 < shareConfig.getChangeMaxCapacity();
    }

    /**
     * 校验缩容数
     * @param shareConfig
     * @return
     */
    private static boolean checkMaxDegradeCount(ShareConfig shareConfig) {
        return shareConfig.getMaxDegradeCount() <= 0 || 20 < shareConfig.getMaxDegradeCount();
    }

    /**
     * 校验最大扩容数
     * @param shareConfig
     * @return
     */
    private static boolean checkMaxUpgradeCount(ShareConfig shareConfig) {
        return shareConfig.getMaxUpgradeCount() <= 0 || 20 < shareConfig.getMaxUpgradeCount();
    }

    /**
     * 校验最小专属容量
     * @param shareConfig
     * @return
     */
    private static boolean checkPrivateMinCapacity(ShareConfig shareConfig) {
        return shareConfig.getPrivateMinCapacity() < 50 || 100 < shareConfig.getPrivateMinCapacity();
    }

    /**
     * 校验专属容量
     * @param shareConfig
     * @return
     */
    private static boolean checkPrivateMaxCapacity(ShareConfig shareConfig) {
        return shareConfig.getPrivateMaxCapacity() < 50 || 10000 < shareConfig.getPrivateMaxCapacity();
    }

    /**
     * 校验默认共享目录
     * @param shareConfig
     * @return
     */
    private static boolean checkDefaultShareCapacity(ShareConfig shareConfig) {
        return shareConfig.getDefaultShareCapacity() <= 0;
    }

    /**
     * 校验总容量
     * @param shareConfig
     * @return
     */
    private static boolean checkTotalCapacity(ShareConfig shareConfig) {
        return shareConfig.getSfsTotalCapacity() <= 0 || 1048576000000L < shareConfig.getSfsTotalCapacity();
    }

    /**
     * 开启/禁用平台最小冻结金额
     */
    private List<Long> minimumFrozenAmountOfPlatform(Map<String, SysConfig> sysMap, AuthUser authUser, HttpServletRequest request) {
        // 异常租户
        List<Long> errorUserSid = new ArrayList<>();
        SysConfig minAmountSysConfig = sysMap.get(ProductQuotaConfig.MINIMUM_PURCHASE_AMOUNT + authUser.getEntityId());
        SysConfig minFrozenAmountSysConfig = sysMap.get(
                ProductQuotaConfig.MINIMUM_FROZEN_AMOUNT + authUser.getEntityId());
        SysConfig minFrozenAmountOpenSysConfig = sysMap.get(
                ProductQuotaConfig.MINIMUM_FROZEN_AMOUNT_OPEN + authUser.getEntityId());
        if (ObjectUtil.isNotNull(minAmountSysConfig) || ObjectUtil.isNotNull(minFrozenAmountSysConfig)) {
            // 平台限额白名单
            List<String> userSidList = JedisUtil.INSTANCE.getList(
                    authUser.getEntityId() + PRODUCTS_QUOTA_WHITE_LIST_KEY);
            // 查询是否存在产品限额，产品限额>平台限额
            // 查询支持按量付费serviceId
            List<String> serviceIds = serviceCategoryService.selectServiceIdByPostPaid(
                    authUser.getEntityId());
            // 是否存在产品限额-最小冻结金额
            List<String> productsQuotaList = new ArrayList<>();
            for (String serviceId : serviceIds) {
                String productsQuota = JedisUtil.INSTANCE.get(PRODUCTS_QUOTA_KEY + serviceId);
                if (Objects.nonNull(productsQuota)) {
                    productsQuotaList.add(serviceId);
                }
            }
            List<cn.com.cloudstar.rightcloud.core.pojo.dto.user.User> status = userMapper.selectByParams(
                    new Criteria("status", ONE));
            // 只要启用的用户
            List<Long> validUserSid = status.stream()
                                            .map(cn.com.cloudstar.rightcloud.core.pojo.dto.user.User::getUserSid)
                                            .collect(Collectors.toList());
            // 拥有按量付费产品的组织
            List<Long> parentOrgSidByPostPaid = sfProductResourceMapper.getParentOrgSidByPostPaid();
            // 最小购买金额，冻结账户
            if ("true".equals(minFrozenAmountOpenSysConfig.getConfigValue())) {
                log.info("平台最小金额开启");
                Criteria criteria = new Criteria();
                criteria.put("thresholdValue", minFrozenAmountSysConfig.getConfigValue());
                criteria.put("status", "normal");
                criteria.put("entityId", authUser.getEntityId());
                List<BizBillingAccount> bizBillingAccounts = bizBillingAccountMapper.selectByParams(criteria);

                // 存在解冻情况
                Criteria criteriaFreeze = new Criteria();
                criteriaFreeze.put("greaterThanBalance", minFrozenAmountSysConfig.getConfigValue());
                criteriaFreeze.put("status", "freeze");
                criteriaFreeze.put("entityId", authUser.getEntityId());
                criteriaFreeze.put("unfreezeType", ZERO);
                List<BizBillingAccount> accountsFreeze = bizBillingAccountMapper.selectByParams(criteriaFreeze);

                if (CollectionUtil.isNotEmpty(accountsFreeze)) {
                    readyToFreeze(accountsFreeze, productsQuotaList, errorUserSid, validUserSid, authUser);
                }

                bizBillingAccounts.forEach(t -> {
                    // 过滤没有按量付费产品的客户
                    if (!parentOrgSidByPostPaid.contains(t.getOrgSid())) {
                        return;
                    }
                    // 只要启用的用户
                    if (!validUserSid.contains(t.getAdminSid())) {
                        return;
                    }
                    if (!userSidList.contains("\"" + t.getId() + "\"")) {
                        // 冻结账户accountId
                        Long accountId = t.getId();
                        // 该账户的运营实体Id
                        Long entityId = t.getEntityId();
                        // 查询是否存在客户限额，客户限额>产品限额>平台限额
                        Map<String, String> productsQuotaAccount = JedisUtil.INSTANCE.hgetall(
                                PRODUCTS_QUOTA_ACCOUNT_KEY + accountId);
                        if (productsQuotaAccount.isEmpty()) {

                            // 查询是否存在产品限额，客户限额>产品限额>平台限额
                            int productByServiceId = sfProductResourceMapper.isProductByServiceId(entityId,
                                                                                                  productsQuotaList);
                            if (CollectionUtil.isEmpty(productsQuotaList) || productByServiceId < 1) {

                                try {
                                    boolean freezeResult = userService.disableFreezeStatusByAccountIDWithLock(t.getId(), ZERO, true,
                                        minFrozenAmountSysConfig.getConfigValue(),false);
                                    if (freezeResult) {
                                        log.info("冻结账户id=[{}]，成功", accountId);
                                        HashMap<String, String> platMessageContent = new HashMap<>();

                                        if (BigDecimal.ZERO.compareTo(new BigDecimal(minFrozenAmountSysConfig.getConfigValue())) > 0) {
                                            platMessageContent.put("symbolAmount", "-");
                                        } else {
                                            platMessageContent.put("symbolAmount", "");
                                        }
                                        platMessageContent.put("minFrozenAmount",
                                            String.valueOf(
                                                Math.abs(Double.parseDouble(new BigDecimal(minFrozenAmountSysConfig.getConfigValue()).setScale(2, BigDecimal.ROUND_DOWN).toString()))));

                                        platMessageContent.put("balance", t.getBalance()
                                            .setScale(2, BigDecimal.ROUND_DOWN)
                                            .toString());
                                        platMessageContent.put("creditLine", t.getCreditLine()
                                            .setScale(2, BigDecimal.ROUND_DOWN)
                                            .toString());
                                        platMessageContent.put("userAccount",
                                            userService.selectByPrimaryKey(t.getAdminSid())
                                                .getAccount());
                                        platMessageContent.put("entityName", t.getEntityName());
                                        frozenLog(t.getAdminSid(),
                                            "调整最小冻结金额策略，" + "账户【" + t.getEntityName() + "】" +
                                                "自动冻结", request);
                                        if (BigDecimal.ZERO.compareTo(t.getBalance()) > 0) {
                                            platMessageContent.put("symbol", "-");
                                        } else {
                                            platMessageContent.put("symbol", "");
                                        }
                                        if (BigDecimal.ZERO.compareTo(t.getCreditLine()) > 0) {
                                            platMessageContent.put("symbolCredit", "-");
                                        } else {
                                            platMessageContent.put("symbolCredit", "");
                                        }

                                        cn.com.cloudstar.rightcloud.core.pojo.dto.user.User user = userService.selectByPrimaryKey(t.getAdminSid());
                                        sysOssMessageService.sendOssMessage(user.getUserSid(), platMessageContent, NotificationConsts.ConsoleMsg.AccountMsg.TENANT_MIN_FROZEN_AMOUNT, NotificationConsts.PlatformMsg.AccountMsg.BSSMGT_MIN_FROZEN_AMOUNT, t.getEntityId());
                                    } else {
                                        log.info("冻结用户id=[{}]失败：{}", t.getAdminSid());
                                        errorUserSid.add(t.getAdminSid());
                                    }

                                } catch (Exception e) {
                                    log.info("冻结用户id=[{}]请求异常：{}", t.getAdminSid(), e.getMessage());
                                    errorUserSid.add(t.getAdminSid());
                                }
                            }
                        }
                    }
                });
            } else {
                log.info("最小冻结金额禁用，解冻自动冻结用户");
                //获取自动冻结的用户
                Criteria criteria = new Criteria();
                criteria.put("status", "freeze");
                criteria.put("entityId", authUser.getEntityId());
                criteria.put("unfreezeType", ZERO);
                List<BizBillingAccount> accounts = bizBillingAccountMapper.selectByParams(criteria);
                readyToFreeze(accounts, productsQuotaList, errorUserSid, validUserSid, authUser);
            }
        }
        return errorUserSid;
    }

    /**
     * 根据条件冻结账户
     */
    private void readyToFreeze(List<BizBillingAccount> accounts, List<String> productsQuotaList,
                               List<Long> errorUserSid, List<Long> validUserSid, AuthUser authUser) {
        List<BizBillingAccount> freezeUsers = new ArrayList<>();
        for (BizBillingAccount account : accounts) {
            // 只要启用的用户
            if (!validUserSid.contains(account.getAdminSid())) {
                continue;
            }
            // 查询是否存在客户限额，客户限额>产品限额>平台限额
            Map<String, String> productsQuotaAccount = JedisUtil.INSTANCE.hgetall(
                    PRODUCTS_QUOTA_ACCOUNT_KEY + account.getId());
            // 查询是否存在产品限额，客户限额>产品限额>平台限额
            int productByServiceId = sfProductResourceMapper.isProductByServiceId(authUser.getEntityId(),
                                                                                  productsQuotaList);
            if (!productsQuotaAccount.isEmpty() || (!productsQuotaList.isEmpty() && productByServiceId > 0)) {
                continue;
            }
            freezeUsers.add(account);
        }
        freezeUsers.forEach(account -> {
            try {
                userService.enableFreezeStatus(account.getId(), false, true, true,authUser,Constants.ZERO);
            } catch (Exception e) {
                log.info("解冻用户id=[{}]请求异常：{}", account.getAdminSid(), e.getStackTrace());
                errorUserSid.add(account.getAdminSid());
            }
        });
    }

    /**
     * 获取entityId
     */
    public Long getEntityId() {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo(WebUtil.getRequest());
        if (ObjectUtils.isEmpty(authUserInfo)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_531007114));
        }
        return authUserInfo.getEntityId();
    }

    /**
     * 解冻/冻结日志
     */
    private void frozenLog(Long userSid, String actionName, HttpServletRequest request) {

        try {
            if (Objects.nonNull(userSid)) {
                cn.com.cloudstar.rightcloud.core.pojo.dto.user.User user = userMapper.selectByPrimaryKey(userSid);
                if (Objects.nonNull(user)) {
                    ActionLog log = new ActionLog();
                    log.setAccount(user.getAccount());
                    log.setActionName(actionName);
                    log.setResource(actionName);
                    log.setTagName(actionName);
                    log.setSuccess("成功");
                    log.setActionTime(new Date());
                    String remoteIp = cn.com.cloudstar.rightcloud.common.util.IPAddressUtil.getRemoteHostIp(request);
                    String ipAddress = cn.com.cloudstar.rightcloud.common.util.IPAddressUtil.getIpAddress();
                    log.setActionPath(cn.com.cloudstar.rightcloud.common.util.StringUtil.getUri(request));
                    log.setActionMethod("ProductQuotaController.operateBizAccountProductQuotas");
                    log.setHttpMethod(request.getMethod());
                    log.setLbIp(remoteIp);
                    log.setRemoteIp(ipAddress);
                    log.setRoleName(RequestContextUtil.getRoleNames(AuthUserHolder.getAuthUser().getUserSid()));
                    if (Objects.nonNull(this.tracer)) {
                        Span span = this.tracer.currentSpan();
                        if (Objects.nonNull(span) && Objects.nonNull(span.context())) {
                            log.setTraceId(span.context().traceId());
                            log.setSpanId(span.context().spanId());
                        }
                    }
                    String userClient = request.getHeader("User-Agent");
                    log.setClient(userClient);
                    mongoTemplate.insert(log, "action_log");

                    String logDetail  = actionName+"成功";
                    OperationResourceEnum opEnum = OperationResourceEnum.ENABLE_FREEZESTATUS;
                    if (cn.com.cloudstar.rightcloud.oss.common.util.StringUtil.containsIgnoreCase(actionName, "冻结")) {
                        opEnum = OperationResourceEnum.DISABLE_RESOURCESTATUS;
                    }
                    bizCustomerActionLogService.insertActionLog(user, opEnum, logDetail);

                }
            }
        } catch (Exception e) {
            log.error("exception message:", e);
            log.error("SystemConfigCtrl_【{}】", JSON.toJSONString(e.getMessage()));
        }
    }


    private void sendDelayMessage(BigScreenMessage data) {
        long delayMilliseconds = 1;
        // 给延迟队列发送消息
        amqpTemplate.convertAndSend(EXCHANGE_NAME, BIG_SCREEN_ROUTING_KEY,
                data, (message) -> {
                    // 给消息设置延迟毫秒值
                    message.getMessageProperties().setHeader("x-delay", delayMilliseconds);
                    return message;
                });
    }

    /**
     * 【Since v2.5.0】获取敏感数据通过配置Key
     *
     * @param describeSysConfigRequest 描述综管系统配置请求
     * @return {@link RestResult}
     */
    @GetMapping("/sensitive_data")
    @ApiOperation(httpMethod = "GET", value = "查询脱敏配置信息")
    @AuthorizeOss(action = ZF.ZF)
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "'查询脱敏配置信息'", bizId = "#describeSysConfigRequest.configKey", resource = OperationResourceEnum.SELECT_USER_PERSONAL_INFORMATION,integrity = true, tagNameUs ="'Query desensitization configuration information'")
    private RestResult getSensitiveDataByConfigKey(@Valid DescribeSysConfigRequest describeSysConfigRequest) {
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (authUser == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        if (StringUtils.isEmpty(describeSysConfigRequest.getConfigKey())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAMS_NOT_LEGALITY));
        }

        String key = TWICE_VALIDATE + authUser.getUserSid();
        DateTime dateTime = new DateTime();
        String value = String.valueOf(redisTemplate.opsForValue().get(key));
        DateTime parse = cn.hutool.core.date.DateUtil.parse(value);
        if (StrUtil.isBlank(value) || "null".equalsIgnoreCase(value)) {
            return new RestResult(NEED_TWICE_VALIDATE);
        }
        if (StrUtil.isNotBlank(value) && !"null".equalsIgnoreCase(value)) {
            if (cn.hutool.core.date.DateUtil.between(parse, dateTime, DateUnit.MINUTE) >= AUTH_AGAIN_MINUTE) {
                return new RestResult(NEED_TWICE_VALIDATE);
            }
        }

        String configValue = sysConfigService.getValueByConfigKey(describeSysConfigRequest.getConfigKey());
        return new RestResult(CrytoUtilSimple.decrypt(configValue));
    }


    /**
     * 批量更新配置
     **/
    @PostMapping("/payTest")
    @ApiOperation(httpMethod = "POST", value = "支付测试", notes = "通过提交SysConfig的json数组测试支付配置是否成功")
    @AuthorizeOss(action = ZF10.ZF1001)
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "'支付测试'", resource = OperationResourceEnum.TEST_ONLINE_PAY, tagNameUs ="'Payment Testing'")
    public RestResult payConfigTest(@Valid @RequestBody List<UpdateSysConfigsRequest> updateRequestList) {
        List<SysConfig> sysConfigs = BeanConvertUtil.convert(updateRequestList, SysConfig.class);
        boolean result = systemConfigService.payConfigTest(sysConfigs);
        if (result) {
            return new RestResult(RestResult.Status.SUCCESS,WebUtil.getMessage(MsgCd.CONFIG_TEST_SUCCESS));
        }
        return new RestResult(RestResult.Status.FAILURE,WebUtil.getMessage(MsgCd.CONFIG_TEST_FAILURE));
    }
}
