/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.module.operationcore.controller.common;

import cn.com.cloudstar.rightcloud.common.constants.NotificationConsts;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.mq.request.BaseNotificationMqBean;
import cn.com.cloudstar.rightcloud.common.util.MapsKit;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.SysConfig;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.User;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.operationlog.annotation.OperationLog;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationTypeEnum;
import cn.com.cloudstar.rightcloud.operationlog.util.OperationLogMdcUtil;
import cn.com.cloudstar.rightcloud.oss.common.annotation.RejectCall;
import cn.com.cloudstar.rightcloud.oss.common.api.ApiGroup;
import cn.com.cloudstar.rightcloud.oss.common.api.ApiGroupEnum;
import cn.com.cloudstar.rightcloud.oss.common.cache.JedisUtil;
import cn.com.cloudstar.rightcloud.oss.common.constants.RestConst;
import cn.com.cloudstar.rightcloud.oss.common.constants.SysConfigConstants;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.oss.common.enums.BooleanStrEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.SysConfigKeyEnum;
import cn.com.cloudstar.rightcloud.oss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.oss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.oss.common.pojo.RestResult.Status;
import cn.com.cloudstar.rightcloud.oss.common.schedule.constant.MQConstants;
import cn.com.cloudstar.rightcloud.oss.common.schedule.helper.ScheduleHelper;
import cn.com.cloudstar.rightcloud.oss.common.util.*;
import cn.com.cloudstar.rightcloud.oss.common.util.sms.CheckSmsCodeUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.sms.SMSUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.sms.bean.SendEmailRequest;
import cn.com.cloudstar.rightcloud.oss.common.util.sms.bean.SendNotifyRequest;
import cn.com.cloudstar.rightcloud.oss.common.util.sms.bean.SysMSmsTemplate;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user.MobileSmsCodeRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user.ValidateEmailRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user.MobileSmsCodeRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.service.user.UserService;
import cn.com.cloudstar.rightcloud.oss.module.msg.bean.param.SysMSmsTemplateParam;
import cn.com.cloudstar.rightcloud.oss.module.msg.dao.SysMSmsTemplateMapper;
import cn.com.cloudstar.rightcloud.oss.module.system.dao.config.SystemConfigMapper;
import cn.com.cloudstar.rightcloud.oss.module.system.service.message.BusinessNotificationService;
import cn.com.cloudstar.rightcloud.oss.module.system.service.message.ISysOssMessageService;
import cn.com.cloudstar.rightcloud.oss.module.system.service.message.NotificationService;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Sets;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.ws.rs.QueryParam;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static cn.com.cloudstar.rightcloud.common.constants.NotificationConsts.SEND_NOTIFICATION_EXCHANGE;

/**
 * 共通控制器
 *
 * <AUTHOR>
 */
@ApiGroup(ApiGroupEnum.OPERATION_GROUP)
@Api(tags = "共通Ctrl", value = "/common")
@RestController
@RequestMapping("/common")
@Slf4j
public class CommonController {

    @Autowired
    private BusinessNotificationService businessNotificationService;

    @Resource
    private SysMSmsTemplateMapper sysMSmsTemplateMapper;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private UserService userService;
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private SystemConfigMapper systemConfigMapper;

    @Autowired
    private ISysOssMessageService  sysOssMessageService;

    private static final List<String> CHECK_TYPES = Arrays.asList("maBms","406sms","businessTag","amountClearance","creditLine","deleteCustomer","freeze","recharege","expirationTime","editUser","recharge","specificationFamily","setDir","updateMobile","deleteEnv","association","deleteEntity","saveConfigs","register","retrievePassword","reAuthentication","deleteAccesskey","firstLogin", "userLogin");

    /**
     * 获取验证码
     * 该接口用作已经登录用户的验证码发送
     * @param request    请求
     * @param moduleType 模块类型
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "POST", value = "获取验证码", notes = "获取验证码 用户登陆时将这个验证码发送给手机")
    @PostMapping(value = "/sms/send_code")
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "'获取短信验证码'", param = "#request", resource = OperationResourceEnum.GET_SMS_CODE, tagNameUs ="'Get SMS verification code'")
    public RestResult getMobileSmsCode(@RequestBody @Valid MobileSmsCodeRequest request,
                                       @RequestHeader String moduleType) {
        RestResult restResult = new RestResult(RestConst.HttpConst.Forbidden,
                                               RestResult.Status.FAILURE,
                                               "短信验证码发送失败",
                                               null
        );
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (authUser != null) {
            request.setPhone(authUser.getMobile());
        }else{
            log.info("CommonController.getMobileSmsCode 如果有账号，请先登录再操作");
        }
        if (StringUtils.isNotBlank(request.getCheckType()) && !CHECK_TYPES.contains(request.getCheckType())) {
            log.info("CommonController.getMobileSmsCode CHECK_TYPES");
            return restResult.setMessage(WebUtil.getMessage(MsgCd.PARAMS_NOT_LEGALITY));
        }
        //验证码频繁校验
        if(Objects.nonNull(request.getPhone())){
            String amount= JedisUtil.INSTANCE.get(request.getPhone() + request.getUserSid());
            if(!StrUtil.isBlank(amount)){
                return restResult.setMessage("您的验证码请求过于频繁，请一分钟后再试。");
            }
        }

        if(!this.validatePhone(request.getPhone())){
            throw new  BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_285305065));
        }

        //运营管理员 无orgSid
        boolean isAdminUser = false;
        if("bss".equals(moduleType)){
            isAdminUser=true;
        }
        String property = PropertiesUtil.getProperty(SysConfigConstants.SMS_ENABLE);
        if(!"1".equals(property)){
            return restResult.setMessage("短信未启用");
        }

        log.info("CommonController.getMobileSmsCode isAdminUser： {}", isAdminUser);

        if (StringUtil.isNullOrEmpty(request.getPhone())
                || StringUtil.isNullOrEmpty(request.getT())
                || StringUtil.isNullOrEmpty(request.getCaptcha())) {
            log.info("CommonController.getMobileSmsCode phone|t|captcha is null");
            return restResult;
        }
        try {
            JedisUtil jedisUtil = JedisUtil.INSTANCE;
            // 判断IP地址限制
            String ip = IPAddressUtil.getRemoteHostIp(WebUtil.getRequest());
            // 剩余IP发送短信条数
            String ipLimitKey = ip + "LIMIT";
            String ipLimit = jedisUtil.get(ipLimitKey);
            // 同一IP上一次发送时间
            String ipTimeLimitKey = ip + "TIME_LIMIT";
            String ipTimeLimit = jedisUtil.get(ipTimeLimitKey);
            if (!isAdminUser && !StringUtil.isNullOrEmpty(ipLimit) && Integer.parseInt(ipLimit) <= 0) {
                // IP发送短信条数超出限制
                log.info("CommonController.getMobileSmsCode IP发送短信条数超出限制 key: {}", ipLimitKey);
                return restResult;
            }
            if (!StringUtil.isNullOrEmpty(ipTimeLimit)
                    && Calendar.getInstance().getTime().getTime() / 1000 - Long.parseLong(ipTimeLimit) <= 60) {
                // 同一IP上一次发送时间<=60秒
                return restResult.setMessage("您的验证码请求过于频繁，请一分钟后再试。");
            }

            String limitKey = "SEND_CODE:" + IPAddressUtil.getRemoteHostIp(WebUtil.getRequest());
            String limitRedisKey = "VALIDATION_LIMIT:" + limitKey;
            String limitTimeLineRedisKey = limitRedisKey + ":TIME_LINE";
            Integer period = 60;

            // 同一IP上一次发送时间
            String timeLimit = jedisUtil.get(limitTimeLineRedisKey);
            if (!isAdminUser && !StringUtil.isNullOrEmpty(timeLimit) && period > 0
                    && Calendar.getInstance().getTime().getTime() / 1000 - Long.parseLong(timeLimit) <= period) {
                // 上一次发送时间<=60秒
                return restResult.setMessage("您的验证码请求过于频繁，请一分钟后再试。");
            }
            Calendar calendarOther = Calendar.getInstance();
            calendarOther.set(Calendar.HOUR_OF_DAY, 23);
            calendarOther.set(Calendar.MINUTE, 59);
            calendarOther.set(Calendar.SECOND, 59);
            jedisUtil.expireAt(limitRedisKey, Math.toIntExact(calendarOther.getTime().getTime() / 1000));

            jedisUtil.set(limitTimeLineRedisKey, Calendar.getInstance().getTime().getTime() / 1000 + "");
            jedisUtil.expireAt(limitTimeLineRedisKey, Math.toIntExact(calendarOther.getTime().getTime() / 1000));

            String limit = jedisUtil.get(request.getPhone() + "LIMIT");
            limit = StringUtil.isNullOrEmpty(limit) ? "0" : limit;
            Criteria criteria = new Criteria();
            criteria.put("configType", "verification_code_config");
            List<SysConfig> sysConfigs = systemConfigMapper.displaySystemConfigList(criteria);
            String configLimit = "0";
            for (SysConfig config : sysConfigs) {
                if (SysConfigKeyEnum.VERIFICATION_CODE_PER_DAY.getKey().equals(config.getConfigKey())) {
                    configLimit = config.getConfigValue();
                }
                if (SysConfigKeyEnum.MANAGEMENT_SIDE_STARTUP.getKey().equals(config.getConfigKey())) {
                    if (BooleanStrEnum.TRUE.getCode().equals(config.getConfigValue())) {
                        isAdminUser = false;
                    }
                }
            }
            boolean superAdmin = Objects.nonNull(authUser) && Objects.equals(100L, authUser.getUserSid());
            if (!superAdmin && !isAdminUser && Integer.parseInt(limit) >= Integer.parseInt(configLimit)) {
                return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.SMS_LIMIT, new String[]{configLimit}));
            }

            Map<String,String> params = new HashMap();
            String smscode = RandomUtil.randomInt(100000, 999999) + "";
            params.put("code",smscode);

            String templateCode= "";
            SysMSmsTemplate sysMSmsTemplate = null;
            String platform = PropertiesUtil.getProperty(SysConfigConstants.SMS_PLATFORM_SELECTED);
            SysMSmsTemplateParam sysMSmsTemplateParam = new SysMSmsTemplateParam();
            sysMSmsTemplateParam.setSmsPlatform(platform);
            sysMSmsTemplateParam.setMsgId(NotificationConsts.OtherMsg.COMMON_SMS_CODE);
            List<SysMSmsTemplate> sysMSmsTamplates = sysMSmsTemplateMapper.queryAll(sysMSmsTemplateParam);
            if(!CollectionUtils.isEmpty(sysMSmsTamplates)){
                sysMSmsTemplate = sysMSmsTamplates.get(0);
                templateCode = sysMSmsTamplates.get(0).getSmsPlatformCode();
            }
            boolean result = false;
            log.info("短信发送平台：{}",platform);
            if(sysMSmsTemplate !=null){
                if("huaweiyun".equals(platform)){
                    result= SMSUtil.sendHuaweiSms(request.getPhone(),sysMSmsTemplate,params);
                }
                if("aliyun".equals(platform)){
                    result= SMSUtil.sendAliSms(request.getPhone(),templateCode,params);
                }
            }


            if (result) {
                Calendar calendar = Calendar.getInstance();
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                //验证码增加验证类型
                if (Objects.nonNull(request.getCheckType())){
                    jedisUtil.set(request.getPhone(), request.getCheckType()+smscode);
                }else {
                    jedisUtil.set(request.getPhone(), smscode);
                }
                jedisUtil.expire(request.getPhone(), 300);
                Date time = calendar.getTime();
                long betweenMs = DateUtil.betweenMs(time, new Date()) / 1000;
                jedisUtil.set(request.getPhone() + "LIMIT", Integer.parseInt(limit) + 1 + "", Math.toIntExact(betweenMs));

                jedisUtil.set(ip + "LIMIT",
                        StringUtil.isNullOrEmpty(ipLimit) ? "19" : Integer.parseInt(ipLimit) - 1 + ""
                );
                jedisUtil.expireAt(ip + "LIMIT", Math.toIntExact(calendar.getTime().getTime() / 1000));

                jedisUtil.set(ip + "TIME_LIMIT", Calendar.getInstance().getTime().getTime() / 1000 + "");
                jedisUtil.expireAt(ip + "TIME_LIMIT", Math.toIntExact(calendar.getTime().getTime() / 1000));

                JedisUtil.INSTANCE.set(request.getPhone() + request.getUserSid(),String.valueOf(request.getPhone()),60 * 1);
                return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_855933043));
            } else {
                return restResult;
            }
        } catch (Exception e) {
            log.error("发送验证码异常：",e);
            return restResult;
        }
    }

    private Boolean validatePhone(String phone) {
        String regex = "^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\\d{8}$";
        java.util.regex.Pattern compile = java.util.regex.Pattern.compile(regex);
        Matcher matcher = compile.matcher(phone);
        return matcher.matches();
    }

    /**
     * 获取验证码
     * 该接口用作已经登录用户的验证码发送
     * @param request    请求
     * @param moduleType 模块类型
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "POST", value = "获取验证码", notes = "用户未登录时使用该接口获取短信验证码")
    @PostMapping(value = "/sms/register/send_code")
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "'获取短信验证码'", param = "#request", resource = OperationResourceEnum.GET_SMS_CODE, tagNameUs ="'Get SMS verification code'")
    public RestResult getMobileVerifyCode(@RequestBody @Valid MobileSmsCodeRequest request,
                                       @RequestHeader String moduleType){
        OperationLogMdcUtil.saveContent("获取短信验证码(未登录用户)");
        return this.getMobileSmsCode(request,moduleType);
    }

    /**
     * 【Since v2.5.0】验证短信验证码
     *
     * @param smsCode 短信代码
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "GET", value = "验证短信验证码", notes = "验证短信验证码")
    @GetMapping(value = "/sms/check_code")
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "'验证短信验证码'", bizId = "#smsCode", resource = OperationResourceEnum.SMS_CHECK, tagNameUs ="'Verify SMS verification code'")
    public RestResult checkMobileSmsCode(@RequestParam(required = true) @QueryParam("smsCode") String smsCode) {
        if(StringUtils.isEmpty(smsCode)){
            throw new BizException("验证码不能为空!");
        }
        //验证码长度
        checkSmsCodeLength(smsCode);

        Pattern pattern = java.util.regex.Pattern.compile("^[A-Za-z0-9_-]+$");
        if (StringUtils.isNotBlank(smsCode)) {
            if (!pattern.matcher(smsCode).matches()) {
                throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
            }
        }
        CheckSmsCodeUtil.checkCode(null, smsCode);
        cn.com.cloudstar.rightcloud.oss.common.pojo.User authUser = AuthUtil.getAuthUser();
        if (authUser != null) {
            // 判断IP地址限制
            String ip = IPAddressUtil.getRemoteHostIp(WebUtil.getRequest());

            // 1小时内不需要重新验证
            JedisUtil.INSTANCE.set(authUser.getAccount() + authUser.getMobile() + ip + "SMS_CODE_LIMIT",
                          Calendar.getInstance().getTime().getTime() / 1000 + "", 60 * 60);

            return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1465594404));
        } else {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1968892607));
        }
    }

    private static void checkSmsCodeLength(String smsCode) {
        String realSmsCode = smsCode;
        for (String checkType : CHECK_TYPES) {
            if (StringUtils.startsWith(smsCode, checkType)) {
                realSmsCode = StringUtils.removeStart(smsCode, checkType);
                break;
            }
        }
        if (StringUtils.length(realSmsCode) != 6) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERROR_SMS_CODE_LENGTH));
        }
    }

    /**
     * 获取邮箱验证码
     *
     * @param request 请求
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "POST", value = "获取邮箱验证码")
    @PostMapping("/email/send_code")
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "'邮箱'", param = "#request", resource = OperationResourceEnum.GET_EMAIL_CODE, tagNameUs ="'Mailbox'")
    public RestResult getEmailCode(@RequestBody @Valid ValidateEmailRequest request) {
        String code = RandomUtil.randomInt(100000, 999999) + "";
        try {
            RequestLimitUtil.limit("SEND_CODE:" + IPAddressUtil.getRemoteHostIp(WebUtil.getRequest()),20,60);
            List<cn.com.cloudstar.rightcloud.core.pojo.dto.user.User> users = userService.findUserByEmail(request.getEmail());
            String userAccount = "【" + users.get(0).getAccount() + "】";
            AtomicBoolean isSuccess = new AtomicBoolean(false);
            RequestLimitUtil.limit("SEND_CODE:"+request.getEmail(),20,60,()->{
                isSuccess.set(businessNotificationService.sendCode(request.getEmail(), code, userAccount));
            });
            if (isSuccess.get()) {
                JedisUtil.INSTANCE.set(request.getEmail(), code);
                JedisUtil.INSTANCE.expire(request.getEmail(), 300);
                return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_675452127));
            }
        } catch (Exception e) {
            // do nothing
            log.error("CommonController.getEmailCode Exception:",e.getMessage());
        }
        return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_675396708));
    }

    /**
     * [INNER API] 发送自动续订电子邮件
     *
     * @param sendEmailRequest 发送电子邮件请求
     * @return {@link RestResult}
     */
    @ApiOperation("发送邮件")
    @RejectCall
    @PostMapping("/email")
    public RestResult sendAutoRenewEmail(@RequestBody SendEmailRequest sendEmailRequest) {
        businessNotificationService.sendAutoRenewEmail(sendEmailRequest.getEmail(), sendEmailRequest.getContent());
        return new RestResult();
    }

    /**
     * [INNER API] fluentd日志目录容量告警
     *
     * @param request 请求
     * @return {@link RestResult}
     */
    @RejectCall
    @ApiOperation("fluentd日志目录容量告警")
    @PostMapping("/sendNotify")
    public RestResult sendNotify(@RequestBody SendNotifyRequest request) {

        if (request.isMinioFlag()){
            // 发给所有的运营管理员
            BigDecimal minioConfig = BigDecimal.ZERO;
            BigDecimal minioSize = BigDecimal.ZERO;
            if(request.getMinioConfig() != null){
                minioConfig = new BigDecimal(request.getMinioConfig());
            }
            if(request.getMinioSize() != null){
                minioSize = new BigDecimal(request.getMinioSize());
            }
            if(minioSize.compareTo(minioConfig) >= 0){
                HashMap<String, String> messageContent = new HashMap<>();
                messageContent.put("serviceName", "fluentd日志大小限制预警");
                messageContent.put("minioConfig", String.valueOf(request.getMinioConfig()));
                messageContent.put("minioSize", String.valueOf(request.getMinioSize()));
                messageContent.put("companyName", PropertiesUtil.getProperty("company.name"));
                BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
                baseNotificationMqBean.setMsgId(NotificationConsts.PlatformMsg.AccountMsg.BSSMGT_MINIO_LOG);
                baseNotificationMqBean.setMap(messageContent);
                sysOssMessageService.sendOssMessage(null,messageContent,null,NotificationConsts.PlatformMsg.AccountMsg.BSSMGT_MINIO_LOG,null);
                return new RestResult();
            }
        }
        if (request.getRoleIds() != null) {
            if (request.getRoleIds() != null) {
                List<Long> roleIds = request.getRoleIds();
                Criteria criteria = new Criteria();
                Set<cn.com.cloudstar.rightcloud.core.pojo.dto.user.User> userByRoleId = Sets.newHashSet();
                for (Long roleId : roleIds) {
                    criteria.setCondition(MapsKit.of("roleSid", roleId));
                    userByRoleId.addAll(userService.findUserByRoleId(criteria));
                }
                if (CollectionUtil.isNotEmpty(userByRoleId)) {
                    for (cn.com.cloudstar.rightcloud.core.pojo.dto.user.User s : userByRoleId) {
                        try {
                            HashMap<String, String> messageContent = new HashMap<>(request.getMessageContent());

                            messageContent.put("recipient", s.getAccount());
                            BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
                            baseNotificationMqBean.setMsgId(request.getMessageId());
                            baseNotificationMqBean.getImsgUserIds().add(s.getUserSid());
                            baseNotificationMqBean.setMap(messageContent);

                            rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT, baseNotificationMqBean);

                        } catch (Exception e) {
                            log.error("HPCServiceImpl_releaseHPC_sendNotification_unsubscribe_hpc_error_{}",
                                      e.getMessage());
                            continue;
                        }
                    }
                }
            }
        } else if(CollectionUtil.isNotEmpty(request.getToUserIds())){
            BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
            baseNotificationMqBean.setMsgId(request.getMessageId());
            baseNotificationMqBean.setImsgUserIds(request.getToUserIds());
            baseNotificationMqBean.setMap(request.getMessageContent());
            rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT, baseNotificationMqBean);

        }
        return new RestResult();
    }

    /**
     * [INNER API] 发送更新信用额度
     *
     * @param request 请求
     * @return {@link RestResult}
     */
    @RejectCall
    @ApiOperation("发送信用额度通知 bss 内部调用")
    @PostMapping("/sendUpdateCreditLine")
    public RestResult sendUpdateCreditLine(@RequestBody SendNotifyRequest request) {
        BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
        baseNotificationMqBean.setMsgId(request.getMessageId());
        baseNotificationMqBean.getImsgUserIds().addAll(request.getToUserIds());
        baseNotificationMqBean.setMap(request.getMessageContent());
        baseNotificationMqBean.setEntityId(request.getEntityId());
        rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT, baseNotificationMqBean);

        return new RestResult();
    }

    /**
     * 【SINCE2.6.0】信用额度修改后加入延时 oss 转发
     *
     * @param id ID
     *
     * @return {@link RestResult}
     */
    @RejectCall
    @ApiOperation("信用额度修改后加入延时 oss 转发")
    @GetMapping("/sendUpdateCreditLineSchedule/{id}")
    public RestResult sendUpdateCreditLineSchedule(@PathVariable Long id) {

        ScheduleHelper.checkExpire(id, MQConstants.MessageType.CHECK_EXPIRE_CREDIT_LINE);
        return new RestResult();
    }

    @ApiOperation(httpMethod = "POST", value = "获取验证码", notes = "获取验证码 并且将这个验证码发送给手机")
    @PostMapping(value = "/login/sms/send_code")
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "'获取短信验证码(用户登录)'", param = "#request", resource = OperationResourceEnum.GET_SMS_CODE, tagNameUs ="'Get SMS verification code'")
    public RestResult getLoginMobileSmsCode(@RequestBody @Valid MobileSmsCodeRequest request, @RequestHeader String moduleType) {
        User user = userService.selectUserNameByMobile(request.getPhone(), moduleType);
        request.setUserSid(user.getUserSid());
        RestResult result =  this.getMobileSmsCode(request,moduleType);
        if (result.getStatus()) {
            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_855933043), user.getAccount());
        }else {
            return new RestResult(RestConst.HttpConst.Forbidden, RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2004029892), null);
        }
    }
}
