/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.module.analysis.bean.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

@Data
@ApiModel(description = "操作返回数据")
public class DescribeResOptAllResponse implements Serializable {

    @ApiModelProperty(value = "实例操作返回")
    private DescribeResOptInstanceResponse optInstanceResponse;

    @ApiModelProperty(value = "硬盘操作返回")
    private DescribeResOptDiskResponse optDiskResponse;

    @ApiModelProperty(value = "网络操作返回")
    private DescribeResOptNetworkResponse optNetworkResponse;
}
