<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2018 Cloud-Star, Inc. All Rights Reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.cloudstar.rightcloud.oss.module.maintenance.dao.templates.ServerTemplateMapper">
    <resultMap id="BaseResultMap"
               type="cn.com.cloudstar.rightcloud.core.pojo.dto.st.ServerTemplate">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="href" property="href" jdbcType="VARCHAR"/>
        <result column="publisher" property="publisher" jdbcType="VARCHAR"/>
        <result column="publish_status" property="publishStatus" jdbcType="VARCHAR"/>
        <result column="cloned_id" property="clonedId" jdbcType="BIGINT"/>
        <result column="cloned_name" property="clonedName" jdbcType="VARCHAR"/>
        <result column="cloned_dt" property="clonedDt" jdbcType="TIMESTAMP"/>
        <result column="org_sid" property="orgSid" jdbcType="BIGINT"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="created_dt" property="createdDt" jdbcType="TIMESTAMP"/>
        <result column="updated_by" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="updated_dt" property="updatedDt" jdbcType="TIMESTAMP"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
        <result column="published_id" property="publishedId" jdbcType="BIGINT"/>
        <result column="env_config" property="envConfig" jdbcType="VARCHAR"/>
        <result column="fixed" property="fixed" jdbcType="TINYINT"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <collection property="versions" ofType="java.util.Map">
            <result column="unique_id" property="id" jdbcType="BIGINT"/>
            <result column="server_template_id" property="serverTemplateId" jdbcType="VARCHAR"/>
            <result column="version_name" property="versionName" jdbcType="VARCHAR"/>
            <result column="rev_env_config" property="revEnvConfig" jdbcType="VARCHAR"/>
            <result column="commit_comments" property="commitComments" jdbcType="VARCHAR"/>
            <result column="rev_description" property="description" jdbcType="VARCHAR"/>
            <result column="version_updated_dt" property="versionUpdatedDt" jdbcType="TIMESTAMP"/>
            <result column="version_updated_user" property="versionUpdatedUser" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>

    <resultMap id="ServerTemplateListMap"
        type="cn.com.cloudstar.rightcloud.core.pojo.dto.st.ServerTemplateListDTO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="template_class" property="templateClass" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="publisher" property="publisher" jdbcType="VARCHAR"/>
        <result column="publish_status" property="publishStatus" jdbcType="VARCHAR"/>
        <result column="cloud_env_names" property="cloudEnvNames" jdbcType="VARCHAR"/>
        <result column="cloud_env_types" property="cloudEnvTypes" jdbcType="VARCHAR"/>
        <result column="version_name" property="versionName" jdbcType="VARCHAR"/>
        <result column="org_name" property="orgName" jdbcType="VARCHAR"/>
        <result column="latest_history" property="latestHistory" jdbcType="VARCHAR"/>

        <result column="updated_dt" property="updatedDt" jdbcType="TIMESTAMP"/>
        <result column="fixed" property="fixed" jdbcType="TINYINT"/>
    </resultMap>
    <resultMap id="ServerTemplateListMapWithRevison"
               type="cn.com.cloudstar.rightcloud.core.pojo.dto.st.ServerTemplate">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="href" property="href" jdbcType="VARCHAR"/>
        <result column="publisher" property="publisher" jdbcType="VARCHAR"/>
        <result column="publish_status" property="publishStatus" jdbcType="VARCHAR"/>
        <result column="template_class" property="templateClass" jdbcType="VARCHAR"/>
        <result column="cloned_id" property="clonedId" jdbcType="BIGINT"/>
        <result column="cloned_name" property="clonedName" jdbcType="VARCHAR"/>
        <result column="cloned_dt" property="clonedDt" jdbcType="TIMESTAMP"/>
        <result column="org_sid" property="orgSid" jdbcType="BIGINT"/>
        <result column="org_name" property="orgName" jdbcType="VARCHAR"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="created_dt" property="createdDt" jdbcType="TIMESTAMP"/>
        <result column="updated_by" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="updated_dt" property="updatedDt" jdbcType="TIMESTAMP"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
        <result column="published_id" property="publishedId" jdbcType="BIGINT"/>
        <result column="env_config" property="envConfig" jdbcType="VARCHAR"/>
        <result column="cloud_env_names" property="cloudEnvNames" jdbcType="VARCHAR"/>
        <result column="cloud_env_types" property="cloudEnvTypes" jdbcType="VARCHAR"/>
        <result column="fixed" property="fixed" jdbcType="TINYINT"/>

        <association property="revision" javaType="cn.com.cloudstar.rightcloud.core.pojo.dto.st.ServerTemplateRevision">
            <id property="id" column="revision_id"/>
            <result property="mainTemplateId" column="main_template_id"/>
            <result property="serverTemplateId" column="server_template_id"/>
            <result property="commitComments" column="commit_comments"/>
            <result property="versionName" column="version_name"/>
            <result property="createdDt" column="rev_created_at"/>
        </association>
    </resultMap>

    <resultMap id="ServerTemplateDetailMap"
        type="cn.com.cloudstar.rightcloud.core.pojo.dto.st.ServerTemplateDetailDTO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="template_class" property="templateClass" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="version_name" property="versionName" jdbcType="VARCHAR"/>
        <result column="publisher" property="publisher" jdbcType="VARCHAR"/>
        <result column="publish_status" property="publishStatus" jdbcType="VARCHAR"/>
        <result column="cloud_env_names" property="cloudEnvNames" jdbcType="VARCHAR"/>
        <result column="cloud_env_types" property="cloudEnvTypes" jdbcType="VARCHAR"/>
        <result column="updated_dt" property="updatedDt" jdbcType="TIMESTAMP"/>
        <result column="updated_by" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="href" property="href" jdbcType="VARCHAR"/>
        <result column="cloned_id" property="clonedId" jdbcType="BIGINT"/>
        <result column="main_template_id" property="mainTemplateId" jdbcType="BIGINT"/>
        <result column="cloned_name" property="clonedName" jdbcType="VARCHAR"/>
        <result column="cloned_dt" property="clonedDt" jdbcType="TIMESTAMP"/>
        <result column="env_config" property="envConfig" jdbcType="VARCHAR"/>
        <result column="fixed" property="fixed" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Example_Where_Clause">
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.name != null">
                and name = #{condition.name}
            </if>
            <if test="condition.cloneLikeName != null">
                and name like CONCAT(#{condition.cloneLikeName},'%')
            </if>
            <if test="condition.description != null">
                and description = #{condition.description}
            </if>
            <if test="condition.href != null">
                and href = #{condition.href}
            </if>
            <if test="condition.publisher != null">
                and publisher = #{condition.publisher}
            </if>
            <if test="condition.publishStatus != null">
                and publish_status = #{condition.publishStatus}
            </if>
            <if test="condition.clonedId != null">
                and cloned_id = #{condition.clonedId}
            </if>
            <if test="condition.clonedName != null">
                and cloned_name = #{condition.clonedName}
            </if>
            <if test="condition.clonedDt != null">
                and cloned_dt = #{condition.clonedDt}
            </if>
            <if test="condition.orgSid != null">
                and org_sid = #{condition.orgSid}
            </if>
            <if test="condition.createdBy != null">
                and created_by = #{condition.createdBy}
            </if>
            <if test="condition.createdDt != null">
                and created_dt = #{condition.createdDt}
            </if>
            <if test="condition.updatedBy != null">
                and updated_by = #{condition.updatedBy}
            </if>
            <if test="condition.updatedDt != null">
                and updated_dt = #{condition.updatedDt}
            </if>
            <if test="condition.version != null">
                and version = #{condition.version}
            </if>
            <if test="condition.templateClass != null">
                and template_class = #{condition.templateClass}
            </if>
            <if test="condition.envConifg != null">
                and env_config = #{condition.envConifg}
            </if>
            <if test="condition.serverTemplateIds != null">
                and id in
                <foreach collection="condition.serverTemplateIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.fixed != null">
                and fixed = #{condition.fixed}
            </if>
        </trim>
    </sql>
    <sql id="Example_Where_Clause_Count">
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.name != null">
                and A.name = #{condition.name}
            </if>
            <if test="condition.cloneLikeName != null">
                and A.name like CONCAT(#{condition.cloneLikeName},'%')
            </if>
            <if test="condition.description != null">
                and A.description = #{condition.description}
            </if>
            <if test="condition.href != null">
                and A.href = #{condition.href}
            </if>
            <if test="condition.publisher != null">
                and A.publisher = #{condition.publisher}
            </if>
            <if test="condition.publishStatus != null">
                and A.publish_status = #{condition.publishStatus}
            </if>
            <if test="condition.clonedId != null">
                and A.cloned_id = #{condition.clonedId}
            </if>
            <if test="condition.clonedName != null">
                and A.cloned_name = #{condition.clonedName}
            </if>
            <if test="condition.clonedDt != null">
                and A.cloned_dt = #{condition.clonedDt}
            </if>
            <if test="condition.orgSid != null">
                and A.org_sid = #{condition.orgSid}
            </if>
            <if test="condition.createdBy != null">
                and A.created_by = #{condition.createdBy}
            </if>
            <if test="condition.createdDt != null">
                and A.created_dt = #{condition.createdDt}
            </if>
            <if test="condition.updatedBy != null">
                and A.updated_by = #{condition.updatedBy}
            </if>
            <if test="condition.updatedDt != null">
                and A.updated_dt = #{condition.updatedDt}
            </if>
            <if test="condition.version != null">
                and A.version = #{condition.version}
            </if>
            <if test="condition.templateClass != null">
                and A.template_class = #{condition.templateClass}
            </if>
            <if test="condition.envConifg != null">
                and A.env_config = #{condition.envConifg}
            </if>
            <if test="condition.serverTemplateIds != null">
                and A.id in
                <foreach collection="condition.serverTemplateIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.fixed != null">
                and A.fixed = #{condition.fixed}
            </if>
        </trim>
    </sql>
    <sql id="Base_Column_List">
        id, name, description, href, publisher, publish_status, cloned_id,
        cloned_name, cloned_dt, org_sid, created_by, created_dt, updated_by,
        updated_dt,
        version, published_id, env_config, fixed, `type`
    </sql>
    <sql id="Base_Column_List_With_Alias">
        A.id, A.name, A.description, A.href, A.publisher, A.publish_status, A.cloned_id,
        A.cloned_name, A.cloned_dt, A.org_sid, A.created_by, A.created_dt,
        A.updated_by, A.updated_dt,
        A.version, A.env_config, A.fixed, A.`type`
    </sql>
    <select id="getServerTemplateById" resultMap="ServerTemplateDetailMap">
        select
        A.id,
        A.name,
        A.template_class,
        A.description,
        A.href,
        H.version_name,
        A.publish_status,
        A.publisher,
        A.updated_dt,
        A.updated_by,
        A.env_config,
        A.fixed,
        A.type,
        json_extract(A.env_config, '$**.cloudEnvType') AS cloud_env_types,
        json_extract(A.env_config, '$**.cloudEnvName') AS cloud_env_names,
        H.main_template_id
        from server_template A
        <if test="isHead == true">
            INNER JOIN server_template_revision H on(A.id = H.main_template_id and A.id =
            H.server_template_id)
        </if>
        <if test="isHead == false">
            INNER JOIN server_template_revision H on(A.id = H.server_template_id)
        </if>

        <trim prefix="where" prefixOverrides="and|or">
            <if test="id != null">
                and A.id = #{id}
            </if>
        </trim>
        GROUP BY A.id
    </select>

    <select id="getEnvListByTemplateId" resultType="java.util.HashMap">
        select
        DISTINCT
        A.id as envId,
        A.cloud_env_name as envName,
        A.cloud_env_type as envType
        from cloud_env A
        INNER JOIN cloud_env_account B on B.id = A.cloud_env_account_id
        INNER JOIN server_template C on JSON_CONTAINS(C.env_config->'$[*].cloudEnvId', CONCAT('', A.id, ''), '$')
        where C.id = #{serverTemplateId}
    </select>

  <select id="getEnvTypesByTemplateId" resultType="cn.com.cloudstar.rightcloud.core.pojo.dto.env.CloudEnv">
        select
        DISTINCT
        A.id,
        A.cloud_env_name as cloudEnvName,
        A.cloud_env_type as cloudEnvType
        from cloud_env A
        INNER JOIN cloud_env_account B on B.id = A.cloud_env_account_id
        INNER JOIN server_template C on JSON_CONTAINS(C.env_config->'$[*].cloudEnvId', CONCAT('', A.id, ''), '$')
        where A.id = #{cloudEnvId}
    </select>



    <select id="getServerTemplates" resultMap="ServerTemplateListMap"
            parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria">
        select
        A.id,
        A.name,
        A.template_class,
        A.description,
        A.publish_status,
        A.publisher,
        A.updated_dt,
        A.fixed,
        ifnull(json_extract(D.env_config, '$**.cloudEnvName'), json_extract(A.env_config, '$**.cloudEnvName')) AS cloud_env_names,
        json_extract(D.env_config, '$**.cloudEnvType') AS cloud_env_types,
        H.org_name,
        T.version_name as latest_history
        from server_template A
        INNER JOIN server_template_revision G on A.id = G.main_template_id
        LEFT JOIN sys_m_org H on(A.org_sid = H.org_sid)
        LEFT JOIN (
        SELECT
        A.server_template_id,
        A.version_name,
        A.main_template_id
        FROM
        server_template_revision A
        WHERE
        server_template_id IN (
        SELECT
        MAX(server_template_id)
        FROM
        server_template_revision
        GROUP BY
        main_template_id
        )) T on T.main_template_id = A.id
        LEFT JOIN server_template D ON D.id = T.server_template_id
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.templateNameLike != null">
                and A.name like concat('%',#{condition.templateNameLike}, '%')
            </if>
            <if test="condition.type != null">
                and A.type = #{condition.type}
            </if>
        </trim>
        group by A.id
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>

    </select>

    <select id="getServerTemplatesByProjectId" resultMap="ServerTemplateListMap"
            parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria">
        select
        A.id,
        A.name,
        A.template_class,
        A.description,
        G.version_name,
        A.publish_status,
        A.publisher,
        A.updated_dt,
        H.org_name,
        GROUP_CONCAT(F.cloud_env_name) as cloud_env_names,
        GROUP_CONCAT(F.cloud_env_type) as cloud_env_types
        from server_template A
        LEFT JOIN (
        select A.id,
        A.cloud_env_account_id,
        A.cloud_env_name,
        A.cloud_env_type,
        B.org_sid as belongs_to
        from cloud_env A
        INNER JOIN cloud_env_account B on(A.cloud_env_account_id = B.id and B.org_sid =
        #{condition.orgSid})
        LEFT JOIN cloud_env_alloc cea ON(cea.cloud_env_id = a.id)
        WHERE cea.alloc_target_id = #{condition.projectId}
        ) F on(F.id = E.cloud_env_id and F.belongs_to = A.org_sid)
        INNER JOIN server_template_revision G on(A.id = G.main_template_id and A.id =
        G.server_template_id)
        LEFT join sys_m_org H on(A.org_sid = H.org_sid)
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.orgSid != null">
                and A.org_sid = #{condition.orgSid}
            </if>
            <if test="condition.templateNameLike != null">
                and A.name like concat('%',#{condition.templateNameLike}, '%')
            </if>
        </trim>
        GROUP BY A.id

    </select>

    <select id="countServerTemplates" resultType="java.lang.Integer"
            parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria">
        select count(swq.id) FROM
        (
        select
        A.id, A.org_sid,
        A.created_by
        from server_template A
        INNER JOIN server_template_revision G on A.id = G.main_template_id
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.templateNameLike != null">
                and A.name like concat('%',#{condition.templateNameLike}, '%')
            </if>
        </trim>
        GROUP BY A.id
        ) swq

    </select>

    <select id="countByParamsWithoutDf" resultType="java.lang.Integer"
        parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria">
        SELECT
        COUNT(A.id)
        FROM server_template A
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.templateName != null">
                AND A.name = #{condition.templateName}
            </if>
            <if test="condition.mainOrgSid != null">
                AND EXISTS (
                SELECT 1
                FROM sys_m_org C
                WHERE A.org_sid = C.org_sid
                AND C.org_sid = #{condition.mainOrgSid} OR C.tree_path LIKE CONCAT(#{condition.mainOrgSid}, '%')
                )
            </if>
        </trim>

    </select>
    <!-- 根据条件查询主机模板 -->
    <select id="selectServerTemplateByCondition" resultMap="BaseResultMap"
            parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria">
        select
        DISTINCT
        A.id,
        A.name,
        A.description,
        A.href,
        A.publisher,
        A.publish_status,
        A.cloned_id,
        A.cloned_name,
        A.cloned_dt,
        A.org_sid,
        A.created_by,
        A.created_dt,
        A.updated_by,
        A.updated_dt,
        A.env_config,
        G.server_template_id,
        G.version_name,
        G.created_dt as version_updated_dt,
        G.commit_comments,
        G.id as unique_id,
        B.env_config as rev_env_config,
        B.updated_by as version_updated_user
        from server_template A
        INNER JOIN server_template_revision G on(A.id = G.main_template_id and G.version_name != 'head')
        INNER JOIN server_template B on G.server_template_id = B.id
--         改为 dubbo 查询
--         INNER JOIN cloud_env ce ON JSON_CONTAINS(B.env_config -> '$[*].cloudEnvId', CONCAT('', ce.id, ''), '$')
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.templateNameLike != null">
                and A.name like concat('%',#{condition.templateNameLike}, '%')
            </if>
            <if test="condition.resourceType != null">
                and A.type = #{condition.resourceType}
            </if>
        </trim>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <!-- 根据云环境ID查询主机模板 -->
    <select id="selectServerTemplateByEnvId" resultMap="BaseResultMap">
        select
        DISTINCT
        A.id,
        A.name,
        A.description,
        A.href,
        A.publisher,
        A.publish_status,
        A.cloned_id,
        A.cloned_name,
        A.cloned_dt,
        A.org_sid,
        A.created_by,
        A.created_dt,
        A.updated_by,
        A.updated_dt,
        A.env_config,
        B.server_template_id,
        B.version_name,
        C.env_config as rev_env_config,
        C.description as rev_description,
        B.commit_comments
        from server_template A
        INNER JOIN server_template_revision B on(A.id = B.main_template_id and B.version_name != 'HEAD')
        INNER JOIN server_template C on B.server_template_id  = C.id
        INNER JOIN cloud_env D on JSON_CONTAINS(C.env_config->'$[*].cloudEnvId', CONCAT('', D.id, ''), '$')
        INNER JOIN cloud_env_account F on D.cloud_env_account_id = F.id
        <where>
            <if test="envId != null">
                and D.id = #{envId}
            </if>
            <if test="fixed != null">
                and A.fixed = #{fixed}
            </if>
        </where>
    </select>

    <select id="selectServerTemplateEnvs" resultType="map">
        SELECT DISTINCT
        F.id as envId,
        F.cloud_env_name as envName,
        F.region,
        F.cloud_env_type as envType
        FROM
        server_template A
        INNER JOIN cloud_env F on JSON_CONTAINS(A.env_config->'$[*].cloudEnvId', CONCAT('', F.id, ''), '$')
        INNER JOIN cloud_env_account cea on F.cloud_env_account_id = cea.id
        INNER JOIN server_template_revision G ON A.id = G.main_template_id AND A.id =
        G.server_template_id
        <where>
        <if test="envIds != null">
            and F.id IN
            <foreach collection="envIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        </where>
    </select>

    <select id="selectByParams" resultMap="BaseResultMap"
            parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from server_template
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>

    </select>
    <select id="selectByParamsWithFilter" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from server_template
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from server_template
        where id = #{id}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from server_template
        where id = #{id}
    </delete>
    <delete id="deleteByParams" parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria">
        delete from server_template
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" useGeneratedKeys="true" keyProperty="id"
            parameterType="cn.com.cloudstar.rightcloud.core.pojo.dto.st.ServerTemplate">
        insert into server_template (name, description, href, publisher, publish_status, cloned_id,
        cloned_name,
        cloned_dt, org_sid, created_by,
        created_dt, updated_by, updated_dt, version,template_class, env_config, fixed)
        values (#{name}, #{description}, #{href}, #{publisher}, #{publishStatus}, #{clonedId},
        #{clonedName},
        #{clonedDt}, #{orgSid}, #{createdBy},
        #{createdDt}, #{updatedBy}, #{updatedDt}, #{version},#{templateClass}, #{envConfig}, #{fixed})
    </insert>
    <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id"
            parameterType="cn.com.cloudstar.rightcloud.core.pojo.dto.st.ServerTemplate">
        insert into server_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">
                name,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="href != null">
                href,
            </if>
            <if test="publisher != null">
                publisher,
            </if>
            <if test="publishStatus != null">
                publish_status,
            </if>
            <if test="clonedId != null">
                cloned_id,
            </if>
            <if test="clonedName != null">
                cloned_name,
            </if>
            <if test="clonedDt != null">
                cloned_dt,
            </if>
            <if test="orgSid != null">
                org_sid,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="createdDt != null">
                created_dt,
            </if>
            <if test="updatedBy != null">
                updated_by,
            </if>
            <if test="updatedDt != null">
                updated_dt,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="templateClass != null">
                template_class,
            </if>
            <if test="envConfig != null">
                env_config,
            </if>
            <if test="fixed != null">
                fixed,
            </if>
            <if test="type != null">
                type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">
                #{name},
            </if>
            <if test="description != null">
                #{description},
            </if>
            <if test="href != null">
                #{href},
            </if>
            <if test="publisher != null">
                #{publisher},
            </if>
            <if test="publishStatus != null">
                #{publishStatus},
            </if>
            <if test="clonedId != null">
                #{clonedId},
            </if>
            <if test="clonedName != null">
                #{clonedName},
            </if>
            <if test="clonedDt != null">
                #{clonedDt},
            </if>
            <if test="orgSid != null">
                #{orgSid},
            </if>
            <if test="createdBy != null">
                #{createdBy},
            </if>
            <if test="createdDt != null">
                #{createdDt},
            </if>
            <if test="updatedBy != null">
                #{updatedBy},
            </if>
            <if test="updatedDt != null">
                #{updatedDt},
            </if>
            <if test="version != null">
                #{version},
            </if>
            <if test="templateClass != null">
                #{templateClass},
            </if>
            <if test="envConfig != null">
                #{envConfig},
            </if>
            <if test="fixed != null">
                #{fixed},
            </if>
            <if test="type != null">
                #{type},
            </if>
        </trim>
    </insert>
    <select id="countByParams" parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria"
            resultType="java.lang.Integer">
        select count(0) from (select DISTINCT A.id, A.org_sid from server_template A
        INNER JOIN server_template_revision G ON A.id = G.main_template_id
        <if test="_parameter != null">
            <include refid="Example_Where_Clause_Count"/>
        </if>
        group by A.id) table_count
    </select>
    <select id="selectServerTemplateHosts" parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria"
            resultType="java.util.HashMap">
        SELECT
        A.ID as id,
        A.INSTANCE_NAME as instanceName,
        A.STATUS as status,
        A.STATUS_INFO as statusInfo,
        B.CODE_DISPLAY as statusName,
        C.version_name as versionName,
        D.name as cloudDeployName,
        A.created_dt as createdDt
        from res_vm A
        left join cloud_deployment_vm SDRH on SDRH.res_vm_id = A.id
        LEFT JOIN SYS_M_CODE B on (B.CODE_CATEGORY = 'RES_VM_STATUS' and B.CODE_VALUE = A.STATUS)
        INNER JOIN server_template_revision C on C.server_template_id = A.server_template_id
        LEFT JOIN cloud_deployment D on SDRH.deployment_id = D.id
        WHERE (C.main_template_id = #{condition.id} or A.server_template_id = #{condition.id})
        and A.status != 'deleted'
        <if test="condition.serverNameLike != null">
          and A.INSTANCE_NAME like concat('%', #{condition.serverNameLike},'%')
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>

    <select id="selectResourceTemplateResVds" parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria"
        resultType="java.util.HashMap">
        SELECT
        A.res_vd_sid as id,
        A.vd_name as resourceName,
        A.STATUS as status,
        B.CODE_DISPLAY as statusName,
        C.version_name as versionName,
        A.created_dt as createdDt
        from res_vd A
        LEFT JOIN SYS_M_CODE B on (B.CODE_CATEGORY = 'RES_VD_STATUS' and B.CODE_VALUE = A.STATUS)
        INNER JOIN server_template_revision C on C.server_template_id = A.resource_template_id
        WHERE (C.main_template_id = #{condition.id} or A.resource_template_id = #{condition.id})
        and A.status != 'deleted'
        <if test="condition.resourceNameLike != null">
            and A.vd_name like concat('%', #{condition.resourceNameLike},'%')
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>

    <select id="selectResourceTemplateResFloatingIps" parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria"
        resultType="java.util.HashMap">
        SELECT
        A.id as id,
        A.ip as resourceName,
        A.STATUS as status,
        B.CODE_DISPLAY as statusName,
        C.version_name as versionName,
        A.created_dt as createdDt
        from res_floating_ip A
        <!--浮动ip状态包括在硬盘状态中-->
        LEFT JOIN SYS_M_CODE B on (B.CODE_CATEGORY = 'RES_VD_STATUS' and B.CODE_VALUE = A.STATUS)
        INNER JOIN server_template_revision C on C.server_template_id = A.resource_template_id
        WHERE (C.main_template_id = #{condition.id} or A.resource_template_id = #{condition.id})
        and A.status != 'deleted'
        <if test="condition.resourceNameLike != null">
            and A.ip like concat('%', #{condition.resourceNameLike},'%')
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>

    <select id="countServerTemplateHosts" resultType="integer">
        SELECT
        count(*)
        from res_vm A
        INNER JOIN server_template_revision B on B.server_template_id = A.server_template_id
        WHERE B.main_template_id = #{serverTemplateId} or A.server_template_id = #{serverTemplateId}
        and a.status != 'deleted'
    </select>
    <update id="updateByParamsSelective" parameterType="map">
        update server_template
        <set>
            <if test="record.id != null">
                id = #{record.id},
            </if>
            <if test="record.name != null">
                name = #{record.name},
            </if>
            <if test="record.description != null">
                description = #{record.description},
            </if>
            <if test="record.href != null">
                href = #{record.href},
            </if>
            <if test="record.publisher != null">
                publisher = #{record.publisher},
            </if>
            <if test="record.publishStatus != null">
                publish_status = #{record.publishStatus},
            </if>
            <if test="record.clonedId != null">
                cloned_id = #{record.clonedId},
            </if>
            <if test="record.clonedName != null">
                cloned_name = #{record.clonedName},
            </if>
            <if test="record.clonedDt != null">
                cloned_dt = #{record.clonedDt},
            </if>
            <if test="record.orgSid != null">
                org_sid = #{record.orgSid},
            </if>
            <if test="record.createdBy != null">
                created_by = #{record.createdBy},
            </if>
            <if test="record.createdDt != null">
                created_dt = #{record.createdDt},
            </if>
            <if test="record.updatedBy != null">
                updated_by = #{record.updatedBy},
            </if>
            <if test="record.updatedDt != null">
                updated_dt = #{record.updatedDt},
            </if>
            <if test="record.version != null">
                version = #{record.version},
            </if>
            <if test="record.envConfig != null">
                env_config = #{record.envConfig},
            </if>
            <if test="record.fixed != null">
                fixed = #{record.fixed},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByParams" parameterType="map">
        update server_template
        set id = #{record.id},
        name = #{record.name},
        description = #{record.description},
        href = #{record.href},
        publisher = #{record.publisher},
        publish_status = #{record.publishStatus},
        cloned_id = #{record.clonedId},
        cloned_name = #{record.clonedName},
        cloned_dt = #{record.clonedDt},
        org_sid = #{record.orgSid},
        created_by = #{record.createdBy},
        created_dt = #{record.createdDt},
        updated_by = #{record.updatedBy},
        updated_dt = #{record.updatedDt},
        version = #{record.version},
        env_config = #{record.envConfig},
        fixed = #{record.fixed}
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective"
            parameterType="cn.com.cloudstar.rightcloud.core.pojo.dto.st.ServerTemplate">
        update server_template
        <set>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="description != null">
                description = #{description},
            </if>
            <if test="href != null">
                href = #{href},
            </if>
            <if test="publisher != null">
                publisher = #{publisher},
            </if>
            <if test="publishStatus != null">
                publish_status = #{publishStatus},
            </if>
            <if test="clonedId != null">
                cloned_id = #{clonedId},
            </if>
            <if test="clonedName != null">
                cloned_name = #{clonedName},
            </if>
            <if test="clonedDt != null">
                cloned_dt = #{clonedDt},
            </if>
            <if test="orgSid != null">
                org_sid = #{orgSid},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy},
            </if>
            <if test="createdDt != null">
                created_dt = #{createdDt},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy},
            </if>
            <if test="updatedDt != null">
                updated_dt = #{updatedDt},
            </if>
            <if test="version != null">
                version = #{version},
            </if>
            <if test="publishedId != null">
                published_id = #{publishedId},
            </if>
            <if test="envConfig != null">
                env_config = #{envConfig},
            </if>
            <if test="fixed != null">
                fixed = #{fixed},
            </if>
        </set>
        where id = #{id}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="cn.com.cloudstar.rightcloud.core.pojo.dto.st.ServerTemplate">
        update server_template
        set name = #{name},
        description = #{description},
        href = #{href},
        publisher = #{publisher},
        publish_status = #{publishStatus},
        cloned_id = #{clonedId},
        cloned_name = #{clonedName},
        cloned_dt = #{clonedDt},
        org_sid = #{orgSid},
        created_by = #{createdBy},
        created_dt = #{createdDt},
        updated_by = #{updatedBy},
        updated_dt = #{updatedDt},
        version = #{version},
        env_config = #{envConfig},
        fixed = #{fixed}
        where id = #{id}
    </update>
    <select id="getServerTemplatesWithRevision" resultMap="ServerTemplateListMapWithRevison">
        SELECT
            A.id,A.name,A.description,A.href,A.publisher,A.publish_status,A.cloned_id,
            A.cloned_name,A.cloned_dt,A.org_sid,A.created_by,A.created_dt,A.updated_by,
            A.updated_dt,A.published_id,A.template_class,A.env_config,H.org_name,
            GROUP_CONCAT(ce.cloud_env_name) AS cloud_env_names,
            GROUP_CONCAT(ce.cloud_env_type) AS cloud_env_types,
            G.id as revision_id,G.main_template_id,G.server_template_id,
            G.commit_comments,G.version_name,G.created_dt as rev_created_at
        FROM server_template A
        INNER JOIN cloud_env ce ON JSON_CONTAINS (
            A.env_config -> '$[*].cloudEnvId',
            CONCAT('', ce.id, ''),
            '$')
        INNER JOIN cloud_env_account cea ON ce.cloud_env_account_id = cea.id
        INNER JOIN server_template_revision G ON A.id = G.server_template_id and G.version_name != 'HEAD'
        AND A.id = G.server_template_id
        LEFT JOIN sys_m_org H ON A.org_sid = H.org_sid
        GROUP BY A.id;
    </select>
    <select id="getServiceByTemplateId" resultType="java.util.HashMap"
            parameterType="cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria">
        SELECT
        A.id,
        A.service_name as serviceName,
        A.status,
        A.created_dt as createdDt,
        C.version_name as versionName
        FROM sf_service_category A
        INNER JOIN sf_service_category_additional B on A.id = B.service_id
        INNER JOIN server_template_revision C on C.server_template_id = B.template_rev_id
        where A.status != 'delete'
        <if test="condition.serviceNameLike != null">
            and A.service_name like concat('%', #{condition.serviceNameLike},'%')
        </if>
        <if test="condition.serviceFrom != null">
            and A.service_form = #{condition.serviceFrom}
        </if>
        <if test="condition.serverTemplateId != null">
            and (C.main_template_id = #{condition.serverTemplateId} or C.server_template_id = #{condition.serverTemplateId})
        </if>
        group by A.id
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
</mapper>
