package cn.com.cloudstar.rightcloud.oss.module.account.bean.response.user;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(description = "查询运营实体分销商数据")
@Data
public class DescribeEntityDistributorResponse {
    /**
     * id
     */
    @ApiModelProperty("分销商id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 名称
     */
    @ApiModelProperty("分销商名称")
    private String name;
}
