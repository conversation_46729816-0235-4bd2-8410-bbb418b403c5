/*
 * Copyright (c) 2018 Cloud-Star, Inc. All Rights Reserved.
 */

package cn.com.cloudstar.rightcloud.oss.module.account.service.org.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Strings;
import com.google.common.collect.HashMultimap;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.ThreadFactoryBuilder;

import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ClassUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.ReflectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.FileNotFoundException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.CharUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import feign.RetryableException;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.ResCloudEnv;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule;
import cn.com.cloudstar.rightcloud.common.constants.NotificationConsts;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.pojo.ValidationResult;
import cn.com.cloudstar.rightcloud.common.util.CrytoUtilSimple;
import cn.com.cloudstar.rightcloud.common.util.MapsKit;
import cn.com.cloudstar.rightcloud.common.util.ValidationUtils;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.common.util.excel.ExcelUtil;
import cn.com.cloudstar.rightcloud.common.util.excel.service.ExcelImportService;
import cn.com.cloudstar.rightcloud.common.util.json.JsonUtil;
import cn.com.cloudstar.rightcloud.core.pojo.constant.SysMNotifyConfigConstant.StatusEnum;
import cn.com.cloudstar.rightcloud.core.pojo.dto.charge.BizBillingAccount;
import cn.com.cloudstar.rightcloud.core.pojo.dto.charge.BizBillingAccountDTO;
import cn.com.cloudstar.rightcloud.core.pojo.dto.sfs.ServiceCategory;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.SysConfig;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.SysMgtObjQuota;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.SysQuota;
import cn.com.cloudstar.rightcloud.core.pojo.dto.tag.CloudTag;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.Company;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.LdapUserDTO;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.Org;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.OrgTree;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.Project;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.Role;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.RoleModule;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.User;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.UserExceptionResource;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.UserJoinTargetDTO;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.UserOrg;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.UserRole;
import cn.com.cloudstar.rightcloud.core.pojo.vo.user.UserVo;
import cn.com.cloudstar.rightcloud.module.support.access.constants.DataScopeEnum;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicInfoUtil;
import cn.com.cloudstar.rightcloud.module.support.file.storage.bean.enums.StoragePathEnum;
import cn.com.cloudstar.rightcloud.module.support.file.storage.bean.vo.StorageResult;
import cn.com.cloudstar.rightcloud.module.support.file.storage.service.StorageService;
import cn.com.cloudstar.rightcloud.oss.common.cache.JedisUtil;
import cn.com.cloudstar.rightcloud.oss.common.constants.AuthConstants;
import cn.com.cloudstar.rightcloud.oss.common.constants.Constants;
import cn.com.cloudstar.rightcloud.oss.common.constants.ModuleTypeConstants;
import cn.com.cloudstar.rightcloud.oss.common.constants.RestConst;
import cn.com.cloudstar.rightcloud.oss.common.constants.WebConstants;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.OrgType;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.USER_AUTH_TYPE;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.UserType;
import cn.com.cloudstar.rightcloud.oss.common.enums.BusinessTagEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.FileTypeCodeEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.RoleEnum;
import cn.com.cloudstar.rightcloud.oss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.oss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.oss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.DataScopeUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.NoUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.WebUserUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.cache.AuthUserHolder;
import cn.com.cloudstar.rightcloud.oss.module.access.dao.SysGroupMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.model.BizDistributor;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.model.CreateUserVO;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.model.ExtVO;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.org.CreateBillingAccountRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.org.CreateUserAdminRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.org.UpdateAccountRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.org.UpdateBmsRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.org.UserExceptionResourceRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user.ActivationForm;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user.CreateUsersRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.template.BillingAccountTemplate;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.template.ImportCustomerRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.template.SubUserTemplate;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.org.OrgMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.user.RoleMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.user.RoleModuleMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.user.UserMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.user.UserOrgMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.user.UserRoleMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.enums.IamDomainPermissionsEnum;
import cn.com.cloudstar.rightcloud.oss.module.account.service.IBizDistributorService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.org.OrgService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.project.ProjectService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.role.RoleOrgService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.role.RoleService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.user.IamPermissionService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.user.LdapService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.user.UserFeginService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.user.UserService;
import cn.com.cloudstar.rightcloud.oss.module.bigscrean.dao.BizUserAiBmsRecordMapper;
import cn.com.cloudstar.rightcloud.oss.module.export.bean.BizDownload;
import cn.com.cloudstar.rightcloud.oss.module.export.bean.BizDownloadExtend;
import cn.com.cloudstar.rightcloud.oss.module.export.dao.BizDownloadExtentMapper;
import cn.com.cloudstar.rightcloud.oss.module.export.dao.BizDownloadMapper;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.DescribeBizBillingStrategyServingRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.SysOrgCreateBatchFeignForm;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.SysOrgDeleteForm;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.SysOrgUpdateForm;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.response.DescribeBillingStrategyServingSimpleResponse;
import cn.com.cloudstar.rightcloud.oss.module.feign.service.FeignService;
import cn.com.cloudstar.rightcloud.oss.module.feign.service.ResourceDcFeignService;
import cn.com.cloudstar.rightcloud.oss.module.feign.service.SystemFeignService;
import cn.com.cloudstar.rightcloud.oss.module.maintenance.dao.tag.CloudTagMapper;
import cn.com.cloudstar.rightcloud.oss.module.pricing.dao.priceconfig.BizBillingAccountMapper;
import cn.com.cloudstar.rightcloud.oss.module.pricing.dao.priceconfig.UserExceptionResourceMapper;
import cn.com.cloudstar.rightcloud.oss.module.pricing.service.priceconfig.BizBillingAccountService;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.dao.ServiceCategoryMapper;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.dao.SfProductResourceMapper;
import cn.com.cloudstar.rightcloud.oss.module.system.dao.config.SysConfigMapper;
import cn.com.cloudstar.rightcloud.oss.module.system.dao.config.SysMgtObjQuotaMapper;
import cn.com.cloudstar.rightcloud.oss.module.system.dao.config.SysQuotaMapper;
import cn.com.cloudstar.rightcloud.oss.module.system.service.config.SysConfigService;
import cn.com.cloudstar.rightcloud.oss.module.system.service.message.BusinessNotificationService;
import cn.com.cloudstar.rightcloud.oss.module.tcc.service.CancelService;
import cn.com.cloudstar.rightcloud.remote.api.iam.pojo.HcsoUser;
import cn.com.cloudstar.rightcloud.remote.api.iam.pojo.IamOrg;
import cn.com.cloudstar.rightcloud.remote.api.iam.pojo.IamUser;
import cn.com.cloudstar.rightcloud.remote.api.iam.service.HcsoUserRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.iam.service.IamRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.user.ResUser;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.user.ResUserRemoteService;

/**
 * OrgServiceImpl
 *
 * <AUTHOR>
 * @date 2018/1/25.
 */
@Component
@Slf4j
public class OrgServiceImpl implements OrgService {

    private static final Logger logger = LoggerFactory.getLogger(OrgServiceImpl.class);
    private static final List<String> SHOW_DATA_SCOPE = Arrays.asList("company", "project");
    private static final String PARENT_ORG_SID_PATH = "/%s/";


    private static final String USER_MA_BMS_UP = "userBmsUp";

    public static final String USER_MA_BMS_UP_LOCK = "userBmsUpLock";

    /**
     * exchange
     */
    private static final String ACCOUNT_FREEZE_PUSH_EXCHANGE = "account-freeze-push-exchange";

    /**
     * routingKey
     */
    private static final String ACCOUNT_FREEZE_PUSH_MONITOR_FREEZE = "account.freeze.push.monitor.freeze";

    private static final Integer MAX_THREAD_NUM = 8;

    @Value("${hcsoFlg:true}")
    private Boolean hcsoFlg;


    enum GroupIdEnum {
        // 管理员组
        ADMIN_GROUP("ADMIN_GROUP", 1L),
        // 资源管理员组
        RESOURCE_GROUP("RESOURCE_GROUP", 2L),
        // 项目管理员组
        PROJECT_GROUP("PROJECT_GROUP", 3L),
        // 项目成员组
        PROJECT_MEMBER_GROUP("PROJECT_MEMBER_GROUP", 4L),
        // 只读成员组
        READ_ONLY_GROUP("READ_ONLY_GROUP", 5L);
        private final String name;
        private final Long id;

        GroupIdEnum(String name, Long id) {
            this.name = name;
            this.id = id;
        }

        private String getName(){
           return name;
        }

        private Long getId() {
            return id;
        }

        public static Long getIdByName(String name) {
            for (GroupIdEnum groupIdEnum : GroupIdEnum.values()) {
                if (name.equals(groupIdEnum.getName())) {
                    return groupIdEnum.getId();
                }
            }
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_276090791));
        }
    }

    @Autowired
    private OrgMapper orgMapper;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private SysConfigMapper sysConfigMapper;

    @Autowired()
    @Lazy
    private StringRedisTemplate redisTemplate;

    @Autowired
    private RoleModuleMapper roleModuleMapper;

    @Autowired
    private RoleOrgService roleOrgService;

    @Autowired
    private UserRoleMapper userRoleMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private ServiceCategoryMapper serviceCategoryMapper;

    @Autowired
    private UserOrgMapper userOrgMapper;

    @Autowired
    @Lazy
    private ProjectService projectService;

    @Autowired
    private RoleService roleService;

    @Autowired
    @Lazy
    private UserService userService;

    @Autowired
    private SysConfigService sysConfigService;

    @Autowired
    private CloudTagMapper cloudTagMapper;

    @Autowired
    private SysQuotaMapper sysQuotaMapper;

    @Autowired
    private SysMgtObjQuotaMapper sysMgtObjQuotaMapper;

    @DubboReference
    private IamRemoteService iamRemoteService;

    @Autowired
    @Lazy
    private BusinessNotificationService businessNotificationService;

    @Autowired
    private CancelService cancelService;

    @Autowired
    private UserFeginService userFeginService;

    @Autowired
    private SysGroupMapper sysGroupMapper;

    @Autowired
    private BizBillingAccountMapper bizBillingAccountMapper;

    @Autowired
    private IBizDistributorService distributorService;

    @Autowired
    private BizBillingAccountService bizBillingAccountService;

    @Autowired
    private RoleMapper roleMapper;

    @Autowired
    private LdapService ldapService;

    @Autowired
    private BizDownloadMapper bizDownloadMapper;

    @Autowired
    private BizDownloadExtentMapper bizDownloadExtentMapper;

    @Autowired
    private IamPermissionService iamPermissionService;
    @Autowired
    private SfProductResourceMapper sfProductResourceMapper;

    @Autowired
    @Qualifier("cloudExecutor")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @DubboReference
    private ResUserRemoteService resUserRemoteService;
    @Autowired
    private StorageService storageService;
    @Autowired
    private BizUserAiBmsRecordMapper bizUserAiBmsRecordMapper;

    @Autowired
    private UserExceptionResourceMapper userExceptionResourceMapper;

    @DubboReference
    private HcsoUserRemoteService hcsoUserRemoteService;

    @Autowired
    private FeignService feignService;

    @Autowired
    private SystemFeignService systemFeignService;

    @Autowired
    private ResourceDcFeignService resourceDcFeignService;

    @Override
    public int countByParams(Criteria example) {
        return this.orgMapper.countByParams(example);
    }

    @Override
    public Org selectByPrimaryKey(Long orgSid) {
        return this.orgMapper.selectByPrimaryKey(orgSid);
    }

    @Override
    public Org selectRootOrg(Long orgSid) {
        if (Objects.isNull(orgSid)) {
            return null;
        }
        Org org = this.selectByPrimaryKey(orgSid);
        if (Objects.isNull(org)) {
            return null;
        }
        if (Objects.isNull(org.getParentId())) {
            return org;
        } else {
            return selectRootOrg(org.getParentId());
        }
    }

    @Override
    public List<Org> selectByParams(Criteria example) {
        return this.orgMapper.selectByParams(example);
    }

    @Override
    public List<Org> selectByParamsV(Criteria criteria) {
        return this.orgMapper.selectByParamsV(criteria);
    }

    @Override
    public List<Long> findChildOrgIdsByTreePath(Long orgSid) {
        Criteria criteria = new Criteria();
        criteria.put("treePath","/"+orgSid+"/");
        return orgMapper.selectSidsByTreePath(criteria);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteByPrimaryKey(Long orgSid) {
        Org org = this.orgMapper.selectByPrimaryKey(orgSid);
        int count = this.orgMapper.deleteByPrimaryKey(orgSid);

        return count;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByPrimaryKeySelective(Org record) {
        return this.orgMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByPrimaryKeySelectiveAndWipeData(Org record) {
        return this.orgMapper.updateByPrimaryKeySelectiveAndWipeData(record);
    }

    @Override
    public int updateByPrimaryKey(Org record) {
        return this.orgMapper.updateByPrimaryKey(record);
    }

    @Override
    public int deleteByParams(Criteria example) {
        return this.orgMapper.deleteByParams(example);
    }

    @Override
    public int updateByParamsSelective(Org record, Criteria example) {
        return this.orgMapper.updateByParamsSelective(record, example.getCondition());
    }

    @Override
    public int updateByParams(Org record, Criteria example) {
        return this.orgMapper.updateByParams(record, example.getCondition());
    }

    @Override
    public int insert(Org record) {
        return this.orgMapper.insert(record);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertSelective(Org record) {
        int count = this.orgMapper.insertSelective(record);

        return count;
    }

    @Override
    public List<Org> findAllOrgByUserSid(Criteria criteria) {
        return orgMapper.findAllOrgByUserSid(criteria);
    }

    /**
     * 组织树
     */
    @Override
    public List<OrgTree> findOrgTree(Long userSid, String type, Boolean style) {
        Criteria criteria = new Criteria("userSid", userSid);
        List<Org> orgList = Lists.newArrayList();
        // 查询当前用户所有关联的组织
        if (null != style && style) {
            orgList = orgMapper.findAllOrgByUserSidWithAuth(criteria);
        } else {
            orgList = orgMapper.findAllOrgByUserSid(criteria);
        }

        // 如果用户关联的组织为空，直接返回
        if (CollectionUtil.isEmpty(orgList)) {
            return new ArrayList<>(1);
        }

        if (null == style || !style) {
            // 获取完整的用户组织树
            List<String> orgIds = new ArrayList<>();
            orgList.stream().forEach(org -> {
                orgIds.add(org.getOrgSid().toString());
                String[] orgSidArr = StringUtils.split(org.getTreePath(), "/");
                orgIds.addAll(Arrays.asList(orgSidArr));
            });

            orgList = orgMapper.listByOrgIds(new Criteria("orgIds", orgIds));
        }

        // 查询用户角色
        List<UserRole> userRoles = userRoleMapper.selectRoleByUserSid(criteria);

        // 构建当前用户组织与角色的map eg: {1: [1,2,3], 2: [1,2,3]} 用户在组织1和组织2下有 角色1，角色2，角色3
        HashMultimap<Long, Long> orgUserMap = HashMultimap.create();
        userRoles.forEach(userRole -> orgUserMap.put(userRole.getOrgSid(), userRole.getRoleSid()));

        List<OrgTree> orgTree = new ArrayList<>();

        SysConfig sysConfig = sysConfigService.findByConfigTypeAndKey("other_config", "org.switch.limit");
        if (sysConfig != null && "part".equalsIgnoreCase(sysConfig.getConfigValue())) {
            // 数据清洗，只留下company和project，重新设定parentId信息 （如果不用只显示企业和项目，跳过此步骤即可构建完整的树）
            clearOrgList(orgList);
        }

        // 构建组织树
        buildTree(orgTree, null, orgList, orgUserMap);
        return orgTree;
    }

    private void clearOrgList(List<Org> orgList) {
        List<Org> orgAll = orgMapper.selectAllOrg();
        Map<Long, Org> orgSidMap = orgAll.stream()
                                         .collect(
                                                 Collectors.toMap(Org::getOrgSid, Function.identity(), (k1, k2) -> k1));

        Iterator<Org> iterator = orgList.iterator();
        while (iterator.hasNext()) {
            Org org = iterator.next();

            // 如果不是company和project类型的组织，不显示
            if (!SHOW_DATA_SCOPE.contains(org.getOrgType())) {
                iterator.remove();

                continue;
            }

            // 修改企业为根节点
            if (OrgType.COMPANY.equals(org.getOrgType())) {
                org.setParentId(null);
            }

            if (!Objects.isNull(org.getParentId()) && !Objects.isNull(orgSidMap.get(org.getParentId()))
                    && !SHOW_DATA_SCOPE.contains(orgSidMap.get(org.getParentId()).getOrgType())) {
                org.setParentId(findParentId(orgSidMap, org.getParentId()));
            }
        }
    }

    private Long findParentId(Map<Long, Org> orgSidMap, Long parentId) {
        if (Objects.isNull(parentId)) {
            return null;
        }
        if (!Objects.isNull(orgSidMap.get(parentId)) && SHOW_DATA_SCOPE.contains(
                orgSidMap.get(parentId).getOrgType())) {
            return parentId;
        }

        return findParentId(orgSidMap, orgSidMap.get(parentId).getParentId());
    }

    private void buildTree(List<OrgTree> orgTree, OrgTree node, List<Org> orgList, HashMultimap<Long, Long> userRoles) {
        if (Objects.isNull(node)) {
            // 构建根节点
            orgTree.addAll(orgList.stream()
                                  .filter(t -> isRoot(t, orgList))
                                  .map(t -> convertOrgToOrgTree(t, userRoles))
                                  .collect(Collectors.toList()));

            for (OrgTree child : orgTree) {
                buildTree(orgTree, child, orgList, userRoles);
            }

        } else {
            // 找到当前节点下的子节点
            List<OrgTree> treeNode = orgList.stream()
                                            .filter(org -> node.getOrgSid().equals(org.getParentId()))
                                            .map(t -> convertOrgToOrgTree(t, userRoles))
                                            .collect(Collectors.toList());

            node.setNode(treeNode);

            for (OrgTree child : treeNode) {
                buildTree(orgTree, child, orgList, userRoles);
            }
        }
    }

    /**
     * 判断当前节点是否是根节点
     */
    private boolean isRoot(Org org, List<Org> orgList) {
        // 父节点ID为空，判断为根节点
        if (Objects.isNull(org.getParentId())) {
            return true;
        }

        // 当前可选择的组织列表中找不到当前节点的父节点
        boolean anyMatch = orgList.stream()
                                  .anyMatch(og -> org.getParentId().equals(og.getOrgSid())
                                          || org.getTreePath().indexOf(String.format(PARENT_ORG_SID_PATH, og.getOrgSid()))
                                          > 0);
        return !anyMatch;
    }

    private OrgTree convertOrgToOrgTree(Org org, HashMultimap<Long, Long> userRoles) {
        OrgTree node = new OrgTree();
        node.setOrgSid(org.getOrgSid());
        node.setOrgName(org.getOrgName());
        node.setOrgType(org.getOrgType());

        boolean match = !CollectionUtils.isEmpty(userRoles.get(org.getOrgSid()));

        node.setPermit(match);

        return node;
    }

    @Override
    public List<Org> selectAllChildOrg(Long parentOrgId) {
        List<Org> list = new ArrayList<>();
        selectAllChildOrgByPid(parentOrgId, list);
        return list;
    }

    private void selectAllChildOrgByPid(Long parentOrgId, List<Org> list) {
        List<Org> childList = this.selectChildOrg(parentOrgId);
        for (Org org : childList) {
            list.add(org);
            List<Org> childList2 = this.selectChildOrg(org.getOrgSid());
            if (childList2 != null && !childList2.isEmpty()) {
                selectAllChildOrgByPid(org.getOrgSid(), list);
            }
        }
    }

    @Override
    public List<Org> selectChildOrg(Long parentOrgId) {
        Criteria criteria = new Criteria();
        criteria.put("parentId", parentOrgId);
        return this.selectByParams(criteria);
    }

    @Override
    public List<Org> findOrgWithoutCompany() {
        List<Org> orgs = orgMapper.findOrgWithoutCompany(RequestContextUtil.getAuthUserInfo().getOrgSid());
        orgs.stream().forEach(org -> {
            User user = userMapper.selectByPrimaryKey(org.getOwner());
            if (Objects.nonNull(user)) {
                org.setOwnerName(user.getAccount());
            }
        });

        return orgs;
    }

    @Override
    public List<Org> selectDepartments() {
        return orgMapper.selectDepartment(RequestContextUtil.getAuthUserInfo().getOrgSid());
    }

    @Override
    public List<Org> selectOrgs() {
        return orgMapper.selectOrgs();
    }

    @Override
    public List<Org> selectWholeOrgs(Criteria criteria) {
        List<Org> orgs = orgMapper.selectWholeOrgs(criteria);
        orgs = filterOrgs(orgs);
        return orgs;
    }

    /**
     * 过滤组织
     */
    private List<Org> filterOrgs(List<Org> orgs) {
        Map<Long, Org> orgIdMap = orgs.stream().collect(Collectors.toMap(Org::getOrgSid, Function.identity(), (k1, k2) -> k1));
        // 过滤没有上级组织的默认项目
        orgs = orgs.stream().filter((orgItem) -> {
            if ("default".equals(orgItem.getOrgCode()) && "project".equals(orgItem.getOrgType())) {
                Long parentId = orgItem.getParentId();
                return !Objects.isNull(parentId) && !Objects.isNull(orgIdMap.get(parentId)) && !"/null/".equals(orgItem.getTreePath());
            }
            return true;
        }).collect(Collectors.toList());
        return orgs;
    }

    /**
     * 将list 转为树结构
     */
    private void listToTree(List<Org> orgs, Org parentOrg) {
        for (Org org : orgs) {
            if (org.getParentId().equals(parentOrg.getOrgSid())) {
                parentOrg.getChildrens().add(org);
                listToTree(orgs, org);
            }
        }
    }

    private List<Role> setRoleStatus(List<UserRole> userRoles, List<Role> roles) {
        // bss运维人员（组织管理员）角色返回
        roles.addAll(BeanConvertUtil.convert(RoleEnum.transferRoleList(), Role.class));

        if (!CollectionUtils.isEmpty(roles)) {
            if (!CollectionUtils.isEmpty(userRoles)) {
                List<Long> roleIds = userRoles.stream().map(UserRole::getRoleSid).collect(Collectors.toList());
                roles.forEach(role -> {
                    if (roleIds.contains(role.getRoleSid())) {
                        role.setStatus("1");
                    } else {
                        role.setStatus("0");
                    }
                });
            } else {
                roles.forEach(role -> role.setStatus("0"));
            }
        }

        // 去重
        Iterator<Role> iterator = roles.iterator();
        Set<Long> roleIds = Sets.newHashSet();
        while (iterator.hasNext()) {
            Role role = iterator.next();
            if (roleIds.contains(role.getRoleSid())) {
                iterator.remove();
                continue;
            }
            roleIds.add(role.getRoleSid());
        }
        return roles;
    }

    @Override
    public List<Org> selectWholeorgsWithRole(Criteria criteria) {
        List<Org> treeOrg = Lists.newArrayList();
        List<Org> orgs = orgMapper.selectWholeorgsWithRole(criteria);
        // 只查询当前用户所属组织
        User user = userMapper.selectByPrimaryKey(Long.valueOf(criteria.get("userSid").toString()));
        if (Objects.isNull(user)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_17529500));
        }
        orgs = orgs.stream()
                   .filter(org -> Objects.equals(org.getOrgSid(), user.getCompanyId()))
                   .collect(Collectors.toList());

        List<UserRole> userRoles = userRoleMapper.selectRoleByUserSid(criteria);

        if (!CollectionUtils.isEmpty(orgs)) {
            Map<Long, List<UserRole>> roleMap = Maps.newHashMap();
            if (!CollectionUtils.isEmpty(userRoles)) {
                roleMap = userRoles.stream().collect(Collectors.groupingBy(UserRole::getOrgSid));
            }
            List<Long> parentIds = orgs.stream().map(Org::getOrgSid).collect(Collectors.toList());
            for (Org org : orgs) {
                org.setRoleList(setRoleStatus(roleMap.get(org.getOrgSid()), org.getRoleList()));
                if (!parentIds.contains(org.getParentId())) {
                    treeOrg.add(org);
                }
            }
            orgs = orgs.stream().filter(org -> null != org.getParentId()).collect(Collectors.toList());
            for (Org org : treeOrg) {
                listToTree(orgs, org);
            }
        }

        return treeOrg;
    }

    @Override
    public List<Org> selectByParamsWithoutAuth(Criteria criteria) {
        return orgMapper.selectByParamsNoAuth(criteria);
    }

    @Override
    public void uniqueNameCheck(String orgName) {
        Criteria criteria = new Criteria();
        criteria.put("orgName", orgName);
        int count = orgMapper.countByParamsWithoutDataFilter(criteria);
        if (count > 0) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_190088809));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int createDepartment(Org org, AuthUser authUser) {

        uniqueNameCheck(org.getOrgName());

        org.setOrgType(OrgType.DEPARTMENT);
        org.setOrgIcon("fa fa-building-o");
        org.setStatus("1");

        Org orgParent = orgMapper.selectByPrimaryKey(org.getParentId());
        if (Objects.isNull(orgParent)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_496009997));
        }

        org.setTreePath(orgParent.getTreePath() + orgParent.getOrgSid() + "/");

        int count = orgMapper.insertSelective(org);

        roleOrgService.insert(org.getOrgSid(), 101L);

        //创建组织时，初始化组织配额
        List<SysQuota> sysQuotas = sysQuotaMapper.selectByParams(new Criteria());
        SysMgtObjQuota sysMgtObjQuota = new SysMgtObjQuota();
        WebUserUtil.prepareInsertParams(sysMgtObjQuota);
        sysMgtObjQuota.setQuotaValue(JsonUtil.toJson(sysQuotas));
        sysMgtObjQuota.setQuotaObj(WebConstants.QUOTA_OBJ_TYPE.QUOTA_DEPARTMENT_TYPE);
        sysMgtObjQuota.setQuotaObjSid(org.getOrgSid());
        sysMgtObjQuotaMapper.insertSelective(sysMgtObjQuota);

        /// 初始化平台标签
        initPlatformClougTags(org, authUser);

        return count;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateDepartment(Org orgUpdate, AuthUser authUser) {

        if (Objects.isNull(orgUpdate.getOrgSid())) {
            return 0;
        }

        Org org = orgMapper.selectByPrimaryKey(orgUpdate.getOrgSid());
        if (Objects.isNull(org)) {
            return 0;
        }
        if (!orgUpdate.getOrgName().equals(org.getOrgName())) {
            uniqueNameCheck(orgUpdate.getOrgName());
        }

        org.setOrgName(orgUpdate.getOrgName());
        org.setOrgCode(orgUpdate.getOrgCode());
        org.setParentId(orgUpdate.getParentId());
        org.setDescription(orgUpdate.getDescription());
        org.setUpdatedBy(authUser.getAccount());
        org.setUpdatedDt(DateTime.now().toDate());
        org.setContactName(orgUpdate.getContactName());
        org.setContactPosition(orgUpdate.getContactPosition());
        org.setContactPhone(orgUpdate.getContactPhone());
        org.setFax(orgUpdate.getFax());
        //初始化数据为空的数据提交
        if (org.getContactName() == null) {
            org.setContactName("");
        }
        if (org.getContactPosition() == null) {
            org.setContactPosition("");
        }
        if (org.getContactPhone() == null) {
            org.setContactPhone("");
        }
        if (org.getFax() == null) {
            org.setFax("");
        }
        if (org.getDescription() == null) {
            org.setDescription("");
        }

        Org orgParent = orgMapper.selectByPrimaryKey(org.getParentId());
        if (Objects.isNull(orgParent)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_496009997));
        }

        org.setTreePath(orgParent.getTreePath() + orgParent.getOrgSid() + "/");

        int count = orgMapper.updateByPrimaryKeySelective(org);

        return count;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteDepartment(Long orgSid, AuthUser authUser) {
        List<Org> childList = orgMapper.selectByParams(new Criteria("parentId", orgSid));
        if (CollectionUtil.isNotEmpty(childList)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_884688910));
        }
        Org org = orgMapper.selectByPrimaryKey(orgSid);
        if (Objects.isNull(org)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1068638748));
        }

        orgMapper.deleteByPrimaryKey(orgSid);
    }

    /**
     * 查询当前组织及其子组织下的用户
     *
     * @param example 保留分页参数
     */
    @Override
    public List<User> departmentUsers(Criteria example) {
        return userMapper.findByOrgIds(example);
    }

    @Override
    public List<User> departmentUsersJoined(Criteria criteria, Long orgSid) {
        criteria.put("orgSid", orgSid);

        return userMapper.findJoinedUserByOrgId(criteria);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createProject(Project project) {
        return projectService.addProject(project);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateProject(Project projectUpdate) {
        return projectService.updateProject(projectUpdate) ? 1 : 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteProject(Long orgSid, AuthUser authUser) {
        projectService.removeProject(Arrays.asList(orgSid));
    }

    @Override
    public void cacheRecentOrg(Long userSid, Long orgSid) {
        Org org = orgMapper.selectByPrimaryKey(orgSid);

        String orgCache = JsonUtil.toJson(
                ImmutableMap.of("orgSid", org.getOrgSid(), "orgName", org.getOrgName(), "time",
                                Calendar.getInstance().getTimeInMillis()));


        JedisUtil.INSTANCE.hset(AuthConstants.CACHE_USER_RECENT_ORG + userSid, orgSid.toString(), orgCache,
                                3600 * 24 * 30);
    }

    @Override
    public List<Long> findChildOrgIds(Long orgSid, List<String> orgTypeIn) {
        Org org = orgMapper.selectByPrimaryKey(orgSid);
        if (Objects.isNull(org)) {
            return Lists.newArrayList();
        }
        // 查询当前组织的所有子组织
        Criteria criteria = new Criteria();
        if (!CollectionUtil.isEmpty(orgTypeIn)) {
            criteria.put("orgTypeIn", orgTypeIn);
        }
        criteria.put("treePath", org.getTreePath() + org.getOrgSid() + "/");

        List<Org> departments = orgMapper.selectByTreePath(criteria);

        return departments.stream().map(Org::getOrgSid).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean joinUserToOrg(UserJoinTargetDTO userJoinTarget) {

        if (Objects.isNull(userJoinTarget)) {
            return true;
        }

        List<Long> userSids = userJoinTarget.getUsers().stream().map(UserVo::getUserSid).collect(Collectors.toList());

        // 删除和新增关联
        List<UserOrg> userOrgs = userOrgMapper.selectByParams(new Criteria("orgSid", userJoinTarget.getOrgSid()));

        if (userOrgs.size() > 0) {
            for (UserOrg userOrg : userOrgs) {
                Criteria criteria = new Criteria();
                criteria.put("orgSid", userOrg.getOrgSid());
                criteria.put("userSid", userOrg.getUserSid());

                userRoleMapper.deleteByParams(criteria);

                userOrgMapper.deleteByPrimaryKey(userOrg);

                // 更新用户所属组织
                User user = userMapper.selectByPrimaryKey(userOrg.getUserSid());
                if (Objects.isNull(user)) {
                    continue;
                }
                if (userJoinTarget.getOrgSid().equals(user.getCompanyId()) && !userSids.contains(user.getUserSid())) {
                    Criteria example = new Criteria("userSid", userOrg.getUserSid());
                    example.put("orgSidNotEqual", userJoinTarget.getOrgSid());
                    List<UserOrg> userOrgList = userOrgMapper.selectByParams(example);

                    Long companyIdNew = CollectionUtils.isEmpty(userOrgList) ? null : userOrgList.get(0).getOrgSid();

                    user.setCompanyId(companyIdNew);
                    userMapper.updateByPrimaryKey(user);
                }
            }
        }

        List<UserRole> userRoles = new ArrayList<>();

        for (UserVo vo : userJoinTarget.getUsers()) {
            UserOrg userOrg = new UserOrg();

            userOrg.setOrgSid(userJoinTarget.getOrgSid());
            userOrg.setUserSid(vo.getUserSid());
            userService.judgeUserNum(userJoinTarget.getOrgSid());
            userOrgMapper.insertSelective(userOrg);
            userService.insertSaasOrgAndRole(vo.getUserSid());

            if (!CollectionUtils.isEmpty(vo.getRoleIds())) {

                for (Long roleSid : vo.getRoleIds()) {
                    UserRole userRole = new UserRole();
                    userRole.setOrgSid(userJoinTarget.getOrgSid());
                    userRole.setUserSid(vo.getUserSid());
                    userRole.setRoleSid(roleSid);
                    userRoles.add(userRole);
                }
            }
        }

        if (!CollectionUtil.isEmpty(userRoles)) {
            userRoleMapper.insertMulti(userRoles);
        }

        // 更新用户所属组织
        List<Long> userIds = userJoinTarget.getUsers().stream().map(UserVo::getUserSid).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(userIds)) {
            // 更新用户表所属组织
            User user = User.builder().companyId(userJoinTarget.getOrgSid()).build();

            userMapper.updateByParamsSelective(user, MapsKit.of("companyIdIsNull", true, "userSidList", userIds));
        }

        return true;
    }

    @Override
    public List<Org> findByUserSid(Criteria criteria) {
        return orgMapper.findByUserSid(criteria);
    }

    @Override
    public List<Org> findAll() {
        return orgMapper.selectAllOrg();
    }

    @Override
    public List<Org> findAllByEntity(Long entityId) {
        return orgMapper.selectAllOrgByEntity(entityId);
    }

    @Override
    public List<Org> selectOrgByCompany() {
        return orgMapper.selectOrgByCompany();
    }

    @Override
    public boolean isDupOrg(Long parentOrgId, Long orgId, String orgName) {
        Criteria criteria = new Criteria();
        criteria.put("parentId", parentOrgId);
        criteria.put("orgSid", orgId);
        criteria.put("orgName", orgName);
        int count = orgMapper.orgNameExit(criteria);
        if (count >= 1) {
            return true;
        }
        return false;
    }

    @Override
    public List<User> findUsersAndJoinedByOrgId(Long orgSid, String searchFilter) {
        Criteria criteria = new Criteria();
        criteria.put("orgSid", orgSid);
        if (!Strings.isNullOrEmpty(searchFilter)) {
            criteria.put("searchFilter", searchFilter);
        }

        List<User> users = userService.findUserByOrgId(criteria);

        fetchUsersRole(users, orgSid);

        return users;
    }

    @Override
    public List<User> findUsersAndJoinedByOrgIdHasApproval(Long orgSid) {
        List<User> users = findUsersAndJoinedByOrgId(orgSid, null);

        // 查询用审核权限的角色
        List<Role> orgRoles = roleService.findRolesByOrgSidHasApproval();

        List<User> approvalUsers = users.stream().filter(user -> {
            if (user.getRoles() == null) {
                return false;
            }

            for (Role role : user.getRoles()) {
                for (Role orgRole : orgRoles) {
                    if (role.getRoleSid().equals(orgRole.getRoleSid())) {
                        return true;
                    }
                }
            }

            return false;
        }).collect(Collectors.toList());

        return approvalUsers;
    }

    @Override
    public List<User> findUsersAndJoinedByOrgIdHasApprovalCustomize(Long orgSid) {
        List<User> users = findUsersAndJoinedByOrgId(orgSid, null);

        // 查询用审核权限的角色
        List<Role> orgRoles = roleService.findRolesByOrgSidHasApproval(orgSid);

        List<User> approvalUsers = users.stream().filter(user -> {
            if (user.getRoles() == null) {
                return false;
            }

            for (Role role : user.getRoles()) {
                for (Role orgRole : orgRoles) {
                    if (role.getRoleSid().equals(orgRole.getRoleSid())) {
                        return true;
                    }
                }
            }

            return false;
        }).collect(Collectors.toList());

        return approvalUsers;
    }

    @Override
    public boolean validateUserIsProjectOwner(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Boolean.FALSE;
        }
        List<User> users = userMapper.selectBatchUser(userIds);
        if (users.size() != userIds.size()) {
            BizException.e(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_867857480));
        }
        Criteria criteria = new Criteria("ownerIds", userIds);
        criteria.put("orgType", OrgType.PROJECT);

        return orgMapper.countByParamsWithoutDataFilter(criteria) > 0;
    }

    @Override
    public List<Org> findProjectsByOrgSid(Long orgSid) {
        return orgMapper.findProjectsByOrgSid(orgSid);
    }

    @Override
    public List<User> findUsersJoinedByOrgIdAndParentOrg(Long orgSid, String searchFilter) {
        Org org = orgMapper.selectByPrimaryKey(orgSid);
        String[] orgSidStrings = org.getTreePath().substring(1).split("/");
        List<Long> orgSids = Lists.newArrayList();
        orgSids.add(orgSid);

        orgSids.addAll(Arrays.stream(orgSidStrings)
                             .filter(a -> StringUtils.isNotEmpty(a))
                             .map(Long::parseLong)
                             .collect(Collectors.toList()));

        Criteria criteria = new Criteria();
        criteria.put("orgSidList", orgSids);
        if (!Strings.isNullOrEmpty(searchFilter)) {
            criteria.put("searchFilter", searchFilter);
        }

        return userService.findByOrgIds(criteria);
    }

    @Override
    public void initPlatformClougTags(Org org, AuthUser authUser) {
        /// 添加平台标签
        List<CloudTag> cloudTags = cloudTagMapper.selectByPlatTag();
        if (cloudTags.size() > 0) {
            for (CloudTag tag : cloudTags) {
                tag.setOwnerId(authUser.getUserSid() + "");
                tag.setOrgSid(org.getOrgSid());
                tag.setCreatedBy(authUser.getAccount());
                tag.setCreatedDt(DateTime.now().toDate());
                tag.setUpdatedBy(authUser.getAccount());
                tag.setUpdatedDt(tag.getCreatedDt());
            }

            cloudTagMapper.insertBatch(cloudTags);

            List<CloudTag> newCloudTags = cloudTagMapper.selectByParams(new Criteria("orgSid", org.getOrgSid()));

            Map<String, Long> tagKNameId = newCloudTags.stream()
                                                       .filter(cloudTag -> "k".equals(cloudTag.getTagType()))
                                                       .collect(Collectors.toMap(CloudTag::getTagName,
                                                                                 CloudTag::getTagId));
            if (!CollectionUtils.isEmpty(newCloudTags)) {
                newCloudTags.forEach(cloudTag -> {
                    if ("v".equals(cloudTag.getTagType())) {
                        // 为子类型的cloudtag设置pid
                        cloudTag.setPid(tagKNameId.get(cloudTag.getTagName()));
                        cloudTagMapper.updateByPrimaryKey(cloudTag);
                    }
                });
            }
        }
    }

    @Override
    public List<Org> selectChildOrgWithoutAuth(Long parentOrgId) {
        Criteria criteria = new Criteria();
        criteria.put("parentId", parentOrgId);
        return this.selectByParamsWithoutAuth(criteria);
    }

    @Override
    public <T> void setOwnerOrgNameByOriginOrgSid(List<T> list) {
        for (T e : list) {
            Long orgSid = Convert.toLong(ReflectUtil.getFieldValue(e, "originOrgSid"));
            if (Objects.nonNull(orgSid)) {
                Org org = orgMapper.selectByPrimaryKey(orgSid);
                if (Objects.isNull(org)) {
                    continue;
                }
                long[] orgIds = StrUtil.splitToLong(org.getTreePath(), CharUtil.SLASH);
                List<Org> orgs = Lists.newArrayList();
                if (ArrayUtil.isNotEmpty(orgIds)) {
                    Criteria criteria = new Criteria();
                    criteria.put("orgIds", orgIds);
                    orgs = orgMapper.selectByParamsNoAuth(criteria);
                }
                orgs.add(org);
                String ownerOrgNames = orgs.stream()
                                           .sorted(Comparator.comparing(Org::getOrgSid))
                                           .map(Org::getOrgName)
                                           .distinct()
                                           .collect(Collectors.joining(StrUtil.SLASH));
                ReflectUtil.setFieldValue(e, "ownerOrgName", ownerOrgNames);
            }
        }
    }

    private void fetchUsersRole(List<User> users, Long orgSid) {
        for (User user : users) {
            user.setProjectName(userService.findProjectNamesByUserId(user.getUserSid()));
            user.setCompanyName(userService.findCompanyNamesByUserId(user.getUserSid()));

            //查询用户的角色列表,并拼接成字符串
            List<Role> roleList = this.roleService.findRolesByUserSidAndOrgSid(user.getUserSid(), orgSid);
            if (!CollectionUtils.isEmpty(roleList)) {
                List<String> roleNameList = new ArrayList<>();
                List<Long> roleIdList = new ArrayList<>();
                for (Role role : roleList) {
                    roleNameList.add(role.getRoleName());
                    roleIdList.add(role.getRoleSid());
                }

                user.setRoles(roleList);
                user.setRoleIds(roleIdList);
                user.setRoleName(StringUtils.join(roleNameList, ","));
                user.setRoleString(StringUtils.join(roleIdList, ","));
            }
        }
    }
    @Override
    public Long createBillingAccount(CreateBillingAccountRequest request) {
        JSONObject param = new JSONObject();
        param.put("user", buildUser(request));
        param.put("company", buildCompany(request));
        param.put("isPreOpen",request.getIsPreOpen());
        ExtVO extVO = new ExtVO();
        extVO.setBusiness(request.getBusiness());
        extVO.setCompanyCode(request.getCompanyCode());
        extVO.setLicense(request.getLicense());
        param.put("ext", extVO);

        User remoteUser = userService.insertRegisterUser(JSON.toJSONString(param));
        try {

            //如果是预开通时，不传邮箱到keyCloak
            if(request.getIsPreOpen()){
                remoteUser.setEmail(null);
                remoteUser.setMobile("");
            }
            IamUser iamUser = BeanConvertUtil.convert(remoteUser, IamUser.class);
            iamUser.setGroupIds(Collections.singletonList(1L));
            iamUser.setIsAdmin(true);
            //远程添加
            iamRemoteService.insertUser(iamUser);
            businessNotificationService.sendInformToAdminRequest(remoteUser);
        } catch (Exception e) {
            cancelService.cancelUser(remoteUser.getUserSid());
            // 回滚用户组
            cancelService.cancelOrg(remoteUser.getOrgSid());
            throw new BizException(e.getMessage());
        }

        return remoteUser.getUserSid();
    }

    @Override
    public Long createUserAdmin(CreateUserAdminRequest request) {
        log.info("create tenant admin:{}", request.getAccount());
        JSONObject param = new JSONObject();
        Org org = orgMapper.selectByPrimaryKey(request.getOrgSid());
        if (Objects.isNull(org)) {
            throw new BizException("租户信息不存在");
        }
        // 是否检验电话重复
        String checkMobile = sysConfigMapper.selectConfigValue("mobile.duplicate.check");
        log.info("联系电话重复开关-【{}】", checkMobile);
        // 租户下电话不重复
        List<Long> orgIds = orgMapper.selectAllOrgSids(org.getOrgSid());
        if (!"1".equals(checkMobile) && userMapper.countSameMobileByOrgIds(request.getPhone(), orgIds) > 0) {
            throw new BizException("联系电话重复。");
        }
        param.put("company", org);
        CreateBillingAccountRequest createBillingAccountRequest = BeanConvertUtil.convert(request,
                                                                                          CreateBillingAccountRequest.class);
        createBillingAccountRequest.setContactPhone(request.getPhone());
        param.put("user", buildUser(createBillingAccountRequest));
        User remoteUser = userService.insertTenantAdmin(JSON.toJSONString(param));
        //如果是预开通时，不传邮箱到keyCloak
        if (request.getIsPreOpen()) {
            remoteUser.setEmail(null);
        }
        try {
            //远程添加
            IamUser iamUser = BeanConvertUtil.convert(remoteUser, IamUser.class);
            iamUser.setGroupIds(Collections.singletonList(1L));
            iamUser.setIsAdmin(true);
            log.info("iam insert user");
            ResCloudEnv resCloudEnv = iamRemoteService.insertUser(iamUser);
            log.info("OrgServiceImpl-hcsoFlg:{}", hcsoFlg);
            if (hcsoFlg) {
                userService.builtInUser(remoteUser, resCloudEnv);
            }
        } catch (Exception e) {
            cancelService.cancelUser(remoteUser.getUserSid());
            iamRemoteService.deleteUser(remoteUser.getUserSid());
            throw new BizException(e.getMessage());
        }
        return remoteUser.getUserSid();
    }

    @Override
    public List<Org> registCompany() {
        return orgMapper.registedCompany();
    }

    @Override
    public List<Org> registCompany(Long entityId) {
        return orgMapper.registedCompanyE(entityId);
    }


    @Override
    public User findAdministratorByOrgSid(Long orgSid) {
        return userMapper.findAdministratorByOrgSid(orgSid);
    }

    @Override
    public RestResult batchInsertBillingAccount(String type, MultipartFile file) {
        if (Objects.nonNull(file) && !FileTypeCodeEnum.XLSX.getFileTye().equals(FilenameUtils.getExtension(file.getOriginalFilename()))) {
            throw new BizException(WebUtil.getMessage(MsgCd.IMPORT_FILE_FORM_INCORRECT));
        }
        if (!Objects.equals(Constants.CUSTOMER, type) && !Objects.equals(Constants.SUB_USERS, type)) {
            throw new BizException(WebUtil.getMessage(MsgCd.IMPORT_FILE_CONTENT_INCORRECT));
        }

        //先保存文件
        String downloadNum = NoUtil.generateNo("DT");
        StorageResult storage = storageService.saveFile(file, StoragePathEnum.EXCEL.getPath(type));

        List<BillingAccountTemplate> billingAccountTemplates = null;
        List<SubUserTemplate> subUserTemplates = null;
        try {

            ExcelImportService importService = ExcelUtil.read().inputStream(file.getInputStream());
            //解析映射客户
            if (Objects.equals(Constants.CUSTOMER, type)) {
                billingAccountTemplates = importService.readSheet(BillingAccountTemplate.class, "映射客户模板");
                //解析子用户模板
                subUserTemplates = importService.readSheet(SubUserTemplate.class, "映射子用户模板");
            }
            else {
                subUserTemplates = importService.readSheet(SubUserTemplate.class, "映射子用户模板");
            }

        } catch (Exception e) {
            log.error("OrgServiceImpl.batchInsertBillingAccount 导入文件格式不符合规范，点击下载Excel模板导入", e);
            throw new BizException(WebUtil.getMessage(MsgCd.IMPORT_FILE_FORM_INCORRECT));
        }

        if ((ObjectUtils.isEmpty(billingAccountTemplates) && ObjectUtils.isEmpty(subUserTemplates)) ||
                (Objects.equals(Constants.SUB_USERS, type) &&  ObjectUtils.isEmpty(subUserTemplates))) {
            throw new BizException(WebUtil.getMessage(MsgCd.TABLE_CONTENT_NOT_EXIT));
        }
        if (!CollectionUtils.isEmpty(billingAccountTemplates) && billingAccountTemplates.size() > 1000) {
            BizException.e(WebUtil.getMessage(MsgCd.IMPORT_CUSTOMER_CAPS));
        }
        if (!CollectionUtils.isEmpty(subUserTemplates) && subUserTemplates.size() > 1000) {
            BizException.e(WebUtil.getMessage(MsgCd.IMPORT_SUBUSER_CAPS));
        }
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();

        BizDownload download = new BizDownload();
        download.setOperationType("batchImportCustomer");
        //添加导入任务数据
        download.setOrgSid(authUserInfo.getOrgSid());
        download.setDownloadNum(downloadNum);
        download.setDownloadPath(CrytoUtilSimple.encrypt(storage.getRelativeNginxUrl()));
        download.setStatus(2);
        download.setParam(type);
        download.setAccountId(authUserInfo.getUserSid());
        download.setCreatedBy(authUserInfo.getAccount());
        download.setCreatedDt(new Date());
        download.setOrgSid(authUserInfo.getOrgSid());
        download.setVersion(1);
        download.setEntityId(authUserInfo.getEntityId());
        bizDownloadMapper.insert(download);

        ExecutorService executorService = Executors.newSingleThreadExecutor();
        List<BillingAccountTemplate> finalBillingAccountTemplates = billingAccountTemplates;
        List<SubUserTemplate> finalSubUserTemplates = subUserTemplates;
        AtomicReference<ImportCustomerRequest> request = new AtomicReference<>();

        executorService.execute(() -> {
            try {
                //解析excel
                if (Objects.equals(Constants.CUSTOMER, type)) {
                    log.info("OrgServiceImpl.batchInsertBillingAccount 创建客户开始");
                    long starTime = System.currentTimeMillis();
                    AuthUserHolder.setAuthUser(authUserInfo);
                    this.importCustomer(finalBillingAccountTemplates, finalSubUserTemplates, authUserInfo, downloadNum);
                    long endTime = System.currentTimeMillis();
                    log.info("OrgServiceImpl.batchInsertBillingAccount 创建客户结束 耗时：【{}】ms", endTime - starTime);

                    request.set(ImportCustomerRequest.builder()
                            .type(type)
                            .customers(finalBillingAccountTemplates)
                            .subUsers(finalSubUserTemplates)
                            .downloadNum(downloadNum)
                            .build());
                }
                else {
                    log.info("OrgServiceImpl.batchInsertBillingAccount 创建子用户开始");
                    long starTime = System.currentTimeMillis();
                    AuthUserHolder.setAuthUser(authUserInfo);
                    this.importSubUser(finalSubUserTemplates,  "subUser", authUserInfo, downloadNum);
                    long endTime = System.currentTimeMillis();
                    log.info("OrgServiceImpl.batchInsertBillingAccount 创建子用户结束 耗时：【{}】ms", endTime - starTime);

                    // 檢查是否存在超时失败的数据
                    List<BizDownloadExtend>  timeOutFaileds = bizDownloadExtentMapper.selectTimeOutFailed(downloadNum);
                    if (CollectionUtil.isNotEmpty(timeOutFaileds)) {
                        this.handTimeOutSubUser(timeOutFaileds, downloadNum, 1);
                    }

                     request.set(ImportCustomerRequest.builder()
                            .subUsers(finalSubUserTemplates)
                            .type("subUser")
                            .downloadNum(downloadNum)
                            .build());
                }

                download.setStatus(3);
                download.setParam(null);
                bizDownloadMapper.updateByPrimaryKey(download);
                //删除文件
                storageService.deleteFile(storage.getRelativeNginxUrl());

                log.info("OrgServiceImpl.batchInsertBillingAccount 开始生成结果excel");
                userFeginService.asynExportCustomer(request.get());

            } catch (BizException e) {
                throw new BizException(e.getMessage());
            } catch (Exception e) {
                log.error("OrgServiceImpl.batchInsertBillingAccount 导入文件格式不符合规范，点击下载Excel模板导入", e);
                throw new BizException(WebUtil.getMessage(MsgCd.IMPORT_FILE_FORM_INCORRECT));
            }
        });

        return new RestResult();
    }


    /**
     *  处理超时子用户(对失败的检查三次)
     * @param timeOutUser 超时用户
     */
    private void handTimeOutSubUser(List<BizDownloadExtend> timeOutUser, String downloadNum, int num) {
        try {
            log.info("OrgServiceImpl.handTimeOutUser num: {}, timeOutUser: {}", num, JSON.toJSONString(timeOutUser));

            if (num > 7) {
                return;
            }

            for (BizDownloadExtend downloadExtend : timeOutUser) {
                SubUserTemplate subUserTemplate = JSON.parseObject(downloadExtend.getJsonData(), SubUserTemplate.class);
                String account = subUserTemplate.getAccount();
                int count = userMapper.countUserByUserId(account);
                log.info("OrgServiceImpl.handTimeOutUser-{} count: [{}]", account, count);

                if (userService.checkCreateIamSubUser()) {
                    cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria resUserCri = new cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria();
                    resUserCri.put("name", userService.getIamUserNameByAccount(subUserTemplate.getAccount()));
                    List<cn.com.cloudstar.rightcloud.remote.api.pojo.resource.user.ResUser> resUsers = resUserRemoteService.selectByParams(resUserCri);
                    boolean exitResUser = CollectionUtil.isNotEmpty(resUsers);
                    log.info("OrgServiceImpl.handTimeOutUser-{} exitResUser: [{}]", account, exitResUser);

                    // 用户数据及ima子用户数据都存在的情况，改为成功
                    if (count > 0 && exitResUser) {
                        subUserTemplate.setResult("success");
                        bizDownloadExtentMapper.updateBizDownloadExtentJsonData(subUserTemplate.getDownloadExtendId(), JSON.toJSONString(subUserTemplate));
                        continue;
                    }
                    else if (count <= 0 && exitResUser) {
                        // 用户数据不存在，子用户数据存在，回滚子用户数据
                        ResUser resUser = resUsers.get(0);
                        log.info("OrgServiceImpl.handTimeOutSubUser 回滾iam子用戶 iamName: {}", resUser.getName());
                        userService.deleteSubUser(resUser.getUuid(), resUser.getOrgSid());
                        cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria criteria = new cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria();
                        criteria.put("uuid", resUser.getUuid());
                        resUserRemoteService.deleteByParams(criteria);
                        continue;
                    }
                }
                else {
                    if (count > 0) {
                        subUserTemplate.setResult("success");
                        bizDownloadExtentMapper.updateBizDownloadExtentJsonData(subUserTemplate.getDownloadExtendId(), JSON.toJSONString(subUserTemplate));
                        continue;
                    }
                }

            }

            timeOutUser = bizDownloadExtentMapper.selectTimeOutFailed(downloadNum);
            if (timeOutUser.size() > 0) {
                num++;
                Thread.sleep(2 * 60 * 1000);
                this.handTimeOutSubUser(timeOutUser, downloadNum, num);
            }

            log.info("OrgServiceImpl.handTimeOutUser downloadNum： {}, 超时检查完成", downloadNum);
        } catch (Exception e) {
            log.error("OrgServiceImpl.handTimeOutUser error: ", e);
        }

    }

    @Override
    public List<String> queryIndustry() {
        return orgMapper.queryIndustry();
    }

    @Override
    public List<Long> selectAllSidsByOrgSid(Long orgSid) {
        return orgMapper.selectAllSidsByOrgSid(orgSid);
    }

    @Override
    public void checkDistributorRole(String accountId) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectByPrimaryKey(Long.valueOf(accountId));
        if (Objects.isNull(bizBillingAccount) || Objects.isNull(authUserInfo)) {
            throw new BizException(WebUtil.getMessage(WebConstants.MsgCd.WARNING_QUERY_FAILURE));
        }
        if (UserType.DISTRIBUTOR_USER.equals(authUserInfo.getUserType())) {
            List<Role> rolesByUserSid = roleMapper.findRolesByUserSid(authUserInfo.getUserSid());
            if (!authUserInfo.getOrgSid().equals(bizBillingAccount.getDistributorId()) || (
                    rolesByUserSid.get(0).getDataScope().equals("3")
                            && !authUserInfo.getUserSid().equals(bizBillingAccount.getSalesmenId()))) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
            }
        }
    }


    @Override
    public void downLoadAutoGenerateBatchImportCustomer() throws FileNotFoundException {
        List<BizDownload>  downloads = bizDownloadMapper.selectBizDownloadByTypeAndStatus("batchImportCustomer", 2);
        if (downloads.size() > 0) {
            for (BizDownload download :downloads) {
                AtomicReference<ImportCustomerRequest> request = new AtomicReference<>();
                String path = download.getDownloadPath();
                if (StringUtils.isBlank(path)) {
                    continue;
                }
                if (CrytoUtilSimple.isEncryptedData(path)) {
                    path = CrytoUtilSimple.decrypt(path);
                }
                StorageResult storage = storageService.getFile(path);
                if (ObjectUtils.isEmpty(storage)) {
                    continue;
                }
                String type = download.getParam();

                List<BillingAccountTemplate> billingAccountTemplates = null;
                List<SubUserTemplate> subUserTemplates = null;
                try {
                    ExcelImportService importService = ExcelUtil.read().inputStream(storage.getInputStream());
                    //解析映射客户
                    if (Objects.equals(Constants.CUSTOMER, type)) {
                        billingAccountTemplates = importService.readSheet(BillingAccountTemplate.class, "映射客户模板");
                        //解析子用户模板
                        subUserTemplates = importService.readSheet(SubUserTemplate.class, "映射子用户模板");
                    }
                    else {
                        subUserTemplates = importService.readSheet(SubUserTemplate.class, "映射子用户模板");
                    }
                    importService.finish();
                } catch (Exception e) {
                    log.error("OrgServiceImpl.downLoadAutoGenerateBatchImportCustomer 导入文件格式不符合规范，点击下载Excel模板导入", e);
                    continue;
                }

                User user = userMapper.selectByPrimaryKey(download.getAccountId());
                AuthUser authUserInfo = BeanConvertUtil.convert(user, AuthUser.class);
                AuthUserHolder.setAuthUser(authUserInfo);

                List<BillingAccountTemplate> oldAccountTemplates = new ArrayList<>();
                List<SubUserTemplate> oldSubUserTemplates = new ArrayList<>();

                this.setCustomerAndSubUser(billingAccountTemplates, subUserTemplates, oldAccountTemplates, oldSubUserTemplates,  download.getDownloadNum());

                // 过滤掉已经执行过的客户
                if (CollectionUtil.isNotEmpty(oldAccountTemplates)) {
                    List<String> oldCustomerAccounts = oldAccountTemplates.stream().map(BillingAccountTemplate::getAccount).collect(Collectors.toList());
                    billingAccountTemplates = billingAccountTemplates.stream()
                            .filter(e -> !oldCustomerAccounts.contains(e.getAccount())).collect(Collectors.toList());
                }

                if (CollectionUtil.isNotEmpty(oldSubUserTemplates)) {
                    List<String> oldSubUsers = oldSubUserTemplates.stream().map(SubUserTemplate::getAccount).collect(Collectors.toList());
                    subUserTemplates = subUserTemplates.stream()
                            .filter(e -> !oldSubUsers.contains(e.getAccount())).collect(Collectors.toList());
                }

                List<SubUserTemplate> finalSubUserTemplates = subUserTemplates;
                try {
                    //解析excel
                    if (Objects.equals(Constants.CUSTOMER, type)) {
                        log.info("OrgServiceImpl.downLoadAutoGenerateBatchImportCustomer 创建客户开始");
                        long starTime = System.currentTimeMillis();
                        int dataSize = billingAccountTemplates.size();
                        int size = dataSize / MAX_THREAD_NUM;
                        if (size <= 0) {
                            size = 1;
                        } else {
                            int ys = dataSize % MAX_THREAD_NUM;
                            size = ys > size ? size + 1 : size;
                        }
                        List<List<BillingAccountTemplate>> lists = Lists.partition(billingAccountTemplates, size);
                        int listSize = lists.size();
                        final CountDownLatch countDownLatch = new CountDownLatch(listSize);

                        for (int i = 0; i < listSize; i++) {
                            final int finalI = i;
                            threadPoolTaskExecutor.execute(() ->   {
                                AuthUserHolder.setAuthUser(authUserInfo);
                                try {
                                    AuthUserHolder.setAuthUser(authUserInfo);
                                    this.importCustomer(lists.get(finalI), finalSubUserTemplates, authUserInfo, download.getDownloadNum());
                                } finally {
                                    log.info("OrgServiceImpl.downLoadAutoGenerateBatchImportCustomer 已完成次数： {}， 总次数： {}", finalI + 1, listSize);
                                    countDownLatch.countDown();
                                }
                            });
                        }

                        countDownLatch.await();
                        long endTime = System.currentTimeMillis();
                        log.info("OrgServiceImpl.downLoadAutoGenerateBatchImportCustomer 创建客户结束 耗时：【{}】ms", endTime - starTime);

                        if (CollectionUtil.isNotEmpty(oldSubUserTemplates)) {
                            finalSubUserTemplates.addAll(oldSubUserTemplates);
                        }
                        if (CollectionUtil.isNotEmpty(oldAccountTemplates)) {
                            billingAccountTemplates.addAll(oldAccountTemplates);
                        }

                        request.set(ImportCustomerRequest.builder()
                                .type(type)
                                .customers(billingAccountTemplates)
                                .subUsers(finalSubUserTemplates)
                                .downloadNum(download.getDownloadNum())
                                .build());
                    }
                    else {
                        log.info("OrgServiceImpl.downLoadAutoGenerateBatchImportCustomer 创建子用户开始");
                        long starTime = System.currentTimeMillis();

                        int dataSize = finalSubUserTemplates.size();
                        int size = dataSize / MAX_THREAD_NUM;
                        if (size <= 0) {
                            size = 1;
                        } else {
                            int ys = dataSize % MAX_THREAD_NUM;
                            size = ys > size ? size + 1 : size;
                        }

                        List<List<SubUserTemplate>> lists = Lists.partition(finalSubUserTemplates, size);
                        int listSize = lists.size();
                        final CountDownLatch countDownLatch = new CountDownLatch(listSize);
                        for (int i = 0; i < listSize; i++) {
                            final int finalI = i;
                            threadPoolTaskExecutor.execute(() ->   {
                                try {
                                    AuthUserHolder.setAuthUser(authUserInfo);
                                    this.importSubUser(lists.get(finalI),  "subUser", authUserInfo, download.getDownloadNum());
                                } finally {
                                    countDownLatch.countDown();
                                }
                            });
                        }

                        countDownLatch.await();
                        long endTime = System.currentTimeMillis();
                        log.info("OrgServiceImpl.downLoadAutoGenerateBatchImportCustomer 创建子用户结束 耗时：【{}】ms", endTime - starTime);

                        if (CollectionUtil.isNotEmpty(oldSubUserTemplates)) {
                            finalSubUserTemplates.addAll(oldSubUserTemplates);
                        }

                        request.set(ImportCustomerRequest.builder()
                                .subUsers(finalSubUserTemplates)
                                .type("subUser")
                                .downloadNum(download.getDownloadNum())
                                .build());
                    }

                    download.setStatus(3);
                    download.setParam(null);
                    bizDownloadMapper.updateByPrimaryKey(download);
                    //删除文件
                    storageService.deleteFile(path);

                    log.info("OrgServiceImpl.downLoadAutoGenerateBatchImportCustomer 开始生成结果excel");
                    userFeginService.asynExportCustomer(request.get());

                } catch (BizException e) {
                    log.error("OrgServiceImpl.downLoadAutoGenerateBatchImportCustomer error: ", e);
                } catch (Exception e) {
                    log.error("OrgServiceImpl.downLoadAutoGenerateBatchImportCustomer 导入文件格式不符合规范，点击下载Excel模板导入", e);
                }

            }
        }
    }

    private void setCustomerAndSubUser(List<BillingAccountTemplate> billingAccountTemplates, List<SubUserTemplate> subUserTemplates,
                                       List<BillingAccountTemplate> oldAccountTemplates, List<SubUserTemplate> oldSubUserTemplates, String downloadNum) {
        // 获取历史执行的数据
        Map<String, Object> params = new HashMap<>();
        params.put("downloadNum", downloadNum);
        List<BizDownloadExtend> bizDownloadExtends = bizDownloadExtentMapper.selectBizDownloadExtendByDownloadNum(params);
        if (CollectionUtil.isNotEmpty(bizDownloadExtends)) {
            List<String> oldCustomer = bizDownloadExtends.stream()
                    .filter(e -> "customer".equals(e.getExtend())).map(BizDownloadExtend::getJsonData).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(oldCustomer)) {
                for (String customer : oldCustomer) {
                    BillingAccountTemplate accountTemplate = JSON.parseObject(customer, BillingAccountTemplate.class);
                    oldAccountTemplates.add(accountTemplate);
                }
            }

            List<String> oldSubUser = bizDownloadExtends.stream()
                    .filter(e -> "subUsers".equals(e.getExtend())).map(BizDownloadExtend::getJsonData).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(oldSubUser)) {
                for (String subUser : oldSubUser) {
                    SubUserTemplate subUserTemplate = JSON.parseObject(subUser, SubUserTemplate.class);
                    oldSubUserTemplates.add(subUserTemplate);
                }
            }
        }
    }

    /**
     * 导入客户信息
     *
     */
    private void importCustomer(List<BillingAccountTemplate> billingAccountTemplates,  List<SubUserTemplate> subUserTemplates, AuthUser authUser, String downloadNum) {
        //查看有没有预开通权限
        boolean preOpenAuth = true;
        try {
            List<RoleModule> roleModules = roleModuleMapper.selectRoleModuleByUserSid(authUser.getUserSid());
            if(CollectionUtil.isEmpty(roleModules)){
                preOpenAuth = false;
            }else{
                List<String> cacheAuths = roleModules.stream().map(RoleModule::getModuleSid).collect(Collectors.toList());
                if(!cacheAuths.contains(AuthModule.BQ.BQ0111)){
                    preOpenAuth = false;
                }
            }
        } catch (Exception e) {
            log.info("OrgServiceImpl.importCustomer authUser is null");
        }

        //检验映射客户字段是否为空，是否重复
        checkBillingAccount(billingAccountTemplates);

        //获取当前用户的分销商，校验分销商人数是否超出范围
        if ( UserType.DISTRIBUTOR_USER.equals(authUser.getUserType()) && Objects.nonNull(authUser.getOrgSid())) {
            try {
                checkDistributorCount(authUser.getOrgSid(), billingAccountTemplates.size());
            } catch (BizException e) {
                billingAccountTemplates.forEach(accountTemplate -> {
                    accountTemplate.setResult("failed");
                    accountTemplate.setFailInfo(e.getMessage());
                    accountTemplate.setFalg(Boolean.FALSE);
                });
            }
        }

        //校验映射客户,并入库
        verifyBillingAccount(billingAccountTemplates, preOpenAuth, downloadNum);
        //入库子用户
        importSubUser(subUserTemplates, "customer", preOpenAuth, downloadNum);
        //填充子用户导入情况到映射客户列表
    }

    /**
     * 校验分销商人数
     *
     * @param orgSid 当前用户组织id
     * @param billSize 当前导入的分销商个数
     *
     * <AUTHOR>
     */
    private BizDistributor checkDistributorCount(Long orgSid, int billSize) {
        BizDistributor distributor = distributorService.getById(orgSid);
        if (ObjectUtils.isEmpty(distributor)) {
            return null;
        }
        //查询当前分销商数量
        int count = bizBillingAccountService.countByDistributorId(distributor.getId());
        //当前已存在分销商数量加上导入的数量与maxnum判断
        if (count + billSize > distributor.getCustomerMaxNum()) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2043330009)+ count +WebUtil.getMessage(MsgCd.ERR_MSG_BSS_943228649)+ distributor.getCustomerMaxNum() +WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1442499585));
        }

        return distributor;
    }

    /**
     * 校验映射客户,并入库
     *
     * @param billingAccountTemplates 映射客户列表
     *
     * <AUTHOR>
     */
    private void verifyBillingAccount(List<BillingAccountTemplate> billingAccountTemplates, boolean preOpenAuth, String downloadNum) {
        billingAccountTemplates.forEach(accountTemplate -> {
            accountTemplate.setResult("failed");


            BizDownloadExtend downloadExtend = new BizDownloadExtend();
            downloadExtend.setDownloadNum(downloadNum);
            downloadExtend.setCreateTime(new Date());
            downloadExtend.setExtend(Constants.CUSTOMER);

            //在checkBillingAccount方法里会去校验重复和必要字段，不符合则会将错误信息写入failInfo
            if (!ObjectUtils.isEmpty(accountTemplate.getFailInfo())) {
                accountTemplate.setPassword("*********");
                downloadExtend.setJsonData(JSON.toJSONString(accountTemplate));
                bizDownloadExtentMapper.insert(downloadExtend);
                return;
            }
            //验证密码
            String validStr = userService.validPasswordByPolicyToError(accountTemplate.getPassword(), null, null, null, true);
            if (!ObjectUtils.isEmpty(validStr)) {
                accountTemplate.setFailInfo(validStr);
                accountTemplate.setPassword("*********");
                downloadExtend.setJsonData(JSON.toJSONString(accountTemplate));
                bizDownloadExtentMapper.insert(downloadExtend);
                return;
            }
            //原密码
            String oldPassword = accountTemplate.getPassword();
            //密码hash加密
            accountTemplate.setPassword(CrytoUtilSimple.encodeHash(accountTemplate.getPassword()));

            JSONObject param = new JSONObject();
            if(accountTemplate.getBusinessTag() != null && "preOpen".equalsIgnoreCase(accountTemplate.getBusinessTag())){
                if (!preOpenAuth) {
                    accountTemplate.setFailInfo("当前账户没有预开通权限!");
                    accountTemplate.setPassword("*********");
                    downloadExtend.setJsonData(JSON.toJSONString(accountTemplate));
                    bizDownloadExtentMapper.insert(downloadExtend);
                    return;
                }
                param.put("isPreOpen",true);
            }

            User remoteUser = new User();
            try {
                CreateBillingAccountRequest request = new CreateBillingAccountRequest();
                request.setAccount(accountTemplate.getAccount());
                request.setEmail(accountTemplate.getEmail());
                request.setContactNumber(accountTemplate.getContactNumber());
                request.setPassword(oldPassword);
                param.put("user", buildUser(request));

                request.setCompanyName(accountTemplate.getCompanyName());
                request.setContactName(accountTemplate.getContactName());
                request.setBusiness(accountTemplate.getBusiness());
                request.setLicense(accountTemplate.getLicense());
                request.setCompanyCode(accountTemplate.getCompanyCode());
                param.put("company", buildCompany(request));
                ExtVO extVO = BeanUtil.copyProperties(request, ExtVO.class);
                param.put("ext", extVO);

                //添加映射客户
                remoteUser = userService.insertRegisterUser(JSON.toJSONString(param));
                if(accountTemplate.getBusinessTag() != null && "preOpen".equalsIgnoreCase(accountTemplate.getBusinessTag())){
                    remoteUser.setEmail(null);
                }
                try {
                    iamRemoteService.insertUser(BeanConvertUtil.convert(remoteUser, IamUser.class));
                    businessNotificationService.sendInformToAdminRequest(remoteUser);
                } catch (Exception e) {
                    log.error("创建用户出错:", e);
                    cancelService.cancelUser(remoteUser.getUserSid());
                    // 回滚用户组
                    cancelService.cancelOrg(remoteUser.getOrgSid());
                    accountTemplate.setFailInfo(e.getMessage());
                    //添加错误后，将原密码再塞回去
                    accountTemplate.setPassword("*********");
                    downloadExtend.setJsonData(JSON.toJSONString(accountTemplate));
                    bizDownloadExtentMapper.insert(downloadExtend);
                    return;
                }

            } catch (Exception e) {
                log.error("创建用户出错:", e);
                accountTemplate.setFailInfo(e.getMessage());
                //添加错误后，将原密码再塞回去
                accountTemplate.setPassword("*********");
                downloadExtend.setJsonData(JSON.toJSONString(accountTemplate));
                bizDownloadExtentMapper.insert(downloadExtend);
                return;
            }
            accountTemplate.setResult("success");
            accountTemplate.setPassword("*********");
            downloadExtend.setJsonData(JSON.toJSONString(accountTemplate));
            bizDownloadExtentMapper.insert(downloadExtend);
        });
    }

    /**
     * 填充子用户导入情况到映射客户列表
     *
     * @param billingAccountTemplates 映射客户列表
     * @param subUsers 子用户列表
     *
     * <AUTHOR>
     */
    private void fillBillingAccountBySub(List<BillingAccountTemplate> billingAccountTemplates, List<SubUserTemplate> subUsers) {
        if (ObjectUtils.isEmpty(subUsers) || !ObjectUtils.isEmpty(billingAccountTemplates)) {
            return;
        }
        //根据客户名称区分子用户
        Map<String, List<SubUserTemplate>> subMap = subUsers.stream()
                                                            .collect(Collectors.groupingBy(SubUserTemplate::getParentAccount));
        //获取所有子用户的客户名称
        Map<String, List<BizBillingAccountDTO>> bizBillingAccountMaps = bizBillingAccountMapper.selectByAccountIn(subMap.keySet())
                                                                                               .stream()
                                                                                               .collect(Collectors.groupingBy(BizBillingAccountDTO::getAccount));
        subMap.forEach((key, val) -> {
            //获取客户
            List<BizBillingAccountDTO> bizBills = bizBillingAccountMaps.get(key);
            BizBillingAccountDTO bizBillingAccount = ObjectUtils.isEmpty(bizBills) ? null : bizBills.stream().findFirst().orElse(null);
            if (!ObjectUtils.isEmpty(bizBillingAccount)) {
                //过滤已经成功导入的子用户
                List<SubUserTemplate> sub = val.stream()
                                               .filter(v -> "success".equals(v.getResult()))
                                               .collect(Collectors.toList());
                BillingAccountTemplate accountDTO = BillingAccountTemplate.builder()
                                                                          .account(bizBillingAccount.getAccount())
                                                                          .contactName(bizBillingAccount.getAccountName())
                                                                          .companyName(bizBillingAccount.getOrgName())
                                                                          .email(bizBillingAccount.getEmail())
                                                                          .license(bizBillingAccount.getLicense())
                                                                          .contactNumber(bizBillingAccount.getMobile())
                                                                          .result(sub.size() > 0 ? "success" : "failed")
                                                                          .subUserSzie(sub.size())
                                                                          .failInfo(sub.size() > 0 ? "" : "请下载导入结果查看失败信息").build();
                billingAccountTemplates.add(accountDTO);
                return;
            }
            BillingAccountTemplate accountDTO = BillingAccountTemplate.builder()
                                                                      .account(key)
                                                                      .result("failed")
                                                                      .failInfo("没有该主账户").build();
            billingAccountTemplates.add(accountDTO);
        });
    }


    /**
     * 导入子用户
     *
     * @param subUserTemplates 解析出的子用户列表
     * @param type 操作类型
     */
    private List<SubUserTemplate> importSubUser(List<SubUserTemplate> subUserTemplates, String type, boolean preOpenAuth, String downloadNum) {
        //读取后是否为空，如果为customer直接返空
        if (ObjectUtils.isEmpty(subUserTemplates)) {
            if (Objects.equals("customer", type)) {
                return Lists.newArrayList();
            }
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_244314541));
        }
        subUserTemplates.forEach(subUserTemplate -> {
            subUserTemplate.setResult("failed");

            BizDownloadExtend downloadExtend = new BizDownloadExtend();
            downloadExtend.setDownloadNum(downloadNum);
            downloadExtend.setCreateTime(new Date());
            downloadExtend.setExtend(Constants.SUB_USERS);

            if (BooleanUtil.isFalse(subUserTemplate.getFalg())) {
                subUserTemplate.setFailInfo("数据校验标识符false");
                subUserTemplate.setPassword("*********");
                downloadExtend.setJsonData(JSON.toJSONString(subUserTemplate));
                bizDownloadExtentMapper.insert(downloadExtend);
                return;
            }
            cn.com.cloudstar.rightcloud.common.pojo.ValidationResult result = ValidationUtils.validateEntity(subUserTemplate);
            if (result.isHasErrors()) {
                subUserTemplate.setResult("failed");
                subUserTemplate.setFailInfo(result.getErrorMsg());
                downloadExtend.setJsonData(JSON.toJSONString(subUserTemplate));
                bizDownloadExtentMapper.insert(downloadExtend);
                return;
            }
            try {
                //查找主用户账号
                User parentUser = userService.findUserByAccount(subUserTemplate.getParentAccount())
                        .stream()
                        .findFirst()
                        .orElse(null);
                if (ObjectUtils.isEmpty(parentUser)) {
                    subUserTemplate.setResult("failed");
                    subUserTemplate.setFailInfo("请填写正确的主账户名");
                    subUserTemplate.setPassword("*********");
                    downloadExtend.setJsonData(JSON.toJSONString(subUserTemplate));
                    bizDownloadExtentMapper.insert(downloadExtend);
                    return;
                }
                //获取权限
                if(ModuleTypeConstants.FROM_BSS.equalsIgnoreCase(RequestContextUtil.getAuthUserInfo().getRemark())){
                    List<Long>  adminSids = new ArrayList<>();
                    List<Role> currentRoleList =  roleService.selectRoleByUserSid(RequestContextUtil.getAuthUserInfo().getUserSid());
                    List<cn.com.cloudstar.rightcloud.common.pojo.Role> convertsRolesList = cn.com.cloudstar.rightcloud.common.util.BeanConvertUtil.convert(currentRoleList, cn.com.cloudstar.rightcloud.common.pojo.Role.class);
                    String maxScope = DataScopeUtil.getMaxDataScope(convertsRolesList);
                    if(DataScopeEnum.DATA_SCOPE_COMPANY.getScope().equals(maxScope)){
                        Criteria bizBillingAccountCriteria = new Criteria();
                        bizBillingAccountCriteria.put("entityId",RequestContextUtil.getEntityId());
                        bizBillingAccountCriteria.put("salesmenId",RequestContextUtil.getAuthUserInfo().getUserSid());
                        List<BizBillingAccount> accounts = bizBillingAccountMapper.selectByParams(bizBillingAccountCriteria);
                        if(CollectionUtil.isNotEmpty(accounts)){
                            for(BizBillingAccount account : accounts){
                                adminSids.add(account.getAdminSid());
                            }
                        }
                        if(CollectionUtil.isNotEmpty(adminSids)){
                            if(!adminSids.contains(parentUser.getUserSid())){
                                subUserTemplate.setResult("failed");
                                subUserTemplate.setFailInfo("您还没有客户,不能直接创建子用户!");
                                downloadExtend.setJsonData(JSON.toJSONString(subUserTemplate));
                                bizDownloadExtentMapper.insert(downloadExtend);
                                return;
                            }
                        }else{
                            subUserTemplate.setResult("failed");
                            subUserTemplate.setFailInfo("您还没有客户,不能直接创建子用户!");
                            downloadExtend.setJsonData(JSON.toJSONString(subUserTemplate));
                            bizDownloadExtentMapper.insert(downloadExtend);
                            return;
                        }
                    }
                }
                //校验标签，为false不执行了
                if (!subUserTemplate.getFalg()) {
                    subUserTemplate.setFailInfo("数据校验标识符false");
                    subUserTemplate.setPassword("*********");
                    downloadExtend.setJsonData(JSON.toJSONString(subUserTemplate));
                    bizDownloadExtentMapper.insert(downloadExtend);
                    return;
                }
                if (ObjectUtils.isEmpty(subUserTemplate.getGroupIds())) {
                    subUserTemplate.setFailInfo("用户组id不能为空");
                    subUserTemplate.setResult("failed");
                    downloadExtend.setJsonData(JSON.toJSONString(subUserTemplate));
                    bizDownloadExtentMapper.insert(downloadExtend);
                    return;
                }
                List<String> groupNames = StrUtil.splitTrim(subUserTemplate.getGroupIds(), ",")
                        .stream().distinct().collect(Collectors.toList());
                List<Long> groupIds = groupNames.stream().map(GroupIdEnum::getIdByName).collect(Collectors.toList());
                if (groupNames.size() != groupIds.size()) {
                    subUserTemplate.setFailInfo("用户组id有误");
                    subUserTemplate.setResult("failed");
                    downloadExtend.setJsonData(JSON.toJSONString(subUserTemplate));
                    bizDownloadExtentMapper.insert(downloadExtend);
                    return;
                }

                //构建请求
                CreateUsersRequest request = buildUserVOReq(
                        Collections.singletonList(subUserTemplate)
                        , buildUserReq(parentUser, subUserTemplate, groupIds));
                //为空，则直接返回
                if (ObjectUtils.isEmpty(request)) {
                    subUserTemplate.setFailInfo("构建请求null");
                    subUserTemplate.setPassword("*********");
                    downloadExtend.setJsonData(JSON.toJSONString(subUserTemplate));
                    bizDownloadExtentMapper.insert(downloadExtend);
                    return;
                }
                //不为空，则发送create请求
                request.setDownloadExtendId(downloadExtend.getId());
                RestResult restResult = BasicInfoUtil.replaceUserToInvoke(
                        () -> userFeginService.createUsers(request)
                        , parentUser.getUserSid());
                //返回
                if (restResult != null) {
                    if (restResult.getStatus()) {
                        subUserTemplate.setResult("success");
                    } else {
                        subUserTemplate.setFailInfo(restResult.getMessage().toString());
                    }
                }
                subUserTemplate.setPassword("*********");
                downloadExtend.setJsonData(JSON.toJSONString(subUserTemplate));
                bizDownloadExtentMapper.insert(downloadExtend);
            } catch (Exception e) {
                log.error("OrgServiceImpl.importSubUser-error-"+subUserTemplate.getAccount()+" 创建子用户出错： ", e);
                String failInfo = e.getMessage();
                if (e instanceof RetryableException || failInfo.contains("timed out")) {
                    subUserTemplate.setDownloadExtendId(downloadExtend.getId());
                }

                subUserTemplate.setFailInfo(failInfo);
                subUserTemplate.setPassword("*********");
                downloadExtend.setJsonData(JSON.toJSONString(subUserTemplate));
                bizDownloadExtentMapper.insert(downloadExtend);
            }
            //FIXME tr6暂时没有ldap逻辑，且原代码逻辑有问题，需要修复
        });
        return subUserTemplates;
    }

    private List<SubUserTemplate> importSubUser( List<SubUserTemplate> subUserTemplates, String type, AuthUser authUser, String downloadNum) {
        //查看有没有预开通权限
        boolean preOpenAuth = true;
        try {
            List<RoleModule> roleModules = roleModuleMapper.selectRoleModuleByUserSid(authUser.getUserSid());
            if(CollectionUtil.isEmpty(roleModules)){
                preOpenAuth = false;
            }else{
                List<String> cacheAuths = roleModules.stream().map(RoleModule::getModuleSid).collect(Collectors.toList());
                if(!cacheAuths.contains(AuthModule.BQ.BQ0111)){
                    preOpenAuth = false;
                }
            }
        } catch (Exception e) {
            log.info("OrgServiceImpl.importCustomer authUser is null");
        }

        //入库
        return importSubUser(subUserTemplates, type, preOpenAuth, downloadNum);
    }

    private CreateUsersRequest buildUserReq(User parentUser, SubUserTemplate subUser, List<Long> groupIds) {
        CreateUsersRequest request = new CreateUsersRequest();
        request.setOrgSid(parentUser.getOrgSid());
        request.setPassword(subUser.getPassword());
        request.setForceResetPwd(Objects.equals("firstlogin", subUser.getForceResetPwd()) ? true : false);
        request.setParentUserSid(parentUser.getUserSid());
        request.setPasswordRule(subUser.getPasswordRule());
        request.setGroupIds(groupIds);
        request.setUsers(new ArrayList<>());
        return request;
    }

    private CreateUsersRequest buildUserVOReq(List<SubUserTemplate> subUsers, CreateUsersRequest request) {
        for (SubUserTemplate subUser : subUsers) {
            CreateUserVO userVO = new CreateUserVO();
            //检验通过才执行set
            if (subUser.getFalg()) {
                userVO.setMobile(subUser.getPhone());
                userVO.setAccount(subUser.getAccount());
                userVO.setEmail(subUser.getEmail());
                if(subUser.getBusinessTag() != null && subUser.getBusinessTag().equalsIgnoreCase(BusinessTagEnum.preOpen.getTag())){
                    userVO.setIsPreOpen(true);
                }else{
                    userVO.setIsPreOpen(false);
                }
                request.getUsers().add(userVO);
            }

        }
        return request;
    }

    private User buildUser(CreateBillingAccountRequest request) {
        User user = new User();
        String account = request.getAccount();
        checkUserAccount(account);
        user.setAccount(account);
        user.setEmail(request.getEmail());
        user.setMobile(request.getContactNumber());
        user.setPassword(request.getPassword());
        if (Objects.nonNull(request.getForceResetPwd())) {
            user.setForceResetPwd(request.getForceResetPwd().toString());
        }
        return user;
    }

    //检查用户名(账户名)
    private void checkUserAccount(String insertAccount) {
        if (StringUtil.isEmpty(insertAccount)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_609490706));
        }

        String reg = "^[a-zA-Z][a-zA-Z0-9-_.]{3,15}$";
        if (!insertAccount.matches(reg)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1595981882));
        }

        if (StringUtils.startsWithIgnoreCase(insertAccount, "admin")
                || StringUtils.startsWithIgnoreCase(insertAccount, "test")) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_112246097));
        }

        List<User> userList = userService.findUserByAccount(insertAccount);
        if (!CollectionUtil.isEmpty(userList)) {
            throw new BizException(WebUtil.getMessage(MsgCd.USER_ACCOUNT_USED));
        }
    }

    private Company buildCompany(CreateBillingAccountRequest request) {
        Company company = new Company();
        company.setCompanyName(request.getCompanyName());
        company.setOrgName(request.getCompanyName());
        company.setContactName(request.getContactName());
        company.setBusiness(request.getBusiness());
        company.setContactPhone(request.getContactNumber());
        company.setLicense(request.getLicense());
        company.setCompanyCode(request.getCompanyCode());
        company.setAddress(request.getAddress());
        company.setIndustryType(request.getIndustryType());
        company.setApplicationScenario(request.getApplicationScenario());
        company.setPersonnelSize(request.getPersonnelSize());
        company.setSolution(request.getSolution());
        company.setContactPhone(request.getContactPhone());
        company.setContactEmail(request.getEmail());
        company.setRefOrgId(request.getRefOrgId());
        return company;
    }

    private String importCheckUserAccount(String account) {
        if (StringUtil.isEmpty(account)) {
            return "用户名不能为空";
        }

        String reg = "^[a-zA-Z][a-zA-Z0-9-_.]{0,31}$";
        if (!account.matches(reg)) {
            return "用户名无效";
        }

        if (StringUtils.startsWithIgnoreCase(account, "admin")
                || StringUtils.startsWithIgnoreCase(account, "test")) {
            return "用户名不能以admin，test开头";
        }

        List<User> userList = userService.findUserByAccount(account);
        if (!CollectionUtil.isEmpty(userList)) {
            return WebUtil.getMessage(MsgCd.USER_ACCOUNT_USED);
        }
        return "";
    }

    private Boolean checkBillingAccount(List<BillingAccountTemplate> bizBillingAccounttempliates) {
        Boolean falg = Boolean.FALSE;
        for (BillingAccountTemplate accountTemplate : bizBillingAccounttempliates) {
            //校验非空字段是否为空
            if (isCheckNull(accountTemplate)) {
                continue;
            }
            //校验数据格式
            falg = validate(accountTemplate);
        }
        return falg;
    }

    private Boolean isCheckNull(Object object) {
        Class clazz = (Class) ClassUtils.getUserClass(object);
        Field fields[] = clazz.getDeclaredFields();
        Boolean flag = Boolean.FALSE;
        String fieldName = "";
        for (Field field : fields) {
            ReflectionUtils.makeAccessible(field);
            try {
                Object fieldValue = field.get(object);
                fieldName = field.getName();
                if (includeFieldName(fieldName, fieldValue)) {
                    continue;
                }
                if (StringUtil.isNullOrEmpty(fieldValue)) {
                    flag = Boolean.TRUE;
                    break;
                }
            } catch (IllegalAccessException e) {
                log.error(e.getMessage());
            }
        }
        if (object instanceof BillingAccountTemplate) {
            BillingAccountTemplate accountTemplate = (BillingAccountTemplate) object;
            if (flag) {
                if (Objects.equals("account", fieldName)) {
                    accountTemplate.setResult("failed");
                    accountTemplate.setFailInfo("用户名不能为空");
                }
                if (Objects.equals("companyName", fieldName)) {
                    accountTemplate.setResult("failed");
                    accountTemplate.setFailInfo("企业名称不能为空");
                }
                if (Objects.equals("contactName", fieldName)) {
                    accountTemplate.setResult("failed");
                    accountTemplate.setFailInfo("企业联系人不能为空");
                }
                if (Objects.equals("contactNumber", fieldName)) {
                    accountTemplate.setResult("failed");
                    accountTemplate.setFailInfo("企业电话不能为空");
                }
                if (Objects.equals("email", fieldName)) {
                    accountTemplate.setResult("failed");
                    accountTemplate.setFailInfo("邮箱不能为空");
                }
                if (Objects.equals("password", fieldName)) {
                    accountTemplate.setResult("failed");
                    accountTemplate.setFailInfo("密码不能为空");
                }
                if (Objects.equals("companyCode", fieldName)) {
                    accountTemplate.setResult("failed");
                    accountTemplate.setFailInfo("企业代码不能为空");
                }
            }
        }
        if (object instanceof SubUserTemplate) {
            SubUserTemplate subUser = (SubUserTemplate) object;
            if (Objects.equals("parentAccount", fieldName)) {
                subUser.setResult("failed");
                subUser.setFailInfo("主账户名不能为空");
            }
            if (Objects.equals("account", fieldName)) {
                subUser.setResult("failed");
                subUser.setFailInfo("用户名不能为空");
            }
            if (Objects.equals("userName", fieldName)) {
                subUser.setResult("failed");
                subUser.setFailInfo("姓名不能为空");
            }
            if (Objects.equals("email", fieldName)) {
                subUser.setResult("failed");
                subUser.setFailInfo("邮箱不能为空");
            }
            if (Objects.equals("phone", fieldName)) {
                subUser.setResult("failed");
                subUser.setFailInfo("电话不能为空");
            }
            if (Objects.equals("passwordRule", fieldName)) {
                subUser.setResult("failed");
                subUser.setFailInfo("密码规则不能为空");
            }
            if (Objects.equals("password", fieldName)) {
                subUser.setResult("failed");
                subUser.setFailInfo("密码不能为空");
            }
            if (Objects.equals("groupIds", fieldName)) {
                subUser.setResult("failed");
                subUser.setFailInfo("用户组不能为空");
            }
            if (Objects.equals("forceResetPwd", fieldName)) {
                subUser.setResult("failed");
                subUser.setFailInfo("密码重置策略不能为空");
            }
        }
        return flag;
    }

    private static boolean includeFieldName(String fieldName, Object fieldValue) {
        boolean anyMatch = Stream.of("address",
            "business",
            "distributorId",
            "remark",
            "result",
            "failInfo",
            "salesmen",
            "groupId",
            "subUserSzie",
            "subUsers",
            "downloadId",
            "license",
            "businessTag").anyMatch(name -> Objects.equals(fieldName, name));
        return  anyMatch || (Objects.equals(fieldName, "passwordRule") && Objects.equals("none", fieldValue));
    }

    private Boolean validate(SubUserTemplate subUser) {
        String checkUserAccount = importCheckUserAccount(subUser.getAccount());
        if (StringUtils.isNotBlank(checkUserAccount)) {
            subUser.setResult("failed");
            subUser.setFailInfo(checkUserAccount);
            subUser.setFalg(Boolean.FALSE);
            return Boolean.FALSE;
        }

        List<User> userAccount = userService.findUserByAccount(subUser.getParentAccount());
        if (CollectionUtils.isEmpty(userAccount)) {
            subUser.setResult("failed");
            subUser.setFailInfo("没有该主账户");
            subUser.setFalg(Boolean.FALSE);
            return Boolean.FALSE;
        }

        List<User> emailV = userService.findUserByEmail(subUser.getEmail());
        if (!CollectionUtils.isEmpty(emailV)) {
            subUser.setResult("failed");
            subUser.setFailInfo("该邮箱已被使用");
            subUser.setFalg(Boolean.FALSE);
            return Boolean.FALSE;
        }

        if (!validatePhone(subUser.getPhone())) {
            subUser.setResult("failed");
            subUser.setFailInfo("手机号格式不正确");
            subUser.setFalg(Boolean.FALSE);
            return Boolean.FALSE;
        }

        List<User> mobiles = userService.findUserByMobile(subUser.getAccount());
        if (!CollectionUtils.isEmpty(mobiles)) {
            subUser.setResult("failed");
            subUser.setFailInfo("该电话号码已被使用");
            subUser.setFalg(Boolean.FALSE);
            return Boolean.FALSE;
        }
        if (subUser.getGroupIds().contains("，")) {
            subUser.setResult("failed");
            subUser.setFailInfo("用户组ID请使用英文逗号隔开");
            subUser.setFalg(Boolean.FALSE);
            return Boolean.FALSE;
        } else {
            List<String> groupNames = Arrays.stream(subUser.getGroupIds().split(",")).map(group -> {
                if (Objects.equals(group, "ADMIN_GROUP")) {
                    return "管理员组";
                }
                if (Objects.equals(group, "RESOURCE_GROUP")) {
                    return "资源管理员组";
                }
                if (Objects.equals(group, "PROJECT_GROUP")) {
                    return "项目管理员组";
                }
                if (Objects.equals(group, "PROJECT_MEMBER_GROUP")) {
                    return "项目成员组";
                }
                if (Objects.equals(group, "READ_ONLY_GROUP")) {
                    return "只读成员组";
                }
                return group;
            }).collect(Collectors.toList());
            subUser.setGroupId(sysGroupMapper.getByNames(groupNames));
        }
        return Boolean.TRUE;
    }

    private Boolean validate(BillingAccountTemplate accountTemplate) {
        String checkUserAccount = importCheckUserAccount(accountTemplate.getAccount());
        if (StringUtils.isNotBlank(checkUserAccount)) {
            accountTemplate.setResult("failed");
            accountTemplate.setFailInfo(checkUserAccount);
            accountTemplate.setFalg(Boolean.FALSE);
            return Boolean.FALSE;
        }

        String tag = accountTemplate.getBusinessTag();
        if (StringUtils.isNotBlank(tag) && !tag.equalsIgnoreCase(BusinessTagEnum.preOpen.getTag())) {
            accountTemplate.setResult("failed");
            accountTemplate.setFailInfo("预开通列数据不正确");
            accountTemplate.setFalg(Boolean.FALSE);
            return Boolean.FALSE;
        }

        Criteria criteria = new Criteria();
        criteria.put("orgName", accountTemplate.getCompanyName());
        List<Org> orgs = orgMapper.selectByParams(criteria);
        if (!CollectionUtils.isEmpty(orgs)) {
            accountTemplate.setResult("failed");
            accountTemplate.setFailInfo("企业名称已被使用");
            accountTemplate.setFailInfo("公司名称已被使用");
            accountTemplate.setFalg(Boolean.FALSE);
            return Boolean.FALSE;
        }

        if (!validatePhone(accountTemplate.getContactNumber())) {
            accountTemplate.setResult("failed");
            accountTemplate.setFailInfo("手机号格式不正确");
            accountTemplate.setFalg(Boolean.FALSE);
            return Boolean.FALSE;
        }

        String businessTag = accountTemplate.getBusinessTag();
        if(StringUtils.isBlank(businessTag) || !"preOpen".equalsIgnoreCase(businessTag)){
            List<User> mobiles = userService.findUserByMobile(accountTemplate.getAccount());
            if (!CollectionUtils.isEmpty(mobiles)) {
                accountTemplate.setResult("failed");
                accountTemplate.setFailInfo("该电话号码已被使用");
                accountTemplate.setFalg(Boolean.FALSE);
                return Boolean.FALSE;
            }
            List<User> emailV = userService.findUserByEmail(accountTemplate.getEmail());
            if (!CollectionUtils.isEmpty(emailV)) {
                accountTemplate.setResult("failed");
                accountTemplate.setFailInfo("该邮箱已被使用");
                accountTemplate.setFalg(Boolean.FALSE);
                return Boolean.FALSE;
            }
            ValidationResult result = ValidationUtils.validateEntity(accountTemplate);
            if (result.isHasErrors()) {
                accountTemplate.setResult("failed");
                accountTemplate.setFailInfo(result.getErrorMsg());
                accountTemplate.setFalg(Boolean.FALSE);
                return Boolean.FALSE;
            }
        }
        if (Objects.equals(accountTemplate.getAccount(), accountTemplate.getCompanyName())) {
            accountTemplate.setResult("failed");
            accountTemplate.setFailInfo("企业名称和企业联系人不能同名");
            accountTemplate.setFalg(Boolean.FALSE);
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    private boolean validateEmail(String email) {
        return Pattern.compile("^[a-zA-Z0-9_.-]{4,16}@[a-zA-Z0-9-]+(\\.[a-zA-Z0-9-]+)*\\.[a-zA-Z0-9]{2,6}$")
                .matcher(email)
                .matches();
    }

    private Boolean validatePhone(String phone) {
        String regex = "^(13[0-9]|14[********]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\\d{8}$";
        Pattern compile = Pattern.compile(regex);
        Matcher matcher = compile.matcher(phone);
        return matcher.matches();
    }

    @Override
    public <T> void setOwnerOrgName(List<T> list) {
        for (T e : list) {
            Long orgSid = Convert.toLong(ReflectUtil.getFieldValue(e, "orgSid"));
            if (Objects.nonNull(orgSid)) {
                Org org = orgMapper.selectByPrimaryKey(orgSid);
                if (Objects.isNull(org)) {
                    continue;
                }
                long[] orgIds = StrUtil.splitToLong(org.getTreePath(), CharUtil.SLASH);
                List<Org> orgs = Lists.newArrayList();
                if (ArrayUtil.isNotEmpty(orgIds)) {
                    Criteria criteria = new Criteria();
                    criteria.put("orgIds", orgIds);
                    orgs = orgMapper.selectByParamsNoAuth(criteria);
                }
                orgs.add(org);
                String ownerOrgNames = orgs.stream().sorted(Comparator.comparing(Org::getOrgSid))
                        .map(Org::getOrgName).distinct().collect(Collectors.joining(StrUtil.SLASH));
                ReflectUtil.setFieldValue(e, "ownerOrgName", ownerOrgNames);
            }
        }
    }

    @Override
    public boolean updateBillingAccount(UpdateAccountRequest request) {
        BizBillingAccount account = bizBillingAccountMapper.selectByPrimaryKey(request.getId());
        if (Objects.isNull(account)) {
            throw new BizException("企业不存在");
        }
        if (StringUtils.isNotBlank(request.getCompanyName()) && !account.getAccountName()
                                                                        .equals(request.getCompanyName())) {
            BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectByAccountName(request.getCompanyName());
            if (Objects.nonNull(bizBillingAccount)) {
                throw new BizException("企业名称已被使用");
            } else {
                account.setAccountName(request.getCompanyName());
                bizBillingAccountMapper.updateByPrimaryKey(account);
            }
        }
        Org rootOrg = orgMapper.selectByPrimaryKey(account.getOrgSid());
        if (Objects.nonNull(rootOrg) && !request.updatePropertiesEmpty()) {
            // 修改org
            Org org = new Org();
            org.setOrgSid(account.getOrgSid());
            org.setContactName(request.getContactName());
            org.setContactPhone(request.getContactPhone());
            org.setContactEmail(request.getContactEmail());
            org.setPersonnelSize(request.getPersonnelSize());
            org.setIndustryType(request.getIndustryType());
            org.setAddress(request.getAddress());
            org.setApplicationScenario(request.getApplicationScenario());
            org.setSolution(request.getSolution());
            this.updateByPrimaryKeySelective(org);
            iamRemoteService.updateOrg(BeanConvertUtil.convert(org, IamOrg.class));

            if (StringUtils.isNotBlank(org.getContactName()) || StringUtils.isNotBlank(org.getContactPhone())
                    || StringUtils.isNotBlank(org.getContactEmail())) {
                SysOrgUpdateForm sysOrgUpdateForm = new SysOrgUpdateForm();
                sysOrgUpdateForm.setContactName(org.getContactName());
                sysOrgUpdateForm.setContactPhone(org.getContactPhone());
                sysOrgUpdateForm.setContactEmail(org.getContactEmail());
                RestResult<Void> voidRestResult = systemFeignService.updateOrg(account.getOrgSid(), sysOrgUpdateForm);
                if (!voidRestResult.getStatus()) {
                    throw new BizException(voidRestResult.getMessage().toString());
                }
            }

        }
        return true;
    }

    /**
     * 修改组织的Bms策略
     * @param updateBmsRequest
     * @return RestResult
     */
    @Override
    public boolean bmsStrategy(UpdateBmsRequest updateBmsRequest) {
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        List<Long> entityId = serviceCategoryMapper.findEntityByProductCode("MA-BMS");
        if(!entityId.contains(authUser.getEntityId())){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
        }
        Long userSid = updateBmsRequest.getUserSid();
        User user = userMapper.selectByPrimaryKey(userSid);
        //如果是分销商管理员，或子用户，或运营侧用户则抛错
        if (Objects.isNull(user)) {
            BizException.e(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        List<Org> orgs = orgMapper.selectAllOrgSid(updateBmsRequest.getOrgSid());
        if (CollectionUtils.isEmpty(orgs) || orgs.stream().noneMatch(e -> Objects.equals(e.getOrgSid(), user.getOrgSid()))) {
            BizException.e(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        if (ObjectUtils.isEmpty(user.getOrgSid())
                || !ObjectUtils.isEmpty(user.getParentSid())
                || "04".equals(user.getUserType())) {
            BizException.e("该用户没有对应Bms策略");
        }
        Long bmsEnable = updateBmsRequest.getBmsEnable();

        if(!this.checkUser(user, bmsEnable, orgs)) {
            return false;
        }

        if (bmsEnable - orgs.get(0).getBmsEnable() == 0) {
            // 状态相同的情况只更新策略和缓冲期
            orgs.forEach(org -> {
                orgMapper.updaterBmsStrategyById(org.getOrgSid(), bmsEnable, updateBmsRequest.getStrategyBufferPeriod(), updateBmsRequest.getFreezingStrategy());
            });
            return true;
        }


        //获取bms 自定义权限限制配置
        SysConfig sysConfig = sysConfigMapper.selectByConfigKey(IamDomainPermissionsEnum.CUSTOM_MODELARTS_DENY.getValue());
        if (sysConfig == null) {
            log.error("OrgServiceImpl.bmsStrategy bms 配置没找到");
            return false;
        }

        Map<String, Object> policy = JSON.parseObject(sysConfig.getConfigValue());
        // 开启bms时 移除限制权限， 关闭时添加限制权限
        boolean flag = iamPermissionService.operationUserCustomAuthority(updateBmsRequest.getUserSid(), policy, bmsEnable != 1);
        if (!flag) {
            return flag;
        }
        flag = iamPermissionService.operationUserMABMSAuthority(updateBmsRequest.getUserSid(), bmsEnable == 1);
        if (!flag) {
            return flag;
        }

        orgs.forEach(org -> {
            orgMapper.updaterBmsStrategyById(org.getOrgSid(), bmsEnable, updateBmsRequest.getStrategyBufferPeriod(), updateBmsRequest.getFreezingStrategy());
        });

        threadPoolTaskExecutor.execute(() -> {
            if (bmsEnable == 0) {
                Long orgSid = updateBmsRequest.getOrgSid();
                // 关闭需要删除 bms 资源
                rabbitTemplate.convertAndSend(ACCOUNT_FREEZE_PUSH_EXCHANGE, ACCOUNT_FREEZE_PUSH_MONITOR_FREEZE, "bmsStrategy,releaseResource,delBms," + orgSid);
            }

            String msg1 = bmsEnable == 0 ? NotificationConsts.ConsoleMsg.ProductMsg.TENANT_BMS_CLOSE : NotificationConsts.ConsoleMsg.ProductMsg.TENANT_BMS_OPEN;
            String msg2 = bmsEnable == 0 ? NotificationConsts.PlatformMsg.AccountMsg.BSSMGT_BMS_CLOSE : NotificationConsts.PlatformMsg.AccountMsg.BSSMGT_BMS_OPEN;
            Map<String, String> messageContent = new HashMap<>(8);
            messageContent.put("userAccount", user.getAccount());
            iamPermissionService.sendMessage(user, messageContent, msg1, msg2, RequestContextUtil.getEntityId());
        });

        return true;
    }

    private boolean checkUser(User user, Long bmsEnable,  List<Org> orgs) {
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        // 越权判断
        if (authUser == null || !StringUtils.equals("bss", authUser.getRemark())) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }

        boolean flag = true;
        if (user == null || !"1".equals(user.getStatus())) {
            log.error("OrgServiceImpl.checkUser 用户数据不存在或者状态不正确");
            return false;
        }

        if (CollectionUtil.isEmpty(orgs)) {
            log.error("OrgServiceImpl.checkUser 组织数据不存在");
            return false;
        }

        // 判断是否定价
        List<DescribeBillingStrategyServingSimpleResponse> servings = new ArrayList<>();
        DescribeBizBillingStrategyServingRequest request = new DescribeBizBillingStrategyServingRequest();
        request.setServiceType("MA-BMS");
        RestResult restResult = feignService.queryBillingServingConfig(request);
        if (restResult.getStatus()) {
            servings = BeanConvertUtil.convert((List<?>) restResult.getData(), DescribeBillingStrategyServingSimpleResponse.class);
        }

        if (bmsEnable != 1) {
            return true;
        }

        if (CollectionUtil.isEmpty(servings) || Objects.isNull(servings.get(0)) || !servings.get(0).getAccountPriced()) {
            log.error("OrgServiceImpl.checkUser 产品未定价 data: {}", servings == null ? "null": JsonUtil.toJson(servings));
            throw new BizException(WebUtil.getMessage(MsgCd.ERROR_PRODUCT_NO_PRICE));
        }

        Long userSid = user.getUserSid();
        int count = sfProductResourceMapper.countResourceByOrgsSidAndProductType(orgs.stream().map(Org::getOrgSid).collect(Collectors.toList()),
                ProductCodeEnum.MODEL_ARTS.getProductCode());
        if (count <= 0) {
            throw new BizException(WebUtil.getMessage(MsgCd.PLEASE_OPEN_MA_RESOURCE));
        }

        BizBillingAccount account = bizBillingAccountMapper.getByUserSidAndEntityId(userSid, RequestContextUtil.getEntityId());
        if ("freeze".equals(account.getStatus())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ACCOUNT_FREEZE));
        }

        if (Objects.isNull(account.getBalance()) || account.getBalance().compareTo(BigDecimal.ZERO) < 0) {
            throw new BizException(WebUtil.getMessage(MsgCd.ACCOUNT_ARREARS));
        }

        return flag;
    }

    /**
     * 增加BMS白名单策略
     * @param
     * @return boolean
     */
    @Override
    public boolean createBmsPool(UserExceptionResourceRequest userExceptionResourceRequest) {
        ServiceCategory serviceCategory = serviceCategoryMapper.selectByServiceType(ProductCodeEnum.MA_BMS.getProductCode());
        if (serviceCategory == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1226065223));
        }

        if (serviceCategory.getEntityId() != null && serviceCategory.getEntityId() - RequestContextUtil.getEntityId() != 0) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }

        Date endTime = cn.hutool.core.date.DateUtil.parse(userExceptionResourceRequest.getEndTime(), DatePattern.NORM_DATETIME_PATTERN);
        if (cn.hutool.core.date.DateUtil.compare(endTime, new Date()) <= 0) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_940920911));
        }

        Long userSid = userExceptionResourceRequest.getUserSid();
        User user = userMapper.findUserById(userSid);
        if (user == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1903178708));
        }

        UserExceptionResource userExceptionResource = BeanConvertUtil.convert(userExceptionResourceRequest,
                                                                UserExceptionResource.class);
        userExceptionResource.setEndTime(endTime);
        //hcso_user accountId
        HcsoUser hcsoUser = hcsoUserRemoteService.selectByRefUserId(userSid);
        if(hcsoUser == null){
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        userExceptionResource.setAccountId(hcsoUser.getAccountId());
        userExceptionResource.setStatus(Long.valueOf(StatusEnum.ENABLE.getValue()));
        WebUserUtil.prepareInsertParams(userExceptionResource);
        int i = userExceptionResourceMapper.insertResource(userExceptionResource);
        if(i>0){
            return true;
        }
        return false;
    }

    /**
     * 删除BMS白名单
     * @param id
     * @return boolean
     */
    @Override
    public boolean deleteBmsPool(Long id, Long orgSid) {
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        // 越权判断
        if (authUser == null || !StringUtils.equals("bss", authUser.getRemark())) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }

        ServiceCategory serviceCategory = serviceCategoryMapper.selectByServiceType(ProductCodeEnum.MA_BMS.getProductCode());
        if (serviceCategory == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1226065223));
        }

        if (serviceCategory.getEntityId() != null && serviceCategory.getEntityId() - RequestContextUtil.getEntityId() != 0) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }

        UserExceptionResource userExceptionResource = userExceptionResourceMapper.selectBmsByIdAndOrgSid(orgSid, id, RequestContextUtil.getEntityId());
        if (userExceptionResource == null) {
            log.error("OrgServiceImpl.deleteBmsPool 删除bms资源失败-资源数据不存在");
            return false;
        }

        int i = userExceptionResourceMapper.delBmsRrsourceById(Long.valueOf(StatusEnum.DISABLE.getValue()),id);
        if (i > 0){
            return true;
        }

        return false;
    }

    /**
     * 查询白名单
     * @param criteria criteria
     * @return UserExceptionResource
     */
    @Override
    public  List<UserExceptionResource> selectBmsResource(Criteria criteria) {
        return userExceptionResourceMapper.selectBmsByCriteria(criteria);

    }

    @Override
    public void userMaAuthorityUpgrade() {
        //设置同步锁
        Boolean lock = redisTemplate.opsForValue().setIfAbsent(USER_MA_BMS_UP_LOCK, "1", 1, TimeUnit.DAYS);
        if (!lock) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_183737680));
        }

        log.info("OrgServiceImpl.userMaAuthorityUpgrade 用户MA权限升级");

        try {
            //获取bms 自定义权限配置
            SysConfig sysConfig = sysConfigMapper.selectByConfigKey(IamDomainPermissionsEnum.CUSTOM_MODELARTS_DENY.getValue());
            if (sysConfig == null) {
                log.error("OrgServiceImpl.userMaAuthorityUpgrade bms 配置没找到-升級結束");
                return;
            }
            Map<String, Object> policy = JSON.parseObject(sysConfig.getConfigValue());

            cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria criteria
                    = new cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria();
            List<HcsoUser> hcsoUserList = hcsoUserRemoteService.selectByParams(criteria);
            List<Long> users = hcsoUserList.stream().filter(e -> e.getRefUserId() != null && "used".equals(e.getStatus()))
                    .map(HcsoUser::getRefUserId).collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(users)) {
                String successIds = JedisUtil.INSTANCE.get(USER_MA_BMS_UP);
                if (StringUtils.isNotBlank(successIds)) {
                    // 去除添加成功用户
                    users = users.stream().filter(e -> !successIds.contains(e+"")).collect(Collectors.toList());
                }
                if (CollectionUtil.isEmpty(users)) {
                    log.error("OrgServiceImpl.userMaAuthorityUpgrade bms 配置没找到-沒有同步的用户");
                    return;
                }

                int size = users.size();
                log.info("OrgServiceImpl.userMaAuthorityUpgrade 升级用户数： {}", size);
                //线程池
                ExecutorService executorService = new ThreadPoolExecutor(4, 4,
                        10L, TimeUnit.MILLISECONDS,
                        new LinkedBlockingQueue<>(size),
                        new ThreadFactoryBuilder()
                                .setNameFormat("user-ma-bms-pool-%d")
                                .build());
                final CountDownLatch maxCountDown = new CountDownLatch(size);

                Set<Long> failUsers = new HashSet<>();
                Set<Long> successlUsers = new HashSet<>();
                for (Long userSid : users) {
                    executorService.execute(() -> {
                        try {
                            boolean flag = iamPermissionService.operationUserCustomAuthority(userSid, policy, true);
                            if (!flag) {
                                failUsers.add(userSid);
                            } else {
                                successlUsers.add(userSid);
                            }
                        } finally {
                            maxCountDown.countDown();
                        }
                    });
                }

                try {
                    maxCountDown.await();
                } catch (InterruptedException e) {
                    log.error("UserServiceImpl.reMapIamUser-唤醒线程异常！", e);
                    Thread.currentThread().interrupt();
                }

                if (failUsers.size() > 0) {
                    log.info("OrgServiceImpl.userMaAuthorityUpgrade 升级失败的用户： {}", JSON.toJSONString(failUsers));
                }

                JedisUtil.INSTANCE.set(USER_MA_BMS_UP, JSON.toJSONString(successlUsers));
            }
        } finally {
            log.error("OrgServiceImpl.userMaAuthorityUpgrade 删除同步锁");
            redisTemplate.delete(USER_MA_BMS_UP_LOCK);
        }

        log.info("OrgServiceImpl.userMaAuthorityUpgrade 升級結束");
    }

    @Override
    public List<Org> findSimpleAll() {
        return orgMapper.findSimpleAll();
    }
}
