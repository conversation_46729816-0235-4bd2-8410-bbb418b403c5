/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.module.system.bean.syslog.response;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.io.Serializable;
import java.util.Date;

import cn.com.cloudstar.rightcloud.common.util.CrytoUtilSimple;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import cn.com.cloudstar.rightcloud.common.util.CrytoUtilSimple;
import cn.com.cloudstar.rightcloud.oss.common.util.StringUtil;

/**
 * <AUTHOR>
 */

@Data
@ApiModel(description = "系统日志")
public class SysLogListResponse implements Serializable {

    /**
     * 文件名称"
     */
    @ApiModelProperty("文件名称")
    private String name;
    /**
     * 文件全称
     */
    @ApiModelProperty("文件全称")
    @JsonIgnore
    private String fullName;
    /**
     * 模块名称
     */
    @ApiModelProperty("模块名称")
    private String moduleName;
    /**
     * 日志级别
     */
    @ApiModelProperty("日志级别")
    private String logLevel;
    /**
     * 文件类型
     */
    @ApiModelProperty("文件类型")
    private String fileType;
    /**
     * 文件大小
     */
    @ApiModelProperty("文件大小")
    private String fileSize;
    /**
     * 更新日期
     */
    @ApiModelProperty("更新时间")
    private Date updateDate;

    /**
     * add by SongChao 安全对应 对path等路径不能文明暴露
     */
    public void setFullName(String fullName) {
        if (StringUtil.isNotEmpty(fullName))
            this.fullName = CrytoUtilSimple.encrypt(fullName);
    }
}
