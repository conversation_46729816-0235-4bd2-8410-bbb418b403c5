/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.module.account.controller.user;

import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.IamCredentialResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.IamUserResult;
import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnv;
import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.ResCloudEnv;
import cn.com.cloudstar.rightcloud.basic.data.pojo.system.ActionLog;
import cn.com.cloudstar.rightcloud.common.annotation.ListenExpireBack;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.BQ;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.BQ.BQ03;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.BU;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.CB;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.CH;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.COMMON.PUBLIC.B1;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.USER;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.ZB;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.ZC;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.ZF;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.ZF.ZF02;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.ZK;
import cn.com.cloudstar.rightcloud.common.constants.NotificationConsts;
import cn.com.cloudstar.rightcloud.common.constants.NotificationConsts.PlatformMsg.AccountMsg;
import cn.com.cloudstar.rightcloud.common.constants.RedisLockConstants;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.constants.res.status.ResHpcClusterStatus;
import cn.com.cloudstar.rightcloud.common.mq.request.BaseNotificationMqBean;
import cn.com.cloudstar.rightcloud.common.mq.request.ImsgNotificationMq;
import cn.com.cloudstar.rightcloud.common.mq.request.MailNotificationMq;
import cn.com.cloudstar.rightcloud.common.util.AssertUtil;
import cn.com.cloudstar.rightcloud.common.util.CrytoUtilSimple;
import cn.com.cloudstar.rightcloud.common.util.MapsKit;
import cn.com.cloudstar.rightcloud.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.common.util.excel.ExcelUtil;
import cn.com.cloudstar.rightcloud.common.util.json.JsonUtil;
import cn.com.cloudstar.rightcloud.core.action.ActionLogTracker;
import cn.com.cloudstar.rightcloud.core.annotation.idempotent.Idempotent;
import cn.com.cloudstar.rightcloud.core.annotation.log.LogParam;
import cn.com.cloudstar.rightcloud.core.annotation.log.SmsValidation;
import cn.com.cloudstar.rightcloud.core.pojo.dto.charge.BillingAccount;
import cn.com.cloudstar.rightcloud.core.pojo.dto.charge.BizBillingAccount;
import cn.com.cloudstar.rightcloud.core.pojo.dto.file.SysMFilePath;
import cn.com.cloudstar.rightcloud.core.pojo.dto.operate.EntityUser;
import cn.com.cloudstar.rightcloud.core.pojo.dto.saas.SysSaasOrg;
import cn.com.cloudstar.rightcloud.core.pojo.dto.sfs.ServiceCategory;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.Code;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.SysConfig;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.UserGroup;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.BizInvoice;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.Company;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.IamAccessKeyCsv;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.LdapUserDTO;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.Module;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.ObsIamUserAK;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.ObsIamUserCreate;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.Org;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.OrgIdp;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.PasswordPolicyDTO;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.Role;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.SysBssEntity;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.User;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.UserPrivacySign;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.UserRole;
import cn.com.cloudstar.rightcloud.module.support.access.constants.DataScopeEnum;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicInfoUtil;
import cn.com.cloudstar.rightcloud.module.support.file.storage.bean.vo.StorageResult;
import cn.com.cloudstar.rightcloud.module.support.file.storage.service.StorageService;
import cn.com.cloudstar.rightcloud.operationlog.annotation.OperationLog;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationTypeEnum;
import cn.com.cloudstar.rightcloud.operationlog.util.OperationLogMdcUtil;
import cn.com.cloudstar.rightcloud.oss.common.annotation.AuthorizeOss;
import cn.com.cloudstar.rightcloud.oss.common.annotation.DataPermission;
import cn.com.cloudstar.rightcloud.oss.common.annotation.RejectCall;
import cn.com.cloudstar.rightcloud.oss.common.api.ApiGroup;
import cn.com.cloudstar.rightcloud.oss.common.api.ApiGroupEnum;
import cn.com.cloudstar.rightcloud.oss.common.cache.JedisUtil;
import cn.com.cloudstar.rightcloud.oss.common.ccsp.CCSPCacheUtil;
import cn.com.cloudstar.rightcloud.oss.common.constants.AuthConstants;
import cn.com.cloudstar.rightcloud.oss.common.constants.ModuleTypeConstants;
import cn.com.cloudstar.rightcloud.oss.common.constants.RestConst;
import cn.com.cloudstar.rightcloud.oss.common.constants.RestConst.HttpConst;
import cn.com.cloudstar.rightcloud.oss.common.constants.SysConfigConstants;
import cn.com.cloudstar.rightcloud.oss.common.constants.UserRoleConstants;
import cn.com.cloudstar.rightcloud.oss.common.constants.WebConstants;
import cn.com.cloudstar.rightcloud.oss.common.constants.status.CertificationStatus;
import cn.com.cloudstar.rightcloud.oss.common.constants.status.IdentityTypeStatus;
import cn.com.cloudstar.rightcloud.oss.common.constants.status.UserStatus;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.CloudEnvTenantKey;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.OrgType;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.QuotaMode;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.RoleType;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.USER_AUTH_TYPE;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.UserType;
import cn.com.cloudstar.rightcloud.oss.common.encryptdata.encrypt.DesensitizationUtil;
import cn.com.cloudstar.rightcloud.oss.common.enums.BssRoleEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.BusinessTagEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.CertificationStatusEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.CertificationTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.FeaturesEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.PrivacyStatusEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.RoleEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.StatusTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.SysRoleEnum;
import cn.com.cloudstar.rightcloud.oss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.oss.common.pojo.LdapSyncRequest;
import cn.com.cloudstar.rightcloud.oss.common.pojo.LicenseVo;
import cn.com.cloudstar.rightcloud.oss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.oss.common.pojo.RestResult.Status;
import cn.com.cloudstar.rightcloud.oss.common.pojo.SysMUserPasswordHistory;
import cn.com.cloudstar.rightcloud.oss.common.util.AuthUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.DataProcessingUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.DataScopeUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.FileUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.IPAddressUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.LicenseUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.SaasUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.WebUserUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.ZipUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.cache.AuthUserHolder;
import cn.com.cloudstar.rightcloud.oss.common.util.encrypt.Encrypt;
import cn.com.cloudstar.rightcloud.oss.common.util.ldap.OpenLdapUser;
import cn.com.cloudstar.rightcloud.oss.common.util.sms.CheckSmsCodeUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.tree.TreeBuilder;
import cn.com.cloudstar.rightcloud.oss.common.util.tree.TreeNode;
import cn.com.cloudstar.rightcloud.oss.common.websocket.ServerMsgPublisher;
import cn.com.cloudstar.rightcloud.oss.module.access.service.PolicyService;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.model.BizDistributor;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.model.CreateUserVO;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.model.RoleSidVO;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.model.SysHpcPass;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.model.UserCertificationStatusVO;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.model.UserVO;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.InvitationRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user.AccessKeyRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user.ActivateUserRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user.ActivationForm;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user.AuthCompanyUserRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user.AuthUserRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user.CheckAndUpdatePhoneRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user.CheckPasswordRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user.ConfirmPrivacySignRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user.CreateCompanyUserApiRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user.CreateCompanyUserRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user.CreateUsersRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user.DeleteCompanyUserRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user.DeleteUserRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user.DescribeUserRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user.EmailRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user.EmailValidateRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user.FindPassByEmailRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user.FirstLoginRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user.ForgetPwdRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user.ObsIamUserList;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user.RefuseCompanyRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user.RefuseUserRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user.RegisterUserRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user.ResetAdminNameRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user.ResetPwdRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user.ResetValidPasswordRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user.SendEmailRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user.UpdateCompanyUserApiRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user.UpdateCompanyUserRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user.UpdateUserPwdRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user.UpdateUserStatus;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user.UserGroupCreate;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user.UserSensitiveRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user.UserUnLockRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user.ValidPasswordPolicyRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user.ValidateRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.response.ProductResourceHPCResponse;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.response.user.DescribeCompanyDetailResponse;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.response.user.DescribeEntityDistributorResponse;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.response.user.DescribeManagerUserSimpleResponse;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.response.user.DescribeUserDetailResponse;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.response.user.DescribeUserResponse;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.response.user.DescribeUserTokenResponse;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.response.user.IamUserNumResponse;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.response.user.PersonalAuthenticationResponse;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.response.user.ResUserCredentialResponse;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.valid.CompanyUserValidView;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.valid.ResetPasswordValidView;
import cn.com.cloudstar.rightcloud.oss.module.account.cfn.CfnService;
import cn.com.cloudstar.rightcloud.oss.module.account.cfn.dto.CreateUserReq;
import cn.com.cloudstar.rightcloud.oss.module.account.cfn.dto.DeleteUserReq;
import cn.com.cloudstar.rightcloud.oss.module.account.cfn.dto.ModifyStatusReq;
import cn.com.cloudstar.rightcloud.oss.module.account.cfn.dto.UserAccountStatusEnum;
import cn.com.cloudstar.rightcloud.oss.module.account.cfn.dto.UserStatusEnum;
import cn.com.cloudstar.rightcloud.oss.module.account.controller.oauth.util.EnCode;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.org.BizDistributorMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.org.CompanyMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.org.OrgMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.orgidp.OrgIdpMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.user.RoleMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.user.RoleModuleMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.user.SysHpcPassMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.user.UserMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.user.UserPrivacySignMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.user.UserRoleMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.enums.IamProductUrlEnum;
import cn.com.cloudstar.rightcloud.oss.module.account.enums.UserActivateFlowEnum;
import cn.com.cloudstar.rightcloud.oss.module.account.enums.UserSensitiveEnum;
import cn.com.cloudstar.rightcloud.oss.module.account.service.HpcFeignService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.IBizDistributorService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.company.CompanyService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.org.OrgService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.role.AuthService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.role.RoleService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.role.UserRoleService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.saas.SysSaasOrgService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.user.ISysMUserDelLogService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.user.IamPermissionService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.user.LdapUserService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.user.ModuleService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.user.SysBssEntityService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.user.UserExportService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.user.UserFeginService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.user.UserSecurityService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.user.UserService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.user.UserSyncService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.user.impl.UserServiceImpl;
import cn.com.cloudstar.rightcloud.oss.module.auth.dto.SysAuthModuleDTO;
import cn.com.cloudstar.rightcloud.oss.module.auth.response.RightCloudResult;
import cn.com.cloudstar.rightcloud.oss.module.bigscrean.bean.BigScreenRoleAuthModuleResult;
import cn.com.cloudstar.rightcloud.oss.module.export.bean.BizDownload;
import cn.com.cloudstar.rightcloud.oss.module.export.dao.BizDownloadMapper;
import cn.com.cloudstar.rightcloud.oss.module.feign.service.FeignService;
import cn.com.cloudstar.rightcloud.oss.module.feign.service.ResourceDcFeignService;
import cn.com.cloudstar.rightcloud.oss.module.file.dao.SysMFilePathMapper;
import cn.com.cloudstar.rightcloud.oss.module.invoice.dao.BizInvoiceMapper;
import cn.com.cloudstar.rightcloud.oss.module.msg.service.impl.SysMMsgReceiveContactServiceImpl;
import cn.com.cloudstar.rightcloud.oss.module.operate.bean.request.QueryResHpcClusterRequest;
import cn.com.cloudstar.rightcloud.oss.module.operate.dao.EntityUserMapper;
import cn.com.cloudstar.rightcloud.oss.module.operationcore.service.process.ProcessService;
import cn.com.cloudstar.rightcloud.oss.module.order.service.ServiceOrderService;
import cn.com.cloudstar.rightcloud.oss.module.order.util.IDUtils;
import cn.com.cloudstar.rightcloud.oss.module.pricing.dao.priceconfig.BizBillingAccountMapper;
import cn.com.cloudstar.rightcloud.oss.module.pricing.service.priceconfig.BillingAccountService;
import cn.com.cloudstar.rightcloud.oss.module.pricing.service.priceconfig.BizBillingAccountService;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.dao.ServiceCategoryMapper;
import cn.com.cloudstar.rightcloud.oss.module.system.bean.config.response.DescribeBssEntityResponse;
import cn.com.cloudstar.rightcloud.oss.module.system.bean.config.response.DescribeCodeFullResponse;
import cn.com.cloudstar.rightcloud.oss.module.system.dao.config.AssertionMapper;
import cn.com.cloudstar.rightcloud.oss.module.system.dao.config.CodeMapper;
import cn.com.cloudstar.rightcloud.oss.module.system.dao.config.SysConfigMapper;
import cn.com.cloudstar.rightcloud.oss.module.system.dao.config.UserGroupMapper;
import cn.com.cloudstar.rightcloud.oss.module.system.service.config.SysConfigService;
import cn.com.cloudstar.rightcloud.oss.module.system.service.message.BusinessNotificationService;
import cn.com.cloudstar.rightcloud.oss.module.system.service.message.ISysOssMessageService;
import cn.com.cloudstar.rightcloud.oss.module.system.service.message.MessageService;
import cn.com.cloudstar.rightcloud.oss.module.system.service.syslog.IBizCustomerActionLogService;
import cn.com.cloudstar.rightcloud.oss.module.tcc.service.CancelService;
import cn.com.cloudstar.rightcloud.oss.util.Constants;
import cn.com.cloudstar.rightcloud.oss.util.PasswordUtil;
import cn.com.cloudstar.rightcloud.oss.util.PropertiesUtil;
import cn.com.cloudstar.rightcloud.oss.util.SHA512Util;
import cn.com.cloudstar.rightcloud.remote.api.iam.pojo.HcsoUser;
import cn.com.cloudstar.rightcloud.remote.api.iam.pojo.IamUser;
import cn.com.cloudstar.rightcloud.remote.api.iam.service.HcsoUserRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.iam.service.IamRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.iam.service.KeycloakRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.cloud.request.CloudEnvParams;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.user.ResUserCredential;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.cloud.CloudEnvRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.hpc.HPCRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.ma.MaRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.user.ResUserCredentialRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.system.service.user.IamUserRemoteService;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.map.MapBuilder;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.CharUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import feign.Response;
import io.seata.spring.annotation.GlobalTransactional;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.sleuth.Span;
import org.springframework.cloud.sleuth.Tracer;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ClassUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.naming.NamingException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.com.cloudstar.rightcloud.common.constants.NotificationConsts.SEND_NOTIFICATION_EXCHANGE;
import static cn.com.cloudstar.rightcloud.oss.common.constants.SysConfigConstants.SMS_HUAWEIYUN_APP_KEY;
import static cn.com.cloudstar.rightcloud.oss.module.account.service.user.impl.UserServiceImpl.CACHE_VALIDATE_CONTACTR_KEY;

/**
 * 用户控制器
 *
 * <AUTHOR>
 */
@ApiGroup(ApiGroupEnum.OPERATION_GROUP)
@Api(tags = "用户Ctrl", value = "/users")
@RestController
@RequestMapping("/users")
@Validated
public class UserCtrl {


    /**
     * 授权标识
     */
    private static final String AUTHORIZATION = "true";
    /**
     * HCSO
     */
    private static final String IAM_ENV_TYPE = "HCSO";

    private static final long AUTH_AGAIN_MINUTE = 30L;

    private static final String MOBILE_SKIP_VALIDATE = "bypass";

    /**
     * 运营控制台
     */
    private static final String FROM_BSS = "bss";

    private static final List<String> NOT_FILTER_MODULE = Collections.singletonList("P");

    private static final List<String> FILTER_PERMISSION = Arrays.asList("CD01", "CD02", "CD03", "CD04", "CD05", "CD06", "CD07",
                                                                        "CF01", "CF02", "CF03", "CG01", "CF04", "CF05");

    private static final String ALL_PERMISSION = "all";

    private static final String MINUS_ONE = "-1";

    private static final String ZERO = "0";

    private static final String ONE = "1";

    private static final String TWO = "2";

    private static final String FOUR = "04";

    private static final String FREEZE = "freeze";

    private static final String TWICE_VALIDATE = "LOCK_PASSWORD";

    private static final String NEED_TWICE_VALIDATE = "needPasswordValidate";

    private static final String CHANGE_CIPHER_EMAIL = "change_pwd_email-";

    public static Pattern patternMobile = Pattern.compile("^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\\d{8}$");

    public static Pattern patternMobile1 = Pattern.compile("^((0\\d{2,3})-?)?(\\d{7,8})(-(\\d{3,}))?$");

    public static Pattern patternRaleName = Pattern.compile("^[\\u4e00-\\u9fa5a-zA-Z0-9]{2,64}$");

    private static final Map<String, String> STATUS = Maps.newHashMap();

    private static final Map<String, String> CERTIFICATION_STATUS = Maps.newHashMap();

    private static final Map<String, String> TYPE = Maps.newHashMap();

    private static final String NOT_NEED = "n";

    private static final String ADMIN_ACCOUNT = "admin";

    public final static List<String> CLUSTER_NO_QUERY_SEARCH_STATUS = Arrays.asList(ResHpcClusterStatus.DELETED);

    //身份证加密解密key
    private static final String AUTH_KEY = "auth_id_card_ctrl_key";

    private static final Logger log = LoggerFactory.getLogger(UserCtrl.class);

    static {
        STATUS.put(ONE, "启用");
        STATUS.put(ZERO, "禁用");
        STATUS.put("2", "待审核");
        STATUS.put("4", "已拒绝");

        CERTIFICATION_STATUS.put("noAuth", "待认证");
        CERTIFICATION_STATUS.put("authing", "认证中");
        CERTIFICATION_STATUS.put("authSucceed", "认证成功");
        CERTIFICATION_STATUS.put("authFiled", "认证失败");

        TYPE.put("user", "个人认证");
        TYPE.put("company", "企业认证");
    }

    private static final String BSS = "bss";
    private static final String CONSOLE = "console";

    private static final String SUB_USER = "子账户";
    private static final String ACCOUNT = "主账户";

    /**
     * 更新用户密码锁
     */
    private static final String UPDATE_USER_CIPHER_LOCK = "updateUserPassword";

    private static final String CHANGE_USER = "user";

    private static final String CHANGE_HPC = "hpc";


    private final Logger logger = LoggerFactory.getLogger(ClassUtils.getUserClass(this));

    @Autowired
    private UserService userService;

    @Autowired
    private BizInvoiceMapper bizInvoiceMapper;

    @Resource
    private SysConfigMapper sysConfigMapper;

    @Autowired
    private RoleService roleService;

    @Autowired
    private BizDistributorMapper bizDistributorMapper;

    @Autowired
    private BusinessNotificationService businessNotificationService;
    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private UserPrivacySignMapper userPrivacySignMapper;

    @Autowired
    private UserRoleService userRoleService;

    @Autowired
    private SysConfigService sysconfigService;

    @Autowired
    private ModuleService moduleService;

    @Autowired
    private ServiceCategoryMapper serviceCategoryMapper;

    @Autowired
    private AuthService authService;

    @Autowired
    private OrgService orgService;

    @Autowired
    private CompanyService companyService;

    @Autowired
    private SysBssEntityService sysBssEntityService;

    @Autowired
    private BillingAccountService billingAccountService;

    @Autowired
    private SysSaasOrgService sysSaasOrgService;

    @Autowired
    private AssertionMapper assertionMapper;

    @DubboReference
    private IamRemoteService iamRemoteService;

    @DubboReference
    private IamUserRemoteService iamUserRemoteService;

    @DubboReference
    private KeycloakRemoteService keycloakRemoteService;

    @DubboReference
    private HcsoUserRemoteService hcsoUserRemoteService;

    @DubboReference
    private CloudEnvRemoteService cloudEnvRemoteService;

    @Autowired
    private MessageService messageService;
    @Autowired
    private SysConfigService sysConfigService;

    @Autowired
    private CompanyMapper companyMapper;

    @Autowired
    private UserGroupMapper userGroupMapper;

    @Autowired
    private SysHpcPassMapper hpcPassMapper;


    @Value("${modelArtsFlg:true}")
    private Boolean modelArtsFlg;

    @Value("${SKIP_HPC_FUNCTION:true}")
    private Boolean skipHPCFunction;

    @Value("${idpCertificationFlg:true}")
    private Boolean idpCertificationFlg;

    @Value("${BSS_INTEGRATE_CMP:false}")
    private Boolean bssIntegrateCmp;

    @Value("${INTEGRATE_CMP_URL:}")
    private String integrateCmpUrl;

    @Value("${upload.base.path}")
    private String uploadBasePath;

    @Autowired
    private OrgIdpMapper orgIdpMapper;

    @Autowired
    private CancelService cancelService;


    @Autowired
    private ProcessService processService;

    @DubboReference
    private ResUserCredentialRemoteService resUserCredentialRemoteService;

    @Autowired
    private ISysMUserDelLogService sysMUserDelLogService;

    @Autowired
    private CodeMapper codeMapper;

    @DubboReference
    private HPCRemoteService hpcRemoteService;
    @Autowired
    private PolicyService policyService;
    @Autowired
    private HpcFeignService hpcFeignService;

    @Autowired
    private UserFeginService userFeginService;
    @Autowired
    private UserSecurityService userSecurityService;

    @Autowired
    private ActionLogTracker actionLogTracker;

    @Autowired()
    @Lazy
    private StringRedisTemplate redisTemplate;

    @Autowired
    private SysMFilePathMapper sysMFilePathMapper;

    @Autowired
    private UserExportService userExportService;

    @Autowired
    private StorageService storageService;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private BizBillingAccountService bizBillingAccountService;

    @Autowired
    private RoleMapper roleMapper;

    @Autowired
    private BizBillingAccountMapper bizBillingAccountMapper;

    @Autowired
    private IamPermissionService iamPermissionService;

    @Resource
    private EntityUserMapper entityUserMapper;

    @Autowired
    private SysMMsgReceiveContactServiceImpl sysMMsgReceiveContact;

    @Autowired
    private ISysOssMessageService sysOssMessageService;
    @Autowired
    private LdapUserService ldapUserService;
    @Autowired
    private SysMFilePathMapper filePathMapper;



    @Autowired
    private IBizDistributorService bizDistributorService;

    @Autowired
    private UserRoleMapper userRoleMapper;

    @Autowired
    private RoleModuleMapper roleModuleMapper;

    @Autowired
    private BizDownloadMapper bizDownloadMapper;

    @Autowired
    private FeignService feignService;

    @Autowired
    @Qualifier("cloudExecutor")
    private ThreadPoolTaskExecutor executorService;

    @Autowired
    private ServiceOrderService serviceOrderService;
    @DubboReference
    private MaRemoteService maRemoteService;

    @Autowired
    private IBizCustomerActionLogService bizCustomerActionLogService;
    @Autowired
    private Tracer tracer;
    private static final String AUDITED_LOCK_KEY = "TENANT.AUDITED.";

    @Autowired
    private UserSyncService userSyncService;

    @Autowired
    private ResourceDcFeignService resourceDcFeignService;

    /**
     * 激活用户
     *
     * @param userId 用户id
     *
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = USER.AUDIT_USER)
    @ApiOperation(httpMethod = "PUT", value = "激活用户", notes = "通过用户ID激活用户")
    @ApiImplicitParam(name = "userId", value = "用户ID", paramType = "path", dataType = "long", required = true)
    @PutMapping("/{userId}/activation")
    @Transactional
    @GlobalTransactional
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'激活用户'", tagNameUs ="'Activate User'",
            resource = OperationResourceEnum.ACTIVATE_USER, bizId = "#userId")
    @DataPermission(resource = OperationResourceEnum.ACTIVATE_USER, bizId = "#userId")
    public RestResult activate(@PathVariable("userId") Long userId) {
        int updRs = userService.userReview(userId);
        if (updRs == 1) {
            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1327286805));
        } else {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1327231386));
        }
    }

    /**
     * 设置到期时间,激活用户
     *
     * @param request 请求
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = USER.AUDIT_USER)
    @ApiOperation(httpMethod = "PUT", value = "激活用户", notes = "审核时设置到期时间，激活用户")
    @ApiImplicitParam(name = "userId", value = "用户ID", paramType = "path", dataType = "long", required = true)
    @PutMapping("/setEndTime")
    @Transactional
    @GlobalTransactional
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'激活用户'", tagNameUs ="'Activate User'",
            resource = OperationResourceEnum.ACTIVATE_USER, bizId = "#request.userId")
    @DataPermission(resource = OperationResourceEnum.ACTIVATE_USER, bizId = "#request.userId")
    public RestResult setEndTime(@RequestBody @Valid ActivateUserRequest request) {
        logger.info("---------> UserCtrl.setEndTime-{} 激活用户", request.getUserId());
        Long userId = request.getUserId();
        String key = AUDITED_LOCK_KEY + userId;
        boolean lock = JedisUtil.INSTANCE.setnx(key, 5 * 60 * 1000);
        logger.info("---------> UserServiceImpl.setEndTime-{} 激活用户开始 key: {}, lock: {}", userId, key, lock);
        if (!lock) {
//            throw new BizException(WebUtil.getMessage(MsgCd.TENANT_AUDITED_WAIT));
            // 重复请求直接放过
            return new RestResult(RestResult.Status.SUCCESS);
        }

        try {
        boolean status = userService.userReviewEndTime(request);

        if (status) {
            log.info("用户审核通过--同步算力");
            syncCfn(userId);
            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1327286805));
        } else {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1327231386));
        }
        } finally {
            logger.info("---------> UserServiceImpl.setEndTime-{} 激活用户-删除key key: {}", userId, key);
            JedisUtil.INSTANCE.del(key);
        }

    }

    @Autowired
    private CfnService cfnService;
    @Autowired
    private OrgMapper orgMapper;

    @GetMapping("/cfn_user")
    public void syncCfn(@RequestParam("userId") Long userId) {
        CreateUserReq req = new CreateUserReq();
        User user = userMapper.selectUserByUserId(userId);
        req.setAccount(user.getAccount());
        req.setRealName(user.getRealName());
        req.setEmail(user.getEmail());
        req.setMobile(user.getMobile());

        BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectByOrgIdAndEntityId(user.getOrgSid(), 1L);
        if (Objects.isNull(bizBillingAccount)) {
            throw new BizException("查询企业不存在");
        }
        // 账号是否冻结
        //bizBillingAccount.setFreezeStatus("normal".equals(bizBillingAccount.getStatus()) ? ONE : ZERO);
        Org org = orgMapper.selectByPrimaryKey(bizBillingAccount.getOrgSid());
        if (Objects.nonNull(org)) {
            req.setAddress(org.getAddress());
            req.setContactName(org.getContactName());
            if (StrUtil.isBlank(req.getEmail())) {
                req.setEmail(org.getContactEmail());
            }
            if (StrUtil.isBlank(req.getMobile())) {
                req.setMobile(org.getContactPhone());
            }
            Code applicationScenario = codeMapper.selectByCodeCategoryAndCodeValue("APPLICATION_SCENARIO", org.getApplicationScenario());
            if (Objects.nonNull(applicationScenario)) {
                req.setApplicationScenario(applicationScenario.getCodeDisplay());
            }
            Code industryType = codeMapper.selectByCodeCategoryAndCodeValue("INDUSTRY_TYPE", org.getIndustryType());
            if (Objects.nonNull(industryType)) {
                req.setIndustryType(industryType.getCodeDisplay());
            }
            Code personnelSize = codeMapper.selectByCodeCategoryAndCodeValue("PERSONNEL_SIZE", org.getPersonnelSize());
            if (Objects.nonNull(personnelSize)) {
                req.setPersonnelSize(personnelSize.getCodeDisplay());
            }
        }
        req.setPassword("!Q2w3e4r");
        req.setIsResetPassword("false");
        req.setRealName(req.getAccount());
        String keycloakUserId = iamRemoteService.findKeycloakUserId(user.getUserSid());
        log.info("keycloakUserId:{}",keycloakUserId);
        req.setKcUserId(keycloakUserId);
        String cfnUserSid = cfnService.createUser(req);
        log.info("sync cfn user success {}", cfnUserSid);
        userMapper.updateByPrimaryKeyUserNo(userId, cfnUserSid);
    }

    @GetMapping("/cfn_user/action")
    public void cfnUserAction(@RequestParam("userId") Long userId, @RequestParam("action") String action) {
        String userNo = userMapper.selectUserNoById(userId);
        log.info("action:{},cfnUser:{}", action, userNo);
        if (!NumberUtil.isLong(userNo)) {
            return;
        }
        Long cfnUser = Long.valueOf(userNo);
        switch (action) {
            case "freeze":
                cfnService.modifyUserStatus(ModifyStatusReq.builder().userSid(cfnUser).accountStatus(UserAccountStatusEnum.FREEZE).build());
                break;
            case "normal":
                cfnService.modifyUserStatus(ModifyStatusReq.builder().userSid(cfnUser).accountStatus(UserAccountStatusEnum.NORMAL).build());
                break;
            case "delete":
                cfnService.deleteUser(DeleteUserReq.builder().userSid(cfnUser).build());
                break;
            case "ENABLE":
                cfnService.modifyUserStatus(ModifyStatusReq.builder().userSid(cfnUser).status(UserStatusEnum.ENABLE).build());
                break;
            case "DISABLE":
                cfnService.modifyUserStatus(ModifyStatusReq.builder().userSid(cfnUser).status(UserStatusEnum.DISABLE).build());
                break;
            default:
                log.warn("action:{} not support", action);
                break;
        }
    }

    /**
     * 激活用户流程获取
     *
     * @param userId 用户id
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "GET", value = "激活用户流程获取", notes = "通过用户ID激活用户流程获取")
    @ApiImplicitParam(name = "userId", value = "用户ID", paramType = "path", dataType = "long", required = true)
    @GetMapping("/activation/flows/{userId}")
    @AuthorizeOss(action = USER.AUDIT_USER)
    public RestResult activateFlows(@PathVariable("userId") Long userId) {
        User user = userService.selectByPrimaryKey(userId);
        if (user == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1671398101));
        }
        // 如果是租户管理员
        if (null == user.getParentSid()) {
            boolean checkCreateIamSubUser = userService.checkCreateIamSubUser();
            return new RestResult(UserActivateFlowEnum.tenantUserFlow(modelArtsFlg, skipHPCFunction, idpCertificationFlg, checkCreateIamSubUser));
        } else {
            return new RestResult(UserActivateFlowEnum.subuserFlow(skipHPCFunction));
        }
    }

    /**
     * 拒绝用户
     *
     * @param refuseUserRequest 拒绝用户请求
     *
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = USER.AUDIT_USER)
    @ApiOperation(httpMethod = "PUT", value = "拒绝用户", notes = "拒绝用户")
    @PutMapping("/refuse")
    @Transactional
    @GlobalTransactional
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'拒绝用户'", tagNameUs ="'Reject user'",
            resource = OperationResourceEnum.REFUSE_USER, bizId = "#refuseUserRequest.userId", param = "#refuseUserRequest")
    public RestResult refuse(@RequestBody @Valid RefuseUserRequest refuseUserRequest) {
        User user = userService.selectByPrimaryKey(refuseUserRequest.getUserId());
        if (user == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1496023145));
        }
        if (ObjectUtils.isEmpty(user.getOrgSid())) {
            BizException.e(WebUtil.getMessage(MsgCd.ERR_MSG_14));
        }
        // 此接口仅用于待审核租户
        if (!UserStatus.NOTAPPROVE.equals(user.getStatus())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2124622564));
        }
        user.setStatus(UserStatus.REFUSE);
        user.setRemark(refuseUserRequest.getRemark());
        Org org = orgService.selectByPrimaryKey(user.getOrgSid());
        if (ObjectUtils.isEmpty(org)) {
            BizException.e(WebUtil.getMessage(MsgCd.ERR_MSG_14));
        }
        org.setStatus(UserStatus.REFUSE);
        iamRemoteService.deleteUser(refuseUserRequest.getUserId());
        // 添加用户业务标识
        String businessTag = user.getBusinessTag();
        if (StringUtil.isNotEmpty(businessTag)) {
            String[] tagSplit = businessTag.split(";");
            List<String> tagList = Arrays.asList(tagSplit);
            if (!tagList.contains(BusinessTagEnum.CANCELLED.getTag())) {
                businessTag += ";" + BusinessTagEnum.CANCELLED.getTag();
            }
            user.setBusinessTag(businessTag);
        } else {
            user.setBusinessTag(BusinessTagEnum.CANCELLED.getTag());
        }
        userService.updateByPrimaryKeySelective(user);
        //修改 org 状态
        orgService.updateByPrimaryKeySelective(org);
        Map<String, String> content = new HashMap<>(8);
        content.put("userAccount", user.getAccount());
        content.put("reason", refuseUserRequest.getRemark());

        //给租户发送消息 租户未配置消息设置只能采取单独发送
        String msgId = NotificationConsts.ConsoleMsg.AccountMsg.TENANT_USER_REFUSE;
        MailNotificationMq mailNotificationMq = new MailNotificationMq();
        mailNotificationMq.setMsgId(msgId);
        mailNotificationMq.setMap(content);
        HashSet<String> mails = new HashSet<>(8);
        mails.add(CrytoUtilSimple.decrypt(user.getEmail()));
        mailNotificationMq.setMails(mails);
        rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.ACCOUNT, mailNotificationMq);

        ImsgNotificationMq imsgNotificationMq = new ImsgNotificationMq();
        imsgNotificationMq.setMsgId(msgId);
        imsgNotificationMq.setMap(content);
        LinkedHashSet<Long> userIds = new LinkedHashSet<>(8);
        userIds.add(user.getUserSid());
        imsgNotificationMq.setImsgUserIds(userIds);
        rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.ACCOUNT, imsgNotificationMq);
        return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_789079806));
    }

    /**
     * 验证邀请码
     *
     * @param invitationRequest 请求体
     * @return {@link Boolean}
     */
    @ApiOperation(httpMethod = "GET", value = "验证邀请码", notes = "验证邀请码是否存在")
    @GetMapping("/validateInvitationCode")
    public Boolean validateInvitationCode(InvitationRequest invitationRequest) {
        return userService.validateInvitationCode(invitationRequest.getUserId());
    }

    /**
     * 设置用户的状态(客户管理-账户管理)
     *
     * @param userId 用户id
     * @param status 状态
     * @return {@link Boolean}
     */
    @AuthorizeOss(action = BQ03.BQ0302 + "," + AuthModule.CH.CH0104)
    @ApiOperation(httpMethod = "PUT", value = "设置用户的状态", notes = "通过status、用户ID设置用户的状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户ID", paramType = "path", dataType = "long", required = true),
            @ApiImplicitParam(name = "status", value = "状态", paramType = "path", dataType = "string", required = true)})
    @PutMapping("/{userId}/status/{status}")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'更新用户状态'", tagNameUs ="'Update user status'",
            resource = OperationResourceEnum.UPDATE_USER_STATUS, bizId = "#userId", param = "#status")
    @SmsValidation
    public RestResult setUserStatusOss(@PathVariable("userId") Long userId, @PathVariable("status") String status) {
        List<Role> userRole = roleMapper.findRolesByUserSid(userId);
        if (CollectionUtil.isNotEmpty(userRole)) {
            Set<Long> userRoleSid = userRole.stream().map(it -> it.getRoleSid()).collect(Collectors.toSet());
            if(!userRoleSid.contains(302L)){
                AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
                if (!("console").equals(authUserInfo.getRemark()) || !userRoleSid.contains(306L)) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_386105363));
                }
            }
        }
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (ObjectUtils.isEmpty(authUser)){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_531007114));
        }
        User user = userService.selectByPrimaryKey(userId);
        if (ObjectUtils.isEmpty(user)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_867857480));
        }
        // 运营管理员横向越权判断
        if (!"admin".equals(authUser.getAccount())) {
            checkRole(authUser, user.getUserSid());
        }
        if (!Arrays.asList("0", "1").contains(status)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAMS_NOT_LEGALITY));
        }
        if ("0".equals(status) && "0".equals(user.getStatus())) {
            return new RestResult(Status.SUCCESS);
        }
        if ("1".equals(status) && "1".equals(user.getStatus())) {
            return new RestResult(Status.SUCCESS);
        }
        // 运营管理员横向越权判断
        if (100L != authUser.getUserSid()) {
            checkRole(authUser, user.getUserSid());
        }
        // 如果账号的有效截止时间小于了当前时间那么就提示账号已过期
        if (!ObjectUtils.isEmpty(user.getEndTime()) && !ObjectUtils.isEmpty(user)) {
            if (user.getEndTime().before(new Date())) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_324593543));
            }
        }

        //这个判断需要根据运营和租户单独判断
        if (authUser.getRemark().equals(CONSOLE)) {
            List<Role> roles = roleMapper.findRolesByUserSid(authUser.getUserSid());
            if (!ObjectUtils.isEmpty(roles)) {
                Boolean result = roles.stream()
                                      .anyMatch(o -> o.getRoleSid().equals(RoleEnum.OPERATION_USER.getRoleSid()) || o.getRoleSid().equals(RoleEnum.IAM_SUBUSER.getRoleSid()));
                if (!result) {
                    throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
                }
            }
        }

        if (ObjectUtils.isEmpty(user)) {
            throw new BizException(RestConst.BizError.BAD_PARAM.getType());
        }
        //修改账户为子用户，则需要判断parentId是否是当前登录用户的id
        if (!ObjectUtils.isEmpty(authUser) && !("bss").equals(authUser.getRemark())) {
            if (ObjectUtils.isEmpty(authUser.getParentSid())) {
                //当前登录是管理员
                if (!ObjectUtils.isEmpty(authUser) && !user.getParentSid().equals(authUser.getUserSid())) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_9631134));
                }
            }else {
                //当前登录用户不是管理员
                if (!ObjectUtils.isEmpty(authUser) && !user.getParentSid().equals(authUser.getParentSid())) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_9631134));
                }
            }
        }

        Criteria criteria = new Criteria();
        criteria.put("userSid",userId);
        List<Long> roleSids = Arrays.asList(302L, 306L);
        criteria.put("roleSids", roleSids);
        int count = userRoleMapper.countByParams(criteria);
        logger.info("UserCtrl.setUserStatus count: {}", count);

        if (userService.checkCreateIamSubUser() && count > 0) {
            Long userSid = user.getUserSid();
            boolean enabled = "1".equals(status);
            boolean flag = AuthUtil.replaceUserToInvoke( () -> userService.updateSubUserEnabled(userSid, user.getOrgSid(), enabled), userSid);
            if (!flag) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1172658454) + (WebUtil.getMessage(MsgCd.ERR_MSG_BSS_49).equals(status) ? WebUtil.getMessage(MsgCd.ERR_MSG_BSS_671592109) : WebUtil.getMessage(MsgCd.ERR_MSG_BSS_956215323)));
            }
        }

        user.setStatus(status);
        userService.updateByPrimaryKeySelective(user);
        iamRemoteService.updateUserStatus(BeanConvertUtil.convert(user, IamUser.class));
        if (UserStatus.FORBIDDEN.equals(status)) {
            RequestContextUtil.removeUserCache(user.getAccount());
        }

        if (count > 0) {
            try {
                //同步ldap资源
                this.synHpcToLdap(userId, user.getOrgSid(), Long.parseLong(status), user.getParentSid() != null);
            } catch (Exception e) {
                logger.info("UserCtrl.setUserStatus 设置用户状态ldap同步失败-回滚iam子用户状态 error: {}", e.getMessage());
            }
        }
        cfnUserAction(userId, "1".equals(status) ? UserStatusEnum.ENABLE.getType() : UserStatusEnum.DISABLE.getType());

        return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_789079806));
    }

    /**
     *  ldap同步
     *   租户操作使用组织ID，操作所有用户
     *   子用户操作，只操作当前用户
     * @param userId 用户ID
     * @param orgSid 组织ID
     * @param status 状态
     */
    private void synHpcToLdap(Long userId, Long orgSid, Long status, boolean isSub) {
        LdapSyncRequest ldapSyncRequest = new LdapSyncRequest();
        ldapSyncRequest.setOrgId(orgSid);
        if (isSub) {
            List<Long> userIds = new ArrayList<>();
            userIds.add(userId);
            ldapSyncRequest.setUserIds(userIds);
        }

        if (status == 1) {
            // 同步ldap 添加资源
            policyService.synHpcToLdapByLdapSyncRequest(ldapSyncRequest);
        }
        else {
            // 同步ldap 移除资源
            QueryResHpcClusterRequest request = new QueryResHpcClusterRequest();
            request.setPagesize("10000");
            request.setStatus("available");

            RestResult clusterResult = AuthUtil.replaceUserToInvoke( () -> hpcFeignService.getHpcCluster(request), userId);
            Object data = clusterResult.getData();
            cn.com.cloudstar.rightcloud.common.pojo.BaseGridReturn baseGridReturn = BeanConvertUtil.convert(data, cn.com.cloudstar.rightcloud.common.pojo.BaseGridReturn.class);
            List list = baseGridReturn.getDataList();
            if (CollectionUtil.isEmpty(list)) {
                logger.info("UserCtrl.synHpcToLdap 没有可移除的资源");
                return;
            }

            List<ProductResourceHPCResponse> hpcResponses = BeanConvertUtil.convert(list,
                    ProductResourceHPCResponse.class);

            List<String> businessCategorys = hpcResponses.stream().map(ProductResourceHPCResponse::getBusinessCategory)
                    .filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (CollectionUtil.isEmpty(businessCategorys)) {
                logger.info("UserCtrl.synHpcToLdap businessCategory is null 没有可移除的资源");
                return;
            }

            logger.info("UserCtrl.synHpcToLdap 用戶：{}，移除ldap资源：{}", userId,
                    JSONUtil.toJsonStr(businessCategorys));

            for (String businessCategory : businessCategorys) {
                List<String> remove = new ArrayList<>();
                remove.add(businessCategory);
                ldapSyncRequest.setRemoveBusinessCategoryList(remove);
                policyService.synHpcToLdapByLdapSyncRequest(ldapSyncRequest);
            }

        }

    }

    /**
     * 设置用户的状态(客户管理-账户管理-子账户管理)
     *
     * @param userId 用户id
     * @param status 状态
     *
     * @return {@link Boolean}
     */
    @AuthorizeOss(action = BQ03.BQ030401)
    @ApiOperation(httpMethod = "PUT", value = "设置用户的状态", notes = "通过status、用户ID设置用户的状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户ID", paramType = "path", dataType = "long", required = true),
            @ApiImplicitParam(name = "status", value = "状态", paramType = "path", dataType = "string", required = true)})
    @PutMapping("/{userId}/account/status/{status}")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'设置用户的状态'", tagNameUs ="'Set the status of a user'",
            resource = OperationResourceEnum.UPDATE_USER_STATUS, bizId = "#userId", param = "#status")
    @SmsValidation
    public RestResult setUserStatusByAccount(@PathVariable("userId") Long userId, @PathVariable("status") String status) {
        List<Role> userRole = roleMapper.findRolesByUserSid(userId);
        if (CollectionUtil.isNotEmpty(userRole)) {
            Set<Long> userRoleSid = userRole.stream().map(it -> it.getRoleSid()).collect(Collectors.toSet());
            if(!userRoleSid.contains(306L)){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1655373924));
            }
        }
        return setUserStatus(userId, status);
    }

    /**
     * 设置用户的状态(分销管理)
     *
     * @param userId 用户id
     * @param status 状态
     *
     * @return {@link Boolean}
     */
    @AuthorizeOss(action = BU.BU05)
    @ApiOperation(httpMethod = "PUT", value = "设置用户的状态", notes = "通过status、用户ID设置用户的状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户ID", paramType = "path", dataType = "long", required = true),
            @ApiImplicitParam(name = "status", value = "状态", paramType = "path", dataType = "string", required = true)})
    @PutMapping("/{userId}/distributor/status/{status}")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'设置用户的状态'", tagNameUs ="'Set the status of a user'",
            resource = OperationResourceEnum.UPDATE_USER_STATUS, bizId = "#userId", param = "#status")
    @SmsValidation
    public RestResult setUserStatusDistributor(@PathVariable("userId") Long userId,
                                            @PathVariable("status") String status) {
        User user = userService.selectByPrimaryKey(userId);
        if (ObjectUtils.isEmpty(user)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_867857480));
        }
        if (!StringUtils.equals(UserType.DISTRIBUTOR_USER, user.getUserType())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        return setUserStatus(userId, status);
    }

    /**
     * 设置用户的状态(用户管理)
     *
     * @param userId 用户id
     * @param status 状态
     *
     * @return {@link Boolean}
     */
    @AuthorizeOss(action = ZC.ZC04)
    @ApiOperation(httpMethod = "PUT", value = "设置用户的状态", notes = "通过status、用户ID设置用户的状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户ID", paramType = "path", dataType = "long", required = true),
            @ApiImplicitParam(name = "status", value = "状态", paramType = "path", dataType = "string", required = true)})
    @PutMapping("/{userId}/user/status/{status}")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'设置用户的状态'", tagNameUs ="'Set the status of a user'",
            resource = OperationResourceEnum.UPDATE_USER_STATUS, bizId = "#userId", param = "#status")
    @SmsValidation
    public RestResult setUserStatusByUser(@PathVariable("userId") Long userId, @PathVariable("status") String status) {
        User user = userService.selectByPrimaryKey(userId);
        if (UserStatus.NOTAPPROVE.equals(user.getStatus()) || UserStatus.REFUSE.equals(user.getStatus())
                || Objects.nonNull(user.getParentSid())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1575901082));
        }
        return setUserStatus(userId, status);
    }

    /**
     * 系统控制台-用户管理-邮件重置密码
     *
     * @param sendEmailRequest 发送电子邮件请求
     * @param request 请求
     *
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = "ZC06,BQ0305,CH0107")
    @ApiOperation(httpMethod = "POST", value = " 重置密码-给用户发送邮件")
    @PostMapping("/mgt/reset_password/send_email")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "#sendEmailRequest.userIdList", resource = OperationResourceEnum.SEND_RESET_PWD_EMAIL)
    @DataPermission(resource = OperationResourceEnum.SEND_RESET_PWD_EMAIL, bizId = "#sendEmailRequest.userIdList")
    public RestResult updatePwd(@RequestBody @Valid SendEmailRequest sendEmailRequest,
                                HttpServletRequest request) {

        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (ObjectUtils.isEmpty(authUserInfo)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_531007114));
        }
        //验证码频繁校验
        String key = "sendValidateEmail" + authUserInfo.getUserSid();
        String amount = JedisUtil.INSTANCE.get(key);
        if (Objects.nonNull(amount)) {
            if (Integer.parseInt(amount) >= 10) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_403448001));
            }
        } else {
            amount = "0";
        }

        if (CollectionUtils.isEmpty(sendEmailRequest.getUserIdList())) {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_123976647));
        }
        List<User> users = userMapper.selectBatchUser(sendEmailRequest.getUserIdList());
        Map<Long, User> userMap = users.stream().collect(Collectors.toMap(User::getUserSid, user -> user));
        boolean verifyUser = sendEmailRequest.getUserIdList().stream().allMatch(userSid -> {
            if (userSid.equals(100L)) {
                return false;
            }
            User user = userMap.get(userSid);
            if (ObjectUtils.isEmpty(user) || !ObjectUtils.isEmpty(user.getParentSid())) {
                return false;
            }
            return true;
        });
        if (!verifyUser) {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_473915648));
        }
        String consoleURl = sysConfigService.getValueByConfigKey("rightcloud.console.url");
        String mgtURl = sysConfigService.getValueByConfigKey("rightcloud.mgt.url");
        for (Long sid : sendEmailRequest.getUserIdList()) {
            User user = this.userMapper.selectByPrimaryKey(sid);
            String realUrl = "";
            if(user != null && UserType.PLATFORM_USER.equalsIgnoreCase(user.getUserType()) && user.getOrgSid() != null){
                realUrl = consoleURl;
            }else{
                realUrl = mgtURl;
            }

            if (null != user) {
                businessNotificationService.changePwdEmail(user, realUrl);
            }
        }

        JedisUtil.INSTANCE.set(key, String.valueOf(Integer.parseInt(amount) + 1), 60 * 3);
        return new RestResult(RestResult.Status.SUCCESS);
    }


    /**
     * 登录后-重置密码-给用户发送邮件
     *
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "POST", value = "重置密码-给用户发送邮件")
    @PostMapping("/resetPwd/send_email")
    @AuthorizeOss(action = CH.CH0107)
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "'发送邮件重置密码'", bizId = "#sendEmailRequest.userIdList", resource = OperationResourceEnum.LOGIN_SEND_RESET_PWD_EMAIL, tagNameUs ="'Send an email to reset your password'")
    @DataPermission(resource = OperationResourceEnum.LOGIN_SEND_RESET_PWD_EMAIL, bizId = "#sendEmailRequest.userIdList")
    public RestResult sendEmail(@RequestBody @Valid SendEmailRequest sendEmailRequest,
                                HttpServletRequest request) {

        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (ObjectUtils.isEmpty(authUserInfo)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_531007114));
        }
        //验证码频繁校验
        String key = "sendValidateEmail" + authUserInfo.getUserSid();
        String amount = JedisUtil.INSTANCE.get(key);
        if (Objects.nonNull(amount)) {
            if (Integer.parseInt(amount) >= 10) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_403448001));
            }
        } else {
            amount = "0";
        }

        if (CollectionUtils.isEmpty(sendEmailRequest.getUserIdList())) {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_123976647));
        }
        for (Long userSid : sendEmailRequest.getUserIdList()) {
            User user = this.userMapper.selectByPrimaryKey(userSid);
            if (null == user) {
                return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_473915648));
            }
        }
        String consoleURl = sysConfigService.getValueByConfigKey("rightcloud.console.url");
        String mgtURl = sysConfigService.getValueByConfigKey("rightcloud.mgt.url");
        for (Long userSid : sendEmailRequest.getUserIdList()) {
            User user = this.userMapper.selectByPrimaryKey(userSid);
            String realUrl = "";
            if(user != null && UserType.PLATFORM_USER.equalsIgnoreCase(user.getUserType()) && user.getOrgSid() != null){
                realUrl = consoleURl;
            }else{
                realUrl = mgtURl;
            }
            if (null != user) {
                businessNotificationService.changePwdEmail(user, realUrl);
            }
        }

        JedisUtil.INSTANCE.set(key, String.valueOf(Integer.parseInt(amount) + 1), 60 * 3);
        return new RestResult(RestResult.Status.SUCCESS);
    }


    /**
     * 重置用户密码(用户管理)
     *
     * @param request 请求
     *
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "PUT", value = " 重置用户密码")
    @PutMapping("/password/reset")
    @SmsValidation
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'重置用户密码'", bizId = "#request.userIds", resource = OperationResourceEnum.RESET_USER_PASSWORD, tagNameUs ="'Reset User Password'")
    @Encrypt
    @AuthorizeOss(action = ZC.ZC07)
    public RestResult resetUserPassword(@RequestBody @Valid ResetPwdRequest request) {
        // 需要修改的用户id 必传
        if (CollectionUtil.isEmpty(request.getUserIds())) {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }else{
            for(Long userSid: request.getUserIds()){
                User user = userMapper.selectByPrimaryKey(userSid);
                if(user == null){
                    return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
                }
            }
        }
        // 改密码前校验权限
        log.info("重置权限校验开始........");
        if (!userSecurityService.validRestPasswordPermission(request.getUserIds())) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        log.info("重置权限校验结束........");

        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        // 验证密码复杂度
        // 判断是否是个人信息点修改密码还是列表重置密码
        Pattern p = Pattern.compile("[\u4E00-\u9FA5|\\！|\\，|\\。|\\（|\\）|\\《|\\》|\\“|\\”|\\？|\\：|\\；|\\【|\\】]");
        Matcher m = p.matcher(request.getPassword());
        if(m.find()){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_190267765));
        }
        List<User> users = userMapper.selectByParams(new Criteria("userSidList", request.getUserIds()));
        log.info("验证策略是否符合开始........");
        for (User user : users) {
            String validStr = userService.validPasswordByPolicyToError(request.getPassword(), user.getOrgSid(),
                                                                       user.getUserSid(), user.getParentSid(), false);
            if (!ObjectUtils.isEmpty(validStr)){
                return new RestResult(RestResult.Status.FAILURE, validStr);
            }
        }
        log.info("验证策略是否符合结束........");
        boolean result = userService.resetUserPassword(request.getUserIds(),
                                                       request.getPassword(),
                                                       Convert.toBool(request.getForceResetPwd(), true));
        log.info("重置结束..........");
        if (result) {
            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS));
        }
        return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERROR_OPERATE_FAILURE));
    }

    /**
     * 重置用户密码(账户管理)
     *
     * @param request 请求
     *
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "PUT", value = " 重置用户密码")
    @PutMapping("/account/password/reset")
    @SmsValidation
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'重置用户密码'", bizId = "#request.userIds", resource = OperationResourceEnum.RESET_USER_PASSWORD, tagNameUs ="'Reset User Password'")
    @Encrypt
    @AuthorizeOss(action = BQ03.BQ0306)
    public RestResult resetUserPasswordByAccount(@RequestBody @Valid ResetPwdRequest request) {
        return resetUserPassword(request);
    }

    /**
     * [INNER API] 重置用户密码(用户管理)
     *
     * @param request 请求
     *
     * @return {@link RestResult}
     */
    @RejectCall
    @ApiOperation(httpMethod = "PUT", value = " 重置用户密码")
    @PutMapping("/password/reset/feign")
    public RestResult resetUserPasswordByFeign(@RequestBody @Valid ResetPwdRequest request) {
        return resetUserPassword(request);
    }


    /**
     * 确认隐私签名
     *
     * @param confirmPrivacySignRequest 确认私隐签名请求
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = AuthModule.COMMON.PUBLIC.C1.C116)
    @ApiOperation(httpMethod = "POST", value = " 隐私声明确认")
    @PostMapping("/privacy/confirm")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'确认隐私声明'", resource = OperationResourceEnum.PRIVACY_SIGN_CONFIRM, tagNameUs ="'Confirm Privacy Statement'")
    @Encrypt
    public RestResult confirmPrivacySign(@RequestBody @Valid ConfirmPrivacySignRequest confirmPrivacySignRequest) {
        Boolean isSuccess = false;
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo(WebUtil.getRequest());
        AssertUtil.requireNonBlank(authUserInfo, "获取当前登录用户失败");
        if (!ONE.equals(authUserInfo.getStatus())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2043115026));
        }
        User currentUser = this.userService.selectByPrimaryKey(authUserInfo.getUserSid());
        if (PrivacyStatusEnum.DISAGREE.getProductType()
                                      .equalsIgnoreCase(confirmPrivacySignRequest.getPrivacyStatus())) {
            currentUser.setPolicyAgreeSign(Integer.valueOf(PrivacyStatusEnum.DISAGREE.getProductType()));
            currentUser.setPolicyAgreeTime(new Date());
            //加入隐私声明协议记录
            UserPrivacySign userPrivacySign = new UserPrivacySign();
            userPrivacySign.setPrivacyStatus(PrivacyStatusEnum.DISAGREE.getProductName());
            userPrivacySign.setUserSid(authUserInfo.getUserSid());
            userPrivacySign.setPrivacyConfirmTime(new Date());
            userPrivacySign.setCreatedDt(new Date());
            userPrivacySign.setCreatedBy(authUserInfo.getAccount());
            userPrivacySignMapper.insertSelective(userPrivacySign);
            isSuccess = userService.updateUser(currentUser);
        } else if (PrivacyStatusEnum.AGREE.getProductType()
                                          .equalsIgnoreCase(confirmPrivacySignRequest.getPrivacyStatus())) {
            currentUser.setPolicyAgreeSign(Integer.valueOf(PrivacyStatusEnum.AGREE.getProductType()));
            currentUser.setPolicyAgreeTime(new Date());
            UserPrivacySign userPrivacySign = new UserPrivacySign();
            userPrivacySign.setPrivacyStatus(PrivacyStatusEnum.DISAGREE.getProductName());
            userPrivacySign.setUserSid(authUserInfo.getUserSid());
            userPrivacySign.setPrivacyConfirmTime(new Date());
            userPrivacySign.setCreatedDt(new Date());
            userPrivacySign.setCreatedBy(authUserInfo.getAccount());
            userPrivacySignMapper.insertSelective(userPrivacySign);
            isSuccess = userService.updateUser(currentUser);
        } else {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_664511972));
        }
        if (isSuccess) {
            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS));
        } else {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERROR_OPERATE_FAILURE));
        }
    }


    /**
     * 获取用户凭证
     *
     * @param moduleType 模块类型
     *
     * @return {@link DescribeUserTokenResponse}
     */
    @ApiOperation(httpMethod = "GET", value = "获取用户凭证", notes = "通过用户user实体对象，数据用json数据格式提交")
    //@ApiImplicitParam(name = "user", value = "user实体类，请参照下方user实体类", paramType = "body", required = true)
    @GetMapping("/token")
   // @OperationLog(type = OperationTypeEnum.QUERY, tagName = "'登录系统名称'", param = "#moduleType", resource = OperationResourceEnum.GET_USER_TOKEN, tagNameUs ="'Login System Name'")
    public DescribeUserTokenResponse getUserToken(@RequestParam("moduleType") String moduleType) {
        if (!Arrays.asList(Constants.BSS, Constants.CONSOLE).contains(moduleType)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1319710067));
        }
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo(WebUtil.getRequest());
        AssertUtil.requireNonBlank(authUserInfo, "获取当前登录用户失败");
        if (!ONE.equals(authUserInfo.getStatus())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2043115026));
        }
        Map<String, Object> user = JsonUtil.fromJson(JsonUtil.toJson(authUserInfo),
                                                     new TypeReference<Map<String, Object>>() {
                                                     });

        // 防止数据缓存，每次从数据库中获取用户某个数据
        User currentUser = this.userService.selectByPrimaryKey(authUserInfo.getUserSid());
        user.put("userType", currentUser.getUserType());
        PasswordPolicyDTO passwordPolicy = this.userService.buildPasswordPolicy(currentUser.getOrgSid(),
                                                                                currentUser.getParentSid());
        if (passwordPolicy.getPwdExpireTimeValidity()) {
            if (Objects.nonNull(currentUser.getPwdEndTime())) {
                if (currentUser.getPwdEndTime().before(new Date())) {
                    user.put("pwdEndTime", currentUser.getPwdEndTime());
                } else {
                    user.put("pwdEndTime", null);
                }
            } else {
                Criteria criteria = new Criteria();
                criteria.put("userId", currentUser.getUserSid());
                criteria.setOrderByClause("created_dt desc");
                List<SysMUserPasswordHistory> sysMUserPasswordHistories = userMapper.selectPasswordHistoryByParams(
                        criteria);
                if (CollectionUtil.isNotEmpty(sysMUserPasswordHistories)) {
                    SysMUserPasswordHistory sysMUserPasswordHistory = sysMUserPasswordHistories.get(0);
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(sysMUserPasswordHistory.getCreatedDt());
                    calendar.add(Calendar.DATE, Integer.valueOf(Long.toString(passwordPolicy.getPwdExpireTime())));
                    boolean b = calendar.getTime().getTime() < System.currentTimeMillis();
                    if (b) {
                        user.put("pwdEndTime", calendar.getTime());
                    } else {
                        user.put("pwdEndTime", null);
                    }
                }

            }
        } else {
            user.put("pwdEndTime", null);
        }

        // 如果当前时间小于账号有效开始时间则报错
        if (!ObjectUtils.isEmpty(currentUser.getStartTime())) {
            if (new Date().before(currentUser.getStartTime())) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_575826027));
            }
        }
        user.put("lastLoginIp", currentUser.getLastLoginIp());
        user.put("lastLoginTime", currentUser.getLastLoginTime());
        user.put("startTime", currentUser.getStartTime());
        user.put("endTime", currentUser.getEndTime());
        user.put("skinTheme", currentUser.getSkinTheme());
        SysConfig sysConfig = sysConfigMapper.selectByConfigKey("email.enable");
        //是否开启邮箱
        if(Objects.nonNull(sysConfig) && "1".equals(sysConfig.getConfigValue())){
            user.put("email", currentUser.getEmail());
        }else{
            user.put("email",null);
        }

        user.put("admin", authUserInfo.isAdmin());

        user.put("openId", currentUser.getOpenId());
        user.put("avatarUrl", currentUser.getAvatarUrl());
        user.put("wechatName", currentUser.getWechatName());

        // 用户是否同意隐私协议标识
        if ((currentUser.getUserType().equalsIgnoreCase(UserType.PLATFORM_USER)
                || currentUser.getUserType().equalsIgnoreCase(UserType.DISTRIBUTOR_USER))
                && currentUser.getOrgSid() != null &&
                ObjectUtils.isEmpty(currentUser.getLastLoginTime())) {
            // 用户隐私协议只针对租户用户
            user.put("policyAgreeSign", 0);
        } else {
            if ((currentUser.getUserType().equalsIgnoreCase(UserType.PLATFORM_USER)
                    || currentUser.getUserType().equalsIgnoreCase(UserType.DISTRIBUTOR_USER))
                    && currentUser.getOrgSid() != null) {
                user.put("policyAgreeSign", Convert.toInt(currentUser.getPolicyAgreeSign(), 0));
            } else {
                // 其他用户不需要统一隐私协议，但前端需要获取此值为 1 才不会弹框
                user.put("policyAgreeSign", 1);
            }
        }

        // 对于用户控制台登录进行，所处行业转码
        if (Objects.nonNull(currentUser.getOrgSid())) {
            if (Objects.nonNull(currentUser.getParentSid())) {
                User parentUser = userService.selectByPrimaryKey(currentUser.getParentSid());
                if (Objects.nonNull(parentUser)) {
                    user.put("industry", parentUser.getIndustry());
                    Code industry_type = codeMapper.selectByCodeCategoryAndCodeValue("INDUSTRY_TYPE",
                                                                                     parentUser.getIndustry());
                    if (Objects.nonNull(industry_type)) {
                        user.put("industryName", industry_type.getCodeDisplay());
                    }
                    // 子用户登录，校验租户状态
                    if (ZERO.equals(parentUser.getStatus())) {
                        throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_301621271));
                    }
                    if (TWO.equals(parentUser.getStatus())) {
                        throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1669888439));
                    }
                }
            } else {
                user.put("industry", currentUser.getIndustry());
                Code industry_type = codeMapper.selectByCodeCategoryAndCodeValue("INDUSTRY_TYPE",
                                                                                 currentUser.getIndustry());
                if (Objects.nonNull(industry_type)) {
                    user.put("industryName", industry_type.getCodeDisplay());
                }
            }
        }

        if (Objects.nonNull(currentUser.getCertificationStatus()) && CertificationStatus.AUTHSUCCEED.equals(
                currentUser.getCertificationStatus())) {
            user.put("realName", currentUser.getAuthName());
        } else {
            user.put("realName", currentUser.getRealName());
        }
        user.put("mobile", currentUser.getMobile());
        user.put("parentSid", currentUser.getParentSid());
        // 33676 判断是否过期
        //账号有效期判断  admin不受限制
        if (authUserInfo.getAdminFlag() == null || !authUserInfo.getAdminFlag()) {
            RestResult restResult = userService.validateUserActiveDate(currentUser, true);
            if (!ObjectUtils.isEmpty(restResult)
                    && !ObjectUtils.isEmpty(restResult.getStatus())
                    && !restResult.getStatus()) {
                if (StringUtils.isNotBlank(moduleType) && "bss".equalsIgnoreCase(moduleType)) {
                    restResult.setMessage("当前账号已过期，无法登录系统，请联系平台管理员。");
                } else if (StringUtils.isNotBlank(moduleType) && "console".equalsIgnoreCase(moduleType)) {
                    restResult.setMessage("当前账号已过期，无法登录系统，请联系公司管理员。");
                }
                throw new BizException(HttpConst.Unauthorized, restResult.getMessage().toString());
            }
        }
        // 判断运营管理是否关联运营实体
        if (cn.com.cloudstar.rightcloud.oss.common.constants.Constants.BSS.equalsIgnoreCase(moduleType) && 100L != currentUser.getUserSid()) {
            List<Long> entityIds = entityUserMapper.queryEntityIds(currentUser.getUserSid());
            if (org.apache.commons.collections.CollectionUtils.isEmpty(entityIds)) {
                throw new BizException(HttpConst.Unauthorized, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1848280179));
            }
        }
        //判断是否是首次登陆
        Criteria criteria = new Criteria();
        criteria.put("opUser", currentUser.getAccount());
        criteria.put("opType", "login");
        User confirmUser = userMapper.selectByPrimaryKey(authUserInfo.getUserSid());
        if (Objects.nonNull(confirmUser) && "Y".equals(confirmUser.getNavigationConfirm())) {
            user.put("loginNotFirstFlag", confirmUser.getNavigationConfirm());
        }
        // 如果是客户登录，判断资源状态是否被冻结
        if (isFreeze(currentUser.getUserSid())) {
            user.put("freezeStatus", ZERO);
        }
        if (StringUtil.isEmpty(currentUser.getCertificationStatus())) {
            user.put("certificationStatus", CertificationStatus.NOAUTH);
        } else {
            user.put("certificationStatus", currentUser.getCertificationStatus());
        }
        user.put("remark", currentUser.getRemark());

        Org org = orgService.selectByPrimaryKey(authUserInfo.getOrgSid());
        if (org != null) {
            Org rootOrg = orgService.selectRootOrg(authUserInfo.getOrgSid());
            Map<String, Object> currentOrg = new HashMap<>();
            currentOrg.put("skip2FA", rootOrg.getSkip2FA());
            currentOrg.put("orgSid", org.getOrgSid());
            currentOrg.put("orgName", org.getOrgName());
            currentOrg.put("orgType", org.getOrgType());
            currentOrg.put("parentOrgSid", org.getParentId());
            currentOrg.put("certificationStatus", org.getCertificationStatus());
            currentOrg.put("remark", org.getRemark());
            BillingAccount billingAccount = billingAccountService.getBillingAccount(org.getOrgSid());
            if (billingAccount != null) {
                currentOrg.put("balance", billingAccount.getBalance());
                currentOrg.put("accountSid", billingAccount.getAccountSid());
                if (QuotaMode.NOLIMIT.equals(org.getQuotaCtrl())) {
                    currentOrg.put("quotaMode", null);
                } else {
                    currentOrg.put("quotaMode", org.getQuotaMode());
                }
            } else {
                currentOrg.put("balance", BigDecimal.ZERO);
                currentOrg.put("accountSid", null);
                currentOrg.put("quotaMode", null);
            }
            if (CertificationStatus.NOAUTH.equals(org.getCertificationStatus()) ||
                    CertificationStatus.AUTHFILED.equals(org.getCertificationStatus())) {
                user.put("type", "user");
            } else {
                user.put("type", "company");
            }
            user.put("currentOrg", currentOrg);
            setSaasLimitDate(org, user);

            // 运营管理员创建的分销商，authName为空，因此将realName值返回前端
            if ("distributor".equals(org.getOrgType()) && StringUtil.isNullOrEmpty(
                    String.valueOf(user.get("realName")))) {
                user.put("realName", currentUser.getRealName());
            }
        }
        user.put("currentRoleType", AuthUtil.getCurrentUserType());

        // 获取demo账号
        String demoAccount = PropertiesUtil.getProperty("demo.account");
        user.put("isDemoAccount", authUserInfo.getAccount().equals(demoAccount));

        user.put("platformType", PropertiesUtil.getProperty(SysConfigConstants.PLATFORM_TYPE));
        List<String> userPermissions = getUserPermissions(authUserInfo.isAdmin(),authUserInfo.getEntityId())
                .stream().distinct().collect(Collectors.toList());
        Map<String, List<TreeNode>> menus = getUserMenus(authUserInfo.isAdmin(), moduleType,authUserInfo.getEntityId());

        Map<String, List<SysAuthModuleDTO>> auth = userService.getAuth(moduleType);
        user.put("auth", auth);
        // 子用户侧边栏优化
        if (CollectionUtil.isEmpty(auth)) {
            Map<String, String> routes = new HashMap<>();
            routes.put("router", "403");
            routes.put("belongTo", "other");
            user.put("routes", routes);
            // 增加缓存，方便resource去获取
            Executors.newSingleThreadExecutor().submit(() -> {
                String cache = redisTemplate.opsForValue().get(AuthConstants.USER_PERMISSION_CACHE
                        + authUserInfo.getUserSid());
                List<String> cacheAuths = JSON.parseObject(cache, List.class);
                // 如果缓存里没有权限或者權限有變動，則更新緩存里的權限
                if (CollectionUtil.isEmpty(cacheAuths) || !cacheAuths.equals(userPermissions)) {
                    roleService.addCache(authUserInfo.getUserSid(), userPermissions);
                }
            });
            DescribeUserTokenResponse convert = BeanConvertUtil.convert(user, DescribeUserTokenResponse.class);
            DesensitizationUtil.doDesensitization(convert);
            return convert;
        }

        // 子用户侧边栏优化
        /*if (!ObjectUtils.isEmpty(authUserInfo.getOrgSid()) && !ObjectUtils.isEmpty(authUserInfo.getParentSid())) {
            optimizeUserMenu(userPermissions, menus);
        }*/
        if (CollectionUtil.isEmpty(menus) && BSS.equals(authUserInfo.getRemark())) {
            Map<String, String> routes = new HashMap<>();
            routes.put("router", "403");
            routes.put("belongTo", "other");
            user.put("routes", routes);
            // 增加缓存，方便resource去获取
            Executors.newSingleThreadExecutor().submit(() -> {
                String cache = redisTemplate.opsForValue().get(AuthConstants.USER_PERMISSION_CACHE
                                                                       + authUserInfo.getUserSid());
                List<String> cacheAuths = JSON.parseObject(cache, List.class);
                // 如果缓存里没有权限或者權限有變動，則更新緩存里的權限
                if (CollectionUtil.isEmpty(cacheAuths) || !cacheAuths.equals(userPermissions)) {
                    roleService.addCache(authUserInfo.getUserSid(), userPermissions);
                }
            });
            DescribeUserTokenResponse convert = BeanConvertUtil.convert(user, DescribeUserTokenResponse.class);
            DesensitizationUtil.doDesensitization(convert);
            return convert;
        }
        user.put("menus", menus);
        user.put("permissions", userPermissions);

        List<Role> roles;
        List<String> roleNames = new ArrayList<>();
        roles = roleService.findRolesByUserSid(authUserInfo.getUserSid());
        Long currentOrg = null == authUserInfo.getOrgSid() ? 0L : authUserInfo.getOrgSid();

        List<Role> currentRoles = roleService.findRolesByUserSidAndOrgSid(authUserInfo.getUserSid(), currentOrg);
        if (CollectionUtils.isNotEmpty(currentRoles)) {
            for (Role currentRole : currentRoles) {
                roleNames.add(currentRole.getRoleName());
            }
            user.put("currentRoleName", String.join(",", roleNames));

            user.put("currentRoleIds",
                     currentRoles.stream().map(Role::getRoleSid).collect(Collectors.toList()));

            // 判断用户角色类型
            currentRoles.forEach(role -> {
                if (role.getRoleType().equals(RoleType.BSS)) {
                    user.put("userRoleType", RoleType.BSS);
                    return;
                }
                if (role.getRoleType().equals(RoleType.CONSOLE)) {
                    user.put("userRoleType", RoleType.BSS);
                    return;
                }
                if (role.getRoleType().equals(RoleType.DISTRIBUTOR)) {
                    user.put("userRoleType", RoleType.DISTRIBUTOR);
                    return;
                }
            });

        }

        if (CollectionUtils.isNotEmpty(roles)) {
            Map<String, String> routes = new HashMap<>();
            if (CollectionUtil.isNotEmpty(menus)) {
                boolean isFind = false;
                if (roles.size() > 1 && authUserInfo.isAdmin()) {
                    roles = roles.stream().filter(r -> r.getRoleSid() != 100L).collect(Collectors.toList());
                }

                for (Role role : roles) {
                    if (CollectionUtils.isNotEmpty(menus.get(role.getModuleCategory()))) {
                        routes.put("belongTo", role.getModuleCategory());
                        getFirstRouterName(routes, menus.get(role.getModuleCategory()));
                        user.put("routes", routes);
                        isFind = true;
                        break;
                    }
                }
                if (!isFind) {
                    for (Map.Entry<String, List<TreeNode>> entry : menus.entrySet()) {
                        routes.put("belongTo", entry.getKey());
                        getFirstRouterName(routes, entry.getValue());
                        user.put("routes", routes);
                    }
                }
            }
        }
        //隐私声明没有确认得时候不能跳过隐私声明确认界面，访问总览页面 console界面才需要跳转到隐私声明界面
        if ("console".equalsIgnoreCase(moduleType) && 0 == Convert.toInt(currentUser.getPolicyAgreeSign(), 0)) {
            log.info("开始跳转到隐私声明确认界面....");
            Map<String, String> rMap = MapBuilder.<String, String>create()
                                                 .put("router", "/loginPrivacyStatement")
                                                 .put("belongTo", "mgt")
                                                 .build();
            user.put("routes", rMap);
            log.info("跳转到隐私声明确认界面成功...");
        }

        //是否同意HPC通行证协议
        if ("console".equalsIgnoreCase(moduleType)) {
            SysHpcPass hpcPass = hpcPassMapper.findHpcPass(authUserInfo.getUserSid());
            user.put("hpcPass", Objects.nonNull(hpcPass));
            log.info("是否同意HPC通行证协议...");

            //是否启用iam实体用户
            user.put("enabeIamSub", userService.checkCreateIamSubUser());
            log.info("是否启用iam实体用户...");
        }

        //是否重置密码弹窗, true弹窗，false不弹窗
        Boolean forceResetPwd = Convert.toBool(currentUser.getForceResetPwd(), false);
        if (currentUser.getForceResetPwd()!=null && currentUser.getForceResetPwd().equals("true")) {
            // 改路由为首次登陆
            log.info("开始获取是否需要弹出重置密码框");
            boolean isAdmin = AuthConstants.ADMIN_USER_SID.equals(authUserInfo.getUserSid());
            Map<String, String> rMap = MapBuilder.<String, String>create()
                                                 .put("router", "/firstLogin")
                                                 .put("belongTo", isAdmin ? "sys" : "mgt").build();
            user.put("routes", rMap);
            log.info("获取弹出重置密码框成功");
        } else if (currentUser.getForceResetPwd()!=null && currentUser.getForceResetPwd().equals("1")) {
            // 改路由为重置密码
            log.info("开始获取是否需要弹出重置密码框");
            boolean isAdmin = AuthConstants.ADMIN_USER_SID.equals(authUserInfo.getUserSid());
            Map<String, String> rMap = MapBuilder.<String, String>create()
                    .put("router", "/restPassword")
                    .put("belongTo", isAdmin ? "sys" : "mgt").build();
            user.put("routes", rMap);
            log.info("获取弹出重置密码框成功");
        }

        if (user.get("routes") == null) {
            Map<String, String> routes = new HashMap<>();
            routes.put("router", "403");
            routes.put("belongTo", "other");
            user.put("routes", routes);
        }
        // 按需 隐藏某些菜单
        if (CollectionUtil.isNotEmpty(menus)) {
            filterMenuAndPermission(user, authUserInfo.isLicenceExpire());
        }
        // 增加缓存，方便resource去获取
        Executors.newSingleThreadExecutor().submit(() -> {
            String cache = redisTemplate.opsForValue().get(AuthConstants.USER_PERMISSION_CACHE
                                                                   + authUserInfo.getUserSid());
            List<String> cacheAuths = JSON.parseObject(cache, List.class);
            // 如果缓存里没有权限或者權限有變動，則更新緩存里的權限
            if (CollectionUtil.isEmpty(cacheAuths) || !cacheAuths.equals(userPermissions)) {
                roleService.addCache(authUserInfo.getUserSid(), userPermissions);
            }
        });

        // 多运营实体，划资源冻结
        if (cn.com.cloudstar.rightcloud.oss.common.constants.Constants.CONSOLE.equalsIgnoreCase(moduleType)) {
            HashMap<String, String> freezeProductStatus = Maps.newHashMap();
            // 默认资源正常
            freezeProductStatus.put(ProductCodeEnum.HPC.getProductCode(), ONE);
            freezeProductStatus.put(ProductCodeEnum.HPC_DRP.getProductCode(), ONE);
            freezeProductStatus.put(ProductCodeEnum.BIZ_BAG.getProductCode(), ONE);
            freezeProductStatus.put(ProductCodeEnum.MODEL_ARTS.getProductCode(), ONE);
            freezeProductStatus.put(ProductCodeEnum.SFS2.getProductCode(), ONE);
            freezeProductStatus.put(ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode(), ONE);
            freezeProductStatus.put(ProductCodeEnum.HPC_SAAS.getProductCode(), ONE);
            freezeProductStatus.put(ProductCodeEnum.MA_BMS.getProductCode(), ONE);
            freezeProductStatus.put("MaConsole", ONE);
            // 支持资源冻结List
            List<String> products = new ArrayList<>(Arrays.asList(ProductCodeEnum.HPC.getProductCode(),
                                                    ProductCodeEnum.HPC_DRP.getProductCode(),
                                                    ProductCodeEnum.BIZ_BAG.getProductCode(),
                                                    ProductCodeEnum.MODEL_ARTS.getProductCode(),
                                                    ProductCodeEnum.SFS2.getProductCode(),
                                                    ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode(),
                                                    ProductCodeEnum.HPC_SAAS.getProductCode(),
                                                    ProductCodeEnum.MA_BMS.getProductCode()));

            // 子账户没有账户数据，需查找租户的
            Long parentSid = currentUser.getParentSid();
            List<BizBillingAccount> accounts = bizBillingAccountMapper.selectByParams(
                    new Criteria("adminSid", parentSid == null ? currentUser.getUserSid() : parentSid));
            // drp冻结标识，手动冻结生效
            boolean drpFreeze = false;
            for (BizBillingAccount account : accounts) {
                if (FREEZE.equals(account.getStatus())) {
                    Criteria serviceCategoryCriteria = new Criteria("entityId", account.getEntityId());
                    serviceCategoryCriteria.put("serviceComponent", "innerService");
                    List<ServiceCategory> serviceCategories = serviceCategoryMapper.selectByParams(
                            serviceCategoryCriteria);
                    // 该账户关联的产品
                    List<String> serviceTypes = serviceCategories.stream()
                                                                 .map(ServiceCategory::getServiceType)
                                                                 .collect(Collectors.toList());
                    for (String serviceType : serviceTypes) {
                        if (products.contains(serviceType)) {
                            freezeProductStatus.put(serviceType, ZERO);
                        }
                        if (ProductCodeEnum.MODEL_ARTS.getProductCode().equals(serviceType)) {
                            freezeProductStatus.put("MaConsole", ZERO);
                            if (cn.com.cloudstar.rightcloud.oss.common.constants.Constants.ONE.equals(
                                    account.getUnfreezeType())) {
                                drpFreeze = true;
                            }

                        }
                    }
                }
            }
            // DRP冻结需判断当前租户是否至少有一个冻结
            // 系统控制台-平台配置-modelArts版本V2特性
            if (cn.com.cloudstar.rightcloud.oss.common.constants.Constants.V2.equals(PropertiesUtil.getProperty(SysConfigConstants.MODEL_ARTS_EDITION))) {
//                if (isDrpFrozen(currentUser.getUserSid(),currentUser.getOrgSid())) {
//                    freezeProductStatus.put("MaConsole", ZERO);
//                } else
                if (!iamPermissionService.isDrpNormal(currentOrg) && ResHpcClusterStatus.FROZEN.equals(
                        iamPermissionService.selectModelartsStatus(currentUser.getOrgSid()))) {
                    freezeProductStatus.put("MaConsole", TWO);
                } else {
                    freezeProductStatus.put("MaConsole", ONE);
                }
            }

            // MA-BMS未启用隐藏其功能
            if (!ONE.equals(String.valueOf(org.getBmsEnable()))) {
                freezeProductStatus.put(ProductCodeEnum.MA_BMS.getProductCode(), MINUS_ONE);
            }

            user.put("freezeProductStatus", freezeProductStatus);
        }

        // 许可证标识
        LicenseVo licenseVo = LicenseUtil.queryLicenseInfoFromDb();
        List<String> allowProducts = Stream.of(ProductCodeEnum.MODEL_ARTS.getProductCode(),
                                               ProductCodeEnum.HPC.getProductCode()).collect(Collectors.toList());
        List<String> disabledFeatures = new ArrayList<>();
        if (Objects.nonNull(licenseVo)) {
            String versionType = licenseVo.getVersionType();
            if (LicenseUtil.STAND.equals(versionType)) {
                allowProducts = Stream.of(ProductCodeEnum.MODEL_ARTS.getProductCode()).collect(Collectors.toList());
            } else if (LicenseUtil.CLOSE_AI.equals(versionType)) {
                allowProducts = Stream.of(ProductCodeEnum.HPC.getProductCode()).collect(Collectors.toList());
            }
            if (licenseVo.isAi1980BAdvancedExpansionPackage()) {
                licenseVo.setAi1980BChipSupport(true);
                licenseVo.setAi1980BLogicalResourcePool(true);
            }
            List<String> featuresList = FeaturesEnum.getValues();
            Field[] declaredFields = licenseVo.getClass().getDeclaredFields();
            FeaturesEnum.getValues();
            try {
                for (Field field : declaredFields) {
                    field.setAccessible(true);
                    if (featuresList.contains(field.getName()) && (Boolean) field.get(licenseVo)) {
                        disabledFeatures.add(field.getName());
                    }
                }
            } catch (IllegalAccessException e) {
                log.error("许可证属性错误");
            }
        }
        user.put("allowProducts", allowProducts);
        user.put("disabledFeatures", disabledFeatures);
        //运营与云管多云集成标识
        user.put("bssIntegrateCmp",bssIntegrateCmp);
        if(bssIntegrateCmp){
            user.put("integrateCmpUrl",integrateCmpUrl);
        }

        DescribeUserTokenResponse convert = BeanConvertUtil.convert(user, DescribeUserTokenResponse.class);
        if (org != null) {
            convert.setBmsFreezingStrategy(org.getFreezingStrategy());
            convert.setBmssTrategyBufferPeriod(org.getStrategyBufferPeriod());
        }
        DesensitizationUtil.doDesensitization(convert);
        return convert;
    }


    /**
     * 冻结策略
     * @param userSid
     * @return boolean
     */
    private boolean isDrpFrozen(Long userSid, Long orgSid) {
        List<String> poolNames = serviceOrderService.isDrpFrozen(orgSid);
        return maRemoteService.isFrozen(userSid,poolNames);
    }

    private void optimizeUserMenu(List<String> userPermissions, Map<String, List<TreeNode>> menus) {
        if (CollUtil.isEmpty(menus)) {
            log.warn("menus is empty");
            return;
        }
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        Long userSid = authUserInfo.getUserSid();
        // 获取当前登录用户关联的用户组
        List<Long> groupIds = userGroupMapper.selectByParams(new Criteria().put("userSid", userSid))
                .stream()
                .map(UserGroup::getGroupSid)
                .collect(Collectors.toList());
        // 获取当前登录用户关联的策略
        List<Long> assertionSids = assertionMapper.selectUserAssertionSids(userSid);
        // 当用户关联的用户组不是系统初始用户组且关联策略不包含所有时，移除总览菜单
        if (Collections.disjoint(Arrays.asList(1L, 2L, 3L, 4L, 5L), groupIds)
                && !assertionSids.contains(1L)) {
            userPermissions.remove("CA");
            menus.get("mgt")
                    .get(0)
                    .getChildren()
                    .removeIf(treeNode -> "CA".equals(treeNode.getId()));
        }
    }


    private void setSaasLimitDate(Org org, Map<String, Object> user) {
        if (!SaasUtil.isSaasEnable()) {
            return;
        }
        String treePath = org.getTreePath();
        List<String> split = StrUtil.split(treePath, CharUtil.SLASH, true, true);
        String first = CollectionUtil.getFirst(split);
        Long value = Convert.toLong(first, org.getOrgSid());
        if (Objects.nonNull(value)) {
            Criteria criteria = new Criteria();
            criteria.put("orgSid", value);
            List<SysSaasOrg> sysSaasOrgs = sysSaasOrgService.selectByParams(criteria);
            SysSaasOrg saasOrg = CollectionUtil.getFirst(sysSaasOrgs);
            Optional.ofNullable(saasOrg).ifPresent(o -> user.put("limitDate", o.getLimitDate()));
        }
    }

    private void filterMenuAndPermission(Map<String, Object> user, Boolean isLicenceExpire) {
        List<String> permissions = (List<String>) user.get("permissions");
        Map<String, Object> menus = (Map<String, Object>) user.get("menus");

        // 确定过滤范围
        List<String> blackPermissionRegxs = new ArrayList<>();
        // 过滤permissions
        permissions = permissions.stream().filter(p -> {
            for (String regx : blackPermissionRegxs) {
                if (p.matches(regx)) {
                    return false;
                }
            }
            return true;
        }).collect(Collectors.toList());
        user.put("permissions", permissions);
        // 过滤 menus

        List<String> parentNeedRemovedKeys = new ArrayList<>();
        menus.entrySet().forEach(kv -> {
            List<TreeNode> arrays = (List<TreeNode>) kv.getValue();
            List<TreeNode> needRemoved = new ArrayList<>();
            for (TreeNode array : arrays) {
                boolean removeFlag = filterMenu(array, blackPermissionRegxs);
                if (removeFlag) {
                    needRemoved.add(array);
                }
            }
            for (TreeNode treeNode : needRemoved) {
                arrays.remove(treeNode);
            }
            kv.setValue(arrays);
            if (arrays.size() == 0) {
                parentNeedRemovedKeys.add(kv.getKey());
            }
        });
        for (String key : parentNeedRemovedKeys) {
            menus.remove(key);
        }
        user.put("menus", menus);
        user.put("isLicenceExpire", isLicenceExpire);
    }

    // 递归过滤菜单
    private boolean filterMenu(TreeNode menu, List<String> blackPermissionRegxs) {
        List<Integer> needRemove = new ArrayList<>();
        for (String regx : blackPermissionRegxs) {
            if ((menu.getId()).matches(regx)) {
                //需要被删掉
                return true;
            }
        }
        if (CollectionUtils.isNotEmpty(menu.getChildren())) {
            List<TreeNode> children = menu.getChildren();
            for (int i = 0; i < children.size(); i++) {
                boolean removeFlag = filterMenu(children.get(i), blackPermissionRegxs);
                if (removeFlag) {
                    needRemove.add(i);
                }
            }
            for (Integer removeIndex : needRemove) {
                children.remove(removeIndex.intValue());
            }
            menu.setChildren(children);
        }
        return false;
    }

    private void getFirstRouterName(Map<String, String> routes, List<TreeNode> nodeList) {

        for (TreeNode treeNode : nodeList) {
            if (StringUtils.isNotBlank(treeNode.getSpecial())) {
                routes.put("router", treeNode.getSpecial());
                return;
            }
            getFirstRouterName(routes, treeNode.getChildren());
            //说明在子菜单中已找到 firstRoute 用于打断外层 for 循环 #25988
            if (!"403".equals(routes.get("router"))) {
                return;
            }
        }

    }

    /**
     * 查看用户是否被冻结
     *
     * @param userId 用户id
     *
     * @return {@link Boolean}
     */
    @ApiOperation(httpMethod = "GET", value = "查看用户是否被冻结", notes = "查看用户是否被冻结")
    @GetMapping("/isFreeze/{userId}")
    @AuthorizeOss(action = AuthModule.CB.CB19)
    public Boolean isFreeze(@PathVariable Long userId) {
        User currentUser = this.userService.selectByPrimaryKey(userId);
        // 如果是客户登录，判断资源状态是否被冻结
        if (currentUser.getOrgSid() != null && currentUser.getFreezeStatus() != null && ZERO.equals(
                currentUser.getFreezeStatus())) {
            return Objects.nonNull(currentUser.getUnfreezeType());
        }
        return false;
    }

    /**
     * 验证用户是否存在
     *
     * @param request 请求
     *
     * @return {@link Boolean}
     */
    @ApiOperation(httpMethod = "POST", value = " 验证用户是否存在", notes = "通过提交request.getValidateType()、request.getValidateData()验证用户是否存在")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "request.getValidateType()", value = "验证类型", paramType = "query", dataType = "string", required = true),
            @ApiImplicitParam(name = "request.getValidateData()", value = "验证数据", paramType = "query", dataType = "string", required = true)})
    @AuthorizeOss(action = AuthModule.BQ.BQ0101+AuthModule.COMMA+AuthModule.CH.CH0101+AuthModule.COMMA+ ZC.ZC01_ZC02)
    @PostMapping("/validation/exists")
    @ResponseBody
    public Boolean validateUserExits(@RequestBody @Valid ValidateRequest request) {

        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        AssertUtil.requireNonBlank(authUserInfo, "获取当前登录用户失败");
        // 获取当前登录用户的组id
        List<Long> userGroupIds = userGroupMapper.selectByParams(new Criteria().put("userSid", authUserInfo.getUserSid()))
                                                 .stream()
                                                 .map(UserGroup::getGroupSid)
                                                 .collect(Collectors.toList());
        // 检查用户是否属于特定的用户组
        Set<Long> groupIds = new HashSet<>(Arrays.asList(2L, 3L, 4L, 5L));
        if (userGroupIds.stream().anyMatch(groupIds::contains)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
        }
        if (ObjectUtils.isEmpty(request.getValidateType())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1372762068));
        }
        if (ObjectUtils.isEmpty(request.getValidateData())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_27318110));
        }
        return validateUserInfo(request.getValidateType(), request.getValidateData(), request.getIsPreOpen());
    }

    /**
     * 验证用户信息是否可用
     * @param
     * @return true 可用，false 不可用
     */
    private Boolean validateUserInfo(String validateType,String validateData,boolean isPreOpen) {

        if ("mobile".equals(validateType)) {
            if (isPreOpen) {
                return true;
            }
            List<User> userList = this.userService.findUserByMobile(validateData);
            return CollectionUtils.isEmpty(userList);
        } else if ("email".equals(validateType)) {
            if (isPreOpen) {
                return true;
            }
            List<User> userList = this.userService.findUserByEmail(validateData);
            return CollectionUtils.isEmpty(userList);
        } else if ("account".equals(validateType)) {
            try {
                this.userService.checkRetentionAccount(validateData);
            }catch (BizException e){
                if (WebUtil.getMessage(MsgCd.USER_ACCOUNT_USED).equals(e.getMessage())) {
                    return Boolean.FALSE;
                }
            }
            List<User> userList = this.userService.findUserByAccountV(validateData);
            if (CollectionUtil.isEmpty(userList)) {
                if (!sysMUserDelLogService.exist(validateData)) {
                    return Boolean.TRUE;
                }
            }
            return Boolean.FALSE;
        } else if ("orgName".equals(validateType)) {
            Criteria criteria = new Criteria();
            criteria.put("orgName", validateData);
            List<Org> orgs = orgService.selectByParamsV(criteria);
            return CollectionUtils.isEmpty(orgs);
        }
        return Boolean.TRUE;
    }

    /**
     * 验证用户是否存在
     *
     * @param account      帐户
     * @param validateType 验证类型
     * @param validateData 验证数据
     * @return {@link Boolean}
     * @throws Exception 例外
     */
    @ApiOperation(httpMethod = "GET", value = "", notes = "通过提交validateType、validateData验证用户是否存在")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "validateType", value = "验证类型", paramType = "query", dataType = "string", required = true),
            @ApiImplicitParam(name = "validateData", value = "验证数据", paramType = "query", dataType = "string", required = true)})
    @GetMapping("/validation/existExcludeCurrentUser")
    @ResponseBody
    @AuthorizeOss(action = BQ.BQ0108)
    public Boolean existExcludeCurrentUser(@NotNull(message = "当前用户不能为空!") String account, @NotNull(message = "验证类型不能为空！") String validateType,
                                           @NotNull(message = "验证数据不能为空！") String validateData) throws Exception {

        //电话号码
        if (cn.com.cloudstar.rightcloud.oss.common.constants.Constants.MOBILE.equals(validateType)) {
            List<User> userList = this.userService.findUserExcludeCurrentUserByMobile(validateData,account);
            return org.apache.commons.collections.CollectionUtils.isEmpty(userList);
            //邮箱
        } else if (cn.com.cloudstar.rightcloud.oss.common.constants.Constants.EMAIL.equals(validateType)) {
            List<User> userList = this.userService.findUserExcludeCurrentUserByEmail(validateData,account);
            return org.apache.commons.collections.CollectionUtils.isEmpty(userList);
        }
        return Boolean.FALSE;
    }

    /**
     * 注册用户
     *
     * @param request 请求
     * @return {@link RestResult}
     * @since 2.4.1
     */
    @ApiOperation(httpMethod = "POST", value = "注册用户", notes = "通过提交user实体对象提交，请用json数据格式")
    @PostMapping("/register")
    @ListenExpireBack
    @Encrypt
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "'用户'", resource = OperationResourceEnum.REGISTER_USER, tagNameUs ="'User'")
    public RestResult registerProtalUser(@RequestBody @Valid RegisterUserRequest request) {
        //查询当前是否在升级中
        boolean isUpgrade = sysconfigService.queryIsSysUpgradeNow();
        User remoteUser = new User();
        if (isUpgrade) {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_472377897));
        }
        if (request.getCompany().getOrgName().equals(request.getUser().getAccount())) {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_910546272));
        }

        String configValidresult = userService.validPasswordPolicy(request.getUser().getPassword());
        if (configValidresult != null) {
            return new RestResult(RestResult.Status.FAILURE, configValidresult);
        }

        UserVO user = request.getUser();
        //验证短信验证码
        CheckSmsCodeUtil.checkCode(user.getMobile(), "register" + user.getSmscode());


        //检查组织是否存在
        if (!validateUserInfo("orgName", request.getCompany().getOrgName(), false)) {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ORG_NAME_USED));
        }
        //检查邮箱是否存在
        if (!validateUserInfo("email", user.getEmail(), false)) {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_850188202));
        }
        //检查手机号是否存在
        if (!validateUserInfo("mobile", user.getMobile(), false)) {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1006327739));
        }
        //检查用户名是否存在
        if (!validateUserInfo("account", user.getAccount(), false)) {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.USER_ACCOUNT_USED));
        }

        boolean result;

        if ((!StringUtil.isNullOrEmpty(request.getFlag())) && "rightcloud".equals(request.getFlag())) {
            request.getUser().setPolicyAgreeSign(1);
            request.getUser().setPolicyAgreeTime(new Date());
            remoteUser = userService.insertRegisterUser(JSON.toJSONString(request));
            try {
                //远程添加
                IamUser iamUser = BeanConvertUtil.convert(remoteUser, IamUser.class);
                iamUser.setGroupIds(Collections.singletonList(1L));
                iamUser.setIsAdmin(true);
                iamRemoteService.insertUser(iamUser);
            } catch (Exception e) {
                cancelService.cancelUser(remoteUser.getUserSid());
                // 回滚用户组
                cancelService.cancelOrg(remoteUser.getOrgSid());
                throw new BizException(e.getMessage());
            }
            result = true;
        } else {
            result = userService.insertTenantUser(JSON.toJSONString(request));
        }
        if (result) {
            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_REGISTER_SUCCESS), remoteUser.getUserSid());
        } else {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1414917458));
        }
    }

    /**
     * 添加企业用户
     *
     * @param request 请求
     *
     * @return {@link RestResult}
     * @since 2.4.1
     */
    @AuthorizeOss(action = USER.CREATE_LOCAL_USER)
    @ApiOperation(httpMethod = "POST", value = " 添加企业用户")
    @PostMapping()
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "#request.account", resource = OperationResourceEnum.ADD_COMPANY_USER)
    @Encrypt
    public RestResult addCompanyUser(@RequestBody @Valid CreateCompanyUserRequest request) {
        Optional.ofNullable(request.getRoles()).ifPresent(roles -> {
            if (roles.stream().map(RoleSidVO::getRoleSid).anyMatch(r -> r == 100L)) {
                throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
            }
        });
        if(request.getAccount().equals(request.getPassword())){
            throw new BizException("账户名与密码不能保存一致");
        }
        if(StringUtil.isNotBlank(request.getEntityIds())){
            List<String> entityIds = StringUtil.split(request.getEntityIds(),',');
            if(CollectionUtils.isNotEmpty(entityIds)){
                for(String entityId: entityIds){
                    try {
                        SysBssEntity  bssEntity =  sysBssEntityService.selectByPrimaryKey(Long.valueOf(entityId));
                        if(bssEntity == null){
                            throw new BizException("运营实体id参数非法");
                        }
                    } catch (NumberFormatException e) {
                        throw new BizException("运营实体id参数非法");
                    } catch (BizException e) {
                        throw new BizException("运营实体id参数非法");
                    }
                }

            }
        }


        List<User> existsAccount = userMapper.findUserByAccountV(request.getAccount());
        if(CollectionUtils.isNotEmpty(existsAccount)){
            throw new BizException("该用户名已被使用");
        }
        User user = BeanConvertUtil.convert(request, User.class);
        cn.com.cloudstar.rightcloud.oss.common.pojo.User authUser = AuthUtil.getAuthUser();
        if (authUser != null && authUser.getUserSid() == 100L) {
            //查询是否开启双因子
            SysConfig sysConfig = sysconfigService.selectByConfigKey("keycloak.admin.skip2FA");
            user.setSkip2FA(sysConfig.getConfigValue());
        }

        if (!Strings.isNullOrEmpty(request.getMobile())) {
            List<User> userList = this.userService.findUserByMobile(request.getMobile());
            if (!CollectionUtils.isEmpty(userList)) {
                BizException.throwException(WebUtil.getMessage(MsgCd.ERROR_MSG_00012));
            }
        }
        if (!Strings.isNullOrEmpty(request.getEmail())) {
            List<User> userList = this.userService.findUserByEmail(request.getEmail());
            if (!CollectionUtils.isEmpty(userList)) {
                BizException.throwException(WebUtil.getMessage(MsgCd.ERROR_MSG_00013));
            }
        }
        if (Objects.nonNull(request.getStartTime()) && Objects.nonNull(request.getEndTime())) {
            if (request.getStartTime().after(request.getEndTime())) {
                BizException.throwException(
                        WebUtil.getMessage(MsgCd.THE_END_TIME_SHOULD_BE_GREATER_THAN_THE_START_TIME));
            }
        }

        // 37584 开始时间大于当前时间 不算时分秒
        if (Objects.nonNull(request.getStartTime())
                && new Date().compareTo(request.getStartTime()) >= 0) {
            BizException.throwException("开始时间应大于当前时间");
        }
        if (Objects.nonNull(request.getEndTime()) && request.getEndTime().before(new Date())) {
            BizException.throwException(WebUtil.getMessage(MsgCd.THE_END_TIME_SHOULD_BE_GREATER_THAN_THE_CURRENT_TIME));
        }

        // 检查企业是否与所选择的企业角色关联
        checkRoleLinkToOrg(request.getRoles(), request.getCompanyId());

        // 检查项目是否与所选择的项目角色关联
        checkRoleLinkToOrg(request.getProjectRoles(), request.getProjectId());
        user.setOrgSid(request.getCompanyId());
        user.setAuthType(USER_AUTH_TYPE.AUTH_TYPE_LOCAL);
        user.setEntityIds(request.getEntityIds());
        //是否重置密码，默认为false
        user.setForceResetPwd(Convert.toBool(request.getForceResetPwd(), false).toString());
        // 验证密码复杂度
        String validError = userService.validPasswordByPolicyToError(request.getPassword(), user.getOrgSid(), null,
                user.getParentSid(), true);
        if (!ObjectUtils.isEmpty(validError)) {
            throw new BizException(validError);
        }
        // 放入未加密密码
        user.setPassword(user.getPassword());
        //密码策略
        PasswordPolicyDTO policy = userService.buildPasswordPolicy(user.getOrgSid(), user.getParentSid());
        //修改密码有效期
        userService.setUserPasswordEndTime(user, policy);
        boolean addUserRs = userService.insertUser(user);
        if (addUserRs) {
            try {
                //远程添加
                iamRemoteService.insertUser(
                        BeanConvertUtil.convert(user, IamUser.class));
                //mq同步至云管
                userSyncService.createUser(user);
                //添加到 activity
                List<String> collect = request.getRoles()
                        .stream()
                        .map(a -> a.getRoleSid().toString())
                        .collect(Collectors.toList());
                processService.getCandidateGroups(collect);
            } catch (Exception e) {
                cancelService.cancelUser(user.getUserSid());
                // 回滚用户组
                cancelService.cancelOrg(user.getOrgSid());
                throw new BizException(e.getMessage());
            }

            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS),
                    user.getUserSid());
        }
        return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERROR_OPERATE_FAILURE));
    }

    private void checkRoleLinkToOrg(List<RoleSidVO> roleSidVOS, Long orgSid) {
        if (!CollectionUtils.isEmpty(roleSidVOS) && Objects.nonNull(orgSid)) {
            List<Role> companyRoles = roleService.findRolesByOrgSid(orgSid);

            Set<Long> companyRoleSet = companyRoles.stream().map(Role::getRoleSid).collect(Collectors.toSet());

            Set<Long> userCompanyRoleSet = roleSidVOS.stream().filter(roleSidVO -> {
                Role role = roleService.selectByPrimaryKey(roleSidVO.getRoleSid());
                return role.getIsSys().equals(ZERO);
            }).map(RoleSidVO::getRoleSid).collect(Collectors.toSet());

            if (!companyRoleSet.containsAll(userCompanyRoleSet)) {
                BizException.throwException(WebUtil.getMessage(MsgCd.ERROR_MSG_00014));
            }
        }
    }


    /**
     * 更新企业用户(用户管理-编辑)
     *
     * @param request 请求
     *
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = AuthModule.BQ.BQ03.BQ0301)
    @ApiOperation(httpMethod = "PUT", value = " 更新企业用户")
    @PutMapping("/account")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "#request.account",
            resource = OperationResourceEnum.UPDATE_COMPANY_USER, bizId = "#request.userSid", param = "#request")
    @Transactional
    @DataPermission(resource = OperationResourceEnum.UPDATE_COMPANY_USER, bizId = "#request.userSid")
    public RestResult updateCompanyUser(@RequestBody @Valid UpdateCompanyUserRequest request) {
        User user = BeanConvertUtil.convert(request, User.class);
        if (user == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1639013906));
        }
        if (StringUtil.isNotBlank(user.getRealName()) && cn.com.cloudstar.rightcloud.oss.common.util.StringUtil.stringFilter(user.getRealName())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2099493327));
        }
        // 检查该用户是否拥有内置的角色，没有则不能设置系统管理员权限
        List<Role> roles = roleService.selectRoleByUserSid(user.getUserSid());
        List<Role> roleList = roles.stream().filter(t -> t.getRoleSid() == 100L).collect(Collectors.toList());
        if (roleList.size() > 0) {
            long count = roles.stream().map(Role::getIsSys).filter(t -> "1".equals(t)).count();
            if (count <= 0) {
                return new RestResult(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1661954069));
            }
        }
        cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser authUser = BasicInfoUtil.getCurrentUserInfo();
        //判断当前用户无法更新自己
        if (ObjectUtils.isEmpty(authUser)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1038086020));
        }
        if (Objects.nonNull(request.getJustRealName())) {
            if (!request.getJustRealName() && authUser.getUserSid().equals(user.getUserSid())) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2002246151));
            }
        }
        List<Role> authRoles = roleService.selectRoleByUserSid(authUser.getUserSid());
        List<Role> authRoleList = authRoles.stream().filter(t -> t.getRoleSid() == 100L).collect(Collectors.toList());
        if (authRoleList.size() <= 0 && Boolean.TRUE.equals(user.getAdminFlag())) {
            return new RestResult(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_822770033));
        }
        if (Objects.nonNull(request.getStartTime()) && Objects.nonNull(request.getEndTime())) {
            if (request.getStartTime().after(request.getEndTime())) {
                BizException.throwException(
                        WebUtil.getMessage(MsgCd.THE_END_TIME_SHOULD_BE_GREATER_THAN_THE_START_TIME));
            }
        }
        if (Objects.nonNull(request.getEndTime()) && request.getEndTime().before(new Date())) {
            BizException.throwException(WebUtil.getMessage(MsgCd.THE_END_TIME_SHOULD_BE_GREATER_THAN_THE_CURRENT_TIME));
        }

        // 只更新真实姓名
        if (Objects.nonNull(request.getJustRealName()) && request.getJustRealName()) {
            if (userService.updateUserRealName(user)) {
                AssertUtil.requireNonBlank(user, "获取当前登录用户失败");
                return new RestResult(Status.SUCCESS,
                        WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS),
                        user.getUserSid());
            } else {
                return new RestResult(Status.FAILURE, WebUtil.getMessage(
                        MsgCd.ERROR_OPERATE_FAILURE));
            }
        }

        User userDb = userService.selectByPrimaryKey(request.getUserSid());
        if (userDb == null) {
            BizException.throwException(String.format(WebUtil.getMessage(MsgCd.ERROR_RES_NOT_FOUND), "用户"));
        }
        user.setAccount(userDb.getAccount());
        request.setAccount(userDb.getAccount());
        //检查email
        if (!ObjectUtils.isEmpty(request.getEmail()) && !request.getEmail().equals(userDb.getEmail())) {
            checkEmail(request.getEmail(), userDb.getUserSid());
        }
        //检查mobile
        if (!ObjectUtils.isEmpty(request.getMobile()) && !request.getMobile().equals(userDb.getMobile())) {
            checkMobile(request.getMobile(), request.getSmsCode(), userDb.getOrgSid());
        }


        // 检查企业是否与所选择的企业角色关联
        checkRoleLinkToOrg(request.getUserRoles(), userDb.getCompanyId());

        //更新公司名称，应用场景，人员规模，所属行业
        if (!ObjectUtils.isEmpty(request.getOrgName())
                || !ObjectUtils.isEmpty(request.getApplicationScenario())
                || !ObjectUtils.isEmpty(request.getPersonnelSize())
                || !ObjectUtils.isEmpty(request.getIndustryType())) {
            updateCompanyByUser(request, userDb);
        }
        // 运营控制台-客户管理-编辑账户中修改了org_name同步更新到bizBillingAccount中的account_name
        BizBillingAccount account = new BizBillingAccount();
        if (!ObjectUtils.isEmpty(request.getOrgName()) && !ObjectUtils.isEmpty(userDb.getOrgSid())) {
            account.setAccountName(request.getOrgName());
            account.setOrgSid(userDb.getOrgSid());
            account.setSkipCCSPHandle(true);
            bizBillingAccountService.updateByOrgSid(account);
        }

        // 更新云运营DB
        if (!ObjectUtils.isEmpty(user.getEmail()) && user.getEmail().contains("@")) {
            user.setEmail(user.getEmail());
        }
        if (!ObjectUtils.isEmpty(user.getMobile()) && !user.getMobile()
                                                           .startsWith(PropertiesUtil.getProperty(
                                                                   "password.algorithm.decrypt.prefi"))) {
            user.setMobile(user.getMobile());
        }
        String password = "";
        if (Objects.nonNull(user.getPassword())) {
            password = user.getPassword();
            user.setPassword(CrytoUtilSimple.encodeHash(password));
        }
        boolean result = userService.updateUser(user);

        // 同步到iam(*只能更新姓名和手机号码，不考虑用户是否为租户，不用同步到底层和ldap)
        if (Objects.nonNull(user.getPassword())) {
            user.setPassword(password);
        }
        iamRemoteService.updateUser(BeanConvertUtil.convert(user, IamUser.class));
        // 增加缓存，方便resource去获取
        Executors.newSingleThreadExecutor().submit(() -> {
            redisTemplate.delete(AuthConstants.USER_PERMISSION_CACHE + user.getUserSid());
            String cache = redisTemplate.opsForValue().get(AuthConstants.USER_PERMISSION_CACHE
                                                                   + user.getUserSid());
            List<String> cacheAuths = JSON.parseObject(cache, List.class);
            if (CollectionUtil.isEmpty(cacheAuths)) {
                roleService.addCache(user.getUserSid(), null);
            }
        });
        if (result) {
            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS),
                                  user.getUserSid());
        }
        return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERROR_OPERATE_FAILURE));
    }

    /**
     * 更新企业用户(用户管理-编辑)
     *
     * @param request 请求
     *
     * @return {@link RestResult}
     */
    private RestResult updateUser(UpdateCompanyUserRequest request) {
        User userDb = userService.selectByPrimaryKey(request.getUserSid());
        if (Objects.isNull(userDb)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        if (Objects.nonNull(request.getAdminFlag()) && request.getAdminFlag() && Objects.nonNull(userDb.getOrgSid())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }

        User user = BeanConvertUtil.convert(request, User.class);
        if (user == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1639013906));
        }
        if ("admin".equals(user.getAccount())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_193451409));
        }
        if (StringUtil.isNotBlank(user.getRealName()) && cn.com.cloudstar.rightcloud.oss.common.util.StringUtil.stringFilter(user.getRealName())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2099493327));
        }
        if (FOUR.equals(userDb.getUserType()) && Objects.nonNull(request.getAdminFlag()) && request.getAdminFlag()) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1000659492));
        }
        // 检查该用户是否拥有内置的角色，没有则不能设置系统管理员权限
        List<Role> roles = roleService.selectRoleByUserSid(user.getUserSid());
        List<Role> roleList = roles.stream().filter(t -> t.getRoleSid() == 100L).collect(Collectors.toList());
        if (roleList.size() > 0) {
            long count = roles.stream().map(Role::getIsSys).filter(t -> "1".equals(t)).count();
            if (count <= 0) {
                return new RestResult(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1661954069));
            }
        }
        cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser authUser = BasicInfoUtil.getCurrentUserInfo();
        //判断当前用户无法更新自己
        if (ObjectUtils.isEmpty(authUser)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1038086020));
        }
        if (!BooleanUtil.isTrue(request.getJustRealName()) && authUser.getUserSid().equals(user.getUserSid())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2002246151));
        }
        List<Role> authRoles = roleService.selectRoleByUserSid(authUser.getUserSid());
        List<Role> authRoleList = authRoles.stream().filter(t -> t.getRoleSid() == 100L).collect(Collectors.toList());
        if (authRoleList.size() <= 0 && Boolean.TRUE.equals(user.getAdminFlag())) {
            return new RestResult(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_822770033));
        }
        if (Objects.nonNull(request.getStartTime()) && Objects.nonNull(request.getEndTime())) {
            if (request.getStartTime().after(request.getEndTime())) {
                BizException.throwException(
                        WebUtil.getMessage(MsgCd.THE_END_TIME_SHOULD_BE_GREATER_THAN_THE_START_TIME));
            }
        }
        if (Objects.nonNull(request.getEndTime()) && request.getEndTime().before(new Date())) {
            BizException.throwException(WebUtil.getMessage(MsgCd.THE_END_TIME_SHOULD_BE_GREATER_THAN_THE_CURRENT_TIME));
        }

        // 只更新真实姓名
        if (Objects.nonNull(request.getJustRealName()) && request.getJustRealName()) {
            user.setAccount(userDb.getAccount());
            if (userService.updateUserRealName(user)) {
                AssertUtil.requireNonBlank(user, "获取当前登录用户失败");
                return new RestResult(Status.SUCCESS,
                        WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS),
                        user.getUserSid());
            } else {
                return new RestResult(Status.FAILURE, WebUtil.getMessage(
                        MsgCd.ERROR_OPERATE_FAILURE));
            }
        }

        if (Objects.nonNull(user) && !UserType.DISTRIBUTOR_USER.equals(userDb.getUserType()) && StringUtils.isNotEmpty(user.getEntityIds())) {
            String entityIds = user.getEntityIds();
            Long userSid = userDb.getUserSid();
            Long orgSid = userDb.getOrgSid();
            entityUserMapper.deleteEntities(userSid);
            String[] entityIdList = entityIds.split(",");

            for (String s : entityIdList) {
                EntityUser entityUser = new EntityUser();
                entityUser.setUserSid(userSid);
                entityUser.setOrgSid(orgSid);
                entityUser.setBssEntityId(Long.valueOf(s));
                entityUserMapper.insertEntityUser(entityUser);
            }
        }
        //检查用户名
        if (!ObjectUtils.isEmpty(request.getAccount()) && !request.getAccount().equals(userDb.getAccount())) {
            checkAccount(request.getAccount(), userDb.getUserSid());
        }
        //检查email
        if (!ObjectUtils.isEmpty(request.getEmail()) && !request.getEmail().equals(userDb.getEmail())) {
            checkEmail(request.getEmail(), userDb.getUserSid());
        }
        //检查mobile
        if (!ObjectUtils.isEmpty(request.getMobile()) && !request.getMobile().equals(userDb.getMobile())) {
            checkMobile(request.getMobile(), request.getSmsCode(), userDb.getOrgSid());
        }


        // 检查企业是否与所选择的企业角色关联
        checkRoleLinkToOrg(request.getUserRoles(), userDb.getCompanyId());

        //更新公司名称，应用场景，人员规模，所属行业
        if (!ObjectUtils.isEmpty(request.getOrgName())
                || !ObjectUtils.isEmpty(request.getApplicationScenario())
                || !ObjectUtils.isEmpty(request.getPersonnelSize())
                || !ObjectUtils.isEmpty(request.getIndustryType())) {
            updateCompanyByUser(request, userDb);
        }
        // 运营控制台-客户管理-编辑账户中修改了org_name同步更新到bizBillingAccount中的account_name
        BizBillingAccount account = new BizBillingAccount();
        if (!ObjectUtils.isEmpty(request.getOrgName()) && !ObjectUtils.isEmpty(userDb.getOrgSid())) {
            account.setAccountName(request.getOrgName());
            account.setOrgSid(userDb.getOrgSid());
            bizBillingAccountService.updateByOrgSid(account);
        }

        // 更新云运营DB
        if (!ObjectUtils.isEmpty(user.getEmail()) && user.getEmail().contains("@")) {
            user.setEmail(user.getEmail());
        }
        if (!ObjectUtils.isEmpty(user.getMobile()) && !user.getMobile()
                .startsWith(PropertiesUtil.getProperty(
                        "password.algorithm.decrypt.prefi"))) {
            user.setMobile(user.getMobile());
        }
        String password = "";
        if (Objects.nonNull(user.getPassword())) {
            password = user.getPassword();
            user.setPassword(CrytoUtilSimple.encodeHash(password));
        }
        boolean result = userService.updateUser(user);

        // 同步到iam(*只能更新姓名和手机号码，不考虑用户是否为租户，不用同步到底层和ldap)
        if (Objects.nonNull(user.getPassword())) {
            user.setPassword(password);
        }
        //同步至多云
        userSyncService.updateUser(user);

        iamRemoteService.updateUser(BeanConvertUtil.convert(user, IamUser.class));
        // 增加缓存，方便resource去获取
        Executors.newSingleThreadExecutor().submit(() -> {
            redisTemplate.delete(AuthConstants.USER_PERMISSION_CACHE + user.getUserSid());
            String cache = redisTemplate.opsForValue().get(AuthConstants.USER_PERMISSION_CACHE
                    + user.getUserSid());
            List<String> cacheAuths = JSON.parseObject(cache, List.class);
            if (CollectionUtil.isEmpty(cacheAuths)) {
                roleService.addCache(user.getUserSid(), null);
            }
        });
        if (result) {

            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS),
                    user.getUserSid());
        }
        return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERROR_OPERATE_FAILURE));
    }

    /**
     * 更新企业用户(个人信息-修改)
     *
     * @param request 请求
     *
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = AuthModule.COMMON.PUBLIC.B1.B109)
    @ApiOperation(httpMethod = "PUT", value = " 更新企业用户")
    @PutMapping("/self")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "#request.account",
            resource = OperationResourceEnum.UPDATE_COMPANY_USER, bizId = "#request.userSid")
    @Transactional
    public RestResult updateCompanyUserBySelf(@RequestBody @Valid UpdateCompanyUserRequest request) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (!Objects.equals(authUserInfo.getUserSid(), request.getUserSid())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        return updateUser(request);
    }


    /**
     * 个人信息修改，与CMP集成环境使用客户修改自己的个人信息
     *
     *
     */
    @ApiOperation(httpMethod = "PUT", value = " 更新企业用户")
    @PutMapping("/self/feign")
    @Transactional
    public RestResult updateCompanyUserBySelfFeign(@RequestBody @Valid UpdateCompanyUserRequest request) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (!Objects.equals(authUserInfo.getUserSid(), request.getUserSid())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        return updateUser(request);
    }

    /**
     * 更新企业用户(用户管理-编辑)
     *
     * @param request 请求
     *
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = AuthModule.ZC.ZC02)
    @ApiOperation(httpMethod = "PUT", value = " 更新企业用户")
    @PutMapping()
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "#request.account",
            resource = OperationResourceEnum.UPDATE_COMPANY_USER, bizId = "#request.userSid", param = "#request")
    @Transactional
    public RestResult updateCompanyUserBySys(@RequestBody @Valid UpdateCompanyUserRequest request) {
        return updateUser(request);
    }

    /**
     * [INNER API] 更新企业用户
     *
     * @param request 请求
     *
     * @return {@link RestResult}
     */
    @RejectCall
    @ApiOperation(httpMethod = "PUT", value = " 更新企业用户")
    @PutMapping("/feign")
    public RestResult updateCompanyUserByFeign(@RequestBody @Valid UpdateCompanyUserRequest request) {
        return updateUser(request);
    }

    //更新公司
    private void updateCompanyByUser(UpdateCompanyUserRequest request, User userDb) {
        Company company = new Company();
        company.setOrgSid(userDb.getCompanyId());
        if (!ObjectUtils.isEmpty(request.getOrgName())) {
            //检查公司姓名，如以认证无法修改
            checkCompanyOrgName(request.getOrgName()
                    , !ObjectUtils.isEmpty(request.getAccount()) ? request.getAccount() : userDb.getAccount()
                    , userDb.getCompanyId());
            company.setOrgName(request.getOrgName());
        }
        if (!ObjectUtils.isEmpty(request.getApplicationScenario())) {
            company.setApplicationScenario(request.getApplicationScenario());
        }
        if (!ObjectUtils.isEmpty(request.getPersonnelSize())) {
            company.setPersonnelSize(request.getPersonnelSize());
        }
        if (!ObjectUtils.isEmpty(request.getIndustryType())) {
            company.setIndustryType(request.getIndustryType());
        }
        if (!ObjectUtils.isEmpty(request.getSolution())) {
            company.setSolution(request.getSolution());
        }
        companyMapper.updateByPrimaryKeySelective(company);

    }


    //检查手机号码
    private void checkMobile(String mobile, String smsCode, Long orgSid) {
        long mobileCount = userService.findUserByMobile(mobile)
                                     .stream()
                                     .filter(v -> !v.getOrgSid().equals(orgSid))
                                     .count();
        if (mobileCount > 0) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1006327739));
        }
    }

    //检查邮箱
    private void checkEmail(String email, Long userSid) {
        if (StringUtil.isNotBlank(email)) {
            String emailFrontStr = email.split("@")[0];
            if (emailFrontStr.length() < 4 || emailFrontStr.length() > 16) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1359978533));
            }
        }

        long emailCount = userService.findUserByEmail(email)
                                    .stream()
                                    .filter(v -> !v.getUserSid().equals(userSid))
                                    .count();
        if (emailCount > 0) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_850188202));
        }
    }

    //检查用户名
    private void checkAccount(String account, Long userSid) {
        if (!account.matches("^[a-zA-Z][a-zA-Z0-9-_.]{3,15}$")) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1595981882));
        }
        if (StringUtils.startsWithIgnoreCase(account, "admin")
                || StringUtils.startsWithIgnoreCase(account, "test")) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_112246097));
        }
        long accountCount = userMapper.findUserByAccount(account)
                                      .stream()
                                      .filter(v -> !v.getUserSid().equals(userSid))
                                      .count();
        if (accountCount > 0) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_315408795));
        }
    }


    //检查公司名
    private void checkCompanyOrgName(String orgName, String account, Long companyId) {
        if (orgName.equals(account)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_910546272));
        }
        if (account.replaceAll("[-_.]", "").equalsIgnoreCase(orgName)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_521795834));
        }
        if (!SaasUtil.isSaasEnable()) {
            long companyCount = companyMapper.selectByParams(new Criteria()
                                                                     .put("orgName", orgName)
                                                                     .put("status", 1))
                                             .stream().filter(v -> !v.getCompanyId().equals(companyId)).count();
            if (companyCount > 0) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_518484020));
            }
        }
        Company company = companyMapper.selectByPrimaryKey(companyId);
        if (ObjectUtils.isEmpty(company)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_800405742));
        }
        //如果已经认证则不能修改

        if (CertificationStatus.AUTHSUCCEED.equals(company.getCertificationStatus())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_139016942));
        }
    }

    /**
     * 更新企业用户角色
     *
     * @param request 请求
     *
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = ZC.ZC03)
    @ApiOperation(httpMethod = "PUT", value = "更新企业用户角色")
    @PutMapping("/roles")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "#request.account", resource = OperationResourceEnum.UPDATE_COMPANY_USER_ROLES, bizId = "#request.userSid")
    public RestResult updateCompanyUserRoles(
            @RequestBody @Validated(value = {CompanyUserValidView.class}) UpdateCompanyUserRequest request) {
        User user = BeanConvertUtil.convert(request, User.class);
        if (Objects.equals(AuthConstants.ADMIN_USER_SID, user.getUserSid())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        //获取当前用户以便禁止更新自己角色
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (ObjectUtils.isEmpty(authUser)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1038086020));
        }
        if (ObjectUtils.isEmpty(user)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1648305177));
        }
        if (authUser.getUserSid().equals(user.getUserSid())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_324563825));
        }
        if(user.getUserSid() != null){
            User u = userService.findUserById(user.getUserSid());
            if (Objects.isNull(u)) {
                throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
            }
            if (Objects.nonNull(u.getOrgSid())) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_569164433));
            }
            if (Objects.equals(u.getStatus(), UserStatus.REFUSE)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_462650773));
            }
            if(UserType.PLATFORM_USER.equals(u.getUserType()) && u.getOrgSid() != null){
                if(CollectionUtil.isNotEmpty(user.getRoleIds()) && !user.getRoleIds().get(0).equals(SysRoleEnum.OPERATION_USER.getRoleSid())){
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1130788360));
                }
            }
        }

        boolean result = userService.updateCompanyUserRoles(user);
        if (result) {
            // 同步用户信息到运管
            if(user.getUserSid() != null){
                userSyncService.updateUser(userService.findUserById(user.getUserSid()));
            }
            //清空session
            keycloakRemoteService.logoutByUserIds(Collections.singletonList(user.getUserSid()));
            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS),
                    user.getUserSid());
        }
        return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERROR_OPERATE_FAILURE));
    }

    /**
     * 删除企业用户
     *
     * @param request 请求
     *
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = ZC.ZC08)
    @ApiOperation(httpMethod = "DELETE", value = " 删除企业用户", notes = "通过userIds数组删除用户")
    @DeleteMapping("")
    @OperationLog(type = OperationTypeEnum.DELETE, tagName = "'企业用户'", tagNameUs ="'Enterprise Users'",
            resource = OperationResourceEnum.DELUSER_1, bizId = "#request.userIds", param = "#request")
    public RestResult deleteCompanyUser(@RequestBody @Valid DeleteCompanyUserRequest request) {
        if (ObjectUtils.isEmpty(request.getUserIds())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2098358662));
        }
        List<Long> userIds = request.getUserIds().stream().distinct().collect(Collectors.toList());
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        //不能删除自己
        if (!ObjectUtils.isEmpty(authUserInfo) &&
                userIds.stream().anyMatch(userSid -> userSid.equals(authUserInfo.getUserSid()))) {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERROR_OPERATE_FAILURE));
        }
        //查看符合系统用户的个数
        Long systemUserCount = userMapper.selectSystemUserCount(userIds);
        if (systemUserCount > 0) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2041991220));
        }

        //批量获取用户
        List<User> usersNeedDelete = userMapper.selectBatchUser(userIds);
        if (usersNeedDelete.size() != userIds.size()) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_677366086));
        }
        if (usersNeedDelete.stream().anyMatch(user -> "8".equals(user.getStatus()))) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_917257068));
        }
        //批量删除用户
        boolean result = userService.deleteUsers(userIds);
        // 没有删除失败的，认为成功
        if (result) {
            //清空用户session
            keycloakRemoteService.logoutByUserIds(userIds);
            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS));
        }
        return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERROR_OPERATE_FAILURE));
    }

    /**
     * 删除企业用户
     *
     * @param request 请求
     *
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = BQ.BQ0106)
    @ApiOperation(httpMethod = "DELETE", value = " 删除企业用户", notes = "通过userIds数组删除用户")
    @DeleteMapping("/del")
    @OperationLog(type = OperationTypeEnum.DELETE, tagName = "'用户'", tagNameUs ="'User'",
            resource = OperationResourceEnum.DELUSER, bizId = "#request.userIds", param = "#request")
    @DataPermission(resource = OperationResourceEnum.DELUSER_1, bizId = "#request.userIds")
    public RestResult deleteCompanyUser(@RequestBody @Valid DeleteUserRequest request) {
        // 校验验证码长度
        Pattern pattern = Pattern.compile("[^1-9]");
        Matcher matcher = pattern.matcher(request.getSmscode());
        if (matcher.replaceAll("").trim().length() != 6) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_971001586));
        }

        List<Long> userIdList = request.getUserIds();
        if(CollectionUtils.isNotEmpty(userIdList)){
            for(Long userSid: userIdList){
                User u = userMapper.selectByPrimaryKey(userSid);
                if(u == null){
                    return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
                }
                //设置同步锁
                Object value = redisTemplate.opsForValue().get(UserServiceImpl.DELETE_USER_KEY+ StringUtil.COLON+u.getOrgSid());
                if (value != null) {
                    return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERROR_DELETE_ING));
                }
            }
        }else{
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.VALID_SPEC_REF_VALUE_VALUE_NOT_EMPTY));
        }
        List<User> usersNeedDelete = new ArrayList<>();
        userIdList.forEach(id -> {
            User user = userService.selectByPrimaryKey(id);
            request.getUsers().add(user);
            usersNeedDelete.add(user);
            // 33779 删除租户没有删除子账户
            if (user.getOrgSid() != null) {
                // 使用租户的组织ID查询出来下面的子账户
                List<User> users = userService.findOrgMgtUsers(user.getOrgSid());
                usersNeedDelete.addAll(users.stream()
                                            .filter(t -> !t.getAccount().equals(user.getAccount()))
                                            .collect(Collectors.toList()));
            }
        });

        boolean result = userService.deleteUsersData(usersNeedDelete.stream()
                                                                    .map(User::getUserSid)
                                                                    .sorted((userId1, userId2) -> Integer.parseInt(
                                                                            String.valueOf(userId2 - userId1)))
                                                                    .collect(Collectors.toList()));
        // 没有删除失败的，认为成功
        if (result) {
            for (Long userId : userIdList) {
                cfnUserAction(userId, "delete");
            }
            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.DATA_SYNC_HANDLE));
        }
        return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERROR_OPERATE_FAILURE));
    }

    /**
     * 清除Cookie
     */
    public static void clearCookie(HttpServletRequest servletRequest, HttpServletResponse response) {
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (authUser == null) {
            return;
        }
        RequestContextUtil.removeUserCache(authUser.getAccount());
        // delete token in cookie
        RequestContextUtil.clearPlatformTokenCookie(servletRequest, response);
    }

    /**
     * 找回密码
     *
     * @param request 请求
     *
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "POST", value = " 找回密码", notes = "通过email找回密码")
    @PostMapping("/reset_password/by_email")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "#request.email", resource = OperationResourceEnum.FIND_LOST_PWD)
    public RestResult userAtFindLostPwd(@RequestBody @Valid FindPassByEmailRequest request) {

        List<User> list = this.userService.findUserByEmail(request.getEmail());
        if (CollectionUtils.isEmpty(list)) {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1496023145));
        }
        //发送邮件
        if (UserStatus.FORBIDDEN.equals(list.get(0).getStatus())) {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_735250124));
        }
        if (UserStatus.NOTAPPROVE.equals(list.get(0).getStatus())) {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_923588186));
        }

        if (WebConstants.UserStatus.LOCKED.equals(list.get(0).getStatus())) {
            //当前用户若为锁定状态，且配置为可通过密码方式激活
            SysConfig openConfig = sysconfigService.findByConfigTypeAndKey("loginfailure_config",
                                                                           "loginfailure_config.open");
            SysConfig activeConfig = sysconfigService.findByConfigTypeAndKey("loginfailure_config",
                                                                             "loginfailure_config.active.way");
            if (Boolean.parseBoolean(openConfig.getConfigValue()) && !activeConfig.getConfigValue().contains(ZERO)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1759464620));
            }
        }
        User user = list.get(0);
        String realUrl = sysConfigService.getValueByConfigKey("rightcloud.mgt.url");
        if (!ObjectUtils.isEmpty(user.getUserType())
                && !ObjectUtils.isEmpty(user.getOrgSid())
                && UserType.PLATFORM_USER.equals(user.getUserType())) {
            realUrl = sysConfigService.getValueByConfigKey("rightcloud.console.url");
        }
        boolean result = userService.changePwdEmail(user, realUrl);
        if (result) {
            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_675452127));
        } else {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_675396708));
        }

    }

    /**
     * 忘记密码-找回密码
     *
     * @param request 请求
     *
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "POST", value = "忘记密码-找回密码")
    @PostMapping("/forget_password/update_password")
    @Encrypt
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'忘记密码-找回密码'", resource = OperationResourceEnum.FORGET_PASSWORD_RESET_PWD, tagNameUs ="'Forgot password'")
    public RestResult modifyPwdByForgetPassword(@RequestBody @Validated ForgetPwdRequest request) {
        if (!validateCode(request)) {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1020330924));
        }
        List<User> users = Lists.newArrayList();
        if (Objects.nonNull(request.getEmail())) {
            users = userService.findUserByEmail(request.getEmail());
        } else if (Objects.nonNull(request.getPhone())) {
            users = userService.findUserByMobile(request.getPhone());
        }
        if (!StrUtil.containsAny(request.getModuleType(), "management", "console")) {
            BizException.e(WebUtil.getMessage(MsgCd.ERR_MSG_15));
        }
        if("console".equals(request.getModuleType())) {
            List<User> collect = users.stream()
                                      .filter(user -> Objects.isNull(user.getOrgSid()))
                                      .collect(Collectors.toList());
            if(collect.size()>0) {
                return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_227632394));
            }
        }
        List<User> updateUsers = users.stream()
                            .filter(user -> !WebConstants.UserStatus.DELETED.equalsIgnoreCase(user.getStatus()))
                            .collect(Collectors.toList());
        if (updateUsers.isEmpty()) {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1597545170));
        }
        List<Long> userSids = new ArrayList<>();
        //校验密码规则, 并验证密码
        for (User user : updateUsers) {
            String error = userService.validPasswordByPolicyToError(request.getPassword(), user.getOrgSid(), user.getUserSid(), user.getParentSid(), false);
            if (!ObjectUtils.isEmpty(error)) {
                return new RestResult(Status.FAILURE, error);
            }
            userSids.add(user.getUserSid());
        }
        boolean result = userService.resetUserPassword(userSids, request.getPassword(),
                                                  Convert.toBool(request.getForceResetPwd(), false));
        if (!result) {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2049135760));
        }
        //资源ID
        OperationLogMdcUtil.saveBizId(userSids.stream().map(String::valueOf).collect(Collectors.toList()));
        return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2049191179));
    }

    /**
     * 通过邮箱修改密码
     *
     * @param request 请求
     *
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "POST", value = "通过邮箱修改密码")
    @PostMapping("/reset_password/update_password")
    @Encrypt
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "#request.email", resource = OperationResourceEnum.MODIFY_PWD_BY_EMAIL)
    public RestResult modifyPwdAtFindLostPwdV2(
            @RequestBody @Validated({ResetPasswordValidView.class}) ResetPwdRequest request) {
        // 非验证码方式
        if (StrUtil.isBlank(request.getCode())) {
            try {
                String securityKey = request.getSecurityKey();
                //解密错误应该返回url错误而不是系统异常。故try_catch处理
                String decryptStr;
                Long userSid;
                try {
                    decryptStr = CrytoUtilSimple.decrypt(securityKey, false);
                    userSid = Long.parseLong(decryptStr.split("-")[0]);
                    User user = this.userService.selectByPrimaryKey(userSid);
                    if (Objects.nonNull(user)) {
                        OperationLogMdcUtil.saveContent(user.getAccount());
                    }
                    OperationLogMdcUtil.saveBizId(userSid.toString());
                    //有效期
                    String securityCache = JedisUtil.INSTANCE.hget(CACHE_VALIDATE_CONTACTR_KEY, userSid.toString());
                    if (ObjectUtils.isEmpty(securityCache) || !securityCache.equals(securityKey)) {
                        BizException.e(WebUtil.getMessage(MsgCd.VALIDATE_CONTACTR_TIME_OUT));
                    }
                } catch (Exception e) {
                    return new RestResult(RestResult.Status.FAILURE, e.getMessage(), 03);
                }
                String newPassword = request.getNewPassword();

                User user = this.userService.selectByPrimaryKey(userSid);
                if (Objects.isNull(user)) {
                    return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_420974398), 02);
                } else {
                    String oldPassword = user.getPassword();
                    if (!oldPassword.equals(WebUtil.encrypt(newPassword, user.getAccount()))) {

                        String error = userService.validPasswordByPolicyToError(newPassword, user.getOrgSid(), user.getUserSid(), user.getParentSid(), false);
                        if (!ObjectUtils.isEmpty(error)) {
                            return new RestResult(Status.FAILURE, error);
                        }
                        boolean result = userService.resetUserPassword(Arrays.asList(user.getUserSid()), newPassword,
                                Convert.toBool(request.getForceResetPwd(), false));
                        if (!result) {
                            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2049135760));
                        }
                        JedisUtil.INSTANCE.hdel(CACHE_VALIDATE_CONTACTR_KEY, userSid.toString());
                        return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2049191179));
                    }
                }
            } catch (Exception e) {
                logger.error("通过邮箱修改密码失败", e);
                return new RestResult(RestResult.Status.FAILURE, e.getMessage(), 04);
            }
        }

        if (!validateCode(request)) {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1020330924));
        }
        List<User> users = Lists.newArrayList();
        if (Objects.nonNull(request.getEmail())) {
            users = userService.findUserByEmail(request.getEmail());
        } else if (Objects.nonNull(request.getPhone())) {
            users = userService.findUserByMobile(request.getPhone());
        }
        List<User> updateUsers = users.stream()
                .filter(user -> !WebConstants.UserStatus.DELETED.equalsIgnoreCase(user.getStatus()))
                .collect(Collectors.toList());
        if (updateUsers.isEmpty()) {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1597545170));
        }
        List<Long> userSids = new ArrayList<>();
        //校验密码规则, 并验证密码
        for (User user : updateUsers) {
            String error = userService.validPasswordByPolicyToError(request.getPassword(), user.getOrgSid(), user.getUserSid(), user.getParentSid(), false);
            if (!ObjectUtils.isEmpty(error)) {
                return new RestResult(Status.FAILURE, error);
            }
            userSids.add(user.getUserSid());
        }
        boolean result = userService.resetUserPassword(userSids, request.getPassword(),
                Convert.toBool(request.getForceResetPwd(), false));
        if (!result) {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2049135760));
        }
        OperationLogMdcUtil.saveBizId(userSids.stream().map(String::valueOf).collect(Collectors.toList()));
        return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2049191179));
    }

    /**
     * 首次登陆
     *
     * @param request 请求
     *
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "POST", value = "首次登陆")
    @PostMapping("/first_login")
    @Encrypt
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'首次登录重置密码'", param = "#request", resource = OperationResourceEnum.FIRST_LOGIN, tagNameUs ="'First login reset password'")
    public RestResult firstLogin(@RequestBody FirstLoginRequest request) {
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (authUser == null) {
            throw new BizException(HttpConst.Unauthorized.getType());
        }
        if (CollectionUtil.isEmpty(request.getUserIds())) {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1597545170));
        }
        Criteria criteria = new Criteria();
        criteria.setIgnoreDataFilter(true);
        criteria.put("userSidList", request.getUserIds());
        List<User> users = userService.selectUserBySample(criteria);
        if (users.isEmpty()) {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1597545170));
        }
        if(!authUser.getUserSid().equals(request.getUserIds().get(0))){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1678509082));
        }
        User user = users.get(0);
        String password = users.get(0).getPassword();
        String initPWD = request.getInitialPassword();
        String validStr = userService.validPasswordByPolicyToError(request.getPassword(), user.getOrgSid(),
                                                                   user.getUserSid(), user.getParentSid(), true);

        if (!ObjectUtils.isEmpty(validStr)) {
            return new RestResult(RestResult.Status.FAILURE, validStr);
        }
        if (!CrytoUtilSimple.validateHash(initPWD, password)) {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1002902223));
        }
        if (CollectionUtil.isNotEmpty(request.getUserIds())) {
            boolean result = userService.resetUserPassword(request.getUserIds(),
                                                           request.getPassword(),
                                                           Convert.toBool(request.getForceResetPwd(), false));
            if (result) {
                userMapper.updateIamForceResetPwd(users.get(0).getUserSid());
                return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2049191179));
            }
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2049135760));
        }
        RestResult restResult = this.modifyPwdAtFindLostPwdV2(request);
        if (restResult.getStatus()) {
            userMapper.updateIamForceResetPwd(users.get(0).getUserSid());
        }
        return restResult;

    }

    private boolean validateCode(ForgetPwdRequest request) {
        if (Objects.nonNull(request.getPhone())) {
            //验证短信验证码
            try {
                CheckSmsCodeUtil.checkCode(request.getPhone(), request.getCode());
                return true;
            } catch (Exception exception) {
                return false;
            }
        }
        if (Objects.nonNull(request.getEmail())) {
            String code = JedisUtil.INSTANCE.get(request.getEmail());
            return Objects.nonNull(request.getCode()) && Objects.equals(code, request.getCode());
        }
        return false;
    }

    private boolean validateCode(ResetPwdRequest request) {
        if (Objects.nonNull(request.getPhone())) {
            //验证短信验证码
            try {
                CheckSmsCodeUtil.checkCode(request.getPhone(), request.getCode());
                return true;
            } catch (Exception exception) {
                return false;
            }
        }
        if (Objects.nonNull(request.getEmail())) {
            String code = JedisUtil.INSTANCE.get(request.getEmail());
            return Objects.nonNull(request.getCode()) && Objects.equals(code, request.getCode());
        }
        return false;
    }

    /**
     * 发送验证邮箱
     *
     * @param emailRequest 邮箱请求
     * @return {@code Boolean}
     */
    @ApiOperation(httpMethod = "GET", value = "发送验证邮箱", notes = "需要提交email")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "email", value = "邮箱地址", dataType = "string", paramType = "query", required = true)})
    @PutMapping("/validation/email")
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "#email", resource = OperationResourceEnum.SEND_VALIDATE_EMAIL)
    public Boolean sendValidateEmail(@RequestBody @Valid EmailRequest emailRequest) {

        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (ObjectUtils.isEmpty(authUserInfo)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_531007114));
        }
        //验证码频繁校验
        String key = "sendValidateEmail" + authUserInfo.getUserSid();
        String amount = JedisUtil.INSTANCE.get(key);
        if (Objects.nonNull(amount)) {
            if (Integer.parseInt(amount) >= 10) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_403448001));
            }
        } else {
            amount = "0";
        }
        List<User> users = this.userService.findUserByEmail(emailRequest.getEmail());
        if (CollectionUtils.isNotEmpty(users)) {
            return Boolean.FALSE;
        }
        userService.sendValidateEmail(authUserInfo, emailRequest);

        JedisUtil.INSTANCE.set(key,String.valueOf( Integer.valueOf(amount)+1),60 * 3);
        return Boolean.TRUE;
    }

    /**
     * 获取当前用户所能管理的所有用户(客户管理-账户管理)
     *
     * @param request 请求
     *
     * @return {@link List}<{@link DescribeUserResponse}>
     */
    @AuthorizeOss(action = BQ03.BQ03)
    @ApiOperation(httpMethod = "GET", value = "获取当前用户所能管理的所有用户")
    @GetMapping("/by/account")
    public List<DescribeUserResponse> listByAccunt(@Valid DescribeUserRequest request) {
        List<Long>  adminSids = new ArrayList<>();
        request.setEntityId(RequestContextUtil.getEntityId());
        List<Role> currentRoleList =  roleService.selectRoleByUserSid(RequestContextUtil.getAuthUserInfo().getUserSid());
        List<cn.com.cloudstar.rightcloud.common.pojo.Role> convertsRolesList = BeanConvertUtil.convert(currentRoleList, cn.com.cloudstar.rightcloud.common.pojo.Role.class);
        String maxScope = DataScopeUtil.getMaxDataScope(convertsRolesList);
        if(DataScopeEnum.DATA_SCOPE_COMPANY.getScope().equals(maxScope)){
            Criteria bizBillingAccountCriteria = new Criteria();
            bizBillingAccountCriteria.put("entityId",request.getEntityId());
            bizBillingAccountCriteria.put("salesmenId",RequestContextUtil.getAuthUserInfo().getUserSid());
            List<BizBillingAccount> accounts = bizBillingAccountMapper.selectByParams(bizBillingAccountCriteria);
            if(CollectionUtil.isNotEmpty(accounts)){
                for(BizBillingAccount account : accounts){
                    adminSids.add(account.getAdminSid());
                }
            }
        }
        Criteria criteria = packageQueryUserInfo(request);
        WebUserUtil.preparePageParams(request, criteria, "created_dt desc");
        //查出来所有的用户
        criteria.setConditionObject(request);
        if (StringUtils.isNotBlank(request.getKeyword()) && !CCSPCacheUtil.ccspServiceOpen()) {
            criteria.put("keywordHash", DigestUtils.sha256Hex(request.getKeyword()));
        }
        if(CollectionUtil.isNotEmpty(adminSids)){
            criteria.put("userSidIn",adminSids);
        }
        String realName = request.getRealName();
        if (!Strings.isNullOrEmpty(realName)) {
            Criteria.newCriteriaHash(criteria,"realName",realName);
        }
        List<User> users = userService.findCertificationStatusUsers(criteria);
        Set<Long> companyIds = users.stream().map(User::getCompanyId).filter(Objects::nonNull).collect(Collectors.toSet());
        List<Company> companyList = null;
        if (CollectionUtils.isNotEmpty(companyIds)) {
            Criteria query = new Criteria();
            query.put("orgSidIn", companyIds);
            query.put("orgType", OrgType.COMPANY);
            companyList = companyService.selectByParams(query);
        }
        if (!ObjectUtils.isEmpty(request.getOrgSid())) {
            Org org = orgService.selectByPrimaryKey(request.getOrgSid());
            if (!ObjectUtils.isEmpty(org) && !ObjectUtils.isEmpty(org.getParentId())) {
                // 把默认项目中的角色赋值为父组织中的角色为准
                request.setOrgSid(org.getParentId());
            }
        }

        for (User user : users) {
            //查询用户的角色列表,并拼接成字符串
            List<Role> roleList;
            if (Objects.nonNull(request.getOrgSid())) {
                roleList = roleService.findRolesByUserSidAndOrgSid(user.getUserSid(), request.getOrgSid());
            } else {
                roleList = roleService.findRolesByUserSidForOrgSid(user.getUserSid());
            }
            String roleStr = "";
            Set<Long> roleIdSet = new HashSet<>();
            if (CollectionUtils.isNotEmpty(roleList)) {
                //利用set去重
                Set<String> roleNameSet = new HashSet<>();
                for (Role aRoleList : roleList) {
                    roleNameSet.add(aRoleList.getRoleName());
                    roleIdSet.add(aRoleList.getRoleSid());
                }
                roleStr = StringUtils.join(roleNameSet.toArray(), ",");

                // 判定用户是否有指定组织的角色
                if (request.getJoinFlag() != null && request.getJoinFlag() && Objects.nonNull(request.getOrgSid())) {
                    user.setJoinFlag(Boolean.TRUE);
                }
            }
            user.setRoleName(roleStr);
            if (CollectionUtil.isNotEmpty(roleIdSet)) {
                user.setRoleIds(Lists.newArrayList(roleIdSet));
            }
            Company company = null;
            if (CollectionUtils.isNotEmpty(companyList)) {
                user.setInnerFlag(false);
                company = companyList.stream()
                                     .filter(v -> v.getCompanyId().equals(user.getCompanyId()))
                                     .findAny().orElse(null);
                if (!ObjectUtils.isEmpty(company)) {
                    user.setInnerFlag(true);
                }
            }
            //默认设置直营
            if (StringUtil.isNullOrEmpty(user.getDistributorName())) {
                user.setDistributorName("直营");
            }
            user.setType(CertificationTypeEnum.USER.getCode());
            //判断是个人认证还是企业认证
            //"orgType", OrgType.COMPANY,"userSis",user.getUserSid()
            if (!ObjectUtils.isEmpty(company)
                    && !CertificationStatus.NOAUTH.equals(company.getCertificationStatus())
                    && !CertificationStatus.AUTHFILED.equals(company.getCertificationStatus())) {
                user.setType(CertificationTypeEnum.COMPANY.getCode());
                user.setCertificationStatus(company.getCertificationStatus());
                //如果企业认证成功,就显示为企业认证。
                user.setStatus(user.getStatus());
                user.setCompanyName(company.getCompanyName());
            }

            if (StringUtil.isNotBlank(user.getRealName())) {
                user.setRealName(DesensitizedUtil.chineseName(user.getRealName()));
            }
            if (StringUtil.isNotBlank(user.getMobile())) {
                user.setMobile(DesensitizedUtil.mobilePhone(user.getMobile()));
            }
            if (StringUtil.isNotBlank(user.getEmail())) {
                user.setEmail(DesensitizedUtil.email(user.getEmail()));
            }
        }
        List<DescribeUserResponse> convert = BeanConvertUtil.convert(users, DescribeUserResponse.class);
        return DesensitizationUtil.desensitization(convert);
    }

    /**
     * 获取当前用户所能管理的所有用户(组织管理)
     *
     * @param request 请求
     *
     * @return {@link List}<{@link DescribeUserResponse}>
     */
    @AuthorizeOss(action = ZB.ZB_COMMON)
    @ApiOperation(httpMethod = "GET", value = " 获取当前用户所能管理的所有用户")
    @GetMapping("/by/org")
    public List<DescribeUserResponse> listByOrg(@Valid DescribeUserRequest request) {
        request.setPagenum(null);
        request.setPagesize(null);
        Criteria criteria = packageQueryUserInfo(request);
        WebUserUtil.preparePageParams(request, criteria, "created_dt desc");
        //查出来所有的用户
        criteria.setConditionObject(request);
        if (StringUtils.isNotBlank(request.getKeyword()) && !CCSPCacheUtil.ccspServiceOpen()) {
            criteria.put("keywordHash", DigestUtils.sha256Hex(request.getKeyword()));
        }
        Criteria.newCriteriaHash(criteria);
        List<User> users = userService.findCertificationStatusUsers(criteria);
        List<Company> companyList = companyService.findOrgWithParentOrgName(new Criteria("orgType", OrgType.COMPANY));
        if (!ObjectUtils.isEmpty(request.getOrgSid())) {
            Org org = orgService.selectByPrimaryKey(request.getOrgSid());
            if (!ObjectUtils.isEmpty(org) && !ObjectUtils.isEmpty(org.getParentId())) {
                // 把默认项目中的角色赋值为父组织中的角色为准
                request.setOrgSid(org.getParentId());
            }
        }

        for (User user : users) {
            //查询用户的角色列表,并拼接成字符串
            List<Role> roleList;
            if (Objects.nonNull(request.getOrgSid())) {
                roleList = roleService.findRolesByUserSidAndOrgSid(user.getUserSid(), request.getOrgSid());
            } else {
                roleList = roleService.findRolesByUserSidForOrgSid(user.getUserSid());
            }
            String roleStr = "";
            Set<Long> roleIdSet = new HashSet<>();
            if (CollectionUtils.isNotEmpty(roleList)) {
                //利用set去重
                Set<String> roleNameSet = new HashSet<>();
                for (Role aRoleList : roleList) {
                    roleNameSet.add(aRoleList.getRoleName());
                    roleIdSet.add(aRoleList.getRoleSid());
                }
                roleStr = StringUtils.join(roleNameSet.toArray(), ",");

                // 判定用户是否有指定组织的角色
                if (request.getJoinFlag() != null && request.getJoinFlag() && Objects.nonNull(request.getOrgSid())) {
                    user.setJoinFlag(Boolean.TRUE);
                }
            }
            user.setRoleName(roleStr);
            if (CollectionUtil.isNotEmpty(roleIdSet)) {
                user.setRoleIds(Lists.newArrayList(roleIdSet));
            }
            Company company = null;
            if (CollectionUtils.isNotEmpty(companyList)) {
                user.setInnerFlag(false);
                company = companyList.stream()
                                     .filter(v -> v.getCompanyId().equals(user.getCompanyId()))
                                     .findAny().orElse(null);
                if (!ObjectUtils.isEmpty(company)) {
                    user.setInnerFlag(true);
                }
            }
            //默认设置直营
            if (StringUtil.isNullOrEmpty(user.getDistributorName())) {
                user.setDistributorName("直营");
            }
            user.setType(CertificationTypeEnum.USER.getCode());
            //判断是个人认证还是企业认证
            //"orgType", OrgType.COMPANY,"userSis",user.getUserSid()
            if (!ObjectUtils.isEmpty(company)
                    && !CertificationStatus.NOAUTH.equals(company.getCertificationStatus())
                    && !CertificationStatus.AUTHFILED.equals(company.getCertificationStatus())) {
                user.setType(CertificationTypeEnum.COMPANY.getCode());
                user.setCertificationStatus(company.getCertificationStatus());
                //如果企业认证成功,就显示为企业认证。
                user.setStatus(user.getStatus());
                user.setCompanyName(company.getCompanyName());
            }

            if (StringUtil.isNotBlank(user.getRealName())) {
                user.setRealName(DesensitizedUtil.chineseName(user.getRealName()));
            }
            if (StringUtil.isNotBlank(user.getMobile())) {
                user.setMobile(DesensitizedUtil.mobilePhone(user.getMobile()));
            }
            if (StringUtil.isNotBlank(user.getEmail())) {
                user.setEmail(DesensitizedUtil.email(user.getEmail()));
            }
        }
        List<DescribeUserResponse> convert = BeanConvertUtil.convert(users, DescribeUserResponse.class);
        return DesensitizationUtil.desensitization(convert);
    }

    /**
     * 获取当前用户所能管理的所有用户(用户管理)
     *
     * @param request 请求
     *
     * @return {@link List}<{@link DescribeUserResponse}>
     */
    @AuthorizeOss(action = ZC.ZC_COMMON)
    @ApiOperation(httpMethod = "GET", value = " 获取当前用户所能管理的所有用户")
    @GetMapping()
    public List<DescribeUserResponse> list(@Valid DescribeUserRequest request) {
//        request.setPagenum(null);
//        request.setPagesize(null);
        Criteria criteria = packageQueryUserInfo(request);
        WebUserUtil.preparePageParams(request, criteria, "created_dt desc");
        //查出来所有的用户
        criteria.setConditionObject(request);
        Criteria.newCriteriaHash(criteria);
        if (StringUtils.isNotBlank(request.getKeyword()) && !CCSPCacheUtil.ccspServiceOpen()) {
            criteria.put("keywordHash", DigestUtils.sha256Hex(request.getKeyword()));
        }
        List<User> users = userService.findCertificationStatusUsers(criteria);
        List<Company> companyList = companyService.findOrgWithParentOrgName(new Criteria("orgType", OrgType.COMPANY));
        if (!ObjectUtils.isEmpty(request.getOrgSid())) {
            Org org = orgService.selectByPrimaryKey(request.getOrgSid());
            if (!ObjectUtils.isEmpty(org) && !ObjectUtils.isEmpty(org.getParentId())) {
                // 把默认项目中的角色赋值为父组织中的角色为准
                request.setOrgSid(org.getParentId());
            }
        }

        for (User user : users) {
            //查询用户的角色列表,并拼接成字符串
            List<Role> roleList;
            if (Objects.nonNull(request.getOrgSid())) {
                roleList = roleService.findRolesByUserSidAndOrgSid(user.getUserSid(), request.getOrgSid());
            } else {
                roleList = roleService.findRolesByUserSidForOrgSid(user.getUserSid());
            }
            String roleStr = "";
            Set<Long> roleIdSet = new HashSet<>();
            if (CollectionUtils.isNotEmpty(roleList)) {
                //利用set去重
                Set<String> roleNameSet = new HashSet<>();
                for (Role aRoleList : roleList) {
                    roleNameSet.add(aRoleList.getRoleName());
                    roleIdSet.add(aRoleList.getRoleSid());
                }
                roleStr = StringUtils.join(roleNameSet.toArray(), ",");

                // 判定用户是否有指定组织的角色
                if (request.getJoinFlag() != null && request.getJoinFlag() && Objects.nonNull(request.getOrgSid())) {
                    user.setJoinFlag(Boolean.TRUE);
                }
            }
            user.setRoleName(roleStr);
            if (CollectionUtil.isNotEmpty(roleIdSet)) {
                user.setRoleIds(Lists.newArrayList(roleIdSet));
            }
            Company company = null;
            if (CollectionUtils.isNotEmpty(companyList)) {
                user.setInnerFlag(false);
                company = companyList.stream()
                                     .filter(v -> v.getCompanyId().equals(user.getCompanyId()))
                                     .findAny().orElse(null);
                if (!ObjectUtils.isEmpty(company)) {
                    user.setInnerFlag(true);
                }
            }
            //默认设置直营
            if (StringUtil.isNullOrEmpty(user.getDistributorName())) {
                user.setDistributorName("直营");
            }
            user.setType(CertificationTypeEnum.USER.getCode());
            //判断是个人认证还是企业认证
            //"orgType", OrgType.COMPANY,"userSis",user.getUserSid()
            List<Company> companys = companyService.findCompanyByUserSid(OrgType.COMPANY, user.getUserSid());
            if (companys.size() > 0) {
                if (!CertificationStatus.NOAUTH.equals(companys.get(0).getCertificationStatus()) &&
                        !CertificationStatus.AUTHFILED.equals(companys.get(0).getCertificationStatus())) {
                    user.setType(CertificationTypeEnum.COMPANY.getCode());
                    user.setCertificationStatus(companys.get(0).getCertificationStatus());
                    //如果企业认证成功,就显示为企业认证。
                    user.setStatus(user.getStatus());
                }
            }

            //根据用户id和用户名称模糊查询用户关联得运营实体
            Set<String> sb = new HashSet<>();
            Criteria param = new Criteria();
            param.put("userSid", user.getUserSid());
            param.put("orgSid", user.getOrgSid() == null ? -1L : user.getOrgSid());
            List<SysBssEntity> bssEntityList = sysBssEntityService.selectBssEntityByParams(param);
            if (CollectionUtil.isNotEmpty(bssEntityList)) {
                bssEntityList.forEach(b -> {
                    sb.add(b.getEntityName());
                });
            }
            if (CollectionUtil.isNotEmpty(sb)) {
                user.setEntityName(StringUtils.join(sb.toArray(), ","));
            }

            if (StringUtil.isNotBlank(user.getRealName())) {
                user.setRealName(DesensitizedUtil.chineseName(user.getRealName()));
            }
            if (StringUtil.isNotBlank(user.getMobile())) {
                user.setMobile(DesensitizedUtil.mobilePhone(user.getMobile()));
            }
            if (StringUtil.isNotBlank(user.getEmail())) {
                user.setEmail(DesensitizedUtil.email(user.getEmail()));
            }
        }
        // 运营实体条件查询
        if (Objects.nonNull(request.getEntityName())) {
            users = users.stream()
                    .filter(u -> !Objects.isNull(u.getEntityName()))
                    .filter(u -> u.getEntityName().contains(request.getEntityName()))
                    .collect(Collectors.toList());
        }
        List<DescribeUserResponse> convert = BeanConvertUtil.convert(users, DescribeUserResponse.class);
        return DesensitizationUtil.desensitization(convert);
    }

    /**
     * 获取当前用户所能管理的用户
     *
     * @param request 请求
     *
     * @return {@link List}<{@link DescribeManagerUserSimpleResponse}>
     */
    @AuthorizeOss(action = ZF02.ZF02)
    @ApiOperation(httpMethod = "GET", value = " 获取当前用户所能管理的所有用户简单信息")
    @GetMapping("/simple")
    public List<DescribeManagerUserSimpleResponse> simplelist(@Valid DescribeUserRequest request) {
        Criteria criteria = packageQueryUserInfo(request);
        WebUserUtil.preparePageParams(request, criteria, "created_dt desc");
        criteria.setConditionObject(request);
        List<User> users = userService.findAllUsers(criteria);
        return BeanConvertUtil.convert(users, DescribeManagerUserSimpleResponse.class);
    }

    /**
     * 需要提交密匙串
     *
     * @param request 请求
     *
     * @return {@link Boolean}
     */
    @ApiOperation(httpMethod = "POST", value = " 邮箱验证", notes = "需要提交密匙串")
    @PostMapping("/email")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'邮箱验证'", resource = OperationResourceEnum.EMAIL_VALIDATE_SUCCESSFUL, tagNameUs ="'Email verification'")
    public Boolean emailValidateSuccessful(@RequestBody @Valid EmailValidateRequest request) {
        return userService.emailValidateSuccessful(request.getSecurityKey());
    }

    /**
     * 获取用户信息(客户管理)
     *
     * @param userId 用户id
     * @return {@link DescribeUserDetailResponse}
     */
    @AuthorizeOss(action = AuthModule.BQ.BQ03.BQ0301)
    @ApiOperation(httpMethod = "GET", value = "获取用户信息", notes = "通过用户id获取用户信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户id", dataType = "long", paramType = "path", required = true)})
    @GetMapping("/customer/{userId}")
    @DataPermission(resource = OperationResourceEnum.GET_BILLING_ACCOUNT_DETAIL, bizId = "#userId")
    public DescribeUserDetailResponse getCustomerById(@PathVariable Long userId) {
        // 获取用户
        User user = userService.findUserById(userId);
        //用户为空，或用户类型是分销商，或用户组织id为空则抛错
        if (Objects.isNull(user)
                || !"03".equals(user.getUserType())
                || ObjectUtils.isEmpty(user.getOrgSid())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        user.setAdminFlag(roleService.isAdmin(userId));
        if (!ObjectUtils.isEmpty(user.getEmail())) {
            user.setEmail(DesensitizedUtil.email(user.getEmail()));
        }
        if (!ObjectUtils.isEmpty(user.getMobile())) {
            user.setMobile(DesensitizedUtil.mobilePhone(user.getMobile()));
        }
        if (!ObjectUtils.isEmpty(user.getRealName())) {
            user.setRealName(DesensitizedUtil.chineseName(user.getRealName()));
        }
        return BeanConvertUtil.convert(user, DescribeUserDetailResponse.class);
    }

    /**
     * 获取用户信息
     *
     * @param userId 用户id
     *
     * @return {@link DescribeUserDetailResponse}
     */
    @AuthorizeOss(action = ZC.ZC02)
    @ApiOperation(httpMethod = "GET", value = "获取用户信息", notes = "通过用户id获取用户信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户id", dataType = "long", paramType = "path", required = true)})
    @GetMapping("/{userId}")
    public DescribeUserDetailResponse getUserById(@PathVariable Long userId) {
        // 获取用户
        User user = userService.findUserById(userId);
        if (Objects.isNull(user)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        user.setAdminFlag(roleService.isAdmin(userId));
        if (!ObjectUtils.isEmpty(user.getEmail())) {
            user.setEmail(DesensitizedUtil.email(user.getEmail()));
        }
        if (!ObjectUtils.isEmpty(user.getMobile())) {
            user.setMobile(DesensitizedUtil.mobilePhone(user.getMobile()));
        }
        if (!ObjectUtils.isEmpty(user.getRealName())) {
            user.setRealName(DesensitizedUtil.chineseName(user.getRealName()));
        }
        List<String> entityIds = entityUserMapper.queryEntityIds(userId)
                                                 .stream()
                                                 .map(String::valueOf)
                                                 .collect(Collectors.toList());
        DescribeUserDetailResponse result = BeanConvertUtil.convert(user, DescribeUserDetailResponse.class);
        result.setEntityIds(entityIds);
        return result;
    }

    /**
     * 校验新手机号的验证码, 并修改手机号
     *
     * @param request 请求
     *
     * @return {@link RestResult}
     */
    @DataPermission(resource = OperationResourceEnum.UPDATE_USER_PHONE, bizId = "#request.id")
    @ApiOperation(httpMethod = "PUT", value = "并修改手机号", notes = "校验新手机号的验证码, 并修改手机号")
    @PutMapping("/phone")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'修改手机号'", resource = OperationResourceEnum.UPDATE_USER_PHONE, tagNameUs ="'Modify mobile phone number'")
    public RestResult compareNewVerificationCode(@Valid @LogParam("request") CheckAndUpdatePhoneRequest request) {
        String code = request.getVerificationCode().replace("updateMobile", "");
        if (code.length() != 6 || !StringUtils.isNumeric(code)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1303147031));
        }
        CheckSmsCodeUtil.checkCode(request.getNewPhoneNum(), request.getVerificationCode());
        // 校验前端userSid传值与token中的用户信息是否一致
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (ObjectUtils.isEmpty(authUserInfo)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_531007114));
        }
        if (!authUserInfo.getUserSid().equals(request.getId())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1450209074));
        }
        try {
            User user = userService.selectByPrimaryKey(request.getId());
            User updateUser = new User();
            updateUser.setUserSid(user.getUserSid());
            if (!ObjectUtils.isEmpty(request.getNewPhoneNum())) {
                updateUser.setMobile(request.getNewPhoneNum());
            }else{
                updateUser.setMobile(user.getMobile());
            }
            updateUser.setAccount(user.getAccount());
            userService.updateByPrimaryKeySelective(updateUser);
            updateUser.setPassword(null);
            iamRemoteService.updateUser(BeanConvertUtil.convert(updateUser, IamUser.class));
            //同步ad 手机号码
            if (USER_AUTH_TYPE.AUTH_TYPE_AD.equals(user.getAuthType())) {
                LdapUserDTO ldapUserVo = new LdapUserDTO();
                ldapUserVo.setMobile(request.getNewPhoneNum());
            }
            if (request.getUpdateReceiveFlag()) {
                sysMMsgReceiveContact.updateReceiveByUserId(request.getId(), "mobile");
            }
            //完成修改后清除redis的缓存.
            RequestContextUtil.removeUserCache(user.getAccount());
            // 判断IP地址限制
            String ip = IPAddressUtil.getRemoteHostIp(WebUtil.getRequest());
            JedisUtil.INSTANCE.set(ip + "TIME_LIMIT", (Calendar.getInstance().getTime().getTime() / 1000) - 60 + "");
            //记录日志脱敏手机号
            if (StringUtil.isNotBlank(request.getNewPhoneNum())) {
                request.setNewPhoneNum((String) DesensitizationUtil.doMobilephone("class java.lang.String", request.getNewPhoneNum()));
            }
            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1780929689));
        } catch (BizException | cn.com.cloudstar.rightcloud.common.exception.BizException e) {
            e.printStackTrace();
            throw new BizException(e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_609387497));
        } finally {
            //记录日志脱敏手机号
            if (StringUtil.isNotBlank(request.getNewPhoneNum())) {
                request.setNewPhoneNum((String) DesensitizationUtil.doMobilephone("class java.lang.String", request.getNewPhoneNum()));
            }
        }
    }


    /**
     * 验证用户密码
     *
     * @param request 请求
     *
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "GET", value = "验证用户密码")
    @PostMapping("/password/check")
    @Encrypt
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "#request.account", resource = OperationResourceEnum.CHECK_PASSWORD)
    public RestResult checkUserPassword(@RequestBody @Valid CheckPasswordRequest request) {
        //验证码频繁校验
        String amount= JedisUtil.INSTANCE.get("checkUserPassword");
        if(Objects.nonNull(amount)){
            if(Integer.valueOf(amount)>=10){
                return new RestResult(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1513418090));
            }
        }
        logger.info("进入验证用户密码接口");
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        String sessionId = Optional.ofNullable(WebUtil.getRequest()).map(v -> v.getHeader("sessionid")).orElse(null);
        logger.info("当前登录用户信息:[{}]", authUser.getAccount());
        if (ObjectUtils.isEmpty(authUser) || ObjectUtils.isEmpty(authUser.getUserSid()) || ObjectUtils.isEmpty(sessionId)) {
            return new RestResult(Status.SUCCESS, "");
        }
        if (!authUser.getAccount().equals(request.getAccount())){
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        User info = userService.selectByPrimaryKey(authUser.getUserSid());
        logger.info("通过id查询获得的用户信息:[{}]", info.getAccount());
        if (!CrytoUtilSimple.validateHash(request.getPassword(), info.getPassword())) {
            int errorCount=1;
            if(Objects.isNull(amount)){
                JedisUtil.INSTANCE.set("checkUserPassword",String.valueOf(errorCount),60 * 3);
            }else{
                JedisUtil.INSTANCE.set("checkUserPassword",String.valueOf( Integer.valueOf(amount)+1),60 * 3);
            }
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_589426131));
        }
        // 30分钟内不需要重新验证
        String key = TWICE_VALIDATE + DigestUtil.sha256Hex(sessionId);
        DateTime dateTime = new DateTime();
        String strTime = String.valueOf(dateTime);
        redisTemplate.opsForValue().set(key, strTime, 30, TimeUnit.MINUTES);
        return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_589481550));
    }

    /**
     * 修改用户密码
     *
     * @param request 请求
     *
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "PUT", value = "修改用户密码")
    @PutMapping("/password")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'密码'", resource = OperationResourceEnum.UPDATE_USER_PASSWORD, tagNameUs ="'Password'")
    @Transactional(rollbackFor = Exception.class)
    public RestResult updateUserPassword(@Valid @RequestBody UpdateUserPwdRequest request) {
        //验证码频繁校验
        Integer amount = Convert.toInt(JedisUtil.INSTANCE.get(UPDATE_USER_CIPHER_LOCK), 0);
        if (amount >= 10) {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1513418090));
        }
        return updatePassword(request);
    }


    public RestResult updatePassword(UpdateUserPwdRequest request) {
        //验证码频繁校验
        Integer amount = Convert.toInt(JedisUtil.INSTANCE.get(UPDATE_USER_CIPHER_LOCK), 0);
        if (amount >= 10) {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1513418090));
        }
        // 获取当前登录用户
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (ObjectUtils.isEmpty(authUser)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_531007114));
        }
        if (Constants.BSS.equals(authUser.getRemark()) && ObjectUtils.isEmpty(request.getUserSid())
                && !authUser.getUserSid().equals(request.getUserSid())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1360443513));
        }
        // 获取请求参数
        String newPwd = request.getNewpassword();
        String oldPwd = request.getOldpassword();
        // 从数据库拿到当前用户
        User oldUser = this.userService.selectByPrimaryKey(authUser.getUserSid());
        // 通过hash值判断旧密码是否一致
        if (!CrytoUtilSimple.validateHash(oldPwd, oldUser.getPassword())) {
            JedisUtil.INSTANCE.set(UPDATE_USER_CIPHER_LOCK, String.valueOf(amount + 1), 60 * 3);
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_122169855));
        }
        //变更HPC通行证
        if (CollectionUtils.isNotEmpty(request.getChangeTypes()) && request.getChangeTypes().contains(CHANGE_HPC)) {
            updateHpcPass(request);
        }
        if (CollectionUtils.isNotEmpty(request.getChangeTypes()) && !request.getChangeTypes().contains(CHANGE_USER)) {
            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1653564579), authUser.getUserSid());
        }

        User user = User.builder().userSid(authUser.getUserSid()).build();
        // 验证密码复杂度
        String validStr = userService.validModifyPasswordByPolicyToError(newPwd,
                                                                         authUser.getOrgSid(),
                    authUser.getUserSid(),
                    authUser.getParentSid());
        if (!ObjectUtils.isEmpty(validStr)) {
            return new RestResult(RestResult.Status.FAILURE, validStr);
        }
        String hashPassword = CrytoUtilSimple.encodeHash(newPwd);
        user.setPassword(hashPassword);

        // 修改云运营平台密码
        WebUserUtil.prepareUpdateParams(user);
        //设置密码有效期
        userService.setUserPasswordEndTime(user, userService.buildPasswordPolicy(authUser.getOrgSid(),
                                                                                 authUser.getParentSid()));
        // 修改IAM(CMP)密码
        oldUser.setPassword(newPwd);
        WebUserUtil.prepareUpdateParams(oldUser);
        this.iamRemoteService.updateUser(BeanConvertUtil.convert(oldUser, IamUser.class));
        // keycloak修改密码成功后，user表才记录新密码
        if(CCSPCacheUtil.ccspServiceOpen()) {
            // 开启国密需要更新创建时间
            user.setCreatedDt(new Date());
            cn.hutool.json.JSONObject object = JSONUtil.parseObj(hashPassword);
            String ccspPass = CCSPCacheUtil.verifyAndCCSPEncrypt(newPwd);
            object.set("secretData", ccspPass);
            user.setPassword(JSONUtil.toJsonStr(object));
        }

        userService.updatePasswordByPrimaryKey(user);
        // 记录历史密码
        // 注：得记录老密码，新密码不需要记录
        userService.insertSelectivePasswordHistory(user.getUserSid(), user.getPassword());
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1653564579), authUser.getUserSid());
    }

    public void updateHpcPass(UpdateUserPwdRequest request) {
        // 获取当前登录用户
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        // 获取请求参数
        String newPwd = request.getNewpassword();
        // 从数据库拿到当前用户
        User oldUser = this.userService.selectByPrimaryKey(authUser.getUserSid());
        SysHpcPass hpcPass = hpcPassMapper.findHpcPass(authUser.getUserSid());
        if (Objects.isNull(hpcPass)) {
            throw new BizException("HPC通行证修改失败，未配置！");
        }
        hpcPass.setPassword(CrytoUtilSimple.encrypt(newPwd));
        hpcPassMapper.updateHpcPass(hpcPass);
        // 如果变更对象为租户
        Long orgSid = authUser.getOrgSid();
        if (!StringUtil.isNullOrEmpty(orgSid) && orgSid > 0) {
            logger.info("UserCtrl.updatePassword userSid: {}, orgSid: {}", authUser.getUserSid(), authUser.getOrgSid());
            // 更新Ldap用户密码
            User userInfo = userService.selectByPrimaryKey(authUser.getUserSid());
            String accountName = userInfo.getAccount();
            try {
                OpenLdapUser openLdapUser = new OpenLdapUser();
                Org org = orgService.selectRootOrg(oldUser.getOrgSid());
                openLdapUser.setUserName(accountName)
                            .setPassword(SHA512Util.LdapEncoderBySHA512(request.getNewpassword()))
                            .setLdapOu(org.getLdapOu())
                            .setUid(String.valueOf(userInfo.getUserSid()));

                ldapUserService.changeLdapUserPassword(openLdapUser);
            } catch (NamingException e) {
                logger.error("更新 ldap 用户失败[{}]", accountName);
                throw new BizException("HPC通行证修改失败!");
            }
        }
    }

    /**
     * 导出表格
     *
     * @param request 请求
     */
    @AuthorizeOss(action = USER.EXPORT_USER)
    @ApiOperation(httpMethod = "GET", value = "导出表格")
    @GetMapping("/export")
    @SmsValidation
    @OperationLog(type = OperationTypeEnum.EXPORT, tagName = "'用户列表'", resource = OperationResourceEnum.EXPORT_USER_LIST, tagNameUs ="'User List'")
    @Idempotent
    public RestResult exportToExcel(DescribeUserRequest request) {
        return userService.asynchronousExportUsers(request);
    }


    /**
     * 【SINCE2.6.0】异步导出用户列表
     *
     * @param num num 任务编号
     * @param response 响应
     */
    @AuthorizeOss(action = USER.EXPORT_USER+","+USER.EXPORT_ACCOUNT_INFO)
    @GetMapping("/download/file")
    @ApiOperation("异步导出用户列表")
    @ApiResponses({
            @ApiResponse(code = 200, message = ".biz文件")
    })
    @OperationLog(type = OperationTypeEnum.EXPORT, tagName = "'用户列表'", bizId = "#num",resource = OperationResourceEnum.DOWNLOAD_USER_LIST, tagNameUs ="'User List'")
    public void actionLogFile(@RequestParam("num") String num, HttpServletResponse response) {
        try {
            BizDownload download = bizDownloadMapper.selectByBownloadNum(num);
            if (ObjectUtils.isEmpty(download)
                    || ObjectUtils.isEmpty(download.getFileName())
                    || (!"sys_users".equals(download.getOperationType()) && !"user_account".equals(download.getOperationType()))

            ) {
                BizException.e(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
            }
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode(download.getFileName(), "UTF-8"));
            transformation(feignService.downloadFile(num), response);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }

    public void transformation(Response fileInputStream, HttpServletResponse response) {
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Access-Control-Expose-Headers","Content-Disposition");
        response.setContentType("application/octet-stream");
        InputStream in = null;
        try {
            in = fileInputStream.body().asInputStream();
            ByteArrayOutputStream out = ZipUtil.toOutputStream(in);
            response.getOutputStream().write(out.toByteArray());
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            IOUtils.closeQuietly(in);
        }
    }

    /**
     * 解除用户锁定状态
     *
     * @param request 入参
     *
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "PUT", value = "解除用户锁定状态", notes = "设置用户的状态从锁定到启用")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userIds", value = "用户ID数组", paramType = "body", dataType = "string", required = true)})
    @PutMapping("/unlock")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'用户名称'", bizId = "#request.userIds", param = "#params", resource = OperationResourceEnum.UNLOCK_USER_STATUS, tagNameUs ="'User Name'")
    @AuthorizeOss(action = ZC.ZC02 + "," + BQ03.BQ0301)
    public RestResult setUserUnlockStatus(@ApiIgnore @RequestBody UserUnLockRequest request) {
        List<Long> userSids = request.getUserIds().stream().map(Long::valueOf).collect(Collectors.toList());
        boolean result = userService.unlockUsers(userSids, null);
        if (result) {
            return new RestResult(RestResult.Status.SUCCESS,
                                  WebUtil.getMessage(WebConstants.MsgCd.INFO_OPERATE_SUCCESS));
        }
        return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(WebConstants.MsgCd.ERROR_UPDATE_FAILURE));
    }

    private Map<String, List<TreeNode>> getUserMenus(Boolean isAdmin, String moduleType,Long entityId) {
        List<SysConfig> sysConfigs = sysConfigMapper.selectByParams(new Criteria("configKey", "listen.expire.login"));
        //许可证过期允许登录
        boolean listenLoginFalg = CollUtil.isNotEmpty(sysConfigs) && "true".equals(sysConfigs.get(0).getConfigValue());
        Map<String, List<TreeNode>> menuMap = new HashMap<>();

        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        Criteria criteria = new Criteria();
        List<Module> modules;

        Map<String, String> map = productInfoList();

        //如果licence 过期即为true 时 则跳过这段代码
        AssertUtil.requireNonBlank(authUserInfo, "获取当前登录用户失败");
        if (!authUserInfo.isLicenceExpire() || listenLoginFalg) {
            criteria.put("userSid", authUserInfo.getUserSid());
            if (Objects.nonNull(authUserInfo.getOrgSid()) && authUserInfo.getOrgSid() > 0L) {
                Org org = orgService.selectByPrimaryKey(authUserInfo.getOrgSid());
                Optional.ofNullable(org).ifPresent(x -> criteria.put("orgType", org.getOrgType()));
            }

            // 子用户过滤访问控制菜单
            boolean b = userMapper.assertIam(authUserInfo.getUserSid());
            if (b) {
                //获取父用户的权限，子用户的权限不能大于父用户权限
                criteria.clear();
                criteria.put("userSid", authUserInfo.getParentSid());
                if (Objects.nonNull(authUserInfo.getOrgSid())) {
                    Org org = orgService.selectByPrimaryKey(authUserInfo.getOrgSid());
                    if (Objects.nonNull(org)) {
                        criteria.put("orgType", org.getOrgType());
                    }
                }
                List<String> parentNewPermission= moduleService.selectUserPermissions(criteria);
                List<String> parentPermission = new ArrayList<>();
                //租户端检验aiMarket相关菜单是否需要显示
                User user = userMapper.selectUserByUserId(authUserInfo.getUserSid());
                if (!AUTHORIZATION.equals(user.getAuthorizeTag())) {
                    parentNewPermission = parentNewPermission.stream()
                            .filter(moduleSid -> {
                                if (moduleSid.startsWith("CAI0") && !moduleSid.startsWith("CAI04")) {
                                    return false;
                                }
                                return true;
                            })
                            .collect(Collectors.toList());
                }
                List<String> permissions = getIamPermissions(authUserInfo.getUserSid());
                /*List<String> finalParentPermission = parentPermission;
                permissions = permissions.stream()
                                         .filter(permission -> finalParentPermission.contains(permission))
                                         .collect(Collectors.toList());*/
                if (CollectionUtil.isEmpty(permissions)) {
                    return null;
                }
                criteria.clear();
                criteria.put("moduleSids", permissions.stream().distinct().collect(Collectors.toList()));
                criteria.setOrderByClause("sort_rank");
                modules = moduleService.selectByParams(criteria);
                //只保留0目录，1菜单
                modules = modules.stream()
                                 .filter(module -> !CollectionUtil.contains(FILTER_PERMISSION, module.getModuleSid()))
                                 .filter(module -> !ObjectUtils.isEmpty(module.getModuleType())
                                        /*&& (module.getModuleType().equals(0) || module.getModuleType().equals(1))*/)
                                 .collect(Collectors.toList());
            } else {
                modules = moduleService.selectUserMenus(criteria);
            }
            if (CollectionUtils.isNotEmpty(modules)) {
                //租户端检验aiMarket相关菜单是否需要显示
                User user = userMapper.selectUserByUserId(authUserInfo.getUserSid());
                if (!AUTHORIZATION.equals(user.getAuthorizeTag())) {
                    modules = modules.stream()
                            .filter(m -> {
                                String moduleSid = m.getModuleSid();
                                if (moduleSid.startsWith("CAI0") && !moduleSid.startsWith("CAI04")) {
                                    return false;
                                }
                                return true;
                            })
                            .collect(Collectors.toList());
                }
                // 过滤云资源下面菜单
                modules = modules.stream().filter(m -> !"BB".equals(m.getParentSid())).filter(m -> {
                    if (cn.com.cloudstar.rightcloud.oss.common.constants.Constants.BSS.equals(moduleType)) {
                        return cn.com.cloudstar.rightcloud.oss.common.constants.Constants.BSS.equals(m.getModuleCategory()) || cn.com.cloudstar.rightcloud.oss.common.constants.Constants.BIG.equals(
                                m.getModuleCategory());
                    }

                    if (cn.com.cloudstar.rightcloud.oss.common.constants.Constants.CONSOLE.equals(moduleType)) {
                        return cn.com.cloudstar.rightcloud.oss.common.constants.Constants.MGT.equals(m.getModuleCategory());
                    }
                    return false;
                }).collect(Collectors.toList());

                // 子用户剔除套餐包权限
                if (cn.com.cloudstar.rightcloud.oss.common.constants.Constants.CONSOLE.equals(moduleType) && authUserInfo.getParentSid() != null) {
                    modules = modules.stream().filter(e -> !e.getModuleName().contains("套餐包")).collect(Collectors.toList());
                }

                Map<String, List<Module>> moduleMap = modules.stream()
                                                             .collect(Collectors.groupingBy(Module::getModuleCategory));

                List<Module> bssModule = moduleMap.get("bss");
                if (CollectionUtil.isNotEmpty(bssModule)) {
                    bssModule = bssModule.stream().filter(e -> e.getRoleSid() - 100 != 0).collect(Collectors.toList());
                    moduleMap.put("bss", bssModule);
                }

                for (Map.Entry<String, List<Module>> entry : moduleMap.entrySet()) {
                    List<Module> moduleList = entry.getValue();
                    if (ModuleTypeConstants.FROM_BSS.equalsIgnoreCase(entry.getKey())) {
                        moduleList = filterMenus(entry.getValue(), map);
                        moduleList = this.distinctMenus(moduleList);
                    }
                    List<TreeNode> nodes = TreeBuilder.buildTreeNode(moduleList, "moduleSid", "moduleName",
                                                                     "parentSid", "moduleIconUrl", "moduleUrl");
                    menuMap.put(entry.getKey(), filterEmptyChildrenNode(nodes));
                }
            }
        }

        if (isAdmin) {
            criteria.clear();
            criteria.put("userSid", authUserInfo.getUserSid());
            criteria.put("orgSid", 0L);

            modules = moduleService.selectUserMenus(criteria);
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(modules)) {
                modules = modules.stream().filter(m -> !"BB".equals(m.getParentSid())).collect(Collectors.toList());
                Map<String, List<Module>> moduleMap = modules.stream()
                                                             .collect(Collectors.groupingBy(Module::getModuleCategory));

                moduleMap.remove("bss");

                for (Map.Entry<String, List<Module>> entry : moduleMap.entrySet()) {
                    List<Module> moduleList = entry.getValue();
                    if (ModuleTypeConstants.FROM_BSS.equalsIgnoreCase(entry.getKey())) {
                        moduleList = filterMenus(entry.getValue(), map);
                    }
                    List<TreeNode> nodes = TreeBuilder.buildTreeNode(moduleList, "moduleSid", "moduleName",
                                                                     "parentSid", "moduleIconUrl", "moduleUrl");
                    menuMap.put(entry.getKey(), filterEmptyChildrenNode(nodes));
                }
            }
        }

        List<UserRole> userRoleList = userRoleMapper.selectRoleByUserSid(
                new Criteria("userSid", authUserInfo.getUserSid()));
        if (userRoleList.size() == 1) {
            UserRole userRole = userRoleList.get(0);
            if (userRole.getOrgSid() == 0 && userRole.getRoleSid() == 100) {
                Map<String, List<TreeNode>> menuMapAdmin = new HashMap<>(8);
                menuMapAdmin.put("sys", menuMap.get("sys"));
                return menuMapAdmin;
            }
        }

        return menuMap;
    }

    private List<Module> distinctMenus(List<Module> moduleList) {
        moduleList.forEach(module -> module.setRoleSid(null));
        return moduleList.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 对菜单根据产品来过滤
     *
     * @param modules modules
     * @param map     map
     * @return {@link List}<{@link Module}>
     */
    private List<Module> filterMenus(List<Module> modules, Map<String, String> map) {
        List<Module> list = new ArrayList<>();
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(modules)) {
            for (Module module : modules) {
                if ("计算集群".equalsIgnoreCase(module.getModuleName())) {
                    if (map.containsKey(ProductCodeEnum.HPC.getProductCode()) || map.containsKey(
                            ProductCodeEnum.HPC_DRP.getProductCode())
                            || map.containsKey(ProductCodeEnum.HPC_SAAS.getProductCode())) {
                        list.add(module);
                    }
                } else if ("云主机规格".equalsIgnoreCase(module.getModuleName())) {
                    if (map.containsKey(ProductCodeEnum.HPC_DRP.getProductCode())) {
                        list.add(module);
                    }
                } else if ("裸金属规格".equalsIgnoreCase(module.getModuleName())) {
                    if (map.containsKey(ProductCodeEnum.HPC_DRP.getProductCode()) || map.containsKey(
                            ProductCodeEnum.MODEL_ARTS.getProductCode())) {
                        list.add(module);
                    }
                } else if ("弹性文件".equalsIgnoreCase(module.getModuleName())) {
                    if (map.containsKey(ProductCodeEnum.SFS2.getProductCode()) || map.containsKey(
                            ProductCodeEnum.HPC_DRP.getProductCode())
                            || map.containsKey(ProductCodeEnum.HPC_SAAS.getProductCode())) {
                        list.add(module);

                    }
                } else if ("MA专属池规格".equalsIgnoreCase(module.getModuleName())) {
                    if (map.containsKey(ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode())) {
                        list.add(module);
                    }
                } else if ("存储集群".equalsIgnoreCase(module.getModuleName())) {
                    if (map.containsKey(ProductCodeEnum.SFS2.getProductCode()) ||  map.containsKey(
                            ProductCodeEnum.HPC_DRP.getProductCode())
                            || map.containsKey(ProductCodeEnum.HPC_SAAS.getProductCode())) {
                        list.add(module);
                    }
                }else if ("镜像中心".equalsIgnoreCase(module.getModuleName())) {
                    if (map.containsKey(ProductCodeEnum.MIRROR_CENTER.getProductCode())) {
                        list.add(module);
                    }
                } else {
                    list.add(module);
                }
            }
        }

        return list;
    }

    private List<String> getUserPermissions(Boolean isAdmin ,Long entityId) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        AssertUtil.requireNonBlank(authUserInfo, "获取当前登录用户失败");
        Criteria criteria = new Criteria("userSid", Objects.requireNonNull(authUserInfo).getUserSid());
        if (Objects.nonNull(authUserInfo.getOrgSid())) {
            Org org = orgService.selectByPrimaryKey(authUserInfo.getOrgSid());
            if (Objects.nonNull(org)) {
                criteria.put("orgType", org.getOrgType());
            }
        }
        // 子用户过滤访问控制菜单
        boolean b = userMapper.assertIam(authUserInfo.getUserSid());

        /*List<String> permissions;
        if (b) {
            //获取支持的权限菜单
            criteria.clear();
            criteria.put("userSid", authUserInfo.getParentSid());
            if (Objects.nonNull(authUserInfo.getOrgSid())) {
                Org org = orgService.selectByPrimaryKey(authUserInfo.getOrgSid());
                if (Objects.nonNull(org)) {
                    criteria.put("orgType", org.getOrgType());
                }
            }
            List<String> parentPermission = moduleService.selectUserPermissions(criteria);
            permissions = getIamPermissions(authUserInfo.getUserSid());
            *//*permissions = permissions.stream()
                                     .filter(parentPermission::contains)
                                     .collect(Collectors.toList());*//*
            if (permissions.contains(ALL_PERMISSION)) {
                criteria.clear();
                criteria.put("isIam", true);
                List<Module> modules = moduleService.selectByParams(criteria);
                if (CollectionUtil.isNotEmpty(modules)) {
                    permissions = modules.stream().map(Module::getModuleSid).collect(Collectors.toList());
                }
            }
            // 子用户添加公共接口权限
            permissions.add(C1.C101);
            permissions.add(C1.C102);
            permissions.add(C1.C103);
            permissions.add(C1.C104);
            permissions.add(C1.C105);
            permissions.add(C1.C106);
            permissions.add(C1.C107);
            permissions.add(C1.C108);
            permissions.add(C1.C109);
            permissions.add(C1.C110);
            permissions.add(C1.C111);
            permissions.add(C1.C112);
            permissions.add(C1.C113);
            permissions.add(C1.C114);
            permissions.add(C1.C115);
            permissions.add(C1.C116);

            // 去掉购买套餐包权限
            permissions.remove(CC.CC0206);
        } else {
            permissions = moduleService.selectUserPermissions(criteria);
        }*/
        List<String> permissions = moduleService.selectUserPermissions(criteria);

        if (isAdmin) {
            criteria.clear();
            criteria.put("userSid", authUserInfo.getUserSid());
            criteria.put("orgSid", 0L);
            permissions.addAll(moduleService.selectUserPermissions(criteria));
        }
        Map<String, String> map = productInfoList();
        permissions = filterMenusPermission(permissions, map);
        //查询当前登录实体是否关联ai
        Integer aiCount = 0;
        if (null!=entityId && 0L !=entityId) {
            Criteria serviceCategory = new Criteria();
            serviceCategory.put("entityId",entityId);
            serviceCategory.put("showType","AI");
            aiCount = serviceCategoryMapper.countByParams(serviceCategory);
        }
        //如果没有关联ai产品就过滤掉资源分析下的modelarts
        if (aiCount == 0) {
            permissions = permissions.stream().filter(module -> !"BT0301".equals(module)).collect(Collectors.toList());
        }
        return permissions;
    }

    /**
     * 产品信息列表
     */
    private Map<String, String> productInfoList() {

        logger.info("运营实体Id:{}", RequestContextUtil.getEntityId());
        Criteria serviceCategoryCriteria = new Criteria("entityId", RequestContextUtil.getEntityId());
        serviceCategoryCriteria.put("publishStatus", "succeed");
        serviceCategoryCriteria.put("publishDtIsNotNull", ONE);
        serviceCategoryCriteria.put("serviceComponent", "innerService");
        List<ServiceCategory> serviceCategories = serviceCategoryMapper.selectDistinctByParams(serviceCategoryCriteria);
        serviceCategories = serviceCategories.stream()
                                             .filter(t -> Objects.nonNull(t.getEntityId()))
                                             .collect(Collectors.toList());
        Map<String, String> map = serviceCategories.stream()
                                                   .collect(Collectors.toMap(ServiceCategory::getServiceType,
                                                                             ServiceCategory::getProductName));
        return map;
    }

    /**
     * 对菜单根据产品来过滤
     *
     * @param
     * @param map
     */
    private List<String> filterMenusPermission(List<String> permissions, Map<String, String> map) {
        List<String> list = new ArrayList<>();
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(permissions)) {
            for (String permission : permissions) {
                if ("BH05".equalsIgnoreCase(permission)) {
                    if (map.containsKey(ProductCodeEnum.HPC.getProductCode()) || map.containsKey(
                            ProductCodeEnum.HPC_DRP.getProductCode())
                            || map.containsKey(ProductCodeEnum.HPC_SAAS.getProductCode())) {
                        list.add(permission);
                    }
                } else if ("BH01".equalsIgnoreCase(permission)) {
                    if (map.containsKey(ProductCodeEnum.HPC_DRP.getProductCode())) {
                        list.add(permission);
                    }
                } else if ("BH02".equalsIgnoreCase(permission)) {
                    if (map.containsKey(ProductCodeEnum.HPC_DRP.getProductCode()) || map.containsKey(
                            ProductCodeEnum.MODEL_ARTS.getProductCode())) {
                        list.add(permission);
                    }
                } else if ("BH03".equalsIgnoreCase(permission)) {
                    if (map.containsKey(ProductCodeEnum.SFS2.getProductCode()) || map.containsKey(
                            ProductCodeEnum.HPC_DRP.getProductCode())
                            || map.containsKey(ProductCodeEnum.HPC_SAAS.getProductCode())) {
                        list.add(permission);
                    }
                } else if ("BH07".equalsIgnoreCase(permission)) {
                    if (map.containsKey(ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode())) {
                        list.add(permission);
                    }
                } else if ("BH04".equalsIgnoreCase(permission)) {
                    if (map.containsKey(ProductCodeEnum.SFS2.getProductCode()) || map.containsKey(
                            ProductCodeEnum.HPC_DRP.getProductCode())
                            || map.containsKey(ProductCodeEnum.HPC_SAAS.getProductCode())) {
                        list.add(permission);
                    }
                } else {
                    list.add(permission);
                }
            }

            if (!map.containsKey(ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode())) {
                list = list.stream()
                           .filter(module -> !"BQ0107".equals(module))
                           .collect(Collectors.toList());
            }
        }

        return list;
    }

    /**
     * 设置查询用户参数
     *
     * @param request the query request
     */
    private Criteria packageQueryUserInfo(DescribeUserRequest request) {
        Criteria criteria = new Criteria();
        // 如果需要用户是否在当前组织的标识， orgSid用来判断是否属于当前组织，不做查询筛选条件
        if (request.getJoinFlag() == null || !request.getJoinFlag()) {
            criteria.put("orgSid", request.getOrgSid());
        }
        if (!Strings.isNullOrEmpty(request.getSearchFilter())) {
            criteria.put("searchFilter", request.getSearchFilter());
        }
        if (!Strings.isNullOrEmpty(request.getStartDate())) {
            criteria.put("startDate", request.getStartDate());
            criteria.put("endDate", request.getEndDate());
        }
        if (StringUtil.isNotBlank(request.getStatus())) {
            criteria.put("status", Integer.parseInt(request.getStatus()));
        }
        if (!Strings.isNullOrEmpty(request.getAccount())) {
            criteria.put("account", request.getAccount());
        }

        if (!Strings.isNullOrEmpty(request.getEmail())) {
            criteria.put("email", CCSPCacheUtil.verifyAndCCSPEncrypt(request.getEmail()));
        }
        if (!Strings.isNullOrEmpty(request.getMobile())) {
            criteria.put("mobile", CCSPCacheUtil.verifyAndCCSPEncrypt(request.getMobile()));
        }
        if (!Strings.isNullOrEmpty(request.getOrgName())) {
            criteria.put("orgName", request.getOrgName());
        }
        if (!Strings.isNullOrEmpty(request.getCertificationStatus())) {
            criteria.put("certificationStatus", request.getCertificationStatus());
        }
        if (!Strings.isNullOrEmpty(request.getAccountNameLike())) {
            criteria.put("accountNameLike", request.getAccountNameLike());
        }
        if (Objects.nonNull(request.getUserSid())) {
            criteria.put("userSid", request.getUserSid());
        }
        criteria.put("hiddenChild", request.getHiddenChild());
        return criteria;
    }

    /**
     * 过滤子节点为空的菜单, P权限为大屏展示，目前无子节点，需要保留
     */
    private List<TreeNode> filterEmptyChildrenNode(List<TreeNode> nodes) {
        return nodes.stream()
                    .filter(node -> CollectionUtil.isNotEmpty(node.getChildren()) || NOT_FILTER_MODULE.contains(
                            node.getId()))
                    .collect(Collectors.toList());
    }

    /**
     * 获取iam用户配置的权限
     */
    private List<String> getIamPermissions(Long userSid) {
        return getIamPermissions2(userSid);
        /*List<String> iamPermissions = Lists.newArrayList();
        //获取用户所拥有权限配置
        List<Long> assertionSids = assertionMapper.selectUserAssertionSids(userSid);
        if (CollectionUtil.isNotEmpty(assertionSids)) {
            Criteria query = new Criteria();
            query.put("assertionSids", assertionSids);
            query.put("status", true);
            List<Assertion> assertions = assertionMapper.selectByParams(query);
            if (CollectionUtil.isNotEmpty(assertions)) {
                if (assertions.stream().anyMatch(assertion -> assertion.getServiceCode().equals(ALL_PERMISSION))) {
                    //获取306权限
                    List<RoleModule> roleModules = roleModuleMapper.selectRoleModuleByUserSid(userSid);
                    iamPermissions.addAll(roleModules.stream().map(RoleModule::getModuleSid).collect(Collectors.toList()));
                }
                assertions.forEach(assertion -> {
                    List<String> moduleSids = StrUtil.splitTrim(assertion.getModuleSid(), StrUtil.COLON);
                    iamPermissions.addAll(moduleSids);
                });
            }
            return iamPermissions.stream().distinct().collect(Collectors.toList());
        }
        return iamPermissions;*/
    }

    private List<String> getIamPermissions2(Long userSid) {
        List<String> list = moduleService.selectUserPermissionsFromGroup(userSid);
        if (CollUtil.isEmpty(list)) {
            log.warn("{}未获取到任何权限", userSid);
            return Collections.emptyList();
        }
        return list;
    }

    /**
     * 提交认证
     */
    @AuthorizeOss(action = AuthModule.COMMON.PUBLIC.C1.C104)
    @ApiOperation(httpMethod = "POST", value = "提交认证")
    @PostMapping("/submitAuth")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'提交实名认证'", tagNameUs ="'Submit real-name authentication'",
            resource = OperationResourceEnum.SUBMITAUTH, bizId = "#request.userSid")
    public RestResult submitAuth(@RequestBody @LogParam("request") AuthUserRequest request) {
        if (request == null) {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_417956708));
        }
        if(request.getRealName() != null){
            Pattern pattern = Pattern.compile("^[\\u4e00-\\u9fa5]{1,10}$");
            Matcher matcher = pattern.matcher(request.getRealName());
            if (!matcher.find()) {
                return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1364846652));
            }
        }else{
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2061043592));
        }
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (request.getUserSid() == null) {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1000949214));
        } else {
            if (ObjectUtils.isEmpty(authUserInfo)){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_531007114));
            }
            if (!authUserInfo.getUserSid().equals(request.getUserSid())){
                throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
            }
        }

        if (request.getRealName() == null) {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_975614668));
        }

        if (authUserInfo == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_531007114));
        }

        if (!authUserInfo.getUserSid().equals(request.getUserSid())){
            throw new BizException(WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }

        if (StringUtil.isEmpty(request.getAuthId())) {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1365012338));
        } else {
            String authId = request.getAuthId();

            //校验身份证信息是否合规
            boolean idNumber = IDUtils.isIDNumber(authId);
            if (!idNumber) {
                return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1449909042));
            }
            if(!IDUtils.isAdult(authId)){
                return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1371465254));
            }

            // 检验身份证使用被使用
            int count = userMapper.countAuthIdHash(authUserInfo.getUserSid(), DigestUtils.sha256Hex(authId));
            if (count > 0) {
                return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1449909042));
            }
        }

        if (StringUtil.isEmpty(request.getIdCardFront()) || StringUtil.isEmpty(request.getIdCardReverse())) {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1362024570));
        }

        if (!request.getRealNameCheck()) {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1121608694));
        }

        String IdCardFrontDecrypt = request.getIdCardFront();
        String getIdCardReverseDecrypt = request.getIdCardReverse();
        if (StringUtil.isEmpty(IdCardFrontDecrypt) || StringUtil.isEmpty(getIdCardReverseDecrypt)) {
            throw new BizException(WebUtil.getMessage(MsgCd.DECRYPT_PARAMS_FAILURE));
        }

        // 检查身份证文件是否使用
        this.checkFile(IdCardFrontDecrypt, authUserInfo);
        this.checkFile(getIdCardReverseDecrypt, authUserInfo);

        //个人认证和企业认证检查
        //获取认证状态
        UserCertificationStatusVO authStatus = userMapper.getCertificationStatusByUserSid(
                request.getUserSid());
        if (CertificationStatus.AUTHING.equals(authStatus.getAuthCertificationStatus())
                || CertificationStatus.AUTHING.equals(authStatus.getCompanyCertificationStatus())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1966210670));
        }

        User userOld = this.userMapper.findUserById(request.getUserSid());
        //更新用户表实名认证信息
        User user = new User();
        user.setUserSid(request.getUserSid());
        user.setUpdatedDt(new Date());
        user.setUpdatedBy(authUserInfo.getRealName());
        user.setCertificationStatus(CertificationStatus.AUTHING);
        user.setIdCardFront(IdCardFrontDecrypt);
        user.setIdCardReverse(getIdCardReverseDecrypt);
        user.setAuthName(request.getRealName());
        if (userOld.getVersion() != null) {
            user.setVersion(userOld.getVersion() + 1);
        } else {
            user.setVersion(1L);
        }
        user.setRemark("");
        user.setAuthId(CrytoUtilSimple.decrypt(request.getAuthId()));
        if (!CCSPCacheUtil.ccspServiceOpen()) {
            user.setAuthId(CrytoUtilSimple.encrypt(user.getAuthId()));
        }
        this.userMapper.updateAuthInfo(user);
        // 实名认证推送站内信
        List<User> users = userService.findAdminstratorsByEntityId(null);
        Map<String, String> content = Maps.newHashMap();
        String property = PropertiesUtil.getProperty("rightcloud.mgt.url");
        content.put("url",property + "#/appbssorg/authent?id="+authUserInfo.getUserSid()+"&type=user&orgSid="+authUserInfo.getOrgSid());
        content.put("userAccount", userOld.getAccount());
        String accountType = "";
        String accountTypeUs = "";
        if (Objects.isNull(userOld.getParentSid())) {
            accountType = ACCOUNT;
            accountTypeUs = "Main account";
        } else {
            accountType = SUB_USER;
            accountTypeUs = "Sub account";
        }
        content.put("accountType",  accountType);
        content.put("accountTypeUs",  accountTypeUs);
        content.put("applyAccountType", "用户管理员");
        content.put("detail", String.format("申请账户：%s", authUserInfo.getAccount()));
        if (!ObjectUtils.isEmpty(userOld) && !ObjectUtils.isEmpty(userOld.getParentSid())) {
            content.put("applyAccountType", "子账户");
            User parentUser = this.userMapper.selectByPrimaryKey(userOld.getParentSid());
            if (!ObjectUtils.isEmpty(parentUser)) {
                content.put("detail", String.format("管理员：%s <br>申请账户：%s", parentUser.getAccount(), authUserInfo.getAccount()));
            }
        }
        List<String> entityIds = users.stream().map(User::getEntityIds).distinct().collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(entityIds)){
            Set<Long> adminUserIds = users.stream().map(User::getUserSid).collect(Collectors.toSet());
            entityIds.forEach(entityId->{
                if(Objects.nonNull(entityId)){
                    BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
                    baseNotificationMqBean.getImsgUserIds().addAll(adminUserIds);
                    baseNotificationMqBean.setMsgId(NotificationConsts.PlatformMsg.AccountMsg.BSSMGT_VERIFY_PERSONAL);
                    baseNotificationMqBean.setMap(content);
                    baseNotificationMqBean.setEntityId(Long.valueOf(entityId));
                    rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.ACCOUNT, baseNotificationMqBean);
                }
            });
        }
        return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_781365859));
    }

    /**
     * 用户审核
     *
     * @param request 请求
     *
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = USER.CERTIFICATION_AUDIT)
    @ApiOperation(httpMethod = "POST", value = "用户审核")
    @PostMapping("/approve")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "#request.flag",
            resource = OperationResourceEnum.CERTIFICATIONAUDIT, bizId = "#request.userSid")
    public RestResult approve(@RequestBody @LogParam("request") AuthUserRequest request) {
        RestResult result;

        // 审批人权限判断
        boolean roleFlg = false;
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (Objects.isNull(authUserInfo)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1255531007));
        }
        List<Role> roles = roleService.findRolesByUserSid(authUserInfo.getUserSid());
        if (CollectionUtil.isNotEmpty(roles)) {
            List<Long> roleSids = roles.stream().map(Role::getRoleSid).collect(Collectors.toList());
            if (roleSids.contains(SysRoleEnum.OPERATION_ADMIN.getRoleSid())
                    || roleSids.contains(SysRoleEnum.OPERATION_GENERAL.getRoleSid())
                    || roleSids.contains(SysRoleEnum.OPERATION_MANAGER.getRoleSid())) {
                roleFlg = true;
            }
        }
        if (!roleFlg) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1594121035));
        }

        Long userSid = request.getUserSid();
        if (userSid == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1639417704));
        }
        if (StringUtil.isEmpty(request.getFlag())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_831986910));
        }
        User user1 = Optional.ofNullable(request.getUserSid())
                .map(userService::selectByPrimaryKey)
                .orElseThrow(() -> new BizException("用户不存在，无法完成审核"));
        User user = new User();
        user.setParentSid(user1.getParentSid());
        user.setUserSid(userSid);
        user.setUpdatedBy(RequestContextUtil.getAuthUserInfo().getRealName());
        user.setUpdatedDt(new Date());
        user.setRemark(request.getRemark());
        user.setAuthId(CrytoUtilSimple.decrypt(user1.getAuthId()));
        if (!CCSPCacheUtil.ccspServiceOpen()) {
            user.setAuthId(CrytoUtilSimple.encrypt(user.getAuthId()));
        }
        Map<String, String> map = new HashMap<>();
        map.put("userAccount",user1.getAccount());
        map.put("name",user1.getAccount());
        map.put("user",user1.getAccount());
        String msgId;
        String msgId2;
        if ("pass".equals(request.getFlag())) {
            if(Objects.isNull(user.getParentSid())){
                sysOssMessageService.sendOssMessage(userSid,map,NotificationConsts.ConsoleMsg.AccountMsg.TENANT_VERIFY_PERSONAL_ACCESS
                        ,NotificationConsts.PlatformMsg.AccountMsg.BSSMGT_VERIFY_PERSONAL_ACCESS,RequestContextUtil.getEntityId());
            }else {
                User parentUser = userService.selectByPrimaryKey(user1.getParentSid());
                map.put("userAccount",parentUser.getAccount());
                map.put("subUserAccount",user1.getAccount());
                sysOssMessageService.sendOssMessage(user.getParentSid(),map,NotificationConsts.ConsoleMsg.AccountMsg.TENANT_SUBUSER_VERIFY_ACCESS
                        ,NotificationConsts.PlatformMsg.AccountMsg.BSSMGT_SUBUSER_VERIFY_ACCESS,RequestContextUtil.getEntityId());
            }
            user.setRealName(user1.getAuthName());
            user.setCertificationStatus(CertificationStatus.AUTHSUCCEED);
            result = new RestResult(Status.SUCCESS, "审核成功", JsonUtil.toJson(request));

        } else {
            if(Objects.isNull(user.getParentSid())){
                sysOssMessageService.sendOssMessage(userSid,map,NotificationConsts.ConsoleMsg.AccountMsg.TENANT_VERIFY_PERSONAL_REJECT
                        ,NotificationConsts.PlatformMsg.AccountMsg.BSSMGT_VERIFY_PERSONAL_REJECT,RequestContextUtil.getEntityId());
            }else {
                User parentUser = userService.selectByPrimaryKey(user1.getParentSid());
                map.put("userAccount",parentUser.getAccount());
                map.put("subUserAccount",user1.getAccount());
                map.put("reason",request.getRemark());
                sysOssMessageService.sendOssMessage(user.getParentSid(),map,NotificationConsts.ConsoleMsg.AccountMsg.TENANT_SUBUSER_VERIFY_REJECT
                        ,NotificationConsts.PlatformMsg.AccountMsg.BSSMGT_SUBUSER_VERIFY_REJECT,RequestContextUtil.getEntityId());
            }
            user.setCertificationStatus(CertificationStatus.AUTHFILED);
            result = new RestResult(Status.SUCCESS, "审核操作成功", JsonUtil.toJson(request));

        }
        userMapper.updateAuthInfo(user);
        if ("pass".equals(request.getFlag())) {
            policyService.synHpcToLdap(user1.getOrgSid());
        } else {
            // 去掉用户auth_id_hash值
            userMapper.updateAuthIdHash(user.getUserSid(), null);
        }
        webSocketSend(userSid);
        return result;
    }

    /**
     * 获取用户认证信息
     *
     * @param userId 用户id
     *
     * @return {@link PersonalAuthenticationResponse}
     */
    @AuthorizeOss(action = USER.GET_ACCOUNTS)
    @ApiOperation(httpMethod = "GET", value = "获取用户认证信息", notes = "通过用户id获取用户认证信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户id", dataType = "long", paramType = "path", required = true)})
    @GetMapping("/getAuthUserInfoById/{userId}")
    public PersonalAuthenticationResponse getAuthUserInfoById(@PathVariable Long userId) {
        // 获取用户
        if (userId == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_367330839));
        }
        User user = userService.selectByPrimaryKey(userId);
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (ObjectUtils.isEmpty(authUser)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_531007114));
        }
        if (!user.getUserSid().equals(authUser.getUserSid()) && !ObjectUtils.isEmpty(authUser.getOrgSid())) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        if (!ObjectUtils.isEmpty(authUser) && !ObjectUtils.isEmpty(authUser.getRemark())) {
            // 运营端和租户端判断
            if ("bss".equals(authUser.getRemark())) {
                user.setRealName(user.getAuthName());
                //若用户未未认证或认证中，则姓名和身份证不脱敏显示，否则脱敏显示
                if (CertificationStatus.NOAUTH.equals(user.getCertificationStatus()) || CertificationStatus.AUTHING.equals(user.getCertificationStatus())) {
                    if (StringUtil.isNotBlank(user.getAuthId())) {
                        user.setAuthId(CrytoUtilSimple.decrypt(user.getAuthId()));
                    }
                } else {
                    if (StringUtil.isNotBlank(user.getAuthName())) {
                        user.setRealName(DesensitizedUtil.chineseName(user.getAuthName()));
                    }
                    if (StringUtil.isNotBlank(user.getAuthId())) {
                        user.setAuthId(DesensitizedUtil.idCardNum(CrytoUtilSimple.decrypt(user.getAuthId()), 3, 4));
                    }
                }
            }
            if ("console".equals(authUser.getRemark())) {
                user.setRealName(DesensitizedUtil.chineseName(user.getAuthName()));
                user.setAuthId(DesensitizedUtil.idCardNum(CrytoUtilSimple.decrypt(user.getAuthId()), 3, 4));
            }
        }
        PersonalAuthenticationResponse convertResp = BeanConvertUtil.convert(user, PersonalAuthenticationResponse.class);
        if (ObjectUtils.isEmpty(convertResp)) {
            return convertResp;
        }
        convertResp.setApplyAccountType("用户管理员");
        if (!ObjectUtils.isEmpty(user.getParentSid())) {
            convertResp.setApplyAccountType("子账户");
            User admin = userService.selectByPrimaryKey(user.getParentSid());
            if (!ObjectUtils.isEmpty(admin)) {
                convertResp.setAdminName(admin.getAccount());
            }
        }
        return convertResp;
    }

    /**
     * 华为跳转路径取得
     *
     * @param productIds 产品id
     *
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = AuthModule.CB.CB19)
    @GetMapping("/huawei/url")
    @ApiOperation(httpMethod = "GET", value = "华为跳转路径取得", notes = "华为跳转路径取得")
    public RestResult getHuaweiUrl(
            @ApiParam(value = "华为产品列表") @RequestParam(value = "productId", required = false) List<String> productIds) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (authUserInfo == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_531007114));
        }
        Org org = orgService.selectRootOrg(authUserInfo.getOrgSid());
        if (!authUserInfo.isAuth() && !CertificationStatus.AUTHSUCCEED.equals(org.getCertificationStatus())) {
            return new RestResult(MapsKit.of(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1172412509), WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1813224111)));
        }
        Long orgSid = authUserInfo.getOrgSid();
        if (Objects.isNull(orgSid)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1161901479));
        }
        OrgIdp orgIdp = orgIdpMapper.selectByParam(orgSid);
        StringBuilder url =
                new StringBuilder(Objects.nonNull(orgIdp) ? orgIdp.getRedirectUrl()
                                                                  .substring(
                                                                          orgIdp.getRedirectUrl().indexOf("/authui/"))
                                          : "");

        CloudEnvParams cloudEnvParams = new CloudEnvParams();
        cloudEnvParams.setCloudEnvType(IAM_ENV_TYPE);
        List<CloudEnv> cloudEnvs = cloudEnvRemoteService.selectByParams(cloudEnvParams);
        Long envId = Objects.nonNull(cloudEnvs) && Objects.nonNull(cloudEnvs.get(0)) ? cloudEnvs.get(0).getId() : 0;
        CloudEnv cloudEnv = cloudEnvRemoteService.selectByPrimaryKey(envId);

        if (cloudEnv == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1767929217));
        }

        String attrData = CrytoUtilSimple.decrypt(cloudEnv.getAttrData());
        final JSONObject jsonObject = JSON.parseObject(ObjectUtils.isEmpty(attrData) ? cloudEnv.getAttrData() : attrData);
        String region = jsonObject.get(CloudEnvTenantKey.REGION).toString();

        // 华为产品链接取得
        String productUrl = sysconfigService.getValueByConfigKey("iam.product.url");
        // 身份提供商访问登录链接
        url.insert(0, sysconfigService.getValueByConfigKey("iam.identityprovider.url"));
        // 配置文件中的参数个数取得
        int paramCount = productUrl.length() - productUrl.replace("%s", "?").length();
        // Hcso的IAM用户信息取得
        //IamCredentialResult userResult = userService.getIamCredential(envId);
        IamCredentialResult userResult = null;
        String agencyId = "";
        if (paramCount == 4 && Strings.isNullOrEmpty(agencyId)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_846490870));
        }
        //获取当前用户是否已经hcso认证
        cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria criteria
                = new cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria();
        if (Objects.nonNull(authUserInfo.getParentSid())) {
            criteria.put("refUserId", authUserInfo.getParentSid());
        } else {
            criteria.put("refUserId", authUserInfo.getUserSid());
        }
        List<HcsoUser> hcsoUsers = hcsoUserRemoteService.selectByParams(criteria);

        Map<String, String> urlMap = new HashMap<>();
        if (Objects.nonNull(productIds)) {
            productIds.forEach(productId -> {
                IamProductUrlEnum productUrlEnum = IamProductUrlEnum.getByProductName(productId);
                if (ObjectUtils.isEmpty(productUrlEnum.getFirstParam())) {
                    return;
                }
                //hcso认证不为空添加跳转地址
                if (!ObjectUtils.isEmpty(hcsoUsers)) {
                    // 参数3个的情况默认华为云，参数四个为HCSO
                    if (paramCount == 4) {
                        logger.info(" 进入HCSO ");
                        urlMap.put(productId, url + "&service=" + EnCode.decode(
                                String.format(productUrl, productUrlEnum.getFirstParam(), agencyId, region,
                                              productUrlEnum.getSecondParam())));
                        return;
                    }
                    urlMap.put(productId, url + "&service=" + EnCode.decode(
                            String.format(productUrl, productUrlEnum.getFirstParam(), region,
                                          productUrlEnum.getSecondParam())));
                    return;
                }
                //hcso认证为空跳转地址置空
                urlMap.put(productId, "");
            });
        }
        return new RestResult(urlMap);
    }

    /**
     * 华为跳转路径取得(通过资源编码)
     *
     * @return {@link RestResult}
     */
    @GetMapping("/huawei/url/{code}")
    @ApiOperation(httpMethod = "GET", value = "华为跳转路径取得(通过资源编码)", notes = "华为跳转路径取得(通过资源编码)")
    public RestResult getHuaweiUrl(@PathVariable("code") String code,
                                   @RequestParam(value = "resCloudEnvId", required = false) Long resCloudEnvId) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (authUserInfo == null) {
            throw new BizException("获取当前登录用户失败");
        }
        Long orgSid = authUserInfo.getOrgSid();
        if (Objects.isNull(orgSid)) {
            throw new BizException("请检查用户信息");
        }
        orgSid = orgService.selectRootOrg(orgSid).getOrgSid();
        OrgIdp orgIdp = orgIdpMapper.selectByParam(orgSid);
        StringBuilder url =
                new StringBuilder(Objects.nonNull(orgIdp) ? orgIdp.getRedirectUrl()
                        .substring(
                                orgIdp.getRedirectUrl().indexOf("/authui/"))
                        : "");

        List<ResCloudEnv> resCloudEnvList = cloudEnvRemoteService.selectReginsByParam(IAM_ENV_TYPE, orgSid);

        if (!CollectionUtils.isEmpty(resCloudEnvList)) {
            for (ResCloudEnv resCloudEnv : resCloudEnvList) {
                if (Objects.nonNull(resCloudEnvId) && !resCloudEnv.getId().equals(resCloudEnvId)) {
                    continue;
                }
                String region = resCloudEnv.getRegion();
                Long relaIdpId = resCloudEnv.getRelaIdpId();
                // 华为产品链接取得(从rightcloud库，idp相关表中获取)
                // 获取通用路径
                String federationProductUrl = cloudEnvRemoteService.selectCommonUrl(relaIdpId);
                // 身份提供商访问登录链接
                String loginUrl = cloudEnvRemoteService.selectLoginUrl(relaIdpId);
                url.insert(0, loginUrl);
                // 获取产品特定路径
                cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria criteria = new cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria();
                criteria.put("relaIdpId", relaIdpId);
                criteria.put("envCode", code);
                List<String> productLinks = cloudEnvRemoteService.selectProductUrl(criteria);
                if (CollectionUtil.isEmpty(productLinks)) {
                    continue;
                }
                String productLink = Objects.nonNull(productLinks.get(0)) ? productLinks.get(0) : "";
                // 获取华为产品跳转URL所需参数
                IamProductUrlEnum productUrlEnum = IamProductUrlEnum.getByProductName(code);
                /*if (ObjectUtils.isEmpty(productUrlEnum.getFirstParam())) {
                    throw new BizException("获取华为产品跳转URL所需参数失败!");
                }*/
                return new RestResult(url + "&service=" + EnCode.decode(
                        String.format(federationProductUrl + productLink, region, productUrlEnum.getSecondParam())));
            }
        }
        throw new BizException("联邦配置存在问题,请检查联邦配置!");
    }

    @GetMapping("/huawei/url/monitor/{code}")
    @ApiOperation(httpMethod = "GET", value = "华为跳转路径取得(通过资源编码)", notes = "华为跳转路径取得(通过资源编码)")
    public RestResult getHuaweiUrl(@PathVariable("code") String code,
                                   @RequestParam(value = "resCloudEnvId", required = false) Long resCloudEnvId,
                                   @RequestParam(value = "resourceId", required = false) String resourceId
    ) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (authUserInfo == null) {
            throw new BizException("获取当前登录用户失败");
        }
        Long orgSid = authUserInfo.getOrgSid();
        if (Objects.isNull(orgSid)) {
            throw new BizException("请检查用户信息");
        }

        Map<String, String> defaultMap = new HashMap<String, String>() {{
            put("ecsX", "55");
            put("ecsY", "133");
            put("evsX", "55");
            put("evsY", "95");
            put("rdsX", "55");
            put("rdsY", "95");
        }};
        Map<String, String> map = new HashMap<>();
        String codeX = code.toLowerCase() + "X";
        String codeY = code.toLowerCase() + "Y";
        String gapX = StrUtil.isBlank(System.getenv(codeX)) ? defaultMap.get(codeX) : System.getenv(codeX);
        String gapY = StrUtil.isBlank(System.getenv(codeY)) ? defaultMap.get(codeY) : System.getenv(codeY);
        gapX = getGapFromDB(codeX, gapX);
        gapY = getGapFromDB(codeY, gapY);
        map.put("x", gapX);
        map.put("y", gapY);
        //测试跳转
        String federationUrl = System.getenv("custom_federation_url");
        //https://auth.huaweicloud.com/authui/federation/websso?domain_id=0d12b96aa0000f9d0f02c00acafcaee0&idp=pfce-318&protocol=saml&service=
        if (StrUtil.isNotBlank(federationUrl)) {
            log.info("custom_federation_url:{}", federationUrl);
            String serviceUrl = System.getenv("custom_federation_service_url");
            IamProductUrlEnum productName = IamProductUrlEnum.getByProductName(code + "_monitor");
            //https://console.huaweicloud.com/%s/?locale=zh-cn&region=cn-north-4%s
            String secondParam = String.format(productName.getSecondParam(), resourceId);
            String decode = EnCode.decode(String.format(serviceUrl, productName.getFirstParam(), secondParam));
            log.info("decode:{}", decode);
            map.put("src", federationUrl + decode);
            //return new RestResult(federationUrl + decode);
            return new RestResult(map);
        }


        orgSid = orgService.selectRootOrg(orgSid).getOrgSid();
        OrgIdp orgIdp = orgIdpMapper.selectByParam(orgSid);
        StringBuilder url =
                new StringBuilder(Objects.nonNull(orgIdp) ? orgIdp.getRedirectUrl()
                                                                  .substring(orgIdp.getRedirectUrl().indexOf("/authui/")) : "");

        List<ResCloudEnv> resCloudEnvList = cloudEnvRemoteService.selectReginsByParam(IAM_ENV_TYPE, orgSid);
        log.info("resCloudEnvList:{}", resCloudEnvList);
        if (!CollectionUtils.isEmpty(resCloudEnvList)) {
            for (ResCloudEnv resCloudEnv : resCloudEnvList) {
                if (Objects.nonNull(resCloudEnvId) && !resCloudEnv.getId().equals(resCloudEnvId)) {
                    continue;
                }
                String region = resCloudEnv.getRegion();
                Long relaIdpId = resCloudEnv.getRelaIdpId();
                // 华为产品链接取得(从rightcloud库，idp相关表中获取)
                // 获取通用路径
                String federationProductUrl = cloudEnvRemoteService.selectCommonUrl(relaIdpId);
                // 身份提供商访问登录链接
                String loginUrl = cloudEnvRemoteService.selectLoginUrl(relaIdpId);
                url.insert(0, loginUrl);
                // 获取产品特定路径
                cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria criteria = new cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria();
                criteria.put("relaIdpId", relaIdpId);
                criteria.put("envCode", code);
                List<String> productLinks = cloudEnvRemoteService.selectProductUrl(criteria);
                log.info("productLinks:{}", productLinks);
                if (CollectionUtil.isEmpty(productLinks)) {
                    continue;
                }
                String productLink = Objects.nonNull(productLinks.get(0)) ? productLinks.get(0) : "";
                // 获取华为产品跳转URL所需参数
                IamProductUrlEnum productUrlEnum = IamProductUrlEnum.getByProductName(code + "_monitor");
                log.info("productUrlEnum:{}", productUrlEnum);
                if (ObjectUtils.isEmpty(productUrlEnum.getFirstParam())) {
                    return new RestResult(Status.FAILURE, "获取华为产品跳转URL所需参数失败!");
                }

                if (IamProductUrlEnum.EVS_MONITOR.name().equals(productUrlEnum.name())) {
                    String vmId = cloudEnvRemoteService.selectVmId(resourceId);
                    log.info("vmId:{},resourceId:{}", vmId, resourceId);
                    resourceId = vmId;
                }

                if (IamProductUrlEnum.RDS_MONITOR.name().equals(productUrlEnum.name())) {
                    final RestResult<String> result = resourceDcFeignService.getRdsTypeByUuid(resourceId);
                    if(ObjectUtil.isEmpty(result)|| !result.getStatus()){
                        throw new BizException("获取资源失败");
                    }
                    String dimK="";
                    if("MySQL".equals(result.getData().toString())){
                        dimK="rds_cluster_id";
                    }else{
                        dimK="postgresql_cluster_id";
                    }
                    String secondParam = String.format(productUrlEnum.getSecondParam(),dimK, resourceId);
                    log.info("secondParam:{}", secondParam);
                    map.put("src", url + "&service=" + EnCode.decode(
                            String.format(federationProductUrl + productLink, region, secondParam)));
                }else{
                    String secondParam = String.format(productUrlEnum.getSecondParam(), resourceId);
                    log.info("secondParam:{}", secondParam);
                    map.put("src", url + "&service=" + EnCode.decode(
                            String.format(federationProductUrl + productLink, region, secondParam)));
                }
                return new RestResult(map);
            }
        }
        return new RestResult(Status.FAILURE, "联邦配置存在问题,请检查联邦配置!");
    }

    private String getGapFromDB(String code, String gap) {
        log.info("gap from db:{}", code);
        Criteria criteria = new Criteria();
        criteria.put("configKey", code);
        List<SysConfig> dbX = sysConfigService.selectByParams(criteria);
        if (!dbX.isEmpty()) {
            return dbX.get(0).getConfigValue();
        }
        return gap;
    }

    /**
     * [INNER API] 禁用用户资源权限
     *
     * @param accountId 帐户ID
     * @return {@link RestResult}
     */
    @RejectCall
    @PutMapping("/disable/freeze/status/schedule/{accountId}/{minFrozenAmount}")
    @ApiImplicitParam(name = "accountId", value = "账户ID", paramType = "path", dataType = "long", required = true)
    @ApiOperation(httpMethod = "PUT", value = "禁用账户资源权限", notes = "提供给schedule自动冻结账户的接口")
    @GlobalTransactional
    public RestResult disableFreezeStatusSchedule(@PathVariable Long accountId, @PathVariable String minFrozenAmount) {
        boolean result = userService.disableFreezeStatusByAccountIDWithLock(accountId, ZERO, true, minFrozenAmount,false);
        return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1117764661), result);
    }

    /**
     * [INNER API] 禁用用户资源权限
     *
     * @param accountId       帐户ID
     * @param minFrozenAmount 最小冻结量
     * @return {@link RestResult}
     */
    @RejectCall
    @PutMapping("/disable/freeze/status/bss/{accountId}/{minFrozenAmount}")
    @ApiImplicitParam(name = "accountId", value = "账户ID", paramType = "path", dataType = "long", required = true)
    @ApiOperation(httpMethod = "PUT", value = "禁用账户资源权限", notes = "提供给bss自动冻结账户的接口")
    @GlobalTransactional
    public RestResult disableFreezeBssStatus(@PathVariable Long accountId, @PathVariable String minFrozenAmount) {
        boolean b = userService.disableFreezeStatusByAccountIDWithLock(accountId, ZERO, false, minFrozenAmount,true);
        return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1117764661), b);
    }

    /**
     * 禁用用户资源权限(试用期账号到期调用不停止作业，需要停止作业单独策略处理）
     * [INNER API] 试用期账号冻结定时任务
     *
     * @param accountId 帐户ID
     * @return {@link RestResult}
     */
    @PutMapping("/disable/freeze/status/schedule/expire/{accountId}")
    @RejectCall
    @ApiImplicitParam(name = "accountId", value = "账户ID", paramType = "path", dataType = "long", required = true)
    @ApiOperation(httpMethod = "PUT", value = "禁用账户资源权限", notes = "提供给schedule自动冻结账户的接口")
    @GlobalTransactional
    public RestResult expireDisableFreezeStatus(@PathVariable Long accountId) {
        boolean status = userService.expireFreezeStatusByAccountID(accountId, ONE, true, null);
        return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1117764661), status);
    }

    /**
     * [INNER API] 启用用户资源权限
     *
     * @param accountId 帐户ID
     * @return {@link RestResult}
     */
    @RejectCall
    @PutMapping("/disable/thaw/status/bss/{accountId}")
    @ApiImplicitParam(name = "accountId", value = "账户ID", paramType = "path", dataType = "long", required = true)
    @ApiOperation(httpMethod = "PUT", value = "启用账户资源权限", notes = "提供给schedule、bss自动解冻账户的接口")
    @GlobalTransactional
    public RestResult disableThawBssStatus(@PathVariable Long accountId) {
        boolean b = false;
        BizBillingAccount account = bizBillingAccountMapper.selectByPrimaryKey(accountId);
        if (Objects.isNull(account)) {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR), b);
        }
        try {
            b = userService.enableFreezeStatus(accountId, false, false, false,null, cn.com.cloudstar.rightcloud.oss.common.constants.Constants.ZERO);
        } catch (Exception e) {
            log.error("[{}]解冻失败", accountId);
        }
        return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1131222133), b);
    }

    /**
     * 禁用用户资源权限
     *
     * @param userSid 用户ID
     * @param smsCode 短信代码
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = AuthModule.BQ.BQ01080203)
    @PutMapping("/disable/freeze/status/{userSid}/{smscode}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户ID", paramType = "path", dataType = "long", required = true),
            @ApiImplicitParam(name = "smscode", value = "验证码", paramType = "path", dataType = "string", required = true)})
    @ApiOperation(httpMethod = "PUT", value = "禁用用户资源权限", notes = "禁用用户资源权限")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'禁用用户资源权限'", tagNameUs ="'Disable user resource permissions'",
            resource = OperationResourceEnum.DISABLE_RESOURCESTATUS, bizId = "#userSid")
    @DataPermission(resource = OperationResourceEnum.DISABLE_RESOURCESTATUS, bizId = "#userSid" )
    @GlobalTransactional
    public RestResult disableFreezeStatus(@PathVariable("userSid") @LogParam("userSid") Long userSid,
                                          @PathVariable("smscode") String smsCode) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        Pattern pattern = Pattern.compile("[^0-9]");
        Matcher matcher = pattern.matcher(smsCode);
        String result = matcher.replaceAll("").trim();
        int num=6;
        if(result.length() !=num){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1442388670));
        }
        CheckSmsCodeUtil.checkCode(null, smsCode);
        BizBillingAccount account = bizBillingAccountMapper.getByUserSidAndEntityId(userSid, RequestContextUtil.getEntityId());
        if (Objects.isNull(account)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        if ("freeze".equals(account.getStatus()) || StringUtils.isNotBlank(JedisUtil.INSTANCE.get(RedisLockConstants.ACCOUNT_FREEZE_STATUS_KEY + account.getId()))) {
            throw new BizException(WebUtil.getMessage(MsgCd.INFO_OPERATE_FREEZING));
        }
        CompletableFuture.runAsync(()-> {
            AuthUserHolder.setAuthUser(authUserInfo);
            doFreezeAndSendMsg(userSid, authUserInfo, account);
        });
        cfnUserAction(userSid, UserAccountStatusEnum.FREEZE.getType());
        return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_FREEZE_SUCCESS));
    }

    private void doFreezeAndSendMsg(Long userSid, AuthUser authUserInfo, BizBillingAccount account) {
        boolean freezeResult = userService.disableFreezeStatusByAccountIDWithLock(account.getId(), ONE, true, null,true);
        //冻结的时候，通知租户管理员
        this.notifyUserAccount(authUserInfo, userSid, false,freezeResult);
        //还原当前线程登录用户
        AuthUserHolder.setAuthUser(authUserInfo);
        frozenLog(userSid, "冻结",RequestContextUtil.getRequest(),freezeResult);
    }


    /**
     * 解冻/冻结日志
     */
    private void frozenLog(Long userSid, String actionName, HttpServletRequest request,boolean freezeResult) {

        try {
            if (Objects.nonNull(userSid)) {
                cn.com.cloudstar.rightcloud.core.pojo.dto.user.User user = userMapper.selectByPrimaryKey(userSid);
                if (Objects.nonNull(user)) {
                    ActionLog log = new ActionLog();
                    log.setAccount("系统");
                    log.setActionName(actionName);
                    log.setResource(actionName);
                    log.setTagName(actionName+user.getAccount());
                    log.setActionTime(new Date());
                    log.setSuccess("成功");
                    if (!freezeResult) {
                        log.setSuccess("失败");
                    }
                    String remoteIp = cn.com.cloudstar.rightcloud.common.util.IPAddressUtil.getRemoteHostIp(request);
                    String ipAddress = cn.com.cloudstar.rightcloud.common.util.IPAddressUtil.getIpAddress();
                    log.setActionPath(cn.com.cloudstar.rightcloud.common.util.StringUtil.getUri(request));
                    log.setActionMethod("ProductQuotaController.operateBizAccountProductQuotas");
                    log.setHttpMethod(request.getMethod());
                    log.setLbIp(remoteIp);
                    log.setRemoteIp(ipAddress);
                    log.setRoleName("系统");
                    if (Objects.nonNull(tracer)) {
                        Span span = tracer.currentSpan();
                        if (Objects.nonNull(span) && Objects.nonNull(span.context())) {
                            log.setTraceId(span.context().traceId());
                            log.setSpanId(span.context().spanId());
                        }
                    }
                    String userClient = request.getHeader("User-Agent");
                    log.setClient(userClient);
                    mongoTemplate.insert(log, "action_log");


                    String logDetail  = "冻结失败";
                    if (freezeResult) {
                        logDetail  = "冻结成功";
                    }
                    OperationResourceEnum opEnum = OperationResourceEnum.DISABLE_RESOURCESTATUS;
                    bizCustomerActionLogService.insertActionLog(user, opEnum, logDetail);

                }
            }
        } catch (Exception e) {
            log.error("exception message:", e);
            log.error("SystemConfigCtrl_【{}】", JSON.toJSONString(e.getMessage()));
        }
    }




    /**
     * 解冻
     *
     * @param userSid 用户ID
     * @param smscode Smscode
     * @return {@link RestResult}
     */
    @PutMapping("/enable/freeze/status/{userSid}/{smscode}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户ID", paramType = "path", dataType = "long", required = true),
            @ApiImplicitParam(name = "smscode", value = "验证码", paramType = "path", dataType = "string", required = true)})
    @ApiOperation(httpMethod = "PUT", value = "解冻", notes = "解冻")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'客户名称'", tagNameUs ="'Customer Name'",
            resource = OperationResourceEnum.ENABLE_FREEZESTATUS, bizId = "#userSid")
    @AuthorizeOss(action = BQ.BQ01080203)
    @DataPermission(resource = OperationResourceEnum.DISABLE_RESOURCESTATUS, bizId = "#userSid" )
    @GlobalTransactional
    public RestResult enableFreezeStatus(@PathVariable("userSid") Long userSid,
                                         @PathVariable("smscode") String smscode) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        Pattern pattern = Pattern.compile("[^0-9]");
        Matcher matcher = pattern.matcher(smscode);
        String result = matcher.replaceAll("").trim();
        int num=6;
        if(result.length() !=num){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1442388670));
        }
        //验证短信验证码
        CheckSmsCodeUtil.checkCode(null, smscode);
        BizBillingAccount account = bizBillingAccountMapper.getByUserSidAndEntityId(userSid, RequestContextUtil.getEntityId());
        if ("normal".equals(account.getStatus()) || StringUtils.isNotBlank(JedisUtil.INSTANCE.get(RedisLockConstants.ACCOUNT_FREEZE_STATUS_KEY + account.getId()))) {
            throw new BizException(WebUtil.getMessage(MsgCd.INFO_OPERATE_THAWING));
        }
        RequestAttributes requestAttributes = RequestContextHolder.currentRequestAttributes();
        CompletableFuture.runAsync(()-> {
            AuthUserHolder.setAuthUser(authUserInfo);
            boolean unfreezeResult = userService.enableFreezeStatus(userSid, true, true, true, null,
                cn.com.cloudstar.rightcloud.oss.common.constants.Constants.ONE);
            if (unfreezeResult) {
                //通知租户已经解冻
                this.notifyUserAccount(authUserInfo, userSid, true, unfreezeResult);
            }
        });
        cfnUserAction(userSid, UserAccountStatusEnum.NORMAL.getType());
        return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_FREEZE_SUCCESS));
    }

    // 手动冻结、解冻租户通知
    private void notifyUserAccount(AuthUser authUserInfo, Long userSid, boolean isEnable, boolean result) {
        SysBssEntity entity = sysBssEntityService.selectByPrimaryKey(authUserInfo.getEntityId());
        HashMap<String, String> messageContent = new HashMap<>(2);
        messageContent.put("adminUser", authUserInfo.getAccount());
        messageContent.put("entityName", entity.getEntityName());
        messageContent.put("freezeInfo", "失败");
        //冻结或解冻成功发送成功消息
        if (result) {
            if (isEnable) {
                sysOssMessageService.sendOssMessage(userSid, messageContent, NotificationConsts.ConsoleMsg.AccountMsg.TENANT_HAND_ENABLE, null, entity.getEntityId());
            } else {
                messageContent.put("freezeInfo", "成功");
                sysOssMessageService.sendOssMessage(userSid, messageContent, NotificationConsts.ConsoleMsg.AccountMsg.TENANT_HAND_FROZEN, AccountMsg.BSSMGT_ACCOUNT_MAN_FREEZE, entity.getEntityId());
            }
        } else {
            sysOssMessageService.sendOssMessage(userSid, messageContent, null, AccountMsg.BSSMGT_ACCOUNT_MAN_FREEZE, entity.getEntityId());
        }
    }

    /**
     * 用户跳转控制台增加日志
     *
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = AuthModule.CB.CB1902 + AuthModule.COMMA + AuthModule.CB.CB1903 + AuthModule.COMMA + AuthModule.CB.CB1904 + AuthModule.COMMA + AuthModule.CB.CB1915)
    @ApiOperation(httpMethod = "POST", value = "用户跳转控制台增加日志")
    @PostMapping("/add/log")
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "'跳转HCSO控制台'", resource = OperationResourceEnum.CONSOLE, tagNameUs ="'jump'")
    public RestResult addLog() {
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        User userById = userService.findUserById(authUser.getUserSid());
        if (!CertificationStatusEnum.AUTHSUCCEED.getStatus().equals(userById.getCertificationStatus())) {
            Org org = orgService.selectByPrimaryKey(authUser.getOrgSid());
            if (!CertificationStatusEnum.AUTHSUCCEED.getStatus().equals(org.getCertificationStatus())) {
                throw new cn.com.cloudstar.rightcloud.common.exception.BizException(cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd.AUTHORIZE_FAILURE));
            }
        }
        return new RestResult(Status.SUCCESS, null);
    }


    /**
     * 访问密钥创建
     *
     * @param request 请求
     *
     * @return {@link RestResult}
     */
    @ApiOperation("访问密钥创建")
    @PostMapping("/accesskey/create")
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "'访问密钥'", bizId = "#request.orgSid", resource = OperationResourceEnum.ACCESSKEYCREATE, tagNameUs ="'Access key'")
    @Idempotent
    @AuthorizeOss(action = CB.CB1911)
    @DataPermission(resource = OperationResourceEnum.ACCESSKEYCREATE,bizId = "#request.orgSid")
    public RestResult resUserCredentialsCreate(@RequestBody @Valid AccessKeyRequest request) {
        String orgSid = request.getOrgSid();
        if (StringUtils.isBlank(orgSid) || !StringUtil.isNumeric(orgSid)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAMS_NOT_LEGALITY));
        }

        Map<String, String> result = new HashMap();
        result.put("id", userService.createIamAccessKey(Long.valueOf(orgSid), request.getDescription()));

        return new RestResult(result);
    }

    /**
     * 访问密钥一览
     *
     * @param orgSid org sid
     *
     * @return {@link List}<{@link ResUserCredentialResponse}>
     */
    @DataPermission(resource = OperationResourceEnum.ACCESSKEYINFO, bizId = "#orgSid")
    @ApiOperation("访问密钥一览")
    @GetMapping("/accesskey/query/{orgSid}")
    @AuthorizeOss(action = CB.CB1911)
    public List<ResUserCredentialResponse> resUserCredentialsList(@PathVariable("orgSid") Long orgSid) {
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (authUser == null) {
            throw new BizException(RestConst.HttpConst.Unauthorized.getType());
        }

        // 获取IAM子账号AKSK信息
        cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria criteria = new cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria();
        criteria.put("userSid", authUser.getUserSid());
        List<ResUserCredential> result = resUserCredentialRemoteService.selectByParams(criteria);

        // 获取历史共享的 AKSK信息
        criteria.clear();
        criteria.put("orgSid", orgSid);
        List<ResUserCredential> result2 = resUserCredentialRemoteService.selectByParams(criteria);
        if (CollectionUtil.isNotEmpty(result2)) {
            result.addAll(result2);
        }

        List<ResUserCredentialResponse> list = new ArrayList<>();

        if (!CollectionUtils.isEmpty(result)) {
            result.forEach(res -> {
                res.setAk(CrytoUtilSimple.decrypt(res.getAk()));
                res.setSk(CrytoUtilSimple.decrypt(res.getSk()));
                ResUserCredentialResponse response = BeanConvertUtil.convert(res, ResUserCredentialResponse.class);
                response.setId(res.getId() + "");
                list.add(response);
            });
        }

        return list;
    }

    /**
     * 访问密钥删除
     *
     * @param request 请求
     *
     * @return {@link RestResult}
     */
    @DataPermission(resource = OperationResourceEnum.ACCESSKEYINFO, bizId = "#request.orgSid")
    @ApiOperation("访问密钥删除")
    @PostMapping("/accesskey/delete")
    @AuthorizeOss(action = CB.CB1911)
    @OperationLog(type = OperationTypeEnum.DELETE, tagName = "'访问密钥'", bizId = "#request.id", resource = OperationResourceEnum.ACCESSKEYDELETE, tagNameUs ="'Access key'")
    public RestResult resUserCredentialsDelete(@RequestBody @Valid AccessKeyRequest request) {
        //验证短信验证码
        String id = request.getId();
        if (StringUtils.isBlank(id) || !StringUtil.isNumeric(id)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAMS_NOT_LEGALITY));
        }
        CheckSmsCodeUtil.checkCode(null, request.getSmscode());
        userService.deleteIamAccessKey(Long.valueOf(id), true);
        return new RestResult(Status.SUCCESS);
    }

    /**
     * 访问密钥下载
     *
     * @param request 请求
     * @param response 响应
     */
    @DataPermission(resource = OperationResourceEnum.ACCESSKEYINFO, bizId = "#request.orgSid")
    @ApiOperation("访问密钥下载")
    @GetMapping("/accesskey/download")
    @AuthorizeOss(action = CB.CB1911)
    @OperationLog(type = OperationTypeEnum.EXPORT, tagName = "'访问密钥'", bizId = "#request.id", resource = OperationResourceEnum.ACCESSKEYDOWNLOAD, tagNameUs ="'Access key'")
    public void resUserCredentialsDownLoad(@Valid AccessKeyRequest request, HttpServletResponse response) {
        String id = request.getId();
        if (StringUtils.isBlank(id) || !StringUtil.isNumeric(id)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAMS_NOT_LEGALITY));
        }
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (!StringUtils.equals(authUserInfo.getOrgSid().toString(), request.getOrgSid())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }

        // 获取IAM子账号AKSK信息
        cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria criteria = new cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria();
        criteria.put("id", id);
        List<ResUserCredential> resUserCredentialList = resUserCredentialRemoteService.selectByParams(criteria);
        if (resUserCredentialList.stream().anyMatch(e -> !Objects.equals(authUserInfo.getOrgSid(), e.getOrgSid()))) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        if (CollectionUtil.isEmpty(resUserCredentialList) || Objects.isNull(resUserCredentialList.get(0))) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_447508325));
        }

        // 这里先判断如果是下载的是下载次数不是零的就报错
        resUserCredentialList.forEach(s -> {
            if (s.getDownloadNum() > 0) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1404346982));
            }
        });
        //转换导出类型
        List<IamAccessKeyCsv> accessKeyCsvList = CollectionUtil.newArrayList();
        resUserCredentialList.forEach(resUserCredential -> {
            IamAccessKeyCsv accessKeyCsv = new IamAccessKeyCsv();
            accessKeyCsv.setAk(CrytoUtilSimple.decrypt(resUserCredential.getAk()));
            accessKeyCsv.setSk(CrytoUtilSimple.decrypt(resUserCredential.getSk()));
            accessKeyCsvList.add(accessKeyCsv);
        });
        //导出到excel
        String destFileName = "Credentials.xlsx";
        OutputStream out = null;
        try {
            destFileName = new String(destFileName.getBytes(), StandardCharsets.UTF_8);
            //设置响应
            response.setHeader("Content-Disposition", "attachment;filename=" + destFileName);
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            out = response.getOutputStream();
            //将内容写入输出流并把缓存的内容全部发出去
            ExcelUtil.write()
                    .buildWriter(out, IamAccessKeyCsv.class)
                    .buildSheet("credentials")
                    .write(accessKeyCsvList)
                    .finish();
            out.flush();
            // 这里是下载成功后吧下载次数赋值为1
            resUserCredentialList.forEach(res -> {
                res.setDownloadNum(1);
                res.setSk("******");
                resUserCredentialRemoteService.updateByPrimaryKeySelective(res);
            });
        } catch (Exception e) {
            logger.error("用户表格导出异常", e);
        } finally {
            IOUtils.closeQuietly(out);
        }
    }

    /**
     * 刷新短信配置
     *
     * @return {@link RestResult}
     */
    @PutMapping("/refresh/2FA/smsConfig")
    @AuthorizeOss(action = ZK.ZK01)
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'短信通道状态'", resource = OperationResourceEnum.REFRESH_SMS_STATUS, tagNameUs ="'SMS channel status'")
    public RestResult refresh2FASmsConfig() {
        return userService.refresh2FASmsConfig();
    }

    /**
     * [INNER API] 双因子认证开关
     *
     * @param disabled 开关
     * @return {@link RestResult}
     */
    @RejectCall
    @PutMapping("/2FA/state/{disabled}")
    public RestResult disable2FA(@PathVariable Boolean disabled) {
        return userService.disabled2FA(disabled);
    }

    private void createKeyword(String keyword, Criteria criteria){
        List<String> statusList = new ArrayList<>();
        List<String> certificationStatusList = new ArrayList<>();
        if(StrUtil.isNotBlank(keyword)){
            criteria.put("keyword", keyword);
            if(StatusTypeEnum.ENABLE.getName().contains(keyword)){
                statusList.add(StatusTypeEnum.ENABLE.getCode());
            }
            if(StatusTypeEnum.DISABLE.getName().contains(keyword)){
                statusList.add(StatusTypeEnum.DISABLE.getCode());
            }
            if(StatusTypeEnum.REVIEW.getName().contains(keyword)){
                statusList.add(StatusTypeEnum.REVIEW.getCode());
            }
            if(StatusTypeEnum.LOCK.getName().contains(keyword)){
                statusList.add(StatusTypeEnum.LOCK.getCode());
            }
            if(StatusTypeEnum.REJECTED.getName().contains(keyword)){
                statusList.add(StatusTypeEnum.REJECTED.getCode());
            }
            if(CollectionUtil.isNotEmpty(statusList)){
                criteria.put("statusList",statusList);
            }
            if(StatusTypeEnum.NOAUTH.getName().contains(keyword)){
                certificationStatusList.add(StatusTypeEnum.NOAUTH.getCode());
            }
            if(StatusTypeEnum.AUTHING.getName().contains(keyword)){
                certificationStatusList.add(StatusTypeEnum.AUTHING.getCode());
            }
            if(StatusTypeEnum.AUTHSUCCEED.getName().contains(keyword)){
                certificationStatusList.add(StatusTypeEnum.AUTHSUCCEED.getCode());
            }
            if(StatusTypeEnum.AUTHFILED.getName().contains(keyword)){
                certificationStatusList.add(StatusTypeEnum.AUTHFILED.getCode());
            }
            if(CollectionUtil.isNotEmpty(certificationStatusList)){
                criteria.put("certificationStatusList",certificationStatusList);
            }
        }
    }
    /**
     * 异步导出账户信息列表
     *
     * @param request 请求
     */
    @AuthorizeOss(action = USER.EXPORT_ACCOUNT_INFO)
    @ApiOperation(httpMethod = "GET", value = "导出账户信息列表")
    @GetMapping("/async/exportUser")
    @OperationLog(type = OperationTypeEnum.EXPORT, tagName = "'账户信息列表'", tagNameUs ="'Account Information List'",
        resource = OperationResourceEnum.EXPORT_ACCOUNT_INFORMATION_LIST, param = "#request")
    @Idempotent
    public RestResult asyncExpertUserList(DescribeUserRequest request) {
        return userService.asyncExportUserAccount(request);
    }

    /**
     * [INNER API] 解冻权限设置
     *
     * @param userSid 用户ID
     */
    @RejectCall
    @PutMapping("/resource/permission/add/{userSid}")
    public void resourcePermissionAdd(@PathVariable Long userSid) {
        userService.resourcePermissionAdd(userSid);
    }

    /**
     * 拒绝企业认证
     *
     * @param refuseCompanyRequest 公司拒绝请求
     *
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "PUT", value = "拒绝企业认证", notes = "拒绝企业认证")
    @PutMapping("/refuseCompany")
    @Transactional
    @GlobalTransactional
    @AuthorizeOss(action = USER.CERTIFICATION_AUDIT)
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'机构名称'", bizId = "#refuseCompanyRequest.companyId", resource = OperationResourceEnum.REFUSE_COMPANY, tagNameUs ="'Name of Institution'")
    public RestResult refuseCompany(
            @RequestBody @LogParam("refuseCompanyRequest") @Valid RefuseCompanyRequest refuseCompanyRequest) {
        //修改 org 状态
        Org org = orgService.selectByPrimaryKey(refuseCompanyRequest.getCompanyId());
        if (Objects.isNull(org) || !"company".equals(org.getOrgType())) {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1087210640));
        }
        if (!org.getCertificationStatus().equals(CertificationStatus.AUTHING)) {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_754211191));
        }
        User user = userService.selectByPrimaryKey(org.getOwner());
        if (Objects.isNull(user)) {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_773060942));
        }
        org.setApplyStatus(user.getStatus());
        org.setCertificationStatus(CertificationStatus.AUTHFILED);
        org.setRemark(refuseCompanyRequest.getRemark());
        // 更新状态并清空认证数据
        orgService.updateByPrimaryKeySelectiveAndWipeData(org);
        if (Objects.nonNull(user.getEmail())) {
            Map<String, String> content = new HashMap<>();
            content.put("userAccount", user.getRealName());
            content.put("reason", refuseCompanyRequest.getRemark());
            businessNotificationService.refuseCompany(user, content);
        }
        webSocketSend(user.getUserSid());
        return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_789079806));
    }

    /**
     * 激活企业
     *
     * @param companyId 公司标识
     * @param refuseCompanyRequest 公司拒绝请求
     *
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "PUT", value = "激活企业", notes = "通过企业ID激活企业")
    @ApiImplicitParam(name = "companyId", value = "企业ID", paramType = "path", dataType = "long", required = true)
    @PutMapping("/{companyId}/activateCompany")
    @Transactional
    @GlobalTransactional
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'机构名称'", bizId = "#companyId", resource = OperationResourceEnum.ACTIVATE_COMPANY, tagNameUs ="'Name of Institution'")
    @AuthorizeOss(action = USER.AUDIT_USER)
    public RestResult activateCompany(@PathVariable("companyId") Long companyId,
                                      @RequestBody @Valid RefuseCompanyRequest refuseCompanyRequest) {
        Org org = orgService.selectByPrimaryKey(companyId);
        if (org == null || !"company".equals(org.getOrgType())) {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1425935868));
        }
        if (!CertificationStatus.AUTHING.equals(org.getCertificationStatus())) {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_754211191));
        }
        User user = userService.selectByPrimaryKey(org.getOwner());
        if (Objects.isNull(user)) {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_773060942));
        }
        if (Objects.isNull(refuseCompanyRequest.getRemark())) {
            refuseCompanyRequest.setRemark("通过");
        }
        org.setCertificationStatus(CertificationStatus.AUTHSUCCEED).setApplyStatus(user.getStatus()).
           setOrgSid(companyId).setRemark(refuseCompanyRequest.getRemark());
        int updRs = orgService.updateByPrimaryKeySelective(org);
        if (updRs == 1) {
            //需要将租户下面的没有进行实名认证的子用户包括自己没有实名认证的加入到ladp中
            Criteria criteria = new Criteria();
            criteria.put("orgSid", org.getOrgSid());
            List<User> users = userService.selectByParams(criteria);
            users.removeIf(userLadp -> !CertificationStatus.NOAUTH.equals(userLadp.getCertificationStatus()) ||
                    !UserStatus.AVAILABILITY.equals(userLadp.getStatus()));
            //子用户同步hpc资源信息
            userService.synHpcToLdap(users);
            if (Objects.nonNull(user.getEmail())) {
                Map<String, String> content = new HashMap<>(8);
                content.put("userAccount", user.getRealName());
                content.put("reason", refuseCompanyRequest.getRemark());
                businessNotificationService.activateCompany(user, content);
            }

            //租户存在为审核的子用户，统一审核
            criteria = new Criteria();
            criteria.put("parentSid", user.getUserSid());
            criteria.put("status", 2);
            List<User> subUser = userService.findAllUsers(criteria);
            if (CollectionUtil.isNotEmpty(subUser)) {
                logger.info("企业认证成功，审核未审核的子用户 parentSid: {}, size: {}", user.getUserSid(), subUser.size());
                executorService.execute(() -> {
                    subUser.forEach(sub -> {
                        Long userSid = sub.getUserSid();
                        try {
                            userService.userReview(userSid);
                        } catch (Exception e) {
                            logger.info("企业认证成功，审核未审核的子用户失败 account: {}, error: {}", sub.getAccount(), e.getMessage());
                        }
                    });
                });
            }
            webSocketSend(user.getUserSid());
            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1031369919));
        }
        else {
            webSocketSend(user.getUserSid());
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1031314500));
        }


    }

    /**
     * webSocket更新token
     * @param userSid userSid
     */
    private void webSocketSend(Long userSid) {
        ServerMsgPublisher.sendMsg("/topic/certificationStatus/" + userSid, true);
    }

    /**
     * 获取企业认证信息
     *
     * @param companyId 公司标识
     *
     * @return {@link DescribeCompanyDetailResponse}
     */
    @ApiOperation(httpMethod = "GET", value = "获取企业认证信息", notes = "通过企业id获取企业认证信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "companyId", value = "企业id", dataType = "long", paramType = "path", required = true)})
    @GetMapping("/getAuthCompanyInfoById/{companyId}")
    @AuthorizeOss(action = BQ03.BQ0303)
    public DescribeCompanyDetailResponse getAuthCompanyInfoById(@PathVariable Long companyId) {
        // 获取用户
        if (companyId == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_367330839));
        }
        Org org = orgService.selectByPrimaryKey(companyId);
        if (StringUtil.isNotEmpty(org.getLegalPersonCard())) {
            org.setLegalPersonCard(CrytoUtilSimple.decrypt(org.getLegalPersonCard()));
        }
        if (StringUtil.isNotEmpty(org.getEmpowerPersonCard())) {
            org.setEmpowerPersonCard(CrytoUtilSimple.decrypt(org.getEmpowerPersonCard()));
        }
        DescribeCompanyDetailResponse describeCompanyDetailResponse = BeanConvertUtil.convert(org,
                                                                                              DescribeCompanyDetailResponse.class);
        DesensitizationUtil.desensitization(describeCompanyDetailResponse);
        return describeCompanyDetailResponse;
    }

    /**
     * [INNER API] 通过企业id获取企业认证信息
     *
     * @param companyId 公司标识
     *
     * @return {@link DescribeCompanyDetailResponse}
     */
    @RejectCall
    @ApiOperation(httpMethod = "GET", value = "获取企业认证信息", notes = "通过企业id获取企业认证信息")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "companyId", value = "企业id", dataType = "long", paramType = "path", required = true)})
    @GetMapping("/getAuthCompanyInfoById/feign/{companyId}")
    public DescribeCompanyDetailResponse getAuthCompanyInfoByIdFeign(@PathVariable Long companyId) {
        // 获取用户
        if (companyId == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_367330839));
        }
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (ObjectUtils.isEmpty(authUserInfo)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_531007114));
        }
        if (!authUserInfo.getOrgSid().equals(companyId)) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        Org org = orgService.selectByPrimaryKey(companyId);
        if (StringUtil.isNotEmpty(org.getLegalPersonCard())) {
            org.setLegalPersonCard(CrytoUtilSimple.decrypt(org.getLegalPersonCard()));
        }
        if (StringUtil.isNotEmpty(org.getEmpowerPersonCard())) {
            org.setEmpowerPersonCard(CrytoUtilSimple.decrypt(org.getEmpowerPersonCard()));
        }
        DescribeCompanyDetailResponse describeCompanyDetailResponse = BeanConvertUtil.convert(org, DescribeCompanyDetailResponse.class);
        DesensitizationUtil.desensitization(describeCompanyDetailResponse);
        return describeCompanyDetailResponse;
    }



    /**
     * 提交企业认证
     *
     * @param request 请求
     *
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = AuthModule.COMMON.PUBLIC.C1.C104)
    @ApiOperation(httpMethod = "POST", value = "提交企业认证")
    @PostMapping("/companyAuth")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'机构名称'", bizId = "#request.orgSid", resource = OperationResourceEnum.SUBMIT_COMPANY_AUTH, tagNameUs ="'Name of Institution'")
    public RestResult<Object> companyAuth(@RequestBody @Valid AuthCompanyUserRequest request) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (authUserInfo == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_531007114));
        }



        //授权人认证，校验EmpowerPersonCard 的身份证信息
        if("authorizedPerson".equalsIgnoreCase(request.getEmpowerPersonCard())){
            if(!IDUtils.isIDNumber(request.getEmpowerPersonCard())){
                return new RestResult<>(Status.FAILURE, WebUtil.getMessage(MsgCd.COMPANY_AUTH_ERROR_13));
            }
        }
        if("legalRepresentative".equalsIgnoreCase(request.getLegalPersonCard())){
            if(!IDUtils.isIDNumber(request.getLegalPersonCard())){
                return new RestResult<>(Status.FAILURE, WebUtil.getMessage(MsgCd.COMPANY_AUTH_ERROR_13));
            }
        }
        User user1 = userMapper.selectByPrimaryKey(authUserInfo.getUserSid());
        if (ObjectUtil.isNull(user1) || null != user1.getParentSid()) {
            throw new BizException(WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        if (!request.getOrgSid().equals(authUserInfo.getOrgSid())  || null != user1.getParentSid()) {
            throw new BizException(WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        if (request == null) {
            return new RestResult<>(Status.FAILURE, WebUtil.getMessage(MsgCd.COMPANY_AUTH_ERROR_1));
        }
        if (request.getOrgSid() == null) {
            return new RestResult<>(Status.FAILURE, WebUtil.getMessage(MsgCd.COMPANY_AUTH_ERROR_2));
        }
        if (authUserInfo == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.THE_CURRENT_LOGGED_IN_USER_INFORMATION_WAS_NOT_OBTAINED));
        }

        if (CertificationStatus.AUTHING.equals(authUserInfo.getCertificationStatus())) {
            return new RestResult<>(Status.FAILURE, WebUtil.getMessage(MsgCd.COMPANY_AUTH_ERROR_3));
        }
        Org org = orgService.selectByPrimaryKey(request.getOrgSid());
        //子用户不能进行个人认证
        Criteria criteria = new Criteria();
        criteria.put("orgSid", org.getOrgSid());
        List<User> userList = userService.selectByParams(criteria);
        //获取租户
        Long userAdminId = userList.stream()
                .filter(user -> Objects.isNull(user.getParentSid()))
                .map(User::getUserSid)
                .findFirst()
                .orElse(0L);

        //去掉租户
        userList.removeIf(userLadp -> Objects.isNull(userLadp.getParentSid()));
        //判断子用户是否处于个人认证中
        userList.forEach(user -> {
            if (CertificationStatus.AUTHING.equals(user.getCertificationStatus())) {
                user.setCertificationStatus(CertificationStatus.NOAUTH);
                user.setAuthId(CrytoUtilSimple.decrypt(user.getAuthId()));
                if (!CCSPCacheUtil.ccspServiceOpen()) {
                    user.setAuthId(CrytoUtilSimple.encrypt(user.getAuthId()));
                }
                userMapper.updateAuthInfo(user);
            }
        });
        boolean b = userList.stream().anyMatch(userLadp -> userLadp.getUserSid().equals(authUserInfo.getUserSid()));
        if (b) {
            return new RestResult<>(Status.FAILURE, WebUtil.getMessage(MsgCd.COMPANY_AUTH_ERROR_4));
        }

        if (!cn.com.cloudstar.rightcloud.oss.common.constants.Constants.COMPANY.equals(org.getOrgType())) {
            return new RestResult<>(Status.FAILURE, WebUtil.getMessage(MsgCd.COMPANY_AUTH_ERROR_5));
        }
        if (CertificationStatus.AUTHSUCCEED.equals(org.getCertificationStatus())) {
            return new RestResult<>(Status.FAILURE, WebUtil.getMessage(MsgCd.COMPANY_AUTH_ERROR_6));
        }
        if (CertificationStatus.AUTHING.equals(org.getCertificationStatus())) {
            return new RestResult<>(Status.FAILURE, WebUtil.getMessage(MsgCd.COMPANY_AUTH_ERROR_7));
        }
        if (StringUtil.isEmpty(request.getBusinessLicenseUrl())) {
            return new RestResult<>(Status.FAILURE, WebUtil.getMessage(MsgCd.COMPANY_AUTH_ERROR_8));
        }
        checkFile(request.getBusinessLicenseUrl(), authUserInfo);

        if (StringUtil.isEmpty(request.getIdentityType())) {
            return new RestResult<>(Status.FAILURE, WebUtil.getMessage(MsgCd.COMPANY_AUTH_ERROR_9));
        } else {
            if (StringUtil.isEmpty(request.getLegalPerson())) {
                return new RestResult<>(Status.FAILURE, WebUtil.getMessage(MsgCd.COMPANY_AUTH_ERROR_10));
            }
            if (StringUtil.isNotEmpty(request.getEmpowerPerson()) && !request.getEmpowerPerson()
                                                                             .matches(
                                                                                     "^[a-zA-Z\\u4E00-\\u9FA5.]{2,16}$")) {
                return new RestResult<>(Status.FAILURE, WebUtil.getMessage(MsgCd.COMPANY_AUTH_ERROR_19));
            }
            if (IdentityTypeStatus.AUTHPERSION.equals(request.getIdentityType())) {
                if (StringUtil.isEmpty(request.getEmpowerPerson())) {
                    return new RestResult<>(Status.FAILURE, WebUtil.getMessage(MsgCd.COMPANY_AUTH_ERROR_11));
                }
                if (StringUtil.isEmpty(request.getEmpowerPersonCard())) {
                    return new RestResult<>(Status.FAILURE, WebUtil.getMessage(MsgCd.COMPANY_AUTH_ERROR_12));
                } else {
                    //校验身份证信息是否合规
                    boolean idNumber = IDUtils.isIDNumber(request.getEmpowerPersonCard());
                    if (!idNumber) {
                        return new RestResult<>(Status.FAILURE, WebUtil.getMessage(MsgCd.COMPANY_AUTH_ERROR_13));
                    }
                    if (StringUtil.isEmpty(request.getEmpowerIdCardFront()) || StringUtil.isEmpty(
                            request.getEmpowerIdCardReverse())) {
                        return new RestResult<>(Status.FAILURE, WebUtil.getMessage(MsgCd.COMPANY_AUTH_ERROR_14));
                    }
                    if(!IDUtils.isAdult(request.getEmpowerPersonCard())){
                        return new RestResult<>(Status.FAILURE, WebUtil.getMessage(MsgCd.COMPANY_AUTH_ERROR_20));
                    }
                    if (StringUtil.isEmpty(request.getPowerAttorneyUrl())) {
                        return new RestResult<>(Status.FAILURE, WebUtil.getMessage(MsgCd.COMPANY_AUTH_ERROR_15));
                    }
                    checkFile(request.getPowerAttorneyUrl(), authUserInfo);
                    checkFile(request.getEmpowerIdCardFront(), authUserInfo);
                    checkFile(request.getEmpowerIdCardReverse(), authUserInfo);
                    org.setEmpowerPerson(request.getEmpowerPerson());
                    org.setEmpowerPersonCard(CrytoUtilSimple.encrypt(request.getEmpowerPersonCard()));
                    org.setEmpowerIdCardFront(request.getEmpowerIdCardFront());
                    org.setEmpowerIdCardReverse(request.getEmpowerIdCardReverse());
                    org.setPowerAttorneyUrl(request.getPowerAttorneyUrl());
                }
            } else if (IdentityTypeStatus.LEGPERSION.equals(request.getIdentityType())) {
                //校验身份证信息是否合规
                boolean idNumber = IDUtils.isIDNumber(request.getLegalPersonCard());
                if (!idNumber) {
                    return new RestResult<>(Status.FAILURE, WebUtil.getMessage(MsgCd.COMPANY_AUTH_ERROR_16));
                }
                if(!IDUtils.isAdult(request.getLegalPersonCard())){
                    return new RestResult<>(Status.FAILURE, WebUtil.getMessage(MsgCd.COMPANY_AUTH_ERROR_20));
                }
                if (StringUtil.isEmpty(request.getLegalIdCardFront()) || StringUtil.isEmpty(
                        request.getLegalIdCardReverse())) {
                    return new RestResult<>(Status.FAILURE, WebUtil.getMessage(MsgCd.COMPANY_AUTH_ERROR_17));
                }
                checkFile(request.getLegalIdCardFront(), authUserInfo);
                checkFile(request.getLegalIdCardReverse(), authUserInfo);
                org.setLegalPersonCard(CrytoUtilSimple.encrypt(request.getLegalPersonCard()));
                org.setLegalIdCardFront(request.getLegalIdCardFront());
                org.setLegalIdCardReverse(request.getLegalIdCardReverse());
            }
        }
        org.setLegalPerson(request.getLegalPerson());
        org.setIdentityType(request.getIdentityType());
        org.setBusinessLicenseType(request.getBusinessLicenseType());
        org.setBusinessLicenseUrl(request.getBusinessLicenseUrl());
        org.setCertificationStatus(CertificationStatus.AUTHING);
        org.setApplyStatus(authUserInfo.getStatus());
        org.setUpdatedDt(new Date());
        org.setUpdatedBy(authUserInfo.getRealName());
        //清空上一次的审批意见
        org.setRemark("");
        // 实名认证推送站内信
        List<User> users = userService.findAdminstrators();
        Map<String, String> content = Maps.newHashMap();
        String property = PropertiesUtil.getProperty("rightcloud.mgt.url");
        content.put("url",property + "#/appbssorg/authent?orgSid=" + authUserInfo.getOrgSid());

        LinkedList<Long> adminUserList = users.stream()
                .map(User::getUserSid)
                .collect(Collectors.toCollection(LinkedList::new));

        //用户创建的工单 通知运营管理员
        List<BizBillingAccount> bizBillingAccounts = bizBillingAccountService.getBizBillingAccountsByAdminSid(
                userAdminId);
        if (CollectionUtil.isNotEmpty(bizBillingAccounts)) {
            bizBillingAccounts.forEach(account -> {
                businessNotificationService.sendCommonPlatformNotification(userAdminId,
                        NotificationConsts.PlatformMsg.AccountMsg.BSSMGT_VERIFY_ENTERPRISE,
                        content, account.getEntityId(),
                        adminUserList);
            });
        }

        orgService.updateByPrimaryKey(org);
        return new RestResult<>(Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_781365859));
    }

    private void checkFile(String fileNum, AuthUser authUserInfo) {
        SysMFilePath sysMFilePath = filePathMapper.selectByPrimaryKey(fileNum);
        if (Objects.isNull(sysMFilePath) ) {
            throw new BizException(WebUtil.getMessage(MsgCd.FILE_NOT_ERROR));
        }
        if (!authUserInfo.getUserSid().equals(sysMFilePath.getAccountId())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ULTRA_VIRES_OPERATE));
        }
    }


    /**
     * 下载身份认证图片(账户认证审核)
     *
     * @param num 文件编号
     * @param response 响应
     */
    @GetMapping("/file")
    @ResponseBody
    @ApiOperation("下载文件")
    @AuthorizeOss(action = AuthModule.BQ.BQ03.BQ0303 + AuthModule.COMMA + AuthModule.COMMON.PUBLIC.C1.C104)
    @OperationLog(type = OperationTypeEnum.EXPORT, tagName = "'认证图片'", bizId = "#num", resource = OperationResourceEnum.AUTH_DOWNLOAD_FILE, tagNameUs ="'Certification pictures'")
    public void downloadFile(
            @ApiParam("文件路径") @RequestParam("num") String num,
            HttpServletResponse response) {
        if(!StringUtils.isNotBlank(num)){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1497645912));
        }
        //下载的minio地址
        String urlPath = "";
        //解密密码(如果是走filepath表；则需要这个解密)
        String filePasswod = "";
        //判断是否带后缀
        if (!ObjectUtils.isEmpty(FileNameUtil.getSuffix(num))) {
            //带后缀 直接下载
            urlPath = num;
        } else {
            Pattern NUMBER_PATTERN = Pattern.compile("^[a-zA-Z0-9]*$");
            Matcher m = NUMBER_PATTERN.matcher(num);
            if (!m.matches()) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2016849060));
            }
            //不带后缀  直接先走file path表；
            SysMFilePath filePath = sysMFilePathMapper.selectByPrimaryKey(num);
            if (ObjectUtils.isEmpty(filePath)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1138191507));
            }
            AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
            if (UserType.DISTRIBUTOR_USER.equals(authUserInfo.getUserType()) && Objects.nonNull(filePath.getAccountId())) {
                User user = userMapper.selectByPrimaryKey(filePath.getAccountId());
                List<Long> orgSids = orgService.selectAllSidsByOrgSid(authUserInfo.getOrgSid());
                if (!orgSids.contains(user.getOrgSid())) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1138191507));
                }
            }

            //进行解密；
            urlPath = filePath.getFilePath();
            filePasswod = CrytoUtilSimple.decrypt(filePath.getCompressPassword());
        }

        //先进行统一解密
        String path = CrytoUtilSimple.decrypt(urlPath);
        StorageResult result = storageService.getFile(path);

        if (ObjectUtils.isEmpty(result)) {
            throw new cn.com.cloudstar.rightcloud.oss.common.exception.BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1138191507));
        }

        Map<String, InputStream> fileMap = new HashMap<>();
        InputStream in = result.getInputStream();
        try {
            //进行判断 文件下载的后缀是否是zip如果是；则需要进一步解密；
            if (!ObjectUtils.isEmpty(FileNameUtil.getSuffix(path))) {
                //截取最后3位是否是.zip
                String strsub = path.substring(path.length() - 3);
                //一个参数表示截取传递的序号之后的部分
                //如果是zip；就直接走解压
                if (!ObjectUtils.isEmpty(FileNameUtil.getSuffix(path)) && "zip".equals(strsub)) {
                    //解压
                    fileMap = ZipUtil.decompress(result.getInputStream(), filePasswod);
                    Map.Entry<String, InputStream> file = fileMap.entrySet().iterator().next();
                    FileUtil.fileDownload(response, file.getValue(), file.getKey());
                } else {
                    FileUtil.fileDownload(response, in, result.getFileName());
                }
            } else {
                FileUtil.fileDownload(response, in, result.getFileName());
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        } finally {
            fileMap.forEach((key, val) -> {
                IOUtils.closeQuietly(val);
            });
            IOUtils.closeQuietly(in);
        }
    }

    /**
     * 下载身份认证图片(账户认证审核)
     *
     * @param num 文件编号
     * @param response 响应
     */
    @GetMapping("/child/file")
    @ResponseBody
    @ApiOperation("下载文件")
    @OperationLog(type = OperationTypeEnum.EXPORT, tagName = "'身份认证图片'", bizId = "#num", resource = OperationResourceEnum.AUTH_DOWNLOAD_FILE, tagNameUs ="'Identity authentication picture'")
    @AuthorizeOss(action = AuthModule.BQ.BQ03.BQ030402)
    public void downloadFileCopy(
            @ApiParam("文件路径") @RequestParam("num") String num,
            HttpServletResponse response) {
        //下载的minio地址
        String urlPath = "";
        //解密密码(如果是走filepath表；则需要这个解密)
        String filePasswod = "";
        //判断是否带后缀
        if (!ObjectUtils.isEmpty(FileNameUtil.getSuffix(num))) {
            //带后缀 直接下载
            urlPath = num;
        } else {
            //不带后缀  直接先走file path表；
            SysMFilePath filePath = sysMFilePathMapper.selectByPrimaryKey(num);
            if (ObjectUtils.isEmpty(filePath)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1138191507));
            }
            AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
            if (UserType.DISTRIBUTOR_USER.equals(authUserInfo.getUserType()) && Objects.nonNull(filePath.getAccountId())) {
                User user = userMapper.selectByPrimaryKey(filePath.getAccountId());
                List<Long> orgSids = orgService.selectAllSidsByOrgSid(authUserInfo.getOrgSid());
                if (!orgSids.contains(user.getOrgSid())) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1138191507));
                }
            }
            //进行解密；
            urlPath = filePath.getFilePath();
            filePasswod = CrytoUtilSimple.decrypt(filePath.getCompressPassword());
        }

        //先进行统一解密
        String path = CrytoUtilSimple.decrypt(urlPath);
        StorageResult result = storageService.getFile(path);

        if (ObjectUtils.isEmpty(result)) {
            throw new cn.com.cloudstar.rightcloud.oss.common.exception.BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1138191507));
        }

        InputStream in = result.getInputStream();
        Map<String, InputStream> fileMap = new HashMap<>();
        try {
            //进行判断 文件下载的后缀是否是zip如果是；则需要进一步解密；
            if (!ObjectUtils.isEmpty(FileNameUtil.getSuffix(path))) {
                //截取最后3位是否是.zip
                String strsub = path.substring(path.length() - 3);
                //一个参数表示截取传递的序号之后的部分
                //如果是zip；就直接走解压
                if (!ObjectUtils.isEmpty(FileNameUtil.getSuffix(path)) && "zip".equals(strsub)) {
                    //解压
                    fileMap = ZipUtil.decompress(result.getInputStream(), filePasswod);
                    Map.Entry<String, InputStream> file = fileMap.entrySet().iterator().next();
                    FileUtil.fileDownload(response, file.getValue(), file.getKey());
                } else {
                    FileUtil.fileDownload(response, in, result.getFileName());
                }
            } else {
                FileUtil.fileDownload(response, in, result.getFileName());
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        } finally {
            fileMap.forEach((key, val) -> {
                IOUtils.closeQuietly(val);
            });
            IOUtils.closeQuietly(in);
        }
    }

    /**
     * 获取上传模板的文件名
     *
     * @param configKey 系统配置key
     *
     * @return {@link String}
     */
    @ApiOperation("获取上传模板的文件名")
    @GetMapping("/companyAuth/getFileName")
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "'基础配置文件'", resource = OperationResourceEnum.GET_FILE_NAME, tagNameUs ="'Base profile'")
    @AuthorizeOss(action = ZF.ZF0101)
    public String getFileName(String configKey) {
        Criteria criteria = new Criteria();
        if (StringUtil.isNotBlank(configKey)) {
            criteria.put("configKey", configKey);
        }
        String fileName = "";
        List<SysConfig> sysconfigList = sysConfigService.selectByParams(criteria);
        if (sysconfigList.size() > 0 && Objects.nonNull(
                sysconfigList.get(0).getConfigValue())) {
            fileName = sysconfigList.get(0).getConfigValue();
            String decrypt = StringUtil.isEmpty(CrytoUtilSimple.decrypt(fileName)) ? null
                    : CrytoUtilSimple.decrypt(fileName).trim();
            if (decrypt == null) {
                return "";
            }
            fileName = decrypt.substring(decrypt.lastIndexOf('/') + 1);
        }
        return fileName;
    }

    /**
     * 创建子用户
     *
     * @param request 请求
     *
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = (AuthModule.BQ.BQ03.BQ03 + StrUtil.COMMA + AuthModule.BQ.BQ030403))
    @PutMapping("/createSubUsers")
    @ApiOperation("批量创建子用户")
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "'子用户信息'", tagNameUs ="'Child User Information'",
            resource = OperationResourceEnum.ADD_SUB_USER, param = "#request")
    @DataPermission(resource = OperationResourceEnum.ADD_SUB_USER , bizId = "#request.parentUserSid")
    public RestResult createSubUsers(@RequestBody @Valid CreateUsersRequest request) {
        if(Objects.isNull(request.getParentUserSid())){
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2074391480));
        }
        List<Long>  adminSids = new ArrayList<>();
        List<Role> currentRoleList =  roleService.selectRoleByUserSid(RequestContextUtil.getAuthUserInfo().getUserSid());
        List<cn.com.cloudstar.rightcloud.common.pojo.Role> convertsRolesList = BeanConvertUtil.convert(currentRoleList, cn.com.cloudstar.rightcloud.common.pojo.Role.class);
        String maxScope = DataScopeUtil.getMaxDataScope(convertsRolesList);
        if(DataScopeEnum.DATA_SCOPE_COMPANY.getScope().equals(maxScope)){
            Criteria bizBillingAccountCriteria = new Criteria();
            bizBillingAccountCriteria.put("entityId",RequestContextUtil.getEntityId());
            bizBillingAccountCriteria.put("salesmenId",RequestContextUtil.getAuthUserInfo().getUserSid());
            List<BizBillingAccount> accounts = bizBillingAccountMapper.selectByParams(bizBillingAccountCriteria);
            if(CollectionUtil.isNotEmpty(accounts)){
                for(BizBillingAccount account : accounts){
                    adminSids.add(account.getAdminSid());
                }
            }
            if(CollectionUtil.isNotEmpty(adminSids)){
                if(!adminSids.contains(request.getParentUserSid())){
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_805320749));
                }
            }else{
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1633845289));
            }
        }
        User parentUser = userService.findUserById(request.getParentUserSid());
        cn.com.cloudstar.rightcloud.oss.common.pojo.User authUser = AuthUtil.getAuthUser();
        if (Objects.isNull(authUser) || Objects.isNull(authUser.getUserSid())) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        //解决分销商纵向越权
        if(!Objects.isNull(authUser.getOrgSid()) && FOUR.equals(authUser.getUserType())){
            Org org = orgService.selectByPrimaryKey(parentUser.getOrgSid());
            if(!org.getTreePath().contains(String.valueOf(authUser.getOrgSid()))){
                throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
            }
        }

        if(!UserStatus.AVAILABILITY.equals(parentUser.getStatus())){
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1287360922));
        }
        try {
            RestResult restResult = BasicInfoUtil.replaceUserToInvoke(() -> userFeginService.createUsers(request),
                                                                      request.getParentUserSid());
            //日志记录处理数据脱敏
            for (CreateUserVO userVo : request.getUsers()) {
                userVo.setEmail(DataProcessingUtil.processing(userVo.getEmail(),"email"));
                userVo.setMobile(DataProcessingUtil.processing(userVo.getMobile(),"phone"));
            }
            request.setPassword(DataProcessingUtil.processing(request.getPassword(),"password"));
            return restResult;
        } catch (Exception e) {
            //日志记录处理数据脱敏
            for (CreateUserVO userVO : request.getUsers()) {
                userVO.setEmail(DataProcessingUtil.processing(userVO.getEmail(),"email"));
                userVO.setMobile(DataProcessingUtil.processing(userVO.getMobile(),"phone"));
            }
            request.setPassword(DataProcessingUtil.processing(request.getPassword(),"password"));
            if (e instanceof BizException) {
                e.printStackTrace();
                throw new BizException(e.getMessage());
            } else {
                e.printStackTrace();
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_609387497));
            }
        }

    }

    /**
     * 校核密码是否符合规则
     *
     * @param request 请求
     *
     * @return {@link RestResult}
     */
    @ApiOperation("校核密码是否符合规则")
    @PostMapping("/password_policy/valid")
    @Encrypt
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "'校核密码规则'", bizId = "#request.orgSid", resource = OperationResourceEnum.VALID_PASSWORD_POLICY, tagNameUs ="'Check password rules'")
    @AuthorizeOss(action = AuthModule.COMMON.PUBLIC.B1.B104 + "," + AuthModule.COMMON.PUBLIC.Z1.Z103)
    public RestResult validPasswordByPolicy(@RequestBody @Valid ValidPasswordPolicyRequest request) {
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (ObjectUtils.isEmpty(authUser)) {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_227093865));
        }
        if(request.getFirstLogin() == null){
            request.setFirstLogin(true);
        }

        String error = userService.validPasswordByPolicyToError(request.getPassword(), authUser.getOrgSid(),
                                                                request.getFirstLogin() ? null : authUser.getUserSid(), authUser.getParentSid(),
                                                                request.getFirstLogin());
        if (!ObjectUtils.isEmpty(error)) {
            return new RestResult(Status.FAILURE, error);
        }
        return new RestResult(Status.SUCCESS);
    }

    /**
     * 重置用户密码校核密码是否符合规则
     *
     * @param request 请求
     *
     * @return {@link RestResult}
     */
    @ApiOperation("重置用户密码校核密码是否符合规则")
    @PostMapping("/password_policy/resetValid")
    @Encrypt
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "'重置用户密码校核密码规则'", bizId = "#request.userSid", resource = OperationResourceEnum.VALID_PASSWORD_POLICY, tagNameUs ="'Reset User Password Check Password Rules'")
    @AuthorizeOss(action = AuthModule.COMMON.PUBLIC.B1.B104 + "," + AuthModule.COMMON.PUBLIC.Z1.Z103)
    public RestResult resetValidPassword(@RequestBody @Valid ResetValidPasswordRequest request) {
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (ObjectUtils.isEmpty(authUser)) {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_227093865));
        }
        User user = userMapper.selectByPrimaryKey(request.getUserSid());
        if (ObjectUtils.isEmpty(user)) {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1597545170));
        }
        if (user.getUserSid().equals(100L) || (StringUtils.equals(UserType.PLATFORM_USER, user.getUserType()) && Objects.nonNull(user.getParentSid()))) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }

        String error = userService.validPasswordByPolicyToError(request.getPassword(), authUser.getOrgSid(), request.getUserSid(), authUser.getParentSid(),false);
        if (!ObjectUtils.isEmpty(error)) {
            return new RestResult(Status.FAILURE, error);
        }
        return new RestResult(Status.SUCCESS);
    }


    /**
     * 创建用户校核密码是否符合规则
     *
     * @param request 请求
     *
     * @return {@link RestResult}
     */
    @ApiOperation("校核密码是否符合规则")
    @PostMapping("/password_policy/validCreateUser")
    @Encrypt
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "'校核密码规则'", bizId = "#request.orgSid", resource = OperationResourceEnum.VALID_PASSWORD_POLICY, tagNameUs ="'Check password rules'")
    @AuthorizeOss(action = AuthModule.COMMON.PUBLIC.B1.B104 + "," + AuthModule.COMMON.PUBLIC.Z1.Z103)
    public RestResult validPasswordPolicyCreateUser(@RequestBody @Valid ValidPasswordPolicyRequest request) {
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (ObjectUtils.isEmpty(authUser)) {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_227093865));
        }
        if(request.getFirstLogin() == null){
            request.setFirstLogin(true);
        }
        String error = userService.validPasswordByPolicyToError(request.getPassword(), authUser.getOrgSid(),
                                                                null, authUser.getParentSid(),
                                                                request.getFirstLogin());
        if (!ObjectUtils.isEmpty(error)) {
            return new RestResult(Status.FAILURE, error);
        }
        return new RestResult(Status.SUCCESS);
    }



    /**
     * 用户重新同意隐私协议
     *
     * @return {@link RestResult}
     */
    @ApiOperation("用户重新同意隐私协议")
    @PostMapping("/agree_policy")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'同意隐私协议'", resource = OperationResourceEnum.AGREE_PRIVACY_POLICY, tagNameUs ="'Agree to Privacy Agreement'")
    public RestResult agreePolicy() {
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        AssertUtil.requireNonBlank(authUser, "获取当前登录用户失败");
        User user = userMapper.selectByPrimaryKey(authUser.getUserSid());
        // 获取当前登录用户的权限id
        List<Long> roleIds = userRoleMapper.selectByParams(new Criteria().put("userSid", authUser.getUserSid()))
                                           .stream()
                                           .map(UserRole::getRoleSid)
                                           .collect(Collectors.toList());
        Set<Long> userRoleIds = new HashSet<>(
                Arrays.asList(UserRoleConstants.SYSTEM_ADMIN, UserRoleConstants.OPERATE_ADMIN, UserRoleConstants.NORMAL_OPERATE,
                              UserRoleConstants.ONLINE_SERVICE));
        // 检查用户是否属于特定的用户类型
        if (roleIds.stream().anyMatch(userRoleIds::contains)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
        }

        user.setPolicyAgreeSign(1);
        user.setPolicyAgreeTime(new Date());
        userService.updateByPrimaryKeySelective(user);
        return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_789079806));
    }


    /**
     * 用户拒绝隐私协议
     *
     * @return {@link RestResult}
     */
    @ApiOperation("用户拒绝隐私协议")
    @PostMapping("/reject_policy")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'拒绝隐私协议'", resource = OperationResourceEnum.REJECT_PRIVACY_POLICY, tagNameUs ="'Deny Privacy Agreement'")
    public RestResult rejectPolicy() {
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        AssertUtil.requireNonBlank(authUser, "获取当前登录用户失败");
        // 获取当前登录用户的权限id
        List<Long> roleIds = userRoleMapper.selectByParams(new Criteria().put("userSid", authUser.getUserSid()))
                                           .stream()
                                           .map(UserRole::getRoleSid)
                                           .collect(Collectors.toList());
        Set<Long> userRoleIds = new HashSet<>(
                Arrays.asList(UserRoleConstants.SYSTEM_ADMIN, UserRoleConstants.OPERATE_ADMIN, UserRoleConstants.NORMAL_OPERATE,
                              UserRoleConstants.ONLINE_SERVICE));
        // 检查用户是否属于特定的用户类型
        if (roleIds.stream().anyMatch(userRoleIds::contains)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
        }
        return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_789079806));
    }

    /**
     * 校验原密码
     *
     * @param request 请求
     *
     * @return {@link RestResult}
     */
    @ApiOperation("校验原密码")
    @PostMapping("/login")
    @Encrypt
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "#request.account", resource = OperationResourceEnum.CHECK_PASSWORD)
    @AuthorizeOss(action = AuthModule.COMMON.PUBLIC.B1.B109)
    public RestResult checkPassword(@RequestBody @Valid CheckPasswordRequest request) {
        if (userService.checkPassword(request)) {
            return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_832959066));
        }
        return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_406021703));
    }

    /**
     * 获取登录用户可关联得实体
     */
    @AuthorizeOss(action = AuthModule.ZC.ZC_COMMON)
    @ApiOperation(httpMethod = "POST", value = " 获取登录用户可关联得实体")
    @GetMapping("/sysBssEntity")
    public List<DescribeCodeFullResponse> sysBssEntity() {
        //可关联实体对象
        List<Code> codeList = sysBssEntityService.selectBssEntityCodeByParams();
        if (CollectionUtil.isEmpty(codeList)) {
            codeList = new ArrayList<>();
        }
        return BeanConvertUtil.convert(codeList, DescribeCodeFullResponse.class);
    }

    /**
     * 根据当前登录用户获取当前登录用户的实体
     *
     * @return {@link List}<{@link DescribeBssEntityResponse}>
     *
     * @since 2.4.1
     */
    @AuthorizeOss(action = AuthModule.BA.BA + AuthModule.COMMA + B1.B115 + AuthModule.COMMA + AuthModule.ZA)
    @ApiOperation(httpMethod = "POST", value = " 根据当前登录用户获取当前登录用户的实体")
    @GetMapping("/sysBssEntityByCurrentUser")
    public List<DescribeBssEntityResponse> sysBssEntityByCurrentUser() {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo(WebUtil.getRequest());
        if (ObjectUtils.isEmpty(authUserInfo)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_531007114));
        }
        if(UserType.PLATFORM_USER.equals(authUserInfo.getUserType()) && authUserInfo.getOrgSid() != null){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_465687352));
        }
        if (!ONE.equals(authUserInfo.getStatus())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2043115026));
        }
        //可关联实体对象
        List<Code> codeList = sysBssEntityService.sysBssEntityByCurrentUser(authUserInfo.getUserSid());
        if (CollectionUtil.isEmpty(codeList)) {
            codeList = new ArrayList<>();
        }
        return BeanConvertUtil.convert(codeList, DescribeBssEntityResponse.class);
    }

    /**
     * [INNER API] 校验原密码
     *
     * @param request 请求
     *
     * @return {@link RestResult}
     */
    @RejectCall
    @ApiOperation("校验原密码")
    @PostMapping("/login/feign")
    @Encrypt
    public RestResult checkPasswordByBss(@RequestBody @Valid CheckPasswordRequest request) {
        if (userService.checkPassword(request)) {
            return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_832959066));
        }
        return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_406021703));
    }

    /**
     * 根据用户id获取用户敏感数据
     *
     * @return RestResult
     */
    @AuthorizeOss(action = AuthModule.COMMON.PUBLIC.B1.B116)
    @ApiOperation(httpMethod = "GET", value = "获取用户个人隐私数据", notes = "根据用户id获取用户个人隐私数据")
    @GetMapping("/sensitive_data")
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "'用户个人隐私信息'", bizId = "#userSensitiveRequest.userId", resource = OperationResourceEnum.SELECT_USER_PERSONAL_INFORMATION, integrity = true, tagNameUs ="'User Personal Privacy Information'")
    public RestResult getSensitiveDataByUserId(@Valid UserSensitiveRequest userSensitiveRequest) {
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        String sessionId = Optional.ofNullable(WebUtil.getRequest()).map(request -> request.getHeader("sessionid")).orElse(null);
        if (ObjectUtils.isEmpty(authUser) || ObjectUtils.isEmpty(sessionId)) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        String result = "";
        //改为session作为key
        String key = TWICE_VALIDATE + DigestUtil.sha256Hex(sessionId);
        DateTime dateTime = new DateTime();
        String value = redisTemplate.opsForValue().get(key);
        DateTime parse = cn.hutool.core.date.DateUtil.parse(value);
        if (StrUtil.isBlank(value) || "null".equalsIgnoreCase(value)) {
            return new RestResult(RestConst.HttpConst.OK, Status.FAILURE, null, NEED_TWICE_VALIDATE);
        }
        if (StrUtil.isNotBlank(value) && !"null".equalsIgnoreCase(value)) {
            if (cn.hutool.core.date.DateUtil.between(parse, dateTime, DateUnit.MINUTE) >= AUTH_AGAIN_MINUTE) {
                return new RestResult(RestConst.HttpConst.OK, Status.FAILURE, null, NEED_TWICE_VALIDATE);
            }
        }
        UserSensitiveEnum userSensitiveEnum = UserSensitiveEnum.getEnum(userSensitiveRequest.getType());
        if (Objects.isNull(userSensitiveEnum)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }

        List<Long> adminSids = Lists.newArrayList();
        Set<Long> orgSids = new HashSet<>();
        Set<Long> accountSids = new HashSet<>();

        List<Role> roleList = roleService.selectRoleByUserSid(authUser.getUserSid());
        List<cn.com.cloudstar.rightcloud.common.pojo.Role> convertsRolesList = cn.com.cloudstar.rightcloud.common.util.BeanConvertUtil.convert(roleList, cn.com.cloudstar.rightcloud.common.pojo.Role.class);
        String maxScope = DataScopeUtil.getMaxDataScope(convertsRolesList);
        //运营实体下数据
        Criteria bizBillingAccountCriteria = new Criteria();
        if(RequestContextUtil.getEntityId() != null){
            bizBillingAccountCriteria.put("entityId", RequestContextUtil.getEntityId());
        }
        if (DataScopeEnum.DATA_SCOPE_COMPANY_AND_CHILD.getScope().equals(maxScope)){
            bizBillingAccountCriteria.put("distributorId",RequestContextUtil.getAuthUserInfo().getOrgSid());
            //当前组织-个人及关联客户数据
        } else if (DataScopeEnum.DATA_SCOPE_COMPANY.getScope().equals(maxScope)){
            bizBillingAccountCriteria.put("salesmenId", RequestContextUtil.getAuthUserInfo().getUserSid());
        }
        List<BizBillingAccount> accounts = bizBillingAccountMapper.selectByParams(bizBillingAccountCriteria);
        if(CollectionUtil.isEmpty(accounts)){
            return new RestResult(result);
        }
        for(BizBillingAccount ac : accounts){
            adminSids.add(ac.getAdminSid());
            orgSids.add(ac.getOrgSid());
            accountSids.add(ac.getId());
        }
        //用户
        if (UserSensitiveEnum.USER.getKey().equals(userSensitiveRequest.getSensitiveType())) {
            User user = userService.selectByPrimaryKey(Long.valueOf(userSensitiveRequest.getUserId()));
            if (Objects.isNull(user)) {
                throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
            }
            if(!adminSids.contains(user.getUserSid()) && !orgSids.contains(user.getOrgSid())){
                throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
            }
            switch (userSensitiveEnum) {
                case MOBILE:
                    result = CrytoUtilSimple.decrypt(user.getMobile());
                    break;
                case EMAIL:
                    result = CrytoUtilSimple.decrypt(user.getEmail());
                    break;
                case REALNAME:
                    result = CrytoUtilSimple.decrypt(user.getRealName());
                    break;
                case ID_CARD:
                    result = CrytoUtilSimple.decrypt(user.getAuthId());
                    break;
                case ID_CARD_FRONT_PICTURE:
                    result = user.getIdCardFront();
                    break;
                case ID_CARD_REVERSE_PICTURE:
                    result = user.getIdCardReverse();
                    break;
                case ACCOUNT_NAME:
                    result = user.getAccount();
                    break;
                case ACCOUNT_AND_REAL_NAME:
                    result = user.getAccount();
                    if (StringUtils.isNotBlank(user.getRealName())) {
                        result = String.format("%s(%s)", user.getAccount(), user.getRealName());
                    }
                    break;
            }
        }
        //账户
        else if (UserSensitiveEnum.ACCOUNT.getKey().equals(userSensitiveRequest.getSensitiveType())) {
            BizInvoice bizInvoice = bizInvoiceMapper.selectByInvoiceSid(userSensitiveRequest.getUserId());
            if (ObjectUtils.isEmpty(bizInvoice)) {
                return new RestResult(result);
            }
            if (!accountSids.contains(Long.valueOf(bizInvoice.getAccountId()))) {
                throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
            }
            switch (userSensitiveEnum) {
                case REALNAME:
                    result = CrytoUtilSimple.decrypt(bizInvoice.getReceiver());
                    break;
                case MOBILE:
                    result = CrytoUtilSimple.decrypt(bizInvoice.getPhone());
                    break;
                case REGISTER_MOBILE:
                    result = CrytoUtilSimple.decrypt(bizInvoice.getRegisterPhone());
                    break;
                case EMAIL:
                    result = bizInvoice.getEmail();
                    break;
                case ADDRESS:
                    result = CrytoUtilSimple.decrypt(bizInvoice.getAddress());
                    break;
                case REGISTER_address:
                    result = CrytoUtilSimple.decrypt(bizInvoice.getRegisterAddress());
                    break;
                case BANK_ACCOUNT:
                    result = CrytoUtilSimple.decrypt(bizInvoice.getBankAccount());
                    break;
            }
        }
        //发票
        else if (UserSensitiveEnum.INVOICE.getKey().equals(userSensitiveRequest.getSensitiveType())){
            BizInvoice bizInvoice = bizInvoiceMapper.selectByInvoiceSid(userSensitiveRequest.getUserId());
            if (ObjectUtils.isEmpty(bizInvoice)) {
                return new RestResult(result);
            }
            if (!orgSids.contains(Long.valueOf(bizInvoice.getAccountId()))) {
                throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
            }
            switch (userSensitiveEnum) {
                case REALNAME:
                    result = bizInvoice.getReceiver();
                    break;
                case MOBILE:
                    result = CrytoUtilSimple.decrypt(bizInvoice.getPhone());
                    break;
                case REGISTER_MOBILE:
                    result = CrytoUtilSimple.decrypt(bizInvoice.getRegisterPhone());
                    break;
                case EMAIL:
                    result = bizInvoice.getEmail();
                    break;
                case ADDRESS:
                    result = CrytoUtilSimple.decrypt(bizInvoice.getAddress());
                    break;
                case REGISTER_address:
                    result = CrytoUtilSimple.decrypt(bizInvoice.getRegisterAddress());
                    break;
                case BANK_ACCOUNT:
                    result = CrytoUtilSimple.decrypt(bizInvoice.getBankAccount());
                    break;
            }
        }
        //分销商
        else if(UserSensitiveEnum.DISTRIBUTORS.getKey().equals(userSensitiveRequest.getSensitiveType())){
            BizDistributor bizDistributor = bizDistributorService.getById(Long.parseLong(userSensitiveRequest.getUserId()));
            if (ObjectUtils.isEmpty(bizDistributor)) {
                return new RestResult(result);
            }
            if (!DataScopeEnum.DATA_SCOPE_ALL.getScope().equals(maxScope) && !Objects.equals(bizDistributor.getId(), authUser.getOrgSid())){
                throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
            }
            switch (userSensitiveEnum) {
                case REALNAME:
                    result = CrytoUtilSimple.decrypt(bizDistributor.getContact());
                    break;
                case MOBILE:
                    result = CrytoUtilSimple.decrypt(bizDistributor.getMobile());
                    break;
                case ADDRESS:
                    result = bizDistributor.getAddress();
                    break;
            }
        }
        //组织
        else if (UserSensitiveEnum.ORG.getKey().equals(userSensitiveRequest.getSensitiveType())) {
            Org org = orgService.selectByPrimaryKey(Long.valueOf(userSensitiveRequest.getUserId()));
            if (ObjectUtils.isEmpty(org)) {
                return new RestResult(result);
            }
            if (!DataScopeEnum.DATA_SCOPE_ALL.getScope().equals(maxScope) && !Objects.equals(org.getOrgSid(), authUser.getOrgSid())){
                if (!orgSids.contains(org.getOrgSid())) {
                    throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
                }
            }
            switch (userSensitiveEnum) {
                case REALNAME:
                    result = org.getContactName();
                    break;
                case ADDRESS:
                    result = org.getAddress();
                    break;
            }
        }
        return new RestResult(result);
    }

    /**
     * 根据用户id获取用户敏感数据
     *
     * @return RestResult
     */
    @AuthorizeOss(action = AuthModule.COMMON.PUBLIC.Z1.Z105)
    @ApiOperation(httpMethod = "GET", value = "获取用户个人隐私数据", notes = "根据用户id获取用户个人隐私数据")
    @GetMapping("/sensitive_data/sys")
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "'用户个人隐私信息'", bizId = "#userSensitiveRequest.userId", resource = OperationResourceEnum.SELECT_USER_PERSONAL_INFORMATION, tagNameUs ="'User Personal Privacy Information'")
    public RestResult getSensitiveDataByUserIdSys(@Valid UserSensitiveRequest userSensitiveRequest) {
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        String sessionId = Optional.ofNullable(WebUtil.getRequest()).map(request -> request.getHeader("sessionid")).orElse(null);
        if (authUser == null || ObjectUtils.isEmpty(sessionId)) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        String result = "";

        String key = TWICE_VALIDATE + DigestUtil.sha256Hex(sessionId);
        DateTime dateTime = new DateTime();
        String value = redisTemplate.opsForValue().get(key);
        DateTime parse = cn.hutool.core.date.DateUtil.parse(value);
        if (StrUtil.isBlank(value) || "null".equalsIgnoreCase(value)) {
            return new RestResult(RestConst.HttpConst.OK, Status.FAILURE, null, NEED_TWICE_VALIDATE);
        }
        if (StrUtil.isNotBlank(value) && !"null".equalsIgnoreCase(value)) {
            if (cn.hutool.core.date.DateUtil.between(parse, dateTime, DateUnit.MINUTE) >= AUTH_AGAIN_MINUTE) {
                return new RestResult(RestConst.HttpConst.OK, Status.FAILURE, null, NEED_TWICE_VALIDATE);
            }
        }
        UserSensitiveEnum userSensitiveEnum = UserSensitiveEnum.getEnum(userSensitiveRequest.getType());
        if (UserSensitiveEnum.USER.getKey().equals(userSensitiveRequest.getSensitiveType())) {
            User user = userService.selectByPrimaryKey(Long.valueOf(userSensitiveRequest.getUserId()));
            if (ObjectUtils.isEmpty(user)) {
                return new RestResult(result);
            }
            switch (userSensitiveEnum) {
                case MOBILE:
                    result = user.getMobile();
                    break;
                case EMAIL:
                    result = user.getEmail();
                    break;
                case REALNAME:
                    result = user.getRealName();
                    break;
                case ID_CARD:
                    result = CrytoUtilSimple.decrypt(user.getAuthId());
                    break;
                case ID_CARD_FRONT_PICTURE:
                    result = user.getIdCardFront();
                    break;
                case ID_CARD_REVERSE_PICTURE:
                    result = user.getIdCardReverse();
                    break;
                default:
                    return new RestResult(result);
            }
        } else if (UserSensitiveEnum.ACCOUNT.getKey().equals(userSensitiveRequest.getSensitiveType())) {
            BizInvoice bizInvoice = bizInvoiceMapper.selectByInvoiceSid(userSensitiveRequest.getUserId());
            if (ObjectUtils.isEmpty(bizInvoice)) {
                return new RestResult(result);
            }
            switch (userSensitiveEnum) {
                case REALNAME:
                    result = bizInvoice.getReceiver();
                    break;
                case MOBILE:
                    result = bizInvoice.getPhone();
                    break;
                case REGISTER_MOBILE:
                    result = CrytoUtilSimple.decrypt(bizInvoice.getRegisterPhone());
                    break;
                case REGISTER_address:
                    result = CrytoUtilSimple.decrypt(bizInvoice.getRegisterAddress());
                    break;
                case EMAIL:
                    result = bizInvoice.getEmail();
                    break;
                case ADDRESS:
                    result = bizInvoice.getAddress();
                    break;
                case BANK_ACCOUNT:
                    result = CrytoUtilSimple.decrypt(bizInvoice.getBankAccount());
                    break;
                default:
                    return new RestResult(result);
            }

        }else if(UserSensitiveEnum.INVOICE.getKey().equals(userSensitiveRequest.getSensitiveType())){
            BizInvoice bizInvoice = bizInvoiceMapper.selectByInvoiceSid(userSensitiveRequest.getUserId());
            if (ObjectUtils.isEmpty(bizInvoice)) {
                return new RestResult(result);
            }
            switch (userSensitiveEnum) {
                case REALNAME:
                    result = bizInvoice.getReceiver();
                    break;
                case MOBILE:
                    result = CrytoUtilSimple.decrypt(bizInvoice.getPhone());
                    break;
                case REGISTER_MOBILE:
                    result = CrytoUtilSimple.decrypt(bizInvoice.getRegisterPhone());
                    break;
                case EMAIL:
                    result = bizInvoice.getEmail();
                    break;
                case ADDRESS:
                    result = CrytoUtilSimple.decrypt(bizInvoice.getAddress());
                    break;
                case REGISTER_address:
                    result = CrytoUtilSimple.decrypt(bizInvoice.getRegisterAddress());
                    break;
                case BANK_ACCOUNT:
                    result = CrytoUtilSimple.decrypt(bizInvoice.getBankAccount());
                    break;
                default:
                    return new RestResult(result);
            }

        }else if(UserSensitiveEnum.DISTRIBUTORS.getKey().equals(userSensitiveRequest.getSensitiveType())){
            BizDistributor bizDistributor = bizDistributorService.getById(Long.parseLong(userSensitiveRequest.getUserId()));
            if (ObjectUtils.isEmpty(bizDistributor)) {
                return new RestResult(result);
            }
            switch (userSensitiveEnum) {
                case REALNAME:
                    result = bizDistributor.getContact();
                    break;
                case MOBILE:
                    result = bizDistributor.getMobile();
                    break;
                case ADDRESS:
                    result = bizDistributor.getAddress();
                    break;
                default:
                    return new RestResult(result);
            }
        // 新增组织信息二次认证
        } else if (UserSensitiveEnum.ORG.getKey().equals(userSensitiveRequest.getSensitiveType())) {
            Org org = orgService.selectByPrimaryKey(Long.valueOf(userSensitiveRequest.getUserId()));
            if (ObjectUtils.isEmpty(org)) {
                return new RestResult(result);
            }
            switch (userSensitiveEnum) {
                case MOBILE:
                    result = org.getContactPhone();
                    break;
                default:
                    return new RestResult(result);
            }
        } else if (UserSensitiveEnum.SMS.getKey().equals(userSensitiveRequest.getSensitiveType())) {
            switch (userSensitiveEnum) {
                case SMS_HUAWEI_APP_KEY:
                    Criteria criteria = new Criteria();
                    criteria.put("configKey", SMS_HUAWEIYUN_APP_KEY);
                    List<SysConfig> codes = sysConfigMapper.selectByParams(criteria);
                    String code = Optional.ofNullable(codes).map(CollectionUtil::getFirst)
                            .map(SysConfig::getConfigValue).orElse(StringUtil.EMPTY);
                    result = CrytoUtilSimple.decrypt(code);
                    break;
                default:
                    return new RestResult(result);
            }
        } else {
            return new RestResult(result);
        }
        return new RestResult(result);
    }

    /**
     * [INNER API] 检查身份验证
     *
     * @param userSid 用户ID
     * @return {@link RestResult}
     */
    @GetMapping("/checkAuth/{userSid}")
    @RejectCall
    @ApiOperation(httpMethod = "GET", value = "通过用户ID校验是否认证", notes = "通过用户ID校验是否认证")
    public RestResult checkAuth(@PathVariable Long userSid) {
        RestResult result = userService.checkAuth(userSid);
        return result;
    }

    /**
     * 用户支付成功通知
     */
    @GetMapping("/notifySuccess/{userSid}")
    @ApiOperation(httpMethod = "GET", value = "用户支付成功通知", notes = "用户支付成功通知")
    @RejectCall
    public void notifySuccess(@PathVariable Long userSid) {
        ServerMsgPublisher.sendMsg("/topic/users/" + userSid + "/recharge", "RechargeSuccess");
    }
    /**
     * 按实体查找总代理商树
     * @since 2.4.1
     * @param entityId 实体ID
     * @return {@link List}<{@link DescribeEntityDistributorResponse}>
     */
    @AuthorizeOss(action = AuthModule.ZD.ZD06)
    @ApiOperation(httpMethod = "GET", value = "根据运营实体获取分销商树")
    @GetMapping("/treeByEntity")
    public List<DescribeEntityDistributorResponse> findDistributorsTreeByEntity(@RequestParam("entityId") Long entityId) {

        List<BizDistributor> result = bizDistributorMapper.findDistributorsTreeByEntity(entityId);
        List<TreeNode> nodes = TreeBuilder.buildTreeNode(result, "id",
                "name", "parentId", "treePath", "licenseUrl");
        return BeanConvertUtil.convert(nodes, DescribeEntityDistributorResponse.class);
    }

    /**
     * 重置管理员账号密码
     *
     * @param request 请求
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "PUT", value = "重置管理员账号密码")
    @PutMapping("/resetAdminName")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'admin重置口令及密码'", bizId = "#request.userSid", resource = OperationResourceEnum.RESET_ADMIN_ACCOUNT, tagNameUs ="'Reset passwords and passwords'")
    public RestResult resetAdminName(@Valid @RequestBody ResetAdminNameRequest request) {
        Long userSid=Long.valueOf(request.getUserSid());
        if(Objects.isNull(userSid)){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2061170872));
        }
        User user = this.userService.selectByPrimaryKey(userSid);
        // 安全校验：admin账号首次登陆,才允许被重置
        if(Objects.isNull(user) || !ADMIN_ACCOUNT.equals(user.getAccount())){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1762351419));
        }
        boolean FirstLogin = user.getLastLoginTime() != null ? false : true;
        String error = userService.validPasswordByPolicyToError(request.getPassword(), user.getOrgSid(),
                user.getUserSid(), user.getParentSid(),
                FirstLogin);
        if (!ObjectUtils.isEmpty(error)) {
            return new RestResult(Status.FAILURE, error);
        }
        userService.resetAdminName(user,request);
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_547114930), userSid);

    }


    /**
     * 检查运营管理员权限
     *
     * @param authUser 身份验证用户
     * @param userId   用户id
     */
    private void checkRole(AuthUser authUser, Long userId) {
        if (authUser.getUserSid().equals(userId)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_386463893));
        }
        List<UserRole> user1 = userRoleService.selectByParams(
                new Criteria().put("userSid", authUser.getUserSid()));
        List<UserRole> user2 = userRoleService.selectByParams(
                new Criteria().put("userSid", userId));
        // 同级越权问题
        if (user1.get(0).getRoleSid() == 306L) {
            // iam子用户
            User user = userService.selectByPrimaryKey(userId);
            if (!authUser.getOrgSid().equals(user.getOrgSid())){
                throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
            } else {
                // 主账号用户组
                List<Long> groupList1 =
                        userGroupMapper.selectByParams(new Criteria().put("userSid", authUser.getUserSid())).stream()
                                       .map(UserGroup::getGroupSid)
                                       .collect(Collectors.toList());
                // 子账号用户组
                List<Long> groupList2 =
                        userGroupMapper.selectByParams(new Criteria().put("userSid", user.getUserSid())).stream()
                                       .map(UserGroup::getGroupSid)
                                       .collect(Collectors.toList());
                if (groupList1.contains(1L) && groupList2.contains(1L)) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1382757922));
                }
            }
        } else {
            // 其他用户
            if (Objects.equals(user1.get(0).getRoleSid(), user2.get(0).getRoleSid())) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1156735588));
            }
        }

        if (user1.get(0).getRoleSid() > user2.get(0).getRoleSid()
                && !RoleEnum.OPERATION_USER.getRoleSid().equals(user2.get(0).getRoleSid())
                // 自定义角色不在判断范围内
                && user1.get(0).getRoleSid() <= 405
                && !RoleEnum.IAM_SUBUSER.getRoleSid().equals(user2.get(0).getRoleSid())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_443301334));
        }

        User user = userMapper.selectByPrimaryKey(userId);
        if (ObjectUtils.isEmpty(user)){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1757129758));
        }

        // 分销商用户
        if (!ObjectUtils.isEmpty(user.getOrgSid()) && "04".equals(authUser.getUserType())) {
            Org org = orgService.selectByPrimaryKey(user.getOrgSid());
            Optional.ofNullable(org).ifPresent(o->{
                if (o.getParentId() == null || (o.getParentId() != null && !o.getParentId().equals(authUser.getOrgSid()))){
                    throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
                }
            });
        }

    }


    /**
     * 更新元数据bash
     *
     * @return {@link RestResult}
     */
    public RestResult updateMetadataBash() {
        HashMap<String, Integer> result = userService.updateMetadataBash();
        return new RestResult(result);
    }

    private RestResult setUserStatus(Long userId, String status) {
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (ObjectUtils.isEmpty(authUser)){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_531007114));
        }
        if (userId == 100L) {
            BizException.e(WebUtil.getMessage(MsgCd.ERR_MSG_16));
        }
        User user = userService.selectByPrimaryKey(userId);
        if (ObjectUtils.isEmpty(user)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_867857480));
        }
        if (authUser.getUserSid().equals(userId)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_386463893));
        }

        // 运营管理员横向越权判断
        if (!"admin".equals(authUser.getAccount())) {
            checkRole(authUser, user.getUserSid());
        }
        if (!Arrays.asList("0", "1").contains(status)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAMS_NOT_LEGALITY));
        }
        if ("0".equals(status) && "0".equals(user.getStatus())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1686132067));
        }
        if ("1".equals(status) && "1".equals(user.getStatus())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2019516811));
        }
        // 如果账号的有效截止时间小于了当前时间那么就提示账号已过期
        if (!ObjectUtils.isEmpty(user.getEndTime()) && !ObjectUtils.isEmpty(user)) {
            if (user.getEndTime().before(new Date())) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_324593543));
            }
        }

        //这个判断需要根据运营和租户单独判断
        if (authUser.getRemark().equals(CONSOLE)) {
            List<Role> roles = roleMapper.findRolesByUserSid(authUser.getUserSid());
            if (!ObjectUtils.isEmpty(roles)) {
                Boolean result = roles.stream()
                                      .anyMatch(o -> o.getRoleSid().equals(RoleEnum.OPERATION_USER.getRoleSid()) || o.getRoleSid().equals(RoleEnum.IAM_SUBUSER.getRoleSid()));
                if (!result) {
                    throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
                }
            }
        }

        if (ObjectUtils.isEmpty(user)) {
            throw new BizException(RestConst.BizError.BAD_PARAM.getType());
        }
        //修改账户为子用户，则需要判断parentId是否是当前登录用户的id
        if (!ObjectUtils.isEmpty(authUser) && !("bss").equals(authUser.getRemark())) {
            if (ObjectUtils.isEmpty(authUser.getParentSid())) {
                //当前登录是管理员
                if (!ObjectUtils.isEmpty(authUser) && !user.getParentSid().equals(authUser.getUserSid())) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_9631134));
                }
            }else {
                //当前登录用户不是管理员
                if (!ObjectUtils.isEmpty(authUser) && !user.getParentSid().equals(authUser.getParentSid())) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_9631134));
                }
            }
        }
        Criteria criteria = new Criteria();
        criteria.put("userSid",userId);
        List<Long> roleSids = Arrays.asList(302L, 306L);
        criteria.put("roleSids", roleSids);
        int count = userRoleMapper.countByParams(criteria);
        logger.info("UserCtrl.setUserStatus count: {}", count);

        if (userService.checkCreateIamSubUser() && count > 0) {
            Long userSid = user.getUserSid();
            boolean enabled = "1".equals(status);
            boolean flag = AuthUtil.replaceUserToInvoke( () -> userService.updateSubUserEnabled(userSid, user.getOrgSid(), enabled), userSid);
            if (!flag) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1172658454) + (WebUtil.getMessage(MsgCd.ERR_MSG_BSS_49).equals(status) ? WebUtil.getMessage(MsgCd.ERR_MSG_BSS_671592109) : WebUtil.getMessage(MsgCd.ERR_MSG_BSS_956215323)));
            }
        }
        user.setStatus(status);
        iamRemoteService.updateUserStatus(BeanConvertUtil.convert(user, IamUser.class));
        userService.updateByPrimaryKeySelective(user);
        if (UserStatus.FORBIDDEN.equals(status)) {
            RequestContextUtil.removeUserCache(user.getAccount());
        }
        if (count > 0) {
            try {
                //同步ldap资源
                this.synHpcToLdap(userId, user.getOrgSid(), Long.parseLong(status), user.getParentSid() != null);
            } catch (Exception e) {
                logger.info("UserCtrl.setUserStatus 设置用户状态ldap同步失败-回滚iam子用户状态 error: {}", e.getMessage());
            }
        }
//        policyService.synHpcToLdap(user.getOrgSid() == null ? 0L : user.getOrgSid());
        return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_789079806));
    }

    /**
     * 【SINCE2.6.0】MA专属资源池冻结
     *
     * @param id ID
     *
     * @return {@link RestResult}
     */
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "'MA专属资源池'", tagNameUs ="'Exclusive resource pool'",
            resource = OperationResourceEnum.REFREEZE_RESOURCESTATUS, bizId = "#id")
    @PostMapping("/{id}/drp/refshDrpFrozen")
    @AuthorizeOss(action = AuthModule.COMMON.PUBLIC.B1.B106)
    public RestResult refshDrpFrozen(@PathVariable("id")  @DecimalMin(value = "0", inclusive = false) String id) {
        AuthUser userInfo = BasicInfoUtil.getCurrentUserInfo();
        if (userInfo == null || !FROM_BSS.equals(userInfo.getRemark())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ULTRA_VIRES_OPERATE));
        }
        List<Role> roles = roleMapper.findRolesByUserSid(userInfo.getUserSid());
        if(CollectionUtil.isNotEmpty(roles)){
            List<Long> roleUserIds = roles.stream().map(Role::getRoleSid).collect(Collectors.toList());
            if(!roleUserIds.contains(BssRoleEnum.OPERATION_ADMIN.getRoleSid())){
                throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
            }
        }else{
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        boolean b = userService.refshDrpFrozen(Long.valueOf(id));
        if(b){
            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1937183478));
        }else{
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1937238897));
        }

    }

    /**
     * 升级场景
     * @return RestResult
     */
    @GetMapping("/drp/drpFrozen")
    @RejectCall
    public RestResult DrpFrozen() {
       userService.drpFrozen();
       return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1701437625));

    }

    /**
     * 【SINCE2.6.0】IAM用户映射数
     *
     * @return {@link IamUserNumResponse}
     */
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "IAM用户映射数",resource = OperationResourceEnum.QUERY_IAM_USER_NUM, tagNameUs ="'Number of user mappings'")
    @GetMapping("/iamUserNum")
    @AuthorizeOss(action = AuthModule.COMMON.PUBLIC.Z1.Z101)
    public IamUserNumResponse iamUserNum() {
        return userService.getIamUserNum();
    }

    /**
     * 【SINCE2.6.0】重新映射IAM子用户
     *
     * @return {@link RestResult}
     */
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "重新映射IAM子用户",resource = OperationResourceEnum.REMAP_IAM_USER, tagNameUs ="'Remapping'")
    @PostMapping("/reMapIamUser")
    @AuthorizeOss(action = ZF.ZF0101)
    public RestResult reMapIamUser() {
        // 判断子用户是否开启SSO
        if (!userService.checkCreateIamSubUser()) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_20189350));
        }
        //设置同步锁
        Object value = redisTemplate.opsForValue().get(UserServiceImpl.REMAP_IAM_USER_KEY);
        if(value != null){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_328002523));
        }
        //异步
        CompletableFuture.runAsync(() -> userService.reMapIamUser());
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1308264509));
    }


    /**
     * 【SINCE2.6.0】obs权限隔离，同步委托(升级场景)
     */
    @PostMapping("/syncAuthAgency")
    @RejectCall
    public void syncAuthAgency() {
        log.info("访问授权开始初始化");
        if (!userService.checkCreateIamSubUser()) {
            return;
        }
        Criteria criteria = new Criteria();
        criteria.put("parentSidIsNull",true);
        List<User> userList = userMapper.selectSampleUser(criteria);

        for (User user : userList) {
            try {
                maRemoteService.syncAuthAgency(user.getUserSid());
            } catch (Exception e) {
                e.printStackTrace();
                log.info("访问授权同步失败:{}", user.getUserSid());
            }
        }
    }

    /**
     *  用户表和其他表敏感数据加密
     * @return void
     */
    @GetMapping("/syncUserInfoEncrypt")
//    @RejectCall
    public void syncUserInfoEncrypt() {

        if (CCSPCacheUtil.ccspServiceOpen()) {
            log.debug("系统已开启国密服务，不需要再次加密...");
            return;
        }
        log.info("敏感数据加密升级开始...");
        try {
            userSecurityService.syncUserInfoEncrypt();


            log.info("敏感数据加密升级结束...");

        } catch (Exception e) {

            log.error("敏感数据加密升级异常",e);

        }
    }


    @PutMapping("/user/status/api")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "用户名称",
            resource = OperationResourceEnum.UPDATE_USER_STATUS, bizId = "#userId", param = "#status")
    public RestResult setUserStatusByUser(@RequestBody @Valid UpdateUserStatus updateUserStatus) {
        return setUserStatus(updateUserStatus.getUserId(), updateUserStatus.getStatus());
    }

    /**
     * 添加企业用户OpenApi
     *
     * @param requestApi 请求
     */
    @PostMapping("/api")
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "系统控制台添加管理员用户", bizId = "#requestApi.account", resource = OperationResourceEnum.ADD_COMPANY_USER)
    @Encrypt
    public RestResult addCompanyUserApi(@RequestBody @Valid CreateCompanyUserApiRequest requestApi) {
        HttpServletRequest servletRequest = cn.com.cloudstar.rightcloud.common.util.RequestContextUtil.getRequest();
        String ak = servletRequest.getHeader("Access-Key");
        log.info("ak...........,{}", ak);
        if (StrUtil.isNotBlank(ak) && Boolean.parseBoolean(System.getenv("sm3_start"))) {
            String realName = CrytoUtilSimple.decrypt(requestApi.getRealName());
            String mobile = CrytoUtilSimple.decrypt(requestApi.getMobile());
            requestApi.setRealName(StrUtil.isNotBlank(realName) ? realName : requestApi.getRealName());
            log.info("realName...........,{}", realName);
            requestApi.setMobile(StrUtil.isNotBlank(mobile) ? mobile : requestApi.getMobile());
        }

        //校验用户名称 and 手机号
        this.checkRealNameAndPhone(requestApi.getRealName(), requestApi.getMobile());

        try {
            CreateCompanyUserRequest request = BeanConvertUtil.convert(requestApi, CreateCompanyUserRequest.class);
            Optional.ofNullable(request.getRoles()).ifPresent(roles -> {
                if (roles.stream().map(RoleSidVO::getRoleSid).anyMatch(r -> r == 100L)) {
                    throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED,
                                           WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
                }
            });
            if (request.getAccount().equals(request.getPassword())) {
                throw new BizException("账户名与密码不能保存一致");
            }

            //密码策略
            PasswordPolicyDTO policy = userService.buildPasswordPolicy(null, null);
            boolean isVlidError = true;
            if (Objects.isNull(request.getPassword())) {
                isVlidError = false;
                request.setPassword(PasswordUtil.generatRandomPassword(policy));
            }

            User user = BeanConvertUtil.convert(request, User.class);

            cn.com.cloudstar.rightcloud.oss.common.pojo.User authUser = AuthUtil.getAuthUser();
            if (authUser != null && authUser.getUserSid() == 100L) {
                //查询是否开启双因子
                SysConfig sysConfig = sysconfigService.selectByConfigKey("keycloak.admin.skip2FA");
                user.setSkip2FA(sysConfig.getConfigValue());
            }

            /*if (!Strings.isNullOrEmpty(request.getMobile())) {
                List<User> userList = this.userService.findUserByMobile(request.getMobile());
                if (!CollectionUtils.isEmpty(userList)) {
                    BizException.throwException(WebUtil.getMessage(MsgCd.ERROR_MSG_00012));
                }
            }*/
            /*if (!Strings.isNullOrEmpty(request.getEmail())) {
                List<User> userList = this.userService.findUserByEmail(request.getEmail());
                if (!CollectionUtils.isEmpty(userList)) {
                    BizException.throwException(WebUtil.getMessage(MsgCd.ERROR_MSG_00013));
                }
            }*/
            if (Objects.nonNull(request.getStartTime()) && Objects.nonNull(request.getEndTime())) {
                if (request.getStartTime().after(request.getEndTime())) {
                    BizException.throwException(WebUtil.getMessage(MsgCd.THE_END_TIME_SHOULD_BE_GREATER_THAN_THE_START_TIME));
                }
            }

            // 37584 开始时间大于当前时间 不算时分秒
            if (Objects.nonNull(request.getStartTime())
                    && new Date().compareTo(request.getStartTime()) >= 0) {
                BizException.throwException("开始时间应大于当前时间");
            }
            if (Objects.nonNull(request.getEndTime()) && request.getEndTime().before(new Date())) {
                BizException.throwException(WebUtil.getMessage(MsgCd.THE_END_TIME_SHOULD_BE_GREATER_THAN_THE_CURRENT_TIME));
            }

            // 检查企业是否与所选择的企业角色关联
            checkRoleLinkToOrg(request.getRoles(), request.getCompanyId());

            // 检查项目是否与所选择的项目角色关联
            checkRoleLinkToOrg(request.getProjectRoles(), request.getProjectId());
            user.setOrgSid(request.getCompanyId());
            user.setAuthType(USER_AUTH_TYPE.AUTH_TYPE_LOCAL);
            user.setEntityIds(request.getEntityIds());
            //是否重置密码，默认为false
//            user.setForceResetPwd(Convert.toBool(request.getForceResetPwd(), false).toString());
            user.setForceResetPwd("false");
            // 验证密码复杂度
            if (Objects.nonNull(request.getPassword()) && isVlidError) {
                String validError = userService.validPasswordByPolicyToError(request.getPassword(), user.getOrgSid(), null,
                                                                             user.getParentSid(), true);
                if (!ObjectUtils.isEmpty(validError)) {
                    throw new BizException(validError);
                }
            }
            // 放入未加密密码
            user.setPassword(user.getPassword());
            //修改密码有效期
            userService.setUserPasswordEndTime(user, policy);
            user.setUserPlatform("mgt");
            boolean addUserRs = userService.insertUser(user);
            if (addUserRs) {
                try {
                    //远程添加
                    user.setAdminFlag(true);
                    iamRemoteService.insertUser(
                            BeanConvertUtil.convert(user, IamUser.class));
                    //添加到 activity
                    if (Objects.nonNull(request.getRoles()) && !request.getRoles().isEmpty()) {
                        List<String> collect = request.getRoles()
                                                      .stream()
                                                      .filter(a -> Objects.nonNull(a.getRoleSid()))
                                                      .map(a -> a.getRoleSid().toString())
                                                      .collect(Collectors.toList());
                        processService.getCandidateGroups(collect);
                    }
                } catch (Exception e) {
                    cancelService.cancelUser(user.getUserSid());
                    // 回滚用户组
                    cancelService.cancelOrg(user.getOrgSid());
                    log.error("远程添加用户异常:", e);
                    throw new BizException(e.getMessage());
                }
                //日志记录处理铭感信息
                request.setEmail(DataProcessingUtil.processing(request.getEmail(), "email"));
                request.setMobile(DataProcessingUtil.processing(request.getMobile(), "phone"));
                request.setRealName(DataProcessingUtil.processing(request.getRealName(), "name"));
                request.setPassword(DataProcessingUtil.processing(request.getPassword(), "password"));
                HashMap<String, Object> hashMap = new HashMap<>(8);
                HashMap<String, Object> hashMapSub = new HashMap<>(8);
                hashMapSub.put("userId", user.getUserSid());
                hashMapSub.put("account", user.getAccount());
                hashMap.put("user", hashMapSub);
                return new RestResult(hashMap);
            }
            //日志记录处理铭感信息
            request.setEmail(DataProcessingUtil.processing(request.getEmail(), "email"));
            request.setMobile(DataProcessingUtil.processing(request.getMobile(), "phone"));
            request.setRealName(DataProcessingUtil.processing(request.getRealName(), "name"));
            request.setPassword(DataProcessingUtil.processing(request.getPassword(), "password"));
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERROR_OPERATE_FAILURE));
        } catch (Exception e) {
            if (e instanceof BizException) {
                e.printStackTrace();
                throw new BizException(e.getMessage());
            } else {
                e.printStackTrace();
                throw new BizException("服务器异常,请联系管理员");
            }
        }

    }


    /**
     * 校验手机号和用户名
     *
     * @param raleName 用户名称
     * @param mobile   手机号
     */
    private void checkRealNameAndPhone(String raleName, String mobile) {
        boolean matches = patternMobile.matcher(mobile).matches();
        boolean matches1 = patternMobile1.matcher(mobile).matches();
        if (!(matches || matches1)) {
            throw new BizException("请输入正确的手机号");
        }


        if (!patternRaleName.matcher(raleName).matches()) {
            throw new BizException("真实姓名长度为2-64个字符,并且不能包含特殊符号");
        }
    }

    /**
     * API更新企业用户(用户管理-编辑)
     *
     * @param requestApi 请求
     */
    @ApiOperation(httpMethod = "PUT", value = " 更新用户")
    @PutMapping("/api")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "用户",
            resource = OperationResourceEnum.UPDATE_USER, bizId = "#requestApi.refUserId", param = "#requestApi")
    public RestResult updateCompanyUserBySysApi(@RequestBody @Valid UpdateCompanyUserApiRequest requestApi) {
        try {
            UpdateCompanyUserRequest request = new UpdateCompanyUserRequest();
            HttpServletRequest servletRequest = cn.com.cloudstar.rightcloud.common.util.RequestContextUtil.getRequest();
            String ak = servletRequest.getHeader("Access-Key");
            log.info("ak...........,{}", ak);
            if (StrUtil.isNotBlank(ak) && Boolean.parseBoolean(System.getenv("sm3_start"))) {
                if (StringUtils.isNotEmpty(request.getRealName())) {
                    String realName = CrytoUtilSimple.decrypt(requestApi.getRealName());
                    requestApi.setRealName(StrUtil.isNotBlank(realName) ? realName : requestApi.getRealName());
                    log.info("realName...........,{}", realName);
                }
                if (StringUtils.isNotEmpty(request.getMobile())) {
                    String mobile = CrytoUtilSimple.decrypt(requestApi.getMobile());
                    requestApi.setMobile(StrUtil.isNotBlank(mobile) ? mobile : requestApi.getMobile());
                }
            }
            request.setUserSid(requestApi.getUserId());
            request.setEmail(requestApi.getEmail());
            request.setRefUserId(requestApi.getRefUserId());
            return updateUser(request);
        } catch (Exception e) {
            if (e instanceof BizException) {
                e.printStackTrace();
                throw new BizException(e.getMessage());
            } else {
                e.printStackTrace();
                throw new BizException("服务器异常,请联系管理员");
            }
        }
    }


    /**
     * 激活租户
     */
    @ApiOperation(httpMethod = "GET", value = "激活租户", notes = "通过租户ID激活")
    @ApiImplicitParam(name = "orgId", value = "租户ID", paramType = "path", dataType = "long", required = true)
    @PostMapping("/activation/{orgId}")
    public RightCloudResult activation(@PathVariable("orgId") Long orgId, @RequestBody ActivationForm activationForm) {
        return userService.activation(orgId, activationForm);
    }

    /**
     * 移除IAM身份提供商
     */
    @ApiOperation(httpMethod = "GET", value = "移除IAM身份提供商", notes = "通过租户ID移除")
    @ApiImplicitParam(name = "orgId", value = "租户ID", paramType = "path", dataType = "long", required = true)
    @PostMapping("/deleteIamIdentity/{orgId}")
    public RightCloudResult deleteIamIdentity(@PathVariable("orgId") Long orgId, @RequestBody ActivationForm activationForm) {
        return userService.deleteIamIdentity(orgId, activationForm);
    }


    /**
     * 创建用户组
     */
    @ApiOperation(httpMethod = "POST", value = "创建用户组", notes = "创建用户组")
    @ApiImplicitParam(name = "orgId", value = "租户ID", paramType = "path", dataType = "long", required = true)
    @PostMapping("/createUserGroup")
    public RightCloudResult createUserGroup(@RequestBody UserGroupCreate userGroupCreate) {
        return userService.createUserGroup(userGroupCreate);
    }

    /**
     * 删除用户组
     *
     * @param userGroupCreate
     * @return
     */
    @ApiOperation(httpMethod = "POST", value = "删除用户组", notes = "删除用户组")
    @ApiImplicitParam(name = "orgId", value = "租户ID", paramType = "path", dataType = "long", required = true)
    @PostMapping("/deleteUserGroup")
    public RightCloudResult deleteUserGroup(@RequestBody UserGroupCreate userGroupCreate) {
        return userService.deleteUserGroup(userGroupCreate);
    }

    /**
     * 修改用户组
     *
     * @param userGroupCreate
     * @return
     */
    @ApiOperation(httpMethod = "POST", value = "修改用户组", notes = "修改用户组")
    @ApiImplicitParam(name = "orgId", value = "租户ID", paramType = "path", dataType = "long", required = true)
    @PostMapping("/updateUserGroup")
    public RightCloudResult updateUserGroup(@RequestBody UserGroupCreate userGroupCreate) {
        return userService.updateUserGroup(userGroupCreate);
    }

    /**
     * 同步组织数据
     *
     * @param company
     * @return
     */
    @ApiOperation(httpMethod = "POST", value = "同步组织数据", notes = "同步组织数据")
    @ApiImplicitParam(name = "orgId", value = "租户ID", paramType = "path", dataType = "long", required = true)
    @PostMapping("/createOrg")
    public RightCloudResult createOrg(@RequestBody Company company) {
        return userService.createOrg(company);
    }

    @ApiOperation(httpMethod = "PUT", value = "修改组织数据", notes = "修改组织数据")
    @PutMapping("/updateOrg")
    public RightCloudResult updateOrg(@RequestBody Company company) {
        return userService.updateOrg(company);
    }

    @ApiOperation(httpMethod = "POST", value = "创建OBS子用户", notes = "创建OBS子用户")
    @PostMapping("/createIamUser")
    public ObsIamUserAK createIamUser(@RequestBody ObsIamUserCreate iamUserCreate) {
        return userService.createObsIamUser(iamUserCreate);
    }

    @ApiOperation(httpMethod = "POST", value = "删除OBS子用户", notes = "删除OBS子用户")
    @PostMapping("/deleteObsIamUser")
    public Boolean deleteObsIamUser(@RequestBody ObsIamUserCreate iamUserCreate){
        return userService.deleteObsIamUser(iamUserCreate);
    }


    @PostMapping("/selectAkByUser")
    public ObsIamUserAK selectAkByUser(@RequestBody ObsIamUserCreate iamUserCreate){
        return userService.selectAkByUser(iamUserCreate);
    }

    @PostMapping("/addUserByObsAdminGroup")
    public RightCloudResult addUserByObsAdminGroup(@RequestBody ObsIamUserCreate iamUserCreate){
        return userService.addUserByObsAdminGroup(iamUserCreate);
    }

    /**
     * 同步组织数据
     *
     * @param orgId
     * @return
     */
    @PostMapping("/deleteOrg/{orgId}")
    public RightCloudResult deleteOrg(@PathVariable("orgId") Long orgId) {
        return userService.deleteOrg(orgId);
    }

    @ApiOperation(httpMethod = "POST", value = "同步组织数据", notes = "同步组织数据")
    @ApiImplicitParam(name = "orgId", value = "租户ID", paramType = "path", dataType = "long", required = true)
    @PostMapping("/deleteUser")
    public RightCloudResult deleteUser(@RequestBody UserGroupCreate userGroupCreate) {
        return userService.deleteCmpUser(userGroupCreate);
    }

    @ApiOperation(httpMethod = "POST", value = "用户移除用户组", notes = "用户移除用户组")
    @ApiImplicitParam(name = "orgId", value = "租户ID", paramType = "path", dataType = "long", required = true)
    @PostMapping("/addUserByGroup")
    public RightCloudResult addUserByGroup(@RequestBody UserGroupCreate userGroupCreate) {
        return userService.addUserByGroup(userGroupCreate);
    }


    @GetMapping("getEcsConfig")
    public Long getEcsConfig() {
        return userService.getEcsConfig();
    }

    @PostMapping("/selectIamUserCount")
    public List<IamUserResult> selectIamUserCount(@RequestBody ObsIamUserList obsIamUserList) {
        return userService.selectIamUserCount(obsIamUserList);
    }

    @PostMapping("/isPfadmin/{userId}")
    public Boolean isPfadmin(@PathVariable("userId") Long userId) {
        return userService.isPfadmin(userId);
    }

    @GetMapping("/screen/module_url")
    public List<BigScreenRoleAuthModuleResult> getBigScreenModuleConfigUrl(@RequestParam(required = false) String authKey) {
        DescribeUserTokenResponse userToken = this.getUserToken("bss");

        List<BigScreenRoleAuthModuleResult> resultList = new ArrayList<>();
        Map<String, List<SysAuthModuleDTO>> auth = userToken.getAuth();
        if (Objects.isNull(auth)) {
            return resultList;
        }
        List<SysAuthModuleDTO> sysAuthModuleDTOS = auth.get("big");
        if (CollectionUtils.isEmpty(sysAuthModuleDTOS) || CollectionUtils.isEmpty(sysAuthModuleDTOS.get(0).getChildren())) {
            return resultList;
        }
        List<SysAuthModuleDTO> children = sysAuthModuleDTOS.get(0).getChildren();

        for (SysAuthModuleDTO child : children) {
            BigScreenRoleAuthModuleResult result = new BigScreenRoleAuthModuleResult();
            result.setModuleSid(child.getAuthKey());
            result.setModuleName(child.getName());
            result.setModuleUrl(child.getModuleUrl());

            String configKey = "";
            switch (child.getAuthKey()) {
                case "G01":
                    configKey = "screen.airesource.url";
                    break;
                case "G02":
                    configKey = "hpc_screen_adress";
                    break;
                case "G03":
                    configKey = SysConfigConstants.RES_SCREEN_URL;
                    break;
                case "G04":
                    configKey = "market_screen_address";
                    break;
                default:
            }
            if (StringUtils.isNotEmpty(configKey)) {
                SysConfig sysConfig = sysConfigMapper.selectByConfigKey(configKey);
                if (Objects.nonNull(sysConfig)) {
                    result.setConfigUrl(sysConfig.getConfigValue());
                }
            }

            resultList.add(result);
        }

        if (StringUtils.isNotEmpty(authKey)) {
            BigScreenRoleAuthModuleResult item = null;
            for (BigScreenRoleAuthModuleResult result : resultList) {
                if (authKey.equals(result.getModuleSid())) {
                    item = result;
                    break;
                }
            }

            if (Objects.nonNull(item)) {
                resultList.remove(item);
                resultList.add(0, item);
            }
        }
        return resultList;
    }
}
