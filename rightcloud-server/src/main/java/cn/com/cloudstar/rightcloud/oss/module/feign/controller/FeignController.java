package cn.com.cloudstar.rightcloud.oss.module.feign.controller;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;

import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import feign.Response;
import io.minio.org.apache.commons.validator.routines.RegexValidator;
import io.seata.spring.annotation.GlobalTransactional;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.response.CashCouponAccountResponse;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.BD.BD06;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.BL.BL01;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.BQ;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.COMMON;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.COMMON.PUBLIC.Z1;
import cn.com.cloudstar.rightcloud.common.constants.AuthModuleOss;
import cn.com.cloudstar.rightcloud.common.constants.RestConst;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.exception.BizException;
import cn.com.cloudstar.rightcloud.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.core.annotation.idempotent.Idempotent;
import cn.com.cloudstar.rightcloud.core.annotation.log.SmsValidation;
import cn.com.cloudstar.rightcloud.core.pojo.constant.DiscountPolicyEnum;
import cn.com.cloudstar.rightcloud.core.pojo.dto.bss.BizCoupon;
import cn.com.cloudstar.rightcloud.core.pojo.dto.bss.BizDiscount;
import cn.com.cloudstar.rightcloud.core.pojo.dto.charge.BizBillingAccount;
import cn.com.cloudstar.rightcloud.core.pojo.dto.file.SysMFilePath;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.Code;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.Org;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.Role;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicInfoUtil;
import cn.com.cloudstar.rightcloud.module.support.db.util.DBUtils;
import cn.com.cloudstar.rightcloud.operationlog.annotation.OperationLog;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.annotation.AuthorizeOss;
import cn.com.cloudstar.rightcloud.oss.common.annotation.DataPermission;
import cn.com.cloudstar.rightcloud.oss.common.constants.ModuleTypeConstants;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.RoleType;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.UserType;
import cn.com.cloudstar.rightcloud.oss.common.mybatis.enums.DataScopeEnum;
import cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.oss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.oss.common.util.AuthUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.DataProcessingUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.DataScopeUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.ZipUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.annotation.ListenExpireBack;
import cn.com.cloudstar.rightcloud.oss.common.util.annotation.StartWithWord;
import cn.com.cloudstar.rightcloud.oss.common.util.annotation.constant.TypesConstant;
import cn.com.cloudstar.rightcloud.oss.common.util.cache.AuthUserHolder;
import cn.com.cloudstar.rightcloud.oss.common.util.encrypt.Encrypt;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.model.BizContract;
import cn.com.cloudstar.rightcloud.oss.module.account.cfn.CfnService;
import cn.com.cloudstar.rightcloud.oss.module.account.cfn.dto.QueryPoolStatusReq;
import cn.com.cloudstar.rightcloud.oss.module.account.cfn.dto.UpdatePoolUserReq;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.org.BizContractMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.user.UserMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.service.org.OrgService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.role.RoleModuleService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.role.RoleService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.user.UserService;
import cn.com.cloudstar.rightcloud.oss.module.coupon.bean.CashCoupon;
import cn.com.cloudstar.rightcloud.oss.module.coupon.dao.CashCouponMapper;
import cn.com.cloudstar.rightcloud.oss.module.export.dao.BizDownloadMapper;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.BaseGridReturn;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.BizDownload;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.ServiceCategory;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.ViewContractVO;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.ApplyServiceRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.BizDownloadRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.CancelCashCouponRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.CreateCashCouponRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.CreateDiscountPolicyRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.CreateDiscountRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.CreateDistributorUserRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.DeductCashCouponRecordRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.DescribeAccountCouponRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.DescribeBillingAccountRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.DescribeBizBillingStrategyRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.DescribeBizContractRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.DescribeBizContractTemplateRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.DescribeDealsRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.DescribeDiscountPolicyPageRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.DescribeDiscountPolicyRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.DescribeDistributorRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.DescribeGaapCostPostRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.DescribeGaapCostRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.DescribeInvoicesRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.DescribeProductRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.DescribeProductResourceRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.DescribeProductTemplateRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.DescribeSubuserRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.DescribeUserGroupRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.DistributeCashCouponRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.DistributeRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.DownloadRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.EditFreezingStrategyRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.GaapCostDetailRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.InquiryRenewPriceRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.IsNonBillProductRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.ModifyInquiryPriceRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.OperateDiscountPolicyRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.OperateDiscountRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.PasswordPolicyRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.RechargeBillingAccountRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.RenewRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.SetExpirationTimeRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.UpdateCreditLineRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.UpdateDiscountRequest;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.response.BillBillingCycleCostResponse;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.response.DeductCashCouponRecordResponse;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.response.DescribeAccountCouponResponse;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.response.DescribeBillingAccountResponse;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.response.DescribeBizBillingStrategyResponse;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.response.DescribeContractDetailResponse;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.response.DescribeContractResponse;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.response.DescribeContractTemplateResponse;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.response.DescribeDealsResponse;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.response.DescribeDistributorTreeResponse;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.response.DescribeGaapCostSimpleResponse;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.response.DescribeInvoiceDetailResponse;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.response.DescribeInvoiceResponse;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.response.DescribeProductResourceResponse;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.response.DescribeProductResponse;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.response.DescribeProductTemplateResponse;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.response.DescribeProjectSimpleResponse;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.response.DescribeSubusersResponse;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.response.DescribeUserGroupResponse;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.response.InquiryPriceResponse;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.response.ModifyQueryPriceResponse;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.response.PasswordPolicyResponse;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.response.PasswordRuleResponse;
import cn.com.cloudstar.rightcloud.oss.module.feign.service.FeignService;
import cn.com.cloudstar.rightcloud.oss.module.file.dao.SysMFilePathMapper;
import cn.com.cloudstar.rightcloud.oss.module.operationcore.service.process.ProcessMgtService;
import cn.com.cloudstar.rightcloud.oss.module.order.dao.BizCouponMapper;
import cn.com.cloudstar.rightcloud.oss.module.order.dao.BizDiscountMapper;
import cn.com.cloudstar.rightcloud.oss.module.order.entity.CalculateDateRequest;
import cn.com.cloudstar.rightcloud.oss.module.order.entity.ModifyUnifyDateRequest;
import cn.com.cloudstar.rightcloud.oss.module.pricing.dao.priceconfig.BizBillingAccountMapper;
import cn.com.cloudstar.rightcloud.oss.module.pricing.service.priceconfig.BizBillingAccountService;
import cn.com.cloudstar.rightcloud.oss.module.resource.service.CodeService;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.dao.ServiceCategoryMapper;

import static cn.com.cloudstar.rightcloud.common.constants.AuthModule.BQ.BQ010701;
import static cn.com.cloudstar.rightcloud.common.constants.AuthModule.COMMA;

/**
 * 共享控制器
 *
 * <AUTHOR>
 * @date 2023/01/09
 */
@RestController
@RequestMapping()
public class FeignController {

    private static final String CRLF_REGEX = ".*[\\r\\n].*";

    @Autowired
    private BizBillingAccountMapper bizBillingAccountMapper;

    @Autowired
    private UserService userService;

    @Autowired
    private OrgService orgService;

    @Autowired
    FeignService feignService;

    @Autowired
    SysMFilePathMapper sysMFilePathMapper;

    @Autowired
    private BizContractMapper bizContractMapper;

    @Autowired
    private BizDownloadMapper bizDownloadMapper;
    @Autowired
    private BizBillingAccountService bizBillingAccountService;

    @Autowired
    private CodeService codeService;
    @Autowired
    private ServiceCategoryMapper serviceCategoryMapper;
    @Autowired
    @Lazy
    private ProcessMgtService processMgtService;
    @Autowired
    private UserMapper userMapper;


    @Autowired
    private BizDiscountMapper  bizDiscountMapper;

    @Autowired
    private RoleService roleService;

    @Autowired
    private RoleModuleService roleModuleService;
    @Autowired
    private BizCouponMapper bizCouponMapper;

    @Autowired
    private CashCouponMapper cashCouponMapper;

    @Autowired
    private CfnService cfnService;


    /**
     * operationType
     */
    private static final String OPERATION_TYPE = "operationType";
    /**
     * operationType
     */
    private static final String CONTRACT = "02";
    /**
     * operationId
     */
    private static final String FILENUM = "fileNum";
    /**
     * 分销商类型
     */
    private static final String FOUR = "04";

    private final static String QUERY_SQL = "select code_value from sys_m_code where CODE_CATEGORY = ?";
    private final static String CONDITION_COLUMN = "code_value";

    private static final String ONE = "1";
    /**
     * 运营控制台
     */
    private static final String FROM_BSS = "bss";

    /**
     * 类型-充值现金券
     */
    private static final String TYPE_DEPOSIT = "deposit";
    /**
     * 类型-抵扣现金券
     */
    private static final String TYPE_DEDUCT = "deduct";


    /**
     * 【Since v2.5.0】获取全局默认折扣策略
     *
     * @return {@code DescribeDiscountPolicyResponse}
     */
    @AuthorizeOss(action = AuthModule.BR.BR05.BR05)
    @ApiOperation(httpMethod = "GET", value = "获取全局默认折扣策略")
    @GetMapping("/discount/policy")
    public RestResult findPDiscountDetail() {
        return feignService.findPDiscountDetail();
    }


    /**
     * 【Since v2.5.0】启用、禁用、删除、修改折扣策略(客户折扣策略)
     *
     * @param request 操作启用、禁用、删除折扣策略请求体
     * @return {@code RestResult}
     */
    @AuthorizeOss(action = AuthModule.BR.BR05.BR0504)
    @ApiOperation(httpMethod = "PUT", value = "启用、禁用、删除折扣策略")
    @PutMapping("/discount/policy")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'修改客户折扣策略'", resource = OperationResourceEnum.UPDATE_DISCOUNT_POLICY,param = "#request",bizId = "#request.userAccountId", tagNameUs ="'Modify customer discount strategy'")
    public RestResult updateDiscountPolicy(@Valid @RequestBody OperateDiscountPolicyRequest request) {
        //参数检查
        String cloudEnvScope = request.getCloudEnvScope();
        if (StringUtils.isNotEmpty(cloudEnvScope) && !vailidTypesRanges(cloudEnvScope, TypesConstant.DISCOUNT_ENV_TYPE)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        String productScope = request.getProductScope();
        if (StringUtils.isNotEmpty(productScope)  && !vailidTypesRanges(productScope, TypesConstant.DISCOUNT_PRODUCT_TYPE)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        if (StringUtils.isNotEmpty(productScope)) {
            validProductType(productScope);
        }
        String policyType = request.getPolicyType();
        if (StringUtils.isNotEmpty(policyType)  && DiscountPolicyEnum.getEnum(policyType) == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        Long userAccountId = request.getUserAccountId();
        if (userAccountId != null) {
            BizBillingAccount bizBillingAccount = bizBillingAccountService.selectById(userAccountId);
            if( bizBillingAccount == null || !Objects.equals(RequestContextUtil.getEntityId(), bizBillingAccount.getEntityId())){
                throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
            }
        }
        return feignService.updateDiscountPolicy(request);
    }

    private boolean  vailidTypesRanges(Object value,String type) {
        List<String> result = DBUtils.INSTANCE.queryColumnList(QUERY_SQL, CONDITION_COLUMN, new Object[]{type});
        if (CollectionUtil.isNotEmpty(result)) {
            if (Stream.of(String.valueOf(value).split(",")).allMatch(s1 -> result.contains(s1))) {
                return true;
            }
        }
        return false;
    }

    private void validProductType(String productScope){
        Criteria criteria = new Criteria();
        criteria.put("codeCategory", "DISCOUNT_PRODUCT_TYPE");
        List<Code> codes = this.codeService.selectByParams(criteria);
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        Criteria criteriaProduct = new Criteria();
        criteriaProduct.put("publishStatus", "succeed");
        criteriaProduct.put("publishDtIsNotNull", ONE);
        if (ModuleTypeConstants.FROM_BSS.equalsIgnoreCase(authUserInfo.getRemark())) {
            criteriaProduct.put("entityId", RequestContextUtil.getEntityId());
            criteriaProduct.put("serviceComponent", "innerService");
            criteriaProduct.setOrderByClause("service_type");
            List<cn.com.cloudstar.rightcloud.core.pojo.dto.sfs.ServiceCategory> serviceCategories = serviceCategoryMapper.selectDistinctByParams(criteriaProduct);
            Set<String> current = serviceCategories.stream().filter(t -> Objects.nonNull(t.getEntityId())).map(
                    cn.com.cloudstar.rightcloud.core.pojo.dto.sfs.ServiceCategory::getServiceType).collect(
                    Collectors.toSet());
            Set<String> productTypes = codes.stream().map(Code::getCodeValue).collect(Collectors.toSet());
            current.retainAll(productTypes);
            if (Arrays.stream(productScope.split(",")).anyMatch(e -> !current.contains(e))) {
                throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
            }
        }
    }


    /**
     * 【Since v2.5.0】保存折扣策略
     *
     * @param request 保存折扣策略
     * @return {@code RestResult}
     */
    @AuthorizeOss(action = AuthModule.BR.BR05.BR0501)
    @ApiOperation(httpMethod = "POST", value = "新增客户折扣策略")
    @PostMapping("/discount/policy")
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "'新增客户折扣策略'", resource = OperationResourceEnum.CREATE_DISCOUNT_STRATEGY,param = "#request",bizId = "#request.userAccountId", tagNameUs ="'Add customer discount strategy'")
    public RestResult createDiscountPolicy(@Valid @RequestBody CreateDiscountPolicyRequest request) {

        //参数检查
        String cloudEnvScope = request.getCloudEnvScope();
        if (StringUtils.isNotEmpty(cloudEnvScope) && !vailidTypesRanges(cloudEnvScope, TypesConstant.DISCOUNT_ENV_TYPE)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        String productScope = request.getProductScope();
        if (StringUtils.isNotEmpty(productScope)  && !vailidTypesRanges(productScope, TypesConstant.DISCOUNT_PRODUCT_TYPE)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        if (StringUtils.isNotEmpty(productScope)) {
            validProductType(productScope);
        }
        String policyType = request.getPolicyType();
        if (StringUtils.isNotEmpty(policyType)  && DiscountPolicyEnum.getEnum(policyType) == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        Long userAccountId = request.getUserAccountId();
        if (userAccountId != null) {
            BizBillingAccount bizBillingAccount = bizBillingAccountService.selectById(userAccountId);
            if( bizBillingAccount == null || !Objects.equals(RequestContextUtil.getEntityId(), bizBillingAccount.getEntityId())){
                throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
            }
        }

        return feignService.createDiscountPolicy(request);
    }

    /**
     * 【Since v2.5.0】获取客户折扣策略
     *
     * @return {@code DescribeDiscountPolicyResponse}
     */
    @AuthorizeOss(action = AuthModule.BR.BR05.BR05)
    @ApiOperation(httpMethod = "GET", value = "获取客户折扣策略")
    @GetMapping("/discount/policy/findCustomerPage")
    public RestResult findCustomerPage(DescribeDiscountPolicyPageRequest request) {
        return feignService.findCustomerPage(request);
    }

    /**
     * 【Since v2.5.0】获取单个客户折扣策略
     *
     * @return {@code DescribeDiscountPolicyResponse}
     */
    @AuthorizeOss(action = AuthModule.BR.BR05.BR05)
    @ApiOperation(httpMethod = "GET", value = "获取单个客户折扣策略")
    @GetMapping("/discount/policy/findCustomerByUserAccountId")
    public RestResult findCustomerByUserAccountId(@Validated DescribeDiscountPolicyRequest request) {
        return feignService.findCustomerByUserAccountId(request);
    }





    /**
     * 【Since v2.5.0】账单明细异步导出(费用中心-账单管理-账单明细-导出)
     *
     * @param request    成本明细请求体
     * @param moduleType 模块类型
     * @return {@code RestResult}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BC.BC03.BC030202)
    @PostMapping(value = "/bills/asynExportBillDetails")
    @ApiOperation(httpMethod = "POST", value = "账单明细异步导出")
    @SmsValidation
    @OperationLog(type = OperationTypeEnum.EXPORT, tagName = "'账单明细'",integrity = true, resource = OperationResourceEnum.BILL_ASYNC_EXPORT, param = "#request", tagNameUs ="'Billing Details'")
    @Idempotent
    public RestResult asynExportBillDetails(@RequestBody @Valid DescribeGaapCostPostRequest request, @RequestHeader String moduleType) {
        if (!moduleType.equals(ModuleTypeConstants.FROM_BSS) && !moduleType.equals(ModuleTypeConstants.FROM_CONSOLE) &&
                !moduleType.equals(ModuleTypeConstants.FROM_COMMON)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        return feignService.asynExportBillDetailsByFeign(request,moduleType);
    }

    /**
     * 【Since v2.5.0】账单明细异步导出(客户管理)
     *
     * @param request    成本明细请求体
     * @param moduleType 模块类型
     * @return {@code RestResult}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BQ.BQ0108)
    @PostMapping(value = "/bills/asynExportBillDetails/customer")
    @ApiOperation(httpMethod = "POST", value = "账单明细异步导出")
    @SmsValidation
    @OperationLog(type = OperationTypeEnum.EXPORT, tagName = "'账单明细'", resource = OperationResourceEnum.BILL_ASYNC_EXPORT, param = "#request", tagNameUs ="'Billing Details'")
    @Idempotent
    public RestResult asynExportBillDetailsByCustomer(@RequestBody @Valid DescribeGaapCostPostRequest request, @RequestHeader String moduleType) {
        return feignService.asynExportBillDetailsByFeign(request,moduleType);
    }

    /**
     * 【Since v2.5.0】用户套餐包使用明细
     *
     * @param request 成本明细请求体
     * @return {@code IPage<DescribeGaapCostResponse>}
     */
    @AuthorizeOss(action = AuthModule.BL.BL03.BL030802)
    @GetMapping("bills/user/bag")
    public RestResult userBagListBills(DescribeGaapCostRequest request) {
        return feignService.userBagListBills(request);
    }

    /**
     * 获取发票列表
     *
     * @param request    获取发票列表请求体
     * @param moduleType 模块类型
     * @return {@code IPage<DescribeInvoiceResponse>}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BC.BC04.BC0401)
    @GetMapping("/invoice")
    @ApiOperation("获取发票列表")
    @ListenExpireBack
    public RestResult<IPage<DescribeInvoiceResponse>> selectInvoices(DescribeInvoicesRequest request, @RequestHeader String moduleType) {
        return feignService.selectInvoicesByFeign(request,moduleType);
    }

    /**
     * 账单周期异步导出
     *
     * @param request    请求体
     * @param moduleType 模块类型
     * @return {@code RestResult}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BC.BC03.BC030102)
    @GetMapping(value = "/bills/asynExportBills")
    @ApiOperation(httpMethod = "GET", value = "账单周期异步导出")
    @SmsValidation
    @OperationLog(type = OperationTypeEnum.EXPORT, tagName = "'账单周期列表'",integrity = true, bizId = "#request.billBillingCycleId", resource = OperationResourceEnum.BILLING_CYCLE_EXPORT, tagNameUs ="'Billing Cycle List'")
    @Idempotent
    public RestResult asynBillingCycleExport(@Valid DescribeGaapCostRequest request, @RequestHeader String moduleType) {
        return feignService.asynExportBillsByFeign(request, moduleType);
    }


    /**
     * 获取账户列表(创建合同)
     *
     * @param request 账户列表查询请求体
     * @return {@code IPage<DescribeBillingAccountResponse>}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BC.BC05.BC0505)
    @ApiOperation(httpMethod = "GET", value = "获取账户列表")
    @GetMapping("/billing_account/contract")
    public RestResult<IPage<DescribeBillingAccountResponse>> findBillingAccountsByContract(@Valid DescribeBillingAccountRequest request) {
        return feignService.findBillingAccounts(request);
    }

    /**
     * 获取账户列表(客户管理-客户信息)
     *
     * @param request 账户列表查询请求体
     * @return {@code IPage<DescribeBillingAccountResponse>}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BQ.BQ_COMMON)
    @ApiOperation(httpMethod = "GET", value = "获取账户列表")
    @GetMapping("/billing_account/customer")
    public RestResult<IPage<DescribeBillingAccountResponse>> findBillingAccountsByCustomer(@Valid DescribeBillingAccountRequest request) {
        AuthUser  user = RequestContextUtil.getAuthUserInfo();
        if(user != null){
            List<Role> roleList =  roleService.selectRoleByUserSid(user.getUserSid());
            List<cn.com.cloudstar.rightcloud.common.pojo.Role> convertsRolesList = BeanConvertUtil.convert(roleList,cn.com.cloudstar.rightcloud.common.pojo.Role.class);
            String maxScope = DataScopeUtil.getMaxDataScope(convertsRolesList);
            //运营实体下数据
            if(DataScopeEnum.DATA_SCOPE_COMPANY_AND_CHILD.getScope().equals(maxScope)){
                request.setDistributorId(user.getOrgSid());
                //当前组织-个人及关联客户数据
            }else if(DataScopeEnum.DATA_SCOPE_COMPANY.getScope().equals(maxScope)){
                request.setSalesmenId(user.getUserSid());
            }
        }
        return feignService.findBillingAccounts(request);
    }

    /**
     * 获取账户列表(分销管理)
     *
     * @param request 账户列表查询请求体
     * @return {@code IPage<DescribeBillingAccountResponse>}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BU.BU11)
    @ApiOperation(httpMethod = "GET", value = "获取账户列表")
    @GetMapping("/billing_account/distribution")
    public RestResult<IPage<DescribeBillingAccountResponse>> findBillingAccountsByDistribution(@Valid DescribeBillingAccountRequest request) {
        return feignService.findBillingAccounts(request);
    }

    /**
     *【Since v2.5.0】获取账户列表(折扣管理-平台折扣)
     *
     * @param request 账户列表查询请求体
     * @return {@code IPage<DescribeBillingAccountResponse>}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BR.BR01.BR01)
    @ApiOperation(httpMethod = "GET", value = "获取账户列表")
    public RestResult<IPage<DescribeBillingAccountResponse>> findBillingAccountsByPlatformDiscount(DescribeBillingAccountRequest request) {
        return feignService.findBillingAccounts(request);
    }

    /**
     * 获取账户列表(折扣管理-客户折扣)
     *
     * @param request 账户列表查询请求体
     * @return {@code IPage<DescribeBillingAccountResponse>}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BR.BR02.BR02 + "," + AuthModule.BQ.BQ0108080202)
    @ApiOperation(httpMethod = "GET", value = "获取账户列表")
    @GetMapping("/billing_account/customerDiscount")
    public RestResult<IPage<DescribeBillingAccountResponse>> findBillingAccountsByCustomerDiscount(DescribeBillingAccountRequest request) {
        return feignService.findBillingAccounts(request);
    }
    /**
     * 【Since v2.5.0】试用期账号批量设置到期时间和处理策略(客户管理-客户信息)
     *
     * @param request 试用期账号批量设置到期时间和处理策略
     * @return {@code RestResult}
     */
    @AuthorizeOss(action = AuthModule.BQ.BQ0113)
    @ApiOperation(httpMethod = "POST", value = "试用期账号批量设置到期时间和处理策略")
    @PutMapping("/billing_account/expirationTime")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'设置到期时间'", tagNameUs ="'Set the expiration time'",
            resource = OperationResourceEnum.SET_EXPIRATION_TIME, bizId = "#request.id", param = "#request")
    public RestResult setExpirationTime(@RequestBody @Valid SetExpirationTimeRequest request) {
        return feignService.setExpirationTime(request);
    }
    /**
     * 创建现金券(充值现金券)
     *
     * @param createCashCouponRequest 创建现金优惠券请求体
     * @return {@code RestResult}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BL.BL02.BL020101)
    @ApiOperation(value = "创建充值现金券", httpMethod = "POST")
    @PostMapping("/topCashCoupon")
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "'充值现金券'", tagNameUs ="'Recharge cash coupon'",
            resource = OperationResourceEnum.CREATEBALANCECASH,param = "#createCashCouponRequest",
            bizId = "#createCashCouponRequest.couponName")
    @Idempotent
    public RestResult createTopCashCoupons(@RequestBody @Valid CreateCashCouponRequest createCashCouponRequest) {
        if (!TYPE_DEPOSIT.equals(createCashCouponRequest.getType())) {
            BizException.e(WebUtil.getMessage(MsgCd.ERR_MSG_21));
        }
        return feignService.createCashCoupons(createCashCouponRequest);
    }

    /**
     * 创建现金券(抵扣现金券)
     *
     * @param createCashCouponRequest 创建现金优惠券请求体
     * @return {@code RestResult}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BL.BL02.BL020201)
    @ApiOperation(value = "创建抵扣现金券", httpMethod = "POST")
    @PostMapping("/deductibleCashCoupon")
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "'抵扣现金券'", tagNameUs ="'Deduction cash coupon'",
            resource = OperationResourceEnum.CREATEBALANCEDUCTCASH,param = "#createCashCouponRequest",
            bizId = "#createCashCouponRequest.couponName")
    @Idempotent
    public RestResult createCashCoupons(@RequestBody @Valid CreateCashCouponRequest createCashCouponRequest) {
        if (!TYPE_DEDUCT.equals(createCashCouponRequest.getType())) {
            BizException.e(WebUtil.getMessage(MsgCd.ERR_MSG_22));
        }
        return feignService.createCashCoupons(createCashCouponRequest);
    }

    /**
     * 查询合同模板列表
     *
     * @param request 查询合同模板列表请求体
     * @return {@code RestResult}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BC.BC05.BC0501 + "," + AuthModule.BC.BC05.BC0505)
    @ApiOperation("查询合同模板列表")
    @GetMapping("/contract/template")
    public RestResult<List<DescribeContractTemplateResponse>> queryList(DescribeBizContractTemplateRequest request) {
        return feignService.queryList(request);
    }

    /**
     * 启用、禁用折扣(平台折扣)
     *
     * @param request 操作折扣，禁用、启用，删除客户下所有折扣请求体
     * @return {@code RestResult}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BR.BR01.BR0104)
    @ApiOperation(httpMethod = "PUT", value = "启用、禁用折扣")
    @PutMapping("/discount/operate/platform")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'平台折扣'", tagNameUs ="'Platform Discounts'",
            resource = OperationResourceEnum.START_DISABLE_DISCOUNT,bizId = "#request.discountSid",param = "#request")
    public RestResult updateBillingStrategyStatusByPlatform(@RequestBody @Valid OperateDiscountRequest request) {
        if(request.getUserSid() != null){
            BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectByPrimaryKey(request.getUserSid());
            if(bizBillingAccount ==null){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1694975136));
            }
        }
        BizDiscount discount = bizDiscountMapper.selectByPrimaryKey(request.getDiscountSid());
        if(discount == null){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1033172136));
        }else{
            if(!"platform".equals(discount.getDiscountType())){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1033172136));
            }
        }
        return feignService.updateBillingStrategyStatus(request);
    }

    /**
     * 启用、禁用折扣(客户折扣)
     *
     * @param request 操作折扣，禁用、启用，删除客户下所有折扣请求体
     * @return {@code RestResult}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BR.BR02.BR0204 + "," + AuthModule.BQ.BQ0108080203 + "," + AuthModule.BQ.BQ0108080204)
    @ApiOperation(httpMethod = "PUT", value = "启用、禁用折扣")
    @PutMapping("/discount/operate/customer")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'客户折扣'", tagNameUs ="'Customer Discount'",
            resource = OperationResourceEnum.START_DISABLE_DISCOUNT,bizId = "#request.discountSid",param = "#request")
    public RestResult updateBillingStrategyStatusByCustomer(@RequestBody @Valid OperateDiscountRequest request) {
        if(request.getUserSid() != null){
            BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectByPrimaryKey(request.getUserSid());
            if(bizBillingAccount ==null){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1694975136));
            }
        }
        if(Objects.nonNull(request.getDiscountSid())){
            BizDiscount discount = bizDiscountMapper.selectByPrimaryKey(request.getDiscountSid());
            if(discount == null){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_399127456));
            }else{
                if(!"customer".equals(discount.getDiscountType())){
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_399127456));
                }
            }
        }

        return feignService.updateBillingStrategyStatus((request));
    }

    /**
     * 查询产品模板(产品管理-产品模板)
     *
     * @param request 查询产品模板请求体
     * @return {@code IPage<DescribeProductTemplateResponse>}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BD.BD05.BD05)
    @ApiOperation("查询产品模板")
    @GetMapping("/management/products/templates")
    public RestResult<IPage<DescribeProductTemplateResponse>> managementListTemplates(@Valid DescribeProductTemplateRequest request) {
        return feignService.listTemplates(request);
    }

    /**
     * 查询产品模板(客户管理-资源-代客订购)
     *
     * @param request 查询产品模板请求体
     * @return {@code IPage<DescribeProductTemplateResponse>}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BQ.BQ010701)
    @ApiOperation("查询产品模板")
    @GetMapping("/products/templates")
    public RestResult<IPage<DescribeProductTemplateResponse>> listTemplates(@Valid DescribeProductTemplateRequest request) {
        return feignService.listTemplates(request);
    }

    /**
     * 获取账户详情(客户管理-客户信息-管理)
     *
     * @param id 账户id
     * @return {@code DescribeBillingAccountResponse}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BQ.BQ01080201)
    @ApiOperation(httpMethod = "GET", value = "获取账户详情")
    @GetMapping("/billing_account/{id}")
    public RestResult<DescribeBillingAccountResponse> getBillingAccountDetail(@PathVariable("id") @ApiParam(value = "账户ID", type = "Long", required = true) Long id) {
        return feignService.getBillingAccountDetail(id);
    }

    /**
     * 获取帐单帐户详细信息
     *
     * @param accountIds id
     *
     * @return {@code RestResult}
     */
    @AuthorizeOss(action = AuthModule.BG.BG03)
    @ApiOperation(httpMethod = "GET", value = "获取账户名称")
    @GetMapping("/billing_account")
    public RestResult getBillingAccount(String accountIds) {
        List<String> accountName = new ArrayList<>();
        Arrays.asList(accountIds.split(",")).forEach(accountId->{
            accountName.add(bizBillingAccountMapper.selectByPrimaryKey(Long.valueOf(accountId)).getAccountName());
        });
        return new RestResult(accountName);
    }

    /**
     * 获取账户详情(客户管理-账户管理-子账户管理)
     *
     * @param id 账户id
     * @return {@code DescribeBillingAccountResponse}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BQ.BQ03.BQ0304)
    @ApiOperation(httpMethod = "GET", value = "获取账户详情")
    @GetMapping("/billing_account_management/{id}")
    public RestResult<DescribeBillingAccountResponse> getBillingAccountDetailByManagement(@PathVariable("id") @ApiParam(value = "账户ID", type = "Long", required = true) Long id) {
        RestResult billingAccountDetail = feignService.getBillingAccountDetail(id);
        DescribeBillingAccountResponse data = BeanConvertUtil.convert(billingAccountDetail.getData(), DescribeBillingAccountResponse.class);
        if (Objects.nonNull(data)){
            data.setAdminmobileName(data.getMobile());
        }
        return new RestResult<>(data);
    }

    /**
     * 获取账单周期列表数据
     *
     * @param request 成本明细请求体
     * @return {@code IPage<BillBillingCycleCostVo>}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BC.BC03.BC03_COMMON)
    @GetMapping(value = "/bills/listCycleBills")
    @ApiOperation(httpMethod = "GET", value = "获取账单周期列表数据")
    public RestResult<IPage<BillBillingCycleCostResponse>> listCycleBills(@Valid DescribeGaapCostRequest request) {
        return feignService.listCycleBillsByFeign(request);
    }

    /**
     * 导出发票列表
     *
     * @param request    导出发票列表请求体
     * @param response   响应
     * @param moduleType 模块类型
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BC.BC04.BC0402)
    @ApiOperation(httpMethod = "GET", value = "导出发票列表")
    @GetMapping("/invoice/export")
    @SmsValidation
    @OperationLog(type = OperationTypeEnum.EXPORT, tagName = "'发票列表'", tagNameUs ="'Invoice List'",
            resource = OperationResourceEnum.EXPORT_INVOICE_LIST, param = "#request")
    @Idempotent
    public RestResult expertOrderList(DescribeInvoicesRequest request) {
        return feignService.expertOrderListByFeign(request);
    }

    /**
     * 删除折扣(折扣管理-平台折扣)
     *
     * @param id id
     * @return {@code RestResult}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BR.BR01.BR0103)
    @ApiOperation(httpMethod = "DELETE", value = "删除折扣")
    @DeleteMapping("/platform/discount/{id}")
    @OperationLog(type = OperationTypeEnum.DELETE, tagName = "'平台折扣'", tagNameUs ="'Platform Discounts'",
            resource = OperationResourceEnum.DELDISCOUNT,bizId = "#id")
    public RestResult<Long> deletePlatformDiscount(@PathVariable("id") Long id) {
        return feignService.deleteDiscount(id);
    }

    /**
     * 删除折扣(折扣管理-客户折扣)
     *
     * @param id id
     * @return {@code RestResult}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BR.BR02.BR0203 + "," + AuthModule.BQ.BQ0108080204)
    @ApiOperation(httpMethod = "DELETE", value = "删除折扣")
    @DeleteMapping("/customer/discount/{id}")
    @OperationLog(type = OperationTypeEnum.DELETE, tagName = "'客户折扣'", tagNameUs ="'Customer Discount'",
            resource = OperationResourceEnum.DELDISCOUNT,bizId = "#id")
    public RestResult<Long> deleteCustomerDiscount(@PathVariable("id") Long id) {
        return feignService.deleteDiscount(id);
    }

    /**
     * 查询账户下的优惠劵(客户管理-客户信息-管理)
     *
     * @param request 查询账户下优惠劵请求体
     * @return {@code IPage<DescribeAccountCouponResponse>}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BQ.BQ010805)
    @GetMapping("/coupons/accounts")
    @ApiOperation("查询账户下的优惠劵")
    public RestResult<IPage<DescribeAccountCouponResponse>> listCoupons(@Validated DescribeAccountCouponRequest request) {
        if(!Objects.isNull(request.getAccountId())){
            BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectByPrimaryKey(request.getAccountId());
            if (Objects.isNull(bizBillingAccount)){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1047765662));
            }
            cn.com.cloudstar.rightcloud.oss.common.pojo.User authUser = AuthUtil.getAuthUser();
            if (Objects.isNull(authUser) || Objects.isNull(authUser.getUserSid())) {
                throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
            }
            //解决分销商纵向越权
            if(!Objects.isNull(authUser.getOrgSid()) && FOUR.equals(authUser.getUserType())){
                if(!authUser.getOrgSid().equals(bizBillingAccount.getDistributorId())){
                    throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
                }
            }
        }
        return feignService.listCoupons(request);
    }

    /**
     * 计费策略列表(产品管理-计费配置)
     *
     * @param request 计费策略查询请求体
     * @return {@code RestResult}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BD.BD03.BD03)
    @ApiOperation("计费策略列表")
    @GetMapping("/billing/strategy")
    public RestResult<BaseGridReturn<List<DescribeBizBillingStrategyResponse>>> queryList(@Validated DescribeBizBillingStrategyRequest request) {
        return feignService.queryList(request);
    }
    /**
     * 计费策略列表(产品管理-计费策略-产品计费策略-定价)
     *
     * @param request 计费策略查询请求体
     * @return {@code RestResult}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BD.BD04.BD040202)
    @ApiOperation("计费策略列表")
    @GetMapping("/billing/strategy/pricing")
    public RestResult<BaseGridReturn<List<DescribeBizBillingStrategyResponse>>> queryListPricing(@Validated DescribeBizBillingStrategyRequest request) {
        return feignService.queryList(request);
    }

    /**
     * 内置代客下单产品列表
     *
     * @return {@code RestResult}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BQ.BQ0107)
    @GetMapping("/products/resources/behalf/service")
    @ApiOperation("内置代客下单产品列表")
    public RestResult<List<ServiceCategory>> queryBehalfService() {
        return feignService.queryBehalfService();
    }

    /**
     * 编辑折扣(折扣管理-平台折扣)
     *
     * @param request 编辑折扣请求体
     * @return {@code RestResult}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BR.BR01.BR0102)
    @ApiOperation(httpMethod = "PUT", value = "编辑折扣")
    @PutMapping("/discount")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "#request.discountName",
            resource = OperationResourceEnum.EDIT_PLATFORM_DISCOUNT,bizId = "#request.discountSid",param = "#request")
    public RestResult<Long> updateDiscount(@Valid @RequestBody UpdateDiscountRequest request) {
        if(request.getUserSid() != null){
            BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectByPrimaryKey(request.getUserSid());
            if(bizBillingAccount ==null){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1694975136));
            }
        }
        BizDiscount discount = bizDiscountMapper.selectByPrimaryKey(request.getDiscountSid());
        if(discount == null){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1033172136));
        }else{
            if(!"platform".equals(discount.getDiscountType())){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1033172136));
            }
        }
        return feignService.updateDiscount(request);
    }

    /**
     * 编辑折扣(折扣管理-客户折扣)
     *
     * @param request 编辑折扣请求体
     * @return {@code RestResult}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BR.BR02.BR0202 + "," + AuthModule.BQ.BQ0108080202)
    @ApiOperation(httpMethod = "PUT", value = "编辑折扣")
    @PutMapping("/discount/customer")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "#request.discountName",
            resource = OperationResourceEnum.EDIT_CUSTOMER_DISCOUNT,bizId = "#request.discountSid",param = "#request")
    public RestResult<Long> updateDiscountByCustomer(@Valid @RequestBody UpdateDiscountRequest request) {
        if(request.getUserSid() != null){
            BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectByPrimaryKey(request.getUserSid());
            if(bizBillingAccount ==null){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1694975136));
            }
        }
        if(Objects.nonNull(request.getDiscountSid())){
            BizDiscount discount = bizDiscountMapper.selectByPrimaryKey(request.getDiscountSid());
            if(discount == null){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_399127456));
            }else{
                if(!"customer".equals(discount.getDiscountType())){
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_399127456));
                }
            }
        }

        return feignService.updateDiscount(request);
    }


    /**
     * 账单周期账期列表(费用中心-账单管理-账单周期)
     *
     * @param request 账单周期查询明细请求体
     * @return {@code IPage<cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost>}
     * @since 2.4.0
     */
    @ApiOperation("账单周期账期列表")
    @PostMapping("/bills/listBillDetails")
    @Idempotent
    @AuthorizeOss(action = AuthModule.BC.BC03.BC030101)
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "'查看账单周期账期列表'", resource = OperationResourceEnum.QUERY_BILLING_CRYLE_LIST, tagNameUs ="'View Billing Period Billing Period List'")
    public RestResult<IPage<cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost>> listBillDetailsByFeign(@RequestBody @Valid  GaapCostDetailRequest request) {
        List<String> ids = request.getIds();
        boolean anyMatch = ids.stream().anyMatch(id -> new RegexValidator(CRLF_REGEX).isValid(id));
        if (anyMatch) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        return feignService.listBillDetails(request);
    }

    /**
     * 下载客户导入模版
     *
     * @param response 响应
     * @param fileType 文件类型
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BQ.BQ0109)
    @ApiOperation("下载客户导入模版")
    @GetMapping("/billing_account/import/download_template")
    @OperationLog(type = OperationTypeEnum.EXPORT, tagName = "'租户导入模版'", resource = OperationResourceEnum.TEMPLATE_EXPORT, tagNameUs ="'Tenant Import Template'")
    public void downloadPricingTemplateByCustomer(HttpServletResponse response, @RequestParam("fileType") String fileType) {
        transformation(feignService.downloadPricingTemplate(fileType),response);
    }

    /**
     * 下载账户导入模版
     *
     * @param response 响应
     * @param fileType 文件类型
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BQ.BQ0110)
    @ApiOperation("下载账户导入模版")
    @GetMapping("/billing_account/import/download_template/customer")
    @OperationLog(type = OperationTypeEnum.EXPORT, tagName = "'租户导入模版'", bizId = "#fileType", resource = OperationResourceEnum.TEMPLATE_EXPORT, tagNameUs ="'Tenant Import Template'")
    public void downloadPricingTemplateByAccount(HttpServletResponse response, @RequestParam("fileType") String fileType) {
        transformation(feignService.downloadPricingTemplate(fileType),response);
    }

    /**
     * 账户分页查询现金券
     *
     * @param pagenum       页数
     * @param pagesize      页大小
     * @param type          类型
     * @param couponNo      优惠券不
     * @param status        状态
     * @param accountId     账户id
     * @param sortdatafield 排序字段
     * @param sortorder     排序方式
     * @return {@code IPage<CashCouponAccountResponse>}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BQ.BQ010806 + "," + AuthModule.BQ.BQ010807)
    @ApiOperation(value = "账户分页查询现金券", httpMethod = "GET")
    @GetMapping("/cashCoupon/account")
    public RestResult<IPage<CashCouponAccountResponse>> accountList(
            @RequestParam(value = "pagenum", defaultValue = "0") Integer pagenum,
            @RequestParam(value = "pagesize", defaultValue = "10") Integer pagesize,
            @RequestParam(value = "type", defaultValue = "deposit") String type, String couponNo, String status,
            Long accountId, String sortdatafield, String sortorder) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if(!Objects.isNull(accountId)){
            BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectByPrimaryKey(accountId);
            if (Objects.isNull(bizBillingAccount)){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1047765662));
            }
            if (Objects.isNull(authUserInfo) || Objects.isNull(authUserInfo.getUserSid())) {
                throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
            }
            //解决分销商纵向越权
            if(!Objects.isNull(authUserInfo.getOrgSid()) && FOUR.equals(authUserInfo.getUserType())){
                Org org = orgService.selectByPrimaryKey(bizBillingAccount.getOrgSid());
                if(!org.getTreePath().contains(String.valueOf(authUserInfo.getOrgSid()))){
                    throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
                }
            }
        }
        if (TYPE_DEPOSIT.equals(type) && roleModuleService.countByUserSidAndModuleSids(authUserInfo.getUserSid(), Collections.singletonList(BQ.BQ010806)) == 0) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        } else if (TYPE_DEDUCT.equals(type) && roleModuleService.countByUserSidAndModuleSids(authUserInfo.getUserSid(), Collections.singletonList(BQ.BQ010807)) == 0) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        return feignService.accountList(pagenum,pagesize,type,couponNo,status,accountId,sortdatafield,sortorder);
    }

    /**
     * 收支明细异步导出(费用中心-收支明细)
     *
     * @param request    账户收支明细查询请求体
     * @param moduleType 模块类型
     * @return {@code RestResult}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BC.BC02.BC0202 + COMMA + AuthModule.BQ.BQ01081002)
    @GetMapping(value = "/account_deals/export")
    @ApiOperation(httpMethod = "GET", value = "导出收支明细")
    @SmsValidation
    @OperationLog(type = OperationTypeEnum.EXPORT, tagName = "'收支明细'", tagNameUs ="'Details of income and expenditure'",
            resource = OperationResourceEnum.EXPORT_REVENUE_EXPENSE_DETAILS,param = "#request")
    @Idempotent
    public RestResult expertRegisterUserInfo(@Valid DescribeDealsRequest request, @RequestHeader String moduleType) {
        return feignService.expertRegisterUserInfo(request,moduleType);
    }

    /**
     * 收支明细异步导出(客户管理-客户信息-管理)
     *
     * @param request    账户收支明细查询请求体
     * @param moduleType 模块类型
     * @return {@code RestResult}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BQ.BQ01081002)
    @GetMapping(value = "/account_deals/mgt/export")
    @ApiOperation(httpMethod = "GET", value = "导出收支明细")
    @SmsValidation
    @OperationLog(type = OperationTypeEnum.EXPORT, tagName = "'收支明细'",
            resource = OperationResourceEnum.EXPORT_REVENUE_EXPENSE_DETAILS,param = "#request")
    @Idempotent
    public RestResult expertRegisterUserInfoMgt(DescribeDealsRequest request, @RequestHeader String moduleType) {
        if(StrUtil.isNotBlank(request.getAccountSid())){
            BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectByPrimaryKey(Long.valueOf(request.getAccountSid()));
            cn.com.cloudstar.rightcloud.oss.common.pojo.User authUser = AuthUtil.getAuthUser();
            if (Objects.isNull(authUser) || Objects.isNull(authUser.getUserSid())) {
                throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
            }
            //解决分销商纵向越权
            if(!Objects.isNull(authUser.getOrgSid()) && FOUR.equals(authUser.getUserType())){
                Org org = orgService.selectByPrimaryKey(bizBillingAccount.getOrgSid());
                if(!org.getTreePath().contains(String.valueOf(authUser.getOrgSid()))){
                    throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
                }
            }
        }

        return feignService.expertRegisterUserInfo(request,moduleType);
    }

    /**
     * 账单列表(费用中心-账单管理-账单明细)
     *
     * @param request 成本明细请求体
     * @return {@code IPage<DescribeGaapCostResponse>}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BC.BC03.BC0302)
    @GetMapping("/bills")
    public RestResult<IPage<DescribeGaapCostSimpleResponse>> listBills(DescribeGaapCostRequest request) {
        if (org.apache.commons.lang3.StringUtils.isBlank(request.getPagenum())) {
            request.setPagenum("0");
        }
        if (org.apache.commons.lang3.StringUtils.isBlank(request.getPagesize())) {
            request.setPagesize("10");
        }
        return feignService.listBills(request);
    }
    /**
     * 账单列表(费用中心-账单管理-账单明细)
     *
     * @param request 成本明细请求体
     * @return {@code IPage<DescribeGaapCostResponse>}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BC.BC03.BC0302)
    @GetMapping("/bills/totalRows")
    public RestResult<IPage> billTotalRows(DescribeGaapCostRequest request) {
        return feignService.billTotalRows(request);
    }

    /**
     * 账单列表(客户管理-客户信息-管理)
     *
     * @param request 成本明细请求体
     * @return {@code IPage<DescribeGaapCostResponse>}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BQ.BQ01080901)
    @GetMapping("/mgt/bills")
    public RestResult<IPage<DescribeGaapCostSimpleResponse>> listBillsByMgt(DescribeGaapCostRequest request) {
        return feignService.listBills(request);
    }

    /**
     * 账单列表(客户管理-客户信息-管理)
     *
     * @param request 成本明细请求体
     * @return {@code IPage<DescribeGaapCostResponse>}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BQ.BQ01080901)
    @GetMapping("/mgt/bills/totalRows")
    public RestResult<IPage> billTotalRowsByMgt(DescribeGaapCostRequest request) {
        return feignService.billTotalRows(request);
    }

    /**
     * 下载合同接口
     *
     * @param contractId 合同id
     * @param fileNum    文件编号
     * @param response   响应
     * @since 2.4.0
     */
    @ApiOperation("下载合同")
    @GetMapping("/contract/download/{contractId}")
    @AuthorizeOss(action = AuthModule.BC.BC05.BC0514 + "," + AuthModule.BQ.BQ01080405)
    @OperationLog(type = OperationTypeEnum.EXPORT, tagName = "'合同名称'", resource = OperationResourceEnum.DOWNLOAD_CONTRACT,bizId = "#contractId", tagNameUs ="'Contract Name'")
    public void downloadContract(@PathVariable("contractId") Long contractId, String fileNum, HttpServletResponse response) {
        try {

            // 查询合同文件名称
            Criteria criteria = new Criteria();
            criteria.put(OPERATION_TYPE, CONTRACT);
            criteria.put(FILENUM, fileNum);
            criteria.setOrderByClause("sort_order");
            List<SysMFilePath> sysMFilePaths = sysMFilePathMapper.selectSysMFilePathByParams(criteria);
            if (!CollectionUtils.isEmpty(sysMFilePaths)) {
                response.setHeader("Content-Disposition",
                        "attachment;filename=" + URLEncoder.encode(sysMFilePaths.get(0).getFileName(), "UTF-8"));
            }
            transformation(feignService.downloadContract(contractId,fileNum),response);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取分销商列表（分销管理）
     *
     * @param request 查询分销商请求体
     * @return {@code List<DescribeDistributorTreeResponse>}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BU.BU_COMMON)
    @ApiOperation(httpMethod = "GET", value = "获取分销商列表（树形展示）")
    @GetMapping("/distributors/tree")
    public RestResult<List<DescribeDistributorTreeResponse>> findDistributorsTree(DescribeDistributorRequest request) {
        return feignService.findDistributorsTree(request);
    }

    /**
     * 获取分销商列表（客户管理-更多-关联分销商）
     *
     * @param request 查询分销商请求体
     * @return {@code List<DescribeDistributorTreeResponse>}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BQ.BQ0104)
    @ApiOperation(httpMethod = "GET", value = "获取分销商列表（树形展示）")
    @GetMapping("correlation/distributors/tree")
    public RestResult<List<DescribeDistributorTreeResponse>> findDistributorsTreeCorrelation(DescribeDistributorRequest request) {
        return feignService.findDistributorsTree(request);
    }

    /**
     * 获取分销商列表（用户管理-创建本地用户-分销商管理类）
     *
     * @param request 查询分销商请求体
     * @return {@code List<DescribeDistributorTreeResponse>}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.ZC.ZC01)
    @ApiOperation(httpMethod = "GET", value = "获取分销商列表（树形展示）")
    @GetMapping("/user/distributors/tree")
    public RestResult<List<DescribeDistributorTreeResponse>> findDistributorsTreeByUser(DescribeDistributorRequest request) {
        return feignService.findDistributorsTree(request);
    }

    /**
     * 下载任务列表
     *
     * @param request 下载任务请求体
     * @return {@code RestResult}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.COMMON.PUBLIC.B1.B103 + COMMA + Z1.Z106)
    @GetMapping(value = "/download/list")
    @ApiOperation(httpMethod = "GET", value = "下载任务列表")
    public RestResult<List<BizDownload>> list(@Valid BizDownloadRequest request) {
        return feignService.list(request);
    }

    /**
     * 作废充值现金券
     *
     * @param cancelCashCouponRequest 取消现金优惠券请求体
     * @return {@code RestResult}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BL.BL02.BL020103)
    @ApiOperation(value = "作废充值现金券", httpMethod = "PUT")
    @PutMapping("/cashCoupon/cancel")
    @OperationLog(type = OperationTypeEnum.DELETE, tagName = "'充值现金券'", tagNameUs ="'Recharge cash coupon'",
            resource = OperationResourceEnum.CANCELBALANCECASH,bizId = "#cancelCashCouponRequest.couponNo",param = "#cancelCashCouponRequest")
    public RestResult cancelCashCoupon(@RequestBody @Valid CancelCashCouponRequest cancelCashCouponRequest) {
        cancelCashCouponRequest.setType(TYPE_DEPOSIT);
        return feignService.cancelCashCoupon(cancelCashCouponRequest);
    }

    /**
     * 作废抵扣现金券
     *
     * @param cancelCashCouponRequest 取消现金优惠券请求体
     * @return {@code RestResult}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BL.BL02.BL020203)
    @ApiOperation(value = "作废抵扣现金券", httpMethod = "PUT")
    @PutMapping("/cashCoupon/cancel/deduction")
    @OperationLog(type = OperationTypeEnum.DELETE, tagName = "'抵扣现金券'", tagNameUs ="'Deduction cash coupon'",
            resource = OperationResourceEnum.CANCELBALANCEDEDUCTIONCASH,bizId = "#cancelCashCouponRequest.couponNo",param = "#cancelCashCouponRequest")
    public RestResult cancelCashCouponDeduction(@RequestBody @Valid CancelCashCouponRequest cancelCashCouponRequest) {
        cancelCashCouponRequest.setType(TYPE_DEDUCT);
        return feignService.cancelCashCoupon(cancelCashCouponRequest);
    }

    /**
     * 获取收支明细(费用中心-收支明细)
     *
     * @param request 账户收支明细查询请求体
     * @return {@code IPage<DescribeDealsResponse>}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BC.BC02.BC02_COMMON)
    @ApiOperation(httpMethod = "GET", value = "获取收支明细")
    @GetMapping("/account_deals")
    public RestResult<IPage<DescribeDealsResponse>> getDepositRecords(@Valid DescribeDealsRequest request) {
        return feignService.getDepositRecords(request);
    }

    /**
     * 获取收支明细(客户管理-客户信息-管理)
     *
     * @param request 账户收支明细查询请求体
     * @return {@code IPage<DescribeDealsResponse>}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BQ.BQ01081001)
    @ApiOperation(httpMethod = "GET", value = "获取收支明细")
    @GetMapping("/account_deals/mgt")
    public RestResult<IPage<DescribeDealsResponse>> getDepositRecordsMgt(DescribeDealsRequest request) {
        return feignService.getDepositRecords(request);
    }

    /**
     * 查询内置产品资源
     *
     * @param request 查询内置产品资源请求体
     * @return {@code IPage<DescribeProductResourceResponse>}
     * @since 2.4.0
     */
    @AuthorizeOss(action = BD06.BD06 + COMMA + AuthModuleOss.BQ.BQ010702 + COMMA + BQ.BQ0108180101)
    @ApiOperation("查询内置产品资源")
    @GetMapping("/products/resources")
    public RestResult<IPage<DescribeProductResourceResponse>> listResourcesBy(@Valid DescribeProductResourceRequest request) {
        return feignService.listResourcesByFeign(request);
    }


    /**
     * 获取用户下的密码策略
     *
     * @return {@code PasswordPolicyResponse}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.COMMON.PUBLIC.B1.B104)
    @ApiOperation("获取用户下的密码策略")
    @GetMapping("/access/password_policy")
    public RestResult<PasswordPolicyResponse> getPasswordPolicyByFeign(PasswordPolicyRequest request) {
        return feignService.getPasswordPolicyByFeign(request);
    }

    /**
     * 下载导入结果(客户)
     *
     * @param request  下载文件请求体
     * @param response 响应
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BQ.BQ0109)
    @PostMapping("/download/file")
    @ApiOperation("下载文件")
    @OperationLog(type = OperationTypeEnum.EXPORT, tagName = "'客户导入结果'", resource = OperationResourceEnum.DOWNLOAD_IMPORT_RESULT_FILE,bizId = "#request.num", tagNameUs ="'Customer Import Results'")
    public void downloadFile(@RequestBody DownloadRequest request, HttpServletResponse response) {
        transformation(feignService.downloadFile(request),response);
    }

    /**
     * 下载导入结果(账户)
     *
     * @param request  下载文件请求体
     * @param response 响应
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BQ.BQ0110)
    @PostMapping("/account/download/file")
    @ApiOperation("下载文件")
    @OperationLog(type = OperationTypeEnum.EXPORT, tagName = "'账户导入结果'", resource = OperationResourceEnum.DOWNLOAD_IMPORT_RESULT_FILE,bizId = "#request.num")
    public void downloadFileByAccount(@RequestBody DownloadRequest request, HttpServletResponse response) {
        transformation(feignService.downloadFile(request),response);
    }

    /**
     * 下载导入结果(账户)
     *
     * @param request  下载文件请求体
     * @param response 响应
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.ZI.ZI0202)
    @PostMapping("/hcso/account/download/file")
    @ApiOperation("下载文件")
    @OperationLog(type = OperationTypeEnum.EXPORT, tagName = "'hcso导入结果'", resource = OperationResourceEnum.DOWNLOAD_FILE,param = "#request", tagNameUs ="'Import results'")
    public void downloadFileByHCSOAccount(@RequestBody DownloadRequest request, HttpServletResponse response) {
        transformation(feignService.downloadFile(request),response);
    }

    /**
     * 获取密码配置
     *
     * @param type 类型
     * @return {@code RestResult<PasswordRuleResponse>}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.COMMON.PUBLIC.B1.B105 + COMMA + AuthModule.COMMON.PUBLIC.Z1.Z103)
    @ApiOperation("获取配置")
    @GetMapping("/access/password_config")
    public RestResult<PasswordRuleResponse> pswRuleByFeign(@RequestParam("type") String type) {
        return feignService.pswRuleByFeign(type);
    }

    /**
     * 获取密码配置
     *
     * @param type 类型
     * @return {@code RestResult<PasswordRuleResponse>}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.COMMON.PUBLIC.Z1.Z103)
    @ApiOperation("获取配置")
    @GetMapping("/sys/password_config")
    public RestResult<PasswordRuleResponse> pswRuleBySys(@RequestParam("type") String type) {
        return feignService.pswRuleByFeign(type);
    }

    /**
     * 账户充值
     *
     * @param request 账户充值请求体
     * @return {@code RestResult}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BQ.BQ0105 + "," + AuthModule.BQ.**********)
    @ApiOperation(httpMethod = "POST", value = "账户充值")
    @PostMapping("/billing_account/recharge")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'客户账户'", resource = OperationResourceEnum.CHARGE, bizId = "#request.id", param = "#request", tagNameUs ="'Customer Account'")
    @Idempotent
    public RestResult<Long> recharge(@RequestBody @Valid RechargeBillingAccountRequest request) {
        return processMgtService.verifyAndCreate(request);
    }

    /**
     * 更新信用额度
     *
     * @param request 更新信用额度请求体
     *
     * @return {@code RestResult}
     *
     * @since 2.4.2
     */
    @AuthorizeOss(action = COMMON.CREDIT_LINE)
    @PutMapping("/billing_account/credit_line")
    @ApiOperation("更新信用额度")
    @DataPermission(resource = OperationResourceEnum.CREDIT, bizId = "#request.accountId")
    @ListenExpireBack
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'更新信用额度'", resource = OperationResourceEnum.CREDIT, param = "#request", bizId = "#request.accountId", tagNameUs ="'Renew credit limit'")
    public RestResult updateCreditLine(@Valid @RequestBody UpdateCreditLineRequest request) {
        return processMgtService.verifyAndCreate(request);
    }

    /**
     * 管理员分发优惠劵
     *【Since v2.6.0】
     * @param sid        ID
     * @param distributeRequest 帐户ID
     * @return {@link RestResult}
     */
    @AuthorizeOss(action = BL01.BL0102)
    @ApiOperation("管理员分发优惠劵")
    @PutMapping("/coupons/{sid}/distribute")
    @Idempotent
    @ListenExpireBack
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'分发优惠券'", resource = OperationResourceEnum.DISTRIBUTECOUPON, bizId = "#sid", param ="#accountIds" , tagNameUs ="'Distribute coupons'")
    public RestResult distributeCoupon(@PathVariable("sid") Long sid, @Valid @RequestBody DistributeRequest distributeRequest) {
        // 安全校验
        Set<Long> setAccountIds = new HashSet<>(distributeRequest.getAccountIds());
        Criteria query = new Criteria();
        query.put("entityId", RequestContextUtil.getEntityId());
        query.put("ids", distributeRequest.getAccountIds());
        List<BizBillingAccount> allAccounts = bizBillingAccountMapper.selectByParams(query);
        if (setAccountIds.size() != allAccounts.size()) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        List<Long> userSids = allAccounts.stream().map(BizBillingAccount::getAdminSid).collect(Collectors.toList());
        Criteria criteria = new Criteria();
        criteria.put("status", "1");
        criteria.put("userSidList", userSids);
//        criteria.put("certificationStatus", "authSucceed");
        if (userService.countByParams(criteria) != userSids.size()) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        //账户状态是否冻结
        if (allAccounts.stream().anyMatch(e -> e.getStatus().equals("freeze"))) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2070382864));
        }
        BizCoupon bizCoupon = bizCouponMapper.selectByPrimaryKey(sid);
        if (Objects.isNull(bizCoupon)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        if (bizCoupon.getDeleted()) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_304025433));
        }
        if (new Date().after(bizCoupon.getEndTime())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_438123602));
        }
        return processMgtService.verifyAndCreate(sid, distributeRequest.getAccountIds(),distributeRequest.getRemark());
    }

    /**
     * 创建折扣(折扣管理-平台折扣)
     *
     * @param request 创建折扣请求体
     * @return {@code RestResult}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BR.BR01.BR0101)
    @ApiOperation(httpMethod = "POST", value = "创建折扣")
    @PostMapping("/discount")
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "#request.discountName",
            resource = OperationResourceEnum.DISCOUNT,param = "#request")
    @Idempotent
    public RestResult<Long> createDiscount(@Valid @RequestBody CreateDiscountRequest request) {
        return feignService.createDiscount(request);
    }

    /**
     * 创建折扣(折扣管理-客户折扣)
     *
     * @param request 创建折扣请求体
     * @return {@code RestResult}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BR.BR02.BR0201)
    @ApiOperation(httpMethod = "POST", value = "创建折扣")
    @PostMapping("/customer/discount")
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "#request.discountName",
            resource = OperationResourceEnum.DISCOUNT,param = "#request")
    @Idempotent
    public RestResult<Long> createDiscountByCustomer(@Valid @RequestBody CreateDiscountRequest request) {
        return feignService.createDiscount(request);
    }

    /**
     * 发放现金券(营销管理-现金券管理-充值现金券)
     *
     * @param distributeCashCouponRequest 分发现金券请求体
     * @return {@code RestResult}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BL.BL02.BL020102)
    @ApiOperation(value = "分发现金券", httpMethod = "PUT")
    @PutMapping("/cashCoupon/distribute")
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "'充值现金券'", resource = OperationResourceEnum.DISTRIBUTECASH, bizId = "#distributeCashCouponRequest.couponNo", param = "#distributeCashCouponRequest.accountId", tagNameUs ="'Recharge cash coupon'")
    @Idempotent
    public RestResult distributeCashCoupon(@Valid @RequestBody DistributeCashCouponRequest distributeCashCouponRequest) {
        distributeCashCouponRequest.setType(TYPE_DEPOSIT);
        validateCoupon(distributeCashCouponRequest);
        return processMgtService.verifyAndCreate(distributeCashCouponRequest);
    }

    /**
     * 发放现金券(营销管理-现金券管理-抵扣现金券)
     *
     * @param distributeCashCouponRequest 分发现金券请求体
     * @return {@code RestResult}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BL.BL02.BL020202)
    @ApiOperation(value = "分发现金券", httpMethod = "PUT")
    @PutMapping("/deduction/cashCoupon/distribute")
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "'抵扣现金券'", resource = OperationResourceEnum.DISTRIBUTEDEDUCTIONCASH, bizId = "#distributeCashCouponRequest.couponNo", param = "#distributeCashCouponRequest.accountId", tagNameUs ="'Deduction cash coupon'")
    @Idempotent
    public RestResult distributeCashCouponByDeduction(@Valid @RequestBody DistributeCashCouponRequest distributeCashCouponRequest) {
        distributeCashCouponRequest.setType(TYPE_DEDUCT);
        validateCoupon(distributeCashCouponRequest);
        return processMgtService.verifyAndCreate(distributeCashCouponRequest);
    }

    /**
     * 获取发票详情
     *
     * @param invoiceSid 发票sid
     * @return {@code DescribeInvoiceDetailResponse}
     * @since 2.4.0
     */
    @GetMapping("/invoice/detail/{invoiceSid}")
    @ApiOperation("获取发票详情")
    @AuthorizeOss(action = AuthModule.BC.BC04.BC0401)
    public RestResult<DescribeInvoiceDetailResponse> selectDetailInvoice(@NotNull @PathVariable Long invoiceSid) {
        return feignService.selectDetailInvoice(invoiceSid);
    }

    /**
     * 查看合同
     *
     * @param contractId 合同id
     * @return {@code RestResult}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BC.BC05.BC0509 + "," + AuthModule.BQ.BQ01080404)
    @ApiOperation("查看合同")
    @GetMapping("/contract/view")
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "'合同名称'", resource = OperationResourceEnum.VIEW_CONTRACT,bizId = "#contractId", tagNameUs ="'Contract Name'")
    public RestResult<ViewContractVO> viewContract(@RequestParam("contractId") Long contractId) {
        BizContract bizContract = bizContractMapper.selectById(contractId);
        BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectByPrimaryKey(bizContract.getAccountId());
        if (Objects.nonNull(bizBillingAccount)&&!bizBillingAccount.getEntityId().equals(RequestContextUtil.getEntityId())){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1384912769));
        }

        cn.com.cloudstar.rightcloud.oss.common.pojo.User authUser = AuthUtil.getAuthUser();
        if (Objects.isNull(authUser) || Objects.isNull(authUser.getUserSid())) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        //解决分销商纵向越权
        if(!Objects.isNull(authUser.getOrgSid()) && FOUR.equals(authUser.getUserType())){
            if(!authUser.getOrgSid().equals(bizBillingAccount.getDistributorId())){
                throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
            }
        }
        return feignService.viewContract(contractId);
    }

    /**
     * 查询合同详情
     *
     * @param contractId 合同id
     * @return {@code RestResult}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BC.BC05.BC0506 + "," + AuthModule.BQ.BQ01080402)
    @ApiOperation("查询合同详情")
    @GetMapping("/contract/{contractId}")
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "'合同名称'", resource = OperationResourceEnum.DETAIL_CONTRACT,bizId = "#contractId", tagNameUs ="'Contract Name'")
    public RestResult<DescribeContractDetailResponse> detailContract(@PathVariable("contractId") Long contractId) {
        return feignService.detailContract(contractId);
    }

    /**
     * 下载文件(费用中心-账单管理-账单周期-下载)
     * 已弃用
     * @param num      文件编号
     * @param response 响应
     * @since 2.4.0
     */
//    @AuthorizeOss(action = AuthModule.BC.BC03.BC030102)
//    @GetMapping("/download/billingCycle/file")
    @ResponseBody
    @SmsValidation
    @ApiOperation("下载文件")
    @OperationLog(type = OperationTypeEnum.EXPORT, tagName = "'账单周期'", resource = OperationResourceEnum.DOWNLOAD_BILLING_CRYLE, tagNameUs ="'Billing cycle'",
    bizId = "#num")
    @Deprecated
    public void downloadBillingCycleFile(@ApiParam("文件路径") @RequestParam("num") String num, HttpServletResponse response) {
        transformation(feignService.downloadFile(num),response);
    }

    /**
     * 下载文件(费用中心-账单管理-账单明细-下载)
     *
     * @param num      文件编号
     * @param response 响应
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BC.BC03.BC030202)
    @GetMapping("/download/billingDetail/file")
    @ResponseBody
    @ApiOperation("下载文件")
    @OperationLog(type = OperationTypeEnum.EXPORT, tagName = "'账单明细'", resource = OperationResourceEnum.DOWNLOAD_BILLING_DETAILS, bizId = "#num")
    public void downloadBillingDetailFile(@ApiParam("文件路径") @RequestParam("num") String num, HttpServletResponse response) {
        try {
            String destFileName = new SimpleDateFormat("yyyyMMddHHmm'.xlsx'").format(new Date());

            cn.com.cloudstar.rightcloud.oss.module.export.bean.BizDownload download = bizDownloadMapper.selectByBownloadNum(num);
            if (download != null && StringUtils.isNotBlank(download.getFileName())) {
                destFileName = download.getFileName();
            }

            response.setHeader("Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode(destFileName, "UTF-8"));
            transformation(feignService.downloadFile(num),response);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 【Since v2.5.0】下载文件(营销管理-套餐包管理-订购明细-使用明细-下载)
     *
     * @param num      文件编号
     * @param response 响应
     */
    @AuthorizeOss(action = AuthModule.BC.BC03.BC030202)
    @GetMapping("/download/bizBagBillDetail/file")
    @ResponseBody
    @ApiOperation("下载文件")
    @OperationLog(type = OperationTypeEnum.EXPORT, tagName = "'套餐包使用明细'", resource = OperationResourceEnum.DOWNLOAD_BILLING_DETAILS)
    public void downloadBizBagBillDetailFile(@ApiParam("文件路径") @RequestParam("num") String num, HttpServletResponse response) {
        try {
            String destFileName = new SimpleDateFormat("yyyyMMddHHmm'.zip'").format(new Date());
            response.setHeader("Content-Disposition",
                               "attachment;filename=" + URLEncoder.encode(destFileName, "UTF-8"));
            transformation(feignService.downloadFile(num),response);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 【Since v2.5.0】下载文件(营销管理-套餐包管理-订购明细-下载)
     *
     * @param num      文件编号
     * @param response 响应
     */
    @AuthorizeOss(action = AuthModule.BL.BL03.BL030802)
    @GetMapping("/download/bizBagOrder/file")
    @ResponseBody
    @ApiOperation("下载文件")
    @OperationLog(type = OperationTypeEnum.EXPORT, tagName = "'订单'", resource = OperationResourceEnum.DOWNLOAD_ORDER, tagNameUs ="'Order'")
    public void downloadBizTagOrderFile(@ApiParam("文件路径") @RequestParam("num") String num, HttpServletResponse response) {
        try {
            String destFileName = new SimpleDateFormat("yyyyMMddHHmm'.zip'").format(new Date());
            response.setHeader("Content-Disposition",
                               "attachment;filename=" + URLEncoder.encode(destFileName, "UTF-8"));
            transformation(feignService.downloadFile(num),response);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 下载文件(费用中心-订单管理-下载)
     *
     * @param num      文件编号
     * @param response 响应
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BC.BC01.BC0102)
    @GetMapping("/download/order/file")
    @ResponseBody
    @ApiOperation("下载文件")
    @OperationLog(type = OperationTypeEnum.EXPORT, tagName = "'订单'",bizId = "#num", resource = OperationResourceEnum.DOWNLOAD_ORDER, tagNameUs ="'Order'")
    public void downloadOrderFile(@ApiParam("文件路径") @RequestParam("num") String num, HttpServletResponse response) {
        try {
            String destFileName = new SimpleDateFormat("yyyyMMddHHmm'.zip'").format(new Date());
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode(destFileName, "UTF-8"));
            transformation(feignService.downloadOrderFile(num), response);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 下载文件(费用中心-收支明细-下载)
     *
     * @param num      文件编号
     * @param response 响应
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BC.BC02.BC0202 + "," + AuthModule.BQ.BQ01081002)
    @GetMapping("/download/incomeExpenditure/file")
    @ResponseBody
    @ApiOperation("下载文件")
    @OperationLog(type = OperationTypeEnum.EXPORT, tagName = "'收支明细'", bizId = "#num", resource = OperationResourceEnum.DOWNLOAD_INCOME_AND_EXPENDITURE_DETAILS)
    public void downloadIncomeExpenditureFile(@ApiParam("文件路径") @RequestParam("num") String num, HttpServletResponse response) {
        try {
            String destFileName = new SimpleDateFormat("yyyyMMddHHmm'.xlsx'").format(new Date());

            cn.com.cloudstar.rightcloud.oss.module.export.bean.BizDownload download = bizDownloadMapper.selectByBownloadNum(num);
            if (download != null && StringUtils.isNotBlank(download.getFileName())) {
                destFileName = download.getFileName();
            }
            AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
            if (authUserInfo == null) {
                throw new cn.com.cloudstar.rightcloud.common.exception.BizException("获取当前登录用户失败");
            }
            if(!authUserInfo.getEntityId().equals(download.getEntityId())){
                throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
            }

            response.setHeader("Content-Disposition",
                               "attachment;filename=" + URLEncoder.encode(destFileName, "UTF-8"));
            transformation(feignService.downloadFile(num),response);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 查询用户组(客户管理-账户管理-创建)
     *
     * @param request 查询用户组请求体
     *
     * @return {@code List<DescribeUserGroupResponse>}
     * @since 2.4.0
     */
    @GetMapping("/access/user_group")
    @ApiOperation("查询用户组")
    @AuthorizeOss(action = AuthModule.BQ.BQ03.BQ03)
    public RestResult<List<DescribeUserGroupResponse>> queryUserGroupByUser(DescribeUserGroupRequest request) {
        return feignService.queryUserGroupByUser(request);
    }

    /**
     * 查询用户组(客户管理-账户管理-子账号管理-创建)
     *
     * @param request 查询用户组请求体
     *
     * @return {@code List<DescribeUserGroupResponse>}
     * @since 2.4.0
     */
    @GetMapping("/access/account/user_group")
    @ApiOperation("查询用户组")
    @AuthorizeOss(action = AuthModule.BQ.BQ03.BQ0304)
    public RestResult<List<DescribeUserGroupResponse>> queryUserGroupByAccount(@Valid DescribeUserGroupRequest request) {
        return feignService.queryUserGroupByUser(request);
    }

    /**
     * 查询子用户
     *
     * @param request 查询用户请求体
     *
     * @return {@code List<DescribeSubusersResponse>}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.COMMON.PUBLIC.B1.B111 + "," + AuthModule.BQ.BQ03.BQ03)
    @ApiOperation("查询子用户")
    @GetMapping("/access/subuser")
    @DataPermission(resource = OperationResourceEnum.SUB_USER_LIST, bizId = "#request.orgSid")
    public RestResult<List<DescribeSubusersResponse>> querySubuser(@Valid DescribeSubuserRequest request) {
        return feignService.querySubuser(request);
    }

    /**
     * 获取下载任务
     *
     * @param downloadId 下载id
     * @return {@code BizDownload}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BQ.BQ0109)
    @GetMapping("/download")
    public RestResult<BizDownload> getById(@RequestParam("downloadId") Long downloadId) {
        return feignService.getById(downloadId);
    }

    /**
     * 获取下载任务
     *
     * @param downloadId 下载id
     * @return {@code BizDownload}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BQ.BQ0110)
    @GetMapping("/account/download")
    public RestResult<BizDownload> getAccountById(@RequestParam("downloadId") Long downloadId) {
        return feignService.getById(downloadId);
    }


    /**
     * 查询产品(feign调用使用)
     *
     * @param request 查询产品请求体
     * @return {@code IPage<DescribeProductResponse>}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.COMMON.PUBLIC.B1.B112)
    @GetMapping("/products")
    @ApiOperation("查询产品")
    public RestResult<IPage<DescribeProductResponse>> listProductsByFeign(DescribeProductRequest request) {
        return feignService.listProductsByFeign(request);
    }

    /**
     * 获取用户所属项目(feign调用使用)d
     *
     * @param userSid 用户Sid
     * @return {@code List<DescribeProjectSimpleResponse>}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BQ.BQ010701)
    @GetMapping("/access/projects/{userSid}/project")
    @ApiOperation("查询产品")
    public RestResult<List<DescribeProjectSimpleResponse>> OssfindSimpleProjectsByFeign(@PathVariable("userSid")
                                                                                @ApiParam(value = "用户ID", type = "Long", required = true) Long userSid) {
        return feignService.OssfindSimpleProjectsByFeign(userSid);
    }

    /**
     * 询价
     *
     * @param request 服务申请请求体
     * @return {@code List<InquiryPriceResponse>}
     * @since 2.4.0
     */
    @PostMapping("/billing/price")
    @AuthorizeOss(action = AuthModule.COMMON.PUBLIC.B1.B113)
    public RestResult<List<InquiryPriceResponse>> inquiryPrice(@Valid @RequestBody ApplyServiceRequest request) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (Objects.nonNull(request.getBehalfUserSid())) {
            if (UserType.DISTRIBUTOR_USER.equals(authUserInfo.getUserType()) && Objects.nonNull(request.getUserSid())) {
                BizBillingAccount account = bizBillingAccountService.getByUserSidAndEntityId(request.getUserSid(), RequestContextUtil.getEntityId());
                if (Objects.isNull(account) || !Objects.equals(account.getDistributorId(), authUserInfo.getOrgSid())) {
                    throw new BizException(WebUtil.getMessage(cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd.PARAM_NOT_VALID_ERROR));
                }
            }
        }
        return feignService.inquiryPrice(request);
    }


    /**
     * 【Since v2.5.0】计算价格
     *
     * @param request 续费询价请求体
     * @return {@code InquiryPriceResponse}
     */
    @PostMapping("/billing/renew/inquiry/price")
    @AuthorizeOss(action = AuthModule.BQ.BQ010701)
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "'计算价格'", resource = OperationResourceEnum.CALCULATE_PRICE, bizId = "#request.id", tagNameUs ="'Calculate the price'")
    @DataPermission(resource = OperationResourceEnum.CALCULATE_PRICE, bizId = "#request.userSid")
    public RestResult<InquiryPriceResponse> calculatePriceFeign(
            @Valid @RequestBody InquiryRenewPriceRequest request) {
        AuthUser userInfo = BasicInfoUtil.getCurrentUserInfo();
        if (userInfo == null || !FROM_BSS.equals(userInfo.getRemark())) {
            throw new cn.com.cloudstar.rightcloud.common.exception.BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
        }
        return feignService.calculatePriceFeign(request);
    }

    /**
     * 【Since v2.5.0】续费
     *
     * @param request 续订请求体
     * @return {@code RestResult}
     */
    @ApiOperation("续费")
    @PostMapping("/renew")
    @AuthorizeOss(action = BQ010701)
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'续费'", bizId = "#request.userSid", tagNameUs ="'Renewal'",
            resource = OperationResourceEnum.RENEW_RESOURCE, param = "#request")
    @ListenExpireBack
    public RestResult renewResourceFeign(@RequestBody @Valid RenewRequest request) {
        AuthUser userInfo = BasicInfoUtil.getCurrentUserInfo();
        if (userInfo == null || !FROM_BSS.equals(userInfo.getRemark())) {
            throw new cn.com.cloudstar.rightcloud.common.exception.BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
        }
        return feignService.renewResourceFeign(request);
    }

    /**
     * 修改查询价格
     *
     * @param request 变更询价请求体
     * @return {@code ModifyQueryPriceResponse}
     */
    @GetMapping("/billing/modify/inquiry/price")
    @AuthorizeOss(action = AuthModule.COMMON.PUBLIC.B1.B113)
    public RestResult<ModifyQueryPriceResponse> modifyInquiryPrice(@Valid ModifyInquiryPriceRequest request) {
        cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if(!request.getBehalfUserSid().equals(authUserInfo.getUserSid())){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1035468590));
        }
        return feignService.modifyInquiryPriceOss(request);
    }

    /**
     * 分页查询抵扣现金券抵扣记录
     *
     * @param request 查询抵扣现金券抵扣记录请求体
     * @return {@code IPage<DeductCashCouponRecordResponse>}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BL.BL02.BL0202)
    @ApiOperation(value = "分页查询抵扣现金券抵扣记录", httpMethod = "GET")
    @GetMapping("/cashCoupon/deductRecord")
    public RestResult<IPage<DeductCashCouponRecordResponse>> deductRecordList(@Valid DeductCashCouponRecordRequest request) {
        return feignService.deductRecordList(request);
    }

    /**
     * 查询合同列表
     *
     * @param request 合同查询请求体
     * @return {@code RestResult}
     * @since 2.4.0
     */

  @AuthorizeOss(action = AuthModule.BC.BC05.BC05_COMMMON + "," + AuthModule.BQ.********** + "," + AuthModule.BQ.********** + "," + AuthModule.BQ.**********)
    @ApiOperation("查询合同列表")
    @GetMapping("/contract")
    public RestResult<List<DescribeContractResponse>> queryList(@Valid DescribeBizContractRequest request) {
        return feignService.queryList(request);
    }
    /**
     * 退订
     *
     * @param id           id
     * @param userSid      用户sid
     * @param unsubAmount  退订金额
     * @param type         类型
     * @param originStatus 开始状态
     * @return {@code RestResult}
     * @since 2.4.0
     */
    @ApiOperation("退订")
    @DeleteMapping("/service/unsubscribe/{id}")
    @OperationLog(type = OperationTypeEnum.DELETE, tagName ="'退订'", bizId = "#id", resource = OperationResourceEnum.UNSUBSCRIBE, tagNameUs ="'Unsubscribe'")
    @GlobalTransactional
    @AuthorizeOss(action = AuthModule.BQ.BQ010701)
    public RestResult unsubscribe(@PathVariable String id,
                                  @RequestParam(value = "userSid", required = false) Long userSid,
                                  @RequestParam(value = "unsubAmount", required = false) BigDecimal unsubAmount,
                                  @StartWithWord(words = {"SFS", "HPC"}, message = "该产品不存在" ) @RequestParam(required = false) String type,
                                  @RequestParam(required = false) String originStatus,
                                  @RequestParam(required = false) String applyType) {
        return feignService.unsubscribe(id, userSid, unsubAmount, type, originStatus,applyType);
    }

    /**
     * 创建分销商账户(分销管理-创建分销商账户)
     *
     * @param request 创建分销商用户请求体
     * @return {@code RestResult}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BU.BU06)
    @ApiOperation(httpMethod = "POST", value = "创建分销商账户")
    @PostMapping("/distributors/user")
    @Encrypt
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "#request.account",
            resource = OperationResourceEnum.CREATE_DISTRIBUTOR_USER,param = "#request")
    public RestResult<Long> createDistributorUser(@Valid @RequestBody CreateDistributorUserRequest request) {
        Role role = roleService.selectByPrimaryKey(request.getRoleId());
        if (Objects.isNull(role) || !RoleType.DISTRIBUTOR.equals(role.getRoleType())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        try {
            RestResult restResult = feignService.createDistributorUser(request);
            //日志处理数据脱敏
            request.setEmail(DataProcessingUtil.processing(request.getEmail(), DataProcessingUtil.EMAIL));
            request.setMobile(DataProcessingUtil.processing(request.getMobile(), DataProcessingUtil.PHONE));
            request.setPassword(DataProcessingUtil.processing(request.getPassword(), DataProcessingUtil.CIPHER));
            request.setRealName(DataProcessingUtil.processing(request.getRealName(), DataProcessingUtil.NAME));
            return restResult;
        } catch (Exception e) {
            //日志处理数据脱敏
            request.setEmail(DataProcessingUtil.processing(request.getEmail(), DataProcessingUtil.EMAIL));
            request.setMobile(DataProcessingUtil.processing(request.getMobile(), DataProcessingUtil.PHONE));
            request.setPassword(DataProcessingUtil.processing(request.getPassword(), DataProcessingUtil.CIPHER));
            request.setRealName(DataProcessingUtil.processing(request.getRealName(), DataProcessingUtil.NAME));
            if (e instanceof BizException) {
                e.printStackTrace();
                throw new BizException(e.getMessage());
            } else {
                e.printStackTrace();
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_609387497));
            }
        }
    }

    /**
     * 创建分销商账户(用户管理-创建分销商账户)
     *
     * @param request 创建分销商用户请求体
     * @return {@code RestResult}
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.ZC.ZC01)
    @ApiOperation(httpMethod = "POST", value = "创建分销商账户")
    @PostMapping("/distributors/sys/user")
    @Encrypt
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "#request.account",
            resource = OperationResourceEnum.CREATE_DISTRIBUTOR_USER,param = "#request")
    public RestResult<Long> createDistributorUserBySys(@Valid @RequestBody CreateDistributorUserRequest request) {
        Role role = roleService.selectByPrimaryKey(request.getRoleId());
        if (Objects.isNull(role) || !RoleType.DISTRIBUTOR.equals(role.getRoleType())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        try {
            RestResult distributorUser = feignService.createDistributorUser(request);
            request.setRealName(DataProcessingUtil.processing(request.getRealName(),DataProcessingUtil.NAME));
            request.setPassword("********");
            request.setMobile(DataProcessingUtil.processing(request.getMobile(),DataProcessingUtil.PHONE));
            request.setEmail(DataProcessingUtil.processing(request.getEmail(),DataProcessingUtil.EMAIL));
            return distributorUser;
        } catch (Exception e) {
            e.printStackTrace();
            request.setRealName(DataProcessingUtil.processing(request.getRealName(),DataProcessingUtil.NAME));
            request.setPassword("********");
            request.setMobile(DataProcessingUtil.processing(request.getMobile(),DataProcessingUtil.PHONE));
            request.setEmail(DataProcessingUtil.processing(request.getEmail(),DataProcessingUtil.EMAIL));
            if (e instanceof BizException) {
                throw new BizException(e.getMessage());
            } else {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_609387497));
            }
        }
    }

    /**
     * 预览合同
     *
     * @param viewContractVO 查看合同签证官
     * @param response       响应
     * @since 2.4.0
     */
    @AuthorizeOss(action = AuthModule.BC.BC05.BC0509 + "," + AuthModule.BQ.BQ01080404)
    @ApiOperation("预览合同")
    @GetMapping("/contract/viewImage")
    public void viewImageContract(ViewContractVO viewContractVO, HttpServletResponse response) {
        response.setContentType("image/png");
        InputStream inputStream = null;
        Response fileInputStream = feignService.viewImageContract(viewContractVO);
        try {
            inputStream = fileInputStream.body().asInputStream();
            response.getOutputStream().write(ZipUtil.toOutputStream(inputStream).toByteArray());
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            IOUtils.closeQuietly(inputStream);
        }
    }

    /**
     * 【Since v2.5.0】修改资源冻结策略
     *
     * @param request 请求
     * @return {@code RestResult}
     */
    @ApiOperation(httpMethod = "PUT", value = "")
    @PutMapping("/modify/freezing_strategy")
    @AuthorizeOss(action = AuthModule.BD.BD06.UPDATE_STRATEGY)
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'修改实例冻结策略'", bizId = "#request.id", resource = OperationResourceEnum.UPDATE_RESOURCE_STRATEGY,param = "#request", tagNameUs ="'Modify instance freezing policy'")
    public RestResult editFreezingStrategy(@RequestBody @Validated EditFreezingStrategyRequest request) {
        return feignService.editFreezingStrategy(request);
    }

    // feign 调用流转换
    public void transformation(Response fileInputStream,HttpServletResponse response){
        Collection<String> code = fileInputStream.headers().get("code");
        if (Objects.nonNull(code) && code.contains(String.valueOf(RestConst.BizError.BIZ_ERROR.getCode()))) {
            try (BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(fileInputStream.body().asInputStream(), Charset.forName("UTF-8")))) {
                StringBuilder errorBody = new StringBuilder();
                String line;
                while ((line = bufferedReader.readLine()) != null) {
                    errorBody.append(line);
                }
                RestResult restResult = JSON.parseObject(errorBody.toString(), RestResult.class);
                throw new BizException(restResult.getMessage().toString());
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        InputStream in = null;
        try {
            if (fileInputStream.headers().get("Access-Control-Expose-Headers") != null) {
                response.setHeader("Access-Control-Expose-Headers",
                        String.join("", fileInputStream.headers().get("Access-Control-Expose-Headers")));
            }

            if (fileInputStream.headers().get("Content-Type") != null) {
                response.setContentType(String.join("", fileInputStream.headers().get("Content-Type")));
            }
            response.setCharacterEncoding("UTF-8");
            if (fileInputStream.headers().get("Content-Disposition") != null) {
                response.setHeader("Content-Disposition", String.join("", fileInputStream.headers().get("Content-Disposition")));
            }
            in = fileInputStream.body().asInputStream();
            ByteArrayOutputStream out = ZipUtil.toOutputStream(in);
            response.getOutputStream().write(out.toByteArray());
        } catch (Exception e) {
            try {
                String body = IoUtil.read(fileInputStream.body().asReader(StandardCharsets.UTF_8));
                RestResult restResult = JSON.parseObject(body, RestResult.class);
                if (!restResult.getStatus()) {
                    throw new BizException(restResult.getMessage().toString());
                }
            } catch (IOException io) {
                throw new RuntimeException(io);
            }
            throw new RuntimeException(e);
        } finally {
            IOUtils.closeQuietly(in);
        }
    }


    /**
     * 实例退订资源询价
     *
     * @since 2.4.2
     * @return {@code RestResult}
     */

    @AuthorizeOss(action = AuthModule.BD.BD06.BD0603.BD060303 + "," + AuthModule.BQ.BQ0107)
    @ApiOperation("实例退订资源询价")
    @GetMapping("/ai/unsubscribe/inquiry/price/{id}")
    public RestResult unsubscribeAIInquiryPrice(@PathVariable("id") String id) {
        return feignService.unsubscribeAIInquiryPrice(id);
    }


    /**
     * 实例详情列表
     *
     * @since 2.4.2
     * @return {@code RestResult}
     */

    @AuthorizeOss(action = AuthModule.BD.BD06.BD0603.BD060303 + "," + AuthModule.BQ.BQ0107)
    @ApiOperation("实例详情列表")
    @GetMapping("/ai/products/resources/{id}")
    public RestResult getDetail(@PathVariable("id") String id) {
        return feignService.getDetail(id);
    }

    /**
     * 申请服务
     *
     * @param request 服务申请请求体
     * @return {@code RestResult}
     */
    @AuthorizeOss(action = BQ010701)
    @ApiOperation("申请服务")
    @PostMapping("/service/apply")
    @ListenExpireBack
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "#request.productName",
            resource = OperationResourceEnum.VALET_ORDER,bizId = "#request.projectId",param = "#request")
    @Idempotent
    public RestResult applyFeign(@Valid @RequestBody ApplyServiceRequest request) {
        return feignService.apply(request);
    }

    /**
     * 查询统一到期时间
     *
     * @return {@code RestResult}
     */
    @AuthorizeOss(action = BQ010701)
    @ApiOperation("查询统一到期时间")
    @GetMapping("/renew/unify_date")
    public RestResult queryUnifyDate() {
        return feignService.renew();
    }

    /**
     * 修改统一到期时间
     *
     * @param request 修改统一到期时间请求体
     * @return {@code RestResult}
     */
    @AuthorizeOss(action = BQ010701)
    @ApiOperation("查询统一到期时间")
    @PutMapping("/renew/unify_date")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName ="#request.unifyDate", resource = OperationResourceEnum.MODIFY_UNIFYDATE)
    public RestResult modifyUnifyDate(@RequestBody @Valid ModifyUnifyDateRequest request) {
        return feignService.queryUnifyDateUpdate(request);
    }

    /**
     * 计算到期时间
     *
     * @param request 计算到期时间请求体
     * @return {@code CalculateDateResponse}
     */
    @AuthorizeOss(action = BQ010701)
    @ApiOperation("计算到期时间")
    @GetMapping("/renew/date")
    public RestResult calculateDateFeign(@Valid CalculateDateRequest request) {
        return feignService.calculateDate(request);
    }

    /**
     * 判断是否关联计费产品
     * @return
     */
    @AuthorizeOss(action = BQ010701)
    @GetMapping("/billing/isNonBillProduct")
    public RestResult isNonBillProduct(IsNonBillProductRequest request) {
        return feignService.isNonBillProduct(request);
    }

    /**
     * 查询配额基础信息
     * @return
     */
    @GetMapping("/quota/basic")
    public RestResult queryQuotaBasicInfo() {
        return new RestResult(cfnService.queryQuotaBasicInfo());
    }

    /**
     * ai-bms共享资源池开通关闭
     * @return
     */
    @PutMapping("/pool/status/{status}")
    public RestResult updatePoolStatus(@PathVariable String status) {
        UpdatePoolUserReq req=new UpdatePoolUserReq();
        final AuthUser authUser = AuthUserHolder.getAuthUser();
        if(ObjectUtil.isEmpty(authUser)){
            throw new BizException("未获取到登录用户信息");
        }
        if (!("open".equals(status) || "close".equals(status))) {
            throw new BizException("参数取值异常");
        }
        req.setAccount(authUser.getAccount());
        if("open".equals(status)){
            req.setEvent("add");
        }else{
            req.setEvent("delete");
        }
        return new RestResult(cfnService.updatePoolUser(req));
    }

    /**
     * ai-bms共享资源池开通关闭
     * @return
     */
    @GetMapping("/pool/status")
    public RestResult queryPoolStatus() {
        QueryPoolStatusReq req=new QueryPoolStatusReq();
        final AuthUser authUser = AuthUserHolder.getAuthUser();
        if(ObjectUtil.isEmpty(authUser)){
            throw new BizException("未获取到登录用户信息");
        }
        req.setAccount(authUser.getAccount());
        return new RestResult(cfnService.queryPoolStatus(req));
    }


    private void validateCoupon(DistributeCashCouponRequest distributeCashCouponRequest) {
        Set<String> couponNos = new HashSet<>(distributeCashCouponRequest.getCouponNo());
        Criteria query = new Criteria("couponNos", couponNos);
        query.put("type", distributeCashCouponRequest.getType());
        List<CashCoupon> cashCoupons = cashCouponMapper.selectByParams(query);
        if (CollectionUtils.isEmpty(cashCoupons) || cashCoupons.size() != couponNos.size()) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        cashCoupons.forEach(e -> {
            if (!e.getEntityId().equals(RequestContextUtil.getEntityId())) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1384912769));
            }
            // 验证现金券是否是未分发状态
            if (!"undistributed".equals(e.getStatus())
                    && !"approvaling".equals(e.getStatus())) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1026419822));
            }
        });

        Set<Long> accountIds = new HashSet<>(distributeCashCouponRequest.getAccountId());
        Criteria criteria = new Criteria("ids", accountIds).put("status", "freeze");
        //账户状态是否冻结
        List<BizBillingAccount> bizBillingAccounts = bizBillingAccountMapper.selectByParams(criteria);
        if (bizBillingAccounts.size() == accountIds.size()) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        if (bizBillingAccounts.stream().anyMatch(e -> e.getStatus().equals("freeze"))) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2070382864));
        }
    }

}
