package cn.com.cloudstar.rightcloud.oss.common.logs;

import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnv;
import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnvAccount;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.exception.BizException;
import cn.com.cloudstar.rightcloud.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.common.util.CrytoUtilSimple;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.core.pojo.constant.DiscountPolicyEnum;
import cn.com.cloudstar.rightcloud.core.pojo.constant.SysMNotifyConfigConstant;
import cn.com.cloudstar.rightcloud.core.pojo.dto.audit.ServiceOrder;
import cn.com.cloudstar.rightcloud.core.pojo.dto.audit.ServiceOrderDetail;
import cn.com.cloudstar.rightcloud.core.pojo.dto.audit.ServiceOrderResourceRef;
import cn.com.cloudstar.rightcloud.core.pojo.dto.bss.BizCoupon;
import cn.com.cloudstar.rightcloud.core.pojo.dto.bss.BizDiscount;
import cn.com.cloudstar.rightcloud.core.pojo.dto.charge.BizBillingAccount;
import cn.com.cloudstar.rightcloud.core.pojo.dto.cloud.CloudDeployment;
import cn.com.cloudstar.rightcloud.core.pojo.dto.sfs.ServiceCategory;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.BizCustomerActionLog;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.SysMIpConfig;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.SysMNotifyConfig;
import cn.com.cloudstar.rightcloud.core.pojo.dto.ticket.TicketCategory;
import cn.com.cloudstar.rightcloud.core.pojo.dto.ticket.WorkTicket;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.Org;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.Project;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.Role;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicInfoUtil;
import cn.com.cloudstar.rightcloud.operationlog.dto.OperationLogBaseDto;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;
import cn.com.cloudstar.rightcloud.operationlog.service.OperationLogPostProcessor;
import cn.com.cloudstar.rightcloud.operationlog.util.JacksonUtils;
import cn.com.cloudstar.rightcloud.oss.common.constants.Constants;
import cn.com.cloudstar.rightcloud.oss.common.enums.ProcessBusinessEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.ProductComponentEnum;
import cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.oss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.oss.module.Bill.pojo.AddTypeFamilyRequest;
import cn.com.cloudstar.rightcloud.oss.module.Bill.pojo.ConfigRegionChargeRequest;
import cn.com.cloudstar.rightcloud.oss.module.Bill.pojo.TypeFamilyVo;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.model.BizContract;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.model.User;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.company.DeleteUserFromCompanyRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.org.CreateUserToOrgRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.org.UpdateAccountRequest;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.request.user.*;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.template.BillingAccountTemplate;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.template.SubUserTemplate;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.org.BizContractMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.user.RoleMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.user.UserMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.enums.UserSensitiveEnum;
import cn.com.cloudstar.rightcloud.oss.module.account.service.org.OrgService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.project.ProjectService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.user.UserService;
import cn.com.cloudstar.rightcloud.oss.module.collector.bean.request.CollectorRepairRequest;
import cn.com.cloudstar.rightcloud.oss.module.collector.dao.CloudEnvMapper;
import cn.com.cloudstar.rightcloud.oss.module.coupon.bean.CashCoupon;
import cn.com.cloudstar.rightcloud.oss.module.coupon.dao.CashCouponMapper;
import cn.com.cloudstar.rightcloud.oss.module.export.bean.BizDownload;
import cn.com.cloudstar.rightcloud.oss.module.export.dao.BizDownloadMapper;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.*;
import cn.com.cloudstar.rightcloud.oss.module.maintenance.bean.tag.model.CloudTagVO;
import cn.com.cloudstar.rightcloud.oss.module.maintenance.bean.tag.request.CreateBindTagBatchRequest;
import cn.com.cloudstar.rightcloud.oss.module.maintenance.bean.tag.request.CreateCloudTagBindRequest;
import cn.com.cloudstar.rightcloud.oss.module.msg.bean.SysMMsgReceiveContact;
import cn.com.cloudstar.rightcloud.oss.module.msg.bean.SysMMsgUserConfig;
import cn.com.cloudstar.rightcloud.oss.module.msg.bean.request.SysMMsgReceiveContactInsertReq;
import cn.com.cloudstar.rightcloud.oss.module.msg.bean.request.SysMMsgReceiveContactReq;
import cn.com.cloudstar.rightcloud.oss.module.msg.bean.request.SysMMsgUserConfigUpdateReq;
import cn.com.cloudstar.rightcloud.oss.module.msg.dao.SysMMsgReceiveContactMapper;
import cn.com.cloudstar.rightcloud.oss.module.msg.dao.SysMMsgUserConfigMapper;
import cn.com.cloudstar.rightcloud.oss.module.notice.bean.entity.SysMNotice;
import cn.com.cloudstar.rightcloud.oss.module.notice.bean.request.NoticeUpdateRequest;
import cn.com.cloudstar.rightcloud.oss.module.notice.service.ISysMNoticeService;
import cn.com.cloudstar.rightcloud.oss.module.operate.bean.request.RelatedProductsRequest;
import cn.com.cloudstar.rightcloud.oss.module.operationcore.dao.process.BindProcessMapper;
import cn.com.cloudstar.rightcloud.oss.module.order.dao.BizCouponMapper;
import cn.com.cloudstar.rightcloud.oss.module.order.dao.BizDiscountMapper;
import cn.com.cloudstar.rightcloud.oss.module.order.dao.ServiceOrderDetailMapper;
import cn.com.cloudstar.rightcloud.oss.module.order.dao.ServiceOrderMapper;
import cn.com.cloudstar.rightcloud.oss.module.order.dao.ServiceOrderResourceRefMapper;
import cn.com.cloudstar.rightcloud.oss.module.order.service.ServiceOrderService;
import cn.com.cloudstar.rightcloud.oss.module.others.dao.BizCustomerActionLogMapper;
import cn.com.cloudstar.rightcloud.oss.module.others.dao.ticket.TicketCategoryMapper;
import cn.com.cloudstar.rightcloud.oss.module.others.dao.ticket.WorkTicketMapper;
import cn.com.cloudstar.rightcloud.oss.module.pricing.dao.priceconfig.BizBillingAccountMapper;
import cn.com.cloudstar.rightcloud.oss.module.pricing.dao.priceconfig.BizBillingRegionResourceMapper;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.bean.model.SfProductResource;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.bean.request.ApproveOrderRequest;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.dao.SfProductResourceMapper;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.service.ServiceCategoryService;
import cn.com.cloudstar.rightcloud.oss.module.system.bean.config.model.IpAddressVO;
import cn.com.cloudstar.rightcloud.oss.module.system.bean.config.request.*;
import cn.com.cloudstar.rightcloud.oss.module.system.bean.hcso.vo.HcsoUserTemplateVO;
import cn.com.cloudstar.rightcloud.oss.module.system.dao.config.SysMIpConfigMapper;
import cn.com.cloudstar.rightcloud.oss.module.system.dao.config.SysMNotifyConfigMapper;
import cn.com.cloudstar.rightcloud.oss.module.system.service.config.SysConfigService;
import cn.com.cloudstar.rightcloud.oss.module.ticket.request.CreateUpdateTicketCategoryRequest;
import cn.com.cloudstar.rightcloud.remote.api.iam.pojo.HcsoUser;
import cn.com.cloudstar.rightcloud.remote.api.iam.service.HcsoUserRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.ResHpcClusterRemoteModule;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.user.ResUserCredential;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.cloud.CloudEnvAccountRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.cloud.CloudEnvRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.user.ResUserCredentialRemoteService;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ClassUtils;
import org.yaml.snakeyaml.util.UriEncoder;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 自定义 日志前后置处理器
 *
 * @author: mwy
 * @date: 2022/11/3 14:55
 */
@Slf4j
@Component
public class CustomOperationLogPostProcessor implements OperationLogPostProcessor {

    public static final String COMMA= "，";
    public static final String RESOURCE_ID= "资源ID：";

    @Autowired
    private ISysMNoticeService iSysMNoticeService;
    @Autowired
    private UserService userService;
    @Autowired
    SysMNotifyConfigMapper sysMNotifyConfigMapper;
    @Autowired
    SysMIpConfigMapper sysMIpConfigMapper;
    @Autowired
    RoleMapper roleMapper;
    @Autowired
    UserMapper userMapper;
    @Autowired
    private WorkTicketMapper workTicketMapper;
    @Autowired
    private ServiceCategoryService serviceCategoryService;
    @Autowired
    private ServiceOrderService serviceOrderService;
    @DubboReference
    CloudEnvAccountRemoteService cloudEnvAccountRemoteService;
    @Autowired
    private SysConfigService sysConfigService;
    @Autowired
    private OrgService orgService;
    @DubboReference
    private CloudEnvRemoteService cloudEnvService;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private ServiceOrderMapper serviceOrderMapper;
    @Autowired
    private ServiceOrderResourceRefMapper resourceRefMapper;
    @Autowired
    private ServiceOrderDetailMapper orderDetailMapper;
    @Autowired
    private SfProductResourceMapper productResourceMapper;
    @Resource
    private CloudEnvMapper cloudEnvMapper;

    @Autowired
    private BizDiscountMapper bizDiscountMapper;

    @Autowired
    private BizBillingAccountMapper bizBillingAccountMapper;
    @Autowired
    private BizCustomerActionLogMapper bizCustomerActionLogMapper;

    @Autowired
    private CashCouponMapper cashCouponMapper;

    @DubboReference
    private ResUserCredentialRemoteService resUserCredentialRemoteService;

    @Autowired
    private BizDownloadMapper bizDownloadMapper;

    @Autowired
    private SysMMsgUserConfigMapper sysMMsgUserConfigMapper;

    @Autowired
    private SysMMsgReceiveContactMapper sysMMsgReceiveContactMapper;

    @Autowired
    private BizContractMapper bizContractMapper;

    @DubboReference
    private HcsoUserRemoteService hcsoUserRemoteService;

    @Autowired
    private SfProductResourceMapper sfProductResourceMapper;

    @Autowired
    private TicketCategoryMapper ticketCategoryMapper;
    @Autowired
    private BizBillingRegionResourceMapper bizBillingRegionResourceMapper;
    @Autowired
    private BindProcessMapper bindProcessMapper;
    @Autowired
    private BizCouponMapper bizCouponMapper;


    /**
     * 多重审核开关
     */
    private static final String MULTIPLE_AUDITS_ENABLE = "multiple.audits.enable";
    /**
     * 开启
     */
    private static final String OPEN = "1";

    /**
     * 前置处理器
     *
     * @param point 指向
     * @param baseDto 基准到
     *
     * @return {@code OperationLogBaseDto}
     */
    @Override
    public OperationLogBaseDto beforeProcessor(JoinPoint point, OperationLogBaseDto baseDto) {
        try {
            switch (baseDto.getResource()) {
                case CERTIFICATIONAUDIT:
                    operationLogCertificationAudit(baseDto);
                    break;
                case REFUSE_USER:
                    operationLogForRefuseUser(baseDto);
                    break;
                case ACTIVATE_USER:
                    operationLogForActivateUser(baseDto);
                    break;
                case ENABLE_FREEZESTATUS:
                    operationLogForEnableFreezestatus(point,baseDto);
                    break;
                case DISABLE_RESOURCESTATUS:
                    operationLogForEnableFreezestatus(point,baseDto);
                    break;
                case ENABLE_RESOURCESTATUS:
                    operationLogForDisableFreezeStatus(baseDto);
                    break;
                case SUBMITAUTH:
                    operationLogForDisableFreezeStatus(baseDto);
                    break;
                case NAVIGATION_CONFIRM:
                    operationLogForDisableFreezeStatus(baseDto);
                    break;
                case DELUSER:
                    operationLogForDeleteUser(baseDto);
                    break;
                case UPDATE_USER_STATUS:
                    operationLogForSetUserStatus(baseDto);
                    break;
                case UPDATE_NOTICE_STATUS:
                    operationLogDeleteUnpublish(baseDto);
                    break;
                case DELETE_NOTICE:
                    operationLogDeletePublish(baseDto);
                    break;
                case SAVE_NOTICE:
                    operationLogSavePublish(baseDto);
                    break;
                case SEND_RESET_PWD_EMAIL:
                    operationLogForSendResetPwdEmail(point, baseDto);
                    break;
                case RESET_USER_PASSWORD:
                    operationLogForResetUserPassword(baseDto);
                    break;
                case ROLE_AUTHORIZE:
                    operationLogForRoleAuthorize(point, baseDto);
                    break;
                case DELROLE:
                    operationLogForDelRole(point, baseDto);
                    break;
                case UPDATE_CONFIGS:
                    operationLogForUpdateConfigs(point, baseDto);
                    break;
                case INSERT_NOTIFY_POLICY:
                    operationLogForInsertNotifyPolicy(point,baseDto);
                    break;
                case UPDATE_NOTIFY_POLICY:
                case DELETE_NOTIFY_POLICY:
                    operationLogForDealNotifyPolicy(point,baseDto);
                    break;
                case SWITCH_NOTIFY_POLICY_STATUS:
                    operationLogForSwitchNotifyPolicyStatus(point, baseDto);
                    break;
                case UPDATE_NOTIFY_CONFIG:
                    operationLogForUpdateNotifyConfig(point, baseDto);
                    break;
                case DELETE_NOTIFY_CONFIG:
                    operationLogForDeleteNotifyConfig(point, baseDto);
                    break;
                case RESEND_NOTIFY:
                    operationLogForResendNotify(point, baseDto);
                    break;
                case ADDIPCONFIG:
                    operationLogForAddIpConfig(point, baseDto);
                    break;
                case DELIPCONFIG:
                    operationLogForDelIpConfig(point, baseDto);
                    break;
                case DELIPCONFIGS:
                    operationLogForDelIpConfigs(point, baseDto);
                    break;
                case COLLECTOR_REPAIR:
                    operationLogForCollectorRepair(point, baseDto);
                    break;
                case DELETE_HCSO_USER:
                    deleteHcsoUser(baseDto);
                    break;
                case INSERT_NOTIFY_CONFIG:
                    operationLogForCreateMessageNotification(point,baseDto);
                    break;
                case FINISH_TICKET:
                    operationLogForFinishTicket(baseDto);
                    break;
                case TICKET:
                    operationLogForDistributionTicket(baseDto);
                    break;
                case AUDIT_STATUS:
                    operationLogForAuditStatus(baseDto);
                    break;
                case DELETE_SERVICE:
                    operationLogForRemoveServiceCategory(baseDto);
                    break;
                case APPROVE_ORDER:
                    operationLogForApproveOrder(baseDto);
                    break;
                case DOWNLOAD_CONTRACT_FILE:
                    operationLogForDownloadContractFile(baseDto);
                    break;
                case SYNCHRONIZE_CLOUD_ENVIRONMENT_INFORMATION:
                    operationLogForSyncRes(baseDto);
                    break;
                case GET_EMAIL_CODE:
                    operationLogForGetEmailCode(baseDto);
                    break;
                case GET_SMS_CODE:
                    operationLogForGetMobileSmsCode(baseDto);
                    break;
                case BIND_NEW_CLOUD_TAG_BATCH:
                    operationLogForBindNewCloudTagBatch(baseDto);
                    break;
                case BIND_NEW_CLOUD_TAG:
                    operationLogForBindNewCloudTag(baseDto);
                    break;
                case GET_FILE_NAME:
                    operationLogForGetFileName(baseDto);
                    break;
                case SUBMIT_COMPANY_AUTH:
                    operationLogForCompanyAuth(baseDto);
                    break;
                case ACTIVATE_COMPANY:
                    operationLogForCompanyAuth(baseDto);
                    break;
                case REFUSE_COMPANY:
                    operationLogForCompanyAuth(baseDto);
                    break;
                case ACCESSKEYDOWNLOAD:
                    operationLogForAccessKey(baseDto);
                    break;
                case ACCESSKEYDELETE:
                    operationLogForAccessKey(baseDto);
                    break;
                case UPDATE_COMPANY_RES_COUNT_LIMIT:
                    operationLogForCompanyAuth(baseDto);
                    break;
                case UNLOCK_USER_STATUS:
                    operationLogForSetUserUnlockStatus(baseDto);
                    break;
                case UPDATE_USER_ROLE:
                    operationLogForUpdateUserRole(baseDto);
                    break;
                case SEND_VALIDATE_EMAIL:
                    operationLogForSendValidateEmail(baseDto);
                    break;
                case MODIFY_PWD_BY_EMAIL:
                    operationLogForSendValidateEmail(baseDto);
                    break;
                case FIND_LOST_PWD:
                    operationLogForSendValidateEmail(baseDto);
                    break;
                case UPDATE_USER_PASSWORD:
                case UPDATE_USER_PHONE:
                    setTagName(baseDto);
                    break;
                case FIRST_LOGIN:
                    operationLogForFirstLogin(baseDto);
                    break;
                case LOGIN_SEND_RESET_PWD_EMAIL:
                    operationLogForLoginSendEmail(baseDto);
                    break;
                case RECOVERY:
                    operationLogForRecovery(baseDto);
                    break;
                case MODIFY_ENV_PROJECT:
                    operationLogForModifyEnvProject(baseDto);
                    break;
                case REMOVE_PROJECT:
                    operationLogForRemoveProject(baseDto);
                    break;
                case JOIN_USER_TO_ORG:
                    operationLogForJoinOwnerUserToOrg(baseDto);
                    break;
                case REMOVE_USER_FROM_COMPANY:
                    operationLogForRemoveUserFromCompany(baseDto);
                    break;
                case GET_USER_TOKEN:
                    operationLogForGetUserToken(baseDto);
                    break;
                case GET_VERIFY_CODE:
                    operationLogForGetVerifyCode(baseDto);
                    break;
                case UPDATE_COMPANY_USER_ROLES:
                    operationLogForUpdateEnterpriseUsers(baseDto);
                    break;
                case START_DISABLE_DISCOUNT:
                    operationLogForDeleteDiscount(point,baseDto);
                    break;
                case DELDISCOUNT:
                    operationLogForDeleteDiscount(point,baseDto);
                    break;
                case DOWNLOAD_JOB_LIST:
                    operationLogForDownloadJobList(point, baseDto);
                    break;
                case REGISTER_USER:
                    operationLogRegisterUser(point, baseDto);
                    break;
                case INQUIRY_PRICE:
                    operationLogForInquiryPrice(point,baseDto);
                    break;
                case CHARGE:
                    operationLogForCharge(baseDto);
                    break;
                case CREDIT:
                    operationLogForUpdateCreditLine(baseDto);
                    break;
                case DISTRIBUTECOUPON:
                    operationLogForDisrbuteCoupon(baseDto, point);
                    break;
                case DISTRIBUTECASH:
                    operationLogForDistributeCash(baseDto);
                    break;
                case DISTRIBUTEDEDUCTIONCASH:
                    operationLogForDistributeDuctioncash(baseDto);
                    break;
                case UPDIPCONTROL:
                    updIpControl(baseDto);
                    break;
                case UPLOAD_FILE:
                case UPLOAD_FILE_PATH:
                    operationLogForUploadFile(baseDto);
                    break;
                case UPDATE_COMPANY_USER:
                    operationLogForUpdateUserSelf(baseDto);
                    break;
                case DELUSER_1:
                    operationLogForDelUser(baseDto);
                    break;
                case MODELARTS_COMMON_SHARE_POOL:
                    operationLogForModelartsCommonSharePool(baseDto);
                    break;
                case UPDATE_MSG:
                    updateMsg(baseDto, point);
                    break;
                case UPDATE_MSG_CONTACT:
                    updateMsgContact(baseDto, point);
                    break;
                case UNSUBSCRIBE:
                    unsubscribe(baseDto);
                    break;
                case DOWNLOAD_CONTRACT:
                case DETAIL_CONTRACT:
                case VIEW_CONTRACT:
                    operationLogForDetailContract(baseDto);
                    break;
                case EMAIL_VALIDATE_SUCCESSFUL:
                    updateEmailValidateSuccessful(baseDto, point);
                    break;
                case VALIDATE_USER_EXITS:
                    operationLogValidateUserExits(baseDto, point);
                    break;
                case CLOSE_TICKET:
                case COMPLETE_TICKET:
                case TICKET_RECORD:
                    closeTicket(baseDto);
                    break;
                case DOWNLOAD_IMPORT_RESULT_FILE:
                    downloadImportResultFile(baseDto);
                    break;
                case CONFIGURE_RESOURCE_TYPE:
                    billingStrategyAccount(baseDto);
                    break;
                case ADD_TYPE_FAMILY:
                    addTypeFamily(baseDto);
                    break;
                case ENTITY_RELATED_PRODUCTS:
                    entityRelatedProducts(baseDto);
                    break;
                case SET_EXPIRATION_TIME:
                    setExpirationTime(baseDto);
                    break;
                case AUTH_DOWNLOAD_FILE:
                    downloadFileAuth(baseDto);
                case UPDATE_TICKET_CATEGORY:
                    operationLogUpdateTicketCategory(baseDto, point);
                    break;
                case RENEW_RESOURCE:
                    operationLogForRenewResource(baseDto);
                    break;
                case FORGET_PASSWORD_RESET_PWD:
                    operationLogForForgetPassword(baseDto, point);
                    break;
                case UPDATE_FORM_TEMPLATE_STATUS:
                    operationLogForUpdateTemplateStatus(baseDto, point);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            log.error("OperationLogAspect_beforeProcessor_error_【{}】", JSON.toJSONString(e.getMessage()));
        }
        return baseDto;
    }

    /**
     * 后置处理器
     *
     * @param point 指向
     * @param baseDto 基准到
     * @param methodResult 方法结果
     *
     * @return {@code OperationLogBaseDto}
     */
    @Override
    public OperationLogBaseDto afterReturning(JoinPoint point,OperationLogBaseDto baseDto,String methodResult) {
        try {
            switch (baseDto.getResource()) {
                case PROCESS_DEFINES:
                case CREATE_FORM_TEMPLATE:
                case CREATE_CUSTOMER:
                    convertResultMsg(methodResult, baseDto, "", point);
                    break;
                case SAVE_NOTICE:
                    convertResultMsg(methodResult, baseDto, "", point);
                    break;
                case ADD_COMPANY_USER:
                    convertResultMsg(methodResult, baseDto, "", point);
                    break;
                case ADD_ROLE:
                    convertResultMsg(methodResult, baseDto, "", point);
                    break;
                case INSERT_NOTIFY_CONFIG:
                    convertResultMsg(methodResult, baseDto, "", point);
                    break;
                case ADDIPCONFIG:
                    convertResultMsg(methodResult, baseDto, "", point);
                    break;
                case INSERT_HCSO_USER:
                    convertResultMsg(methodResult, baseDto, "", point);
                    break;
                case CREATE_SERVICE:
                    convertResultMsg(methodResult, baseDto, "", point);
                    break;
                case DISCOUNT:
                    convertResultMsg(methodResult, baseDto, "discount", point);
                    break;
                case EDIT_PLATFORM_DISCOUNT:
                    convertResultMsg(methodResult, baseDto, "editPlatformDiscount", point);
                    break;
                case EDIT_CUSTOMER_DISCOUNT:
                    convertResultMsg(methodResult, baseDto, "editCustomerDiscount", point);
                    break;
                case DISTRIBUTECASH:
                case DISTRIBUTECASH_PROCESS:
                    convertResultMsg(methodResult, baseDto, "", point);
                    break;
                case DISTRIBUTEDEDUCTIONCASH:
                case DISTRIBUTEDEDUCTIONCASH_PROCESS:
                    convertResultMsg(methodResult, baseDto, "", point);
                    break;
                case CREATE_CLOUD_DEPLOYMENT:
                    convertResultMsgForCreateCloudDeployment(methodResult, baseDto);
                    break;
                case CREATE_COMPANY:
                    convertResultMsg(methodResult, baseDto, "", point);
                    break;
                case ADD_SUB_USER:
                    operationLogForCreateSubUsers(methodResult,baseDto);
                    break;
                case CREATE_TICKET:
                    convertResultMsg(methodResult, baseDto, "", point);
                    break;
                case ADD_PACKAGES:
                    convertResultMsg(methodResult, baseDto, "", point);
                    break;
                case BATCH_INSERT_HCSO_USER:
                    operationLogForBatchInsertHcsoUser(methodResult,baseDto);
                    break;
                case VERIFY_HCSO_USER:
                    operationLogForVerifyHcsoUser(methodResult,baseDto);
                    break;
                case BATCH_IMPORT_ACCOUNT_INFO:
                    operationLogForBatchImportAccountInfo(point,methodResult,baseDto);
                    break;
                case REGISTER_USER:
                    operationLogRegisterUser(methodResult, baseDto);
                    break;
                case UPLOAD_FILE:
                    operationLogForUploadFileAfter(methodResult,baseDto);
                    break;
                case ACCESSKEYCREATE:
                    operationLogForAccessKey(methodResult, baseDto);
                    break;
                case EXPORT_REVENUE_EXPENSE_DETAILS:
                case EXPORT_ORDER_LIST:
                case BILLING_CYCLE_EXPORT:
                case BILLING_DETAILS_EXPORT:
                case BILL_ASYNC_EXPORT:
                case EXPORT_TICKET_LIST:
                    operationLogForEXPORTList(baseDto);
                    break;
                case OPENSFS:
                    applyProduct(baseDto, point);
                    break;
                case VALET_ORDER:
                    applyProduct(baseDto, point);
                    break;
                case INSERT_NOTIFY_POLICY:
                    operationLogForInsertNotifyPolicy(methodResult,baseDto);
                    break;
                case CERTIFICATIONAUDIT:
                    operationLogForCertification(baseDto, point);
                case EDIT_CUSTOMER:
                    operationLogForEditCustomer(baseDto, point);
                default:
                    break;
            }
        } catch (Exception e) {
            log.error("OperationLogAspect_afterReturning_error_【{}】", JSON.toJSONString(e.getMessage()));
        }
        return baseDto;
    }

    private void operationLogForDealNotifyPolicy(JoinPoint point, OperationLogBaseDto baseDto) {
        SysMNotifyConfig sysMNotifyConfig = sysMNotifyConfigMapper.selectByPrimaryKey(Long.valueOf(baseDto.getBizId()));
        if (Objects.isNull(sysMNotifyConfig)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1128534018));
        }
        switch (sysMNotifyConfig.getBssType()) {
            case "billing":
                baseDto.setTagName("费用通知");
                break;
            case "biz_bag_expire":
                baseDto.setTagName("套餐包过期通知");
                break;
            case "password_expiration_replacement":
                baseDto.setTagName("密码到期更换通知");
                break;
            case "drp_resource_expire":
                baseDto.setTagName("昇腾Modelarts专属资源池到期通知");
                break;
            case "mq_backlog_alarm":
                baseDto.setTagName("MQ消息积压通知");
                break;
            case "license_expire":
                baseDto.setTagName("许可证到期通知");
                break;
            case "csdr_bill_backlog_warning":
                baseDto.setTagName("话单出账积压异常通知");
                break;
            case "csdr_collector_warning":
                baseDto.setTagName("话单采集异常通知");
                break;
            case "bssaccount_expired":
                baseDto.setTagName("账户到期提前通知");
                break;
            default:
                break;
        }
    }

    private void operationLogForInsertNotifyPolicy(JoinPoint point, OperationLogBaseDto baseDto) {
        switch (baseDto.getTagName()){
            case "billing":
                baseDto.setTagName("费用通知");
                break;
            case "biz_bag_expire":
                baseDto.setTagName("套餐包过期通知");
                break;
            case "password_expiration_replacement":
                baseDto.setTagName("密码到期更换通知");
                break;
            case "drp_resource_expire":
                baseDto.setTagName("昇腾Modelarts专属资源池到期通知");
                break;
            case "mq_backlog_alarm":
                baseDto.setTagName("MQ消息积压通知");
                break;
            case "license_expire":
                baseDto.setTagName("许可证到期通知");
                break;
            case "csdr_bill_backlog_warning":
                baseDto.setTagName("话单出账积压异常通知");
                break;
            case "csdr_collector_warning":
                baseDto.setTagName("话单采集异常通知");
                break;
            case "bssaccount_expired":
                baseDto.setTagName("客户账号到期通知");
                break;
            default:
                break;
        }
    }

    /**
     * 认证图片下载，修改tag
     */
    private void downloadFileAuth(OperationLogBaseDto baseDto) {
        Long userCount = userMapper.countByFileId(baseDto.getBizId());
        if (userCount > 0) {
            baseDto.setTagName("用户认证图片");
            return;
        }
        int orgCount = orgService.countByParams(new Criteria("businessLicenseUrl", baseDto.getBizId()));
        if (orgCount > 0) {
            baseDto.setTagName("企业认证图片");
        }
    }

    /**
     * 关闭工单
     *
     * @param baseDto baseDto
     */
    private void closeTicket(OperationLogBaseDto baseDto) {
        WorkTicket workTicket = workTicketMapper.selectByPrimaryKey(Long.valueOf(baseDto.getBizId()));
        baseDto.setTagName(workTicket.getTicketTitle());
    }

    /**
     * 删除映射租户
     *
     * @param baseDto baseDto
     */
    private void deleteHcsoUser(OperationLogBaseDto baseDto) {
        HcsoUser hcsoUser = hcsoUserRemoteService.selectByPrimaryKey(Long.valueOf(baseDto.getBizId()));
        if (Objects.nonNull(hcsoUser)) {
            baseDto.setTagName(hcsoUser.getAccountName());
        }
    }

    /**
     *
     * @param baseDto
     * @param point
     */
    private void operationLogValidateUserExits(OperationLogBaseDto baseDto, JoinPoint point) {
        ValidateRequest request = BeanConvertUtil.convert(point.getArgs()[0],
            ValidateRequest.class);
        if (Objects.nonNull(request)) {

            String validateType = "组织名称";
            UserSensitiveEnum userSensitiveEnum = UserSensitiveEnum.getEnum(request.getValidateType());
            if (userSensitiveEnum != null) {
                validateType = userSensitiveEnum.getDesc();
            }
            baseDto.setTagName("验证"+validateType+"是否存在");
        }
    }

    /**
     * 邮箱验证
     * @param baseDto
     * @param point
     */
    private void updateEmailValidateSuccessful(OperationLogBaseDto baseDto, JoinPoint point) {
        EmailValidateRequest request = BeanConvertUtil.convert(point.getArgs()[0],
            EmailValidateRequest.class);
        if (Objects.nonNull(request) && StringUtils.isNotBlank(request.getSecurityKey())) {
            String decryptMessage = CrytoUtilSimple.decrypt(UriEncoder.decode(request.getSecurityKey()), false);
            String[] messageArr = decryptMessage.split("#");
            Long userSid = Long.parseLong(messageArr[0]);
            baseDto.setUserSid(userSid);

            cn.com.cloudstar.rightcloud.core.pojo.dto.user.User user = userService.selectByPrimaryKey(userSid);
            if (user != null) {
                baseDto.setAccount(user.getAccount());
                baseDto.setUserType(user.getUserType());
                baseDto.setOrgSid(user.getParentSid());
            }
        }
    }
    /**
     * 下载合同/查看合同详细
     * @param baseDto baseDto
     */
    private void operationLogForDetailContract(OperationLogBaseDto baseDto) {
        if(StringUtils.isNotEmpty(baseDto.getBizId())){
            BizContract contract = bizContractMapper.selectById(Long.valueOf(baseDto.getBizId()));
            Optional.ofNullable(contract).ifPresent(i->baseDto.setTagName(i.getContractTitle()));
        }
    }


    /**
     * 计费配置
     * @param baseDto baseDto
     * @param
     */
    private void billingStrategyAccount(OperationLogBaseDto baseDto) {
        ConfigRegionChargeRequest request = BeanConvertUtil.convert(baseDto.getParam(),
                                                               ConfigRegionChargeRequest.class);
        if (Objects.nonNull(request.getId())){
            String name = bizBillingRegionResourceMapper.selectById(request.getId());
            baseDto.setBizId(StringUtils.isBlank(name) ? request.getId().toString() : name);
        }
    }

    /**
     * 添加类型族
     * @param baseDto baseDto
     * @param
     */
    private void addTypeFamily(OperationLogBaseDto baseDto) {
        StringBuilder sb = new StringBuilder();
        AddTypeFamilyRequest request = BeanConvertUtil.convert(baseDto.getParam(),
                AddTypeFamilyRequest.class);
        List<TypeFamilyVo> list = request.getTypeFamilys();
        if(CollectionUtils.isNotEmpty(list)) {
            for(int i =0 ;i< list.size();i++){
                if(sb.length() == 0){
                    sb.append(list.get(i).getName());
                }else{
                    sb.append(","+list.get(i).getName());
                }
            }
        }
        baseDto.setBizId(sb.toString());
    }

    /**
     * 运营实体关联日志处理
     * @param baseDto baseDto
     * @param
     */
    private void entityRelatedProducts(OperationLogBaseDto baseDto) {
        StringBuilder sb = new StringBuilder();
        RelatedProductsRequest request = BeanConvertUtil.convert(baseDto.getParam(),
                RelatedProductsRequest.class);
        if(request.getStatus() != null){
            // 是否关联；1关联，2取消关联
            if("0".equals(request.getStatus())){
                baseDto.setTagName("取消关联运营实体");
                baseDto.setResource(OperationResourceEnum.CANCEL_ENTITY_RELATED_PRODUCTS);
            }
        }
        baseDto.setBizId(sb.toString());
    }

    /**
     * 设置到期时间日志处理
     * @param baseDto baseDto
     * @param
     */
    private void setExpirationTime(OperationLogBaseDto baseDto) {
        StringBuilder sb = new StringBuilder();
        SetExpirationTimeRequest request = BeanConvertUtil.convert(baseDto.getParam(),
                SetExpirationTimeRequest.class);
        if(request.getId() != null && CollectionUtils.isNotEmpty(request.getId())){
            for(int i= 0;i < request.getId().size();i++){
                if(sb.length() == 0){
                    sb.append(request.getId().get(i));
                }else{
                    sb.append(","+request.getId().get(i));
                }
            }
        }
        baseDto.setBizId(sb.toString());
    }

    /**
     * 修改消息接收人
     * @param baseDto baseDto
     * @param point point
     */
    private void updateMsgContact(OperationLogBaseDto baseDto, JoinPoint point) {
        SysMMsgReceiveContactReq request = BeanConvertUtil.convert(point.getArgs()[0],
                                                                   SysMMsgReceiveContactReq.class);
        if (Objects.nonNull(request)) {
            String status = "";
            List<Long> userSidList = request.getInsert()
                                            .stream()
                                            .map(SysMMsgReceiveContactInsertReq::getUserSid)
                                            .collect(Collectors.toList());
            if (request.getConfigIds().size() > 1) {
                status = "批量修改接收人：";
            } else {
                List<SysMMsgReceiveContact> sysMMsgReceiveContacts = sysMMsgReceiveContactMapper.queryByConfigId(
                        request.getConfigIds().get(0));
                List<Long> receives = sysMMsgReceiveContacts.stream()
                                                            .map(SysMMsgReceiveContact::getUserSid)
                                                            .collect(Collectors.toList());
                if (request.getInsert().size() != sysMMsgReceiveContacts.size()) {
                    status = request.getInsert().size() > sysMMsgReceiveContacts.size() ? "添加接收人：" : "删除接收人：";
                    if (request.getInsert().size() > sysMMsgReceiveContacts.size()) {
                        userSidList.removeAll(receives);
                    } else {
                        receives.removeAll(userSidList);
                        userSidList = receives;
                    }
                } else {
                    status = "修改接收人：";
                }
            }
            String userSid = StringUtils.join(userSidList, StrUtil.COMMA);
            String id = StringUtils.join(request.getConfigIds(), StrUtil.COMMA);
            baseDto.setTagName(status + userSid + COMMA + RESOURCE_ID + id);
        }
    }

    /**
     * 修改消息配置
     * @param baseDto baseDto
     * @param point point
     */
    private void updateMsg(OperationLogBaseDto baseDto, JoinPoint point) {
        SysMMsgUserConfigUpdateReq request = BeanConvertUtil.convert(point.getArgs()[0], SysMMsgUserConfigUpdateReq.class);
        if (Objects.nonNull(request)) {
            if (request.getStatus() != Constants.ONE_INT && request.getStatus() != Constants.ZERO_INT) {
                baseDto.setTagName("违规操作类型:" + request.getStatus());
                return;
            }
            String status = Constants.ONE_INT == request.getStatus() ? "启用" : "禁用";
            String type = Constants.SMS.equals(request.getType()) ? "短信" : "邮件";
            baseDto.setTagName(status + type + COMMA + RESOURCE_ID + StringUtils.join(request.getIds(), ","));
        }
    }

    /**
     * 退订
     *
     * @param baseDto baseDto
     */
    private void unsubscribe(OperationLogBaseDto baseDto) {
        SfProductResource resource = productResourceMapper.selectByPrimaryKey(Long.valueOf(baseDto.getBizId()));
        Criteria criteria = new Criteria();
        criteria.put("resourceId", resource.getId());
        criteria.put("type", resource.getProductType());
        List<ServiceOrderResourceRef> serviceOrderResourceRefs = resourceRefMapper.selectByParams(criteria);
        ServiceOrderDetail serviceOrderDetail = orderDetailMapper.selectByPrimaryKey(serviceOrderResourceRefs.get(0).getOrderDetailId());
        ServiceOrder serviceOrder = serviceOrderMapper.selectByPrimaryKey(serviceOrderDetail.getOrderId());
        baseDto.setTagName(serviceOrder.getName());
    }

    /**
     * 下载导入结果
     *
     * @param baseDto baseDto
     */
    private void downloadImportResultFile(OperationLogBaseDto baseDto) {
        baseDto.setBizId(CrytoUtilSimple.decrypt(baseDto.getBizId()));
    }


    /**
     * 活动日志对于插入通知策略
     *
     * @param methodResult 方法结果
     * @param baseDto 基准到
     */
    private void operationLogForInsertNotifyPolicy(String methodResult, OperationLogBaseDto baseDto) {
        if (StringUtil.isNotEmpty(methodResult)) {
            RestResult restResult = JacksonUtils.fromJson(methodResult, RestResult.class);
            if (Objects.nonNull(restResult) && restResult.getStatus()) {
                baseDto.setBizId(restResult.getData().toString());
            }
        }
    }


    /**
     * 申请服务
     *
     * @param baseDto baseDto
     * @param point point
     */
    private void applyProduct(OperationLogBaseDto baseDto, JoinPoint point) {
        ApplyServiceRequest request = BeanConvertUtil.convert(point.getArgs()[0], ApplyServiceRequest.class);
        Criteria criteria = new Criteria();
        criteria.setOrderByClause("id Desc");
        criteria.put("orgSid", request.getProjectId());
        List<SfProductResource> sfProductResources = sfProductResourceMapper.selectByParams(criteria);
        if (CollectionUtils.isEmpty(sfProductResources)) {
            criteria.clear();
            criteria.setOrderByClause("id Desc");
            sfProductResources = sfProductResourceMapper.selectByParams(criteria);
        }
        if (CollectionUtils.isNotEmpty(sfProductResources) && StringUtils.isEmpty(baseDto.getTagName())) {
            baseDto.setTagName(sfProductResources.get(0).getProductName());
            baseDto.setBizId(sfProductResources.get(0).getId().toString());
        }
    }

    private void operationLogForModelartsCommonSharePool(OperationLogBaseDto baseDto) {
        String param = baseDto.getParam();
        OperationResourceEnum resource =
                BooleanUtil.toBoolean(param) ? OperationResourceEnum.MODELARTS_COMMON_SHARE_POOL_ENABLE
                        : OperationResourceEnum.MODELARTS_COMMON_SHARE_POOL_DISENABLE;
        baseDto.setResource(resource);
    }

    /**
     * setTagName 用户名，资源ID：
     *
     * @param baseDt baseDt
     */
    private void setTagName(OperationLogBaseDto baseDt) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (Objects.nonNull(authUserInfo)) {
            baseDt.setTagName(authUserInfo.getAccount() + "，资源ID：" + authUserInfo.getUserSid());
        }
    }

    /**
     * 删除折扣/启用禁用折扣共用接口
     *
     * @param point   点
     * @param baseDto 基地dto
     */
    private void operationLogForDeleteDiscount(JoinPoint point,OperationLogBaseDto baseDto) {
        OperateDiscountRequest arg = (OperateDiscountRequest) point.getArgs()[0];
        // 启用/禁用/删除客户折扣
        String index = LogEnum.get(arg.getType());
        if ("delete".equals(arg.getType())) {
            BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectByPrimaryKey(arg.getUserSid());
            baseDto.setResource(OperationResourceEnum.DELDISCOUNT);
            baseDto.setTagName(bizBillingAccount.getAccountName() + "，资源ID：" +arg.getUserSid() );
            String s =
                    "删除客户折扣：客户[" + bizBillingAccount.getAccountName() + "]";
            insertActionLog(bizBillingAccount.getId(), OperationResourceEnum.DELDISCOUNT, s);
        } else {
            BizDiscount bizDiscount = bizDiscountMapper.selectByPrimaryKey(arg.getDiscountSid());
            if ("enabled".equals(arg.getType()) && DiscountPolicyEnum.PLATFORM.getCode().equals(bizDiscount.getDiscountType())) {
                baseDto.setResource(OperationResourceEnum.START_PLATFORM_DISCOUNT);
                if(baseDto.getTagName() != null && baseDto.getTagName().contains("客户")){
                    baseDto.setResource(OperationResourceEnum.START_CUSTOMER_DISCOUNT);
                }
            } else if ("disabled".equals(arg.getType()) && DiscountPolicyEnum.PLATFORM.getCode().equals(bizDiscount.getDiscountType())) {
                baseDto.setResource(OperationResourceEnum.DISABLE_PLATFORM_DISCOUNT);
                if(baseDto.getTagName() != null && baseDto.getTagName().contains("客户")){
                    baseDto.setResource(OperationResourceEnum.DISABLE_CUSTOMER_DISCOUNT);
                }
            } else if ("enabled".equals(arg.getType()) && DiscountPolicyEnum.CUSTOMER.getCode().equals(bizDiscount.getDiscountType())) {
                baseDto.setResource(OperationResourceEnum.START_CUSTOMER_DISCOUNT);
                if(baseDto.getTagName() != null && baseDto.getTagName().contains("平台")){
                    baseDto.setResource(OperationResourceEnum.START_PLATFORM_DISCOUNT);
                }
            } else if ("disabled".equals(arg.getType()) && DiscountPolicyEnum.CUSTOMER.getCode().equals(bizDiscount.getDiscountType())) {
                baseDto.setResource(OperationResourceEnum.DISABLE_CUSTOMER_DISCOUNT);
                if(baseDto.getTagName() != null && baseDto.getTagName().contains("平台")){
                    baseDto.setResource(OperationResourceEnum.DISABLE_PLATFORM_DISCOUNT);
                }
            }
            baseDto.setTagName(bizDiscount.getDiscountName());
            if (DiscountPolicyEnum.CUSTOMER.getCode().equals(bizDiscount.getDiscountType())) {
                BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectByPrimaryKey(bizDiscount.getUserSid());
                String s =
                        index + "客户折扣：客户[" + bizBillingAccount.getAccountName() + "]，折扣名[" + bizDiscount.getDiscountName()
                                + "]，产品[" + ProductComponentEnum.transformDesc(
                                StrUtil.splitToArray(bizDiscount.getProductScope(), StrUtil.COMMA)) + "]，范围["
                                + LogEnum.get(bizDiscount.getScopeType()) + "]，折扣系数["
                                + bizDiscount.getDiscountRatio() + "]";
                insertActionLog(bizDiscount.getUserSid(), baseDto.getResource(), s);
            }
        }
    }

    /**
     * 下载列表
     *
     * @param point   点
     * @param baseDto 基地dto
     */
    private void operationLogForDownloadJobList(JoinPoint point,OperationLogBaseDto baseDto) {
        BizDownloadRequest arg = (BizDownloadRequest) point.getArgs()[0];
        baseDto.setTagName(LogEnum.get(arg.getOperationType()));
        List<BizDownload> bizDownloads = bizDownloadMapper.listDownLoad(baseDto.getAccount());
        String fileName="";
        if(bizDownloads.size()>0){
            fileName=bizDownloads.get(0).getDownloadNum();
        }
        switch (arg.getOperationType()){
            case "service_order":
                baseDto.setResource(OperationResourceEnum.DOWNLOAD_ORDER_JOB_LIST);
                baseDto.setTagName("订单列表");
                break;
            case "bizAccountDeal":
                baseDto.setResource(OperationResourceEnum.DOWNLOAD_INCOME_AND_EXPENDITURE_DETAILS_JOB_LIST);
                baseDto.setTagName("收支明细列表");
                break;
            case "billCycle":
                baseDto.setResource(OperationResourceEnum.DOWNLOAD_BILLING_CRYLE_JOB_LIST);
                baseDto.setTagName("账单周期列表");
                break;
            case "billDetail":
                baseDto.setResource(OperationResourceEnum.DOWNLOAD_BILLING_DETAILS_JOB_LIST);
                baseDto.setTagName("账单明细列表");
                break;
            case "batchImportCustomer":
                baseDto.setResource(OperationResourceEnum.DOWNLOAD_ACCOUNT_JOB_LIST);
                baseDto.setTagName("客户导入列表");
                break;
            case "action_logs":
                baseDto.setResource(OperationResourceEnum.DOWNLOAD_LOG_JOB_LIST);
                baseDto.setTagName("用户日志导出任务列表");
                break;
            default:
                break;
        }
    }

    /**
     * 接口返回转换
     *
     * @param methodResult
     * @param baseDto
     * @param flag
     */
    private void convertResultMsg(String methodResult, OperationLogBaseDto baseDto,String flag, JoinPoint point) {
        if (StringUtil.isNotEmpty(methodResult)) {
            RestResult restResult = JacksonUtils.fromJson(methodResult, RestResult.class);
            Optional.ofNullable(restResult.getData()).ifPresent(bizId -> baseDto.setBizId(bizId.toString()));
        }
        switch (flag){
            case "discount":
                CreateDiscountRequest createDiscountRequest = JacksonUtils.fromJson(baseDto.getParam(), CreateDiscountRequest.class);
                if (DiscountPolicyEnum.CUSTOMER.getCode().equals(createDiscountRequest.getDiscountType())) {
                    BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectByPrimaryKey(createDiscountRequest.getUserSid());
                    String s =
                            "创建客户折扣：客户[" + bizBillingAccount.getAccountName() + "]，折扣名[" + createDiscountRequest.getDiscountName() + "]，产品["
                                    + ProductComponentEnum.transformDesc(
                                    StrUtil.splitToArray(createDiscountRequest.getProductScope(), StrUtil.COMMA)) + "]，范围["
                                    + LogEnum.get(createDiscountRequest.getScopeType()) + "]，折扣系数["
                                    + createDiscountRequest.getDiscountRatio() + "]";
                    insertActionLog(createDiscountRequest.getUserSid(), OperationResourceEnum.DISCOUNT, s);
                }
                break;
            case "editCustomerDiscount":
                UpdateDiscountRequest updateDiscountRequest = JacksonUtils.fromJson(baseDto.getParam(), UpdateDiscountRequest.class);
                BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectByPrimaryKey(updateDiscountRequest.getUserSid());
                String s =
                        "编辑客户折扣：客户[" + bizBillingAccount.getAccountName() + "]，折扣名[" + updateDiscountRequest.getDiscountName() + "]，产品["
                                + ProductComponentEnum.transformDesc(
                                StrUtil.splitToArray(updateDiscountRequest.getProductScope(), StrUtil.COMMA)) + "]，范围["
                                + LogEnum.get(updateDiscountRequest.getScopeType()) + "]，折扣系数["
                                + updateDiscountRequest.getDiscountRatio() + "]";
                insertActionLog(updateDiscountRequest.getUserSid(), OperationResourceEnum.EDIT_CUSTOMER_DISCOUNT, s);
                break;
            case "distributeCash":
            case "distributeCashProcess":
                cashCouponMapper.selectByParams(new Criteria("couponNo",baseDto.getBizId()))
                        .forEach(cashCoupon -> {
                            String logDetail = "发放现金券(金额：￥" + cashCoupon.getAmount() + ",现金券编号：" + cashCoupon.getCouponNo() + ")";
                            insertActionLog(cashCoupon.getAccountId(), baseDto.getResource(), logDetail);
                });
                break;
            case "distributeDuctioncash":
            case "distributeDuctioncashProcess":
                cashCouponMapper.selectByParams(new Criteria("couponNo",baseDto.getBizId()))
                        .forEach(cashCoupon -> {
                            String logDetail = "发放抵扣现金券(金额：￥" + cashCoupon.getAmount() + ",现金券编号：" + cashCoupon.getCouponNo() + ")";
                            insertActionLog(cashCoupon.getAccountId(), baseDto.getResource(), logDetail);
                });
            default:
                break;
        }
    }

    /**
     * 创建资源组接口返回转换
     *
     * @param methodResult
     * @param baseDto
     */
    private void convertResultMsgForCreateCloudDeployment(String methodResult, OperationLogBaseDto baseDto) {
        if (StringUtil.isNotEmpty(methodResult)) {
            RestResult restResult = JacksonUtils.fromJson(methodResult, RestResult.class);
            if(restResult.getData() != null){
                if(restResult.getData() instanceof CloudDeployment){
                    CloudDeployment deployment = (CloudDeployment)restResult.getData();
                    Optional.ofNullable(deployment).ifPresent(x -> baseDto.setBizId(x.getId().toString()));
                }
            }
        }
    }

    /**
     * 获取验证码
     * @param baseDto
     */
    private void operationLogForGetVerifyCode(OperationLogBaseDto baseDto) {
        String userName = baseDto.getTagName();
        AuthUser authUser = BasicInfoUtil.getCurrentUserInfo();
        if (authUser != null) {
            baseDto.setAccount(authUser.getAccount());
            baseDto.setBizId(authUser.getUserSid().toString());
        } else {
            baseDto.setAccount(userName);
            cn.com.cloudstar.rightcloud.core.pojo.dto.user.User user = userMapper.selectPassword(userName);
            if (Objects.nonNull(user)) {
                baseDto.setBizId(user.getUserSid().toString());
            }
        }
    }

    /**
     * 创建消息通知
     * @param baseDto
     */
    private void operationLogForCreateMessageNotification(JoinPoint point, OperationLogBaseDto baseDto) {
        SysNotifyConfigRequest arg = (SysNotifyConfigRequest) point.getArgs()[0];
        switch (arg.getBssType()){
            case "billing":
                baseDto.setTagName("费用通知");
                break;
            case "sfs_storage":
                baseDto.setTagName("sfs存储通知");
                break;
            case "drp_resource_expire":
                baseDto.setTagName("昇腾Modelarts专属资源池到期通知");
                break;
            default:
                break;
        }
    }


    /**
     * 更新企业用户
     * @param baseDto
     */
    private void operationLogForUpdateEnterpriseUsers(OperationLogBaseDto baseDto) {
        baseDto.setTagName(userMapper.selectByPrimaryKey(Long.valueOf(baseDto.getBizId())).getAccount());
    }

    /**
     * 获取用户凭证
     * @param baseDto
     */
    private void operationLogForGetUserToken(OperationLogBaseDto baseDto) {
        String moduleType = baseDto.getParam();
        if (StringUtils.isNotBlank(moduleType) && "bss".equalsIgnoreCase(moduleType)) {
            baseDto.setTagName("运营控制页token");
        } else if (StringUtils.isNotBlank(moduleType) && "console".equalsIgnoreCase(moduleType)) {
            baseDto.setTagName("用户控制页token");
        }
    }

    /**
     * 将用户从企业中移除
     * @param baseDto
     */
    private void operationLogForRemoveUserFromCompany(OperationLogBaseDto baseDto) {
        DeleteUserFromCompanyRequest request = JacksonUtils.fromJson(baseDto.getParam(), DeleteUserFromCompanyRequest.class);
        List<String> userNames = new ArrayList<>();
        List<Long> userIds = request.getUserIds();
        userIds.stream().forEach(id -> {
            User user = userService.selectUser(id);
            Optional.ofNullable(user).ifPresent(x -> userNames.add(user.getAccount()));
        });
        baseDto.setTagName(StringUtils.join(userNames,"，"));
    }

    /**
     * 将用户加入组织
     * @param baseDto
     */
    private void operationLogForJoinOwnerUserToOrg(OperationLogBaseDto baseDto) {
        CreateUserToOrgRequest request = JacksonUtils.fromJson(baseDto.getParam(), CreateUserToOrgRequest.class);
        List<String> userNames = new ArrayList<>();
        for (CreateUserToOrgRequest.UserInfo user : request.getUsers()) {
            User user1 = userService.selectUser(user.getUserSid());
            Optional.ofNullable(user1).ifPresent(x -> userNames.add(x.getAccount()));
        }
        baseDto.setTagName(StringUtils.join(userNames,"，"));
    }

    /**
     * 移除项目
     * @param baseDto
     */
    private void operationLogForRemoveProject(OperationLogBaseDto baseDto) {
        if(StringUtils.isNotEmpty(baseDto.getBizId())) {
            List<String> ids = Arrays.asList(baseDto.getBizId().split(","));
            List<String> names = new ArrayList<>();
            for (String id : ids) {
                Project project = projectService.selectByPrimaryKey(Long.valueOf(id));
                Optional.ofNullable(project).ifPresent(x -> names.add(x.getProjectName()));
            }
            baseDto.setTagName(StringUtils.join(names,"，"));
        }

    }

    /**
     * 修改关联到项目的云环境
     * @param baseDto
     */
    private void operationLogForModifyEnvProject(OperationLogBaseDto baseDto) {
        if(StringUtils.isNotEmpty(baseDto.getBizId())) {
            CloudEnv cloudEnv = cloudEnvService.selectByPrimaryKey(Long.valueOf(baseDto.getBizId()));
            Optional.ofNullable(cloudEnv).ifPresent(x -> baseDto.setTagName(x.getCloudEnvName()));
        }
    }

    /**
     * 通过roleSid还原默认角色的权限
     * @param baseDto
     */
    private void operationLogForRecovery(OperationLogBaseDto baseDto) {
        if(StringUtils.isNotEmpty(baseDto.getBizId())) {
            Role role = roleMapper.selectByPrimaryKey(Long.valueOf(baseDto.getBizId()));
            Optional.ofNullable(role).ifPresent(x -> baseDto.setTagName(role.getRoleName()));
        }
    }

    /**
     * 登录后-重置密码-给用户发送邮件
     * @param baseDto
     */
    private void operationLogForLoginSendEmail(OperationLogBaseDto baseDto) {
        if(StringUtils.isNotEmpty(baseDto.getBizId())) {
            List<String> userIds = Arrays.asList(baseDto.getBizId().split(","));
            List<String> userNames = new ArrayList<>();
            for (String userId : userIds) {
                User user = userService.selectUser(Long.valueOf(userId));
                // 手动拼接tagName
                Optional.ofNullable(user).ifPresent(u -> userNames.add(user.getAccount() + "，资源ID：" + userId));
            }
            baseDto.setTagName(StringUtils.join(userNames,"；"));
            // bizId置空，防止二次组装
            baseDto.setBizId(null);
        }
    }

    /**
     * 首次登陆
     * @param baseDto
     */
    private void operationLogForFirstLogin(OperationLogBaseDto baseDto) {
        FirstLoginRequest request = JacksonUtils.fromJson(baseDto.getParam(), FirstLoginRequest.class);
        List<Long> userIds = request.getUserIds();
        List<String> userNames = new ArrayList<>();
        userIds.stream().forEach(id -> {
            cn.com.cloudstar.rightcloud.core.pojo.dto.user.User user = userService.selectByPrimaryKey(id);
            Optional.ofNullable(user).ifPresent(x -> userNames.add(user.getAccount()));
        });
        baseDto.setBizId(StringUtils.join(userIds,","));
        baseDto.setTagName(baseDto.getTagName()+COMMA+StringUtils.join(userNames,"，"));
    }

    /**
     * 发送验证邮箱/通过邮箱修改密码/找回密码
     * @param baseDto
     */
    private void operationLogForSendValidateEmail(OperationLogBaseDto baseDto) {
        if (Objects.nonNull(baseDto.getTagName())) {
            baseDto.setTagName(DesensitizedUtil.email(baseDto.getTagName()));
            setTagName(baseDto);
        }
    }

    /**
     * 更新用户角色
     * @param baseDto
     */
    private void operationLogForUpdateUserRole(OperationLogBaseDto baseDto) {
        UpdateUserRoleRequest request = JacksonUtils.fromJson(baseDto.getParam(), UpdateUserRoleRequest.class);
        User user = userService.selectUser(Long.valueOf(request.getUserSid()));
        Optional.ofNullable(user).ifPresent(x -> baseDto.setTagName(x.getAccount()));
    }

    /**
     * 解除用户锁定状态
     * @param baseDto
     */
    private void operationLogForSetUserUnlockStatus(OperationLogBaseDto baseDto) {
        JsonNode jsonNode = JacksonUtils.fromJson(baseDto.getParam(), JsonNode.class);
        JsonNode userIds = jsonNode.get("userIds");
        List<Long> userIdList = new ArrayList<>();
        List<String> userNameList = new ArrayList<>();
        for (JsonNode userIdNode : userIds) {
            userIdList.add(userIdNode.asLong());
            User user = userService.selectUser(userIdNode.asLong());
            Optional.ofNullable(user).ifPresent(x -> userNameList.add(x.getAccount()));
        }
        baseDto.setBizId(StringUtils.join(userIdList,","));
        baseDto.setTagName(StringUtils.join(userNameList,"，"));
    }

    /**
     * 提交企业认证/激活企业/拒绝企业/访问密钥下载/访问密钥一览/访问密钥删除/访问密钥创建/更新企业资源数量限制
     * @param baseDto
     */
    private void operationLogForCompanyAuth(OperationLogBaseDto baseDto) {
        if(StringUtils.isNotEmpty(baseDto.getBizId())) {
            Org org = orgService.selectByPrimaryKey(Long.valueOf(baseDto.getBizId()));
            Optional.ofNullable(org).ifPresent(x -> baseDto.setTagName(x.getOrgName()));
        }
    }

    /**
     * 访问密钥相关日志记录(删除/下载)
     * @param baseDto
     */
    private void operationLogForAccessKey(String methodResult, OperationLogBaseDto baseDto) {
        if (StringUtil.isNotEmpty(methodResult)) {
            RestResult restResult = JacksonUtils.fromJson(methodResult, RestResult.class);
            // 注册成功修改userSid
            if (Objects.nonNull(restResult) && restResult.getStatus()) {
                if (StringUtils.isNotEmpty(baseDto.getBizId())) {
                    cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria criteria = new cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria();
                    criteria.put("orgSid", Integer.valueOf(baseDto.getBizId()));
                    List<ResUserCredential> resUserCredentials = resUserCredentialRemoteService.selectByParams(criteria);
                    if (CollectionUtils.isNotEmpty(resUserCredentials)) {
                        resUserCredentials = resUserCredentials.stream()
                                .sorted(Comparator.comparing(ResUserCredential::getCreatedDt).reversed())
                                .collect(Collectors.toList());
                        baseDto.setBizId(resUserCredentials.get(0).getId().toString());
                    }
                }
            }
        }
    }

    /**
     * 访问密钥相关日志记录(创建)
     * @param baseDto
     */
    private void operationLogForAccessKey(OperationLogBaseDto baseDto) {
        if (StringUtils.isNotEmpty(baseDto.getBizId())) {
            cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria criteria = new cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria();
            criteria.put("orgSid", Integer.valueOf(baseDto.getBizId()));
            List<ResUserCredential> resUserCredentials = resUserCredentialRemoteService.selectByParams(criteria);
            List<Long> ids = resUserCredentials.stream()
                    .map(ResUserCredential::getId)
                    .collect(Collectors.toList());
            Optional.of(ids)
                    .ifPresent(r -> baseDto.setBizId(ids.toString()));
        }
    }


    /**
     * 获取上传模板的文件名
     * @param baseDto
     */
    private void operationLogForGetFileName(OperationLogBaseDto baseDto) {
        Criteria criteria = new Criteria();
        if (StringUtil.isNotBlank(baseDto.getBizId())) {
            criteria.put("configKey", baseDto.getBizId());
        }
    }

    /**
     * 绑定新标签
     * @param baseDto
     */
    private void operationLogForBindNewCloudTag(OperationLogBaseDto baseDto) {
        List<CreateCloudTagBindRequest> requests = JacksonUtils.parseList(baseDto.getParam(), CreateCloudTagBindRequest.class);
        List<String> tagNames = requests.stream().map(CreateCloudTagBindRequest::getTagName).collect(Collectors.toList());
        baseDto.setTagName(StringUtils.join(tagNames,"，"));
    }

    /**
     * 批量绑定标签
     * @param baseDto
     */
    private void operationLogForBindNewCloudTagBatch(OperationLogBaseDto baseDto) {
        CreateBindTagBatchRequest re = JacksonUtils.fromJson(baseDto.getParam(), CreateBindTagBatchRequest.class);
        List<String> tagNames = re.getCloudTags().stream().map(CloudTagVO::getTagName).collect(Collectors.toList());
        baseDto.setTagName(StringUtils.join(tagNames,"，"));
    }

    /**
     * 获取短信验证码
     * @param baseDto
     */
    private void operationLogForGetMobileSmsCode(OperationLogBaseDto baseDto) {
        MobileSmsCodeRequest re = JacksonUtils.fromJson(baseDto.getParam(), MobileSmsCodeRequest.class);
        baseDto.setTagName(DesensitizedUtil.mobilePhone(re.getPhone()));
        cn.com.cloudstar.rightcloud.core.pojo.dto.user.User user = userService.selectByPrimaryKey(re.getUserSid());
        Optional.ofNullable(user).ifPresent(x -> baseDto.setAccount(x.getAccount()));
        Criteria criteria = new Criteria();
        criteria.put("mobile", re.getPhone());
        List<cn.com.cloudstar.rightcloud.core.pojo.dto.user.User> users = userService.selectByParams(criteria);
        if(CollectionUtils.isNotEmpty(users)){
            baseDto.setAccount(users.get(0).getAccount());
        }
    }

    /**
     * 获取邮箱验证码
     * @param baseDto
     */
    private void operationLogForGetEmailCode(OperationLogBaseDto baseDto) {
        ValidateRequest re = JacksonUtils.fromJson(baseDto.getParam(), ValidateRequest.class);
        baseDto.setTagName(DesensitizedUtil.email(re.getEmail()));
    }

    /**
     * 同步云环境信息
     * @param baseDto
     */
    private void operationLogForSyncRes(OperationLogBaseDto baseDto) {
        if(StringUtils.isNotEmpty(baseDto.getBizId())){
            CloudEnvAccount account = cloudEnvAccountRemoteService.selectByPrimaryKey(Long.valueOf(baseDto.getBizId()));
            Optional.ofNullable(account).ifPresent(x -> baseDto.setTagName(x.getEnvAccountName()));
        }
    }

    /**
     * 下载专属资源池计算节点配置文件
     * @param baseDto
     */
    private void operationLogForDownloadContractFile(OperationLogBaseDto baseDto) {
        if(StringUtils.isNotEmpty(baseDto.getBizId())){
            ResHpcClusterRemoteModule module = serviceOrderService.getHpcDrpOrderActivate(Long.valueOf(baseDto.getBizId()));
            Optional.ofNullable(module).ifPresent(x -> baseDto.setTagName(x.getName()));
        }
    }

    /**
     * 审核订单
     * @param baseDto
     */
    private void operationLogForApproveOrder(OperationLogBaseDto baseDto) {
        ApproveOrderRequest request = JacksonUtils.fromJson(baseDto.getParam(), ApproveOrderRequest.class);
        ServiceOrder serviceOrder = serviceOrderMapper.selectByPrimaryKey(request.getId());
        Optional.ofNullable(serviceOrder).ifPresent(x -> baseDto.setTagName(x.getName()));
        switch (request.getApproveType()) {
            case "01":
                baseDto.setResource(OperationResourceEnum.APPROVE_ORDER_PASS);
                break;
            case "02":
                baseDto.setResource(OperationResourceEnum.APPROVE_ORDER_BACK);
                break;
            case "03":
                baseDto.setResource(OperationResourceEnum.APPROVE_ORDER_REFUSE);
                break;
            default:
                break;
        }
    }

    /**
     * 删除服务
     * @param baseDto
     */
    private void operationLogForRemoveServiceCategory(OperationLogBaseDto baseDto) {
        if(StringUtils.isNotEmpty(baseDto.getBizId())){
            ServiceCategory serviceCategory = serviceCategoryService.selectCategoryById(Long.valueOf(baseDto.getBizId()));
            Optional.ofNullable(serviceCategory).ifPresent(service -> baseDto.setTagName(service.getServiceName()));
        }
    }

    /**
     * 设置审核状态
     * @param baseDto
     */
    private void operationLogForAuditStatus(OperationLogBaseDto baseDto) {
        ConfigAuditStatusRequest request = JacksonUtils.fromJson(baseDto.getParam(), ConfigAuditStatusRequest.class);
        Boolean selfServiceAuditStatus = request.getSelfServiceAuditStatus();
        Boolean ticketAuditStatus = request.getTicketAuditStatus();
        baseDto.setTagName("自服务审批状态："+selfServiceAuditStatus+"，工单审批状态："+ticketAuditStatus);
    }

    /**
     * 分配一个工单
     * @param baseDto
     */
    private void operationLogForDistributionTicket(OperationLogBaseDto baseDto) {
        WorkTicket workTicket = this.workTicketMapper.selectByPrimaryKey(Long.valueOf(baseDto.getBizId()));
        Optional.ofNullable(workTicket).ifPresent(ticket -> baseDto.setTagName(ticket.getTicketTitle()));
    }

    /**
     * 解决一个工单/完成工单/
     * @param baseDto
     */
    private void operationLogForFinishTicket(OperationLogBaseDto baseDto) {
        if(StringUtils.isNotEmpty(baseDto.getBizId())){
            WorkTicket workTicket = workTicketMapper.selectByPrimaryKey(Long.valueOf(baseDto.getBizId()));
            Optional.ofNullable(workTicket).ifPresent(x -> baseDto.setTagName(x.getTicketTitle()));
        }
    }

    /**
     * 删除公告/修改公告状态
     * @param baseDto
     */
    private void operationLogDeleteUnpublish(OperationLogBaseDto baseDto) {
        if(StringUtils.isNotEmpty(baseDto.getBizId())){
            SysMNotice sysMNotice = iSysMNoticeService.selectById(Long.valueOf(baseDto.getBizId()));
            Optional.ofNullable(sysMNotice).ifPresent(x -> baseDto.setTagName(x.getNoticeTitle()));
        }
        if(StringUtils.equals(baseDto.getParam(),"1")){
            baseDto.setResource(OperationResourceEnum.UPDATE_NOTICE_STATUS_PUBLISH);
        }else if(StringUtils.equals(baseDto.getParam(),"0")){
            baseDto.setResource(OperationResourceEnum.UPDATE_NOTICE_STATUS_UNPUBLISH);
        }
    }

    /**
     * 删除公告
     * @param baseDto
     */
    private void operationLogDeletePublish(OperationLogBaseDto baseDto) {
        if(StringUtils.isNotEmpty(baseDto.getBizId())){
            SysMNotice sysMNotice = iSysMNoticeService.selectById(Long.valueOf(baseDto.getBizId()));
            Optional.ofNullable(sysMNotice).ifPresent(x -> baseDto.setTagName(x.getNoticeTitle()));
        }
    }

    /**
     * 保存公告
     * @param baseDto
     */
    private void operationLogSavePublish(OperationLogBaseDto baseDto) {
        NoticeUpdateRequest re = JacksonUtils.fromJson(baseDto.getParam(), NoticeUpdateRequest.class);
        baseDto.setTagName(re.getNoticeTitle());
        if(StringUtils.isNotEmpty(re.getNoticeTitle())){
            if(Objects.nonNull(re.getNoticeId())){
                baseDto.setResource(OperationResourceEnum.UPDATE_NOTICE);
            }
        }
    }

    /**
     * 费用相关导出(公共)
     *
     * @param baseDto              基地dto
     */
    private void operationLogForEXPORTList(OperationLogBaseDto baseDto) {
        List<BizDownload> bizDownloads = bizDownloadMapper.listDownLoad(baseDto.getAccount());
        String fileName="";
        if(bizDownloads.size()>0){
            fileName=bizDownloads.get(0).getDownloadNum();
        }
        baseDto.setBizId(fileName);
    }


    /**
     * 修改用户状态
     * @param baseDto
     */
    private void operationLogForSetUserStatus(OperationLogBaseDto baseDto) {
        if(StringUtils.isNotEmpty(baseDto.getBizId())){
            cn.com.cloudstar.rightcloud.core.pojo.dto.user.User user = userService.findUserById(Long.valueOf(baseDto.getBizId()));
            Optional.ofNullable(user).ifPresent(x -> baseDto.setTagName(x.getAccount()));
        }
        // 禁用
        if(StringUtils.equals("0",baseDto.getParam())){
            baseDto.setResource(OperationResourceEnum.UPDATE_USER_STATUS_DISABLE);
        }
        // 启用
        else if(StringUtils.equals("1",baseDto.getParam())){
            baseDto.setResource(OperationResourceEnum.UPDATE_USER_STATUS_ENABLE);
        }
    }

    /**
     * 删除用户
     * @param baseDto
     */
    private void operationLogForDeleteUser(OperationLogBaseDto baseDto) {
        DeleteUserRequest request = JacksonUtils.fromJson(baseDto.getParam(), DeleteUserRequest.class);
        List<Long> userIds = request.getUserIds();
        List<String> userNames = userIds.stream().map(id -> {
            cn.com.cloudstar.rightcloud.core.pojo.dto.user.User user = userService.selectByPrimaryKey(id);
            return user.getAccount();
        }).collect(Collectors.toList());
        baseDto.setBizId(StringUtils.join(userIds,","));
        baseDto.setTagName(StringUtils.join(userNames,","));
    }

    /**
     * 创建子用户
     *
     * @param methodResult 方法结果
     * @param baseDto      基地dto
     */
    private void operationLogForCreateSubUsers(String methodResult, OperationLogBaseDto baseDto) {
        CreateUsersRequest request = JacksonUtils.fromJson(baseDto.getParam(), CreateUsersRequest.class);
        List<String> userAccountS = request.getUsers().stream().map(u -> u.getAccount()).collect(Collectors.toList());
        baseDto.setTagName(StringUtils.join(userAccountS,","));
        if (StringUtil.isNotEmpty(methodResult)) {
            RestResult restResult = JacksonUtils.fromJson(methodResult, RestResult.class);
            Optional.ofNullable(restResult.getData()).ifPresent(bizId -> baseDto.setBizId(bizId.toString()));
        }
    }

    /**
     * 导入hcso用户
     *
     * @param methodResult 方法结果
     * @param baseDto      基地dto
     */
    private void operationLogForBatchInsertHcsoUser(String methodResult, OperationLogBaseDto baseDto) {
        if (StringUtil.isNotEmpty(methodResult)) {
            RestResult restResult = JacksonUtils.fromJson(methodResult, RestResult.class);
            List<HcsoUserTemplateVO> hcsoUserTemplateVOList = (List<HcsoUserTemplateVO>)restResult.getData();
            // list转换为json再转为list，防止出现参数类型问题
            hcsoUserTemplateVOList = JSON.parseArray(JSON.toJSONString(hcsoUserTemplateVOList), HcsoUserTemplateVO.class);
            // 导入接口的成功根据返回参数的值做具体判断，不根据最终返回状态确定
            if (hcsoUserTemplateVOList.stream().anyMatch(hcsoUserTemplateVO -> "failed".equals(hcsoUserTemplateVO.getViewStatus()))){
                baseDto.setSuccess(false);
            }
        }
    }

    /**
     * 验证映射租户正确性
     *
     * @param methodResult 方法结果
     * @param baseDto      基地dto
     */
    private void operationLogForVerifyHcsoUser(String methodResult, OperationLogBaseDto baseDto) {
        if (StringUtil.isNotEmpty(methodResult)) {
            RestResult restResult = JacksonUtils.fromJson(methodResult, RestResult.class);
            Optional.ofNullable(restResult.getData()).ifPresent(tagName->baseDto.setTagName(String.valueOf(tagName)));
        }
    }

    /**
     * 导入客户/账户
     *
     * @param methodResult 方法结果
     * @param baseDto      基地dto
     */
    private void operationLogForBatchImportAccountInfo(JoinPoint point,String methodResult, OperationLogBaseDto baseDto) {
        if (StringUtil.isNotEmpty(methodResult)) {
            RestResult restResult = JacksonUtils.fromJson(methodResult, RestResult.class);
            // 导入客户
            if (point.getArgs().length > 1) {
                List<BillingAccountTemplate> billingAccountTemplates = (List<BillingAccountTemplate>) restResult.getData();
                // list转换为json再转为list，防止出现参数类型问题
                billingAccountTemplates = JSON.parseArray(JSON.toJSONString(billingAccountTemplates), BillingAccountTemplate.class);
                // 导入接口的成功根据返回参数的值做具体判断，不根据最终返回状态确定
                if (billingAccountTemplates.stream().anyMatch(billingAccountTemplate -> "failed".equals(billingAccountTemplate.getResult()))) {
                    baseDto.setSuccess(false);
                }
            // 导入账户
            } else {
                List<SubUserTemplate> subUser = (List<SubUserTemplate>) restResult.getData();
                // list转换为json再转为list，防止出现参数类型问题
                subUser = JSON.parseArray(JSON.toJSONString(subUser), SubUserTemplate.class);
                // 导入接口的成功根据返回参数的值做具体判断，不根据最终返回状态确定
                if (subUser.stream().anyMatch(s -> "failed".equals(s.getResult()))) {
                    baseDto.setSuccess(false);
                }
            }
        }
    }

    /**
     * 注册用户(前置)
     *
     * @param baseDto 基地dto
     * @param point   点
     */
    private void operationLogRegisterUser(JoinPoint point, OperationLogBaseDto baseDto) {
        RegisterUserRequest registerUserRequest = (RegisterUserRequest) point.getArgs()[0];
        Optional.ofNullable(registerUserRequest.getUser()).ifPresent(user -> {
            baseDto.setTagName(user.getAccount());
            // 默认修改失败，返回资源id:-1
            baseDto.setBizId("-1");
            baseDto.setAccount(user.getAccount());
        });
    }


    /**
     * 询价
     *
     * @param point   点
     * @param baseDto 基地dto
     */
    private void operationLogForInquiryPrice(JoinPoint point, OperationLogBaseDto baseDto) {
        ApplyServiceRequest applyServiceRequest = (ApplyServiceRequest) point.getArgs()[0];
        Optional.ofNullable(applyServiceRequest).ifPresent(a->{
            if ("apply".equals(a.getOrderType())){
                baseDto.setTagName("ModelArts专属资源池");
            } else if ("apply-other_service".equals(a.getOrderType())){
                baseDto.setTagName("ModelArts共享资源池");
            }
        });
    }

    /**
     * 充值
     *
     * @param baseDto 基地dto
     */
    private void operationLogForCharge(OperationLogBaseDto baseDto) {
        RechargeBillingAccountRequest request = JacksonUtils.fromJson(baseDto.getParam(), RechargeBillingAccountRequest.class);
        BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectByPrimaryKey(Long.valueOf(baseDto.getBizId()));
        Optional.ofNullable(bizBillingAccount).ifPresent(b -> baseDto.setTagName(b.getAccountName()));
        String logDetail = "平台充值：" + "￥" + request.getAmount();
        if (doubleAuditOpen(ProcessBusinessEnum.CASH_RECHARGE.getBusinessCode())) {
            baseDto.setResource(OperationResourceEnum.CHARGE_PROCESS);
        }
        insertActionLog(request.getId(), baseDto.getResource(), logDetail);
    }

    /**
     * 编辑信用额度
     * @param baseDto
     */
    private void operationLogForUpdateCreditLine(OperationLogBaseDto baseDto) {
        UpdateCreditLineRequest request = JacksonUtils.fromJson(baseDto.getParam(), UpdateCreditLineRequest.class);
        BizBillingAccount billingAccount = bizBillingAccountMapper.selectById(request.getAccountId());
        baseDto.setBizId(request.getAccountId().toString());
        Optional.ofNullable(billingAccount).ifPresent(i->baseDto.setTagName(billingAccount.getAccountName()));
        String logDetail = "调整信用额度为：" + "￥" + request.getCreditLine();
        if (doubleAuditOpen(ProcessBusinessEnum.QUOTA_ADJUSTMENT.getBusinessCode())) {
            baseDto.setResource(OperationResourceEnum.CREDIT_PROCESS);
        }
        insertActionLog(request.getAccountId(), baseDto.getResource(), logDetail);
    }

    /**
     * 操作分发优惠券
     *
     * @param baseDto 基地dto
     */
    private void operationLogForDisrbuteCoupon(OperationLogBaseDto baseDto, JoinPoint point) {
        DistributeCashCouponRequest request = (DistributeCashCouponRequest) point.getArgs()[1];
        if (doubleAuditOpen(ProcessBusinessEnum.COUPONS_DISTRIBUTE.getBusinessCode())) {
            baseDto.setResource(OperationResourceEnum.DISTRIBUTECOUPON_PROCESS);
        }
        request.getAccountId().forEach(accountid -> {
            // 获取优惠券信息
            BizCoupon coupon = bizCouponMapper.selectByPrimaryKey(Long.valueOf(baseDto.getBizId()));
            String logDetail =
                    "发放优惠券(金额：￥" + coupon.getDiscountAmount().setScale(3, BigDecimal.ROUND_HALF_UP) + ",优惠卷编号：" + coupon
                            .getCouponNo() + ")";
            insertActionLog(accountid, baseDto.getResource(), logDetail);
        });
    }

    /**
     * 操作分发充值现金券
     *
     * @param baseDto 基地dto
     */
    private void operationLogForDistributeCash(OperationLogBaseDto baseDto) {
        if (doubleAuditOpen(ProcessBusinessEnum.CASH_COUPON_DISTRIBUTE_DEPOSIT.getBusinessCode())) {
            baseDto.setResource(OperationResourceEnum.DISTRIBUTECASH_PROCESS);
        }
    }

    /**
     * 操作分发抵扣现金券
     *
     * @param baseDto 基地dto
     */
    private void operationLogForDistributeDuctioncash(OperationLogBaseDto baseDto) {
        if (doubleAuditOpen(ProcessBusinessEnum.CASH_COUPON_DISTRIBUTE_DEDUCT.getBusinessCode())) {
            baseDto.setResource(OperationResourceEnum.DISTRIBUTEDEDUCTIONCASH_PROCESS);
        }
    }

    /**
     * 注册用户(后置)
     *
     * @param methodResult 方法结果
     * @param baseDto      基地dto
     */
    private void operationLogRegisterUser(String methodResult, OperationLogBaseDto baseDto) {
        if (StringUtil.isNotEmpty(methodResult)) {
            RestResult restResult = JacksonUtils.fromJson(methodResult, RestResult.class);
            // 注册成功修改userSid
            if (Objects.nonNull(restResult) && restResult.getStatus()) {
                baseDto.setBizId(String.valueOf(restResult.getData()));
            }
        }
    }

    /**
     * 认证审核
     *
     * @param baseDto
     */
     private void  operationLogCertificationAudit(OperationLogBaseDto baseDto){
         if(StringUtils.isNotEmpty(baseDto.getBizId())){
             cn.com.cloudstar.rightcloud.core.pojo.dto.user.User user = userService.selectByPrimaryKey(Long.valueOf(baseDto.getBizId()));
             Optional.ofNullable(user).ifPresent(newUser -> baseDto.setTagName(newUser.getAccount()));
         }
     }


    /**
     * 启用用户资源权限/禁用用户资源权限/解冻/提交实名认证/导航栏确认
     *
     * @param baseDto
     */
    private void operationLogForDisableFreezeStatus(OperationLogBaseDto baseDto) {
        if(StringUtils.isNotEmpty(baseDto.getBizId())){
            cn.com.cloudstar.rightcloud.core.pojo.dto.user.User user = userService.selectByPrimaryKey(Long.valueOf(baseDto.getBizId()));
            Optional.ofNullable(user).ifPresent(user1 -> baseDto.setTagName(user1.getAccount()));
        }
    }

    /**
     * 拒绝用户，记录操作日志
     */
    private void operationLogForRefuseUser(OperationLogBaseDto baseDto) {
        if(StringUtils.isNotEmpty(baseDto.getBizId())){
            cn.com.cloudstar.rightcloud.core.pojo.dto.user.User user = userService.selectByPrimaryKey(Long.valueOf(baseDto.getBizId()));
            Optional.ofNullable(user).ifPresent(user1 -> baseDto.setTagName(user1.getAccount()));
            //添加操作日志
            String logDetail = "拒绝账户激活。";
            Long userSid = Convert.toLong(baseDto.getBizId(), null);
            if (ObjectUtils.isNotEmpty(userSid)) {
                BizBillingAccount bizBillingAccount = bizBillingAccountMapper.getByAdminId(userSid);
                insertActionLog(bizBillingAccount.getId(), OperationResourceEnum.ACTIVATE_USER, logDetail);
            }
        }
    }

    /**
     * 激活用户，记录操作日志
     */
    private void operationLogForActivateUser(OperationLogBaseDto baseDto) {
        if(StringUtils.isNotEmpty(baseDto.getBizId())){
            cn.com.cloudstar.rightcloud.core.pojo.dto.user.User user = userService.selectByPrimaryKey(Long.valueOf(baseDto.getBizId()));
            Optional.ofNullable(user).ifPresent(user1 -> baseDto.setTagName(user1.getAccount()));
            //添加操作日志
            String logDetail = "对账户进行了激活。";
            Long userSid = Convert.toLong(baseDto.getBizId(), null);
            if (ObjectUtils.isNotEmpty(userSid)) {
                BizBillingAccount bizBillingAccount = bizBillingAccountMapper.getByAdminId(userSid);
                insertActionLog(bizBillingAccount.getId(), OperationResourceEnum.ACTIVATE_USER, logDetail);
            }
        }
    }

    /**
     * 冻结/解冻操作日志
     *
     * @param baseDto 基地dto
     */
    private void operationLogForEnableFreezestatus(JoinPoint point, OperationLogBaseDto baseDto) throws NoSuchMethodException {
        //获取相关参数
        Long userSid = Long.valueOf(baseDto.getBizId());
        Long accountId = 0L;
        Criteria criteria = new Criteria();
        criteria.put("adminSid",userSid);
        criteria.put("entityId",RequestContextUtil.getEntityId());
        List<BizBillingAccount> bizBillingAccounts = bizBillingAccountMapper.selectByParams(criteria);
        cn.com.cloudstar.rightcloud.core.pojo.dto.user.User user = userService.selectByPrimaryKey(userSid);
        Optional.ofNullable(user).ifPresent(e -> baseDto.setTagName(e.getAccount()));
        if (CollectionUtils.isNotEmpty(bizBillingAccounts)){
            accountId = bizBillingAccounts.get(0).getId();
        }
        Method targetMethod = findTargetMethod(point);
        String logDetail;
        if ("disableFreezeStatus".equals(targetMethod.getName())) {
            logDetail = "手动冻结了资源权限";
            insertActionLog(accountId, OperationResourceEnum.DISABLE_RESOURCESTATUS, logDetail);
        } else if ("enableFreezeStatus".equals(targetMethod.getName())) {
            logDetail = "手动解冻了资源权限";
            insertActionLog(accountId, OperationResourceEnum.ENABLE_FREEZESTATUS, logDetail);
        }
    }

    /**
     * 操作日志给用户发送重置密码邮件
     *
     * @param point   点
     * @param baseDto 基地dto
     */
    private void operationLogForSendResetPwdEmail(JoinPoint point, OperationLogBaseDto baseDto) {
        SendEmailRequest arg = (SendEmailRequest) point.getArgs()[0];
        List<Long> userIdList = arg.getUserIdList();
        List<String> accounts = new ArrayList<>();
        for (Long userId : userIdList) {
            cn.com.cloudstar.rightcloud.core.pojo.dto.user.User user = userMapper.selectByPrimaryKey(userId);
            if (Objects.isNull(user)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_782004165));
            }
            accounts.add(user.getAccount());
        }
        baseDto.setTagName(accounts.toString());
        baseDto.setBizId(StringUtils.join(arg.getUserIdList(),","));
    }

    /**
     * 操作日志重置用户密码
     *
     * @param baseDto 基地dto
     */
    private void operationLogForResetUserPassword(OperationLogBaseDto baseDto) {
        if (StringUtils.isNotEmpty(baseDto.getBizId())) {
            List<String> userIds = Arrays.asList(baseDto.getBizId().split(","));
            List<String> accounts = new ArrayList<>();
            for (String userId : userIds) {
                cn.com.cloudstar.rightcloud.core.pojo.dto.user.User user = userMapper.selectByPrimaryKey(Long.valueOf(userId));
                // 手动拼接tagName
                Optional.ofNullable(user).ifPresent(u -> accounts.add(user.getAccount() + "，资源ID：" + userId));
            }
            baseDto.setTagName(StringUtils.join(accounts, "；"));
            // bizId置空，防止二次组装
            baseDto.setBizId(null);
        }
    }

    /**
     * 操作日志角色授权
     *
     * @param point   点
     * @param baseDto 基地dto
     */
    private void operationLogForRoleAuthorize(JoinPoint point, OperationLogBaseDto baseDto) {
        AuthorizeRoleRequest arg = (AuthorizeRoleRequest) point.getArgs()[0];
        Role role = roleMapper.selectByPrimaryKey(arg.getRoleSid());
        if (Objects.isNull(role)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1618268500));
        }
        baseDto.setTagName(role.getRoleName());
    }

    /**
     * 操作日志删除角色
     *
     * @param point   点
     * @param baseDto 基地dto
     */
    private void operationLogForDelRole(JoinPoint point, OperationLogBaseDto baseDto) {
        Long arg = (Long) point.getArgs()[0];
        Role role = roleMapper.selectByPrimaryKey(arg);
        if (Objects.isNull(role)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1618268500));
        }
        baseDto.setTagName(role.getRoleName());
    }

    /**
     * 操作日志批量更新配置
     *
     * @param baseDto 基地dto
     */
    private void operationLogForUpdateConfigs(JoinPoint point, OperationLogBaseDto baseDto) {
        List<UpdateSysConfigsRequest> args = (List<UpdateSysConfigsRequest>) point.getArgs()[0];
        List<String> configType = args.stream().map(UpdateSysConfigsRequest::getConfigType).collect(Collectors.toList());
        String s = configType.toString();
        if (s.contains("system_config")
                || s.contains("logintips_config")
                || s.contains("other_config")
                || s.contains("regist_address")) {
            baseDto.setTagName("系统配置基础配置");
            baseDto.setResource(OperationResourceEnum.UPDATE_CONFIGS1);
        } else if (s.contains("email_server_config")
                || s.contains("sms_config")) {
            baseDto.setTagName("消息网关");
            baseDto.setResource(OperationResourceEnum.UPDATE_CONFIGS2);
        } else if (s.contains("ldap_config")) {
            baseDto.setTagName("三方认证");
            baseDto.setResource(OperationResourceEnum.UPDATE_CONFIGS3);
        } else if (s.contains("sensitive_word_config")
                || s.contains("expired_config")
                || s.contains("customer_max_config")) {
            baseDto.setTagName("业务配置");
            baseDto.setResource(OperationResourceEnum.UPDATE_CONFIGS4);
        } else if (s.contains("screen_config")) {
            baseDto.setTagName("大屏配置");
            baseDto.setResource(OperationResourceEnum.UPDATE_CONFIGS5);
        } else if (s.contains("system_upgrade")) {
            baseDto.setTagName("升级配置");
            baseDto.setResource(OperationResourceEnum.UPDATE_CONFIGS6);
        } else if (contianConfigs(s)) {
            baseDto.setTagName("安全设置基础配置");
            baseDto.setResource(OperationResourceEnum.UPDATE_CONFIGS7);
        }
    }

    private static boolean contianConfigs(String s) {
        return Stream.of("cert_expire_warn_config", "credential_strategy_config", "loginaccount_config",
                "loginfailure_config", "loginlimit_config", "session_config", "keycloak_admin_config")
            .anyMatch(value -> s.contains(value));
    }

    /**
     * 操作日志更新通知配置状态
     *
     * @param point   点
     * @param baseDto 基地dto
     */
    private void operationLogForSwitchNotifyPolicyStatus(JoinPoint point, OperationLogBaseDto baseDto) {
        Long arg = (Long) point.getArgs()[0];
        SysMNotifyConfig sysMNotifyConfig = sysMNotifyConfigMapper.selectByPrimaryKey(arg);
        if (Objects.isNull(sysMNotifyConfig)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1128534018));
        }
        switch (sysMNotifyConfig.getBssType()) {
            case "billing":
                baseDto.setTagName("费用通知");
                break;
            case "biz_bag_expire":
                baseDto.setTagName("套餐包过期通知");
                break;
            case "password_expiration_replacement":
                baseDto.setTagName("密码到期更换通知");
                break;
            case "drp_resource_expire":
                baseDto.setTagName("昇腾Modelarts专属资源池到期通知");
                break;
            case "mq_backlog_alarm":
                baseDto.setTagName("MQ消息积压通知");
                break;
            case "license_expire":
                baseDto.setTagName("许可证到期通知");
                break;
            case "csdr_bill_backlog_warning":
                baseDto.setTagName("话单出账积压异常通知");
                break;
            case "csdr_collector_warning":
                baseDto.setTagName("话单采集异常通知");
                break;
            case "bssaccount_expired":
                baseDto.setTagName("客户账号到期通知");
                break;
            default:
                break;
        }
        if (StringUtil.equals(SysMNotifyConfigConstant.StatusEnum.ENABLE.getValue(), baseDto.getParam())) {
            baseDto.setResource(OperationResourceEnum.SWITCH_NOTIFY_POLICY_STATUS_ENABLE);
        } else if (StringUtil.equals(SysMNotifyConfigConstant.StatusEnum.DISABLE.getValue(), baseDto.getParam())) {
            baseDto.setResource(OperationResourceEnum.SWITCH_NOTIFY_POLICY_STATUS_DISABLE);
        }
    }

    /**
     * 操作日志更新通知配置
     *
     * @param point   点
     * @param baseDto 基地dto
     */
    private void operationLogForUpdateNotifyConfig(JoinPoint point, OperationLogBaseDto baseDto) {
        Long arg = (Long) point.getArgs()[0];
        SysMNotifyConfig sysMNotifyConfig = sysMNotifyConfigMapper.selectByPrimaryKey(arg);
        if (Objects.isNull(sysMNotifyConfig)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1128534018));
        }
        switch (sysMNotifyConfig.getBssType()){
            case "billing":
                baseDto.setTagName("费用通知");
                break;
            case "sfs_storage":
                baseDto.setTagName("sfs存储通知");
                break;
            case "drp_resource_expire":
                baseDto.setTagName("昇腾Modelarts专属资源池到期通知");
                break;
            default:
                break;
        }
    }

    /**
     * 操作日志删除通知配置
     *
     * @param point   点
     * @param baseDto 基地dto
     */
    private void operationLogForDeleteNotifyConfig(JoinPoint point, OperationLogBaseDto baseDto) {
        Long arg = (Long) point.getArgs()[0];
        SysMNotifyConfig sysMNotifyConfig = sysMNotifyConfigMapper.selectByPrimaryKey(arg);
        if (Objects.isNull(sysMNotifyConfig)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1128534018));
        }
        switch (sysMNotifyConfig.getBssType()){
            case "billing":
                baseDto.setTagName("费用通知");
                break;
            case "sfs_storage":
                baseDto.setTagName("sfs存储通知");
                break;
            case "drp_resource_expire":
                baseDto.setTagName("昇腾Modelarts专属资源池到期通知");
                break;
            default:
                break;
        }
    }

    /**
     * 操作日志重新发送通知
     *
     * @param point   点
     * @param baseDto 基地dto
     */
    private void operationLogForResendNotify(JoinPoint point, OperationLogBaseDto baseDto) {
        Long arg = (Long) point.getArgs()[0];
        SysMNotifyConfig sysMNotifyConfig = sysMNotifyConfigMapper.selectByPrimaryKey(arg);
        if (sysMNotifyConfig != null) {
            baseDto.setTagName(sysMNotifyConfig.getBssType());
        }
    }

    /**
     * 操作日志添加IP的地址或者网段
     *
     * @param baseDto 基地dto
     * @param point   点
     */
    private void operationLogForAddIpConfig(JoinPoint point, OperationLogBaseDto baseDto) {
        IpConfigRequest args = (IpConfigRequest) point.getArgs()[0];
        List<IpAddressVO> ipInfos = args.getIpInfos();
        List<String> ips = new ArrayList<>();
        for (IpAddressVO ipInfo : ipInfos) {
            ips.add(ipInfo.getIp());
        }

        OperationResourceEnum resource = baseDto.getResource();
        if (resource != null) {
            if(0 == args.getIpType()) {
                resource.setDesc("添加允许访问的ip地址或网段");
            }else{
                resource.setDesc("添加不允许访问的ip地址或网段");
            }
        }

        if ("console".equals(args.getControlType())) {
            baseDto.setTagName("用户控制台->" + ips.toString());
        } else {
            baseDto.setTagName("管理控制台->" + ips.toString());
        }
    }

    /**
     * 操作日志删除IP的地址或者网段
     *
     * @param point   点
     * @param baseDto 基地dto
     */
    private void operationLogForDelIpConfig(JoinPoint point, OperationLogBaseDto baseDto) {
        Object[] args = point.getArgs();
        SysMIpConfig sysMIpConfig = sysMIpConfigMapper.selectByPrimaryKey((Long) args[2]);
        if (Objects.isNull(sysMIpConfig)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1825668691));
        }
        OperationResourceEnum resource = baseDto.getResource();
        if (resource != null) {
            if(0 == sysMIpConfig.getIpType()) {
                resource.setDesc("删除允许访问的ip地址或网段");
            }else{
                resource.setDesc("删除不允许访问的ip地址或网段");
            }
        }
        if ("console".equals(args[0])) {
            baseDto.setTagName("用户控制台->" + sysMIpConfig.getIp());
        } else {
            baseDto.setTagName("管理控制台->" + sysMIpConfig.getIp());
        }
    }

    /**
     * 操作日志批量删除IP的地址或者网段
     *
     * @param point   点
     * @param baseDto 基地dto
     */
    private void operationLogForDelIpConfigs(JoinPoint point, OperationLogBaseDto baseDto) {
        DeleteIpConfigsRequest arg = (DeleteIpConfigsRequest) point.getArgs()[0];
        List<String> ids = arg.getIds();
        List<String> ips;
        try {
            ips = sysMIpConfigMapper.selectByIds(ids);
        } catch (Exception e) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1825668691));
        }
        if(CollectionUtils.isEmpty(ips)){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1825668691));
        }
        OperationResourceEnum resource = baseDto.getResource();
        if (resource != null) {
            SysMIpConfig sysMIpConfig = sysMIpConfigMapper.selectByPrimaryKey(Long.valueOf(ips.get(0)));
            if(0 == sysMIpConfig.getIpType()) {
                resource.setDesc("批量删除允许访问的ip地址或网段");
            }else{
                resource.setDesc("批量删除不允许访问的ip地址或网段");
            }
        }

        if ("console".equals(arg.getControlType())) {
            baseDto.setTagName("用户控制台->" + ips.toString());
        } else {
            baseDto.setTagName("管理控制台->" + ips.toString());
        }
        baseDto.setBizId(StringUtils.join(arg.getIds(),","));
    }

    /**
     * 操作日志补采话单
     *
     * @param baseDto 基地dto
     */
    private void operationLogForCollectorRepair(JoinPoint point, OperationLogBaseDto baseDto) {
        CollectorRepairRequest arg = (CollectorRepairRequest) point.getArgs()[0];
        baseDto.setTagName(LogEnum.get(arg.getType().toString()));
    }

    /**
     * ip访问设置细分
     * @param baseDto
     */
    private void updIpControl(OperationLogBaseDto baseDto){
        IpControlRequest request = JacksonUtils.fromJson(baseDto.getParam(), IpControlRequest.class);
        OperationResourceEnum resource = baseDto.getResource();
        if(resource != null){
            if(!ObjectUtils.isEmpty(request.getIpType()) && 0 == request.getIpType()) {
                resource.setDesc("更新允许访问的ip地址或网段");
            }else{
                resource.setDesc("更新不允许访问的ip地址或网段");
            }
        }
        Optional.ofNullable(Objects.requireNonNull(request).getIpInfo()).ifPresent(r->{
            if("console".equals(request.getControlType())){
                baseDto.setTagName("用户控制台->" + request.getIpInfo().getIp());
            }else{
                baseDto.setTagName("管理控制台->" + request.getIpInfo().getIp());
            }
            baseDto.setResource(OperationResourceEnum.UPDIPCONTROL_IP);
        });
        Optional.ofNullable(request.getIpControlFlg()).ifPresent(i->{
            if("console".equals(request.getControlType())){
                baseDto.setTagName("用户控制台ip访问控制");
            }else{
                baseDto.setTagName("管理控制台ip访问控制");
            }

            if(i.equals(0)){
                baseDto.setResource(OperationResourceEnum.UPDIPCONTROL_DISABLE);
            }else{
                baseDto.setResource(OperationResourceEnum.UPDIPCONTROL_ENABLE);
            }
        });
        Optional.ofNullable(request.getIpRequestLimit()).ifPresent(l->{
            if("console".equals(request.getControlType())){
                baseDto.setTagName("用户控制台ip请求速率");
            }else{
                baseDto.setTagName("管理控制台ip请求速率");
            }
            baseDto.setResource(OperationResourceEnum.UPDIPCONTROL_SPEED);
        });
    }

    /**
     * 上传文件
     * @param baseDto
     */
    private void operationLogForUploadFile(OperationLogBaseDto baseDto){
        if ("上传平台图片".equals(baseDto.getTagName())){
            List files = JacksonUtils.fromJson(baseDto.getParam(), List.class);
            baseDto.setTagName(LogEnum.get(((LinkedHashMap) files.get(0)).get("type").toString()));
        }
        baseDto.setTagName(LogEnum.get(baseDto.getTagName()));
    }


    /**
     * 操作日志上传文件后置处理
     *
     * @param methodResult 方法结果
     * @param baseDto      基地dto
     */
    private void operationLogForUploadFileAfter(String methodResult, OperationLogBaseDto baseDto) {
        if (StringUtil.isNotEmpty(methodResult)) {
            RestResult restResult = JacksonUtils.fromJson(methodResult, RestResult.class);
            // 注册成功修改userSid
            if (Objects.nonNull(restResult) && restResult.getStatus()) {
                if (restResult.getData().toString().contains("DT")){
                    baseDto.setBizId(restResult.getData().toString());
                } else {
                    String[] s = restResult.getData().toString().split("/");
                    baseDto.setBizId(s[s.length - 1]);
                }
            }
        }
    }

    /**
     * 修改自身真实姓名
     * @param baseDto
     */
    private void operationLogForUpdateUserSelf(OperationLogBaseDto baseDto){
        if(StringUtils.isNotEmpty(baseDto.getBizId())){
            cn.com.cloudstar.rightcloud.core.pojo.dto.user.User user = userService.selectByPrimaryKey(Long.valueOf(baseDto.getBizId()));
            Optional.ofNullable(user).ifPresent(newUser -> baseDto.setTagName(newUser.getAccount()));
        }
    }

    /**
     * 删除用户
     * @param baseDto
     */
    private void operationLogForDelUser(OperationLogBaseDto baseDto){
        DeleteCompanyUserRequest deleteCompanyUserRequest = JacksonUtils.fromJson(baseDto.getParam(), DeleteCompanyUserRequest.class);
        List<String> userNames = new ArrayList<>();
        if (Objects.nonNull(deleteCompanyUserRequest) && Objects.nonNull(deleteCompanyUserRequest.getUserIds())) {
            deleteCompanyUserRequest.getUserIds().forEach(uid -> {
                        cn.com.cloudstar.rightcloud.core.pojo.dto.user.User user = userMapper.selectByPrimaryKey(uid);
                        Optional.ofNullable(user)
                                .ifPresent(u -> userNames.add(u.getAccount()));
                    }
            );
        }
        Optional.of(userNames).ifPresent(u->baseDto.setTagName(u.toString()));
    }

    /**
     * 续订
     *
     * @param baseDto
     */
    private void operationLogForRenewResource(OperationLogBaseDto baseDto) {
        RenewRequest renewRequest = JacksonUtils.fromJson(baseDto.getParam(), RenewRequest.class);
        if (Objects.isNull(renewRequest)) {
            return;
        }
        ProductInfoVO productInfoVO = Optional.ofNullable(renewRequest.getProductInfo()).map(CollectionUtil::getFirst).orElse(null);
        Optional.ofNullable(productInfoVO).ifPresent(p -> {
            SfProductResource sfProductResource = sfProductResourceMapper.selectByPrimaryKey(Long.valueOf(p.getId()));
            if (Objects.nonNull(sfProductResource)) {
                ServiceOrder serviceOrder = serviceOrderMapper.selectByPrimaryKey(sfProductResource.getServiceOrderId());
                baseDto.setTagName("续费产品" + serviceOrder.getName());
            } else {
                baseDto.setTagName("续费产品" + p.getName());
            }
            baseDto.setBizId(p.getId());
            if ("DRP".equals(p.getProductCode())) {
                baseDto.setResource(OperationResourceEnum.RENEWRESOURCE);
            }
        });
    }


    /**
     * 修改工单分类
     */
    private void operationLogUpdateTicketCategory(OperationLogBaseDto baseDto, JoinPoint point) {
        CreateUpdateTicketCategoryRequest request = (CreateUpdateTicketCategoryRequest)point.getArgs()[0];
        if (ObjectUtils.isNotEmpty(request.getId())) {
            TicketCategory ticketCategory = ticketCategoryMapper.selectByPrimaryKey(request.getId());
            //启用/禁用工单类型
            if (!ticketCategory.getEnable().equals(request.getEnable())) {
                baseDto.setResource(OperationResourceEnum.UPDATE_TICKET_CATEGORY_ENABLE);
                String prefix = Convert.toBool(request.getEnable(), false) ? "启用": "禁用";
                baseDto.setTagName(prefix + "工单类型");
            }
            baseDto.setBizId(String.valueOf(request.getId()));
        }
    }

    /**
     * 用户实名认证，记录操作日志
     */
    private void operationLogForCertification(OperationLogBaseDto baseDto, JoinPoint point) {
        AuthUserRequest request = (AuthUserRequest)point.getArgs()[0];
        String logDetail = "对账户进行了认证审核。";
        Long userSid = request.getUserSid();
        BizBillingAccount account = bizBillingAccountMapper.selectByUserSid(userSid);
        insertActionLog(account.getId(), OperationResourceEnum.CERTIFICATIONAUDIT, logDetail);
    }

    private void operationLogForForgetPassword(OperationLogBaseDto baseDto, JoinPoint point) {
        ForgetPwdRequest request = (ForgetPwdRequest)point.getArgs()[0];
        if (ObjectUtils.isNotEmpty(request.getPhone())) {
            baseDto.setResource(OperationResourceEnum.FORGET_PASSWORD_RESET_PWD_BY_PHONE);
            baseDto.setTagName(DesensitizedUtil.mobilePhone(request.getPhone()));
        } else {
            baseDto.setResource(OperationResourceEnum.FORGET_PASSWORD_RESET_PWD_BY_EMAIL);
            baseDto.setTagName(DesensitizedUtil.email(request.getEmail()));
        }
    }

    private void operationLogForUpdateTemplateStatus(OperationLogBaseDto baseDto, JoinPoint point) {
        String status = (String) point.getArgs()[1];
        if (ObjectUtil.isEmpty(status)) {
            return;
        }
        if ("enable".equalsIgnoreCase(status)) {
            baseDto.setResource(OperationResourceEnum.UPDATE_ENABLE_FORM_TEMPLATE_STATUS);
            baseDto.setTagName("启用流程模板");
        } else if ("disable".equalsIgnoreCase(status)) {
            baseDto.setResource(OperationResourceEnum.UPDATE_DISABLE_FORM_TEMPLATE_STATUS);
            baseDto.setTagName("禁用流程模板");
        }
    }


    private void operationLogForEditCustomer(OperationLogBaseDto baseDto, JoinPoint point) {
        UpdateAccountRequest req = (UpdateAccountRequest) point.getArgs()[0];
        String logDetail = "修改了客户信息";
        insertActionLog(req.getId(), OperationResourceEnum.EDIT_CUSTOMER, logDetail);
    }

    enum LogEnum {
        /**
         * 对象存储
         */
        TYPE_1("1", "对象存储"),
        /**
         * Modelarts
         */
        TYPE_2("2", "Modelarts"),
        /**
         * 弹性文件服务
         */
        TYPE_3("3", "弹性文件服务"),
        /**
         * 高性能计算
         */
        TYPE_4("4", "高性能计算"),
        /**
         * 订单
         */
        FILE_1("service_order","订单"),
        /**
         * 收支明细
         */
        FILE_2("bizAccountDeal","收支明细"),
        /**
         * 账单周期
         */
        FILE_3("billCycle","账单周期"),
        /**
         * 账单明细
         */
        FILE_4("billDetail","账单明细"),
        /**
         * 图片文件
         */
        FILE1("picture","图片文件"),
        /**
         * 合同文件
         */
        FILE2("contract","合同文件"),
        /**
         * 身份证文件
         */
        FILE3("id_card","认证图片"),
        /**
         * 身份证文件
         */
        FILE4("auth","认证图片"),
        /**
         * 工单文件
         */
        FILE5("ticket","工单文件"),
        /**
         * 配置文件
         */
        FILE6("rest","配置文件"),
        /**
         * 平台图标
         */
        FILE7("logo","平台图标"),
        /**
         * 登录图片
         */
        FILE8("login","登录图片"),
        /**
         * 平台徽标
         */
        FILE9("favicon","平台徽标"),
        /**
         * 通用文件，目前只有产品模板使用
         */
        FILE10("files","产品附件"),
        /**
         * 数量
         */
        QUANTITY("quantity", "数量"),
        /**
         * 金额
         */
        MONEY("money", "金额"),
        /**
         * 时间
         */
        TIME("time", "时间"),
        /**
         * 无限制
         */
        UNLIMITED("unlimited", "无限制"),
        /**
         * 启用
         */
        ENABLE("enable", "启用"),
        /**
         * 禁用
         */
        DISABLE("disable", "禁用"),
        /**
         * 删除
         */
        DELETE("delete", "删除");
        private final String key;
        private final String value;

        LogEnum(String key, String value) {
            this.key = key;
            this.value = value;
        }

        public static String get(String key) {
            for (LogEnum logEnum : LogEnum.values()) {
                if (key.equals(logEnum.getKey())) {
                    return logEnum.getValue();
                }
            }
            return key;
        }

        public String getKey() {
            return key;
        }

        public String getValue() {
            return value;
        }


    }

    /**
     * 插入操作日志(mysql-biz_customer_action_log)
     *
     * @param accountId             账户id
     * @param operationResourceEnum 操作资源枚举
     * @param logDetail             日志详细
     */
    private void insertActionLog(Long accountId, OperationResourceEnum operationResourceEnum, String logDetail) {
        BizCustomerActionLog actionLogDTO = new BizCustomerActionLog();
        BizBillingAccount billingAccount = bizBillingAccountMapper.selectByPrimaryKey(accountId);
        if (ObjectUtils.isEmpty(billingAccount)) {
            log.error("billingAccount为空，客户日志记录失败");
            return;
        }
        actionLogDTO.setUserSid(billingAccount.getAdminSid());
        actionLogDTO.setOrgSid(billingAccount.getOrgSid());
        actionLogDTO.setOpType(operationResourceEnum.getType());
        actionLogDTO.setOpDetail(logDetail);
        actionLogDTO.setOpDate(new Date());
        actionLogDTO.setOpUser(RequestContextUtil.getAuthUserInfo().getUserSid());
        bizCustomerActionLogMapper.insert(actionLogDTO);
    }

    /**
     * 找到目标方法
     *
     * @param joinPoint 连接点
     * @return {@code Method}
     * @throws NoSuchMethodException 号这样方法异常
     */
    private Method findTargetMethod(JoinPoint joinPoint) throws NoSuchMethodException {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Class[] parameterTypes = signature.getParameterTypes();
        return ClassUtils.getUserClass(joinPoint.getTarget()).getMethod(joinPoint.getSignature().getName(), parameterTypes);
    }

    private boolean doubleAuditOpen(String businessCode){
        String configValue = sysConfigService.getValueByConfigKey(MULTIPLE_AUDITS_ENABLE);
        if (OPEN.equals(configValue)) {
            return CollectionUtil.isNotEmpty(bindProcessMapper.selectByBusinessCode(businessCode));
        }
        return false;
    }
}
