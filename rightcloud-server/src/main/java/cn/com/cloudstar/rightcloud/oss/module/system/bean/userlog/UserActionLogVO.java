/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.module.system.bean.userlog;

import com.fasterxml.jackson.annotation.JsonIgnore;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * The type UserActionLogVO.
 * <p>
 *
 * <AUTHOR>
 * @date 2019/7/10
 */
@Data
@ApiModel(description = "用户操作日志实体类")
public class UserActionLogVO {
    /**
     * 用户名
     */
    @ApiModelProperty(notes = "用户名")
    private String account;

    /**
     * 角色权限
     */
    @ApiModelProperty(notes = "角色权限")
    private String roleName;

    /**
     * 资源类型
     */
    @ApiModelProperty(notes = "资源类型")
    private String resource;

    /**
     * 访问IP
     */
    @ApiModelProperty(notes = "访问IP")
    private String remoteIp;

    /**
     * 客户端
     */
    @ApiModelProperty(notes = "客户端")
    private String client;

    /**
     * 操作时间
     */
    @ApiModelProperty(notes = "操作时间")
    private String actionTime;

    /**
     * 资源id
     */
    @ApiModelProperty(notes = "资源id")
    private String bizId;

    /**
     * 资源名称
     */
    @ApiModelProperty(notes = "资源名称")
    private String tagName;

    /**
     * 成功标识
     */
    @ApiModelProperty(notes = "成功标识")
    private String success;

    /**
     * 数据是否被篡改
     */
    @ApiModelProperty(notes = "数据是否被篡改")
    @JsonIgnore
    private boolean resultSign;

}
