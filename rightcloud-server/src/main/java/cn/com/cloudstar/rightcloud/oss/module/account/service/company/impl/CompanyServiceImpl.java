/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.module.account.service.company.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.base.Throwables;
import com.google.common.collect.Maps;

import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ReflectUtil;

import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnv;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResVd;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResVm;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResVpc;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResVpcPort;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.common.util.json.JsonUtil;
import cn.com.cloudstar.rightcloud.core.pojo.dto.charge.BizBillingAccount;
import cn.com.cloudstar.rightcloud.core.pojo.dto.saas.SysSaasOrg;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.Code;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.SysConfig;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.SysMgtObjQuota;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.SysQuota;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.Company;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.MgtObj;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.MgtObjDef;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.MgtObjExt;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.Org;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.RoleOrg;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.User;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.oss.common.constants.CodeCategoryConstants;
import cn.com.cloudstar.rightcloud.oss.common.constants.RestConst;
import cn.com.cloudstar.rightcloud.oss.common.constants.WebConstants;
import cn.com.cloudstar.rightcloud.oss.common.constants.res.status.NetworkStatus;
import cn.com.cloudstar.rightcloud.oss.common.constants.res.status.SnapshotStatus;
import cn.com.cloudstar.rightcloud.oss.common.constants.status.CertificationStatus;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.EnvQuotaKey;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.MgtObjType;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.OrgType;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.QuotaMode;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.UserType;
import cn.com.cloudstar.rightcloud.oss.common.enums.SysRoleEnum;
import cn.com.cloudstar.rightcloud.oss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.oss.common.util.AuthUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.IdWorker;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.SaasUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.WebUserUtil;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.openapi.OpenApiKeyMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.org.BizDistributorMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.org.CompanyMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.saas.SysSaasOrgMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.user.MgtObjDefMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.user.MgtObjMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.user.RoleMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.user.RoleOrgMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.user.UserOrgMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.user.UserRoleMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.service.company.CompanyService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.org.MgtObjExtService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.org.MgtObjService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.org.OrgService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.project.ProjectService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.user.UserService;
import cn.com.cloudstar.rightcloud.oss.module.maintenance.dao.tag.CloudTagMapper;
import cn.com.cloudstar.rightcloud.oss.module.pricing.dao.priceconfig.BizBillingAccountMapper;
import cn.com.cloudstar.rightcloud.oss.module.resource.service.CodeService;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.service.ServiceCatalogService;
import cn.com.cloudstar.rightcloud.oss.module.system.dao.config.SysMgtObjQuotaMapper;
import cn.com.cloudstar.rightcloud.oss.module.system.dao.config.SysQuotaMapper;
import cn.com.cloudstar.rightcloud.oss.module.system.service.config.SysConfigService;
import cn.com.cloudstar.rightcloud.oss.util.PropertiesUtil;
import cn.com.cloudstar.rightcloud.remote.api.iam.service.IamRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResVmType;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.CloudKeyPairParams;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.CountResFloatingIpByParamsRequest;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.CountResSnapshotByParamsRequest;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.NetworkParams;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.QueryAllocVdRequest;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.QueryAllocVpcByDfRequest;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.QueryResLoadBalanceByCriteriaRequest;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.QueryResRouterRequest;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.QueryResVmTypeByParamsRequest;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.ResFireWallParams;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.ResFirewallDefaultRuleParams;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.ResSecurityGroupParams;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.ResVmByCriteria;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.ResVpcByParams;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.ResVpcPortParams;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.cloud.CloudEnvRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.firewall.ResFirewallDefaultRuleRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.keypair.CloudKeyPairRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.lb.ResLoadBalanceRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.network.NetworkRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.network.ResFireWallRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.network.ResFloatingIpRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.network.ResRouterRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.network.ResVpcPortRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.network.ResVpcRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.server.ResVmRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.server.ResVmTypeRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.sg.ResSecurityGroupRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.snapshot.ResSnapshotRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.storage.ResVdRemoteService;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ReflectUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.base.Throwables;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * the class CompanyServiceImpl
 *
 * <AUTHOR>
 * @date 2018/1/26.
 */
@Component
@SuppressWarnings("checkstyle:MagicNumber")
public class CompanyServiceImpl implements CompanyService {

    private static final Logger logger = LoggerFactory.getLogger(CompanyServiceImpl.class);

    @Autowired
    private CompanyMapper companyMapper;

    @Autowired
    private OrgService orgService;

    @Autowired
    @Lazy
    private ProjectService projectService;

    @DubboReference
    private CloudEnvRemoteService cloudEnvService;


    @Autowired
    private MgtObjService mgtObjService;

    @Autowired
    private MgtObjExtService mgtObjExtService;

    @Autowired
    private CodeService codeService;

    @Autowired
    private RoleOrgMapper roleOrgMapper;

    @Autowired
    private UserOrgMapper userOrgMapper;

    @Autowired
    private UserRoleMapper userRoleMapper;

    @Autowired
    private MgtObjMapper mgtObjMapper;

    @Autowired
    private MgtObjDefMapper mgtObjDefMapper;

    @Autowired
    private RoleMapper roleMapper;

    @Autowired
    private OpenApiKeyMapper openApiKeyMapper;

    @Autowired
    private ServiceCatalogService serviceCatalogService;

    @Autowired
    private SysQuotaMapper sysQuotaMapper;

    @Autowired
    private SysMgtObjQuotaMapper sysMgtObjQuotaMapper;

    @DubboReference
    private ResVmTypeRemoteService resVmTypeRemoteService;

    @DubboReference
    private ResVmRemoteService resVmService;

    @DubboReference
    private ResVdRemoteService resVdService;

    @DubboReference
    private ResSnapshotRemoteService resSnapshotService;

    @DubboReference
    private ResFloatingIpRemoteService resFloatingIpService;

    @DubboReference
    private ResLoadBalanceRemoteService resLoadBalanceService;

    @DubboReference
    private ResRouterRemoteService resRouterService;

    @DubboReference
    private ResSecurityGroupRemoteService resSecurityGroupService;

    @DubboReference
    private NetworkRemoteService networkService;

    @DubboReference
    private CloudKeyPairRemoteService cloudKeyPairService;

    @DubboReference
    private ResVpcPortRemoteService resVpcPortRemoteService;

    @Autowired
    private SysConfigService sysConfigService;

    @DubboReference
    private ResFireWallRemoteService resFireWallRemoteService;

    @DubboReference
    private ResFirewallDefaultRuleRemoteService resFirewallDefaultRuleRemoteService;

    @DubboReference
    private ResVpcRemoteService resVpcRemoteService;

    @Autowired
    private CloudTagMapper cloudTagMapper;

    @Autowired
    private SysSaasOrgMapper sysSaasOrgMapper;

    @Autowired
    private BizBillingAccountMapper bizBillingAccountMapper;

    @Autowired
    private UserService userService;

    @DubboReference
    private IamRemoteService iamRemoteService;

    @Autowired
    private BizDistributorMapper distributorMapper;

    @Override
    public int countByParams(Criteria example) {
        return this.companyMapper.countByParams(example);
    }

    @Override
    public Company selectByPrimaryKey(Long companyId) {
        Company company = this.companyMapper.selectByPrimaryKey(companyId);

        List<MgtObjExt> mgtObjExts = mgtObjExtService.selectByParentId(companyId);
        for (MgtObjExt mgtObjExt : mgtObjExts) {
            ReflectUtil.setFieldValue(company, mgtObjExt.getAttrKey(), mgtObjExt.getAttrValue());
        }
        return company;
    }

    @Override
    public List<Company> selectByParams(Criteria example) {
        return this.companyMapper.selectByParams(example);
    }

    @Override
    public List<Company> findOrgWithParentOrgName(Criteria example) {
        return this.companyMapper.findOrgWithParentOrgName(example);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteByPrimaryKey(Long orgSid) {
        // 查询企业下的部门和项目
        List<Long> orgIds = orgService.findChildOrgIds(orgSid, null);

        if (CollectionUtil.isNotEmpty(orgIds)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_884688910));
        }

        // 分销商无下级组织，可以删除。
        Org org = orgService.selectByPrimaryKey(orgSid);
        if (OrgType.DISTRIBUTOR.equals(org.getOrgType())) {

            distributorMapper.deleteByPrimaryKey(orgSid);

            Criteria criteria = new Criteria();
            criteria.put("userType", UserType.DISTRIBUTOR_USER);
            criteria.put("orgSid", orgSid);
            List<User> users = userService.selectByParams(criteria);
            users.forEach(user -> {
                iamRemoteService.deleteUser(user.getUserSid());
            });
            users.forEach(user -> {
                userService.deleteByPrimaryKey(user.getUserSid());
                          });
        }

        orgIds.add(orgSid);

        // 删除企业及企业下的部门和项目
        Criteria criteria = new Criteria("orgSidIn", orgIds);
        this.companyMapper.deleteByParams(criteria);

        // 删除企业 - 角色关联表
        roleOrgMapper.deleteByParams(criteria);

        // 删除角色
        roleMapper.deleteByParams(criteria);

        // 删除企业 - 用户 - 角色的关联表
        criteria.clear();
        criteria.put("orgSids", orgIds);
        userRoleMapper.deleteByParams(criteria);

        // 删除企业 - 用户的关联表
        userOrgMapper.deleteByParams(criteria);

        // 删除管理对象表
        criteria.clear();
        criteria.put("parentId", orgSid);
        this.mgtObjService.deleteByParams(criteria);

        //删除hostname默认规则
        List<Long> mgtObjSids = mgtObjService.selectByParams(new Criteria("parentId", orgSid))
                                             .stream()
                                             .map(MgtObj::getMgtObjSid)
                                             .collect(Collectors.toList());
        for (Long mgtObjSid : mgtObjSids) {
            mgtObjExtService.deleteByParams(new Criteria("mgtObjSid", mgtObjSid));
        }

        // 删除云环境
        List<CloudEnv> envs = cloudEnvService.selectByParams(null);
        envs.stream().filter(env -> orgSid.equals(env.getOrgSid()))
            .forEach(env -> cloudEnvService.removeCloudEnv(env.getId()));

        // 删除组织下的密钥
        criteria.clear();
        criteria.put("orgSid", orgSid);
        openApiKeyMapper.deleteByParams(criteria);

        // 删除组织下标签
        cloudTagMapper.deleteByParams(new Criteria("orgSid", orgSid));

        //删除服务链防火墙默认规则
        ResFirewallDefaultRuleParams resFirewallDefaultRuleParams = new ResFirewallDefaultRuleParams();
        resFirewallDefaultRuleParams.setOrgSid(orgSid);
        resFirewallDefaultRuleRemoteService.deleteByParams(resFirewallDefaultRuleParams);

        // 删除组织账户
        bizBillingAccountMapper.deleteByParams(criteria);
        return 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByPrimaryKeySelective(Company record) {
        if (record.getContactPosition() == null) {
            record.setContactPosition("");
        }
        if (record.getFax() == null) {
            record.setFax("");
        }
        if (record.getDescription() == null) {
            record.setDescription("");
        }

        Company orgCompany = this.companyMapper.selectByPrimaryKey(record.getOrgSid());
        if (Objects.isNull(orgCompany)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_892148282));
        }

        if (!orgCompany.getCompanyName().equals(record.getCompanyName())) {
            orgService.uniqueNameCheck(record.getCompanyName());
        }

        record.setTreePath(getTreePath(record.getParentId()));
        WebUserUtil.prepareUpdateParams(record);
        int count = this.companyMapper.updateByPrimaryKeySelective(record);
        if (record.getOrgSid().compareTo(1L) == 0) {
            return count;
        }
        //更新账户名称
        Criteria criteria = new Criteria("orgSid", orgCompany.getOrgSid());
        BizBillingAccount account = new BizBillingAccount();
        account.setAccountName(record.getOrgName());
        account.setSkipCCSPHandle(true);
        bizBillingAccountMapper.updateByParamsSelective(account, criteria.getCondition());

        // 如果parentId为空 表示修改成一个父企业 也需要更新
        if (Objects.isNull(record.getParentId())) {
            this.updateParentId(record.getParentId(), record.getOrgSid());
        }

        return count;
    }

    @Override
    public int insertSelective(Company record) {
        return this.companyMapper.insertSelective(record);
    }

    /**
     * 更新企业父id
     */
    @Override
    public int updateParentId(Long parentId, Long companyId) {
        Company record = new Company();
        record.setOrgSid(companyId);
        record.setParentId(parentId);
        return this.companyMapper.updateParentId(record);
    }

    @Override
    public Map<String, Object> queryInstanceNameRules() {
        Map<String, Object> result = Maps.newHashMap();
        // 查询简称 企业，项目，组织
        Map<String, String> enName = Maps.newHashMap();

        // 固定前缀分割后的长度为4
        Integer fixedPrefixLength = 4;

        // 查询当前组织所在的父组织（项目部门查询企业）
        if (Objects.isNull(AuthUtil.getCurrentOrgSid())) {
            return new HashMap<>();
        }
        Org currentOrg = orgService.selectByPrimaryKey(AuthUtil.getCurrentOrgSid());
        enName.put(currentOrg.getOrgType(), currentOrg.getOrgCode());

        Org company = currentOrg;
        if (!Strings.isNullOrEmpty(currentOrg.getTreePath()) && !Strings.isNullOrEmpty(
                currentOrg.getTreePath().substring(1))) {
            String[] parentIds = StringUtils.split(currentOrg.getTreePath(), "/");
            List<Org> orgs = orgService.selectByParamsWithoutAuth(new Criteria("orgIds", parentIds));
            Map<Long, Org> orgMap = orgs.stream().collect(Collectors.toMap(Org::getOrgSid, Function.identity()));
            // 查找距离当前组织最近的组织类型为company的数据
            Org nearestCompany = findNearestCompany(company, orgMap);

            Criteria criteria = new Criteria("orgIdList", parentIds);
            criteria.put("treePath", nearestCompany.getTreePath());
            List<Org> orgList = orgService.findByUserSid(criteria);
            for (Org org : orgList) {
                enName.put(org.getOrgType(), org.getOrgCode());
            }

            Optional<Org> optional = orgList.stream()
                                            .filter(org -> OrgType.COMPANY.equals(org.getOrgType()))
                                            .findFirst();

            if (optional.isPresent()) {
                company = optional.get();
            }
        }

        // region 查询配置项
        List<MgtObj> mgtObjs = mgtObjService.selectBaseByParams(new Criteria("parentId", company.getOrgSid()));

        // 查询key
        List<Code> codes = codeService.selectByParams(
                new Criteria("codeCategory", CodeCategoryConstants.INSTANCE_NAME_PREFIX));

        if (!CollectionUtils.isEmpty(mgtObjs) && !CollectionUtils.isEmpty(codes)) {
            // 根据管理对象查询配置项
            Criteria criteria = new Criteria();
            criteria.put("mgtObjSid", mgtObjs.get(0).getMgtObjSid());
            criteria.put("attrKeyContains", codes.get(0).getCodeValue());
            List<MgtObjExt> mgtObjExts = mgtObjExtService.selectByParams(criteria);

            mgtObjExts.stream()
                      .filter(mgtObjExt -> !Strings.isNullOrEmpty(mgtObjExt.getAttrKey()))
                      .forEach(mgtObjExt -> {
                          String[] attrKeySplits = mgtObjExt.getAttrKey().split("\\*");
                          // 设置attr_key
                          mgtObjExt.setAttrKey(attrKeySplits[0]);
                          // 从key上取出排序号
                          mgtObjExt.setSort(Integer.parseInt(attrKeySplits[1]));
                          // 从key上取出分隔符
                          if (!Strings.isNullOrEmpty(attrKeySplits[2])) {
                              mgtObjExt.setSplit(attrKeySplits[2]);
                          }
                          if (attrKeySplits.length == fixedPrefixLength) {
                              // 如果是固定前缀，从key上取出类型
                              mgtObjExt.setType(attrKeySplits[3]);
                          } else {
                              // 将多选项分割为列表
                              mgtObjExt.setDynamicValue(Arrays.asList(mgtObjExt.getAttrValue().split(",")));
                          }
                      });

            // 根据sort排序
            mgtObjExts.sort(Comparator.comparingInt(MgtObjExt::getSort));

            result.put("configInfo", mgtObjExts);
        }
        //endregion

        result.put("enName", enName);
        return result;
    }

    private Org findNearestCompany(Org currentOrg, Map<Long, Org> orgMap) {
        if (Objects.isNull(currentOrg) || CollectionUtils.isEmpty(orgMap)) {
            return currentOrg;
        }

        if (OrgType.COMPANY.equals(currentOrg.getOrgType())) {
            return currentOrg;
        }

        if (Objects.isNull(currentOrg.getParentId())) {
            return currentOrg;
        }

        if (!orgMap.containsKey(currentOrg.getParentId())) {
            return currentOrg;
        }

        return findNearestCompany(orgMap.get(currentOrg.getParentId()), orgMap);
    }

    @Override
    public Map<String, Object> queryHostNameRules() {
        Map<String, Object> result = new HashMap<>(5);
        // 查询简称 企业，项目，组织
        Map<String, String> enName = new HashMap<>(3);

        // 固定前缀分割后的长度为4
        Integer fixedPrefixLength = 6;

        // 查询当前组织所在的父组织（项目部门查询企业）
        if (Objects.isNull(AuthUtil.getCurrentOrgSid())) {
            return new HashMap<>();
        }
        Org currentOrg = orgService.selectByPrimaryKey(AuthUtil.getCurrentOrgSid());
        enName.put(currentOrg.getOrgType(), currentOrg.getOrgCode());

        Org company = currentOrg;

        if (!Strings.isNullOrEmpty(currentOrg.getTreePath()) && !Strings.isNullOrEmpty(
                currentOrg.getTreePath().substring(1))) {
            String[] parentIds = StringUtils.split(currentOrg.getTreePath(), "/");
            List<Org> orgs = orgService.selectByParamsWithoutAuth(new Criteria("orgIds", parentIds));
            Map<Long, Org> orgMap = orgs.stream().collect(Collectors.toMap(Org::getOrgSid, Function.identity()));
            // 查找距离当前组织最近的组织类型为company的数据
            Org nearestCompany = findNearestCompany(company, orgMap);

            Criteria criteria = new Criteria("orgIdList", parentIds);
            criteria.put("treePath", nearestCompany.getTreePath());
            List<Org> orgList = orgService.findByUserSid(criteria);
            for (Org org : orgList) {
                enName.put(org.getOrgType(), org.getOrgCode());
            }
            //找到组织关系中的company
            Optional<Org> optional = orgList.stream()
                                            .filter(org -> OrgType.COMPANY.equals(org.getOrgType()))
                                            .findFirst();
            if (optional.isPresent()) {
                company = optional.get();
            }
        }
        //region 查询配置项
        List<MgtObj> mgtObjs = mgtObjService.selectBaseByParams(new Criteria("parentId", company.getOrgSid()));

        // 查询key
        List<Code> codes = codeService.selectByParams(
                new Criteria("codeCategory", CodeCategoryConstants.HOST_NAME_RULE));

        if (!CollectionUtils.isEmpty(mgtObjs) && !CollectionUtils.isEmpty(codes)) {
            // 根据管理对象查询配置项
            Criteria criteria = new Criteria();
            criteria.put("mgtObjSid", mgtObjs.get(0).getMgtObjSid());
            criteria.put("attrKeyContains", codes.get(0).getCodeValue());
            List<MgtObjExt> mgtObjExts = mgtObjExtService.selectByParams(criteria);

            // 根据sort排序
            mgtObjExts.sort(Comparator.comparingInt(MgtObjExt::getSort));

            result.put("configInfo", mgtObjExts);
        }
        //endregion

        result.put("enName", enName);
        return result;
    }

    @Override
    public boolean createInstanceNameRules(List<MgtObjExt> mgtObjExts) {
        // 查询key
        List<Code> codes = codeService.selectByParams(
                new Criteria("codeCategory", CodeCategoryConstants.INSTANCE_NAME_PREFIX));

        List<MgtObj> mgtObjs = mgtObjService.selectBaseByParams(new Criteria("parentId", AuthUtil.getCurrentOrgSid()));
        if (!CollectionUtils.isEmpty(codes) && !CollectionUtils.isEmpty(mgtObjs)) {
            Criteria criteria = new Criteria();
            criteria.put("mgtObjSid", mgtObjs.get(0).getMgtObjSid());
            criteria.put("attrKeyContains", codes.get(0).getCodeValue());
            // 删除规则
            mgtObjExtService.deleteByParams(criteria);

            if (!CollectionUtils.isEmpty(mgtObjExts)) {
                // 保存规则
                Integer index = 0;
                for (MgtObjExt mgtObjExt : mgtObjExts) {
                    index++;
                    List<String> attrKey = new ArrayList<>(5);
                    attrKey.add(codes.get(0).getCodeValue());
                    attrKey.add(String.valueOf(index));
                    attrKey.add(mgtObjExt.getSplit());
                    attrKey.add(mgtObjExt.getType());

                    mgtObjExt.setMgtObjSid(mgtObjs.get(0).getMgtObjSid());
                    mgtObjExt.setAttrKey(Joiner.on("*").skipNulls().join(attrKey));

                    mgtObjExtService.insertSelective(mgtObjExt);
                }
            }

            return true;
        }

        return false;
    }

    @Override
    public boolean createHostNameRules(List<MgtObjExt> mgtObjExts) {
        List<Code> codes = codeService.selectByParams(
                new Criteria("codeCategory", CodeCategoryConstants.HOST_NAME_RULE));
        List<MgtObj> mgtObjs = mgtObjService.selectBaseByParams(new Criteria("parentId", AuthUtil.getCurrentOrgSid()));
        //默认规则
        String defaultRule = "default";
        //自定义规则
        String otherRule = "other";
        if (!CollectionUtils.isEmpty(codes) && !CollectionUtils.isEmpty(mgtObjs)) {
            //默认规则
            if (!CollectionUtils.isEmpty(mgtObjExts) && defaultRule.equalsIgnoreCase(mgtObjExts.get(0).getType())) {
                //默认规则的长度校验
                if (!StringUtil.isNullOrEmpty(mgtObjExts) && (1 > mgtObjExts.get(0).getAttrValue().length()
                        || mgtObjExts.get(0).getAttrValue().length() > 10)) {
                    return false;
                }
                Criteria criteria = new Criteria();
                criteria.put("mgtObjSid", mgtObjs.get(0).getMgtObjSid());
                criteria.put("attrKeyContains", codes.get(0).getCodeValue());
                criteria.put("attrKeyContains", defaultRule);
                // 删除规则
                mgtObjExtService.deleteByParams(criteria);
                //修改自定义规则失效（false）
                criteria.clear();
                criteria.put("mgtObjSid", mgtObjs.get(0).getMgtObjSid());
                criteria.put("attrKeyContains", codes.get(0).getCodeValue());
                criteria.put("attrKeyContains", otherRule);
                List<MgtObjExt> otherMgtObjExts = mgtObjExtService.selectByParams(criteria);
                otherMgtObjExts.forEach(mgtObjExt -> {
                    String attrKey = mgtObjExt.getAttrKey();
                    String substring = attrKey.substring(0, attrKey.lastIndexOf('*') + 1);
                    mgtObjExt.setAttrKey(substring + "false");
                    mgtObjExtService.updateByPrimaryKey(mgtObjExt);
                });
                //自定义规则
            } else {
                Criteria criteria = new Criteria();
                criteria.put("mgtObjSid", mgtObjs.get(0).getMgtObjSid());
                criteria.put("attrKeyContains", codes.get(0).getCodeValue());
                criteria.put("attrKeyContains", otherRule);
                // 删除规则
                mgtObjExtService.deleteByParams(criteria);
                //修改默认规则失效（false）
                criteria.clear();
                criteria.put("mgtObjSid", mgtObjs.get(0).getMgtObjSid());
                criteria.put("attrKeyContains", codes.get(0).getCodeValue());
                criteria.put("attrKeyContains", defaultRule);
                List<MgtObjExt> otherMgtObjExts = mgtObjExtService.selectByParams(criteria);
                otherMgtObjExts.forEach(mgtObjExt -> {
                    String attrKey = mgtObjExt.getAttrKey();
                    mgtObjExt.setAttrKey(attrKey.substring(0, attrKey.lastIndexOf('*') + 1) + "false");
                    mgtObjExtService.updateByPrimaryKey(mgtObjExt);
                });
            }
            //保存规则
            if (!CollectionUtils.isEmpty(mgtObjExts)) {
                Integer index = 0;
                for (MgtObjExt mgtObjExt : mgtObjExts) {
                    index++;
                    List<String> attrKey = new ArrayList<>(8);
                    attrKey.add(codes.get(0).getCodeValue());
                    attrKey.add(String.valueOf(index));
                    attrKey.add(mgtObjExt.getSplit());
                    attrKey.add(mgtObjExt.getType());
                    attrKey.add(mgtObjExt.getContent());
                    attrKey.add("true");

                    mgtObjExt.setMgtObjSid(mgtObjs.get(0).getMgtObjSid());
                    mgtObjExt.setAttrKey(Joiner.on("*").skipNulls().join(attrKey));

                    mgtObjExtService.insertSelective(mgtObjExt);
                }
            }
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createCompany(Company company) {
        orgService.uniqueNameCheck(company.getOrgName());

        WebUserUtil.prepareInsertParams(company);
        company.setOrgIcon("fa fa-building-o");
        company.setOrgType(OrgType.COMPANY);
        company.setSmsMax(Integer.parseInt(PropertiesUtil.getProperty("company-sms-max-count")));

        company.setTreePath(getTreePath(company.getParentId()));
        //配额控制:用量控制
        SysConfig sysConfig = sysConfigService.findByConfigTypeAndKey("quota_config", "quota.ctrl");
        company.setQuotaCtrl(sysConfig.getConfigValue());
        company.setQuotaMode("use");
        company.setCertificationStatus(CertificationStatus.NOAUTH);
        this.companyMapper.insertSelective(company);

        //默认项目
        Criteria criteria = new Criteria();
        criteria.put("parentId", company.getOrgSid());
        criteria.put("orgCode", "default");
        List<Org> defaultProjects = orgService.selectByParams(criteria);
        if (CollectionUtil.isEmpty(defaultProjects)) {
            Org defaultProject = new Org();
            defaultProject.setOrgType(OrgType.PROJECT);
            defaultProject.setTreePath("/" + company.getOrgSid() + "/");
            defaultProject.setOrgName("默认项目");
            defaultProject.setOrgCode("default");
            defaultProject.setStatus("1");
            defaultProject.setParentId(company.getOrgSid());
            WebUserUtil.prepareInsertParams(defaultProject, "admin");
            orgService.insertSelective(defaultProject);
        }
        // 新增账户
        AuthUser createdBy = RequestContextUtil.getAuthUserInfo();
        BizBillingAccount account = new BizBillingAccount();

        account.setId(new IdWorker().nextId());
        account.setAdminSid(createdBy.getUserSid());
        account.setAccountName(company.getOrgName());
        account.setOrgSid(company.getOrgSid());
        account.setBalance(BigDecimal.ZERO);
        account.setCreatedBy(createdBy.getAccount());
        account.setCreatedDt(new Date());
        account.setUpdatedBy(createdBy.getAccount());
        account.setUpdatedDt(new Date());
        account.setBalanceCash(BigDecimal.ZERO);
        account.setCreditLine(BigDecimal.ZERO);
        bizBillingAccountMapper.insertSelective(account);



        // 默认给新企业关联系统内置的组织管理员角色
        List<Long> roleIds = Arrays.asList(SysRoleEnum.ORG_ADMIN.getRoleSid());
        List<RoleOrg> roleOrgs = roleIds.stream().map(roleId -> {
            RoleOrg roleOrg = new RoleOrg();
            roleOrg.setOrgSid(company.getOrgSid());
            roleOrg.setRoleSid(roleId);
            return roleOrg;
        }).collect(Collectors.toList());

        roleOrgMapper.insertMulti(roleOrgs);

        /// 初始化平台标签
        Org org = BeanUtil.toBean(company, Org.class);
        orgService.initPlatformClougTags(org, RequestContextUtil.getAuthUserInfo());

        // 插入管理对象表
        MgtObj mgtObj = new MgtObj();
        mgtObj.setType(MgtObjType.COMPANY);
        mgtObj.setLevel(MgtObjType.ENTERPRISE);
        mgtObj.setParentId(company.getOrgSid());
        mgtObj.setStatus("1");
        WebUserUtil.prepareInsertParams(mgtObj);
        int mgtObjcount = this.mgtObjMapper.insertSelective(mgtObj);
        //设置企业hostName默认规则
        if (mgtObjcount > 0) {
            MgtObjExt mgtObjExt = new MgtObjExt();
            mgtObjExt.setMgtObjSid(mgtObj.getMgtObjSid());
            mgtObjExt.setAttrKey("hostNameRule*1*-*default**true");
            mgtObjExtService.insertSelective(mgtObjExt);
        }
        // 创建组织时，初始化组织配额
        List<SysQuota> sysQuotas = sysQuotaMapper.selectByParams(new Criteria());
        SysMgtObjQuota sysMgtObjQuota = new SysMgtObjQuota();
        WebUserUtil.prepareInsertParams(sysMgtObjQuota);
        sysMgtObjQuota.setQuotaValue(JsonUtil.toJson(sysQuotas));
        sysMgtObjQuota.setQuotaObj(2L);
        sysMgtObjQuota.setQuotaObjSid(company.getOrgSid());
        sysMgtObjQuotaMapper.insertSelective(sysMgtObjQuota);
        return mgtObjcount > 0;
    }

    /**
     * 加入SaaS限制
     * @param orgSid
     */
    private void joinSaas(Long orgSid) {
        if (SaasUtil.isSaasEnable()) {
            Criteria criteria = new Criteria();
            criteria.put("orgSid", orgSid);
            int count = sysSaasOrgMapper.countByParams(criteria);
            if (count <= 0) {
                SysSaasOrg sysSaasOrg = new SysSaasOrg();
                sysSaasOrg.setOrgSid(orgSid);
                DateTime now = DateUtil.date();
                sysSaasOrg.setRegTime(now);
                Integer days = Convert.toInt(PropertiesUtil.getProperty(SaasUtil.EXPERIENCE_DAYS), 0);
                sysSaasOrg.setLimitDate(DateUtil.offsetDay(now, days));
               WebUserUtil.prepareInsertAdminParams(sysSaasOrg);
                sysSaasOrgMapper.insertSelective(sysSaasOrg);
            }
        }
    }

    @Override
    public boolean checkCompanyNameExists(Long companyId, String companyName) {
        Company orgCompany = this.companyMapper.selectByPrimaryKey(companyId);
        if (Objects.isNull(orgCompany)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_892148282));
        }

        if (!orgCompany.getCompanyName().equals(companyName)) {
            Criteria criteria = new Criteria();
            criteria.put("orgType", OrgType.COMPANY);
            criteria.put("orgName", companyName);

            int countNames = companyMapper.countByParams(criteria);
            return countNames > 0;
        }
        return false;
    }

    private String getTreePath(Long parentId) {
        String treePath = "/";

        if (Objects.nonNull(parentId)) {
            Company company = companyMapper.selectByPrimaryKey(parentId);
            treePath = company.getTreePath() + company.getOrgSid() + "/";
        }

        return treePath;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCompanyInfo(Company company) {
        // 查询的定义表
        Criteria criteria = new Criteria();
        String mgtBizModeCompany = "company";
        criteria.put("mgtBizMode", mgtBizModeCompany);
        List<MgtObjDef> mgtObjDefs = mgtObjDefMapper.selectByParams(criteria);
        List<String> attrKeys = mgtObjDefs.stream().map(MgtObjDef::getAttrKey).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(attrKeys)) {
            logger.error("企业信息拓展项未定义，请检查。");
            return false;
        }

        // 查询管理对象
        criteria.clear();
        criteria.put("parentId", company.getCompanyId());
        criteria.put("type", OrgType.COMPANY);
        List<MgtObj> mgtObjs = mgtObjService.selectByParams(criteria);


        Company oldInfo = this.selectByPrimaryKey(company.getCompanyId());
        boolean success = this.companyMapper.updateByPrimaryKeySelective(company) > 0;

        return success;
    }

    @Override
    public void checkCompanyQuota(Long orgId, JsonNode hostSpec) {

        this.checkCompanyQuota(orgId, Collections.singletonList(hostSpec), null);

    }

    private void checkCompanyQuota(Long orgId, List<JsonNode> jsonNodes, Long excludeOrderId) {
        Map<String, Integer> specQuotaMap = new HashMap<>();

        int hostNum = 0;
        int cpuNum = 0;
        int memNum = 0;
        int storeSum = 0;
        int floatingIpNum = 0;
        int volumeNum = 0;

        if (!CollectionUtils.isEmpty(jsonNodes)) {
            for (JsonNode hostSpec : jsonNodes) {
                JsonNode instance = hostSpec.findValue("instance");
                String envId = hostSpec.findValue("envId").toString();
                // 通过vmType取得cpu内存值
                String instanceType = instance.findValue("instanceType").textValue();
                QueryResVmTypeByParamsRequest queryResVmTypeByParamsRequest = new QueryResVmTypeByParamsRequest();
                queryResVmTypeByParamsRequest.setUuid(instanceType);
                ResVmType resVmType = this.resVmTypeRemoteService.selectByParams(queryResVmTypeByParamsRequest).get(0);
                hostNum += hostSpec.findValue("hostCount").asInt();
                cpuNum += resVmType.getCpu();
                memNum += resVmType.getRam().intValue() / 1024;
                storeSum += sumDiskSize(hostSpec);
                volumeNum += (hostSpec.get("dataDisk") == null ? 0 : hostSpec.get("dataDisk").size()) + 1; //系统盘+1

                JsonNode network = hostSpec.findValue("networks");

                //当选择自动创建时，检查FloatingIP
                if (network != null && network.findValue("hasPublicNet") != null && network.findValue("hasPublicNet")
                                                                                           .booleanValue()) {
                    floatingIpNum += 1;
                }

            }
        }
        specQuotaMap.put(EnvQuotaKey.VM_COUNT, hostNum);
        specQuotaMap.put(EnvQuotaKey.CPU_COUNT, cpuNum * hostNum);
        specQuotaMap.put(EnvQuotaKey.MEMORY_MAX, memNum * hostNum);
        specQuotaMap.put(EnvQuotaKey.STORAGE_MAX, storeSum * hostNum);
        specQuotaMap.put(EnvQuotaKey.FLOATING_IP, floatingIpNum * hostNum);
        specQuotaMap.put(EnvQuotaKey.VOLUME_COUNT, volumeNum * hostNum);

        this.checkCompanyQuota(specQuotaMap, orgId);
    }

    @Override
    public void checkCompanyQuotaByType(Long orgId, int count, String quotaType) {
        Org org = orgService.selectByPrimaryKey(orgId);
        if (org == null) {
            throw new BizException(RestConst.BizError.BAD_PARAM, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_285980848));
        }
        if (QuotaMode.NOLIMIT.equals(org.getQuotaCtrl())) {
            if (!StringUtil.isNullOrEmpty(org.getParentId())) {
                checkCompanyQuotaByType(org.getParentId(), count, quotaType);
            }
            return;
        }
        Criteria criteria = new Criteria();
        criteria.put("quotaObjSid", orgId);
        List<SysMgtObjQuota> objQuotas = sysMgtObjQuotaMapper.selectByParams(criteria);
        List<SysQuota> sysQuotas = null;
        if (objQuotas != null && !objQuotas.isEmpty()) {
            try {
                sysQuotas = JsonUtil.fromJson(objQuotas.get(0).getQuotaValue(), new TypeReference<List<SysQuota>>() {
                });
            } catch (Exception e) {
                logger.error("无法正确取得配额信息 | {}", Throwables.getStackTraceAsString(e));
                throw new BizException(RestConst.BizError.BAD_PARAM, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_570158371));
            }
        }
        if (sysQuotas == null) {
            if (!StringUtil.isNullOrEmpty(org.getParentId())) {
                checkCompanyQuotaByType(org.getParentId(), count, quotaType);
            }
            return;
        }
        //过滤指定的配额类型
        List<SysQuota> filterQuotas = sysQuotas.stream()
                                               .filter(q -> q.getQuotaKey().equals(quotaType))
                                               .collect(Collectors.toList());

        // 需要的规格数量的数量
        Map<String, Integer> specQuotaMap = newInitQuotaMap();
        specQuotaMap.put(quotaType, count);
        Map<String, Integer> usedQuota = getCompanyUsedQuotaByType(orgId, quotaType);

        Map<String, Integer> allocedQuota = newInitQuotaMap();
        // 组织下子组织占用的配额
        if (!org.getOrgType().equals(OrgType.PROJECT)) {
            allocedQuota = getCompanyAllocedQuotasByType(orgId, quotaType);
        }
        // 流程中的订单使用的配额
        Map<String, Integer> freezeQuota = projectService.getProjectFreezeQuotas(orgId, null);

        String msg = compareQuota(org, filterQuotas, specQuotaMap, usedQuota, allocedQuota, freezeQuota);
        if (!Strings.isNullOrEmpty(msg)) {
            logger.warn("{} | orgId[{}]", msg, orgId);
            throw new BizException(RestConst.BizError.INSUFFICIENT_RESOURCES, msg);
        }
    }

    /**
     * 获取组织总配额
     */
    @Override
    public Map<String, Integer> getCompanyQuotas(Long companyId) {
        Map<String, Integer> globalQuota = newInitQuotaMap();
        Criteria criteria = new Criteria();
        criteria.put("quotaObjSid", companyId);
        List<SysMgtObjQuota> objQuotas = sysMgtObjQuotaMapper.selectByParams(criteria);

        List<SysQuota> globalQuotaList = null;
        if (objQuotas != null && !objQuotas.isEmpty()) {
            try {
                globalQuotaList = JsonUtil
                        .fromJson(objQuotas.get(0).getQuotaValue(), new TypeReference<List<SysQuota>>() {
                        });
            } catch (Exception e) {
                throw new BizException(RestConst.BizError.BAD_PARAM, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_570158371));
            }
        } else {
            // 默认配额项
            globalQuotaList = sysQuotaMapper.selectByParams(new Criteria());
        }

        for (SysQuota envQuota : globalQuotaList) {
            Integer quotaVal = envQuota.getLimit() ? Integer.valueOf(envQuota.getQuotaValue()) : 0;
            if (EnvQuotaKey.VM_COUNT.equals(envQuota.getQuotaKey())) {
                globalQuota.put(EnvQuotaKey.VM_COUNT, quotaVal);
            } else if (EnvQuotaKey.CPU_COUNT.equals(envQuota.getQuotaKey())) {
                globalQuota.put(EnvQuotaKey.CPU_COUNT, quotaVal);
            } else if (EnvQuotaKey.MEMORY_MAX.equals(envQuota.getQuotaKey())) {
                globalQuota.put(EnvQuotaKey.MEMORY_MAX, quotaVal);
            } else if (EnvQuotaKey.STORAGE_MAX.equals(envQuota.getQuotaKey())) {
                globalQuota.put(EnvQuotaKey.STORAGE_MAX, quotaVal);
            } else if (EnvQuotaKey.VOLUME_COUNT.equals(envQuota.getQuotaKey())) {
                globalQuota.put(EnvQuotaKey.VOLUME_COUNT, quotaVal);
            } else if (EnvQuotaKey.FIREWALL.equals(envQuota.getQuotaKey())) {
                globalQuota.put(EnvQuotaKey.FIREWALL, quotaVal);
            } else if (EnvQuotaKey.FLOATING_IP.equals(envQuota.getQuotaKey())) {
                globalQuota.put(EnvQuotaKey.FLOATING_IP, quotaVal);
            } else if (EnvQuotaKey.KEY_PAIR.equals(envQuota.getQuotaKey())) {
                globalQuota.put(EnvQuotaKey.KEY_PAIR, quotaVal);
            } else if (EnvQuotaKey.LOAD_BALANCE.equals(envQuota.getQuotaKey())) {
                globalQuota.put(EnvQuotaKey.LOAD_BALANCE, quotaVal);
            } else if (EnvQuotaKey.NET_CARD.equals(envQuota.getQuotaKey())) {
                globalQuota.put(EnvQuotaKey.NET_CARD, quotaVal);
            } else if (EnvQuotaKey.NETWORK.equals(envQuota.getQuotaKey())) {
                globalQuota.put(EnvQuotaKey.NETWORK, quotaVal);
            } else if (EnvQuotaKey.SECURITY_GROUP.equals(envQuota.getQuotaKey())) {
                globalQuota.put(EnvQuotaKey.SECURITY_GROUP, quotaVal);
            } else if (EnvQuotaKey.SNAPSHOT_COUNT.equals(envQuota.getQuotaKey())) {
                globalQuota.put(EnvQuotaKey.SNAPSHOT_COUNT, quotaVal);
            } else if (EnvQuotaKey.SUBNET.equals(envQuota.getQuotaKey())) {
                globalQuota.put(EnvQuotaKey.SUBNET, quotaVal);
            } else if (EnvQuotaKey.ROUTER.equals(envQuota.getQuotaKey())) {
                globalQuota.put(EnvQuotaKey.ROUTER, quotaVal);
            }
        }

        return globalQuota;
    }

    @Override
    public Map<String, Integer> getCompanyUsedQuotas(Long orgId) {
        Map<String, Integer> result = newInitQuotaMap();
        Map<String, Integer> usedQuotas = getCurrentOrgUsedQuotas(orgId);
        List<Org> childOrgs = orgService.selectAllChildOrg(orgId);
        if (!CollectionUtils.isEmpty(childOrgs)) {
            for (Org org : childOrgs) {
                Map<String, Integer> childUsedQuotas = getCurrentOrgUsedQuotas(org.getOrgSid());
                usedQuotas = Stream.concat(childUsedQuotas.entrySet().stream(), usedQuotas.entrySet().stream())
                                   .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                                                             (value1, value2) -> value1 + value2));
            }
        }
        Field[] fields = EnvQuotaKey.class.getFields();
        for (Field field : fields) {
            try {
                int usedQuota = usedQuotas.get((String) field.get(EnvQuotaKey.class));
                result.put((String) field.get(EnvQuotaKey.class), usedQuota);
            } catch (IllegalAccessException e) {
                logger.error(Throwables.getStackTraceAsString(e));
            }
        }
        return result;
    }

    @Override
    public Map<String, Integer> getCompanyAllocatedQuotas(Long orgId, Long excludeOrgSid) {
        Map<String, Integer> result = newInitQuotaMap();
        List<Org> childOrgs = this.orgService.selectAllChildOrg(orgId);
        Field[] fields = EnvQuotaKey.class.getFields();
        childOrgs.stream()
                 .filter(childOrg -> Objects.isNull(excludeOrgSid) || !childOrg.getOrgSid().equals(excludeOrgSid))
                 .forEach(org -> {
                     Map<String, Integer> usedQuota = null;
                     if (QuotaMode.NOLIMIT.equals(org.getQuotaCtrl())) {
                         usedQuota = this.getCurrentOrgUsedQuotas(org.getOrgSid());
                     } else {
                         usedQuota = this.getCurrentOrgAllocQuotas(org.getOrgSid());
                     }
                     for (Field field : fields) {
                         try {
                             int usedQ = usedQuota.get((String) field.get(EnvQuotaKey.class));
                             result.put((String) field.get(EnvQuotaKey.class),
                                        usedQ + result.get((String) field.get(EnvQuotaKey.class)));
                         } catch (IllegalAccessException e) {
                             logger.error(Throwables.getStackTraceAsString(e));
                         }
                     }
                 });
        return result;
    }

    @Override
    public Map<String, Integer> getCompanyMaxQuotas(Long orgId, Long excludeOrgSid) {
        Map<String, Integer> max = newMaxQuotaMap();

        Org org = orgService.selectByPrimaryKey(orgId);
        if (org == null) {
            return max;
        }
        if (Strings.isNullOrEmpty(org.getQuotaCtrl())
                || QuotaMode.NOLIMIT.equalsIgnoreCase(org.getQuotaCtrl())) {
            return getCompanyMaxQuotas(org.getParentId(), excludeOrgSid);
        }
        //查找上级组织配额
        Criteria criteria = new Criteria();
        criteria.put("quotaObjSid", orgId);
        List<SysMgtObjQuota> objQuotas = sysMgtObjQuotaMapper.selectByParams(criteria);
        String quotaString = "";
        if (objQuotas != null && !objQuotas.isEmpty()) {
            quotaString = objQuotas.get(0).getQuotaValue();
        }
        // 默认配额
        if (StringUtils.isBlank(quotaString)) {
            List<SysQuota> quotas = sysQuotaMapper.selectByParams(new Criteria());
            quotaString = JsonUtil.toJson(quotas);
        }
        List<SysQuota> sysQuotas = null;
        if (!StringUtils.isBlank(quotaString)) {
            try {
                //取出的总配额
                sysQuotas = JsonUtil.fromJson(quotaString, new TypeReference<List<SysQuota>>() {
                });
            } catch (Exception e) {
                logger.error("无法正确取得配额信息 | {}", Throwables.getStackTraceAsString(e));
                throw new BizException(RestConst.BizError.BAD_PARAM, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_570158371));
            }
        }
        if (sysQuotas == null) {
            return max;
        }

        // 获取上级组织下已占用配额=当前已使用 + 子组织配额
        Map<String, Integer> usedQuota = getCurrentOrgUsedQuotas(orgId);
        Map<String, Integer> allocatedQuotas = getCompanyAllocatedQuotas(orgId, excludeOrgSid);

        for (SysQuota sysQuota : sysQuotas) {
            int used = usedQuota.get(sysQuota.getQuotaKey()) == null ? 0 : usedQuota.get(sysQuota.getQuotaKey());
            int allocated = allocatedQuotas.get(sysQuota.getQuotaKey()) == null ? 0
                    : allocatedQuotas.get(sysQuota.getQuotaKey());
            // 不限制
            if (!sysQuota.getLimit()) {
                //不限制的时候，再去获取上级的配额
                Map<String, Integer> parentQuotaMap = getCompanyMaxQuotas(org.getParentId(), excludeOrgSid);
                Integer parentQuota = parentQuotaMap.get(sysQuota.getQuotaKey());
                max.put(sysQuota.getQuotaKey(), parentQuota - used - allocated);
                continue;
            }
            Integer quotaVal = Integer.valueOf(sysQuota.getQuotaValue());
            max.put(sysQuota.getQuotaKey(), quotaVal - used - allocated);
        }
        return max;
    }

    @Override
    public void checkCompanyQuota(Map<String, Integer> specQuotaMap, Long orgId) {
        Map<String, Integer> usedQuota = getCurrentOrgUsedQuotas(orgId);
        //分配给子组织配额
        Map<String, Integer> allocatedQuota = getCompanyAllocatedQuotas(orgId, null);
        //流程中的订单
        Map<String, Integer> freezeQuota = projectService.getProjectFreezeQuotas(orgId, null);

        Criteria criteria = new Criteria();
        criteria.put("quotaObjSid", orgId);
        List<SysMgtObjQuota> objQuotas = sysMgtObjQuotaMapper.selectByParams(criteria);
        List<SysQuota> sysQuotas = null;
        if (objQuotas != null && !objQuotas.isEmpty()) {
            try {
                sysQuotas = JsonUtil.fromJson(objQuotas.get(0).getQuotaValue(), new TypeReference<List<SysQuota>>() {
                });
            } catch (Exception e) {
                logger.error("无法正确取得配额信息 | {}", Throwables.getStackTraceAsString(e));
                throw new BizException(RestConst.BizError.BAD_PARAM, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_570158371));
            }
        }
        if (sysQuotas == null) {
            return;
        }
        List<SysQuota> filterSysQuotas = sysQuotas.stream()
                                                  .filter(quota -> specQuotaMap.keySet().contains(quota.getQuotaKey()))
                                                  .collect(Collectors.toList());
        Org org = this.orgService.selectByPrimaryKey(orgId);
        String msg = compareQuota(org, filterSysQuotas, specQuotaMap, usedQuota, allocatedQuota, freezeQuota);
        if (!Strings.isNullOrEmpty(msg)) {
            logger.warn("{} | orgId[{}]", msg, orgId);
            throw new BizException(RestConst.BizError.INSUFFICIENT_RESOURCES, msg);
        }
    }

    @Override
    public Map<String, Integer> getCurrentOrgUsedQuotas(Long orgId) {
        Map<String, Integer> usedQuota = newInitQuotaMap();
        //实例、cpu、内存
        usedQuota.putAll(getCompanyUsedQuotaByType(orgId, EnvQuotaKey.VM_COUNT));
        //存储、硬盘
        usedQuota.putAll(getCompanyUsedQuotaByType(orgId, EnvQuotaKey.STORAGE_MAX));
        usedQuota.putAll(getCompanyUsedQuotaByType(orgId, EnvQuotaKey.SUBNET));
        usedQuota.putAll(getCompanyUsedQuotaByType(orgId, EnvQuotaKey.SECURITY_GROUP));
        usedQuota.putAll(getCompanyUsedQuotaByType(orgId, EnvQuotaKey.KEY_PAIR));
        usedQuota.putAll(getCompanyUsedQuotaByType(orgId, EnvQuotaKey.SNAPSHOT_COUNT));
        usedQuota.putAll(getCompanyUsedQuotaByType(orgId, EnvQuotaKey.FIREWALL));
        usedQuota.putAll(getCompanyUsedQuotaByType(orgId, EnvQuotaKey.LOAD_BALANCE));
        usedQuota.putAll(getCompanyUsedQuotaByType(orgId, EnvQuotaKey.ROUTER));
        usedQuota.putAll(getCompanyUsedQuotaByType(orgId, EnvQuotaKey.VPC));
        usedQuota.putAll(getCompanyUsedQuotaByType(orgId, EnvQuotaKey.NET_CARD));
        usedQuota.putAll(getCompanyUsedQuotaByType(orgId, EnvQuotaKey.FLOATING_IP));

        return usedQuota;
    }

    @Override
    public Map<String, Integer> getCompanyUsedQuotaByType(Long orgId, String quotaType) {
        Map<String, Integer> usedQuota = new HashMap<>();
        Criteria param = new Criteria();
        param.put("orgSid", orgId);
        switch (quotaType) {
            case EnvQuotaKey.VM_COUNT:
            case EnvQuotaKey.CPU_COUNT:
            case EnvQuotaKey.MEMORY_MAX:
                usedQuota.put(EnvQuotaKey.VM_COUNT, 0);
                usedQuota.put(EnvQuotaKey.CPU_COUNT, 0);
                usedQuota.put(EnvQuotaKey.MEMORY_MAX, 0);
                ResVmByCriteria resVmByCriteria = new ResVmByCriteria();
                resVmByCriteria.setProjectId(orgId);
                List<ResVm> resVms = resVmService.findAllocVm(resVmByCriteria);
                // 过滤导入的主机 (CPU或内存为空)
                resVms = resVms.stream()
                               .filter(resVm -> !Objects.isNull(resVm.getCpu()) && !Objects.isNull(resVm.getMemory()))
                               .collect(Collectors.toList());
                // 取已关联资源最大配额项
                if (!CollectionUtils.isEmpty(resVms)) {
                    Integer cpuCount = resVms.stream().mapToInt(ResVm::getCpu).sum();
                    Integer memoryMax = resVms.stream().mapToInt(ResVm::getMemory).sum();
                    usedQuota.put(EnvQuotaKey.VM_COUNT,
                                  usedQuota.get(EnvQuotaKey.VM_COUNT) + resVms.size());
                    usedQuota.put(EnvQuotaKey.CPU_COUNT,
                                  usedQuota.get(EnvQuotaKey.CPU_COUNT) + cpuCount);
                    usedQuota.put(EnvQuotaKey.MEMORY_MAX,
                                  usedQuota.get(EnvQuotaKey.MEMORY_MAX) + memoryMax / 1024
                    );
                }
                break;
            case EnvQuotaKey.STORAGE_MAX:
            case EnvQuotaKey.VOLUME_COUNT:
                usedQuota.put(EnvQuotaKey.STORAGE_MAX, 0);
                usedQuota.put(EnvQuotaKey.VOLUME_COUNT, 0);
                QueryAllocVdRequest queryAllocVdRequest = new QueryAllocVdRequest();
                queryAllocVdRequest.setProjectId(orgId);
                List<ResVd> vdList = resVdService.findAllocResVd(queryAllocVdRequest);
                if (!CollectionUtils.isEmpty(vdList)) {
                    // 存储总量
                    Long size = vdList.stream().mapToLong(ResVd::getAllocateDiskSize).sum();
                    usedQuota.put(EnvQuotaKey.STORAGE_MAX,
                                  usedQuota.get(EnvQuotaKey.STORAGE_MAX) + size.intValue());
                    // 硬盘数量
                    usedQuota.put(EnvQuotaKey.VOLUME_COUNT,
                                  usedQuota.get(EnvQuotaKey.VOLUME_COUNT) + vdList.size());
                }
                break;
            case EnvQuotaKey.FIREWALL:
                ResFireWallParams resFireWallParams = new ResFireWallParams();
                resFireWallParams.setOrgSid(orgId);
                resFireWallParams.setType("VPC");
                int vpcFirewalls = resFireWallRemoteService.countByParams(resFireWallParams);
                resFireWallParams.setType("SERVICE_CHAIN");
                int serviceChainFws = resFireWallRemoteService.countByParams(resFireWallParams);
                usedQuota.put(EnvQuotaKey.FIREWALL, vpcFirewalls + serviceChainFws);
                break;
            case EnvQuotaKey.NET_CARD:
                ResVpcPortParams resVpcPortParams = new ResVpcPortParams();
                resVpcPortParams.setVpcOrgSid(orgId);
                resVpcPortParams.setDeviceOwnerLike("compute:");
                resVpcPortParams.setHostIsNotRecycle(true);
                List<ResVpcPort> ports = resVpcPortRemoteService.selectPortByVpcParam(resVpcPortParams);
                usedQuota.put(EnvQuotaKey.NET_CARD, ports.size());
                break;
            case EnvQuotaKey.KEY_PAIR:
                CloudKeyPairParams cloudKeyPairParams = new CloudKeyPairParams();
                cloudKeyPairParams.setOrgSid(orgId);
                usedQuota.put(EnvQuotaKey.KEY_PAIR, cloudKeyPairService.countByParams(cloudKeyPairParams));
                break;
            case EnvQuotaKey.LOAD_BALANCE:
                QueryResLoadBalanceByCriteriaRequest queryResLoadBalanceByCriteriaRequest = new QueryResLoadBalanceByCriteriaRequest();
                queryResLoadBalanceByCriteriaRequest.setOrgSid(orgId);
                usedQuota.put(EnvQuotaKey.LOAD_BALANCE, resLoadBalanceService.countByParams(queryResLoadBalanceByCriteriaRequest));
                break;
            case EnvQuotaKey.ROUTER:
                QueryResRouterRequest queryResRouterRequest = new QueryResRouterRequest();
                queryResRouterRequest.setOrgSid(orgId);
                usedQuota.put(EnvQuotaKey.ROUTER, resRouterService.countByParams(queryResRouterRequest));
                break;
            case EnvQuotaKey.SECURITY_GROUP:
                ResSecurityGroupParams resSecurityGroupParams = new ResSecurityGroupParams();
                resSecurityGroupParams.setOrgSid(orgId);
                usedQuota.put(EnvQuotaKey.SECURITY_GROUP, resSecurityGroupService.countByParams(resSecurityGroupParams));
                break;
            case EnvQuotaKey.SNAPSHOT_COUNT:
                CountResSnapshotByParamsRequest countResSnapshotByParamsRequest = new CountResSnapshotByParamsRequest();
                countResSnapshotByParamsRequest.setStatusNotIn(Arrays.asList(SnapshotStatus.DELETED, SnapshotStatus.FAILURE));
                countResSnapshotByParamsRequest.setOrgSid(orgId);
                usedQuota.put(EnvQuotaKey.SNAPSHOT_COUNT, resSnapshotService.countByParams(countResSnapshotByParamsRequest));
                break;
            case EnvQuotaKey.SUBNET:
                usedQuota.put(EnvQuotaKey.SUBNET, 0);
                ResVpcByParams resVpcByParams = new ResVpcByParams();
                resVpcByParams.setOrgSid(orgId);
                resVpcByParams.setExternal("false");
                List<ResVpc> vpcs = this.resVpcRemoteService.selectVpcsOfShare(resVpcByParams);
                vpcs = vpcs.stream()
                           .filter(vpc -> !"quotaVpcName".equalsIgnoreCase(vpc.getName()))
                           .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(vpcs)) {
                    NetworkParams networkParams = new NetworkParams();
                    networkParams.setNetVpcIds(vpcs.stream().map(ResVpc::getId).collect(Collectors.toList()));
                    usedQuota.put(EnvQuotaKey.SUBNET, networkService.countByParams(networkParams));
                }
                break;
            case EnvQuotaKey.VPC:
                QueryAllocVpcByDfRequest queryAllocVpcByDfRequest = new QueryAllocVpcByDfRequest();
                queryAllocVpcByDfRequest.setExternal("false");
                queryAllocVpcByDfRequest.setOrgSid(orgId);
                List<ResVpc> vpcs1 = this.resVpcRemoteService.selectAllocVpcByDf(queryAllocVpcByDfRequest);
                vpcs1 = vpcs1.stream()
                             .filter(vpc -> !"quotaVpcName".equalsIgnoreCase(vpc.getName()))
                             .collect(Collectors.toList());
                usedQuota.put(EnvQuotaKey.VPC, vpcs1.size());
                break;
            case EnvQuotaKey.FLOATING_IP:
                CountResFloatingIpByParamsRequest countResFloatingIpByParamsRequest = new CountResFloatingIpByParamsRequest();
                countResFloatingIpByParamsRequest.setStatusNotEquals(NetworkStatus.DELETED);
                countResFloatingIpByParamsRequest.setProjectId(orgId);
                usedQuota.put(EnvQuotaKey.FLOATING_IP, resFloatingIpService.countByParams(countResFloatingIpByParamsRequest));
                break;
            default:

                break;
        }
        return usedQuota;
    }

    /**
     * 分配给子组织的总配额
     *
     * @param orgId
     */
    private Map<String, Integer> getCurrentOrgAllocQuotas(Long orgId) {
        Map<String, Integer> result = newInitQuotaMap();
        Criteria criteria = new Criteria();
        criteria.put("quotaObjSid", orgId);
        List<SysMgtObjQuota> objQuotas = sysMgtObjQuotaMapper.selectByParams(criteria);
        List<SysQuota> sysQuotas = null;
        if (objQuotas != null && !objQuotas.isEmpty()) {
            try {
                sysQuotas = JsonUtil.fromJson(objQuotas.get(0).getQuotaValue(), new TypeReference<List<SysQuota>>() {
                });
            } catch (Exception e) {
                logger.error("无法正确取得配额信息 | {}", Throwables.getStackTraceAsString(e));
                throw new BizException(RestConst.BizError.BAD_PARAM, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_570158371));
            }
        }

        // 全局配额
        if (!CollectionUtils.isEmpty(sysQuotas)) {
            for (SysQuota sysQuota : sysQuotas) {
                if (!sysQuota.getLimit()) {
                    //不限制配额，查询其下的资源作占用的配额
                    Map<String, Integer> used = this.getCompanyUsedQuotaByType(orgId, sysQuota.getQuotaKey());
                    result.put(sysQuota.getQuotaKey(),
                               result.get(sysQuota.getQuotaKey()) + used.get(sysQuota.getQuotaKey()));
                    continue;
                }
                int quotaValue = sysQuota.getQuotaValue() != null ? Integer.parseInt(sysQuota.getQuotaValue()) : 0;
                switch (sysQuota.getQuotaKey()) {
                    case EnvQuotaKey.VM_COUNT:
                        result.put(EnvQuotaKey.VM_COUNT,
                                   result.get(EnvQuotaKey.VM_COUNT) + quotaValue);
                        break;
                    case EnvQuotaKey.CPU_COUNT:
                        result.put(EnvQuotaKey.CPU_COUNT,
                                   result.get(EnvQuotaKey.CPU_COUNT) + quotaValue);
                        break;
                    case EnvQuotaKey.MEMORY_MAX:
                        result.put(EnvQuotaKey.MEMORY_MAX,
                                   result.get(EnvQuotaKey.MEMORY_MAX) + quotaValue);
                        break;
                    case EnvQuotaKey.STORAGE_MAX:
                        result.put(EnvQuotaKey.STORAGE_MAX,
                                   result.get(EnvQuotaKey.STORAGE_MAX) + quotaValue);
                        break;
                    case EnvQuotaKey.FIREWALL:
                        result.put(EnvQuotaKey.FIREWALL,
                                   result.get(EnvQuotaKey.FIREWALL) + quotaValue);
                        break;
                    case EnvQuotaKey.FLOATING_IP:
                        result.put(EnvQuotaKey.FLOATING_IP,
                                   result.get(EnvQuotaKey.FLOATING_IP) + quotaValue);
                        break;
                    case EnvQuotaKey.KEY_PAIR:
                        result.put(EnvQuotaKey.KEY_PAIR,
                                   result.get(EnvQuotaKey.KEY_PAIR) + quotaValue);
                        break;
                    case EnvQuotaKey.LOAD_BALANCE:
                        result.put(EnvQuotaKey.LOAD_BALANCE,
                                   result.get(EnvQuotaKey.LOAD_BALANCE) + quotaValue);
                        break;
                    case EnvQuotaKey.NET_CARD:
                        result.put(EnvQuotaKey.NET_CARD,
                                   result.get(EnvQuotaKey.NET_CARD) + quotaValue);
                        break;
                    case EnvQuotaKey.NETWORK:
                        result.put(EnvQuotaKey.NETWORK,
                                   result.get(EnvQuotaKey.NETWORK) + quotaValue);
                        break;
                    case EnvQuotaKey.ROUTER:
                        result.put(EnvQuotaKey.ROUTER,
                                   result.get(EnvQuotaKey.ROUTER) + quotaValue);
                        break;
                    case EnvQuotaKey.SECURITY_GROUP:
                        result.put(EnvQuotaKey.SECURITY_GROUP,
                                   result.get(EnvQuotaKey.SECURITY_GROUP) + quotaValue);
                        break;
                    case EnvQuotaKey.SNAPSHOT_COUNT:
                        result.put(EnvQuotaKey.SNAPSHOT_COUNT,
                                   result.get(EnvQuotaKey.SNAPSHOT_COUNT) + quotaValue);
                        break;
                    case EnvQuotaKey.SUBNET:
                        result.put(EnvQuotaKey.SUBNET,
                                   result.get(EnvQuotaKey.SUBNET) + quotaValue);
                        break;
                    case EnvQuotaKey.VOLUME_COUNT:
                        result.put(EnvQuotaKey.VOLUME_COUNT,
                                   result.get(EnvQuotaKey.VOLUME_COUNT) + quotaValue);
                        break;
                    case EnvQuotaKey.VPC:
                        result.put(EnvQuotaKey.VPC, result.get(EnvQuotaKey.VPC) + quotaValue);
                        break;
                    default:

                        break;
                }
            }
        } else {
            //配额不控制，配额值为空，查询组织项目下开通出的资源
            return this.getCurrentOrgUsedQuotas(orgId);

        }

        return result;
    }

    /**
     * 获取组织下指定配额类型的分配配额
     *
     * @param orgId
     * @param quotaType
     */
    private Map<String, Integer> getCompanyAllocedQuotasByType(Long orgId, String quotaType) {
        Map<String, Integer> result = newInitQuotaMap();
        List<Org> childOrgs = this.orgService.selectAllChildOrg(orgId);
        if (!CollectionUtils.isEmpty(childOrgs)) {
            childOrgs.stream().forEach(org -> {
                Map<String, Integer> allocedQuota = null;
                //关闭配额控制，查询该组织下使用的资源
                if (QuotaMode.NOLIMIT.equals(org.getQuotaCtrl())) {
                    allocedQuota = this.getCompanyUsedQuotaByType(org.getOrgSid(), quotaType);
                } else {
                    allocedQuota = this.getCurrentOrgAllocQuotas(org.getOrgSid());
                }
                int alloced = allocedQuota.get(quotaType);
                result.put(quotaType, alloced + result.get(quotaType));
            });
        }
        return result;
    }

    private String compareQuota(Org org, List<SysQuota> sysQuotas, Map<String, Integer> specQuotaMap,
                                Map<String, Integer> usedQuota, Map<String, Integer> allocedQuota,
                                Map<String, Integer> freezeQuota) {
        boolean flag;
        String msg = null;
        int errorQuota = 0;
        int requiredQuota = 0;
        // 全局配额
        for (SysQuota sysQuota : sysQuotas) {
            flag = false;
            // 不限制
            if (!sysQuota.getLimit()) {
                continue;
            }
            Integer quotaVal = Integer.valueOf(sysQuota.getQuotaValue());
            if (EnvQuotaKey.VM_COUNT.equals(sysQuota.getQuotaKey())) {
                // 实例个数
                int vmCount = usedQuota.get(EnvQuotaKey.VM_COUNT)
                        + allocedQuota.get(EnvQuotaKey.VM_COUNT)
                        + freezeQuota.get(EnvQuotaKey.VM_COUNT);
                requiredQuota = specQuotaMap.get(EnvQuotaKey.VM_COUNT);
                if (vmCount + requiredQuota > quotaVal) {
                    errorQuota = vmCount;
                    flag = true;
                }
            } else if (EnvQuotaKey.CPU_COUNT.equals(sysQuota.getQuotaKey())) {
                // CPU核数
                int cpuCores = usedQuota.get(EnvQuotaKey.CPU_COUNT)
                        + allocedQuota.get(EnvQuotaKey.CPU_COUNT)
                        + freezeQuota.get(EnvQuotaKey.CPU_COUNT);
                requiredQuota = specQuotaMap.get(EnvQuotaKey.CPU_COUNT);
                if (cpuCores + requiredQuota > quotaVal) {
                    errorQuota = cpuCores;
                    flag = true;
                }
            } else if (EnvQuotaKey.MEMORY_MAX.equals(sysQuota.getQuotaKey())) {
                // 内存大小
                int memSum = usedQuota.get(EnvQuotaKey.MEMORY_MAX)
                        + allocedQuota.get(EnvQuotaKey.MEMORY_MAX)
                        + freezeQuota.get(EnvQuotaKey.MEMORY_MAX);
                requiredQuota = specQuotaMap.get(EnvQuotaKey.MEMORY_MAX);
                if (memSum + requiredQuota > quotaVal) {
                    errorQuota = memSum;
                    flag = true;
                }
            } else if (EnvQuotaKey.STORAGE_MAX.equals(sysQuota.getQuotaKey())) {
                int used = usedQuota.get(EnvQuotaKey.STORAGE_MAX)
                        + allocedQuota.get(EnvQuotaKey.STORAGE_MAX)
                        + freezeQuota.get(EnvQuotaKey.STORAGE_MAX);
                requiredQuota = specQuotaMap.get(EnvQuotaKey.STORAGE_MAX);
                // 存储大小
                if (used + requiredQuota > quotaVal) {
                    errorQuota = used;
                    flag = true;
                }
            } else if (EnvQuotaKey.SNAPSHOT_COUNT.equals(sysQuota.getQuotaKey())) {
                int used = usedQuota.get(EnvQuotaKey.SNAPSHOT_COUNT) + allocedQuota.get(
                        EnvQuotaKey.SNAPSHOT_COUNT);
                requiredQuota = specQuotaMap.get(EnvQuotaKey.SNAPSHOT_COUNT);
                if (used + requiredQuota > quotaVal) {
                    errorQuota = used;
                    flag = true;
                }
            } else if (EnvQuotaKey.VOLUME_COUNT.equals(sysQuota.getQuotaKey())) {
                int used = usedQuota.get(EnvQuotaKey.VOLUME_COUNT)
                        + allocedQuota.get(EnvQuotaKey.VOLUME_COUNT)
                        + freezeQuota.get(EnvQuotaKey.VOLUME_COUNT);
                requiredQuota = specQuotaMap.get(EnvQuotaKey.VOLUME_COUNT);
                // 硬盘数量
                if (used + requiredQuota > quotaVal) {
                    errorQuota = used;
                    flag = true;
                }
            } else if (EnvQuotaKey.SECURITY_GROUP.equals(sysQuota.getQuotaKey())) {
                int used = usedQuota.get(EnvQuotaKey.SECURITY_GROUP) + allocedQuota.get(
                        EnvQuotaKey.SECURITY_GROUP);
                requiredQuota = specQuotaMap.get(EnvQuotaKey.SECURITY_GROUP);
                if (used + requiredQuota > quotaVal) {
                    errorQuota = used;
                    flag = true;
                }
            } else if (EnvQuotaKey.KEY_PAIR.equals(sysQuota.getQuotaKey())) {
                int used = usedQuota.get(EnvQuotaKey.KEY_PAIR) + allocedQuota.get(
                        EnvQuotaKey.KEY_PAIR);
                requiredQuota = specQuotaMap.get(EnvQuotaKey.KEY_PAIR);
                if (used + requiredQuota > quotaVal) {
                    errorQuota = used;
                    flag = true;
                }
            } else if (EnvQuotaKey.NETWORK.equals(sysQuota.getQuotaKey())) {
                int used = usedQuota.get(EnvQuotaKey.NETWORK) + allocedQuota.get(
                        EnvQuotaKey.NETWORK);
                requiredQuota = specQuotaMap.get(EnvQuotaKey.NETWORK);
                if (used + requiredQuota > quotaVal) {
                    errorQuota = used;
                    flag = true;
                }
            } else if (EnvQuotaKey.NET_CARD.equals(sysQuota.getQuotaKey())) {
                int used = usedQuota.get(EnvQuotaKey.NET_CARD) + allocedQuota.get(
                        EnvQuotaKey.NET_CARD);
                requiredQuota = specQuotaMap.get(EnvQuotaKey.NET_CARD);
                if (used + requiredQuota > quotaVal) {
                    errorQuota = used;
                    flag = true;
                }
            } else if (EnvQuotaKey.SUBNET.equals(sysQuota.getQuotaKey())) {
                int used = usedQuota.get(EnvQuotaKey.SUBNET) + allocedQuota.get(
                        EnvQuotaKey.SUBNET);
                requiredQuota = specQuotaMap.get(EnvQuotaKey.SUBNET);
                if (used + requiredQuota > quotaVal) {
                    errorQuota = used;
                    flag = true;
                }
            } else if (EnvQuotaKey.ROUTER.equals(sysQuota.getQuotaKey())) {
                int used = usedQuota.get(EnvQuotaKey.ROUTER) + allocedQuota.get(
                        EnvQuotaKey.ROUTER);
                requiredQuota = specQuotaMap.get(EnvQuotaKey.ROUTER);
                if (used + requiredQuota > quotaVal) {
                    errorQuota = used;
                    flag = true;
                }
            } else if (EnvQuotaKey.FIREWALL.equals(sysQuota.getQuotaKey())) {
                int used = usedQuota.get(EnvQuotaKey.FIREWALL) + allocedQuota.get(
                        EnvQuotaKey.FIREWALL);
                requiredQuota = specQuotaMap.get(EnvQuotaKey.FIREWALL);
                if (used + requiredQuota > quotaVal) {
                    errorQuota = used;
                    flag = true;
                }
            } else if (EnvQuotaKey.FLOATING_IP.equals(sysQuota.getQuotaKey())) {
                int used = usedQuota.get(EnvQuotaKey.FLOATING_IP)
                        + allocedQuota.get(EnvQuotaKey.FLOATING_IP)
                        + freezeQuota.get(EnvQuotaKey.FLOATING_IP);
                requiredQuota = specQuotaMap.get(EnvQuotaKey.FLOATING_IP);
                // 弹性IP
                if (used + requiredQuota > quotaVal) {
                    errorQuota = used;
                    flag = true;
                }
            } else if (EnvQuotaKey.VPC.equals(sysQuota.getQuotaKey())) {
                int used = usedQuota.get(EnvQuotaKey.VPC) + allocedQuota.get(EnvQuotaKey.VPC);
                requiredQuota = specQuotaMap.get(EnvQuotaKey.VPC);
                if (used + requiredQuota > quotaVal) {
                    errorQuota = used;
                    flag = true;
                }
            } else if (EnvQuotaKey.LOAD_BALANCE.equals(sysQuota.getQuotaKey())) {
                int used = usedQuota.get(EnvQuotaKey.LOAD_BALANCE) + allocedQuota.get(
                        EnvQuotaKey.LOAD_BALANCE);
                requiredQuota = specQuotaMap.get(EnvQuotaKey.LOAD_BALANCE);
                if (used + requiredQuota > quotaVal) {
                    errorQuota = used;
                    flag = true;
                }
            }

            // 判断
            if (flag) {
                int availabilityQuota = quotaVal - errorQuota;
                if (availabilityQuota < 0) {
                    availabilityQuota = 0;
                }
                msg = String.format("%s超过部门[%s]总配额限制，所需配额：%s%s，可用配额：%s%s，预占配额：%s%s, 子组织分配配额：%s%s",
                                    sysQuota.getQuotaName(),
                                    org.getOrgName(),
                                    requiredQuota,
                                    sysQuota.getUnit(),
                                    availabilityQuota,
                                    sysQuota.getUnit(),
                                    freezeQuota.get(sysQuota.getQuotaKey()),
                                    sysQuota.getUnit(),
                                    allocedQuota.get(sysQuota.getQuotaKey()),
                                    sysQuota.getUnit()
                );

                break;
            }
        }
        return msg;
    }

    private int sumDiskSize(JsonNode params) {
        int storeSum = 0;
        // 系统盘
        JsonNode systemDisk = params.get("systemDisk");
        if (systemDisk != null && systemDisk.get("systemDiskSize") != null) {
            storeSum += systemDisk.get("systemDiskSize").asLong();
        }

        // 数据盘
        JsonNode dataDisks = params.get("dataDisk");
        if (dataDisks != null && dataDisks.elements().hasNext()) {
            for (Iterator<JsonNode> iterator = dataDisks.elements(); iterator.hasNext(); ) {
                JsonNode dataDisk = iterator.next();
                if (dataDisk.get("dataDiskSize") != null) {
                    storeSum += dataDisk
                            .get("dataDiskSize").asInt();
                }

            }

        }
        return storeSum;
    }

    private Map<String, Integer> newInitQuotaMap() {
        Map<String, Integer> quotaMap = new HashMap<>();
        quotaMap.put(EnvQuotaKey.VM_COUNT, 0);
        quotaMap.put(EnvQuotaKey.CPU_COUNT, 0);
        quotaMap.put(EnvQuotaKey.MEMORY_MAX, 0);
        quotaMap.put(EnvQuotaKey.STORAGE_MAX, 0);

        quotaMap.put(EnvQuotaKey.SNAPSHOT_COUNT, 0);
        quotaMap.put(EnvQuotaKey.VOLUME_COUNT, 0);
        quotaMap.put(EnvQuotaKey.NET_CARD, 0);
        quotaMap.put(EnvQuotaKey.NETWORK, 0);
        quotaMap.put(EnvQuotaKey.SECURITY_GROUP, 0);
        quotaMap.put(EnvQuotaKey.VPC, 0);
        quotaMap.put(EnvQuotaKey.SUBNET, 0);
        quotaMap.put(EnvQuotaKey.ROUTER, 0);
        quotaMap.put(EnvQuotaKey.FIREWALL, 0);
        quotaMap.put(EnvQuotaKey.FLOATING_IP, 0);
        quotaMap.put(EnvQuotaKey.KEY_PAIR, 0);
        quotaMap.put(EnvQuotaKey.LOAD_BALANCE, 0);
        return quotaMap;
    }

    private Map<String, Integer> newMaxQuotaMap() {
        Map<String, Integer> quotaMap = new HashMap<>();
        quotaMap.put(EnvQuotaKey.VM_COUNT, WebConstants.MAX_QUOTA);
        quotaMap.put(EnvQuotaKey.CPU_COUNT, WebConstants.MAX_QUOTA);
        quotaMap.put(EnvQuotaKey.MEMORY_MAX, WebConstants.MAX_QUOTA);
        quotaMap.put(EnvQuotaKey.STORAGE_MAX, WebConstants.MAX_QUOTA);

        quotaMap.put(EnvQuotaKey.SNAPSHOT_COUNT, WebConstants.MAX_QUOTA);
        quotaMap.put(EnvQuotaKey.VOLUME_COUNT, WebConstants.MAX_QUOTA);
        quotaMap.put(EnvQuotaKey.NET_CARD, WebConstants.MAX_QUOTA);
        quotaMap.put(EnvQuotaKey.NETWORK, WebConstants.MAX_QUOTA);
        quotaMap.put(EnvQuotaKey.SECURITY_GROUP, WebConstants.MAX_QUOTA);
        quotaMap.put(EnvQuotaKey.VPC, WebConstants.MAX_QUOTA);
        quotaMap.put(EnvQuotaKey.SUBNET, WebConstants.MAX_QUOTA);
        quotaMap.put(EnvQuotaKey.ROUTER, WebConstants.MAX_QUOTA);
        quotaMap.put(EnvQuotaKey.FIREWALL, WebConstants.MAX_QUOTA);
        quotaMap.put(EnvQuotaKey.FLOATING_IP, WebConstants.MAX_QUOTA);
        quotaMap.put(EnvQuotaKey.KEY_PAIR, WebConstants.MAX_QUOTA);
        quotaMap.put(EnvQuotaKey.LOAD_BALANCE, WebConstants.MAX_QUOTA);
        return quotaMap;
    }

    @Override
    public List<Company> findCompanyByUserSid(String company, Long userSid) {
        return this.companyMapper.findCompanyByUserSid(company,userSid);
    }
}
