/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.module.operationcore.service.process.impl;

import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResBms;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResShareTypeUse;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResShareTypes;
import cn.com.cloudstar.rightcloud.core.pojo.common.ShareConfig;
import cn.com.cloudstar.rightcloud.core.pojo.vo.audit.ServiceOrderDTO;
import cn.com.cloudstar.rightcloud.oss.common.constants.BusinessMessageConstants;
import cn.com.cloudstar.rightcloud.oss.module.account.cfn.CfnService;
import cn.com.cloudstar.rightcloud.oss.module.account.cfn.dto.BssQuotaReq;
import cn.com.cloudstar.rightcloud.oss.module.account.cfn.dto.QuotaDetailsDto;
import cn.com.cloudstar.rightcloud.oss.module.account.cfn.dto.UpdatePoolUserReq;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.response.*;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mongodb.client.result.UpdateResult;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.logging.log4j.util.Strings;
import org.bson.types.ObjectId;
import org.camunda.bpm.engine.HistoryService;
import org.camunda.bpm.engine.IdentityService;
import org.camunda.bpm.engine.ProcessEngine;
import org.camunda.bpm.engine.RepositoryService;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.TaskService;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.DelegateTask;
import org.camunda.bpm.engine.history.HistoricActivityInstance;
import org.camunda.bpm.engine.history.HistoricProcessInstance;
import org.camunda.bpm.engine.history.HistoricTaskInstance;
import org.camunda.bpm.engine.history.HistoricVariableInstance;
import org.camunda.bpm.engine.identity.Group;
import org.camunda.bpm.engine.impl.persistence.entity.ProcessDefinitionEntity;
import org.camunda.bpm.engine.impl.pvm.process.ActivityImpl;
import org.camunda.bpm.engine.repository.ProcessDefinition;
import org.camunda.bpm.engine.runtime.ProcessInstance;
import org.camunda.bpm.engine.task.IdentityLink;
import org.camunda.bpm.engine.task.Task;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.EndEvent;
import org.camunda.bpm.model.bpmn.instance.ExclusiveGateway;
import org.camunda.bpm.model.bpmn.instance.SequenceFlow;
import org.camunda.bpm.model.bpmn.instance.ServiceTask;
import org.camunda.bpm.model.bpmn.instance.StartEvent;
import org.camunda.bpm.model.bpmn.instance.UserTask;
import org.camunda.bpm.model.xml.instance.ModelElementInstance;
import org.jetbrains.annotations.NotNull;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.sleuth.SpanNamer;
import org.springframework.cloud.sleuth.Tracer;
import org.springframework.cloud.sleuth.instrument.async.TraceCallable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ClassUtils;
import org.springframework.util.ObjectUtils;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.Resource;
import javax.xml.XMLConstants;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.XmlUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.adapter.core.MQHelper;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.MANetworksCidrsQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.MANetworksCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.MAPoolsDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.MAPoolsUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.MASharePoolsUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.Network;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.NetworkMetaLabels;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.NetworkMetadata;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.NetworkSpec;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.PoolMetaAnnotations;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.PoolMetaLabels;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.PoolMetadata;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.PoolNodeAz;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.PoolSpec;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.result.MANetworkCidrsResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.result.MANetworksCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.result.MAPoolsDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.result.MAPoolsQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.result.MASharePoolsQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.price.enums.ChargeTypeEnum;
import cn.com.cloudstar.rightcloud.basic.data.pojo.base.Base;
import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnv;
import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.ResCloudEnv;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResContainerCluster;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResShare;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResVd;
import cn.com.cloudstar.rightcloud.common.common.FeignUrlConfig;
import cn.com.cloudstar.rightcloud.common.common.RightCloudResult;
import cn.com.cloudstar.rightcloud.common.constants.NotificationConsts;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.constants.res.type.CloudEnvType;
import cn.com.cloudstar.rightcloud.common.enums.CommonPropertyKeyEnum;
import cn.com.cloudstar.rightcloud.common.mq.request.BaseNotificationMqBean;
import cn.com.cloudstar.rightcloud.common.mq.request.MailNotificationMq;
import cn.com.cloudstar.rightcloud.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.common.util.CrytoUtilSimple;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.core.pojo.constant.ServiceProcessStatus;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.BillBillingCycleCost;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.BizAccountDeal;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost;
import cn.com.cloudstar.rightcloud.core.pojo.dto.audit.ResourceInfo;
import cn.com.cloudstar.rightcloud.core.pojo.dto.audit.ServiceOrder;
import cn.com.cloudstar.rightcloud.core.pojo.dto.audit.ServiceOrderDetail;
import cn.com.cloudstar.rightcloud.core.pojo.dto.audit.ServiceOrderPriceDetail;
import cn.com.cloudstar.rightcloud.core.pojo.dto.audit.ServiceOrderResourceRef;
import cn.com.cloudstar.rightcloud.core.pojo.dto.bss.BizCouponAccount;
import cn.com.cloudstar.rightcloud.core.pojo.dto.charge.BizBillingAccount;
import cn.com.cloudstar.rightcloud.core.pojo.dto.process.BusinessDto;
import cn.com.cloudstar.rightcloud.core.pojo.dto.process.Process;
import cn.com.cloudstar.rightcloud.core.pojo.dto.process.ProcessActivity;
import cn.com.cloudstar.rightcloud.core.pojo.dto.process.ProcessActivityDto;
import cn.com.cloudstar.rightcloud.core.pojo.dto.process.ProcessDto;
import cn.com.cloudstar.rightcloud.core.pojo.dto.process.ProcessNode;
import cn.com.cloudstar.rightcloud.core.pojo.dto.process.ProcessNodeConfig;
import cn.com.cloudstar.rightcloud.core.pojo.dto.process.ProcessNodeRoleDto;
import cn.com.cloudstar.rightcloud.core.pojo.dto.process.ProcessVersion;
import cn.com.cloudstar.rightcloud.core.pojo.dto.process.ServiceProcess;
import cn.com.cloudstar.rightcloud.core.pojo.dto.st.ProcessTemplate;
import cn.com.cloudstar.rightcloud.core.pojo.dto.st.ProcessTemplateDetail;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.SysConfig;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.User;
import cn.com.cloudstar.rightcloud.driver.pojo.base.BaseResult;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicInfoUtil;
import cn.com.cloudstar.rightcloud.oss.common.cache.JedisUtil;
import cn.com.cloudstar.rightcloud.oss.common.constants.BillingConstants;
import cn.com.cloudstar.rightcloud.oss.common.constants.BillingConstants.ChargeType;
import cn.com.cloudstar.rightcloud.oss.common.constants.Constants;
import cn.com.cloudstar.rightcloud.oss.common.constants.ServiceConfigArrKey;
import cn.com.cloudstar.rightcloud.oss.common.constants.ma.MaBasicStatus;
import cn.com.cloudstar.rightcloud.oss.common.constants.ma.MaPoolProessPhase;
import cn.com.cloudstar.rightcloud.oss.common.constants.ma.MaPoolStatusEnum;
import cn.com.cloudstar.rightcloud.oss.common.constants.status.OrderStatus;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.BillType;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.DealType;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.LdapPropertyKey;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.OrderType;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.PriceType;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.TradeType;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.UserType;
import cn.com.cloudstar.rightcloud.oss.common.enums.ApplyTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.BooleanEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.BusinessTagEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.FederationStatusConstants;
import cn.com.cloudstar.rightcloud.oss.common.enums.NodeTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.RechargeTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.ResourceApiEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.SfProductEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.UnitsEnum;
import cn.com.cloudstar.rightcloud.oss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.oss.common.mybatis.enums.RequirePermissionEnum;
import cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.oss.common.pojo.LdapSyncRequest;
import cn.com.cloudstar.rightcloud.oss.common.pojo.LicenseVo;
import cn.com.cloudstar.rightcloud.oss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.oss.common.pojo.ecs.dto.ResVdDto;
import cn.com.cloudstar.rightcloud.oss.common.pojo.ecs.request.ResVmInfoListRequest;
import cn.com.cloudstar.rightcloud.oss.common.pojo.ecs.result.ResVmInfoListResult;
import cn.com.cloudstar.rightcloud.oss.common.util.AuthUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.BaseClearPassUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.BigDecimalUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.CloudClientFactory;
import cn.com.cloudstar.rightcloud.oss.common.util.DateUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.IdWorker;
import cn.com.cloudstar.rightcloud.oss.common.util.LicenseUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.NoUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.PlaintextShieldUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.PropertiesUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.WebUserUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.cache.AuthUserHolder;
import cn.com.cloudstar.rightcloud.oss.module.Bill.bean.IdName;
import cn.com.cloudstar.rightcloud.oss.module.Bill.service.IBizBillingCycleService;
import cn.com.cloudstar.rightcloud.oss.module.access.service.PolicyService;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.model.BizContract;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.model.BizContractDetail;
import cn.com.cloudstar.rightcloud.oss.module.account.bean.model.BizDistributor;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.org.BizContractDetailMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.org.BizContractMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.org.BizDistributorMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.dao.user.UserMapper;
import cn.com.cloudstar.rightcloud.oss.module.account.service.org.OrgService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.role.RoleService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.user.IamPermissionService;
import cn.com.cloudstar.rightcloud.oss.module.account.service.user.UserService;
import cn.com.cloudstar.rightcloud.oss.module.collector.bean.util.RedisLock;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.ActionParam;
import cn.com.cloudstar.rightcloud.oss.module.feign.bean.request.RenewParam;
import cn.com.cloudstar.rightcloud.oss.module.feign.service.FeignService;
import cn.com.cloudstar.rightcloud.oss.module.feign.service.ResourceDcFeignService;
import cn.com.cloudstar.rightcloud.oss.module.maintenance.dao.templates.ProcessTemplateDetailMapper;
import cn.com.cloudstar.rightcloud.oss.module.maintenance.service.templates.ProcessTemplateService;
import cn.com.cloudstar.rightcloud.oss.module.operate.dao.EntityMapper;
import cn.com.cloudstar.rightcloud.oss.module.operationcore.activiti.util.ProcessConstants;
import cn.com.cloudstar.rightcloud.oss.module.operationcore.bean.process.request.ApproveProcessRequest;
import cn.com.cloudstar.rightcloud.oss.module.operationcore.bean.process.response.AuditProcessNodeResponse;
import cn.com.cloudstar.rightcloud.oss.module.operationcore.bean.process.response.ServiceProcessResponse;
import cn.com.cloudstar.rightcloud.oss.module.operationcore.dao.process.ProcessMapper;
import cn.com.cloudstar.rightcloud.oss.module.operationcore.dao.process.ProcessNodeMapper;
import cn.com.cloudstar.rightcloud.oss.module.operationcore.dao.process.TaskDetailMapper;
import cn.com.cloudstar.rightcloud.oss.module.operationcore.service.process.BusinessService;
import cn.com.cloudstar.rightcloud.oss.module.operationcore.service.process.ProcessMgtService;
import cn.com.cloudstar.rightcloud.oss.module.operationcore.service.process.ProcessService;
import cn.com.cloudstar.rightcloud.oss.module.operationcore.service.process.ServiceProcessService;
import cn.com.cloudstar.rightcloud.oss.module.order.dao.BizCouponAccountMapper;
import cn.com.cloudstar.rightcloud.oss.module.order.dao.ServiceOrderDetailMapper;
import cn.com.cloudstar.rightcloud.oss.module.order.dao.ServiceOrderMapper;
import cn.com.cloudstar.rightcloud.oss.module.order.dao.ServiceOrderPriceDetailMapper;
import cn.com.cloudstar.rightcloud.oss.module.order.dao.ServiceOrderResourceRefMapper;
import cn.com.cloudstar.rightcloud.oss.module.order.service.ServiceOrderService;
import cn.com.cloudstar.rightcloud.oss.module.pricing.bean.priceconfig.model.BizBillingSpecRef;
import cn.com.cloudstar.rightcloud.oss.module.pricing.bean.priceconfig.model.BizBillingSpecRefExample;
import cn.com.cloudstar.rightcloud.oss.module.pricing.bean.priceconfig.model.SpecRefValue;
import cn.com.cloudstar.rightcloud.oss.module.pricing.dao.priceconfig.BizAccountDealMapper;
import cn.com.cloudstar.rightcloud.oss.module.pricing.dao.priceconfig.BizBillingAccountMapper;
import cn.com.cloudstar.rightcloud.oss.module.pricing.dao.priceconfig.BizBillingSpecRefMapper;
import cn.com.cloudstar.rightcloud.oss.module.resource.service.HPCService;
import cn.com.cloudstar.rightcloud.oss.module.resource.service.HPCServiceImpl;
import cn.com.cloudstar.rightcloud.oss.module.resource.service.IResMaPoolService;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.bean.model.ResFederationInst;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.bean.model.SfProductResource;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.bean.model.SfProductResourceCue;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.bean.model.SfProductResourceCueExample;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.dao.ResFederationInstMapper;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.dao.SfProductResourceCueMapper;
import cn.com.cloudstar.rightcloud.oss.module.selfservice.dao.SfProductResourceMapper;
import cn.com.cloudstar.rightcloud.oss.module.serviceProcess.bean.model.SfServiceProcessRela;
import cn.com.cloudstar.rightcloud.oss.module.serviceProcess.dao.SfServiceProcessRelaMapper;
import cn.com.cloudstar.rightcloud.oss.module.system.service.config.SysConfigService;
import cn.com.cloudstar.rightcloud.oss.module.system.service.message.ISysOssMessageService;
import cn.com.cloudstar.rightcloud.oss.module.system.service.message.NotificationService;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.cloud.request.CloudEnvParams;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.CreateHPCClusterResult;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.CreateHPCSharePool;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.CreateHPCSharePoolResult;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.CreateHpcDrpResource;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.HpcSyncCloudEnvRequest;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.QueryHpcDrpResourceRequest;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.ResHpcClusterRemoteModule;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.ma.ResMaNetWorkVo;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.ma.ResMaPoolRelation;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.ma.ResMaPoolRelationExample;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.ma.ResMaPoolVO;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.oceanstor.ResOceanstorShare;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResChangeRecordDTO;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.cce.CceRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.cloud.CloudEnvRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.ecs.VmRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.hpc.HPCRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.ma.MaRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.ma.ResMaPoolRelationRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.res.ResChangeRecordRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.server.ResVmRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.share.ShareRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.storage.ResVdRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.system.service.system.SysConfigRemoteService;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.*;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mongodb.client.result.UpdateResult;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.logging.log4j.util.Strings;
import org.bson.types.ObjectId;
import org.camunda.bpm.engine.*;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.DelegateTask;
import org.camunda.bpm.engine.history.HistoricActivityInstance;
import org.camunda.bpm.engine.history.HistoricProcessInstance;
import org.camunda.bpm.engine.history.HistoricTaskInstance;
import org.camunda.bpm.engine.history.HistoricVariableInstance;
import org.camunda.bpm.engine.identity.Group;
import org.camunda.bpm.engine.impl.persistence.entity.ProcessDefinitionEntity;
import org.camunda.bpm.engine.impl.pvm.process.ActivityImpl;
import org.camunda.bpm.engine.repository.ProcessDefinition;
import org.camunda.bpm.engine.runtime.ProcessInstance;
import org.camunda.bpm.engine.task.IdentityLink;
import org.camunda.bpm.engine.task.Task;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.*;
import org.camunda.bpm.model.xml.instance.ModelElementInstance;
import org.jetbrains.annotations.NotNull;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.sleuth.SpanNamer;
import org.springframework.cloud.sleuth.Tracer;
import org.springframework.cloud.sleuth.instrument.async.TraceCallable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ClassUtils;
import org.springframework.util.ObjectUtils;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.annotation.Resource;
import javax.xml.XMLConstants;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.*;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mongodb.client.result.UpdateResult;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.logging.log4j.util.Strings;
import org.bson.types.ObjectId;
import org.camunda.bpm.engine.*;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.DelegateTask;
import org.camunda.bpm.engine.history.HistoricActivityInstance;
import org.camunda.bpm.engine.history.HistoricProcessInstance;
import org.camunda.bpm.engine.history.HistoricTaskInstance;
import org.camunda.bpm.engine.history.HistoricVariableInstance;
import org.camunda.bpm.engine.identity.Group;
import org.camunda.bpm.engine.impl.persistence.entity.ProcessDefinitionEntity;
import org.camunda.bpm.engine.impl.pvm.process.ActivityImpl;
import org.camunda.bpm.engine.repository.ProcessDefinition;
import org.camunda.bpm.engine.runtime.ProcessInstance;
import org.camunda.bpm.engine.task.IdentityLink;
import org.camunda.bpm.engine.task.Task;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.*;
import org.camunda.bpm.model.xml.instance.ModelElementInstance;
import org.jetbrains.annotations.NotNull;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.sleuth.SpanNamer;
import org.springframework.cloud.sleuth.Tracer;
import org.springframework.cloud.sleuth.instrument.async.TraceCallable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ClassUtils;
import org.springframework.util.ObjectUtils;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.annotation.Resource;
import javax.xml.XMLConstants;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.com.cloudstar.rightcloud.common.constants.NotificationConsts.SEND_NOTIFICATION_EXCHANGE;
import static cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.MONTH_PATTERN;

/**
 * <AUTHOR>
 * @date 2018/8/2
 */
@Component("processService")
@Slf4j
public class ProcessServiceImpl implements ProcessService {

    public static final String AVAILABLE = "available";
    public static final String ROLLBACK = "rollback";
    public static final String HPC_RENEW = "hpc_renew";

    private static final String POST_PAID_CONFIG_KEY = "postpaid.min.limit";
    private static final String SALE_TYPE = "02";
    private static final String NORMAL_TYPE = "01";
    private static final String PORT = "22";
    /**
     * 配额申请类型
     */
    private final String QUOTA_PROCESS = "quota-process";
    /**
     * 一分钱
     */
    private static final BigDecimal onePennyMoney = new BigDecimal("0.01");

    @DubboReference
    private MaRemoteService maRemoteService;

    @Autowired
    private ServiceOrderMapper serviceOrderMapper;

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private HistoryService historyService;

    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private BusinessService businessService;

    @Autowired
    private UserService userService;

    @Autowired
    private ProcessMgtService processMgtService;

    @Autowired
    private ProcessMapper processMapper;

    @Autowired
    private ServiceProcessService serviceProcessService;

    @Autowired
    private ProcessNodeMapper processNodeMapper;
    @Autowired
    private SfServiceProcessRelaMapper sfServiceProcessRelaMapper;
    @Autowired
    private RoleService roleService;

    @Autowired
    @Lazy
    private ServiceOrderService serviceOrderService;

    @Autowired
    private IamPermissionService iamPermissionService;

    @DubboReference
    private ShareRemoteService shareRemoteService;
    @Autowired
    private SysConfigService sysConfigService;

    @Autowired
    private BizBillingAccountMapper bizBillingAccountMapper;

    @Autowired
    private ServiceOrderDetailMapper serviceOrderDetailMapper;

    @Autowired
    private ProcessTemplateDetailMapper processTemplateDetailMapper;

    @Autowired
    private ProcessTemplateService processTemplateService;

    @Autowired
    private BizContractMapper bizContractMapper;
    @Autowired
    private BizContractDetailMapper bizContractDetailMapper;

    @DubboReference
    private CloudEnvRemoteService cloudEnvRemoteService;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    @SuppressWarnings("all")
    @Qualifier("cloudExecutor")
    private ThreadPoolTaskExecutor executorService;

    @DubboReference
    private ResChangeRecordRemoteService resChangeRecordRemoteService;
    @Autowired
    private EntityMapper entityMapper;

    @Autowired
    private HPCService hpcService;
    @Autowired
    private TaskDetailMapper taskDetailMapper;

    @Autowired
    private BizCouponAccountMapper bizCouponAccountMapper;

    @Value("${sfsType:NFS}")
    String sfsType;

    /**
     * HCSO
     */
    private static final String IAM_ENV_TYPE = "HCSO";

    @Autowired
    private ServiceOrderPriceDetailMapper serviceOrderPriceDetailMapper;

    @Autowired
    private MongoTemplate mongoTemplate;

    final ExecutorService threadPool = new ThreadPoolExecutor(4, 32, 0L, TimeUnit.SECONDS,
                                                              new LinkedBlockingQueue<>());

    @Value("${integrate.skip:false}")
    private Boolean integrateSkip;

    @Autowired
    private BizAccountDealMapper bizAccountDealMapper;

    @Autowired
    private SfProductResourceMapper sfProductResourceMapper;
    @Autowired
    private ServiceOrderResourceRefMapper serviceOrderResourceRefMapper;

    @Autowired
    private IdentityService identityService;

    @Autowired
    private SfProductResourceCueMapper sfProductResourceCueMapper;

    @Autowired
    private BizBillingSpecRefMapper bizBillingSpecRefMapper;

    @Autowired
    private PolicyService policyService;

    @Autowired
    private OrgService orgService;

    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private NotificationService notificationService;

    @Autowired
    private SysConfigRemoteService sysConfigRemoteService;

    @Resource
    private RedisLock redisLock;

    @Autowired
    private IResMaPoolService resMaPoolsService;
    @DubboReference
    private ResMaPoolRelationRemoteService resMaPoolRelationRemoteService;

    private static final IdWorker ID_WORKER = new IdWorker();
    @Resource
    private BizDistributorMapper bizDistributorMapper;
    @DubboReference
    private HPCRemoteService hpcRemoteService;

    @Autowired
    private Tracer tracer;

    @Autowired
    private SpanNamer spanNamer;

    @DubboReference
    private ResVmRemoteService resVmRemoteService;

    @Autowired
    private ISysOssMessageService sysOssMessageService;

    private static final List<String> RES_MA_POOL_PROCESS_NOT_IN_LIST = Arrays.asList(
            MaPoolProessPhase.UPDATE_MA_POOL_PEDING, MaPoolProessPhase.UPDATE_SHARE_POOL_PEDING);


    @Autowired
    private ProcessEngine processEngine;

    @Autowired
    private IBizBillingCycleService bizBillingCycleService;

    @Autowired
    private FeignService feignService;
    @Autowired
    private ResFederationInstMapper resFederationInstMapper;

    @Autowired
    private ResourceDcFeignService resourceDcFeignService;

    @DubboReference
    private CceRemoteService cceRemoteService;

    @DubboReference
    private ResVdRemoteService ebsRemoteService;
    @DubboReference
    private VmRemoteService vmRemoteService;


    @Autowired
    private ServiceOrderPriceDetailMapper priceDetailMapper;

    @Autowired
    private ServiceOrderResourceRefMapper resourceRefMapper;

    @Autowired
    private CfnService cfnService;

    @Override
    public List<ProcessDto> list() {
        List<ProcessDefinition> processDefinitions = repositoryService
                .createProcessDefinitionQuery()
                .orderByProcessDefinitionVersion().asc().list();

        List<Process> defines = processMgtService.selectProcessByParams(new Process());
        final Map<String, Process> definesMap = Maps.newHashMap();
        for (Process process : defines) {
            definesMap.put(process.getProcessCode(), process);
        }

        Map<String, ProcessDefinition> map = Maps.newHashMap();

        if (processDefinitions != null && processDefinitions.size() > 0) {
            for (ProcessDefinition pd : processDefinitions) {
                map.put(pd.getKey(), pd);
            }
        }
        List<ProcessDefinition> pdList = new ArrayList<>(map.values());

        List<ProcessDto> processDtos = pdList.stream().map(define -> {
            ProcessDto processDto = new ProcessDto();
            processDto.setDefineKey(define.getKey());
            processDto.setCode(define.getKey());
            processDto.setName(define.getName());
            processDto.setImage(processPngImage(define));

            return processDto;
        }).filter(processDto -> {
            if (definesMap.containsKey(processDto.getDefineKey())) {
                Process process = definesMap.get(processDto.getDefineKey());

                if (processDto.getName() == null) {
                    processDto.setName(process.getProcessName());
                }

                return true;
            }

            return false;
        }).collect(Collectors.toList());

        return processDtos;
    }

    private String processPngImage(ProcessDefinition define) {
        InputStream pngStream = null;
        ByteArrayOutputStream out = null;
        try {
            pngStream = repositoryService.getResourceAsStream(define.getDeploymentId(), define.getResourceName());
            out = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int len;
            while ((len = pngStream.read(buffer, 0, 1024)) != -1) {
                out.write(buffer, 0, len);
            }

            byte[] pngBytes = out.toByteArray();

            return Base64.getEncoder().encodeToString(pngBytes);
        } catch (IOException e) {
            log.error(e.getMessage());
        } finally {
            IoUtil.close(pngStream);
            IOUtils.closeQuietly(out);
        }

        return "";
    }

    public String getXml(BpmnModelInstance bpmnModelInstance) {
        String xlsTemplateFileName = "template/xml/camunda-service-apply.xml";
        InputStream is = ClassUtils.getUserClass(this).getClassLoader().getResourceAsStream(xlsTemplateFileName);
        Document document = XmlUtil.readXML(is);
        String docStr = "";
        ByteArrayOutputStream outputStream = null;
        if (document != null) {
            try {
                TransformerFactory transformerFactory = TransformerFactory.newInstance();
                transformerFactory.setFeature(XMLConstants.FEATURE_SECURE_PROCESSING, true);
                Transformer transformer = transformerFactory.newTransformer();
                transformer.setOutputProperty("encoding", "UTF-8");
                outputStream = new ByteArrayOutputStream();
                transformer.transform(new DOMSource(document), new StreamResult(outputStream));
                //docStr = outputStream.toString();
                docStr = outputStream.toString("UTF-8");
            } catch (Exception e) {
                log.error("exception message:", e);
            } finally {
                IOUtils.closeQuietly(outputStream);
            }
        }
        Collection<UserTask> userTask = bpmnModelInstance.getModelElementsByType(UserTask.class);
        Collection<ServiceTask> serviceTask = bpmnModelInstance.getModelElementsByType(ServiceTask.class);
        Collection<StartEvent> startEvents = bpmnModelInstance.getModelElementsByType(StartEvent.class);
        Collection<ExclusiveGateway> exclusiveGateway = bpmnModelInstance.getModelElementsByType(
                ExclusiveGateway.class);
        Collection<SequenceFlow> sequenceFlows = bpmnModelInstance.getModelElementsByType(SequenceFlow.class);
        Collection<EndEvent> endEvents = bpmnModelInstance.getModelElementsByType(EndEvent.class);
        Collection<ModelElementInstance> processElements = bpmnModelInstance.getModelElementsByType(bpmnModelInstance.getModel().getType(org.camunda.bpm.model.bpmn.instance.Process.class));
        if (!processElements.isEmpty()) {
            org.camunda.bpm.model.bpmn.instance.Process process = (org.camunda.bpm.model.bpmn.instance.Process) processElements.iterator().next();
            String processId = process.getId();
            docStr = docStr.replaceAll("processId", processId);
        } else {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_508136419));
        }

        for (StartEvent startEvent : startEvents) {
            String id = startEvent.getId();
            docStr = docStr.replaceAll("startEventId", id);
            for (SequenceFlow sequenceFlow : sequenceFlows) {
                String sourceRef = sequenceFlow.getAttributeValue("sourceRef");
                if (StrUtil.equals(sourceRef, id)) {
                    docStr = docStr.replaceAll("startEventOutFlowId", sequenceFlow.getId());
                }
            }
        }
        for (UserTask task : userTask) {
            String id = task.getAttributeValue("id");
            if (StrUtil.isNotBlank(id)) {
                docStr = docStr.replaceAll("userTaskId", id);
            }
            for (SequenceFlow sequenceFlow : sequenceFlows) {
                String sourceRef = sequenceFlow.getAttributeValue("sourceRef");
                if (StrUtil.equals(sourceRef, id)) {
                    docStr = docStr.replaceAll("userTaskOutFlowId", sequenceFlow.getId());
                }
            }

        }
        for (ExclusiveGateway gateway : exclusiveGateway) {
            String id = gateway.getId();
            docStr = docStr.replaceAll("gatewayEventId", id);
            for (SequenceFlow sequenceFlow : sequenceFlows) {
                String sourceRef = sequenceFlow.getAttributeValue("sourceRef");
                String targetRef = sequenceFlow.getAttributeValue("targetRef");
                if (StrUtil.equals(sourceRef, id)) {
                    if (StrUtil.contains(targetRef, "open")) {
                        docStr = docStr.replaceAll("openFlowId", sequenceFlow.getId());
                    } else if (StrUtil.contains(targetRef, "close")) {
                        docStr = docStr.replaceAll("closedFlowId", sequenceFlow.getId());
                    }
                }
            }
        }
        for (ServiceTask task : serviceTask) {
            String id = task.getAttributeValue("id");
            if (StrUtil.contains(id, "open")) {
                docStr = docStr.replaceAll("executeServiceTaskId", id);
                for (SequenceFlow sequenceFlow : sequenceFlows) {
                    String sourceRef = sequenceFlow.getAttributeValue("sourceRef");
                    if (StrUtil.equals(sourceRef, id)) {
                        docStr = docStr.replaceAll("executeOutFlowId", sequenceFlow.getId());
                    }
                }
            } else {
                docStr = docStr.replaceAll("refuseServiceTaskId", id);
                for (SequenceFlow sequenceFlow : sequenceFlows) {
                    String sourceRef = sequenceFlow.getAttributeValue("sourceRef");
                    if (StrUtil.equals(sourceRef, id)) {
                        docStr = docStr.replaceAll("refuseOutFlowId", sequenceFlow.getId());
                    }
                }
            }
        }

        for (EndEvent endEvent : endEvents) {
            String id = endEvent.getId();
            docStr = docStr.replaceAll("endEventId", id);
        }

        boolean isUs = WebUtil.getHeaderAcceptLanguage();
        if (isUs) {
            docStr = this.xmlPngToUs(docStr);
        }

        return docStr;
    }


    public String getXml2(String xmlString) {

        HashMap<String, String> stringStringHashMap = new HashMap<>();
        Document document = XmlUtil.readXML(xmlString);
        // 获取开始Event
        NodeList startEvent = XmlUtil.getNodeListByXPath("//*[name()='startEvent']", document);
        for (int i = 0; i < startEvent.getLength(); i++) {
            stringStringHashMap.put(startEvent.item(i).getAttributes().getNamedItem("id").getNodeValue(), "startEvent");
        }
        // 获取userTask
        NodeList userTask = XmlUtil.getNodeListByXPath("//*[name()='userTask']", document);
        for (int i = 0; i < userTask.getLength(); i++) {
            stringStringHashMap.put(userTask.item(i).getAttributes().getNamedItem("id").getNodeValue(), "userTask");
        }
        // 获取exclusiveGateway
        NodeList exclusiveGateway = XmlUtil.getNodeListByXPath("//*[name()='exclusiveGateway']", document);
        for (int i = 0; i < exclusiveGateway.getLength(); i++) {
            stringStringHashMap.put(exclusiveGateway.item(i).getAttributes().getNamedItem("id").getNodeValue(),
                                    "exclusiveGateway");
        }
        // 获取serviceTask
        NodeList serviceTask = XmlUtil.getNodeListByXPath("//*[name()='serviceTask']", document);
        for (int i = 0; i < serviceTask.getLength(); i++) {
            stringStringHashMap.put(serviceTask.item(i).getAttributes().getNamedItem("id").getNodeValue(),
                                    "serviceTask");
        }
        // 获取endEvent
        NodeList endEvent = XmlUtil.getNodeListByXPath("//*[name()='endEvent']", document);
        for (int i = 0; i < endEvent.getLength(); i++) {
            stringStringHashMap.put(endEvent.item(i).getAttributes().getNamedItem("id").getNodeValue(), "endEvent");
        }

        NodeList BPMNShape = XmlUtil.getNodeListByXPath("//*[name()='bpmndi:BPMNShape']", document);
        for (int i = 0; i < BPMNShape.getLength(); i++) {
            String bpmnElementValue = BPMNShape.item(i).getAttributes().getNamedItem("bpmnElement").getNodeValue();
            String bpmnElement = stringStringHashMap.get(bpmnElementValue);
            if (StrUtil.isNotBlank(bpmnElement)) {
                Node width = BPMNShape.item(i).getChildNodes().item(1).getAttributes().getNamedItem("width");
                Node x = BPMNShape.item(i).getChildNodes().item(1).getAttributes().getNamedItem("x");
                Node y = BPMNShape.item(i).getChildNodes().item(1).getAttributes().getNamedItem("y");
                Node height = BPMNShape.item(i).getChildNodes().item(1).getAttributes().getNamedItem("height");
                if (StrUtil.equals(bpmnElement, "userTask")) {
                    width.setTextContent("100");
                    x.setTextContent("280");
                    y.setTextContent("125");
                    height.setTextContent("60");
                } else if (StrUtil.equals(bpmnElement, "serviceTask")) {
                    if (StrUtil.contains(bpmnElementValue, "openact-")) {
                        width.setTextContent("100");
                        x.setTextContent("710");
                        y.setTextContent("125");
                        height.setTextContent("60");
                    } else {
                        width.setTextContent("100");
                        x.setTextContent("710");
                        y.setTextContent("310");
                        height.setTextContent("60");
                    }
                } else if (StrUtil.equals(bpmnElement, "exclusiveGateway")) {
                    width.setTextContent("40");
                    x.setTextContent("470");
                    y.setTextContent("135");
                    height.setTextContent("40");
                    Element element = document.createElement("omgdc:Bounds");
                    element.setAttribute("x", "470");
                    element.setAttribute("y", "130");
                    element.setAttribute("width", "40");
                    element.setAttribute("height", "40");
                    Element element1 = document.createElement("bpmndi:BPMNLabel");
                    element1.appendChild(element);
                    BPMNShape.item(i).appendChild(element1);
                } else if (StrUtil.equals(bpmnElement, "startEvent")) {
                    width.setTextContent("30");
                    x.setTextContent("155");
                    y.setTextContent("140");
                    height.setTextContent("30");
                    Element element = document.createElement("omgdc:Bounds");
                    element.setAttribute("x", "159");
                    element.setAttribute("y", "170");
                    element.setAttribute("width", "23");
                    element.setAttribute("height", "14");
                    Element element1 = document.createElement("bpmndi:BPMNLabel");
                    element1.appendChild(element);
                    BPMNShape.item(i).appendChild(element1);
                } else if (StrUtil.equals(bpmnElement, "userTask")) {
                    width.setTextContent("100");
                    x.setTextContent("280");
                    y.setTextContent("125");
                    height.setTextContent("60");
                } else if (StrUtil.equals(bpmnElement, "endEvent")) {
                    width.setTextContent("30");
                    x.setTextContent("925");
                    y.setTextContent("140");
                    height.setTextContent("30");
                    Element element = document.createElement("omgdc:Bounds");
                    element.setAttribute("x", "928");
                    element.setAttribute("y", "116");
                    element.setAttribute("width", "23");
                    element.setAttribute("height", "14");
                    Element element1 = document.createElement("bpmndi:BPMNLabel");
                    element1.appendChild(element);
                    BPMNShape.item(i).appendChild(element1);
                }
            }
        }
        // 获取bpmndi:BPMNEdge
        NodeList BPMNEdge = XmlUtil.getNodeListByXPath("//*[name()='bpmndi:BPMNEdge']", document);
        // 获取sequenceFlow
        NodeList sequenceFlow = XmlUtil.getNodeListByXPath("//*[name()='sequenceFlow']", document);
        for (int i = 0; i < sequenceFlow.getLength(); i++) {
            String id = sequenceFlow.item(i).getAttributes().getNamedItem("id").getNodeValue();
            for (int j = 0; j < BPMNEdge.getLength(); j++) {
                Node item = BPMNEdge.item(j);
                String bpmnElement = BPMNEdge.item(j).getAttributes().getNamedItem("bpmnElement").getNodeValue();
                if (StrUtil.equals(id, bpmnElement)) {
                    if (i == 0) {
                        BPMNEdge.item(j).removeChild(BPMNEdge.item(j).getChildNodes().item(1));
                        BPMNEdge.item(j).removeChild(BPMNEdge.item(j).getChildNodes().item(2));
                        Element element1 = document.createElement("omgdi:waypoint");
                        element1.setAttribute("x", "185");
                        element1.setAttribute("y", "155");
                        Element element2 = document.createElement("omgdi:waypoint");
                        element2.setAttribute("x", "280");
                        element2.setAttribute("y", "155");
                        item.appendChild(element1);
                        item.appendChild(element2);
                    } else if (i == 1) {
                        BPMNEdge.item(j).removeChild(BPMNEdge.item(j).getChildNodes().item(1));
                        BPMNEdge.item(j).removeChild(BPMNEdge.item(j).getChildNodes().item(2));
                        Element element1 = document.createElement("omgdi:waypoint");
                        element1.setAttribute("x", "380");
                        element1.setAttribute("y", "155");
                        Element element2 = document.createElement("omgdi:waypoint");
                        element2.setAttribute("x", "470");
                        element2.setAttribute("y", "155");
                        BPMNEdge.item(j).appendChild(element1);
                        BPMNEdge.item(j).appendChild(element2);
                    } else if (i == 2) {
                        BPMNEdge.item(j).removeChild(BPMNEdge.item(j).getChildNodes().item(1));
                        BPMNEdge.item(j).removeChild(BPMNEdge.item(j).getChildNodes().item(2));
                        Element element1 = document.createElement("omgdi:waypoint");
                        element1.setAttribute("x", "510");
                        element1.setAttribute("y", "155");
                        Element element2 = document.createElement("omgdi:waypoint");
                        element2.setAttribute("x", "710");
                        element2.setAttribute("y", "155");
                        BPMNEdge.item(j).appendChild(element1);
                        BPMNEdge.item(j).appendChild(element2);
                        Element element = document.createElement("omgdc:Bounds");
                        element.setAttribute("x", "481");
                        element.setAttribute("y", "235");
                        element.setAttribute("width", "23");
                        element.setAttribute("height", "14");
                        Element element3 = document.createElement("bpmndi:BPMNLabel");
                        element1.appendChild(element);
                        BPMNShape.item(i).appendChild(element3);
                    } else if (i == 3) {
                        BPMNEdge.item(j).removeChild(BPMNEdge.item(j).getChildNodes().item(1));
                        BPMNEdge.item(j).removeChild(BPMNEdge.item(j).getChildNodes().item(2));
                        BPMNEdge.item(j).removeChild(BPMNEdge.item(j).getChildNodes().item(3));
                        Element element1 = document.createElement("omgdi:waypoint");
                        element1.setAttribute("x", "490");
                        element1.setAttribute("y", "180");
                        Element element2 = document.createElement("omgdi:waypoint");
                        element2.setAttribute("x", "490");
                        element2.setAttribute("y", "340");
                        Element element3 = document.createElement("omgdi:waypoint");
                        element3.setAttribute("x", "710");
                        element3.setAttribute("y", "340");
                        BPMNEdge.item(j).appendChild(element1);
                        BPMNEdge.item(j).appendChild(element2);
                        BPMNEdge.item(j).appendChild(element3);
                        Element element = document.createElement("omgdc:Bounds");
                        element.setAttribute("x", "481");
                        element.setAttribute("y", "235");
                        element.setAttribute("width", "23");
                        element.setAttribute("height", "14");
                        Element element4 = document.createElement("bpmndi:BPMNLabel");
                        element4.appendChild(element);
                        BPMNShape.item(i).appendChild(element4);
                    } else if (i == 4) {
                        BPMNEdge.item(j).removeChild(BPMNEdge.item(j).getChildNodes().item(1));
                        BPMNEdge.item(j).removeChild(BPMNEdge.item(j).getChildNodes().item(2));
                        Element element1 = document.createElement("omgdi:waypoint");
                        element1.setAttribute("x", "810");
                        element1.setAttribute("y", "155");
                        Element element2 = document.createElement("omgdi:waypoint");
                        element2.setAttribute("x", "925");
                        element2.setAttribute("y", "155");
                        BPMNEdge.item(j).appendChild(element1);
                        BPMNEdge.item(j).appendChild(element2);
                    } else {
                        BPMNEdge.item(j).removeChild(BPMNEdge.item(j).getChildNodes().item(1));
                        BPMNEdge.item(j).removeChild(BPMNEdge.item(j).getChildNodes().item(2));
                        BPMNEdge.item(j).removeChild(BPMNEdge.item(j).getChildNodes().item(3));
                        Element element1 = document.createElement("omgdi:waypoint");
                        element1.setAttribute("x", "810");
                        element1.setAttribute("y", "340");
                        Element element2 = document.createElement("omgdi:waypoint");
                        element2.setAttribute("x", "940");
                        element2.setAttribute("y", "340");
                        Element element3 = document.createElement("omgdi:waypoint");
                        element3.setAttribute("x", "940");
                        element3.setAttribute("y", "170");
                        BPMNEdge.item(j).appendChild(element1);
                        BPMNEdge.item(j).appendChild(element2);
                        BPMNEdge.item(j).appendChild(element3);
                    }

                }
            }
        }

        String docStr = "";
        ByteArrayOutputStream outputStream = null;
        if (document != null) {
            try {
                TransformerFactory transformerFactory = TransformerFactory.newInstance();
                transformerFactory.setFeature(XMLConstants.FEATURE_SECURE_PROCESSING, true);
                Transformer transformer = transformerFactory.newTransformer();
                transformer.setOutputProperty("encoding", "UTF-8");
                outputStream = new ByteArrayOutputStream();
                transformer.transform(new DOMSource(document), new StreamResult(outputStream));
                docStr = outputStream.toString("UTF-8");
            } catch (Exception e) {
                log.error("exception message:", e);
            } finally {
                IOUtils.closeQuietly(outputStream);
            }
        }
        return docStr;
    }

    private String processPngImage(String businessId) {

        Task task = taskService.createTaskQuery()
                               .processInstanceBusinessKey(businessId).singleResult();

        HistoricProcessInstance processInstance;
        String processInstanceId;
        //获取历史流程实例
        if (task == null) {
            processInstance = historyService.createHistoricProcessInstanceQuery()
                                            .processInstanceBusinessKey(businessId).singleResult();

            if (processInstance == null) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2145813674));
            }

            processInstanceId = processInstance.getId();
            ProcessConstants.PROCESS_FINISHED.set(true);
        } else {
            processInstanceId = task.getProcessInstanceId();
            processInstance = historyService.createHistoricProcessInstanceQuery()
                                            .processInstanceId(processInstanceId).singleResult();
            ProcessConstants.PROCESS_FINISHED.set(false);
        }

        BpmnModelInstance bpmnModelInstance = repositoryService.getBpmnModelInstance(
                processInstance.getProcessDefinitionId());

        return getXml(bpmnModelInstance);
    }

    @Override
    public ProcessDto selectByBusinessId(String businessId) {
        HistoricProcessInstance hpi = historyService.createHistoricProcessInstanceQuery()
                .processInstanceBusinessKey(businessId).singleResult();
        if (hpi == null) {
            return null;
        }

        ProcessDefinition define = repositoryService.createProcessDefinitionQuery()
                .processDefinitionId(hpi.getProcessDefinitionId()).singleResult();
        if (define == null) {
            return null;
        }

        ProcessDto processDto = new ProcessDto();
        processDto.setDefineKey(hpi.getProcessDefinitionKey());
        processDto.setCode(hpi.getProcessDefinitionKey());
        processDto.setName(hpi.getProcessDefinitionName());
        processDto.setImage(processPngImage(businessId));

        return processDto;
    }

    @Override
    public ProcessDto selectByDefineKey(String defineKey) {
        ProcessDefinition define = repositoryService.createProcessDefinitionQuery()
                                                    .processDefinitionKey(defineKey)
                                                    .orderByProcessDefinitionVersion().desc()
                                                    .listPage(0, 1).get(0);

        ProcessDto processDto = new ProcessDto();
        processDto.setDefineKey(define.getKey());
        processDto.setCode(define.getKey());
        processDto.setName(define.getName());
        BpmnModelInstance bpmnModelInstance = repositoryService.getBpmnModelInstance(define.getId());
        String xmlPng = Bpmn.convertToString(bpmnModelInstance);
        processDto.setImage(xmlPng);
        if (processDto.getName() == null) {
            Process param = new Process();
            param.setProcessCode(defineKey);

            List<Process> processList = processMapper.selectByParams(param);
            if (processList.size() > 0) {
                Process process = processList.get(0);
                processDto.setName(process.getProcessName());
            }
        }

        return processDto;
    }

    @Override
    public ProcessDto selectByProcessMgtDefineId(Long id) {
        Process process = processMgtService.selectByPrimaryKey(id);
        if (process == null) {
            return null;
        }

        return this.selectByDefineKey(process.getProcessCode());
    }

    /**
     * 开始流程 (业务系统调用)
     *
     * @param processId 流程定义ID
     * @param businessId 业务ID
     * @param variables 业务流程变量
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProcessInstance startProcess(String processId, String businessId, Map<String, Object> variables) {
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();

        if (Objects.isNull(variables)) {
            variables = Maps.newHashMap();
        }

        ProcessDto processDto = selectByDefineKey(processId);

        String businessCode = (String) variables.get("_business_code");
        String businessName = ProcessConstants.BUSINESS_MAP.get(businessCode);

        String startComment = String.format("[%s]提交了[%s]申请",
                                            authUser.toNameInfo(),
                                            businessName != null ? businessName : processDto.getName());

        variables.put("__process_start_comment", startComment);
        variables.put("__process_id", processId);

        log.info("流程启动消息：{}, 申请单: [{}]", startComment, businessId);

        return runtimeService.startProcessInstanceByKey(processId, businessId, variables);
    }

    /**
     * 获取任务审批候选人列表(流程内部自动调用)
     *
     * @param execution 流程执行环境
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> auditCandidates(DelegateExecution execution, String auditLevel) {

        String businssId = execution.getProcessBusinessKey();
        Map<String, Object> variables = execution.getVariables();

        variables.put(ProcessConstants.AUDIT_LEVEL, auditLevel);

        return businessService.queryCandidateUsers(businssId, variables);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> auditCandidateInlineMgts(DelegateExecution execution, String auditIds) {
        String[] auditIdArr = auditIds.split(",");

        String businssId = execution.getProcessBusinessKey();

        Map<String, Object> variables = execution.getVariables();

        log.info("申请单[{}], 组织: [{}], 候选人参数: [{}]",
                 businssId, variables.get("__org_sid"), auditIds);

        List<String> allCandidateUsers = Lists.newArrayList();
        List<String> roleIds = Lists.newArrayList();
        List<String> userIds = Lists.newArrayList();

        for (String auditId : auditIdArr) {
            String[] split = auditId.split("-");
            //已20开头的是用户 ID，已10开头的是角色 ID
            if (split[0].startsWith("20")) {
                userIds.add(split[1]);
            } else if (split[0].startsWith("10")) {
                roleIds.add(split[2]);
            }
        }
        String businessCode = (String) variables.get("_business_code");
        if (includeProcess(businessCode)) {
            //查询所有平台级角色
            roleIds = roleService.findRoleIdsByDataScope(RequirePermissionEnum.DATA_SCOPE_ENTITY.getScope());
            // 服务发布，查询角色关联所有人
            if (roleIds.size() > 0) {
                List<String> candidateUsers = businessService.queryRolesCandidateUsers(businssId, variables, roleIds);
                allCandidateUsers.addAll(candidateUsers);
            }
        } else {
            //根据角色查询候选人
            if (roleIds.size() > 0) {
                List<String> candidateUsers = businessService.queryCandidateUsers(businssId, variables, roleIds);
                allCandidateUsers.addAll(candidateUsers);
            }

            //根据选定用户选择候选人，需要判断权限
            if (userIds.size() > 0) {
                if(Objects.nonNull(variables.get("__org_sid"))){
                    List<User> users = userService.findUserAboveOrg(Long.valueOf((String) variables.get("__org_sid")));
                    List<String> candidateUsers
                            = userIds.parallelStream()
                                     .filter(userId -> users
                                             .parallelStream()
                                             .anyMatch(user -> Objects.equals(userId, String.valueOf(user.getUserSid()))))
                                     .collect(Collectors.toList());
                    allCandidateUsers.addAll(candidateUsers);
                }
            }
        }


        if (!ProcessConstants.TICKET.equals(businessCode)) {
            ServiceOrder serviceOrder = serviceOrderService.selectByOrderSn(businssId);
            ServiceOrder serviceOrderDetail = serviceOrderService.getServiceOrderDetail(serviceOrder.getId());
            if (CollectionUtils.isEmpty(serviceOrderDetail.getDetails()) ||
                    (!"HPC".equals(serviceOrderDetail.getDetails().get(0).getServiceType())
                            && !"HPC-SAAS".equals(serviceOrderDetail.getDetails().get(0).getServiceType()))) {
                serviceOrder.setStepName(execution.getCurrentActivityName());
                serviceOrderService.updateByPrimaryKeySelective(serviceOrder);
            }

        } else {
            String ticketUserid = (String) variables.get("_ticket_usersid");
            if (StringUtils.isNotBlank(ticketUserid)) {
                allCandidateUsers.add(ticketUserid);
            }
        }

        List<String> candidateUsers = allCandidateUsers.stream().distinct().collect(Collectors.toList());
        if (candidateUsers.size() < 1) {
            // 自动审批通过（当审批节点没有候选人）(废弃)
            // autoApprovalWhenNoCandidates(execution.getId(), businssId, variables);
        }

        return candidateUsers;
    }

    private static boolean includeProcess(String businessCode) {
        return Stream.of(ProcessConstants.SERVICE_PUBLISH,
                ProcessConstants.HPC_SERVICE_APPLY,
                ProcessConstants.HPC_DRP_SERVICE_APPLY,
                ProcessConstants.HPC_DRP_SERVICE_UPGRADE,
                ProcessConstants.SERVICE_APPLY,
                ProcessConstants.MA_DRP_SERVICE_APPLY,
                ProcessConstants.MA_SERVICE_RELEASE,
                ProcessConstants.MA_DRP_SERVICE_RELEASE,
                ProcessConstants.MA_DRP_SERVICE_UPGRADE,
                ProcessConstants.MA_DRP_SERVICE_DEGRADE,
                ProcessConstants.SERVICE_RELEASE)
            .anyMatch(p -> p.equals(businessCode));
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> auditGroupInlineMgts(DelegateExecution execution, String auditIds) {
        List<String> roleIds = Lists.newArrayList();
        Map<String, Object> variables = execution.getVariables();
        String businessCode = (String) variables.get("_business_code");
        if (includeBusinessCode(businessCode)) {
            //查询所有平台级角色
            roleIds = roleService.findRoleIdsByDataScope(RequirePermissionEnum.DATA_SCOPE_ENTITY.getScope());
        } else {
            String[] auditIdArr = auditIds.split(",");
            for (String auditId : auditIdArr) {
                String[] split = auditId.split("-");
                //已20开头的是用户 ID，已10开头的是角色 ID
                if (split[0].startsWith("10")) {
                    roleIds.add(split[2]);
                }
            }
        }

        return getCandidateGroups(roleIds);
    }

    private static boolean includeBusinessCode(String businessCode) {
        return Stream.of(ProcessConstants.SERVICE_PUBLISH,
                ProcessConstants.HPC_SERVICE_APPLY,
                ProcessConstants.HPC_DRP_SERVICE_APPLY,
                ProcessConstants.HPC_DRP_SERVICE_UPGRADE,
                ProcessConstants.SERVICE_APPLY,
                ProcessConstants.MA_DRP_SERVICE_APPLY,
                ProcessConstants.MA_SERVICE_RELEASE,
                ProcessConstants.MA_DRP_SERVICE_UPGRADE,
                ProcessConstants.MA_DRP_SERVICE_DEGRADE)
            .anyMatch(p -> p.equals(businessCode));
    }

    /**
     * 根据角色获取候选组
     *
     * @param roleIds
     */
    @Override
    public List<String> getCandidateGroups(List<String> roleIds) {
        List<String> allCandidateGroups = Lists.newArrayList();
        // 根据角色获取候选人
        Map<String, List<Long>> roleUsersMap = Maps.newHashMap();

        if (roleIds.size() > 0) {
            for (String roleId : roleIds) {
                List<Long> userIds = roleService.findUserIdsByRoleId(roleId);
                roleUsersMap.put(roleId, userIds);
            }

        }
        // 保存角色-用户到activiti的group中
        Set<Entry<String, List<Long>>> entries = roleUsersMap.entrySet();
        Set<Long> userss = new HashSet<>();
        for (List<Long> value : roleUsersMap.values()) {
            userss.addAll(value);
        }
        for (Long user : userss) {
            org.camunda.bpm.engine.identity.User dbUser = identityService.createUserQuery()
                                                                         .userId(user.toString()).singleResult();
            if (Objects.isNull(dbUser)) {
                dbUser = identityService.newUser(user.toString());
                dbUser.setFirstName(user.toString());
                dbUser.setLastName(user.toString());
                dbUser.setEmail(user.toString());
                identityService.saveUser(dbUser);
            }

        }

        for (Entry<String, List<Long>> entry : entries) {
            String role = entry.getKey();
            List<Long> users = entry.getValue();
            allCandidateGroups.add(role);

            Group group = identityService.createGroupQuery().groupId(role).singleResult();
            if (Objects.isNull(group)) {
                group = identityService.newGroup(role);
                group.setName(role);
                group.setType("assignment");
                identityService.saveGroup(group);
            }

            List<org.camunda.bpm.engine.identity.User> groupUsers =
                    identityService.createUserQuery().memberOfGroup(role).list();

            Map<String, org.camunda.bpm.engine.identity.User> userGroupedMap = groupUsers.stream()
                                                                                         .collect(Collectors.toMap(
                                                                                                 org.camunda.bpm.engine.identity.User::getId,
                                                                                                 u -> u));

            for (org.camunda.bpm.engine.identity.User groupedUser : groupUsers) {
                if (!users.contains(Long.parseLong(groupedUser.getId()))) {
                    identityService.deleteMembership(groupedUser.getId(), role);
                }
            }

            for (Long user : users) {
                if (userGroupedMap.containsKey(user.toString())) {
                    continue;
                }
                identityService.createMembership(user.toString(), role);
            }
        }

        return allCandidateGroups;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> candidateTaskBusinessIds(String candidateId) {
        List<Task> tasks = taskService.createTaskQuery()
                                      .taskCandidateUser(candidateId)
                                      .list();
        if (tasks.size() == 0) {
            return Arrays.asList();
        }

        Set<String> instanceIds = tasks.stream().map(task -> {
            return task.getProcessInstanceId();
        }).collect(Collectors.toSet());
        List<ProcessInstance> instances = runtimeService.createProcessInstanceQuery()
                                                        .processInstanceIds(instanceIds).list();

        List<String> businsessIds = instances.stream()
                                             .map(instance ->
                                                          instance.getBusinessKey())
                                             .collect(Collectors.toList());

        return businsessIds;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BusinessDto candidateTask(String candidateId) {
        List<Task> tasks = taskService.createTaskQuery()
                                      .taskCandidateUser(candidateId)
                                      .list();
        if (tasks.size() == 0) {
            return new BusinessDto(Lists.newArrayList(), Maps.newHashMapWithExpectedSize(0));
        }

        Map<String, String> statusMap = Maps.newHashMap();

        Set<String> instanceIds = tasks.stream().map(task -> {
            statusMap.put(task.getProcessInstanceId(), task.getName());
            return task.getProcessInstanceId();
        }).collect(Collectors.toSet());
        List<ProcessInstance> instances = runtimeService.createProcessInstanceQuery()
                                                        .processInstanceIds(instanceIds).list();

        Map<String, String> businessStatusMap = Maps.newHashMap();
        List<String> businsessIds = instances.stream().map(instance -> {
            businessStatusMap.put(instance.getBusinessKey(), statusMap.get(instance.getId()));
            return instance.getBusinessKey();
        }).collect(Collectors.toList());

        return new BusinessDto(businsessIds, businessStatusMap);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> candidateHistoryTasks(String assigneeId) {

        List<HistoricActivityInstance> historys = historyService
                .createHistoricActivityInstanceQuery()
                .taskAssignee(assigneeId).list();

        List<String> ids = historys.stream().map(history -> {

            HistoricProcessInstance historicProcessInstance = historyService
                    .createHistoricProcessInstanceQuery()
                    .processInstanceId(history.getProcessInstanceId()).singleResult();

            return historicProcessInstance.getBusinessKey();
        }).collect(Collectors.toList());

        return ids;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void taskPass(String candidateId, String businsessId, Map<String, Object> variables) {
        if (variables == null) {
            variables = Maps.newHashMap();
        }

        variables.put(ProcessConstants.AUDIT, ProcessConstants.AUDIT_PASS);

        Task task = taskService.createTaskQuery().processInstanceBusinessKey(businsessId).singleResult();
        if (task == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_296564528));
        }

        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                                                        .processInstanceId(task.getProcessInstanceId()).singleResult();
        log.info("processInstance:[{}]", processInstance.toString());
        variables.put(processInstance.getCaseInstanceId() + ProcessConstants.AUDIT_STATUS_SUFFIX,
                      ProcessConstants.AUDIT_PASS);

        ProcessDefinition processDefinition = repositoryService.getProcessDefinition(
                processInstance.getProcessDefinitionId());
        String processDefinitionName = processDefinition.getName();
        Object comment = variables.get(ProcessConstants.AUDIT_COMMENT);
        if (comment != null) {
            // 保存审批注释(暂时未用到)
            taskService.addComment(task.getId(), task.getProcessInstanceId(), String.valueOf(comment));

            variables.put(processInstance.getCaseInstanceId() + ProcessConstants.AUDIT_COMMENT_SUFFIX,
                          String.valueOf(comment));
        }

        Object auditUname = variables.get("_audit_uname");
        if (auditUname != null) {
            variables.put(processInstance.getCaseInstanceId() + "_audit_uname",
                          String.valueOf(auditUname));
        }
        variables.put("_task_name", task.getName());
        //业务处理
        bizProcess(task, processDefinitionName, businsessId,
                   Convert.convert(ResourceInfo.class, variables.get("_resource_info")));

        taskService.claim(task.getId(), candidateId);
        taskService.complete(task.getId(), variables);

        log.info("审批任务完成消息：[{}]完成了申请单[{}]的[{}]审批任务", auditUname, businsessId, task.getName());
    }

    private void bizProcess(Task task, String processDefinitionName, String businsessId,
                            ResourceInfo resourceInfo) {
        // 查询申请单
        ServiceOrder serviceOrder = serviceOrderService.getServiceOrderDetail(Long.valueOf(businsessId));
        //hpc 预审批相关资源开通
        boolean notSkip = BooleanUtil.isFalse(this.integrateSkip);
        if (!notSkip) {
            log.info("hpc跳过预审批相关资源开通...");
        }
        List<ServiceOrderDetail> details = serviceOrder.getDetails();
        String serviceType = details.get(0).getServiceType();
        if (notSkip && (ProductCodeEnum.HPC.getProductCode().equalsIgnoreCase(details.get(0).getServiceType())
                || ProductCodeEnum.HPC_SAAS.getProductCode().equalsIgnoreCase(details.get(0).getServiceType()))) {
            if ("运营管理员预审批".equals(task.getName()) && "HPC服务开通".equals(processDefinitionName)) {
                log.info("hpc共享资源池预审批相关资源开通...");
                Criteria criteria = new Criteria();
                criteria.put("service_order_id", serviceOrder.getId());
                List<SfProductResource> sfProductResources = sfProductResourceMapper.selectByParams(criteria);
                SfProductResource sfProductResource = sfProductResources.get(0);

                // 将hpc共享资源池开通时的ldap同步移到预审批
                ResHpcClusterRemoteModule resHpcClusterRemoteModule = hpcRemoteService.selectByPrimaryKey(
                        sfProductResource.getClusterId());
                LdapSyncRequest ldapSyncRequest = new LdapSyncRequest();
                ldapSyncRequest.setOrgId(serviceOrder.getOrgSid());
                ldapSyncRequest.setBusinessCategoryList(Arrays.asList(resHpcClusterRemoteModule.getBusinessCategory()));
                policyService.synHpcToLdapByLdapSyncRequest(ldapSyncRequest);

                String oceanStroFlg = PropertiesUtil.getProperty("hpc.sfs.oceanstor.flg");
                //需要通知前端修改各个版本参数
                String hpcVersion = HPCServiceImpl.hpcVersion(serviceOrder);
                // 并发创建弹性文件，出现存储为负数
                synchronized (this) {
                    //启用原生调用创建共享
                    if ("2".equals(hpcVersion) && StringUtils.equalsIgnoreCase(oceanStroFlg, "1")) {
                        createOceanStor(businsessId, serviceOrder, details);
                    }
                    else if ("1".equals(hpcVersion) || ("2".equals(hpcVersion) && !StringUtils.equalsIgnoreCase(oceanStroFlg, "1"))) {
                        JSONObject sfsDetail = JSON.parseObject(details.get(1).getServiceConfig());
                        JSONObject sfsData = sfsDetail.getJSONObject("data").getJSONObject("sfs");
                        if (Objects.isNull(sfsData)) {
                            sfsData = sfsDetail.getJSONObject("data").getJSONObject(ProductCodeEnum.SFS.getProductCode());
                        }

                        //兼容武汉1.0版本
                        ResShare resShare = new ResShare();
                        resShare.setCloudEnvId(sfsDetail.getLong("cloudEnvId"));
                        SysConfig sfsAK = sysConfigService.selectByConfigKey("hpc.sfsAk");
                        String data = CrytoUtilSimple.decrypt(sfsAK.getConfigValue());
                        if (StringUtil.isNotBlank(data)) {
                            sfsAK.setConfigValue(data);
                        }
                        resShare.setAccessTo(sfsAK.getConfigValue());
                        resShare.setAccessLevel("rw");
                        if ("DPC".equals(sfsType)) {
                            String shareType = "HPC";
                            if (StringUtils.isNotBlank(sfsData.getString("shareType"))) {
                                shareType = sfsData.getString("shareType");
                            }
                            resShare.setAccessType("user");
                            //获取存储集群名称
                            resShare.setShareType(shareType);
                            resShare.setShareProto("DPC");
                        } else {
                            resShare.setShareProto("NFS");
                        }
                        resShare.setType("SFS");
                        resShare.setName("HPC-SFS-" + serviceOrder.getOrgSid());
                        resShare.setCreatedOrgSid(serviceOrder.getOrgSid());
                        Integer size = sfsData.getInteger("size");
                        resShare.setSize(size);
                        resShare.setServiceOrderId(Long.valueOf(businsessId));
                        //使用默认可用区
                        //resShare.setAvailabilityZone(sfsData.getString("availabilityZone"));
                        resShare.setStartTime(new Date());
                        resShare.setChargeType(ChargeType.POST_PAID);
                        log.info("resShare:{}", resShare);
                        this.checkUserTotal("SFS", size);

                        cn.com.cloudstar.rightcloud.common.pojo.RestResult result = BasicInfoUtil.replaceUserToInvoke(() -> shareRemoteService.createShare(resShare),
                                serviceOrder.getOwnerId());
                        log.info("ProcessServiceImpl.bizProcess createShareResult: {}", JSON.toJSONString(result));

                        // 将创建弹性文件回调时创建 ServiceOrderResourceRef 放在审批处
                        if (ProductCodeEnum.HPC.getProductCode().equalsIgnoreCase(details.get(0).getServiceType()) && result.getStatus()) {
                            ServiceOrderResourceRef resourceRef = new ServiceOrderResourceRef();
                            resourceRef.setOrderDetailId(details.get(1).getId());
                            resourceRef.setType("SFS");
                            resourceRef.setResourceId(result.getData().toString());
                            serviceOrderResourceRefMapper.insert(resourceRef);
                        }
                    }
                    else if ("3".equals(hpcVersion)) {
                        //调用ccm接口 创建共享资源池
                        CreateHPCSharePool createHPCSharePool = new CreateHPCSharePool();
                        createHPCSharePool.setHPCClusterID(sfProductResource.getClusterId());
                        createHPCSharePool.setCommonShareSize(HPCServiceImpl.getCommonSize(serviceOrder));
                        //取专属文件系统大小
                        Integer size = 0;
                        if (HPCServiceImpl.hasPrivateSFS(serviceOrder)) {
                            String productCode = "SFS";
                            JSONObject sfsDetail = JSON.parseObject(details.get(1).getServiceConfig());
                            JSONObject sfsData = sfsDetail.getJSONObject("data").getJSONObject("sfs");
                            if (Objects.isNull(sfsData)) {
                                sfsData = sfsDetail.getJSONObject("data").getJSONObject("SFS2.0");
                                productCode = "SFS2.0";
                            }
                            size = sfsData.getInteger("size");

                            createHPCSharePool.setShareSize(size);
                            this.checkUserTotal(productCode, size);
                        }

                        CreateHPCSharePoolResult createHPCSharePoolResult = BasicInfoUtil.replaceUserToInvoke(
                                () -> hpcRemoteService.createSharePool(createHPCSharePool),
                                serviceOrder.getOwnerId());
                        //插入ref
                        if (HPCServiceImpl.hasPrivateSFS(serviceOrder)) {
                            ServiceOrderResourceRef resourceRef = new ServiceOrderResourceRef();
                            resourceRef.setOrderDetailId(details.get(1).getId());
                            resourceRef.setType(details.get(1).getServiceType());
                            resourceRef.setResourceId(createHPCSharePoolResult.getPrivateShareId() + "");
                            serviceOrderResourceRefMapper.insert(resourceRef);
                        }
                    }else {
                        throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1652980930));
                    }
                }
            }
            else if ("服务退订".equals(processDefinitionName)) {
                policyService.synHpcToLdap(serviceOrder.getOrgSid());
            }
        }
        else if (notSkip && ProductCodeEnum.HPC_DRP.getProductCode().equals(details.get(0).getServiceType())) {
            hpcDrpBusinessProcess(task, processDefinitionName, businsessId, serviceOrder, details);
        }
        else if (isDedicatedResourcePoolApplication(serviceOrder, notSkip, details)) {
            ResMaPoolVO resMaPool = maRemoteService.getResMaPoolById(serviceOrder.getClusterId());
            if (isDeptrainPoolCreationNotPendingOrError(serviceOrder, details, resMaPool)) {
                //MA资源订购审批,申请类型是开发训练的需要走新流程
                resMaHandle(task, processDefinitionName, serviceOrder, details.get(0));
            }
        }
        else if (isDedicatedResourcePoolDeptrainRelease(serviceOrder, notSkip, details)) {
            log.info("MA专属退订开始");
            //删除专属资源池
            ResMaPoolVO resMaPool = maRemoteService.getResMaPoolById(serviceOrder.getClusterId());
            if (resMaPool.getAutoVersion() == 2) {
                Criteria criteria = new Criteria();
                criteria.put("cluster_id", serviceOrder.getClusterId());
                criteria.put("product_type", ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode());
                List<SfProductResource> sfProductResources = sfProductResourceMapper.selectByParams(criteria);
                if (sfProductResources.size() > 0 &&
                        ApplyTypeEnum.MA_VERSION_DEPTRAIN.getType().
                                equals(String.valueOf(sfProductResources.get(0).getMaVersion()))) {
                    //获取共享节点失败,更新共享资源池失败，删除成功的不需要走该流程
                    if (!MaPoolProessPhase.GETCOMMONPOOLNODEERROR.equals(resMaPool.getProessPhase()) &&
                            !MaPoolProessPhase.RELEASECOMMONPOOLNODEERROR.equals(resMaPool.getProessPhase())
                            && !MaPoolProessPhase.DELETEINGDRPPOOL.equals(resMaPool.getProessPhase())) {
                        log.info("AI专属退订预审批");
                        AuthUtil.replaceUserToInvoke(
                                () -> delMaDrp(task, processDefinitionName, serviceOrder, serviceOrder.getClusterId(), resMaPool, details.get(0)),
                                serviceOrder.getOwnerId());
                    }
                }
            }
        }
        else if (isDeptrainPoolUpgradeOrDegrade(serviceOrder, notSkip, details)) {
            this.approvalDrp(serviceOrder, details.get(0));
        }
        else if (ProductCodeEnum.isFederationProduct(serviceType)) {
            this.validCost(serviceOrder, details.get(0));
            // 判断内置是否是最后一次审批
            SfServiceProcessRela processRela = sfServiceProcessRelaMapper.selectByServiceCode(serviceType);
            if (processRela != null) {
                String taskName = task.getName();
                // 获取流程审批最后节点名称
                String lastNodeName = processNodeMapper.selectLastNodeName(processRela.getProcessId());
                log.info("ServiceOrderServiceImpl.approveServiceOrder taskName: {}, lastNodeName:{}", taskName, lastNodeName);
                if (!taskName.equals(lastNodeName)) {
                    return;
                }
            }
            ServiceOrderDetail detail = details.get(0);
            cn.hutool.json.JSONObject serviceConfig = JSONUtil.parseObj(detail.getServiceConfig());

            SfProductResource sfProductResource = sfProductResourceMapper.selectByPrimaryKey(
                    serviceConfig.getLong("productResourceId"));
            if (Objects.isNull(sfProductResource)) {
                throw new BizException("审批失败，资源缺失。");
            }
            Criteria criteria = new Criteria();
            criteria.put("orderSn", serviceOrder.getOrderSn());
            List<ServiceOrderPriceDetail> priceDetails = serviceOrderPriceDetailMapper.selectByParams(criteria);

            AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
            // 页面集成服务 购买添加产品权限，退订取消产品权限
            if (OrderType.APPLY.equals(serviceOrder.getType())) {

                // 获取租户 orgId
                User user = userMapper.selectByPrimaryKey(serviceOrder.getOwnerId());
                String huaweiUrl = userService.getHuaweiUrl(user.getOrgSid(), sfProductResource.getCloudEnvId(), sfProductResource.getProductType());
                AuthUtil.replaceUserToInvoke(
                        () ->   iamPermissionService.addProductPolicy(serviceType, serviceOrder.getOwnerId()),
                        serviceOrder.getOwnerId());

                ResFederationInst updateInst = new ResFederationInst();
                updateInst.setResourceId(sfProductResource.getId() + "");
                updateInst.setStatus(FederationStatusConstants.AVAILABLE);
                updateInst.setFederationUrl(huaweiUrl);
                updateInst.setStartTime(new Date());
                updateInst.setUpdatedDt(new Date());
                updateInst.setUpdatedBy(authUserInfo.getAccount());

                if (CollectionUtils.isNotEmpty(priceDetails)) {
                    updateInst.setEndTime(priceDetails.get(0).getEndTime());
                }
                resFederationInstMapper.updateResFederationInst(updateInst);
            }
            else if (OrderType.RELEASE.equals(serviceOrder.getType())) {
                AuthUtil.replaceUserToInvoke(
                        () ->       iamPermissionService.removeProductPolicy(serviceType, serviceOrder.getOwnerId()),
                        serviceOrder.getOwnerId());
            }else if (OrderType.RENEW.equals(serviceOrder.getType())) {
                ResFederationInst updateInst = new ResFederationInst();
                updateInst.setResourceId(sfProductResource.getId() + "");
                updateInst.setStatus(FederationStatusConstants.AVAILABLE);
                updateInst.setUpdatedDt(new Date());
                updateInst.setUpdatedBy(authUserInfo.getAccount());
                if (CollectionUtils.isNotEmpty(priceDetails)) {
                    updateInst.setEndTime(priceDetails.stream().max(Comparator.comparing(ServiceOrderPriceDetail::getEndTime)).get().getEndTime());
                }
                resFederationInstMapper.updateResFederationInst(updateInst);
            }
            // 出账
            approved(serviceOrder, detail, resourceInfo,
                     Convert.toBigDecimal(detail.getQuantity(), BigDecimal.ONE), false,null);
            serviceOrder.setStatus(OrderStatus.COMPLETED);
            serviceOrderService.updateByPrimaryKeySelective(serviceOrder);
        }
        else if (ProductCodeEnum.AS_GROUP.getProductCode().equals(serviceType)) {
            SfServiceProcessRela processRela = sfServiceProcessRelaMapper.selectByServiceCode(serviceType);
            if (processRela != null) {
                String taskName = task.getName();
                // 获取流程审批最后节点名称
                String lastNodeName = processNodeMapper.selectLastNodeName(processRela.getProcessId());
                log.info("ServiceOrderServiceImpl.approveServiceOrder taskName: {}, lastNodeName:{}", taskName, lastNodeName);
                if (!taskName.equals(lastNodeName)) {
                    return;
                }
            }

            ServiceOrderDetail detail = details.get(0);
            cn.hutool.json.JSONObject serviceConfig = JSONUtil.parseObj(detail.getServiceConfig());

            SfProductResource sfProductResource = sfProductResourceMapper.selectByPrimaryKey(
                    serviceConfig.getLong("productResourceId"));
            if (Objects.isNull(sfProductResource)) {
                throw new BizException("审批失败，资源缺失。");
            }
            if (OrderType.APPLY.equals(serviceOrder.getType())) {
                ServiceOrder order = serviceOrderMapper.selectByPrimaryKey(serviceOrder.getId());
                ActionParam actionParam = JSON.parseObject(order.getResourceInfo(), ActionParam.class);
                actionParam.setResIds(Collections.singletonList(sfProductResource.getClusterId()));
                RestResult<Long> result = AuthUtil.replaceUserToInvoke(
                        () -> resourceDcFeignService.postCreateScalingGroup(actionParam),
                        order.getOwnerId());
                if (!result.getStatus()) {
                    throw new BizException(String.valueOf(result.getMessage()));
                }
            }else if (OrderType.RELEASE.equals(serviceOrder.getType())) {
                ActionParam actionParam = new ActionParam();
                actionParam.setAction("delete");
                actionParam.setCloudEnvId(sfProductResource.getCloudEnvId());
                actionParam.setOrgId(sfProductResource.getOrgSid());
                actionParam.setResTypeCode("AS-GROUP");
                HashMap<String, Object> map = new HashMap<>();
                map.put("id", sfProductResource.getClusterId());
                actionParam.setData(map);
                RestResult<Void> result = AuthUtil.replaceUserToInvoke(
                        () -> resourceDcFeignService.deleteScalingGroup(actionParam),
                        serviceOrder.getOwnerId());
                if (!result.getStatus()) {
                    throw new BizException(String.valueOf(result.getMessage()));
                }
            }

        }
        else if (ProductCodeEnum.isCmpApiProduct(serviceType)) {

            // 判断内置是否是最后一次审批
            SfServiceProcessRela processRela = sfServiceProcessRelaMapper.selectByServiceCode(serviceType);
            if (processRela != null) {
                String taskName = task.getName();
                // 获取流程审批最后节点名称
                String lastNodeName = processNodeMapper.selectLastNodeName(processRela.getProcessId());
                log.info("ServiceOrderServiceImpl.approveServiceOrder taskName: {}, lastNodeName:{}", taskName, lastNodeName);
                if (!taskName.equals(lastNodeName)) {
                    return;
                }
            }
            Object auditAccount = taskService.getVariable(task.getId(), "_audit_account");
            if (auditAccount != null) {
                serviceOrder.setUpdatedBy((String) auditAccount);
            }
            ServiceOrderDetail detail = CollectionUtil.getFirst(serviceOrder.getDetails());
            if (ObjectUtils.isEmpty(detail)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_607095064));
            }
            // 续订修改资源结束时间 ecs单独处理的
//            if (OrderType.RENEW.equals(serviceOrder.getType()) &&
//                    (ProductCodeEnum.EIP.getProductCode().equals(serviceType)
//                            || ProductCodeEnum.DISK.getProductCode().equals(serviceType)
//                            || ProductCodeEnum.ELB.getProductCode().equals(serviceType)
//                            || ProductCodeEnum.DCS.getProductCode().equals(serviceType)
//                    )) {
//                ResourceApiEnum apiEnum = ResourceApiEnum.getResourceApiEnumByCode(detail.getServiceType(), OrderType.RENEW);
//                if (apiEnum == null) {
//                    throw new BizException("资源【"+detail.getServiceType()+"】没有定义API ENUM");
//                }
//                this.issuedBottomRequest(apiEnum, serviceOrder, detail);
//            }
            if (ProductCodeEnum.ECS.getProductCode().equals(serviceType)) {
                this.ecsBizProcess(serviceOrder);
            } else {
                this.addResourceUuid(serviceOrder);
            }
            try {
                //审批出账
                approved(serviceOrder, detail, resourceInfo, Convert.toBigDecimal(detail.getQuantity(), BigDecimal.ONE), false, null);
            } catch (Exception e) {
                e.printStackTrace();
                User user = userService.selectByPrimaryKey(serviceOrder.getOwnerId());
                Map messageContent = new HashMap();
                messageContent.put("name", serviceOrder.getName());
                messageContent.put("action", OrderType.getOrderTypeName(serviceOrder.getType()));
                messageContent.put("applyUser", "运营管理员");
                messageContent.put("userAccount", user.getAccount());
                messageContent.put("auditUser", "运营管理员");
                messageContent.put("orderSn", serviceOrder.getOrderSn());
                messageContent.put("productCode", serviceOrder.getProductCode());
                messageContent.put("productName", serviceOrder.getProductName());
                messageContent.put("errMsg", "余额不足,请充值");
                MailNotificationMq mailNotificationMq = new MailNotificationMq();
                mailNotificationMq.setMsgId(NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_RES_AUDIT_ERROR);
                mailNotificationMq.setMails(sysConfigRemoteService.getSendMailUrl());
                mailNotificationMq.setMap(messageContent);
                rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT, mailNotificationMq);
                throw new BizException(e);
            }
        }

        processProduct(task, processDefinitionName, resourceInfo, serviceOrder);
    }

    private void createOceanStor(String businsessId, ServiceOrder serviceOrder, List<ServiceOrderDetail> details) {
        details.get(1).setServiceAttr("oceanstor");
        serviceOrderDetailMapper.updateByPrimaryKeySelective(details.get(1));
        JSONObject sfsDetail = JSON.parseObject(details.get(1).getServiceConfig());
        JSONObject sfsData = sfsDetail.getJSONObject("data").getJSONObject("sfs");
        if (Objects.isNull(sfsData)) {
            sfsData = sfsDetail.getJSONObject("data").getJSONObject(ProductCodeEnum.SFS.getProductCode());
        }

        ResShare resShare = new ResShare();
        resShare.setCloudEnvId(sfsDetail.getLong("cloudEnvId"));
        ResOceanstorShare resOceanstorShare = new ResOceanstorShare();
        resOceanstorShare.setId(resShare.getId());
        resOceanstorShare.setCloudEnvId(resShare.getCloudEnvId());
        SysConfig sfsAK = sysConfigService.selectByConfigKey("hpc.sfsAk");
        String data = CrytoUtilSimple.decrypt(sfsAK.getConfigValue());
        if (StringUtil.isNotBlank(data)) {
            sfsAK.setConfigValue(data);
        }
        resOceanstorShare.setAccessTo(sfsAK.getConfigValue());
        resOceanstorShare.setAccessLevel("rw");
        if ("DPC".equals(sfsType)) {
            String shareType = "HPC";
            if (StringUtils.isNotBlank(sfsData.getString("shareType"))) {
                shareType = sfsData.getString("shareType");
            }
            resOceanstorShare.setAccessType("user");
            resOceanstorShare.setShareType(shareType);
            resOceanstorShare.setShareProto("DPC");
        } else {
            resOceanstorShare.setShareProto("NFS");
        }
        resOceanstorShare.setType("SFS");
        resOceanstorShare.setName("HPC-SFS-" + serviceOrder.getOrgSid());
        resOceanstorShare.setCreatedOrgSid(serviceOrder.getOrgSid());
        resOceanstorShare.setOwnerId(serviceOrder.getOwnerId().toString());
        resOceanstorShare.setOrgSid(serviceOrder.getOrgSid());
        resOceanstorShare.setOwnerOrgName(
                BasicInfoUtil.getCurrentOrgInfo(serviceOrder.getOrgSid()).getLdapOu());
        Integer size = sfsData.getInteger("size");
        resOceanstorShare.setSize(size);
        resOceanstorShare.setServiceOrderId(Long.valueOf(businsessId));
        resOceanstorShare.setAvailabilityZone(sfsData.getString("availabilityZone"));
        resOceanstorShare.setStartTime(new Date());
        String qosConfig = PropertiesUtil.getProperty("hpc.sfs.qos.config");
        resOceanstorShare.setQosConfigJson(qosConfig);

        String tierConfigJson = PropertiesUtil.getProperty("hpc.sfs.tier.config");
        resOceanstorShare.setTierConfigJson(tierConfigJson);

        String tierFlag = PropertiesUtil.getProperty("hpc.sfs.tier.flg");
        resOceanstorShare.setTierFlag(tierFlag);
        resOceanstorShare.setChargeType(ChargeType.POST_PAID);

        this.checkUserTotal("SFS", size);

        log.info("ProcessServiceImpl.bizProcess resOceanstorShare: {}", JSON.toJSONString(resOceanstorShare));
        cn.com.cloudstar.rightcloud.common.pojo.RestResult result = shareRemoteService.createShareByOceanStor(resOceanstorShare);

        log.info("ProcessServiceImpl.bizProcess createShareResult: {}", JSON.toJSONString(result));

        // 将创建弹性文件回调时创建 ServiceOrderResourceRef 放在审批处
        if (ProductCodeEnum.HPC.getProductCode().equalsIgnoreCase(details.get(0).getServiceType()) && result.getStatus()) {
            ServiceOrderResourceRef resourceRef = new ServiceOrderResourceRef();
            resourceRef.setOrderDetailId(details.get(1).getId());
            resourceRef.setType("SFS");
            resourceRef.setResourceId(result.getData().toString());
            serviceOrderResourceRefMapper.insert(resourceRef);
        }
    }
    @Override
    public void checkUserTotal(String productCode, Integer size) {
        ShareConfig shareConfig = getShareConfig(productCode);
        RestResult result = feignService.usedSfsStorageTypeFeign();
        Long userCount = 0L;
        if (result != null && result.getData() != null) {
            Object data = result.getData();
            ResShareTypes resShareTypes = JSONUtil.toBean(JSONUtil.toJsonStr(data), ResShareTypes.class);
            //获取产品已使用量
            ResShareTypeUse resShareTypeUse = resShareTypes.getResShareTypeUseList()
                .stream()
                .filter(shareTypeUse -> productCode.equals(
                    shareTypeUse.getServiceType()))
                .findFirst()
                .orElseThrow(
                    () -> new BizException(WebUtil.getMessage(
                        BusinessMessageConstants.Sfs2Message.NON_SYSTEM_config)));
            log.info("系统支持多套配置存储-checkShareConfig-OUTPUT: [{}]", JSONUtil.toJsonStr(resShareTypeUse));
            userCount = resShareTypeUse.getUseCount();
        }
        //校验存储总容量 >=产品已使用量+当前申请的容量
        if (userCount + size > shareConfig.getSfsTotalCapacity()) {
            throw new BizException(WebUtil.getMessage(BusinessMessageConstants.Sfs2Message.OVER_MAX_SIZE_LIMIT));
        }
    }
    /**
     * 根据产品cod获取产品信息
     *
     * @param productCode 产品编码
     *
     * @return ShareConfig 产品配置
     */
    private ShareConfig getShareConfig(String productCode) {
        String configValue = PropertiesUtil.getProperty("hpc.sfs.base.config");
        //解析内置平台参数
        List<ShareConfig> shareConfigs = JSONObject.parseArray(configValue, ShareConfig.class);
        //取出对应产品的配置项
        return shareConfigs.stream().filter(share -> productCode.equals(share.getServiceType())).
            findFirst().orElse(null);
    }


    /**
     *  请求下发底层
     * @param apiEnum api
     * @param serviceOrder 订单详情
     */
    private void issuedBottomRequest(ResourceApiEnum apiEnum, ServiceOrder serviceOrder, ServiceOrderDetail detail) {
        cn.hutool.json.JSONObject serviceConfig = JSONUtil.parseObj(detail.getServiceConfig());
        String productResourceId = serviceConfig.getStr("productResourceId");
        if (StringUtils.isBlank(productResourceId)) {
            throw new BizException("审批失败，资源缺失。");
        }
        SfProductResource resources = sfProductResourceMapper.selectByPrimaryKey(Long.valueOf(productResourceId));
        if (resources == null) {
            throw new BizException("审批失败，资源缺失。");
        }

        Long clusterId = resources.getClusterId();
        RenewParam renewParam = new RenewParam();
        renewParam.setId(clusterId);
        renewParam.setPeriod(serviceConfig.getInt("period"));
        renewParam.setChargeType(serviceConfig.getStr("chargeType"));


        String resourceUrl = SpringUtil.getBean(FeignUrlConfig.class).getResourceUrl();
        String url = resourceUrl + apiEnum.getUri();
        String queryResult = cn.com.cloudstar.rightcloud.common.util.RestTemplateUtil.sendHttpRequest(url, Maps.newHashMap(), apiEnum.getHttpMethod(),
                JSONUtil.toJsonStr(renewParam), serviceOrder.getOwnerId(), serviceOrder.getOrgSid());
        RightCloudResult result = JSONUtil.toBean(queryResult, RightCloudResult.class);
        if (!result.isSuccess()) {
            throw new BizException(result.getMessage());
        }

        serviceOrder.setStatus(OrderStatus.AUDITED);
        serviceOrderMapper.updateByPrimaryKeySelective(serviceOrder);
    }








    private static boolean isDeptrainPoolUpgradeOrDegrade(ServiceOrder serviceOrder, boolean notSkip, List<ServiceOrderDetail> details) {
        if (!notSkipMaDRP(notSkip, details)) {
            return false;
        }
        if (!isUpgradeOrDegrade(serviceOrder)) {
            return false;
        }
        return  ApplyTypeEnum.DEPTRAIN.getType().equals(details.get(0).getApplyType())
            && Objects.nonNull(serviceOrder.getClusterId());
    }

    private static boolean isUpgradeOrDegrade(ServiceOrder serviceOrder) {
        return OrderType.UPGRADE.equals(serviceOrder.getType()) || OrderType.DEGRADE.equals(serviceOrder.getType());
    }

    private static boolean isDedicatedResourcePoolDeptrainRelease(ServiceOrder serviceOrder, boolean notSkip, List<ServiceOrderDetail> details) {
        if (!notSkipMaDRP(notSkip, details)) {
            return false;
        }
        if (!OrderType.RELEASE.equals(serviceOrder.getType())) {
            return false;
        }
        return ApplyTypeEnum.DEPTRAIN.getType().equals(details.get(0).getApplyType())
            && Objects.nonNull(serviceOrder.getClusterId());
    }

    private static boolean notSkipMaDRP(boolean notSkip, List<ServiceOrderDetail> details) {
        return notSkip && ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode().equals(details.get(0).getServiceType());
    }

    private static boolean isDedicatedResourcePoolApplication(ServiceOrder serviceOrder, boolean notSkip, List<ServiceOrderDetail> details) {
        return notSkipMaDRP(notSkip, details) && OrderType.APPLY.equals(serviceOrder.getType());
    }

    private static boolean isDeptrainPoolCreationNotPendingOrError(ServiceOrder serviceOrder, List<ServiceOrderDetail> details, ResMaPoolVO resMaPool) {
        if (!OrderType.APPLY.equals(serviceOrder.getType())) {
            return false;
        }
        if (!ApplyTypeEnum.DEPTRAIN.getType().equals(details.get(0).getApplyType())) {
            return false;
        }
        boolean anyMatch = Stream.of(MaPoolProessPhase.STARTCREATEINGNETWORKPEDING, MaPoolProessPhase.CREATEINGPOOLError, MaPoolProessPhase.CREATEINGPOOL)
            .noneMatch(phase -> phase.equals(resMaPool.getProessPhase()));
        return anyMatch;
    }

    /**
     * MA预审批流程
     *
     * @param task
     * @param processInstance
     * @param serviceOrder
     * @param detail
     */
    private void resMaHandle(Task task, String processInstance,
                             ServiceOrder serviceOrder,
                             ServiceOrderDetail detail) {
        // 临时去掉 && "MA专属资源池服务开通".equals(processInstance)
        if ("运营管理员预审批".equals(task.getName())) {
            SfProductResource sfProductResource = getSfProductResource(
                    detail.getId());
            ResMaPoolVO resMaPool = maRemoteService.getResMaPoolById(sfProductResource.getClusterId());

            //检查账户余额
            Boolean isCheck = chekcPrice(serviceOrder, detail);
            if (!isCheck) {
                Map messageContent = new HashMap();
                messageContent.put("applyUser", "运营管理员");
                messageContent.put("orderSn", serviceOrder.getOrderSn());
                messageContent.put("productName", serviceOrder.getProductName());
                messageContent.put("phase", "释放共享资源池");
                messageContent.put("errMsg", "释放共享资源池失败,账户余额不足");
                log.info("HPC专属资源池开通发送错误邮件,{}", messageContent);

                MailNotificationMq baseNotificationMqBean = new MailNotificationMq();
                baseNotificationMqBean.setMsgId(NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_AI_DRP_ERROR);
                baseNotificationMqBean.setMails(sysConfigRemoteService.getSendMailUrl());
                baseNotificationMqBean.setMap(messageContent);
                rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT, baseNotificationMqBean);

                ResMaPoolVO insertMaDrpResource = new ResMaPoolVO();
                insertMaDrpResource.setId(sfProductResource.getClusterId());
                insertMaDrpResource.setProessPhase(MaPoolProessPhase.RELEASECOMMONPOOLNODEERROR);
                insertMaDrpResource.setErrorInfo("释放共享资源池,账户余额不足,审核失败");
                maRemoteService.updateByPrimaryKeySelective(insertMaDrpResource);
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1955839460));
            }


            log.info("resMaPool...,{}", resMaPool.toString());
            String proessPhase = "";
            //状态为获取共享节点中
            //获取共享资源池节点
            if (!MaPoolProessPhase.STARTCREATEINGNETWORKERROR.equals(resMaPool.getProessPhase()) &&
                    !MaPoolProessPhase.ROLLBACKERROR.equals(resMaPool.getProessPhase())) {
                BaseResult baseResult = AuthUtil.replaceUserToInvoke(
                        () -> resMaPoolsService.getCommonPoolNode(serviceOrder,
                                                                  serviceOrder.getClusterId(),
                                                                  OrderType.APPLY),
                        serviceOrder.getOwnerId());

                if (!baseResult.isSuccess()) {
                    Map messageContent = new HashMap();
                    messageContent.put("applyUser", "运营管理员");
                    messageContent.put("orderSn", serviceOrder.getOrderSn());
                    messageContent.put("productName", serviceOrder.getProductName());
                    messageContent.put("phase", "释放共享资源池");
                    messageContent.put("errMsg", "节点状态异常，请咨询运维管理员");

                    MailNotificationMq baseNotificationMqBean = new MailNotificationMq();
                    baseNotificationMqBean.setMsgId(NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_AI_DRP_ERROR);
                    baseNotificationMqBean.setMails(sysConfigRemoteService.getSendMailUrl());
                    baseNotificationMqBean.setMap(messageContent);
                    rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT, baseNotificationMqBean);


                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2127464660));
                }
                proessPhase = MaPoolProessPhase.RELEASECOMMONPOOLNODE;
                Integer amount = resMaPool.getMaxCount();
                //共享资源池运行节点数
                Integer runCount = resMaPoolsService.getCommonRunCount(baseResult);
                String shareNodesMinCount = sysConfigService.getValueByConfigKey("iam.auto.node.min.num");
                if (Objects.nonNull(shareNodesMinCount)) {
                    log.info("共享资源池最小节点数必须满足：[{}]，当前节点数：[{}]", shareNodesMinCount, runCount);
                    if (runCount <= Integer.parseInt(shareNodesMinCount)) {
                        throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_168554852) + shareNodesMinCount + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_65281));
                    }
                }
                if (amount - runCount > 0) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_354563552) + amount + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_931215074));
                }
                int availableCount = resMaPoolsService.getCommonAvailableCount(serviceOrder.getClusterId());
                if (amount - availableCount > 0) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_354563552) + amount + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1934277331));
                }
                Integer desiredNodeCount = runCount - amount;
                log.info("开通专属资源池归还的节点数,{},...{}", amount, runCount);
                //释放共享资源池节点
                if (MaPoolProessPhase.RELEASECOMMONPOOLNODE.equals(proessPhase) ||
                        MaPoolProessPhase.RELEASECOMMONPOOLNODEERROR.equals(resMaPool.getProessPhase())) {
                    BaseResult maSharePoolsUpdateResult = AuthUtil.replaceUserToInvoke(
                            () -> resMaPoolsService.releaseCommonPoolNode(baseResult, serviceOrder,
                                                                          sfProductResource.getClusterId(),
                                                                          OrderType.APPLY, desiredNodeCount, false),
                            serviceOrder.getOwnerId());
                    if (!maSharePoolsUpdateResult.isSuccess()) {
                        Map sendAddr = new HashMap();
                        Map messageContent = new HashMap();
                        messageContent.put("applyUser", "运营管理员");
                        messageContent.put("orderSn", serviceOrder.getOrderSn());
                        messageContent.put("productName", serviceOrder.getProductName());
                        messageContent.put("phase", "释放共享资源池");
                        messageContent.put("errMsg", "节点状态异常，请咨询运维管理员");

                        MailNotificationMq baseNotificationMqBean = new MailNotificationMq();
                        baseNotificationMqBean.setMsgId(NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_AI_DRP_ERROR);
                        baseNotificationMqBean.setMails(sysConfigRemoteService.getSendMailUrl());
                        baseNotificationMqBean.setMap(messageContent);
                        rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT, baseNotificationMqBean);
                        throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1225363455));
                    }
                }
            }
            //预审批处理
            //校验当前租户是否存在网络信息
            ResMaNetWorkVo maNetworksCreates = checkUserHasNetWork(serviceOrder);

            Long aLong = Optional.ofNullable(maNetworksCreates.getId()).orElse(null);
            log.info("along...,{}", aLong);
            if (Objects.isNull(aLong)) {
                //创建网络
                ResMaNetWorkVo finalMaNetworksCreates = maNetworksCreates;
                maNetworksCreates = AuthUtil.replaceUserToInvoke(
                        () -> createNetWork(serviceOrder, sfProductResource, finalMaNetworksCreates), serviceOrder.getOwnerId());
                if (MaPoolProessPhase.STARTCREATEINGNETWORKERROR.equals(maNetworksCreates.getProessPhase())) {
                    Map messageContent = new HashMap();
                    messageContent.put("applyUser", "运营管理员");
                    messageContent.put("orderSn", serviceOrder.getOrderSn());
                    messageContent.put("productName", serviceOrder.getProductName());
                    messageContent.put("phase", "创建网络");
                    messageContent.put("errMsg", "创建网络信息失败,参数校验失败");
                    log.info("HPC专属资源池开通发送错误邮件,{}", messageContent);
                    MailNotificationMq baseNotificationMqBean = new MailNotificationMq();
                    baseNotificationMqBean.setMsgId(NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_AI_DRP_ERROR);
                    baseNotificationMqBean.setMails(sysConfigRemoteService.getSendMailUrl());
                    baseNotificationMqBean.setMap(messageContent);
                    rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT, baseNotificationMqBean);
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1428622228));
                }
            }
            log.info("创建的网络信息,{}", maNetworksCreates.toString());
        }
    }


    /**
     * 环境ID取得
     */
    private Long cloudEnvId() {
        CloudEnvParams cloudEnvParams = new CloudEnvParams();
        cloudEnvParams.setCloudEnvType("HCSO");
        List<CloudEnv> cloudEnvs = cloudEnvRemoteService.selectByParams(cloudEnvParams);
        return Objects.nonNull(cloudEnvs) && Objects.nonNull(cloudEnvs.get(0)) ? cloudEnvs.get(0).getId() : 0;
    }


    /**
     * 校验当前租户是否绑定了网络信息
     *
     * @param serviceOrder
     * @return ResMaNetWorkVo
     */
    private ResMaNetWorkVo checkUserHasNetWork(ServiceOrder serviceOrder) {
        List<cn.com.cloudstar.rightcloud.core.pojo.dto.user.User> account = userService.findByAccount(serviceOrder.getCreatedBy());
        ResMaNetWorkVo maNetworksCreates = new ResMaNetWorkVo();
        if (account.size() > 0) {
            maNetworksCreates = maRemoteService.getMaNetWork(account.get(0).getUserSid());
        }
        return maNetworksCreates;
    }


    /**
     * 检查账户余额
     *
     * @param serviceOrder
     * @param detail
     * @return Boolean
     */
    private Boolean chekcPrice(ServiceOrder serviceOrder,
                               ServiceOrderDetail detail) {
        // 校验余额
        BizBillingAccount bizBillingAccount = bizBillingAccountMapper
                .selectByPrimaryKey(serviceOrder.getBizBillingAccountId());
        BigDecimal finalCost = detail.getAmount();
        log.info("当前支付金额...,{}", finalCost);
        if (NumberUtil.isGreater(finalCost, BigDecimal.ZERO)) {
            if (NumberUtil.isGreaterOrEqual(
                    Convert.toBigDecimal(bizBillingAccount.getBalanceCash(), BigDecimal.ZERO),
                    finalCost)) {

            } else {
                BigDecimal overCost = NumberUtil.sub(finalCost, bizBillingAccount.getBalanceCash());
                if (NumberUtil.isGreaterOrEqual(
                        Convert.toBigDecimal(bizBillingAccount.getBalance(), BigDecimal.ZERO),
                        overCost)) {

                } else {
                    overCost = NumberUtil.sub(overCost, bizBillingAccount.getBalance());

                    if (NumberUtil.isGreaterOrEqual(
                            Convert.toBigDecimal(bizBillingAccount.getCreditLine(), BigDecimal.ZERO),
                            overCost)) {
                    } else {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    /**
     * 创建网络资源
     *
     * @param serviceOrder
     * @param sfProductResource
     * @param resMaNetWorkVo
     */
    private ResMaNetWorkVo createNetWork(
            ServiceOrder serviceOrder,
            SfProductResource sfProductResource,
            ResMaNetWorkVo resMaNetWorkVo) {
        //判断当前租户是否存在网络，不存在则需要创建网络
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        String proessPhase = MaPoolProessPhase.RELEASECOMMONPOOLNODE;
        ResMaPoolVO insertMaDrpResource = new ResMaPoolVO();
        MANetworksCidrsQuery networksCidrsQuery = CloudClientFactory.buildMQBean(cloudEnvId(), MANetworksCidrsQuery.class);
        MANetworkCidrsResult mANetworkCidrsResult = maRemoteService.maNetWorksCidrs(networksCidrsQuery);
        log.info("MA查询网络cdir,{}",JSONUtil.toJsonStr(mANetworkCidrsResult));
        String cdir = "";
        if(Objects.isNull(mANetworkCidrsResult.getNetworkCidrs())){
            insertMaDrpResource.setId(sfProductResource.getClusterId());
            insertMaDrpResource.setProessPhase(MaPoolProessPhase.STARTCREATEINGNETWORKERROR);
            insertMaDrpResource.setErrorInfo("创建网络信息失败,查询CDIR失败");
            log.info("获取cdir信息,{}", insertMaDrpResource.getErrorInfo());
            maRemoteService.updateByPrimaryKeySelective(insertMaDrpResource);
            proessPhase = MaPoolProessPhase.STARTCREATEINGNETWORKERROR;
            resMaNetWorkVo.setProessPhase(proessPhase);
            return resMaNetWorkVo;
        }
        if (mANetworkCidrsResult.getNetworkCidrs().size() == 0) {
            cdir = "192.168.20.0/24";
        } else {
            cdir = mANetworkCidrsResult.getNetworkCidrs().get(0);
        }
        //配置网络
        MANetworksCreateResult maNetworksCreateResult = new MANetworksCreateResult();
        MANetworksCreate networksCreate = CloudClientFactory.buildMQBean(cloudEnvId(), MANetworksCreate.class);
        networksCreate.setApiVersion("v1");
        networksCreate.setKind("Network");
        NetworkSpec networkSpec = new NetworkSpec();
        //创建Cdir
        networkSpec.setCidr(cdir);
        networksCreate.setSpec(networkSpec);
        NetworkMetadata networkMetadata = new NetworkMetadata();
        NetworkMetaLabels networkMetaLabels = new NetworkMetaLabels();
        networkMetaLabels.setName(serviceOrder.getName().toLowerCase());
        networkMetadata.setLabels(networkMetaLabels);
        networksCreate.setMetadata(networkMetadata);
        Base clearPass = BaseClearPassUtil.clearPass(networksCreate);

        maNetworksCreateResult = maRemoteService.maNetWorksCreate(networksCreate);
        log.info("创建MA资源网络的返回参数,{}", JSONUtil.toJsonStr(maNetworksCreateResult));
        if (Objects.isNull(maNetworksCreateResult.isSuccess())) {
            maNetworksCreateResult.setSuccess(false);
        }
        insertMaDrpResource.setId(sfProductResource.getClusterId());
        if (!maNetworksCreateResult.isSuccess()) {
            insertMaDrpResource.setProessPhase(MaPoolProessPhase.STARTCREATEINGNETWORKERROR);
            insertMaDrpResource.setErrorInfo("创建网络信息失败,参数校验失败");
            log.info("获取共享资源池信息,{}", insertMaDrpResource.getErrorInfo());
            maRemoteService.updateByPrimaryKeySelective(insertMaDrpResource);
            proessPhase = MaPoolProessPhase.STARTCREATEINGNETWORKERROR;
            resMaNetWorkVo.setProessPhase(proessPhase);
            return resMaNetWorkVo;
        }
        //创建网络节点信息 hcso系统生成的网络ID
        resMaNetWorkVo.setName(maNetworksCreateResult.getMetadata().getName());
        resMaNetWorkVo.setCidr(maNetworksCreateResult.getSpec().getCidr());
        resMaNetWorkVo.setPhase(maNetworksCreateResult.getStatus().getPhase());
        List<cn.com.cloudstar.rightcloud.core.pojo.dto.user.User> account = userService.findByAccount(serviceOrder.getCreatedBy());
        if (account.size() > 0) {
            resMaNetWorkVo.setOwnerId(String.valueOf(account.get(0).getUserSid()));
        }
        resMaNetWorkVo.setUpdatedBy(serviceOrder.getCreatedBy());
        resMaNetWorkVo.setCreatedBy(serviceOrder.getCreatedBy());
        resMaNetWorkVo.setCreatedDt(new Date());
        resMaNetWorkVo.setUpdatedDt(new Date());
        resMaNetWorkVo = maRemoteService.insertMaNetWork(resMaNetWorkVo);
        proessPhase = MaPoolProessPhase.STARTCREATEINGNETWORK;
        resMaNetWorkVo.setProessPhase(proessPhase);
        //创建网络完成以后修改节点状态为释放共享资源池等待中
        insertMaDrpResource.setProessPhase(MaPoolProessPhase.RELEASECOMMONPOOLNODEPEDING);
        insertMaDrpResource.setErrorInfo("");
        maRemoteService.updateByPrimaryKeySelective(insertMaDrpResource);
        return resMaNetWorkVo;
    }

    private void processProduct(Task task, String processDefinitionName, ResourceInfo resourceInfo,
                                ServiceOrder serviceOrder) {
        User user = userService.selectByPrimaryKey(serviceOrder.getOwnerId());
        ServiceOrderDetail detail = CollectionUtil.getFirst(serviceOrder.getDetails());
        if (ObjectUtils.isEmpty(detail)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_607095064));
        }
        boolean isServiceApplyAudit = isServiceApplyAudit(task, processDefinitionName);
        if (isServiceApplyAudit && ProductCodeEnum.isInnerProduct(detail.getServiceType())
                && !ProductCodeEnum.AS_GROUP.getProductCode().equals(detail.getServiceType())) {
            //紧急解决问题，之后再优化
            Integer maDrpAutoVersion = getDrpAutoVersion(serviceOrder, detail);

            //hpc专属走得地方
            if (ProductCodeEnum.HPC_DRP.getProductCode().equals(detail.getServiceType())
                    && OrderType.APPLY.equals(serviceOrder.getType())) {

                hpcDrpPoolActi(serviceOrder, resourceInfo);
            }
            else if (OrderType.RELEASE.equals(serviceOrder.getType()) &&
                    ApplyTypeEnum.DEPTRAIN.getType().equals(detail.getApplyType()) &&
                    maDrpAutoVersion == 2 &&
                    ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode().equals(detail.getServiceType())) {
                //MA资源退订审批,申请类型是开发训练的需要走新流程
                log.info("MA退订流程..开始");
                Long clusterId = detail.getClusterId();
                ResMaPoolVO resMaPool = maRemoteService.getResMaPoolById(clusterId);
                if (!MaPoolProessPhase.DELETEINGDRPPOOLERROR.equals(resMaPool.getProessPhase())
                        && !MaPoolProessPhase.ERRORINFO.equals(resMaPool.getErrorInfo())) {
                    String poolType = resMaPool.getType();
                   if (StringUtils.isNotBlank(poolType) && MaBasicStatus.LOGICAL.equals(poolType)) {
                        ResMaPoolVO insertMaDrpResource = new ResMaPoolVO();
                        insertMaDrpResource.setId(clusterId);
                        insertMaDrpResource.setStatus(OrderType.DELETEING);
                        insertMaDrpResource.setProessPhase(MaPoolProessPhase.DELETEINGDRPPOOL);
                        maRemoteService.updateByPrimaryKeySelective(insertMaDrpResource);
                    }
                    else {
                        AuthUtil.replaceUserToInvoke(
                                () -> resMaHandleRelease(serviceOrder, detail, resMaPool),
                                serviceOrder.getOwnerId());
                    }


                    String serviceConfig = detail.getServiceConfig();
                    JSONObject jsonObjectOld = JSON.parseObject(serviceConfig);
                    JSONObject productConfigDesc = jsonObjectOld.getJSONObject("productConfigDesc");
                    com.alibaba.fastjson.JSONArray currentConfigDesc = productConfigDesc.getJSONArray(
                            "currentConfigDesc");
                    for (Object object : currentConfigDesc) {
                        JSONObject jsonObject = (JSONObject) object;
                        Object value = jsonObject.get("value");
                        if (Objects.isNull(value)) {
                            continue;
                        } else if (Objects.equals(jsonObject.get("attrKey"), "count")) {
                            jsonObject.put("value", resMaPool.getAvailableCount() + "/" + resMaPool.getMaxCount());
                        }
                    }
                    productConfigDesc.put("currentConfigDesc", JSONObject.toJSONString(currentConfigDesc));
                    jsonObjectOld.put("productConfigDesc", productConfigDesc);

                    detail.setServiceConfig(jsonObjectOld.toString());
                    serviceOrderDetailMapper.updateByPrimaryKeySelective(detail);
                    //余额不足出账错误,审批流程回滚操作
                    try {
                        approved(serviceOrder, detail, resourceInfo,
                                 Convert.toBigDecimal(detail.getQuantity(), BigDecimal.ONE), false,null);
                        approvedPushMsg(serviceOrder, detail);
                    } catch (Exception e) {
                        ResMaPoolVO insertMaDrpResource = new ResMaPoolVO();
                        insertMaDrpResource.setId(clusterId);
                        insertMaDrpResource.setStatus(OrderType.RELEASE);
                        insertMaDrpResource.setUpdatedDt(new Date());
                        insertMaDrpResource.setProessPhase(MaPoolProessPhase.RELEASECOMMONPOOLNODEERROR);
                        insertMaDrpResource.setErrorInfo(MaPoolProessPhase.ERRORINFO);
                        log.info("专属资源池退订失败,{}",e.getMessage());
                        maRemoteService.updateByPrimaryKeySelective(insertMaDrpResource);

                        Map messageContent = new HashMap();
                        messageContent.put("applyUser", "运营管理员");
                        messageContent.put("orderSn", serviceOrder.getOrderSn());
                        messageContent.put("productName", serviceOrder.getProductName());
                        messageContent.put("phase", "释放共享资源池");
                        messageContent.put("errMsg", "专属资源池退订失败,余额不足,请充值");
                        log.info("MA专属资源池开通发送错误邮件");
                        MailNotificationMq baseNotificationMqBean = new MailNotificationMq();
                        baseNotificationMqBean.setMsgId(NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_AI_DRP_ERROR);
                        baseNotificationMqBean.setMails(sysConfigRemoteService.getSendMailUrl());
                        baseNotificationMqBean.setMap(messageContent);
                                rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT, baseNotificationMqBean);
                        throw new BizException(e);
                    }
                    log.info("AI专属资源池退订成功，消息通知");
                    executorService.execute(() -> {
                        HashMap<String, String> messageContent = new HashMap<>();
                        messageContent.put("productName", serviceOrder.getProductName());
                        messageContent.put("userAccount",user.getAccount());
                        messageContent.put("orderSn",serviceOrder.getOrderSn());
                        messageContent.put("poolName", serviceOrder.getName());
                        try {
                            Thread.sleep(10000);
                        } catch (InterruptedException e) {
                            log.error("exception message:", e);
                            Thread.currentThread().interrupt();
                        }

                        //给租户和管理员发送信息
                        sysOssMessageService.sendOssMessage(serviceOrder.getOwnerId(),messageContent,NotificationConsts.ConsoleMsg.ProductMsg.TENANT_AI_PRIVATE_UNSUBSCRIBE,NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_AI_PRIVATE_UNSUBSCRIBE,serviceOrder.getEntityId());
                    });

                } else {
                    ResMaPoolVO insertMaDrpResource = new ResMaPoolVO();
                    insertMaDrpResource.setId(clusterId);
                    insertMaDrpResource.setUpdatedDt(new Date());
                    try {
                        insertMaDrpResource.setStatus(OrderType.RELEASE);
                        insertMaDrpResource.setProessPhase(MaPoolProessPhase.RELEASECOMMONPOOLNODE);
                        insertMaDrpResource.setErrorInfo("");
                        maRemoteService.updateByPrimaryKeySelective(insertMaDrpResource);
                        approved(serviceOrder, detail, resourceInfo,
                                 Convert.toBigDecimal(detail.getQuantity(), BigDecimal.ONE), false,null);
                        approvedPushMsg(serviceOrder, detail);
                    } catch (Exception e) {
                        log.error("专属资源池退订失败, error: ", e);
                        insertMaDrpResource.setStatus(OrderType.RELEASE);
                        insertMaDrpResource.setProessPhase(MaPoolProessPhase.RELEASECOMMONPOOLNODEERROR);
                        insertMaDrpResource.setErrorInfo(MaPoolProessPhase.ERRORINFO);
                        maRemoteService.updateByPrimaryKeySelective(insertMaDrpResource);
                        Map messageContent = new HashMap();
                        messageContent.put("applyUser", "运营管理员");
                        messageContent.put("orderSn", serviceOrder.getOrderSn());
                        messageContent.put("productName", serviceOrder.getProductName());
                        messageContent.put("phase", "释放共享资源池");
                        messageContent.put("errMsg", "专属资源池退订失败,余额不足,请充值");
                        log.info("MA专属资源池开通发送错误邮件,{}", messageContent);

                        MailNotificationMq baseNotificationMqBean = new MailNotificationMq();
                        baseNotificationMqBean.setMsgId(NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_AI_DRP_ERROR);
                        baseNotificationMqBean.setMails(sysConfigRemoteService.getSendMailUrl());
                        baseNotificationMqBean.setMap(messageContent);
                        rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT, baseNotificationMqBean);
                        throw new BizException(e);
                    }
                }
            }
            else if (ApplyTypeEnum.DEPTRAIN.getType().equals(detail.getApplyType())
                    && maDrpAutoVersion == 2
                    && ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode().equals(detail.getServiceType())
                    && OrderType.APPLY.equals(serviceOrder.getType())) {
                ResMaPoolVO insertMaDrpResource = new ResMaPoolVO();
                insertMaDrpResource.setId(serviceOrder.getClusterId());
                insertMaDrpResource.setUpdatedDt(new Date());
                try {
                    insertMaDrpResource.setStatus(OrderType.AVAILABLE);
                    insertMaDrpResource.setProessPhase(MaPoolProessPhase.CREATEINGPOOL);
                    insertMaDrpResource.setErrorInfo("");
                    maRemoteService.updateByPrimaryKeySelective(insertMaDrpResource);
                    approved(serviceOrder, detail, resourceInfo,
                             Convert.toBigDecimal(detail.getQuantity(), BigDecimal.ONE), false,null);
                    approvedPushMsg(serviceOrder, detail);
                } catch (Exception e) {
                    insertMaDrpResource.setStatus(OrderType.APPLY);
                    insertMaDrpResource.setProessPhase(MaPoolProessPhase.CREATEINGPOOLERRORPRICE);
                    insertMaDrpResource.setErrorInfo(MaPoolProessPhase.ERRORINFO);
                    log.info("专属资源池开通出账失败,{}", "余额不足,请充值");
                    maRemoteService.updateByPrimaryKeySelective(insertMaDrpResource);

                    Map messageContent = new HashMap();
                    messageContent.put("applyUser", "运营管理员");
                    messageContent.put("orderSn", serviceOrder.getOrderSn());
                    messageContent.put("productName", serviceOrder.getProductName());
                    messageContent.put("phase", "MA专属资源池创建失败");
                    messageContent.put("errMsg", "专属资源池开通失败,余额不足,请充值");
                    log.info("HPC专属资源池开通发送错误邮件,{}", messageContent);
                    MailNotificationMq baseNotificationMqBean = new MailNotificationMq();
                    baseNotificationMqBean.setMsgId(NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_AI_DRP_ERROR);
                    baseNotificationMqBean.setMails(sysConfigRemoteService.getSendMailUrl());
                    baseNotificationMqBean.setMap(messageContent);
                    rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT, baseNotificationMqBean);
                    throw new BizException(e);
                }
            } else if (ProductCodeEnum.AI_BMS_QUOTA.getProductCode().equals(detail.getServiceType())) {
                Object auditAccount = taskService.getVariable(task.getId(), "_audit_account");
                if (auditAccount != null) {
                    serviceOrder.setUpdatedBy((String) auditAccount);
                }
                final ServiceOrder serviceOrder1 = serviceOrderMapper.selectByPrimaryKey(serviceOrder.getId());
                if(ObjectUtil.isEmpty(serviceOrder1)){
                    throw new BizException("订单不存在");
                }
                final String info = serviceOrder1.getResourceInfo();
                if (ObjectUtil.isEmpty(info)) {
                    throw new BizException("算力调度平台配额信息为空");
                }
                final JSONObject object = JSON.parseObject(info);
                final JSONObject data = object.getJSONObject("data");
                final String productName = data.getString("productName");
                final Long clusterId = data.getLong("clusterId");
                final com.alibaba.fastjson.JSONArray quotaDetails = data.getJSONArray("quotaDetails");
                BssQuotaReq req = new BssQuotaReq();
                req.setAccount(serviceOrder.getCreatedBy());
                req.setQuotaName(productName);
                req.setClusterId(clusterId);
                req.setQuotaDetails(BeanUtil.copyToList(quotaDetails, QuotaDetailsDto.class));
                if (OrderType.APPLY.equals(serviceOrder.getType())) {
                    req.setEvent("add");
                } else if (OrderType.MODIFY.equals(serviceOrder.getType())) {
                    req.setEvent("update");
                    final Long quotaId = data.getLong("quotaId");
                    req.setQuotaId(quotaId);
                    final String serviceConfig = detail.getServiceConfig();
                    JSONObject configJson = JSON.parseObject(serviceConfig);
                    //修改resource表
                    String productResourceId = configJson.getString("productResourceId");
                    ServiceOrderResourceRef serviceOrderResourceRef = new ServiceOrderResourceRef();
                    serviceOrderResourceRef.setOrderDetailId(detail.getId());
                    HashMap<String, Object> condition = new HashMap<>(2);
                    condition.put("resourceId", productResourceId);
                    condition.put("type", detail.getServiceType());
                    serviceOrderResourceRefMapper.updateByParamsSelective(serviceOrderResourceRef, condition);
                } else if (OrderType.RELEASE.equals(serviceOrder.getType())) {
                    req.setEvent("delete");
                    final Long quotaId = data.getLong("quotaId");
                    req.setQuotaId(quotaId);
                }
                final Long quotaId = cfnService.operateQuota(req);
                data.put("quotaId",quotaId);
                object.put("data",data);
                serviceOrder1.setResourceInfo(JSON.toJSONString(object));
                serviceOrderMapper.updateByPrimaryKeySelective(serviceOrder1);
                //审批出账
                approved(serviceOrder, detail, resourceInfo, Convert.toBigDecimal(detail.getQuantity(), BigDecimal.ONE),
                         false, null);
                approvedPushMsg(serviceOrder, detail);
            }else if (ProductCodeEnum.AI_BMS.getProductCode().equals(detail.getServiceType())) {
                Object auditAccount = taskService.getVariable(task.getId(), "_audit_account");
                if (auditAccount != null) {
                    serviceOrder.setUpdatedBy((String) auditAccount);
                }
                UpdatePoolUserReq req = new UpdatePoolUserReq();
                req.setAccount(serviceOrder.getCreatedBy());
                if (OrderType.APPLY.equals(serviceOrder.getType())) {
                    req.setEvent("add");
                } else if (OrderType.RELEASE.equals(serviceOrder.getType())) {
                    req.setEvent("delete");
                }
                cfnService.updatePoolUser(req);
                //审批出账
                approved(serviceOrder, detail, resourceInfo, Convert.toBigDecimal(detail.getQuantity(), BigDecimal.ONE),
                         false, null);
                approvedPushMsg(serviceOrder, detail);
            } else {
                Object auditAccount = taskService.getVariable(task.getId(), "_audit_account");
                if (auditAccount != null) {
                    serviceOrder.setUpdatedBy((String) auditAccount);
                }
                //审批出账
                approved(serviceOrder, detail, resourceInfo, Convert.toBigDecimal(detail.getQuantity(), BigDecimal.ONE),
                         false,null);
                approvedPushMsg(serviceOrder, detail);
            }

            // 发送MA专属申请成功消息
            if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode().equals(detail.getServiceType()) &&
                    OrderType.APPLY.equals(serviceOrder.getType())) {
                HashMap<String, String> messageContent = new HashMap<>();
                messageContent.put("productName", serviceOrder.getProductName());
                messageContent.put("userAccount", user.getAccount());
                messageContent.put("poolName", serviceOrder.getName());
                messageContent.put("orderSn", serviceOrder.getOrderSn());
                messageContent.put("user", user.getAccount());
                messageContent.put("startDate",  cn.hutool.core.date.DateUtil.format(detail.getStartTime(), DatePattern.NORM_DATETIME_PATTERN));
                messageContent.put("endDate", cn.hutool.core.date.DateUtil.format(detail.getEndTime(), DatePattern.NORM_DATETIME_PATTERN));
                //给租户发送
                BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
                baseNotificationMqBean.setMsgId(NotificationConsts.ConsoleMsg.ProductMsg.TENANT_AI_PRIVATE_ORDER);
                baseNotificationMqBean.getImsgUserIds().add(serviceOrder.getOwnerId());
                baseNotificationMqBean.setMap(messageContent);
                baseNotificationMqBean.setEntityId(serviceOrder.getEntityId());
                rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT, baseNotificationMqBean);

                // 给管理员发送
                List<cn.com.cloudstar.rightcloud.core.pojo.dto.user.User> userByDataScope = userMapper.findAdminstratorsByEntityId(serviceOrder.getEntityId());
                if (CollectionUtil.isNotEmpty(userByDataScope)) {
                    BaseNotificationMqBean adminBaseNotificationMqBean = new BaseNotificationMqBean();
                    adminBaseNotificationMqBean.setMsgId(NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_AI_PRIVATE_ORDER);
                    adminBaseNotificationMqBean.getImsgUserIds().addAll(userByDataScope.stream()
                            .map(cn.com.cloudstar.rightcloud.core.pojo.dto.user.User::getUserSid).collect(Collectors.toSet()));
                    adminBaseNotificationMqBean.setMap(messageContent);
                    adminBaseNotificationMqBean.setEntityId(serviceOrder.getEntityId());
                    rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT, adminBaseNotificationMqBean);
                }
            }
            else if (OrderType.RELEASE.equals(serviceOrder.getType()) &&
                    ApplyTypeEnum.DEPONLINE.getType().equals(detail.getApplyType()) &&
                    ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode().equals(detail.getServiceType())){
                // 发送MA部署上线专属退订成功消息
                HashMap<String, String> messageContent = new HashMap<>();
                messageContent.put("productName", serviceOrder.getProductName());
                messageContent.put("userAccount",user.getAccount());
                messageContent.put("poolName", serviceOrder.getName());
                messageContent.put("orderSn", serviceOrder.getOrderSn());
                //给租户和运营管理员发送信息
                sysOssMessageService.sendOssMessage(serviceOrder.getOwnerId(),messageContent,NotificationConsts.ConsoleMsg.ProductMsg.TENANT_AI_PRIVATE_UNSUBSCRIBE,NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_AI_PRIVATE_UNSUBSCRIBE,serviceOrder.getEntityId());
            }
        }
        else if (ProductCodeEnum.isCmpApiProduct(detail.getServiceType()) || ProductCodeEnum.isFederationProduct(detail.getServiceType()) || ProductCodeEnum.AS_GROUP.getProductCode().equals(detail.getServiceType())) {
            HashMap<String, String> messageContent = new HashMap<>();
            String tenantMsg = null;
            String bssMgtMsg = null;
            if (OrderType.APPLY.equals(serviceOrder.getType())) {
                if (ProductCodeEnum.isNoEndTimeProduct(detail.getServiceType())) {
                    tenantMsg = NotificationConsts.ConsoleMsg.ProductMsg.TENANT_RES_ORDER_NO_END_TIME;
                    bssMgtMsg = NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_RES_ORDER_NO_END_TIME;
                }else {
                    tenantMsg = NotificationConsts.ConsoleMsg.ProductMsg.TENANT_RES_ORDER;
                    bssMgtMsg = NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_RES_ORDER;
                    messageContent.put("endDate", cn.hutool.core.date.DateUtil.format(detail.getEndTime(), DatePattern.NORM_DATETIME_PATTERN));
                }

                messageContent.put("productName", serviceOrder.getProductName());
                messageContent.put("userAccount", user.getAccount());
                messageContent.put("poolName", serviceOrder.getName());
                messageContent.put("orderSn", serviceOrder.getOrderSn());
                messageContent.put("user", user.getAccount());
                messageContent.put("startDate", cn.hutool.core.date.DateUtil.format(detail.getStartTime(), DatePattern.NORM_DATETIME_PATTERN));
            }
            else if (OrderType.RELEASE.equals(serviceOrder.getType())){
                tenantMsg = NotificationConsts.ConsoleMsg.ProductMsg.TENANT_RES_UNSUBSCRIBE;
                bssMgtMsg = NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_RES_UNSUBSCRIBE;
                messageContent.put("productName", serviceOrder.getProductName());
                messageContent.put("userAccount",user.getAccount());
                messageContent.put("poolName", serviceOrder.getName());
                messageContent.put("orderSn", serviceOrder.getOrderSn());
            }
            else if (OrderType.RENEW.equals(serviceOrder.getType())){
                tenantMsg = NotificationConsts.ConsoleMsg.ProductMsg.TENANT_RES_RENEW;
                bssMgtMsg = NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_RES_RENEW;
                messageContent.put("user", serviceOrder.getOwnerRealName());
                messageContent.put("poolName", serviceOrder.getName());
                messageContent.put("productName", serviceOrder.getProductName());
                messageContent.put("orderSn", serviceOrder.getOrderSn());
                messageContent.put("startDate", cn.hutool.core.date.DateUtil.format(detail.getStartTime(), "yyyy年MM月dd日 HH:mm:ss"));
                messageContent.put("endDate", cn.hutool.core.date.DateUtil.format(detail.getEndTime(), "yyyy年MM月dd日 HH:mm:ss"));
            }
            else if (OrderType.MODIFY.equals(serviceOrder.getType())){
                tenantMsg = NotificationConsts.ConsoleMsg.ProductMsg.TENANT_RES_RENEW;
                bssMgtMsg = NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_RES_RENEW;
                messageContent.put("user", serviceOrder.getOwnerRealName());
                messageContent.put("poolName", serviceOrder.getName());
                messageContent.put("productName", serviceOrder.getProductName());
                messageContent.put("orderSn", serviceOrder.getOrderSn());
                messageContent.put("startDate", cn.hutool.core.date.DateUtil.format(detail.getStartTime(), "yyyy年MM月dd日 HH:mm:ss"));
                messageContent.put("endDate", cn.hutool.core.date.DateUtil.format(detail.getEndTime(), "yyyy年MM月dd日 HH:mm:ss"));
            }
            sysOssMessageService.sendOssMessage(serviceOrder.getOwnerId(), messageContent, tenantMsg, bssMgtMsg, serviceOrder.getEntityId());
        }
    }

    private static boolean isServiceApplyAudit(Task task, String processDefinitionName) {
        return "运营管理员审批".equals(task.getName()) && includeProcessDefinitionName(processDefinitionName);
    }

    private static boolean includeProcessDefinitionName(String processDefinitionName) {
        return Stream.of("资源手动审批", "服务开通", "服务退订", "HPC专属资源池服务开通", "HPC专属资源池服务扩容", "MA专属资源池服务开通", "MA专属资源池退订")
            .anyMatch(name -> name.equals(processDefinitionName));
    }

    private Integer getDrpAutoVersion(ServiceOrder serviceOrder, ServiceOrderDetail detail) {
        Integer autoVersion = 1;
        if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode().equals(detail.getServiceType())) {
            Long clusterId = null;
            if (OrderType.APPLY.equals(serviceOrder.getType())) {
                clusterId = serviceOrder.getClusterId();
            } else if (OrderType.RELEASE.equals(serviceOrder.getType())) {
                clusterId = detail.getClusterId();
            }
            if (clusterId != null) {
                ResMaPoolVO resMaPool = maRemoteService.getResMaPoolById(clusterId);
                if (resMaPool == null) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1473503497));
                }
                autoVersion = resMaPool.getAutoVersion();
            }
        }
        return autoVersion;
    }


    /**
     * HPC专属资源池激活
     *
     * @param serviceOrder
     * @param resourceInfo
     */
    private void hpcDrpPoolActi(ServiceOrder serviceOrder,
                                ResourceInfo resourceInfo) {
        //hpc未实现
    }

    private SfProductResource getSfProductResource(Long detailId) {
        Criteria criteria = new Criteria();
        criteria.put("orderDetailId", detailId);
        List<ServiceOrderResourceRef> serviceOrderResourceRefs = serviceOrderResourceRefMapper.selectByParams(criteria);
        if (!CollectionUtil.isEmpty(serviceOrderResourceRefs)) {
            Optional<ServiceOrderResourceRef> resourceRefOptional = serviceOrderResourceRefs.stream().findFirst();
            if (resourceRefOptional.isPresent()) {
                ServiceOrderResourceRef serviceOrderResourceRef = resourceRefOptional.get();
                String resourceId = serviceOrderResourceRef.getResourceId();
                SfProductResource sfProductResource = sfProductResourceMapper.selectByPrimaryKey(Long.valueOf(resourceId));
                return sfProductResource;
            }
        }
        return null;
    }

    public void insertAccountDeal(List<InstanceGaapCost> costs, ServiceOrder serviceOrder) {
        List<BizAccountDeal> deals = Lists.newArrayList();
        boolean releaseBeforeEndTime = releaseBeforeEndTime(serviceOrder);
        costs.forEach(cost -> {
            BizAccountDeal accountDeal = new BizAccountDeal();
            accountDeal.setCouponAmount(BigDecimal.ZERO);
            accountDeal.setFlowNo(NoUtil.generateNo(BillingConstants.DEAL_PREFIX));
            if (serviceOrder.getFinalCost().compareTo(BigDecimal.ZERO) >= 0) {
                accountDeal.setType(DealType.OUT);
            } else {
                accountDeal.setType(DealType.IN);
            }
            //去掉折扣和优惠券金额后的金额
            BigDecimal tradePrice = cost.getPretaxAmount();
            accountDeal.setTradeType(
                    tradePrice.compareTo(BigDecimal.ZERO) < 0 ? TradeType.REFUND : TradeType.PAY);
            accountDeal.setTradeNo(NoUtil.generateNo("ZD"));
            accountDeal.setEnvType(cost.getCloudEnvType());
            accountDeal.setEnvName(cost.getCloudEnvName());
            accountDeal.setOrderNo(serviceOrder.getOrderSn());
            accountDeal.setBillNo(cost.getBillNo());
            accountDeal.setRemark(cost.getDescription());
            accountDeal.setBillingCycle(
                    LocalDate.now().format(DateTimeFormatter.ofPattern(MONTH_PATTERN)));
            BizBillingAccount account = cost.getBizBillingAccount();
            accountDeal.setAccountSid(account.getId());
            accountDeal.setAccountName(account.getAccountName());

//            accountDeal.setDeductBalanceCash(cost.getCouponDiscount());
            accountDeal.setBalanceCash(cost.getCouponAmount());

            accountDeal.setOrgSid(account.getOrgSid());
            accountDeal.setUserSid(account.getId());
            accountDeal.setDealTime(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
            accountDeal.setBalance(account.getBalance());
            accountDeal.setBalanceCredit(account.getCreditLine());
            accountDeal.setBalanceCash(account.getBalanceCash());
            accountDeal.setEntityId(serviceOrder.getEntityId());
            accountDeal.setEntityName(serviceOrder.getEntityName());
            Optional.ofNullable(entityMapper.selectByPrimaryKey(accountDeal.getEntityId()))
                    .ifPresent(entity -> accountDeal.setEntityName(entity.getName()));
            String priceType = cost.getPriceType();
            String priceTypeDes = "";
            if (PriceType.RESOURCE.equals(priceType)) {
                priceTypeDes = "-资源费用";
            } else if (PriceType.SERVICE.equals(priceType)) {
                priceTypeDes = "-服务费用";
            } else if (PriceType.EXTRA_CONFIG.equals(priceType)) {
                priceTypeDes = "-配置费用";
            }
            if (ProductCodeEnum.HPC_DRP.getProductName().equals(serviceOrder.getProductName())) {
                priceTypeDes += "\n 个数：" + cost.getQuantity();
                priceTypeDes += "\n 集群ID：" + serviceOrder.getClusterUuid();
            }
            String remark = (org.springframework.util.StringUtils.hasText(cost.getProductCode())
                    && Objects.nonNull(EnumUtil.likeValueOf(ProductCodeEnum.class, cost.getProductCode())))
                    ? StrUtil.concat(true,
                                     EnumUtil.likeValueOf(ProductCodeEnum.class, cost.getProductCode())
                                             .getProductName(), priceTypeDes) : "";
            Boolean isRds = checkIsRdsByOrderId(serviceOrder.getId());
            if(isRds){
                remark=ProductCodeEnum.RDS.getProductName()+priceTypeDes;
                if(!ProductCodeEnum.RDS.getProductCode().equals(cost.getProductCode())){
                    remark="云硬盘RDS"+priceTypeDes;
                }
            }
            accountDeal.setRemark(remark);
            WebUserUtil.prepareInsertParams(accountDeal, serviceOrder.getCreatedBy());
            //关联不计费产品，设置收费规则为销售计费
            if (Objects.nonNull(serviceOrder.getChargingType()) && SALE_TYPE.equalsIgnoreCase(serviceOrder.getChargingType())) {
                accountDeal.setChargingType(SALE_TYPE);
                //销售计费情景下，记录收支明细，交易渠道默认现金余额
                accountDeal.setTradeChannel(RechargeTypeEnum.ACC_TCASH.getCode());
                accountDeal.setAmount(BigDecimal.ZERO);
                BizAccountDeal bizAccountDeal = BeanUtil.toBean(accountDeal, BizAccountDeal.class);
                bizAccountDeal.setDealSid(ID_WORKER.nextId());
                bizAccountDeal.setTradeNo(NoUtil.generateNo("ZD"));
                bizAccountDeal.setFlowNo(NoUtil.generateNo(BillingConstants.DEAL_PREFIX));
                deals.add(bizAccountDeal);
                //fix(39348):关联不计费产品-HPC专属资源池后分销商租户进行购买出现收支明细重复，销售计费直接跳过本次循环
                return;
            } else {
                accountDeal.setChargingType(NORMAL_TYPE);
            }
            BigDecimal couponAmount = cost.getCouponAmount();
            //优惠券抵扣为0元
            if (cost.getCouponDiscount() != null && cost.getCouponDiscount().compareTo(BigDecimal.ZERO) > 0
                    && cost.getPretaxAmount().compareTo(BigDecimal.ZERO) >= 0) {
                // 采用余额

                accountDeal.setAmount(BigDecimal.ZERO);
                BizAccountDeal bizAccountDeal = BeanUtil.toBean(accountDeal, BizAccountDeal.class);
                bizAccountDeal.setRemark(accountDeal.getRemark() + "(优惠券抵扣" + cost.getCouponDiscount().setScale(2,
                    RoundingMode.HALF_UP)
                    + "元)");
                bizAccountDeal.setTradeChannel(RechargeTypeEnum.COUPON.getCode());
                bizAccountDeal.setBalance(NumberUtil.add(account.getBalance(), cost.getCashAmount()));
                bizAccountDeal.setBalanceCredit(NumberUtil.add(account.getCreditLine(), cost.getCreditAmount()));
                bizAccountDeal.setBalanceCash(NumberUtil.add(account.getBalanceCash(), cost.getCouponAmount()));

                bizAccountDeal.setCouponAmount(cost.getCouponDiscount());
                bizAccountDeal.setDealSid(ID_WORKER.nextId());
                bizAccountDeal.setTradeNo(NoUtil.generateNo("ZD"));
                bizAccountDeal.setFlowNo(NoUtil.generateNo(BillingConstants.DEAL_PREFIX));
                deals.add(bizAccountDeal);
            }
            //实际支付金额为0 并且非优惠券抵扣 当前不可能出现0元商品 后边可能会有
            if (cost.getPretaxAmount().compareTo(BigDecimal.ZERO) == 0 && (cost.getCouponDiscount() == null ||
                    cost.getCouponDiscount().compareTo(BigDecimal.ZERO) == 0)) {
                accountDeal.setTradeChannel(RechargeTypeEnum.ACC_TCASH.getCode());
                accountDeal.setAmount(BigDecimal.ZERO);
                BizAccountDeal bizAccountDeal = BeanUtil.toBean(accountDeal, BizAccountDeal.class);
                bizAccountDeal.setDealSid(ID_WORKER.nextId());
                bizAccountDeal.setTradeNo(NoUtil.generateNo("ZD"));
                bizAccountDeal.setFlowNo(NoUtil.generateNo(BillingConstants.DEAL_PREFIX));
                deals.add(bizAccountDeal);
            }

            if (NumberUtil.isGreater(couponAmount, BigDecimal.ZERO) || NumberUtil
                    .isLess(couponAmount, BigDecimal.ZERO)) {
                // 现金券余额
                accountDeal.setTradeChannel(RechargeTypeEnum.ACC_BCASH.getCode());
                accountDeal.setAmount(releaseBeforeEndTime ? couponAmount.abs() : couponAmount);
                BizAccountDeal bizAccountDeal = BeanUtil.toBean(accountDeal, BizAccountDeal.class);
                bizAccountDeal.setDealSid(ID_WORKER.nextId());
                bizAccountDeal.setBalance(NumberUtil.add(account.getBalance(), cost.getCashAmount()));
                bizAccountDeal.setBalanceCredit(NumberUtil.add(account.getCreditLine(), cost.getCreditAmount()));
                bizAccountDeal.setTradeNo(NoUtil.generateNo("ZD"));
                bizAccountDeal.setFlowNo(NoUtil.generateNo(BillingConstants.DEAL_PREFIX));
                deals.add(bizAccountDeal);
            }
            BigDecimal cashAmount = cost.getCashAmount();
            if (NumberUtil.isGreater(cashAmount, BigDecimal.ZERO) || NumberUtil
                    .isLess(cashAmount, BigDecimal.ZERO)) {
                //现金支付金额
                accountDeal.setTradeChannel(RechargeTypeEnum.ACC_TCASH.getCode());
                accountDeal.setAmount(releaseBeforeEndTime ? cashAmount.abs() : cashAmount);
                BizAccountDeal bizAccountDeal = BeanUtil.toBean(accountDeal, BizAccountDeal.class);
                bizAccountDeal.setBalanceCredit(NumberUtil.add(account.getCreditLine(), cost.getCreditAmount()));
                bizAccountDeal.setDealSid(ID_WORKER.nextId());
                bizAccountDeal.setTradeNo(NoUtil.generateNo("ZD"));
                bizAccountDeal.setFlowNo(NoUtil.generateNo(BillingConstants.DEAL_PREFIX));
                deals.add(bizAccountDeal);
            }
            BigDecimal creditAmount = cost.getCreditAmount();
            if (NumberUtil.isGreater(creditAmount, BigDecimal.ZERO) || NumberUtil
                    .isLess(creditAmount, BigDecimal.ZERO)) {
                // 信用额度
                accountDeal.setTradeChannel(RechargeTypeEnum.ACC_CREDIT.getCode());
                accountDeal.setAmount(releaseBeforeEndTime ? creditAmount.abs() : creditAmount);
                BizAccountDeal bizAccountDeal = BeanUtil.toBean(accountDeal, BizAccountDeal.class);
                bizAccountDeal.setDealSid(ID_WORKER.nextId());
                bizAccountDeal.setTradeNo(NoUtil.generateNo("ZD"));
                bizAccountDeal.setFlowNo(NoUtil.generateNo(BillingConstants.DEAL_PREFIX));
                deals.add(bizAccountDeal);
            }
        });
        if (CollectionUtil.isNotEmpty(deals)) {
            bizAccountDealMapper.batchInsert(deals);
        }
    }

    private boolean releaseBeforeEndTime(ServiceOrder serviceOrder) {
        boolean isRelease = OrderType.RELEASE.equals(serviceOrder.getType());
        if (!isRelease) {
            return false;
        }
        ServiceOrderDetail detail = CollectionUtil.getFirst(serviceOrder.getDetails());
        if (Objects.isNull(detail)) {
            return false;
        }
        Date originalEndTime = JSONUtil.parseObj(detail.getServiceConfig())
                                       .getDate("originalEndTime");
        if (Objects.isNull(originalEndTime) || Objects.isNull(detail.getEndTime())) {
            return false;
        }
        return detail.getEndTime().before(originalEndTime);
    }

    private void setUsedCost(BizBillingAccount bizBillingAccount, InstanceGaapCost cost,
                             BigDecimal finalCost) {
        cost.setCashAmount(BigDecimal.ZERO);
        cost.setCreditAmount(BigDecimal.ZERO);
        cost.setCouponAmount(BigDecimal.ZERO);
        finalCost = BigDecimalUtil.remainTwoPointAmount(finalCost);
        if (NumberUtil.isGreater(finalCost, BigDecimal.ZERO)) {
            if (NumberUtil.isGreaterOrEqual(
                    Convert.toBigDecimal(BigDecimalUtil.getTwoPointAmount(bizBillingAccount.getBalanceCash()), BigDecimal.ZERO),
                    finalCost)) {
                cost.setCouponAmount(finalCost);
            } else {
                BigDecimal overCost = NumberUtil.sub(finalCost, BigDecimalUtil.getTwoPointAmount(bizBillingAccount.getBalanceCash()));
                cost.setCouponAmount(BigDecimalUtil.getTwoPointAmount(bizBillingAccount.getBalanceCash()));
                if (NumberUtil.isGreaterOrEqual(
                        Convert.toBigDecimal(BigDecimalUtil.getTwoPointAmount(bizBillingAccount.getBalance()), BigDecimal.ZERO),
                        overCost)) {
                    cost.setCashAmount(overCost);
                } else {
                    overCost = NumberUtil.sub(overCost, BigDecimalUtil.getTwoPointAmount(bizBillingAccount.getBalance()));
                    cost.setCashAmount(BigDecimalUtil.getTwoPointAmount(bizBillingAccount.getBalance()));
                    if (NumberUtil.isGreaterOrEqual(
                            Convert.toBigDecimal(BigDecimalUtil.getTwoPointAmount(bizBillingAccount.getCreditLine()), BigDecimal.ZERO),
                            overCost)) {
                        cost.setCreditAmount(overCost);
                    } else {
                        throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_643345578));
                    }
                }
            }
        }
    }

    private void setModifyCost(BizBillingAccount bizBillingAccount, InstanceGaapCost cost,
                             BigDecimal finalCost) {
        cost.setCashAmount(BigDecimal.ZERO);
        cost.setCreditAmount(BigDecimal.ZERO);
        cost.setCouponAmount(BigDecimal.ZERO);
        finalCost = BigDecimalUtil.remainTwoPointAmount(finalCost);
        if (NumberUtil.isGreater(finalCost, BigDecimal.ZERO)) {
            if (NumberUtil.isGreaterOrEqual(
                    Convert.toBigDecimal(BigDecimalUtil.getTwoPointAmount(bizBillingAccount.getBalanceCash()), BigDecimal.ZERO),
                    finalCost)) {
                cost.setCouponAmount(finalCost);
            } else {
                BigDecimal overCost = NumberUtil.sub(finalCost, BigDecimalUtil.getTwoPointAmount(bizBillingAccount.getBalanceCash()));
                cost.setCouponAmount(BigDecimalUtil.getTwoPointAmount(bizBillingAccount.getBalanceCash()));
                if (NumberUtil.isGreaterOrEqual(
                        Convert.toBigDecimal(BigDecimalUtil.getTwoPointAmount(bizBillingAccount.getBalance()), BigDecimal.ZERO),
                        overCost)) {
                    cost.setCashAmount(overCost);
                } else {
                    overCost = NumberUtil.sub(overCost, BigDecimalUtil.getTwoPointAmount(bizBillingAccount.getBalance()));
                    cost.setCashAmount(BigDecimalUtil.getTwoPointAmount(bizBillingAccount.getBalance()));
                    if (NumberUtil.isGreaterOrEqual(
                            Convert.toBigDecimal(BigDecimalUtil.getTwoPointAmount(bizBillingAccount.getCreditLine()), BigDecimal.ZERO),
                            overCost)) {
                        cost.setCreditAmount(overCost);
                    } else {
                        throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_643345578));
                    }
                }
            }
        } else  {
            cost.setCashAmount(finalCost);
        }
    }

    private Integer calculateOffDay(Date startTime, Date endTime) {
        if (Objects.isNull(startTime) || Objects.isNull(endTime) || startTime.compareTo(endTime) >= 0) {
            return 0;
        }

        int offset = (int) Math
                .ceil((endTime.getTime() - startTime.getTime()) / ((double) 1000 * 60 * 60 * 24));
        return offset == 0 ? 1 : offset;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized void taskPass(Task task, Map<String, Object> variables) {
        if (variables == null) {
            variables = Maps.newHashMap();
        }

        variables.put(ProcessConstants.AUDIT, ProcessConstants.AUDIT_PASS);

        if (task == null || task.getProcessInstanceId() == null) {
            return;
        }
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                                                        .processInstanceId(task.getProcessInstanceId()).singleResult();


        if (processInstance == null) {

            return;
        }
        variables.put(processInstance.getCaseInstanceId() + ProcessConstants.AUDIT_STATUS_SUFFIX,
                      ProcessConstants.AUDIT_PASS);

        Object comment = variables.get(ProcessConstants.AUDIT_COMMENT);
        if (comment != null) {
            // 保存审批注释(暂时未用到)
            taskService.addComment(task.getId(), task.getProcessInstanceId(), String.valueOf(comment));

            variables.put(processInstance.getCaseInstanceId() + ProcessConstants.AUDIT_COMMENT_SUFFIX,
                          String.valueOf(comment));
        }

        Object auditUname = variables.get("_audit_uname");
        if (auditUname != null) {
            variables.put(processInstance.getCaseInstanceId() + "_audit_uname",
                          String.valueOf(auditUname));
        }
        variables.put("_task_name", task.getName());
        taskService.resolveTask(task.getId(), variables);

        taskService.complete(task.getId(), variables);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void taskGoback(String candidateId, Task task, Map<String, Object> variables, String goback) {
        if (Objects.isNull(task)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_296564528));
        }
        if (Objects.isNull(variables)) {
            variables = Maps.newHashMap();
        }

        String audit = ProcessConstants.AUDIT_GOBACK + "-" + goback;

        variables.put(ProcessConstants.AUDIT, audit);

        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                                                        .processInstanceId(task.getProcessInstanceId()).singleResult();
        String taskId = task.getId();
        variables.put(taskId + ProcessConstants.AUDIT_STATUS_SUFFIX, audit);

        Object comment = variables.get(ProcessConstants.AUDIT_COMMENT);
        if (comment != null) {
            // 保存审批注释(暂时未用到)
            taskService.addComment(taskId, task.getProcessInstanceId(), String.valueOf(comment));
            variables.put(taskId + ProcessConstants.AUDIT_COMMENT_SUFFIX,
                          String.valueOf(comment));
        }

        Object auditUname = variables.get("_audit_uname");
        if (auditUname != null) {
            variables.put(taskId + "_audit_uname",
                          String.valueOf(auditUname));
        }
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (Objects.nonNull(authUser)) {
            variables.put(taskId + "_audit_usersid", authUser.getUserSid().toString());
        }
        variables.put("_task_name", task.getName());

        String advice = (String) variables.get("auditAdvice");
        if (StringUtils.isNotEmpty(advice)) {
            variables.put(taskId + ProcessConstants.AUDIT_ADVICE, advice);
        }

        if (StringUtils.isEmpty(candidateId)) {
            taskService.resolveTask(taskId, variables);
        }
        taskService.complete(taskId, variables);

        // 回退标识
        // 回退任务
        List<Task> currentTasks = taskService.createTaskQuery()
                                             .processInstanceId(processInstance.getProcessInstanceId())
                                             .list();
        // 当前节点ID
        ProcessTemplateDetail processTemplateDetail = new ProcessTemplateDetail();
        processTemplateDetail.setStatus("GoBack");
        Map<String, Object> condition = new HashMap<>();
        condition.put("processInstanceId", processInstance.getProcessInstanceId());
        if(Objects.nonNull(currentTasks.get(0).getOwner())){
            condition.put("nodeId", Convert.toLong(currentTasks.get(0).getOwner().replace("nodeid-", StrUtil.EMPTY)));
        }
        processTemplateDetailMapper.updateByParam(processTemplateDetail, condition);
        log.info("审批任务完成消息：[{}]完成了[{}]审批任务", auditUname, task.getName());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void taskReject(Task task, Map<String, Object> variables) {
        if (Objects.isNull(task)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_296564528));
        }
        if (Objects.isNull(variables)) {
            variables = Maps.newHashMap();
        }

        variables.put(ProcessConstants.AUDIT, ProcessConstants.AUDIT_REJECT);
        String taskId = task.getId();
        variables.put(taskId + ProcessConstants.AUDIT_STATUS_SUFFIX,
                      ProcessConstants.AUDIT_REJECT);

        Object comment = variables.get(ProcessConstants.AUDIT_COMMENT);
        if (comment != null) {
            taskService.addComment(taskId, task.getProcessInstanceId(), String.valueOf(comment));

            variables.put(taskId + ProcessConstants.AUDIT_COMMENT_SUFFIX,
                          String.valueOf(comment));
        }

        Object auditUname = variables.get("_audit_uname");
        if (auditUname != null) {
            variables.put(taskId + "_audit_uname",
                          String.valueOf(auditUname));
        }
        variables.put("_task_name", task.getName());

        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (Objects.nonNull(authUser)) {
            variables.put(taskId + "_audit_usersid", authUser.getUserSid().toString());
        }
        String advice = (String) variables.get("auditAdvice");
        if (StringUtils.isNotEmpty(advice)) {
            variables.put(taskId + ProcessConstants.AUDIT_ADVICE, advice);
        }

        taskService.complete(taskId, variables);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void taskReject(String candidateId, String businsessId, Map<String, Object> variables) {
        if (variables == null) {
            variables = Maps.newHashMap();
        }

        variables.put(ProcessConstants.AUDIT, ProcessConstants.AUDIT_REJECT);

        Task task = taskService.createTaskQuery().processInstanceBusinessKey(businsessId).singleResult();
        if (task == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_296564528));
        }

        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                                                        .processInstanceId(task.getProcessInstanceId()).singleResult();

        variables.put(processInstance.getCaseInstanceId() + ProcessConstants.AUDIT_STATUS_SUFFIX,
                      ProcessConstants.AUDIT_REJECT);

        Object comment = variables.get(ProcessConstants.AUDIT_COMMENT);
        if (comment != null) {
            // 保存审批注释(暂时未用到)
            taskService.addComment(task.getId(), task.getProcessInstanceId(), String.valueOf(comment));

            variables.put(processInstance.getCaseInstanceId() + ProcessConstants.AUDIT_COMMENT_SUFFIX,
                          String.valueOf(comment));
        }

        Object auditUname = variables.get("_audit_uname");
        if (auditUname != null) {
            variables.put(processInstance.getCaseInstanceId() + "_audit_uname",
                          String.valueOf(auditUname));
        }
        variables.put("_task_name", task.getName());

        taskService.claim(task.getId(), candidateId);
        taskService.complete(task.getId(), variables);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void taskGoback(String candidateId, String businsessId, Map<String, Object> variables) {
        if (variables == null) {
            variables = Maps.newHashMap();
        }

        String audit = ProcessConstants.AUDIT_GOBACK + "-1";

        variables.put(ProcessConstants.AUDIT, ProcessConstants.AUDIT_GOBACK + "-1");

        Task task = taskService.createTaskQuery().processInstanceBusinessKey(businsessId).singleResult();
        if (task == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_296564528));
        }

        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                                                        .processInstanceId(task.getProcessInstanceId()).singleResult();

        variables.put(processInstance.getCaseInstanceId() + ProcessConstants.AUDIT_STATUS_SUFFIX, audit);

        Object comment = variables.get(ProcessConstants.AUDIT_COMMENT);
        if (comment != null) {
            // 保存审批注释(暂时未用到)
            taskService.addComment(task.getId(), task.getProcessInstanceId(), String.valueOf(comment));

            variables.put(processInstance.getCaseInstanceId() + ProcessConstants.AUDIT_COMMENT_SUFFIX,
                          String.valueOf(comment));
        }

        Object auditUname = variables.get("_audit_uname");
        if (auditUname != null) {
            variables.put(processInstance.getCaseInstanceId() + "_audit_uname",
                          String.valueOf(auditUname));
        }
        variables.put("_task_name", task.getName());
        if (StringUtils.isEmpty(candidateId)) {
            taskService.resolveTask(task.getId(), variables);
        } else {
            taskService.claim(task.getId(), candidateId);
        }
        taskService.complete(task.getId(), variables);

        log.info("审批任务完成消息：[{}]完成了申请单[{}]的[{}]审批任务", auditUname, businsessId, task.getName());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doOpen(DelegateExecution execution) {
        String businssId = execution.getProcessBusinessKey();
        Map<String, Object> variables = execution.getVariables();

        log.info("申请单[{}]的审批流程已经全部审批通过，执行服务开通", businssId);

        businessService.approve(businssId, variables);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doClose(DelegateExecution execution) {
        String businssId = execution.getProcessBusinessKey();
        Map<String, Object> variables = execution.getVariables();
        if (StringUtils.isNotEmpty(businssId)) {

            log.info("申请单[{}]的审批流程已被拒绝，执行拒绝并关闭", businssId);

            businessService.reject(businssId, variables);
        } else {
            String serviceProcessId = (String) variables.get(ProcessConstants.SERVICE_PROCESS_ID);
            if (StringUtils.isNotEmpty(serviceProcessId)) {
                ServiceProcess serviceProcess = serviceProcessService.queryById(Long.parseLong(serviceProcessId));
                if (Objects.nonNull(serviceProcess)) {
                    serviceProcess.setStatus(ServiceProcessStatus.REJECTED);
                    serviceProcessService.updateById(serviceProcess);
                    log.info("流程申请[{}]的审批流程被拒绝并关闭，执行流程申请状态更改", serviceProcess.getName());
                }
            }
        }
    }

    @Override
    public List<ProcessActivityDto> instanceRecords(String businessId) {
        HistoricProcessInstance hpi = historyService.createHistoricProcessInstanceQuery()
                                                    .processInstanceBusinessKey(businessId).singleResult();
        if (hpi == null) {
            return Arrays.asList();
        }

        List<HistoricActivityInstance> activitiyInstances = historyService
                .createHistoricActivityInstanceQuery()
                .processInstanceId(hpi.getId())
                .orderByHistoricActivityInstanceId().asc().list();

        List<ProcessActivityDto> acts = new ArrayList<>();
        ProcessActivityDto actDto = null;
        Map<String, Object> variables = null;

        String processDefinitionId = hpi.getProcessDefinitionId();

        ProcessDefinitionEntity entity = (ProcessDefinitionEntity) repositoryService
                .getProcessDefinition(processDefinitionId);

        /*Collections.sort(activitiyInstances, (a, b) -> {
            int asort = actMap.get(a.getActivityId());
            int bsort = actMap.get(b.getActivityId());
            return asort - bsort;
        });*/

        boolean isUs = WebUtil.getHeaderAcceptLanguage();
        for (HistoricActivityInstance act : activitiyInstances) {
            String activityName = act.getActivityName();
            if (isUs) {
                if ("运营管理员审批".equals(activityName)) {
                    activityName = "Approval by Operations Administrator";
                } else if ("领导审批".equals(activityName)) {
                    activityName = "Leadership approval";
                } else if ("拒绝并关闭".equals(activityName)) {
                    activityName = "Reject and close";
                } else if ("执行".equals(activityName)) {
                    activityName = "execute";
                }
            }

            if ("startEvent".equals(act.getActivityType())) {
                actDto = new ProcessActivityDto();
                actDto.setId(act.getId());
                actDto.setActivityId(act.getActivityId());
                actDto.setActivityName(isUs ? "Submit application process" : "提交申请流程");
                actDto.setAssigneeId(act.getAssignee());
                actDto.setActivityType(act.getActivityType());
                actDto.setStartTime(act.getStartTime());
                actDto.setEndTime(act.getEndTime());
                actDto.setStatus("提交申请");

                if (variables == null) {
                    variables = executionVariables(act.getExecutionId());
                }
                actDto.setAssigneeName((String) variables.get("_apply_uname"));
                actDto.setComment((String) variables.get("__process_start_comment"));

                acts.add(actDto);
            } else if ("endEvent".equals(act.getActivityType())) {
                actDto = new ProcessActivityDto();
                actDto.setId(act.getId());
                actDto.setActivityId(act.getActivityId());
                actDto.setActivityName(isUs ? "Process completed" : "流程结束");
                actDto.setAssigneeId(act.getAssignee());
                actDto.setActivityType(act.getActivityType());
                actDto.setStartTime(act.getStartTime());
                actDto.setEndTime(act.getEndTime());
                actDto.setStatus("结束");
                actDto.setComment("流程结束");

                actDto.setAssigneeId("SYSTEM");
                actDto.setAssigneeName("[系统]");

                acts.add(actDto);
            } else if ("userTask".equals(act.getActivityType())) {
                actDto = new ProcessActivityDto();
                actDto.setId(act.getId());
                actDto.setActivityId(act.getActivityId());
                actDto.setActivityName(activityName);
                actDto.setAssigneeId(act.getAssignee());
                actDto.setActivityType(act.getActivityType());
                actDto.setStartTime(Optional.ofNullable(act.getEndTime()).orElse(act.getStartTime()));

                if (act.getEndTime() == null) {
                    List<IdentityLink> identitys = taskService.getIdentityLinksForTask(act.getTaskId());
                    if (identitys != null && identitys.size() > 0) {
                        String condiates = identitys.stream().map(identity -> {
                            identity.getUserId();
                            User user = userService.selectByPrimaryKey(Long.parseLong(identity.getUserId()));
                            if (user != null) {
                                return user.toNameInfo();
                            }

                            return "";
                        }).collect(Collectors.joining(","));
                        actDto.setComment(String.format("等待[%s]审批", condiates));
                        actDto.setStartTime(null);
                    }
                }

                acts.add(actDto);
            } else if ("exclusiveGateway".equals(act.getActivityType())) {
                if (actDto != null) {
                    actDto.setEndTime(act.getEndTime());

                    if (variables == null) {
                        variables = executionVariables(act.getExecutionId());
                    }

                    actDto.setStatus("审批中");
                    if (variables.containsKey(actDto.getActivityId()
                                                      + ProcessConstants.AUDIT_STATUS_SUFFIX)) {

                        String audit = variables.get(actDto.getActivityId()
                                                             + ProcessConstants.AUDIT_STATUS_SUFFIX).toString();
                        if (ProcessConstants.AUDIT_PASS.equals(audit)) {
                            actDto.setStatus("通过");
                        } else if (ProcessConstants.AUDIT_REJECT.equals(audit)) {
                            actDto.setStatus("拒绝");
                        }
                    }
                    if (variables.containsKey(actDto.getActivityId()
                                                      + ProcessConstants.AUDIT_COMMENT_SUFFIX)) {

                        actDto.setComment(variables.get(actDto.getActivityId()
                                                                + ProcessConstants.AUDIT_COMMENT_SUFFIX).toString());
                    }

                    if (variables.containsKey(actDto.getActivityId() + "_audit_uname")) {
                        actDto.setAssigneeName(variables.get(actDto.getActivityId() + "_audit_uname").toString());
                    } else {
                        if ("SYSTEM".equals(actDto.getAssigneeId())) {
                            actDto.setAssigneeName("[系统]");
                        } else {
                            try {
                                User user = userService.selectByPrimaryKey(Long.parseLong(actDto.getAssigneeId()));
                                if (user != null) {
                                    actDto.setAssigneeName(user.getRealName());
                                }
                            } catch (Exception e) {
                                log.error(e.getMessage());
                            }
                        }
                    }
                }

            } else if ("serviceTask".equals(act.getActivityType())) {
                actDto = new ProcessActivityDto();
                actDto.setId(act.getId());
                actDto.setActivityId(act.getActivityId());
                actDto.setActivityName(activityName);
                actDto.setAssigneeId(act.getAssignee());
                actDto.setActivityType(act.getActivityType());
                actDto.setStartTime(act.getStartTime());
                actDto.setEndTime(act.getEndTime());

                actDto.setAssigneeId("SYSTEM");
                actDto.setAssigneeName(isUs ? "[SYSTEM]" : "[系统]");

                if (variables == null) {
                    variables = executionVariables(act.getExecutionId());
                }

                String audit = (String) variables.get(ProcessConstants.AUDIT);
                if (ProcessConstants.AUDIT_PASS.equals(audit)) {
                    actDto.setStatus("服务开通");
                    actDto.setComment("执行服务开通");

                    String businessCode = (String) variables.get("_business_code");
                    if (businessCode != null) {
                        String businessName = ProcessConstants.BUSINESS_MAP.get(businessCode);
                        if (businessName != null) {
                            actDto.setComment(String.format("执行[%s]", businessName));
                        }
                    }

                } else if (ProcessConstants.AUDIT_REJECT.equals(audit)) {
                    actDto.setStatus("拒绝并关闭");
                    actDto.setComment("关闭流程");
                }

                acts.add(actDto);
            }
        }

        Map<String, User> users = Maps.newHashMap();
        User system = new User();
        //system.setName("自动处理");
        users.put("SYSTEM", system);

        for (ProcessActivityDto act : acts) {
            if (!users.containsKey(act.getAssigneeId())) {
                try {
                    User user = userService.selectByPrimaryKey(Long.parseLong(act.getAssigneeId()));
                    users.put(act.getAssigneeId(), user);
                } catch (Exception ignored) {
                    log.error(ignored.getMessage());
                }
            }

            User user = users.get(act.getAssigneeId());
            if (user != null && StringUtils.isBlank(act.getAssigneeName())) {
                act.setAssigneeName(user.getRealName());
            }

            ActivityImpl activity = entity.findActivity(act.getActivityId());
            act.setActLeft(activity.getX());
            act.setActTop(activity.getY());
            act.setActWidth(activity.getWidth());
            act.setActHeight(activity.getHeight());

            if (act.getEndTime() == null) {
                act.setActived(true);
            }
        }

        return acts;
    }

    @Override
    public void actTackCreatedCallback(DelegateTask task, String nodeId) {
        String businessId = task.getExecution().getProcessBusinessKey();
        if (StringUtils.isNotEmpty(businessId)) {
            log.info("申请单[{}]的审批任务[{}]已创建", businessId, task.getName());
        } else {
            log.info("审批任务[{}]已创建", task.getName());
        }

        Criteria criteria = new Criteria();
        criteria.put("nodeName",task.getName());
        criteria.put("processIdentify",task.getProcessDefinitionId());
        List<ProcessNode> processNodes = processNodeMapper.selectByParams(criteria);
        if (processNodes != null && !CollectionUtils.isEmpty(processNodes)) {
            Process process = processMgtService.selectByPrimaryKey(processNodes.get(0).getProcessId());
            // 发送消息
            ProcessNodeConfig config = JSON.parseObject(processNodes.get(0).getConfigData(), ProcessNodeConfig.class);

            Map<String, Object> execVariables = task.getExecution().getVariables();
            List<Long> candidates = task.getCandidates().stream().filter(c -> null != c.getUserId())
                    .map(c -> Long.parseLong(c.getUserId())).collect(Collectors.toList());

            Future<Object> submit =threadPool.submit(new TraceCallable<>(tracer, spanNamer, () -> {
                if (StringUtils.isNotEmpty(businessId)) {
                    List<String> notifyWays = new LinkedList<>();
                    notifyWays.add("station");
                    notifyWays.add("mail");
                    businessService.taskCreatedMessage(businessId, candidates, execVariables, notifyWays);
                } else {
                    businessService.taskCreatedMessage(process, candidates, execVariables, config.getNotifyWays());
                }
                return null;
            }));
        }
    }

    @Override
    public void actTackCompletedCallback(DelegateTask task, String nodeId) {
        Map<String, Object> execVariables = task.getExecution().getVariables();
        String auditUname = (String) execVariables.get("_audit_uname");
        String applyUname = Objects.nonNull(execVariables.get("_apply_uname")) ? execVariables.get("_apply_uname").toString().split("\\(")[0] : "";
        //流程变量
       // String audit = (String) execVariables.get(ProcessConstants.AUDIT);
        String businessKey = task.getExecution().getProcessBusinessKey();
        if (StringUtils.isNotEmpty(businessKey)) {
            log.info("[{}]完成了申请单[{}]的审批任务[{}]", auditUname, businessKey, task.getName());
        } else {
            log.info("[{}]完成了审批任务[{}]", auditUname, task.getName());
        }

        Criteria criteria = new Criteria();
        criteria.put("nodeName",task.getName());
        criteria.put("processIdentify",task.getProcessDefinitionId());
        List<ProcessNode> processNodes = processNodeMapper.selectByParams(criteria);
        if (CollectionUtils.isNotEmpty(processNodes) && processNodes.size() == 1) {
            Process process = processMgtService.selectByPrimaryKey(processNodes.get(0).getProcessId());
        //    log.info("审批节点信息：{}", processNodes.get(0));
        /*    List<String> notifyWays = new LinkedList<>();
            notifyWays.add("station");
            notifyWays.add("mail");*/
            // 发送消息
            ProcessNodeConfig config = JSON.parseObject(processNodes.get(0).getConfigData(), ProcessNodeConfig.class);
            String businessId = task.getExecution().getProcessBusinessKey();
            String usersid = execVariables.get("_apply_usersid").toString();

            Future<Object> submit = threadPool.submit(new TraceCallable<>(tracer, spanNamer, () -> {
                if (StringUtils.isNotEmpty(businessId)) {
                    businessService.taskCompletedMessage(businessId, auditUname,
                                                         Long.parseLong(usersid), execVariables,
                                                         config.getNotifyWays());
                } else {
                    businessService.taskCompletedMessage(process, auditUname, Long.parseLong(usersid), execVariables,
                                                         config.getNotifyWays());
                }
                return null;
            }));
        }
    }

    @Override
    public void removeProcessInstanceByBusinessId(String businessId, String reason) {
        HistoricProcessInstance hpi = historyService.createHistoricProcessInstanceQuery()
                                                    .processInstanceBusinessKey(businessId).singleResult();
        String processInstanceId = hpi.getId();

        ProcessInstance pi = runtimeService.createProcessInstanceQuery()
                                           .processInstanceId(processInstanceId).singleResult();
        if (pi == null) {
            // 该流程实例已经完成了
            historyService.deleteHistoricProcessInstance(processInstanceId);
        } else {
            // 该流程实例未结束的
            runtimeService.deleteProcessInstance(processInstanceId, reason);
            historyService.deleteHistoricProcessInstance(processInstanceId);
        }
    }

    private Map<String, Object> executionVariables(String executionId) {

        // 获取流程变量
        List<HistoricVariableInstance> variableInstances = historyService
                .createHistoricVariableInstanceQuery().executionIdIn(executionId).list();
        Map<String, Object> variables = Maps.newHashMap();
        for (HistoricVariableInstance variable : variableInstances) {
            variables.put(variable.getVariableName(), variable.getValue());
        }

        return variables;
    }

    private ProcessActivityDto getAssigneeId(ProcessActivityDto actDto, Map<String, Object> variables) {
        Object userSid = variables.get("userSid");
        if (userSid != null) {
            actDto.setAssigneeId(userSid.toString());
        }

        return actDto;
    }

    @Override
    public int countByOrgSid(Long orgSid) {
        return processMapper.countByOrgSid(orgSid);
    }

    @Override
    public boolean hasBusinessAuditNode(String businessCode) {
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        Long compayId = roleService.getCompayIdByOrgSid(authUser.getOrgSid());
        Process businessProcess = processMapper.selectByBusinessCode(businessCode, compayId);

        if (Objects.isNull(businessProcess)) {
            return false;
        }

        List<ProcessVersion> processVersions = processMgtService.versionList(businessProcess.getId());
        if (processVersions.size() < 1) {
            return false;
        }

        ProcessVersion processVersion = processVersions.get(0);
        List<ProcessNode> processNodes = processMgtService.auditNodeList(processVersion.getId());
        if (processNodes.size() < 1) {
            return false;
        }

        return true;
    }

    @Override
    public void processOrder(ServiceOrder serviceOrder) {
        if (!OrderStatus.APPROVING.equals(serviceOrder.getStatus())) {
            return;
        }

        Task task = taskService.createTaskQuery().processInstanceBusinessKey(serviceOrder.getOrderSn()).singleResult();
        if (task != null) {
            try {
                List<IdentityLink> identityLinks = taskService.getIdentityLinksForTask(task.getId());
                if (identityLinks.size() > 0) {
                    return;
                }
            } catch (Exception e) {
                log.error("task:[{}]", task.toString());
            }

            Map<String, Object> variablesMap = Maps.newHashMap();
            variablesMap.put("_audit_uname", "系统");
            variablesMap.put(ProcessConstants.AUDIT_COMMENT, "节点无审批候选人，自动通过");
            taskPass(task, variablesMap);

            if (OrderStatus.APPROVING.equals(serviceOrder.getStatus())) {
                processOrder(serviceOrder);
            }
        }
    }

    @Override
    public void returnShareNode(Integer amount, ResMaPoolVO resMaPoolVO) {
        CloudEnvParams params = new CloudEnvParams();
        params.setCloudEnvType(CloudEnvType.HCSO.getValue().get(0));
        List<CloudEnv> cloudEnvs = cloudEnvRemoteService.selectByParams(params);
        if (org.springframework.util.CollectionUtils.isEmpty(cloudEnvs)) {
            throw new cn.com.cloudstar.rightcloud.common.exception.BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1000498884));
        }

        Criteria criteria = new Criteria();
        criteria.put("configKeys", CommonPropertyKeyEnum.getAIAutoPropertyList());
        List<SysConfig> sysConfigs = sysConfigService.selectByParams(criteria);
        //避免空指针
        sysConfigs.forEach(sysConfig -> {
            if (Objects.isNull(sysConfig.getConfigValue())) {
                sysConfig.setConfigValue("");
            }
        });
        Map<String, String> configMap = sysConfigs.stream().collect(Collectors.toMap(SysConfig::getConfigKey, SysConfig::getConfigValue));

        Integer allNodesCount = this.querySharePoolNodeCount(configMap, cloudEnvs, amount, resMaPoolVO, ROLLBACK);

        this.updateMASharePools(configMap, cloudEnvs, allNodesCount - amount, resMaPoolVO, ROLLBACK);
    }


    /**
     * 查询AI共享资源池
     *
     * @param configMap
     * @param cloudEnvs
     * @param amount
     * @param resMaPoolVO
     * @param approveType
     */
    private Integer querySharePoolNodeCount(Map<String, String> configMap, List<CloudEnv> cloudEnvs, Integer amount,
                                            ResMaPoolVO resMaPoolVO, String approveType) {
        BaseResult baseResult = this.queryMASharePools(configMap, cloudEnvs, resMaPoolVO);

        String statusName = this.getCommonPoolStatus(baseResult);
        log.info("底层共享资源池状态：[{}]", statusName);
        if (!MaPoolStatusEnum.RUNNING.getStatusCode().equalsIgnoreCase(statusName)) {
            if (Objects.nonNull(resMaPoolVO)) {
                // 修改流程节点信息
                resMaPoolVO.setProessPhase(MaPoolProessPhase.UPDATE_SHARE_POOL_ERROR);
                if (ROLLBACK.equals(approveType)) {
                    resMaPoolVO.setProessPhase(MaPoolProessPhase.ROLLBACKERROR);
                }
                resMaPoolVO.setUpdatedDt(new Date());
                resMaPoolVO.setErrorInfo("底层共享资源池处于[" + statusName + "]状态，请稍后再试");
                maRemoteService.updateByPrimaryKeySelective(resMaPoolVO);
            }
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_449111516) + statusName + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1385592996));
        }

        // 获取当前共享资源池节点数量
        Integer allNodesCount = resMaPoolsService.getCommonRunCount(baseResult);
        Integer shareNodesMinCount = Integer.parseInt(configMap.get("iam.auto.node.min.num"));
        // 只有扩容判断最小节点数
        if (Integer.valueOf(0).compareTo(amount) < 0 && shareNodesMinCount > allNodesCount - amount) {
            if (Objects.nonNull(resMaPoolVO)) {
                // 修改流程节点信息
                resMaPoolVO.setProessPhase(MaPoolProessPhase.UPDATE_SHARE_POOL_ERROR);
                if (ROLLBACK.equals(approveType)) {
                    resMaPoolVO.setProessPhase(MaPoolProessPhase.ROLLBACKERROR);
                }
                resMaPoolVO.setUpdatedDt(new Date());
                resMaPoolVO.setErrorInfo("共享资源池节点数量不足，操作后剩余节点数不能小于配置的共享资源池最小节点数" + shareNodesMinCount + "！");
                maRemoteService.updateByPrimaryKeySelective(resMaPoolVO);
            }
            log.info("共享资源池节点数量不足，操作后剩余节点数不能小于配置的共享资源池最小节点数：[{}]，当前节点数：[{}]", shareNodesMinCount, allNodesCount - amount);
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_168554852) + shareNodesMinCount + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_65281));
        }
        int availableCount = resMaPoolsService.getCommonAvailableCount(resMaPoolVO.getId());
        if (amount - availableCount > 0) {
            if (Objects.nonNull(resMaPoolVO)) {
                // 修改流程节点信息
                resMaPoolVO.setProessPhase(MaPoolProessPhase.UPDATE_SHARE_POOL_ERROR);
                if (ROLLBACK.equals(approveType)) {
                    resMaPoolVO.setProessPhase(MaPoolProessPhase.ROLLBACKERROR);
                }
                resMaPoolVO.setUpdatedDt(new Date());
                resMaPoolVO.setErrorInfo("购买的数量" + amount + "大于共享池可用节点数量！");
                maRemoteService.updateByPrimaryKeySelective(resMaPoolVO);
            }
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_354563552) + amount + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1934342579));
        }
        return allNodesCount;
    }

    /**
     * 查询底层共享资源池剩余节点数量
     *
     * @param configMap
     * @param cloudEnvs
     * @param resMaPoolVO
     */
    private BaseResult queryMASharePools(Map<String, String> configMap, List<CloudEnv> cloudEnvs, ResMaPoolVO resMaPoolVO) {


        BaseResult result;
        String errMsg = "";
        try {
            Long clusterId = resMaPoolVO.getId();
            result = resMaPoolsService.getMaSharePoolsQueryResult(clusterId);
            if (!result.isSuccess()) {
                throw new Exception();
            }
        } catch (Exception e) {
            if (Objects.nonNull(resMaPoolVO)) {
                // 修改流程节点信息
                resMaPoolVO.setProessPhase(MaPoolProessPhase.UPDATE_SHARE_POOL_ERROR);
                resMaPoolVO.setUpdatedDt(new Date());
                if (StringUtils.isNotBlank(errMsg)) {
                    resMaPoolVO.setErrorInfo(errMsg);
                } else {
                    resMaPoolVO.setErrorInfo("节点状态异常，请咨询运维管理员查看底层共享资源池运行情况");
                }
                maRemoteService.updateByPrimaryKeySelective(resMaPoolVO);
            }
            log.error("共享资源池查询异常:", e);
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_947357107));
        }
        return result;
    }

    /**
     * 共享资源池节点释放、归还
     *
     * @param configMap
     * @param cloudEnvs
     * @param desiredNodeCount
     * @param resMaPoolVO
     * @param approveType
     */
    private void updateMASharePools(Map<String, String> configMap, List<CloudEnv> cloudEnvs, Integer desiredNodeCount,
                                    ResMaPoolVO resMaPoolVO, String approveType) {

        String errMsg = "";
        try {
            BaseResult result = new BaseResult();
            Long id = resMaPoolVO.getId();
            ResMaPoolRelationExample poolRelationExample = new ResMaPoolRelationExample();
            poolRelationExample.createCriteria().andResMaPoolIdEqualTo(id);
            List<ResMaPoolRelation> maPoolRelations = resMaPoolRelationRemoteService.selectByExample(poolRelationExample);
            ResMaPoolRelation resMaPoolRelation = maPoolRelations.stream().findFirst().orElse(new ResMaPoolRelation());

            if (StringUtils.equalsIgnoreCase("V2", resMaPoolRelation.getPoolVersion())) {
                MAPoolsQueryResult poolsQueryResultV2 = resMaPoolsService.getMaSharePoolsQueryResultV2(resMaPoolRelation);
                result = resMaPoolsService.getMaSharePoolsUpdateResultV2(resMaPoolRelation, desiredNodeCount, poolsQueryResultV2);
            } else {
                String poolId = resMaPoolRelation.getPoolId();
                if (StringUtils.isEmpty(poolId)) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_42530987));
                }
                MASharePoolsUpdate maSharePoolsUpdate = CloudClientFactory.buildMQBean(cloudEnvs.get(0).getId(),
                                                                                       MASharePoolsUpdate.class);
                maSharePoolsUpdate.setPoolId(poolId);
                maSharePoolsUpdate.setDesiredNodeCount(desiredNodeCount);
                resMaPoolsService.completeShareBase(maSharePoolsUpdate);
                Base clearPass = BaseClearPassUtil.clearPass(maSharePoolsUpdate);

                result = (BaseResult) MQHelper.rpc(maSharePoolsUpdate);
                log.info("AI共享资源池变更V1结果返回：[{}]", JSONUtil.toJsonStr(result));
            }
            if (!result.isSuccess()) {
                errMsg = result.getErrMsg();
                throw new BizException(errMsg);
            }
        } catch (Exception e) {
            if (Objects.nonNull(resMaPoolVO)) {
                // 修改流程节点信息
                resMaPoolVO.setProessPhase(MaPoolProessPhase.UPDATE_SHARE_POOL_ERROR);
                if (ROLLBACK.equals(approveType)) {
                    resMaPoolVO.setProessPhase(MaPoolProessPhase.ROLLBACKERROR);
                }
                resMaPoolVO.setUpdatedDt(new Date());
                if (StringUtils.isNotBlank(errMsg)) {
                    resMaPoolVO.setErrorInfo(errMsg);
                } else {
                    resMaPoolVO.setErrorInfo("节点状态异常，请咨询运维管理员查看底层共享资源池运行情况");
                }
                maRemoteService.updateByPrimaryKeySelective(resMaPoolVO);
            }
            log.error("共享资源池变更异常:", e);
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1109359604));
        }

        if (Objects.nonNull(resMaPoolVO)) {
            // 修改流程节点信息
            resMaPoolVO.setProessPhase(MaPoolProessPhase.UPDATE_SHARE_POOL_PEDING);
            if (ROLLBACK.equals(approveType)) {
                resMaPoolVO.setProessPhase(MaPoolProessPhase.ROLLBACKERROR);
            }
            resMaPoolVO.setUpdatedDt(new Date());
            maRemoteService.updateByPrimaryKeySelective(resMaPoolVO);
        }
    }

    private String getCommonPoolStatus(BaseResult baseResult) {
        // MASharePoolsQueryResult V2版本获取共享资源池节点信息结果 ，MAPoolsQueryResult V2版本获取共享资源池节点信息结果
        String status = StrUtil.EMPTY;
        if (baseResult instanceof MASharePoolsQueryResult) {
            MASharePoolsQueryResult queryResult = (MASharePoolsQueryResult) baseResult;
            status = queryResult.getData().getState();
        }
        if (baseResult instanceof MAPoolsQueryResult) {
            MAPoolsQueryResult queryResult = (MAPoolsQueryResult) baseResult;
            status = queryResult.getStatus().getPhase();
        }
        return status;
    }

    /**
     * 生成账单周期表数据
     *
     * @param costs
     */
    @Transactional
    public void insertBillCycleInfo(List<InstanceGaapCost> costs) {
        costs.forEach(cost -> {
            BillBillingCycleCost cycleCost = new BillBillingCycleCost();
            cycleCost.setBillingCycle(cost.getBillingCycle());
            cycleCost.setBillNo(NoUtil.generateNo("BP"));
            cycleCost.setCloudEnvType(cost.getCloudEnvType());
            cycleCost.setCloudEnvName(cost.getCloudEnvName());
            cycleCost.setProductCode(cost.getProductCode());
            cycleCost.setProductName(cost.getProductName());
            cycleCost.setBillStartTime(cost.getUsageStartDate());
            cycleCost.setBillEndTime(cost.getUsageEndDate());
            cycleCost.setPayTime(cost.getPayTime());
            cycleCost.setOfficialAmount(cost.getPretaxGrossAmount());
            cycleCost.setDiscountAmount(cost.getPretaxAmount());
            cycleCost.setCashAmount(cost.getCashAmount());
            cycleCost.setCreditAmount(cost.getCreditAmount());
            cycleCost.setVoucherAmount(cost.getCouponAmount());
            cycleCost.setInvoiceAmount(cost.getCashAmount());
            cycleCost.setEntityId(cost.getEntityId());
            cycleCost.setEntityName(cost.getEntityName());
            if (cost.getUserAccountId() != null) {
                cycleCost.setOwnerId(cost.getUserAccountId());
            }
            cycleCost.setOrgId(cost.getOrgSid());
            cycleCost.setOrgName(cost.getOrgName());
            cycleCost.setPriceType(cost.getPriceType());
            cycleCost.setBillType(cost.getBillType());
            BigDecimal cashAmount = cost.getCashAmount() != null ? cost.getCashAmount() : new BigDecimal(0);
            BigDecimal creditAmount = cost.getCreditAmount() != null ? cost.getCreditAmount() : new BigDecimal(0);
            BigDecimal voucherAmount = cost.getCouponAmount() != null ? cost.getCouponAmount() : new BigDecimal(0);
            cycleCost.setAmount(
                    (cashAmount.add(creditAmount).add(voucherAmount)).setScale(5, BigDecimal.ROUND_HALF_UP));
            cycleCost.setCouponDiscount(cost.getCouponDiscount());
            cycleCost.setPricingDiscount(cost.getPricingDiscount());
            cycleCost.setCreateDt(new Date());
            cycleCost.setEntityId(cost.getEntityId());
            cycleCost.setEntityName(cost.getEntityName());
            //增加抹零金额
            cycleCost.setEraseZeroAmount(cost.getEraseZeroAmount());
            BillBillingCycleCost insert = mongoTemplate.insert(cycleCost);
            //保存拆分账单周期数据
            bizBillingCycleService.splitAndSaveCycleItem(cycleCost);
            //更新账单明细
            Query query = new Query();
            Update update = new Update();
            query.addCriteria(org.springframework.data.mongodb.core.query.Criteria.where("_id").is(cost.getMongoId()));
            update.set("billBillingCycleId", insert.getId().toString());
            update.set("entityId",cost.getEntityId());
            update.set("entityName",cost.getEntityName());
            UpdateResult updateResult = mongoTemplate.updateMulti(query, update, "biz_bill_usage_item");
            if (updateResult.getMatchedCount() > 0) {
                log.error("ProcessServiceImpl------insertBillCycleInfo-----------success");
            } else {
                log.error("ProcessServiceImpl------insertBillCycleInfo-----------Failed-----{}",
                          JSON.toJSONString(updateResult));
            }
        });

    }

    private void updateApplyBillOrderStatus(List<InstanceGaapCost> costs) {
        log.debug("ProcessServiceImpl_updateApplyBillOrderStatus_【{}】", JSON.toJSONString(costs));
        List<InstanceGaapCost> noCycleIdCostList = new ArrayList<>();
        costs.forEach(cost -> {

            String invoiceStatus = cost.getInvoiceStatus();
            if (StringUtils.isNoneEmpty(cost.getBillBillingCycleId())) {
                //更新账单明细
                Query query = new Query();
                Update update = new Update();
                query.addCriteria(org.springframework.data.mongodb.core.query.Criteria.
                                                                                              where("billBillingCycleId")
                                                                                      .is(cost.getBillBillingCycleId()));
                update.set("orderStatus", OrderStatus.RELEASE_SUCCESS);
                UpdateResult updateResult = mongoTemplate.updateMulti(query, update, "biz_bill_usage_item");

                //更新账期表结束日期
                Query queryCycle = new Query();
                Update updateCycle = new Update();
                queryCycle.addCriteria(org.springframework.data.mongodb.core.query.Criteria.
                                                                                                   where("_id")
                                                                                           .is(new ObjectId(
                                                                                                   cost.getBillBillingCycleId())));
                updateCycle.set("billEndTime", cost.getUsageEndDate());
                UpdateResult billingCycle = mongoTemplate.updateMulti(queryCycle, updateCycle,
                                                                      "biz_bill_billing_cycle");
                if (billingCycle.getMatchedCount() > 0) {
                    log.error("ProcessServiceImpl------updatebillingCycle-----------success-----{}",
                              JSON.toJSONString(billingCycle));
                } else {
                    log.error("ProcessServiceImpl------updatebillingCycle-----------Failed-----{}",
                              JSON.toJSONString(billingCycle));
                }
            } else {
                noCycleIdCostList.add(cost);
            }
        });
        if (!CollectionUtil.isEmpty(noCycleIdCostList)) {
            this.insertBillCycleInfo(noCycleIdCostList);
        }
    }

    private Long getDRPCueValue(String specName) {
        BizBillingSpecRefExample specRefExample = new BizBillingSpecRefExample();

        specRefExample.createCriteria()
                      .andResourceTypeEqualTo(ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode());

        List<BizBillingSpecRef> bizBillingSpecRefs = bizBillingSpecRefMapper.selectByExampleWithBLOBs(specRefExample);
        if (!CollectionUtil.isEmpty(bizBillingSpecRefs)) {
            for (BizBillingSpecRef bizBillingSpecRef : bizBillingSpecRefs) {
                String value = bizBillingSpecRef.getValue();
                if (StringUtils.isNotEmpty(value)) {
                    List<SpecRefValue> specRefValues = JSONObject.parseArray(value, SpecRefValue.class);
                    if (!CollectionUtil.isEmpty(specRefValues)) {
                        for (SpecRefValue specRefValue : specRefValues) {
                            if (StringUtil.equals(specName, specRefValue.getValue())) {
                                return specRefValue.getCueValue();
                            }
                        }
                    }
                }
            }
        }
        return null;
    }

    private void hpcDrpBusinessProcess(Task task, String processDefinitionName, String businessId,
                                       ServiceOrder serviceOrder, List<ServiceOrderDetail> details) {
        if ("运营管理员预审批".equals(task.getName()) && "HPC专属资源池服务开通".equals(processDefinitionName)) {
            log.info("hpc专属资源池预审批相关资源开通...");
            Long clusterId = getClusterId(serviceOrder.getId());
            // CCM接口调用
            // LdapPwd获取
            User user = userService.selectByPrimaryKey(serviceOrder.getOwnerId());
            String consoleUrl = cn.com.cloudstar.rightcloud.oss.util.PropertiesUtil.getProperty(
                    "rightcloud.console.url");
            consoleUrl = cn.com.cloudstar.rightcloud.module.support.access.util.StringUtil.addSuffixIfNot(consoleUrl,
                                                                                                          "/");
            log.info("hpc专属资源池预审批相关资源开通..." + consoleUrl);
            CreateHpcDrpResource createHpcDrpResource = new CreateHpcDrpResource();
            createHpcDrpResource.setHpcClusterId(clusterId);
            createHpcDrpResource.setLdapPassword(
                    CrytoUtilSimple.decrypt(PropertiesUtil.getProperty(LdapPropertyKey.LDAP_CIPHER)));
            createHpcDrpResource.setManagementPlatformLoginURL(consoleUrl + "#/login");
            createHpcDrpResource.setManagementPlatformErrorURL(consoleUrl + "#/unavailable");

            CreateHPCClusterResult hpcClusterResult = null;
            try {
                hpcClusterResult = BasicInfoUtil.replaceUserToInvoke(
                        () -> hpcRemoteService.createHPCCluster(createHpcDrpResource),
                        serviceOrder.getOwnerId());

            } catch (cn.com.cloudstar.rightcloud.common.exception.BizException e) {
                ResHpcClusterRemoteModule resHpcClusterRemoteModule = hpcRemoteService.selectByPrimaryKey(clusterId);
                Map sendAddr = new HashMap();
                Map messageContent = new HashMap();
                messageContent.put("applyUser", resHpcClusterRemoteModule.getAdminUser());
                messageContent.put("clusterUuid", "开通未生成");
                messageContent.put("clusterName", resHpcClusterRemoteModule.getName());
                messageContent.put("phase", "HPC专属资源池开通");
                messageContent.put("errMsg", resHpcClusterRemoteModule.getErrorInfo());
                sendAddr.put("email", new ArrayList<>(sysConfigRemoteService.getSendMailUrl()));
                log.info("HPC专属资源池开通发送错误邮件,{}", messageContent);
                notificationService.sendNotification(new int[]{1}, false, sendAddr, messageContent,
                                                     "hpc_drp_error_notice",null);
                log.info("HPC专属资源池开通发送错误邮件完成");
                throw e;
            }
            // CreateHPCClusterResult hpcClusterResult = new CreateHPCClusterResult();
            // 更新集群信息
            ResHpcClusterRemoteModule hpcClusterRemoteModule = new ResHpcClusterRemoteModule();
            hpcClusterRemoteModule.setId(clusterId);
            hpcClusterRemoteModule.setResourceId(hpcClusterResult.getHPCClusterID());
            hpcClusterRemoteModule.setTaskId(hpcClusterResult.getTaskID());
            hpcRemoteService.updateHpcCluster(hpcClusterRemoteModule);
            serviceOrder.setClusterUuid(hpcClusterResult.getHPCClusterID());
            serviceOrder.setClusterId(clusterId);
            serviceOrderService.updateByPrimaryKeySelective(serviceOrder);
        }
    }

    private void hpcDrpMail(Long clusterId) {
        //hpc暂无
    }

    /**
     * 通过订单ID获取生成的资源集群ID
     *
     * @param serviceOrderId
     */
    private Long getClusterId(Long serviceOrderId) {
        try {
            Criteria criteria = new Criteria();
            criteria.put("service_order_id", serviceOrderId);
            List<SfProductResource> sfProductResources = sfProductResourceMapper.selectByParams(criteria);
            SfProductResource sfProductResource = sfProductResources.get(0);

            return sfProductResource.getClusterId();
        } catch (Exception ex) {
            return null;
        }
    }


    /**
     * 审核详细（包括出账）
     *
     * @param serviceOrder
     * @param detail
     * @param resourceInfo
     * @param quantity
     * @param priceDetailQuantityFlg true：使用priceDetail的quantity
     * @param currentTime            当前时间
     */
    private void approved(ServiceOrder serviceOrder, ServiceOrderDetail detail, ResourceInfo resourceInfo,
                          BigDecimal quantity, boolean priceDetailQuantityFlg, Date currentTime) {
        log.info("审核详细（包括出账）");
        BizBillingAccount bizBillingAccount = bizBillingAccountMapper
                .selectByPrimaryKey(serviceOrder.getBizBillingAccountId());
        if (ObjectUtils.isEmpty(bizBillingAccount)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_607095064));
        }
        if (OrderType.RELEASE.equals(serviceOrder.getType())) {
            boolean noneMatch = serviceOrder.getDetails().stream().noneMatch(
                    d -> ProductCodeEnum.HPC_DRP.getProductCode().equals(d.getServiceType())
                            || ProductCodeEnum.ECS.getProductCode().equals(d.getServiceType())
                            || ProductCodeEnum.RS_BMS.getProductCode().equals(d.getServiceType()));
            boolean isDrp = ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode().equals(detail.getServiceType());
            Date endTime = cn.hutool.core.date.DateUtil.date();
            if (Objects.nonNull(resourceInfo)) {
                endTime = Convert.toDate(resourceInfo.getTime(), cn.hutool.core.date.DateUtil.date());
            }
            SfProductResource sfProductResource = null;
            if (noneMatch) {
                cn.hutool.json.JSONObject serviceConfig = JSONUtil
                        .parseObj(detail.getServiceConfig());
                Long productResourceId = serviceConfig.getLong("productResourceId");
                if (productResourceId != null) {
                    sfProductResource = sfProductResourceMapper.selectByPrimaryKey(productResourceId);
                }
                if (serviceConfig.getDate("originalEndTime") != null && serviceConfig.getDate("originalEndTime")
                        .after(endTime)) {
                    Date startTime = sfProductResource.getStartTime();
                    Date temp = startTime;
                    int mount = 0;
                    while (temp.compareTo(endTime) <= 0) {
                        mount++;
                        temp = cn.hutool.core.date.DateUtil.offsetMonth(startTime, mount);
                    }
                    startTime = temp;
                    endTime = startTime.after(serviceConfig.getDate("originalEndTime")) ? serviceConfig.getDate(
                            "originalEndTime") : startTime;
                }
                detail.setEndTime(endTime);
                serviceOrderDetailMapper.updateByPrimaryKeySelective(detail);
            }
            if (serviceOrder.getDetails().size() > 1 && noneMatch) {
                ServiceOrderDetail serviceOrderDetail = serviceOrder.getDetails().get(1);
                serviceOrderDetail.setEndTime(endTime);
                serviceOrderDetailMapper.updateByPrimaryKeySelective(serviceOrderDetail);
            }
            if (serviceOrder.getDetails().size() > 2 && noneMatch) {
                ServiceOrderDetail serviceOrderDetail = serviceOrder.getDetails().get(2);
                serviceOrderDetail.setEndTime(endTime);
                serviceOrderDetailMapper.updateByPrimaryKeySelective(serviceOrderDetail);
            }
            if (ProductCodeEnum.ECS.getProductCode().equals(detail.getServiceType())
                    || ProductCodeEnum.RS_BMS.getProductCode().equals(detail.getServiceType())) {
                for (ServiceOrderDetail serviceOrderDetail : serviceOrder.getDetails()) {
                    serviceOrderDetail.setEndTime(endTime);
                    serviceOrderDetailMapper.updateByPrimaryKeySelective(serviceOrderDetail);
                }
            }

            //判断是不是HPC预部署，如果是HPC预部署需要调用同步资源接口
            if(ApplyTypeEnum.HPC_DRP_STANDARD.getType().equals(detail.getApplyType())){
                log.info("HPC退订同步开始");
                HpcSyncCloudEnvRequest hpcSyncCloudEnvRequest=new HpcSyncCloudEnvRequest();
                hpcSyncCloudEnvRequest.setCloudEnvId(getFDCloudEnv());
                hpcSyncCloudEnvRequest.setKey("HPC_CLUSTER");
                //hpc暂无
                log.info("HPC退订同步结束");
            }
            //ecs本身是按量计费，挂载了包年包月的硬盘，需要对包年包月的硬盘出账11
            if (ProductCodeEnum.ECS.getProductCode().equals(detail.getServiceType())
                    || ProductCodeEnum.RS_BMS.getProductCode().equals(detail.getServiceType())
                    ||ProductCodeEnum.RDS.getProductCode().equals(detail.getServiceType())
                    ||ProductCodeEnum.AI_BMS_EXCLUSIVE.getProductCode().equals(detail.getServiceType())
                    ||ProductCodeEnum.isFederationProduct(detail.getServiceType())) {
                billEcs(serviceOrder);
                return;
            }
            if (BillingConstants.ChargeType.PRE_PAID.equals(detail.getChargeType())) {
                cn.hutool.json.JSONObject serviceConfig = JSONUtil
                        .parseObj(detail.getServiceConfig());
                List<String> serviceOrderIds = new ArrayList<>();
                if (isDrp) {
                    serviceOrderIds = Convert
                            .convert(new TypeReference<List<String>>() {
                            }, serviceConfig.get("applyServiceOrderId"));
                } else {
                    serviceOrderIds = Lists
                            .newArrayList(serviceConfig.getStr("applyServiceOrderId"));
                }

                List<String> renewServiceOrderIds = Convert
                        .convert(new TypeReference<List<String>>() {
                        }, serviceConfig.get("renewServiceOrderIds"));

                List<String> upgradeServiceOrderId = Convert
                        .convert(new TypeReference<List<String>>() {
                        }, serviceConfig.get("upgradeServiceOrderId"));

                List<String> degradeServiceOrderId = Convert
                        .convert(new TypeReference<List<String>>() {
                        }, serviceConfig.get("degradeServiceOrderId"));


                if (CollectionUtil.isNotEmpty(renewServiceOrderIds)) {
                    serviceOrderIds.addAll(renewServiceOrderIds);
                }

                BigDecimal degradeOriginal = BigDecimal.ZERO;
                if (CollectionUtil.isNotEmpty(degradeServiceOrderId)) {
                    Criteria orderIdListCriteria = new Criteria("orderIdList", degradeServiceOrderId);
                    List<ServiceOrder> serviceOrders = serviceOrderMapper.selectByAllParams(orderIdListCriteria);
                    List<BigDecimal> finalCosts = serviceOrders.stream()
                            .map(ServiceOrder::getFinalCost)
                            .collect(Collectors.toList());
                    for (BigDecimal finalCost : finalCosts) {
                        degradeOriginal = finalCost.add(degradeOriginal);
                    }
                    serviceOrderIds.addAll(degradeServiceOrderId);
                }


                if (CollectionUtil.isNotEmpty(upgradeServiceOrderId)) {
                    serviceOrderIds.addAll(upgradeServiceOrderId);
                }
                org.springframework.data.mongodb.core.query.Criteria criteria = org.springframework.data.mongodb.core.query.Criteria
                        .where("orderId").in(serviceOrderIds)
                        .and("invoiceStatus").in("pending", "done");
                List<InstanceGaapCost> invoicedCostList = mongoTemplate
                        .find(Query.query(criteria), InstanceGaapCost.class, "biz_bill_usage_item");

                if (CollectionUtil.isNotEmpty(invoicedCostList)) {
                    Date maxEndDate = invoicedCostList.stream()
                            .map(InstanceGaapCost::getUsageEndDate)
                            .max((e1, e2) -> e1.compareTo(e2))
                            .get();
                    if (maxEndDate.after(endTime)) {
                        throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1151614270));
                    }
                }
                //HPC走另外的逻辑
                if (!noneMatch) {
                    try {
                        String checkJobFlag = cn.com.cloudstar.rightcloud.oss.common.util.PropertiesUtil.getProperty(
                                "hpc.checkJob.flag");
                    } catch (BizException be) {
                        throw be;
                    } catch (Exception e) {
                        log.error("北向接口服务异常：", e);
                        throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1024429538));
                    }
                    billHpcDrp(serviceOrder);
                    return;
                }

                Map<Long, ServiceOrderDetail> detailMap = new HashMap<>();

                Criteria orderDetalCri = new Criteria();
                orderDetalCri.put("orderIds", serviceOrderIds);
                if (ProductCodeEnum.SFS_TURBO.getProductCode().equals(detail.getServiceType())
                        ||ProductCodeEnum.RDS.getProductCode().equals(detail.getServiceType())
                        ||ProductCodeEnum.DCS.getProductCode().equals(detail.getServiceType())
                ) {
                    Criteria c = new Criteria();
                    c.put("orderSourceSn", serviceOrder.getOrderSourceSn());
                    List<ServiceOrderDTO> dtos = serviceOrderMapper.selectOrders(c);
                    if (CollectionUtil.isNotEmpty(dtos)) {
                        serviceOrderIds.addAll(dtos.stream().filter(s -> StrUtil.equals(s.getStatus(), "completed")).map(o -> String.valueOf(o.getId())).collect(Collectors.toList()));
                    }
                }
                List<ServiceOrderDetail> serviceOrderDetails = serviceOrderDetailMapper.selectByParams(orderDetalCri);
                if (ProductCodeEnum.RS_BMS.getProductCode().equals(detail.getServiceType())) {
                    Long id = sfProductResource.getId();
                    serviceOrderDetails = serviceOrderDetails.stream()
                                                             .filter(serviceOrderDetail ->
                                                                             id.equals(JSON.parseObject(serviceOrderDetail.getServiceConfig())
                                                                                           .getLong("productResourceId")))
                                                             .collect(Collectors.toList());
                }
                if (ProductCodeEnum.RDS.getProductCode().equals(detail.getServiceType())) {
                    serviceOrderDetails = serviceOrderDetails.stream().collect(
                            Collectors.collectingAndThen(
                                    Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(ServiceOrderDetail::getOrderId))), ArrayList::new));

                }
                detailMap = serviceOrderDetails.stream()
                        .collect(Collectors.toMap(ServiceOrderDetail::getOrderId,
                                serviceOrderDetail -> serviceOrderDetail));

                Date originalEndTime = serviceConfig.getDate("originalEndTime");

                org.springframework.data.mongodb.core.query.Criteria query = org.springframework.data.mongodb.core.query.Criteria
                        .where("orderId").in(serviceOrderIds);
                List<InstanceGaapCost> costs = mongoTemplate
                        .find(Query.query(query), InstanceGaapCost.class);
                if (ProductCodeEnum.ECS.getProductCode().equals(detail.getServiceType())
                        || ProductCodeEnum.RS_BMS.getProductCode().equals(detail.getServiceType())) {
                    Long id = sfProductResource.getId();
                    List<String> applyOrder = Lists.newArrayList(serviceConfig.getStr("applyServiceOrderId"));
                    List<InstanceGaapCost> costsa = costs.stream().filter(e -> !applyOrder.contains(e.getOrderId())).collect(Collectors.toList());
                    InstanceGaapCost instanceGaapCost = costs.stream().filter(e -> applyOrder.contains(e.getOrderId()) && StringUtils.equals(e.getInstanceId(), id.toString())).findFirst().orElse(null);
                    costsa.add(instanceGaapCost);
                    costs = costsa;
                }
                BigDecimal[] unsubAmount = null;
                if (Strings.isNotBlank(serviceOrder.getContractId()) && CollectionUtil.isNotEmpty(costs)) {
                    unsubAmount = serviceOrder.getFinalCost().divideAndRemainder(BigDecimal.valueOf(costs.size()));
                }
                List<InstanceGaapCost> finalCostList = new ArrayList<>();
                Long productResourceId = serviceConfig.getLong("productResourceId");
                if (sfProductResource == null) {
                    sfProductResource = sfProductResourceMapper.selectByPrimaryKey(
                        productResourceId);
                }
                //MA训练,已冻结或者已过期走新的补扣逻辑
                if (isDrp && ApplyTypeEnum.MA_VERSION_DEPTRAIN.getType().equals(String.valueOf(sfProductResource.getMaVersion()))
                        && sfProductResource.getEndTime().before(endTime)){
                    org.springframework.data.mongodb.core.query.Criteria queryCost = org.springframework.data.mongodb.core.query.Criteria
                            .where("orderId").in(serviceOrderIds);
                    List<InstanceGaapCost> renewCost = mongoTemplate
                            .find(Query.query(queryCost), InstanceGaapCost.class);
                    if (CollectionUtil.isNotEmpty(renewCost)) {
                        List<InstanceGaapCost> renewCostList = new ArrayList<InstanceGaapCost>(Arrays.asList(renewCost.get(renewCost.size() - 1)));
                        finalCostList = deductCost(renewCostList, detailMap, detail,
                                sfProductResource, endTime, originalEndTime,
                                serviceOrder, bizBillingAccount, finalCostList, isDrp,
                                productResourceId, unsubAmount, degradeOriginal, serviceConfig);
                    } else {
                        if (CollectionUtil.isNotEmpty(costs)) {
                            finalCostList = deductCost(costs, detailMap, detail,
                                    sfProductResource, endTime, originalEndTime,
                                    serviceOrder, bizBillingAccount, finalCostList, isDrp,
                                    productResourceId, unsubAmount, degradeOriginal, serviceConfig);

                        }
                    }
                    if (Objects.nonNull(sfProductResource.getFrozenTime())
                            && SfProductEnum.FROZEN.status.equals(sfProductResource.getPreStatus())) {
                        detail.setEndTime(sfProductResource.getFrozenTime());
                    }
                }
                else {
                    if (CollectionUtil.isNotEmpty(costs)) {
                        finalCostList = this.deductCost(costs, detailMap, detail,
                                sfProductResource, endTime, originalEndTime,
                                serviceOrder, bizBillingAccount, finalCostList, isDrp,
                                productResourceId, unsubAmount, degradeOriginal, serviceConfig);

                    }
                    //MA专属资源池,未到期退订设置订单开始时间为当前时间
                    if(isDrp &&  !sfProductResource.getEndTime().before(endTime)){
                        detail.setStartTime(new Date());
                        detail.setEndTime(sfProductResource.getEndTime());
                    }
                }

                bizBillingAccountMapper.updateByPrimaryKeySelective(bizBillingAccount);
                BigDecimal finalCost = finalCostList.stream().filter(t -> t.getPretaxAmount() != null)
                        .map(InstanceGaapCost::getPretaxAmount)
                        .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                BigDecimal originalCost = finalCostList.stream().filter(t -> t.getPretaxGrossAmount() != null)
                        .map(InstanceGaapCost::getPretaxGrossAmount)
                        .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                //合并账单数据
                List<InstanceGaapCost> costList = mergeBill(bizBillingAccount, finalCost, finalCostList, serviceOrder, detail, productResourceId);
                Collection<InstanceGaapCost> instanceGaapCosts = mongoTemplate.insertAll(costList);
                serviceOrder.setFinalCost(finalCost);
                serviceOrder.setOriginalCost(originalCost);
                serviceOrder.setPayTime(cn.hutool.core.date.DateUtil.date());
                serviceOrderService.updateByPrimaryKeySelective(serviceOrder);
                detail.setAmount(finalCost);
                serviceOrderDetailMapper.updateByPrimaryKeySelective(detail);
                if (CollectionUtil.isNotEmpty(costList)) {
                    //生成账单明细
                    this.insertAccountDeal(costList, serviceOrder);
                    //生成账单周期和更新账单结束时间
                    this.updateApplyBillOrderStatus(costList);
                }


            }
        }
        else if (OrderType.APPLY.equals(serviceOrder.getType()) || OrderType.RENEW
                .equals(serviceOrder.getType())) {
            //AI专属资源池回填开始时间，结束时间。
            Date applyResourceStartTime = null;
            Date applyResourceEndTime = null;
            String serviceConfig = detail.getServiceConfig();
            cn.hutool.json.JSONObject serviceConfigObj = JSONUtil.parseObj(serviceConfig);
            if (OrderType.APPLY.equals(serviceOrder.getType())) {
                // currentTime 统一多产品开始时间
//                Date startTime = Objects.nonNull(currentTime) ? currentTime : cn.hutool.core.date.DateUtil.date();
                // 大模型的开始时间使用下订单时间，因为不然和资源表时间对不上
                Date startTime = Objects.nonNull(currentTime) ? currentTime : detail.getStartTime() ;

                if (Objects.nonNull(resourceInfo)) {
                    startTime = Convert.toDate(resourceInfo.getTime(), cn.hutool.core.date.DateUtil.date());
                    serviceConfigObj.put("resourceInfo", JSONUtil.parseObj(resourceInfo));
                    applyResourceStartTime = startTime;
                }
                Integer duration = Convert.toInt(detail.getDuration(), 0);
                //预付费才设置结束时间
                if (Objects.nonNull(startTime) && duration > 0 && BillingConstants.ChargeType.PRE_PAID.equals(
                        detail.getChargeType())) {
                    detail.setEndTime(
                            cn.hutool.core.date.DateUtil.date(startTime).offsetNew(DateField.MONTH, duration));
                    applyResourceEndTime = cn.hutool.core.date.DateUtil.date(startTime)
                            .offsetNew(DateField.MONTH, duration);
                }
                detail.setStartTime(startTime);
                detail.setServiceConfig(JSONUtil.toJsonStr(serviceConfigObj));
                serviceOrderDetailMapper.updateByPrimaryKeySelective(detail);
                boolean isBms = serviceOrder.getDetails().stream().anyMatch(d -> ProductCodeEnum.RS_BMS.getProductCode().equals(d.getServiceType()));
                if (isBms) {
                    for (ServiceOrderDetail serviceOrderDetail : serviceOrder.getDetails()) {
                        serviceOrderDetail.setStartTime(startTime);
                        serviceOrderDetail.setEndTime(detail.getEndTime());
                        serviceOrderDetailMapper.updateByPrimaryKeySelective(serviceOrderDetail);
                    }
                }else if (serviceOrder.getDetails().size() > 1 && !ProductCodeEnum.DISK.equals(serviceOrder.getDetails().get(1).getProductCode())) {
                    ServiceOrderDetail serviceOrderDetail = serviceOrder.getDetails().get(1);
                    serviceOrderDetail.setStartTime(startTime);
                    serviceOrderDetail.setEndTime(detail.getEndTime());
                    serviceOrderDetailMapper.updateByPrimaryKeySelective(serviceOrderDetail);
                    //evs更新
                    if (serviceOrder.getDetails().size()==3){
                        ServiceOrderDetail serviceOrderDetailEvs = serviceOrder.getDetails().get(2);
                        serviceOrderDetailEvs.setStartTime(startTime);
                        serviceOrderDetailEvs.setEndTime(detail.getEndTime());
                        serviceOrderDetailMapper.updateByPrimaryKeySelective(serviceOrderDetailEvs);
                    }
                } else if (serviceOrder.getDetails().size() > 1 && ProductCodeEnum.DISK.equals(serviceOrder.getDetails().get(1).getProductCode())) {
                    for (int i = 1; i < serviceOrder.getDetails().size(); i++) {
                        ServiceOrderDetail serviceOrderDetail = serviceOrder.getDetails().get(i);
                        serviceOrderDetail.setStartTime(startTime);
                        serviceOrderDetail.setEndTime(detail.getEndTime());
                        serviceOrderDetailMapper.updateByPrimaryKeySelective(serviceOrderDetail);
                    }
                }
            }
            if (BillingConstants.ChargeType.PRE_PAID.equals(detail.getChargeType())) {
                Criteria criteria = new Criteria();
                String orderSn = serviceOrder.getOrderSn();
                criteria.put("orderSn", orderSn);
                List<ServiceOrderPriceDetail> serviceOrderPriceDetails = serviceOrderPriceDetailMapper
                        .selectByParams(criteria);
                List<InstanceGaapCost> costs = Lists.newArrayList();
                //获取订单明细对应的CUE
                Long cueValue = null;
                if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode().equals(detail.getServiceType())
                        && serviceConfigObj.getJSONObject("data") != null
                        && !serviceConfigObj.getJSONObject("data").isNull("DRP")) {
                    String specName = serviceConfigObj.getJSONObject("data").getJSONObject("DRP").getStr("spec");
                    cueValue = getDRPCueValue(specName);
                }
                // 优惠卷

                BigDecimal couponAmount = Optional
                        .ofNullable(this.getCouponPriceDetail(serviceOrderPriceDetails))
                        .map(ServiceOrderPriceDetail::getCouponAmount).orElse(BigDecimal.ZERO);
                if (OrderType.RENEW.equals(serviceOrder.getType())) {
                    resourceInfo = JSONUtil.parseObj(detail.getServiceConfig())
                            .get("resourceInfo", ResourceInfo.class, false);
                }

                // 专属资源池实例ID取得
                Map<String, String> hpcDrpResourceMap = new HashMap<>();
                if (priceDetailQuantityFlg) {
                    QueryHpcDrpResourceRequest queryHpcDrpResourceRequest = new QueryHpcDrpResourceRequest();
                    queryHpcDrpResourceRequest.setHpcClusterID(serviceOrder.getClusterId());
                    hpcDrpResourceMap = hpcRemoteService.getHpcDrpResourceInfo(queryHpcDrpResourceRequest);
                    log.debug("HPC专属资源池集群ID：[{}], HPC专属资源池所有实例：[{}]", serviceOrder.getClusterId(), hpcDrpResourceMap);
                }

                List<String> resourceIds = Optional.ofNullable(resourceInfo)
                        .map(ResourceInfo::getIdNames)
                        .orElseGet(Lists::newArrayList)
                        .stream()
                        .map(ResourceInfo.IdName::getId)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                List<String> resourceNames = Optional.ofNullable(resourceInfo)
                        .map(ResourceInfo::getIdNames)
                        .orElseGet(Lists::newArrayList)
                        .stream()
                        .map(ResourceInfo.IdName::getName)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                BigDecimal duration = Convert
                        .toBigDecimal(detail.getDuration(), BigDecimal.ONE);
                boolean isOnePriceComputed = false;
                if (priceDetailQuantityFlg) {
                    log.info("HPC专属资源池出账调查：[{}]", JSONUtil.toJsonStr(serviceOrderPriceDetails));
                }
                List<BizCouponAccount> bizCouponAccounts = bizCouponAccountMapper.selectByOrderId(serviceOrder.getId(), bizBillingAccount.getId());
                BizCouponAccount bizCouponAccount = null;
                if (CollectionUtil.isNotEmpty(bizCouponAccounts)) {
                    bizCouponAccount = bizCouponAccounts.get(0);
                }

                //判断是不是HPC预部署，如果是HPC预部署需要调用同步资源接口
                //hpc-drp-standard
                log.info("申请的产品类型,{}", detail.getApplyType());
                if (ApplyTypeEnum.HPC_DRP_STANDARD.getType().equals(detail.getApplyType())) {
                    log.info("审批同步内置弹性文件的结束时间");
                    //同步内置弹性文件的过期时间
                    List<ResShare> shareHPCSFS = hpcRemoteService.getDefaultShareHPCSFS(serviceOrder.getClusterId());
                    if (shareHPCSFS.size() > 0) {
                        log.info("关联的内置弹性文件,{}", shareHPCSFS.toString());
                        for (ResShare shareSfs : shareHPCSFS) {
                            shareSfs.setStartTime(detail.getStartTime());
                            shareSfs.setEndTime(detail.getEndTime());
                            shareRemoteService.updateByPrimaryKey(shareSfs);
                        }

                    }
                }

                if ((ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode().equals(detail.getServiceType()))
                        && (OrderType.APPLY.equals(serviceOrder.getType()) || OrderType.RENEW.equals(serviceOrder.getType()))) {
                    costs = maApplyAprove(serviceOrderPriceDetails, costs, detail, serviceOrder,
                            priceDetailQuantityFlg, orderSn,
                            bizBillingAccount, quantity, serviceConfigObj,
                            resourceIds, resourceNames, cueValue,
                            duration, isOnePriceComputed, isOnePriceComputed,
                            couponAmount, serviceConfig, applyResourceStartTime, applyResourceEndTime, bizCouponAccount);
                } else if (ProductCodeEnum.ECS.getProductCode().equals(detail.getServiceType()) && (OrderType.APPLY.equals(serviceOrder.getType()) || OrderType.RENEW.equals(serviceOrder.getType()))) {
                    costs = multiQuantityApproved(serviceOrder, serviceOrderPriceDetails, bizBillingAccount, costs, applyResourceStartTime, applyResourceEndTime);
                }else if (ProductCodeEnum.RDS.getProductCode().equals(detail.getServiceType()) && (OrderType.APPLY.equals(serviceOrder.getType()) || OrderType.RENEW.equals(serviceOrder.getType()))) {
                    costs = multiQuantityApproved(serviceOrder, serviceOrderPriceDetails, bizBillingAccount, costs, applyResourceStartTime, applyResourceEndTime);
                } else {
                    for (int i = 0; i < quantity.intValue(); i++) {
                        for (ServiceOrderPriceDetail priceDetail : serviceOrderPriceDetails) {
                            String priceType = priceDetail.getPriceType();
                            String fixedMonth = priceDetail.getFixedMonth();
                            InstanceGaapCost cost = new InstanceGaapCost();
                            cost.setQuantity(priceDetailQuantityFlg ? priceDetail.getQuantity() : detail.getQuantity());
                            cost.setOrderId(serviceOrder.getId().toString());
                            cost.setOrderSn(orderSn);
                            cost.setPayTime(new Date());
                            cost.setUsageStartDate(detail.getStartTime());
                            cost.setUsageEndDate(detail.getEndTime());

                            if ("expired".equals(priceDetail.getType())) {
                                // 超期补扣订单入账开始时间/结束时间只记录补扣的时间
                                cost.setUsageStartDate(priceDetail.getStartTime());
                                cost.setUsageEndDate(priceDetail.getEndTime());
                            }

                            long offsetDay = cn.hutool.core.date.DateUtil.betweenDay(priceDetail.getStartTime(), priceDetail.getEndTime(),
                                    false);
                            offsetDay = OrderType.EXPIRED.equals(priceDetail.getType()) ? offsetDay + 1 : offsetDay;
                            cost.setUsageCount(offsetDay + "天");

                            cost.setBillType(BillType.fromChargeTypeEnum(detail.getChargeType()));
                            cost.setBillingCycle(
                                    cn.hutool.core.date.DateUtil.format(cn.hutool.core.date.DateUtil.date(), "yyyy-MM"));
                            cost.setOrgSid(serviceOrder.getOrgSid());
                            cost.setOwnerId(serviceOrder.getOwnerId().toString());
                            cost.setBillNo(NoUtil.generateNo("ZD"));
                            cost.setCurrency("CNY");
                            cost.setPriceType(priceType);
                            cost.setProductCode(priceDetail.getProductCode());

                            String productName = ProductCodeEnum.toDesc(priceDetail.getProductCode());
                            log.info("所属产品Code：[{}]", priceDetail.getProductCode());
                            if (ProductCodeEnum.HPC_DRP.getProductCode().equals(priceDetail.getProductCode())) {
                                productName = serviceOrder.getProductName();
                                log.info("HPC_DRP 所属产品名称：[{}]", productName);
                            }
                            boolean isRds= checkIsRdsByOrderDetailId(priceDetail.getOrderDetailId());
                            if(isRds){
                                productName = ProductCodeEnum.toDesc(ProductCodeEnum.RDS.getProductCode());
                                if(!ProductCodeEnum.RDS.getProductCode().equals(priceDetail.getProductCode())){
                                    productName="云硬盘 RDS";
                                }
                            }
                            cost.setProductName(productName);
                            cost.setBillSource("platform");
                            cost.setUserAccountId(bizBillingAccount.getId());
                            Long adminSid = bizBillingAccount.getAdminSid();
                            cn.com.cloudstar.rightcloud.core.pojo.dto.user.User user = userMapper.selectByPrimaryKey(adminSid);
                            String accountName = bizBillingAccount.getCreatedBy();
                            if (user != null) {
                                accountName = user.getAccount();
                            }
                            cost.setUserAccountName(accountName);
                            cost.setPrice(priceDetail.getPrice());
                            cost.setConfiguration(priceDetail.getBillingSpec());
                            cost.setEntityId(serviceOrder.getEntityId());
                            cost.setEntityName(serviceOrder.getEntityName());
                            cost.setType(serviceOrder.getType());
                            cost.setOrderSourceSn(serviceOrder.getOrderSourceSn());
                            // originalCost 原价
                            BigDecimal originalCost = BigDecimal.ZERO;
                            BigDecimal finalCost = BigDecimal.ZERO;

                            if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode().equals(detail.getServiceType())) {
                                originalCost = priceDetail.getOriginalCost().multiply(quantity);
                                // tradeFinalCost 最终价格
                                if (OrderType.EXPIRED.equals(priceDetail.getType())) {
                                    finalCost = priceDetail.getAmount();
                                } else {
                                    finalCost = priceDetail.getTradePrice().multiply(quantity);
                                }
                                if (ApplyTypeEnum.DEPTRAIN.getType().equals(detail.getApplyType())) {
                                    ResMaPoolVO resMaPoolById = maRemoteService.getResMaPoolById(serviceOrder.getClusterId());
                                    log.info("资源ID,{}", resMaPoolById.getName());
                                    cost.setInstanceId(resMaPoolById.getName());
                                    cost.setResourceId(resMaPoolById.getName());
                                } else {
                                    cost.setInstanceId(resourceIds.get(0));
                                    cost.setResourceId(resourceIds.get(0));
                                }
                                cost.setInstanceName(serviceOrder.getName());
                            }
                            else if (ProductCodeEnum.CCE.getProductCode().equals(detail.getServiceType())) {
                                finalCost = priceDetail.getTradePrice();
                                if (OrderType.EXPIRED.equals(priceDetail.getType())) {
                                    finalCost = priceDetail.getAmount();
                                }
                                originalCost = priceDetail.getOriginalCost();
                                couponAmount = Objects.isNull(priceDetail.getCouponAmount()) ? BigDecimal.ZERO : priceDetail.getCouponAmount();
                                List<Long> resIds = JSONObject.parseArray(priceDetail.getRefInstanceId(), Long.class);
                                SfProductResource resource = sfProductResourceMapper.selectByPrimaryKey(resIds.get(0));
                                cost.setInstanceId(resource.getId().toString());
                                setResourceInfo(resource, cost);
                            }
                            else if (ProductCodeEnum.EIP.getProductCode().equals(detail.getServiceType())) {
                                finalCost = priceDetail.getTradePrice();
                                if (OrderType.EXPIRED.equals(priceDetail.getType())) {
                                    finalCost = priceDetail.getAmount();
                                }
                                originalCost = priceDetail.getOriginalCost();
                                couponAmount = Objects.isNull(priceDetail.getCouponAmount()) ? BigDecimal.ZERO : priceDetail.getCouponAmount();
                                List<Long> resIds = JSONObject.parseArray(priceDetail.getRefInstanceId(), Long.class);
                                SfProductResource resource = sfProductResourceMapper.selectByPrimaryKey(resIds.get(0));
                                cost.setInstanceId(resource.getId().toString());
                                setResourceInfo(resource, cost);
                            }
                            else if (ProductCodeEnum.SFS_TURBO.getProductCode().equals(detail.getServiceType())
                                    ||ProductCodeEnum.RDS.getProductCode().equals(detail.getServiceType())
                                    ||ProductCodeEnum.DCS.getProductCode().equals(detail.getServiceType())
                                    ||ProductCodeEnum.CBR.getProductCode().equals(detail.getServiceType())
                                    ||ProductCodeEnum.AS_GROUP.getProductCode().equals(detail.getServiceType())
                                    ||ProductCodeEnum.ELB.getProductCode().equals(detail.getServiceType())
                            ){
                                finalCost = priceDetail.getTradePrice();
                                if (OrderType.EXPIRED.equals(priceDetail.getType())) {
                                    finalCost = priceDetail.getAmount();
                                }
                                originalCost = priceDetail.getOriginalCost();
                                couponAmount = Objects.isNull(priceDetail.getCouponAmount()) ? BigDecimal.ZERO : priceDetail.getCouponAmount();
                                List<Long> resIds = JSONObject.parseArray(priceDetail.getRefInstanceId(), Long.class);
                                SfProductResource resource = sfProductResourceMapper.selectByPrimaryKey(resIds.get(0));
                                cost.setInstanceId(resource.getId().toString());
                                setResourceInfo(resource, cost);
                            }
                            else if (ProductCodeEnum.DISK.getProductCode().equals(detail.getServiceType())) {
                                finalCost = priceDetail.getTradePrice();
                                if (OrderType.EXPIRED.equals(priceDetail.getType())) {
                                    finalCost = priceDetail.getAmount();
                                }
                                originalCost = priceDetail.getOriginalCost();
                                couponAmount = Objects.isNull(priceDetail.getCouponAmount()) ? BigDecimal.ZERO : priceDetail.getCouponAmount();
                                List<Long> resIds = JSONObject.parseArray(priceDetail.getRefInstanceId(), Long.class);
                                SfProductResource resource = sfProductResourceMapper.selectByPrimaryKey(resIds.get(0));
                                cost.setInstanceId(resource.getId().toString());
                                setResourceInfo(resource, cost);
                            }
                            else if (ProductCodeEnum.RS_BMS.getProductCode().equals(detail.getServiceType())) {
                                finalCost = priceDetail.getTradePrice();
                                if (OrderType.EXPIRED.equals(priceDetail.getType())) {
                                    finalCost = priceDetail.getAmount();
                                }
                                originalCost = priceDetail.getOriginalCost();
                                couponAmount = Objects.isNull(priceDetail.getCouponAmount()) ? BigDecimal.ZERO : priceDetail.getCouponAmount();
                                List<Long> resIds = JSONObject.parseArray(priceDetail.getRefInstanceId(), Long.class);
                                for (Long resId : resIds) {
                                    SfProductResource resource = sfProductResourceMapper.selectByPrimaryKey(resId);
                                    cost.setInstanceId(resource.getId().toString());
                                    cost.setInstanceName(resource.getClusterName());
                                    cost.setResourceId(resource.getInstanceUuid());
                                }
                            } else {
                                originalCost = priceDetail.getOriginalCost();
                                // tradeFinalCost 最终价格
                                finalCost = priceDetail.getTradePrice();
                                couponAmount = Objects.isNull(priceDetail.getCouponAmount()) ? BigDecimal.ZERO : priceDetail.getCouponAmount();
                                cost.setInstanceId(resourceIds.size() > i ? resourceIds.get(i) : null);
                                cost.setInstanceName(resourceNames.size() > i ? resourceNames.get(i) : null);
                                cost.setResourceId(resourceIds.size() > i ? resourceIds.get(i) : null);
                            }
                            if (PriceType.RESOURCE.equals(priceType)) {

                                String finalCueValue = null;
                                if (cueValue != null) {
                                    long dur = detail.getEndTime().getTime() - detail.getStartTime().getTime();
                                    finalCueValue = BigInteger.valueOf(cueValue)
                                            .multiply(NumberUtil.div(BigDecimal.valueOf(dur),
                                                            1000 * 60 * 60)
                                                    .setScale(0, BigDecimal.ROUND_HALF_UP)
                                                    .toBigInteger())
                                            .toString();
                                }
                                cost.setCueValue(finalCueValue);

                                if (Objects.equals(priceDetail.getServiceType(), "contract")) {
                                    BigDecimal originalTmp = NumberUtil.div(originalCost, quantity)
                                            .setScale(3, BigDecimal.ROUND_HALF_UP);
                                    BigDecimal finalTmp = NumberUtil.div(finalCost, quantity)
                                            .setScale(3, BigDecimal.ROUND_HALF_UP);
                                    if (i + 1 == quantity.intValue()) {
                                        originalCost = NumberUtil.sub(originalCost,
                                                NumberUtil.mul(originalTmp, quantity.intValue() - 1));
                                        finalCost = NumberUtil.sub(finalCost,
                                                NumberUtil.mul(finalTmp, quantity.intValue() - 1));
                                    } else {
                                        originalCost = originalTmp;
                                        finalCost = finalTmp;
                                    }
                                } else {
                                    if (!BooleanEnum.YES.getCode().equals(fixedMonth)
                                            && !ProductCodeEnum.RDS.getProductCode().equals(detail.getServiceType())) {
                                        if (!ProductCodeEnum.RS_BMS.getProductCode().equals(detail.getServiceType())) {
                                            originalCost = NumberUtil.mul(originalCost, duration);
                                        }
                                        finalCost = NumberUtil.mul(finalCost, duration);
                                    }
                                    if (OrderType.RENEW.equals(serviceOrder.getType())) {
                                        originalCost = NumberUtil.div(originalCost, quantity);
                                        finalCost = NumberUtil.div(finalCost, quantity);
                                    }
                                }
                            }
                            if (PriceType.SERVICE.equals(priceType)) {
                                if (!BooleanEnum.YES.getCode().equals(fixedMonth)) {
                                    originalCost = NumberUtil
                                            .mul(priceDetail.getOriginalCost(), duration);
                                    finalCost = NumberUtil.mul(priceDetail.getTradePrice(), duration);
                                }
                                if (OrderType.RENEW.equals(serviceOrder.getType())) {
                                    originalCost = NumberUtil.div(originalCost, quantity);
                                    finalCost = NumberUtil.div(finalCost, quantity);
                                }
                            }
                            if (PriceType.EXTRA_CONFIG.equals(priceType)) {
                                BigDecimal oncePrice = priceDetail.getOncePrice();
                                if (Objects.nonNull(oncePrice)) {
                                    if (isOnePriceComputed) {
                                        originalCost = BigDecimal.ZERO;
                                        finalCost = BigDecimal.ZERO;
                                    } else {
                                        originalCost = oncePrice;
                                        finalCost = oncePrice;
                                        // isOnePriceComputed = true;
                                    }
                                } else {
                                    if (!BooleanEnum.YES.getCode().equals(fixedMonth)) {
                                        originalCost = NumberUtil
                                                .mul(priceDetail.getOriginalCost(), duration);
                                        finalCost = NumberUtil
                                                .mul(priceDetail.getOriginalCost(), duration);
                                    }
                                    if (OrderType.RENEW.equals(serviceOrder.getType())) {
                                        originalCost = NumberUtil.div(originalCost, quantity);
                                        finalCost = NumberUtil.div(finalCost, quantity);
                                    }
                                }
                            }
                            cost.setPricingDiscount(NumberUtil.sub(originalCost, finalCost));
                            if (NumberUtil.isGreater(couponAmount, BigDecimal.ZERO)) {
                                if (NumberUtil.isGreaterOrEqual(couponAmount, finalCost)) {
                                    couponAmount = NumberUtil.sub(couponAmount, finalCost);
                                    cost.setCouponDiscount(finalCost);
                                    finalCost = BigDecimal.ZERO;
                                } else {
                                    finalCost = NumberUtil.sub(finalCost, couponAmount);
                                    cost.setCouponDiscount(couponAmount);
                                    if (!ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode().equals(priceDetail.getProductCode())) {
                                        couponAmount = BigDecimal.ZERO;
                                    }
                                }
                            }
                            // HPC专属资源池特殊处理
                            if (priceDetailQuantityFlg) {
                                originalCost = priceDetail.getOriginalCost();
                                finalCost = priceDetail.getAmount();
                                cost.setCouponDiscount(priceDetail.getCouponAmount());
                                // cost.setPricingDiscount(NumberUtil.sub(originalCost, finalCost));
                                cost.setPricingDiscount(NumberUtil.sub(originalCost, finalCost,
                                        Optional.ofNullable(cost.getCouponDiscount()).orElse(BigDecimal.ZERO)));
                                // 资源实例ID取得
                                String key = priceDetail.getProductCode() + priceDetail.getBillingSpec();
                                cost.setResourceId(hpcDrpResourceMap.get(key));
                                //如果实例名称为空，取申请单名称
                                if (Objects.isNull(cost.getInstanceName())) {
                                    cost.setInstanceName(serviceOrder.getName());
                                }
                                log.info("HPC专属资源池实例：Key：[{}], Value:[{}]", key, cost.getResourceId());
                                // 专属更新priceDetail时间
                                priceDetail.setStartTime(detail.getStartTime());
                                priceDetail.setEndTime(detail.getEndTime());
                            }
                            //关联不计费产品设置实付金额为0
                            if (Objects.nonNull(serviceOrder.getChargingType()) && SALE_TYPE.equalsIgnoreCase(serviceOrder.getChargingType())) {
                                cost.setCashAmount(BigDecimal.ZERO);
                                cost.setCreditAmount(BigDecimal.ZERO);
                                cost.setCouponAmount(BigDecimal.ZERO);
                                finalCost = BigDecimal.ZERO;
                                cost.setChargingType(SALE_TYPE);
                            } else {
                                cost.setChargingType(NORMAL_TYPE);
                                cost.setEraseZeroAmount(BigDecimalUtil.getEraseZeroColumn(finalCost));
                                setUsedCost(bizBillingAccount, cost, BigDecimalUtil.remainTwoPointAmount(finalCost));
                                bizBillingAccount.setBalance(
                                        NumberUtil.sub(bizBillingAccount.getBalance(), BigDecimalUtil.remainTwoPointAmount(cost.getCashAmount()))
                                );
                                bizBillingAccount.setCreditLine(
                                        NumberUtil.sub(bizBillingAccount.getCreditLine(), BigDecimalUtil.remainTwoPointAmount(cost.getCreditAmount()))
                                );
                                bizBillingAccount.setBalanceCash(
                                        NumberUtil.sub(bizBillingAccount.getBalanceCash(), BigDecimalUtil.remainTwoPointAmount(cost.getCouponAmount()))
                                );
                            }
                            cost.setPretaxGrossAmount(originalCost);
                            cost.setPretaxAmount(BigDecimalUtil.remainTwoPointAmount(finalCost));
                            cost.setBizBillingAccount(
                                    BeanUtil.toBean(bizBillingAccount, BizBillingAccount.class));
                            cost.setType(serviceOrder.getType());

                            JSONObject data = JSONObject.parseObject(serviceConfig);
                            if (data != null) {
                                Long cloudEnvId = data.getLong("cloudEnvId");
                                if (cloudEnvId != null) {
                                    CloudEnv cloudEnv = cloudEnvRemoteService.selectByPrimaryKey(cloudEnvId);
                                    if (Objects.isNull(cloudEnv)) {
                                        ResCloudEnv resCloudEnv = cloudEnvRemoteService.selectResCloudEnvById(cloudEnvId);
                                        cost.setCloudEnvId(resCloudEnv.getId());
                                        cost.setCloudEnvName(resCloudEnv.getCloudEnvName());
                                        cost.setCloudEnvType("HCSO");
                                        cost.setRegion(resCloudEnv.getRegion());
                                    }else {
                                        cost.setCloudEnvId(cloudEnv.getId());
                                        cost.setCloudEnvName(cloudEnv.getCloudEnvName());
                                        cost.setCloudEnvType(cloudEnv.getCloudEnvType());
                                        cost.setRegion(cloudEnv.getRegion());
                                    }
                                }
                            } else {
                                cost.setCloudEnvId(detail.getCloudEnvId());
                                cost.setCloudEnvName(detail.getCloudEnvName());
                                cost.setCloudEnvType(detail.getCloudEnvType());
                                cost.setRegion(detail.getRegion());
                            }

                            this.processCostComputingPower(cost, detail);
                            this.processDistributorName(cost, serviceOrder);
                            costs.add(cost);
                            //AI专属资源池回填开始时间和结束时间
                            if (applyResourceStartTime != null) {
                                priceDetail.setStartTime(applyResourceStartTime);
                                priceDetail.setEndTime(applyResourceEndTime);
                            }

                            //priceDetail 设置实际扣除的 余额，充值现金券，信用额度
                            if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode().equals(detail.getServiceType())) {
                                priceDetail.setPayBalance(NumberUtil.add(priceDetail.getPayBalance(), cost.getCashAmount()));
                                priceDetail.setPayBalanceCash(NumberUtil.add(priceDetail.getPayBalanceCash(), cost.getCouponAmount()));
                                priceDetail.setPayCreditLine(NumberUtil.add(priceDetail.getPayCreditLine(), cost.getCreditAmount()));
                                serviceOrderPriceDetailMapper.updateByPrimaryKeySelective(priceDetail);

                                if (OrderType.RENEW.equals(priceDetail.getType()) && ApplyTypeEnum.DEPTRAIN.getType().equals(detail.getApplyType())) {
                                    ServiceOrderResourceRef serviceOrderResourceRef = new ServiceOrderResourceRef();
                                    serviceOrderResourceRef.setOrderDetailId(detail.getId());
                                    HashMap<String, Object> condition = new HashMap<>(2);
                                    condition.put("resourceId", serviceConfigObj.get("productResourceId"));
                                    condition.put("type", detail.getServiceType());
                                    serviceOrderResourceRefMapper.updateByParamsSelective(serviceOrderResourceRef, condition);
                                }
                            } else {
                                priceDetail.setPayBalance(cost.getCashAmount());
                                priceDetail.setPayBalanceCash(cost.getCouponAmount());
                                priceDetail.setPayCreditLine(cost.getCreditAmount());
                                serviceOrderPriceDetailMapper.updateByPrimaryKeySelective(priceDetail);
                            }
                        }
                    }
                }

                /*if (ProductCodeEnum.DCS.getProductCode().equals(detail.getServiceType()) && OrderType.RENEW.equals(serviceOrder.getType())
                        && serviceOrderPriceDetails.size() == 1 && costs.size() > 1) {
                    BigDecimal cashAmount = BigDecimal.ZERO, couponAmount1 = BigDecimal.ZERO, creditAmount = BigDecimal.ZERO;
                    for (InstanceGaapCost cost : costs) {
                        cashAmount = cost.getCashAmount().add(cashAmount);
                        couponAmount1 = cost.getCouponAmount().add(couponAmount1);
                        creditAmount = cost.getCreditAmount().add(creditAmount);
                    }
                    ServiceOrderPriceDetail priceDetail = serviceOrderPriceDetails.get(0);
                    priceDetail.setPayBalance(cashAmount);
                    priceDetail.setPayBalanceCash(couponAmount1);
                    priceDetail.setPayCreditLine(creditAmount);
                    serviceOrderPriceDetailMapper.updateByPrimaryKeySelective(priceDetail);
                }*/

                log.info("更新账户前的金额额度:充值现金券:{},现金余额:{},信用额度:{}", bizBillingAccount.getBalanceCash(), bizBillingAccount.getBalance(),
                        bizBillingAccount.getCreditLine());
                bizBillingAccountMapper.updateByPrimaryKeySelective(bizBillingAccount);
                updateUserBusinessTag(bizBillingAccount);

                if (Objects.nonNull(bizCouponAccount)) {
                    bizCouponAccountMapper.updateByPrimaryKeySelective(bizCouponAccount);
                }
                try {
                    if (priceDetailQuantityFlg) {
                        log.info("HPC专属资源池出账调查：账单数量[{}]", costs.size());
                    }

                    /*if (OrderType.APPLY.equals(serviceOrder.getType()) && ProductCodeEnum.DCS.getProductCode().equals(detail.getServiceType())) {
                        log.info("dcs申请cost去重");
                        List<InstanceGaapCost> list = new ArrayList<>();
                        List<String> strs = new ArrayList<>();
                        for (InstanceGaapCost cost : costs) {
                            String str = cost.getOrderSn() + "," + cost.getOrderId();
                            if (strs.contains(str)) {
                                continue;
                            }
                            list.add(cost);
                            strs.add(str);
                        }
                        costs = list;
                    }*/

                    Collection<InstanceGaapCost> instanceGaapCosts = mongoTemplate.insertAll(costs);
                    if (!CollectionUtil.isEmpty(instanceGaapCosts)) {
                        List<InstanceGaapCost> costList = new ArrayList<InstanceGaapCost>(instanceGaapCosts);
                        this.insertBillCycleInfo(costList);
                        this.insertAccountDeal(costs, serviceOrder);
                    } else {
                        log.error("ServiceOrderServiceImpl------processProduct-----------failed---instanceGaapCosts isEmpty");
                    }
                } catch (Exception e) {
                    log.error("入账失败", e);
                    if (priceDetailQuantityFlg) {
                        throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_7836561));
                    }
                }
            }
        }
        else if (OrderType.UPGRADE.equalsIgnoreCase(serviceOrder.getType()) || OrderType.DEGRADE.equalsIgnoreCase(
                serviceOrder.getType())) {
            if (ProductCodeEnum.HPC_DRP.getProductCode().equalsIgnoreCase(detail.getServiceType())) {
            } else if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode().equals(detail.getServiceType())) {
            }
        } else if (OrderType.MODIFY.equals(serviceOrder.getType()) && BillingConstants.ChargeType.PRE_PAID.equals(detail.getChargeType())) {
            this.approvalModify(serviceOrder, bizBillingAccount);
        }
    }

    private List<InstanceGaapCost> multiQuantityApproved(ServiceOrder serviceOrder, List<ServiceOrderPriceDetail> serviceOrderPriceDetails
            , BizBillingAccount bizBillingAccount, List<InstanceGaapCost> costs, Date applyResourceStartTime, Date applyResourceEndTime) {
        Map<Long, ServiceOrderDetail> detailMap = serviceOrder.getDetails().stream().collect(Collectors.toMap(ServiceOrderDetail::getId, Function.identity()));
        for (ServiceOrderPriceDetail priceDetail : serviceOrderPriceDetails) {
            ServiceOrderDetail detail = detailMap.get(priceDetail.getOrderDetailId());
            for (int i = 0; i < detail.getQuantity().intValue(); i++) {
                String priceType = priceDetail.getPriceType();
                String fixedMonth = priceDetail.getFixedMonth();
                InstanceGaapCost cost = new InstanceGaapCost();
                cost.setQuantity(1);
                cost.setOrderId(serviceOrder.getId().toString());
                cost.setOrderSn(serviceOrder.getOrderSn());
                cost.setPayTime(new Date());
                cost.setUsageStartDate(detail.getStartTime());
                cost.setUsageEndDate(detail.getEndTime());
                long offsetDay = cn.hutool.core.date.DateUtil.betweenDay(priceDetail.getStartTime(), priceDetail.getEndTime(),
                                                                         false);
                offsetDay = OrderType.EXPIRED.equals(priceDetail.getType()) ? offsetDay + 1 : offsetDay;
                cost.setUsageCount(offsetDay + "天");

                cost.setBillType(BillType.fromChargeTypeEnum(detail.getChargeType()));
                cost.setBillingCycle(
                        cn.hutool.core.date.DateUtil.format(cn.hutool.core.date.DateUtil.date(), "yyyy-MM"));
                cost.setOrgSid(serviceOrder.getOrgSid());
                cost.setOwnerId(serviceOrder.getOwnerId().toString());
                cost.setBillNo(NoUtil.generateNo("ZD"));
                cost.setCurrency("CNY");
                cost.setPriceType(priceType);
                cost.setProductCode(priceDetail.getProductCode());

                String productName = ProductCodeEnum.toDesc(priceDetail.getProductCode());
                log.info("所属产品Code：[{}]", priceDetail.getProductCode());
                if (ProductCodeEnum.HPC_DRP.getProductCode().equals(priceDetail.getProductCode())) {
                    productName = serviceOrder.getProductName();
                    log.info("HPC_DRP 所属产品名称：[{}]", productName);
                }
                boolean isRds= checkIsRdsByOrderDetailId(priceDetail.getOrderDetailId());
                if(isRds){
                    productName = ProductCodeEnum.toDesc(ProductCodeEnum.RDS.getProductCode());
                    if(!ProductCodeEnum.RDS.getProductCode().equals(priceDetail.getProductCode())){
                        productName="云硬盘 RDS";
                    }
                }
                cost.setProductName(productName);
                cost.setBillSource("platform");
                cost.setUserAccountId(bizBillingAccount.getId());
                Long adminSid = bizBillingAccount.getAdminSid();
                User user = userMapper.selectByPrimaryKey(adminSid);
                String accountName = bizBillingAccount.getCreatedBy();
                if (user != null) {
                    accountName = user.getAccount();
                }
                cost.setUserAccountName(accountName);
                cost.setPrice(priceDetail.getPrice());
                cost.setConfiguration(priceDetail.getBillingSpec());
                cost.setEntityId(serviceOrder.getEntityId());
                cost.setEntityName(serviceOrder.getEntityName());
                cost.setType(serviceOrder.getType());
                cost.setOrderSourceSn(serviceOrder.getOrderSourceSn());
                // originalCost 原价
                BigDecimal originalCost = BigDecimal.ZERO;
                BigDecimal finalCost = BigDecimal.ZERO;
                BigDecimal discount = priceDetail.getDiscount();

                if (ProductCodeEnum.ECS.getProductCode().equals(detail.getServiceType())) {
                    finalCost = priceDetail.getAmount();
                    originalCost = priceDetail.getOriginalCost();
                    List<Long> resIds = JSONObject.parseArray(priceDetail.getRefInstanceId(), Long.class);
                    if (resIds.size() > i) {
                        SfProductResource resource = sfProductResourceMapper.selectByPrimaryKey(resIds.get(i));
                        cost.setInstanceId(resource.getId().toString());
                        setResourceInfo(resource, cost);
                    }
                }
                else if (ProductCodeEnum.CCE.getProductCode().equals(detail.getServiceType())) {
                    finalCost = priceDetail.getTradePrice();
                    originalCost = priceDetail.getOriginalCost();
                    List<Long> resIds = JSONObject.parseArray(priceDetail.getRefInstanceId(), Long.class);
                    SfProductResource resource = sfProductResourceMapper.selectByPrimaryKey(resIds.get(0));
                    cost.setInstanceId(resource.getId().toString());
                    setResourceInfo(resource, cost);
                }
                else if (ProductCodeEnum.EIP.getProductCode().equals(detail.getServiceType())) {
                    finalCost = priceDetail.getTradePrice();
                    originalCost = priceDetail.getOriginalCost();
                    List<Long> resIds = JSONObject.parseArray(priceDetail.getRefInstanceId(), Long.class);
                    SfProductResource resource = sfProductResourceMapper.selectByPrimaryKey(resIds.get(0));
                    cost.setInstanceId(resource.getId().toString());
                    setResourceInfo(resource, cost);
                }
                else if (ProductCodeEnum.SFS_TURBO.getProductCode().equals(detail.getServiceType())
                        ||ProductCodeEnum.RDS.getProductCode().equals(detail.getServiceType())
                        ||ProductCodeEnum.DCS.getProductCode().equals(detail.getServiceType())
                        ||ProductCodeEnum.CBR.getProductCode().equals(detail.getServiceType())
                ) {
                    finalCost = priceDetail.getTradePrice();
                    originalCost = priceDetail.getOriginalCost();
                    List<Long> resIds = JSONObject.parseArray(priceDetail.getRefInstanceId(), Long.class);
                    SfProductResource resource = sfProductResourceMapper.selectByPrimaryKey(resIds.get(0));
                    cost.setInstanceId(resource.getId().toString());
                    setResourceInfo(resource, cost);
                }
                else if (ProductCodeEnum.DISK.getProductCode().equals(detail.getServiceType())) {
                    finalCost = priceDetail.getTradePrice();
                    if (ProductCodeEnum.ECS.getProductCode().equals(serviceOrder.getDetails().get(0).getServiceType())) {
                        finalCost = priceDetail.getAmount();
                    }
                    originalCost = priceDetail.getOriginalCost();
                    List<Long> resIds = JSONObject.parseArray(priceDetail.getRefInstanceId(), Long.class);
                    SfProductResource resource = sfProductResourceMapper.selectByPrimaryKey(resIds.get(i));
                    cost.setInstanceId(resource.getId().toString());
                    setResourceInfo(resource, cost);
                }
//                else if (ProductCodeEnum.RS_BMS.getProductCode().equals(detail.getServiceType())) {
//                    finalCost = priceDetail.getTradePrice();
//                    originalCost = priceDetail.getOriginalCost();
//                    List<Long> resIds = JSONObject.parseArray(priceDetail.getRefInstanceId(), Long.class);
//                    if (resIds.size() > i) {
//                        SfProductResource resource = sfProductResourceMapper.selectByPrimaryKey(resIds.get(i));
//                        cost.setInstanceId(resource.getId().toString());
//                        List<ResBms> resBms = vmRemoteService.getBmsStatusByIds(Collections.singletonList(resource.getClusterId()));
//                        if (CollectionUtils.isNotEmpty(resBms)) {
//                            cost.setInstanceName(resBms.get(0).getName());
//                            cost.setResourceId(resBms.get(0).getUuid());
//                        }
//                    }
//                }
                else {
                    originalCost = priceDetail.getOriginalCost();
                    // tradeFinalCost 最终价格
                    finalCost = priceDetail.getTradePrice();
                }
                //如果是rds
                if (isRds) {
                    finalCost = priceDetail.getAmount();
                    originalCost = priceDetail.getOriginalCost();
                    List<Long> resIds = JSONObject.parseArray(priceDetail.getRefInstanceId(), Long.class);
                    if (resIds.size() > i) {
                        SfProductResource resource = sfProductResourceMapper.selectByPrimaryKey(resIds.get(i));
                        cost.setInstanceId(resource.getId().toString());
                        setResourceInfo(resource, cost);
                    }
                }
                if (PriceType.RESOURCE.equals(priceType)) {
                    BigDecimal originalTmp = NumberUtil.div(originalCost, detail.getQuantity()).setScale(3, BigDecimal.ROUND_HALF_UP);
                    BigDecimal finalTmp = NumberUtil.div(finalCost, detail.getQuantity()).setScale(3, BigDecimal.ROUND_HALF_UP);
                    BigDecimal discountTmp = NumberUtil.div(discount, detail.getQuantity()).setScale(3, BigDecimal.ROUND_HALF_UP);
                    //多数量会产生多位小数，最终金额只保留了两位小数，多的会抹零，优惠券不应被抹去，没用完的补充到最后一个账单
                    finalTmp  = BigDecimalUtil.remainTwoPointAmount(finalTmp);
                    if (i + 1 == detail.getQuantity().intValue()) {
                        originalCost = NumberUtil.sub(originalCost, NumberUtil.mul(originalTmp, detail.getQuantity().intValue() - 1));
                        finalCost = NumberUtil.sub(finalCost, NumberUtil.mul(finalTmp, detail.getQuantity().intValue() - 1));
                        discount = NumberUtil.sub(discount, NumberUtil.mul(discountTmp, detail.getQuantity().intValue() - 1));
                    } else {
                        originalCost = originalTmp;
                        finalCost = finalTmp;
                        discount = discountTmp;
                    }
                }
                if (PriceType.SERVICE.equals(priceType)) {
                    if (!BooleanEnum.YES.getCode().equals(fixedMonth)) {
                        originalCost = NumberUtil.mul(priceDetail.getOriginalCost(), detail.getDuration());
                        finalCost = NumberUtil.mul(priceDetail.getTradePrice(), detail.getDuration());
                    }
                    if (OrderType.RENEW.equals(serviceOrder.getType())) {
                        originalCost = NumberUtil.div(originalCost, detail.getQuantity());
                        finalCost = NumberUtil.div(finalCost, detail.getQuantity());
                    }
                }
                if (PriceType.EXTRA_CONFIG.equals(priceType)) {
                    BigDecimal oncePrice = priceDetail.getOncePrice();
                    if (Objects.nonNull(oncePrice)) {
                        originalCost = oncePrice;
                        finalCost = oncePrice;
                    } else {
                        if (!BooleanEnum.YES.getCode().equals(fixedMonth)) {
                            originalCost = NumberUtil.mul(priceDetail.getOriginalCost(), detail.getDuration());
                            finalCost = NumberUtil.mul(priceDetail.getOriginalCost(), detail.getDuration());
                        }
                        if (OrderType.RENEW.equals(serviceOrder.getType())) {
                            originalCost = NumberUtil.div(originalCost, detail.getQuantity());
                            finalCost = NumberUtil.div(finalCost, detail.getQuantity());
                        }
                    }
                }
                cost.setPricingDiscount(discount);
                cost.setCouponDiscount(NumberUtil.sub(originalCost, finalCost, discount));
                if (Objects.isNull(cost.getInstanceName())) {
                    cost.setInstanceName(serviceOrder.getName());
                }
                priceDetail.setStartTime(detail.getStartTime());
                priceDetail.setEndTime(detail.getEndTime());

                //关联不计费产品设置实付金额为0
                if (Objects.nonNull(serviceOrder.getChargingType()) && SALE_TYPE.equalsIgnoreCase(serviceOrder.getChargingType())) {
                    cost.setCashAmount(BigDecimal.ZERO);
                    cost.setCreditAmount(BigDecimal.ZERO);
                    cost.setCouponAmount(BigDecimal.ZERO);
                    finalCost = BigDecimal.ZERO;
                    cost.setChargingType(SALE_TYPE);
                } else {
                    cost.setChargingType(NORMAL_TYPE);
                    cost.setEraseZeroAmount(BigDecimalUtil.getEraseZeroColumn(finalCost));
                    setUsedCost(bizBillingAccount, cost, BigDecimalUtil.remainTwoPointAmount(finalCost));
                    bizBillingAccount.setBalance(NumberUtil.sub(bizBillingAccount.getBalance(), BigDecimalUtil.remainTwoPointAmount(cost.getCashAmount()))
                    );
                    bizBillingAccount.setCreditLine(NumberUtil.sub(bizBillingAccount.getCreditLine(), BigDecimalUtil.remainTwoPointAmount(cost.getCreditAmount()))
                    );
                    bizBillingAccount.setBalanceCash(NumberUtil.sub(bizBillingAccount.getBalanceCash(), BigDecimalUtil.remainTwoPointAmount(cost.getCouponAmount()))
                    );
                }
                cost.setPretaxGrossAmount(originalCost);
                cost.setPretaxAmount(BigDecimalUtil.remainTwoPointAmount(finalCost));
                cost.setBizBillingAccount(BeanUtil.toBean(bizBillingAccount, BizBillingAccount.class));
                cost.setType(serviceOrder.getType());

                JSONObject serviceConfig = JSONObject.parseObject(detail.getServiceConfig());
                if (serviceConfig != null) {
                    Long cloudEnvId = serviceConfig.getLong("cloudEnvId");
                    if (cloudEnvId != null) {
                        CloudEnv cloudEnv = cloudEnvRemoteService.selectByPrimaryKey(cloudEnvId);
                        if (Objects.isNull(cloudEnv)) {
                            ResCloudEnv resCloudEnv = cloudEnvRemoteService.selectResCloudEnvById(cloudEnvId);
                            cost.setCloudEnvId(resCloudEnv.getId());
                            cost.setCloudEnvName(resCloudEnv.getCloudEnvName());
                            cost.setCloudEnvType("HCSO");
                            cost.setRegion(resCloudEnv.getRegion());
                        }else {
                            cost.setCloudEnvId(cloudEnv.getId());
                            cost.setCloudEnvName(cloudEnv.getCloudEnvName());
                            cost.setCloudEnvType(cloudEnv.getCloudEnvType());
                            cost.setRegion(cloudEnv.getRegion());
                        }
                    }
                } else {
                    cost.setCloudEnvId(detail.getCloudEnvId());
                    cost.setCloudEnvName(detail.getCloudEnvName());
                    cost.setCloudEnvType(detail.getCloudEnvType());
                    cost.setRegion(detail.getRegion());
                }

                this.processDistributorName(cost, serviceOrder);
                costs.add(cost);
                //AI专属资源池回填开始时间和结束时间
                if (applyResourceStartTime != null) {
                    priceDetail.setStartTime(applyResourceStartTime);
                    priceDetail.setEndTime(applyResourceEndTime);
                }

                //priceDetail 设置实际扣除的 余额，充值现金券，信用额度
                if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode().equals(detail.getServiceType())) {
                    priceDetail.setPayBalance(NumberUtil.add(priceDetail.getPayBalance(), cost.getCashAmount()));
                    priceDetail.setPayBalanceCash(NumberUtil.add(priceDetail.getPayBalanceCash(), cost.getCouponAmount()));
                    priceDetail.setPayCreditLine(NumberUtil.add(priceDetail.getPayCreditLine(), cost.getCreditAmount()));
                    serviceOrderPriceDetailMapper.updateByPrimaryKeySelective(priceDetail);
                } else {
                    priceDetail.setPayBalance(cost.getCashAmount());
                    priceDetail.setPayBalanceCash(cost.getCouponAmount());
                    priceDetail.setPayCreditLine(cost.getCreditAmount());
                    serviceOrderPriceDetailMapper.updateByPrimaryKeySelective(priceDetail);
                }
            }
        }
        return costs;
    }

    private ServiceOrderPriceDetail getCouponPriceDetail(List<ServiceOrderPriceDetail> serviceOrderPriceDetails) {
        if (serviceOrderPriceDetails.size() > 1
                && ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode().equals(serviceOrderPriceDetails.get(0).getProductCode())) {
            return CollectionUtil.getFirst(
                    serviceOrderPriceDetails.stream()
                            .filter(serviceOrderPriceDetail -> OrderType.RENEW.equals(serviceOrderPriceDetail.getType()))
                            .collect(Collectors.toList()));
        }
        Optional<ServiceOrderPriceDetail> first = serviceOrderPriceDetails.stream()
                    .filter(serviceOrderPriceDetail -> OrderType.RENEW.equals(serviceOrderPriceDetail.getType())).findFirst();
        //续订时 补扣详情在第一个，不能正常获取优惠券
        if (first.isPresent()) {
            return first.get();
        }

        return CollectionUtil.getFirst(serviceOrderPriceDetails);
    }



    /**
     * 退订出账
     *
     * @param
     * @param
     * @param detailMap
     * @param detail
     * @param sfProductResource
     * @param endTime
     * @param serviceOrder
     * @param bizBillingAccount
     * @param finalCostList     endTime,originalEndTime
     * @param isDrp
     * @param productResourceId
     * @param unsubAmount
     * @param degradeOriginal
     * @param serviceConfig
     * @return List<InstanceGaapCost>
     */
    private List<InstanceGaapCost> deductCost(List<InstanceGaapCost> costs,
                                              Map<Long, ServiceOrderDetail> detailMap,
                                              ServiceOrderDetail detail,
                                              SfProductResource sfProductResource, Date endTime, Date originalEndTime,
                                              ServiceOrder serviceOrder,
                                              BizBillingAccount bizBillingAccount,
                                              List<InstanceGaapCost> finalCostList, boolean isDrp,
                                              Long productResourceId, BigDecimal[] unsubAmount,
                                              BigDecimal degradeOriginal, cn.hutool.json.JSONObject serviceConfig) {
        for (int i = 0; i < costs.size(); i++) {
            InstanceGaapCost orderCost = costs.get(i);
            Date usageStartDate = orderCost.getUsageStartDate();
            Date usageEndDate = orderCost.getUsageEndDate();
            usageStartDate = DateUtil.halfUpMillisecond(usageStartDate);
            usageEndDate = DateUtil.halfUpMillisecond(usageEndDate);

            ServiceOrderDetail orderDetail = detailMap.get(Long.valueOf(orderCost.getOrderId()));

            log.info("ProcessServiceImpl.deductCost-{} orderId: {} usageStartDate: {}, usageEndDate: {}",
                    sfProductResource.getId(), orderCost.getOrderId(), DateUtil.dateFormat(usageStartDate), DateUtil.dateFormat(usageEndDate));
            String config = orderDetail.getServiceConfig();
            BigDecimal period = BigDecimal.ONE;
            if (StringUtils.isNotEmpty(config)) {
                cn.hutool.json.JSONObject configJson = JSONUtil
                        .parseObj(detail.getServiceConfig());
                period = configJson.getBigDecimal("period");
            }

            // BigDecimal allDays = NumberUtil.mul( period,BigDecimal.valueOf(30));
            BigDecimal allDays = NumberUtil.div(BigDecimal.valueOf(
                                    usageEndDate.getTime() - usageStartDate.getTime()),
                            BigDecimal.valueOf(1000 * 60 * 60 * 24L))
                    .setScale(0, BigDecimal.ROUND_CEILING);
            BigDecimal allMonths = BigDecimal.valueOf(
                    cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffMonth(usageStartDate, usageEndDate));

            Integer offsetDays = 0;
            BigDecimal offsetMonths = BigDecimal.ZERO;
            boolean isOverUsed = false;
            //如果冻结就算冻结的金额
            if (Objects.nonNull(sfProductResource.getFrozenTime())
                    && "frozen".equals(sfProductResource.getPreStatus())) {
                Date frozenTime = sfProductResource.getFrozenTime();
                offsetDays = calculateOffDay(originalEndTime, frozenTime);
                isOverUsed = true;
            }
            else {
                if (cn.hutool.core.date.DateUtil.between(usageEndDate, endTime, DateUnit.SECOND) <= 1) {
                    usageEndDate = endTime;
                }
                log.info("...,{},...{}", DateUtil.dateFormat(usageEndDate), DateUtil.dateFormat(originalEndTime));
                if (usageEndDate.before(endTime)) {
                    //最后一笔订单，结算超期天数
                    long xcTime = cn.hutool.core.date.DateUtil.between(usageEndDate, originalEndTime, DateUnit.SECOND);
                    if (xcTime <= 1) {
                        isOverUsed = true;
                        offsetDays = calculateOffDay(originalEndTime, endTime);
                    } else {
                        log.info("...,{},...{}", DateUtil.dateFormat(usageEndDate), DateUtil.dateFormat(originalEndTime));
                        continue;
                    }
                } else if (usageStartDate.before(endTime) && !"degrade".equals(orderCost.getType())) {
                    if (usageStartDate.before(new Date())) {
                        Long calculateOffMonth = DateUtil.calculateOffMonth(usageStartDate, endTime);
                        offsetMonths = BigDecimal.valueOf(calculateOffMonth);
                        orderCost.setUsageStartDate(endTime);
                        orderCost.setUsageEndDate(sfProductResource.getEndTime());
                    }
                } else if (usageStartDate.before(endTime) && "degrade".equals(orderCost.getType())) {
                    //  缩容的时间需要当前时间+1
                    if (usageStartDate.before(new Date())) {
                        usageStartDate = cn.hutool.core.date.DateUtil.offsetMonth(usageStartDate, 1);
                        offsetMonths = BigDecimal.valueOf(
                                cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffMonth(usageStartDate, endTime));
                        allMonths = BigDecimal.valueOf(
                                cn.com.cloudstar.rightcloud.oss.common.util.DateUtil.calculateOffMonth(usageStartDate, usageEndDate));
                    }
                } else {
                    offsetDays = -1;
                }

            }

            log.info("ProcessServiceImpl.deductCost-{} allDays: {}, allMonths: {}, offsetDays: {} offsetMonths: {}",sfProductResource.getId(), allDays, allMonths, offsetDays, offsetMonths);

            //查看是否有补扣金额
            InstanceGaapCost cost = orderCost;
            cost.setOrderId(serviceOrder.getId().toString());
            cost.setOrderSn(serviceOrder.getOrderSn());
            cost.setPayTime(new Date());
            cost.setBillingCycle(
                    cn.hutool.core.date.DateUtil.format(cn.hutool.core.date.DateUtil.date(), "yyyy-MM"));
            cost.setBillNo(NoUtil.generateNo("ZD"));
            cost.setBillSource("platform");
            cost.setUserAccountId(bizBillingAccount.getId());
            Long adminSid = bizBillingAccount.getAdminSid();
            cn.com.cloudstar.rightcloud.core.pojo.dto.user.User user = userMapper.selectByPrimaryKey(adminSid);
            String accountName = bizBillingAccount.getCreatedBy();
            if (user != null) {
                accountName = user.getAccount();
            }
            cost.setUserAccountName(accountName);
            cost.setMongoId(null);
            cost.setCueValue(null);
            cost.setEntityId(bizBillingAccount.getEntityId());
            cost.setEntityName(bizBillingAccount.getEntityName());
            cost.setType(serviceOrder.getType());
            cost.setOrderSourceSn(serviceOrder.getOrderSourceSn());
            BigDecimal cashAmount = cost.getCashAmount();
            BigDecimal creditAmount = cost.getCreditAmount();
            BigDecimal couponAmount = cost.getCouponAmount();
            BigDecimal payCost = cost.getPretaxAmount();


            SfProductResourceCueExample cueExample = new SfProductResourceCueExample();
            cueExample.createCriteria()
                    .andResourceIdEqualTo(productResourceId)
                    .andOrderDetailIdEqualTo(orderDetail.getId());
            List<SfProductResourceCue> sfProductResourceCues = sfProductResourceCueMapper.selectByExample(
                    cueExample);
            BigDecimal usedCost = payCost;
            if (offsetDays > 0) {
                //超期
                if (isDrp && ApplyTypeEnum.MA_VERSION_DEPTRAIN.getType().equals(String.valueOf(sfProductResource.getMaVersion()))) {
                    BigDecimal monthDays = NumberUtil.div(BigDecimal.valueOf(
                            DateUtil.ignoreMillisecond(cost.getUsageEndDate()).getTime() - DateUtil.ignoreMillisecond(cost.getUsageStartDate()).getTime()), BigDecimal.valueOf(1000 * 60 * 60 * 24L))
                            .setScale(0, BigDecimal.ROUND_CEILING);
                    cost.setMonthDays(monthDays + "");
                    usedCost = NumberUtil.div(NumberUtil.mul(cost.getPretaxAmount(), offsetDays), monthDays);
                }
                else {
                    cost.setAllDays(allDays + "");
                    usedCost = NumberUtil.div(NumberUtil.mul(cost.getPretaxAmount(), offsetDays), allDays);
                    if(ProductCodeEnum.isFederationProduct(sfProductResource.getProductType())){
                        usedCost = NumberUtil.div(NumberUtil.mul(cost.getPretaxGrossAmount(), offsetDays), allDays);
                    }
                }
                if (!CollectionUtil.isEmpty(sfProductResourceCues)) {
                    SfProductResourceCue resCue = CollectionUtil.getFirst(sfProductResourceCues);
                    Long cueValue = resCue.getCueValue();
                    if (cueValue != null) {
                        cost.setCueValue(BigInteger.valueOf(cueValue)
                                .multiply(BigInteger.valueOf(offsetDays * 24))
                                .toString());
                    }
                    resCue.setEndTime(endTime);
                    sfProductResourceCueMapper.updateByPrimaryKeySelective(resCue);
                }
                cost.setUsageStartDate(orderDetail.getEndTime());
                cost.setUsageEndDate(endTime);
                if (Objects.nonNull(sfProductResource.getFrozenTime())
                        && "frozen".equals(sfProductResource.getPreStatus())) {
                    cost.setUsageEndDate(sfProductResource.getFrozenTime());
                }
                cost.setUsageCount(offsetDays + " 天");
            }
            else {
                if (NumberUtil.isGreater(offsetMonths, BigDecimal.ZERO)) {
                    usedCost = NumberUtil.div(NumberUtil.mul(payCost, offsetMonths), allMonths);
                    Integer day = calculateOffDay(usageStartDate, endTime);
                    cost.setUsageCount(day + " 天");
                    if (!CollectionUtil.isEmpty(sfProductResourceCues)) {
                        SfProductResourceCue resCue = CollectionUtil.getFirst(sfProductResourceCues);
                        Long cueValue = resCue.getCueValue();
                        if (cueValue != null) {
                            cost.setCueValue(BigInteger.valueOf(cueValue).multiply(BigInteger.valueOf(
                                    day * 24)).toString());
                        }
                        resCue.setEndTime(endTime);
                        sfProductResourceCueMapper.updateByPrimaryKeySelective(resCue);
                    }
                } else {
                    usedCost = BigDecimal.ZERO;
                    //未使用,设置结束时间为开始时间;使用的时间是0，表示未使用。
                    if (!CollectionUtil.isEmpty(sfProductResourceCues)) {
                        SfProductResourceCue resCue = CollectionUtil.getFirst(sfProductResourceCues);
                        resCue.setEndTime(resCue.getStartTime());
                        sfProductResourceCueMapper.updateByPrimaryKeySelective(resCue);
                    }
                }
            }

            if (unsubAmount != null) {
                usedCost = i == costs.size() - 1
                        ? NumberUtil.add(payCost, NumberUtil.add(unsubAmount[0], unsubAmount[1]))
                        : NumberUtil.add(payCost, unsubAmount[0]);
            }
            //未超期退款按余额支付退款
            BigDecimal finalCost = BigDecimal.ZERO;
            if (!isOverUsed) {
                BigDecimal cashPart =
                        NumberUtil.equals(payCost, BigDecimal.ZERO) ? BigDecimal.ONE
                                : NumberUtil.div(cashAmount, payCost);
                BigDecimal usedCash = NumberUtil.mul(usedCost, cashPart);
                finalCost = NumberUtil.sub(usedCash, cashAmount).setScale(5, BigDecimal.ROUND_HALF_UP);
                log.info("ProcessServiceImpl.deductCost-{} cashPart: {},usedCash: {},finalCost: {}",sfProductResource.getId(), cashPart, usedCash, finalCost);
            } else {
                finalCost = usedCost.setScale(5, BigDecimal.ROUND_HALF_UP);
            }

            log.info("ProcessServiceImpl.deductCost-{} usedCost: {}, finalCost: {}",sfProductResource.getId(), usedCost, finalCost);

            //缩容不进行运算
            if (isDrp && "degrade".equals(cost.getType())) {
                Date currDate = new Date();
                List<InstanceGaapCost> applyCost = costs.stream()
                        .filter(c -> !"apply".equals(c.getType()))
                        .collect(Collectors.toList());

                long degradeMonth = monthCompare(applyCost.get(0).getUsageStartDate(), usageStartDate);
                log.info("..,{}..,{},...{}", DateUtil.dateFormat(applyCost.get(0).getUsageStartDate()),
                        DateUtil.dateFormat(usageStartDate), DateUtil.dateFormat(currDate));
                long currentMonth = monthCompare(applyCost.get(0).getUsageStartDate(), currDate);
                if (currentMonth - degradeMonth == 0) {
                    finalCost = degradeOriginal.abs();
                } else if (usageEndDate.before(endTime)) {
                    finalCost = BigDecimal.ZERO;
                }
            }
            //关联不计费产品设置实付金额为0
            if (Objects.nonNull(serviceOrder.getChargingType()) && SALE_TYPE.equalsIgnoreCase(serviceOrder.getChargingType())) {
                cost.setCashAmount(BigDecimal.ZERO);
                cost.setCreditAmount(BigDecimal.ZERO);
                cost.setCouponAmount(BigDecimal.ZERO);
                finalCost = BigDecimal.ZERO;
                cost.setChargingType(SALE_TYPE);
            } else {
                cost.setChargingType(NORMAL_TYPE);
            }
            if (NumberUtil.isGreater(finalCost, BigDecimal.ZERO)) {
                serviceOrderService.setUsedCost(bizBillingAccount, cost, BigDecimalUtil.remainTwoPointAmount(finalCost));
                bizBillingAccount.setBalance(
                        NumberUtil.sub(bizBillingAccount.getBalance(), cost.getCashAmount()));
                bizBillingAccount.setCreditLine(NumberUtil
                        .sub(bizBillingAccount.getCreditLine(),
                                cost.getCreditAmount()));
                bizBillingAccount.setBalanceCash(NumberUtil
                        .sub(bizBillingAccount.getBalanceCash(),
                                cost.getCouponAmount()));
            } else {
                cost.setCashAmount(BigDecimalUtil.remainTwoPointAmount(finalCost));
                cost.setCreditAmount(BigDecimal.ZERO);
                cost.setCouponAmount(BigDecimal.ZERO);
                bizBillingAccount.setBalance(
                        NumberUtil.sub(bizBillingAccount.getBalance(), cost.getCashAmount()));
            }
            if (isDrp) {
                ResMaPoolVO resMaPool = maRemoteService.getResMaPoolById(serviceOrder.getClusterId());
                if (Objects.nonNull(resMaPool) && Objects.nonNull(resMaPool.getName())) {
                    cost.setResourceId(resMaPool.getName());
                    cost.setInstanceId(resMaPool.getName());
                } else {
                    ResourceInfo resourceInf = serviceConfig.getBean("resourceInfo", ResourceInfo.class);
                    List<String> resourceIds = Optional.ofNullable(resourceInf)
                            .map(ResourceInfo::getIdNames)
                            .orElseGet(Lists::newArrayList)
                            .stream()
                            .map(ResourceInfo.IdName::getId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(resourceIds)) {
                        cost.setResourceId(resourceIds.get(0));
                        cost.setInstanceId(resourceIds.get(0));
                    }

                }
                cost.setInstanceName(serviceOrder.getName());
            }
            if (ProductCodeEnum.isCmpApiProduct(sfProductResource.getProductType())) {
                cost.setInstanceId(sfProductResource.getId().toString());
                cost.setInstanceName(serviceOrder.getName());
            }
            if (ProductCodeEnum.ECS.getProductCode().equals(detail.getServiceType()) || ProductCodeEnum.CCE.getProductCode().equals(detail.getServiceType())
                    || ProductCodeEnum.DISK.getProductCode().equals(detail.getServiceType())
                    || ProductCodeEnum.EIP.getProductCode().equals(detail.getServiceType())
                    || ProductCodeEnum.RDS.getProductCode().equals(detail.getServiceType())
                    || ProductCodeEnum.DCS.getProductCode().equals(detail.getServiceType())
                    || ProductCodeEnum.SFS_TURBO.getProductCode().equals(detail.getServiceType())
                    || ProductCodeEnum.CBR.getProductCode().equals(detail.getServiceType())
                    || ProductCodeEnum.ELB.getProductCode().equals(detail.getServiceType())
                    || ProductCodeEnum.AS_GROUP.getProductCode().equals(detail.getServiceType())
            ) {
                setResourceInfo(sfProductResource, cost);
            }

            cost.setPretaxGrossAmount(finalCost.setScale(5, BigDecimal.ROUND_HALF_UP));
            cost.setEraseZeroAmount(BigDecimalUtil.getEraseZeroColumn(finalCost));
            cost.setPretaxAmount(BigDecimalUtil.remainTwoPointAmount(finalCost));
            cost.setPricingDiscount(BigDecimal.ZERO);
            cost.setCouponDiscount(BigDecimal.ZERO);
            cost.setBizBillingAccount(
                    BeanUtil.toBean(bizBillingAccount, BizBillingAccount.class));
            cost.setType(OrderType.RELEASE);
            cost.setOrderSourceSn(serviceOrder.getOrderSourceSn());

            String invoiceStatus = cost.getInvoiceStatus();
            if ("done".equalsIgnoreCase(invoiceStatus) || "pending".equalsIgnoreCase(invoiceStatus)) {
                cost.setBillBillingCycleId(null);
            }
            cost.setOrderStatus(OrderStatus.RELEASE_SUCCESS);
            cost.setInvoiceStatus(null);
            this.processCostComputingPower(cost, detail);
            this.processDistributorName(cost, serviceOrder);
            finalCostList.add(cost);
        }
        return finalCostList;

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void applyTaskPass(String nodeName, ProcessInstance processInstance) {
        Task task = taskService.createTaskQuery()
                               .processInstanceId(processInstance.getId())
                               .singleResult();

        Map<String, Object> variables = Maps.newHashMap();
        String taskId = task.getId();

        variables.put(ProcessConstants.AUDIT, ProcessConstants.AUDIT_PASS);
        variables.put(taskId + ProcessConstants.AUDIT_STATUS_SUFFIX, ProcessConstants.AUDIT_PASS);

        Object applyUname = variables.get("_apply_uname");
        Object applyUserSid = variables.get("_apply_usersid");
        if (Objects.nonNull(applyUname)) {
            variables.put(taskId + "_audit_uname", String.valueOf(applyUname));
        }
        if (Objects.nonNull(applyUserSid)) {
            variables.put(taskId + "_audit_usersid", String.valueOf(applyUserSid));
        }

        variables.put("_task_name", task.getName());
        variables.put(taskId + ProcessConstants.AUDIT_ADVICE, "提交申请");

        if (StringUtils.equalsIgnoreCase(task.getName(), nodeName)) {
            taskService.complete(taskId, variables);
        }
    }

    /**
     * 处理cost 分销商名称
     *
     * @param cost
     */
    @Override
    public void processDistributorName(InstanceGaapCost cost, ServiceOrder serviceOrder) {
        if (Objects.isNull(cost)) {
            return;
        }
        BizBillingAccount account = bizBillingAccountMapper.selectByPrimaryKey(cost.getUserAccountId());
        if (Objects.nonNull(account)) {
            BizDistributor distributor = bizDistributorMapper.getById(account.getDistributorId());
            cost.setDistributorName(Objects.nonNull(distributor) ? distributor.getName() : null);
        }

        Criteria criteria = new Criteria();
        criteria.put("orderId", serviceOrder.getId());
        List<ServiceOrderDetail> details = serviceOrderDetailMapper.selectByParams(criteria);

        cost.setCustomFlag("system");
        String productCode = cost.getProductCode();
        if (StringUtils.isNotBlank(productCode) && CollectionUtil.isNotEmpty(details)) {
            Optional<ServiceOrderDetail> detail = details.stream().filter(e -> productCode.equals(e.getServiceType())).findFirst();
            if (detail.isPresent()) {
                String customFlag = detail.get().getCustomFlag();
                cost.setCustomFlag(StringUtils.isNotBlank(customFlag) ? customFlag : "system");
            }
        }

    }

    @Override
    public void processCostComputingPower(InstanceGaapCost cost, ServiceOrderDetail detail) {
        String serviceConfig = detail.getServiceConfig();
        if (StringUtils.isBlank(serviceConfig)) {
            return;
        }
        try {
            JSONObject object = JSON.parseObject(serviceConfig);
            if (object != null) {
                BigDecimal computingPower = object.getBigDecimal("computingPower");
                if (computingPower != null) {
                    computingPower = computingPower.setScale(2, BigDecimal.ROUND_HALF_UP);
                    String serviceType = detail.getServiceType();
                    String computingPowerStr = computingPower.toString();
                    if (serviceType.equals(ProductCodeEnum.MODEL_ARTS.getProductCode())) {
                        computingPowerStr += " " + UnitsEnum.PFLOPSRESOURCEUSAGE.getUnit();
                    } else if (serviceType.equals(ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode())) {
                        computingPowerStr += " " + UnitsEnum.COMPUTINGPOWER.getUnit();
                    }
                    cost.setComputingPower(computingPowerStr);
                }
                BigDecimal convertRatio = object.getBigDecimal("convertRatio");
                if (convertRatio != null) {
                    cost.setConvertRatio(convertRatio);
                }
            }
        } catch (Exception e) {
            log.error("处理设置账单算力值异常 ProcessServiceImpl.processCostComputingPower OUTPUT: orderId: {}", detail.getOrderId());
        }
    }

    /**
     * 过期费用出账
     *
     * @param costs
     * @param serviceOrder
     */
    public void expiredMountMakeUp(List<InstanceGaapCost> costs, ServiceOrder serviceOrder,
                                   BizBillingAccount bizBillingAccount, Long cueValue, String priceType,
                                   String configuration) {
        Date now = new Date();
     //   Long resourceId = serviceOrder.getProductResourceId();

        List<ServiceOrderDetail> details = serviceOrder.getDetails();
        ServiceOrderDetail orderDetail = details.get(0);

        String serviceConfig = orderDetail.getServiceConfig();
        cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(serviceConfig);
        Long productResourceId = jsonObject.getLong("productResourceId");
        SfProductResource sfProductResource = sfProductResourceMapper.selectByPrimaryKey(productResourceId);
        Date orlEndTime = sfProductResource.getEndTime();
        Date newStartTime = orderDetail.getStartTime();

        String resourceInfo = sfProductResource.getResourceInfo();
        JSONObject resourceInfoJSONObejct = JSONObject.parseObject(resourceInfo);
        String idNamesJsonStr = resourceInfoJSONObejct.getString("idNames");
        List<IdName> idNames = JSONObject.parseArray(idNamesJsonStr, IdName.class);

        if (CollectionUtil.isNotEmpty(idNames)) {
            List<String> resourceIds = idNames.stream().map(IdName::getId).distinct().collect(Collectors.toList());
            List<String> resourceNames = idNames.stream().map(IdName::getName).distinct().collect(Collectors.toList());
            details.stream().forEach(detail -> {
                BigDecimal makeUpAmount = detail.getExpiredUsedAmount();
                if (!NumberUtil.isGreater(makeUpAmount, BigDecimal.ZERO)) {
                    return;
                }
                Long detailId = detail.getId();
                Criteria criteria = new Criteria();
                criteria.put("orderDetailId", detailId);
                List<ServiceOrderPriceDetail> serviceOrderPriceDetails = serviceOrderPriceDetailMapper.selectByParams(
                        criteria);
                ServiceOrderPriceDetail priceDetail = CollectionUtil.getFirst(serviceOrderPriceDetails);

                InstanceGaapCost cost = new InstanceGaapCost();
                cost.setQuantity(detail.getQuantity());
                cost.setOrderId(serviceOrder.getId().toString());
                cost.setOrderSn(serviceOrder.getOrderSn());
                cost.setPayTime(now);
                cost.setUsageStartDate(orlEndTime);
                cost.setUsageEndDate(newStartTime);
                cost.setUsageCount(cn.hutool.core.date.DateUtil.betweenDay(orlEndTime, newStartTime, false) + "天");
                cost.setBillType(BillType.fromChargeTypeEnum(detail.getChargeType()));
                cost.setBillingCycle(
                        cn.hutool.core.date.DateUtil.format(cn.hutool.core.date.DateUtil.date(), "yyyy-MM"));
                cost.setOrgSid(serviceOrder.getOrgSid());
                cost.setOwnerId(serviceOrder.getOwnerId().toString());
                cost.setBillNo(NoUtil.generateNo("ZD"));
                cost.setCurrency("CNY");
                if (priceDetail != null) {
                    cost.setPriceType(priceDetail.getPriceType());
                    cost.setConfiguration(priceDetail.getBillingSpec());
                } else {
                    cost.setPriceType(priceType);
                    cost.setConfiguration(configuration);
                }

                cost.setProductCode(detail.getServiceType());
                cost.setProductName(ProductCodeEnum.toDesc(detail.getServiceType()));
                cost.setBillSource("platform");
                cost.setUserAccountId(bizBillingAccount.getId());
                Long adminSid = bizBillingAccount.getAdminSid();
                cn.com.cloudstar.rightcloud.core.pojo.dto.user.User user = userMapper.selectByPrimaryKey(adminSid);
                String accountName = bizBillingAccount.getCreatedBy();
                if (user != null) {
                    accountName = user.getAccount();
                }
                cost.setUserAccountName(accountName);
                cost.setRegion(orderDetail.getRegion());
                cost.setPrice(orderDetail.getPrice());
                if (CollectionUtil.isNotEmpty(resourceIds)) {
                    cost.setResourceId(String.join(",", resourceIds));
                }
                if (CollectionUtil.isNotEmpty(resourceNames)) {
                    cost.setInstanceName(String.join(",", resourceNames));
                }

                // originalCost 原价
                BigDecimal originalCost = BigDecimalUtil.scaleAndRoundHalfUp(makeUpAmount);
                // tradeFinalCost 最终价格
                BigDecimal finalCost = BigDecimalUtil.remainTwoPointAmount(makeUpAmount);
                //折扣金额默认为0
                cost.setPricingDiscount(BigDecimal.ZERO);
                //优惠券优惠金额默认为0
                cost.setCouponDiscount(BigDecimal.ZERO);
                //套餐包优惠金额默认为0
                cost.setBagDiscountAmount(BigDecimal.ZERO);
                //抵扣现金券优惠金额,默认为0
                cost.setDeductCouponDiscount(BigDecimal.ZERO);
                cost.setEraseZeroAmount(BigDecimalUtil.getEraseZeroColumn(finalCost));
                //计算扣钱的金额
                setUsedCost(bizBillingAccount, cost, BigDecimalUtil.remainTwoPointAmount(finalCost));
                bizBillingAccount.setBalance(
                        NumberUtil.sub(bizBillingAccount.getBalance(), BigDecimalUtil.getTwoPointAmount(cost.getCashAmount())));
                bizBillingAccount.setCreditLine(
                        NumberUtil.sub(bizBillingAccount.getCreditLine(), BigDecimalUtil.getTwoPointAmount(cost.getCreditAmount())));
                bizBillingAccount.setBalanceCash(
                        NumberUtil.sub(bizBillingAccount.getBalanceCash(), BigDecimalUtil.getTwoPointAmount(cost.getCouponAmount())));
                //原价
                cost.setPretaxGrossAmount(originalCost);
                //支付金额
                cost.setPretaxAmount(BigDecimalUtil.remainTwoPointAmount(finalCost));
                cost.setEraseZeroAmount(BigDecimalUtil.getEraseZeroColumn(finalCost));
                //账户快照
                cost.setBizBillingAccount(
                        BeanUtil.toBean(bizBillingAccount, BizBillingAccount.class));
                cost.setType(serviceOrder.getType());
                cost.setOrderSourceSn(serviceOrder.getOrderSourceSn());
                cost.setCloudEnvId(orderDetail.getCloudEnvId());
                cost.setCloudEnvName(orderDetail.getCloudEnvName());
                cost.setCloudEnvType(orderDetail.getCloudEnvType());
                cost.setRegion(orderDetail.getRegion());
                //设置补扣账单的cueValue
                if (cueValue != null) {
                    String finalCueValue = null;
                    long dur = cost.getUsageEndDate().getTime() - cost.getUsageStartDate().getTime();
                    finalCueValue = BigInteger.valueOf(cueValue)
                                              .multiply(NumberUtil.div(BigDecimal.valueOf(dur), 1000 * 60 * 60)
                                                                  .setScale(0, BigDecimal.ROUND_HALF_UP)
                                                                  .toBigInteger())
                                              .toString();
                    cost.setCueValue(finalCueValue);
                }
                costs.add(cost);
            });
        }

    }

    /**
     * 审核发送消息
     *
     * @param serviceOrder
     * @param detail
     */
    private void approvedPushMsg(ServiceOrder serviceOrder, ServiceOrderDetail detail) {
        Long time = System.currentTimeMillis() + RedisLock.TIME_OUT * 5;
        String key = serviceOrder.getOrderSn() + "-approvedPushMsg-"
                + NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_PRODUCT_RENEW_OPERATION;
        log.info("----------------- 审批通过，发送消息 approvedPushMsg key: {} ---------------------", key);

        Boolean isLock = redisLock.lock(key, String.valueOf(time));
        if (!isLock) {
            log.info("----------------- 审批通过，发送消息 没有获取到锁 approvedPushMsg ---------------------");
            return;
        }
        String result = JedisUtil.INSTANCE.get(key);
        if (StringUtils.isNotBlank(result)) {
            log.info("----------------- 审批通过，已经发送过站内信 approvedPushMsg result: {}---------------------", result);
            return;
        }

        try {
            User user = userService.selectByPrimaryKey(serviceOrder.getOwnerId());
            cn.com.cloudstar.rightcloud.oss.common.pojo.User authUser = AuthUtil.getAuthUser();
            executorService.execute(() -> {
                String orderType;
                switch (serviceOrder.getType()) {
                    case OrderType.RENEW:
                        orderType = "续订";
                        break;
                    default:
                        orderType = "开通";
                }

                HashMap<String, String> messageContent = new HashMap<>();
                messageContent.put("orderType", orderType);
                messageContent.put("productName", serviceOrder.getProductName());
                messageContent.put("userAccount", authUser.getAccount());
                messageContent.put("poolId", serviceOrder.getName());
                messageContent.put("tenantName", user.getAccount());
                messageContent.put("endTime",
                                   DateUtil.formatDate(detail.getEndTime().getTime(), "yyyy-MM-dd HH:mm:ss"));

                BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
                baseNotificationMqBean.setMsgId(
                        NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_PRODUCT_RENEW_OPERATION);
                baseNotificationMqBean.getImsgUserIds().add(authUser.getUserSid());
                baseNotificationMqBean.setMap(messageContent);
                baseNotificationMqBean.setEntityId(serviceOrder.getEntityId());
                rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT,
                                              baseNotificationMqBean);
                //发送站内信给运营管理员

            });
            JedisUtil.INSTANCE.set(key, "true", 10 * 60);
        } catch (Exception e) {
            e.printStackTrace();
            Thread.currentThread().interrupt();
        } finally {
            redisLock.unlock(key, String.valueOf(time));
            log.info("----------------- 审批通过，释放锁 approvedPushMsg key: {} ---------------------", key);
        }
    }

    private void billHpcDrp(ServiceOrder serviceOrder) {
        SfProductResource sfProductResource = null;
        Date releaseNow = null;
        Map<String,String> mapProductCode=new HashMap<>();
        for (ServiceOrderDetail detail : serviceOrder.getDetails()) {
            Date now = cn.hutool.core.date.DateUtil.date();
            BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectByPrimaryKey(
                    serviceOrder.getBizBillingAccountId());
            if (ObjectUtils.isEmpty(bizBillingAccount)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_607095064));
            }
            List<InstanceGaapCost> costs = Lists.newArrayList();
            cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(detail.getServiceConfig());
            if (Objects.isNull(sfProductResource)) {
                sfProductResource = sfProductResourceMapper.selectByPrimaryKey(
                        jsonObject.getLong("productResourceId"));
            }
            List<ServiceOrderPriceDetail> priceDetails = Lists.newArrayList();

            // 专属资源池实例ID取得
            Long clusterId = sfProductResource.getClusterId();
            QueryHpcDrpResourceRequest queryHpcDrpResourceRequest = new QueryHpcDrpResourceRequest();
            queryHpcDrpResourceRequest.setHpcClusterID(clusterId);
            Map<String, String> hpcDrpResourceMap = hpcRemoteService.getHpcDrpResourceInfo(queryHpcDrpResourceRequest);
            log.debug("退订 HPC专属资源池集群ID：[{}], HPC专属资源池所有实例：[{}]", serviceOrder.getClusterId(), hpcDrpResourceMap);
            // 1.检查专属资源池。
            Date endTime = sfProductResource.getEndTime();
            boolean isExpired = now.after(endTime);

            Criteria criteria = new Criteria();
            criteria.put("refInstanceIdLike", "\"" + sfProductResource.getId() + "\"");
            criteria.put("orderTypes", Lists.newArrayList("apply", "renew", "modify"));
            criteria.put("orderStatus", "completed");
            criteria.put("productCode", detail.getServiceType());
            criteria.put("isValid", true);
            List<ServiceOrderPriceDetail> lastOrderPriceDetails = serviceOrderPriceDetailMapper.selectByCriteria(
                    criteria);
            Set<Long> orderIds = lastOrderPriceDetails.stream()
                    .map(ServiceOrderPriceDetail::getOrderId)
                    .collect(Collectors.toCollection(LinkedHashSet::new));
            Long maxOrderId = orderIds.stream().max(Long::compare).orElse(-1L);
            lastOrderPriceDetails = lastOrderPriceDetails.stream()
                    .filter(d -> Objects.equals(d.getOrderId(), maxOrderId))
                    .collect(Collectors.toList());
            // 2.未过期
            if (!isExpired) {
                BigDecimal resource = jsonObject.getBigDecimal("releaseResource", BigDecimal.ZERO);
                priceDetails.add(
                        getPriceDetail("resource", resource, detail.getStartTime(), endTime, lastOrderPriceDetails.get(0).getBillingSpec()));
                priceDetails.forEach(pd -> {
                    InstanceGaapCost cost = serviceOrderService.getBaseCost(serviceOrder, pd,
                                                                            Convert.toStr(null), ProductCodeEnum.toDesc(
                                    detail.getServiceType()));
                    BigDecimal amount = pd.getAmount();
                    if (NumberUtil.isGreater(amount, BigDecimal.ZERO)) {
                        cost.setEraseZeroAmount(BigDecimalUtil.getEraseZeroColumn(amount));

                        serviceOrderService.setUsedCost(bizBillingAccount, cost, BigDecimalUtil.remainTwoPointAmount(amount));
                        bizBillingAccount.setBalance(
                                NumberUtil.sub(bizBillingAccount.getBalance(), BigDecimalUtil.getTwoPointAmount(cost.getCashAmount())));

                        bizBillingAccount.setCreditLine(
                                NumberUtil.sub(bizBillingAccount.getCreditLine(), BigDecimalUtil.getTwoPointAmount(cost.getCreditAmount())));

                        bizBillingAccount.setBalanceCash(
                                NumberUtil.sub(bizBillingAccount.getBalanceCash(),BigDecimalUtil.getTwoPointAmount(cost.getCouponAmount())));
                    } else {
                        cost.setCashAmount(amount);
                        cost.setCreditAmount(BigDecimal.ZERO);
                        cost.setCouponAmount(BigDecimal.ZERO);
                        bizBillingAccount.setBalance(
                                NumberUtil.sub(bizBillingAccount.getBalance(), BigDecimalUtil.getTwoPointAmount(cost.getCashAmount())));
                    }
                    cost.setProductCode(detail.getServiceType());
                    String productName = ProductCodeEnum.toDesc(detail.getServiceType());
                    log.info("所属产品Code：[{}]", detail.getServiceType());
                    if (ProductCodeEnum.HPC_DRP.getProductCode().equals(detail.getServiceType())) {
                        productName = serviceOrder.getProductName();
                        log.info("HPC_DRP 所属产品名称：[{}]", productName);
                    }
                    cost.setProductName(productName);
                    cost.setPretaxGrossAmount(amount);
                    cost.setConfiguration(pd.getBillingSpec());
                    cost.setEraseZeroAmount(BigDecimalUtil.getEraseZeroColumn(amount));
                    cost.setPretaxAmount(BigDecimalUtil.remainTwoPointAmount(amount));
                    cost.setBizBillingAccount(
                            BeanUtil.toBean(bizBillingAccount, BizBillingAccount.class));
                    // 资源实例ID取得
                    String resourceId;
                    if (ProductCodeEnum.SFS2.getProductCode().equals(jsonObject.getStr("productCode"))
                            && ApplyTypeEnum.HPC_DRP_STANDARD.getType().equals(detail.getApplyType())) {
                        // 针对标准HPC，内置弹性文件的resourceId已经记录在config中
                        resourceId = jsonObject.getJSONObject("data").getJSONObject("SFS2.0").getStr("name");
                        log.info("HPC标准专属资源池实例：SFS2.0，Value:[{}], name:[{}]", resourceId, serviceOrder.getName());
                    } else {
                        String key = jsonObject.getStr("productCode") + jsonObject.getStr("productCategory");
                        resourceId = hpcDrpResourceMap.get(key);
                        log.info("HPC专属资源池实例：Key：[{}], Value:[{}], name:[{}]", key, cost.getResourceId(),
                                 serviceOrder.getName());
                    }
                    cost.setResourceId(resourceId);
                    //如果实例名称为空，取申请单名称
                    cost.setInstanceName(serviceOrder.getName());

                    // 记录资源用量
                    cost.setUsageCount("--");
                    cost.setEntityId(bizBillingAccount.getEntityId());
                    cost.setEntityName(bizBillingAccount.getEntityName());
                    cost.setType(serviceOrder.getType());
                    cost.setOrderSourceSn(serviceOrder.getOrderSourceSn());

                    costs.add(cost);
                });
                bizBillingAccountMapper.updateByPrimaryKeySelective(bizBillingAccount);
                //修改用户业务标识Tag
                this.updateUserBusinessTag(bizBillingAccount);
                serviceOrderService.insertCostAndCycleAndDeal(serviceOrder, costs);
                continue;
            }
            // 3.已过期 未冻结
            boolean isFrozen = "frozen".equalsIgnoreCase(serviceOrder.getResourceOriginStatus());
            //  防止monogo插入重复的账单数据
            if(Objects.nonNull(mapProductCode.get(detail.getServiceType()))){
                continue;
            }
            mapProductCode.put(detail.getServiceType(),detail.getServiceType());

            if (releaseNow == null) {
                releaseNow = jsonObject.getDate("releaseNow");
            }

            Integer overDays = DateUtil.calculateOffDay(endTime,
                                                        isFrozen ? sfProductResource.getFrozenTime() : releaseNow);

            for (ServiceOrderPriceDetail priceDetail : lastOrderPriceDetails) {
                InstanceGaapCost cost = serviceOrderService.getBaseCost(serviceOrder,
                                                                        priceDetail, Convert.toStr(null),
                                                                        ProductCodeEnum.toDesc(
                                                                                detail.getServiceType()));


                BigDecimal tradePrice = priceDetail.getPrice();
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(priceDetail.getStartTime());
                BigDecimal finalCost = NumberUtil.div(tradePrice, calendar.getActualMaximum(Calendar.DAY_OF_MONTH))
                                                 .multiply(BigDecimal.valueOf(overDays))
                                                 .multiply(BigDecimal.valueOf(priceDetail.getQuantity()))
                                                 .negate();

                //超期结束时间和开始时间,重新设置
                if (overDays > 0) {
                    cost.setUsageStartDate(detail.getEndTime());
                    cost.setUsageEndDate(isFrozen ? sfProductResource.getFrozenTime() : releaseNow);
                }
                if (NumberUtil.isGreater(finalCost, BigDecimal.ZERO)) {
                    serviceOrderService.setUsedCost(bizBillingAccount, cost, BigDecimalUtil.remainTwoPointAmount(finalCost));
                    bizBillingAccount.setBalance(
                            NumberUtil.sub(bizBillingAccount.getBalance(), BigDecimalUtil.getTwoPointAmount(cost.getCashAmount()))
                    );

                    bizBillingAccount.setCreditLine(
                            NumberUtil.sub(bizBillingAccount.getCreditLine(), BigDecimalUtil.getTwoPointAmount(cost.getCreditAmount()))
                    );

                    bizBillingAccount.setBalanceCash(
                            NumberUtil.sub(bizBillingAccount.getBalanceCash(), BigDecimalUtil.getTwoPointAmount(cost.getCouponAmount()))
                    );
                } else {
                    cost.setCashAmount(finalCost);
                    cost.setCreditAmount(BigDecimal.ZERO);
                    cost.setCouponAmount(BigDecimal.ZERO);
                    bizBillingAccount.setBalance(NumberUtil.sub(bizBillingAccount.getBalance(), BigDecimalUtil.remainTwoPointAmount(cost.getCashAmount())));
                }

                cost.setProductCode(detail.getServiceType());
                String productName = ProductCodeEnum.toDesc(detail.getServiceType());
                log.info("所属产品Code：[{}]", detail.getServiceType());
                if (ProductCodeEnum.HPC_DRP.getProductCode().equals(detail.getServiceType())) {
                    productName = serviceOrder.getProductName();
                    log.info("HPC_DRP 所属产品名称：[{}]", productName);
                }
                cost.setProductName(productName);
                cost.setPretaxGrossAmount(finalCost);
                cost.setEraseZeroAmount(BigDecimalUtil.getEraseZeroColumn(finalCost));
                cost.setPretaxAmount(BigDecimalUtil.remainTwoPointAmount(finalCost));
                cost.setPricingDiscount(BigDecimal.ZERO);
                cost.setBizBillingAccount(
                        BeanUtil.toBean(bizBillingAccount, BizBillingAccount.class));
                // 资源实例ID取得
                String key = priceDetail.getProductCode() + priceDetail.getBillingSpec();
                cost.setResourceId(hpcDrpResourceMap.get(key));
                cost.setInstanceName(serviceOrder.getName());
                log.info("HPC专属资源池实例：Key：[{}], Value:[{}]", key, cost.getResourceId());

                // 记录资源用量
                Integer offsetDays = calculateOffDay(cost.getUsageStartDate(), cost.getUsageEndDate());
                cost.setUsageCount("--");
                cost.setEntityId(bizBillingAccount.getEntityId());
                cost.setEntityName(bizBillingAccount.getEntityName());
                cost.setType(serviceOrder.getType());
                cost.setOrderSourceSn(serviceOrder.getOrderSourceSn());
                costs.add(cost);
            }
            bizBillingAccountMapper.updateByPrimaryKeySelective(bizBillingAccount);
            serviceOrderService.insertCostAndCycleAndDeal(serviceOrder, costs);
            // }
        }
        serviceOrder.setPayTime(new Date());
        serviceOrderService.updateByPrimaryKeySelective(serviceOrder);
    }

    private void updateUserBusinessTag(BizBillingAccount bizBillingAccount) {
        User user = userMapper.selectByPrimaryKey(bizBillingAccount.getAdminSid());
        String businessTag = user.getBusinessTag();
        if (bizBillingAccount.getBalance().compareTo(BigDecimal.ZERO) < 0) {
            if (StringUtil.isNotEmpty(businessTag)) {
                List<String> tagList = new ArrayList<>(Arrays.asList(businessTag.split(";")));
                if (businessTag.indexOf(BusinessTagEnum.ARREARAGE.getTag()) >= 0) {
                    tagList = tagList.stream().map(s -> {
                        if (s.indexOf(BusinessTagEnum.ARREARAGE.getTag()) >= 0) {
                            String[] split = s.replaceAll(BusinessTagEnum.ARREARAGE.getTag(), "")
                                    .replaceAll("\\[", "")
                                    .replaceAll("]", "")
                                    .split(",");
                            List<String> accountIdList = new ArrayList<>(Arrays.asList(split));
                            if (!accountIdList.contains(bizBillingAccount.getId().toString())) {
                                accountIdList.add(bizBillingAccount.getId().toString());
                                s = BusinessTagEnum.ARREARAGE.getTag() + "[" + StringUtils.join(accountIdList, ",")
                                        + "]";
                            }
                        }
                        return s;
                    }).collect(Collectors.toList());
                } else {
                    tagList.add(BusinessTagEnum.ARREARAGE.getTag() + "[" + bizBillingAccount.getId() + "]");
                }
                user.setBusinessTag(StringUtils.join(tagList, ";"));
            } else {
                user.setBusinessTag(BusinessTagEnum.ARREARAGE.getTag() + "[" + bizBillingAccount.getId() + "]");
            }
            userService.updateByPrimaryKeySelective(user);
        } else {
            if (StringUtil.isNotEmpty(businessTag)) {
                List<String> tagList = new ArrayList<>(Arrays.asList(businessTag.split(";")));
                if (businessTag.indexOf(BusinessTagEnum.ARREARAGE.getTag()) >= 0) {
                    tagList = tagList.stream().map(s -> {
                        if (s.indexOf(BusinessTagEnum.ARREARAGE.getTag()) >= 0) {
                            String[] split = s.replaceAll(BusinessTagEnum.ARREARAGE.getTag(), "")
                                    .replaceAll("\\[", "")
                                    .replaceAll("]", "")
                                    .split(",");
                            List<String> accountIdList = new ArrayList<>(Arrays.asList(split));
                            if (accountIdList.contains(bizBillingAccount.getId().toString())) {
                                accountIdList.remove(bizBillingAccount.getId().toString());
                                if (org.springframework.util.CollectionUtils.isEmpty(accountIdList)) {
                                    return null;
                                }
                                s = BusinessTagEnum.ARREARAGE.getTag() + "[" + StringUtils.join(accountIdList, ",")
                                        + "]";
                            }
                        }
                        return s;
                    }).filter(Objects::nonNull).collect(Collectors.toList());
                    user.setBusinessTag(StringUtils.join(tagList, ";"));
                    userService.updateByPrimaryKeySelective(user);
                }
            }
        }
    }

    /**
     * 退订合并成一条账单数据
     *
     * @param bizBillingAccount
     * @param finalCost
     * @param finalCostList
     * @param serviceOrder
     * @param productResourceId
     * @return List<InstanceGaapCost>
     */
    private List<InstanceGaapCost> mergeBill(
            BizBillingAccount bizBillingAccount, BigDecimal finalCost,
            List<InstanceGaapCost> finalCostList,
            ServiceOrder serviceOrder,ServiceOrderDetail detail,
            Long productResourceId) {
        log.info("账单退订合并:{}", PlaintextShieldUtil.eliminatePlaintext(finalCostList));
        BigDecimal finalPretaxGrossAmount = finalCostList.stream().filter(t -> t.getPretaxGrossAmount() != null)
                .map(InstanceGaapCost::getPretaxGrossAmount)
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        BigDecimal finalCashAmount = finalCostList.stream().filter(t -> t.getCashAmount() != null)
                .map(InstanceGaapCost::getCashAmount)
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        BigDecimal finalCreditAmount = finalCostList.stream().filter(t -> t.getCreditAmount() != null)
                .map(InstanceGaapCost::getCreditAmount)
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        BigDecimal finalCouponAmount = finalCostList.stream().filter(t -> t.getCouponAmount() != null)
                .map(InstanceGaapCost::getCouponAmount)
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        BigDecimal finalErazeZeroAmount = finalCostList.stream().filter(t -> t.getEraseZeroAmount() != null)
                .map(InstanceGaapCost::getEraseZeroAmount)
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        List<InstanceGaapCost> costList = new ArrayList<>();

        //Ma需要将退订的账单合并成一条
        InstanceGaapCost cost = new InstanceGaapCost();
        if(finalCostList.size()>0){
            InstanceGaapCost finalInstanceGaapCost = finalCostList.get(0);
            cost.setInstanceId(finalInstanceGaapCost.getInstanceId());
            cost.setResourceId(finalInstanceGaapCost.getResourceId());
            cost.setInstanceName(finalInstanceGaapCost.getInstanceName());
            cost.setProductCode(finalInstanceGaapCost.getProductCode());
            cost.setOrgSid(finalInstanceGaapCost.getOrgSid());
            cost.setCloudEnvId(finalInstanceGaapCost.getCloudEnvId());
            cost.setCloudEnvType(finalInstanceGaapCost.getCloudEnvType());
            cost.setCloudEnvName(finalInstanceGaapCost.getCloudEnvName());
            cost.setBillType(finalInstanceGaapCost.getBillType());
            cost.setOwnerId(finalInstanceGaapCost.getOwnerId());
            cost.setRegion(finalInstanceGaapCost.getRegion());
            cost.setCurrency(finalInstanceGaapCost.getCurrency());
            String productName = finalInstanceGaapCost.getProductName();
            cost.setCueValue(finalInstanceGaapCost.getCueValue());
            cost.setUsageStartDate(finalInstanceGaapCost.getUsageStartDate());
            cost.setUsageEndDate(finalInstanceGaapCost.getUsageEndDate());
            cost.setEntityName(finalInstanceGaapCost.getEntityName());
            cost.setEntityId(finalInstanceGaapCost.getEntityId());
            cost.setConfiguration(finalInstanceGaapCost.getConfiguration());
            cost.setPriceType(finalInstanceGaapCost.getPriceType());
            if (ProductCodeEnum.HPC_DRP.getProductCode().equals(detail.getServiceType())) {
                productName = serviceOrder.getProductName();
            }
            cost.setProductName(productName);
            cost.setUsageCount(finalInstanceGaapCost.getUsageCount());
            cost.setAllDays(finalInstanceGaapCost.getAllDays());
            cost.setMonthDays(finalInstanceGaapCost.getMonthDays());
        }
        cost.setOrderId(serviceOrder.getId().toString());
        cost.setOrderSn(serviceOrder.getOrderSn());
        cost.setPayTime(new Date());
        cost.setBillingCycle(
                cn.hutool.core.date.DateUtil.format(cn.hutool.core.date.DateUtil.date(), "yyyy-MM"));
        cost.setBillNo(NoUtil.generateNo("ZD"));
        cost.setBillSource("platform");
        cost.setUserAccountId(bizBillingAccount.getId());
        Long adminSid = bizBillingAccount.getAdminSid();
        cn.com.cloudstar.rightcloud.core.pojo.dto.user.User user = userMapper.selectByPrimaryKey(adminSid);
        String accountName = bizBillingAccount.getCreatedBy();
        if (user != null) {
            accountName = user.getAccount();
        }
        cost.setUserAccountName(accountName);
        cost.setMongoId(null);
        cost.setCueValue(null);
        cost.setUsageCount(null);
        cost.setEntityId(bizBillingAccount.getEntityId());
        cost.setEntityName(bizBillingAccount.getEntityName());
        cost.setType(serviceOrder.getType());
        cost.setOrderSourceSn(serviceOrder.getOrderSourceSn());
        cost.setUsageCount(null);
        cost.setChargingType(NORMAL_TYPE);
        cost.setCashAmount(finalCashAmount);
        cost.setCreditAmount(finalCreditAmount);
        cost.setCouponAmount(finalCouponAmount);

        cost.setPretaxGrossAmount(finalPretaxGrossAmount);
        cost.setEraseZeroAmount(finalErazeZeroAmount);
        cost.setPretaxAmount(finalCost);
        cost.setPricingDiscount(BigDecimal.ZERO);
        cost.setCouponDiscount(BigDecimal.ZERO);
        cost.setBizBillingAccount(
                BeanUtil.toBean(bizBillingAccount, BizBillingAccount.class));
        cost.setType(OrderType.RELEASE);
        cost.setOrderSourceSn(serviceOrder.getOrderSourceSn());

        cost.setBillBillingCycleId(null);
        //cost.setOrderStatus(OrderStatus.RELEASE_SUCCESS);
        cost.setInvoiceStatus(null);

        cost.setPrice(finalCost);

        cost.setType(serviceOrder.getType());
        cost.setOrderSourceSn(serviceOrder.getOrderSourceSn());

        // 插入算力 PFLOPS
        this.processCostComputingPower(cost, detail);
        //设置分销商名称
        this.processDistributorName(cost, serviceOrder);

        costList.add(cost);
        return costList;
    }

    /**
     * 修改出账
     */
    private void approvalModify(ServiceOrder serviceOrder, BizBillingAccount bizBillingAccount) {
        List<ServiceOrderDetail> details = serviceOrder.getDetails();
        ServiceOrderDetail serviceOrderDetail = details.get(0);
        if (ProductCodeEnum.isCmpApiProduct(serviceOrderDetail.getServiceType())
                ||ProductCodeEnum.AI_BMS_EXCLUSIVE.getProductCode().equals(serviceOrderDetail.getServiceType())) {
            this.modifyCost(serviceOrder, bizBillingAccount, new Date());
            for (ServiceOrderDetail detail : details) {
                ServiceOrderDetail upgradeServiceDetail = new ServiceOrderDetail();
                upgradeServiceDetail.setId(detail.getId());
                upgradeServiceDetail.setStartTime(detail.getStartTime());
                upgradeServiceDetail.setEndTime(detail.getEndTime());
                upgradeServiceDetail.setQuantity(detail.getQuantity());
                serviceOrderDetailMapper.updateByPrimaryKeySelective(upgradeServiceDetail);
            }
        }
    }

    private void updateHpcClusterLoginAddress(ResHpcClusterRemoteModule resHpcClusterRemoteModule) {
        //hpc暂无
    }

    @Transactional
    public void modifyCost(ServiceOrder serviceOrder, BizBillingAccount bizBillingAccount, Date startTime) {
        List<ServiceOrderDetail> details = serviceOrder.getDetails();
        ServiceOrderDetail serviceOrderDetail = details.get(0);
        if (startTime == null) {
            startTime = serviceOrderDetail.getStartTime();
        }
        if (startTime == null) {
            startTime = new Date();
        }
        // 入账账单
        Date endTime = serviceOrderDetail.getEndTime();
        if (endTime == null) {
            endTime = DateUtils.addMonths(startTime, serviceOrderDetail.getDuration());
        }

        Criteria criteria = new Criteria();
        String orderSn = serviceOrder.getOrderSn();
        criteria.put("orderSn", orderSn);
        List<ServiceOrderPriceDetail> serviceOrderPriceDetails = serviceOrderPriceDetailMapper
                .selectByParams(criteria);

        List<InstanceGaapCost> costs = Lists.newArrayList();

        Criteria cri = new Criteria();
        if(ProductCodeEnum.AI_BMS_EXCLUSIVE.getProductCode().equals(serviceOrderDetail.getServiceType())){
            final String serviceConfig = serviceOrderDetail.getServiceConfig();
            JSONObject configJson = JSON.parseObject(serviceConfig);
            //修改resource表
            String productResourceId = configJson.getString("productResourceId");
            cri.put("id", productResourceId);
        }else{
            cri.put("cluster_id", serviceOrder.getClusterId());
        }
        cri.put("product_type", serviceOrderDetail.getServiceType());
        List<SfProductResource> sfProductResources = sfProductResourceMapper.selectByParams(cri);
        SfProductResource sfProductResource = CollectionUtil.getFirst(sfProductResources);
        if(ObjectUtil.isEmpty(sfProductResource)){
            cn.hutool.json.JSONObject serviceConfig = JSONUtil.parseObj(serviceOrderDetail.getServiceConfig());
            Long productResourceId = serviceConfig.getLong("productResourceId");
            sfProductResource = sfProductResourceMapper.selectByPrimaryKey(productResourceId);
        }
        if(ProductCodeEnum.AI_BMS_EXCLUSIVE.getProductCode().equals(serviceOrderDetail.getServiceType())){
            ServiceOrderResourceRef serviceOrderResourceRef = new ServiceOrderResourceRef();
            serviceOrderResourceRef.setOrderDetailId(serviceOrderDetail.getId());
            HashMap<String, Object> condition = new HashMap<>(2);
            condition.put("resourceId", sfProductResource.getId());
            condition.put("type", serviceOrderDetail.getServiceType());
            serviceOrderResourceRefMapper.updateByParamsSelective(serviceOrderResourceRef, condition);
        }
        Date sfEndTime = sfProductResource.getEndTime();
        if (endTime.after(sfEndTime)) {
            endTime = sfEndTime;
        }

        String contractId = serviceOrder.getContractId();
        BizContract bizContract = null;
        if (StringUtils.isNotEmpty(contractId)) {
            bizContract = bizContractMapper.selectById(Long.valueOf(contractId));
        }

        for (ServiceOrderPriceDetail priceDetail : serviceOrderPriceDetails) {
            //扩容续订不计算基础集群的价格
            if (org.apache.commons.lang.StringUtils.equals(priceDetail.getProductCode(),
                                                           ProductCodeEnum.HPC_DRP.getProductCode())) {
                continue;
            }
            BigDecimal amount = Optional.ofNullable(priceDetail.getAmount()).orElse(BigDecimal.ZERO);
            if (Objects.nonNull(serviceOrder.getChargingType()) && SALE_TYPE.equals(serviceOrder.getChargingType())) {
                amount = BigDecimal.ZERO;
            }

            priceDetail.setStartTime(startTime);

            priceDetail.setEndTime(endTime);
            InstanceGaapCost cost = new InstanceGaapCost();

            cost.setQuantity(priceDetail.getQuantity());
            cost.setOrderId(serviceOrder.getId().toString());
            cost.setOrderSn(orderSn);
            cost.setPayTime(new Date());
            cost.setUsageStartDate(priceDetail.getStartTime());
            cost.setUsageEndDate(priceDetail.getEndTime());
            cost.setUsageCount(
                    cn.hutool.core.date.DateUtil.betweenDay(priceDetail.getStartTime(), priceDetail.getEndTime(), false)
                            + "天");

            cost.setBillType(BillType.fromChargeTypeEnum(priceDetail.getChargeType()));
            cost.setBillingCycle(cn.hutool.core.date.DateUtil.format(startTime, "yyyy-MM"));
            cost.setOrgSid(serviceOrder.getOrgSid());
            cost.setOwnerId(serviceOrder.getOwnerId().toString());
            cost.setBillNo(NoUtil.generateNo("ZD"));
            cost.setCurrency("CNY");
            cost.setPriceType("resource");
            cost.setBillSource("platform");
            cost.setUserAccountId(bizBillingAccount.getId());
            Long adminSid = bizBillingAccount.getAdminSid();
            cn.com.cloudstar.rightcloud.core.pojo.dto.user.User user = userMapper.selectByPrimaryKey(adminSid);
            String accountName = bizBillingAccount.getCreatedBy();
            if (user != null) {
                accountName = user.getAccount();
            }
            cost.setUserAccountName(accountName);
            cost.setPrice(priceDetail.getPrice());
            cost.setPretaxGrossAmount(priceDetail.getOriginalCost());
            cost.setPricingDiscount(priceDetail.getDiscount());

            cost.setEraseZeroAmount(BigDecimalUtil.getEraseZeroColumn(amount));
            cost.setPretaxAmount(BigDecimalUtil.remainTwoPointAmount(amount)); // tradeFinalCost 最终价格

            cost.setProductCode(priceDetail.getProductCode());
            String productName = ProductCodeEnum.toDesc(priceDetail.getProductCode());
            log.info("所属产品Code：[{}]", priceDetail.getProductCode());
            if (ProductCodeEnum.HPC_DRP.getProductCode().equals(priceDetail.getProductCode())) {
                productName = serviceOrder.getProductName();
                log.info("HPC_DRP 所属产品名称：[{}]", productName);
            }
            boolean isRds= checkIsRdsByOrderDetailId(priceDetail.getOrderDetailId());
            if(isRds){
                productName = ProductCodeEnum.toDesc(ProductCodeEnum.RDS.getProductCode());
                if(!ProductCodeEnum.RDS.getProductCode().equals(priceDetail.getProductCode())){
                    productName="云硬盘 RDS";
                }
            }
            cost.setProductName(productName);

            cost.setEntityId(serviceOrder.getEntityId());
            cost.setEntityName(serviceOrder.getEntityName());
            cost.setType(serviceOrder.getType());
            cost.setOrderSourceSn(serviceOrder.getOrderSourceSn());
//            if (Objects.nonNull(bizBillingAccount)) {
//                BizDistributor distributor = bizDistributorMapper.getById(bizBillingAccount.getDistributorId());
//                cost.setDistributorName(Objects.nonNull(distributor) ? distributor.getName() : null);
//            }
//            cost.setDistributorName(serviceOrder.getEntityName());
            this.processCostComputingPower(cost, serviceOrderDetail);
            this.processDistributorName(cost, serviceOrder);

            ServiceOrderDetail orderDetail = serviceOrderDetailMapper.selectByPrimaryKey(
                    priceDetail.getOrderDetailId());
            JSONObject jsonObject = JSON.parseObject(orderDetail.getServiceConfig());
            cost.setResourceId(Optional.ofNullable(jsonObject.getString(ServiceConfigArrKey.NODE_INSTANCE_ID_LIST))
                                       .orElse("")); // 节点uuid
            if (ProductCodeEnum.ECS.getProductCode().equals(orderDetail.getServiceType())
                    || ProductCodeEnum.CCE.getProductCode().equals(orderDetail.getServiceType())
                    || ProductCodeEnum.DISK.getProductCode().equals(orderDetail.getServiceType())
                    || ProductCodeEnum.SFS_TURBO.getProductCode().equals(orderDetail.getServiceType())
                    || ProductCodeEnum.EIP.getProductCode().equals(orderDetail.getServiceType())
                    || ProductCodeEnum.DCS.getProductCode().equals(orderDetail.getServiceType())
                    || ProductCodeEnum.RDS.getProductCode().equals(orderDetail.getServiceType())
                    || ProductCodeEnum.CBR.getProductCode().equals(orderDetail.getServiceType())
                    || ProductCodeEnum.AS_GROUP.getProductCode().equals(orderDetail.getServiceType())
                    || ProductCodeEnum.ELB.getProductCode().equals(orderDetail.getServiceType())
            ) {
                List<Long> resIds = JSONObject.parseArray(priceDetail.getRefInstanceId(), Long.class);
                SfProductResource resource = sfProductResourceMapper.selectByPrimaryKey(resIds.get(0));
                cost.setInstanceId(resource.getId().toString());
                cost.setInstanceName(serviceOrder.getName());
                setResourceInfo(resource, cost);
            }

            cost.setConfiguration(priceDetail.getBillingSpec());

            setModifyCost(bizBillingAccount, cost, BigDecimalUtil.remainTwoPointAmount(amount));
            bizBillingAccount.setBalance(
                    NumberUtil.sub(bizBillingAccount.getBalance(), cost.getCashAmount()));
            bizBillingAccount.setCreditLine(NumberUtil
                                                    .sub(bizBillingAccount.getCreditLine(),
                                                         cost.getCreditAmount()));
            bizBillingAccount.setBalanceCash(NumberUtil
                                                     .sub(bizBillingAccount.getBalanceCash(),
                                                          cost.getCouponAmount()));
            cost.setBizBillingAccount(BeanUtil.toBean(bizBillingAccount, BizBillingAccount.class));
            priceDetail.setPayBalance(cost.getCashAmount());
            priceDetail.setPayCreditLine(cost.getCreditAmount());
            priceDetail.setPayBalanceCash(cost.getCouponAmount());
            cost.setOrderSourceSn(serviceOrder.getOrderSourceSn());
            costs.add(cost);
            serviceOrderPriceDetailMapper.updateByPrimaryKeySelective(priceDetail);
        }
        //修改订单开始结束时间
        for (ServiceOrderDetail detail : details) {
            detail.setStartTime(startTime);
            detail.setEndTime(endTime);
        }
        //修改合同时间
        if (bizContract != null && bizContract.getStartTime() == null) {
            bizContract.setStartTime(startTime);
            bizContract.setEndTime(endTime);
            bizContract.setUpdatedDt(startTime);
            bizContractMapper.updateByPrimaryKeySelective(bizContract);

            List<BizContractDetail> bizContractDetails = bizContractDetailMapper.selectListByContractId(
                    Long.valueOf(contractId));
            if (CollectionUtil.isNotEmpty(bizContractDetails)) {
                for (BizContractDetail bizContractDetail : bizContractDetails) {
                    bizContractDetail.setStartTime(startTime);
                    bizContractDetail.setEndTime(endTime);
                    bizContractDetail.setUpdatedDt(endTime);

                    bizContractDetailMapper.updateByPrimaryKeySelective(bizContractDetail);
                }
            }
        }

        bizBillingAccountMapper.updateByPrimaryKeySelective(bizBillingAccount);
        sfProductResource.setStatus("normal");
        sfProductResourceMapper.updateByPrimaryKey(sfProductResource);

        //修改用户业务标识Tag
        updateUserBusinessTag(bizBillingAccount);
        try {
            Collection<InstanceGaapCost> instanceGaapCosts = mongoTemplate.insertAll(costs);
            if (!CollectionUtil.isEmpty(instanceGaapCosts)) {
                List<InstanceGaapCost> costList = new ArrayList<InstanceGaapCost>(instanceGaapCosts);
                this.insertBillCycleInfo(costList);
                BigDecimal finalCost = serviceOrder.getFinalCost();
                this.insertAccountDeal(costs, serviceOrder);
            } else {
                log.error("ServiceOrderServiceImpl------processProduct-----------failed---instanceGaapCosts isEmpty");
            }
        } catch (Exception e) {
            log.error("入账失败", e);
        }
    }

    private void setResourceInfo(SfProductResource resource, InstanceGaapCost cost) {
        switch (resource.getProductType()) {
            case "ECS" :
                RestResult<Object> vmResoult = resourceDcFeignService.getVmById(resource.getClusterId());
                if (Objects.nonNull(vmResoult) && vmResoult.getStatus() && Objects.nonNull(vmResoult.getData())) {
                    ResVmResponse vm = JSON.parseObject(JSON.toJSONString(vmResoult.getData()), ResVmResponse.class);
                    cost.setInstanceName(vm.getName());
                    cost.setResourceId(vm.getUuid());
                }
            break;
            case "CCE" :
                List<ResContainerCluster> resContainerClusters = cceRemoteService.getResContainerClusterByIds(Arrays.asList(resource.getClusterId()));
                if (CollectionUtil.isNotEmpty(resContainerClusters)) {
                    cost.setInstanceName(resContainerClusters.get(0).getName());
                    cost.setResourceId(resContainerClusters.get(0).getUuid());
                }
            case "EBS" :
                ResVd resVd = ebsRemoteService.selectByPrimaryKey(String.valueOf(resource.getClusterId()));
                if (Objects.nonNull(resVd)) {
                    cost.setInstanceName(resVd.getVdName());
                    cost.setResourceId(resVd.getUuid());
                }
                break;
            case "RDS" :
                RestResult<DescribeRdsDetailResult> rdsResult = resourceDcFeignService.getRdsById(resource.getClusterId());
                if (Objects.nonNull(rdsResult) && rdsResult.getStatus() && Objects.nonNull(rdsResult.getData())) {
                    DescribeRdsDetailResult result = JSON.parseObject(JSON.toJSONString(rdsResult.getData()), DescribeRdsDetailResult.class);
                    cost.setInstanceName(result.getRds().getName());
                    cost.setResourceId(result.getRds().getUuid());
                }
                break;
            case "CBR" :
                RestResult<ResVaultDetailResult> cbrResult = resourceDcFeignService.getCbrById(resource.getClusterId());
                if (Objects.nonNull(cbrResult) && cbrResult.getStatus() && Objects.nonNull(cbrResult.getData())) {
                    ResVaultDetailResult result=JSON.parseObject(JSON.toJSONString(cbrResult.getData()), ResVaultDetailResult.class);
                    cost.setInstanceName(result.getName());
                    cost.setResourceId(result.getUuid());
                }
                break;
            case "SFS_TURBO":
                log.info("获取{}详情{}", "SFS_TURBO", resource.getClusterId());
                RestResult<ResSfsTurboDetailResult> sfsResult = resourceDcFeignService.getSfsById(resource.getClusterId());
                if (Objects.nonNull(sfsResult) && sfsResult.getStatus() && Objects.nonNull(sfsResult.getData())) {
                    ResSfsTurboDetailResult result = JSON.parseObject(JSON.toJSONString(sfsResult.getData()), ResSfsTurboDetailResult.class);
                    cost.setInstanceName(result.getName());
                    cost.setResourceId(result.getUuid());
                }
                break;
            case "DCS":
                cost.setResourceId(resource.getInstanceUuid());
                break;
            case "ELB":
                cost.setResourceId(resource.getInstanceUuid());
                break;
            case "EIP":
                RestResult<ResFloatingIpResult> eipResult = resourceDcFeignService.getEipById(resource.getClusterId());
                if (Objects.nonNull(eipResult) && eipResult.getStatus() && Objects.nonNull(eipResult.getData())) {
                    ResFloatingIpResult result = JSON.parseObject(JSON.toJSONString(eipResult.getData()), ResFloatingIpResult.class);
                    cost.setInstanceName(result.getBandwidthName());
                    cost.setResourceId(result.getUuid());
                }
                break;
            case "AS":
                RestResult<ResScalingGroupDetailResult> asResult = resourceDcFeignService.getAsGroupById(resource.getClusterId());
                if (Objects.nonNull(asResult) && asResult.getStatus() && Objects.nonNull(asResult.getData())) {
                    ResScalingGroupDetailResult result = JSON.parseObject(JSON.toJSONString(asResult.getData()), ResScalingGroupDetailResult.class);
                    cost.setInstanceName(result.getScalingGroupName());
                    cost.setResourceId(result.getUuid());
                }
                break;
            default:
                log.warn("未找到资源类型:{}", resource.getProductType());
        }

    }

    @Override
    @Transactional
    public void hpcDrpUpgradeCost(ServiceOrder serviceOrder, BizBillingAccount bizBillingAccount, Date startTime) {
        List<ServiceOrderDetail> details = serviceOrder.getDetails();
        ServiceOrderDetail serviceOrderDetail = details.get(0);
        if (startTime == null) {
            startTime = serviceOrderDetail.getStartTime();
        }
        if (startTime == null) {
            startTime = new Date();
        }
        Criteria criteria = new Criteria();
        String orderSn = serviceOrder.getOrderSn();
        criteria.put("orderSn", orderSn);
        List<ServiceOrderPriceDetail> serviceOrderPriceDetails = serviceOrderPriceDetailMapper
                .selectByParams(criteria);

        List<InstanceGaapCost> costs = Lists.newArrayList();
        // 入账账单
        Date endTime = DateUtils.addMonths(startTime, serviceOrderDetail.getDuration());
        Criteria cri = new Criteria();
        cri.put("cluster_id", serviceOrder.getClusterId());
        cri.put("product_type", ProductCodeEnum.HPC_DRP.getProductCode());
        List<SfProductResource> sfProductResources = sfProductResourceMapper.selectByParams(cri);
        SfProductResource sfProductResource = CollectionUtil.getFirst(sfProductResources);
        Date sfEndTime = sfProductResource.getEndTime();
        if (endTime.after(sfEndTime)) {
            endTime = sfEndTime;
        }

        String contractId = serviceOrder.getContractId();
        BizContract bizContract = null;
        if (StringUtils.isNotEmpty(contractId)) {
            bizContract = bizContractMapper.selectById(Long.valueOf(contractId));
        }

        for (ServiceOrderPriceDetail priceDetail : serviceOrderPriceDetails) {
            //扩容续订不计算基础集群的价格
            if (org.apache.commons.lang.StringUtils.equals(priceDetail.getProductCode(), ProductCodeEnum.HPC_DRP.getProductCode())) {
                continue;
            }
            BigDecimal amount = Optional.ofNullable(priceDetail.getAmount()).orElse(BigDecimal.ZERO);
            if (Objects.nonNull(serviceOrder.getChargingType()) && SALE_TYPE.equals(serviceOrder.getChargingType())) {
                amount = BigDecimal.ZERO;
            }

            priceDetail.setStartTime(startTime);

            priceDetail.setEndTime(endTime);
            InstanceGaapCost cost = new InstanceGaapCost();

            cost.setQuantity(priceDetail.getQuantity());
            cost.setOrderId(serviceOrder.getId().toString());
            cost.setOrderSn(orderSn);
            cost.setPayTime(new Date());
            cost.setUsageStartDate(priceDetail.getStartTime());
            cost.setUsageEndDate(priceDetail.getEndTime());
            cost.setUsageCount(
                    cn.hutool.core.date.DateUtil.betweenDay(priceDetail.getStartTime(), priceDetail.getEndTime(), false)
                            + "天");

            cost.setBillType(BillType.fromChargeTypeEnum(priceDetail.getChargeType()));
            cost.setBillingCycle(cn.hutool.core.date.DateUtil.format(startTime, "yyyy-MM"));
            cost.setOrgSid(serviceOrder.getOrgSid());
            cost.setOwnerId(serviceOrder.getOwnerId().toString());
            cost.setBillNo(NoUtil.generateNo("ZD"));
            cost.setCurrency("CNY");
            cost.setPriceType("resource");
            cost.setBillSource("platform");
            cost.setUserAccountId(bizBillingAccount.getId());
            Long adminSid = bizBillingAccount.getAdminSid();
            cn.com.cloudstar.rightcloud.core.pojo.dto.user.User user = userMapper.selectByPrimaryKey(adminSid);
            String accountName = bizBillingAccount.getCreatedBy();
            if (user != null) {
                accountName = user.getAccount();
            }
            cost.setUserAccountName(accountName);
            cost.setPrice(priceDetail.getPrice());
            cost.setPretaxGrossAmount(priceDetail.getOriginalCost());
            cost.setPricingDiscount(priceDetail.getDiscount());

            cost.setEraseZeroAmount(BigDecimalUtil.getEraseZeroColumn(amount));
            cost.setPretaxAmount(BigDecimalUtil.remainTwoPointAmount(amount)); // tradeFinalCost 最终价格

            cost.setProductCode(priceDetail.getProductCode());
            String productName = ProductCodeEnum.toDesc(priceDetail.getProductCode());
            log.info("所属产品Code：[{}]", priceDetail.getProductCode());
            if (ProductCodeEnum.HPC_DRP.getProductCode().equals(priceDetail.getProductCode())) {
                productName = serviceOrder.getProductName();
                log.info("HPC_DRP 所属产品名称：[{}]", productName);
            }
            cost.setProductName(productName);

            cost.setEntityId(serviceOrder.getEntityId());
            cost.setEntityName(serviceOrder.getEntityName());
            cost.setType(serviceOrder.getType());
            cost.setOrderSourceSn(serviceOrder.getOrderSourceSn());
//            if (Objects.nonNull(bizBillingAccount)) {
//                BizDistributor distributor = bizDistributorMapper.getById(bizBillingAccount.getDistributorId());
//                cost.setDistributorName(Objects.nonNull(distributor) ? distributor.getName() : null);
//            }
//            cost.setDistributorName(serviceOrder.getEntityName());
            this.processCostComputingPower(cost, serviceOrderDetail);
            this.processDistributorName(cost, serviceOrder);

            ServiceOrderDetail orderDetail = serviceOrderDetailMapper.selectByPrimaryKey(
                    priceDetail.getOrderDetailId());
            JSONObject jsonObject = JSON.parseObject(orderDetail.getServiceConfig());
            cost.setResourceId(Optional.ofNullable(jsonObject.getString(ServiceConfigArrKey.NODE_INSTANCE_ID_LIST))
                    .orElse("")); // 节点uuid

            Boolean contractPayStatus = serviceOrder.getContractPayStatus();
            if (contractPayStatus == null) {
                if (bizContract != null) {
                    contractPayStatus = bizContract.getPayStatus();
                }
            }
            String bizContractPayStatus = Boolean.TRUE.equals(contractPayStatus) ? "合同已付款。" : "合同未付款。";
            cost.setConfiguration(priceDetail.getBillingSpec() + ";" + bizContractPayStatus);

            setUsedCost(bizBillingAccount, cost, BigDecimalUtil.remainTwoPointAmount(amount));
            bizBillingAccount.setBalance(
                    NumberUtil.sub(bizBillingAccount.getBalance(), cost.getCashAmount()));
            bizBillingAccount.setCreditLine(NumberUtil
                    .sub(bizBillingAccount.getCreditLine(), cost.getCreditAmount()));
            bizBillingAccount.setBalanceCash(NumberUtil
                    .sub(bizBillingAccount.getBalanceCash(), cost.getCouponAmount()));
            cost.setBizBillingAccount(BeanUtil.toBean(bizBillingAccount, BizBillingAccount.class));
            priceDetail.setPayBalance(cost.getCashAmount());
            priceDetail.setPayCreditLine(cost.getCreditAmount());
            priceDetail.setPayBalanceCash(cost.getCouponAmount());
            cost.setOrderSourceSn(serviceOrder.getOrderSourceSn());
            costs.add(cost);
            serviceOrderPriceDetailMapper.updateByPrimaryKeySelective(priceDetail);
        }
        //修改订单开始结束时间
        for (ServiceOrderDetail detail : details) {
            detail.setStartTime(startTime);
            detail.setEndTime(endTime);
        }
        //修改合同时间
        if (bizContract != null && bizContract.getStartTime() == null) {
            bizContract.setStartTime(startTime);
            bizContract.setEndTime(endTime);
            bizContract.setUpdatedDt(startTime);
            bizContractMapper.updateByPrimaryKeySelective(bizContract);

            List<BizContractDetail> bizContractDetails = bizContractDetailMapper.selectListByContractId(
                    Long.valueOf(contractId));
            if (CollectionUtil.isNotEmpty(bizContractDetails)) {
                for (BizContractDetail bizContractDetail : bizContractDetails) {
                    bizContractDetail.setStartTime(startTime);
                    bizContractDetail.setEndTime(endTime);
                    bizContractDetail.setUpdatedDt(endTime);

                    bizContractDetailMapper.updateByPrimaryKeySelective(bizContractDetail);
                }
            }
        }

        bizBillingAccountMapper.updateByPrimaryKeySelective(bizBillingAccount);
        //修改用户业务标识Tag
        updateUserBusinessTag(bizBillingAccount);
        try {
            Collection<InstanceGaapCost> instanceGaapCosts = mongoTemplate.insertAll(costs);
            if (!CollectionUtil.isEmpty(instanceGaapCosts)) {
                List<InstanceGaapCost> costList = new ArrayList<InstanceGaapCost>(instanceGaapCosts);
                this.insertBillCycleInfo(costList);
                BigDecimal finalCost = serviceOrder.getFinalCost();
                if (NumberUtil.isGreater(finalCost, BigDecimal.ZERO)) {
                    this.insertAccountDeal(costs, serviceOrder);
                }
            } else {
                log.error("ServiceOrderServiceImpl------processProduct-----------failed---instanceGaapCosts isEmpty");
            }
        } catch (Exception e) {
            log.error("入账失败", e);
        }
    }


    private ServiceOrderPriceDetail getPriceDetail(String priceType, BigDecimal price,
                                                   Date starTime, Date endTime, String billingSpec) {
        ServiceOrderPriceDetail priceDetail = new ServiceOrderPriceDetail();
        priceDetail.setPriceType(priceType);
        priceDetail.setPrice(price);
        priceDetail.setTradePrice(price);
        priceDetail.setStartTime(starTime);
        priceDetail.setEndTime(endTime);
        priceDetail.setDiscount(BigDecimal.ZERO);
        priceDetail.setCouponAmount(BigDecimal.ZERO);
        priceDetail.setOriginalCost(price);
        priceDetail.setAmount(price);
        priceDetail.setBillingSpec(billingSpec);
        return priceDetail;
    }

    @Override
    public void approvalDrp(ServiceOrder serviceOrder, ServiceOrderDetail detail) {
        if (CollectionUtils.isEmpty(serviceOrder.getDetails())) {
            // 防止后审批从serviceOrder获取detail导致空指针
            serviceOrder.setDetails(Collections.singletonList(detail));
        }

        //校验扩容or缩容
        cn.hutool.json.JSONObject serviceConfig = JSONUtil.parseObj(detail.getServiceConfig());
        cn.hutool.json.JSONObject productConfigDesc = JSONUtil.parseObj(detail.getProductConfigDesc());

        SfProductResource sfProductResource = sfProductResourceMapper.selectByPrimaryKey(
                serviceConfig.getLong("productResourceId"));

        // 校验资源状态、是否过期
        if (sfProductResource.getEndTime().before(new Date())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_54840825));
        }

        ResMaPoolVO resMaPoolVO = maRemoteService.getResMaPoolById(sfProductResource.getClusterId());

        // 校验余额
        if (OrderStatus.PENDING.equalsIgnoreCase(serviceOrder.getStatus())
                && serviceOrder.getFinalCost().compareTo(BigDecimal.ZERO) > 0) {
            Criteria criteria = new Criteria();
            criteria.put("adminSid", serviceOrder.getOwnerId());
            BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectByParams(criteria).get(0);
            //查询现金余额
//            if (Objects.isNull(bizBillingAccount.getBalance()) || bizBillingAccount.getBalance().compareTo(BigDecimal.ZERO) < 0){
//                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_77398982));
//            }
            if (ChargeTypeEnum.PREPAID.getValue().equalsIgnoreCase(detail.getChargeType())) {
                // 若余额+信用额度+现金券余额 < 当前订单消费
                if (NumberUtil.add(BigDecimalUtil.getTwoPointAmount(bizBillingAccount.getBalance()),
                                   BigDecimalUtil.getTwoPointAmount(bizBillingAccount.getCreditLine()))
                              .add(BigDecimalUtil.getTwoPointAmount(bizBillingAccount.getBalanceCash()))
                              .compareTo(BigDecimalUtil.remainTwoPointAmount(serviceOrder.getFinalCost())) < 0) {
                    log.info("bizBillingAccount id : {}", bizBillingAccount.getId());
                    if (MaPoolProessPhase.UPDATE_MA_POOL.equalsIgnoreCase(resMaPoolVO.getProessPhase())
                            || MaPoolProessPhase.UPDATE_MA_POOL_ERROR.equalsIgnoreCase(resMaPoolVO.getProessPhase())) {
                        serviceOrder.setErrorInfo("用户余额不足，请充值后重试。");
                        resMaPoolVO.setErrorInfo("用户余额不足，请充值后重试。");
                        resMaPoolVO.setProessPhase(MaPoolProessPhase.UPDATE_MA_POOL_ERROR);
                        serviceOrderService.updateByPrimaryKeySelective(serviceOrder);
                        maRemoteService.updateByPrimaryKeySelective(resMaPoolVO);
                    }
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_881668963));
                }
            } else if (ChargeTypeEnum.POSTPAID.getValue().equalsIgnoreCase(detail.getChargeType())) {
                String configValue = cn.com.cloudstar.rightcloud.oss.util.PropertiesUtil.getProperty(
                        POST_PAID_CONFIG_KEY);
                BigDecimal amount = StrUtil.isBlank(configValue) ? BigDecimal.ZERO : new BigDecimal(configValue);
                if (NumberUtil.isLess(NumberUtil.add(BigDecimalUtil.getTwoPointAmount(bizBillingAccount.getBalance()),
                                                     BigDecimalUtil.getTwoPointAmount(bizBillingAccount.getCreditLine())),
                                      BigDecimalUtil.remainTwoPointAmount(amount))) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1601270136) + amount + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_580695533));
                }
            }
        }
        CloudEnvParams params = new CloudEnvParams();
        params.setCloudEnvType(CloudEnvType.HCSO.getValue().get(0));
        List<CloudEnv> cloudEnvs = cloudEnvRemoteService.selectByParams(params);
        if (org.springframework.util.CollectionUtils.isEmpty(cloudEnvs)) {
            throw new cn.com.cloudstar.rightcloud.common.exception.BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1000498884));
        }

        Criteria criteria = new Criteria();
        criteria.put("configKeys", CommonPropertyKeyEnum.getAIAutoPropertyList());
        List<SysConfig> sysConfigs = sysConfigService.selectByParams(criteria);
        //避免空指针
        sysConfigs.forEach(sysConfig -> {
            if (Objects.isNull(sysConfig.getConfigValue())) {
                sysConfig.setConfigValue("");
            }
        });
        Map<String, String> configMap = sysConfigs.stream()
                                                  .collect(Collectors.toMap(SysConfig::getConfigKey,
                                                                            SysConfig::getConfigValue));
        log.info("iam自动化配置:{}", PlaintextShieldUtil.eliminatePlaintext(configMap));

        if (RES_MA_POOL_PROCESS_NOT_IN_LIST.contains(resMaPoolVO.getProessPhase())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_972079363));
        }

        //查询目标节点数量
        cn.hutool.json.JSONObject changeDesc = (cn.hutool.json.JSONObject) productConfigDesc.getJSONArray("changeDesc")
                                                                                            .get(0);
        Integer newValue = changeDesc.getInt("newValue");
        Integer oldValue = changeDesc.getInt("oldValue");
        log.info("AI专属资源池节点变更，目标数量：[{}]", newValue);

        Integer amount = newValue - oldValue;
        log.info("Modelarts资源变更，节点数量变化：[{}]", amount);
        String operate = "";
        //amount大于0为扩容，小于0为缩容

        // 待审批用户信息替换
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        Long loginUserSid = null;
        Long loginOrgSid = null;
        if (Objects.nonNull(authUserInfo)) {
            loginUserSid = authUserInfo.getUserSid();
            loginOrgSid = authUserInfo.getOrgSid();
        }
        userService.updAuthInfo(serviceOrder.getOwnerId(), serviceOrder.getOrgSid());
        if (0 > amount) {
            log.info("进入缩容流程");
            resMaPoolVO.setAvailableCount(newValue);
            if (MaPoolProessPhase.START.equalsIgnoreCase(resMaPoolVO.getProessPhase())
                    || MaPoolProessPhase.UPDATE_MA_POOL_ERROR.equalsIgnoreCase(resMaPoolVO.getProessPhase())) {
                // 缩容预审批
                try {
                    this.updateMAPools(serviceOrder, cloudEnvs, resMaPoolVO, newValue);

                    String poolType = resMaPoolVO.getType();
                    if (StringUtils.isNotBlank(poolType) && MaBasicStatus.LOGICAL.equals(poolType) &&
                            !AVAILABLE.equalsIgnoreCase(resMaPoolVO.getStatus())) {

                        resMaPoolVO.setStatus(AVAILABLE);
                        // 缩容完成后修改最大节点数
                        resMaPoolVO.setMaxCount(newValue);
                        resMaPoolVO.setProessPhase(MaPoolProessPhase.COMPLETE);
                        maRemoteService.updateByPrimaryKeySelective(resMaPoolVO);
                        serviceOrder.setStepName("缩容完成");
                        operate = "缩容";
                    }
                } catch (Exception e) {
                    resMaPoolVO.setAvailableCount(resMaPoolVO.getMaxCount());
                    maRemoteService.updateByPrimaryKeySelective(resMaPoolVO);
                    throw e;
                }
            } else if (MaPoolProessPhase.UPDATE_MA_POOL.equalsIgnoreCase(resMaPoolVO.getProessPhase())
                    || MaPoolProessPhase.UPDATE_SHARE_POOL_ERROR.equalsIgnoreCase(resMaPoolVO.getProessPhase())) {
                // 缩容后审批
                Integer allNodesCount = this.querySharePoolNodeCount(configMap, cloudEnvs, amount, resMaPoolVO,
                                                                     "apply");

                this.updateMASharePools(configMap, cloudEnvs, allNodesCount - amount, resMaPoolVO, "apply");

                if (!AVAILABLE.equalsIgnoreCase(resMaPoolVO.getStatus())) {
                    resMaPoolVO.setStatus(AVAILABLE);
                    // 缩容完成后修改最大节点数
                    resMaPoolVO.setMaxCount(newValue);
                    resMaPoolVO.setProessPhase(MaPoolProessPhase.COMPLETE);
                    maRemoteService.updateByPrimaryKeySelective(resMaPoolVO);
                    serviceOrder.setStepName("缩容完成");
                    operate = "缩容";
                }
            }
        }
        else {
            log.info("进入扩容流程");
            if (MaPoolProessPhase.START.equalsIgnoreCase(resMaPoolVO.getProessPhase())
                    || MaPoolProessPhase.UPDATE_SHARE_POOL_ERROR.equalsIgnoreCase(resMaPoolVO.getProessPhase())) {
                // 扩容预审批流程  todo 报错
                Integer allNodesCount = this.querySharePoolNodeCount(configMap, cloudEnvs, amount, resMaPoolVO,
                                                                     "apply");
                Integer shareNodesMinCount = Integer.parseInt(configMap.get("iam.auto.node.min.num"));
                if (shareNodesMinCount > allNodesCount - amount) {
                    //throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1389989036) + shareNodesMinCount + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_188289));
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_168554852) + shareNodesMinCount + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_65281));

                }
                int availableCount = resMaPoolsService.getCommonAvailableCount(serviceOrder.getClusterId());
                if (amount - availableCount > 0) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_354563552) + amount + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1934277331));
                }
                this.updateMASharePools(configMap, cloudEnvs, allNodesCount - amount, resMaPoolVO, "apply");
            }
            else if (MaPoolProessPhase.UPDATE_SHARE_POOL.equalsIgnoreCase(resMaPoolVO.getProessPhase())
                    || (MaPoolProessPhase.UPDATE_MA_POOL_ERROR.equalsIgnoreCase(resMaPoolVO.getProessPhase())
                    && "upgrade".equals(resMaPoolVO.getStatus()))
                    || MaPoolProessPhase.ROLLBACKERROR.equalsIgnoreCase(resMaPoolVO.getProessPhase())) {
                // 扩容后审批流程
                this.updateMAPools(serviceOrder, cloudEnvs, resMaPoolVO, newValue);
            } else if (MaPoolProessPhase.UPDATE_MA_POOL.equalsIgnoreCase(resMaPoolVO.getProessPhase())
                    || MaPoolProessPhase.UPDATE_MA_POOL_ERROR.equalsIgnoreCase(resMaPoolVO.getProessPhase())) {
                // 流程结束
                resMaPoolVO.setErrorInfo("");
                resMaPoolVO.setAvailableCount(newValue);
                resMaPoolVO.setProessPhase(MaPoolProessPhase.COMPLETE);
                maRemoteService.updateByPrimaryKeySelective(resMaPoolVO);
                serviceOrder.setStepName("扩容完成");
                operate = "扩容";
            }
        }

        // 审批用户信息替换
        if (Objects.nonNull(loginUserSid)) {
            userService.updAuthInfo(loginUserSid, loginOrgSid);
        }

        if (StringUtils.isEmpty(operate)) {
            return;
        }
        // 插入变更记录
        ResChangeRecordDTO resChangeRecordDTO = new ResChangeRecordDTO();
        resChangeRecordDTO.setOrgSid(detail.getOrgSid());
        resChangeRecordDTO.setResType(detail.getServiceType());
        resChangeRecordDTO.setOwnerId(Convert.toLong(detail.getOwnerId()));
        resChangeRecordDTO.setResourceId(serviceConfig.getStr("productResourceId"));
        resChangeRecordDTO.setCloudEnvId(detail.getCloudEnvId());
        resChangeRecordDTO.setInstanceId(resMaPoolVO.getName());

        //新大小
        resChangeRecordDTO.setNewType(changeDesc.getStr("newValue"));
        //原始大小
        resChangeRecordDTO.setOriginalType(changeDesc.getStr("oldValue"));
        WebUserUtil.prepareInsertParams(resChangeRecordDTO);
        resChangeRecordRemoteService.insertSelective(resChangeRecordDTO);
        billing(serviceOrder, resMaPoolVO.getName());

        serviceOrder.setPayTime(new Date());
        serviceOrder.setStatus(OrderStatus.COMPLETED);

        sfProductResource.setStatus("normal");
        sfProductResourceMapper.updateByPrimaryKeySelective(sfProductResource);
        serviceOrderService.updateByPrimaryKeySelective(serviceOrder);

        // 针对产品实例，修改serviceConfig
        cn.hutool.json.JSONObject serviceConfigJSONObject = serviceConfig.getJSONObject("productConfigDesc");
        JSONArray currentConfigDesc = JSONUtil.parseArray(serviceConfigJSONObject.getJSONArray("currentConfigDesc"));

        cn.hutool.json.JSONObject attrKey = (cn.hutool.json.JSONObject) currentConfigDesc.stream()
                                                                                         .filter(currentConfig -> {
                                                                                             cn.hutool.json.JSONObject jsonObject = (cn.hutool.json.JSONObject) currentConfig;
                                                                                             return "count".equals(
                                                                                                     jsonObject.getStr(
                                                                                                             "attrKey"));
                                                                                         })
                                                                                         .collect(Collectors.toList())
                                                                                         .get(0);

        attrKey.put("value", newValue + "/" + newValue);
        serviceConfigJSONObject.put("currentConfigDesc", JSONUtil.toJsonStr(currentConfigDesc));
//                serviceConfig.put("productConfigDesc",serviceConfigJSONObject);

        detail.setServiceConfig(JSONUtil.toJsonStr(serviceConfig));
        serviceOrderDetailMapper.updateByPrimaryKeySelective(detail);

        ServiceOrderResourceRef serviceOrderResourceRef = new ServiceOrderResourceRef();
        serviceOrderResourceRef.setOrderDetailId(detail.getId());
        HashMap<String, Object> condition = new HashMap<>(2);
        condition.put("resourceId", sfProductResource.getId());
        condition.put("type", detail.getServiceType());
        int i = serviceOrderResourceRefMapper.updateByParamsSelective(serviceOrderResourceRef, condition);

        User user = userService.selectByPrimaryKey(serviceOrder.getOwnerId());
        cn.com.cloudstar.rightcloud.oss.common.pojo.User authUser = AuthUtil.getAuthUser();
        String finalOperate = operate;
        executorService.execute(() -> {
            HashMap<String, String> messageContent = new HashMap<>();
            messageContent.put("productName", serviceOrder.getProductName());
            messageContent.put("userAccount", user.getAccount());
            messageContent.put("poolName", serviceOrder.getName());
            messageContent.put("orderSn", serviceOrder.getOrderSn());
            messageContent.put("nodeNum", String.valueOf(newValue));
            messageContent.put("startDate", cn.hutool.core.date.DateUtil.format(detail.getStartTime(), "yyyy年MM月dd日 HH:mm:ss"));
            messageContent.put("endDate", cn.hutool.core.date.DateUtil.format(detail.getEndTime(), "yyyy年MM月dd日 HH:mm:ss"));
            messageContent.put("nodeNum", String.valueOf(newValue));
            try {
                Thread.sleep(10000);
            } catch (InterruptedException e) {
                e.printStackTrace();
                Thread.currentThread().interrupt();
            }
            //发送站内信给租户管理员
            if ("扩容".equals(finalOperate)) {
                BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
                baseNotificationMqBean.setMsgId(NotificationConsts.ConsoleMsg.ProductMsg.TENANT_AI_PRIVATE_SCALE_UP);
                baseNotificationMqBean.getImsgUserIds().add(user.getUserSid());
                baseNotificationMqBean.setMap(messageContent);
                baseNotificationMqBean.setEntityId(serviceOrder.getEntityId());
                rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT, baseNotificationMqBean);

            }else{
                BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
                baseNotificationMqBean.setMsgId(NotificationConsts.ConsoleMsg.ProductMsg.TENANT_AI_PRIVATE_SCALE_DOWN);
                baseNotificationMqBean.getImsgUserIds().add(user.getUserSid());
                baseNotificationMqBean.setMap(messageContent);
                baseNotificationMqBean.setEntityId(serviceOrder.getEntityId());
                rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT, baseNotificationMqBean);
            }
            HashMap<String, String> messageContent1 = new HashMap<>();
            messageContent1.put("productName", serviceOrder.getProductName());
            messageContent1.put("userAccount", user.getAccount());
            messageContent1.put("poolName", serviceOrder.getName());
            messageContent1.put("orderSn", serviceOrder.getOrderSn());
            messageContent1.put("nodeNum", String.valueOf(newValue));
            messageContent1.put("user", user.getAccount());
            List<cn.com.cloudstar.rightcloud.core.pojo.dto.user.User> userByDataScope = userMapper.findAdminstratorsByEntityId(serviceOrder.getEntityId());
            //发送站内信给运营管理员
            if ("扩容".equals(finalOperate)) {

                BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
                baseNotificationMqBean.setMsgId(NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_AI_PRIVATE_SCALE_UP);
                baseNotificationMqBean.getImsgUserIds().addAll(userByDataScope.stream().map(cn.com.cloudstar.rightcloud.core.pojo.dto.user.User::getUserSid).collect(Collectors.toSet()));
                baseNotificationMqBean.setMap(messageContent1);
                baseNotificationMqBean.setEntityId(serviceOrder.getEntityId());
                rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT, baseNotificationMqBean);

            }else {
                BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
                baseNotificationMqBean.setMsgId( NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_AI_PRIVATE_SCALE_DOWN);
                baseNotificationMqBean.getImsgUserIds().addAll(userByDataScope.stream().map(cn.com.cloudstar.rightcloud.core.pojo.dto.user.User::getUserSid).collect(Collectors.toSet()));
                baseNotificationMqBean.setMap(messageContent1);
                baseNotificationMqBean.setEntityId(serviceOrder.getEntityId());
                rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT, baseNotificationMqBean);
            }
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Task checkApprovalProcess(ApproveProcessRequest request, AuthUser authUser) {
        ServiceProcess serviceProcess = serviceProcessService.queryById(request.getServiceProcessId());
        if (Objects.isNull(serviceProcess)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1445877125));
        }

        ProcessInstance instance = runtimeService.createProcessInstanceQuery()
                                                 .variableValueEquals(ProcessConstants.SERVICE_PROCESS_ID,
                                                                      request.getServiceProcessId()
                                                                             .toString())
                                                 .singleResult();
        if (Objects.isNull(instance)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_394907995));
        }

        List<Task> taskList = taskService.createTaskQuery()
                                         .processInstanceId(instance.getProcessInstanceId())
                                         .list()
                                         .stream()
                                         .filter(task -> StringUtils.equalsIgnoreCase(task.getName(),
                                                                                      request.getNodeName()))
                                         .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(taskList)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2013176584));
        }

        Task task = null;
        String userSid = authUser.getUserSid().toString();
        if (taskList.size() > 1) {
            for (Task realTask : taskList) {
                if (StringUtils.equals(userSid, realTask.getAssignee())) {
                    task = realTask;
                    break;
                }
            }
        } else {
            task = taskList.get(0);
        }

        if (Objects.isNull(task)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_316315162));
        }

        ProcessNode processNode = processNodeMapper.selectByPrimaryKey(request.getNodeId());
        ProcessNodeConfig config = BeanConvertUtil.convert(processNode.getConfigData(), ProcessNodeConfig.class);
        Map<String, Object> variables = runtimeService.getVariables(task.getExecutionId());
        //  Map<String, Object> variables = ((TaskEntity) task).getActivityInstanceVariables();
        if (StringUtils.equalsIgnoreCase(processNode.getNodeFeature(), "parallel")) {
            taskService.claim(task.getId(), userSid);
            return task;
        }
        if (processNode.getNodeType().equals(NodeTypeEnum.APPLYTASK.getType())) {
            String applyUserSid = (String) variables.get("_apply_usersid");
            if (StringUtils.equalsIgnoreCase(applyUserSid, userSid)) {
                taskService.claim(task.getId(), userSid);
                return task;
            } else {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1290743044));
            }
        }

        if (Objects.nonNull(config)) {
            String approvalLocation = StringUtils.isNotBlank(config.getApprovalLocation())
                    ? config.getApprovalLocation()
                    : ProcessConstants.APPROVAL_LOCATION_CURRENT;
            if (StringUtils.equalsIgnoreCase(ProcessConstants.APPROVAL_LOCATION_APPLYER, approvalLocation)) {
                String applyUserSid = (String) variables.get("_apply_usersid");
                if (StringUtils.equalsIgnoreCase(applyUserSid, userSid)) {
                    taskService.claim(task.getId(), userSid);
                    return task;
                } else {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_960484795));
                }
            } else {
                List<String> roleIds = roleService.selectRoleByUserSid(Long.parseLong(userSid))
                                                  .stream()
                                                  .map(role -> role.getRoleSid().toString())
                                                  .collect(Collectors.toList());
                boolean candidatesMatch = false;
                boolean candidateThirdsMatch = false;
                if (CollectionUtils.isNotEmpty(roleIds)) {
                    candidatesMatch = config.getCandidates()
                                            .stream()
                                            .anyMatch(processNodeRoleDto -> roleIds.contains(
                                                    processNodeRoleDto.getRefId()));
                } else {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_960215624));
                }
                if (candidatesMatch) {
                    candidateThirdsMatch = config.getCandidateThirds()
                                                 .stream()
                                                 .anyMatch(processNodeRoleDto -> userSid.equals(
                                                         processNodeRoleDto.getRefId()));
                    if (candidateThirdsMatch) {
                        try {
                            taskService.claim(task.getId(), userSid);
                        } catch (Exception e) {
                            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_501313290));
                        }
                        return task;
                    } else {
                        throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1615933454));
                    }
                } else {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_274071896));
                }
            }
        }

        throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1264914955));
    }

    @Override
    @Transactional
    public ProcessTemplateDetail handleTemplateContent(Task task, ApproveProcessRequest request) {
        Criteria criteria = new Criteria();
        criteria.put("processInstanceId", task.getProcessInstanceId());
        criteria.put("nodeId", request.getNodeId());
        criteria.put("templateId", request.getTemplateId());
        List<ProcessTemplateDetail> templateDetails = processTemplateDetailMapper.queryByCriteria(criteria);
        if (org.springframework.util.CollectionUtils.isEmpty(templateDetails)) {
            ProcessTemplateDetail templateDetail = new ProcessTemplateDetail();
            templateDetail.setNodeId(request.getNodeId());
            templateDetail.setProcessInstanceId(task.getProcessInstanceId());
            templateDetail.setTemplateId(request.getTemplateId());
            templateDetail.setTemplateContent(request.getTemplateContent());
            processTemplateDetailMapper.insert(templateDetail);
            return templateDetail;
        } else {
            ProcessTemplateDetail templateDetail = templateDetails.get(0);
            if (Objects.nonNull(templateDetails.get(0).getTemplateId())) {
                ProcessTemplate processTemplate = processTemplateService.queryById(templateDetail.getTemplateId());
                if (StringUtils.equals(processTemplate.getTemplateType(), QUOTA_PROCESS)) {
                    if (StringUtils.isNotBlank(request.getTemplateContent())) {
                        JSONObject templateContent = JSONObject.parseObject(request.getTemplateContent());
                        Object panelDataList = templateContent.get("panelDataList");
                        if (Objects.nonNull(panelDataList)) {
                            templateDetail.setTemplateContent(request.getTemplateContent());
                        }
                    }
                } else {
                    templateDetail.setTemplateContent(request.getTemplateContent());
                }
            } else {
                templateDetail.setTemplateContent(request.getTemplateContent());
            }

            templateDetail.setTemplateId(templateDetail.getTemplateId());
            processTemplateDetailMapper.updateById(templateDetail);
            return templateDetail;
        }
    }

    @Override
    public void processMgtTaskPass(Task task, Map<String, Object> variables, ApproveProcessRequest request,
                                   String templateContent) {
        if (variables == null) {
            variables = Maps.newHashMap();
        }

        variables.put(ProcessConstants.AUDIT, ProcessConstants.AUDIT_PASS);

        if (task == null || task.getProcessInstanceId() == null) {
            return;
        }
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                                                        .processInstanceId(task.getProcessInstanceId()).singleResult();

        // log.info("当前通过任务参数:{}-->{}", JSON.toJSONString(task), JSON.toJSONString(processInstance));
        if (processInstance == null) {

            return;
        }
        String taskId = task.getId();

        variables.put(taskId + ProcessConstants.AUDIT_STATUS_SUFFIX, ProcessConstants.AUDIT_PASS);

        Object comment = variables.get(ProcessConstants.AUDIT_COMMENT);
        if (comment != null) {
            // 保存审批注释(暂时未用到)
            taskService.addComment(taskId, task.getProcessInstanceId(), String.valueOf(comment));

            variables.put(taskId + ProcessConstants.AUDIT_COMMENT_SUFFIX, String.valueOf(comment));
        }

        Object auditUname = variables.get("_audit_uname");
        if (auditUname != null) {
            variables.put(taskId + "_audit_uname", String.valueOf(auditUname));
        }
        variables.put("_task_name", task.getName());
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (Objects.nonNull(authUser)) {
            variables.put(taskId + "_audit_usersid", authUser.getUserSid().toString());
        }

        String advice = (String) variables.get("auditAdvice");
        if (StringUtils.isNotEmpty(advice)) {
            variables.put(taskId + ProcessConstants.AUDIT_ADVICE, advice);
        }

        // 删除回退标识
        // 当前任务
        List<Task> currentTasks = taskService.createTaskQuery()
                                             .processInstanceId(processInstance.getProcessInstanceId())
                                             .list();
        // 当前节点ID
        ProcessTemplateDetail processTemplateDetail = new ProcessTemplateDetail();
        processTemplateDetail.setStatus("approve");
        Map<String, Object> condition = new HashMap<>();
        condition.put("processInstanceId", processInstance.getProcessInstanceId());
        if(Objects.nonNull(currentTasks.get(0).getOwner())){
            condition.put("nodeId", Convert.toLong(currentTasks.get(0).getOwner().replace("nodeid-", StrUtil.EMPTY)));
        }
        processTemplateDetailMapper.updateByParam(processTemplateDetail, condition);

        taskService.resolveTask(taskId, variables);

        Long nodeId = request.getNodeId();
        ProcessNode processNode = processNodeMapper.selectByPrimaryKey(nodeId);
        ProcessNodeConfig config = BeanConvertUtil.convert(processNode.getConfigData(),
                ProcessNodeConfig.class);
        if (Objects.nonNull(config) && "whole".equals(config.getApprovalWay())) {
            List<ProcessNodeRoleDto> candidateThirds = config.getCandidateThirds();
            for (ProcessNodeRoleDto candidateThird : candidateThirds) {
                String refId = candidateThird.getRefId();
                if (Objects.nonNull(authUser) && Objects.equals(refId, authUser.getUserSid().toString())) {
                    continue;
                }
                long count =  taskDetailMapper.getAuditUserCount(taskId, refId);
                if (count < 1) {
                    return;
                }
            }
        }
        taskService.complete(taskId, variables);
    }

    @Override
    public List<AuditProcessNodeResponse> getCurrentAndHistoryAuditNode(String serviceProcessId, String moduleType) {
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (Objects.isNull(authUser)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_756673464));
        }
        this.chedkPermissions(serviceProcessId, moduleType);
        if (UserType.DISTRIBUTOR_USER.equals(authUser.getUserType())) {
            Integer processCount = serviceProcessService.countByCriteria(new Criteria()
                    .put("ownerId", authUser.getUserSid())
                    .put("id", serviceProcessId));
            if (processCount == 0) {
                BizException.e("无权查看该申请单");
            }
        }
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                                                        .variableValueEquals(ProcessConstants.SERVICE_PROCESS_ID,
                                                                             serviceProcessId)
                                                        .singleResult();
        String processDefinitionId;
        String processInstanceId;
        if (Objects.isNull(processInstance)) {
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                                                                            .variableValueEquals(
                                                                                    ProcessConstants.SERVICE_PROCESS_ID,
                                                                                    serviceProcessId)
                                                                            .singleResult();
            processDefinitionId = historicProcessInstance.getProcessDefinitionId();
            processInstanceId = historicProcessInstance.getId();
        } else {
            processDefinitionId = processInstance.getProcessDefinitionId();
            processInstanceId = processInstance.getId();
        }

        List<ProcessNode> processNodes = processNodeMapper.selectAuditNodeByProcessIdentify(processDefinitionId);
        if (CollectionUtils.isEmpty(processNodes)) {
            return Collections.emptyList();
        }
        Long processId = processNodes.get(0).getProcessId();
        if (Objects.nonNull(processId)) {
            Process process = processMapper.selectByPrimaryKey(processId);
            if ("built-in".equals(process.getBusinessCode())) {
                processNodes = processNodes.stream().filter(node -> !"申请".equals(node.getNodeName())).collect(Collectors.toList());
            }
        }
        List<AuditProcessNodeResponse> nodeResponses = BeanConvertUtil.convert(processNodes,
                                                                               AuditProcessNodeResponse.class);

        List<HistoricTaskInstance> hisTasks = historyService.createHistoricTaskInstanceQuery()
                                                            .finishedBefore(new Date())
                                                            .processInstanceId(processInstanceId)
                                                            .orderByHistoricActivityInstanceId()
                                                            .desc()
                                                            .list();

        List<HistoricActivityInstance> acts = historyService.createHistoricActivityInstanceQuery()
                                                            .processInstanceId(processInstanceId)
                                                            .orderByHistoricActivityInstanceId()
                                                            .asc()
                                                            .list();

        Set<String> historyTaskNames = hisTasks.stream()
                                               .map(HistoricTaskInstance::getName)
                                               .collect(Collectors.toSet());

        List<Task> currentTasks = taskService.createTaskQuery()
                                             .processInstanceId(processInstanceId)
                                             .list();

        Set<String> currentTaskNames = currentTasks.stream()
                                                   .map(Task::getName)
                                                   .collect(Collectors.toSet());

        Set<AuditProcessNodeResponse> result = new HashSet<>();
        List<AuditProcessNodeResponse> hisNodes = new ArrayList<>();
        List<AuditProcessNodeResponse> curNodes = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(historyTaskNames)) {
            for (String hisTaskName : historyTaskNames) {
                nodeResponses.forEach(node -> {
                    if (StringUtils.equalsIgnoreCase(node.getNodeName(), hisTaskName)) {
                        List<HistoricTaskInstance> instances = hisTasks.stream()
                                                                       .filter(hisTask -> StringUtils.equals(
                                                                               hisTaskName,
                                                                               hisTask.getName()))
                                                                       .sorted(Comparator.comparing(
                                                                               HistoricTaskInstance::getEndTime))
                                                                       .collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(instances)) {
                            Collections.reverse(instances);
                            HistoricTaskInstance hisTask = instances.get(0);
                             Map<String, Object> adviceVariables = new HashMap<>();
                            for (HistoricActivityInstance act : acts) {
                                if(!OrderStatus.COMPLETED.equals(hisTask.getDeleteReason())){
                                     adviceVariables = runtimeService.getVariables(act.getExecutionId());

                                }else{
                                    List<HistoricVariableInstance> list = historyService.createHistoricVariableInstanceQuery()
                                                                                        .processInstanceId(processInstanceId)
                                                                                        .orderByVariableName()
                                                                                        .desc()
                                                                                        .list();
                                    adviceVariables =list.stream().collect(Collectors.toMap(HistoricVariableInstance::getName,HistoricVariableInstance::getValue));
                                }
                                if (StringUtils.equalsIgnoreCase(act.getTaskId(), hisTask.getId())) {
                                    node.setApproveAdvice((String) Optional
                                            .ofNullable(adviceVariables.get(act.getTaskId() + ProcessConstants.AUDIT_ADVICE))
                                            .orElse(StringUtils.EMPTY));
                                           String approveType = (String) Optional
                                            .ofNullable(adviceVariables.get(act.getTaskId() + ProcessConstants.AUDIT_STATUS_SUFFIX))
                                            .orElse(StringUtils.EMPTY);
                                    //已审批过的节点 只有通过和拒绝的状态会显示。
                                    if (Objects.equals(approveType, ProcessConstants.AUDIT_PASS)) {
                                        node.setApproveType("01");
                                    } else if (Objects.equals(approveType, ProcessConstants.AUDIT_REJECT)) {
                                        node.setApproveType("03");
                                    }
                                }
                            }
                            node.setCurrentAudit(false);
                            node.setAuditable(false);
                            hisNodes.add(node);
                        }
                    }
                });
            }
        }
        if (CollectionUtils.isNotEmpty(currentTaskNames)) {
            for (String currentTaskName : currentTaskNames) {
                nodeResponses.forEach(node -> {
                    if (StringUtils.equalsIgnoreCase(node.getNodeName(), currentTaskName)) {
                        node.setCurrentAudit(true);
                        ProcessNodeConfig config = BeanConvertUtil.convert(node.getConfigData(),
                                                                           ProcessNodeConfig.class);
                        String userSid = authUser.getUserSid().toString();
                        //申请节点均可审批
                        if (node.getNodeType().equals(NodeTypeEnum.APPLYTASK.getType())) {
                            node.setAuditable(true);
                        } else {
                            if (Objects.nonNull(config)) {
                                if ("whole".equals(config.getApprovalWay())) {
                                    List<String> tasks = currentTasks.stream()
                                                                     .map(Task::getAssignee)
                                                                     .filter(s -> StringUtils.equalsIgnoreCase(s,userSid))
                                                                     .collect(Collectors.toList());
                                    if (CollectionUtils.isNotEmpty(tasks)) {
                                        node.setAuditable(true);
                                    }
                                }
                                if (Objects.nonNull(config.getCandidateThirds())){
                                    for (ProcessNodeRoleDto candidateThird : config.getCandidateThirds()) {
                                        if (candidateThird.getRefId().equals(userSid)) {
                                            node.setAuditable(true);
                                            break;
                                        }
                                    }
                                }
                            }
                        }

                        if (Objects.isNull(node.getAuditable())) {
                            node.setAuditable(false);
                        }

                        curNodes.add(node);
                    }
                });
            }
        }

        for (AuditProcessNodeResponse curNode : curNodes) {
            hisNodes.removeIf(node -> node.getCreatedDt().after(curNode.getCreatedDt()));
        }

        result.addAll(hisNodes);
        result.addAll(curNodes);
        // 获取当前是否拥有编辑权限（审批权限，模版操作权限）
        boolean approveFlg = false;
        List<Long> editableFormTemplates = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(curNodes)) {
            approveFlg = curNodes.get(0).getAuditable();
            if (approveFlg) {
                ProcessNodeConfig nodeConfig = BeanConvertUtil.convert(curNodes.get(0).getConfigData(), ProcessNodeConfig.class);
                // 设置当前节点对之前节点模版的控制
                if (CollectionUtil.isNotEmpty(nodeConfig.getEditableFormTemplates())) {
                    editableFormTemplates = nodeConfig.getEditableFormTemplates();
                }
            }
        }

        for (AuditProcessNodeResponse node : result) {
            ProcessNodeConfig nodeConfig = BeanConvertUtil.convert(node.getConfigData(), ProcessNodeConfig.class);
            if (node.getCurrentAudit()) {
                if (Objects.nonNull(nodeConfig)) {
                    Criteria criteria = new Criteria();
                    criteria.put("processInstanceId", processInstanceId);
                    criteria.put("nodeId", node.getId());
                    List<ProcessTemplateDetail> details = processTemplateDetailMapper.queryByCriteria(criteria);
                    if ("whole".equals(nodeConfig.getApprovalWay())) {
                        if (CollectionUtils.isNotEmpty(details)) {
                            putTemplateDetail(node, processInstanceId, details);
                        } else {
                            String formTemplateId = nodeConfig.getFormTemplate();
                            putTemplate(node, formTemplateId);
                        }
                    } else {
                        String formTemplateId = nodeConfig.getFormTemplate();
                        putTemplate(node, formTemplateId);
                        if (CollectionUtils.isNotEmpty(details) && "GoBack".equals(details.get(0).getStatus())) {
                            node.setTemplateContent(details.get(0).getTemplateContent());
                        }
                        //配额申请审批节点需要展示申请信息
                        if (StringUtils.isNotEmpty(nodeConfig.getFormTemplate())) {
                            ProcessTemplate processTemplate = processTemplateService.queryById(Long.valueOf(nodeConfig.getFormTemplate()));
                            if (!ObjectUtil.isNull(processTemplate) && StringUtils.equals(processTemplate.getTemplateType(), QUOTA_PROCESS)) {
                                if(details.size()>0){
                                    node.setTemplateContent(details.get(0).getTemplateContent());
                                }
                            }
                        }
                    }
                    if (approveFlg) {
                        node.setTemplateAuditable(true);
                    } else {
                        node.setCurrentAudit(false);
                    }
                }
            } else {
                if (Objects.nonNull(nodeConfig)) {
                    if (CollectionUtils.isNotEmpty(editableFormTemplates)) {
                        String templateId = nodeConfig.getFormTemplate();
                        if (StringUtils.isNotEmpty(templateId)) {
                            boolean anyMatch = editableFormTemplates.stream()
                                                                    .anyMatch(id -> templateId.equals(id.toString()));
                            if (anyMatch) {
                                node.setTemplateAuditable(true);
                            }
                        }
                    }
                }

                putTemplateDetail(node, processInstanceId);
            }
        }

        if (Objects.isNull(processId)) {
            processId = result.stream().findFirst().orElse(new AuditProcessNodeResponse()).getProcessId();
        }
        if (Objects.nonNull(processId)) {
            Process process = processMapper.selectByPrimaryKey(processId);
            log.info("流程id【{}】", processId);
            if (Objects.nonNull(process)) {
                result.forEach(r -> {
                    r.setBusinessCode(process.getBusinessCode());
                });
            }
        }
        return result.stream()
//                     .sorted((o1, o2) -> cn.hutool.core.date.DateUtil.compare(o1.getCreatedDt(), o2.getCreatedDt()))
                     //目前去掉了并行节点，此处排序可以使用sorm。当有并行节点时，改为上面的排序，同时修改克隆流程接口中给节点的创建时间复制操作。
                     .sorted((o1, o2) -> Float.compare(o1.getSortNum(), o2.getSortNum()))
                     .collect(Collectors.toList());
    }

    @Override
    public void handleServiceProcessResponse(ServiceProcessResponse res) {
        ProcessInstance instance = runtimeService.createProcessInstanceQuery()
                .variableValueEquals(ProcessConstants.SERVICE_PROCESS_ID,
                        res.getId().toString())
               // .includeProcessVariables()
                .singleResult();
        if (Objects.isNull(instance)) {
            return;
        }
        List<ProcessNode> processNodes = processNodeMapper.selectAuditNodeByProcessIdentify(instance.getProcessDefinitionId());
        Set<String> taskNames = taskService.createTaskQuery()
                .processInstanceId(instance.getProcessInstanceId())
                //.includeProcessVariables()
                .list()
                .stream().map(Task::getName).collect(Collectors.toSet());
                //.map(TaskInfo::getName)
                //.collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(taskNames)) {
            return;
        }
        Set<String> candidates = new HashSet<>();
        processNodes.stream().filter(e -> taskNames.contains(e.getNodeName())).forEach(node -> {
            ProcessNodeConfig config = JSON.parseObject(node.getConfigData(), ProcessNodeConfig.class);
            if (Objects.nonNull(config) && CollectionUtil.isNotEmpty(config.getCandidateThirds())) {
                candidates.addAll(config.getCandidateThirds().stream().map(ProcessNodeRoleDto::getRefId).filter(StringUtils::isNotBlank).collect(Collectors.toList()));
            }
        });
        String approverName = candidates.stream().map(e -> userService.selectUser(Long.valueOf(e))).filter(Objects::nonNull).map(cn.com.cloudstar.rightcloud.oss.module.account.bean.model.User::getAccount).collect(
                Collectors.joining(","));
        res.setNodeNames(new ArrayList<>(taskNames));
        res.setApprovers(new ArrayList<>(candidates));
        res.setApproverName(approverName);
    }


    @Override
    public List<ProcessTemplateDetail> getNodeTemplateDetail(String serviceProcessId, Long nodeId) {
        ProcessInstance instance = runtimeService.createProcessInstanceQuery()
                                                 .variableValueEquals(ProcessConstants.SERVICE_PROCESS_ID,
                                                                      serviceProcessId)
                                                 .singleResult();
        if (Objects.isNull(instance)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_394907995));
        }
        Criteria criteria = new Criteria();
        criteria.put("processInstanceId", instance.getId());
        criteria.put("nodeId", nodeId);
        return processTemplateDetailMapper.queryByCriteria(criteria);
    }

    private void putTemplateDetail(@NotNull AuditProcessNodeResponse node, String processInstanceId) {
        Criteria criteria = new Criteria();
        criteria.put("processInstanceId", processInstanceId);
        criteria.put("nodeId", node.getId());
        List<ProcessTemplateDetail> details = processTemplateDetailMapper.queryByCriteria(criteria);
        if (CollectionUtils.isNotEmpty(details)) {
            ProcessTemplateDetail templateDetail = details.get(0);
            if (Objects.nonNull(templateDetail)) {
                node.setTemplateId(templateDetail.getTemplateId());
                node.setTemplateDetailId(templateDetail.getId());
                node.setTemplateContent(templateDetail.getTemplateContent());
            }
        }
    }

    private void putTemplateDetail(AuditProcessNodeResponse node, String processInstanceId,
                                   List<ProcessTemplateDetail> details) {
        ProcessTemplateDetail templateDetail = details.get(0);
        if (Objects.nonNull(templateDetail)) {
            node.setTemplateId(templateDetail.getTemplateId());
            node.setTemplateDetailId(templateDetail.getId());
            node.setTemplateContent(templateDetail.getTemplateContent());
        }
    }

    @Override
    public void chedkPermissions(String serviceProcessId, String moduleType) {
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (Objects.isNull(authUser)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_756673464));
        }
        ServiceProcess serviceProcess = serviceProcessService.queryById(Long.parseLong(serviceProcessId));
        if (serviceProcess == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1231566334));
        }
        if (Constants.CONSOLE.equals(moduleType)) {
            if (Objects.isNull(authUser.getParentSid())) {
                if (!serviceProcess.getOwnerId().equals(String.valueOf(authUser.getUserSid()))) {
                    throw new BizException(WebUtil.getMessage(MsgCd.NOT_HAVE_PERMISSION_VIEW));
                }
            } else {
                if (Objects.isNull(serviceProcess.getSubUserId()) || !serviceProcess.getSubUserId()
                                                                                    .equals(String.valueOf(
                                                                                            authUser.getUserSid()))) {
                    throw new BizException(WebUtil.getMessage(MsgCd.NOT_HAVE_PERMISSION_VIEW));
                }
            }
        }

        if(serviceProcess.getEntryId() != null && !serviceProcess.getEntryId().equals(authUser.getEntityId()) && Objects.isNull(authUser.getOrgSid())){
            throw new BizException(WebUtil.getMessage(MsgCd.NOT_HAVE_PERMISSION_VIEW));
        }
    }

    private void putTemplate(AuditProcessNodeResponse node, String formTemplateId) {
        if (StringUtils.isNotEmpty(formTemplateId)) {
            ProcessTemplate processTemplate = processTemplateService.queryById(
                    Long.parseLong(formTemplateId));
            if (Objects.nonNull(processTemplate)) {
                node.setTemplateId(processTemplate.getId());
                node.setTemplateContent(processTemplate.getTemplateContent());
            }
        }
    }


    @Override
    public Integer updateNodeTemplateDetail(ProcessTemplateDetail templateDetail) {
        return processTemplateDetailMapper.updateById(templateDetail);
    }


    @Override
    public List<String> auditApplyInlineMgts(DelegateExecution execution, String auditIds,
                                             String orgSidStr, String approvalLocadoSynction, String nodeId) {
        Map<String, Object> variables = execution.getVariables();
        String applyAccount = variables.get("_apply_usersid").toString();
        return Collections.singletonList(applyAccount);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> auditCandidateOnlyUserIds(DelegateExecution execution, String auditIds) {
        String[] auditIdArr = auditIds.split(",");

        List<String> userIds = Lists.newArrayList();

        for (String auditId : auditIdArr) {
            String[] split = auditId.split("-");
            //已20开头的是用户 ID，已10开头的是角色 ID
            if (split[0].startsWith("20")) {
                userIds.add(split[1]);
            }
        }

        return userIds.stream().distinct().collect(Collectors.toList());
    }





    @Override
    public List<ProcessActivityDto> instanceRecordsByServiceProcessId(String serviceProcessId) {
        HistoricProcessInstance hpi = historyService.createHistoricProcessInstanceQuery()
                                                    .variableValueEquals(ProcessConstants.SERVICE_PROCESS_ID, serviceProcessId).singleResult();
        if (hpi == null) {
            return Collections.emptyList();
        }

        List<HistoricActivityInstance> activitiyInstances = historyService
                .createHistoricActivityInstanceQuery()
                .processInstanceId(hpi.getId())
                .orderByHistoricActivityInstanceId().asc().list();
        //把结束事件放到最后展示
        List<HistoricActivityInstance> collect =
                activitiyInstances.stream().filter(a -> "noneEndEvent".equals(a.getActivityType())).collect(Collectors.toList());
        activitiyInstances.removeIf(a -> "noneEndEvent".equals(a.getActivityType()));
        activitiyInstances.addAll(collect);
        List<ProcessActivityDto> acts = new ArrayList<>();
        ProcessActivity actDto = null;
        Map<String, Object> variables = null;

        String processDefinitionId = hpi.getProcessDefinitionId();

        ProcessDefinitionEntity entity = (ProcessDefinitionEntity) repositoryService
                .getProcessDefinition(processDefinitionId);
        boolean isUs = WebUtil.getHeaderAcceptLanguage();
        for (HistoricActivityInstance act : activitiyInstances) {
            String activityName = act.getActivityName();
            if (isUs) {
                if ("运营管理员审批".equals(activityName)) {
                    activityName = "Approval by Operations Administrator";
                } else if ("领导审批".equals(activityName)) {
                    activityName = "Leadership approval";
                } else if ("拒绝并关闭".equals(activityName)) {
                    activityName = "Reject and close";
                } else if ("执行".equals(activityName)) {
                    activityName = "execute";
                } else if ("申请".equals(activityName)) {
                    activityName = "apply";
                }
            }

            if ("startEvent".equals(act.getActivityType())) {
                actDto = new ProcessActivity();
                actDto.setId(act.getId());
                actDto.setActivityId(act.getActivityId());
                actDto.setActivityName(isUs ? "Submit application process" : "提交申请流程");
                actDto.setAssigneeId(act.getAssignee());
                actDto.setActivityType(act.getActivityType());
                actDto.setStartTime(act.getStartTime());
                actDto.setEndTime(act.getEndTime());
                actDto.setStatus("提交申请");

                if (variables == null) {
                    variables = executionVariables(act.getExecutionId());
                }
                actDto.setAssigneeName("--");
                actDto.setAssigneeId((String) variables.get("_apply_usersid"));
                actDto.setComment((String) variables.get("__process_start_comment"));

                acts.add(actDto);
            } else if ("noneEndEvent".equals(act.getActivityType())) {
                actDto = new ProcessActivity();
                actDto.setId(act.getId());
                actDto.setActivityId(act.getActivityId());
                actDto.setActivityName(isUs ? "Process completed" : "流程结束");
                actDto.setActivityType(act.getActivityType());
                actDto.setStartTime(act.getStartTime());
                actDto.setEndTime(act.getEndTime());
                actDto.setStatus("结束");
                actDto.setComment("流程结束");

                actDto.setAssigneeId("SYSTEM");
                actDto.setAssigneeName("[系统]");

                acts.add(actDto);
            } else if ("userTask".equals(act.getActivityType())) {
                ServiceProcess serviceProcess = serviceProcessService.queryById(Long.parseLong(serviceProcessId));
                Process process = processMapper.selectByPrimaryKey(serviceProcess.getProcessId());

                if ("built-in".equals(process.getBusinessCode()) && "申请".equals(act.getActivityName())) {
                    continue;
                }
                actDto = new ProcessActivity();
                actDto.setId(act.getId());
                actDto.setTaskId(act.getTaskId());
                actDto.setActivityId(act.getActivityId());
                actDto.setActivityName(activityName);
                String auditUserSid = (String) Optional
                        .ofNullable(variables.get(act.getTaskId() + "_audit_usersid"))
                        .orElse(StringUtils.EMPTY);
                actDto.setAssigneeId(auditUserSid);
                actDto.setActivityType(act.getActivityType());
                actDto.setStartTime(Optional.ofNullable(act.getStartTime()).orElse(act.getEndTime()));

                if (act.getEndTime() == null) {
                    actDto.setStatus("审批中");
                    List<IdentityLink> identitys = taskService
                            .getIdentityLinksForTask(act.getTaskId())
                            .stream()
                            .filter(identityLink -> !"owner".equals(identityLink.getType()))
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(identitys)) {
                        String condiates = identitys.stream().map(identity -> {
                            User user = userService.selectByPrimaryKey(Long.parseLong(identity.getUserId()));
                            if (user != null) {
                                return user.toNameInfo();
                            }
                            return "";
                        }).collect(Collectors.joining(","));
                        actDto.setComment(String.format("等待[%s]审批", condiates));
                        actDto.setAssigneeName(String.format("[%s]", condiates));
                    }
                } else {
                    String status = (String) Optional
                            .ofNullable(variables.get(actDto.getTaskId() + ProcessConstants.AUDIT_STATUS_SUFFIX))
                            .orElse(StringUtils.EMPTY);
                    String advice = (String) Optional
                            .ofNullable(variables.get(actDto.getTaskId() + ProcessConstants.AUDIT_ADVICE))
                            .orElse(StringUtils.EMPTY);
                    if (StringUtils.isNotEmpty(status)) {
                        if (ProcessConstants.AUDIT_PASS.equals(status)) {
                            actDto.setStatus("通过");
                        }
                        if (ProcessConstants.AUDIT_REJECT.equals(status)) {
                            actDto.setStatus("拒绝");
                        }
                        if (status.contains(ProcessConstants.AUDIT_GOBACK)) {
                            actDto.setStatus("驳回");
                        }
                        actDto.setStatus("审批" + actDto.getStatus() + "，审批意见：" + advice);
                    } else {
                        actDto.setStatus("已审批");
                    }
                    actDto.setEndTime(act.getEndTime());
                }
                if (Objects.equals(act.getActivityName(), NodeTypeEnum.APPLYTASK.getValue())) {
                    actDto.setAssigneeName("--");
                    actDto.setStatus("提交申请");
                }

                acts.add(actDto);
            } else if ("exclusiveGateway".equals(act.getActivityType())) {
                    actDto = new ProcessActivity();
                    actDto.setId(act.getId());
                    actDto.setActivityId(act.getActivityId());
                    actDto.setActivityName(activityName);
                    actDto.setActivityType(act.getActivityType());
                    actDto.setStartTime(act.getStartTime());
                    actDto.setEndTime(act.getEndTime());
                    actDto.setAssigneeId("SYSTEM");

                    if (variables == null) {
                        variables = executionVariables(act.getExecutionId());
                    }
                    if (variables.containsKey(actDto.getActivityId()
                                                      + ProcessConstants.AUDIT_COMMENT_SUFFIX)) {

                        actDto.setComment(variables.get(actDto.getActivityId()
                                                                + ProcessConstants.AUDIT_COMMENT_SUFFIX).toString());
                    }

                    if (variables.containsKey(actDto.getTaskId() + "_audit_uname")) {
                        actDto.setAssigneeName(variables.get(actDto.getTaskId() + "_audit_uname").toString());
                    } else {
                        if ("SYSTEM".equals(actDto.getAssigneeId())) {
                            actDto.setAssigneeName("[系统]");
                        } else {
                            if (StringUtils.isNotEmpty(actDto.getAssigneeId())) {
                                try {
                                    User user = userService.selectByPrimaryKey(Long.parseLong(actDto.getAssigneeId()));
                                    if (user != null) {
                                        actDto.setAssigneeName(user.getRealName());
                                    }
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            }
                        }
                    }

                if (act.getEndTime() == null) {
                    actDto.setStatus("审批中");
                    List<IdentityLink> identitys = taskService
                            .getIdentityLinksForTask(act.getTaskId())
                            .stream()
                            .filter(identityLink -> !"owner".equals(identityLink.getType()))
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(identitys)) {
                        String condiates = identitys.stream().map(identity -> {
                            User user = userService.selectByPrimaryKey(Long.parseLong(identity.getUserId()));
                            if (user != null) {
                                return user.toNameInfo();
                            }
                            return "";
                        }).collect(Collectors.joining(","));
                        actDto.setComment(String.format("等待[%s]审批", condiates));
                        actDto.setAssigneeName(String.format("[%s]", condiates));
                    }
                } else {
                    String status = (String) Optional
                            .ofNullable(variables.get(actDto.getTaskId() + ProcessConstants.AUDIT_STATUS_SUFFIX))
                            .orElse(StringUtils.EMPTY);
                    String advice = (String) Optional
                            .ofNullable(variables.get(actDto.getTaskId() + ProcessConstants.AUDIT_ADVICE))
                            .orElse(StringUtils.EMPTY);
                    if (StringUtils.isNotEmpty(status)) {
                        if (ProcessConstants.AUDIT_PASS.equals(status)) {
                            actDto.setStatus("通过");
                        }
                        if (ProcessConstants.AUDIT_REJECT.equals(status)) {
                            actDto.setStatus("拒绝");
                        }
                        if (status.contains(ProcessConstants.AUDIT_GOBACK)) {
                            actDto.setStatus("驳回");
                        }
                        actDto.setStatus("审批" + actDto.getStatus() + "，审批意见：" + advice);
                    } else {
                        actDto.setStatus("已审批");
                    }
                    actDto.setEndTime(act.getEndTime());
                }
                if (Objects.equals(act.getActivityName(), NodeTypeEnum.APPLYTASK.getValue())) {
                    actDto.setAssigneeName("--");
                    actDto.setStatus("提交申请");
                }
                acts.add(actDto);
            } else if ("serviceTask".equals(act.getActivityType())) {
                actDto = new ProcessActivity();
                actDto.setId(act.getId());
                actDto.setActivityId(act.getActivityId());
                actDto.setActivityName(activityName);
                actDto.setActivityType(act.getActivityType());
                actDto.setStartTime(act.getStartTime());
                actDto.setEndTime(act.getEndTime());

                actDto.setAssigneeId("SYSTEM");
                actDto.setAssigneeName("[系统]");

                if (variables == null) {
                    variables = executionVariables(act.getExecutionId());
                }

                String audit = (String) variables.get(ProcessConstants.AUDIT);
                if (ProcessConstants.AUDIT_PASS.equals(audit)) {
                    actDto.setStatus("即将结束");
                    actDto.setComment("执行结束前业务操作");

                    String businessCode = (String) variables.get("_business_code");
                    if (businessCode != null) {
                        String businessName = ProcessConstants.BUSINESS_MAP.get(businessCode);
                        if (businessName != null) {
                            actDto.setComment(String.format("执行[%s]", businessName));
                        }
                    }

                } else if (ProcessConstants.AUDIT_REJECT.equals(audit)) {
                    actDto.setStatus("拒绝并关闭");
                    actDto.setComment("关闭流程");
                }

                acts.add(actDto);
            }
        }

        Map<String, User> users = Maps.newHashMap();
        User system = new User();
        //system.setName("自动处理");
        users.put("SYSTEM", system);

        for (ProcessActivityDto act : acts) {
            if (!users.containsKey(act.getAssigneeId())) {
                try {
                    User user = userService.selectByPrimaryKey(Long.parseLong(act.getAssigneeId()));
                    users.put(act.getAssigneeId(), user);
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            }

            User user = users.get(act.getAssigneeId());
            if (user != null && StringUtils.isBlank(act.getAssigneeName())) {
                act.setAssigneeName(user.toNameInfo());
            }

            ActivityImpl activity = entity.findActivity(act.getActivityId());
            act.setActLeft(activity.getX());
            act.setActTop(activity.getY());
            act.setActWidth(activity.getWidth());
            act.setActHeight(activity.getHeight());

            if (act.getEndTime() == null) {
                act.setActived(true);
            }
        }

        return acts;
    }


    @Override
    public ProcessDto selectByServiceProcessId(String serviceProcessId) {
/*
        HistoricProcessInstance hpi = historyService.createHistoricProcessInstanceQuery()
                                                    .variableValueEquals(ProcessConstants.SERVICE_PROCESS_ID, serviceProcessId).singleResult();
*/
        HistoricProcessInstance hpi = this.processEngine.getHistoryService().createHistoricProcessInstanceQuery()
                                                        .variableValueEquals(ProcessConstants.SERVICE_PROCESS_ID, serviceProcessId)
                                                        .singleResult();

        if (hpi == null) {
            return null;
        }

        ProcessDefinition define = repositoryService.createProcessDefinitionQuery()
                                                    .processDefinitionId(hpi.getProcessDefinitionId()).singleResult();
        if (define == null) {
            return null;
        }

        ProcessDto processDto = new ProcessDto();
        processDto.setDefineKey(hpi.getProcessDefinitionKey());
        processDto.setCode(hpi.getProcessDefinitionKey());
        processDto.setName(hpi.getProcessDefinitionName());
        BpmnModelInstance bpmnModelInstance = repositoryService.getBpmnModelInstance(define.getId());
        String xmlPng = Bpmn.convertToString(bpmnModelInstance);
        boolean isUs = WebUtil.getHeaderAcceptLanguage();
        if (isUs) {
            xmlPng = this.xmlPngToUs(xmlPng);
        }
        processDto.setImage(xmlPng);
      //  processDto.setImage(processPngImage(define));

        return processDto;
    }

    private String xmlPngToUs(String xmlPng) {
        xmlPng = xmlPng.replaceAll("双重审核流程-资源审批", "Dual review process - resource approval")
                        .replaceAll("双重审核流程-非资源审批", "Dual review process - Non resource approval")
                        .replaceAll("运营管理员预审批", "Pre approval by Operations Administrator")
                        .replaceAll("运营管理员审批", "Approval by Operations Administrator")
                        .replaceAll("拒绝并关闭", "Reject and close")
                        .replaceAll("领导审批", "Leadership approval")
                        .replaceAll("开始", "start")
                        .replaceAll("申请", "apply")
                        .replaceAll("拒绝", "refuse")
                        .replaceAll("执行", "execute")
                        .replaceAll("通过", "adopt")
                        .replaceAll("驳回", "reject")
                        .replaceAll("结束", "end");
        return xmlPng;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doSync(DelegateExecution execution) {
        Map<String, Object> variables = execution.getVariables();
        String serviceProcessId = (String) variables.get(ProcessConstants.SERVICE_PROCESS_ID);

        if (StringUtils.isNotBlank(serviceProcessId)) {
            ServiceProcess serviceProcess = serviceProcessService.queryById(Long.parseLong(serviceProcessId));
            if (Objects.nonNull(serviceProcess)) {
                serviceProcess.setStatus(ServiceProcessStatus.COMPLETE);
                serviceProcessService.updateById(serviceProcess);
                log.info("流程申请[{}]的审批流程已经全部审批通过，执行流程申请状态更改", serviceProcess.getName());
            }
        }


    }

    @Override
    public List<Process> builtInlist() {
        List<String> filter = new ArrayList<>();
        // 标准版不包含hpc
        LicenseVo licenseVo = LicenseUtil.queryLicenseInfoFromDb();
        if (Objects.nonNull(licenseVo)) {
            String versionType = licenseVo.getVersionType();
            if (LicenseUtil.STAND.equals(versionType)) {
                filter.addAll(Arrays.asList("双重审核流程-资源审批-hpc", "双重审核流程-非资源审批-hpc"));
            }
        }
        List<Process> processes = processMapper.selectByCode("built-in");
        List<Process> collect = processes.stream().filter(e -> !filter.contains(e.getProcessName())).collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getProcessName()))), ArrayList::new));
        return collect;
    }
    /**
     * MA开通出账
     *
     * @param serviceOrderPriceDetails
     * @param costs
     * @param detail
     * @param serviceOrder
     * @param priceDetailQuantityFlg
     * @param orderSn
     * @param bizBillingAccount
     * @param quantity
     * @param serviceConfigObj
     * @param resourceIds
     * @param resourceNames
     * @param cueValue
     * @param duration
     * @param isOnePriceComputed
     * @param onePriceComputed
     * @param couponAmount
     * @param serviceConfig
     * @param applyResourceStartTime
     * @param applyResourceEndTime
     * @param bizCouponAccount
     * @return List<InstanceGaapCost>
     */
    private List<InstanceGaapCost> maApplyAprove(
            List<ServiceOrderPriceDetail> serviceOrderPriceDetails,
            List<InstanceGaapCost> costs, ServiceOrderDetail detail,
            ServiceOrder serviceOrder, boolean priceDetailQuantityFlg, String orderSn,
            BizBillingAccount bizBillingAccount,
            BigDecimal quantity, cn.hutool.json.JSONObject serviceConfigObj,
            List<String> resourceIds, List<String> resourceNames, Long cueValue, BigDecimal duration,
            boolean isOnePriceComputed, boolean onePriceComputed, BigDecimal couponAmount,
            String serviceConfig, Date applyResourceStartTime, Date applyResourceEndTime, BizCouponAccount bizCouponAccount) {
        log.info("ma开通出账....");


        if (OrderType.RENEW.equals(serviceOrder.getType())) {
            couponAmount = BigDecimalUtil.getTwoPointAmount(NumberUtil.mul(couponAmount, quantity));
            //续订的时候，如果单价* 数量 的误差跟订单的优惠券金额误差在1分钱的时候，就使用订单里面的总的优惠券金额,以防止差一分钱,如果大于1分钱就按照单价*数量扣优惠金额
            if (onePennyMoney.compareTo(NumberUtil.sub(serviceOrder.getCouponDiscount(), BigDecimalUtil.getTwoPointAmount(couponAmount))) >= 0) {
                couponAmount = BigDecimalUtil.getTwoPointAmount(serviceOrder.getCouponDiscount());
            }
        }
        for (ServiceOrderPriceDetail priceDetail : serviceOrderPriceDetails) {
            log.info("ma开通出账循环开始......");
            String priceType = priceDetail.getPriceType();
            String fixedMonth = priceDetail.getFixedMonth();
            InstanceGaapCost cost = new InstanceGaapCost();
            cost.setQuantity(priceDetailQuantityFlg ? priceDetail.getQuantity() : detail.getQuantity());
            cost.setOrderId(serviceOrder.getId().toString());
            cost.setOrderSn(orderSn);
            cost.setPayTime(new Date());
            long offsetDay = DateUtil.calculateOffDay(priceDetail.getStartTime(), priceDetail.getEndTime(),
                    true);
            cost.setUsageCount(offsetDay + "天");
            if (OrderType.EXPIRED.equals(priceDetail.getType())) {
                log.info("ma开通出账-priceDetail开始结束时间【{}】【{}】", priceDetail.getStartTime(), priceDetail.getEndTime());
                cost.setUsageStartDate(priceDetail.getStartTime());
                cost.setUsageEndDate(DateUtil.addHours(priceDetail.getStartTime(), (int)offsetDay * 24));
            } else {
                log.info("ma开通出账-detail【{}】【{}】", detail.getStartTime(), detail.getEndTime());
                cost.setUsageStartDate(detail.getStartTime());
                cost.setUsageEndDate(detail.getEndTime());
                priceDetail.setStartTime(detail.getStartTime());
                priceDetail.setEndTime(detail.getEndTime());
                cost.setUsageCount(DateUtil.calculateOffDay(priceDetail.getStartTime(), priceDetail.getEndTime(),
                                                            true) + "天");
            }

            cost.setBillType(BillType.fromChargeTypeEnum(detail.getChargeType()));
            cost.setBillingCycle(
                    cn.hutool.core.date.DateUtil.format(cn.hutool.core.date.DateUtil.date(), "yyyy-MM"));
            cost.setOrgSid(serviceOrder.getOrgSid());
            cost.setOwnerId(serviceOrder.getOwnerId().toString());
            cost.setBillNo(NoUtil.generateNo("ZD"));
            cost.setCurrency("CNY");
            cost.setPriceType(priceType);
            cost.setProductCode(priceDetail.getProductCode());
            cost.setProductName(ProductCodeEnum.toDesc(priceDetail.getProductCode()));
            cost.setBillSource("platform");
            cost.setUserAccountId(bizBillingAccount.getId());
            Long adminSid = bizBillingAccount.getAdminSid();
            cn.com.cloudstar.rightcloud.core.pojo.dto.user.User user = userMapper.selectByPrimaryKey(adminSid);
            String accountName = bizBillingAccount.getCreatedBy();
            if (user != null) {
                accountName = user.getAccount();
            }
            cost.setUserAccountName(accountName);
            cost.setPrice(priceDetail.getPrice());
            cost.setConfiguration(priceDetail.getBillingSpec());
            cost.setEntityId(serviceOrder.getEntityId());
            cost.setEntityName(serviceOrder.getEntityName());
            cost.setType(serviceOrder.getType());
            cost.setOrderSourceSn(serviceOrder.getOrderSourceSn());
            // originalCost 原价
            BigDecimal originalCost = BigDecimal.ZERO;
            BigDecimal finalCost = BigDecimal.ZERO;
            if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode().equals(detail.getServiceType())) {
                originalCost = priceDetail.getOriginalCost().multiply(quantity);
                // tradeFinalCost 最终价格
                if (OrderType.EXPIRED.equals(priceDetail.getType())) {
                    finalCost = priceDetail.getAmount().multiply(quantity);
                } else {
                    finalCost = priceDetail.getTradePrice().multiply(quantity);
                }
                if (ApplyTypeEnum.DEPTRAIN.getType().equals(detail.getApplyType())) {
                    ResMaPoolVO resMaPoolById = maRemoteService.getResMaPoolById(serviceOrder.getClusterId());
                    log.info("资源ID,{}", resMaPoolById.getName());
                    cost.setInstanceId(resMaPoolById.getName());
                    cost.setResourceId(resMaPoolById.getName());
                } else {
                    cost.setInstanceId(resourceIds.get(0));
                    cost.setResourceId(resourceIds.get(0));
                }
                cost.setInstanceName(serviceOrder.getName());
            } else {
                originalCost = priceDetail.getOriginalCost();
                // tradeFinalCost 最终价格
                finalCost = priceDetail.getTradePrice();
                cost.setInstanceId(resourceIds.get(0));
                cost.setInstanceName(resourceNames.get(0));
                cost.setResourceId(resourceIds.get(0));
            }
            if (PriceType.RESOURCE.equals(priceType)) {

                String finalCueValue = null;
                if (cueValue != null) {
                    long dur = detail.getEndTime().getTime() - detail.getStartTime().getTime();
                    finalCueValue = BigInteger.valueOf(cueValue)
                            .multiply(NumberUtil.div(BigDecimal.valueOf(dur),
                                    1000 * 60 * 60)
                                    .setScale(0, BigDecimal.ROUND_HALF_UP)
                                    .toBigInteger())
                            .toString();
                }
                cost.setCueValue(finalCueValue);

                if (Objects.equals(priceDetail.getServiceType(), "contract")) {
                    BigDecimal originalTmp = NumberUtil.div(originalCost, quantity)
                            .setScale(3, BigDecimal.ROUND_HALF_UP);
                    BigDecimal finalTmp = NumberUtil.div(finalCost, quantity);

                    originalCost = originalTmp;
                    finalCost = finalTmp;

                } else {
                    if (!BooleanEnum.YES.getCode().equals(fixedMonth)) {
                        if (OrderType.APPLY.equals(priceDetail.getType())) {
                            originalCost = NumberUtil.mul(originalCost, duration);
                        }
                        if (!OrderType.EXPIRED.equals(priceDetail.getType())) {
                            finalCost = NumberUtil.mul(finalCost, duration);
                        }
                    }
//                    if (OrderType.RENEW.equals(serviceOrder.getType())) {
//                        originalCost = NumberUtil.div(originalCost, quantity);
//                        finalCost = NumberUtil.div(finalCost, quantity);
//                    }
                }
            }
            if (PriceType.SERVICE.equals(priceType)) {
                if (!BooleanEnum.YES.getCode().equals(fixedMonth)) {
                    originalCost = NumberUtil
                            .mul(priceDetail.getOriginalCost(), duration);
                    finalCost = NumberUtil.mul(priceDetail.getTradePrice(), duration);
                }
//                if (OrderType.RENEW.equals(serviceOrder.getType())) {
//                    originalCost = NumberUtil.div(originalCost, quantity);
//                    finalCost = NumberUtil.div(finalCost, quantity);
//                }
            }
            if (PriceType.EXTRA_CONFIG.equals(priceType)) {
                BigDecimal oncePrice = priceDetail.getOncePrice();
                if (Objects.nonNull(oncePrice)) {
                    if (isOnePriceComputed) {
                        originalCost = BigDecimal.ZERO;
                        finalCost = BigDecimal.ZERO;
                    } else {
                        originalCost = oncePrice;
                        finalCost = oncePrice;
                        // isOnePriceComputed = true;
                    }
                } else {
                    if (!BooleanEnum.YES.getCode().equals(fixedMonth)) {
                        if (OrderType.APPLY.equals(priceDetail.getType())) {
                            originalCost = NumberUtil
                                    .mul(priceDetail.getOriginalCost(), duration);
                        }
                        if (!OrderType.EXPIRED.equals(priceDetail.getType())) {
                            finalCost = NumberUtil
                                    .mul(priceDetail.getOriginalCost(), duration);
                        }
                    }
//                    if (OrderType.RENEW.equals(serviceOrder.getType())) {
//                        originalCost = NumberUtil.div(originalCost, quantity);
//                        finalCost = NumberUtil.div(finalCost, quantity);
//                    }
                }
            }
            //对最终的金额做抹零处理
            cost.setPricingDiscount(NumberUtil.sub(originalCost, finalCost));

            if (!OrderType.EXPIRED.equals(priceDetail.getType())
                    && NumberUtil.isGreater(couponAmount, BigDecimal.ZERO)) {
                if (NumberUtil.isGreaterOrEqual(couponAmount, finalCost)) {
                    couponAmount = NumberUtil.sub(couponAmount, finalCost);
                    cost.setCouponDiscount(finalCost);
                    finalCost = BigDecimal.ZERO;
                } else {
                    finalCost = NumberUtil.sub(finalCost, couponAmount);
                    cost.setCouponDiscount(couponAmount);
                    couponAmount = BigDecimal.ZERO;
                }
            }

            if (Objects.nonNull(bizCouponAccount)) {
                BigDecimal costCouponDiscount = Objects.isNull(cost.getCouponDiscount()) ? BigDecimal.ZERO : cost.getCouponDiscount();
                bizCouponAccount.setDiscountUsed(costCouponDiscount.add(bizCouponAccount.getDiscountUsed()));
            }

            //关联不计费产品设置实付金额为0
            if (Objects.nonNull(serviceOrder.getChargingType()) && SALE_TYPE.equalsIgnoreCase(serviceOrder.getChargingType())) {
                cost.setCashAmount(BigDecimal.ZERO);
                cost.setCreditAmount(BigDecimal.ZERO);
                cost.setCouponAmount(BigDecimal.ZERO);
                finalCost = BigDecimal.ZERO;
                cost.setChargingType(SALE_TYPE);
            } else {
                cost.setChargingType(NORMAL_TYPE);
                log.info("ma出账价格:{}", finalCost);
                setUsedCost(bizBillingAccount, cost, BigDecimalUtil.remainTwoPointAmount(finalCost));
                log.info("ma出账信用额度金额....");
                bizBillingAccount.setBalance(
                        NumberUtil.sub(bizBillingAccount.getBalance(), cost.getCashAmount()));
                bizBillingAccount.setCreditLine(NumberUtil
                        .sub(bizBillingAccount.getCreditLine(),
                                cost.getCreditAmount()));
                bizBillingAccount.setBalanceCash(NumberUtil
                        .sub(bizBillingAccount.getBalanceCash(),
                                cost.getCouponAmount()));
                log.info("出账后账户信用额度:{}", bizBillingAccount.getCreditLine());
                log.info("账户id:{}", bizBillingAccount.getId());
            }

            log.info("ma出账价格:{}， 原始金额： {}", finalCost, originalCost);
            cost.setPretaxGrossAmount(originalCost);
            cost.setEraseZeroAmount(BigDecimalUtil.getEraseZeroColumn(finalCost));
            cost.setPretaxAmount(BigDecimalUtil.remainTwoPointAmount(finalCost));
            cost.setBizBillingAccount(
                    BeanUtil.toBean(bizBillingAccount, BizBillingAccount.class));
            cost.setType(serviceOrder.getType());
            cost.setOrderSourceSn(serviceOrder.getOrderSourceSn());

            JSONObject data = JSONObject.parseObject(serviceConfig);
            if (data != null) {
                Long cloudEnvId = data.getLong("cloudEnvId");
                if (cloudEnvId != null) {
                    CloudEnv cloudEnv = cloudEnvRemoteService.selectByPrimaryKey(cloudEnvId);
                    cost.setCloudEnvId(cloudEnv.getId());
                    cost.setCloudEnvName(cloudEnv.getCloudEnvName());
                    cost.setCloudEnvType(cloudEnv.getCloudEnvType());
                    cost.setRegion(cloudEnv.getRegion());
                }
            } else {
                cost.setCloudEnvId(detail.getCloudEnvId());
                cost.setCloudEnvName(detail.getCloudEnvName());
                cost.setCloudEnvType(detail.getCloudEnvType());
                cost.setRegion(detail.getRegion());
            }

            this.processCostComputingPower(cost, detail);
            this.processDistributorName(cost, serviceOrder);
            costs.add(cost);
            //AI专属资源池回填开始时间和结束时间
            if (applyResourceStartTime != null) {
                priceDetail.setStartTime(applyResourceStartTime);
                priceDetail.setEndTime(applyResourceEndTime);
            }

            //priceDetail 设置实际扣除的 余额，充值现金券，信用额度
            if (ProductCodeEnum.DEDICATED_RESOURCE_POOL.getProductCode().equals(detail.getServiceType())) {
                priceDetail.setPayBalance(cost.getCashAmount());
                priceDetail.setPayBalanceCash(cost.getCouponAmount());
                priceDetail.setPayCreditLine(cost.getCreditAmount());
                serviceOrderPriceDetailMapper.updateByPrimaryKeySelective(priceDetail);
                if (OrderType.RENEW.equals(priceDetail.getType()) && ApplyTypeEnum.DEPTRAIN.getType().equals(detail.getApplyType())) {
                    ServiceOrderResourceRef serviceOrderResourceRef = new ServiceOrderResourceRef();
                    serviceOrderResourceRef.setOrderDetailId(detail.getId());
                    HashMap<String, Object> condition = new HashMap<>(2);
                    condition.put("resourceId", serviceConfigObj.get("productResourceId"));
                    condition.put("type", detail.getServiceType());
                    serviceOrderResourceRefMapper.updateByParamsSelective(serviceOrderResourceRef, condition);
                }

                //关联不计费产品设置实付金额为0
                if (Objects.nonNull(serviceOrder.getChargingType()) && SALE_TYPE.equalsIgnoreCase(serviceOrder.getChargingType())) {
                    finalCost = serviceOrder.getCurrAmount();
                    serviceOrder.setFinalCost(finalCost);
                    serviceOrderMapper.updateByPrimaryKeySelective(serviceOrder);
                }
            } else {
                priceDetail.setPayBalance(cost.getCashAmount());
                priceDetail.setPayBalanceCash(cost.getCouponAmount());
                priceDetail.setPayCreditLine(cost.getCreditAmount());
                serviceOrderPriceDetailMapper.updateByPrimaryKeySelective(priceDetail);
            }
        }
        return costs;

    }


    /**
     * AI专属变更
     *
     * @param serviceOrder
     * @param cloudEnvs
     * @param resMaPoolVO
     * @param newValue
     */
    private void updateMAPools(ServiceOrder serviceOrder, List<CloudEnv> cloudEnvs, ResMaPoolVO resMaPoolVO,
                               Integer newValue) {

        AuthUser authUser = cn.com.cloudstar.rightcloud.oss.common.util.BeanConvertUtil.convert(BasicInfoUtil.getUserInfoByUserSid(serviceOrder.getOwnerId()), AuthUser.class);
        AuthUserHolder.setAuthUser(authUser);
        log.info("ProcessServiceImpl.updateMAPools AI专属变更userSid: {}", authUser.getUserSid());

        MAPoolsUpdate maPoolsUpdate = CloudClientFactory.buildMQBean(cloudEnvs.get(0).getId(), MAPoolsUpdate.class);
        maPoolsUpdate.setPoolName(resMaPoolVO.getName());

        //资源池的spec信息
        PoolSpec poolSpec = new PoolSpec();
        cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.Resource resource = new cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.Resource();

        String config = serviceOrder.getDetails().get(0).getServiceConfig();
        JSONObject jsonServiceConfig = JSON.parseObject(config);
        JSONObject data = jsonServiceConfig.getJSONObject("data");
        String flavor = data.getJSONObject("DRP").getString("category");
        resource.setFlavor(flavor);
        resource.setCount(newValue);

        JSONArray jsonArray = new JSONArray();
        if(resMaPoolVO.getAzNods() != null && !"null".equalsIgnoreCase(resMaPoolVO.getAzNods())){
            jsonArray = JSONUtil.parseArray(resMaPoolVO.getAzNods());
        }
        if(!jsonArray.isEmpty()){
            if (jsonArray.get(0) instanceof JSONArray) {
                JSONArray jsonArray1 = (JSONArray) jsonArray.get(0);
                // az的count需要和resource的count一致
                cn.hutool.json.JSONObject jsonObject = (cn.hutool.json.JSONObject) jsonArray1.get(0);
                jsonObject.put("count", newValue);
            } else {
                cn.hutool.json.JSONObject jsonObject = (cn.hutool.json.JSONObject) jsonArray.get(0);
                // az的count需要和resource的count一致
                jsonObject.put("count", newValue);
            }
        }

        resource.setAzs(jsonArray.toList(PoolNodeAz.class));
        poolSpec.setType(resMaPoolVO.getType());
        poolSpec.setScope(JSONUtil.parseArray(resMaPoolVO.getScope()).toList(String.class));
        poolSpec.setResources(Arrays.asList(resource));

        Network network = new Network();
        network.setName(resMaPoolVO.getNetworkName());
        poolSpec.setNetwork(network);
        poolSpec.setJobFlavors(Arrays.asList(resMaPoolVO.getJobFlavors()));

        //资源池的metadata信息
        PoolMetadata poolMetadata = new PoolMetadata();

        PoolMetaLabels poolMetaLabels = new PoolMetaLabels();
        poolMetaLabels.setName(resMaPoolVO.getName());

        poolMetadata.setLabels(poolMetaLabels);

        PoolMetaAnnotations poolMetaAnnotations = new PoolMetaAnnotations();
        poolMetaAnnotations.setMode("string");
        poolMetaAnnotations.setDescription(resMaPoolVO.getDescription());

        poolMetadata.setAnnotations(poolMetaAnnotations);

        maPoolsUpdate.setSpec(poolSpec);
        maPoolsUpdate.setMetadata(poolMetadata);

        Date now = new Date();
        String errMsg = "";
        try {
            Base clearPass = BaseClearPassUtil.clearPass(maPoolsUpdate);

            BaseResult result = (BaseResult) MQHelper.rpc(maPoolsUpdate);
            if (!result.isSuccess()) {
                errMsg = result.getErrMsg();
                throw new Exception();
            }
        } catch (Exception e) {
            resMaPoolVO.setProessPhase(MaPoolProessPhase.UPDATE_MA_POOL_ERROR);
            resMaPoolVO.setUpdatedDt(now);
            if (StringUtils.isNotBlank(errMsg)) {
                resMaPoolVO.setErrorInfo(errMsg);
            } else {
                resMaPoolVO.setErrorInfo("变更专属资源池节点失败，请联系运维管理员。");
            }
            maRemoteService.updateByPrimaryKeySelective(resMaPoolVO);
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1859358309));
        }

        log.info("AI专属资源池可用节点数量：[{}]", resMaPoolVO.getAvailableCount());
        // 修改流程节点信息
        resMaPoolVO.setProessPhase(MaPoolProessPhase.UPDATE_MA_POOL_PEDING);
        resMaPoolVO.setUpdatedDt(now);
        maRemoteService.updateByPrimaryKeySelective(resMaPoolVO);
    }

    private void billing(ServiceOrder serviceOrder, String name) {
        ServiceOrderDetail serviceOrderDetail = serviceOrder.getDetails().get(0);
        cn.hutool.json.JSONObject serviceConfig = JSONUtil.parseObj(serviceOrderDetail.getServiceConfig());
        BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectByPrimaryKey(
                serviceOrder.getBizBillingAccountId());
        List<InstanceGaapCost> costs = Lists.newArrayList();
        Criteria criteria = new Criteria();
        criteria.put("orderSn", serviceOrder.getOrderSn());
        List<ServiceOrderPriceDetail> priceDetails = serviceOrderPriceDetailMapper.selectByParams(criteria);
        priceDetails.forEach(priceDetail -> {
            InstanceGaapCost cost = serviceOrderService.getBaseCost(serviceOrder, priceDetail,
                                                                    serviceConfig.getStr("productResourceId"),
                                                                    serviceOrder.getName());
            cost.setInstanceId(name);
            cost.setResourceId(name);
            Long offsetDays =cn.hutool.core.date.DateUtil.betweenDay(priceDetail.getStartTime(), priceDetail.getEndTime(), false);
            cost.setUsageCount(offsetDays + "天");
            // 记录有效算力
            SfProductResourceCueExample sfProductResourceCueExample = new SfProductResourceCueExample();
            SfProductResourceCueExample.Criteria criteria1 = sfProductResourceCueExample.createCriteria();
            criteria1.andResourceIdEqualTo(Long.parseLong(serviceConfig.getStr("productResourceId")));
            List<SfProductResourceCue> sfProductResourceCues = sfProductResourceCueMapper.selectByExample(sfProductResourceCueExample);
            if (CollectionUtils.isNotEmpty(sfProductResourceCues)) {
                Long cueValue = CollectionUtil.getFirst(sfProductResourceCues).getCueValue();
                if (Objects.nonNull(cueValue)) {
                    cost.setCueValue(String.valueOf(offsetDays * cueValue * 24));
                }
            }

            BigDecimal amount = priceDetail.getAmount();
            if (Objects.nonNull(serviceOrder.getChargingType()) && SALE_TYPE.equalsIgnoreCase(serviceOrder.getChargingType())) {
                amount = BigDecimal.ZERO;
            }
            serviceOrderService.setUsedCost(bizBillingAccount, cost, BigDecimalUtil.remainTwoPointAmount(amount));
            bizBillingAccount.setBalance(
                    NumberUtil.sub(bizBillingAccount.getBalance(), cost.getCashAmount()));
            bizBillingAccount.setCreditLine(
                    NumberUtil.sub(bizBillingAccount.getCreditLine(), cost.getCreditAmount()));
            bizBillingAccount.setBalanceCash(
                    NumberUtil.sub(bizBillingAccount.getBalanceCash(), cost.getCouponAmount()));
            cost.setPretaxGrossAmount(priceDetail.getOriginalCost());
            //四舍五入
            cost.setEraseZeroAmount(BigDecimalUtil.getEraseZeroColumn(amount));
            cost.setPretaxAmount(BigDecimalUtil.remainTwoPointAmount(amount));
            cost.setPricingDiscount(serviceOrder.getOrgDiscount());
            cost.setBizBillingAccount(
                    BeanUtil.toBean(bizBillingAccount, BizBillingAccount.class));

            cost.setEntityId(serviceOrder.getEntityId());
            cost.setEntityName(serviceOrder.getEntityName());
            cost.setType(serviceOrder.getType());
            cost.setOrderSourceSn(serviceOrder.getOrderSourceSn());
            this.processDistributorName(cost, serviceOrder);
            costs.add(cost);
            priceDetail.setPayBalance(cost.getCashAmount());
            priceDetail.setPayBalanceCash(cost.getCouponAmount());
            priceDetail.setPayCreditLine(cost.getCreditAmount());
            serviceOrderPriceDetailMapper.updateByPrimaryKeySelective(priceDetail);
        });
        bizBillingAccountMapper.updateByPrimaryKeySelective(bizBillingAccount);
        //修改用户业务标识Tag
        updateUserBusinessTag(bizBillingAccount);
        serviceOrderService.insertCostAndCycleAndDeal(serviceOrder, costs);
    }

    /**
     * MA资源退订操作
     *
     * @param serviceOrder
     * @param detail
     */
    private ResMaPoolVO resMaHandleRelease(ServiceOrder serviceOrder, ServiceOrderDetail detail, ResMaPoolVO resMaPool) {
        Long clusterId = detail.getClusterId();
        // 获取共享资源池节点信息
        BaseResult baseResult = resMaPoolsService.getCommonPoolNode(serviceOrder, clusterId, OrderType.DELETEING);
        if (Objects.isNull(baseResult.isSuccess())) {
            baseResult.setSuccess(false);
        }
        if (!baseResult.isSuccess()) {
            Map messageContent = new HashMap();
            messageContent.put("applyUser", "运营管理员");
            messageContent.put("orderSn", serviceOrder.getOrderSn());
            messageContent.put("productName", serviceOrder.getProductName());
            messageContent.put("phase", "释放共享资源池");
            messageContent.put("errMsg", "节点状态异常，请咨询运维管理员");
            log.info("HPC专属资源池开通发送错误邮件,{}", messageContent);
            MailNotificationMq baseNotificationMqBean = new MailNotificationMq();
            baseNotificationMqBean.setMsgId(NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_AI_DRP_ERROR);
            baseNotificationMqBean.setMails(sysConfigRemoteService.getSendMailUrl());
            baseNotificationMqBean.setMap(messageContent);
            rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT, baseNotificationMqBean);
            if(OrderType.RELEASE.equals(serviceOrder.getType())){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1437048147));
            }
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2127464660));
        }
        //更新共享资源池
        Integer runCount = resMaPoolsService.getCommonRunCount(baseResult);
        Integer desiredNodeCount = runCount + resMaPool.getMaxCount();
        log.info("退订专属资源池归还的节点数,{},...{}", resMaPool.getMaxCount(), runCount);
        BaseResult maSharePoolsUpdateResult = resMaPoolsService.releaseCommonPoolNode(baseResult, serviceOrder,
                                                                                      clusterId, OrderType.DELETEING, desiredNodeCount, false);
        ResMaPoolVO insertMaDrpResource = new ResMaPoolVO();
        insertMaDrpResource.setId(clusterId);
        if (Objects.isNull(maSharePoolsUpdateResult.isSuccess())) {
            maSharePoolsUpdateResult.setSuccess(false);
        }
        if (!maSharePoolsUpdateResult.isSuccess()) {
            insertMaDrpResource.setStatus(OrderType.RELEASE);
            insertMaDrpResource.setProessPhase(MaPoolProessPhase.RELEASECOMMONPOOLNODEERROR);
            insertMaDrpResource.setErrorInfo("节点状态异常，请咨询运维管理员");
            maRemoteService.updateByPrimaryKeySelective(insertMaDrpResource);
            Map messageContent = new HashMap();
            messageContent.put("applyUser", "运营管理员");
            messageContent.put("orderSn", serviceOrder.getOrderSn());
            messageContent.put("productName", serviceOrder.getProductName());
            messageContent.put("phase", "释放共享资源池");
            messageContent.put("errMsg", "节点状态异常，请咨询运维管理员");
            log.info("HPC专属资源池开通发送错误邮件,{}", messageContent);
            MailNotificationMq baseNotificationMqBean = new MailNotificationMq();
            baseNotificationMqBean.setMsgId(NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_AI_DRP_ERROR);
            baseNotificationMqBean.setMails(sysConfigRemoteService.getSendMailUrl());
            baseNotificationMqBean.setMap(messageContent);
            rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT, baseNotificationMqBean);
            if(OrderType.RELEASE.equals(serviceOrder.getType())){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1437048147));
            }
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1225363455));
        }
        insertMaDrpResource.setStatus(OrderType.DELETEING);
        insertMaDrpResource.setProessPhase(MaPoolProessPhase.RELEASECOMMONPOOLNODE);
        maRemoteService.updateByPrimaryKeySelective(insertMaDrpResource);
        return insertMaDrpResource;
    }


    /**
     * 删除专属资源池
     *
     * @param task
     * @param processInstance
     * @param serviceOrder
     * @param clusterId
     * @param resMaPool
     */
    private String delMaDrp(Task task, String processInstance,
                            ServiceOrder serviceOrder,
                            Long clusterId,
                            ResMaPoolVO resMaPool, ServiceOrderDetail detail) {
        ResMaPoolVO insertMaDrpResource = new ResMaPoolVO();
        String poolType = resMaPool.getType();
        if (("运营管理员预审批".equals(task.getName()) || (StringUtils.isNotBlank(poolType) && MaBasicStatus.LOGICAL.equals(poolType)))
                && "MA专属资源池退订".equals(processInstance)) {

            //获取共享节点失败,更新共享资源池失败，删除成功的不需要走该流程
            AuthUser authUserInfo = AuthUserHolder.getAuthUser();
            log.info("MA资源退订的用户ID,{}", authUserInfo.getUserSid());
            //删除专属资源池
            MAPoolsDeleteResult maPoolsDeleteResult = new MAPoolsDeleteResult();
            MAPoolsDelete maPoolsDelete = CloudClientFactory.buildMQBean(cloudEnvId(), MAPoolsDelete.class);
            maPoolsDelete.setPoolName(resMaPool.getName());
            Base clearPass = BaseClearPassUtil.clearPass(maPoolsDelete);

            maPoolsDeleteResult = maRemoteService.maPoolsDelete(maPoolsDelete);
            log.info("删除专属资源池返回参数,{}", maPoolsDeleteResult.toString());
            insertMaDrpResource.setId(clusterId);
            insertMaDrpResource.setUpdatedDt(new Date());
            if (Objects.isNull(maPoolsDeleteResult.isSuccess())) {
                maPoolsDeleteResult.setSuccess(false);
            }
            if (!maPoolsDeleteResult.isSuccess()) {
                insertMaDrpResource.setProessPhase(MaPoolProessPhase.DELETEINGDRPPOOLERROR);
                insertMaDrpResource.setErrorInfo("删除专属资源池失败,请联系运维管理员查看资源池运行情况");
                log.info("删除专属资源池资源池节点信息,{}", insertMaDrpResource.getErrorInfo());
                maRemoteService.updateByPrimaryKeySelective(insertMaDrpResource);
                Map sendAddr = new HashMap();
                Map messageContent = new HashMap();
                messageContent.put("applyUser", "运营管理员");
                messageContent.put("orderSn", serviceOrder.getOrderSn());
                messageContent.put("productName", serviceOrder.getProductName());
                messageContent.put("phase", "删除专属资源池");
                messageContent.put("errMsg", "删除专属资源池失败,请联系运维管理员查看资源池运行情况");
                log.info("HPC专属资源池开通发送错误邮件,{}", messageContent);
                MailNotificationMq baseNotificationMqBean = new MailNotificationMq();
                baseNotificationMqBean.setMsgId(NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_AI_DRP_ERROR);
                baseNotificationMqBean.setMails(sysConfigRemoteService.getSendMailUrl());
                baseNotificationMqBean.setMap(messageContent);
                rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT, baseNotificationMqBean);

                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_931722621));
            }
            insertMaDrpResource.setProessPhase(MaPoolProessPhase.DELETEINGDRPPOOLPEDING);
            if (StringUtils.isNotBlank(poolType) && MaBasicStatus.LOGICAL.equals(poolType)) {
                insertMaDrpResource.setProessPhase(MaPoolProessPhase.DELETEINGDRPPOOL);
            }
            maRemoteService.updateByPrimaryKeySelective(insertMaDrpResource);
        }
        return insertMaDrpResource.getProessPhase();
    }



    private long monthCompare(Date fromDate, Date toDate) {
        Calendar c1 = Calendar.getInstance();
        Calendar c2 = Calendar.getInstance();
        c1.setTime(fromDate);
        c2.setTime(toDate);
        int year1 = c1.get(Calendar.YEAR);
        int year2 = c2.get(Calendar.YEAR);
        int month1 = c1.get(Calendar.MONTH);
        int month2 = c2.get(Calendar.MONTH);
        int day1 = c1.get(Calendar.DAY_OF_MONTH);
        int day2 = c2.get(Calendar.DAY_OF_MONTH);
        // 获取年的差值
        int yearInterval = year1 - year2;
        // 如果 d1的 月-日 小于 d2的 月-日 那么 yearInterval-- 这样就得到了相差的年数
        if (month1 < month2 || month1 == month2 && day1 < day2) {
            yearInterval--;
        }
        // 获取月数差值
        int monthInterval = (month1 + 12) - month2;
        if (day1 < day2) {
            monthInterval--;
        }
        monthInterval %= 12;
        int monthsDiff = Math.abs(yearInterval * 12 + monthInterval);
        return monthsDiff;
    }

    private Long getFDCloudEnv() {
        CloudEnvParams cloudEnvParams = new CloudEnvParams();
        cloudEnvParams.setCloudEnvType(
                cn.com.cloudstar.rightcloud.common.constants.res.type.CloudEnvType.FUSION_DIRECTOR.getValue().get(0));
        List<CloudEnv> cloudEnvs = cloudEnvRemoteService.selectByParams(cloudEnvParams);
        return Objects.nonNull(cloudEnvs) && Objects.nonNull(cloudEnvs.get(0)) ? cloudEnvs.get(0).getId() : 0;
    }

    private void validCost(ServiceOrder serviceOrder, ServiceOrderDetail detail) {
        // 校验余额
        if (OrderStatus.PENDING.equalsIgnoreCase(serviceOrder.getStatus())
                && serviceOrder.getFinalCost().compareTo(BigDecimal.ZERO) > 0) {
            Criteria criteria = new Criteria();
            criteria.put("adminSid", serviceOrder.getOwnerId());
            BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectByParams(criteria).get(0);
            //查询现金余额
            if (ChargeTypeEnum.PREPAID.getValue().equalsIgnoreCase(detail.getChargeType())) {
                // 若余额+信用额度+现金券余额 < 当前订单消费
                if (NumberUtil.add(BigDecimalUtil.getTwoPointAmount(bizBillingAccount.getBalance()),
                                   BigDecimalUtil.getTwoPointAmount(bizBillingAccount.getCreditLine()))
                              .add(BigDecimalUtil.getTwoPointAmount(bizBillingAccount.getBalanceCash()))
                              .compareTo(BigDecimalUtil.remainTwoPointAmount(serviceOrder.getFinalCost())) < 0) {
                    log.info("bizBillingAccount id : {}", bizBillingAccount.getId());

                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_881668963));
                }
            } else if (ChargeTypeEnum.POSTPAID.getValue().equalsIgnoreCase(detail.getChargeType())) {
                String configValue = cn.com.cloudstar.rightcloud.oss.util.PropertiesUtil.getProperty(
                        POST_PAID_CONFIG_KEY);
                BigDecimal amount = StrUtil.isBlank(configValue) ? BigDecimal.ZERO : new BigDecimal(configValue);
                if (NumberUtil.isLess(NumberUtil.add(BigDecimalUtil.getTwoPointAmount(bizBillingAccount.getBalance()),
                                                     BigDecimalUtil.getTwoPointAmount(bizBillingAccount.getCreditLine())),
                                      BigDecimalUtil.remainTwoPointAmount(amount))) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1601270136) + amount + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_580695533));
                }
            }
        }
    }

    /**
     * ecs审批完成业务处理
     *
     * @param serviceOrder serviceOrder
     */
    private void ecsBizProcess(ServiceOrder serviceOrder) {
        if (OrderType.APPLY.equals(serviceOrder.getType())) {
            List<SfProductResource> resources = sfProductResourceMapper.selectListByApplyOrderId(serviceOrder.getId());
            List<Long> vmIds = resources.stream().map(SfProductResource::getClusterId).collect(Collectors.toList());

            List<ResVdDto> vds = new ArrayList<>();
            ResVmInfoListRequest request = new ResVmInfoListRequest();
            request.setIdIn(vmIds);
            RestResult<List<ResVmInfoListResult>> vmInfoResult = resourceDcFeignService.getVmInfoByIds(request);
            if (Objects.nonNull(vmInfoResult) && vmInfoResult.getStatus() && Objects.nonNull(vmInfoResult.getData())) {
                List<ResVmInfoListResult> vmInfo = JSON.parseArray(JSON.toJSONString(vmInfoResult.getData()), ResVmInfoListResult.class);
                Map<Long, ResVmInfoListResult> collect = vmInfo.stream().collect(Collectors.toMap(ResVmInfoListResult::getId, Function.identity()));
                for (SfProductResource resource : resources) {
                    ResVmInfoListResult resVmInfoListResult = collect.get(resource.getClusterId());
                    if (Objects.nonNull(resVmInfoListResult)) {
                        resource.setInstanceUuid(resVmInfoListResult.getUuid());
                        vds.addAll(resVmInfoListResult.getVds());
                    }
                    sfProductResourceMapper.updateByPrimaryKeySelective(resource);
                }
            }
            List<ServiceOrderDetail> details = serviceOrder.getDetails();
            // 补扣时间
            Criteria criteria = new Criteria();
            criteria.put("orderSn", serviceOrder.getOrderSn());
            List<ServiceOrderPriceDetail> serviceOrderPriceDetails = serviceOrderPriceDetailMapper.selectByCriteria(criteria);
            Map<Long, ServiceOrderPriceDetail> detailMap = serviceOrderPriceDetails.stream()
                                                                                   .collect(
                                                                                           Collectors.toMap(ServiceOrderPriceDetail::getOrderDetailId,
                                                                                                            Function.identity()));
            List<ServiceOrderResourceRef> orderResourceRefs = new ArrayList<>();
            ArrayList<String> vdUuids = new ArrayList<>();
            details.stream().filter(e -> ProductCodeEnum.DISK.getProductCode().equalsIgnoreCase(e.getServiceType())).forEach(e -> {
                ArrayList<String> resourceIds = new ArrayList<>();
                ServiceOrderPriceDetail detail = detailMap.get(e.getId());
                String[] config = detail.getResourceConfigDesc().split("-");
                String storagePurpose = e.getProductConfigDesc().contains("系统盘") ? "sysDisk" : "dataDisk";
                for (Integer i = 0; i < detail.getQuantity(); i++) {
                    ResVdDto resVd = vds.stream()
                                        .filter(v -> !vdUuids.contains(v.getUuid()))
                                        .filter(vdType -> config[1].equals(vdType.getVdTypeName()) && StringUtils.equals(vdType.getSize().toString(),
                                                                                                                         config[2]))
                                        .filter(vd -> storagePurpose.equals(vd.getStoragePurpose())).findFirst().orElse(null);
                    if (Objects.nonNull(resVd)) {
                        vdUuids.add(resVd.getUuid());
                        //初始化resource
                        SfProductResource resource = BeanConvertUtil.convert(resources.get(0), SfProductResource.class);
                        resource.setId(null);
                        resource.setProductType(ProductCodeEnum.DISK.getProductCode());
                        resource.setProductName(ProductCodeEnum.toDesc(ProductCodeEnum.DISK.getProductCode()));
                        resource.setClusterId(resVd.getId());
                        resource.setClusterName(resVd.getName());
                        resource.setInstanceUuid(resVd.getUuid());
                        sfProductResourceMapper.insert(resource);
                        resourceIds.add(resource.getId().toString());
                        ServiceOrderResourceRef resourceRef = new ServiceOrderResourceRef();
                        resourceRef.setOrderDetailId(e.getId());
                        resourceRef.setType(e.getServiceType());
                        resourceRef.setResourceId(resource.getId().toString());
                        orderResourceRefs.add(resourceRef);
                        detail.setRefKey(StrUtil.concat(true, detail.getRefKey(), resource.getId().toString(), "-"));
                    }
                }
                detail.setRefInstanceId(com.alibaba.fastjson.JSONArray.toJSONString(resourceIds));

                cn.hutool.json.JSONObject serviceConfig = JSONUtil
                        .parseObj(e.getServiceConfig());
                serviceConfig.set("productResourceId", CollectionUtil.join(resourceIds, ","));
                e.setServiceConfig(JSONUtil.toJsonStr(serviceConfig));
                serviceOrderDetailMapper.updateByPrimaryKeySelective(e);
            });
            priceDetailMapper.updateBatchById(serviceOrderPriceDetails);
            resourceRefMapper.insertBatch(orderResourceRefs);

        }
    }

    /**
     * 补充资源uuid
     *
     * @param serviceOrder serviceOrder
     */
    private void addResourceUuid(ServiceOrder serviceOrder) {
        if (!OrderType.APPLY.equals(serviceOrder.getType())) {
            return;
        }
        List<SfProductResource> resources = sfProductResourceMapper.selectListByApplyOrderId(serviceOrder.getId());
        if (CollectionUtils.isEmpty(resources)) {
            log.warn("Resource uuid add error: not find resource");
            return;
        }
        SfProductResource resource = resources.get(0);
        if (ProductCodeEnum.ELB.getProductCode().equals(resource.getProductType())) {
            RestResult<ResLbResult> elbResult = resourceDcFeignService.getElbById(resource.getClusterId());
            if (Objects.nonNull(elbResult) && elbResult.getStatus() && Objects.nonNull(elbResult.getData())) {
                ResLbResult result = JSON.parseObject(JSON.toJSONString(elbResult.getData()), ResLbResult.class);
                resource.setInstanceUuid(result.getUuid());
            }
            sfProductResourceMapper.updateByPrimaryKeySelective(resource);
        }
        if (ProductCodeEnum.DCS.getProductCode().equals(resource.getProductType())) {
            RestResult<ResDcsDetailResultDto> dcsResult = resourceDcFeignService.getDcsById(resource.getClusterId());
            if (Objects.nonNull(dcsResult) && dcsResult.getStatus() && Objects.nonNull(dcsResult.getData())) {
                ResDcsDetailResultDto result = JSON.parseObject(JSON.toJSONString(dcsResult.getData()), ResDcsDetailResultDto.class);
                resource.setInstanceUuid(result.getUuid());
            }
            sfProductResourceMapper.updateByPrimaryKeySelective(resource);
        }
    }

    private void billEcs(ServiceOrder serviceOrder) {
        BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectByPrimaryKey(serviceOrder.getBizBillingAccountId());
        List<InstanceGaapCost> costs = Lists.newArrayList();
        serviceOrder.getDetails().stream()
                    .filter(e -> ChargeTypeEnum.PREPAID.getValue().equalsIgnoreCase(e.getChargeType()) && !NumberUtil.equals(e.getAmount(), BigDecimal.ZERO))
                    .forEach(detail -> {
            cn.hutool.json.JSONObject serviceConfig = JSONUtil.parseObj(detail.getServiceConfig());
            String productResourceIds = serviceConfig.getStr("productResourceId");
            List<Long> resourceIds = Arrays.stream(productResourceIds.split(",")).map(e -> Long.valueOf(e)).collect(Collectors.toList());
            SfProductResource resource = sfProductResourceMapper.selectByPrimaryKey(resourceIds.get(0));

            Criteria criteria = new Criteria();
            criteria.put("refInstanceIdLike", "\"" + resource.getId() + "\"");
            criteria.put("orderTypes", Lists.newArrayList("apply", "renew", "modify"));
            criteria.put("orderStatus", "completed");
            criteria.put("isValid", true);
            criteria.put("getOne", "ORDER BY A.id DESC LIMIT 1");
            List<ServiceOrderPriceDetail> lastPds = serviceOrderPriceDetailMapper.selectByCriteria(criteria);
            ServiceOrderPriceDetail pd = getPriceDetail("resource", detail.getAmount(), detail.getStartTime(), detail.getEndTime(), lastPds.get(0).getBillingSpec());

            InstanceGaapCost cost = serviceOrderService.getBaseCost(serviceOrder, pd, Convert.toStr(null), ProductCodeEnum.toDesc(pd.getProductCode()));
            BigDecimal amount = pd.getAmount();
            if (NumberUtil.isGreater(amount, BigDecimal.ZERO)) {
                cost.setEraseZeroAmount(BigDecimalUtil.getEraseZeroColumn(amount));
                serviceOrderService.setUsedCost(bizBillingAccount, cost, BigDecimalUtil.remainTwoPointAmount(amount));
                bizBillingAccount.setBalance(NumberUtil.sub(bizBillingAccount.getBalance(), BigDecimalUtil.getTwoPointAmount(cost.getCashAmount())));
                bizBillingAccount.setCreditLine(NumberUtil.sub(bizBillingAccount.getCreditLine(), BigDecimalUtil.getTwoPointAmount(cost.getCreditAmount())));
                bizBillingAccount.setBalanceCash(NumberUtil.sub(bizBillingAccount.getBalanceCash(),BigDecimalUtil.getTwoPointAmount(cost.getCouponAmount())));
            } else {
                cost.setCashAmount(amount);
                cost.setCreditAmount(BigDecimal.ZERO);
                cost.setCouponAmount(BigDecimal.ZERO);
                bizBillingAccount.setBalance(NumberUtil.sub(bizBillingAccount.getBalance(), BigDecimalUtil.getTwoPointAmount(cost.getCashAmount())));
            }
            cost.setProductCode(detail.getServiceType());
            String productName = ProductCodeEnum.toDesc(detail.getServiceType());
            log.info("所属产品Code：[{}]", detail.getServiceType());
            if (ProductCodeEnum.ECS.getProductCode().equals(detail.getServiceType())) {
                productName = serviceOrder.getProductName();
                log.info("HPC_DRP 所属产品名称：[{}]", productName);
            }
            boolean isRds= checkIsRdsByOrderDetailId(pd.getOrderDetailId());
            if(isRds){
                productName = ProductCodeEnum.toDesc(ProductCodeEnum.RDS.getProductCode());
                if(!ProductCodeEnum.RDS.getProductCode().equals(pd.getProductCode())){
                    productName="云硬盘 RDS";
                }
            }
            cost.setProductName(productName);
            cost.setPretaxGrossAmount(amount);
            cost.setConfiguration(pd.getBillingSpec());
            cost.setEraseZeroAmount(BigDecimalUtil.getEraseZeroColumn(amount));
            cost.setPretaxAmount(BigDecimalUtil.remainTwoPointAmount(amount));
            cost.setBizBillingAccount(BeanUtil.toBean(bizBillingAccount, BizBillingAccount.class));
            //如果实例名称为空，取申请单名称
            cost.setInstanceId(resource.getId().toString());
            cost.setInstanceName(serviceOrder.getName());
            cost.setInstanceName(resource.getClusterName());
            cost.setResourceId(resource.getInstanceUuid());
            setResourceInfo(resource, cost);
            // 记录资源用量
            cost.setUsageCount("--");
            cost.setEntityId(bizBillingAccount.getEntityId());
            cost.setEntityName(bizBillingAccount.getEntityName());
            cost.setType(serviceOrder.getType());
            cost.setOrderSourceSn(serviceOrder.getOrderSourceSn());
            costs.add(cost);
        });
        bizBillingAccountMapper.updateByPrimaryKeySelective(bizBillingAccount);
        //修改用户业务标识Tag
        this.updateUserBusinessTag(bizBillingAccount);
        serviceOrderService.insertCostAndCycleAndDeal(serviceOrder, costs);
        serviceOrder.setPayTime(new Date());
        serviceOrderService.updateByPrimaryKeySelective(serviceOrder);
    }

    /**
     *
     * 根据resourceId判断是否是rds资源
     * @param resourceId id
     * @return true
     */
    private boolean checkIsRdsByResourceId(String resourceId){
        //判断是否是rds
        Boolean isRds = false;
        if(ObjectUtil.isNotEmpty(resourceId)){
            final SfProductResource sfProductResource = sfProductResourceMapper.selectByPrimaryKey(Long.parseLong(resourceId));
            if(ObjectUtil.isNotEmpty(sfProductResource)&& ProductCodeEnum.RDS.getProductCode().equals(sfProductResource.getProductType())){
                isRds=true;
            }
        }
        return isRds;
    }

    /**
     *
     * 根据订单号判断是否是 rds
     * @param orderDetailId 订单号
     * @return true
     */
    private boolean checkIsRdsByOrderDetailId(Long orderDetailId){
        boolean isRds = false;
        if(ObjectUtil.isNotEmpty(orderDetailId)){
            final List<String> productCodeList = serviceOrderDetailMapper.selectProductCodeByOrderDetailId(orderDetailId);
            if(CollectionUtil.isNotEmpty(productCodeList)
                    &&productCodeList.contains(ProductCodeEnum.RDS.getProductCode())){
                isRds=true;
            }
        }
        return  isRds;
    }

    /**
     *
     * 根据订单号判断是否是 rds
     * @param orderId 订单号
     * @return true
     */
    private boolean checkIsRdsByOrderId(Long orderId){
        boolean isRds = false;
        if(ObjectUtil.isNotEmpty(orderId)){
            final List<String> productCodeList = serviceOrderDetailMapper.selectProductCodeByOrderId(orderId);
            if(CollectionUtil.isNotEmpty(productCodeList)
                    &&productCodeList.contains(ProductCodeEnum.RDS.getProductCode())){
                isRds=true;
            }
        }
        return  isRds;
    }
}
