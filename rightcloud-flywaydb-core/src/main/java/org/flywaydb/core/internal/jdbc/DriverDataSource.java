/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */


package org.flywaydb.core.internal.jdbc;

import org.flywaydb.core.api.FlywayException;
import org.flywaydb.core.api.logging.Log;
import org.flywaydb.core.api.logging.LogFactory;
import org.flywaydb.core.internal.util.ClassUtils;
import org.flywaydb.core.internal.util.FeatureDetector;
import org.flywaydb.core.internal.util.StringUtils;

import java.io.PrintWriter;
import java.sql.Connection;
import java.sql.Driver;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Properties;
import java.util.Set;
import java.util.logging.Logger;

import javax.sql.DataSource;

import cn.com.cloudstar.rightcloud.common.util.CrytoUtilSimple;

public class DriverDataSource implements DataSource {
    private static final Log LOG = LogFactory.getLog(org.flywaydb.core.internal.jdbc.DriverDataSource.class);
    private static final String DB2_JDBC_URL_PREFIX = "jdbc:db2:";
    private static final String DERBY_CLIENT_JDBC_URL_PREFIX = "jdbc:derby://";
    private static final String DERBY_EMBEDDED_JDBC_URL_PREFIX = "jdbc:derby:";
    private static final String MARIADB_JDBC_DRIVER = "org.mariadb.jdbc.Driver";
    private static final String MARIADB_JDBC_URL_PREFIX = "jdbc:mariadb:";
    private static final String MYSQL_JDBC_DRIVER = "org.mariadb.jdbc.Driver";
    private static final String MYSQL_LEGACY_JDBC_DRIVER = "com.mysql.jdbc.Driver";
    private static final String MYSQL_JDBC_URL_PREFIX = "jdbc:mysql:";
    private static final String ORACLE_JDBC_URL_PREFIX = "jdbc:oracle:";
    private static final String POSTGRESQL_JDBC_URL_PREFIX = "jdbc:postgresql:";
    private static final String REDSHIFT_JDBC_URL_PREFIX = "jdbc:redshift:";
    private static final String REDSHIFT_JDBC41_DRIVER = "com.amazon.redshift.jdbc41.Driver";
    private static final String SAPHANA_JDBC_URL_PREFIX = "jdbc:sap:";
    private static final String SQLDROID_DRIVER = "org.sqldroid.SQLDroidDriver";
    private static final String SQLSERVER_JDBC_URL_PREFIX = "jdbc:sqlserver:";
    private static final String SYBASE_JDBC_URL_PREFIX = "jdbc:sybase:";
    private static final String APPLICATION_NAME = "Flyway by Boxfuse";
    private static final String SQLITE_JDBC_URL_PREFIX = "jdbc:sqlite:";
    private Driver driver;
    private final String url;
    private final String user;
    private final String password;
    private final Properties defaultProps;
    private final ClassLoader classLoader;
    private boolean autoCommit;

    private static final Map<String, String> JDBC_DRIVER_MAP = new HashMap<>();

    static {
        JDBC_DRIVER_MAP.put("jdbc:tc:", "org.testcontainers.jdbc.ContainerDatabaseDriver");
        JDBC_DRIVER_MAP.put("jdbc:db2:", "com.ibm.db2.jcc.DB2Driver");
        JDBC_DRIVER_MAP.put("jdbc:derby://", "org.apache.derby.jdbc.ClientDriver");
        JDBC_DRIVER_MAP.put("jdbc:derby:", "org.apache.derby.jdbc.EmbeddedDriver");
        JDBC_DRIVER_MAP.put("jdbc:h2:", "org.h2.Driver");
        JDBC_DRIVER_MAP.put("jdbc:hsqldb:", "org.hsqldb.jdbcDriver");
        JDBC_DRIVER_MAP.put("jdbc:sqlite:", "org.sqlite.JDBC"); // 根据FeatureDetector的结果动态决定
        JDBC_DRIVER_MAP.put("jdbc:sqldroid:", "org.sqldroid.SQLDroidDriver");
        JDBC_DRIVER_MAP.put("jdbc:mysql:", "org.mariadb.jdbc.Driver");
        JDBC_DRIVER_MAP.put("jdbc:mariadb:", "org.mariadb.jdbc.Driver");
        JDBC_DRIVER_MAP.put("jdbc:google:", "com.mysql.jdbc.GoogleDriver");
        JDBC_DRIVER_MAP.put("jdbc:oracle:", "oracle.jdbc.OracleDriver");
        JDBC_DRIVER_MAP.put("jdbc:postgresql:", "org.postgresql.Driver");
        JDBC_DRIVER_MAP.put("jdbc:redshift:", "com.amazon.redshift.jdbc42.Driver");
        JDBC_DRIVER_MAP.put("jdbc:jtds:", "net.sourceforge.jtds.jdbc.Driver");
        JDBC_DRIVER_MAP.put("jdbc:sybase:", "com.sybase.jdbc4.jdbc.SybDriver");
        JDBC_DRIVER_MAP.put("jdbc:sqlserver:", "com.microsoft.sqlserver.jdbc.SQLServerDriver");
        JDBC_DRIVER_MAP.put("jdbc:sap:", "com.sap.db.jdbc.Driver");
        JDBC_DRIVER_MAP.put("jdbc:informix-sqli:", "com.informix.jdbc.IfxDriver");
    }


    public DriverDataSource(ClassLoader classLoader, String driverClass, String url, String user, String password) throws FlywayException {
        this(classLoader, driverClass, url, user, password, new Properties());
    }

    public DriverDataSource(ClassLoader classLoader, String driverClass, String url, String user, String password, Properties props) throws FlywayException {
        this.autoCommit = true;
        this.classLoader = classLoader;
        this.url = this.detectFallbackUrl(url);
        if (!StringUtils.hasLength(driverClass)) {
            driverClass = this.detectDriverForUrl(url);
            if (!StringUtils.hasLength(driverClass)) {
                throw new FlywayException("Unable to autodetect JDBC driver for url: " + url);
            }
        }

        this.defaultProps = new Properties(props);
        this.defaultProps.putAll(this.detectPropsForUrl(url));

        try {
            this.driver = (Driver)ClassUtils.instantiate(driverClass, classLoader);
        } catch (FlywayException var11) {
            String backupDriverClass = this.detectBackupDriverForUrl(url);
            if (backupDriverClass == null) {
                throw new FlywayException("Unable to instantiate JDBC driver: " + driverClass + " => Check whether the jar file is present", var11);
            }

            try {
                this.driver = (Driver)ClassUtils.instantiate(backupDriverClass, classLoader);
            } catch (Exception var10) {
                throw new FlywayException("Unable to instantiate JDBC driver: " + driverClass + " => Check whether the jar file is present", var11);
            }
        }

        this.user = this.detectFallbackUser(user);
        this.password = this.detectFallbackPassword(password);
    }

    private String detectFallbackUrl(String url) {
        if (!StringUtils.hasText(url)) {
            String boxfuseDatabaseUrl = System.getenv("BOXFUSE_DATABASE_URL");
            if (StringUtils.hasText(boxfuseDatabaseUrl)) {
                return boxfuseDatabaseUrl;
            } else {
                throw new FlywayException("Missing required JDBC URL. Unable to create DataSource!");
            }
        } else if (!url.toLowerCase().startsWith("jdbc:")) {
            throw new FlywayException("Invalid JDBC URL (should start with jdbc:) : " + url);
        } else {
            return url;
        }
    }

    private String detectFallbackUser(String user) {
        if (!StringUtils.hasText(user)) {
            String boxfuseDatabaseUser = System.getenv("BOXFUSE_DATABASE_USER");
            if (StringUtils.hasText(boxfuseDatabaseUser)) {
                return boxfuseDatabaseUser;
            }
        }

        return user;
    }

    private String detectFallbackPassword(String password) {
        if (!StringUtils.hasText(password)) {
            String boxfuseDatabasePassword = System.getenv("BOXFUSE_DATABASE_PASSWORD");
            if (StringUtils.hasText(boxfuseDatabasePassword)) {
                return boxfuseDatabasePassword;
            }
        }

        return password;
    }

    private Properties detectPropsForUrl(String url) {
        Properties result = new Properties();
        if (url.startsWith("jdbc:oracle:")) {
            String osUser = System.getProperty("user.name");
            result.put("v$session.osuser", osUser.substring(0, Math.min(osUser.length(), 30)));
            result.put("v$session.program", "Flyway by Boxfuse");
            result.put("oracle.net.keepAlive", "true");
        } else if (url.startsWith("jdbc:sqlserver:")) {
            result.put("applicationName", "Flyway by Boxfuse");
        } else if (url.startsWith("jdbc:postgresql:")) {
            result.put("ApplicationName", "Flyway by Boxfuse");
        } else if (!url.startsWith("jdbc:mysql:") && !url.startsWith("jdbc:mariadb:")) {
            if (url.startsWith("jdbc:db2:")) {
                result.put("clientProgramName", "Flyway by Boxfuse");
                result.put("retrieveMessagesFromServerOnGetMessage", "true");
            } else if (url.startsWith("jdbc:sybase:")) {
                result.put("APPLICATIONNAME", "Flyway by Boxfuse");
            } else if (url.startsWith("jdbc:sap:")) {
                result.put("SESSIONVARIABLE:APPLICATION", "Flyway by Boxfuse");
            }
        } else {
            result.put("connectionAttributes", "program_name:Flyway by Boxfuse");
        }

        return result;
    }

    private String detectBackupDriverForUrl(String url) {
        if (url.startsWith("jdbc:mysql:") && ClassUtils.isPresent("com.mysql.jdbc.Driver", this.classLoader)) {
            return "com.mysql.jdbc.Driver";
        } else if (url.startsWith("jdbc:mysql:") && ClassUtils.isPresent("org.mariadb.jdbc.Driver", this.classLoader)) {
            LOG.warn("You are attempting to connect to a MySQL database using the MariaDB driver. This is known to cause issues. An upgrade to Oracle's MySQL JDBC driver is highly recommended.");
            return "org.mariadb.jdbc.Driver";
        } else if (url.startsWith("jdbc:redshift:")) {
            return ClassUtils.isPresent("com.amazon.redshift.jdbc41.Driver", this.classLoader) ? "com.amazon.redshift.jdbc41.Driver" : "com.amazon.redshift.jdbc4.Driver";
        } else {
            return null;
        }
    }

    private String detectDriverForUrl(String url) {
        Set<Entry<String, String>> entries = JDBC_DRIVER_MAP.entrySet();
        if (url.startsWith(SQLITE_JDBC_URL_PREFIX)) {
            return (new FeatureDetector(this.classLoader)).isAndroidAvailable() ? SQLDROID_DRIVER : "org.sqlite.JDBC";
        }
        for (Entry<String, String> entry : entries) {
            if (url.startsWith(entry.getKey())) {
                return  entry.getValue();
            }
        }
        return null;
    }



    public Driver getDriver() {
        return this.driver;
    }

    public String getUrl() {
        return this.url;
    }

    public String getUser() {
        return this.user;
    }
    //自定义前缀
    private String prefix = "RC_EN(";
    //自定义后缀
    private String suffix = ")";
    public String getPassword() {
        if(isEncrypted(this.password)){
            return  decrypt(this.password);
        }
        return this.password;
    }
    public String decrypt(String decryptMessage) {

        int prefixIndex = decryptMessage.indexOf(prefix);
        int suffixIndex = decryptMessage.indexOf(suffix);
        //还原密文
        decryptMessage = decryptMessage.substring(prefixIndex+prefix.length(),suffixIndex);
        //还原密码。注意如果需要密钥的这里添加
        return CrytoUtilSimple.decrypt(decryptMessage).trim();
    }
    public boolean isEncrypted(String message) {
        if (!StringUtils.hasLength(message)) {
            return false;
        } else {
            String trimmedValue = message.trim();
            return trimmedValue.startsWith(this.prefix) && trimmedValue.endsWith(this.suffix);
        }
    }
    public Connection getConnection() {
        return this.getConnectionFromDriver(this.getUser(), this.getPassword());
    }

    public Connection getConnection(String username, String password) {
        return this.getConnectionFromDriver(username, password);
    }

    protected Connection getConnectionFromDriver(String username, String password) {
        Properties props = new Properties(this.defaultProps);
        if (username != null) {
            props.setProperty("user", username);
        }

        if (password != null) {
            props.setProperty("password", password);
        }

        Connection connection = null;
        try {
            connection = this.driver.connect(this.url, props);
        } catch (SQLException e) {
            LOG.error("这里发生了SQLException:DriverDataSource:273");
        }
        if (connection == null) {
            throw new FlywayException("Unable to connect to " + this.url);
        } else {
            try {
                connection.setAutoCommit(this.autoCommit);
            } catch (SQLException e) {
                LOG.error("这里发生了SQLException:DriverDataSource:281");
            }
            return connection;
        }
    }

    public boolean isAutoCommit() {
        return this.autoCommit;
    }

    public void setAutoCommit(boolean autoCommit) {
        this.autoCommit = autoCommit;
    }

    public int getLoginTimeout() {
        return 0;
    }

    public void setLoginTimeout(int timeout) {
        throw new UnsupportedOperationException("setLoginTimeout");
    }

    public PrintWriter getLogWriter() {
        throw new UnsupportedOperationException("getLogWriter");
    }

    public void setLogWriter(PrintWriter pw) {
        throw new UnsupportedOperationException("setLogWriter");
    }

    public <T> T unwrap(Class<T> iface) {
        throw new UnsupportedOperationException("unwrap");
    }

    public boolean isWrapperFor(Class<?> iface) {
        return DataSource.class.equals(iface);
    }

    public Logger getParentLogger() {
        throw new UnsupportedOperationException("getParentLogger");
    }

    public void shutdownDatabase() {
        if (this.url.startsWith("jdbc:derby:") && !this.url.startsWith("jdbc:derby://")) {
            try {
                int i = this.url.indexOf(';');
                String shutdownUrl = (i < 0 ? this.url : this.url.substring(0, i)) + ";shutdown=true";
                this.driver.connect(shutdownUrl, new Properties());
            } catch (SQLException var3) {
                LOG.debug("Expected error on Derby Embedded Database shutdown SQLException:DriverDataSource:321");
            }
        }

    }
}
