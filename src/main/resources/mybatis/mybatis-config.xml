<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2018 Cloud-Star, Inc. All Rights Reserved.
  -->

<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
    <settings>
        <!-- 将数据库字段命名规则A_COLUMN转换为Java使用的驼峰式命名规则aCloumn -->
        <setting name="mapUnderscoreToCamelCase" value="true"/>
        <setting name="cacheEnabled" value="true"/>
        <setting name="logImpl" value="NO_LOGGING" />
    </settings>

    <!-- 插件配置  -->
    <plugins>
        <!-- com.github.pagehelper为PageHelper类所在包名 -->
        <plugin interceptor="com.github.pagehelper.PageInterceptor">
            <property name="supportMethodsArguments" value="true"/>
            <property name="params" value="pageNum=pageNum;pageSize=pageSize;"/>
            <property name="helperDialect" value="mysql"/>
        </plugin>
        <!--  数据权限控制，如有需要，可放开，并引入rightcloud-access模块   -->
        <plugin interceptor="cn.com.cloudstar.rightcloud.module.support.access.mybatis.interceptor.DataScopeInterceptor"/>

        <!-- 数据加密/解密拦截器, 如有需要可放开，并引入rightcloud-data-encrypt模块 -->
        <plugin interceptor="cn.com.cloudstar.rightcloud.common.encryptdata.interceptor.ParameterEncryptInterceptor"/>
        <plugin interceptor="cn.com.cloudstar.rightcloud.common.encryptdata.interceptor.ResultDecryptInterceptor"/>
    </plugins>

</configuration>
