<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.cloudstar.rightcloud.module.standard.dao.BigScreenMapper">

   <resultMap id="cluster_resource_used_total" type="cn.com.cloudstar.rightcloud.module.standard.pojo.entity.ClusterResourceUsedTotalRecord">
       <result column="cluster_name" property="clusterName" jdbcType="VARCHAR"/>
       <result column="cpu_core_ms" property="cpuCoreMs" jdbcType="DECIMAL"/>
       <result column="gpu_card_ms" property="gpuCardMs" jdbcType="DECIMAL"/>
       <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
   </resultMap>

    <resultMap id="sample_all_job_state_count" type="cn.com.cloudstar.rightcloud.module.standard.pojo.entity.SampleAllJobStateCountRecord">
        <result column="job_state" property="jobState" jdbcType="VARCHAR"/>
        <result column="job_count" property="jobCount" jdbcType="BIGINT"/>
        <result column="sample_time" property="sampleTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="sample_cluster_info" type="cn.com.cloudstar.rightcloud.module.standard.pojo.entity.SampleClusterInfo">
        <result column="cluster_name" property="clusterName" jdbcType="VARCHAR"/>
        <result column="node_num" property="nodeNum" jdbcType="BIGINT"/>
        <result column="sample_time" property="sampleTime" jdbcType="TIMESTAMP"/>
        <result column="cpu_used" property="cpuUsed" jdbcType="BIGINT"/>
        <result column="cpu_total" property="cpuTotal" jdbcType="BIGINT"/>
        <result column="cpu_util" property="cpuUtil" jdbcType="DECIMAL"/>
        <result column="gpu_used" property="gpuUsed" jdbcType="BIGINT"/>
        <result column="gpu_total" property="gpuTotal" jdbcType="BIGINT"/>
    </resultMap>
    <resultMap id="sample_cluster_info_after" type="cn.com.cloudstar.rightcloud.module.standard.pojo.entity.SampleClusterInfo">
        <result column="cluster_name" property="clusterName" jdbcType="VARCHAR"/>
        <result column="node_num" property="nodeNum" jdbcType="BIGINT"/>
        <result column="sample_time" property="sampleTime" jdbcType="TIMESTAMP"/>
        <result column="cpu_used" property="cpuUsed" jdbcType="BIGINT"/>
        <result column="cpu_total" property="cpuTotal" jdbcType="BIGINT"/>
        <result column="cpu_util" property="cpuUtil" jdbcType="DECIMAL"/>
        <result column="gpu_used" property="gpuUsed" jdbcType="BIGINT"/>
        <result column="gpu_total" property="gpuTotal" jdbcType="BIGINT"/>
        <result column="resource_pool" property="resourcePool" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="sample_node_info" type="cn.com.cloudstar.rightcloud.module.standard.pojo.entity.SampleNodeInfoRecord">
        <result column="cluster_name" property="clusterName" jdbcType="VARCHAR"/>
        <result column="node_name" property="nodeName" jdbcType="VARCHAR"/>
        <result column="sample_time" property="sampleTime" jdbcType="TIMESTAMP"/>
        <result column="cpu_used" property="cpuUsed" jdbcType="BIGINT"/>
        <result column="cpu_total" property="cpuTotal" jdbcType="BIGINT"/>
        <result column="cpu_util" property="cpuUtil" jdbcType="DECIMAL"/>
        <result column="cpu_core_util" property="cpuCoreUtil" jdbcType="DECIMAL"/>
        <result column="gpu_used" property="gpuUsed" jdbcType="BIGINT"/>
        <result column="gpu_total" property="gpuTotal" jdbcType="BIGINT"/>
        <result column="gpu_card_util" property="gpuCardUtil" jdbcType="DECIMAL"/>
    </resultMap>

    <resultMap id="sample_node_info_after" type="cn.com.cloudstar.rightcloud.module.standard.pojo.entity.SampleNodeInfoRecord">
        <result column="cluster_name" property="clusterName" jdbcType="VARCHAR"/>
        <result column="node_name" property="nodeName" jdbcType="VARCHAR"/>
        <result column="sample_time" property="sampleTime" jdbcType="TIMESTAMP"/>
        <result column="cpu_used" property="cpuUsed" jdbcType="BIGINT"/>
        <result column="cpu_total" property="cpuTotal" jdbcType="BIGINT"/>
        <result column="cpu_util" property="cpuUtil" jdbcType="DECIMAL"/>
        <result column="cpu_core_util" property="cpuCoreUtil" jdbcType="DECIMAL"/>
        <result column="gpu_used" property="gpuUsed" jdbcType="BIGINT"/>
        <result column="gpu_total" property="gpuTotal" jdbcType="BIGINT"/>
        <result column="gpu_card_util" property="gpuCardUtil" jdbcType="DECIMAL"/>
        <result column="resource_pool" property="resourcePool" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="tenant_resource_used_total" type="cn.com.cloudstar.rightcloud.module.standard.pojo.entity.TenantResourceUsedTotalRecord">
        <result column="tenant_name" property="tenantName" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="cpu_core_ms" property="cpuCoreMs" jdbcType="BIGINT"/>
        <result column="gpu_card_ms" property="gpuCardMs" jdbcType="BIGINT"/>
    </resultMap>

    <resultMap id="job_resource_used_today" type="cn.com.cloudstar.rightcloud.module.standard.pojo.entity.JobResourceUsedTodayRecord">
        <result column="job_id" property="jobId" jdbcType="VARCHAR"/>
        <result column="job_name" property="jobName" jdbcType="VARCHAR"/>
        <result column="user_name" property="userName" jdbcType="VARCHAR"/>
        <result column="tenant_name" property="tenantName" jdbcType="VARCHAR"/>
        <result column="cluster_name" property="clusterName" jdbcType="VARCHAR"/>
        <result column="wall_time" property="wallTime" jdbcType="BIGINT"/>
        <result column="cpu_core_ms" property="cpuCoreMs" jdbcType="BIGINT"/>
        <result column="gpu_card_ms" property="gpuCardMs" jdbcType="BIGINT"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="sample_tenant_info" type="cn.com.cloudstar.rightcloud.module.standard.pojo.entity.SampleTenantInfo">
        <result column="sample_time" property="sampleTime" jdbcType="TIMESTAMP"/>
        <result column="tenant_name" property="tenantName" jdbcType="VARCHAR"/>
        <result column="cluster_name" property="clusterName" jdbcType="VARCHAR"/>
        <result column="cpu_used" property="cpuUsed" jdbcType="BIGINT"/>
        <result column="gpu_used" property="gpuUsed" jdbcType="BIGINT"/>
    </resultMap>
    <resultMap id="map_tenant" type="cn.com.cloudstar.rightcloud.module.standard.pojo.entity.Tenant">
        <result column="org_name" property="orgName" jdbcType="VARCHAR"/>
        <result column="ldap_ou" property="ldapOu"  jdbcType="VARCHAR"/>
        <result column="org_sid" property="orgSid"  jdbcType="BIGINT"/>
    </resultMap>
    <resultMap id="sample_task_info" type="cn.com.cloudstar.rightcloud.module.standard.pojo.entity.SampleTaskInfo">
        <result column="sample_time" property="sampleTime" jdbcType="TIMESTAMP"/>
        <result column="task_state" property="taskState" jdbcType="VARCHAR"/>
        <result column="submit_time" property="submintTime" jdbcType="TIMESTAMP"/>
        <result column="task_start_time" property="taskStartTime" jdbcType="TIMESTAMP"/>
        <result column="task_end_time" property="taskEndTime" jdbcType="TIMESTAMP"/>
        <result column="exec_node" property="execNode" jdbcType="VARCHAR"/>
        <result column="resource_pool" property="resourcePool" jdbcType="VARCHAR"/>
        <result column="cluster_name" property="clusterName" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="selectClusterResourceUsedTotal" resultMap="cluster_resource_used_total">
    select cluster_name,cpu_core_ms,gpu_card_ms,update_time
    from  hpc.view_hpcop_cluster_resource_used_total
    </select>

    
    <select id="selectSampleAllJobStateCount"  resultMap="sample_all_job_state_count">
        SELECT job_state,job_count,sample_time
        FROM hpc.view_hpcop_sample_all_job_state_count
        where sample_time =  (select max(sample_time) from hpc.view_hpcop_sample_all_job_state_count);
    </select>


    <select id="selectSampleClusterInfo" resultMap="sample_cluster_info">
 SELECT cluster_name,node_num,sample_time,cpu_used,cpu_total,cpu_util,gpu_used,gpu_total FROM  hpc.view_hpcop_sample_cluster_info  e
 WHERE NOT EXISTS(SELECT 1 FROM hpc.view_hpcop_sample_cluster_info f WHERE f.sample_time > e.sample_time  AND  e.cluster_name = f.cluster_name);
    </select>

    <select id="selectSampleClusterInfoAfter" resultMap="sample_cluster_info_after">
 SELECT cluster_name,node_num,sample_time,cpu_used,cpu_total,cpu_util,gpu_used,gpu_total,resource_pool  FROM  hpc.view_hpcop_sample_cluster_info  e
 WHERE NOT EXISTS(SELECT 1 FROM hpc.view_hpcop_sample_cluster_info f WHERE f.sample_time > e.sample_time  AND  e.cluster_name = f.cluster_name);
    </select>


    <select id="selectSampleNodeInfo" resultMap="sample_node_info">
        SELECT sample_time,node_name,cluster_name,cpu_used,cpu_total,cpu_util,cpu_core_util,gpu_used,gpu_total,gpu_card_util
        FROM hpc.view_hpcop_sample_node_info
        where sample_time =  (select max(sample_time) from hpc.view_hpcop_sample_node_info);
    </select>

    <select id="selectSampleNodeInfoAfter" resultMap="sample_node_info_after">
        SELECT sample_time,node_name,cluster_name,cpu_used,cpu_total,cpu_util,cpu_core_util,gpu_used,gpu_total,gpu_card_util,resource_pool
        FROM hpc.view_hpcop_sample_node_info
        where sample_time =  (select max(sample_time) from hpc.view_hpcop_sample_node_info);
    </select>

    <select id="selectTenantResourceUsedTotal" resultMap="tenant_resource_used_total">
       select  update_time,tenant_name,cpu_core_ms,gpu_card_ms
       from hpc.view_hpcop_tenant_resource_used_total
    </select>

    <select id="selectJobResourceUsedToday" resultMap="job_resource_used_today">
       select job_id,update_time,job_name,user_name,tenant_name,cluster_name,wall_time,cpu_core_ms,gpu_card_ms
       from hpc.view_hpcop_job_resource_used_today
    </select>

    <select id="select1" resultType="java.lang.String">
        select CONFIG_VALUE FROM sys_m_config where  CONFIG_KEY='first_hpc_name'
    </select>
    <select id="select2" resultType="java.lang.Double">
       select CONFIG_VALUE FROM sys_m_config where  CONFIG_KEY='cpu_weight'
    </select>
    <select id="select3" resultType="java.lang.Double">
        select CONFIG_VALUE FROM sys_m_config where  CONFIG_KEY='gpu_weight'
    </select>
    <select id="selectSampleTenantInfos"  resultMap="sample_node_info">
       SELECT sample_time,node_name,cluster_name,cpu_used,cpu_total,cpu_util,cpu_core_util,gpu_used,gpu_total,gpu_card_util
        FROM hpc.view_hpcop_sample_node_info
        WHERE sample_time  &lt; #{beginTime} and  sample_time &gt; #{endTime}
    </select>
    <select id="all"  resultMap="sample_tenant_info">
        select sample_time,tenant_name,cpu_used,cluster_name,gpu_used
        from hpc.view_hpcop_sample_tenant_info
    </select>
    <select id="selectTenant" resultMap="map_tenant" parameterType="list">
        SELECT  org_sid, org_name,ldap_ou FROM  sys_m_org  WHERE  ldap_ou in
        <foreach collection="stringList"  item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="selectTenantAll" resultMap="map_tenant" >
        SELECT  org_sid, org_name,ldap_ou FROM  sys_m_org  WHERE  ldap_ou is not null
    </select>
    <select id="selectSampleTaskInfo" resultMap="sample_task_info">
        SELECT
            sample_time,
            task_state,
            submit_time,
            task_start_time,
            task_end_time,
            exec_node,
            resource_pool,
            cluster_name
        FROM
            hpc.view_hpcop_sample_task_info
        <where>
            <if test="lastSampleTime != null">
                and sample_time > #{lastSampleTime}
            </if>
        </where>
    </select>
</mapper>