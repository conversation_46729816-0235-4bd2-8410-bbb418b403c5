<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.cloudstar.rightcloud.module.standard.dao.BigScreenHourMapper">
    <resultMap id="sample_tenant_info" type="cn.com.cloudstar.rightcloud.module.standard.pojo.entity.SampleTenantInfo">
        <result column="sample_time" property="sampleTime" jdbcType="TIMESTAMP"/>
        <result column="tenant_name" property="tenantName" jdbcType="VARCHAR"/>
        <result column="cluster_name" property="clusterName" jdbcType="VARCHAR"/>
        <result column="cpu_used" property="cpuUsed" jdbcType="BIGINT"/>
        <result column="gpu_used" property="gpuUsed" jdbcType="BIGINT"/>
    </resultMap>
    <resultMap id="sample_tenant_info1" type="cn.com.cloudstar.rightcloud.module.standard.pojo.entity.SampleTenantInfoRecord">
        <result column="cluster_name" property="clusterName" jdbcType="VARCHAR"/>
        <result column="node_name" property="nodeName" jdbcType="VARCHAR"/>
        <result column="sample_time" property="sampleTime" jdbcType="TIMESTAMP"/>
        <result column="cpu_used" property="cpuUsed" jdbcType="BIGINT"/>
        <result column="cpu_total" property="cpuTotal" jdbcType="BIGINT"/>
        <result column="cpu_util" property="cpuUtil" jdbcType="DECIMAL"/>
        <result column="cpu_core_util" property="cpuCoreUtil" jdbcType="DECIMAL"/>
        <result column="gpu_used" property="gpuUsed" jdbcType="BIGINT"/>
        <result column="gpu_total" property="gpuTotal" jdbcType="BIGINT"/>
        <result column="gpu_card_util" property="gpuCardUtil" jdbcType="DECIMAL"/>
    </resultMap>
    <resultMap id="tenant_resource_used_month" type="cn.com.cloudstar.rightcloud.module.standard.pojo.entity.TenantResourceUsedMonthRecord">
        <result column="tenant_name" property="tenantName" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="cpu_core_ms" property="cpuCoreMs" jdbcType="BIGINT"/>
        <result column="gpu_card_ms" property="gpuCardMs" jdbcType="BIGINT"/>
        <result column="job_count" property="jobCount" jdbcType="BIGINT"/>
    </resultMap>
    <select id="selectSampleTenantInfo" resultMap="sample_tenant_info">
        select sample_time,tenant_name,cpu_used,cluster_name,gpu_used
       from hpc.view_hpcop_sample_tenant_info   WHERE  sample_time  &gt;=  #{time}
    </select>
    <select id="selectAll" resultMap="sample_tenant_info">
        select sample_time,tenant_name,cpu_used,cluster_name,gpu_used
       from hpc.view_hpcop_sample_tenant_info
    </select>
    <select id="selectSampleTenantInfo1" resultMap="sample_tenant_info1">
         SELECT sample_time,node_name,cluster_name,cpu_used,cpu_total,cpu_util,cpu_core_util,gpu_used,gpu_total,gpu_card_util
        FROM hpc.view_hpcop_sample_node_info
    </select>
    <select id="selectTenantResourceUsedMonth" resultMap="tenant_resource_used_month">
        select  update_time,tenant_name,cpu_core_ms,gpu_card_ms,job_count
       from hpc.view_hpcop_tenant_resource_used_month
    </select>
    <select id="selectSampleTenantInfos" resultMap="sample_tenant_info">
        select sample_time,tenant_name,cpu_used,cluster_name,gpu_used
        from hpc.view_hpcop_sample_tenant_info
        WHERE sample_time  &lt; #{beginTime} and  sample_time &gt; #{endTime}
    </select>
</mapper>