spring:
  profiles: cloudstar
base-name: ${cloudstar.server.name:rightcloud-csdr-collector}
base-port: ${cloudstar.server.port:38085}
base-mgr-port: ${cloudstar.server.mgr.port:8081}
base-dbAddress: ${cloudstar.db.address:127.0.0.1}
base-dbPort: ${cloudstar.db.port:3306}
base-dbName: ${cloudstar.db.name:rightcloud}
base-dbUserName: ${cloudstar.db.username:ENC@[PrFg0jg07iY33UyuBmQaIQ==]}
base-dbPassword: ${bss.db.password}
base-dbSsl: ${cloudstar.db.ssl:verify-ca}
base-dbUrl: jdbc:mariadb://${base-dbAddress}:${base-dbPort}/${base-dbName}?sslMode=${base-dbSsl}&allowLocalInfile=false&useUnicode=true&autoReconnect=true&failOverReadOnly=false&characterEncoding=UTF-8&allowMultiQueries=true&useJDBCCompliantTimezoneShift=true&useLegacyDatetimeCode=false&serverTimezone=GMT%2B8
base-redis-host: ${cloudstar.redis.host:127.0.0.1}
base-redis-port: ${cloudstar.redis.port:6379}
base-redis-password: ${redis.password}
base-redis-database: ${cloudstar.redis.database:0}
root-logging-level: ${cloudstar.logging.level:info}
mybatis-logging-level: ${cloudstar.mybatis.logging.level:info}
base-mongo-host: ${cloudstar.mongo.host}
base-mongo-dbName: ${cloudstar.mongo.dbname:rightcloud}
base-mongo-port: ${cloudstar.mongo.port}
base-mongo-username: ${cloudstar.mongo.username}
base-mongo-password: ${mongodb.password}
base-mongo-ssl: ${cloudstar.mongodb.ssl.enabled:false}
base-mongo-algorithm: ${cloudstar.mongodb.ssl.algorithm:TLSv1.2}
base-mongo-jks-pwd: ${mongodb.client.keystore.password}
base-mongo-jks-path: ${cloudstar.keystore.mongodb.filepath}
base-mongo-cert-pwd: ${mongodb.client.p12.password}
base-mongo-cert-path: ${cloudstar.p12.mongodb.filepath}
cloudstar:
  db:
    password: ${bss.db.password}
---
server:
  port: ${base-port:38085}
  max-http-header-size: 128KB
  servlet:
    context-path: /
    encoding:
      charset: UTF-8
      force: true
      enabled: true
  ssl:
    enabled: ${cloudstar.gateway.ssl.enable:false}
    enabled-protocols:
      - TLSv1.2
    ciphers:
      - TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384
      - TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384
      - TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256
      - TLS_ECDH_ECDSA_WITH_AES_256_GCM_SHA384
      - TLS_ECDH_RSA_WITH_AES_256_GCM_SHA384
      - TLS_ECDH_ECDSA_WITH_AES_128_GCM_SHA256
      - TLS_ECDH_RSA_WITH_AES_128_GCM_SHA256
      - TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256
    protocol: TLS
    key-store-type: ${cloudstar.keystore.type:JKS}
    key-store: ${cloudstar.keystore:classpath:server.jks}
    key-password: ${bss.csdr.keystore.password}
    key-store-password: ${bss.csdr.keystore.password}
  tomcat:
    uri-encoding: UTF-8
  address: ${cloudstar.local.podip:0.0.0.0}
spring:
  main:
    #懒加载置为true
    lazy-initialization: true
    banner-mode: off
    #允许覆盖bean定义
    allow-bean-definition-overriding: true
    allow-circular-references: true
  application:
    name: rightcloud-csdr-collector
  aop:
    proxy-target-class: true

# MySQL数据库配置
  datasource:
    name: ${base-dbName}
    type: com.zaxxer.hikari.HikariDataSource
    url: ${base-dbUrl}
    username: ${base-dbUserName}
    password: ${base-dbPassword}
    driver-class-name: org.mariadb.jdbc.Driver
    hikari:
      minimum-idle: 5
      maximum-pool-size: 200
      connection-test-query: SELECT 1
      max-lifetime: 1800000
      connection-timeout: 30000
    mongodb:
      connection-string: mongodb://${base-mongo-host}:${base-mongo-port}/?&ssl=${base-mongo-ssl}&authMechanism=MONGODB-X509
      database: ${base-mongo-dbName:rightcloud}
      ssl: ${base-mongo-ssl}
      username: ${base-mongo-username}
      password: ${base-mongo-password}
      algorithm: ${base-mongo-algorithm}
      jks-pwd: ${base-mongo-jks-pwd}
      jks-path: ${base-mongo-jks-path}
      client-cert-pwd: ${base-mongo-cert-pwd}
      client-cert-path: ${base-mongo-cert-path}
# Redis配置
  redis:
    database: ${base-redis-database}
    host: ${base-redis-host}
    port: ${base-redis-port}
    password: ${base-redis-password}
    lettuce:
      pool:
        max-idle: 128
        max-active: 512
        max-wait: 10000
        min-idle: 4

  jackson:
    default-property-inclusion: ALWAYS
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      fail_on_empty_beans: false
  servlet:
    multipart:
      enabled: true
      max-file-size: 200MB
  freemarker:
    checkTemplateLocation: false
  jpa:
    open-in-view: false

logging:
  level:
    root: ${root-logging-level:INFO}
    cn.com.cloudstar.rightcloud: info
    org.mybatis: ${mybatis-logging-level:warn}
    org.apache.http: OFF
    org.apache.http.headers: OFF
    org.apache.tomcat.util.http: OFF
    org.apache.http.wire: OFF
    org.apache.coyote.http11.Http11InputBuffer: OFF
    sun.net.www.protocol.http: OFF
    reactor.netty.http: info
    org.mongodb.driver: warn
    com.zaxxer.hikari.HikariConfig: info
    com.alibaba.nacos: warn
    com.obs.services.internal.RestStorageService: warn
    com.obs.services.AbstractClient: INFO
    com.obs.log.AccessLogger: ERROR
    org.apache.dubbo.remoting.transport.netty4: warn
    org.apache.dubbo.registry.support: warn
    org.apache.catalina.valves: warn
    com.sun.mail.smtp: OFF
    org.apache.catalina.util: INFO
    org.apache.dubbo.registry: OFF
    com.alibaba.nacos.api.config.remote.request: OFF

# 日志目录
log:
  home: ${cloudstar.logging.home:/cmplog}

endpoints:
  health:
    sensitive: false
  restart:
    enabled: true
  shutdown:
    enabled: true
  cors:
    allowed-origins: "*"
    allowed-headers: "*"
    allowed-methods:
      - "GET"
      - "POST"
      - "PUT"
      - "DELETE"
management:
  health:
    mongo:
      enabled: false
    nacos-discovery:
      enabled: false
    nacos-config:
      enabled: false
  security:
    enabled: false
  server:
    port: ${base-mgr-port:8081}
    servlet:
      context-path: /
    ssl:
      enabled: ${cloudstar.gateway.ssl.enable:false}
      enabled-protocols:
        - TLSv1.2
      ciphers:
        - TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384
        - TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384
        - TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256
        - TLS_ECDH_ECDSA_WITH_AES_256_GCM_SHA384
        - TLS_ECDH_RSA_WITH_AES_256_GCM_SHA384
        - TLS_ECDH_ECDSA_WITH_AES_128_GCM_SHA256
        - TLS_ECDH_RSA_WITH_AES_128_GCM_SHA256
        - TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256
      protocol: TLS
      key-store-type: ${cloudstar.keystore.type:JKS}
      key-store: ${cloudstar.keystore:classpath:server.jks}
      key-password: ${bss.csdr.keystore.password}
      key-store-password: ${bss.csdr.keystore.password}
    address: ${cloudstar.local.podip:0.0.0.0}
  endpoints:
    health:
      enabled: true
      show-details: always
      status:
        order: up,down
        http-mapping:
          down: 500
    metrics:
      enabled: true
      tags:
        application: ${spring.application.name}
      export:
        prometheus:
          enabled: true
    web:
      base-path: /
      path-mapping:
        health: healthz
        prometheus: metrics
        metrics: bootmetrics
      exposure:
        include: health,metrics,prometheus
    jmx:
      exposure:
        include: health,info
cron:
  collector:  ${cron-collector:0 0/5 * * * ?}
  repair:  ${cron-repair:0 0/10 * * * ?}
  monitor:  ${cron-monitor:0 0/5 * * * ?}

ribbon:
  eager-load:
    enabled: true
    clients:
      - cloud-oss
      - cloud-resource
      - cloud-oss-yyh
      - cloud-resource-ly

feign:
  client:
    config:
      default:
        connectTimeout: 60000  # 指定Feign客户端连接提供者的超时时限
        readTimeout: 60000
  httpclient:
    enabled: true