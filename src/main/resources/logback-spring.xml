<?xml version="1.0" encoding="UTF-8"?>

<configuration scan="true" scanPeriod="30 seconds" debug="false">
    <springProperty scope="context" name="applicationName" source="spring.application.name"
                    defaultValue="cloud-bss"/>

    <!-- 日志滚动方式，动清理功能，配合 fluentd 的收集，保证日志缓存被定时清理-->
    <springProperty scope="context" name="LOG_HOME" source="log.home" defaultValue="/cmplog"/>

    <!-- 排除异常换行符以及不可见字符 -->
    <conversionRule conversionWord="nlf" converterClass="cn.com.cloudstar.rightcloud.common.security.EscapeCharThrowableConverter"/>

    <!-- 日志消息输出到控制台配置 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder charset="UTF-8">
            <pattern>[${applicationName}] %d{yyyy-MM-dd HH:mm:ss} [%X{traceId},%X{spanId}] [%X{account:-system}] %-5level %logger{36} - %replace(%msg){'\p{C}', ''} %nlf%n
            </pattern>
        </encoder>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="cn.com.cloudstar.rightcloud.module.standard.util.LogFilterPatternLayout">
                <pattern>[${applicationName}] %d{yyyy-MM-dd HH:mm:ss} [%X{traceId},%X{spanId}] [%X{account:-system}] %-5level %logger{36} - %replace(%msg){'\p{C}', ''} %nlf%n</pattern>
            </layout>
        </encoder>
    </appender>

    <appender name="INFO_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="cn.com.cloudstar.rightcloud.module.standard.util.LogFilterPatternLayout">
                <pattern>[${applicationName}] %d{yyyy-MM-dd HH:mm:ss} [%X{traceId},%X{spanId}] [%X{account:-system}] %-5level %logger{36} - %replace(%msg){'\p{C}', ''} %nlf%n</pattern>
            </layout>
        </encoder>
        <file>${LOG_HOME}/${applicationName}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <!--日志文件输出的文件名-->
            <FileNamePattern>${LOG_HOME}/${applicationName}.%i.gz</FileNamePattern>
            <MaxIndex>1</MaxIndex>
            <MinIndex>1</MinIndex>
        </rollingPolicy>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
            <pattern>[${applicationName}] %d{yyyy-MM-dd HH:mm:ss} [%X{traceId},%X{spanId}] [%X{account:-system}] %-5level %logger{36} - %replace(%msg){'\p{C}', ''} %nlf%n</pattern>
        </layout>
        <!--日志文件最大的大小-->
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <MaxFileSize>50MB</MaxFileSize>
        </triggeringPolicy>
    </appender>

    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <file>${LOG_HOME}/${applicationName}_error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <!--日志文件输出的文件名-->
            <FileNamePattern>${LOG_HOME}/${applicationName}_error.%i.gz</FileNamePattern>
            <MaxIndex>1</MaxIndex>
            <MinIndex>1</MinIndex>
        </rollingPolicy>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
            <!--排除不可见控制符，   - %replace(%msg){'\p{C}', ''} %nlf   请勿更改-->
            <pattern>[${applicationName}] %d{yyyy-MM-dd HH:mm:ss} [%X{traceId},%X{spanId}] [%X{account:-system}] %-5level %logger{36} - %replace(%msg){'\p{C}', ''} %nlf%n</pattern>
        </layout>
        <!--日志文件最大的大小-->
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <MaxFileSize>50MB</MaxFileSize>
        </triggeringPolicy>
    </appender>

    <!--相应模块日志输入等级，请勿随意更改-->
    <logger name="com.alibaba.nacos.common.remote.client" level="OFF" />
    <logger name="com.alibaba.nacos.client.auth.impl.process.HttpLoginProcessor" level="OFF" />
    <logger name="org.springframework" level="WARN"/>
    <logger name="org.apache.dubbo" level="WARN"/>
    <logger name="org.apache.http.wire" level="OFF"/>
    <logger name="org.apache.http.headers" level="OFF"/>
    <logger name="org.apache.tomcat.util.http" level="OFF"/>
    <logger name="org.apache.coyote.http11.Http11InputBuffer" level="OFF"/>
    <logger name="org.mongodb.driver" level="WARN"/>
    <logger name="org.keycloak.adapters" level="INFO"/>
    <logger name="com.zaxxer.hikari.HikariConfig" level="INFO"/>
    <logger name="com.alibaba.dubbo" level="ERROR"/>
    <logger name="sun.net.www.protocol.http" level="OFF"/>
    <logger name="com.obs.services.internal.RestStorageService" level="WARN"/>
    <logger name="com.obs.services.AbstractClient" level="INFO"/>
    <logger name="com.obs.log.AccessLogger" level="ERROR"/>
    <logger name="org.apache.dubbo.remoting.transport.netty4" level="warn"/>
    <logger name="org.apache.dubbo.registry.support" level="warn"/>
    <logger name="org.apache.catalina.valves" level="warn"/>
    <logger name="com.sun.mail.smtp" level="OFF"/>
    <logger name="org.apache.dubbo.registry" level="OFF"/>
    <logger name="org.apache.catalina.util" level="info"/>
    <Logger name="com.alibaba.nacos.api.config.remote.request" level="OFF" />
    <logger name="cn.com.cloudstar.rightcloud" level="INFO" additivity="false">
        <appender-ref ref="INFO_FILE"/>
        <appender-ref ref="ERROR_FILE"/>
        <appender-ref ref="STDOUT"/>
    </logger>

    <!--输出到日志文件-->
    <root level="INFO">
        <appender-ref ref="ERROR_FILE"/>
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="STDOUT"/>
    </root>

</configuration>
