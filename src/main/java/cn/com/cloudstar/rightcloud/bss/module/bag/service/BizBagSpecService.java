/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.bag.service;


import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.entity.BizBagSpec;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.request.CreateBizBagSpecRequest;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.request.DescribeBizBagSpecRequest;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.response.DescribeBizBagSpecResponse;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.response.PageDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 运营-用户套餐包实例表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-07
 */
public interface BizBagSpecService extends IService<BizBagSpec>  {
    /**
     * 创建套餐包规格表
     * @param bagSpec
     * @return
     */
    Integer createBizBagSpec(CreateBizBagSpecRequest bagSpec);

    /**
     * 修改套餐包规格
     * @param bizSpec
     */
    Boolean updateBizSpecById(BizBagSpec bizSpec);

    /**
     * 删除套餐包规格
     * @param ids
     */
    void deleteBizSpecById(List<String> ids);

    /**
     * 分页查询套餐包规格
     *
     * @param request 创建参数
     * @return 创建是否成功
     */
    IPage<BizBagSpec> listBizSpecBag(IPage<BizBagSpec> page, DescribeBizBagSpecRequest request);

    PageDTO listBizSpecBags(DescribeBizBagSpecRequest request);
}
