/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.common.util;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;

import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.bss.common.constants.UserTypeConstants;
import cn.com.cloudstar.rightcloud.bss.common.mybatis.enums.DataScopeEnum;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Org;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.pojo.User;
import cn.com.cloudstar.rightcloud.bss.module.access.service.SysUserService;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.account.service.IBizBillingAccountService;
import cn.com.cloudstar.rightcloud.bss.module.sys.pojo.Role;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.OrgService;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.UserType;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;

/**
 * The type DistributorAuthUtil.
 *
 * Created on 2020/9/1
 *
 * <AUTHOR>
 */
@Slf4j
public class DistributorAuthUtil {

    private static final IBizBillingAccountService bizBillingAccountService = SpringContextHolder.getBean(
            IBizBillingAccountService.class);

    private static final OrgService orgService = SpringContextHolder.getBean(
            OrgService.class);

    private static final SysUserService userService = SpringContextHolder.getBean(
            SysUserService.class);
    /**
     * 用于判断查询属性
     */
    private static final List<Integer> FLAG_LIST = Lists.newArrayList(1, 2);

    public static RestResult put(Criteria criteria, QueryWrapper<?> wrapper, User user, Integer flag) {
        try {
            if (UserType.DISTRIBUTOR_USER.equals(user.getUserType())
                && !UserTypeConstants.CONSOLE.equals(user.getRemark())) {

                List<BizBillingAccount> bizBillingAccounts;
                QueryWrapper<BizBillingAccount> accountQueryWrapper = new QueryWrapper<>();
                List<Role> roleList = userService.selectUserRoleByUserSid(user.getUserSid());
                // 如果是销售
                Optional<Role> first = roleList.stream().min(Comparator.comparing(Role::getDataScope));
                if (first.isPresent()) {
                    Role role = first.get();
                    if (Objects.equals(DataScopeEnum.DATA_SCOPE_COMPANY.getScope(), role.getDataScope())) {
                        accountQueryWrapper.lambda().eq(BizBillingAccount::getSalesmenId, user.getUserSid());
                        bizBillingAccounts = bizBillingAccountService.list(accountQueryWrapper);
                        if (CollectionUtil.isEmpty(bizBillingAccounts)) {
                            criteria.getCondition().put("accountIds", Collections.singletonList(-1));
                            return null;
                        }
                    } else {
                        List<Long> orgSids = orgService.selectCustomerOrgSids(user.getOrgSid());
                        if (null == orgSids || orgSids.size() == 0) {
                            return null;
                        }
                        accountQueryWrapper.lambda().in(BizBillingAccount::getOrgSid, orgSids);
                        bizBillingAccounts = bizBillingAccountService.list(accountQueryWrapper);
                        if (CollectionUtil.isEmpty(bizBillingAccounts)) {
                            if (wrapper != null && FLAG_LIST.contains(flag)) {
                                wrapper.in("account_id", Collections.singletonList(-1));
                            } else if (wrapper != null) {
                                wrapper.in("account_sid", Collections.singletonList(-1));
                            }
                            if (criteria != null) {
                                criteria.getCondition().put("accountIds", Collections.singletonList(-1));
                            }
                            return null;
                        }
                    }

                    List<Long> accountIds = bizBillingAccounts.stream().map(BizBillingAccount::getId).collect(
                            Collectors.toList());
                    if (wrapper != null && FLAG_LIST.contains(flag)) {
                        wrapper.in("account_id", accountIds);
                    } else if (wrapper != null) {
                        wrapper.in("account_sid", accountIds);
                    }
                    if (criteria != null) {
                        criteria.getCondition().put("accountIds", accountIds);
                    }
                }
            }
        } catch (Exception e) {
            log.error("分销商用户查询条件错误！", e);
            return null;
        }
        return new RestResult();
    }

    public static RestResult put(Criteria criteria, QueryWrapper<?> wrapper, User user, Integer flag,Long entityId) {
        try {
            if (UserType.DISTRIBUTOR_USER.equals(user.getUserType())
                    && !UserTypeConstants.CONSOLE.equals(user.getRemark())) {

                List<BizBillingAccount> bizBillingAccounts = null;
                QueryWrapper<BizBillingAccount> accountQueryWrapper = new QueryWrapper<>();
                List<Role> roleList = userService.selectUserRoleByUserSid(user.getUserSid());
                // 如果是销售
                Optional<Role> first = roleList.stream()
                        .sorted(Comparator.comparing(Role::getDataScope))
                        .findFirst();
                if (first.isPresent()){
                    Role role = first.get();
                    if (Objects.equals(DataScopeEnum.DATA_SCOPE_COMPANY.getScope(), role.getDataScope())){
                        accountQueryWrapper.lambda().eq(BizBillingAccount::getSalesmenId, user.getUserSid());
                        bizBillingAccounts = bizBillingAccountService.list(accountQueryWrapper);
                        if (CollectionUtil.isEmpty(bizBillingAccounts)) {
                            criteria.getCondition().put("accountIds", Arrays.asList(-1));
                            return null;
                        }
                    } else {
                        if (Objects.isNull(entityId)) {
                            entityId = RequestContextUtil.getEntityId();
                        }

                        accountQueryWrapper.lambda().in(BizBillingAccount::getDistributorId, user.getOrgSid()).eq(BizBillingAccount::getEntityId,entityId);
                        bizBillingAccounts = bizBillingAccountService.list(accountQueryWrapper);
                        if (CollectionUtil.isEmpty(bizBillingAccounts)) {
                            criteria.getCondition().put("accountIds", Arrays.asList(-1));
                            return null;
                        }
                    }
                    List<Long> accountIds = bizBillingAccounts.stream().map(BizBillingAccount::getId).collect(
                            Collectors.toList());
                    if (wrapper != null && FLAG_LIST.contains(flag)) {
                        wrapper.in("account_id", accountIds);
                    } else if (wrapper != null){
                        wrapper.in("account_sid", accountIds);
                    }
                    if (criteria != null) {
                        criteria.getCondition().put("accountIds", accountIds);
                    }
                }
            }
        } catch (Exception e) {
            log.error("分销商用户查询条件错误！", e);
            return null;
        }
        return new RestResult();
    }

    /**
     * 根据accountId查询所属分销商
     *
     * @param accountId
     */
    public static String seachDistributor(Long accountId) {

        if (null == accountId) {
            return null;
        }

        BizBillingAccount accountBean = bizBillingAccountService.getById(accountId);
        if (accountBean != null && accountBean.getOrgSid() != null) {
            Org org = orgService.getById(accountBean.getOrgSid());
            if (org != null && null != org.getParentId()) {
                Org distributorOrg = orgService.getById(org.getParentId());
                if (distributorOrg != null) {
                    return distributorOrg.getOrgName();
                }
            }
        }

        return null;
    }
}
