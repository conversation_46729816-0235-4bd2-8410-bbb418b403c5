/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.common.pojo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @createDate 2020/07/13 15:51
 */
@Data
public class ProductService implements Serializable {
    /**
     * 计费类型
     */
    private String category;


    /**
     * 按小时计费价格
     */
    private BigDecimal hourPrice;
    /**
     * 小时计费详情
     */
    private String hourPriceDesc;
    /**
     * 按月计费价格
     */
    private BigDecimal monthPrice;
    /**
     * 按月计费价格详情
     */
    private String mouthPriceDesc;
    /**
     * 单次计费价格
     */
    private BigDecimal oncePrice;
    /**
     * 单次计费详情描述
     */
    private String oncePriceDesc;

    /**
     * 平台折扣系数
     */
    private BigDecimal platformDiscount;

    /**
     * 是否指定月数
     */
    private Boolean appoint;

    /**
     * 计费类型，资源计费（resource），服务计费(service)，额外配置(extraConfig)费用
     */
    private String priceType;

    /**
     * 额外收费类型，单次（once）或周期（period）
     */
    private String extraType;

    /**
     * 价格描述
     */
    private String priceDesc;

    /**
     * 具体计费配置
     */
    private String billingSpec;

    /**
     * 具体资源配置
     */
    private String resourceConfig;

    /**
     * 资源类型
     */
    private String resourceType;

    /**
     * 上浮点数
     */
    private BigDecimal floatingRatio;

    /**
     * 原价
     */
    private BigDecimal originalPrice;

    /**
     * 最终价格
     */
    private BigDecimal finalCost;

    /**
     * 客户自定义标志
     */
    private Boolean customFlag;

    public BigDecimal getFinalCost() {
        return Objects.isNull(finalCost) ? BigDecimal.ZERO : this.finalCost;
    }

}
