/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 新增网关入参
 *
 * <AUTHOR>
 */
@ApiModel(description = "新增网关")
@Data
public class CreateRouterGatewayRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 路由器ID
     */

    @ApiModelProperty(value = "路由器ID", name = "id", example = "123")
    @NotNull
    private Long id;

    /**
     * 云环境ID
     */
    @ApiModelProperty(value = "云环境ID", name = "cloudEnvId", example = "123")
    private Long cloudEnvId;

    /**
     * 静网络ID
     */
    @ApiModelProperty(value = "静态网络ID", name = "vpcId", example = "123")
    private Long vpcId;

    /**
     * 静网络UUID
     */
    @ApiModelProperty(value = "静态网络UUID", name = "vpcUuid", example = "6493d490-f7e2-4f2c-9b1d-c6e3b8c093e6")
    private String vpcUuid;

    /**
     * 静网络名称
     */
    @ApiModelProperty(value = "静态网络名称", name = "vpcName", example = "网络123")
    private String vpcName;

    /**
     * 外部网络名称
     */
    @ApiModelProperty(value = "外部网络名称", name = "publicName", example = "网络123")
    private String publicName;

    /**
     * 外部网络地址
     */
    @ApiModelProperty(value = "外部网络地址", name = "externalNetworkAddress")
    private String externalNetworkAddress;

    /**
     * 路由器名称
     */
    @ApiModelProperty(value = "路由器名称", name = "name", example = "路由器123")
    private String name;

    /**
     * 外部网络ID
     */
    @ApiModelProperty(value = "外部网络ID", name = "externalNetworkId", example = "6493d490-f7e2-4f2c-9b1d-c6e3b8c093e6")
    private String externalNetworkId;

    /**
     * 资源ID
     */
    @ApiModelProperty(value = "资源ID", name = "uuid", example = "597eb986-4dbd-4443-b7fc-07dbcd3a292a")
    private String uuid;

    /**
     * 区域
     */
    @ApiModelProperty(value = "区域", name = "region")
    private String region;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述", name = "description")
    private String description;

    /**
     * 路由表类型，0：默认路由表；1：普通路由表
     */
    @ApiModelProperty(value = "路由表类型", name = "routeTableType", example = "0(默认路由表)|1(普通路由表)")
    private Integer routeTableType;

    /**
     * 路由表类型
     */
    @ApiModelProperty(value = "路由表类型", name = "routeTableType", example = "默认路由表|普通路由表")
    private String routeTableTypeName;

    /**
     * 标签名称
     */
    @ApiModelProperty("标签名称")
    private String tagNames;

    /**
     * 标签值
     */
    @ApiModelProperty("标签值")
    private String tagValues;

    /**
     * rgb代码
     */
    @ApiModelProperty("code")
    private String rgbCodes;

}
