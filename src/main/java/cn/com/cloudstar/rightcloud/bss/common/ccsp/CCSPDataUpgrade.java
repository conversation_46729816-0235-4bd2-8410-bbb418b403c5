/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */
package cn.com.cloudstar.rightcloud.bss.common.ccsp;

import cn.com.cloudstar.rightcloud.bss.common.pojo.Org;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.SysGroup;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.User;
import cn.com.cloudstar.rightcloud.bss.module.access.service.SysGroupService;
import cn.com.cloudstar.rightcloud.bss.module.access.service.SysRoleService;
import cn.com.cloudstar.rightcloud.bss.module.access.service.SysUserService;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.account.service.IBizBillingAccountService;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.BizBillingSpecGroup;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.BizBillingTariffSpecCharge;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.BizBillingTariffSpecChargeService;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.IBizBillingSpecGroupService;
import cn.com.cloudstar.rightcloud.bss.module.contract.pojo.entity.BizContract;
import cn.com.cloudstar.rightcloud.bss.module.contract.service.BizContractService;
import cn.com.cloudstar.rightcloud.bss.module.invoice.pojo.entity.BizInvoice;
import cn.com.cloudstar.rightcloud.bss.module.invoice.service.IBizInvoiceService;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrder;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderDetail;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderDetailService;
import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderService;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.ServiceCategory;
import cn.com.cloudstar.rightcloud.bss.module.sfs.service.ServiceCategoryService;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.OrgMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.pojo.Role;
import cn.com.cloudstar.rightcloud.bss.module.sys.pojo.SysHpcPass;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.OrgService;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.SysHpcPassService;
import cn.com.cloudstar.rightcloud.common.util.CCSP;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria;
import cn.com.cloudstar.rightcloud.remote.api.iam.pojo.HcsoUser;
import cn.com.cloudstar.rightcloud.remote.api.iam.service.HcsoUserRemoteService;
import cn.hutool.core.collection.CollectionUtil;
import com.ccsp.sdk.crypto.CryptoException;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.stream.Collectors;


/**
 * @description: 原始数据进行国密升级
 * @author: ouyonghui
 * @date: 2023/4/3 15:26
 */
@Component
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
public class CCSPDataUpgrade {
    final ServiceCategoryService serviceCategoryService;
    final IBizBillingSpecGroupService bizBillingSpecGroupService;
    final BizBillingTariffSpecChargeService chargeService;
    final IServiceOrderService serviceOrderService;
    final IServiceOrderDetailService serviceOrderDetailService;
    final BizContractService bizContractService;
    final IBizInvoiceService bizInvoiceService;
    final IBizBillingAccountService bizBillingAccountService;
    final SysUserService userService;
    final SysRoleService roleService;
    final SysGroupService sysGroupService;
    final CCSPHandler ccspHandler;
    final SysHpcPassService sysHpcPassService;
    @DubboReference
    public HcsoUserRemoteService hcsoUserRemoteService;
    final OrgService orgService;

    public void upgrade(CCSP ccsp) throws CryptoException, InvocationTargetException, IllegalAccessException {
        log.info("---------------------ccspUpgrade beginning---------------------");
        ccspHandler.setCCSPConfig(ccsp);
        orgCCSPUpgrade();
        userCCSPUpgrade();
        hcsoUserUpgrade();
        sysHpcPassCCSPUpgrade();
        serviceCategoryCCSPUpgrade();
        specGroupCCSPUpgrade();
        tariffSpecChargeCCSPUpgrade();
        serverOrderCCSPUpgrade();
        serverOrderDetailCCSPUpgrade();
        bizInvoiceCCSPUpgrade();
        bizBillingAccountCCSPUpgrade();
        bizContractServiceCCSPUpgrade();
        roleCCSPUpgrade();
        sysUserGroupCCSPUpgrade();
        log.info("---------------------ccspUpgrade end---------------------");
    }

    private void sysHpcPassCCSPUpgrade() throws CryptoException, InvocationTargetException, IllegalAccessException {
        List<SysHpcPass> sysHpcPasses = sysHpcPassService.list();
        for (SysHpcPass sysHpcPass : sysHpcPasses) {
            sysHpcPass.setSkipCCSPHandle(true);
        }
        sysHpcPassService.updateBatchById(sysHpcPasses);
        log.info("---------------------sysHpcPassCCSP ccspUpgrade success---------------------");
    }


    public void serviceCategoryCCSPUpgrade() throws CryptoException, InvocationTargetException, IllegalAccessException {
        List<ServiceCategory> serviceCategories = serviceCategoryService.list();
        for (ServiceCategory serviceCategory : serviceCategories) {
            serviceCategory.setSkipCCSPHandle(true);
        }
        serviceCategoryService.updateBatchById(serviceCategories);
        log.info("---------------------serviceCategoryCCSP ccspUpgrade success---------------------");
    }


    public void specGroupCCSPUpgrade() throws CryptoException, InvocationTargetException, IllegalAccessException {
        List<BizBillingSpecGroup> bizBillingSpecGroups = bizBillingSpecGroupService.list();
        for (BizBillingSpecGroup bizBillingSpecGroup : bizBillingSpecGroups) {
            bizBillingSpecGroup.setSkipCCSPHandle(true);
        }
        bizBillingSpecGroupService.updateBatchById(bizBillingSpecGroups);
        log.info("---------------------specGroupCCSP ccspUpgrade success---------------------");
    }

    public void tariffSpecChargeCCSPUpgrade() throws CryptoException, InvocationTargetException, IllegalAccessException {
        List<BizBillingTariffSpecCharge> bizBillingTariffSpecCharges = chargeService.list();
        for (BizBillingTariffSpecCharge bizBillingTariffSpecCharge : bizBillingTariffSpecCharges) {
            bizBillingTariffSpecCharge.setSkipCCSPHandle(true);
        }
        chargeService.updateBatchById(bizBillingTariffSpecCharges);
        log.info("---------------------tariffSpecChargeCCSP ccspUpgrade success---------------------");
    }

    public void serverOrderCCSPUpgrade() throws CryptoException, InvocationTargetException, IllegalAccessException {
        List<ServiceOrder> serviceOrders = serviceOrderService.list();
        for (ServiceOrder serviceOrder : serviceOrders) {
            serviceOrder.setSkipCCSPHandle(true);
        }
        serviceOrderService.updateBatchById(serviceOrders);
        log.info("---------------------serverOrderCCSP ccspUpgrade success---------------------");
    }

    public void serverOrderDetailCCSPUpgrade() throws CryptoException, InvocationTargetException, IllegalAccessException {
        List<ServiceOrderDetail> serviceOrderDetails = serviceOrderDetailService.list();
        for (ServiceOrderDetail serviceOrderDetail : serviceOrderDetails) {
            serviceOrderDetail.setSkipCCSPHandle(true);
        }
        serviceOrderDetailService.updateBatchById(serviceOrderDetails);
        log.info("---------------------serverOrderDetailCCSP ccspUpgrade success---------------------");
    }

    public void bizContractServiceCCSPUpgrade() throws CryptoException, InvocationTargetException, IllegalAccessException {
        List<BizContract> bizContracts = bizContractService.list();
        bizContractService.updateBatchById(bizContracts);
        log.info("---------------------bizContractServiceCCSP ccspUpgrade success---------------------");
    }

    public void bizInvoiceCCSPUpgrade() throws CryptoException, InvocationTargetException, IllegalAccessException {
        List<BizInvoice> bizInvoices = bizInvoiceService.list();
        for (BizInvoice bizInvoice : bizInvoices) {
            bizInvoice.setSkipCCSPHandle(true);
        }
        bizInvoiceService.updateBatchById(bizInvoices);
        log.info("---------------------bizInvoiceCCSP ccspUpgrade success---------------------");
    }

    public void bizBillingAccountCCSPUpgrade() throws CryptoException, InvocationTargetException, IllegalAccessException {
        List<BizBillingAccount> bizBillingAccounts = bizBillingAccountService.list();
        for (BizBillingAccount bizBillingAccount : bizBillingAccounts) {
            bizBillingAccount.setSkipCCSPHandle(true);

        }
        bizBillingAccountService.updateBatchById(bizBillingAccounts);
        log.info("---------------------bizBillingAccountCCSP ccspUpgrade success---------------------");
    }

    public void userCCSPUpgrade() throws CryptoException, InvocationTargetException, IllegalAccessException {
        List<User> users = userService.list();
        for (User user : users) {
            user.setSkipCCSPHandle(true);
        }
        userService.updateBatchById(users);
        log.info("---------------------userCCSP ccspUpgrade success---------------------");
    }

    public void hcsoUserUpgrade() {
        List<HcsoUser> list = hcsoUserRemoteService.selectByParams(new Criteria());
        list.forEach(e -> {
            e.setSkipCCSPHandle(true);
            try {
                ccspHandler.upgradeCCSP(e);
            } catch (Exception exception) {
                log.info("---------------------userCCSP hcsoUserUpgrade upgradeCCSP error: {}---------------------", exception.getMessage());
            }
            hcsoUserRemoteService.updateByPrimaryKey(e);
        });

        log.info("---------------------userCCSP hcsoUserUpgrade success---------------------");
    }

    public void roleCCSPUpgrade() throws CryptoException, InvocationTargetException, IllegalAccessException {
        List<Role> roles = roleService.selectByParams(null);
        for (Role role : roles) {
            role.setSkipCCSPHandle(true);
        }
        roleService.updateBatchById(roles);
        log.info("---------------------roleCCSP ccspUpgrade success---------------------");
    }

    public void sysUserGroupCCSPUpgrade() throws CryptoException, InvocationTargetException, IllegalAccessException {
        List<SysGroup> sysGroups = sysGroupService.selectByParams(null);
        for (SysGroup sysGroup : sysGroups) {
            sysGroup.setSkipCCSPHandle(true);
        }
        sysGroupService.updateBatchById(sysGroups);
        log.info("---------------------sysUserGroupCCSP ccspUpgrade success---------------------");

    }

    public void orgCCSPUpgrade() {
        List<Org> orgs = orgService.findAll();
        orgs = orgs.stream().filter(e -> StringUtils.isNotBlank(e.getContactPhone())).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(orgs)) {
            for (Org org : orgs) {
                org.setSkipCCSPHandle(true);
            }

            orgService.updateBatchById(orgs);
        }
        log.info("---------------------orgCCSPUpgrade ccspUpgrade success---------------------");
    }



}
