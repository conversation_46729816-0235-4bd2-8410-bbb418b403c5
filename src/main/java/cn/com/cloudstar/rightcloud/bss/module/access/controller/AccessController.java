/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.access.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.Page;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.security.Security;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.ResCloudEnv;
import cn.com.cloudstar.rightcloud.bss.common.annotation.Authorize;
import cn.com.cloudstar.rightcloud.bss.common.annotation.AuthorizeBss;
import cn.com.cloudstar.rightcloud.bss.common.annotation.DataPermission;
import cn.com.cloudstar.rightcloud.bss.common.annotation.PermissionToModifyCheck;
import cn.com.cloudstar.rightcloud.bss.common.annotation.RejectCall;
import cn.com.cloudstar.rightcloud.bss.common.constants.AuthConstants;
import cn.com.cloudstar.rightcloud.bss.common.constants.RestConst;
import cn.com.cloudstar.rightcloud.bss.common.constants.RestConst.HttpConst;
import cn.com.cloudstar.rightcloud.bss.common.constants.SysConfigConstants;
import cn.com.cloudstar.rightcloud.bss.common.constants.UserTypeConstants;
import cn.com.cloudstar.rightcloud.bss.common.enums.PrivacyStatusEnum;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.mybatis.enums.DataScopeEnum;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Org;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult.Status;
import cn.com.cloudstar.rightcloud.bss.common.pojo.User;
import cn.com.cloudstar.rightcloud.bss.common.util.AuthUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.WebUserUtil;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.PasswordPolicy;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.PolicyGroup;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.PolicyUser;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.RequestInfo;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.SysGroup;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.UserGroup;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.pojo.PasswordConfigVO;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.pojo.PasswordPolicyDTO;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.pojo.SubuserVO;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.pojo.UserApiVO;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.pojo.UserVO;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.request.ConfirmPrivacySignRequest;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.request.CreateUserGroupRequest;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.request.CreateUsersApiRequest;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.request.CreateUsersRequest;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.request.DeletePolicy4GroupRequest;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.request.DeletePolicy4UserRequest;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.request.DescribePasswordConfigRequest;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.request.DescribeSubuserRequest;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.request.DescribeUserGroupRequest;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.request.MoveUserToGroupRequest;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.request.PasswordPolicyRequest;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.request.RemoveSubuserRequest;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.request.UnlockIamUserRequest;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.request.UpdateGroupRequest;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.request.UpdatePasswordPolicyRequest;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.request.UpdateSubuserRequest;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.request.UserSensitiveRequest;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.request.ValidPasswordPolicyRequest;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.request.ValidSubUserPasswordPolicyRequest;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.response.CcPortalUserInfoResponse;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.response.DescribeGroupDetailResponse;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.response.DescribeSimplePolicy;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.response.DescribeSimplePolicyDetailResponse;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.response.DescribeSubuserDetailResponse;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.response.DescribeSubusersResponse;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.response.DescribeUserGroupDetailResponse;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.response.DescribeUserGroupResponse;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.response.DescribeUserGroupSimpleResponse;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.response.DistributorUserDTO;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.response.PasswordPolicyResponse;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.response.PasswordRuleResponse;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.response.PfResponseUser;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.response.UserDto;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.response.UserExportDTO;
import cn.com.cloudstar.rightcloud.bss.module.access.common.enums.CharatorTypeEnum;
import cn.com.cloudstar.rightcloud.bss.module.access.common.enums.PasswordRuleEnum;
import cn.com.cloudstar.rightcloud.bss.module.access.common.enums.UserSensitiveEnum;
import cn.com.cloudstar.rightcloud.bss.module.access.common.util.PasswordUtil;
import cn.com.cloudstar.rightcloud.bss.module.access.common.util.PropertiesUtil;
import cn.com.cloudstar.rightcloud.bss.module.access.common.util.SimpleTreeBuilder;
import cn.com.cloudstar.rightcloud.bss.module.access.mapper.PolicyMapper;
import cn.com.cloudstar.rightcloud.bss.module.access.mapper.UserGroupMapper;
import cn.com.cloudstar.rightcloud.bss.module.access.mapper.UserPrivacySignMapper;
import cn.com.cloudstar.rightcloud.bss.module.access.service.HCSOTimeService;
import cn.com.cloudstar.rightcloud.bss.module.access.service.IPolicyAssertionService;
import cn.com.cloudstar.rightcloud.bss.module.access.service.IPolicyGroupService;
import cn.com.cloudstar.rightcloud.bss.module.access.service.IPolicyService;
import cn.com.cloudstar.rightcloud.bss.module.access.service.IPolicyUserService;
import cn.com.cloudstar.rightcloud.bss.module.access.service.IUserGroupService;
import cn.com.cloudstar.rightcloud.bss.module.access.service.IamSyncService;
import cn.com.cloudstar.rightcloud.bss.module.access.service.IamUserService;
import cn.com.cloudstar.rightcloud.bss.module.access.service.LdapUserService;
import cn.com.cloudstar.rightcloud.bss.module.access.service.PasswordPolicyService;
import cn.com.cloudstar.rightcloud.bss.module.access.service.SysGroupService;
import cn.com.cloudstar.rightcloud.bss.module.access.service.SysUserService;
import cn.com.cloudstar.rightcloud.bss.module.account.mapper.BizBillingAccountMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.download.mapper.BizDownloadExtentMapper;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.mapper.SfProductResourceMapper;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity.EmailVO;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity.SfProductResource;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.SendZipCompressPasswordRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.response.ResHpcClusterDetailDTO;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.FeignService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.HpcClusterService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.OrderService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.UserAuthService;
import cn.com.cloudstar.rightcloud.bss.module.invoice.mapper.BizInvoiceMapper;
import cn.com.cloudstar.rightcloud.bss.module.invoice.mapper.BizInvoiceSettingMapper;
import cn.com.cloudstar.rightcloud.bss.module.invoice.pojo.entity.BizInvoice;
import cn.com.cloudstar.rightcloud.bss.module.invoice.pojo.entity.BizInvoiceSetting;
import cn.com.cloudstar.rightcloud.bss.module.market.mapper.MarketShopSubscribeMapper;
import cn.com.cloudstar.rightcloud.bss.module.market.pojo.MarketShopSubscribe;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.ConfigMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.RoleMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.UserMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.UserRoleMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.OrgService;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.SysHpcPassService;
import cn.com.cloudstar.rightcloud.bss.module.tcc.service.CancelService;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.CH;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.COMMON.PUBLIC.B1;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.COMMON.PUBLIC.C1;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.COMMON.PUBLIC.Z1;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.USER;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.AssertUtil;
import cn.com.cloudstar.rightcloud.common.util.CrytoUtilSimple;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.common.util.excel.ExcelUtil;
import cn.com.cloudstar.rightcloud.core.annotation.idempotent.Idempotent;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.UserPrivacySign;
import cn.com.cloudstar.rightcloud.core.pojo.dto.user.UserRole;
import cn.com.cloudstar.rightcloud.core.pojo.models.access.DeletePolicyResourceRequest;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.operationlog.annotation.OperationLog;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationTypeEnum;
import cn.com.cloudstar.rightcloud.operationlog.util.OperationLogMdcUtil;
import cn.com.cloudstar.rightcloud.oss.common.cache.JedisUtil;
import cn.com.cloudstar.rightcloud.oss.common.ccsp.CCSPCacheUtil;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.UserType;
import cn.com.cloudstar.rightcloud.oss.common.encryptdata.encrypt.DesensitizationUtil;
import cn.com.cloudstar.rightcloud.oss.common.enums.CertificationStatusEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.SfProductEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.UserStatusEnum;
import cn.com.cloudstar.rightcloud.oss.common.pojo.LdapSyncRequest;
import cn.com.cloudstar.rightcloud.oss.common.pojo.UserDTO;
import cn.com.cloudstar.rightcloud.oss.common.util.DataProcessingUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.DataScopeUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.ZipUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.encrypt.Encrypt;
import cn.com.cloudstar.rightcloud.oss.common.util.ldap.OpenLdapUser;
import cn.com.cloudstar.rightcloud.remote.api.iam.pojo.HcsoUser;
import cn.com.cloudstar.rightcloud.remote.api.iam.pojo.IamUser;
import cn.com.cloudstar.rightcloud.remote.api.iam.service.HcsoUserRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.iam.service.IamRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.ResHpcClusterRemoteModule;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.hpc.HPCRemoteService;

import static cn.com.cloudstar.rightcloud.bss.module.access.common.constant.Constants.GROUP_NOT_EXIST;
import static cn.com.cloudstar.rightcloud.bss.module.access.common.constant.Constants.USER_NOT_EXIST;

/**
 * 访问控制
 *
 * <AUTHOR>
 * @createDate 2020/04/24 10:11
 */
@Api(value = "/access", tags = "访问控制")
@RestController
@RequestMapping("/access")
@Slf4j
public class AccessController {

    private static final String TWICE_VALIDATE = "LOCK_PASSWORD";

    private static final long AUTH_AGAIN_MINUTE = 30L;

    private static final String NEED_TWICE_VALIDATE = "needPasswordValidate";

    private static final String ALL = "all";

    private static final String IAM = "iam";

    private static final String PROJECT = "project";

    private static final String ALGORITHMSTR = "AES/GCM/NoPadding";

    private static final String DEFAULT_CODING = "utf-8";

    private static final String SECRET_KEY = "password.secret.key";

    private static final String SECRET_IV = "password.secret.iv";

    private static final String FOUR = "04";

    @Value("${hcsoFlg:true}")
    private Boolean hcsoFlg;

    @Autowired
    private SysUserService userService;

    @Autowired
    private UserGroupMapper userGroupMapper;

    @Autowired
    private PolicyMapper policyMapper;

    @Autowired
    private UserPrivacySignMapper userPrivacySignMapper;

    @Autowired
    private PasswordPolicyService passwordPolicyService;

    @Autowired
    private SysGroupService sysGroupService;

    @Autowired
    private RoleMapper  roleMapper;

    @Autowired
    private IUserGroupService userGroupService;

    @Autowired
    private IPolicyUserService policyUserService;

    @Autowired
    private IPolicyGroupService policyGroupService;

    @Autowired
    private IamSyncService iamSyncService;

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private BizInvoiceMapper bizInvoiceMapper;

    @Autowired
    private BizInvoiceSettingMapper bizInvoiceSettingMapper;


    @Autowired
    private IPolicyService policyService;

    @Autowired
    private UserMapper userMapper;

    @DubboReference
    private IamRemoteService iamRemoteService;
    @Autowired
    UserAuthService userAuthService;
    @Autowired
    private CancelService cancelService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private LdapUserService ldapUserService;

    @Autowired()
    @Lazy
    private StringRedisTemplate redisTemplates;

    @Autowired
    private OrgService orgService;
    @Autowired
    private IPolicyAssertionService policyAssertionService;
    @Autowired
    private FeignService feignService;

    @Autowired
    private BizBillingAccountMapper bizBillingAccountMapper;

    private static final String ONE = "1";

    private static final String NOT_NEED = "n";

    @DubboReference
    private HPCRemoteService hpcRemoteService;
    @Autowired
    private HCSOTimeService hcsoTimeService;

    @Autowired
    private HpcClusterService hpcClusterService;

    private static final String CCP_TOKEN_API = "/sso-server/authentication/form";

    private static final String ACCOUNT_ID = "ACCOUNT_ID";

    @DubboReference
    private HcsoUserRemoteService hcsoUserRemoteService;

    @Autowired
    private ConfigMapper configMapper;

    @Autowired
    private SfProductResourceMapper sfProductResourceMapper;
    @Autowired
    private SysHpcPassService hpcPassService;

    @Autowired
    private UserRoleMapper userRoleMapper;

    @Autowired
    private IamUserService iamUserService;

    @Autowired
    private BizDownloadExtentMapper bizDownloadExtentMapper;

    @Autowired
    private MarketShopSubscribeMapper subscribeMapper;

    static {
        try {
            Security.addProvider(new BouncyCastleProvider());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @GetMapping("distributor-users")
    public RestResult getbDistributorUsers() {
        User authUser = AuthUtil.getAuthUser();
        if (Objects.isNull(authUser) || Objects.isNull(authUser.getUserSid())) {
            throw new BizException(USER_NOT_EXIST);
        }

        Criteria criteria2 = new Criteria("userSid", authUser.getUserSid());
        List<UserRole> userRole = userRoleMapper.selectByParams(criteria2);
        if (CollectionUtil.isEmpty(userRole) || (userRole.size() == 1 && userRole.get(0).getRoleSid() - 304 == 0)) {
            throw new cn.com.cloudstar.rightcloud.oss.common.exception.BizException("越权访问");
        }

        if (Objects.equals(UserTypeConstants.BSS,authUser.getRemark())){
            List<DistributorUserDTO> user = new ArrayList<>();
            if (UserType.DISTRIBUTOR_USER.equals(authUser.getUserType())) {
                user = userService.getUserByDistributotId(authUser.getUserSid());
            } else {
                user = BeanConvertUtil.convert(userService.getAllDistributotUser("1",UserType.PLATFORM_USER), DistributorUserDTO.class);
            }
            if (CollectionUtil.isNotEmpty(user)) {
                List<Long> userIds = user.stream().map(DistributorUserDTO::getUserSid).collect(Collectors.toList());
                cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria queryHcso=new cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria();
                queryHcso.put("refUsers",userIds);
                List<HcsoUser> hcsoUsers = hcsoUserRemoteService.selectByParams(queryHcso);
                user = user.stream().map(u -> {
                    hcsoUsers.forEach(hu -> {
                        if (Objects.equals(u.getUserSid(), hu.getRefUserId())) {
                            u.setProjectId(hu.getProjectId());
                        }
                    });
                    return u;
                }).collect(Collectors.toList());
                return new RestResult(user);
            }
        }else {
            DistributorUserDTO distributorUserDTO=new DistributorUserDTO();
            HcsoUser hcsoUser = hcsoUserRemoteService.selectByRefUserId(authUser.getUserSid());
            if (Objects.isNull(hcsoUser)){
                throw new BizException("未接入HCSO云环境");
            }
            Org org = orgService.getById(authUser.getOrgSid());
            distributorUserDTO.setProjectId(hcsoUser.getProjectId());
            distributorUserDTO.setUserSid(authUser.getUserSid());
            distributorUserDTO.setOrgSid(authUser.getOrgSid());
            distributorUserDTO.setUid(org.getLdapOu());
            distributorUserDTO.setOrgName(org.getOrgName());
            return new RestResult(distributorUserDTO);
        }
        return new RestResult();
    }

    /**
     * 批量创建子用户
     *
     * @param request 批量创建子用户请求体
     *
     * @return {@code RestResult}
     */
    @ApiOperation("批量创建子用户")
    @PostMapping("/subuser")
    @Authorize(action = "iam:users:CreateUser")
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "'子用户信息'", tagNameUs ="'Child User Information'",
            resource = OperationResourceEnum.ADD_SUB_USER, param = "#request")
    @Idempotent
    @Encrypt
    @AuthorizeBss(action = AuthModule.CH.CH0102, actionName = "创建子用户")
    public RestResult createUser(@RequestBody @Valid CreateUsersRequest request) {
        try {
            if (CollectionUtils.isEmpty(request.getUsers())) {
                return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERROR_INSERT_FAILURE));
            }
            User authUser = AuthUtil.getAuthUser();
            List<cn.com.cloudstar.rightcloud.bss.module.access.bean.User> userList = userService.createUsers(request,
                                                                                                             authUser.getUserSid());
            List<String> userSids = new ArrayList<>();
            boolean success = true;
            String erreInfo = StringUtils.EMPTY;
            List<PfResponseUser> pfResponseUsers=new ArrayList<>();
            for (cn.com.cloudstar.rightcloud.bss.module.access.bean.User user : userList) {
                PfResponseUser pfResponseUser=new PfResponseUser();
                pfResponseUser.setAccount(user.getAccount());
                pfResponseUser.setUserId(user.getUserSid());
                pfResponseUsers.add(pfResponseUser);
                try {
                    if (!success) {
                        throw new BizException("回滚子用户");
                    }
                    user.setGroupIds(request.getGroupIds());
                    //远程添加
                    iamRemoteService.insertUser(BeanConvertUtil.convert(user, IamUser.class));

                    // 判断子用户是否开启SSO
                    if (iamUserService.checkCreateIamSubUser() && StringUtils.isNotBlank(user.getStatus()) && "1".equals(user.getStatus())) {
                        iamUserService.resUserCreate(user.getUserSid(), user.getAccount(), user.getOrgSid());
                    }
                }
                catch (Exception e) {
                    log.error("创建失败,{}",e.getMessage());
                   try {
                        iamRemoteService.deleteUser(user.getUserSid());
                    } catch (Exception exception) {
                        log.error("创建子用户出错 iam 用户回滚失败", exception.getMessage());
                    }
                    /* try {
                        OpenLdapUser openLdapUser = new OpenLdapUser();
                        openLdapUser.setUserName(user.getAccount())
                                .setLdapOu(org.getLdapOu())
                                .setUid(user.getUserSid() + "")
                                .setOrgNumber(org.getOrgSid() + "");
                        ldapUserService.deleteLdapUser(openLdapUser);
                    } catch (Exception exception) {
                        log.error("创建子用户出错 ldap 用户回滚失败", exception.getMessage());
                    }*/

                    try {
                        cancelService.cancelUser(user.getUserSid());
                    } catch (Exception exception) {
                        log.error("创建子用户出错 删除本地用户回滚失败", exception.getMessage());
                    }
                    success = false;
                    if (StringUtils.isEmpty(erreInfo)) {
                        erreInfo = e.getMessage();
                    }
                }
                if (success) {
                    //发送邮件给对应子用户
                    //解密密文密码
                    String password = CrytoUtilSimple.decrypt(user.getPassword());
                    EmailVO emailVO = new EmailVO();
                    emailVO.setEmail(user.getEmail());
                    Map<String, String> map = new HashMap<>();
                    map.put("pwssword", password);
                    map.put("account", user.getAccount());
                    orderService.sendEmail(emailVO);
                    userSids.add(user.getUserSid().toString());
                }
            }
            if (!success) {
                throw new BizException(erreInfo);
            }


            String ids = StringUtils.join(userSids, ",");

            //日志记录  脱敏处理
            for (UserVO user : request.getUsers()) {
                user.setMobile(DataProcessingUtil.processing(user.getMobile(),DataProcessingUtil.PHONE));
                user.setEmail(DataProcessingUtil.processing(user.getEmail(),DataProcessingUtil.EMAIL));
            }
            request.setPassword(DataProcessingUtil.processing(request.getPassword(),DataProcessingUtil.CIPHER));

            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_INSERT_SUCCESS), pfResponseUsers);
        } catch (Exception e) {
            //日志记录  脱敏处理
            for (UserVO user : request.getUsers()) {
                user.setMobile(DataProcessingUtil.processing(user.getMobile(),DataProcessingUtil.PHONE));
                user.setEmail(DataProcessingUtil.processing(user.getEmail(),DataProcessingUtil.EMAIL));
            }
            request.setPassword(DataProcessingUtil.processing(request.getPassword(),DataProcessingUtil.CIPHER));
            if (e instanceof BizException) {
                e.printStackTrace();
                throw new BizException(e.getMessage());
            } else {
                e.printStackTrace();
                throw new BizException("服务器异常,请联系管理员");
            }
        }
    }

    /**
     * [INNER API] 批量创建子用户
     *
     * @param request 批量创建子用户请求体
     *
     * @return {@code RestResult}
     */
    @RejectCall
    @ApiOperation("批量创建子用户")
    @PostMapping("/subusers")
    @Encrypt
    public RestResult createUsers(@RequestBody @Valid CreateUsersRequest request) {
        if (CollectionUtils.isEmpty(request.getUsers())) {
            return new RestResult(RestResult.Status.FAILURE, cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.ERROR_INSERT_FAILURE));
        }
        List<UserDTO> userDtos = userService.batchCreateUsers(request, request.getParentUserSid());
        List<cn.com.cloudstar.rightcloud.bss.module.access.bean.User> userList = userDtos.stream()
                                                                                         .filter(e -> e.getLdapFalg())
                                                                                         .map(e -> BeanConvertUtil.convert(
                                                                                                 e,
                                                                                                 cn.com.cloudstar.rightcloud.bss.module.access.bean.User.class))
                                                                                         .collect(Collectors.toList());
        boolean allCreateSuccess = true;
        StringBuffer errorInfoSb = new StringBuffer();
        Org org = orgService.selectRootOrg(request.getOrgSid());
        List<String> userSids = new ArrayList<>();
        for (cn.com.cloudstar.rightcloud.bss.module.access.bean.User user : userList) {
            boolean createSubUser = true;
            try {
                if(user.getIsPreOpen()){
                    user.setEmail(null);
                }
                //远程添加
                iamRemoteService.insertUser(
                        BeanConvertUtil.convert(user, IamUser.class));

                // 判断子用户是否开启SSO
                if (iamUserService.checkCreateIamSubUser() && StringUtils.isNotBlank(user.getStatus()) && "1".equals(user.getStatus())) {
                    synchronized (this) {
                        cn.com.cloudstar.rightcloud.oss.common.util.AuthUtil.replaceUserToInvoke(() -> iamUserService.resUserCreate(user.getUserSid(), user.getAccount(), user.getOrgSid()), user.getUserSid());
                    }
                }

                // 检查导入任务，存在超时失败的情况，用户成功创建后修改导入记录成功
                this.checkDownloadExtend(request.getDownloadExtendId());

            }
            catch (Exception e) {
                log.error("AccessController.createUsers-"+user.getAccount()+" 批量创建子用户失败 error:", e);
                try {
                    if (iamUserService.checkCreateIamSubUser() && StringUtils.isNotBlank(user.getStatus()) && "1".equals(user.getStatus())) {

                            synchronized (this) {
                                log.info("AccessController.createUsers-"+user.getAccount()+" 华为IAM子用户回滚成功！！");
                                cn.com.cloudstar.rightcloud.oss.common.util.AuthUtil.replaceUserToInvoke(() -> iamUserService.deleteSubUser(null, user.getAccount(), user.getUserSid()), user.getUserSid());
                                log.info("AccessController.createUsers-"+user.getAccount()+" 创建子用户-映射IAM用户[{}]-华为IAM子用户回滚成功！！",user.getAccount());
                            }

                    }
                } catch (Exception exception) {
                    log.error("AccessController.createUsers-"+user.getAccount()+" 创建子用户出错 删除IAM_USER异常:", exception.getMessage());
                }
                try {
                    iamRemoteService.deleteUser(user.getUserSid());
                } catch (Exception exception) {
                    log.error("AccessController.createUsers-"+user.getAccount()+" 创建子用户出错 iam 用户回滚失败", exception.getMessage());
                }
                try {
                    OpenLdapUser openLdapUser = new OpenLdapUser();
                    openLdapUser.setUserName(user.getAccount())
                                .setLdapOu(org.getLdapOu())
                                .setUid(user.getUserSid() + "")
                                .setOrgNumber(org.getOrgSid() + "");
                    ldapUserService.deleteLdapUser(openLdapUser);
                } catch (Exception exception) {
                    log.error("AccessController.createUsers-"+user.getAccount()+" 创建子用户出错 ldap 用户回滚失败", exception.getMessage());
                }
                try {
                    cancelService.cancelUser(user.getUserSid());
                } catch (Exception exception) {
                    log.error("AccessController.createUsers-"+user.getAccount()+" 创建子用户出错 删除本地用户回滚失败", exception.getMessage());
                }
                if (!allCreateSuccess) {
                    errorInfoSb.append(StrUtil.C_COMMA);
                }
                allCreateSuccess = false;
                createSubUser = false;

                errorInfoSb.append(StrUtil.format("创建{}失败[{}]",user.getAccount(),e.getMessage()));
            }
            if (createSubUser) {
                userSids.add(user.getUserSid().toString());
            }
        }
        if (!allCreateSuccess) {
            throw new BizException(errorInfoSb.toString());
        }

        String ids = StringUtils.join(userSids, ",");
        if (("").equals(request.getPassword())) {
            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1102568356), ids);
        } else {
            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_650342158), ids);
        }

    }

    private void checkDownloadExtend(Long downloadExtendId) {
        if (downloadExtendId != null) {
            try {
                String jsonData = bizDownloadExtentMapper.countBizDownloadExtendByDownloadNum(downloadExtendId, "%failed%");
                if (StringUtils.isNotBlank(jsonData)) {
                    JSONObject object = JSON.parseObject(jsonData);
                    object.put("result", "success");
                    bizDownloadExtentMapper.updateBizDownloadExtentJsonData(downloadExtendId, JSON.toJSONString(object));
                }
            } catch (Exception e) {
                log.error("AccessController.checkDownloadExtend 检测导入任务异常 error:", e);
            }

        }
    }

    /**
     * 查询子用户
     *
     * @param request 查询用户请求体
     *
     * @return {@code List<DescribeSubusersResponse>}
     */
    @AuthorizeBss(action = AuthModule.CH.CH0101)
    @ApiOperation("查询子用户")
    @GetMapping("/subuser")
    @Authorize(action = "iam:users:ListUsers")
    public List<DescribeSubusersResponse> querySubuser(DescribeSubuserRequest request) {
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (Objects.isNull(authUser) || Objects.isNull(authUser.getUserSid())) {
            throw new BizException(USER_NOT_EXIST);
        }
        if (Objects.nonNull(request.getOrgSid())) {
            orgService.checkDistributorRole(Long.valueOf(request.getOrgSid()), null);
        }

        Criteria criteria = new Criteria();
        criteria.setConditionObject(request);
        String realNameLike = request.getRealNameLike();
        if(ObjectUtil.isNotEmpty(realNameLike)){
            if (CCSPCacheUtil.ccspServiceOpen()) {
                criteria.put("realNameLike", CCSPCacheUtil.verifyAndCCSPEncrypt(realNameLike));
            } else {
                criteria.put("realNameLike", null);
                criteria.put("realNameHash", DigestUtils.sha256Hex(realNameLike));
            }
        }
        String mobileLike = request.getMobileLike();
        if(ObjectUtil.isNotEmpty(mobileLike)){
            if (CCSPCacheUtil.ccspServiceOpen()) {
                criteria.put("mobileLike", CCSPCacheUtil.verifyAndCCSPEncrypt(mobileLike));
            } else {
                criteria.put("mobileLike", null);
                criteria.put("mobileHash", DigestUtils.sha256Hex(mobileLike));
            }
        }

        String emailLike = request.getEmailLike();
        if(ObjectUtil.isNotEmpty(emailLike)){
            if (CCSPCacheUtil.ccspServiceOpen()) {
                criteria.put("emailLike", CCSPCacheUtil.verifyAndCCSPEncrypt(request.getEmailLike()));
            } else {
                criteria.put("emailLike", null);
                criteria.put("emailHash", DigestUtils.sha256Hex(emailLike));
            }
        }

        if (!request.getIncludeParent()) {
            criteria.put("parentSidNotNull", true);
        }
        if (UserTypeConstants.CONSOLE.equals(authUser.getRemark())) {
            criteria.put("orgSid", authUser.getOrgSid());
        } else {
            if(ObjectUtil.isEmpty(request.getOrgSid())){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_708181218));
            }
            criteria.put("orgSid", request.getOrgSid());
        }
        if (Strings.isNullOrEmpty(request.getSortdatafield())) {
            criteria.setOrderByClause("created_dt desc");
        }
        Long pageSize = request.getPagesize();
        Long pageNum = request.getPagenum();
        int totalCount = userService.countUser(criteria);
        if (totalCount == 0) {
            Page<DescribeSubusersResponse> page = new Page<>(pageNum == null ? 0 : pageNum.intValue(), pageSize == null ? 10 : pageSize.intValue());
            page.setTotal(totalCount);
            return page;
        }

        if (pageSize != null && pageNum !=null) {
            criteria.put("startNum", pageNum <= 1 ? 0 : (pageNum - 1) * pageSize);
            criteria.setPageSize(pageSize.intValue());
        }
        List<cn.com.cloudstar.rightcloud.bss.module.access.bean.User> users = userService.selectByParam(criteria);
        List<UserDto> userDtos = BeanConvertUtil.convert(users, UserDto.class);
        //企业认证，需要展示企业的认证状态
        if ("company".equals(request.getType()) && !CollectionUtils.isEmpty(userDtos)) {
            List<Long> companyIds = userDtos.stream()
                                            .map(UserDto::getCompanyId)
                                            .collect(Collectors.toList());

            Map<Long, Org> orgMap = orgService.listByIds(companyIds)
                                              .stream()
                                              .collect(Collectors.toMap(Org::getOrgSid, org -> org));
            userDtos.forEach(user -> {
                Org org = orgMap.get(user.getCompanyId());
                user.setCertificationStatus(org.getCertificationStatus());
                user.setType(request.getType());
                if (StringUtil.isNotBlank(user.getMobile())) {
                    user.setMobile(DesensitizedUtil.mobilePhone(user.getMobile()));
                }
            });
        } else {
            userDtos.forEach(userDto -> userDto.setType(request.getType()));
        }
        // 子用户无法获取当主账号信息
        if (!ObjectUtils.isEmpty(authUser.getParentSid())) {
            userDtos = userDtos.stream().filter(u -> !u.getUserSid().equals(authUser.getParentSid())).collect(Collectors.toList());
        }
        List<DescribeSubusersResponse> convert = BeanConvertUtil.convert(userDtos, DescribeSubusersResponse.class);
        if(pageNum == null || pageSize == null ){
            return DesensitizationUtil.desensitization(convert);
        }
        Page<DescribeSubusersResponse> page = new Page<>(pageNum.intValue()-1,pageSize.intValue());
        page.addAll(DesensitizationUtil.desensitization(convert));
        page.setTotal(totalCount);
        return page;
    }

    /**
     * [INNER API] 查询子用户
     *
     * @param request 查询用户请求体
     *
     * @return {@code List<DescribeSubusersResponse>}
     */
    @RejectCall
    @ApiOperation("查询子用户")
    @GetMapping("/subuser/feign")
    public List<DescribeSubusersResponse> querySubuserByFeign(@Valid DescribeSubuserRequest request) {
        return querySubuser(request);
    }

    /**
     * 导出子用户
     *
     * @param request 查询用户请求体
     * @param response 响应
     */
    @ApiOperation("导出子用户")
    @GetMapping("/exportSubuser")
    @OperationLog(type = OperationTypeEnum.EXPORT, bizId = "#request.accountLike",
            resource = OperationResourceEnum.EXPORT_SUBUSER, param = "#request", tagName = "'子用户'")
    @AuthorizeBss(action = USER.EXPORT_ACCOUNT_INFO)
    @DataPermission(resource = OperationResourceEnum.EXPORT_SUBUSER, bizId = "#request.orgSid")
    public void expertUserList(@Valid DescribeSubuserRequest request, HttpServletResponse response) {
        User authUser = AuthUtil.getAuthUser();
        if (Objects.isNull(authUser) || Objects.isNull(authUser.getUserSid())) {
            throw new BizException(USER_NOT_EXIST);
        }
        boolean isUs = WebUtil.getHeaderAcceptLanguage();

        Criteria criteria = new Criteria();
        criteria.setConditionObject(request);
        if(Objects.isNull(request.getIncludeParent())){
            request.setIncludeParent(false);
        }
        if (!request.getIncludeParent()) {
            criteria.put("parentSidNotNull", true);
        }
        if (UserTypeConstants.CONSOLE.equals(authUser.getRemark())) {
            criteria.put("orgSid", authUser.getOrgSid());
        } else {
            List<Long>  adminSids = new ArrayList<>();
            List<cn.com.cloudstar.rightcloud.bss.module.sys.pojo.Role> currentRoleList =  roleMapper.findRolesByUserSid(RequestContextUtil.getAuthUserInfo().getUserSid());
            List<cn.com.cloudstar.rightcloud.common.pojo.Role> convertsRolesList = cn.com.cloudstar.rightcloud.common.util.BeanConvertUtil.convert(currentRoleList, cn.com.cloudstar.rightcloud.common.pojo.Role.class);
            String maxScope = DataScopeUtil.getMaxDataScope(convertsRolesList);
            if(DataScopeEnum.DATA_SCOPE_COMPANY.getScope().equals(maxScope)){
                QueryWrapper<BizBillingAccount> qwer = new QueryWrapper();
                qwer.eq("entity_id",RequestContextUtil.getEntityId());
                qwer.eq("salesmen_id",RequestContextUtil.getAuthUserInfo().getUserSid());
                List<BizBillingAccount> accounts = bizBillingAccountMapper.selectList(qwer);
                if(CollectionUtil.isNotEmpty(accounts)){
                    for(BizBillingAccount ac : accounts){
                        adminSids.add(ac.getAdminSid());
                    }
                }
                criteria.put("parentSidSidIn",adminSids);
            }else{
                criteria.put("orgSid", request.getOrgSid());
            }

        }
        if (Strings.isNullOrEmpty(request.getSortdatafield())) {
            criteria.setOrderByClause("created_dt desc");
        }
        List<UserExportDTO> list = BeanConvertUtil.convert(userService.selectByParams(criteria), UserExportDTO.class);
        if ("company".equals(request.getType()) && !CollectionUtils.isEmpty(list)) {
            List<Long> companyIds = list.stream()
                    .map(UserExportDTO::getCompanyId)
                    .collect(Collectors.toList());
            log.info("企业Id数量：[{}]，企业Id列表：[{}]", companyIds.size(), companyIds);
            Map<Long, Org> orgMap = orgService.listByIds(companyIds)
                    .stream()
                    .collect(Collectors.toMap(Org::getOrgSid, org -> org));
            list.forEach(user -> {
                Org org = orgMap.get(user.getCompanyId());
                user.setCertificationStatus(org.getCertificationStatus());
                user.setType(isUs ? "Enterprise certification" : "企业认证");
            });
        } else {
            list.forEach(userDto -> userDto.setType(isUs ? "Personal certification" : "个人认证"));
        }

        list.forEach(e -> {
            e.setAccountName(request.getAccountName());
            e.setMainAccount(request.getMainAccount());
            e.setAdminName(request.getAdminName());
            e.setStatusName(UserStatusEnum.status2DescByI18n(e.getStatus(), isUs));
            e.setCertificationStatusName(CertificationStatusEnum.status2DescByI18n(e.getCertificationStatus(), isUs));
        });
        ByteArrayOutputStream outExcel = null;
        InputStream inExcel = null;
        InputStream inZip = null;
        //设置响应
        try (OutputStream out = response.getOutputStream()) {
            //导出到excel
            //安全随机数
            String format = cn.com.cloudstar.rightcloud.bss.common.util.DateUtil.dateFormat(new Date(), "yyyyMMddHHmmss")
                    + Arrays.stream(NumberUtil.generateRandomNumber(0, 9, 5)).mapToObj(String::valueOf).collect(Collectors.joining());
            String name = isUs ? "list_of_subusers_" : "子用户信息列表";
            String destFileName = name + format + ".xlsx";
            String zipFileName = name + format + ".zip";
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode(zipFileName, "UTF-8"));
            response.setContentType("application/octet-stream");
            response.setCharacterEncoding("UTF-8");
            String template = isUs ? "template/subUser-export-template-us.xlsx" : "template/subUser-export-template.xlsx";
            outExcel = new ByteArrayOutputStream();
            ExcelUtil.write()
                    .buildWriter(outExcel, template)
                    .buildSheet(0)
                    .fill(list)
                    .finish();
            inExcel = new ByteArrayInputStream(outExcel.toByteArray());

            //压缩密码
            String password = cn.com.cloudstar.rightcloud.common.util.WebUtil.randomPwd(8);
            //压缩文件+
            inZip = ZipUtil.compress(inExcel, destFileName, password, true);
            out.write(ZipUtil.toOutputStream(inZip).toByteArray());

            //发送压缩密码至用户
            AuthUser user = RequestContextUtil.getAuthUserInfo();
            if (!ObjectUtils.isEmpty(user) && !ObjectUtils.isEmpty(user.getEmail())) {
                SendZipCompressPasswordRequest sendRequest = new SendZipCompressPasswordRequest();
                sendRequest.setUserSid(user.getUserSid());
                sendRequest.setFileName(zipFileName);
                sendRequest.setPassword(password);
                feignService.sendZipCompressPassword(sendRequest);
            }
            out.flush();
        } catch (Exception e) {
            log.error(e.getMessage(), e.getMessage());
        } finally {
            IOUtils.closeQuietly(outExcel);
            IOUtils.closeQuietly(inExcel);
            IOUtils.closeQuietly(inZip);
        }
    }

    /**
     * 删除子用户
     *
     * @param id 用户sid
     *
     * @return {@code RestResult}
     */
    @ApiOperation("删除子用户")
    @DeleteMapping("/subuser/{userSid}")
    @Authorize(action = "iam:users:DeleteUser")
    @OperationLog(type = OperationTypeEnum.DELETE, tagName = "'删除子用户'", tagNameUs ="'Delete Child User'",
            resource = OperationResourceEnum.DELETE_SUB_USER, bizId = "#id")
    @AuthorizeBss(action = CH.CH0103)
    public RestResult deleteSubuser(
            @PathVariable("userSid") @ApiParam(value = "用户id", type = "Long", required = true) Long id) {
        String key = "lock:delete_sub_user:" + id;
        Boolean lock = redisTemplate.opsForValue().setIfAbsent(key, 1, 1, TimeUnit.MINUTES);
        try {
            if (lock) {
                AuthUser authUser = RequestContextUtil.getAuthUserInfo();
                if (authUser == null) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_531007114));
                }
                if (Objects.equals(authUser.getUserSid(), id)) {
                    throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
                }
                cn.com.cloudstar.rightcloud.bss.module.access.bean.User user = userService.selectByPrimaryKey(id);
                if (ObjectUtils.isEmpty(user)){
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2061045042));
                }
                if (!authUser.getOrgSid().equals(user.getOrgSid())){
                    throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
                }
                userService.deleteSubuser(id);
                JedisUtil.INSTANCE.expire(AuthConstants.USER_ACCESS_CACHE + id, 0);
                return new RestResult(Status.SUCCESS, cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.INFO_DELETE_SUCCESS));
            } else {
                return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1596076550));
            }
        } finally {
            redisTemplate.delete(key);
        }


    }

    /**
     * 查询子用户详情
     *
     * @param id 用户sid
     *
     * @return {@code DescribeSubuserDetailResponse}
     */
    @ApiOperation("查询子用户详情")
    @GetMapping("/subuser/{userSid}")
    @Authorize(action = "iam:users:ListUsers")
    @AuthorizeBss(action = CH.CH0101)
    public RestResult<DescribeSubuserDetailResponse> querySubuserDetail(
            @PathVariable("userSid") @ApiParam(value = "用户id", type = "Long", required = true) Long id) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (ObjectUtils.isEmpty(authUserInfo)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_531007114));
        }
        QueryWrapper<cn.com.cloudstar.rightcloud.bss.module.access.bean.User> userQueryWrapper = new QueryWrapper<>();
        userQueryWrapper.lambda().eq(cn.com.cloudstar.rightcloud.bss.module.access.bean.User::getOrgSid, authUserInfo.getOrgSid());
        List<Long> userSids = userService.list(userQueryWrapper).stream()
                .map(cn.com.cloudstar.rightcloud.bss.module.access.bean.User::getUserSid)
                .collect(Collectors.toList());
        if (!userSids.contains(id)) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1105671400));
        }
        DescribeSubuserDetailResponse convert = BeanConvertUtil.convert(userService.selectByPrimaryKey(id),
                                                                        DescribeSubuserDetailResponse.class);
        convert.setGroups(
                BeanConvertUtil.convert(sysGroupService.selectByUserSid(id), DescribeUserGroupDetailResponse.class));
        List<DescribeSimplePolicy> describeSimplePolicies = userService.listPolicyForUser(id);
        if (describeSimplePolicies != null) {
            convert.setPolicies(
                    BeanConvertUtil.convert(describeSimplePolicies, DescribeSimplePolicyDetailResponse.class));
        }
        DesensitizationUtil.doDesensitization(convert);

        return new RestResult(convert);
    }

    /**
     * 编辑子用户
     *
     * @param id 用户sid
     * @param request 编辑子用户请求体
     *
     * @return {@code RestResult}
     */
    @DataPermission(resource = OperationResourceEnum.DELETE_SUB_USER, bizId = "#id")
    @ApiOperation("编辑子用户")
    @PutMapping("/subuser/{userSid}")
    @Authorize(action = "iam:users:ModifyUser")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'编辑子用户'", bizId = "#id", tagNameUs ="'Edit Child User'",
            resource = OperationResourceEnum.MODIFY_SUB_USER, param = "#request")
    @AuthorizeBss(action = CH.CH0101)
    public RestResult modifySubuser(
            @PathVariable("userSid") @ApiParam(value = "用户id", type = "Long", required = true) Long id,
            @RequestBody @Valid UpdateSubuserRequest request) {
        if (Objects.nonNull(request.getStartTime()) && Objects.nonNull(request.getEndTime())) {
            if (request.getStartTime().after(request.getEndTime())) {
                BizException.throwException(
                        cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.THE_END_TIME_SHOULD_BE_GREATER_THAN_THE_START_TIME));
            }
        }
        if (Objects.nonNull(request.getEndTime()) && request.getEndTime().before(new Date())) {
            BizException.throwException(cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.THE_END_TIME_SHOULD_BE_GREATER_THAN_THE_CURRENT_TIME));
        }
        cn.com.cloudstar.rightcloud.bss.module.access.bean.User user = userService.selectByPrimaryKey(id);
        if (Objects.isNull(user) || !UserType.PLATFORM_USER.equals(user.getUserType()) || Objects.isNull(user.getParentSid())) {
            BizException.throwException(cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        userService.updateSubuser(request, id);
        return new RestResult(RestResult.Status.SUCCESS, cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.INFO_UPDATE_SUCCESS));
    }


    /**
     * 获取密码配置
     *
     * @param request 请求
     *
     * @return {@code PasswordRuleResponse}
     */
    @ApiOperation("获取配置")
    @GetMapping("/password_config")
    @AuthorizeBss(action = AuthModule.COMMON.PUBLIC.C1.C107)
    public PasswordRuleResponse pswRule(@Valid DescribePasswordConfigRequest request) {
        PasswordRuleResponse response = new PasswordRuleResponse();
        List<PasswordConfigVO> rules = Lists.newArrayList();
        if ("rule".equals(request.getType())) {
            PasswordRuleEnum[] values = PasswordRuleEnum.values();
            for (PasswordRuleEnum passwordRuleEnum : values) {
                PasswordConfigVO passwordRuleVO = new PasswordConfigVO();
                passwordRuleVO.setKey(passwordRuleEnum.getKey());
                passwordRuleVO.setDesc(passwordRuleEnum.getDesc());
                rules.add(passwordRuleVO);
            }
        }

        if ("charactorType".equals(request.getType())) {
            for (CharatorTypeEnum charatorTypeEnum : CharatorTypeEnum.values()) {
                PasswordConfigVO passwordRuleVO = new PasswordConfigVO();
                passwordRuleVO.setKey(charatorTypeEnum.getType());
                passwordRuleVO.setDesc(charatorTypeEnum.getDesc());
                rules.add(passwordRuleVO);
            }
        }
        response.setRules(rules);
        return response;
    }

    /**
     * [INNER API] 获取密码配置
     *
     * @param type 类型
     *
     * @return {@code RestResult<PasswordRuleResponse>}
     */
    @RejectCall
    @ApiOperation("获取配置")
    @GetMapping("/password_config/feign")
    public RestResult<PasswordRuleResponse> pswRuleByFeign(@RequestParam("type") String type) {
        List<String> collect = Stream.of("rule", "charactorType").collect(Collectors.toList());
        if (!collect.contains(type)) {
            throw new BizException(MsgCd.PARAM_NOT_VALID_ERROR);
        }
        PasswordRuleResponse response = new PasswordRuleResponse();
        List<PasswordConfigVO> rules = Lists.newArrayList();
        if ("rule".equals(type)) {
            PasswordRuleEnum[] values = PasswordRuleEnum.values();
            for (PasswordRuleEnum passwordRuleEnum : values) {
                PasswordConfigVO passwordRuleVO = new PasswordConfigVO();
                passwordRuleVO.setKey(passwordRuleEnum.getKey());
                passwordRuleVO.setDesc(passwordRuleEnum.getDesc());
                rules.add(passwordRuleVO);
            }
        }

        if ("charactorType".equals(type)) {
            for (CharatorTypeEnum charatorTypeEnum : CharatorTypeEnum.values()) {
                PasswordConfigVO passwordRuleVO = new PasswordConfigVO();
                passwordRuleVO.setKey(charatorTypeEnum.getType());
                passwordRuleVO.setDesc(charatorTypeEnum.getDesc());
                rules.add(passwordRuleVO);
            }
        }
        response.setRules(rules);
        return new RestResult(response);
    }

    /**
     * 用户侧注册获取psw规则
     *
     * @param request 获取密码配置信息请求体
     *
     * @return {@code PasswordRuleResponse}
     */
    @ApiOperation("用户侧注册获取psw规则,获取配置")
    @GetMapping("/console/password_config")
    public PasswordRuleResponse pswRuleForConsole(@Valid DescribePasswordConfigRequest request) {
        PasswordRuleResponse response = new PasswordRuleResponse();
        List<PasswordConfigVO> rules = Lists.newArrayList();
        if ("rule".equals(request.getType())) {
            PasswordRuleEnum[] values = PasswordRuleEnum.values();
            for (PasswordRuleEnum passwordRuleEnum : values) {
                PasswordConfigVO passwordRuleVO = new PasswordConfigVO();
                passwordRuleVO.setKey(passwordRuleEnum.getKey());
                passwordRuleVO.setDesc(passwordRuleEnum.getDesc());
                rules.add(passwordRuleVO);
            }
        }

        if ("charactorType".equals(request.getType())) {
            for (CharatorTypeEnum charatorTypeEnum : CharatorTypeEnum.values()) {
                PasswordConfigVO passwordRuleVO = new PasswordConfigVO();
                passwordRuleVO.setKey(charatorTypeEnum.getType());
                passwordRuleVO.setDesc(charatorTypeEnum.getDesc());
                rules.add(passwordRuleVO);
            }
        }
        response.setRules(rules);
        return response;
    }

    /**
     * 获取用户下的密码策略
     *
     * @return {@code PasswordPolicyResponse}
     */
    @ApiOperation("获取用户下的密码策略")
    @GetMapping("/password_policy")
    @AuthorizeBss(action = AuthModule.COMMON.PUBLIC.C1.C106)
    public PasswordPolicyResponse getPasswordPolicy() {
        PasswordPolicyDTO policy = null;
        try {
            AuthUser authUser = RequestContextUtil.getAuthUserInfo();
            if (authUser == null) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1038086020));
            }
            policy = passwordPolicyService.getPasswordPolicyByOrgSid(authUser.getOrgSid());
        } catch (BizException e) {
            throw new BizException(USER_NOT_EXIST);
        }
        return BeanConvertUtil.convert(policy, PasswordPolicyResponse.class);
    }

    /**
     * 【Since v2.5.0】
     * @return
     */
    @ApiOperation("获取子用户下的密码策略")
    @GetMapping("/password_policy/subUser")
    @AuthorizeBss(action = AuthModule.COMMON.PUBLIC.C1.C106)
    public PasswordPolicyResponse getPasswordPolicySubUser() {
        PasswordPolicyDTO policy = null;
        try {
            AuthUser authUser = RequestContextUtil.getAuthUserInfo();
            if (authUser == null) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1038086020));
            }
            QueryWrapper qw = new QueryWrapper();
            qw.eq("user_sid",authUser.getUserSid());
            List<UserGroup> userGroups = userGroupMapper.selectList(qw);
            if(!CollectionUtils.isEmpty(userGroups)){
                userGroups = userGroups.stream().filter(u-> u.getGroupSid() == 1).collect(Collectors.toList());
                if(CollectionUtils.isEmpty(userGroups)){
                    throw new cn.com.cloudstar.rightcloud.oss.common.exception.BizException(cn.com.cloudstar.rightcloud.oss.common.constants.RestConst.BizError.PERMISSION_VERIFICATION_FAILED, "越权操作");
                }
            }
            policy = passwordPolicyService.getPasswordPolicyByOrgSidSubUser(authUser.getOrgSid());
        } catch (BizException e) {
            throw new BizException(USER_NOT_EXIST);
        }
        return BeanConvertUtil.convert(policy, PasswordPolicyResponse.class);
    }

    /**
     * [INNER API] 获取用户下的密码策略
     *
     * @return {@code PasswordPolicyResponse}
     */
    @RejectCall
    @ApiOperation("获取用户下的密码策略")
    @GetMapping("/password_policy/feign")
    public RestResult<PasswordPolicyResponse> getPasswordPolicyByFeign(PasswordPolicyRequest request) {
        User authUser = AuthUtil.getAuthUser();
        if (Objects.isNull(authUser) || Objects.isNull(authUser.getUserSid())) {
            throw new BizException(USER_NOT_EXIST);
        }
        Long orgSid = authUser.getOrgSid();
        if (StringUtils.isNotBlank(request.getOrgSid())) {
            if (!request.getOrgSid().matches("\\d+")) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_996771796));
            }

            orgSid = Long.valueOf(request.getOrgSid());
            Org org = orgService.selectRootOrg(orgSid);
            if (org == null) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_996771796));
            }
        }

        PasswordPolicyDTO policy = passwordPolicyService.getPasswordPolicyByOrgSid(orgSid);
        return new RestResult(BeanConvertUtil.convert(policy, PasswordPolicyResponse.class));
    }

    public static final String KEYCLOAK_USER_SKIP_2_FA = "keycloak.user.skip2FA";

    /**
     * 更新用户密码策略
     *
     * @param request 更新用户密码策略请求体
     *
     * @return {@code RestResult}
     */
    @ApiOperation("更新用户密码策略")
    @PutMapping("/password_policy")
    @Authorize(action = "iam:configs:CreateConfig")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'用户控制台访问控制设置'", tagNameUs ="'User Console Access Control Settings'",
            resource = OperationResourceEnum.UPDATE_PASSWORD_POLICY, param = "#request")
    @AuthorizeBss(action = CH.CH06)
    public RestResult updatePasswordPolicy(@RequestBody @Valid UpdatePasswordPolicyRequest request) {
        PasswordPolicy convert = BeanConvertUtil.convert(request, PasswordPolicy.class);
        StringBuffer sb = new StringBuffer();
        Set<String> collect = request.getCharactorType().stream().collect(Collectors.toSet());
        if (collect.size() < 3) {
            return new RestResult(RestConst.HttpConst.BadRequest, Status.FAILURE ,WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1348714857),WebUtil.getMessage(MsgCd.ERR_MSG_BSS_586078363));
        }


        long count = collect.stream().filter(e ->
                                    !e.equals(CharatorTypeEnum.LOWERCASE.getType()) &&
                                    !e.equals(CharatorTypeEnum.UPPERCASE.getType()) &&
                                    !e.equals(CharatorTypeEnum.NUMBER.getType()) &&
                                    !e.equals(CharatorTypeEnum.SPECIAL_CHARACTOR.getType())
                         ).count();
        if (count > 0) {
            return new RestResult(Status.FAILURE ,WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1145397087));
        }
        User authUser = AuthUtil.getAuthUser();

        Org org = orgService.selectRootOrg(authUser.getOrgSid());
        if (!request.getSkip2FA() && "0".equals(PropertiesUtil.getProperty("sms.enable"))) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_823320488));
        }
        if (!request.getSkip2FA() && "false".equals(PropertiesUtil.getProperty("keycloak.SmsConfig"))) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1025000482));
        }
        if (!org.getSkip2FA().equals(request.getSkip2FA())) {
            userAuthService.disable2FA(request.getSkip2FA());
        }

        convert.setRule(JSONArray.toJSONString(collect));
        convert.setRuledOut(JSONArray.toJSONString(request.getRuleOut().stream().collect(Collectors.toSet())));

        if (Objects.isNull(authUser) || Objects.isNull(authUser.getUserSid())) {
            throw new BizException(USER_NOT_EXIST);
        }
        convert.setUserSid(authUser.getUserSid());
        convert.setOrgSid(authUser.getOrgSid());
        Criteria criteria = new Criteria("orgSid", authUser.getOrgSid());
        List<PasswordPolicy> passwordPolicies = passwordPolicyService.selectByParams(criteria);
        if (CollectionUtils.isEmpty(passwordPolicies)) {
            cn.com.cloudstar.rightcloud.bss.common.util.WebUserUtil.prepareInsertParams(convert);
            // 添加默认的密码修改策略 xw_add 登录状态开关默认true
            convert.setLoginfailureEnable(true);
            convert.setLoginfailureCount(3);
            convert.setPwdExpireTimeValidity(true);
            convert.setPwdExpireTime(30L);
            convert.setAccountValidity(true);
            convert.setExpireTime(30);
            passwordPolicyService.insertSelective(convert);
        } else {
            if (StringUtil.isNullOrEmpty(passwordPolicies.get(0).getPwdExpireTimeValidity())) {
                passwordPolicies.get(0).setPwdExpireTimeValidity(PasswordUtil.DEFAULT_CREDENTIAL_EXPIRE_TIME_VALIDITY);
            }
            if (StringUtil.isNullOrEmpty(passwordPolicies.get(0).getPwdExpireTime())) {
                passwordPolicies.get(0).setPwdExpireTime(PasswordUtil.DEFAULT_CREDENTIAL_EXPIRE_TIME);
            }
            if (StringUtil.isNullOrEmpty(passwordPolicies.get(0).getPwdLeastUsedDay())) {
                passwordPolicies.get(0).setPwdLeastUsedDay(PasswordUtil.DEFAULT_CREDENTIAL_LEAST_USED_DAY);
            }
            if (StringUtil.isNullOrEmpty(passwordPolicies.get(0).getPwdRepeatNum())) {
                passwordPolicies.get(0).setPwdRepeatNum(PasswordUtil.DEFAULT_CREDENTIAL_REPEAT_NUM);
            }

            WebUserUtil.prepareUpdateParams(convert);
            if(Boolean.compare(passwordPolicies.get(0).getPwdExpireTimeValidity(),convert.getPwdExpireTimeValidity()) != 0
            || !passwordPolicies.get(0).getPwdExpireTime().equals(convert.getPwdExpireTime())){
                sb.append("密码有效期配置修改");
            }
            if(Boolean.compare(passwordPolicies.get(0).getAccountValidity(),convert.getAccountValidity()) != 0
            || !passwordPolicies.get(0).getExpireTime().equals(convert.getExpireTime())){
                if(sb.length() > 0){
                    sb.append("&账号有效期配置修改");
                }else{
                    sb.append("账号有效期配置修改");
                }

            }
            if(Boolean.compare(passwordPolicies.get(0).getLoginfailureEnable(),convert.getLoginfailureEnable()) != 0
            || !passwordPolicies.get(0).getLoginfailureCount().equals(convert.getLoginfailureCount())){
                if(sb.length() > 0){
                    sb.append("&登录失败锁定配置修改");
                }else{
                    sb.append("登录失败锁定配置修改");
                }
            }
            boolean b = !passwordPolicies.get(0).getMinLength().equals(convert.getMinLength())
                    || !passwordPolicies.get(0).getCharactorLimit().equals(convert.getCharactorLimit())
                    || !passwordPolicies.get(0).getRule().equals(convert.getRule())
                    || !passwordPolicies.get(0).getRuledOut().equals(convert.getRuledOut());

            if(b || !passwordPolicies.get(0).getPwdLeastUsedDay().equals(convert.getPwdLeastUsedDay())
                    || !passwordPolicies.get(0).getPwdRepeatNum().equals(convert.getPwdRepeatNum())){
                if(sb.length() > 0){
                    sb.append("&登录密码策略修改");
                }else{
                    sb.append("登录密码策略修改");
                }

            }
            OperationLogMdcUtil.saveContent(sb.toString());
            passwordPolicyService.updateByParamsSelective(convert, criteria);
        }


        return new RestResult(RestResult.Status.SUCCESS, cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.INFO_UPDATE_SUCCESS));
    }

    /**
     * 查询用户组
     *
     * @param request 查询用户组请求体
     *
     * @return {@code List<DescribeUserGroupSimpleResponse>}
     */
    @GetMapping("/user_group")
    @ApiOperation("查询用户组")
    @Authorize(action = "iam:usergroups:ListUserGroups")
    @AuthorizeBss(action = CH.CH01)
    public List<DescribeUserGroupSimpleResponse> queryUserGroup(DescribeUserGroupRequest request) {
        /* 将请求转换为查询信息类*/
        Criteria criteria = new Criteria();
        criteria.setConditionObject(request);
        /* 获取当前用户*/
        User authUser = AuthUtil.getAuthUser();
        if (Objects.isNull(authUser) || Objects.isNull(authUser.getUserSid())) {
            throw new BizException(USER_NOT_EXIST);
        }
        /* 将用户信息添加进查询类*/
        if (!request.isIncludeDefaultGroup()) {
            criteria.put("orgSid", authUser.getOrgSid());
        } else {
            criteria.put("org", authUser.getOrgSid());
        }
        /* 获取用户组*/
        List<DescribeUserGroupResponse> convert = BeanConvertUtil.convert(
                sysGroupService.selectGroup(criteria, authUser.getOrgSid()), DescribeUserGroupResponse.class);
        if (!Strings.isNullOrEmpty(request.getType()) && "tree".equals(request.getType())) {
            convert = SimpleTreeBuilder.treeBuild(convert);
        }
        return BeanConvertUtil.convert(convert, DescribeUserGroupSimpleResponse.class);
    }

    /**
     * 查询用户组(访问控制-用户组)
     *
     * @param request 查询用户组请求体
     *
     * @return {@code List<DescribeUserGroupResponse>}
     */
    @GetMapping("/user_group/user")
    @ApiOperation("查询用户组")
    @AuthorizeBss(action = CH.CH02)
    public List<DescribeUserGroupSimpleResponse> queryUserGroupByUser(@Valid DescribeUserGroupRequest request) {
        /* 获取当前用户*/
        cn.com.cloudstar.rightcloud.bss.common.pojo.User authUser = AuthUtil.getAuthUser();
        if (authUser == null) {
            throw new BizException(HttpConst.Unauthorized.getType());
        }

        return queryUserGroup(request);
    }

    /**
     * 查询用户组(访问控制-权限配置-关联用户组)
     *
     * @param request 查询用户组请求体
     *
     * @return {@code List<DescribeUserGroupResponse>}
     */
    @GetMapping("/user_group/authority")
    @ApiOperation("查询用户组")
    @Authorize(action = "iam:usergroups:ListUserGroups")
    @AuthorizeBss(action = CH.CH0503)
    public List<DescribeUserGroupSimpleResponse> queryUserGroupAuthority(DescribeUserGroupRequest request) {
        return queryUserGroup(request);
    }

    /**
     * [INNER API] 查询用户组
     *
     * @param request 查询用户组请求体
     *
     * @return {@code List<DescribeUserGroupResponse>}
     */
    @RejectCall
    @GetMapping("/user_group/feign")
    @ApiOperation("查询用户组")
    public List<DescribeUserGroupSimpleResponse> queryUserGroupByFeign(@Valid DescribeUserGroupRequest request) {
        return queryUserGroup(request);
    }

    /**
     * 创建用户组
     *
     * @param request 创建用户组请求体
     *
     * @return {@code RestResult}
     */
    @PostMapping("/user_group/user")
    @ApiOperation("创建用户组")
    @Authorize(action = "iam:usergroups:CreateUserGroup")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "#request.groupName",
            resource = OperationResourceEnum.CREATE_USER_GROUP)
    @Idempotent
    @AuthorizeBss(action = CH.CH0204)
    public RestResult createUserGroup1(@RequestBody @Valid CreateUserGroupRequest request) {
        return createUserGroup(request);
    }

    /**
     * 创建用户组
     *
     * @param request 创建用户组请求体
     *
     * @return {@code RestResult}
     */
    @PostMapping("/user_group")
    @ApiOperation("创建用户组")
    @Authorize(action = "iam:usergroups:CreateUserGroup")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "#request.groupName",
            resource = OperationResourceEnum.CREATE_USER_GROUP)
    @Idempotent
    @AuthorizeBss(action = CH.CH0204)
    public RestResult createUserGroup(@RequestBody @Valid CreateUserGroupRequest request) {
        User authUser = AuthUtil.getAuthUser();
        if (Objects.isNull(authUser) || Objects.isNull(authUser.getUserSid())) {
            throw new BizException(USER_NOT_EXIST);
        }
        Long groupId = sysGroupService.createUserGroup(request, authUser);
        return new RestResult(RestResult.Status.SUCCESS, cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.INFO_UPDATE_SUCCESS), groupId);
    }

    /**
     * 用户组关联用户
     *
     * @param request 用户组批量配置用户请求体
     *
     * @return {@code RestResult}
     */
    @ApiOperation("用户组关联用户")
    @PutMapping("/user_group/add_user")
    @Authorize(action = "iam:usergroups:OperateGroupFunc")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'用户组'", resource = OperationResourceEnum.MODIFY_USER_IN_GROUP, tagNameUs ="'User Group'",
            param = "#request")
    @Transactional
    @PermissionToModifyCheck
    @AuthorizeBss(action = CH.CH0205)
    public RestResult modifyUserInGroup(@RequestBody @Valid MoveUserToGroupRequest request) {
        authorityJudgment(request);
        userGroupService.multiAddUser(request.getUserSids(), request.getGroupIds());
        return new RestResult(RestResult.Status.SUCCESS, cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.INFO_UPDATE_SUCCESS));
    }


    /**
     * 用户关联用户组
     *
     * @param request 用户组批量配置用户请求体
     *
     * @return {@code RestResult}
     */
    @ApiOperation("用户关联用户组")
    @PutMapping("/user/join_group")
    @Authorize(action = "iam:usergroups:OperateGroupFunc")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'用户组名称'", tagNameUs ="'User Group Name'",
            resource = OperationResourceEnum.JOIN_GROUP, param = "#request")
    @Transactional
    @PermissionToModifyCheck
    @AuthorizeBss(action = CH.CH0205)
    public RestResult joinGroup(@RequestBody @Valid MoveUserToGroupRequest request) {
        userGroupService.joinGroup(request.getUserSids(), request.getGroupIds());
        return new RestResult(RestResult.Status.SUCCESS, cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.INFO_UPDATE_SUCCESS));
    }

    /**
     * 删除用户组
     *
     * @param id 用户组sid
     *
     * @return {@code RestResult}
     */
    @ApiOperation("删除用户组")
    @DeleteMapping("/user_group/{group_sid}")
    @Authorize(action = "iam:usergroups:DeleteUserGroup")
    @OperationLog(type = OperationTypeEnum.DELETE, tagName = "'删除用户组'", tagNameUs ="'Delete User Group'",
            resource = OperationResourceEnum.DELETE_USER_GROUP, bizId = "#id")
    @Transactional
    @PermissionToModifyCheck
    @AuthorizeBss(action = CH.CH0203)
    public RestResult deleteUserGroup(
            @PathVariable("group_sid") @ApiParam(value = "用户组id", type = "Long", required = true) Long id) {
        sysGroupService.deleteGroup(id);
        return new RestResult(RestResult.Status.SUCCESS, cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.INFO_DELETE_SUCCESS));
    }

    /**
     * 查询用户组详情
     *
     * @param id 用户组id
     *
     * @return {@code DescribeGroupDetailResponse}
     */

    @ApiOperation("查询用户组详情")
    @GetMapping("/user_group/{group_sid}")
    @AuthorizeBss(action = CH.CH0201)
    @Authorize(action = "iam:usergroups:ListUserGroups")
    @DataPermission(resource = OperationResourceEnum.CREATE_USER_GROUP, bizId = "#id")
    public DescribeGroupDetailResponse queryGroupDetail(
            @PathVariable("group_sid") @ApiParam(value = "用户组id", type = "Long", required = true) Long id) {
        SysGroup sysGroup = sysGroupService.selectByPrimaryKey(id);
        if (Objects.isNull(sysGroup)) {
            throw new BizException(GROUP_NOT_EXIST);
        }
        DescribeGroupDetailResponse convert = BeanConvertUtil.convert(sysGroup, DescribeGroupDetailResponse.class);

        Criteria criteria = new Criteria("groupSid", id);
        criteria.put("orgSid", AuthUtil.getAuthUser().getOrgSid());
        List<SubuserVO> subUserVos = BeanConvertUtil.convert(userService.selectByParams(criteria), SubuserVO.class);
        DesensitizationUtil.desensitization(subUserVos);
        convert.setSubusers(subUserVos);
        List<DescribeSimplePolicy> describeSimplePolicies = sysGroupService.selectPolicy4Group(id);
        convert.setPolicies(BeanConvertUtil.convert(describeSimplePolicies, DescribeSimplePolicyDetailResponse.class));
        return convert;
    }

    /**
     * 编辑用户组
     *
     * @param id 用户组id
     * @param request 编辑用户组请求体
     *
     * @return {@code RestResult}
     */
    @ApiOperation("编辑用户组")
    @PutMapping("/user_group/{group_sid}")
    @Authorize(action = "iam:usergroups:ModifyUserGroup")
    @DataPermission(resource = OperationResourceEnum.CREATE_USER_GROUP, bizId = "#id")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "#request.groupName", bizId = "#id", resource = OperationResourceEnum.UPDATE_USER_GROUP)
    @AuthorizeBss(action = CH.CH0202)
    public RestResult modifyGroup(
            @PathVariable("group_sid") @NotNull @ApiParam(value = "用户组id", type = "Long", required = true) Long id,
            @RequestBody @Valid UpdateGroupRequest request) {
        SysGroup convert = BeanConvertUtil.convert(request, SysGroup.class);
        SysGroup sysGroup = sysGroupService.selectByPrimaryKey(id);
        if (Objects.isNull(sysGroup)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1746407428));
        }
        if (sysGroup.isSysDefault()) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_120386593));
        }
        if (Objects.isNull(sysGroup.getParentId()) && Objects.nonNull(request.getParentId())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_557111211));
        }
        if (Objects.nonNull(request.getParentId())) {
            convert.setTreePath("/" + request.getParentId() + "/");
            convert.setParentId(request.getParentId());
        }
        convert.setGroupSid(id);
        sysGroupService.updateByPrimaryKeySelective(convert);
        return new RestResult(RestResult.Status.SUCCESS, cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.INFO_UPDATE_SUCCESS));
    }

    /**
     * 从用户组中移除用户
     *
     * @param request 移除用户请求体
     *
     * @return {@code RestResult}
     */
    @ApiOperation("从用户组中移除用户")
    @DeleteMapping("/user_group/remove_user")
    @Authorize(action = "iam:usergroups:OperateGroupFunc")
    @Transactional
    @PermissionToModifyCheck
    @OperationLog(type = OperationTypeEnum.DELETE, tagName = "'用户组名称'",param = "#request", tagNameUs ="'User Group Name'",
            resource = OperationResourceEnum.REMOVE_SUBUSER_FROM_GROUP)
    @AuthorizeBss(action = CH.CH0207)
    public RestResult removeSubuserFromGroup(@RequestBody @Valid RemoveSubuserRequest request) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (ObjectUtils.isEmpty(authUserInfo)){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_531007114));
        }
        if (request.getUserSids().contains(authUserInfo.getUserSid())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1719730526));
        }
        userGroupService.removeUsers(request.getUserSids(), request.getGroupSids());

        if (CollectionUtil.isNotEmpty(request.getUserSids())) {
            request.getUserSids().forEach(userSid -> {
                // 清除权限缓存
                JedisUtil.INSTANCE.expire(AuthConstants.USER_ACCESS_CACHE + userSid, 0);
            });
        }
        return new RestResult(RestResult.Status.SUCCESS, cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.INFO_DELETE_SUCCESS));
    }

    /**
     * 从用户中移除用户组
     *
     * @param request 移除用户请求体
     *
     * @return {@code RestResult}
     */
    @ApiOperation("从用户中移除用户组")
    @DeleteMapping("/user_group/remove_userGroup")
    @Authorize(action = "iam:usergroups:OperateGroupFunc")
    @Transactional
    @PermissionToModifyCheck
    @DataPermission(resource = OperationResourceEnum.REMOVE_USER_PERMISSIONS, bizId = "#request.userSids")
    @OperationLog(type = OperationTypeEnum.DELETE, tagName = "'用户组名称'", param = "#request", tagNameUs ="'User Group Name'",
            resource = OperationResourceEnum.REMOVE_SUBUSER_FROM_GROUP)
    @AuthorizeBss(action = CH.CH0111)
    public RestResult removeSubuserGroup(@RequestBody @Valid RemoveSubuserRequest request) {
        return removeSubuserFromGroup(request);
    }

    /**
     * 移除用户权限
     *
     * @param request 移除用户权限请求体
     *
     * @return {@code RestResult}
     */
    @ApiOperation("移除用户权限")
    @DeleteMapping("/subuser/policy")
    @Authorize(action = "iam:usergroups:OperateGroupFunc")
    @DataPermission(resource = OperationResourceEnum.REMOVE_USER_PERMISSIONS, bizId = "#request.userSid")
    @PermissionToModifyCheck
    @Transactional
    @AuthorizeBss(action = CH.CH0208)
    @OperationLog(type = OperationTypeEnum.DELETE, tagName = "'移除用户权限'", param = "#request", tagNameUs ="'Remove user privileges'",
            resource = OperationResourceEnum.REMOVE_USER_PERMISSIONS)
    public RestResult removePolicy4User(@RequestBody @Valid DeletePolicy4UserRequest request) {
        List<Long> policySids = request.getPolicies();
        Long userSid = request.getUserSid();
        QueryWrapper<PolicyUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_sid", userSid);
        queryWrapper.in("policy_sid", policySids);
        boolean remove = policyUserService.remove(queryWrapper);
        // 清除权限缓存
        JedisUtil.INSTANCE.expire(AuthConstants.USER_ACCESS_CACHE + userSid, 0);
        if (remove){
            return new RestResult(RestResult.Status.SUCCESS, cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.INFO_DELETE_SUCCESS));
        }else{
            return new RestResult(Status.FAILURE, cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.ERROR_DELETE_FAILURE));
        }

    }


    /**
     * [INNER API] 同步资源到ldap
     *
     * @param orgSid 组织sid
     *
     * @return {@code RestResult}
     */
    @ApiOperation("同步资源到ldap")
    @RejectCall
    @PutMapping("/synHpcToLdap")
    public RestResult synHpcToLdap(@RequestParam("orgSid") Long orgSid) {
        log.info("同步资源到ldap:{}", String.valueOf(orgSid).replaceAll("[\r\n]", ""));
        LdapSyncRequest ldapSyncRequest = new LdapSyncRequest();
        ldapSyncRequest.setOrgId(orgSid);
        ldapUserService.synHPCTOLdap(ldapSyncRequest);
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_UPDATE_SUCCESS));
    }
        /**
     * [INNER API] 激活CCP用户
     *
     *
     * @return {@code RestResult}
     */
    @ApiOperation("激活CCP用户")
    @RejectCall
    @PutMapping("/ccpUserActive")
    public RestResult ccpUserActive(@RequestBody LdapSyncRequest ldapSyncRequest) {
        ldapUserService.checkUserAndCcpActive(ldapSyncRequest);
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_UPDATE_SUCCESS));
    }


    @ApiOperation("使用request对象，同步资源到ldap")
    @PutMapping("/synHpcToLdap/LdapSyncRequest")
    public RestResult synHpcToLdapByLdapSyncRequest(@RequestBody LdapSyncRequest ldapSyncRequest) {
        log.info("同步资源到ldap:{}", ldapSyncRequest.getOrgId());
        if (ldapSyncRequest.getIsAsync()){
            ldapUserService.synHpcToLdapAsync(ldapSyncRequest);
        }else {
            ldapUserService.synHPCTOLdap(ldapSyncRequest);
        }
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_UPDATE_SUCCESS));
    }

    @ApiOperation("同步指定businessCategory资源到ldap")
    @PutMapping("/synHpcToLdap/synHpcToLdapForHPC")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'同步资源到ldap'", param = "#request",
            resource = OperationResourceEnum.SYNCHRONIZE_LDAP_RESOURCES)
    public RestResult synHpcToLdapForHPC(@RequestParam("orgSid") Long orgSid, @RequestParam("businessCategory") String businessCategory) {
        log.info("同步资源到ldap:{}，businessCategory：{}", orgSid,businessCategory);
        LdapSyncRequest ldapSyncRequest = new LdapSyncRequest();
        ldapSyncRequest.setOrgId(orgSid);
        ldapSyncRequest.setBusinessCategoryList(Collections.singletonList(businessCategory));
        ldapUserService.synHPCTOLdap(ldapSyncRequest);
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_UPDATE_SUCCESS));
    }


    /**
     * [INNER API] 同步资源到ldap
     *
     * @param orgSid 组织sid
     *
     * @return {@code RestResult}
     */
    @ApiOperation("同步资源到ldap")
    @RejectCall
    @PutMapping("/synHpcToLdap/synchronization")
    public RestResult synHpcToLdapSynchronization(@RequestParam("orgSid") Long orgSid) {

        return new RestResult(RestResult.Status.SUCCESS, cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.INFO_UPDATE_SUCCESS));
    }

    /**
     * [INNER API] 移除断言关联资源
     *
     * @param deletePolicyResourceRequest 移除权限关联资源请求体
     *
     * @return {@code RestResult}
     */
    @ApiOperation("移除断言关联资源")
    @RejectCall
    @PutMapping("/removeResourceWithOutCheck")
    @Transactional
    @PermissionToModifyCheck(false)
    @OperationLog(type = OperationTypeEnum.DELETE, tagName = "#deletePolicyResourceRequest.type", bizId = "#deletePolicyResourceRequest.resourceIds",
            resource = OperationResourceEnum.REMOVE_RESOURCE_WITHOUT_CHECK)
    public RestResult removeResourceWithOutCheck(
            @RequestBody @Valid DeletePolicyResourceRequest deletePolicyResourceRequest) {
        policyAssertionService.removeResource(deletePolicyResourceRequest.getType(),
                                              deletePolicyResourceRequest.getOrgSid(),
                                              deletePolicyResourceRequest.getResourceIds());
        return new RestResult(RestResult.Status.SUCCESS, cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.INFO_UPDATE_SUCCESS));
    }

    /**
     * 确认隐私签名
     *
     * @param confirmPrivacySignRequest 确认私隐签名请求
     * @return {@link RestResult}
     */
    @ApiOperation(httpMethod = "POST", value = " 隐私声明确认")
    @PostMapping("/privacy/confirm")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'确认隐私声明'", resource = OperationResourceEnum.PRIVACY_SIGN_CONFIRM, tagNameUs ="'Confirm Privacy Statement'")
    @Encrypt
    public RestResult confirmPrivacySign(@RequestBody @Valid ConfirmPrivacySignRequest confirmPrivacySignRequest) {
        Boolean isSuccess = false;
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo(
                cn.com.cloudstar.rightcloud.common.util.WebUtil.getRequest());
        AssertUtil.requireNonBlank(authUserInfo, "获取当前登录用户失败");
        if (!ONE.equals(authUserInfo.getStatus())) {
            throw new cn.com.cloudstar.rightcloud.oss.common.exception.BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2043115026));
        }
        cn.com.cloudstar.rightcloud.bss.module.access.bean.User currentUser = this.sysUserService.selectByPrimaryKey(
                authUserInfo.getUserSid());
        if (PrivacyStatusEnum.DISAGREE.getProductType()
                                      .equalsIgnoreCase(confirmPrivacySignRequest.getPrivacyStatus())) {
            currentUser.setPolicyAgreeSign(Integer.valueOf(PrivacyStatusEnum.DISAGREE.getProductType()));
            currentUser.setPolicyAgreeTime(new Date());
            //加入隐私声明协议记录
            UserPrivacySign userPrivacySign = new UserPrivacySign();
            userPrivacySign.setPrivacyStatus(PrivacyStatusEnum.DISAGREE.getProductName());
            userPrivacySign.setUserSid(authUserInfo.getUserSid());
            userPrivacySign.setPrivacyConfirmTime(new Date());
            userPrivacySign.setCreatedDt(new Date());
            userPrivacySign.setCreatedBy(authUserInfo.getAccount());
            userPrivacySignMapper.insertSelective(userPrivacySign);
            this.sysUserService.updateByPrimaryKeySelective(currentUser);
            isSuccess = true;
        } else if (PrivacyStatusEnum.AGREE.getProductType()
                                          .equalsIgnoreCase(confirmPrivacySignRequest.getPrivacyStatus())) {
            currentUser.setPolicyAgreeSign(Integer.valueOf(PrivacyStatusEnum.AGREE.getProductType()));
            currentUser.setPolicyAgreeTime(new Date());
            UserPrivacySign userPrivacySign = new UserPrivacySign();
            userPrivacySign.setPrivacyStatus(PrivacyStatusEnum.AGREE.getProductName());
            userPrivacySign.setUserSid(authUserInfo.getUserSid());
            userPrivacySign.setPrivacyConfirmTime(new Date());
            userPrivacySign.setCreatedDt(new Date());
            userPrivacySign.setCreatedBy(authUserInfo.getAccount());
            userPrivacySignMapper.insertSelective(userPrivacySign);
            this.sysUserService.updateByPrimaryKeySelective(currentUser);
            isSuccess = true;
        } else {
            throw new cn.com.cloudstar.rightcloud.oss.common.exception.BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_664511972));
        }

        if (isSuccess) {
            return new RestResult(RestResult.Status.SUCCESS, cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.INFO_UPDATE_SUCCESS));
        } else {
            return new RestResult(RestResult.Status.FAILURE, cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.ERROR_OPERATE_FAILURE));
        }
    }

    /**
     * 获取隐私声明记录
     */
    @ApiOperation(httpMethod = "POST", value = " 是否确认隐私声明")
    @PostMapping("/privacy/isConfirm")
    @Encrypt
    public Boolean isConfirmPrivacySign() {
        Boolean confirmFlag = false;
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo(
                cn.com.cloudstar.rightcloud.common.util.WebUtil.getRequest());
        AssertUtil.requireNonBlank(authUserInfo, "获取当前登录用户失败");
        if (!ONE.equals(authUserInfo.getStatus())) {
            throw new cn.com.cloudstar.rightcloud.oss.common.exception.BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2043115026));
        }
        //隐私声明记录列表
        List<UserPrivacySign> userPrivacySignList = userPrivacySignMapper.selectByPrimaryKey(authUserInfo.getUserSid());
        if (CollectionUtil.isNotEmpty(userPrivacySignList) && userPrivacySignList.size() > 0) {
            if (PrivacyStatusEnum.AGREE.getProductName()
                                       .equalsIgnoreCase(userPrivacySignList.get(0).getPrivacyStatus())) {
                confirmFlag = true;
            }
        }

        return confirmFlag;
    }


    /**
     * 移除用户组权限
     *
     * @param request 删除用户组权限关联请求体
     *
     * @return {@code RestResult}
     */
    @ApiOperation("移除用户组权限")
    @DeleteMapping("/user_group/policy")
    @Authorize(action = "iam:usergroups:OperateGroupFunc")
    @Transactional
    @PermissionToModifyCheck
    @OperationLog(type = OperationTypeEnum.DELETE, tagName = "'用户组名称'", param = "#request", resource = OperationResourceEnum.REMOVE_POLICY_FOR_GROUP, tagNameUs ="'User Group Name'")
    @AuthorizeBss(action = CH.CH0206)
    public RestResult removePolicy4Group(@RequestBody @Valid DeletePolicy4GroupRequest request) {
        List<Long> ids = sysGroupService.filter4DefaultGroup(Lists.newArrayList(request.getGroupSid()));
        if (CollectionUtils.isEmpty(ids)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1279737620));
        }
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();

        SysGroup sysGroup = sysGroupService.selectByPrimaryKey(request.getGroupSid());
        if (sysGroup == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1003759958));
        }

        Org org = orgService.selectRootOrg(authUserInfo.getOrgSid());
        policyService.checkGroup(Arrays.asList(request.getGroupSid()), org.getOrgSid());
        QueryWrapper<UserGroup> userGroupQuery = new QueryWrapper<>();
        userGroupQuery.lambda().eq(UserGroup::getGroupSid, request.getGroupSid());
        List<UserGroup> userGroups = userGroupService.list(userGroupQuery);
        QueryWrapper<PolicyGroup> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("group_sid", request.getGroupSid());
        queryWrapper.in("policy_sid", request.getPolicies());
        boolean remove = policyGroupService.remove(queryWrapper);
        if(!remove){
            return new RestResult(Status.FAILURE, cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.ERROR_DELETE_FAILURE));
        }
        if (CollectionUtil.isNotEmpty(userGroups)) {
            userGroups.forEach(userGroup -> {
                // 清除权限缓存
                JedisUtil.INSTANCE.expire(AuthConstants.USER_ACCESS_CACHE + userGroup.getUserSid(), 0);
            });
        }
        //这里通过修改触发国密mac更新
        if (StrUtil.isBlank(sysGroup.getGroupPolicys())) {
            return new RestResult(RestResult.Status.SUCCESS, cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.INFO_DELETE_SUCCESS));
        }
        List<Long> collect = Arrays.stream(sysGroup.getGroupPolicys().split(",")).mapToLong(Long::valueOf).boxed().collect(Collectors.toList());
        for (Long policy : request.getPolicies()) {
            collect.remove(policy);
        }
        sysGroup.setGroupPolicys(collect.stream().map(String::valueOf).collect(Collectors.joining(",")));
        sysGroup.setSkipCCSPHandle(true);
        sysGroupService.updateById(sysGroup);
        return new RestResult(RestResult.Status.SUCCESS, cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.INFO_DELETE_SUCCESS));
    }

    /**
     * 校核密码是否符合规则
     *
     * @param request 校核密码规则请求体
     *
     * @return {@code RestResult}
     */
    @ApiOperation("创建用户校核密码是否符合规则")
    @PostMapping("/password_policy/validCreateUser")
    @Encrypt
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "'校核密码'", bizId = "#request.orgSid", resource = OperationResourceEnum.VALID_PASSWORD_POLICY, tagNameUs ="'Check password'")
    @AuthorizeBss(action = CH.CH0109 + "," + CH.CH0108 + "," + CH.CH0102)
    public RestResult validPasswordPolicyCreateUser(@RequestBody ValidPasswordPolicyRequest request) {
        User authUser = AuthUtil.getAuthUser();
        if (ObjectUtils.isEmpty(authUser)) {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1473130476));
        }
        //密码规则
        PasswordPolicyDTO policy = passwordPolicyService.getPasswordPolicyByOrgSid(authUser.getOrgSid());
        policy.setPwdRepeatNum(0L);
        //校验密码
        String error = userService.validPasswordPolicyToError(policy, request.getPassword(), null,
                                                              request.getFirstLogin());
        if (!ObjectUtils.isEmpty(error)) {
            return new RestResult(Status.FAILURE, error);
        }
        return new RestResult(Status.SUCCESS);
    }

    /**
     * 校核密码是否符合规则
     *
     * @param validPasswordPolicyRequest 有效密码政策请求
     * @return {@link RestResult}
     */
    @ApiOperation("校核密码是否符合规则")
    @PostMapping("/password_policy/valid")
    @Encrypt
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "'校核密码'",resource = OperationResourceEnum.VALID_PASSWORD_POLICY, tagNameUs ="'Check password'")
    @AuthorizeBss(action = C1.C106 + "," + B1.B104 + "," + Z1.Z103)
    public RestResult validPasswordPolicy(@RequestBody ValidPasswordPolicyRequest validPasswordPolicyRequest) {
        PasswordPolicyDTO policy;
        String error = null;
        User authUser = AuthUtil.getAuthUser();
        if (ObjectUtils.isEmpty(authUser)) {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1473130476));
        }

        // 修改子用户密码，获取租户的密码规则
        if (!ObjectUtils.isEmpty(validPasswordPolicyRequest.getUserSids())) {
            for (Long userSid : validPasswordPolicyRequest.getUserSids()) {
                cn.com.cloudstar.rightcloud.bss.module.access.bean.User user =
                        userService.selectByPrimaryKey(userSid);
                if (ObjectUtils.isEmpty(user)) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1401732832));
                }
                policy = passwordPolicyService.getPasswordPolicy(user.getOrgSid(), user.getParentSid());
                //校验密码
                error = userService.validPasswordPolicyToError(policy, validPasswordPolicyRequest.getPassword(), userSid, false);
                if (!ObjectUtils.isEmpty(error)) {
                    return new RestResult(Status.FAILURE, error);
                }
            }
        } else {
            policy = passwordPolicyService.getPasswordPolicy(authUser.getOrgSid(), authUser.getParentSid());
            //校验密码
            error = userService.validPasswordPolicyToError(policy, validPasswordPolicyRequest.getPassword(), authUser.getUserSid(), validPasswordPolicyRequest.getFirstLogin());
        }

        if (!ObjectUtils.isEmpty(error)) {
            return new RestResult(Status.FAILURE, error);
        }
        return new RestResult(Status.SUCCESS);
    }


    /**
     * 租户侧-重置子用户密码接口校验密码规则
     *
     * @param request 校核密码规则请求体
     *
     * @return {@code RestResult}
     */
    @ApiOperation("校核子用户密码是否符合规则")
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "'校核密码'", bizId = "#request.orgSid", resource = OperationResourceEnum.VALID_PASSWORD_POLICY, tagNameUs ="'Check password'")
    public RestResult validSubUserPasswordPolicy(@RequestBody @Valid ValidSubUserPasswordPolicyRequest request) {
        User authUser = AuthUtil.getAuthUser();
        if (ObjectUtils.isEmpty(authUser)) {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1473130476));
        }
        // 判断选定用户是否是当前用户的子用户
        User subUser = userMapper.selectByPrimaryKey(request.getUserSid());
        if (!ObjectUtils.isEmpty(subUser) && !authUser.getUserSid().equals(subUser.getParentSid())) {
            return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1672206086));
        }
        //密码规则
        PasswordPolicyDTO policy = passwordPolicyService.getPasswordPolicyByOrgSid(subUser.getOrgSid());
        //校验密码
        String error = userService.validPasswordPolicyToError(policy, request.getPassword(), request.getUserSid(),
                                                              request.getFirstLogin());
        if (!ObjectUtils.isEmpty(error)) {
            return new RestResult(Status.FAILURE, error);
        }
        return new RestResult(Status.SUCCESS);
    }

    /**
     * 同步Iam
     * 【Since v2.5.0】
     * @return {@code RestResult}
     */
//    @ApiOperation("同步Iam")
//    @PutMapping("/sync")
//    @Authorize(action = "iam:users:OperateUserFunc")
//    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'同步Iam'", resource = OperationResourceEnum.SYSC_IAM, tagNameUs ="'Synchronization'")
//    @AuthorizeBss(action = CH.CH0106)
//    public RestResult syncIam() {
//        iamSyncService.syncIamUser();
//        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS));
//    }

    /**
     * 解除用户锁定状态
     *【Since v2.5.0】
     * @param request 解锁iam用户请求体
     *
     * @return {@code RestResult}
     */
    @ApiOperation(httpMethod = "PUT", value = "解除用户锁定状态", notes = "设置用户的状态从锁定到启用")
    @Authorize(action = "iam:users:ModifyUser")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'解除用户锁定状态'", bizId = "#request.userSids", param = "#request", resource = OperationResourceEnum.USER_UNLOCK_STATUS, tagNameUs ="'Unlock User Status'")
    @AuthorizeBss(action = CH.CH0115)
    public RestResult setUserUnlockStatus(@RequestBody UnlockIamUserRequest request) {
        boolean result = userService.unlockUsers(request.getUserSids());
        if (result) {
            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS));
        }
        return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERROR_UPDATE_FAILURE));
    }

    /**
     * 获取CCPortal的URL和用户信息
     *
     * @param clusterId 集群id
     * @param poolId 池id
     *
     * @return {@code CcPortalUserInfoResponse}
     *
     * @throws IOException ioexception
     */
    @ApiOperation("获取CCPortal的URL和用户信息")
    @GetMapping("/ccportal_info")
    @Authorize(action = "hpc:hpc:SendCCPortal")
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "'删除ModelArts共享池'",
            resource = OperationResourceEnum.CCPPORTALINFO,param = "#poolId" ,bizId = "#clusterId")
    public CcPortalUserInfoResponse getCcPortalUserInfo(Long clusterId, Long poolId) throws IOException {
        User authUser = AuthUtil.getAuthUser();
        if (authUser.getOrgSid() != null) {
            checkHpcStatus(clusterId);
        }
        if (Objects.isNull(authUser) || Objects.isNull(authUser.getUserSid())) {
            throw new BizException(USER_NOT_EXIST);
        }
        cn.com.cloudstar.rightcloud.bss.module.access.bean.User user = sysUserService.selectByPrimaryKey(
                authUser.getUserSid());
        CcPortalUserInfoResponse result = new CcPortalUserInfoResponse();
        //获取HCSO系统时间使用内网地址
        String ccpInternelAddress;
        if (clusterId == null) {
            result.setUserId(user.getAccount());
            result.setCcPortalUrl(configMapper.selectConfigValue(SysConfigConstants.CCPORTAL_URL));
            ccpInternelAddress = result.getCcPortalUrl();
        } else {
            ResHpcClusterDetailDTO resHpcClusterDetailDTO = getResHpcClusterDetailDTO(authUser.getOrgSid(), clusterId);
            String ccpExternalAddress = resHpcClusterDetailDTO.getCcpExternalAddress();
            ccpInternelAddress = resHpcClusterDetailDTO.getCcpInternelAddress();
            result.setUserId(user.getAccount());
            result.setCcPortalUrl(ccpExternalAddress);
            result.setCcpVersion(resHpcClusterDetailDTO.getCcpVersion());
        }
        String password = hpcPassService.findPassword(user.getUserSid());
        result.setUserPwd(aesEncrypt(password));
        RequestInfo requestInfo = hcsoTimeService.getHCSOTime(ccpInternelAddress);
        result.setRequestTime(requestInfo.getRequestTime());
        result.setRequestId(requestInfo.getRequestId());
        return result;
    }

    private String aesEncrypt(String content) {
        try {
            byte[] input = content.getBytes(DEFAULT_CODING);
            SecretKeySpec skc = new SecretKeySpec(PropertiesUtil.getProperty(SECRET_KEY).getBytes(DEFAULT_CODING), "AES");
            IvParameterSpec ivspec = new IvParameterSpec(PropertiesUtil.getProperty(SECRET_IV).getBytes(DEFAULT_CODING));
            Cipher cipher = Cipher.getInstance(ALGORITHMSTR, "BC");
            cipher.init(Cipher.ENCRYPT_MODE, skc, ivspec);
            byte[] cipherText = new byte[cipher.getOutputSize(input.length)];
            int ctLength = cipher.update(input, 0, input.length, cipherText, 0);
            cipher.doFinal(cipherText, ctLength);
            return Base64.getEncoder().encodeToString(cipherText)
                         .replaceAll("\r\n", "")
                         .replaceAll("\r", "")
                         .replaceAll("\n", "");
        } catch (Exception e) {
            log.info("gcm算法加密失败！");
            return null;
        }
    }

    //通过clusterId判断用户是否关联了该HPC资源
    private Boolean getIsExistence(List<Long> hpcCluIdList, Long clusterId) {
        if (CollectionUtil.isNotEmpty(hpcCluIdList)) {
            for (Long clusterId1 : hpcCluIdList) {
                if (Objects.equals(clusterId1, clusterId)) {
                    return true;
                }
            }
        } else {
            return false;
        }
        return false;
    }

    private ResHpcClusterDetailDTO getResHpcClusterDetailDTO(Long orgSid, Long clusterId) {
        RestResult restResult = hpcClusterService.resHpcClusterDetail(orgSid, clusterId);
        if (restResult.getStatus()) {
            Object data = restResult.getData();
            ResHpcClusterDetailDTO detailDTO = BeanConvertUtil.convert(data, ResHpcClusterDetailDTO.class);
            if (detailDTO == null) {
                throw new BizException(
                        cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(
                                MsgCd.HPC_CLUSTER_NO_EXIST));
            }
            return detailDTO;
        } else {
            log.info(" call hpcClusterService.resHpcClusterDetail:{}", restResult);
        }
        return null;
    }

    private void checkHpcStatus(Long clusterId) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        RestResult freeze = orderService.isFreeze(authUserInfo.getUserSid());
        if (freeze.getStatus() && (Boolean) freeze.getData()) {
            if (clusterId == null) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2146900544));
            } else {
                //如果是专属 不抛异常
                ResHpcClusterRemoteModule resHpcClusterRemoteModule = hpcRemoteService.selectByPrimaryKey(clusterId);
                if ("SAASShare".equals(resHpcClusterRemoteModule.getClusterType())) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2146900544));
                }
            }
        }
        //开通状态检查
        Criteria criteria = new Criteria();
        criteria.put("product_type_like", "HPC");
        criteria.put("org_sid", authUserInfo.getOrgSid());
        criteria.put("inStatusList", Arrays.asList(SfProductEnum.NORMAL.getStatus(),
                                                   SfProductEnum.EXPIRED.getStatus()
        ));
        criteria.put("cluster_id", clusterId);
        List<SfProductResource> sfProductResources = sfProductResourceMapper.selectByParams(criteria);
        if (sfProductResources.size() == 0) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1657593872));
        }
        //判断用户是否实名认证
        RestResult result = userAuthService.checkAuth(authUserInfo.getUserSid());
        if (!result.getStatus()) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2102672431));
        }

    }

    /**
     * 判断当前用户是否为分销商销售
     *
     * @return boolean
     */
    @ApiOperation("判断当前用户是否为分销商销售")
    @GetMapping("/login/is/salesman")
    @AuthorizeBss(action = AuthModule.BQ.BQ_COMMON)
    public boolean isSalesman() {
        if (UserType.DISTRIBUTOR_USER.equals(AuthUtil.getAuthUser().getUserType())) {
            List<Long> role = userService.selectUserRole(AuthUtil.getAuthUser().getUserSid());
            return "403".equals(role.get(0).toString());
        }
        return false;
    }

    /**
     * 根据用户id获取用户敏感数据
     *
     * @return RestResult
     */
    @AuthorizeBss(action = C1.C116)
    @ApiOperation(httpMethod = "GET", value = "获取用户敏感数据", notes = "根据用户id获取用户敏感数据")
    @GetMapping("/sensitive_data")
    @OperationLog(type = OperationTypeEnum.QUERY, tagName = "'用户个人隐私信息'", bizId = "#userSensitiveRequest.userId", resource = OperationResourceEnum.SELECT_USER_PERSONAL_INFORMATION, tagNameUs ="'User Personal Privacy Information'")
    public RestResult<String> getSensitiveDataByUserId(@Valid UserSensitiveRequest userSensitiveRequest) {
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        String sessionId = Optional.ofNullable(cn.com.cloudstar.rightcloud.common.util.WebUtil.getRequest()).map(request -> request.getHeader("sessionid")).orElse(null);
        if (ObjectUtils.isEmpty(authUser) || ObjectUtils.isEmpty(sessionId)) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        // 从缓存中取权限
        String key1 = "user:permission:" + authUser.getUserSid();
        String cacheObj = redisTemplates.opsForValue().get(key1);
        List<String> cacheAuths = JSON.parseObject(cacheObj, List.class);

        String result = "";
        String key = TWICE_VALIDATE + DigestUtil.sha256Hex(sessionId);
        DateTime dateTime = new DateTime();
        String value = redisTemplates.opsForValue().get(key);
        DateTime parse = cn.hutool.core.date.DateUtil.parse(value);
        if (StrUtil.isBlank(value) || "null".equalsIgnoreCase(value)) {
            return new RestResult(RestConst.HttpConst.OK, Status.FAILURE, null, NEED_TWICE_VALIDATE);
        }
        if (StrUtil.isNotBlank(value) && !"null".equalsIgnoreCase(value)) {
            if (cn.hutool.core.date.DateUtil.between(parse, dateTime, DateUnit.MINUTE) >= AUTH_AGAIN_MINUTE) {
                return new RestResult(RestConst.HttpConst.OK, Status.FAILURE, null, NEED_TWICE_VALIDATE);
            }
        }

        UserSensitiveEnum userSensitiveEnum = UserSensitiveEnum.getEnum(userSensitiveRequest.getType());
        if (UserSensitiveEnum.USER.getKey().equals(userSensitiveRequest.getSensitiveType())) {
            cn.com.cloudstar.rightcloud.bss.module.access.bean.User user = userService.selectByPrimaryKey(
                    Long.valueOf(userSensitiveRequest.getUserId()));
            if (ObjectUtils.isEmpty(user)) {
                return new RestResult(result);
            }
            QueryWrapper<MarketShopSubscribe> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda()
                        .eq(MarketShopSubscribe::getShopOwnerId, authUser.getUserSid())
                        .eq(MarketShopSubscribe::getOwnerId, user.getUserSid());
            List<MarketShopSubscribe> subscribes = subscribeMapper.selectList(queryWrapper);
            if (!authUser.getOrgSid().equals(user.getOrgSid())&& CollectionUtil.isEmpty(subscribes)) {
                throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
            } else {
                if (!ObjectUtils.isEmpty(authUser.getParentSid()) && !authUser.getParentSid().equals(user.getUserSid())) {
                    throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
                }
                if (cacheAuths.stream().noneMatch("CH01"::equals) && !authUser.getUserSid().equals(user.getUserSid())) {
                    throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
                }
            }
            switch (userSensitiveEnum) {
                case MOBILE:
                    result = CrytoUtilSimple.decrypt(user.getMobile());
                    break;
                case EMAIL:
                    result = CrytoUtilSimple.decrypt(user.getEmail());
                    break;
                case REALNAME:
                    result = CrytoUtilSimple.decrypt(user.getRealName());
                    break;
                case ID_CARD:
                    result = CrytoUtilSimple.decrypt(user.getAuthId());
                    break;
                case ID_CARD_FRONT_PICTURE:
                    result = CrytoUtilSimple.decrypt(user.getIdCardFront());
                    break;
                case ID_CARD_REVERSE_PICTURE:
                    result = CrytoUtilSimple.decrypt(user.getIdCardReverse());
                    break;
                case ACCOUNT:
                    result = CrytoUtilSimple.decrypt(user.getAccount());
                    break;
                default:
                    return new RestResult(result);
            }
        }
        else if (UserSensitiveEnum.INVOICE_DETAIL.getKey().equals(userSensitiveRequest.getSensitiveType())) {
            BizInvoice bizInvoice = bizInvoiceMapper.selectOne(
                    Wrappers.lambdaQuery(BizInvoice.class)
                            .eq(BizInvoice::getInvoiceSid, userSensitiveRequest.getUserId())
                            .last("limit 1"));
            if (ObjectUtils.isEmpty(bizInvoice)) {
                return new RestResult(result);
            }
            BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectById(bizInvoice.getAccountId());
            if (ObjectUtils.isEmpty(bizBillingAccount)) {
                return new RestResult(result);
            }
            if (!authUser.getOrgSid().equals(bizBillingAccount.getOrgSid())) {
                throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
            } else {
                if (cacheAuths.stream().noneMatch("CC05"::equals)) {
                    throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, cn.com.cloudstar.rightcloud.common.util.WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
                }
            }
            switch (userSensitiveEnum) {
                case REALNAME:
                    result = CrytoUtilSimple.decrypt(bizInvoice.getReceiver());
                    break;
                case MOBILE:
                    result = CrytoUtilSimple.decrypt(bizInvoice.getPhone());
                    break;
                case REGISTER_MOBILE:
                    result = CrytoUtilSimple.decrypt(bizInvoice.getRegisterPhone());
                    break;
                case EMAIL:
                    result = CrytoUtilSimple.decrypt(bizInvoice.getEmail());
                    break;
                case ADDRESS:
                    result = CrytoUtilSimple.decrypt(bizInvoice.getAddress());
                    break;
                case BANK_ACCOUNT:
                    result = CrytoUtilSimple.decrypt(bizInvoice.getBankAccount());
                    break;
                case REGISTER_ADDRESS:
                    result = CrytoUtilSimple.decrypt(bizInvoice.getRegisterAddress());
                    break;
                default:
                    return new RestResult(result);
            }
        }
        else if(UserSensitiveEnum.INVOICE_SETTING.getKey().equals(userSensitiveRequest.getSensitiveType())){
            BizInvoiceSetting bizInvoiceSetting = bizInvoiceSettingMapper.selectById(userSensitiveRequest.getUserId());
            if (bizInvoiceSetting == null) {
                bizInvoiceSetting = bizInvoiceSettingMapper.selectOne(new QueryWrapper<BizInvoiceSetting>()
                        .eq("account_id", userSensitiveRequest.getUserId()));
            }
            if (ObjectUtils.isEmpty(bizInvoiceSetting)) {
                return new RestResult(result);
            }
            switch (userSensitiveEnum) {
                case REALNAME:
                    result = CrytoUtilSimple.decrypt(bizInvoiceSetting.getReceiver());
                    break;
                case MOBILE:
                    result = CrytoUtilSimple.decrypt(bizInvoiceSetting.getPhone());
                    break;
                case REGISTER_MOBILE:
                    result = CrytoUtilSimple.decrypt(bizInvoiceSetting.getRegisterPhone());
                    break;
                case ADDRESS:
                    result = CrytoUtilSimple.decrypt(bizInvoiceSetting.getAddress());
                    break;
                case BANK_ACCOUNT:
                    result = CrytoUtilSimple.decrypt(bizInvoiceSetting.getBankAccount());
                    break;
                case REGISTER_ADDRESS:
                    result = CrytoUtilSimple.decrypt(bizInvoiceSetting.getRegisterAddress());
                    break;
                default:
                    return new RestResult(result);
            }
        }
        else if(UserSensitiveEnum.ORG.getKey().equals(userSensitiveRequest.getSensitiveType())){
            Org org = orgService.selectRootOrg(userSensitiveRequest.getUserId());
            if (org == null) {
                return new RestResult(result);
            }
            switch (userSensitiveRequest.getType()) {
                case "realName":
                    result = CrytoUtilSimple.decrypt(org.getContactName());
                    break;
                case "mobile":
                    result = CrytoUtilSimple.decrypt(org.getContactPhone());
                    break;
                default:
                    return new RestResult(result);
            }
        }
        else {
            return new RestResult(result);
        }
        return new RestResult(result);
    }


    private void authorityJudgment(MoveUserToGroupRequest request) {
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (ObjectUtils.isEmpty(authUserInfo)){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_531007114));
        }
        if (ObjectUtils.isEmpty(authUserInfo.getParentSid())){
            return;
        }
        QueryWrapper<UserGroup> userGroupQueryWrapper = new QueryWrapper<>();
        userGroupQueryWrapper.lambda()
                             .eq(UserGroup::getUserSid,authUserInfo.getUserSid());
        List<Long> groupList = userGroupService.list(userGroupQueryWrapper).stream()
                                          .map(UserGroup::getGroupSid)
                                          .collect(Collectors.toList());
        if (ObjectUtils.isEmpty(groupList)){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1618358003));
        }
    }

    /**
     * 批量创建子用户
     *
     * @param requestApi 批量创建子用户请求体
     *
     * @return {@code RestResult}
     */
    @ApiOperation("批量创建子用户")
    @PostMapping("/subuser/api")
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "'创建用户信息'",
            resource = OperationResourceEnum.ADD_SUB_USER, param = "#requestApi")
    @Idempotent
    @Encrypt
    @AuthorizeBss(action = AuthModule.CH.CH0102, actionName = "创建子用户")
    @Transactional(rollbackFor = Exception.class)
    public RestResult createUser(@RequestBody @Valid CreateUsersApiRequest requestApi) {
        HttpServletRequest servletRequest = cn.com.cloudstar.rightcloud.common.util.RequestContextUtil.getRequest();
        String ak = servletRequest.getHeader("Access-Key");
        log.info("ak...........,{}", ak);
        if (Boolean.parseBoolean(System.getenv("sm3_start"))) {
            //模板导入不用再次进入循环解密
            if(Objects.isNull(requestApi.getIsImport()) || !"import".equalsIgnoreCase(requestApi.getIsImport())){
                //requestApi.setPassword(CrytoUtilSimple.encodeSm3(requestApi.getPassword()));
                for (UserApiVO user : requestApi.getUsers()) {
                    String realName = CrytoUtilSimple.decrypt(user.getRealName());
                    String mobile = CrytoUtilSimple.decrypt(user.getMobile());
                    user.setRealName(StrUtil.isNotBlank(realName) ? realName : user.getRealName());
                    log.info("realName...........,{},...account....{}", realName,user.getAccount());
                    user.setMobile(StrUtil.isNotBlank(mobile) ? mobile : user.getMobile());
                }
            }
        }

        try {
            CreateUsersRequest request = BeanConvertUtil.convert(requestApi, CreateUsersRequest.class);
            if (CollectionUtils.isEmpty(request.getUsers())) {
                return new RestResult(Status.FAILURE, WebUtil.getMessage(MsgCd.ERROR_INSERT_FAILURE));
            }

            User authUser = AuthUtil.getAuthUser();
            List<cn.com.cloudstar.rightcloud.bss.module.access.bean.User> userList = userService.createUsers(request,
                                                                                                             authUser.getUserSid());
            List<PfResponseUser> pfResponseUsers = new ArrayList<>();
            for (cn.com.cloudstar.rightcloud.bss.module.access.bean.User user : userList) {
                log.info("AccessController-createUsers-account【{}】", user.getAccount());
                PfResponseUser pfResponseUser = new PfResponseUser();
                pfResponseUser.setAccount(user.getAccount());
                pfResponseUser.setUserId(user.getUserSid());
                pfResponseUser.setRefUserId(user.getRefUserId());
                pfResponseUsers.add(pfResponseUser);
                try {
                    user.setGroupIds(request.getGroupIds());
                    //远程添加
                    ResCloudEnv resCloudEnv = iamRemoteService.insertUser(
                            BeanConvertUtil.convert(user, IamUser.class));
                    log.info("AccessController-hcsoFlg:{}", hcsoFlg);
/*                    if (hcsoFlg) {
                        iamUserService.resUserCreate(user.getUserSid(), user.getAccount(), user.getOrgSid(),
                                                     resCloudEnv);
                    }*/
                } catch (Exception e) {
                    log.error("创建失败,{}", e.getMessage());
                    iamRemoteService.deleteUserOrgByUser(userList.stream()
                                                                 .map(cn.com.cloudstar.rightcloud.bss.module.access.bean.User::getUserSid)
                                                                 .collect(Collectors.toList()));
                    throw e;
                }
            }

            //日志记录  脱敏处理
            for (UserVO user : request.getUsers()) {
                user.setMobile(DataProcessingUtil.processing(user.getMobile(), DataProcessingUtil.PHONE));
                user.setEmail(DataProcessingUtil.processing(user.getEmail(), DataProcessingUtil.EMAIL));
            }
            request.setPassword(DataProcessingUtil.processing(request.getPassword(), DataProcessingUtil.CIPHER));

            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_INSERT_SUCCESS),
                                  pfResponseUsers);
        } catch (Exception e) {
            //日志记录  脱敏处理
            for (UserApiVO user : requestApi.getUsers()) {
                user.setMobile(DataProcessingUtil.processing(user.getMobile(), DataProcessingUtil.PHONE));
                user.setEmail(DataProcessingUtil.processing(user.getEmail(), DataProcessingUtil.EMAIL));
            }
            requestApi.setPassword(DataProcessingUtil.processing(requestApi.getPassword(), DataProcessingUtil.CIPHER));
            if (e instanceof BizException) {
                e.printStackTrace();
                log.error("服务器异常,请联系管理员,{}",e.getMessage());
                throw new BizException(e.getMessage());
            } else {
                e.printStackTrace();
                log.error("服务器异常,请联系管理员,{}",e.getMessage());
                throw new BizException("服务器异常,请联系管理员");
            }
        }
    }

}
