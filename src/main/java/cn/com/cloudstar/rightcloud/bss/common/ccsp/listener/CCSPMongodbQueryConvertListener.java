package cn.com.cloudstar.rightcloud.bss.common.ccsp.listener;

import cn.com.cloudstar.rightcloud.bss.common.ccsp.CCSPHandler;
import com.ccsp.sdk.crypto.CryptoException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.mapping.event.AbstractMongoEventListener;
import org.springframework.data.mongodb.core.mapping.event.AfterConvertEvent;
import org.springframework.stereotype.Component;

import java.lang.reflect.InvocationTargetException;

/**
 * @description: CCSPMongodbConvertListener mongodb的查询监听
 * 国密验证
 * @author: ouyonghui
 * @date: 2023/4/6 10:54
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class CCSPMongodbQueryConvertListener extends AbstractMongoEventListener<Object> {
    private final CCSPHandler ccspHandler;

    @Override
    public void onAfterConvert(AfterConvertEvent<Object> event) {
        log.debug("executing mongodb onAfterConvert");
        boolean needVerifyCCSP = ccspHandler.needVerifyCCSP(event.getSource());
        if (!needVerifyCCSP) {
            return;
        }
        try {
            ccspHandler.verifyCCSP(event.getSource());
        } catch (IllegalAccessException | InvocationTargetException | CryptoException e) {
            e.printStackTrace();
        }
    }


}

