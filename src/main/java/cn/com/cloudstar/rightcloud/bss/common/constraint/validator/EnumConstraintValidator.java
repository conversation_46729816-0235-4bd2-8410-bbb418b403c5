/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.common.constraint.validator;

import java.util.Objects;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

import cn.com.cloudstar.rightcloud.bss.common.constraint.EnumValue;

/**
 * The type MobileConstraintValidator.
 * <p>
 *
 * <AUTHOR>
 * @date 2019/8/8
 */
public class EnumConstraintValidator implements ConstraintValidator<EnumValue, Object> {

    private String[] strValues;

    @Override
    public void initialize(EnumValue constraintAnnotation) {
        strValues = constraintAnnotation.strValues();
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        if (Objects.isNull(value)) {
            return true;
        }

        if (value instanceof String) {
            for (String s : strValues) {
                if (s.equals(value)) {
                    return true;
                }
            }
        }
        return false;

    }
}
