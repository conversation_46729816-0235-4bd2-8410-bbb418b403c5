/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.common.aop;

import cn.com.cloudstar.rightcloud.common.constants.RestConst.BizError;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.exception.BizException;
import cn.com.cloudstar.rightcloud.common.util.IPAddressUtil;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.oss.common.cache.JedisUtil;
import cn.com.cloudstar.rightcloud.oss.common.pojo.User;
import cn.com.cloudstar.rightcloud.oss.common.util.AuthUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR> jin
 * @Description 二次认证 短信验证码
 * @createTime 2022年04月18日 10:15:00
 */
@Aspect
@Component
@Slf4j
public class SmsValidationAspect {

    @Pointcut("@annotation(cn.com.cloudstar.rightcloud.core.annotation.log.SmsValidation)")
    public void pointCut() {

    }

    @Before(value = "pointCut()")
    public void before() {
        String skipSmsValidate = System.getProperty("SKIP_SMS_VALIDATE");
        if (!"true".equals(skipSmsValidate)) {
            User authUser = AuthUtil.getAuthUser();
            // 判断IP地址限制
            String ip = IPAddressUtil.getRemoteHostIp(WebUtil.getRequest());

            Object object = JedisUtil.INSTANCE.get(authUser.getAccount() + authUser.getMobile() + ip + "SMS_CODE_LIMIT");

            if (ObjectUtil.isEmpty(object)) {
                throw new BizException(BizError.NEED_SMS_VALI, WebUtil.getMessage(MsgCd.NEED_SMS_VALI));
            }
        }
    }
}
