/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.common.constraint.validator;

import com.google.common.base.Strings;

import java.util.regex.Pattern;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

import cn.com.cloudstar.rightcloud.bss.common.constraint.Tel;

/**
 * The type MobileConstraintValidator.
 * <p>
 *
 * <AUTHOR>
 * @date 2019/8/8
 */
public class TelConstraintValidator implements ConstraintValidator<Tel, String> {

    private static final String TEL_CONSTRAINT = "^0\\d{2,3}\\-\\d{7,8}$";

    @Override
    public void initialize(Tel mobile) {

    }

    @Override
    public boolean isValid(String s, ConstraintValidatorContext constraintValidatorContext) {
        // 允许为空，不允许为空时，交由@NotBlank注解验证
        if (Strings.isNullOrEmpty(s)) {
            return true;
        }

        Pattern compile = Pattern.compile(TEL_CONSTRAINT);

        return compile.matcher(s).matches();
    }
}
