/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.distributor.service.impl;

import cn.com.cloudstar.rightcloud.bss.common.constants.OrgType;
import cn.com.cloudstar.rightcloud.bss.common.constants.RestConst;
import cn.com.cloudstar.rightcloud.bss.common.enums.ExportTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Org;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.pojo.User;
import cn.com.cloudstar.rightcloud.bss.common.util.*;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.UserOrg;
import cn.com.cloudstar.rightcloud.bss.module.access.common.util.PropertiesUtil;
import cn.com.cloudstar.rightcloud.bss.module.access.mapper.SysUserMapper;
import cn.com.cloudstar.rightcloud.bss.module.access.service.SysUserService;
import cn.com.cloudstar.rightcloud.bss.module.access.service.UserOrgService;
import cn.com.cloudstar.rightcloud.bss.module.account.mapper.BizBillingAccountMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.account.service.ISysBssEntityService;
import cn.com.cloudstar.rightcloud.bss.module.coupon.util.NoUtil;
import cn.com.cloudstar.rightcloud.bss.module.distributor.mapper.BizDistributorMapper;
import cn.com.cloudstar.rightcloud.bss.module.distributor.pojo.entity.BizDistributor;
import cn.com.cloudstar.rightcloud.bss.module.distributor.pojo.request.GenerateMeteringFileRequest;
import cn.com.cloudstar.rightcloud.bss.module.distributor.service.IBizDistributorService;
import cn.com.cloudstar.rightcloud.bss.module.download.entity.BizDownload;
import cn.com.cloudstar.rightcloud.bss.module.download.mapper.BizDownloadMapper;
import cn.com.cloudstar.rightcloud.bss.module.export.service.ExportService;
import cn.com.cloudstar.rightcloud.bss.module.sfs.service.SysMFilePathService;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.OrgService;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.CrytoUtilSimple;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.core.pojo.dto.file.SysMFilePath;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.oss.common.constants.SysConfigConstants;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.UserType;
import cn.com.cloudstar.rightcloud.oss.common.enums.SysMFileTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.ZipUtil;
import cn.com.cloudstar.rightcloud.remote.api.iam.pojo.IamOrg;
import cn.com.cloudstar.rightcloud.remote.api.iam.service.IamRemoteService;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.seata.spring.annotation.GlobalTransactional;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static cn.com.cloudstar.rightcloud.bss.module.access.common.constant.Constants.CUSTOMER_MAX;
import static cn.com.cloudstar.rightcloud.bss.module.access.common.constant.Constants.EXISTENCE_RELATION_CUSTOMER;

/**
 * <p>
 * 分销商信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-28
 */
@Service
public class BizDistributorServiceImpl extends ServiceImpl<BizDistributorMapper, BizDistributor> implements
    IBizDistributorService {

    private final Integer BIZ_DOWN_STATUS = 0;

    private final Integer BIZ_DOWN_VERSION = 1;

    private final static String USER_TYPE = "04";

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private UserOrgService userOrgService;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private OrgService orgService;

    @DubboReference
    private IamRemoteService iamRemoteService;
    @Autowired
    private BizBillingAccountMapper bizBillingAccountMapper;

    @Autowired
    private SysMFilePathService sysMFilePathService;

    @Autowired
    private ExportService exportService;

    @Autowired
    private BizDownloadMapper bizDownloadMapper;


    @Autowired
    private ISysBssEntityService iSysBssEntityService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeDistributorUser(List<Long> userSids) {
        //判断当前分销商账户下是否有关联客户
        Integer count = bizBillingAccountMapper.getSalesmenAccountBySalesmenId(userSids);
        if (count>0){
            throw new BizException(EXISTENCE_RELATION_CUSTOMER);
        }
        // 查询数据库判断数据库中是否存在该用户,不存在就抛出错误
        boolean userIsExist = userSids.stream().map(userSid -> sysUserMapper.selectByPrimaryKey(userSid)).anyMatch(user -> ObjectUtils.isEmpty(user) || "8".equals(user.getStatus()));
        if (userIsExist) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1387618309));
        }
        sysUserService.removeByIds(userSids);
        QueryWrapper<UserOrg> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(UserOrg::getUserSid, userSids);
        userOrgService.remove(queryWrapper);
        sysUserMapper.deleteUserRole(userSids);

        iamRemoteService.deleteUserOrgByUser(userSids);
        userSids.forEach(userSid -> {
            iamRemoteService.deleteUser(userSid);
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @GlobalTransactional
    public void createDistributor(BizDistributor bizDistributor) {
        User user = AuthUtil.getAuthUser();
        if (user == null) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        bizDistributor.setExpiredTime(DateUtil.convertLocalDateToDate(LocalDate.now().plusYears(1)));

        //检查名字是否存在；
        LambdaQueryWrapper<Org> qw = new LambdaQueryWrapper<>();
        qw.eq(Org::getOrgName,bizDistributor.getName());
        Org dbOrgTmp = orgService.getOne(qw);
        if(dbOrgTmp !=null)
        {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1966420551));
        }

        // 组织层级最大为4级
        Org orgTmp = orgService.getById(user.getOrgSid());
        if (orgTmp != null && !StringUtil.isNullOrEmpty(orgTmp.getTreePath())) {
            List<String> distributorSize = Arrays.stream(orgTmp.getTreePath().split("/")).filter(s -> StringUtil.isNotEmpty(s)).collect(
                    Collectors.toList());
            String orgLevelCount = PropertiesUtil.getProperty(SysConfigConstants.ORG_LEVEL_COUNT);
            if (StringUtil.isNullOrEmpty(orgLevelCount) || Integer.parseInt(orgLevelCount) <= 0) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_66713528));
            }
            if (distributorSize.size() + 1 >= Integer.parseInt(orgLevelCount)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1557390068));
            }
        }

        //分销商账户创建下级
        if (bizDistributor.getParentId() == null
            && UserType.DISTRIBUTOR_USER.equals(user.getUserType())) {
            bizDistributor.setTreePath("/" + user.getOrgSid() + "/");
            bizDistributor.setParentId(user.getOrgSid());
        }
        //设置treepath
        if (bizDistributor.getParentId() != null) {
            BizDistributor parent = this.getById(bizDistributor.getParentId());
            String treePath = parent.getTreePath();
            bizDistributor.setTreePath(treePath + parent.getId() + "/");
        }
        WebUserUtil.prepareInsertParams(bizDistributor);

        //添加一条组织信息
        Org org = new Org();
        org.setOrgSid(bizDistributor.getId());
        org.setOrgType(OrgType.DISTRIBUTOR);
        org.setParentId(bizDistributor.getParentId());
        org.setOrgName(bizDistributor.getName());
        org.setOrgCode("default");
        org.setTreePath(bizDistributor.getTreePath() == null ? "/" : bizDistributor.getTreePath());
        org.setStatus("1");
        WebUserUtil.prepareInsertParams(org);
        Long orgId = iamRemoteService.insertOrg(BeanConvertUtil.convert(org, IamOrg.class));
        org.setOrgSid(orgId);
        orgService.save(org);
        bizDistributor.setId(orgId);
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if(authUserInfo != null){
            bizDistributor.setEntityId(authUserInfo.getEntityId());
        }

        if (StringUtils.isNotBlank(bizDistributor.getLicenseUrl()) && StringUtils.isNotBlank(CrytoUtilSimple.decrypt(bizDistributor.getLicenseUrl()))) {
            bizDistributor.setLicenseUrl(CrytoUtilSimple.decrypt(bizDistributor.getLicenseUrl()));
        }

        if (StringUtils.isNotBlank(bizDistributor.getLicenseUrl()) && bizDistributor.getLicenseUrl().contains(ZipUtil.JOINER)) {
            String[] split = bizDistributor.getLicenseUrl().split(ZipUtil.JOINER);
            bizDistributor.setLicenseUrl(CrytoUtilSimple.decrypt(split[0]));

            SysMFilePath sysMFilePath = new SysMFilePath();
            sysMFilePath.setFilePath(CrytoUtilSimple.decrypt(split[0]));
            sysMFilePath.setCompressPassword(split[1]);
            String fileName = bizDistributor.getLicenseName().substring(0, bizDistributor.getLicenseName().lastIndexOf(".")) + ".zip";
            sysMFilePath.setFileName(fileName);
            sysMFilePathService.saveSysMFilePath(sysMFilePath, SysMFileTypeEnum.ID_CARD, bizDistributor.getId());
        }
        this.save(bizDistributor);


    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateDistributor(BizDistributor bizDistributor) {
        BizDistributor dbRecord = this.getById(bizDistributor.getId());
        if (dbRecord == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERROR_DATA_FAILURE));
        }

        Map<String,Object> query=new HashMap<>(8);
        query.put("distributorId",dbRecord.getId());
        List<BizBillingAccount> accounts = bizBillingAccountMapper.selectByList(query);
        //判断分销商下用户数量
        if (accounts.size() >bizDistributor.getCustomerMaxNum()){
            throw new BizException(CUSTOMER_MAX);
        }

        Org org = new Org();
        org.setOrgName(bizDistributor.getName());
        org.setOrgSid(dbRecord.getId());
        orgService.updateById(org);

        //存入数据库需解密
        if (Objects.nonNull(bizDistributor.getLicenseUrl())) {
            bizDistributor.setLicenseUrl(CrytoUtilSimple.decrypt(bizDistributor.getLicenseUrl()));
        }

        this.updateById(bizDistributor);

        return true;
    }

    @Override
    public List<BizDistributor> selectDistributorInfoByRequest(BizDistributor distributor) {
        QueryWrapper<BizDistributor> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("name",distributor.getName());
        List<BizDistributor> distributors = this.baseMapper.selectList(queryWrapper);
        return distributors;
    }

    @Override
    public RestResult asynGenerateMeteringFile(GenerateMeteringFileRequest request, String moduleType) {
        User authUser = AuthUtil.getAuthUser();
        if (Objects.isNull(authUser) || !USER_TYPE.equals(authUser.getUserType())) {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_825160051)).setCode(RestConst.HttpConst.Unauthorized);
        }
        if (StringUtil.isNotEmpty(request.getTenant())) {
            if (bizBillingAccountMapper.selectAccountIsExist(request.getTenant(), authUser.getOrgSid()) == 0) {
                return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1949911565)).setCode(RestConst.HttpConst.CREATED);
            }
        }
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo(WebUtil.getRequest());
        request.setOrgSid(authUser.getOrgSid());
        BizDownload download = new BizDownload();
        download.setOperationType(ExportTypeEnum.USER_STATISTICAL_DATA.getCode());
        //添加下载任务数据
        download = getBizDownload(download, request, moduleType, authUser);
        if (Objects.isNull(download.getDownloadId())) {
            new RestResult(RestResult.Status.FAILURE, "计量数据生成异常，请稍后重试!");
        }
        new ExportThreadUtil(exportService,
                             request,
                             moduleType,
                             ExportTypeEnum.USER_STATISTICAL_DATA.getCode(),
                             download.getDownloadId(),
                             authUserInfo
        ).submit();
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1308608287), download.getDownloadNum());

    }

    @Override
    public boolean checkBelong(Long distributorSid, Long orgSid) {
        Org targetOrg = orgService.getById(orgSid);
        return targetOrg.getTreePath().contains(String.valueOf(distributorSid));
    }


    /**
     * 分销商下
     * @param accountId
     * @return true可操作，false不可操作
     */
    @Override
    public boolean checkAccountId(Long accountId) {
        User authUser = AuthUtil.getAuthUser();
        if (authUser != null){
            LambdaQueryWrapper<BizBillingAccount> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            if(UserType.DISTRIBUTOR_USER.equals(authUser.getUserType())) {
                lambdaQueryWrapper.eq(BizBillingAccount::getDistributorId,authUser.getOrgSid());
                Integer count = bizBillingAccountMapper.selectCount(lambdaQueryWrapper);
                if(count == 0){
                    return false;
                }
            }else {
                lambdaQueryWrapper.eq(BizBillingAccount::getEntityId,authUser.getEntityId());
            }
            lambdaQueryWrapper.eq(BizBillingAccount::getId,accountId);
            Integer count = bizBillingAccountMapper.selectCount(lambdaQueryWrapper);
            if(count == 0){
                return false;
            }
        }
        return true;
    }

    private BizDownload getBizDownload(BizDownload download, GenerateMeteringFileRequest request, String moduleType,
                                       User authUserInfo) {
        download.setOrgSid(authUserInfo.getOrgSid());
        download.setDownloadNum(NoUtil.generateNo("DT"));
        download.setParam("request【" + JSON.toJSONString(request) + "】---moduleType【" + moduleType + "】");
        download.setStatus(BIZ_DOWN_STATUS);
        download.setCreatedBy(authUserInfo.getAccount());
        download.setCreatedDt(new Date());
        download.setVersion(BIZ_DOWN_VERSION);
        int insert = bizDownloadMapper.insert(download);
        return download;
    }
}
