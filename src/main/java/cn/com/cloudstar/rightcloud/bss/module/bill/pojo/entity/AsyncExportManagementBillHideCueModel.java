/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.bill.pojo.entity;

import com.alibaba.excel.annotation.ExcelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 导出账单明细
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class AsyncExportManagementBillHideCueModel extends AsyncExportBaseModel implements Serializable {

    /**
     * 账期YYYY－MM
     */
    @ExcelProperty(value = "账期",order = 1)
    private String billingCycle;
    /**
     * 账单号
     */
    @ApiModelProperty(value = "账单号")
    @ExcelProperty(value = "账单号",order = 2)
    private String billNo;
    /**
     * accountID-用户处理逻辑数据
     */
    @ExcelProperty(value = "账户ID",order = 3)
    private String userAccountId;
    /**
     * 实体名称
     */
    @ExcelProperty(value = "账户名称",order = 4)
    private String entityName;
    /**
     * 产品名称
     */
    @ExcelProperty(value = "产品名称",order = 5)
    private String productName;
    /**
     * 实例名称
     */
    @ExcelProperty(value = "名称", order = 6)
    private String instanceName;
    /**
     *
     */
    @ExcelProperty(value = "ID",order = 6)
    private String instanceId;
    /**
     * 机构名称
     */
    @ApiModelProperty(value = "机构名称")
    @ExcelProperty(value = "客户名称",order = 7)
    private String orgName;
    /**
     * 用户名
     */
    @ExcelProperty(value = "用户名",order = 8)
    private String userAccountName;
    /**
     * 所属分销商名称
     */
    @ExcelProperty(value = "所属分销商",order = 9)
    private String distributorName;
    /**
     * 账单类型：SubscriptionOrder (预付订单)， PayAsYouGoBill (后付账单)， Refund (退款)， Adjustment (调账)
     */
    @ExcelProperty(value = "计费类型",order = 10)
    private String billType;
    /**
     * Region
     */
    @ExcelProperty(value = "区域",order = 11)
    private String region;
    /**
     * 云环境名称
     */
    @ExcelProperty(value = "云环境",order = 12)
    private String cloudEnvName;
    /**
     * 账单来源
     */
    @ExcelProperty(value = "账单来源",order = 13)
    private String billSource;
    /**
     * 账单类型
     */
    @ExcelProperty(value = "账单类型",order = 14)
    private String priceType;
    /**
     * 订单来源
     */
    @ExcelProperty(value = "订单来源",order = 15)
    private String orderSourceSn;
    /**
     *
     */
    @ExcelProperty(value = "订单类型",order = 16)
    private String type;
    /**
     * 使用量
     */
    @ExcelProperty(value = "资源用量",order = 17)
    private String usageCount;

    @ExcelProperty(value = "计费用量/免费用量",order = 15)
    private String billedAndFree;
    /**
     * 描述
     */
    @ExcelProperty(value = "描述",order = 19)
    private String description;
    /**
     * 支付时间str
     */
    @ExcelProperty(value = "支付时间",order = 20)
    private String payTimeStr;
    /**
     * 订单编号
     */
    @ExcelProperty(value = "订单号",order = 21)
    private String orderId;
    /**
     * 原始金额
     */
    @ExcelProperty(value = "原始金额(元)",order = 22)
    private BigDecimal pretaxGrossAmount;
    /**
     * 官方字符串数量
     */
    @ExcelProperty(value = "原平台金额(元)",order = 23)
    private String officialAmountString;
    /**
     * 优惠后金额
     */
    @ExcelProperty(value = "出账金额(元) ",order = 24)
    private BigDecimal pretaxAmount;
    /**
     * 所有折扣
     */
    @ExcelProperty(value = "优惠金额(元)",order = 25)
    private BigDecimal allDiscount;
    /**
     * 询价优惠
     */
    @ExcelProperty(value = "折扣优惠金额(元)",order = 26)
    private BigDecimal pricingDiscount;
    /**
     * 优惠劵折扣
     */
    @ExcelProperty(value = "优惠券抵扣金额(元)",order = 27)
    private BigDecimal couponDiscount;
    /**
     * 代金券支付金额
     */
    @ExcelProperty(value = "充值现金券支付金额(元)",order = 28)
    private BigDecimal couponAmount;
    /**
     * 抵扣现金券支付金额
     */
    @ExcelProperty(value = "抵扣现金券支付金额（元）",order = 29)
    private BigDecimal deductCouponDiscount;
    /**
     * 抹零列金额
     */
    @ExcelProperty(value = "抹零金额(元)",order = 30)
    private BigDecimal eraseZeroAmount;
    /**
     * 信用额度支付金额
     */
    @ExcelProperty(value = "信用额度支付金额(元) ",order = 31)
    private BigDecimal creditAmount;
    /**
     * 现金账户支付金额
     */
    @ExcelProperty(value = "现金支付金额(元) ",order = 32)
    private BigDecimal cashAmount;
    /**
     * 开票状态
     */
    @ExcelProperty(value = "开票状态",order = 33)
    private String invoiceStatus;
    /**
     * 使用起始日期str
     */
    @ExcelProperty(value = "统计开始时间",order = 34)
    private String usageStartDateStr;
    /**
     * 使用结束日期str
     */
    @ExcelProperty(value = "统计结束时间",order = 35)
    private String usageEndDateStr;
    /**
     * 统计小时数
     */
    @ExcelProperty(value = "统计小时数",order = 36)
    private BigDecimal statisticHours;
    /**
     * 统计天数
     */
    @ExcelProperty(value = "统计天数",order = 37)
    private BigDecimal statisticDays;
    /**
     * 支付币种(国际)
     */
    @ExcelProperty(value = "币种",order = 38)
    private String paymentCurrency;
    /**
     * 规格项
     */
    @ExcelProperty(value = "配置",order = 39)
    private String configuration;
    /**
     * 使用的卡时
     */
    @ExcelProperty(value = "消费卡时",order = 40)
    private String usedCardHourAmountString;

}
