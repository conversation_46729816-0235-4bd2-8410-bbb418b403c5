/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.operation.service.impl;

import cn.com.cloudstar.rightcloud.bss.common.constants.*;
import cn.com.cloudstar.rightcloud.bss.common.enums.InvoiceStatusEnum;
import cn.com.cloudstar.rightcloud.bss.common.enums.RechargeTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Org;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Period;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Period.PeriodType;
import cn.com.cloudstar.rightcloud.bss.common.util.AuthUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.DateUtil;
import cn.com.cloudstar.rightcloud.bss.module.account.mapper.BizBillingAccountMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.CurrentBillStatisticVO;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.entity.InstanceGaapAmount;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.entity.InstanceGaapCost;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.entity.InstanceGaapCostSummary;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.request.DescribeGaapCostRequest;
import cn.com.cloudstar.rightcloud.bss.module.bill.service.IInstanceGaapCostService;
import cn.com.cloudstar.rightcloud.bss.module.contract.mapper.BizContractMapper;
import cn.com.cloudstar.rightcloud.bss.module.contract.pojo.entity.BizContract;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.request.DescribeAccountCouponRequest;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.response.DescribeAccountCouponResponse;
import cn.com.cloudstar.rightcloud.bss.module.coupon.service.IBizCouponAccountService;
import cn.com.cloudstar.rightcloud.bss.module.distributor.mapper.BizDistributorMapper;
import cn.com.cloudstar.rightcloud.bss.module.distributor.pojo.entity.BizDistributor;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.DescribeProductResourceRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.response.DescribeProductResourceResponse;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.ISfProductResourceService;
import cn.com.cloudstar.rightcloud.bss.module.invoice.pojo.entity.InvoiceDTO;
import cn.com.cloudstar.rightcloud.bss.module.invoice.service.IBizInvoiceService;
import cn.com.cloudstar.rightcloud.bss.module.message.pojo.entity.AccountVO;
import cn.com.cloudstar.rightcloud.bss.module.operation.mapper.OperationAnalysisMapper;
import cn.com.cloudstar.rightcloud.bss.module.operation.mapper.StatisticsMapper;
import cn.com.cloudstar.rightcloud.bss.module.operation.pojo.entity.*;
import cn.com.cloudstar.rightcloud.bss.module.operation.service.IOperationStatisticsService;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.ConfigMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.OrgMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.UserMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.OrgService;
import cn.com.cloudstar.rightcloud.oss.common.cache.JedisUtil;
import cn.com.cloudstar.rightcloud.oss.common.constants.status.UserStatus;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.UserType;
import cn.com.cloudstar.rightcloud.oss.common.util.BigDecimalUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.sleuth.SpanNamer;
import org.springframework.cloud.sleuth.Tracer;
import org.springframework.cloud.sleuth.instrument.async.TraceRunnable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.aggregation.ConvertOperators.ToDecimal;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;


import java.math.BigDecimal;
import java.time.*;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static org.springframework.data.mongodb.core.aggregation.ConvertOperators.ToDecimal.toDecimal;

/**
 * <AUTHOR>
 * @date 2019/10/18.
 */
@Service("operationStatisticsService")
@Slf4j
public class OperationStatisticsServiceImpl implements IOperationStatisticsService {

    private static final List<String> RES_VM_STATUS_NOT_IN = Arrays.asList(ResVmStatus.DELETING,
        ResVmStatus.CREATE_FAILURE,
        ResVmStatus.PENDING,
        ResVmStatus.DELETED,
        ResVmStatus.CREATING);

    private static final List<String> RES_VM_SERVER_TYPE_IN = Arrays.asList(ServerType.SERVER_INSTANCE,
        ServerType.INSTANCE,
        ServerType.ELASTIC_INSTANCE);

    private static final List<String> FLOATING_IP_STATUS_NOT_IN = Arrays.asList(ResFloatingIpStatus.DELETED,
        ResFloatingIpStatus.CREATING,
        ResFloatingIpStatus.DELETING,
        ResFloatingIpStatus.ERROR);

    private static final List<String> VD_STATUS_NOT_IN = Arrays.asList(ResVdStatus.DELETED,
        ResVdStatus.CREATING,
        ResVdStatus.ERROR,
        ResVdStatus.DELETING,
        ResVdStatus.FAILURE);

    @Autowired
    private BizContractMapper contractMapper;

    @Autowired
    private StatisticsMapper statisticsMapper;

    @Autowired
    private OrgMapper orgMapper;

    @Autowired
    private ConfigMapper configMapper;

    public static final String EXPENSE_INFORMATION_KEY = "EXPENSE_INFORMATION_KEY";

    //消费分布各组织账单信息key值
    public static final String BILL_CONSUMPTION_DISTRIBUTION_KEY = "BILL_CONSUMPTION_DISTRIBUTION_KEY";


    public static final String INVOICED_CONSUMPTION_DISTRIBUTION_KEY = "INVOICED_CONSUMPTION_DISTRIBUTION_KEY";

    @Autowired
    private BizBillingAccountMapper bizBillingAccountMapper;

    @Autowired
    private IBizCouponAccountService bizCouponAccountService;

    @Autowired
    private OperationAnalysisMapper operationAnalysisMapper;

    @Autowired
    private IInstanceGaapCostService instanceGaapCostService;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private BizDistributorMapper bizDistributorMapper;

    @Autowired
    private OrgService orgService;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private ISfProductResourceService sfProductResourceService;

    @Autowired
    private Tracer tracer;

    @Autowired
    private SpanNamer spanNamer;
    @Autowired
    private IBizInvoiceService invoiceService;

    @Override
    public Map<String, Object> getStatistics(Long orgSid) {
        Map<String, Object> result = Maps.newHashMap();
        Criteria criteria = new Criteria();
        AuthUtil.setCustomerDataCriteria(criteria, AuthUtil.getAuthUser());
        if (orgSid != null) {
            criteria.put("orgSid", orgSid);
        }

        final CountDownLatch maxCountDown = new CountDownLatch(4);
        ExecutorService singleThreadPool = new ThreadPoolExecutor(4, 4,
                10L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(1024),
                new ThreadFactoryBuilder()
                        .setNameFormat("statistics-res-pool-%d")
                        .build());
        //根据当前角色获取数据得账户信息

        Set<Long> accountIds = instanceGaapCostService.findBillingAccountByCurrentUserRole(null);
        List<Long> interSection = Lists.newArrayList(accountIds);
        if(orgSid != null){
            List<Long> orgAccountIds = bizBillingAccountMapper.selectAccountIdByOrgId(orgSid);
            if(CollectionUtil.isNotEmpty(accountIds) && CollectionUtil.isNotEmpty(orgAccountIds)){
                interSection =  accountIds.stream().filter(item->orgAccountIds.contains(item)).collect(Collectors.toList());
                }
        }

        criteria.put("accountIds",interSection);
        Date start = new Date();

        // 1、充值
        singleThreadPool.execute(new TraceRunnable(tracer,spanNamer,() -> {
            try {
                result.put("recharge", getRechargeStatistics(criteria));
            } catch (Exception e) {
                log.error("统计充值数据出错", e);
            } finally {
                maxCountDown.countDown();
            }
        }));
        // 2、账单
        singleThreadPool.execute(new TraceRunnable(tracer,spanNamer,() -> {
            try {
                result.put("bill", getBillStatisticsBySummary(criteria));
            } catch (Exception e) {
                log.error("统计账单数据出错", e);
            } finally {
                maxCountDown.countDown();
            }
        }));

        // 3、订单
        singleThreadPool.execute(new TraceRunnable(tracer,spanNamer,() -> {
            try {
                result.put("order", getOrderStatistics(criteria));
            } catch (Exception e) {
                log.error("统计订单数据出错", e);
            } finally {
                maxCountDown.countDown();
            }
        }));

        // 4、消费分布
        singleThreadPool.execute(new TraceRunnable(tracer,spanNamer,() -> {
            try {
                result.put("consume", getConsumeStatistics(criteria));
            } catch (Exception e) {
                log.error("统计消费分布数据出错", e);
            } finally {
                maxCountDown.countDown();
            }
        }));
        singleThreadPool.shutdown();
        try {
            maxCountDown.await();
        } catch (InterruptedException e) {
            log.error("唤醒线程异常！", e);
            Thread.currentThread().interrupt();
        }
        Date end = new Date();
        log.info("getStatistics_耗时 = "+String.valueOf(end.getTime()-start.getTime()) );
        return result;
    }

    @Override
    public List<ConsumeTrendVO> getConsumeTrend(String period, Long orgSid) {
        List<Period> periods = new ArrayList<>();
        // 按周查询 或者 按月份查询
        if (Period.PeriodType.Daily.is(period)) {
            periods = DateUtil.getPeriod(LocalDate.now(), Period.PeriodType.Daily, 7);
        } else if (Period.PeriodType.Weekly.is(period)) {
            periods = DateUtil.getPeriod(LocalDate.now(), Period.PeriodType.Weekly, 4);
        } else if (Period.PeriodType.Monthly.is(period)) {
            periods = DateUtil.getPeriod(LocalDate.now(), Period.PeriodType.Monthly, 12);
        }
        Criteria criteria = new Criteria();
        AuthUtil.setCustomerDataCriteria(criteria, AuthUtil.getAuthUser());
        if (orgSid != null) {
            criteria.put("orgSid", orgSid);
        }
        Set<Long> accountIds = instanceGaapCostService.findBillingAccountByCurrentUserRole(null);
        List<Long>  interSection = Lists.newArrayList(accountIds);
        if(orgSid != null){
            List<Long> orgAccountIds = bizBillingAccountMapper.selectAccountIdByOrgId(orgSid);
            if(CollectionUtil.isNotEmpty(accountIds) && CollectionUtil.isNotEmpty(orgAccountIds)){
                interSection =  accountIds.stream().filter(item->orgAccountIds.contains(item)).collect(Collectors.toList());
            }
        }

        criteria.put("accountIds",interSection);
        criteria.put("entityId",RequestContextUtil.getEntityId());
        List<ConsumeTrendVO> consumeTrends = statisticsMapper.selectConsumeTrend(criteria);
        for (Period period1 : periods) {
            List<Period> periods1 = Arrays.asList(period1);
            addConsumerTrendBillAmounts(consumeTrends, Convert.toLong(criteria.get("orgSid")),periods1,accountIds);
        }

        Map<Date, ConsumeTrendVO> tempMap = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(consumeTrends)) {
            // 按日期分组处理
            Map<Date, List<ConsumeTrendVO>> consumeMaps = consumeTrends.stream()
                    .collect(Collectors.groupingBy(ConsumeTrendVO::getDataTime));
            consumeMaps.forEach((key, value) -> {
                ConsumeTrendVO consumeTrend = new ConsumeTrendVO();
                consumeTrend.setRechargeAmount(value.stream().map(ConsumeTrendVO::getRechargeAmount)
                        .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
                consumeTrend.setInvoiceAmount(value.stream().map(ConsumeTrendVO::getInvoiceAmount)
                        .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
                consumeTrend.setOrderAmount(value.stream().map(ConsumeTrendVO::getOrderAmount)
                        .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
                consumeTrend.setBillAmount(value.stream().map(ConsumeTrendVO::getBillAmount)
                        .reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
                tempMap.put(key, consumeTrend);
            });
        }

        List<ConsumeTrendVO> result = Lists.newArrayList();
        for (Period p : periods) {
            ConsumeTrendVO consumeTrend = new ConsumeTrendVO();
            tempMap.forEach((key, value) -> {
                if (p.isInPeriod(key)) {
                    consumeTrend.setBillAmount(consumeTrend.getBillAmount().add(tempMap.get(key).getBillAmount()));
                    consumeTrend.setOrderAmount(consumeTrend.getOrderAmount().add(tempMap.get(key).getOrderAmount()));
                    consumeTrend.setInvoiceAmount(consumeTrend.getInvoiceAmount().add(tempMap.get(key).getInvoiceAmount()));
                    consumeTrend.setRechargeAmount(consumeTrend.getRechargeAmount().add(tempMap.get(key).getRechargeAmount()));
                }
            });
            consumeTrend.setPeriod(p.getPeriodName());
            consumeTrend.setBillAmount(consumeTrend.getBillAmount().setScale(3,BigDecimal.ROUND_DOWN));
            consumeTrend.setOrderAmount(consumeTrend.getOrderAmount().setScale(3,BigDecimal.ROUND_DOWN));
            consumeTrend.setInvoiceAmount(consumeTrend.getInvoiceAmount().setScale(3,BigDecimal.ROUND_DOWN));
            consumeTrend.setRechargeAmount(consumeTrend.getRechargeAmount().setScale(3,BigDecimal.ROUND_DOWN));
            result.add(consumeTrend);
        }

        return result;
    }

    private Date localDate2Date(LocalDate localDate) {
        if(null == localDate) {
            return null;
        }
        ZonedDateTime zonedDateTime = localDate.atStartOfDay(ZoneId.systemDefault());
        return Date.from(zonedDateTime.toInstant());
    }

    private void addConsumerTrendBillAmounts(List<ConsumeTrendVO> consumeTrends, Long orgSid, List<Period> periods,Set<Long> accountIds) {
        org.springframework.data.mongodb.core.query.Criteria criteria = org.springframework.data.mongodb.core.query.Criteria
                .where("payTime").exists(true);
        if (Objects.nonNull(orgSid)) {
            List<Long> orgIds = orgService.selectChildrenOrgIds(orgSid);
            orgIds.add(orgSid);
            criteria.and("orgSid").in(orgIds);
        }
        org.springframework.data.mongodb.core.query.Criteria orCri = new org.springframework.data.mongodb.core.query.Criteria();
        List<org.springframework.data.mongodb.core.query.Criteria> criteriaList = new ArrayList<>();
        for (Period period : periods) {
            org.springframework.data.mongodb.core.query.Criteria payTimeCri = new org.springframework.data.mongodb.core.query.Criteria();
            payTimeCri.andOperator(org.springframework.data.mongodb.core.query.Criteria.where("payTime").gte(localDate2Date(period.getPeriodStart())),
                    org.springframework.data.mongodb.core.query.Criteria.where("payTime").lte(DateUtils.addDays(localDate2Date(period.getPeriodEnd()),1)));
            criteriaList.add(payTimeCri);
        }
        if(!CollectionUtil.isEmpty(criteriaList)){
            orCri.orOperator(criteriaList.toArray(new org.springframework.data.mongodb.core.query.Criteria[criteriaList.size()]));
        }
        criteria.andOperator(orCri);
        if (Objects.nonNull(accountIds)){
            criteria.and("userAccountId").in(accountIds);
        }
        Aggregation aggregation = Aggregation.newAggregation(Aggregation.match(criteria),
            Aggregation.project("orgSid", "payTime", "pretaxAmount","cashAmount","invoiceStatus","rechargeCreditAmount").andExpression(
                "{ $dateToString : {format : '%Y-%m-%d', date: '$payTime', timezone : 'Asia/Shanghai'}}")
                .as("dataTime")

        );
        AggregationResults<Map> aggregate = mongoTemplate
                .aggregate(aggregation, InstanceGaapCost.class, Map.class);
        Map<String, Map<String, List<Map>>> groupData = aggregate.getMappedResults().stream()
                .filter(map -> Objects.nonNull(MapUtil.getStr(map, "orgSid")) && Objects.nonNull(MapUtil.getStr(map, "dataTime")))
                .collect(Collectors.groupingBy(map -> MapUtil.getStr(map, "orgSid"),
                        Collectors.groupingBy(map -> MapUtil.getStr(map, "dataTime"))));
        for (Map<String, List<Map>> map : groupData.values()) {
            map.forEach((k, list) -> {
                ConsumeTrendVO vo = new ConsumeTrendVO();
                vo.setDataTime(cn.hutool.core.date.DateUtil.parseDate(k));
                vo.setBillAmount(list.stream().map(m -> MapUtil.getStr(m, "pretaxAmount"))
                    .filter(StrUtil::isNotEmpty).map(BigDecimal::new).reduce(BigDecimal::add)
                    .orElse(BigDecimal.ZERO));
                consumeTrends.add(vo);
            });
        }
//-------------------- 注释没用的代码
//        criteria = org.springframework.data.mongodb.core.query.Criteria
//                .where("invoiceDt").exists(true).and("invoiceStatus").is(InvoiceStatusEnum.DONE.getCode());
//        criteria.andOperator(orCri);
//        aggregation = Aggregation.newAggregation(Aggregation.match(criteria),
//                Aggregation.project("orgSid", "payTime", "pretaxAmount","cashAmount","invoiceStatus","invoiceDt","rechargeCreditAmount").andExpression(
//                        "{ $dateToString : {format : '%Y-%m-%d', date: '$invoiceDt', timezone : 'Asia/Shanghai'}}")
//                        .as("dataTime")
//
//        );
//        aggregate = mongoTemplate
//                .aggregate(aggregation, InstanceGaapCost.class, Map.class);
//        groupData = aggregate.getMappedResults().stream()
//                .collect(Collectors.groupingBy(map -> MapUtil.getStr(map, "orgSid"),
//                        Collectors.groupingBy(map -> MapUtil.getStr(map, "dataTime"))));
//
//        Collection<Map<String, List<Map>>> values = groupData.values();
//        for (Map<String, List<Map>> dateTimeMap : values) {
//            dateTimeMap.forEach((dataTime, list) -> {
//                Optional<ConsumeTrendVO> ConsumeTrendVoOp = consumeTrends.stream().filter(tend -> tend.getDataTime().equals(cn.hutool.core.date.DateUtil.parseDate(dataTime))).findFirst();
//                if(ConsumeTrendVoOp.isPresent()){
//
//                    BigDecimal rechargeCreditAmount = list.stream().map(m -> MapUtil.getStr(m, "rechargeCreditAmount"))
//                            .filter(StrUtil::isNotEmpty)
//                            .map(BigDecimal::new)
//                            .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
//
//                }
//            });
//        }
    }

    private void addConsumeBillAmounts(List<ConsumeVO> consumes, Long orgSid) {
        org.springframework.data.mongodb.core.query.Criteria criteria = new org.springframework.data.mongodb.core.query.Criteria();
        //查询系统的所有组织
        List<Org> orgs = orgService.list();
        List<Long> orgSids = new ArrayList<>();
        //获取组织及组织下的子组织id
        if (Objects.nonNull(orgSid)) {
            orgSids = Lists.newArrayList(orgSid);
            findOrgSidsByParentId(orgSid, orgs, orgSids);
        }
        //获取缓存数据
        if(CollectionUtil.isNotEmpty(consumes)){
            Map<String,String> billByOrgSid = JedisUtil.INSTANCE.hgetall(BILL_CONSUMPTION_DISTRIBUTION_KEY + "::"+ RequestContextUtil.getEntityId());
            Map<String,String> invoicedByOrgSid = JedisUtil.INSTANCE.hgetall(INVOICED_CONSUMPTION_DISTRIBUTION_KEY + "::"+ RequestContextUtil.getEntityId());
            for(ConsumeVO consumeVO: consumes){
                if(consumeVO.getOrgSid() != null){
                    if(billByOrgSid.get(consumeVO.getOrgSid().toString()) != null){
                        consumeVO.setBillAmount(new BigDecimal(billByOrgSid.get(consumeVO.getOrgSid().toString())));
                    }else{
                        consumeVO.setBillAmount(BigDecimal.ZERO);
                    }
                    if(invoicedByOrgSid.get(consumeVO.getOrgSid().toString()) != null){
                        consumeVO.setInvoiceAmount(new BigDecimal(invoicedByOrgSid.get(consumeVO.getOrgSid().toString())));
                    }else{
                        consumeVO.setInvoiceAmount(BigDecimal.ZERO);
                    }
                }
            }
        }

    }

    private void findOrgSidsByParentId(Long orgSid, List<Org> orgs, List<Long> orgSids){
        List<Org> children = orgs.stream().filter(org -> Objects.equals(orgSid, org.getParentId()))
            .collect(Collectors.toList());
        orgSids.addAll(children.stream().map(Org::getOrgSid).collect(Collectors.toList()));
        children.forEach(org -> findOrgSidsByParentId(org.getOrgSid(), orgs, orgSids));
    }

    @Override
    public Map<String, Object> getTopInfo(Long orgSid, Integer num) {
        Map<String, Object> result = Maps.newHashMap();
        Criteria criteria = new Criteria();
        AuthUtil.setCustomerDataCriteria(criteria, AuthUtil.getAuthUser());
        if (orgSid != null) {
            criteria.put("orgSid", orgSid);
        }

        // 1、账户余额
        result.put("balance", getTopOfAccountBalance(orgSid, num));
        // 2、用户到期资源
        result.put("resource", getTopOfResourceExpire(criteria, num));
        // 3、合同执行到期
        result.put("contract", getTopOfContract(orgSid, num));
        return result;
    }

    @Override
    public List<OrderNumAnalysisVO> getOrderNumAnalysis(Criteria criteria) {
        return operationAnalysisMapper.selectOrderNumAnalysis(criteria);
    }

    @Override
    public List<OrderTypeAnalysisVO> getOrderTypeAnalysis(Criteria criteria) {
        return operationAnalysisMapper.selectOrderTypeAnalysis(criteria);
    }

    @Override
    public IPage<OrderUserAnalysisVO> getOrderUserAnalysis(IPage<OrderUserAnalysisVO> page, Criteria criteria) {
        return operationAnalysisMapper.selectOrderUserAnalysis(page, criteria.getCondition());
    }

    /**
     * 获取充值统计信息
     * @return
     */
    private RechargeVO getRechargeStatistics(Criteria criteria) {
        return statisticsMapper.selectRechargeStatistics(criteria);
    }




    /**
     * 获取订单统计信息
     * @return
     */
    private OrderVO getOrderStatistics(Criteria criteria) {
        // 订单统计信息
        return statisticsMapper.selectOrderStatistics(criteria);
    }

    /**
     * 获取消费分布统计信息
     * @return
     */
    private List<ConsumeVO> getConsumeStatistics(Criteria criteria) {
        Date statr = new Date();
        //统计 平台充值 支付宝/微信余额充值
        criteria.put("channels",
                Arrays.asList(RechargeTypeEnum.ALI_PAY.getCode(),
                        RechargeTypeEnum.WECHAT_PAY.getCode(),
                        RechargeTypeEnum.PLATFORM.getCode(),
                        RechargeTypeEnum.UNION_PAY.getCode(),
                        RechargeTypeEnum.ACC_CREDIT.getCode(),
                        RechargeTypeEnum.ACC_BCASH.getCode()));
        List<ConsumeVO> consumes = statisticsMapper.selectConsumeStatistics(criteria);
        addConsumeBillAmounts(consumes, Convert.toLong(criteria.get("orgSid")));

        //分销商
        QueryWrapper<BizDistributor> tWrapper = new QueryWrapper<>();
            tWrapper.eq("entity_id", RequestContextUtil.getEntityId());
        List<BizDistributor> bizDistributors = bizDistributorMapper.selectList(tWrapper);
        List<Long> bizDistributorIdList = bizDistributors.stream().map(BizDistributor::getId).collect(Collectors.toList());

        List<ConsumeVO> parents = consumes.stream().filter(consume ->
            Objects.isNull(consume.getParentId()) || bizDistributorIdList.contains(consume.getParentId())).collect(Collectors.toList());


        List<ConsumeVO> children = consumes.stream().filter(consume -> Objects.nonNull(consume.getParentId()) && !bizDistributorIdList.contains(consume.getParentId())).collect(Collectors.toList());

        Map<Long, List<ConsumeVO>> childrenMap = children.stream().collect(Collectors.groupingBy(ConsumeVO::getParentId));

        parents.forEach(consumeVO -> {
            List<ConsumeVO> childrenConsumes = childrenMap.get(consumeVO.getOrgSid());
            if (Objects.isNull(childrenConsumes)) {
                return;
            }
            consumeVO.setBillAmount(consumeVO.getBillAmount().add(childrenConsumes.stream().map(ConsumeVO::getBillAmount)
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO)));
            consumeVO.setInvoiceAmount(consumeVO.getInvoiceAmount().add(childrenConsumes.stream().map(ConsumeVO::getInvoiceAmount)
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO)));
            consumeVO.setOrderAmount(consumeVO.getOrderAmount().add(childrenConsumes.stream().map(ConsumeVO::getOrderAmount)
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO)));
            consumeVO.setRechargeAmount(consumeVO.getRechargeAmount().add(childrenConsumes.stream().map(ConsumeVO::getRechargeAmount)
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO)));
        });
        // 统一处理小数位
        parents.forEach(consumeVO -> {
            consumeVO.setBillAmount(consumeVO.getBillAmount().setScale(5,BigDecimal.ROUND_HALF_UP));
            consumeVO.setInvoiceAmount(consumeVO.getInvoiceAmount().setScale(5,BigDecimal.ROUND_HALF_UP));
            consumeVO.setOrderAmount(consumeVO.getOrderAmount().setScale(5,BigDecimal.ROUND_HALF_UP));
            consumeVO.setRechargeAmount(consumeVO.getRechargeAmount().setScale(5,BigDecimal.ROUND_HALF_UP));
        });
        // 该组织运营管理员是否启用及禁用
        List<Long> orgSidList = userMapper.findOrgSidByEnableAndDisableUsers();
        parents = parents.stream().filter(t -> orgSidList.contains(t.getOrgSid())).collect(Collectors.toList());
        log.info("getConsumeStatistics================耗时：【{}】",String.valueOf(new Date().getTime()-statr.getTime()));
        return parents;
    }

    private void setChildConsumes(List<ConsumeVO> consumes, ConsumeVO parent) {
        consumes.forEach(e -> {
            if (Objects.equals(e.getParentId(), parent.getOrgSid())) {
                parent.getOrgConsumes().add(e);
                setChildConsumes(consumes, e);
            }
        });
    }
    /**
     * 获取用户账户余额top
     * @return
     */
    private List<BizBillingAccount> getTopOfAccountBalance(Long orgSid, Integer num) {
        Criteria query = new Criteria();
        query.put("orderByClause", "balance desc");
        if (orgSid != null) {
            List<Long> orgSids = orgMapper.selectCustomerAccountIds(orgSid, RequestContextUtil.getEntityId());
            if (orgSids.size() == 0) {return new ArrayList<BizBillingAccount>();}
            query.put("accountIds", orgSids);
        } else if(AuthUtil.getAuthUser().getUserType().equals(UserType.DISTRIBUTOR_USER)) {
            List<Long> orgSids = orgMapper.selectCustomerAccountIds(AuthUtil.getAuthUser().getOrgSid(),RequestContextUtil.getEntityId());
            if (orgSids.size() == 0) {return new ArrayList<BizBillingAccount>();}
            query.put("accountIds", orgSids);
        } else {
            query.put("entityId", RequestContextUtil.getEntityId());
        }
        List<String> strings = new ArrayList<>();
        // 禁用
        strings.add(UserStatus.AVAILABILITY);
        // 启用
        strings.add(UserStatus.FORBIDDEN);
        query.put("statusList", strings);
        //增加实体权限过滤
        query.put("entityId", RequestContextUtil.getEntityId());

        IPage<BizBillingAccount> page = new Page<>();
        page.setCurrent(1);
        if (num != null) {
            page.setSize(num);
        } else {
            page.setSize(10);
        }
        return bizBillingAccountMapper.selectByParams(page, query.getCondition()).getRecords();
    }

    /**
     * 获取用户合同到期top10
     * @return
     */
    private List<BizContract> getTopOfContract(Long orgSid, Integer num) {
        QueryWrapper<BizContract> query = new QueryWrapper<>();
        query.orderByAsc("end_time");
        query.ge("end_time", new Date());
        query.eq("contract_status", "audit_executing");
        query.eq("entity_id",RequestContextUtil.getEntityId());
        if (orgSid != null) {
            List<Long> orgSids = orgMapper.selectCustomerAccountIds(orgSid,RequestContextUtil.getEntityId());
            if (orgSids.size() == 0) {return new ArrayList<BizContract>();}
            query.in("account_id", orgSids);
        } else if(AuthUtil.getAuthUser().getUserType().equals(UserType.DISTRIBUTOR_USER)) {
            List<Long> orgSids = orgMapper.selectCustomerAccountIds(AuthUtil.getAuthUser().getOrgSid(),RequestContextUtil.getEntityId());
            if (orgSids.size() == 0) {return new ArrayList<BizContract>();}
            query.in("account_id", orgSids);
        }
        IPage<BizContract> page = new Page<>();
        page.setCurrent(1);
        if (num != null) {
            page.setSize(num);
        } else {
            page.setSize(10);
        }
        return contractMapper.selectPage(page, query).getRecords();
    }

    /**
     * 获取用户资源到期top10
     * @return
     */
    private List<ResourceVO> getTopOfResourceExpire(Criteria criteria1, Integer num) {
        List<ResourceVO> resource = new ArrayList<>();
        Long orgSid = (Long)criteria1.get("orgSid");

        // 先查询ModelArts过期资源
        modelArtsExprire(orgSid,num, resource);
        if (resource.size() == num) {
            return resource;
        }
        return resource;
    }

    private void modelArtsExprire(Long orgId,Integer num, List<ResourceVO> resource) {
        DescribeProductResourceRequest resourceRequest = new DescribeProductResourceRequest();
        resourceRequest.setPagenum(0L);
        resourceRequest.setPagesize(Long.valueOf(num));
        resourceRequest.setStatus("expired");
        resourceRequest.setMgtConsole(true);
        if(orgId != null){
            resourceRequest.setParentOrgSid(orgId);
        }
        IPage<DescribeProductResourceResponse> responsePage = sfProductResourceService.listResources(resourceRequest);
        long total = responsePage.getTotal();
        List<DescribeProductResourceResponse> records = responsePage.getRecords();
        if (CollectionUtil.isNotEmpty(records)) {
            for (DescribeProductResourceResponse record : records) {
                ResourceVO resourceVO = new ResourceVO();
                resourceVO.setName(record.getName());
                resourceVO.setEndTime(record.getEndTime());
                resourceVO.setType(record.getApplyType());
                resource.add(resourceVO);
            }
        }
    }

    @Override
    public AccountVO getAccountVO(Long accountId) {
        BizBillingAccount account = bizBillingAccountMapper.selectByPrimaryKey(accountId);

        DescribeAccountCouponRequest request = new DescribeAccountCouponRequest();
        request.setCouponStatus("unused");
        request.setAccountId(accountId);
        IPage<DescribeAccountCouponResponse> response = bizCouponAccountService.listCoupons(request);

        AccountVO accountVO = new AccountVO();
        accountVO.setAmount(Objects.isNull(account.getBalance()) ? BigDecimal.ZERO : account.getBalance().setScale(5,BigDecimal.ROUND_DOWN));
        accountVO.setCashAmount(account.getBalanceCash().setScale(5,BigDecimal.ROUND_DOWN));
        if (Objects.isNull(account.getCreditLine())) {
            accountVO.setCreditLine(new BigDecimal(0));
        } else {
            if (Objects.nonNull(account.getBalance())
                    && account.getBalance().compareTo(BigDecimal.valueOf(0)) < 0) {
                accountVO.setCreditLine(account.getCreditLine().add(account.getBalance()).compareTo(BigDecimal.valueOf(0)) < 0 ?
                    BigDecimal.valueOf(0) : account.getCreditLine().add(account.getBalance()));
            } else {
                accountVO.setCreditLine(account.getCreditLine());
            }
        }
        accountVO.setCoupon(0L);
        if (Objects.nonNull(response)) {
            response.getRecords().forEach(c -> {
                if (Objects.nonNull(c.getDiscountAmount())) {
                    accountVO.setCoupon(accountVO.getCoupon() + 1);
                }
            });
        }

        return accountVO;
    }

    @Override
    public ProjectOverviewVO getProjectOverviewCount(Criteria criteria) {
        criteria.put("statusNotIn", RES_VM_STATUS_NOT_IN);
        criteria.put("serverTypeIn", RES_VM_SERVER_TYPE_IN);
        ProjectOverviewVO result = new ProjectOverviewVO();
        ProjectOverviewVO vmOverview = statisticsMapper.selectProjectVmCount(criteria);
        if (vmOverview == null) {
            vmOverview = new ProjectOverviewVO();
        }
        result.setHostCount(vmOverview.getHostCount());
        result.setCpuCount(vmOverview.getCpuCount());
        result.setMemory(vmOverview.getMemory());

        criteria.put("statusNotIn", VD_STATUS_NOT_IN);
        ProjectOverviewVO vdOverview = statisticsMapper.selectProjectVdCount(criteria);
        if (vdOverview == null) {
            vdOverview = new ProjectOverviewVO();
        }
        result.setMount(vdOverview.getMount());
        result.setUnmount(vdOverview.getUnmount());
        result.setDisk(vdOverview.getMount() + vdOverview.getUnmount());

        criteria.put("statusNotIn", FLOATING_IP_STATUS_NOT_IN);
        ProjectOverviewVO ipOverview = statisticsMapper.selectProjectFloatingIpCount(criteria);
        if (ipOverview == null) {
            ipOverview = new ProjectOverviewVO();
        }
        result.setTotalEip(ipOverview.getTotalEip());

        ProjectOverviewVO lbOverview = statisticsMapper.selectProjectLbCount(criteria);
        if (lbOverview == null) {
            lbOverview = new ProjectOverviewVO();
        }
        result.setTotalLb(lbOverview.getTotalLb());
        return result;
    }

    @Override
    public Map<String, Object> getProjectStatistics(Criteria criteria, String period) {
        List<Period> periods = new ArrayList<>();
        LocalDate startDate = LocalDate.now().minusMonths(1);
        if (StrUtil.isEmpty(period)) {
            periods = DateUtil.getPeriod(startDate, LocalDate.now(), PeriodType.Daily);
        } else {
            // 按周查询 或者 按月份查询
            if (Period.PeriodType.Daily.is(period)) {
                periods = DateUtil.getPeriod(LocalDate.now(), Period.PeriodType.Daily, 7);
            } else if (Period.PeriodType.Weekly.is(period)) {
                periods = DateUtil.getPeriod(LocalDate.now(), Period.PeriodType.Weekly, 4);
            } else if (Period.PeriodType.Monthly.is(period)) {
                periods = DateUtil.getPeriod(LocalDate.now(), Period.PeriodType.Monthly, 12);
            }
        }

        criteria.put("startTime", LocalDateTime.of(startDate, LocalTime.MIN).toString());
        criteria.put("endTime", LocalDateTime.now().toString());
        criteria.put("vmStatusNotIn", RES_VM_STATUS_NOT_IN);
        criteria.put("vmServerTypeIn", RES_VM_SERVER_TYPE_IN);
        criteria.put("vdStatusNotIn", VD_STATUS_NOT_IN);
        criteria.put("ipStatusNotIn", FLOATING_IP_STATUS_NOT_IN);

        Map<String, Object> result = Maps.newHashMap();
        List<ProjectStatisticsVO> projectStatistics = statisticsMapper.selectProjectStatistics(criteria);
        Map<String, List<ProjectStatisticsVO>> statisticsMap = projectStatistics.stream()
            .collect(Collectors.groupingBy(ProjectStatisticsVO::getMeterType));

        List<ProjectStatisticsVO> ecsStatistics = Lists.newArrayList();
        List<ProjectStatisticsVO> diskStatistics = Lists.newArrayList();
        List<ProjectStatisticsVO> ipStatistics = Lists.newArrayList();

        for (Period p : periods) {
            statisticsMap.forEach((meterType, statistics) -> {
                ProjectStatisticsVO statisticsVO = new ProjectStatisticsVO();
                statisticsVO.setPeriod(p.getPeriodName());
                Map<String, BigDecimal> dateMap = statistics.stream().collect(Collectors.toMap(
                    ProjectStatisticsVO::getDataTime, ProjectStatisticsVO::getMeterage, (k1, k2) -> k1));
                statisticsVO.setMeterage(dateMap.getOrDefault(p.getPeriodName(), BigDecimal.ZERO));
                if (meterType.equals(ServiceManage.COMPUTE)) {
                    ecsStatistics.add(statisticsVO);
                } else if (meterType.equals(ServiceManage.DISK)) {
                    diskStatistics.add(statisticsVO);
                } else if (meterType.equals(ServiceManage.FLOATING_IP)) {
                    ipStatistics.add(statisticsVO);
                }
            });
        }
        result.put("ecs", ecsStatistics);
        result.put("disk", diskStatistics);
        result.put("floatingIp", ipStatistics);

        return result;
    }

    @Override
    public Map<String, Object> getProjectResStatistics(Criteria criteria, String period) {
        List<Period> periods = new ArrayList<>();
        LocalDate startDate = LocalDate.now().minusMonths(1);
        if (StrUtil.isEmpty(period)) {
            periods = DateUtil.getPeriod(startDate, LocalDate.now(), PeriodType.Daily);
        } else {
            // 按周查询 或者 按月份查询
            if (Period.PeriodType.Daily.is(period)) {
                periods = DateUtil.getPeriod(LocalDate.now(), Period.PeriodType.Daily, 7);
            } else if (Period.PeriodType.Weekly.is(period)) {
                periods = DateUtil.getPeriod(LocalDate.now(), Period.PeriodType.Weekly, 4);
            } else if (Period.PeriodType.Monthly.is(period)) {
                periods = DateUtil.getPeriod(LocalDate.now(), Period.PeriodType.Monthly, 12);
            }
        }

        criteria.put("startTime", LocalDateTime.of(startDate, LocalTime.MIN).toString());
        criteria.put("endTime", LocalDateTime.now().toString());
        criteria.put("computeCode", "ecs");
        List<ProjectStatisticsVO> resBillStatistics = statisticsMapper.selectResBillStatistics(criteria);
        Map<String, Map<String, List<ProjectStatisticsVO>>> billStatisticsMap = resBillStatistics.stream()
            .collect(Collectors.groupingBy(ProjectStatisticsVO::getProductCode,
                Collectors.groupingBy(ProjectStatisticsVO::getMeterType)));

        List<ProjectStatisticsVO> ecsStatistics = Lists.newArrayList();
        List<ProjectStatisticsVO> diskStatistics = Lists.newArrayList();
        List<ProjectStatisticsVO> ipStatistics = Lists.newArrayList();

        for (Period p : periods) {
            billStatisticsMap.forEach((productCode, billMap) -> {
                billMap.forEach((billType, billStatistics) -> {
                    ProjectStatisticsVO statisticsVO = new ProjectStatisticsVO();
                    statisticsVO.setPeriod(p.getPeriodName());
                    Map<String, BigDecimal> dateMap = billStatistics.stream().collect(Collectors.toMap(
                        ProjectStatisticsVO::getDataTime, ProjectStatisticsVO::getAmount, (k1, k2) -> k1));
                    statisticsVO.setAmount(dateMap.getOrDefault(p.getPeriodName(), BigDecimal.ZERO));
                    statisticsVO.setMeterType(billType);
                    if (productCode.equals(ServiceManage.COMPUTE)) {
                        ecsStatistics.add(statisticsVO);
                    } else if (productCode.equals(ServiceManage.DISK)) {
                        diskStatistics.add(statisticsVO);
                    } else if (productCode.equals(ServiceManage.FLOATING_IP)) {
                        ipStatistics.add(statisticsVO);
                    }
                });
            });
        }
        Map<String, Object> result = Maps.newHashMap();
        result.put("ecs", ecsStatistics.stream().collect(Collectors.groupingBy(ProjectStatisticsVO::getMeterType)));
        result.put("disk", diskStatistics.stream().collect(Collectors.groupingBy(ProjectStatisticsVO::getMeterType)));
        result.put("floatingIp", ipStatistics.stream().collect(Collectors.groupingBy(ProjectStatisticsVO::getMeterType)));
        return result;
    }

    /**
     * 获取账单统计信息
     * @return
     */
    private BillVO getBillStatisticsBySummary(Criteria criteria) {
        Date statr = new Date();
        Map<String, String>  costMap = new HashMap<>();
        BillVO resultBill = new BillVO();
        // 获取已开票账单
        BigDecimal invoiceAmount = BigDecimal.ZERO;
        // 获取累计出账
        BigDecimal totalBillAmount = BigDecimal.ZERO;
        // 获取当月出账
        BigDecimal currentBillAmount = BigDecimal.ZERO;
        DescribeGaapCostRequest request = new DescribeGaapCostRequest();
        Query query = new Query();
        org.springframework.data.mongodb.core.query.Criteria criteria1 = new org.springframework.data.mongodb.core.query.Criteria();
        List<Long> orgSids = new ArrayList<>();
        if (null != criteria.get("orgSid")) {
            request.setOrgSid(Long.valueOf(criteria.get("orgSid").toString()));
        }
        //当前组织及当前组织子组织
        if (Objects.nonNull(request.getOrgSid())) {
            orgSids = orgService.selectCustomerOrgSids(request.getOrgSid());
            //自己的组织id
            criteria1.and("orgSid").in(orgSids);
        } else if (Objects.isNull(request.getOrgSid()) && Objects.nonNull(AuthUtil.getAuthUser()) && Objects.nonNull(AuthUtil.getAuthUser().getOrgSid())) {
            //当前组织及当前组织子组织
            orgSids = orgService.selectCustomerOrgSids(AuthUtil.getAuthUser().getOrgSid());
            criteria1.and("orgSid").in(orgSids);
        }else{
            costMap = JedisUtil.INSTANCE.hgetall(EXPENSE_INFORMATION_KEY+ "::" + RequestContextUtil.getEntityId());
            if(CollectionUtil.isNotEmpty(costMap)){
                try {
                invoiceAmount = new BigDecimal(costMap.get("Invoice_Amount"));
                totalBillAmount = new BigDecimal(costMap.get("Total_Bill_Amount"));
                currentBillAmount = new BigDecimal(costMap.get("Current_Month_Bill_Amount"));
                } catch (Exception e) {
                    List<Org> orgs = orgService.findAll();
                    orgSids = orgs.stream().map(Org::getOrgSid).collect(Collectors.toList());
                    criteria1.and("orgSid").in(orgSids);
                    log.info("OperationStatisticsServiceImpl.getBillStatisticsBySummary 获取缓存数据异常 error: {}", e.getMessage());
                }
            }
        }
        criteria1.and("entityId").is(RequestContextUtil.getEntityId());
        if(CollectionUtil.isNotEmpty(orgSids) && orgSids.size() > 0) {
            Aggregation aggregation = Aggregation.newAggregation(Aggregation.match(criteria1),
                    Aggregation.project("orgSid", "pretaxAmount", "invoiceAmount", "totalBillAmount", "rechargeCreditAmount"));

            AggregationResults<InstanceGaapCostSummary> aggregate = mongoTemplate
                    .aggregate(aggregation, "biz_bill_usage_item_summary", InstanceGaapCostSummary.class);
            List<InstanceGaapCostSummary> summaryJobList = aggregate.getMappedResults();

            if (CollectionUtil.isNotEmpty(summaryJobList) && summaryJobList.size() > 0) {
                log.info("getBillStatisticsBySummary=========summaryJobList=======size【{}】", summaryJobList.size());
                for (InstanceGaapCostSummary costSummary : summaryJobList) {
                    invoiceAmount = invoiceAmount.add((costSummary.getInvoiceAmount() == null ? BigDecimal.ZERO : costSummary.getInvoiceAmount()).setScale(3, BigDecimal.ROUND_HALF_UP));
                }
            }

            org.springframework.data.mongodb.core.query.Criteria costCri = new org.springframework.data.mongodb.core.query.Criteria();
            if (CollectionUtil.isNotEmpty(orgSids) && orgSids.size() > 0) {
                costCri.and("orgSid").in(orgSids);
            }

            org.springframework.data.mongodb.core.query.Criteria criteria2 = new org.springframework.data.mongodb.core.query.Criteria();
            criteria2.and("summaryFlag").ne("Y");
            //分销商用户需要区分运营实体
            if(CollectionUtil.isNotEmpty(orgSids) && orgSids.size()>0){
                criteria2.and("orgSid").in(orgSids);
            }
            if (Objects.nonNull(criteria.get("accountIds"))) {
                criteria2.and("userAccountId").in(JSON.parseArray(criteria.get("accountIds").toString()));
            }
            Aggregation aggregation0 = Aggregation.newAggregation(
                    Aggregation.match(criteria2),
                    Aggregation.project("productCode", "userAccountId", "billingCycle", "orgSid", "pretaxAmount"),
                    Aggregation.addFields().addFieldWithValue("dubboPretaxAmount", toDecimal("$pretaxAmount")).build(),
                    Aggregation.project("productCode", "userAccountId", "billingCycle", "orgSid", "pretaxAmount", "dubboPretaxAmount").andExclude("_id"),
                    Aggregation.group("productCode").sum("dubboPretaxAmount").as("pretaxAmount"));
            List<InstanceGaapAmount> totalBillAmountList =
                    mongoTemplate.aggregate(aggregation0, "biz_bill_usage_item", InstanceGaapAmount.class).getMappedResults();
            if (!CollectionUtils.isEmpty(totalBillAmountList)) {
                log.info("OperationStatisticsServiceImpl-getBillStatisticsBySummary-totalBillAmountList.size:[{}]", totalBillAmountList.size());
                BigDecimal totalBillAmountSum =
                        totalBillAmountList.stream().map(InstanceGaapAmount::getPretaxAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                log.info("OperationStatisticsServiceImpl-getBillStatisticsBySummary-totalBillAmountList.sum:[{}]", totalBillAmountSum);
                totalBillAmount = BigDecimalUtil.scaleAndRoundHalfUp(totalBillAmount.add(totalBillAmountSum));
            }

            Date monthFistDay = DateUtil.getMonthFistDay();
            String curMonth = DateFormatUtils.format(monthFistDay, "yyyy-MM");

            Query unSummaryQuery = new Query();
            org.springframework.data.mongodb.core.query.Criteria unSummaryCri = org.springframework.data.mongodb.core.query.Criteria.where("billingCycle").is(curMonth);

            if (CollectionUtil.isNotEmpty(orgSids)) {
                unSummaryCri.and("orgSid").in(orgSids);
            }
            unSummaryQuery.addCriteria(unSummaryCri);
            Aggregation aggregation1 = Aggregation.newAggregation(
                    Aggregation.match(unSummaryCri),
                    Aggregation.project("billingCycle", "orgSid", "pretaxAmount"),
                    Aggregation.addFields().addFieldWithValue("dubboPretaxAmount", toDecimal("$pretaxAmount")).build(),
                    Aggregation.project("billingCycle", "orgSid", "pretaxAmount", "dubboPretaxAmount").andExclude("_id"),
                    Aggregation.group("billingCycle").sum("dubboPretaxAmount").as("pretaxAmount"));
            List<CurrentBillStatisticVO> unSummaryCostList1 =
                    mongoTemplate.aggregate(aggregation1, "biz_bill_usage_item", CurrentBillStatisticVO.class).getMappedResults();
            if (!CollectionUtils.isEmpty(unSummaryCostList1)) {
                log.info("OperationStatisticsServiceImpl-getBillStatisticsBySummary-unSummaryCostList1.size:[{}]", unSummaryCostList1.size());
                BigDecimal unSummaryAmount =
                        unSummaryCostList1.stream().map(CurrentBillStatisticVO::getPretaxAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                log.info("OperationStatisticsServiceImpl-getBillStatisticsBySummary-unSummaryCostList1.sum:[{}]", unSummaryAmount);
                currentBillAmount = BigDecimalUtil.scaleAndRoundHalfUp(currentBillAmount.add(unSummaryAmount));
            }

            //充值金额方式开票
            List<InvoiceDTO> invoiceDTOS = invoiceService.selectRechargeByOrgSid(orgSids, RequestContextUtil.getEntityId());
            if (!CollectionUtils.isEmpty(invoiceDTOS)) {
                for (InvoiceDTO invoice : invoiceDTOS) {
                    invoiceAmount = invoiceAmount.add((invoice.getDepositeAmount() == null ? BigDecimal.ZERO : invoice.getDepositeAmount()).setScale(3, BigDecimal.ROUND_HALF_UP));
                }
            }
        }
        resultBill.setInvoiceAmount(invoiceAmount);
        resultBill.setTotalBillAmount(totalBillAmount);
        resultBill.setCurrentBillAmount(currentBillAmount);
        log.info("getBillStatisticsBySummary================耗时：【{}】",String.valueOf(new Date().getTime()-statr.getTime()));
        return resultBill;
    }

    private Query getSummaryCriteria(List<Long> orgSids,Date start,Date end) {
        org.springframework.data.mongodb.core.query.Criteria criteria = new org.springframework.data.mongodb.core.query.Criteria();
        criteria.and("summaryFlag").ne("Y")
        ;
        if(CollectionUtil.isNotEmpty(orgSids) && orgSids.size()>0){
            criteria.and("orgSid").in(orgSids);
        }
        return new Query(criteria);
    }
    private Query getInvoiceCriteria(List<Long> orgSids,Date start,Date end) {
        org.springframework.data.mongodb.core.query.Criteria criteria = new org.springframework.data.mongodb.core.query.Criteria();
        criteria.and("summaryInvoiceFlag").ne("Y").and("invoiceStatus").is(InvoiceStatusEnum.DONE.getCode())
        ;
        if(CollectionUtil.isNotEmpty(orgSids) && orgSids.size()>0){
            criteria.and("orgSid").in(orgSids);
        }
        return new Query(criteria);
    }
    private Query getMonthCriteria(List<Long> orgSids,Date start,Date end) {
        org.springframework.data.mongodb.core.query.Criteria criteria = new org.springframework.data.mongodb.core.query.Criteria();
        if(CollectionUtil.isNotEmpty(orgSids) && orgSids.size()>0){
            criteria.and("orgSid").in(orgSids);
        }
        criteria.andOperator(org.springframework.data.mongodb.core.query.Criteria.where("payTime").gte(start),
                org.springframework.data.mongodb.core.query.Criteria.where("payTime").lte(end));
        return new Query(criteria);
    }

}
