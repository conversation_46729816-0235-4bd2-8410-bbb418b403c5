package cn.com.cloudstar.rightcloud.bss.module.sys.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.SysHpcPassMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.pojo.SysHpcPass;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.SysHpcPassService;
import cn.com.cloudstar.rightcloud.common.util.CrytoUtilSimple;

/**
 * <AUTHOR>
 * @Date 2023-02-23 16:51
 * @Desc
 */
@Service
public class SysHpcPassServiceImpl extends ServiceImpl<SysHpcPassMapper, SysHpcPass>
        implements SysHpcPassService {


    @Autowired
    private SysHpcPassMapper sysHpcPassMapper;

    @Override
    public String findPassword(Long userSid) {
        LambdaQueryWrapper<SysHpcPass> qw = new LambdaQueryWrapper<>();
        qw = qw.eq(SysHpcPass::getUserSid, userSid);
        SysHpcPass hpcPass = sysHpcPassMapper.selectOne(qw);
        if (hpcPass == null) {
            return null;
        } else {
            return CrytoUtilSimple.decrypt(hpcPass.getPassword());
        }
    }

    @Override
    public List<SysHpcPass> getByUserIds(Set<Long> ids) {
        LambdaQueryWrapper<SysHpcPass> qw = new LambdaQueryWrapper<>();
        qw.in(SysHpcPass::getUserSid, ids);
        return sysHpcPassMapper.selectList(qw);
    }
}
