/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */
package cn.com.cloudstar.rightcloud.bss.common.ccsp.strategy;

import cn.com.cloudstar.rightcloud.bss.module.account.mapper.BizBillingAccountMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * @description: 客户账户-国密处理
 * @author: ouyonghui
 * @date: 2023/4/3 14:05
 */
@Component
@RequiredArgsConstructor
public class CCSPBizBillingAccountStrategy extends AbstractCCSPStrategy<BizBillingAccount, Long> {
    private final BizBillingAccountMapper bizBillingAccountMapper;


    @Override
    public String getStrategy() {
        return BizBillingAccount.class.getName();
    }

    @Override
    public Boolean needEncryptDecrypt(BizBillingAccount data) {
        return true;
    }

    @Override
    public BizBillingAccount handle(BizBillingAccount data) {
        if (data.getSkipCCSPHandle() != null && data.getSkipCCSPHandle()) {
            return data;
        }
        return super.handle(data.getId(), data, bizBillingAccount -> bizBillingAccountMapper.selectByPrimaryKey(data.getId()));
    }
}
