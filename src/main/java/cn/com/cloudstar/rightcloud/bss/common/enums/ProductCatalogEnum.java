/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.common.enums;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * DESC:产品类别
 *
 * <AUTHOR>
 * @date 2019/10/24 14:47
 */
public enum ProductCatalogEnum {

    /**
     * 计算
     */
    COMPUTE("计算", "compute"),

    /**
     * 网络
     */
    NETWORK("网络", "floatingIp"),

    /**
     * 存储
     */
    VOLUME("存储", "disk");

    private String catalog;
    private String key;

    ProductCatalogEnum(String catalog, String key) {
        this.catalog = catalog;
        this.key = key;
    }

    public String getCatalog() {
        return catalog;
    }

    public String getKey() {
        return key;
    }

    public static List<String> keys() {
        List<String> result = Lists.newArrayList();
        ProductCatalogEnum[] enums = ProductCatalogEnum.values();
        for (ProductCatalogEnum catalog : enums) {
            result.add(catalog.getKey());
        }
        return result;
    }

}
