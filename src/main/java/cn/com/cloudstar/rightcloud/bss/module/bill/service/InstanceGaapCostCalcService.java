package cn.com.cloudstar.rightcloud.bss.module.bill.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapBuilder;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import cn.com.cloudstar.rightcloud.bss.common.enums.PayTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Org;
import cn.com.cloudstar.rightcloud.bss.module.account.mapper.BizBillingAccountMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.OrgService;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.BillBillingCycleCost;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.BillBillingCycleCostVo;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost;
import cn.com.cloudstar.rightcloud.oss.common.util.BigDecimalUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.DateUtil;

/**
 * 账单计算统一处理类
 *
 * <AUTHOR>
 */
@Service
@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
@RequiredArgsConstructor
public class InstanceGaapCostCalcService {

    MongoTemplate mongoTemplate;

    BizBillingAccountMapper bizBillingAccountMapper;

    OrgService orgService;

    public List<BillBillingCycleCostVo> getBillBillingCycleCost(Aggregation aggregation) {
        //获取账单结果
        AggregationResults<BillBillingCycleCost> costs = mongoTemplate.aggregate(aggregation, "biz_bill_billing_cycle",
                                                                                 BillBillingCycleCost.class);
        if (ObjectUtils.isEmpty(costs)) {
            return null;
        }
        List<BillBillingCycleCost> billingCycleCosts = costs.getMappedResults();
        //根据周期获取账单列表
        List<BillBillingCycleCostVo> costVos = billingCycleCosts.stream()
                                                                .map(this::getBillBillingCycleCostVo)
                                                                .filter(ObjectUtil::isNotEmpty)
                                                                .collect(Collectors.toList());
        if (ObjectUtils.isEmpty(costVos)) {
            return null;
        }
        List<Long> owenIds = costVos.stream()
                                    .map(BillBillingCycleCostVo::getOwnerId)
                                    .collect(Collectors.toList());
        List<Long> orgIds = costVos.stream()
                                   .map(BillBillingCycleCostVo::getOrgId)
                                   .collect(Collectors.toList());
        Map<Long, List<BizBillingAccount>> billingAccountMap = bizBillingAccountMapper.selectList(new QueryWrapper<BizBillingAccount>().in("id", owenIds))
                                                                                         .stream()
                                                                                         .collect(Collectors.groupingBy(BizBillingAccount::getId));
        Map<Long, List<Org>> orgMap = orgService.getOrgInfo(MapBuilder.create().put("orgIds", orgIds).build())
                                                .stream()
                                                .collect(Collectors.groupingBy(Org::getOrgSid));
        costVos.forEach(vo -> {
            BizBillingAccount billingAccount = billingAccountMap.get(vo.getOwnerId())
                                                                .stream()
                                                                .findFirst()
                                                                .orElse(null);
            if (!ObjectUtils.isEmpty(billingAccount)) {
                vo.setAccountName(billingAccount.getAccountName());
            }
            Org org = orgMap.get(vo.getOrgId())
                            .stream()
                            .findFirst()
                            .orElse(null);
            if (!ObjectUtils.isEmpty(org)) {
                vo.setOrgName(org.getOrgName());
                vo.setDistributorName(org.getDistributorName());
            }
            vo.setPayTimeStr(DateUtil.dateFormat(vo.getPayTime()));
        });
        return costVos;
    }


    private BillBillingCycleCostVo getBillBillingCycleCostVo(BillBillingCycleCost cost) {
        BillBillingCycleCostVo costVo = BeanUtil.copyProperties(cost, BillBillingCycleCostVo.class);
        //根据周期ids获取汇总成账单明细;
        if (ObjectUtils.isEmpty(cost.getIds())) {
            return null;
        }
        //根据账单周期id获取计算后的账单列表
        List<InstanceGaapCost> instanceGaapCosts = getBillBillingCycleCostByMonth(cost.getIds());
        if (ObjectUtils.isEmpty(instanceGaapCosts)) {
            return null;
        }
        //折扣后支付金额
        BigDecimal discountAmount = BigDecimal.ZERO;
        //现金支付金额
        BigDecimal cashAmount = BigDecimal.ZERO;
        //信用额度支付金额
        BigDecimal creditAmount = BigDecimal.ZERO;
        //充值现金券支付金额
        BigDecimal voucherAmount = BigDecimal.ZERO;
        BigDecimal invoiceAmount = BigDecimal.ZERO;
        //优惠券优惠金额
        BigDecimal couponDiscount = BigDecimal.ZERO;
        //抵扣现金券支付金额
        BigDecimal deductCouponDiscount = BigDecimal.ZERO;
        //套餐包优惠金额
        BigDecimal bizBagDiscount = BigDecimal.ZERO;
        //折扣优惠金额
        BigDecimal pricingDiscount = BigDecimal.ZERO;
        //原始金额
        BigDecimal pretaxGrossAmount =BigDecimal.ZERO;
        //抹零金额
        BigDecimal eraseZeroAmount =BigDecimal.ZERO;
        for (InstanceGaapCost monthCost : instanceGaapCosts) {
            discountAmount = discountAmount.add(NumberUtil.toBigDecimal(monthCost.getDiscountAmount()));
            cashAmount = cashAmount.add(NumberUtil.toBigDecimal(monthCost.getCashAmount()));
            creditAmount = creditAmount.add(NumberUtil.toBigDecimal(monthCost.getCreditAmount()));
            voucherAmount = voucherAmount.add(NumberUtil.toBigDecimal(monthCost.getCouponAmount()));
            invoiceAmount = invoiceAmount.add(NumberUtil.toBigDecimal(monthCost.getCashAmount()));
            couponDiscount = couponDiscount.add(NumberUtil.toBigDecimal(monthCost.getCouponDiscount()));
            deductCouponDiscount = deductCouponDiscount.add(NumberUtil.toBigDecimal(monthCost.getDeductCouponDiscount()));
            bizBagDiscount = bizBagDiscount.add(NumberUtil.toBigDecimal(monthCost.getBagDiscountAmount()));
            pricingDiscount = pricingDiscount.add(NumberUtil.toBigDecimal(monthCost.getPricingDiscount()));
            pretaxGrossAmount = pretaxGrossAmount.add(NumberUtil.toBigDecimal(monthCost.getPretaxGrossAmount()));
            eraseZeroAmount = eraseZeroAmount.add(NumberUtil.toBigDecimal(monthCost.getEraseZeroAmount()));
        }
        //原始金额
        costVo.setPretaxGrossAmount(pretaxGrossAmount.setScale(5, BigDecimal.ROUND_HALF_UP));
        //抹零金额
        costVo.setEraseZeroAmount(eraseZeroAmount);
        //实际支付金额= 现金支付金额+
        costVo.setAmount(cashAmount.add(creditAmount)
                               .add(voucherAmount)
                               .add(deductCouponDiscount)
                               .setScale(5, BigDecimal.ROUND_HALF_UP));
        //折扣后支付金额
        costVo.setDiscountAmount(discountAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
        //现金支付金额
        costVo.setCashAmount(cashAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
        //信用额度支付金额
        costVo.setCreditAmount(creditAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
        //充值现金券支付金额
        costVo.setVoucherAmount(voucherAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
        costVo.setInvoiceAmount(invoiceAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
        //优惠券优惠金额
        costVo.setCouponDiscount(couponDiscount.setScale(5, BigDecimal.ROUND_HALF_UP));
        //抵扣现金券支付金额
        costVo.setDeductCouponDiscount(deductCouponDiscount.setScale(2, BigDecimal.ROUND_HALF_UP));
        //套餐包优惠金额
        costVo.setBizBagDiscount(bizBagDiscount.setScale(5, BigDecimal.ROUND_HALF_UP));
        // 询价优惠金额
        costVo.setPricingDiscount( pricingDiscount.setScale(5, BigDecimal.ROUND_HALF_UP));
        //询价折扣金额

        //优惠金额=套餐包优惠+优惠卷折扣+充值现金券支付金额+抵扣现金券支付金额
        costVo.setDiscountTotalAmount(
                pricingDiscount.add(deductCouponDiscount)
                               .add(bizBagDiscount)
                               .add(voucherAmount)
                               .add(couponDiscount)
                               .setScale(5, BigDecimal.ROUND_HALF_UP));
        // 原始金额
        costVo.setPretaxGrossAmount(pretaxGrossAmount.setScale(5, BigDecimal.ROUND_HALF_UP));
        return costVo;
    }



    /**
     * 根据账单周期id获取计算后的账单列表
     *
     * <AUTHOR>
     */
    private List<InstanceGaapCost> getBillBillingCycleCostByMonth(List<String> cycleIds) {
        //账单周期列表
        //查询所有的账单周期,获取所有的账单周期的数据,根据账单周期列表
        List<BillBillingCycleCost> monthCycleCost = mongoTemplate.find(new Query(Criteria.where("id").in(cycleIds)),
                                                                       BillBillingCycleCost.class);
        if (ObjectUtils.isEmpty(monthCycleCost)) {
            return null;
        }
        return monthCycleCost.stream().map(cost -> {
            //退订的账期为空，就走账期表的数据
            //优惠券优惠金额
            BigDecimal applyCouponDiscount = NumberUtil.toBigDecimal(cost.getCouponDiscount());
            //套餐包优惠金额
            BigDecimal applyBizBagDiscount = NumberUtil.toBigDecimal(cost.getBagDiscountAmount());
            //抵扣现金券支付金额
            BigDecimal applyDeductCouponDiscount = NumberUtil.toBigDecimal(cost.getDeductCouponDiscount());
            //优惠后金额
            BigDecimal applyPretaxAmount = NumberUtil.toBigDecimal(cost.getAmount());
            //已分摊优惠后金额
            BigDecimal applyGaapPretaxAmount = NumberUtil.toBigDecimal(cost.getDiscountAmount());
            //现金支付金额
            BigDecimal applyCashAmount = NumberUtil.toBigDecimal(cost.getCashAmount());
            //信用额度支付金额
            BigDecimal applyCreditAmount = NumberUtil.toBigDecimal(cost.getCreditAmount());
            //充值现金券支付金额
            BigDecimal applyCouponAmount = NumberUtil.toBigDecimal(cost.getVoucherAmount());
            //抹零金额
            BigDecimal applyEraseZeroAmount = NumberUtil.toBigDecimal(cost.getEraseZeroAmount());
            //原始金额
            BigDecimal applyPretaxGrossAmount = NumberUtil.toBigDecimal(
                    Convert.toBigDecimal(cost.getOfficialAmount(), cost.getAmount()));
            //预付费得折扣优惠金额 = 原始金额- 优惠券优惠金额-所有支付金额,后付费得直接取表里面汇总得折扣优惠金额
            BigDecimal applyPricingDiscount = NumberUtil.toBigDecimal(cost.getPricingDiscount());
            //如果预付款
            if (PayTypeEnum.SUBSCRIPTION.getCode().equalsIgnoreCase(cost.getBillType())) {
                applyPricingDiscount = applyPretaxGrossAmount.subtract(applyCouponDiscount)
                                                             .subtract(applyCashAmount)
                                                             .subtract(applyCreditAmount)
                                                             .subtract(applyCouponAmount)
                                                             .subtract(applyDeductCouponDiscount)
                                                             .subtract(applyBizBagDiscount);
            }
            int roundType = BigDecimal.ROUND_HALF_UP;
            return InstanceGaapCost.builder()
                                   .pretaxGrossAmount(applyPretaxGrossAmount.setScale(5, roundType))
                                   .pretaxAmount(BigDecimalUtil.remainTwoPointAmount(applyPretaxAmount))
                                   .gaapPretaxAmount(applyGaapPretaxAmount.setScale(5, roundType))
                                   .cashAmount(applyCashAmount.setScale(2, roundType))
                                   .creditAmount(applyCreditAmount.setScale(2, roundType))
                                   .couponAmount(applyCouponAmount.setScale(2, roundType))
                                   .couponDiscount(applyCouponDiscount.setScale(2, roundType))
                                   .pricingDiscount(applyPricingDiscount.setScale(5, roundType))
                                   .deductCouponDiscount(applyDeductCouponDiscount.setScale(2, roundType))
                                   .bagDiscountAmount(applyBizBagDiscount.setScale(5, roundType))
                                   .eraseZeroAmount(applyEraseZeroAmount.setScale(5, roundType))
                                   .build();
        }).collect(Collectors.toList());
    }



}
