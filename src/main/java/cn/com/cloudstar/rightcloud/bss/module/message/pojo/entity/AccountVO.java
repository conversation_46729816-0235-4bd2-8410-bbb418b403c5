/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.message.pojo.entity;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @createDate 2020/03/23 15:20
 */
@ApiModel("账户简要信息")
@Data
public class AccountVO {

    private BigDecimal amount;

    private BigDecimal creditLine;

    private Long coupon;

    private Long cashCoupon;

    /**
     * 现金券余额
     */
    private BigDecimal cashAmount;
}
