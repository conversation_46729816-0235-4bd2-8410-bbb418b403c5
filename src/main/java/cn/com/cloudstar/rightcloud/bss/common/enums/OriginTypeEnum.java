/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.common.enums;


import cn.hutool.core.util.StrUtil;

/**
 * DESC:折扣来源
 *
 * <AUTHOR>
 * @date 2020/3/19 14:47
 */
public enum OriginTypeEnum {

    /**
     * 合同
     */
    CONTRACT("contract", "合同"),

    /**
     * 折扣
     */
    DISCOUNT("discount", "配置");

    private String code;
    private String name;

    OriginTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String codeFromName(String code) {
        if (code == null) {
            return StrUtil.EMPTY;
        }

        for (OriginTypeEnum value : OriginTypeEnum.values()) {
            if (value.code.equals(code)) {
                return value.name;
            }
        }

        return StrUtil.EMPTY;
    }

    public static OriginTypeEnum getEnum(String code) {
        for (OriginTypeEnum value : OriginTypeEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }


}
