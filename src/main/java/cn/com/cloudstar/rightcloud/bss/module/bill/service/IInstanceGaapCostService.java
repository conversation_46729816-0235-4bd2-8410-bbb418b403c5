/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.bill.service;

import cn.com.cloudstar.rightcloud.bss.common.pojo.Org;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.User;
import cn.com.cloudstar.rightcloud.bss.module.sys.pojo.request.ImportCustomerRequest;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.List;
import java.util.Map;
import java.util.Set;

import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizAccountDeal;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.entity.InstanceGaapCost;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.entity.OrderDetail;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.request.DescribeBizBagOrderRequest;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.request.DescribeGaapCostRequest;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.request.GaapCostDetailRequest;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.response.DescribeGaapCostResponse;
import cn.com.cloudstar.rightcloud.bss.module.sys.pojo.request.ImportCustomerRequest;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.BillBillingCycleCostVo;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;

/**
 * <p>
 * 月费用分摊服务 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-24
 */
public interface IInstanceGaapCostService extends IService<InstanceGaapCost> {

    /**
     * 查询账单
     *
     * @param request 请求参数
     * @return 账单
     */
    IPage<DescribeGaapCostResponse> listBills(DescribeGaapCostRequest request);

    /**
     * 查询导出账单
     *
     * @param request 请求参数
     * @return 账单
     */
    List<InstanceGaapCost> listExportBills(DescribeGaapCostRequest request, String id, Boolean excelFlg, AuthUser authUserInfo);

    List<InstanceGaapCost> exportBills(DescribeGaapCostRequest request);

    /**
     * 组装查询条件类
     */
    Criteria getCriteria(DescribeGaapCostRequest request, String id);

    /**
     * 查询计量账单数据
     *
     * @param request 请求参数
     * @return 账单
     */
    IPage<DescribeGaapCostResponse> listCalculateBills(DescribeGaapCostRequest request);

    /**
     * 计算账单费用
     * @param detail
     * @param chargeType
     * @param costs
     * @param deals
     */
    void handleOrderBill(OrderDetail detail, String chargeType, List<InstanceGaapCost> costs, List<BizAccountDeal> deals);

    /**
     * 根据当前用户角色获取所属账户ID
     * @return
     */
    Set<Long> findBillingAccountByCurrentUserRole(AuthUser authUserInfo);

    /**
     * 根据当前用户获取账户ID
     */
    Set<Long> findAccountIdsByCurrentLoginUser(AuthUser authUserInfo);

    /**
     * 查询账期数据
     *
     * @param request 请求参数
     * @return 账单
     */
    IPage<BillBillingCycleCostVo> listCycleBills(DescribeGaapCostRequest request);

    RestResult asynExportBillDetails(DescribeGaapCostRequest request, String moduleType);

    RestResult asynExportBillCycles(DescribeGaapCostRequest request, String moduleType);

    List<BillBillingCycleCostVo> listExportCycle(DescribeGaapCostRequest request, AuthUser authUserInfo);

    IPage<cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.InstanceGaapCost> listBillDetails(
            GaapCostDetailRequest request);

    RestResult asynExportBizBagBillDetails(DescribeGaapCostRequest request, String moduleType);

    RestResult asynExportBizBagOrder(DescribeBizBagOrderRequest request, String moduleType);

    RestResult asynExportCustomer(ImportCustomerRequest request);

    Long getCountExportBills(DescribeGaapCostRequest request, String id, AuthUser authUserInfo);

    RestResult asynExportFeginBillDetails(DescribeGaapCostRequest request, String moduleType);

    Map<Long, Org> getOrgMap();

    /**
     * req260 账单明细优化
     * 提供给upgrade模块处理历史数据
     */
    void upgradeOfBills();

    /**
     * 查询账单总数
     * @param request
     * @return
     */
    IPage listBillsCount(DescribeGaapCostRequest request);
}
