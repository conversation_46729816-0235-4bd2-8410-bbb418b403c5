/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.common.enums;


import cn.hutool.core.util.StrUtil;

/**
 * @Description 计费类型
 * <AUTHOR>
 * @Date: 2021/3/11 16:25
 */
public enum PriceTypeEnum {
    /**
     * 资源计费
     */
    RESOURCE("resource","资源计费","resource billing"),
    /**
     * 服务计费
     */
    SERVICE("service","服务计费","service billing"),
    /**
     * 额外配置
     */
    EXTRA_CONFIG("extraConfig","配置计费","configuring billing"),
    ;


    private String code;
    private String name;
    private String nameUs;

    PriceTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    PriceTypeEnum(String code, String name,String nameUs) {
        this.code = code;
        this.name = name;
        this.nameUs = nameUs;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getNameUs() {
        return nameUs;
    }

    public static String codeFromName(String code) {
        if (code == null) {
            return StrUtil.EMPTY;
        }

        for (PriceTypeEnum value : PriceTypeEnum.values()) {
            if (value.code.equals(code)) {
                return value.name;
            }
        }

        return code;
    }

    public static String code2NameWithI18n(String code, Boolean en) {
        for (PriceTypeEnum value : PriceTypeEnum.values()) {
            if (value.code.equals(code)) {
                return en ? value.nameUs : value.name;
            }
        }
        return code;
    }

    public static String nameFromCode(String name) {
        if (name == null) {
            return StrUtil.EMPTY;
        }

        for (PriceTypeEnum value : PriceTypeEnum.values()) {
            if (value.name.equals(name)) {
                return value.nameUs;
            }
        }

        return name;
    }

}
