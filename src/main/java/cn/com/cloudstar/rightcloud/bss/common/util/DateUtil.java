/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.common.util;

import org.springframework.util.ReflectionUtils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.Format;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Pattern;

import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.bss.common.pojo.Period;
import cn.com.cloudstar.rightcloud.bss.common.pojo.PeriodTime;

/**
 * <AUTHOR>
 * @date 2017/5/16
 */
@Slf4j
public class DateUtil {

    private static long NY = 1000 * 24 * 60 * 60 * 365L;
    private static long ND = 1000 * 24 * 60 * 60;
    private static long NH = 1000 * 60 * 60;
    private static long NM = 1000 * 60;
    private static String UTC_FORMAT_WITH_SECOND = "yyyy-MM-dd'T'HH:mm:ss'Z'";
    private static String UTC_FORMAT_WITHOUT_SECOND = "yyyy-MM-dd'T'HH:mm'Z'";
    private static String UTC_PATTERN_WITHOUT_SECOND = "^\\d{4}-\\d{1,2}-\\d{1,2}T\\d{2}:\\d{2}Z$";
    public static String COMMON_DATE_PATTERN = "yyyy-MM-dd HH:mm:ss";
    public static String MONTH_PATTERN = "yyyy-MM";

    /**
     * 将时间转为距离现在
     * @param date
     * @return
     * @throws Exception
     */
    public static String dateDiff(Date date){
        long year = 0;
        long day = 0;
        long hour = 0;
        long min = 0;
        long time=0;
        String strTime="";

        long diff = LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() - date.getTime();
        // 计算差多少年
        year = diff / NY;
        // 计算差多少天
        day = diff % NY / ND;
        // 计算差多少小时
        hour = diff % NY % ND / NH;
        // 计算差多少分钟
        min = diff % NY % ND % NH / NM ;
        if(year >= 1){
            time=year;
            strTime=time+"年前";
        }else if(day>=1 && day < 365){
            time=day;
            strTime=time+"天前";
        } else if(hour>=1 && hour<24){
            time=hour;
            strTime=time+"小时前";
        } else if(min >= 1 && min < 60){
            time=min;
            strTime=time+"分钟前";
        } else{
            strTime="刚刚";
        }

        return strTime;
    }

    /**
     * Date 转化为LocalDate
     */
    public static LocalDate convertDateToLocalDate(Date date) {
        Instant instant = date.toInstant();
        ZoneId zone = ZoneId.systemDefault();
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, zone);
        LocalDate localDate = localDateTime.toLocalDate();
        return localDate;
    }

    /**
     * UNIX时间戳转换成指定格式日期字符串
     *
     * @param timeStampString 时间戳 如："1473048265";
     * @return 返回结果 如："2016-09-05 16:06:42";
     */
    public static String timeStamp2Date(String timeStampString, String formats) {
        Long timestamp = Long.parseLong(timeStampString) * 1000;
        String date = new SimpleDateFormat(formats, Locale.CHINA).format(new Date(timestamp));
        return date;
    }

    /**
     * 日期格式字符串转换成UNIX时间戳
     *
     * @param dateStr 字符串日期
     * @param format  如：yyyy-MM-dd HH:mm:ss
     */
    public static String date2TimeStamp(String dateStr, String format) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(format);
            return String.valueOf(sdf.parse(dateStr).getTime() / 1000);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return "";
    }

    /**
     * LocalDate 转化为Date
     */
    public static Date convertLocalDateToDate(LocalDate localDate) {
        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime zonedDateTime = localDate.atStartOfDay(zoneId);
        Date date = Date.from(zonedDateTime.toInstant());
        return date;
    }


    /**
     * 获取两个日期相差的小时数
     *
     * @param now  当前日期
     * @param date 数据的创建日期
     * @return 小时差
     */
    public static Long getHourDiff(Date now, Date date) {

        long diff = now.getTime() - date.getTime();
        long day = diff / ND;
        // 计算差多少小时
        long hour = diff % ND / NH + day * 24;
        return hour;
    }

    /**
     * 获取两时间间隔小时数
     *
     * @param roundingMode 舍入模式BigDecimal.*
     */
    public static Long getHourDiffHalfUp(Date now, Date date, int roundingMode) {
        long seconds = Math.abs(now.getTime() - date.getTime()) / 1000;
        BigDecimal minutes = new BigDecimal(seconds).divide(new BigDecimal(60), 0, roundingMode);
        BigDecimal hours = minutes.divide(new BigDecimal(60), 0, roundingMode);

        return hours.longValue();
    }

    /**
     * 获取两个日期相差的分钟数
     *
     * @param now  当前日期
     * @param date 数据的创建日期
     * @return 分钟差
     */
    public static Long getSecondDiff(Date now, Date date) {
        long diff = now.getTime() / (1000) - date.getTime() / (1000);
        return diff;
    }

    /**
     * 获取两个时间相差的天数
     *
     * @param now  当前日期
     * @param date 数据的创建日期
     * @return 天数差
     */
    public static Long getDayDiff(Date now, Date date) {
        long diff = now.getTime() - date.getTime();
        long day = diff / ND;
        return day;
    }

    /**
     * 根据创建时间和现在的时间获取数组
     *
     * @param hour  创建时间
     * @param now   现在的时间
     * @param array 数组
     * @return 数组
     */
    public static int[] getArrayOfHour(Date hour, Date now, int[] array, int num) {
        int index = getHourIndex(hour, now);
        for (int i = index; i < array.length; i++) {
            array[i] += num;
        }
        return array;
    }

    /**
     * 获得插入数据的位置
     */
    public static int getHourIndex(Date hour, Date now) {
        Date[] hourNum = getHourNum(now);
        int index = 0;
        for (int i = 0; i < hourNum.length; i++) {
            if (hour.getTime() < hourNum[0].getTime()) {
                index = 0;
                break;
            }
            if (hour.getTime() > hourNum[i].getTime() && hour.getTime() < hourNum[i + 1].getTime()) {
                index = i + 1;
                break;
            }
        }
        return index;
    }

    /**
     * 小时数组
     *
     * @return 小时数组
     */
    public static Date[] getHourNum(Date date) {
        Date[] xAxis = new Date[7];
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        for (int i = 6; i >= 0; i--) {
            xAxis[i] = calendar.getTime();
            if (i == 1) {
                calendar.add(Calendar.HOUR_OF_DAY, -3);
            } else {
                calendar.add(Calendar.HOUR_OF_DAY, -4);
            }
        }
        return xAxis;
    }

    /**
     * 根据创建时间和现在的时间获取数组
     *
     * @param day    创建时间
     * @param nowDay 现在的时间
     * @param array  数组
     * @return 数组
     */
    public static int[] getArrayOfWeek(Date day, Date nowDay, int[] array, int num) {
        int index = getWeekIndex(day, nowDay);
        for (int i = index; i < array.length; i++) {
            array[i] += num;
        }
        return array;
    }

    /**
     * 获得插入数据的位置
     */
    public static int getWeekIndex(Date day, Date nowDay) {
        Date[] week = getWeekNum(nowDay);
        int index = 0;
        for (int i = 0; i < week.length; i++) {
            if (day.getTime() > week[i].getTime() && day.getTime() < week[i + 1].getTime()) {
                index = i + 1;
                break;
            } else if (day.getTime() <= week[0].getTime()) {
                index = 0;
            }
        }
        return index;
    }

    /**
     * 星期数组
     *
     * @return X轴的星期数
     */
    public static Date[] getWeekNum(Date nowDay) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(nowDay);
        Date[] xAxis = new Date[7];
        for (int i = xAxis.length - 1; i >= 0; i--) {
            xAxis[i] = calendar.getTime();
            calendar.add(Calendar.DAY_OF_WEEK, -1);
        }
        return xAxis;
    }

    /**
     * 获取总览界面计算部分的X轴坐标数组，以小时区分
     *
     * @return X轴的坐标数组
     */
    public static Date[] getXAxisOfHour(Date hour) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(hour);
        Date[] hourNum = getHourNum(hour);
        return hourNum;
    }

    /**
     * 星期数组
     *
     * @return X轴的星期数
     */
    public static String[] getWeek(Date date) {
        SimpleDateFormat format = new SimpleDateFormat("MM-dd");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        String[] weekString = new String[7];
        for (int i = weekString.length - 1; i >= 0; i--) {
            weekString[i] = format.format(calendar.getTime());
            calendar.set(Calendar.DATE, calendar.get(Calendar.DATE) - 1);
        }
        return weekString;
    }

    /**
     * 获得提示框内容
     */
    public static Map[] getTipsArrayOfHour(Object key, Date time, int size, Map[] maps, Date nowHour) {
        int index = getHourIndex(time, nowHour);
        for (int i = index; i < maps.length; i++) {
            if (maps[i].containsKey(key)) {
                maps[i].put(key, ((HashMap<String, Integer>) maps[i]).get(key) + size);
            } else {
                maps[i].put(key, size);
            }
        }
        return maps;
    }

    /**
     * 获得提示框内容
     */
    public static Map[] getTipsArrayOfWeek(Object key, Date day, int size, Map[] maps, Date nowDay) {
        int index = getWeekIndex(day, nowDay);
        for (int i = index; i < maps.length; i++) {
            if (maps[i].containsKey(key)) {
                maps[i].put(key, ((HashMap<String, Integer>) maps[i]).get(key) + size);
            } else {
                maps[i].put(key, size);
            }
        }
        return maps;
    }

    /**
     * 将UTC格式的时间转化为Date格式
     */
    public static Date utcToDate(String utc) {
        Pattern utcPatternWithoutSecond = Pattern.compile(UTC_PATTERN_WITHOUT_SECOND);
        try {
            SimpleDateFormat sdf = null;
            if (utcPatternWithoutSecond.matcher(utc).matches()) {
                sdf = new SimpleDateFormat(UTC_FORMAT_WITHOUT_SECOND);
            } else {
                sdf = new SimpleDateFormat(UTC_FORMAT_WITH_SECOND);
            }
            Date date = sdf.parse(utc);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.set(Calendar.HOUR, calendar.get(Calendar.HOUR) + 8);
            return calendar.getTime();
        } catch (ParseException e) {
            log.error(e.getMessage());
        }
        return null;
    }

    /**
     * 将UTC格式的时间转化为Date格式 不加8小时
     */
    public static Date utcToDateNoAddTime(String utc) {
        Pattern utcPatternWithoutSecond = Pattern.compile(UTC_PATTERN_WITHOUT_SECOND);
        try {
            SimpleDateFormat sdf = null;
            if (utcPatternWithoutSecond.matcher(utc).matches()) {
                sdf = new SimpleDateFormat(UTC_FORMAT_WITHOUT_SECOND);
            } else {
                sdf = new SimpleDateFormat(UTC_FORMAT_WITH_SECOND);
            }
            Date date = sdf.parse(utc);
            return date;
        } catch (ParseException e) {
            log.error(e.getMessage());
        }
        return null;
    }
    public static Date utcToDate(Date utc) {
        try {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(utc);
            calendar.set(Calendar.HOUR, calendar.get(Calendar.HOUR) + 8);
            return calendar.getTime();
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return null;
    }
    public static Date localDate2UtcDate(String localDateStr){
        Date localDate =  parseDate(localDateStr,COMMON_DATE_PATTERN);
        return localDate2UtcDate(localDate);
    }
    public static Date localDate2UtcDate(Date localDate){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(localDate);
        calendar.add(Calendar.HOUR, -8);
        return calendar.getTime();
    }

    public static String localDate2Utc(String localDateStr){
        DateFormat dateFormat = new SimpleDateFormat(COMMON_DATE_PATTERN);
        return dateFormat.format(localDate2UtcDate(localDateStr));
    }
    public static String localDate2Utc(Date localDate){
        DateFormat dateFormat = new SimpleDateFormat(COMMON_DATE_PATTERN);
        return dateFormat.format(localDate2UtcDate(localDate));
    }


    public static Date formatDate(String date) {
        try {
            DateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            return format.parse(date);
        } catch (ParseException e) {
            log.error(e.getMessage());
        }
        return null;
    }
    public static Date parseDate(String date, String pattern) {
        try {
            DateFormat format = new SimpleDateFormat(pattern);
            return format.parse(date);
        } catch (ParseException e) {
            log.error(e.getMessage());
        }
        return null;
    }

    public static String formatDate(long date, String pattern) {
        try {
            DateFormat format = new SimpleDateFormat(pattern);
            return format.format(new Date(date));
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return null;
    }

    /**
     * 获取上一个时间段
     */
    public static Date getBeforeData(Date date, boolean isDay) {
        Calendar currentDate = Calendar.getInstance();
        currentDate.setTime(date);
        if (isDay) {
            currentDate.add(Calendar.HOUR_OF_DAY, -4);
        } else {
            currentDate.add(Calendar.DAY_OF_MONTH, -1);
        }

        Date nextDate = currentDate.getTime();
        return nextDate;
    }

    /**
     * 当天的开始时间
     * @return
     */
    public static String startOfToDay(boolean returnTimestamp) {
        Calendar currentDate = Calendar.getInstance();
        //日期回滚1天
        currentDate.add(Calendar.DAY_OF_MONTH, -1);

        Date date = currentDate.getTime();
        if (returnTimestamp) {
            return String.valueOf(date.getTime()/1000);
        } else {
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String timeStr = df.format(date.getTime());
            return Timestamp.valueOf(timeStr).toString();
        }
    }

    public static String startOfToDayFormat() {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar currentDate = Calendar.getInstance();
        //日期回滚1天
        currentDate.add(Calendar.DAY_OF_MONTH, -1);
        Date date = currentDate.getTime();
        return df.format(date);
    }

    /**
     * 当天的开始时间
     * @return
     */
    public static Date getNextDate(Date date) {
        Calendar currentDate = Calendar.getInstance();
        currentDate.setTime(date);
        currentDate.add(Calendar.DAY_OF_MONTH, 1);
        return currentDate.getTime();
    }

    /**
     * 当天的结束时间
     * @return
     */
    public static String endOfTodDay(boolean returnTimestamp) {
        Calendar calendar = Calendar.getInstance();
        Date date=calendar.getTime();
        if (returnTimestamp) {
            return String.valueOf(date.getTime()/1000);
        } else {
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String timeStr = df.format(date.getTime());
            return Timestamp.valueOf(timeStr).toString();
        }
    }

    public static String endOfTodDayFormat() {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar calendar = Calendar.getInstance();
        Date date=calendar.getTime();
        return df.format(date);
    }

    /**
     * 功能：获取本周的开始时间 示例：2013-05-13 00:00:00
     * @param returnTimestamp 当前开始时间
     */
    public static String startOfThisWeek(boolean returnTimestamp) {
        Calendar currentDate = Calendar.getInstance();
        //日期回滚7天
        currentDate.add(Calendar.DAY_OF_MONTH, -7);
        Date date = currentDate.getTime();
        if (returnTimestamp) {
            return String.valueOf(date.getTime()/1000);
        } else {
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String timeStr = df.format(date.getTime());
            return Timestamp.valueOf(timeStr).toString();
        }
    }

    public static String startOfThisWeekFormat() {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar currentDate = Calendar.getInstance();
        //日期回滚7天
        currentDate.add(Calendar.DAY_OF_MONTH, -7);
        Date date = currentDate.getTime();
        return df.format(date);
    }

    /**
     * 功能：获取本周的结束时间 示例：2013-05-19 23:59:59
     */
    public static String endOfThisWeek(boolean returnTimestamp) {// 当周结束时间
        Calendar currentDate = Calendar.getInstance();
        currentDate.setFirstDayOfWeek(Calendar.MONDAY);
        currentDate.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY);
        Date date = currentDate.getTime();
        if (returnTimestamp) {
            return String.valueOf(date.getTime()/1000);
        } else {
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String timeStr = df.format(date.getTime());
            return Timestamp.valueOf(timeStr).toString();
        }
    }

    public static String dateFormat(Date date) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String timeStr = df.format(date.getTime());
        return Timestamp.valueOf(timeStr).toString();
    }

    public static String dateFormat(Date date,String format) {
        SimpleDateFormat df = new SimpleDateFormat(format);
        String timeStr = df.format(date.getTime());
        return timeStr;
    }

    public static String dateFormat(Long date, String format) {
        SimpleDateFormat df = new SimpleDateFormat(format);
        return df.format(date);
    }

    public static Date timestap2Date(long timestap) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Long time = Long.valueOf(timestap);
        String d = format.format(time);
        Date date;
        try {
            date = format.parse(d);
            return date;
        } catch (ParseException e) {
            log.error(e.getMessage());
            return null;
        }
    }

    public static List<PeriodTime> getPeriodTime(Date start, Date end){
        Calendar maxTime = Calendar.getInstance();
        maxTime.setTime(end);

        List<PeriodTime> periodTimes = new ArrayList<>();
        Calendar endTime = Calendar.getInstance();
        Calendar startTime = Calendar.getInstance();
        endTime.setTime(end);
        startTime.setTime(start);
        startTime.set(Calendar.DATE, startTime.get(Calendar.DATE) + 1);
        startTime.set(Calendar.HOUR_OF_DAY,0);
        startTime.set(Calendar.MINUTE,0);
        startTime.set(Calendar.SECOND,0);
        if(startTime.compareTo(maxTime) >= 0){
            PeriodTime periodTime = PeriodTime.builder().startDate(start).endDate(end).build();
            periodTimes.add(periodTime);
            return periodTimes;
        }
        PeriodTime pt1 = PeriodTime.builder().startDate(start).endDate(startTime.getTime()).build();
        periodTimes.add(pt1);
        while (startTime.compareTo(maxTime) < 0){
            Date currStar = startTime.getTime();
            startTime.set(Calendar.DATE,startTime.get(Calendar.DATE) + 1);
            startTime.set(Calendar.HOUR_OF_DAY,0);
            startTime.set(Calendar.MINUTE,0);
            startTime.set(Calendar.SECOND,0);
            if(startTime.getTime().compareTo(maxTime.getTime()) >= 0){
                startTime = maxTime;
            }
            PeriodTime pt = PeriodTime.builder().startDate(currStar).endDate(startTime.getTime()).build();
            periodTimes.add(pt);
        }
        return periodTimes;
    }

    public static List<Period> getPeriod(LocalDate start, LocalDate end, Period.PeriodType periodType) {
        List<Period> periods = new ArrayList<>();
        Period thisPeriod = new Period(end, periodType);
        //月结束时间以传入为准
        if (Period.PeriodType.Monthly.equals(periodType)) {
            thisPeriod.setPeriodEnd(end);
        }
        //开始于结束同属一月则period的开始结束以传入为准
        if (start.getMonthValue() == end.getMonthValue() && Period.PeriodType.Monthly.equals(periodType)) {
            thisPeriod.setPeriodStart(start);
        }
        periods.add(thisPeriod);
        if (!start.isEqual(end)) {
            for (int i = 1; ; i++) {
                if (start.getMonthValue() == end.getMonthValue() && Period.PeriodType.Monthly.equals(periodType)) {
                    break;
                }
                Period curr = new Period(periods.get(i - 1).getPeriodStart().minusDays(1), periodType);
                LocalDate minStart = curr.getPeriodStart();
                if (minStart.isBefore(start) || minStart.isEqual(start)) {
                    long exitsCount = periods.stream().filter(per -> per.getPeriodStart().isEqual(start)).count();
                    if (exitsCount == 0) {
                        curr.setPeriodStart(start);
                        periods.add(curr);
                    }
                    break;
                }
                periods.add(curr);
            }
        } else {
            thisPeriod.setPeriodStart(start);
            thisPeriod.setPeriodEnd(end);
        }
        periods.sort(Comparator.comparing(Period::getPeriodStart));
        return periods;
    }

    public static List<Period> getPeriod(LocalDate now, Period.PeriodType periodType, int periodNum) {
        return getPeriod(now, periodType, periodNum, true);
    }

    public static List<Period> getPeriod(LocalDate now, Period.PeriodType periodType, int periodNum, boolean ascOrder) {
        List<Period> periods = new ArrayList<>(periodNum);
        Period thisPeriod = new Period(now, periodType);
        periods.add(thisPeriod);
        for (int i = 1; i < periodNum; i++) {
            periods.add(new Period(periods.get(i - 1).getPeriodStart().minusDays(1), periodType));
        }

        if (ascOrder) {
            periods.sort(Comparator.comparing(Period::getPeriodStart));
        }

        return periods;
    }

    /**
     * 获取开始时间与当前时间的时差
     */
    public static String parseDateDiff(Date startTime) {
        if (Objects.isNull(startTime)) {
            return "";
        }
        Long minute = 1000 * 60L;
        Long hour = 1000 * 60 * 60L;
        Long day = 1000 * 60 * 60 * 24L;
        Long year = 1000 * 60 * 60 * 24 * 365L;


        Date now = Calendar.getInstance().getTime();
        Float diff = Float.valueOf(now.getTime() - startTime.getTime());
        if (diff < minute) {
            return "刚刚";
        } else if (diff < hour) {
            return Math.round(diff / minute) + "分钟";
        } else if (diff < day) {
            return Math.round(diff / hour) + "小时";
        } else if (diff < year) {
            return Math.round(diff / day) + "天";
        } else {
            return Math.floor(diff / year) + "年以上";
        }
    }

    public static String licenseExpireDay(long diffTime) {
        if (diffTime < 0) {
            return "已经过期";
        }
        long nd = 1000 * 24 * 60 * 60;
        long day = diffTime / nd;
        if (day > 0) {
            return "还剩" + day + "天";
        } else {
            return "还剩" + "不到一天";
        }
    }


    /**
     * 获取几天后或几天前的日期
     * @param date
     * @param day
     * @return
     */
    public static Date getDateByDay(Date date, int day) {
        Calendar now = Calendar.getInstance();
        now.setTime(date);
        now.set(Calendar.DATE, now.get(Calendar.DATE) + day);
        return now.getTime();
    }

    public static Integer calculateOffDay(Date startTime, Date endTime) {
        if (Objects.isNull(startTime) || Objects.isNull(endTime)) {
            return 0;
        }

        if (!startTime.before(endTime)) {
            return 0;
        }

        int offset = (int) Math
            .ceil((endTime.getTime() - startTime.getTime()) / ((double) 1000 * 60 * 60 * 24));
        return offset == 0 ? 1 : offset;
    }

    /**
     * 获取當月第一天
     * @return
     */
    public static Date getMonthFistDay() {
        // 获取当月账单
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        //将小时至0
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        //将分钟至0
        calendar.set(Calendar.MINUTE, 0);
        //将秒至0
        calendar.set(Calendar.SECOND, 0);
        //将毫秒至0
        calendar.set(Calendar.MILLISECOND, 0);
        //获得当前月第一天
        return calendar.getTime();
    }

    /**
     * 获取当月最后一天
     * @return
     */
    public static Date getMonthLastDay() {
        // 获取当月账单
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        //将小时至0
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        //将分钟至0
        calendar.set(Calendar.MINUTE, 0);
        //将秒至0
        calendar.set(Calendar.SECOND, 0);
        //将毫秒至0
        calendar.set(Calendar.MILLISECOND, 0);
        //将当前月加1；
        calendar.add(Calendar.MONTH, 1);
        //在当前月的下一月基础上减去1毫秒
        calendar.add(Calendar.MILLISECOND, -1);
        //获得当前月最后一天
        return calendar.getTime();
    }
    //获取前月的第一天
    public static Date getFirstDay(){
        Calendar cal_1=Calendar.getInstance();//获取当前日期
        cal_1.add(Calendar.MONTH, -1);
        cal_1.set(Calendar.DAY_OF_MONTH,1);//设置为1号,当前日期既为本月第一天
        cal_1.set(Calendar.HOUR_OF_DAY,0);
        cal_1.set(Calendar.MINUTE,0);
        cal_1.set(Calendar.SECOND,0);
        cal_1.set(Calendar.MILLISECOND,0);
        Date time = cal_1.getTime();
        return time;
    }
    //获取前月的最后一天
    public static Date getLastDay(){
        Calendar cale = Calendar.getInstance();
        cale.set(Calendar.DAY_OF_MONTH,0);//设置为1号,当前日期既为本月第一天
        cale.set(Calendar.HOUR_OF_DAY,23);
        cale.set(Calendar.MINUTE,59);
        cale.set(Calendar.SECOND,59);
        cale.set(Calendar.MILLISECOND,0);
        Date time = cale.getTime();
        return time;
    }
    //获取当前小时-8的hour:00:00
    public static Date getCurrentHourStart(){
        Calendar c = new GregorianCalendar();
        c.set(Calendar.HOUR_OF_DAY,getHour(new Date())-8);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        return c.getTime();
    }
    //获取当前小时-8的hour:59:59
    public static Date getCurrentHourEnd(){
        Calendar c = new GregorianCalendar();
        c.set(Calendar.HOUR_OF_DAY, getHour(new Date())-8);
        c.set(Calendar.MINUTE, 59);
        c.set(Calendar.SECOND, 59);
        return c.getTime();
    }
    public static int getHour(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.HOUR_OF_DAY);
    }

    public static int getYear(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.YEAR);
    }

    public static int getMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.MONTH)+1;
    }


    /**
     * 取得当日的最后一个时间
     * @param date
     * @return
     */
    public static Date getEndOfDay(Date date) {
        Calendar c = new GregorianCalendar();
        c.setTime(date);
        c.set(Calendar.HOUR_OF_DAY, 23);
        c.set(Calendar.MINUTE, 59);
        c.set(Calendar.SECOND, 59);
        return c.getTime();
    }

    /**
     * 取得日期对应格式的字符串
     * @param date
     * @param formatStr
     * @return
     */
    public static String getDateFormat(Date date, String formatStr) {
        Format format = new SimpleDateFormat(formatStr);
        return format.format(date);
    }
    //获取指定月份的第一天
    public static Date getSpeMonthFirstDay(int year,int month){
        Calendar cal = Calendar.getInstance();
        //设置年份
        cal.set(Calendar.YEAR,year);
        //设置月份
        cal.set(Calendar.MONTH, month-1);
        //获取某月最小天数
        int firstDay = cal.getActualMinimum(Calendar.DAY_OF_MONTH);
        //设置日历中月份的最小天数
        cal.set(Calendar.DAY_OF_MONTH, firstDay);
        Date time = cal.getTime();
        return time;

    }

    //获取指定月份的最后一天
    public static Date getSpeMonthLastDay(int year,int month){
        Calendar cal = Calendar.getInstance();
        //设置年份
        cal.set(Calendar.YEAR,year);
        //设置月份
        cal.set(Calendar.MONTH, month-1);
        //获取某月最大天数
        int lastDay = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
        //设置日历中月份的最大天数
        cal.set(Calendar.DAY_OF_MONTH, lastDay);
        Date time = cal.getTime();
        return time;

    }


    /**
     * 获取两个日期相差的月数
     */
    public static int getMonthDiff(Date d1, Date d2) {
        Calendar c1 = Calendar.getInstance();
        Calendar c2 = Calendar.getInstance();
        c1.setTime(d1);
        c2.setTime(d2);
        int year1 = c1.get(Calendar.YEAR);
        int year2 = c2.get(Calendar.YEAR);
        int month1 = c1.get(Calendar.MONTH);
        int month2 = c2.get(Calendar.MONTH);
        int day1 = c1.get(Calendar.DAY_OF_MONTH);
        int day2 = c2.get(Calendar.DAY_OF_MONTH);
        // 获取年的差值?
        int yearInterval = year1 - year2;
        // 如果 d1的 月-日 小于 d2的 月-日 那么 yearInterval-- 这样就得到了相差的年数
        if (month1 < month2 || month1 == month2 && day1 < day2) {
            yearInterval--;
        }
        // 获取月数差值
        int monthInterval = (month1 + 12) - month2;
        if (day1 < day2) {
            monthInterval--;
        }
        monthInterval %= 12;
        int monthsDiff = Math.abs(yearInterval * 12 + monthInterval);
        return monthsDiff;
    }

    /**
     * 毫秒时间戳转换成指定格式日期字符串
     *
     * @param timeStampString 时间戳 如："1473048265000";
     * @return 返回结果 如："2016-09-05 16:06:42";
     */
    public static String timeStampDate(String timeStampString) {
        Long timestamp = Long.parseLong(timeStampString);
        String date = new SimpleDateFormat(COMMON_DATE_PATTERN, Locale.getDefault()).format(new Date(timestamp));
        return date;
    }

}
