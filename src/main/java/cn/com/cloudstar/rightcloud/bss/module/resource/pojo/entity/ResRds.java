/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.resource.pojo.entity;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

@Data
public class ResRds implements Serializable {

    private static final long serialVersionUID = 1L;
    private Long id;

    /**
     * DB实例ID
     */
    private String uuid;

    /**
     * 云环境ID
     */
    private Long cloudEnvId;

    private String cloudEnvType;

    private String cloudEnvName;

    /**
     * 实例类别
     */
    private String category;

    /**
     * 实例类型
     */
    private String instanceType;

    /**
     * 付费类型
     */
    private String payType;

    /**
     * 网络类型
     */
    private String networkType;

    /**
     * 锁定模式
     */
    private String lockMode;

    /**
     * 锁定原因
     */
    private String lockReason;

    /**
     * 可用区
     */
    private String zoneId;
    private String zoneName;

    private String slaveZoneId;
    private String slaveZoneName;

    /**
     * 数据库类型
     */
    private String engine;

    /**
     * 数据库版本
     */
    private String engineVersion;

    /**
     * 实例CPU
     */
    private Integer cpu;

    /**
     * 实例内存
     */
    private Long memory;

    /**
     * 实例存储
     */
    private Integer storage;

    /**
     * 实例创建时间
     */
    private Date creationTime;

    /**
     * 过期时间
     */
    private Date expireTime;

    /**
     * 状态
     */
    private String status;

    /**
     * res_vpc_id
     */
    private Long resVpcId;

    /**
     * res_network_id
     */
    private Long resNetworkId;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedDt;

    /**
     * 版本号
     */
    private Long version;

    /**
     * 连接模式
     */
    private String connectionMode;

    /**
     * 连接模式
     */
    private String description;

    private String region;

    private String dbInstanceClass;

    private String period;

    private String usedTime;

    private String resRdsNetType;

    private String resVpcUuid;

    private String resVpcName;

    private String networkName;

    private String networkUuid;

    private Long orgSid;

    private String errorMsg;

    private Long resSecurityGroupId;

    private String resSecurityGroupName;

    private String periodType;
    private Integer periodNum;

    private String ownerOrgName;
    private String ownerId;
    private String ownerName;

    /*****************AWS RDS******************/
    private String dbInstanceIdentifier;

    private String storageType;

    private String masterUsername;

    private String masterUserPassword;

    private Integer publiclyAccessible;

    private Integer multiAZ;

    private Integer iops;

    private String vpcId;

    private String address;

    private Integer port;

    private String securityGroupId;


    private String tagNames;

    private String rgbCodes;

    private String tagValues;

    private String innerIp;

    private String publicIp;

    private Date startTime;

    private Date endTime;
}
