/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.bag.pojo.request;

import cn.com.cloudstar.rightcloud.bss.common.pojo.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @since 2022/3/7 16:39
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel("管理员查询套餐包列表请求参数")
public class DescribeBizBagUserRequest extends BaseRequest {

    /**
     * 名称
     */
    @ApiModelProperty("名称")
    private String name;

    /**
     * 适用产品
     */
    @ApiModelProperty("适用产品")
    private String productType;

    /**
     * 套餐包计费类型,包年包月：PrePaid，按量付费：PostPaid
     */
    @ApiModelProperty("计费类型")
    private String billingType;

    /**
     * 套餐包类型,折扣包：discount 资源包：resource
     */
    @ApiModelProperty("套餐类型")
    private String type;

    /**
     * 状态 available运行中，expired已过期
     */
    @ApiModelProperty("状态")
    private String status;

    /**
     * 套餐包id
     */
    @ApiModelProperty("套餐包id")
    @NotBlank
    private String bagId;

    /**
     * 客户名称
     */
    @ApiModelProperty("客户名称")
    private String flowNoLike;

    /**
     * 套餐包规格名称
     */
    @ApiModelProperty("套餐包规格名称")
    private String bagSpecName;

    /**
     * 订购时间
     */
    @ApiModelProperty("订购时间")
    private String createdDt;

    /**
     * 规格
     */
    private Double bagValue;

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    /**
     * 组织名字
     */
    private String orgName;

    /**
     * 账户id
     */
    private Long accountId;
}
