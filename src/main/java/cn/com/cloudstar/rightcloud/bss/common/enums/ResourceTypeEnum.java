/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.common.enums;


import cn.hutool.core.util.StrUtil;

/**
 * DESC:资源类型
 *
 * <AUTHOR>
 * @date 2020/3/19 14:47
 */
public enum ResourceTypeEnum {

    /**
     * 计算资源
     */
    COMPUTE("compute", "计算资源"),

    /**
     * 存储资源
     */
    STORAGE("blockStorage", "存储资源"),

    /**
     * 网络资源
     */
    NETWORK("network", "网络资源"),

    /**
     * 服务
     */
    CLOUD_SERVICE("cloudService", "服务");

    private String code;
    private String name;

    ResourceTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String codeFromName(String code) {
        if (code == null) {
            return StrUtil.EMPTY;
        }

        for (ResourceTypeEnum value : ResourceTypeEnum.values()) {
            if (value.code.equals(code)) {
                return value.name;
            }
        }

        return StrUtil.EMPTY;
    }

    public static ResourceTypeEnum getEnum(String code) {
        for (ResourceTypeEnum value : ResourceTypeEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
