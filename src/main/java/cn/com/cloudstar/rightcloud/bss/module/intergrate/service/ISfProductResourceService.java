/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.intergrate.service;

import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity.SfProductResource;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.DescribeProductResourceRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.EditFreezingStrategyRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.QueryResHpcClusterRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.StopJobRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.UpdateResHpcClusterRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.response.DescribeProductResourceResponse;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.response.ResHpcClusterDetailVO;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrderVo;
import cn.com.cloudstar.rightcloud.oss.common.pojo.BaseGridReturn;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 产品对应资源信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-02
 */
public interface ISfProductResourceService extends IService<SfProductResource> {


    /**
     * 查询内置产品资源
     *
     * @param request 参数
     *
     * @return 数据
     */
    IPage<DescribeProductResourceResponse> listResources(DescribeProductResourceRequest request);

    /**
     * 查询内置产品资源
     *
     * @param request 参数
     *
     * @return 数据
     */
    IPage<DescribeProductResourceResponse> listResourcesFeign(DescribeProductResourceRequest request);

    /**
     * 查询内置资源详情
     *
     * @param id 资源id
     * @return 资源详情
     */
    DescribeProductResourceResponse getDetail(Long id);

    /**
     * 查找产品的最新订单信息
     * @param productType
     * @param userId
     * @return
     */
    ServiceOrderVo getProductOrderInfo(String productType, Long userId);

    /**
     * 查找所有产品未关闭资源信息
     * @param productTypes
     * @param parentOrgSid
     * @return
     */
    List<DescribeProductResourceResponse> actionResources(List<String> productTypes, Long parentOrgSid);

    /**
     * HPC集群详细信息
     * @param clusterId
     * @param orgSid
     * @return
     */
    ResHpcClusterDetailVO getHpcClusterDetailById(Long orgSid,Long clusterId);

    /**
     * 查找项目组下使用资源
     * @return
     */
    List<SfProductResource> getUsingResource(Long orgSid);

    /**
     * 查询集群
     * @param request
     * @return
     */
    BaseGridReturn queryHpcCluster(QueryResHpcClusterRequest request);

    /**
     * 删除hpc
     * @param orgSid
     * @param clusterId
     * @return
     */
    void deleteHpcCluster(Long orgSid, Long clusterId);

    /**
     * 更新HPC集群
     * @param updateResHpcClusterRequest
     */
    RestResult updateHpcCluster(UpdateResHpcClusterRequest updateResHpcClusterRequest);

    /**
     * 用户是否购买HPC
     * @param productType
     * @param orgSid
     * @param serviceId
     * @return
     */
    boolean hpcExist(String productType, Long orgSid, Long serviceId);

    /**
     * 查询HPC集群实例
     * @param request
     * @return
     */
    BaseGridReturn queryHpcClusterInstances(DescribeProductResourceRequest request, Integer days ,Boolean flag);

    RestResult listResourcesByEnvId(Long envId);

    /**
     * HPC专属资源池名称check
     * @param clusterName
     * @return
     */
    RestResult checkHpcClusterName(String clusterName);

    /**
     * HPC专属资源池默认名称
     * @param account
     * @return
     */
    RestResult getHpcClusterName(String account);

    /**
     * 检查子用户关联的hpc集群是否存在
     * @param userSid
     * @param clusterId
     * @return
     */
    Boolean getCheckHpcCluster(Long userSid, Long  clusterId);

    /**
     * 查询内置产品资源导出
     *
     * @param request 参数
     * @return 数据
     */
    IPage<DescribeProductResourceResponse> exportListResources(DescribeProductResourceRequest request);

    /**
     * HPC集群实例导出
     * @param request 参数
     * @return  数据
     */
    BaseGridReturn exportHpcClusterInstances(DescribeProductResourceRequest request);


    /**
     * MA重名性校验
     */
    Boolean checkMaName(String clusterName,String productCode);

    /**
     * 根据clusterId查询hpc详细信息
     * @param orgSid
     * @param clusterId
     * @return
     */
    ResHpcClusterDetailVO getHpcClusterDetailByClusterId(Long orgSid, Long clusterId,Long userSid);


    /**
     * 退订失败资源释放
     * @param id
     * @param userSid
     * @param type
     */
    void releasUnsubErrorRes(long id, Long userSid, String type);

    /**
     * 查询产品实例所属用户
     * @param id
     * @param type
     * @return
     */
    Long queryOwnerId(long id, String type);

    RestResult<BaseGridReturn> editFreezingStrategy(EditFreezingStrategyRequest request);

    RestResult stopJob(StopJobRequest request);
}
