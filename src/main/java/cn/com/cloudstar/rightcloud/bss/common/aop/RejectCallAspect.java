package cn.com.cloudstar.rightcloud.bss.common.aop;

import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.SpringContextHolder;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.module.support.access.constants.AuthConstants;
import cn.com.cloudstar.rightcloud.oss.common.constants.RestConst;
import cn.com.cloudstar.rightcloud.oss.common.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2023/1/9 10:59
 * @description  内部接口禁止外部调用
 */
@Aspect
@Component
@Slf4j
public class RejectCallAspect {

    @Pointcut("@annotation(cn.com.cloudstar.rightcloud.bss.common.annotation.RejectCall)")
    public void pointCut() {

    }

    @Before(value = "pointCut()")
    public  void  before(){
        HttpServletRequest request = SpringContextHolder.getHttpServletRequest();
        String requestType = request.getHeader("RequstType");
        if (!AuthConstants.HEADER_TAG.equals(requestType)){
            throw new BizException(RestConst.BizError.INNER_INTERFACE, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_24871105));
        }
    }
}
