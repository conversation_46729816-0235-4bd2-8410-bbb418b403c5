/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.contract.controller;

import cn.com.cloudstar.rightcloud.bss.common.annotation.AuthorizeBss;
import cn.com.cloudstar.rightcloud.bss.common.annotation.DataPermission;
import cn.com.cloudstar.rightcloud.bss.common.annotation.RejectCall;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.BaseGridReturn;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.util.AuthUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.PageUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.WebUserUtil;
import cn.com.cloudstar.rightcloud.bss.module.contract.pojo.entity.BizContractTemplate;
import cn.com.cloudstar.rightcloud.bss.module.contract.pojo.request.CreateBizContractTemplateRequest;
import cn.com.cloudstar.rightcloud.bss.module.contract.pojo.request.DescribeBizContractTemplateRequest;
import cn.com.cloudstar.rightcloud.bss.module.contract.pojo.request.UpdateBizContractTemplateRequest;
import cn.com.cloudstar.rightcloud.bss.module.contract.pojo.response.DescribeContractTemplateResponse;
import cn.com.cloudstar.rightcloud.bss.module.contract.service.BizContractTemplateSrvice;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule.BC.BC05;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.CrytoUtilSimple;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.core.annotation.idempotent.Idempotent;
import cn.com.cloudstar.rightcloud.core.annotation.log.SmsValidation;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.module.support.file.storage.bean.enums.StoragePathEnum;
import cn.com.cloudstar.rightcloud.module.support.file.storage.bean.vo.StorageResult;
import cn.com.cloudstar.rightcloud.module.support.file.storage.service.StorageService;
import cn.com.cloudstar.rightcloud.module.support.file.storage.utils.FileVerifyUtil;
import cn.com.cloudstar.rightcloud.operationlog.annotation.OperationLog;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationTypeEnum;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.UserType;
import cn.com.cloudstar.rightcloud.oss.common.util.FileUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.PropertiesUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.StringUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Objects;

/**
 * 合同模板管理
 *
 * <AUTHOR>
 * @date: 9:19 2019/10/17
 */
@Api(tags = "合同模板管理")
@RestController
@RequestMapping("/contract/template")
@Slf4j
public class BizContractTemplateController {

    @Autowired
    private BizContractTemplateSrvice contractTemplateSrvice;

    @Autowired
    private StorageService storageService;

    @Value("${upload.base.path}")
    private String uploadBasePath;

    /**
     * [INNER API] 查询合同模板列表
     *
     * @param request 查询合同模板列表请求体
     * @return {@code RestResult}
     */
    @RejectCall
    @ApiOperation("查询合同模板列表")
    @GetMapping("")
    public RestResult<List<DescribeContractTemplateResponse>> queryList(DescribeBizContractTemplateRequest request) {
        QueryWrapper<BizContractTemplate> queryWrapperPage = new QueryWrapper<>();
        if (!StringUtils.isEmpty(request.getTemplateNameLike())) {
            queryWrapperPage.like("template_name", request.getTemplateNameLike());
        }
        if (!StringUtils.isEmpty(request.getTemplateId())) {
            queryWrapperPage.like("template_id", request.getTemplateId());
        }

        queryWrapperPage.eq("entity_id", RequestContextUtil.getEntityId());

        PageUtil.setOrderBy(request, queryWrapperPage, "template_id desc");

        AuthUtil.setDistributorWrapper(queryWrapperPage, AuthUtil.getAuthUser());
        // 分页查询
        if (PageUtil.isPageQuery(request)) {
            Page<BizContractTemplate> page = new Page<>(request.getPagenum(), request.getPagesize());
            IPage<BizContractTemplate> pageResult = contractTemplateSrvice.page(page, queryWrapperPage);
            return new RestResult(new BaseGridReturn(BeanConvertUtil.convertPage( pageResult,DescribeContractTemplateResponse.class)));
        }
        //根据运营实体查询合同模板
        queryWrapperPage.eq("entity_id",RequestContextUtil.getEntityId());
        // 查询所有
        List<BizContractTemplate> list = contractTemplateSrvice.list(queryWrapperPage);
        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(contractTemplate -> contractTemplate.setTemplateFile(StrUtil.EMPTY));
        }
        List<DescribeContractTemplateResponse> describeContractTemplateResponses = BeanConvertUtil.convert(list,
                DescribeContractTemplateResponse.class);
        return new RestResult(describeContractTemplateResponses);
    }

    /**
     * 创建合同模板
     *
     * @param request 合同模板请求体
     * @return {@code RestResult}
     */
    @AuthorizeBss(action = BC05.BC0502)
    @ApiOperation("创建合同模板")
    @PostMapping("")
    @OperationLog(type = OperationTypeEnum.CREATE, tagName = "#request.templateName",
            resource = OperationResourceEnum.CONTRACTTEMP,param = "#request")
    @Idempotent
    public RestResult createContractTemplate(@Valid CreateBizContractTemplateRequest request,@RequestParam("file") MultipartFile file) {
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_INSERT_SUCCESS),contractTemplateSrvice.createTemplate(request,file));
    }

    /**
     * 删除合同模板
     *
     * @param templateId 模板id
     * @return {@code RestResult}
     */
    @AuthorizeBss(action = BC05.BC0512)
    @ApiOperation("删除合同模板")
    @DeleteMapping("/{templateId}")
    @OperationLog(type = OperationTypeEnum.DELETE, tagName = "'合同模板'", tagNameUs ="'Contract template'", 
            resource = OperationResourceEnum.DELCONTRACTTEMP,bizId = "#templateId")
    @DataPermission(resource = OperationResourceEnum.DELCONTRACTTEMP,bizId = "#templateId")
    public RestResult deleteResRdsAccount(@PathVariable("templateId") Long templateId) {
        contractTemplateSrvice.deleteResRdsAccount(templateId);
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_DELETE_SUCCESS));
    }

    /**
     * 修改合同模板
     *
     * @param request 合同模板请求体
     * @return {@code RestResult}
     */
    @AuthorizeBss(action = BC05.BC0511)
    @ApiOperation("修改合同模板")
    @PutMapping("")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "#request.templateName",
            resource = OperationResourceEnum.EDIT_CONTRACT_TEMPLATE,bizId = "#request.templateId",param = "#request")
    public RestResult update(@Valid UpdateBizContractTemplateRequest request,MultipartFile file) {
        //根据模板id查询模板
        BizContractTemplate bizContractTemplate = contractTemplateSrvice.getById(request.getTemplateId());
        if(bizContractTemplate == null){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2097652341));
        }else{
            if(bizContractTemplate.getEntityId() != null && !bizContractTemplate.getEntityId().equals(RequestContextUtil.getEntityId())){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2128881086));
            }
        }
        if (UserType.DISTRIBUTOR_USER.equals(AuthUtil.getAuthUser())) {
            if(!AuthUtil.getAuthUser().getOrgSid().equals(bizContractTemplate.getOrgSid())){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_664331278));
            }
        }

        BizContractTemplate contractTemplate = BeanConvertUtil.convert(request, BizContractTemplate.class);
        WebUserUtil.prepareUpdateParams(contractTemplate);
        QueryWrapper<BizContractTemplate> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("template_name", contractTemplate.getTemplateName());
        BizContractTemplate one = contractTemplateSrvice.getOne(queryWrapper);
        if (one != null && !one.getTemplateId().equals(contractTemplate.getTemplateId())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1688366303));
        }
        //上传文件
        if(file != null && !file.isEmpty()){
            // 校验文件魔数值和后缀,大小
            FileVerifyUtil.verifyGeneral(file, StoragePathEnum.CONTRACT, Integer.parseInt(PropertiesUtil.getProperty("contractSizeLimit")));
            String ciphertext = "";
            String fileName = "";
            //合同文件需要压缩成zip文件后上传
            StorageResult storageResult = storageService.saveFile(file, StoragePathEnum.CONTRACT.getPrefix());
            ciphertext = storageResult.getRelativeNginxUrl();
            String suffix = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1);
            boolean b = Objects.equals("JPEG", suffix) || Objects.equals("PNG", suffix) || Objects.equals("GIF", suffix);
            if (b || Objects.equals("TIFF", suffix) || Objects.equals("JPG", suffix)) {
                suffix = suffix.toLowerCase();
                String fileRealName = file.getOriginalFilename().substring(0, file.getOriginalFilename().lastIndexOf("."));
                fileName = fileRealName + "." + suffix;
            } else {
                fileName = file.getOriginalFilename();
            }

            //对文件路径进行加密
            contractTemplate.setTemplateFile(CrytoUtilSimple.encrypt(ciphertext));
            //文件名称
            contractTemplate.setTemplateFileName(fileName);
        }

        boolean flag = contractTemplateSrvice.updateById(contractTemplate);
        if (flag) {
            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_UPDATE_SUCCESS));
        }
        return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERROR_UPDATE_FAILURE));
    }

    /**
     * 查询合同模板详情
     *
     * @param templateId 模板id
     * @return {@code RestResult}
     */
    @AuthorizeBss(action = BC05.BC0503)
    @ApiOperation("查询合同模板详情")
    @GetMapping("/{templateId}")
    public RestResult<DescribeContractTemplateResponse> detail(@PathVariable("templateId") Long templateId) {
        BizContractTemplate contractTemplate = contractTemplateSrvice.getById(templateId);
        if (Objects.isNull(contractTemplate)) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }
        if(contractTemplate.getEntityId() != null  && !contractTemplate.getEntityId().equals(RequestContextUtil.getEntityId()) ){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2128881086));
        }
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (Objects.nonNull(contractTemplate.getOrgSid()) && !Objects.equals(authUserInfo.getOrgSid(), contractTemplate.getOrgSid())) {
            throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
        }

        DescribeContractTemplateResponse detailResponse = BeanConvertUtil.convert(contractTemplate,
                DescribeContractTemplateResponse.class);
        return new RestResult(detailResponse);
    }

    /**
     * 下载合同模版
     *
     * @param templateId 模版id
     * @param response   响应
     */
    @AuthorizeBss(action = BC05.BC0513)
    @GetMapping("/file")
    @ResponseBody
    @ApiOperation("下载文件")
    @SmsValidation
    @OperationLog(type = OperationTypeEnum.EXPORT, tagName = "'合同模板文件'", tagNameUs ="'Contract Template Document'", 
            resource = OperationResourceEnum.DOWNLOAD_CONTRACT_TEMPLATE,bizId = "#templateId")
    public void downloadContractFile(
            @ApiParam("模版ID") @RequestParam("templateId") String templateId,
            HttpServletResponse response) {

        if (StringUtil.isEmpty(templateId) || !StringUtil.isNumeric(templateId)) {
            throw new BizException(WebUtil.getMessage(MsgCd.DECRYPT_PARAMS_FAILURE));
        }
        LambdaQueryWrapper<BizContractTemplate> queryWrapperPage =
                Wrappers.<BizContractTemplate>lambdaQuery().eq(BizContractTemplate::getTemplateId, Long.valueOf(templateId));
        AuthUser user = RequestContextUtil.getAuthUserInfo();
        if (UserType.DISTRIBUTOR_USER.equals(user.getUserType())) {
            queryWrapperPage.eq(BizContractTemplate::getOrgSid, user.getOrgSid());
        }
        BizContractTemplate contractTemplate = contractTemplateSrvice.getOne(queryWrapperPage);
        if(contractTemplate != null && contractTemplate.getEntityId() != null && !contractTemplate.getEntityId().equals(RequestContextUtil.getEntityId())){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2128881086));
        }

        if (contractTemplate != null && contractTemplate.getTemplateFile() != null && response != null) {
            // 33904 上传合同模板加密，下载时也解密

            String path = CrytoUtilSimple.decrypt(contractTemplate.getTemplateFile());
            StorageResult result = storageService.getFile(path);
            FileUtil.fileDownload(response, result.getInputStream(), result.getFileName());
        }
    }

}
