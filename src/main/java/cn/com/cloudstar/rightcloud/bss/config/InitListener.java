/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.config;

import brave.Tracer;
import brave.Tracing;
import brave.messaging.MessagingTracing;
import brave.spring.rabbit.SpringRabbitTracing;
import cn.com.cloudstar.rightcloud.adapter.core.MQHelper;
import cn.com.cloudstar.rightcloud.common.tracelog.TraceUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * \n The type InitListener.\n \n Created on 2016/10/9 \n
 *
 * <AUTHOR>
 */
@Component
public class InitListener {

    private final Logger logger = LoggerFactory.getLogger(InitListener.class);

    @Autowired
    private SpringRabbitTracing springRabbitTracing;

    @Autowired
    private MessagingTracing messagingTracing;

    @Autowired
    private Tracer tracer;

    @Autowired
    private Tracing tracing;

    @PostConstruct
    public void init() {
        MQHelper.injectTemplate(springRabbitTracing,messagingTracing);
        TraceUtil.injectTrace(tracer,tracing);
    }
}
