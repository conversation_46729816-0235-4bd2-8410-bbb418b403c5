/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.common.util;


import cn.com.cloudstar.rightcloud.bss.common.constants.UserTypeConstants;
import cn.com.cloudstar.rightcloud.bss.common.mybatis.enums.DataScopeEnum;
import cn.com.cloudstar.rightcloud.bss.common.pojo.*;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.DynamicMapper;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.UserType;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.bss.common.constants.UserTypeConstants;
import cn.com.cloudstar.rightcloud.bss.common.mybatis.enums.DataScopeEnum;
import cn.com.cloudstar.rightcloud.bss.common.pojo.AuthAsyncVo;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Org;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Role;
import cn.com.cloudstar.rightcloud.bss.common.pojo.User;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.DynamicMapper;
import cn.com.cloudstar.rightcloud.common.util.AssertUtil;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicInfoUtil;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.UserType;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;

import cn.com.cloudstar.rightcloud.oss.common.util.cache.AuthUserHolder;

/**
 * 存取登录用户信息
 *
 * <AUTHOR>
 */
@Slf4j
public class AuthUtil {

    private static DynamicMapper dynamicMapper = SpringContextHolder.getBean(DynamicMapper.class);

    /**
     * 获取当前主公司的ID
     */
    public static Long getCurrentMainOrgSid() {
        Org org = getCurrentOrg();
        try {
            String treePath = org.getTreePath();
            if ("/".equals(treePath)) {
                return org.getOrgSid();
            }
            String mainOrgSid = treePath.split("/")[1];
            return Long.valueOf(mainOrgSid);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    public static Long getCurrentOrgSid() {
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (authUser == null) {
            return null;
        }
        return authUser.getOrgSid();
    }


    /**
     * 获取登录用户实体
     */
    public static User getAuthUser() {
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (Objects.isNull(authUser)) {
            return null;
        }
        Map<String, Object> params = MapUtil.of("type", "user");
        params.put("account", authUser.getAccount());
        List<Map<String, Object>> userParams = dynamicMapper.selectByMap(params);
        if (CollectionUtil.isNotEmpty(userParams)) {
            List<Map<String, Object>> availableUserParams = userParams.stream().filter(u -> !"4".equals(u.get("status"))).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(availableUserParams)){
                availableUserParams.get(0).put("remark", authUser.getRemark());
                userParams = availableUserParams;
            }

        }
        User user = getForClass(userParams, User.class);
        user.setEntityId(authUser.getEntityId());
        user.setParentSid(authUser.getParentSid());
        return user;
    }

    /**
     * 获取当前组织
     */
    public static Org getCurrentOrg() {
        User authUser = getAuthUser();
        if (null == authUser || null == authUser.getOrgSid()) {
            return null;
        }
        return queryOrg(authUser.getOrgSid());
    }

    public static Org queryOrg(Long orgSid) {
        Map<String, Object> params = MapUtil.of("type", "org");
        params.put("orgSid", orgSid);

        return getForClass(dynamicMapper.selectByMap(params), Org.class);
    }

    /**
     * 获取当前用户的角色列表
     */
    private static List<Role> getRoleList() {
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (Objects.isNull(authUser)) {
            return Collections.emptyList();
        }

        Map<String, Object> params = MapUtil.of("type", "role");
        params.put("userSid", authUser.getUserSid());

        return getList(dynamicMapper.selectByMap(params), Role.class);
    }

    /**
     * 获取用户在指定组织的角色列表
     */
    private static List<Role> getRoleList(Long orgSid) {
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        if (Objects.isNull(authUser)) {
            return Collections.emptyList();
        }

        Map<String, Object> params = MapUtil.of("type", "userOrgRole");
        params.put("userSid", authUser.getUserSid());
        params.put("orgSid", orgSid);

        return getList(dynamicMapper.selectByMap(params), Role.class);
    }

    /**
     * 获取数据过滤的SQL（符合业务表字段不同的时候使用，采用exists方法）
     */
    public static String getSQLFilter(String tableAlias, String orgField, String userField) {
        // 解决取不到当前用户报错的情况（1. 回调的时候 2.直接用前台访问小概率会出现取不到当前用户的情况）
        AuthUser user;
        try {
            //如果是系统菜单相关接口
            if (RequestContextUtil.getIsSystemMenu()) {
                return null;
            }
            user = RequestContextUtil.getAuthUserInfo();
        } catch (Exception e) {
            return null;
        }

        if (Objects.isNull(user)) {
            return null;
        }

        Org org = getCurrentOrg();
        if (org == null) {
            //运营、运维角色无组织
            return null;
        }

        if ("console".equals(user.getRemark())) {
            return null;
        }

        //获取表的别名
        if (StringUtils.isNotBlank(tableAlias)) {
            tableAlias += ".";
        }

        StringBuilder sqlFilter = new StringBuilder();
        //分销商 组织及以下数据
        if (UserType.DISTRIBUTOR_USER.equals(user.getUserType())) {
            // 仅查看当前组织及以下的数据
            sqlFilter.append(" EXISTS (SELECT 1 FROM sys_m_org");
            sqlFilter.append(" WHERE (org_sid = '" + org.getOrgSid() + "'");
            sqlFilter.append(" OR tree_path LIKE '%/" + org.getOrgSid() + "/%')");
            // 对业务数据的组织ID不需要比较org_id的，传空
            if (StrUtil.isNotBlank(tableAlias)) {
                sqlFilter.append(" AND org_sid = " + tableAlias + orgField);
            }
            sqlFilter.append(")");
            return " AND (" + sqlFilter.toString() + ")";
        }




        String dataScopeString = getMaxDataScope();
        // 生成组织权限SQL语句
        if (DataScopeEnum.DATA_SCOPE_COMPANY_AND_CHILD.getScope().equals(dataScopeString)) {
            // 仅查看当前组织及以下的数据
            sqlFilter.append(" EXISTS (SELECT 1 FROM sys_m_org");
            sqlFilter.append(" WHERE (org_sid = '" + org.getOrgSid() + "'");
            sqlFilter.append(" OR tree_path LIKE '" + org.getTreePath() + org.getOrgSid() + "/%')");
            // 对业务数据的组织ID不需要比较org_id的，传空
            if (StrUtil.isNotBlank(tableAlias)) {
                sqlFilter.append(" AND org_sid = " + tableAlias + orgField);
            }
            sqlFilter.append(")");

        } else if (DataScopeEnum.DATA_SCOPE_COMPANY.getScope().equals(dataScopeString)) {
            // 仅查看当前组织的数据
            sqlFilter.append(" EXISTS (SELECT 1 FROM sys_m_org");
            sqlFilter.append(" WHERE org_sid = '" + org.getOrgSid() + "'");
            sqlFilter.append(" AND org_sid = " + tableAlias + orgField + ")");
        } else if (DataScopeEnum.DATA_SCOPE_SELF.getScope().equals(dataScopeString)) {
            // 仅查看当前组织的个人数据
            sqlFilter.append(" EXISTS (SELECT 1 FROM sys_m_org");
            sqlFilter.append(" WHERE org_sid = '" + org.getOrgSid() + "'");
            sqlFilter.append(" AND org_sid = " + tableAlias + orgField + ")");
            if (StringUtils.isNotBlank(userField)) {
                sqlFilter.append(" AND");
                sqlFilter.append(" EXISTS (SELECT 1 FROM sys_m_user");
                sqlFilter.append(" WHERE user_sid = '" + user.getUserSid() + "'");
                sqlFilter.append(" AND account = " + tableAlias + userField + ")");
            }
        } else if (DataScopeEnum.DATA_SCOPE_CUSTOM.getScope().equals(dataScopeString)) {


        } else if (DataScopeEnum.DATA_SCOPE_ALL.getScope().equals(dataScopeString) && !Objects
            .isNull(org)) {
            // 查看当前指定组织及以下的数据
            sqlFilter.append(" EXISTS (SELECT 1 FROM sys_m_org");
            sqlFilter.append(" WHERE (org_sid = '" + org.getOrgSid() + "'");
            sqlFilter.append(" OR tree_path LIKE '" + org.getTreePath() + org.getOrgSid() + "/%')");
            // 对业务数据的组织ID不需要比较org_id的，传空
            if (StrUtil.isNotBlank(orgField)) {
                sqlFilter.append(" AND org_sid = " + tableAlias + orgField);
            }
            sqlFilter.append(")");
        }

        if (StringUtils.isNotBlank(sqlFilter.toString())) {
            return " AND (" + sqlFilter.toString() + ")";
        }

        return null;

    }

    static class MapperProvider {



    }

    public static String getSQLFilterWithGivingOrgSid(String tableAlias, String orgField,
        Long orgSid) {

        Org org = queryOrg(orgSid);
        if (Objects.isNull(org)) {
            return null;
        }

        //获取表的别名
        if (StringUtils.isNotBlank(tableAlias)) {
            tableAlias += ".";
        }

        StringBuilder sqlFilter = new StringBuilder();
        // 生成组织权限SQL语句,仅查看当前组织及以下的数据
        sqlFilter.append(" EXISTS (SELECT 1 FROM sys_m_org");
        sqlFilter.append(" WHERE (org_sid = '" + org.getOrgSid() + "'");
        sqlFilter.append(" OR tree_path LIKE '" + org.getTreePath() + org.getOrgSid() + "/%')");
        sqlFilter.append(" AND org_sid = " + tableAlias + orgField + ")");

        if (StringUtils.isNotBlank(sqlFilter.toString())) {
            return " AND (" + sqlFilter.toString() + ")";
        }

        return null;
    }

    /**
     * 根据数据权限获取查询条件 仅根据组织查询，如果数据权限不是组织及以下数据，则只能查询当前组织的数据
     *
     * @param tableAlias 表别名
     * @param orgField org_sid对应的字段
     * @param orgSid 当前org_sid
     * @param existInOrg 是否查询存在于组织中的数据（true： 存在于组织中的数据， false：不存在于组织中的数据）
     * @return 权限过滤条件
     */
    public static String getSQLFilterOnlyByOrgSid(String tableAlias, String orgField, Long orgSid,
        boolean existInOrg) {
        Org org = queryOrg(orgSid);
        String dataScopeString = getMaxDataScope();

        //获取表的别名
        if (StringUtils.isNotBlank(tableAlias) && !tableAlias.contains(".")) {
            tableAlias += ".";
        }

        StringBuilder sqlFilter = new StringBuilder();

        // 查询在当前用户可查询范围内，不在指定项目下的数据
        if (!existInOrg) {
            // 当前组织下可查询的数据
            String existDf = AuthUtil
                .getSQLFilterOnlyByOrgSid(tableAlias, orgField, AuthUtil.getCurrentOrgSid(), true);
            sqlFilter.append(existDf);

            sqlFilter.append("AND NOT");
        }
        sqlFilter.append(" EXISTS (SELECT 1 FROM sys_m_org");

        // 生成组织权限SQL语句
        if (DataScopeEnum.DATA_SCOPE_COMPANY_AND_CHILD.getScope().equals(dataScopeString)) {
            // 仅查看当前组织及以下的数据
            sqlFilter.append(" WHERE (org_sid = '" + org.getOrgSid() + "'");
            sqlFilter.append(" OR tree_path LIKE '" + org.getTreePath() + org.getOrgSid() + "/%')");
            sqlFilter.append(" AND org_sid = " + tableAlias + orgField + ")");
        } else if (DataScopeEnum.DATA_SCOPE_ALL.getScope().equals(dataScopeString) && !Objects
            .isNull(org)) {
            // 超级管理员查看指定组织及以下的数据
            sqlFilter.append(" WHERE (org_sid = '" + org.getOrgSid() + "'");
            sqlFilter.append(" OR tree_path LIKE '" + org.getTreePath() + org.getOrgSid() + "/%')");
            sqlFilter.append(" AND org_sid = " + tableAlias + orgField + ")");
        } else {
            // 仅查看当前组织的数据
            sqlFilter.append(" WHERE org_sid = '" + org.getOrgSid() + "'");
            sqlFilter.append(" AND org_sid = " + tableAlias + orgField + ")");
        }

        if (!existInOrg) {
            return sqlFilter.toString();
        }

        if (StringUtils.isNotBlank(sqlFilter.toString())) {
            return " AND (" + sqlFilter.toString() + ")";
        }

        return null;
    }


    /**
     * 获取数据过滤的SQL
     */
    public static String getSQLJoinFilter(String orgAlias, String userAlias, String userId) {
        AuthUser user;
        try {
            user = RequestContextUtil.getAuthUserInfo();
        } catch (Exception e) {
            return null;
        }

        // 超级管理员，跳过权限过滤
        if (user.getAdminFlag()) {
            return null;
        }

        Org org = getCurrentOrg();
        List<Role> roleList = getRoleList();

        StringBuilder sqlFilter = new StringBuilder();

        boolean isDataScopeAll = false;
        for (Role r : roleList) {
            if (DataScopeEnum.DATA_SCOPE_ALL.getScope().equals(r.getDataScope())) {
                isDataScopeAll = true;
            } else if (DataScopeEnum.DATA_SCOPE_COMPANY_AND_CHILD.getScope()
                .equals(r.getDataScope())) {
                sqlFilter.append(" OR " + orgAlias + ".org_sid = '" + org.getOrgSid() + "'");
                sqlFilter.append(
                    " OR " + orgAlias + ".tree_path LIKE '" + org.getTreePath() + org.getOrgSid()
                        + "/%'");
            } else if (DataScopeEnum.DATA_SCOPE_COMPANY.getScope().equals(r.getDataScope())) {
                sqlFilter.append(" OR " + orgAlias + ".org_sid = '" + org.getOrgSid() + "'");
                sqlFilter.append(" OR (" + orgAlias + ".parent_id = '" + org.getOrgSid());
            } else if (DataScopeEnum.DATA_SCOPE_CUSTOM.getScope().equals(r.getDataScope())) {
                sqlFilter.append(
                    " OR EXISTS (SELECT 1 FROM sys_role_org WHERE role_sid = '" + r.getRoleSid()
                        + "'");
                sqlFilter.append(" AND org_sid = " + orgAlias + ".org_sid)");
            } else if (DataScopeEnum.DATA_SCOPE_ALL.getScope().equals(r.getDataScope()) && !Objects
                .isNull(org)) {
                // 超级管理员查看指定组织及以下的数据
                sqlFilter.append(" OR " + orgAlias + ".org_sid = '" + org.getOrgSid() + "'");
                sqlFilter.append(
                    " OR " + orgAlias + ".tree_path LIKE '" + org.getTreePath() + org.getOrgSid()
                        + "/%'");
            }

        }

        // 如果包含全部权限，则直接返回null
        if (isDataScopeAll) {
            return null;
        }

        // 如果没有全部数据权限，并设置了用户表别名，则当前权限为本人；如果未设置别名，当前无权限为已植入权限
        if (StringUtils.isNotBlank(userAlias)) {
            sqlFilter.append(" OR " + userAlias + ".user_sid = '" + user.getUserSid() + "'");
        } else {
            sqlFilter.append(" OR " + orgAlias + ".org_sid IS NULL");
        }

        if (StringUtils.isNotBlank(sqlFilter.toString())) {
            return " AND (" + sqlFilter.substring(4) + ")";
        }

        return null;
    }

    public static String getMaxDataScope() {
        List<Role> roleList = Lists.newArrayList();
        AuthUser authUser = RequestContextUtil.getAuthUserInfo();
        // 如果是超级管理员，数据权限范围为全部
        if (authUser.getAdminFlag()) {
            return DataScopeEnum.DATA_SCOPE_ALL.getScope();
        }

        if (null == authUser.getOrgSid()) {
            roleList = getRoleList();
        } else {
            roleList = getRoleList(authUser.getOrgSid());
        }

        // 获取到最大的数据权限范围
        int dataScopeInteger = 8;
        for (Role r : roleList) {
            int ds = Integer.valueOf(r.getDataScope());
            if (ds == 9) {
                dataScopeInteger = ds;
                break;
            } else if (ds < dataScopeInteger) {
                dataScopeInteger = ds;
            }
        }

        return String.valueOf(dataScopeInteger);
    }

    /**
     * 该方法为了适应很多地方需要判断角色的情况，按数据权限范围判断角色
     */
    public static String getCurrentUserType() {
        String maxDataScope = getMaxDataScope();
        // 企业管理员
        if (DataScopeEnum.DATA_SCOPE_COMPANY_AND_CHILD.getScope().equals(maxDataScope)) {
            return "02";
            // 项目管理员
        } else if (DataScopeEnum.DATA_SCOPE_COMPANY.getScope().equals(maxDataScope)) {
            return "03";
            // 项目用户
        } else if (DataScopeEnum.DATA_SCOPE_SELF.getScope().equals(maxDataScope)) {
            return "04";
        }

        return "99";
    }

    public static boolean hasPermission(String moduleSid) {
        Long orgSid = AuthUtil.getCurrentOrgSid();
        Long maxRole = AuthUtil.getMaxRole(orgSid);
        Map<String, Object> params = MapUtil.of("type", "rolePermission");
        params.put("roleSid", maxRole);
        params.put("moduleSid", moduleSid);
        Map map = getMap(dynamicMapper.selectByMap(params));

        return !CollectionUtils.isEmpty(map);
    }

    /**
     * 获取用户在当前组织的最大角色
     *
     * @param orgSid 组织ID
     * @return 角色ID
     */
    private static Long getMaxRole(Long orgSid) {
        List<Role> roleList = AuthUtil.getRoleList(orgSid);

        Long maxRoleSid = 0L;
        int dataScopeInteger = 8;
        for (Role r : roleList) {
            int ds = Integer.valueOf(r.getDataScope());
            if (ds == 9) {
                maxRoleSid = r.getRoleSid();
                break;
            } else if (ds < dataScopeInteger) {
                dataScopeInteger = ds;
                maxRoleSid = r.getRoleSid();
            }
        }

        return maxRoleSid;
    }

    public static Long getCurrentOrgSidNoException(AuthAsyncVo authAsyncVo) {
        try {
            return getCurrentOrgSid();
        } catch (Exception e) {
            if (authAsyncVo != null) {
                return authAsyncVo.getOrgSid();
            }
        }
        return null;
    }

    private static Map<String, Object> getMap(List<Map<String, Object>> datas) {
        Map<String, Object> first = CollectionUtil.getFirst(datas);
        return Objects.isNull(first) ? Collections.emptyMap() : first;
    }

    private static <T> T getForClass(List<Map<String, Object>> datas, Class<T> clazz) {
        Map<String, Object> map = getMap(datas);
        return BeanUtil.mapToBean(map, clazz, true);
    }

    private static <T> List<T> getList(List<Map<String, Object>> datas, Class<T> clazz) {
        List<T> list = Lists.newArrayList();
        datas.forEach(map -> list.add(BeanUtil.mapToBean(map, clazz, true)));
        return list;
    }

    /**
     * 查询分销商相关数据
     * @param criteria
     * @param user
     * @param orgSid
     * @return
     */
    public static Long setDistributorCriteria(Criteria criteria, User user, Long orgSid) {
        if (user == null) {
            return 0L;
        }

        if (user.getUserType().equals(UserType.DISTRIBUTOR_USER)
            && !UserTypeConstants.CONSOLE.equals(user.getRemark())) {
            criteria.put("orgSid", user.getOrgSid());
            orgSid = user.getOrgSid();
        }
        return orgSid == null ? 0L : orgSid;
    }

    /**
     * 查询分销商拥有客户相关数据
     * @param criteria
     * @param user
     * @return
     */
    public static void setCustomerDataCriteria(Criteria criteria, User user) {
        if (user == null) {
            return;
        }

        if (user.getUserType().equals(UserType.DISTRIBUTOR_USER)) {
            criteria.put("orgSid", user.getOrgSid());
        }
    }

    public static void setDistributorWrapper(QueryWrapper<?> wrapper, User user) {
        if (user == null) {
            return;
        }
        if (user.getUserType().equals(UserType.DISTRIBUTOR_USER)) {
            wrapper.eq("org_sid", user.getOrgSid());
        }
    }

}
