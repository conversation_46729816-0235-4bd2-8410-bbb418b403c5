package cn.com.cloudstar.rightcloud.bss.common.ccsp.strategy;

import cn.com.cloudstar.rightcloud.bss.module.order.service.IServiceOrderDetailService;
import cn.com.cloudstar.rightcloud.core.pojo.dto.audit.ServiceOrderDetail;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * @description: 订单详情-国密处理
 * @author: ouyonghui
 * @date: 2023/4/3 11:23
 */
@Component
@RequiredArgsConstructor
public class CCSPServiceOrderDetailStrategy extends AbstractCCSPStrategy<ServiceOrderDetail, Long> {
    private final IServiceOrderDetailService serviceOrderDetailService;

    @Override
    public String getStrategy() {
        return ServiceOrderDetail.class.getName();
    }

    @Override
    public Boolean needEncryptDecrypt(ServiceOrderDetail data) {
        return true;
    }

    @Override
    public ServiceOrderDetail handle(ServiceOrderDetail data) {
        if (data.getSkipCCSPHandle() != null && data.getSkipCCSPHandle()) {
            return data;
        }
        return super.handle(data.getId(), data, serviceOrderDetail -> serviceOrderDetailService.getById(data.getId()));
    }
}
