/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.common.util;

import cn.com.cloudstar.rightcloud.bss.common.enums.ExportTypeEnum;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.User;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.*;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.request.DescribeBizBagOrderRequest;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.request.DescribeGaapCostRequest;
import cn.com.cloudstar.rightcloud.bss.module.discount.pojo.request.DescribeDiscountRequest;
import cn.com.cloudstar.rightcloud.bss.module.distributor.pojo.request.GenerateMeteringFileRequest;
import cn.com.cloudstar.rightcloud.bss.module.export.service.ExportService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.DescribeProductResourceRequest;
import cn.com.cloudstar.rightcloud.bss.module.invoice.pojo.request.DescribeInvoicesRequest;
import cn.com.cloudstar.rightcloud.bss.module.sys.pojo.CustomerTemplate;
import cn.com.cloudstar.rightcloud.bss.module.sys.pojo.SubUserTemplate;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
@Data
public class ExportThreadUtil {

    private List<CustomerTemplate> customers = new ArrayList<>();
    private List<SubUserTemplate> subUsers= new ArrayList<>();
    private DescribeGaapCostRequest request;
    private DescribeBillingAccountRequest describeBillingAccountRequest;
    private DescribeProductResourceRequest describeProductResourceRequest;
    private DescribeInvoicesRequest describeInvoicesRequest;
    private DescribeDealsRequest describeDealsRequest;
    private DateSummerRequest dateSummerRequest;
    private DescribeBizBagOrderRequest describeBizBagOrderRequest;
    private GenerateMeteringFileRequest generateMeteringFileRequest;
    private DescribeDiscountRequest describeDiscountRequest;
    private final String moduleType;
    private final String exportType;
    private final ExportService exportService;
    private final Long taskId;
    private User user;
    private AuthUser authUserInfo;
    private CostBillExport costBillExport;

    private final static ExecutorService EXPORT_POOL = Executors.newFixedThreadPool(8);

    /**
     *  创建一个收支明细先进先出队列
     */
    private final static Queue<ExportThreadUtil> EXPORT_QUEUE = new ConcurrentLinkedQueue<>();

    private static final Integer QUEUE_MAX = 200000;

    /**
     * 客户信息下载
     */
    public ExportThreadUtil(ExportService clazz, DescribeBillingAccountRequest describeBillingAccountRequest, String moduleType, String exportType,
                            Long taskId, AuthUser authUserInfo) {
        this.describeBillingAccountRequest = describeBillingAccountRequest;
        this.moduleType = moduleType;
        this.exportType = exportType;
        this.exportService = clazz;
        this.taskId = taskId;
        this.authUserInfo = authUserInfo;
    }

    /**
     * 账单周期下载
     */
    public ExportThreadUtil(ExportService clazz, DescribeGaapCostRequest request, String moduleType, String exportType,
                            Long taskId, AuthUser authUserInfo) {
        this.request = request;
        this.moduleType = moduleType;
        this.exportType = exportType;
        this.exportService = clazz;
        this.taskId = taskId;
        this.authUserInfo = authUserInfo;
    }

    /**
     * 收支明细下载
     */
    public ExportThreadUtil(ExportService clazz, DescribeDealsRequest describeDealsRequest, String moduleType,
                            String exportType, Long taskId, User user, AuthUser authUserInfo) {
        this.describeDealsRequest = describeDealsRequest;
        this.moduleType = moduleType;
        this.exportType = exportType;
        this.exportService = clazz;
        this.taskId = taskId;
        this.user = user;
        this.authUserInfo = authUserInfo;
    }

    /**
     * 资源分析下载
     */
    public ExportThreadUtil(ExportService clazz, DateSummerRequest dateSummerRequest, String moduleType,
                            String exportType, Long taskId, User user, AuthUser authUserInfo) {
        this.dateSummerRequest = dateSummerRequest;
        this.moduleType = moduleType;
        this.exportType = exportType;
        this.exportService = clazz;
        this.taskId = taskId;
        this.user = user;
        this.authUserInfo = authUserInfo;
    }

    public ExportThreadUtil(ExportService clazz, DescribeBizBagOrderRequest request, String moduleType,
                            String exportType,
                            Long taskId, AuthUser authUserInfo) {
        this.describeBizBagOrderRequest = request;
        this.moduleType = moduleType;
        this.exportType = exportType;
        this.exportService = clazz;
        this.taskId = taskId;
        this.authUserInfo = authUserInfo;
    }


    public ExportThreadUtil(ExportService clazz,
                            String exportType,
                            Long taskId, List<CustomerTemplate> customers, List<SubUserTemplate> subUsers, AuthUser authUserInfo) {
        this.exportType = exportType;
        this.exportService = clazz;
        this.taskId = taskId;
        this.customers=customers;
        this.moduleType = "batchImportCustomer";
        this.subUsers=subUsers;
        this.authUserInfo = authUserInfo;
    }


    /**
     * 用户计量数据下载
     */
    public ExportThreadUtil(ExportService clazz, GenerateMeteringFileRequest request, String moduleType, String exportType,
                            Long taskId, AuthUser authUserInfo) {
        this.generateMeteringFileRequest = request;
        this.moduleType = moduleType;
        this.exportType = exportType;
        this.exportService = clazz;
        this.taskId = taskId;
        this.authUserInfo = authUserInfo;
    }

    /**
     * ModelArts服务d产品实例导出
     */
    public ExportThreadUtil(ExportService clazz, DescribeProductResourceRequest describeBillingAccountRequest, String moduleType, String exportType,
                            Long taskId, AuthUser authUserInfo) {
        this.describeProductResourceRequest = describeBillingAccountRequest;
        this.moduleType = moduleType;
        this.exportType = exportType;
        this.exportService = clazz;
        this.taskId = taskId;
        this.authUserInfo = authUserInfo;
    }

    /**
     * 发票导出
     */
    public ExportThreadUtil(ExportService clazz, DescribeInvoicesRequest describeBillingAccountRequest, String moduleType, String exportType,
                            Long taskId, AuthUser authUserInfo) {
        this.describeInvoicesRequest = describeBillingAccountRequest;
        this.moduleType = moduleType;
        this.exportType = exportType;
        this.exportService = clazz;
        this.taskId = taskId;
        this.authUserInfo = authUserInfo;
    }

    /**
     * 平台折扣
     */
    public ExportThreadUtil(ExportService clazz, DescribeDiscountRequest describeBillingAccountRequest, String moduleType, String exportType,
                            Long taskId, AuthUser authUserInfo) {
        this.describeDiscountRequest = describeBillingAccountRequest;
        this.moduleType = moduleType;
        this.exportType = exportType;
        this.exportService = clazz;
        this.taskId = taskId;
        this.authUserInfo = authUserInfo;
    }

    /**
     * 平台折扣
     */
    public ExportThreadUtil(ExportService clazz, CostBillExport costBillExport, String moduleType, String exportType,
                            Long taskId, AuthUser authUserInfo) {
        this.costBillExport = costBillExport;
        this.moduleType = moduleType;
        this.exportType = exportType;
        this.exportService = clazz;
        this.taskId = taskId;
        this.authUserInfo = authUserInfo;
    }


    /**
     * 采用线程池，不再是实现Runnable接口
     * <Br>注：线程池作用到方法上的，注解@Async("exportExecutor")
     * <Br>线程核心数3
     * <AUTHOR>
     */
    public void submit() {
        this.addQueue();
        try {
            while (!EXPORT_QUEUE.isEmpty()) {
                ExportThreadUtil threadUtil = EXPORT_QUEUE.poll();
                EXPORT_POOL.execute(() -> {
                    Thread.currentThread().setName("ExportThreadUtil-" + threadUtil.getExportType());
                    handle(threadUtil);
                });
            }

        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    private synchronized void addQueue() {
        if (EXPORT_QUEUE.size() > QUEUE_MAX) {
            throw new BizException(WebUtil.getMessage(MsgCd.SERVER_BUSY));
        }

        EXPORT_QUEUE.offer(this);
        log.info("ExportThreadUtil.addQueue 待处理导出数据 size: {}", EXPORT_QUEUE.size());
    }

    private void handle(ExportThreadUtil threadUtil) {
        String exportType = threadUtil.getExportType();
        DescribeGaapCostRequest request = threadUtil.getRequest();
        String moduleType = threadUtil.getModuleType();
        Long taskId = threadUtil.getTaskId();
        AuthUser authUserInfo = threadUtil.getAuthUserInfo();
        log.info("ExportThreadUtil.handle 处理数据 exportType: {}， 剩余未处理数据：{}", exportType, EXPORT_QUEUE.size());

        if (ExportTypeEnum.BILLDETAIL.getCode().equals(exportType)) {
            exportService.doAsynExportDetail(request, moduleType, taskId, authUserInfo);
        }
        else if (ExportTypeEnum.BIZBAG_BILLDETAIL.getCode().equals(exportType)) {
            exportService.doAsynExportBizBagBill(request, moduleType, taskId, authUserInfo);
        }
        else if (ExportTypeEnum.BIZBAG_BILL_ORDER.getCode().equals(exportType)) {
            DescribeBizBagOrderRequest describeBizBagOrderRequest = threadUtil.getDescribeBizBagOrderRequest();
            exportService.doAsynExportBizBagOrder(describeBizBagOrderRequest, moduleType, taskId, authUserInfo);
        }
        else if (ExportTypeEnum.BILLCYCLE.getCode().equals(exportType)) {
            exportService.doAsynExportCycle(request, moduleType, taskId, authUserInfo);
        }
        else if (ExportTypeEnum.BIZ_BILLING_ACCOUNT.getCode().equals(exportType)) {
            exportService.doAsynExportBizBillingAccount(describeBillingAccountRequest,moduleType,taskId,authUserInfo);
        }
        else if (ExportTypeEnum.BIZ_ACCOUNT_DEAL.getCode().equals(exportType)) {
            DescribeDealsRequest describeDealsRequest = threadUtil.getDescribeDealsRequest();
            User user = threadUtil.getUser();
            exportService.doAsynExportBizAccountDetail(describeDealsRequest, moduleType, taskId, user, authUserInfo);
        }
        else if (Objects.equals(exportType, "customer")) {
            List<CustomerTemplate> customers = threadUtil.getCustomers();
            List<SubUserTemplate> subUsers = threadUtil.getSubUsers();
            exportService.doAsynExportCustomer(customers, subUsers, "customer", taskId, authUserInfo);
        }
        else if (Objects.equals(exportType, "subUser")) {
            List<CustomerTemplate> customers = threadUtil.getCustomers();
            List<SubUserTemplate> subUsers = threadUtil.getSubUsers();
            exportService.doAsynExportCustomer(customers, subUsers, "subUser", taskId, authUserInfo);

        }
        else if (ExportTypeEnum.USER_STATISTICAL_DATA.getCode().equals(exportType)) {
            User user = threadUtil.getUser();
            GenerateMeteringFileRequest generateMeteringFileRequest = threadUtil.getGenerateMeteringFileRequest();
            exportService.doAsynGenerateMeteringFile(generateMeteringFileRequest, moduleType, taskId, user, authUserInfo);
        }
        else if (ExportTypeEnum.RESOURCE_ANALYSIS.getCode().equals(exportType)) {
            exportService.doResAnalysisExportDetail(dateSummerRequest, moduleType, taskId, authUserInfo);
        }
        else if (ExportTypeEnum.EXPORT_RESOURCES_AI.getCode().equals(exportType)) {
            exportService.doAsynExportMaResources(describeProductResourceRequest, moduleType, taskId, authUserInfo);
        }
        else if (ExportTypeEnum.INVOICE.getCode().equals(exportType)) {
            exportService.doAsynExportInvoice(describeInvoicesRequest, moduleType, taskId, authUserInfo);
        }
        else if (ExportTypeEnum.PLATFORM_DISCOUNT.getCode().equals(exportType)) {
            exportService.doAsynPlatformDiscount(describeDiscountRequest, moduleType, taskId, authUserInfo);
        }
        else if (ExportTypeEnum.CUSTOMER_DISCOUNT.getCode().equals(exportType)) {
            exportService.doAsynCustomerDiscount(describeDiscountRequest, moduleType, taskId, authUserInfo);
        }
        else if (ExportTypeEnum.CONSUME_DETAIL.getCode().equals(exportType)) {
            exportService.doAsyncExportOperationalAnalysisOverview(costBillExport, moduleType, taskId, authUserInfo);
        }
    }
}



