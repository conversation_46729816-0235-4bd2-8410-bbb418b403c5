/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.invoice.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

import cn.com.cloudstar.rightcloud.bss.common.constants.RestConst;
import cn.com.cloudstar.rightcloud.bss.common.enums.InvoiceStatusEnum;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.bss.module.account.mapper.BizBillingAccountMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.invoice.mapper.BizInvoiceMapper;
import cn.com.cloudstar.rightcloud.bss.module.invoice.mapper.BizInvoiceSettingMapper;
import cn.com.cloudstar.rightcloud.bss.module.invoice.pojo.entity.BizInvoiceSetting;
import cn.com.cloudstar.rightcloud.bss.module.invoice.pojo.entity.InvoiceSettingDTO;
import cn.com.cloudstar.rightcloud.bss.module.invoice.service.IBizInvoiceService;
import cn.com.cloudstar.rightcloud.bss.module.invoice.service.IBizInvoiceSettingService;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.BillBillingCycleCost;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;

/**
 * <p>
 * 发票设置信息  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-18
 */
@Service
public class BizInvoiceSettingServiceImpl extends ServiceImpl<BizInvoiceSettingMapper, BizInvoiceSetting> implements IBizInvoiceSettingService {
    private static Log logger = LogFactory.getLog(BizInvoiceSettingServiceImpl.class);

    @Autowired
    private  BizInvoiceSettingMapper bizInvoiceSettingMapper;
    @Autowired
    private BizInvoiceMapper bizInvoiceMapper;
    private static final String ACCOUNT_ID = "ACCOUNT_ID";

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    IBizInvoiceService iBizInvoiceService;

    @Autowired
    BizBillingAccountMapper bizBillingAccountMapper;

    /**
     * 根据用户ID查询用户发票设置信息
     * @param accountID
     * @return
     */
    @Override
    public InvoiceSettingDTO selectByAccountID(String accountID) {
        BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectById(accountID);
        if (ObjectUtils.isEmpty(bizBillingAccount)){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1574153436));
        }
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        if (ObjectUtils.isEmpty(authUserInfo)){
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_531007114));
        }
        if (!authUserInfo.getOrgSid().equals(bizBillingAccount.getOrgSid())) {
            throw new BizException(RestConst.BizError.PERMISSION_VERIFICATION_FAILED, WebUtil.getMessage(MsgCd.AUTHORIZE_FAILURE));
        }
        QueryWrapper<BizInvoiceSetting> bizInvoiceSettingQueryWrapper = new QueryWrapper<>();
        bizInvoiceSettingQueryWrapper.eq(ACCOUNT_ID, accountID);
        InvoiceSettingDTO convert =
                BeanConvertUtil.convert(bizInvoiceSettingMapper.selectOne(bizInvoiceSettingQueryWrapper),
                InvoiceSettingDTO.class);
        if (Objects.isNull(convert)) {
            convert = new InvoiceSettingDTO();
        }

        Query query = new Query();
        Criteria c1 = Criteria.where("ownerId").is(Long.parseLong(accountID)).and("invoiceAmount").gt(new BigDecimal(0));
        Criteria c2 = Criteria.where("invoiceStatus").ne(InvoiceStatusEnum.DONE.getCode());
        Criteria c3 = Criteria.where("invoiceStatus").ne(InvoiceStatusEnum.PENDING.getCode());
        query.addCriteria(new Criteria().andOperator(c1));
        List<BillBillingCycleCost> billBillingCycleCosts = mongoTemplate.find(query, BillBillingCycleCost.class);
        if(CollectionUtils.isEmpty(billBillingCycleCosts)){
            logger.info("BizInvoiceSettingServiceImpl billBillingCycleCosts is empty");
            return convert;
        }
        List<BillBillingCycleCost> billingCycleCosts = iBizInvoiceService.checkServiceRelease(billBillingCycleCosts);
        BigDecimal totalMoney = new BigDecimal(0);
        BigDecimal consumptionAmount = new BigDecimal(0);
        BigDecimal invoicedAmount = new BigDecimal(0);
        if(!CollectionUtils.isEmpty(billingCycleCosts) && billingCycleCosts.size()>0){
            for (BillBillingCycleCost cost : billingCycleCosts) {
                BigDecimal rechargeCreditAmount = cost.getRechargeCreditAmount();
                if (rechargeCreditAmount == null) {
                    rechargeCreditAmount=BigDecimal.ZERO;
                }
                consumptionAmount = consumptionAmount.add(cost.getCashAmount()).subtract(rechargeCreditAmount).setScale(2,BigDecimal.ROUND_HALF_UP);
                if(InvoiceStatusEnum.DONE.getCode().equals(cost.getInvoiceStatus()) || InvoiceStatusEnum.PENDING.getCode().equals(cost.getInvoiceStatus())){
                    invoicedAmount = invoicedAmount.add(cost.getInvoiceAmount()).subtract(rechargeCreditAmount).setScale(2,BigDecimal.ROUND_HALF_UP);
                }

            }
        }
        totalMoney = consumptionAmount.subtract(invoicedAmount).setScale(2,BigDecimal.ROUND_HALF_UP);
        convert.setTotalMoney(totalMoney.setScale(2, BigDecimal.ROUND_HALF_UP));
        convert.setConsumptionAmount(consumptionAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
        convert.setInvoicedAmount(invoicedAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
        return convert;
    }


    @Override
    public boolean updateInvoiceSetting(BizInvoiceSetting bizInvoiceSetting) {
        QueryWrapper<BizInvoiceSetting> wrapper = new QueryWrapper<>();
        wrapper.eq(ACCOUNT_ID, bizInvoiceSetting.getAccountId());
        BizInvoiceSetting bs = bizInvoiceSettingMapper.selectOne(wrapper);
        if (Objects.isNull(bs)) {
            if (bizInvoiceSettingMapper.insert(bizInvoiceSetting) < 1){
                return false;
            }
        }
        if (bizInvoiceSettingMapper.update(bizInvoiceSetting, wrapper) < 1) {
            return false;
        }
        return true;
    }


}
