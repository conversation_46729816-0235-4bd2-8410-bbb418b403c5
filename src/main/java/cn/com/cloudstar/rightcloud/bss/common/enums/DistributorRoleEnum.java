/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.common.enums;


import cn.hutool.core.util.StrUtil;

/**
 * DESC:分销商角色
 *
 * <AUTHOR>
 * @date 2020/8/28 14:47
 */
public enum DistributorRoleEnum {

    /**
     * 管理员
     */
    DISTRIBUTOR_ADMIN(401L, "管理员"),
    /**
     * 财务
     */
    DISTRIBUTOR_FINANCE(402L, "财务"),

    /**
     * 销售
     */
    DISTRIBUTOR_SALE(403L, "销售");

    private Long sid;
    private String name;

    DistributorRoleEnum(Long sid, String name) {
        this.sid = sid;
        this.name = name;
    }

    public Long getSid() {
        return sid;
    }

    public String getName() {
        return name;
    }

    public static String codeFromName(Long sid) {
        if (sid == null) {
            return StrUtil.EMPTY;
        }
        for (DistributorRoleEnum value : DistributorRoleEnum.values()) {
            if (value.sid.equals(sid)) {
                return value.name;
            }
        }

        return StrUtil.EMPTY;
    }
}
