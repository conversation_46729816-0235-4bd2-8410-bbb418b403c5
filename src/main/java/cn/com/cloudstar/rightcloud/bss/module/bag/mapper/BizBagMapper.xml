<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.cloudstar.rightcloud.bss.module.bag.mapper.BizBagMapper">
    <resultMap id="BaseResultMap" type="cn.com.cloudstar.rightcloud.bss.module.bag.pojo.response.DescribeBizBagResponse">
       <id  column="id" property="id" jdbcType="VARCHAR" />
        <result column="bag_id" property="bagId" jdbcType="VARCHAR" />
        <result column="name" property="name" jdbcType="VARCHAR" />
        <result column="product_type" property="productType" jdbcType="VARCHAR" />
        <result column="billing_type" property="billingType" jdbcType="VARCHAR" />
        <result column="type" property="type" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="VARCHAR" />
        <result column="clear_policy" property="clearPolicy" jdbcType="TINYINT" />
        <result column="description" property="description" jdbcType="VARCHAR" />
        <result column="org_sid" property="orgSid" jdbcType="VARCHAR" />
        <result column="owner_id" property="ownerId" jdbcType="BIGINT" />
        <result column="version" property="version" jdbcType="BIGINT" />
        <result column="del_flag" property="delFlag" jdbcType="TINYINT" />
        <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
        <result column="created_dt" property="createdDt" jdbcType="TIMESTAMP" />
        <result column="updated_by" property="updatedBy" jdbcType="VARCHAR" />
        <result column="updated_dt" property="updatedDt" jdbcType="TIMESTAMP" />
        <result column="spec_name" property="specName" jdbcType="VARCHAR" />
        <result column="spec_value" property="specValue" jdbcType="DECIMAL" />
        <result column="period" property="period" jdbcType="INTEGER" />
        <result column="price" property="price" jdbcType="DECIMAL" />
    </resultMap>

    <resultMap id="biz_Bag_Spec"  type="cn.com.cloudstar.rightcloud.bss.module.bag.pojo.entity.BizBagSpec">
        <id  column="id" property="id" jdbcType="BIGINT" />
        <result column="bag_id" property="bagId" jdbcType="VARCHAR" />
        <result column="spec_name" property="specName" jdbcType="VARCHAR" />
        <result column="spec_value" property="specValue" jdbcType="DECIMAL" />
        <result column="period" property="period" jdbcType="INTEGER" />
        <result column="price" property="price" jdbcType="DECIMAL" />
        <result column="org_sid" property="orgSid" jdbcType="VARCHAR" />
        <result column="owner_id" property="ownerId" jdbcType="BIGINT" />
        <result column="version" property="version" jdbcType="BIGINT" />
        <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
        <result column="created_dt" property="createdDt" jdbcType="TIMESTAMP" />
        <result column="updated_by" property="updatedBy" jdbcType="VARCHAR" />
        <result column="updated_dt" property="updatedDt" jdbcType="TIMESTAMP" />
    </resultMap>

    <resultMap id="myMap" type="cn.com.cloudstar.rightcloud.bss.module.bag.pojo.response.DescribeBizBagResponseVO">
        <id  column="id" property="id" jdbcType="VARCHAR" />
        <result column="bag_id" property="bagId" jdbcType="VARCHAR" />
        <result column="name" property="name" jdbcType="VARCHAR" />
        <result column="product_type" property="productType" jdbcType="VARCHAR" />
        <result column="billing_type" property="billingType" jdbcType="VARCHAR" />
        <result column="type" property="type" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="VARCHAR" />
        <result column="clear_policy" property="clearPolicy" jdbcType="TINYINT" />
        <result column="description" property="description" jdbcType="VARCHAR" />
        <result column="org_sid" property="orgSid" jdbcType="VARCHAR" />
        <result column="owner_id" property="ownerId" jdbcType="BIGINT" />
        <result column="version" property="version" jdbcType="BIGINT" />
        <result column="del_flag" property="delFlag" jdbcType="TINYINT" />
        <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
        <result column="created_dt" property="createdDt" jdbcType="TIMESTAMP" />
        <result column="updated_by" property="updatedBy" jdbcType="VARCHAR" />
        <result column="updated_dt" property="updatedDt" jdbcType="TIMESTAMP" />
        <collection property="bizBagSpecs" ofType="cn.com.cloudstar.rightcloud.bss.module.bag.pojo.entity.BizBagSpec" select="listBizSpecBag"
        javaType="ArrayList" column="bag_id">
        </collection>

    </resultMap>

    <resultMap id="bizBagResultMap"  type="cn.com.cloudstar.rightcloud.bss.module.bag.pojo.entity.BizBag">
        <id  column="id" property="id" jdbcType="VARCHAR" />
        <result column="name" property="name" jdbcType="VARCHAR" />
        <result column="bag_id" property="bagId" jdbcType="VARCHAR" />
        <result column="billing_type" property="billingType" jdbcType="VARCHAR" />
        <result column="type" property="type" jdbcType="VARCHAR" />
        <result column="status" property="status" jdbcType="VARCHAR" />
        <result column="product_type" property="productType" jdbcType="VARCHAR" />
        <result column="clear_policy" property="clearPolicy" jdbcType="TINYINT" />
        <result column="description" property="description" jdbcType="VARCHAR" />
        <result column="org_sid" property="orgSid" jdbcType="VARCHAR" />
        <result column="owner_id" property="ownerId" jdbcType="BIGINT" />
        <result column="version" property="version" jdbcType="BIGINT" />
        <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
        <result column="created_dt" property="createdDt" jdbcType="TIMESTAMP" />
        <result column="updated_by" property="updatedBy" jdbcType="VARCHAR" />
        <result column="updated_dt" property="updatedDt" jdbcType="TIMESTAMP" />
        <result column="del_flag" property="delFlag" jdbcType="TINYINT" />
    </resultMap>

    <sql id="Base_Column_List">
       id, bag_id,name,billing_type,product_type,type,status,clear_policy,description,org_sid,owner_id,version,del_flag,
       created_by,created_dt,updated_by,updated_dt
    </sql>

    <sql id="Base_Column_Lists">
       id, bag_id,spec_name,spec_value,period,price,org_sid,owner_id,version,
       created_by,created_dt,updated_by,updated_dt
    </sql>


    <select id="listBizBag"  resultMap="myMap">
        SELECT
        *
        FROM biz_bag
        <where>
            <if test="request.id != null and request.id != ''" >
                and id = #{request.id}
            </if>
            <if test="request.name != null and request.name != ''" >
                and name like concat('%',#{request.name},'%')
            </if>
            <if test="request.description != null and request.description != ''" >
                and description like concat('%',#{request.description},'%')
            </if>
            <if test="request.productType != null and request.productType != ''" >
                and product_type like concat('%',#{request.productType},'%')
            </if>
            <if test="request.billingType != null and request.billingType != ''" >
                and billing_type = #{request.billingType}
            </if>
            <if test="request.type != null and request.type != ''" >
                and type = #{request.type}
            </if>
            <if test="request.status != null and request.status != ''" >
                and status = #{request.status}
            </if>
            <if test="request.entityId != null and request.entityId != ''" >
                and entity_id = #{request.entityId}
            </if>
        </where>
    </select>

    <select id="listBizSpecBag" resultMap="biz_Bag_Spec">
        select
        <include refid="Base_Column_Lists"></include>
         from
        biz_bag_spec where bag_id = #{bag_id} ;
    </select>
    <sql id="Base_Column_MyList">
       B.id, A.bag_id,A.name,A.billing_type,A.product_type,A.type,A.status,A.clear_policy,A.description,A.org_sid,A.owner_id,A.version,A.del_flag,
       A.created_by,A.created_dt,A.updated_by,A.updated_dt,B.spec_name,B.spec_value,B.period,B.price
    </sql>
   <select id="listBizBagAndSpec" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_MyList"></include>
      from biz_bag A left join  biz_bag_spec B
      on A.bag_id=B.bag_id
       <where>
           <if test="request.bagId != null and request.bagId != ''" >
               and A.bag_id = #{request.bagId}
           </if>
           <if test="request.specValue != null and request.specValue != ''" >
               and B.spec_value = #{request.specValue}
           </if>
           <if test="request.period != null and request.period != ''" >
               and B.period = #{request.period}
           </if>
       </where>
      order by B.period  asc
   </select>

    <select id="selectByPrimaryKey" resultMap="bizBagResultMap">
        select A.id, A.`name`, A.bag_id, A.billing_type, A.type, A.`status`, A.product_type, A.clear_policy,
               A.description, A.org_sid, A.owner_id, A.version, A.created_by, A.created_dt, A.updated_by,
               A.updated_dt, A.del_flag
        from biz_bag A where id = #{id}
    </select>

    <select id="selectBizBag" parameterType="java.util.List" resultType="java.lang.String">
        select distinct t1.bag_id from biz_bag t1 left join biz_bag_user t2 on t1.bag_id = t2.bag_id
        where t1.status = 'online'
        and t2.bag_id in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="selectBizBagStatus" resultType="java.lang.String">
        select DISTINCT `status` from biz_bag b
        left join biz_bag_spec bs on b.bag_id = bs.bag_id
        where bs.id in
        <foreach collection="specIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>
