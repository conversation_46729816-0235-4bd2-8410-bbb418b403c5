/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.common.constants;

/**
 * Created by swq on 4/7/2016.
 *
 * <AUTHOR>
 */
public interface SysConfigConstants {

    /**
     * ansible server url
     */
    String ANSIBLE_SERVER_URL = "ansible.server.url";

    String OPEN_FALCON_ALARM_CALLBACK_URL = "openfalcon.alarm.callback.url";

    /**
     * kubernetes集群默认dns ip配置
     */
    String KUBERNETES_DEFAULT_CLUSTER_DNS = "kubernetes.default.cluster.dns";

    //通知提醒相关配置参数 add by luxinglin 2017-11-10

    /**
     * 预过期时间配置参数
     */
    String SERVICE_EXPIRE_DURATION = "service.expire.notice.duration";
    /**
     * 服务到期持续发送提醒频率配置参数
     */
    String SERVICE_EXPIRE_NOTICE_FREQUENCY = "service.expire.notice.frequency";
    /**
     * 服务到期提醒开关配置参数
     */
    String SERVICE_EXPIRE_NOTICE_ON = "service.expire.notice.on";
    /**
     * 服务到期提醒方式配置参数
     */
    String SERVICE_EXPIRE_NOTICE_METHOD = "service.expire.notice.method";

    //end 通知提醒相关配置参数 add by luxinglin 2017-11-10

    /**
     * 平台类型
     */
    String PLATFORM_TYPE = "platform.type";

    /**
     * 短信相关配置
     */
    String SMS_CDKEY = "sms.cdkey";

    String SMS_KEY = "sms.key";

    String SMS_PASS_KEY = "sms.password";

    String SMS_URL = "sms.url";

    String SMS_SEQID = "sms.seqid";

    String SMS_ENABLE = "sms.enable";

    /**
     * 平台logo存储根路径
     */
    String PLATFORM_LOGO_PATH = "platform.logo.root.path";

    /**
     * 平台许可密钥
     */
    String PLATFORM_LICENCE_KEY = "platform.licence.key";

    /**
     * 平台密钥key
     */
    String SECURITY_KEY = "security.key";

    /**
     * ldap证书路径
     */
    String LDAP_CREDENTIALS_PATH = "ldap.credentials.path";

    /**
     * mongo 用户执行记录日志集合名
     */
    String MONGO_ACTION_LOG_COLLECTION_NAME = "action_log";


    /***
     * swagger 在线文档开关
     * */
    String SWAGGER_ENABLE = "swagger.enable";

    /***
     * 云监控组件开关
     * */
    String MONITOR_CONFIG = "monitor.acquisition.mode";
    /**
     * 系统名称
     */
    String SYSTEM_NAME = "system.name";
    /**
     * 小图标
     */
    String PLATFORM_SMALL_LOGO = "platform.small.logo";
    /**
     * 大图标
     */
    String PLATFORM_LARGE_LOGO = "platform.large.logo";
    /**
     * 高级监控任务分组
     **/
    String DCIM_INVENTORY_ID = "dcim.inventory.id";
    /**
     * 高级监控 安装pgagent 任务模版ID
     **/
    String DCIM_PGAGENT_INSTALL_TEMPLATE_ID = "dcim.pgangent.install.templateId";
    /**
     * 高级监控 卸载pgagent 任务模版ID
     **/
    String DCIM_PGAGENT_UNINSTALL_TEMPLATE_ID = "dcim.pgangent.uninstall.templateId";
    /**
     * 高级监控配置发现频率默认 20m
     **/
    String DCIM_TASK_CRON = "dcim.task.cron";
    /**
     * 高级监控指标采集频率默认1m
     **/
    String DCIM_TASK_CRON_DEFAULT = "dcim.task.cron.default";
    /**
     * 监控系统手动纳管服务器 所属category
     **/
    String DCIM_MANUAL_SERVER_CATEGORY = "dcim.manual.server.category";
    /**
     * 监控系统 接口前缀
     **/
    String DCIM_API_PREFIX = "monitor.integration.api.prefix";
    /**
     * node_exporter默认端口号
     */
    String DCIM_NODE_EXPORTER_PORT = "dcim.node_exporter.port";

    /**
     * 监控系统 用户名
     **/
    String DCIM_MONITOR_INTEGRATION_SERVER_USERNAME = "monitor.integration.server.username";

    /**
     * 监控系统密码
     **/
    String DCIM_MONITOR_INTEGRATION_SERVER_CIPHER = "monitor.integration.server.password";
    /**
     * 监控系统 sshport
     **/
    String DCIM_MONITOR_INTEGRATION_SERVER_SSHPORT = "monitor.integration.server.sshport";
    /**
     * INVENTORY 内容格式
     **/
    String INVENTORY_FORMAT = "inventory.format";

    /**
     * 基础监控 接口前缀
     **/
    String BASIC_MONITOR_API_PREFIX = "base.monitor.api.prefix";
    /**
     * 基础监控 数据采集频率
     **/
    String BASIC_MONITOR_DATA_COLLECT_FREQUENCY = "base.monitor.data.collect.frequency";
    /**
     * 基础监控 告警数据采集频率
     **/
    String BASIC_MONITOR_ALARM_COLLECT_FREQUENCY = "base.monitor.alarm.collect.frequency";
    /**
     * 基础监控 API版本
     **/
    String BASIC_MONITOR_API_VERSION = "/api/v1";
    /**
     * 基础监控TopN指标项名称
     */
    String BASIC_MONITOR_TOPN_METRIC_NAMES = "basic.monitor.topN.metric.names";

    String PROJECTADMIN_AUDITSTATUS = "approve.project.mgt";

    String COMPANYADMIN_AUDITSTATUS = "approve.company.mgt";

    /**
     * 自动接管等待时间
     */
    String IMPORT_DELAY_TIME = "import.delay.time";

    /**
     * 用户token过期时间
     */
    String SESSION_EXPIRE_TIME = "session.expire.time";

    /**
     * 接管重试次数
     */
    String INSTANCE_ACCESS_COUNT = "instance.access.count";
    /**
     * 接管超时时间
     */
    String INSTANCE_ACCESS_TIME = "instance.access.time";

    /**
     * OpenStack系列云环境是否支持普通租户接入
     */
    String CLOUD_ENV_TENANT_IMPORT = "cloud.env.tenant.import";

    /**
     * CCPortal地址
     */
    String CCPORTAL_URL = "ccportal.url";

    /**
     * TOP信息过滤开关
     */
    String TOP_INFO_FILTER_SWITCH = "top.info.filter.switch";

    /**
     * TOP信息过滤标识
     */
    String TOP_INFO_FILTER_TAG = "top.info.filter.tag";

    interface ImageHarbor {

        String URL = "image.harbor.url";
        String ADMIN = "image.harbor.admin";
        String CIPHER = "image.harbor.admin.passwd";
    }

    interface AlipayConfig{

        /**支付宝支付应用ID*/
        String APPID = "alipay.appid";

        /**支付宝支付应用私钥*/
        String PRIVATE_KEY = "alipay.privateKey";

        /**支付宝支付公钥*/
        String PUBLIC_KEY = "alipay.publicKey";

        /**支付宝支付回调地址*/
        String NOTIFY_URL = "alipay.notifyUrl";

        /**支付宝支付成功返回地址*/
        String RETURN_URL = "alipay.returnUrl";

        /**支付宝支付开关*/
        String SWITCH = "alipay.switch";
    }

    interface WechatpayConfig{

        /**微信支付应用ID*/
        String APPID = "wechatpay.appid";

        /**微信支付直连商户号*/
        String MCHID = "wechatpay.mchid";

        /**微信支付直连商户号*/
        String MCH_KEY = "wechatpay.mchKey";

        /**微信支付回调地址*/
        String NOTIFY_URL = "wechatpay.notifyUrl";

        /**微信支付成功返回地址*/
        String RETURN_URL = "wechatpay.returnUrl";

        /**微信支付开关*/
        String SWITCH = "wechatpay.swithch";
    }

    interface UnionpayConfig{


        /**银联支付直连商户号*/
        String MCHID = "unionpay.mchid";


        /**银联支付回调地址*/
        String NOTIFY_URL = "unionpay.notifyUrl";

        /**银联支付成功返回地址*/
        String RETURN_URL = "unionpay.returnUrl";

        /**银联支付开关*/
        String SWITCH = "unionpay.switch";
    }
}
