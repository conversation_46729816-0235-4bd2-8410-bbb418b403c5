/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.common.util;

import org.springframework.context.MessageSource;

import java.util.Locale;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2019/10/17.
 * 国际化工具替换为WebUtil
 */
@Slf4j
@Deprecated
public class MessageUtil {

    private static MessageSource messageSource;

    static {
        try {
            // 获取消息处理类
            messageSource = SpringContextHolder.getBean(MessageSource.class);
        } catch (Exception e) {
            log.error("加载 spring 国际化 bean 失败", e);
        }
    }

    /**
     * 取得系统消息
     *
     * @param msgId 消息ID
     * @return 消息内容
     */
    public static String getMessage(String msgId) {
        return getMessage(msgId, null);
    }

    /**
     * 取得系统消息
     *
     * @param msgId 消息ID
     * @param arg 消息设置参数
     * @return 消息内容
     */
    public static String getMessage(String msgId, Object[] arg) {
        String message = StrUtil.EMPTY;
        try {
            message = messageSource.getMessage(msgId, arg, Locale.CHINA);
        } catch (Exception e) {
        }

        return message;
    }
}
