/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.billing.pojo.response;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel(description = "询价结果")
public class InquiryPriceResponse {

    /**
     * 货币单位
     */
    @ApiModelProperty(notes = "货币单位")
    private String currency;

    /**
     * 原始价格
     */
    @ApiModelProperty(notes = "原价")
    private BigDecimal originalPrice;

    /**
     * 折扣价格
     */
    @ApiModelProperty(notes = "折扣价")
    private BigDecimal discountPrice;

    /**
     * 最终价
     */
    @ApiModelProperty(notes = " 最终价，为原价减去折扣")
    private BigDecimal tradePrice;

    /**
     * 单次收费原价
     */
    @ApiModelProperty(notes = " 单次收费原价")
    private BigDecimal originalOncePrice = BigDecimal.ZERO;

    /**
     * 单次收费折扣价
     */
    @ApiModelProperty(notes = " 单次收费折扣价")
    private BigDecimal discountOncePrice = BigDecimal.ZERO;

    /**
     * 单次收费交易价
     */
    @ApiModelProperty(notes = " 单次收费交易价")
    private BigDecimal tradeOncePrice = BigDecimal.ZERO;

    /**
     * 配置费用单位
     */
    @ApiModelProperty(notes = "配置费用单位")
    private String chargeUnit;

    /**
     * 带宽价格单位
     */
    @ApiModelProperty(notes = "带宽价格单位")
    private String bandwidthPriceChargeUnit;

    /**
     * 带宽价格原价
     */
    @ApiModelProperty(notes = "带宽价格原价")
    private BigDecimal bandwidthOriginalPrice;

    /**
     * 带宽价格折扣价
     */
    @ApiModelProperty(notes = "带宽价格折扣价")
    private BigDecimal bandwidthDiscountPrice;

    /**
     * 几折
     */
    @ApiModelProperty(notes = "几折")
    private BigDecimal platformDiscount;

    /**
     * 平台折扣价
     */
    @ApiModelProperty(notes = "平台折扣价")
    private BigDecimal platformDiscountPrice;

    /**
     * 结算价格
     */
    private List<BizBillingPriceVO> billingPrices = Lists.newArrayList();

    /**
     * 折扣详情
     */
    @ApiModelProperty(notes = "折扣详情")
    private List<DiscountDetailVO> discountDetails;

    /**
     * 服务价
     */
    @ApiModelProperty(notes = "服务价 ")
    private BigDecimal serviceAmount;


    /**
     * 资源价
     */
    @ApiModelProperty(notes = "资源价 ")
    private BigDecimal resourceAmount;

    /**
     * 产品代码
     */
    @ApiModelProperty(notes = "产品code ")
    private String productCode;

    /**
     * 上浮点数
     */
    @ApiModelProperty(notes = "上浮点数 ")
    private BigDecimal floatingRatio;

    /**
     * 优惠券金额
     */
    @ApiModelProperty(notes = "优惠券金额")
    private BigDecimal couponAmount = BigDecimal.ZERO;

    /**
     * 过期未冻结使用金额
     */
    @ApiModelProperty(notes = "过期未冻结使用金额")
    private BigDecimal expiredUsedAmount = BigDecimal.ZERO;

    /**
     * 过期截止时间
     */
    @ApiModelProperty(notes = "过期截止时间")
    private Date now;

    /**
     * 产品类别
     */
    @ApiModelProperty(notes = "产品类型（用于产品相同时，区分）")
    private String productCategory;

    @ApiModelProperty(notes = "收费规则：02:销售计费、01:正常计费")
    private String chargingType;
    @ApiModelProperty(notes = "抹零金额")
    private BigDecimal eraseZeroAmount = BigDecimal.ZERO;

    @ApiModelProperty(notes = "规格")
    private String skuName;
}
