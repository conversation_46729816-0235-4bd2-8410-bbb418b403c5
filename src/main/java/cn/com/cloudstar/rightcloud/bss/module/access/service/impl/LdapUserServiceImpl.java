package cn.com.cloudstar.rightcloud.bss.module.access.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;

import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.FutureTask;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.Resource;
import javax.naming.Context;
import javax.naming.NamingEnumeration;
import javax.naming.NamingException;
import javax.naming.directory.SearchResult;
import javax.naming.ldap.InitialLdapContext;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.adapter.core.MQHelper;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.GrantJobTemplate;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.GrantJobTemplateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.GrantJobUser;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.Daccount;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.Duser;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.result.DaccountResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.result.DuserResult;
import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnv;
import cn.com.cloudstar.rightcloud.bss.common.constants.OrgType;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Org;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.SHA512Util;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.User;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.request.OperationLdapRequest;
import cn.com.cloudstar.rightcloud.bss.module.access.constance.HpcConstance;
import cn.com.cloudstar.rightcloud.bss.module.access.service.IPolicyAssertionService;
import cn.com.cloudstar.rightcloud.bss.module.access.service.IPolicyService;
import cn.com.cloudstar.rightcloud.bss.module.access.service.LdapUserService;
import cn.com.cloudstar.rightcloud.bss.module.access.service.SysUserService;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.mapper.SfProductResourceMapper;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity.SfProductResource;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.QueryResHpcClusterRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.HpcClusterService;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.ConfigMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.pojo.SysConfig;
import cn.com.cloudstar.rightcloud.bss.module.sys.pojo.SysHpcPass;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.OrgService;
import cn.com.cloudstar.rightcloud.bss.module.sys.service.SysHpcPassService;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.constants.res.status.ResHpcClusterStatus;
import cn.com.cloudstar.rightcloud.common.constants.res.type.CloudEnvType;
import cn.com.cloudstar.rightcloud.common.enums.resource.HPCClusterTypeEnum;
import cn.com.cloudstar.rightcloud.common.exception.BizException;
import cn.com.cloudstar.rightcloud.common.exception.RetryException;
import cn.com.cloudstar.rightcloud.common.util.*;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicInfoUtil;
import cn.com.cloudstar.rightcloud.module.support.access.util.PlaintextShieldUtil;
import cn.com.cloudstar.rightcloud.oss.common.cache.JedisUtil;
import cn.com.cloudstar.rightcloud.oss.common.constants.status.CertificationStatus;
import cn.com.cloudstar.rightcloud.oss.common.constants.status.UserStatus;
import cn.com.cloudstar.rightcloud.oss.common.constants.type.LdapPropertyKey;
import cn.com.cloudstar.rightcloud.oss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.oss.common.enums.SfProductEnum;
import cn.com.cloudstar.rightcloud.oss.common.pojo.LdapSyncRequest;
import cn.com.cloudstar.rightcloud.oss.common.pojo.ResHpcClusterVO;
import cn.com.cloudstar.rightcloud.oss.common.util.CloudClientFactory;
import cn.com.cloudstar.rightcloud.oss.common.util.PropertiesUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.ldap.ActiveDirectory;
import cn.com.cloudstar.rightcloud.oss.common.util.ldap.OpenLdapUser;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.cloud.request.CloudEnvParams;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.LdapInfo;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.ResHpcClusterPool;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.ResHpcClusterRemoteModule;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.cloud.CloudEnvRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.hpc.HPCRemoteService;

import static cn.com.cloudstar.rightcloud.oss.common.util.ldap.ActiveDirectory.getCcpInternalAddr;

/**
 * <AUTHOR>
 * @date 2021/1/19 1:40 下午
 */
@Service
@Slf4j
public class LdapUserServiceImpl implements LdapUserService {

    private static final String OPEN_TAG = "1";
    private static final String CLOSE_TAG = "0";
    private static final Integer CYCLE_COUNT = 4;
    private static final String SUCCESS = "success";
    private static final String PART_SUCCESS = "partSuccess";
    @Autowired
    private ConfigMapper configMapper;
    @Autowired
    @Lazy
    private SysUserService sysUserService;
    @Autowired
    private OrgService orgService;
    @Autowired
    @Lazy
    private IPolicyAssertionService policyAssertionService;

    @Autowired
    @Lazy
    private IPolicyService policyService;

    @Autowired
    @Lazy
    private HpcClusterService hpcClusterService;

    @DubboReference
    private CloudEnvRemoteService cloudEnvRemoteService;

    @Autowired
    private ThreadPoolTaskExecutor cloudExecutor;
    @Autowired
    private SysHpcPassService hpcPassService;

    @Resource
    private SfProductResourceMapper sfProductResourceMapper;

    @DubboReference
    private HPCRemoteService hpcRemoteService;

    private final String LOCK_FLAG = "distributedLock:ldapSync:orgSid:";
    private final String CHCECK_AND_ACTIVE_LOCK_FLAG = "distributedLock:ccpactive:orgSid:";

    public final static List<String> CLUSTER_NO_IN_STATUS =
            Arrays.asList("apply", "deleted", "unsubscribed", "deleting", "unsubscribing", "rejected");

    @Override
    public void createLdapUser(OpenLdapUser openLdapUser) throws NamingException {
        List<Map<String, String>> ldapConfig = getLdapConfig();
        if (ldapConfig.stream().noneMatch(t -> "1".equals(t.get(LdapPropertyKey.LDAP_ENABLE)))) {
            log.info("ldap 已关闭");
            return;
        }
        List<InitialLdapContext> initialLdapContexts = getLdapContext(ldapConfig);
        int index = 0;
        for (Map<String, String> config : ldapConfig) {
            String baseDN = config.get(LdapPropertyKey.LDAP_DOMAINNAME);
            openLdapUser.setBaseDN(baseDN);
            // 如果开通了HPC,则设为启用状态
            if (StringUtil.isNullOrEmpty(openLdapUser.getHomePhone())) {
                openLdapUser.setHomePhone(config.get(LdapPropertyKey.LDAP_HOME_PHONE));
            }
            ActiveDirectory.createLdapUser(initialLdapContexts.get(index), openLdapUser);
            index++;
        }
    }

    @Override
    public void deleteLdapUser(OpenLdapUser openLdapUser) throws NamingException {
        List<Map<String, String>> ldapConfig = getLdapConfig();
        if (ldapConfig.stream().noneMatch(t -> "1".equals(t.get(LdapPropertyKey.LDAP_ENABLE)))) {
            log.info("ldap 已关闭");
            return;
        }
        List<InitialLdapContext> initialLdapContexts = getLdapContext(ldapConfig);
        int index = 0;
        for (Map<String, String> config : ldapConfig) {
            String baseDN = config.get(LdapPropertyKey.LDAP_DOMAINNAME);
            openLdapUser.setBaseDN(baseDN);
            // 如果开通了HPC,则设为启用状态
            if (StringUtil.isNullOrEmpty(openLdapUser.getHomePhone())) {
                openLdapUser.setHomePhone(config.get(LdapPropertyKey.LDAP_HOME_PHONE));
            }
            ActiveDirectory.deleteLdapUser(initialLdapContexts.get(index), openLdapUser);
            index++;
        }
    }

    public boolean isEnableLdap(Map<String, String> ldapConnectParams) {
        String s = ldapConnectParams.get(LdapPropertyKey.LDAP_ENABLE);
        return "1".equals(s);
    }

    public List<Map<String, String>> getLdapConfig() {
        List<Map<String, String>> configs = new ArrayList<>();
        Map<String, String> collect = new HashMap<>();
        AuthUser authUserInfo = RequestContextUtil.getAuthUserInfo();
        log.info("LdapUserServiceImpl-getLdapConfig-组织orgId：【{}】", authUserInfo.getOrgSid());
        Criteria criteria = new Criteria();
        criteria.put("org_sid", authUserInfo.getOrgSid());
        criteria.put("product_type", ProductCodeEnum.HPC_OFFLINE.getProductCode());
        criteria.put("noInStatusList",
                     Stream.of(SfProductEnum.PENDING, SfProductEnum.UNSUBSCRIBED).collect(Collectors.toList()));
        List<SfProductResource> sfProductResources = sfProductResourceMapper.selectByParams(criteria);
        if (sfProductResources.size() > 0) {
            SfProductResource productRes = CollectionUtil.getFirst(sfProductResources);
            Long clusterId = productRes.getClusterId();
            ResHpcClusterRemoteModule resHpcClusterRemoteModule = hpcRemoteService.selectByPrimaryKey(clusterId);

            log.info("LdapUserServiceImpl-getLdapConfig-线下clusterId：【{}】", resHpcClusterRemoteModule.getPoolUuid());
            List<ResHpcClusterPool> resHpcClusterPools = hpcRemoteService.selectResHpcClusterPoolByClusterId(
                    resHpcClusterRemoteModule.getPoolUuid());
            if (resHpcClusterPools.size() > 0 && StringUtils.isNotEmpty(
                    CollectionUtil.getFirst(resHpcClusterPools).getLdapInfo())) {
                String ldapInfo = CollectionUtil.getFirst(resHpcClusterPools).getLdapInfo();
                LdapInfo convert = BeanConvertUtil.convert(ldapInfo, LdapInfo.class);
                collect.put(LdapPropertyKey.LDAP_DOMAINNAME, convert.getLdapDomainName());
                collect.put(LdapPropertyKey.LDAP_SERVERNAME, convert.getLdapServerName());
                collect.put(LdapPropertyKey.LDAP_ENABLE, OPEN_TAG);
                collect.put(LdapPropertyKey.LDAP_USERNAME, convert.getLdapUserName());
                collect.put(LdapPropertyKey.LDAP_CIPHER, CrytoUtilSimple.decrypt(convert.getLdapPassword()));
                collect.put(LdapPropertyKey.LDAP_PORT, convert.getLdapPort());
                collect.put(LdapPropertyKey.LDAP_CONNECT_TYPE, convert.getLdapConnectType());
                collect.put(LdapPropertyKey.LDAP_CREDENTIALS_PATH, convert.getLdapCredentialsPath());
                collect.put(LdapPropertyKey.LDAP_SHADOW_USER, String.valueOf(convert.getLdapShadowUser()));
                collect.put(LdapPropertyKey.IS_SHARE, 1 == convert.getLdapIsShare() ? "true" : "false");
                configs.add(collect);
                log.info("LdapUserServiceImpl-getLdapConfig-获取独立配置");
            }
        }
        LambdaQueryWrapper<SysConfig> wrapper = new LambdaQueryWrapper<>();
        List<SysConfig> ldapConfig = configMapper.selectList(wrapper.eq(SysConfig::getConfigType, "ldap_config"));
        configs.add(ldapConfig.stream().collect(Collectors.toMap(SysConfig::getConfigKey,
                                                                 SysConfig::getConfigValue)));
        configs = configs.stream().distinct().collect(Collectors.toList());
        log.info("LdapUserServiceImpl-getLdapConfig-configs数量：【{}】", configs.size());
        return configs;
    }

    @Override
    public Boolean getLdapContext(OperationLdapRequest operationLdapRequest) {
        String headerUrl = "ldap://";
        try {
            Hashtable<String, String> props = new Hashtable<>();
            if (OPEN_TAG.equals(operationLdapRequest.getConnectType())) {
                props.put(Context.SECURITY_PROTOCOL, "ssl");
                headerUrl = "ldaps://";
                //免证书认证 占时不用 防止错误上传路径
                props.put("java.naming.ldap.factory.socket",
                          "cn.com.cloudstar.rightcloud.bss.module.access.openldap.DummySSLSocketFactory");
                System.setProperty("javax.net.ssl.trustStore", operationLdapRequest.getCredentials());
            }
            props.put(Context.SECURITY_CREDENTIALS, CrytoUtilSimple.decrypt(operationLdapRequest.getPassword()));
            props.put(Context.SECURITY_AUTHENTICATION, "simple");
            props.put(Context.SECURITY_PRINCIPAL, operationLdapRequest.getUserName());
            props.put(Context.INITIAL_CONTEXT_FACTORY, "com.sun.jndi.ldap.LdapCtxFactory");
            props.put(Context.PROVIDER_URL,
                      headerUrl + operationLdapRequest.getServerName() + ":" + operationLdapRequest.getPort());
            InitialLdapContext initialLdapContext = new InitialLdapContext(props, null);
            return initialLdapContext != null ? true : false;
        } catch (NamingException e) {
            e.printStackTrace();
        }
        return false;
    }

    public List<InitialLdapContext> getLdapContext(List<Map<String, String>> configs) {
        String headerUrl = "ldap://";
        ArrayList<InitialLdapContext> initialLdapContexts = new ArrayList<>();
        try {
            for (Map<String, String> config : configs) {
                Hashtable<String, String> props = new Hashtable<>();
                if (OPEN_TAG.equals(config.get(LdapPropertyKey.LDAP_CONNECT_TYPE))) {
                    props.put(Context.SECURITY_PROTOCOL, "ssl");
                    headerUrl = "ldaps://";
                    //免证书认证 占时不用 防止错误上传路径
                    props.put("java.naming.ldap.factory.socket",
                              "cn.com.cloudstar.rightcloud.bss.module.access.openldap.DummySSLSocketFactory");
                    System.setProperty("javax.net.ssl.trustStore", config.get(LdapPropertyKey.LDAP_CREDENTIALS_PATH));
                }
                try {
                    props.put(Context.SECURITY_CREDENTIALS,
                              CrytoUtilSimple.decrypt(config.get(LdapPropertyKey.LDAP_CIPHER)));
                } catch (NumberFormatException e) {
                    props.put(Context.SECURITY_CREDENTIALS, config.get(LdapPropertyKey.LDAP_CIPHER));
                }
                props.put(Context.SECURITY_AUTHENTICATION, "simple");
                props.put(Context.SECURITY_PRINCIPAL, config.get(LdapPropertyKey.LDAP_USERNAME));
                props.put(Context.INITIAL_CONTEXT_FACTORY, "com.sun.jndi.ldap.LdapCtxFactory");
                props.put(Context.PROVIDER_URL,
                          headerUrl + config.get(LdapPropertyKey.LDAP_SERVERNAME) + ":" + config.get(
                                  LdapPropertyKey.LDAP_PORT));
                initialLdapContexts.add(new InitialLdapContext(props, null));
            }
            return initialLdapContexts;
        } catch (NamingException e) {
            e.printStackTrace();
        }
        return null;
    }

    public InitialLdapContext getLdapContext(Map<String, String> config) {
        String headerUrl = "ldap://";
        ArrayList<InitialLdapContext> initialLdapContexts = new ArrayList<>();
        try {
            Hashtable<String, String> props = new Hashtable<>();
            if (OPEN_TAG.equals(config.get(LdapPropertyKey.LDAP_CONNECT_TYPE))) {
                props.put(Context.SECURITY_PROTOCOL, "ssl");
                headerUrl = "ldaps://";
                //免证书认证 占时不用 防止错误上传路径
                props.put("java.naming.ldap.factory.socket",
                          "cn.com.cloudstar.rightcloud.bss.module.access.openldap.DummySSLSocketFactory");
                System.setProperty("javax.net.ssl.trustStore", config.get(LdapPropertyKey.LDAP_CREDENTIALS_PATH));
            }
            try {
                props.put(Context.SECURITY_CREDENTIALS,
                          CrytoUtilSimple.decrypt(config.get(LdapPropertyKey.LDAP_CIPHER)));
            } catch (NumberFormatException e) {
                props.put(Context.SECURITY_CREDENTIALS, config.get(LdapPropertyKey.LDAP_CIPHER));
            }
            props.put(Context.SECURITY_AUTHENTICATION, "simple");
            props.put(Context.SECURITY_PRINCIPAL, config.get(LdapPropertyKey.LDAP_USERNAME));
            props.put(Context.INITIAL_CONTEXT_FACTORY, "com.sun.jndi.ldap.LdapCtxFactory");
            props.put(Context.PROVIDER_URL,
                      headerUrl + config.get(LdapPropertyKey.LDAP_SERVERNAME) + ":" + config.get(
                              LdapPropertyKey.LDAP_PORT));
            return new InitialLdapContext(props, null);
        } catch (NamingException e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    @Async
    public void synHpcToLdapAsync(LdapSyncRequest request) {
        log.info("异步同步Ldap用户信息{}", RequestContextUtil.getAuthUserInfo());
        synHPCTOLdap(request);
    }

    @Override
    public void synHPCTOLdap(LdapSyncRequest request) {
        log.info("LdapUserServiceImpl.synHPCTOLdap ldap同步请求参数[{}]",request);
        List<Map<String, String>> configs = getLdapConfig();
        //如果ldap没有启用，则直接跳过
        List<String> ldapEnables = configs.stream()
                                      .map(t -> t.get(LdapPropertyKey.LDAP_ENABLE))
                                      .collect(Collectors.toList());
        log.info("LdapUserServiceImpl.synHPCTOLdap LDAP启用状态：[{}]", JSONUtil.toJsonStr(ldapEnables));
        if (ldapEnables.stream().allMatch(CLOSE_TAG::equals)) {
            return;
        }

        boolean setnx = JedisUtil.INSTANCE.setnx(LOCK_FLAG + request.getOrgId(), 300000);
        log.info("LdapUserServiceImpl.synHPCTOLdap 分布式锁获取结果：[{}]",setnx);

        OpenLdapUser shadowUser = null;
        if (setnx) {
            try {
                long timeBefor = System.currentTimeMillis();
                log.info("LdapUserServiceImpl.synHPCTOLdap LDAP同步：开始同步");
                Org rootOrg = orgService.selectRootOrg(request.getOrgId());
                log.info("LdapUserServiceImpl.synHPCTOLdap LDAP同步：rootOrg [{}]", JSONUtil.toJsonStr(rootOrg));
                if (rootOrg == null) {
                    log.info("LdapUserServiceImpl.synHPCTOLdap LDAP同步：rootOrg为空，结束同步");
                    return;
                }

                List<User> userList;
                if (CollectionUtils.isEmpty(request.getUserIds())){
                    // 查询该组织下的所用用户
                    List<Org> orgList = orgService.lambdaQuery().and(o -> o.eq(Org::getOrgSid, rootOrg.getOrgSid())
                            .or()
                            .like(Org::getTreePath, "'%/" + rootOrg.getOrgSid() + "/%'")).list();
                    if (CollectionUtils.isEmpty(orgList)) {
                        log.info("LdapUserServiceImpl.synHPCTOLdap LDAP同步：rogList为空，结束同步");
                        return;
                    }
                    userList = sysUserService.lambdaQuery().in(User::getOrgSid
                            , orgList.stream().map(Org::getOrgSid).collect(Collectors.toList()))
                            .notIn(User::getStatus, UserStatus.DELETED, UserStatus.REFUSE).list();
                    if (CollectionUtils.isEmpty(userList)) {
                        log.info("LdapUserServiceImpl.synHPCTOLdap LDAP同步：userList为空，结束同步");
                        return;
                    }
                }else {
                    userList = new ArrayList<>();
                    for (Long userId : request.getUserIds()){
                        userList.add(sysUserService.selectByPrimaryKey(userId));
                    }
                }
                String orgCertificationStatus = rootOrg.getCertificationStatus();
                log.info("LdapUserServiceImpl.synHPCTOLdap LDAP同步：orgCertificationStatus [{}]", orgCertificationStatus);

                //存在个人认证用户 或企业认证的用户需要进行激活
                boolean needActiveUser = userList.stream().anyMatch(user -> {
                    //增加企业认证逻辑判断
                    return CertificationStatus.AUTHSUCCEED.equals(user.getCertificationStatus()) || CertificationStatus.AUTHSUCCEED.equals(orgCertificationStatus);
                });
                //没有HPC通行证不激活
                List<SysHpcPass> hpcPasses = hpcPassService.getByUserIds(userList.stream().map(User::getUserSid).collect(Collectors.toSet()));
                if (CollectionUtils.isEmpty(hpcPasses)) {
                    log.info("LdapUserServiceImpl.synHPCTOLdap LDAP同步：未配置通行证，结束同步");
                    return;
                }
                Map<Long, SysHpcPass> hpcPasseMap = hpcPasses.stream().collect(Collectors.toMap(SysHpcPass::getUserSid, Function.identity()));
                userList = userList.stream().filter(u -> hpcPasseMap.containsKey(u.getUserSid())).collect(Collectors.toList());
              //  userList.stream().filter(u -> hpcPasseMap.containsKey(u.getUserSid())).forEach(e -> e.setHpcPass(hpcPasseMap.get(e.getUserSid()).getPassword()));


                List<ResHpcClusterVO> allResource = policyAssertionService.getHpcNormalResource(rootOrg.getOrgSid());
                allResource = allResource.stream()
                        .filter(resHpcClusterVO -> !CLUSTER_NO_IN_STATUS.contains(resHpcClusterVO.getStatus()))
                        .collect(Collectors.toList());
                log.info("LdapUserServiceImpl.synHPCTOLdap 全量hpc资源池：[{}]",JSONUtil.toJsonStr(allResource));

                for (Map<String, String> config : configs) {
                    String baseDN = config.get(LdapPropertyKey.LDAP_DOMAINNAME);

                    InitialLdapContext ldapContext = getLdapContext(config);

                    if (!CollectionUtils.isEmpty(request.getBusinessCategoryList())) {
                        allResource = allResource.stream()
                                                 .filter(resHpcClusterVO -> request.getBusinessCategoryList()
                                                                                   .contains(
                                                                                           resHpcClusterVO.getBusinessCategory()))
                                                 .collect(Collectors.toList());
                        log.info("LdapUserServiceImpl.synHPCTOLdap 准备ccp激活的资源池：[{}]", JSONUtil.toJsonStr(allResource));
                    }

                    //只有专属资源池激活需要影子用户
                    boolean privateTag = needActiveUser && allResource.stream()
                                                                      .anyMatch(
                                                                              resHpcClusterVO -> HPCClusterTypeEnum.getHPCPrivateTypes()
                                                                                                                   .contains(
                                                                                                                           resHpcClusterVO
                                                                                                                                   .getClusterType()));

                CloudEnvParams params = new CloudEnvParams();
                params.setCloudEnvType(CloudEnvType.HCSO.getValue().get(0));
                List<CloudEnv> cloudEnvs = cloudEnvRemoteService.selectByParams(params);
                if (CollectionUtils.isEmpty(cloudEnvs)) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1000498884));
                }

                    //此处查询结果为有HPC权限的userId，过滤userList中无权限的用户，status设为0
                    List<Long> userIds = policyService.findHpcUsers(
                            userList.stream().map(User::getUserSid).collect(Collectors.toList()));
                    userList.forEach(user -> {
                        if (!userIds.contains(user.getUserSid())) {
                            log.info("LdapUserServiceImpl.synHPCTOLdap 无HPC权限的用户：[{}]", user.getAccount());
                            user.setStatus("0");
                        }
                    });

                    // 获取拥有所有数据权限的策略
                    Map<String, List<ResHpcClusterVO>> hpcListMap = new HashMap<>(5);

                    // LDAP同步用户
                    List<Boolean> successFlag = this.updateHpcResources(userList, rootOrg
                            , hpcListMap, baseDN, orgCertificationStatus, config, ldapContext, request);

                    boolean ccpActiveFlg = Objects.nonNull(request.getIsCcpActive()) && request.getIsCcpActive();
                    boolean ccpActiveUserCheckFlg =
                            Objects.nonNull(request.getCcpUserActiveCheck()) && request.getCcpUserActiveCheck();
                    //需要激活专属资源池场景下，ou下只有租户管理员时才创建影子用户
                    String needShadowUser = config.get(LdapPropertyKey.LDAP_SHADOW_USER);
                    log.info("LdapUserServiceImpl.synHPCTOLdap 是否需要影子用户：[{}]", JSONUtil.toJsonStr(needShadowUser));
                    if (privateTag && !CLOSE_TAG.equals(needShadowUser) && ccpActiveFlg) {
                        shadowUser = creatShadowUser(userList.get(0), allResource, baseDN, ldapContext, rootOrg);
                    }
                    //ldap user同步完成后，才开始激活用户
                    //没有个人认证的用户不进行激活
                    if (ccpActiveUserCheckFlg) {
                        doCCPUserActiveCheck(userList, needActiveUser, successFlag);
                    }
                    if (ccpActiveFlg) {
                        doCCPUserActive(shadowUser, rootOrg, userList, orgCertificationStatus, needActiveUser,
                                        allResource, cloudEnvs, hpcListMap, successFlag);
                    }
                }
                long timeAfter = System.currentTimeMillis();
                log.info("LdapUserServiceImpl.synHPCTOLdap LDAP同步：同步结束，花费时间：[{}]秒", (timeAfter - timeBefor)/1000);
            }catch (Exception e) {
                log.error("LdapUserServiceImpl.synHPCTOLdap LDAP同步["+request.getOrgId()+"]异常：",e);
                throw e;
            } finally {
                if (Objects.nonNull(shadowUser)){
                    try {
                        for (Map<String, String> config : configs) {
                            log.info("LdapUserServiceImpl.synHPCTOLdap 开始删除影子用户,{}", shadowUser.getUserName());
                            ActiveDirectory.deleteLdapUser(getLdapContext(config), shadowUser);
                            log.debug("LdapUserServiceImpl.synHPCTOLdap 删除影子用户成功,{}", shadowUser.getUserName());
                        }
                    } catch (Exception exception) {
                        log.error("LdapUserServiceImpl.synHPCTOLdap 删除影子用户失败", exception);
                    }
                }

                JedisUtil.INSTANCE.del(LOCK_FLAG + request.getOrgId());
            }
        }else {
            // 直接抛出异常
            throw new BizException("正在进行Ldap账号同步，请勿重复操作");
        }
    }


    /**
     * CCP激活前检查LDAP同步是否正常。
     * @param userList
     * @param needActiveUser
     * @param successFlag
     */
    private void doCCPUserActiveCheck(List<User> userList, boolean needActiveUser,List<Boolean> successFlag) {
        log.info("LdapUserServiceImpl.synHPCTOLdap doCCPUserActiveCheck successFlagSize: {},userListSize: {} ", successFlag.size(), userList.size());
        if (successFlag.size() == userList.size()){
            List<Boolean> collect = successFlag.stream().filter(b -> b).collect(Collectors.toList());
            log.info("LdapUserServiceImpl.doCCPUserActiveCheck 异步执行成功数量：[{}],needActiveUser:[{}]",collect.size(), needActiveUser);

            log.info("LdapUserServiceImpl.doCCPUserActive collectSize: {},successFlagSize: {} ", collect.size(), successFlag.size());
            if (collect.size() != successFlag.size()){
                log.info("LdapUserServiceImpl.doCCPUserActiveCheck 异步执行结果：成功数量与结果数量不一致");
                throw new BizException("Ldap同步用户异常，请重试");
            }
        }else {
            log.info("LdapUserServiceImpl.doCCPUserActiveCheck 异步执行结果：数量与userList不一致");
            throw new BizException("Ldap同步用户异常，请重试");
        }
    }

    @Override
    public void checkUserAndCcpActive(LdapSyncRequest request) {
        log.info("LdapUserServiceImpl.checkUserAndCcpActive ccp激活请求[{}]",request);

        boolean setnx = JedisUtil.INSTANCE.setnx(CHCECK_AND_ACTIVE_LOCK_FLAG + request.getOrgId(), 300000);
        log.info("LdapUserServiceImpl.checkUserAndCcpActive 分布式锁获取结果：[{}]",setnx);

        if (setnx) {
            try {
                long timeBefor = System.currentTimeMillis();
                log.info("LdapUserServiceImpl.checkUserAndCcpActive ccp激活：开始同步");
                Org rootOrg = orgService.selectRootOrg(request.getOrgId());
                log.info("LdapUserServiceImpl.checkUserAndCcpActive  ccp激活：rootOrg [{}]", JSONUtil.toJsonStr(rootOrg));
                if (rootOrg == null) {
                    log.info("LdapUserServiceImpl.checkUserAndCcpActive ccp激活：rootOrg为空，结束激活");
                    return;
                }

                List<User> userList;
                if (CollectionUtils.isEmpty(request.getUserIds())){
                    // 查询该组织下的所用用户
                    List<Org> orgList = orgService.lambdaQuery().and(o -> o.eq(Org::getOrgSid, rootOrg.getOrgSid())
                        .or()
                        .like(Org::getTreePath, "'%/" + rootOrg.getOrgSid() + "/%'")).list();
                    if (CollectionUtils.isEmpty(orgList)) {
                        log.info("LdapUserServiceImpl.checkUserAndCcpActive ccp激活：rogList为空，结束同步");
                        return;
                    }
                    userList = sysUserService.lambdaQuery().in(User::getOrgSid
                            , orgList.stream().map(Org::getOrgSid).collect(Collectors.toList()))
                        .notIn(User::getStatus, UserStatus.DELETED, UserStatus.REFUSE).list();
                    if (CollectionUtils.isEmpty(userList)) {
                        log.info("LdapUserServiceImpl.checkUserAndCcpActive ccp激活：userList为空，结束同步");
                        return;
                    }
                }else {
                    userList = new ArrayList<>();
                    for (Long userId : request.getUserIds()){
                        userList.add(sysUserService.selectByPrimaryKey(userId));
                    }
                }
                String orgCertificationStatus = rootOrg.getCertificationStatus();
                log.info("LdapUserServiceImpl.checkUserAndCcpActive ccp激活：orgCertificationStatus [{}]", orgCertificationStatus);

                //存在个人认证用户 或企业认证的用户需要进行激活
                boolean needActiveUser = userList.stream().anyMatch(user -> {
                    //增加企业认证逻辑判断
                    return CertificationStatus.AUTHSUCCEED.equals(user.getCertificationStatus()) || CertificationStatus.AUTHSUCCEED.equals(orgCertificationStatus);
                });
                //没有HPC通行证不激活
                List<SysHpcPass> hpcPasses = hpcPassService.getByUserIds(userList.stream().map(User::getUserSid).collect(Collectors.toSet()));
                if (CollectionUtils.isEmpty(hpcPasses)) {
                    log.info("LdapUserServiceImpl.checkUserAndCcpActive ccp激活：未配置通行证，结束同步");
                    return;
                }
                Map<Long, SysHpcPass> hpcPasseMap = hpcPasses.stream().collect(Collectors.toMap(SysHpcPass::getUserSid, Function.identity()));
                userList = userList.stream().filter(u -> hpcPasseMap.containsKey(u.getUserSid())).collect(Collectors.toList());
                userList.stream().filter(u -> hpcPasseMap.containsKey(u.getUserSid())).forEach(e -> e.setHpcPass(hpcPasseMap.get(e.getUserSid()).getPassword()));


                List<ResHpcClusterVO> allResource = policyAssertionService.getHpcNormalResource(rootOrg.getOrgSid());
                allResource = allResource.stream()
                    .filter(resHpcClusterVO -> !CLUSTER_NO_IN_STATUS.contains(resHpcClusterVO.getStatus()))
                    .collect(Collectors.toList());
                log.info("LdapUserServiceImpl.checkUserAndCcpActive 全量hpc资源池：[{}]",JSONUtil.toJsonStr(allResource));



                if (!CollectionUtils.isEmpty(request.getBusinessCategoryList())){
                    allResource = allResource.stream()
                        .filter(resHpcClusterVO -> request.getBusinessCategoryList().contains(resHpcClusterVO.getBusinessCategory()))
                        .collect(Collectors.toList());
                    log.info("LdapUserServiceImpl.checkUserAndCcpActive 准备ccp激活的资源池：[{}]",JSONUtil.toJsonStr(allResource));
                }

                //只有专属资源池激活需要影子用户
                boolean privateTag = needActiveUser && allResource.stream()
                    .anyMatch(resHpcClusterVO -> HPCClusterTypeEnum.getHPCPrivateTypes().contains(resHpcClusterVO.getClusterType()));

                CloudEnvParams params = new CloudEnvParams();
                params.setCloudEnvType(CloudEnvType.HCSO.getValue().get(0));
                List<CloudEnv> cloudEnvs = cloudEnvRemoteService.selectByParams(params);
                if (CollectionUtils.isEmpty(cloudEnvs)) {
                    throw new BizException("未接入HCSO云环境");
                }

                //此处查询结果为有HPC权限的userId，过滤userList中无权限的用户，status设为0
                List<Long> userIds = policyService.findHpcUsers(userList.stream().map(User::getUserSid).collect(Collectors.toList()));
                userList.forEach(user -> {
                    if (!userIds.contains(user.getUserSid())){
                        log.info("LdapUserServiceImpl.checkUserAndCcpActive 无HPC权限的用户：[{}]",user.getAccount());
                        user.setStatus("0");
                    }
                });

                // 获取拥有所有数据权限的策略
                Map<String,List<ResHpcClusterVO>> hpcListMap=new HashMap<>(5);
                userList.stream().forEach(user ->filterAndStoreHpcClusters(rootOrg, hpcListMap, request, user));
                //需要激活专属资源池场景下，ou下只有租户管理员时才创建影子用户

                //ldap user同步完成后，才开始激活用户
                //没有个人认证的用户不进行激活
                List<Boolean> successFlag = userList.stream().map(u -> Boolean.TRUE).collect(Collectors.toList());
                doCCPUserActive(null, rootOrg, userList, orgCertificationStatus, needActiveUser, allResource, cloudEnvs, hpcListMap, successFlag);

                long timeAfter = System.currentTimeMillis();
                log.info("LdapUserServiceImpl.checkUserAndCcpActive ccp激活：同步结束，花费时间：[{}]秒", (timeAfter - timeBefor)/1000);
            }catch (Exception e) {
                log.error("LdapUserServiceImpl.checkUserAndCcpActive ccp激活["+request.getOrgId()+"]异常：",e);
                throw e;
            } finally {
                JedisUtil.INSTANCE.del(CHCECK_AND_ACTIVE_LOCK_FLAG + request.getOrgId());
            }
        }else {
            // 直接抛出异常
            throw new BizException("正在进行账号CCP激活，请勿重复操作");
        }
    }


    /**
     *
     * @param shadowUser
     * @param rootOrg
     * @param userList
     * @param orgCertificationStatus
     * @param needActiveUser
     * @param allResource
     * @param cloudEnvs
     * @param hpcListMap
     * @param successFlag
     */
    private void doCCPUserActive(OpenLdapUser shadowUser, Org rootOrg, List<User> userList, String orgCertificationStatus, boolean needActiveUser, List<ResHpcClusterVO> allResource, List<CloudEnv> cloudEnvs, Map<String, List<ResHpcClusterVO>> hpcListMap, List<Boolean> successFlag) {
        log.info("LdapUserServiceImpl.synHPCTOLdap doCCPUserActive successFlagSize: {},userListSize: {} ", successFlag.size(), userList.size());
        if (successFlag.size() == userList.size()){
            List<Boolean> collect = successFlag.stream().filter(b -> b).collect(Collectors.toList());
            log.info("LdapUserServiceImpl.ccpActive 异步执行成功数量：[{}],needActiveUser:[{}]",collect.size(), needActiveUser);
            if (!CertificationStatus.AUTHSUCCEED.equals(orgCertificationStatus)){
                userList.removeIf(userLadp -> !CertificationStatus.AUTHSUCCEED.equals(userLadp.getCertificationStatus()));
            }

            log.info("LdapUserServiceImpl.doCCPUserActive collectSize: {},successFlagSize: {} ", collect.size(), successFlag.size());
            if (collect.size() == successFlag.size()){
                if(needActiveUser){

                    this.activeCCPUser(allResource, userList, hpcListMap, rootOrg, cloudEnvs, shadowUser);
                    //作业模板激活
                    List<ResHpcClusterVO> share = allResource.stream()
                            .filter(e -> HPCClusterTypeEnum.SAAS_SHARE.getCode().equals(e.getClusterType()))
                            .collect(Collectors.toList());
                    this.grantJobTemplate(share, rootOrg, cloudEnvs.get(0));
                }
            }else {
                log.info("LdapUserServiceImpl.doCCPUserActive 异步执行结果：成功数量与结果数量不一致");
                throw new BizException("Ldap同步用户异常，请重试");
            }
        }else {
            log.info("LdapUserServiceImpl.doCCPUserActive 异步执行结果：数量与userList不一致");
            throw new BizException("Ldap同步用户异常，请重试");
        }
    }

    private void grantJobTemplate(List<ResHpcClusterVO> share, Org rootOrg, CloudEnv cloudEnv) {
        //查询租户管理员账号密码
        AuthUser userAdmin = BasicInfoUtil.getUserInfoByUserSid(rootOrg.getOwner());
        share.forEach(e -> {
            GrantJobTemplate grantJobTemplate = CloudClientFactory.buildMQBean(cloudEnv.getId(),
                                                                               GrantJobTemplate.class);
            grantJobTemplate.setOptions(MapsKit.of("ROOT_URL", getCcpInternalAddr(e.getCcpInternelAddress())));
            grantJobTemplate.setTenantUserName(CrytoUtilSimple.decrypt(e.getUsername()));
            grantJobTemplate.setTenantUserPass(CrytoUtilSimple.decrypt(e.getPassword()));
            GrantJobUser grantJobUser = new GrantJobUser();
            grantJobUser.setGrantUser(String.format("%s/%s", rootOrg.getLdapOu(), userAdmin.getAccount()));
            grantJobUser.setRole("hpcGroupAdmin");
            ArrayList<GrantJobUser> grantJobUsers = new ArrayList<>();
            grantJobUsers.add(grantJobUser);
            grantJobTemplate.setGrantJobUsers(grantJobUsers);
            log.info("activeJobTemplate-[{}]-[{}]-[{}]", e.getClusterType(), rootOrg.getOrgSid(), userAdmin.getAccount());
            try {
                GrantJobTemplateResult result = (GrantJobTemplateResult) MQHelper.rpc(grantJobTemplate);
                if (result.isSuccess() && StringUtils.equals(result.getCode(), SUCCESS)) {
                    log.info("activeJobTemplate-success-[{}]-[{}]", e.getClusterType(), JSONUtil.toJsonStr(result.getData().getSuccessData()));
                } else if (result.isSuccess() && StringUtils.equals(result.getCode(), PART_SUCCESS)) {
                    log.info("activeJobTemplate-partSuccess-[{}]-[{}]", e.getClusterType(), JSONUtil.toJsonStr(result.getData().getErrorData()));
                } else {
                    log.error("activeJobTemplate-failure-[{}]-[{}]", e.getClusterType(), JSONUtil.toJsonStr(result.getData().getErrorData()));
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                log.error("activeJobTemplate-exception：[{}]", ex.getMessage());
            }
        });
    }

    /**
     * ccp激活
     * @param allResource
     * @param userList
     * @param hpcListMap
     * @param rootOrg
     * @param cloudEnvs
     * @param shadowUser
     */
    private void activeCCPUser(List<ResHpcClusterVO> allResource, List<User> userList,
                               Map<String, List<ResHpcClusterVO>> hpcListMap, Org rootOrg, List<CloudEnv> cloudEnvs, OpenLdapUser shadowUser) {

        for (ResHpcClusterVO hpcRe : allResource) {
            List<String> listAccount=new ArrayList<>();
            //只激活有此资源权限的认证成功用户及子用户，并且专属资源池不激活租户管理员
            userList.forEach(user -> {
                List<ResHpcClusterVO> resHpcClusterVOS = hpcListMap.get(user.getAccount());

                if(!CollectionUtils.isEmpty(resHpcClusterVOS)){
                    // 当前资源是否是租户有权限的资源
                    boolean noneMatch = resHpcClusterVOS.stream()
                            .noneMatch(resHpcClusterVO -> StringUtils.isNotBlank(resHpcClusterVO.getBusinessCategory())
                                    && resHpcClusterVO.getBusinessCategory().equals(hpcRe.getBusinessCategory()));
                    // 是否专属资源池&&租户管理员&&是否有影子用户，专属资源池&&影子用户为空则认为ccp已支持通过租户管理员激活资源
                    boolean b = Objects.isNull(user.getParentSid())
                            && HPCClusterTypeEnum.getHPCPrivateTypes().contains(hpcRe.getClusterType())
                            && Objects.nonNull(shadowUser);
                    if (b || noneMatch){
                        //专属资源池激活不提交租户管理员
                        return;
                    }
                    listAccount.add(rootOrg.getLdapOu()+"/"+user.getAccount());
                }
            });

            if (Objects.nonNull(shadowUser) && HPCClusterTypeEnum.getHPCPrivateTypes().contains(hpcRe.getClusterType())){
                //ou下只有租户管理员的场景，需要提交影子用户，且只有专属资源池激活需要影子用户
                listAccount.add(rootOrg.getLdapOu()+"/"+shadowUser.getUserName());
            }

            if (HPCClusterTypeEnum.AD_SAAS_PRIVATE.getCode().equals(hpcRe.getClusterType())
                    || HPCClusterTypeEnum.SAAS_PRIVATE.getCode().equals(hpcRe.getClusterType())) {
                // 修改为专属资源池每次ccp激活前同步节点ip信息，目前只涉及自定义专属资源池
                log.info("LdapUserServiceImpl.activeCCPUser 更新指定HPC资源池的节点信息 LdapUserServiceImpl.activeCCPUser OUTPUT: {}", hpcRe.getId());
                RestResult result = hpcClusterService.updateHPCClusterCreateTask(hpcRe.getId());
                log.info("LdapUserServiceImpl.activeCCPUser 更新指定HPC资源池的节点信息 LdapUserServiceImpl.activeCCPUser result OUTPUT: {}", result == null ? "null" : JSONUtil.toJsonStr(result));
                if (result !=null && result.getData() != null) {
                    hpcRe.setCcpInternelAddress(result.getData().toString());
                }
            }
            //再激活租户及子用户
            log.info("LdapUserServiceImpl.activeCCPUser 激活租户及子用户，资源池：[{}]，资源池类型：[{}],激活的用户名称:[{}]",hpcRe.getBusinessCategory(), hpcRe.getClusterType(), listAccount);
            cn.com.cloudstar.rightcloud.module.support.access.pojo.Org currentOrgInfo = BasicInfoUtil.getCurrentOrgInfo(rootOrg.getOrgSid());
            String adminPassword = hpcPassService.findPassword(currentOrgInfo.getOwner());
            ActiveDirectory.activeCCPUser(listAccount,rootOrg.getOrgSid(),hpcRe,cloudEnvs.get(0), adminPassword);
        }
    }

    @Override
    public Map<Long, List<ResHpcClusterVO>> getAllHPCPermissionInfo(Long orgId, List<Long> targetUserList) {
        // 查询该组织下的所用用户
        Org rootOrg = orgService.selectRootOrg(orgId);
        HashMap<Long, List<ResHpcClusterVO>> result = new HashMap<>(5);
        if (rootOrg == null) {
            return result;
        }
        List<Org> orgList = orgService.lambdaQuery().and(o ->
                o.eq(Org::getOrgSid, rootOrg.getOrgSid())
                        .or()
                        .like(Org::getTreePath,
                                "'%/" + rootOrg.getOrgSid() + "/%'")).list();
        if (CollectionUtils.isEmpty(orgList)) {
            return result;
        }
        List<User> userList;
        if (CollectionUtils.isEmpty(targetUserList)) {
            userList = sysUserService.lambdaQuery()
                                     .in(User::getOrgSid,
                                         orgList.stream().map(Org::getOrgSid).collect(Collectors.toList()))
                                     .select(User::getUserSid, User::getStatus)
                                     .list();
        } else {
            userList = sysUserService.lambdaQuery()
                                     .in(User::getOrgSid,
                                         orgList.stream().map(Org::getOrgSid).collect(Collectors.toList()))
                                     .in(User::getUserSid, targetUserList)
                                     .select(User::getUserSid, User::getStatus)
                                     .list();
        }
        //去掉删除状态的用户
        userList.removeIf(user -> "8".equals(user.getStatus()));
        if (CollectionUtils.isEmpty(userList)) {
            return result;
        }
        // 获取拥有所有数据权限的策略
        userList.forEach(user -> {
            QueryResHpcClusterRequest queryResHpcClusterRequest = new QueryResHpcClusterRequest();
            queryResHpcClusterRequest.setStatusNotEq(ResHpcClusterStatus.FROZEN);
            queryResHpcClusterRequest.setStatusNoInList(HpcConstance.CLUSTER_NO_QUERY_STATUS);
            queryResHpcClusterRequest.setOrgSid(orgId);
            List<ResHpcClusterVO> hpcList = policyAssertionService.findResourceRelationByUser(
                    HpcConstance.TYPE_HPC, user.getUserSid(), rootOrg.getOrgSid(), Function.identity(), queryResHpcClusterRequest);
            result.put(user.getUserSid(), hpcList);
        });
        return result;
    }


    /**
     * 作业检查 某个用户的所有资源池
     * @param differenceB
     * @param userAccount
     * @param ownerId
     */
    @Override
    public void checkHPCJob(Collection<ResHpcClusterVO> differenceB, String userAccount, Long ownerId) {
        String checkJobFlag = PropertiesUtil.getProperty("hpc.checkJob.flag");
        if (!OPEN_TAG.equals(checkJobFlag)) {
            return;
        }
        //未实名认证的不检查作业
        Criteria criteria = new Criteria();
        criteria.put("account",userAccount);
        List<User> users = sysUserService.selectByParams(criteria);
        List<User> authSuccedUserList = users.stream().filter(user -> {
            if(!CertificationStatus.AUTHSUCCEED.equals(user.getCertificationStatus())){
                if(Objects.nonNull(user.getOrgSid())){
                    List<Org> orgList= orgService.selectOrgByUserSidAndType(user.getOrgSid(), OrgType.COMPANY);
                    if(orgList.size()>0){
                        if(!CertificationStatus.AUTHSUCCEED.equals(orgList.get(0).getCertificationStatus())){
                            log.info("{},个人认证与企业认证都未完成,不进行作业检查",userAccount);
                            return false;
                        }
                    }
                }
            }
            return true;
        }).collect(Collectors.toList());
        //用户未认证或企业未认证的用户不做作业检查
        if (CollectionUtils.isEmpty(authSuccedUserList)) {
            return;
        }

        User owner = sysUserService.getById(ownerId);
        CloudEnvParams params = new CloudEnvParams();
        params.setCloudEnvType(CloudEnvType.HCSO.getValue().get(0));
        List<CloudEnv> cloudEnvs = cloudEnvRemoteService.selectByParams(params);
        if (CollectionUtils.isEmpty(cloudEnvs)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1000498884));
        }
        for (ResHpcClusterVO resHpcClusterVO : differenceB) {
            String status = resHpcClusterVO.getStatus();
            if(!isAvailableHpcCluster(status)){
                continue;
            }

            //作业检查
            try {
                Duser duser = CloudClientFactory.buildMQBean(cloudEnvs.get(0).getId(), Duser.class);
                duser.setOptions(MapsKit.of("ROOT_URL", getCcpInternalAddr(
                        resHpcClusterVO.getCcpInternelAddress())));
                duser.setUsername(Lists.newArrayList(userAccount));
                //用租户管理员账户
                if ("SAASShare".equals(resHpcClusterVO.getClusterType())) {
                    // 设置密码
                    duser.setTenantUserName(
                            CrytoUtilSimple.decrypt(resHpcClusterVO.getUsername()));
                    duser.setTenantUserPass(
                            CrytoUtilSimple.decrypt(resHpcClusterVO.getPassword()));
                } else {
                    //查询租户管理员账号密码
                    duser.setTenantUserName(owner.getAccount());
                    String password = hpcPassService.findPassword(owner.getUserSid());
                    duser.setTenantUserPass(password);
                }

                int count = 1;
                while (count < CYCLE_COUNT) {
                    try {
                        log.info("HPC作业检查次数:[{}]", count);
                        DuserResult result = (DuserResult) MQHelper.rpc(duser);
                        for (DuserResult.UserJobInfo userJobInfo : result.getResult()) {
                            boolean b = userJobInfo.getPendingJobsCount() > 0 || userJobInfo.getRunningJobsCount() > 0
                                    || userJobInfo.getSstoppedJobsCount() > 0;
                            if (b || userJobInfo.getStoppedJobsCount() > 0 || userJobInfo.getWaitingJobsCount() > 0) {
                                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1729904842));
                            }
                        }
                        break;
                    } catch (Exception e) {
                        log.error("HPC作业检查异常:[{}]、尝试次数:[{}]", e.getMessage(), count);
                        if (count == 3) {
                            log.info("HPC作业检查北向接口服务异常:[{}]", count);
                            throw e;
                        }
                        Thread.sleep(1000);
                    }
                    count += 1;
                }
            } catch (BizException be) {
                throw be;
            } catch (Exception e) {
                log.error("北向接口服务异常：", e);
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1070996688));
            }
        }
    }

    /**
     * 实际存在可用的HPC资源
     * @param status
     * @return
     */
    private boolean isAvailableHpcCluster(String status) {
        return ResHpcClusterStatus.AVALIABLE.equals(status)
                || ResHpcClusterStatus.FROZEN.equals(status)
                || ResHpcClusterStatus.EXPIRED.equals(status);
    }


    /**
     * 作业检查 某个用户的所有资源池
     * @param differenceB
     * @param orgSid
     * @param ownerId
     */
    @Override
    public void checkHPCJobOu(Collection<ResHpcClusterRemoteModule> differenceB, Long orgSid, Long ownerId) {
        Org org = orgService.selectRootOrg(orgSid);
        String checkJobFlag = PropertiesUtil.getProperty("hpc.checkJob.flag");
        if (!OPEN_TAG.equals(checkJobFlag)) {
            return;
        }
        User owner = sysUserService.getById(ownerId);

        CloudEnvParams params = new CloudEnvParams();
        params.setCloudEnvType(CloudEnvType.HCSO.getValue().get(0));
        List<CloudEnv> cloudEnvs = cloudEnvRemoteService.selectByParams(params);
        if (CollectionUtils.isEmpty(cloudEnvs)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1000498884));
        }
        for (ResHpcClusterRemoteModule resHpcClusterVO : differenceB) {
            String status = resHpcClusterVO.getStatus();
            if(!isAvailableHpcCluster(status)){
                continue;
            }

            try {
                Daccount daccount = CloudClientFactory.buildMQBean(cloudEnvs.get(0).getId(), Daccount.class);
                daccount.setOptions(
                        MapsKit.of("ROOT_URL",
                                   getCcpInternalAddr(resHpcClusterVO.getCcpInternelAddress())));
                daccount.setAccountList(Arrays.asList(org.getLdapOu()));
                daccount.setTenantUserName(owner.getAccount());
                daccount.setTenantUserPass(hpcPassService.findPassword(owner.getUserSid()));
                DaccountResult result = (DaccountResult) MQHelper.rpc(daccount);
                DaccountResult.AccountJobInfo accountJobInfo = result.getResult().get(0);
                if (accountJobInfo.getPendingJobsCount() > 0 || accountJobInfo.getRunningJobsCount() > 0
                        || accountJobInfo.getSstoppedJobsCount() > 0) {
                    throw new cn.com.cloudstar.rightcloud.bss.common.exception.BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2072178987));
                }
            } catch (cn.com.cloudstar.rightcloud.bss.common.exception.BizException be) {
                throw be;
            } catch (Exception e) {
                log.error("北向接口服务异常：", e);
                throw new cn.com.cloudstar.rightcloud.bss.common.exception.BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1070996688));
            }
        }

    }

    /**
     * 插入一个影子用户
     * @param user
     * @param allResource
     * @param baseDN
     * @param rootOrg
     * @return
     */
    public OpenLdapUser creatShadowUser(User user,
                                        List<ResHpcClusterVO> allResource,
                                        String baseDN,
                                        InitialLdapContext ldapContext, Org rootOrg){
        OpenLdapUser shadowUser = new OpenLdapUser();
        //先插入一个影子用户
        try {
            shadowUser.setUserName(user.getAccount() + "_shadow")
                    .setStatus("1")
                 //   .setPassword(SHA512Util.LdapEncoderBySHA512(CrytoUtilSimple.decrypt(user.getHpcPass())))
                    .setOrgNumber(rootOrg.getOrgSid() + "")
                    .setLdapOu(rootOrg.getLdapOu())
                    .setUid(user.getUserSid() + "")
                    .setHpcRes(allResource)
                    .setHomePhone("disabled")
                    .setBaseDN(baseDN);
            shadowUser.setUid(shadowUser.getUid() + "1");
            log.debug("开始插入影子用户,{}", shadowUser.getUserName());

            ArrayList<String> hpcResCategory = Lists.newArrayList();
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(shadowUser.getHpcRes())) {
                for (ResHpcClusterVO hpcRe : shadowUser.getHpcRes()) {
                    //如果有版本1的资源池设置homephone为enable
                    if (Integer.valueOf(1).equals(hpcRe.getHpcVersion()) || Integer.valueOf(2)
                            .equals(hpcRe.getHpcVersion())) {
                        shadowUser.setHomePhone("enable");
                    } else {
                        if (StringUtils.isNotBlank(hpcRe.getBusinessCategory())) {
                            hpcResCategory.add(hpcRe.getBusinessCategory());
                        }
                    }
                }
            }

            ActiveDirectory.createLdapUser(ldapContext, shadowUser, ActiveDirectory.addHpcAttr(hpcResCategory));

            AtomicReference<Integer> count = new AtomicReference<>();
            count.set(1);
            RetryUtil.retry(3, 1L, TimeUnit.SECONDS, () -> {
                NamingEnumeration<SearchResult> namingEnumeration = ActiveDirectory.searchUser(ldapContext, shadowUser);
                if (Objects.isNull(namingEnumeration)) {
                    log.error("插入影子用户，第{}次查询失败", count.get());
                    count.set(count.get() + 1);
                    throw new RetryException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_19977201));
                }else {
                    log.info("插入影子用户，查询成功");
                }
            });
            log.debug("插入影子用户成功,{}", shadowUser.getUserName());
            log.info("插入影子用户成功");
        } catch (Exception e) {
            log.error("插入影子用户失败", e);
        }
        return shadowUser;
    }

    /**
     * LDAP同步
     * @param userList
     * @param rootOrg
     * @param hpcListMap
     * @param baseDN
     * @param orgCertificationStatus
     * @param config
     * @param ldapContext
     * @param request
     * @return
     */
    private List<Boolean> updateHpcResources(List<User> userList, Org rootOrg, Map<String, List<ResHpcClusterVO>> hpcListMap
            , String baseDN, String orgCertificationStatus, Map<String, String> config, InitialLdapContext ldapContext, LdapSyncRequest request){

        //多线程同步ldap user
        ArrayList<Boolean> successFlag = new ArrayList<>();
        ArrayList<FutureTask<Boolean>> futures = new ArrayList<>();
        //是否需要激活用户

        userList.forEach(user -> {
            List<ResHpcClusterVO> finalHpcList = filterAndStoreHpcClusters(rootOrg, hpcListMap, request, user);
            String hpcPass = CrytoUtilSimple.decrypt(user.getHpcPass());
            futures.add((FutureTask<Boolean>) cloudExecutor.submit(() -> {
                try{
                    OpenLdapUser openLdapUser = new OpenLdapUser();
                    openLdapUser.setUserName(user.getAccount())
                            .setStatus(user.getStatus())
                    //        .setPassword(SHA512Util.LdapEncoderBySHA512(hpcPass))
                            .setOrgNumber(rootOrg.getOrgSid() + "")
                            .setLdapOu(rootOrg.getLdapOu())
                            .setUid(user.getUserSid() + "")
                            .setBaseDN(baseDN)
                            // HPC资源
                            .setHpcRes(finalHpcList);
                    //增加企业认证逻辑判断
                    log.info("ladp....Account:[{}]", user.getAccount());
                    if (!CertificationStatus.AUTHSUCCEED.equals(user.getCertificationStatus()) && !CertificationStatus.AUTHSUCCEED.equals(orgCertificationStatus) ) {
                        //禁用/不同步businessCategory
                        log.info("ladp....不同步资源,{},{}",user.getCertificationStatus(),orgCertificationStatus);
                        openLdapUser.setStatus("0");
                    }
                    if (!isEnableLdap(config)) {
                        log.info("ldap 已关闭");
                        return false;
                    }
                    log.info("updateHpcResource ldapUser[{}] status[{}]",openLdapUser.getUserName(),openLdapUser.getStatus());
                    ActiveDirectory.updateHpcResources(ldapContext, openLdapUser);
                    return true;
                }catch (Exception e){
                    log.info("Ldap同步用户[{}]:[{}]异常",user.getUserSid(),user.getAccount());
                    return false;
                }
            }));
        });

        for (FutureTask<Boolean> future : futures){
            try {
                successFlag.add(future.get(10,TimeUnit.SECONDS));
            } catch (TimeoutException | InterruptedException | ExecutionException e) {
                if (e instanceof InterruptedException) {
                    // 线程中断状态更新为未中断
                    Thread.currentThread().interrupt();
                }
                successFlag.add(false);
            }
        }
        log.info("异步执行结果：[{}]，用户及子用户数量：[{}]",successFlag,userList.size());
        return successFlag;
    }


    private List<ResHpcClusterVO> filterAndStoreHpcClusters(Org rootOrg, Map<String, List<ResHpcClusterVO>> hpcListMap, LdapSyncRequest request, User user) {
        QueryResHpcClusterRequest queryResHpcClusterRequest = new QueryResHpcClusterRequest();
        queryResHpcClusterRequest.setStatusInList(HpcConstance.CLUSTER_QUERY_STATUS);
        queryResHpcClusterRequest.setOrgSid(rootOrg.getOrgSid());
        List<ResHpcClusterVO> hpcList = policyAssertionService.findResourceRelationByUser(
                HpcConstance.TYPE_HPC, user.getUserSid(), rootOrg.getOrgSid(), Function.identity(),
                queryResHpcClusterRequest);
        //ldap删除指定的cateGory
        if(Objects.nonNull(request.getRemoveBusinessCategoryList())){
            if(request.getRemoveBusinessCategoryList().size()>0){
                log.info("LdapUserServiceImpl.updateHpcResources HPC专属资源池删除LDAP--updateHpcResources-INPUT:[{}]",JSONUtil.toJsonStr(request));
                hpcList.removeIf(ldap -> request.getRemoveBusinessCategoryList().get(0).equals(ldap.getBusinessCategory()));
            }
        }
        // HPC预审批时，不应该同步其它未审批的HPC共享资源池，HPC专属资源池不过滤
        hpcList = hpcList.stream().filter(resHpcClusterVO -> {
            boolean b = HPCClusterTypeEnum.getHPCPrivateTypes().contains(resHpcClusterVO.getClusterType())
                    || !ResHpcClusterStatus.APPLY.equals(resHpcClusterVO.getStatus());
            if (!CollectionUtils.isEmpty(request.getBusinessCategoryList())) {
                String businessCategory = request.getBusinessCategoryList().get(0);
                return b || businessCategory.equals(resHpcClusterVO.getBusinessCategory());
            }
            return b;
        }).collect(Collectors.toList());
        // 只读用户也会查出hpcList，但不会添加businessCategory
        if (!CLOSE_TAG.equals(user.getStatus())) {
            hpcListMap.put(user.getAccount(),hpcList);
        }
        log.info("LdapUserServiceImpl.updateHpcResources 子用户[{}]的hpc资源池查询结果：[{}]", user.getAccount(), hpcListMap.get(user.getAccount()));
        List<ResHpcClusterVO> finalHpcList = hpcList;
        return finalHpcList;
    }
}
