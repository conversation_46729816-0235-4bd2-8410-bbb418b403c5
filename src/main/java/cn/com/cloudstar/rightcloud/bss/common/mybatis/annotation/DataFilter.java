/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.common.mybatis.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 数据过滤
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DataFilter {

    /**  业务表别名 */
    String tableAlias() default "";

    /**  业务表组织ID */
    String orgId() default "org_sid";

    /**  业务表用户Account */
    String userId() default "created_by";

    /** 忽略仅个人数据权限 */
    boolean ignoreUserScope() default false;

}
