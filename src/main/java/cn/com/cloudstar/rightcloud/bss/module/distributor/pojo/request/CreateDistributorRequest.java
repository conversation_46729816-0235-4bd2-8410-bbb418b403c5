/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.distributor.pojo.request;


import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import cn.com.cloudstar.rightcloud.oss.common.util.annotation.StartWithWord;
import cn.com.cloudstar.rightcloud.validated.safe.SafeHtml;
import cn.com.cloudstar.rightcloud.validated.validation.NotIllegalString;

/**
 * <AUTHOR>
 * @date 2020/8/28.
 */
@ApiModel(description = "创建分销商")
@Data
public class CreateDistributorRequest {

    /**
     * 名称
     */
    @NotBlank
    @ApiModelProperty("名称")
    @StartWithWord(message = "联系人不能已test，admin开头")
    @Pattern(regexp = "^[\\u4e00-\\u9fa5a-zA-Z0-9]{2,64}$", message = "名称长度为2-64个字符,并且不能包含特殊符号")
    @SafeHtml
    private String name;

    /**
     * 联系人
     */
    @ApiModelProperty("联系人")
    @StartWithWord(message = "联系人不能已test，admin开头")
    @Pattern(regexp = "^[\\u4e00-\\u9fa5a-zA-Z0-9]{2,32}$", message = "联系人长度为2-32个字符,并且不能包含特殊符号")
    private String contact;

    /**
     * 电话
     */
    @ApiModelProperty("联系方式")
    @NotBlank
    @Length(max = 11, min = 6)
    @Pattern(regexp = "^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\\d{8}$"
            , message = "手机号码格式不正确")
    private String mobile;

    /**
     * 分销商地址
     */
    @ApiModelProperty("分销商地址")
    @Length(max = 512)
    @SafeHtml
    private String address;

    /**
     * 主营业务描述
     */
    @ApiModelProperty("主营业务描述")
    @Length(max = 512)
    @SafeHtml
    private String businessDesc;

    /**
     * 组织代码
     */
    @ApiModelProperty("组织机构代码")
    @Length(max = 36)
    @Pattern(regexp = "^[0-9A-HJ-NPQRTUWXY]{8}-[0-9A-HJ-NPQRTUWXY]$", message = "组织机构代码不合法！")
    private String orgCode;

    /**
     * 营业执照名称
     */
    @ApiModelProperty("营业执照名称")
    @Length(max = 512)
    @SafeHtml
    private String licenseName;

    /**
     * 营业执照url
     */
    @ApiModelProperty("营业执照url")
    @Length(max = 512)
    private String licenseUrl;

    /**
     * 树路径
     */
    @ApiModelProperty("层级树,如/15/")
    @Length(max = 512)
    private String treePath;

    /**
     * 所属分销商id
     */
    @ApiModelProperty("所属分销商ID")
    private Long parentId;

    /**
     * 所属分销商名称
     */
    @ApiModelProperty("所属分销商名称")
    @Length(max = 512)
    @SafeHtml
    @NotIllegalString
    private String parentName;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @Length(max = 512)
    @SafeHtml
    private String remark;

    /**
     * 最大客户数量
     */
    @NotNull(message = "分销商下属最大用户数量不能为空")
    @Min(value = 1, message = "不能低于最小用户数量限制")
    @Max(value = 21, message = "不能超过最大用户数量限制")
    @ApiModelProperty("最大客户数量")
    private Long customerMaxNum;
}
