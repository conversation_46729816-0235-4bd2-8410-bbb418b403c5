/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.common.pojo;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.Objects;

/**
 * The type BaseRequest. <p>
 *
 * <AUTHOR>
 * @date 2019/7/1
 */
public class BaseRequest {

    /**
     * 每页记录数
     */
    @ApiModelProperty("每页记录数")
    @Min(value = 1,message = "请输入正确记录数")
    @Max(value = 200, message = "最大记录数不能超过200")
    private Long pagesize;

    /**
     * 当前页(从0开始)
     */
    @ApiModelProperty("当前页(从0开始)")
    @Min(value = 0,message = "请输入正确页数")
    private Long pagenum;

    /**
     * 排序字段
     */
    @ApiModelProperty("排序字段")
    private String sortdatafield;

    /**
     * 排序字符串
     */
    @ApiModelProperty("排序字符串")
    private String sortorder;

    public Long getPagesize() {
        return pagesize;
    }

    public void setPagesize(Long pagesize) {
        this.pagesize = pagesize;
    }

    public Long getPagenum() {
        return pagenum;
    }

    /**
     * 前端的pagenum从0开始的
     * @param pagenum
     */
    public void setPagenum(Long pagenum) {
        this.pagenum = Objects.nonNull(pagenum) ? pagenum + 1 : pagenum;
    }

    public String getSortdatafield() {
        return sortdatafield;
    }

    public void setSortdatafield(String sortdatafield) {
        this.sortdatafield = sortdatafield;
    }

    public String getSortorder() {
        return sortorder;
    }

    public void setSortorder(String sortorder) {
        this.sortorder = sortorder;
    }

}
