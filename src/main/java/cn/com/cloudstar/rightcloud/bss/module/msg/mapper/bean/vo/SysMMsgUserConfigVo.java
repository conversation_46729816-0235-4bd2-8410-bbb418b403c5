package cn.com.cloudstar.rightcloud.bss.module.msg.mapper.bean.vo;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户消息配置表;用户消息配置表(SysMMsgUserConfig)实体类
 *
 * <AUTHOR>
 * @since 2022-11-09 10:42:11
 */
@Data
@Builder
public class SysMMsgUserConfigVo implements Serializable {
    private static final long serialVersionUID = 863359081476758046L;
    /**
     * 主键
     */
    private Long id;
    /**
     * 消息类型;财务消息：finance，产品消息：product，账户：account，其他：other
     */
    private String msgType;
    /**
     * 平台分类;租户侧：console，管理侧：platform
     */
    private String platformCategory;
    /**
     * 消息类型名称
     */
    private String msgTypeName;
    /**
     * 消息名称
     */
    private String msgName;
    /**
     * 消息ID;多个以,分隔
     */
    private String msgIds;
    /**
     * 消息描述
     */
    private String msgDescription;
    /**
     * 排序号;当前消息分类下的排序
     */
    private Integer sortNo;
    /**
     * 是否支持短信发送
     */
    private Integer smsSupport;
    /**
     * 短信启用状态
     */
    private Integer smsStatus;
    /**
     * 短信发送方式是否可编辑
     */
    private Integer smsIsEditable;
    /**
     * 是否支持站内信发送
     */
    private Integer msgSupport;
    /**
     * 站内信启用状态
     */
    private Integer msgStatus;
    /**
     * 站内信发送方式是否可编辑
     */
    private Integer msgIsEditable;
    /**
     * 是否支持邮箱发送
     */
    private Integer emailSupport;
    /**
     * 邮箱启用状态
     */
    private Integer emailStatus;
    /**
     * 邮箱发送方式是否可编辑
     */
    private Integer emailIsEditable;
    /**
     * 所有者ID
     */
    private String ownerId;
    /**
     * 组织ID
     */
    private Long orgSid;
    /**
     * 创建者组织ID
     */
    private Long createdOrgSid;
    /**
     * 版本号
     */
    private Long version;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private Date updatedDt;
    /**
     * 消息接收人
     */
    private String contactNames;
}

