/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;

/**
 * The type DetachPortOfVmRequest.
 * <p>
 *
 * <AUTHOR>
 * @date 2019/8/21
 */
@Data
@ApiModel(description = "虚拟网卡与实例解绑")
public class DetachPortOfVmRequest {
    /**
     * id
     */
    @ApiModelProperty(value = "id", name = "id", example = "123")
    @NotNull
    private Long id;

    /**
     * 连接设备
     */
    @ApiModelProperty(value = "连接设备", name = "device")
    @NotBlank
    private String device;
}
