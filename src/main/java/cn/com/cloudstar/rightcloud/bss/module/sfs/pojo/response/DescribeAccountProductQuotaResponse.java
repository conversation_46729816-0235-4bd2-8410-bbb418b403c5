package cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.response;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * 客户产品限额出参
 *
 * @Auther: 张淇囿
 * @Date: 2022/04/25
 */
@ApiModel(description = "客户产品限额出参")
@Data
public class DescribeAccountProductQuotaResponse {

    /**
     * 主键ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 租户ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userSid;

    /**
     * 客户名称
     */
    private String accountName;

    /**
     * 产品ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long serviceId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 最小购买金额
     */
    private BigDecimal minAmount;

    /**
     * 最小冻结金额
     */
    private BigDecimal minFrozenAmount;

    /**
     * 冻结金额类型;冻结金额是否启用；0禁用，1-启用
     */
    private String frozenAmountStatus;

    /**
     * 状态;是否启用。0-禁用，1-启用
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createdDt;

}
