/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * The type DescribeAvailableResourceRequest.
 * <p>
 *
 * <AUTHOR>
 * @date 2019/8/21
 */
@Data
@ApiModel(description = "创建主机模板时，资源查询")
public class AvailableResourceRequest {

    @ApiModelProperty(value = "云环境ID")
    private Long cloudEnvId;

    @ApiModelProperty(value = "云环境类型")
    private String cloudEnvType;

    @ApiModelProperty(value = "分区/数据中心ID")
    private String zone;

    @ApiModelProperty(value = "付费类型")
    private String instanceChargeType;

    @ApiModelProperty(value = "虚拟化类型")
    private String virtualType;

    @ApiModelProperty(value = "cloudos类型")
    private String extraType;

    @ApiModelProperty(value = "资源类型")
    private String type;

    @ApiModelProperty(value = "资源版本")
    private String version;

    @ApiModelProperty(value = "云账号ID")
    private Long envAccountId;

    @ApiModelProperty(value = "限定条数")
    private Long limitCount;
}
