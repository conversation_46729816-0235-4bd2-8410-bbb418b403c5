package cn.com.cloudstar.rightcloud.bss.module.market.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 商品订阅表
 * @TableName market_shop_subscribe
 */
@TableName(value ="market_shop_subscribe")
@Data
public class MarketShopSubscribe implements Serializable {
    /**
     * 订阅id
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String subscribeId;

    /**
     * AiHub关联的订阅id
     */
    private String aiHubSubscribeId;

    /**
     * 商品id
     */
    private String shopId;

    /**
     * AiHub关联的资产id
     */
    private String aiHubContentId;

    /**
     * 订阅人id
     */
    private Long ownerId;

    /**
     * 价格规格id
     */
    private Long priceJoinId;

    /**
     * 价格：免费则为0
     */
    private BigDecimal price;

    /**
     * 规格单位
     */
    private String unit;

    /**
     * 时长：几年或几个月；免费则为0
     */
    private Integer unitValue;

    /**
     * 商品是否免费：0免费，1按月，2按年,3按次
     */
    private Integer sellType;

    /**
     * 商品发布人id
     */
    private Long shopOwnerId;

    /**
     * 订阅开始时间
     */
    private Date startTime;

    /**
     * 订阅结束时间
     */
    private Date endTime;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 订阅状态
     */
    private String superviseStatus;

    /**
     * 资金监管状态
     */
    private String settlementStatus;

    /**
     * 备注
     */
    private String remark;
    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 计费周期id
     */
    private String billingCycleId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        MarketShopSubscribe other = (MarketShopSubscribe) that;
        return (this.getSubscribeId() == null ? other.getSubscribeId() == null : this.getSubscribeId().equals(other.getSubscribeId()))
            && (this.getAiHubSubscribeId() == null ? other.getAiHubSubscribeId() == null : this.getAiHubSubscribeId().equals(other.getAiHubSubscribeId()))
            && (this.getShopId() == null ? other.getShopId() == null : this.getShopId().equals(other.getShopId()))
            && (this.getAiHubContentId() == null ? other.getAiHubContentId() == null : this.getAiHubContentId().equals(other.getAiHubContentId()))
            && (this.getOwnerId() == null ? other.getOwnerId() == null : this.getOwnerId().equals(other.getOwnerId()))
            && (this.getPriceJoinId() == null ? other.getPriceJoinId() == null : this.getPriceJoinId().equals(other.getPriceJoinId()))
            && (this.getPrice() == null ? other.getPrice() == null : this.getPrice().equals(other.getPrice()))
            && (this.getUnit() == null ? other.getUnit() == null : this.getUnit().equals(other.getUnit()))
            && (this.getUnitValue() == null ? other.getUnitValue() == null : this.getUnitValue().equals(other.getUnitValue()))
            && (this.getSellType() == null ? other.getSellType() == null : this.getSellType().equals(other.getSellType()))
            && (this.getShopOwnerId() == null ? other.getShopOwnerId() == null : this.getShopOwnerId().equals(other.getShopOwnerId()))
            && (this.getStartTime() == null ? other.getStartTime() == null : this.getStartTime().equals(other.getStartTime()))
            && (this.getEndTime() == null ? other.getEndTime() == null : this.getEndTime().equals(other.getEndTime()))
            && (this.getCreatedBy() == null ? other.getCreatedBy() == null : this.getCreatedBy().equals(other.getCreatedBy()))
            && (this.getCreatedDt() == null ? other.getCreatedDt() == null : this.getCreatedDt().equals(other.getCreatedDt()))
            && (this.getOrderId() == null ? other.getOrderId() == null : this.getOrderId().equals(other.getOrderId()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getSubscribeId() == null) ? 0 : getSubscribeId().hashCode());
        result = prime * result + ((getAiHubSubscribeId() == null) ? 0 : getAiHubSubscribeId().hashCode());
        result = prime * result + ((getShopId() == null) ? 0 : getShopId().hashCode());
        result = prime * result + ((getAiHubContentId() == null) ? 0 : getAiHubContentId().hashCode());
        result = prime * result + ((getOwnerId() == null) ? 0 : getOwnerId().hashCode());
        result = prime * result + ((getPriceJoinId() == null) ? 0 : getPriceJoinId().hashCode());
        result = prime * result + ((getPrice() == null) ? 0 : getPrice().hashCode());
        result = prime * result + ((getUnit() == null) ? 0 : getUnit().hashCode());
        result = prime * result + ((getUnitValue() == null) ? 0 : getUnitValue().hashCode());
        result = prime * result + ((getSellType() == null) ? 0 : getSellType().hashCode());
        result = prime * result + ((getShopOwnerId() == null) ? 0 : getShopOwnerId().hashCode());
        result = prime * result + ((getStartTime() == null) ? 0 : getStartTime().hashCode());
        result = prime * result + ((getEndTime() == null) ? 0 : getEndTime().hashCode());
        result = prime * result + ((getCreatedBy() == null) ? 0 : getCreatedBy().hashCode());
        result = prime * result + ((getCreatedDt() == null) ? 0 : getCreatedDt().hashCode());
        result = prime * result + ((getOrderId() == null) ? 0 : getOrderId().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", subscribeId=").append(subscribeId);
        sb.append(", aiHubSubscribeId=").append(aiHubSubscribeId);
        sb.append(", shopId=").append(shopId);
        sb.append(", aiHubContentId=").append(aiHubContentId);
        sb.append(", ownerId=").append(ownerId);
        sb.append(", priceJoinId=").append(priceJoinId);
        sb.append(", price=").append(price);
        sb.append(", unit=").append(unit);
        sb.append(", unitValue=").append(unitValue);
        sb.append(", sellType=").append(sellType);
        sb.append(", shopOwnerId=").append(shopOwnerId);
        sb.append(", startTime=").append(startTime);
        sb.append(", endTime=").append(endTime);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdDt=").append(createdDt);
        sb.append(", orderId=").append(orderId);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}
