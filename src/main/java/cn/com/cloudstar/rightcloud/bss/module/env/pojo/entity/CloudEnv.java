/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.env.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName("cloud_env")
@ApiModel(description = "云环境信息对象CloudEnvShare")
public class CloudEnv {
    @ApiModelProperty(value = "平台云环境ID", name = "id", example = "1234")
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "云环境平台的账户ID", name = "cloudEnvAccountId", example = "1234")
    private Long cloudEnvAccountId;

    @ApiModelProperty(value = "云环境平台的账户名称", name = "cloudEnvAccountName", example = "1234")
    @TableField(exist = false)
    private String cloudEnvAccountName;

    @ApiModelProperty(value = "云环境名称", name = "cloudEnvName", example = "aliyun10")
    private String cloudEnvName;

    @ApiModelProperty(value = "云环境类型", name = "cloudEnvType", example = "VMware，OpenStack，Aliyun等")
    private String cloudEnvType;

    @ApiModelProperty(value = "云环境类型名称", name = "cloudEnvTypeName", example = "VMware，OpenStack，Aliyun等")
    private String cloudEnvTypeName;

    private String cloudEnvCategory;

    private String cloudEnvCategoryName;

    @ApiModelProperty(value = "共享企业ID", name = "shareCompanyId", example = "1234")
    private Long shareCompanyId;

    @ApiModelProperty(value = "云环境分区", name = "region", example = "RegionOne")
    private String region;

    @ApiModelProperty(value = "云环境分区名称", name = "regionName", example = "华北1")
    private String regionName;

    @ApiModelProperty(value = "云环境状态", name = "status", example = "normal/inactive/error/disconnect")
    private String status;

    @ApiModelProperty(value = "云环境状态名称", name = "statusName", example = "normal/inactive/error/disconnect")
    private String statusName;

    @ApiModelProperty(value = "创建人", name = "createdBy", example = "")
    private String createdBy;

    @ApiModelProperty(value = "创建时间", name = "createdDt", example = "")
    private Date createdDt;

    @ApiModelProperty(value = "更新人", name = "updatedBy", example = "")
    @JsonIgnore
    private String updatedBy;

    @ApiModelProperty(value = "更新时间", name = "updatedDt", example = "")
    @JsonIgnore
    private Date updatedDt;

    @ApiModelProperty(value = "版本号", name = "version", example = "非传入字段")
    @JsonIgnore
    private Long version;

    @ApiModelProperty(value = "云环境详细信息", name = "attrData")
    private String attrData;

    private String regionDefaultVal;

    @ApiModelProperty(value = "定时同步时间", name = "cycleTime", example = "60")
    private Integer cycleTime;

    @ApiModelProperty(value = "最后一次同步时间", name = "lastSyncTime", example = "2018-06-26 17:30:36")
    private Date lastSyncTime;

    @ApiModelProperty(value = "同步信息", name = "syncInfo", example = "同步环境[esv3-RegionOne] 失败 | No response from RightcloudAdapter.")
    private String syncInfo;

    @ApiModelProperty(value = "同步标识", name = "syncFlg", example = "0或者1")
    private Boolean syncFlg;

    private Boolean ownFlag;

    @ApiModelProperty(value = "所属项目ID", name = "projectId", example = "123")
    private String projectId;

    @ApiModelProperty(value = "配额", name = "quota", example = "")
    private String quota;

    @ApiModelProperty(value = "云环境开关按钮", name = "billingStrategy", notes = "只有私有云有环境开关按钮， 其他云环境默认使用平台计费")
    private String billingStrategy;

    @ApiModelProperty(value = "", name = "stoppedMode", example = "123")
    private String stoppedMode;

    @ApiModelProperty(value = "云环境属性公有云或私有云", name = "cloudEnvAttrCategory")
    private String cloudEnvAttrCategory;

    private Long orgSid;

    @ApiModelProperty(value = "分配到项目的分区ID列表", name = "[1, 2, 3]")
    private List<Long> resPoolIds;

    private List<String> resPoolNames;

    @ApiModelProperty(value = "云环境下所有项目", name = "projectName", example = "123")
    private String projectName;

    @ApiModelProperty(value = "云环境共享方式", name = "mode", example = "share")
    private String mode;

    private List<Object> resourceNumber;

    private Integer instanceNumber;

    private String description;

    private Integer size;

    private BigDecimal accountBalance;

    private String orgName;

    @ApiModelProperty(value = "基础监控数据采集频率", name = "dataCollectFrequency", example = "20")
    private Integer monitorCollectFrequency;

    @ApiModelProperty(value = "基础监控告警采集频率", name = "alarmCollectFrequency", example = "10")
    private Integer alarmCollectFrequency;

    /**
     * 云环境类型分类【公有云： Public, 私有云：Private】
     */
    @JsonIgnore
    private String cloudEnvTypeCategory;

    @ApiModelProperty(value = "关联平台网关组件id", notes = "关联组件表component_id")
    private String platformComponentId;

    private boolean readOnly;

    private String originOrgName;

    private String validateInfo;

    private String subEnvNames;

    @JsonIgnore
    public boolean isPublicCloud() {
        return "Public".equalsIgnoreCase(this.cloudEnvTypeCategory);
    }
}
