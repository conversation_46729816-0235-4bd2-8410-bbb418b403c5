/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.bag.pojo.response;

import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.entity.BizBag;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 描述包和规范反应
 *
 * <AUTHOR>
 * @date 2022/11/14
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DescribeBagAndSpecResponse  extends BizBag {

    /**
     *规格名称 ,中文，数值+固定值，如0.8折
     */
    private String specName;
    /**
     *规格值,当前的规格值，如折扣包0.8，代表0.8折扣
     */
    private Double specValue;
    /**
     *周期
     */
    private Integer period;
    /**
     *价格
     */
    private Double price;

}
