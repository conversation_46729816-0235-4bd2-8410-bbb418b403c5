/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.order.mapper;

import org.apache.ibatis.annotations.Mapper;

import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.entity.BizCouponResource;
import cn.com.cloudstar.rightcloud.oss.common.pojo.Criteria;


/**
 * <AUTHOR>
 */
@Mapper
public interface CouponMapper {

    /**
     * 查询
     *
     * @param criteria
     *
     * @return
     */
    BizCouponResource getCouponResourceByOrderId(Criteria criteria);

    /**
     * 修改状态
     *
     * @param criteria
     */
    void updateCouponStatus(Criteria criteria);

    /**
     * 修改状态
     *
     * @param criteria
     */
    void updateCouponUsed(Criteria criteria);
}
