package cn.com.cloudstar.rightcloud.bss.common.logs;

import cn.com.cloudstar.rightcloud.adapter.pojo.user.TestPassRequest;
import cn.com.cloudstar.rightcloud.bss.common.enums.ProductCodeEnum;
import cn.com.cloudstar.rightcloud.bss.common.exception.BizException;
import cn.com.cloudstar.rightcloud.bss.common.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.bss.common.pojo.Org;
import cn.com.cloudstar.rightcloud.bss.common.pojo.ProductInfoVO;
import cn.com.cloudstar.rightcloud.bss.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.bss.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.Policy;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.Project;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.SysGroup;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.User;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.pojo.UserVO;
import cn.com.cloudstar.rightcloud.bss.module.access.bean.request.*;
import cn.com.cloudstar.rightcloud.bss.module.access.mapper.PolicyMapper;
import cn.com.cloudstar.rightcloud.bss.module.access.mapper.SysGroupMapper;
import cn.com.cloudstar.rightcloud.bss.module.access.service.IPolicyService;
import cn.com.cloudstar.rightcloud.bss.module.access.service.IProjectService;
import cn.com.cloudstar.rightcloud.bss.module.access.service.SysUserService;
import cn.com.cloudstar.rightcloud.bss.module.account.mapper.BizBillingAccountMapper;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.BizBillingAccount;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.request.*;
import cn.com.cloudstar.rightcloud.bss.module.account.service.IBizBillingAccountService;
import cn.com.cloudstar.rightcloud.bss.module.bag.mapper.BizBagMapper;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.entity.BizBag;
import cn.com.cloudstar.rightcloud.bss.module.bag.pojo.status.StatusType;
import cn.com.cloudstar.rightcloud.bss.module.bill.pojo.request.DescribeGaapCostRequest;
import cn.com.cloudstar.rightcloud.bss.module.billing.mapper.BizBillingCustomRegionResourceMapper;
import cn.com.cloudstar.rightcloud.bss.module.billing.mapper.BizBillingTariffSpecChargeMapper;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.entity.*;
import cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.*;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.BizBillingStrategyService;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.IBizBillingRegionResourceService;
import cn.com.cloudstar.rightcloud.bss.module.billing.service.IBizBillingSpecGroupService;
import cn.com.cloudstar.rightcloud.bss.module.code.mapper.CodeMapper;
import cn.com.cloudstar.rightcloud.bss.module.contract.pojo.entity.BizContract;
import cn.com.cloudstar.rightcloud.bss.module.contract.pojo.entity.BizContractTemplate;
import cn.com.cloudstar.rightcloud.bss.module.contract.pojo.request.CreateBizContractRequest;
import cn.com.cloudstar.rightcloud.bss.module.contract.service.BizContractService;
import cn.com.cloudstar.rightcloud.bss.module.contract.service.BizContractTemplateSrvice;
import cn.com.cloudstar.rightcloud.bss.module.coupon.mapper.BizCouponMapper;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.entity.BizCoupon;
import cn.com.cloudstar.rightcloud.bss.module.coupon.pojo.request.DistributeCashCouponRequest;
import cn.com.cloudstar.rightcloud.bss.module.coupon.service.IBizCouponService;
import cn.com.cloudstar.rightcloud.bss.module.discount.pojo.entity.BizDiscount;
import cn.com.cloudstar.rightcloud.bss.module.discount.service.IBizDiscountService;
import cn.com.cloudstar.rightcloud.bss.module.distributor.pojo.entity.BizDistributor;
import cn.com.cloudstar.rightcloud.bss.module.distributor.pojo.entity.BizDistributorProduct;
import cn.com.cloudstar.rightcloud.bss.module.distributor.pojo.request.DeleteDisProductRequest;
import cn.com.cloudstar.rightcloud.bss.module.distributor.service.IBizDistributorService;
import cn.com.cloudstar.rightcloud.bss.module.download.entity.BizDownload;
import cn.com.cloudstar.rightcloud.bss.module.download.mapper.BizDownloadMapper;
import cn.com.cloudstar.rightcloud.bss.module.download.request.BizDownloadRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.mapper.SfProductResourceMapper;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.mapper.WorkTicketMapper;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity.CloudEnvVO;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.entity.SfProductResource;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.pojo.request.MgtCloudEnvRegionRequest;
import cn.com.cloudstar.rightcloud.bss.module.intergrate.service.ResMaPoolService;
import cn.com.cloudstar.rightcloud.bss.module.msg.mapper.SysMMsgReceiveContactMapper;
import cn.com.cloudstar.rightcloud.bss.module.msg.mapper.SysMMsgUserConfigMapper;
import cn.com.cloudstar.rightcloud.bss.module.msg.mapper.bean.SysMMsgReceiveContact;
import cn.com.cloudstar.rightcloud.bss.module.msg.mapper.bean.request.SysMMsgReceiveContactInsertReq;
import cn.com.cloudstar.rightcloud.bss.module.msg.mapper.bean.request.SysMMsgReceiveContactReq;
import cn.com.cloudstar.rightcloud.bss.module.msg.mapper.bean.request.SysMMsgUserConfigUpdateReq;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.ServiceOrder;
import cn.com.cloudstar.rightcloud.bss.module.order.entity.request.ResizeResourceRequest;
import cn.com.cloudstar.rightcloud.bss.module.order.mapper.ServiceOrderMapper;
import cn.com.cloudstar.rightcloud.bss.module.renewal.pojo.request.RenewRequest;
import cn.com.cloudstar.rightcloud.bss.module.sfs.mapper.SfProductCategoryCatalogMapper;
import cn.com.cloudstar.rightcloud.bss.module.sfs.mapper.SfProductTemplateMapper;
import cn.com.cloudstar.rightcloud.bss.module.sfs.mapper.SysMFilePathMapper;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.entity.*;
import cn.com.cloudstar.rightcloud.bss.module.sfs.pojo.request.*;
import cn.com.cloudstar.rightcloud.bss.module.sfs.service.*;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.BizCustomerActionLogMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.OrgMapper;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.UserMapper;
import cn.com.cloudstar.rightcloud.common.constants.status.ServiceCategoryStatus;
import cn.com.cloudstar.rightcloud.common.enums.CommonPropertyKeyEnum;
import cn.com.cloudstar.rightcloud.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.core.pojo.dto.analysis.BillBillingCycleCost;
import cn.com.cloudstar.rightcloud.core.pojo.dto.file.SysMFilePath;
import cn.com.cloudstar.rightcloud.core.pojo.dto.operate.EntityDTO;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.BizCustomerActionLog;
import cn.com.cloudstar.rightcloud.core.pojo.dto.system.Code;
import cn.com.cloudstar.rightcloud.operationlog.dto.OperationLogBaseDto;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;
import cn.com.cloudstar.rightcloud.operationlog.service.OperationLogPostProcessor;
import cn.com.cloudstar.rightcloud.operationlog.util.JacksonUtils;
import cn.com.cloudstar.rightcloud.oss.common.constants.Constants;
import cn.com.cloudstar.rightcloud.oss.common.enums.BusinessTagEnum;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;
import cn.com.cloudstar.rightcloud.oss.common.util.annotation.constant.TypesConstant;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.ma.ResMaPoolVO;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 自定义 日志前后置处理器
 *
 * @author: mwy
 * @date: 2022/11/3 14:55
 */
@Slf4j
@Component
public class CustomOperationLogPostProcessor implements OperationLogPostProcessor {

    public static final String COMMA= "，";
    public static final String RESOURCE_ID= "资源ID：";

    @Autowired
    private IBizCouponService bizCouponService;
    @Autowired
    private BizCouponMapper bizCouponMapper;
    @Autowired
    private BizBillingAccountMapper bizBillingAccountMapper;
    @Autowired
    private ServiceCategoryService serviceCategoryService;
    @Autowired
    private ISfProductCategoryCatalogService productCategoryService;
    @Autowired
    private ISfProductTemplateService iSfProductTemplateService;
    @Autowired
    private BizBillingStrategyService bizBillingStrategyService;
    @Autowired
    private CodeMapper codeMapper;
    @Autowired
    private IBizBillingRegionResourceService bizBillingRegionResourceService;
    @Autowired
    private BizContractService bizContractService;
    @Autowired
    private BizContractTemplateSrvice bizContractTemplateSrvice;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private IBizDiscountService bizDiscountService;
    @Autowired
    private IBizBillingSpecGroupService iBizBillingSpecGroupService;
    @Autowired
    private IBizBillingAccountService bizBillingAccountService;
    @Autowired
    private IProjectService projectService;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private SysGroupMapper sysGroupMapper;
    @Autowired
    private IPolicyService policyService;
    @Autowired
    private WorkTicketMapper workTicketMapper;

    @Autowired
    private SysMFilePathMapper sysMFilePathMapper;

    @Autowired
    private SfProductCategoryCatalogMapper sfProductCategoryCatalogMapper;

    @Autowired
    private IBizDistributorService bizDistributorService;

    @Autowired
    private BizCustomerActionLogMapper bizCustomerActionLogMapper;

    @Autowired
    private PolicyMapper policyMapper;

    @Autowired
    private BizDownloadMapper bizDownloadMapper;

    @Autowired
    private SysMMsgUserConfigMapper sysMMsgUserConfigMapper;

    @Autowired
    private SysMMsgReceiveContactMapper sysMMsgReceiveContactMapper;

    @Autowired
    private BizProductQuotaWhiteListService bizProductQuotaWhiteListService;

    @Autowired
    private BizProductQuotaService bizProductQuotaService;

    @Autowired
    private BizAccountProductQuotaService bizAccountProductQuotaService;

    @Autowired
    private SfProductTemplateMapper sfProductTemplateMapper;

    @Resource
    private BizBillingTariffSpecChargeMapper specChargeMapper;

    @Autowired
    private SfProductResourceMapper sfProductResourceMapper;

    @Autowired
    private ServiceOrderMapper serviceOrderMapper;

    @Autowired
    private OrgMapper orgMapper;

    @Autowired
    private BizBagMapper bizBagMapper;
    @Autowired
    private BizBillingCustomRegionResourceMapper bizBillingCustomRegionResourceMapper;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private ResMaPoolService resMaPoolService;


    @Override
    public OperationLogBaseDto beforeProcessor(JoinPoint point,OperationLogBaseDto baseDto) {
        try {
            switch (baseDto.getResource()){
                case COUPON:
                    operationLogForDistributeCoupon(baseDto);
                    break;
                case BALANCECASH:
                    operationLogForDistributeCoupon(baseDto);
                    break;
                case PRODUCT_CATALOG_LISTING_AND_DELISTING:
                    operationLogForUpdateProductStatus(baseDto);
                    break;
                case DELETE_PRODUCT_CATEGORY:
                    operationLogForDeleteCategory(baseDto);
                    break;
                case DISABLE_OR_EDIT_PRODUCT_TEMPLATES:
                    operationLogForSwitchTemplateStatus(baseDto);
                    break;
                case DELETE_PRODUCT_TEMPLATES:
                    operationLogFordeleteCategory(baseDto);
                    break;
                case ENABLE_AND_DISABLE_BILLING_POLICY:
                    operationLogForUpdateBillingStrategyStatus(baseDto);
                    break;
                case DELETE_BILLING_POLICY:
                    operationLogForDeleteBillingStrategy(baseDto);
                    break;
                case ADD_RESOURCE_TYPE:
                    operationLogForCreateResourceRegion(baseDto);
                    break;
                case CONFIGURE_RESOURCE_TYPE:
                    operationLogForBillingStrategyAccount(baseDto);
                    break;
                case DELETE_RESOURCE_TYPE:
                    operationLogForRemoveResourceRegion(baseDto);
                    break;
                case DETAIL_CONTRACT:
                    operationLogForDetailContract(baseDto);
                    break;
                case VIEW_CONTRACT:
                    operationLogForDetailContract(baseDto);
                    break;
                case ENDCONTRACT:
                    operationLogForDetailContract1(baseDto);
                    break;
                case UPDATE_DISCOUNT:
                    operationLogForDetailContract2(baseDto);
                    break;
                case AUDIT_CONTRACT:
                    operationLogForDetailContract(baseDto);
                    break;
                case MANAGEMENT_CONTRACT:
                    operationLogForDetailContract(baseDto);
                    break;
                case MANAGEMENT_UPLOAD_CONTRACT:
                    operationLogForDetailContract(baseDto);
                    break;
                case DOWNLOAD_CONTRACT:
                    operationLogForDetailContract(baseDto);
                    break;
                case DELETE_CONTRACT:
                    downloadContract(baseDto);
                    break;
                case DELCONTRACTTEMP:
                    operationLogForDeleteResRdsAccount(baseDto);
                    break;
                case DOWNLOAD_CONTRACT_TEMPLATE:
                    operationLogForDeleteResRdsAccount(baseDto);
                    break;
                case UPDATE_DISTRIBUTOR_USER:
                    operationLogForUpdateDistributorUser(baseDto);
                    break;
                case DELETE_DISTRIBUTOR_USER:
                    operationLogForDeleteDistributorUser(baseDto);
                    break;
                case UPDATE_DISTRIBUTOR_USER_ROLE:
                    operationLogForUpdateDistributorUserRole(baseDto);
                    break;
                case DISTRIBUTOR:
                    operationLogForReCharge1(baseDto);
                    break;
                case DELDISCOUNT:
                    operationLogForDeleteDiscount(baseDto);
                    break;
                case START_DISABLE_DISCOUNT:
                    operationLogForDeleteDiscount(baseDto);
                    break;
                case REMARK:
                    operationLogForRemark(baseDto);
                    break;
                case RELATED_PRODUCT_TEMPLATE:
                    operationLogForRelatedProductTemplate(baseDto);
                    break;
                case DELETE_PRODUCT:
                    operationLogForDeleteProduct(baseDto);
                    break;
                case UPDATE_TARIFF_SPEC_CHARGE:
                    operationLogForUpdateBillingTariffSpecCharge(baseDto);
                    break;
                case MODIFY_USER_IN_GROUP:
                    operationLogForModifyUserInGroup(baseDto);
                    break;
                case DELETE_USER_GROUP:
                    operationLogForDeleteUserGroup(baseDto);
                    break;
                case JOIN_GROUP:
                    operationLogForModifyUserInGroup(baseDto);
                    break;
                case DELETE_SUB_USER:
                    operationLogForDeleteSubUser(baseDto);
                    break;
                case CLONE_SPEC_GROUP:
                    operationLogForCloneSpecGroup(baseDto);
                    break;
                case DELETE_SPEC_GROUP:
                    operationLogForDeleteSpecGroup(baseDto);
                    break;
                case UPDATE_USER_DISCOUNT:
                    operationLogForUpdateUserDiscount(baseDto);
                    break;
                case DELETE_PROJECT:
                    operationLogForDeleteProject(baseDto);
                    break;
                case CONFIG_POLICY_USER:
                    operationLogForConfigPolicyUser(baseDto);
                    break;
                case CONFIG_POLICY_GROUP:
                    operationLogForConfigPolicyGroup(baseDto);
                    break;
                case CONFIG_POLICY:
                    operationLogForConfigPolicy(baseDto);
                    break;
                case DELETE_POLICY:
                    operationLogForDeletePolicy(baseDto);
                    break;
                case CONFIG_POLICY_ASSERTION:
                    operationLogForDeletePolicy(baseDto);
                    break;
                case UPDATE_RELATION_RESOURCE:
                    operationLogForDeletePolicy(baseDto);
                    break;
                case USER_UNLOCK_STATUS:
                    operationLogForUserUnlockStatus(baseDto);
                    break;
                case REMOVE_POLICY_FOR_GROUP:
                    operationLogForRemovePolicyForGroup(baseDto);
                    break;
                case UPDATE_DEPOSIT:
                    operationLogForUpdateDeposit(baseDto);
                    break;
                case REMOVE_USER_PERMISSIONS:
                    operationLogForRemovePolicyUser(baseDto);
                    break;
                case REMOVE_SUBUSER_FROM_GROUP:
                    operationLogForRemoveSubuserFromGroup(baseDto);
                    break;
                case MODIFY_SUB_USER:
                    operationLogForModifySubuser(baseDto);
                    break;
                case ADDUSER:
                    operationLogForAddUsers(baseDto);
                    break;
                case ADDUSERS:
                    operationLogForAddUsers(baseDto);
                    break;
                case MGT_CLOUD_ENV:
                    operationLogForMgtEnvOfDb(baseDto);
                    break;
                case EDIT_BILLING_MODEL:
                    operationLogForBillingPublicAccountConfig(baseDto);
                    break;
                case CREATE_INVOICE:
                    operationLogForcreateInvoice(baseDto);
                    break;
                case DOWNLOAD_JOB_LIST:
                    operationLogForDownloadJobList(point, baseDto);
                    break;
                case RENEW_RESOURCE:
                    operationLogForRenewResource(point,baseDto);
                    break;
                case INQUIRY_PRICE:
                    operationLogForInquiryPrice(point,baseDto);
                    break;
                case RESET_USER_PASSWORD:
                    operationLogForResetUserPassword(baseDto);
                    break;
                case PRODUCT_BILLING_CONFIGURATION_PRICING:
                    operationLogProductBillingConfigurationPricing(baseDto);
                    break;
                case REMOVE_PROJECT_USER:
                    operationLogForRomoveProjectUser(baseDto);
                    break;
                case CONFIG_PROJECT_USER:
                    operationLogForConfigProjectUser(baseDto);
                    break;
                case PICTURE_DISPLAY:
                    operationLogForDownloadFile(baseDto);
                    break;
                case RESIZE_RESOURCE:
                    perationLogChangeDesc(point,baseDto);
                    break;
                case OPENSFS:
                    operationLogForOpenSFS(baseDto);
                case PRODUCTS_QUOTA_OPERATE:
                case PRODUCTS_QUOTA_ACCOUNT_OPERATE:
                    productsQuotaOperate(baseDto, point);
                    break;
                case DELETE_DISTRIBUTOR:
                    deleteDistributor(baseDto, point);
                    break;
                case UPDATE_MSG:
                    updateMsg(baseDto, point);
                    break;
                case UPDATE_MSG_CONTACT:
                    updateMsgContact(baseDto, point);
                    break;
                case UNSUBSCRIBE:
                    unsubscribe(baseDto, point);
                    break;
                case TEST_CONNECT_CCP:
                    testConnectCcp(baseDto, point);
                    break;
                case DELETE_DISTRIBUTOR_PRODUCT:
                    deleteDistributorProduct(baseDto);
                    break;
                case CLOSE_TICKET:
                case TICKET_RECORD:
                    closeTicket(baseDto);
                    break;
                case BUSINESS_TAG:
                    operationLogForUpdateBusinessTag(baseDto);
                    break;
                case ADD_DISTRIBUTOR_PRODUCT:
                    operationLogForAddDistributorProduct(baseDto);
                    break;
                case ASYN_BIZ_BAG_BILL_DETAILS_EXPORT:
                    operationLogForAddAsynBizBagBillDetailsExport(baseDto);
                    break;
                case UPDATE_RESOURCE_STATUS:
                    operationLogForUpdateResourceStatus(baseDto);
                default:
                    break;
            }
        } catch (Exception e) {
            log.error("OperationLogAspect_beforeProcessor_error_【{}】", JSON.toJSONString(e.getMessage()));
        }
        return baseDto;
    }

    /**
     * 关闭工单
     *
     * @param baseDto baseDto
     */
    private void closeTicket(OperationLogBaseDto baseDto) {
        baseDto.setTagName(workTicketMapper.selectTicketTitleById(baseDto.getBizId()));
    }

    private void testConnectCcp(OperationLogBaseDto baseDto, JoinPoint point) {
        TestPassRequest request = BeanConvertUtil.convert(point.getArgs()[0], TestPassRequest.class);
        if (StringUtils.isNotBlank(request.getConfigKey()) && request.getConfigKey().equals(CommonPropertyKeyEnum.BIGSCREEN_INTERFACE.getCode())) {
            baseDto.setResource(OperationResourceEnum.UPDATE_PLATFORM_CONFIGS);
            baseDto.setTagName("ModelArts接口密码调用测试");
        }
    }

    /**
     * 删除不计费产品
     *
     * @param baseDto baseDto
     */
    private void deleteDistributorProduct(OperationLogBaseDto baseDto) {
        StringBuilder sb = new StringBuilder();
        DeleteDisProductRequest deleteDisProductRequest = BeanConvertUtil.convert(baseDto.getParam(), DeleteDisProductRequest.class);
        if (Objects.nonNull(deleteDisProductRequest)) {
            if(CollectionUtil.isNotEmpty(deleteDisProductRequest.getIds())){
                for(int i = 0;i < deleteDisProductRequest.getIds().size(); i++){
                    if(sb.length() == 0){
                        sb.append(deleteDisProductRequest.getIds().get(i));
                    }else{
                        sb.append(","+deleteDisProductRequest.getIds().get(i));
                    }
                }

            }
            baseDto.setBizId(sb.toString());
        }
    }

    /**
     * 删除合同文件
     *
     * @param baseDto baseDto
     */
    private void downloadContract(OperationLogBaseDto baseDto) {
        SysMFilePath sysMFilePath = sysMFilePathMapper.selectByPrimaryKey(baseDto.getBizId());
        if (Objects.nonNull(sysMFilePath)) {
            BizContract bizContract = bizContractService.getById(sysMFilePath.getOperationId());
            Optional.ofNullable(bizContract).ifPresent(i -> {
                baseDto.setBizId(bizContract.getContractId().toString());
                baseDto.setTagName(bizContract.getContractTitle());
            });
        }
    }

    /**
     * 退订
     *
     * @param baseDto baseDto
     * @param point point
     */
    private void unsubscribe(OperationLogBaseDto baseDto, JoinPoint point) {
        if (point.getArgs().length > 3 && Objects.nonNull(point.getArgs()[3])) {
            String type = BeanConvertUtil.convert(point.getArgs()[3], String.class);
            String keyFromDesc = ProductCodeEnum.keyFromDesc(type);
            if (StringUtils.isNotBlank(keyFromDesc)) {
                baseDto.setTagName(keyFromDesc + baseDto.getTagName());
            }
        }
    }

    /**
     * 修改消息接收人
     * @param baseDto baseDto
     * @param point point
     */
    private void updateMsgContact(OperationLogBaseDto baseDto, JoinPoint point) {
        SysMMsgReceiveContactReq request = BeanConvertUtil.convert(point.getArgs()[0],
                                                                   SysMMsgReceiveContactReq.class);
        if (Objects.nonNull(request)) {
            String status = "";
            List<Long> userSidList = request.getInsert()
                                            .stream()
                                            .map(SysMMsgReceiveContactInsertReq::getUserSid)
                                            .collect(Collectors.toList());
            if (request.getConfigIds().size() > 1) {
                status = "批量修改接收人：";
            } else {
                List<SysMMsgReceiveContact> sysMMsgReceiveContacts = sysMMsgReceiveContactMapper.queryByConfigId(
                        request.getConfigIds().get(0));
                List<Long> receives = sysMMsgReceiveContacts.stream()
                                                            .map(SysMMsgReceiveContact::getUserSid)
                                                            .collect(Collectors.toList());
                if (request.getInsert().size() != sysMMsgReceiveContacts.size()) {
                    status = request.getInsert().size() > sysMMsgReceiveContacts.size() ? "添加接收人：" : "删除接收人：";
                    if (request.getInsert().size() > sysMMsgReceiveContacts.size()) {
                        userSidList.removeAll(receives);
                    } else {
                        receives.removeAll(userSidList);
                        userSidList = receives;
                    }
                } else {
                    status = "修改接收人：";
                }
            }
            String userSid = StringUtils.join(userSidList, StrUtil.COMMA);
            String id = StringUtils.join(request.getConfigIds(), StrUtil.COMMA);
            baseDto.setTagName(status + userSid + COMMA + RESOURCE_ID + id);
        }
    }

    /**
     * 修改消息配置
     * @param baseDto baseDto
     * @param point point
     */
    private void updateMsg(OperationLogBaseDto baseDto, JoinPoint point) {
        SysMMsgUserConfigUpdateReq request = BeanConvertUtil.convert(point.getArgs()[0], SysMMsgUserConfigUpdateReq.class);
        if (Objects.nonNull(request)) {
            if (request.getStatus() != Constants.ONE_INT && request.getStatus() != Constants.ZERO_INT) {
                baseDto.setTagName("违规操作类型:" + request.getStatus());
                return;
            }
            String status = Constants.ONE_INT == request.getStatus() ? "启用" : "禁用";
            String type = Constants.SMS.equals(request.getType()) ? "短信" : "邮件";
            baseDto.setTagName(status + type + COMMA + RESOURCE_ID + StringUtils.join(request.getIds(), ","));
        }
    }

    /**
     * 删除分销商
     * @param baseDto baseDto
     */
    private void deleteDistributor(OperationLogBaseDto baseDto, JoinPoint point) {
        Long id = (Long) point.getArgs()[0];
        BizDistributor bizDistributor = bizDistributorService.getById(id);
        baseDto.setTagName(bizDistributor.getName());
    }

    /**
     * 启用/禁用、客户/产品限额
     * @param baseDto baseDto
     * @param point point
     */
    private void productsQuotaOperate(OperationLogBaseDto baseDto, JoinPoint point) {
        List<UpdateOperateProductQuotaRequest> requestList = (List<UpdateOperateProductQuotaRequest>) point.getArgs()[0];
        StringBuilder tagName = new StringBuilder(baseDto.getTagName() + "，资源ID：");
        boolean status = false;
        ArrayList<Long> ids = new ArrayList<>();
        for (UpdateOperateProductQuotaRequest request : requestList) {
            ids.add(request.getId());
            if ("1".equals(request.getStatus())) {
                status = true;
            }
        }
        tagName.append(StringUtils.join(ids, StrUtil.COMMA));
        baseDto.setTagName(tagName.toString());
        if (baseDto.getResource().equals(OperationResourceEnum.PRODUCTS_QUOTA_ACCOUNT_OPERATE)) {
            baseDto.setResource(status ? OperationResourceEnum.ENABLE_PRODUCTS_QUOTA_ACCOUNT_OPERATE
                                        : OperationResourceEnum.DISABLE_PRODUCTS_QUOTA_ACCOUNT_OPERATE);
        } else {
            baseDto.setResource(status ? OperationResourceEnum.ENABLE_PRODUCTS_QUOTA_OPERATE
                                        : OperationResourceEnum.DISABLE_PRODUCTS_QUOTA_OPERATE);
        }
    }

    /**
     * 收支明细导出
     * @param point
     * @param baseDto
     * @param exportRevenueExpenseDetails
     */
    private void operationLogForEXPORTList(JoinPoint point, OperationLogBaseDto baseDto,
                                           OperationResourceEnum exportRevenueExpenseDetails) {
        List<BizDownload> bizDownloads = bizDownloadMapper.listDownLoad(baseDto.getOrgSid());
        String fileName="";
        if(bizDownloads.size()>0){
            fileName=bizDownloads.get(0).getDownloadNum();
        }
        baseDto.setResource(exportRevenueExpenseDetails);
        baseDto.setTagName(exportRevenueExpenseDetails.getDesc()+"，资源ID："+fileName);
    }


    @Override
    public OperationLogBaseDto afterReturning(JoinPoint point,OperationLogBaseDto baseDto,String methodResult) {
        try {
            switch (baseDto.getResource()){
                case CREATECOUPON:
                    convertResultMsg(methodResult,baseDto, "");
                    break;
                case CREATE_DISTRIBUTOR_USER:
                    convertResultMsg(methodResult,baseDto, "");
                    break;
                case CREATE_DISTRIBUTOR:
                    convertResultMsg(methodResult,baseDto, "");
                    break;
                case ADD_SUB_USER:
                    operationLogForCreateSubUsers(methodResult,baseDto);
                    break;
                case ADDUSERS:
                    convertResultMsg(methodResult,baseDto, "");
                    break;
                case CREATE_A_BILLING_POLICY:
                    convertResultMsg(methodResult,baseDto, "");
                    break;
                case CREATE_PRODUCT_TEMPLATES:
                    convertResultMsg(methodResult,baseDto, "createProductTemplates");
                    break;
                case CONTRACTTEMP:
                    convertResultMsg(methodResult,baseDto, "");
                    break;
                case CONTRACT:
                    convertResultMsg(methodResult,baseDto, "contract");
                    break;
                case DISCOUNT:
                    convertResultMsg(methodResult, baseDto, "");
                    break;
                case CREAT_PRODUCT:
                    convertResultMsg(methodResult,baseDto, "");
                    break;
                case CREATE_SPEC_GROUP:
                    convertResultMsg(methodResult,baseDto, "");
                    break;
                case CREATE_TARIFF_SPEC_CHARGE:
                    convertResultMsg(methodResult, baseDto, "createTariffSpecCharge");
                    break;
                case BATCH_CREATE_TARIFF_SPEC_CHARGE:
                    convertResultMsg(methodResult, baseDto, "batchCreateTariffSpecCharge");
                    break;
                case CLONE_SPEC_GROUP:
                    convertResultMsg(methodResult,baseDto, "");
                    break;
                case CREATE_PROJECT:
                    convertResultMsg(methodResult,baseDto, "");
                    break;
                case CREATE_POLICY:
                    convertResultMsg(methodResult,baseDto, "");
                    break;
                case CREATE_USER_GROUP:
                    convertResultMsg(methodResult,baseDto, "");
                    break;
                case CREATE_TICKET:
                    convertResultMsg(methodResult,baseDto, "");
                    break;
                case CREATE_PRODUCT_CATEGORY:
                    operationLogCreateProductCategory(methodResult,baseDto);
                    break;
                case EXPORT_ORDER_LIST:
                    operationLogForEXPORTList(point, baseDto,OperationResourceEnum.EXPORT_ORDER_LIST);
                    break;
                case BILLING_CYCLE_EXPORT:
                    operationLogForEXPORTList(point, baseDto,OperationResourceEnum.BILLING_CYCLE_EXPORT);
                    break;
                case BILLING_DETAILS_EXPORT:
                    operationLogForEXPORTList(point, baseDto,OperationResourceEnum.BILLING_DETAILS_EXPORT);
                    break;
                case BILL_ASYNC_EXPORT:
                    operationLogForEXPORTList(point, baseDto,OperationResourceEnum.BILL_ASYNC_EXPORT);
                    break;
                case EXPORT_REVENUE_EXPENSE_DETAILS:
                    operationLogForEXPORTList(point, baseDto,OperationResourceEnum.EXPORT_REVENUE_EXPENSE_DETAILS);
                    break;
                case PRODUCTS_QUOTA_WHITELIST_ADD:
                    productsQuotaWhiteListAdd(methodResult, baseDto, point);
                    break;
                case PRODUCTS_QUOTA_ADD:
                    productsQuotaAdd(methodResult, baseDto, point);
                    break;
                case PRODUCTS_QUOTA_ACCOUNT_ADD:
                    productsQuotaAccountAdd(methodResult, baseDto, point);
                    break;
                case OPENSFS:
                    applyProduct(baseDto, point);
                    break;
                case ADD_RESOURCE_TYPE:
                    addResourceType(baseDto);
                    break;
                case CREATE_INVOICE:
                    operationLogAfterCreateInvoice(baseDto);
                    break;
                case CLEARANCE:
                    operationLogAfterClearance(baseDto);
                    break;
                default:
                    break;

            }
        } catch (Exception e) {
            log.error("OperationLogAspect_afterReturning_error_【{}】", JSON.toJSONString(e.getMessage()));
        }
        return baseDto;
    }

    /**
     * 账户金额清理日志处理
     * @param baseDto
     */
    private void operationLogAfterClearance(OperationLogBaseDto baseDto) {
        AmountClearanceRequest param =  JacksonUtils.fromJson(baseDto.getParam(), AmountClearanceRequest.class);
        List<String> clearanceTypes  = param.getClearanceTypes();
        List<Code> codes = codeMapper.selectByCodeCategory("CLEARANCE_TYPE");
        StringBuilder sb = new StringBuilder();
        if(CollectionUtil.isNotEmpty(codes) && CollectionUtil.isNotEmpty(clearanceTypes)){
           for(int i=0;i< codes.size();i++){
               if(clearanceTypes.contains(codes.get(i).getCodeValue())){
                   if(sb.length() != 0){
                       sb.append(",");
                   }
                   sb.append(codes.get(i).getCodeDisplay());
               }

           }
        }
        baseDto.setTagName(baseDto.getTagName()+sb);
    }

    /**
     * 申请开票后
     * @param baseDto
     */
    private void operationLogAfterCreateInvoice(OperationLogBaseDto baseDto) {
        String bizId = baseDto.getBizId();
        if (StringUtils.isNotEmpty(bizId)) {
            JSONArray objects = JSONUtil.parseArray(bizId);
            Object o = objects.get(0);
            Query query = new Query(org.springframework.data.mongodb.core.query.Criteria.where("id").is(o.toString()));
            List<BillBillingCycleCost> cycleCosts = mongoTemplate.find(query, BillBillingCycleCost.class,"biz_bill_billing_cycle");
            BillBillingCycleCost first = CollectionUtil.getFirst(cycleCosts);
            if (first != null && first.getInvoiceId() != null) {
                baseDto.setBizId(first.getInvoiceId().toString());
            }
        }
    }

    /**
     * 添加资源类型
     *
     * @param baseDto baseDto
     */
    private void addResourceType(OperationLogBaseDto baseDto) {
        CreateRegionStrategyRequest request = JacksonUtils.fromJson(baseDto.getParam(),
                                                                    CreateRegionStrategyRequest.class);
        QueryWrapper<BizBillingRegionResource> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BizBillingRegionResource::getEnvAccountId, request.getEnvAccountId())
                    .eq(BizBillingRegionResource::getRegion, request.getRegion())
                    .in(BizBillingRegionResource::getResourceType, request.getResourceType());
        List<BizBillingRegionResource> list = bizBillingRegionResourceService.list(queryWrapper);
        baseDto.setBizId(StrUtil.join(StrUtil.COMMA, list.stream().map(BizBillingRegionResource::getId).collect(
                Collectors.toList())));
        baseDto.setTagName("用户ID：" + request.getUserSid() + ";" + baseDto.getTagName());
    }

    /**
     * 申请服务
     *
     * @param baseDto baseDto
     * @param point point
     */
    private void applyProduct(OperationLogBaseDto baseDto, JoinPoint point) {
        ApplyServiceRequest request = BeanConvertUtil.convert(point.getArgs()[0], ApplyServiceRequest.class);
        SfProductResource sfProductResource = sfProductResourceMapper.selectOne(
                new QueryWrapper<SfProductResource>().eq("org_sid", request.getProjectId())
                                                     .orderByDesc("id").last("limit 1"));
        if (Objects.isNull(sfProductResource)) {
            sfProductResource = sfProductResourceMapper.selectOne(
                    new QueryWrapper<SfProductResource>().orderByDesc("id").last("limit 1"));
        }
        if (Objects.nonNull(sfProductResource) && StringUtils.isEmpty(baseDto.getTagName())) {
            baseDto.setTagName(sfProductResource.getProductName());
            baseDto.setBizId(sfProductResource.getId().toString());
        }
    }

    /**
     * 添加客户限额
     * @param methodResult methodResult
     * @param baseDto baseDto
     * @param point point
     */
    private void productsQuotaAccountAdd(String methodResult, OperationLogBaseDto baseDto, JoinPoint point) {
        if (StringUtil.isNotEmpty(methodResult)) {
            RestResult restResult = JacksonUtils.fromJson(methodResult, RestResult.class);
            if (restResult.getStatus()) {
                CreateBizAccountProductQuotaRequest request = BeanConvertUtil.convert(point.getArgs()[0],
                                                                                      CreateBizAccountProductQuotaRequest.class);
                QueryWrapper<BizAccountProductQuota> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("service_id", request.getServiceId());
                queryWrapper.in("user_sid", request.getUserSidList());
                queryWrapper.in("entity_id", RequestContextUtil.getEntityId());
                List<BizAccountProductQuota> bizAccountProductQuotas = bizAccountProductQuotaService.list(queryWrapper);
                List<Long> ids = bizAccountProductQuotas.stream()
                                                        .map(BizAccountProductQuota::getId)
                                                        .collect(Collectors.toList());
                if (ids.size() > 0) {
                    baseDto.setTagName(baseDto.getTagName() + COMMA + RESOURCE_ID + StringUtils.join(ids, StrUtil.COMMA));
                }
            }
        }
    }

    /**
     * 添加产品限额
     * @param methodResult methodResult
     * @param baseDto baseDto
     * @param point point
     */
    private void productsQuotaAdd(String methodResult, OperationLogBaseDto baseDto, JoinPoint point) {
        if (StringUtil.isNotEmpty(methodResult)) {
            RestResult restResult = JacksonUtils.fromJson(methodResult, RestResult.class);
            if (restResult.getStatus()) {
                CreateBizProductQuotaRequest request = BeanConvertUtil.convert(point.getArgs()[0],
                                                                               CreateBizProductQuotaRequest.class);
                QueryWrapper<BizProductQuota> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("service_id", request.getServiceId());
                queryWrapper.orderByDesc("id");
                BizProductQuota bizProductQuota = bizProductQuotaService.getOne(queryWrapper);
                baseDto.setTagName(baseDto.getTagName() + COMMA + RESOURCE_ID + bizProductQuota.getId());
            }
        }
    }

    /**
     * 添加平台限额白名单
     * @param methodResult methodResult
     * @param baseDto baseDto
     * @param point point
     */
    private void productsQuotaWhiteListAdd(String methodResult, OperationLogBaseDto baseDto, JoinPoint point) {
        if (StringUtil.isNotEmpty(methodResult)) {
            RestResult restResult = JacksonUtils.fromJson(methodResult, RestResult.class);
            if (restResult.getStatus()) {
                IdsRequest request = BeanConvertUtil.convert(point.getArgs()[0], IdsRequest.class);
                List<BizProductQuotaWhiteList> bizProductQuotaWhiteLists = bizProductQuotaWhiteListService.list(
                        new QueryWrapper<BizProductQuotaWhiteList>().lambda()
                                                                    .in(BizProductQuotaWhiteList::getUserSid,
                                                                        request.getIds())
                                                                    .eq(BizProductQuotaWhiteList::getEntityId,
                                                                        RequestContextUtil.getEntityId()));
                List<Long> ids = bizProductQuotaWhiteLists.stream()
                                                          .map(BizProductQuotaWhiteList::getId)
                                                          .collect(Collectors.toList());
                baseDto.setTagName(baseDto.getTagName() + COMMA + RESOURCE_ID + StringUtils.join(ids, StrUtil.COMMA));
            }
        }
    }

    /**
     * 接口返回转换
     *
     * @param methodResult
     * @param baseDto
     * @param flag
     */
    private void convertResultMsg(String methodResult,OperationLogBaseDto baseDto,String flag){
        if(StringUtil.isNotEmpty(methodResult)){
            RestResult restResult = JacksonUtils.fromJson(methodResult, RestResult.class);
            Optional.ofNullable(restResult.getData()).ifPresent(bizId ->baseDto.setBizId(bizId.toString()));
        }
        switch (flag){
            case "contract":
                CreateBizContractRequest createBizContractRequest = JacksonUtils.fromJson(baseDto.getParam(), CreateBizContractRequest.class);
                insertActionLog(createBizContractRequest.getAccountId(), OperationResourceEnum.CONTRACT, "创建合同：" + createBizContractRequest.getContractTitle());
                break;
            case "createProductTemplates":
                CreateProductTemplateRequest createProductTemplateRequest = JacksonUtils.fromJson(baseDto.getParam(),
                                                                                                  CreateProductTemplateRequest.class);
                SfProductTemplate sfProductTemplate = sfProductTemplateMapper.selectOne(
                        new QueryWrapper<SfProductTemplate>().eq("template_name",
                                                                 createProductTemplateRequest.getTemplateName())
                                                             .orderByDesc("id"));
                if (Objects.nonNull(sfProductTemplate)) {
                    baseDto.setBizId(sfProductTemplate.getId().toString());
                }
                break;
            case "batchCreateTariffSpecCharge":
                BatchBizBillingTariffSpecChargeRequest batchBizBillingTariffSpecChargeRequest = JacksonUtils.fromJson(
                        baseDto.getParam(),
                        BatchBizBillingTariffSpecChargeRequest.class);

                List<BizBillingTariffSpecCharge> bizBillingTariffSpecCharges = specChargeMapper.selectList(
                        new QueryWrapper<BizBillingTariffSpecCharge>()
                                .eq("spec_group_id", batchBizBillingTariffSpecChargeRequest.getSpecGroupId())
                                .orderByDesc("id")
                                .last("limit " + batchBizBillingTariffSpecChargeRequest.getSpecCharges().size()));
                if (bizBillingTariffSpecCharges.size() > 0) {
                    baseDto.setBizId(StringUtils.join(
                            bizBillingTariffSpecCharges.stream().map(BizBillingTariffSpecCharge::getSpecGroupName).collect(
                                    Collectors.toList()), StrUtil.COMMA));
                }
                break;
            case "createTariffSpecCharge":
                List createBizBillingTariffSpecChargeRequestList = JacksonUtils.fromJson(baseDto.getParam(),
                                                                                         List.class);
                CreateBizBillingTariffSpecChargeRequest convert = BeanConvertUtil.convert(
                        createBizBillingTariffSpecChargeRequestList.get(0),
                        CreateBizBillingTariffSpecChargeRequest.class);

                BizBillingTariffSpecCharge bizBillingTariffSpecCharge = specChargeMapper.selectOne(
                        new QueryWrapper<BizBillingTariffSpecCharge>()
                                .eq("spec_group_id", convert.getSpecGroupId())
                                .orderByDesc("id"));
                if (Objects.nonNull(bizBillingTariffSpecCharge)) {
                    baseDto.setBizId(bizBillingTariffSpecCharge.getId().toString());
                }
                break;
            default:
                break;
        }

    }

    /**
     * 创建子用户
     *
     * @param methodResult 方法结果
     * @param baseDto      基地dto
     */
    private void operationLogForCreateSubUsers(String methodResult, OperationLogBaseDto baseDto) {
        CreateUsersRequest request = JacksonUtils.fromJson(baseDto.getParam(), CreateUsersRequest.class);
        List<String> userAccountS = request.getUsers().stream().map(u -> u.getAccount()).collect(Collectors.toList());
        baseDto.setTagName(StringUtils.join(userAccountS,","));
        if (cn.com.cloudstar.rightcloud.oss.common.util.StringUtil.isNotEmpty(methodResult)) {
            RestResult restResult = JacksonUtils.fromJson(methodResult, RestResult.class);
            Optional.ofNullable(restResult.getData()).ifPresent(bizId -> baseDto.setBizId(bizId.toString()));
        }
    }

    /**
     * 创建产品类别
     *
     * @param methodResult 方法结果
     * @param baseDto      基地dto
     */
    private void operationLogCreateProductCategory(String methodResult, OperationLogBaseDto baseDto) {
        if (cn.com.cloudstar.rightcloud.oss.common.util.StringUtil.isNotEmpty(methodResult)) {
            RestResult restResult = JacksonUtils.fromJson(methodResult, RestResult.class);
            if (Objects.requireNonNull(restResult).getStatus()) {
                QueryWrapper<SfProductCategoryCatalog> queryWrapper = new QueryWrapper<SfProductCategoryCatalog>().orderByDesc("created_dt");
                List<SfProductCategoryCatalog> sfProductCategoryCatalogs = sfProductCategoryCatalogMapper.selectList(queryWrapper);
                Optional.ofNullable(sfProductCategoryCatalogs).ifPresent(s -> baseDto.setBizId(String.valueOf(s.get(0).getId())));
            }
        }
    }

    /**
     * 申请发票
     * @param baseDto
     */
    private void operationLogForcreateInvoice(OperationLogBaseDto baseDto) {
        if(InvoiceTypeEnum.PLAININVOICE.type.equals(baseDto.getParam())){
           baseDto.setTagName(InvoiceTypeEnum.PLAININVOICE.desc);
        }
        if(InvoiceTypeEnum.VATINVOICE.type.equals(baseDto.getParam())){
            baseDto.setTagName(InvoiceTypeEnum.VATINVOICE.desc);
        }
    }

    /**
     * 下载列表
     *
     * @param point   点
     * @param baseDto 基地dto
     */
    private void operationLogForDownloadJobList(JoinPoint point,OperationLogBaseDto baseDto) {
        BizDownloadRequest arg = (BizDownloadRequest) point.getArgs()[0];
        baseDto.setTagName(InvoiceTypeEnum.get(arg.getOperationType()));
        List<BizDownload> bizDownloads = bizDownloadMapper.listDownLoad(baseDto.getOrgSid());
        String fileName="";
        if(bizDownloads.size()>0){
            fileName=bizDownloads.get(0).getDownloadNum();
        }
        switch (arg.getOperationType()){
            case "service_order":
                baseDto.setResource(OperationResourceEnum.DOWNLOAD_ORDER_JOB_LIST);
                baseDto.setTagName("查看下载订单任务列表");
                break;
            case "bizAccountDeal":
                baseDto.setResource(OperationResourceEnum.DOWNLOAD_INCOME_AND_EXPENDITURE_DETAILS_JOB_LIST);
                baseDto.setTagName("查看下载收支明细任务列表");
                break;
            case "billCycle":
                baseDto.setResource(OperationResourceEnum.DOWNLOAD_BILLING_CRYLE_JOB_LIST);
                baseDto.setTagName("查看下载账单周期任务列表");
                break;
            case "billDetail":
                baseDto.setResource(OperationResourceEnum.DOWNLOAD_BILLING_DETAILS_JOB_LIST);
                baseDto.setTagName("查看下载账单明细任务列表");
                break;
            default:
                break;
        }
    }

    /**
     * 续订
     *
     * @param point   点
     * @param baseDto 基地dto
     */
    private void operationLogForRenewResource(JoinPoint point, OperationLogBaseDto baseDto) {
        RenewRequest renewRequest = (RenewRequest) point.getArgs()[0];
        ProductInfoVO productInfoVO = renewRequest.getProductInfo().get(0);
        Optional.ofNullable(productInfoVO).ifPresent(p -> {
            SfProductResource sfProductResource = sfProductResourceMapper.selectById(p.getId());
            if (Objects.nonNull(sfProductResource)) {
                ServiceOrder serviceOrder = serviceOrderMapper.selectById(sfProductResource.getServiceOrderId());
                baseDto.setTagName(serviceOrder.getName());
            } else {
                baseDto.setTagName(p.getName());
            }
            baseDto.setBizId(p.getId());
            if ("DRP".equals(p.getProductCode())) {
                baseDto.setResource(OperationResourceEnum.RENEWRESOURCE);
            }
        });
    }

    /**
     * 询价
     *
     * @param point   点
     * @param baseDto 基地dto
     */
    private void operationLogForInquiryPrice(JoinPoint point, OperationLogBaseDto baseDto) {
        ApplyServiceRequest applyServiceRequest = (ApplyServiceRequest) point.getArgs()[0];
        Optional.ofNullable(applyServiceRequest).ifPresent(a->{
            if ("apply".equals(a.getOrderType())){
                baseDto.setTagName("ModelArts专属资源池");
            } else if ("apply-other_service".equals(a.getOrderType())){
                baseDto.setTagName("ModelArts共享资源池");
            }
        });
    }

    /**
     * 操作日志重置用户密码
     *
     * @param baseDto 基地dto
     */
    private void operationLogForResetUserPassword(OperationLogBaseDto baseDto) {
        if(StringUtils.isNotEmpty(baseDto.getBizId())){
            List<String> userIds = Arrays.asList(baseDto.getBizId().split(","));
            List<String> accounts = new ArrayList<>();
            for (String userId : userIds) {
                cn.com.cloudstar.rightcloud.bss.common.pojo.User user = userMapper.selectByPrimaryKey(Long.valueOf(userId));
                // 手动拼接tagName
                accounts.add(user.getAccount() + "，资源ID：" + userId);
            }
            baseDto.setTagName(StringUtils.join(accounts,"；"));
            // bizId置空，防止二次组装
            baseDto.setBizId(null);
        }
    }

    /**
     * 操作日志产品定价
     *
     * @param baseDto 基地dto
     */
    private void operationLogProductBillingConfigurationPricing(OperationLogBaseDto baseDto) {
        UpdateBizBillingStrategyServingRequest request = JacksonUtils.fromJson(baseDto.getParam(), UpdateBizBillingStrategyServingRequest.class);
        Optional.ofNullable(request).ifPresent(r -> baseDto.setTagName(InvoiceTypeEnum.get(r.getResourceType())));
    }

    /**
     * 移除项目用户
     *
     * @param baseDto 基地dto
     */
    private void operationLogForRomoveProjectUser(OperationLogBaseDto baseDto) {
        AuthUser authUser = userMapper.selectById(Long.valueOf(baseDto.getBizId()));
        Optional.ofNullable(authUser).ifPresent(u->baseDto.setTagName(u.getAccount()));
    }

    /**
     * 配置项目用户/用户组
     * @param baseDto
     */
    private void operationLogForConfigProjectUser(OperationLogBaseDto baseDto) {
        RelateProjectUserRequest request = JacksonUtils.fromJson(baseDto.getParam(), RelateProjectUserRequest.class);
        List<Long> userGroupIds = request.getUserGroupIds();
        List<Long> userIds = request.getUserIds();
        List<String> groupNames = new ArrayList<>();
        List<String> userNames = new ArrayList<>();
        Optional.ofNullable(userGroupIds)
                .ifPresent(groupIds -> groupIds.forEach(groupId -> {
            SysGroup sysGroup = sysGroupMapper.selectByPrimaryKey(groupId);
            Optional.ofNullable(sysGroup)
                    .ifPresent(x -> groupNames.add(sysGroup.getGroupName()));
        }));
        Optional.ofNullable(userIds)
                .ifPresent(ids->ids.forEach(id->{
            AuthUser user = userMapper.selectById(id);
            Optional.ofNullable(user)
                    .ifPresent(x -> userNames.add(user.getAccount()));
        }));
        String tempUserName = StringUtils.join(userNames, "，");
        String userName = "";
        String userGroupName = "";
        if (StringUtil.isNotEmpty(tempUserName)){
            userName = "[用户名称：" + tempUserName + "，资源ID：" + userIds.toString() + "]；";
        }
        String tempGroupNames = StringUtils.join(groupNames, "，");
        if (StringUtil.isNotEmpty(tempGroupNames)){
            userGroupName = "[用户组名称：" + tempGroupNames + "，资源ID：" + userGroupIds.toString() + "]";
        }
        baseDto.setTagName(userName + userGroupName);
    }

    /**
     * 修改计费模式
     * @param baseDto
     */
    private void operationLogForBillingPublicAccountConfig(OperationLogBaseDto baseDto) {
        String billingStrategy = baseDto.getTagName();
        Code code = codeMapper.selectByCodeCategoryAndCodeValue(TypesConstant.BILLING_STRATEGY_CHARGE_TYPE,
                                                                billingStrategy);
        Optional.ofNullable(code).ifPresent(x ->baseDto.setTagName(x.getCodeDisplay()));
    }

    private void perationLogChangeDesc(JoinPoint point,OperationLogBaseDto baseDto) {
        ResizeResourceRequest request = (ResizeResourceRequest) point.getArgs()[0];
        Integer size = request.getProductInfo().getSize();
        String resourceId = request.getProductInfo().getId();
        SfProductResource resource = sfProductResourceMapper.selectById(resourceId);
        if (!ObjectUtils.isEmpty(resource)) {
            ServiceOrder serviceOrder = serviceOrderMapper.selectById(resource.getServiceOrderId());
            ResMaPoolVO resMaPoolVO = BeanConvertUtil.convert(resMaPoolService.getResMaPoolInfo(resource.getClusterId()).getData(), ResMaPoolVO.class);
            Integer availableCount = resMaPoolVO.getAvailableCount();
            if (size.equals(availableCount)) {
                return;
            }
            String action = "缩容";
            baseDto.setResource(OperationResourceEnum.DEGRADE_RESOURCE);
            if (size > availableCount) {
                action = "扩容";
                baseDto.setResource(OperationResourceEnum.UPGRADE_RESOURCE);
            }
            String tag = String.format("[%s]变更配置：由[%s]GB%s到[%s]GB", serviceOrder.getName(), availableCount, action, size);
            baseDto.setTagName(tag);
        }
    }


    private void operationLogForOpenSFS(OperationLogBaseDto baseDto) {
        ApplyServiceRequest request = BeanConvertUtil.convert(baseDto.getParam(), ApplyServiceRequest.class);
        ApplyServiceVO applyServiceVO = BeanConvertUtil.convert(request, ApplyServiceVO.class);
        String MaProductCode = "ModelArts";
        String MaDRPProductCode = "DRP";
        if (StringUtils.isEmpty(Objects.requireNonNull(request).getProductName())){
            for (cn.com.cloudstar.rightcloud.bss.module.billing.pojo.request.ProductInfoVO productInfoVO : applyServiceVO.getProductInfo()) {
                // productInfo 里面只要有 productCode 为 ModelArts 就是共享
                if (MaProductCode.equals(productInfoVO.getProductCode())){
                    baseDto.setTagName("ModelArts共享资源池");
                    return;
                }
                if (MaDRPProductCode.equals(productInfoVO.getProductCode())){
                    baseDto.setTagName("ModelArts专属资源池");
                    return;
                }
            }
        }
    }

    /**
     * 下载图片
     * @param baseDto
     */
    private void operationLogForDownloadFile(OperationLogBaseDto baseDto) {
        SysMFilePath sysMFilePath = sysMFilePathMapper.selectByPrimaryKey(baseDto.getBizId());
        Optional.ofNullable(sysMFilePath).ifPresent(f ->{
            if ("03".equals(f.getOperationType())){
                baseDto.setTagName("认证图片");
            }
        });
    }

    /**
     * 纳管云环境
     * @param baseDto
     */
    private void operationLogForMgtEnvOfDb(OperationLogBaseDto baseDto) {
        MgtCloudEnvRegionRequest request = JacksonUtils.fromJson(baseDto.getParam(), MgtCloudEnvRegionRequest.class);
        List<String> names = new ArrayList<>();
        for (CloudEnvVO cloudEnv : request.getCloudEnvs()) {
            if(cloudEnv != null){
                names.add(cloudEnv.getCloudEnvName());
            }
        }
        baseDto.setTagName(StringUtils.join(names,"，"));
    }

    /**
     * 批量创建用户
     * @param baseDto
     */
    private void operationLogForAddUsers(OperationLogBaseDto baseDto){
        CreateUsersRequest request = JacksonUtils.fromJson(baseDto.getParam(), CreateUsersRequest.class);
        List<String> userNames = request.getUsers().stream().map(UserVO::getAccount).collect(Collectors.toList());
        baseDto.setTagName(StringUtils.join(userNames,"，"));
    }

    /**
     * 编辑子用户
     * @param baseDto
     */
    private void operationLogForModifySubuser(OperationLogBaseDto baseDto) {
        baseDto.setTagName(userMapper.selectByPrimaryKey(Long.valueOf(baseDto.getBizId())).getAccount());
    }

    /**
     * 从用户组中移除用户
     * @param baseDto
     */
    private void operationLogForRemoveSubuserFromGroup(OperationLogBaseDto baseDto) {
        RemoveSubuserRequest request = JacksonUtils.fromJson(baseDto.getParam(), RemoveSubuserRequest.class);
        List<String> groupNames = new ArrayList<>();
        request.getGroupSids().stream().forEach(groupId -> {
            SysGroup sysGroup = sysGroupMapper.selectByPrimaryKey(groupId);
            Optional.ofNullable(sysGroup).ifPresent(x -> groupNames.add(sysGroup.getGroupName()));
        });
        String tempGroupNames = StringUtils.join(groupNames,"，");
        String userGroup = "[用户组名称：" + tempGroupNames + "，资源ID：" + request.getGroupSids().toString() + "]；";
        List<String> accounts = new ArrayList<>();
        request.getUserSids().stream().forEach(userSid -> {
            AuthUser user = userMapper.selectById(userSid);
            Optional.ofNullable(user).ifPresent(u -> accounts.add(u.getAccount()));
        });
        String userNames = StringUtils.join(accounts, "，");
        String user = "[用户名称：" + userNames + "，资源ID：" + request.getUserSids().toString() + "]";
        baseDto.setTagName(userGroup + user);
    }

    /**
     * 移除用户权限
     * @param baseDto
     */
    private void operationLogForRemovePolicyUser(OperationLogBaseDto baseDto) {
        DeletePolicy4UserRequest request = JacksonUtils.fromJson(baseDto.getParam(), DeletePolicy4UserRequest.class);
        Long userSid = request.getUserSid();
        AuthUser user = userMapper.selectById(userSid);
        String userName = "[用户名称：" + user.getAccount() + "，资源ID：" + userSid.toString() + "]；";
        List<String> policyNames = new ArrayList<>();
        request.getPolicies().stream().forEach(policySid -> {
            Policy policy = policyMapper.selectById(policySid);
            Optional.ofNullable(policy).ifPresent(p -> policyNames.add(p.getDisplayName()));
        });
        String policyName = StringUtils.join(policyNames, "，");
        String policy = "[权限名称：" + policyName + "，资源ID：" + request.getPolicies().toString() + "]";
        baseDto.setTagName(userName + policy);
    }

    /**
     * 调整业务标识Tag
     *
     * @param baseDto
     */
    private void operationLogForUpdateBusinessTag(OperationLogBaseDto baseDto) {
        UpdateBusinessTagRequest request = JacksonUtils.fromJson(baseDto.getParam(), UpdateBusinessTagRequest.class);
        if (Objects.isNull(request)) {
            return;
        }
        String businessTag = request.getBusinessTag();
        String[] tags = businessTag.split(";");
        String tagName = Arrays.stream(tags)
                .map(BusinessTagEnum::getBusinessTagEnumByTag)
                .filter(Objects::nonNull)
                .map(BusinessTagEnum::getName).collect(Collectors.joining(","));
        baseDto.setTagName("[修改后标签为：" + tagName + ";Tag ID：" + businessTag + "]");
    }

    /**
     * 新增不计费产品信息
     *
     * @param baseDto
     */
    private void operationLogForAddDistributorProduct(OperationLogBaseDto baseDto) {
        BizDistributorProduct request = JacksonUtils.fromJson(baseDto.getParam(), BizDistributorProduct.class);
        if (Objects.isNull(request)) {
            return;
        }
        Org org = orgMapper.selectById(request.getDistributorId());
        ServiceCategory serviceCategory = serviceCategoryService.getById(request.getSfServiceId());
        baseDto.setTagName("[组织" + org.getOrgName() + "新增不计费产品" + serviceCategory.getServiceName() + "]");
    }

    /**
     * 套餐包导出区分是折扣包导出还是卡时包导出
     *
     * @param baseDto
     */
    private void operationLogForAddAsynBizBagBillDetailsExport(OperationLogBaseDto baseDto) {
        DescribeGaapCostRequest request = JacksonUtils.fromJson(baseDto.getParam(), DescribeGaapCostRequest.class);
        if (Objects.isNull(request)) {
            return;
        }
        BizBag bizBag = bizBagMapper.selectById(Long.valueOf(request.getBagInstUuid()));
        if(StatusType.CARD_HOUR.equals(bizBag.getType())){
            baseDto.setTagName("卡时包明细异步导出");
        }
    }

    /**
     * 修改客户资源类型状态
     *
     * @param baseDto
     */
    private void operationLogForUpdateResourceStatus(OperationLogBaseDto baseDto) {
        EditCustomResourceRequest request = JacksonUtils.fromJson(baseDto.getParam(), EditCustomResourceRequest.class);
        if (Objects.isNull(request)) {
            return;
        }
        AuthUser authUser = userMapper.selectById(request.getUserSid());
        BizBillingCustomRegionResource resource = bizBillingCustomRegionResourceMapper.selectById(request.getId());
        baseDto.setTagName("[修改用户" + authUser.getAccount() + "的" + resource.getResourceType() + "资源为" + request.getType() + "状态]");
    }

    /**
     * 更新充值明细
     * @param baseDto
     */
    private void operationLogForUpdateDeposit(OperationLogBaseDto baseDto) {
        if(StringUtils.isNotEmpty(baseDto.getParam())){
            BizContract contract = bizContractService.getById(Long.valueOf(baseDto.getParam()));
            Optional.ofNullable(contract).ifPresent(i->baseDto.setTagName(i.getContractTitle()));
        }
    }

    /**
     * 移除用户组权限
     * @param baseDto
     */
    private void operationLogForRemovePolicyForGroup(OperationLogBaseDto baseDto) {
        DeletePolicy4GroupRequest request = JacksonUtils.fromJson(baseDto.getParam(), DeletePolicy4GroupRequest.class);
        Long groupId = request.getGroupSid();
        SysGroup sysGroup = sysGroupMapper.selectByPrimaryKey(groupId);
        String userGroup = "[用户组名称：" + sysGroup.getGroupName() + "，资源ID：" + groupId.toString() + "]；";
        List<String> policyNames = new ArrayList<>();
        request.getPolicies().stream().forEach(policySid -> {
            Policy policy = policyMapper.selectById(policySid);
            Optional.ofNullable(policy).ifPresent(p -> policyNames.add(p.getDisplayName()));
        });
        String policyName = StringUtils.join(policyNames, "，");
        String policy = "[权限名称：" + policyName + "，资源ID：" + request.getPolicies().toString() + "]";
        baseDto.setTagName(userGroup + policy);
    }

    /**
     * 解除用户锁定状态
     * @param baseDto
     */
    private void operationLogForUserUnlockStatus(OperationLogBaseDto baseDto) {
        UnlockIamUserRequest request = JacksonUtils.fromJson(baseDto.getParam(), UnlockIamUserRequest.class);
        List<String> userNames = new ArrayList<>();
        request.getUserSids().stream().forEach(userId -> {
            cn.com.cloudstar.rightcloud.bss.common.pojo.User user = userMapper.selectByPrimaryKey(userId);
            Optional.ofNullable(user).ifPresent(x -> userNames.add(user.getAccount()));
        });
       baseDto.setTagName(StringUtils.join(userNames,"，"));
       baseDto.setBizId(StringUtils.join(request.getUserSids(),","));
    }

    /**
     * 删除权限策略/配置权限断言/更新策略关联资源
     * @param baseDto
     */
    private void operationLogForDeletePolicy(OperationLogBaseDto baseDto) {
        if(StringUtils.isNotEmpty(baseDto.getBizId())){
            Policy policy = policyService.getById(Long.valueOf(baseDto.getBizId()));
            Optional.ofNullable(policy).ifPresent(x -> baseDto.setTagName(policy.getPolicyName()));
        }
    }

    /**
     * 配置权限策略用户、用户组
     * @param baseDto
     */
    private void operationLogForConfigPolicy(OperationLogBaseDto baseDto) {
        RelatePolicyUserRequest request = JacksonUtils.fromJson(baseDto.getParam(), RelatePolicyUserRequest.class);
        List<Long> userGroupIds = request.getUserGroupIds();
        List<Long> userIds = request.getUserIds();
        Long policySid = request.getPolicySid();
        Policy policy = policyMapper.selectById(policySid);
        String policyName = "[权限名称：" + policy.getDisplayName() + "，资源ID：" + policySid.toString() + "]；";
        List<String> groupNames = new ArrayList<>();
        List<String> userNames = new ArrayList<>();
        userGroupIds.stream().forEach(groupId -> {
            SysGroup sysGroup = sysGroupMapper.selectByPrimaryKey(groupId);
            Optional.ofNullable(sysGroup).ifPresent(x -> groupNames.add(sysGroup.getGroupName()));
        });
        userIds.forEach(t -> {
            cn.com.cloudstar.rightcloud.bss.common.pojo.User user = userMapper.selectByPrimaryKey(t);
            Optional.ofNullable(user).ifPresent(x -> userNames.add(user.getAccount()));
        });
        String tempUserName = StringUtils.join(userNames, "，");
        String userName = "";
        String userGroupName = "";
        if (StringUtil.isNotEmpty(tempUserName)){
            userName = "[用户名称：" + tempUserName + "，资源ID：" + userIds.toString() + "]；";
        }
        String tempGroupNames = StringUtils.join(groupNames, "，");
        if (StringUtil.isNotEmpty(tempGroupNames)){
            userGroupName = "[用户组名称：" + tempGroupNames + "，资源ID：" + userGroupIds.toString() + "]";
        }
        baseDto.setTagName(policyName + userName + userGroupName);
    }

    /**
     * 用户组关联用户/用户关联用户组
     *
     * @param baseDto
     */
    private void operationLogForModifyUserInGroup(OperationLogBaseDto baseDto) {
        MoveUserToGroupRequest request = JacksonUtils.fromJson(baseDto.getParam(), MoveUserToGroupRequest.class);
        List<String> groupNames = new ArrayList<>();
        request.getGroupIds().stream().forEach(groupId -> {
            SysGroup sysGroup = sysGroupMapper.selectByPrimaryKey(groupId);
            Optional.ofNullable(sysGroup).ifPresent(x -> groupNames.add(sysGroup.getGroupName()));
        });
        String tempGroupNames = StringUtils.join(groupNames,"，");
        String userGroup = "[用户组名称：" + tempGroupNames + "，资源ID：" + request.getGroupIds().toString() + "]；";
        List<String> accounts = new ArrayList<>();
        request.getUserSids().stream().forEach(userSid -> {
            AuthUser user = userMapper.selectById(userSid);
            Optional.ofNullable(user).ifPresent(u -> accounts.add(u.getAccount()));
        });
        String userNames = StringUtils.join(accounts, "，");
        String user = "[用户名称：" + userNames + "，资源ID：" + request.getUserSids().toString() + "]";
        baseDto.setTagName(userGroup + user);
    }

    /**
     * 用户组配置权限
     *
     * @param baseDto
     */
    private void operationLogForConfigPolicyGroup(OperationLogBaseDto baseDto) {
        RelatePolicyGroupRequest request = JacksonUtils.fromJson(baseDto.getParam(), RelatePolicyGroupRequest.class);
        List<String> groupNames = new ArrayList<>();
        request.getUserGroupIds().stream().forEach(groupId -> {
            SysGroup sysGroup = sysGroupMapper.selectByPrimaryKey(groupId);
            Optional.ofNullable(sysGroup).ifPresent(x -> groupNames.add(sysGroup.getGroupName()));
        });
        String tempGroupNames = StringUtils.join(groupNames,"，");
        String userGroup = "[用户组名称：" + tempGroupNames + "，资源ID：" + request.getUserGroupIds().toString() + "]；";
        List<String> policyNames = new ArrayList<>();
        request.getPolicySids().stream().forEach(policySid -> {
            Policy policy = policyMapper.selectById(policySid);
            Optional.ofNullable(policy).ifPresent(p -> policyNames.add(p.getDisplayName()));
        });
        String policyName = StringUtils.join(policyNames, "，");
        String policy = "[权限名称：" + policyName + "，资源ID：" + request.getPolicySids().toString() + "]";
        baseDto.setTagName(userGroup + policy);
    }

    /**
     * 用户配置权限
     * @param baseDto
     */
    private void operationLogForConfigPolicyUser(OperationLogBaseDto baseDto) {
        RelatePolicyUserRequest2 request = JacksonUtils.fromJson(baseDto.getParam(), RelatePolicyUserRequest2.class);
        List<Long> userIds = request.getUserIds();
        List<String> userNames = new ArrayList<>();
        userIds.forEach(t -> {
            cn.com.cloudstar.rightcloud.bss.common.pojo.User user = userMapper.selectByPrimaryKey(t);
            Optional.ofNullable(user).ifPresent(x -> userNames.add(user.getAccount()));
        });
        String tempUserName = StringUtils.join(userNames,"，");
        String user = "[用户名称：" + tempUserName + "，资源ID：" + request.getUserIds().toString() + "]；";
        List<String> policyNames = new ArrayList<>();
        request.getPolicySids().stream().forEach(policySid -> {
            Policy policy = policyMapper.selectById(policySid);
            Optional.ofNullable(policy).ifPresent(p -> policyNames.add(p.getDisplayName()));
        });
        String policyName = StringUtils.join(policyNames, "，");
        String policy = "[权限名称：" + policyName + "，资源ID：" + request.getPolicySids().toString() + "]";
        baseDto.setTagName(user + policy);
    }

    /**
     * 删除项目
     * @param baseDto
     */
    private void operationLogForDeleteProject(OperationLogBaseDto baseDto) {
        if(StringUtils.isNotEmpty(baseDto.getBizId())){
            Project project = projectService.getById(Long.valueOf(baseDto.getBizId()));
            Optional.ofNullable(project).ifPresent(i->baseDto.setTagName(project.getOrgName()));
        }
    }

    /**
     * 更新账户折扣系数
     * @param baseDto
     */
    private void operationLogForUpdateUserDiscount(OperationLogBaseDto baseDto) {
        if(StringUtils.isNotEmpty(baseDto.getBizId())){
            BizBillingAccount billingAccount = bizBillingAccountService.getById(Long.valueOf(baseDto.getBizId()));
            Optional.ofNullable(billingAccount).ifPresent(i->baseDto.setTagName(billingAccount.getAccountName()));
        }
    }

    /**
     * 删除规格族
     * @param baseDto
     */
    private void operationLogForDeleteSpecGroup(OperationLogBaseDto baseDto) {
        if(StringUtils.isNotEmpty(baseDto.getBizId())){
            BizBillingSpecGroup specGroup = iBizBillingSpecGroupService.getById(Long.valueOf(baseDto.getBizId()));
            Optional.ofNullable(specGroup).ifPresent(i->baseDto.setTagName(specGroup.getName()));
        }
    }

    /**
     * 克隆规格族
     * @param baseDto
     */
    private void operationLogForCloneSpecGroup(OperationLogBaseDto baseDto) {
        if(StringUtils.isNotEmpty(baseDto.getParam())){
            BizBillingSpecGroup specGroup = iBizBillingSpecGroupService.getById(Long.valueOf(baseDto.getParam()));
            if (Objects.isNull(specGroup)) {
                baseDto.setTagName("规格族不存在：" + baseDto.getParam());
                return;
            }
            baseDto.setTagName(specGroup.getName());
        }
    }

    /**
     * 更新规格定价
     * @param baseDto
     */
    private void operationLogForUpdateBillingTariffSpecCharge(OperationLogBaseDto baseDto) {
        List<UpdateBizBillingTariffSpecChargeRequest> requests = JacksonUtils.parseList(baseDto.getParam(), UpdateBizBillingTariffSpecChargeRequest.class);
        List<Long> ids = requests.stream().map(UpdateBizBillingTariffSpecChargeRequest::getId).collect(Collectors.toList());
        baseDto.setBizId(StringUtils.join(ids,","));

    }

    /**
     * 删除产品
     * @param baseDto
     */
    private void operationLogForDeleteProduct(OperationLogBaseDto baseDto) {
        if(StringUtils.isNotEmpty(baseDto.getBizId())){
            ServiceCategory serviceCategory = serviceCategoryService.getById(Long.valueOf(baseDto.getBizId()));
            Optional.ofNullable(serviceCategory).ifPresent(i->baseDto.setTagName(serviceCategory.getProductName()));
        }
    }

    /**
     * 关联产品模板
     * @param baseDto
     */
    private void operationLogForRelatedProductTemplate(OperationLogBaseDto baseDto) {
        RelatedProductTemplatesRequest request = JacksonUtils.fromJson(baseDto.getParam(), RelatedProductTemplatesRequest.class);
        ServiceCategory serviceCategory = serviceCategoryService.getById(request.getProductId());
        Optional.ofNullable(serviceCategory).ifPresent(i->baseDto.setTagName(serviceCategory.getProductName()));

    }

    /**
     * 删除折扣/启用禁用折扣
     * @param baseDto
     */
    private void operationLogForDeleteDiscount(OperationLogBaseDto baseDto) {
        if(StringUtils.isNotEmpty(baseDto.getBizId())){
            BizDiscount bizDiscount = bizDiscountService.getById(Long.valueOf(baseDto.getBizId()));
            Optional.ofNullable(bizDiscount).ifPresent(i->baseDto.setTagName(bizDiscount.getDiscountName()));
        }
    }

    /**
     * 编辑备注
     * @param baseDto
     */
    private void operationLogForRemark(OperationLogBaseDto baseDto) {
        UpdateRemarkRequest request = JacksonUtils.fromJson(baseDto.getParam(), UpdateRemarkRequest.class);
        String logDetail = "修改备注为：" + request.getRemark();
        insertActionLog(request.getAccountId(), OperationResourceEnum.REMARK, logDetail);
    }

    /**
     * 更新分销商
     * @param baseDto
     */
    private void operationLogForReCharge1(OperationLogBaseDto baseDto) {
        UpdateDistributorRequest request = JacksonUtils.fromJson(baseDto.getParam(), UpdateDistributorRequest.class);
        if(StringUtils.isNotEmpty(baseDto.getBizId())){
            BizBillingAccount billingAccount = bizBillingAccountMapper.selectById(Long.valueOf(baseDto.getBizId()));
            Optional.ofNullable(billingAccount).ifPresent(i->baseDto.setTagName(billingAccount.getAccountName()));
        }
        String logDetail = "关联了分销商：";
        if (request.getDistributorId() != null) {
            BizDistributor distributor = bizDistributorService.getById(request.getDistributorId());
            logDetail = logDetail + distributor.getName();
        } else {
            logDetail = logDetail + "直营";
        }
        insertActionLog(request.getAccountId(), OperationResourceEnum.DISTRIBUTOR, logDetail);
    }

    /**
     * 编辑分销商账户角色
     * @param baseDto
     */
    private void operationLogForUpdateDistributorUserRole(OperationLogBaseDto baseDto) {
        if(StringUtils.isNotEmpty(baseDto.getBizId())){
            User user = sysUserService.selectByPrimaryKey(Long.valueOf(baseDto.getBizId()));
            Optional.ofNullable(user).ifPresent(x -> baseDto.setTagName(x.getAccount()));
        }
    }

    /**
     * 删除分销商账户
     * @param baseDto
     */
    private void operationLogForDeleteDistributorUser(OperationLogBaseDto baseDto) {
        DeleteDistributorUserRequest request = JacksonUtils.fromJson(baseDto.getParam(), DeleteDistributorUserRequest.class);
        List<String> userNames = new ArrayList<>();
        for (Long userSid : request.getUserSids()) {
            User user = sysUserService.selectByPrimaryKey(userSid);
            if(user!=null){
                userNames.add(user.getAccount());
            }
        }
        String join = StringUtils.join(request.getUserSids(), ',');
        baseDto.setBizId(join);
        baseDto.setTagName(StringUtils.join(userNames,"，"));
    }

    /**
     * 编辑分销商账户
     * @param baseDto
     */
    private void operationLogForUpdateDistributorUser(OperationLogBaseDto baseDto) {
        UpdateDistributorUserRequest request = JacksonUtils.fromJson(baseDto.getParam(), UpdateDistributorUserRequest.class);
        baseDto.setBizId(request.getUserSid().toString());
        baseDto.setTagName(request.getAccount());
    }

    /**
     * 删除/下载合同模板
     * @param baseDto
     */
    private void operationLogForDeleteResRdsAccount(OperationLogBaseDto baseDto) {
        if(StringUtils.isNotEmpty(baseDto.getBizId())){
            BizContractTemplate template = bizContractTemplateSrvice.getById(Long.valueOf(baseDto.getBizId()));
            Optional.ofNullable(template).ifPresent(i->baseDto.setTagName(i.getTemplateName()));

        }
    }

    /**
     * 查看合同详细/终止合同/预览合同/审核合同/审核合同/下载合同/管理合同上传附件
     * @param baseDto
     */
    private void operationLogForDetailContract(OperationLogBaseDto baseDto) {
        if(StringUtils.isNotEmpty(baseDto.getBizId())){
            BizContract contract = bizContractService.getById(Long.valueOf(baseDto.getBizId()));
            Optional.ofNullable(contract).ifPresent(i->baseDto.setTagName(i.getContractTitle()));
        }
    }

    private void operationLogForDetailContract2(OperationLogBaseDto baseDto) {
        if(StringUtils.isNotEmpty(baseDto.getBizId())){
            BizContract contract = bizContractService.getById(Long.valueOf(baseDto.getBizId()));
            if ("terminated".equals(contract.getContractStatus())){
                throw  new BizException("已终止状态的合同不能重新设定折扣!");
            }
            Optional.ofNullable(contract).ifPresent(i->baseDto.setTagName(i.getContractTitle()));
        }
    }
    private void operationLogForDetailContract1(OperationLogBaseDto baseDto) {
        if(StringUtils.isNotEmpty(baseDto.getBizId())){
            BizContract contract = bizContractService.getById(Long.valueOf(baseDto.getBizId()));
            if ("terminated".equals(contract.getContractStatus())){
                throw  new BizException("已禁用!");
            }
            Optional.ofNullable(contract).ifPresent(i->baseDto.setTagName(i.getContractTitle()));
        }
    }

    /**
     * 删除资源类型
     * @param baseDto
     */
    private void operationLogForRemoveResourceRegion(OperationLogBaseDto baseDto) {
        if(StringUtils.isNotEmpty(baseDto.getBizId())){
            BizBillingRegionResource regionResource = bizBillingRegionResourceService.getById(Long.valueOf(baseDto.getBizId()));
            if(regionResource != null){
                Code code = codeMapper.selectByCodeCategoryAndCodeValue(TypesConstant.CHARGE_RESOURCE_TYPE, regionResource.getResourceType());
                Optional.ofNullable(code).ifPresent(i->baseDto.setTagName(i.getCodeDisplay()));
            }
        }

    }

    /**
     * 配置资源类型
     * @param baseDto
     */
    private void operationLogForBillingStrategyAccount(OperationLogBaseDto baseDto) {
        ConfigRegionChargeRequest request = JacksonUtils.fromJson(baseDto.getParam(), ConfigRegionChargeRequest.class);
        BizBillingRegionResource regionResource = bizBillingRegionResourceService.getById(request.getId());
        if(regionResource!=null){
            Code code = codeMapper.selectByCodeCategoryAndCodeValue(TypesConstant.CHARGE_RESOURCE_TYPE, regionResource.getResourceType());
            Optional.ofNullable(code).ifPresent(i->baseDto.setTagName(i.getCodeDisplay()));
        }
        baseDto.setBizId(request.getId().toString());
    }

    /**
     * 添加资源类型
     * @param baseDto
     */
    private void operationLogForCreateResourceRegion(OperationLogBaseDto baseDto) {
        CreateRegionStrategyRequest request = JacksonUtils.fromJson(baseDto.getParam(), CreateRegionStrategyRequest.class);
        List<String> resourceType = request.getResourceType();
        List<String> prodcutNames = new ArrayList<>();
        resourceType.forEach(t -> {
            prodcutNames.add(ProductCodeEnum.keyFromDesc(t));
        });
        baseDto.setTagName(StrUtil.join(StrUtil.COMMA, prodcutNames));
        baseDto.setBizId(request.getEnvAccountId().toString());
    }

    /**
     * 删除计费策略
     * @param baseDto
     */
    private void operationLogForDeleteBillingStrategy(OperationLogBaseDto baseDto) {
        if(StringUtils.isNotEmpty(baseDto.getBizId())){
            BizBillingStrategy strategy = bizBillingStrategyService.getById(Long.valueOf(baseDto.getBizId()));
            Optional.ofNullable(strategy).ifPresent(i->baseDto.setTagName(i.getName()));
        }
    }

    /**
     * 启用/禁用计费策略
     * @param baseDto
     */
    private void operationLogForUpdateBillingStrategyStatus(OperationLogBaseDto baseDto) {
        String status = baseDto.getParam();
        if(BizBillingStrategyService.ENABLE.equals(status)){
            baseDto.setResource(OperationResourceEnum.ENABLE_BILLING_POLICY);
        }else if(BizBillingStrategyService.DISABLE.equals(status)){
            baseDto.setResource(OperationResourceEnum.DISABLE_BILLING_POLICY);
        }
        if(StringUtils.isNotEmpty(baseDto.getBizId())){
            BizBillingStrategy strategy = bizBillingStrategyService.getById(Long.valueOf(baseDto.getBizId()));
            Optional.ofNullable(strategy).ifPresent(i->baseDto.setTagName(i.getName()));

        }
    }

    /**
     * 删除产品模板
     * @param baseDto
     */
    private void operationLogFordeleteCategory(OperationLogBaseDto baseDto) {
        if(StringUtils.isNotEmpty(baseDto.getBizId())){
            SfProductTemplate template = iSfProductTemplateService.getById(Long.valueOf(baseDto.getBizId()));
            Optional.ofNullable(template).ifPresent(i -> baseDto.setTagName(template.getTemplateName()));
        }
    }

    /**
     * 禁用/启用产品模板
     * @param baseDto
     */
    private void operationLogForSwitchTemplateStatus(OperationLogBaseDto baseDto) {
        UpdateProductTemplateStatusRequest request = JacksonUtils.fromJson(baseDto.getParam(), UpdateProductTemplateStatusRequest.class);
        SfProductTemplate template = iSfProductTemplateService.getById(request.getId());
        baseDto.setBizId(request.getId().toString());
        Optional.ofNullable(template).ifPresent(i->baseDto.setTagName(template.getTemplateName()));
        if (Constants.DISABLE.equals(request.getStatus())) {
            baseDto.setResource(OperationResourceEnum.DISABLE_PRODUCT_TEMPLATES);
        } else {
            baseDto.setResource(OperationResourceEnum.ENABLE_PRODUCT_TEMPLATES);
        }
    }

    /**
     * 删除产品类别
     * @param baseDto
     */
    private void operationLogForDeleteCategory(OperationLogBaseDto baseDto) {
        if(StringUtils.isNotEmpty(baseDto.getBizId())){
            SfProductCategoryCatalog category = productCategoryService.getById(Long.valueOf(baseDto.getBizId()));
            Optional.ofNullable(category).ifPresent(i->baseDto.setTagName(category.getCategoryName()));
        }
    }

    /**
     * "产品目录的上架/下架
     * @param baseDto
     */
    private void operationLogForUpdateProductStatus(OperationLogBaseDto baseDto) {
        UpdateProductStatusRequest request = JacksonUtils.fromJson(baseDto.getParam(), UpdateProductStatusRequest.class);
        if(cn.com.cloudstar.rightcloud.common.constants.status.ServiceCategoryStatus.USING.equals(request.getStatus())){
            baseDto.setResource(OperationResourceEnum.PRODUCT_CATALOG_LISTING);
        }else if(ServiceCategoryStatus.NOUSING.equals(request.getStatus())){
            baseDto.setResource(OperationResourceEnum.PRODUCT_CATALOG_DELISTING);
        }
        if(request.getId()!=null){
            baseDto.setBizId(request.getId().toString());
            ServiceCategory serviceCategory = serviceCategoryService.getById(request.getId());
            Optional.ofNullable(serviceCategory).ifPresent(i->baseDto.setTagName(serviceCategory.getProductName()));

        }
    }

    /**
     * 分发优惠券/分发现金卷
     * @param baseDto
     */
    private void operationLogForDistributeCoupon(OperationLogBaseDto baseDto) {
        String param = baseDto.getParam();
        List<Long> accountIds = JacksonUtils.parseList(param, Long.class);
        List<String> accountNames= accountIds.stream().map(accountid -> {
            BizBillingAccount bizBillingAccount = bizBillingAccountMapper.selectByPrimaryKeys(accountid);
            if(bizBillingAccount==null){
                return "";
            }
            return Optional.ofNullable(bizBillingAccount.getAccountName()).orElse("");
        }).collect(Collectors.toList());
        baseDto.setTagName(StringUtils.join(accountNames,","));
    }

    /**
     * 删除用户组
     * @param baseDto
     */
    private void operationLogForDeleteUserGroup(OperationLogBaseDto baseDto){
        if(StringUtils.isNotEmpty(baseDto.getBizId())){
            SysGroup sysGroup = sysGroupMapper.selectByPrimaryKey(Long.valueOf(baseDto.getBizId()));
            Optional.ofNullable(sysGroup).ifPresent(i->baseDto.setTagName(sysGroup.getGroupName()));
        }
    }

    /**
     * 用户关联用户组
     * @param baseDto
     */
    private void operationLogForJoinGroup(OperationLogBaseDto baseDto) {
        MoveUserToGroupRequest request = JacksonUtils.fromJson(baseDto.getParam(), MoveUserToGroupRequest.class);
        List<String> groupNames = new ArrayList<>();
        request.getGroupIds().stream().forEach(groupId -> {
            SysGroup sysGroup = sysGroupMapper.selectByPrimaryKey(groupId);
            Optional.ofNullable(sysGroup).ifPresent(x -> groupNames.add(sysGroup.getGroupName()));
        });
        String tempGroupNames = StringUtils.join(groupNames,"，");
        baseDto.setTagName("用户组名称："+tempGroupNames);
    }

    /**
     * 删除子用户
     * @param baseDto
     */
    private void operationLogForDeleteSubUser(OperationLogBaseDto baseDto) {
        if(StringUtils.isNotEmpty(baseDto.getBizId())){
            User user = sysUserService.selectByPrimaryKey(Long.valueOf(baseDto.getBizId()));
            Optional.ofNullable(user).ifPresent(i->baseDto.setTagName(user.getAccount()));
        }
    }


    private enum InvoiceTypeEnum{

        /**
         * 普通发票
         */
        PLAININVOICE("0", "普通发票"),

        /**
         * 增值税专用发票
         */
        VATINVOICE("1", "增值税专用发票"),
        /**
         * 高性能计算
         */
        TYPE_4("4", "高性能计算"),
        /**
         * 订单
         */
        FILE_1("service_order","订单"),
        /**
         * 收支明细
         */
        FILE_2("bizAccountDeal","收支明细"),
        /**
         * 账单周期
         */
        FILE_3("billCycle","账单周期"),
        /**
         * 账单明细
         */
        FILE_4("billDetail","账单明细"),
        /**
         * ModelArts专属资源池
         */
        PRODUCT_1("DRP","ModelArts专属资源池"),
        /**
         * ModelArts共享资源池
         */
        PRODUCT_2("ModelArts","ModelArts共享资源池"),
        /**
         * 对象存储
         */
        PRODUCT_3("OBS","对象存储");

        private String type;

        private String desc;

        InvoiceTypeEnum(String type, String desc) {
            this.type = type;
            this.desc = desc;
        }

        public static String get(String key) {
            for (InvoiceTypeEnum invoiceTypeEnum : InvoiceTypeEnum.values()) {
                if (key.equals(invoiceTypeEnum.getType())) {
                    return invoiceTypeEnum.getDesc();
                }
            }
            return key;
        }

        public String getDesc() {
            return desc;
        }

        public String getType() {
            return type;
        }
    }

    /**
     * 插入操作日志(mysql-biz_customer_action_log)
     *
     * @param accountId             账户id
     * @param operationResourceEnum 操作资源枚举
     * @param logDetail             日志详细
     */
    private void insertActionLog(Long accountId, OperationResourceEnum operationResourceEnum, String logDetail) {
        BizCustomerActionLog actionLogDTO = new BizCustomerActionLog();
        BizBillingAccount billingAccount = bizBillingAccountMapper.selectById(accountId);
        actionLogDTO.setUserSid(billingAccount.getAdminSid());
        actionLogDTO.setOrgSid(billingAccount.getOrgSid());
        actionLogDTO.setOpType(operationResourceEnum.getType());
        actionLogDTO.setOpDetail(logDetail);
        actionLogDTO.setOpDate(new Date());
        actionLogDTO.setOpUser(RequestContextUtil.getAuthUserInfo().getUserSid());
        bizCustomerActionLogMapper.insert(actionLogDTO);
    }

}
