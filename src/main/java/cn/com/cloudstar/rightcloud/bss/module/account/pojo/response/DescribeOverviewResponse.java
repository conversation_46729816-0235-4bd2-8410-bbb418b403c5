/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.account.pojo.response;

import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.OverViewVO;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.UserVO;
import cn.com.cloudstar.rightcloud.bss.module.account.pojo.entity.WaitingCenterVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/10/29.
 */
@Data
@ApiModel(description = "控制台概览基础信息")
public class DescribeOverviewResponse {

    /**
     * 账户
     */
    @ApiModelProperty("账户ID")
    private UserVO user;

    /**
     * 待办中心
     */
    @ApiModelProperty("待办中心")
    private List<WaitingCenterVO>  waitingCenter;

    /**
     * 推荐产品
     */
    @ApiModelProperty("推荐产品，任意一种产品ID")
    private Map<String, Long> product;

    /**
     * 在视图
     */
    @ApiModelProperty("总览信息")
    private OverViewVO overViews;

}
