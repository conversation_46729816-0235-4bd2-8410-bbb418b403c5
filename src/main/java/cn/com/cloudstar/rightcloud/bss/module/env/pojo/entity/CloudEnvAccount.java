/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.env.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * Created on 2019/10/25
 */
@Data
@TableName("cloud_env_account")
@ApiModel(description = "云环境账户纳管记录CloudEnvShare")
public class CloudEnvAccount {

    @ApiModelProperty(value = "云环境账户纳管记录ID", name = "id", example = "1234")
    @TableId(type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "云环境账户名称", name = "envAccountName", example = "VM100")
    private String envAccountName;

    @ApiModelProperty(value = "云环境平台的账户ID", name = "envAccountId", example = "123")
    private String envAccountId;

    @ApiModelProperty(value = "云环境类型", name = "envType", example = "VMware，OpenStack，Aliyun等")
    private String envType;

    @ApiModelProperty(value = "未使用字段", name = "envCategory", example = "customer")
    private String envCategory;

    @ApiModelProperty(value = "所属企业ID", name = "orgSid", example = "123")
    private Long orgSid;

    @ApiModelProperty(value = "唯一性校验字段", name = "uniqueCheckData", example = "非传入字段")
    private String uniqueCheckData;

    @ApiModelProperty(value = "云环境状态", name = "status", example = "normal/inactive/error/disconnect")
    private String status;

    @ApiModelProperty(value = "纳管人", name = "createdBy", example = "非传入字段")
    private String createdBy;

    @ApiModelProperty(value = "纳管时间", name = "createdDt", example = "Date")
    private Date createdDt;

    @ApiModelProperty(value = "更新人", name = "updatedBy", example = "非传入字段")
    private String updatedBy;

    @ApiModelProperty(value = "更新时间", name = "updatedDt", example = "Date")
    private Date updatedDt;

    @ApiModelProperty(value = "版本号", name = "version", example = "非传入字段")
    private Long version;

    @ApiModelProperty(value = "云环境详细信息", name = "attrData",
            example = "JSON格式传入envDescription、tenantUserPass、tenantUserName、tenantName、envName、envAccount、company、region、providerUrl等信息")
    private String attrData;

    @ApiModelProperty(value = "所属企业ID", name = "belongsTo", example = "123")
    private String belongsTo;

    @ApiModelProperty(value = "关联平台网关组件id", notes = "关联组件表component_id")
    private String platformComponentId;

    private Boolean supportRegionMgt;

    /**
     * 标识是环境接入还是区域管理，默认为false，既不是云环境创建
     */
    @TableField(exist = false)
    private boolean createEnv;

    @TableField(exist = false)
    private String envTypeCategory;
}
