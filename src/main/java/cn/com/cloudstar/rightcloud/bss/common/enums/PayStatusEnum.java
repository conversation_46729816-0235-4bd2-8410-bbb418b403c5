/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.common.enums;


import cn.hutool.core.util.StrUtil;

/**
 * DESC:支付状态
 *
 * <AUTHOR>
 * @date 2019/10/24 14:47
 */
public enum PayStatusEnum {

    /**
     * 已支付
     */
    PAID(1, "已支付"),

    /**
     * 未支付
     */
    UNPAID(0, "未支付");

    private Integer code;
    private String name;

    PayStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String codeFromName(Integer code) {
        if (code == null) {
            return StrUtil.EMPTY;
        }
        for (PayStatusEnum value : PayStatusEnum.values()) {
            if (value.code.equals(code)) {
                return value.name;
            }
        }

        return StrUtil.EMPTY;
    }
}
