/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.module.notice.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.validation.Valid;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import cn.com.cloudstar.rightcloud.bss.common.annotation.AuthorizeBss;
import cn.com.cloudstar.rightcloud.bss.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.bss.common.util.DateUtil;
import cn.com.cloudstar.rightcloud.bss.module.notice.pojo.request.NoticePageListRequest;
import cn.com.cloudstar.rightcloud.bss.module.notice.pojo.request.UserNoticeInfoRequest;
import cn.com.cloudstar.rightcloud.bss.module.notice.pojo.response.NoticeDetailResponse;
import cn.com.cloudstar.rightcloud.bss.module.notice.pojo.response.NoticeInfosResponse;
import cn.com.cloudstar.rightcloud.bss.module.notice.pojo.response.NoticeListResponse;
import cn.com.cloudstar.rightcloud.bss.module.notice.service.ISysMNoticeService;
import cn.com.cloudstar.rightcloud.bss.module.notice.service.IUserNoticeInfoService;
import cn.com.cloudstar.rightcloud.common.constants.AuthModule;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.operationlog.annotation.OperationLog;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationResourceEnum;
import cn.com.cloudstar.rightcloud.operationlog.enums.OperationTypeEnum;

/**
 * 公告
 *
 * <AUTHOR>
 * @date 2020/8/18.
 */
@Api(value = "/notice", tags = {"公告"})
@RestController
@RequestMapping("/notice")
@Validated
public class NoticeController {

    @Autowired
    private ISysMNoticeService sysMNoticeService;

    @Autowired
    private IUserNoticeInfoService userNoticeInfoService;

    private  static  String  NOTICE_TYPE_NAME = "noticeTypeName";

    /**
     * 获取总览-公告
     *
     * @param noticePageRequest 公告列表分页查询请求体
     * @return {@code List<NoticeInfosResponse>}
     */
    @ApiOperation(httpMethod = "GET", value = "总览-公告信息")
    @AuthorizeBss(action = AuthModule.CA.CA)
    @GetMapping("infos")
    public List<NoticeInfosResponse> infos(@Validated NoticePageListRequest noticePageRequest) {

        List<NoticeInfosResponse> responseList = new ArrayList<>();

        noticePageRequest.setPublishEndDate(DateUtil.dateFormat(new Date(),"yyyy-MM-dd HH:mm:ss"));
        IPage<NoticeListResponse> noticeListResponseIPage = sysMNoticeService.pageQuery(noticePageRequest);
        List<NoticeListResponse> records = noticeListResponseIPage.getRecords();
        if(!CollectionUtils.isEmpty(records)){
            records.stream().forEach(record->{
                NoticeInfosResponse infosResponse = new NoticeInfosResponse();
                infosResponse.setNoticeId(record.getNoticeId());
                infosResponse.setNoticeTitle("【"+(WebUtil.getHeaderAcceptLanguage() ? record.getNoticeTypeNameUs() : record.getNoticeTypeName())+"】"+record.getNoticeTitle());
                infosResponse.setPublishDate(record.getPublishDt());
                responseList.add(infosResponse);
            });
        }
        return responseList;
    }

    /**
     * 获取需要弹出的公告
     * 【Since v2.5.0】
     */
    @AuthorizeBss(action = AuthModule.CA.CA)
    @ApiOperation(httpMethod = "GET", value = "总览-公告信息弹出")
    @GetMapping("/infos/eject")
    public List<NoticeListResponse> infosForEject() {
        return sysMNoticeService.selectList();
    }

    /**
     * 修改公告弹出状态
     * 【Since v2.5.0】
     */
    @AuthorizeBss(action = AuthModule.CA.CA)
    @ApiOperation(httpMethod = "PUT", value = "总览-修改公告弹出状态")
    @PutMapping("/update")
    @OperationLog(type = OperationTypeEnum.UPDATE, tagName = "'修改公告弹出状态'", bizId = "#request.noticeIds", resource = OperationResourceEnum.UPDATE_NOTICE_STATUS_FOR, tagNameUs ="'Modify announcement pop-up status'")
    public RestResult updateUserNoticeInfo(@RequestBody  @Valid UserNoticeInfoRequest request) {
        Boolean aBoolean = userNoticeInfoService.updateUserNoticeInfo(request);
        if (!aBoolean){
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERROR_OPERATE_FAILURE));
        }
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS));
    }

    /**
     * 获取公告详情
     *
     * @param noticeId 公告id
     * @return {@code NoticeDetailResponse}
     */
    @AuthorizeBss(action = AuthModule.CF.CF05)
    @ApiOperation(httpMethod = "GET", value = "公告详情")
    @GetMapping("detail/{id}")
    public NoticeDetailResponse detail(@PathVariable("id") Long noticeId) {
        return sysMNoticeService.selectDetail(noticeId);
    }


    /**
     * 获取公告列表
     *
     * @param noticePageRequest 公告列表分页查询请求体
     * @return {@code IPage<NoticeListResponse>}
     */
    @AuthorizeBss(action = AuthModule.CF.CF08)
    @ApiOperation(httpMethod = "GET", value = "公告列表")
    @GetMapping("list")
    public IPage<NoticeListResponse> list(@Validated NoticePageListRequest noticePageRequest) {

        String sortdatafield = noticePageRequest.getSortdatafield();
        if(StringUtils.isNotEmpty(sortdatafield) && NOTICE_TYPE_NAME.equals(sortdatafield)){
            noticePageRequest.setSortdatafield("noticeTypeId");
        }

        return sysMNoticeService.pageQuery(noticePageRequest);
    }

}
