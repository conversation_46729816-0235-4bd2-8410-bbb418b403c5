/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.common.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

import lombok.Data;

import cn.com.cloudstar.rightcloud.common.encryptdata.annotation.CCSPEncryptDecrypt;
import cn.com.cloudstar.rightcloud.common.encryptdata.annotation.CCSPIntegralityHashAndVerify;
import cn.com.cloudstar.rightcloud.common.encryptdata.annotation.CCSPProcessVerifyClass;
import cn.com.cloudstar.rightcloud.common.encryptdata.annotation.EncryptDecryptClass;
import cn.com.cloudstar.rightcloud.common.encryptdata.annotation.EncryptDecryptField;


/**
 * The type Auth user.
 */
@Data
@TableName("sys_m_user")
@CCSPProcessVerifyClass(needCCSPEncryptDecrypt = true)
@EncryptDecryptClass
public class AuthUser implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户SID
     */
    @TableId(value = "user_sid", type = IdType.AUTO)
    private Long userSid;

    /**
     * 账号
     */
    private String account;

    /**
     * 用户类型
     */
    private String userType;

    /**
     * 部门ID
     */
    private Long orgSid;

    /**
     * UUID
     */
    private String uuid;

    /**
     * 用户真实姓名
     */
    @CCSPIntegralityHashAndVerify(segment = 5)
    @CCSPEncryptDecrypt
    @EncryptDecryptField
    private String realName;

    /**
     * 性别 0:男 1:女
     */
    private Integer sex;

    /**
     * 短信限制
     */
    private Integer smsMax;
    /**
     * 已用短信条数
     */
    @TableField(exist = false)
    private Integer smsUsed;

    /**
     * 短信限制
     */
    @TableField(exist = false)
    private Integer smsRemain;

    /**
     * 电子邮件地址
     */
    @CCSPIntegralityHashAndVerify(segment = 1)
    @CCSPEncryptDecrypt
    private String email;

    /**
     * 备注
     */
    private String remark;

    /**
     * 手机
     */
    @CCSPIntegralityHashAndVerify(segment = 2)
    @CCSPEncryptDecrypt
    @EncryptDecryptField
    private String mobile;

    /**
     * 当前请求Ip
     */
    @TableField(exist = false)
    private String currentRequestIp;

    /**
     * 租户SID
     */
    @TableField(exist = false)
    private Long mgtObjSid;

    /**
     * 用户状态
     */
    private String status;

    /**
     * 角色
     */
    @TableField(exist = false)
    private String roles;

    /**
     * 权限
     */
    @TableField(exist = false)
    private String permissions;

    /**
     * 企业ID
     */
    private Long companyId;

    /**
     * 微信openid
     */
    private String openId;

    private String avatarUrl;
    @TableField(exist = false)
    private Boolean wxLogin;

    private String wechatName;

    @CCSPIntegralityHashAndVerify(segment = 7)
    private String city;

    @CCSPIntegralityHashAndVerify(segment = 8)
    private String country;

    @CCSPIntegralityHashAndVerify(segment = 6)
    private String province;

    /**
     * 项目id
     *
     * @return
     */
    @TableField(exist = false)
    private String userMgtObj;

    /**
     * 登录时间(毫秒)
     */
    @TableField(exist = false)
    private Long loginTimeMills;

    /**
     * jwttoken到期时间
     */
    @TableField(exist = false)
    private Long jwtExpiredMills;

    /**
     * jwt uuid
     */
    @TableField(exist = false)
    private String jwtUuid;

    /**
     * 系统管理员flag
     */
    @TableField(exist = false)
    private Boolean adminFlag;
    /**
     * licenceExpire 时间过期
     */
    @TableField(exist = false)
    private Boolean licenceExpire;
    /**
     * 企业ID
     */
    private Long parentSid;

    /**
     * 密码
     */
    @CCSPIntegralityHashAndVerify(segment = 10)
    private String password;

    /**
     * 第三方认证绑定ID
     */
    @CCSPIntegralityHashAndVerify(segment = 9)
    @CCSPEncryptDecrypt
    @EncryptDecryptField
    private String authId;

    @CCSPIntegralityHashAndVerify(segment = 3)
    private String idCardFront;//身份证正面图片路径

    @CCSPIntegralityHashAndVerify(segment = 4)
    private String idCardReverse;//身份证反面图片路径

    private String ccspMac = "";


    /**
     * 标签
     */
    private String businessTag;
}
