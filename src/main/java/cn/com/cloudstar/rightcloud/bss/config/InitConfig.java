/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigInteger;

import javax.annotation.PostConstruct;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.bss.common.constants.AuthConstants;
import cn.com.cloudstar.rightcloud.bss.common.constants.SysConfigConstants;
import cn.com.cloudstar.rightcloud.bss.module.sys.mapper.ConfigMapper;

/**
 * The type InitConfig.
 *
 * Created on 2019/10/16
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class InitConfig {

    @Autowired
    private ConfigMapper configMapper;

    @PostConstruct
    public void initConfig() {
        String value = configMapper.selectConfigValue(SysConfigConstants.SESSION_EXPIRE_TIME);
        if (StrUtil.isNotEmpty(value)) {
            AuthConstants.TTL_MILLIS = new BigInteger(value).multiply(BigInteger.valueOf(60000)).longValue();
            log.info("User Token Expired time: {}", AuthConstants.TTL_MILLIS);
        }
    }
}
