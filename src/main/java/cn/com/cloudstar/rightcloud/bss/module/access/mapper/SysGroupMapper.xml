<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.cloudstar.rightcloud.bss.module.access.mapper.SysGroupMapper">
    <resultMap id="BaseResultMap" type="cn.com.cloudstar.rightcloud.bss.module.access.bean.SysGroup">
        <id column="group_sid" property="groupSid" jdbcType="BIGINT"/>
        <result column="group_name" property="groupName" jdbcType="VARCHAR"/>
        <result column="group_name_us" property="groupNameUs" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="description_us" property="descriptionUs" jdbcType="VARCHAR"/>
        <result column="tree_path" property="treePath" jdbcType="VARCHAR"/>
        <result column="parent_id" property="parentId" jdbcType="BIGINT"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="owner_id" property="ownerId" jdbcType="BIGINT"/>
        <result column="org_sid" property="orgSid" jdbcType="BIGINT"/>
        <result column="created_dt" property="createdDt" jdbcType="TIMESTAMP"/>
        <result column="updated_by" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="updated_dt" property="updatedDt" jdbcType="TIMESTAMP"/>
        <result column="version" property="version" jdbcType="INTEGER"/>
        <result column="user_num" property="userNum" jdbcType="INTEGER"/>
        <result column="parent_name" property="parentName" jdbcType="VARCHAR"/>
        <result column="groupUsers" property="groupUsers" jdbcType="VARCHAR" />
        <result column="groupPolicys" property="groupPolicys" jdbcType="VARCHAR"/>
        <result column="sys_default" property="sysDefault" jdbcType="BIT"/>
        <result column="ccsp_mac" property="ccspMac" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.groupName != null">
                and group_name = #{condition.groupName}
            </if>
            <if test="condition.description != null">
                and description = #{condition.description}
            </if>
            <if test="condition.treePath != null">
                and tree_path = #{condition.treePath}
            </if>
            <if test="condition.parentId != null">
                and parent_id = #{condition.parentId}
            </if>
            <if test="condition.createdBy != null">
                and created_by = #{condition.createdBy}
            </if>
            <if test="condition.ownerId != null">
                and owner_id = #{condition.ownerId}
            </if>
            <if test="condition.orgSid != null">
                and org_sid = #{condition.orgSid}
            </if>
            <if test="condition.createdDt != null">
                and created_dt = #{condition.createdDt}
            </if>
            <if test="condition.updatedBy != null">
                and updated_by = #{condition.updatedBy}
            </if>
            <if test="condition.updatedDt != null">
                and updated_dt = #{condition.updatedDt}
            </if>
            <if test="condition.version != null">
                and version = #{condition.version}
            </if>
            <if test="condition.parentSidNotNull != null">
                and A.parent_sid is null
            </if>
        </trim>
    </sql>
    <sql id="Example_Where_Clause_1">
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.groupName != null">
                and A.group_name = #{condition.groupName}
            </if>
            <if test="condition.groupSids != null">
                and A.group_sid in
                    <foreach collection="condition.groupSids" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
            </if>
            <if test="condition.description != null">
                and A.description = #{condition.description}
            </if>
            <if test="condition.treePath != null">
                and A.tree_path = #{condition.treePath}
            </if>
            <if test="condition.parentId != null">
                and A.parent_id = #{condition.parentId}
            </if>
            <if test="condition.createdBy != null">
                and A.created_by = #{condition.createdBy}
            </if>
            <if test="condition.ownerId != null">
                and A.owner_id = #{condition.ownerId}
            </if>
            <if test="condition.orgSid != null">
                and A.org_sid = #{condition.orgSid}
            </if>
            <if test="condition.createdDt != null">
                and A.created_dt = #{condition.createdDt}
            </if>
            <if test="condition.updatedBy != null">
                and A.updated_by = #{condition.updatedBy}
            </if>
            <if test="condition.updatedDt != null">
                and A.updated_dt = #{condition.updatedDt}
            </if>
            <if test="condition.version != null">
                and A.version = #{condition.version}
            </if>
            <if test="condition.parentSidNotNull == true">
                and A.parent_id is null
            </if>
            <if test="condition.sysDefault == true">
                or A.sys_default = 1
            </if>
            <if test="condition.groupNameLike != null">
                and A.group_name like concat ('%', #{condition.groupNameLike} ,'%')
            </if>
            <if test="condition.includeDefaultGroup == true">
                and (A.org_sid = #{condition.org} or A.sys_default = 1)
            </if>
        </trim>
    </sql>
    <sql id="Base_Column_List">
    A.group_sid, A.group_name, A.description, A.tree_path, A.parent_id, A.created_by, A.owner_id, A.org_sid,
    A.created_dt, A.updated_by, A.updated_dt, A.version, A.sys_default,
    A.ccsp_mac,
    CASE
        WHEN A.group_sid != - 1 THEN
            ( SELECT GROUP_CONCAT( u.user_sid ORDER BY u.created_dt) FROM sys_m_user_group u WHERE u.group_sid = A.group_sid ) ELSE NULL
    END groupUsers,
    CASE
        WHEN A.group_sid != - 1 THEN
            ( SELECT GROUP_CONCAT( p.policy_sid ORDER BY p.created_dt) FROM sys_m_policy_group p WHERE p.group_sid = A.group_sid ) ELSE NULL
    END groupPolicys
  </sql>
    <sql id="Base_Column_List_1">
    A.group_sid, A.group_name,A.group_name_us, A.description, A.description_us, A.tree_path, A.parent_id, A.created_by, A.owner_id, A.org_sid,
    A.created_dt, A.updated_by, A.updated_dt, A.version, ifnull(A.sys_default, 0) sys_default,
    A.ccsp_mac,
    CASE
        WHEN A.group_sid != - 1 THEN
            ( SELECT GROUP_CONCAT( u.user_sid ORDER BY u.created_dt) FROM sys_m_user_group u WHERE u.group_sid = A.group_sid ) ELSE NULL
        END groupUsers,
    CASE
        WHEN A.group_sid != - 1 THEN
            ( SELECT GROUP_CONCAT( p.policy_sid ORDER BY p.created_dt) FROM sys_m_policy_group p WHERE p.group_sid = A.group_sid ) ELSE NULL
        END groupPolicys
    </sql>
    <select id="selectByParams" resultMap="BaseResultMap" parameterType="cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List_1"/>
        , B.group_name as parent_name
        from sys_m_group A
        left join sys_m_group B on A.parent_id = B.group_sid
        <if test="_parameter != null">
            <include refid="Example_Where_Clause_1"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List_1"/>
        ,B.group_name as parent_name
        from sys_m_group A
        left join sys_m_group B on A.parent_id = B.group_sid
        where A.group_sid = #{groupSid}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from sys_m_group
    where group_sid = #{groupSid}
  </delete>
    <delete id="deleteByParams" parameterType="cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria">
        delete from sys_m_group
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="cn.com.cloudstar.rightcloud.bss.module.access.bean.SysGroup">
    insert into sys_m_group (group_sid, group_name, description, tree_path, parent_id, created_by, 
      owner_id, org_sid, created_dt, updated_by, updated_dt, version)
    values (#{groupSid}, #{groupName}, #{description}, #{treePath}, #{parentId}, #{createdBy}, 
      #{ownerId}, #{orgSid}, #{createdDt}, #{updatedBy}, #{updatedDt}, #{version})
  </insert>
    <insert id="insertSelective" parameterType="cn.com.cloudstar.rightcloud.bss.module.access.bean.SysGroup" useGeneratedKeys="true" keyProperty="groupSid">
        insert into sys_m_group
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="groupSid != null">
                group_sid,
            </if>
            <if test="groupName != null">
                group_name,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="treePath != null">
                tree_path,
            </if>
            <if test="parentId != null">
                parent_id,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="ownerId != null">
                owner_id,
            </if>
            <if test="orgSid != null">
                org_sid,
            </if>
            <if test="createdDt != null">
                created_dt,
            </if>
            <if test="updatedBy != null">
                updated_by,
            </if>
            <if test="updatedDt != null">
                updated_dt,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="sysDefault != null">
                sys_default,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="groupSid != null">
                #{groupSid},
            </if>
            <if test="groupName != null">
                #{groupName},
            </if>
            <if test="description != null">
                #{description},
            </if>
            <if test="treePath != null">
                #{treePath},
            </if>
            <if test="parentId != null">
                #{parentId},
            </if>
            <if test="createdBy != null">
                #{createdBy},
            </if>
            <if test="ownerId != null">
                #{ownerId},
            </if>
            <if test="orgSid != null">
                #{orgSid},
            </if>
            <if test="createdDt != null">
                #{createdDt},
            </if>
            <if test="updatedBy != null">
                #{updatedBy},
            </if>
            <if test="updatedDt != null">
                #{updatedDt},
            </if>
            <if test="version != null">
                #{version},
            </if>
            <if test="sysDefault != null">
                #{sysDefault},
            </if>
        </trim>
    </insert>
    <select id="countByParams" parameterType="cn.com.cloudstar.rightcloud.bss.common.pojo.Criteria" resultType="java.lang.Integer">
        select count(*) from sys_m_group
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByParamsSelective" parameterType="map">
        update sys_m_group
        <set>
            <if test="record.groupSid != null">
                group_sid = #{record.groupSid},
            </if>
            <if test="record.groupName != null">
                group_name = #{record.groupName},
            </if>
            <if test="record.description != null">
                description = #{record.description},
            </if>
            <if test="record.treePath != null">
                tree_path = #{record.treePath},
            </if>
            <if test="record.parentId != null">
                parent_id = #{record.parentId},
            </if>
            <if test="record.createdBy != null">
                created_by = #{record.createdBy},
            </if>
            <if test="record.ownerId != null">
                owner_id = #{record.ownerId},
            </if>
            <if test="record.orgSid != null">
                org_sid = #{record.orgSid},
            </if>
            <if test="record.createdDt != null">
                created_dt = #{record.createdDt},
            </if>
            <if test="record.updatedBy != null">
                updated_by = #{record.updatedBy},
            </if>
            <if test="record.updatedDt != null">
                updated_dt = #{record.updatedDt},
            </if>
            <if test="record.version != null">
                version = #{record.version},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByParams" parameterType="map">
        update sys_m_group
        set group_sid = #{record.groupSid},
        group_name = #{record.groupName},
        description = #{record.description},
        tree_path = #{record.treePath},
        parent_id = #{record.parentId},
        created_by = #{record.createdBy},
        owner_id = #{record.ownerId},
        org_sid = #{record.orgSid},
        created_dt = #{record.createdDt},
        updated_by = #{record.updatedBy},
        updated_dt = #{record.updatedDt},
        version = #{record.version}
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="cn.com.cloudstar.rightcloud.bss.module.access.bean.SysGroup">
        update sys_m_group
        <set>
            <if test="groupName != null">
                group_name = #{groupName},
            </if>
            <if test="description != null">
                description = #{description},
            </if>
            <if test="treePath != null">
                tree_path = #{treePath},
            </if>
            <if test="parentId != null">
                parent_id = #{parentId},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy},
            </if>
            <if test="ownerId != null">
                owner_id = #{ownerId},
            </if>
            <if test="orgSid != null">
                org_sid = #{orgSid},
            </if>
            <if test="createdDt != null">
                created_dt = #{createdDt},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy},
            </if>
            <if test="updatedDt != null">
                updated_dt = #{updatedDt},
            </if>
            <if test="version != null">
                version = #{version},
            </if>
            <if test="ccspMac != null">
                ccsp_mac = #{ccspMac},
            </if>
        </set>
        where group_sid = #{groupSid}
    </update>
    <update id="updateByPrimaryKey" parameterType="cn.com.cloudstar.rightcloud.bss.module.access.bean.SysGroup">
    update sys_m_group
    set group_name = #{groupName},
      description = #{description},
      tree_path = #{treePath},
      parent_id = #{parentId},
      created_by = #{createdBy},
      owner_id = #{ownerId},
      org_sid = #{orgSid},
      created_dt = #{createdDt},
      updated_by = #{updatedBy},
      updated_dt = #{updatedDt},
      ccsp_mac = #{ccspMac},
      version = #{version}
    where group_sid = #{groupSid}
  </update>

    <select id="selectByUserSid" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List_1"/>
        from sys_m_group A
        left join sys_m_user_group B on A.group_sid = B.group_sid
        where B.user_sid = #{userSid}
    </select>
    <select id="selectDefaultGroup" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"></include>
        from sys_m_group A where sys_default = 1;
    </select>
</mapper>