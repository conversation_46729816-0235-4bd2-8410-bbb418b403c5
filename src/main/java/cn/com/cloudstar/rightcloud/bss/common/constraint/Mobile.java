/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.bss.common.constraint;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import javax.validation.Constraint;
import javax.validation.Payload;

import cn.com.cloudstar.rightcloud.bss.common.constraint.validator.MobileConstraintValidator;

/**
 * The type Mobile.
 * <p> 手机号码约束
 *
 * <AUTHOR>
 * @date 2019/8/8
 */
@Target({ ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = MobileConstraintValidator.class)
public @interface Mobile {
    String message() default "请输入正确的手机号";

    Class<?>[] groups() default { };

    Class<? extends Payload>[] payload() default { };
}
