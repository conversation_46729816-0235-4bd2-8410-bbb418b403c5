package cn.com.cloudstar.rightcloud.module.standard.service;

import cn.com.cloudstar.rightcloud.module.standard.enums.CollectorTypeEnum;
import cn.com.cloudstar.rightcloud.module.standard.pojo.dto.CloudEnv;

import java.util.List;

public interface CloudEnvService {

    List<CloudEnv> findCloudEnvByProductType(CollectorTypeEnum collectorType);

    List<CloudEnv> findCloudEnvByEnvTypeToHCOS();

    CloudEnv findCloudEnvById(Long id, CollectorTypeEnum collectorType);
}
