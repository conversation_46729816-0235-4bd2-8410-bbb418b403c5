package cn.com.cloudstar.rightcloud.module.standard.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.mongodb.core.mapping.MongoId;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/5/17 16:59
 * @description
 */
@Data
@Document(collection = "tenant_resource_used_total_record")
public class TenantResourceUsedTotalRecord implements Serializable {
    private static  final  long serialVersionUID = 1L;

    @TableId(value="id",type = IdType.AUTO)
    @MongoId
    private  String  id;
    /**
     *集群标识
     */
    @Field("besuness_category")
    private String  besunessCategory;
    /**
     * 采集时间
     */
    @Field("update_time")
    private Date updateTime;
    /**
     *租户名称
     */
    @Field("tenant_name")
    private  String tenantName;
    /**
     * 租户累计使用CPU核时
     */
    @Field("cpu_core_ms")
    private Long cpuCoreMs;
    /**
     * 租户累计使用GPU卡时
     */
    @Field("gpu_card_ms")
    private Long gpuCardMs;
    /**
     * 总时
     */
    @Field("total_ms")
    private Double totalMs;
}
