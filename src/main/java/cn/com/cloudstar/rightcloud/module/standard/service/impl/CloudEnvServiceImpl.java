package cn.com.cloudstar.rightcloud.module.standard.service.impl;

import cn.com.cloudstar.rightcloud.common.util.CrytoUtilSimple;
import cn.com.cloudstar.rightcloud.module.standard.dao.CloudEnvMapper;
import cn.com.cloudstar.rightcloud.module.standard.enums.CollectorTypeEnum;
import cn.com.cloudstar.rightcloud.module.standard.pojo.dto.CloudEnv;
import cn.com.cloudstar.rightcloud.module.standard.service.CloudEnvService;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huaweicloud.sdk.core.utils.StringUtils;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 2023年9月19日修改
 * <AUTHOR>
 * @Date 2023-09-19
 */
@Service
public class CloudEnvServiceImpl implements CloudEnvService {

    private static final String COLLECTOR_TYPE = "collectorType";
    private static final String TYPE_HPC = "hpc";
    private static final String TYPE_HPC_OFFLINE = "hpc-offline";
    @Resource
    private CloudEnvMapper cloudEnvMapper;

    @Override
    public List<CloudEnv> findCloudEnvByProductType(CollectorTypeEnum collectorType) {
        List<CloudEnv> cloudEnvs = new ArrayList<>();
        QueryWrapper<CloudEnv> queryWrapper = new QueryWrapper<>();
        List<CloudEnv> list = cloudEnvMapper.selectList(queryWrapper);
        for (CloudEnv cloudEnv : list) {
            String aes = CrytoUtilSimple.decrypt(cloudEnv.getAttrData());
            JSONObject jsonObject = JSONObject.parseObject(aes);
            if (jsonObject.getString(COLLECTOR_TYPE) == null || !jsonObject.getString(COLLECTOR_TYPE)
                                                                           .equals(collectorType.getMessage())) {
                continue;
            }
            cloudEnv.setAk(jsonObject.getString("collectorKey"));
            cloudEnv.setSk(jsonObject.getString("collectorSecret"));
            if (StringUtils.isEmpty(cloudEnv.getAk())) {
                cloudEnv.setAk(jsonObject.getString("apiKey"));
                cloudEnv.setSk(jsonObject.getString("secureToken"));
            }
            cloudEnv.setEndPoint(jsonObject.getString("providerUrl"));
            if (collectorType.getMessage().equalsIgnoreCase(TYPE_HPC) || collectorType.getMessage()
                                                                                      .equalsIgnoreCase(
                                                                                              TYPE_HPC_OFFLINE)) {
                cloudEnv.setBucket(jsonObject.getString("hpcName"));
            } else {
                cloudEnv.setBucket(jsonObject.getString("hcsoName"));
            }
            cloudEnvs.add(cloudEnv);
        }
        return cloudEnvs;
    }

    @Override
    public List<CloudEnv> findCloudEnvByEnvTypeToHCOS() {
        QueryWrapper<CloudEnv> queryWrapper = new QueryWrapper<>();
        return cloudEnvMapper.selectList(queryWrapper);
    }

    @Override
    public CloudEnv findCloudEnvById(Long id, CollectorTypeEnum collectorType) {
        CloudEnv cloudEnv = cloudEnvMapper.selectById(id);
        String aes = CrytoUtilSimple.decrypt(cloudEnv.getAttrData());
        JSONObject jsonObject = JSONObject.parseObject(aes);
        if (jsonObject.getString(COLLECTOR_TYPE) == null || !jsonObject.getString(COLLECTOR_TYPE).contains(collectorType.getMessage())) {
            return null;
        }
        cloudEnv.setAk(jsonObject.getString("collectorKey"));
        cloudEnv.setSk(jsonObject.getString("collectorSecret"));
        if (StringUtils.isEmpty(cloudEnv.getAk())) {
            cloudEnv.setAk(jsonObject.getString("apiKey"));
            cloudEnv.setSk(jsonObject.getString("secureToken"));
        }
        cloudEnv.setEndPoint(jsonObject.getString("providerUrl"));
        if (collectorType.getMessage().equalsIgnoreCase(TYPE_HPC) || collectorType.getMessage()
                                                                                  .equalsIgnoreCase(
                                                                                          TYPE_HPC_OFFLINE)) {
            cloudEnv.setBucket(jsonObject.getString("hpcName"));
        } else {
            cloudEnv.setBucket(jsonObject.getString("hcsoName"));
        }
        return cloudEnv;
    }
}
