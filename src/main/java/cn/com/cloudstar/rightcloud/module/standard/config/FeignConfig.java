package cn.com.cloudstar.rightcloud.module.standard.config;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.ssl.SSLContextBuilder;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.openfeign.support.SpringDecoder;
import org.springframework.context.annotation.Bean;
import org.springframework.http.MediaType;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.TimeZone;

import javax.net.ssl.SSLContext;
import javax.servlet.http.HttpServletRequest;

import cn.hutool.core.util.StrUtil;
import feign.Client;
import feign.Feign;
import feign.Logger;
import feign.RequestInterceptor;
import feign.codec.Decoder;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.common.certificate.CustomizeTrustStrategy;
import cn.com.cloudstar.rightcloud.common.enums.ReqSource;
import cn.com.cloudstar.rightcloud.common.util.IPAddressUtil;
import cn.com.cloudstar.rightcloud.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.module.support.access.constants.AuthConstants;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.oss.common.constants.ModuleTypeConstants;
import cn.com.cloudstar.rightcloud.oss.common.util.RequestContextUtil;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class FeignConfig {
    @Bean
    public feign.Logger.Level level() {
        // 设置Feign的日志级别默认为BASIC，需要调试添加环境变量FEIGN_LOG_LEVEL=FULL
        String feignLogLevel = System.getenv("FEIGN_LOG_LEVEL");
        if (StrUtil.isEmpty(feignLogLevel)) {
            return feign.Logger.Level.BASIC;
        } else {
            return Logger.Level.valueOf(feignLogLevel.toUpperCase());
        }
    }

    @Bean
    @ConditionalOnProperty(name = "cloudstar.gateway.ssl.enable", havingValue = "true", matchIfMissing = false)
    public Client feignClient() {
        Client client;
        try {
            SSLContext context =
                    new SSLContextBuilder()
                            .loadTrustMaterial(null, CustomizeTrustStrategy.INSTANCE)
                            .build();
            client = new Client.Default(context.getSocketFactory(), NoopHostnameVerifier.INSTANCE);
        } catch (Exception e) {
            client = new Client.Default(null, NoopHostnameVerifier.INSTANCE);
        }
        return client;
    }

    @Bean
    public Feign.Builder feignBuilder(@Autowired Decoder feignDecoder) {
        return Feign.builder().decoder(feignDecoder);
    }


    @Bean
    public Decoder feignDecoder() {
        MappingJackson2HttpMessageConverter jackson2HttpMessageConverter = new MappingJackson2HttpMessageConverter();
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
        objectMapper.setTimeZone(TimeZone.getTimeZone("GMT+8"));
        jackson2HttpMessageConverter.setObjectMapper(objectMapper);

        StringHttpMessageConverter stringHttpMessageConverter = new StringHttpMessageConverter();
        stringHttpMessageConverter.setWriteAcceptCharset(false);

        List<MediaType> supportedMediaTypes = new ArrayList<>();
        supportedMediaTypes.add(MediaType.APPLICATION_JSON);
        supportedMediaTypes.add(MediaType.APPLICATION_JSON_UTF8);
        supportedMediaTypes.add(MediaType.APPLICATION_ATOM_XML);
        supportedMediaTypes.add(MediaType.APPLICATION_FORM_URLENCODED);
        supportedMediaTypes.add(MediaType.APPLICATION_OCTET_STREAM);
        supportedMediaTypes.add(MediaType.APPLICATION_PDF);
        supportedMediaTypes.add(MediaType.APPLICATION_RSS_XML);
        supportedMediaTypes.add(MediaType.APPLICATION_XHTML_XML);
        supportedMediaTypes.add(MediaType.APPLICATION_XML);
        supportedMediaTypes.add(MediaType.IMAGE_GIF);
        supportedMediaTypes.add(MediaType.IMAGE_JPEG);
        supportedMediaTypes.add(MediaType.IMAGE_PNG);
        supportedMediaTypes.add(MediaType.TEXT_EVENT_STREAM);
        supportedMediaTypes.add(MediaType.TEXT_HTML);
        supportedMediaTypes.add(MediaType.TEXT_MARKDOWN);
        supportedMediaTypes.add(MediaType.TEXT_PLAIN);
        supportedMediaTypes.add(MediaType.TEXT_XML);
        jackson2HttpMessageConverter.setSupportedMediaTypes(supportedMediaTypes);

        ObjectFactory<HttpMessageConverters> objectFactory = () -> new HttpMessageConverters(jackson2HttpMessageConverter, stringHttpMessageConverter);
        return new SpringDecoder(objectFactory);
    }

    @Bean
    public RequestInterceptor requestInterceptor() {
        return requestTemplate -> {
            log.info("开始调用 {}", requestTemplate.url());
//            AuthUser authUser = AuthUserHolder.getAuthUser();
            AuthUser authUser = RequestContextUtil.getAuthUserInfo();

            requestTemplate.header("Accept", "application/json");
            if (authUser != null) {
                requestTemplate.header(AuthConstants.HEADER_USER, String.valueOf(authUser.getUserSid()));
            }
            requestTemplate.header(AuthConstants.HEADER_REQ_SOURCE, ReqSource.CLOUD_BOSS.name());
            requestTemplate.header(AuthConstants.HEADER_ORG, (authUser == null || authUser.getOrgSid() == null) ? "0"
                    : String.valueOf(authUser.getOrgSid()));
            requestTemplate.header(AuthConstants.ENTITY_ID, (authUser == null || authUser.getEntityId() == null) ? "0"
                    : String.valueOf(authUser.getEntityId()));
            try {
                ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
                HttpServletRequest request = requestAttributes.getRequest();
                requestTemplate.header(AuthConstants.HEADER_MODULE_TYPE, request.getHeader(AuthConstants.HEADER_MODULE_TYPE));
                String ip=request.getHeader("RequstIp");
                if(StringUtil.isEmpty(ip)){
                    ip= IPAddressUtil.getRemoteHostIp(request);
                }
                requestTemplate.header("RequstIp",ip);
                requestTemplate.header("RequstType",AuthConstants.HEADER_TAG);
            } catch (Exception e) {
                requestTemplate.header(AuthConstants.HEADER_MODULE_TYPE, ModuleTypeConstants.FROM_COMMON);
                requestTemplate.header("RequstType",AuthConstants.HEADER_TAG);
                log.info("OSS Feign 异常 [{}] ", e.getMessage());
            }


        };
    }


}
