package cn.com.cloudstar.rightcloud.module.standard.service;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import cn.com.cloudstar.rightcloud.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.module.standard.config.FeignConfig;

/**
 * <AUTHOR>
 * Created on 2022/4/25
 */
@FeignClient(name = "https://cmp-resource:38180", configuration = FeignConfig.class,path = "/api/v1/resource")
public interface HpcClusterService {

    /**
     * 获取资源池信息
     * @return
     */
    @GetMapping("/hpc_cluster/name")
    RestResult getResHpcCluster();
}
