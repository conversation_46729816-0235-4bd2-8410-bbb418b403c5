package cn.com.cloudstar.rightcloud.module.standard.feignCilent;

import cn.com.cloudstar.rightcloud.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.module.standard.config.FeignConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.Map;

/**
 * 用户帐户服务
 *
 * <AUTHOR>
 * @date 2023/04/11
 */
@FeignClient(value = "https://cmp-bss:8082", configuration = FeignConfig.class, path = "/api/v1/bss")
//@FeignClient(name = "127.0.0.1:8082", configuration = FeignConfig.class, path = "/api/v1/bss")
public interface BssFeignClientService {

    /**
     * 获取资源池信息
     *
     * @return
     */
    @GetMapping("/billing_account/get_collector_info")
    RestResult getCollectorInfo();
}
