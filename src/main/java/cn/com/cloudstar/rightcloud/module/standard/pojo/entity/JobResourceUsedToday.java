package cn.com.cloudstar.rightcloud.module.standard.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.mongodb.core.mapping.MongoId;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 当日资源消耗数据表top5
 * <AUTHOR>
 * @date 2022/4/20 13:32
 * @description
 */
@Data
@Document(collection = "original_view_hpcop_job_resource_used_today")
@CompoundIndex(name = "total_ms_1",def = "{'total_ms':-1}")
public class JobResourceUsedToday implements Serializable {
    private static  final  long serialVersionUID = 1L;

    @TableId(value="id",type = IdType.AUTO)
    @MongoId
    private  String  id;
    /**
     *真实名称
     */
    @Field("real_name")
    private String  realName;
    /**
     *作业ID
     */
    @Field("job_id")
    private  String  jobId;
    /**
     * 更新时间
     */
    @Field("update_time")
    private Date updateTime;
    /**
     *作业名称
     */
    @Field("job_name")
    private  String  jobName;
    /**
     *用户名称
     */
    @Field("user_name")
    private  String  userName;
    /**
     *租户名称
     */
    @Field("tenant_name")
    private  String  tenantName;
    /**
     *集群名称
     */
    @Field("cluster_name")
    private  String clusterName ;
    /**
     *作业当日运行时长
     */
    @Field("wall_time")
    private BigDecimal wallTime;
    /**
     *作业当日使用CPU核时（单位：核·毫秒）
     */
    @Field("cpu_core_ms")
    private  BigDecimal  cpuCoreMs;
    /**
     *作业当日使用GPU卡时（单位：卡·毫秒）
     */
    @Field("gpu_card_ms")
    private  BigDecimal  gpuCardMs;

    /**
     * *权重后的值
     */
    @Field("total_ms")
    private  BigDecimal  totalMs;
}
