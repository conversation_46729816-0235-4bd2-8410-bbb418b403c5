package cn.com.cloudstar.rightcloud.module.standard.service.impl;

import cn.com.cloudstar.rightcloud.module.standard.config.DBContextHolder;
import cn.com.cloudstar.rightcloud.module.standard.config.DynamicDataSource;
import cn.com.cloudstar.rightcloud.module.standard.dao.BigScreenHourMapper;
import cn.com.cloudstar.rightcloud.module.standard.dao.BigScreenMapper;
import cn.com.cloudstar.rightcloud.module.standard.enums.HpcPowerEnum;
import cn.com.cloudstar.rightcloud.module.standard.pojo.entity.*;
import cn.com.cloudstar.rightcloud.module.standard.service.CleanAndColleteService;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;


/**
 * <AUTHOR>
 * @date 2022/5/10 16:11
 * @description
 */
@Service
@Slf4j
public class CleanAndColletServiceImpl implements CleanAndColleteService {

    public static final String TIME_STAMP_PATTERN = "yyyyMMddHHmmss";
    public static final String YYYY_MM_DD = "yyyyMMdd";
    public static final String  METRIC_15DAY= "metric_15day_center_cf_apex";
    public static final String  METRIC_24HOUR = "metric_24hour_tenant_cf_apex";
    public static final String HH = "HH";
    @Autowired
    MongoTemplate mongoTemplate;
    @Autowired
    BigScreenMapper bigScreenMapper;
    @Autowired
    BigScreenHourMapper bigScreenHourMapper;
    @Autowired
    DynamicDataSource dynamicDataSource;

    //补采十五天算力
    @Override
    public void collect1() {
        DateTime beginTime = new DateTime();
        List<Ccpdbinfo> dataSource = getDataSource();
        HpcConfigValues values = getValues();
        for (Ccpdbinfo info : dataSource) {
            try {
                //找出当前资源池里当天的数据
                Query query = Query.query(Criteria.where("sample_time").gt(getHoursAgo())
                        .and("category").is(METRIC_15DAY).and("besuness_category").is(info.getBesunessCategory()));
                List<CcpCollectionRecord> list = mongoTemplate.find(query, CcpCollectionRecord.class);
                //查询第几小时没有采集到
                List different = findDifferent(list);
                if (different.size()==0){
                    continue;
                }
                log.info("需要使用的的数据源已经找到,集群是:" + info.getBesunessCategory());
                //创建数据源连接&检查 若存在则不需重新创建
                boolean flag = dynamicDataSource.createDataSourceWithCheck(info);
                if (!flag){
                    continue;
                }
                //切换到该数据源
                DBContextHolder.setDataSource(info.getBesunessCategory());
                //补采数据
                different.forEach(d-> {
                    Date begin = getDay(Integer.parseInt(d.toString()));
                    Date end = getDay(Integer.parseInt(d.toString()) - 1);
                    List<SampleTenantInfo> infos = bigScreenMapper.selectSampleTenantInfos(begin, end);
                    log.info("在{}-补采租户15天算力，采集到{}条", DateUtil.format(new Date(), "yyyyMMddHH"), infos.size());
                    if(infos.size()!=0){
                        List<MetricDayTenantCfApex> days = new LinkedList<>();
                        for (SampleTenantInfo tenantInfo : infos) {
                            MetricDayTenantCfApex day = new MetricDayTenantCfApex();
                            Long cpu1Count = 0l;
                            Long cpu2Count = 0l;
                            Long gpuCount = 0l;
                            if (tenantInfo.getClusterName().equals(values.getFirstName())) {
                                cpu1Count = tenantInfo.getCpuUsed();
                                gpuCount = tenantInfo.getGpuUsed();
                            } else{
                                cpu2Count = tenantInfo.getCpuUsed();
                                gpuCount = tenantInfo.getGpuUsed();
                            }
                            BigDecimal cpu1Cf = BigDecimal.valueOf(cpu1Count * HpcPowerEnum.DAYCPU1.getValues())
                                    .setScale(6, BigDecimal.ROUND_HALF_UP);
                            BigDecimal cpu2Cf = BigDecimal.valueOf(cpu2Count * HpcPowerEnum.ADYCPU2.getValues())
                                    .setScale(6, BigDecimal.ROUND_HALF_UP);
                            BigDecimal gpuCf = BigDecimal.valueOf(tenantInfo.getGpuUsed() * HpcPowerEnum.DAYGPU.getValues())
                                    .setScale(6, BigDecimal.ROUND_HALF_UP);
                            BigDecimal totalCf = cpu1Cf.add(cpu2Cf).add(gpuCf);
                            day.setSampleTime(getHoursAgos(tenantInfo.getSampleTime()));
                            day.setCpu1Count(cpu1Count);
                            day.setCpu1Cf(cpu1Cf);
                            day.setCpu2Count(cpu2Count);
                            day.setCpu2Cf(cpu2Cf);
                            day.setGpuCount(gpuCount);
                            day.setGpuCf(gpuCf);
                            day.setTotalCf(totalCf);
                            SimpleDateFormat format = new SimpleDateFormat(YYYY_MM_DD);
                            day.setDay(Long.parseLong(format.format(tenantInfo.getSampleTime())));
                            days.add(day) ;
                        }
                        //插入MongoDB
                        mongoTemplate.insert(days, MetricDayTenantCfApex.class);
                        log.info("补采租户15天算力成功,采集到{}条",days.size());
                        //插记录
                        Date date = infos.stream().map(SampleTenantInfo::getSampleTime)
                                .max(Date::compareTo).get();
                        CcpCollectionRecord record = new CcpCollectionRecord();
                        record.setBesunessCategory(info.getBesunessCategory());
                        record.setCategory(METRIC_15DAY);
                        record.setCollectCount(list.size());
                        record.setBeginTime(beginTime);
                        record.setEndTime(new Date());
                        record.setSampleTime(date);
                        SimpleDateFormat format = new SimpleDateFormat(HH);
                        int i = Integer.parseInt(format.format(date));
                        record.setFlag(i+1);
                        mongoTemplate.insert(record);
                    }
                });
                DBContextHolder.clearDataSource();
            } catch (Exception e) {
                e.printStackTrace();
                log.info("{}链接失败",info.getDbAddress());
                continue;
            }
        }
    }

    @Override
    public void collect2() {
        Date beginTime = new Date();
        List<Ccpdbinfo> dataSource = getDataSource();
        HpcConfigValues values = getValues();
        //查询org名称
        List<Tenant> tenants = bigScreenMapper.selectTenantAll();
        for (Ccpdbinfo info : dataSource) {
            try {
                Query query = Query.query(Criteria.where("sample_time").gt(getHoursAgo())
                        .and("category").is(METRIC_24HOUR).and("besuness_category").is(info.getBesunessCategory()));
                List<CcpCollectionRecord> list = mongoTemplate.find(query, CcpCollectionRecord.class);
                List different = findDifferent(list);
                if (different.size()==0){
                    continue;
                }
                log.info("需要使用的的数据源已经找到,集群是:" + info.getBesunessCategory());
                //创建数据源连接&检查 若存在则不需重新创建
                boolean flag = dynamicDataSource.createDataSourceWithCheck(info);
                if (!flag){
                    continue;
                }
                //切换到该数据源
                DBContextHolder.setDataSource(info.getBesunessCategory());
                //补采数据
                different.forEach(d-> {
                    Date begin = getDay(Integer.parseInt(d.toString())+1);
                    Date end = getDay(Integer.parseInt(d.toString()));
                    List<SampleTenantInfo> infos = bigScreenMapper.selectSampleTenantInfos(begin, end);
                    log.info("在{}-补采租户24小时算力，采集到{}条", DateUtil.format(new Date(), "yyyyMMddHH"), infos.size());
                    if(infos.size()!=0){
                        List<MetricHourTenantCfApex> hours = new LinkedList<>();
                        Long cpu1Count = 0l;
                        Long cpu2Count = 0l;
                        BigDecimal cpu1Cf;
                        BigDecimal cpu2Cf;
                        BigDecimal gpuCf;
                        BigDecimal totalCf;
                        for (SampleTenantInfo tenantInfo : infos) {
                            MetricHourTenantCfApex tenant = new MetricHourTenantCfApex();
                            if (tenantInfo.getClusterName().equals(values.getFirstName())) {
                                cpu1Count = tenantInfo.getCpuUsed();
                            } else{
                                cpu2Count = tenantInfo.getCpuUsed();
                            }
                            for (Tenant tenant1 : tenants) {
                                if (tenantInfo.getTenantName().equals(tenant1.getLdapOu())){
                                    tenant.setRealName(tenant1.getOrgName());
                                    tenant.setOrgSid(tenant1.getOrgSid());
                                }
                            }
                            cpu1Cf = BigDecimal.valueOf(cpu1Count).multiply(BigDecimal.valueOf(HpcPowerEnum.CPU1.getValues()));
                            cpu2Cf = BigDecimal.valueOf(cpu2Count).multiply(BigDecimal.valueOf(HpcPowerEnum.CPU2.getValues()));
                            gpuCf = BigDecimal.valueOf(tenantInfo.getGpuUsed()).multiply(BigDecimal.valueOf(HpcPowerEnum.GPU.getValues()));
                            totalCf = cpu1Cf.add(cpu2Cf).add(gpuCf);
                            tenant.setSampleTime(getHoursAgos(tenantInfo.getSampleTime()));
                            tenant.setTenantName(tenantInfo.getTenantName());
                            tenant.setCpu1Cf(cpu1Cf.divide(BigDecimal.valueOf(3600000), 4, BigDecimal.ROUND_HALF_UP));
                            tenant.setCpu1Count(cpu1Count);
                            tenant.setCpu2Count(cpu2Count);
                            tenant.setCpu2Cf(cpu2Cf.divide(BigDecimal.valueOf(3600000), 4, BigDecimal.ROUND_HALF_UP));
                            tenant.setGpuCount(tenantInfo.getGpuUsed());
                            tenant.setGpuCf(gpuCf.divide(BigDecimal.valueOf(3600000), 4, BigDecimal.ROUND_HALF_UP));
                            tenant.setTotalCf(totalCf.divide(BigDecimal.valueOf(3600000), 4, BigDecimal.ROUND_HALF_UP));
                            hours.add(tenant);
                        }
                        //插入MongoDB
                        mongoTemplate.insert(hours, MetricHourTenantCfApex.class);
                        log.info("补采租户24小时算力成功,采集到{}条",hours.size());
                        //修复记录
                        Date date = infos.stream().map(SampleTenantInfo::getSampleTime)
                                .max(Date::compareTo).get();
                        CcpCollectionRecord record = new CcpCollectionRecord();
                        record.setBesunessCategory(info.getBesunessCategory());
                        record.setCategory(METRIC_24HOUR);
                        record.setCollectCount(list.size());
                        record.setBeginTime(beginTime);
                        record.setEndTime(new DateTime());
                        record.setSampleTime(date);
                        SimpleDateFormat format = new SimpleDateFormat(HH);
                        int i = Integer.parseInt(format.format(date));
                        record.setFlag(i+1);
                        mongoTemplate.insert(record);
                    }
                });
                DBContextHolder.clearDataSource();
            } catch (Exception e) {
                e.printStackTrace();
                log.info("{}链接失败",info.getDbAddress());
                continue;
            }
        }

    }

    public List findDifferent(List<CcpCollectionRecord> list){
        int[] a=new int[list.size()];
        int i=0;
        for (CcpCollectionRecord record : list) {
            a[i]=record.getFlag();
            i++;
        }
        int[] b=new int[24];
        for (int i1 = 0; i1 <24; i1++) {
            b[i1]=i1+1;
        }
        List<Integer> list1 = new ArrayList();
        Outer: for(int i2=0;i2<b.length;i2++){
            for(int j=0;j<a.length;j++){
                if(b[i2] == a[j]){
                    continue Outer;
                }}
            list1.add(b[i2]);
        }
        return list1;
    }

    private Date getDay(int hour){
        DateFormat format = new SimpleDateFormat(YYYY_MM_DD);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set( Calendar.HOUR,calendar.get(Calendar.HOUR) +hour);
        Date day =calendar.getTime();
        return day;
    }

    //获取当前时间的前24小时时间
    private Date getHoursAgo(){
        DateFormat format = new SimpleDateFormat(TIME_STAMP_PATTERN);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set( Calendar.HOUR,calendar.get(Calendar.HOUR) -24);
        Date daysAgo =calendar.getTime();
        return daysAgo;
    }

    public List<Ccpdbinfo> getDataSource(){
        return mongoTemplate.findAll(Ccpdbinfo.class);
    }

    public HpcConfigValues getValues(){
        HpcConfigValues values = new HpcConfigValues();
        String name = bigScreenMapper.select1();
        Double cpu  = bigScreenMapper.select2();
        Double gpu  = bigScreenMapper.select3();
        values.setFirstName(StringUtils.isNotEmpty(name)? name:"-");
        values.setCpcWeight(cpu!=null? cpu:1);
        values.setGpcWeight(gpu!=null? gpu:1);
        return values;
    }
    private Date getHoursAgos(Date date){
        DateFormat format = new SimpleDateFormat(TIME_STAMP_PATTERN);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set( Calendar.HOUR,calendar.get(Calendar.HOUR) -8);
        Date daysAgo =calendar.getTime();
        return daysAgo;
    }

}
