package cn.com.cloudstar.rightcloud.module.standard.config;

import cn.com.cloudstar.rightcloud.module.standard.pojo.entity.Ccpdbinfo;
import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.stat.DruidDataSourceStatManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
public class DynamicDataSource extends AbstractRoutingDataSource {

    private boolean debug = true;
    private Map<Object, Object> dynamicTargetDataSources;
    private Object dynamicDefaultTargetDataSource;


    @Override
    protected Object determineCurrentLookupKey() {
        String datasource = DBContextHolder.getDataSource();
        if (!StringUtils.isEmpty(datasource)) {
            Map<Object, Object> dynamicTargetDataSources2 = this.dynamicTargetDataSources;
            if (dynamicTargetDataSources2.containsKey(datasource)) {
                log.info("---当前数据源：" + datasource + "---");
            } else {
                log.info("不存在的数据源：");
            }
        } else {
            log.info("---当前数据源：默认数据源---");
        }

        return datasource;

    }

    @Override
    public void setTargetDataSources(Map<Object, Object> targetDataSources) {

        super.setTargetDataSources(targetDataSources);

        this.dynamicTargetDataSources = targetDataSources;

    }

    /**
     * 创建数据源
     * @param key
     * @param driveClass
     * @param url
     * @param username
     * @param password
     * @return
     */
    public boolean createDataSource(String key, String driveClass, String url, String username, String password) {
        Connection testConnection = null;
        try {
            /**
             * 排除连接不上的错误
             */
            Class.forName(driveClass);
            testConnection = DriverManager.getConnection(url, username, password);// 相当于连接数据库

            @SuppressWarnings("resource")
//          HikariDataSource druidDataSource = new HikariDataSource();
            DruidDataSource druidDataSource = new DruidDataSource();
            druidDataSource.setName(key);
            druidDataSource.setDriverClassName(driveClass);
            druidDataSource.setUrl(url);
            druidDataSource.setUsername(username);
            druidDataSource.setPassword(password);
            //初始化时建立物理连接的个数。初始化发生在显示调用init方法，或者第一次getConnection时
            druidDataSource.setInitialSize(1);
            //最大连接池数量
            druidDataSource.setMaxActive(20);
            //获取连接时最大等待时间，单位毫秒。当链接数已经达到了最大链接数的时候，应用如果还要获取链接就会出现等待的现象，
            // 等待链接释放并回到链接池，如果等待的时间过长就应该踢掉这个等待，不然应用很可能出现雪崩现象
            druidDataSource.setMaxWait(60000);
            //最小连接池数量
            druidDataSource.setMinIdle(5);
            //默认的验证语句
//            String validationQuery = "select 1 from dual";
            String validationQuery = "select version()";

            //申请连接时执行validationQuery检测连接是否有效，这里建议配置为TRUE，防止取到的连接不可用
            druidDataSource.setTestOnBorrow(true);
            //建议配置为true，不影响性能，并且保证安全性。申请连接的时候检测，如果空闲时间大于timeBetweenEvictionRunsMillis，执行validationQuery检测连接是否有效。
            druidDataSource.setTestWhileIdle(true);
            //用来检测连接是否有效的sql，要求是一个查询语句。如果validationQuery为null，testOnBorrow、testOnReturn、testWhileIdle都不会起作用。
            druidDataSource.setValidationQuery(validationQuery);
            //属性类型是字符串，通过别名的方式配置扩展插件，常用的插件有：监控统计用的filter:stat日志用的filter:log4j防御sql注入的filter:wall
            druidDataSource.setFilters("stat");
            //配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
            druidDataSource.setTimeBetweenEvictionRunsMillis(60000);
            //配置一个连接在池中最小生存的时间，单位是毫秒，这里配置为3分钟180000
            druidDataSource.setMinEvictableIdleTimeMillis(180000);
            druidDataSource.setMaxEvictableIdleTimeMillis(300000);
            //打开druid.keepAlive之后，当连接池空闲时，池中的minIdle数量以内的连接，空闲时间超过minEvictableIdleTimeMillis，则会执行keepAlive操作，
            // 即执行druid.validationQuery指定的查询SQL，一般为select * from dual，只要minEvictableIdleTimeMillis设置的小于防火墙切断连接时间，
            // 就可以保证当连接空闲时自动做保活检测，不会被防火墙切断
            druidDataSource.setKeepAlive(false);
            //是否移除泄露的连接/超过时间限制是否回收。
            druidDataSource.setRemoveAbandoned(true);
            //泄露连接的定义时间(要超过最大事务的处理时间)；单位为秒。这里配置为1小时
            druidDataSource.setRemoveAbandonedTimeout(3600);
            //移除泄露连接发生是是否记录日志
            druidDataSource.setLogAbandoned(true);
            druidDataSource.init();

            this.dynamicTargetDataSources.put(key, druidDataSource);
            // 将map赋值给父类的TargetDataSources
            setTargetDataSources(this.dynamicTargetDataSources);
            // 将TargetDataSources中的连接信息放入resolvedDataSources管理
            super.afterPropertiesSet();
            log.info(key + "数据源初始化成功");
            //log.info(key+"数据源的概况："+druidDataSource.dump());
            return true;
        } catch (Exception e) {
            log.info("数据源：" + key + "连接数据库失败！",e + "");
            e.printStackTrace();
            return false;
        }finally {
            if (testConnection !=null){
                log.info("创建数据链接-DynamicDataSource.createDataSource-数据源：[{}]连接关闭",key);
                try {
                    testConnection.close();
                } catch (Exception ex) {
                    log.error("关闭测试链接-DynamicDataSource.createDataSource-异常:[{}]",ex.getMessage());
                }
            }
        }
    }


    // 测试数据源连接是否有效
    public String testDatasource(String key, String driveClass, List<String> urls, String username, String password) throws ClassNotFoundException {
            Class.forName(driveClass);
            String successUrl = null;
            for (String url : urls) {
                Connection connection = null;
                try {
                    connection = DriverManager.getConnection(url, username, password);
                    log.info("测试连接成功：{}",url);
                    successUrl = url;
                    break;
                } catch (SQLException e) {
                    log.error(e+"");
                    e.printStackTrace();
                    continue;
                }finally {
                    if (connection !=null){
                        log.info("创建数据链接-DynamicDataSource.testDatasource-数据源：[{}]连接关闭",key);
                        try {
                            connection.close();
                        } catch (Exception ex) {
                            log.error("关闭测试链接-DynamicDataSource.testDatasource-异常:[{}]",ex.getMessage());
                        }
                    }
                }
            }
            return successUrl;
    }

    @Override
    public void setDefaultTargetDataSource(Object defaultTargetDataSource) {
        super.setDefaultTargetDataSource(defaultTargetDataSource);
        this.dynamicDefaultTargetDataSource = defaultTargetDataSource;
    }

    /**
     * 检查数据源状态，没有创建或失效时重新创建
     * @param dataSource 数据源
     * @throws Exception
     */
    public boolean createDataSourceWithCheck(Ccpdbinfo dataSource) throws Exception {

        return createDataSource(dataSource);
    }

    /**
     * 创建数据源
     * @param dataSource 数据源
     */
    private boolean createDataSource(Ccpdbinfo dataSource) {
        String besunessCategory = dataSource.getBesunessCategory();
         log.info("准备创建数据源" + besunessCategory);
        String username = dataSource.getDbUser();
        String password = dataSource.getDbPassword();
        List<String> urls = dataSource.getUrls();
        // 默认的驱动类
        String driveClass = "org.postgresql.Driver";
        try {
            String url = testDatasource(besunessCategory, driveClass, urls, username, password);
            if (StringUtils.isNotBlank(url)) {
                boolean result = this.createDataSource(besunessCategory, driveClass, url, username, password);
                if (!result) {
                    log.info("数据源" + besunessCategory + "配置正确，但是创建失败");
                    return false;
                }else {
                    return true;
                }
            } else {
                log.error("数据源配置有错误");
                return false;
            }
        } catch (ClassNotFoundException e) {
            log.info("数据库驱动注册失败",e);
            e.printStackTrace();
            return false;
        }
    }













    /**
     * @param debug the debug to set
     */
    public void setDebug(boolean debug) {
        this.debug = debug;
    }

    /**
     * @return the debug
     */
    public boolean isDebug() {
        return debug;
    }

    /**
     * @return the dynamicTargetDataSources
     */
    public Map<Object, Object> getDynamicTargetDataSources() {
        return dynamicTargetDataSources;
    }

    /**
     * @param dynamicTargetDataSources the dynamicTargetDataSources to set
     */
    public void setDynamicTargetDataSources(Map<Object, Object> dynamicTargetDataSources) {
        this.dynamicTargetDataSources = dynamicTargetDataSources;
    }

    /**
     * @return the dynamicDefaultTargetDataSource
     */
    public Object getDynamicDefaultTargetDataSource() {
        return dynamicDefaultTargetDataSource;
    }

    /**
     * @param dynamicDefaultTargetDataSource the dynamicDefaultTargetDataSource to set
     */
    public void setDynamicDefaultTargetDataSource(Object dynamicDefaultTargetDataSource) {
        this.dynamicDefaultTargetDataSource = dynamicDefaultTargetDataSource;
    }
    /**
     * 关闭数据链接
     */
    public  void  CloseConnection(Ccpdbinfo dataSource) throws SQLException {
        Connection connection=null;
        DruidDataSource druidDataSource=null;
        try{
            String besunessCategory = dataSource.getBesunessCategory();
            Map<Object, Object> dynamicTargetDataSources2 = this.dynamicTargetDataSources;
            druidDataSource = (DruidDataSource) dynamicTargetDataSources2.get(besunessCategory);
            if (null != druidDataSource){
                connection=druidDataSource.getConnection();
            }
        }catch (Exception e){
            log.info(e.getMessage(), e);
        }finally {
            if (null != connection) {
                connection.close();
                druidDataSource.close();
            }
        }
    }
}
