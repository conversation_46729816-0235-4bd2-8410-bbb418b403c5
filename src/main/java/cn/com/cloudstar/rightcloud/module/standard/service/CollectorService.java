package cn.com.cloudstar.rightcloud.module.standard.service;

import cn.com.cloudstar.rightcloud.module.standard.enums.CollectorTypeEnum;

public interface CollectorService {

    void startCollector(CollectorTypeEnum collectorType) throws Exception;

    void repairCollector() throws Exception;

    /**
     * 话单采集监控：采集会有漏采的情况，监控1个小时内新增的话单
     */
    void monitorCollector(CollectorTypeEnum collectorType) throws Exception;

    void aiBmsCollector() throws Exception;
}
