package cn.com.cloudstar.rightcloud.module.standard.service.impl;

import cn.com.cloudstar.rightcloud.module.standard.config.DBContextHolder;
import cn.com.cloudstar.rightcloud.module.standard.config.DynamicDataSource;
import cn.com.cloudstar.rightcloud.module.standard.dao.BigScreenHourMapper;
import cn.com.cloudstar.rightcloud.module.standard.dao.BigScreenMapper;
import cn.com.cloudstar.rightcloud.module.standard.pojo.entity.*;
import cn.com.cloudstar.rightcloud.module.standard.service.BigScreenService;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/5/10 10:13
 * @description
 */
@Service
@Slf4j
public class BigScreenServiceImpl implements BigScreenService {

    @Autowired
    BigScreenMapper bigScreenMapper;
    @Autowired
    MongoTemplate mongoTemplate;
    @Autowired
    BigScreenHourMapper bigScreenHourMapper;
    @Autowired
    DynamicDataSource dynamicDataSource;

    //用户资源累计消耗TOP5
    @Override
    public void getTenantResourceUsedTotal() {
        mongoTemplate.remove(new Query(), TenantResourceUsedTotalRecord.class);
        List<Ccpdbinfo> dataSource = getDataSource();
        for (Ccpdbinfo info : dataSource) {
            try {
                log.info("需要使用的的数据源已经找到,集群是:" + info.getBesunessCategory());
                //创建数据源连接&检查 若存在则不需重新创建
                boolean flag = dynamicDataSource.createDataSourceWithCheck(info);
                if (!flag){
                    continue;
                }
                //切换到该数据源
                DBContextHolder.setDataSource(info.getBesunessCategory());
                //获取视图里面时间是最新的数据
                List<TenantResourceUsedTotalRecord> list = bigScreenMapper.selectTenantResourceUsedTotal();
                log.info("{}-开始采集租户资源累积消耗数据，采集到{}条", DateUtil.format(new Date(), "yyyyMMddHH"),list.size());
                //插入MongoDB
                mongoTemplate.insertAll(list);
                DBContextHolder.clearDataSource();
            } catch (Exception e) {
                log.info("{}链接失败",info.getDbAddress());
                e.printStackTrace();
                continue;
            }
        }
        getTenantResourceUsedTotalCount();
    }

    //填充率
    @Override
    public void getSampleClusterInfo() {
        //移除记录表里数据
        mongoTemplate.remove(new Query(), SampleClusterInfo.class);
        List<Ccpdbinfo> dataSource = getDataSource();
        for (Ccpdbinfo info : dataSource) {
            try {
                log.info("需要使用的的数据源已经找到,集群是:" + info.getBesunessCategory());
                //创建数据源连接&检查 若存在则不需重新创建
                boolean flag = dynamicDataSource.createDataSourceWithCheck(info);
                if (!flag){
                    continue;
                }
                //切换到该数据源
                DBContextHolder.setDataSource(info.getBesunessCategory());
                //获取视图里面时间是最新的数据
                List<SampleClusterInfo> list = bigScreenMapper.selectSampleClusterInfo();
                log.info("{}-开始采集填充率，采集到{}条", DateUtil.format(new Date(), "yyyyMMddHH"),list.size());
                //插入MongoDB
                mongoTemplate.insert(list,SampleClusterInfo.class);
                DBContextHolder.clearDataSource();
            } catch (Exception e) {
                e.printStackTrace();
                log.info("{}链接失败",info.getDbAddress());
                continue;
            }
        }
        getSampleClusterInfoCount();
    }

    //作业状态
    @Override
    public void getSampleAllJobStateCount() {
        //先移除记录表里的数据
        mongoTemplate.remove(new Query(), SampleAllJobStateCountRecord.class);
        List<Ccpdbinfo> dataSource = getDataSource();
        for (Ccpdbinfo info : dataSource) {
            try {
                log.info("需要使用的的数据源已经找到,集群是:" + info.getBesunessCategory());
                //创建数据源连接&检查 若存在则不需重新创建
                boolean flag = dynamicDataSource.createDataSourceWithCheck(info);
                if (!flag){
                    continue;
                }
                //切换到该数据源
                DBContextHolder.setDataSource(info.getBesunessCategory());
                //获取视图里面时间是最新的数据
                List<SampleAllJobStateCountRecord> list = bigScreenMapper.selectSampleAllJobStateCount();
                log.info("{}-开始采集作业状态，采集到{}条", DateUtil.format(new Date(), "yyyyMMddHH"),list.size());
                //插入MongoDB
                mongoTemplate.insertAll(list);
                DBContextHolder.clearDataSource();
            } catch (Exception e) {
                e.printStackTrace();
                log.info("{}链接失败",info.getDbAddress());
                continue;
            }
        }
        getSampleAllJobStateCountCount();
    }

    //集群累积消耗资源
    @Override
    public void getClusterResourceUsedTotal() {
        mongoTemplate.remove(new Query(), ClusterResourceUsedTotalRecord.class);
        List<Ccpdbinfo> dataSource = getDataSource();
        for (Ccpdbinfo info : dataSource) {
            try {
                log.info("需要使用的的数据源已经找到,集群是:" + info.getBesunessCategory());
                //创建数据源连接&检查 若存在则不需重新创建
                boolean flag = dynamicDataSource.createDataSourceWithCheck(info);
                if (!flag){
                    continue;
                }
                //切换到该数据源
                DBContextHolder.setDataSource(info.getBesunessCategory());
                //获取视图里面时间是最新的数据
                List<ClusterResourceUsedTotalRecord> list = bigScreenMapper.selectClusterResourceUsedTotal();
                log.info("{}-开始采集集群累积消耗资源，采集到{}条", DateUtil.format(new Date(), "yyyyMMddHH"),list.size());
                //插入MongoDB
                mongoTemplate.insertAll(list);
                DBContextHolder.clearDataSource();
            } catch (Exception e) {
                e.printStackTrace();
                log.info("{}链接失败",info.getDbAddress());
                continue;
            }
        }
        getClusterResourceUsedTotalCount();
    }

    // 当月资源使用排行
    @Override
    public void getTenantResourceUsedMonth() {
        //移除记录表数据
        mongoTemplate.remove(new Query(), TenantResourceUsedMonthRecord.class);
        List<Ccpdbinfo> dataSource = getDataSource();
        for (Ccpdbinfo info : dataSource) {
            try {
                log.info("需要使用的的数据源已经找到,集群是:" + info.getBesunessCategory());
                //创建数据源连接&检查 若存在则不需重新创建
                boolean flag = dynamicDataSource.createDataSourceWithCheck(info);
                if (!flag){
                    continue;
                }
                //切换到该数据源
                DBContextHolder.setDataSource(info.getBesunessCategory());
                //获取视图里面时间是最新的数据
                List<TenantResourceUsedMonthRecord> list = bigScreenHourMapper.selectTenantResourceUsedMonth();
                log.info("{}-开始采集用户当月资源使用数据，采集到{}条", DateUtil.format(new Date(), "yyyyMMddHH"),list.size());
                //插入MongoDB
                mongoTemplate.insertAll(list);
                DBContextHolder.clearDataSource();
            } catch (Exception e) {
                e.printStackTrace();
                log.info("{}链接失败",info.getDbAddress());
                continue;
            }
        }
       getTenantResourceUsedMonthCount();
    }

    //当日资源消耗数据TOP5
    @Override
    public void getJobResourceUsedToday() {
        //清除记录表里面数据
        mongoTemplate.remove(new Query(),JobResourceUsedTodayRecord.class);
        List<Ccpdbinfo> dataSource = getDataSource();
        for (Ccpdbinfo info : dataSource) {
            try {
                log.info("需要使用的的数据源已经找到,集群是:" + info.getBesunessCategory());
                //创建数据源连接&检查 若存在则不需重新创建
                boolean flag = dynamicDataSource.createDataSourceWithCheck(info);
                if (!flag){
                    continue;
                }
                //切换到该数据源
                DBContextHolder.setDataSource(info.getBesunessCategory());
                //获取视图里面时间是最新的数据
                List<JobResourceUsedTodayRecord> list = bigScreenMapper.selectJobResourceUsedToday();
                log.info("{}-开始采集当日资源消耗数据，采集到{}条", DateUtil.format(new Date(), "yyyyMMddHH"),list.size());
                //插入到MongoDB
                mongoTemplate.insertAll(list);
                DBContextHolder.clearDataSource();
            } catch (Exception e) {
                e.printStackTrace();
                log.info("{}链接失败",info.getDbAddress());
                continue;
            }
        }
        getJobResourceUsedTodayCount();
    }

    @Override
    public void handle() {

        getTenantResourceUsedTotal();

        getSampleClusterInfo();

        getSampleAllJobStateCount();

        getClusterResourceUsedTotal();

        getTenantResourceUsedMonth();

        getJobResourceUsedToday();

    }

    public void getTenantResourceUsedTotalCount(){
        //汇总数据
        try{
            HpcConfigValues values = getValues();
            //查出记录表里的数据
            List<TenantResourceUsedTotalRecord> all = mongoTemplate.findAll(TenantResourceUsedTotalRecord.class);
            if(all.size()==0){
                return;
            }
            //查出展示表里的数据
            List<TenantResourceUsedTotal> list = mongoTemplate.findAll(TenantResourceUsedTotal.class);
            List<TenantResourceUsedTotal> newList=new LinkedList<>();
            //获取所以的租户名称
            Map<String, List<TenantResourceUsedTotalRecord>> collect = all.stream()
                    .collect(Collectors.groupingBy(sa -> sa.getTenantName()));
            Set<String> strings = collect.keySet();
            List<Tenant> tenants = bigScreenMapper.selectTenant(strings);
            //遍历租户名 统计数据
            for (String string : strings) {
                //移除上次展示集合里面包含更新次集合里面有的租户名
                list.removeIf(b -> b.getTenantName().equals(string));
                Long cpuCoreMs= 0l;
                Long gpuCardMs= 0l;
                TenantResourceUsedTotal newTotal=new TenantResourceUsedTotal();
                for (TenantResourceUsedTotalRecord month : all) {
                    if (month.getTenantName().equals(string)){
                        cpuCoreMs+=month.getCpuCoreMs();
                        gpuCardMs+=month.getGpuCardMs();
                    }
                    for (Tenant tenant : tenants) {
                        if (tenant.getLdapOu().equals(string)){
                            newTotal.setRealName(tenant.getOrgName());
                            newTotal.setOrgSid(tenant.getOrgSid());
                        }
                    }
                }
                BigDecimal divide = BigDecimal.valueOf(cpuCoreMs).divide(BigDecimal.valueOf(360000),2,BigDecimal.ROUND_HALF_UP);
                BigDecimal divide1 = BigDecimal.valueOf(gpuCardMs).divide(BigDecimal.valueOf(360000),2,BigDecimal.ROUND_HALF_UP);
                BigDecimal cpu = divide.multiply(BigDecimal.valueOf(values.getCpcWeight()));
                BigDecimal gpu = divide1.multiply(BigDecimal.valueOf(values.getGpcWeight()));
                BigDecimal add = cpu.add(gpu);
                newTotal.setTenantName(string);
                newTotal.setCpuCoreMs(divide);
                newTotal.setGpuCardMs(divide1);
                newTotal.setUpdateTime(new Date());
                newTotal.setCpuCoreCMs(cpu);
                newTotal.setGpuCardCMs(gpu);
                newTotal.setTotalMs(add.doubleValue());
                newList.add(newTotal);
            }
            //移除MongoDB数据
            mongoTemplate.remove(new Query(), TenantResourceUsedTotal.class);
            //更新展示表数据
            if (list.size()!=0){
                mongoTemplate.insert(list,TenantResourceUsedTotal.class);
                log.info("更新用户资源累计消耗数据成功");
            }
            if(newList.size()!=0){
                mongoTemplate.insert(newList,TenantResourceUsedTotal.class);
                log.info("更新用户资源累计消耗数据成功");
            }
        }catch (Exception e){
            log.info("{}--更新用户资源累计消耗数据失败",e.getMessage());
            e.printStackTrace();
        }
    }

    public void getSampleClusterInfoCount(){
        //汇总数据
        try{
            List<SampleClusterInfo> list = mongoTemplate.findAll(SampleClusterInfo.class);
            if(list.size()!=0){
                //处理数据
                Long cpuUsed = (list.stream().mapToLong(SampleClusterInfo::getCpuUsed).sum());
                Long cpuTotal = (list.stream().mapToLong(SampleClusterInfo::getCpuTotal).sum());
                Long gpuUsed = (list.stream().mapToLong(SampleClusterInfo::getGpuUsed).sum());
                Long gpuTotal = (list.stream().mapToLong(SampleClusterInfo::getGpuTotal).sum());
                BigDecimal used = BigDecimal.valueOf(cpuUsed + gpuUsed);
                BigDecimal total = BigDecimal.valueOf(cpuTotal + gpuTotal);
                BigDecimal rate = used.divide(total, 2, BigDecimal.ROUND_HALF_UP);
                FillingRate fillingRate=new FillingRate();
                fillingRate.setCpuUsedCnt(cpuUsed);
                fillingRate.setCpuTotalCount(cpuTotal);
                fillingRate.setGpuUsedCount(gpuUsed);
                fillingRate.setGpuTotalCount(gpuTotal);
                fillingRate.setRate(rate);
                fillingRate.setSampleTime(new Date());
                //移除填充率mongoDB里面的数据
                mongoTemplate.remove(new Query(), FillingRate.class);
                //新数据插入MongoDB
                mongoTemplate.insert(fillingRate);
                log.info("更新填充率成功");
            }
        }catch (Exception e){
            log.info("更新填充率失败",e.getMessage());
            e.printStackTrace();
        }
    }

    public void getSampleAllJobStateCountCount(){
        try{
            //查询出记录表所有数据
            List<SampleAllJobStateCountRecord> all = mongoTemplate.findAll(SampleAllJobStateCountRecord.class);
            if (all.size()!=0){
                mongoTemplate.remove(new Query(), SampleAllJobStateCount.class);
                //更新到展示表中
                List<SampleAllJobStateCount> collect = all.stream().map(a -> new SampleAllJobStateCount(
                        a.getSampleTime(), a.getJobState(), a.getJobCount())).collect(Collectors.toList());
                mongoTemplate.insert(collect,SampleAllJobStateCount.class);
                log.info("更新作业状态数据成功");
            }
        }catch (Exception e){
            log.info("{}--更新作业状态数据失败",e.getMessage());
            e.printStackTrace();
        }
    }

    public void getClusterResourceUsedTotalCount(){
        try{
            //查询记录表里所以的数据
            List<ClusterResourceUsedTotalRecord> all = mongoTemplate.findAll(ClusterResourceUsedTotalRecord.class);
            if(all.size()==0){
                return;
            }
            //查询展示表里面的数据
            List<ClusterResourceUsedTotal> list = mongoTemplate.findAll(ClusterResourceUsedTotal.class);
            //获取所以的集群名称
            Map<String, List<ClusterResourceUsedTotalRecord>> collect = all.stream()
                    .collect(Collectors.groupingBy(sa -> sa.getClusterName()));
            Set<String> strings = collect.keySet();
            for (String string : strings) {
                //移除上次展示集合里面包含更新次集合里面有的集群名
                list.removeIf(b -> b.getClusterName().equals(string));

            }
            for (ClusterResourceUsedTotalRecord total : all) {
                BigDecimal cpu = total.getCpuCoreMs().divide(BigDecimal.valueOf(360000), 0, BigDecimal.ROUND_HALF_UP);
                BigDecimal gpu = total.getGpuCardMs().divide(BigDecimal.valueOf(360000), 0, BigDecimal.ROUND_HALF_UP);
                total.setCpuCoreMs(cpu);
                total.setGpuCardMs(gpu);
            }
            //移除mongoDB里面旧的数据
            mongoTemplate.remove(new Query(), ClusterResourceUsedTotal.class);
            //把新数据插入到展示表中
            if (list.size()!=0){
                mongoTemplate.insert(list,ClusterResourceUsedTotal.class);
                log.info("更新集群累积消耗资源成功");
            }
            if (all.size()!=0){
                mongoTemplate.insert(all,ClusterResourceUsedTotal.class);
                log.info("更新集群累积消耗资源成功");
            }
        }catch (Exception e){
            log.info("更新集群累积消耗资源失败",e.getMessage());
            e.printStackTrace();
        }
    }

    public void getTenantResourceUsedMonthCount(){
        //汇总数据
        try{
            HpcConfigValues values = getValues();
            //查出记录表里的数据
            List<TenantResourceUsedMonthRecord> all = mongoTemplate.findAll(TenantResourceUsedMonthRecord.class);
            if(all.size()==0){
                return;
            }
            //查出展示表里的数据
            List<TenantResourceUsedMonth> list = mongoTemplate.findAll(TenantResourceUsedMonth.class);
            List<TenantResourceUsedMonth> newList=new LinkedList<>();
            //获取所以的租户名称
            Map<String, List<TenantResourceUsedMonthRecord>> collect = all.stream()
                    .collect(Collectors.groupingBy(sa -> sa.getTenantName()));
            Set<String> strings = collect.keySet();
            List<Tenant> tenants = bigScreenMapper.selectTenant(strings);
            //遍历租户名 统计数据
            for (String string : strings) {
                //移除上次展示集合里面包含更新次集合里面有的租户名
                list.removeIf(b -> b.getTenantName().equals(string));
                Long jobCount = 0l;
                Long cpuCoreMs= 0l;
                Long gpuCardMs= 0l;
                TenantResourceUsedMonth newMonth=new TenantResourceUsedMonth();
                for (TenantResourceUsedMonthRecord month : all) {
                    if (month.getTenantName().equals(string)){
                        jobCount+=month.getJobCount();
                        cpuCoreMs+=month.getCpuCoreMs();
                        gpuCardMs+=month.getGpuCardMs();
                    }
                    for (Tenant tenant : tenants) {
                        if (tenant.getLdapOu().equals(string)){
                            newMonth.setRealName(tenant.getOrgName());
                            newMonth.setOrgSid(tenant.getOrgSid());
                        }
                    }
                }
                BigDecimal divide = BigDecimal.valueOf(cpuCoreMs).divide(BigDecimal.valueOf(360000), 2, BigDecimal.ROUND_HALF_UP);
                BigDecimal divide1 = BigDecimal.valueOf(gpuCardMs).divide(BigDecimal.valueOf(360000), 2, BigDecimal.ROUND_HALF_UP);
                BigDecimal cpus=divide.multiply(BigDecimal.valueOf(values.getCpcWeight()));
                BigDecimal gpus=divide1.multiply(BigDecimal.valueOf(values.getGpcWeight()));
                BigDecimal total=cpus.add(gpus);
                newMonth.setTenantName(string);
                newMonth.setJobCount(jobCount);
                newMonth.setCpuCoreMs(divide);
                newMonth.setGpuCardMs(divide1);
                newMonth.setUpdateTime(new Date());
                newMonth.setCpuCoreCMs(cpus);
                newMonth.setGpuCardCMs(gpus);
                newMonth.setTotalMs(total);
                newList.add(newMonth);
            }
            //移除MongoDB数据
            mongoTemplate.remove(new Query(), TenantResourceUsedMonth.class);
            //更新展示表数据
            if (list.size()!=0){
                mongoTemplate.insert(list,TenantResourceUsedMonth.class);
                log.info("更新用户当月资源使用数据成功");
            }
            if (newList.size()!=0){
                mongoTemplate.insert(newList,TenantResourceUsedMonth.class);
                log.info("更新用户当月资源使用数据成功");
            }
        }catch (Exception e){
            log.info("{}--更新用户当月资源使用数据失败",e.getMessage());
            e.printStackTrace();
        }
    }

    public void getJobResourceUsedTodayCount(){
        try{
            HpcConfigValues values = getValues();
            //查出记录表里的数据
            List<JobResourceUsedTodayRecord> all = mongoTemplate.findAll(JobResourceUsedTodayRecord.class);
            if(all.size()==0){
                return;
            }
            //查询展示表里面的数
            List<JobResourceUsedToday> list = mongoTemplate.findAll(JobResourceUsedToday.class);
            //获取所有的用户名
            Map<String, List<JobResourceUsedTodayRecord>> name = all.stream()
                    .collect(Collectors.groupingBy(sa -> sa.getUserName()));
            Set<String> names = name.keySet();
            List<Tenant> tenants = bigScreenMapper.selectTenant(names);
            for (String s : names) {
                //移除上次展示集合里面包含更新次集合里面有的租户名
                list.removeIf(b -> b.getUserName().equals(s));
            }
            List<JobResourceUsedToday>  newList=new LinkedList<>();
            for (JobResourceUsedTodayRecord usedToday : all) {
                JobResourceUsedToday  today=new JobResourceUsedToday();
                BigDecimal cpuCoreMs =BigDecimal.valueOf(usedToday.getCpuCoreMs()).divide(BigDecimal.valueOf(360000),2,BigDecimal.ROUND_HALF_UP);
                BigDecimal gpuCardMs =BigDecimal.valueOf(usedToday.getGpuCardMs()).divide(BigDecimal.valueOf(360000),2,BigDecimal.ROUND_HALF_UP);
                BigDecimal cpu = cpuCoreMs.multiply(BigDecimal.valueOf(values.getCpcWeight()));
                BigDecimal gpu = gpuCardMs.multiply(BigDecimal.valueOf(values.getGpcWeight()));
                today.setJobId(usedToday.getJobId());
                today.setUpdateTime(usedToday.getUpdateTime());
                today.setJobName(usedToday.getJobName());
                today.setUserName(usedToday.getUserName());
                today.setTenantName(usedToday.getTenantName());
                today.setClusterName(usedToday.getClusterName());
                today.setWallTime(BigDecimal.valueOf(usedToday.getWallTime()));
                today.setTotalMs(cpu.add(gpu));
                today.setGpuCardMs(gpuCardMs);
                today.setCpuCoreMs(cpuCoreMs);
                newList.add(today);
            }
            //获取所有的租户名
            Map<String, List<JobResourceUsedTodayRecord>> collect = all.stream()
                    .collect(Collectors.groupingBy(sa -> sa.getTenantName()));
            Set<String> strings = collect.keySet();
            //遍历租户名 统计数据
            //对应真实名称
            for (JobResourceUsedToday today : newList) {
                for (Tenant tenant : tenants) {
                    if (today.getTenantName().equals(tenant.getLdapOu())){
                        today.setRealName(tenant.getOrgName());
                    }
                }
            }
            //移除展示表里面旧的数据
            mongoTemplate.remove(new Query(), JobResourceUsedToday.class);
            //更新最新数据
            if(newList.size()!=0){
                mongoTemplate.insert(newList,JobResourceUsedToday.class);
                log.info("更新当日资源消耗数据成功");
            }
            if(list.size()!=0){
                mongoTemplate.insert(list,JobResourceUsedToday.class);
                log.info("更新当日资源消耗数据成功");
            }
        }catch (Exception e){
            log.info("更新当日资源消耗数据失败",e.getMessage());
            e.printStackTrace();
        }
    }

    public List<Ccpdbinfo> getDataSource(){
        return mongoTemplate.findAll(Ccpdbinfo.class);
    }

    public HpcConfigValues getValues(){
        HpcConfigValues values = new HpcConfigValues();
        String name = bigScreenMapper.select1();
        Double cpu  = bigScreenMapper.select2();
        Double gpu  = bigScreenMapper.select3();
        values.setFirstName(StringUtils.isNotEmpty(name)? name:"-");
        values.setCpcWeight(cpu!=null? cpu:1);
        values.setGpcWeight(gpu!=null? gpu:1);
        return values;
    }
}
