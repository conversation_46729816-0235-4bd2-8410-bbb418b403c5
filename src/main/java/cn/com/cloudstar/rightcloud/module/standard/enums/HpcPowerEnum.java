package cn.com.cloudstar.rightcloud.module.standard.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/5/9 14:26
 * @description
 */
@AllArgsConstructor
@Getter
public enum HpcPowerEnum {
    CPU1("一期CPU算力占比(单位T)",0.1125),
    CPU2("二期CPU算力占比(单位T)",0.01171875),
    GPU("一期二期GPU算力占比(单位T)",19.5),
    DAYCPU1("一期CPU算力占比(单位P)", 0.000079345703125),
    ADYCPU2("二期CPU算力占比(单位P)", 0.000011444091796875),
    DAYGPU("一期二期GPU算力占比(单位P)",0.001904296875);
    private  String   message;
    private  Double   values;

}
