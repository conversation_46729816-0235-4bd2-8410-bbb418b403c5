package cn.com.cloudstar.rightcloud.module.standard.service.impl;

import cn.com.cloudstar.rightcloud.module.standard.pojo.dto.Collector;
import cn.com.cloudstar.rightcloud.module.standard.pojo.dto.OBSCollector;
import cn.com.cloudstar.rightcloud.module.standard.pojo.dto.SdrCollectionRecord;
import cn.com.cloudstar.rightcloud.module.standard.service.MongoService;

import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.List;

@Service
public class MongoServiceImpl implements MongoService {

    @Resource
    private MongoTemplate mongoTemplate;

    @Override
    public void find() {
        Query query = new Query();
        List<SdrCollectionRecord> sdrCollectionRecords = mongoTemplate.find(query, SdrCollectionRecord.class);
        List<OBSCollector> collectors = mongoTemplate.find(query, OBSCollector.class);
    }

    @Override
    public void insertCollectorRecord(SdrCollectionRecord sdrCollectionRecord) {
        mongoTemplate.insert(sdrCollectionRecord);
    }

    @Override
    public void insertCollector(List<Collector> collectorList) {
        mongoTemplate.insertAll(collectorList);
    }

    @Override
    public Boolean existsCollectorByFileName(String fileName) {
        Query query = new Query();
        query.addCriteria(Criteria.where("file_name").is(fileName));
        return mongoTemplate.exists(query, SdrCollectionRecord.class);
    }

    @Override
    public SdrCollectionRecord getRecordByEnvIdAndType(Long envId, Integer type) {
        Query query = new Query();
        query.addCriteria(Criteria.where("cloud_env_id").is(envId).and("type").is(type));
        query.with(Sort.by(Sort.Order.desc("file_date"))).limit(1);
        SdrCollectionRecord sdrCollectionRecord = mongoTemplate.findOne(query, SdrCollectionRecord.class);
        return sdrCollectionRecord;
    }

    @Override
    public Boolean existsCollectorByFileDateAndTypeAndEnvId(String fileDate, Integer type, Long envId) {
        Query query = new Query();
        query.addCriteria(Criteria.where("file_date").is(fileDate).and("type").is(type).and("cloud_env_id").is(envId));
        SdrCollectionRecord sdrCollectionRecord = mongoTemplate.findOne(query, SdrCollectionRecord.class);
        return sdrCollectionRecord != null;
    }

    @Override
    public String getSdrCollectionRecordsLastTime() {
        Query query = new Query();

        List<SdrCollectionRecord> sdrCollectionRecordasc = mongoTemplate.find(query, SdrCollectionRecord.class);
        Query query1 = new Query();
        query.with(Sort.by(Sort.Order.desc("create_time")));
        List<SdrCollectionRecord> sdrCollectionRecorddesc = mongoTemplate.find(query1, SdrCollectionRecord.class);

        return null;
    }

}
