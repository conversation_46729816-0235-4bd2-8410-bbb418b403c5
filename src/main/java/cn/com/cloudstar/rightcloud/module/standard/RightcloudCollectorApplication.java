package cn.com.cloudstar.rightcloud.module.standard;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.Map;

import cn.hutool.core.util.StrUtil;

import cn.com.cloudstar.rightcloud.module.support.jasypt.configuration.detector.RightCloudEncryptablePropertyDetector;
import cn.com.cloudstar.rightcloud.module.support.jasypt.configuration.encryptor.DefaultEncryptor;

@EnableScheduling
@ComponentScan(excludeFilters = {
        @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = SecurityAutoConfiguration.class),
        @ComponentScan.Filter(type = FilterType.REGEX, pattern = {
                "cn.com.cloudstar.rightcloud.common.exception.handle.*"}),
        @ComponentScan.Filter(type = FilterType.REGEX, pattern = {
                "cn.com.cloudstar.rightcloud.common.encryptdata.interceptor.*"}),},
        basePackages = {"cn.com.cloudstar"})
@SpringBootApplication(exclude = {RabbitAutoConfiguration.class})
@MapperScan(basePackages = {"cn.com.cloudstar.rightcloud.module.standard.dao"})
@EnableFeignClients
public class RightcloudCollectorApplication {

    public static void main(String[] args) {
        initCerVerify();
        SpringApplication.run(RightcloudCollectorApplication.class, args);
    }


    /**
     * 初始化CA证书验证
     */
    private static void initCerVerify() {
        String caTrustorePath = System.getenv("CA_TRUSTORE_PATH");
        String caTrustorePassword = System.getenv("CA_TRUSTORE_PASSWORD");

        // 验证 CA 是否合法
        if (StrUtil.isNotEmpty(caTrustorePath)) {
            System.setProperty("javax.net.ssl.trustStore", caTrustorePath);
            RightCloudEncryptablePropertyDetector propertyDetector = new RightCloudEncryptablePropertyDetector();
            DefaultEncryptor encryptor = new DefaultEncryptor(propertyDetector);
            if (propertyDetector.isEncrypted(caTrustorePassword)) {
                System.setProperty("javax.net.ssl.trustStorePassword", encryptor.decrypt(caTrustorePassword));
            } else {
                System.setProperty("javax.net.ssl.trustStorePassword", caTrustorePassword);
            }
        }

        // 验证 CRL
        Map<String, String> envMap = System.getenv();
        String sslCheckRevocation = envMap.getOrDefault("SSL_CHECK_REVOCATION", "false");
        if (StrUtil.isNotEmpty(sslCheckRevocation) && "true".equalsIgnoreCase(sslCheckRevocation)) {
            System.setProperty("com.sun.net.ssl.checkRevocation", "true");
            System.setProperty("com.sun.security.enableCRLDP", "true");
        }
    }
}
