package cn.com.cloudstar.rightcloud.module.standard.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;

/**
 * 防御XXE漏洞
 *
 * <AUTHOR>
 */
@Configuration
public class XXEConfig {

    @Bean
    public DocumentBuilderFactory getDocumentBuilderFactory() throws ParserConfigurationException {
        //完全禁用 DTD
        DocumentBuilderFactory dbf =DocumentBuilderFactory.newInstance();
        dbf.setExpandEntityReferences(false);
        dbf.setFeature("http://apache.org/xml/features/disallow-doctype-decl",true);
        dbf.setFeature("http://xml.org/sax/features/external-general-entities",false);
        dbf.setFeature("http://xml.org/sax/features/external-parameter-entities",false);
        return dbf;
    }

}
