package cn.com.cloudstar.rightcloud.module.standard.service.impl;

import cn.com.cloudstar.rightcloud.module.standard.config.DBContextHolder;
import cn.com.cloudstar.rightcloud.module.standard.config.DynamicDataSource;
import cn.com.cloudstar.rightcloud.module.standard.config.RedisLock;
import cn.com.cloudstar.rightcloud.module.standard.dao.BigScreenHourMapper;
import cn.com.cloudstar.rightcloud.module.standard.dao.BigScreenMapper;
import cn.com.cloudstar.rightcloud.module.standard.pojo.entity.*;
import cn.com.cloudstar.rightcloud.module.standard.service.BigScreenHourService;
import cn.com.cloudstar.rightcloud.module.standard.service.CleanAndColleteService;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/5/10 10:27
 * @description
 */
@Service
@Slf4j
public class BigScreenHourServiceImpl implements BigScreenHourService {
    public static final String YYYY_MM_DD = "yyyyMMdd";
    public static final String YYYY_MM = "yyyyMM";
    public static final String YYYY_MM_DD_HH = "yyyyMMddHH";
    public static final String  METRIC_15DAY= "metric_15day_center_cf_apex";
    public static final String  METRIC_24HOUR = "metric_24hour_tenant_cf_apex";
    public static final String HH = "HH";
    public static final String DD = "dd";
    public static final String TIME_STAMP_PATTERN = "yyyyMMddHHmmss";
    public static final String MONTH="view_hpcop_tenant_resource_used_month";
    public static final String NODE="view_hpcop_sample_node_info";
    public static final  String  SAASPRIVATE="SAASPrivate";
    public static final  String  PRESAASPRIVATE="PreSAASPrivate";
    public static final  String  ADSAASPRIVATE="AdSAASPrivate";
    @Autowired
    BigScreenHourMapper bigScreenHourMapper;
    @Autowired
    MongoTemplate mongoTemplate;
    @Autowired
    BigScreenMapper bigScreenMapper;
    @Autowired
    DynamicDataSource dynamicDataSource;
    @Autowired
    CleanAndColleteService cleanAndColleteService;
    @Resource
    private RedisLock redisLock;
    @Autowired
    private StringRedisTemplate redisTemplate;

    //15天算力
    @Override
    public void getFifteenDays() throws SQLException {
        log.info("开始采集租户15天算力starTime:"+new Date());
        if (Boolean.FALSE.equals(redisTemplate.opsForValue().setIfAbsent(METRIC_15DAY,"success",5,TimeUnit.MINUTES))){
            return;
        }
        //先移除记录表理数据
        mongoTemplate.remove(new Query(), SampleTenantInfoRecord.class);
        List<Ccpdbinfo> dataSource = getDataSource();
        for (Ccpdbinfo info : dataSource) {
            if (StringUtils.isEmpty(info.getDbUser()) || StringUtils.isEmpty(info.getDbPassword())){
                continue;
            }
            try {
                log.info("需要使用的的数据源已经找到,集群是:" + info.getBesunessCategory());
                //创建数据源连接&检查 若存在则不需重新创建
                boolean flag = dynamicDataSource.createDataSourceWithCheck(info);
                if (!flag){
                    continue;
                }
                //切换到该数据源
                DBContextHolder.setDataSource(info.getBesunessCategory());
                //获取hpc视图数据
                List<SampleTenantInfoRecord> list = bigScreenHourMapper.selectSampleTenantInfo1();
                list.forEach(l->{
                    l.setClusterType(info.getClusterType());
                    l.setSampleTime(getHoursAgos(l.getSampleTime(),8));
                });
                log.info("{}-开始采集租户15天算力，采集到{}条", DateUtil.format(new Date(), "yyyyMMddHH"),list.size());
                if (list.size()!=0){
                    //插入MongoDB
                    mongoTemplate.insertAll(list);
                }
                DBContextHolder.clearDataSource();
            } catch (Exception e) {
                e.printStackTrace();
                log.info("{}链接失败",info.getDbAddress());
                continue;
            }finally {
                dynamicDataSource.CloseConnection(info);
            }
        }
        log.info("开始采集租户15天算力endTime:"+new Date());
    }

    //用户占用算力排行24小时算力
    @Override
    public void getTwentyFour() throws SQLException {
        log.info("24小时算力starTime:"+new Date());
        if (Boolean.FALSE.equals(redisTemplate.opsForValue().setIfAbsent(METRIC_24HOUR,"success",5,TimeUnit.MINUTES))){
            return;
        }
        log.info("开始采集租户24小时算力");
        //先移除记录表理数据
        mongoTemplate.remove(new Query(),SampleTenantInfo.class);
        List<Ccpdbinfo> dataSource = getDataSource();
        for (Ccpdbinfo info : dataSource) {
            if (StringUtils.isEmpty(info.getDbUser()) || StringUtils.isEmpty(info.getDbPassword())){
                continue;
            }
            try {
                log.info("需要使用的的数据源已经找到,集群是:" + info.getBesunessCategory());
                //创建数据源连接&检查 若存在则不需重新创建
                boolean flag = dynamicDataSource.createDataSourceWithCheck(info);
                if (!flag){
                    continue;
                }
                //切换到该数据源
                DBContextHolder.setDataSource(info.getBesunessCategory());
                //获取视图里面时间是最新的数据
                log.info("采样时间下限:{}", DateUtil.format(oneHourAgo(), "yyyyMMddHHmmss"));
                Date sampleTime = bigScreenHourMapper.selectAll().get(0).getSampleTime();
                log.info("数据库时间:{}",DateUtil.format(sampleTime, "yyyyMMddHHmmss"));
                List<SampleTenantInfo> list = bigScreenHourMapper.selectSampleTenantInfo(oneHourAgo());
                list.forEach(l->l.setClusterType(info.getClusterType()));
                log.info("{}-开始采集租户24小时算力，采集到{}条", DateUtil.format(new Date(), "yyyyMMddHHmmss"),list.size());
                if (list.size()!=0){
                    //插入MongoDB
                    mongoTemplate.insertAll(list);
                }
                DBContextHolder.clearDataSource();
            } catch (Exception e) {
                e.printStackTrace();
                log.info("{}链接失败",info.getDbAddress());
                continue;
            }finally {
                dynamicDataSource.CloseConnection(info);
            }
        }
        log.info("24小时算力endTime:"+new Date());
    }

    //热力图
    @Override
    public void getSampleNodeInfo() throws SQLException {
        String key = redisTemplate.opsForValue().get(NODE);
        if (key==null) {
            mongoTemplate.remove(new Query(), SampleNodeInfoRecord.class);
            List<Ccpdbinfo> dataSource = getDataSource();
            for (Ccpdbinfo info : dataSource) {
                if (StringUtils.isEmpty(info.getDbUser()) || StringUtils.isEmpty(info.getDbPassword())){
                    continue;
                }
                try {
                    log.info("需要使用的的数据源已经找到,集群是:" + info.getBesunessCategory());
                    //创建数据源连接&检查 若存在则不需重新创建
                    boolean flag = dynamicDataSource.createDataSourceWithCheck(info);
                    if (!flag){
                        continue;
                    }
                    //切换到该数据源
                    DBContextHolder.setDataSource(info.getBesunessCategory());
                    //获取视图里面时间是最新的数据
                    List<SampleNodeInfoRecord> list = bigScreenMapper.selectSampleNodeInfo();
                    log.info("{}-开始采集热力图数据，采集到{}条", DateUtil.format(new Date(), "yyyyMMddHH"),list.size());
                    //插入MongoDB
                    mongoTemplate.insertAll(list);
                    DBContextHolder.clearDataSource();
                } catch (Exception e) {
                    log.info("{}链接失败",info.getDbAddress());
                    e.printStackTrace();
                    continue;
                }finally {
                    dynamicDataSource.CloseConnection(info);
                }
            }
            redisTemplate.delete(NODE);
        }
    }

    // 当月资源使用排行
    @Override
    public void getTenantResourceUsedMonth() throws SQLException {
        log.info("当月资源使用采集startTime:"+new Date());
        if (Boolean.FALSE.equals(redisTemplate.opsForValue().setIfAbsent(MONTH,"success",5,TimeUnit.MINUTES))){
            return;
        }
        List<Ccpdbinfo> dataSource = getDataSource();
        for (Ccpdbinfo info : dataSource) {
            if (StringUtils.isEmpty(info.getDbUser()) ||
                    StringUtils.isEmpty(info.getDbPassword()) ||
                    SAASPRIVATE.equalsIgnoreCase(info.getClusterType()) ||
                    PRESAASPRIVATE.equalsIgnoreCase(info.getClusterType()) ||
                    ADSAASPRIVATE.equalsIgnoreCase(info.getClusterType())){
                continue;
            }
            try {
                log.info("需要使用的的数据源已经找到,集群是:" + info.getBesunessCategory());
                //创建数据源连接&检查 若存在则不需重新创建
                boolean flag = dynamicDataSource.createDataSourceWithCheck(info);
                if (!flag) {
                    continue;
                }
                //切换到该数据源
                DBContextHolder.setDataSource(info.getBesunessCategory());
                //获取视图里面时间是最新的数据
                List<TenantResourceUsedMonthRecord> list = bigScreenHourMapper.selectTenantResourceUsedMonth();
                log.info("{}-开始采集用户当月资源使用数据，采集到{}条", DateUtil.format(new Date(), "yyyyMMddHH"), list.size());
                getTenantResourceUsedMonthCount(list);
                //插入MongoDB
                if (list.size() != 0) {
                    mongoTemplate.insertAll(list);
                }
                //老化数据
                Date firstDay = DateUtils.addHours( cn.hutool.core.date.DateUtil.offsetDay(DateUtil.beginOfDay(new Date()),-29), 8);
                Query query = new Query(Criteria.where("update_time").lte(firstDay));
                mongoTemplate.remove(query, TenantResourceUsedMonthRecord.class);
                DBContextHolder.clearDataSource();
            } catch (Exception e) {
                log.info("{}链接失败", info.getDbAddress());
                e.printStackTrace();
                continue;
            }finally {
                dynamicDataSource.CloseConnection(info);
            }
        }
        log.info("当月资源使用采集endTime:"+new Date());
    }

    public List<TenantResourceUsedMonthRecord> getTenantResourceUsedMonthCount(List<TenantResourceUsedMonthRecord> list){
        Date date = new Date();
        List<TenantResourceUsedMonthRecord> all = mongoTemplate.findAll(TenantResourceUsedMonthRecord.class);
        if (all.size()==0){
            for (TenantResourceUsedMonthRecord record : list) {
                SimpleDateFormat format = new SimpleDateFormat(YYYY_MM_DD);
                record.setDay(Long.parseLong(format.format(date)));
                SimpleDateFormat format1 = new SimpleDateFormat(YYYY_MM_DD_HH);
                record.setTimestamp(Long.parseLong(format1.format(date)));
            }
        }else {
            for (TenantResourceUsedMonthRecord record : list) {
                String tenantName = record.getTenantName();
                Date firstDay = DateUtils.addHours(DateUtil.beginOfMonth(new Date()), 8);
                Query query = new Query(Criteria.where("update_time").gte(firstDay));
                query.addCriteria(Criteria.where("tenant_name").is(tenantName));
                List<TenantResourceUsedMonthRecord> records = mongoTemplate.find(query, TenantResourceUsedMonthRecord.class);
                SimpleDateFormat format = new SimpleDateFormat(YYYY_MM_DD);
                record.setDay(Long.parseLong(format.format(date)));
                SimpleDateFormat format1 = new SimpleDateFormat(YYYY_MM_DD_HH);
                record.setTimestamp(Long.parseLong(format1.format(date)));
                if (records.size() != 0){
                    long cpu = record.getCpuCoreMs()-records.stream().mapToLong(s -> s.getCpuCoreMs()).sum();
                    long gpu = record.getGpuCardMs()-records.stream().mapToLong(s -> s.getGpuCardMs()).sum();
                    record.setCpuCoreMs(cpu < 0L ? 0L:cpu);
                    record.setGpuCardMs(gpu < 0L ? 0L:gpu);
                }
            }
        }
        return list;
    }

    public List<Ccpdbinfo> getDataSource(){
        return mongoTemplate.findAll(Ccpdbinfo.class);
    }

    public HpcConfigValues getValues(){
        HpcConfigValues values = new HpcConfigValues();
        String name = bigScreenMapper.select1();
        Double cpu  = bigScreenMapper.select2();
        Double gpu  = bigScreenMapper.select3();
        values.setFirstName(StringUtils.isNotEmpty(name)? name:"-");
        values.setCpcWeight(cpu!=null? cpu:1);
        values.setGpcWeight(gpu!=null? gpu:1);
        return values;
    }

    //获取当前时间的前24小时时间
    private Date getHoursAgo(){
        DateFormat format = new SimpleDateFormat(TIME_STAMP_PATTERN);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set( Calendar.HOUR,calendar.get(Calendar.HOUR) -24);
        Date daysAgo =calendar.getTime();
        return daysAgo;
    }
    private Date getHoursAgos(Date date,int hour){
        DateFormat format = new SimpleDateFormat(TIME_STAMP_PATTERN);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set( Calendar.HOUR,calendar.get(Calendar.HOUR) +hour);
        Date daysAgo =calendar.getTime();
        return daysAgo;
    }
    private String collectorDate() {
        DateTime date = new DateTime();
        date.offset(DateField.HOUR_OF_DAY,-0);
        String file_date = DateUtil.format(date, "yyyyMMddHHMMSS");
        return file_date;
    }
    private Date oneHourAgo(){
        DateFormat format = new SimpleDateFormat(TIME_STAMP_PATTERN);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set( Calendar.HOUR,calendar.get(Calendar.HOUR) -1);
        calendar.set( Calendar.MINUTE,calendar.get(Calendar.MINUTE) -5);
        Date daysAgo =calendar.getTime();
        return daysAgo;
    }
}
