package cn.com.cloudstar.rightcloud.module.standard.util;

import cn.com.cloudstar.rightcloud.module.standard.pojo.dto.*;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CollectorTypeUtil {

    public static Collector init(Integer productType) {
        Collector collector = null;
        switch (productType){
            case 1:
                collector = new OBSCollector();
                break;
            case 2:
                collector = new ModelartsCollector();
                break;
            case 3:
                collector = new SFSCollector();
                break;
            case 4:
                collector = new HPCCollector();
                break;
            case 5:
                collector = new EvsCollector();
                break;
            case 6:
                collector = new BMSCollector();
                break;
            case 7:
                collector = new ECSCollector();
                break;
            case 8:
                collector = new ELBCollector();
                break;
            case 9:
                collector = new EIPCollector();
                break;
            case 10:
                collector = new HPCOfflineCollector();
                break;
            case 11:
                collector = new AIBmsCollector();
            default:
                log.info("没有找到此采集类型 productType:{}",productType);
        }
        return collector;
    }
}
