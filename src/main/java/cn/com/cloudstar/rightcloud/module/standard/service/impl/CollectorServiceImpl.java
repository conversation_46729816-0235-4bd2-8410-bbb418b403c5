package cn.com.cloudstar.rightcloud.module.standard.service.impl;

import cn.com.cloudstar.rightcloud.module.standard.dao.HcsoUserMapper;
import cn.com.cloudstar.rightcloud.module.standard.feignCilent.BssFeignClientService;
import cn.com.cloudstar.rightcloud.module.standard.pojo.dto.CollectorUserInfo;
import cn.com.cloudstar.rightcloud.module.standard.pojo.dto.CsvRequiredDataDto;
import cn.com.cloudstar.rightcloud.remote.api.iam.pojo.HcsoUser;
import cn.hutool.core.util.ObjectUtil;
import com.obs.services.ObsClient;
import com.obs.services.model.ListObjectsRequest;
import com.obs.services.model.ObjectListing;
import com.obs.services.model.ObsObject;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ZipUtil;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.common.pojo.BaseGridReturn;
import cn.com.cloudstar.rightcloud.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.module.standard.config.RedisLock;
import cn.com.cloudstar.rightcloud.module.standard.enums.CollectorTypeEnum;
import cn.com.cloudstar.rightcloud.module.standard.pojo.dto.CloudEnv;
import cn.com.cloudstar.rightcloud.module.standard.pojo.dto.Collector;
import cn.com.cloudstar.rightcloud.module.standard.pojo.dto.SdrCollectionRecord;
import cn.com.cloudstar.rightcloud.module.standard.pojo.response.SdrCollectorAndRecord;
import cn.com.cloudstar.rightcloud.module.standard.pojo.vo.ResHpcClusterVo;
import cn.com.cloudstar.rightcloud.module.standard.service.CloudEnvService;
import cn.com.cloudstar.rightcloud.module.standard.service.CollectorService;
import cn.com.cloudstar.rightcloud.module.standard.service.HpcClusterService;
import cn.com.cloudstar.rightcloud.module.standard.util.CsvUtil;
import cn.com.cloudstar.rightcloud.module.standard.util.FileUtil;
import cn.com.cloudstar.rightcloud.module.standard.util.ObsClientUtil;

/**
 * 2023-09-19 修改
 * <AUTHOR>
 * @Date 2023-09-19
 */
@Slf4j
@Service
public class CollectorServiceImpl implements CollectorService {

    private static final String ZIP_SUFFIX = ".zip";
    private static final String CSV_SUFFIX = ".csv";
    private static final String BMS_PREFIX = "CFN_bms_service";
    private static final String BMS_NOTEBOOK = "bms_notebook";
    private static final String BMS_JOB = "bms_job";

    @Resource
    private CloudEnvService cloudEnvService;

    @Resource
    private MongoServiceImpl mongoService;

    @Resource
    private RedisLock redisLock;

    @Resource
    private HpcClusterService hpcClusterService;

    @Resource
    private HcsoUserMapper hcsoUserMapper;

    @Resource
    private BssFeignClientService bssFeignClientService;
    /**
     * 定义文件最多个数
      */
    private static final int MAX_COUNT = 10000;
    /**
     * 定义文件最大大小
      */
    private static final long ZIP_MAX_SIZE = 4L * 1024 * 1024 * 1024;
    /**
     * 定义解压后文件最大
     */
    private static final long UNPACK_MAX_SIZE = 5L * 1024 * 1024 * 1024;


    /**
     *     本地临时存储话单文件路径
      */
    private static final String LOCAL_PATH = "/tmp/collector/";

    private static final String MA_BMS_LOCAL_PATH = "/bakfile/pm";

    @Override
    public void startCollector(CollectorTypeEnum collectorType) throws Exception {
        List<CloudEnv> cloudEnvList = cloudEnvService.findCloudEnvByProductType(collectorType);
        log.info("{}----开始执行{}话单采集, 找到{}个采集环境", DateUtil.format(new Date(), "yyyyMMddHH"),
                 collectorType.getMessage(), cloudEnvList.size());
        for (CloudEnv cloudEnv : cloudEnvList) {
            String endPoint = providerUrlFormatEndPoint(cloudEnv.getEndPoint());
            ObsClient obsClient = ObsClientUtil.connect(endPoint, cloudEnv.getAk(), cloudEnv.getSk());
            if (obsClient == null) {
                log.info("obsClient 连接失败 envId:{}", cloudEnv.getId());
                continue;
            }
            if (!ObsClientUtil.exisBucket(obsClient, cloudEnv.getBucket())) {
                log.info("ak,sk错误或bucket不存在 cloudEnvId{}", cloudEnv.getId());
                log.debug("bucket:{} endPoint:{}", cloudEnv.getBucket(), endPoint);
                continue;
            }
            // 获取上一个小时北京时间
            String fileDate = collectorDate(cloudEnv, collectorType);
            // 转为UTC时间
            fileDate = parseUtc(fileDate);
            //加锁防止并发重新采集
            Long time = System.currentTimeMillis() + RedisLock.TIME_OUT;
            String lockName = collectorType.getMessage() + ":" + fileDate + ":" + cloudEnv.getId();
            Boolean isLock = redisLock.lock(lockName, String.valueOf(time));
            if (!isLock) {
                continue;
            }
            // 验证是否已经采集到最新话单,如果已经采集完最新话单则不用采集了
            if (checkCollectorDate(fileDate)) {
                continue;
            }
            //构建阅读csv需要的数据
            CsvRequiredDataDto csvRequiredDataDto = buildReadCsvRequiredData();

            try {
                if (collectorType.getMode() == 1) {
                    collector1(collectorType, cloudEnv, obsClient, fileDate, csvRequiredDataDto);
                } else {
                    collector2(collectorType, cloudEnv, obsClient, fileDate, csvRequiredDataDto);
                }
            } catch (Exception e) {
                log.info("采集失败 cloudEnvId:{},collectorType:{}", cloudEnv.getId(), collectorType.getMessage());
                e.printStackTrace();
            } finally {
                // 释放锁
                redisLock.unlock(lockName, String.valueOf(time));
                obsClient.close();
            }
        }
    }

    @Override
    public void repairCollector() throws IOException {
        // 获取到需要修复的数据
        List<SdrCollectionRecord> sdrCollectionRecords = getRepairList();
        log.info("开始执行话单采集修复,找到{}条需修复数据", sdrCollectionRecords.size());
        for (int i = 0; i < sdrCollectionRecords.size(); i++) {
            String currentDate = DateUtil.format(new DateTime().offset(DateField.HOUR_OF_DAY, -8), "yyyyMMddHH");
            SdrCollectionRecord sdrCollectionRecord = sdrCollectionRecords.get(i);
            log.info("{}----开始执行话单采集修复, cloudEnvId:{} 采集类型:{}", currentDate, sdrCollectionRecord.getCloud_env_id(),
                     sdrCollectionRecord.getType_name());
            String fileDate = DateUtil.format(
                    new DateTime(sdrCollectionRecord.getFile_date(), "yyyyMMddHH").offset(DateField.HOUR_OF_DAY, +1),
                    "yyyyMMddHH");
            int count = 0;
            int errorCount = 0;
            CollectorTypeEnum collectorType = CollectorTypeEnum.find(sdrCollectionRecord.getType());
            CloudEnv cloudEnv = cloudEnvService.findCloudEnvById(sdrCollectionRecord.getCloud_env_id(), collectorType);
            String endPoint = providerUrlFormatEndPoint(cloudEnv.getEndPoint());
            ObsClient obsClient = ObsClientUtil.connect(endPoint, cloudEnv.getAk(), cloudEnv.getSk());
            if (obsClient == null) {
                log.info("obsClient 连接失败 envId:{}", cloudEnv.getId());
                continue;
            }
            if (!ObsClientUtil.exisBucket(obsClient, cloudEnv.getBucket())) {

                continue;
            }
            while (fileDate.compareTo(currentDate) < 0) {
                //加锁防止并发重新采集
                Long time = System.currentTimeMillis() + RedisLock.TIME_OUT;
                String lockName = collectorType.getMessage() + ":" + fileDate + ":" + cloudEnv.getId();
                Boolean isLock = redisLock.lock(lockName, String.valueOf(time));
                if (!isLock) {
                    continue;
                }
                // 验证是否已经被采集过
                if (mongoService.existsCollectorByFileDateAndTypeAndEnvId(fileDate, sdrCollectionRecord.getType(),
                                                                          cloudEnv.getId())) {
                    fileDate = DateUtil.format(new DateTime(fileDate, "yyyyMMddHH").offset(DateField.HOUR_OF_DAY, +1),
                                               "yyyyMMddHH");
                    continue;
                }
                //构建阅读csv需要的数据
                CsvRequiredDataDto csvRequiredDataDto = buildReadCsvRequiredData();
                try {
                    if (collectorType.getMode() == 1) {
                        collector1(collectorType, cloudEnv, obsClient, fileDate, csvRequiredDataDto);
                    } else {
                        collector2(collectorType, cloudEnv, obsClient, fileDate, csvRequiredDataDto);
                    }
                    count++;
                } catch (Exception e) {
                    log.info("采集修复失败 collectorType:{} fileDate:{} 失败原因:{}", collectorType.getMessage(), fileDate,
                             e.getMessage());
                    errorCount++;
                } finally {
                    fileDate = DateUtil.format(new DateTime(fileDate, "yyyyMMddHH").offset(DateField.HOUR_OF_DAY, +1),
                                               "yyyyMMddHH");
                    // 释放锁
                    redisLock.unlock(lockName, String.valueOf(time));
                }
            }
            obsClient.close();

        }


    }

    @Override
    public void monitorCollector(CollectorTypeEnum collectorType) throws Exception {
        List<CloudEnv> cloudEnvList = cloudEnvService.findCloudEnvByProductType(collectorType);
        log.info("{}----开始执行{}话单监控采集, 找到{}条需监控的话单数据", DateUtil.format(new Date(), "yyyyMMddHH"), collectorType.getMessage(), cloudEnvList.size());
        for (CloudEnv cloudEnv : cloudEnvList) {
            String endPoint = providerUrlFormatEndPoint(cloudEnv.getEndPoint());
            ObsClient obsClient = ObsClientUtil.connect(endPoint, cloudEnv.getAk(), cloudEnv.getSk());
            if (obsClient == null) {
                log.info("obsClient 连接失败 envId:{}", cloudEnv.getId());
                continue;
            }
            if (!ObsClientUtil.exisBucket(obsClient, cloudEnv.getBucket())) {
                log.info("ak,sk错误或bucket不存在 cloudEnvId{}", cloudEnv.getId());
                continue;
            }
            //监控2小时内的数据，大于2小时就不在监控
            String currentDate = DateUtil.format(new DateTime().offset(DateField.HOUR_OF_DAY, -9), "yyyyMMddHH");
            // 获取最新的话单时间
            String fileDate = getLatestDate(cloudEnv, collectorType);
            if ("".equals(fileDate) || currentDate.compareTo(fileDate) > 0){
                continue;
            }
            //加锁防止并发重新采集
            Long time = System.currentTimeMillis() + RedisLock.TIME_OUT;
            String lockName = "monitor" + ":" + collectorType.getMessage() + ":" + fileDate + ":" + cloudEnv.getId();
            Boolean isLock = redisLock.lock(lockName, String.valueOf(time));
            if (!isLock) {
                continue;
            }
            //构建阅读csv需要的数据
            CsvRequiredDataDto csvRequiredDataDto = buildReadCsvRequiredData();
            try {
                if (collectorType.getMode() == 1) {
                    collector1(collectorType, cloudEnv, obsClient, fileDate, csvRequiredDataDto);
                } else {
                    collector2(collectorType, cloudEnv, obsClient, fileDate, csvRequiredDataDto);
                }
            } catch (Exception e) {
                log.info("采集失败 cloudEnvId:{},collectorType:{}", cloudEnv.getId(), collectorType.getMessage());
                e.printStackTrace();
            } finally {
                // 释放锁
                redisLock.unlock(lockName, String.valueOf(time));
                obsClient.close();
            }
        }
    }

    @Override
    public void aiBmsCollector() throws Exception {
        List<CloudEnv> cloudEnvList = cloudEnvService.findCloudEnvByProductType(CollectorTypeEnum.AI_BMS);
        log.info("{}----开始执行{}话单采集, 找到{}个采集环境", DateUtil.format(new Date(), "yyyyMMddHH"),
                 CollectorTypeEnum.AI_BMS.getMessage(), cloudEnvList.size());
        for (CloudEnv cloudEnv : cloudEnvList) {
            String endPoint = cloudEnv.getEndPoint();
            ObsClient obsClient = ObsClientUtil.connect(endPoint, cloudEnv.getAk(), cloudEnv.getSk());
            if (obsClient == null) {
                log.info("obsClient 连接失败 envId:{}", cloudEnv.getId());
                continue;
            }
            if (!ObsClientUtil.exisBucket(obsClient, cloudEnv.getBucket())) {
                log.info("bucket不存在 cloudEnvId{}", cloudEnv.getId());
                continue;
            }
            String fileDate = collectorDate(cloudEnv, CollectorTypeEnum.AI_BMS);
            log.info("采集时间:{}", fileDate);
            Long time = System.currentTimeMillis() + RedisLock.TIME_OUT;
            String lockName = CollectorTypeEnum.AI_BMS.getMessage() + ":" + fileDate + ":" + cloudEnv.getId();
            boolean isLock = redisLock.lock(lockName, String.valueOf(time));
            if (!isLock) {
                continue;
            }

            //构建阅读csv需要的数据
            CsvRequiredDataDto csvRequiredDataDto = buildReadCsvRequiredData();
            aiBmsCollector(CollectorTypeEnum.AI_BMS, cloudEnv, obsClient, fileDate, csvRequiredDataDto);
        }
    }

    private void aiBmsCollector(CollectorTypeEnum collectorType, CloudEnv cloudEnv,
                                ObsClient obsClient, String fileDate, CsvRequiredDataDto csvRequiredDataDto) throws Exception {
        List<ObsObject> tarGzFiles = new ArrayList<>();
        try {
            ListObjectsRequest request = new ListObjectsRequest();
            request.setBucketName(cloudEnv.getBucket());
            request.setPrefix("cn-central/" + fileDate.substring(0, 10) + "/");
            ObjectListing objectListing = obsClient.listObjects(request);
            log.info("OBS中找到{}个话单tar.gz文件", objectListing.getObjects().size());
            for (ObsObject object : objectListing.getObjects()) {
                String objectKey = object.getObjectKey();
                String fileName = objectKey.substring(objectKey.lastIndexOf("/") + 1);
                // 只处理tar.gz文件
                if (objectKey.endsWith(".tar.gz")
                        && fileName.contains(fileDate)) {
                    tarGzFiles.add(object);
                }
            }
        } catch (Exception e) {
            log.error("列出OBS中BMS tar.gz文件失败: {}", e.getMessage(), e);
        }
        if (ObjectUtil.isEmpty(tarGzFiles)) {
            log.info("没有找到BMS tar.gz文件");
            return;
        }
        log.info("tarGzFiles中有{}个文件", tarGzFiles.size());
        int count = 0;
        for (ObsObject tarGzFile : tarGzFiles) {
            try {
                String objectKey = tarGzFile.getObjectKey();
                ObsObject file = ObsClientUtil.getFile(obsClient, cloudEnv.getBucket(), objectKey);
                if (ObjectUtil.isEmpty(file) || file == null) {
                    log.info("此obs对象不存在 objectKey:{}", objectKey);
                    continue;
                }
                if (tarGzFile.getMetadata().getContentLength() > ZIP_MAX_SIZE) {
                    log.error("话单压缩文件太大，超过最大限制，文件大小：{}", file.getMetadata().getContentLength());
                    throw new Exception("话单压缩包太大");
                }
                // 存储tar.gz文件
                String tarGzPath = LOCAL_PATH + collectorType.getMessage() + "/" + objectKey;
                log.info("tarGzPath:" + tarGzPath);
                writeToLocal(tarGzPath, file.getObjectContent());
                // 解压tar.gz文件
                FileUtil.deCompressGZipFile(tarGzPath, replaceTarGz(tarGzPath));
                String extractDir = replaceTarGz(tarGzPath);
                log.info("extractDir:{}", extractDir);
                log.info("解压tar.gz文件完成,解压路径:{}", extractDir);
                String zipPath = getHwsZipPath(extractDir, collectorType);
                log.info("zipPath:{}", zipPath);
                Map<String, String> zipMap = FileUtil.zipPaths(zipPath);

                for (Map.Entry<String, String> entry : zipMap.entrySet()) {
                    if (entry.getKey().startsWith(BMS_PREFIX)
                            && entry.getKey().endsWith(ZIP_SUFFIX)) {
                        ZipUtil.unzip(entry.getValue());
                        String unzipPath = replaceZip(entry.getValue());
                        log.info("解压后路径:{}", unzipPath);
                        Map<String, String> unzipMaps = FileUtil.zipPaths(unzipPath);
                        log.info("unzipMaps:{}", unzipMaps.size());
                        for (Map.Entry<String, String> unzipEntry : unzipMaps.entrySet()) {
                            if (unzipEntry.getKey().startsWith(BMS_PREFIX)
                                    && unzipEntry.getKey().endsWith(ZIP_SUFFIX)) {
                                ZipUtil.unzip(unzipEntry.getValue());
                                String csvPath = replaceZip(unzipEntry.getValue()) + "/" + replaceZipToCsv(unzipEntry.getKey());
                                log.info("csv文件路径:{}", csvPath);
                                String csvName = replaceZipToCsv(unzipEntry.getKey());
                                log.info("csv文件名称:{}", csvName);
                                if (!mongoService.existsCollectorByFileName(csvName)) {
                                    // 解析
                                    SdrCollectorAndRecord collector = CsvUtil.readCsv(
                                            objectKey, csvPath, csvName, collectorType,
                                            cloudEnv.getId(), fileDate, csvRequiredDataDto);
                                    log.info("解析后话单数量:{}", collector.getCollectorList().size());
                                    // 只要notebook和训练作业的话单
                                    collector.getCollectorList().removeIf(c ->
                                            !c.getExtend_params().equals(BMS_NOTEBOOK)
                                                    && !c.getExtend_params().equals(BMS_JOB));
                                    log.info("过滤后话单数量:{}", collector.getCollectorList().size());
                                    // 入库
                                    mongoService.insertCollectorRecord(collector.getSdrCollectionRecord());
                                    collector.getCollectorList().forEach(c ->
                                            c.setCollection_record_id(collector.getSdrCollectionRecord().getId())
                                    );
                                    mongoService.insertCollector(collector.getCollectorList());
                                    if (!CollectionUtils.isEmpty(collector.getDrpCollectorList())) {
                                        mongoService.insertCollector(collector.getDrpCollectorList());
                                    }
                                    count++;
                                }
                            }
                        }
                    }
                }
                FileUtil.deleteFile(replaceTarGz(tarGzPath), tarGzPath);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        if (count == 0) {
            log.info("未采集到aiBms入库话单 fileDate:{},cloudEnvId:{},type:{}", fileDate, cloudEnv.getId(),
                     collectorType.getMessage());
            throw new Exception("没有采集到入库话单");
        }
    }

    /**
     * 获取最新时间
     * @param cloudEnv
     * @param collectorType
     * @return
     */
    private String getLatestDate(CloudEnv cloudEnv, CollectorTypeEnum collectorType) {
        SdrCollectionRecord sdrCollectionRecord = mongoService.getRecordByEnvIdAndType(cloudEnv.getId(), collectorType.getCode());
        if (sdrCollectionRecord == null) {
            return "";
        }
        String file_date = sdrCollectionRecord.getFile_date();
        DateTime date = new DateTime(file_date, "yyyyMMddHH");
        return DateUtil.format(date, "yyyyMMddHH");
    }


    private List<SdrCollectionRecord> getRepairList() {
        List<SdrCollectionRecord> sdrCollectionRecords = new ArrayList<>();
        // 查询全部hcos云环境
        List<CloudEnv> cloudEnvs = cloudEnvService.findCloudEnvByEnvTypeToHCOS();
        for (CloudEnv cloudEnv : cloudEnvs) {
            for (CollectorTypeEnum collectorTypeEnum : CollectorTypeEnum.values()) {
                SdrCollectionRecord sdrCollectionRecord = mongoService.getRecordByEnvIdAndType(cloudEnv.getId(),
                                                                                               collectorTypeEnum.getCode());
                if (sdrCollectionRecord == null) {
                    continue;
                }
                DateTime dateTime = new DateTime();
                String dateString = DateUtil.format(dateTime.offset(DateField.HOUR_OF_DAY, -9), "yyyyMMddHH");
                if (!sdrCollectionRecord.getFile_date().equals(dateString)) {
                    sdrCollectionRecords.add(sdrCollectionRecord);
                }
            }

        }
        return sdrCollectionRecords;
    }


    /**
     * 采集方式1 现用于obs,modelarts,sfs,ma-bms 话单采集
     *
     * @param collectorType
     * @param cloudEnv
     * @param obsClient
     *
     * @throws Exception
     */
    private void collector1(CollectorTypeEnum collectorType, CloudEnv cloudEnv, ObsClient obsClient, String fileDate, CsvRequiredDataDto csvRequiredDataDto)
            throws Exception {
        String obsBackupPath = getBackupPath(cloudEnv.getRegion(), fileDate);
        ListObjectsRequest request = new ListObjectsRequest(cloudEnv.getBucket());
        log.info("obsBackupPath:{}", obsBackupPath);
        // 设置列举文件夹下全部对象
        request.setPrefix(obsBackupPath);
        ObjectListing objectListing = obsClient.listObjects(request);
        log.info("objectListing size:{}", objectListing.getObjects().size());
        // 实际读取到话单条数
        int count = 0;
        for (ObsObject obsObject : objectListing.getObjects()) {
            String obsBackupName = getCSDRBackupName(obsObject.getObjectKey());
            if (!obsBackupName.contains(fileDate)) {
                continue;
            }
            String objectKey = obsObject.getObjectKey();
            log.info("开始获取压缩文件:{}", objectKey);
            ObsObject obsObject1 = ObsClientUtil.getFile(obsClient, cloudEnv.getBucket(), obsObject.getObjectKey());
            if (obsObject1 == null) {
                log.info("此obs对象不存在 objectKey:{}", obsObject.getObjectKey());
                throw new Exception("此obs对象不存在 objectKey:" + obsObject.getObjectKey());
            }
            log.info("开始下载压缩文件:{}", objectKey);
            if (obsObject1.getMetadata().getContentLength() > ZIP_MAX_SIZE) {
                log.error("话单压缩文件太大，超过最大限制，文件大小：{}", obsObject1.getMetadata().getContentLength());
                throw new Exception("话单压缩包太大");
            }
            // 将.tar.gz 压缩文件临时存储到服务器
            writeToLocal(LOCAL_PATH + collectorType.getMessage() + "/" + objectKey, obsObject1.getObjectContent());
            // 解压.tar.gz
            log.info("开始解压文件 dir:{}", replaceTarGz(LOCAL_PATH + collectorType.getMessage() + "/" + objectKey));
            FileUtil.deCompressGZipFile(LOCAL_PATH + collectorType.getMessage() + "/" + objectKey,
                                        replaceTarGz(LOCAL_PATH + collectorType.getMessage() + "/" + objectKey));
            // 获取到.tar.gz解压后文件夹地址
            log.info("获取到.tar.gz解压后文件夹地址 {}", LOCAL_PATH + collectorType.getMessage() + "/" + objectKey);
            String unTarGzPath = replaceTarGz(LOCAL_PATH + collectorType.getMessage() + "/" + objectKey);
            // 获取到对应目录下全部话单zip压缩文件
            log.info("获取到对应目录下全部话单zip压缩文件 unTarGzPath:{}", unTarGzPath);
            String hwsZipPath = getHwsZipPath(unTarGzPath, collectorType);
            log.info("hwsZipPath:{}", hwsZipPath);
            Map<String, String> zipMap = FileUtil.zipPaths(hwsZipPath);
            log.info("zipMap:{}", zipMap.size());
            for (Map.Entry<String, String> entry : zipMap.entrySet()) {
                // 校验是否是话单zip文件
                if (checkHwsZip(entry.getKey())) {
                    // 解压话单zip文件
                    log.info("开始解压zip文件,entry.getValue():{}", entry.getValue());
                    ZipUtil.unzip(entry.getValue());
                    String unzipPath = replaceZip(entry.getValue());
                    log.info("unzipPath:{}", unzipPath);
                    Map<String, String> zipMap1 = FileUtil.zipPaths(unzipPath);
                    for (Map.Entry<String, String> entry1 : zipMap1.entrySet()) {
                        // 校验是否是话单zip文件,因为里面还有个md5文件
                        log.info("校验是否是话单zip文件,因为里面还有个md5文件");
                        if (checkHwsZip(entry1.getKey())) {
                            // 解压话单zip文件,解压后会生成话单csv文件
                            log.info("解压话单zip文件,解压后会生成话单csv文件");
                            ZipUtil.unzip(entry1.getValue());
                            String csvPath = replaceZip(entry1.getValue()) + "/" + replaceZipToCsv(entry1.getKey());
                            String csvName = replaceZipToCsv(entry1.getKey());
                            // 验证是否已经采集过此话单,没有采集过才入库
                            log.info("开始验证是否已经采集过此话单,没有采集过才入库csvPath:{},csvName:{}", csvPath, csvName);
                            if (!mongoService.existsCollectorByFileName(csvName)) {
                                log.info("开始解析话单");
                                // 解析话单
                                SdrCollectorAndRecord sdrCollectorAndRecord = CsvUtil.readCsv(objectKey, csvPath,
                                                                                              csvName, collectorType,
                                                                                              cloudEnv.getId(),
                                                                                              fileDate, csvRequiredDataDto);
                                // 入库
                                log.info("开始入库");
                                mongoService.insertCollectorRecord(sdrCollectorAndRecord.getSdrCollectionRecord());
                                String id = sdrCollectorAndRecord.getSdrCollectionRecord().getId();
                                sdrCollectorAndRecord.getCollectorList().forEach(collector -> collector.setCollection_record_id(id));
                                mongoService.insertCollector(sdrCollectorAndRecord.getCollectorList());
                                //专属资源池话单入库
                                List<Collector> drpCollectorList = sdrCollectorAndRecord.getDrpCollectorList();
                                if (!CollectionUtils.isEmpty(drpCollectorList)) {
                                    mongoService.insertCollector(drpCollectorList);
                                }

                                count++;
                            }
                        }
                    }
                }
            }
            // 采集完成后删除临时压缩文件
            FileUtil.deleteFile(replaceTarGz(LOCAL_PATH + collectorType.getMessage() + "/" + objectKey),
                                LOCAL_PATH + collectorType.getMessage() + "/" + objectKey);
        }
        if (count == 0) {
            log.info("没有采集到入库话单 fileDate:{},cloudEnvId:{},type:{}", fileDate, cloudEnv.getId(),
                     collectorType.getMessage());
            throw new Exception("没有采集到入库话单");
        }

    }

    /**
     * 采集方式2 现用于hpc话单采集
     *
     * @param collectorType
     * @param cloudEnv
     * @param obsClient
     *
     * @throws Exception
     */
    private void collector2(CollectorTypeEnum collectorType, CloudEnv cloudEnv, ObsClient obsClient, String fileDate, CsvRequiredDataDto csvRequiredDataDto)
            throws Exception {
        String obsBackupPath = getHpcBackupPath(fileDate);
        ListObjectsRequest request = new ListObjectsRequest(cloudEnv.getBucket());
        // 设置列举文件夹下全部对象
        request.setPrefix(obsBackupPath);
        ObjectListing objectListing = obsClient.listObjects(request);
        // 采集到话单文件数量
        int count = 0;

        //查询hpcCluster资源池
        RestResult restResult = hpcClusterService.getResHpcCluster();
        BaseGridReturn baseGridReturn = BeanConvertUtil.convert(restResult.getData(), BaseGridReturn.class);
        List<ResHpcClusterVo> hpcClusters = BeanConvertUtil.convert(baseGridReturn.getDataList()
                , ResHpcClusterVo.class);
        //hpc专属资源池映射map
        Map<String, ResHpcClusterVo> hpcClustersMap = new HashMap<>(20);
        for (ResHpcClusterVo hpcCluster : hpcClusters) {
            hpcClustersMap.put(hpcCluster.getName(), hpcCluster);
        }
        csvRequiredDataDto.setHpcClustersMap(hpcClustersMap);

        for (ObsObject obsObject : objectListing.getObjects()) {
            // 获取到话单zip的名称
            String obsBackupName = getHpcBackupName(obsObject.getObjectKey());
            // 验证是否是本次要采集的话单zip
            if (checkHpcZip(obsBackupName, fileDate)) {
                // 获取到obs上话单压缩文件
                ObsObject obsObject1 = ObsClientUtil.getFile(obsClient, cloudEnv.getBucket(), obsObject.getObjectKey());
                if (obsObject1 == null) {
                    log.info("此obs对象不存在 objectKey:{}", obsObject.getObjectKey());
                    throw new Exception("此obs对象不存在 objectKey:" + obsObject.getObjectKey());
                }
                // 将zip 压缩文件临时存储到服务器
                writeToLocal(LOCAL_PATH + obsObject.getObjectKey(), obsObject1.getObjectContent());
                ZipUtil.unzip(LOCAL_PATH + obsObject.getObjectKey());
                String unzipPath = replaceZip(LOCAL_PATH + obsObject.getObjectKey());
                Map<String, String> zipMap = FileUtil.zipPaths(unzipPath);
                for (Map.Entry<String, String> entry : zipMap.entrySet()) {
                    if (checkHpcCsv(entry.getKey(), fileDate)) {
                        String csvPath = entry.getValue();
                        String csvName = entry.getKey();
                        // 验证是否已经采集过此话单,没有采集过才入库
                        if (!mongoService.existsCollectorByFileName(csvName)) {
                            // 解析话单
                            SdrCollectorAndRecord sdrCollectorAndRecord = CsvUtil.readCsv(obsObject.getObjectKey(),
                                                                                          csvPath, csvName,
                                                                                          collectorType,
                                                                                          cloudEnv.getId(), fileDate,csvRequiredDataDto);
                            // 入库
                            mongoService.insertCollectorRecord(sdrCollectorAndRecord.getSdrCollectionRecord());
                            String id = sdrCollectorAndRecord.getSdrCollectionRecord().getId();
                            sdrCollectorAndRecord.getCollectorList().forEach(collector -> collector.setCollection_record_id(id));
                            mongoService.insertCollector(sdrCollectorAndRecord.getCollectorList());
                            //专属资源池话单入库
                            List<Collector> drpCollectorList = sdrCollectorAndRecord.getDrpCollectorList();
                            if (!CollectionUtils.isEmpty(drpCollectorList)) {
                                drpCollectorList.forEach(collector -> collector.setCollection_record_id(id));
                                mongoService.insertCollector(drpCollectorList);
                            }
                            List<Collector> hpcPriCollectorList = sdrCollectorAndRecord.getHpcPriCollectorList();
                            if(!CollectionUtils.isEmpty(hpcPriCollectorList)){
                                hpcPriCollectorList.forEach(collector -> collector.setCollection_record_id(id));
                                mongoService.insertCollector(hpcPriCollectorList);
                            }
                            count++;
                        }
                    }

                }
                // 采集完成后删除临时压缩文件
                FileUtil.deleteFile(replaceZip(LOCAL_PATH + obsObject.getObjectKey()),
                                                LOCAL_PATH + obsObject.getObjectKey());

            }
        }
        if (count == 0) {
            log.info("没有采集到入库话单 fileDate:{},cloudEnvId:{},type:{}", fileDate, cloudEnv.getId(),
                     collectorType.getMessage());
            throw new Exception("没有采集到入库话单");
        }

    }


    /**
     * 将InputStream写入本地文件
     *
     * @param destination 写入本地目录
     * @param input 输入流
     *
     * @throws IOException IOException
     */
    private static void writeToLocal(String destination, InputStream input) throws IOException {
        int available = input.available();
        int lastIndex = destination.lastIndexOf('/');
        checkDirExists(cn.hutool.core.io.FileUtil.file(destination.substring(0, lastIndex)));
        checkFileExists(cn.hutool.core.io.FileUtil.file(destination));
        int index;
        byte[] bytes = new byte[1024];

        BufferedOutputStream downloadFile = cn.hutool.core.io.FileUtil.getOutputStream(destination);
        while ((index = input.read(bytes)) != -1) {
            downloadFile.write(bytes, 0, index);
            downloadFile.flush();
        }
        input.close();
        downloadFile.close();
    }

    /**
     * 判断文件是否存在
     *
     * @param file
     */
    private static void checkFileExists(File file) {
        if (!file.exists()) {
            try {
                file.createNewFile();
                file.setReadable(true);
                file.setWritable(true);
            } catch (IOException e) {
                log.error(e.getMessage());
            }
        }
    }

    /**
     * 判断文件夹是否存在
     *
     * @param file
     */
    private static void checkDirExists(File file) {
        if (!file.exists()) {
            file.mkdirs();
        }
    }

    private static String providerUrlFormatEndPoint(String providerUrl) {
        String endPoint = providerUrl.replace("iam", "obs")
                                     .replace("-pub", "")
                                     .replace("/v3", "")
                                     .replace(":443", "")
                                     .replace(":30443", "");
        return endPoint;
    }

    private static String regionFormatEndPint(String region) {
        return region.replace("iam", "obs");
    }

    /**
     * 获取obs上备份文件路径地址
     */
    private static String getBackupPath(String region, String fileDate) {
        StringBuffer path = new StringBuffer();
        path.append(region + "/");
        String datePath = fileDate.substring(0, fileDate.length() - 2);
        path.append(datePath);
        return path.toString();
    }

    private static String getHpcBackupPath(String fileDate) {
        StringBuffer path = new StringBuffer();
        String datePath = fileDate.substring(0, fileDate.length() - 2);
        path.append(datePath);
        return path.toString();
    }

    private static String getHpcBackupName(String obsObjectKey) {
        return obsObjectKey.substring(obsObjectKey.lastIndexOf('/') + 1);
    }

    private static String getCSDRBackupName(String obsObjectKey) {
        return obsObjectKey.substring(obsObjectKey.lastIndexOf('/') + 1);
    }


    /**
     * 获取obs上备份tar.gz文件名
     */
    private static String getBackFileName(String region, String fileDate) {
        StringBuffer fileName = new StringBuffer();
        fileName.append(region);
        fileName.append("IATmeterticket");
        fileName.append(fileDate + "H-");
        String role = "active";
        fileName.append(role);
        fileName.append(".tar.gz");
        return fileName.toString();
    }


    private static String replaceTarGz(String tarGzPath) {
        return tarGzPath.replaceAll(".tar.gz", "");
    }

    private static String replaceZip(String zipPath) {
        return zipPath.replaceAll(ZIP_SUFFIX, "");
    }

    private static String replaceZipToCsv(String zipPath) {
        return zipPath.replaceAll(ZIP_SUFFIX, CSV_SUFFIX);
    }


    private static String getHwsZipPath(String unTarGzPath, CollectorTypeEnum collectorType) {
        StringBuffer hwsZipPath = new StringBuffer(unTarGzPath);
        hwsZipPath.append("/bakfiles/");
        switch (collectorType.getCode()) {
            case 1:
                hwsZipPath.append("obs");
                break;
            case 2:
                hwsZipPath.append("modelarts");
                break;
            case 3:
                hwsZipPath.append("sfs");
                break;
            case 4:
                hwsZipPath.append("hpc");
                break;
            case 6:
                hwsZipPath.append("pm");
                break;
            case 11:
                hwsZipPath.append("bms");
                break;
            default:
                log.info("getHwsZipPath 没有找到此采集类型!");
        }
        return hwsZipPath.toString();
    }

    /**
     * 校验是否是话单zip文件
     */
    private Boolean checkHwsZip(String hwsZipFileName) {
        String prefix = "HWS";
        if (!hwsZipFileName.startsWith(prefix)) {
            return false;
        }
        if (!hwsZipFileName.endsWith(ZIP_SUFFIX)) {
            return false;
        }
        return true;
    }

    /**
     * 校验是否是hpc话单csv文件
     */
    private Boolean checkHpcCsv(String hpcCsvFileName, String fileDate) {
        if (!hpcCsvFileName.startsWith(fileDate)) {
            return false;
        }
        if (!hpcCsvFileName.endsWith(CSV_SUFFIX)) {
            return false;
        }
        return true;
    }

    private Boolean checkHpcZip(String hpcZip, String fileDate) {
        if (!hpcZip.startsWith(fileDate)) {
            return false;
        }
        if (!hpcZip.endsWith(ZIP_SUFFIX)) {
            return false;
        }
        return true;
    }

    /**
     * 获取本次需采集的时间
     *
     * @param cloudEnv
     * @param collectorType
     */
    private String collectorDate(CloudEnv cloudEnv, CollectorTypeEnum collectorType) {
        SdrCollectionRecord sdrCollectionRecord = mongoService.getRecordByEnvIdAndType(cloudEnv.getId(),
                                                                                       collectorType.getCode());
        String file_date;
        if (sdrCollectionRecord == null) {
            // 获取上一个小时UTC时间(改为了当前小时)
            DateTime date = new DateTime();
            date.offset(DateField.HOUR_OF_DAY, -1);
            file_date = DateUtil.format(date, "yyyyMMddHH");
        } else {
            file_date = sdrCollectionRecord.getFile_date();
            DateTime date = new DateTime(file_date, "yyyyMMddHH");
            date.offset(DateField.HOUR_OF_DAY, +9);
            file_date = DateUtil.format(date, "yyyyMMddHH");
        }
        return file_date;
    }

    private String parseUtc(String file_date) {
        DateTime date = new DateTime(file_date, "yyyyMMddHH");
        date.offset(DateField.HOUR_OF_DAY, -8);
        return DateUtil.format(date, "yyyyMMddHH");
    }


    private Boolean checkCollectorDate(String fileDate) {
        DateTime date = new DateTime().offset(DateField.HOUR_OF_DAY, -8).offset(DateField.HOUR_OF_DAY, +1);
        String current_date = DateUtil.format(date, "yyyyMMddHH");
        return current_date.equals(fileDate);
    }


    /**
     * 构建读取csv需要的数据
     */
    private CsvRequiredDataDto buildReadCsvRequiredData() {
        RestResult collectorInfoRes = bssFeignClientService.getCollectorInfo();
        Object data = collectorInfoRes.getData();
        CollectorUserInfo convert = BeanConvertUtil.convert(data, CollectorUserInfo.class);
        Map<String, Long> projectIdUserSidMap = new HashMap<>(10);
        Map<String, Long> obsProjectIdUserSidMap = new HashMap<>(10);
        //有效的hcosuser
        cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria criteria = new cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria();
        criteria.put("status", "used");
        List<HcsoUser> hcsoUsers = hcsoUserMapper.selectByCriteria(criteria);
        for (HcsoUser hcsoUser : hcsoUsers) {
            projectIdUserSidMap.put(hcsoUser.getProjectId(), hcsoUser.getRefUserId());
            obsProjectIdUserSidMap.put(hcsoUser.getObsProjectId(), hcsoUser.getRefUserId());
        }
        return CsvRequiredDataDto.builder().projectIdUserSidMap(projectIdUserSidMap).obsProjectIdUserSidMap(obsProjectIdUserSidMap).userInfo(convert).build();
    }

}
