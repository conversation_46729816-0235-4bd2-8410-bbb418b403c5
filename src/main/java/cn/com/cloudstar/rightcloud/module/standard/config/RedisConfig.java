/*
 * Copyright (c) 2019.  Cloud-Star. Co. Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.module.standard.config;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisNode;
import org.springframework.data.redis.connection.RedisSentinelConfiguration;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.jedis.JedisClientConfiguration;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.net.ssl.SSLSocketFactory;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import redis.clients.jedis.JedisPoolConfig;

import cn.com.cloudstar.rightcloud.common.redis.SslUtil;
import cn.com.cloudstar.rightcloud.common.util.CrytoUtilSimple;
import cn.com.cloudstar.rightcloud.oss.common.cache.DefaultStringSerializer;
import cn.com.cloudstar.rightcloud.oss.common.cache.JedisUtil;

/**
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class RedisConfig {

    private static final String CLOUDSTAR_REDIS_IS_SENTINEL = "cloudstar.redis.is_sentinel";

    private static final String CLOUDSTAR_REDIS_MASTER = "cloudstar.redis.master";

    private static final String CLOUDSTAR_REDIS_SENTINELS = "cloudstar.redis.sentinels";

    private static final String CLOUDSTAR_REDIS_SENTINEL_CIPHER = "redis.sentinel.password";

    private static final String CLOUDSTAR_REDIS_CIPHER = "redis.password";

    private static final String CLOUDSTART_REDIS_SSL = "cloudstar.redis.ssl";

    private static final String CLOUDSTART_REDIS_TRUSTKEYSTORE = "cloudstar.redis.trustKeyStore";

    private static final String CLOUDSTART_REDIS_TRUSTPW = "redis.trust.store.password";

    // 自定义前缀
    private final String prefix = "RC_EN(";

    // 自定义后缀
    private final String suffix = ")";

    @Autowired
    private Environment env;

    @Value("${spring.redis.host}")
    private String host;

    @Value("${spring.redis.port}")
    private String port;

    @Value("${spring.redis.password}")
    private String password;

    @Bean
    @ConditionalOnProperty(name = CLOUDSTAR_REDIS_IS_SENTINEL, havingValue = "false", matchIfMissing = false)
    public RedisConnectionFactory redisConnectionFactory() {
        RedisStandaloneConfiguration configuration = new RedisStandaloneConfiguration();
        configuration.setHostName(host);
        configuration.setPort(Integer.parseInt(port));
        configuration.setPassword(password);

        JedisClientConfiguration jedisClientConfig = null;
        if (Boolean.parseBoolean(env.getProperty(CLOUDSTART_REDIS_SSL))) {
            String trustPw = env.getProperty(CLOUDSTART_REDIS_TRUSTPW);
            if (!StrUtil.isEmpty(trustPw)) {
                if (isEncrypted(trustPw)) {
                    trustPw = decrypt(trustPw);
                }
            }
            SSLSocketFactory socketFactory = SslUtil.getSSLSocketFactory(null, null, env.getProperty(CLOUDSTART_REDIS_TRUSTKEYSTORE), trustPw);
            jedisClientConfig =
                    JedisClientConfiguration.builder().useSsl()
                                            .hostnameVerifier(NoopHostnameVerifier.INSTANCE)
                                            .sslSocketFactory(socketFactory)
                                            .build();
        } else {
            jedisClientConfig = JedisClientConfiguration.builder().usePooling().poolConfig(new JedisPoolConfig()).build();
        }

        return new JedisConnectionFactory(configuration, jedisClientConfig);
    }


    @Bean
    @ConditionalOnProperty(name = CLOUDSTAR_REDIS_IS_SENTINEL, havingValue = "true", matchIfMissing = false)
    public RedisConnectionFactory redisSentinelConnectionFactory() throws Exception {
        String master = env.getProperty(CLOUDSTAR_REDIS_MASTER);
        String sentinels = env.getProperty(CLOUDSTAR_REDIS_SENTINELS);
        RedisSentinelConfiguration rsc = new RedisSentinelConfiguration();
        rsc.setMaster(master);
        List<String> redisNodes = Arrays.asList(StrUtil.splitToArray(sentinels, StrUtil.COMMA));
        List<RedisNode> nodes = redisNodes.stream().map(str -> {
            String[] hostPorts = str.split(StrUtil.COLON);
            return new RedisNode(hostPorts[0], Integer.parseInt(hostPorts[1]));
        }).collect(Collectors.toList());
        rsc.setSentinels(nodes);
        rsc.setPassword(env.getProperty(CLOUDSTAR_REDIS_CIPHER));
        String sentinelPassword = StrUtil.emptyToNull(env.getProperty(CLOUDSTAR_REDIS_SENTINEL_CIPHER));
        if (!StrUtil.isEmpty(sentinelPassword)) {
            if (isEncrypted(sentinelPassword)) {
                sentinelPassword = decrypt(sentinelPassword);
            }
        }
        rsc.setSentinelPassword(sentinelPassword);
        JedisClientConfiguration jedisClientConfig;

        if (Boolean.parseBoolean(env.getProperty(CLOUDSTART_REDIS_SSL))) {
            String trustPw = env.getProperty(CLOUDSTART_REDIS_TRUSTPW);
            if (!StrUtil.isEmpty(trustPw)) {
                if (isEncrypted(trustPw)) {
                    trustPw = decrypt(trustPw);
                }
            }
            SSLSocketFactory socketFactory = SslUtil.getSSLSocketFactory(null, null, env.getProperty(CLOUDSTART_REDIS_TRUSTKEYSTORE), trustPw);
            jedisClientConfig = JedisClientConfiguration.builder().useSsl().sslSocketFactory(socketFactory).build();
        } else {
            jedisClientConfig = JedisClientConfiguration.builder().build();
        }
        return new JedisConnectionFactory(rsc, jedisClientConfig);
    }

    public boolean isEncrypted(String message) {
        if (StrUtil.isEmpty(message)) {
            return false;
        } else {
            String trimmedValue = message.trim();
            return trimmedValue.startsWith(this.prefix) && trimmedValue.endsWith(this.suffix);
        }
    }

    public String decrypt(String decryptMessage) {

        int prefixIndex = decryptMessage.indexOf(prefix);
        int suffixIndex = decryptMessage.indexOf(suffix);
        // 还原密文
        decryptMessage = decryptMessage.substring(prefixIndex + prefix.length(), suffixIndex);
        // 还原密码。注意如果需要密钥的这里添加
        return CrytoUtilSimple.decrypt(decryptMessage).trim();
    }

    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);

        // 使用Jackson2JsonRedisSerializer来序列化和反序列化redis的value值
        Jackson2JsonRedisSerializer jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer<>(Object.class);

        ObjectMapper mapper = new ObjectMapper();
        mapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        jackson2JsonRedisSerializer.setObjectMapper(mapper);

        // 使用stringSerializer来序列化和反序列化redis的key值
        DefaultStringSerializer stringSerializer = new DefaultStringSerializer();
        template.setValueSerializer(stringSerializer);
        template.setKeySerializer(stringSerializer);
        template.setHashKeySerializer(stringSerializer);
        template.setHashValueSerializer(stringSerializer);
        template.afterPropertiesSet();
        JedisUtil.INSTANCE.init(template);

        return template;
    }

    @Bean(name = "stringRedisTemplate")
    public StringRedisTemplate stringRedisTemplate(RedisConnectionFactory connectionFactory) {
        StringRedisTemplate stringRedisTemplate = new StringRedisTemplate(connectionFactory);
        JedisUtil.INSTANCE.initString(stringRedisTemplate);
        return stringRedisTemplate;
    }

}
