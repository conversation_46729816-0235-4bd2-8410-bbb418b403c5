package cn.com.cloudstar.rightcloud.module.standard.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.collections.map.LinkedMap;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

/**
 * 采集类型
 *
 * <AUTHOR>
 * 2021-2-25
 */
@AllArgsConstructor
@Getter
public enum CollectorTypeEnum {

    OBS(1,"obs",1),
    MODELARTS(2,"modelarts",1),
    SFS(3,"sfs",1),
    HPC(4,"hpc",2),
    //云硬盘:EVS
    EVS(5,"volume",1),
    //裸金属服务器:BMS
    BMS(6,"pm",1),
    //弹性云服务器
    ECS(7,"ecs-plugin",1),
    //弹性负载均衡:ELB
    ELB(8,"elbv3Instance",1),
    //弹性IP:EIP 弹性ip按带宽计费，取带宽话单能够拿到使用时长，以及对应的带宽大小
    EIP(9,"bandwidth",1),
    HPC_OFFLINE(10,"hpc-offline",2),
    AI_BMS(11,"ai-bms",1);

    // 采集类型
    private Integer code;
    private String message;
    // 采集方式
    private Integer mode;

    private static final Map<Integer, CollectorTypeEnum> lookup = new HashMap<>();

    static {
        for(CollectorTypeEnum e : EnumSet.allOf(CollectorTypeEnum.class)){
            lookup.put(e.getCode(), e);
        }
    }

    public static CollectorTypeEnum find(Integer code){
        return lookup.get(code);
    }
}
