package cn.com.cloudstar.rightcloud.module.standard.service.impl;


import cn.com.cloudstar.rightcloud.module.standard.config.DBContextHolder;
import cn.com.cloudstar.rightcloud.module.standard.config.DynamicDataSource;
import cn.com.cloudstar.rightcloud.module.standard.config.RedisLock;
import cn.com.cloudstar.rightcloud.module.standard.dao.BigScreenHourMapper;
import cn.com.cloudstar.rightcloud.module.standard.dao.BigScreenMapper;
import cn.com.cloudstar.rightcloud.module.standard.pojo.entity.*;
import cn.com.cloudstar.rightcloud.module.standard.service.HpcCollectorService;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.jdbc.BadSqlGrammarException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/5/26 11:15
 * @description
 */
@Service
@Slf4j
public class HpcCollectorServiceImpl implements HpcCollectorService {

    public static final String KEY="view_hpcop_sample_node_info";
    public static final String MONTH_RATE_KEY="view_hpcop_sample_task_info";
    public static final String FILL_RATE="view_hpcop_sample_cluster_info";
    public static final String SUCCESS="success";
    public static final int    MIN=3;
    public static final String SAASSHARE="SAASShare";

    @Resource
    BigScreenMapper bigScreenMapper;
    @Autowired
    MongoTemplate mongoTemplate;
    @Resource
    BigScreenHourMapper bigScreenHourMapper;
    @Autowired
    DynamicDataSource dynamicDataSource;
    @Resource
    private RedisLock redisLock;
    @Autowired
    private StringRedisTemplate redisTemplate;

    @Override
    public void collector() throws SQLException {
        log.info("五分钟定时任务starTime:"+new Date());
        if (Boolean.FALSE.equals(redisTemplate.opsForValue().setIfAbsent(KEY,SUCCESS,MIN,TimeUnit.MINUTES))){
            return;
        }
        List<Ccpdbinfo> dataSource = getDataSource();
        //先移除记录表的数据
        mongoTemplate.remove(new Query(), SampleNodeInfoRecord.class);
        mongoTemplate.remove(new Query(), SampleAllJobStateCountRecord.class);
        mongoTemplate.remove(new Query(), ClusterResourceUsedTotalRecord.class);
        mongoTemplate.remove(new Query(), JobResourceUsedTodayRecord.class);
        for (Ccpdbinfo info : dataSource) {
            if (StringUtils.isEmpty(info.getDbUser()) || StringUtils.isEmpty(info.getDbPassword())) {
                continue;
            }
            try {
                log.info("需要使用的的数据源已经找到,集群是:" + info.getBesunessCategory());
                //创建数据源连接&检查 若存在则不需重新创建
                boolean flag = dynamicDataSource.createDataSourceWithCheck(info);
                if (!flag) {
                    continue;
                }
                //切换到该数据源
                DBContextHolder.setDataSource(info.getBesunessCategory());
                String clusterType = info.getClusterType();
                List<SampleNodeInfoRecord> sampleNodeInfo = getSampleNodeInfo(clusterType,info.getBesunessCategory());
                List<TenantResourceUsedTotalRecord> tenantResourceUsedTotalRecords = getTenantResourceUsedTotal(clusterType,info.getBesunessCategory());
                List<SampleAllJobStateCountRecord> sampleAllJobStateCountRecords = getSampleAllJobStateCount();
                List<ClusterResourceUsedTotalRecord> clusterResourceUsedTotalRecords = getClusterResourceUsedTotal(clusterType);
                List<JobResourceUsedTodayRecord> jobResourceUsedTodayRecords = getJobResourceUsedToday();
                //插入MongoDB
                if (sampleNodeInfo.size() != 0) {
                    mongoTemplate.insertAll(sampleNodeInfo);
                }
                if (tenantResourceUsedTotalRecords.size() != 0) {
                    mongoTemplate.insertAll(tenantResourceUsedTotalRecords);
                }
                if (sampleAllJobStateCountRecords.size() != 0) {
                    mongoTemplate.insertAll(sampleAllJobStateCountRecords);
                }
                if (clusterResourceUsedTotalRecords.size() != 0) {
                    mongoTemplate.insertAll(clusterResourceUsedTotalRecords);
                }
                if (jobResourceUsedTodayRecords.size() != 0) {
                    mongoTemplate.insertAll(jobResourceUsedTodayRecords);
                }
                DBContextHolder.clearDataSource();
            } catch (Exception e) {
                log.info("{}链接失败", info.getDbAddress());
                e.printStackTrace();
                continue;
            } finally {
                dynamicDataSource.CloseConnection(info);
            }
        }
        log.info("五分钟定时任务endTime:" + new Date());
    }

    @Override
    public void getFillRate() throws SQLException {
        log.info("HpcCollectorServiceImpl-getFillRate-实时填充率采集开始时间：{}",new Date());
        if (Boolean.FALSE.equals(redisTemplate.opsForValue().setIfAbsent(FILL_RATE,SUCCESS,MIN,TimeUnit.MINUTES))){
            return;
        }
        List<Ccpdbinfo> dataSource = getDataSource();
        //先移除记录表的数据
        mongoTemplate.remove(new Query(), SampleClusterInfo.class);
        for (Ccpdbinfo info : dataSource) {
            if (StringUtils.isEmpty(info.getDbUser()) || StringUtils.isEmpty(info.getDbPassword())){
                continue;
            }
            try {
                log.info("HpcCollectorServiceImpl-getFillRate-需要使用的的数据源已经找到,集群是:" + info.getBesunessCategory());
                //创建数据源连接&检查 若存在则不需重新创建
                boolean flag = dynamicDataSource.createDataSourceWithCheck(info);
                if (!flag) {
                    continue;
                }
                //切换到该数据源
                DBContextHolder.setDataSource(info.getBesunessCategory());
                String clusterType = info.getClusterType();
                List<SampleClusterInfo> sampleClusterInfos = getSampleClusterInfo(clusterType);
                //插入MongoDB
                if (sampleClusterInfos.size() != 0) {
                    mongoTemplate.insertAll(sampleClusterInfos);
                }
                DBContextHolder.clearDataSource();
            } catch (Exception e) {
                log.info("HpcCollectorServiceImpl-getFillRate-{}链接失败", info.getDbAddress());
                e.printStackTrace();
                continue;
            }finally {
                dynamicDataSource.CloseConnection(info);
            }
        }
        log.info("HpcCollectorServiceImpl-getFillRate-实时填充率采集结束时间:{}",new Date());
    }

    @Override
    public void getMonthAverageRate()  throws SQLException{
        log.info("HpcCollectorServiceImpl-getMonthAverageRate-五分钟定时任务starTime:"+new Date());
        if (Boolean.FALSE.equals(redisTemplate.opsForValue().setIfAbsent(MONTH_RATE_KEY,SUCCESS,MIN,TimeUnit.MINUTES))){
            return;
        }
        List<Ccpdbinfo> dataSource = getDataSource();
        for (Ccpdbinfo info : dataSource) {
            if (StringUtils.isEmpty(info.getDbUser()) || StringUtils.isEmpty(info.getDbPassword())){
                continue;
            }
            try {
                log.info("HpcCollectorServiceImpl-getMonthAverageRate-需要使用的的数据源已经找到,集群是:" + info.getBesunessCategory());
                //创建数据源连接&检查 若存在则不需重新创建
                boolean flag = dynamicDataSource.createDataSourceWithCheck(info);
                if (!flag) {
                    continue;
                }
                //切换到该数据源
                DBContextHolder.setDataSource(info.getBesunessCategory());
                List<SampleTaskInfo> sampleTaskInfos = getMonthAverageRateInfo(info);
                //插入MongoDB
                if (sampleTaskInfos.size() != 0) {
                    mongoTemplate.insertAll(sampleTaskInfos);
                }
                DBContextHolder.clearDataSource();
            } catch (Exception e) {
                log.info("HpcCollectorServiceImpl-getMonthAverageRate-{}链接失败", info.getDbAddress());
                e.printStackTrace();
                continue;
            }finally {
                dynamicDataSource.CloseConnection(info);
            }
        }
        log.info("HpcCollectorServiceImpl-getMonthAverageRate-五分钟定时任务endTime:"+new Date());
    }


    private List<SampleTaskInfo> getMonthAverageRateInfo(Ccpdbinfo info) {
        Query query = new Query().limit(1);
        Sort sort = Sort.by(Sort.Direction.DESC, "sample_time");
        query.with(sort);
        query.addCriteria(Criteria.where("besuness_category").is(info.getBesunessCategory()));
        List<SampleTaskInfo> sampleTaskInfos = mongoTemplate.find(query, SampleTaskInfo.class);
        Date lastSampleTime;
        if (CollectionUtils.isEmpty(sampleTaskInfos)) {
            lastSampleTime = null;
        }else {
            lastSampleTime = sampleTaskInfos.get(0).getSampleTime();
        }
        //获取视图里面时间是最新的数据
        List<SampleTaskInfo> list = bigScreenMapper.selectSampleTaskInfo(lastSampleTime);
        list.forEach(l -> {
            l.setEntityId(info.getEntityId());
            l.setClusterType(info.getClusterType());
            l.setBesunessCategory(info.getBesunessCategory());});
        log.info("HpcCollectorServiceImpl-getMonthAverageRateInfo-{}-开始采集，采集到{}条", DateUtil.format(new Date(), "yyyyMMddHH"), list.size());
        return list;
    }

    /**
     * 填充率
     * @param clusterType
     * @return
     */
    private List<SampleClusterInfo> getSampleClusterInfo(String  clusterType) {
        //获取视图里面时间是最新的数据
        List<SampleClusterInfo> list = bigScreenMapper.selectSampleClusterInfo();
        list.forEach(l->l.setClusterType(clusterType));
        log.info("HpcCollectorServiceImpl-getSampleClusterInfo-{}-开始采集，采集到{}条", DateUtil.format(new Date(), "yyyyMMddHH"), list.size());
        return list;
    }
    /**
     * 热力图
     * @param clusterType
     * @return
     */
    public List<SampleNodeInfoRecord> getSampleNodeInfo(String  clusterType,String cate) {
        List<SampleNodeInfoRecord> list;
        try{
            log.info("HpcCollectorServiceImpl-getSampleNodeInfo-已升级场景查询");
            list= bigScreenMapper.selectSampleNodeInfoAfter();
            list.forEach(l->l.setFlag(true));
        }catch (BadSqlGrammarException e ){
            log.info("HpcCollectorServiceImpl-getSampleNodeInfo-未升级场景查询");
           list= bigScreenMapper.selectSampleNodeInfo();
            list.forEach(l->l.setFlag(false));
        }catch (Exception e){
            log.error("HPC节点数据采集-HpcCollectorServiceImpl-getSampleNodeInfo-异常：[{}]",e.getMessage());
            list = new ArrayList<>();
        };
        //获取视图里面时间是最新的数据
        list.forEach(l->l.setClusterType(clusterType));
        list.forEach(l->l.setBesunessCategory(cate));
        log.info("{}-开始采集热力图数据，采集到{}条", DateUtil.format(new Date(), "yyyyMMddHH"),list.size());
        return list;
    }

    /**
     * 用户资源累计消耗TOP5
     *
     * @param type
     * @param besunessCategory
     *
     * @return
     */
    public List<TenantResourceUsedTotalRecord> getTenantResourceUsedTotal(String type, String besunessCategory) {
        //获取视图里面时间是最新的数据
        List<TenantResourceUsedTotalRecord> list =new LinkedList<>();
        if (SAASSHARE.equalsIgnoreCase(type)){
            //查询出来的数据
            List<TenantResourceUsedTotalRecord> recordList = bigScreenMapper.selectTenantResourceUsedTotal();
            //上次记录表里面的数据
            List<TenantResourceUsedTotalRecord> all = mongoTemplate.findAll(TenantResourceUsedTotalRecord.class);
            for (TenantResourceUsedTotalRecord record : recordList) {
                all.removeIf(a->(a.getTenantName().equalsIgnoreCase(record.getTenantName()) && StringUtils.equalsIgnoreCase(a.getBesunessCategory(),besunessCategory)) || a.getBesunessCategory() == null);
                record.setBesunessCategory(besunessCategory);
            }
            if (recordList.size() != 0){
                list.addAll(recordList);
            }
            if (all.size() != 0){
                list.addAll(all);
            }
            mongoTemplate.remove(new Query(), TenantResourceUsedTotalRecord.class);
            log.info("{}-开始采集租户资源累积消耗数据，采集到{}条", DateUtil.format(new Date(), "yyyyMMddHH"), recordList.size());
        }
        return list;
    }


    /**
     * 作业状态
     * @return
     */
    public List<SampleAllJobStateCountRecord> getSampleAllJobStateCount() {
        //获取视图里面时间是最新的数据
        List<SampleAllJobStateCountRecord> list = bigScreenMapper.selectSampleAllJobStateCount();
        log.info("{}-开始采集作业状态，采集到{}条", DateUtil.format(new Date(), "yyyyMMddHH"), list.size());
        return list;
    }
    /**
     * 集群累积消耗资源
     * @return
     */
    public List<ClusterResourceUsedTotalRecord> getClusterResourceUsedTotal(String  clusterType) {
        //获取视图里面时间是最新的数据
        List<ClusterResourceUsedTotalRecord> list = bigScreenMapper.selectClusterResourceUsedTotal();
        list.forEach(l->l.setClusterType(clusterType));
        log.info("{}-开始采集集群累积消耗资源，采集到{}条", DateUtil.format(new Date(), "yyyyMMddHH"), list.size());
        return list;
    }
    /**
     * 当日资源消耗数据TOP5
     * @return
     */
    public List<JobResourceUsedTodayRecord> getJobResourceUsedToday() {
        //获取视图里面时间是最新的数据
        List<JobResourceUsedTodayRecord> list = bigScreenMapper.selectJobResourceUsedToday();
        log.info("{}-开始采集当日资源消耗数据，采集到{}条", DateUtil.format(new Date(), "yyyyMMddHH"), list.size());
        return list;
    }


    public List<Ccpdbinfo> getDataSource() {
        return mongoTemplate.findAll(Ccpdbinfo.class);
    }

    public HpcConfigValues getValues() {
        HpcConfigValues values = new HpcConfigValues();
        String name = bigScreenMapper.select1();
        Double cpu = bigScreenMapper.select2();
        Double gpu = bigScreenMapper.select3();
        values.setFirstName(StringUtils.isNotEmpty(name) ? name : "-");
        values.setCpcWeight(cpu != null ? cpu : 1);
        values.setGpcWeight(gpu != null ? gpu : 1);
        return values;
    }

    private String collectorDate() {
        DateTime date = new DateTime();
        date.offset(DateField.HOUR_OF_DAY,-0);
        String fileDate = DateUtil.format(date, "yyyyMMddHHMMSS");
        return fileDate;
    }
}
