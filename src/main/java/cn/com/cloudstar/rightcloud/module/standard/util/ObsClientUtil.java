package cn.com.cloudstar.rightcloud.module.standard.util;

import com.obs.services.ObsClient;
import com.obs.services.ObsConfiguration;
import com.obs.services.exception.ObsException;
import com.obs.services.model.DeleteObjectResult;
import com.obs.services.model.HttpMethodEnum;
import com.obs.services.model.ObjectListing;
import com.obs.services.model.ObsObject;
import com.obs.services.model.PutObjectResult;
import com.obs.services.model.TemporarySignatureRequest;
import com.obs.services.model.TemporarySignatureResponse;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ObsClientUtil {

    /**
     * 获取obsClient
     *
     * @param endPoint
     * @param ak
     * @param sk
     * @return
     */
    public static ObsClient connect(String endPoint, String ak, String sk) {
        try {
            ObsConfiguration config = new ObsConfiguration();
            config.setValidateCertificate(true);
            config.setEndPoint(endPoint);
            // 创建ObsClient实例
            return new ObsClient(ak, sk, config);
        } catch (Exception e) {
            log.error(e.getMessage());
            return null;
        }
    }

    /**
     * 验证桶是否存在
     *
     * @param obsClient
     * @param bucket
     * @return
     * @throws Exception
     */
    public static Boolean exisBucket(ObsClient obsClient, String bucket) {
        // 测试bucket是否存在OBS
        try {
            return obsClient.headBucket(bucket);
        }catch (Exception e){
            return false;
        }
    }

    /**
     * 上传文件
     * @param obsClient
     * @param bucketName
     * @param is
     * @param objectKey
     * @return
     * @throws IOException
     */
    public static Integer uploadFile(ObsClient obsClient, String bucketName, InputStream is, String objectKey) throws IOException {
        PutObjectResult result = null;
        //同名文件可能被覆盖
        result = obsClient.putObject(bucketName, objectKey, is);
        obsClient.close();
        return result.getStatusCode();
    }

    /**
     * 查询桶下全部文件
     * @param obsClient
     * @param bucketName
     * @return
     * @throws IOException
     */
    public static List<ObsObject> getAllFileInfo(ObsClient obsClient, String bucketName) throws IOException {
        ObjectListing objectList = obsClient.listObjects(bucketName);
        List<ObsObject> list = objectList.getObjects();
        obsClient.close();
        return list;
    }

    /**
     * 删除对象
     * @param obsClient
     * @param bucketName
     * @param objectKey
     * @return
     * @throws IOException
     */
    public static Boolean removeFile(ObsClient obsClient, String bucketName,String objectKey) throws IOException {
        DeleteObjectResult result = null;
        result = obsClient.deleteObject(bucketName, objectKey);
        obsClient.close();
        return result.isDeleteMarker();//是否可以被标记为删除
    }

    /**
     * 获取文件对象-下载
     * @param obsClient
     * @param bucketName
     * @param objectKey
     * @return
     */
    public static ObsObject getFile(ObsClient obsClient,String bucketName,String objectKey) {
        try {
            return obsClient.getObject(bucketName, objectKey);
        }catch (ObsException e){
            return null;
        }
    }

    /**
     * 如果是流式文件，返回的链接可以在浏览器预览
     * 如果是非流式文件，返回的链接可以在浏览器里下载文件
     *
     * @param objectKey
     * @return
     * @throws IOException
     */
    //预览授权访问-支持流式文件
    public static String preview(ObsClient obsClient,String bucketName,String objectKey) throws IOException {
        //300有效时间
        TemporarySignatureRequest request = new TemporarySignatureRequest(HttpMethodEnum.GET, 300);
        request.setBucketName(bucketName);
        request.setObjectKey(objectKey);
        TemporarySignatureResponse response = obsClient.createTemporarySignature(request);
        obsClient.close();
        return response.getSignedUrl();
    }
}
