package cn.com.cloudstar.rightcloud.module.standard.service;

import java.sql.SQLException;

/**
 * <AUTHOR>
 * @date 2022/5/7 10:50
 * @description
 */
public interface BigScreenHourService {

    /**
     * 租户15天算力
     * @throws SQLException
     */
    void getFifteenDays() throws SQLException;

    /**
     * 集租户24小时算力
     * @throws SQLException
     */
    void getTwentyFour() throws SQLException;

    /**
     * 热力图
     * @throws SQLException
     */
    void getSampleNodeInfo() throws SQLException;

    /**
     * 当月资源使用排行
     * @throws SQLException
     */
    void getTenantResourceUsedMonth() throws SQLException;

}
