package cn.com.cloudstar.rightcloud.module.standard.util;

import cn.hutool.core.io.IoUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.archivers.tar.TarArchiveEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveInputStream;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.io.FilenameUtils;
import org.springframework.lang.Nullable;

import java.io.*;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.zip.GZIPInputStream;

@Slf4j
public class FileUtil {

    /**
     * Tar文件解压方法
     *
     * @param tarGzFile 要解压的压缩文件名称（绝对路径名称）
     * @param destDir   解压后文件放置的路径名（绝对路径名称）当路径不存在，会自动创建
     * @return 解压出的文件列表
     */
    public static void deCompressGZipFile(String tarGzFile, String destDir) {

        // 建立输出流，用于将从压缩文件中读出的文件流写入到磁盘
        TarArchiveEntry entry = null;
        TarArchiveEntry[] subEntries = null;
        File subEntryFile = null;
        FileOutputStream out = null;
        FileInputStream fis = null;
        GZIPInputStream gis = null;
        TarArchiveInputStream taris = null;
        try {
            fis = new FileInputStream(tarGzFile);
            gis = new GZIPInputStream(fis);
            taris = new TarArchiveInputStream(gis);
            while ((entry = taris.getNextTarEntry()) != null) {
                StringBuilder entryFileName = new StringBuilder();
                entryFileName.append(destDir).append(File.separator).append(entry.getName());
                File entryFile = cn.hutool.core.io.FileUtil.file(entryFileName.toString());
                if (entry.isDirectory()) {
                    if (!entryFile.exists()) {
                        entryFile.mkdir();
                    }
                    subEntries = entry.getDirectoryEntries();
                    for (int i = 0; i < subEntries.length; i++) {
                        OutputStream outFile = null;
                        try {
                            outFile = new FileOutputStream(subEntryFile);
                            subEntryFile = cn.hutool.core.io.FileUtil.file(entryFileName + File.separator + subEntries[i].getName());
                            IOUtils.copy(taris, outFile);
                        } catch (Exception e) {
                            log.error("deCompressing file failed:" + subEntries[i].getName() + "in" + tarGzFile);
                        } finally {
                            IoUtil.close(outFile);
                        }
                    }
                } else {
                    checkFileExists(entryFile);
                    out = new FileOutputStream(entryFile);
                    IOUtils.copy(taris, out);
                    //如果是gz文件进行递归解压
                    if (entryFile.getName().endsWith(".gz")) {
                        deCompressGZipFile(entryFile.getPath(), destDir);
                    }
                }
            }
            //如果需要刪除之前解压的gz文件，在这里进行

        } catch (Exception e) {
            log.warn("decompress failed", e);
        } finally {
            IoUtil.close(out);
            IoUtil.close(fis);
            IoUtil.close(gis);
            IoUtil.close(taris);
        }
    }

    @Nullable
    private static File copyFile(String tarGzFile, File subEntryFile, TarArchiveInputStream taris, StringBuilder entryFileName, TarArchiveEntry subEntries) {
        OutputStream outFile = null;
        try {
            outFile = new FileOutputStream(subEntryFile);
            subEntryFile = cn.hutool.core.io.FileUtil.file(entryFileName + File.separator + subEntries.getName());
            IOUtils.copy(taris, outFile);
        } catch (Exception e) {
            log.error("deCompressing file failed:" + subEntries.getName() + "in" + tarGzFile);
        } finally {
            IoUtil.close(outFile);
        }
        return subEntryFile;
    }

    private static void checkFileExists(File file) {
        //判断是否是目录
        if (file.isDirectory()) {
            if (!file.exists()) {
                file.mkdir();
            }
        } else {
            //判断父目录是否存在，如果不存在，则创建
            if (file.getParentFile() != null && !file.getParentFile().exists()) {
                file.getParentFile().mkdirs();
            }
            try {
                file.createNewFile();
                file.setReadable(true);
                file.setWritable(true);
            } catch (IOException e) {
                log.error(e.getMessage());
            }
        }
    }


    /**
     * 获取到这个路径下的全部zip文件路径及名称
     *
     * @param hwZipPath
     * @return
     */
    public static Map<String, String> zipPaths(String hwZipPath) {
        File file = new File(hwZipPath);
        String fileName = null;
        String filePath = null;
        Map<String, String> fileMap = new HashMap<String, String>();//在这里声明一个Map，将遍历得到的zip文件的名字作为key，zip文件的路径作为value。
        if (file.isDirectory()) {
            for (File files : file.listFiles()) {
                if (files.isDirectory()) {
                    for (File f : files.listFiles()) {
                        if (f.isDirectory()) {
                            for (File ff : f.listFiles()) {
                                fileName = ff.getName();
                                filePath = ff.getPath();
                                fileMap.put(fileName, filePath);
                            }
                        } else {
                            fileName = f.getName();
                            filePath = f.getPath();
                            fileMap.put(fileName, filePath);
                        }
                    }
                } else {
                    fileName = files.getName();
                    filePath = files.getPath();
                    fileMap.put(fileName, filePath);
                }
            }
        }
        return fileMap;
    }


    public static boolean deleteFile(String filesPath,String filePath) {
        // 删除文件
        File file1 = cn.hutool.core.io.FileUtil.file(filePath);
        cn.hutool.core.io.FileUtil.del(file1);
        // 删除文件夹
        File file = cn.hutool.core.io.FileUtil.file(filesPath);
        return deleteFile(file);
    }

    private static boolean deleteFile(File file){
        File[] files = file.listFiles();
        for(File deleteFile : files){
            if(deleteFile.isDirectory()){
                //如果是文件夹，则递归删除下面的文件后再删除该文件夹
                if(!deleteFile(deleteFile)){
                    //如果失败则返回
                    return false;
                }
            } else {
                if(!cn.hutool.core.io.FileUtil.del(deleteFile)){
                    //如果失败则返回
                    return false;
                }
            }
        }
        cn.hutool.core.io.FileUtil.del(file);
        return true;
    }


}
