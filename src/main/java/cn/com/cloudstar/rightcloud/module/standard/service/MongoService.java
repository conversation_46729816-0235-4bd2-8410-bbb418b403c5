package cn.com.cloudstar.rightcloud.module.standard.service;

import cn.com.cloudstar.rightcloud.module.standard.pojo.dto.Collector;
import cn.com.cloudstar.rightcloud.module.standard.pojo.dto.SdrCollectionRecord;

import java.util.List;

public interface MongoService {

    void find();

    //新增采集记录
    void insertCollectorRecord(SdrCollectionRecord sdrCollectionRecord);

    //批量新增采集明细
    void insertCollector(List<Collector> collectorList);

    // 通过文件名字校验话单是否被采集过
    Boolean existsCollectorByFileName(String fileName);

    // 通过envId,采集类型查询
    SdrCollectionRecord getRecordByEnvIdAndType(Long envId, Integer type);

    // 通过 时间,采集类型,envId 验证是否已经被采集过
    Boolean existsCollectorByFileDateAndTypeAndEnvId(String fileDate, Integer type, Long envId);

    /**
     * 查询最后一条采集时间
     *
     * @return
     */
    String getSdrCollectionRecordsLastTime();
}
