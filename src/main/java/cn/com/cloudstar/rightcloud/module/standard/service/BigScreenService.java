package cn.com.cloudstar.rightcloud.module.standard.service;


/**
 * <AUTHOR>
 * @date 2022/5/5 15:02
 * @description
 */
public interface BigScreenService {

    //租户资源累计消耗TOP5
    void getTenantResourceUsedTotal();

    //填充率
    void getSampleClusterInfo();

    //作业状态
    void getSampleAllJobStateCount();

    //集群累积消耗资源
    void getClusterResourceUsedTotal();

    //当日资源消耗数据
    void getJobResourceUsedToday();

    //用户当月资源使用数据
    void getTenantResourceUsedMonth();

    void handle();

}
