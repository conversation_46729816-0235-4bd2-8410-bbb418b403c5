/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.common.constants.msg;

/**
 * DESC:消息代码
 *
 * <AUTHOR>
 * @date 2019/07/15 10:34
 */
public interface MsgCd {
    /* 提示消息 */
    /**
     * info.insert.success=创建成功。
     */
    String INFO_INSERT_SUCCESS = "info.insert.success";

    /**
     * info.register.success=注册成功。
     */
    String INFO_REGISTER_SUCCESS = "info.register.success";

    /**
     * info.update.success=更新成功。
     */
    String INFO_UPDATE_SUCCESS = "info.update.success";

    /**
     * info.publish.success=发布成功。
     */
    String INFO_PUBLISH_SUCCESS = "info.publish.success";

    /**
     * info.delete.success=删除成功。
     */
    String INFO_DELETE_SUCCESS = "info.delete.success";

    /**
     * info.delete.success=移除成功。
     */
    String INFO_REMOVE_SUCCESS = "info.remove.success";

    /**
     * info.relation.success=关联操作成功。
     */
    String INFO_RELATION_SUCCESS = "info.relation.success";

    /**
     * info.approve.success=审核成功。
     */
    String INFO_APPROVE_SUCCESS = "info.approve.success";

    /**
     * info.message.success=发送成功。
     */
    String INFO_MESSAGE_SUCCESS = "info.message.success";

    /**
     * info.inventory.success=盘点成功。
     */
    String INFO_INVENTORY_SUCCESS = "info.inventory.success";

    /**
     * info.in.success=入库成功。
     */
    String INFO_IN_SUCCESS = "info.in.success";

    /**
     * info.relate.success=关联成功。
     */
    String INFO_RELATE_SUCCESS = "info.relate.success";

    /**
     * info.cancel.relate.success=取消关联成功。
     */
    String INFO_CANCEL_RELATE_SUCCESS = "info.cancel.relate.success";

    /**
     * info.out.success=出库成功。
     */
    String INFO_OUT_SUCCESS = "info.out.success";

    /**
     * info.copy.success=复制成功。
     */
    String INFO_COPY_SUCCESS = "info.copy.success";

    /**
     * vm.start.success=启动成功。
     */
    String VM_START_SUCCESS = "vm.start.success";

    /**
     * vm.stop.success=关机成功。
     */
    String VM_STOP_SUCCESS = "vm.stop.success";

    /**
     * vm.restart.success=重启成功。
     */
    String VM_RESTART_SUCCESS = "vm.restart.success";

    /**
     * vm.reconfig.success=调整成功。
     */
    String VM_RECONFIG_SUCCESS = "vm.start.reconfig";

    /**
     * vm.migrate.success=迁移成功。
     */
    String VM_MIGRATE_SUCCESS = "vm.migrate.success";

    /**
     * vm.destory.success=退订成功。
     */
    String VM_DESTORY_SUCCESS = "vm.destory.success";

    /**
     * vm.managed.success=纳管成功。
     */
    String VM_MANAGED_SUCCESS = "vm.managed.success";

    /**
     * vm.rename.success=虚拟机修改名称成功。
     */
    String VM_RENAME_SUCCESS = "vm.rename.success";

    /**
     * task.issued.success=任务下发成功，请到日志中心查看详情。<br>待任务完成后请手动进行刷新操作。
     */
    String TASK_ISSUED_SUCCESS = "task.issued.success";

    /**
     * task.distribute.success=任务提交成功，请稍后查看结果。
     */
    String TASK_DISTRIBUTE_SUCCESS = "task.distribute.success";

    /**
     * image.disabled.success=停用成功。
     */
    String IMAGE_DISABLED_SUCCESS = "image.disabled.success";

    /**
     * info.ticket.success=分配工单成功
     */
    String INFO_TICKET_SUCCESS = "info.ticket.success";

    /**
     * info.vm.res.check=资源检查成功。
     */
    String INFO_VM_RES_CHECK = "info.vm.res.check";

    /**
     * info.ticket.execute=重新执行工单成功。
     */
    String INFO_TICKET_EXECUTE = "info.ticket.execute";

    /**
     * info.ticket.execute=操作成功。
     */
    String INFO_OPERATE_SUCCESS = "info.operate.success";


    String SYSTEM_MESSAGE_COUPON = "system.message.coupon";

    String INFO_MOBILE_GET_SUCCESS = "info.mobile.get.success";

    /**
     * error.edit.failure=编辑失败。
     */
    String ERROR_EDIT_FAILURE = "error.edit.failure";
    /**
     * info.edit.success=编辑成功。
     */
    String INFO_EDIT_SUCCESS = "info.edit.success";

    /**
     * info.department.exist=部门已存在
     */
    String INFO_DEPARTMENT_EXIST = "info.department.exist";

    /* 错误消息 */
    /**
     * error.system.exception
     **/
    String ERROR_SYS_EXCEPTION = "error.system.exception";

    /* 警告消息 */
    /**
     * warning_service_repeat=对不起，该服务不能重复订购。
     */
    String WARNING_SERVICE_REPEAT = "warning_service_repeat";

    /**
     * warning.query.failure=对不起，数据为空。
     */
    String WARNING_QUERY_FAILURE = "warning.query.failure";

    /* 错误消息 */
    /**
     * error.insert.failure=创建失败。
     */
    String ERROR_INSERT_FAILURE = "error.insert.failure";
    /**
     * error.insert.failure.param={0}创建失败。
     */
    String ERROR_INSERT_FAILURE_PARAM = "error.insert.failure.param";
    /**
     * error.message.failure=发送失败。
     */
    String ERROR_MESSAGE_FAILURE = "error.message.failure";

    /**
     * error.query.failure=获取信息失败，数据已被更新或删除。
     */
    String ERROR_QUERY_FAILURE = "error.query.failure";

    /**
     * error.register.failure=注册失败，数据或已存在，请重试。
     */
    String ERROR_REGISTER_FAILURE = "error.register.failure";

    /**
     * error.update.failure=更新失败，数据已被更新或删除。
     */
    String ERROR_UPDATE_FAILURE = "error.update.failure";

    /**
     * error.publish.success=发布失败。
     */
    String ERROR_PUBLISH_FAILURE = "error.publish.failure";

    /**
     * error.delete.failure=删除失败，数据已被更新或删除。
     */
    String ERROR_DELETE_FAILURE = "error.delete.failure";

    /**
     * error.approve.failure=审核失败。
     */
    String ERROR_APPROVE_FAILURE = "error.approve.failure";

    /**
     * error.sendmail.failure=邮件发送失败。
     */
    String ERROR_SENDMAIL_FAILURE = "error.sendmail.failure";

    /**
     * error.inventory.failure=盘点失败。
     */
    String ERROR_INVENTORY_FAILURE = "error.inventory.failure";

    /**
     * error.in.failure=入库失败。
     */
    String ERROR_IN_FAILURE = "error.in.failure";

    /**
     * error.valid.failure=当前配置信息有误，请检查后重试。
     */
    String ERROR_VALID_FAILURE = "error.valid.failure";

    /**
     * error.relate.failure=关联失败。
     */
    String ERROR_RELATE_FAILURE = "error.relate.failure";

    /**
     * error.out.failure=出库失败。
     */
    String ERROR_OUT_FAILURE = "error.out.failure";

    /**
     * error.copy.failure=复制失败。
     */
    String ERROR_COPY_FAILURE = "error.copy.failure";

    /**
     * error.relation.failure=关联操作失败，数据已被更新或删除。
     */
    String ERROR_RELATION_FAILURE = "error.relation.failure";

    /**
     * error.data.exist={0}已经存在，请重新填写。
     */
    String ERROR_DATA_EXIST = "error.data.exist";

    /**
     * error.data.relation={0}，不能进行删除。
     */
    String ERROR_DATA_RELATION = "error.data.relation";

    /**
     * error.data.relation.delete=存在关联关系，不能进行删除。
     */
    String ERROR_DATA_RELATION_DELETE = "error.data.relation.delete";
    /**
     * error.data.relation.delete.param=部分数据无法删除，请检查{0}是否存在关联关系。
     */
    String ERROR_DATA_RELATION_DELETE_PARAM = "error.data.relation.delete.param";
    /**
     * error.data.relation.update=不能进行修改。
     */
    String ERROR_DATA_RELATION_UPDATE = "error.data.relation.update";

    /**
     * error.data.relation={0}，数据已被更新或删除。
     */
    String ERROR_DATA_FAILURE = "error.data.failure";

    /**
     * error.file.oversize=您选择的文件过大，请重新选择。
     */
    String ERROR_FILE_OVERSIZE = "error.file.oversize";

    /**
     * error.plan.published=该名称预案已经发布。
     */
    String ERROR_PLAN_PUBLISHED = "error.plan.published";

    /**
     * error.plan.published=该预案已经停用。
     */
    String ERROR_PLAN_DISABLED = "error.plan.disabled";

    /**
     * error.assess.maxnum={0}不能超过十个。
     */
    String ERROR_ASSESS_MAXNUM = "error.assess.maxnum";

    /**
     * 订单取消失败。
     */
    String ERROR_CANCEL_ORDER = "error.cancel.order";

    /**
     * error.vm.start=启动失败。
     */
    String ERROR_VM_START = "error.vm.start";

    /**
     * error.vm.stop=关机失败。
     */
    String ERROR_VM_STOP = "error.vm.stop";

    /**
     * error.vm.restart=重启失败。
     */
    String ERROR_VM_RESTART = "error.vm.restart";

    /**
     * error.vm.reconfig=调整失败。
     */
    String ERROR_VM_RECONFIG = "error.vm.reconfig";

    /**
     * error.vm.migrate=迁移失败。
     */
    String ERROR_VM_MIGRATE = "error.vm.migrate";

    /**
     * error.vm.managed=纳管失败。
     */
    String ERROR_VM_MANAGED = "error.vm.managed";

    /**
     * error.vm.scan=扫描失败。
     */
    String ERROR_VM_SCAN = "error.vm.scan";

    /**
     * error.vm.destory=退订失败。
     */
    String ERROR_VM_DESTORY = "error.vm.destory";

    /**
     * error.vm.rename=虚拟机修改名称失败。
     */
    String ERROR_VM_RENAME = "error.vm.rename";

    /**
     * error.task.issued=任务发送失败。
     */
    String ERROR_TASK_ISSUED = "error.task.issued";
    /**
     * error.image.disabled=停用失败。
     */
    String ERROR_IMAGE_DISABLED = "error.image.disabled";

    /**
     * error.recover.busy={0}，请等待。
     */
    String ERROR_RECOVER_BUSY = "error.recover.busy";

    /**
     * error.ip.unreachable=IP地址连接失败，无法启动远程连接。
     */
    String ERROR_IP_UNREACHABLE = "error.ip.unreachable";

    /* 警告消息 */
    /**
     * warning_ippool_repeat=对不起，该IP不能重复添加到资源池。
     */
    String WARNING_IPPOOL_REPEAT = "warning_ippool_repeat";

    /* 警告消息 */
    /**
     * warning_ip_repeat=对不起，该IP已经添加到资源。
     */
    String WARNING_IP_REPEAT = "warning_ip_repeat";

    /**
     * error.ticket.allocate=分配工单失败。
     */
    String ERROR_TICKET_ALLOCATE = "error.ticket.allocate";

    /**
     * error.vm.res.check=资源不足。
     */
    String ERROR_VM_RES_CHECK = "error.vm.res.check";

    /**
     * error.biz.user.check=没有用户关联到所选业务名称。
     */
    String ERROR_BIZ_USER_CHECK = "error.biz.user.check";

    /**
     * error.ticket.execute=重新执行工单失败。
     */
    String ERROR_TICKET_EXECUTE = "error.ticket.execute";

    /**
     * error.operate.failure=操作失败。
     */
    String ERROR_OPERATE_FAILURE = "error.operate.failure";

    /**
     * warning.mgtobjres.failure=该租户尚未关联资源。
     */
    String WARNING_MGTOBJRES_FAILURE = "warning.mgtobjres.failure";

    /**
     * error.operate.failure=获取虚拟化环境失败。
     */
    String ERROR_VE_FAILURE = "error.ve.failure";

    /**
     * error.not.support=暂不支持当前类型
     */
    String ERROR_NOT_SUPPORT = "error.not.support";

    /**
     * error.env.exclusive.can.not.share=当前云环境为独占模式，不支持再次分配
     */
    String ERROR_ENV_EXCLUSIVE_CAN_NOT_SHARE = "error.env.exclusive.can.not.share";

    /**
     * error.env.share.can.not.modify.mode=云环境以共享模式分配到当前组织，只能以共享模式再次分配
     */
    String ERROR_ENV_SHARE_CAN_NOT_MODIFY_MODE = "error.env.share.can.not.modify.mode";

    /**
     * error.env.exclusive.can.not.share=当前云环境为独占模式，资源不可移除
     */
    String ERROR_ENV_EXCLUSIVE_CAN_NOT_REMOVE = "error.env.exclusive.can.not.remove";

    /**
     * error.env.exclusive.can.not.share=当前云环境为独占模式，资源不可移动
     */
    String ERROR_ENV_EXCLUSIVE_CAN_NOT_MOVE = "error.env.exclusive.can.not.move";

    /**
     * error.self.inst.res.forbidden=当前资源与自服务实例关联，不支持分配
     */
    String ERROR_SELF_INST_RES_FORBIDDEN = "error.self.inst.res.forbidden";

    /**
     * INFO_GIFT_CARD_GEN_SUCCESS
     */
    String INFO_GIFT_CARD_GEN_SUCCESS = "info_gift_card_gen_success";
    String ERROR_PARAMETER_WRONG = "error_parameeter_wrong";
    String LOGIN_SUCCESS = "user.login.success";
    String LOGIN_FAILED = "user.login.failed";
    String LOGOUT_SUCCESS = "user.logout.success";

    String ERROR_TRIAL_HOST_CREATE = "error.vm.trial.failure";
    String ERROR_VM_TRIAL_RETRY_FAILURE = "error.vm.trial.retry.failure";
    String ERROR_TRIAL_HOST_ENV_ABSENT = "error.vm.trial.env.absent";

    /**
     * ldap info
     */
    String LDAP_CONNECT_SUCCESS = "ldap.connect.success";
    String LDAP_CONNECT_FAILURE = "ldap.connect.failure";

    /**
     * %s不存在或已被删除，请刷新页面后重试。
     */
    String ERROR_RES_NOT_FOUND = "error.res.not.found";

    /**
     * 远程调用MQ失败
     */
    String ERROR_MQ_CONNECT = "error.mq.connect";

    /**
     * 非运行中或已停止状态的实例，不可执行变更%s操作
     */
    String ERROR_OPERATE_FORBIDDEN = "error.operate.forbidden";

    /**
     * 配额控制已%s
     */
    String QUOTA_CTRL = "info.quota.ctrl";

    /**
     * 参数校验不通过
     */
    String PARAM_NOT_VALID_ERROR = "param.not.valid.error";

    /**
     * 用户已被锁定
     */
    String USER_LOCK_ERROR = "user.lock.error";

    /**
     * 存在重复的账户&组织OU信息：[]
     */
    String ACCOUNT_AND_OU_REPEAT = "account.and.ou.repeat";

    /**
     * HEAD版本不支持创建实例
     */
    String ERROR_MSG_00001 = "error.msg.00001";

    /**
     * 所选择的主机模板不支持当前云环境
     */
    String ERROR_MSG_00002 = "error.msg.00002";

    /**
     * 仅VMware环境支持克隆, 当前云环境类型为%s
     */
    String ERROR_MSG_00003 = "error.msg.00003";

    /**
     * 暂不支持该操作
     */
    String ERROR_MSG_00004 = "error.msg.00004";

    /**
     * 当前云环境不支持配置变更
     */
    String ERROR_MSG_00005 = "error.msg.00005";

    /**
     * 该实例未关联任何分区，不支持配置变更
     */
    String ERROR_MSG_00006 = "error.msg.00006";

    /**
     * %s不能为空
     */
    String ERROR_MSG_00007 = "error.msg.00007";

    /**
     * 项目已有此共享云环境
     */
    String ERROR_MSG_00008 = "error.msg.00008";

    /**
     * 云环境共享当前仅支持共享（share）, 独享（exclusive）两种模式
     */
    String ERROR_MSG_00009 = "error.msg.00009";

    /**
     * 项目没有此云环境，请先关联云环境
     */
    String ERROR_MSG_00010 = "error.msg.00010";

    /**
     * 只支持playbook类型脚本
     */
    String ERROR_MSG_00011 = "error.msg.00011";

    /**
     * 手机号重复
     */
    String ERROR_MSG_00012 = "error.msg.00012";

    /**
     * 邮箱重复
     */
    String ERROR_MSG_00013 = "error.msg.00013";

    /**
     * 所选择的角色与所选择的组织未关联
     */
    String ERROR_MSG_00014 = "error.msg.00014";

    /**
     * 仅VMware环境和FusionCompute支持创建存储类型，当前云环境类型为%s
     */
    String ERROR_MSG_00015 = "error.msg.00015";

    /**
     * 仅VMware和FusionCompute环境支持编辑存储类型，当前云环境类型为%s
     */
    String ERROR_MSG_00016 = "error.msg.00016";

    /**
     * 仅VMware环境支持当前操作
     */
    String ERROR_MSG_00017 = "error.msg.00017";
    /**
     * 云环境不存在
     */
    String CLOUD_ENV_NOT_EXIST = "cloud.env.not.exist";

    /**
     * res param error
     */
    String RES_PARAM_STATUS_ERROR = "res.param.status.error";

    /**
     * 只读资源无法移除
     */
    String ERROR_READONLY_RES_CANNOT_REMOVE = "error.readonly.res.cannot.remove";

    /**
     * 只读资源无法编辑
     */
    String ERROR_READONLY_RES_CANNOT_EDIT = "error.readonly.res.cannot.remove";

    /**
     * 环境纳管需要保留具有管理员权限的项目
     */
    String ERROR_MSG_00018 = "error.msg.00018";

    /**
     * 当前项目已按云环境接入，请先删除云环境后再删除该项目
     */
    String ERROR_MSG_00019 = "error.msg.00019";

    /**
     * 当前许可证不支持该云环境，如有需要请联系管理员。
     */
    String ERROR_MSG_00020 = "error.msg.00020";

    /**
     * %s已存在，请检查后重试
     */
    String ERROR_MSG_00021 = "error.msg.00021";

    /**
     * 平台已接入当前区域下的云环境，请先删除该区域下所有已纳管的云环境后重试
     */
    String ERROR_MSG_00022 = "error.msg.00022";
    /**
     * 您试图删除的存储桶不是空的。
     */
    String BUCKET_DELETE_ERROR = "bucket.delete.error";

    /**
     * 自服务主机续订请前往（自服务-服务实例）菜单
     */
    String VM_RENEW_WARN = "vm.renew.warn";

    /**
     * 关联到组织上的环境，再分配时不支持独立使用模式
     */
    String ERROR_MSG_00023 = "error.msg.00023";

    /**
     * 用户名重复
     */
    String ERROR_MSG_00024 = "error.msg.00024";

    /**
     * 该用户无组织
     */
    String ERROR_MSG_00025 = "error.msg.00025";
    /**
     * 所选角色不存在
     */
    String ERROR_MSG_00031 = "error.msg.00031";
    /**
     * 解密参数异常
     */
    String DECRYPT_PARAMS_FAILURE = "decrypt.params.failure";

    /**
     * 类型族  param.valid.cuevalue.range = cueValue参数范围0-10000000之间
     */
    String VALID_CUEVALUE_RANGE = "param.valid.cuevalue.range";

    /**
     * 类型族  param.valid.name.not.empty=显示名不为空
     */
    String VALID_SPEC_REF_VALUE_NAME_NOT_EMPTY = "param.valid.name.not.empty";

    /**
     * 类型族 param.valid.value.not.empty=值不能为空
     */
    String VALID_SPEC_REF_VALUE_VALUE_NOT_EMPTY = "param.valid.value.not.empty";

    /**
     * 暂不支持该文件类型上传
     */
    String FILE_TYPE_ERROR = "file.type.error";

    /**
     * 文件过大，无法上传
     */
    String FILE_SIZE_ERROR = "file.size.error";

    /**
     * 最多上传5个附件
     */
    String FILE_FIVE_SUM_ERROR = "file.five.sum.error";

    /**
     * 最多上传20个附件
     */
    String FILE_TWENTY_SUM_ERROR = "file.twenty.sum.error";

    /**
     * 参数 产品名称校验 param.valid.productName=名称在1-25字之间
     */
    String PARAM_VALID_PRODUCT_NAME = "param.valid.productName";
    /**
     * 参数 产品名图标 param.valid.servcieIconPath=图标地址太长
     */
    String PARAM_VALID_SERVCIE_ICON_PATH = "param.valid.servcieIconPath";

    /**
     * 参数 产品代码 param.valid.productCode=产品代码格式错误
     */
    String PARAM_VALID_PRODUCT_CODE = "param.valid.productCode";

    /**
     * 参数 产品代码 param.valid.productCode.exist=产品代码已存在
     */
    String PARAM_VALID_PRODUCT_CODE_EXIST = "param.valid.productCode.exist";

    /**
     * 参数 产品名描述 param.valid.productDesc=产品描述太长
     */
    String PARAM_VALID_PRODUCT_DESC = "param.valid.productDesc";
    /**
     * 产品不存在 error.product.not.exist=产品不存在
     */
    String ERROR_PRODUCT_NOT_EXIST = "error.product.not.exist";
    /**
     * error.product.no.template = 没有配置产品模板或模板已禁用
     */
    String ERROR_PRODUCT_NO_TEMPLATE = "error.product.no.template";
    /**
     * error.product.using=产品已上架，请先下架
     */
    String ERROR_PRODUCT_USING = "error.product.using";
    /**
     * error.product.no.price=当前产品的资源定价状态为未定价，请确认后重试！
     */
    String ERROR_PRODUCT_NO_PRICE = "error.product.no.price";
    /**
     * error.product.no.sfs=高性能计算上架前，请检查弹性文件服务产品是否正常！
     */
    String ERROR_PRODUCT_NO_SFS = "error.product.no.sfs";
    /**
     * error.product.no.dmeosp=HPC共享资源池-二期上架前，请检查DME-OceanStor Pacific产品是否正常！
     */
    String ERROR_PRODUCT_NO_DME_OSP = "error.product.no.dmeosp";
    /**
     * error.product.sfs.no.price=高性能计算上架前，请检查弹性文件服务产品是否正常！
     */
    String ERROR_PRODUCT_SFS_NO_PRICE = "error.product.sfs.no.price";
    /**
     * error.product.dmeops.no.price=高性能计算上架前，请检查弹性文件服务产品是否正常！
     */
    String ERROR_PRODUCT_DME_OSP_NO_PRICE = "error.product.dmeops.no.price";
    /**
     * error.product.no.obs="ModelArts上架前，请检查对象存储产品是否正常！"
     */
    String ERROR_PRODUCT_NO_OBS = "error.product.no.obs";
    /**
     * error.product.obs.no.price="ModelArts上架前，请检查对象存储产品是否正常！"
     */
    String ERROR_PRODUCT_OBS_NO_PRICE = "error.product.obs.no.price";
    /**
     * error.product.no.catalog=没有配置产品类别
     */
    String ERROR_PRODUCT_NO_CATALOG = "error.product.no.catalog";
    /**
     * error.product.template.no.exist=产品模板不存在
     */
    String ERROR_PRODUCT_TEMPLATE_NO_EXIST = "error.product.template.no.exist";
    /**
     * 参数不合法
     */
    String PARAMS_NOT_LEGALITY = "params.not.legality";

    /**
     * 文件名重复
     */
    String FILE_REPEAT_ERROR = "file.repeat.error";

    /**
     * 产品模板重复
     */
    String TEMPLATE_NAME_REPEAT = "template.name.repeat";

    /**
     * hpc.cluster.no.exist =HPC集群不存在
     */
    String HPC_CLUSTER_NO_EXIST = "hpc.cluster.no.exist";

    /**
     * error.otherapi.curl =调用第三方接口失败
     */
    String ERROR_OTHERAPI_CURL = "error.otherapi.curl";

    /**
     * error.product.deleting = 退订中
     */
    String ERROR_PRODUCT_DELETING = "error.product.deleting";
    /**
     * error.product.deleted = 已退订
     */
    String ERROR_PRODUCT_DELETED = "error.product.deleted";
    /**
     * 当前用户无权查看
     */
    String NOT_HAVE_PERMISSION_VIEW = "not.have.permission.view";

    /**
     * 当前用户无权操作
     */
    String NOT_HAVE_PERMISSION_OPERATE = "not.have.permission.operate";

    /**
     * 用户不存在
     */
    String USER_DOES_NOT_EXIST = "user.does.not.exist";

    /**
     * 密钥不存在
     */
    String SECRET_KEY_DOES_NOT_EXIST = "secret.key.does.not.exist";

    /**
     * 密钥下载失败!
     */
    String KEY_DOWNLOAD_FAILED = "key.download.failed";


    /**
     * 密钥创建次数过多!
     */
    String TOO_MANY_KEY_CREATIONS = "too.many.key.creations";

    /**
     * 不能重复下载!
     */
    String CANNOT_DOWNLOAD_REPEATEDLY = "cannot.download.repeatedly";

    /**
     * 未获取到当前登录用户信息
     */
    String THE_CURRENT_LOGGED_IN_USER_INFORMATION_WAS_NOT_OBTAINED = "the.current.logged.in.user.information.was.not.obtained";

    /**
     * 结束时间应大于开始时间
     */
    String THE_END_TIME_SHOULD_BE_GREATER_THAN_THE_START_TIME = "the.end.time.should.be.greater.than.the.start.time";

    /**
     * 结束时间应大于当前时间
     */
    String THE_END_TIME_SHOULD_BE_GREATER_THAN_THE_CURRENT_TIME = "the.end.time.should.be.greater.than.the.current.time";

    /**
     * 最小购买金额需大于最小冻结金额
     */
    String MINIMUM_PURCHASE_FROZEN = "the.minimum.purchase.amount.must.be.greater.than.the.minimum.frozen.amount";

    /**
     * 请检查最小购买金额设值范围[0,100000]
     */
    String MIN_AMOUNT_RANGE = "min.amount.range";

    /**
     * 请检查最小冻结金额设值范围[-100000,10000]
     */
    String MIN_FROZEN_AMOUNT_RANGE = "min.frozen.amount.range";

    /**
     * 删除失败，该产品关联产品限额，请删除产品限额后重试。
     */
    String ERROR_MSG_00026 = "error_msg_00026";

    /**
     * 开始时间应大于当前时间
     */
    String THE_START_TIME_SHOULD_BE_GREATER_THAN_THE_CURRENT_TIME = "the.start.time.should.be.greater.than.the.current.time";

    /**
     * 无权限操作接口
     */
    String AUTHORIZE_FAILURE = "operate.interface.failure";

    /**
     * 该用户名已被使用。
     */
    String USER_ACCOUNT_USED = "user.account.used";

    /**
     * 获取平台保留账号&组织OU配置失败，请联系系统管理员确认平台保留账号&组织OU配置是否正确。
     */
    String PLATFORM_RETENTION_CONFIGURATION_ABNORMAL = "platform.retention.configuration.abnormal";

    /**
     * 创建失败，最多只支持三个运营实体。
     */
    String ERROR_ENTITY_00001 = "error_entity_00001";

    /**
     * 删除失败，该运营实体下有账户存在费用数据。
     */
    String ERROR_ENTITY_00002 = "error_entity_00002";

    /**
     * 关联失败，该产品已关联运营实体，不可重复关联。
     */
    String ERROR_ENTITY_00003 = "error_entity_00003";

    /**
     * 删除失败，默认运营实体不能被删除。
     */
    String ERROR_ENTITY_00004 = "error_entity_00004";

    /**
     * 配置通过！
     */
    String ERROR_ENTITY_00005 = "error_entity_00005";

    /**
     * 配置未通过！
     */
    String ERROR_ENTITY_00006 = "error_entity_00006";

    /**
     * 取消关联失败，该产品存在费用数据。
     */
    String ERROR_ENTITY_00007 = "error_entity_00007";

    /**
     * 资源收费规格已发生变化，暂不支持当前操作，请先退订产品
     */
    String CHARGINGTYPE_CHANGE_RELEASE_PRODUCT = "chargingType.change.release.product";


    /**
     * 新密码与旧密码不应该一致
     */
    String THE_OLDPWD_NEWCIPHER_ERROR = "the_oldpwd_newpwd_error";

    /**
     * 关联失败，Modelarts共享资源池与Modelarts专属资源池应属于相同运营实体！
     */
    String ERROR_ENTITY_00008 = "error_entity_00008";

    /**
     * 取消关联失败，该产品下存在套餐包数据。
     */
    String ERROR_ENTITY_00009 = "error_entity_00009";

    /**
     * 删除失败，该运营实体存在关联产品。
     */
    String ERROR_ENTITY_00010 = "error_entity_00010";

    /**
     * 关联失败，暂不支持该类型HPC共享资源池。
     */
    String ERROR_ENTITY_00011 = "error_entity_00011";

    /**
     * 关联失败，请先关联HPC共享资源池。
     */
    String ERROR_ENTITY_00015 = "error_entity_00015";

    /**
     * 请检查产品是否关联模板！
     */
    String ERROR_ENTITY_00016 = "error_entity_00016";

    /**
     * 账户处理中，请稍后重试！
     */
    String ERROR_ENTITY_00017 = "error_entity_00017";

    /**
     * 创建失败，实体名称重复！
     */
    String ERROR_ENTITY_00019 = "error_entity_00019";

    /**
     * 审批失败，该产品的购买限额为{0}元。
     */
    String APPROVAL_LIMIT_MIN_AMOUNT = "approval.limit.min.amount";

    /**
     * 用户名重复，已默认追加后缀。
     */
    String ERROR_ENTITY_00018 = "error_entity_00018";

    /**
     * 创建失败，组织下无此用户！
     */
    String SHARE_QUOTAS_001 = "share_quotas_001";

    /**
     * 创建失败，未查找到文件系统！
     */
    String SHARE_QUOTAS_002 = "share_quotas_002";

    /**
     * 创建失败，组织下无此文件系统！
     */
    String SHARE_QUOTAS_003 = "share_quotas_003";

    /**
     * 创建失败，配额必须大于0！
     */
    String SHARE_QUOTAS_004 = "share_quotas_004";

    /**
     * 创建失败，配额申请容量不可超过SFS文件大小！
     */
    String SHARE_QUOTAS_005 = "share_quotas_005";

    /**
     * 重复的工单分类名称
     */
    String TICKET_CATEGORY_DUPLICATE_NAME = "ticket.category.duplicate.name";

    /**
     * 重复的工单模板名称
     */
    String TICKET_TEMPLATE_DUPLICATE_NAME = "ticket.template.duplicate.name";

    /**
     * 工单名称长度超限制
     */
    String TICKET_CATEGORY_NAME_TOO_LONG = "ticket.category.name.too.long";

    /**
     * 工单类型绑定工单模板不存在
     */
    String TICKET_CATEGORY_BINDING_TEMPLATE_NOT_EXIST = "ticket.category.binding.template.not.exist";

    /**
     * 工单类型绑定的模板未启用
     */
    String TICKET_CATEGORY_BINDING_TEMPLATE_DISABLE = "ticket.category.binding.template.disable";

    /**
     * 工单类型处理人不存在
     */
    String TICKET_CATEGORY_DEAL_NOT_EXIST = "ticket.category.deal.not.exist";

    /**
     * 工单类型绑定的处理人未启用
     */
    String TICKET_CATEGORY_DEAL_DISABLE = "ticket.category.binding.deal.disable";

    /**
     * 工单模板长度超限制
     */
    String TICKET_TEMPLATE_NAME_TOO_LONG = "ticket.template.name.too.long";

    /**
     *修改期间被修改了
     */
    String TICKET_UPDATE_VERSION_UPDATED = "ticket.update.version.updated";

    /**
     * 操作失败，请刷新页面后重试！
     */
    String OPERATION_FAILED_NOT_FOUND = "operation.failed.not.found";


    /**
     * 暂时无法注册用户，已达上限，请联系平台管理员
     */
    String USER_LOGON_UPPER_LIMIT_REACHED = "user.logon.upper.limit.reached";

    /**
     *  公司名称已经被使用
     */
    String CORPORATE_NAME_USED = "corporate_name_used";

    /**
     * 公司和用户不能同名
     */
    String OGR_USER_IDENTICAL = "ogr_user_identical";

    /**
     *  公司和用户名称关联性太强，请修改
     */
    String OGR_USER_STRONG_CORRELATION = "ogr_user_strong_correlation";

    /**
     * 导入文件格式不符合规范，点击下载Excel模板导入
     */
    String IMPORT_FILE_FORM_INCORRECT = "import_file_form_incorrect";

    /**
     * 导入文件内容不符合
     */
    String IMPORT_FILE_CONTENT_INCORRECT = "import_file_content_incorrect";


    /**
     * 国密不存在ccsp_mac字段
     */
    String CCSP_NO_FIELD_CCSP_MAC = "ccsp_no_field_ccsp_mac";

    /**
     * 国密ccsp_mac验证失败
     */
    String CCSP_MAC_VERIFY_FAILED = "ccsp_mac_verify_failed";


    /**
     * 国密升级
     */
    String CCSP_UPGRADING = "ccsp.upgrading";

    /**
     *  资源池剩余卡数不足
     */
    String MC_APPLY_RESOURCES_INSUFFICIENT = "mc_apply_resources_insufficient";

    /**
     *  请求不合法
     */
    String REQUEST_IS_ILLEGAL = "request_is_illegal";

    /**
     *当前角色有用户存在未审批流程
     */
    String ROLE_HAVE_UNAPPROVED_PROCESS = "role.have.unapproved.process";

    /**
     *当前角色有用户存在未审批流程
     */
    String SMS_LIMIT = "sms.limit";

    String ORDER_PENDING_ERROR = "order.pending.error";
    String SET_FAILED = "set.endtime.failed";

    /**
     * 认证失败，请求信息不能为空
     */
    String COMPANY_AUTH_ERROR_1 = "company.auth.error.1";
    /**
     * 认证失败，组织SID信息不能为空
     */
    String COMPANY_AUTH_ERROR_2 = "company.auth.error.2";
    /**
     * 认证失败，请先完成个人认证
     */
    String COMPANY_AUTH_ERROR_3 = "company.auth.error.3";
    /**
     * 子用户不能进行企业认证
     */
    String COMPANY_AUTH_ERROR_4 = "company.auth.error.4";
    /**
     * 认证失败，公司不存在
     */
    String COMPANY_AUTH_ERROR_5 = "company.auth.error.5";
    /**
     * 企业已认证成功,不支持变更为其他认证
     */
    String COMPANY_AUTH_ERROR_6 = "company.auth.error.6";
    /**
     * 企业认证中,不允许重新进行认证
     */
    String COMPANY_AUTH_ERROR_7 = "company.auth.error.7";
    /**
     * 认证失败，请检查企业营业执照图片是否已上传
     */
    String COMPANY_AUTH_ERROR_8 = "company.auth.error.8";
    /**
     * 认证失败，身份类型不能为空
     */
    String COMPANY_AUTH_ERROR_9 = "company.auth.error.9";
    /**
     * 认证失败,法定代表人不能为空
     */
    String COMPANY_AUTH_ERROR_10 = "company.auth.error.10";
    /**
     * 认证失败,被授权人姓名不能为空
     */
    String COMPANY_AUTH_ERROR_11 = "company.auth.error.11";
    /**
     * 认证失败,被授权人身份证号码不能为空
     */
    String COMPANY_AUTH_ERROR_12 = "company.auth.error.12";
    /**
     * 认证失败，请输入正确的被授权人身份证信息
     */
    String COMPANY_AUTH_ERROR_13 = "company.auth.error.13";
    /**
     * 认证失败，被授权人身份证是否已上传
     */
    String COMPANY_AUTH_ERROR_14 = "company.auth.error.14";
    /**
     * 认证失败，请检查授权书是否已上传
     */
    String COMPANY_AUTH_ERROR_15 = "company.auth.error.15";
    /**
     * 认证失败，请输入正确的法人身份证号码信息
     */
    String COMPANY_AUTH_ERROR_16 = "company.auth.error.16";
    /**
     * 认证失败，法人身份证是否已上传
     */
    String COMPANY_AUTH_ERROR_17 = "company.auth.error.17";
    /**
     * 认证失败，法人身份证号码应为空
     */
    String COMPANY_AUTH_ERROR_18 = "company.auth.error.18";
    /**
     * 被授权人姓名长度为2-16个字符,并且不能包含特殊符号
     */
    String COMPANY_AUTH_ERROR_19 = "company.auth.error.19";
    /**
     * 认证失败，身份证未成年
     */
    String COMPANY_AUTH_ERROR_20 = "company.auth.error.20";

    /**
     *  表格内容为空
     */
    String TABLE_CONTENT_NOT_EXIT = "table.content.not.exit";

    String OPERATE_FAILURE = "error.msg.00035";

    /**
     *  越权操作
     */
    String ULTRA_VIRES_OPERATE = "ultra.vires.operate";

    /**
     * 处理人信息错误！
     */
    String PROCESSING_PERSON_INFORMATION_ERROR = "processing.person.information.error";

    /**
     * 模板信息错误！
     */
    String TEMPLATE_INFORMATION_ERROR = "template.information.error";

    /**
     * 下载密码获取失败，请重试！
     */
    String ERROR_MSG_00036 = "error.msg.00036";

    /**
     * 服务器繁忙，请稍后重试!
     */
    String SERVER_BUSY = "server.busy";

    /**
     * 请接入【OceanStor】后再操作！
     */
    String OCEANSTOR_NOT_ACCESS = "oceanstor.not.access";

    /**
     * 请启用【OceanStor】原生调用后再操作！
     */
    String oceanstor_not_enable_native_calls = "oceanstor.not.enable.native.calls";

    /**
     * 购买产品与云环境不匹配
     */
    String PRODUCT_AND_ENV_NOT_MATE = "product.and.env.not.mate";

    /**
     *  请勿重复启用！
     */
    String REPEAT_ENABLE = "repeat.enable";

    /**
     *  请先开通昇腾Modelarts共享资源池！
     */
    String PLEASE_OPEN_MA_RESOURCE = "please.open.ma.resource";

    /**
     *  账户已经欠费，请先充值！
     */
    String ACCOUNT_ARREARS = "account.arrears";

    /**
     *  账户已经被冻结，请先解冻！
     */
    String ACCOUNT_FREEZE = "account.freeze";

    /**
     *  租户状态正在同步中，请勿重复同步!
     */
    String TENANT_STATUS_SYNCING = "tenant.status.syncing";
}

