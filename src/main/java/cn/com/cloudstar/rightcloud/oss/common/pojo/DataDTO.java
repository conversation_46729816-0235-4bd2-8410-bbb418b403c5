/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.common.pojo;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2021/12/9 21:11
 */
@NoArgsConstructor
@Data
public class DataDTO {
    private HpcDTO hpc;
    @JSONField(name = "HPC-SAAS")
    private HpcDTO hpc_saas;

    @J<PERSON><PERSON>ield(name = "HPC-OFFLINE")
    private HpcDTO hpcOffline;

    private SfsDTO sfs;
    @JSONField(name = "SFS2.0")
    private SfsDTO sfs2;
    @JSONField(name = "DME-OSP")
    private SfsDTO dmeOsp;
    @JSONField(serialize = false)
    public HpcDTO realHpcDTO() {
        if (hpcOffline != null) {
            return hpcOffline;
        }
        return Objects.nonNull(hpc) ? hpc : hpc_saas;
    }
    @JSONField(serialize = false)
    public SfsDTO realSfsDTO() {
        if (dmeOsp != null) {
            return dmeOsp;
        }
        return Objects.nonNull(sfs) ? sfs : sfs2;
    }

    /**
     * 订单价格设置为空
     */
    @JSONField(serialize = false)
    public void setBillingPrices(List<BillingPricesDTO> billingPricesList) {
        if(billingPricesList == null){
            billingPricesList = new ArrayList<>();
        }
        if (hpc != null) {
            hpc.setBillingPrices(billingPricesList);
        }
        if (hpc_saas != null) {
            hpc_saas.setBillingPrices(billingPricesList);
        }
        if (hpcOffline != null) {
            hpcOffline.setBillingPrices(billingPricesList);
        }
        if (sfs != null) {
            sfs.setBillingPrices(billingPricesList);
        }
        if (sfs2 != null) {
            sfs2.setBillingPrices(billingPricesList);
        }
        if (dmeOsp != null) {
            dmeOsp.setBillingPrices(billingPricesList);
        }
    }


    /**
     * 订单价格设置为空
     */
    @JSONField(serialize = false)
    public List<BillingPricesDTO> getBillingPrices() {
        if (hpc != null) {
            return hpc.getBillingPrices();
        }
        if (hpc_saas != null) {
           return hpc_saas.getBillingPrices();
        }
        if (hpcOffline != null) {
            return hpcOffline.getBillingPrices();
        }
        if (sfs != null) {
            return sfs.getBillingPrices();
        }
        if (sfs2 != null) {
            return sfs2.getBillingPrices();
        }
        if (dmeOsp != null) {
            return dmeOsp.getBillingPrices();
        }
        return new ArrayList<>();
    }
}
