package cn.com.cloudstar.rightcloud.oss.common.pojo;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * Ldap用户同步
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-16
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LdapSyncRequest implements Serializable {

    /**
     * orgSid必须
     */
    @NonNull
    private Long orgId;

    private String poolUuid;

    /**
     * 是否抛出异常（暂未实现）
     */
    private Boolean isReturnException = false;

    /**
     * 非必填，同步的用户id，传入此参数则只同步指定用户
     */
    private List<Long> userIds;

    /**
     * 非必填，同步的HPC businessCategory，传入此参数则只激活指定资源
     */
    private List<String> businessCategoryList;

    /**
     * 需要从ldap中删除的businessCategory，目前只能传入一个元素
     */
    private List<String> removeBusinessCategoryList;

    /**
     * 非必填
     * true：异步方式执行
     * false：同步方式执行
     */
    private Boolean isAsync = false;

    /**
     * 是否ccp激活
     */
    private Boolean isCcpActive = true;
    /**
     * 是否检查LDAP同步用户
     */
    private Boolean ccpUserActiveCheck ;
}
