package cn.com.cloudstar.rightcloud.oss.common.pojo;

import org.springframework.util.Assert;

import java.util.List;

public class RightCloudTrunc extends AbstractRightCloudAggregationExpression {

	private RightCloudTrunc(Object value) {
		super(value);
	}

	@Override
	protected String getMongoMethod() {
		return "$trunc";
	}

	public static RightCloudTrunc truncValueOf(List<Object> listValue) {

		Assert.notNull(listValue, "expression must not be null!");
		return new RightCloudTrunc(listValue);
	}
}
