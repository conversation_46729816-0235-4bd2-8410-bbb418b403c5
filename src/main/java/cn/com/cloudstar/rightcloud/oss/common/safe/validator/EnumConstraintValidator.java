/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.common.safe.validator;

import java.util.List;
import java.util.Objects;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

import cn.hutool.core.util.ArrayUtil;

import cn.com.cloudstar.rightcloud.oss.common.safe.EnumValue;

/**
 * The type MobileConstraintValidator.
 * <p>
 *
 * <AUTHOR>
 * @date 2019/8/8
 */
public class EnumConstraintValidator implements ConstraintValidator<EnumValue, Object> {

    private String[] strValues;

    @Override
    public void initialize(EnumValue constraintAnnotation) {
        strValues = constraintAnnotation.strValues();
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        if (Objects.isNull(value)) {
            return true;
        }

        if (value instanceof String) {
            if (validateString(value)) {
                return true;
            }
        }else if (value instanceof List){
            return validateList((List<String>) value);
        }
        return false;

    }

    private boolean validateString(Object value) {
        for (String s : strValues) {
            if (s.equals(value)) {
                return true;
            }
        }
        return false;
    }

    private boolean validateList(List<String> value) {
        if (ArrayUtil.isNotEmpty(strValues)) {
            for (String str : value) {
                Boolean flag = false;
                for (String str1 : strValues) {
                    if (str1.equals(str)) {
                        flag = true;
                    }
                }
                if (flag) {
                    continue;
                } else {
                    return flag;
                }
            }
            return true;
        } else {
            return false;
        }
    }
}
