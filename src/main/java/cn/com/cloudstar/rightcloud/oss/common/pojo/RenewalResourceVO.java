/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.common.pojo;

import java.math.BigDecimal;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @createDate 2020/05/29 11:53
 */
@ApiModel("续订请求")
@Data
public class RenewalResourceVO {
    @ApiModelProperty("资源id")
    private String resourceId;

    @ApiModelProperty("平台主键")
    private String primaryKey;

    @ApiModelProperty("时长")
    @NotNull
    private Integer period;

    @ApiModelProperty(value = "优惠券id")
    private Long couponSid;

    @ApiModelProperty("产品价格")
    @NotNull
    private BigDecimal serviceAmount;

    @ApiModelProperty("资源价格")
    @NotNull
    private BigDecimal resourceAmount;

    @ApiModelProperty("平台折扣")
    private BigDecimal platformDiscount;

    private String resourceDetail;

    private Long cloudEnvId;

    @ApiModelProperty("统一到期时间")
    private Integer unifyDate;

    @ApiModelProperty("服务ID")
    private String serviceId;

}
