package cn.com.cloudstar.rightcloud.oss.common.pojo.bag.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 卡时包使用详情;
 *
 * <AUTHOR> wanlongfei
 * @date : 2023-4-19
 */
@ApiModel(value = "卡时包使用详情", description = "")
@TableName("biz_bag_card_hour_usage_detail")
@Data
public class BizBagCardHourUsageDetail implements Serializable, Cloneable {

    /**  */
    @ApiModelProperty(name = "", notes = "")
    @TableId
    private Long id;

    /** 账单号;账单编号 */
    @ApiModelProperty(name = "账单号", notes = "账单编号")
    private String billNo;

    /** 套餐包ID;套餐包ID */
    @ApiModelProperty(name = "套餐包ID", notes = "套餐包ID")
    private String bagId;

    /** 卡时包实例UUID;卡时包实例UUID */
    @ApiModelProperty(name = "卡时包实例UUID", notes = "卡时包实例UUID")
    private String bagInstUuid;

    /** 支付时间;卡时使用时间（支付时间） */
    @ApiModelProperty(name = "支付时间", notes = "卡时使用时间（支付时间）")
    private Date payTime;

    /** 使用卡时 */
    @ApiModelProperty(name = "使用卡时", notes = "")
    private BigDecimal usageAmount;

    /** 剩余卡时 */
    @ApiModelProperty(name = "剩余卡时", notes = "")
    private BigDecimal remainingAmount;

    /** 账户ID;账户ID */
    @ApiModelProperty(name = "账户ID", notes = "账户ID")
    private Long accountId;

    /** 所有者ID */
    @ApiModelProperty(name = "所有者ID", notes = "")
    private Long ownerId;

    /** 组织ID */
    @ApiModelProperty(name = "组织ID", notes = "")
    private Long orgSid;

    /** 创建者组织ID */
    @ApiModelProperty(name = "创建者组织ID", notes = "")
    private Long createdOrgSid;

    /** 版本号 */
    @ApiModelProperty(name = "版本号", notes = "")
    private Long version;

    /** 创建人 */
    @ApiModelProperty(name = "创建人", notes = "")
    private String createdBy;

    /** 创建时间 */
    @ApiModelProperty(name = "创建时间", notes = "")
    private Date createdDt;

    /** 更新人 */
    @ApiModelProperty(name = "更新人", notes = "")
    private String updatedBy;

    /** 更新时间 */
    @ApiModelProperty(name = "更新时间", notes = "")
    private Date updatedDt;


}
