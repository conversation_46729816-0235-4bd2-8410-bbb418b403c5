/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.common.pojo;

import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/2/18
 */
public class SensitiveWordDetector {


    /**
     * 敏感词库
     */
    private Map sensitiveWordMap;
    /**
     * 最小匹配规则
     */
    private static final int MIN_MATCH_YPE = 1;

    /**
     * 字符长度，大于1为词
     */
    private static final int WORLD_LENGTH = 2;

    /**
     * 删除文本中指定特殊符号，如有新增直接写入REG_EX中括号内即可
     */
    private static final String REG_EX = "[\n`~!@#$%^&*()+=|{}':;',\\[\\].<>/?~！@#￥%……&*（）——+|{}【】‘；：”“’。， 、？]";

    /**
     * 构造函数，初始化敏感词库
     * @param keyWordList 敏感词库
     */
    public SensitiveWordDetector(List<String> keyWordList) {
        if (Objects.isNull(keyWordList)){
            sensitiveWordMap = new HashMap<>();
        }else {
            this.addSensitiveWordToHashMap(keyWordList);
        }
    }


    /**
     * 将得到的敏感词库用一个DFA算法模型放到map中
     */
    private void addSensitiveWordToHashMap(List<String> keyWordList) {
        //初始化敏感词容器，减少扩容操作
        sensitiveWordMap = new HashMap<>(keyWordList.size());
        String key;
        Map nowMap;
        HashMap<String, String> newWorMap;
        //迭代keyWordSet
        Iterator<String> iterator = keyWordList.iterator();
        while (iterator.hasNext()) {
            //关键字
            key = iterator.next();
            nowMap = sensitiveWordMap;
            for (int i = 0; i < key.length(); i++) {
                //转换成char型
                char keyChar = key.charAt(i);
                //获取
                Object wordMap = nowMap.get(keyChar);

                //如果存在该key，直接赋值
                if (wordMap != null) {
                    nowMap = (Map) wordMap;
                } else {
                    //不存在则，则构建一个map，同时将isEnd设置为0，因为他不是最后一个
                    newWorMap = new HashMap<>();
                    //不是最后一个
                    newWorMap.put("isEnd", "0");
                    nowMap.put(keyChar, newWorMap);
                    nowMap = newWorMap;
                }

                if (i == key.length() - 1) {
                    //最后一个
                    nowMap.put("isEnd", "1");
                }
            }
        }
    }

    /**
     * 判断文字是否包含敏感字符
     *
     * @param txt       文字
     * @param matchType 匹配规则&nbsp;1：最小匹配规则，2：最大匹配规则
     * @return 若包含返回true，否则返回false
     */
    public boolean isContainSensitiveWord(String txt, int matchType) {
        if (StringUtils.isEmpty(txt)){
            return false;
        }
        //删除文本中的特殊符号
        txt = txt.replaceAll(REG_EX,"");
        boolean flag = false;
        for (int i = 0; i < txt.length(); i++) {
            //判断是否包含敏感字符
            int matchFlag = this.checkSensitiveWord(txt, i, matchType);
            //大于0存在，返回true
            if (matchFlag > 0) {
                flag = true;
            }
        }
        return flag;
    }


    /**
     * 检查文字中是否包含敏感字符，检查规则如下：
     *
     * @param txt
     * @param beginIndex
     * @param matchType
     * @return，如果存在，则返回敏感词字符的长度，不存在返回0
     * @version 1.0
     */
    private int checkSensitiveWord(String txt, int beginIndex, int matchType) {
        //敏感词结束标识位：用于敏感词只有1位的情况
        boolean flag = false;
        //匹配标识数默认为0
        int matchFlag = 0;
        char word = 0;
        Map nowMap = sensitiveWordMap;
        for (int i = beginIndex; i < txt.length(); i++) {
            word = txt.charAt(i);
            //获取指定key
            nowMap = (Map) nowMap.get(word);
            //存在，则判断是否为最后一个
            if (nowMap == null) {
                break;
            }
            //找到相应key，匹配标识+1
            matchFlag++;
            //如果为最后一个匹配规则,结束循环，返回匹配标识数
            if ("1".equals(nowMap.get("isEnd"))) {
                //结束标志位为true
                flag = true;
                //最小规则，直接返回,最大规则还需继续查找
                if (MIN_MATCH_YPE == matchType) {
                    break;
                }
            }
        }
        if (!flag) {
            matchFlag = 0;
        }
        return matchFlag;
    }


    /**
     * 获取文字中的敏感词
     *
     * @param matchType 匹配规则&nbsp;1：最小匹配规则，2：最大匹配规则
     * @return 文本中的敏感词
     */
    public Set<String> getSensitiveWord(String txt, int matchType) {
        Set<String> sensitiveWordList = new HashSet<>();
        if (StringUtils.isEmpty(txt)){
            return sensitiveWordList;
        }

        //删除文本中的特殊符号
        txt = txt.replaceAll(REG_EX,"");

        for (int i = 0; i < txt.length(); i++) {
            //判断是否包含敏感字符
            int length = checkSensitiveWord(txt, i, matchType);
            //存在,加入list中
            if (length > 0) {
                sensitiveWordList.add(txt.substring(i, i + length));
                //减1的原因，是因为for会自增
                i = i + length - 1;
            }
        }

        return sensitiveWordList;
    }
}
