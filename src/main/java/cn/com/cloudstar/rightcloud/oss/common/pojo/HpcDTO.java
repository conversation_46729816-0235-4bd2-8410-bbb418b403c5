/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.common.pojo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/9 21:11
 */
@NoArgsConstructor
@Data
public class HpcDTO {
    private ChargeDataDTO chargeData;
    private String chargeItemCategory;
    private String productCode;
    private Boolean unitPriceDisplay;
    private Integer size;
    private List<BillingPricesDTO> billingPrices;
    private String cpu;
    private String gpu;
    private String fileStorageType;
    @JSONField(name="hpc.version")
    private String hpcVersion;
    private String shareType;
    @JSONField(name="HPCClusterID")
    private String hpcClusterID;
    private String availabilityZone;
    private String category;
}
