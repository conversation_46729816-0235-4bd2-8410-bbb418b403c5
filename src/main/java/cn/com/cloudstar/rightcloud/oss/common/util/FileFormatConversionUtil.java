/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.common.util;


import cn.hutool.core.io.FileUtil;


import cn.hutool.core.io.IoUtil;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.io.FileUtils;
import org.apache.pdfbox.io.MemoryUsageSetting;
import org.apache.pdfbox.multipdf.PDFMergerUtility;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.graphics.image.LosslessFactory;
import org.apache.pdfbox.pdmodel.graphics.image.PDImageXObject;
import org.docx4j.Docx4J;
import org.docx4j.fonts.IdentityPlusMapper;
import org.docx4j.fonts.Mapper;
import org.docx4j.fonts.PhysicalFont;
import org.docx4j.fonts.PhysicalFonts;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.docx4j.org.apache.poi.util.IOUtils;
import org.springframework.util.ObjectUtils;

import javax.imageio.ImageIO;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.nio.file.Files;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.List;

/**
 * @Auther: 张淇囿
 * @Date: 2021/11/22
 */
@Slf4j
public class FileFormatConversionUtil {

    /**
     * 多图片合成pdf的限制后缀
     */
    public static final List<String> IMAGE_SUFFIX = Arrays.asList("jpg", "png", "jpeg");

    /**
     * PDF
     */
    public static final List<String> PDF_SUFFIX = Arrays.asList("pdf", "PDF");

    /**
     * 文档
     */
    public static final List<String> FILE_SUFFIX = Arrays.asList("docx", "DOCX");


    /**
     * /
     */
    private static final String LINE_FEED = "/";

    /**
     * 将文件转换为pdf，返回pdf路径
     *
     * @param inPaths 文件路径集合
     */
    public static String convertFileToPDF(List<String> inPaths) {

        String string = inPaths.get(0);
        String tempDir = string.substring(0, string.lastIndexOf(LINE_FEED) + 1) + "tempDir";
        File tempDirFile = FileUtils.getFile(tempDir);
        if (tempDirFile.exists()) {
            deleteFile(tempDirFile);
        }
        tempDirFile.mkdirs();

        List<File> files = new ArrayList<>();

        inPaths.forEach(t -> {
            String fileType = t.substring(t.lastIndexOf('.') + 1);
            File inFile = FileUtils.getFile(t);
            String inFileName = inFile.getName();
            String outFilePath = tempDir + LINE_FEED + inFileName.substring(0, inFileName.lastIndexOf('.')) + ".pdf";
            File outFile = FileUtils.getFile(outFilePath);
            if (IMAGE_SUFFIX.contains(fileType)) {
                imageToOnePdf(t, outFilePath);
            } else if (PDF_SUFFIX.contains(fileType)) {
                try {
                    Files.copy(inFile.toPath(), outFile.toPath());
                } catch (IOException e) {
                    log.error("PDF文件拷贝报错", e);
                }
            } else if (FILE_SUFFIX.contains(fileType)) {
                word2pdf(t, outFilePath);
            }
            files.add(outFile);
        });

        long time = LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        // pdf合并
        manyPdfToOne(files, tempDir + LINE_FEED + time + ".pdf");
        return tempDir + LINE_FEED + time + ".pdf";
    }


    /**
     * 将文件转换为pdf，返回pdf输入流
     */
    public static InputStream convertFileToPDF(Map<String, InputStream> files) {
        List<InputStream> ins = new ArrayList<>();
        files.forEach((key, val) -> {
            String fileType = key.substring(key.lastIndexOf('.') + 1);
            if (IMAGE_SUFFIX.contains(fileType)) {
                //图片转Pdf
                val = imageToOnePdf(val);
            } else if (PDF_SUFFIX.contains(fileType)) {

            } else if (FILE_SUFFIX.contains(fileType)) {
                //word转PDF
                val = word2pdf(val);
            } else if ("zip".equals(fileType)) {
                try {
                    Map<String, InputStream> decompress = ZipUtil.decompress(val, null);
                    List<InputStream> zips = converFileZip(decompress);
                    if (!ObjectUtils.isEmpty(zips)) {
                        ins.addAll(zips);
                    }
                    return;
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            }
            ins.add(val);
        });
        return manyPdfToOne(ins);
    }

    private static List<InputStream> converFileZip(Map<String, InputStream> files) {
        List<InputStream> ins = new ArrayList<>();
        files.forEach((key, val) -> {
            String fileType = key.substring(key.lastIndexOf('.') + 1);
            if (IMAGE_SUFFIX.contains(fileType)) {
                //图片转Pdf
                val = imageToOnePdf(val);
            } else if (PDF_SUFFIX.contains(fileType)) {

            } else if (FILE_SUFFIX.contains(fileType)) {
                //word转PDF
                val = word2pdf(val);
            }
            ins.add(val);
        });
        return ins;
    }


    /**
     * 给图片流加水印
     *
     * @param image 图片流
     * @param waterMark 水印
     */
    public static void watermarkThePictureStream(BufferedImage image, String waterMark) {
        // 得到画笔对象
        Graphics2D g = image.createGraphics();
        // 设置对线段的锯齿状边缘处理
        g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g.drawImage(image.getScaledInstance(image.getWidth(null), image.getHeight(null), Image.SCALE_SMOOTH),
                    0, 0, null);
        // 设置水印旋转
        g.rotate(Math.toRadians(-10), (double) image.getWidth() / 2, (double) image.getHeight() / 2);

        // 设置水印文字颜色
        g.setColor(Color.red);
        // 设置水印文字Font
        g.setFont(new Font("宋体", Font.PLAIN, 30));
        // 设置水印文字透明度
        g.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_ATOP, 0.3f));
        int x = -image.getWidth() / 2;
        int y;
        // 字体长度
        int markWidth = 30 * getTextLength(waterMark);
        // 字体高度
        int markHeight = 30;
        // 循环添加水印
        while (x < image.getWidth() * 1.5) {
            y = -image.getHeight() / 2;
            while (y < image.getHeight() * 1.5) {
                g.drawString(waterMark, x, y);
                y += markHeight + 250;
            }
            x += markWidth + 250;
        }
        // 释放资源
        g.dispose();
    }

    /**
     * 获取文本长度。汉字为1:1，英文和数字为2:1
     */
    private static int getTextLength(String text) {
        int length = text.length();
        for (int i = 0; i < text.length(); i++) {
            String s = String.valueOf(text.charAt(i));
            if (s.getBytes().length > 1) {
                length++;
            }
        }
        length = length % 2 == 0 ? length / 2 : length / 2 + 1;
        return length;
    }

    /**
     * 先根遍历序递归删除文件夹
     *
     * @param dirFile 要被删除的文件或者目录
     *
     * @return 删除成功返回true, 否则返回false
     */
    public static boolean deleteFile(File dirFile) {
        // 如果dir对应的文件不存在，则退出
        if (!dirFile.exists()) {
            return false;
        }

        if (dirFile.isFile()) {
            return FileUtil.del(dirFile);
        } else {

            for (File file : Objects.requireNonNull(dirFile.listFiles())) {
                deleteFile(file);
            }
        }

        return FileUtil.del(dirFile);
    }

    /**
     * pdf合并拼接
     *
     * @param files pdf文件列表
     * @param targetPath 合并后的文件
     */
    public static void manyPdfToOne(List<File> files, String targetPath) {
        try {
            PDFMergerUtility mergePdf = new PDFMergerUtility();
            for (File f : files) {
                if (f.exists() && f.isFile()) {
                    // 循环添加要合并的pdf
                    mergePdf.addSource(f);
                }
            }
            // 设置合并生成pdf文件名称
            mergePdf.setDestinationFileName(targetPath);
            // 合并pdf
            mergePdf.mergeDocuments(MemoryUsageSetting.setupMainMemoryOnly());
            FileUtils.getFile(targetPath);
        } catch (Exception e) {
            log.error("合并pdf错误：", e);
        }
    }

    /**
     * pdf合并拼接
     *
     * <AUTHOR>
     */
    public static InputStream manyPdfToOne(List<InputStream> files) {
        ByteArrayOutputStream tempOut = null;
        ByteArrayOutputStream out = null;
        try {
            PDFMergerUtility mergePdf = new PDFMergerUtility();
            mergePdf.addSources(files);
            tempOut = new ByteArrayOutputStream();
            mergePdf.setDestinationStream(tempOut);
            mergePdf.mergeDocuments(MemoryUsageSetting.setupMainMemoryOnly());
            out = (ByteArrayOutputStream) mergePdf.getDestinationStream();
            return new ByteArrayInputStream(out.toByteArray());
        } catch (Exception e) {
            log.error("合并pdf错误：", e);
        } finally {
            IoUtil.close(tempOut);
            IoUtil.close(out);
        }
        return null;
    }


    /**
     * 一个图片合成一个pdf
     *
     * @param imgFolder 图片的路径  例如:"D:\\image\\merge.png"
     * @param target 合并的图片路径          "D:\\image\\merge.pdf"
     */
    public static void imageToOnePdf(String imgFolder, String target) {
        try (PDDocument doc = new PDDocument()) {
            //创建一个空的pdf文件
            doc.save(target);
            PDPage page;
            PDImageXObject pdImage;
            PDPageContentStream contents;
            BufferedImage bufferedImage;
            float w;
            float h;
            File folder = FileUtils.getFile(imgFolder);
            bufferedImage = ImageIO.read(folder);
            //Retrieving the page
            pdImage = LosslessFactory.createFromImage(doc, bufferedImage);
            w = pdImage.getWidth();
            h = pdImage.getHeight();
            page = new PDPage(new PDRectangle(w, h));
            contents = new PDPageContentStream(doc, page);
            contents.drawImage(pdImage, 0, 0, w, h);
            contents.close();
            doc.addPage(page);
            //保存pdf
            doc.save(target);
        } catch (IOException e) {
            log.error("一个图片合成一个pdf错误：", e);
        }
    }

    /**
     * 图片输入流抓PDF输入流
     */
    public static InputStream imageToOnePdf(InputStream in) {
        ByteArrayOutputStream out = null;
        try (PDDocument doc = new PDDocument()) {
            //创建一个空的pdf文件
            PDPage page;
            PDImageXObject pdImage;
            PDPageContentStream contents;
            BufferedImage bufferedImage;
            float w;
            float h;
            bufferedImage = ImageIO.read(in);
            //Retrieving the page
            pdImage = LosslessFactory.createFromImage(doc, bufferedImage);
            w = pdImage.getWidth();
            h = pdImage.getHeight();
            page = new PDPage(new PDRectangle(w, h));
            contents = new PDPageContentStream(doc, page);
            contents.drawImage(pdImage, 0, 0, w, h);
            contents.close();
            doc.addPage(page);
            //保存pdf
            out = new ByteArrayOutputStream();
            doc.save(out);
            return new ByteArrayInputStream(out.toByteArray());
        } catch (IOException e) {
            log.error("一个图片合成一个pdf错误：", e);
        } finally {
            IOUtils.closeQuietly(out);
        }
        return null;
    }


    /**
     * doc、docx文档转pdf
     *
     * @param docPath 文档路径
     * @param savePath pdf路径
     */
    public static void word2pdf(String docPath, String savePath) {
        FileOutputStream fileOutputStream = null;
        try {
            File inputWord = new File(docPath);
            File outputFile = new File(savePath);
            fileOutputStream = new FileOutputStream(outputFile);
            WordprocessingMLPackage mlPackage = WordprocessingMLPackage.load(inputWord);
            setFontMapper(mlPackage);
            Docx4J.toPDF(mlPackage, fileOutputStream);
        }catch (Exception e){
            log.error("docx文档转pdf报错", e);
        }finally {
            IOUtils.closeQuietly(fileOutputStream);
        }
    }

    /**
     * doc、docx转PDF
     */
    public static InputStream word2pdf(InputStream in) {
        ByteArrayOutputStream outputStream = null;
        try {
            WordprocessingMLPackage mlPackage = WordprocessingMLPackage.load(in);
            setFontMapper(mlPackage);
            outputStream = new ByteArrayOutputStream();
            Docx4J.toPDF(mlPackage, outputStream);
            byte[] bytes = outputStream.toByteArray();
            outputStream.close();
            return new ByteArrayInputStream(bytes);
        } catch (Exception e) {
            log.error("docx文档转pdf报错", e);
        } finally {
            IoUtil.close(outputStream);
        }
        return null;
    }


    public static List<String> streamToTempFile(Map<String, InputStream> files) {
        List<String> result = new ArrayList<>();
        files.forEach((key, value) -> {
            String tempPath = "/tmp/" + System.currentTimeMillis() + key;
            int index = 0;
            byte[] bytes = new byte[1024];
            FileOutputStream fileOutputStream = null;
            try {
                fileOutputStream = new FileOutputStream(FileUtil.getCanonicalPath(new File(tempPath)));
                while ((index = value.read(bytes)) != -1) {
                    fileOutputStream.write(bytes, 0, index);
                    fileOutputStream.flush();
                }
            } catch (IOException e) {
                log.error("exec streamToTempFile failed,reason:[{}]",e.getMessage());
            } finally {
                try {
                    if (Objects.nonNull(value)) {
                        value.close();
                    }
                    if (Objects.nonNull(fileOutputStream)) {
                        fileOutputStream.close();
                    }
                } catch (IOException e) {
                    log.error("exec streamToTempFile failed,reason:[{}]",e.getMessage());
                }
            }
            result.add(tempPath);
        });
        return result;
    }

    /**
     * 设置字体
     * @param mlPackage
     * @throws Exception
     */
    private static void setFontMapper(WordprocessingMLPackage mlPackage) throws Exception {
        Mapper fontMapper = new IdentityPlusMapper();
        fontMapper.put("隶书", PhysicalFonts.get("LiSu"));
        fontMapper.put("宋体", PhysicalFonts.get("SimSun"));
        fontMapper.put("微软雅黑", PhysicalFonts.get("Microsoft YaHei"));
        fontMapper.put("黑体", PhysicalFonts.get("SimHei"));
        fontMapper.put("楷体", PhysicalFonts.get("KaiTi"));
        fontMapper.put("新宋体", PhysicalFonts.get("NSimSun"));
        fontMapper.put("华文行楷", PhysicalFonts.get("STXingkai"));
        fontMapper.put("华文仿宋", PhysicalFonts.get("STFangsong"));
        fontMapper.put("仿宋", PhysicalFonts.get("FangSong"));
        fontMapper.put("幼圆", PhysicalFonts.get("YouYuan"));
        fontMapper.put("华文宋体", PhysicalFonts.get("STSong"));
        fontMapper.put("华文中宋", PhysicalFonts.get("STZhongsong"));
        fontMapper.put("等线", PhysicalFonts.get("DengXian"));
        fontMapper.put("等线 Light", PhysicalFonts.get("DengXian"));
        fontMapper.put("华文琥珀", PhysicalFonts.get("STHupo"));
        fontMapper.put("华文隶书", PhysicalFonts.get("STLiti"));
        fontMapper.put("华文新魏", PhysicalFonts.get("STXinwei"));
        fontMapper.put("华文彩云", PhysicalFonts.get("STCaiyun"));
        fontMapper.put("方正姚体", PhysicalFonts.get("FZYaoti"));
        fontMapper.put("方正舒体", PhysicalFonts.get("FZShuTi"));
        fontMapper.put("华文细黑", PhysicalFonts.get("STXihei"));
        fontMapper.put("宋体扩展", PhysicalFonts.get("simsun-extB"));
        fontMapper.put("仿宋_GB2312", PhysicalFonts.get("FangSong_GB2312"));
        fontMapper.put("新細明體", PhysicalFonts.get("SimSun"));
        //解决宋体（正文）和宋体（标题）的乱码问题
        PhysicalFonts.put("PMingLiU", PhysicalFonts.get("SimSun"));
        PhysicalFonts.put("新細明體", PhysicalFonts.get("SimSun"));
        //宋体&新宋体
        PhysicalFont simsunFont = PhysicalFonts.get("SimSun");
        fontMapper.put("SimSun", simsunFont);
        //设置字体
        mlPackage.setFontMapper(fontMapper);
    }

    public static PDDocument wordToDocument(InputStream inputStream) {
        try (ByteArrayOutputStream outStream = new ByteArrayOutputStream()) {
            WordprocessingMLPackage mlPackage = WordprocessingMLPackage.load(inputStream);
            setFontMapper(mlPackage);
            Docx4J.toPDF(mlPackage, outStream);
            return PDDocument.load(outStream.toByteArray());
        } catch (Exception ex) {
            log.error("wordToDocument --> word转换异常");
            return new PDDocument();
        }
    }

    public static PDDocument imageToDocument(InputStream inputStream) {
        PDDocument pdf = new PDDocument();
        PDPageContentStream contentStream = null;
        try {
            PDImageXObject pdImage = LosslessFactory.createFromImage(pdf, ImageIO.read(inputStream));
            PDPage page = new PDPage(new PDRectangle(pdImage.getWidth(), pdImage.getHeight()));
            pdf.addPage(page);
            // 将图片绘制到PDF页面上
            contentStream = new PDPageContentStream(pdf, page);
            contentStream.drawImage(pdImage, 0, 0, pdImage.getWidth(), pdImage.getHeight());
        } catch (Exception e) {
            log.error("imageToDocument --> 图片转PDF异常");
        } finally {
            IoUtil.close(contentStream);
        }
        return pdf;
    }

}
