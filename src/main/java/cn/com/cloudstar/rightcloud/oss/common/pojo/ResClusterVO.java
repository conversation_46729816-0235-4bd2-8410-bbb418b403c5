/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.common.pojo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

/**
 * res集群
 *
 * <AUTHOR>
 * @date 2022/11/16
 */
@Data
public class ResClusterVO {

    /**
     * id
     */
    protected Long id;
    /**
     * 名称
     */
    protected String name;
    /**
     * 状态
     */
    @JsonIgnore
    protected String status;
    /**
     * 状态名称
     */
    protected String statusName;
    /**
     * desc
     */
    protected String desc;
    /**
     * 组织id
     */
    @JsonIgnore
    protected Long orgId;
    /**
     * 策略id
     */
    @JsonIgnore
    private Long policyId;

    public String getStatusName(String status) {
        switch (status) {
            case "apply":
                return "申请中";
            case "configing":
                return "配置中";
            case "available":
                return "运行中";
            case "expired":
                return "已过期";
            case "frozen":
                return "已冻结";
            case "rejected":
                return "已拒绝";
            case "deleting":
                return "删除中";
            case "deleted":
                return "已删除";
            case "unsubscribing":
                return "退订中";
            case "unsubscribed":
                return "已退订";
            default:
                return "";
        }
    }
}
