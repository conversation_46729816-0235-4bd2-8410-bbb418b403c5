/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.common.enums;

import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 计费结算账单产品代码
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum BillingGaapProductCode {

    /**
     * 公网IP
     */
    EIP("eip", "公网IP"),

    /**
     * ecs 实例
     */
    ECS("ecs", "云主机"),
    HOST("host", "实例"),

    /**
     * 快照
     */
    SNAPSHORT("snapshot", "快照"),

    /**
     * RDS
     */
    RDS("rds", "RDS"),

    /**
     * 负载均衡
     */
    SLB("slb", "负载均衡"),
    /**
     *
     * 腾讯云负载均衡
     * */
    LB("lb", "负载均衡"),

    /**
     * 华为云弹性负载均衡
     * **/
    ELB("elb", "弹性负载均衡"),

    /**
     * 云盘 yundisk （阿里云产品代码返回叫这个） disk（账单的产品代码又叫这个）
     */
    YUNDISK("disk", "硬盘"),


    VPC("vpc", "私有网络"),

    OBS("obs", "对象存储服务"),

    IMAGE("image", "镜像服务"),

    DDOS("ddos", "DDos流量清洗"),

    DIS("dis", "数据接入服务"),

    AIS("ais", "人工智能服务"),

    CDN("cdn", "内容分发网络"),

    CES("ces", "云监控服务"),

    KMS("kms", "数据加密服务"),

    SFS("sfs", "弹性文件服务"),

    SMN("smn", "消息通知服务"),

    API("api", "API网关"),

    SC("sc", "安全中心"),

    SWR("swr", "容器镜像服务"),

    CSE("cse", "微服务引擎"),

    BW("bw", "固定带宽");

    private String code;

    private String name;

    public static String formatQcloudProductName(String name, String resourceId) {
        if (resourceId.startsWith("disk-")) {
            return BillingGaapProductCode.YUNDISK.getCode();
        } else if (resourceId.startsWith("ins-")) {
            return BillingGaapProductCode.ECS.getCode();
        } else if (resourceId.contains("-")) {
            return resourceId.split("-")[0];
        }
        return name;
    }

    public static String formatHuaweiCloudServiceCode(String serviceCode) {
        if (Objects.equals(serviceCode, "hws.service.type.ec2")) {
            return BillingGaapProductCode.ECS.getCode();
        } else if (Objects.equals(serviceCode, "hws.service.type.ebs")) {
            return BillingGaapProductCode.YUNDISK.getCode();
        } else if (Objects.equals(serviceCode, "hws.service.type.antiddos")) {
            return BillingGaapProductCode.DDOS.getCode();
        } else if (Objects.equals(serviceCode, "hws.service.type.apig")) {
            return BillingGaapProductCode.API.getCode();
        }
        return serviceCode.substring(serviceCode.lastIndexOf('.') + 1);
    }

    public static String formatHuaweiCloudResourceCode(String resourceCode) {
        if (Objects.equals(resourceCode, "hws.resource.type.vm")) {
            return BillingGaapProductCode.ECS.getCode();
        } else if (Objects.equals(resourceCode, "hws.resource.type.volume")) {
            return BillingGaapProductCode.YUNDISK.getCode();
        } else if (Objects.equals(resourceCode, "hws.resource.type.antiddos")) {
            return BillingGaapProductCode.DDOS.getCode();
        } else if (Objects.equals(resourceCode, "hws.resource.type.apig")) {
            return BillingGaapProductCode.API.getCode();
        } else if (Objects.equals(resourceCode, "hws.resource.type.rds")){
            return BillingGaapProductCode.RDS.getCode();
        } else if (Objects.equals(resourceCode, "hws.resource.type.obs")){
            return BillingGaapProductCode.OBS.getCode();
        } else if (Objects.equals(resourceCode, "hws.resource.type.ip")){
            return BillingGaapProductCode.EIP.getCode();
        } else if (Objects.equals(resourceCode, "hws.resource.type.cdn")){
            return BillingGaapProductCode.CDN.getCode();
        } else if (Objects.equals(resourceCode, "hws.resource.type.bandwidth")){
            return BillingGaapProductCode.BW.getCode();
        }
        return resourceCode.substring(resourceCode.lastIndexOf('.') + 1);
    }

    /**
     * 根据code获取产品名称
     *
     * @param code
     */
    public static String getProductName(String code) {
        String name = "";
        for (BillingGaapProductCode billingCode : BillingGaapProductCode.values()) {
            if (billingCode.getCode().equals(code)) {
                name = billingCode.getName();
            }
        }
        return name;
    }

    public static String getCodeFromBillingPriceCategrory(String categrory) {
        switch (categrory) {
            case "compute":
                return ECS.getCode();
            case "blockStorage":
                return YUNDISK.getCode();
            case "network":
                return EIP.getCode();
            default:
                return categrory;
        }
    }

    /**
     * 根据code获取产品名称
     *
     * @param code
     */
    public static String toDesc(String code) {
        String name = "";
        for (BillingGaapProductCode billingCode : BillingGaapProductCode.values()) {
            if (billingCode.getCode().equals(code)) {
                name = billingCode.getName() + " " + code.toUpperCase();
            }
        }
        return name;
    }
}
