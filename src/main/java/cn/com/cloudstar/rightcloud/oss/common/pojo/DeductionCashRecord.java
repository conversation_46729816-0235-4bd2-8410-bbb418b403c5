package cn.com.cloudstar.rightcloud.oss.common.pojo;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class DeductionCashRecord {

    /**
     * 抵扣现金券支付金额
     */
    private BigDecimal deductCouponDiscount;
    /**
     * 抵扣现金券ID
     */
    private String deductionCouponNo;
    /**
     * 抵扣现金券抵扣前金额
     */
    private BigDecimal deductionCouponBalanceBefore;
    /**
     * 抵扣现金券抵扣后金额
     */
    private BigDecimal deductionCouponBalanceAfter;
}
