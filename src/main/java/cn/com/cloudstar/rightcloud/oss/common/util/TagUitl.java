/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.common.util;

import com.google.common.base.Strings;

/**
 * <Description> <br>
 *
 * <AUTHOR>
 * @createDate 2020/01/19 9:47
 */
public class TagUitl {

    public static String mergeTag(String tagName, String tagValue) {
        StringBuilder sb = new StringBuilder();
        if (Strings.isNullOrEmpty(tagName) || Strings.isNullOrEmpty(tagValue)) {
            return "";
        }
        String[] names = tagName.split(",");
        String[] values = tagValue.split(",");
        for (int i = 0; i < names.length; i++) {
            sb.append(names[i]).append(":").append(values[i]).append(";");
        }
        return sb.toString();
    }
}
