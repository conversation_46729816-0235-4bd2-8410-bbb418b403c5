/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.common.constants;

/**
 * Created by swq on 4/7/2016.
 *
 * <AUTHOR>
 */
public interface SysConfigConstants {

    /**
     * ansible server url
     */
    String ANSIBLE_SERVER_URL = "ansible.server.url";

    String OPEN_FALCON_ALARM_CALLBACK_URL = "openfalcon.alarm.callback.url";

    /**
     * kubernetes集群默认dns ip配置
     */
    String KUBERNETES_DEFAULT_CLUSTER_DNS = "kubernetes.default.cluster.dns";

    //通知提醒相关配置参数 add by luxinglin 2017-11-10

    /**
     * 预过期时间配置参数
     */
    String SERVICE_EXPIRE_DURATION = "service.expire.notice.duration";
    /**
     * 服务到期持续发送提醒频率配置参数
     */
    String SERVICE_EXPIRE_NOTICE_FREQUENCY = "service.expire.notice.frequency";
    /**
     * 服务到期提醒开关配置参数
     */
    String SERVICE_EXPIRE_NOTICE_ON = "service.expire.notice.on";
    /**
     * 服务到期提醒方式配置参数
     */
    String SERVICE_EXPIRE_NOTICE_METHOD = "service.expire.notice.method";

    //end 通知提醒相关配置参数 add by luxinglin 2017-11-10

    /**
     * 平台类型
     */
    String PLATFORM_TYPE = "platform.type";

    /**
     * 短信相关配置
     */
    String SMS_CDKEY = "sms.cdkey";

    String SMS_KEY = "sms.key";

    String SMS_PASS_KEY = "sms.password";

    String SMS_URL = "sms.url";

    String SMS_SEQID = "sms.seqid";

    String SMS_ENABLE = "sms.enable";

    String SMS_SIGN = "sms.sign";

    String SMS_ALIYUN_SIGN = "sms.aliyun.sign";
    String SMS_ALIYUN_URL = "sms.aliyun.url";
    String SMS_ALIYUN_ACCESS_KEY_ID = "sms.aliyun.access.key.id";
    String SMS_ALIYUN_ACCESS_KEY_SECRET = "sms.aliyun.access.key.secret";

    String ACCESS_KEY_ID = "AccessKeyId";

    String APP_KEY ="AppKey";

    String ACCESS_KEY_SECRET = "AccessKeySecret";

    String APP_SECRET ="AppSecret";

    String SMS_HUAWEIYUN_SIGN = "sms.huaweiyun.sign";
    String SMS_HUAWEIYUN_SIGN_NUMBER = "sms.huaweiyun.sign.number";
    String SMS_HUAWEIYUN_URL = "sms.huaweiyun.url";
    String SMS_HUAWEIYUN_APP_KEY = "sms.huaweiyun.app.key";
    String SMS_HUAWEIYUN_APP_SECRET = "sms.huaweiyun.app.secret";
    String SMS_PLATFORM_SELECTED = "sms.platform.selected";
    String SMS_PLATFORM_OPTIONS = "sms.platform.options";

    String LOGINT_IPS_PRIVACY = "logintips.privacy";

    String MINIO_EXTERNAL_ENDPOINT = "minio.external.net.endpoint";

    /**
     * 平台logo存储根路径
     */
    String PLATFORM_LOGO_PATH = "platform.logo.root.path";

    /**
     * 平台密钥key
     */
    String SECURITY_KEY = "security.key";

    /**
     * ldap证书路径
     */
    String LDAP_CREDENTIALS_PATH = "ldap.credentials.path";

    /**
     * mongo 用户执行记录日志集合名
     */
    String MONGO_ACTION_LOG_COLLECTION_NAME = "action_log";


    /***
     * swagger 在线文档开关
     * */
    String SWAGGER_ENABLE = "swagger.enable";

    /***
     * 云监控组件开关
     * */
    String MONITOR_CONFIG = "monitor.acquisition.mode";
    /**
     * 系统名称
     */
    String SYSTEM_NAME = "system.name";

    /**
     * 公司名称
     */
    String COMPANY_NAME = "company.name";


    /**
     * 公司电话
     */
    String SYSTEM_CONTACT_NUMBER = "system.contact.number";

    /**
     * 公司邮箱
     */
    String SYSTEM_CONTACT_EMAIL = "system.contact.email";

    /**
     * 到期时间天数
     */
    String UPCOMING_EXPIRED_DAYS = "upcoming_expired_days";

    /**
     * 最大客户数量
     */
    String CUSTOMER_MAX_NUM = "customer_max_num";

    /**
     * 小图标
     */
    String PLATFORM_SMALL_LOGO = "platform.small.logo";
    /**
     * 大图标
     */
    String PLATFORM_LARGE_LOGO = "platform.large.logo";
    /**
     * 高级监控任务分组
     **/
    String DCIM_INVENTORY_ID = "dcim.inventory.id";
    /**
     * 高级监控 安装pgagent 任务模版ID
     **/
    String DCIM_PGAGENT_INSTALL_TEMPLATE_ID = "dcim.pgangent.install.templateId";
    /**
     * 高级监控 卸载pgagent 任务模版ID
     **/
    String DCIM_PGAGENT_UNINSTALL_TEMPLATE_ID = "dcim.pgangent.uninstall.templateId";
    /**
     * 高级监控配置发现频率默认 20m
     **/
    String DCIM_TASK_CRON = "dcim.task.cron";
    /**
     * 高级监控指标采集频率默认1m
     **/
    String DCIM_TASK_CRON_DEFAULT = "dcim.task.cron.default";
    /**
     * 监控系统手动纳管服务器 所属category
     **/
    String DCIM_MANUAL_SERVER_CATEGORY = "dcim.manual.server.category";
    /**
     * 监控系统 接口前缀
     **/
    String DCIM_API_PREFIX = "monitor.integration.api.prefix";
    /**
     * node_exporter默认端口号
     */
    String DCIM_NODE_EXPORTER_PORT = "dcim.node_exporter.port";

    /**
     * 监控系统 用户名
     **/
    String DCIM_MONITOR_INTEGRATION_SERVER_USERNAME = "monitor.integration.server.username";

    /**
     * 监控系统密码
     **/
    String DCIM_MONITOR_INTEGRATION_SERVER_CIPHER = "monitor.integration.server.password";
    /**
     * 监控系统 sshport
     **/
    String DCIM_MONITOR_INTEGRATION_SERVER_SSHPORT = "monitor.integration.server.sshport";
    /**
     * INVENTORY 内容格式
     **/
    String INVENTORY_FORMAT = "inventory.format";

    /**
     * 基础监控 接口前缀
     **/
    String BASIC_MONITOR_API_PREFIX = "base.monitor.api.prefix";
    /**
     * 基础监控 数据采集频率
     **/
    String BASIC_MONITOR_DATA_COLLECT_FREQUENCY = "base.monitor.data.collect.frequency";
    /**
     * 基础监控 告警数据采集频率
     **/
    String BASIC_MONITOR_ALARM_COLLECT_FREQUENCY = "base.monitor.alarm.collect.frequency";
    /**
     * 基础监控配置应用到所有云环境
     */
    String BASIC_MONITOR_APPLY_ALL = "base.monitor.apply.all";
    /**
     * 基础监控 API版本
     **/
    String BASIC_MONITOR_API_VERSION = "/api/v1";
    /**
     * 基础监控TopN指标项名称
     */
    String BASIC_MONITOR_TOPN_METRIC_NAMES = "basic.monitor.topN.metric.names";

    String PROJECTADMIN_AUDITSTATUS = "approve.project.mgt";

    String COMPANYADMIN_AUDITSTATUS = "approve.company.mgt";

    /**
     * 自动接管等待时间
     */
    String IMPORT_DELAY_TIME = "import.delay.time";

    /**
     * 用户token过期时间
     */
    String SESSION_EXPIRE_TIME = "session.expire.time";

    /**
     * 接管重试次数
     */
    String INSTANCE_ACCESS_COUNT = "instance.access.count";
    /**
     * 接管超时时间
     */
    String INSTANCE_ACCESS_TIME = "instance.access.time";

    /**
     * OpenStack系列云环境是否支持普通租户接入
     */
    String CLOUD_ENV_TENANT_IMPORT = "cloud.env.tenant.import";

    /**
     * 工单审批流程状态
     */
    String TICKET_AUDIT_STATUS = "ticket.audit.status";

    /**组织层级*/
    String ORG_LEVEL_COUNT = "org.level.count";

    /**
     * 不显示
     */
    String DISPLAY_TYPE = "secret";

    /**
     * 文件保留时间
     */
    String BILL_RETENTION_TIME = "bill.retention.time";

    /**
     * AI共享资源池版本切换：版本选择
     */
    String MODEL_ARTS_EDITION = "model.arts.edition";

    /**
     * Modelarts资源账号名称
     */
    String IAM_AUTO_ENTRUST_ACCOUNT = "iam.auto.entrust.account";

    /**
     * Modelarts委托名称
     */
    String IAM_AUTO_ENTRUST_NAME = "iam.auto.entrust.name";


    /**
     * 大屏账号
     */
    String BIGSCREEN_CIPHER = "bigScreen.pwd";

    /**
     * 大屏密码
     */
    String BIGSCREEN_ACCOUNT = "bigScreen.account";

    /**
     * 大屏调用认证方式
     */
    String BIGSCREEN_INTERFACE = "bigScreen.call.interface";
    /**
     * 资源池
     */
    String IAM_AUTO_ENTRUST_SHARE_ID = "iam.auto.entrust.share.id";

    String  SCREEN_AI_HIDE_COMPANY="screen.airesource.hide-company";

    String KEYCLOAK_CIPHER ="keycloak.PASSWORD";

    /**
     * minio公钥
     */
    String  MINIO_ACCESS_KEY="minio.access.key";

    /**
     * minio私钥
     */
    String  MINIO_SECRET_KEY="minio.secret.key";

    /**
     * Modelarts资源被委托账号密码
     */
    String IAM_AUTO_SHARE_TENANTUSERPASS ="iam.auto.share.tenantUserPass";
    /**
     * 双重审批开关
     */
    String MULTIPLE_AUDITS_ENABLE = "multiple.audits.enable";


    interface ImageHarbor {

        String URL = "image.harbor.url";
        String ADMIN = "image.harbor.admin";
        String CIPHER = "image.harbor.admin.passwd";
    }


    interface AlipayConfig{

        /**支付宝支付应用ID*/
        String APPID = "alipay.appid";

        /**支付宝支付应用私钥*/
        String PRIVATE_KEY = "alipay.privateKey";

        /**支付宝支付公钥*/
        String PUBLIC_KEY = "alipay.publicKey";

        /**支付宝支付回调地址*/
        String NOTIFY_URL = "alipay.notifyUrl";

        /**支付宝支付成功返回地址*/
        String RETURN_URL = "alipay.returnUrl";

        /**支付宝支付开关*/
        String SWITCH = "alipay.switch";
    }

    interface WechatpayConfig{

        /**微信支付应用ID*/
        String APPID = "wechatpay.appid";

        /**微信支付直连商户号*/
        String MCHID = "wechatpay.mchid";

        /**微信支付直连商户号*/
        String MCH_KEY = "wechatpay.mchKey";

        /**微信支付回调地址*/
        String NOTIFY_URL = "wechatpay.notifyUrl";

        /**微信支付成功返回地址*/
        String RETURN_URL = "wechatpay.returnUrl";

        /**微信支付开关*/
        String SWITCH = "wechatpay.swithch";
    }



    interface HpcConfig{
        /**作业/用户激活检查*/
        String checkJob = "hpc.checkJob.flag";
        /**ccportalAPI认证地址*/
        String ccApi = "hpc.cc.api.path";
        /**ccportal管理账号*/
        String ccadminName = "hpc.cc.admin.username";
        /**ccportal管理账号密码*/
        String ccadminPass = "hpc.cc.admin.password";

    }

    interface MailConfig{
        /**邮件地址*/
        String mailHost = "mail.smtp.host";
        /**邮件昵称*/
        String mailNickname = "mail.nickname";
        /**邮件账号*/
        String mailAccount = "mail.account";
        /**邮件用户名*/
        String mailUsername = "mail.username";
        /**
         * 邮件密码
         */
        String mailCipher = "mail.password";

    }
    interface LoginLock{
        /**开启状态*/
        String open = "loginfailure_config.open";
        /**失败次数*/
        String errNum = "loginfailure_config.count";
        /**临时锁定时间*/
        String lockTime = "loginfailure_config.time";

    }
    interface LoginTipsConfig{
        /**是否开启*/
        String open = "logintips.open";
        /**提示标题*/
        String title = "logintips.title";
        /**提示内容*/
        String contente = "logintips.content";
    }

    interface SystemUpgradeConfig {
        /**升级开始时间*/
        String starttime = "platform.upgrade.starttime";
        /**升级结束时间*/
        String endtime = "platform.upgrade.endtime";
        /**提示内容*/
        String userSid = "platform.upgrade.usersid";
        /** 升级公告ID*/
        String noticeId = "platform.upgrade.noticeid";
    }

    interface UnionpayConfig{


        /**银联支付直连商户号*/
        String MCHID = "unionpay.mchid";


        /**
         * 银联支付回调地址
         */
        String NOTIFY_URL = "unionpay.notifyUrl";

        /**
         * 银联支付成功返回地址
         */
        String RETURN_URL = "unionpay.returnUrl";

        /**
         * 银联支付开关
         */
        String SWITCH = "unionpay.switch";
    }

    interface SfsConfig {

        /**
         * SFS存储总容量
         */
        String SFS_MAX_STORAGE = "hpc.sfs.max.storage";

    }


    /**
     * 预占用算力
     */
    String SCREEN_AIRESOURCE_PREOCCUPANCY = "screen.airesource.preoccupancy";

    /**
     * 预占用有效算力CUE
     */
    String SCREEN_AIRESOURCE_PRECUE = "screen.airesource.precue";
    /**
     * 按量付费实时询价计费
     */
    String BILL_PREPAID_FROM_INQUIRYPRICE = "bill.prepaid.from.inquiryprice";

    /**
     * 产品限额
     */
    interface ProductQuotaConfig {
        /**最小购买金额*/
        String MINIMUM_PURCHASE_AMOUNT = "minimum.purchase.amount.";
        /**开启状态*/
        String MINIMUM_PURCHASE_AMOUNT_OPEN = "minimum.purchase.amount.open.";
        /**最小冻结金额*/
        String MINIMUM_FROZEN_AMOUNT = "minimum.frozen.amount.";
        /**开启状态*/
        String MINIMUM_FROZEN_AMOUNT_OPEN = "minimum.frozen.amount.open.";
    }

    /**密码有效期-有效天数*/
    String CIPHERPRESCRIPTION_CONFIG_EXPIRE_TIME = "pwdprescription_config.expire.time";
    /**临时登录失败锁定-失败次数*/
    String LOGINFAILURE_CONFIG_COUNT = "loginfailure_config.count";
    /**临时登录失败锁定-临时锁定时间*/
    String LOGINFAILURE_CONFIG_TIME = "loginfailure_config.time";
    /**同账号登录限制-人数限制*/
    String LOGINLIMIT_CONFIG_COUNT = "loginlimit_config.count";
    /**
     * 安全证书到期告警
     */
    String CERT_EXPR_WARN = "cert.expr.warn";
    /**
     * 安全证书到期告警检查周期
     */
    String CERT_EXPR_CYCLE = "cert.expr.cycle";

    /**
     * IAM用户SSO
     */
    String IAM_CREATE_USER_FLAG = "iam.create.user.flag";
    /**
     * IAM子用户配额
     */
    String IAM_CREATE_USER_LIMIT = "iam.create.user.limit";
    /**
     * IAM查看用户权限
     */
    String IAM_LIST_USER_POLICY = "iam.list.user.policy";
    /**
     * 嵌入HCSO控制台页面
     */
    String IAM_HCSO_CONSOLE_FLAG = "iam.hcso.console.flag";
    /**
     * 身份提供商文件
     */
    String IAM_IDENTITY_URL_CLIENT_FILE = "iam.identity.url.client.file";

    /**
     * 业务标识类型
     */
    String BUSINESS_TAG_CONFIG = "business.tag.config";

    /**
     * 自定义业务标识配置
     */
    String CUSTOM_BUSINESS_TAG = "custom.business.tag";

    /**
     * TOP信息过滤开关
     */
    String TOP_INFO_FILTER_SWITCH = "top.info.filter.switch";

    /**
     * TOP信息过滤标识
     */
    String TOP_INFO_FILTER_TAG = "top.info.filter.tag";

    /**
     * 自定义用户信息模板
     */
    String CUSTOM_INFO_TEMPLATE = "custom.info.template";

    String RES_SCREEN_CONFIG = "res_screen_config";

    String RES_SCREEN_BMS = "res.screen.bms";
    String RES_SCREEN_ECS = "res.screen.ecs";
    String RES_SCREEN_CPU = "res.screen.vcpu";
    String RES_SCREEN_MEMORY = "res.screen.memory";
    String RES_SCREEN_STORAGE = "res.screen.storage";
    String RES_SCREEN_FIP = "res.screen.fip";
    String RES_SCREEN_ELB = "res.screen.elb";
    String RES_SCREEN_CCE = "res.screen.cce";
    String RES_SCREEN_RDS = "res.screen.rds";
    String RES_SCREEN_SFS = "res.screen.sfs";
    String RES_SCREEN_DCS = "res.screen.dcs";
    String RES_SCREEN_URL = "res.screen.url";
}
