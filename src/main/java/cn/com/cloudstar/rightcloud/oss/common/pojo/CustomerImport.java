/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.common.pojo;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * The type User import.
 *
 * <AUTHOR>
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CustomerImport extends BaseUserImp implements Serializable {
    private String corpName;
    private String username;
    private String account;
    private String gender;
    private String phone;
    private String email;
    private String expires;
    private String description;

    private String salesAccount;
    private String userType;

    /**
     * 是否来自销售同步
     */
    private boolean isFromSalesSync = false;

    private Map<String, String> appQuota;

    private Map<String, Map<String, String>> userAppQuota;

}
