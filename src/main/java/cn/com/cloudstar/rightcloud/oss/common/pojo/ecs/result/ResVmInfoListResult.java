package cn.com.cloudstar.rightcloud.oss.common.pojo.ecs.result;


import java.io.Serializable;
import java.util.List;

import lombok.Data;

import cn.com.cloudstar.rightcloud.oss.common.pojo.ecs.dto.ResVdDto;

/**
 * 云主机分页响应结果
 *
 * @author: chengpeng
 * @date: 2023/5/25 9:06
 */
@Data
public class ResVmInfoListResult implements Serializable {
    private static final long serialVersionUID = -5148570494439813584L;
    /**
     * id
     */
    private Long id;

    /**
     * 组织sid
     */
    private Long orgId;
    /**
     * 所属组织
     */
    private String orgName;
    /**
     * 项目ID
     */
    private Long projectId;
    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 云环境id
     */
    private Long cloudEnvId;
    /**
     * 云环境名称
     */
    private String cloudEnvName;
    /**
     * 云平台定义图标
     */
    private String cloudEnvIcon;
    /**
     * 云平台定义编码
     */
    private String cloudEnvCode;

    /**
     * 实例id
     */
    private String uuid;

    /**
     * 实例名称
     */
    private String name;

    /**
     * 状态
     */
    private String status;
    /**
     * 内ip
     */
    private String innerIp;

    /**
     * 公共ip
     */
    private String publicIp;

    private List<ResVdDto> vds;


}
