/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.common.pojo;

import com.github.pagehelper.PageInfo;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;

/**
 * Grid返回对象
 *
 * <AUTHOR>
 */
public class BaseGridReturn {

    /**
     * 总数据条数
     */
    @ApiModelProperty(value = "总数据条数")
    private long totalRows;

    /**
     * 显示数据对象
     */
    @ApiModelProperty(value = "显示数据对象")
    private Object data;

    /**
     * 显示数据列表
     */
    @ApiModelProperty(value = "显示数据列表")
    private List<?> dataList;

    public BaseGridReturn() {
    }

    public BaseGridReturn(List<?> dataList) {
        PageInfo page = new PageInfo<>(dataList);
        this.totalRows = page.getTotal();
        this.dataList = page.getList();
    }

    public BaseGridReturn(PageInfo<?> page) {
        this.totalRows = page.getTotal();
        this.dataList = page.getList();
    }

    public BaseGridReturn(PageInfo<?> page, Object data) {
        this.totalRows = page.getTotal();
        this.data = data;
        this.dataList = page.getList();
    }

    /**
     * 取得总数据条擿
     *
     * @return 总数据条擿
     */
    public long getTotalRows() {
        return totalRows;
    }

    /**
     * 设置总数据条擿
     */
    public void setTotalRows(long totalRows) {
        this.totalRows = totalRows;
    }

    /**
     * 取得显示数据列表
     *
     * @return data
     */
    public List<?> getDataList() {
        return dataList;
    }

    /**
     * 设置显示数据列表
     */
    public void setDataList(List<?> dataList) {
        this.dataList = dataList;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }
}
