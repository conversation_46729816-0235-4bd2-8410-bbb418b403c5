/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.oss.common.enums;

import java.util.ArrayList;
import java.util.List;

/**
 * 集群规模 HA高可用，非HA，非高可用
 */
public enum ScenarioEnum {

    /** NO */
    Paired("Paired", "HA"),

    /** YES */
    Unpaired("Unpaired", "非HA");

    /** 枚举值 */
    private final String code;

    /** 枚举描述 */
    private final String message;

    /**
     * 构造一个<code>BooleanEnum</code>枚举对象
     *
     * @param code
     * @param message
     */
    private ScenarioEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    /**
     * @return Returns the code.
     */
    public String getCode() {
        return code;
    }

    /**
     * @return Returns the message.
     */
    public String getMessage() {
        return message;
    }

    /**
     * @return Returns the code.
     */
    public String code() {
        return code;
    }

    /**
     * @return Returns the message.
     */
    public String message() {
        return message;
    }

    /**
     * 通过枚举<code>code</code>获得枚举
     *
     * @param code
     * @return BooleanEnum
     */
    public static ScenarioEnum getByCode(String code) {
        for (ScenarioEnum _enum : values()) {
            if (_enum.getCode().equals(code)) {
                return _enum;
            }
        }
        return null;
    }

    /**
     * 获取全部枚举
     *
     * @return List<BooleanEnum>
     */
    public List<ScenarioEnum> getAllEnum() {
        List<ScenarioEnum> list = new ArrayList<ScenarioEnum>();
        for (ScenarioEnum _enum : values()) {
            list.add(_enum);
        }
        return list;
    }

    /**
     * 获取全部枚举值
     *
     * @return List<String>
     */
    public List<String> getAllEnumCode() {
        List<String> list = new ArrayList<String>();
        for (ScenarioEnum _enum : values()) {
            list.add(_enum.code());
        }
        return list;
    }
}
