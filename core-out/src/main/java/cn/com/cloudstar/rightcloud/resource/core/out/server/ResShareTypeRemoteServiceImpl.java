/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.core.out.server;

import cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResShareType;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.QueryResShareTypeByParamsRequest;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.server.ResShareTypeRemoteService;
import cn.com.cloudstar.rightcloud.resource.service.server.ResShareTypeService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-11
 */
@Component
@DubboService
public class ResShareTypeRemoteServiceImpl implements ResShareTypeRemoteService {

    @Autowired
    private ResShareTypeService resShareTypeService;

    @Override
    public List<ResShareType> selectByParams(QueryResShareTypeByParamsRequest request) {
        Criteria criteria = Criteria.prepareNewCriteria(request);

        return resShareTypeService.selectByParams(criteria);
    }
}
