package cn.com.cloudstar.rightcloud.resource.core.out.hpc;

import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.HPCPreDrpMgmt;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.HPCPreDrpMgmtResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.vo.HPCNodeDifferVo;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResHpcClusterDeleteNodeTask;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.Org;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.*;
import cn.com.cloudstar.rightcloud.resource.service.hpc.IResHpcClusterDeleteNodeTaskService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.FDTaskInfoResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.HPCClusterDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.HPCClusterInfoIDResult;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResShare;
import cn.com.cloudstar.rightcloud.common.pojo.BaseGridReturn;
import cn.com.cloudstar.rightcloud.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResHpcCluster;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResHpcClusterResource;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.ActiveHPCClusterResult;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.ClusterSfsInfoResult;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.ClusterTaskInfoRequest;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.CreateHPCClusterResult;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.CreateHPCSharePool;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.CreateHPCSharePoolResult;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.CreateHpcDrpResource;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.HPCActiveClusterRequest;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.HpcDrpExtentionNodeRequest;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.InsertHPCSharePool;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.InsertHpcDrpResource;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.QueryHPCShareCluster;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.QueryHpcDrpResourceRequest;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.ReleaseHPCSharePool;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.ResHpcClusterRemoteModule;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.ResHpcClusterResourceRemote;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.UpdateHpcDrpResource;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.UpdateRouteTableResult;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.UpdateRouteTableVO;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.VpcPeeringInfoResource;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.VpcPeeringInfoResult;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.VpcPeeringResource;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.VpcPeeringResult;
import cn.com.cloudstar.rightcloud.remote.api.resource.service.hpc.HPCRemoteService;
import cn.com.cloudstar.rightcloud.resource.service.hpc.HPCService;
import cn.com.cloudstar.rightcloud.resource.service.hpc.IResHpcClusterPoolService;

/**
 * <AUTHOR>
 * @date 2021/12/6 15:27
 */
@Component
@DubboService
public class HPCRemoteSercieImpl implements HPCRemoteService {

    @Autowired
    HPCService hpcService;

    @Autowired
    IResHpcClusterPoolService iResHpcClusterPoolService;

    @Autowired
    private IResHpcClusterDeleteNodeTaskService resHpcClusterDeleteNodeTaskService;

    @Override
    public void updateClusterStatus(UpdateClusterStatusRemote updateClusterStatusRemote) {
        hpcService.updateClusterStatus(updateClusterStatusRemote);
    }

    @Override
    public Long insertClusterResource(ResHpcClusterResourceRemote resHpcClusterResource) {
        return hpcService.insertClusterResource(
                BeanConvertUtil.convert(resHpcClusterResource, ResHpcClusterResource.class));
    }

    @Override
    public CreateHPCSharePoolResult createSharePool(CreateHPCSharePool createHPCSharePool) {
        return hpcService.createSharePool(createHPCSharePool);
    }

    @Override
    public ResHpcClusterRemoteModule selectByPrimaryKey(Long id) {
        return BeanConvertUtil.convert(hpcService.selectByPrimaryKey(id), ResHpcClusterRemoteModule.class);

    }

    @Override
    public ResHpcClusterPool selectResHpcCluster(String id) {
        return BeanConvertUtil.convert(hpcService.selectResHpcCluster(id), ResHpcClusterPool.class);

    }

    @Override
    public ResHpcClusterPool selectResHpcClusterById(Long id) {
        return BeanConvertUtil.convert(iResHpcClusterPoolService.getResHpcClusterPoolById(id), ResHpcClusterPool.class);
    }

    @Override
    public List<ResHpcClusterPool>  selectResHpcClusterPoolByClusterId(String clusterId) {
        Criteria criteria = new Criteria();
        criteria.put("clusterId",clusterId);
        return BeanConvertUtil.convert(iResHpcClusterPoolService.selectByParam(criteria),ResHpcClusterPool.class );
    }

    @Override
    public List<ResHpcClusterPool>  selectResHpcClusterPoolByClusterName(List<String> clusterNameList) {
        Criteria criteria = new Criteria();
        criteria.put("clusterNameIn",clusterNameList);
        return BeanConvertUtil.convert(iResHpcClusterPoolService.selectByParam(criteria),ResHpcClusterPool.class );
    }



    @Override
    public void updateResHpcClusterPoolById(ResHpcClusterPool clusterPool) {
        iResHpcClusterPoolService.updateHpcClusterPoolById(BeanConvertUtil.convert(clusterPool,cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResHpcClusterPool.class));
    }



    @Override
    public int updateByPrimaryKeySelective(ResHpcClusterRemoteModule resHpcCluster) {
        return hpcService.updateByPrimaryKeySelective(BeanConvertUtil.convert(resHpcCluster, ResHpcCluster.class));
    }

    @Override
    public ResHpcClusterRemoteModule insertResHpcCluster(InsertHPCSharePool insertHPCSharePool) {
        return BeanConvertUtil.convert(hpcService.insertResHpcCluster(insertHPCSharePool),
                                       ResHpcClusterRemoteModule.class);
    }

    @Override
    public ResHpcClusterRemoteModule insertHpcDrpResource(InsertHpcDrpResource insertHpcDrpResource) {
        return BeanConvertUtil.convert(hpcService.insertHpcDrpResource(insertHpcDrpResource),
                                       ResHpcClusterRemoteModule.class);
    }

    @Override
    public VpcPeeringResult vpcPeeringAccept(VpcPeeringResource vpcPeeringResource) {
        return hpcService.vpcPeeringAccept(vpcPeeringResource);
    }

    @Override
    public CreateHPCClusterResult createHPCCluster(CreateHpcDrpResource createHpcDrpResource) {
        return hpcService.createHPCCluster(createHpcDrpResource);
    }

    @Override
    public UpdateRouteTableResult updateRouteTable(UpdateRouteTableVO updateRouteTableVO) {
        return hpcService.updateRouteTable(updateRouteTableVO);
    }

    @Override
    public String updateHPCClusterCreateTask(ResHpcClusterRemoteModule resHpcClusterRemoteModule,
                                             FDTaskInfoResult result) {
        return hpcService.updateHPCClusterCreateTask(resHpcClusterRemoteModule, result);
    }

    @Override
    public ActiveHPCClusterResult activeHPCCluster(HPCActiveClusterRequest request) {
        return hpcService.activeHPCCluster(request);
    }

    @Override
    public List<ResHpcClusterRemoteModule> selectByParams(QueryHPCShareCluster queryHPCShareCluster) {
        return BeanConvertUtil.convert(hpcService.selectByParams(Criteria.prepareNewCriteria(queryHPCShareCluster)),
                                       ResHpcClusterRemoteModule.class);
    }

    @Override
    public ResHpcClusterRemoteModule updateHpcDrpResource(UpdateHpcDrpResource updateHpcDrpResource) {
        return BeanConvertUtil.convert(hpcService.updateHpcDrpResource(updateHpcDrpResource),
                                       ResHpcClusterRemoteModule.class);
    }

    @Override
    public void releaseShareHPC(ReleaseHPCSharePool releaseHPCSharePool) {
        hpcService.releaseShareHPC(releaseHPCSharePool);
    }


    /**
     * 返回关联的文件系统
     *
     * @param id hpc cluster id
     */
    @Override
    public List<ResShare> getShareHPCSFS(Long id) {
        return hpcService.getShareHPCSFS(id);
    }

    @Override
    public List<ResShare> getShareDMEOSP(Long id) {
        return hpcService.getShareDMEOSP(id);
    }


    @Override
    public void postProcessReleaseHpcShare(Long id, Boolean flag) {
        hpcService.postProcessReleaseHpcShare(id, flag);
    }

    @Override
    public void updateHpcCluster(ResHpcClusterRemoteModule hpcClusterRemoteModule) {
        hpcService.updateHpcCluster(hpcClusterRemoteModule);
    }

    @Override
    public void deleteHpcCluster(Long hpcClusterId, String status) {
        hpcService.deleteHpcCluster(hpcClusterId, status);
    }

    @Override
    public void addResShareRule(Long hpcClusterId) {
        hpcService.addResShareRule(hpcClusterId);
    }

    @Override
    public String getClusterTaskResult(ClusterTaskInfoRequest clusterTaskInfoRequest) {
        return hpcService.getClusterTaskResult(clusterTaskInfoRequest);
    }

    @Override
    public VpcPeeringInfoResult getVpcPeeringScanInfo(VpcPeeringInfoResource vpcPeeringInfoResource) {
        return hpcService.getVpcPeeringScanInfo(vpcPeeringInfoResource);
    }

    @Override
    public ClusterSfsInfoResult getClusterSfsInfo(Long clusterId) {
        return hpcService.getClusterSfsInfo(clusterId);
    }

    @Override
    public void rollbackShare(ResHpcClusterRemoteModule resHpcClusterRemoteModule) {
        hpcService.rollbackShare(resHpcClusterRemoteModule);
    }

    @Override
    public HPCClusterDeleteResult releaseDrpHPC(Long clusterId) {
        return hpcService.releaseDrpHPC(clusterId);
    }

    @Override
    public HPCClusterInfoIDResult getHPCClusterInfoById(Long clusterId) {
        return hpcService.getHPCClusterInfoById(clusterId);
    }

    @Override
    public Boolean checkHpcClusterName(String clusterName) {
        return hpcService.checkHpcClusterName(clusterName);
    }

    @Override
    public String getHpcClusterName(String account) {
        return hpcService.getHpcClusterName(account);
    }

    @Override
    public Map<String, String> getHpcDrpResourceInfo(QueryHpcDrpResourceRequest queryHpcDrpResourceRequest) {
        return hpcService.getHpcDrpResourceInfo(queryHpcDrpResourceRequest);
    }

    @Override
    public Boolean hpcDrpExpansionNode(HpcDrpExtentionNodeRequest extentionNodeRequest) {
        return hpcService.hpcDrpExpansionNode(extentionNodeRequest);
    }

    @Override
    public List<NodeInfo> upgradeHpcDrp(UpgradeHpcDrpResource upgradeHpcDrpResource) {
        return hpcService.upgradeHpcDrp(upgradeHpcDrpResource);
    }

    @Override
    public void hpcDrpGenerateClusterFile(Long clusterId) {
        hpcService.hpcDrpGenerateClusterFile(clusterId);
    }

    @Override
    public boolean operateNode(Long clusterId, List<String> computeList, List<String> managerList,
                               String action,boolean operateAdmin) {
        return hpcService.operateNode(clusterId, computeList, managerList, action,operateAdmin);

    }

    @Override
    public HPCNodeDifferVo queryHPCNodeDiffer(Long clusterId) {
        return hpcService.queryHPCNodeDiffer(clusterId);
    }

    @Override
    public String getNodeInfo(Long clusterId) {
        return hpcService.getNodeInfo(clusterId);

    }


    @Override
    public BaseGridReturn queryHPCNodeInfoByClusterId(HPCNodeInfoRequest request) {
        return hpcService.queryHPCNodeInfoByClusterId(request);
    }

    @Override
    public List<ResHpcClusterRemoteModule> selectByByPoolUuid(String poolUuid) {
        return BeanConvertUtil.convert(hpcService.selectByByPoolUuid(poolUuid),
                                       ResHpcClusterRemoteModule.class);
    }


    @Override
    public List<ResHpcClusterDeleteNodeTask> selectAllDeleteNodeTask() {
        return resHpcClusterDeleteNodeTaskService.selectAll();
    }

    @Override
    public void clearRemovingNode(Long clusterId) {
        hpcService.clearRemovingNode(clusterId);
    }

    /**
     * 移除节点和记录变更记录
     * @param clusterId
     */
    @Override
    public void clearRemovingNodeAndRecord(Long clusterId) {
        hpcService.clearRemovingNodeAndRecord(clusterId);
    }

    @Override
    public void removeDeleteNodeTaskByPrimaryKey(Long id) {
        resHpcClusterDeleteNodeTaskService.deleteByPrimaryKey(id);
    }

    @Override
    public void rollBackHPC(Long id) {
        hpcService.rollBackHPC(id);
    }

    @Override
    public Boolean checkMultipleHpcPool() {
        return hpcService.checkMultipleHpcPool();
    }

    @Override
    public HPCPreDrpMgmtResult hpcPreDrpMgmt(HPCPreDrpMgmt hpcPreDrpMgmt,
                                             Org currentOrg) {
        return BeanConvertUtil.convert(hpcService.hpcPreDrpMgmt(hpcPreDrpMgmt,currentOrg),
                                       HPCPreDrpMgmtResult.class);
    }

    @Override
    public ResHpcClusterRemoteModule delPreHpcPool(Long clusterId,String type) {
        return BeanConvertUtil.convert(hpcService.delPreHpcPool(clusterId,type),
                                       ResHpcClusterRemoteModule.class);
    }

    @Override
    public void updateHPCClusterStatus(ResHpcClusterRemoteModule resHpcClusterRemoteModule) {
        hpcService.updateHpcPreClusterStatus(resHpcClusterRemoteModule);
    }


    @Override
    public List<ResShare> getDefaultShareHPCSFS(Long id) {

            return hpcService.getDefaultShareHPCSFS(id);
    }

    @Override
    public void syncRes(HpcSyncCloudEnvRequest hpcSyncCloudEnvRequest) {
        hpcService.syncRes(hpcSyncCloudEnvRequest);
    }

    @Override
    public void updateShareMountPath(Long clusterId) {
        hpcService.updateShareMountPath(clusterId);
    }


    @Override
    public void updateInnerShareMountPath(Long clusterId) {
        hpcService.updateInnerShareMountPath(clusterId);
    }

    @Override
    public ResHpcClusterRemoteModule isUnfreezeByResShare(Long id) {
        return BeanConvertUtil.convert(hpcService.isUnfreezeByResShare(id),
                ResHpcClusterRemoteModule.class);
    }

    @Override
    public List<ResHpcClusterRemoteModule> getHpcClusterComputeResources() {
        return BeanConvertUtil.convert(hpcService.getHpcClusterComputeResources(),
                ResHpcClusterRemoteModule.class);
    }

    @Override
    public void clearResHpcClusterPoolOwnerByClusterId(String clusterUuid) {
        iResHpcClusterPoolService.clearOwnerByCusterId(clusterUuid);
    }

    @Override
    public boolean checkClusterUsed(String clusterUuid) {
        return iResHpcClusterPoolService.checkUsed(clusterUuid);
    }
}
