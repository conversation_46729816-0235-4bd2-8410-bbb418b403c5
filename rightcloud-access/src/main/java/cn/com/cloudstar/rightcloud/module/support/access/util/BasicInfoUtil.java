/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.module.support.access.util;

import cn.com.cloudstar.rightcloud.common.redis.JedisUtil;
import cn.com.cloudstar.rightcloud.common.util.json.JsonUtil;
import cn.com.cloudstar.rightcloud.module.support.access.cache.AuthUserHolder;
import cn.com.cloudstar.rightcloud.module.support.access.constants.AuthConstants;
import cn.com.cloudstar.rightcloud.module.support.access.constants.BasicSqlEnum;
import cn.com.cloudstar.rightcloud.module.support.access.constants.DataScopeEnum;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.Org;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.Role;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.User;
import cn.com.cloudstar.rightcloud.module.support.db.util.DBUtils;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.base.CaseFormat;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.dubbo.rpc.RpcContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.function.Supplier;

/**
 * 系统基础信息获取工具类
 *
 * <AUTHOR> @date
 */
public class BasicInfoUtil {

    private static Logger logger = LoggerFactory.getLogger(BasicInfoUtil.class);

    /**
     * 是否为系统菜单请求
     */
    public static final String IS_SYSTEM = "isSystem";

    private static final String EMPTY_STR = "null";

    private static final String BASE_ORG_SQL =
            "SELECT\n" + "A.ORG_SID,\n" + "A.ORG_NAME,\n" + "A.ORG_CODE,\n" + "A.ORG_TYPE,\n" +
                    "A.TREE_PATH,\n" + "A.PARENT_ID,\n" + "A.ORG_ICON,\n" + "A.STATUS,\n" + "A.LDAP_OU\n" +
                    "FROM sys_m_org A\n" +
                    "WHERE A.ORG_SID = ?";

    private static final String USER_BASE_INFO = "SELECT A.USER_SID, A.USER_TYPE, A.ACCOUNT, A.REAL_NAME, A.SEX, A.EMAIL," +
            " A.MOBILE, A.STATUS, A.COMPANY_ID,A.CREATED_DT FROM sys_m_user A where A.ACCOUNT = ? and A.status = 1";

    private static final String ROLE_PERMISSION = "SELECT 1 FROM sys_m_role_module WHERE role_sid = ? AND module_sid = ?";

    private static final String QUERY_USER_BY_ID_SQL =
            "SELECT\n"
                    + "A.USER_SID AS userSid,\n"
                    + "A.USER_TYPE AS userType,\n"
                    + "A.ACCOUNT AS account,\n"
                    + "A.REAL_NAME AS realName,\n"
                    + "A.SEX AS sex,\n"
                    + "A.EMAIL AS email,\n"
                    + "A.MOBILE AS mobile,\n"
                    + "A.title AS title,\n"
                    + "A.STATUS AS status,\n"
                    + "A.UUID AS uuid,\n"
                    + "A.ORG_SID AS orgSid,\n"
                    + "A.COMPANY_ID AS companyId\n"
                    + "FROM\n"
                    + "  sys_m_user A\n"
                    + "WHERE A.USER_SID = ?";

    private static final String USER_SQL =
            "SELECT\n"
                    + "A.USER_SID,\n"
                    + "A.USER_TYPE,\n"
                    + "A.ACCOUNT,\n"
                    + "A.REAL_NAME,\n"
                    + "A.SEX,\n"
                    + "A.EMAIL,\n"
                    + "A.MOBILE,\n"
                    + "A.PROJECT_ID,\n"
                    + "A.STATUS,\n"
                    + "A.REMARK,\n"
                    +
                    "A.LAST_LOGIN_IP,\n"
                    + "A.UUID,\n"
                    + "A.ORG_SID,\n"
                    + "A.COMPANY_ID,\n"
                    +
                    "B.CODE_DISPLAY AS USER_TYPE_NAME,\n"
                    + "IF (SEX = 1, '女', '男') AS SEX_NAME,\n"
                    +
                    " C.CODE_DISPLAY AS STATUS_NAME\n"
                    + "FROM\n"
                    + "  sys_m_user A\n"
                    +
                    "LEFT JOIN sys_m_code B ON A.USER_TYPE = B.CODE_VALUE AND B.CODE_CATEGORY = 'USER_TYPE'\n"
                    +
                    "LEFT JOIN sys_m_code C ON A. STATUS = C.CODE_VALUE AND C.CODE_CATEGORY = 'USER_STATUS'\n"
                    +
                    "WHERE A.ACCOUNT = ?";

    private static final String PARENT_SID = "select  parent_user_sid  from sys_m_user_parent where user_sid=?";
    private static final String HCSOAKSK = "select ak,sk,account_id,project_id from hcso_user where  ref_user_id=?";

    /**
     * 取得HttpRequest的简化函数.
     */
    public static HttpServletRequest getRequest() {
        try {
            return ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        } catch (Exception e) {
            return null;
        }
    }


    /**
     * 从请求头中获取请求来源
     */
    public static String getRequestSource() {
        HttpServletRequest request = getRequest();
        if (request != null) {
            String reqSource = request.getHeader("REQ_SOURCE");
            if (org.apache.commons.lang3.StringUtils.isBlank(reqSource)) {
                reqSource = request.getHeader("req_source");
            }
            return reqSource;
        }
        //dubbo
        RpcContext rpcContext = RpcContext.getContext();
        String reqSource = rpcContext.getAttachment(AuthConstants.HEADER_REQ_SOURCE);
        return org.apache.commons.lang3.StringUtils.isBlank(reqSource) ? "" : reqSource;
    }

    // region Bss用户权限Scope追加
    /**
     * 从请求头中获取请求来源
     */
    public static String getRequestDataScope() {
        HttpServletRequest request = getRequest();
        if (request != null) {
            return request.getHeader("data_scope");
        }
        //dubbo
        RpcContext rpcContext = RpcContext.getContext();
        String reqDataScope = rpcContext.getAttachment(AuthConstants.HEADER_DATA_SCOPE);
        return org.apache.commons.lang3.StringUtils.isBlank(reqDataScope) ? null : reqDataScope;
    }
    // endregion

    /**
     * 获得当前用户
     *
     * @return the auth user info
     */
    public static AuthUser getCurrentUserInfo() {
        AuthUser authUser = AuthUserHolder.getAuthUser();
        if (Objects.nonNull(authUser)) {
            return authUser;
        }
        HttpServletRequest request = getRequest();
        if (Objects.isNull(request)) {
            return null;
        } else {
            return getCurrentUserInfo(request);
        }
    }

    /**
     * 获得当前用户
     *
     * @param req the req
     *
     * @return the auth user info
     */
    public static AuthUser getCurrentUserInfo(HttpServletRequest req) {
        if (req == null) {
            return null;
        }
        AuthUser authUser = null;
        String userSid = req.getHeader(AuthConstants.HEADER_USER);
        String userAccount = req.getHeader(AuthConstants.HEADER_ACCOUNT);
        Object entityId = req.getHeader(AuthConstants.ENTITY_ID);

        // 从Header取得remark
        List<AuthUser> authUsers= new ArrayList<>();
        if (!StringUtils.isEmpty(userSid) && !EMPTY_STR.equals(userSid)) {
            // 根据用户id获取用户信息
             authUsers = DBUtils.INSTANCE.queryBeanList(BasicSqlEnum.QUERY_USER_BY_ID.getValue(), AuthUser.class, userSid);
        } else if (!StringUtils.isEmpty(userAccount) && !EMPTY_STR.equals(userAccount)) {
            // 根据用户account获取用户信息
             authUsers = DBUtils.INSTANCE.queryBeanList(BasicSqlEnum.QUERY_USER_BY_ACCOUNT.getValue(),
                                                            AuthUser.class, userAccount);
        }
        authUser = setOrgSid(req, authUsers);
        if (authUser != null) {
            String remark = req.getHeader(AuthConstants.HEADER_MODULE_TYPE);
            if (!Strings.isNullOrEmpty(remark)) {
                authUser.setRemark(remark);
            }

            if(Objects.nonNull(entityId)){
                authUser.setEntityId(Long.valueOf(entityId.toString()) );
            }
        }
        return authUser;
    }

    private static AuthUser setOrgSid(HttpServletRequest req, List<AuthUser> authUsers) {
        AuthUser authUser = null;
        if (!CollectionUtils.isEmpty(authUsers)) {
            authUser = authUsers.get(0);
            String orgId = req.getHeader(AuthConstants.HEADER_ORG);
            if (!Strings.isNullOrEmpty(orgId) && !"null".equals(orgId)) {
                authUser.setOrgSid(Long.parseLong(orgId));
            }
            setAdminFlag(authUser);
        }
        return authUser;
    }

    private static void setAdminFlag(AuthUser authUser) {
        if (authUser == null) {
            return;
        }

        Map map = DBUtils.INSTANCE.queryMap(BasicSqlEnum.QUERY_USER_ADMIN_FLAG.getValue(), authUser.getUserSid());
        boolean adminFlag = !CollectionUtils.isEmpty(map) && Convert.toInt(map.get("adminRoleCount")) > 0;
        authUser.setAdminFlag(adminFlag);
    }

    public static AuthUser getCurrentUserInfo(String userAccount) {
        if (Strings.isNullOrEmpty(userAccount)) {
            return null;
        }
        // Redis中
        Map<String, String> userInfo = JedisUtil.instance().hgetall(AuthConstants.CACHE_KEY_USER_PREFIX
                                                                            + userAccount);
        if (userInfo != null && userInfo.size() > 0) {
            return JsonUtil.fromJson(JsonUtil.toJson(userInfo), AuthUser.class);
        }

        Map dbUserMap = null;
        try {
            dbUserMap = DBUtils.INSTANCE.queryMap(USER_SQL, userAccount);
        } catch (Exception ignored) {}
        Map<String, String> userMap = new HashMap<>(16);
        for (Object column : dbUserMap.keySet()) {
            String key = column.toString();
            String camelKey = CaseFormat.UPPER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, key);
            userMap.put(camelKey, StringUtil.nullToEmpty(dbUserMap.get(key)));
        }

        AuthUser authUser = JsonUtil.fromJson(JsonUtil.toJson(userMap), AuthUser.class);
        JedisUtil.instance()
                 .hmset(AuthConstants.CACHE_KEY_USER_PREFIX
                                + userAccount, userMap, AuthConstants.PERIED_TIME);

        return authUser;
    }

    /**
     * 根据用户id获取用户信息
     *
     * @param userSid 用户id
     */
    public static AuthUser getUserInfoByUserSid(Long userSid) {
        return getUserInfoByUserSid(userSid, null);
    }

    /**
     * 根据用户id获取用户信息
     *
     * @param userSid 用户id
     * @param orgSid 设置用户当前组织id，null不设置
     */
    public static AuthUser getUserInfoByUserSid(Long userSid, Long orgSid) {
        AuthUser authUser = null;
        // 根据用户id获取用户信息
        List<AuthUser> authUsers = DBUtils.INSTANCE.queryBeanList(BasicSqlEnum.QUERY_USER_BY_ID.getValue(), AuthUser.class,
                                                        userSid);
        if (!CollectionUtils.isEmpty(authUsers)) {
            authUser = authUsers.get(0);
            if (null != orgSid) {
                authUser.setOrgSid(orgSid);
            }
        }
        return authUser;
    }

    /**
     * 获得当前用户
     *
     * @return the auth user info
     */
    public static AuthUser getUserInfoByAccount(String account) {
        if (!StringUtils.isEmpty(account) && !EMPTY_STR.equals(account)) {
            // 根据用户account获取用户信息
            List<AuthUser> authUsers = DBUtils.INSTANCE.queryBeanList(BasicSqlEnum.QUERY_USER_BY_ACCOUNT.getValue(),
                                                            AuthUser.class, account);
            if (!CollectionUtils.isEmpty(authUsers)) {
                return authUsers.get(0);
            }
        }
        return null;
    }


    /**
     * 该方法为了适应很多地方需要判断角色的情况，按数据权限范围判断角色
     */
    public static String getCurrentUserType() {
        String maxDataScope = getMaxDataScope();
        // 企业管理员
        if (DataScopeEnum.DATA_SCOPE_COMPANY_AND_CHILD.getScope().equals(maxDataScope)) {
            return "02";
            // 项目管理员
        } else if (DataScopeEnum.DATA_SCOPE_COMPANY.getScope().equals(maxDataScope)) {
            return "03";
            // 项目用户
        } else if (DataScopeEnum.DATA_SCOPE_SELF.getScope().equals(maxDataScope)) {
            return "04";
        }

        return "99";
    }

    /**
     * 获取当前组织（线程缓存）
     */
    public static Org getCurrentOrg() {
        Org org = AuthUserHolder.getOrg();
        if(Objects.nonNull(org)){
            return org;
        }
        return getCurrentOrgInfo();
    }

    /**
     * 获得当前组织ID
     *
     * @return the org sid
     */
    public static Long getCurrentOrgSid() {
        Org org = AuthUserHolder.getOrg();
        if (org != null) {
            return org.getOrgSid();
        }

        HttpServletRequest request = getRequest();
        if (Objects.isNull(request)) {
            return null;
        } else {
            String orgid = request.getHeader(AuthConstants.HEADER_ORG);
            if (!StringUtils.isEmpty(orgid) && !EMPTY_STR.equals(orgid)) {
                return Long.parseLong(orgid);
            } else {
                AuthUser authUser = getCurrentUserInfo();
                if(authUser==null) {
                    return null;
                }
                return authUser.getOrgSid();
            }
        }
    }

    /**
     * 获得当前组织信息
     *
     * @return the org info
     */
    public static Org getCurrentOrgInfo() {
        Long orgSid = getCurrentOrgSid();
        if (null != orgSid) {
            return getCurrentOrgInfo(orgSid);
        }
        return null;
    }

    public static Org getCurrentOrgInfo(Long orgSid) {
        // 根据id获取组织信息
        List<Org> orgList = DBUtils.INSTANCE.queryBeanList(BasicSqlEnum.QUERY_ORG_BY_ID.getValue(), Org.class, orgSid);
        if (!CollectionUtils.isEmpty(orgList)) {
            return orgList.get(0);
        }
        return null;
    }

    /**
     * 获取当前主公司的ID
     */
    public static Long getCurrentMainOrgSid() {
        Org org = getCurrentOrg();
        return getCurrentMainOrgSid(org);
    }

    public static Long getCurrentMainOrgSid(Org org) {
        try {
            String treePath = org.getTreePath();
            if ("/".equals(treePath)) {
                return org.getOrgSid();
            }
            String mainOrgSid = treePath.split("/")[1];
            return Long.valueOf(mainOrgSid);
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return null;
    }

    /**
     * 获取当前用户的角色列表
     */
    private static List<Role> getRoleList() {
        String userSid = getRequest().getHeader(AuthConstants.HEADER_USER);
        if (!StringUtils.isEmpty(userSid)) {
            return DBUtils.INSTANCE.queryBeanList(BasicSqlEnum.QUERY_ROLE_BY_USER.getValue(), Role.class, userSid);
        }
        return null;
    }

    /**
     * 获取用户在指定组织的角色列表
     */
    private static List<Role> getRoleList(Long orgSid) {
        AuthUser authUser = getCurrentUserInfo();
        return DBUtils.INSTANCE.queryBeanList(BasicSqlEnum.QUERY_ROLE_BY_USER_ORG.getValue(), Role.class, authUser.getUserSid(), orgSid);
    }

    public static Boolean getIsSystemMenu() {
        HttpServletRequest httpRequest = getRequest();
        Boolean result = false;
        String isAdmin = httpRequest.getHeader(BasicInfoUtil.IS_SYSTEM);
        if (!StringUtils.isEmpty(isAdmin) && !"null".equals(isAdmin)) {
            result = Boolean.parseBoolean(isAdmin);
        }
        return result;
    }

    public static String getMaxDataScope() {
        return getMaxDataScope(getCurrentUserInfo());
    }

    public static String getMaxDataScope(AuthUser authUser) {
        List<Role> roleList = Lists.newArrayList();
        // 如果是超级管理员，数据权限范围为全部
        if (Objects.nonNull(authUser) && authUser.isAdmin()) {
            return DataScopeEnum.DATA_SCOPE_ALL.getScope();
        } else if (Objects.isNull(authUser)) {
            return DataScopeEnum.DATA_SCOPE_ALL.getScope();
        }

        // region Bss用户权限Scope追加
        String dataScope = getRequestDataScope();
        if (Objects.nonNull(dataScope)) {
            return dataScope;
        }
        // endregion

        if (null == authUser.getOrgSid()) {
            roleList = getRoleList();
        }else{
            roleList = getRoleList(authUser.getOrgSid());
        }

        // 获取到最大的数据权限范围
        int dataScopeInteger = 1;
        for (Role r : roleList) {
            int ds = Integer.parseInt(r.getDataScope());
            if (ds == 9) {
                dataScopeInteger = ds;
                break;
            } else if (ds < dataScopeInteger) {
                dataScopeInteger = ds;
            }
        }

        return String.valueOf(dataScopeInteger);
    }

    public static String getSQLFilter(String tableAlias, String orgField, String userField, boolean selfService) {
        return getSQLFilter(tableAlias, orgField, userField, selfService, null, null, false);
    }

    /**
     * 获取数据过滤的SQL（符合业务表字段不同的时候使用，采用exists方法）
     */
    public static String getSQLFilter(String tableAlias, String orgField, String userField, boolean selfService,
                                      Long mockOrgSid, Long mockUserSid, boolean userIdAsValue) {
        // 解决取不到当前用户报错的情况（1. 回调的时候 2.直接用前台访问小概率会出现取不到当前用户的情况）
        AuthUser user = getAuthUser(mockOrgSid, mockUserSid);
        if (Objects.isNull(user)) {
            return null;
        }
        Org org;
        if (null != mockOrgSid) {
            org = getCurrentOrgInfo(mockOrgSid);
        } else {
            org = getCurrentOrg();
        }
        //如果是运营项目 并且 orgId 为 0  则不作权限过滤
        if (needIgnoreOrg()) {
            return null;
        }
        String dataScopeString = getMaxDataScope(user);
        //获取表的别名
        if (!Strings.isNullOrEmpty(tableAlias)) {
            tableAlias += ".";
        }
        //拼接sql
        StringBuilder sqlFilter = getStringBuilder(tableAlias, orgField, userField, userIdAsValue, user, org, dataScopeString);
        if (!Strings.isNullOrEmpty(sqlFilter.toString())) {
            return " AND (" + sqlFilter.toString() + ")";
        }

        return null;

    }

    private static StringBuilder getStringBuilder(String tableAlias, String orgField, String userField, boolean userIdAsValue, AuthUser user, Org org, String dataScopeString) {
        StringBuilder sqlFilter = new StringBuilder();
        // 生成组织权限SQL语句
        if (DataScopeEnum.DATA_SCOPE_COMPANY_AND_CHILD.getScope().equals(dataScopeString)) {
            // 仅查看当前组织及以下的数据
            appendSqlFilterCompanyAndChild(tableAlias, orgField, org, sqlFilter);

        } else if (DataScopeEnum.DATA_SCOPE_COMPANY.getScope().equals(dataScopeString)) {
            // 仅查看当前组织的数据
            appendSqlFilterCompany(tableAlias, orgField, org, sqlFilter);
        } else if (DataScopeEnum.DATA_SCOPE_SELF.getScope().equals(dataScopeString)) {
            // 仅查看当前组织的个人数据
            appendSqlFilterCompany(tableAlias, orgField, org, sqlFilter);
            appendSqlFilterSelf(tableAlias, userField, userIdAsValue, user, sqlFilter);
        } else if (DataScopeEnum.DATA_SCOPE_CUSTOM.getScope().equals(dataScopeString)) {

        } else if (DataScopeEnum.DATA_SCOPE_ALL.getScope().equals(dataScopeString) && !Objects.isNull(org)) {
            // 查看当前指定组织及以下的数据
            appendSqlFilterCompanyAndChild(tableAlias, orgField, org, sqlFilter);
        }
        return sqlFilter;
    }

    private static void appendSqlFilterSelf(String tableAlias, String userField, boolean userIdAsValue, AuthUser user, StringBuilder sqlFilter) {
        if (!Strings.isNullOrEmpty(userField)) {
            if (userIdAsValue) {
                sqlFilter.append(" AND EXISTS (");
                sqlFilter.append(" SELECT 1 FROM dual");
                sqlFilter.append(" WHERE ").append(tableAlias).append(userField).append(" = ");
                sqlFilter.append(" '").append(user.getUserSid()).append("' ) ");
            } else {
                sqlFilter.append(" AND");
                sqlFilter.append(" EXISTS (SELECT 1 FROM sys_m_user");
                sqlFilter.append(" WHERE user_sid = '" + user.getUserSid() + "'");
                sqlFilter.append(" AND account = " + tableAlias + userField + ")");
            }
        }
    }

    private static void appendSqlFilterCompany(String tableAlias, String orgField, Org org, StringBuilder sqlFilter) {
        sqlFilter.append(" EXISTS (SELECT 1 FROM sys_m_org");
        sqlFilter.append(" WHERE org_sid = '" + org.getOrgSid() + "'");
        sqlFilter.append(" AND org_sid = " + tableAlias + orgField + ")");
    }

    private static void appendSqlFilterCompanyAndChild(String tableAlias, String orgField, Org org, StringBuilder sqlFilter) {
        sqlFilter.append(" EXISTS (SELECT 1 FROM sys_m_org");
        sqlFilter.append(" WHERE (org_sid = '" + org.getOrgSid() + "'");
        sqlFilter.append(" OR tree_path LIKE '" + org.getTreePath() + org.getOrgSid() + "/%')");
        // 对业务数据的组织ID不需要比较org_id的，传空
        if (StrUtil.isNotBlank(orgField)) {
            sqlFilter.append(" AND org_sid = " + tableAlias + orgField);
        }
        sqlFilter.append(")");
    }

    private static AuthUser getAuthUser(Long mockOrgSid, Long mockUserSid) {
        AuthUser user;
        try {
            //如果是系统菜单相关接口 和 openAPi调用
            if (BasicInfoUtil.getIsSystemMenu()) {
                return null;
            }
            if (null != mockUserSid) {
                user = getUserInfoByUserSid(mockUserSid, mockOrgSid);
            } else {
                user = getCurrentUserInfo();
            }
        } catch (Exception e) {
            return null;
        }
        return user;
    }

    public static String getSQLFilterWithGivingOrgSid(String tableAlias, String orgField, Long orgSid) {
        Org org = getCurrentOrgInfo(orgSid);
        if (Objects.isNull(org)) {
            return null;
        }
        //获取表的别名
        if (!StringUtils.isEmpty(tableAlias)) {
            tableAlias += ".";
        }
        StringBuilder sqlFilter = new StringBuilder();
        // 生成组织权限SQL语句,仅查看当前组织及以下的数据
        sqlFilter.append(" EXISTS (SELECT 1 FROM sys_m_org");
        sqlFilter.append(" WHERE (org_sid = '").append(org.getOrgSid()).append("'");
        sqlFilter.append(" OR tree_path LIKE '").append(org.getTreePath()).append(org.getOrgSid()).append("/%')");
        sqlFilter.append(" AND org_sid = ").append(tableAlias).append(orgField).append(")");
        if (!StringUtils.isEmpty(sqlFilter.toString())) {
            return " AND (" + sqlFilter.toString() + ")";
        }
        return null;
    }


    /**
     * 根据数据权限获取查询条件
     * 仅根据组织查询，如果数据权限不是组织及以下数据，则只能查询当前组织的数据
     *
     * @param tableAlias 表别名
     * @param orgField org_sid对应的字段
     * @param orgSid 当前org_sid
     * @param existInOrg 是否查询存在于组织中的数据（true： 存在于组织中的数据， false：不存在于组织中的数据）
     * @return 权限过滤条件
     */
    public static String getSQLFilterOnlyByOrgSid(String tableAlias, String orgField, Long orgSid, boolean existInOrg) {
        Org org = queryOrg(orgSid);
        String dataScopeString = getMaxDataScope();

        //获取表的别名
        if (!Strings.isNullOrEmpty(tableAlias) && !tableAlias.contains(".")) {
            tableAlias += ".";
        }

        StringBuilder sqlFilter = new StringBuilder();

        // 查询在当前用户可查询范围内，不在指定项目下的数据
        if (!existInOrg) {
            // 当前组织下可查询的数据
            String existDf = getSQLFilterOnlyByOrgSid(tableAlias, orgField,
                                                               getCurrentOrgSid(), true);
            sqlFilter.append(existDf);

            sqlFilter.append("AND NOT");
        }
        sqlFilter.append(" EXISTS (SELECT 1 FROM sys_m_org");

        // 生成组织权限SQL语句
        if (DataScopeEnum.DATA_SCOPE_COMPANY_AND_CHILD.getScope().equals(dataScopeString)) {
            // 仅查看当前组织及以下的数据
            sqlFilter.append(" WHERE (org_sid = '").append(org.getOrgSid()).append("'");
            sqlFilter.append(" OR tree_path LIKE '").append(org.getTreePath()).append(org.getOrgSid()).append("/%')");
            sqlFilter.append(" AND org_sid = ").append(tableAlias).append(orgField).append(")");
        } else if (DataScopeEnum.DATA_SCOPE_ALL.getScope().equals(dataScopeString) && !Objects.isNull(org)) {
            // 超级管理员查看指定组织及以下的数据
            sqlFilter.append(" WHERE (org_sid = '").append(org.getOrgSid()).append("'");
            sqlFilter.append(" OR tree_path LIKE '").append(org.getTreePath()).append(org.getOrgSid()).append("/%')");
            sqlFilter.append(" AND org_sid = ").append(tableAlias).append(orgField).append(")");
        } else {
            // 仅查看当前组织的数据
            sqlFilter.append(" WHERE org_sid = '").append(org.getOrgSid()).append("'");
            sqlFilter.append(" AND org_sid = ").append(tableAlias).append(orgField).append(")");
        }

        if (!existInOrg) {
            return sqlFilter.toString();
        }

        if (!Strings.isNullOrEmpty(sqlFilter.toString())) {
            return " AND (" + sqlFilter.toString() + ")";
        }

        return null;
    }

    public static Org queryOrg(Long orgSid) {
        Map<String, String> orgMap = new HashMap<>();
        Map<String, Object> dbOrgMap = null;
        try {
            dbOrgMap = DBUtils.INSTANCE.queryMap(BASE_ORG_SQL, orgSid);
        } catch (Exception ignored) {}
        if (!CollectionUtils.isEmpty(dbOrgMap)) {
            for (Object column : dbOrgMap.keySet()) {
                String key = column.toString();
                String camelKey = CaseFormat.UPPER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, key);
                orgMap.put(camelKey, StringUtil.nullToEmpty(dbOrgMap.get(key)));
            }
        }

        return JsonUtil.fromJson(JsonUtil.toJson(orgMap), Org.class);
    }


    /**
     * 获取登录用户实体
     */
    public static User getAuthUser() {
        User user;
        try {
            AuthUser authUser = getCurrentUserInfo();

            Map<String, Object> dbUserMap = null;
            try {
                dbUserMap = DBUtils.INSTANCE.queryMap(USER_BASE_INFO, authUser.getAccount());
            } catch (Exception ignored) {}
            Map<String, String> userMap = new HashMap<>();
            for (Object column : dbUserMap.keySet()) {
                String key = column.toString();
                String camelKey = CaseFormat.UPPER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, key);
                userMap.put(camelKey, StringUtil.nullToEmpty(dbUserMap.get(key)));
            }

            user = JSONUtil.toBean(JsonUtil.toJson(userMap), User.class);
            user.setAdminFlag(authUser.getAdminFlag());
            user.setRemark(authUser.getRemark());
        } catch (Exception e) {
            user = null;
        }

        return user;
    }

    public static boolean hasPermission(String moduleSid) {
        Long orgSid = getCurrentOrgSid();
        Long maxRole = getMaxRole(orgSid);

        Map map = null;
        try {
            map = DBUtils.INSTANCE.queryMap(ROLE_PERMISSION, maxRole, moduleSid);
        } catch (Exception ignored) {}

        return !CollectionUtils.isEmpty(map);
    }

    /**
     * 获取用户在当前组织的最大角色
     *
     * @param orgSid 组织ID
     * @return 角色ID
     */
    private static Long getMaxRole(Long orgSid) {
        List<Role> roleList = getRoleList(orgSid);

        Long maxRoleSid = 0L;
        int dataScopeInteger = 8;
        for (Role r : roleList) {
            int ds = Integer.parseInt(r.getDataScope());
            if (ds == 9) {
                maxRoleSid = r.getRoleSid();
                break;
            } else if (ds < dataScopeInteger) {
                dataScopeInteger = ds;
                maxRoleSid = r.getRoleSid();
            }
        }

        return maxRoleSid;
    }

    public static Long getCurrentOrgSidNoException(Long orgId) {
        try {
            return getCurrentOrgSid();
        } catch (Exception e) {
            if (orgId != null) {
                return orgId;
            }
        }
        return null;
    }

    public static Long getCurrentUserSid() {
        AuthUser authUser = getCurrentUserInfo();
        if (authUser != null) {
            return authUser.getUserSid();
        }
        return null;
    }

    public static String getCurrentUserName() {
        return getCurrentUserName("admin");
    }

    public static String getCurrentUserName(String defaultUserName) {
        AuthUser authUser = getCurrentUserInfo();
        if (authUser != null) {
            return authUser.getAccount();
        } else {
            return defaultUserName;
        }
    }


    public static Long getParentUserSid() {
        AuthUser authUser = getCurrentUserInfo();
        if (authUser == null) {
            return null;
        }
        Map map = MapUtil.newHashMap();
        try {
            map = DBUtils.INSTANCE.queryMap(PARENT_SID, authUser.getUserSid());
        } catch (Exception ignored) {

        }
        return Optional.ofNullable(map)
                                 .map(v -> v.get("parent_user_sid"))
                                 .map(it -> Long.parseLong(it.toString()))
                                  //没有父用户,则当前用户可能为租户管理员
                                 .orElse(authUser.getUserSid());
    }

    /**
     * 运营系统为每个租户管理员一个单独的账号 所以普通用户则获取自己的父用户去拿对应的 aksk
     */
    public static Map getHCSOAKSK() {
        Long parentUserSid = BasicInfoUtil.getParentUserSid();
        if (parentUserSid == null) {
            return MapUtil.newHashMap();
        }
        Map map = null;
        try {
            map = DBUtils.INSTANCE.queryMap(HCSOAKSK, parentUserSid);
        } catch (Exception ignored) {

        }
        return Optional.ofNullable(map)
                .orElse(MapUtil.newHashMap());
    }

    public static boolean needIgnoreOrg() {
        String reqSource;
        Long orgId = 0L;
        HttpServletRequest request = getRequest();
        if (null != request) {
            reqSource = getRequestSource();
            if (Objects.nonNull(getCurrentOrgSid())) {
                orgId = getCurrentOrgSid();
            }
        } else {
            //dubbo
            RpcContext rpcContext = RpcContext.getContext();
            reqSource = rpcContext.getAttachment(AuthConstants.HEADER_REQ_SOURCE);
            if (null != rpcContext.getAttachment(AuthConstants.HEADER_ORG)) {
                orgId = Long.valueOf(rpcContext.getAttachment(AuthConstants.HEADER_ORG));
            }
        }
        if ("CLOUD_BOSS".equals(reqSource) && Long.valueOf(0).equals(orgId)) {
            return true;
        }
        return false;
    }

    /**
     * 替换用户来调用方法
     *
     * @param supplier
     * @param userSid
     */
    public static <T> T replaceUserToInvoke(Supplier<T> supplier, Long userSid) {
        //老用户
        AuthUser auditUser = AuthUserHolder.getAuthUser();
        Org auditUserOrg = AuthUserHolder.getOrg();
        //执行方法的用户
        AuthUser authUser = BasicInfoUtil.getUserInfoByUserSid(userSid);
        AuthUserHolder.setAuthUser(authUser);
        if(Objects.nonNull(authUser)){
            AuthUserHolder.setOrg(BasicInfoUtil.getCurrentOrgInfo(authUser.getOrgSid()));
        }
        T t;
        try {
            //调用方法
            t = supplier.get();
            return t;
        } finally {
            //还原用户
            AuthUserHolder.setAuthUser(auditUser);
            AuthUserHolder.setOrg(auditUserOrg);
        }
    }


    /**
     * 替换用户来调用方法
     *
     * @param supplier
     * @param userSid
     */
    public static void replaceUserToInvoke(NoArgAndVoidFuncion supplier, Long userSid) {
        //老用户
        AuthUser auditUser = AuthUserHolder.getAuthUser();
        Org auditUserOrg = AuthUserHolder.getOrg();
        //执行方法的用户
        AuthUser authUser = BasicInfoUtil.getUserInfoByUserSid(userSid);
        AuthUserHolder.setAuthUser(authUser);
        AuthUserHolder.setOrg(BasicInfoUtil.getCurrentOrgInfo(authUser.getOrgSid()));
        //调用方法
        try {
            supplier.run();
        } finally {
            //还原用户
            AuthUserHolder.setAuthUser(auditUser);
            AuthUserHolder.setOrg(auditUserOrg);
        }
    }

    @FunctionalInterface
    public interface NoArgAndVoidFuncion {
        void run();
    }


}
