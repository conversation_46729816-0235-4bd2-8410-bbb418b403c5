<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.cloudstar.rightcloud.resource.dao.network.ResFireWallAntivirusPolicyMapper">
    <resultMap id="BaseResultMap" type="cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResFireWallAntivirusPolicy">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="uuid" property="uuid" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="inspect_protocols" property="inspectProtocols" jdbcType="VARCHAR"/>
        <result column="exception_applications" property="exceptionApplications" jdbcType="VARCHAR"/>
        <result column="exception_signatures" property="exceptionSignatures" jdbcType="VARCHAR"/>
        <result column="tenant_id" property="tenantId" jdbcType="VARCHAR"/>
        <result column="tenant_name" property="tenantName" jdbcType="VARCHAR"/>
        <result column="cloud_env_id" property="cloudEnvId" jdbcType="BIGINT"/>
        <result column="org_sid" property="orgSid" jdbcType="BIGINT"/>
        <result column="created_dt" property="createdDt" jdbcType="TIMESTAMP"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="updated_dt" property="updatedDt" jdbcType="TIMESTAMP"/>
        <result column="updated_by" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.uuid != null">
                and uuid = #{condition.uuid}
            </if>
            <if test="condition.name != null">
                and name = #{condition.name}
            </if>
            <if test="condition.description != null">
                and description = #{condition.description}
            </if>
            <if test="condition.inspectProtocols != null">
                and inspect_protocols = #{condition.inspectProtocols}
            </if>
            <if test="condition.exceptionApplications != null">
                and exception_applications = #{condition.exceptionApplications}
            </if>
            <if test="condition.exceptionSignatures != null">
                and exception_signatures = #{condition.exceptionSignatures}
            </if>
            <if test="condition.tenantId != null">
                and tenant_id = #{condition.tenantId}
            </if>
            <if test="condition.tenantName != null">
                and tenant_name = #{condition.tenantName}
            </if>
            <if test="condition.cloudEnvId != null">
                and cloud_env_id = #{condition.cloudEnvId}
            </if>
            <if test="condition.orgSid != null">
                and org_sid = #{condition.orgSid}
            </if>
            <if test="condition.createdDt != null">
                and created_dt = #{condition.createdDt}
            </if>
            <if test="condition.createdBy != null">
                and created_by = #{condition.createdBy}
            </if>
            <if test="condition.updatedDt != null">
                and updated_dt = #{condition.updatedDt}
            </if>
            <if test="condition.updatedBy != null">
                and updated_by = #{condition.updatedBy}
            </if>
            <if test="condition.version != null">
                and version = #{condition.version}
            </if>
            <if test="condition.nameLike != null">
                and name like concat('%', #{condition.nameLike},'%')
            </if>
        </trim>
    </sql>
    <sql id="Base_Column_List">
        id, uuid, name, description, inspect_protocols, exception_applications, exception_signatures,
        tenant_id, tenant_name, cloud_env_id, org_sid, created_dt, created_by, updated_dt,
        updated_by, version
    </sql>
    <select id="selectByParams" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from res_firewall_antivirus_policy
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <include refid="common.Mysql_Pagination_Limit"/>
    </select>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from res_firewall_antivirus_policy
        where id = #{id}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from res_firewall_antivirus_policy
        where id = #{id}
    </delete>
    <delete id="deleteByParams" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        delete from res_firewall_antivirus_policy
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResFireWallAntivirusPolicy">
        insert into res_firewall_antivirus_policy (id, uuid, name, description, inspect_protocols,
        exception_applications,
        exception_signatures, tenant_id, tenant_name, cloud_env_id, org_sid, created_dt,
        created_by, updated_dt, updated_by, version)
        values (#{id}, #{uuid}, #{name}, #{description}, #{inspectProtocols}, #{exceptionApplications},
        #{exceptionSignatures}, #{tenantId}, #{tenantName}, #{cloudEnvId}, #{orgSid}, #{createdDt},
        #{createdBy}, #{updatedDt}, #{updatedBy}, #{version})
    </insert>
    <insert id="insertSelective"
        parameterType="cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResFireWallAntivirusPolicy">
        insert into res_firewall_antivirus_policy
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="uuid != null">
                uuid,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="inspectProtocols != null">
                inspect_protocols,
            </if>
            <if test="exceptionApplications != null">
                exception_applications,
            </if>
            <if test="exceptionSignatures != null">
                exception_signatures,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="tenantName != null">
                tenant_name,
            </if>
            <if test="cloudEnvId != null">
                cloud_env_id,
            </if>
            <if test="orgSid != null">
                org_sid,
            </if>
            <if test="createdDt != null">
                created_dt,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="updatedDt != null">
                updated_dt,
            </if>
            <if test="updatedBy != null">
                updated_by,
            </if>
            <if test="version != null">
                version,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="uuid != null">
                #{uuid},
            </if>
            <if test="name != null">
                #{name},
            </if>
            <if test="description != null">
                #{description},
            </if>
            <if test="inspectProtocols != null">
                #{inspectProtocols},
            </if>
            <if test="exceptionApplications != null">
                #{exceptionApplications},
            </if>
            <if test="exceptionSignatures != null">
                #{exceptionSignatures},
            </if>
            <if test="tenantId != null">
                #{tenantId},
            </if>
            <if test="tenantName != null">
                #{tenantName},
            </if>
            <if test="cloudEnvId != null">
                #{cloudEnvId},
            </if>
            <if test="orgSid != null">
                #{orgSid},
            </if>
            <if test="createdDt != null">
                #{createdDt},
            </if>
            <if test="createdBy != null">
                #{createdBy},
            </if>
            <if test="updatedDt != null">
                #{updatedDt},
            </if>
            <if test="updatedBy != null">
                #{updatedBy},
            </if>
            <if test="version != null">
                #{version},
            </if>
        </trim>
    </insert>
    <select id="countByParams" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultType="java.lang.Integer">
        select count(*) from res_firewall_antivirus_policy
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByParamsSelective" parameterType="map">
        update res_firewall_antivirus_policy
        <set>
            <if test="record.id != null">
                id = #{record.id},
            </if>
            <if test="record.uuid != null">
                uuid = #{record.uuid},
            </if>
            <if test="record.name != null">
                name = #{record.name},
            </if>
            <if test="record.description != null">
                description = #{record.description},
            </if>
            <if test="record.inspectProtocols != null">
                inspect_protocols = #{record.inspectProtocols},
            </if>
            <if test="record.exceptionApplications != null">
                exception_applications = #{record.exceptionApplications},
            </if>
            <if test="record.exceptionSignatures != null">
                exception_signatures = #{record.exceptionSignatures},
            </if>
            <if test="record.tenantId != null">
                tenant_id = #{record.tenantId},
            </if>
            <if test="record.tenantName != null">
                tenant_name = #{record.tenantName},
            </if>
            <if test="record.cloudEnvId != null">
                cloud_env_id = #{record.cloudEnvId},
            </if>
            <if test="record.orgSid != null">
                org_sid = #{record.orgSid},
            </if>
            <if test="record.createdDt != null">
                created_dt = #{record.createdDt},
            </if>
            <if test="record.createdBy != null">
                created_by = #{record.createdBy},
            </if>
            <if test="record.updatedDt != null">
                updated_dt = #{record.updatedDt},
            </if>
            <if test="record.updatedBy != null">
                updated_by = #{record.updatedBy},
            </if>
            <if test="record.version != null">
                version = #{record.version},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByParams" parameterType="map">
        update res_firewall_antivirus_policy
        set id = #{record.id},
        uuid = #{record.uuid},
        name = #{record.name},
        description = #{record.description},
        inspect_protocols = #{record.inspectProtocols},
        exception_applications = #{record.exceptionApplications},
        exception_signatures = #{record.exceptionSignatures},
        tenant_id = #{record.tenantId},
        tenant_name = #{record.tenantName},
        cloud_env_id = #{record.cloudEnvId},
        org_sid = #{record.orgSid},
        created_dt = #{record.createdDt},
        created_by = #{record.createdBy},
        updated_dt = #{record.updatedDt},
        updated_by = #{record.updatedBy},
        version = #{record.version}
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective"
        parameterType="cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResFireWallAntivirusPolicy">
        update res_firewall_antivirus_policy
        <set>
            <if test="uuid != null">
                uuid = #{uuid},
            </if>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="description != null">
                description = #{description},
            </if>
            <if test="inspectProtocols != null">
                inspect_protocols = #{inspectProtocols},
            </if>
            <if test="exceptionApplications != null">
                exception_applications = #{exceptionApplications},
            </if>
            <if test="exceptionSignatures != null">
                exception_signatures = #{exceptionSignatures},
            </if>
            <if test="tenantId != null">
                tenant_id = #{tenantId},
            </if>
            <if test="tenantName != null">
                tenant_name = #{tenantName},
            </if>
            <if test="cloudEnvId != null">
                cloud_env_id = #{cloudEnvId},
            </if>
            <if test="orgSid != null">
                org_sid = #{orgSid},
            </if>
            <if test="createdDt != null">
                created_dt = #{createdDt},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy},
            </if>
            <if test="updatedDt != null">
                updated_dt = #{updatedDt},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy},
            </if>
            <if test="version != null">
                version = #{version},
            </if>
        </set>
        where id = #{id}
    </update>
    <update id="updateByPrimaryKey"
        parameterType="cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResFireWallAntivirusPolicy">
        update res_firewall_antivirus_policy
        set uuid = #{uuid},
        name = #{name},
        description = #{description},
        inspect_protocols = #{inspectProtocols},
        exception_applications = #{exceptionApplications},
        exception_signatures = #{exceptionSignatures},
        tenant_id = #{tenantId},
        tenant_name = #{tenantName},
        cloud_env_id = #{cloudEnvId},
        org_sid = #{orgSid},
        created_dt = #{createdDt},
        created_by = #{createdBy},
        updated_dt = #{updatedDt},
        updated_by = #{updatedBy},
        version = #{version}
        where id = #{id}
    </update>
</mapper>
