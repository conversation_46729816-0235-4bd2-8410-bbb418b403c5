/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.service.container.impl;

import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import cn.hutool.core.map.MapUtil;

import cn.com.cloudstar.rightcloud.basic.data.pojo.cluster.CloudClusterNode;
import cn.com.cloudstar.rightcloud.common.constants.BusinessMessageConstants;
import cn.com.cloudstar.rightcloud.common.constants.SysConfigConstants;
import cn.com.cloudstar.rightcloud.common.constants.res.status.ClusterNodeStatus;
import cn.com.cloudstar.rightcloud.common.constants.res.type.ClusterCategory;
import cn.com.cloudstar.rightcloud.common.constants.res.type.ClusterNodeType;
import cn.com.cloudstar.rightcloud.common.constants.res.type.KubernetesAuthType;
import cn.com.cloudstar.rightcloud.common.constants.status.SelfServiceInstanceStatus;
import cn.com.cloudstar.rightcloud.common.exception.BizException;
import cn.com.cloudstar.rightcloud.common.observable.CommonObservableData;
import cn.com.cloudstar.rightcloud.common.util.PropertiesUtil;
import cn.com.cloudstar.rightcloud.common.util.SpringContextHolder;
import cn.com.cloudstar.rightcloud.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.common.websocket.ServerMsgPublisher;
import cn.com.cloudstar.rightcloud.core.pojo.dto.deploy.ClusterData;
import cn.com.cloudstar.rightcloud.core.pojo.dto.event.ClusterDeployDataEvent;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicInfoUtil;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicWebUtil;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.cluster.CloudCluster;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.cluster.ClusterInitInfoModel;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.cluster.ClusterJoinInfoModel;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.cluster.ClusterListDTO;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.cluster.ClusterNodeModel;
import cn.com.cloudstar.rightcloud.remote.api.system.service.app.AppInstRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.system.service.maintainance.order.ServiceInstTargetRemoteService;
import cn.com.cloudstar.rightcloud.resource.service.container.CloudClusterNodeService;
import cn.com.cloudstar.rightcloud.resource.service.container.CloudClusterService;
import cn.com.cloudstar.rightcloud.resource.service.container.ClusterMainService;
import cn.com.cloudstar.rightcloud.resource.service.container.observer.K8sClusterObserver;

/**
 * Created by ShiWenQiang on 2016/10/24. cluster main service
 *
 * <AUTHOR>
 */
@Component
public class ClusterMainServiceImpl implements ClusterMainService {


    private static final String CLUSTER_INFO = "clusterInfo";
    private static final String CLUSTER_NODE_INFO = "clusterNodesInfo";
    private static final String AUTHUSER = "authUser";
    private static Logger logger = LoggerFactory.getLogger(ClusterMainServiceImpl.class);
    @Autowired
    private CloudClusterService cloudClusterService;

    @Autowired
    private CloudClusterNodeService cloudClusterNodeService;

    @DubboReference
    private AppInstRemoteService appInstRemoteService;

    @DubboReference
    private ServiceInstTargetRemoteService serviceInstTargetRemoteService;

    /**
     * Init k 8 s cluster boolean.
     *
     * @param clusterInitInfoModel the cluster init info model
     * @param authUser the auth user
     *
     * @return the boolean
     */
    @Override
    @Transactional(rollbackFor = {BizException.class, Exception.class})
    public boolean initK8sCluster(ClusterInitInfoModel clusterInitInfoModel, AuthUser authUser) {

        //k8s cluster master node must >1
        if (StringUtil.isNullOrEmpty(clusterInitInfoModel.getNodes()) || clusterInitInfoModel.getNodes().isEmpty()) {
            throw new BizException(
                    WebUtil.getMessage(BusinessMessageConstants.ClusterMessage.CLUSTER_MASTER_NODE_NOT_NULL_ERROR));
        }

        //validate master count
        if (clusterInitInfoModel.getNodes()
                                .parallelStream()
                                .filter(node -> node.getType().equals(ClusterNodeType.MASTER))
                                .count() <= 0L) {
            throw new BizException(
                    WebUtil.getMessage(BusinessMessageConstants.ClusterMessage.CLUSTER_MASTER_NODE_NOT_NULL_ERROR));

        }

        CloudCluster cloudCluster = new CloudCluster();
        cloudCluster.setName(clusterInitInfoModel.getName());
        cloudCluster.setDescription(clusterInitInfoModel.getDescription());
        cloudCluster.setType(clusterInitInfoModel.getType());
        cloudCluster.setClusterType(clusterInitInfoModel.getClusterType());
        cloudCluster.setCategory(clusterInitInfoModel.getCategory());
        cloudCluster.setKubernetesVersion("1.9");
        cloudCluster.setAuthType(KubernetesAuthType.TOKEN);
        cloudCluster.setOrgSid(BasicInfoUtil.getCurrentOrgSid());
        //set api external ip
        cloudCluster.setExternalIp(clusterInitInfoModel.getClusterExternalIp());

        String dns = PropertiesUtil.getProperty(SysConfigConstants.KUBERNETES_DEFAULT_CLUSTER_DNS);
        cloudCluster.setDns(dns);
        cloudCluster.setDomain(clusterInitInfoModel.getClusterDomain());
        //api token
        String token = StringUtil.getUUID();
        cloudCluster.setToken(token);
        List<String> apiAddress = new ArrayList<>();
        List<String> etcdAddress = new ArrayList<>();

        //cluster node list
        List<CloudClusterNode> cloudClusterNodes = new ArrayList<>();
        clusterInitInfoModel.getNodes().forEach(node -> {
            CloudClusterNode cloudClusterNode = new CloudClusterNode();
            cloudClusterNode.setType(node.getType());
            cloudClusterNode.setResVmId(node.getHostId());
            cloudClusterNode.setHostIp(node.getIp().split("\r\n")[0]);
            //host name is ips default used first
            cloudClusterNode.setHostName(node.getHostName().split("\r\n")[0]);
            cloudClusterNode.setStatus(ClusterNodeStatus.CREATING);
            cloudClusterNode.setClusterId(cloudCluster.getId());
            cloudClusterNodes.add(cloudClusterNode);
            if (node.getType().equals(ClusterNodeType.MASTER)) {
                //set cluster info
                //default every muster node deploy the same kube api and etcd address and ports
                //if exit ips default used first
                String firstIp = node.getIp().split("\r\n")[0];
                apiAddress.add("https://" + firstIp + ":6443");
                etcdAddress.add("http://" + firstIp + ":2379");
            }

        });
        cloudCluster.setApi(getExternalApiAddress(clusterInitInfoModel.getClusterExternalIp()));
        cloudCluster.setDashboardUrl(getExternalApiAddress(clusterInitInfoModel.getClusterExternalIp()) + "/ui");

        cloudCluster.setEtcd(StringUtils.join(etcdAddress, ","));
        cloudCluster.setStatus(ClusterNodeStatus.CREATING);
        cloudCluster.setOwnerId(authUser.getUserSid());
        cloudCluster.setSfServiceInstId(clusterInitInfoModel.getSfServiceInstId());
        BasicWebUtil.prepareInsertParams(cloudCluster, authUser);
        //save cluster data
        cloudClusterService.insertSelective(cloudCluster);

        //save cluster data no batch insert
        cloudClusterNodes.forEach(node -> {
            node.setClusterId(cloudCluster.getId());
            BasicWebUtil.prepareInsertParams(node, authUser);
            cloudClusterNodeService.insertSelective(node);
        });
        Map<String, Object> data = new HashMap<>();
        data.put(CLUSTER_INFO, cloudCluster);
        data.put(CLUSTER_NODE_INFO, cloudClusterNodes);
        data.put(AUTHUSER, authUser);
        ClusterData clusterData = new ClusterData(CommonObservableData.BUSINESS_TYPE.K8S_CLUSTER_INIT_OR_JOIN,
                                                  K8sClusterObserver.class.getName(), data);
        ClusterDeployDataEvent clusterDeployDataEvent = new ClusterDeployDataEvent(clusterData);

        SpringContextHolder.publishEvent(clusterDeployDataEvent);
        return true;
    }

    /**
     * Join node to k 8 s cluster boolean.
     *
     * @param clusterJoinInfoModel the cluster join info model
     * @param authUser the auth user
     *
     * @return the boolean
     */
    @Override
    @Transactional(rollbackFor = {BizException.class, Exception.class})
    public boolean joinNodeToK8sCluster(ClusterJoinInfoModel clusterJoinInfoModel, AuthUser authUser) {
        CloudCluster cloudCluster = cloudClusterService.selectByPrimaryKey(
                Long.parseLong(clusterJoinInfoModel.getClusterId()));

        if (StringUtil.isNullOrEmpty(cloudCluster)) {
            throw new BizException(WebUtil.getMessage(BusinessMessageConstants.ClusterMessage.CLUSTER_NOT_FOUND_ERROR));
        }

        List<ClusterNodeModel> clusterNodeModels = clusterJoinInfoModel.getNodes();
        if (StringUtil.isNullOrEmpty(clusterNodeModels) || clusterNodeModels.isEmpty()
                || clusterNodeModels.stream().filter(node -> node.getType().equals(ClusterNodeType.NODE)).count()
                <= 0L) {
            throw new BizException(
                    WebUtil.getMessage(BusinessMessageConstants.ClusterMessage.CLUSTER_NODE_NOT_FOUND_ERROR));
        }

        List<CloudClusterNode> cloudClusterNodes = new ArrayList<>();
        clusterNodeModels.stream().filter(node -> node.getType().equals(ClusterNodeType.NODE)).forEach(node -> {
            CloudClusterNode cloudClusterNode = new CloudClusterNode();
            cloudClusterNode.setType(node.getType());
            cloudClusterNode.setResVmId(node.getHostId());
            cloudClusterNode.setHostIp(node.getIp());
            cloudClusterNode.setHostName(node.getHostName().split(",")[0]);
            cloudClusterNode.setStatus(ClusterNodeStatus.CREATING);
            cloudClusterNode.setClusterId(cloudCluster.getId());
            cloudClusterNode.setClusterId(cloudCluster.getId());
            BasicWebUtil.prepareInsertParams(cloudClusterNode, authUser);
            cloudClusterNodeService.insertSelective(cloudClusterNode);
            cloudClusterNodes.add(cloudClusterNode);
        });

        Map<String, Object> data = new HashMap<>();
        data.put(CLUSTER_INFO, cloudCluster);
        data.put(CLUSTER_NODE_INFO, cloudClusterNodes);
        data.put(AUTHUSER, authUser);
        ClusterData clusterData = new ClusterData(CommonObservableData.BUSINESS_TYPE.K8S_CLUSTER_INIT_OR_JOIN,
                                                  K8sClusterObserver.class.getName(), data);
        ClusterDeployDataEvent clusterDeployDataEvent = new ClusterDeployDataEvent(clusterData);
        SpringContextHolder.publishEvent(clusterDeployDataEvent);
        return true;
    }

    /**
     * Delete cluster boolean.
     *
     * @param clusterId the cluster id
     * @param authUser the auth user
     *
     * @return the boolean
     */
    @Override
    @Transactional(rollbackFor = {BizException.class, Exception.class})
    public boolean deleteCluster(Long clusterId, AuthUser authUser) {
        CloudCluster cloudCluster = this.cloudClusterService.selectByPrimaryKey(clusterId);
        //delete cluster all apps
        this.appInstRemoteService.deleteAllAppsByClusterId(clusterId, authUser);
        //db delete
        this.cloudClusterService.deleteByPrimaryKey(clusterId);
        Criteria criteria = new Criteria();
        criteria.put("clusterId", clusterId);
        List<CloudClusterNode> cloudClusterNodes = this.cloudClusterNodeService.selectByParams(criteria);
        List<Long> sids = new ArrayList<>();
        cloudClusterNodes.forEach(node -> sids.add(node.getId()));
        List<CloudClusterNode> assembleNodes = new ArrayList<>();
        if (!StringUtil.isNullOrEmpty(sids) && !sids.isEmpty()) {
            assembleNodes = this.cloudClusterNodeService.selectBySids(sids);
            cloudClusterNodes.forEach(node -> cloudClusterNodeService.deleteByPrimaryKey(node.getId()));
        }
        serviceInstTargetRemoteService.updateSelfInstByClusterId(clusterId, SelfServiceInstanceStatus.DELETED);

        if (StringUtil.equals(ClusterCategory.CREATED, cloudCluster.getCategory())) {
            Map<String, Object> data = new HashMap<>();
            data.put(CLUSTER_INFO, cloudCluster);
            data.put(CLUSTER_NODE_INFO, assembleNodes);
            ClusterData clusterData = new ClusterData(CommonObservableData.BUSINESS_TYPE.K8S_CLUSTER_DELETE,
                                                      K8sClusterObserver.class.getName(), data);
            ClusterDeployDataEvent clusterDeployDataEvent = new ClusterDeployDataEvent(clusterData);
            SpringContextHolder.publishEvent(clusterDeployDataEvent);
        }

        // 更新用户Id推送状态，客户端重新拉取任务状态
        ServerMsgPublisher.sendMsg("/topic/task/" + authUser.getUserSid(), "delete");
        return true;
    }

    /**
     * Delete cluster node boolean.
     *
     * @param clusterNodeId the cluster node id
     * @param authUser the auth user
     *
     * @return the boolean
     */
    @Override
    @Transactional(rollbackFor = {BizException.class, Exception.class})
    public boolean deleteClusterNode(Long clusterNodeId, AuthUser authUser) {
        //db delete
        CloudClusterNode cloudClusterNode = this.cloudClusterNodeService.selectByPrimaryKey(clusterNodeId);
        CloudCluster cloudCluster = this.cloudClusterService.selectByPrimaryKey(cloudClusterNode.getClusterId());
        List<Long> sids = new ArrayList<>();
        sids.add(cloudClusterNode.getId());
        List<CloudClusterNode> ccns = this.cloudClusterNodeService.selectBySids(sids);
        cloudClusterNodeService.deleteByPrimaryKey(cloudClusterNode.getId());

        Map<String, Object> data = new HashMap<>();
        data.put(CLUSTER_INFO, cloudCluster);
        data.put("cloudClusterNode", ccns.get(0));
        ClusterData clusterData = new ClusterData(CommonObservableData.BUSINESS_TYPE.K8S_CLUSTER_DELETE_NODE,
                                                  K8sClusterObserver.class.getName(), data);
        ClusterDeployDataEvent clusterDeployDataEvent = new ClusterDeployDataEvent(clusterData);
        SpringContextHolder.publishEvent(clusterDeployDataEvent);
        return true;
    }

    /**
     * Display clusters list.
     *
     * @param criteria the criteria
     * @param authUser the auth user
     * @param refresh the refresh
     *
     * @return the list
     */
    @Override
    @Transactional(rollbackFor = {BizException.class, Exception.class})
    public List<ClusterListDTO> displayClusters(Criteria criteria, AuthUser authUser, Boolean refresh) {
        if (refresh) {
            Map<String, Object> data = new HashMap<>();
            data.put("authUser", authUser);
            ClusterData clusterData = new ClusterData(CommonObservableData.BUSINESS_TYPE.K8S_CLUSTER_INFO_UPDATE,
                                                      K8sClusterObserver.class.getName(), data);


            ClusterDeployDataEvent clusterDeployDataEvent = new ClusterDeployDataEvent(clusterData);
            SpringContextHolder.publishEvent(clusterDeployDataEvent);
        }
        return cloudClusterService.displayClusters(criteria);
    }

    @Override
    @Transactional(rollbackFor = {BizException.class, Exception.class})
    public boolean importCluster(CloudCluster cloudCluster, String account) {
        List<CloudClusterNode> cloudClusterNodes = new ArrayList<>();
        cloudClusterNodeService.insertBatch(cloudClusterNodes);
        return true;
    }

    @Override
    public boolean connectCluster(String authType, String token, String api) {
        return true;
    }


    /**
     * Compares two version strings.
     *
     * Use this instead of String.compareTo() for a non-lexicographical comparison that works for version strings. e.g.
     * "1.10".compareTo("1.6").
     *
     * @param v1 a string of alpha numerals separated by decimal points.
     * @param v2 a string of alpha numerals separated by decimal points.
     *
     * @return The result is 1 if v1 is greater than v2. The result is 2 if v2 is greater than v1. The result is -1 if
     *         the version format is unrecognized. The result is zero if the strings are equal.
     */
    public static int versionCompare(String v1, String v2) {
        int v1Len = StringUtils.countMatches(v1, ".");
        int v2Len = StringUtils.countMatches(v2, ".");

        if (v1Len != v2Len) {
            int count = Math.abs(v1Len - v2Len);
            if (v1Len > v2Len) {
                for (int i = 1; i <= count; i++) {
                    v2 += ".0";
                }
            } else {
                for (int i = 1; i <= count; i++) {
                    v1 += ".0";
                }
            }
        }

        if (v1.equals(v2)) {
            return 0;
        }

        String[] v1Str = StringUtils.split(v1, ".");
        String[] v2Str = StringUtils.split(v2, ".");
        for (int i = 0; i < v1Str.length; i++) {
            String str1 = "", str2 = "";
            for (char c : v1Str[i].toCharArray()) {
                if (Character.isLetter(c)) {
                    int u = c - 'a' + 1;
                    if (u < 10) {
                        str1 += String.valueOf("0" + u);
                    } else {
                        str1 += String.valueOf(u);
                    }
                } else {
                    str1 += String.valueOf(c);
                }
            }
            for (char c : v2Str[i].toCharArray()) {
                if (Character.isLetter(c)) {
                    int u = c - 'a' + 1;
                    if (u < 10) {
                        str2 += String.valueOf("0" + u);
                    } else {
                        str2 += String.valueOf(u);
                    }
                } else {
                    str2 += String.valueOf(c);
                }
            }
            v1Str[i] = "1" + str1;
            v2Str[i] = "1" + str2;

            int num1 = Integer.parseInt(v1Str[i]);
            int num2 = Integer.parseInt(v2Str[i]);

            if (num1 != num2) {
                if (num1 > num2) {
                    return 1;
                } else {
                    return 2;
                }
            }
        }
        return -1;
    }

    @Override
    public Map getClusterResource(Long clusterId, String resourceType, String namespace) {
        return MapUtil.newHashMap();
    }


    public String getExternalApiAddress(String externalIps) {
        List<String> apis = new ArrayList<>();
        if (null != externalIps && externalIps != "") {
            for (String ip : externalIps.split(",")) {
                apis.add("https://" + ip + ":6443");
            }
        }
        return StringUtils.join(apis, ",");
    }
}
