/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.listener;

import com.alibaba.fastjson.JSON;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

import cn.hutool.core.convert.Convert;

import cn.com.cloudstar.rightcloud.common.constants.res.type.ServiceInstResDeleteType;
import cn.com.cloudstar.rightcloud.common.enums.ExpireStrategyEnum;
import cn.com.cloudstar.rightcloud.common.enums.NoticeResTypeEnum;
import cn.com.cloudstar.rightcloud.common.schedule.bean.MessageDTO;
import cn.com.cloudstar.rightcloud.resource.service.common.ResourceService;
import cn.com.cloudstar.rightcloud.resource.service.network.ResFloatingIpService;
import cn.com.cloudstar.rightcloud.resource.service.server.ResVmService;

@Component
public class ResourceExpireHandleListener {

    private final Logger logger = LoggerFactory.getLogger(ResourceExpireHandleListener.class);

    @Autowired
    private ResVmService resVmService;

    @Autowired
    private ResFloatingIpService resFloatingIpService;

    @Autowired
    private ResourceService resourceService;


    public void handleMessage(MessageDTO messageDTO) {
        logger.info("接收到schedule资源到期处理消息：[{}]", JSON.toJSONString(messageDTO));
        try {
            Map dataMap = (Map) messageDTO.getData();
            String resourceId = (String) dataMap.get("resourceId");
            ExpireStrategyEnum expireStrategyEnum = ExpireStrategyEnum.toEnum((String) dataMap.get("action"));
            NoticeResTypeEnum type = NoticeResTypeEnum.valueOf(messageDTO.getType());

            switch (expireStrategyEnum) {
                case DELETE:
                    deleteResource(resourceId, type);
                    break;
                case RECYCLE:
                    recycleResource(resourceId, type);
                    break;
            }
        } catch (Exception e) {
            logger.error("资源到期处理失败!", e);
        }
    }

    private void recycleResource(String resourceId, NoticeResTypeEnum type) {
        switch (type) {
            case VM:
                resVmService.stopResVm(resourceId, false);
                break;
        }
    }

    private void deleteResource(String resourceId, NoticeResTypeEnum type) {
        switch (type) {
            case VM:
                resVmService.removeCloudInstance(resourceId, true, false);
                break;
            case DISK:
                resourceService.deleteVolume(resourceId, true, false, true);
                break;
            case FLOATINGIP:
                resFloatingIpService.deleteFloatingIp(Convert.toLong(resourceId), true);
                break;
        }
    }

}
