/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.service.hpc.impl;


import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnv;
import cn.com.cloudstar.rightcloud.common.ccsp.CCSPCacheUtil;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.constants.res.status.ResHpcClusterStatus;
import cn.com.cloudstar.rightcloud.common.exception.BizException;
import cn.com.cloudstar.rightcloud.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.common.pojo.RestResult.Status;
import cn.com.cloudstar.rightcloud.common.redis.JedisUtil;
import cn.com.cloudstar.rightcloud.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.common.util.CrytoUtilSimple;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.Ccpdbinfo;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.CreateOfflineDto;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.LdapInfo;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.ManagerPointInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResHpcClusterExample;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResHpcClusterPool;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.UpdateLdapInfoDto;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.UpdateOfflineDto;
import cn.com.cloudstar.rightcloud.core.pojo.vo.res.ResHpcClusterPoolResponse;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicInfoUtil;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicWebUtil;
import cn.com.cloudstar.rightcloud.module.support.access.util.JsonUtil;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResShareType;
import cn.com.cloudstar.rightcloud.resource.dao.env.CloudEnvMapper;
import cn.com.cloudstar.rightcloud.resource.dao.hpc.ResHpcClusterMapper;
import cn.com.cloudstar.rightcloud.resource.dao.hpc.ResHpcClusterPoolMapper;
import cn.com.cloudstar.rightcloud.resource.dao.server.ResShareTypeMapper;
import cn.com.cloudstar.rightcloud.resource.service.hpc.IResHpcClusterPoolService;

/**
 * <AUTHOR>
 * @date 2021/12/5 19:37
 */
@Service
@Slf4j
public class ResHpcCusterPoolServiceImpl implements IResHpcClusterPoolService {

    private static final String ACTIVE = "ACTIVE";
    private static final String HCSO = "HCSO";
    private static final String HPC_OFFLINE = "HPCOffline";
    private static final String ECS = "ECS";
    private static final String CCS_CLI = "CCS_CLI";
    private static final String CCP_DB = "CCP_DB";
    private static final String CCP_MASTER = "CCP_MASTER";
    private static final String ENABLE = "enable";
    private static final String ENCIPHER = "********";
    private static final int FOUR = 4;
    private static final int TWO = 2;

    @Autowired
    private ResHpcClusterPoolMapper resHpcClusterPoolMapper;
    @Autowired
    private ResShareTypeMapper resShareTypeMapper;

    @Autowired
    private CloudEnvMapper cloudEnvMapper;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private ResHpcClusterMapper resHpcClusterMapper;

    @Override
    public List<ResHpcClusterPool> selectByParam(Criteria criteria) {
        return resHpcClusterPoolMapper.selectByParam(criteria);
    }

    @Override
    public int update(ResHpcClusterPool updateResHpcClusterRequest) {
        ResHpcClusterPool resHpcClusterPool = resHpcClusterPoolMapper.selectByPrimaryKey(
                updateResHpcClusterRequest.getId());
        if (updateResHpcClusterRequest.getShareTypeId() != null) {
            ResShareType resShareType = resShareTypeMapper.selectByPrimaryKey(
                    Integer.valueOf(updateResHpcClusterRequest.getShareTypeId()));
            resHpcClusterPool.setShareTypeName(resShareType.getName());
        } else {
            resHpcClusterPool.setShareTypeName(null);
        }
        //可能为null
        resHpcClusterPool.setShareTypeId(updateResHpcClusterRequest.getShareTypeId());
        //全量更新
        return resHpcClusterPoolMapper.updateByPrimaryKey(resHpcClusterPool);
    }

    @Override
    public List<ResHpcClusterPool> selectByParamForConsole(Criteria criteria) {
        return resHpcClusterPoolMapper.selectByParamForConsole(criteria);
    }

    @Override
    public ResHpcClusterPool getResHpcClusterPoolById(Long clusterId) {
        return resHpcClusterPoolMapper.selectByPrimaryKey(clusterId);
    }

    @Override
    public void updateHpcClusterPoolById(ResHpcClusterPool hpcClusterPool) {
        resHpcClusterPoolMapper.updateByPrimaryKeySelective(hpcClusterPool);
    }

    @Override
    public List<ResHpcClusterPool> selectByParams(Criteria criteria) {
        return resHpcClusterPoolMapper.selectByParams(criteria);
    }

    @Override
    public List<ResHpcClusterPool> selectShareList() {
        return resHpcClusterPoolMapper.selectShareList();
    }


    @Override
    public Boolean updateSharePool(ResHpcClusterPool resHpcClusterPool) {
        List<ResHpcClusterPool> resHpcClusterPools = resHpcClusterPoolMapper.selectShareList();
        for (ResHpcClusterPool pool : resHpcClusterPools) {
            if (Objects.isNull(pool.getUsername()) || "".equals(pool.getUsername()) ||
                    Objects.isNull(pool.getPassword()) || "".equals(pool.getPassword())) {
                pool.setUsername(resHpcClusterPool.getUsername());
                pool.setPassword(resHpcClusterPool.getPassword());
                resHpcClusterPoolMapper.updateByPrimaryKey(pool);
            }
        }
        return true;
    }


    @Override
    public void checkConnectionStatus(List<ResHpcClusterPoolResponse> request) {
        List<ResHpcClusterPool> lists = new LinkedList<>();
        for (ResHpcClusterPoolResponse response : request) {
            ResHpcClusterPool resHpcClusterPool = resHpcClusterPoolMapper.selectByPrimaryKey(
                    response.getId());
            if (Objects.nonNull(resHpcClusterPool)) {
                resHpcClusterPool.setCcPortalConnectionStatus(response.getStatus());
                lists.add(resHpcClusterPool);
            }
        }
        //全量更新
        if (lists.size() > 0) {
            resHpcClusterPoolMapper.updateResHpcClusterPoolStatus(lists);

        }
    }


    @Override
    public List<ResHpcClusterPool> selectByPreParam(Criteria criteria) {
        return resHpcClusterPoolMapper.selectByPreParam(criteria);
    }

    @Override
    public void updateShareStatus(ResHpcClusterPool updateResHpcClusterRequest) {
        ResHpcClusterPool resHpcClusterPool = resHpcClusterPoolMapper.selectByPrimaryKey(
                updateResHpcClusterRequest.getId());
        resHpcClusterPool.setShareStatus(updateResHpcClusterRequest.getShareStatus());
        resHpcClusterPool.setLdapStatus(updateResHpcClusterRequest.getLdapStatus());
        //全量更新
        resHpcClusterPoolMapper.updateByPrimaryKey(resHpcClusterPool);
    }

    @Override
    public int updatePassWord(ResHpcClusterPool updateResHpcClusterRequest) {
        ResHpcClusterPool resHpcClusterPool = resHpcClusterPoolMapper.selectByPrimaryKey(
                updateResHpcClusterRequest.getId());
        resHpcClusterPool.setPassword(updateResHpcClusterRequest.getPassword());
        resHpcClusterPool.setUsername(updateResHpcClusterRequest.getUsername());
        resHpcClusterPool.setManagerPointInfo(updateResHpcClusterRequest.getManagerPointInfo());
        resHpcClusterPool.setShareStatus(ACTIVE);
        //存入redis
        String clusterId = resHpcClusterPool.getClusterId();
        try {
            JedisUtil.instance().del(clusterId);
            //全量更新
            return resHpcClusterPoolMapper.updateByPrimaryKey(resHpcClusterPool);
        } catch (Exception e) {
            log.info(e.getMessage());
            e.printStackTrace();
            return resHpcClusterPoolMapper.updateByPrimaryKey(resHpcClusterPool);
        }
    }

    @Override
    public ResHpcClusterPool getByClusterId(String clusterId) {
        return resHpcClusterPoolMapper.getByClusterId(clusterId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RestResult createOffline(CreateOfflineDto createOffline) {
        List<ResHpcClusterPool> businessCategory = resHpcClusterPoolMapper.selectByParam(
                new Criteria("businessCategory", createOffline.getBusinessCategory()));
        if (!businessCategory.isEmpty()) {
            throw new BizException(WebUtil.getMessage(MsgCd.DELETE_HPC_ONLINE_THREE));
        }
        ResHpcClusterPool resHpcClusterPool = BeanConvertUtil.convert(createOffline, ResHpcClusterPool.class);
        // 集群基本信息
        resHpcClusterPool.setStatus(ACTIVE);
        resHpcClusterPool.setShareStatus(ACTIVE);
        resHpcClusterPool.setCcPortalConnectionStatus(ACTIVE);
        resHpcClusterPool.setLdapStatus(ACTIVE);
        Criteria criteria = new Criteria("cloudEnvTypes", Stream.of(HCSO, HPC_OFFLINE).collect(Collectors.toList()));
        criteria.setOrderByClause("created_by");
        List<CloudEnv> cloudEnvs = cloudEnvMapper.selectByParams(criteria);
        if (cloudEnvs.size() > 0) {
            resHpcClusterPool.setCloudEnvId(cloudEnvs.get(0).getId());
        }
        resHpcClusterPool.setHpcVersion(FOUR);
        resHpcClusterPool.setCcpVersion(TWO);
        List<ManagerPointInfo> managerPointInfos = new ArrayList<>();
        // 集群基本信息
        setOfflineBaseInfo(resHpcClusterPool, createOffline, managerPointInfos);
        // ldap
        if (Objects.nonNull(createOffline.getLdapServerName())) {
            LdapInfo ldapInfo = BeanConvertUtil.convert(createOffline, LdapInfo.class);
            ldapInfo.setLdapPassword(CrytoUtilSimple.encrypt(ldapInfo.getLdapPassword()));
            resHpcClusterPool.setLdapInfo(JsonUtil.toJson(ldapInfo));
        }
        // dp配置
        ManagerPointInfo masterPointInfo = new ManagerPointInfo();
        masterPointInfo.setResourceType(ECS);
        masterPointInfo.setHPCNodeType(CCP_MASTER);
        masterPointInfo.setEIP(createOffline.getDpEip());
        masterPointInfo.setEIpPort(createOffline.getDpEipPort());
        masterPointInfo.setFIP(createOffline.getDpEip());
        masterPointInfo.setFIpPort(createOffline.getDpEipPort());
        masterPointInfo.setVIP(createOffline.getDpVip());
        masterPointInfo.setVIpPort(createOffline.getDpEipPort());
        managerPointInfos.add(masterPointInfo);
        // db信息
        List<Ccpdbinfo> ccpdbinfos = new ArrayList<>();
        String[] dbAddresss = createOffline.getDbAddress().split(StrUtil.COMMA);
        for (String dbAddress : dbAddresss) {
            ManagerPointInfo dbPointInfo = new ManagerPointInfo();
            dbPointInfo.setResourceType(ECS);
            dbPointInfo.setHPCNodeType(CCP_DB);
            dbPointInfo.setFIP(dbAddress);
            dbPointInfo.setEIP(dbAddress);
            managerPointInfos.add(dbPointInfo);
        }
        resHpcClusterPool.setManagerPointInfo(JsonUtil.toJson(managerPointInfos));
        BasicWebUtil.prepareInsertParams(resHpcClusterPool);
        // password
        String password = CrytoUtilSimple.encrypt(resHpcClusterPool.getPassword());
        if (CCSPCacheUtil.ccspServiceOpen()) {
            password = CCSPCacheUtil.verifyAndCCSPEncrypt(resHpcClusterPool.getPassword());
        }
        resHpcClusterPool.setPassword(password);
        resHpcClusterPoolMapper.insertSelective(resHpcClusterPool);

        AuthUser authUser = BasicInfoUtil.getCurrentUserInfo();
        for (String dbAddress : dbAddresss) {
            Ccpdbinfo ccpdbinfo = BeanConvertUtil.convert(createOffline, Ccpdbinfo.class);
            ccpdbinfo.setDbAddress(dbAddress);
            ccpdbinfo.setBesunessCategory(resHpcClusterPool.getBusinessCategory());
            ccpdbinfo.setClusterType(resHpcClusterPool.getClusterType());
            ccpdbinfo.setNum(resHpcClusterPool.getId());
            ccpdbinfo.setStatus(ENABLE);
            if (Objects.nonNull(authUser)) {
                ccpdbinfo.setEntityId(authUser.getEntityId());
            }
            ccpdbinfo.setDbPort(ccpdbinfo.getDbPort());
            ccpdbinfo.setDbName(ccpdbinfo.getDbName());
            ccpdbinfo.setDbUser(ccpdbinfo.getDbUser());
            String dbPassword = CrytoUtilSimple.encrypt(ccpdbinfo.getDbPassword());
            if (CCSPCacheUtil.ccspServiceOpen()) {
                dbPassword = CCSPCacheUtil.verifyAndCCSPEncrypt(ccpdbinfo.getDbPassword());
            }
            ccpdbinfo.setDbPassword(dbPassword);
            ccpdbinfos.add(ccpdbinfo);
        }
        mongoTemplate.insert(ccpdbinfos, Ccpdbinfo.class);

        return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_INSERT_SUCCESS),
                              String.valueOf(resHpcClusterPool.getId()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RestResult updateOffline(UpdateOfflineDto updateOffline) {
        ResHpcClusterPool clusterPool = resHpcClusterPoolMapper.selectByPrimaryKey(updateOffline.getId());
        if (Objects.isNull(clusterPool)) {
            throw new BizException(WebUtil.getMessage(MsgCd.WARNING_QUERY_FAILURE));
        }
        Criteria criteria = new Criteria("businessCategory", updateOffline.getBusinessCategory());
        criteria.put("idNotEq", updateOffline.getId());
        List<ResHpcClusterPool> businessCategory = resHpcClusterPoolMapper.selectByParam(criteria);
        if (!businessCategory.isEmpty()) {
            throw new BizException(WebUtil.getMessage(MsgCd.DELETE_HPC_ONLINE_THREE));
        }
        ResHpcClusterPool resHpcClusterPool = BeanConvertUtil.convert(updateOffline, ResHpcClusterPool.class);
        // 集群基本信息
        List<cn.com.cloudstar.rightcloud.core.pojo.dto.res.ManagerPointInfo> oldManagerPointInfos = JSONObject
                .parseArray((String) clusterPool.getManagerPointInfo(),
                            cn.com.cloudstar.rightcloud.core.pojo.dto.res.ManagerPointInfo.class);
        List<ManagerPointInfo> managerPointInfos = oldManagerPointInfos.stream()
                                                                       .filter(t -> !t.getHPCNodeType().equals(CCS_CLI))
                                                                       .collect(Collectors.toList());
        setOfflineBaseInfo(resHpcClusterPool, BeanConvertUtil.convert(updateOffline, CreateOfflineDto.class),
                           managerPointInfos);
        resHpcClusterPool.setManagerPointInfo(JsonUtil.toJson(managerPointInfos));
        BasicWebUtil.prepareUpdateParams(resHpcClusterPool);
        resHpcClusterPoolMapper.updateByPrimaryKeySelective(resHpcClusterPool);
        return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_UPDATE_SUCCESS));
    }

    @Override
    public boolean checkUsed(String clusterId) {
        ResHpcClusterExample hpcClusterExample = new ResHpcClusterExample();
        hpcClusterExample.createCriteria().andPoolUuidEqualTo(clusterId).andStatusNotIn(Arrays.asList(ResHpcClusterStatus.UNSUBSCRIBED,ResHpcClusterStatus.DELETED,ResHpcClusterStatus.REJECTED));
        long count = resHpcClusterMapper.countByExample(hpcClusterExample);
        if (count > 0) {
            return true;
        }
        return false;
    }

    @Override
    public void clearOwnerByCusterId(String clusterUuid) {
        resHpcClusterPoolMapper.clearOwnerByCusterId(clusterUuid);
    }

    /**
     * 处理集群基本信息
     */
    private void setOfflineBaseInfo(ResHpcClusterPool resHpcClusterPool, CreateOfflineDto offline, List<ManagerPointInfo> managerPointInfos) {
        // tag用于出账cloudName
        resHpcClusterPool.setTag(offline.getClusterName());
        String[] cliEips = offline.getCliEip().split(StrUtil.COMMA);
        String[] cliVips = offline.getCliVip().split(StrUtil.COMMA);
        int index = 0;
        for (String cliEip : cliEips) {
            ManagerPointInfo managerPointInfo = new ManagerPointInfo();
            managerPointInfo.setResourceType(ECS);
            managerPointInfo.setHPCNodeType(CCS_CLI);
            managerPointInfo.setEIP(cliEip);
            managerPointInfo.setEIpPort(offline.getCliEipPort());
            managerPointInfo.setFIP(cliEip);
            managerPointInfo.setFIpPort(offline.getCliEipPort());
            if (cliVips.length > index) {
                String cliVip = cliVips[index];
                if (StringUtils.isNotEmpty(cliVip)) {
                    managerPointInfo.setVIP(cliVip);
                    managerPointInfo.setVIpPort(offline.getCliVipPort());
                }
            }
            managerPointInfos.add(managerPointInfo);
            index++;
        }
        resHpcClusterPool.setManagerPointInfo(JsonUtil.toJson(managerPointInfos));
    }

    @Override
    public RestResult deleteOffline(Long id) {
        ResHpcClusterPool byClusterId = resHpcClusterPoolMapper.selectByPrimaryKey(id);
        if (Objects.isNull(byClusterId)) {
            throw new BizException(WebUtil.getMessage(MsgCd.WARNING_QUERY_FAILURE));
        }
        if (FOUR != byClusterId.getHpcVersion()) {
            throw new BizException(WebUtil.getMessage(MsgCd.DELETE_HPC_ONLINE_ONE));
        }
        Criteria criteria = new Criteria();
        criteria.put("statusNoEq", ResHpcClusterStatus.UNSUBSCRIBED);
        criteria.put("poolUuid", byClusterId.getClusterId());
        if (CollectionUtils.isNotEmpty(resHpcClusterMapper.selectByParams(criteria))) {
            throw new BizException(WebUtil.getMessage(MsgCd.DELETE_HPC_ONLINE_TWO));
        }
        resHpcClusterPoolMapper.deleteByPrimaryKey(id);
        return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_DELETE_SUCCESS), byClusterId.getClusterId());
    }

    @Override
    public RestResult updateLdap(UpdateLdapInfoDto updateLdapInfo) {
        ResHpcClusterPool resHpcClusterPool = resHpcClusterPoolMapper.selectByPrimaryKey(updateLdapInfo.getId());
        if (Objects.isNull(resHpcClusterPool)) {
            throw new BizException(WebUtil.getMessage(MsgCd.WARNING_QUERY_FAILURE));
        }
        if (Objects.isNull(updateLdapInfo.getLdapDomainName())) {
            resHpcClusterPool.setLdapInfo(null);
        } else {
            LdapInfo ldapInfo = BeanConvertUtil.convert(updateLdapInfo, LdapInfo.class);
            resHpcClusterPool.setLdapInfo(JsonUtil.toJson(ldapInfo));
        }
        resHpcClusterPool.setLdapStatus(ACTIVE);
        resHpcClusterPoolMapper.updateByPrimaryKey(resHpcClusterPool);
        updateLdapInfo.setLdapPassword(ENCIPHER);
        return new RestResult(Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_UPDATE_SUCCESS));
    }
}
