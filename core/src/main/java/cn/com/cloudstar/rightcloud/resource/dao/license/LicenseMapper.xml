<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.cloudstar.rightcloud.resource.dao.license.LicenseMapper">

    <resultMap id="ModuleResultMap" type="cn.com.cloudstar.rightcloud.core.pojo.models.module.Module">
        <id column="module_sid" property="moduleSid" jdbcType="VARCHAR"/>
        <result column="module_name" property="moduleName" jdbcType="VARCHAR"/>
        <result column="module_url" property="moduleUrl" jdbcType="VARCHAR"/>
        <result column="module_icon_url" property="moduleIconUrl" jdbcType="VARCHAR"/>
        <result column="parent_sid" property="parentSid" jdbcType="VARCHAR"/>
        <result column="permission" property="permission" jdbcType="VARCHAR"/>
        <result column="module_type" property="moduleType" jdbcType="INTEGER"/>
        <result column="display_flag" property="displayFlag" jdbcType="INTEGER"/>
        <result column="sort_rank" property="sortRank" jdbcType="INTEGER"/>
        <result column="module_category" property="moduleCategory" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
    </resultMap>
    <delete id="delete">
    delete from license
  </delete>

    <insert id="insert" parameterType="String">
    insert into license (LICENSE_SERIALNO)
    values (#{record,jdbcType=VARCHAR})
  </insert>

    <select id="queryForList" resultType="String">
    select LICENSE_SERIALNO from license
  </select>

    <select id="count" resultType="Integer">
        select count(*) from license
    </select>

    <update id="update" parameterType="String">
        update license set LICENSE_SERIALNO = #{record,jdbcType=VARCHAR}
    </update>

    <select id="querySupportModule" resultMap="ModuleResultMap">
        SELECT
            module_sid,
            module_name,
            module_url,
            module_icon_url,
            parent_sid,
            permission,
            module_type,
            display_flag,
            sort_rank,
            module_category,
            description,
            CASE
                    module_sid
                    WHEN 'M' THEN -4
                    WHEN 'A' THEN -3
                    WHEN 'S' THEN -2
                    WHEN 'z' THEN -1
                    ELSE sort_rank
                END sort_symble
        FROM
            sys_m_module
        WHERE
            module_type IN ( '0', '1' )
            AND display_flag = 1
        ORDER BY sort_symble
    </select>

</mapper>
