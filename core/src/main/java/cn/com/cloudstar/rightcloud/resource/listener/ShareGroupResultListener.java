/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.listener;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.ShareGroupCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.ShareGroupDeleteResult;
import cn.com.cloudstar.rightcloud.common.constants.res.status.ShareGroupStatus;
import cn.com.cloudstar.rightcloud.common.constants.res.status.ShareStatus;
import cn.com.cloudstar.rightcloud.common.enums.resource.ResourceOperateEnum;
import cn.com.cloudstar.rightcloud.common.enums.resource.ResourceTypeEnum;
import cn.com.cloudstar.rightcloud.common.util.json.JsonUtil;
import cn.com.cloudstar.rightcloud.common.websocket.annonation.Message;
import cn.com.cloudstar.rightcloud.common.websocket.annonation.MessageParam;
import cn.com.cloudstar.rightcloud.common.websocket.support.OperateEnum;
import cn.com.cloudstar.rightcloud.common.websocket.support.ServerMsgType;
import cn.com.cloudstar.rightcloud.core.annotation.log.LogMethod;
import cn.com.cloudstar.rightcloud.core.annotation.log.LogParam;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResShareRightsGroup;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicWebUtil;
import cn.com.cloudstar.rightcloud.resource.dao.network.ResVpcMapper;
import cn.com.cloudstar.rightcloud.resource.dao.share.ResShareRightsGroupMapper;
import cn.com.cloudstar.rightcloud.resource.dao.share.ResShareRuleMapper;
import cn.com.cloudstar.rightcloud.resource.dao.share.ResShareTargetMapper;

@Component
public class ShareGroupResultListener {

    private final Logger logger = LoggerFactory.getLogger(ShareGroupResultListener.class);

    @Autowired
    private ResShareRuleMapper resShareRuleMapper;

    @Autowired
    private ResShareTargetMapper resShareTargetMapper;

    @Autowired
    private ResVpcMapper resVpcMapper;

    @Autowired
    private ResShareRightsGroupMapper resShareRightsGroupMapper;

    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#shareGroupDeleteResult.id", resourceType = ResourceTypeEnum.SHARE_GROUP, opUser = "#shareGroupDeleteResult.opUser", operate = ResourceOperateEnum.DELETE, success = "#shareGroupDeleteResult.success", orgSid = "#shareGroupDeleteResult.orgSid")
    @Message(refKey = "#shareGroupDeleteResult.id", envId = "#shareGroupDeleteResult.cloudEnvId", msgType = ServerMsgType.SHARE_GROUP, opUser = "#shareGroupDeleteResult.opUser", operate = OperateEnum.DELETE, success = "#shareGroupDeleteResult.success", refNameKey = "#shareGroupDeleteResult.name", errorMsg = "#shareGroupDeleteResult.errMsg")
    public void handleMessage(
            @LogParam("shareGroupDeleteResult") @MessageParam("shareGroupDeleteResult") ShareGroupDeleteResult shareGroupDeleteResult) {
        logger.info("删除弹性文件权限组回调 | 回调参数 ： {}", JsonUtil.toJson(shareGroupDeleteResult));
        try {
            ResShareRightsGroup resShareRightsGroup = resShareRightsGroupMapper.selectByPrimaryKey(
                    shareGroupDeleteResult.getId());
            if (shareGroupDeleteResult.isSuccess()) {
                resShareRightsGroupMapper.deleteByPrimaryKey(shareGroupDeleteResult.getId());
            } else {
                resShareRightsGroup.setStatus(ShareStatus.AVAILABLE);
                resShareRightsGroupMapper.updateByPrimaryKey(resShareRightsGroup);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#shareGroupCreateResult.id", resourceType = ResourceTypeEnum.SHARE_GROUP, opUser = "#shareGroupCreateResult.opUser", operate = ResourceOperateEnum.CREATE, success = "#shareGroupCreateResult.success", orgSid = "#shareGroupCreateResult.orgSid")
    @Message(refKey = "#shareGroupCreateResult.id", envId = "#shareGroupCreateResult.cloudEnvId", msgType = ServerMsgType.SHARE_GROUP, opUser = "#shareGroupCreateResult.opUser", operate = OperateEnum.CREATE, success = "#shareGroupCreateResult.success", refNameKey = "#shareGroupCreateResult.groupName", errorMsg = "#shareGroupCreateResult.errMsg")
    public void handleMessage(
            @LogParam("shareGroupCreateResult") @MessageParam("shareGroupCreateResult") ShareGroupCreateResult shareGroupCreateResult) {
        logger.info("创建弹性文件权限组回调 | 回调参数 ： {}", JsonUtil.toJson(shareGroupCreateResult));
        try {
            ResShareRightsGroup resShareRightsGroup = resShareRightsGroupMapper.selectByPrimaryKey(
                    shareGroupCreateResult.getId());
            if (shareGroupCreateResult.isSuccess()) {
                BasicWebUtil.prepareUpdateParams(resShareRightsGroup);
                BeanUtils.copyProperties(shareGroupCreateResult, resShareRightsGroup);
            } else {
                BasicWebUtil.prepareUpdateParams(resShareRightsGroup);
                resShareRightsGroup.setErrorMsg(shareGroupCreateResult.getErrMsg());
                resShareRightsGroup.setStatus(ShareGroupStatus.CREATE_FAILED);
            }
            resShareRightsGroupMapper.updateByPrimaryKey(resShareRightsGroup);
        } catch (Exception e) {
            logger.error("创建弹性文件权限组回调", e);
        }
    }
}
