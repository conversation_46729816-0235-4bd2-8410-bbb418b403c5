/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.listener;

import cn.com.cloudstar.rightcloud.adapter.core.MQException;
import cn.com.cloudstar.rightcloud.adapter.core.MQHelper;
import cn.com.cloudstar.rightcloud.adapter.pojo.block.BlockSnapshotCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.block.result.BlockSnapshotCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.block.result.BlockSnapshotDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.block.result.BlockSnapshotRecovryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.DiskCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.result.*;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResVd;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResVm;
import cn.com.cloudstar.rightcloud.basic.data.pojo.snapshot.ResSnapshot;
import cn.com.cloudstar.rightcloud.common.constants.BillingConstants;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.constants.res.status.ResVdBackupStatus;
import cn.com.cloudstar.rightcloud.common.constants.res.status.ResVdStatus;
import cn.com.cloudstar.rightcloud.common.constants.res.status.ResVmStatus;
import cn.com.cloudstar.rightcloud.common.constants.res.status.SnapshotStatus;
import cn.com.cloudstar.rightcloud.common.constants.res.type.CloudEnvType;
import cn.com.cloudstar.rightcloud.common.constants.res.type.ResVdSource;
import cn.com.cloudstar.rightcloud.common.constants.res.type.StoragePurpose;
import cn.com.cloudstar.rightcloud.common.constants.type.ReleaseMode;
import cn.com.cloudstar.rightcloud.common.enums.ReqSource;
import cn.com.cloudstar.rightcloud.common.enums.resource.ResourceOperateEnum;
import cn.com.cloudstar.rightcloud.common.enums.resource.ResourceTypeEnum;
import cn.com.cloudstar.rightcloud.common.exception.BizException;
import cn.com.cloudstar.rightcloud.common.exception.RetryException;
import cn.com.cloudstar.rightcloud.common.redis.JedisUtil;
import cn.com.cloudstar.rightcloud.common.util.*;
import cn.com.cloudstar.rightcloud.common.util.json.JsonUtil;
import cn.com.cloudstar.rightcloud.common.websocket.ServerMsgPublisher;
import cn.com.cloudstar.rightcloud.common.websocket.annonation.Message;
import cn.com.cloudstar.rightcloud.common.websocket.annonation.MessageParam;
import cn.com.cloudstar.rightcloud.common.websocket.support.OperateEnum;
import cn.com.cloudstar.rightcloud.common.websocket.support.ServerMsgType;
import cn.com.cloudstar.rightcloud.core.annotation.log.LogMethod;
import cn.com.cloudstar.rightcloud.core.annotation.log.LogParam;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResVdBackup;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResVdHost;
import cn.com.cloudstar.rightcloud.resource.dao.server.ResVmMapper;
import cn.com.cloudstar.rightcloud.resource.dao.snapshot.ResSnapshotMapper;
import cn.com.cloudstar.rightcloud.resource.dao.storage.ResVdBackupMapper;
import cn.com.cloudstar.rightcloud.resource.dao.storage.ResVdHostMapper;
import cn.com.cloudstar.rightcloud.resource.dao.storage.ResVdMapper;
import cn.com.cloudstar.rightcloud.resource.notify.BizNotify;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.base.Strings;
import com.google.common.base.Throwables;
import org.apache.commons.lang3.ObjectUtils;
import org.joda.time.LocalDateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;


/**
 * 块存储回调监听器
 *
 * <AUTHOR>
 */
@Component
public class DiskResultListener {

    private static List<String> MONGO_COST_OPTIMIZE_DISK_COLLECTION_NAMES = Arrays.asList("cost_optimize_disks_10",
                                                                                          "cost_optimize_disks_20");

    private static final String INUSE = "in-use";
    private static final String ERROR = "error";
    private static final String AVAILABLE = "available";
    private final Logger logger = LoggerFactory.getLogger(DiskResultListener.class);
    @Autowired
    private ResVmMapper resVmMapper;

    @Autowired
    private ResVdMapper resVdMapper;

    @Autowired
    private ResSnapshotMapper resSnapshotMapper;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private ResVdHostMapper resVdHostMapper;

    @Autowired
    private ResVdBackupMapper resVdBackupMapper;
    /**
     * 快照创建回调
     */
    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#blockSnapshotCreateResult.id", resourceType = ResourceTypeEnum.SNAPSHOT, opUser = "#blockSnapshotCreateResult.opUser", operate = ResourceOperateEnum.CREATE, success = "#blockSnapshotCreateResult.success", orgSid = "#blockSnapshotCreateResult.orgSid")
    @Message(refKey = "#blockSnapshotCreateResult.id", envId = "#blockSnapshotCreateResult.cloudEnvId", msgType = ServerMsgType.SNAPSHOT, opUser = "#blockSnapshotCreateResult.opUser", operate = OperateEnum.CREATE, success = "#blockSnapshotCreateResult.success", refNameKey = "#blockSnapshotCreateResult.name", errorMsg = "#blockSnapshotCreateResult.errMsg")
    @BizNotify
    public void handleMessage(
            @LogParam("blockSnapshotCreateResult") @MessageParam("blockSnapshotCreateResult") BlockSnapshotCreateResult blockSnapshotCreateResult) {

        ResSnapshot resSnapshot = null;
        try {
            AtomicReference<ResSnapshot> reference = new AtomicReference<>();
            RetryUtil.retry(10, 5, TimeUnit.SECONDS, () -> {
                ResSnapshot snapshot = resSnapshotMapper.selectByPrimaryKeyForUpdate(
                        Long.valueOf(blockSnapshotCreateResult.getId()));
                if (Objects.isNull(snapshot)) {
                    throw new RetryException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_807627314));
                }
                reference.set(snapshot);
            });
            resSnapshot = reference.get();
            if (Objects.isNull(resSnapshot)) {
                logger.warn("未找到指定的硬盘快照数据!, 硬盘快照id:{}", blockSnapshotCreateResult.getId());
                return;
            }
            if (blockSnapshotCreateResult.isSuccess()) {
                resSnapshot.setUuid(blockSnapshotCreateResult.getSnapshotId());
                resSnapshot.setStatus(SnapshotStatus.SUCCESS);
                resSnapshot.setCreatedDate(blockSnapshotCreateResult.getCreatedDate());
                //运营特殊逻辑
                if (ReqSource.CLOUD_BOSS.name().equals(blockSnapshotCreateResult.getReqSource())) {
                    if (blockSnapshotCreateResult.getPeriod() != null && BillingConstants.ChargeType.PRE_PAID.equals(
                            resSnapshot.getChargeType())) {
                        resSnapshot.setEndTime(cn.hutool.core.date.DateUtil.offsetMonth(LocalDateTime.now().toDate(),
                                                                                        blockSnapshotCreateResult.getPeriod()));
                    }
                }
                resSnapshotMapper.updateByPrimaryKeySelective(resSnapshot);
            } else {
                resSnapshot.setStatus(SnapshotStatus.FAILURE);
                resSnapshotMapper.updateByPrimaryKeySelective(resSnapshot);
            }
        } catch (Exception e) {
            logger.error("快照创建回调", e);
        } finally {
            boolean ksy = CloudEnvType.KSYUN.equals(blockSnapshotCreateResult.getProviderType())
                    || CloudEnvType.KING_STACK.equals(blockSnapshotCreateResult.getProviderType());

            // 金山云创建硬盘快照需要排队创建
            boolean listComplete = false;
            try {
                BlockSnapshotCreate blockSnapshotCreate = null;
                String diskListKey = "disk:snapshot:create:" + resSnapshot.getResVdId();
                do {
                    String json = JedisUtil.instance().lpop(diskListKey);
                    if (StringUtil.isNullOrEmpty(json)) {
                        blockSnapshotCreate = null;
                        break;
                    }
                    if (JSONUtil.isJson(json)) {
                        blockSnapshotCreate = JSONUtil.toBean(json, BlockSnapshotCreate.class);
                    }
                } while (Objects.nonNull(blockSnapshotCreate) && blockSnapshotCreateResult.getId()
                                                                                          .equals(String.valueOf(
                                                                                                  blockSnapshotCreate.getId())));
                if (Objects.nonNull(blockSnapshotCreate)) {

                    MQHelper.sendMessage(blockSnapshotCreate);
                } else {
                    logger.info("硬盘:{} 快照创建完成，清空redis数据", resSnapshot.getResVdId());
                    listComplete = true;
                    JedisUtil.instance().del(diskListKey);
                }
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            }
            boolean updateVdFlag = StringUtil.isNotBlank(blockSnapshotCreateResult.getVolumeId());
            if (listComplete && updateVdFlag) {
                ResVd resVdUpdate = new ResVd();
                resVdUpdate.setResVdSid(blockSnapshotCreateResult.getVolumeId());
                resVdUpdate.setStatus(ResVdStatus.NORMAL);
                this.resVdMapper.updateByPrimaryKeySelective(resVdUpdate);
            }
        }
    }

    /**
     * 删除快照回调函数
     */
    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#blockSnapshotDeleteResult.id", resourceType = ResourceTypeEnum.SNAPSHOT, opUser = "#blockSnapshotDeleteResult.opUser", operate = ResourceOperateEnum.DELETE, success = "#blockSnapshotDeleteResult.success", orgSid = "#blockSnapshotDeleteResult.orgSid")
    @Message(refKey = "#blockSnapshotDeleteResult.id", envId = "#blockSnapshotDeleteResult.cloudEnvId", msgType = ServerMsgType.SNAPSHOT, opUser = "#blockSnapshotDeleteResult.opUser", operate = OperateEnum.DELETE, success = "#blockSnapshotDeleteResult.success", refNameKey = "#blockSnapshotDeleteResult.volumeId", errorMsg = "#blockSnapshotDeleteResult.errMsg")
    @BizNotify
    public void handleMessage(
            @LogParam("blockSnapshotDeleteResult") @MessageParam("blockSnapshotDeleteResult") BlockSnapshotDeleteResult blockSnapshotDeleteResult) {
        logger.info("删除快照回调 | 回调参数 ： {}", JsonUtil.toJson(blockSnapshotDeleteResult));
        try {
            ResSnapshot resSnapshot = resSnapshotMapper.selectByPrimaryKey(blockSnapshotDeleteResult.getId());
            if (blockSnapshotDeleteResult.isSuccess()) {
                resSnapshotMapper.deleteByPrimaryKey(blockSnapshotDeleteResult.getId());
            } else {
                Map<String, Object> options = blockSnapshotDeleteResult.getOptions();

                // 如果没成功，还原为初始状态
                resSnapshot.setStatus(options.get("originStatus").toString());
                resSnapshotMapper.updateByPrimaryKeySelective(resSnapshot);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }

    }

    /**
     * 磁盘回滚回调函数
     */
    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#blockSnapshotRecovryResult.id", resourceType = ResourceTypeEnum.SNAPSHOT, opUser = "#blockSnapshotRecovryResult.opUser", operate = ResourceOperateEnum.ROLL_BACK, success = "#blockSnapshotRecovryResult.success", orgSid = "#blockSnapshotRecovryResult.orgSid")
    @Message(refKey = "#blockSnapshotRecovryResult.id", envId = "#blockSnapshotRecovryResult.cloudEnvId", msgType = ServerMsgType.SNAPSHOT, opUser = "#blockSnapshotRecovryResult.opUser", operate = OperateEnum.ROLL_BACK, success = "#blockSnapshotRecovryResult.success", refNameKey = "#blockSnapshotRecovryResult.name", errorMsg = "#blockSnapshotRecovryResult.errMsg")
    public void handleMessage(
            @LogParam("blockSnapshotRecovryResult") @MessageParam("blockSnapshotRecovryResult") BlockSnapshotRecovryResult blockSnapshotRecovryResult) {
        logger.info("回滚磁盘回调 | 回调参数 ： {}", JsonUtil.toJson(blockSnapshotRecovryResult));
        try {
            // 不管成功与否，都将状态改成最初状态
            ResSnapshot resSnapshotUpdate = new ResSnapshot();
            resSnapshotUpdate.setId(blockSnapshotRecovryResult.getId());
            resSnapshotUpdate.setStatus(SnapshotStatus.SUCCESS);
            this.resSnapshotMapper.updateByPrimaryKeySelective(resSnapshotUpdate);

            if (blockSnapshotRecovryResult.isSuccess()) {
                ResSnapshot resSnapshot = resSnapshotMapper.selectByPrimaryKey(blockSnapshotRecovryResult.getId());

                if (CloudEnvType.KSYUN.equals(blockSnapshotRecovryResult.getProviderType())
                        || CloudEnvType.KING_STACK.equals(blockSnapshotRecovryResult.getProviderType())) {
                    ResVd resVd = this.resVdMapper.selectByPrimaryKey(resSnapshot.getResVdId());
                    if (Objects.nonNull(resVd)) {
                        ResVm resVmUpdate = new ResVm();
                        resVmUpdate.setId(resVd.getResVmId());
                        resVmUpdate.setStatus(ResVmStatus.RUNNING);
                        this.resVmMapper.updateByPrimaryKeySelective(resVmUpdate);
                    }
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
    }

    /**
     * 块存储创建回调
     */
    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#diskCreateResult.sid", resourceType = ResourceTypeEnum.VD, opUser = "#diskCreateResult.opUser", operate = ResourceOperateEnum.CREATE, success = "#diskCreateResult.success", orgSid = "#diskCreateResult.orgSid")
    @Message(refKey = "#diskCreateResult.sid", envId = "#diskCreateResult.cloudEnvId", msgType = ServerMsgType.VD, opUser = "#diskCreateResult.opUser", operate = OperateEnum.CREATE, success = "#diskCreateResult.success", refNameKey = "#diskCreateResult.name", errorMsg = "#diskCreateResult.errMsg")
    @BizNotify
    public void handleMessage(
            @LogParam("diskCreateResult") @MessageParam("diskCreateResult") DiskCreateResult diskCreateResult) {
        logger.info("创建块存储回调 | 回调参数 ： {}", JsonUtil.toJson(diskCreateResult));
        ResVd resVd = null;

        Map<String, Object> options = diskCreateResult.getOptions();
        String resVmIdField = "resVmId";
        String resVmId =
                (Objects.nonNull(options) && Objects.nonNull(options.get(resVmIdField))) ? options.get(resVmIdField)
                                                                                                  .toString() : null;
        try {
            if (!Strings.isNullOrEmpty(resVmId) && !CloudEnvType.HUAWEICLOUD.equals(
                    diskCreateResult.getProviderType())) {
                // 将主机还原为原始状态
                ResVm resVm = new ResVm();
                resVm.setId(options.get(resVmIdField).toString());
                resVm.setStatus(options.get("originStatus").toString());
                resVmMapper.updateByPrimaryKeySelective(resVm);
            }

            // 创建成功
            if (diskCreateResult.isSuccess()) {
                String sid = diskCreateResult.getSid();
                resVd = resVdMapper.selectByPrimaryKey(sid);

                // 更新块存储的为可用状态
                //进行判断，如果不为空，则进行相关处理
                if (resVd != null) {
                    resVd.setStatus(ResVdStatus.NORMAL);
                    // 设置块存储的uuid
                    resVd.setUuid(diskCreateResult.getUuid());
                    resVd.setStartTime(new Date());
                    resVd.setEndTime(DateUtil.plusMonths(resVd.getStartTime(), diskCreateResult.getPeriod()));
                } else {
                    //无法找到硬盘时使用
                    return;
                }
                //vmware的uuid和磁盘存放路径相同
                if (CloudEnvType.VMWARE.equals(diskCreateResult.getProviderType())) {
                    resVd.setLogicVolume(diskCreateResult.getUuid());
                    //现在在这儿存所属宿主名称
                    resVd.setDeviceName(diskCreateResult.getDatastore());
                    resVd.setPath(diskCreateResult.getLocation());
                    resVd.setMountPoint(diskCreateResult.getLocation());
                } else if (CloudEnvType.OPEN_STACK.equals(diskCreateResult.getProviderType())
                        || CloudEnvType.CLOUDOS.equals(diskCreateResult.getProviderType())) {
                    resVd.setPath(diskCreateResult.getLocation());
                    resVd.setMountPoint(diskCreateResult.getLocation());
                    if (CloudEnvType.OPEN_STACK.equals(diskCreateResult.getProviderType())) {
                        resVd.setBootable(diskCreateResult.getBootable());
                    }
                    if (INUSE.equalsIgnoreCase(diskCreateResult.getStatus())) {
                        resVd.setStatus(ResVdStatus.NORMAL);
                    } else if (ERROR.equalsIgnoreCase(diskCreateResult.getStatus())) {
                        resVd.setStatus(ResVdStatus.ERROR);
                        resVd.setVolumeTypeId(null);
                    } else if (AVAILABLE.equalsIgnoreCase(diskCreateResult.getStatus())) {
                        resVd.setStatus(ResVdStatus.NORMAL);
                    }
                } else if (CloudEnvType.POWER_VC.equals(diskCreateResult.getProviderType())) {
                    if (INUSE.equalsIgnoreCase(diskCreateResult.getStatus())) {
                        resVd.setStatus(ResVdStatus.NORMAL);
                        resVd.setErrorMsg("创建成功");
                    } else if (ERROR.equalsIgnoreCase(diskCreateResult.getStatus())) {
                        resVd.setStatus(ResVdStatus.ERROR);
                        resVd.setVolumeTypeId(null);
                        resVd.setErrorMsg("创建失败，执行存储操作时发生错误");
                    } else if (AVAILABLE.equalsIgnoreCase(diskCreateResult.getStatus())) {
                        resVd.setStatus(ResVdStatus.NORMAL);
                        resVd.setErrorMsg("创建成功");
                    }
                } else if (CloudEnvType.HUAWEICLOUD.equals(diskCreateResult.getProviderType())) {
                    Object device = options.get("device");
                    if (!StringUtil.isNullOrEmpty(device)) {
                        resVd.setPath((String) device);
                        resVd.setMountPoint((String) device);
                    }
                } else if (CloudEnvType.FUSIONCOMPUTE.equals(diskCreateResult.getProviderType())) {
                    resVd.setLogicVolume(diskCreateResult.getUuid());
                    //现在在这儿存所属宿主名称
                    resVd.setDeviceName(diskCreateResult.getDatastore());
                    resVd.setUri(diskCreateResult.getUri());
                    resVd.setUrn(diskCreateResult.getUrn());
                } else if (CloudEnvType.KSYUN.equals(diskCreateResult.getProviderType())
                        || CloudEnvType.KING_STACK.equals(diskCreateResult.getProviderType())) {
                    resVd.setStatus(diskCreateResult.getStatus());
                    resVd.setSource(ResVdSource.CLOUD_VOLUME);
                    Map<String, ?> map = diskCreateResult.getMap();
                    if (Objects.nonNull(map.get("mountPoint"))) {
                        resVd.setMountPoint(map.get("mountPoint").toString());
                    }
                } else if (CloudEnvType.AWS.equals(diskCreateResult.getProviderType())) {
                    resVd.setPath(diskCreateResult.getPath());
                    resVd.setMountPoint(diskCreateResult.getPath());
                }
                // 判断是否有hostId
                if (options != null && !options.isEmpty()) {
                    if (!Strings.isNullOrEmpty(resVmId)) {
                        resVd.setResVmId(resVmId);
                        //如果云主机属于项目，则硬盘也属于该项目
                        ResVm resVm = resVmMapper.selectByPrimaryKey(resVmId);
                        if (Objects.nonNull(resVm)) {
                            resVd.setOrgSid(resVm.getOrgSid());
                        }
                        if (CloudEnvType.KSYUN.equals(diskCreateResult.getProviderType())
                                || CloudEnvType.KING_STACK.equals(diskCreateResult.getProviderType())) {
                            if (Objects.isNull(diskCreateResult.getMap().get("mountPoint"))) {
                                resVd.setResVmId("");
                                resVd.setReleaseMode("");
                            }
                        }
                    }
                }
                resVdMapper.updateByPrimaryKeySelective(resVd);
            } else { // 创建失败
                logger.error(diskCreateResult.getErrMsg());
                String sid = diskCreateResult.getSid();
                resVd = resVdMapper.selectByPrimaryKey(sid);

                // 更新块存储的为创建失败状态
                resVd.setStatus(ResVdStatus.FAILURE);
                // 创建块存储失败时，将错误信息存入到表中，以便取出来显示
                resVd.setErrorMsg(diskCreateResult.getErrMsg());
                resVdMapper.updateByPrimaryKeySelective(resVd);

            }

            if (ObjectUtils.isEmpty(resVd)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_607095064));
            }
        } catch (Exception e) {
            logger.error("块存储创建回调", e);
        } finally {
            // 发送推送消息，更新页面
            if (!Strings.isNullOrEmpty(resVmId)) {
                ServerMsgPublisher.sendMsgToDetail(ServerMsgType.VM, resVmId, MapsKit.of("updType", "vd"));

            }

            if (StrUtil.isNotEmpty(resVmId) && CloudEnvType.VMWARE.equals(resVd.getCloudEnvType())) {
                try {
                    DiskCreate diskCreate = null;
                    String diskListKey = "vmware:disk:create:" + resVmId;
                    String statusKey = "vmware:disk:create:originStatus:" + resVmId;
                    do {
                        String json = JedisUtil.instance().lpop(diskListKey);
                        if (StringUtil.isNullOrEmpty(json)) {
                            diskCreate = null;
                            break;
                        }
                        if (JSONUtil.isJson(json)) {
                            diskCreate = JSONUtil.toBean(json, DiskCreate.class);
                        }
                    } while (Objects.nonNull(diskCreate) && resVd.getResVdSid().equals(diskCreate.getSid()));
                    if (Objects.nonNull(diskCreate)) {
                        logger.info("VMWare 虚拟机:{} 硬盘创建队列[消费]，总数量:{}", resVmId,
                                    JedisUtil.instance().countList(diskListKey));
                        diskCreate.getOptions().put("originStatus", JedisUtil.instance().get(statusKey));
                        MQHelper.sendMessage(diskCreate);
                    } else {
                        logger.info("VMWare 虚拟机:{} 硬盘创建完成，清空redis数据", resVmId);
                        JedisUtil.instance().del(diskListKey);
                        JedisUtil.instance().del(statusKey);
                    }
                } catch (MQException e) {
                    logger.error(e.getMessage(), e);
                }
            }
        }
    }

    /**
     * 块存储挂载回调
     */
    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#diskAttachResult.options.get('resVdSid')", resourceType = ResourceTypeEnum.VD, opUser = "#diskAttachResult.opUser", operate = ResourceOperateEnum.ATTACH, success = "#diskAttachResult.success", orgSid = "#diskAttachResult.orgSid")
    @Message(refKey = "#diskAttachResult.options.get('resVdSid')", envId = "#diskAttachResult.cloudEnvId", msgType = ServerMsgType.VD, opUser = "#diskAttachResult.opUser", operate = OperateEnum.ATTACH, success = "#diskAttachResult.success", errorMsg = "#diskAttachResult.errMsg")
    public void handleMessage(
            @LogParam("diskAttachResult") @MessageParam("diskAttachResult") DiskAttachResult diskAttachResult) {
        logger.info("挂载块存储回调 | 回调参数 ： {}", JsonUtil.toJson(diskAttachResult));
        ResVd resVd = null;
        Map<String, Object> options = diskAttachResult.getOptions();
        ResVm resVm = resVmMapper.selectByPrimaryKey(options.get("resVmId").toString());
        try {
            // 挂载成功
            if (diskAttachResult.isSuccess()) {
                resVd = resVdMapper.selectByPrimaryKey(options.get("resVdSid").toString());

                // 更新块存储的为运行状态
                resVd.setStatus(ResVdStatus.NORMAL);
                if (StringUtil.isNullOrEmpty(options.get("isShare"))) {
                    resVd.setPath(diskAttachResult.getDevice());
                    resVd.setMountPoint(diskAttachResult.getDevice());
                    // 设置云主机ID
                    resVd.setResVmId(resVm.getId());
                } else {
                    Criteria criteria = new Criteria();
                    ResVdHost resVdHost = new ResVdHost();
                    resVdHost.setVdSid(diskAttachResult.getVolumeId());
                    resVdHost.setHostId(options.get("resVmId").toString());
                    resVdHost.setDevicePath(diskAttachResult.getDevice());
                    resVdHost.setMountPoint(diskAttachResult.getDevice());
                    resVdHost.setCloudEnvId(resVm.getCloudEnvId());
                    resVdHost.setOrgSid(resVm.getOrgSid());
                    criteria.put("vdSid", diskAttachResult.getVolumeId());
                    criteria.put("hostId", resVm.getId());
                    resVdHostMapper.deleteByParams(criteria);
                    resVdHostMapper.insert(resVdHost);
                }
                //如果云主机属于项目，则硬盘也属于该项目
                resVd.setOrgSid(resVm.getOrgSid());
                if (!Strings.isNullOrEmpty(diskAttachResult.getDevice())) {
                    resVd.setPath(diskAttachResult.getDevice());
                    resVd.setMountPoint(diskAttachResult.getDevice());
                }
                resVdMapper.updateByPrimaryKeySelective(resVd);

            } else { // 挂载失败
                logger.error(diskAttachResult.getErrMsg());
                resVd = new ResVd();
                resVd.setResVdSid(options.get("resVdSid").toString());
                if (!StringUtil.isNullOrEmpty(options.get("isShare"))) {
                    Criteria criteria = new Criteria();
                    criteria.put("vdSid", options.get("resVdSid").toString());
                    resVdHostMapper.deleteByParams(criteria);
                }
                // 更新状态为正常
                resVd.setStatus(ResVdStatus.NORMAL);
                resVdMapper.updateByPrimaryKeySelective(resVd);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        } finally {
            // 发送推送消息，更新页面
            ServerMsgPublisher.sendMsgToDetail(ServerMsgType.VM, resVm.getId(), MapsKit.of("updType", "vd"));
        }
    }

    /**
     * 块存储卸载回调
     */
    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#diskDetachResult.options.get('resVdSid')", resourceType = ResourceTypeEnum.VD, opUser = "#diskDetachResult.opUser", operate = ResourceOperateEnum.DETACH, success = "#diskDetachResult.success", orgSid = "#diskDetachResult.orgSid")
    @Message(refKey = "#diskDetachResult.options.get('resVdSid')", envId = "#diskDetachResult.cloudEnvId", msgType = ServerMsgType.VD, opUser = "#diskDetachResult.opUser", operate = OperateEnum.DETACH, success = "#diskDetachResult.success", errorMsg = "#diskDetachResult.errMsg")
    public void handleMessage(
            @LogParam("diskDetachResult") @MessageParam("diskDetachResult") DiskDetachResult diskDetachResult) {
        logger.info("卸载块存储回调 | 回调参数 ： {}", JsonUtil.toJson(diskDetachResult));
        ResVd resVd = new ResVd();
        Map<String, Object> options = diskDetachResult.getOptions();
        resVd.setResVdSid(options.get("resVdSid").toString());
        try {
            // 卸载成功
            if (diskDetachResult.isSuccess()) {

                // 更新块存储的为可用状态
                resVd.setStatus(ResVdStatus.NORMAL);
                // 已卸载的硬盘，释放模式更新为单独
                resVd.setReleaseMode(ReleaseMode.STAND_ALONE);
                // 解决19967bug，卸载后硬盘属性丢失问题
                resVd.setStoragePurpose(StoragePurpose.DATA_DISK);
                resVd.setResVmId(null);
                if (!StringUtil.isNullOrEmpty(options.get("isShare")) && !StringUtil.isNullOrEmpty(
                        options.get("resVmId"))) {
                    Criteria criteria = new Criteria();
                    criteria.put("vdSid", diskDetachResult.getVolumeId());
                    criteria.put("hostId", options.get("resVmId").toString());
                    resVdHostMapper.deleteByParams(criteria);
                }
                resVdMapper.detachResVd(resVd);

            } else { // 卸载失败
                logger.error(diskDetachResult.getErrMsg());

                // 还原块存储为挂载状态
                resVd.setStatus(ResVdStatus.NORMAL);
                resVdMapper.updateByPrimaryKeySelective(resVd);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        } finally {
            // 发送推送消息，更新页面
            ServerMsgPublisher.sendMsgToDetail(ServerMsgType.VD, resVd.getResVdSid(), MapsKit.of("updType", "vd"));
        }
    }

    /**
     * 块存储删除回调
     */
    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#diskDeleteResult.options.get('resVdSid')", resourceType = ResourceTypeEnum.VD, opUser = "#diskDeleteResult.opUser", operate = ResourceOperateEnum.DELETE, success = "#diskDeleteResult.success", orgSid = "#diskDeleteResult.orgSid")
    @Message(refKey = "#diskDeleteResult.options.get('resVdSid')", envId = "#diskDeleteResult.cloudEnvId", msgType = ServerMsgType.VD, opUser = "#diskDeleteResult.opUser", operate = OperateEnum.DELETE, success = "#diskDeleteResult.success", errorMsg = "#diskDeleteResult.errMsg")
    @BizNotify
    public void handleMessage(
            @LogParam("diskDeleteResult") @MessageParam("diskDeleteResult") DiskDeleteResult diskDeleteResult) {
        logger.info("删除块存储回调 | 回调参数 ： {}", JsonUtil.toJson(diskDeleteResult));
        ResVd resVd = new ResVd();
        Map<String, Object> options = diskDeleteResult.getOptions();
        resVd.setResVdSid(options.get("resVdSid").toString());
        ResVd vd = this.resVdMapper.selectByPrimaryKey(resVd.getResVdSid());
        try {
            // 删除成功
            if (diskDeleteResult.isSuccess()) {

                if (!CollectionUtils.isEmpty(diskDeleteResult.getSnapshotIds())) {

                    resSnapshotMapper.deleteByParams(
                            new Criteria("uuids", diskDeleteResult.getSnapshotIds()).put("cloudEnvId",
                                                                                         resVd.getCloudEnvId()));
                }
                // 更新状态为已删除
                resVd.setStatus(ResVdStatus.DELETED);
                resVd.setEndTime(Calendar.getInstance().getTime());
                resVdMapper.updateByPrimaryKeySelective(resVd);

                // 将快照与硬盘的关系解绑
                resSnapshotMapper.unbindingFromVd(resVd.getResVdSid());

                Query query = Query.query(org.springframework.data.mongodb.core.query.Criteria.where("instanceId")
                                                                                              .is(resVd.getResVdSid()));

                for (String collectionName : MONGO_COST_OPTIMIZE_DISK_COLLECTION_NAMES) {
                    mongoTemplate.remove(query, collectionName);
                }
            } else { // 删除失败e
                logger.error(diskDeleteResult.getErrMsg());
                resVd = resVdMapper.selectByPrimaryKey(options.get("resVdSid").toString());

                // 还原块存储为可用状态
                resVd.setStatus(Convert.toStr(options.get("originalStatus"), ResVdStatus.NORMAL));
                resVdMapper.updateByPrimaryKeySelective(resVd);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        } finally {
            // 发送推送消息，更新页面
            if (!Strings.isNullOrEmpty(vd.getResVmId())) {
                ServerMsgPublisher.sendMsgToDetail(ServerMsgType.VM, vd.getResVmId(), MapsKit.of("updType", "vd"));

            }
        }
    }

    /**
     * 块存储备份创建回调
     */
    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#diskBackupCreateResult.id", resourceType = ResourceTypeEnum.VDBACKUP, opUser = "#diskBackupCreateResult.opUser", operate = ResourceOperateEnum.CREATE, success = "#diskBackupCreateResult.success", orgSid = "#diskBackupCreateResult.orgSid")
    @Message(refKey = "#diskBackupCreateResult.id", envId = "#diskBackupCreateResult.cloudEnvId", msgType = ServerMsgType.VDBACKUP, opUser = "#diskBackupCreateResult.opUser", operate = OperateEnum.CREATE, success = "#diskBackupCreateResult.success", refNameKey = "#diskBackupCreateResult.name", errorMsg = "#diskBackupCreateResult.errMsg")
    public void handleMessage(
            @LogParam("diskBackupCreateResult") @MessageParam("diskBackupCreateResult") DiskBackupCreateResult diskBackupCreateResult) {
        logger.info("创建块存储备份回调 | 回调参数 ： {}", JsonUtil.toJson(diskBackupCreateResult));
        ResVdBackup resVdBackup = null;
        try {
            // 还原磁盘状态
            if (StringUtil.isNotBlank(diskBackupCreateResult.getResVdSid())) {
                ResVd resVdUpdate = new ResVd();
                resVdUpdate.setResVdSid(diskBackupCreateResult.getResVdSid());
                resVdUpdate.setStatus(ResVdStatus.NORMAL);
                resVdMapper.updateByPrimaryKeySelective(resVdUpdate);
            }

            resVdBackup = resVdBackupMapper.selectByPrimaryKey(diskBackupCreateResult.getId());
            if (Objects.isNull(resVdBackup)) {
                logger.warn("{}块存储备份不存在.", diskBackupCreateResult.getId());
                return;
            }
            if (diskBackupCreateResult.isSuccess()) {
                // 创建成功
                ResVdBackup resVdBackupUpdate = new ResVdBackup();
                resVdBackupUpdate.setId(diskBackupCreateResult.getId());
                BeanUtils.copyProperties(diskBackupCreateResult, resVdBackupUpdate);
                resVdBackupUpdate.setStatus(ResVdBackupStatus.AVAILABLE);
                resVdBackupUpdate.setUuid(diskBackupCreateResult.getUuid());
                resVdBackupMapper.updateByPrimaryKeySelective(resVdBackupUpdate);
            } else {
                // 创建失败
                ResVdBackup resVdBackupUpdate = new ResVdBackup();
                resVdBackupUpdate.setId(diskBackupCreateResult.getId());
                resVdBackupUpdate.setStatus(ResVdBackupStatus.CREATE_FAILURE);
                resVdBackupUpdate.setErrorMsg(diskBackupCreateResult.getErrMsg());
                resVdBackupMapper.updateByPrimaryKeySelective(resVdBackupUpdate);
            }
        } catch (Exception e) {
            logger.error("块存储备份创建回调异常 [{}]", Throwables.getStackTraceAsString(e));
        }
    }


    /**
     * 块存储备份删除回调
     */
    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#diskBackupDeleteResult.id", resourceType = ResourceTypeEnum.VDBACKUP, opUser = "#diskBackupDeleteResult.opUser", operate = ResourceOperateEnum.DELETE, success = "#diskBackupDeleteResult.success", orgSid = "#diskBackupDeleteResult.orgSid")
    @Message(refKey = "#diskBackupDeleteResult.id", envId = "#diskBackupDeleteResult.cloudEnvId", msgType = ServerMsgType.VDBACKUP, opUser = "#diskBackupDeleteResult.opUser", operate = OperateEnum.DELETE, success = "#diskBackupDeleteResult.success", errorMsg = "#diskBackupDeleteResult.errMsg")
    public void handleMessage(
            @LogParam("diskBackupDeleteResult") @MessageParam("diskBackupDeleteResult") DiskBackupDeleteResult diskBackupDeleteResult) {
        logger.info("删除块存储备份回调 | 回调参数 ： {}", JsonUtil.toJson(diskBackupDeleteResult));
        ResVdBackup resVdBackup = null;
        try {
            resVdBackup = this.resVdBackupMapper.selectByPrimaryKey(diskBackupDeleteResult.getId());
            if (diskBackupDeleteResult.isSuccess()) {
                // 删除成功
                resVdBackupMapper.deleteByPrimaryKey(diskBackupDeleteResult.getId());
            } else {
                // 删除失败 还原状态
                ResVdBackup resVdBackupUpdate = new ResVdBackup();
                resVdBackupUpdate.setId(resVdBackup.getId());
                resVdBackupUpdate.setStatus(ResVdBackupStatus.AVAILABLE);
                resVdBackupUpdate.setErrorMsg("删除失败：" + diskBackupDeleteResult.getErrMsg());
                resVdBackupMapper.updateByPrimaryKeySelective(resVdBackupUpdate);
            }
        } catch (Exception e) {
            logger.error("删除块存储备份回调异常 [{}]", Throwables.getStackTraceAsString(e));
        }
    }

    /**
     * 块存储备份恢复回调
     */
    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#diskBackupRestoreResult.id", resourceType = ResourceTypeEnum.VDBACKUP, opUser = "#diskBackupRestoreResult.opUser", operate = ResourceOperateEnum.RECOVER, success = "#diskBackupRestoreResult.success", orgSid = "#diskBackupRestoreResult.orgSid")
    @Message(refKey = "#diskBackupRestoreResult.id", envId = "#diskBackupRestoreResult.cloudEnvId", msgType = ServerMsgType.VDBACKUP, opUser = "#diskBackupRestoreResult.opUser", operate = OperateEnum.RECOVER, success = "#diskBackupRestoreResult.success", errorMsg = "#diskBackupRestoreResult.errMsg")
    public void handleMessage(
            @LogParam("diskBackupRestoreResult") @MessageParam("diskBackupRestoreResult") DiskBackupRestoreResult diskBackupRestoreResult) {
        logger.info("恢复块存储备份回调 | 回调参数 ： {}", JsonUtil.toJson(diskBackupRestoreResult));
        try {
            ResVdBackup resVdBackup = resVdBackupMapper.selectByPrimaryKey(diskBackupRestoreResult.getId());
            if (Objects.isNull(resVdBackup)) {
                ResVd resVdUpdate = new ResVd();
                resVdUpdate.setResVdSid(diskBackupRestoreResult.getTargetResVdSid());
                resVdUpdate.setStatus(ResVdStatus.NORMAL);
                resVdMapper.updateByPrimaryKeySelective(resVdUpdate);
                logger.warn("{}块存储备份不存在.", diskBackupRestoreResult.getId());
                return;
            }

            if (diskBackupRestoreResult.isSuccess()) {
                // 恢复备份成功
                ResVdBackup resVdBackupUpdate = new ResVdBackup();
                resVdBackupUpdate.setId(resVdBackup.getId());
                resVdBackupUpdate.setStatus(ResVdBackupStatus.AVAILABLE);
                resVdBackupMapper.updateByPrimaryKeySelective(resVdBackupUpdate);

                // 更新硬盘信息
                DiskBackupRestoreResult.VolumeInfo volumeInfo = diskBackupRestoreResult.getVolumeInfo();
                ResVd resVdUpdate = new ResVd();
                resVdUpdate.setResVdSid(diskBackupRestoreResult.getTargetResVdSid());
                resVdUpdate.setAllocateDiskSize(Long.parseLong(volumeInfo.getAllocateDiskSize()));
                resVdUpdate.setDesc(volumeInfo.getDesc());
                resVdUpdate.setUuid(volumeInfo.getUuid());
                resVdUpdate.setStoragePurpose(volumeInfo.getStoragePurpose());
                resVdUpdate.setStatus(volumeInfo.getStatus());
                resVdUpdate.setBootable(volumeInfo.getBootable());
                resVdMapper.updateByPrimaryKeySelective(resVdUpdate);
            } else {
                // 恢复备份失败
                ResVdBackup resVdBackupUpdate = new ResVdBackup();
                resVdBackupUpdate.setId(resVdBackup.getId());
                resVdBackupUpdate.setStatus(ResVdBackupStatus.AVAILABLE);
                resVdBackupUpdate.setErrorMsg(diskBackupRestoreResult.getErrMsg());
                resVdBackupMapper.updateByPrimaryKeySelective(resVdBackupUpdate);

                // 删除创建中的硬盘
                resVdMapper.deleteByPrimaryKey(diskBackupRestoreResult.getTargetResVdSid());
            }
        } catch (Exception e) {
            logger.error("恢复块存储备份创建回调异常 [{}]", Throwables.getStackTraceAsString(e));
        } finally {
            // 发送推送消息，更新硬盘页面
            ServerMsgPublisher.sendMsgToResourceType(ServerMsgType.VD.getTypeFamily(), "");
        }
    }

    /**
     * 块存储扩容回调
     */
    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#diskExtendResult.resVdSid", resourceType = ResourceTypeEnum.VD, opUser = "#diskExtendResult.opUser", operate = ResourceOperateEnum.EXTEND, success = "#diskExtendResult.success", orgSid = "#diskExtendResult.orgSid")
    @Message(refKey = "#diskExtendResult.resVdSid", envId = "#diskExtendResult.cloudEnvId", msgType = ServerMsgType.VD, opUser = "#diskExtendResult.opUser", operate = OperateEnum.EXTEND, success = "#diskExtendResult.success", errorMsg = "#diskExtendResult.errMsg")
    public void handleMessage(
            @LogParam("diskExtendResult") @MessageParam("diskExtendResult") DiskExtendResult diskExtendResult) {
        logger.info("扩容块存储回调 | 回调参数 ： {}", JsonUtil.toJson(diskExtendResult));
        ResVd resVd = null;
        try {
            resVd = this.resVdMapper.selectByPrimaryKey(diskExtendResult.getResVdSid());
            if (Objects.isNull(resVd)) {
                logger.warn("{}块存储不存在.", diskExtendResult.getResVdSid());
                return;
            }
            if (diskExtendResult.isSuccess()) {
                // 扩容成功
                ResVd resVdUpdate = new ResVd();
                resVdUpdate.setResVdSid(diskExtendResult.getResVdSid());
                resVdUpdate.setStatus(ResVdStatus.NORMAL);
                resVdUpdate.setAllocateDiskSize(diskExtendResult.getSize().longValue());
                resVdMapper.updateByPrimaryKeySelective(resVdUpdate);
            } else {
                // 扩容失败
                ResVd resVdUpdate = new ResVd();
                resVdUpdate.setResVdSid(diskExtendResult.getResVdSid());
                resVdUpdate.setStatus(ResVdStatus.NORMAL);
                resVdMapper.updateByPrimaryKeySelective(resVdUpdate);
            }
        } catch (Exception e) {
            logger.error("扩容块存储回调异常[{}]", Throwables.getStackTraceAsString(e));
        }
    }
}
