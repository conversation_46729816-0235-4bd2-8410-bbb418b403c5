/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.engine.event.publish;

import com.google.common.base.Throwables;

import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionalEventListener;

import cn.com.cloudstar.rightcloud.adapter.core.MQHelper;
import cn.com.cloudstar.rightcloud.basic.data.pojo.deploy.DeployTask;
import cn.com.cloudstar.rightcloud.basic.data.service.code.BasicCodeService;
import cn.com.cloudstar.rightcloud.basic.data.service.deploy.BasicDeployTaskService;
import cn.com.cloudstar.rightcloud.common.constants.ansible.AnsibleMessageFlag;
import cn.com.cloudstar.rightcloud.common.constants.ansible.AnsibleMqConfig;
import cn.com.cloudstar.rightcloud.common.constants.ansible.AnsibleTaskTypeConfig;
import cn.com.cloudstar.rightcloud.common.constants.status.DeployTaskStatus;
import cn.com.cloudstar.rightcloud.common.exception.RetryException;
import cn.com.cloudstar.rightcloud.common.util.HttpClientUtil;
import cn.com.cloudstar.rightcloud.common.util.MessageUtil;
import cn.com.cloudstar.rightcloud.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.common.util.json.JsonUtil;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.deploy.AnsiblePlayBook;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.deploy.AnsiblePlaybookEvent;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.engine.CallbakLog;
import cn.com.cloudstar.rightcloud.resource.engine.event.callback.handler.log.AppDeployCallbackLogMessageHandler;

/**
 * <AUTHOR>
 * @date 2018/4/3
 */
@Component
public class AnsiblePlayBookListener {

    private static Logger logger = LoggerFactory.getLogger(AnsiblePlayBookListener.class);

    @Autowired
    private BasicDeployTaskService basicDeployTaskService;

    @Autowired
    private AmqpTemplate cloudAmqpTemplate;

    @Autowired
    @Lazy
    private AppDeployCallbackLogMessageHandler appDeployCallbackLogMessageHandler;

    @Autowired
    private BasicCodeService codeService;

    /**
     * @param appDeployEvent
     */
    @Async("cloudExecutor")
    @Transactional(rollbackFor = Exception.class)
    @TransactionalEventListener(classes = AnsiblePlaybookEvent.class, fallbackExecution = true)
    public void onEvent(AnsiblePlaybookEvent appDeployEvent) {
        AnsiblePlayBook source = appDeployEvent.getSource();
        if (source != null && logger.isInfoEnabled()) {


        }

        AnsiblePlayBook ansiblePlaybook = appDeployEvent.getSource();

        try {
            String componentId = ansiblePlaybook.getComponentId();
            if (StringUtil.isNullOrEmpty(componentId)) {
                componentId = AnsibleMqConfig.ANSIBLE_PLATFORM_RCLINK_ID;
            }

            String ansibleExchange = AnsibleMqConfig.ANSIBLE_EXCHANGE_PREFIX + componentId;
            String routingKey = componentId + AnsibleMqConfig.ANSIBLE_RUN_ROUTING_KEY_INCOMPLETE
                    + AnsibleTaskTypeConfig.AUTO_OPS_SCRIPT;
            //发送消息执行
            if (StringUtils.equals(componentId, AnsibleMqConfig.ANSIBLE_PLATFORM_RCLINK_ID)) {
                cloudAmqpTemplate.convertAndSend(ansibleExchange, routingKey,
                                                 HttpClientUtil.transBean2Map(ansiblePlaybook));
            } else {

                MQHelper.gatewaySendMessage(ansibleExchange, routingKey, HttpClientUtil.transBean2Map(ansiblePlaybook));
            }



        } catch (RetryException e) {
            logger.error("AnsiblePlayBookListener--RetryException-->> {} ", Throwables.getStackTraceAsString(e));

            String log = MessageUtil.getEndLogMessage(AnsibleMessageFlag.NORMAL_FAILED_MESSAGE_FLAG, e.getMessage());
            CallbakLog callbakLog = new CallbakLog(appDeployEvent.getTaskId().toString(), log, CallbakLog.Type.APP);

            appDeployCallbackLogMessageHandler.execute(callbakLog);

            // set task to failure
            deployAppTask(appDeployEvent.getTaskId(), DeployTaskStatus.FAILURE, e.getMessage());
            return;

        } catch (Exception e) {
            logger.error(Throwables.getStackTraceAsString(e));
            String log = MessageUtil.getEndLogMessage(AnsibleMessageFlag.NORMAL_FAILED_MESSAGE_FLAG, e.getMessage());
            CallbakLog callbakLog = new CallbakLog(appDeployEvent.getTaskId().toString(), log, CallbakLog.Type.APP);

            appDeployCallbackLogMessageHandler.execute(callbakLog);
            // set task to failure
            deployAppTask(appDeployEvent.getTaskId(), DeployTaskStatus.FAILURE, e.getMessage());
            return;
        }
        deployAppTask(appDeployEvent.getTaskId(), DeployTaskStatus.RUNNING, "");
    }

    private DeployTask deployAppTask(String taskId, String status, String failureReason) {
        DeployTask deployTask = new DeployTask();
        // setup host task
        deployTask.setId(taskId);
        deployTask.setStatus(status);
        deployTask.setStatusName(codeService.getTaskStatusName(deployTask.getStatus()));
        if (status.equals(DeployTaskStatus.FAILURE)) {
            deployTask.setEndDate(DateTime.now().toDate());
            deployTask.setFailureReason(failureReason);
        } else {
            deployTask.setStartDate(DateTime.now().toDate());
        }

        this.basicDeployTaskService.updateByPrimaryKeySelective(deployTask);

        return deployTask;
    }
}
