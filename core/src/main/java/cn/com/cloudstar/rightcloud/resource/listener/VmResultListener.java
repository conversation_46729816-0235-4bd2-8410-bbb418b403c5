/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.listener;

import cn.com.cloudstar.rightcloud.adapter.core.MQException;
import cn.com.cloudstar.rightcloud.adapter.core.MQHelper;
import cn.com.cloudstar.rightcloud.adapter.pojo.image.result.ImageDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.Port;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.GpuGroup;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmDisk;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmNic;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmSnapshotCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.*;
import cn.com.cloudstar.rightcloud.basic.data.platform.CloudClientFactory;
import cn.com.cloudstar.rightcloud.basic.data.pojo.base.Base;
import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnv;
import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnvAccount;
import cn.com.cloudstar.rightcloud.basic.data.pojo.deploy.DeployTask;
import cn.com.cloudstar.rightcloud.basic.data.pojo.deploy.HostDepoyEvent;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.*;
import cn.com.cloudstar.rightcloud.basic.data.pojo.snapshot.ResSnapshot;
import cn.com.cloudstar.rightcloud.basic.data.pojo.user.User;
import cn.com.cloudstar.rightcloud.basic.data.service.code.BasicCodeService;
import cn.com.cloudstar.rightcloud.basic.data.service.deploy.BasicDeployTaskService;
import cn.com.cloudstar.rightcloud.basic.data.service.res.BasicResActionLogService;
import cn.com.cloudstar.rightcloud.basic.data.service.user.BasicUserService;
import cn.com.cloudstar.rightcloud.common.constants.BillingConstants;
import cn.com.cloudstar.rightcloud.common.constants.BillingConstants.ChargeType;
import cn.com.cloudstar.rightcloud.common.constants.MailTemplateConstants;
import cn.com.cloudstar.rightcloud.common.constants.WebConstants.PowerStatus;
import cn.com.cloudstar.rightcloud.common.constants.ansible.AnsibleMessageFlag;
import cn.com.cloudstar.rightcloud.common.constants.ansible.AnsibleMqConfig;
import cn.com.cloudstar.rightcloud.common.constants.ansible.AnsibleServerMethod;
import cn.com.cloudstar.rightcloud.common.constants.ansible.AnsibleTaskTypeConfig;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.constants.res.status.*;
import cn.com.cloudstar.rightcloud.common.constants.res.type.*;
import cn.com.cloudstar.rightcloud.common.constants.status.CloudPhysicalHostPoolAllocStatus;
import cn.com.cloudstar.rightcloud.common.constants.status.DeployTaskStatus;
import cn.com.cloudstar.rightcloud.common.constants.status.SelfServiceInstanceStatus;
import cn.com.cloudstar.rightcloud.common.constants.type.*;
import cn.com.cloudstar.rightcloud.common.enums.ReqSource;
import cn.com.cloudstar.rightcloud.common.enums.ResVmExtEnum;
import cn.com.cloudstar.rightcloud.common.enums.resource.ResourceOperateEnum;
import cn.com.cloudstar.rightcloud.common.enums.resource.ResourceTypeEnum;
import cn.com.cloudstar.rightcloud.common.exception.BizException;
import cn.com.cloudstar.rightcloud.common.exception.RetryException;
import cn.com.cloudstar.rightcloud.common.exception.resolver.CloudErrorResolver;
import cn.com.cloudstar.rightcloud.common.mongo.MongoUtil;
import cn.com.cloudstar.rightcloud.common.pojo.ResImageInfo;
import cn.com.cloudstar.rightcloud.common.redis.JedisUtil;
import cn.com.cloudstar.rightcloud.common.schedule.helper.ScheduleHelper;
import cn.com.cloudstar.rightcloud.common.util.*;
import cn.com.cloudstar.rightcloud.common.util.json.JsonUtil;
import cn.com.cloudstar.rightcloud.common.websocket.ServerMsg;
import cn.com.cloudstar.rightcloud.common.websocket.ServerMsgPublisher;
import cn.com.cloudstar.rightcloud.common.websocket.annonation.Message;
import cn.com.cloudstar.rightcloud.common.websocket.annonation.MessageParam;
import cn.com.cloudstar.rightcloud.common.websocket.support.OperateEnum;
import cn.com.cloudstar.rightcloud.common.websocket.support.ServerMsgType;
import cn.com.cloudstar.rightcloud.core.annotation.log.LogMethod;
import cn.com.cloudstar.rightcloud.core.annotation.log.LogParam;
import cn.com.cloudstar.rightcloud.core.pojo.dto.event.TaskCtrlEvent;
import cn.com.cloudstar.rightcloud.core.pojo.dto.gpu.ResGpuGroup;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResChangeRecord;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResVpcPortSecurityGroup;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicInfoUtil;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicWebUtil;
import cn.com.cloudstar.rightcloud.remote.api.monitor.service.AlarmRuleTargetRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.monitor.service.BasicMonitorRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.cloud.CloudEnvAlloc;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.cloud.CloudPhysicalHostPool;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.engine.CallbakLog;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.*;
import cn.com.cloudstar.rightcloud.remote.api.pojo.system.maintenance.order.request.ServiceOrderBasicResourceRelationParams;
import cn.com.cloudstar.rightcloud.remote.api.pojo.system.system.LogInfo;
import cn.com.cloudstar.rightcloud.remote.api.system.service.maintainance.order.ServiceInstTargetRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.system.service.maintainance.order.ServiceOrderBasicResourceRelationRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.system.service.message.BusinessNotificationRemoteService;
import cn.com.cloudstar.rightcloud.resource.dao.env.CloudEnvAccountMapper;
import cn.com.cloudstar.rightcloud.resource.dao.env.CloudEnvAllocMapper;
import cn.com.cloudstar.rightcloud.resource.dao.env.CloudEnvMapper;
import cn.com.cloudstar.rightcloud.resource.dao.host.ResHostMapper;
import cn.com.cloudstar.rightcloud.resource.dao.image.ResImageMapper;
import cn.com.cloudstar.rightcloud.resource.dao.lb.ResLbBackendMapper;
import cn.com.cloudstar.rightcloud.resource.dao.network.*;
import cn.com.cloudstar.rightcloud.resource.dao.security.ResSecurityGroupMapper;
import cn.com.cloudstar.rightcloud.resource.dao.server.ResChangeRecordMapper;
import cn.com.cloudstar.rightcloud.resource.dao.server.ResVmMapper;
import cn.com.cloudstar.rightcloud.resource.dao.server.ResVmNetcardMapper;
import cn.com.cloudstar.rightcloud.resource.dao.snapshot.ResSnapshotMapper;
import cn.com.cloudstar.rightcloud.resource.dao.storage.ResStorageMapper;
import cn.com.cloudstar.rightcloud.resource.dao.storage.ResVdHostMapper;
import cn.com.cloudstar.rightcloud.resource.dao.storage.ResVdMapper;
import cn.com.cloudstar.rightcloud.resource.dao.storage.ResVolumeTypeMapper;
import cn.com.cloudstar.rightcloud.resource.dao.zone.ResZoneMapper;
import cn.com.cloudstar.rightcloud.resource.engine.event.callback.handler.log.HostDeployCallbackLogMessageHandler;
import cn.com.cloudstar.rightcloud.resource.notify.BizNotify;
import cn.com.cloudstar.rightcloud.resource.service.gpu.ResGpuGroupService;
import cn.com.cloudstar.rightcloud.resource.service.redundance.core.ServiceInstTargetService;
import cn.com.cloudstar.rightcloud.resource.service.server.*;
import cn.com.cloudstar.rightcloud.resource.service.thirdpartyservice.FortressService;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.base.Throwables;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.joda.time.DateTime;
import org.joda.time.LocalDateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * Created by tono_ on 2016/8/3.
 */
@Component
public class VmResultListener {

    private static final Logger logger = LoggerFactory.getLogger(VmResultListener.class);

    private static final String MONITOR_KEY = "monitor";

    private static final String SYNC_ENV_LOCK = "sync:env:%s:lock";
    private static final String SYNC_ENV_VM_LOCK = "sync:env:vm:%s:lock";

    @Autowired
    private ResVmMapper resVmMapper;

    @Autowired
    private ResVmTypeService resVmTypeService;

    @Autowired
    private ResHostMapper resHostMapper;

    @Autowired
    private ResVdMapper resVdMapper;

    @Autowired
    private ResVmService resVmService;

    @Autowired
    private NetworkIpMapper networkIpMapper;

    @Autowired
    private ResStorageMapper resStorageMapper;

    @Autowired
    private BasicResActionLogService basicResActionLogService;

    @Autowired
    private ResFloatingIpMapper resFloatingIpMapper;

    @Autowired
    private ResVolumeTypeMapper resVolumeTypeMapper;

    @Autowired
    private ResVpcMapper resVpcMapper;

    @Autowired
    private NetworkMapper networkMapper;

    @Autowired
    private ResSnapshotMapper resSnapshotMapper;

    @Autowired
    private ResVmNetcardMapper resVmNetcardMapper;

    @Autowired
    private BasicDeployTaskService basicDeployTaskService;

    @Autowired
    private CloudEnvMapper cloudEnvMapper;

    @Autowired
    private ResChangeRecordMapper resChangeRecordMapper;

    @Autowired
    private ResImageMapper resImageMapper;

    @Autowired
    private ResSecurityGroupMapper resSecurityGroupMapper;

    @Autowired
    private ResVmExtService resVmExtService;

    @Autowired
    private ResVdHostMapper resVdHostMapper;

    @Autowired
    private ResZoneMapper resZoneMapper;

    @Autowired
    private ResVpcPortMapper resVpcPortMapper;

    @Autowired
    private ResLbBackendMapper resLbBackendMapper;

    @Autowired
    @Lazy
    private HostDeployCallbackLogMessageHandler hostDeployCallbackLogMessageHandler;

    @Autowired
    private CloudEnvAllocMapper cloudEnvAllocMapper;

    @Autowired
    private CloudEnvAccountMapper cloudEnvAccountMapper;

    @Autowired
    private BasicCodeService codeService;

    @Autowired
    private CloudPhysicalHostPoolService cloudPhysicalHostPoolService;

    @Autowired
    private VmTypeChangeService vmTypeChangeService;

    @Autowired
    private ResVpcPortSecurityGroupMapper resVpcPortSecurityGroupMapper;

    @Autowired
    private ResGpuGroupService resGpuGroupService;

    @DubboReference
    private ServiceInstTargetRemoteService serviceInstTargetRemoteService;

    @Autowired
    private ServiceInstTargetService serviceInstTargetService;

    @DubboReference
    private ServiceOrderBasicResourceRelationRemoteService serviceOrderBasicResourceRelationRemoteService;

    @DubboReference
    private BusinessNotificationRemoteService businessNotificationRemoteService;

    @Autowired
    private BasicUserService basicUserService;

    @DubboReference
    private BasicMonitorRemoteService basicMonitorRemoteService;

    @DubboReference
    private AlarmRuleTargetRemoteService alarmRuleTargetRemoteService;

    @Autowired
    private FortressService fortressService;

    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#vmSnapshotCreateResult.snapshotId", resourceType = ResourceTypeEnum.SNAPSHOT, opUser = "#vmSnapshotCreateResult.opUser", operate = ResourceOperateEnum.CREATE, success = "#vmSnapshotCreateResult.success", orgSid = "#vmSnapshotCreateResult.orgSid")
    @Message(refKey = "#vmSnapshotCreateResult.snapshotId", envId = "#vmSnapshotCreateResult.cloudEnvId", msgType = ServerMsgType.SNAPSHOT, opUser = "#vmSnapshotCreateResult.opUser", operate = OperateEnum.CREATE, success = "#vmSnapshotCreateResult.success", refNameKey = "#vmSnapshotCreateResult.snapshotName", errorMsg = "#vmSnapshotCreateResult.errMsg")
    @BizNotify
    public void handleMessage(
            @LogParam("vmSnapshotCreateResult") @MessageParam("vmSnapshotCreateResult") VmSnapshotCreateResult vmSnapshotCreateResult) {
        logger.info("创建云主机快照回调 | 回调参数 ： {}", JsonUtil.toJson(vmSnapshotCreateResult));
        ResVm resVm = null;
        try {
            ResSnapshot resSnapshot = resSnapshotMapper.selectByPrimaryKey(vmSnapshotCreateResult.getSnapshotId());
            resVm = resVmMapper.selectByPrimaryKey(resSnapshot.getResVmId());
            if (vmSnapshotCreateResult.isSuccess()) {
                BasicWebUtil.prepareUpdateParams(resSnapshot);
                resSnapshot.setStatus(SnapshotStatus.SUCCESS);
                resSnapshot.setUuid(vmSnapshotCreateResult.getUuid());
                resSnapshot.setSnapshotSize(vmSnapshotCreateResult.getSnapshotSize());
                //运营特殊逻辑
                if (ReqSource.CLOUD_BOSS.name().equals(vmSnapshotCreateResult.getReqSource())) {
                    if (vmSnapshotCreateResult.getPeriod() != null && BillingConstants.ChargeType.PRE_PAID.equals(
                            resSnapshot.getChargeType())) {
                        resSnapshot.setEndTime(
                                DateUtil.offsetMonth(LocalDateTime.now().toDate(), vmSnapshotCreateResult.getPeriod()));
                    }
                }
                if (CloudEnvType.VMWARE.equals(vmSnapshotCreateResult.getProviderType())) {
                    Object instanceId = vmSnapshotCreateResult.getOptions().get("instanceId");
                    if (Objects.nonNull(instanceId)) {
                        processDisk(instanceId, vmSnapshotCreateResult.getDisks());
                    }
                }
            } else {
                BasicWebUtil.prepareUpdateParams(resSnapshot);
                resSnapshot.setStatus(SnapshotStatus.FAILURE);
            }
            resSnapshotMapper.updateByPrimaryKeySelective(resSnapshot);
        } catch (Exception e) {
            logger.error(e.getMessage());
        } finally {
            try {
                VmSnapshotCreate snapshotCreate = null;
                String snapListKey = "vm:snapshot:create:" + resVm.getId();
                do {
                    String json = JedisUtil.instance().lpop(snapListKey);
                    if (StringUtil.isNullOrEmpty(json)) {
                        snapshotCreate = null;
                        break;
                    }
                    if (JSONUtil.isJson(json)) {
                        snapshotCreate = JSONUtil.toBean(json, VmSnapshotCreate.class);
                    }
                } while (Objects.nonNull(snapshotCreate) && vmSnapshotCreateResult.getSnapshotId()
                                                                                  .equals(snapshotCreate.getSnapshotId()));
                if (Objects.nonNull(snapshotCreate)) {
                    logger.info("虚拟机:[{}] 快照创建队列[消费]，剩余数量:{}", resVm.getInstanceName(),
                                JedisUtil.instance().countList(snapListKey));
                    MQHelper.sendMessage(snapshotCreate);
                } else {
                    logger.info("虚拟机:[{}] 快照创建完成，清空redis数据", resVm.getInstanceName());
                    JedisUtil.instance().del(snapListKey);
                }
            } catch (MQException e) {
                logger.error(e.getMessage(), e);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#vmSnapshotDeleteResult.snapshotId", resourceType = ResourceTypeEnum.SNAPSHOT, opUser = "#vmSnapshotDeleteResult.opUser", operate = ResourceOperateEnum.DELETE, success = "#vmSnapshotDeleteResult.success", orgSid = "#vmSnapshotDeleteResult.orgSid")
    @Message(refKey = "#vmSnapshotDeleteResult.snapshotId", envId = "#vmSnapshotDeleteResult.cloudEnvId", msgType = ServerMsgType.SNAPSHOT, opUser = "#vmSnapshotDeleteResult.opUser", operate = OperateEnum.DELETE, success = "#vmSnapshotDeleteResult.success", refNameKey = "#vmSnapshotDeleteResult.snapshotName", errorMsg = "#vmSnapshotDeleteResult.errMsg")
    public void handleMessage(
            @LogParam("vmSnapshotDeleteResult") @MessageParam("vmSnapshotDeleteResult") VmSnapshotDeleteResult vmSnapshotDeleteResult) {
        logger.info("删除云主机快照回调 | 回调参数 ： {}", JsonUtil.toJson(vmSnapshotDeleteResult));
        try {
            ResSnapshot resSnapshot = resSnapshotMapper.selectByPrimaryKey(vmSnapshotDeleteResult.getSnapshotId());
            if (vmSnapshotDeleteResult.isSuccess()) {
                resSnapshotMapper.deleteByPrimaryKey(vmSnapshotDeleteResult.getSnapshotId());

                if (CloudEnvType.VMWARE.equals(vmSnapshotDeleteResult.getProviderType())) {
                    Object instanceId = vmSnapshotDeleteResult.getOptions().get("instanceId");
                    if (Objects.nonNull(instanceId)) {
                        processDisk(instanceId, vmSnapshotDeleteResult.getVmDisks());
                    }
                }
            } else {
                Map<String, Object> options = vmSnapshotDeleteResult.getOptions();
                resSnapshot.setStatus(options.get("originStatus").toString());
                resSnapshotMapper.updateByPrimaryKeySelective(resSnapshot);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#vmSnapshotRevertResult.snapshotId", resourceType = ResourceTypeEnum.SNAPSHOT, opUser = "#vmSnapshotRevertResult.opUser", operate = ResourceOperateEnum.ROLL_BACK, success = "#vmSnapshotRevertResult.success", orgSid = "#vmSnapshotRevertResult.orgSid")
    @Message(refKey = "#vmSnapshotRevertResult.snapshotId", envId = "#vmSnapshotRevertResult.cloudEnvId", msgType = ServerMsgType.SNAPSHOT, opUser = "#vmSnapshotRevertResult.opUser", operate = OperateEnum.RECOVER, success = "#vmSnapshotRevertResult.success", refNameKey = "#vmSnapshotRevertResult.vmName", errorMsg = "#vmSnapshotRevertResult.errMsg")
    public void handleMessage(
            @LogParam("vmSnapshotRevertResult") @MessageParam("vmSnapshotRevertResult") VmSnapshotRevertResult vmSnapshotRevertResult) {
        logger.info("还原云主机快照回调 | 回调参数 ： {}", JsonUtil.toJson(vmSnapshotRevertResult));
        try {
            ResSnapshot resSnapshot = resSnapshotMapper.selectByPrimaryKey(vmSnapshotRevertResult.getSnapshotId());
            resSnapshot.setStatus(SnapshotStatus.SUCCESS);
            resSnapshotMapper.updateByPrimaryKeySelective(resSnapshot);

            Object instanceId = vmSnapshotRevertResult.getOptions().get("instanceId");
            if (Objects.nonNull(instanceId)) {
                if (!CloudEnvType.FUSIONCOMPUTE.equals(vmSnapshotRevertResult.getProviderType())) {
                    processDisk(instanceId, vmSnapshotRevertResult.getVmDisks());
                }

                // 设置实例的状态
                ResVm host = new ResVm();
                host.setId(instanceId.toString());
                if (Objects.nonNull(vmSnapshotRevertResult.getPowerState())) {
                    if ("poweredOff".equalsIgnoreCase(vmSnapshotRevertResult.getPowerState())) {
                        host.setStatus(ResVmStatus.STOPPED);
                    } else if ("poweredOn".equalsIgnoreCase(vmSnapshotRevertResult.getPowerState())) {
                        host.setStatus(ResVmStatus.RUNNING);
                    } else if ("suspended".equalsIgnoreCase(vmSnapshotRevertResult.getPowerState())) {
                        host.setStatus(ResVmStatus.SUSPENDED);
                    }
                }
                this.resVmMapper.updateByPrimaryKeySelective(host);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#snapshotUpdateResult.snapshotId", resourceType = ResourceTypeEnum.SNAPSHOT, opUser = "#snapshotUpdateResult.opUser", operate = ResourceOperateEnum.MODIFY, success = "#snapshotUpdateResult.success", orgSid = "#snapshotUpdateResult.orgSid")
    @Message(refKey = "#snapshotUpdateResult.snapshotId", envId = "#snapshotUpdateResult.cloudEnvId", msgType = ServerMsgType.SNAPSHOT, opUser = "#snapshotUpdateResult.opUser", operate = OperateEnum.MODIFY, success = "#snapshotUpdateResult.success", refNameKey = "#snapshotUpdateResult.name", errorMsg = "#snapshotUpdateResult.errMsg")
    public void handleMessage(
            @LogParam("snapshotUpdateResult") @MessageParam("snapshotUpdateResult") VmSnapshotUpdateResult snapshotUpdateResult) {
        logger.info("编辑快照回调 | 回调参数 ： {}", JsonUtil.toJson(snapshotUpdateResult));
        try {
            if (snapshotUpdateResult.isSuccess()) {
                ResSnapshot update = new ResSnapshot();
                update.setName(snapshotUpdateResult.getName());
                update.setDescription(snapshotUpdateResult.getDescription());
                update.setId(snapshotUpdateResult.getSnapshotId());
                resSnapshotMapper.updateByPrimaryKeySelective(update);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#vmCloneResult.id", resourceType = ResourceTypeEnum.VM, opUser = "#vmCloneResult.opUser", operate = ResourceOperateEnum.CLONE, success = "#vmCloneResult.success", orgSid = "#vmCloneResult.orgSid")
    @Message(refKey = "#vmCloneResult.id", envId = "#vmCloneResult.cloudEnvId", msgType = ServerMsgType.VM, opUser = "#vmCloneResult.opUser", operate = OperateEnum.CLONE, success = "#vmCloneResult.success", refNameKey = "#vmCloneResult.vmName", errorMsg = "#vmCloneResult.errMsg")
    public void handleMessage(@LogParam("vmCloneResult") @MessageParam("vmCloneResult") VmCloneResult vmCloneResult) {
        logger.info("克隆云主机回调 | 回调参数 ： {}", JsonUtil.toJson(vmCloneResult));
        String eventStatus = DeployTaskStatus.SUCCESS;
        String failReason = null;
        ResVm resVm = null;
        Map<String, Object> options = vmCloneResult.getOptions();
        try {
            final String sid = vmCloneResult.getId();
            resVm = this.resVmMapper.selectByPrimaryKey(sid);
            updateElasticHistory(resVm, vmCloneResult.isSuccess());
            if (vmCloneResult.isSuccess()) {
                // save to db
                if (CollectionUtil.isNotEmpty(vmCloneResult.getNics())) {
                    Map<String, String> vsPortGroupMap = new HashMap<>();
                    if (!CollectionUtils.isEmpty(vmCloneResult.getOrignNics())) {
                        // key name value id
                        for (VmNic orignNic : vmCloneResult.getOrignNics()) {
                            if (!Strings.isNullOrEmpty(orignNic.getVirSwitch())) {
                                vsPortGroupMap.put(orignNic.getVirSwitch(), orignNic.getPortGroupId());
                            } else {
                                vsPortGroupMap.put(orignNic.getPort(), orignNic.getPortGroupId());
                            }
                        }
                    }

                    // 实例与网卡的关系
                    vmCloneResult.getNics().forEach(nic -> {
                        ResVmNetcard resVmNetcard = new ResVmNetcard();
                        resVmNetcard.setMacAddress(nic.getMac());
                        String ipAddress = "";
                        if (!Strings.isNullOrEmpty(nic.getPrivateIp())) {
                            ipAddress = nic.getPrivateIp();
                        } else if (!Strings.isNullOrEmpty(nic.getPublicIp())) {
                            ipAddress = nic.getPublicIp();
                        }
                        resVmNetcard.setIpAddress(ipAddress);

                        resVmNetcard.setResVmId(sid);
                        resVmNetcard.setResVsPortGroupId(vsPortGroupMap.get(nic.getPort()));

                        resVmNetcardMapper.insertSelective(resVmNetcard);
                    });

                    List<String> privateIps = vmCloneResult.getNics()
                                                           .stream()
                                                           .filter(vmNic -> !Strings.isNullOrEmpty(
                                                                   vmNic.getPrivateIp()))
                                                           .map(VmNic::getPrivateIp)
                                                           .collect(Collectors.toList());
                    List<String> publicIps = vmCloneResult.getNics()
                                                          .stream()
                                                          .filter(vmNic -> !Strings.isNullOrEmpty(vmNic.getPublicIp()))
                                                          .map(VmNic::getPublicIp)
                                                          .collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(privateIps)) {
                        resVm.setInnerIp(Joiner.on(",").join(privateIps));
                    }
                    if (!CollectionUtils.isEmpty(publicIps)) {
                        resVm.setPublicIp(Joiner.on(",").skipNulls().join(publicIps));
                    }
                } else if (!vmCloneResult.getNetworkChange()) {
                    // 网卡信息未改变
                    List<ResVmNetcard> resVmNetcards = resVmNetcardMapper.selectByResVmId(resVm.getCloneSourceId());
                    for (ResVmNetcard resVmNetcard : resVmNetcards) {
                        resVmNetcard.setResVmId(resVm.getId());
                        resVmNetcardMapper.insertSelective(resVmNetcard);
                    }
                    List<String> ipList = resVmNetcards.stream()
                                                       .filter(t -> !Strings.isNullOrEmpty(t.getIpAddress()))
                                                       .map(ResVmNetcard::getIpAddress)
                                                       .collect(Collectors.toList());
                    resVm.setInnerIp(Joiner.on(",").skipNulls().join(ipList));

                    // 附加信息表，copy原数据
                    List<ResVmExt> resVmExts = resVmExtService.selectByParams(
                            new Criteria("instanceId", resVm.getCloneSourceId()).put("type",
                                                                                     ResVmExtEnum.SUBNET.getType()));
                    for (ResVmExt resVmExt : resVmExts) {
                        resVmExt.setInstanceId(resVm.getId());
                    }
                    resVmExtService.insertMulti(resVmExts);
                }

                resVm.setInstanceName(vmCloneResult.getVmName());
                resVm.setStatusInfo(" ");
                if (vmCloneResult.getPowerStatus().equals(Boolean.FALSE)) {
                    resVm.setStatus(ResVmStatus.STOPPED);
                } else {
                    resVm.setStatus(ResVmStatus.RUNNING);
                }
                resVm.setInstanceId(vmCloneResult.getUuid());
                resVm.setUri(vmCloneResult.getUri());
                resVm.setUrn(vmCloneResult.getUrn());
                resVm.setStartTime(Calendar.getInstance().getTime());
                if (!Objects.isNull(vmCloneResult.getFloatingIp()) && !Objects.isNull(
                        vmCloneResult.getFloatingIp().getIp())) {
                    resVm.setPublicIp(vmCloneResult.getFloatingIp().getIp());
                }
                BasicWebUtil.prepareUpdateParams(resVm, resVm.getCreatedBy());

                //设置宿主机ID
                Criteria criteria = new Criteria();
                criteria.put("hostName", vmCloneResult.getHostName());
                criteria.put("parentTopologySid", resVm.getCloudEnvId());
                List<ResHost> resHosts = resHostMapper.selectByParams(criteria);
                if (!CollectionUtils.isEmpty(resHosts)) {
                    resVm.setAllocateResHostSid(resHosts.get(0).getResHostSid());
                }

                ResVm cloneSourceResVm = resVmMapper.selectByPrimaryKey(resVm.getCloneSourceId());

                this.resVmMapper.updateByPrimaryKeySelective(resVm);
                //系统盘ID
                criteria.clear();
                criteria.put("parentTopologySid", resVm.getCloudEnvId());
                List<ResStorage> resStorages = resStorageMapper.selectByParams(criteria);
                Map<String, ResStorage> storageWithVolumeTypeId = resStorages.stream()
                                                                             .collect(Collectors.toMap(
                                                                                     ResStorage::getStorageName,
                                                                                     Function.identity(),
                                                                                     (key1, key2) -> key1));
                CloudEnv cloudEnv = cloudEnvMapper.selectByPrimaryKey(resVm.getCloudEnvId());

                // data disk
                List<VmDisk> disks = vmCloneResult.getDisks();
                if (disks != null && disks.size() > 0) {
                    for (VmDisk disk : disks) {
                        ResVd resVd = new ResVd();
                        resVd.setVdName(disk.getName());
                        if (!Strings.isNullOrEmpty(disk.getSize())) {
                            resVd.setAllocateDiskSize(Long.parseLong(disk.getSize()));
                        }
                        resVd.setStoragePurpose(disk.getType());
                        resVd.setUuid(disk.getUuid());
                        resVd.setLogicVolume(disk.getUuid());
                        resVd.setPath(disk.getPath());
                        resVd.setDataStoreName(disk.getLocation());
                        resVd.setUri(disk.getUri());
                        resVd.setUrn(disk.getUrn());

                        resVd.setCloudEnvId(resVm.getCloudEnvId());
                        resVd.setResVmId(resVm.getId());
                        resVd.setStatus(ResVdStatus.NORMAL);
                        resVd.setStartTime(new Date());
                        resVd.setReleaseModeBoolean(disk.getDeleteWithInstance());
                        if (storageWithVolumeTypeId.containsKey(disk.getLocation())) {
                            if (CloudEnvType.VMWARE.equals(cloudEnv.getCloudEnvType())
                                    || CloudEnvType.FUSIONCOMPUTE.equals(cloudEnv.getCloudEnvType())) {
                                ResStorage resStorage = storageWithVolumeTypeId.get(disk.getLocation());
                                resVd.setAllocateResStorageSid(resStorage.getResStorageSid());
                                resVd.setDeviceName(resStorage.getStorageName());
                                resVd.setVolumeTypeId(resStorage.getResVolumeTypeId());
                            }
                        }
                        if (!Objects.isNull(resVm.getOrgSid())) {
                            resVd.setOrgSid(resVm.getOrgSid());
                        }
                        if (!Objects.isNull(resVm.getCloudDeploymentId())) {
                            resVd.setCloudDeploymentId(resVm.getCloudDeploymentId());
                        }
                        BasicWebUtil.prepareInsertParams(resVd, resVm.getCreatedBy());
                        this.resVdMapper.insertSelective(resVd);

                    }
                }
                // network ip
                List<NetworkIp> ips = this.networkIpMapper.selectByExample(
                        new Criteria("allocateTargetId", resVm.getId()));
                if (!CollectionUtils.isEmpty(ips)) {
                    NetworkIp networkIp = ips.get(0);
                    networkIp.setStatus(NetworkManagement.UNAVAILABLE);
                    BasicWebUtil.prepareUpdateParams(networkIp, resVm.getCreatedBy());
                    this.networkIpMapper.updateByPrimaryKeySelective(networkIp);
                }

                // floating ip
                Criteria example = new Criteria();
                example.put("cloudEnvId", resVm.getCloudEnvId());
                example.put("ip", resVm.getPublicIp());
                example.put("statusNotEquals", NetworkStatus.DELETED);
                List<ResFloatingIp> floatingIps = resFloatingIpMapper.selectByParams(example);

                VmCreateResult vmCreateResult = new VmCreateResult();
                vmCreateResult.setFloatingIp(vmCloneResult.getFloatingIp());
                ResFloatingIp resFloatingIp = makeFloatingIp(floatingIps, resVm, vmCreateResult);
                if (!Objects.isNull(resFloatingIp) && !CollectionUtils.isEmpty(floatingIps)) {
                    BasicWebUtil.prepareUpdateParams(resFloatingIp, vmCloneResult.getOpUser());
                    resFloatingIpMapper.updateByPrimaryKeySelective(resFloatingIp);

                    basicResActionLogService.insertIntoActionLog(resFloatingIp.getId().toString(),
                                                                 vmCloneResult.getOpUser(),
                                                                 ResourceTypeEnum.FLOATING_IP,
                                                                 ResourceOperateEnum.ATTACH, Boolean.TRUE);

                } else if (!Objects.isNull(resFloatingIp)) {

                    // 随实例创建的弹性ip，付费方式默认为按量付费
                    if (CloudEnvType.OPEN_STACK.equals(cloudEnv.getCloudEnvType())) {
                        resFloatingIp.setChargeType(ChargeType.POST_PAID);
                    }

                    BasicWebUtil.prepareInsertParams(resFloatingIp, vmCloneResult.getOpUser());
                    resFloatingIpMapper.insertSelective(resFloatingIp);

                    basicResActionLogService.insertIntoActionLog(resFloatingIp.getId().toString(),
                                                                 vmCloneResult.getOpUser(),
                                                                 ResourceTypeEnum.FLOATING_IP,
                                                                 ResourceOperateEnum.CREATE, Boolean.TRUE);
                }

                //success message
                sendHostCreateSuccessPlatformNotification(resVm,
                                                          MailTemplateConstants.CLOUD_HOST_CREATE_SUCCESS_NOTIFI);

                // deploy agent
                CallbakLog callbakLog = new CallbakLog(options.get("taskId").toString(),
                                                       MessageUtil.getLogMessage("实例克隆成功."), CallbakLog.Type.HOST);
                if (vmCloneResult.getPowerStatus().equals(Boolean.TRUE)) {
                    logger.info("克隆云主机回调 | 开始纳管 ： {}", resVm.toString());

                    hostDeployCallbackLogMessageHandler.execute(callbakLog);

                    //卸载监控组件
                    if (StringUtil.isNotEmpty(resVm.getManagementAccount()) && StringUtil.isNotEmpty(
                            resVm.getManagemenPassword())) {
                        try {
                            unInstallAgent(resVm);
                        } catch (Exception e) {
                            logger.error("{} 监控组件卸载失败：{}", vmCloneResult.getId(), vmCloneResult.getErrMsg());
                        }
                    }
                }

                try {
                    fortressService.fortressAddDev(resVm);
                } catch (Exception e) {
                    logger.error("推送资源到堡垒机失败:", e);
                }
            } else {
                failReason = CloudErrorResolver.getErrorMsg(vmCloneResult.getErrMsg());
                if (resVm != null) {
                    resVm.setInstanceId(vmCloneResult.getUuid());
                    resVm.setStatusInfo(failReason);
                    resVm.setStatus(ResVmStatus.CREATE_FAILURE);
                    resVm.setManageStatus(ResVmManageStatus.DISCONNECT);
                    this.resVmMapper.updateByPrimaryKeySelective(resVm);
                    // network ip revert
                    NetworkIp networkIp = new NetworkIp();
                    networkIp.setAllocateTargetId(resVm.getId());
                    networkIp.setStatus(NetworkManagement.AVAILABLE);
                    this.networkIpMapper.updateStatusByAllocateTargetId(networkIp);

                    // floating ip revert
                    Criteria example = new Criteria();
                    example.put("cloudEnvId", resVm.getCloudEnvId());
                    example.put("ip", resVm.getPublicIp());
                    example.put("status", NetworkStatus.RESERVED);
                    List<ResFloatingIp> floatingIps = resFloatingIpMapper.selectByParams(example);
                    if (!CollectionUtils.isEmpty(floatingIps)) {
                        this.resFloatingIpMapper.unbandingFloatingIp(floatingIps.get(0).getId().intValue());
                    }

                    //send failed message
                    setHostCreateFailedPlatformNotification(resVm, failReason,
                                                            MailTemplateConstants.CLOUD_HOST_CREATE_FAILED_NOTIFI);


                    serviceInstTargetRemoteService.updateSelfInstByHostId(resVm.getId(),
                                                                          SelfServiceInstanceStatus.ERROR);
                }

                logger.error(vmCloneResult.getErrMsg());
                logger.error("{} 克隆实例失败：{}", vmCloneResult.getId(), vmCloneResult.getErrMsg());
                logger.error("克隆云主机回调 | 克隆实例失败");
                eventStatus = DeployTaskStatus.FAILURE;
            }
        } catch (Exception ex) {
            try {
                logger.error("克隆云主机回调异常 : {}", Throwables.getStackTraceAsString(ex));
                final String sid = vmCloneResult.getId();
                resVm = this.resVmMapper.selectByPrimaryKey(sid);
                failReason = ex.getMessage();
                if (resVm != null) {
                    resVm.setStatusInfo("系统错误，请联系管理员：" + failReason);
                    resVm.setStatus(ResVmStatus.CREATE_FAILURE);
                    resVm.setManageStatus(ResVmManageStatus.DISCONNECT);
                    this.resVmMapper.updateByPrimaryKeySelective(resVm);
                    // network ip revert
                    Criteria criteria = new Criteria("allocateTargetId", resVm.getId());
                    List<NetworkIp> networkIps = this.networkIpMapper.selectByExample(criteria);
                    if (!CollectionUtils.isEmpty(networkIps)) {
                        for (NetworkIp networkIp : networkIps) {
                            networkIp.setAllocateTargetId(null);
                            networkIp.setStatus(NetworkManagement.AVAILABLE);
                            BasicWebUtil.prepareInsertParams(networkIp, resVm.getCreatedBy());
                            this.networkIpMapper.updateByPrimaryKey(networkIp);
                        }
                    }

                    //send failed message
                    setHostCreateFailedPlatformNotification(resVm, failReason,
                                                            MailTemplateConstants.CLOUD_HOST_CREATE_FAILED_NOTIFI);

                }
                logger.error("{} 开机失败：{}", vmCloneResult.getId(), failReason);
                logger.error("克隆云主机回调 | 克隆实例失败");
                eventStatus = DeployTaskStatus.FAILURE;
            } catch (Exception e) {
                logger.error("克隆云主机回调异常 : {}", Throwables.getStackTraceAsString(e));
            }


        } finally {
            if (resVm == null) {
                resVm = new ResVm();
                resVm.setId(vmCloneResult.getSid());
            }
            // send error event
            TaskCtrlEvent<ResVm> ctrlEvent = new TaskCtrlEvent<>(resVm);
            ctrlEvent.setPlaybookId(options.get("playbookId").toString());
            ctrlEvent.setTaskId(options.get("taskId").toString());
            ctrlEvent.setTaskStatus(eventStatus);
            ctrlEvent.setFailureReason(failReason);
            SpringContextHolder.publishEvent(ctrlEvent);
        }

    }

    private void sendHostCreateSuccessPlatformNotification(ResVm resVm, String mailTemplateId) {
        if (resVm == null || !StringUtil.isNumeric(resVm.getOwnerId())) {
            return;
        }

        User user = this.basicUserService.selectByPrimaryKey(Long.parseLong(resVm.getOwnerId()));
        if (StringUtil.isNullOrEmpty(user)) {
            return;
        }

        Map<String, String> content = new HashMap<>(8);
        content.put("instanceName", resVm.getInstanceName());
        content.put("id", resVm.getId());
        content.put("cloudEnvName", resVm.getCloudEnvName());
        content.put("cpu", String.valueOf(resVm.getCpu()));
        content.put("memory", Objects.nonNull(resVm.getMemory()) ? String.valueOf(resVm.getMemory() / 1024.0) : null);
        StringBuilder sb = new StringBuilder();
        if (!Strings.isNullOrEmpty(resVm.getPublicIp())) {
            sb.append("(公)").append(resVm.getPublicIp());
        }
        if (!Strings.isNullOrEmpty(resVm.getInnerIp())) {
            if (sb.length() > 0) {
                sb.append("\n");
            }
            sb.append("(内)").append(resVm.getInnerIp());
        }
        content.put("ip", sb.toString());
        List<ResVd> resVds = resVmService.selectDiskInHost(
                new Criteria("resVmId", resVm.getId()).put("statusNotEquals", ResVdStatus.DELETED));
        content.put("disks", resVds.stream().map(ResVd::getVdName).collect(Collectors.joining(StrUtil.COMMA)));
        resVmService.setResVmExtInfo(resVm);
        Map<String, List<ResVmExt>> cloudHostExt = resVm.getCloudHostExt();
        List<ResVmExt> resVmExts = cloudHostExt.get(ResVmExtEnum.SECURITY_GROUP.getType());
        content.put("networkInfo", resVm.getNetworkInfo());
        if (CollectionUtil.isNotEmpty(resVmExts)) {
            content.put("securityGroups",
                        resVmExts.stream().map(ResVmExt::getSgName).collect(Collectors.joining(StrUtil.COMMA)));
        }
        businessNotificationRemoteService.sendCommonPlatformNotification(user.getUserSid(), mailTemplateId, content);
    }


    private void setHostCreateFailedPlatformNotification(ResVm resVm, String errMsg, String mailTemplateId) {
        if (Objects.isNull(resVm) || !StringUtil.isNumeric(resVm.getOwnerId())) {
            return;
        }

        User user = this.basicUserService.selectByPrimaryKey(Long.parseLong(resVm.getOwnerId()));
        if (StringUtil.isNullOrEmpty(user)) {
            return;
        }

        Map<String, String> content = new HashMap<>(4);
        content.put("instanceName", resVm.getInstanceName());
        content.put("id", resVm.getId());
        content.put("failReason", errMsg);
        businessNotificationRemoteService.sendCommonPlatformNotification(user.getUserSid(), mailTemplateId, content);
    }

    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#vmCreateResult.id", resourceType = ResourceTypeEnum.VM, opUser = "#vmCreateResult.opUser", operate = ResourceOperateEnum.CREATE, success = "#vmCreateResult.success", orgSid = "#vmCreateResult.orgSid")
    @Message(refKey = "#vmCreateResult.id", envId = "#vmCreateResult.cloudEnvId", msgType = ServerMsgType.VM, opUser = "#vmCreateResult.opUser", operate = OperateEnum.CREATE, success = "#vmCreateResult.success", refNameKey = "#vmCreateResult.vmName", errorMsg = "#vmCreateResult.errMsg")
    @BizNotify
    public void handleMessage(
            @LogParam("vmCreateResult") @MessageParam("vmCreateResult") VmCreateResult vmCreateResult) {
        logger.info("创建云主机回调 | 回调参数 ： {}", JsonUtil.toJson(vmCreateResult));
        String eventStatus = DeployTaskStatus.SUCCESS;
        String failReason = null;
        ResVm resVm = null;
        Map<String, Object> options = vmCreateResult.getOptions();

        try {
            final String sid = vmCreateResult.getId();
            AtomicReference<ResVm> atomicReference = new AtomicReference<>(null);
            RetryUtil.retry(3, 5, TimeUnit.SECONDS, false, () -> {
                ResVm rm = this.resVmMapper.selectSimpleByPrimaryKeyForUpdate(sid);
                if (rm == null) {
                    throw new RetryException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1867841409));
                } else {
                    atomicReference.set(rm);
                }
            });
            resVm = atomicReference.get();
            if (null == resVm) {
                logger.warn("云主机:[{}] 没有找到!", sid);
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1781194848));
            }
            updateElasticHistory(resVm, vmCreateResult.isSuccess());
            if (vmCreateResult.isSuccess()) {
                // 更新 告警关联 instanceId
                updateAlarmRuleTarget(vmCreateResult.getId(), vmCreateResult.getUuid());

                List<VmNic> orignNics = vmCreateResult.getOrignNics();
                Map<String, String> vsPortGroupMap = new HashMap<>();
                if (!CollectionUtils.isEmpty(orignNics)) {
                    // key name value id
                    for (VmNic orignNic : orignNics) {
                        if (!Strings.isNullOrEmpty(orignNic.getVirSwitch())) {
                            vsPortGroupMap.put(orignNic.getVirSwitch(), orignNic.getPortGroupId());
                        } else {
                            vsPortGroupMap.put(orignNic.getPort(), orignNic.getPortGroupId());
                        }
                    }
                }

                CloudEnv cloudEnv = cloudEnvMapper.selectByPrimaryKey(resVm.getCloudEnvId());
                List<CloudEnvAlloc> allocList = cloudEnvAllocMapper.selectByExample(
                        new Criteria("cloudEnvId", cloudEnv.getId()));
                // 实例与网卡的关系
                if (CloudEnvType.CLOUDOS.equals(cloudEnv.getCloudEnvType()) || CloudEnvType.HCSO.equals(
                        cloudEnv.getCloudEnvType()) || CloudEnvType.OPEN_STACK.equals(cloudEnv.getCloudEnvType())) {
                    Long orgSid = resVm.getOrgSid();
                    String instanceId = resVm.getId();
                    String instanceName = resVm.getInstanceName();
                    String createdBy = resVm.getCreatedBy();
                    Long id = cloudEnv.getId();

                    List<Long> sgIds = Lists.newArrayList();
                    if (!CollectionUtils.isEmpty(vmCreateResult.getSecurityGroupNames())) {
                        Criteria criteria = new Criteria();
                        criteria.put("uuidIn", vmCreateResult.getSecurityGroupNames());
                        criteria.put("cloudEnvId", id);
                        List<ResSecurityGroup> resSecurityGroups = resSecurityGroupMapper.selectAllByParams(criteria);
                        sgIds.addAll(
                                resSecurityGroups.stream().map(ResSecurityGroup::getId).collect(Collectors.toList()));
                    }

                    Criteria criteria = new Criteria();
                    criteria.put("uuid", resVm.getZone());
                    criteria.put("cloudEnvId", id);
                    List<ResZone> resZoneList = resZoneMapper.selectByParams(criteria);
                    vmCreateResult.getNics().forEach(nic -> {
                        ResVpcPort resVpcPort = new ResVpcPort();
                        resVpcPort.setCloudEnvId(id);
                        resVpcPort.setUuid(nic.getPort());
                        resVpcPort.setOrgSid(orgSid);
                        resVpcPort.setDevice(instanceId);
                        resVpcPort.setFixedIp(nic.getPrivateIp());
                        if (CloudEnvType.CLOUDOS.equals(cloudEnv.getCloudEnvType())
                                && CloudEnvType.CLOUDOS_ADMIN.equals(cloudEnv.getCloudEnvType())) {
                            List<Port> resultPort = vmCreateResult.getPorts()
                                                                  .stream()
                                                                  .filter(p -> p.getId()
                                                                                .equalsIgnoreCase(nic.getPort()))
                                                                  .collect(Collectors.toList());
                            resVpcPort.setPortName(resultPort.get(0).getName());
                        } else {
                            resVpcPort.setPortName(nic.getName());
                        }
                        resVpcPort.setMacAddress(nic.getMac());
                        resVpcPort.setDeviceOwner("compute:" + resZoneList.get(0).getName());
                        resVpcPort.setStatus(NetworkStatus.ACTIVE);

                        Criteria netCriteria = new Criteria("uuid", nic.getNetId());
                        List<ResVpc> vpcList = Lists.newArrayList();
                        List<Network> networkList = Lists.newArrayList();
                        if (CloudEnvType.OPEN_STACK.equals(cloudEnv.getCloudEnvType())) {
                            // 处理 OpenStack 共享网络问题
                            List<CloudEnv> cloudEnvs = cloudEnvMapper.selectByCloudEnvAccountId(
                                    cloudEnv.getCloudEnvAccountId());
                            List<Long> cloudEnvIds = cloudEnvs.stream()
                                                              .map(CloudEnv::getId)
                                                              .collect(Collectors.toList());
                            netCriteria.put("cloudEnvIdIn", cloudEnvIds);
                            vpcList.addAll(resVpcMapper.selectByParams(netCriteria));
                            netCriteria.put("uuid", nic.getSubnetId());
                            networkList.addAll(networkMapper.selectByParams(netCriteria));
                        } else {
                            netCriteria.put("cloudEnvId", id);
                            vpcList.addAll(resVpcMapper.selectByParams(netCriteria));
                            netCriteria.put("uuid", nic.getSubnetId());
                            networkList.addAll(networkMapper.selectByParams(netCriteria));
                        }
                        if (!CollectionUtils.isEmpty(networkList)) {
                            resVpcPort.setSubnetId(networkList.get(0).getId().toString());
                        }
                        if (!CollectionUtils.isEmpty(vpcList)) {
                            resVpcPort.setVpcId(vpcList.get(0).getId());
                        }

                        resVpcPort.setPortName(Strings.nullToEmpty(resVpcPort.getPortName()));
                        BasicWebUtil.prepareInsertParams(resVpcPort, createdBy);
                        resVpcPortMapper.insertSelective(resVpcPort);

                        sgIds.forEach(sgId -> resVpcPortSecurityGroupMapper.insertSelective(
                                new ResVpcPortSecurityGroup(resVpcPort.getId(), sgId)));
                    });
                } else {
                    vmCreateResult.getNics().forEach(nic -> {
                        ResVmNetcard resVmNetcard = new ResVmNetcard();
                        resVmNetcard.setMacAddress(nic.getMac());
                        String ipAddress = "";
                        if (!Strings.isNullOrEmpty(nic.getPrivateIp())) {
                            ipAddress = nic.getPrivateIp();
                        } else if (!Strings.isNullOrEmpty(nic.getPublicIp())) {
                            ipAddress = nic.getPublicIp();
                        }
                        resVmNetcard.setIpAddress(ipAddress);

                        resVmNetcard.setResVmId(sid);
                        resVmNetcard.setResVsPortGroupId(vsPortGroupMap.get(nic.getPort()));

                        ResVmNetcard existResVmNetcard = resVmNetcardMapper.selectByPrimaryKey(resVmNetcard);
                        if (Objects.isNull(existResVmNetcard)) {
                            resVmNetcardMapper.insertSelective(resVmNetcard);
                        }
                    });
                }
                if (CloudEnvType.HUAWEICLOUD.equals(vmCreateResult.getProviderType())) {
                    Map<String, String> params = Maps.newHashMap();
                    params.put("key", "subnet");

                    CloudEnvAccount cloudEnvAccount = cloudEnvAccountMapper.selectByCloudEnvId(
                            vmCreateResult.getCloudEnvId());
                    if (Objects.nonNull(cloudEnvAccount)) {
                        params.put("companyId", cloudEnvAccount.getOrgSid().toString());
                    } else {
                        params.put("companyId", vmCreateResult.getOrgSid());
                    }

                    ScheduleHelper.manualSyncTask(vmCreateResult.getCloudEnvId(), params,
                                                  RequestContextUtil.getUserIdFromAccount(vmCreateResult.getOpUser()));
                }

                // save to db
                List<String> privateIps = vmCreateResult.getNics()
                                                        .stream()
                                                        .filter(vmNic -> !Strings.isNullOrEmpty(vmNic.getPrivateIp()))
                                                        .map(VmNic::getPrivateIp)
                                                        .collect(Collectors.toList());
                List<String> publicIps = vmCreateResult.getNics()
                                                       .stream()
                                                       .filter(vmNic -> !Strings.isNullOrEmpty(vmNic.getPublicIp()))
                                                       .map(VmNic::getPublicIp)
                                                       .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(privateIps)) {
                    resVm.setInnerIp(Joiner.on(",").join(privateIps));
                }
                if (!CollectionUtils.isEmpty(publicIps)) {
                    resVm.setPublicIp(Joiner.on(",").skipNulls().join(publicIps));
                }
                if (ServerType.SERVER.equals(resVm.getServerType())) {
                    resVm.setServerType(ServerType.SERVER_INSTANCE);
                }
                resVm.setInstanceName(vmCreateResult.getVmName());
                resVm.setStatusInfo(" ");
                resVm.setStatus(ResVmStatus.RUNNING);
                resVm.setInstanceId(vmCreateResult.getUuid());
                resVm.setZone(vmCreateResult.getZone());
                if (!Objects.isNull(vmCreateResult.getFloatingIp()) && !Strings.isNullOrEmpty(
                        vmCreateResult.getFloatingIp().getIp())) {
                    resVm.setPublicIp(vmCreateResult.getFloatingIp().getIp());
                }
                resVm.setStoppedMode(vmCreateResult.getStoppedMode());
                if (vmCreateResult.getSecurityGroupNames() != null
                        && vmCreateResult.getSecurityGroupNames().size() > 0) {
                    List<ResSecurityGroup> securityGroups = resSecurityGroupMapper.selectByParams(
                            new Criteria("nameIn", vmCreateResult.getSecurityGroupNames()).put("cloudEnvId",
                                                                                               resVm.getCloudEnvId()));
                    Map<String, Long> securityMapWithId = securityGroups.stream()
                                                                        .collect(Collectors.toMap(
                                                                                ResSecurityGroup::getName,
                                                                                o -> o.getId()));

                    resVmExtService.insertResVmExt(ResVmExtEnum.SECURITY_GROUP.getType(), resVm.getId(),
                                                   Lists.newArrayList(vmCreateResult.getSecurityGroupNames()),
                                                   securityMapWithId);
                }

                final GpuGroup gpuGroup = vmCreateResult.getGpuGroup();
                if (Objects.nonNull(gpuGroup)) {
                    final List<ResGpuGroup> resGpuGroups = resGpuGroupService.selectByParams(
                            new Criteria("cloudEnvId", resVm.getCloudEnvId()).put("urn", gpuGroup.getUrn()));
                    if (CollectionUtil.isNotEmpty(resGpuGroups)) {
                        final ResGpuGroup resGpuGroup = resGpuGroups.get(0);
                        resVmExtService.insertResVmExt(ResVmExtEnum.GPU_GROUP.getType(), resVm.getId(),
                                                       Lists.newArrayList(resGpuGroup.getId()), null,
                                                       String.valueOf(gpuGroup.getCount()));
                    }
                }

                // AWS创建windows后，通过接口获取的随机密码
                if (StringUtils.isNotBlank(vmCreateResult.getAdminPasswd())) {
                    resVm.setManagemenPassword(vmCreateResult.getAdminPasswd());
                }
                resVm.setUri(vmCreateResult.getUri());
                resVm.setUrn(vmCreateResult.getUrn());
                BasicWebUtil.prepareUpdateParams(resVm, resVm.getCreatedBy());
                // update platform
                if (CloudClientFactory.buildMQBean(resVm.getCloudEnvId(), Base.class).isRCLinkType()) {
                    resVm.setPlatform(StringUtil.nullToEmpty(vmCreateResult.getOptions().get("platform")));
                    resVm.setOsName(StringUtil.nullToEmpty(vmCreateResult.getOptions().get("osName")));
                }

                //设置宿主机ID
                Criteria criteria = new Criteria();
                criteria.put("hostName", vmCreateResult.getHostName());
                criteria.put("parentTopologySid", resVm.getCloudEnvId());
                List<ResHost> resHosts = resHostMapper.selectByParams(criteria);
                if (!CollectionUtils.isEmpty(resHosts)) {
                    resVm.setAllocateResHostSid(resHosts.get(0).getResHostSid());
                }

                resVm.setHasVmTools(vmCreateResult.getHasVmTools());
                String account = resVm.getManagementAccount();
                String password = resVm.getManagemenPassword();
                resVm.setCreatedOrgSid(resVm.getOrgSid());
                this.resVmMapper.updateByPrimaryKeySelective(resVm);
                resVm.setManagementAccount(account);
                resVm.setManagemenPassword(password);
                //系统盘ID
                criteria.clear();
                criteria.put("parentTopologySid", resVm.getCloudEnvId());
                List<ResStorage> resStorages = resStorageMapper.selectByParams(criteria);

                Map<String, ResStorage> resStorageMap = resStorages.stream()
                                                                   .collect(Collectors.toMap(ResStorage::getStorageName,
                                                                                             Function.identity(),
                                                                                             (k1, k2) -> k2));

                Set<String> diskIdSet = Sets.newHashSet();

                if ("disk".equalsIgnoreCase(vmCreateResult.getImageType())) {
                    List<ResVd> resVds = resVdMapper.selectBaseByParams(
                            new Criteria("uuid", vmCreateResult.getTargetUuid()).put("cloudEnvId",
                                                                                     vmCreateResult.getCloudEnvId())
                                                                                .put("status", "normal"));
                    if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(resVds)) {
                        ResVd resVd = resVds.get(0);
                        diskIdSet.add(vmCreateResult.getUuid());
                        resVd.setResVmId(resVm.getId());
                        resVd.setReleaseMode(ReleaseMode.STAND_ALONE);
                        resVd.setStoragePurpose(StoragePurpose.SYSTEM_DISK);
                        resVd.setCreatedOrgSid(resVm.getOrgSid());
                        resVdMapper.updateByPrimaryKeySelective(resVd);
                    }
                }

                // data disk
                List<String> diskDbIds = new ArrayList<>();
                List<VmDisk> disks = vmCreateResult.getDisks();
                if (disks != null && disks.size() > 0) {
                    for (VmDisk disk : disks) {
                        if ("disk".equalsIgnoreCase(vmCreateResult.getImageType())) {
                            if (diskIdSet.contains(disk.getUuid())) {
                                continue;
                            }
                        }
                        ResVd resVd = new ResVd();
                        resVd.setVdName(disk.getName());
                        if (!Strings.isNullOrEmpty(disk.getSize())) {
                            resVd.setAllocateDiskSize(Long.parseLong(disk.getSize()));
                        }
                        resVd.setStoragePurpose(disk.getType());
                        resVd.setUuid(disk.getUuid());
                        resVd.setLogicVolume(disk.getUuid());
                        resVd.setPath(disk.getPath());
                        resVd.setDataStoreName(disk.getLocation());
                        resVd.setUri(disk.getUri());
                        resVd.setUrn(disk.getUrn());

                        if ("share".equalsIgnoreCase(disk.getType()) || "share".equalsIgnoreCase(disk.getDiskType())) {
                            Criteria vdCriteria = new Criteria();
                            vdCriteria.put("hostId", resVm.getId());

                            ResVdHost resVdHost = new ResVdHost();
                            resVdHost.setVdSid(disk.getUuid());
                            resVdHost.setHostId(resVm.getId());
                            resVdHost.setDevicePath(disk.getPath());
                            resVdHost.setMountPoint(disk.getPath());
                            resVdHost.setCloudEnvId(resVm.getCloudEnvId());
                            resVdHost.setOrgSid(resVm.getOrgSid());
                            resVdHostMapper.updateByParams(resVdHost, vdCriteria.getCondition());
                            resVd.setDiskType("share");
                        }
                        resVd.setCloudEnvId(resVm.getCloudEnvId());
                        resVd.setResVmId(resVm.getId());
                        resVd.setStatus(ResVdStatus.NORMAL);
                        resVd.setStartTime(resVm.getStartTime());
                        resVd.setZone(disk.getAvailabilityZone());
                        resVd.setUuid(disk.getUuid());
                        resVd.setReleaseModeBoolean(disk.getDeleteWithInstance());
                        resVd.setChargeType(disk.getChargeType());
                        resVd.setSource(disk.getSource());
                        resVd.setBootable(disk.getBootable());

                        if (CloudEnvType.OPEN_STACK.equals(cloudEnv.getCloudEnvType()) || CloudEnvType.CLOUDOS.equals(
                                cloudEnv.getCloudEnvType()) || CloudEnvType.POWER_VC.equals(
                                cloudEnv.getCloudEnvType())) {
                            if (!Strings.isNullOrEmpty(disk.getVolumeType())) {
                                resVd.setVolumeTypeId(Long.valueOf(disk.getVolumeType()));
                            } else {
                                List<ResVolumeType> resVolumeTypeList = resVolumeTypeMapper.selectByParamsOpenStack(
                                        new Criteria("envId", resVm.getCloudEnvId()));
                                if (!CollectionUtils.isEmpty(resVolumeTypeList) && !"01".equals(disk.getType())) {
                                    resVd.setVolumeTypeId(resVolumeTypeList.get(0).getId());
                                }
                            }
                        } else if (CloudEnvType.VMWARE.equals(cloudEnv.getCloudEnvType()) && !Strings.isNullOrEmpty(
                                disk.getVolumeType())) {
                            resVd.setVolumeTypeId(Long.parseLong(disk.getVolumeType()));
                        }
                        if (!CollectionUtils.isEmpty(resStorages) && CloudEnvType.VMWARE.equals(
                                cloudEnv.getCloudEnvType()) && resStorageMap.containsKey(disk.getLocation())) {
                            resVd.setAllocateResStorageSid(resStorageMap.get(disk.getLocation()).getResStorageSid());
                            resVd.setDeviceName(resStorageMap.get(disk.getLocation()).getStorageName());
                            resVd.setVolumeTypeId(resStorageMap.get(disk.getLocation()).getResVolumeTypeId());
                        }
                        if (!CollectionUtils.isEmpty(resStorages) && CloudEnvType.FUSIONCOMPUTE.equals(
                                cloudEnv.getCloudEnvType()) && resStorageMap.containsKey(disk.getLocation())) {
                            resVd.setAllocateResStorageSid(resStorageMap.get(disk.getLocation()).getResStorageSid());
                            resVd.setDeviceName(resStorageMap.get(disk.getLocation()).getStorageName());
                            resVd.setVolumeTypeId(resStorageMap.get(disk.getLocation()).getResVolumeTypeId());
                            resVd.setUri(disk.getUri());
                            resVd.setUrn(disk.getUrn());
                        }
                        boolean b = CloudEnvType.HUAWEICLOUD.equals(cloudEnv.getCloudEnvType()) || CloudEnvType.QCLOUD.equals(
                                cloudEnv.getCloudEnvType()) || CloudEnvType.AWS.equals(cloudEnv.getCloudEnvType());
                        if (b || CloudEnvType.ALIYUN.equals(cloudEnv.getCloudEnvType()) || CloudEnvType.AZURE.equals(
                                cloudEnv.getCloudEnvType()) || CloudEnvType.KSYUN.equals(cloudEnv.getCloudEnvType())) {
                            if (StringUtil.isNotBlank(disk.getVolumeType())) {
                                Criteria criteria1 = new Criteria();
                                criteria1.put("envId", Long.valueOf(-1));
                                criteria1.put("cloudEnvType", cloudEnv.getCloudEnvType());
                                criteria1.put("uuid", disk.getVolumeType());
                                if (CloudEnvType.HUAWEICLOUD.equals(cloudEnv.getCloudEnvType())
                                        || CloudEnvType.KSYUN.equals(cloudEnv.getCloudEnvType())) {
                                    criteria1.put("region", cloudEnv.getRegion());
                                }
                                List<ResVolumeType> resVolumeTypeList = resVolumeTypeMapper.selectByParamsOpenStack(
                                        criteria1);
                                if (!CollectionUtils.isEmpty(resVolumeTypeList)) {
                                    resVd.setVolumeTypeId(resVolumeTypeList.get(0).getId());
                                }
                            }
                        }
                        if (CloudEnvType.KING_STACK.equals(cloudEnv.getCloudEnvType())) {
                            List<ResVolumeType> resVolumeTypeList = resVolumeTypeMapper.selectByParamsOpenStack(
                                    new Criteria("envId", resVm.getCloudEnvId()));
                            if (CollectionUtil.isNotEmpty(resVolumeTypeList)) {
                                resVd.setVolumeTypeId(resVolumeTypeList.get(0).getId());
                            }
                        }
                        if (CloudEnvType.AZURE.equals(cloudEnv.getCloudEnvType())) {
                            resVd.setLogicVolume(disk.getId());
                        }
                        if (!Objects.isNull(resVm.getOrgSid())) {
                            resVd.setOrgSid(resVm.getOrgSid());
                        }

                        if (!Objects.isNull(resVm.getCloudDeploymentId())) {
                            resVd.setCloudDeploymentId(resVm.getCloudDeploymentId());
                        }
                        resVd.setRegion(resVm.getRegion());
                        resVd.setEndTime(resVm.getEndTime());
                        resVd.setOwnerId(resVm.getOwnerId());
                        resVd.setOrgSid(resVm.getOrgSid());
                        resVd.setCreatedOrgSid(resVm.getOrgSid());
                        BasicWebUtil.prepareInsertParams(resVd, resVm.getCreatedBy());
                        this.resVdMapper.insertSelective(resVd);
                        vmCreateResult.getDiskSids().add(resVd.getResVdSid());
                        // 系统盘也关联申请单
                        if (!"share".equalsIgnoreCase(disk.getDiskType())) {
                            diskDbIds.add(resVd.getResVdSid());
                        }
                    }
                }
                // 新创硬盘 增加到订单关联资源记录
                if (CollectionUtil.isNotEmpty(diskDbIds)) {

                    serviceOrderBasicResourceRelationRemoteService.addHostRelationResource(resVm.getId(),
                                                                                           SelfServiceYamlDeployType.DISK,
                                                                                           diskDbIds);
                }

                // network ip
                List<NetworkIp> ips = this.networkIpMapper.selectByExample(
                        new Criteria("allocateTargetId", resVm.getId()));
                if (!CollectionUtils.isEmpty(ips)) {
                    NetworkIp networkIp = ips.get(0);
                    networkIp.setStatus(NetworkManagement.UNAVAILABLE);
                    BasicWebUtil.prepareUpdateParams(networkIp, resVm.getCreatedBy());
                    this.networkIpMapper.updateByPrimaryKeySelective(networkIp);
                }

                // floating ip
                Criteria example = new Criteria();
                example.put("cloudEnvId", resVm.getCloudEnvId());
                example.put("ip", resVm.getPublicIp());
                example.put("statusNotEquals", NetworkStatus.DELETED);
                List<ResFloatingIp> floatingIps = resFloatingIpMapper.selectByParams(example);
                ResFloatingIp resFloatingIp = makeFloatingIp(floatingIps, resVm, vmCreateResult);
                if (!Objects.isNull(resFloatingIp) && !CollectionUtils.isEmpty(floatingIps)) {
                    BasicWebUtil.prepareUpdateParams(resFloatingIp, vmCreateResult.getOpUser());
                    resFloatingIp.setOwnerId(resVm.getOwnerId());
                    // bug24036, 已有弹性IP生命周期独立管理
                    resFloatingIp.setOrgSid(resVm.getOrgSid());
                    resFloatingIp.setCreatedOrgSid(resVm.getOrgSid());
                    resFloatingIpMapper.updateByPrimaryKeySelective(resFloatingIp);
                    vmCreateResult.setResFloatingIpSid(resFloatingIp.getId());
                    basicResActionLogService.insertIntoActionLog(resFloatingIp.getId().toString(),
                                                                 vmCreateResult.getOpUser(),
                                                                 ResourceTypeEnum.FLOATING_IP,
                                                                 ResourceOperateEnum.ATTACH, Boolean.TRUE);

                } else if (!Objects.isNull(resFloatingIp)) {
                    BasicWebUtil.prepareInsertParams(resFloatingIp, resVm.getCreatedBy());
                    // 随实例创建的弹性ip，付费方式默认为按量付费
                    if (CloudEnvType.OPEN_STACK.equals(cloudEnv.getCloudEnvType())) {
                        resFloatingIp.setChargeType(ChargeType.POST_PAID);
                    }
                    // 归属主机的所有者
                    resFloatingIp.setOwnerId(resVm.getOwnerId());
                    resFloatingIp.setOrgSid(resVm.getOrgSid());
                    resFloatingIp.setCreatedOrgSid(resVm.getOrgSid());
                    resFloatingIpMapper.insertSelective(resFloatingIp);
                    vmCreateResult.setResFloatingIpSid(resFloatingIp.getId());
                    basicResActionLogService.insertIntoActionLog(resFloatingIp.getId().toString(),
                                                                 vmCreateResult.getOpUser(),
                                                                 ResourceTypeEnum.FLOATING_IP,
                                                                 ResourceOperateEnum.CREATE, Boolean.TRUE);
                    // 新创建弹性IP 增加到订单关联资源记录

                    serviceOrderBasicResourceRelationRemoteService.addHostRelationResource(resVm.getId(),
                                                                                           SelfServiceYamlDeployType.FLOATING_IP,
                                                                                           Arrays.asList(String.valueOf(
                                                                                                   resFloatingIp.getId())));
                }

                //success message
                resVm.setCloudEnvName(cloudEnv.getCloudEnvName());
                sendHostCreateSuccessPlatformNotification(resVm,
                                                          MailTemplateConstants.CLOUD_HOST_CREATE_SUCCESS_NOTIFI);

                // deploy agent
                logger.info("创建云主机回调 | 开始纳管: [{} - {}]", resVm.getInstanceId(), resVm.getInstanceName());
                CallbakLog callbakLog = new CallbakLog(options.get("taskId").toString(),
                                                       MessageUtil.getLogMessage("实例创建成功."), CallbakLog.Type.HOST);

                hostDeployCallbackLogMessageHandler.execute(callbakLog);

                // 判断是否所有的任务都完成
                boolean finishFlg = true;
                List<DeployTask> deployTasks = this.basicDeployTaskService.findAllByTarget(resVm.getId());
                Optional<DeployTask> taskOptional = deployTasks.stream()
                                                               .filter(task -> !DeployTaskStatus.SUCCESS.equals(
                                                                       task.getStatus()))
                                                               .findAny();
                if (taskOptional.isPresent()) {
                    finishFlg = false;
                }

                // 所有任务都完成之后，将服务实例的状态置为Running
                if (finishFlg) {
                    serviceInstTargetRemoteService.updateSelfInstByHostId(resVm.getId(),
                                                                          SelfServiceInstanceStatus.RUNNING);
                }
                // 更新关联服务的输出项

                serviceInstTargetRemoteService.updateSelfInstOutputs(resVm.getId());

                if (Objects.nonNull(vmCreateResult.getPhysicalHostPoolId())) {
                    CloudPhysicalHostPool cloudPhysicalHostPoolUpdate = new CloudPhysicalHostPool();
                    cloudPhysicalHostPoolUpdate.setId(vmCreateResult.getPhysicalHostPoolId());
                    cloudPhysicalHostPoolUpdate.setPhysicalUUID(vmCreateResult.getUuid());

                    cloudPhysicalHostPoolUpdate.setJoinMaas(true);
                    cloudPhysicalHostPoolUpdate.setPowerStatus(vmCreateResult.getPowerStatus());
                    this.cloudPhysicalHostPoolService.updateByPrimaryKeySelective(cloudPhysicalHostPoolUpdate);

                    // 更新网卡信息
                    this.cloudPhysicalHostPoolService.updatePhysicalHostInterface(
                            vmCreateResult.getPhysicalHostPoolId(), vmCreateResult.getInterfaceSet());

                    // 更新存储分区信息
                    this.cloudPhysicalHostPoolService.updatePhysicalHostPartition(
                            vmCreateResult.getPhysicalHostPoolId(), vmCreateResult.getBlockDeviceSet(),
                            vmCreateResult.getSpecialFsSet());
                }
                try {
                    fortressService.fortressAddDev(resVm);
                } catch (Exception e) {
                    logger.error("推送资源到堡垒机失败:", e);
                }
            } else {
                failReason = CloudErrorResolver.getErrorMsg(vmCreateResult.getErrMsg());
                if (resVm != null) {
                    resVm.setInstanceId(vmCreateResult.getUuid());
                    resVm.setStatusInfo(failReason);
                    resVm.setStatus(ResVmStatus.CREATE_FAILURE);
                    resVm.setManageStatus(ResVmManageStatus.DISCONNECT);
                    this.resVmMapper.updateByPrimaryKeySelective(resVm);
                    // network ip revert
                    NetworkIp networkIp = new NetworkIp();
                    networkIp.setAllocateTargetId(resVm.getId());
                    networkIp.setStatus(NetworkManagement.AVAILABLE);
                    this.networkIpMapper.updateStatusByAllocateTargetId(networkIp);

                    // floating ip revert
                    Criteria example = new Criteria();
                    example.put("cloudEnvId", resVm.getCloudEnvId());
                    example.put("ip", resVm.getPublicIp());
                    example.put("status", NetworkStatus.RESERVED);
                    List<ResFloatingIp> floatingIps = resFloatingIpMapper.selectByParams(example);
                    if (!CollectionUtils.isEmpty(floatingIps)) {
                        this.resFloatingIpMapper.unbandingFloatingIp(floatingIps.get(0).getId().intValue());
                    }

                    //send failed message
                    setHostCreateFailedPlatformNotification(resVm, failReason,
                                                            MailTemplateConstants.CLOUD_HOST_CREATE_FAILED_NOTIFI);

                    serviceInstTargetRemoteService.updateSelfInstByHostId(resVm.getId(),
                                                                          SelfServiceInstanceStatus.ERROR);
                    logger.error("取消主机: {} 所有的发布任务", resVm.getId());
                    // 取消主机发布任务
                    this.basicDeployTaskService.cancelTaskByHostNotEnd(resVm.getId());
                }

                if (Objects.nonNull(vmCreateResult.getPhysicalHostPoolId())) {
                    CloudPhysicalHostPool cloudPhysicalHostPoolUpdate = new CloudPhysicalHostPool();
                    cloudPhysicalHostPoolUpdate.setId(vmCreateResult.getPhysicalHostPoolId());
                    // 失败变成已分配
                    cloudPhysicalHostPoolUpdate.setPhysicalStatus(CloudPhysicalHostStatus.JOIN_SUCCESS);
                    cloudPhysicalHostPoolUpdate.setStatusInfo(vmCreateResult.getErrMsg());
                    this.cloudPhysicalHostPoolService.updateByPrimaryKeySelective(cloudPhysicalHostPoolUpdate);
                }

                logger.error(vmCreateResult.getErrMsg());
                logger.error("{} 创建失败：{}", vmCreateResult.getId(), vmCreateResult.getErrMsg());
                logger.error("创建云主机回调 | 创建失败");
                eventStatus = DeployTaskStatus.FAILURE;
            }
        } catch (Exception ex) {
            eventStatus = DeployTaskStatus.FAILURE;
            try {
                logger.error("创建云主机回调异常 : {}", Throwables.getStackTraceAsString(ex));
                final String sid = vmCreateResult.getId();
                resVm = this.resVmMapper.selectByPrimaryKey(sid);
                failReason = ex.getMessage();
                if (resVm != null) {
                    resVm.setStatusInfo("系统错误，请联系管理员：" + failReason);
                    resVm.setStatus(ResVmStatus.CREATE_FAILURE);
                    resVm.setManageStatus(ResVmManageStatus.DISCONNECT);
                    this.resVmMapper.updateByPrimaryKeySelective(resVm);
                    // network ip revert
                    Criteria criteria = new Criteria("allocateTargetId", resVm.getId());
                    List<NetworkIp> networkIps = this.networkIpMapper.selectByExample(criteria);
                    if (!CollectionUtils.isEmpty(networkIps)) {
                        for (NetworkIp networkIp : networkIps) {
                            networkIp.setAllocateTargetId(null);
                            networkIp.setStatus(NetworkManagement.AVAILABLE);
                            BasicWebUtil.prepareInsertParams(networkIp, resVm.getCreatedBy());
                            this.networkIpMapper.updateByPrimaryKey(networkIp);
                        }
                    }

                    //send failed message
                    setHostCreateFailedPlatformNotification(resVm, failReason,
                                                            MailTemplateConstants.CLOUD_HOST_CREATE_FAILED_NOTIFI);

                    serviceInstTargetRemoteService.updateSelfInstByHostId(resVm.getId(),
                                                                          SelfServiceInstanceStatus.ERROR);

                    if (Objects.nonNull(resVm.getPhysicalHostPoolId())) {
                        CloudPhysicalHostPool cloudPhysicalHostPool = new CloudPhysicalHostPool();
                        cloudPhysicalHostPool.setId(resVm.getPhysicalHostPoolId());

                        cloudPhysicalHostPool.setStatusInfo("系统错误，请联系管理员：" + failReason);
                        this.cloudPhysicalHostPoolService.updateByPrimaryKeySelective(cloudPhysicalHostPool);
                    }
                }
                logger.error("{} 开机失败：{}", vmCreateResult.getId(), failReason);
                logger.error("创建云主机回调 | 创建失败");
            } catch (Exception e) {
                logger.error("创建云主机回调异常 : {}", Throwables.getStackTraceAsString(e));
            }


        } finally {
            if (resVm == null) {
                resVm = new ResVm();
                resVm.setId(vmCreateResult.getSid());
            }
            // send error event
            TaskCtrlEvent<ResVm> ctrlEvent = new TaskCtrlEvent<>(resVm);
            ctrlEvent.setPlaybookId(options.get("playbookId").toString());
            ctrlEvent.setTaskId(options.get("taskId").toString());
            ctrlEvent.setTaskStatus(eventStatus);
            ctrlEvent.setFailureReason(failReason);
            SpringContextHolder.publishEvent(ctrlEvent);
        }

    }


    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#vmImageCreateResult.id", resourceType = ResourceTypeEnum.IMAGE, opUser = "#vmImageCreateResult.opUser", operate = ResourceOperateEnum.CREATE, success = "#vmImageCreateResult.success", orgSid = "#vmImageCreateResult.orgSid")
    @Message(refKey = "#vmImageCreateResult.id", envId = "#vmImageCreateResult.cloudEnvId", msgType = ServerMsgType.IMAGE, opUser = "#vmImageCreateResult.opUser", operate = OperateEnum.CREATE, success = "#vmImageCreateResult.success", refNameKey = "#vmImageCreateResult.imageName", errorMsg = "#vmImageCreateResult.errMsg")
    public void handleMessage(
            @LogParam("vmImageCreateResult") @MessageParam("vmImageCreateResult") VmImageCreateResult vmImageCreateResult) {
        logger.info("从快照创建镜像 | 回调参数 ： {}", JsonUtil.toJson(vmImageCreateResult));
        String failReason = "";
        Date date = new Date();
        try {
            Criteria criteria = new Criteria("cloudEnvId", vmImageCreateResult.getCloudEnvId());
            criteria.put("uuid", vmImageCreateResult.getSnapshotUuid());
            ResSnapshot resSnapshot = resSnapshotMapper.selectByParams(criteria).get(0);
            CloudEnv cloudEnv = cloudEnvMapper.selectByPrimaryKey(resSnapshot.getCloudEnvId());
            ResImage image = resImageMapper.selectByPrimaryKey(Long.parseLong(vmImageCreateResult.getId()));
            if (vmImageCreateResult.isSuccess()) {
                image.setStatus(ResImageStatus.OK);
                image.setImageId(vmImageCreateResult.getImageId());
                image.setImageSize(vmImageCreateResult.getImageSize());
                image.setImageName(vmImageCreateResult.getImageName());
                image.setVirtualizationType(vmImageCreateResult.getVirtualizationType());
                image.setCloudEnvType(vmImageCreateResult.getProviderType());
                image.setCloudEnvId(vmImageCreateResult.getCloudEnvId());
                image.setManagementAccount(vmImageCreateResult.getManagementAccount());
                image.setManagementPassword(vmImageCreateResult.getManagementPassword());
                image.setMinRam(vmImageCreateResult.getMinRam());
                image.setMinDisk(vmImageCreateResult.getMinDisk());
                image.setVisibility(vmImageCreateResult.getVisibility());
                image.setDiskFormat(vmImageCreateResult.getDiskFormat());
                image.setUpdatedBy(BasicInfoUtil.getCurrentUserName());
                image.setUpdatedDt(date);
                image.setOsType(vmImageCreateResult.getOsType());
                image.setOsPlatform(vmImageCreateResult.getOsPlatform());
                resImageMapper.updateByPrimaryKeySelective(image);
                if ("public".equalsIgnoreCase(image.getVisibility()) && CloudEnvType.ESCLOUD.equals(
                        cloudEnv.getCloudEnvType())) {
                    List<CloudEnv> cloudEnvs = cloudEnvMapper.selectByCloudEnvAccountId(
                            cloudEnv.getCloudEnvAccountId());
                    CloudEnv cloudEnvAdmin = cloudEnvs.stream()
                                                      .filter(env -> "ESCloud-Admin".equalsIgnoreCase(
                                                              env.getCloudEnvType()))
                                                      .findFirst()
                                                      .get();
                    image.setId(null);
                    image.setCloudEnvId(cloudEnvAdmin.getId());
                    resImageMapper.insertSelective(image);
                }

                resSnapshotMapper.deleteByPrimaryKey(resSnapshot.getId());
            } else {
                resSnapshot.setStatus(SnapshotStatus.SUCCESS);
                resSnapshotMapper.updateByPrimaryKeySelective(resSnapshot);

                resImageMapper.deleteByPrimaryKey(image.getId());
            }
        } catch (Exception ex) {
            try {
                logger.error("快照创建镜像回调异常 : {}", Throwables.getStackTraceAsString(ex));
                failReason = ex.getMessage();
                logger.error("{} 快照创建镜像转换：{}", vmImageCreateResult.getId(), failReason);
            } catch (Exception e) {
                logger.error("快照创建镜像回调异常 : {}", Throwables.getStackTraceAsString(e));
            }
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#ImageDeleteResult.sid", resourceType = ResourceTypeEnum.IMAGE, opUser = "#ImageDeleteResult.opUser", operate = ResourceOperateEnum.DELETE, success = "#ImageDeleteResult.success", orgSid = "#ImageDeleteResult.orgSid")
    @Message(refKey = "#ImageDeleteResult.sid", envId = "#ImageDeleteResult.cloudEnvId", msgType = ServerMsgType.IMAGE, opUser = "#ImageDeleteResult.opUser", operate = OperateEnum.DELETE, success = "#ImageDeleteResult.success", refNameKey = "#ImageDeleteResult.imageName", errorMsg = "#ImageDeleteResult.errMsg")
    public void handleMessage(
            @LogParam("ImageDeleteResult") @MessageParam("ImageDeleteResult") ImageDeleteResult imageDeleteResult) {
        logger.info("删除镜像 | 回调参数 ： {}", JsonUtil.toJson(imageDeleteResult));
        String failReason = "";
        try {
            final String sid = imageDeleteResult.getSid();
            ResImage resImage = resImageMapper.selectByPrimaryKey(Long.parseLong(sid));
            if (imageDeleteResult.isSuccess()) {
                resImageMapper.deleteByPrimaryKey(Long.parseLong(sid));
            } else {
                resImage.setStatus("ok");
                resImageMapper.updateByPrimaryKeySelective(resImage);
            }
        } catch (Exception ex) {
            try {
                logger.error("删除镜像回调异常 : {}", Throwables.getStackTraceAsString(ex));
                failReason = ex.getMessage();
                logger.error("{} 删除镜像异常：{}", imageDeleteResult.getErrMsg(), failReason);
            } catch (Exception e) {
                logger.error("删除镜像回调异常 : {}", Throwables.getStackTraceAsString(e));
            }
        }

    }

    private void updateAlarmRuleTarget(String id, String uuid) {
        logger.info("更新 告警规则关联表中instanceId, {} --> {}", id, uuid);
        try {
            this.basicMonitorRemoteService.updateResVmIdToInstanceId(id, uuid);
        } catch (Exception e) {
            logger.error("更新告警规则表中的instanceId失败， [{}] -> [{}]", id, Throwables.getStackTraceAsString(e));
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#vmRemoveResult.sid", resourceType = ResourceTypeEnum.VM, opUser = "#vmRemoveResult.opUser", operate = ResourceOperateEnum.DELETE, success = "#vmRemoveResult.success", orgSid = "#vmRemoveResult.orgSid")
    @Message(refKey = "#vmRemoveResult.sid", envId = "#vmRemoveResult.cloudEnvId", msgType = ServerMsgType.VM, opUser = "#vmRemoveResult.opUser", operate = OperateEnum.DELETE, success = "#vmRemoveResult.success", refNameKey = "#vmRemoveResult.vmName", errorMsg = "#vmRemoveResult.errMsg")
    @BizNotify
    public void handleMessage(
            @LogParam("vmRemoveResult") @MessageParam("vmRemoveResult") VmRemoveResult vmRemoveResult) {
        logger.info("删除云主机回调 | 回调参数 ： {}", JsonUtil.toJson(vmRemoveResult));
        ResVm resVm = this.resVmMapper.selectSimpleByPrimaryKey(vmRemoveResult.getSid());
        String taskId = vmRemoveResult.getOptions().get("taskId").toString();
        try {
            // 删除前检查环境是否处于同步状态，避免数据死锁
            String syncEnvLock = String.format(SYNC_ENV_LOCK, resVm.getCloudEnvId());
            String syncEnvVmLock = String.format(SYNC_ENV_VM_LOCK, resVm.getCloudEnvId());
            RetryUtil.retry(60, 10L, TimeUnit.SECONDS, () -> JedisUtil.instance().get(syncEnvLock) == null
                    && JedisUtil.instance().get(syncEnvVmLock) == null);

            updateElasticHistory(resVm, vmRemoveResult.isSuccess());
            if (vmRemoveResult.isSuccess()) {
                CloudEnv cloudEnv = cloudEnvMapper.selectByPrimaryKey(resVm.getCloudEnvId());
                if (Objects.nonNull(cloudEnv) && CloudEnvType.MAAS.equals(cloudEnv.getCloudEnvType())) {
                    resVm.setStatus(ResVmStatus.RUNNING);
                    resVm.setStatusInfo("");
                    User user = basicUserService.selectByPrimaryAccount(cloudEnv.getCreatedBy());
                    if (Objects.nonNull(user)) {
                        resVm.setOwnerId(user.getUserSid().toString());
                    }
                    resVmMapper.updateByPrimaryKeySelective(resVm);

                    if (Objects.nonNull(resVm.getPhysicalHostPoolId())) {
                        CloudPhysicalHostPool cloudPhysicalHostPoolUpdate = new CloudPhysicalHostPool();
                        cloudPhysicalHostPoolUpdate.setId(resVm.getPhysicalHostPoolId());
                        BasicWebUtil.prepareUpdateParams(cloudPhysicalHostPoolUpdate);
                        cloudPhysicalHostPoolUpdate.setAllocStatus(CloudPhysicalHostPoolAllocStatus.UNUSED);
                        cloudPhysicalHostPoolService.updateByPrimaryKeySelective(cloudPhysicalHostPoolUpdate);
                    }
                } else {
                    this.resVmService.removeInstanceFromDB(vmRemoveResult.getSid(), false, false,
                                                           vmRemoveResult.getOpUser());

                    // 移除Agent监听对象
                    JedisUtil.instance().delHSet(MONITOR_KEY, vmRemoveResult.getSid());
                    //删除关联记录
                    Criteria criteria = new Criteria();
                    criteria.put("hostId", resVm.getId());
                    List<ResVdHost> resVdHosts = resVdHostMapper.selectByParams(criteria);
                    if (StringUtil.isNullOrEmpty(resVdHosts) && resVdHosts.size() > 0) {
                        resVdHostMapper.deleteByParams(criteria);
                    }
                    //删除负载均衡的关联该云主机的后端服务器
                    resLbBackendMapper.deleteByParams(new Criteria("resVmId", resVm.getId()));
                }

                //更新task状态
                CallbakLog callbakLog = new CallbakLog(taskId, MessageUtil.getEndLogMessage(
                        AnsibleMessageFlag.NORMAL_SUCCESS_MESSAGE_FLAG, "实例删除成功."), CallbakLog.Type.HOST);
                // 更新用户Id推送状态，客户端重新拉取任务状态
                hostDeployCallbackLogMessageHandler.execute(callbakLog);

                if (vmRemoveResult.isDeleteFloatingIp()) {
                    //floating ip delete
                    deleteFloatingIp(resVm, vmRemoveResult);
                }
                if (Objects.nonNull(resVm.getPhysicalHostPoolId())) {
                    this.cloudPhysicalHostPoolService.updatePhysicalHostWhenResVmRemove(resVm.getPhysicalHostPoolId());
                }

                if (CloudEnvType.HUAWEICLOUD.equals(vmRemoveResult.getProviderType())) {
                    Map<String, String> params = Maps.newHashMap();
                    params.put("key", "subnet");
                    CloudEnvAccount cloudEnvAccount = cloudEnvAccountMapper.selectByCloudEnvId(
                            vmRemoveResult.getCloudEnvId());
                    if (Objects.nonNull(cloudEnvAccount)) {
                        params.put("companyId", cloudEnvAccount.getOrgSid().toString());
                    } else {
                        params.put("companyId", vmRemoveResult.getOrgSid());
                    }

                    ScheduleHelper.manualSyncTask(vmRemoveResult.getCloudEnvId(), params,
                                                  BasicInfoUtil.getCurrentUserSid());
                }

                // 如果支持堡垒机，需要删除堡垒机上的资产
                try {
                    fortressService.fortressRemoveDev(resVm);
                } catch (Exception e) {
                    logger.error("移除堡垒机的资产[{}]失败:{}", resVm.getInstanceName(), e);
                }
            } else {
                CallbakLog callbakLog = new CallbakLog(taskId, MessageUtil.getEndLogMessage(
                        AnsibleMessageFlag.NORMAL_FAILED_MESSAGE_FLAG, "实例删除失败." + vmRemoveResult.getErrMsg()),
                                                       CallbakLog.Type.HOST);

                hostDeployCallbackLogMessageHandler.execute(callbakLog);

                resVm.setStatus(ResVmStatus.FAILURE);
                resVm.setStatusInfo(vmRemoveResult.getErrMsg());
                BasicWebUtil.prepareUpdateParams(resVm, resVm.getCreatedBy());
                this.resVmService.updateByPrimaryKeySelective(resVm);
            }
        } catch (Exception e) {
            logger.info("删除云主机回调 | 失败 ： {}", Throwables.getStackTraceAsString(e));
            CallbakLog callbakLog = new CallbakLog(taskId, MessageUtil.getEndLogMessage(
                    AnsibleMessageFlag.NORMAL_FAILED_MESSAGE_FLAG, "实例删除失败." + e.getMessage()), CallbakLog.Type.HOST);
            hostDeployCallbackLogMessageHandler.execute(callbakLog);

            if (resVm != null) {
                resVm.setStatus(ResVmStatus.FAILURE);
                resVm.setStatusInfo("删除主机失败：" + e.getMessage());
                BasicWebUtil.prepareUpdateParams(resVm, resVm.getCreatedBy());
                this.resVmService.updateByPrimaryKeySelective(resVm);
            }
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#resetPasswordResult.options.get('resVmId')", resourceType = ResourceTypeEnum.VM, opUser = "#resetPasswordResult.opUser", operate = ResourceOperateEnum.RESET_PASSWORD, success = "#resetPasswordResult.success", orgSid = "#resetPasswordResult.orgSid")
    @Message(refKey = "#resetPasswordResult.options.get('resVmId')", envId = "#resetPasswordResult.cloudEnvId", msgType = ServerMsgType.VM, opUser = "#resetPasswordResult.opUser", operate = OperateEnum.RESET_PASSWORD, success = "#resetPasswordResult.success", errorMsg = "#resetPasswordResult.errMsg")
    public void handleMessage(
            @LogParam("resetPasswordResult") @MessageParam("resetPasswordResult") ResetPasswordResult resetPasswordResult) {
        Map<String, Object> options = resetPasswordResult.getOptions();
        String resVmId = options.get("resVmId").toString();
        ResVm resVm = new ResVm();
        resVm.setId(resVmId);
        try {
            if (resetPasswordResult.isSuccess()) {
                if (!Strings.isNullOrEmpty(resetPasswordResult.getUsername())) {
                    resVm.setManagementAccount(resetPasswordResult.getUsername());
                }

                resVm.setManagemenPassword(resetPasswordResult.getPassword());

                resVmMapper.updateByPrimaryKeySelective(resVm);
            }

        } catch (Exception e) {
            logger.error(Throwables.getStackTraceAsString(e));
        } finally {
            String msg = resetPasswordResult.isSuccess() ? "实例重置密码成功." : "实例重置密码失败. " + resetPasswordResult.getErrMsg();
            String flag = resetPasswordResult.isSuccess() ? AnsibleMessageFlag.NORMAL_SUCCESS_MESSAGE_FLAG
                                                          : AnsibleMessageFlag.NORMAL_FAILED_MESSAGE_FLAG;
            CallbakLog callbakLog = new CallbakLog(options.get("taskId").toString(),
                                                   MessageUtil.getEndLogMessage(flag, msg), CallbakLog.Type.HOST);
            hostDeployCallbackLogMessageHandler.execute(callbakLog);

            TaskCtrlEvent<ResVm> ctrlEvent = new TaskCtrlEvent<>(resVm);
            ctrlEvent.setPlaybookId(options.get("pid").toString());
            ctrlEvent.setTaskId(options.get("taskId").toString());
            ctrlEvent.setTaskStatus(
                    resetPasswordResult.isSuccess() ? DeployTaskStatus.SUCCESS : DeployTaskStatus.FAILURE);
            SpringContextHolder.publishEvent(ctrlEvent);
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    public void updateElasticHistory(ResVm resVm, boolean success) {
    }

    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#vmOperateResult.id", resourceType = ResourceTypeEnum.VM, opUser = "#vmOperateResult.opUser", operate = ResourceOperateEnum.OPERATE, success = "#vmOperateResult.success", other = "#vmOperateResult.action", orgSid = "#vmOperateResult.orgSid")
    @Message(refKey = "#vmOperateResult.id", envId = "#vmOperateResult.cloudEnvId", msgType = ServerMsgType.VM, opUser = "#vmOperateResult.opUser", operate = OperateEnum.OPERATE, success = "#vmOperateResult.success", refNameKey = "#vmOperateResult.vmName", operateAction = "#vmOperateResult.action", errorMsg = "#vmOperateResult.errMsg")
    public void handleMessage(
            @LogParam("vmOperateResult") @MessageParam("vmOperateResult") VmOperateResult vmOperateResult) {
        logger.info("主机[{}] 操作[{}] {}", vmOperateResult.getId(), vmOperateResult.getAction(),
                    vmOperateResult.isSuccess() ? "成功" : "失败");

        Map<String, Object> options = vmOperateResult.getOptions();
        try {
            ResVm resVm = new ResVm();
            resVm.setId(vmOperateResult.getId());
            if (vmOperateResult.isSuccess()) {
                resVm.setStatus(vmOperateResult.getVmStatus());
                if (Objects.nonNull(options) && options.containsKey("isStopCharging")) {
                    boolean isStopCharging = (boolean) options.get("isStopCharging");
                    if (isStopCharging) {
                        resVm.setStoppedMode(StoppedMode.STOP_CHARGING);
                        Criteria criteria = new Criteria();
                        criteria.put("cloudEnvId", resVm.getCloudEnvId());
                        criteria.put("ip", resVm.getPublicIp());
                        int count = resFloatingIpMapper.countByParams(criteria);
                        if (!Strings.isNullOrEmpty(resVm.getPublicIp()) && count < 1) {
                            resVm.setPublicIp(null);
                        }
                    }
                }
                if (Objects.nonNull(options) && options.containsKey("physicalHostPoolId")) {
                    CloudPhysicalHostPool cloudPhysicalHostPool = new CloudPhysicalHostPool();
                    cloudPhysicalHostPool.setId(Long.parseLong(options.get("physicalHostPoolId").toString()));
                    cloudPhysicalHostPool.setPowerStatus(
                            ResVmStatus.STOPPED.equals(vmOperateResult.getVmStatus()) ? PowerStatus.OFF
                                                                                      : PowerStatus.ON);
                    this.cloudPhysicalHostPoolService.updateByPrimaryKeySelective(cloudPhysicalHostPool);
                }

                ResVm resVmOrg = this.resVmService.selectSimpleByPrimaryKey(vmOperateResult.getId());
                CloudEnv cloudEnv = this.cloudEnvMapper.selectByPrimaryKey(resVmOrg.getCloudEnvId());
                if (CloudEnvType.FUSIONCOMPUTE.equals(cloudEnv.getCloudEnvType())) {
                    Criteria criteria = new Criteria();
                    criteria.put("parentTopologySid", resVmOrg.getCloudEnvId());
                    criteria.put("urn", vmOperateResult.getHostUrn());
                    List<ResHost> resHosts = this.resHostMapper.selectByParamsForScan(criteria);
                    resVm.setAllocateResHostSid(
                            CollectionUtil.isNotEmpty(resHosts) ? resHosts.get(0).getResHostSid() : "");
                }
                serviceInstTargetRemoteService.updateSelfInstByHostId(resVm.getId(), vmOperateResult.getVmStatus(),
                                                                      false);
                if (Objects.nonNull(options) && options.get("taskId") != null) {
                    DeployTask deployTask = basicDeployTaskService.selectByPrimaryKey(options.get("taskId").toString());
                    deployTask.setStatus(DeployTaskStatus.SUCCESS);
                    deployTask.setStatusName(codeService.getTaskStatusName(deployTask.getStatus()));
                    deployTask.setCompletedQuantity(deployTask.getTotalQuantity());
                    deployTask.setEndDate(new Date());
                    basicDeployTaskService.updateByPrimaryKeySelective(deployTask);
                    String message = "";
                    switch (deployTask.getType()) {
                        case DeployTaskType.STOP_HOST:
                            message = "实例停止成功.";
                            break;
                        case DeployTaskType.START_HOST:
                            message = "实例启动成功.";
                            break;
                        case DeployTaskType.RESTART_HOST:
                            message = "实例重启成功.";
                            break;
                        default:
                            break;
                    }
                    CallbakLog callbakLogStop = new CallbakLog(options.get("taskId").toString(),
                                                               MessageUtil.getLogMessage(message),
                                                               CallbakLog.Type.HOST);

                    hostDeployCallbackLogMessageHandler.execute(callbakLogStop);
                }

            } else {
                resVm.setStatus(ResVmStatus.FAILURE);
                resVm.setStatusInfo(vmOperateResult.getErrMsg());
                if (Objects.nonNull(options) && options.get("taskId") != null) {
                    DeployTask deployTask = basicDeployTaskService.selectByPrimaryKey(options.get("taskId").toString());
                    deployTask.setStatus(DeployTaskStatus.FAILURE);
                    deployTask.setStatusName(codeService.getTaskStatusName(deployTask.getStatus()));
                    deployTask.setCompletedQuantity(deployTask.getTotalQuantity());
                    deployTask.setEndDate(new Date());
                    basicDeployTaskService.updateByPrimaryKeySelective(deployTask);
                    String message = "";
                    switch (deployTask.getType()) {
                        case DeployTaskType.STOP_HOST:
                            message = "实例停止失败.";
                            break;
                        case DeployTaskType.START_HOST:
                            message = "实例启动失败.";
                            break;
                        case DeployTaskType.RESTART_HOST:
                            message = "实例重启失败.";
                            break;
                        default:
                            break;
                    }
                    CallbakLog callbakLogStop = new CallbakLog(options.get("taskId").toString(),
                                                               MessageUtil.getLogMessage(
                                                                       message + vmOperateResult.getErrMsg()),
                                                               CallbakLog.Type.HOST);

                    hostDeployCallbackLogMessageHandler.execute(callbakLogStop);
                }
            }
            BasicWebUtil.prepareUpdateParams(resVm, resVm.getCreatedBy());
            this.resVmMapper.updateByPrimaryKeySelective(resVm);
        } catch (Exception e) {
            logger.error(e.getMessage());
        } finally {
            serviceInstTargetRemoteService.updateSelfInstByHostId(vmOperateResult.getId(), vmOperateResult.isSuccess()
                                                                                           ? vmOperateResult.getVmStatus()
                                                                                           : SelfServiceInstanceStatus.ERROR,
                                                                  Collections.singletonList(CloudDeploymentType.INFRA));
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#vmReconfigResult.options.get('resVmId')", resourceType = ResourceTypeEnum.VM, opUser = "#vmReconfigResult.opUser", operate = ResourceOperateEnum.RE_CONFIG, success = "#vmReconfigResult.success", orgSid = "#vmReconfigResult.orgSid")
    @Message(refKey = "#vmReconfigResult.options.get('resVmId')", envId = "#vmReconfigResult.cloudEnvId", msgType = ServerMsgType.VM, opUser = "#vmReconfigResult.opUser", operate = OperateEnum.RE_CONFIG, success = "#vmReconfigResult.success", refNameKey = "#vmReconfigResult.vmName", errorMsg = "#vmReconfigResult.errMsg")
    public void handleMessage(
            @LogParam("vmReconfigResult") @MessageParam("vmReconfigResult") VmReconfigResult vmReconfigResult) {
        logger.info("重新配置云主机回调 | 回调参数 ： {}", JsonUtil.toJson(vmReconfigResult));
        Map<String, Object> options = vmReconfigResult.getOptions();
        String resVmId = options.get("resVmId").toString();
        String taskId = options.get("taskId").toString();
        List<VmDisk> disks = vmReconfigResult.getDisks();
        List<VmNic> vmNics = vmReconfigResult.getVmNics();
        ResVm resVm = resVmMapper.selectByPrimaryKey(resVmId);
        boolean haveError = false;
        boolean multiAttach = false;

        Criteria vdHostcriteria = new Criteria();
        vdHostcriteria.put("hostId", resVmId);
        try {
            if (vmReconfigResult.isSuccess()) {
                //更新task状态
                CallbakLog callbakLog = new CallbakLog(taskId, MessageUtil.getEndLogMessage(
                        AnsibleMessageFlag.NORMAL_SUCCESS_MESSAGE_FLAG, "实例重新配置成功."), CallbakLog.Type.HOST);
                // 更新用户Id推送状态，客户端重新拉取任务状态
                hostDeployCallbackLogMessageHandler.execute(callbakLog);

                List<String> createDiskSids = new ArrayList<>();
                //region 操作硬盘
                for (VmDisk disk : disks) {
                    ResVd resVd = new ResVd();
                    resVd.setResVdSid(disk.getId());
                    if (!StringUtil.isNullOrEmpty(disk.getDiskType()) && "share".equalsIgnoreCase(disk.getDiskType())) {
                        multiAttach = true;
                    }
                    if (ResOperation.DELETE.equals(disk.getOperate())) {
                        //更新状态为已删除
                        resVd.setStatus(ResVdStatus.DELETED);
                        resVd.setEndTime(DateTime.now().toDate());
                        resVdMapper.updateByPrimaryKeySelective(resVd);
                        if (multiAttach) {
                            resVdHostMapper.deleteByParams(vdHostcriteria);
                        }
                        basicResActionLogService.insertIntoActionLog(resVd.getResVdSid(), vmReconfigResult.getOpUser(),
                                                                     ResourceTypeEnum.VD, ResourceOperateEnum.DELETE,
                                                                     Boolean.TRUE);
                        // 删除订单相关资源
                        ServiceOrderBasicResourceRelationParams serviceOrderBasicResourceRelationParams = new ServiceOrderBasicResourceRelationParams();
                        serviceOrderBasicResourceRelationParams.setResourceId(resVd.getResVdSid());
                        serviceOrderBasicResourceRelationParams.setResourceType(SelfServiceYamlDeployType.DISK);
                        serviceOrderBasicResourceRelationRemoteService.deleteByParams(
                                serviceOrderBasicResourceRelationParams);
                    } else if (ResOperation.DETACH.equals(disk.getOperate())) {
                        //更新状态为未使用
                        resVd = resVdMapper.selectByPrimaryKey(resVd.getResVdSid());
                        resVd.setStatus(ResVdStatus.NORMAL);
                        resVd.setResVmId(null);
                        resVdMapper.updateByPrimaryKey(resVd);
                        if (multiAttach) {
                            resVdHostMapper.deleteByParams(vdHostcriteria);
                        }
                        basicResActionLogService.insertIntoActionLog(resVd.getResVdSid(), vmReconfigResult.getOpUser(),
                                                                     ResourceTypeEnum.VD, ResourceOperateEnum.DETACH,
                                                                     Boolean.TRUE);
                    } else if (ResOperation.MODIFY.equals(disk.getOperate())) {
                        //更新状态为使用中
                        resVd.setStatus(ResVdStatus.NORMAL);
                        resVd.setAllocateDiskSize(Long.valueOf(disk.getSize()));
                        resVdMapper.updateByPrimaryKeySelective(resVd);

                        basicResActionLogService.insertIntoActionLog(resVd.getResVdSid(), vmReconfigResult.getOpUser(),
                                                                     ResourceTypeEnum.VD, ResourceOperateEnum.MODIFY,
                                                                     Boolean.TRUE);
                    } else if (ResOperation.CREATE.equals(disk.getOperate())) {
                        //新创建的硬盘更新相关信息
                        resVd.setUuid(disk.getPath());
                        resVd.setResVmId(resVm.getId());
                        resVd.setStartTime(resVm.getStartTime());
                        resVd.setEndTime(resVm.getEndTime());
                        resVd.setReleaseModeBoolean(disk.getDeleteWithInstance());
                        resVd.setStoragePurpose(StoragePurpose.DATA_DISK);
                        resVd.setPath(disk.getPath());
                        resVd.setLogicVolume(disk.getPath());
                        resVd.setDeviceName(disk.getLocation());
                        resVd.setReleaseModeBoolean(disk.getDeleteWithInstance());
                        if (!Objects.isNull(resVm.getOrgSid())) {
                            resVd.setOrgSid(resVm.getOrgSid());
                        }

                        if (!Objects.isNull(resVm.getCloudDeploymentId())) {
                            resVd.setCloudDeploymentId(resVm.getCloudDeploymentId());
                        }
                        resVd.setStatus(ResVdStatus.NORMAL);
                        resVd.setUri(disk.getUri());
                        resVd.setUrn(disk.getUrn());
                        resVdMapper.updateByPrimaryKeySelective(resVd);
                        if (multiAttach) {
                            ResVdHost vdHost = new ResVdHost();
                            vdHost.setHostId(resVmId);
                            vdHost.setVdSid(resVd.getUuid());
                            vdHost.setCloudEnvId(resVm.getCloudEnvId());
                            vdHost.setOrgSid(BasicInfoUtil.getCurrentOrgSid());
                            resVdHostMapper.insert(vdHost);
                        }
                        basicResActionLogService.insertIntoActionLog(resVd.getResVdSid(), vmReconfigResult.getOpUser(),
                                                                     ResourceTypeEnum.VD, ResourceOperateEnum.CREATE,
                                                                     Boolean.TRUE);
                        createDiskSids.add(resVd.getResVdSid());
                    } else if (ResOperation.KEEP.equals(disk.getOperate()) && !StringUtil.isNullOrEmpty(
                            disk.getSize())) {
                        resVd.setAllocateDiskSize(Long.parseLong(disk.getSize()));
                        resVdMapper.updateByPrimaryKeySelective(resVd);
                    }
                }
                //endregion
                if (CollectionUtil.isNotEmpty(createDiskSids)) {
                    // 新创硬盘 增加到订单关联资源记录
                    serviceOrderBasicResourceRelationRemoteService.addHostRelationResource(resVmId,
                                                                                           SelfServiceYamlDeployType.DISK,
                                                                                           createDiskSids);
                }

                //region 操作网络
                // 获取需要占用的IP地址
                List<String> networkIps = vmNics.stream()
                                                .filter(vmNic -> !Strings.isNullOrEmpty(vmNic.getPrivateIp())
                                                        || !Strings.isNullOrEmpty(vmNic.getPublicIp()))
                                                .filter(vmNic -> !"remove".equals(vmNic.getOperate()))
                                                .map(vmNic -> Strings.isNullOrEmpty(vmNic.getPublicIp())
                                                              ? vmNic.getPrivateIp() : vmNic.getPrivateIp())
                                                .collect(Collectors.toList());

                Map<String, String> vsPortGroupMap = new HashMap<>();
                if (!CollectionUtils.isEmpty(vmReconfigResult.getOrignNics())) {
                    for (VmNic orignNic : vmReconfigResult.getOrignNics()) {
                        if (!Strings.isNullOrEmpty(orignNic.getVirSwitch())) {
                            vsPortGroupMap.put(orignNic.getVirSwitch(), orignNic.getPortGroupId());
                        } else {
                            vsPortGroupMap.put(orignNic.getPort(), orignNic.getPortGroupId());
                        }
                    }
                }

                for (VmNic vmNic : vmNics) {
                    // 把实例的网卡信息维护到实例与网卡的关系表
                    ResVmNetcard resVmNetcard = new ResVmNetcard();
                    resVmNetcard.setMacAddress(vmNic.getMac());
                    String ipAddress = "";
                    if (!Strings.isNullOrEmpty(vmNic.getPrivateIp())) {
                        ipAddress = vmNic.getPrivateIp();
                    } else if (!Strings.isNullOrEmpty(vmNic.getPublicIp())) {
                        ipAddress = vmNic.getPublicIp();
                    }
                    resVmNetcard.setIpAddress(ipAddress);

                    resVmNetcard.setResVmId(resVmId);
                    resVmNetcard.setResVsPortGroupId(vsPortGroupMap.get(vmNic.getVirSwitch()));

                    if ("add".equals(vmNic.getOperate())) {
                        resVmNetcardMapper.insertSelective(resVmNetcard);
                    } else if ("remove".equals(vmNic.getOperate())) {
                        resVmNetcardMapper.deleteByPrimaryKey(resVmNetcard);
                    } else {
                        Criteria criteria = new Criteria();
                        criteria.put("resVmId", resVmId);
                        criteria.put("macAddress", resVmNetcard.getMacAddress());
                        resVmNetcardMapper.deleteByParams(criteria);
                        //删除主机额外信息
                        criteria.clear();
                        criteria.put("instanceId", resVmId);
                        criteria.put("resourceId", vmNic.getResNetworkId());
                        criteria.put("type", "subnet");
                        resVmExtService.deleteByParams(criteria);

                        resVmNetcardMapper.insertSelective(resVmNetcard);
                    }

                    if ("remove".equals(vmNic.getOperate())) {
                        // 删除网卡时，取消IP占用
                        if (!Strings.isNullOrEmpty(vmNic.getSubnetId())) {
                            NetworkIp networkIp = networkIpMapper.selectByPrimaryKey(Long.valueOf(vmNic.getSubnetId()));
                            networkIp.setStatus(NetworkManagement.AVAILABLE);
                            networkIp.setAllocateTargetId(null);

                            BasicWebUtil.prepareUpdateParams(networkIp, vmReconfigResult.getOpUser());
                            networkIpMapper.updateByPrimaryKey(networkIp);
                        }
                    }
                }

                if (!CollectionUtils.isEmpty(networkIps)) {
                    // 根据IP和云环境ID查询子网
                    Criteria criteria = new Criteria();
                    criteria.put("cloudEnvId", resVm.getCloudEnvId());
                    criteria.put("networkIps", networkIps);
                    List<Network> networks = networkMapper.selectByParams(criteria);

                    // 如果查询结果不为空, 根据IP地址和子网ID进行IP占用
                    if (!CollectionUtils.isEmpty(networks)) {
                        List<NetworkIp> ipAddressList = networkIpMapper.selectByExample(
                                new Criteria("ipAddressList", networkIps).put("cloudEnvId", resVm.getCloudEnvId()));
                        Map<Long, List<NetworkIp>> ipGroupBySubnetId = ipAddressList.stream()
                                                                                    .filter(o -> Objects.nonNull(
                                                                                            o.getNetworkId()))
                                                                                    .collect(Collectors.groupingBy(
                                                                                            NetworkIp::getNetworkId));

                        List<ResVmExt> resVmExts = Lists.newArrayList();
                        for (Long key : ipGroupBySubnetId.keySet()) {
                            ResVmExt resVmExt = new ResVmExt();
                            resVmExt.setExtra(ipGroupBySubnetId.get(key)
                                                               .stream()
                                                               .map(NetworkIp::getIpAddress)
                                                               .collect(Collectors.joining(",")));
                            resVmExt.setType(ResVmExtEnum.SUBNET.getType());
                            resVmExt.setResourceId(key.toString());
                            resVmExt.setInstanceId(resVmId);
                            resVmExts.add(resVmExt);
                        }

                        resVmExtService.insertMulti(resVmExts);

                        List<Long> networkIds = networks.stream().map(Network::getId).collect(Collectors.toList());
                        // ip占用
                        NetworkIp networkIp = new NetworkIp();
                        networkIp.setAllocateTargetId(resVmId);
                        networkIp.setStatus(NetworkManagement.UNAVAILABLE);
                        networkIpMapper.updateByExampleSelective(networkIp, ImmutableMap.of("ipAddressList", networkIps,
                                                                                            "networkIds", networkIds));
                    }

                    // 释放修改后未使用的ip
                    NetworkIp networkIp = new NetworkIp();
                    networkIp.setStatus(NetworkManagement.AVAILABLE);
                    BasicWebUtil.prepareUpdateParams(networkIp, vmReconfigResult.getOpUser());
                    networkIpMapper.resetNetworkIpStatus(networkIp, ImmutableMap.of("ipNotInAddressList", networkIps,
                                                                                    "allocateTargetId", resVmId));

                    // 将IP进行内网和外网分类，更新到实例表的内网IP和外网IP字段上
                    List<String> innerIpList = new ArrayList<>();
                    List<String> publicIpList = new ArrayList<>();
                    for (String ip : networkIps) {
                        if (IPUtil.internalIp(ip)) {
                            innerIpList.add(ip);
                        } else {
                            publicIpList.add(ip);
                        }
                    }
                    if (!innerIpList.isEmpty()) {
                        resVm.setInnerIp(Joiner.on(",").skipNulls().join(innerIpList));
                    }
                    if (!publicIpList.isEmpty()) {
                        resVm.setPublicIp(Joiner.on(",").skipNulls().join(publicIpList));
                    }
                }
                //endregion

                //修改实例类型
                resVm.setId(resVmId);
                resVm.setCpu(Integer.parseInt(vmReconfigResult.getCpu()));
                resVm.setMemory(Integer.parseInt(vmReconfigResult.getMemory()));
                resVm.setInstanceType(vmReconfigResult.getInstanceType());
                resVm.setStatus(vmReconfigResult.getStatus());
                BasicWebUtil.prepareUpdateParams(resVm, vmReconfigResult.getOpUser());
                resVmMapper.updateByPrimaryKeySelective(resVm);
                serviceInstTargetRemoteService.updateSelfInstByHostId(resVm.getId(), SelfServiceInstanceStatus.RUNNING,
                                                                      Collections.singletonList(
                                                                              CloudDeploymentType.INFRA));
                //添加站内信
                sendHostCreateSuccessPlatformNotification(resVm,
                                                          MailTemplateConstants.CLOUD_HOST_RECONFIG_SUCCESS_NOTIFI);
            } else {
                CallbakLog callbakLog = new CallbakLog(taskId, MessageUtil.getEndLogMessage(
                        AnsibleMessageFlag.NORMAL_FAILED_MESSAGE_FLAG, "实例重新配置失败." + vmReconfigResult.getErrMsg()),
                                                       CallbakLog.Type.HOST);

                hostDeployCallbackLogMessageHandler.execute(callbakLog);

                //重置硬盘
                List<String> operate = Arrays.asList(ResOperation.DELETE, ResOperation.DETACH, ResOperation.MODIFY);
                for (VmDisk disk : disks) {
                    if (operate.contains(disk.getOperate())) {
                        ResVd resVd = new ResVd();
                        resVd.setResVdSid(disk.getId());
                        resVd.setStatus(ResVdStatus.NORMAL);

                        BasicWebUtil.prepareUpdateParams(resVd, vmReconfigResult.getOpUser());
                        resVdMapper.updateByPrimaryKeySelective(resVd);
                    } else if (ResOperation.CREATE.equals(disk.getOperate())) {
                        resVdMapper.deleteByPrimaryKey(disk.getId());
                    }
                }

                //重置网络
                for (VmNic vmNic : vmNics) {
                    NetworkIp networkIp = networkIpMapper.selectByPrimaryKey(Long.valueOf(vmNic.getSubnetId()));
                    if ("add".equals(vmNic.getOperate())) {
                        networkIp.setStatus(NetworkManagement.AVAILABLE);
                        networkIp.setAllocateTargetId(null);
                    } else if ("remove".equals(vmNic.getOperate())) {
                        networkIp.setStatus(NetworkManagement.UNAVAILABLE);
                    }

                    BasicWebUtil.prepareUpdateParams(networkIp, vmReconfigResult.getOpUser());
                    networkIpMapper.updateByPrimaryKey(networkIp);
                }

                resVm.setStatus(vmReconfigResult.getStatus());
                BasicWebUtil.prepareUpdateParams(resVm, vmReconfigResult.getOpUser());
                resVmMapper.updateByPrimaryKeySelective(resVm);
                serviceInstTargetRemoteService.updateSelfInstByHostId(resVm.getId(), SelfServiceInstanceStatus.ERROR,
                                                                      Collections.singletonList(
                                                                              CloudDeploymentType.INFRA));
                setHostCreateFailedPlatformNotification(resVm, vmReconfigResult.getErrMsg(),
                                                        MailTemplateConstants.CLOUD_HOST_RECONFIG_FAILED_NOTIFI);
            }
        } catch (Exception e) {
            logger.error(Throwables.getStackTraceAsString(e));
            //重置主机状态
            resVm.setStatus(vmReconfigResult.getStatus());
            BasicWebUtil.prepareUpdateParams(resVm, vmReconfigResult.getOpUser());
            resVmMapper.updateByPrimaryKeySelective(resVm);
            haveError = true;
            setHostCreateFailedPlatformNotification(resVm, e.getMessage(),
                                                    MailTemplateConstants.CLOUD_HOST_RECONFIG_FAILED_NOTIFI);
        } finally {
            if (haveError) {
                serviceInstTargetRemoteService.updateSelfInstByHostId(resVm.getId(), vmReconfigResult.getStatus(),
                                                                      Collections.singletonList(
                                                                              CloudDeploymentType.INFRA));
            }
        }
    }

    public void unInstallAgent(ResVm resVm) {
        List<Long> deployInstIds = serviceInstTargetService.getDeployInstByTargetId(resVm.getId(),
                                                                                    SelfServiceYamlDeployType.INFRA);

        ResVm deployResVm = new ResVm();
        deployResVm.setId("-1");
        deployResVm.setInstanceName(resVm.getInstanceName());
        deployResVm.setOwnerId(resVm.getOwnerId());
        deployResVm.setServiceDeployInstId(!CollectionUtils.isEmpty(deployInstIds) ? deployInstIds.get(0) : null);
        deployResVm.setCreatedDt(resVm.getCreatedDt());
        DeployTask retryTask = this.basicDeployTaskService.setupHostModuleTask(deployResVm,
                                                                               DeployTaskType.UNINSTALL_AGENT,
                                                                               DeployTaskStatus.RUNNING);
        HostDepoyEvent hostDepoyEvent = new HostDepoyEvent(resVm);
        hostDepoyEvent.setType(DeployTaskType.UNINSTALL_AGENT);
        hostDepoyEvent.setApiPath(AnsibleServerMethod.ControlApi.UNINSTALL);
        hostDepoyEvent.setTaskId(retryTask.getId().toString());
        hostDepoyEvent.setEventRoutingKey(AnsibleMqConfig.NODE_CONTROL_ROUTING_KEY_INCOMPLETE
                                                  + AnsibleTaskTypeConfig.NODE_CONTROL_UNINSTALL_AGENT);

        SpringContextHolder.publishEvent(hostDepoyEvent);
    }

    private void deleteFloatingIp(ResVm resVm, VmRemoveResult vmRemoveResult) {
        Criteria example = new Criteria();
        example.put("cloudEnvId", resVm.getCloudEnvId());
        example.put("ip", resVm.getPublicIp());
        example.put("statusNotEquals", NetworkStatus.DELETED);
        List<ResFloatingIp> floatingIps = resFloatingIpMapper.selectByParams(example);
        if (!CollectionUtils.isEmpty(floatingIps)) {
            ResFloatingIp resFloatingIp = floatingIps.get(0);
            resFloatingIp.setEndTime(Calendar.getInstance().getTime());
            resFloatingIp.setStatus(NetworkStatus.DELETED);
            resFloatingIpMapper.updateByPrimaryKeySelective(resFloatingIp);

            basicResActionLogService.insertIntoActionLog(resFloatingIp.getId().toString(), vmRemoveResult.getOpUser(),
                                                         ResourceTypeEnum.FLOATING_IP, ResourceOperateEnum.DELETE,
                                                         Boolean.TRUE);
        }
    }

    private ResFloatingIp makeFloatingIp(List<ResFloatingIp> floatingIps, ResVm resVm, VmCreateResult vmCreateResult) {
        if (Objects.isNull(vmCreateResult.getFloatingIp()) || Strings.isNullOrEmpty(
                vmCreateResult.getFloatingIp().getIp())) {
            return null;
        }
        ResFloatingIp resFloatingIp = new ResFloatingIp();
        //使用已有的弹性IP
        if (!CollectionUtils.isEmpty(floatingIps)) {
            resFloatingIp = floatingIps.get(0);
        } else {
            resFloatingIp.setEndTime(resVm.getEndTime());
        }

        resFloatingIp.setIp(vmCreateResult.getFloatingIp().getIp());
        resFloatingIp.setStartTime(resVm.getStartTime());
        resFloatingIp.setInstanceName(resVm.getInstanceName());
        resFloatingIp.setInstanceId(resVm.getInstanceId());
        resFloatingIp.setFixedIp(vmCreateResult.getFloatingIp().getFixedip());
        resFloatingIp.setUuid(vmCreateResult.getFloatingIp().getId());
        resFloatingIp.setStatus(NetworkStatus.ACTIVE);
        resFloatingIp.setOrgSid(resVm.getOrgSid());
        resFloatingIp.setEnvId(resVm.getCloudEnvId());
        resFloatingIp.setInstanceType(ResourceType.RES_HOST);
        String bandwidth = vmCreateResult.getFloatingIp().getBandwidth();
        resFloatingIp.setBandWidth(NumberUtil.isNumber(bandwidth) ? bandwidth : null);
        resFloatingIp.setPolicyId(vmCreateResult.getFloatingIp().getPolicyId());
        resFloatingIp.setRuleId(vmCreateResult.getFloatingIp().getRuleId());

        List<ResVpc> resVpcs = resVpcMapper.selectByParams(new Criteria("cloudEnvId", resVm.getCloudEnvId()));
        addAdminShare(resVm.getCloudEnvId(), resVpcs);
        if (!CollectionUtils.isEmpty(resVpcs)) {
            Optional<ResVpc> vpcOptional = resVpcs.stream()
                                                  .filter(resVpc -> resVpc.getUuid()
                                                                          .equals(vmCreateResult.getFloatingIp()
                                                                                                .getPool()))
                                                  .findFirst();

            if (vpcOptional.isPresent()) {
                resFloatingIp.setPool(vpcOptional.get().getName());
            }

        }

        return resFloatingIp;
    }

    private void addAdminShare(Long cloudEnvId, List<ResVpc> resVpcs) {
        CloudEnv cloudEnv = cloudEnvMapper.selectByPrimaryKey(cloudEnvId);
        if (CloudEnvType.OPEN_STACK.equals(cloudEnv.getCloudEnvType())) {
            List<CloudEnv> cloudEnvs = cloudEnvMapper.selectByCloudEnvAccountId(cloudEnv.getCloudEnvAccountId());
            List<Long> cloudEnvIds = cloudEnvs.stream().map(CloudEnv::getId).collect(Collectors.toList());
            // 查询admin共享的ip池
            Criteria criteria = new Criteria("cloudEnvIdIn", cloudEnvIds);
            criteria.put("external", "true");
            resVpcs.addAll(resVpcMapper.selectByParams(criteria));
        }
    }

    private void processDisk(Object instanceId, List<VmDisk> disks) throws Exception {
        ResVm resVm = resVmMapper.selectByPrimaryKey(instanceId.toString());
        // 删除当前主机已挂载的硬盘
        resVdMapper.deleteByParams(new Criteria("resVmId", instanceId));

        Criteria criteria = new Criteria();
        criteria.put("parentTopologySid", resVm.getCloudEnvId());
        List<ResStorage> resStorages = resStorageMapper.selectByParams(criteria);

        if (!CollectionUtils.isEmpty(resStorages)) {
            Map<String, ResStorage> storageMap = resStorages.stream()
                                                            .filter(o -> !Strings.isNullOrEmpty(o.getStorageName()))
                                                            .filter(distinctByKey(ResStorage::getStorageName))
                                                            .collect(Collectors.toMap(ResStorage::getStorageName,
                                                                                      Function.identity()));

            if (disks != null && disks.size() > 0) {
                for (VmDisk disk : disks) {
                    ResVd resVd = new ResVd();
                    resVd.setVdName(disk.getName());
                    if (!Strings.isNullOrEmpty(disk.getSize())) {
                        resVd.setAllocateDiskSize(Long.parseLong(disk.getSize()));
                    }
                    resVd.setStoragePurpose(disk.getType());
                    resVd.setUuid(disk.getUuid());
                    resVd.setLogicVolume(disk.getUuid());
                    resVd.setPath(disk.getPath());
                    resVd.setDataStoreName(disk.getLocation());
                    resVd.setUri(disk.getUri());
                    resVd.setUrn(disk.getUrn());

                    resVd.setCloudEnvId(resVm.getCloudEnvId());
                    resVd.setResVmId(resVm.getId());
                    resVd.setStatus(ResVdStatus.NORMAL);
                    resVd.setStartTime(new Date());
                    resVd.setReleaseModeBoolean(disk.getDeleteWithInstance());
                    if (storageMap.containsKey(disk.getLocation())) {
                        resVd.setAllocateResStorageSid(storageMap.get(disk.getLocation()).getResStorageSid());
                        resVd.setDeviceName(storageMap.get(disk.getLocation()).getStorageName());
                        resVd.setVolumeTypeId(storageMap.get(disk.getLocation()).getResVolumeTypeId());
                    }
                    if (!Objects.isNull(resVm.getOrgSid())) {
                        resVd.setOrgSid(resVm.getOrgSid());
                    }

                    if (!Objects.isNull(resVm.getCloudDeploymentId())) {
                        resVd.setCloudDeploymentId(resVm.getCloudDeploymentId());
                    }
                    BasicWebUtil.prepareInsertParams(resVd, resVm.getCreatedBy());
                    this.resVdMapper.insertSelective(resVd);
                }
            }
        }
    }

    /**
     * 通过属性过滤
     */
    private <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#vmTypeChangeResult.resVmId", resourceType = ResourceTypeEnum.VM, opUser = "#vmTypeChangeResult.opUser", operate = ResourceOperateEnum.VM_TYPE_CHANGE, success = "#vmTypeChangeResult.success", orgSid = "#vmTypeChangeResult.orgSid")
    @Message(refKey = "#vmTypeChangeResult.resVmId", envId = "#vmTypeChangeResult.cloudEnvId", msgType = ServerMsgType.VM, opUser = "#vmTypeChangeResult.opUser", operate = OperateEnum.VM_TYPE_CHANGE, success = "#vmTypeChangeResult.success", refNameKey = "#vmTypeChangeResult.name", errorMsg = "#vmTypeChangeResult.errMsg")
    @BizNotify
    public void handleMessage(
            @LogParam("vmTypeChangeResult") @MessageParam("vmTypeChangeResult") VmTypeChangeResult vmTypeChangeResult) {
        logger.info("主机变更实例类型回调 | 回调参数 ： {}", JsonUtil.toJson(vmTypeChangeResult));
        Map<String, Object> options = vmTypeChangeResult.getOptions();
        String taskId = options.get("taskId").toString();
        ResVm resVm = resVmMapper.selectByPrimaryKey(vmTypeChangeResult.getResVmId());
        if (vmTypeChangeResult.isSuccess()) {
            ResChangeRecord resChangeRecord = resChangeRecordMapper.seletResChangeRecordById(
                    vmTypeChangeResult.getResChangeRecordId());
            resChangeRecord.setChangeEndTime(new Date());
            resChangeRecordMapper.updateByPrimaryKey(resChangeRecord);
            // 查询更改后的配置规格
            Criteria criteria = new Criteria();
            criteria.put("uuid", vmTypeChangeResult.getVmTypeUuid());
            criteria.put("envId", vmTypeChangeResult.getCloudEnvId());
            List<ResVmType> resVmTypes = resVmTypeService.selectByParams(criteria);
            resVm.setInstanceType(vmTypeChangeResult.getVmTypeUuid());
            if (!CollectionUtils.isEmpty(resVmTypes)) {
                resVm.setCpu(resVmTypes.get(0).getCpu());
                resVm.setMemory(resVmTypes.get(0).getRam().intValue());
            }

            //更新task状态
            CallbakLog callbakLog = new CallbakLog(taskId, MessageUtil.getEndLogMessage(
                    AnsibleMessageFlag.NORMAL_SUCCESS_MESSAGE_FLAG, "实例重新配置成功."), CallbakLog.Type.HOST);
            // 更新用户Id推送状态，客户端重新拉取任务状态
            hostDeployCallbackLogMessageHandler.execute(callbakLog);
        } else {
            resChangeRecordMapper.deleteResChangeRecordById(vmTypeChangeResult.getResChangeRecordId());

            CallbakLog callbakLog = new CallbakLog(taskId, MessageUtil.getEndLogMessage(
                    AnsibleMessageFlag.NORMAL_FAILED_MESSAGE_FLAG, "实例重新配置失败." + vmTypeChangeResult.getErrMsg()),
                                                   CallbakLog.Type.HOST);

            hostDeployCallbackLogMessageHandler.execute(callbakLog);
        }
        // 还原状态
        resVm.setStatus(ResVmStatus.STOPPED);
        resVmMapper.updateByPrimaryKey(resVm);
    }

    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#vmTemplateResult.options.get('imageId')", resourceType = ResourceTypeEnum.IMAGE, opUser = "#vmTemplateResult.opUser", operate = ResourceOperateEnum.CLONE_AS_TEMPLATE, success = "#vmTemplateResult.success", orgSid = "#vmTemplateResult.orgSid")
    @Message(refKey = "#vmTemplateResult.options.get('imageId')", envId = "#vmTemplateResult.cloudEnvId", msgType = ServerMsgType.IMAGE, opUser = "#vmTemplateResult.opUser", operate = OperateEnum.CLONE_AS_TEMPLATE, success = "#vmTemplateResult.success", refNameKey = "#vmTemplateResult.templateName", errorMsg = "#vmTemplateResult.errMsg")
    public void handleMessage(
            @LogParam("vmTemplateResult") @MessageParam("vmTemplateResult") VmTemplateResult vmTemplateResult) {
        logger.info("VMWare 实例克隆为模板回调 | 回调参数 ： {}", JsonUtil.toJson(vmTemplateResult));
        ResImage resImage = new ResImage();
        Map<String, Object> options = vmTemplateResult.getOptions();
        resImage.setId(Long.valueOf((Integer) options.get("imageId")));
        if (vmTemplateResult.isSuccess()) {
            resImage.setStatus(ResImageStatus.NOT_CONFIG);
            if (Objects.nonNull(vmTemplateResult.getAvailableSize())) {
                resImage.setAvailableSize(Double.valueOf(vmTemplateResult.getAvailableSize()));
            }
        } else {
            resImage.setStatus(ResImageStatus.ERROR);
        }
        resImage.setUri(vmTemplateResult.getUri());
        resImage.setUrn(vmTemplateResult.getUrn());
        BasicWebUtil.prepareUpdateParams(resImage, null);
        resImageMapper.updateByPrimaryKeySelective(resImage);

        //暂时没有操作，直接结束掉任务
        DeployTask deployTask = basicDeployTaskService.selectByPrimaryKey(options.get("taskId").toString());
        deployTask.setStatus(DeployTaskStatus.SUCCESS);
        deployTask.setStatusName(codeService.getTaskStatusName(deployTask.getStatus()));
        deployTask.setCompletedQuantity(deployTask.getTotalQuantity());
        deployTask.setEndDate(new Date());
        basicDeployTaskService.updateByPrimaryKeySelective(deployTask);
    }

    /***rcLink接管主机**/
    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#reconnectVmResult.resVmId", resourceType = ResourceTypeEnum.VM, opUser = "#reconnectVmResult.opUser", operate = ResourceOperateEnum.IMPORT, success = "#reconnectVmResult.success", orgSid = "#reconnectVmResult.orgSid")
    @Message(refKey = "#reconnectVmResult.resVmId", envId = "#reconnectVmResult.cloudEnvId", msgType = ServerMsgType.VM, opUser = "#reconnectVmResult.opUser", operate = OperateEnum.IMPORT_HOST, success = "#reconnectVmResult.success", errorMsg = "#reconnectVmResult.errMsg")
    public void handleMessage(
            @LogParam("reconnectVmResult") @MessageParam("reconnectVmResult") ReconnectVmResult reconnectVmResult) {
        if (logger.isInfoEnabled()) {
            logger.info("二级云管接管返回结果 [{}]", JsonUtil.toJson(reconnectVmResult));
        }
        // 根据执行结果 修改结果任务状态
        if (StringUtil.isNotBlank(reconnectVmResult.getTaskId())) {
            DeployTask deployTask = this.basicDeployTaskService.selectByPrimaryKey(reconnectVmResult.getTaskId());
            if (deployTask != null) {
                int totalQuantity = null != deployTask.getTotalQuantity() ? deployTask.getTotalQuantity() : 1;
                deployTask.setFailureReason(reconnectVmResult.getErrMsg());
                deployTask.setCompletedQuantity(totalQuantity);
                deployTask.setTotalQuantity(totalQuantity);
                deployTask.setStatus(
                        reconnectVmResult.isSuccess() ? DeployTaskStatus.SUCCESS : DeployTaskStatus.FAILURE);
                deployTask.setStatusName(codeService.getTaskStatusName(deployTask.getStatus()));
                deployTask.setEndDate(Calendar.getInstance().getTime());
                this.basicDeployTaskService.updateByPrimaryKeySelective(deployTask);
            }
        }

        // 修改实例接管状态
        // 修改实例其他属性字段
        if (StringUtil.isBlank(reconnectVmResult.getResVmId())) {
            logger.error("没有返回 主机id，详细数据: {}", JsonUtil.toJson(reconnectVmResult));
            return;
        }
        ResVm resVm = new ResVm();
        resVm.setId(reconnectVmResult.getResVmId());
        resVm.setOsName(reconnectVmResult.getOsName());
        resVm.setPlatform(reconnectVmResult.getPlatform());
        resVm.setManagedIp(reconnectVmResult.getManageIp());
        resVm.setManageStatus(
                reconnectVmResult.isSuccess() ? ResVmManageStatus.CONNECTED : ResVmManageStatus.DISCONNECT);
        if (reconnectVmResult.isSuccess() && StringUtil.isNotBlank(reconnectVmResult.getNewPassword())) {
            resVm.setManagemenPassword(reconnectVmResult.getNewPassword());
        }
        resVmMapper.updateByPrimaryKeySelective(resVm);
        logger.info("设置主机({})接入状态为:{}", resVm.getId(), resVm.getManageStatus());
        if (reconnectVmResult.isSuccess()) {
            // 二级云管创建主机不会用于其他自服务，此处直接将关联自服务实例状态修改
            serviceInstTargetRemoteService.updateSelfInstByHostId(resVm.getId(), SelfServiceInstanceStatus.RUNNING);
        } else {
            serviceInstTargetRemoteService.updateSelfInstByHostId(resVm.getId(), SelfServiceInstanceStatus.ERROR);
        }
        // 发送更新前端
        ServerMsgPublisher.sendMsgToResourceType(ServerMsgType.VM.getTypeFamily(), reconnectVmResult.getResVmId());
    }

    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#resetVncPasswordResult.options.get('resVmId')", resourceType = ResourceTypeEnum.VM, opUser = "#resetVncPasswordResult.opUser", operate = ResourceOperateEnum.RESET_VNC_PASSWORD, success = "#resetVncPasswordResult.success", orgSid = "#resetVncPasswordResult.orgSid")
    @Message(refKey = "#resetVncPasswordResult.options.get('resVmId')", envId = "#resetVncPasswordResult.cloudEnvId", msgType = ServerMsgType.VM, opUser = "#resetVncPasswordResult.opUser", operate = OperateEnum.RESET_VNC_PASSWORD, success = "#resetVncPasswordResult.success", errorMsg = "#resetVncPasswordResult.errMsg")
    public void handleMessage(
            @LogParam("resetVncPasswordResult") @MessageParam("resetVncPasswordResult") ResetVncPasswordResult resetVncPasswordResult) {
        Map<String, Object> options = resetVncPasswordResult.getOptions();
        String resVmId = options.get("resVmId").toString();
        try {
            if (resetVncPasswordResult.isSuccess()) {
                ResVm resVmUpdate = new ResVm();
                resVmUpdate.setId(resVmId);
                resVmUpdate.setVncInfo(resetVncPasswordResult.getVncInfo());
                this.resVmMapper.updateByPrimaryKeySelective(resVmUpdate);
            }
        } catch (Exception e) {
            logger.error(Throwables.getStackTraceAsString(e));
        }
    }

    @LogMethod(resourceKey = "#vmRenewInstanceResult.resVmId", resourceType = ResourceTypeEnum.RENEW_INSTANCE, opUser = "#vmRenewInstanceResult.opUser", operate = ResourceOperateEnum.VM_RENEW, success = "#vmRenewInstanceResult.success", orgSid = "#vmRenewInstanceResult.orgSid")
    @Transactional(rollbackFor = Exception.class)
    public void handleMessage(
            @LogParam("vmRenewInstanceResult") @MessageParam("vmRenewInstanceResult") VmRenewInstanceResult vmRenewInstanceResult) {
        logger.info("主机续订 | 回调参数 ： {}", JsonUtil.toJson(vmRenewInstanceResult));
        ResVm resVm = resVmService.selectByPrimaryKey(vmRenewInstanceResult.getResVmId());
        if (Objects.isNull(resVm)) {
            logger.error("找不到主机实例, 参数:{}", JsonUtil.toJson(vmRenewInstanceResult));
            return;
        }
        CloudEnv cloudEnv = cloudEnvMapper.selectByPrimaryKey(resVm.getCloudEnvId());
        String taskId = vmRenewInstanceResult.getOptions().get("taskId").toString();
        DeployTask deployTask = basicDeployTaskService.selectByPrimaryKey(taskId);
        String sendMsg = "实例续费成功.";
        if (vmRenewInstanceResult.isSuccess()) {
            if (CloudEnvType.HUAWEICLOUD.equals(cloudEnv.getCloudEnvType())) {
                sendMsg = "续订已提交，请登录华为云控制台查看订单支付情况。";
            }
            vmTypeChangeService.updateVmEndTime(resVm.getId(), vmRenewInstanceResult.getEndTime());
            String message = MessageUtil.getLogMessage("任务成功.. 实例续费成功.");
            MongoUtil.save(new LogInfo(resVm.getId(), message), DeployConst.HOST_LOG_KEY_PREFIX);
            MongoUtil.save(new LogInfo(taskId, message), DeployConst.TASK_LOG_KEY_PREFIX);
            deployTask.setStatus(DeployTaskStatus.SUCCESS);
            deployTask.setStatusName(codeService.getTaskStatusName(DeployTaskStatus.SUCCESS));
            deployTask.setCompletedQuantity(deployTask.getTotalQuantity());
            deployTask.setEndDate(new Date());
            basicDeployTaskService.updateByPrimaryKeySelective(deployTask);
        } else {
            sendMsg = "实例续费失败! " + vmRenewInstanceResult.getErrMsg();
            String message = MessageUtil.getLogMessage("续订失败.. " + vmRenewInstanceResult.getErrMsg());
            MongoUtil.save(new LogInfo(resVm.getId(), message), DeployConst.HOST_LOG_KEY_PREFIX);
            MongoUtil.save(new LogInfo(taskId, message), DeployConst.TASK_LOG_KEY_PREFIX);
            deployTask.setStatus(DeployTaskStatus.FAILURE);
            deployTask.setStatusName(codeService.getTaskStatusName(DeployTaskStatus.FAILURE));
            deployTask.setFailureReason(vmRenewInstanceResult.getErrMsg());
            deployTask.setEndDate(DateUtil.date());
            deployTask.setCompletedQuantity(deployTask.getTotalQuantity());
            basicDeployTaskService.updateByPrimaryKeySelective(deployTask);
        }
        logger.info("实例:{}-{} 续订结果:{}", resVm.getId(), resVm.getInstanceName(), sendMsg);
        long userSid = BasicInfoUtil.getCurrentUserInfo(vmRenewInstanceResult.getOpUser()).getUserSid();
        Map<String, Object> content = MapsKit.of("instanceId", resVm.getInstanceId(), "instanceName",
                                                 resVm.getInstanceName(), "message", sendMsg, "errorMsg",
                                                 Strings.nullToEmpty(sendMsg));
        // 发送错误消息给对应的用户提示
        ServerMsgPublisher.sendMsgToUser(String.valueOf(userSid),
                                         ServerMsg.create(ServerMsgType.VM, resVm.getId(), content,
                                                          vmRenewInstanceResult.isSuccess()));
        ServerMsgPublisher.sendMsgToResourceType(ServerMsgType.VM.getTypeFamily(), resVm.getId());

    }

    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#vmReinstallSystemResult.resVmId", resourceType = ResourceTypeEnum.VM, opUser = "#vmReinstallSystemResult.opUser", operate = ResourceOperateEnum.REINSTALL_SYSTEM, success = "#vmReinstallSystemResult.success", orgSid = "#vmReinstallSystemResult.orgSid")
    @Message(refKey = "#vmReinstallSystemResult.resVmId", envId = "#vmReinstallSystemResult.cloudEnvId", msgType = ServerMsgType.VM, opUser = "#vmReinstallSystemResult.opUser", operate = OperateEnum.REINSTALL_SYSTEM, success = "#vmReinstallSystemResult.success", refNameKey = "#vmReinstallSystemResult.resVmName", errorMsg = "#vmReinstallSystemResult.errMsg")
    public void handleMessage(
            @LogParam("vmReinstallSystemResult") @MessageParam("vmReinstallSystemResult") VmReinstallSystemResult vmReinstallSystemResult) {
        logger.info("操作系统重装主机回调 | 回调参数 ： {}", JsonUtil.toJson(vmReinstallSystemResult));
        Map<String, Object> options = vmReinstallSystemResult.getOptions();
        String taskStatus = DeployTaskStatus.SUCCESS;
        String resVmId = vmReinstallSystemResult.getResVmId();
        String failureReason = null;
        ResVm resVm = null;
        try {
            resVm = this.resVmMapper.selectByPrimaryKey(resVmId);
            if (vmReinstallSystemResult.isSuccess()) {
                ResVm resVmUpdate = new ResVm();
                resVmUpdate.setId(resVmId);
                resVmUpdate.setImageId(vmReinstallSystemResult.getImageUuid());
                ResImage resImage = resImageMapper.selectByPrimaryKey(vmReinstallSystemResult.getImageSid());
                if (Objects.nonNull(resImage)) {
                    ResImageInfo resImageInfo = ResImageInfo.builder()
                                                            .imageName(resImage.getImageName())
                                                            .osPlatform(resImage.getOsPlatform())
                                                            .osVersion(resImage.getOsVersion())
                                                            .bit(Objects.nonNull(resImage.getBit()) ? String.valueOf(
                                                                    resImage.getBit()) : null)
                                                            .build();
                    resVmUpdate.setOsName(ResVmHelper.getOsName(null, resImageInfo));
                    Map<String, Object> imageHostInfoMap = this.resVmService.getHostInfoMapByImage(resImage);
                    resVmUpdate.setOsCategory((String) imageHostInfoMap.get("osCategory"));
                    resVmUpdate.setSshPort((Integer) imageHostInfoMap.get("sshPort"));
                    resVmUpdate.setBit(String.valueOf(imageHostInfoMap.get("bit")));
                } else {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_153033725));
                }
                resVmUpdate.setStatus(ResVmStatus.RUNNING);
                resVmUpdate.setManageStatus(ResVmManageStatus.UNUNITED);
                if (CloudEnvType.MAAS.equals(resVm.getCloudEnvType())) {
                    resVmUpdate.setInnerIp(vmReinstallSystemResult.getInnerIp());
                    resVmUpdate.setStatus(PowerStatus.OFF.equalsIgnoreCase(vmReinstallSystemResult.getPowerStatus())
                                          ? ResVmStatus.STOPPED : ResVmStatus.RUNNING);
                    resVmUpdate.setStatusInfo("");
                    resVmUpdate.setHostName(vmReinstallSystemResult.getHostName());
                }

                if (StringUtils.isNotBlank(vmReinstallSystemResult.getImageUuid())) {
                    resVmUpdate.setImageId(vmReinstallSystemResult.getImageUuid());
                }
                if (StringUtils.isNotBlank(vmReinstallSystemResult.getRemoteLoginType())) {
                    resVmUpdate.setRemoteLoginType(vmReinstallSystemResult.getRemoteLoginType());
                }
                if (Objects.nonNull(vmReinstallSystemResult.getKeypairId())) {
                    resVmUpdate.setKeypairId(vmReinstallSystemResult.getKeypairId());
                }
                if (StringUtils.isNotBlank(vmReinstallSystemResult.getManagementAccount())) {
                    resVmUpdate.setManagementAccount(vmReinstallSystemResult.getManagementAccount());
                }
                if (StringUtils.isNotBlank(vmReinstallSystemResult.getAdminPass())) {
                    resVmUpdate.setManagemenPassword(vmReinstallSystemResult.getAdminPass());
                }

                this.resVmMapper.updateByPrimaryKeySelective(resVmUpdate);

                if (CloudEnvType.MAAS.equals(resVm.getCloudEnvType())) {
                    if (Objects.nonNull(vmReinstallSystemResult.getSubnetId())) {
                        ResVmExt resVmExt = new ResVmExt();
                        resVmExt.setInstanceId(resVm.getId());
                        resVmExt.setResourceId(String.valueOf(vmReinstallSystemResult.getSubnetId()));
                        resVmExt.setType(ResVmExtEnum.SUBNET.getType());
                        resVmExt.setExtra(vmReinstallSystemResult.getInnerIp());
                        resVmExtService.insertMulti(Arrays.asList(resVmExt));
                    }
                    if (Objects.nonNull(resVm.getPhysicalHostPoolId())) {
                        CloudPhysicalHostPool cloudPhysicalHostPoolUpdate = new CloudPhysicalHostPool();
                        cloudPhysicalHostPoolUpdate.setId(resVm.getPhysicalHostPoolId());
                        cloudPhysicalHostPoolUpdate.setInnerIp(resVmUpdate.getInnerIp());
                        cloudPhysicalHostPoolUpdate.setOsCategory(resVmUpdate.getOsCategory());
                        cloudPhysicalHostPoolUpdate.setOsName(resVmUpdate.getOsName());

                        if (StringUtils.isNotBlank(vmReinstallSystemResult.getRemoteLoginType())) {
                            cloudPhysicalHostPoolUpdate.setRemoteLoginType(
                                    vmReinstallSystemResult.getRemoteLoginType());
                        }
                        if (Objects.nonNull(vmReinstallSystemResult.getKeypairId())) {
                            cloudPhysicalHostPoolUpdate.setKeypairId(vmReinstallSystemResult.getKeypairId());
                        }
                        if (StringUtils.isNotBlank(vmReinstallSystemResult.getManagementAccount())) {
                            cloudPhysicalHostPoolUpdate.setManagementAccount(
                                    vmReinstallSystemResult.getManagementAccount());
                        }
                        if (StringUtils.isNotBlank(vmReinstallSystemResult.getAdminPass())) {
                            cloudPhysicalHostPoolUpdate.setManagemenPassword(vmReinstallSystemResult.getAdminPass());
                        }
                        BasicWebUtil.prepareUpdateParams(cloudPhysicalHostPoolUpdate);
                        cloudPhysicalHostPoolService.updateByPrimaryKeySelective(cloudPhysicalHostPoolUpdate);
                    }
                }

            } else {
                Map<String, Object> resVmOriginalInfo = (Map<String, Object>) options.get("resVmOriginalInfo");
                ResVm resVmUpdate = new ResVm();
                resVmUpdate.setId(resVmId);
                resVmUpdate.setStatus(String.valueOf(resVmOriginalInfo.get("status")));
                if (CloudEnvType.MAAS.equals(resVm.getCloudEnvType())) {
                    resVmUpdate.setStatus(ResVmStatus.REINSTALL_SYSTEM_FAILURE);
                    resVmUpdate.setStatusInfo(vmReinstallSystemResult.getErrMsg());
                }
                resVmUpdate.setRemoteLoginType(String.valueOf(resVmOriginalInfo.get("remoteLoginType")));
                resVmUpdate.setManagementAccount(String.valueOf(resVmOriginalInfo.get("managementAccount")));
                if (Objects.nonNull(resVmOriginalInfo.get("keypairId"))) {
                    resVmUpdate.setKeypairId(Long.parseLong(resVmOriginalInfo.get("keypairId").toString()));
                }
                resVmUpdate.setManagemenPassword(String.valueOf(resVmOriginalInfo.get("managemenPassword")));
                this.resVmMapper.updateByPrimaryKeySelective(resVmUpdate);
                taskStatus = DeployTaskStatus.FAILURE;
                failureReason = vmReinstallSystemResult.getErrMsg();
            }
        } catch (Exception e) {
            logger.error("操作系统重装失败 : {}", Throwables.getStackTraceAsString(e));
        } finally {
            if (resVm == null) {
                resVm = new ResVm();
                resVm.setId(resVmId);
            }
            TaskCtrlEvent<ResVm> ctrlEvent = new TaskCtrlEvent<>(resVm);
            ctrlEvent.setPlaybookId(options.get("playbookId").toString());
            ctrlEvent.setTaskId(options.get("taskId").toString());
            ctrlEvent.setTaskStatus(taskStatus);
            ctrlEvent.setFailureReason(failureReason);
            SpringContextHolder.publishEvent(ctrlEvent);
        }
    }
}
