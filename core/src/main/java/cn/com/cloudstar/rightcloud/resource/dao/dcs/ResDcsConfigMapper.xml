<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.cloudstar.rightcloud.resource.dao.dcs.ResDcsConfigMapper">
    <resultMap id="BaseResultMap" type="cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResDcsConfig">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="instance_id" jdbcType="VARCHAR" property="instanceId"/>
        <result column="param_id" jdbcType="VARCHAR" property="paramId"/>
        <result column="param_name" jdbcType="VARCHAR" property="paramName"/>
        <result column="param_value" jdbcType="VARCHAR" property="paramValue"/>
        <result column="default_value" jdbcType="VARCHAR" property="defaultValue"/>
        <result column="value_type" jdbcType="VARCHAR" property="valueType"/>
        <result column="value_range" jdbcType="VARCHAR" property="valueRange"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="org_sid" jdbcType="BIGINT" property="orgSid"/>
        <result column="cloud_env_id" jdbcType="BIGINT" property="cloudEnvId"/>
        <result column="VERSION" jdbcType="BIGINT" property="version"/>
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="CREATED_DT" jdbcType="TIMESTAMP" property="createdDt"/>
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
        <result column="UPDATED_DT" jdbcType="TIMESTAMP" property="updatedDt"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, instance_id, param_id, param_name, param_value, default_value, value_type, value_range,
        description, org_sid,cloud_env_id, VERSION, CREATED_BY, CREATED_DT, UPDATED_BY, UPDATED_DT
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from res_dcs_config
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from res_dcs_config
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResDcsConfig">
        insert into res_dcs_config (id, instance_id, param_id,
        param_name, param_value, default_value,
        value_type, value_range, description,
        org_sid,cloud_env_id, VERSION, CREATED_BY,
        CREATED_DT, UPDATED_BY, UPDATED_DT
        )
        values (#{id,jdbcType=BIGINT}, #{instanceId,jdbcType=VARCHAR}, #{paramId,jdbcType=VARCHAR},
        #{paramName,jdbcType=VARCHAR}, #{paramValue,jdbcType=VARCHAR}, #{defaultValue,jdbcType=VARCHAR},
        #{valueType,jdbcType=VARCHAR}, #{valueRange,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR},
        #{orgSid,jdbcType=BIGINT}, #{cloudEnvId,jdbcType=BIGINT},#{version,jdbcType=BIGINT},
        #{createdBy,jdbcType=VARCHAR},
        #{createdDt,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{updatedDt,jdbcType=TIMESTAMP}
        )
    </insert>
    <insert id="insertSelective" parameterType="cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResDcsConfig">
        insert into res_dcs_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="instanceId != null">
                instance_id,
            </if>
            <if test="paramId != null">
                param_id,
            </if>
            <if test="paramName != null">
                param_name,
            </if>
            <if test="paramValue != null">
                param_value,
            </if>
            <if test="defaultValue != null">
                default_value,
            </if>
            <if test="valueType != null">
                value_type,
            </if>
            <if test="valueRange != null">
                value_range,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="orgSid != null">
                org_sid,
            </if>
            <if test="version != null">
                VERSION,
            </if>
            <if test="createdBy != null">
                CREATED_BY,
            </if>
            <if test="createdDt != null">
                CREATED_DT,
            </if>
            <if test="updatedBy != null">
                UPDATED_BY,
            </if>
            <if test="updatedDt != null">
                UPDATED_DT,
            </if>
            <if test="cloudEnvId != null">
                cloud_env_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="instanceId != null">
                #{instanceId,jdbcType=VARCHAR},
            </if>
            <if test="paramId != null">
                #{paramId,jdbcType=VARCHAR},
            </if>
            <if test="paramName != null">
                #{paramName,jdbcType=VARCHAR},
            </if>
            <if test="paramValue != null">
                #{paramValue,jdbcType=VARCHAR},
            </if>
            <if test="defaultValue != null">
                #{defaultValue,jdbcType=VARCHAR},
            </if>
            <if test="valueType != null">
                #{valueType,jdbcType=VARCHAR},
            </if>
            <if test="valueRange != null">
                #{valueRange,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="orgSid != null">
                #{orgSid,jdbcType=BIGINT},
            </if>
            <if test="version != null">
                #{version,jdbcType=BIGINT},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDt != null">
                #{createdDt,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDt != null">
                #{updatedDt,jdbcType=TIMESTAMP},
            </if>
            <if test="cloudEnvId != null">
                #{cloudEnvId,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResDcsConfig">
        update res_dcs_config
        <set>
            <if test="instanceId != null">
                instance_id = #{instanceId,jdbcType=VARCHAR},
            </if>
            <if test="paramId != null">
                param_id = #{paramId,jdbcType=VARCHAR},
            </if>
            <if test="paramName != null">
                param_name = #{paramName,jdbcType=VARCHAR},
            </if>
            <if test="paramValue != null">
                param_value = #{paramValue,jdbcType=VARCHAR},
            </if>
            <if test="defaultValue != null">
                default_value = #{defaultValue,jdbcType=VARCHAR},
            </if>
            <if test="valueType != null">
                value_type = #{valueType,jdbcType=VARCHAR},
            </if>
            <if test="valueRange != null">
                value_range = #{valueRange,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
            </if>
            <if test="orgSid != null">
                org_sid = #{orgSid,jdbcType=BIGINT},
            </if>
            <if test="version != null">
                VERSION = #{version,jdbcType=BIGINT},
            </if>
            <if test="createdBy != null">
                CREATED_BY = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDt != null">
                CREATED_DT = #{createdDt,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDt != null">
                UPDATED_DT = #{updatedDt,jdbcType=TIMESTAMP},
            </if>
            <if test="cloudEnvId != null">
                cloud_env_id = #{cloudEnvId,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResDcsConfig">
        update res_dcs_config
        set instance_id = #{instanceId,jdbcType=VARCHAR},
        param_id = #{paramId,jdbcType=VARCHAR},
        param_name = #{paramName,jdbcType=VARCHAR},
        param_value = #{paramValue,jdbcType=VARCHAR},
        default_value = #{defaultValue,jdbcType=VARCHAR},
        value_type = #{valueType,jdbcType=VARCHAR},
        value_range = #{valueRange,jdbcType=VARCHAR},
        description = #{description,jdbcType=VARCHAR},
        org_sid = #{orgSid,jdbcType=BIGINT},
        VERSION = #{version,jdbcType=BIGINT},
        CREATED_BY = #{createdBy,jdbcType=VARCHAR},
        CREATED_DT = #{createdDt,jdbcType=TIMESTAMP},
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
        UPDATED_DT = #{updatedDt,jdbcType=TIMESTAMP},
        cloud_env_id = #{cloudEnvId,jdbcType=BIGINT}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!--自动生成代码结束位置-->

    <delete id="deleteByParams" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        delete from res_dcs_config
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>

    <select id="selectByParams" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from res_dcs_config
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <update id="updateByParamsSelective" parameterType="map">
        update res_dcs_config
        <set>
            <if test="record.instanceId != null">
                instance_id = #{record.instanceId,jdbcType=VARCHAR},
            </if>
            <if test="record.paramId != null">
                param_id = #{record.paramId,jdbcType=VARCHAR},
            </if>
            <if test="record.paramName != null">
                param_name = #{record.paramName,jdbcType=VARCHAR},
            </if>
            <if test="record.paramValue != null">
                param_value = #{record.paramValue,jdbcType=VARCHAR},
            </if>
            <if test="record.defaultValue != null">
                default_value = #{record.defaultValue,jdbcType=VARCHAR},
            </if>
            <if test="record.valueType != null">
                value_type = #{record.valueType,jdbcType=VARCHAR},
            </if>
            <if test="record.valueRange != null">
                value_range = #{record.valueRange,jdbcType=VARCHAR},
            </if>
            <if test="record.description != null">
                description = #{record.description,jdbcType=VARCHAR},
            </if>
            <if test="record.orgSid != null">
                org_sid = #{record.orgSid,jdbcType=BIGINT},
            </if>
            <if test="record.cloudEnvId != null">
                cloud_env_id = #{record.cloudEnvId,jdbcType=BIGINT},
            </if>
            <if test="record.version != null">
                VERSION = #{record.version,jdbcType=BIGINT},
            </if>
            <if test="record.createdBy != null">
                CREATED_BY = #{record.createdBy,jdbcType=VARCHAR},
            </if>
            <if test="record.createdDt != null">
                CREATED_DT = #{record.createdDt,jdbcType=TIMESTAMP},
            </if>
            <if test="record.updatedBy != null">
                UPDATED_BY = #{record.updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="record.updatedDt != null">
                UPDATED_DT = #{record.updatedDt,jdbcType=TIMESTAMP},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </update>
    <sql id="Example_Where_Clause">
        <where>
            <if test="condition.id != null">
                AND id=#{condition.id}
            </if>
            <if test="condition.instanceId != null">
                AND instance_id=#{condition.instanceId}
            </if>
            <if test="condition.paramId != null">
                AND param_id=#{condition.paramId}
            </if>
            <if test="condition.paramName != null">
                AND param_name=#{condition.paramName}
            </if>
            <if test="condition.paramValue != null">
                AND param_value=#{condition.paramValue}
            </if>
            <if test="condition.defaultValue != null">
                AND default_value=#{condition.defaultValue}
            </if>
            <if test="condition.valueType != null">
                AND value_type=#{condition.valueType}
            </if>
            <if test="condition.valueRange != null">
                AND value_range=#{condition.valueRange}
            </if>
            <if test="condition.description != null">
                AND description=#{condition.description}
            </if>
            <if test="condition.orgSid != null">
                AND org_sid=#{condition.orgSid}
            </if>
            <if test="condition.cloudEnvId != null">
                AND cloud_env_id=#{condition.cloudEnvId}
            </if>
            <if test="condition.version != null">
                AND version=#{condition.version}
            </if>
            <if test="condition.createdBy != null">
                AND created_by=#{condition.createdBy}
            </if>
            <if test="condition.createdDt != null">
                AND created_dt=#{condition.createdDt}
            </if>
            <if test="condition.updatedBy != null">
                AND updated_by=#{condition.updatedBy}
            </if>
            <if test="condition.updatedDt != null">
                AND updated_dt=#{condition.updatedDt}
            </if>
        </where>
    </sql>
</mapper>
