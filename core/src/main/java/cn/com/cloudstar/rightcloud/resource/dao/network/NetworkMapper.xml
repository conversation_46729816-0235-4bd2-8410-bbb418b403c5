<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2018 Cloud-Star, Inc. All Rights Reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.cloudstar.rightcloud.resource.dao.network.NetworkMapper">
    <resultMap id="BaseResultMap" type="cn.com.cloudstar.rightcloud.basic.data.pojo.res.Network">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="cloud_env_id" property="cloudEnvId" jdbcType="BIGINT"/>
        <result column="network_name" property="networkName" jdbcType="VARCHAR"/>
        <result column="cloud_env_type" property="cloudEnvType" jdbcType="VARCHAR"/>
        <result column="cloud_env_name" property="cloudEnvName" jdbcType="VARCHAR"/>
        <result column="uuid" property="uuid" jdbcType="VARCHAR"/>
        <result column="cidr" property="cidr" jdbcType="VARCHAR"/>
        <result column="subnet" property="subnet" jdbcType="VARCHAR"/>
        <result column="mask" property="mask" jdbcType="VARCHAR"/>
        <result column="gateway" property="gateway" jdbcType="VARCHAR"/>
        <result column="dns1" property="dns1" jdbcType="VARCHAR"/>
        <result column="dns2" property="dns2" jdbcType="VARCHAR"/>
        <result column="vlan" property="vlan" jdbcType="VARCHAR"/>
        <result column="region" property="region" jdbcType="VARCHAR"/>
        <result column="zone" property="zone" jdbcType="VARCHAR"/>
        <result column="ip_segment" property="ipSegment" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="created_dt" property="createdDt" jdbcType="TIMESTAMP"/>
        <result column="updated_by" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="updated_dt" property="updatedDt" jdbcType="TIMESTAMP"/>
        <result column="used" property="used" jdbcType="BIGINT"/>
        <result column="unused" property="unused" jdbcType="VARCHAR"/>
        <result column="comments" property="comments" jdbcType="VARCHAR"/>
        <result column="res_target_id" property="resTargetId" jdbcType="VARCHAR"/>
        <result column="port_group_name" property="portGroupName" jdbcType="VARCHAR"/>
        <result column="PORT_GROUP_ID" property="portGroupId" jdbcType="VARCHAR"/>
        <result column="tenant_id" property="tenantId" jdbcType="VARCHAR"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
        <result column="net_vpc_id" property="netVpcId" jdbcType="BIGINT"/>
        <result column="vpc_name" property="vpcName" jdbcType="VARCHAR"/>
        <result column="network_uuid" property="networkUuid" jdbcType="VARCHAR"/>
        <result column="enable_dhcp" property="enableDhcp" jdbcType="BIGINT"/>
        <result column="used_ip" property="usedIp" jdbcType="BIGINT"/>
        <result column="total_ip" property="totalIp" jdbcType="BIGINT"/>
        <result column="router_id" property="routerId" jdbcType="BIGINT"/>
        <result column="vpc_devices_num" property="vpcDevicesNum" jdbcType="INTEGER"/>

        <result column="router_uuid" property="routerUuid" jdbcType="VARCHAR"/>
        <result column="router_name" property="routerName" jdbcType="VARCHAR"/>
        <result column="URN" property="urn" jdbcType="VARCHAR"/>
        <result column="URI" property="uri" jdbcType="VARCHAR"/>

        <result column="tag_names" property="tagNames" jdbcType="VARCHAR"/>
        <result column="tag_values" property="tagValues" jdbcType="VARCHAR"/>
        <result column="rgb_codes" property="rgbCodes" jdbcType="VARCHAR"/>
        <result column="shared" property="shared" jdbcType="BOOLEAN"/>
        <result column="share_to_env_ids" property="shareToEnvIds" jdbcType="VARCHAR"/>
        <result column="share_to_env_names" property="shareToEnvNames" jdbcType="VARCHAR"/>
        <result column="status_info" property="statusInfo" jdbcType="VARCHAR"/>
        <result column="net_vpc_uuid" property="netVpcUuid" jdbcType="VARCHAR"/>
        <result column="virt_type" property="virtType" jdbcType="VARCHAR"/>
        <result column="read_only" property="readOnly" jdbcType="BOOLEAN"/>
        <result column="origin_org_name" property="originOrgName" jdbcType="VARCHAR"/>
        <result column="loadData" property="loadData" jdbcType="VARCHAR"/>
        <result column="zone_name" property="zoneName" jdbcType="VARCHAR"/>
        <result column="owner" property="owner" jdbcType="TINYINT"/>
    </resultMap>
    <resultMap id="VpcSubnetCountResultMap" type="cn.com.cloudstar.rightcloud.core.pojo.dto.cloud.VpcSubnetCountVO">
        <result column="vpc_id" property="vpcId" jdbcType="BIGINT"/>
        <result column="subnet_count" property="subnetCount" jdbcType="INTEGER"/>

    </resultMap>

    <sql id="Example_Where_Clause">
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.networkIds != null">
                and A.id in
                <foreach collection="condition.networkIds" index="index" item="item" open="("
                    separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.networkId != null">
                and A.id = #{condition.networkId}
            </if>
            <if test="condition.networkIps != null">
                and C.ip_address in
                <foreach collection="condition.networkIps" index="index" item="item" open="("
                    separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.cloudEnvId != null">
                and A.cloud_env_id = #{condition.cloudEnvId}
            </if>
            <if test="condition.cloudEnvIdIn != null and condition.cloudEnvIdIn.size() > 0">
                and A.CLOUD_ENV_ID in
                <foreach collection="condition.cloudEnvIdIn" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="condition.tenantId != null">
                and A.tenant_id = #{condition.tenantId}
            </if>
            <if test="condition.networkName != null">
                and A.network_name = #{condition.networkName}
            </if>
            <if test="condition.nameLike != null">
                and A.network_name like concat('%',#{condition.nameLike}, '%')
            </if>
            <if test="condition.cidr != null">
                and A.cidr = #{condition.cidr}
            </if>
            <if test="condition.mask != null">
                and A.mask = #{condition.mask}
            </if>
            <if test="condition.subnet != null">
                and A.subnet = #{condition.subnet}
            </if>
            <if test="condition.gateway != null">
                and A.gateway = #{condition.gateway}
            </if>
            <if test="condition.dns1 != null">
                and A.dns1 = #{condition.dns1}
            </if>
            <if test="condition.dns2 != null">
                and A.dns2 = #{condition.dns2}
            </if>
            <if test="condition.ips != null">
                and A.ips = #{condition.ips}
            </if>
            <if test="condition.status != null">
                and A.status = #{condition.status}
            </if>
            <if test="condition.subnetStatus != null">
                and A.status = #{condition.subnetStatus}
            </if>
            <if test="condition.statusNotIn != null">
                and A.status not in
                <foreach item="item" index="index" collection="condition.statusNotIn"
                    open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.ipSegment != null">
                and A.ip_segment = #{condition.ipSegment}
            </if>
            <if test="condition.type != null">
                and A.type = #{condition.type}
            </if>
            <if test="condition.createdBy != null">
                and A.created_by = #{condition.createdBy}
            </if>
            <if test="condition.createdDt != null">
                and A.created_dt = #{condition.createdDt}
            </if>
            <if test="condition.updatedBy != null">
                and A.updated_by = #{condition.updatedBy}
            </if>
            <if test="condition.updatedDt != null">
                and A.updated_dt = #{condition.updatedDt}
            </if>
            <if test="condition.version != null">
                and A.version = #{condition.version}
            </if>
            <if test="condition.enableDhcp != null">
                and A.enable_dhcp = #{condition.enableDhcp}
            </if>
            <if test="condition.netVpcId != null and condition.netVpcId != -1">
                and A.net_vpc_id = #{condition.netVpcId}
            </if>
            <if test="condition.uuid != null">
                and A.uuid = #{condition.uuid}
            </if>
            <if test="condition.zone != null">
                and A.zone = #{condition.zone}
            </if>
            <if test="condition.zoneEqualOrNull != null">
                and (A.zone = #{condition.zoneEqualOrNull} or A.zone is null)
            </if>
            <if test="condition.zonesIn != null">
                and A.zone in
                <foreach collection="condition.zonesIn" index="index" item="item" open="("
                    separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.netVpcIds != null">
                and A.net_vpc_id in
                <foreach collection="condition.netVpcIds" index="index" item="item" open="("
                    separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.usedIp != null">
                and A.used_ip = #{condition.usedIp}
            </if>
            <if test="condition.totalIp != null">
                and A.total_ip = #{condition.totalIp}
            </if>
            <if test="condition.routerId != null">
                and A.router_id = #{condition.routerId}
            </if>
            <if test="condition.notRouterId != null">
                <![CDATA[
                and (A.router_id != #{condition.notRouterId} or A.router_id is null)
                 ]]>
            </if>
            <if test="condition.networkUuid != null">
                and A.network_uuid = #{condition.networkUuid}
            </if>
            <if test="condition.idNotEqual != null">
                and A.id != #{condition.idNotEqual}
            </if>
            <if test="condition.subnetRegion != null">
                and A.region = #{condition.subnetRegion}
            </if>
            <if test="condition.urn != null">
                and A.URN = #{condition.urn}
            </if>
            <if test="condition.uri != null">
                and A.URI = #{condition.uri}
            </if>
            <if test="condition.id != null">
                and A.id = #{condition.id}
            </if>
            <if test="condition.uuidIsNotNull != null">
                and A.uuid is not null
            </if>
            <if test="condition.external != null">
                <if test="condition.external == 'true'">
                    and E.external = #{condition.external}
                </if>
                <if test="condition.external == 'false'">
                    and (E.external = #{condition.external} or E.external is null)
                </if>
            </if>
            <if test="condition.zoneVirtTypeNotEq != null">
                and reszone.virt_type != #{condition.zoneVirtTypeNotEq}
            </if>
            <if test="condition.uuidsIn != null">
                and A.uuid in
                <foreach collection="condition.uuidsIn" index="index" item="item" open="("
                    separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.featureGroupId != null">
                AND A.uuid NOT IN (
                SELECT
                rffgt.target_id
                FROM
                res_flow_feature_group rffg,
                res_flow_feature_group_target rffgt
                WHERE
                rffg.cloud_env_id = #{condition.cloudEnvId}
                AND rffg.uuid = rffgt.feature_group_id
                AND rffg.type = 'subnet'
                AND rffg.id = #{condition.featureGroupId}
                )
            </if>
            <if test="condition.cloudEnvType != null">
                and (EXISTS (SELECT 1 FROM cloud_env WHERE (cloud_env_type LIKE
                CONCAT('%',#{condition.cloudEnvType},'%')) AND id = A.cloud_env_id))
            </if>
            <if test="condition.cloudEnvAccountId!= null">
                and (EXISTS (SELECT 1 FROM cloud_env WHERE cloud_env_account_id = #{condition.cloudEnvAccountId} AND id
                = A.cloud_env_id))
            </if>
        </trim>
    </sql>
    <sql id="Base_Column_List">
        A.id, A.cloud_env_id, A.network_name, A.cidr, A.mask, A.subnet, A.gateway, A.dns1, A.dns2,
        A.vlan, A.region,
        A.zone, A.ip_segment, A.type,
        A.status, A.created_by, A.created_dt, A.updated_by, A.updated_dt, A.version,comments,
        A.res_target_id, A.uuid,
        A.net_vpc_id, A.enable_dhcp, A.tenant_id, A.used_ip, A.total_ip, A.router_id,
        A.vpc_devices_num,A.network_uuid,A.urn,A.uri
    </sql>
    <select id="selectByParams" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        select * from (
        #属于自己的网络#
        select
        A.id,
        A.cloud_env_id,
        A.network_name,
        A.cidr,
        A.mask,
        A.subnet,
        A.gateway,
        A.dns1,
        A.dns2,
        A.vlan,
        reszone.name as region,
        reszone.virt_type,
        -- A.zone,
        Z.NAME AS zone,
        A.ip_segment,
        A.type,
        A.status,
        A.created_by,
        A.created_dt,
        A.updated_by,
        A.updated_dt,
        A.version,
        A.comments,
        A.uuid,
        A.net_vpc_id,
        E.name as vpc_name,
        A.enable_dhcp,
        A.tenant_id,
        A.router_id,
        SUM(CASE WHEN rv.status not in ('create_failure', 'pending', 'deleted') THEN 1 ELSE 0 END) AS vpc_devices_num,
        /*在此添加*/
        A.network_uuid,
        A.used_ip,
        A.total_ip,
        A.used_ip as used,
        (A.total_ip - A.used_ip) as unused,
        GROUP_CONCAT(I.tag_name ORDER BY I.tag_id) as TAG_NAMES,
        GROUP_CONCAT(I.rgb_code ORDER BY I.tag_id) as RGB_CODES,
        GROUP_CONCAT(I.tag_value ORDER BY I.tag_id) as TAG_VALUES,
        D.uuid as router_uuid,
        D.name as router_name,
        E.uuid as net_vpc_uuid,
        E.org_sid
        from network A
        LEFT JOIN res_vm_ext rve ON A.id = rve.resource_id AND rve.type = 'subnet'
        LEFT JOIN res_vm rv ON rv.id = rve.instance_id
        LEFT JOIN network_ip C on A.id = C.network_id
        LEFT JOIN res_router D on D.id = A.router_id
        LEFT JOIN res_vpc E on A.net_vpc_id = E.id
        LEFT JOIN res_zone reszone on E.res_zone_uuids = reszone.uuid
        LEFT JOIN cloud_tag_ref H on (A.id = H.obj_id and H.obj_type = 'subnet')
        LEFT JOIN cloud_tag I on (H.tag_id= I.tag_id)
        LEFT JOIN res_zone Z ON (z.cloud_env_id = A.cloud_env_id AND z.uuid =A.zone)
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        GROUP BY id, org_sid
        #属于自己的网络#
        UNION ALL
        #分配到的网络#
        select
        A.id,
        A.cloud_env_id,
        A.network_name,
        A.cidr,
        A.mask,
        A.subnet,
        A.gateway,
        A.dns1,
        A.dns2,
        A.vlan,
        reszone.name as region,
        reszone.virt_type,
        -- A.zone,
        Z.NAME AS zone,
        A.ip_segment,
        A.type,
        A.status,
        A.created_by,
        A.created_dt,
        A.updated_by,
        A.updated_dt,
        A.version,
        A.comments,
        A.uuid,
        A.net_vpc_id,
        E.name as vpc_name,
        A.enable_dhcp,
        A.tenant_id,
        A.router_id,
        A.vpc_devices_num,
        /*在此添加*/
        A.network_uuid,
        A.used_ip,
        A.total_ip,
        A.used_ip as used,
        (A.total_ip - A.used_ip) as unused,
        GROUP_CONCAT(I.tag_name ORDER BY I.tag_id) as TAG_NAMES,
        GROUP_CONCAT(I.rgb_code ORDER BY I.tag_id) as RGB_CODES,
        GROUP_CONCAT(I.tag_value ORDER BY I.tag_id) as TAG_VALUES,
        D.uuid as router_uuid,
        D.name as router_name,
        E.uuid as net_vpc_uuid,
        alloc.org_sid
        from network A
        INNER JOIN cloud_resource_alloc alloc ON A.id = alloc.target_id AND alloc.target_type = 'subnet'
        AND alloc.org_sid = #{condition.orgSid}
        LEFT JOIN network_ip C on A.id = C.network_id
        LEFT JOIN res_router D on D.id = A.router_id
        LEFT JOIN res_vpc E on A.net_vpc_id = E.id
        LEFT JOIN res_zone reszone on E.res_zone_uuids = reszone.uuid
        LEFT JOIN cloud_tag_ref H on (A.id = H.obj_id and H.obj_type = 'subnet')
        LEFT JOIN cloud_tag I on (H.tag_id= I.tag_id)
        LEFT JOIN res_zone Z ON (z.cloud_env_id = A.cloud_env_id AND z.uuid =A.zone)
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        GROUP BY id, org_sid
        #分配到的网络#
        ) A
        GROUP BY id
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>

    <select id="selectIdsByParams" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        select * from (
        #属于自己的网络#
        select
        A.id,
        A.network_name,
        A.cidr,
        E.org_sid
        from network A
        LEFT JOIN res_vpc E ON A.net_vpc_id = E.id
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        GROUP BY id, org_sid
        #属于自己的网络#
        UNION ALL
        #分配到的网络#
        select
        A.id,
        A.network_name,
        A.cidr,
        alloc.org_sid
        from network A
        INNER JOIN cloud_resource_alloc alloc ON A.id = alloc.target_id AND alloc.target_type = 'subnet'
        AND alloc.org_sid = #{condition.orgSid}
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        GROUP BY id, org_sid
        #分配到的网络#
        ) A
        GROUP BY id
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>

    <select id="selectVmNetworks" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        SELECT * FROM (
        SELECT
        <if test="distinct">
            DISTINCT
        </if>
        n.id, n.cloud_env_id, n.network_name, n.cidr, n.mask,n.subnet, n.gateway, n.dns1, n.dns2,
        n.vlan, n.ip_segment, n.type,
        n.status, n.created_by, n.created_dt, n.updated_by, n.updated_dt,
        n.version,n.comments, n.uuid,
        n.net_vpc_id,
        n.tenant_id,
        n.zone,
        IFNULL(n.total_ip, count(DISTINCT ni.id)) AS total_ip,
        IFNULL(n.used_ip, count(DISTINCT ni.id,IF(ni.`status` = 'used',ni.id,NULL))) AS used_ip,
        GROUP_CONCAT(I.tag_name ORDER BY I.tag_id) AS TAG_NAMES,
        GROUP_CONCAT(I.rgb_code ORDER BY I.tag_id) AS RGB_CODES,
        GROUP_CONCAT(I.tag_value ORDER BY I.tag_id) AS TAG_VALUES,
        GROUP_CONCAT(DISTINCT rp.name) AS pool,
        IFNULL(n.used_ip, count(DISTINCT ni.id,IF(ni.`status` = 'used',ni.id,NULL))) as used,
        IFNULL(n.total_ip - n.used_ip, count(DISTINCT ni.id,IF(ni.`status` = 'unused',ni.id,NULL))) as unused,
        M.org_sid AS org_sid,
        false AS shared,
        GROUP_CONCAT(DISTINCT J.id) AS share_to_env_ids,
        GROUP_CONCAT(DISTINCT J.cloud_env_name) AS share_to_env_names
        , K.status_info
        , M.NAME AS vpc_name
        , M.uuid AS net_vpc_uuid
        , GROUP_CONCAT(DISTINCT rvpg.name) AS port_group_name
        , GROUP_CONCAT(DISTINCT rvpg.uuid) AS port_group_id
        , ce.cloud_env_type
        , ce.cloud_env_name
        , M.owner
        FROM network n
        LEFT JOIN network_ip ni ON n.id = ni.network_id
        LEFT JOIN res_vs_port_group_network rvpgn ON n.id = rvpgn.network_id
        LEFT JOIN res_vs_port_group rvpg ON rvpg.id = rvpgn.res_vs_port_group_id
        LEFT JOIN res_pool_resource rpr ON (rpr.resource_sid = rvpgn.id AND rpr.resource_type='RES-NETWORK')
        LEFT JOIN res_pool rp ON rpr.res_pool_id = rp.id
        INNER JOIN cloud_env ce ON ce.id = n.cloud_env_id
        INNER JOIN cloud_env_account cea ON cea.id = ce.cloud_env_account_id
        LEFT JOIN cloud_tag_ref H ON (n.id = H.obj_id AND H.obj_type = 'subnet')
        LEFT JOIN cloud_tag I ON (H.tag_id= I.tag_id)
        LEFT JOIN network_alloc G ON n.id = G.network_id
        LEFT JOIN cloud_env J ON G.cloud_env_id = J.id
        LEFT JOIN res_status_info K ON n.id = K.res_sid AND K.res_type = 'network'
        LEFT JOIN res_vpc M ON n.net_vpc_id = M.id
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.networkIds != null">
                AND n.id IN
                <foreach collection="condition.networkIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.cloudEnvId != null">
                AND n.cloud_env_id = #{condition.cloudEnvId}
            </if>
            <if test="condition.cloudEnvIds != null">
                AND n.cloud_env_id IN
                <foreach collection="condition.cloudEnvIds" open="(" close=")" separator="," item="cloudEnvId">
                    #{cloudEnvId}
                </foreach>
            </if>
            <if test="condition.cloudEnvIdIn != null">
                AND n.cloud_env_id IN
                <foreach collection="condition.cloudEnvIdIn" open="(" close=")" separator="," item="cloudEnvId">
                    #{cloudEnvId}
                </foreach>
            </if>
            <if test="condition.netVpcId != null">
                AND n.net_vpc_id = #{condition.netVpcId}
            </if>
            <if test="condition.status != null">
                AND n.status = #{condition.status}
            </if>
            <if test="condition.poolId != null">
                AND rpr.res_pool_id = #{condition.poolId}
            </if>
            <if test="condition.poolName != null">
                AND rp.name = #{condition.poolName}
            </if>
            <if test="condition.networkUuids != null">
                AND n.uuid IN
                <foreach item="item" index="index" collection="condition.networkUuids"
                    open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.nameLike != null">
                AND n.network_name LIKE concat('%',#{condition.nameLike}, '%')
            </if>
            <if test="condition.netVpcIds != null">
                AND n.net_vpc_id IN
                <foreach collection="condition.netVpcIds" index="index" item="item" open="("
                    separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.idNotEqual != null">
                AND n.id != #{condition.idNotEqual}
            </if>
            <if test="condition.external != null">
                AND M.external = #{condition.external}
            </if>
            <if test="condition.shared != null">
                AND M.shared = #{condition.shared}
            </if>
            <if test="condition.vpcName != null">
                AND M.name like concat(#{condition.vpcName}, "%")
            </if>
            <if test="condition.routeId != null">
                AND NOT EXISTS
                (SELECT 1 FROM res_router_interface ri
                LEFT JOIN network ni ON ni.uuid = ri.network_uuid
                WHERE ri.router_id != #{condition.routeId}
                AND ri.cloud_env_id = #{condition.cloudEnvId}
                AND ni.net_vpc_id = M.id)
            </if>
            <if test="condition.zoneNotIn != null">
                AND M.res_zone_uuids NOT IN
                <foreach collection="condition.zoneNotIn" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </trim>
        GROUP BY id, M.org_sid
        UNION ALL
        SELECT
        <if test="distinct">
            DISTINCT
        </if>
        n.id, n.cloud_env_id, n.network_name, n.cidr, n.mask,n.subnet, n.gateway, n.dns1, n.dns2,
        n.vlan, n.ip_segment, n.type,
        n.status, n.created_by, n.created_dt, n.updated_by, n.updated_dt,
        n.version,n.comments, n.uuid,
        n.net_vpc_id, n.tenant_id,
        n.zone,
        IFNULL(n.total_ip, COUNT(DISTINCT ni.id)) AS total_ip,
        IFNULL(n.used_ip, COUNT(DISTINCT ni.id,IF(ni.`status` = 'used',ni.id,NULL))) AS used_ip,
        GROUP_CONCAT(I.tag_name ORDER BY I.tag_id) AS TAG_NAMES,
        GROUP_CONCAT(I.rgb_code ORDER BY I.tag_id) AS RGB_CODES,
        GROUP_CONCAT(I.tag_value ORDER BY I.tag_id) AS TAG_VALUES,
        '' AS pool,
        COUNT(DISTINCT ni.id,IF(ni.`status` = 'used',ni.id,NULL)) AS used,
        COUNT(DISTINCT ni.id,IF(ni.`status` = 'unused',ni.id,NULL)) AS unused,
        E.org_sid AS org_sid,
        true AS shared,
        GROUP_CONCAT(DISTINCT J.id) AS share_to_env_ids,
        GROUP_CONCAT(DISTINCT J.cloud_env_name) AS share_to_env_names
        , M.status_info
        , L.name AS vpc_name
        , L.uuid AS net_vpc_uuid
        , GROUP_CONCAT(DISTINCT rvpg.name) AS port_group_name
        , GROUP_CONCAT(DISTINCT rvpg.uuid) AS port_group_id
        , D.cloud_env_type
        , D.cloud_env_name
        , L.owner
        FROM network n
        LEFT JOIN network_ip ni ON n.id = ni.network_id
        LEFT JOIN res_vs_port_group_network rvpgn ON n.id = rvpgn.network_id
        LEFT JOIN res_vs_port_group rvpg ON rvpg.id = rvpgn.res_vs_port_group_id
        INNER JOIN network_alloc C ON n.id = C.network_id
        INNER JOIN cloud_env D ON C.cloud_env_id = D.id
        INNER JOIN cloud_env_account E ON D.cloud_env_account_id = E.id
        LEFT JOIN cloud_tag_ref H ON (n.id = H.obj_id AND H.obj_type = 'subnet')
        LEFT JOIN cloud_tag I ON (H.tag_id= I.tag_id)
        LEFT JOIN network_alloc K ON K.network_id = n.id
        LEFT JOIN cloud_env J ON K.cloud_env_id = J.id
        LEFT JOIN res_status_info M ON n.id = M.res_sid AND M.res_type = 'network'
        LEFT JOIN res_vpc L ON n.net_vpc_id = L.id
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.networkIds != null">
                AND n.id IN
                <foreach collection="condition.networkIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.cloudEnvId != null">
                AND C.cloud_env_id = #{condition.cloudEnvId}
            </if>
            <if test="condition.cloudEnvIds != null">
                AND C.cloud_env_id IN
                <foreach collection="condition.cloudEnvIds" open="(" close=")" separator="," item="cloudEnvId">
                    #{cloudEnvId}
                </foreach>
            </if>
            <if test="condition.cloudEnvIdIn != null">
                AND C.cloud_env_id IN
                <foreach collection="condition.cloudEnvIdIn" open="(" close=")" separator="," item="cloudEnvId">
                    #{cloudEnvId}
                </foreach>
            </if>
            <if test="condition.netVpcId != null">
                AND n.net_vpc_id = #{condition.netVpcId}
            </if>
            <if test="condition.status != null">
                AND n.status = #{condition.status}
            </if>
            <if test="condition.networkUuids != null">
                AND n.uuid IN
                <foreach item="item" index="index" collection="condition.networkUuids"
                    open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.nameLike != null">
                AND n.network_name LIKE concat('%',#{condition.nameLike}, '%')
            </if>
            <if test="condition.netVpcIds != null">
                AND n.net_vpc_id IN
                <foreach collection="condition.netVpcIds" index="index" item="item" open="("
                    separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.idNotEqual != null">
                AND n.id != #{condition.idNotEqual}
            </if>
            <if test="condition.external != null">
                AND L.external = #{condition.external}
            </if>
            <if test="condition.shared != null">
                AND L.shared = #{condition.shared}
            </if>
            <if test="condition.vpcName != null">
                AND L.name like concat(#{condition.vpcName}, "%")
            </if>
            <if test="condition.routeId != null">
                AND NOT EXISTS
                (SELECT 1 FROM res_router_interface ri
                LEFT JOIN network ni ON ni.uuid = ri.network_uuid
                WHERE ri.router_id != #{condition.routeId}
                AND ri.cloud_env_id = #{condition.cloudEnvId}
                AND ni.net_vpc_id = L.id)
            </if>
        </trim>
        GROUP BY id, E.org_sid
        UNION ALL
        SELECT
        <if test="distinct">
            DISTINCT
        </if>
        n.id, n.cloud_env_id, n.network_name, n.cidr, n.mask,n.subnet, n.gateway, n.dns1, n.dns2,
        n.vlan, n.ip_segment, n.type,
        n.status, n.created_by, n.created_dt, n.updated_by, n.updated_dt,
        n.version,n.comments, n.uuid,
        n.net_vpc_id, n.tenant_id,
        n.zone,
        IFNULL(n.total_ip, count(DISTINCT ni.id)) AS total_ip,
        IFNULL(n.used_ip, count(DISTINCT ni.id,IF(ni.`status` = 'used',ni.id,null))) AS used_ip,
        GROUP_CONCAT(I.tag_name ORDER BY I.tag_id) AS TAG_NAMES,
        GROUP_CONCAT(I.rgb_code ORDER BY I.tag_id) AS RGB_CODES,
        GROUP_CONCAT(I.tag_value ORDER BY I.tag_id) AS TAG_VALUES,
        '' AS pool,
        COUNT(DISTINCT ni.id,IF(ni.`status` = 'used',ni.id,null)) AS used,
        COUNT(DISTINCT ni.id,IF(ni.`status` = 'unused',ni.id,null)) AS unused,
        C.org_sid AS org_sid,
        TRUE AS shared,
        GROUP_CONCAT(DISTINCT J.id) AS share_to_env_ids,
        GROUP_CONCAT(DISTINCT J.cloud_env_name) AS share_to_env_names
        , M.status_info
        , L.name AS vpc_name
        , L.uuid AS net_vpc_uuid
        , GROUP_CONCAT(DISTINCT rvpg.name) AS port_group_name
        , GROUP_CONCAT(DISTINCT rvpg.uuid) AS port_group_id
        , J.cloud_env_type
        , J.cloud_env_name
        , L.owner
        FROM network n
        LEFT JOIN network_ip ni ON n.id = ni.network_id
        LEFT JOIN res_vs_port_group_network rvpgn ON n.id = rvpgn.network_id
        LEFT JOIN res_vs_port_group rvpg ON rvpg.id = rvpgn.res_vs_port_group_id
        INNER JOIN cloud_resource_alloc C ON n.id = C.target_id AND C.target_type = 'subnet' AND C.org_sid =
        #{condition.currentOrgSid}
        LEFT JOIN cloud_tag_ref H ON (n.id = H.obj_id AND H.obj_type = 'subnet')
        LEFT JOIN cloud_tag I ON (H.tag_id= I.tag_id)
        LEFT JOIN network_alloc K ON K.network_id = n.id
        LEFT JOIN cloud_env J ON K.cloud_env_id = J.id
        LEFT JOIN res_status_info M ON n.id = M.res_sid AND M.res_type = 'network'
        LEFT JOIN res_vpc L ON n.net_vpc_id = L.id
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.networkIds != null">
                AND n.id IN
                <foreach collection="condition.networkIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.cloudEnvId != null">
                AND n.cloud_env_id = #{condition.cloudEnvId}
            </if>
            <if test="condition.cloudEnvIds != null">
                AND n.cloud_env_id IN
                <foreach collection="condition.cloudEnvIds" open="(" close=")" separator="," item="cloudEnvId">
                    #{cloudEnvId}
                </foreach>
            </if>
            <if test="condition.cloudEnvIdIn != null">
                AND n.cloud_env_id IN
                <foreach collection="condition.cloudEnvIdIn" open="(" close=")" separator="," item="cloudEnvId">
                    #{cloudEnvId}
                </foreach>
            </if>
            <if test="condition.netVpcId != null">
                AND n.net_vpc_id = #{condition.netVpcId}
            </if>
            <if test="condition.status != null">
                AND n.status = #{condition.status}
            </if>
            <if test="condition.networkUuids != null">
                AND n.uuid IN
                <foreach item="item" index="index" collection="condition.networkUuids"
                    open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.nameLike != null">
                AND n.network_name LIKE concat('%',#{condition.nameLike}, '%')
            </if>
            <if test="condition.netVpcIds != null">
                AND n.net_vpc_id IN
                <foreach collection="condition.netVpcIds" index="index" item="item" open="("
                    separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.idNotEqual != null">
                AND n.id != #{condition.idNotEqual}
            </if>
            <if test="condition.external != null">
                AND L.external = #{condition.external}
            </if>
            <if test="condition.shared != null">
                AND L.shared = #{condition.shared}
            </if>
            <if test="condition.vpcName != null">
                AND L.name like concat(#{condition.vpcName}, "%")
            </if>
            <if test="condition.routeId != null">
                AND NOT EXISTS
                (SELECT 1 FROM res_router_interface ri
                LEFT JOIN network ni ON ni.uuid = ri.network_uuid
                WHERE ri.router_id != #{condition.routeId}
                AND ri.cloud_env_id = #{condition.cloudEnvId}
                AND ni.net_vpc_id = L.id)
            </if>
        </trim>
        GROUP BY id, C.org_sid
        ) T
        GROUP BY T.id
        <if test="orderByClause != null">
            ORDER BY ${orderByClause}
        </if>
    </select>
    <select id="selectVmNetworkNames" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        SELECT * FROM (
        SELECT
        <if test="distinct">
            DISTINCT
        </if>
        n.id,
        n.network_name,
        n.cidr,
        M.org_sid AS org_sid
        FROM network n
        INNER JOIN cloud_env ce ON ce.id = n.cloud_env_id
        INNER JOIN cloud_env_account cea ON cea.id = ce.cloud_env_account_id
        LEFT JOIN res_vpc M ON n.net_vpc_id = M.id
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.networkIds != null">
                AND n.id IN
                <foreach collection="condition.networkIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.cloudEnvId != null">
                AND n.cloud_env_id = #{condition.cloudEnvId}
            </if>
            <if test="condition.cloudEnvIds != null">
                AND n.cloud_env_id IN
                <foreach collection="condition.cloudEnvIds" open="(" close=")" separator="," item="cloudEnvId">
                    #{cloudEnvId}
                </foreach>
            </if>
            <if test="condition.netVpcId != null">
                AND n.net_vpc_id = #{condition.netVpcId}
            </if>
            <if test="condition.status != null">
                AND n.status = #{condition.status}
            </if>
            <if test="condition.poolId != null">
                AND rpr.res_pool_id = #{condition.poolId}
            </if>
            <if test="condition.poolName != null">
                AND rp.name = #{condition.poolName}
            </if>
            <if test="condition.networkUuids != null">
                AND n.uuid IN
                <foreach item="item" index="index" collection="condition.networkUuids"
                    open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.nameLike != null">
                AND n.network_name LIKE concat('%',#{condition.nameLike}, '%')
            </if>
            <if test="condition.netVpcIds != null">
                AND n.net_vpc_id IN
                <foreach collection="condition.netVpcIds" index="index" item="item" open="("
                    separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.netVpcId != null">
                AND n.net_vpc_id = #{condition.netVpcId}
            </if>
            <if test="condition.idNotEqual != null">
                AND n.id != #{condition.idNotEqual}
            </if>
            <if test="condition.external != null">
                AND M.external = #{condition.external}
            </if>
            <if test="condition.shared != null">
                AND M.shared = #{condition.shared}
            </if>
            <if test="condition.routeId != null">
                AND NOT EXISTS
                (SELECT 1 FROM res_router_interface ri
                LEFT JOIN network ni ON ni.uuid = ri.network_uuid
                WHERE ri.router_id != #{condition.routeId}
                AND ri.cloud_env_id = #{condition.cloudEnvId}
                AND ni.net_vpc_id = M.id)
            </if>
            <if test="condition.zoneNotIn != null">
                AND M.res_zone_uuids NOT IN
                <foreach collection="condition.zoneNotIn" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </trim>
        GROUP BY id, M.org_sid
        UNION ALL
        SELECT
        <if test="distinct">
            DISTINCT
        </if>
        n.id,
        n.network_name,
        n.cidr,
        E.org_sid AS org_sid
        FROM network n
        INNER JOIN network_ip ni ON n.id = ni.network_id
        INNER JOIN network_alloc C ON n.id = C.network_id
        INNER JOIN cloud_env D ON C.cloud_env_id = D.id
        INNER JOIN cloud_env_account E ON D.cloud_env_account_id = E.id
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.networkIds != null">
                AND n.id IN
                <foreach collection="condition.networkIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.cloudEnvId != null">
                AND C.cloud_env_id = #{condition.cloudEnvId}
            </if>
            <if test="condition.cloudEnvIds != null">
                AND C.cloud_env_id IN
                <foreach collection="condition.cloudEnvIds" open="(" close=")" separator="," item="cloudEnvId">
                    #{cloudEnvId}
                </foreach>
            </if>
            <if test="condition.netVpcId != null">
                AND n.net_vpc_id = #{condition.netVpcId}
            </if>
            <if test="condition.status != null">
                AND n.status = #{condition.status}
            </if>
            <if test="condition.networkUuids != null">
                AND n.uuid IN
                <foreach item="item" index="index" collection="condition.networkUuids"
                    open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.nameLike != null">
                AND n.network_name LIKE concat('%',#{condition.nameLike}, '%')
            </if>
            <if test="condition.netVpcIds != null">
                AND n.net_vpc_id IN
                <foreach collection="condition.netVpcIds" index="index" item="item" open="("
                    separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.netVpcId != null">
                AND n.net_vpc_id = #{condition.netVpcId}
            </if>
            <if test="condition.idNotEqual != null">
                AND n.id != #{condition.idNotEqual}
            </if>
            <if test="condition.external != null">
                AND L.external = #{condition.external}
            </if>
            <if test="condition.shared != null">
                AND L.shared = #{condition.shared}
            </if>
            <if test="condition.routeId != null">
                AND NOT EXISTS
                (SELECT 1 FROM res_router_interface ri
                LEFT JOIN network ni ON ni.uuid = ri.network_uuid
                WHERE ri.router_id != #{condition.routeId}
                AND ri.cloud_env_id = #{condition.cloudEnvId}
                AND ni.net_vpc_id = L.id)
            </if>
        </trim>
        GROUP BY id, E.org_sid
        UNION ALL
        SELECT
        <if test="distinct">
            DISTINCT
        </if>
        n.id,
        n.network_name,
        n.cidr,
        C.org_sid AS org_sid
        FROM network n
        INNER JOIN network_ip ni ON n.id = ni.network_id
        INNER JOIN cloud_resource_alloc C ON n.id = C.target_id AND C.target_type = 'subnet' AND C.org_sid =
        #{condition.currentOrgSid}
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.networkIds != null">
                AND n.id IN
                <foreach collection="condition.networkIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.cloudEnvId != null">
                AND n.cloud_env_id = #{condition.cloudEnvId}
            </if>
            <if test="condition.cloudEnvIds != null">
                AND n.cloud_env_id IN
                <foreach collection="condition.cloudEnvIds" open="(" close=")" separator="," item="cloudEnvId">
                    #{cloudEnvId}
                </foreach>
            </if>
            <if test="condition.netVpcId != null">
                AND n.net_vpc_id = #{condition.netVpcId}
            </if>
            <if test="condition.status != null">
                AND n.status = #{condition.status}
            </if>
            <if test="condition.networkUuids != null">
                AND n.uuid IN
                <foreach item="item" index="index" collection="condition.networkUuids"
                    open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.nameLike != null">
                AND n.network_name LIKE concat('%',#{condition.nameLike}, '%')
            </if>
            <if test="condition.netVpcIds != null">
                AND n.net_vpc_id IN
                <foreach collection="condition.netVpcIds" index="index" item="item" open="("
                    separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.netVpcId != null">
                AND n.net_vpc_id = #{condition.netVpcId}
            </if>
            <if test="condition.idNotEqual != null">
                AND n.id != #{condition.idNotEqual}
            </if>
            <if test="condition.external != null">
                AND L.external = #{condition.external}
            </if>
            <if test="condition.shared != null">
                AND L.shared = #{condition.shared}
            </if>
            <if test="condition.routeId != null">
                AND NOT EXISTS
                (SELECT 1 FROM res_router_interface ri
                LEFT JOIN network ni ON ni.uuid = ri.network_uuid
                WHERE ri.router_id != #{condition.routeId}
                AND ri.cloud_env_id = #{condition.cloudEnvId}
                AND ni.net_vpc_id = L.id)
            </if>
        </trim>
        GROUP BY id, C.org_sid
        ) T
        GROUP BY T.id
        <if test="orderByClause != null">
            ORDER BY ${orderByClause}
        </if>
    </select>

    <select id="countVmNetworks" resultType="java.lang.Integer"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        select count(*) from (
        SELECT * FROM (
        SELECT
        <if test="distinct">
            DISTINCT
        </if>
        n.id,
        M.org_sid AS org_sid
        FROM network n
        INNER JOIN cloud_env ce ON ce.id = n.cloud_env_id
        INNER JOIN cloud_env_account cea ON cea.id = ce.cloud_env_account_id
        LEFT JOIN res_vpc M ON n.net_vpc_id = M.id
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.networkIds != null">
                AND n.id IN
                <foreach collection="condition.networkIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.cloudEnvId != null">
                AND n.cloud_env_id = #{condition.cloudEnvId}
            </if>
            <if test="condition.cloudEnvIds != null">
                AND n.cloud_env_id IN
                <foreach collection="condition.cloudEnvIds" open="(" close=")" separator="," item="cloudEnvId">
                    #{cloudEnvId}
                </foreach>
            </if>
            <if test="condition.netVpcId != null">
                AND n.net_vpc_id = #{condition.netVpcId}
            </if>
            <if test="condition.status != null">
                AND n.status = #{condition.status}
            </if>
            <if test="condition.poolId != null">
                AND rpr.res_pool_id = #{condition.poolId}
            </if>
            <if test="condition.poolName != null">
                AND rp.name = #{condition.poolName}
            </if>
            <if test="condition.networkUuids != null">
                AND n.uuid IN
                <foreach item="item" index="index" collection="condition.networkUuids"
                    open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.nameLike != null">
                AND n.network_name LIKE concat('%',#{condition.nameLike}, '%')
            </if>
            <if test="condition.netVpcIds != null">
                AND n.net_vpc_id IN
                <foreach collection="condition.netVpcIds" index="index" item="item" open="("
                    separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.netVpcId != null">
                AND n.net_vpc_id = #{condition.netVpcId}
            </if>
            <if test="condition.idNotEqual != null">
                AND n.id != #{condition.idNotEqual}
            </if>
            <if test="condition.external != null">
                AND M.external = #{condition.external}
            </if>
            <if test="condition.shared != null">
                AND M.shared = #{condition.shared}
            </if>
            <if test="condition.routeId != null">
                AND NOT EXISTS
                (SELECT 1 FROM res_router_interface ri
                LEFT JOIN network ni ON ni.uuid = ri.network_uuid
                WHERE ri.router_id != #{condition.routeId}
                AND ri.cloud_env_id = #{condition.cloudEnvId}
                AND ni.net_vpc_id = M.id)
            </if>
            <if test="condition.zoneNotIn != null">
                AND M.res_zone_uuids NOT IN
                <foreach collection="condition.zoneNotIn" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </trim>
        GROUP BY id, M.org_sid
        UNION ALL
        SELECT
        <if test="distinct">
            DISTINCT
        </if>
        n.id,
        E.org_sid AS org_sid
        FROM network n
        INNER JOIN network_ip ni ON n.id = ni.network_id
        INNER JOIN network_alloc C ON n.id = C.network_id
        INNER JOIN cloud_env D ON C.cloud_env_id = D.id
        INNER JOIN cloud_env_account E ON D.cloud_env_account_id = E.id
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.networkIds != null">
                AND n.id IN
                <foreach collection="condition.networkIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.cloudEnvId != null">
                AND C.cloud_env_id = #{condition.cloudEnvId}
            </if>
            <if test="condition.cloudEnvIds != null">
                AND C.cloud_env_id IN
                <foreach collection="condition.cloudEnvIds" open="(" close=")" separator="," item="cloudEnvId">
                    #{cloudEnvId}
                </foreach>
            </if>
            <if test="condition.netVpcId != null">
                AND n.net_vpc_id = #{condition.netVpcId}
            </if>
            <if test="condition.status != null">
                AND n.status = #{condition.status}
            </if>
            <if test="condition.networkUuids != null">
                AND n.uuid IN
                <foreach item="item" index="index" collection="condition.networkUuids"
                    open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.nameLike != null">
                AND n.network_name LIKE concat('%',#{condition.nameLike}, '%')
            </if>
            <if test="condition.netVpcIds != null">
                AND n.net_vpc_id IN
                <foreach collection="condition.netVpcIds" index="index" item="item" open="("
                    separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.netVpcId != null">
                AND n.net_vpc_id = #{condition.netVpcId}
            </if>
            <if test="condition.idNotEqual != null">
                AND n.id != #{condition.idNotEqual}
            </if>
            <if test="condition.external != null">
                AND L.external = #{condition.external}
            </if>
            <if test="condition.shared != null">
                AND L.shared = #{condition.shared}
            </if>
            <if test="condition.routeId != null">
                AND NOT EXISTS
                (SELECT 1 FROM res_router_interface ri
                LEFT JOIN network ni ON ni.uuid = ri.network_uuid
                WHERE ri.router_id != #{condition.routeId}
                AND ri.cloud_env_id = #{condition.cloudEnvId}
                AND ni.net_vpc_id = L.id)
            </if>
        </trim>
        GROUP BY id, E.org_sid
        UNION ALL
        SELECT
        <if test="distinct">
            DISTINCT
        </if>
        n.id,
        C.org_sid AS org_sid
        FROM network n
        INNER JOIN network_ip ni ON n.id = ni.network_id
        INNER JOIN cloud_resource_alloc C ON n.id = C.target_id AND C.target_type = 'subnet' AND C.org_sid =
        #{condition.currentOrgSid}
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.networkIds != null">
                AND n.id IN
                <foreach collection="condition.networkIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.cloudEnvId != null">
                AND n.cloud_env_id = #{condition.cloudEnvId}
            </if>
            <if test="condition.cloudEnvIds != null">
                AND n.cloud_env_id IN
                <foreach collection="condition.cloudEnvIds" open="(" close=")" separator="," item="cloudEnvId">
                    #{cloudEnvId}
                </foreach>
            </if>
            <if test="condition.netVpcId != null">
                AND n.net_vpc_id = #{condition.netVpcId}
            </if>
            <if test="condition.status != null">
                AND n.status = #{condition.status}
            </if>
            <if test="condition.networkUuids != null">
                AND n.uuid IN
                <foreach item="item" index="index" collection="condition.networkUuids"
                    open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.nameLike != null">
                AND n.network_name LIKE concat('%',#{condition.nameLike}, '%')
            </if>
            <if test="condition.netVpcIds != null">
                AND n.net_vpc_id IN
                <foreach collection="condition.netVpcIds" index="index" item="item" open="("
                    separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.netVpcId != null">
                AND n.net_vpc_id = #{condition.netVpcId}
            </if>
            <if test="condition.idNotEqual != null">
                AND n.id != #{condition.idNotEqual}
            </if>
            <if test="condition.external != null">
                AND L.external = #{condition.external}
            </if>
            <if test="condition.shared != null">
                AND L.shared = #{condition.shared}
            </if>
            <if test="condition.routeId != null">
                AND NOT EXISTS
                (SELECT 1 FROM res_router_interface ri
                LEFT JOIN network ni ON ni.uuid = ri.network_uuid
                WHERE ri.router_id != #{condition.routeId}
                AND ri.cloud_env_id = #{condition.cloudEnvId}
                AND ni.net_vpc_id = L.id)
            </if>
        </trim>
        GROUP BY id, C.org_sid
        ) T
        ) A
        <where>
            <if test="condition.df != null">
                ${condition.df}
            </if>
        </where>
    </select>

    <select id="selectSimpleVmNetworks" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        SELECT * FROM (
        SELECT
        <if test="distinct">
            DISTINCT
        </if>
        n.id,#
        IFNULL(n.total_ip, COUNT( DISTINCT ni.id )) AS total_ip,#
        IFNULL(
        n.used_ip,
        COUNT( DISTINCT ni.id, IF ( ni.`status` = 'used', ni.id, NULL ) )
        ) AS used_ip,
        M.org_sid AS org_sid
        ,n.network_name
        ,n.cidr
        FROM
        network n
        INNER JOIN network_ip ni ON n.id = ni.network_id
        INNER JOIN res_vpc M ON n.net_vpc_id = M.id
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.networkIds != null">
                AND n.id IN
                <foreach collection="condition.networkIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.cloudEnvId != null">
                AND n.cloud_env_id = #{condition.cloudEnvId}
            </if>
            <if test="condition.cloudEnvIds != null">
                AND n.cloud_env_id IN
                <foreach collection="condition.cloudEnvIds" open="(" close=")" separator="," item="cloudEnvId">
                    #{cloudEnvId}
                </foreach>
            </if>
            <if test="condition.netVpcId != null">
                AND n.net_vpc_id = #{condition.netVpcId}
            </if>
            <if test="condition.status != null">
                AND n.status = #{condition.status}
            </if>
            <if test="condition.networkUuids != null">
                AND n.uuid IN
                <foreach item="item" index="index" collection="condition.networkUuids"
                    open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.nameLike != null">
                AND n.network_name LIKE concat('%',#{condition.nameLike}, '%')
            </if>
            <if test="condition.netVpcIds != null">
                AND n.net_vpc_id IN
                <foreach collection="condition.netVpcIds" index="index" item="item" open="("
                    separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.netVpcId != null">
                AND n.net_vpc_id = #{condition.netVpcId}
            </if>
            <if test="condition.idNotEqual != null">
                AND n.id != #{condition.idNotEqual}
            </if>
            <if test="condition.external != null">
                AND M.external = #{condition.external}
            </if>
            <if test="condition.shared != null">
                AND M.shared = #{condition.shared}
            </if>
        </trim>
        GROUP BY id, M.org_sid
        UNION ALL
        SELECT
        <if test="distinct">
            DISTINCT
        </if>
        n.id,
        IFNULL( n.total_ip, COUNT( DISTINCT ni.id ) ) AS total_ip,
        IFNULL(
        n.used_ip,
        COUNT( DISTINCT ni.id, IF ( ni.`status` = 'used', ni.id, NULL ) )
        ) AS used_ip,
        E.org_sid AS org_sid
        ,n.network_name
        ,n.cidr
        FROM
        network n
        INNER JOIN network_ip ni ON n.id = ni.network_id
        INNER JOIN network_alloc C ON n.id = C.network_id
        INNER JOIN cloud_env D ON C.cloud_env_id = D.id
        INNER JOIN cloud_env_account E ON D.cloud_env_account_id = E.id
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.networkIds != null">
                AND n.id IN
                <foreach collection="condition.networkIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.cloudEnvId != null">
                AND C.cloud_env_id = #{condition.cloudEnvId}
            </if>
            <if test="condition.cloudEnvIds != null">
                AND C.cloud_env_id IN
                <foreach collection="condition.cloudEnvIds" open="(" close=")" separator="," item="cloudEnvId">
                    #{cloudEnvId}
                </foreach>
            </if>
            <if test="condition.netVpcId != null">
                AND n.net_vpc_id = #{condition.netVpcId}
            </if>
            <if test="condition.status != null">
                AND n.status = #{condition.status}
            </if>
            <if test="condition.networkUuids != null">
                AND n.uuid IN
                <foreach item="item" index="index" collection="condition.networkUuids"
                    open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.nameLike != null">
                AND n.network_name LIKE concat('%',#{condition.nameLike}, '%')
            </if>
            <if test="condition.netVpcIds != null">
                AND n.net_vpc_id IN
                <foreach collection="condition.netVpcIds" index="index" item="item" open="("
                    separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.netVpcId != null">
                AND n.net_vpc_id = #{condition.netVpcId}
            </if>
            <if test="condition.idNotEqual != null">
                AND n.id != #{condition.idNotEqual}
            </if>
        </trim>
        GROUP BY id, E.org_sid
        UNION ALL
        SELECT
        <if test="distinct">
            DISTINCT
        </if>
        n.id,
        IFNULL( n.total_ip, COUNT( DISTINCT ni.id ) ) AS total_ip,
        IFNULL(
        n.used_ip,
        COUNT( DISTINCT ni.id, IF ( ni.`status` = 'used', ni.id, NULL ) )
        ) AS used_ip,
        C.org_sid AS org_sid
        ,n.network_name
        ,n.cidr
        FROM
        network n
        INNER JOIN network_ip ni ON n.id = ni.network_id
        INNER JOIN cloud_resource_alloc C ON n.id = C.target_id AND C.target_type = 'subnet'
        <if test="condition.allocTargetId != null">
            AND C.org_sid = #{condition.allocTargetId}
        </if>
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.networkIds != null">
                AND n.id IN
                <foreach collection="condition.networkIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.cloudEnvId != null">
                AND n.cloud_env_id = #{condition.cloudEnvId}
            </if>
            <if test="condition.cloudEnvIds != null">
                AND n.cloud_env_id IN
                <foreach collection="condition.cloudEnvIds" open="(" close=")" separator="," item="cloudEnvId">
                    #{cloudEnvId}
                </foreach>
            </if>
            <if test="condition.netVpcId != null">
                AND n.net_vpc_id = #{condition.netVpcId}
            </if>
            <if test="condition.status != null">
                AND n.status = #{condition.status}
            </if>
            <if test="condition.networkUuids != null">
                AND n.uuid IN
                <foreach item="item" index="index" collection="condition.networkUuids"
                    open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.nameLike != null">
                AND n.network_name LIKE concat('%',#{condition.nameLike}, '%')
            </if>
            <if test="condition.netVpcIds != null">
                AND n.net_vpc_id IN
                <foreach collection="condition.netVpcIds" index="index" item="item" open="("
                    separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.netVpcId != null">
                AND n.net_vpc_id = #{condition.netVpcId}
            </if>
            <if test="condition.idNotEqual != null">
                AND n.id != #{condition.idNotEqual}
            </if>
        </trim>
        GROUP BY id, C.org_sid
        ) T
        GROUP BY T.id
        <if test="orderByClause != null">
            ORDER BY ${orderByClause}
        </if>
    </select>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from network A
        where A.id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectInfoByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        , B.cloud_env_type
        , count(DISTINCT C.id,IF(C.`status` = 'used',C.id,null)) as used
        , count(DISTINCT C.id,IF(C.`status` = 'unused',C.id,null)) as unused
        , D.name AS zone_name
        , E.name AS vpc_name
        from network A
        LEFT JOIN cloud_env B on A.cloud_env_id = B.id
        LEFT JOIN network_ip C on A.id = C.network_id
        LEFT JOIN res_zone D ON A.zone = D.uuid
        LEFT JOIN res_vpc E ON A.net_vpc_id = E.id
        where A.id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectByPrimaryKeyWithPortGroupName" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from network A
        where A.id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from network
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByExample" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        delete A from network A
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="cn.com.cloudstar.rightcloud.basic.data.pojo.res.Network"
        keyProperty="id"
        useGeneratedKeys="true">
        insert into network (id, cloud_env_id, network_name,
                             cidr, mask, gateway, subnet,
                             dns1, dns2, vlan, ip_segment, type,
                             status, created_by, created_dt,
                             updated_by, updated_dt, version, comments, uuid, net_vpc_id, enable_dhcp,
                             tenant_id, used_ip, total_ip, router_id, vpc_devices_num)
        values (#{id,jdbcType=BIGINT}, #{cloudEnvId,jdbcType=BIGINT},
                #{networkName,jdbcType=VARCHAR},
                #{cidr,jdbcType=VARCHAR}, #{mask,jdbcType=VARCHAR}, #{gateway,jdbcType=VARCHAR},
                #{subnet,jdbcType=VARCHAR},
                #{dns1,jdbcType=VARCHAR}, #{dns2,jdbcType=VARCHAR}, #{vlan,jdbcType=VARCHAR},
                #{ipSegment,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR},
                #{status,jdbcType=VARCHAR}, #{createdBy,jdbcType=VARCHAR}, #{createdDt,jdbcType=TIMESTAMP},
                #{updatedBy,jdbcType=VARCHAR}, #{updatedDt,jdbcType=TIMESTAMP},
                #{version,jdbcType=BIGINT}, #{comments,jdbcType=VARCHAR}
                   , #{resTargetId}, #{uuid,jdbcType=VARCHAR}, #{netVpcId,jdbcType=VARCHAR},
                #{enableDhcp,jdbcType=VARCHAR},
                #{tenantId,jdbcType=VARCHAR}, #{usedIp,jdbcType=BIGINT}, #{totalIp,jdbcType=BIGINT},
                #{routerId,jdbcType=BIGINT}, #{vpcDevicesNum,jdbcType=INTEGER})
    </insert>
    <insert id="insertSelective"
        parameterType="cn.com.cloudstar.rightcloud.basic.data.pojo.res.Network"
        keyProperty="id"
        useGeneratedKeys="true">
        insert into network
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="cloudEnvId != null">
                cloud_env_id,
            </if>
            <if test="networkName != null">
                network_name,
            </if>
            <if test="cidr != null">
                cidr,
            </if>
            <if test="mask != null">
                mask,
            </if>
            <if test="subnet != null">
                subnet,
            </if>
            <if test="gateway != null">
                gateway,
            </if>
            <if test="dns1 != null">
                dns1,
            </if>
            <if test="dns2 != null">
                dns2,
            </if>
            <if test="vlan != null">
                vlan,
            </if>
            <if test="region != null">
                region,
            </if>
            <if test="zone != null">
                zone,
            </if>
            <if test="ipSegment != null">
                ip_segment,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="createdDt != null">
                created_dt,
            </if>
            <if test="updatedBy != null">
                updated_by,
            </if>
            <if test="updatedDt != null">
                updated_dt,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="comments != null">
                comments,
            </if>
            <if test="resTargetId != null">
                res_target_id,
            </if>
            <if test="uuid != null">
                uuid,
            </if>
            <if test="netVpcId != null">
                net_vpc_id,
            </if>
            <if test="enableDhcp != null">
                enable_dhcp,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="usedIp != null">
                used_ip,
            </if>
            <if test="totalIp != null">
                total_ip,
            </if>
            <if test="routerId != null">
                router_id,
            </if>
            <if test="vpcDevicesNum != null">
                vpc_devices_num,
            </if>
            <if test="urn != null">
                URN,
            </if>
            <if test="uri != null">
                URI,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="cloudEnvId != null">
                #{cloudEnvId,jdbcType=BIGINT},
            </if>
            <if test="networkName != null">
                #{networkName,jdbcType=VARCHAR},
            </if>
            <if test="cidr != null">
                #{cidr,jdbcType=VARCHAR},
            </if>
            <if test="mask != null">
                #{mask,jdbcType=VARCHAR},
            </if>
            <if test="subnet != null">
                #{subnet,jdbcType=VARCHAR},
            </if>
            <if test="gateway != null">
                #{gateway,jdbcType=VARCHAR},
            </if>
            <if test="dns1 != null">
                #{dns1,jdbcType=VARCHAR},
            </if>
            <if test="dns2 != null">
                #{dns2,jdbcType=VARCHAR},
            </if>
            <if test="vlan != null">
                #{vlan,jdbcType=VARCHAR},
            </if>
            <if test="region != null">
                #{region,jdbcType=VARCHAR},
            </if>
            <if test="zone != null">
                #{zone,jdbcType=VARCHAR},
            </if>
            <if test="ipSegment != null">
                #{ipSegment,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDt != null">
                #{createdDt,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDt != null">
                #{updatedDt,jdbcType=TIMESTAMP},
            </if>
            <if test="version != null">
                #{version,jdbcType=BIGINT},
            </if>
            <if test="comments != null">
                #{comments,jdbcType=VARCHAR},
            </if>
            <if test="resTargetId != null">
                #{resTargetId,jdbcType=VARCHAR},
            </if>
            <if test="uuid != null">
                #{uuid,jdbcType=VARCHAR},
            </if>
            <if test="netVpcId != null">
                #{netVpcId,jdbcType=VARCHAR},
            </if>
            <if test="enableDhcp != null">
                #{enableDhcp,jdbcType=VARCHAR},
            </if>
            <if test="tenantId != null">
                #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="usedIp != null">
                #{usedIp,jdbcType=BIGINT},
            </if>
            <if test="totalIp != null">
                #{totalIp,jdbcType=BIGINT},
            </if>
            <if test="routerId != null">
                #{routerId,jdbcType=BIGINT},
            </if>
            <if test="vpcDevicesNum != null">
                #{vpcDevicesNum,jdbcType=INTEGER},
            </if>
            <if test="urn != null">
                #{urn},
            </if>
            <if test="uri != null">
                #{uri},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultType="java.lang.Integer">
        select count(*) from network A
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update network A
        <set>
            <if test="record.id != null">
                A.id = #{record.id,jdbcType=BIGINT},
            </if>
            <if test="record.cloudEnvId != null">
                A.cloud_env_id = #{record.cloudEnvId,jdbcType=BIGINT},
            </if>
            <if test="record.networkName != null">
                A.network_name = #{record.networkName,jdbcType=VARCHAR},
            </if>
            <if test="record.cidr != null">
                A.cidr = #{record.cidr,jdbcType=VARCHAR},
            </if>
            <if test="record.mask != null">
                A.mask = #{record.mask,jdbcType=VARCHAR},
            </if>
            <if test="record.subnet != null">
                A.subnet = #{record.subnet,jdbcType=VARCHAR},
            </if>
            <if test="record.gateway != null">
                A.gateway = #{record.gateway,jdbcType=VARCHAR},
            </if>
            <if test="record.dns1 != null">
                A.dns1 = #{record.dns1,jdbcType=VARCHAR},
            </if>
            <if test="record.dns2 != null">
                A.dns2 = #{record.dns2,jdbcType=VARCHAR},
            </if>
            <if test="record.vlan != null">
                A.vlan = #{record.vlan,jdbcType=VARCHAR},
            </if>
            <if test="record.region != null">
                A.region = #{record.region,jdbcType=VARCHAR},
            </if>
            <if test="record.zone != null">
                A.zone = #{record.zone,jdbcType=VARCHAR},
            </if>
            <if test="record.ipSegment != null">
                A.ip_segment = #{record.ipSegment,jdbcType=VARCHAR},
            </if>
            <if test="record.type != null">
                A.type = #{record.type,jdbcType=VARCHAR},
            </if>
            <if test="record.status != null">
                A.status = #{record.status,jdbcType=VARCHAR},
            </if>
            <if test="record.createdBy != null">
                A.created_by = #{record.createdBy,jdbcType=VARCHAR},
            </if>
            <if test="record.createdDt != null">
                A.created_dt = #{record.createdDt,jdbcType=TIMESTAMP},
            </if>
            <if test="record.updatedBy != null">
                A.updated_by = #{record.updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="record.updatedDt != null">
                A.updated_dt = #{record.updatedDt,jdbcType=TIMESTAMP},
            </if>
            <if test="record.version != null">
                A.version = #{record.version,jdbcType=BIGINT},
            </if>
            <if test="record.comments != null">
                A.comments = #{record.comments,jdbcType=VARCHAR},
            </if>
            <if test="record.resTargetId != null">
                A.res_target_id = #{record.resTargetId,jdbcType=VARCHAR},
            </if>
            <if test="record.uuid != null">
                A.uuid = #{record.uuid,jdbcType=VARCHAR},
            </if>
            <if test="record.netVpcId != null">
                A.net_vpc_id = #{record.netVpcId,jdbcType=VARCHAR},
            </if>
            <if test="record.enableDhcp != null">
                A.enable_dhcp = #{record.enableDhcp,jdbcType=VARCHAR},
            </if>
            <if test="record.tenantId != null">
                A.tenant_id = #{record.tenantId,jdbcType=VARCHAR},
            </if>
            <if test="record.usedIp != null">
                A.used_ip = #{record.usedIp,jdbcType=BIGINT},
            </if>
            <if test="record.totalIp != null">
                A.total_ip = #{record.totalIp,jdbcType=BIGINT},
            </if>
            <if test="record.routerId != null">
                A.router_id = #{record.routerId,jdbcType=BIGINT},
            </if>
            <if test="record.vpcDevicesNum != null">
                A.vpc_devices_num = #{record.vpcDevicesNum,jdbcType=INTEGER},
            </if>
            <if test="record.urn != null">
                A.URN = #{record.urn},
            </if>
            <if test="record.uri != null">
                A.URI = #{record.uri},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update network A
        set A.id = #{record.id,jdbcType=BIGINT},
        A.cloud_env_id = #{record.cloudEnvId,jdbcType=BIGINT},
        A.network_name = #{record.networkName,jdbcType=VARCHAR},
        A.cidr = #{record.cidr,jdbcType=VARCHAR},
        A.mask = #{record.mask,jdbcType=VARCHAR},
        A.gateway = #{record.gateway,jdbcType=VARCHAR},
        A.subnet = #{record.subnet,jdbcType=VARCHAR},
        A.dns1 = #{record.dns1,jdbcType=VARCHAR},
        A.dns2 = #{record.dns2,jdbcType=VARCHAR},
        A.vlan = #{record.vlan,jdbcType=VARCHAR},
        A.region = #{record.region,jdbcType=VARCHAR},
        A.zone = #{record.zone,jdbcType=VARCHAR},
        A.ip_segment = #{record.ipSegment,jdbcType=VARCHAR},
        A.type = #{record.type,jdbcType=VARCHAR},
        A.status = #{record.status,jdbcType=VARCHAR},
        A.created_by = #{record.createdBy,jdbcType=VARCHAR},
        A.created_dt = #{record.createdDt,jdbcType=TIMESTAMP},
        A.updated_by = #{record.updatedBy,jdbcType=VARCHAR},
        A.updated_dt = #{record.updatedDt,jdbcType=TIMESTAMP},
        A.version = #{record.version,jdbcType=BIGINT},
        A.comments = #{record.comments,jdbcType=VARCHAR},
        A.res_target_id = #{record.resTargetId,jdbcType=VARCHAR},
        A.uuid = #{record.uuid,jdbcType=VARCHAR},
        A.net_vpc_id = #{record.netVpcId,jdbcType=VARCHAR},
        A.enable_dhcp = #{record.enableDhcp,jdbcType=VARCHAR},
        A.tenant_id = #{record.tenantId,jdbcType=VARCHAR},
        A.used_ip = #{record.usedIp,jdbcType=BIGINT},
        A.total_ip = #{record.totalIp,jdbcType=BIGINT},
        A.router_id = #{record.routerId,jdbcType=BIGINT},
        A.vpc_devices_num = #{record.vpcDevicesNum,jdbcType=INTEGER},
        URN = #{record.urn},
        URI = #{record.uri},
        A.network_uuid = #{record.networkUuid,jdbcType=VARCHAR},
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective"
        parameterType="cn.com.cloudstar.rightcloud.basic.data.pojo.res.Network">
        update network
        <set>
            <if test="cloudEnvId != null">
                cloud_env_id = #{cloudEnvId,jdbcType=BIGINT},
            </if>
            <if test="networkName != null">
                network_name = #{networkName,jdbcType=VARCHAR},
            </if>
            <if test="cidr != null">
                cidr = #{cidr,jdbcType=VARCHAR},
            </if>
            <if test="mask != null">
                mask = #{mask,jdbcType=VARCHAR},
            </if>
            <if test="gateway != null">
                gateway = #{gateway,jdbcType=VARCHAR},
            </if>
            <if test="subnet != null">
                subnet = #{subnet,jdbcType=VARCHAR},
            </if>
            <if test="dns1 != null">
                dns1 = #{dns1,jdbcType=VARCHAR},
            </if>
            <if test="dns2 != null">
                dns2 = #{dns2,jdbcType=VARCHAR},
            </if>
            <if test="vlan != null">
                vlan = #{vlan,jdbcType=VARCHAR},
            </if>
            <if test="region != null">
                region = #{region,jdbcType=VARCHAR},
            </if>
            <if test="zone != null">
                zone = #{zone,jdbcType=VARCHAR},
            </if>
            <if test="ipSegment != null">
                ip_segment = #{ipSegment,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDt != null">
                created_dt = #{createdDt,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDt != null">
                updated_dt = #{updatedDt,jdbcType=TIMESTAMP},
            </if>
            <if test="version != null">
                version = #{version,jdbcType=BIGINT},
            </if>
            <if test="comments != null">
                comments = #{comments,jdbcType=VARCHAR},
            </if>
            <if test="resTargetId != null">
                res_target_id = #{resTargetId,jdbcType=VARCHAR},
            </if>
            <if test="uuid != null">
                uuid = #{uuid,jdbcType=VARCHAR},
            </if>
            <if test="netVpcId != null">
                net_vpc_id = #{netVpcId,jdbcType=VARCHAR},
            </if>
            <if test="enableDhcp != null">
                enable_dhcp = #{enableDhcp,jdbcType=VARCHAR},
            </if>
            <if test="tenantId != null">
                tenant_id = #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="usedIp != null">
                used_ip = #{usedIp,jdbcType=BIGINT},
            </if>
            <if test="totalIp != null">
                total_ip = #{totalIp,jdbcType=BIGINT},
            </if>
            <if test="routerId != null">
                router_id = #{routerId,jdbcType=BIGINT},
            </if>
            <if test="vpcDevicesNum != null">
                vpc_devices_num = #{vpcDevicesNum,jdbcType=INTEGER},
            </if>
            <if test="networkUuid != null">
                network_uuid = #{networkUuid,jdbcType=VARCHAR},
            </if>
            <if test="urn != null">
                URN = #{urn},
            </if>
            <if test="uri != null">
                URI = #{uri},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey"
        parameterType="cn.com.cloudstar.rightcloud.basic.data.pojo.res.Network">
        update network
        set cloud_env_id    = #{cloudEnvId,jdbcType=BIGINT},
            network_name    = #{networkName,jdbcType=VARCHAR},
            cidr            = #{cidr,jdbcType=VARCHAR},
            mask            = #{mask,jdbcType=VARCHAR},
            gateway         = #{gateway,jdbcType=VARCHAR},
            subnet          = #{subnet,jdbcType=VARCHAR},
            dns1            = #{dns1,jdbcType=VARCHAR},
            dns2            = #{dns2,jdbcType=VARCHAR},
            vlan            = #{vlan,jdbcType=VARCHAR},
            region          = #{region,jdbcType=VARCHAR},
            zone            = #{zone,jdbcType=VARCHAR},
            ipSegment       = #{ipSegment,jdbcType=VARCHAR},
            type            = #{type,jdbcType=VARCHAR},
            status          = #{status,jdbcType=VARCHAR},
            created_by      = #{createdBy,jdbcType=VARCHAR},
            created_dt      = #{createdDt,jdbcType=TIMESTAMP},
            updated_by      = #{updatedBy,jdbcType=VARCHAR},
            updated_dt      = #{updatedDt,jdbcType=TIMESTAMP},
            version         = #{version,jdbcType=BIGINT},
            comments        = #{comments,jdbcType=VARCHAR},
            res_target_id   = #{resTargetId,jdbcType=VARCHAR},
            uuid            = #{uuid,jdbcType=VARCHAR},
            net_vpc_id      = #{netVpcId,jdbcType=VARCHAR},
            enable_dhcp     = #{enableDhcp,jdbcType=VARCHAR},
            tenant_id       = #{tenantId,jdbcType=VARCHAR},
            used_ip         = #{usedIp,jdbcType=BIGINT},
            total_ip        = #{totalIp,jdbcType=BIGINT},
            router_id       = #{routerId,jdbcType=BIGINT},
            urn             = #{urn},
            uri             = #{uri},
            vpc_devices_num = #{vpcDevicesNum,jdbcType=INTEGER}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <select id="selectByResPool" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        select
        <include refid="Base_Column_List"/>
        , count(DISTINCT D.id,IF(D.status = 'used',D.id,null)) as used
        , count(DISTINCT D.id,IF(D.status = 'unused',D.id,null)) as unused
        from network A
        INNER JOIN res_vs_port_group_network B ON A.id = B.network_id
        INNER JOIN res_pool_resource C ON (B.id = C.resource_sid and c.resource_type = 'RES-NETWORK')
        LEFT JOIN network_ip D on A.id = D.network_id
        <where>
            <if test="condition.resPoolId != null">
                and C.res_pool_id = #{condition.resPoolId}
            </if>
            <if test="condition.resPoolIds != null">
                and C.res_pool_id in
                <foreach collection="condition.resPoolIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY A.id
    </select>
    <select id="countByNetVpcId" resultMap="VpcSubnetCountResultMap"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        SELECT
        A.net_vpc_id as vpc_id,
        COUNT(DISTINCT A.id) as subnet_count
        FROM
        (
        SELECT
        n.id,
        n.network_name,
        n.net_vpc_id,
        vpc.org_sid AS org_sid
        FROM
        network n
        INNER JOIN res_vpc vpc ON vpc.id = n.net_vpc_id
        <where>
            <if test="condition.cloudEnvId != null">
                and n.cloud_env_id = #{condition.cloudEnvId}
            </if>
            <if test="condition.vpcIds != null and condition.vpcIds.size() > 0">
                and n.net_vpc_id in
                <foreach collection="condition.vpcIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.cloudEnvIdIn != null and condition.cloudEnvIdIn.size() > 0">
                and n.cloud_env_id in
                <foreach collection="condition.cloudEnvIdIn" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY id, org_sid
        UNION ALL
        SELECT
        n.id,
        n.network_name,
        n.net_vpc_id,
        E.org_sid AS org_sid
        FROM
        network n
        INNER JOIN network_alloc C ON n.id = C.network_id
        INNER JOIN cloud_env D ON C.cloud_env_id = D.id
        INNER JOIN cloud_env_account E ON D.cloud_env_account_id = E.id
        <where>
            <if test="condition.cloudEnvId != null">
                and C.cloud_env_id = #{condition.cloudEnvId}
            </if>
            <if test="condition.cloudEnvIdIn != null and condition.cloudEnvIdIn.size() > 0">
                and C.cloud_env_id in
                <foreach collection="condition.cloudEnvIdIn" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.vpcIds != null and condition.vpcIds.size() > 0">
                and n.net_vpc_id in
                <foreach collection="condition.vpcIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY id, org_sid
        UNION ALL
        SELECT
        n.id,
        n.network_name,
        n.net_vpc_id,
        C.org_sid AS org_sid
        FROM
        network n
        INNER JOIN cloud_resource_alloc C ON n.id = C.target_id AND C.target_type = 'vpc'
        <where>
            <if test="condition.cloudEnvId != null">
                and n.cloud_env_id = #{condition.cloudEnvId}
            </if>
            <if test="condition.cloudEnvIdIn != null and condition.cloudEnvIdIn.size() > 0">
                and n.cloud_env_id in
                <foreach collection="condition.cloudEnvIdIn" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.vpcIds != null and condition.vpcIds.size() > 0">
                and n.net_vpc_id in
                <foreach collection="condition.vpcIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY id, org_sid
        UNION ALL
        SELECT
        n.id,
        n.network_name,
        n.net_vpc_id,
        C.org_sid AS org_sid
        FROM
        network n
        INNER JOIN cloud_resource_alloc C ON n.id = C.target_id AND C.target_type = 'subnet'
        <if test="condition.orgSid != null">
            AND C.org_sid = #{condition.orgSid}
        </if>
        <if test="condition.orgSid4Subnet != null">
            AND C.org_sid = #{condition.orgSid4Subnet}
        </if>
        <where>
            <if test="condition.cloudEnvId != null">
                and n.cloud_env_id = #{condition.cloudEnvId}
            </if>
            <if test="condition.cloudEnvIdIn != null and condition.cloudEnvIdIn.size() > 0">
                and n.cloud_env_id in
                <foreach collection="condition.cloudEnvIdIn" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.vpcIds != null and condition.vpcIds.size() > 0">
                and n.net_vpc_id in
                <foreach collection="condition.vpcIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY id, org_sid
        ) A
        WHERE A.net_vpc_id IS NOT NULL
        <if test="condition.df != null">
            ${condition.df}
        </if>
        GROUP BY
        net_vpc_id
    </select>
    <select id="selectByInstanceId" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        SELECT
        <include refid="Base_Column_List"/>
        FROM network A
        INNER JOIN res_vm_ext B ON A.id = B.resource_id AND B.type = 'subnet'
        <where>
            <if test="condition.instanceId != null">
                B.instance_id = #{condition.instanceId}
            </if>
        </where>
    </select>

    <select id="selectByOptions" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        select
        <include refid="Base_Column_List"/>
        from network A
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <select id="selectAllocSubnetByDf" resultMap="BaseResultMap">
        SELECT * FROM (
        #所有者#
        SELECT
        n.id,
        n.network_name,
        n.net_vpc_id,
        vpc.org_sid AS org_sid,
        ce.id AS cloud_env_id,
        ce.cloud_env_type,
        ce.cloud_env_name,
        n.cidr,
        n.created_dt,
        n.status,
        #所有者判断: res.org_sid = cloud_env_account.org_sid#
        case when vpc.org_sid = account.org_sid then true else false end as read_only,
        'org_owner' res_from
        FROM
        network n
        INNER JOIN res_vpc vpc ON vpc.id = n.net_vpc_id
        INNER JOIN cloud_env ce ON ce.id = n.cloud_env_id
        INNER JOIN cloud_env_account account ON account.id = ce.cloud_env_account_id
        GROUP BY n.id, vpc.org_sid
        #所有者#
        #分区分配#
        UNION ALL
        #网络分配#
        SELECT
        n.id,
        n.network_name,
        n.net_vpc_id,
        E.org_sid AS org_sid,
        D.id AS cloud_env_id,
        D.cloud_env_type,
        D.cloud_env_name,
        n.cidr,
        n.created_dt,
        n.status,
        true AS read_only,
        'network_alloc' res_from
        FROM
        network n
        INNER JOIN network_alloc C ON n.id = C.network_id
        INNER JOIN cloud_env D ON C.cloud_env_id = D.id
        INNER JOIN cloud_env_account E ON D.cloud_env_account_id = E.id
        GROUP BY n.id, E.org_sid
        #网络分配#
        UNION ALL
        #云环境资源分配#
        SELECT
        n.id,
        n.network_name,
        n.net_vpc_id,
        C.org_sid AS org_sid,
        D.id AS cloud_env_id,
        D.cloud_env_type,
        D.cloud_env_name,
        n.cidr,
        n.created_dt,
        n.status,
        false AS read_only,
        'cloud_resource_alloc' res_from
        FROM
        network n
        INNER JOIN cloud_resource_alloc C ON n.id = C.target_id AND C.target_type = 'subnet' AND C.org_sid =
        #{condition.orgSid}
        INNER JOIN cloud_env D ON n.cloud_env_id = D.id
        GROUP BY n.id, C.org_sid
        #云环境资源分配#
        ) A
        <where>
            <if test="condition.df != null">
                ${condition.df}
            </if>
            <if test="condition.cloudEnvId != null">
                AND A.cloud_env_id = #{condition.cloudEnvId}
            </if>
            <if test="condition.vpcId != null">
                AND A.net_vpc_id = #{condition.vpcId}
            </if>
            <if test="condition.cloudEnvTypeNotIn != null">
                AND A.cloud_env_type NOT IN
                <foreach collection="condition.cloudEnvTypeNotIn" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY
        id
    </select>
    <select id="selectNotAllocSubnetByDf" resultMap="BaseResultMap">
        SELECT A.* FROM (
        SELECT
        n.id,
        n.network_name,
        n.net_vpc_id,
        cea.org_sid AS org_sid,
        ce.id AS cloud_env_id,
        ce.cloud_env_type,
        ce.cloud_env_name,
        n.cidr,
        n.created_dt,
        n.status
        FROM
        network n
        INNER JOIN cloud_env ce ON ce.id = n.cloud_env_id
        INNER JOIN cloud_env_account cea ON cea.id = ce.cloud_env_account_id
        GROUP BY id, org_sid
        UNION ALL
        SELECT
        n.id,
        n.network_name,
        n.net_vpc_id,
        E.org_sid AS org_sid,
        D.id AS cloud_env_id,
        D.cloud_env_type,
        D.cloud_env_name,
        n.cidr,
        n.created_dt,
        n.status
        FROM
        network n
        INNER JOIN network_alloc C ON n.id = C.network_id
        INNER JOIN cloud_env D ON C.cloud_env_id = D.id
        INNER JOIN cloud_env_account E ON D.cloud_env_account_id = E.id
        GROUP BY id, org_sid
        UNION ALL
        SELECT
        n.id,
        n.network_name,
        n.net_vpc_id,
        C.org_sid AS org_sid,
        D.id AS cloud_env_id,
        D.cloud_env_type,
        D.cloud_env_name,
        n.cidr,
        n.created_dt,
        n.STATUS
        FROM
        network n
        INNER JOIN cloud_resource_alloc C ON n.id = C.target_id
        AND C.target_type = 'subnet'
        LEFT JOIN cloud_env D ON C.cloud_env_id = D.id
        GROUP BY
        id,
        org_sid
        ) A
        INNER JOIN res_vpc vpc on A.net_vpc_id=vpc.id AND ((vpc.shared != "true" and vpc.external != "true")
        or (vpc.shared is null and vpc.external is null))
        <where>
            <if test="condition.df != null">
                ${condition.df}
            </if>
            <if test="condition.cloudEnvId != null">
                AND A.cloud_env_id = #{condition.cloudEnvId}
            </if>
            <if test="condition.vpcId != null">
                AND A.net_vpc_id = #{condition.vpcId}
            </if>
            <if test="condition.idNotIn != null and condition.idNotIn.size() > 0">
                AND A.id NOT IN
                <foreach collection="condition.idNotIn" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.orgSid != null">
                AND NOT EXISTS (
                SELECT 1 FROM cloud_resource_alloc
                WHERE target_id = A.id
                AND target_type = 'subnet'
                AND org_sid = #{condition.orgSid})
                AND EXISTS ( SELECT 1 FROM cloud_env_alloc cea WHERE cea.alloc_target_id = #{condition.orgSid}
                AND cea.cloud_env_id = A.cloud_env_id )
            </if>
            <if test="condition.cloudEnvTypeNotIn != null">
                AND A.cloud_env_type NOT IN
                <foreach collection="condition.cloudEnvTypeNotIn" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY
        A.id
    </select>
    <select id="selectAllocSubnet" resultMap="BaseResultMap">
        SELECT * FROM (
        SELECT
        n.id,
        n.network_name,
        n.net_vpc_id,
        n.uuid,
        n.network_uuid,
        vpc.org_sid AS org_sid,
        ce.id AS cloud_env_id,
        ce.cloud_env_type,
        ce.cloud_env_name,
        n.created_by,
        n.cidr,
        n.zone
        FROM
        network n
        INNER JOIN cloud_env ce ON ce.id = n.cloud_env_id
        INNER JOIN res_vpc vpc ON vpc.id = n.net_vpc_id
        GROUP BY id, org_sid
        UNION ALL
        SELECT
        n.id,
        n.network_name,
        n.net_vpc_id,
        n.uuid,
        n.network_uuid,
        E.org_sid AS org_sid,
        D.id AS cloud_env_id,
        D.cloud_env_type,
        D.cloud_env_name,
        n.created_by,
        n.cidr,
        n.zone
        FROM
        network n
        INNER JOIN network_alloc C ON n.id = C.network_id
        INNER JOIN cloud_env D ON C.cloud_env_id = D.id
        INNER JOIN cloud_env_account E ON D.cloud_env_account_id = E.id
        GROUP BY id, org_sid
        UNION ALL
        SELECT
        n.id,
        n.network_name,
        n.net_vpc_id,
        n.uuid,
        n.network_uuid,
        C.org_sid AS org_sid,
        D.id AS cloud_env_id,
        D.cloud_env_type,
        D.cloud_env_name,
        n.created_by,
        n.cidr,
        n.zone
        FROM
        network n
        INNER JOIN cloud_resource_alloc C ON n.id = C.target_id AND C.target_type = 'subnet' AND C.org_sid =
        #{condition.orgSid}
        INNER JOIN cloud_env D ON n.cloud_env_id = D.id
        LEFT JOIN res_vs_port_group_network rvpgn on rvpgn.network_id = c.target_id
        LEFT JOIN res_pool_resource rpr ON rvpgn.id = rpr.resource_sid and rpr.resource_type = 'RES-NETWORK'
        <where>
            <if test="condition.resPoolId != null">
                AND rpr.res_pool_id = #{condition.resPoolId}
            </if>
        </where>
        GROUP BY id, org_sid
        ) T
        <where>
            <if test="condition.cloudEnvId != null">
                AND T.cloud_env_id = #{condition.cloudEnvId}
            </if>
            <if test="condition.cloudEnvIdIn != null and condition.cloudEnvIdIn.size() > 0">
                AND T.cloud_env_id IN
                <foreach collection="condition.cloudEnvIdIn" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="condition.vpcId != null">
                AND T.net_vpc_id = #{condition.vpcId}
            </if>
            <if test="condition.netVpcIds != null and condition.netVpcIds.size() > 0">
                AND T.net_vpc_id IN
                <foreach collection="condition.netVpcIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="condition.zone != null and condition.zone != ''">
                AND T.zone = #{condition.zone}
            </if>
            <if test="condition.zonesIn != null and condition.zonesIn.size() > 0">
                AND T.zone IN
                <foreach collection="condition.zonesIn" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY
        T.id
    </select>
    <select id="selectAllocSubnetWithOutDataFilter" resultMap="BaseResultMap">
        SELECT * FROM (
        SELECT
        n.id,
        n.network_name,
        n.net_vpc_id,
        n.uuid,
        n.network_uuid,
        vpc.org_sid AS org_sid,
        ce.id AS cloud_env_id,
        ce.cloud_env_type,
        ce.cloud_env_name,
        n.created_by,
        n.cidr,
        n.zone
        FROM
        network n
        INNER JOIN cloud_env ce ON ce.id = n.cloud_env_id
        INNER JOIN res_vpc vpc ON vpc.id = n.net_vpc_id
        GROUP BY id, org_sid
        UNION ALL
        SELECT
        n.id,
        n.network_name,
        n.net_vpc_id,
        n.uuid,
        n.network_uuid,
        E.org_sid AS org_sid,
        D.id AS cloud_env_id,
        D.cloud_env_type,
        D.cloud_env_name,
        n.created_by,
        n.cidr,
        n.zone
        FROM
        network n
        INNER JOIN network_alloc C ON n.id = C.network_id
        INNER JOIN cloud_env D ON C.cloud_env_id = D.id
        INNER JOIN cloud_env_account E ON D.cloud_env_account_id = E.id
        GROUP BY id, org_sid
        UNION ALL
        SELECT
        n.id,
        n.network_name,
        n.net_vpc_id,
        n.uuid,
        n.network_uuid,
        C.org_sid AS org_sid,
        D.id AS cloud_env_id,
        D.cloud_env_type,
        D.cloud_env_name,
        n.created_by,
        n.cidr,
        n.zone
        FROM
        network n
        INNER JOIN cloud_resource_alloc C ON n.id = C.target_id AND C.target_type = 'subnet' AND C.org_sid =
        #{condition.orgSid}
        INNER JOIN cloud_env D ON n.cloud_env_id = D.id
        GROUP BY id, org_sid
        ) T
        <where>
            <if test="condition.cloudEnvId != null">
                AND T.cloud_env_id = #{condition.cloudEnvId}
            </if>
            <if test="condition.cloudEnvIdIn != null and condition.cloudEnvIdIn.size() > 0">
                AND T.cloud_env_id IN
                <foreach collection="condition.cloudEnvIdIn" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="condition.vpcId != null">
                AND T.net_vpc_id = #{condition.vpcId}
            </if>
            <if test="condition.netVpcIds != null and condition.netVpcIds.size() > 0">
                AND T.net_vpc_id IN
                <foreach collection="condition.netVpcIds" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY
        T.id
    </select>
    <select id="countAllocSubnetByDf" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT id) FROM (
        SELECT
        n.id,
        account.org_sid,
        vpc.external,
        env.id as cloud_env_id,
        env.cloud_env_type
        FROM
        network n
        INNER JOIN res_vpc vpc ON n.net_vpc_id = vpc.id
        INNER JOIN cloud_env env ON env.id = n.cloud_env_id
        INNER JOIN cloud_env_account account ON env.cloud_env_account_id = account.id
        GROUP BY n.id, vpc.org_sid
        UNION ALL
        SELECT
        n.id,
        E.org_sid,
        vpc.external,
        D.id as cloud_env_id,
        D.cloud_env_type
        FROM
        network n
        INNER JOIN network_alloc C ON n.id = C.network_id
        INNER JOIN cloud_env D ON C.cloud_env_id = D.id
        INNER JOIN cloud_env_account E ON D.cloud_env_account_id = E.id
        INNER JOIN res_vpc vpc ON n.net_vpc_id = vpc.id
        GROUP BY n.id, E.org_sid
        UNION ALL
        SELECT
        n.id,
        C.org_sid,
        vpc.external,
        env.id as cloud_env_id,
        env.cloud_env_type
        FROM
        network n
        INNER JOIN cloud_resource_alloc C ON n.id = C.target_id AND C.target_type = 'subnet' AND C.org_sid =
        #{condition.orgSid}
        INNER JOIN res_vpc vpc ON n.net_vpc_id = vpc.id
        INNER JOIN cloud_env env ON env.id = n.cloud_env_id
        GROUP BY n.id, C.org_sid
        ) T
        <where>
            <if test="condition.df != null">
                ${condition.df}
            </if>
            <if test="condition.external != null">
                <if test="condition.external == 'true'">
                    AND T.external = #{condition.external}
                </if>
                <if test="condition.external == 'false'">
                    AND (T.external = #{condition.external} OR T.external IS NULL)
                </if>
            </if>
            <if test="condition.cloudEnvTypeNotIn != null">
                AND T.cloud_env_type NOT IN
                <foreach collection="condition.cloudEnvTypeNotIn" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectNotAllocPool" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"></include>
        FROM
        network A
        WHERE
        NOT EXISTS (
        SELECT
        B.id
        FROM
        network B
        LEFT JOIN res_pool pool ON pool.cloud_env_id = B.cloud_env_id
        LEFT JOIN res_pool_resource rpr ON rpr.res_pool_id = pool.id
        AND rpr.resource_type = 'RES-NETWORK'
        INNER JOIN res_vs_port_group_network rvpgn ON rvpgn.id = rpr.resource_sid
        AND B.id = rvpgn.network_id
        WHERE
        A.id = b.id
        GROUP BY
        B.id
        )
        AND A.cloud_env_id = #{envId}
    </select>

    <select id="selectNetworksByUuid" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"></include>
        FROM network A
        WHERE A.uuid = #{uuid}
    </select>
    <select id="selectVmNetworksUsedInfo" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        SELECT a.res_id, b.used, a.total, a.total - b.used AS unused
        FROM (
                 SELECT n.id                                      AS res_id,
                        IFNULL(n.total_ip, COUNT(DISTINCT ni.id)) AS total
                 FROM network n
                          LEFT JOIN network_ip ni ON n.id = ni.network_id
                 WHERE n.id = #{condition.networkId}
             ) a
                 INNER JOIN (
            SELECT rve.resource_id                                                                            AS res_id,
                   SUM(CASE WHEN rv.STATUS NOT IN ('create_failure', 'pending', 'deleted') THEN 1 ELSE 0 END) AS used
            FROM res_vm_ext rve
                     INNER JOIN res_vm rv ON rv.id = rve.instance_id
            WHERE rve.resource_id = concat(#{condition.networkId}, '')
              AND rve.type = 'subnet'
        ) b
                            ON a.res_id = b.res_id;
    </select>

    <select id="getVmNumberByVpcId" parameterType="java.lang.Long"
        resultType="java.lang.Integer">
        SELECT count(1)
        FROM res_vm_ext che
                 INNER JOIN network net ON che.resource_id = net.id AND che.type = 'subnet'
                 LEFT JOIN res_vm vm ON che.instance_id = vm.ID
        WHERE net.id = #{netWorkId}
          and vm.status not in ('deleting', 'create_failure', 'pending', 'deleted', 'creating')
    </select>

    <select id="getSubnetAlreadyAttached" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        SELECT
        C.id,
        D.`name` AS vpc_name,
        C.cidr,
        C.network_name
        FROM res_vpc_port_ext A
        LEFT JOIN res_vpc_port B ON A.port_id = B.id
        LEFT JOIN network C ON B.subnet_id = C.id
        LEFT JOIN res_vpc D ON C.net_vpc_id = D.id
        <where>
            <if test="condition.fireWallId != null">
                AND A.resource_id = #{condition.fireWallId}
            </if>
            <if test="condition.networkName != null">
                and C.network_name like concat('%',#{condition.networkName}, '%')
            </if>
            <if test="condition.vpcName != null">
                and D.name like concat('%',#{condition.vpcName}, '%')
            </if>
            <if test="condition.cidr != null">
                and C.cidr = #{condition.cidr}
            </if>
        </where>
    </select>

    <select id="getSubnetUnAttached" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        SELECT
        A.id,
        C.`name` AS vpc_name,
        A.cidr,
        A.network_name
        FROM network A
        LEFT JOIN res_vpc_port B ON A.id = B.subnet_id
        LEFT JOIN res_vpc C ON A.net_vpc_id = C.id
        <where>
            <if test="condition.cloudEnvId != null">
                and A.cloud_env_id = #{condition.cloudEnvId}
            </if>
            <if test="condition.networkNameLike != null">
                and A.network_name like concat('%',#{condition.networkNameLike}, '%')
            </if>
            <if test="condition.vpcName != null">
                and C.name like concat('%',#{condition.vpcName}, '%')
            </if>
            <if test="condition.cidr != null">
                and A.cidr like concat('%',#{condition.cidr}, '%')
            </if>
            <if test="condition.deviceOwner != null">
                and B.device_owner = #{condition.deviceOwner}
            </if>
            <if test="condition.fireWallId != null">
                and B.id not in (
                SELECT port_id FROM res_vpc_port_ext WHERE type = 'subnet' and resource_id = #{condition.fireWallId}
                )
            </if>
        </where>
        GROUP BY A.id
    </select>


    <select id="countInstance" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultType="java.lang.Integer">
        select count(*) from res_vm_ext A
        left join res_vm B on A.instance_id = B.id
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.resourceId != null">
                and A.resource_id = #{condition.resourceId}
            </if>
            <if test="condition.type != null">
                and A.type = #{condition.type}
            </if>
            <if test="condition.statusNotIn != null">
                and B.status not in
                <foreach item="item" index="index" collection="condition.statusNotIn"
                    open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </trim>
    </select>
</mapper>
