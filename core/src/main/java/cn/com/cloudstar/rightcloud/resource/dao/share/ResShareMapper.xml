<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.cloudstar.rightcloud.resource.dao.share.ResShareMapper">
    <resultMap id="BaseResultMap" type="cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResShare">
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="file_system_name" property="fileSystemName" jdbcType="VARCHAR"/>
        <result column="uuid" property="uuid" jdbcType="VARCHAR"/>
        <result column="cloud_env_id" property="cloudEnvId" jdbcType="BIGINT"/>
        <result column="cloud_env_type" property="cloudEnvType" jdbcType="VARCHAR"/>
        <result column="cloud_env_name" property="cloudEnvName" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="VARCHAR"/>
        <result column="links" property="links" jdbcType="VARCHAR"/>
        <result column="availability_zone" property="availabilityZone" jdbcType="VARCHAR"/>
        <result column="share_server_id" property="shareServerId" jdbcType="VARCHAR"/>
        <result column="share_network_id" property="shareNetworkId" jdbcType="VARCHAR"/>
        <result column="security_group_id" property="securityGroupId" jdbcType="VARCHAR"/>
        <result column="consistency_group_id" property="consistencyGroupId" jdbcType="VARCHAR"/>
        <result column="share_proto" property="shareProto" jdbcType="VARCHAR"/>
        <result column="size" property="size" jdbcType="BIGINT"/>
        <result column="size_allocate" property="sizeAllocate" jdbcType="BIGINT"/>
        <result column="used_size" property="usedSize" jdbcType="DECIMAL"/>
        <result column="task_state" property="taskState" jdbcType="VARCHAR"/>
        <result column="has_replicas" property="hasReplicas" jdbcType="VARCHAR"/>
        <result column="replication_type" property="replicationType" jdbcType="VARCHAR"/>
        <result column="host" property="host" jdbcType="VARCHAR"/>
        <result column="created_at" property="createdAt" jdbcType="VARCHAR"/>
        <result column="volume_type" property="volumeType" jdbcType="VARCHAR"/>
        <result column="export_locations" property="exportLocations" jdbcType="VARCHAR"/>
        <result column="export_location" property="exportLocation" jdbcType="VARCHAR"/>
        <result column="snapshot_support" property="snapshotSupport" jdbcType="VARCHAR"/>
        <result column="snapshot_id" property="snapshotId" jdbcType="BIGINT"/>
        <result column="is_public" property="isPublic" jdbcType="VARCHAR"/>
        <result column="share_type" property="shareType" jdbcType="VARCHAR"/>
        <result column="share_type_name" property="shareTypeName" jdbcType="VARCHAR"/>
        <result column="snapshot_type" property="snapshotType" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="metadata" property="metadata" jdbcType="VARCHAR"/>
        <result column="org_sid" property="orgSid" jdbcType="BIGINT"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="created_dt" property="createdDt" jdbcType="TIMESTAMP"/>
        <result column="updated_by" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="updated_dt" property="updatedDt" jdbcType="TIMESTAMP"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
        <result column="accessNumber" property="accessNumber" jdbcType="BIGINT"/>
        <result column="availabilityName" property="availabilityName" jdbcType="VARCHAR"/>
        <result column="mountNumber" property="mountNumber" jdbcType="BIGINT"/>
        <result column="rgbCodes" property="rgbCodes" jdbcType="VARCHAR"/>
        <result column="tagNames" property="tagNames" jdbcType="VARCHAR"/>
        <result column="tagValues" property="tagValues" jdbcType="VARCHAR"/>
        <result column="tagIds" property="tagIds" jdbcType="VARCHAR"/>
        <result column="error_msg" property="errorMsg" jdbcType="VARCHAR"/>
        <result column="targetNames" property="targetNames" jdbcType="VARCHAR"/>
        <result column="localPaths" property="localPaths" jdbcType="VARCHAR"/>
        <result column="ip" property="mountIp" jdbcType="VARCHAR"/>
        <result column="vpc_id" property="resVpcId" jdbcType="VARCHAR"/>
        <result column="vpc_name" property="resVpcName" jdbcType="VARCHAR"/>
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="security_group_pid" property="securityGroupPid" jdbcType="VARCHAR"/>
        <result column="security_group_name" property="securityGroupName" jdbcType="VARCHAR"/>
        <result column="subnet_pid" property="subnetPid" jdbcType="VARCHAR"/>
        <result column="subnet_name" property="subnetName" jdbcType="VARCHAR"/>
        <result column="owner_id" property="ownerId" jdbcType="VARCHAR"/>
        <result column="owner_name" property="ownerName" jdbcType="VARCHAR"/>
        <result column="network_name" property="networkName" jdbcType="VARCHAR"/>
        <result column="service_order_id" property="serviceOrderId" jdbcType="BIGINT"/>
        <result column="service_order_sn" property="serviceOrderSn" jdbcType="VARCHAR"/>
        <result column="server_status" property="serverStatus" jdbcType="VARCHAR"/>
        <result column="delete_resource" property="deleteResource" jdbcType="VARCHAR"/>
        <result column="deploy_id" property="deployId" jdbcType="BIGINT"/>
        <result column="owner_id" property="ownerId" jdbcType="VARCHAR"/>
        <result column="created_org_sid" property="createdOrgSid" jdbcType="BIGINT"/>
        <result column="charge_type" property="chargeType" jdbcType="VARCHAR"/>
        <result column="share_type_id" property="shareTypeId" jdbcType="BIGINT"/>
        <result column="is_share_dir" property="isShareDir" jdbcType="TINYINT"/>
        <result column="is_virtual" property="isVirtual" jdbcType="BOOLEAN"/>
        <result column="orgName" property="orgName" jdbcType="VARCHAR"/>
        <result column="hpc_version" property="hpcVersion" jdbcType="INTEGER"/>
        <result column="freezing_time" property="freezingTime" jdbcType="TIMESTAMP"/>
        <result column="cluster_name" property="clusterName" jdbcType="VARCHAR"/>
        <result column="cluster_type" property="clusterType" jdbcType="VARCHAR"/>
        <result column="cluster_id" property="clusterId" jdbcType="BIGINT"/>
        <result column="is_cluster_default" property="isClusterDefault" jdbcType="BOOLEAN"/>
        <result column="support_cluster_type" property="supportClusterType" jdbcType="VARCHAR"/>
        <result column="quota_size" property="quotaSize" javaType="decimal"/>
        <result column="quota_id" property="quotaId" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , `name`,uuid,cloud_env_id,`type`,links,availability_zone,share_server_id,
        share_network_id,security_group_id,consistency_group_id,share_proto,`size`,used_size,task_state,has_replicas,replication_type,host,
        created_at,volume_type,export_locations,export_location,snapshot_support,snapshot_id,is_public,share_type,share_type_name,snapshot_type,status,description,metadata,org_sid,
        created_by,created_dt,updated_by,updated_dt,version,start_time,end_time
        ,owner_id, created_org_sid,share_type_id,is_share_dir,is_virtual,size_allocate,is_cluster_default,support_cluster_type,quota_size,quota_id
    </sql>

    <sql id="Base_Column_List_alias">
        A
        .id,A.`name`,A.uuid,A.cloud_env_id,A.`type`,A.links,A.availability_zone,A.share_server_id,
        A.share_network_id,A.consistency_group_id,A.share_proto,A.`size`,A.used_size,A.task_state,A.has_replicas,A.replication_type,A.host,
        A.created_at,A.volume_type,A.export_locations,A.export_location,A.snapshot_support,A.snapshot_id,A.is_public,
        A.share_type,A.snapshot_type,A.status,A.description,A.metadata,A.org_sid,
        A.created_by,A.created_dt,A.updated_by,A.updated_dt,A.error_msg,A.start_time,A.end_time,A.security_group_id
        ,A.owner_id, A.created_org_sid,A.share_type_id, A.share_type_name,A.is_share_dir,A.charge_type,A.hpc_version,A.freezing_time,A.is_virtual,A.size_allocate
        ,A.is_cluster_default,A.support_cluster_type,A.quota_size,A.quota_id
    </sql>


    <sql id="getShareList_Column_List_alias">
        A
        .id,A.`name`,A.uuid,A.cloud_env_id,A.`type`,A.links,A.availability_zone,A.share_server_id,
        A.share_network_id,A.consistency_group_id,A.share_proto,A.`size`,A.used_size,A.task_state,A.has_replicas,A.replication_type,A.host,
        A.created_at,A.volume_type,A.export_locations,A.export_location,A.snapshot_support,A.snapshot_id,A.is_public,
        A.share_type,A.snapshot_type,A.status,A.description,A.metadata,A.org_sid,
        A.created_by,A.created_dt,A.updated_by,A.updated_dt,A.error_msg,A.start_time,A.end_time,A.security_group_id
        ,A.owner_id, A.created_org_sid,A.share_type_id,A.is_share_dir,A.charge_type,A.hpc_version,A.freezing_time,A.is_virtual
        ,A.is_cluster_default,A.support_cluster_type,A.quota_size,a.file_system_name,A.quota_id
    </sql>

    <sql id="Example_Where_Clause">
        <trim prefix="where" prefixOverrides="and|or">
            A.name is not null
            <if test="condition.includeDelete == null or !condition.includeDelete">
                and !A.is_deleted
            </if>
            <if test="condition.nameLike != null">
                and A.name like concat('%',#{condition.nameLike},'%')
            </if>
            <if test="condition.fileSystemNameLike != null ">
                and A.file_system_name like concat('%',#{condition.fileSystemNameLike},'%')
            </if>
            <if test="condition.name != null">
                and A.name = #{condition.name}
            </if>
            <if test="condition.envId != null">
                and A.cloud_env_id = #{condition.envId}
            </if>
            <if test="condition.status != null">
                and A.status = #{condition.status}
            </if>
            <if test="condition.soonMark != null ">
                and  A.end_time &lt;= #{condition.afterDay}
            </if>
            <if test="condition.normalMark != null ">
                and (A.end_time &gt; #{condition.afterDay} or  A.end_time is null)
            </if>
            <if test="condition.selfServiceEqual != null">
                AND SO.ID IS NOT NULL
            </if>
            <if test="condition.ownerId!=null">
                AND A.owner_id = #{condition.ownerId}
            </if>
            <if test="condition.createdOrgSid!=null">
                <choose>
                    <when test="condition.createdOrgSid == 0">
                        AND A.created_org_sid =0
                        AND A.uuid NOT IN
                        (SELECT uuid FROM res_share WHERE !ISNULL(uuid) AND is_virtual = 0 GROUP BY uuid HAVING COUNT(uuid) != 1)
                        <if test="condition.uuidNotInList != null">
                            AND A.uuid NOT IN
                            <foreach item="item" index="index" collection="condition.uuidNotInList" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                        </if>
                    </when>
                    <otherwise>
                        AND A.created_org_sid = #{condition.createdOrgSid}
                    </otherwise>
                </choose>
            </if>
            <if test="condition.isShareDir!=null">
                AND A.is_share_dir = #{condition.isShareDir}
            </if>
            <if test="condition.filterOrgNullAndShareTypeIdNull == true">
                AND (A.org_sid is not null or A.share_type_id is not null)
            </if>
            <if test="condition.shareTypeId!=null">
                AND A.share_type_id = #{condition.shareTypeId}
            </if>
            <if test="condition.resourceIdList != null and condition.resourceIdList.size()>0">
                and A.id in
                <foreach item="item" index="index" collection="condition.resourceIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.shareProto!=null">
                <choose>
                    <when test="condition.shareProtoList!=null">
                        <foreach collection="condition.shareProtoList" item="item" open=" " close=" "
                                 separator=" ">
                            AND A.share_proto LIKE CONCAT('%',#{item},'%')
                        </foreach>
                    </when>
                    <otherwise>
                        AND A.share_proto = #{condition.shareProto}
<!--                        AND A.share_proto LIKE CONCAT('%',#{condition.shareProto},'%')-->
                    </otherwise>
                </choose>
            </if>
            <if test="condition.chargeType!=null">
                AND A.charge_type = #{condition.chargeType}
            </if>
            <if test="!condition.includeVirtual">
                AND !A.is_virtual
            </if>
            <if test="condition.shareTypeName != null">
                and IFNULL(A.share_Type_name, A.share_type) like concat('%',#{condition.shareTypeName},'%')
            </if>
            <if test="condition.shareType != null">
                and A.share_Type like concat('%',#{condition.shareType},'%')
            </if>
            <if test="condition.statusNotIn!=null and condition.statusNotIn.size() > 0">
                AND A.status not in
                <foreach collection="condition.statusNotIn" item="item" open="(" close=")"
                    separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.statusIn!=null and condition.statusIn.size() > 0">
                AND A.status in
                <foreach collection="condition.statusIn" item="item" open="(" close=")"
                         separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.day!=null">
                AND now()>date_sub(A.end_time, interval #{condition.day} day)
            </if>
            <if test="condition.hpcClusterId !=null">
                AND A.id in (select resource_id from res_hpc_cluster_resource rhcr where rhcr.cluster_id =#{condition.hpcClusterId} and rhcr.resource_type in ("sfs","DME-OSP"))
            </if>
            <if test="condition.notIncludeUsed">
                AND A.id not in (select resource_id from res_hpc_cluster_resource rhcr where rhcr.resource_type="sfs")
            </if>
            <if test="condition.isDeleted!=null">
                AND A.is_deleted = #{condition.isDeleted}
            </if>
            <if test="condition.createdOrgSidNotEq!=null">
                AND A.created_org_sid != #{condition.createdOrgSidNotEq}
            </if>
            <if test="condition.orgSid != null">
                AND A.org_sid = #{condition.orgSid}
            </if>
            <if test="condition.orgSidIn!=null and condition.orgSidIn.size() > 0">
                AND A.org_sid in
                <foreach collection="condition.orgSidIn" item="item" open="(" close=")"
                    separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.createdBy != null">
                AND A.created_by = #{condition.createdBy}
            </if>
            <if test="condition.orgSidNotEmpty">
                AND A.org_sid is not null AND A.org_sid != 0
            </if>
            <if test="condition.categoryId!=null">
                AND A.category_id = #{condition.categoryId}
            </if>
            <if test="condition.createdBy != null">
                and A.created_by like concat('%',#{condition.createdBy},'%')
            </if>
            <if test="condition.isClusterDefault != null">
                and A.is_cluster_default = #{condition.isClusterDefault}
            </if>
            <if test="condition.supportClusterType != null">
                <choose>
                    <when test="condition.supportClusterType == 'ondemand'">
                        and (ISNULL(A.support_cluster_type) or A.support_cluster_type = #{condition.supportClusterType})
                    </when>
                    <otherwise>
                        and A.support_cluster_type = #{condition.supportClusterType}
                    </otherwise>
                </choose>
            </if>
            <if test="condition.linksNotLike != null">
                AND links NOT LIKE CONCAT('%',#{condition.linksNotLike},'%')
            </if>
            <if test="condition.accountName != null">
                and o.org_name like concat('%',#{condition.accountName},'%')
            </if>
            <if test="condition.sfsIds!=null and condition.sfsIds.size() > 0">
                AND A.id not in
                <foreach collection="condition.sfsIds" item="item" open="(" close=")"
                    separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.quotaRelease == false" >
                and ropq.resource_id is not null and ropq.release_time is not null
            </if>
            <if test="condition.quotaRelease == true" >
                and ropq.id is null
            </if>
        </trim>
    </sql>
    <sql id="Example_Where_Clause_Without_Alias">
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.orNoDeleted == null">
                and !is_deleted
            </if>
            <if test="condition.nameLike != null">
                and name like concat('%',#{condition.nameLike},'%')
            </if>
            <if test="condition.name != null">
                and name = #{condition.name}
            </if>
            <if test="condition.uuid != null">
                and uuid = #{condition.uuid}
            </if>
            <if test="condition.envId != null">
                and cloud_env_id = #{condition.envId}
            </if>
            <if test="condition.status != null">
                and status = #{condition.status}
            </if>
            <if test="condition.inStatusList != null">
                and status in
                <foreach item="inStatus" index="index" collection="condition.inStatusList"
                    open="(" separator="," close=")">
                    #{inStatus}
                </foreach>
            </if>
            <if test="condition.ownerId!=null">
                AND owner_id = #{condition.ownerId}
            </if>
            <if test="condition.shareTypeId!=null">
                AND share_type_id = #{condition.shareTypeId}
            </if>
            <if test="condition.isShareDir!=null">
                AND is_share_dir = #{condition.isShareDir}
            </if>
            <if test="condition.createdOrgSid!=null">
                AND created_org_sid = #{condition.createdOrgSid}
            </if>
            <if test="condition.resShareIds != null">
                and id in
                <foreach collection="condition.resShareIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.isClusterDefault != null">
                and is_cluster_default = #{condition.isClusterDefault}
            </if>
            <if test="condition.supportClusterType != null">
                and support_cluster_type = #{condition.supportClusterType}
            </if>
            <if test="condition.fileSystemName != null">
                and file_system_name = #{condition.fileSystemName}
            </if>
            <if test="condition.quotaId != null">
                and quota_id = #{condition.quotaId}
            </if>
            <if test="condition.quotaIdIn != null and condition.quotaIdIn.size() > 0">
                and quota_id in
                <foreach collection="condition.quotaIdIn" item="itemId" open="(" close=")" separator=",">
                    #{itemId}
                </foreach>
            </if>
        </trim>
    </sql>

    <select id="getShareList" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultMap="BaseResultMap">
        SELECT
        (select count(*) from res_share_rule B where B.share_id = A.id) as accessNumber,
        CASE B.cloud_env_type
        WHEN 'Aliyun' THEN ( SELECT count( * ) FROM res_share_mount_target st WHERE st.share_id = A.id)
        ELSE (select count(*) from res_share_target C where C.share_id = A.id and C.status = 'installed')
        END AS mountNumber,
        A.availability_zone as availabilityName,
        F.targetNames,
        F.localPaths,
        B.cloud_env_name, B.cloud_env_type,
        GROUP_CONCAT(I.tag_name ORDER BY I.tag_id) as tagNames,
        GROUP_CONCAT(I.rgb_code ORDER BY I.tag_id) as rgbCodes,
        GROUP_CONCAT(I.tag_value ORDER BY I.tag_id) as tagValues,
        GROUP_CONCAT(target.mount_target_domain) as ip,
        vpc.id as vpc_id,
        vpc.name as vpc_name,
        <include refid="getShareList_Column_List_alias"/>
        ,IFNULL(A.share_Type_name, A.share_type) as share_type_name
        ,IFNULL(A.size_allocate,A.size) as size_allocate,
        O.org_name as orgName,
        cluster.name as cluster_name,
        cluster.cluster_type as cluster_type,
        cluster.id as cluster_id
        from
        res_share A
        left join cloud_env B on B.id = A.cloud_env_id
        left join sys_m_code code on a.share_type = code.code_value and code.CODE_CATEGORY = 'RES_SHARE_STORAGE_TYPE'
        LEFT JOIN cloud_tag_ref D ON A.id = D.obj_id AND D.obj_type = 'sfs'
        LEFT JOIN cloud_tag I on (D.tag_id= I.tag_id)
        left join sys_m_org O on A.org_sid=O.org_sid
        <if test="condition.tagName != null">
            inner JOIN (select H.obj_id from cloud_tag_ref H
            LEFT JOIN cloud_tag I on (H.tag_id= I.tag_id and H.obj_type = 'sfs')
            <where>
                <if test="condition.tagName != null">
                    and I.tag_name like concat(#{condition.tagName}, '%')
                </if>
                <if test="condition.tagValue != null">
                    and I.tag_value like concat(#{condition.tagValue}, '%')
                </if>
            </where>
            GROUP BY H.obj_id) HIT on HIT.obj_id = A.ID
        </if>
        LEFT JOIN
        (select T.share_id,GROUP_CONCAT(T.name ORDER BY T.id) as targetNames,
        GROUP_CONCAT(T.local_path ORDER BY T.id) as localPaths
        from res_share_target T WHERE
        T.status = 'installed' GROUP BY T.share_id) F on (F.share_id = A.id)
        LEFT JOIN res_share_mount_target target on A.id = target.share_id
        left join network N on ((N.network_uuid = A.share_network_id or N.uuid = A.share_network_id) and A.cloud_env_id
        = N.cloud_env_id)
        left join res_vpc vpc on ((vpc.id = N.net_vpc_id or target.vpc_info = vpc.uuid) and A.cloud_env_id =
        vpc.cloud_env_id)
        left join res_hpc_cluster_resource resource on resource.resource_id = A.id and resource.resource_type = 'SFS'
        left join res_hpc_cluster cluster on cluster.id = resource.cluster_id
        <if test="condition.requestSource != null and condition.requestSource=='manager'">
            RIGHT JOIN (
            SELECT resource_id FROM bss.service_order_resource_ref a
            LEFT JOIN bss.service_order_detail b on a.order_detail_id = b.id
            LEFT JOIN bss.service_order c on b.order_id = c.id
            where c.entity_id =#{condition.entityId} and  a.type like concat('%', #{condition.serviceType} ,'%')
            <if test="condition.sfs2FlgNum != null and condition.sfs2FlgNum==1">
                union all
                SELECT id FROM res_share WHERE is_cluster_default = 1 AND support_cluster_type = 'predeploy' AND is_virtual = 0
            </if>
            ) G on A.id = G.resource_id
        </if>
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>

        GROUP BY A.id
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>

    <select id="getShareList2" resultType="cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResShare">
        select
        <include refid="getShareList_Column_List_alias"/>
        ,o.org_name AS orgName, o.org_name AS accountName, IFNULL( a.share_Type_name, a.share_type ) AS share_type_name,
        cluster.NAME AS cluster_name,	cluster.cluster_type AS cluster_type, cluster.status as clusterStatus,
        <if test="condition.requestSource != null and condition.requestSource=='manager'">
            b.extraAttr, b.serviceOrderSn,
        </if>
        cluster.id AS cluster_id
        from res_share a
        LEFT JOIN sys_m_org o ON a.org_sid = o.org_sid
        LEFT JOIN res_hpc_cluster_resource resource ON resource.resource_id = a.id and resource.resource_type in ('SFS','DME-OSP')
        LEFT JOIN res_hpc_cluster cluster ON cluster.id = resource.cluster_id
        <if test="condition.quotaRelease != null" >
            left join res_oceanstor_p_quota ropq on a.quota_id = ropq.id
        </if>
        <if test="condition.requestSource != null and condition.requestSource=='manager'">
            right join
            (
            SELECT resource_id, b.service_config extraAttr, c.order_sn serviceOrderSn FROM bss.service_order_resource_ref a
            LEFT JOIN bss.service_order_detail b ON a.order_detail_id = b.id
            LEFT JOIN bss.service_order c ON b.order_id = c.id
            where c.entity_id =#{condition.entityId} and a.type in
            <foreach collection="condition.serviceTypeIn" item="stype" open="(" close=")" separator=",">
                #{stype}
            </foreach>
            <if test="condition.sfs2FlgNum != null and condition.sfs2FlgNum==1">
                union
                SELECT id, '' as extraAttr, '' as serviceOrderSn FROM res_share WHERE is_cluster_default = 1 AND support_cluster_type = 'predeploy' AND is_virtual = 0
            </if>
            <if test="condition.hpcClusterId !=null">
                union
                select rhcr.resource_id, null as extraAttr, so.order_sn as serviceOrderSn from res_hpc_cluster rhc
                left join bss.service_order so on rhc.id = so.cluster_id
                left join res_hpc_cluster_resource rhcr on rhc.id = rhcr.cluster_id
                where rhc.id =#{condition.hpcClusterId}
                and (
                (rhcr.resource_type="sfs" and rhc.name = '共享资源池-SAAS') or (rhcr.resource_type="DME-OSP" and
                rhc.name = '共享资源池-二期')
                )
            </if>
            <if test="condition.hpsSaas and condition.orgSidIn!=null and condition.orgSidIn.size() > 0">
                union
                select rhcr.resource_id, null as extraAttr, so.order_sn as serviceOrderSn from res_hpc_cluster rhc
                left join bss.service_order so on rhc.id = so.cluster_id
                left join res_hpc_cluster_resource rhcr on rhc.id = rhcr.cluster_id
                where rhc.org_sid in
                <foreach collection="condition.orgSidIn" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                and (
                (rhcr.resource_type="sfs" and rhc.name = '共享资源池-SAAS') or (rhcr.resource_type="DME-OSP" and
                rhc.name = '共享资源池-二期')
                )
            </if>
            ) b ON a.id = b.resource_id
        </if>

        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>

        group by a.id

        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>

    <select id="selectShareListByOrgId" parameterType="java.lang.Long"
        resultMap="BaseResultMap">
        SELECT (SELECT COUNT(*) FROM res_share_rule B WHERE B.share_id = A.id)   AS accessNumber,
               CASE B.cloud_env_type
                   WHEN 'Aliyun' THEN (SELECT COUNT(*) FROM res_share_mount_target st WHERE st.share_id = A.id)
                   ELSE (SELECT COUNT(*)
                         FROM res_share_target C
                         WHERE C.share_id = A.id AND C.status = 'installed') END AS mountNumber,
               A.availability_zone                                               AS availabilityName,
               F.targetNames,
               F.localPaths,
               B.cloud_env_name,
               B.cloud_env_type,
               GROUP_CONCAT(I.tag_name ORDER BY I.tag_id)                        AS tagNames,
               GROUP_CONCAT(I.rgb_code ORDER BY I.tag_id)                        AS rgbCodes,
               GROUP_CONCAT(I.tag_value ORDER BY I.tag_id)                       AS tagValues,
               GROUP_CONCAT(target.mount_target_domain)                          AS ip,
               vpc.id                                                            AS vpc_id,
               vpc.name                                                          AS vpc_name,
               A.id,
               A.`name`,
               A.uuid,
               A.cloud_env_id,
               A.`type`,
               A.links,
               A.availability_zone,
               A.share_server_id,
               A.share_network_id,
               A.consistency_group_id,
               A.share_proto,
               A.`size`,
               A.used_size,
               A.task_state,
               A.has_replicas,
               A.replication_type,
               A.host,
               A.created_at,
               A.volume_type,
               A.export_locations,
               A.export_location,
               A.snapshot_support,
               A.snapshot_id,
               A.is_public,
               A.share_type,
               A.snapshot_type,
               A.status,
               A.description,
               A.metadata,
               A.org_sid,
               A.created_by,
               A.created_dt,
               A.updated_by,
               A.updated_dt,
               A.error_msg,
               A.start_time,
               A.end_time,
               A.security_group_id,
               A.owner_id,
               A.created_org_sid,
               A.share_type_id,
               A.is_share_dir,
               A.charge_type,
               A.hpc_version,
               A.freezing_time,
               A.is_virtual,
               A.is_cluster_default,
               A.support_cluster_type,
               IFNULL(A.share_Type_name, A.share_type)                           AS share_type_name,
               IFNULL(A.size_allocate, A.size)                                   AS size_allocate,
               O.org_name                                                        AS orgName,
               cluster.name                                                      AS cluster_name,
               cluster.cluster_type                                              AS cluster_type,
               cluster.id                                                        AS cluster_id
        FROM res_share A
                 LEFT JOIN cloud_env B ON B.id = A.cloud_env_id
                 LEFT JOIN sys_m_code code
                           ON a.share_type = code.code_value AND code.CODE_CATEGORY = 'RES_SHARE_STORAGE_TYPE'
                 LEFT JOIN cloud_tag_ref D ON A.id = D.obj_id AND D.obj_type = 'sfs'
                 LEFT JOIN cloud_tag I ON D.tag_id = I.tag_id
                 LEFT JOIN sys_m_org O ON A.org_sid = O.org_sid
                 LEFT JOIN (SELECT T.share_id,
                                   GROUP_CONCAT(T.name ORDER BY T.id)       AS targetNames,
                                   GROUP_CONCAT(T.local_path ORDER BY T.id) AS localPaths
                            FROM res_share_target T
                            WHERE T.status = 'installed'
                            GROUP BY T.share_id) F ON F.share_id = A.id
                 LEFT JOIN res_share_mount_target target ON A.id = target.share_id
                 LEFT JOIN network N ON (N.network_uuid = A.share_network_id OR N.uuid = A.share_network_id) AND
                                        A.cloud_env_id = N.cloud_env_id
                 LEFT JOIN res_vpc vpc ON (vpc.id = N.net_vpc_id OR target.vpc_info = vpc.uuid) AND
                                          A.cloud_env_id = vpc.cloud_env_id
                 LEFT JOIN res_hpc_cluster_resource resource
                           ON resource.resource_id = A.id AND resource.resource_type = 'SFS'
                 LEFT JOIN res_hpc_cluster cluster ON cluster.id = resource.cluster_id
        WHERE !A.is_deleted
          AND A.status !='deleted'
          AND EXISTS ( SELECT 1 FROM sys_m_org WHERE (org_sid = #{orgId}
           OR tree_path LIKE concat('/' , #{orgId} , '/%'))
          AND org_sid = A.org_sid )
        GROUP BY A.id
        ORDER BY A.status ASC, A.created_dt DESC
    </select>

    <delete id="deleteShareById" parameterType="java.lang.Long">
        delete
        from res_share
        where id = #{id}
    </delete>

    <delete id="deleteDefaultShareById" parameterType="java.lang.Long">
        delete
        from res_share
        where id = #{id} and is_cluster_default=1
    </delete>

    <delete id="logicDeleteShareById" parameterType="java.lang.Long">
        update res_share
        set is_deleted= true,
            status='deleted'
        where id = #{id}
    </delete>


    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        C.name as availabilityName,A.share_type_id,
        <include refid="Base_Column_List_alias"/>
        from
        res_share A
        left join res_zone C on A.cloud_env_id = C.cloud_env_id and A.availability_zone = C.uuid
        where A.id = #{id}
    </select>
    <select id="selectShareDetailByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        C.name as availabilityName,
        sg.name as security_group_name,sg.id as security_group_pid,
        N.network_name as subnet_name, N.id as subnet_pid,
        V.name as vpc_name, V.id as vpc_id,
        IFNULL(A.share_Type_name, code.CODE_DISPLAY) as share_type_name,
        <include refid="Base_Column_List_alias"/>
        from
        res_share A
        left join res_zone C on A.cloud_env_id = C.cloud_env_id and A.availability_zone = C.uuid
        left join res_security_group sg on sg.uuid = A.security_group_id and A.cloud_env_id = sg.cloud_env_id
        left join network N on ((N.network_uuid = A.share_network_id or N.uuid = A.share_network_id) and A.cloud_env_id
        = N.cloud_env_id)
        left join res_vpc V on V.id = N.net_vpc_id
        left join sys_m_code code on a.share_type = code.code_value and code.CODE_CATEGORY = 'RES_SHARE_STORAGE_TYPE'
        where A.id = #{id}
    </select>
    <select id="selectByParams" resultType="cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResShare">
        select
        <include refid="Base_Column_List"/>
        from res_share
        <if test="_parameter != null">
            <include refid="Example_Where_Clause_Without_Alias"/>
        </if>
        <if test="orderByClause !=null " >
            order by ${orderByClause}
        </if>
    </select>
    <delete id="deleteByParams" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        delete from res_share
        <if test="_parameter != null">
            <include refid="Example_Where_Clause_Without_Alias"/>
        </if>
    </delete>

    <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id"
        parameterType="cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResShare">
        insert into res_share
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">
                `name`,
            </if>
            <if test="fileSystemName != null">
                `file_system_name`,
            </if>
            <if test="uuid != null">
                uuid,
            </if>
            <if test="cloudEnvId != null">
                cloud_env_id,
            </if>
            <if test="type != null">
                `type`,
            </if>
            <if test="links != null">
                links,
            </if>
            <if test="availabilityZone != null">
                availability_zone,
            </if>
            <if test="shareServerId != null">
                share_server_id,
            </if>
            <if test="shareNetworkId != null">
                share_network_id,
            </if>
            <if test="securityGroupId != null">
                security_group_id,
            </if>
            <if test="consistencyGroupId != null">
                consistency_group_id,
            </if>
            <if test="shareProto != null">
                share_proto,
            </if>
            <if test="size != null">
                `size`,
            </if>
            <if test="usedSize != null">
                used_size,
            </if>
            <if test="taskState != null">
                task_state,
            </if>
            <if test="hasReplicas != null">
                has_replicas,
            </if>
            <if test="replicationType != null">
                replication_type,
            </if>
            <if test="host != null">
                host,
            </if>
            <if test="createdAt != null">
                created_at,
            </if>
            <if test="volumeType != null">
                volume_type,
            </if>
            <if test="exportLocations != null">
                export_locations,
            </if>
            <if test="exportLocation != null">
                export_location,
            </if>
            <if test="snapshotSupport != null">
                snapshot_support,
            </if>
            <if test="snapshotId != null">
                snapshot_id,
            </if>
            <if test="isPublic != null">
                is_public,
            </if>
            <if test="shareType != null">
                share_type,
            </if>
            <if test="shareTypeName != null">
                share_type_name,
            </if>
            <if test="snapshotType != null">
                snapshot_type,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="metadata != null">
                metadata,
            </if>
            <if test="orgSid != null">
                org_sid,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="createdDt != null">
                created_dt,
            </if>
            <if test="updatedBy != null">
                updated_by,
            </if>
            <if test="updatedDt != null">
                updated_dt,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="ownerId!=null">
                owner_id,
            </if>
            <if test="createdOrgSid!=null">
                created_org_sid,
            </if>
            <if test="startTime!=null">
                start_time,
            </if>
            <if test="endTime!=null">
                end_time,
            </if>
            <if test="chargeType!=null">
                charge_type,
            </if>
            <if test="isVirtual!=null">
                is_virtual,
            </if>
            <if test="shareTypeId!=null">
                share_type_id,
            </if>
            <if test="hpcVersion!=null">
                hpc_version,
            </if>
            <if test="freezingTime!=null">
                freezing_time,
            </if>
            <if test="categoryId!=null">
                category_id,
            </if>
            <if test="isClusterDefault!=null">
                is_cluster_default,
            </if>
            <if test="supportClusterType!=null">
                support_cluster_type,
            </if>
            <if test="quotaId != null">
                quota_id,
            </if>
            <if test="orgId != null">
                org_id,
            </if>
            <if test="zoneId != null">
                zone_id,
            </if>
            <if test="subnetId != null">
                subnet_id,
            </if>

        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">
                #{name},
            </if>
            <if test="fileSystemName != null">
                #{fileSystemName},
            </if>
            <if test="uuid != null">
                #{uuid},
            </if>
            <if test="cloudEnvId != null">
                #{cloudEnvId},
            </if>
            <if test="type != null">
                #{type},
            </if>
            <if test="links != null">
                #{links},
            </if>
            <if test="availabilityZone != null">
                #{availabilityZone},
            </if>
            <if test="shareServerId != null">
                #{shareServerId},
            </if>
            <if test="shareNetworkId != null">
                #{shareNetworkId},
            </if>
            <if test="securityGroupId != null">
                #{securityGroupId},
            </if>
            <if test="consistencyGroupId != null">
                #{consistencyGroupId},
            </if>
            <if test="shareProto != null">
                #{shareProto},
            </if>
            <if test="size != null">
                #{size},
            </if>
            <if test="usedSize != null">
                #{usedSize},
            </if>
            <if test="taskState != null">
                #{taskState},
            </if>
            <if test="hasReplicas != null">
                #{hasReplicas},
            </if>
            <if test="replicationType != null">
                #{replicationType},
            </if>
            <if test="host != null">
                #{host},
            </if>
            <if test="createdAt != null">
                #{createdAt},
            </if>
            <if test="volumeType != null">
                #{volumeType},
            </if>
            <if test="exportLocations != null">
                #{exportLocations},
            </if>
            <if test="exportLocation != null">
                #{exportLocation},
            </if>
            <if test="snapshotSupport != null">
                #{snapshotSupport},
            </if>
            <if test="snapshotId != null">
                #{snapshotId},
            </if>
            <if test="isPublic != null">
                #{isPublic},
            </if>
            <if test="shareType != null">
                #{shareType},
            </if>
            <if test="shareTypeName != null">
                #{shareTypeName},
            </if>
            <if test="snapshotType != null">
                #{snapshotType},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="description != null">
                #{description},
            </if>
            <if test="metadata != null">
                #{metadata},
            </if>
            <if test="orgSid != null">
                #{orgSid},
            </if>
            <if test="createdBy != null">
                #{createdBy},
            </if>
            <if test="createdDt != null">
                #{createdDt},
            </if>
            <if test="updatedBy != null">
                #{updatedBy},
            </if>
            <if test="updatedDt != null">
                #{updatedDt},
            </if>
            <if test="version != null">
                #{version},
            </if>
            <if test="ownerId!=null">
                #{ownerId},
            </if>
            <if test="createdOrgSid!=null">
                #{createdOrgSid},
            </if>
            <if test="startTime!=null">
                #{startTime},
            </if>
            <if test="endTime!=null">
                #{endTime},
            </if>
            <if test="chargeType!=null">
                #{chargeType},
            </if>
            <if test="isVirtual!=null">
                #{isVirtual},
            </if>
            <if test="shareTypeId!=null">
                #{shareTypeId},
            </if>
            <if test="hpcVersion!=null">
                #{hpcVersion},
            </if>
            <if test="freezingTime!=null">
                #{freezingTime},
            </if>
            <if test="categoryId!=null">
                #{categoryId},
            </if>
            <if test="isClusterDefault!=null">
                #{isClusterDefault},
            </if>
            <if test="supportClusterType!=null">
                #{supportClusterType},
            </if>
            <if test="quotaId!=null">
                #{quotaId},
            </if>
            <if test="orgId != null">
                #{orgId},
            </if>
            <if test="zoneId != null">
                #{zoneId},
            </if>
            <if test="subnetId != null">
                #{subnetId},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKey" parameterType="cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResShare">
        update res_share
        <set>
            <if test="name != null">
                `name`= #{name},
            </if>
            <if test="fileSystemName != null">
                `file_system_name`= #{fileSystemName},
            </if>
            <if test="uuid != null">
                uuid = #{uuid},
            </if>
            <if test="cloudEnvId != null">
                cloud_env_id = #{cloudEnvId},
            </if>
            <if test="type != null">
                `type` = #{type},
            </if>
            <if test="links != null">
                links = #{links},
            </if>
            <if test="availabilityZone != null">
                availability_zone = #{availabilityZone},
            </if>
            <if test="shareServerId != null">
                share_server_id = #{shareServerId},
            </if>
            <if test="shareNetworkId != null">
                share_network_id = #{shareNetworkId},
            </if>
            <if test="securityGroupId != null">
                security_group_id = #{securityGroupId},
            </if>
            <if test="consistencyGroupId != null">
                consistency_group_id = #{consistencyGroupId},
            </if>
            <if test="shareProto != null">
                share_proto = #{shareProto},
            </if>
            <if test="size != null">
                `size` = #{size},
            </if>
            <if test="usedSize != null">
                used_size = #{usedSize},
            </if>
            <if test="taskState != null">
                task_state = #{taskState},
            </if>
            <if test="hasReplicas != null">
                has_replicas = #{hasReplicas},
            </if>
            <if test="replicationType != null">
                replication_type = #{replicationType},
            </if>
            <if test="host != null">
                host = #{host},
            </if>
            <if test="createdAt != null">
                created_at = #{createdAt},
            </if>
            <if test="volumeType != null">
                volume_type = #{volumeType},
            </if>
            <if test="exportLocations != null">
                export_locations = #{exportLocations},
            </if>
            <if test="exportLocation != null">
                export_location = #{exportLocation},
            </if>
            <if test="snapshotSupport != null">
                snapshot_support = #{snapshotSupport},
            </if>
            <if test="snapshotId != null">
                snapshot_id = #{snapshotId},
            </if>
            <if test="isPublic != null">
                is_public = #{isPublic},
            </if>
            <if test="shareType != null">
                share_type = #{shareType},
            </if>
            <if test="shareTypeName != null">
                share_type_name = #{shareTypeName},
            </if>
            <if test="snapshotType != null">
                snapshot_type = #{snapshotType},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="description != null">
                description = #{description},
            </if>
            <if test="metadata != null">
                metadata = #{metadata},
            </if>
            <if test="orgSid != null">
                org_sid = #{orgSid},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy},
            </if>
            <if test="createdDt != null">
                created_dt = #{createdDt},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy},
            </if>
            <if test="updatedDt != null">
                updated_dt = #{updatedDt},
            </if>
            <if test="version != null">
                version = #{version},
            </if>
            <if test="errorMsg != null">
                error_msg = #{errorMsg},
            </if>
            <if test="ownerId!=null">
                owner_id = #{ownerId},
            </if>
            <if test="createdOrgSid!=null">
                created_org_sid = #{createdOrgSid},
            </if>
            <if test="endTime!=null">
                end_time = #{endTime},
            </if>
            <if test="isShareDir!=null">
                is_share_dir = #{isShareDir},
            </if>
            <if test="shareTypeId!=null">
                share_type_id = #{shareTypeId},
            </if>
            <if test="hpcVersion!=null">
                hpc_version = #{hpcVersion},
            </if>
            <if test="endTime!=null">
                end_time = #{endTime},
            </if>
            <if test="freezingTime!=null">
                freezing_time = #{freezingTime},
            </if>
            <if test="sizeAllocate!=null">
                size_allocate = #{sizeAllocate},
            </if>
            <if test="startTime!=null">
                start_time=#{startTime},
            </if>
            <if test="chargeType!=null">
                charge_type=#{chargeType},
            </if>
            <if test="isClusterDefault!=null">
                is_cluster_default=#{isClusterDefault},
            </if>
            <if test="supportClusterType!=null">
                support_cluster_type=#{supportClusterType},
            </if>
             <if test="quotaId != null">
                quota_id =#{quotaId},
            </if>
        </set>
        where id = #{id}
    </update>
    <update id="updateLifeCycleByPrimaryKey"
        parameterType="cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResShare">
        update res_share
        set start_time = #{creationTime},
            end_time   = #{expireTime}
        where id = #{id}
    </update>
    <update id="resetShareDirByShareTypeId">
        update res_share
        set is_share_dir=0,
            size_allocate=null
        where share_type_id = #{shareTypeId}
          and is_share_dir = #{type}
    </update>

    <select id="countShareList" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultType="java.lang.Integer">
        SELECT count(*)
        from
        res_share A
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>

    <select id="countAllocShareByDf" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultType="java.lang.Integer">
        select count(*) from res_share T
        LEFT JOIN cloud_env B on T.cloud_env_id = B.id
        WHERE T.status NOT IN ('deleting','error','creating','error_deleting'
        -- ,'manage_starting','manage_error','unmanage_starting','unmanage_error',
        -- 'unmanaged','extending','extending_error','shrinking_possible_data_loss_error'
        )
        <if test="condition.cloudEnvTypeNotIn != null">
            AND B.cloud_env_type NOT IN
            <foreach collection="condition.cloudEnvTypeNotIn" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="condition.cloudEnvTypes != null">
            and B.CLOUD_ENV_TYPE in
            <foreach item="item" index="index" collection="condition.cloudEnvTypes"
                open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="condition.df != null">
            ${condition.df}
        </if>
    </select>

    <select id="selectSelfServiceByParams"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria" resultMap="BaseResultMap">
        SELECT
        (select count(*) from res_share_rule B where B.share_id = A.id) as accessNumber,
        CASE B.cloud_env_type
        WHEN 'Aliyun' THEN ( SELECT count( * ) FROM res_share_mount_target st WHERE st.share_id = A.id)
        ELSE (select count(*) from res_share_target C where C.share_id = A.id and C.status = 'installed')
        END AS mountNumber,
        C.name as availabilityName,
        F.targetNames,
        F.localPaths,
        B.cloud_env_name, B.cloud_env_type,
        GROUP_CONCAT(I.tag_name ORDER BY I.tag_id) as tagNames,
        GROUP_CONCAT(I.rgb_code ORDER BY I.tag_id) as rgbCodes,
        GROUP_CONCAT(I.tag_value ORDER BY I.tag_id) as tagValues,
        GROUP_CONCAT(target.mount_target_domain) as ip,
        vpc.id as vpc_id,
        vpc.name as vpc_name,
        <include refid="Base_Column_List_alias"/>
        ,IFNULL(A.share_Type_name, A.share_type) as share_type_name
        ,IFNULL(A.size_allocate,A.size) as size_allocate,
        SO.delete_resource as delete_resource,
        SOBRR.id AS deploy_id,
        SO.id as service_order_id,
        SO.STATUS as server_status,
        SO.order_sn as service_order_sn
        from
        res_share A
        left join cloud_env B on B.id = A.cloud_env_id
        left join res_zone C on A.cloud_env_id = C.cloud_env_id and A.availability_zone = C.uuid
        left join sys_m_code code on a.share_type = code.code_value and code.CODE_CATEGORY = 'RES_SHARE_STORAGE_TYPE'
        LEFT JOIN cloud_tag_ref D ON A.id = D.obj_id AND D.obj_type = 'sfs'
        LEFT JOIN cloud_tag I on (D.tag_id= I.tag_id)
        <if test="condition.tagName != null">
            inner JOIN (select H.obj_id from cloud_tag_ref H
            LEFT JOIN cloud_tag I on (H.tag_id= I.tag_id and H.obj_type = 'sfs')
            <where>
                <if test="condition.tagName != null">
                    and I.tag_name like concat(#{condition.tagName}, '%')
                </if>
                <if test="condition.tagValue != null">
                    and I.tag_value like concat(#{condition.tagValue}, '%')
                </if>
            </where>
            GROUP BY H.obj_id) HIT on HIT.obj_id = A.ID
        </if>
        LEFT JOIN
        (select T.share_id,GROUP_CONCAT(T.name ORDER BY T.id) as targetNames,
        GROUP_CONCAT(T.local_path ORDER BY T.id) as localPaths
        from res_share_target T WHERE
        T.status = 'installed' GROUP BY T.share_id) F on (F.share_id = A.id)
        LEFT JOIN res_share_mount_target target on A.id = target.share_id
        left join network N on ((N.network_uuid = A.share_network_id or N.uuid = A.share_network_id) and A.cloud_env_id
        = N.cloud_env_id)
        left join res_vpc vpc on ((vpc.id = N.net_vpc_id or target.vpc_info = vpc.uuid) and A.cloud_env_id =
        vpc.cloud_env_id)
        LEFT JOIN service_order_basic_resource_relation SOBRR ON SOBRR.resource_id = A.id
        LEFT JOIN service_order SO ON SO.id = SOBRR.service_order_id
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        GROUP BY A.id
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>

    <select id="countByParams" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultType="java.lang.Integer">
        select count(*) from res_share A
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <select id="sumSizeByIds" resultType="java.math.BigDecimal">
        select ifnull(sum(if(used_size > size, used_size, size)),0) total from res_share
        <where>
            id in
            <foreach collection="ids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </select>
    <select id="getShareTypeId" resultType="java.lang.Long">
        select id
        from res_share_type
    </select>
    <select id="getShareId" resultType="java.lang.Long">
        select id
        from res_share
    </select>
    <update id="updateShareTypeByPrimaryKey"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        update res_share
        set share_type_id = #{condition.shareTypeId},
            is_share_dir = 0,
            size_allocate = null
        where id = #{condition.id}
    </update>
    <update id="increaseAllocateSize">
        update res_share
        set size_allocate=size_allocate + #{commonShareSize}
        where id = #{id}
    </update>

    <update id="updateShareResStatusToPreStatus">
        update res_share set `status` = 'expired' where `status` = 'frozen'
    </update>
    <select id="findOneUnuseShareByShareTypeId"
        resultType="cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResShare">
        select
        <include refid="Base_Column_List"/>
        from res_share
        where share_type_id = #{id}
        and cloud_env_id =#{cloudEnvId}
        and is_share_dir=0
        and (org_sid = 0 or org_sid is null)
        AND uuid NOT IN (SELECT uuid FROM res_share WHERE !ISNULL(uuid) AND is_virtual = 0 GROUP BY uuid HAVING COUNT(uuid) != 1);
    </select>

    <select id="selectByClusterKey" parameterType="java.lang.Long" resultType="cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResShare">
        select
        <include refid="Base_Column_List_alias"/>
        from res_share A inner join res_hpc_cluster_resource B on A.id = B.resource_id and B.resource_type = 'SFS'
        where B.cluster_id = #{clusterId,jdbcType=BIGINT}
    </select>
    <select id="selectDMEByClusterId" parameterType="java.lang.Long" resultType="cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResShare">
        select
        <include refid="Base_Column_List_alias"/>
        from res_share A inner join res_hpc_cluster_resource B on A.id = B.resource_id and B.resource_type = 'DME-OSP'
        where B.cluster_id = #{clusterId,jdbcType=BIGINT}
    </select>

    <select id="countShareByParams" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
        `res_share`
        WHERE
        `is_deleted` = '0'
        AND `status` != 'error'
        and  org_sid in (
            SELECT
            A.org_sid
            FROM
            sys_m_org A
            WHERE
            (A.parent_id = #{condition.orgSid} or A.org_sid = #{condition.orgSid})
        )
        and category_id=#{condition.categoryId}
    </select>

    <select id="getShareAllocateSize" parameterType="java.lang.Long" resultType="java.lang.Integer">
        select size_allocate
        from res_share A
        where A.is_share_dir=1
        and A.share_type_id=#{shareTypeId,jdbcType=BIGINT}
    </select>
    <select id="checkPredeployShare" resultType="java.lang.Boolean" parameterType="java.lang.Long">
        SELECT
                (
                    SELECT
                        COUNT(*)
                    FROM
                        res_share
                    WHERE
                        `status` NOT IN ('error', 'deleted', 'error_deleting')
                      AND is_cluster_default = 0
                      AND support_cluster_type = 'predeploy'
                      AND org_sid = #{orgSid}
                ) > 5
    </select>

    <update id="updateShareStatusByPrimaryKey">
        update res_share
        set status = #{status}
        where id = #{id}
    </update>

    <select id="sureMountPreResShare" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List"/>
        FROM
            res_share
        WHERE
            support_cluster_type = 'predeploy'
          AND is_cluster_default = 0
          AND STATUS IN ('available', 'modify_error', 'mount_error')
          AND owner_id = #{userSid,jdbcType=VARCHAR}
          AND id NOT IN (
            SELECT
                resource_id
            FROM
                res_hpc_cluster_resource
            WHERE
                resource_type = 'SFS'
        );
    </select>

    <!-- and (hpc_version is null or hpc_version < 3) 二期的hpc共享资源池不需要配额申请-->
    <select id="getQuotaSfs" resultMap="BaseResultMap">
        select * from res_share
        <trim prefix="where" prefixOverrides="and|or">
            <if test="orgSid!=null">
                and org_sid=#{orgSid}
            </if>
            <if test="shareId!=null">
                and id=#{shareId}
            </if>
        </trim>
        and status='available'
        AND (hpc_version is null or (`hpc_version` &lt;= 3 and category_id is not null) )
        and (used_size is null or used_size &lt; size)
    </select>

    <select id="getByPrimaryKeys" resultMap="BaseResultMap">
        select * from res_share
        <trim prefix="where" prefixOverrides="and|or">
            <if test="shareIds != null and shareIds.size() > 0">
                id in
                <foreach collection="shareIds" item="shareId" open="(" close=")" separator=",">
                    #{shareId}
                </foreach>
            </if>
        </trim>
    </select>

    <update id="updateResShares">
        update res_share
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="quota_size =case" suffix="end,">
                <foreach collection="resShares" item="share" index="index">
                    <if test="share.id !=null">
                        when id=#{share.id} then #{share.quotaSize}
                    </if>
                </foreach>
            </trim>
        </trim>
        <where>
            id in
            <foreach collection="resShares" item="share" separator="," close=")" open="(">
                #{share.id}
            </foreach>
        </where>
    </update>


    <select id="getBytIds" resultMap="BaseResultMap">
        select * from res_share
        <if test="ids !=null">
            where id in
            <foreach collection="ids" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="getByIdsAndOrgSids" resultMap="BaseResultMap">
        select id,org_sid from res_share
        <trim prefix="where" prefixOverrides="and|or">
            <if test="ids!=null">
                and id in
                <foreach collection="ids" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="orgSids!=null">
                and org_sid in
                <foreach collection="orgSids" item="orgSid" open="(" close=")" separator=",">
                    #{orgSid}
                </foreach>
            </if>
        </trim>
    </select>

    <select id="getById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from res_share where id=#{shareId,jdbcType=BIGINT}
    </select>

    <select id="sumSizeByIdGroupShareType" resultType="cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResShareSumInfo">
        select ifnull(sum(if(used_size > size, used_size, size)),0) useCount,GROUP_CONCAT(DISTINCT share_type) shareType from res_share
        <where>
            id in
            <foreach collection="ids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </select>
    <select id="getClusterDefaultIds" resultType="java.lang.String">
        SELECT id FROM res_share WHERE is_cluster_default = 1 AND support_cluster_type = 'predeploy' AND is_virtual = 0;
    </select>
    <select id="getHpcVersionSFSById" resultType="java.lang.Long">
        SELECT
        	rs.id
        FROM
        	res_share rs
        	LEFT JOIN res_hpc_cluster_resource rh ON rs.id = rh.resource_id
        	LEFT JOIN res_hpc_cluster rc ON rc.id = rh.cluster_id
        WHERE
        	rs.id IN
        	<foreach collection="list" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
            <if test="sfsProductCode == 'SFS'">
                AND rc.hpc_version = 1
            </if>
            <if test="sfsProductCode == 'SFS2.0'">
                AND (
                rc.hpc_version = 3
                OR rc.hpc_version IS NULL)
            </if>
    </select>
    <select id="countRelatedQuota" resultType="java.lang.Long">
        select count(1) from res_share rs where rs.status not in ('deleted') and rs.quota_id = #{quotaId}
    </select>
    <select id="getDMEOSPShareIdRelateQuota" resultType="java.lang.Long">
            select max(rs.id) from res_share rs
                left join res_oceanstor_p_quota ropq on ropq.id = rs.quota_id
                where rs.type = "DME-OSP" and ropq.resource_id is not null and rs.quota_id is not null
            group by rs.quota_id

    </select>

</mapper>
