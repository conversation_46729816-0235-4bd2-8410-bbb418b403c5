/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.service.storage.impl;

import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnv;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResVd;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.constants.res.status.ResVdStatus;
import cn.com.cloudstar.rightcloud.common.constants.res.type.CloudEnvType;
import cn.com.cloudstar.rightcloud.common.constants.res.type.StorageCategory;
import cn.com.cloudstar.rightcloud.common.exception.BizException;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicInfoUtil;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResStorage;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResVolumeType;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.platform.Storage;
import cn.com.cloudstar.rightcloud.resource.dao.env.CloudEnvMapper;
import cn.com.cloudstar.rightcloud.resource.dao.storage.ResStorageMapper;
import cn.com.cloudstar.rightcloud.resource.dao.storage.ResVolumeTypeMapper;
import cn.com.cloudstar.rightcloud.resource.service.storage.ResStorageService;
import cn.com.cloudstar.rightcloud.resource.service.storage.ResVdService;
import cn.hutool.core.collection.CollectionUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by liuxiaolu on 2017/8/24.
 */
@Component
public class ResStorageServiceImpl implements ResStorageService {

    @Autowired
    private ResStorageMapper resStorageMapper;

    @Autowired
    private CloudEnvMapper cloudEnvMapper;

    @Autowired
    private ResVolumeTypeMapper resVolumeTypeMapper;

    @Autowired
    private ResVdService resVdService;

    /**
     * Select by params list.
     *
     * @param envId the env id
     * @param criteria the criteria
     *
     * @return the list
     */
    @Override
    public List<Storage> selectByParams(Long envId, Criteria criteria) {
        criteria.put("orgSid", BasicInfoUtil.getCurrentOrgSid());

        CloudEnv cloudEnv = this.cloudEnvMapper.selectByPrimaryKey(envId);
        assertEnvExists(cloudEnv);
        List<ResStorage> resStorages;
        if (CloudEnvType.VMWARE.equals(cloudEnv.getCloudEnvType()) || CloudEnvType.FUSIONCOMPUTE.equals(
                cloudEnv.getCloudEnvType())) {
            criteria.setOrderByClause(null);
            resStorages = this.resStorageMapper.selectInfoOfShare(criteria);
        } else {
            resStorages = this.resStorageMapper.selectBaseInfoByParams(criteria);
        }

        return resStorages.stream().map(Storage::new).collect(Collectors.toList());
    }

    private void assertEnvExists(CloudEnv cloudEnv) {
        if (Objects.isNull(cloudEnv)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_832756465));
        }
    }

    @Override
    public List<ResStorage> selectStorageByEnvId(Long cloudEnvId) {
        return this.resStorageMapper.selectStorageByEnvId(cloudEnvId);
    }

    @Override
    public List<ResStorage> selectInfoOfShare(Criteria criteria) {
        return this.resStorageMapper.selectInfoOfShare(criteria);
    }

    @Override
    public List<Storage> selectInfoOfShareToStorage(Criteria criteria) {
        List<ResStorage> resStorages = this.resStorageMapper.selectInfoOfShare(criteria);

        List<Long> cloudEnvIds = resStorages.stream()
                                            .map(resStorage -> Long.parseLong(resStorage.getParentTopologySid()))
                                            .distinct()
                                            .collect(Collectors.toList());

        Map<String, Map<String, Long>> resVdMap = Maps.newHashMap();
        Map<String, Map<Long, String>> volumeTypeMapGroupByCloudEnvId = Maps.newHashMap();
        if (!CollectionUtil.isEmpty(cloudEnvIds)) {
            // 分配容量和分配率
            criteria = new Criteria();
            criteria.put("envIdList", cloudEnvIds);
            criteria.put("statusNotIn", Arrays.asList(ResVdStatus.DELETED, ResVdStatus.CREATING, ResVdStatus.FAILURE));
            List<ResVd> resVds = resVdService.selectBaseByParams(criteria);

            // {"云环境ID": {"数据存储ID": "硬盘列表"}}
            // 先按云环境ID分组
            // 再根据数据存储ID分组拿到对应的数据存储的已使用硬盘容量
            resVdMap = resVds.stream()
                             .filter(o -> !Strings.isNullOrEmpty(o.getAllocateResStorageSid()))
                             .collect(Collectors.groupingBy(o -> o.getCloudEnvId().toString(),
                                                            Collectors.groupingBy(ResVd::getAllocateResStorageSid,
                                                                                  Collectors.summingLong(
                                                                                          ResVd::getAllocateDiskSize))));

            List<ResVolumeType> volumeTypes = resVolumeTypeMapper.selectByParamsOpenStack(
                    new Criteria("envIdList", cloudEnvIds));
            for (Long id : cloudEnvIds) {
                Criteria criteria1 = new Criteria();
                CloudEnv cloudEnv = cloudEnvMapper.selectByPrimaryKey(id);
                if (CloudEnvType.getPublicCloud()
                                .stream()
                                .map(CloudEnvType::getValue)
                                .anyMatch(t -> t.contains(cloudEnv.getCloudEnvType()))) {
                    criteria1.put("envId", Long.valueOf(-1));
                    criteria1.put("cloudEnvType", cloudEnv.getCloudEnvType());
                    if (cloudEnv.getCloudEnvType().equalsIgnoreCase("HuaweiCloud")) {
                        criteria1.put("region", cloudEnv.getRegion());
                    }
                }
                volumeTypes.addAll(resVolumeTypeMapper.selectByParamsOpenStack(criteria1));
            }

            // {"云环境ID": {"存储类型ID": "存储类型名称"}}
            // 先按云环境ID分组，分组后的值id和name造map
            volumeTypeMapGroupByCloudEnvId = volumeTypes.stream()
                                                        .filter(o -> Objects.nonNull(o.getCloudEnvId()))
                                                        .collect(
                                                                Collectors.groupingBy(o -> o.getCloudEnvId().toString(),
                                                                                      Collectors.toMap(
                                                                                              ResVolumeType::getId,
                                                                                              ResVolumeType::getTypeName,
                                                                                              (s, a) -> s + a)));
        }

        List<Storage> list = new ArrayList<>();
        for (ResStorage resStorage : resStorages) {
            Storage storage = new Storage();
            storage.setId(resStorage.getResStorageSid());
            storage.setName(resStorage.getStorageName());
            storage.setTotalSize(resStorage.getTotalCapacity());
            storage.setVolumeTypeId(resStorage.getResVolumeTypeId());
            storage.setAvailableSize(resStorage.getAvailableCapacity());
            storage.setUsedSize(formatData(resStorage.getTotalCapacity() - resStorage.getAvailableCapacity()));
            storage.setUsage(formatData(storage.getUsedSize() / storage.getTotalSize() * 100));
            if (resStorage.getProvisionCapacity() != null) {
                storage.setProvisionedSize(resStorage.getProvisionCapacity());
                storage.setUncommittedSize(resStorage.getProvisionCapacity() - storage.getUsedSize());
            }
            storage.setLocal(StorageCategory.LOCAL.equals(resStorage.getStorageCategory()));
            storage.setParentTopologySid(resStorage.getParentTopologySid());
            storage.setCloudEnvName(resStorage.getParentTopologyName());
            storage.setCloudEnvType(resStorage.getParentTopologyType());

            // 存储类型
            if (volumeTypeMapGroupByCloudEnvId.containsKey(storage.getParentTopologySid())) {
                storage.setVolumeTypeName(volumeTypeMapGroupByCloudEnvId.get(storage.getParentTopologySid())
                                                                        .getOrDefault(storage.getVolumeTypeId(), "--"));
            } else {
                storage.setVolumeTypeName("--");
            }

            if (resVdMap.containsKey(storage.getParentTopologySid())) {
                storage.setAllocSize(resVdMap.get(storage.getParentTopologySid()).getOrDefault(storage.getId(), 0L));
            } else {
                storage.setAllocSize(0L);
            }
            storage.setAllocRate(formatData(storage.getAllocSize() / storage.getTotalSize() * 100));
            list.add(storage);
        }
        return list;
    }

    @Override
    public List<ResStorage> selectStorageByHostSid(Criteria criteria) {
        List<ResStorage> resStorages = resStorageMapper.selectStorageByHostSid(criteria);
        if (CollectionUtils.isEmpty(resStorages)) {
            return resStorages;
        }

        // 查询存储类型
        List<Long> resVolumeTypeIds = resStorages.stream()
                                                 .filter(Objects::nonNull)
                                                 .map(ResStorage::getResVolumeTypeId)
                                                 .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(resVolumeTypeIds)) {
            return resStorages;
        }

        List<ResVolumeType> resVolumeTypes = resVolumeTypeMapper.selectByExample(
                new Criteria("idIn", resVolumeTypeIds));

        Map<Long, String> resVolumeTypeMap = resVolumeTypes.stream()
                                                           .collect(Collectors.toMap(ResVolumeType::getId,
                                                                                     ResVolumeType::getTypeName));
        resStorages.forEach(item -> item.setVolumeTypeName(resVolumeTypeMap.get(item.getResVolumeTypeId())));

        return resStorages;
    }

    @Override
    public List<ResStorage> selectResStorageByParams(Criteria criteria) {
        return resStorageMapper.selectByParams(criteria);
    }

    private Double formatData(Double data) {
        BigDecimal scale = new BigDecimal(data).setScale(2, BigDecimal.ROUND_HALF_UP);

        return scale.doubleValue();
    }
}
