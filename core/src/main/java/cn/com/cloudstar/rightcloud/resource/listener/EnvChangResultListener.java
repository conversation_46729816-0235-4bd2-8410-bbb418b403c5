/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.listener;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.adapter.pojo.rclink.result.EnvChangeResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rclink.result.RcLinkGatewayEnvAddResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rclink.result.RcLinkGatewayEnvRemoveResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rclink.result.RcLinkGatewayEnvUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rclink.result.RcLinkGatewayRemoveResult;
import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnv;
import cn.com.cloudstar.rightcloud.common.constants.res.status.CloudEnvStatus;
import cn.com.cloudstar.rightcloud.common.enums.resource.ResourceOperateEnum;
import cn.com.cloudstar.rightcloud.common.enums.resource.ResourceTypeEnum;
import cn.com.cloudstar.rightcloud.common.schedule.helper.ScheduleHelper;
import cn.com.cloudstar.rightcloud.common.util.json.JsonUtil;
import cn.com.cloudstar.rightcloud.common.websocket.annonation.Message;
import cn.com.cloudstar.rightcloud.common.websocket.annonation.MessageParam;
import cn.com.cloudstar.rightcloud.common.websocket.support.OperateEnum;
import cn.com.cloudstar.rightcloud.common.websocket.support.ServerMsgType;
import cn.com.cloudstar.rightcloud.core.annotation.log.LogMethod;
import cn.com.cloudstar.rightcloud.core.annotation.log.LogParam;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicInfoUtil;
import cn.com.cloudstar.rightcloud.remote.api.pojo.system.maintenance.gateway.CloudPlatformComponent;
import cn.com.cloudstar.rightcloud.remote.api.pojo.system.maintenance.gateway.RcLinkGateway;
import cn.com.cloudstar.rightcloud.resource.service.env.CloudEnvService;
import cn.com.cloudstar.rightcloud.resource.service.redundance.core.RcLinkGatewayService;

/**
 * 二级云管云环境变更监听
 */
@Slf4j
@Component
public class EnvChangResultListener {

    @Autowired
    private CloudEnvService cloudEnvService;

    @Autowired
    private RcLinkGatewayService rcLinkGatewayService;

    /**
     * 云环境添加返回结果
     **/
    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#envAddResult.cloudEnvId", resourceType = ResourceTypeEnum.CLOUD_ENV, opUser = "#envAddResult.opUser", operate = ResourceOperateEnum.CREATE, success = "#envAddResult.success", orgSid = "#envAddResult.orgSid")
    @Message(refKey = "#envAddResult.rcLinkId", envId = "#envAddResult.cloudEnvId", msgType = ServerMsgType.RCLINK_GATEWAY_ENV, opUser = "#envAddResult.opUser", operate = OperateEnum.ADD_ENV, success = "#envAddResult.success", errorMsg = "#envAddResult.errMsg")
    public void handleMessage(
            @LogParam("envAddResult") @MessageParam("envAddResult") RcLinkGatewayEnvAddResult envAddResult) {
        if (log.isInfoEnabled()) {
            log.info("云环境添加返回结果: {}", JsonUtil.toJson(envAddResult));
        }

        this.updateCloudEnvStatus(envAddResult);
    }

    /**
     * 云环境变更返回结果
     **/
    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#envUpdateResult.cloudEnvId", resourceType = ResourceTypeEnum.CLOUD_ENV, opUser = "#envUpdateResult.opUser", operate = ResourceOperateEnum.MODIFY, success = "#envUpdateResult.success", orgSid = "#envUpdateResult.orgSid")
    @Message(refKey = "#envUpdateResult.rcLinkId", envId = "#envUpdateResult.cloudEnvId", msgType = ServerMsgType.RCLINK_GATEWAY_ENV, opUser = "#envUpdateResult.opUser", operate = OperateEnum.UPDATE_ENV, success = "#envUpdateResult.success", errorMsg = "#envUpdateResult.errMsg")
    public void handleMessage(
            @LogParam("envUpdateResult") @MessageParam("envUpdateResult") RcLinkGatewayEnvUpdateResult envUpdateResult) {
        if (log.isInfoEnabled()) {
            log.info("云环境变更返回结果: {}", JsonUtil.toJson(envUpdateResult));
        }

        this.updateCloudEnvStatus(envUpdateResult);
    }

    /**
     * 云环境移除返回结果
     **/
    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#envRemoveResult.cloudEnvId", resourceType = ResourceTypeEnum.CLOUD_ENV, opUser = "#envRemoveResult.opUser", operate = ResourceOperateEnum.REMOVE_CLOUDENV, success = "#envRemoveResult.success", orgSid = "#envRemoveResult.orgSid")
    @Message(refKey = "#envRemoveResult.rcLinkId", envId = "#envRemoveResult.cloudEnvId", msgType = ServerMsgType.RCLINK_GATEWAY_ENV, opUser = "#envRemoveResult.opUser", operate = OperateEnum.REMOVE_ENV, success = "#envRemoveResult.success", errorMsg = "#envRemoveResult.errMsg")
    public void handleMessage(
            @LogParam("envRemoveResult") @MessageParam("envRemoveResult") RcLinkGatewayEnvRemoveResult envRemoveResult) {
        if (log.isInfoEnabled()) {
            log.info("云环境移除返回结果: {}", JsonUtil.toJson(envRemoveResult));
        }
        if (envRemoveResult.isSuccess()) {
            CloudEnv cloudEnv = this.cloudEnvService.selectByPrimaryKey(envRemoveResult.getCloudEnvId());
            if (null == cloudEnv) {
                log.warn("云环境id:{} 不存在, 跳过", envRemoveResult.getCloudEnvId());
                return;
            }
            log.info("二级云管 云环境 {}@{} 删除成功, 开始删除本地云环境...", cloudEnv.getId(), cloudEnv.getCloudEnvName());
            if (CloudEnvStatus.DELETING.equalsIgnoreCase(cloudEnv.getStatus())) {
                log.info("云环境 {}@{} 正在被删除...", cloudEnv.getId(), cloudEnv.getCloudEnvName());
                return;
            }
            try {
                this.cloudEnvService.removeCloudEnv(cloudEnv.getId(), Long.valueOf(envRemoveResult.getOrgSid()),
                                                    envRemoveResult.getOpUser());
            } catch (Exception e) {
                log.info("二级云管 云环境 {}@{} 删除失败! 错误原因: {}", cloudEnv.getId(), cloudEnv.getCloudEnvName(), e.getMessage());
            }
        } else {
            log.error("云环境移除失败: {}", envRemoveResult.getErrMsg());
        }
    }

    /**
     * 二级云管移除返回结果
     **/
    @LogMethod(resourceKey = "#gatewayRemoveResult.rcLinkId", resourceType = ResourceTypeEnum.RCLINK_GATEWAY, opUser = "#gatewayRemoveResult.opUser", operate = ResourceOperateEnum.REMOVE_RCLINK_GATEWAY, success = "#gatewayRemoveResult.success", orgSid = "#gatewayRemoveResult.orgSid")
    @Message(refKey = "#gatewayRemoveResult.rcLinkId", envId = "#gatewayRemoveResult.cloudEnvId", msgType = ServerMsgType.RCLINK_GATEWAY, opUser = "#gatewayRemoveResult.opUser", operate = OperateEnum.REMOVE_RCLINK_GATEWAY, success = "#gatewayRemoveResult.success", errorMsg = "#gatewayRemoveResult.errMsg")
    public void handleMessage(
            @LogParam("gatewayRemoveResult") @MessageParam("gatewayRemoveResult") RcLinkGatewayRemoveResult gatewayRemoveResult) {
        if (log.isInfoEnabled()) {
            log.info("二级云管移除返回结果: {}", JSONUtil.toJsonStr(gatewayRemoveResult));
        }
        if (gatewayRemoveResult.getRcLinkId() == null) {
            return;
        }
        Criteria criteria = new Criteria();
        criteria.put("componentId", gatewayRemoveResult.getRcLinkId());
        List<RcLinkGateway> list = this.rcLinkGatewayService.queryRcLinkGateway(criteria);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        if (gatewayRemoveResult.isSuccess()) {
            log.info("二级云管: {} 底层删除成功, 开始删除本地数据...", gatewayRemoveResult.getRcLinkId());
        } else {
            log.error("二级云管: {} 底层删除失败! 错误原因: {}", gatewayRemoveResult.getRcLinkId(), gatewayRemoveResult.getErrMsg());
        }
    }

    /**
     * 更新云环境状态
     **/
    private void updateCloudEnvStatus(EnvChangeResult result) {
        CloudEnv cloudEnv = this.cloudEnvService.selectByPrimaryKey(result.getCloudEnvId());
        if (null == cloudEnv) {
            log.warn("云环境:{} 不存在, 跳过", result.getCloudEnvId());
            return;
        }
        CloudEnv record = new CloudEnv();
        record.setStatus(result.isSuccess() ? CloudEnvStatus.NORMAL : CloudEnvStatus.ERROR);
        record.setId(result.getCloudEnvId());
        if (!result.isSuccess()) {
            record.setSyncInfo(result.getErrMsg());
            log.error("云环境 {}@{} 操作失败! 错误原因: {}", cloudEnv.getId(), cloudEnv.getCloudEnvName(), result.getErrMsg());
            this.cloudEnvService.updateByPrimaryKeySelective(record);
        } else {
            log.info("云环境 {}@{} 状态修改为:{}", cloudEnv.getId(), cloudEnv.getCloudEnvName(), CloudEnvStatus.NORMAL);
            this.cloudEnvService.updateByPrimaryKeySelective(record);
        }
        int time = cloudEnv.getCycleTime();
        if (result.getCycleTimeChange() == null) {
            if (cloudEnv.getLastSyncTime() == null) {
                log.info("云环境 [{}]@[{}] 没有同步记录, 尝试添加任务，频率: [{}]分钟", cloudEnv.getId(), cloudEnv.getCloudEnvName(), time);
            }
            return;
        }
        log.info("云环境 [{}]@[{}] 修改采集频率: [{}]分钟", cloudEnv.getId(), cloudEnv.getCloudEnvName(), time);
    }

    /**
     * 添加定时任务
     **/
    private void setScheduleJob(CloudEnv cloudEnv) {
        int time = cloudEnv.getCycleTime();
        try {
            /*this.cloudEnvService.setSyncTask(cloudEnv.getId(), cloudEnv.getCloudEnvName(), cloudEnv.getOrgSid(), time,
                                             null);*/
        } catch (Exception e1) {
            log.info("云环境 {}@{} 添加定时任务失败! 错误信息: {}", cloudEnv.getId(), cloudEnv.getCloudEnvName(), e1.getMessage());
            try {
                log.info("云环境 {}@{} 尝试更新云环境采集定时任务", cloudEnv.getId(), cloudEnv.getCloudEnvName());
                ScheduleHelper.updateSyncEnvTask(cloudEnv.getId(), time, BasicInfoUtil.getCurrentUserSid());
                log.info("云环境 {}@{} 尝试更新云环境采集定时任务 成功");
            } catch (Exception e2) {
                log.error("云环境 {}@{} 重试 更新采集定时任务失败! 错误信息: {}", cloudEnv.getId(), cloudEnv.getCloudEnvName(),
                          e2.getMessage());
            }
        }
    }

    /**
     * 更新定时任务
     **/
    private void updateScheduleJob(CloudEnv cloudEnv) {
        int time = cloudEnv.getCycleTime();
        try {
            ScheduleHelper.updateSyncEnvTask(cloudEnv.getId(), time, BasicInfoUtil.getCurrentUserSid());
        } catch (Exception e1) {
            try {
                log.info("云环境 {}@{} 尝试 重新设置云环境采集定时任务", cloudEnv.getId(), cloudEnv.getCloudEnvName());
                /*this.cloudEnvService.setSyncTask(cloudEnv.getId(), cloudEnv.getCloudEnvName(), cloudEnv.getOrgSid(),
                                                 time, null);*/
                log.info("云环境 {}@{} 重新设置云环境采集定时任务 成功", cloudEnv.getId(), cloudEnv.getCloudEnvName());
            } catch (Exception e2) {
                log.error("云环境 {}@{} 重试 设置采集定时任务失败! 错误信息: {}", cloudEnv.getId(), cloudEnv.getCloudEnvName(),
                          e2.getMessage());
            }
        }
    }
}
