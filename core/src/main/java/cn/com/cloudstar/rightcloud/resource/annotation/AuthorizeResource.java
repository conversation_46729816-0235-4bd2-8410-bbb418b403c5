/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 基于接口的权限拦截
 *
 * <AUTHOR>
 * @since 2022/4/11 17:16:27
 */
@Target(ElementType.METHOD)
@Inherited
@Retention(RetentionPolicy.RUNTIME)
public @interface AuthorizeResource {

    /**
     * 产品code:资源类型code:操作 </br>
     * 如ecs:servers:ListVms
     */
    String action() default "";
}
