<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2018 Cloud-Star, Inc. All Rights Reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.cloudstar.rightcloud.resource.dao.server.ResVmMapper">
    <resultMap id="BaseResultMap" type="cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResVm">
        <id column="ID" jdbcType="BIGINT" property="id"/>
        <result column="CLOUD_ENV_ID" jdbcType="BIGINT" property="cloudEnvId"/>
        <result column="CLOUD_ENV_ACCOUNT_ID" jdbcType="BIGINT" property="cloudEnvAccountId"/>
        <result column="ALLOCATE_RES_HOST_SID" jdbcType="VARCHAR" property="allocateResHostSid"/>
        <result column="ALLOCATE_RES_VC_SID" jdbcType="VARCHAR" property="allocateResVcSid"/>
        <result column="CLOUD_ENV_NAME" jdbcType="VARCHAR" property="cloudEnvName"/>
        <result column="CLOUD_ENV_TYPE" jdbcType="VARCHAR" property="cloudEnvType"/>
        <result column="CLOUD_ENV_TYPE_NAME" jdbcType="VARCHAR" property="cloudEnvTypeName"/>
        <result column="ENV_ACCOUNT_NAME" jdbcType="VARCHAR" property="envAccountName"/>
        <result column="INSTANCE_ID" jdbcType="VARCHAR" property="instanceId"/>
        <result column="INSTANCE_NAME" jdbcType="VARCHAR" property="instanceName"/>
        <result column="INSTANCE_ALL_NAME" jdbcType="VARCHAR" property="instanceAllName"/>
        <result column="MANAGEMENT_ACCOUNT" jdbcType="VARCHAR" property="managementAccount"/>
        <result column="MANAGEMEN_PASSWORD" jdbcType="VARCHAR" property="managemenPassword"/>
        <result column="IMAGE_ID" jdbcType="VARCHAR" property="imageId"/>
        <result column="IMAGE_TYPE" jdbcType="VARCHAR" property="imageType"/>
        <result column="IMAGE_NAME" jdbcType="VARCHAR" property="imageName"/>
        <result column="CLOUD_IMAGE_NAME" jdbcType="VARCHAR" property="cloudImageName"/>
        <result column="PLATFORM" jdbcType="VARCHAR" property="platform"/>
        <result column="CPU" jdbcType="INTEGER" property="cpu"/>
        <result column="MEMORY" jdbcType="INTEGER" property="memory"/>
        <result column="DESCRIPTION" jdbcType="VARCHAR" property="description"/>
        <result column="INSTANCE_TYPE" jdbcType="VARCHAR" property="instanceType"/>
        <result column="OWNER_ID" jdbcType="VARCHAR" property="ownerId"/>
        <result column="OWNER_ACCOUNT" jdbcType="VARCHAR" property="ownerAccount"/>
        <result column="INSTANCE_CHARGE_TYPE" jdbcType="VARCHAR" property="instanceChargeType"/>
        <result column="INSTANCE_CHARGE_TYPE_NAME" jdbcType="VARCHAR"
            property="instanceChargeTypeName"/>
        <result column="INTERNET_CHARGE_TYPE" jdbcType="VARCHAR" property="internetChargeType"/>
        <result column="INTERNET_MAX_BANDWIDTH_OUT" jdbcType="INTEGER"
            property="internetMaxBandwidthOut"/>
        <result column="INTERNET_MAX_BANDWIDTH_IN" jdbcType="INTEGER"
            property="internetMaxBandwidthIn"/>
        <result column="INNER_IP" jdbcType="VARCHAR" property="innerIp"/>
        <result column="PUBLIC_IP" jdbcType="VARCHAR" property="publicIp"/>
        <result column="SSH_PORT" jdbcType="VARCHAR" property="sshPort"/>
        <result column="INSTANCE_NETWORK_TYPE" jdbcType="VARCHAR" property="instanceNetworkType"/>
        <result column="ZONE" jdbcType="VARCHAR" property="zone"/>
        <result column="REGION" jdbcType="VARCHAR" property="region"/>
        <result column="MONITOR_STATUS_NAME" jdbcType="VARCHAR" property="monitorStatusName"/>
        <result column="STATUS" jdbcType="VARCHAR" property="status"/>
        <result column="STATUS_INFO" jdbcType="VARCHAR" property="statusInfo"/>
        <result column="TASK_STATUS" jdbcType="VARCHAR" property="taskStatus"/>
        <result column="STATUS_NAME" jdbcType="VARCHAR" property="statusName"/>
        <result column="MANAGE_STATUS_NAME" jdbcType="VARCHAR" property="manageStatusName"/>
        <result column="CLOUD_ENV_CATEGORY" property="cloudEnvCategory" jdbcType="VARCHAR"/>
        <result column="CLOUD_ENV_CATEGORY_NAME" property="cloudEnvCategoryName"
            jdbcType="VARCHAR"/>
        <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy"/>
        <result column="CREATED_DT" jdbcType="TIMESTAMP" property="createdDt"/>
        <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy"/>
        <result column="UPDATED_DT" jdbcType="TIMESTAMP" property="updatedDt"/>
        <result column="VERSION" jdbcType="BIGINT" property="version"/>
        <result column="MANAGE_STATUS" jdbcType="VARCHAR" property="manageStatus"/>
        <result column="ORIGIN_PARAM" jdbcType="LONGVARCHAR" property="originParam"/>
        <result column="OS_NAME" jdbcType="VARCHAR" property="osName"/>
        <result column="REMOTE_LOGIN_TYPE" jdbcType="VARCHAR" property="remoteLoginType"/>
        <result column="KEYPAIR_ID" jdbcType="INTEGER" property="keypairId"/>
        <result column="KEYPAIR_NAME" jdbcType="VARCHAR" property="keypairName"/>
        <result column="FLOATING_IP_POOL_NAME" jdbcType="VARCHAR" property="floatingIpPoolName"/>
        <result column="FLOATING_IP_POOL_ID" jdbcType="VARCHAR" property="floatingIpPoolId"/>
        <result column="DEPLOYMENT_ID" jdbcType="BIGINT" property="cloudDeploymentId"/>
        <result column="CLOUD_DEPLOYMENT_NAME" jdbcType="BIGINT" property="cloudDeploymentName"/>
        <result column="SERVER_TEMPLATE_ID" jdbcType="BIGINT" property="serverTemplateId"/>
        <result column="SERVER_TEMPLATE_NAME" jdbcType="VARCHAR" property="serverTemplateName"/>
        <result column="SERVER_TYPE" jdbcType="VARCHAR" property="serverType"/>
        <result column="use_memory_size" jdbcType="DOUBLE" property="useMemorySize"/>
        <result column="use_store_size" jdbcType="INTEGER" property="useStoreSize"/>
        <result column="provision_storage" jdbcType="INTEGER" property="provisionStorage"/>
        <result column="cpu_usage" jdbcType="INTEGER" property="cpuUsage"/>
        <result column="use_cpu_ghz" jdbcType="DOUBLE" property="useCpuGhz"/>
        <result column="disk_usage" jdbcType="DOUBLE" property="diskUsage"/>
        <result column="org_sid" jdbcType="BIGINT" property="orgSid"/>
        <result column="org_name" jdbcType="VARCHAR" property="orgName"/>
        <result column="org_type" jdbcType="VARCHAR" property="orgType"/>
        <result column="lock_user" jdbcType="BIGINT" property="lockUser"/>
        <result column="elastic_group_id" jdbcType="BIGINT" property="elasticGroupId"/>
        <result column="elastic_history_id" jdbcType="BIGINT" property="elasticHistoryId"/>
        <result column="lock_status" jdbcType="VARCHAR" property="lockStatus"/>
        <result column="lock_user_email" jdbcType="VARCHAR" property="lockUserEmail"/>
        <result column="elastic_tag" jdbcType="VARCHAR" property="elasticTag"/>
        <result column="vote_time" jdbcType="TIMESTAMP" property="voteTime"/>
        <result column="last_vote_result" jdbcType="VARCHAR" property="lastVoteResult"/>
        <result column="last_vote_time" jdbcType="TIMESTAMP" property="lastVoteTime"/>
        <result column="elastic_mode" jdbcType="VARCHAR" property="elasticMode"/>
        <result column="vote_reason" jdbcType="VARCHAR" property="voteReason"/>
        <result column="creater" jdbcType="VARCHAR" property="creater"/>
        <result column="interval_time" jdbcType="VARCHAR" property="intervalTime"/>
        <result column="CONCAT_PROJECT_NAME" jdbcType="VARCHAR" property="contactProjectName"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="os_category" jdbcType="VARCHAR" property="osCategory"/>
        <result column="res_pool_id" jdbcType="BIGINT" property="resPoolId"/>
        <result column="zone_name" jdbcType="VARCHAR" property="zoneName"/>
        <result column="cluster_id" jdbcType="VARCHAR" property="clusterId"/>
        <result column="stopped_mode" jdbcType="VARCHAR" property="stoppedMode"/>
        <result column="env_stopped_mode" jdbcType="VARCHAR" property="envStoppedMode"/>
        <result column="clone_source_id" jdbcType="VARCHAR" property="cloneSourceId"/>
        <result column="is_recycle" jdbcType="TINYINT" property="recycle"/>
        <result column="recycle_date" jdbcType="TIMESTAMP" property="recycleDate"/>
        <result column="URN" property="urn" jdbcType="VARCHAR"/>
        <result column="URI" property="uri" jdbcType="VARCHAR"/>
        <result column="allocate_res_host_uuid" property="allocateResHostUuid" jdbcType="VARCHAR"/>
        <result column="allocate_res_host_name" property="allocateResHostName" jdbcType="VARCHAR"/>
        <result column="tag_names" property="tagNames" jdbcType="VARCHAR"/>
        <result column="tag_values" property="tagValues" jdbcType="VARCHAR"/>
        <result column="rgb_codes" property="rgbCodes" jdbcType="VARCHAR"/>
        <result column="has_vm_tools" property="hasVmTools" jdbcType="TINYINT"/>
        <result column="instance_type_family_name" property="instanceTypeFamilyName" jdbcType="VARCHAR"/>
        <result column="host_name" property="hostName" jdbcType="VARCHAR"/>
        <result column="extra_type" property="extraType" jdbcType="VARCHAR"/>
        <result column="platform_component_id" property="platformComponentId" jdbcType="VARCHAR"/>
        <result column="owner_org_name" property="ownerOrgName" jdbcType="VARCHAR"/>
        <result column="count_status" jdbcType="INTEGER" property="countStatus"/>
        <result column="res_host_name" property="resHostName" jdbcType="VARCHAR"/>
        <result column="deploy_id" property="deployId" jdbcType="BIGINT"/>
        <result column="created_org_sid" property="createdOrgSid" jdbcType="BIGINT"/>
        <result column="service_order_id" property="serviceOrderId" jdbcType="VARCHAR"/>
        <result column="server_status" property="serverStatus" jdbcType="VARCHAR"/>
        <result column="delete_resource" property="deleteResource" jdbcType="VARCHAR"/>
        <result column="service_order_sn" property="serviceOrderSn" jdbcType="VARCHAR"/>
        <result column="physical_host_pool_id" property="physicalHostPoolId" jdbcType="VARCHAR"/>
        <result column="original_status" property="originalStatus" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="Base_Example_Where_Clause">
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.instanceId != null">
                and A.INSTANCE_ID = #{condition.instanceId}
            </if>
            <if test="condition.elasticGroupId != null">
                and A.elastic_group_id = #{condition.elasticGroupId}
            </if>
            <if test="condition.allocateResVcSid != null">
                and A.ALLOCATE_RES_VC_SID = #{condition.allocateResVcSid}
            </if>
            <if test="condition.securityGroupId != null">
                and exists (
                select 1
                from res_vm_ext che
                where che.instance_id = A.id
                and che.resource_id = #{condition.securityGroupId}
                and type = 'sg'
                )
            </if>
            <if test="condition.securityGroupIdNotEqual != null">
                and not exists (
                select 1
                from res_vm_ext che
                where che.instance_id = A.id
                and che.resource_id = #{condition.securityGroupIdNotEqual}
                and type = 'sg'
                )
            </if>
            <if test="condition.ipAddr != null">
                and (A.inner_ip LIKE concat('%',#{condition.ipAddr},'%') or A.public_ip LIKE
                concat('%',#{condition.ipAddr},'%'))
            </if>
            <if test="condition.elasticHistoryId != null">
                and A.elastic_history_id = #{condition.elasticHistoryId}
            </if>
            <if test="condition.allocateResHostSid != null">
                and A.ALLOCATE_RES_HOST_SID = #{condition.allocateResHostSid}
            </if>
            <if test="condition.allocateResHostSidIn != null">
                and A.ALLOCATE_RES_HOST_SID IN
                <foreach item="item" index="index" collection="condition.allocateResHostSidIn"
                    open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.instanceIds != null">
                and A.INSTANCE_ID IN
                <foreach item="item" index="index" collection="condition.instanceIds"
                    open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.publicIp != null and condition.publicIp != 'empty'">
                and A.PUBLIC_IP = #{condition.publicIp}
            </if>
            <if test="condition.publicIp != null and condition.publicIp == 'empty'">
                and (A.PUBLIC_IP is null or A.PUBLIC_IP = '')
            </if>
            <if test="condition.instanceName != null">
                and A.INSTANCE_NAME = #{condition.instanceName}
            </if>
            <if test="condition.instanceNameLike != null">
                and A.INSTANCE_NAME like concat('%', #{condition.instanceNameLike},'%')
            </if>
            <if test="condition.cloneLikeName != null">
                and A.INSTANCE_NAME like CONCAT(#{condition.cloneLikeName},'%')
            </if>
            <if test="condition.serverNameLike != null">
                and A.INSTANCE_NAME like concat('%', #{condition.serverNameLike},'%')
            </if>
            <if test="condition.ownerId != null">
                and A.OWNER_ID = #{condition.ownerId}
            </if>

            <if test="condition.hostIds != null and condition.hostIds.size() > 0">
                and A.id in
                <foreach item="item" index="index" collection="condition.hostIds"
                    open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.serverTemplateId != null">
                and A.SERVER_TEMPLATE_ID = #{condition.serverTemplateId}
            </if>
            <if test="condition.cloudEnvId != null">
                and A.CLOUD_ENV_ID = #{condition.cloudEnvId}
            </if>
            <if test="condition.cloudEnvIdIn != null and condition.cloudEnvIdIn.size() > 0">
                and A.CLOUD_ENV_ID in
                <foreach collection="condition.cloudEnvIdIn" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="condition.cloudEnvId == null and condition.isOther == 'yes'">
                and A.CLOUD_ENV_ID is null
            </if>
            <if test="condition.otherEnv != null">
                and A.CLOUD_ENV_ID is null
            </if>
            <if test="condition.instanceChargeType != null">
                and A.INSTANCE_CHARGE_TYPE = #{condition.instanceChargeType}
            </if>
            <if test="condition.status != null">
                and A.status = #{condition.status}
            </if>
            <if test="condition.statuss != null">
                and A.status in
                <foreach item="item" index="index" collection="condition.statuss"
                    open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.statusNotIn != null">
                and A.status not in
                <foreach item="item" index="index" collection="condition.statusNotIn"
                    open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.statusIn != null">
                and A.status in
                <foreach item="item" index="index" collection="condition.statusIn"
                    open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.serverTypeIn != null">
                and A.server_type in
                <foreach item="item" index="index" collection="condition.serverTypeIn"
                    open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.connectStatus != null">
                and A.manage_status = #{condition.connectStatus}
            </if>
            <if test="condition.keypairId != null">
                and A.KEYPAIR_ID = #{condition.keypairId}
            </if>
            <if test="condition.cloudDeploymentId != null">
                and exists (
                select 1
                from cloud_deployment_vm SDRH2
                where SDRH2.res_vm_id = A.id
                and SDRH2.DEPLOYMENT_ID = #{condition.cloudDeploymentId}
                )
            </if>
            <if test="condition.serverType != null">
                and A.SERVER_TYPE = #{condition.serverType}
            </if>
            <if test="condition.notInDeployment != null">
                and not not exists (
                select 1
                from cloud_deployment_vm SDRH2
                where SDRH2.DEPLOYMENT_ID != #{condition.notInDeployment}
                or SDRH2.DEPLOYMENT_ID is null
                )
            </if>
            <if test="condition.notInAnyDeployment != null">
                and not exists (
                select 1
                from cloud_deployment_vm SDRH2
                where SDRH2.res_vm_id = A.id
                )
            </if>
            <if test="condition.inDeployment != null">
                and exists (
                select 1 from cloud_deployment_vm SDRH2
                where SDRH2.res_vm_id = A.id
                and SDRH2.DEPLOYMENT_ID in
                <foreach collection="condition.inDeployment" item="listItem" open="(" close=")"
                    separator=",">
                    #{listItem}
                </foreach>
                )
            </if>

            <if test="condition.cloudEnvIds != null">
                and
                (
                A.CLOUD_ENV_ID in
                <foreach item="item" index="index" collection="condition.cloudEnvIds"
                    open="(" separator="," close=")">
                    #{item}
                </foreach>
                <if test="condition.notInAnyEnv != null">
                    or A.CLOUD_ENV_ID is null
                </if>
                )
            </if>

            <if test="condition.lockUser != null">
                AND A.lock_user = #{condition.lockUser}
            </if>
            <if test="condition.lockStatus != null">
                AND A.lock_status = #{condition.lockStatus}
            </if>
            <if test="condition.besidesLocked != null">
                AND (A.lock_status != #{condition.besidesLocked} OR A.lock_status is NULL )
            </if>
            <if test="condition.deblocking != null">
                AND (A.lock_status = #{condition.deblocking} or A.lock_status is NULL)
            </if>
            <if test="condition.elasticGroupId != null">
                AND A.elastic_group_id = #{condition.elasticGroupId}
            </if>
            <if test="condition.serverTypeNotEquals != null">
                and (A.server_type != #{condition.serverTypeNotEquals} or A.server_type is null)
            </if>
            <if test="condition.hostStatus != null">
                and (A.status = #{condition.hostStatus})
            </if>
            <if test="condition.hostVolumeStatus != null">
                and (A.status = #{condition.hostVolumeStatus} OR A.status = 'stopped')
            </if>
            <if test="condition.region != null">
                and A.region = #{condition.region}
            </if>
            <if test="condition.subnetId != null">
                and exists (
                select 1
                from res_vm_ext che
                inner join network net on che.resource_id = net.id and che.type = 'subnet'
                where net.id = #{condition.subnetId}
                and A.id = che.instance_id
                )
            </if>
            <if test="condition.subnetIdNotEqual != null">
                and not exists (
                select 1
                from res_vm_ext che
                inner join network net on che.resource_id = net.id and che.type = 'subnet'
                where net.id = #{condition.subnetIdNotEqual}
                and A.id = che.instance_id
                )
            </if>
            <if test="condition.startTime != null">
                and A.start_time = #{condition.startTime}
            </if>
            <if test="condition.endTime != null">
                and A.end_time = #{condition.endTime}
            </if>
            <if test="condition.statusNotEquals != null">
                and A.status != #{condition.statusNotEquals}
            </if>
            <if test="condition.instanceType != null">
                and A.instance_type = #{condition.instanceType}
            </if>
            <if test="condition.ipLike != null">
                and (A.PUBLIC_IP like concat('%', #{condition.ipLike},'%') or A.INNER_IP like
                concat('%',
                #{condition.ipLike},'%'))
            </if>
            <if test="condition.zone != null">
                and A.zone = #{condition.zone}
            </if>

            <if test="condition.serverTypeNotEqualsList != null">
                and A.server_type not in
                <foreach item="item" index="index" collection="condition.serverTypeNotEqualsList"
                    open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.statusNotEqualsList != null">
                and A.status not in
                <foreach item="item" index="index" collection="condition.statusNotEqualsList"
                    open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.remoteLoginType != null">
                and A.remote_login_type = #{condition.remoteLoginType}
            </if>
            <if test="condition.stoppedMode != null">
                and A.stopped_mode = #{condition.stoppedMode}
            </if>
            <if test="condition.ignoreRecycle == null">
                <choose>
                    <when test="condition.recycle != null">
                        and A.is_recycle = #{condition.recycle}
                    </when>
                    <otherwise>
                        and (A.is_recycle = 0 OR A.is_recycle IS NULL)
                    </otherwise>
                </choose>
            </if>
            <if test="condition.urn != null">
                and A.URN = #{condition.urn}
            </if>
            <if test="condition.uri != null">
                and A.URI = #{condition.uri}
            </if>
            <if test="condition.quickFilter != null">
                and (
                A.public_ip like concat('%', #{condition.quickFilter},'%')
                or A.inner_ip like concat('%', #{condition.quickFilter},'%')
                or A.instance_name like concat('%', #{condition.quickFilter},'%')
                or A.instance_id like concat('%', #{condition.quickFilter},'%')
                )
            </if>
            <if test="condition.orgSid != null">
                AND (EXISTS (SELECT 1 FROM sys_m_org WHERE (org_sid = #{condition.orgSid} OR tree_path LIKE
                CONCAT('/',#{condition.orgSid},'/%')) AND org_sid = A.org_sid))
            </if>
            <if test="condition.hostName != null">
                and A.host_name = #{condition.hostName}
            </if>
            <if test="condition.envIsNull != null">
                and A.cloud_env_id is null
            </if>
            <if test="condition.df != null">
                ${condition.df}
            </if>
            <if test="condition.createdBy != null">
                and A.created_by = #{condition.createdBy}
            </if>
            <if test="condition.startCreatedDt != null">
                and A.created_dt > #{condition.startCreatedDt}
            </if>
            <if test="condition.endCreatedDt != null">
                and A.created_dt
                <![CDATA[
                     <
                ]]>
                #{condition.endCreatedDt}
            </if>
            <if test="condition.startCreatedDt != null">
                and A.created_dt > #{condition.startCreatedDt}
            </if>
            <if test="condition.endCreatedDt != null">
                and A.created_dt
                <![CDATA[
                     <
                ]]>
                #{condition.endCreatedDt}
            </if>
            <if test="condition.imageId != null">
                and A.image_id = #{condition.imageId}
            </if>
            <if test="condition.imageType != null">
                and A.image_type = #{condition.imageType}
            </if>
            <if test="condition.cloudEnvTypes != null">
                and B.CLOUD_ENV_TYPE in
                <foreach item="item" index="index" collection="condition.cloudEnvTypes"
                    open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.physicalHostPoolId != null">
                and A.physical_host_pool_id = #{condition.physicalHostPoolId}
            </if>
            <if test="condition.resVmIds != null">
                and A.id in
                <foreach collection="condition.resVmIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.vmCategory != null">
                and A.vm_category = #{condition.vmCategory}
            </if>
            <if test="condition.allocStatus != null">
                and CPHP.alloc_status = #{condition.allocStatus}
            </if>
            <if test="condition.cloudEnvType != null">
                and (EXISTS (SELECT 1 FROM cloud_env WHERE (cloud_env_type LIKE
                CONCAT('%',#{condition.cloudEnvType},'%')) AND id = A.cloud_env_id))
            </if>
            <if test="condition.cloudEnvAccountId!= null">
                and (EXISTS (SELECT 1 FROM cloud_env WHERE cloud_env_account_id = #{condition.cloudEnvAccountId} AND id
                = A.cloud_env_id))
            </if>
            <if test="condition.projectIds != null and condition.projectIds.size  > 0">
                and A.org_sid in
                <foreach collection="condition.projectIds" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="condition.byClusterId != null">
                and id in (select resource_id from res_hpc_cluster_resource where cluster_id = #{condition.byClusterId,jdbcType=VARCHAR} and resource_type = 'ECS')
            </if>
        </trim>
    </sql>

    <sql id="Example_Where_Caluse_Params">
        <if test="condition.instanceId != null">
            and A.INSTANCE_ID = #{condition.instanceId}
        </if>
        <if test="condition.allocateResVcSid != null">
            and A.ALLOCATE_RES_VC_SID = #{condition.allocateResVcSid}
        </if>
        <if test="condition.instanceIdLike != null">
            and A.INSTANCE_ID like concat('%', #{condition.instanceIdLike}, '%')
        </if>
        <if test="condition.elasticGroupId != null">
            and A.elastic_group_id = #{condition.elasticGroupId}
        </if>
        <if test="condition.securityGroupId != null">
            and exists (
            select 1
            from res_vm_ext che
            where che.instance_id = A.id
            and che.resource_id = #{condition.securityGroupId}
            and type = 'sg'
            )
        </if>
        <if test="condition.securityGroupIdNotEqual != null">
            and not exists (
            select 1
            from res_vm_ext che
            where che.instance_id = A.id
            and che.resource_id = #{condition.securityGroupIdNotEqual}
            and type = 'sg'
            )
        </if>
        <if test="condition.serverGroupId != null">
            and A.instance_id is not null
            and exists (
            select 1
            from res_vm_ext che
            where che.instance_id = A.id
            and che.resource_id = #{condition.serverGroupId}
            and type = 'server_group'
            )
        </if>
        <if test="condition.ipAddr != null">
            and (A.inner_ip LIKE concat('%',#{condition.ipAddr},'%') or A.public_ip LIKE
            concat('%',#{condition.ipAddr},'%'))
        </if>
        <if test="condition.elasticHistoryId != null">
            and A.elastic_history_id = #{condition.elasticHistoryId}
        </if>
        <if test="condition.allocateResHostSid != null">
            and A.ALLOCATE_RES_HOST_SID = #{condition.allocateResHostSid}
        </if>
        <if test="condition.allocateResHostSidIn != null and condition.allocateResHostSidIn.size() > 0">
            and A.ALLOCATE_RES_HOST_SID IN
            <foreach item="item" index="index" collection="condition.allocateResHostSidIn"
                open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="condition.instanceNames != null and condition.instanceNames.size() > 0">
            and A.instance_name IN
            <foreach item="item" index="index" collection="condition.instanceNames"
                open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="condition.publicIp != null and condition.publicIp != 'empty'">
            and A.PUBLIC_IP = #{condition.publicIp}
        </if>
        <if test="condition.publicIp != null and condition.publicIp == 'empty'">
            and (A.PUBLIC_IP is null or A.PUBLIC_IP = '')
        </if>
        <if test="condition.instanceName != null">
            and A.INSTANCE_NAME = #{condition.instanceName}
        </if>
        <if test="condition.instanceNameLike != null">
            and A.INSTANCE_NAME like concat('%', #{condition.instanceNameLike},'%')
        </if>
        <if test="condition.cloneLikeName != null">
            and A.INSTANCE_NAME like CONCAT(#{condition.cloneLikeName},'%')
        </if>
        <if test="condition.serverNameLike != null">
            and A.INSTANCE_NAME like concat('%', #{condition.serverNameLike},'%')
        </if>
        <if test="condition.tagNamesLike != null">
            and I.tag_name like concat('%', #{condition.tagNamesLike},'%')
        </if>
        <if test="condition.tagNameList != null and condition.tagNameList.size() > 0">
            and I.tag_name in
            <foreach collection="condition.tagNameList" item="item" open="(" close=")"
                separator=",">
                #{item}
            </foreach>
        </if>

        <if test="condition.hostIds != null and condition.hostIds.size() > 0">
            and A.id in
            <foreach item="item" index="index" collection="condition.hostIds"
                open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="condition.networkIdIn != null and condition.networkIdIn.size() > 0">
            and exists (
            select 1
            from res_vm_ext che
            left join network net on che.resource_id = net.id and che.type = 'subnet'
            where net.net_vpc_id in
            <foreach collection="condition.networkIdIn" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and A.id = che.instance_id
            )
        </if>
        <if test="condition.notIds != null and condition.notIds.size() > 0">
            and A.id not in
            <foreach item="item" index="index" collection="condition.notIds"
                open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="condition.cloudEnvType != null">
            and B.CLOUD_ENV_TYPE = #{condition.cloudEnvType}
        </if>
        <if test="condition.cloudEnvTypes != null">
            and B.CLOUD_ENV_TYPE in
            <foreach item="item" index="index" collection="condition.cloudEnvTypes"
                open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="condition.serverTemplateId != null">
            and A.SERVER_TEMPLATE_ID = #{condition.serverTemplateId}
        </if>
        <if test="condition.clusterId != null">
            and E.cluster_id = #{condition.clusterId}
        </if>
        <if test="condition.cloudEnvId != null">
            and A.CLOUD_ENV_ID = #{condition.cloudEnvId}
        </if>
        <if test="condition.cloudEnvIdIn != null and condition.cloudEnvIdIn.size() > 0">
            and A.CLOUD_ENV_ID in
            <foreach collection="condition.cloudEnvIdIn" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="condition.cloudEnvId == null and condition.isOther == 'yes'">
            and A.CLOUD_ENV_ID is null
        </if>
        <if test="condition.otherEnv != null">
            and A.CLOUD_ENV_ID is null
        </if>
        <if test="condition.cloudEnvIdNotIn != null and condition.cloudEnvIdNotIn.size() > 0">
            and A.CLOUD_ENV_ID not in
            <foreach item="item" index="index" collection="condition.cloudEnvIdNotIn"
                open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="condition.instanceChargeType != null">
            and A.INSTANCE_CHARGE_TYPE = #{condition.instanceChargeType}
        </if>
        <if test="condition.status != null">
            and A.status = #{condition.status}
        </if>
        <if test="condition.statuss != null and condition.statuss.size() > 0">
            and A.status in
            <foreach item="item" index="index" collection="condition.statuss"
                open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="condition.statusNotIn != null and condition.statusNotIn.size() > 0">
            and A.status not in
            <foreach item="item" index="index" collection="condition.statusNotIn"
                open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="condition.statusIn != null and condition.statusIn.size() > 0">
            and A.status in
            <foreach item="item" index="index" collection="condition.statusIn"
                open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="condition.serverTypeIn != null and condition.serverTypeIn.size() > 0">
            and A.server_type in
            <foreach item="item" index="index" collection="condition.serverTypeIn"
                open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="condition.connectStatus != null">
            and A.manage_status = #{condition.connectStatus}
        </if>
        <if test="condition.keypairId != null">
            and A.KEYPAIR_ID = #{condition.keypairId}
        </if>
        <if test="condition.cloudDeploymentId != null">
            and exists (
            select 1 from cloud_deployment_vm cdh
            where cdh.res_vm_id = A.id
            and cdh.deployment_id = #{condition.cloudDeploymentId}
            )
        </if>
        <if test="condition.excludeCloudDeploymentId != null">
            and not exists (
            select 1 from
            cloud_deployment_vm cdh
            where cdh.deployment_id = #{condition.excludeCloudDeploymentId}
            and cdh.res_vm_id = A.id)
        </if>
        <if test="condition.serverType != null">
            and A.SERVER_TYPE = #{condition.serverType}
        </if>
        <if test="condition.notInDeployment != null">
            and (SDRH.DEPLOYMENT_ID != #{condition.notInDeployment}
            or SDRH.DEPLOYMENT_ID is null)
        </if>
        <if test="condition.notInAnyDeployment != null">
            and not exists (
            select 1 from
            cloud_deployment_vm cdh
            where cdh.res_vm_id = A.id)
        </if>
        <if test="condition.inDeployment != null and condition.inDeployment.size() > 0">
            and SDRH.DEPLOYMENT_ID in
            <foreach collection="condition.inDeployment" item="listItem" open="(" close=")"
                separator=",">
                #{listItem}
            </foreach>
        </if>

        <if test="condition.cloudEnvIds != null and condition.cloudEnvIds.size() > 0">
            and
            (
            A.CLOUD_ENV_ID in
            <foreach item="item" index="index" collection="condition.cloudEnvIds"
                open="(" separator="," close=")">
                #{item}
            </foreach>
            <if test="condition.notInAnyEnv != null">
                or A.CLOUD_ENV_ID is null
            </if>
            )
        </if>

        <if test="condition.lockUser != null">
            AND A.lock_user = #{condition.lockUser}
        </if>
        <if test="condition.lockStatus != null">
            AND A.lock_status = #{condition.lockStatus}
        </if>
        <if test="condition.besidesLocked != null">
            AND (A.lock_status != #{condition.besidesLocked} OR A.lock_status is NULL )
        </if>
        <if test="condition.deblocking != null">
            AND (A.lock_status = #{condition.deblocking} or A.lock_status is NULL)
        </if>
        <if test="condition.elasticGroupId != null">
            AND A.elastic_group_id = #{condition.elasticGroupId}
        </if>
        <if test="condition.serverTypeNotEquals != null">
            and (A.server_type != #{condition.serverTypeNotEquals} or A.server_type is null)
        </if>
        <if test="condition.resHostIds != null and condition.resHostIds.size() > 0">
            and RH.res_host_sid in
            <foreach item="item" index="index" collection="condition.resHostIds"
                open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="condition.hostStatus != null">
            and (A.status = #{condition.hostStatus})
        </if>
        <if test="condition.hostVolumeStatus != null">
            and (A.status = #{condition.hostVolumeStatus} OR A.status = 'stopped')
        </if>
        <if test="condition.region != null">
            and A.region = #{condition.region}
        </if>
        <if test="condition.startTime != null">
            and A.start_time = #{condition.startTime}
        </if>
        <if test="condition.endTime != null">
            and A.end_time = #{condition.endTime}
        </if>
        <if test="condition.statusNotEquals != null">
            and A.status != #{condition.statusNotEquals}
        </if>
        <if test="condition.instanceType != null">
            and A.instance_type = #{condition.instanceType}
        </if>
        <if test="condition.ipLike != null">
            and (A.PUBLIC_IP like concat('%', #{condition.ipLike},'%') or A.INNER_IP like
            concat('%',
            #{condition.ipLike},'%'))
        </if>
        <if test="condition.manageStatus != null">
            and A.MANAGE_STATUS = #{condition.manageStatus}
        </if>
        <if test="condition.zone != null">
            and (A.zone like concat('%', #{condition.zone},'%') or A.zone in (select ZA.uuid from (SELECT rz.name AS
            NAME,rz.uuid AS UUID FROM res_vm AS A LEFT JOIN res_zone AS rz ON (rz.uuid = A.zone AND A.cloud_env_id
            =rz.cloud_env_id) UNION
            SELECT rp.name AS UUID,rp.name AS NAME FROM res_pool AS rp LEFT JOIN res_vm A ON (rp.cloud_env_id =
            A.cloud_env_id AND rp.NAME = A.zone)) ZA where ZA.uuid=A.zone and ZA.name like concat('%',
            #{condition.zone},'%')))
        </if>
        <if test="condition.zoneEqual != null">
            and (A.zone = #{condition.zoneEqual} or A.zone in
            (select ZA.uuid from (SELECT rz.name AS NAME,rz.uuid AS UUID FROM res_vm AS A LEFT JOIN res_zone AS rz ON
            (rz.uuid = A.zone AND A.cloud_env_id =rz.cloud_env_id)
            UNION
            SELECT rp.name AS UUID,rp.name AS NAME FROM res_pool AS rp LEFT JOIN res_vm A ON (rp.cloud_env_id =
            A.cloud_env_id AND rp.NAME = A.zone)) ZA where ZA.uuid=A.zone and ZA.name = #{condition.zoneEqual}))
        </if>
        <if test="condition.zoneNotEqual != null">
            and (A.zone != #{condition.zoneNotEqual} or A.zone is null)
        </if>

        <if test="condition.serverTypeNotEqualsList != null and condition.serverTypeNotEqualsList.size() > 0">
            and A.server_type not in
            <foreach item="item" index="index" collection="condition.serverTypeNotEqualsList"
                open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="condition.statusNotEqualsList != null and condition.statusNotEqualsList.size() > 0">
            and A.status not in
            <foreach item="item" index="index" collection="condition.statusNotEqualsList"
                open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="condition.searchOther != null">
            and A.cloud_env_id is null
        </if>
        <if test="condition.stoppedMode != null">
            and A.stopped_mode = #{condition.stoppedMode}
        </if>
        <if test="condition.ignoreRecycle == null">
            <choose>
                <when test="condition.recycle != null">
                    and A.is_recycle = #{condition.recycle}
                </when>
                <otherwise>
                    and A.is_recycle = 0
                </otherwise>
            </choose>
        </if>
        <if test="condition.orgSid != null">
            AND (EXISTS (SELECT 1 FROM sys_m_org WHERE (org_sid = #{condition.orgSid} OR tree_path LIKE
            CONCAT('%/',#{condition.orgSid},'/%')) AND org_sid = A.org_sid))
        </if>
        <if test="condition.instanceType != null">
            and A.instance_type = #{condition.instanceType}
        </if>
        <if test="condition.innerIp != null">
            and A.inner_ip = #{condition.innerIp}
        </if>
        <if test="condition.networkName != null">
            and exists (
            select 1
            from res_vm_ext che
            inner join network net on che.resource_id = net.id and che.type = 'subnet'
            inner join res_vpc vpc on net.net_vpc_id = vpc.id
            where vpc.name = #{condition.networkName}
            and A.id = che.instance_id
            )
        </if>
        <if test="condition.networkId != null">
            and exists (
            select 1
            from res_vm_ext che
            inner join network net on che.resource_id = net.id and che.type = 'subnet'
            where net.net_vpc_id = #{condition.networkId}
            and A.id = che.instance_id
            )
        </if>
        <if test="condition.subnetName != null">
            and exists (
            select 1
            from res_vm_ext che
            inner join network net on che.resource_id = net.id and che.type = 'subnet'
            where net.network_name = #{condition.subnetName}
            and A.id = che.instance_id
            )
        </if>
        <if test="condition.subnetId != null">
            and exists (
            select 1
            from res_vm_ext che
            inner join network net on che.resource_id = net.id and che.type = 'subnet'
            where net.id = #{condition.subnetId}
            and A.id = che.instance_id
            )
        </if>
        <if test="condition.subnetIdNotEqual != null">
            and not exists (
            select 1
            from res_vm_ext che
            inner join network net on che.resource_id = net.id and che.type = 'subnet'
            where net.id = #{condition.subnetIdNotEqual}
            and A.id = che.instance_id
            )
        </if>
        <if test="condition.imageId != null">
            and A.image_id = #{condition.imageId}
        </if>
        <if test="condition.allocateResHostName != null">
            and RH.host_name = #{condition.allocateResHostName}
        </if>
        <if test="condition.forClusterNode != null">
            and not exists (select 1 from cloud_cluster_node b where b.host_id = a.ID)
        </if>
        <if test="condition.inventoryDefaultHosts != null">
            and (
            exists (select 1 from cloud_platform_component c where c.res_vm_id = a.id)
            or
            not exists ( select 1 from cloud_platform_component d
            inner join res_vm_ext che on a.id = che.instance_id
            inner join network subnet on che.type = 'subnet' and subnet.id = che.resource_id
            where FIND_IN_SET(subnet.net_vpc_id,d.network_ids))
            )
        </if>
        <if test="condition.ipExistCheck != null">
            AND (FIND_IN_SET(#{condition.ipExistCheck}, A.inner_ip) OR FIND_IN_SET(#{condition.ipExistCheck},
            A.public_ip))
        </if>
        <if test="condition.routerId != null">
            AND EXISTS (
            SELECT 1 FROM res_router router
            INNER JOIN network net ON router.vpc_id = net.net_vpc_id
            INNER JOIN res_vm_ext che ON net.id = che.resource_id AND che.type = 'subnet'
            WHERE router.id = #{condition.routerId}
            AND che.instance_id = A.id
            )
        </if>
        <if test="condition.vpcId != null">
            AND EXISTS (
            SELECT 1 FROM res_vm_ext che
            INNER JOIN network net ON che.resource_id = net.id and che.type = 'subnet'
            WHERE net.net_vpc_id = #{condition.vpcId}
            AND che.instance_id = A.id
            )
        </if>
        <if test="condition.hostName != null">
            and A.host_name = #{condition.hostName}
        </if>
        <if test="condition.notUseInPlatformComponent != null">
            <!--过滤 已经安装二级云管的主机-->
            AND A.id not in (
            SELECT res_vm_id FROM cloud_platform_component comp
            where comp.res_vm_id is not null and comp.cloud_env_id = #{condition.cloudEnvId}
            )
        </if>
        <if test="condition.extraType != null">
            <choose>
                <when test="condition.extraType == 'UnBareMetal'">
                    and (A.extra_type != 'BareMetal' or A.extra_type is null)
                </when>
                <otherwise>
                    and A.extra_type = #{condition.extraType}
                </otherwise>
            </choose>
        </if>

        <if test="condition.floatingIpBind != null">
            AND EXISTS (
            SELECT 1 from res_floating_ip i
            LEFT JOIN res_vpc vpc1 ON vpc1.`name` = i.pool
            LEFT JOIN res_router r ON r.vpc_id = vpc1.id
            LEFT JOIN res_router_interface RI ON (RI.router_id = R.id AND RI.interface_type = 'inner')
            LEFT JOIN network n on n.uuid = RI.network_uuid
            LEFT JOIN RES_VPC_PORT vp ON vp.subnet_id = N.ID
            WHERE vp.device = A.id AND i.cloud_env_id = A.cloud_env_id AND i.ip = #{condition.floatingIpBind}
            )
        </if>
        <if test="condition.networkNameLike != null">
            and exists (
            select 1
            from res_vm_ext che
            inner join network net on che.resource_id = net.id and che.type = 'subnet'
            inner join res_vpc vpc on net.net_vpc_id = vpc.id
            where vpc.name like concat('%', #{condition.networkNameLike}, '%')
            and A.id = che.instance_id
            )
        </if>
        <if test="condition.subnetNameLike != null">
            and exists (
            select 1
            from res_vm_ext che
            inner join network net on che.resource_id = net.id and che.type = 'subnet'
            where net.network_name like concat('%', #{condition.subnetNameLike}, '%')
            and A.id = che.instance_id
            )
        </if>
        <if test="condition.ownerAccountLike != null">
            and exists (select 1 from sys_m_user b where b.user_sid = A.owner_id and b.account like
            concat('%', #{condition.ownerAccountLike}, '%'))
        </if>

        <if test="condition.expireDay != null and condition.expireDay > 0">
            and A.end_time > now() and A.end_time <![CDATA[ <= ]]> date_add(now(),interval #{condition.expireDay} DAY)
        </if>

        <if test="condition.expireDay != null and condition.expireDay == 0">
            and A.end_time <![CDATA[ < ]]> now()
        </if>
        <!--过滤子网连接路由器的可以挂载弹性IP的主机-->
        <if test="condition.cloudEnvId != null and condition.canBindFloatingIp != null">
            AND EXISTS (
            SELECT 1 FROM res_vm_ext ext
            WHERE A.id = ext.instance_id AND ext.type = 'subnet' AND ext.resource_id IN (
            #查询云环境定义路由器中包含共有网络的所有子网#
            SELECT N.id FROM res_router A
            INNER JOIN res_router_interface B ON A.id = B.router_id
            INNER JOIN network N ON B.network_uuid = N.uuid
            WHERE B.cloud_env_id IN ( SELECT id FROM cloud_env env WHERE env.cloud_env_account_id =
            (SELECT cloud_env_account_id FROM cloud_env WHERE id = #{condition.cloudEnvId} ) )
            AND EXISTS ( SELECT 1 FROM res_vpc vpc WHERE vpc.id = A.vpc_id AND vpc.external = 'true' )
            )
            )
        </if>
        <!--过滤子网连接路由器的可以挂载弹性IP的主机-->

        <if test="condition.osCategory != null">
            and A.os_category like concat('%', #{condition.osCategory}, '%')
        </if>
        <if test="condition.platform != null">
            and A.platform like concat('%', #{condition.platform}, '%')
        </if>
        <if test="condition.osName != null">
            and A.os_name like concat('%', #{condition.osName}, '%')
        </if>
        <if test="condition.vmCategory != null">
            and A.vm_category = #{condition.vmCategory}
        </if>
        <if test="condition.allocStatus != null">
            AND (A.physical_host_pool_id is null or A.physical_host_pool_id = 0 or A.physical_host_pool_id in
            (select id from cloud_physical_host_pool where alloc_status = #{condition.allocStatus}))
        </if>
        <if test="condition.allocStatusNotIn != null and condition.allocStatusNotIn.size() > 0">
            and (A.physical_host_pool_id is null or A.physical_host_pool_id = 0
            or A.physical_host_pool_id in (select id from cloud_physical_host_pool where alloc_status in
            <foreach collection="condition.allocStatusNotIn" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>))
        </if>
        <if test="condition.cloudEnvAccountId != null">
            and B.cloud_env_account_id = #{condition.cloudEnvAccountId}
        </if>
    </sql>

    <sql id="Example_Where_Clause">
        <trim prefix="where" prefixOverrides="and|or">
            <include refid="Example_Where_Caluse_Params"/>
        </trim>
    </sql>
    <select id="selectCloudHostByProIdAndEnvId"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM res_vm A
        LEFT JOIN cloud_env B on A.cloud_env_id = B.id
        where
        A.cloud_env_id = #{condition.envId}
    </select>

    <update id="relateToUser" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        UPDATE res_vm SET owner_id = #{condition.userSid} WHERE ID = #{condition.resId}
    </update>

    <sql id="Base_Column_List">
        A.ID, A.ALLOCATE_RES_HOST_SID, A.CLOUD_ENV_ID, A.INSTANCE_ID,
        A.INSTANCE_NAME,
        A.MANAGEMENT_ACCOUNT, A.MANAGEMEN_PASSWORD,
        A.IMAGE_ID, A.IMAGE_TYPE, A.CPU, A.MEMORY, A.DESCRIPTION, A.INSTANCE_TYPE, A.OWNER_ID, A.PLATFORM,
        A.INTERNET_CHARGE_TYPE,A.INSTANCE_CHARGE_TYPE,
        A.INTERNET_MAX_BANDWIDTH_OUT, A.INTERNET_MAX_BANDWIDTH_IN, A.INNER_IP, A.PUBLIC_IP,
        A.INSTANCE_NETWORK_TYPE, A.`ZONE`, A.REGION,
        A.STATUS, A.CREATED_BY, A.CREATED_DT, A.UPDATED_BY,
        A.UPDATED_DT, A.VERSION, A.SSH_PORT, A.MANAGE_STATUS, A.STATUS_INFO, A.OS_NAME,
        A.SERVER_TYPE,
        A.REMOTE_LOGIN_TYPE, A.KEYPAIR_ID, A.ORIGIN_PARAM,
        A.FLOATING_IP_POOL_NAME, A.FLOATING_IP_POOL_ID, A.SERVER_TEMPLATE_ID
        , A.USE_MEMORY_SIZE, A.USE_STORE_SIZE, A.PROVISION_STORAGE, A.CPU_USAGE, A.USE_CPU_GHZ,
        A.DISK_USAGE,
        A.org_sid, A.LOCK_USER, A.LOCK_STATUS
        ,A.clone_source_id, A.elastic_group_id, A.elastic_history_id, A.start_time, A.end_time, A.os_category,
        A.stopped_mode, A.is_recycle, A.recycle_date,A.urn,A.uri, A.has_vm_tools, A.host_name, A.physical_host_pool_id,
        A.install_snmp, A.original_status, ALLOCATE_RES_VC_SID
        ,A.created_org_sid,A.vm_type_uuid
    </sql>
    <sql id="Base_Column_Without_Origin_List">
        A.ID, A.ALLOCATE_RES_HOST_SID, A.CLOUD_ENV_ID, A.INSTANCE_ID,
        A.INSTANCE_NAME,
        A.MANAGEMENT_ACCOUNT, A.MANAGEMEN_PASSWORD,
        A.IMAGE_ID, A.IMAGE_TYPE, A.CPU, A.MEMORY, A.DESCRIPTION, A.INSTANCE_TYPE, A.OWNER_ID, A.PLATFORM,
        A.INTERNET_CHARGE_TYPE,A.INSTANCE_CHARGE_TYPE,
        A.INTERNET_MAX_BANDWIDTH_OUT, A.INTERNET_MAX_BANDWIDTH_IN, A.INNER_IP, A.PUBLIC_IP,
        A.INSTANCE_NETWORK_TYPE, A.`ZONE`, A.REGION,
        A.STATUS, A.CREATED_BY, A.CREATED_DT, A.UPDATED_BY,
        A.UPDATED_DT, A.VERSION, A.SSH_PORT, A.MANAGE_STATUS, A.STATUS_INFO, A.OS_NAME,
        A.SERVER_TYPE,
        A.REMOTE_LOGIN_TYPE, A.KEYPAIR_ID,
        A.FLOATING_IP_POOL_NAME, A.FLOATING_IP_POOL_ID, A.SERVER_TEMPLATE_ID
        , A.USE_MEMORY_SIZE, A.USE_STORE_SIZE, A.PROVISION_STORAGE, A.CPU_USAGE, A.USE_CPU_GHZ,
        A.DISK_USAGE,
        A.org_sid, A.LOCK_USER, A.LOCK_STATUS
        ,A.clone_source_id, A.elastic_group_id, A.elastic_history_id, A.start_time, A.end_time, A.os_category,
        A.stopped_mode, A.is_recycle, A.recycle_date,A.urn,A.uri, A.has_vm_tools, A.host_name, A.physical_host_pool_id,
        A.install_snmp, A.ALLOCATE_RES_VC_SID
        ,A.created_org_sid
    </sql>
    <sql id="Base_Column_List_Without_Network_Name">
        A.ID, A.ALLOCATE_RES_HOST_SID, A.CLOUD_ENV_ID, A.INSTANCE_ID,
        A.INSTANCE_NAME,
        A.MANAGEMENT_ACCOUNT, A.MANAGEMEN_PASSWORD,
        A.IMAGE_ID, A.IMAGE_TYPE, A.CPU, A.MEMORY, A.DESCRIPTION, A.INSTANCE_TYPE, A.OWNER_ID, A.PLATFORM,
        A.INTERNET_CHARGE_TYPE,A.INSTANCE_CHARGE_TYPE,
        A.INTERNET_MAX_BANDWIDTH_OUT, A.INTERNET_MAX_BANDWIDTH_IN, A.INNER_IP, A.PUBLIC_IP,
        A.INSTANCE_NETWORK_TYPE, A.`ZONE`, A.REGION,
        A.STATUS, A.CREATED_BY, A.CREATED_DT, A.UPDATED_BY,
        A.UPDATED_DT, A.VERSION, A.SSH_PORT, A.MANAGE_STATUS, A.STATUS_INFO, A.OS_NAME,
        A.SERVER_TYPE,
        A.REMOTE_LOGIN_TYPE, A.KEYPAIR_ID, A.ORIGIN_PARAM,
        A.FLOATING_IP_POOL_NAME, A.FLOATING_IP_POOL_ID, A.SERVER_TEMPLATE_ID
        , A.USE_MEMORY_SIZE, A.USE_STORE_SIZE, A.PROVISION_STORAGE, A.CPU_USAGE, A.USE_CPU_GHZ,
        A.DISK_USAGE,
        A.org_sid, A.LOCK_USER, A.LOCK_STATUS
        ,A.clone_source_id, A.elastic_group_id, A.elastic_history_id, A.start_time, A.end_time, A.os_category,
        A.stopped_mode, A.is_recycle, A.recycle_date,A.urn,A.uri, A.has_vm_tools, A.host_name, A.physical_host_pool_id,
        A.install_snmp, A.ALLOCATE_RES_VC_SID
    </sql>

    <sql id="Host_Group_List">
        A.ID, A.ALLOCATE_RES_HOST_SID, A.CLOUD_ENV_ID, A.INSTANCE_ID,
        A.INSTANCE_NAME,
        A.MANAGEMENT_ACCOUNT, A.MANAGEMEN_PASSWORD,
        A.IMAGE_ID, A.IMAGE_TYPE, A.CPU, A.MEMORY, A.DESCRIPTION, A.INSTANCE_TYPE, A.OWNER_ID, A.PLATFORM,
        A.INTERNET_CHARGE_TYPE,A.INSTANCE_CHARGE_TYPE,
        A.INTERNET_MAX_BANDWIDTH_OUT, A.INTERNET_MAX_BANDWIDTH_IN, A.INNER_IP, A.PUBLIC_IP,
        A.INSTANCE_NETWORK_TYPE, A.ZONE, A.REGION,
        A.STATUS, A.CREATED_BY, A.CREATED_DT, A.UPDATED_BY,
        A.UPDATED_DT, A.VERSION, A.SSH_PORT, A.MANAGE_STATUS, A.STATUS_INFO, A.OS_NAME,
        A.REMOTE_LOGIN_TYPE, A.KEYPAIR_ID,
        A.FLOATING_IP_POOL_NAME, A.FLOATING_IP_POOL_ID, A.SERVER_TEMPLATE_ID
        , A.USE_MEMORY_SIZE, A.USE_STORE_SIZE, A.PROVISION_STORAGE, A.CPU_USAGE, A.USE_CPU_GHZ,
        A.DISK_USAGE,
        A.LOCK_USER, A.LOCK_STATUS
        , A.elastic_group_id, A.elastic_history_id, A.start_time, A.end_time, A.stopped_mode, A.is_recycle,
        A.recycle_date,
        A.has_vm_tools,A.host_name, A.ALLOCATE_RES_VC_SID
    </sql>

    <select id="selectHostSimpleInfoByParams"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultMap="BaseResultMap">
        select
        IFNULL(B.cloud_env_name,'其他') as CLOUD_ENV_NAME,
        B.cloud_env_type as CLOUD_ENV_TYPE,
        B.cloud_env_category as CLOUD_ENV_CATEGORY,
        <include refid="Base_Column_List"/>
        from res_vm A
        LEFT JOIN cloud_env B on (B.id = A.CLOUD_ENV_ID)
        <if test="_parameter != null and condition.tagIds != null and condition.tagIds.size() > 0">
            inner join cloud_tag_ref H on H.obj_id=A.id and H.obj_type='host'
            and H.tag_id in
            <foreach item="tags" index="index" collection="condition.tagIds" open="(" separator=","
                close=")">
                #{tags}
            </foreach>
        </if>
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        GROUP BY A.ID
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>

    </select>

    <!--查询关联name-->
    <select id="selectCloudDeploymentName" parameterType="list" resultType="java.util.Map">
        select SDRH.res_vm_id RES_VM_ID, group_concat(distinct cd.name) as CLOUD_DEPLOYMENT_NAME
        from cloud_deployment_vm SDRH
        INNER JOIN cloud_deployment cd on SDRH.deployment_id = cd.id
        where SDRH.res_vm_id in
        <foreach item="hostId" index="index" collection="hostIds" open="(" separator=","
            close=")">
            #{hostId}
        </foreach>
        GROUP BY SDRH.res_vm_id
    </select>

    <!--重写pagehelper分页条件，会被调用selectHostByParams 当作统计方法调用-->
    <select id="selectHostByParams_COUNT"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultType="Long">
        select count(A.ID) from res_vm A
        LEFT JOIN cloud_env B on (B.id = A.CLOUD_ENV_ID)
        LEFT JOIN res_host RH ON A.allocate_res_host_sid = RH.res_host_sid
        LEFT JOIN (select H.obj_id, GROUP_CONCAT(IFNULL(I.tag_name, ' ') ORDER BY I.tag_id) as TAG_NAMES,
        GROUP_CONCAT(IFNULL(I.rgb_code, ' ') ORDER BY I.tag_id) as RGB_CODES,
        GROUP_CONCAT(IFNULL(I.tag_value, ' ') ORDER BY I.tag_id) as TAG_VALUES from cloud_tag_ref H
        LEFT JOIN cloud_tag I on (H.tag_id= I.tag_id and H.obj_type = 'host') GROUP BY H.obj_id) HI on HI.obj_id = A.ID
        <if test="condition.tagIds != null and condition.tagIds.size > 0">
            inner join cloud_tag_ref K on k.obj_id = a.ID and K.obj_type = 'host' and K.tag_id in
            <foreach item="tags" index="index" collection="condition.tagIds" open="(" separator=","
                close=")">
                #{tags}
            </foreach>
        </if>
        <if test="condition.tagName != null">
            inner JOIN (select H.obj_id from cloud_tag_ref H
            LEFT JOIN cloud_tag I on (H.tag_id= I.tag_id and H.obj_type = 'host')
            <where>
                <if test="condition.tagName != null">
                    and I.tag_name like concat(#{condition.tagName}, '%')
                </if>
                <if test="condition.tagValue != null">
                    and I.tag_value like concat(#{condition.tagValue}, '%')
                </if>
            </where>
            GROUP BY H.obj_id) HIT on HIT.obj_id = A.ID
        </if>
        <if test="_parameter != null">
            <where>
                <include refid="Example_Where_Caluse_Params"/>
                <if test="sqlFilter != null">
                    ${sqlFilter}
                </if>
            </where>
        </if>
    </select>

    <select id="selectHostByParams" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultMap="BaseResultMap">
        select
        <include refid="statusSortNumSql"/>
        as statusSortNum,
        IFNULL(B.cloud_env_name,'其他') as CLOUD_ENV_NAME,
        B.cloud_env_type as CLOUD_ENV_TYPE,
        B.cloud_env_category as CLOUD_ENV_CATEGORY,
        B.stopped_mode as env_stopped_mode,
        B.cloud_env_account_id,
        C.CODE_DISPLAY as STATUS_NAME,
        D.CODE_DISPLAY as CLOUD_ENV_TYPE_NAME,
        F.CODE_DISPLAY as INSTANCE_CHARGE_TYPE_NAME,
        G.CODE_DISPLAY as MANAGE_STATUS_NAME,
        M.name as SERVER_TEMPLATE_NAME,
        case
        when A.image_type='snapshot' then s.name
        else N.image_name
        END IMAGE_NAME,
        IFNULL(O.keypair_name, A.keypair_name) as KEYPAIR_NAME,
        HI.TAG_NAMES,
        HI.RGB_CODES,
        HI.TAG_VALUES,
        smu.EMAIL AS lock_user_email,
        RH.uuid AS allocate_res_host_uuid, RH.host_name AS allocate_res_host_name,
        cea.platform_component_id,
        smur.account as OWNER_ACCOUNT,
        A.host_name as host_name,
        RH.host_name res_host_name,
        SOBRR.id AS deploy_id,
        SO.id as service_order_id,
        SO.order_sn AS service_order_sn,
        cd.name AS CLOUD_DEPLOYMENT_NAME,
        <if test="condition.withoutOriginParam == null">
            <include refid="Base_Column_List"/>
        </if>
        <if test="condition.withoutOriginParam != null">
            <include refid="Base_Column_Without_Origin_List"/>
        </if>
        from res_vm A
        LEFT JOIN cloud_deployment_vm cdv ON cdv.res_vm_id = A.ID
        LEFT JOIN cloud_deployment cd ON cdv.deployment_id = cd.id
        LEFT JOIN cloud_env B on (B.id = A.CLOUD_ENV_ID)
        LEFT JOIN cloud_env_account cea ON B.cloud_env_account_id = cea.id
        LEFT JOIN SYS_M_CODE C on (C.CODE_CATEGORY = 'RES_VM_STATUS' and C.CODE_VALUE = A.STATUS)
        LEFT JOIN SYS_M_CODE D on (D.CODE_CATEGORY = 'CLOUD_ENV_TYPE' and D.CODE_VALUE = B.CLOUD_ENV_TYPE)
        LEFT JOIN SYS_M_CODE F on (F.CODE_CATEGORY = 'ALIYUN_CHARGE_TYPE' and F.CODE_VALUE = A.INSTANCE_CHARGE_TYPE)
        LEFT JOIN SYS_M_CODE G on (G.CODE_CATEGORY = 'CLOUD_HOST_MANAGE_STATUS' and G.CODE_VALUE = A.MANAGE_STATUS)
        LEFT JOIN (select H.obj_id, GROUP_CONCAT(IFNULL(I.tag_name, ' ') ORDER BY I.tag_id) as TAG_NAMES,
        GROUP_CONCAT(IFNULL(I.rgb_code, ' ') ORDER BY I.tag_id) as RGB_CODES,
        GROUP_CONCAT(IFNULL(I.tag_value, ' ') ORDER BY I.tag_id) as TAG_VALUES from cloud_tag_ref H
        LEFT JOIN cloud_tag I on (H.tag_id= I.tag_id and H.obj_type = 'host') GROUP BY H.obj_id) HI on HI.obj_id = A.ID
        <if test="condition.tagIds != null and condition.tagIds.size > 0">
            inner join cloud_tag_ref K on k.obj_id = a.ID and K.obj_type = 'host' and K.tag_id in
            <foreach item="tags" index="index" collection="condition.tagIds" open="(" separator=","
                close=")">
                #{tags}
            </foreach>
        </if>
        <if test="condition.tagName != null">
            inner JOIN (select H.obj_id from cloud_tag_ref H
            LEFT JOIN cloud_tag I on (H.tag_id= I.tag_id and H.obj_type = 'host')
            <where>
                <if test="condition.tagName != null">
                    and I.tag_name like concat(#{condition.tagName}, '%')
                </if>
                <if test="condition.tagValue != null">
                    and I.tag_value like concat(#{condition.tagValue}, '%')
                </if>
            </where>
            GROUP BY H.obj_id) HIT on HIT.obj_id = A.ID
        </if>
        LEFT JOIN SERVER_TEMPLATE M on (A.SERVER_TEMPLATE_ID = M.ID)
        LEFT JOIN res_image N on (A.image_id = N.image_id AND ((A.cloud_env_id = N.cloud_env_id AND N.cloud_env_id != -
        1)
        OR (N.region = B.region AND N.cloud_env_type = B.cloud_env_type AND N.cloud_env_id = - 1)))
        LEFT JOIN res_snapshot s on (A.IMAGE_ID = s.uuid and A.CLOUD_ENV_ID = s.CLOUD_ENV_ID)
        LEFT JOIN CLOUD_KEYPAIR O on (A.KEYPAIR_ID = O.ID)
        LEFT JOIN sys_m_user smu on smu.USER_SID = A.LOCK_USER
        LEFT JOIN res_host RH ON A.allocate_res_host_sid = RH.res_host_sid
        LEFT JOIN sys_m_user smur ON smur.user_sid = A.owner_id
        LEFT JOIN service_order_basic_resource_relation SOBRR ON SOBRR.resource_id = A.id
        LEFT JOIN service_order SO ON SO.id = SOBRR.service_order_id
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        group by A.id
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>

    </select>

    <select id="selectSelfServiceHostByParams"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultMap="BaseResultMap">
        select
        <include refid="statusSortNumSql"/>
        as statusSortNum,
        IFNULL(B.cloud_env_name,'其他') as CLOUD_ENV_NAME,
        B.cloud_env_type as CLOUD_ENV_TYPE,
        B.cloud_env_category as CLOUD_ENV_CATEGORY,
        B.stopped_mode as env_stopped_mode,
        C.CODE_DISPLAY as STATUS_NAME,
        D.CODE_DISPLAY as CLOUD_ENV_TYPE_NAME,
        F.CODE_DISPLAY as INSTANCE_CHARGE_TYPE_NAME,
        G.CODE_DISPLAY as MANAGE_STATUS_NAME,
        M.name as SERVER_TEMPLATE_NAME,
        case
        when A.image_type='snapshot' then s.name
        else N.image_name
        END IMAGE_NAME,
        IFNULL(O.keypair_name, A.keypair_name) as KEYPAIR_NAME,
        HI.TAG_NAMES,
        HI.RGB_CODES,
        HI.TAG_VALUES,
        smu.EMAIL AS lock_user_email,
        RH.uuid AS allocate_res_host_uuid, RH.host_name AS allocate_res_host_name,
        cea.platform_component_id,
        smur.account as OWNER_ACCOUNT,
        RH.host_name res_host_name,
        SO.delete_resource AS delete_resource,
        IFNULL(SOBRR.id,SSDI.id ) AS deploy_id,
        IFNULL(SO.id,SSDI.service_order_id ) as service_order_id,
        IFNULL(SO.STATUS,SSDI.STATUS ) as server_status,
        IFNULL(SO.order_sn,SO2.order_sn) as service_order_sn,
        cd.name AS CLOUD_DEPLOYMENT_NAME,
        <if test="condition.withoutOriginParam == null">
            <include refid="Base_Column_List"/>
        </if>
        <if test="condition.withoutOriginParam != null">
            <include refid="Base_Column_Without_Origin_List"/>
        </if>
        from res_vm A
        LEFT JOIN cloud_deployment_vm cdv ON cdv.res_vm_id = A.ID
        LEFT JOIN cloud_deployment cd ON cdv.deployment_id = cd.id
        LEFT JOIN cloud_env B on (B.id = A.CLOUD_ENV_ID)
        LEFT JOIN cloud_env_account cea ON B.cloud_env_account_id = cea.id
        LEFT JOIN SYS_M_CODE C on (C.CODE_CATEGORY = 'RES_VM_STATUS' and C.CODE_VALUE = A.STATUS)
        LEFT JOIN SYS_M_CODE D on (D.CODE_CATEGORY = 'CLOUD_ENV_TYPE' and D.CODE_VALUE = B.CLOUD_ENV_TYPE)
        LEFT JOIN SYS_M_CODE F on (F.CODE_CATEGORY = 'ALIYUN_CHARGE_TYPE' and F.CODE_VALUE = A.INSTANCE_CHARGE_TYPE)
        LEFT JOIN SYS_M_CODE G on (G.CODE_CATEGORY = 'CLOUD_HOST_MANAGE_STATUS' and G.CODE_VALUE = A.MANAGE_STATUS)
        LEFT JOIN (select H.obj_id, GROUP_CONCAT(IFNULL(I.tag_name, ' ') ORDER BY I.tag_id) as TAG_NAMES,
        GROUP_CONCAT(IFNULL(I.rgb_code, ' ') ORDER BY I.tag_id) as RGB_CODES,
        GROUP_CONCAT(IFNULL(I.tag_value, ' ') ORDER BY I.tag_id) as TAG_VALUES from cloud_tag_ref H
        LEFT JOIN cloud_tag I on (H.tag_id= I.tag_id and H.obj_type = 'host') GROUP BY H.obj_id) HI on HI.obj_id = A.ID
        <if test="condition.tagIds != null and condition.tagIds.size > 0">
            inner join cloud_tag_ref K on k.obj_id = a.ID and K.obj_type = 'host' and K.tag_id in
            <foreach item="tags" index="index" collection="condition.tagIds" open="(" separator=","
                close=")">
                #{tags}
            </foreach>
        </if>
        <if test="condition.tagName != null">
            inner JOIN (select H.obj_id from cloud_tag_ref H
            LEFT JOIN cloud_tag I on (H.tag_id= I.tag_id and H.obj_type = 'host')
            <where>
                <if test="condition.tagName != null">
                    and I.tag_name like concat(#{condition.tagName}, '%')
                </if>
                <if test="condition.tagValue != null">
                    and I.tag_value like concat(#{condition.tagValue}, '%')
                </if>
            </where>
            GROUP BY H.obj_id) HIT on HIT.obj_id = A.ID
        </if>
        LEFT JOIN SERVER_TEMPLATE M on (A.SERVER_TEMPLATE_ID = M.ID)
        LEFT JOIN res_image N on (A.image_id = N.image_id AND ((A.cloud_env_id = N.cloud_env_id AND N.cloud_env_id != -
        1)
        OR (N.region = B.region AND N.cloud_env_type = B.cloud_env_type AND N.cloud_env_id = - 1)))
        LEFT JOIN res_snapshot s on (A.IMAGE_ID = s.uuid and A.CLOUD_ENV_ID = s.CLOUD_ENV_ID)
        LEFT JOIN CLOUD_KEYPAIR O on (A.KEYPAIR_ID = O.ID)
        LEFT JOIN sys_m_user smu on smu.USER_SID = A.LOCK_USER
        LEFT JOIN res_host RH ON A.allocate_res_host_sid = RH.res_host_sid
        LEFT JOIN sys_m_user smur ON smur.user_sid = A.owner_id
        LEFT JOIN service_order_basic_resource_relation SOBRR ON SOBRR.resource_id = A.id
        LEFT JOIN service_order SO ON SO.id = SOBRR.service_order_id
        LEFT JOIN sf_service_inst_target SSIT ON SSIT.target_id = A.id
        LEFT JOIN sf_service_deploy_inst SSDI ON SSDI.id = SSIT.sf_service_inst_id
        LEFT JOIN service_order SO2 ON SO2.id = SSDI.service_order_id
        LEFT JOIN service_order_detail SOD ON SOD.order_id = SOBRR.service_order_id
        LEFT JOIN sf_service_category SSC ON SSC.id = SOD.service_id
        LEFT JOIN sf_service_category SSC2 ON SSC2.id = SSDI.service_id
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="condition.selfServiceEqual != null">
            AND !(A.created_org_sid = A.org_sid and SO.id is null and SO2.id is null and cd.NAME is null)
            AND (SSC.resource_visible = 1 or SSC.resource_visible is null)
            AND (SSC2.resource_visible = 1 or SSC2.resource_visible is null)
        </if>
        group by A.id
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>

    </select>

    <sql id="statusSortNumSql">
        CASE
        WHEN A.`status` IN ( 'pending' ) THEN 1
        WHEN A.`status` IN ( 'creating', 'starting', 'stopping', 'deleting', 'setting' ) THEN 2
        WHEN A.`status` IN ( 'create_failure', 'failure' ) THEN 3
        WHEN A.`status` IN ( 'running', 'stopped', 'expired' ) THEN 4
        WHEN A.`status` IN ( 'suspended', 'deleted' ) THEN 5
        ELSE 99
        END
    </sql>

    <select id="selectHostForClusterByParams"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultMap="BaseResultMap">
        select
        IFNULL(B.cloud_env_name, '其他') as CLOUD_ENV_NAME,
        B.cloud_env_type as CLOUD_ENV_TYPE,
        B.cloud_env_category as CLOUD_ENV_CATEGORY,
        C.CODE_DISPLAY as STATUS_NAME,
        D.CODE_DISPLAY as CLOUD_ENV_TYPE_NAME,
        G.CODE_DISPLAY as MANAGE_STATUS_NAME,
        <include refid="Base_Column_List"/>
        from res_vm A
        LEFT JOIN cloud_env B on (B.id = A.CLOUD_ENV_ID)
        LEFT JOIN SYS_M_CODE C on (C.CODE_CATEGORY = 'RES_VM_STATUS' and C.CODE_VALUE = A.STATUS)
        LEFT JOIN SYS_M_CODE D on (D.CODE_CATEGORY = 'CLOUD_ENV_TYPE' and D.CODE_VALUE = B.CLOUD_ENV_TYPE)
        LEFT JOIN SYS_M_CODE G on (G.CODE_CATEGORY = 'CLOUD_HOST_MANAGE_STATUS' and G.CODE_VALUE = A.MANAGE_STATUS)
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        group by A.ID
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>

    </select>


    <select id="selectCanBeExecScriptHosts"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultMap="BaseResultMap">
        select
        A.instance_name,
        A.server_type,
        A.id,
        A.instance_id,
        b.cloud_env_type,
        b.cloud_env_name,
        A.MANAGE_STATUS,
        c.CODE_DISPLAY AS CLOUD_ENV_TYPE_NAME
        from res_vm A
        INNER JOIN cloud_env B ON a.cloud_env_id = B.id
        INNER JOIN SYS_M_CODE C ON C.CODE_CATEGORY = 'CLOUD_ENV_TYPE' and C.CODE_VALUE =
        B.CLOUD_ENV_TYPE
        LEFT JOIN cloud_deployment_vm SDRH on (SDRH.res_vm_id = A.ID)
        where A.status = 'running' and SDRH.DEPLOYMENT_ID = #{condition.cloudDeploymentId}
        UNION
        select
        A.instance_name,
        A.server_type,
        A.id,
        A.instance_id,
        b.cloud_env_type,
        b.cloud_env_name,
        A.MANAGE_STATUS,
        c.CODE_DISPLAY AS CLOUD_ENV_TYPE_NAME
        from res_vm A
        INNER JOIN res_vm_script D on(A.id = D.instance_id and D.is_enable = 1)
        INNER JOIN cloud_env B ON a.cloud_env_id = B.id
        INNER JOIN SYS_M_CODE C ON C.CODE_CATEGORY = 'CLOUD_ENV_TYPE' and C.CODE_VALUE =
        B.CLOUD_ENV_TYPE
        LEFT JOIN cloud_deployment_vm SDRH on (SDRH.res_vm_id = A.ID)
        where SDRH.DEPLOYMENT_ID = #{condition.cloudDeploymentId} and D.script_id = #{condition.scriptId} and
        A.status = 'running'
    </select>

    <select id="selectBaseByParams" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultMap="BaseResultMap">
        select
        IFNULL(b.cloud_env_type, '其他') as cloud_env_type
        , IFNULL(b.cloud_env_name, '其他') as cloud_env_name
        , b.stopped_mode as env_stopped_mode
        , c.CODE_DISPLAY AS CLOUD_ENV_TYPE_NAME
        , D.CODE_DISPLAY as STATUS_NAME,
        A.keypair_name,
        E.EMAIL as lock_user_email,
        S.name as SERVER_TEMPLATE_NAME,
        N.image_name as IMAGE_NAME,
        HI.TAG_NAMES,
        HI.RGB_CODES,
        HI.TAG_VALUES,
        smo.org_name,
        GROUP_CONCAT(distinct Q.org_name ORDER BY Q.org_sid separator '/') as owner_org_name,
        CD.name as CLOUD_DEPLOYMENT_NAME,
        A.`ZONE` as ZONE_NAME,
        EU.ACCOUNT as OWNER_ACCOUNT,
        <include refid="Base_Column_List"/>
        from res_vm A
        LEFT JOIN cloud_deployment_vm SDRH on (SDRH.res_vm_id = A.ID)
        LEFT JOIN CLOUD_DEPLOYMENT CD on (SDRH.DEPLOYMENT_ID = CD.ID)
        LEFT JOIN sys_m_org smo ON smo.org_sid = A.org_sid
        LEFT JOIN sys_m_org Q on (locate(CONCAT('/', Q.org_sid, '/'), smo.tree_path) or smo.org_sid =
        Q.org_sid)
        LEFT JOIN cloud_env B ON a.cloud_env_id = B.id
        LEFT JOIN SYS_M_CODE C ON C.CODE_CATEGORY = 'CLOUD_ENV_TYPE' and C.CODE_VALUE =
        B.CLOUD_ENV_TYPE
        LEFT JOIN SYS_M_CODE D on (D.CODE_CATEGORY = 'RES_VM_STATUS' and D.CODE_VALUE = A.STATUS
        )
        LEFT JOIN SYS_M_USER E ON A.lock_user = E.USER_SID
        LEFT JOIN SYS_M_USER EU ON A.OWNER_ID = EU.USER_SID
        LEFT JOIN res_host RH ON A.allocate_res_host_sid = RH.res_host_sid
        LEFT JOIN server_template S ON A.server_template_id = S.id
        LEFT JOIN res_image N on (A.image_id = N.image_id
        AND ((A.cloud_env_id = N.cloud_env_id AND N.cloud_env_id != - 1)
        OR (N.region = B.region AND N.cloud_env_type = B.cloud_env_type AND N.cloud_env_id = - 1)))
        LEFT JOIN (select H.obj_id, GROUP_CONCAT(I.tag_name ORDER BY I.tag_id) as TAG_NAMES,
        GROUP_CONCAT(I.rgb_code ORDER BY I.tag_id) as RGB_CODES,
        GROUP_CONCAT(I.tag_value ORDER BY I.tag_id) as TAG_VALUES from cloud_tag_ref H
        LEFT JOIN cloud_tag I on (H.tag_id= I.tag_id and H.obj_type = 'host') GROUP BY H.obj_id) HI on HI.obj_id = A.ID
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="condition.tagNames != null">
            and HI.TAG_NAMES like concat('%', #{condition.tagNames}, '%')
        </if>
        group by A.id
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>

    </select>

    <select id="selectByExample" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultMap="BaseResultMap">
        select
        distinct
        IFNULL(B.cloud_env_name,'其他') as CLOUD_ENV_NAME,
        GROUP_CONCAT(IFNULL(F.name,'其他')) as CLOUD_DEPLOYMENT_NAME,
        CONCAT(A.INSTANCE_NAME,'(',B.cloud_env_name,')') as INSTANCE_ALL_NAME,
        B.cloud_env_type as CLOUD_ENV_TYPE,
        C.CODE_DISPLAY as STATUS_NAME,
        IFNULL(D.CODE_DISPLAY, '其他') as CLOUD_ENV_TYPE_NAME,
        A.keypair_name,
        E.cluster_id,
        <include refid="Base_Column_List"/>
        from res_vm A
        LEFT JOIN cloud_env B on (B.id = A.CLOUD_ENV_ID)
        LEFT JOIN SYS_M_CODE C on (C.CODE_CATEGORY = 'RES_VM_STATUS' and C.CODE_VALUE = A.STATUS
        )
        LEFT JOIN SYS_M_CODE D on (D.CODE_CATEGORY = 'CLOUD_ENV_TYPE' and D.CODE_VALUE =
        B.CLOUD_ENV_TYPE )
        LEFT JOIN CLOUD_CLUSTER_NODE E on (E.host_id = A.id )
        LEFT JOIN cloud_deployment_vm SDRH on (SDRH.res_vm_id = A.ID)
        LEFT JOIN CLOUD_DEPLOYMENT F on (F.id = SDRH.DEPLOYMENT_ID )
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        group by id
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>

    </select>
    <select id="selectByExampleWithoutFilter"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultMap="BaseResultMap">
        select
        distinct
        IFNULL(B.cloud_env_name,'其他') as CLOUD_ENV_NAME,
        GROUP_CONCAT(IFNULL(F.name,'其他')) as CLOUD_DEPLOYMENT_NAME,
        CONCAT(A.INSTANCE_NAME,'(',B.cloud_env_name,')') as INSTANCE_ALL_NAME,
        B.cloud_env_type as CLOUD_ENV_TYPE,
        C.CODE_DISPLAY as STATUS_NAME,
        IFNULL(D.CODE_DISPLAY, '其他') as CLOUD_ENV_TYPE_NAME,
        A.keypair_name,
        E.cluster_id,
        <include refid="Base_Column_List"/>
        from res_vm A
        LEFT JOIN cloud_env B on (B.id = A.CLOUD_ENV_ID)
        LEFT JOIN SYS_M_CODE C on (C.CODE_CATEGORY = 'RES_VM_STATUS' and C.CODE_VALUE = A.STATUS
        )
        LEFT JOIN SYS_M_CODE D on (D.CODE_CATEGORY = 'CLOUD_ENV_TYPE' and D.CODE_VALUE =
        B.CLOUD_ENV_TYPE )
        LEFT JOIN CLOUD_CLUSTER_NODE E on (E.host_id = A.id )
        LEFT JOIN cloud_deployment_vm SDRH on (SDRH.res_vm_id = A.ID)
        LEFT JOIN CLOUD_DEPLOYMENT F on (F.id = SDRH.DEPLOYMENT_ID )
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        group by id
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>

    </select>

    <select id="selectIdsByExample" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultMap="BaseResultMap">
        select
        distinct
        A.ID
        from res_vm A
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        group by id
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>

    <select id="selectSimpleByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from res_vm A
        where A.ID = #{id,jdbcType=VARCHAR}
    </select>

    <select id="selectSimpleByPrimaryKeyForUpdate" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from res_vm A
        where A.ID = #{id,jdbcType=VARCHAR}
        for update
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        B.cloud_env_name as CLOUD_ENV_NAME,
        B.cloud_env_type as CLOUD_ENV_TYPE,
        B.cloud_env_category as CLOUD_ENV_CATEGORY,
        C.CODE_DISPLAY as STATUS_NAME,
        D.CODE_DISPLAY as CLOUD_ENV_TYPE_NAME,
        E.CODE_DISPLAY as INSTANCE_CHARGE_TYPE_NAME,
        F.CODE_DISPLAY as MANAGE_STATUS_NAME,
        IFNULL(J.keypair_name, A.keypair_name) as keypairName,
        HI.TAG_NAMES,
        HI.RGB_CODES,
        HI.TAG_VALUES,
        GROUP_CONCAT(IFNULL(K.name, '--')) as CLOUD_DEPLOYMENT_NAME,
        L.name as SERVER_TEMPLATE_NAME,
        smu.EMAIL as lock_user_email,
        smu2.EMAIL as creater,
        smo.org_name,
        smo.org_type,
        smu3.ACCOUNT as owner_account,
        M.cluster_id,
        <include refid="Base_Column_List"/>
        , A.ORIGIN_PARAM
        from res_vm A
        LEFT JOIN cloud_env B on (B.id = A.CLOUD_ENV_ID)
        LEFT JOIN SYS_M_CODE C on (C.CODE_CATEGORY = 'RES_VM_STATUS' and C.CODE_VALUE = A.STATUS
        )
        LEFT JOIN SYS_M_CODE D on (D.CODE_CATEGORY = 'CLOUD_ENV_TYPE' and D.CODE_VALUE =
        B.CLOUD_ENV_TYPE )
        LEFT JOIN SYS_M_CODE E on (E.CODE_CATEGORY = 'ALIYUN_CHARGE_TYPE' and E.CODE_VALUE =
        A.INSTANCE_CHARGE_TYPE )
        LEFT JOIN SYS_M_CODE F on (F.CODE_CATEGORY = 'CLOUD_HOST_MANAGE_STATUS' and F.CODE_VALUE =
        A.MANAGE_STATUS )
        LEFT JOIN (select H.obj_id, GROUP_CONCAT(I.tag_name ORDER BY I.tag_id) as TAG_NAMES,
        GROUP_CONCAT(I.rgb_code ORDER BY I.tag_id) as RGB_CODES,
        GROUP_CONCAT(I.tag_value ORDER BY I.tag_id) as TAG_VALUES from cloud_tag_ref H
        LEFT JOIN cloud_tag I on (H.tag_id= I.tag_id and H.obj_type = 'host') GROUP BY H.obj_id) HI on HI.obj_id = A.ID
        LEFT JOIN cloud_keypair J on (A.KEYPAIR_ID= J.id)
        LEFT JOIN cloud_deployment_vm SDRH on (SDRH.res_vm_id = A.ID)
        LEFT JOIN cloud_deployment K on (SDRH.DEPLOYMENT_ID = K.id)
        LEFT JOIN server_template L on (A.server_template_id = L.id)
        LEFT JOIN sys_m_user smu on A.LOCK_USER = smu.USER_SID
        LEFT JOIN sys_m_user smu2 on A.created_by = smu2.ACCOUNT
        LEFT JOIN sys_m_org smo ON smo.org_sid = A.org_sid
        LEFT JOIN sys_m_user smu3 ON smu3.USER_SID = A.owner_id
        LEFT JOIN CLOUD_CLUSTER_NODE M on (M.host_id = A.id )
        where A.ID = #{id,jdbcType=VARCHAR}
        GROUP BY A.ID
    </select>

    <select id="selectByPrimaryKeyAndEnvId" parameterType="java.lang.String"
        resultMap="BaseResultMap">
        select
        B.cloud_env_name as CLOUD_ENV_NAME,
        B.cloud_env_type as CLOUD_ENV_TYPE,
        SOBRR.id AS deploy_id,
        SO.id as service_order_id,
        SO.order_sn AS service_order_sn,
        B.cloud_env_category as CLOUD_ENV_CATEGORY,
        B.stopped_mode as env_stopped_mode,
        C.CODE_DISPLAY as STATUS_NAME,
        D.CODE_DISPLAY as CLOUD_ENV_TYPE_NAME,
        E.CODE_DISPLAY as INSTANCE_CHARGE_TYPE_NAME,
        F.CODE_DISPLAY as MANAGE_STATUS_NAME,
        IFNULL(J.keypair_name, A.keypair_name) as keypair_name,
        HI.TAG_NAMES,
        HI.RGB_CODES,
        HI.TAG_VALUES,
        L.name as SERVER_TEMPLATE_NAME,
        smu.EMAIL as lock_user_email,
        smu2.real_name as creater,
        rp.id as res_pool_id,
        if(B.cloud_env_type='CloudOS', rz.label_name, rz.name) as zone_name,
        <include refid="Base_Column_List_Without_Network_Name"/>
        , A.ORIGIN_PARAM
        , IFNULL(rvt.family_name, rvt.family) AS instance_type_family_name
        , IFNULL(cei.image_name, s.name ) AS image_name
        , group_concat(distinct cd.name) as CLOUD_DEPLOYMENT_NAME,
        smo.org_name,
        cea.platform_component_id,
        cea.id as CLOUD_ENV_ACCOUNT_ID
        from res_vm A
        LEFT JOIN cloud_env B on (B.id = A.CLOUD_ENV_ID)
        LEFT JOIN cloud_env_account cea on (B.cloud_env_account_id = cea.id)
        LEFT JOIN SYS_M_CODE C on (C.CODE_CATEGORY = 'RES_VM_STATUS' and C.CODE_VALUE = A.STATUS
        )
        LEFT JOIN SYS_M_CODE D on (D.CODE_CATEGORY = 'CLOUD_ENV_TYPE' and D.CODE_VALUE =
        B.CLOUD_ENV_TYPE )
        LEFT JOIN SYS_M_CODE E on (E.CODE_CATEGORY = 'ALIYUN_CHARGE_TYPE' and E.CODE_VALUE =
        A.INSTANCE_CHARGE_TYPE )
        LEFT JOIN SYS_M_CODE F on (F.CODE_CATEGORY = 'CLOUD_HOST_MANAGE_STATUS' and F.CODE_VALUE =
        A.MANAGE_STATUS )
        LEFT JOIN (select H.obj_id, GROUP_CONCAT(IFNULL(I.tag_name, ' ') ORDER BY I.tag_id) as TAG_NAMES,
        GROUP_CONCAT(IFNULL(I.rgb_code, ' ') ORDER BY I.tag_id) as RGB_CODES,
        GROUP_CONCAT(IFNULL(I.tag_value, ' ') ORDER BY I.tag_id) as TAG_VALUES from cloud_tag_ref H
        LEFT JOIN cloud_tag I on (H.tag_id= I.tag_id and H.obj_type = 'host') GROUP BY H.obj_id) HI on HI.obj_id = A.ID
        LEFT JOIN cloud_keypair J on (A.KEYPAIR_ID= J.id)
        LEFT JOIN server_template L on (A.server_template_id = L.id)
        LEFT JOIN sys_m_user smu on A.LOCK_USER = smu.USER_SID
        LEFT JOIN sys_m_user smu2 on A.created_by = smu2.ACCOUNT
        LEFT JOIN res_pool rp on rp.name = A.zone and rp.cloud_env_id = a.cloud_env_id
        LEFT JOIN res_zone rz on rz.uuid = A.zone
        LEFT JOIN res_vm_type rvt on (A.instance_type = rvt.uuid
        AND ((A.cloud_env_id = rvt.cloud_env_id AND rvt.cloud_env_id != - 1)
        OR ((rvt.region = B.region OR rvt.region = '' OR rvt.region is null) AND rvt.cloud_env_type = B.cloud_env_type
        AND rvt.cloud_env_id = - 1 )))
        LEFT JOIN res_image cei ON (A.image_id = cei.image_id
        AND ((A.cloud_env_id = cei.cloud_env_id AND cei.cloud_env_id != - 1)
        OR (cei.region = B.region AND cei.cloud_env_type = B.cloud_env_type AND cei.cloud_env_id = - 1)))
        LEFT JOIN res_snapshot s on (A.IMAGE_ID = s.uuid and A.CLOUD_ENV_ID = s.CLOUD_ENV_ID)
        LEFT JOIN cloud_deployment_vm SDRH on (SDRH.res_vm_id = A.ID)
        LEFT JOIN cloud_deployment cd on SDRH.deployment_id = cd.id
        LEFT JOIN sys_m_org smo on smo.org_sid = A.org_sid
        LEFT JOIN service_order_basic_resource_relation SOBRR ON SOBRR.resource_id = A.id
        LEFT JOIN service_order SO ON SO.id = SOBRR.service_order_id
        where A.ID = #{id,jdbcType=VARCHAR}
        GROUP BY A.ID
    </select>

    <select id="selectByInstanceId" resultMap="BaseResultMap">
        select
        B.cloud_env_name as CLOUD_ENV_NAME,
        B.cloud_env_type as CLOUD_ENV_TYPE,
        B.cloud_env_category as CLOUD_ENV_CATEGORY,
        C.CODE_DISPLAY as STATUS_NAME,
        D.CODE_DISPLAY as CLOUD_ENV_TYPE_NAME,
        E.CODE_DISPLAY as INSTANCE_CHARGE_TYPE_NAME,
        F.CODE_DISPLAY as MANAGE_STATUS_NAME,
        IFNULL(J.keypair_name, A.keypair_name) as keypairName,
        HI.TAG_NAMES,
        HI.RGB_CODES,
        HI.TAG_VALUES,
        L.name as SERVER_TEMPLATE_NAME,
        smu.EMAIL as lock_user_email,
        <include refid="Base_Column_List"/>
        , A.ORIGIN_PARAM
        from res_vm A
        LEFT JOIN cloud_env B on (B.id = A.CLOUD_ENV_ID)
        LEFT JOIN SYS_M_CODE C on (C.CODE_CATEGORY = 'RES_VM_STATUS' and C.CODE_VALUE = A.STATUS
        )
        LEFT JOIN SYS_M_CODE D on (D.CODE_CATEGORY = 'CLOUD_ENV_TYPE' and D.CODE_VALUE =
        B.CLOUD_ENV_TYPE )
        LEFT JOIN SYS_M_CODE E on (E.CODE_CATEGORY = 'ALIYUN_CHARGE_TYPE' and E.CODE_VALUE =
        A.INSTANCE_CHARGE_TYPE )
        LEFT JOIN SYS_M_CODE F on (F.CODE_CATEGORY = 'CLOUD_HOST_MANAGE_STATUS' and F.CODE_VALUE =
        A.MANAGE_STATUS )
        LEFT JOIN (select H.obj_id, GROUP_CONCAT(I.tag_name ORDER BY I.tag_id) as TAG_NAMES,
        GROUP_CONCAT(I.rgb_code ORDER BY I.tag_id) as RGB_CODES,
        GROUP_CONCAT(I.tag_value ORDER BY I.tag_id) as TAG_VALUES from cloud_tag_ref H
        LEFT JOIN cloud_tag I on (H.tag_id= I.tag_id and H.obj_type = 'host') GROUP BY H.obj_id) HI on HI.obj_id = A.ID
        LEFT JOIN cloud_keypair J on (A.KEYPAIR_ID= J.id)
        LEFT JOIN server_template L on (A.server_template_id = L.id)
        LEFT JOIN sys_m_user smu on A.LOCK_USER = smu.USER_SID
        where A.instance_id = #{instanceId,jdbcType=VARCHAR}
        and A.cloud_env_id = #{cloudEnvId}
        AND A.status != 'deleted'
        GROUP BY A.ID
        LIMIT 1
    </select>
    <select id="selectByCriteria" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from res_vm A
        <if test="_parameter != null">
            <include refid="Base_Example_Where_Clause"/>
        </if>
        GROUP BY A.ID
    </select>
    <select id="countStatusByCriteria" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        A.STATUS, count(*) count_status
        from res_vm A
        <if test="_parameter != null">
            <include refid="Base_Example_Where_Clause"/>
        </if>
        GROUP BY A.STATUS
    </select>
    <select id="selectByCriteriaSample" parameterType="java.lang.String" resultMap="BaseResultMap">
        select A.ID, A.ALLOCATE_RES_HOST_SID, A.CLOUD_ENV_ID, A.INSTANCE_ID,
        A.INSTANCE_NAME,
        A.CPU, A.MEMORY, A.INSTANCE_TYPE,
        A.STATUS, A.CREATED_BY, A.CREATED_DT, A.UPDATED_BY,
        A.UPDATED_DT, A.VERSION, A.SSH_PORT, A.MANAGE_STATUS, A.STATUS_INFO, A.OS_NAME,
        A.SERVER_TYPE,
        A.FLOATING_IP_POOL_NAME, A.FLOATING_IP_POOL_ID, A.SERVER_TEMPLATE_ID
        , A.USE_MEMORY_SIZE, A.USE_STORE_SIZE, A.PROVISION_STORAGE, A.CPU_USAGE, A.USE_CPU_GHZ,
        A.DISK_USAGE,
        A.org_sid, A.LOCK_USER, A.LOCK_STATUS
        , A.start_time, A.end_time, A.os_category,
        A.stopped_mode, A.is_recycle, A.recycle_date,A.urn,A.uri, A.has_vm_tools, A.host_name
        from res_vm A
        INNER JOIN cloud_env env ON A.cloud_env_id = env.id
        INNER JOIN cloud_env_account account ON account.id = env.cloud_env_account_id
        <if test="_parameter != null">
            <include refid="Base_Example_Where_Clause"/>
        </if>
        GROUP BY A.ID
    </select>
    <select id="selectVotingInfo" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        SELECT
        ev.elastic_tag,
        ev.elastic_mode,
        ev.vote_time,
        ev.vote_reason,
        eg.interval_time,
        #{condition.startElasticTime} as last_elastic_time,
        <include refid="Base_Column_List"/>
        FROM
        elastic_group eg
        LEFT JOIN elastic_vote ev ON ev.elastic_tag = eg.elastic_tag
        LEFT JOIN res_vm a ON a.id = ev.res_vm_id
        WHERE
        eg.id = #{condition.elasticGroupId}
        AND ev.vote_time BETWEEN #{condition.startElasticTime}
        AND #{condition.now}
        GROUP BY
        ev.elastic_tag,
        ev.res_vm_id
        ORDER BY
        ev.vote_time DESC
    </select>
    <select id="selectByParams" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultMap="BaseResultMap">
        select
        B.cloud_env_name as CLOUD_ENV_NAME,
        B.cloud_env_type as CLOUD_ENV_TYPE,
        B.cloud_env_category as CLOUD_ENV_CATEGORY,
        C.CODE_DISPLAY as STATUS_NAME,
        D.CODE_DISPLAY as CLOUD_ENV_TYPE_NAME,
        E.CODE_DISPLAY as INSTANCE_CHARGE_TYPE_NAME,
        F.CODE_DISPLAY as MANAGE_STATUS_NAME,
        IFNULL(J.keypair_name, A.keypair_name) as keypairName,
        L.name as SERVER_TEMPLATE_NAME,
        smu.EMAIL as lock_user_email,
        HI.TAG_NAMES,
        HI.RGB_CODES,
        HI.TAG_VALUES,
        <include refid="Base_Column_List"/>
        , A.ORIGIN_PARAM
        from res_vm A
        LEFT JOIN cloud_env B on (B.id = A.CLOUD_ENV_ID)
        LEFT JOIN SYS_M_CODE C on (C.CODE_CATEGORY = 'RES_VM_STATUS' and C.CODE_VALUE = A.STATUS
        )
        LEFT JOIN SYS_M_CODE D on (D.CODE_CATEGORY = 'CLOUD_ENV_TYPE' and D.CODE_VALUE =
        B.CLOUD_ENV_TYPE )
        LEFT JOIN SYS_M_CODE E on (E.CODE_CATEGORY = 'ALIYUN_CHARGE_TYPE' and E.CODE_VALUE =
        A.INSTANCE_CHARGE_TYPE )
        LEFT JOIN SYS_M_CODE F on (F.CODE_CATEGORY = 'CLOUD_HOST_MANAGE_STATUS' and F.CODE_VALUE =
        A.MANAGE_STATUS )
        LEFT JOIN (select H.obj_id, GROUP_CONCAT(I.tag_name ORDER BY I.tag_id) as TAG_NAMES,
        GROUP_CONCAT(I.rgb_code ORDER BY I.tag_id) as RGB_CODES,
        GROUP_CONCAT(I.tag_value ORDER BY I.tag_id) as TAG_VALUES from cloud_tag_ref H
        LEFT JOIN cloud_tag I on (H.tag_id= I.tag_id and H.obj_type = 'host') GROUP BY H.obj_id) HI on HI.obj_id = A.ID
        LEFT JOIN cloud_keypair J on (A.KEYPAIR_ID= J.id)
        LEFT JOIN server_template L on (A.server_template_id = L.id)
        LEFT JOIN sys_m_user smu on A.LOCK_USER = smu.USER_SID
        LEFT JOIN res_host rh ON a.allocate_res_host_sid = rh.res_host_sid
        LEFT JOIN res_pool_resource rpr ON (
        rh.res_host_sid = rpr.resource_sid
        AND rpr.resource_type = 'RES-HOST'
        )
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.elasticGroupId != null">
                and A.elastic_group_id= #{condition.elasticGroupId,jdbcType=BIGINT}
            </if>
            <if test="condition.cloudEnvId != null">
                and A.cloud_env_id= #{condition.cloudEnvId,jdbcType=BIGINT}
            </if>
            <if test="condition.serverType != null">
                and A.server_type = #{condition.serverType,jdbcType=VARCHAR}
            </if>
            <if test="condition.orgSid != null">
                and A.org_sid = #{condition.orgSid,jdbcType=VARCHAR}
            </if>
            <if test="condition.serverTypeNotEquals != null">
                and A.server_type != #{condition.serverTypeNotEquals,jdbcType=VARCHAR}
            </if>
            <if test="condition.status != null">
                and A.status = #{condition.status,jdbcType=VARCHAR}
            </if>
            <if test="condition.resPoolIds != null">
                and rpr.res_pool_id in
                <foreach item="item" index="index" collection="condition.resPoolIds"
                    open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.resPoolId != null">
                and rpr.res_pool_id = #{condition.resPoolId}
            </if>
            <if test="condition.startTime != null">
                and A.start_time = #{condition.startTime}
            </if>
            <if test="condition.endTime != null">
                and A.end_time = #{condition.endTime}
            </if>
            <if test="condition.statusNotEquals != null">
                and A.status != #{condition.statusNotEquals}
            </if>
            <if test="condition.instanceIdNotNull != null">
                and A.instance_id IS NOT NULL
            </if>
            <if test="condition.instanceId != null">
                and A.instance_id = #{condition.instanceId}
            </if>
        </trim>
        group by A.ID
    </select>
    <select id="selectByParamsWithoutFilter"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultMap="BaseResultMap">
        select
        B.cloud_env_name as CLOUD_ENV_NAME,
        B.cloud_env_type as CLOUD_ENV_TYPE,
        B.cloud_env_category as CLOUD_ENV_CATEGORY,
        C.CODE_DISPLAY as STATUS_NAME,
        D.CODE_DISPLAY as CLOUD_ENV_TYPE_NAME,
        E.CODE_DISPLAY as INSTANCE_CHARGE_TYPE_NAME,
        F.CODE_DISPLAY as MANAGE_STATUS_NAME,
        IFNULL(J.keypair_name, A.keypair_name) as keypairName,
        L.name as SERVER_TEMPLATE_NAME,
        smu.EMAIL as lock_user_email,
        HI.TAG_NAMES,
        HI.RGB_CODES,
        HI.TAG_VALUES,
        <include refid="Base_Column_List"/>
        , A.ORIGIN_PARAM
        from res_vm A
        LEFT JOIN cloud_env B on (B.id = A.CLOUD_ENV_ID)
        LEFT JOIN SYS_M_CODE C on (C.CODE_CATEGORY = 'RES_VM_STATUS' and C.CODE_VALUE = A.STATUS
        )
        LEFT JOIN SYS_M_CODE D on (D.CODE_CATEGORY = 'CLOUD_ENV_TYPE' and D.CODE_VALUE =
        B.CLOUD_ENV_TYPE )
        LEFT JOIN SYS_M_CODE E on (E.CODE_CATEGORY = 'ALIYUN_CHARGE_TYPE' and E.CODE_VALUE =
        A.INSTANCE_CHARGE_TYPE )
        LEFT JOIN SYS_M_CODE F on (F.CODE_CATEGORY = 'CLOUD_HOST_MANAGE_STATUS' and F.CODE_VALUE =
        A.MANAGE_STATUS )
        LEFT JOIN (select H.obj_id, GROUP_CONCAT(I.tag_name ORDER BY I.tag_id) as TAG_NAMES,
        GROUP_CONCAT(I.rgb_code ORDER BY I.tag_id) as RGB_CODES,
        GROUP_CONCAT(I.tag_value ORDER BY I.tag_id) as TAG_VALUES from cloud_tag_ref H
        LEFT JOIN cloud_tag I on (H.tag_id= I.tag_id and H.obj_type = 'host') GROUP BY H.obj_id) HI on HI.obj_id = A.ID
        LEFT JOIN cloud_keypair J on (A.KEYPAIR_ID= J.id)
        LEFT JOIN server_template L on (A.server_template_id = L.id)
        LEFT JOIN sys_m_user smu on A.LOCK_USER = smu.USER_SID
        LEFT JOIN res_host rh ON a.allocate_res_host_sid = rh.res_host_sid
        LEFT JOIN res_pool_resource rpr ON (
        rh.res_host_sid = rpr.resource_sid
        AND rpr.resource_type = 'RES-HOST'
        )
        <trim prefix="where" prefixOverrides="and|or">
            <!--只展示已经接管的主机-->
            AND A.manage_status = 'connected'

            <if test="condition.elasticGroupId != null">
                and A.elastic_group_id= #{condition.elasticGroupId,jdbcType=BIGINT}
            </if>
            <if test="condition.cloudEnvId != null">
                and A.cloud_env_id= #{condition.cloudEnvId,jdbcType=BIGINT}
            </if>
            <if test="condition.serverType != null">
                and A.server_type = #{condition.serverType,jdbcType=VARCHAR}
            </if>
            <if test="condition.serverTypeNotEquals != null">
                and A.server_type != #{condition.serverTypeNotEquals,jdbcType=VARCHAR}
            </if>
            <if test="condition.status != null">
                and A.status = #{condition.status,jdbcType=VARCHAR}
            </if>
            <if test="condition.resPoolIds != null">
                and rpr.res_pool_id in
                <foreach item="item" index="index" collection="condition.resPoolIds"
                    open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.idList != null">
                and A.id in
                <foreach item="item" index="index" collection="condition.idList"
                    open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.resPoolId != null">
                and rpr.res_pool_id = #{condition.resPoolId}
            </if>
            <if test="condition.startTime != null">
                and A.start_time = #{condition.startTime}
            </if>
            <if test="condition.endTime != null">
                and A.end_time = #{condition.endTime}
            </if>
            <if test="condition.statusNotEquals != null">
                and A.status != #{condition.statusNotEquals}
            </if>
            <if test="condition.instanceIdNotNull != null">
                and A.instance_id IS NOT NULL
            </if>
            <if test="condition.instanceId != null">
                and A.instance_id = #{condition.instanceId}
            </if>
            <if test="condition.hasPublicId != null">
                and A.public_ip is not null
            </if>
            <!--过滤 已经安装二级云管的主机-->
            AND A.id not in (
            SELECT res_vm_id FROM cloud_platform_component comp where
            1=1
            <!-- 是否过滤已创建
            AND comp.status = 'running'
            -->
            AND comp.res_vm_id is not null
            )
        </trim>
        group by A.ID
    </select>
    <select id="findByAvailablePools"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultMap="BaseResultMap">
        SELECT
        DISTINCT
        smc.CODE_DISPLAY as STATUS_NAME,
        <include refid="Base_Column_List"/>
        FROM
        res_vm A
        LEFT JOIN SYS_M_CODE smc on (smc.CODE_CATEGORY = 'RES_VM_STATUS' and smc.CODE_VALUE =
        A.STATUS )
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.instanceNameLike != null">
                AND A.instance_name like concat('%',#{condition.instanceNameLike}, '%')
            </if>
            <if test="condition.cloudEnvId != null">
                AND A.cloud_env_id = #{condition.cloudEnvId}
            </if>
            <if test="condition.startTime != null">
                and A.start_time = #{condition.startTime}
            </if>
            <if test="condition.endTime != null">
                and A.end_time = #{condition.endTime}
            </if>
        </trim>
    </select>

    <!-- 已关联实例 -->
    <select id="findAllocVm" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultMap="BaseResultMap">
        SELECT
        DISTINCT
        B.CODE_DISPLAY AS status_name,
        IFNULL(C.cloud_env_name,'其他') AS cloud_env_name,
        IFNULL(C.cloud_env_type,'unknown') AS cloud_env_type,
        D.account AS owner_account,
        <include refid="Base_Column_List"/>
        FROM
        res_vm A
        LEFT JOIN SYS_M_CODE B ON (B.CODE_CATEGORY = 'RES_VM_STATUS' AND B.CODE_VALUE = A.STATUS)
        LEFT JOIN cloud_env C ON A.cloud_env_id = C.id
        LEFT JOIN sys_m_user D ON A.owner_id = D.user_sid
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.statusList != null">
                AND A.status NOT IN
                <foreach collection="condition.statusList" item="status" index="index" open="("
                    separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="condition.cloudEnvId != null">
                AND A.cloud_env_id = #{condition.cloudEnvId}
            </if>
            <if test="condition.nameLike != null">
                AND A.instance_name LIKE concat('%',#{condition.nameLike}, '%')
            </if>
            <if test="condition.projectId != null">
                AND A.org_sid = #{condition.projectId}
            </if>
            <if test="condition.isRecycle != null">
                AND A.is_recycle = #{condition.isRecycle}
            </if>
            <if test="condition.ipLike != null">
                AND (A.public_ip LIKE concat('%',#{condition.ipLike}, '%')
                OR A.inner_ip LIKE concat('%',#{condition.ipLike}, '%'))
            </if>
            <if test="condition.df != null">
                ${condition.df}
            </if>
        </trim>
        GROUP BY A.ID
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <select id="findAllocVmForVO" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultType="cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResVmVO">
        SELECT
        B.CODE_DISPLAY AS statusName,
        A.cloud_env_id AS cloudEnvId,
        IFNULL(C.cloud_env_name,'其他') AS cloudEnvName,
        IFNULL(C.cloud_env_type,'unknown') AS cloudEnvType,
        D.account AS ownerAccount,
        A.server_type AS serverType,
        A.inner_ip AS innerIp,
        A.public_ip AS publicIp,
        A.instance_name AS instanceName,
        A.status AS status,
        A.id AS id,
        F.id AS resPoolId,
        IFNULL(G.mode, E.mode) AS mode,
        (A.org_sid = ACCOUNT.org_sid or A.org_sid != #{condition.orgSid}) as readOnly,
        org.org_name as originOrgName,
        A.org_sid orgSid,
        ACCOUNT.platform_component_id as platformComponentId,
        A.cpu,
        A.memory,
        A.physical_host_pool_id
        FROM
        res_vm A
        LEFT JOIN sys_m_code B ON (B.code_category = 'RES_VM_STATUS' AND B.code_value = A.status)
        LEFT JOIN cloud_env C ON A.cloud_env_id = C.id
        LEFT JOIN cloud_env_account ACCOUNT ON C.cloud_env_account_id = ACCOUNT.id
        LEFT JOIN sys_m_user D ON A.owner_id = D.user_sid
        LEFT JOIN cloud_env_alloc E ON A.cloud_env_id = E.cloud_env_id AND A.org_sid = E.alloc_target_id
        LEFT JOIN res_pool F ON A.cloud_env_id = F.cloud_env_id AND A.zone = F.name
        LEFT JOIN res_pool_alloc G ON F.id = G.res_pool_id AND A.org_sid = G.alloc_target_id
        LEFT JOIN sys_m_org org ON ACCOUNT.org_sid = org.org_sid
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.statusList != null">
                AND A.status NOT IN
                <foreach collection="condition.statusList" item="status" index="index" open="("
                    separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="condition.cloudEnvId != null">
                AND A.cloud_env_id = #{condition.cloudEnvId}
            </if>
            <if test="condition.nameLike != null">
                AND A.instance_name LIKE concat('%',#{condition.nameLike}, '%')
            </if>
            <if test="condition.isRecycle != null">
                AND A.is_recycle = #{condition.isRecycle}
            </if>
            <if test="condition.ownerAccount != null">
                AND D.account LIKE #{condition.ownerAccount}
            </if>
            <if test="condition.ipLike != null">
                AND (A.public_ip LIKE concat('%',#{condition.ipLike}, '%')
                OR A.inner_ip LIKE concat('%',#{condition.ipLike}, '%'))
            </if>
            <if test="condition.df != null">
                ${condition.df}
            </if>
            <if test="condition.cloudEnvTypeNotIn != null">
                AND C.cloud_env_type NOT IN
                <foreach collection="condition.cloudEnvTypeNotIn" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.vmCategory != null">
                AND A.vm_category = #{condition.vmCategory}
            </if>
        </trim>
        GROUP BY A.id
        <if test="orderByClause != null">
            ORDER BY ${orderByClause}
        </if>
    </select>
    <select id="countAllocVmByDf" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultType="integer">
        SELECT
        count(T.ID)
        FROM
        res_vm T
        RIGHT JOIN cloud_env C ON T.cloud_env_id = C.id
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.statusList != null">
                AND T.status NOT IN
                <foreach collection="condition.statusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="condition.cloudEnvTypeNotIn != null">
                AND C.cloud_env_type NOT IN
                <foreach collection="condition.cloudEnvTypeNotIn" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.isRecycle != null">
                AND T.is_recycle = #{condition.isRecycle}
            </if>
            <if test="condition.df != null">
                ${condition.df}
            </if>
            <if test="condition.vmCategory != null">
                AND T.vm_category = #{condition.vmCategory}
            </if>
        </trim>
    </select>


    <!-- 未关联实例 -->
    <select id="findNotAllocVm" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultMap="BaseResultMap">
        SELECT
        DISTINCT
        B.CODE_DISPLAY as STATUS_NAME,
        IFNULL(C.cloud_env_name, '其他') as cloud_env_name,
        IFNULL(C.cloud_env_type, 'unknown') as cloud_env_type,
        D.account as owner_account,
        org.org_name as owner_org_name,
        Z.`NAME` AS zone_name,
        C.platform_component_id,
        <include refid="Base_Column_List"/>
        FROM
        res_vm A
        LEFT JOIN SYS_M_CODE B on (B.CODE_CATEGORY = 'RES_VM_STATUS' and B.CODE_VALUE = A.STATUS)
        INNER JOIN (
        select ce.id,ce.cloud_env_name,ce.cloud_env_type,cea.platform_component_id
        from cloud_env ce
        INNER JOIN cloud_env_account cea ON ce.cloud_env_account_id = cea.id
        ) C on A.cloud_env_id = C.id
        LEFT JOIN sys_m_user D on A.owner_id = D.user_sid
        LEFT JOIN cloud_physical_host_pool CPHP ON CPHP.id = A.physical_host_pool_id
        INNER JOIN SYS_M_ORG org on org.org_sid = A.org_sid
        LEFT JOIN
        (SELECT rz.name AS NAME,rz.uuid AS UUID FROM res_vm AS A LEFT JOIN res_zone AS rz ON (rz.uuid = A.zone AND
        A.cloud_env_id =rz.cloud_env_id) UNION
        SELECT rp.name AS UUID,rp.name AS NAME FROM res_pool AS rp LEFT JOIN res_vm A ON (rp.cloud_env_id =
        A.cloud_env_id AND rp.NAME = A.zone)) Z on Z.uuid = A.zone
        WHERE EXISTS ( SELECT 1 FROM sys_m_org WHERE (org_sid = #{condition.orgSid} OR tree_path LIKE
        concat(#{condition.treePath}, '%')) AND org_sid = A.org_sid )
        AND (EXISTS(SELECT 1
        FROM cloud_env_alloc cea
        WHERE cea.alloc_target_id = #{condition.projectId} AND cea.cloud_env_id = C.id AND c.cloud_env_type != 'VMware')
        OR EXISTS(SELECT 1
        FROM res_pool rp
        INNER JOIN res_pool_alloc rpa ON rpa.res_pool_id = rp.id
        WHERE alloc_target_id = #{condition.projectId}
        AND rp.NAME = a.zone))
        AND A.org_sid != #{condition.projectId}
        <if test="condition.statusList != null">
            and A.status not in
            <foreach collection="condition.statusList" item="status" index="index" open="("
                separator="," close=")">
                #{status}
            </foreach>
        </if>
        <if test="condition.cloudEnvId != null">
            AND A.cloud_env_id = #{condition.cloudEnvId}
        </if>
        <if test="condition.nameLike != null">
            AND A.instance_name like concat('%',#{condition.nameLike}, '%')
        </if>
        <if test="condition.isRecycle != null">
            AND A.is_recycle = #{condition.isRecycle}
        </if>
        <if test="condition.ipLike != null">
            AND (A.public_ip like concat('%',#{condition.ipLike}, '%')
            OR A.inner_ip like concat('%',#{condition.ipLike}, '%'))
        </if>
        <if test="condition.cloudEnvTypeNotIn != null">
            AND C.cloud_env_type NOT IN
            <foreach collection="condition.cloudEnvTypeNotIn" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="condition.vmCategory != null">
            AND A.vm_category = #{condition.vmCategory}
        </if>
        <if test="condition.allocStatus != null">
            AND CPHP.alloc_status = #{condition.allocStatus}
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>

    <select id="selectVoterList" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultMap="BaseResultMap">
        select
        B.cloud_env_name as CLOUD_ENV_NAME,
        B.cloud_env_type as CLOUD_ENV_TYPE,
        B.cloud_env_category as CLOUD_ENV_CATEGORY,
        C.CODE_DISPLAY as STATUS_NAME,
        D.CODE_DISPLAY as CLOUD_ENV_TYPE_NAME,
        E.CODE_DISPLAY as INSTANCE_CHARGE_TYPE_NAME,
        F.CODE_DISPLAY as MANAGE_STATUS_NAME,
        IFNULL(J.keypair_name, A.keypair_name) as keypairName,
        IFNULL(K.name,'--') as CLOUD_DEPLOYMENT_NAME,
        L.name as SERVER_TEMPLATE_NAME,
        smu.EMAIL as lock_user_email,
        p.vote_time as last_vote_time,
        p.elastic_mode as last_vote_result,
        <include refid="Base_Column_List"/>
        , A.ORIGIN_PARAM
        from res_vm A
        LEFT JOIN cloud_env B on (B.id = A.CLOUD_ENV_ID)
        LEFT JOIN SYS_M_CODE C on (C.CODE_CATEGORY = 'RES_VM_STATUS' and C.CODE_VALUE = A.STATUS
        )
        LEFT JOIN SYS_M_CODE D on (D.CODE_CATEGORY = 'CLOUD_ENV_TYPE' and D.CODE_VALUE =
        B.CLOUD_ENV_TYPE )
        LEFT JOIN SYS_M_CODE E on (E.CODE_CATEGORY = 'ALIYUN_CHARGE_TYPE' and E.CODE_VALUE =
        A.INSTANCE_CHARGE_TYPE )
        LEFT JOIN SYS_M_CODE F on (F.CODE_CATEGORY = 'CLOUD_HOST_MANAGE_STATUS' and F.CODE_VALUE =
        A.MANAGE_STATUS )
        LEFT JOIN cloud_tag_ref H on (A.ID = H.obj_id and H.obj_type = 'host ')
        LEFT JOIN cloud_tag I on (H.tag_id= I.tag_id)
        LEFT JOIN cloud_keypair J on (A.KEYPAIR_ID= J.id)
        LEFT JOIN cloud_deployment_vm SDRH on (SDRH.res_vm_id = A.ID)
        LEFT JOIN cloud_deployment K on (SDRH.DEPLOYMENT_ID = K.id)
        LEFT JOIN server_template L on (A.server_template_id = L.id)
        LEFT JOIN sys_m_user smu on A.LOCK_USER = smu.USER_SID
        LEFT JOIN (
        SELECT
        ev.elastic_tag,
        ev.res_vm_id,
        ev.vote_time,
        ev.elastic_mode
        FROM
        elastic_vote ev
        INNER JOIN (
        SELECT
        ev2.elastic_tag,
        max(ev2.vote_time) AS vote_time,
        ev2.res_vm_id
        FROM
        elastic_vote ev2
        WHERE
        ev2.status = #{condition.voteStatus}
        AND
        ev2.elastic_tag = #{condition.elasticTag}
        GROUP BY
        ev2.elastic_tag,
        ev2.res_vm_id
        ) o ON ev.elastic_tag = o.elastic_tag
        AND ev.vote_time = o.vote_time
        AND ev.res_vm_id = o.res_vm_id
        ) p ON p.res_vm_id = a.id
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.elasticGroupId != null">
                and A.elastic_group_id= #{condition.elasticGroupId,jdbcType=BIGINT}
            </if>
            <if test="condition.serverType != null">
                and A.server_type = #{condition.serverType,jdbcType=VARCHAR}
            </if>
            <if test="condition.serverTypeNotEquals != null">
                and A.server_type != #{condition.serverTypeNotEquals,jdbcType=VARCHAR}
            </if>
            <if test="condition.statusNotEquals != null">
                and A.status != #{condition.statusNotEquals,jdbcType=VARCHAR}
            </if>
            <if test="condition.status != null">
                and A.status = #{condition.status,jdbcType=VARCHAR}
            </if>
            <if test="condition.startTime != null">
                and A.start_time = #{condition.startTime}
            </if>
            <if test="condition.endTime != null">
                and A.end_time = #{condition.endTime}
            </if>
        </trim>
        group by A.id
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from res_vm
        where ID = #{id,jdbcType=VARCHAR}
    </delete>
    <delete id="deleteByExample" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        delete from res_vm
        <if test="_parameter != null">
            <trim prefix="where" prefixOverrides="and|or">
                <if test="condition.resVmIds != null">
                    and id in
                    <foreach collection="condition.resVmIds" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </if>
            </trim>
        </if>
    </delete>
    <insert id="insert" parameterType="cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResVm"
        keyProperty="id"
        useGeneratedKeys="true">
        insert into res_vm (ID, CLOUD_ENV_ID, ALLOCATE_RES_HOST_SID,
        INSTANCE_ID,
        INSTANCE_NAME, MANAGEMENT_ACCOUNT, MANAGEMEN_PASSWORD,
        IMAGE_ID, IMAGE_TYPE, PLATFORM, CPU, MEMORY,
        DESCRIPTION, INSTANCE_TYPE, OWNER_ID,INSTANCE_CHARGE_TYPE,
        INTERNET_CHARGE_TYPE, INTERNET_MAX_BANDWIDTH_OUT,
        INTERNET_MAX_BANDWIDTH_IN, INNER_IP, PUBLIC_IP, SSH_PORT,
        INSTANCE_NETWORK_TYPE, ZONE,
        REGION, MANAGE_STATUS, STATUS, STATUS_INFO, CREATED_BY,
        CREATED_DT, UPDATED_BY, UPDATED_DT, REMOTE_LOGIN_TYPE, KEYPAIR_ID,
        FLOATING_IP_POOL_NAME, FLOATING_IP_POOL_ID, SERVER_TEMPLATE_ID,
        VERSION, SERVER_TYPE
        , use_memory_size, use_store_size, provision_storage, cpu_usage, use_cpu_ghz,
        disk_usage,org_sid,
        keypair_name,
        LOCK_USER, LOCK_STATUS, start_time, end_time, stopped_mode,
        HAS_VM_TOOLS, HOST_NAME, ALLOCATE_RES_VC_SID
        )
        values (#{id,jdbcType=VARCHAR},
        #{cloudEnvId,jdbcType=BIGINT},#{allocateResHostSid,jdbcType=VARCHAR},
        #{instanceId,jdbcType=VARCHAR},
        #{instanceName,jdbcType=VARCHAR}, #{managementAccount,jdbcType=VARCHAR},
        #{managemenPassword,jdbcType=VARCHAR},
        #{imageId,jdbcType=VARCHAR},#{imageType,jdbcType=VARCHAR},#{platform,jdbcType=VARCHAR}, #{cpu,jdbcType=TINYINT},
        #{memory,jdbcType=INTEGER},
        #{description,jdbcType=VARCHAR}, #{instanceType,jdbcType=VARCHAR},
        #{ownerId,jdbcType=VARCHAR},#{instanceChargeType,jdbcType=VARCHAR},
        #{internetChargeType,jdbcType=VARCHAR}, #{internetMaxBandwidthOut,jdbcType=INTEGER},
        #{internetMaxBandwidthIn,jdbcType=INTEGER}, #{innerIp,jdbcType=VARCHAR},
        #{publicIp,jdbcType=VARCHAR},#{sshPort,jdbcType=VARCHAR},
        #{instanceNetworkType,jdbcType=VARCHAR}, #{zone,jdbcType=VARCHAR},
        #{region,jdbcType=VARCHAR}
        ,#{manageStatus,jdbcType=VARCHAR},#{status,jdbcType=VARCHAR},#{statusInfo,jdbcType=VARCHAR},
        #{createdBy,jdbcType=VARCHAR},
        #{createdDt,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR},
        #{updatedDt,jdbcType=TIMESTAMP},
        #{remoteLoginType,jdbcType=VARCHAR}, #{keypairId,jdbcType=INTEGER},
        #{floatingIpPoolName,jdbcType=VARCHAR},
        #{floatingIpPoolId,jdbcType=VARCHAR},#{serverTemplateId,jdbcType=BIGINT},
        #{version,jdbcType=BIGINT},
        #{serverType,jdbcType=BIGINT}
        , #{useMemorySize,jdbcType=DOUBLE}, #{useStoreSize,jdbcType=INTEGER},
        #{provisionStorage,jdbcType=INTEGER}
        , #{cpuUsage,jdbcType=INTEGER}, #{useCpuGhz,jdbcType=DOUBLE}, #{diskUsage,jdbcType=DOUBLE},
        #{orgSid,jdbcType=BIGINT}
        , #{keypairName,jdbcType=VARCHAR}, #{lockUser,jdbcType=BIGINT},
        #{lockStatus,jdbcType=VARCHAR},
        #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP},
        #{stoppedMode,jdbcType=VARCHAR}, #{hasVmTools,jdbcType=TINYINT}, #{hostName,jdbcType=VARCHAR},
        #{allocateResVcSid,jdbcType=VARCHAR}
        )
    </insert>
    <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id"
        parameterType="cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResVm">
        insert into res_vm
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID,
            </if>
            <if test="cloudEnvId != null">
                CLOUD_ENV_ID,
            </if>
            <if test="allocateResHostSid != null">
                ALLOCATE_RES_HOST_SID,
            </if>
            <if test="instanceId != null">
                INSTANCE_ID,
            </if>
            <if test="instanceName != null">
                INSTANCE_NAME,
            </if>
            <if test="managementAccount != null">
                MANAGEMENT_ACCOUNT,
            </if>
            <if test="managemenPassword != null">
                MANAGEMEN_PASSWORD,
            </if>
            <if test="imageId != null">
                IMAGE_ID,
            </if>
            <if test="imageType != null">
                IMAGE_TYPE,
            </if>
            <if test="platform != null">
                PLATFORM,
            </if>
            <if test="cpu != null">
                CPU,
            </if>
            <if test="memory != null">
                MEMORY,
            </if>
            <if test="description != null">
                DESCRIPTION,
            </if>
            <if test="instanceType != null">
                INSTANCE_TYPE,
            </if>
            <if test="ownerId != null">
                OWNER_ID,
            </if>
            <if test="instanceChargeType != null">
                INSTANCE_CHARGE_TYPE,
            </if>
            <if test="internetChargeType != null">
                INTERNET_CHARGE_TYPE,
            </if>
            <if test="internetMaxBandwidthOut != null">
                INTERNET_MAX_BANDWIDTH_OUT,
            </if>
            <if test="internetMaxBandwidthIn != null">
                INTERNET_MAX_BANDWIDTH_IN,
            </if>
            <if test="innerIp != null">
                INNER_IP,
            </if>
            <if test="publicIp != null">
                PUBLIC_IP,
            </if>
            <if test="sshPort != null">
                SSH_PORT,
            </if>
            <if test="instanceNetworkType != null">
                INSTANCE_NETWORK_TYPE,
            </if>
            <if test="zone != null">
                ZONE,
            </if>
            <if test="region != null">
                REGION,
            </if>

            <if test="manageStatus != null">
                MANAGE_STATUS,
            </if>
            <if test="status != null">
                STATUS,
            </if>
            <if test="statusInfo != null">
                STATUS_INFO,
            </if>
            <if test="createdBy != null">
                CREATED_BY,
            </if>
            <if test="createdDt != null">
                CREATED_DT,
            </if>
            <if test="updatedBy != null">
                UPDATED_BY,
            </if>
            <if test="updatedDt != null">
                UPDATED_DT,
            </if>
            <if test="version != null">
                VERSION,
            </if>
            <if test="originParam != null">
                ORIGIN_PARAM,
            </if>
            <if test="osName != null">
                OS_NAME,
            </if>
            <if test="remoteLoginType != null">
                REMOTE_LOGIN_TYPE,
            </if>
            <if test="keypairId != null">
                KEYPAIR_ID,
            </if>
            <if test="keypairName != null">
                keypair_name,
            </if>
            <if test="floatingIpPoolName != null">
                FLOATING_IP_POOL_NAME,
            </if>
            <if test="floatingIpPoolId != null">
                FLOATING_IP_POOL_ID,
            </if>
            <if test="serverTemplateId != null">
                SERVER_TEMPLATE_ID,
            </if>
            <if test="serverType != null">
                SERVER_TYPE,
            </if>
            <if test="useMemorySize != null">
                use_memory_size,
            </if>
            <if test="useStoreSize != null">
                use_store_size,
            </if>
            <if test="provisionStorage != null">
                provision_storage,
            </if>
            <if test="cpuUsage != null">
                cpu_usage,
            </if>
            <if test="useCpuGhz != null">
                use_cpu_ghz,
            </if>
            <if test="diskUsage != null">
                disk_usage,
            </if>
            <if test="orgSid != null">
                org_sid,
            </if>
            <if test="lockUser != null">
                lock_user,
            </if>
            <if test="lockStatus != null">
                lock_status,
            </if>
            <if test="elasticGroupId != null">
                elastic_group_id,
            </if>
            <if test="elasticHistoryId != null">
                elastic_history_id,
            </if>
            <if test="allocFlag != null">
                alloc_flag,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="osCategory != null">
                os_category,
            </if>
            <if test="stoppedMode != null">
                stopped_mode,
            </if>
            <if test="cloneSourceId != null">
                clone_source_id,
            </if>
            <if test="urn != null">
                URN,
            </if>
            <if test="uri != null">
                URI,
            </if>
            <if test="hasVmTools != null">
                has_vm_tools,
            </if>
            <if test="hostName != null">
                host_name,
            </if>
            <if test="extraType != null">
                extra_type,
            </if>
            <if test="createdOrgSid != null">
                created_org_sid,
            </if>
            <if test="physicalHostPoolId != null">
                physical_host_pool_id,
            </if>
            <if test="installSnmp != null">
                install_snmp,
            </if>
            <if test="allocateResVcSid != null">
                ALLOCATE_RES_VC_SID,
            </if>
            <if test="vmCategory != null">
                vm_category,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="cloudEnvId != null">
                #{cloudEnvId,jdbcType=BIGINT},
            </if>
            <if test="allocateResHostSid != null">
                #{allocateResHostSid,jdbcType=BIGINT},
            </if>
            <if test="serviceDeployInstId != null">
                #{serviceDeployInstId,jdbcType=BIGINT},
            </if>
            <if test="instanceId != null">
                #{instanceId,jdbcType=VARCHAR},
            </if>
            <if test="instanceName != null">
                #{instanceName,jdbcType=VARCHAR},
            </if>
            <if test="managementAccount != null">
                #{managementAccount,jdbcType=VARCHAR},
            </if>
            <if test="managemenPassword != null">
                #{managemenPassword,jdbcType=VARCHAR},
            </if>
            <if test="imageId != null">
                #{imageId,jdbcType=VARCHAR},
            </if>
            <if test="imageType != null">
                #{imageType,jdbcType=VARCHAR},
            </if>
            <if test="platform != null">
                #{platform,jdbcType=VARCHAR},
            </if>
            <if test="cpu != null">
                #{cpu,jdbcType=TINYINT},
            </if>
            <if test="memory != null">
                #{memory,jdbcType=INTEGER},
            </if>
            <if test="description != null">
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="instanceType != null">
                #{instanceType,jdbcType=VARCHAR},
            </if>
            <if test="ownerId != null">
                #{ownerId,jdbcType=VARCHAR},
            </if>
            <if test="instanceChargeType != null">
                #{instanceChargeType,jdbcType=VARCHAR},
            </if>
            <if test="internetChargeType != null">
                #{internetChargeType,jdbcType=VARCHAR},
            </if>
            <if test="internetMaxBandwidthOut != null">
                #{internetMaxBandwidthOut,jdbcType=INTEGER},
            </if>
            <if test="internetMaxBandwidthIn != null">
                #{internetMaxBandwidthIn,jdbcType=INTEGER},
            </if>
            <if test="innerIp != null">
                #{innerIp,jdbcType=VARCHAR},
            </if>
            <if test="publicIp != null">
                #{publicIp,jdbcType=VARCHAR},
            </if>
            <if test="sshPort != null">
                #{sshPort,jdbcType=VARCHAR},
            </if>
            <if test="instanceNetworkType != null">
                #{instanceNetworkType,jdbcType=VARCHAR},
            </if>
            <if test="zone != null">
                #{zone,jdbcType=VARCHAR},
            </if>
            <if test="region != null">
                #{region,jdbcType=VARCHAR},
            </if>

            <if test="manageStatus != null">
                #{manageStatus,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="statusInfo != null">
                #{statusInfo,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDt != null">
                #{createdDt,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDt != null">
                #{updatedDt,jdbcType=TIMESTAMP},
            </if>
            <if test="version != null">
                #{version,jdbcType=BIGINT},
            </if>
            <if test="originParam != null">
                #{originParam,jdbcType=VARCHAR},
            </if>
            <if test="osName != null">
                #{osName,jdbcType=VARCHAR},
            </if>
            <if test="remoteLoginType != null">
                #{remoteLoginType,jdbcType=VARCHAR},
            </if>
            <if test="keypairId != null">
                #{keypairId,jdbcType=INTEGER},
            </if>
            <if test="keypairName != null">
                #{keypairName,jdbcType=INTEGER},
            </if>
            <if test="floatingIpPoolName != null">
                #{floatingIpPoolName,jdbcType=VARCHAR},
            </if>
            <if test="floatingIpPoolId != null">
                #{floatingIpPoolId,jdbcType=VARCHAR},
            </if>
            <if test="serverTemplateId != null">
                #{serverTemplateId,jdbcType=BIGINT},
            </if>
            <if test="serverType != null">
                #{serverType,jdbcType=VARCHAR},
            </if>
            <if test="useMemorySize != null">
                #{useMemorySize},
            </if>
            <if test="useStoreSize != null">
                #{useStoreSize},
            </if>
            <if test="provisionStorage != null">
                #{provisionStorage},
            </if>
            <if test="cpuUsage != null">
                #{cpuUsage},
            </if>
            <if test="useCpuGhz != null">
                #{useCpuGhz},
            </if>
            <if test="diskUsage != null">
                #{diskUsage},
            </if>
            <if test="orgSid != null">
                #{orgSid},
            </if>
            <if test="lockUser != null">
                #{lockUser},
            </if>
            <if test="lockStatus != null">
                #{lockStatus},
            </if>
            <if test="elasticGroupId != null">
                #{elasticGroupId},
            </if>
            <if test="elasticHistoryId != null">
                #{elasticHistoryId},
            </if>
            <if test="allocFlag != null">
                #{allocFlag},
            </if>
            <if test="startTime != null">
                #{startTime},
            </if>
            <if test="endTime != null">
                #{endTime},
            </if>
            <if test="osCategory != null">
                #{osCategory},
            </if>
            <if test="stoppedMode != null">
                #{stoppedMode},
            </if>
            <if test="cloneSourceId != null">
                #{cloneSourceId},
            </if>
            <if test="urn != null">
                #{urn},
            </if>
            <if test="uri != null">
                #{uri},
            </if>
            <if test="hasVmTools != null">
                #{hasVmTools},
            </if>
            <if test="hostName != null">
                #{hostName},
            </if>
            <if test="extraType != null">
                #{extraType},
            </if>
            <if test="createdOrgSid != null">
                #{createdOrgSid},
            </if>
            <if test="physicalHostPoolId != null">
                #{physicalHostPoolId},
            </if>
            <if test="installSnmp != null">
                #{installSnmp},
            </if>
            <if test="allocateResVcSid != null">
                #{allocateResVcSid},
            </if>
            <if test="vmCategory != null">
                #{vmCategory},
            </if>
        </trim>
    </insert>
    <select id="countByParams" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultType="java.lang.Integer">
        select count(A.ID) from res_vm A
        LEFT JOIN cloud_env B on A.cloud_env_id = B.id
        <if test="_parameter != null">
            <include refid="Base_Example_Where_Clause"/>
        </if>
    </select>

    <select id="countSelfServiceVms" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultType="java.lang.Integer">
        SELECT
        count(DISTINCT A.ID)
        FROM
        res_vm A
        LEFT JOIN cloud_deployment_vm cdv ON cdv.res_vm_id = A.ID
        LEFT JOIN cloud_deployment cd ON cdv.deployment_id = cd.id
        LEFT JOIN cloud_env B ON B.id = A.CLOUD_ENV_ID
        LEFT JOIN cloud_env_account cea ON B.cloud_env_account_id = cea.id
        LEFT JOIN SERVER_TEMPLATE M ON A.SERVER_TEMPLATE_ID = M.ID
        LEFT JOIN res_image N ON A.image_id = N.image_id
        AND (
        ( A.cloud_env_id = N.cloud_env_id AND N.cloud_env_id != - 1 )
        OR ( N.region = B.region AND N.cloud_env_type = B.cloud_env_type AND N.cloud_env_id = - 1 )
        )
        LEFT JOIN res_snapshot s ON A.IMAGE_ID = s.uuid
        AND A.CLOUD_ENV_ID = s.CLOUD_ENV_ID
        LEFT JOIN CLOUD_KEYPAIR O ON A.KEYPAIR_ID = O.ID
        LEFT JOIN sys_m_user smu ON smu.USER_SID = A.LOCK_USER
        LEFT JOIN res_host RH ON A.allocate_res_host_sid = RH.res_host_sid
        LEFT JOIN sys_m_user smur ON smur.user_sid = A.owner_id
        LEFT JOIN service_order_basic_resource_relation SOBRR ON SOBRR.resource_id = A.id
        LEFT JOIN service_order SO ON SO.id = SOBRR.service_order_id
        LEFT JOIN sf_service_inst_target SSIT ON SSIT.target_id = A.id
        LEFT JOIN sf_service_deploy_inst SSDI ON SSDI.id = SSIT.sf_service_inst_id
        LEFT JOIN service_order SO2 ON SO2.id = SSDI.service_order_id
        LEFT JOIN service_order_detail SOD ON SOD.order_id = SOBRR.service_order_id
        LEFT JOIN sf_service_category SSC ON SSC.id = SOD.service_id
        LEFT JOIN sf_service_category SSC2 ON SSC2.id = SSDI.service_id
        WHERE
        ( A.server_type != 'elastic' OR A.server_type IS NULL )
        AND A.STATUS != 'deleted'
        AND A.is_recycle = false
        AND !(A.created_org_sid = A.org_sid and SO.id is null and SO2.id is null AND cd.NAME IS NULL)
        AND (SSC.resource_visible = 1 or SSC.resource_visible is null)
        AND (SSC2.resource_visible = 1 or SSC2.resource_visible is null)
        <if test="condition.extraType != null">
            <choose>
                <when test="condition.extraType == 'UnBareMetal'">
                    and (A.extra_type != 'BareMetal' or A.extra_type is null)
                </when>
                <otherwise>
                    and A.extra_type = #{condition.extraType}
                </otherwise>
            </choose>
        </if>
        <if test="condition.vmCategory != null">
            and A.vm_category = #{condition.vmCategory}
        </if>
    </select>

    <select id="countByExample" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultType="java.lang.Integer">
        select count(DISTINCT(A.ID)) from res_vm A
        LEFT JOIN cloud_env B on (B.id = A.CLOUD_ENV_ID)
        LEFT JOIN cloud_tag_ref H on (A.ID = H.obj_id and H.obj_type = 'host ')
        LEFT JOIN cloud_tag I on (H.tag_id= I.tag_id)
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <select id="countByBaseByParams"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultType="java.lang.Integer">
        select count(distinct A.id)
        from res_vm A
        LEFT JOIN cloud_env b ON A.cloud_env_id = b.id
        LEFT JOIN SYS_M_CODE C ON C.CODE_CATEGORY = 'CLOUD_ENV_TYPE' and C.CODE_VALUE = B.CLOUD_ENV_TYPE
        LEFT JOIN SYS_M_CODE D on (D.CODE_CATEGORY = 'RES_VM_STATUS' and D.CODE_VALUE = A.STATUS )
        LEFT JOIN SYS_M_USER E ON A.lock_user = E.USER_SID
        LEFT JOIN res_host RH ON A.allocate_res_host_sid = RH.res_host_sid
        LEFT JOIN server_template S ON A.server_template_id = S.id
        LEFT JOIN res_image N on A.image_id = N.image_id
        LEFT JOIN cloud_env M on A.cloud_env_id = M.id
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update res_vm A
        <set>
            <if test="record.id != null">
                A.ID = #{record.id,jdbcType=VARCHAR},
            </if>
            <if test="record.cloudEnvId != null">
                A.CLOUD_ENV_ID = #{record.cloudEnvId,jdbcType=BIGINT},
            </if>
            <if test="record.allocateResHostSid != null">
                A.ALLOCATE_RES_HOST_SID = #{record.allocateResHostSid,jdbcType=VARCHAR},
            </if>
            <if test="record.instanceId != null">
                A.INSTANCE_ID = #{record.instanceId,jdbcType=VARCHAR},
            </if>
            <if test="record.instanceName != null">
                A.INSTANCE_NAME = #{record.instanceName,jdbcType=VARCHAR},
            </if>
            <if test="record.managementAccount != null">
                A.MANAGEMENT_ACCOUNT = #{record.managementAccount,jdbcType=VARCHAR},
            </if>
            <if test="record.managemenPassword != null">
                A.MANAGEMEN_PASSWORD = #{record.managemenPassword,jdbcType=VARCHAR},
            </if>
            <if test="record.imageId != null">
                A.IMAGE_ID = #{record.imageId,jdbcType=VARCHAR},
            </if>
            <if test="record.platform != null">
                A.PLATFORM = #{record.platform,jdbcType=VARCHAR},
            </if>
            <if test="record.cpu != null">
                A.CPU = #{record.cpu,jdbcType=TINYINT},
            </if>
            <if test="record.memory != null">
                A.MEMORY = #{record.memory,jdbcType=INTEGER},
            </if>
            <if test="record.description != null">
                A.DESCRIPTION = #{record.description,jdbcType=VARCHAR},
            </if>
            <if test="record.instanceType != null">
                A.INSTANCE_TYPE = #{record.instanceType,jdbcType=VARCHAR},
            </if>
            <if test="record.ownerId != null">
                A.OWNER_ID = #{record.ownerId,jdbcType=VARCHAR},
            </if>
            <if test="record.instanceChargeType != null">
                A.INSTANCE_CHARGE_TYPE = #{record.instanceChargeType,jdbcType=VARCHAR},
            </if>
            <if test="record.internetChargeType != null">
                A.INTERNET_CHARGE_TYPE = #{record.internetChargeType,jdbcType=VARCHAR},
            </if>
            <if test="record.internetMaxBandwidthOut != null">
                A.INTERNET_MAX_BANDWIDTH_OUT = #{record.internetMaxBandwidthOut,jdbcType=INTEGER},
            </if>
            <if test="record.internetMaxBandwidthIn != null">
                A.INTERNET_MAX_BANDWIDTH_IN = #{record.internetMaxBandwidthIn,jdbcType=INTEGER},
            </if>
            <if test="record.innerIp != null">
                A.INNER_IP = #{record.innerIp,jdbcType=VARCHAR},
            </if>
            <if test="record.publicIp != null">
                A.PUBLIC_IP = #{record.publicIp,jdbcType=VARCHAR},
            </if>
            <if test="record.sshPort != null">
                A.SSH_PORT = #{record.sshPort,jdbcType=VARCHAR},
            </if>
            <if test="record.instanceNetworkType != null">
                A.INSTANCE_NETWORK_TYPE = #{record.instanceNetworkType,jdbcType=VARCHAR},
            </if>
            <if test="record.zone != null">
                A.ZONE = #{record.zone,jdbcType=VARCHAR},
            </if>
            <if test="record.region != null">
                A.REGION = #{record.region,jdbcType=VARCHAR},
            </if>

            <if test="record.manageStatus != null">
                A.MANAGE_STATUS = #{record.manageStatus,jdbcType=VARCHAR},
            </if>
            <if test="record.status != null">
                A.STATUS = #{record.status,jdbcType=VARCHAR},
            </if>
            <if test="record.statusInfo != null">
                A.STATUS_INFO = #{record.statusInfo,jdbcType=VARCHAR},
            </if>
            <if test="record.osName != null">
                A.OS_NAME = #{record.osName,jdbcType=VARCHAR},
            </if>
            <if test="record.createdBy != null">
                A.CREATED_BY = #{record.createdBy,jdbcType=VARCHAR},
            </if>
            <if test="record.createdDt != null">
                A.CREATED_DT = #{record.createdDt,jdbcType=TIMESTAMP},
            </if>
            <if test="record.updatedBy != null">
                A.UPDATED_BY = #{record.updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="record.updatedDt != null">
                A.UPDATED_DT = #{record.updatedDt,jdbcType=TIMESTAMP},
            </if>
            <if test="record.version != null">
                A.VERSION = #{record.version,jdbcType=BIGINT},
            </if>
            <if test="record.remoteLoginType != null">
                A.REMOTE_LOGIN_TYPE = #{record.remoteLoginType,jdbcType=VARCHAR},
            </if>
            <if test="record.keypairId != null">
                A.KEYPAIR_ID = #{record.keypairId,jdbcType=INTEGER},
            </if>
            <if test="record.keypairName != null">
                A.keypair_name = #{record.keypairName,jdbcType=VARCHAR},
            </if>
            <if test="record.floatingIpPoolName != null">
                A.FLOATING_IP_POOL_NAME = #{record.floatingIpPoolName,jdbcType=VARCHAR},
            </if>
            <if test="record.floatingIpPoolId != null">
                A.FLOATING_IP_POOL_ID = #{record.floatingIpPoolId,jdbcType=VARCHAR},
            </if>
            <if test="record.serverType != null">
                A.SERVER_TYPE = #{record.serverType,jdbcType=VARCHAR},
            </if>
            <if test="record.serverTemplateId != null">
                A.SERVER_TEMPLATE_ID = #{record.serverTemplateId,jdbcType=BIGINT},
            </if>
            <if test="record.useMemorySize != null">
                A.use_memory_size = #{record.useMemorySize},
            </if>
            <if test="record.useStoreSize != null">
                A.use_store_size = #{record.useStoreSize},
            </if>
            <if test="record.provisionStorage != null">
                A.provision_storage = #{record.provisionStorage},
            </if>
            <if test="record.cpuUsage != null">
                A.cpu_usage = #{record.cpuUsage},
            </if>
            <if test="record.useCpuGhz != null">
                A.use_cpu_ghz = #{record.useCpuGhz},
            </if>
            <if test="record.diskUsage != null">
                A.disk_usage = #{record.diskUsage},
            </if>
            <if test="record.orgSid != null">
                A.org_sid = #{record.orgSid},
            </if>
            <if test="record.lockUser != null">
                A.lock_user = #{record.lockUser},
            </if>
            <if test="record.lockStatus != null">
                A.lock_status = #{record.lockStatus},
            </if>
            <if test="record.elasticGroupId != null">
                A.elastic_group_id = #{record.elasticGroupId},
            </if>
            <if test="record.elasticHistoryId != null">
                A.elastic_history_id = #{record.elasticHistoryId},
            </if>
            <if test="record.startTime != null">
                A.start_time = #{record.startTime},
            </if>
            <if test="record.endTime != null">
                A.end_time = #{record.endTime},
            </if>
            <if test="record.osCategory != null">
                A.os_category = #{osCategory},
            </if>
            <if test="record.stoppedMode != null">
                A.stopped_mode = #{stoppedMode},
            </if>
            <if test="record.recycle != null">
                A.is_recycle = #{recycle},
            </if>
            <if test="record.recycleDate != null">
                A.recycle_date = #{recycleDate}
            </if>

            <if test="record.urn != null">
                A.URN = #{record.urn},
            </if>
            <if test="record.uri != null">
                A.URI = #{record.uri},
            </if>
            <if test="record.hasVmTools != null">
                A.has_vm_tools = #{record.hasVmTools},
            </if>
            <if test="record.hostName != null">
                A.host_name = #{record.hostName},
            </if>
            <if test="record.allocateResVcSid != null">
                A.ALLOCATE_RES_VC_SID = #{record.allocateResVcSid},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Base_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update res_vm
        set ID = #{record.id,jdbcType=VARCHAR},
        CLOUD_ENV_ID = #{record.cloudEnvId,jdbcType=BIGINT},
        ALLOCATE_RES_HOST_SID = #{record.allocateResHostSid,jdbcType=VARCHAR},
        INSTANCE_ID = #{record.instanceId,jdbcType=VARCHAR},
        INSTANCE_NAME = #{record.instanceName,jdbcType=VARCHAR},
        MANAGEMENT_ACCOUNT = #{record.managementAccount,jdbcType=VARCHAR},
        MANAGEMEN_PASSWORD = #{record.managemenPassword,jdbcType=VARCHAR},
        IMAGE_ID = #{record.imageId,jdbcType=VARCHAR},
        PLATFORM = #{record.platform,jdbcType=VARCHAR},
        CPU = #{record.cpu,jdbcType=TINYINT},
        MEMORY = #{record.memory,jdbcType=INTEGER},
        DESCRIPTION = #{record.description,jdbcType=VARCHAR},
        INSTANCE_TYPE = #{record.instanceType,jdbcType=VARCHAR},
        OWNER_ID = #{record.ownerId,jdbcType=VARCHAR},
        INSTANCE_CHARGE_TYPE = #{record.instanceChargeType,jdbcType=VARCHAR},
        INTERNET_CHARGE_TYPE = #{record.internetChargeType,jdbcType=VARCHAR},
        INTERNET_MAX_BANDWIDTH_OUT = #{record.internetMaxBandwidthOut,jdbcType=INTEGER},
        INTERNET_MAX_BANDWIDTH_IN = #{record.internetMaxBandwidthIn,jdbcType=INTEGER},
        INNER_IP = #{record.innerIp,jdbcType=VARCHAR},
        PUBLIC_IP = #{record.publicIp,jdbcType=VARCHAR},
        SSH_PORT = #{record.sshPort,jdbcType=VARCHAR},
        INSTANCE_NETWORK_TYPE = #{record.instanceNetworkType,jdbcType=VARCHAR},
        ZONE = #{record.zone,jdbcType=VARCHAR},
        REGION = #{record.region,jdbcType=VARCHAR},
        MANAGE_STATUS = #{record.manageStatus,jdbcType=VARCHAR},
        STATUS_INFO = #{record.statusInfo,jdbcType=VARCHAR},
        STATUS = #{record.status,jdbcType=VARCHAR},
        OS_NAME = #{record.osName,jdbcType=VARCHAR},
        CREATED_BY = #{record.createdBy,jdbcType=VARCHAR},
        CREATED_DT = #{record.createdDt,jdbcType=TIMESTAMP},
        UPDATED_BY = #{record.updatedBy,jdbcType=VARCHAR},
        UPDATED_DT = #{record.updatedDt,jdbcType=TIMESTAMP},
        REMOTE_LOGIN_TYPE = #{record.remoteLoginType,jdbcType=VARCHAR},
        KEYPAIR_ID = #{record.keypairId,jdbcType=INTEGER},
        KEYPAIR_NAME = #{record.keypairName,jdbcType=VARCHAR},
        FLOATING_IP_POOL_NAME = #{record.floatingIpPoolName,jdbcType=VARCHAR},
        FLOATING_IP_POOL_ID = #{record.floatingIpPoolId,jdbcType=VARCHAR},
        VERSION = #{record.version,jdbcType=BIGINT},
        SERVER_TYPE = #{record.serverType,jdbcType=VARCHAR},
        SERVER_TEMPLATE_ID = #{record.serverTemplateId,jdbcType=BIGINT},
        use_memory_size = #{record.useMemorySize},
        use_store_size = #{record.useStoreSize},
        provision_storage = #{record.provisionStorage},
        cpu_usage = #{record.cpuUsage},
        use_cpu_ghz = #{record.useCpuGhz},
        disk_usage = #{record.diskUsage},
        org_sid = #{record.orgSid},
        lock_user = #{record.lockUser},
        lock_status = #{record.lockStatus},
        elastic_group_id = #{record.elasticGroupId},
        elastic_history_id = #{record.elasticHistoryId},
        start_time = #{record.startTime},
        end_time = #{record.endTime},
        stopped_mode = #{record.stoppedMode},
        is_recycle = #{record.recycle},
        URN = #{record.urn},
        URI = #{record.uri},
        recycle_date = #{record.recycleDate},
        has_vm_tools = #{record.hasVmTools},
        host_name = #{record.hostName},
        ALLOCATE_RES_VC_SID = #{record.allocateResVcSid}
        <if test="_parameter != null">
            <include refid="Base_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective"
        parameterType="cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResVm">
        update res_vm
        <set>
            <if test="cloudEnvId != null">
                CLOUD_ENV_ID = #{cloudEnvId,jdbcType=BIGINT},
            </if>
            <if test="allocateResHostSid != null">
                ALLOCATE_RES_HOST_SID = #{allocateResHostSid,jdbcType=VARCHAR},
            </if>
            <if test="instanceId != null">
                INSTANCE_ID = #{instanceId,jdbcType=VARCHAR},
            </if>
            <if test="instanceName != null">
                INSTANCE_NAME = #{instanceName,jdbcType=VARCHAR},
            </if>
            <if test="managementAccount != null">
                MANAGEMENT_ACCOUNT = #{managementAccount,jdbcType=VARCHAR},
            </if>
            <if test="managemenPassword != null">
                MANAGEMEN_PASSWORD = #{managemenPassword,jdbcType=VARCHAR},
            </if>
            <if test="imageId != null">
                IMAGE_ID = #{imageId,jdbcType=VARCHAR},
            </if>
            <if test="platform != null">
                PLATFORM = #{platform,jdbcType=VARCHAR},
            </if>
            <if test="cpu != null">
                CPU = #{cpu,jdbcType=TINYINT},
            </if>
            <if test="memory != null">
                MEMORY = #{memory,jdbcType=INTEGER},
            </if>
            <if test="description != null">
                DESCRIPTION = #{description,jdbcType=VARCHAR},
            </if>
            <if test="instanceType != null">
                INSTANCE_TYPE = #{instanceType,jdbcType=VARCHAR},
            </if>
            <if test="ownerId != null">
                OWNER_ID = #{ownerId,jdbcType=VARCHAR},
            </if>
            <if test="instanceChargeType != null">
                INSTANCE_CHARGE_TYPE = #{instanceChargeType,jdbcType=VARCHAR},
            </if>
            <if test="internetChargeType != null">
                INTERNET_CHARGE_TYPE = #{internetChargeType,jdbcType=VARCHAR},
            </if>
            <if test="internetMaxBandwidthOut != null">
                INTERNET_MAX_BANDWIDTH_OUT = #{internetMaxBandwidthOut,jdbcType=INTEGER},
            </if>
            <if test="internetMaxBandwidthIn != null">
                INTERNET_MAX_BANDWIDTH_IN = #{internetMaxBandwidthIn,jdbcType=INTEGER},
            </if>
            <if test="innerIp != null">
                INNER_IP = #{innerIp,jdbcType=VARCHAR},
            </if>
            <if test="publicIp != null">
                PUBLIC_IP = #{publicIp,jdbcType=VARCHAR},
            </if>
            <if test="sshPort != null">
                SSH_PORT = #{sshPort,jdbcType=VARCHAR},
            </if>
            <if test="instanceNetworkType != null">
                INSTANCE_NETWORK_TYPE = #{instanceNetworkType,jdbcType=VARCHAR},
            </if>
            <if test="zone != null">
                ZONE = #{zone,jdbcType=VARCHAR},
            </if>
            <if test="region != null">
                REGION = #{region,jdbcType=VARCHAR},
            </if>
            <if test="manageStatus != null">
                MANAGE_STATUS = #{manageStatus,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                STATUS = #{status,jdbcType=VARCHAR},
            </if>
            <if test="originalStatus != null">
                original_status = #{originalStatus,jdbcType=VARCHAR},
            </if>
            <if test="statusInfo != null">
                STATUS_INFO = #{statusInfo,jdbcType=VARCHAR},
            </if>
            <if test="osName != null">
                OS_NAME = #{osName,jdbcType=VARCHAR},
            </if>
            <if test="createdBy != null">
                CREATED_BY = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDt != null">
                CREATED_DT = #{createdDt,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDt != null">
                UPDATED_DT = #{updatedDt,jdbcType=TIMESTAMP},
            </if>
            <if test="version != null">
                VERSION = #{version,jdbcType=BIGINT},
            </if>
            <if test="manageStatus != null">
                MANAGE_STATUS = #{manageStatus,jdbcType=VARCHAR},
            </if>
            <if test="remoteLoginType != null">
                REMOTE_LOGIN_TYPE = #{remoteLoginType,jdbcType=VARCHAR},
            </if>
            <if test="keypairId != null">
                KEYPAIR_ID = #{keypairId,jdbcType=INTEGER},
            </if>
            <if test="keypairName != null">
                keypair_name = #{keypairName,jdbcType=VARCHAR},
            </if>
            <if test="floatingIpPoolName != null">
                FLOATING_IP_POOL_NAME = #{floatingIpPoolName,jdbcType=VARCHAR},
            </if>
            <if test="floatingIpPoolId != null">
                FLOATING_IP_POOL_ID = #{floatingIpPoolId,jdbcType=VARCHAR},
            </if>
            <if test="originParam != null">
                ORIGIN_PARAM = #{originParam,jdbcType=LONGVARCHAR},
            </if>
            <if test="serverType != null">
                SERVER_TYPE = #{serverType,jdbcType=VARCHAR},
            </if>
            <if test="serverTemplateId != null">
                SERVER_TEMPLATE_ID = #{serverTemplateId,jdbcType=BIGINT},
            </if>
            <if test="useMemorySize != null">
                use_memory_size = #{useMemorySize},
            </if>
            <if test="useStoreSize != null">
                use_store_size = #{useStoreSize},
            </if>
            <if test="provisionStorage != null">
                provision_storage = #{provisionStorage},
            </if>
            <if test="cpuUsage != null">
                cpu_usage = #{cpuUsage},
            </if>
            <if test="useCpuGhz != null">
                use_cpu_ghz = #{useCpuGhz},
            </if>
            <if test="diskUsage != null">
                disk_usage = #{diskUsage},
            </if>
            <if test="orgSid != null">
                org_sid = #{orgSid},
            </if>
            <if test="lockUser != null">
                lock_user = #{lockUser,jdbcType=BIGINT},
            </if>
            <if test="lockStatus != null">
                lock_status = #{lockStatus,jdbcType=VARCHAR},
            </if>
            <if test="elasticGroupId != null">
                elastic_group_id = #{elasticGroupId,jdbcType=BIGINT},
            </if>
            <if test="elasticHistoryId != null">
                elastic_history_id = #{elasticHistoryId,jdbcType=BIGINT},
            </if>
            <if test="allocFlag != null">
                alloc_flag = #{allocFlag,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null">
                start_time = #{startTime},
            </if>
            <if test="endTime != null">
                end_time = #{endTime},
            </if>
            <if test="osCategory != null">
                os_category = #{osCategory},
            </if>
            <if test="stoppedMode != null">
                stopped_mode = #{stoppedMode},
            </if>
            <if test="recycle != null">
                is_recycle = #{recycle},
            </if>
            <if test="recycleDate != null">
                recycle_date = #{recycleDate},
            </if>
            <if test="urn != null">
                URN = #{urn},
            </if>
            <if test="uri != null">
                URI = #{uri},
            </if>
            <if test="hasVmTools != null">
                has_vm_tools = #{hasVmTools},
            </if>
            <if test="managedIp != null">
                managed_ip = #{managedIp},
            </if>
            <if test="hostName != null">
                host_name = #{hostName},
            </if>
            <if test="installSnmp != null">
                install_snmp = #{installSnmp},
            </if>
            <if test="vncInfo != null">
                vnc_info = #{vncInfo},
            </if>
            <if test="allocateResVcSid != null">
                ALLOCATE_RES_VC_SID = #{allocateResVcSid},
            </if>
        </set>
        where ID = #{id,jdbcType=VARCHAR}
    </update>
    <insert id="insertSerRelateHost">
        insert into cloud_deployment_vm(res_vm_id,deployment_id) values (#{resVmId},#{deploymentId})
    </insert>
    <delete id="removeCloudDeploymentRelation">
        delete from cloud_deployment_vm where deployment_id = #{deploymentId}
        <if test="resVmIds != null and resVmIds.size() > 0">
            and res_vm_id in
            <foreach collection="resVmIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </delete>
    <select id="checkInsertSerRelateHost" resultType="java.lang.Integer">
        select count(*) from cloud_deployment_vm where res_vm_id = #{resVmId} and deployment_id = #{deploymentId}
    </select>
    <update id="updateByPrimaryKey"
        parameterType="cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResVm">
        update res_vm
        set
        CLOUD_ENV_ID = #{cloudEnvId,jdbcType=BIGINT},
        ALLOCATE_RES_HOST_SID = #{allocateResHostSid,jdbcType=VARCHAR},
        INSTANCE_ID = #{instanceId,jdbcType=VARCHAR},
        INSTANCE_NAME = #{instanceName,jdbcType=VARCHAR},
        MANAGEMENT_ACCOUNT = #{managementAccount,jdbcType=VARCHAR},
        MANAGEMEN_PASSWORD = #{managemenPassword,jdbcType=VARCHAR},
        IMAGE_ID = #{imageId,jdbcType=VARCHAR},
        PLATFORM = #{platform,jdbcType=VARCHAR},
        CPU = #{cpu,jdbcType=TINYINT},
        MEMORY = #{memory,jdbcType=INTEGER},
        DESCRIPTION = #{description,jdbcType=VARCHAR},
        INSTANCE_TYPE = #{instanceType,jdbcType=VARCHAR},
        OWNER_ID = #{ownerId,jdbcType=VARCHAR},
        INSTANCE_CHARGE_TYPE = #{instanceChargeType,jdbcType=VARCHAR},
        INTERNET_CHARGE_TYPE = #{internetChargeType,jdbcType=VARCHAR},
        INTERNET_MAX_BANDWIDTH_OUT = #{internetMaxBandwidthOut,jdbcType=INTEGER},
        INTERNET_MAX_BANDWIDTH_IN = #{internetMaxBandwidthIn,jdbcType=INTEGER},
        INNER_IP = #{innerIp,jdbcType=VARCHAR},
        PUBLIC_IP = #{publicIp,jdbcType=VARCHAR},
        FLOATING_IP = #{floatingIp,jdbcType=VARCHAR},
        SSH_PORT = #{sshPort,jdbcType=VARCHAR},
        INSTANCE_NETWORK_TYPE = #{instanceNetworkType,jdbcType=VARCHAR},
        ZONE = #{zone,jdbcType=VARCHAR},
        REGION = #{region,jdbcType=VARCHAR},
        MANAGE_STATUS = #{manageStatus,jdbcType=VARCHAR},
        STATUS = #{status,jdbcType=VARCHAR},
        STATUS_INFO = #{statusInfo,jdbcType=VARCHAR},
        OS_NAME = #{osName,jdbcType=VARCHAR},
        CREATED_BY = #{createdBy,jdbcType=VARCHAR},
        UPDATED_BY = #{updatedBy,jdbcType=VARCHAR},
        UPDATED_DT = #{updatedDt,jdbcType=TIMESTAMP},
        REMOTE_LOGIN_TYPE = #{remoteLoginType,jdbcType=VARCHAR},
        KEYPAIR_ID = #{keypairId,jdbcType=INTEGER},
        keypair_name = #{keypairName,jdbcType=INTEGER},
        FLOATING_IP_POOL_NAME = #{floatingIpPoolName,jdbcType=VARCHAR},
        FLOATING_IP_POOL_ID = #{floatingIpPoolId,jdbcType=VARCHAR},
        SERVER_TYPE = #{serverType,jdbcType=VARCHAR},
        SERVER_TEMPLATE_ID = #{serverTemplateId,jdbcType=BIGINT},
        VERSION = #{version,jdbcType=BIGINT},
        lock_user = #{lockUser,jdbcType=BIGINT},
        lock_status = #{lockStatus,jdbcType=VARCHAR},
        elastic_group_id = #{elasticGroupId,jdbcType=BIGINT},
        elastic_history_id = #{elasticHistoryId,jdbcType=BIGINT},
        start_time = #{startTime,jdbcType=TIMESTAMP},
        end_time = #{endTime,jdbcType=TIMESTAMP},
        os_category = #{osCategory},
        stopped_mode = #{stoppedMode},
        is_recycle = #{recycle},
        urn = #{urn},
        uri = #{uri},
        recycle_date = #{recycleDate},
        has_vm_tools = #{hasVmTools},
        host_name = #{hostName},
        ALLOCATE_RES_VC_SID = #{allocateResVcSid}
        where ID = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateVersionByPrimaryKey" parameterType="map">
        update res_vm
        set version = #{version,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <!-- added by liyi begin-->
    <select id="selectByParamForExaReport"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultMap="BaseResultMap">
        SELECT
        A.ID,
        B.cloud_env_name as ALLOCATE_RES_HOST_SID,
        A.CLOUD_ENV_ID,
        A.INSTANCE_ID,
        A.INSTANCE_NAME
        from res_vm A
        LEFT JOIN cloud_env B on (B.id = A.CLOUD_ENV_ID)
        where 1=1
        <if test="condition.status != null">
            and A.status = #{condition.status,jdbcType=VARCHAR}
        </if>

        <if test="condition.cloudEnvIds != null">
            and A.CLOUD_ENV_ID in
            <foreach item="item" index="index" collection="condition.cloudEnvIds"
                open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="condition.manageStatus != null">
            and A.manage_status = #{condition.manageStatus,jdbcType=VARCHAR}
        </if>

        <if test="condition.createdBy != null">
            and A.created_by = #{condition.createdBy,jdbcType=VARCHAR}
        </if>

        <if test="condition.hostName != null">
            and A.instance_name like CONCAT('%',#{condition.hostName},'%')
        </if>
        ORDER by A.created_dt DESC

    </select>
    <!-- added by liyi end-->
    <update id="unbandingFloatingIp" parameterType="java.lang.String">
        update res_vm SET public_ip = null, internet_max_bandwidth_out = null where id = #{resVmId}
    </update>
    <update id="mutiUpdateCloudHostZone">
        update res_vm
        set ZONE = #{zone}
        where ID in
        <foreach collection="hostIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>
    <delete id="resetCloudHostDeploymentId" parameterType="java.lang.Long">
        delete from cloud_deployment_vm
        where deployment_id = #{cloudDeploymentId}
    </delete>


    <select id="selectResVmByVolumeType"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM res_vm A
        LEFT JOIN res_host_storage B ON A.allocate_res_host_sid = B.res_host_sid
        LEFT JOIN res_vc_storage C ON A.allocate_res_vc_sid = C.res_vc_sid
        # 默认通过虚拟机关联的宿主机查询存储
        LEFT JOIN res_storage D ON (B.res_storage_sid = D.res_storage_sid
        # 虚拟机所关联的宿主机为空的情况下，通过集群查询存储
        OR (C.res_storage_sid = D.res_storage_sid AND (A.allocate_res_host_sid IS NULL OR A.allocate_res_host_sid =
        '')))
        WHERE A.cloud_env_id = #{condition.cloudEnvId}
        AND A.status NOT IN ('deleted', 'deleting', 'creating', 'failure', 'suspended', 'recovering', 'setting')
        AND (A.lock_status != 'locked' OR A.lock_status IS NULL )
        AND D.res_volume_type_id = #{condition.volumeTypeId}
        <if test="condition.resVmIds != null">
            AND A.id IN
            <foreach collection="condition.resVmIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY A.id
    </select>

    <select id="selectVmwareHostByStorageId"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from res_vm A
        inner join res_host_storage B on A.allocate_res_host_sid = B.res_host_sid
        inner join res_storage C on B.res_storage_sid = C.res_storage_sid
        and C.res_storage_sid = #{condition.storageId}
        left join cloud_env D on A.cloud_env_id = D.id
        where A.cloud_env_id = #{condition.cloudEnvId}
        and A.status not in ('deleted', 'deleting', 'creating', 'failure', 'suspended', 'recovering', 'setting')
        and (A.lock_status != 'locked' or A.lock_status is null)
        GROUP BY A.id
    </select>

    <select id="selectHostsByEnvId" parameterType="Long"
        resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        ,B.name as zone_name
        FROM res_vm A
        LEFT JOIN res_zone B ON (A.cloud_env_id = B.cloud_env_id and A.zone = B.uuid)
        LEFT JOIN cloud_env C on A.cloud_env_id = C.id
        WHERE A.cloud_env_id = #{id}
    </select>

    <select id="countHostByInstanceType" parameterType="java.lang.String"
        resultType="java.lang.Integer">
        select count(A.id) from res_vm A where A.instance_type = #{instanceType};
    </select>

    <select id="selectByServiceInstanceId"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM res_vm A
        INNER JOIN sf_service_inst_target B ON A.id = B.target_id and b.target_type = 'host'
        <where>
            <if test="condition.id != null">
                and B.sf_service_inst_id = #{condition.id}
            </if>
            <if test="condition.ids != null">
                and B.sf_service_inst_id in
                <foreach collection="condition.ids" item="item" close=")" open="(" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        group by a.id
    </select>
    <select id="selectBaseByExample" parameterType="java.lang.String"
        resultMap="BaseResultMap">
        SELECT
        B.CLOUD_ENV_TYPE,
        C.env_account_name,
        <include refid="Base_Column_List"/>
        FROM res_vm A
        LEFT JOIN cloud_env B ON A.cloud_env_id = B.id
        LEFT JOIN cloud_env_account C ON B.cloud_env_account_id = C.id
        <if test="_parameter != null">
            <include refid="Base_Example_Where_Clause"/>
        </if>
    </select>
    <select id="countLicenseNormalHost" resultType="java.lang.Integer">
        select count(0) from res_vm A
        INNER JOIN cloud_env B on A.cloud_env_id = B.id
        <if test="_parameter != null">
            <include refid="Base_Example_Where_Clause"/>
        </if>
    </select>
    <select id="selectBaseByParamWithoutDf" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        SELECT
        <include refid="Base_Column_List"/>
        FROM res_vm A
        <if test="_parameter != null">
            <include refid="Base_Example_Where_Clause"/>
        </if>
    </select>
    <select id="selectBaseByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        smu.account OWNER_ACCOUNT,
        smo.org_name org_name,
        <include refid="Base_Column_List"/>
        from res_vm A
        LEFT JOIN sys_m_user smu ON A.owner_id = smu.user_sid
        LEFT JOIN sys_m_org smo ON A.org_sid = smo.org_sid
        where A.ID = #{id,jdbcType=VARCHAR}
    </select>
    <update id="updateOrgSidByHostIds">
        UPDATE res_vm
        SET org_sid = #{orgSid}, owner_id = NULL
        WHERE org_sid != #{orgSid}
        AND id IN
        <foreach collection="hostIds" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
    </update>


    <update id="updateToNewOrg">
        UPDATE res_vm
        SET org_sid  = #{newOrgSid},
            owner_id = NULL
        WHERE org_sid = #{oldOrgSid}
    </update>

    <delete id="deleteDeployments" parameterType="cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResVm">
        delete
        from cloud_deployment_vm
        WHERE res_vm_id = #{id,jdbcType=VARCHAR}
    </delete>

    <update id="mutiUpdateCloudHostEndTime">
        update res_vm
        set end_time = #{endTime, jdbcType=TIMESTAMP}
        where ID in
        <foreach collection="hostIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </update>
    <update id="updateCpuAndMemIfNotExist"
        parameterType="cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResVm">
        update res_vm set cpu = IFNULL(cpu, #{cpu}), memory = IFNULL(memory, #{memory}) where id = #{id};
    </update>

    <select id="getResourceNodeStatistics"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultType="cn.com.cloudstar.rightcloud.core.pojo.dto.dashboard.ResourceNodeHost">
        SELECT
        a.org_sid,
        a.id,
        -- a.instance_name,
        a.STATUS,
        a.end_time AS endTime,
        IFNULL( c.cloud_env_name, '其他' ) AS cloudEnvName,
        IFNULL( c.cloud_env_type, '其他') AS cloudEnvType,
        IFNULL( c.cloud_env_account_id, -1) AS cloudEnvAccountId
        FROM
        res_vm a
        LEFT JOIN cloud_env c ON c.id = a.cloud_env_id
        WHERE
        a.STATUS NOT IN ( 'deleted', 'pending', 'create_failure' )
    </select>

    <select id="findByParams" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultMap="BaseResultMap">
        select
        distinct
        IFNULL(B.cloud_env_name,"其他") as CLOUD_ENV_NAME,
        GROUP_CONCAT(IFNULL(F.name,"其他")) as CLOUD_DEPLOYMENT_NAME,
        CONCAT(A.INSTANCE_NAME,"(",B.cloud_env_name,")") as INSTANCE_ALL_NAME,
        B.cloud_env_type as CLOUD_ENV_TYPE,
        C.CODE_DISPLAY as STATUS_NAME,
        IFNULL(D.CODE_DISPLAY, "其他") as CLOUD_ENV_TYPE_NAME,
        A.keypair_name,
        E.cluster_id,
        <include refid="Base_Column_List"/>
        from res_vm A
        LEFT JOIN cloud_env B on (B.id = A.CLOUD_ENV_ID)
        LEFT JOIN SYS_M_CODE C on (C.CODE_CATEGORY = 'CLOUD_HOST_STATUS' and C.CODE_VALUE = A.STATUS
        )
        LEFT JOIN SYS_M_CODE D on (D.CODE_CATEGORY = 'CLOUD_ENV_TYPE' and D.CODE_VALUE =
        B.CLOUD_ENV_TYPE )
        LEFT JOIN CLOUD_CLUSTER_NODE E on (E.host_id = A.id )
        LEFT JOIN cloud_deployment_host SDRH on (SDRH.cloud_host_id = A.ID)
        LEFT JOIN CLOUD_DEPLOYMENT F on (F.id = SDRH.DEPLOYMENT_ID )
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        group by id
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>

    <select id="selectHostByServiceVisible"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultMap="BaseResultMap">
        select
        IFNULL(B.cloud_env_name,"其他") as CLOUD_ENV_NAME,
        B.cloud_env_type as CLOUD_ENV_TYPE,
        B.cloud_env_category as CLOUD_ENV_CATEGORY,
        B.stopped_mode as env_stopped_mode,
        C.CODE_DISPLAY as STATUS_NAME,
        D.CODE_DISPLAY as CLOUD_ENV_TYPE_NAME,
        F.CODE_DISPLAY as INSTANCE_CHARGE_TYPE_NAME,
        G.CODE_DISPLAY as MANAGE_STATUS_NAME,
        M.name as SERVER_TEMPLATE_NAME,
        N.image_name as IMAGE_NAME,
        P.org_name,
        IFNULL(O.keypair_name, A.keypair_name) as KEYPAIR_NAME,
        HI.TAG_NAMES,
        HI.RGB_CODES,
        HI.TAG_VALUES,
        smu.EMAIL AS lock_user_email,
        RH.uuid AS allocate_res_host_uuid, RH.host_name AS allocate_res_host_name,
        <include refid="Base_Column_List"/>
        from res_vm A
        LEFT JOIN cloud_env B on (B.id = A.CLOUD_ENV_ID)
        LEFT JOIN SYS_M_CODE C on (C.CODE_CATEGORY = 'CLOUD_HOST_STATUS' and C.CODE_VALUE = A.STATUS)
        LEFT JOIN SYS_M_CODE D on (D.CODE_CATEGORY = 'CLOUD_ENV_TYPE' and D.CODE_VALUE = B.CLOUD_ENV_TYPE)
        LEFT JOIN SYS_M_CODE F on (F.CODE_CATEGORY = 'ALIYUN_CHARGE_TYPE' and F.CODE_VALUE = A.INSTANCE_CHARGE_TYPE)
        LEFT JOIN SYS_M_CODE G on (G.CODE_CATEGORY = 'CLOUD_HOST_MANAGE_STATUS' and G.CODE_VALUE = A.MANAGE_STATUS)
        LEFT JOIN (select H.obj_id, GROUP_CONCAT(I.tag_name ORDER BY I.tag_id) as TAG_NAMES,
        GROUP_CONCAT(I.rgb_code ORDER BY I.tag_id) as RGB_CODES,
        GROUP_CONCAT(I.tag_value ORDER BY I.tag_id) as TAG_VALUES from cloud_tag_ref H
        LEFT JOIN cloud_tag I on (H.tag_id= I.tag_id and H.obj_type = 'host') GROUP BY H.obj_id) HI on HI.obj_id = A.ID
        <if test="condition.tagIds != null and condition.tagIds.size > 0">
            inner join cloud_tag_ref K on k.obj_id = a.ID and K.obj_type = 'host' and K.tag_id in
            <foreach item="tags" index="index" collection="condition.tagIds" open="(" separator=","
                close=")">
                #{tags}
            </foreach>
        </if>
        LEFT JOIN SERVER_TEMPLATE M on (A.SERVER_TEMPLATE_ID = M.ID)
        LEFT JOIN res_image N on (A.image_id = N.image_id
        AND ((A.cloud_env_id = N.cloud_env_id AND N.cloud_env_id != - 1)
        OR (N.region = B.region AND N.cloud_env_type = B.cloud_env_type AND N.cloud_env_id = - 1)))
        LEFT JOIN CLOUD_KEYPAIR O on (A.KEYPAIR_ID = O.ID)
        LEFT JOIN sys_m_org P on(A.org_sid = P.org_sid)
        LEFT JOIN sys_m_user smu on smu.USER_SID = A.LOCK_USER
        LEFT JOIN res_host RH ON A.allocate_res_host_sid = RH.res_host_sid
        LEFT JOIN cloud_deployment_vm SDRH on (SDRH.res_vm_id = A.ID)
        LEFT JOIN CLOUD_DEPLOYMENT CD on (SDRH.DEPLOYMENT_ID = CD.ID)
        LEFT JOIN sf_service_inst_target SIT ON (SIT.target_id = A.ID)
        LEFT JOIN sf_service_deploy_inst SDI ON (SDI.ID = SIT.sf_service_inst_id)
        LEFT JOIN SF_SERVICE_CATEGORY CATE ON CATE.ID = SDI.SERVICE_ID
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        GROUP BY A.ID
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>

    </select>

    <select id="selectBaseByParamsAllc" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultMap="BaseResultMap">
        select
        IFNULL(b.cloud_env_type, '其他') as cloud_env_type
        , IFNULL(b.cloud_env_name, '其他') as cloud_env_name
        , b.stopped_mode as env_stopped_mode
        , c.CODE_DISPLAY AS CLOUD_ENV_TYPE_NAME
        , D.CODE_DISPLAY as STATUS_NAME,
        A.keypair_name,
        E.EMAIL as lock_user_email,
        S.name as SERVER_TEMPLATE_NAME,
        N.image_name as IMAGE_NAME,
        HI.TAG_NAMES,
        HI.RGB_CODES,
        HI.TAG_VALUES,
        smo.org_name,
        CD.name as CLOUD_DEPLOYMENT_NAME,
        <include refid="Base_Column_List"/>
        from res_vm A
        LEFT JOIN cloud_deployment_vm SDRH on (SDRH.res_vm_id = A.ID)
        LEFT JOIN CLOUD_DEPLOYMENT CD on (SDRH.DEPLOYMENT_ID = CD.ID)
        LEFT JOIN sys_m_org smo ON smo.org_sid = A.org_sid
        LEFT JOIN cloud_env B ON a.cloud_env_id = B.id
        LEFT JOIN SYS_M_CODE C ON C.CODE_CATEGORY = 'CLOUD_ENV_TYPE' and C.CODE_VALUE =
        B.CLOUD_ENV_TYPE
        LEFT JOIN SYS_M_CODE D on (D.CODE_CATEGORY = 'RES_VM_STATUS' and D.CODE_VALUE = A.STATUS
        )
        LEFT JOIN SYS_M_USER E ON A.lock_user = E.USER_SID
        LEFT JOIN res_host RH ON A.allocate_res_host_sid = RH.res_host_sid
        LEFT JOIN server_template S ON A.server_template_id = S.id
        LEFT JOIN res_image N on (A.image_id = N.image_id
        AND ((A.cloud_env_id = N.cloud_env_id AND N.cloud_env_id != - 1)
        OR (N.region = B.region AND N.cloud_env_type = B.cloud_env_type AND N.cloud_env_id = - 1)))
        LEFT JOIN (select H.obj_id, GROUP_CONCAT(I.tag_name ORDER BY I.tag_id) as TAG_NAMES,
        GROUP_CONCAT(I.rgb_code ORDER BY I.tag_id) as RGB_CODES,
        GROUP_CONCAT(I.tag_value ORDER BY I.tag_id) as TAG_VALUES from cloud_tag_ref H
        LEFT JOIN cloud_tag I on (H.tag_id= I.tag_id and H.obj_type = 'host') GROUP BY H.obj_id) HI on HI.obj_id = A.ID
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        group by A.id
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>

    </select>

    <select id="selectHostStatusCount" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultType="cn.com.cloudstar.rightcloud.common.pojo.NameValue">
        select A.status as name, count(1) as value
        from res_vm A
        <if test="_parameter != null">
            <include refid="Base_Example_Where_Clause"/>
        </if>
        group by A.status
    </select>

    <select id="countSoonExpiredHost" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultType="java.lang.Long">
        select count(*) from res_vm A
        INNER JOIN cloud_env env ON A.cloud_env_id = env.id
        LEFT JOIN sys_m_code C ON C.code_category = 'CLOUD_ENV_TYPE' and C.ATTRIBUTE_1 != 'RCLink' and
        env.cloud_env_type = C.code_value
        <where>
            <if test="condition.expireDate != null">
                AND A.end_time is not null
                AND A.end_time > NOW()
                <![CDATA[
                    AND A.end_time < #{condition.expireDate}
                ]]>
            </if>
            <if test="condition.envTypeCategory != null">
                and C.ATTRIBUTE_1 = #{condition.envTypeCategory}
            </if>
            <include refid="Example_Where_Caluse_Params"/>
        </where>
    </select>
    <select id="countExpiredHost" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultType="java.lang.Long">
        select count(*) from res_vm A
        INNER JOIN cloud_env env ON A.cloud_env_id = env.id
        LEFT JOIN sys_m_code C ON C.code_category = 'CLOUD_ENV_TYPE' and C.ATTRIBUTE_1 != 'RCLink' and
        env.cloud_env_type = C.code_value
        <where>
            A.end_time is not null
            <![CDATA[ AND A.end_time < NOW() ]]>
            <if test="condition.envTypeCategory != null">
                and C.ATTRIBUTE_1 = #{condition.envTypeCategory}
            </if>
            <include refid="Example_Where_Caluse_Params"/>
        </where>
    </select>
    <select id="countResVmType" resultType="cn.com.cloudstar.rightcloud.common.pojo.NameValue">
        SELECT
        CASE WHEN A.instance_type IS NOT NULL THEN A.instance_type
        ELSE ''
        END as name,
        count(*) as value
        FROM
        RES_VM A
        <where>
            <include refid="Example_Where_Caluse_Params"/>
        </where>
        GROUP BY
        A.instance_type
    </select>
    <select id="countCpuMem" resultType="java.util.Map">
        SELECT
        sum( A.cpu ) AS cpu,
        sum( A.memory ) AS ram
        FROM
        RES_VM A
        LEFT JOIN res_host RH ON A.allocate_res_host_sid = RH.res_host_sid
        <where>
            <include refid="Example_Where_Caluse_Params"/>
        </where>
    </select>
    <select id="countResVm" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultType="cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResVmCount">
        select A.cloud_env_id as cloudEnvId, date(A.created_dt) as date, count(*) as count
        from res_vm A
        <if test="_parameter != null">
            <include refid="Base_Example_Where_Clause"/>
        </if>
        group by A.cloud_env_id,date(A.created_dt)
    </select>
    <select id="selectVmEnvs" resultType="cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnv">
        SELECT
        CASE WHEN env.id IS NULL THEN 0 ELSE env.id END id ,
        CASE WHEN env.id IS NULL THEN '其他' ELSE env.cloud_env_name END cloudEnvName,
        count(*) instanceNumber
        FROM
        res_vm A LEFT JOIN cloud_env env ON A.cloud_env_id = env.id
        <where>
            <include refid="Example_Where_Caluse_Params"/>
        </where>
        group by id;
    </select>
    <select id="selectInstanceIdsBaseByParams"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria" resultType="java.lang.String">
        SELECT A.INSTANCE_ID
        FROM res_vm A
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        limit 2000
    </select>

    <select id="selectByPhysicalHostPoolId"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from res_vm A
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.physicalHostPoolId != null">
                and A.physical_host_pool_id = #{condition.physicalHostPoolId}
            </if>
            <if test="condition.statusNotIn != null and condition.statusNotIn.size() > 0">
                and A.status not in
                <foreach item="item" index="index" collection="condition.statusNotIn"
                    open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.notInRecycle != null">
                and (A.is_recycle = 0 OR A.is_recycle IS NULL)
            </if>
        </trim>
        order by A.created_dt desc
    </select>

    <update id="updateResVmStatusByPhysicalHostPoolId">
        UPDATE res_vm SET status = #{resVmStatus}
        WHERE physical_host_pool_id = #{physicalHostPoolId}
        AND status not in('deleted')
        AND is_recycle !=1;
    </update>

    <select id="selectUnusedBaremetal" parameterType="java.lang.String"
        resultMap="BaseResultMap">
        SELECT
        B.CLOUD_ENV_TYPE,
        C.env_account_name,
        <include refid="Base_Column_List"/>
        FROM res_vm A
        LEFT JOIN cloud_env B ON A.cloud_env_id = B.id
        LEFT JOIN cloud_env_account C ON B.cloud_env_account_id = C.id
        LEFT JOIN cloud_physical_host_pool CPHP ON CPHP.id = A.physical_host_pool_id
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.cloudEnvId != null">
                and A.cloud_env_id = #{condition.cloudEnvId}
            </if>
            <if test="condition.allocStatus != null">
                and CPHP.alloc_status = #{condition.allocStatus}
            </if>
            <if test="condition.statusNotIn != null and condition.statusNotIn.size() > 0">
                and A.status not in
                <foreach item="item" index="index" collection="condition.statusNotIn"
                    open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.cloudEnvOwnerOrgSid != null and condition.currentOrgSid != null">
                and (A.org_sid =#{condition.cloudEnvOwnerOrgSid} or A.org_sid =#{condition.currentOrgSid})
            </if>
        </trim>
    </select>

    <select id="selectPhysicalHostVendorCount"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultType="cn.com.cloudstar.rightcloud.common.pojo.NameValue">
        SELECT IFNULL(B.vendor,'其他') as name, count(A.id) as value
        FROM res_vm A
        LEFT JOIN cloud_physical_host_pool B ON A.physical_host_pool_id=B.id
        <if test="_parameter != null">
            <include refid="Base_Example_Where_Clause"/>
        </if>
        GROUP BY name
        ORDER BY B.vendor DESC
    </select>


    <select id="countPhysicalHostGpu" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultType="java.lang.Integer">
        SELECT SUM(IFNULL(B.gpu_number,0))
        FROM res_vm A
        LEFT JOIN cloud_physical_host_pool B ON A.physical_host_pool_id=B.id
        <if test="_parameter != null">
            <include refid="Base_Example_Where_Clause"/>
        </if>
    </select>


    <select id="selectUnBindingVm"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultMap="BaseResultMap">
        SELECT
        C.CODE_DISPLAY as STATUS_NAME,
        <include refid="Base_Column_List"/>
        FROM res_vm A
        LEFT JOIN res_vm_ext B ON A.ID = B.instance_id AND B.type = 'subnet'
        LEFT JOIN SYS_M_CODE C on (C.CODE_CATEGORY = 'RES_VM_STATUS' and C.CODE_VALUE = A.STATUS)
        WHERE A.cloud_env_id = #{condition.cloudEnvId} AND A.`status` = 'running'
        and B.resource_id = #{condition.subnetId}
    </select>


    <select id="selectBindingVm"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultMap="BaseResultMap">
        SELECT
        D.CODE_DISPLAY as STATUS_NAME,
        B.id AS vm_port_id,
        B.fixed_ip AS port_ip,
        <include refid="Base_Column_List"/>
        FROM res_vm A
        LEFT JOIN res_vpc_port B ON A.ID = B.device
        LEFT JOIN res_vpc_port_ext C ON C.resource_id = B.id
        LEFT JOIN SYS_M_CODE D on (D.CODE_CATEGORY = 'RES_VM_STATUS' and D.CODE_VALUE = A.STATUS)
        WHERE C.port_id = #{condition.portId} AND C.type = 'server'
    </select>


    <select id="countCpu" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultType="java.lang.Integer">
        select COALESCE(sum(A.cpu),0) from res_vm A
        LEFT JOIN cloud_env B on A.cloud_env_id = B.id
        <if test="_parameter != null">
            <include refid="Base_Example_Where_Clause"/>
        </if>
    </select>
    <select id="countMemory" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultType="java.lang.Integer">
        select COALESCE(sum(A.memory),0) from res_vm A
        LEFT JOIN cloud_env B on A.cloud_env_id = B.id
        <if test="_parameter != null">
            <include refid="Base_Example_Where_Clause"/>
        </if>
    </select>


    <select id="selectByCriteriaWithNoFilter"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from res_vm A
        <if test="_parameter != null">
            <include refid="Base_Example_Where_Clause"/>
        </if>
        GROUP BY A.ID
    </select>

    <insert id="insertBatch" parameterType="java.util.List"
            useGeneratedKeys="true" keyProperty="id">
        insert into res_vm (CLOUD_ENV_ID, ALLOCATE_RES_HOST_SID,
        INSTANCE_ID,
        INSTANCE_NAME, MANAGEMENT_ACCOUNT, MANAGEMEN_PASSWORD,
        IMAGE_ID, IMAGE_TYPE, PLATFORM, CPU, MEMORY,
        DESCRIPTION, INSTANCE_TYPE, OWNER_ID,INSTANCE_CHARGE_TYPE,
        INTERNET_CHARGE_TYPE, INTERNET_MAX_BANDWIDTH_OUT,
        INTERNET_MAX_BANDWIDTH_IN, INNER_IP, PUBLIC_IP, SSH_PORT,
        INSTANCE_NETWORK_TYPE, ZONE,
        REGION, MANAGE_STATUS, STATUS, STATUS_INFO, CREATED_BY,
        CREATED_DT, UPDATED_BY, UPDATED_DT, REMOTE_LOGIN_TYPE, KEYPAIR_ID,
        FLOATING_IP_POOL_NAME, FLOATING_IP_POOL_ID, SERVER_TEMPLATE_ID,
        VERSION, SERVER_TYPE
        , use_memory_size, use_store_size, provision_storage, cpu_usage, use_cpu_ghz,
        disk_usage,org_sid,
        keypair_name,
        LOCK_USER, LOCK_STATUS, start_time, end_time, stopped_mode,
        HAS_VM_TOOLS, HOST_NAME, ALLOCATE_RES_VC_SID
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.cloudEnvId},#{item.allocateResHostSid},
            #{item.instanceId},
            #{item.instanceName}, #{item.managementAccount},
            #{item.managemenPassword},
            #{item.imageId},#{item.imageType},#{item.platform}, #{item.cpu},
            #{item.memory},
            #{item.description}, #{item.instanceType},
            #{item.ownerId},#{item.instanceChargeType},
            #{item.internetChargeType}, #{item.internetMaxBandwidthOut},
            #{item.internetMaxBandwidthIn}, #{item.innerIp},
            #{item.publicIp},#{item.sshPort},
            #{item.instanceNetworkType}, #{item.zone},
            #{item.region}
            ,#{item.manageStatus},#{item.status},#{item.statusInfo},
            #{item.createdBy},
            #{item.createdDt}, #{item.updatedBy},
            #{item.updatedDt},
            #{item.remoteLoginType}, #{item.keypairId},
            #{item.floatingIpPoolName},
            #{item.floatingIpPoolId},#{item.serverTemplateId},
            #{item.version},
            #{item.serverType}
            , #{item.useMemorySize}, #{item.useStoreSize},
            #{item.provisionStorage}
            , #{item.cpuUsage}, #{item.useCpuGhz}, #{item.diskUsage},
            #{item.orgSid}
            , #{item.keypairName}, #{item.lockUser},
            #{item.lockStatus},
            #{item.startTime}, #{item.endTime},
            #{item.stoppedMode}, #{item.hasVmTools}, #{item.hostName},
            #{item.allocateResVcSid}
            )
        </foreach>
    </insert>
    <select id="countByCriteria" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria" resultType="java.lang.Long">
        select
         count(A.id) count_status
        from res_vm A
        <if test="_parameter != null">
            <include refid="Base_Example_Where_Clause"/>
        </if>
    </select>
    <select id="selectNodeInfoListByClusterId"
            resultType="cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.ResVmNodeInfo">
        select
            rv.id,
            rv.instance_id,
            rv.inner_ip,
            rv.public_ip,
            rv.floating_ip,
            rv.cpu,
            rv.memory ram,
            rv.instance_type as typeName,
            rv.managemen_password,
            rv.status,
            rhcr.resource_type,
            rhcr.node_type,
            rhcr.hpc_point_type,
            rvd.disk_mode,
            rvd.allocate_disk_size
        from res_hpc_cluster_resource rhcr
                 left join res_vm rv on rv.id = rhcr.resource_id
                 left join res_vd rvd on  concat(rv.id,"") = rvd.res_vm_id and rvd.virt_type = "ecs"
        where rhcr.resource_type="ecs" and rhcr.cluster_id=#{id}
    </select>

    <update id="batchUpdateStatus">
        update res_vm
        <set>
            <if test="action != null">
                status = #{action}
            </if>
        </set>
        <where>
            <if test="computerIds != null">
                id in
                <foreach collection="computerIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </update>

    <select id="getVmStatusByIds" resultType="cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResVm">
        select id, status from res_vm where id in
        <foreach collection="ids" separator="," open="(" close=")" item="id">
            #{id,jdbcType=BIGINT}
        </foreach>
    </select>
</mapper>
