/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.service.lb.impl;

import cn.com.cloudstar.rightcloud.adapter.core.MQHelper;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.*;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.FloatingIpUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.FloatingIpUpdateResult;
import cn.com.cloudstar.rightcloud.basic.data.platform.CloudClientFactory;
import cn.com.cloudstar.rightcloud.basic.data.pojo.base.Base;
import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnv;
import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnvAccount;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.*;
import cn.com.cloudstar.rightcloud.basic.data.pojo.slb.BackendServers;
import cn.com.cloudstar.rightcloud.basic.data.pojo.slb.VserverBackendServers;
import cn.com.cloudstar.rightcloud.basic.data.service.res.BasicResActionLogService;
import cn.com.cloudstar.rightcloud.common.additional.ResInstResult;
import cn.com.cloudstar.rightcloud.common.constants.BillingConstants;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.constants.res.status.LbListenerStatus;
import cn.com.cloudstar.rightcloud.common.constants.res.status.LoadBalanceStatus;
import cn.com.cloudstar.rightcloud.common.constants.res.status.NetworkStatus;
import cn.com.cloudstar.rightcloud.common.constants.res.type.CloudEnvType;
import cn.com.cloudstar.rightcloud.common.constants.status.AlarmStatusExpress;
import cn.com.cloudstar.rightcloud.common.constants.type.ResourceType;
import cn.com.cloudstar.rightcloud.common.constants.type.VmOperation;
import cn.com.cloudstar.rightcloud.common.enums.resource.ResourceOperateEnum;
import cn.com.cloudstar.rightcloud.common.enums.resource.ResourceTypeEnum;
import cn.com.cloudstar.rightcloud.common.exception.BizException;
import cn.com.cloudstar.rightcloud.common.exception.resolver.CloudErrorResolver;
import cn.com.cloudstar.rightcloud.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.common.schedule.helper.ScheduleHelper;
import cn.com.cloudstar.rightcloud.common.util.*;
import cn.com.cloudstar.rightcloud.common.util.json.JsonUtil;
import cn.com.cloudstar.rightcloud.core.pojo.dto.slb.ResLbHost;
import cn.com.cloudstar.rightcloud.core.pojo.vo.slb.AwsLoadBalanceVO;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.User;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicInfoUtil;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicWebUtil;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.RenewBasicResourceDTO;
import cn.com.cloudstar.rightcloud.resource.dao.env.CloudEnvAccountMapper;
import cn.com.cloudstar.rightcloud.resource.dao.env.CloudEnvMapper;
import cn.com.cloudstar.rightcloud.resource.dao.lb.*;
import cn.com.cloudstar.rightcloud.resource.dao.network.NetworkMapper;
import cn.com.cloudstar.rightcloud.resource.dao.network.ResFloatingIpMapper;
import cn.com.cloudstar.rightcloud.resource.dao.network.ResVpcMapper;
import cn.com.cloudstar.rightcloud.resource.dao.network.ResVpcPortMapper;
import cn.com.cloudstar.rightcloud.resource.dao.server.ResVmExtMapper;
import cn.com.cloudstar.rightcloud.resource.dao.server.ResVmMapper;
import cn.com.cloudstar.rightcloud.resource.service.env.CloudEnvService;
import cn.com.cloudstar.rightcloud.resource.service.lb.ResLoadBalanceService;
import cn.com.cloudstar.rightcloud.resource.service.network.ResFloatingIpService;
import cn.com.cloudstar.rightcloud.resource.service.network.ResVpcService;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static cn.com.cloudstar.rightcloud.common.additional.ResInstResult.FAILURE;

/**
 * 负载均衡相关实现类
 *
 * <AUTHOR>
 */
@Service
public class ResLoadBalanceServiceImpl implements ResLoadBalanceService {

    private static final Logger logger = LoggerFactory.getLogger(ResLoadBalanceServiceImpl.class);
    @Autowired
    private ResLoadBalanceMapper resLoadBalanceMapper;

    @Autowired
    private CloudEnvMapper cloudEnvMapper;

    @Autowired
    private NetworkMapper networkMapper;

    @Autowired
    private ResLbListenerMapper resLbListenerMapper;

    @Autowired
    private ResLbBackendMapper resLbBackendMapper;

    @Autowired
    private ResVmMapper resVmMapper;

    @Autowired
    private ResLbBackendGroupMapper resLbBackendGroupMapper;

    @Autowired
    private ResLbExtMapper resLbExtMapper;

    @Autowired
    private BasicResActionLogService basicResActionLogService;

    @Autowired
    private ResFloatingIpService resFloatingIpService;

    @Autowired
    private ResFloatingIpMapper resFloatingIpMapper;

    @Autowired
    private ResVpcPortMapper resVpcPortMapper;

    @Autowired
    private ResVpcMapper resVpcMapper;
    @Lazy
    @Autowired
    private CloudEnvService cloudEnvService;

    @Autowired
    private ResVmExtMapper resVmExtMapper;

    @Autowired
    private CloudEnvAccountMapper cloudEnvAccountMapper;

    @Autowired
    private ResVpcService resVpcService;

    /**
     * Count by params int.
     *
     * @param example the example
     *
     * @return the int
     */
    @Override
    public int countByParams(Criteria example) {
        int count = this.resLoadBalanceMapper.countByParams(example);
        logger.debug("count: {}", count);
        return count;
    }

    /**
     * Select by primary key res load balance.
     *
     * @param id the id
     *
     * @return the res load balance
     */
    @Override
    public ResLoadBalance selectByPrimaryKey(Long id) {
        return this.resLoadBalanceMapper.selectByPrimaryKey(id);
    }

    /**
     * Select by params list.
     *
     * @param example the example
     *
     * @return the list
     */
    @Override
    public List<ResLoadBalance> selectByParams(Criteria example) {
        return this.resLoadBalanceMapper.selectByParams(example);
    }

    /**
     * Select backend by id list.
     *
     * @param id the id
     *
     * @return the list
     */
    @Override
    public List<ResLbHost> selectBackendById(Long id) {
        return this.resLoadBalanceMapper.selectBackendById(id);
    }

    /**
     * Delete by primary key int.
     *
     * @param id the id
     *
     * @return the int
     */
    @Override
    public int deleteByPrimaryKey(Long id) {
        return this.resLoadBalanceMapper.deleteByPrimaryKey(id);
    }

    /**
     * Update by primary key selective int.
     *
     * @param record the record
     *
     * @return the int
     */
    @Override
    public int updateByPrimaryKeySelective(ResLoadBalance record) {
        return this.resLoadBalanceMapper.updateByPrimaryKeySelective(record);
    }

    /**
     * Update by primary key int.
     *
     * @param record the record
     *
     * @return the int
     */
    @Override
    public int updateByPrimaryKey(ResLoadBalance record) {
        return this.resLoadBalanceMapper.updateByPrimaryKey(record);
    }

    /**
     * Delete by params int.
     *
     * @param example the example
     *
     * @return the int
     */
    @Override
    public int deleteByParams(Criteria example) {
        return this.resLoadBalanceMapper.deleteByParams(example);
    }

    /**
     * Update by params selective int.
     *
     * @param record the record
     * @param example the example
     *
     * @return the int
     */
    @Override
    public int updateByParamsSelective(ResLoadBalance record, Criteria example) {
        return this.resLoadBalanceMapper.updateByParamsSelective(record, example.getCondition());
    }

    /**
     * Update by params int.
     *
     * @param record the record
     * @param example the example
     *
     * @return the int
     */
    @Override
    public int updateByParams(ResLoadBalance record, Criteria example) {
        return this.resLoadBalanceMapper.updateByParams(record, example.getCondition());
    }

    /**
     * Insert int.
     *
     * @param record the record
     *
     * @return the int
     */
    @Override
    public int insert(ResLoadBalance record) {
        return this.resLoadBalanceMapper.insert(record);
    }

    /**
     * Insert selective int.
     *
     * @param record the record
     *
     * @return the int
     */
    @Override
    public int insertSelective(ResLoadBalance record) {
        return this.resLoadBalanceMapper.insertSelective(record);
    }

    /**
     * Sets load balance status.
     *
     * @param id the id
     * @param status the status
     *
     * @return the load balance status
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setLoadBalanceStatus(Long id, String status) {
        String opUser = "";
        User authUser = BasicInfoUtil.getAuthUser();
        if (Objects.nonNull(authUser) && !StringUtil.isNullOrEmpty(authUser.getAccount())) {
            opUser = BasicInfoUtil.getAuthUser().getAccount();
        }

        //查询出负载均衡实例
        ResLoadBalance resLoadBalance = resLoadBalanceMapper.selectByPrimaryKey(id);
        AssertUtil.requireNonBlank(resLoadBalance, "负载均衡底层资源已不存在，请重新选择。");
        this.cloudEnvService.checkCloudEnvNormal(resLoadBalance.getCloudEnvId(), true);

        cn.com.cloudstar.rightcloud.adapter.pojo.lb.LoadBalanceStatus loadBalanceStatus = CloudClientFactory.buildMQBean(
                resLoadBalance.getCloudEnvId(), cn.com.cloudstar.rightcloud.adapter.pojo.lb.LoadBalanceStatus.class);
        if (CloudEnvType.ALIYUN.equals(loadBalanceStatus.getProviderType())) {
            loadBalanceStatus.setLoadBalanceStatus(status);
            loadBalanceStatus.setLoadBalanceId(resLoadBalance.getUuid());
        } else {
            return false;
        }
        LoadBalanceStatusResult loadBalanceStatusResult = (LoadBalanceStatusResult) sendToMQRPC(loadBalanceStatus);

        if (!loadBalanceStatusResult.isSuccess()) {
            basicResActionLogService.insertIntoActionLog(id.toString(), opUser, ResourceTypeEnum.SNAPSHOT,
                                                         "active".equalsIgnoreCase(status) ? ResourceOperateEnum.START
                                                                                           : ResourceOperateEnum.STOP,
                                                         Boolean.TRUE);

            if (CloudEnvType.ALIYUN.equals(loadBalanceStatus.getProviderType())) {
                throw new BizException(CloudErrorResolver.getErrorMsg(loadBalanceStatusResult.getErrMsg()));
            } else {
                return false;
            }
        } else {
            int actionLogId = basicResActionLogService.insertIntoActionLog(id.toString(), opUser,
                                                                           ResourceTypeEnum.RES_SLB,
                                                                           "active".equalsIgnoreCase(status)
                                                                           ? ResourceOperateEnum.START
                                                                           : ResourceOperateEnum.STOP, Boolean.FALSE);

            if (CloudEnvType.ALIYUN.equals(loadBalanceStatus.getProviderType())) {
                resLoadBalance.setStatus(status);
                resLoadBalance.setUpdatedDt(new Date());
                resLoadBalanceMapper.updateByPrimaryKeySelective(resLoadBalance);
            }

            basicResActionLogService.updateActionLog(actionLogId, ResourceTypeEnum.RES_SLB,
                                                     resLoadBalance.getId().toString(),
                                                     "active".equalsIgnoreCase(status) ? ResourceOperateEnum.START
                                                                                       : ResourceOperateEnum.STOP);

            return true;
        }
    }

    @Override
    public boolean resetLoadBalanceName(Long id, String name, String description) {
        String opUser = "";
        User authUser = BasicInfoUtil.getAuthUser();
        if (Objects.nonNull(authUser) && !StringUtil.isNullOrEmpty(authUser.getAccount())) {
            opUser = BasicInfoUtil.getAuthUser().getAccount();
        }

        ResLoadBalance resLoadBalance = resLoadBalanceMapper.selectByPrimaryKey(id);
        LoadBalanceName loadBalanceName = CloudClientFactory.buildMQBean(resLoadBalance.getCloudEnvId(),
                                                                         LoadBalanceName.class);
        if (CloudEnvType.CLOUDOS.equals(loadBalanceName.getProviderType()) || CloudEnvType.OPEN_STACK.equals(
                loadBalanceName.getProviderType()) || CloudEnvType.HUAWEICLOUD.equals(
                loadBalanceName.getProviderType())) {
            loadBalanceName.setName(name);
            loadBalanceName.setDescription(description);
            loadBalanceName.setLoadBalanceId(resLoadBalance.getUuid());
        } else if (CloudEnvType.QCLOUD.equals(loadBalanceName.getProviderType())) {
            loadBalanceName.setName(name);
            loadBalanceName.setLoadBalanceId(resLoadBalance.getUuid());
        } else {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1411321550));
        }

        LoadBalanceNameResult loadBalanceNameResult = (LoadBalanceNameResult) sendToMQRPC(loadBalanceName);
        if (!loadBalanceNameResult.isSuccess()) {
            basicResActionLogService.insertIntoActionLog(id.toString(), opUser, ResourceTypeEnum.RES_SLB,
                                                         ResourceOperateEnum.MODIFY, Boolean.FALSE);

            if (Strings.isNullOrEmpty(loadBalanceNameResult.getErrMsg())) {
                return false;
            }

            throw new BizException(loadBalanceNameResult.getErrMsg());
        }
        if (CloudEnvType.CLOUDOS.equals(loadBalanceName.getProviderType()) || CloudEnvType.OPEN_STACK.equals(
                loadBalanceName.getProviderType()) || CloudEnvType.HUAWEICLOUD.equals(
                loadBalanceName.getProviderType())) {
            resLoadBalance.setLbName(name);
            resLoadBalance.setUpdatedDt(new Date());
            resLoadBalance.setDescription(description);
            resLoadBalanceMapper.updateByPrimaryKeySelective(resLoadBalance);
        } else if (CloudEnvType.QCLOUD.equals(loadBalanceName.getProviderType())) {
            resLoadBalance.setLbName(name);
            resLoadBalance.setUpdatedDt(new Date());
            resLoadBalanceMapper.updateByPrimaryKeySelective(resLoadBalance);
        }
        basicResActionLogService.insertIntoActionLog(id.toString(), opUser, ResourceTypeEnum.RES_SLB,
                                                     ResourceOperateEnum.MODIFY, Boolean.TRUE);
        return true;
    }

    /**
     * 发送MQ消息
     *
     * @return the res inst result
     */
    private Object sendToMQRPC(Base base) {
        try {
            return MQHelper.rpc(base);
        } catch (Exception e) {
            return new Object();
        }
    }

    /**
     * Delete load balance boolean.
     *
     * @param id the id
     *
     * @return the boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteLoadBalance(Long id) {
        String opUser = "";
        User authUser = BasicInfoUtil.getAuthUser();
        if (Objects.nonNull(authUser) && !StringUtil.isNullOrEmpty(authUser.getAccount())) {
            opUser = BasicInfoUtil.getAuthUser().getAccount();
        }

        ResLoadBalance resLoadBalance = resLoadBalanceMapper.selectByPrimaryKey(id);
        AssertUtil.requireNonBlank(resLoadBalance, "负载均衡底层资源已不存在，请重新选择。");
        this.cloudEnvService.checkCloudEnvNormal(resLoadBalance.getCloudEnvId(), true);

        //删除负载均衡与弹性IP的关联关系
        List<ResFloatingIp> resFloatingIps = resFloatingIpMapper.selectByParams(
                new Criteria("instanceId", resLoadBalance.getUuid()));
        boolean detachFloatingIp;
        if (!CollectionUtils.isEmpty(resFloatingIps)) {
            detachFloatingIp = resFloatingIpService.detachFloatingIp(resFloatingIps.get(0).getId(), false);
        } else {
            detachFloatingIp = true;
        }
        DeleteLb deleteLb = CloudClientFactory.buildMQBean(resLoadBalance.getCloudEnvId(), DeleteLb.class);
        DeleteLbResult deleteLbResult = new DeleteLbResult();
        if (detachFloatingIp) {
            if (CloudEnvType.ALIYUN.equals(deleteLb.getProviderType()) || CloudEnvType.AWS.equals(
                    deleteLb.getProviderType()) || CloudEnvType.HUAWEICLOUD.equals(deleteLb.getProviderType())
                    || CloudEnvType.QCLOUD.equals(deleteLb.getProviderType())) {
                deleteLb.setLoadBalanceId(resLoadBalance.getUuid());
            } else if (CloudEnvType.OPEN_STACK.equals(deleteLb.getProviderType()) || CloudEnvType.CLOUDOS.equals(
                    deleteLb.getProviderType())) {
                // 如果escloud下负载均衡存在监听，那么不允许删除，先让其删掉监听
                Criteria criteria = new Criteria();
                criteria.put("resLbId", id);
                List<ResLbListener> resLbListeners = this.resLbListenerMapper.selectByParams(criteria);
                if (!CollectionUtils.isEmpty(resLbListeners)) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_789598156));
                }
                deleteLb.setLoadBalanceId(resLoadBalance.getUuid());
            }
            deleteLbResult = (DeleteLbResult) sendToMQRPC(deleteLb);
        } else {
            deleteLbResult.setSuccess(false);
        }
        if (!deleteLbResult.isSuccess()) {
            basicResActionLogService.insertIntoActionLog(id.toString(), opUser, ResourceTypeEnum.RES_SLB,
                                                         ResourceOperateEnum.DELETE, Boolean.FALSE);

            if (CloudEnvType.QCLOUD.equals(deleteLb.getProviderType())) {
                if (LoadBalanceStatus.ERROR.equalsIgnoreCase(resLoadBalance.getStatus())) {
                    Criteria criteria = new Criteria();
                    criteria.put("resLbId", id);
                    List<ResLbListener> resLbListeners = this.resLbListenerMapper.selectByParams(criteria);
                    this.resLbBackendMapper.deleteByParams(criteria);
                    List<Long> listenerIds = resLbListeners.stream()
                                                           .map(ResLbListener::getId)
                                                           .collect(Collectors.toList());
                    this.resLbExtMapper.deleteByParams(new Criteria("resListenerIds", listenerIds));
                    this.resLbListenerMapper.deleteByParams(criteria);
                    resLoadBalanceMapper.deleteByPrimaryKey(id);
                    return true;
                }
            }

            if (!Strings.isNullOrEmpty(deleteLbResult.getErrMsg())) {
                throw new BizException(CloudErrorResolver.getErrorMsg(deleteLbResult.getErrMsg()));
            }

            return false;
        } else {
            basicResActionLogService.insertIntoActionLog(id.toString(), opUser, ResourceTypeEnum.RES_SLB,
                                                         ResourceOperateEnum.DELETE, Boolean.TRUE);

            if (CloudEnvType.ALIYUN.equals(deleteLb.getProviderType())) {
                Criteria criteria = new Criteria();
                criteria.put("resLbId", id);
                // 移除监听及相关健康状态关联关系
                List<ResLbListener> resLbListeners = this.resLbListenerMapper.selectByParams(criteria);
                if (!CollectionUtils.isEmpty(resLbListeners)) {
                    resLbListeners.forEach(resLbListener -> {
                        if (resLbListener.getResLbExtId() != null) {
                            this.resLbExtMapper.deleteByPrimaryKey(resLbListener.getResLbExtId());
                            this.resLbListenerMapper.deleteByPrimaryKey(resLbListener.getId());
                        }
                    });
                }
                // 移除组以及实例的关系
                this.resLbBackendGroupMapper.deleteByParams(criteria);
                this.resLbBackendMapper.deleteByParams(criteria);
            } else if (CloudEnvType.QCLOUD.equals(deleteLb.getProviderType())) {
                Criteria criteria = new Criteria();
                criteria.put("resLbId", id);
                List<ResLbListener> resLbListeners = this.resLbListenerMapper.selectByParams(criteria);
                this.resLbBackendMapper.deleteByParams(criteria);
                List<Long> listenerIds = resLbListeners.stream().map(ResLbListener::getId).collect(Collectors.toList());
                this.resLbExtMapper.deleteByParams(new Criteria("resListenerIds", listenerIds));
                this.resLbListenerMapper.deleteByParams(criteria);
            }
            resLoadBalanceMapper.deleteByPrimaryKey(id);

            if (resLoadBalance.getResNetWorkId() != null) {
                Network network = networkMapper.selectByPrimaryKey(resLoadBalance.getResNetWorkId());
                if (!StringUtil.isNullOrEmpty(network.getUsedIp()) && network.getUsedIp() - 1 >= 0) {
                    network.setUsedIp(network.getUsedIp() - 1);
                    networkMapper.updateByPrimaryKeySelective(network);
                }
            }

            // 清理res_vpc_port
            Criteria criteria = new Criteria("fixedIp", resLoadBalance.getAddress());
            criteria.put("cloudEnvId", resLoadBalance.getCloudEnvId());
            criteria.put("vpcId", resLoadBalance.getVpcId());
            resVpcPortMapper.deleteByParams(criteria);
            return true;
        }
    }

    /**
     * Describe zones list.
     *
     * @param envId the env id
     *
     * @return the list
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Zone> describeZones(Long envId) {
        DescribeZone describeZone = CloudClientFactory.buildMQBean(envId, DescribeZone.class);
        DescribeZoneResult describeZoneResult = (DescribeZoneResult) sendToMQRPC(describeZone);
        if (!describeZoneResult.isSuccess()) {
            throw new BizException(CloudErrorResolver.getErrorMsg(describeZoneResult.getErrMsg()));
        } else {
            return describeZoneResult.getZoneList();
        }
    }

    /**
     * Create load balance boolean.
     *
     * @param resLoadBalance the res load balance
     *
     * @return the boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createLoadBalance(ResLoadBalance resLoadBalance) {
        LbCreate lbCreate = CloudClientFactory.buildMQBean(resLoadBalance.getCloudEnvId(), LbCreate.class);
        // 传入创建实例的参数
        if (CloudEnvType.ALIYUN.equals(lbCreate.getProviderType())) {
            lbCreate.setBandwidth(resLoadBalance.getBandwidth());
            lbCreate.setLoadBalancerName(resLoadBalance.getLbName());
            lbCreate.setLoadBalancerSpec(resLoadBalance.getLoadBalancerSpec());
            lbCreate.setMasterZoneId(resLoadBalance.getMasterZoneId());
            lbCreate.setSlaveZoneId(resLoadBalance.getSlaveZoneId());
            lbCreate.setCharType(resLoadBalance.getChargeType());
            lbCreate.setPeriod(resLoadBalance.getPeriod());
            if (resLoadBalance.getResNetWorkId() != null) {
                Network network = networkMapper.selectByPrimaryKey(resLoadBalance.getResNetWorkId());
                lbCreate.setvSwitchId(network.getUuid());
            }
            lbCreate.setInternetChargeType(resLoadBalance.getInternetChargeType());
            lbCreate.setAddressType(resLoadBalance.getAddressType());
        } else if (CloudEnvType.OPEN_STACK.equals(lbCreate.getProviderType()) || CloudEnvType.CLOUDOS.equals(
                lbCreate.getProviderType())) {
            // ES_CLOUD 和 OpenStack采用一样的创建方式
            lbCreate.setLoadBalancerName(resLoadBalance.getLbName());
            lbCreate.setDescription(resLoadBalance.getDescription());
            lbCreate.setCharType(resLoadBalance.getChargeType());
            lbCreate.setPeriod(resLoadBalance.getPeriod());
            lbCreate.setAddress(resLoadBalance.getAddress());
            if (resLoadBalance.getResNetWorkId() != null) {
                Network network = networkMapper.selectByPrimaryKey(resLoadBalance.getResNetWorkId());
                lbCreate.setvSwitchId(network.getUuid());
            }
        } else if (CloudEnvType.HUAWEICLOUD.equals(lbCreate.getProviderType())) {
            lbCreate.setLoadBalancerName(resLoadBalance.getLbName());
            lbCreate.setDescription(resLoadBalance.getDescription());
            lbCreate.setAddress(resLoadBalance.getAddress());
            lbCreate.setAddressType(resLoadBalance.getAddressType());
            if (resLoadBalance.getResFloatingIpId() != null) {
                ResFloatingIp floatingIp = this.resFloatingIpService.selectByPrimaryKey(
                        resLoadBalance.getResFloatingIpId());
                AssertUtil.requireNonBlank(floatingIp, "浮动ip底层资源已不存在，请重新选择。");
                if (NetworkStatus.ACTIVE.equals(floatingIp.getStatus())) {
                    BizException.throwException("浮动ip底层资源已被使用，请重新选择。");
                }

                lbCreate.setFloatingIpId(floatingIp.getUuid());
            }
            Network network = networkMapper.selectByPrimaryKey(resLoadBalance.getResNetWorkId());
            lbCreate.setvSwitchId(network.getUuid());
        } else if (CloudEnvType.QCLOUD.equals(lbCreate.getProviderType())) {
            lbCreate.setLoadBalancerName(resLoadBalance.getLbName());
            lbCreate.setNetworkType(resLoadBalance.getNetworkType());
            lbCreate.setAddressType(resLoadBalance.getAddressType());
            lbCreate.setMasterZoneId(resLoadBalance.getMasterZoneId());
            if (resLoadBalance.getResVpcId() != null) {
                ResVpc resVpc = resVpcMapper.selectByPrimaryKey(resLoadBalance.getResVpcId());
                lbCreate.setVpcId(resVpc.getUuid());
            }
            if (resLoadBalance.getResNetWorkId() != null) {
                Network network = networkMapper.selectByPrimaryKey(resLoadBalance.getResNetWorkId());
                lbCreate.setNetworkId(network.getUuid());
            }
        }
        //调用MQ
        LoadBalanceCreateResult loadBalanceCreateResult = (LoadBalanceCreateResult) sendToMQRPC(lbCreate);
        if (!loadBalanceCreateResult.isSuccess()) {
            basicResActionLogService.insertIntoActionLog(resLoadBalance.getCreatedBy(), ResourceTypeEnum.RES_SLB,
                                                         ResourceOperateEnum.CREATE, Boolean.FALSE);
            throw new BizException(CloudErrorResolver.getErrorMsg(loadBalanceCreateResult.getErrMsg()));
        } else {
            resLoadBalance.setUuid(loadBalanceCreateResult.getLoadBalancerId());
            resLoadBalance.setAddress(loadBalanceCreateResult.getAddress());

            if (CloudEnvType.ALIYUN.equals(lbCreate.getProviderType())) {
                if ("intranet".equalsIgnoreCase(lbCreate.getAddressType())) {
                    resLoadBalance.setBandwidth(5120);
                }
                resLoadBalance.setStatus(AlarmStatusExpress.ACTIVE);
            } else if (CloudEnvType.OPEN_STACK.equals(lbCreate.getProviderType()) || CloudEnvType.CLOUDOS.equals(
                    lbCreate.getProviderType())) {
                resLoadBalance.setStatus(NetworkStatus.ACTIVE);
            } else if (CloudEnvType.AWS.equals(lbCreate.getProviderType())) {
                resLoadBalance.setStatus(loadBalanceCreateResult.getStatus());
            } else if (CloudEnvType.HUAWEICLOUD.equals(lbCreate.getProviderType())) {
                resLoadBalance.setStatus(NetworkStatus.ACTIVE);
                resLoadBalance.setAddressType(loadBalanceCreateResult.getAddressType());
                // 如果绑定了公网ip
                if ("internet".equals(loadBalanceCreateResult.getAddressType())
                        && resLoadBalance.getResFloatingIpId() != null) {
                    // 负载均衡的带宽字段没有设置
                    ResFloatingIp floatingIp = this.resFloatingIpService.selectByPrimaryKey(
                            resLoadBalance.getResFloatingIpId());
                    resLoadBalance.setBandwidth(Integer.parseInt(floatingIp.getBandWidth()));

                    // 更新弹性ip
                    ResFloatingIp floatingIpUpdate = new ResFloatingIp();
                    floatingIpUpdate.setId(resLoadBalance.getResFloatingIpId());
                    floatingIpUpdate.setFixedIp(loadBalanceCreateResult.getAddress());
                    floatingIpUpdate.setStatus(NetworkStatus.ACTIVE);
                    //如果绑定公网，则更新弹性ip绑定的资源字段
                    floatingIpUpdate.setInstanceType(ResourceType.RES_SLB);
                    floatingIpUpdate.setInstanceId(resLoadBalance.getUuid());
                    floatingIpUpdate.setInstanceName(resLoadBalance.getLbName());
                    this.resFloatingIpService.updateByPrimaryKeySelective(floatingIpUpdate);
                }
            } else if (CloudEnvType.QCLOUD.equals(lbCreate.getProviderType())) {
                resLoadBalance.setStatus(LoadBalanceStatus.ACTIVE);
                resLoadBalance.setMasterZoneId(loadBalanceCreateResult.getMasterZone());
            }
            resLoadBalance.setStartTime(new Date());
            if (BillingConstants.ChargeType.PRE_PAID.equals(resLoadBalance.getChargeType())) {
                resLoadBalance.setEndTime(
                        DateUtil.getDateByMonth(resLoadBalance.getStartTime(), resLoadBalance.getPeriod()));
            }
            resLoadBalance.setEndTime(DateUtil.plusMonths(resLoadBalance.getStartTime(), resLoadBalance.getPeriod()));
            resLoadBalanceMapper.insertSelective(resLoadBalance);
            if (resLoadBalance.getResNetWorkId() != null) {
                Network network = networkMapper.selectByPrimaryKey(resLoadBalance.getResNetWorkId());
                if (!StringUtil.isNullOrEmpty(network.getUsedIp())) {
                    network.setUsedIp(network.getUsedIp() + 1);
                    networkMapper.updateByPrimaryKeySelective(network);
                }
                if (CloudEnvType.OPEN_STACK.equals(lbCreate.getProviderType())) {
                    ResVpcPort resVpcPort = new ResVpcPort();
                    resVpcPort.setFixedIp(loadBalanceCreateResult.getAddress());
                    resVpcPort.setVpcId(Long.parseLong(network.getNetVpcId()));
                    resVpcPort.setDeviceOwner(loadBalanceCreateResult.getPortOwner());
                    resVpcPort.setStatus(loadBalanceCreateResult.getPortStatus());
                    resVpcPort.setDevice(resLoadBalance.getId() + "");
                    resVpcPort.setSubnetId(resLoadBalance.getResNetWorkId().toString());
                    resVpcPort.setUuid(loadBalanceCreateResult.getPortId());
                    resVpcPort.setPortName(loadBalanceCreateResult.getPortName());
                    resVpcPort.setCloudEnvId(resLoadBalance.getCloudEnvId());
                    resVpcPort.setOrgSid(resLoadBalance.getOrgSid());
                    resVpcPort.setMacAddress(loadBalanceCreateResult.getPortMacAddress());
                    BasicWebUtil.prepareInsertParams(resVpcPort);
                    resVpcPortMapper.insertSelective(resVpcPort);
                }
                basicResActionLogService.insertIntoActionLog(resLoadBalance.getId().toString(),
                                                             resLoadBalance.getCreatedBy(), ResourceTypeEnum.RES_SLB,
                                                             ResourceOperateEnum.CREATE, Boolean.TRUE);
            }
            return true;
        }
    }

    /**
     * 下发同步port
     **/
    @Async
    public void sendSyncPort(CloudEnv cloudEnv) {
        try {
            logger.info("触发 port 同步");
            CloudEnvAccount cloudEnvAccount = this.cloudEnvAccountMapper.selectByPrimaryKey(
                    cloudEnv.getCloudEnvAccountId());
            Map<String, String> params = MapsKit.of("key", "network", "companyId",
                                                    cloudEnvAccount.getOrgSid().toString());
            ScheduleHelper.manualSyncTask(cloudEnv.getId(), params, BasicInfoUtil.getCurrentUserSid());
        } catch (Exception e) {
            logger.info("发送同步Port 失败: {}", e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createAwsLoadBalance(AwsLoadBalanceVO lb) {
        String opUser = "";
        User authUser = BasicInfoUtil.getAuthUser();
        if (Objects.nonNull(authUser) && !StringUtil.isNullOrEmpty(authUser.getAccount())) {
            opUser = BasicInfoUtil.getAuthUser().getAccount();
        }

        // 判断lbName是否存在
        Criteria criteria = new Criteria();
        criteria.put("cloudEnvId", lb.getCloudEnvId());
        criteria.put("lbName", lb.getLbName());

        List<ResLoadBalance> loadBalances = resLoadBalanceMapper.selectByParams(criteria);

        if (!CollectionUtils.isEmpty(loadBalances)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_19951828));
        }

        LbCreate lbCreate = CloudClientFactory.buildMQBean(lb.getCloudEnvId(), LbCreate.class);
        if (CloudEnvType.AWS.equals(lbCreate.getProviderType())) {
            lbCreate.setLoadBalancerName(lb.getLbName());
            lbCreate.setLbProtocol(lb.getLbProtocol());
            lbCreate.setLbPort(lb.getLbPort());
            lbCreate.setInstanceProtocol(lb.getInstanceProtocol());
            lbCreate.setInstancePort(lb.getInstancePort());
            lbCreate.setVpcId(lb.getVpcId());
            lbCreate.setSecurityGroups(lb.getSecurityGroups());
            lbCreate.setSubnets(lb.getSubnets());
            lbCreate.setCertificateId(lb.getCertificateId());
        }
        //调用MQ
        LoadBalanceCreateResult result = (LoadBalanceCreateResult) sendToMQRPC(lbCreate);
        if (result.isSuccess()) {
            ResLoadBalance resLoadBalance = new ResLoadBalance();
            resLoadBalance.setUuid(lb.getLbName());
            resLoadBalance.setDnsName(result.getAddress());
            resLoadBalance.setStatus(NetworkStatus.ACTIVE);
            resLoadBalance.setCloudEnvId(lb.getCloudEnvId());
            resLoadBalance.setLbName(lb.getLbName());
            resLoadBalance.setLbType("classic");
            resLoadBalance.setListenerProtocal(lb.getLbProtocol());
            resLoadBalance.setListenerPort(lb.getLbPort() + "");
            resLoadBalance.setVpcId(lb.getVpcId());

            BasicWebUtil.prepareInsertParams(resLoadBalance);
            resLoadBalanceMapper.insertSelective(resLoadBalance);

            // 入库监听器
            ResLbListener listener = new ResLbListener();
            listener.setUuid(lb.getLbProtocol() + "#" + lb.getLbPort());
            listener.setProtocol(lb.getLbProtocol());
            listener.setPort(lb.getLbPort());
            listener.setBackendPort(lb.getInstancePort());
            listener.setResLbId(resLoadBalance.getId());
            BasicWebUtil.prepareInsertParams(listener);

            resLbListenerMapper.insertSelective(listener);

            basicResActionLogService.insertIntoActionLog(resLoadBalance.getId().toString(), opUser,
                                                         ResourceTypeEnum.RES_SLB, ResourceOperateEnum.CREATE,
                                                         Boolean.TRUE);

            return true;
        } else {
            basicResActionLogService.insertIntoActionLog(opUser, ResourceTypeEnum.RES_SLB, ResourceOperateEnum.CREATE,
                                                         Boolean.FALSE);
        }
        return false;
    }

    @Override
    public List<ResLoadBalance> selectByCriteria(Criteria criteria) {
        return this.resLoadBalanceMapper.selectByCriteria(criteria);
    }

    @Override
    public boolean bindAndUnBindFloatingiIp(Long lbId, String type, Long floatingIpId) {
        ResLoadBalance resLoadBalance = this.resLoadBalanceMapper.selectByPrimaryKey(lbId);
        ResFloatingIp resFloatingIp = this.resFloatingIpService.selectByPrimaryKey(floatingIpId);
        AssertUtil.requireNonBlank(resLoadBalance, "负载均衡底层资源已不存在，请重新选择。");
        AssertUtil.requireNonBlank(resFloatingIp, "未找到绑定的弹性ip，请重新选择。");
        this.cloudEnvService.checkCloudEnvNormal(resLoadBalance.getCloudEnvId(), true);
        CloudEnv cloudEnv = cloudEnvService.selectByPrimaryKey(resLoadBalance.getCloudEnvId());
        if (FloatingIpUpdate.BindLoadBalance.equals(type) && NetworkStatus.ACTIVE.equals(resFloatingIp.getStatus())) {
            BizException.throwException("浮动ip底层资源已被使用，请重新选择。");
        }

        FloatingIpUpdateResult floatingIpUpdateResult = this.resFloatingIpService.updateFloatingIp(floatingIpId, type,
                                                                                                   resLoadBalance);
        if (floatingIpUpdateResult.isSuccess()) {
            if (FloatingIpUpdate.BindLoadBalance.equals(type)) {
                // 绑定成功
                if (!CloudEnvType.OPEN_STACK.equals(cloudEnv.getCloudEnvType()) && !CloudEnvType.HUAWEICLOUD.equals(
                        cloudEnv.getCloudEnvType())) {
                    resLoadBalance.setAddressType("internet");
                }
                resLoadBalance.setResFloatingIpId(floatingIpId);
                if (!Strings.isNullOrEmpty(floatingIpUpdateResult.getBandwidth())) {
                    resLoadBalance.setBandwidth(Integer.parseInt(floatingIpUpdateResult.getBandwidth()));
                }
                resLoadBalance.setResFloatingIpAddress(floatingIpUpdateResult.getFloatingIpAddress());
                this.resLoadBalanceMapper.updateByPrimaryKey(resLoadBalance);

                // 更新弹性ip
                ResFloatingIp floatingIpUpdate = new ResFloatingIp();
                floatingIpUpdate.setId(floatingIpId);
                floatingIpUpdate.setFixedIp(resLoadBalance.getAddress());
                floatingIpUpdate.setStatus(NetworkStatus.ACTIVE);
                floatingIpUpdate.setInstanceType(ResourceType.RES_SLB);
                floatingIpUpdate.setInstanceId(resLoadBalance.getUuid());
                floatingIpUpdate.setInstanceName(resLoadBalance.getLbName());
                this.resFloatingIpService.updateByPrimaryKeySelective(floatingIpUpdate);
            } else if (FloatingIpUpdate.UnBindLoadBalance.equals(type)) {
                // 解绑成功
                if (!CloudEnvType.OPEN_STACK.equals(cloudEnv.getCloudEnvType())) {
                    resLoadBalance.setAddressType("intranet");
                }
                resLoadBalance.setResFloatingIpId(null);
                resLoadBalance.setBandwidth(null);
                this.resLoadBalanceMapper.updateByPrimaryKey(resLoadBalance);

                // 更新弹性ip
                resFloatingIp.setFixedIp(null);
                resFloatingIp.setStatus(NetworkStatus.UNUSED);
                resFloatingIp.setInstanceType(null);
                resFloatingIp.setInstanceId(null);
                resFloatingIp.setInstanceName(null);
                this.resFloatingIpService.updateByPrimaryKey(resFloatingIp);
            }
        } else {
            throw new BizException(floatingIpUpdateResult.getErrMsg());
        }

        return floatingIpUpdateResult.isSuccess();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateLoadBalanceListener(ResLbListener lbListener) {
        ResLbListener resLbListener = this.resLbListenerMapper.selectByPrimaryKey(lbListener.getId());
        ResLoadBalance resLoadBalance = this.resLoadBalanceMapper.selectByPrimaryKey(resLbListener.getResLbId());
        ResLbBackendGroup resLbBackendGroup = this.resLbBackendGroupMapper.selectByPrimaryKey(
                lbListener.getResLbBackendGroupId());
        // 监听器原来的后端服务组
        Criteria criteria = new Criteria();
        criteria.put("resLbListenerId", lbListener.getId());
        List<ResLbBackendGroup> resLbBackendGroups = this.resLbBackendGroupMapper.selectByParams(criteria);
        Long listenerOrgBackGroupId =
                CollectionUtils.isEmpty(resLbBackendGroups) ? 0L : resLbBackendGroups.get(0).getId();

        LbListenerUpdate lbListenerUpdate = CloudClientFactory.buildMQBean(resLoadBalance.getCloudEnvId(),
                                                                           LbListenerUpdate.class);
        if (CloudEnvType.HUAWEICLOUD.equals(lbListenerUpdate.getProviderType())) {
            lbListenerUpdate.setName(lbListener.getName());
            lbListenerUpdate.setListenerId(resLbListener.getUuid());
            lbListenerUpdate.setPoolId(resLbBackendGroup.getUuid());
        }
        //发送mq
        LbListenerUpdateResult lbListnerUpdateResult = (LbListenerUpdateResult) sendToMQRPC(lbListenerUpdate);
        if (!lbListnerUpdateResult.isSuccess()) {
            basicResActionLogService.insertIntoActionLog(resLbListener.getCreatedBy(),
                                                         ResourceTypeEnum.RES_SLB_LISTENER, ResourceOperateEnum.CREATE,
                                                         Boolean.FALSE);
            throw new BizException(lbListnerUpdateResult.getErrMsg());
        } else {
            if (CloudEnvType.HUAWEICLOUD.equals(lbListenerUpdate.getProviderType())) {
                // 更新监听器名称
                ResLbListener recordUpdate = new ResLbListener();
                recordUpdate.setId(lbListener.getId());
                recordUpdate.setName(lbListener.getName());
                this.resLbListenerMapper.updateByPrimaryKeySelective(recordUpdate);

                // 更新后端服务器组所属监听器
                ResLbBackendGroup backendGroupUpdate = new ResLbBackendGroup();
                backendGroupUpdate.setId(lbListener.getResLbBackendGroupId());
                backendGroupUpdate.setResLbListenerId(lbListener.getId());
                this.resLbBackendGroupMapper.updateByPrimaryKeySelective(backendGroupUpdate);

                if (!listenerOrgBackGroupId.equals(lbListener.getResLbBackendGroupId())) {
                    // 原来的后端服务组的监听器id置空
                    this.resLbBackendGroupMapper.updateResListenerIdNull(listenerOrgBackGroupId);
                }


            }
            return true;
        }
    }

    @Override
    public List<Map<String, Object>> selectCertificates(Long cloudEnvId) {
        // 暂时用监听更新对象去查询证书
        LbListenerUpdate lbListenerUpdate = CloudClientFactory.buildMQBean(cloudEnvId, LbListenerUpdate.class);
        lbListenerUpdate.setType(LbListenerUpdate.typeQueryCertificates);
        LbListenerUpdateResult lbListnerUpdateResult = (LbListenerUpdateResult) sendToMQRPC(lbListenerUpdate);
        return lbListnerUpdateResult.getCertificates();
    }

    @Override
    public boolean updateLoadBalanceListenerWhitelist(ResLbListener lbListener) {
        ResLbListener resLbListenerDb = this.resLbListenerMapper.selectByPrimaryKey(lbListener.getId());
        ResLoadBalance resLoadBalance = this.resLoadBalanceMapper.selectByPrimaryKey(resLbListenerDb.getResLbId());
        LbListenerUpdate lbListenerUpdateMQ = CloudClientFactory.buildMQBean(resLoadBalance.getCloudEnvId(),
                                                                             LbListenerUpdate.class);
        lbListenerUpdateMQ.setType(LbListenerUpdate.typeOpWhitelist);
        lbListenerUpdateMQ.setListenerId(resLbListenerDb.getUuid());
        lbListenerUpdateMQ.setWhitelistUuid(resLbListenerDb.getWhitelistUuid());
        lbListenerUpdateMQ.setWhitelistEnable(lbListener.getWhitelistEnable());
        lbListenerUpdateMQ.setWhitelist(lbListener.getWhitelist());
        LbListenerUpdateResult lbListnerUpdateResult = (LbListenerUpdateResult) sendToMQRPC(lbListenerUpdateMQ);
        if (lbListnerUpdateResult.isSuccess()) {
            ResLbListener resLbListenerUpdate = new ResLbListener();
            resLbListenerUpdate.setId(lbListener.getId());
            resLbListenerUpdate.setWhitelistUuid(lbListnerUpdateResult.getWhitelistUuid());
            resLbListenerUpdate.setWhitelist(lbListener.getWhitelist());
            resLbListenerUpdate.setWhitelistEnable(lbListener.getWhitelistEnable());
            this.resLbListenerMapper.updateByPrimaryKeySelective(resLbListenerUpdate);
            return true;
        } else {
            throw new BizException(lbListnerUpdateResult.getErrMsg());
        }
    }

    /**
     * Create load balance listener boolean.
     *
     * @param resLbListener the res lb listener
     *
     * @return the boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createLoadBalanceListener(ResLbListener resLbListener) {
        if (Objects.nonNull(resLbListener.getName())) {
            Criteria example = new Criteria();
            example.put("resLbId", resLbListener.getResLbId());
            example.put("name", resLbListener.getName());
            int count = resLbListenerMapper.countByParams(example);
            if (count > 0) {
                logger.info("监听器[{}]已存在", resLbListener.getName());
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1957444907));
            }
        }

        ResLoadBalance resLoadBalance = resLoadBalanceMapper.selectByPrimaryKey(resLbListener.getResLbId());
        AssertUtil.requireNonBlank(resLoadBalance, "负载均衡底层资源已不存在，请重新选择。");
        this.cloudEnvService.checkCloudEnvNormal(resLoadBalance.getCloudEnvId(), true);
        LbListenerCreate lbListenerCreate = CloudClientFactory.buildMQBean(resLoadBalance.getCloudEnvId(),
                                                                           LbListenerCreate.class);
        if (CloudEnvType.ALIYUN.equals(lbListenerCreate.getProviderType())) {
            lbListenerCreate.setLoadBalancerId(resLoadBalance.getUuid());
            lbListenerCreate.setProtocol(resLbListener.getProtocol());
            lbListenerCreate.setListenerPort(resLbListener.getPort());
            lbListenerCreate.setBackendServerPort(resLbListener.getPort());
            lbListenerCreate.setBandWith(resLbListener.getBandWith());
            lbListenerCreate.setScheduler(resLbListener.getScheduler());
            lbListenerCreate.setStickySession(resLbListener.getStickySession() ? "on" : "off");
            lbListenerCreate.setStickySessionType(resLbListener.getStickySessionType());
            lbListenerCreate.setCookieTimeout(resLbListener.getCookieTimeout());
            lbListenerCreate.setHealthCheck(resLbListener.getHealthCheck() ? "on" : "off");
            lbListenerCreate.setHealthCheckDomain(resLbListener.getHcDomain());
            lbListenerCreate.setHealthCheckURI(resLbListener.getHcUri());
            lbListenerCreate.setHealthCheckConnectPort(resLbListener.getHcPort());
            lbListenerCreate.setHealthyThreshold(resLbListener.getHcThreshold());
            lbListenerCreate.setUnhealthyThreshold(resLbListener.getUnhcThreshold());
            lbListenerCreate.setHealthCheckInterval(resLbListener.getHcInterval());
            lbListenerCreate.setHealthCheckTimeout(resLbListener.getHealthCheckTimeout());
            lbListenerCreate.setBackendServerPort(resLbListener.getBackPort());
            lbListenerCreate.setcACertificateId(resLbListener.getServerCertificateId());
            if (Objects.nonNull(resLbListener.getResLbBackendGroupId())) {
                ResLbBackendGroup resLbBackendGroup = resLbBackendGroupMapper.selectByPrimaryKey(
                        resLbListener.getResLbBackendGroupId());
                lbListenerCreate.setBackendServerGroupId(resLbBackendGroup.getUuid());
            }
        } else if (CloudEnvType.OPEN_STACK.equals(lbListenerCreate.getProviderType()) || CloudEnvType.CLOUDOS.equals(
                lbListenerCreate.getProviderType())) {
            // ESCloud == OpenStack
            lbListenerCreate.setName(resLbListener.getName());
            lbListenerCreate.setMaxConnect(resLbListener.getConnectionLimit());
            lbListenerCreate.setProtocol(resLbListener.getProtocol());
            lbListenerCreate.setListenerPort(resLbListener.getPort());
            lbListenerCreate.setLoadBalancerId(resLoadBalance.getUuid());
        } else if (CloudEnvType.AWS.equals(lbListenerCreate.getProviderType())) {
            // 判断端口是否被占用
            Criteria criteria = new Criteria();
            criteria.put("resLbId", resLbListener.getResLbId());
            criteria.put("port", resLbListener.getPort());
            List<ResLbListener> listeners = resLbListenerMapper.selectByParams(criteria);
            if (!CollectionUtils.isEmpty(listeners)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1931971019));
            }

            lbListenerCreate.setProtocol(resLbListener.getProtocol());
            lbListenerCreate.setListenerPort(resLbListener.getPort());
            lbListenerCreate.setBackendServerPort(resLbListener.getBackendPort());
            lbListenerCreate.setLoadBalancerId(resLoadBalance.getUuid());
            lbListenerCreate.setServerCertificateId(resLbListener.getServerCertificateId());
        } else if (CloudEnvType.HUAWEICLOUD.equals(lbListenerCreate.getProviderType())) {
            // 判断端口是否被占用
            Criteria criteria = new Criteria();
            criteria.put("resLbId", resLbListener.getResLbId());
            criteria.put("port", resLbListener.getPort());
            List<ResLbListener> listeners = resLbListenerMapper.selectByParams(criteria);
            if (!CollectionUtils.isEmpty(listeners)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1931971019));
            }
            lbListenerCreate.setName(resLbListener.getName());
            lbListenerCreate.setProtocol(resLbListener.getProtocol());
            lbListenerCreate.setListenerPort(resLbListener.getPort());
            lbListenerCreate.setLoadBalancerId(resLoadBalance.getUuid());
            lbListenerCreate.setServerCertificateId(resLbListener.getServerCertificateId());
        } else if (CloudEnvType.QCLOUD.equals(lbListenerCreate.getProviderType())) {
            lbListenerCreate.setProtocol(resLbListener.getProtocol());
            //判断端口是否已存在
            List<ResLbListener> resLbListeners = resLbListenerMapper.selectByParams(
                    new Criteria("resLbId", resLbListener.getResLbId()));
            List<Integer> ports = new ArrayList<>();

            //tcp、udp、http、https，任意两两组合中，TCP和UPD组合的端口可以重复,其他情况不可重复。
            if ("TCP".equalsIgnoreCase(lbListenerCreate.getProtocol())) {
                ports = resLbListeners.stream()
                                      .filter(listner -> !"UDP".equalsIgnoreCase(listner.getProtocol()))
                                      .map(ResLbListener::getPort)
                                      .collect(Collectors.toList());
            } else if ("UDP".equalsIgnoreCase(lbListenerCreate.getProtocol())) {
                ports = resLbListeners.stream()
                                      .filter(listner -> !"TCP".equalsIgnoreCase(listner.getProtocol()))
                                      .map(ResLbListener::getPort)
                                      .collect(Collectors.toList());
            } else {
                ports = resLbListeners.stream().map(ResLbListener::getPort).collect(Collectors.toList());
            }
            if (ports.contains(resLbListener.getPort())) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_479818070));
            }
            lbListenerCreate.setLoadBalancerId(resLoadBalance.getUuid());
            lbListenerCreate.setName(resLbListener.getName());
            lbListenerCreate.setListenerPort(resLbListener.getPort());
            lbListenerCreate.setScheduler(resLbListener.getScheduler());
            //判断协议方式是TCP、UDP还是HTTP、HTTPS
            if ("TCP".equalsIgnoreCase(resLbListener.getProtocol()) || "UDP".equalsIgnoreCase(
                    resLbListener.getProtocol())) {
                //健康检查：是否开启、检查间隔、健康阈值、不健康阈值、响应时间
                lbListenerCreate.setHealthCheck(resLbListener.getHealthCheck() ? "1" : "0");
                if (resLbListener.getHealthCheck()) {
                    lbListenerCreate.setHealthCheckInterval(resLbListener.getHcInterval());
                    lbListenerCreate.setHealthyThreshold(resLbListener.getHcThreshold());
                    lbListenerCreate.setUnhealthyThreshold(resLbListener.getUnhcThreshold());
                    lbListenerCreate.setHealthCheckTimeout(resLbListener.getHealthCheckTimeout());
                }
                //会话保持时间，默认0不开启
                lbListenerCreate.setStickySessionTime(resLbListener.getStickySessionTime());
            } else {
                ///Https 认证类型、服务端认证ID、客户端ID
                if ("HTTPS".equalsIgnoreCase(lbListenerCreate.getProtocol())) {
                    lbListenerCreate.setSslMode(resLbListener.getSslMode());
                    lbListenerCreate.setServerCertificateId(resLbListener.getServerCertificateId());
                    lbListenerCreate.setcACertificateId(resLbListener.getCaId());
                }
            }
        }
        //发送mq
        LbListnerCreateResult lbListnerCreateResult = (LbListnerCreateResult) sendToMQRPC(lbListenerCreate);
        if (!lbListnerCreateResult.isSuccess()) {
            basicResActionLogService.insertIntoActionLog(resLbListener.getCreatedBy(),
                                                         ResourceTypeEnum.RES_SLB_LISTENER, ResourceOperateEnum.CREATE,
                                                         Boolean.FALSE);

            if (CloudEnvType.ALIYUN.equals(lbListenerCreate.getProviderType())) {
                throw new BizException(CloudErrorResolver.getErrorMsg(lbListnerCreateResult.getErrMsg()));
            } else if (CloudEnvType.HUAWEICLOUD.equals(lbListenerCreate.getProviderType())) {
                throw new BizException(CloudErrorResolver.getErrorMsg(lbListnerCreateResult.getErrMsg()));
            } else if (CloudEnvType.OPEN_STACK.equals(lbListenerCreate.getProviderType())) {
                throw new BizException(CloudErrorResolver.getRegexErrorMsg(lbListnerCreateResult.getErrMsg()));
            } else if (CloudEnvType.CLOUDOS.equals(lbListenerCreate.getProviderType())) {
                throw new BizException(CloudErrorResolver.getRegexErrorMsg(lbListnerCreateResult.getErrMsg()));
            } else {
                return false;
            }
        } else {
            if (CloudEnvType.ALIYUN.equals(lbListenerCreate.getProviderType())) {
                resLbListener.setUuid(resLbListener.getProtocol() + "#" + resLbListener.getPort());
                resLbListener.setStatus(LbListenerStatus.RUNNING);
                String listenerPort = resLoadBalance.getListenerPort();
                String listenerProtocol = resLoadBalance.getListenerProtocal();
                if (StringUtil.isNotBlank(listenerPort)) {
                    listenerPort = listenerPort + "," + resLbListener.getPort();
                } else {
                    listenerPort = "" + resLbListener.getPort();
                }
                if (StringUtil.isNotBlank(listenerProtocol)) {
                    if (!listenerProtocol.contains(resLbListener.getProtocol())) {
                        listenerProtocol = listenerProtocol + "," + resLbListener.getProtocol();
                    }
                } else {
                    listenerProtocol = resLbListener.getProtocol();
                }
                resLoadBalance.setListenerPort(listenerPort);
                resLoadBalance.setListenerProtocal(listenerProtocol);
                resLoadBalanceMapper.updateByPrimaryKeySelective(resLoadBalance);
                // 将监听扩展属性存入表，并与监听建立关系
                ResLbExt resLbExt = new ResLbExt();
                this.makeExtBean(resLbExt, resLbListener);
                resLbExtMapper.insertSelective(resLbExt);
                resLbListener.setResLbExtId(resLbExt.getId());
            } else if (CloudEnvType.OPEN_STACK.equals(lbListenerCreate.getProviderType())
                    || CloudEnvType.CLOUDOS.equals(lbListenerCreate.getProviderType())) {
                // ESCloud == OpenStack
                resLbListener.setUuid(lbListnerCreateResult.getId());
                resLbListener.setStatus(LbListenerStatus.ACTIVE);
            } else if (CloudEnvType.AWS.equals(lbListenerCreate.getProviderType())) {
                resLbListener.setUuid(resLbListener.getProtocol() + "#" + resLbListener.getPort());
                resLbListener.setStatus(LbListenerStatus.ACTIVE);
            } else if (CloudEnvType.HUAWEICLOUD.equals(lbListenerCreate.getProviderType())) {
                resLbListener.setUuid(lbListnerCreateResult.getId());
                resLbListener.setStatus(LbListenerStatus.ACTIVE);
            } else if (CloudEnvType.QCLOUD.equals(lbListenerCreate.getProviderType())) {
                resLbListener.setUuid(lbListnerCreateResult.getId());
                resLbListener.setStatus(LbListenerStatus.ACTIVE);
            }
            int resListenerId = resLbListenerMapper.insertSelective(resLbListener);
            if ("TCP".equalsIgnoreCase(resLbListener.getProtocol()) || "UDP".equalsIgnoreCase(
                    resLbListener.getProtocol())) {
                ResLbExt resLbExt = new ResLbExt();
                resLbExt.setResListenerId(resLbListener.getId());
                resLbExt.setHealthCheck(resLbListener.getHealthCheck());
                resLbExt.setHcInterval(resLbListener.getHcInterval());
                resLbExt.setHcThreshold(resLbListener.getHcThreshold());
                resLbExt.setUnhcThreshold(resLbListener.getUnhcThreshold());
                resLbExt.setHcTimeout(resLbListener.getHealthCheckTimeout());
                resLbExt.setStickySession(resLbListener.getStickySession());
                resLbExt.setStickySessionTime(resLbListener.getStickySessionTime());
                resLbExtMapper.insert(resLbExt);
            }

            // 创建监听器时指定了后端服务组
            if (CloudEnvType.ALIYUN.equals(lbListenerCreate.getProviderType()) && Objects.nonNull(
                    resLbListener.getResLbBackendGroupId())) {
                ResLbBackendGroup resLbBackendGroup = new ResLbBackendGroup();
                resLbBackendGroup.setId(resLbListener.getResLbBackendGroupId());
                resLbBackendGroup.setResLbListenerId(resLbListener.getId());
                resLbBackendGroupMapper.updateByPrimaryKeySelective(resLbBackendGroup);
            }

            basicResActionLogService.insertIntoActionLog(resLbListener.getId().toString(), resLbListener.getCreatedBy(),
                                                         ResourceTypeEnum.RES_SLB_LISTENER, ResourceOperateEnum.CREATE,
                                                         Boolean.TRUE);
            return true;
        }
    }

    private void makeExtBean(ResLbExt resLbExt, ResLbListener resLbListener) {
        resLbExt.setStickySession(resLbListener.getStickySession());
        resLbExt.setHcTimeout(resLbListener.getHealthCheckTimeout());
        resLbExt.setCookieTimeout(resLbListener.getCookieTimeout());
        resLbExt.setHcDomain(resLbListener.getHcDomain());
        resLbExt.setHcHttpCode(resLbListener.getHcHttpCode());
        resLbExt.setHcInterval(resLbListener.getHcInterval());
        resLbExt.setHcUri(resLbListener.getHcUri());
        resLbExt.setUnhcThreshold(resLbListener.getUnhcThreshold());
        resLbExt.setHcPort(resLbListener.getHcPort());
        resLbExt.setHealthCheck(resLbListener.getHealthCheck());
        resLbExt.setStickySessionType(resLbListener.getStickySessionType());
        resLbExt.setHcThreshold(resLbListener.getHcThreshold());
    }

    /**
     * Start or stop listener boolean.
     *
     * @param id the id
     * @param lid the lid
     * @param status the status
     *
     * @return the boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean startOrStopListener(Long id, Long lid, String status) {
        String opUser = "";
        User authUser = BasicInfoUtil.getAuthUser();
        if (Objects.nonNull(authUser) && !StringUtil.isNullOrEmpty(authUser.getAccount())) {
            opUser = BasicInfoUtil.getAuthUser().getAccount();
        }

        ResLoadBalance resLoadBalance = resLoadBalanceMapper.selectByPrimaryKey(id);
        AssertUtil.requireNonBlank(resLoadBalance, "负载均衡底层资源已不存在，请重新选择。");
        ResLbListener resLbListener = resLbListenerMapper.selectByPrimaryKey(lid);
        AssertUtil.requireNonBlank(resLoadBalance, "监听底层资源已不存在，请重新选择。");
        this.cloudEnvService.checkCloudEnvNormal(resLoadBalance.getCloudEnvId(), true);
        StartOrStopListener startOrStopListener = CloudClientFactory.buildMQBean(resLoadBalance.getCloudEnvId(),
                                                                                 StartOrStopListener.class);
        if (CloudEnvType.ALIYUN.equals(startOrStopListener.getProviderType())) {
            startOrStopListener.setListenerPort(resLbListener.getPort());
            startOrStopListener.setStatus(status);
            startOrStopListener.setLoadBalanceId(resLoadBalance.getUuid());
        } else if (CloudEnvType.OPEN_STACK.equals(startOrStopListener.getProviderType()) || CloudEnvType.CLOUDOS.equals(
                startOrStopListener.getProviderType())) {
            startOrStopListener.setListenerId(resLbListener.getUuid());
            startOrStopListener.setAdminSetUp(VmOperation.START.equals(status));
        }
        StartOrStopListenerResult startOrStopListenerResult = (StartOrStopListenerResult) sendToMQRPC(
                startOrStopListener);
        if (!startOrStopListenerResult.isSuccess()) {
            basicResActionLogService.insertIntoActionLog(id.toString(), opUser, ResourceTypeEnum.RES_SLB_LISTENER,
                                                         "start".equalsIgnoreCase(status) ? ResourceOperateEnum.START
                                                                                          : ResourceOperateEnum.STOP,
                                                         Boolean.FALSE);

            if (CloudEnvType.ALIYUN.equals(startOrStopListener.getProviderType())) {
                throw new BizException(CloudErrorResolver.getErrorMsg(startOrStopListenerResult.getErrMsg()));
            } else {
                return false;
            }
        } else {
            if (CloudEnvType.ALIYUN.equals(startOrStopListener.getProviderType())) {
                if (VmOperation.START.equals(status)) {
                    resLbListener.setStatus(LbListenerStatus.RUNNING);
                } else {
                    resLbListener.setStatus(LbListenerStatus.STOPPED);
                }
                resLbListener.setUpdatedDt(new Date());
            } else if (CloudEnvType.OPEN_STACK.equals(startOrStopListener.getProviderType())
                    || CloudEnvType.CLOUDOS.equals(startOrStopListener.getProviderType())) {
                if (VmOperation.START.equals(status)) {
                    resLbListener.setStatus(LbListenerStatus.ACTIVE);
                } else {
                    resLbListener.setStatus(LbListenerStatus.INACTIVE);
                }
            } else {
                return false;
            }
            int actionLogId = basicResActionLogService.insertIntoActionLog(resLbListener.getId().toString(), opUser,
                                                                           ResourceTypeEnum.RES_SLB_LISTENER,
                                                                           "start".equalsIgnoreCase(status)
                                                                           ? ResourceOperateEnum.START
                                                                           : ResourceOperateEnum.STOP, Boolean.TRUE);

            resLbListenerMapper.updateByPrimaryKeySelective(resLbListener);

            basicResActionLogService.updateActionLog(actionLogId, ResourceTypeEnum.RES_SLB_LISTENER,
                                                     resLbListener.getId().toString(),
                                                     "start".equalsIgnoreCase(status) ? ResourceOperateEnum.START
                                                                                      : ResourceOperateEnum.STOP);

            return true;
        }
    }

    /**
     * Delete listener boolean.
     *
     * @param lbId the lb id
     * @param lsId the ls id
     *
     * @return the boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteListener(Long lbId, Long lsId) {
        String opUser = "";
        User authUser = BasicInfoUtil.getAuthUser();
        if (Objects.nonNull(authUser) && !StringUtil.isNullOrEmpty(authUser.getAccount())) {
            opUser = BasicInfoUtil.getAuthUser().getAccount();
        }

        ResLoadBalance resLoadBalance = resLoadBalanceMapper.selectByPrimaryKey(lbId);
        AssertUtil.requireNonBlank(resLoadBalance, "负载均衡底层资源已不存在，请重新选择。");
        this.cloudEnvService.checkCloudEnvNormal(resLoadBalance.getCloudEnvId(), true);
        ResLbListener resLbListener = resLbListenerMapper.selectByPrimaryKey(lsId);
        AssertUtil.requireNonBlank(resLbListener, "监听底层资源已不存在，请重新选择。");
        ListenerDelete listenerDelete = CloudClientFactory.buildMQBean(resLoadBalance.getCloudEnvId(),
                                                                       ListenerDelete.class);
        if (CloudEnvType.ALIYUN.equals(listenerDelete.getProviderType()) || CloudEnvType.AWS.equals(
                listenerDelete.getProviderType()) || CloudEnvType.QCLOUD.equals(listenerDelete.getProviderType())) {
            listenerDelete.setListenerPort(resLbListener.getPort());
            listenerDelete.setLoadBalanceId(resLoadBalance.getUuid());
            listenerDelete.setListenerId(resLbListener.getUuid());
        } else if (CloudEnvType.OPEN_STACK.equals(listenerDelete.getProviderType()) || CloudEnvType.CLOUDOS.equals(
                listenerDelete.getProviderType())) {

            // 在escloud下面，如果存在使用该监听的资源池，那么无法删除
            Criteria criteria = new Criteria();
            criteria.put("resLbListenerId", lsId);
            List<ResLbBackendGroup> resLbBackendGroups = this.resLbBackendGroupMapper.selectByParams(criteria);
            if (!CollectionUtils.isEmpty(resLbBackendGroups)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_611328003));
            }

            listenerDelete.setListenerId(resLbListener.getUuid());
        } else if (CloudEnvType.HUAWEICLOUD.equals(listenerDelete.getProviderType())) {
            listenerDelete.setListenerId(resLbListener.getUuid());
        }
        ListenerDeleteResult listenerDeleteResult = (ListenerDeleteResult) sendToMQRPC(listenerDelete);
        if (!listenerDeleteResult.isSuccess()) {
            basicResActionLogService.insertIntoActionLog(lsId.toString(), opUser, ResourceTypeEnum.RES_SLB_LISTENER,
                                                         ResourceOperateEnum.DELETE, Boolean.FALSE);

            if (CloudEnvType.ALIYUN.equals(listenerDelete.getProviderType())) {
                throw new BizException(CloudErrorResolver.getErrorMsg(listenerDeleteResult.getErrMsg()));
            } else if (CloudEnvType.HUAWEICLOUD.equals(listenerDelete.getProviderType())) {
                throw new BizException(CloudErrorResolver.getErrorMsg(listenerDeleteResult.getErrMsg()));
            } else {
                return false;
            }
        } else {
            if (CloudEnvType.ALIYUN.equals(listenerDelete.getProviderType())) {
                ResLbListener listener = this.resLbListenerMapper.selectByPrimaryKey(lsId);
                if (listener.getResLbExtId() != null) {
                    this.resLbExtMapper.deleteByPrimaryKey(listener.getResLbExtId());
                }
            }
            if (CloudEnvType.HUAWEICLOUD.equals(listenerDelete.getProviderType())) {
                ResLbListener listener = this.resLbListenerMapper.selectByPrimaryKey(lsId);
                if (listener.getResLbExtId() != null) {
                    this.resLbExtMapper.deleteByPrimaryKey(listener.getResLbExtId());
                }
                Criteria deletePoolCriteria = new Criteria();
                deletePoolCriteria.put("resLbListenerId", lsId);
                this.resLbBackendGroupMapper.deleteByParams(deletePoolCriteria);
            }
            basicResActionLogService.insertIntoActionLog(lsId.toString(), opUser, ResourceTypeEnum.RES_SLB_LISTENER,
                                                         ResourceOperateEnum.DELETE, Boolean.TRUE);

            resLbListenerMapper.deleteByPrimaryKey(lsId);
            return true;
        }
    }

    /**
     * Describe certificates list.
     *
     * @param id the id
     *
     * @return the list
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ServerCertificates> describeCertificates(Long id) {
        ResLoadBalance resLoadBalance = resLoadBalanceMapper.selectByPrimaryKey(id);
        DecribeCertificates decribeCertificates = CloudClientFactory.buildMQBean(resLoadBalance.getCloudEnvId(),
                                                                                 DecribeCertificates.class);
        ServerCertificatesResult serverCertificatesResult = (ServerCertificatesResult) sendToMQRPC(decribeCertificates);
        if (!serverCertificatesResult.isSuccess()) {
            throw new BizException(CloudErrorResolver.getErrorMsg(serverCertificatesResult.getErrMsg()));
        }
        return serverCertificatesResult.getServerCertificates();
    }

    /**
     * Add backend servers boolean.
     *
     * @param resLbBackend the res lb backend
     *
     * @return the boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addBackendServers(ResLbBackend resLbBackend) {
        ResLoadBalance resLoadBalance = resLoadBalanceMapper.selectByPrimaryKey(resLbBackend.getResLbId());
        AssertUtil.requireNonBlank(resLoadBalance, "负载均衡底层资源已不存在，请重新选择。");
        resLbBackend.setExtId(resLbBackend.getExtId());
        ResLbListener resLbListener = resLbListenerMapper.selectByPrimaryKey(resLbBackend.getResListenerId());
        ResLbExt resLbExt = resLbExtMapper.selectByPrimaryKey(resLbBackend.getExtId());
        Criteria criteria = new Criteria();
        criteria.put("resListenerId", resLbBackend.getResListenerId());
        criteria.put("resVmId", resLbBackend.getResVmId());
        List<ResLbBackend> resLbBackends = resLbBackendMapper.selectByParams(criteria);

        List<String> portList = resLbBackend.getBackendServersList()
                                            .stream()
                                            .map(port -> String.valueOf(port.getPort()))
                                            .collect(Collectors.toList());
        List<String> managerPortList = resLbBackends.stream().map(ResLbBackend::getPort).collect(Collectors.toList());
        //同一实例下的同一端口不允许重复
        for (String port : portList) {
            if (managerPortList.contains(port)) {
                throw new BizException(port + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_479510779));
            }
        }
        this.cloudEnvService.checkCloudEnvNormal(resLoadBalance.getCloudEnvId(), true);
        List<ResVm> resVms = resVmMapper.selectHostsByEnvId(resLoadBalance.getCloudEnvId());
        Map<String, String> hostMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(resVms)) {
            resVms.forEach(ch -> {
                if (StringUtil.isNotBlank(ch.getInstanceId())) {
                    hostMap.put(ch.getInstanceId(), ch.getId());
                }
            });
        }
        BackendServerAdd backendServerAdd = CloudClientFactory.buildMQBean(resLoadBalance.getCloudEnvId(),
                                                                           BackendServerAdd.class);
        BackendServerAddResult backendServerAddResult = new BackendServerAddResult();
        if (CloudEnvType.ALIYUN.equals(backendServerAdd.getProviderType())) {
            backendServerAdd.setLoadBalanceId(resLoadBalance.getUuid());
            backendServerAdd.setBackendServers(JSON.toJSONString(resLbBackend.getBackendServersList()));
            backendServerAddResult = (BackendServerAddResult) sendToMQRPC(backendServerAdd);
        } else if (CloudEnvType.AWS.equals(backendServerAdd.getProviderType())) {
            backendServerAdd.setLoadBalanceId(resLoadBalance.getUuid());
            List<String> instanceIds = new ArrayList<>();
            resLbBackend.getBackendServersList().stream().forEach(server -> {
                instanceIds.add(server.getServerId());
            });
            backendServerAdd.setInstanceIds(instanceIds);
            backendServerAddResult = (BackendServerAddResult) sendToMQRPC(backendServerAdd);
        } else if (CloudEnvType.HUAWEICLOUD.equals(backendServerAdd.getProviderType())) {
            backendServerAdd.setLoadBalanceId(resLoadBalance.getUuid());
            backendServerAdd.setBusinessPort(Integer.parseInt(resLbBackend.getPort()));
            ResLbBackendGroup resLbBackendGroup = this.resLbBackendGroupMapper.selectByPrimaryKey(
                    resLbBackend.getResLbBackendGroupId());
            backendServerAdd.setBackendGroupUuid(resLbBackendGroup.getUuid());
            List<BackendServerAdd.InstanceWeight> instanceWeights = new ArrayList<>();
            List<BackendServers> backendServersList = resLbBackend.getBackendServersList();
            for (BackendServers server : backendServersList) {
                ResVm resVm = resVmMapper.selectByPrimaryKey(server.getResVmId());

                BackendServerAdd.InstanceWeight instanceWeight = new BackendServerAdd.InstanceWeight();
                List<Network> networks = networkMapper.selectByInstanceId(
                        new Criteria("cloudEnvId", resLoadBalance.getCloudEnvId()).put("instanceId", resVm.getId()));

                Optional<Network> first = networks.stream().findFirst();
                first.ifPresent(network -> instanceWeight.setSubnetId(network.getUuid()));
                instanceWeight.setInstanceIp(resVm.getInnerIp());
                instanceWeight.setWeight(server.getWeight());
                instanceWeight.setResVmId(server.getResVmId());
                instanceWeights.add(instanceWeight);
            }
            backendServerAdd.setInstanceWeights(instanceWeights);
            backendServerAddResult = (BackendServerAddResult) sendToMQRPC(backendServerAdd);
        } else if (CloudEnvType.CLOUDOS.equals(backendServerAdd.getProviderType()) || CloudEnvType.OPEN_STACK.equals(
                backendServerAdd.getProviderType())) {
            VServerGroupBackendServersAdd vServerGroupBackendServersAdd = CloudClientFactory.buildMQBean(
                    resLoadBalance.getCloudEnvId(), VServerGroupBackendServersAdd.class);
            ResLbBackendGroup resLbBackendGroup = this.resLbBackendGroupMapper.selectByPrimaryKey(
                    resLbBackend.getResLbBackendGroupId());
            vServerGroupBackendServersAdd.setvServerGroupId(resLbBackendGroup.getUuid());
            vServerGroupBackendServersAdd.setBackendServers(JSON.toJSONString(resLbBackend.getBackendServersList()));
            vServerGroupBackendServersAdd.setPort(Integer.parseInt(resLbBackend.getPort()));
            vServerGroupBackendServersAdd.setWeight(resLbBackend.getWeight());
            vServerGroupBackendServersAdd.setInnerIp(resLbBackend.getInnerIp());
            Network network = networkMapper.selectByPrimaryKey(Long.parseLong(resLbBackend.getNetworkId()));
            vServerGroupBackendServersAdd.setSubnetID(network.getUuid());
            backendServerAddResult = (BackendServerAddResult) sendToMQRPC(vServerGroupBackendServersAdd);

        } else if (CloudEnvType.QCLOUD.equals(backendServerAdd.getProviderType())) {
            backendServerAdd.setLoadBalanceId(resLoadBalance.getUuid());
            backendServerAdd.setListenerId(resLbListener.getUuid());
            if (resLbBackend.getExtId() != null) {
                backendServerAdd.setExtId(resLbExt.getExtUuid());
            }
            List<BackendServers> backendServers = resLbBackend.getBackendServersList();
            List<BackendServerList> backendList = new ArrayList<>();
            for (BackendServers backend : backendServers) {
                ResVm resVm = resVmMapper.selectByPrimaryKey(backend.getResVmId());
                BackendServerList backendServerList = new BackendServerList();
                backendServerList.setResVmId(resVm.getInstanceId());
                backendServerList.setPort(backend.getPort());
                backendServerList.setWeight(backend.getWeight());
                backendList.add(backendServerList);
            }
            backendServerAdd.setBackendServerList(backendList);
            backendServerAddResult = (BackendServerAddResult) sendToMQRPC(backendServerAdd);
        }
        if (!backendServerAddResult.isSuccess()) {
            basicResActionLogService.insertIntoActionLog(resLbBackend.getCreatedBy(), ResourceTypeEnum.RES_SLB_BACKEND,
                                                         ResourceOperateEnum.CREATE, Boolean.FALSE);

            if (CloudEnvType.ALIYUN.equals(backendServerAdd.getProviderType()) || CloudEnvType.QCLOUD.equals(
                    backendServerAdd.getProviderType())) {
                throw new BizException(CloudErrorResolver.getErrorMsg(backendServerAddResult.getErrMsg()));
            } else if (CloudEnvType.HUAWEICLOUD.equals(backendServerAdd.getProviderType())) {
                throw new BizException(CloudErrorResolver.getErrorMsg(backendServerAddResult.getErrMsg()));
            } else {
                return false;
            }
        } else {

            if (CloudEnvType.HUAWEICLOUD.equals(backendServerAdd.getProviderType())) {
                List<BackendServerAddResult.MemberInfo> memberList = backendServerAddResult.getMembers();
                if (!CollectionUtils.isEmpty(memberList)) {
                    for (BackendServerAddResult.MemberInfo memberInfo : memberList) {
                        ResLbBackend backendMember = new ResLbBackend();
                        backendMember.setUuid(memberInfo.getId());
                        backendMember.setPort(memberInfo.getProtocolPort() + "");
                        backendMember.setWeight(memberInfo.getWeight());
                        backendMember.setResVmId(memberInfo.getResVmId());
                        backendMember.setResLbId(resLbBackend.getResLbId());
                        backendMember.setResLbBackendGroupId(resLbBackend.getResLbBackendGroupId());
                        backendMember.setHcStatus(memberInfo.getHcStatus());
                        BasicWebUtil.prepareInsertParams(backendMember);
                        resLbBackendMapper.insertSelective(backendMember);
                    }
                }
            } else if (CloudEnvType.QCLOUD.equals(backendServerAdd.getProviderType())) {
                List<BackendServers> backendServersList = resLbBackend.getBackendServersList();
                for (BackendServers backend : backendServersList) {
                    resLbBackend.setId(null);
                    resLbBackend.setPort(backend.getPort().toString());
                    resLbBackend.setWeight(backend.getWeight());
                    resLbBackend.setResVmId(backend.getResVmId());
                    resLbBackendMapper.insertSelective(resLbBackend);
                    basicResActionLogService.insertIntoActionLog(resLbBackend.getId().toString(),
                                                                 resLbBackend.getCreatedBy(),
                                                                 ResourceTypeEnum.RES_SLB_BACKEND,
                                                                 ResourceOperateEnum.CREATE, Boolean.TRUE);
                }

            } else {
                resLbBackend.getBackendServersList().forEach(bsl -> {
                    resLbBackend.setPort(resLoadBalance.getListenerPort());
                    resLbBackend.setResVmId(hostMap.get(bsl.getServerId()));
                    resLbBackend.setUuid(resLbBackend.getResLbId().toString() + hostMap.get(bsl.getServerId())
                                                 + resLoadBalance.getListenerPort());
                    resLbBackend.setWeight(bsl.getWeight());
                    resLbBackendMapper.insertSelective(resLbBackend);
                    basicResActionLogService.insertIntoActionLog(resLbBackend.getId().toString(),
                                                                 resLbBackend.getCreatedBy(),
                                                                 ResourceTypeEnum.RES_SLB_BACKEND,
                                                                 ResourceOperateEnum.CREATE, Boolean.TRUE);
                    resLbBackend.setId(null);
                });
            }

            return true;
        }
    }

    /**
     * Sets backend server.
     *
     * @param resLbBackend the res lb backend
     *
     * @return the backend server
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setBackendServer(ResLbBackend resLbBackend) {
        String opUser = "";
        User authUser = BasicInfoUtil.getAuthUser();
        if (Objects.nonNull(authUser) && !StringUtil.isNullOrEmpty(authUser.getAccount())) {
            opUser = BasicInfoUtil.getAuthUser().getAccount();
        }
        ResLbBackend oldResLbBackend = resLbBackendMapper.selectByPrimaryKey(resLbBackend.getId());
        AssertUtil.requireNonBlank(oldResLbBackend, "负载均衡底层资源已不存在，请重新选择。");

        ResLoadBalance resLoadBalance = resLoadBalanceMapper.selectByPrimaryKey(oldResLbBackend.getResLbId());
        ResLbListener resLbListener = resLbListenerMapper.selectByPrimaryKey(oldResLbBackend.getResListenerId());
        ResLbExt resLbExt = resLbExtMapper.selectByPrimaryKey(oldResLbBackend.getExtId());

        AssertUtil.requireNonBlank(resLoadBalance, "负载均衡底层资源已不存在，请重新选择。");
        this.cloudEnvService.checkCloudEnvNormal(resLoadBalance.getCloudEnvId(), true);
        BackendServerSet backendServerSet = CloudClientFactory.buildMQBean(resLoadBalance.getCloudEnvId(),
                                                                           BackendServerSet.class);
        if (CloudEnvType.ALIYUN.equals(backendServerSet.getProviderType())) {
            backendServerSet.setLoadBalanceId(resLoadBalance.getUuid());
            backendServerSet.setBackendServers(JSON.toJSONString(resLbBackend.getBackendServersList()));
        } else if (CloudEnvType.CLOUDOS.equals(backendServerSet.getProviderType()) || CloudEnvType.OPEN_STACK.equals(
                backendServerSet.getProviderType())) {
            backendServerSet.setLoadBalanceId(resLoadBalance.getUuid());
            backendServerSet.setBackendServers(JSON.toJSONString(resLbBackend.getBackendServersList()));
        } else if (CloudEnvType.QCLOUD.equals(backendServerSet.getProviderType())) {
            backendServerSet.setLoadBalanceId(resLoadBalance.getUuid());
            backendServerSet.setResListenerId(resLbListener.getUuid());
            if (resLbBackend.getExtId() != null) {
                backendServerSet.setExtId(resLbExt.getExtUuid());
            }
            List<BackendServers> backendServers = resLbBackend.getBackendServersList();
            List<BackendServerList> backendList = new ArrayList<>();
            for (BackendServers backend : backendServers) {
                ResVm resVm = resVmMapper.selectByPrimaryKey(backend.getResVmId());
                BackendServerList backendServerList = new BackendServerList();
                backendServerList.setResVmId(resVm.getInstanceId());
                backendServerList.setPort(backend.getPort());
                backendServerList.setWeight(oldResLbBackend.getWeight());

                backendList.add(backendServerList);
                if (resLbBackend.getPort() != null) {
                    backendServerSet.setPort(Integer.valueOf(resLbBackend.getPort()));
                } else if (resLbBackend.getWeight() != null) {
                    backendServerSet.setWeight(resLbBackend.getWeight());
                }
            }
            backendServerSet.setBackendServerList(backendList);
        }
        BackendServerSetResult backendServerSetResult = (BackendServerSetResult) sendToMQRPC(backendServerSet);
        if (!backendServerSetResult.isSuccess()) {
            basicResActionLogService.insertIntoActionLog(resLbBackend.getId().toString(), opUser,
                                                         ResourceTypeEnum.RES_SLB_BACKEND, ResourceOperateEnum.MODIFY,
                                                         Boolean.TRUE);

            if (CloudEnvType.ALIYUN.equals(backendServerSet.getProviderType()) || CloudEnvType.QCLOUD.equals(
                    backendServerSet.getProviderType())) {
                throw new BizException(CloudErrorResolver.getErrorMsg(backendServerSetResult.getErrMsg()));
            } else {
                return false;
            }
        } else {
            if (CloudEnvType.QCLOUD.equals(backendServerSet.getProviderType())) {
                if (resLbBackend.getWeight() != null) {
                    Integer weight = resLbBackend.getWeight();
                    resLbBackend = resLbBackendMapper.selectByPrimaryKey(resLbBackend.getId());
                    resLbBackend.setWeight(weight);
                } else {
                    String port = resLbBackend.getPort();
                    resLbBackend = resLbBackendMapper.selectByPrimaryKey(resLbBackend.getId());
                    resLbBackend.setPort(port);
                }

            } else {
                resLbBackend.setWeight(resLbBackend.getBackendServersList().get(0).getWeight());
            }
            int actionLogId = basicResActionLogService.insertIntoActionLog(resLbBackend.getId().toString(), opUser,
                                                                           ResourceTypeEnum.RES_SLB_BACKEND,
                                                                           ResourceOperateEnum.MODIFY, Boolean.TRUE);

            resLbBackendMapper.updateByPrimaryKeySelective(resLbBackend);

            basicResActionLogService.updateActionLog(actionLogId, ResourceTypeEnum.RES_SLB_BACKEND,
                                                     resLbBackend.getId().toString(), ResourceOperateEnum.MODIFY);
            return true;
        }
    }

    /**
     * Delete backend server boolean.
     *
     * @param resLbBackend the res lb backend
     *
     * @return the boolean
     */
    @Override
    public boolean deleteBackendServer(ResLbBackend resLbBackend) {
        String opUser = "";
        User authUser = BasicInfoUtil.getAuthUser();
        if (Objects.nonNull(authUser) && !StringUtil.isNullOrEmpty(authUser.getAccount())) {
            opUser = BasicInfoUtil.getAuthUser().getAccount();
        }

        ResLbBackend resLbBackendModel = resLbBackendMapper.selectByPrimaryKey(resLbBackend.getId());
        resLbBackend.setExtId(resLbBackendModel.getExtId());
        ResLoadBalance resLoadBalance = resLoadBalanceMapper.selectByPrimaryKey(resLbBackend.getResLbId());
        AssertUtil.requireNonBlank(resLoadBalance, "负载均衡底层资源已不存在，请重新选择。");

        ResLbListener resLbListener = resLbListenerMapper.selectByPrimaryKey(resLbBackend.getResListenerId());
        ResLbExt resLbExt = resLbExtMapper.selectByPrimaryKey(resLbBackend.getExtId());
        this.cloudEnvService.checkCloudEnvNormal(resLoadBalance.getCloudEnvId(), true);
        BackendServerRemove backendServerRemove = CloudClientFactory.buildMQBean(resLoadBalance.getCloudEnvId(),
                                                                                 BackendServerRemove.class);
        if (CloudEnvType.ALIYUN.equals(backendServerRemove.getProviderType())) {
            backendServerRemove.setLoadBalanceId(resLoadBalance.getUuid());
            backendServerRemove.setBackendServers(JSON.toJSONString(resLbBackend.getBackendServersList()));
        } else if (CloudEnvType.AWS.equals(backendServerRemove.getProviderType())) {
            backendServerRemove.setLoadBalanceId(resLoadBalance.getUuid());
            List<String> instanceIds = new ArrayList<>();
            resLbBackend.getBackendServersList().stream().forEach(server -> {
                instanceIds.add(server.getServerId());
            });
            backendServerRemove.setInstanceIds(instanceIds);
        } else if (CloudEnvType.HUAWEICLOUD.equals(backendServerRemove.getProviderType())) {
            ResLbBackend backend = this.resLbBackendMapper.selectByPrimaryKey(resLbBackend.getId());
            backendServerRemove.setMemId(backend.getUuid());

            ResLbBackendGroup resLbBackendGroup = this.resLbBackendGroupMapper.selectByPrimaryKey(
                    backend.getResLbBackendGroupId());
            backendServerRemove.setResLbBackendGroupId(resLbBackendGroup.getUuid());
        } else if (CloudEnvType.QCLOUD.equals(backendServerRemove.getProviderType())) {
            backendServerRemove.setLoadBalanceId(resLoadBalance.getUuid());
            backendServerRemove.setListenerId(resLbListener.getUuid());
            if (resLbBackend.getExtId() != null) {
                backendServerRemove.setExtId(resLbExt.getExtUuid());
            }
            List<BackendServerList> serverLists = new ArrayList<>();
            resLbBackend.getBackendServersList().forEach(backend -> {
                ResVm resVm = resVmMapper.selectByPrimaryKey(backend.getResVmId());
                BackendServerList backendServer = new BackendServerList();
                backendServer.setResVmId(resVm.getInstanceId());
                backendServer.setPort(backend.getPort());
                serverLists.add(backendServer);
            });
            backendServerRemove.setBackendServerList(serverLists);
        }
        BackendServerRemoveResult backendServerRemoveResult = (BackendServerRemoveResult) sendToMQRPC(
                backendServerRemove);
        if (!backendServerRemoveResult.isSuccess()) {
            basicResActionLogService.insertIntoActionLog(resLbBackend.getId().toString(), opUser,
                                                         ResourceTypeEnum.RES_SLB_BACKEND, ResourceOperateEnum.DELETE,
                                                         Boolean.FALSE);
            if (CloudEnvType.ALIYUN.equals(backendServerRemove.getProviderType()) || CloudEnvType.HUAWEICLOUD.equals(
                    backendServerRemove.getProviderType())) {
                throw new BizException(CloudErrorResolver.getErrorMsg(backendServerRemoveResult.getErrMsg()));
            } else {
                return false;
            }
        } else {
            basicResActionLogService.insertIntoActionLog(resLbBackend.getId().toString(), opUser,
                                                         ResourceTypeEnum.RES_SLB_BACKEND, ResourceOperateEnum.DELETE,
                                                         Boolean.TRUE);
            resLbBackendMapper.deleteByPrimaryKey(resLbBackend.getId());
            return true;
        }
    }

    /**
     * Add master group boolean.
     *
     * @param resLbBackendGroup the res lb backend group
     *
     * @return the boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addMasterGroup(ResLbBackendGroup resLbBackendGroup) {
        String opUser = "";
        User authUser = BasicInfoUtil.getAuthUser();
        if (Objects.nonNull(authUser) && !StringUtil.isNullOrEmpty(authUser.getAccount())) {
            opUser = BasicInfoUtil.getAuthUser().getAccount();
        }

        ResLoadBalance resLoadBalance = resLoadBalanceMapper.selectByPrimaryKey(resLbBackendGroup.getResLbId());
        AssertUtil.requireNonBlank(resLoadBalance, "负载均衡底层资源已不存在，请重新选择。");
        this.cloudEnvService.checkCloudEnvNormal(resLoadBalance.getCloudEnvId(), true);

        List<ResVm> resVms = resVmMapper.selectHostsByEnvId(resLoadBalance.getCloudEnvId());
        Map<String, String> hostMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(resVms)) {
            resVms.forEach(ch -> {
                if (StringUtil.isNotBlank(ch.getInstanceId())) {
                    hostMap.put(ch.getInstanceId(), ch.getId());
                }
            });
        }
        MasterSlaveServerGroupCreate masterSlaveServerGroupCreate = CloudClientFactory.buildMQBean(
                resLoadBalance.getCloudEnvId(), MasterSlaveServerGroupCreate.class);
        if (CloudEnvType.ALIYUN.equals(masterSlaveServerGroupCreate.getProviderType())) {
            masterSlaveServerGroupCreate.setLoadBalanceId(resLoadBalance.getUuid());
            masterSlaveServerGroupCreate.setMasterSlaveServerGroupName(resLbBackendGroup.getGroupName());
            masterSlaveServerGroupCreate.setMasterSlaveBackendServers(
                    JSON.toJSONString(resLbBackendGroup.getMasterSlaveBackendServersList()));
        }
        MasterSlaveServerGroupCreateResult masterSlaveServerGroupCreateResult = (MasterSlaveServerGroupCreateResult) sendToMQRPC(
                masterSlaveServerGroupCreate);
        if (!masterSlaveServerGroupCreateResult.isSuccess()) {
            basicResActionLogService.insertIntoActionLog(opUser, ResourceTypeEnum.RES_SLB_BACKEND_GROUP,
                                                         ResourceOperateEnum.CREATE, Boolean.FALSE);

            if (CloudEnvType.ALIYUN.equals(masterSlaveServerGroupCreate.getProviderType())) {
                throw new BizException(CloudErrorResolver.getErrorMsg(masterSlaveServerGroupCreateResult.getErrMsg()));
            } else {
                return false;
            }
        } else {
            resLbBackendGroup.setUuid(masterSlaveServerGroupCreateResult.getMasterSlaveServerGroupId());
            resLbBackendGroupMapper.insertSelective(resLbBackendGroup);

            basicResActionLogService.insertIntoActionLog(resLbBackendGroup.getId().toString(), opUser,
                                                         ResourceTypeEnum.RES_SLB_BACKEND_GROUP,
                                                         ResourceOperateEnum.CREATE, Boolean.TRUE);

            resLbBackendGroup.getMasterSlaveBackendServersList().forEach(r -> {
                ResLbBackend resLbBackend = new ResLbBackend();
                resLbBackend.setResLbId(resLbBackendGroup.getResLbId());
                resLbBackend.setResLbBackendGroupId(resLbBackendGroup.getId());
                resLbBackend.setResVmId(hostMap.get(r.getServerId()));
                resLbBackend.setPort(r.getPort().toString());
                resLbBackend.setWeight(r.getWeight());
                resLbBackend.setServerType(r.getServerType());
                resLbBackend.setCreatedDt(resLbBackendGroup.getCreatedDt());
                resLbBackend.setCreatedBy(resLbBackendGroup.getCreatedBy());
                resLbBackend.setUpdatedBy(resLbBackendGroup.getUpdatedBy());
                resLbBackend.setUpdatedDt(resLbBackendGroup.getUpdatedDt());
                resLbBackend.setUuid(
                        resLbBackendGroup.getResLbId().toString() + resLbBackendGroup.getId().toString() + hostMap.get(
                                r.getServerId()) + r.getPort().toString());
                resLbBackendMapper.insertSelective(resLbBackend);
            });
            return true;
        }
    }

    /**
     * Add v server group boolean.
     *
     * @param resLbBackendGroup the res lb backend group
     *
     * @return the boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addVServerGroup(ResLbBackendGroup resLbBackendGroup) {
        String opUser = "";
        User authUser = BasicInfoUtil.getAuthUser();
        if (Objects.nonNull(authUser) && !StringUtil.isNullOrEmpty(authUser.getAccount())) {
            opUser = BasicInfoUtil.getAuthUser().getAccount();
        }

        ResLoadBalance resLoadBalance = resLoadBalanceMapper.selectByPrimaryKey(resLbBackendGroup.getResLbId());
        AssertUtil.requireNonBlank(resLoadBalance, "负载均衡底层资源已不存在，请重新选择。");
        this.cloudEnvService.checkCloudEnvNormal(resLoadBalance.getCloudEnvId(), true);

        List<ResVm> resVms = resVmMapper.selectHostsByEnvId(resLoadBalance.getCloudEnvId());
        Map<String, String> hostMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(resVms)) {
            resVms.forEach(ch -> {
                if (StringUtil.isNotBlank(ch.getInstanceId())) {
                    hostMap.put(ch.getInstanceId(), ch.getId());
                }
            });
        }
        VServerGroupCreate vServerGroupCreate = CloudClientFactory.buildMQBean(resLoadBalance.getCloudEnvId(),
                                                                               VServerGroupCreate.class);
        if (CloudEnvType.ALIYUN.equals(vServerGroupCreate.getProviderType())) {
            vServerGroupCreate.setLoadBalanceId(resLoadBalance.getUuid());
            vServerGroupCreate.setvServerGroupName(resLbBackendGroup.getGroupName());
            vServerGroupCreate.setBackendServers(JSON.toJSONString(resLbBackendGroup.getVserverBackendServersList()));
        }
        VServerGroupCreateResult vServerGroupCreateResult = (VServerGroupCreateResult) sendToMQRPC(vServerGroupCreate);
        if (!vServerGroupCreateResult.isSuccess()) {
            basicResActionLogService.insertIntoActionLog(opUser, ResourceTypeEnum.RES_SLB_BACKEND_GROUP,
                                                         ResourceOperateEnum.CREATE, Boolean.TRUE);

            throw new BizException(CloudErrorResolver.getErrorMsg(vServerGroupCreateResult.getErrMsg()));
        } else {
            resLbBackendGroup.setUuid(vServerGroupCreateResult.getVsGroupId());
            resLbBackendGroupMapper.insertSelective(resLbBackendGroup);

            basicResActionLogService.insertIntoActionLog(resLbBackendGroup.getId().toString(), opUser,
                                                         ResourceTypeEnum.RES_SLB_BACKEND_GROUP,
                                                         ResourceOperateEnum.CREATE, Boolean.TRUE);

            resLbBackendGroup.getVserverBackendServersList().forEach(r -> {
                ResLbBackend resLbBackend = new ResLbBackend();
                resLbBackend.setResLbId(resLbBackendGroup.getResLbId());
                resLbBackend.setResLbBackendGroupId(resLbBackendGroup.getId());
                resLbBackend.setResVmId(hostMap.get(r.getServerId()));
                resLbBackend.setPort(r.getPort().toString());
                resLbBackend.setWeight(r.getWeight());
                resLbBackend.setCreatedDt(resLbBackendGroup.getCreatedDt());
                resLbBackend.setCreatedBy(resLbBackendGroup.getCreatedBy());
                resLbBackend.setUpdatedDt(resLbBackendGroup.getUpdatedDt());
                resLbBackend.setUpdatedBy(resLbBackendGroup.getUpdatedBy());
                resLbBackend.setUuid(
                        resLbBackendGroup.getResLbId().toString() + resLbBackendGroup.getId().toString() + hostMap.get(
                                r.getServerId()) + r.getPort().toString());
                resLbBackendMapper.insertSelective(resLbBackend);
            });
            return true;
        }
    }

    /**
     * Add pool group boolean.
     *
     * @param resLbBackendGroup the res lb backend group
     *
     * @return the boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addPoolGroup(ResLbBackendGroup resLbBackendGroup) {
        String opUser = "";
        User authUser = BasicInfoUtil.getAuthUser();
        if (Objects.nonNull(authUser) && !StringUtil.isNullOrEmpty(authUser.getAccount())) {
            opUser = BasicInfoUtil.getAuthUser().getAccount();
        }
        Criteria example = new Criteria();
        example.put("resLbId", resLbBackendGroup.getResLbId());
        example.put("groupName", resLbBackendGroup.getGroupName());
        int count = resLbBackendGroupMapper.countByParams(example);
        if (count > 0) {
            logger.info("后端服务组[{}]已存在", resLbBackendGroup.getGroupName());
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1291774483));
        }
        VServerGroupCreate vServerGroupCreate = null;
        if (resLbBackendGroup.getResLbListenerId() == null) {
            ResLoadBalance resLoadBalance = resLoadBalanceMapper.selectByPrimaryKey(resLbBackendGroup.getResLbId());
            AssertUtil.requireNonBlank(resLoadBalance, "负载均衡底层资源已不存在，请重新选择。");
            vServerGroupCreate = CloudClientFactory.buildMQBean(resLoadBalance.getCloudEnvId(),
                                                                VServerGroupCreate.class);
            vServerGroupCreate.setName(resLbBackendGroup.getGroupName());
            vServerGroupCreate.setDescription(resLbBackendGroup.getDescription());
            vServerGroupCreate.setLoadBalanceId(resLoadBalance.getUuid());
            vServerGroupCreate.setListenerUuid(null);
            vServerGroupCreate.setLbMethod(resLbBackendGroup.getScheduler());
            vServerGroupCreate.setProtocol(null);
            vServerGroupCreate.setBackendProtocol(resLbBackendGroup.getBackendProtocol());
            vServerGroupCreate.setStickySession(resLbBackendGroup.getStickySession());
            vServerGroupCreate.setStickySessionType(resLbBackendGroup.getStickySessionType());
            vServerGroupCreate.setCookieTimeout(resLbBackendGroup.getCookieTimeout());
            vServerGroupCreate.setCookieName(resLbBackendGroup.getCookieName());
            vServerGroupCreate.setHealthCheck(resLbBackendGroup.getHealthCheck());
            vServerGroupCreate.setHcType(resLbBackendGroup.getHcType());
            vServerGroupCreate.setHcDomain(resLbBackendGroup.getHcDomain());
            vServerGroupCreate.setHcPort(resLbBackendGroup.getHcPort());
            vServerGroupCreate.setHcInterval(resLbBackendGroup.getHcInterval());
            vServerGroupCreate.setHcTimeout(resLbBackendGroup.getHealthCheckTimeout());
            vServerGroupCreate.setUnhcThreshold(resLbBackendGroup.getUnhcThreshold());
            vServerGroupCreate.setHcThreshold(resLbBackendGroup.getHcThreshold());
            vServerGroupCreate.setHcUri(resLbBackendGroup.getHcUri());
        } else {
            ResLbListener resLbListener = resLbListenerMapper.selectByPrimaryKey(
                    resLbBackendGroup.getResLbListenerId());
            AssertUtil.requireNonBlank(resLbListener, "负载均衡监听底层资源已不存在，请重新选择。");
            ResLoadBalance resLoadBalance = resLoadBalanceMapper.selectByPrimaryKey(resLbListener.getResLbId());
            AssertUtil.requireNonBlank(resLoadBalance, "负载均衡底层资源已不存在，请重新选择。");
            vServerGroupCreate = CloudClientFactory.buildMQBean(resLoadBalance.getCloudEnvId(),
                                                                VServerGroupCreate.class);
            vServerGroupCreate.setName(resLbBackendGroup.getGroupName());
            vServerGroupCreate.setDescription(resLbBackendGroup.getDescription());
            vServerGroupCreate.setLoadBalanceId(resLoadBalance.getUuid());
            vServerGroupCreate.setListenerUuid(resLbListener.getUuid());
            vServerGroupCreate.setLbMethod(resLbBackendGroup.getScheduler());
            vServerGroupCreate.setProtocol(resLbListener.getProtocol());
            vServerGroupCreate.setBackendProtocol(resLbBackendGroup.getBackendProtocol());
            vServerGroupCreate.setStickySession(resLbBackendGroup.getStickySession());
            vServerGroupCreate.setStickySessionType(resLbBackendGroup.getStickySessionType());
            vServerGroupCreate.setCookieTimeout(resLbBackendGroup.getCookieTimeout());
            vServerGroupCreate.setCookieName(resLbBackendGroup.getCookieName());
            vServerGroupCreate.setHealthCheck(resLbBackendGroup.getHealthCheck());
            vServerGroupCreate.setHcType(resLbBackendGroup.getHcType());
            vServerGroupCreate.setHcDomain(resLbBackendGroup.getHcDomain());
            vServerGroupCreate.setHcPort(resLbBackendGroup.getHcPort());
            vServerGroupCreate.setHcInterval(resLbBackendGroup.getHcInterval());
            vServerGroupCreate.setHcTimeout(resLbBackendGroup.getHealthCheckTimeout());
            vServerGroupCreate.setUnhcThreshold(resLbBackendGroup.getUnhcThreshold());
            vServerGroupCreate.setHcThreshold(resLbBackendGroup.getHcThreshold());
            vServerGroupCreate.setHcUri(resLbBackendGroup.getHcUri());
        }

        VServerGroupCreateResult vServerGroupCreateResult = (VServerGroupCreateResult) sendToMQRPC(vServerGroupCreate);
        if (!vServerGroupCreateResult.isSuccess()) {
            basicResActionLogService.insertIntoActionLog(opUser, ResourceTypeEnum.RES_SLB_BACKEND_GROUP,
                                                         ResourceOperateEnum.CREATE, Boolean.FALSE);

            if (CloudEnvType.HUAWEICLOUD.equals(vServerGroupCreate.getProviderType())) {
                throw new BizException(vServerGroupCreateResult.getErrMsg());
            } else if (CloudEnvType.OPEN_STACK.equals(vServerGroupCreate.getProviderType())) {
                throw new BizException(CloudErrorResolver.getRegexErrorMsg(vServerGroupCreateResult.getErrMsg()));
            }

            return false;
        } else {
            ResLbExt resLbExt = new ResLbExt();
            resLbExt.setHcThreshold(resLbBackendGroup.getHcThreshold());
            resLbExt.setStickySessionType(resLbBackendGroup.getStickySessionType());
            resLbExt.setStickySession(resLbBackendGroup.getStickySession());
            resLbExt.setHealthCheck(resLbBackendGroup.getHealthCheck());
            resLbExt.setUnhcThreshold(resLbBackendGroup.getUnhcThreshold());
            resLbExt.setHcUri(resLbBackendGroup.getHcUri());
            resLbExt.setHcHttpCode(resLbBackendGroup.getHcHttpCode());
            resLbExt.setHcInterval(resLbBackendGroup.getHcInterval());
            resLbExt.setHcTimeout(resLbBackendGroup.getHealthCheckTimeout());
            resLbExt.setCookieName(resLbBackendGroup.getCookieName());
            resLbExt.setCookieTimeout(resLbBackendGroup.getCookieTimeout());
            resLbExt.setHcUuid(vServerGroupCreateResult.getHcId());
            resLbExt.setHcType(resLbBackendGroup.getHcType());
            resLbExtMapper.insertSelective(resLbExt);
            resLbBackendGroup.setResLbExtId(resLbExt.getId());
            resLbBackendGroup.setStatus(NetworkStatus.ACTIVE);
            resLbBackendGroup.setUuid(vServerGroupCreateResult.getVsGroupId());
            resLbBackendGroupMapper.insertSelective(resLbBackendGroup);

            basicResActionLogService.insertIntoActionLog(resLbBackendGroup.getId().toString(), opUser,
                                                         ResourceTypeEnum.RES_SLB_BACKEND_GROUP,
                                                         ResourceOperateEnum.CREATE, Boolean.TRUE);
            return true;
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePoolGroup(ResLbBackendGroup resLbBackendGroup) {
        String opUser = "";
        User authUser = BasicInfoUtil.getAuthUser();
        if (Objects.nonNull(authUser) && !StringUtil.isNullOrEmpty(authUser.getAccount())) {
            opUser = BasicInfoUtil.getAuthUser().getAccount();
        }
        ResLbBackendGroup record = resLbBackendGroupMapper.selectByPrimaryKey(resLbBackendGroup.getId());
        if (Objects.isNull(record)) {
            throw new BizException(StrUtil.format(MsgCd.ERROR_RES_NOT_FOUND, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_35658772)));
        }
        VServerGroupUpdate vServerGroupUpdate = null;
        ResLbListener resLbListener = resLbListenerMapper.selectByPrimaryKey(resLbBackendGroup.getResLbListenerId());
        AssertUtil.requireNonBlank(resLbListener, "负载均衡监听底层资源已不存在，请重新选择。");
        ResLoadBalance resLoadBalance = resLoadBalanceMapper.selectByPrimaryKey(resLbListener.getResLbId());
        AssertUtil.requireNonBlank(resLoadBalance, "负载均衡底层资源已不存在，请重新选择。");
        ResLbExt resLbExtRecord = resLbExtMapper.selectByPrimaryKey(resLbBackendGroup.getResLbExtId());
        AssertUtil.requireNonBlank(resLbExtRecord, "资源池配置信息已不存在，请刷新重试");
        vServerGroupUpdate = CloudClientFactory.buildMQBean(resLoadBalance.getCloudEnvId(), VServerGroupUpdate.class);

        vServerGroupUpdate.setUuid(record.getUuid());
        vServerGroupUpdate.setHealthCheck(resLbBackendGroup.getHealthCheck());
        vServerGroupUpdate.setHcUuid(resLbExtRecord.getHcUuid());
        if (resLbBackendGroup.getHealthCheck()) {
            vServerGroupUpdate.setHcType(resLbBackendGroup.getHcType());
            vServerGroupUpdate.setHcDomain(resLbBackendGroup.getHcDomain());
            vServerGroupUpdate.setHcPort(resLbBackendGroup.getHcPort());
            vServerGroupUpdate.setHcInterval(resLbBackendGroup.getHcInterval());
            vServerGroupUpdate.setHcTimeout(resLbBackendGroup.getHealthCheckTimeout());
            vServerGroupUpdate.setUnhcThreshold(resLbBackendGroup.getUnhcThreshold());
            vServerGroupUpdate.setHcThreshold(resLbBackendGroup.getHcThreshold());
            vServerGroupUpdate.setHcUri(resLbBackendGroup.getHcUri());
        }
        vServerGroupUpdate.setName(resLbBackendGroup.getGroupName());
        vServerGroupUpdate.setDescription(resLbBackendGroup.getDescription());
        vServerGroupUpdate.setLoadBalanceId(resLoadBalance.getUuid());
        vServerGroupUpdate.setLbMethod(resLbBackendGroup.getScheduler());
        vServerGroupUpdate.setStickySession(resLbBackendGroup.getStickySession());
        if (resLbBackendGroup.getStickySession()) {
            vServerGroupUpdate.setStickySessionType(resLbBackendGroup.getStickySessionType());
            vServerGroupUpdate.setCookieTimeout(resLbBackendGroup.getCookieTimeout());
            vServerGroupUpdate.setCookieName(resLbBackendGroup.getCookieName());
        }
        VServerGroupCreateResult vServerGroupCreateResult = (VServerGroupCreateResult) sendToMQRPC(vServerGroupUpdate);
        if (!vServerGroupCreateResult.isSuccess()) {
            basicResActionLogService.insertIntoActionLog(opUser, ResourceTypeEnum.RES_SLB_BACKEND_GROUP,
                                                         ResourceOperateEnum.MODIFY, Boolean.FALSE);

            if (CloudEnvType.HUAWEICLOUD.equals(vServerGroupUpdate.getProviderType())) {
                throw new BizException(vServerGroupCreateResult.getErrMsg());
            } else if (CloudEnvType.OPEN_STACK.equals(vServerGroupUpdate.getProviderType())) {
                throw new BizException(CloudErrorResolver.getRegexErrorMsg(vServerGroupCreateResult.getErrMsg()));
            }

            return false;
        } else {
            ResLbExt resLbExt = new ResLbExt();
            resLbExt.setId(resLbBackendGroup.getResLbExtId());
            resLbExt.setHealthCheck(resLbBackendGroup.getHealthCheck());
            resLbExt.setHcThreshold(resLbBackendGroup.getHcThreshold());
            resLbExt.setUnhcThreshold(resLbBackendGroup.getUnhcThreshold());
            resLbExt.setHcUri(resLbBackendGroup.getHcUri());
            resLbExt.setHcHttpCode(resLbBackendGroup.getHcHttpCode());
            resLbExt.setHcInterval(resLbBackendGroup.getHcInterval());
            resLbExt.setHcTimeout(resLbBackendGroup.getHealthCheckTimeout());
            resLbExt.setHcType(resLbBackendGroup.getHcType());
            if (resLbBackendGroup.getHealthCheck()) {
                resLbExt.setHcUuid(vServerGroupCreateResult.getHcId());
            } else {
                resLbExt.setHcUuid("");
            }
            resLbExt.setStickySession(resLbBackendGroup.getStickySession());
            if (resLbBackendGroup.getStickySession()) {
                resLbExt.setStickySessionType(resLbBackendGroup.getStickySessionType());
                resLbExt.setCookieName(resLbBackendGroup.getCookieName());
                resLbExt.setCookieTimeout(resLbBackendGroup.getCookieTimeout());
            }
            resLbExtMapper.updateByPrimaryKeySelective(resLbExt);
            resLbBackendGroup.setStatus(NetworkStatus.ACTIVE);
            resLbBackendGroupMapper.updateByPrimaryKeySelective(resLbBackendGroup);

            basicResActionLogService.insertIntoActionLog(resLbBackendGroup.getId().toString(), opUser,
                                                         ResourceTypeEnum.RES_SLB_BACKEND_GROUP,
                                                         ResourceOperateEnum.MODIFY, Boolean.TRUE);
            return true;
        }

    }

    /**
     * Add pool member boolean.
     *
     * @param resLbBackend the res lb backend
     *
     * @return the boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addPoolMember(ResLbBackend resLbBackend) {
        String opUser = "";
        User authUser = BasicInfoUtil.getAuthUser();
        if (Objects.nonNull(authUser) && !StringUtil.isNullOrEmpty(authUser.getAccount())) {
            opUser = BasicInfoUtil.getAuthUser().getAccount();
        }

        ResLbBackendGroup resLbBackendGroup = this.resLbBackendGroupMapper.selectByPrimaryKey(
                resLbBackend.getResLbBackendGroupId());
        AssertUtil.requireNonBlank(resLbBackendGroup, "负载均衡资源组底层资源已不存在，请重新选择。");
        ResLoadBalance resLoadBalance = resLoadBalanceMapper.selectByPrimaryKey(resLbBackendGroup.getResLbId());
        AssertUtil.requireNonBlank(resLoadBalance, "负载均衡底层资源已不存在，请重新选择。");
        this.cloudEnvService.checkCloudEnvNormal(resLoadBalance.getCloudEnvId(), true);

        Criteria criteria = new Criteria();
        criteria.put("resLbBackendGroupId", resLbBackend.getResLbBackendGroupId());
        criteria.put("resVmId", resLbBackend.getResVmId());
        List<String> portList = resLbBackendMapper.selectByParams(criteria)
                                                  .stream()
                                                  .map(ResLbBackend::getPort)
                                                  .collect(Collectors.toList());
        if (portList.contains(resLbBackend.getPort())) {
            logger.info("被使用端口:[{}]", resLbBackend.getPort());
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1258147309));
        }
        ResVm resVm = resVmMapper.selectSimpleByPrimaryKey(resLbBackend.getResVmId());
        criteria.clear();
        criteria.put("type", "subnet");
        criteria.put("instanceId", resVm.getId());
        List<ResVmExt> resVmExts = resVmExtMapper.selectByParams(criteria);
        if (!CollectionUtils.isEmpty(resVmExts)) {
            Network network = networkMapper.selectByPrimaryKey(Long.valueOf(resVmExts.get(0).getResourceId()));
            resLbBackend.setNetworkId(network.getUuid());
        }
        VServerGroupBackendServersAdd vServerGroupBackendServersAdd = CloudClientFactory.buildMQBean(
                resLoadBalance.getCloudEnvId(), VServerGroupBackendServersAdd.class);
        vServerGroupBackendServersAdd.setvServerGroupId(resLbBackendGroup.getUuid());
        vServerGroupBackendServersAdd.setSubnetID(resLbBackend.getNetworkId());
        vServerGroupBackendServersAdd.setInnerIp(resLbBackend.getInnerIp());
        vServerGroupBackendServersAdd.setPort(Integer.parseInt(resLbBackend.getPort()));
        vServerGroupBackendServersAdd.setWeight(resLbBackend.getWeight());
        vServerGroupBackendServersAdd.setMemberName(resVm.getInstanceName());
        VServerGroupBackendServersAddResult vServerGroupBackendServersAddResult = (VServerGroupBackendServersAddResult) sendToMQRPC(
                vServerGroupBackendServersAdd);
        if (!vServerGroupBackendServersAddResult.isSuccess()) {
            basicResActionLogService.insertIntoActionLog(opUser, ResourceTypeEnum.RES_SLB_BACKEND,
                                                         ResourceOperateEnum.CREATE, Boolean.FALSE);

            return false;
        } else {
            resLbBackend.setUuid(vServerGroupBackendServersAddResult.getId());
            resLbBackend.setStatus(NetworkStatus.ACTIVE);
            resLbBackend.setMemberName(resVm.getInstanceName());
            resLbBackend.setInnerIp(resLbBackend.getInnerIp());
            this.resLbBackendMapper.insertSelective(resLbBackend);

            basicResActionLogService.insertIntoActionLog(resLbBackend.getId().toString(), opUser,
                                                         ResourceTypeEnum.RES_SLB_BACKEND, ResourceOperateEnum.CREATE,
                                                         Boolean.TRUE);
            return true;
        }
    }

    /**
     * 批量添加
     *
     * @param resLbBackends the res lb backend
     *
     * @return the boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addPoolMembers(List<ResLbBackend> resLbBackends) {
        String opUser = "";
        User authUser = BasicInfoUtil.getAuthUser();
        if (Objects.nonNull(authUser) && !StringUtil.isNullOrEmpty(authUser.getAccount())) {
            opUser = BasicInfoUtil.getAuthUser().getAccount();
        }
        ResLbBackendGroup resLbBackendGroup = this.resLbBackendGroupMapper.selectByPrimaryKey(
                resLbBackends.get(0).getResLbBackendGroupId());
        AssertUtil.requireNonBlank(resLbBackendGroup, "负载均衡资源组底层资源已不存在，请重新选择。");
        ResLoadBalance resLoadBalance = resLoadBalanceMapper.selectByPrimaryKey(resLbBackendGroup.getResLbId());
        AssertUtil.requireNonBlank(resLoadBalance, "负载均衡底层资源已不存在，请重新选择。");
        this.cloudEnvService.checkCloudEnvNormal(resLoadBalance.getCloudEnvId(), true);
        Criteria criteria = new Criteria();
        List<VServerGrouplbServersAdd> vServerGrouplbServersAdds = new ArrayList<>();
        resLbBackends.stream().forEach(resLbBackend -> {
            criteria.put("resLbBackendGroupId", resLbBackend.getResLbBackendGroupId());
            criteria.put("resVmId", resLbBackend.getResVmId());
            List<String> portList = resLbBackendMapper.selectByParams(criteria)
                                                      .stream()
                                                      .map(ResLbBackend::getPort)
                                                      .collect(Collectors.toList());
            if (portList.contains(resLbBackend.getPort())) {
                logger.info("被使用端口:[{}]", resLbBackend.getPort());
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1258147309));
            }
            ResVm resVm = resVmMapper.selectSimpleByPrimaryKey(resLbBackend.getResVmId());
            criteria.clear();
            criteria.put("type", "subnet");
            criteria.put("instanceId", resVm.getId());
            List<ResVmExt> resVmExts = resVmExtMapper.selectByParams(criteria);
            if (!CollectionUtils.isEmpty(resVmExts)) {
                Network network = networkMapper.selectByPrimaryKey(Long.valueOf(resVmExts.get(0).getResourceId()));
                resLbBackend.setNetworkId(network.getUuid());
            }
            resLbBackend.setMemberName(resVm.getInstanceName());
            this.resLbBackendMapper.insertSelective(resLbBackend);
            VServerGrouplbServersAdd vServerGrouplbServersAdd = new VServerGrouplbServersAdd();
            vServerGrouplbServersAdd.setSubnetID(resLbBackend.getNetworkId());
            vServerGrouplbServersAdd.setInnerIp(resLbBackend.getInnerIp());
            vServerGrouplbServersAdd.setPort(Integer.parseInt(resLbBackend.getPort()));
            vServerGrouplbServersAdd.setWeight(resLbBackend.getWeight());
            vServerGrouplbServersAdd.setMemberName(resVm.getInstanceName());
            vServerGrouplbServersAdd.setId(resLbBackend.getId());
            vServerGrouplbServersAdds.add(vServerGrouplbServersAdd);
        });
        VServerGroupBackendServersAdds vServerGroupBackendServersAdds = CloudClientFactory.buildMQBean(
                resLoadBalance.getCloudEnvId(), VServerGroupBackendServersAdds.class);
        vServerGroupBackendServersAdds.setvServerGroupId(resLbBackendGroup.getUuid());
        vServerGroupBackendServersAdds.setvServerGrouplbServersAddList(vServerGrouplbServersAdds);
        vServerGroupBackendServersAdds.setLbUuid(resLoadBalance.getUuid());
        vServerGroupBackendServersAdds.setId(resLoadBalance.getId());
        sendToMQ(vServerGroupBackendServersAdds);
        return true;
    }


    /**
     * Remove pool member boolean.
     *
     * @param groupId the group id
     * @param memberId the member id
     *
     * @return the boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removePoolMember(Long groupId, Long memberId) {
        String opUser = "";
        User authUser = BasicInfoUtil.getAuthUser();
        if (Objects.nonNull(authUser) && !StringUtil.isNullOrEmpty(authUser.getAccount())) {
            opUser = BasicInfoUtil.getAuthUser().getAccount();
        }

        ResLbBackend resLbBackend = this.resLbBackendMapper.selectByPrimaryKey(memberId);
        ResLbBackendGroup resLbBackendGroup = this.resLbBackendGroupMapper.selectByPrimaryKey(groupId);
        ResLoadBalance resLoadBalance = resLoadBalanceMapper.selectByPrimaryKey(resLbBackendGroup.getResLbId());
        VServerGroupBackendServersRemove vServerGroupBackendServersRemove = CloudClientFactory.buildMQBean(
                resLoadBalance.getCloudEnvId(), VServerGroupBackendServersRemove.class);
        vServerGroupBackendServersRemove.setMemberId(resLbBackend.getUuid());
        vServerGroupBackendServersRemove.setvServerGroupId(resLbBackendGroup.getUuid());
        VServerGroupBackendServersRemoveResult vServerGroupBackendServersRemoveResult = (VServerGroupBackendServersRemoveResult) sendToMQRPC(
                vServerGroupBackendServersRemove);
        if (!vServerGroupBackendServersRemoveResult.isSuccess()) {
            basicResActionLogService.insertIntoActionLog(memberId.toString(), opUser, ResourceTypeEnum.RES_SLB_BACKEND,
                                                         ResourceOperateEnum.DELETE, Boolean.FALSE);

            return false;
        } else {

            basicResActionLogService.insertIntoActionLog(memberId.toString(), opUser, ResourceTypeEnum.RES_SLB_BACKEND,
                                                         ResourceOperateEnum.DELETE, Boolean.TRUE);
            this.resLbBackendMapper.deleteByPrimaryKey(memberId);
            return true;
        }
    }

    /**
     * Update pool member boolean.
     *
     * @param resLbBackend the res lb backend
     *
     * @return the boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePoolMember(ResLbBackend resLbBackend) {
        String opUser = "";
        User authUser = BasicInfoUtil.getAuthUser();
        if (Objects.nonNull(authUser) && !StringUtil.isNullOrEmpty(authUser.getAccount())) {
            opUser = BasicInfoUtil.getAuthUser().getAccount();
        }

        ResLbBackend resLbBackendModel = resLbBackendMapper.selectByPrimaryKey(resLbBackend.getId());
        ResLbBackendGroup resLbBackendGroup = this.resLbBackendGroupMapper.selectByPrimaryKey(
                resLbBackend.getResLbBackendGroupId());
        ResLoadBalance resLoadBalance = resLoadBalanceMapper.selectByPrimaryKey(resLbBackendGroup.getResLbId());
        ResVm resVm = resVmMapper.selectBaseByPrimaryKey(resLbBackendModel.getResVmId());
        BackendServerSet backendServerSet = CloudClientFactory.buildMQBean(resLoadBalance.getCloudEnvId(),
                                                                           BackendServerSet.class);

        backendServerSet.setGroupId(resLbBackendGroup.getUuid());
        backendServerSet.setMemberId(resLbBackendModel.getUuid());
        backendServerSet.setStatus(NetworkStatus.ACTIVE.equals(resLbBackend.getStatus()));
        backendServerSet.setWeight(resLbBackend.getWeight());
        BackendServerSetResult backendServerSetResult = (BackendServerSetResult) sendToMQRPC(backendServerSet);
        if (!backendServerSetResult.isSuccess()) {
            basicResActionLogService.insertIntoActionLog(BasicInfoUtil.getAuthUser().getAccount(),
                                                         ResourceOperateEnum.MODIFY, ResourceTypeEnum.RES_SLB_BACKEND,
                                                         BasicInfoUtil.getCurrentOrgSid().toString(),
                                                         resVm.getInstanceName(), Boolean.FALSE, null, null,
                                                         BasicInfoUtil.getCurrentOrgSid());

            return false;
        } else {
            this.resLbBackendMapper.updateByPrimaryKeySelective(resLbBackend);
            basicResActionLogService.insertIntoActionLog(BasicInfoUtil.getAuthUser().getAccount(),
                                                         ResourceOperateEnum.MODIFY, ResourceTypeEnum.RES_SLB_BACKEND,
                                                         BasicInfoUtil.getCurrentOrgSid().toString(),
                                                         resVm.getInstanceName(), Boolean.TRUE,
                                                         JsonUtil.toJson(resLbBackendModel), JsonUtil.toJson(
                            resLbBackendMapper.selectByPrimaryKey(resLbBackend.getId())),
                                                         BasicInfoUtil.getCurrentOrgSid());
            return true;
        }
    }

    /**
     * Update v server group boolean.
     *
     * @param resLbBackendGroup the res lb backend group
     *
     * @return the boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateVServerGroup(ResLbBackendGroup resLbBackendGroup) {
        User authUser = BasicInfoUtil.getAuthUser();
        String opUser = (Objects.nonNull(authUser) && !StringUtil.isNullOrEmpty(authUser.getAccount()))
                        ? BasicInfoUtil.getAuthUser().getAccount() : "";

        ResLoadBalance resLoadBalance = resLoadBalanceMapper.selectByPrimaryKey(resLbBackendGroup.getResLbId());
        AssertUtil.requireNonBlank(resLoadBalance, "负载均衡底层资源已不存在，请重新选择。");
        this.cloudEnvService.checkCloudEnvNormal(resLoadBalance.getCloudEnvId(), true);
        ResLbBackendGroup group = this.resLbBackendGroupMapper.selectByPrimaryKey(resLbBackendGroup.getId());
        AssertUtil.requireNonBlank(group, "负载均衡虚拟服务器组底层资源已不存在，请重新选择。");
        List<ResVm> resVms = resVmMapper.selectHostsByEnvId(resLoadBalance.getCloudEnvId());
        Map<String, String> hostMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(resVms)) {
            resVms.forEach(ch -> {
                if (StringUtil.isNotBlank(ch.getInstanceId())) {
                    hostMap.put(ch.getInstanceId(), ch.getId());
                }
            });
        }
        CloudEnv cloudEnv = cloudEnvMapper.selectByPrimaryKey(resLoadBalance.getCloudEnvId());
        if (CloudEnvType.ALIYUN.equals(cloudEnv.getCloudEnvType())) {
            /**
             * 首先查询出旧的虚拟服务器组以及虚拟服务器组下面的servers
             * 然后和传入的服务器组以及传入服务器组下面的servers进行对比，然后比较出来哪些需要操作的东西
             */
            ResLbBackendGroup old = resLbBackendGroupMapper.selectByPrimaryKey(resLbBackendGroup.getId());
            List<ResLbBackend> oldServers = resLbBackendMapper.selectByParams(
                    new Criteria("resLbBackendGroupId", resLbBackendGroup.getId()));
            if (!CollectionUtils.isEmpty(oldServers)) {
                oldServers.forEach(os -> {
                    ResVm resVm = resVmMapper.selectByPrimaryKey(os.getResVmId());
                    os.setServerId(resVm.getInstanceId());
                });
            }
            List<VserverBackendServers> vserverBackendServersList = resLbBackendGroup.getVserverBackendServersList();
            /**
             * 此处创建五个个list  分别用于存放新增删除以及修改的对象 以及需要删除更新的服务器
             */
            List<VserverBackendServers> addServer = new ArrayList<>();
            List<VserverBackendServers> deleteServer = new ArrayList<>();
            List<VserverBackendServers> updateServer = new ArrayList<>();
            List<Long> deleteIds = new ArrayList<>();
            List<ResLbBackend> resLbBackends = new ArrayList<>();
            if (!CollectionUtils.isEmpty(oldServers)) {
                for (ResLbBackend resLbBackend : oldServers) {
                    boolean opFlag = false;
                    if (!CollectionUtils.isEmpty(vserverBackendServersList)) {
                        for (VserverBackendServers server : vserverBackendServersList) {
                            if (StringUtil.isNotBlank(resLbBackend.getCode())) {
                                if (resLbBackend.getCode().equals(server.getCode())) {
                                    //如果相同的code下面权重不相同，那么加入到update中去
                                    if (!server.getWeight().equals(resLbBackend.getWeight())) {
                                        updateServer.add(server);
                                        resLbBackends.add(resLbBackend);
                                    }
                                    vserverBackendServersList.remove(server);
                                    opFlag = true;
                                    break;
                                }
                            }
                        }
                        if (!opFlag) {
                            VserverBackendServers vserverBackendServers = new VserverBackendServers();
                            vserverBackendServers.setPort(Integer.valueOf(resLbBackend.getPort()));
                            vserverBackendServers.setServerId(resLbBackend.getServerId());
                            vserverBackendServers.setWeight(resLbBackend.getWeight());
                            deleteServer.add(vserverBackendServers);
                            deleteIds.add(resLbBackend.getId());
                        }
                    } else {
                        VserverBackendServers vserverBackendServers = new VserverBackendServers();
                        vserverBackendServers.setServerId(resLbBackend.getServerId());
                        vserverBackendServers.setPort(Integer.valueOf(resLbBackend.getPort()));
                        vserverBackendServers.setWeight(resLbBackend.getWeight());
                        deleteServer.add(vserverBackendServers);
                        deleteIds.add(resLbBackend.getId());
                    }
                }
            }
            //新增加入数据库中不存在的数据
            if (!CollectionUtils.isEmpty(vserverBackendServersList)) {
                for (VserverBackendServers servers : vserverBackendServersList) {
                    addServer.add(servers);
                }
            }
            /**
             * 组装好三个对象之后,分别判断是否为空，判定是否需要调接口进行相关update操作
             * */
            if (!CollectionUtils.isEmpty(addServer)) {
                VServerGroupBackendServersAdd vServerGroupBackendServersAdd = CloudClientFactory.buildMQBean(
                        cloudEnv.getId(), VServerGroupBackendServersAdd.class);
                vServerGroupBackendServersAdd.setRegion(cloudEnv.getRegion());
                vServerGroupBackendServersAdd.setvServerGroupId(group.getUuid());
                vServerGroupBackendServersAdd.setBackendServers(JSON.toJSONString(addServer));
                VServerGroupBackendServersAddResult vServerGroupBackendServersAddResult = (VServerGroupBackendServersAddResult) sendToMQRPC(
                        vServerGroupBackendServersAdd);
                if (!vServerGroupBackendServersAddResult.isSuccess()) {
                    basicResActionLogService.insertIntoActionLog(opUser, ResourceTypeEnum.RES_SLB_BACKEND,
                                                                 ResourceOperateEnum.CREATE, Boolean.FALSE);

                    throw new BizException(
                            CloudErrorResolver.getErrorMsg(vServerGroupBackendServersAddResult.getErrMsg()));
                } else {
                    addServer.forEach(adds -> {
                        ResLbBackend resLbBackend = new ResLbBackend();
                        resLbBackend.setResLbId(resLbBackendGroup.getResLbId());
                        resLbBackend.setResLbBackendGroupId(resLbBackendGroup.getId());
                        resLbBackend.setPort(adds.getPort().toString());
                        resLbBackend.setResVmId(hostMap.get(adds.getServerId()));
                        resLbBackend.setWeight(adds.getWeight());
                        resLbBackend.setCreatedDt(resLbBackendGroup.getCreatedDt());
                        resLbBackend.setUpdatedDt(resLbBackendGroup.getUpdatedDt());
                        resLbBackend.setUpdatedBy(resLbBackendGroup.getUpdatedBy());
                        resLbBackend.setCreatedBy(resLbBackendGroup.getCreatedBy());

                        resLbBackend.setUuid(
                                resLbBackendGroup.getResLbId() + resLbBackend.getResVmId() + resLbBackend.getPort() + (
                                        Objects.isNull(resLbBackend.getResLbBackendGroupId()) ? ""
                                                                                              : resLbBackend.getResLbBackendGroupId()));

                        resLbBackendMapper.insertSelective(resLbBackend);

                        basicResActionLogService.insertIntoActionLog(resLbBackend.getId().toString(), opUser,
                                                                     ResourceTypeEnum.RES_SLB_BACKEND,
                                                                     ResourceOperateEnum.CREATE, Boolean.TRUE);
                    });
                }
            }
            if (!CollectionUtils.isEmpty(deleteServer)) {
                VServerGroupBackendServersRemove vServerGroupBackendServersRemove = CloudClientFactory.buildMQBean(
                        cloudEnv.getId(), VServerGroupBackendServersRemove.class);
                vServerGroupBackendServersRemove.setRegion(cloudEnv.getRegion());
                vServerGroupBackendServersRemove.setvServerGroupId(group.getUuid());
                vServerGroupBackendServersRemove.setBackendServers(JSON.toJSONString(deleteServer));
                VServerGroupBackendServersRemoveResult vServerGroupBackendServersRemoveResult = (VServerGroupBackendServersRemoveResult) sendToMQRPC(
                        vServerGroupBackendServersRemove);
                if (!vServerGroupBackendServersRemoveResult.isSuccess()) {
                    deleteIds.forEach(id -> {
                        basicResActionLogService.insertIntoActionLog(id.toString(), opUser,
                                                                     ResourceTypeEnum.RES_SLB_BACKEND,
                                                                     ResourceOperateEnum.DELETE, Boolean.FALSE);
                    });

                    throw new BizException(
                            CloudErrorResolver.getErrorMsg(vServerGroupBackendServersRemoveResult.getErrMsg()));
                } else {
                    deleteIds.forEach(id -> {
                        basicResActionLogService.insertIntoActionLog(id.toString(), opUser,
                                                                     ResourceTypeEnum.RES_SLB_BACKEND,
                                                                     ResourceOperateEnum.DELETE, Boolean.TRUE);

                        resLbBackendMapper.deleteByPrimaryKey(id);
                    });
                }
            }
            if (!CollectionUtils.isEmpty(updateServer) || !group.getGroupName()
                                                                .equals(resLbBackendGroup.getGroupName())) {
                VServerGroupAttributeSet vServerGroupAttributeSet = CloudClientFactory.buildMQBean(cloudEnv.getId(),
                                                                                                   VServerGroupAttributeSet.class);
                if (!CollectionUtils.isEmpty(updateServer)) {
                    vServerGroupAttributeSet.setBackendServers(JSON.toJSONString(updateServer));
                }
                vServerGroupAttributeSet.setRegion(cloudEnv.getRegion());
                vServerGroupAttributeSet.setvServerGroupId(group.getUuid());
                vServerGroupAttributeSet.setvServerGroupName(resLbBackendGroup.getGroupName());
                VServerGroupAttributeSetResult serverGroupAttributeSetResult = (VServerGroupAttributeSetResult) sendToMQRPC(
                        vServerGroupAttributeSet);
                if (!serverGroupAttributeSetResult.isSuccess()) {
                    resLbBackends.forEach(rbs -> basicResActionLogService.insertIntoActionLog(rbs.toString(), opUser,
                                                                                              ResourceTypeEnum.RES_SLB_BACKEND,
                                                                                              ResourceOperateEnum.MODIFY,
                                                                                              Boolean.FALSE));
                    throw new BizException(CloudErrorResolver.getErrorMsg(serverGroupAttributeSetResult.getErrMsg()));
                } else {
                    resLbBackends.forEach(rbs -> {
                        int actionLogId = basicResActionLogService.insertIntoActionLog(rbs.toString(), opUser,
                                                                                       ResourceTypeEnum.RES_SLB_BACKEND,
                                                                                       ResourceOperateEnum.MODIFY,
                                                                                       Boolean.TRUE);

                        updateServer.forEach(ds -> {
                            if (rbs.getCode().equals(ds.getCode())) {
                                rbs.setWeight(ds.getWeight());
                                BasicWebUtil.prepareUpdateParams(rbs);
                                resLbBackendMapper.updateByPrimaryKeySelective(rbs);
                            }
                        });

                        basicResActionLogService.updateActionLog(actionLogId, ResourceTypeEnum.RES_SLB_BACKEND,
                                                                 rbs.toString(), ResourceOperateEnum.MODIFY);

                    });
                }
            }
            if (!old.getGroupName().equals(resLbBackendGroup.getGroupName())) {
                old.setGroupName(resLbBackendGroup.getGroupName());
                BasicWebUtil.prepareUpdateParams(old);
                resLbBackendGroupMapper.updateByPrimaryKeySelective(old);
            }
            return true;
        }
        return false;
    }

    /**
     * Change pool status boolean.
     *
     * @param id the id
     * @param status the status
     *
     * @return the boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean changePoolStatus(Long id, Boolean status) {
        String opUser = "";
        User authUser = BasicInfoUtil.getAuthUser();
        if (Objects.nonNull(authUser) && !StringUtil.isNullOrEmpty(authUser.getAccount())) {
            opUser = BasicInfoUtil.getAuthUser().getAccount();
        }

        ResLbBackendGroup resLbBackendGroup = this.resLbBackendGroupMapper.selectByPrimaryKey(id);
        AssertUtil.requireNonBlank(resLbBackendGroup, "负载均衡资源组底层资源已不存在，请重新选择。");
        ResLoadBalance resLoadBalance = this.resLoadBalanceMapper.selectByPrimaryKey(resLbBackendGroup.getResLbId());
        AssertUtil.requireNonBlank(resLoadBalance, "负载均衡底层资源已不存在，请重新选择。");
        this.cloudEnvService.checkCloudEnvNormal(resLoadBalance.getCloudEnvId(), true);
        VServerGroupAttributeSet vServerGroupAttributeSet = CloudClientFactory.buildMQBean(
                resLoadBalance.getCloudEnvId(), VServerGroupAttributeSet.class);
        vServerGroupAttributeSet.setvServerGroupId(resLbBackendGroup.getUuid());
        vServerGroupAttributeSet.setStatus(status);
        VServerGroupAttributeSetResult vServerGroupAttributeSetResult = (VServerGroupAttributeSetResult) sendToMQRPC(
                vServerGroupAttributeSet);
        if (!vServerGroupAttributeSetResult.isSuccess()) {
            basicResActionLogService.insertIntoActionLog(resLbBackendGroup.getId().toString(), opUser,
                                                         ResourceTypeEnum.RES_SLB_BACKEND_GROUP,
                                                         status ? ResourceOperateEnum.START : ResourceOperateEnum.STOP,
                                                         Boolean.FALSE);

            return false;
        } else {
            int actionLogId = basicResActionLogService.insertIntoActionLog(resLbBackendGroup.getId().toString(), opUser,
                                                                           ResourceTypeEnum.RES_SLB_BACKEND_GROUP,
                                                                           status ? ResourceOperateEnum.START
                                                                                  : ResourceOperateEnum.STOP,
                                                                           Boolean.TRUE);

            if (status) {
                resLbBackendGroup.setStatus(NetworkStatus.ACTIVE);
            } else {
                resLbBackendGroup.setStatus(NetworkStatus.INACTIVE);
            }
            this.resLbBackendGroupMapper.updateByPrimaryKeySelective(resLbBackendGroup);

            basicResActionLogService.updateActionLog(actionLogId, ResourceTypeEnum.RES_SLB_BACKEND_GROUP,
                                                     resLbBackendGroup.getId().toString(),
                                                     status ? ResourceOperateEnum.START : ResourceOperateEnum.STOP);

            return true;
        }
    }

    /**
     * Delete master group boolean.
     *
     * @param id the id
     *
     * @return the boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteMasterGroup(Long id) {
        String opUser = "";
        User authUser = BasicInfoUtil.getAuthUser();
        if (Objects.nonNull(authUser) && !StringUtil.isNullOrEmpty(authUser.getAccount())) {
            opUser = BasicInfoUtil.getAuthUser().getAccount();
        }

        ResLbBackendGroup resLbBackendGroup = resLbBackendGroupMapper.selectByPrimaryKey(id);
        ResLoadBalance resLoadBalance = resLoadBalanceMapper.selectByPrimaryKey(resLbBackendGroup.getResLbId());
        MasterSlaveServerGroupDelete masterSlaveServerGroupDelete = CloudClientFactory.buildMQBean(
                resLoadBalance.getCloudEnvId(), MasterSlaveServerGroupDelete.class);
        if (CloudEnvType.ALIYUN.equals(masterSlaveServerGroupDelete.getProviderType())) {
            masterSlaveServerGroupDelete.setMasterSlaveServerGroupId(resLbBackendGroup.getUuid());
        } else {
            return false;
        }
        MasterSlaveServerGroupDeleteResult masterSlaveServerGroupCreateResult = (MasterSlaveServerGroupDeleteResult) sendToMQRPC(
                masterSlaveServerGroupDelete);
        if (!masterSlaveServerGroupCreateResult.isSuccess()) {
            basicResActionLogService.insertIntoActionLog(opUser, ResourceTypeEnum.RES_SLB_BACKEND_GROUP,
                                                         ResourceOperateEnum.DELETE, Boolean.FALSE);

            if (CloudEnvType.ALIYUN.equals(masterSlaveServerGroupDelete.getProviderType())) {
                throw new BizException(CloudErrorResolver.getErrorMsg(masterSlaveServerGroupCreateResult.getErrMsg()));
            } else {
                return false;
            }
        } else {
            basicResActionLogService.insertIntoActionLog(id.toString(), opUser, ResourceTypeEnum.RES_SLB_BACKEND_GROUP,
                                                         ResourceOperateEnum.DELETE, Boolean.TRUE);

            this.resLbBackendMapper.deleteByParams(new Criteria("resLbBackendGroupId", id));
            resLbBackendGroupMapper.deleteByPrimaryKey(id);
            return true;
        }
    }

    /**
     * Delete vserver group boolean.
     *
     * @param id the id
     *
     * @return the boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteVserverGroup(Long id) {
        String opUser = "";
        User authUser = BasicInfoUtil.getAuthUser();
        if (Objects.nonNull(authUser) && !StringUtil.isNullOrEmpty(authUser.getAccount())) {
            opUser = BasicInfoUtil.getAuthUser().getAccount();
        }
        ResLbBackendGroup resLbBackendGroup = resLbBackendGroupMapper.selectByPrimaryKey(id);
        AssertUtil.requireNonBlank(resLbBackendGroup, "负载均衡虚拟服务器组底层资源已不存在，请重新选择。");
        ResLoadBalance resLoadBalance = resLoadBalanceMapper.selectByPrimaryKey(resLbBackendGroup.getResLbId());
        AssertUtil.requireNonBlank(resLoadBalance, "负载均衡底层资源已不存在，请重新选择。");

        this.cloudEnvService.checkCloudEnvNormal(resLoadBalance.getCloudEnvId(), true);
        VServerGroupDelete vServerGroupDelete = CloudClientFactory.buildMQBean(resLoadBalance.getCloudEnvId(),
                                                                               VServerGroupDelete.class);
        if (CloudEnvType.ALIYUN.equals(vServerGroupDelete.getProviderType())) {
            vServerGroupDelete.setvServerGroupId(resLbBackendGroup.getUuid());
        } else if (CloudEnvType.OPEN_STACK.equals(vServerGroupDelete.getProviderType()) || CloudEnvType.CLOUDOS.equals(
                vServerGroupDelete.getProviderType())) {
            vServerGroupDelete.setvServerGroupId(resLbBackendGroup.getUuid());
            ResLbExt ext = resLbExtMapper.selectByPrimaryKey(resLbBackendGroup.getResLbExtId());
            // 如果有 healthMonitorId 则需要传递, 删除之前，需要先删除healthMonitor
            if (ext != null && StringUtil.isNotBlank(ext.getHcUuid())) {
                vServerGroupDelete.setOptions(ImmutableMap.of("hcUuid", ext.getHcUuid()));
            }
        } else if (CloudEnvType.HUAWEICLOUD.equals(vServerGroupDelete.getProviderType())) {
            vServerGroupDelete.setvServerGroupId(resLbBackendGroup.getUuid());
        }
        VServerGroupDeleteResult vServerGroupDeleteResult = (VServerGroupDeleteResult) sendToMQRPC(vServerGroupDelete);
        if (!vServerGroupDeleteResult.isSuccess()) {
            basicResActionLogService.insertIntoActionLog(id.toString(), opUser, ResourceTypeEnum.RES_SLB_BACKEND_GROUP,
                                                         ResourceOperateEnum.DELETE, Boolean.FALSE);

            if (CloudEnvType.ALIYUN.equals(vServerGroupDelete.getProviderType())) {
                throw new BizException(CloudErrorResolver.getErrorMsg(vServerGroupDeleteResult.getErrMsg()));
            } else if (CloudEnvType.HUAWEICLOUD.equals(vServerGroupDelete.getProviderType())) {
                throw new BizException(CloudErrorResolver.getErrorMsg(vServerGroupDeleteResult.getErrMsg()));
            } else {
                throw new BizException(vServerGroupDeleteResult.getErrMsg());
            }
        } else {
            basicResActionLogService.insertIntoActionLog(id.toString(), opUser, ResourceTypeEnum.RES_SLB_BACKEND_GROUP,
                                                         ResourceOperateEnum.DELETE, Boolean.TRUE);

            if (CloudEnvType.OPEN_STACK.equals(vServerGroupDelete.getProviderType())) {
                resLbExtMapper.deleteByPrimaryKey(resLbBackendGroup.getResLbExtId());
            } else if (CloudEnvType.ALIYUN.equals(vServerGroupDelete.getProviderType())) {
                this.resLbBackendMapper.deleteByParams(new Criteria("resLbBackendGroupId", id));
            } else if (CloudEnvType.HUAWEICLOUD.equals(vServerGroupDelete.getProviderType())) {
                this.resLbBackendMapper.deleteByParams(new Criteria("resLbBackendGroupId", id));
            }
            resLbBackendGroupMapper.deleteByPrimaryKey(id);
            return true;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public boolean removeResourceFromDBByEnvId(Long cloudEnvId) {
        Criteria envId = new Criteria("cloudEnvId", cloudEnvId);
        List<ResLoadBalance> loadBalances = resLoadBalanceMapper.selectByParams(envId);

        if (!CollectionUtils.isEmpty(loadBalances)) {
            List<Long> lbIds = loadBalances.stream().map(ResLoadBalance::getId).collect(Collectors.toList());

            Criteria loadBalanceIds = new Criteria("resLbIds", lbIds);
            // 后端服务器
            resLbBackendMapper.deleteByParams(loadBalanceIds);

            // 后端服务器组
            List<ResLbBackendGroup> resLbBackendGroups = resLbBackendGroupMapper.selectByParams(loadBalanceIds);

            List<Long> lbExtIds = resLbBackendGroups.stream()
                                                    .filter(resLbBackendGroup -> !Objects.isNull(
                                                            resLbBackendGroup.getResLbExtId()))
                                                    .map(ResLbBackendGroup::getResLbExtId)
                                                    .collect(Collectors.toList());

            resLbBackendGroupMapper.deleteByParams(loadBalanceIds);

            // 监听
            List<ResLbListener> resLbListeners = resLbListenerMapper.selectByParams(loadBalanceIds);

            lbExtIds.addAll(resLbListeners.stream()
                                          .filter(listener -> !Objects.isNull(listener.getResLbExtId()))
                                          .map(ResLbListener::getResLbExtId)
                                          .collect(Collectors.toList()));

            resLbListenerMapper.deleteByParams(loadBalanceIds);

            // lb_ext
            if (!CollectionUtils.isEmpty(lbExtIds)) {
                resLbExtMapper.deleteByParams(new Criteria("resLbExtIds", lbExtIds));
            }

            // lb
            resLoadBalanceMapper.deleteByParams(envId);
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createLoadBalanceRule(ResLbExt resLbExt, Long cloudEnvId) {
        String opUser = "";
        User authUser = BasicInfoUtil.getAuthUser();
        if (Objects.nonNull(authUser) && !StringUtil.isNullOrEmpty(authUser.getAccount())) {
            opUser = BasicInfoUtil.getAuthUser().getAccount();
        }

        ResLoadBalance resLoadBalance = resLoadBalanceMapper.selectByPrimaryKey(resLbExt.getResLoadBalanceId());
        ResLbListener resLbListener = resLbListenerMapper.selectByPrimaryKey(resLbExt.getResListenerId());
        LbRuleCreate lbRuleCreate = CloudClientFactory.buildMQBean(resLbExt.getCloudEnvId(), LbRuleCreate.class);

        if (CloudEnvType.QCLOUD.equals(lbRuleCreate.getProviderType())) {
            lbRuleCreate.setLoadBalancerId(resLoadBalance.getUuid());
            lbRuleCreate.setListenerId(resLbListener.getUuid());
            lbRuleCreate.setSessionExpireTime(resLbExt.getStickySessionTime());
            lbRuleCreate.setDomain(resLbExt.getForwardDomain());
            lbRuleCreate.setUrl(resLbExt.getForwardUrl());
            lbRuleCreate.setScheduler(resLbExt.getScheduler());
            lbRuleCreate.setHealthSwitch(resLbExt.getHealthCheck() ? 1 : 0);
            //规则的健康检查
            if (resLbExt.getHealthCheck()) {
                lbRuleCreate.setIntervalTime(resLbExt.getHcInterval());
                lbRuleCreate.setTimeOut(resLbExt.getHcTimeout());
                lbRuleCreate.setHealthNum(resLbExt.getHcThreshold());
                lbRuleCreate.setUnhealthNum(resLbExt.getUnhcThreshold());
                lbRuleCreate.setHttpCheckDomain(resLbExt.getHcDomain());
                lbRuleCreate.setHttpCheckUrl(resLbExt.getHcUri());
                lbRuleCreate.setHttpCheckCode(resLbExt.getHcHttpCode());
                lbRuleCreate.setHttpCheckMethod(resLbExt.getHcHttpMethod());
            }

        }
        LbRuleCreateResult lbRuleCreateResult = (LbRuleCreateResult) sendToMQRPC(lbRuleCreate);
        if (!lbRuleCreateResult.isSuccess()) {
            basicResActionLogService.insertIntoActionLog(opUser, ResourceTypeEnum.RES_SLB_EXT,
                                                         ResourceOperateEnum.CREATE, Boolean.FALSE);
            throw new BizException(lbRuleCreateResult.getErrMsg());
        } else {
            resLbExt.setExtUuid(lbRuleCreateResult.getLocationId());
            resLbExtMapper.insert(resLbExt);
            return true;
        }
    }

    @Override
    public boolean deleteLoadBalanceRule(Long lbId, Long ruleId) {
        String opUser = "";
        User authUser = BasicInfoUtil.getAuthUser();
        if (Objects.nonNull(authUser) && !StringUtil.isNullOrEmpty(authUser.getAccount())) {
            opUser = BasicInfoUtil.getAuthUser().getAccount();
        }

        ResLoadBalance resLoadBalance = resLoadBalanceMapper.selectByPrimaryKey(lbId);
        AssertUtil.requireNonBlank(resLoadBalance, "负载均衡底层资源已不存在，请重新选择。");
        this.cloudEnvService.checkCloudEnvNormal(resLoadBalance.getCloudEnvId(), true);

        ResLbExt resLbExt = resLbExtMapper.selectByPrimaryKey(ruleId);
        ResLbListener resLbListener = resLbListenerMapper.selectByPrimaryKey(resLbExt.getResListenerId());
        AssertUtil.requireNonBlank(resLoadBalance, "监听底层资源已不存在，请重新选择。");
        LbRuleDelete lbRuleDelete = CloudClientFactory.buildMQBean(resLoadBalance.getCloudEnvId(), LbRuleDelete.class);
        if (CloudEnvType.QCLOUD.equals(lbRuleDelete.getProviderType())) {
            lbRuleDelete.setListenerId(resLbListener.getUuid());
            lbRuleDelete.setLoadBalanceId(resLoadBalance.getUuid());
            lbRuleDelete.setDomain(resLbExt.getForwardDomain());
            lbRuleDelete.setUrl(resLbExt.getForwardUrl());
        }
        LbRuleDeleteResult lbRuleDeleteResult = (LbRuleDeleteResult) sendToMQRPC(lbRuleDelete);
        if (!lbRuleDeleteResult.isSuccess()) {
            basicResActionLogService.insertIntoActionLog(opUser, ResourceTypeEnum.RES_SLB_EXT,
                                                         ResourceOperateEnum.CREATE, Boolean.FALSE);
            throw new BizException(lbRuleDeleteResult.getErrMsg());
        } else {
            resLbExtMapper.deleteByPrimaryKey(ruleId);
            return true;
        }
    }

    @Override
    public boolean resetLoadBalanceRule(ResLbExt resLbExt) {
        String opUser = "";
        User authUser = BasicInfoUtil.getAuthUser();
        if (Objects.nonNull(authUser) && !StringUtil.isNullOrEmpty(authUser.getAccount())) {
            opUser = BasicInfoUtil.getAuthUser().getAccount();
        }

        ResLoadBalance resLoadBalance = resLoadBalanceMapper.selectByPrimaryKey(resLbExt.getResLoadBalanceId());
        ResLbListener resLbListener = resLbListenerMapper.selectByPrimaryKey(resLbExt.getResListenerId());
        ResLbExt resLbExtModel = resLbExtMapper.selectByPrimaryKey(resLbExt.getId());
        LbRuleUpdate lbRuleUpdate = CloudClientFactory.buildMQBean(resLbExt.getCloudEnvId(), LbRuleUpdate.class);

        if (CloudEnvType.QCLOUD.equals(lbRuleUpdate.getProviderType())) {
            lbRuleUpdate.setLoadBalancerId(resLoadBalance.getUuid());
            lbRuleUpdate.setListenerId(resLbListener.getUuid());
            lbRuleUpdate.setSessionExpireTime(resLbExt.getStickySessionTime());
            lbRuleUpdate.setNewDomain(resLbExt.getForwardDomain());
            lbRuleUpdate.setDomain(resLbExtModel.getForwardDomain());
            lbRuleUpdate.setUrl(resLbExt.getForwardUrl());
            lbRuleUpdate.setScheduler(resLbExt.getScheduler());
            lbRuleUpdate.setHealthSwitch(resLbExt.getHealthCheck() ? 1 : 0);
            lbRuleUpdate.setExtId(resLbExtModel.getExtUuid());

            if (resLbExt.getHealthCheck()) {
                lbRuleUpdate.setIntervalTime(resLbExt.getHcInterval());
                lbRuleUpdate.setTimeOut(resLbExt.getHcTimeout());
                lbRuleUpdate.setHealthNum(resLbExt.getHcThreshold());
                lbRuleUpdate.setUnhealthNum(resLbExt.getUnhcThreshold());
                lbRuleUpdate.setHttpCheckDomain(resLbExt.getHcDomain());
                lbRuleUpdate.setHttpCheckUrl(resLbExt.getHcUri());
                lbRuleUpdate.setHttpCheckCode(resLbExt.getHcHttpCode());
                lbRuleUpdate.setHttpCheckMethod(resLbExt.getHcHttpMethod());
            }

        }
        LbRuleUpdateResult lbRuleUpdateResult = (LbRuleUpdateResult) sendToMQRPC(lbRuleUpdate);
        if (!lbRuleUpdateResult.isSuccess()) {
            basicResActionLogService.insertIntoActionLog(opUser, ResourceTypeEnum.RES_SLB_EXT,
                                                         ResourceOperateEnum.MODIFY, Boolean.FALSE);
            throw new BizException(lbRuleUpdateResult.getErrMsg());
        } else {
            resLbExt.setForwardDomain(resLbExtModel.getForwardDomain());
            resLbExtMapper.updateByPrimaryKeySelective(resLbExt);
            return true;
        }
    }

    @Override
    public RestResult renewInstance(RenewBasicResourceDTO rbr, AuthUser authUser) {
        ResLoadBalance resLoadBalance = this.resLoadBalanceMapper.selectByPrimaryKey(Long.valueOf(rbr.getResourceId()));
        AssertUtil.requireNonBlank(resLoadBalance, "该快照不存在,请刷新后重新");
        // 没有结束时间是永久资源，不修改
        if (null != resLoadBalance.getEndTime()) {
            Date newEndTime = cn.com.cloudstar.rightcloud.common.util.DateUtil.plusMonths(resLoadBalance.getEndTime(),
                                                                                          rbr.getPeriod());
            ResLoadBalance t = new ResLoadBalance();
            t.setStartTime(resLoadBalance.getStartTime());
            t.setEndTime(newEndTime);
            t.setId(resLoadBalance.getId());
            this.resLoadBalanceMapper.updateLifeCycleByPrimaryKey(t);
            resLoadBalance.setEndTime(newEndTime);
            basicResActionLogService.insertIntoActionLog(Objects.nonNull(authUser) ? authUser.getAccount() : "admin",
                                                         ResourceOperateEnum.RENEW_INSTANCE, ResourceTypeEnum.RENEW_VBS,
                                                         resLoadBalance.getId(), resLoadBalance.getLbName(),
                                                         Boolean.TRUE, null, null, authUser.getOrgSid());
        }
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS),
                              resLoadBalance.getEndTime());
    }

    @Override
    public Integer countSelfServiceSlb(Criteria sfCriteria) {
        return resLoadBalanceMapper.countSelfServiceSlb(sfCriteria);
    }

    private ResInstResult sendToMQ(Base base) {
        ResInstResult result;
        try {
            logger.info("输入MQ参数：{}", JsonUtil.toJson(base));
            MQHelper.sendMessage(base);
            result = new ResInstResult(ResInstResult.SUCCESS);
        } catch (Exception e) {
            result = new ResInstResult(FAILURE, e.getMessage());
        }
        return result;
    }

    @Override
    public void setLbNetworkName(ResLoadBalance res) {
        if (CloudEnvType.ALIYUN.equals(res.getCloudEnvType()) || CloudEnvType.QCLOUD.equals(res.getCloudEnvType())) {
            if (res.getResNetWorkId() != null) {
                Network network = networkMapper.selectByPrimaryKey(res.getResNetWorkId());
                ResVpc resVpc = resVpcService.selectByPrimaryKey(res.getResVpcId());
                if (Objects.nonNull(network) && Objects.nonNull(network.getNetworkName()) && Objects.nonNull(resVpc)
                        && Objects.nonNull(resVpc.getName())) {
                    res.setNetWorkName(resVpc.getName() + "/" + network.getNetworkName());
                }
            }
        } else if (CloudEnvType.OPEN_STACK.equals(res.getCloudEnvType())) {
            if (res.getResNetWorkId() != null) {
                Network network = networkMapper.selectByPrimaryKey(res.getResNetWorkId());
                StringBuilder networkInfo = new StringBuilder();
                if (null != network) {
                    ResVpc vpc = resVpcService.selectByPrimaryKey(Long.valueOf(network.getNetVpcId()));
                    networkInfo.append(vpc != null ? vpc.getName() : "");
                    networkInfo.append("(").append(network.getNetworkName()).append(")");
                }

                res.setNetWorkName(networkInfo.toString());
            }
        } else if (CloudEnvType.HUAWEICLOUD.equals(res.getCloudEnvType())) {
            if (res.getResNetWorkId() != null) {
                Network network = networkMapper.selectByPrimaryKey(res.getResNetWorkId());
                if (network != null) {
                    ResVpc resVpc = this.resVpcService.selectByPrimaryKey(Long.parseLong(network.getNetVpcId()));
                    res.setNetWorkName(resVpc.getName() + "/" + network.getNetworkName());
                }
            }

        } else if (CloudEnvType.CLOUDOS.equals(res.getCloudEnvType())) {
            if (res.getResNetWorkId() != null) {
                //添加网络名称
                Network network = networkMapper.selectByPrimaryKey(res.getResNetWorkId());
                if (network != null) {
                    ResVpc resVpc = this.resVpcService.selectByPrimaryKey(Long.parseLong(network.getNetVpcId()));
                    res.setNetWorkName(resVpc.getName() + "/" + network.getNetworkName());
                }
            }
        }
    }

    @Override
    public List<ResLoadBalance> getElbByIds(List<Long> ids) {
        return resLoadBalanceMapper.getElbByIds(ids);
    }
}
