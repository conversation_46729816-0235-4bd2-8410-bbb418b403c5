<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.cloudstar.rightcloud.resource.dao.hpc.ResHpcClusterMapper">
  <resultMap id="BaseResultMap" type="cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResHpcCluster">
    <id column="res_hpc_cluster_id" jdbcType="BIGINT" property="id" />
    <result column="res_hpc_cluster_resource_id" jdbcType="VARCHAR" property="resourceId" />
    <result column="res_hpc_cluster_name" jdbcType="VARCHAR" property="name" />
    <result column="res_hpc_cluster_description" jdbcType="VARCHAR" property="description" />
    <result column="res_hpc_cluster_business_category" jdbcType="VARCHAR" property="businessCategory" />
    <result column="res_hpc_cluster_tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="res_hpc_cluster_cloud_env_id" jdbcType="BIGINT" property="cloudEnvId" />
    <result column="res_hpc_cluster_charge_type" jdbcType="VARCHAR" property="chargeType" />
    <result column="res_hpc_cluster_cluster_type" jdbcType="VARCHAR" property="clusterType" />
    <result column="res_hpc_cluster_proess_phase" jdbcType="VARCHAR" property="proessPhase" />
    <result column="res_hpc_cluster_proess_status" jdbcType="VARCHAR" property="proessStatus" />
    <result column="res_hpc_cluster_status" jdbcType="VARCHAR" property="status" />
    <result column="res_hpc_cluster_scenario" jdbcType="VARCHAR" property="scenario" />
    <result column="res_hpc_cluster_vpc_peering_info" jdbcType="VARCHAR" property="vpcPeeringInfo" />
    <result column="res_hpc_cluster_admin_user" jdbcType="VARCHAR" property="adminUser" />
    <result column="res_hpc_cluster_admin_password" jdbcType="VARCHAR" property="adminPassword" />
    <result column="res_hpc_cluster_ccp_external_address" jdbcType="VARCHAR" property="ccpExternalAddress" />
    <result column="res_hpc_cluster_ccp_internel_address" jdbcType="VARCHAR" property="ccpInternelAddress" />
    <result column="res_hpc_cluster_login_node_external_address" jdbcType="VARCHAR" property="loginNodeExternalAddress" />
    <result column="res_hpc_cluster_login_node_internal_address" jdbcType="VARCHAR" property="loginNodeInternalAddress" />
    <result column="res_hpc_cluster_error_info" jdbcType="VARCHAR" property="errorInfo" />
    <result column="res_hpc_cluster_task_id" jdbcType="VARCHAR" property="taskId" />
    <result column="res_hpc_cluster_task_description" jdbcType="VARCHAR" property="taskDescription" />
    <result column="res_hpc_cluster_owner_id" jdbcType="VARCHAR" property="ownerId" />
    <result column="res_hpc_cluster_org_sid" jdbcType="BIGINT" property="orgSid" />
    <result column="res_hpc_cluster_created_org_sid" jdbcType="BIGINT" property="createdOrgSid" />
    <result column="res_hpc_cluster_version" jdbcType="BIGINT" property="version" />
    <result column="res_hpc_cluster_created_by" jdbcType="VARCHAR" property="createdBy"/>
    <result column="res_hpc_cluster_created_dt" jdbcType="TIMESTAMP" property="createdDt"/>
    <result column="res_hpc_cluster_updated_by" jdbcType="VARCHAR" property="updatedBy"/>
    <result column="res_hpc_cluster_updated_dt" jdbcType="TIMESTAMP" property="updatedDt"/>
    <result column="share_type" jdbcType="VARCHAR" property="shareType"/>
    <result column="availability_zone" jdbcType="VARCHAR" property="availabilityZone"/>
    <result column="pool_uuid" jdbcType="VARCHAR" property="poolUuid"/>
    <result column="unsubscribe_task_id" jdbcType="VARCHAR" property="unsubscribeTaskId"/>
    <result column="cluster_file" jdbcType="VARCHAR" property="clusterFile"/>
    <result column="hpc_version" jdbcType="INTEGER" property="hpcVersion"/>
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
    <result column="ccp_version" jdbcType="INTEGER" property="ccpVersion"/>
    <result column="pre_status" jdbcType="VARCHAR" property="preStatus"/>
    <result column="auto_release" jdbcType="INTEGER" property="autoRelease"/>
    <result column="quota_release_days" jdbcType="INTEGER" property="quotaReleaseDays"/>
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and (${criterion.condition} #{criterion.value})
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    res_hpc_cluster.id as res_hpc_cluster_id, res_hpc_cluster.resource_id as res_hpc_cluster_resource_id,
    res_hpc_cluster.`name` as `res_hpc_cluster_name`, res_hpc_cluster.description as res_hpc_cluster_description,
    res_hpc_cluster.business_category as res_hpc_cluster_business_category, res_hpc_cluster.tenant_id as res_hpc_cluster_tenant_id,
    res_hpc_cluster.cloud_env_id as res_hpc_cluster_cloud_env_id, res_hpc_cluster.charge_type as res_hpc_cluster_charge_type,
    res_hpc_cluster.cluster_type as res_hpc_cluster_cluster_type, res_hpc_cluster.proess_phase as res_hpc_cluster_proess_phase,
    res_hpc_cluster.proess_status as res_hpc_cluster_proess_status, res_hpc_cluster.`status` as `res_hpc_cluster_status`,
    res_hpc_cluster.scenario as res_hpc_cluster_scenario, res_hpc_cluster.vpc_peering_info as res_hpc_cluster_vpc_peering_info,
    res_hpc_cluster.admin_user as res_hpc_cluster_admin_user, res_hpc_cluster.admin_password as res_hpc_cluster_admin_password,
    res_hpc_cluster.ccp_external_address as res_hpc_cluster_ccp_external_address, res_hpc_cluster.ccp_internel_address as res_hpc_cluster_ccp_internel_address,
    res_hpc_cluster.login_node_external_address as res_hpc_cluster_login_node_external_address,
    res_hpc_cluster.login_node_internal_address as res_hpc_cluster_login_node_internal_address,
    res_hpc_cluster.error_info as res_hpc_cluster_error_info, res_hpc_cluster.task_id as res_hpc_cluster_task_id,
    res_hpc_cluster.task_description as res_hpc_cluster_task_description, res_hpc_cluster.owner_id as res_hpc_cluster_owner_id,
    res_hpc_cluster.org_sid as res_hpc_cluster_org_sid, res_hpc_cluster.created_org_sid as res_hpc_cluster_created_org_sid,
    res_hpc_cluster.version as res_hpc_cluster_version, res_hpc_cluster.created_by as res_hpc_cluster_created_by,
    res_hpc_cluster.created_dt as res_hpc_cluster_created_dt, res_hpc_cluster.updated_by as res_hpc_cluster_updated_by,
    res_hpc_cluster.updated_dt as res_hpc_cluster_updated_dt,res_hpc_cluster.share_type share_type, res_hpc_cluster.availability_zone availability_zone,
    res_hpc_cluster.pool_uuid pool_uuid, res_hpc_cluster.cluster_file cluster_file,res_hpc_cluster.hpc_version hpc_version,res_hpc_cluster.pre_status pre_status,
    res_hpc_cluster.start_time , res_hpc_cluster.end_time ,res_hpc_cluster.ccp_version ccp_version,res_hpc_cluster.unsubscribe_task_id unsubscribe_task_id,
    res_hpc_cluster.auto_release auto_release
  </sql>
  <select id="selectByExample" parameterType="cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResHpcClusterExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from res_hpc_cluster res_hpc_cluster
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from res_hpc_cluster res_hpc_cluster
    where res_hpc_cluster.id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from res_hpc_cluster
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResHpcClusterExample">
    delete from res_hpc_cluster res_hpc_cluster
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResHpcCluster" useGeneratedKeys="true">
    insert into res_hpc_cluster (resource_id, `name`, description,
                                 business_category, tenant_id, cloud_env_id,
                                 charge_type, share_type, cluster_type, proess_phase,
                                 proess_status, `status`, scenario,
                                 vpc_peering_info, admin_user, admin_password,
                                 ccp_external_address, ccp_internel_address,
                                 login_node_external_address, login_node_internal_address,
                                 error_info, task_id, task_description,
                                 owner_id, org_sid, created_org_sid,
                                 version, created_by, created_dt,
                                 updated_by, updated_dt, hpc_version, availability_zone, pre_status, pool_name,
                                 pool_uuid, ccp_version, os_architecture_type,quota_release_days)
    values (#{resourceId,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR},
            #{businessCategory,jdbcType=VARCHAR}, #{tenantId,jdbcType=BIGINT}, #{cloudEnvId,jdbcType=BIGINT},
            #{chargeType,jdbcType=VARCHAR}, #{shareType,jdbcType=VARCHAR}, #{clusterType,jdbcType=VARCHAR},
            #{proessPhase,jdbcType=VARCHAR},
            #{proessStatus,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{scenario,jdbcType=VARCHAR},
            #{vpcPeeringInfo,jdbcType=VARCHAR}, #{adminUser,jdbcType=VARCHAR}, #{adminPassword,jdbcType=VARCHAR},
            #{ccpExternalAddress,jdbcType=VARCHAR}, #{ccpInternelAddress,jdbcType=VARCHAR},
            #{loginNodeExternalAddress,jdbcType=VARCHAR}, #{loginNodeInternalAddress,jdbcType=VARCHAR},
            #{errorInfo,jdbcType=VARCHAR}, #{taskId,jdbcType=VARCHAR}, #{taskDescription,jdbcType=VARCHAR},
            #{ownerId,jdbcType=VARCHAR}, #{orgSid,jdbcType=BIGINT}, #{createdOrgSid,jdbcType=BIGINT},
            #{version,jdbcType=BIGINT}, #{createdBy,jdbcType=VARCHAR}, #{createdDt,jdbcType=TIMESTAMP},
            #{updatedBy,jdbcType=VARCHAR}, #{updatedDt,jdbcType=TIMESTAMP}, #{hpcVersion,jdbcType=TINYINT},
            #{availabilityZone,jdbcType=VARCHAR}, #{preStatus,jdbcType=VARCHAR}, #{poolName,jdbcType=VARCHAR},
            #{poolUuid,jdbcType=VARCHAR}, #{ccpVersion,jdbcType=TINYINT}, #{osArchitectureType,jdbcType=VARCHAR},
            #{quotaReleaseDays})

  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResHpcCluster" useGeneratedKeys="true">
    insert into res_hpc_cluster
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="resourceId != null">
        resource_id,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="businessCategory != null">
        business_category,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="cloudEnvId != null">
        cloud_env_id,
      </if>
      <if test="chargeType != null">
        charge_type,
      </if>
      <if test="clusterType != null">
        cluster_type,
      </if>
      <if test="proessPhase != null">
        proess_phase,
      </if>
      <if test="proessStatus != null">
        proess_status,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="scenario != null">
        scenario,
      </if>
      <if test="vpcPeeringInfo != null">
        vpc_peering_info,
      </if>
      <if test="adminUser != null">
        admin_user,
      </if>
      <if test="adminPassword != null">
        admin_password,
      </if>
      <if test="ccpExternalAddress != null">
        ccp_external_address,
      </if>
      <if test="ccpInternelAddress != null">
        ccp_internel_address,
      </if>
      <if test="loginNodeExternalAddress != null">
        login_node_external_address,
      </if>
      <if test="loginNodeInternalAddress != null">
        login_node_internal_address,
      </if>
      <if test="errorInfo != null">
        error_info,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
      <if test="taskDescription != null">
        task_description,
      </if>
      <if test="ownerId != null">
        owner_id,
      </if>
      <if test="orgSid != null">
        org_sid,
      </if>
      <if test="createdOrgSid != null">
        created_org_sid,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="createdDt != null">
        created_dt,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="updatedDt != null">
        updated_dt,
      </if>
      <if test="osArchitectureType != null">
        os_architecture_type,
      </if>
      <if test="quotaReleaseDays != null">
        quota_release_days,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="resourceId != null">
        #{resourceId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="businessCategory != null">
        #{businessCategory,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="cloudEnvId != null">
        #{cloudEnvId,jdbcType=BIGINT},
      </if>
      <if test="chargeType != null">
        #{chargeType,jdbcType=VARCHAR},
      </if>
      <if test="clusterType != null">
        #{clusterType,jdbcType=VARCHAR},
      </if>
      <if test="proessPhase != null">
        #{proessPhase,jdbcType=VARCHAR},
      </if>
      <if test="proessStatus != null">
        #{proessStatus,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="scenario != null">
        #{scenario,jdbcType=VARCHAR},
      </if>
      <if test="vpcPeeringInfo != null">
        #{vpcPeeringInfo,jdbcType=VARCHAR},
      </if>
      <if test="adminUser != null">
        #{adminUser,jdbcType=VARCHAR},
      </if>
      <if test="adminPassword != null">
        #{adminPassword,jdbcType=VARCHAR},
      </if>
      <if test="ccpExternalAddress != null">
        #{ccpExternalAddress,jdbcType=VARCHAR},
      </if>
      <if test="ccpInternelAddress != null">
        #{ccpInternelAddress,jdbcType=VARCHAR},
      </if>
      <if test="loginNodeExternalAddress != null">
        #{loginNodeExternalAddress,jdbcType=VARCHAR},
      </if>
      <if test="loginNodeInternalAddress != null">
        #{loginNodeInternalAddress,jdbcType=VARCHAR},
      </if>
      <if test="errorInfo != null">
        #{errorInfo,jdbcType=VARCHAR},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=VARCHAR},
      </if>
      <if test="taskDescription != null">
        #{taskDescription,jdbcType=VARCHAR},
      </if>
      <if test="ownerId != null">
        #{ownerId,jdbcType=VARCHAR},
      </if>
      <if test="orgSid != null">
        #{orgSid,jdbcType=BIGINT},
      </if>
      <if test="createdOrgSid != null">
        #{createdOrgSid,jdbcType=BIGINT},
      </if>
      <if test="version != null">
        #{version,jdbcType=BIGINT},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="createdDt != null">
        #{createdDt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="updatedDt != null">
        #{updatedDt,jdbcType=TIMESTAMP},
      </if>
      <if test="osArchitectureType != null">
        #{osArchitectureType,jdbcType=VARCHAR},
      </if>
       <if test="autoRelease != null">
        #{autoRelease,jdbcType=INTEGER},
      </if>
      <if test="quotaReleaseDays != null">
        #{quotaReleaseDays},
      </if>
    </trim>
  </insert>

  <select id="countByExample" parameterType="cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResHpcClusterExample" resultType="java.lang.Long">
    select count(*) from
    (select
    <include refid="Base_Select_Columns"/>
    from res_hpc_cluster res_hpc_cluster
    <if test="oredCriteria != null and oredCriteria.size()>0">
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <choose>
          <when test="criteria.criteria != null and criteria.criteria.size()>0">
            <include refid="Example_Where_Clause" />
          </when>
          <otherwise>
            where 1=1
          </otherwise>
        </choose>
      </foreach>
      and res_hpc_cluster.`status` != 'unsubscribing'
    </if>


    UNION
    select
    <include refid="Base_Select_Columns"/>
    from res_hpc_cluster res_hpc_cluster
    <if test="oredCriteria != null and oredCriteria.size()>0">
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <choose>
          <when test="criteria.criteria != null and criteria.criteria.size()>0">
            <include refid="Example_Where_Clause" />
          </when>
          <otherwise>
            where 1=1
          </otherwise>
        </choose>
      </foreach>
      and (res_hpc_cluster.hpc_version in (2,4) or (res_hpc_cluster.`status` = 'unsubscribing' and res_hpc_cluster.cluster_type != 'SAASShare'))
    </if>
    ) AS A
  </select>



  <resultMap id="detailDTOMap" type="cn.com.cloudstar.rightcloud.core.pojo.dto.hpc.ResHpcClusterDetailDTO">
    <result column="id" property="clusterId"></result>
    <result column="resource_id" property="resourceId"></result>
    <result column="clusterName" property="clusterName"></result>
    <result column="cluster_type" property="clusterType"></result>
    <result column="pool_name" property="poolName"></result>
    <result column="status" property="status"></result>
    <result column="pre_status" property="preStatus"></result>
    <result column="scenario" property="scenario"></result>
    <result column="description" property="description"></result>
    <result column="cli_num" property="cliNum"></result>
    <result column="cidr" property="cidr"></result>
    <result column="bandwidth" property="bandwidth"></result>
    <result column="ccp_external_address" property="ccpExternalAddress"></result>
    <result column="ccp_internel_address" property="ccpInternelAddress"></result>
    <result column="login_node_external_address" property="loginNodeExternalAddress"></result>
    <result column="login_node_internal_address" property="loginNodeInternalAddress"></result>
    <result column="charge_type" property="chargeType"></result>
    <result column="created_dt" property="createdDt"></result>
    <result column="ccp_version" property="ccpVersion"></result>
    <collection property="bmsDTOList" ofType="cn.com.cloudstar.rightcloud.core.pojo.dto.hpc.ResBmsDTO" column="id" select="selectBmsByClusterId"></collection>
    <collection property="ecsDTOList" ofType="cn.com.cloudstar.rightcloud.core.pojo.dto.hpc.ResEcsDTO" column="id"  select="selectEcsByClusterId"></collection>
    <collection property="shareList" ofType="cn.com.cloudstar.rightcloud.core.pojo.dto.hpc.HPCResponseShareDTO" column="id" select="selectShareByClusterId"></collection>

  </resultMap>
  <select id="getClusterById"  resultMap="detailDTOMap">
    SELECT
        rhc.id,
        rhc.resource_id,
        rhc.NAME AS clusterName,
        rhc.pool_name,
        rhc.pool_uuid,
        rhc.cluster_type,
        rhc.STATUS,
        rhc.pre_status,
        rhc.scenario,
        rhc.ccp_external_address,
        rhc.ccp_internel_address,
        rhc.login_node_external_address,
        rhc.login_node_internal_address,
        rhc.charge_type,
        rhc.description,
        rhc.ccp_version,
        rhc.os_architecture_type,
        (
        SELECT
            rvc.cidr
        FROM
            res_hpc_cluster rhc
            LEFT JOIN res_hpc_cluster_resource rhcr ON rhcr.cluster_id = rhc.id
            LEFT JOIN res_vpc rvc ON rvc.id = rhcr.resource_id
        WHERE
            rhcr.resource_type = "vpc"
            AND rhc.id = #{clusterId}
        ) cidr,
        (
        SELECT
            rfi.bandwidth
        FROM
            res_hpc_cluster rhc
            LEFT JOIN res_hpc_cluster_resource rhcr ON rhcr.cluster_id = rhc.id
            LEFT JOIN res_floating_ip rfi ON rfi.id = rhcr.resource_id
        WHERE
            rhcr.resource_type = "floatingIp"
            AND rhc.id = #{clusterId}
        ) bandwidth,
        rhc.created_dt
    FROM
        res_hpc_cluster rhc
    where rhc.id = #{clusterId} and rhc.org_sid=#{orgSid}
  </select>

  <select id="selectBmsByClusterId" resultType="cn.com.cloudstar.rightcloud.core.pojo.dto.hpc.ResBmsDTO">
    select
      rb.instance_id,
      rb.inner_ip,
      rb.public_ip,
      rb.floating_ip,
      rb.cpu,
      rb.memory ram,
      rb.instance_type as typeName,
      rhcr.node_type,
      rhcr.hpc_point_type
    from res_hpc_cluster_resource rhcr
    left join res_bms rb on rb.id = rhcr.resource_id
    where rhcr.resource_type="bms" and rhcr.cluster_id=#{id}
  </select>

  <select id="selectEcsByClusterId" resultType="cn.com.cloudstar.rightcloud.core.pojo.dto.hpc.ResEcsDTO">
    select
      rv.instance_id,
      rv.inner_ip,
      rv.public_ip,
      rv.floating_ip,
      rv.cpu,
      rv.memory ram,
      rv.instance_type as typeName,
      rhcr.node_type,
      rhcr.hpc_point_type
    from res_hpc_cluster_resource rhcr
    left join res_vm rv on rv.id = rhcr.resource_id
    where rhcr.resource_type="ecs" and rhcr.cluster_id=#{id}
    ORDER BY rhcr.node_type,rhcr.hpc_point_type,rv.id ASC
  </select>


  <resultMap id="clusterDTOMap" type="cn.com.cloudstar.rightcloud.core.pojo.dto.hpc.ResHpcClusterDTO">
    <result column="id" property="clusterId"></result>
    <result column="resource_id" property="resourceId"></result>
    <result column="clusterName" property="clusterName"></result>
    <result column="cluster_type" property="clusterType"></result>
    <result column="pool_name" property="poolName"></result>
    <result column="status" property="status"></result>
    <result column="pre_status" property="preStatus"></result>
    <result column="scenario" property="scenario"></result>
    <result column="login_node_external_address" property="loginNodeExternalAddress"></result>
    <result column="ccp_internel_address" property="ccpInternelAddress"></result>
    <result column="charge_type" property="chargeType"></result>
    <result column="org_sid" property="orgSid"></result>
    <result column="description" property="description"></result>
    <result column="business_category" property="businessCategory"></result>
    <result column="cpp_version" property="ccpVersion"></result>
      <result column="hpc_version" property="hpcVersion"></result>
    <result column="start_time" property="startTime"></result>
    <result column="end_time" property="endTime"></result>
    <result column="proess_phase" property="proessPhase"></result>
    <result column="proess_status" property="proessStatus"></result>
    <result column="pool_uuid" property="poolUuid"></result>
    <result column="owner_id" property="ownerId"></result>

    <collection property="shareList" ofType="cn.com.cloudstar.rightcloud.core.pojo.dto.hpc.HPCResponseShareDTO" column="id" select="selectShareByClusterId"></collection>

  </resultMap>

  <sql id="Base_Select_Columns">
    res_hpc_cluster.id,
    res_hpc_cluster.resource_id,
    res_hpc_cluster.NAME AS clusterName,
    res_hpc_cluster.pool_name,
    res_hpc_cluster.cluster_type,
    res_hpc_cluster.STATUS,
    res_hpc_cluster.pre_status,
    res_hpc_cluster.scenario,
      res_hpc_cluster.login_node_external_address,
      res_hpc_cluster.ccp_internel_address,
      res_hpc_cluster.charge_type,
    res_hpc_cluster.org_sid,
    res_hpc_cluster.description,
    res_hpc_cluster.business_category,
      res_hpc_cluster.ccp_version,
      res_hpc_cluster.hpc_version,
      res_hpc_cluster.start_time,
      res_hpc_cluster.proess_phase,
      res_hpc_cluster.proess_status,
    res_hpc_cluster.end_time,
    res_hpc_cluster.pool_uuid,
    res_hpc_cluster.created_dt,
    res_hpc_cluster.owner_id,
    res_hpc_cluster.quota_release_days
  </sql>

  <select id="queryPage" parameterType="cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResHpcClusterExample" resultMap="clusterDTOMap">

    select * from
    (select
    <include refid="Base_Select_Columns"/>
    from res_hpc_cluster res_hpc_cluster
    <if test="oredCriteria != null and oredCriteria.size()>0">
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <choose>
          <when test="criteria.criteria != null and criteria.criteria.size()>0">
            <include refid="Example_Where_Clause" />
          </when>
          <otherwise>
            where 1=1
          </otherwise>
        </choose>
      </foreach>
      and res_hpc_cluster.`status` != 'unsubscribing'
    </if>
    UNION
    select
    <include refid="Base_Select_Columns"/>
    from res_hpc_cluster res_hpc_cluster
    <if test="oredCriteria != null and oredCriteria.size()>0">
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <choose>
          <when test="criteria.criteria != null and criteria.criteria.size()>0">
            <include refid="Example_Where_Clause" />
          </when>
          <otherwise>
            where 1=1
          </otherwise>
        </choose>
      </foreach>
      and (res_hpc_cluster.hpc_version in (2,4) or (res_hpc_cluster.`status` = 'unsubscribing' and res_hpc_cluster.cluster_type != 'SAASShare'))
    </if>
    ) AS A
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>

  </select>

    <select id="selectShareByClusterId" resultType="cn.com.cloudstar.rightcloud.core.pojo.dto.hpc.HPCResponseShareDTO">
      select rs.id   as shareId,
             rs.name as shareName,
             rs.type,
             rs.share_proto,
             rs.is_share_dir,
             rs.size,
             rs.used_size,
             rs.is_virtual,
             ce.cloud_env_type,
             rs.is_cluster_default
      from res_hpc_cluster_resource rhcr
             left join res_share rs on rs.id = rhcr.resource_id
             left join cloud_env ce on rs.cloud_env_id = ce.id
      where rhcr.resource_type in ("sfs","DME-OSP")
        and rhcr.cluster_id = #{id}
        and rs.status in ("available","frozen","expired","modify_error","modifying", "uninstall_error")
    </select>

    <sql id="Example_Where_Clause_1">
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.clusterType != null">
                and cluster_type = #{condition.clusterType}
            </if>
            <if test="condition.name != null">
                and name = #{condition.name}
            </if>
            <if test="condition.nameLike != null">
                and name like concat("%",#{condition.nameLike},"%")
            </if>
            <if test="condition.id != null">
                and id = #{condition.id}
            </if>
            <if test="condition.status != null">
                and status = #{condition.status}
            </if>
            <if test="condition.statusNoEq != null">
                and status != #{condition.statusNoEq}
            </if>
            <if test="condition.shareTypeId != null">
                and share_type_id = #{condition.shareTypeId}
            </if>
            <if test="condition.scenario != null">
                and scenario = #{condition.scenario}
            </if>
            <if test="condition.taskId != null">
                and task_id = #{condition.taskId}
            </if>
            <if test="condition.orgSid != null">
                and org_sid = #{condition.orgSid}
            </if>
            <if test="condition.description != null">
                and description = #{condition.description}
            </if>
            <if test="condition.businessCategory != null">
                and business_category = #{condition.businessCategory}
            </if>
            <if test="condition.poolUuid != null">
              and pool_uuid = #{condition.poolUuid}
            </if>
        </trim>
    </sql>
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from res_hpc_cluster
        <if test="_parameter != null">
            <include refid="Example_Where_Clause_1"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>

    <select id="selectAllPriCluster" resultType="cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResHpcCluster">
        select id,
               resource_id,
               name,
               cluster_type
        from res_hpc_cluster
        where cluster_type = 'SAASPrivate'
    </select>
  <select id="getResHpcClusterForCollector" resultType="cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResHpcCluster">
         SELECT
          id,
          name,
          tenant_id,
          resource_id,
          start_time,
          end_time,
          `status`,
          owner_id,
          org_sid,
          cluster_type
        FROM
          `res_hpc_cluster`
        WHERE
          cluster_type in ('SAASPrivate','PreSAASPrivate','AdSAASPrivate')
          AND `status` NOT IN ('configing','rejected','apply','deleted')
    </select>

    <resultMap id="detailNodeMap" type="cn.com.cloudstar.rightcloud.core.pojo.dto.hpc.ResHpcClusterDetailDTO">
        <result column="id" property="clusterId"></result>
        <result column="resource_id" property="resourceId"></result>
        <result column="clusterName" property="clusterName"></result>
        <result column="cluster_type" property="clusterType"></result>
        <result column="pool_name" property="poolName"></result>
        <result column="status" property="status"></result>
        <result column="pre_status" property="preStatus"></result>
        <result column="scenario" property="scenario"></result>
        <result column="description" property="description"></result>
        <result column="cli_num" property="cliNum"></result>
        <result column="cidr" property="cidr"></result>
        <result column="bandwidth" property="bandwidth"></result>
        <result column="ccp_external_address" property="ccpExternalAddress"></result>
        <result column="ccp_internel_address" property="ccpInternelAddress"></result>
        <result column="login_node_external_address" property="loginNodeExternalAddress"></result>
        <result column="login_node_internal_address" property="loginNodeInternalAddress"></result>
        <result column="charge_type" property="chargeType"></result>
        <result column="created_dt" property="createdDt"></result>
        <result column="ccp_version" property="ccpVersion"></result>
        <collection property="bmsDTOList" ofType="cn.com.cloudstar.rightcloud.core.pojo.dto.hpc.ResBmsDTO" column="id"
            select="selectNodeByClusterId"></collection>
        <collection property="shareList" ofType="cn.com.cloudstar.rightcloud.core.pojo.dto.hpc.HPCResponseShareDTO"
            column="id" select="selectShareByClusterId"></collection>

    </resultMap>
    <select id="getClusterByIdAndType" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultType="cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.HPCNodeInfoMemberVo">
        select *
        from (
        <if test="criteria.condition.resourceType != null and criteria.condition.resourceType != ''">
            <if test="criteria.condition.resourceType == 'compute' ">
                select rb.instance_id,
                rb.inner_ip,
                rb.public_ip,
                rb.floating_ip,
                rb.cpu,
                rb.memory ram,
                rb.instance_type as typeName,
                rhcr.node_type,
                rhcr.hpc_point_type,
                rb.id,
                rb.`status`
                from res_hpc_cluster_resource rhcr
                left join res_bms rb on rb.id = rhcr.resource_id
                where rhcr.resource_type = "bms"
                and rhcr.cluster_id = #{criteria.condition.clusterId})t
            </if>
            <if test="criteria.condition.resourceType != 'compute' ">
                select rv.instance_id,
                rv.inner_ip,
                rv.public_ip,
                rv.floating_ip,
                rv.cpu,
                rv.memory ram,
                rv.instance_type as typeName,
                rhcr.node_type,
                rhcr.hpc_point_type,
                rv.id,
                rv.`status`
                from res_hpc_cluster_resource rhcr
                left join res_vm rv on rv.id = rhcr.resource_id
                where rhcr.resource_type = "ecs"
                and rhcr.cluster_id = #{criteria.condition.clusterId}
                and rhcr.node_type = #{criteria.condition.resourceType}
                ) t
            </if>
        </if>
        <if test="criteria.condition.resourceType == null">
            select rb.instance_id,
            rb.inner_ip,
            rb.public_ip,
            rb.floating_ip,
            rb.cpu,
            rb.memory ram,
            rb.instance_type as typeName,
            rhcr.node_type,
            rhcr.hpc_point_type,
            rb.id,
            rb.`status`
            from res_hpc_cluster_resource rhcr
            left join res_bms rb on rb.id = rhcr.resource_id
            where rhcr.resource_type = "bms"
            and rhcr.cluster_id = #{criteria.condition.clusterId}
            union all
            select rv.instance_id,
            rv.inner_ip,
            rv.public_ip,
            rv.floating_ip,
            rv.cpu,
            rv.memory ram,
            rv.instance_type as typeName,
            rhcr.node_type,
            rhcr.hpc_point_type,
            rv.id,
            rv.`status`
            from res_hpc_cluster_resource rhcr
            left join res_vm rv on rv.id = rhcr.resource_id
            where rhcr.resource_type = "ecs"
            and rhcr.cluster_id = #{criteria.condition.clusterId}) t
        </if>
    </select>

  <update id="updateByExampleSelective" parameterType="map">
        update res_hpc_cluster res_hpc_cluster
        <set>
            <if test="record.id != null">
                res_hpc_cluster.id = #{record.id,jdbcType=BIGINT},
            </if>
            <if test="record.resourceId != null">
                res_hpc_cluster.resource_id = #{record.resourceId,jdbcType=VARCHAR},
            </if>
            <if test="record.name != null">
                res_hpc_cluster.`name` = #{record.name,jdbcType=VARCHAR},
            </if>
            <if test="record.description != null">
                res_hpc_cluster.description = #{record.description,jdbcType=VARCHAR},
            </if>
            <if test="record.businessCategory != null">
                res_hpc_cluster.business_category = #{record.businessCategory,jdbcType=VARCHAR},
            </if>
            <if test="record.tenantId != null">
                res_hpc_cluster.tenant_id = #{record.tenantId,jdbcType=BIGINT},
            </if>
            <if test="record.cloudEnvId != null">
                res_hpc_cluster.cloud_env_id = #{record.cloudEnvId,jdbcType=BIGINT},
            </if>
            <if test="record.chargeType != null">
                res_hpc_cluster.charge_type = #{record.chargeType,jdbcType=VARCHAR},
            </if>
            <if test="record.clusterType != null">
                res_hpc_cluster.cluster_type = #{record.clusterType,jdbcType=VARCHAR},
            </if>
            <if test="record.proessPhase != null">
                res_hpc_cluster.proess_phase = #{record.proessPhase,jdbcType=VARCHAR},
            </if>
            <if test="record.proessStatus != null">
                res_hpc_cluster.proess_status = #{record.proessStatus,jdbcType=VARCHAR},
            </if>
            <if test="record.status == 'frozen'">
                res_hpc_cluster.pre_status = res_hpc_cluster.status,
            </if>
            <if test="record.status != null">
                res_hpc_cluster.`status` = #{record.status,jdbcType=VARCHAR},
            </if>
            <if test="record.scenario != null">
                res_hpc_cluster.scenario = #{record.scenario,jdbcType=VARCHAR},
            </if>
            <if test="record.vpcPeeringInfo != null">
                res_hpc_cluster.vpc_peering_info = #{record.vpcPeeringInfo,jdbcType=VARCHAR},
            </if>
            <if test="record.adminUser != null">
                res_hpc_cluster.admin_user = #{record.adminUser,jdbcType=VARCHAR},
            </if>
            <if test="record.adminPassword != null">
                res_hpc_cluster.admin_password = #{record.adminPassword,jdbcType=VARCHAR},
            </if>
            <if test="record.ccpExternalAddress != null">
                res_hpc_cluster.ccp_external_address = #{record.ccpExternalAddress,jdbcType=VARCHAR},
            </if>
            <if test="record.ccpInternelAddress != null">
                res_hpc_cluster.ccp_internel_address = #{record.ccpInternelAddress,jdbcType=VARCHAR},
            </if>
            <if test="record.loginNodeExternalAddress != null">
                res_hpc_cluster.login_node_external_address = #{record.loginNodeExternalAddress,jdbcType=VARCHAR},
            </if>
            <if test="record.loginNodeInternalAddress != null">
                res_hpc_cluster.login_node_internal_address = #{record.loginNodeInternalAddress,jdbcType=VARCHAR},
            </if>
            <if test="record.errorInfo != null">
                res_hpc_cluster.error_info = #{record.errorInfo,jdbcType=VARCHAR},
            </if>
            <if test="record.taskId != null">
                res_hpc_cluster.task_id = #{record.taskId,jdbcType=VARCHAR},
            </if>
            <if test="record.taskDescription != null">
                res_hpc_cluster.task_description = #{record.taskDescription,jdbcType=VARCHAR},
            </if>
            <if test="record.ownerId != null">
                res_hpc_cluster.owner_id = #{record.ownerId,jdbcType=VARCHAR},
            </if>
            <if test="record.orgSid != null">
                res_hpc_cluster.org_sid = #{record.orgSid,jdbcType=BIGINT},
            </if>
            <if test="record.createdOrgSid != null">
                res_hpc_cluster.created_org_sid = #{record.createdOrgSid,jdbcType=BIGINT},
            </if>
            <if test="record.version != null">
                res_hpc_cluster.version = #{record.version,jdbcType=BIGINT},
            </if>
            <if test="record.createdBy != null">
                res_hpc_cluster.created_by = #{record.createdBy,jdbcType=VARCHAR},
            </if>
            <if test="record.createdDt != null">
                res_hpc_cluster.created_dt = #{record.createdDt,jdbcType=TIMESTAMP},
            </if>
            <if test="record.updatedBy != null">
                res_hpc_cluster.updated_by = #{record.updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="record.updatedDt != null">
                res_hpc_cluster.updated_dt = #{record.updatedDt,jdbcType=TIMESTAMP},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update res_hpc_cluster res_hpc_cluster
        set res_hpc_cluster.id = #{record.id,jdbcType=BIGINT},
        res_hpc_cluster.resource_id = #{record.resourceId,jdbcType=VARCHAR},
        res_hpc_cluster.`name` = #{record.name,jdbcType=VARCHAR},
        res_hpc_cluster.description = #{record.description,jdbcType=VARCHAR},
        res_hpc_cluster.business_category = #{record.businessCategory,jdbcType=VARCHAR},
        res_hpc_cluster.tenant_id = #{record.tenantId,jdbcType=BIGINT},
        res_hpc_cluster.cloud_env_id = #{record.cloudEnvId,jdbcType=BIGINT},
        res_hpc_cluster.charge_type = #{record.chargeType,jdbcType=VARCHAR},
        res_hpc_cluster.cluster_type = #{record.clusterType,jdbcType=VARCHAR},
        res_hpc_cluster.proess_phase = #{record.proessPhase,jdbcType=VARCHAR},
        res_hpc_cluster.proess_status = #{record.proessStatus,jdbcType=VARCHAR},
        res_hpc_cluster.`status` = #{record.status,jdbcType=VARCHAR},
        res_hpc_cluster.scenario = #{record.scenario,jdbcType=VARCHAR},
        res_hpc_cluster.vpc_peering_info = #{record.vpcPeeringInfo,jdbcType=VARCHAR},
        res_hpc_cluster.admin_user = #{record.adminUser,jdbcType=VARCHAR},
        res_hpc_cluster.admin_password = #{record.adminPassword,jdbcType=VARCHAR},
        res_hpc_cluster.ccp_external_address = #{record.ccpExternalAddress,jdbcType=VARCHAR},
        res_hpc_cluster.ccp_internel_address = #{record.ccpInternelAddress,jdbcType=VARCHAR},
        res_hpc_cluster.login_node_external_address = #{record.loginNodeExternalAddress,jdbcType=VARCHAR},
        res_hpc_cluster.login_node_internal_address = #{record.loginNodeInternalAddress,jdbcType=VARCHAR},
        res_hpc_cluster.error_info = #{record.errorInfo,jdbcType=VARCHAR},
        res_hpc_cluster.task_id = #{record.taskId,jdbcType=VARCHAR},
        res_hpc_cluster.task_description = #{record.taskDescription,jdbcType=VARCHAR},
        res_hpc_cluster.owner_id = #{record.ownerId,jdbcType=VARCHAR},
        res_hpc_cluster.org_sid = #{record.orgSid,jdbcType=BIGINT},
        res_hpc_cluster.created_org_sid = #{record.createdOrgSid,jdbcType=BIGINT},
        res_hpc_cluster.version = #{record.version,jdbcType=BIGINT},
        res_hpc_cluster.created_by = #{record.createdBy,jdbcType=VARCHAR},
        res_hpc_cluster.created_dt = #{record.createdDt,jdbcType=TIMESTAMP},
        res_hpc_cluster.updated_by = #{record.updatedBy,jdbcType=VARCHAR},
        res_hpc_cluster.updated_dt = #{record.updatedDt,jdbcType=TIMESTAMP}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective"
        parameterType="cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResHpcCluster">
        update res_hpc_cluster
        <set>
            <if test="resourceId != null">
                resource_id = #{resourceId,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
            </if>
            <if test="businessCategory != null">
                business_category = #{businessCategory,jdbcType=VARCHAR},
            </if>
            <if test="tenantId != null">
                tenant_id = #{tenantId,jdbcType=BIGINT},
            </if>
            <if test="cloudEnvId != null">
                cloud_env_id = #{cloudEnvId,jdbcType=BIGINT},
            </if>
            <if test="chargeType != null">
                charge_type = #{chargeType,jdbcType=VARCHAR},
            </if>
            <if test="clusterType != null">
                cluster_type = #{clusterType,jdbcType=VARCHAR},
            </if>
            <if test="proessPhase != null">
                proess_phase = #{proessPhase,jdbcType=VARCHAR},
            </if>
            <if test="proessStatus != null">
                proess_status = #{proessStatus,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=VARCHAR},
            </if>
            <if test="scenario != null">
                scenario = #{scenario,jdbcType=VARCHAR},
            </if>
            <if test="vpcPeeringInfo != null">
                vpc_peering_info = #{vpcPeeringInfo,jdbcType=VARCHAR},
            </if>
            <if test="adminUser != null">
                admin_user = #{adminUser,jdbcType=VARCHAR},
            </if>
            <if test="adminPassword != null">
                admin_password = #{adminPassword,jdbcType=VARCHAR},
            </if>
            <if test="ccpExternalAddress != null">
                ccp_external_address = #{ccpExternalAddress,jdbcType=VARCHAR},
            </if>
            <if test="ccpInternelAddress != null">
                ccp_internel_address = #{ccpInternelAddress,jdbcType=VARCHAR},
            </if>
            <if test="loginNodeExternalAddress != null">
                login_node_external_address = #{loginNodeExternalAddress,jdbcType=VARCHAR},
            </if>
            <if test="loginNodeInternalAddress != null">
                login_node_internal_address = #{loginNodeInternalAddress,jdbcType=VARCHAR},
            </if>
            <if test="errorInfo != null">
                error_info = #{errorInfo,jdbcType=VARCHAR},
            </if>
            <if test="taskId != null">
                task_id = #{taskId,jdbcType=VARCHAR},
            </if>
            <if test="taskDescription != null">
                task_description = #{taskDescription,jdbcType=VARCHAR},
            </if>
            <if test="ownerId != null">
                owner_id = #{ownerId,jdbcType=VARCHAR},
            </if>
            <if test="orgSid != null">
                org_sid = #{orgSid,jdbcType=BIGINT},
            </if>
            <if test="createdOrgSid != null">
                created_org_sid = #{createdOrgSid,jdbcType=BIGINT},
            </if>
            <if test="version != null">
                version = #{version,jdbcType=BIGINT},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdDt != null">
                created_dt = #{createdDt,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedDt != null">
                updated_dt = #{updatedDt,jdbcType=TIMESTAMP},
            </if>
            <if test="clusterFile != null">
                cluster_file = #{clusterFile,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null">
                start_time = #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                end_time = #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="preStatus != null">
                pre_status = #{preStatus},
            </if>
            <if test="unsubscribeTaskId != null">
                unsubscribe_task_id = #{unsubscribeTaskId,jdbcType=VARCHAR},
            </if>
             <if test="autoRelease != null">
               auto_release = #{autoRelease,jdbcType=INTEGER},
            </if>
             <if test="quotaReleaseDays != null">
               quota_release_days = #{quotaReleaseDays},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResHpcCluster">
        update res_hpc_cluster
        set resource_id                 = #{resourceId,jdbcType=VARCHAR},
            `name`                      = #{name,jdbcType=VARCHAR},
            description                 = #{description,jdbcType=VARCHAR},
            business_category           = #{businessCategory,jdbcType=VARCHAR},
            tenant_id                   = #{tenantId,jdbcType=BIGINT},
            cloud_env_id                = #{cloudEnvId,jdbcType=BIGINT},
            charge_type                 = #{chargeType,jdbcType=VARCHAR},
            cluster_type                = #{clusterType,jdbcType=VARCHAR},
            proess_phase                = #{proessPhase,jdbcType=VARCHAR},
            proess_status               = #{proessStatus,jdbcType=VARCHAR},
            `status`                    = #{status,jdbcType=VARCHAR},
            scenario                    = #{scenario,jdbcType=VARCHAR},
            vpc_peering_info            = #{vpcPeeringInfo,jdbcType=VARCHAR},
            admin_user                  = #{adminUser,jdbcType=VARCHAR},
            admin_password              = #{adminPassword,jdbcType=VARCHAR},
            ccp_external_address        = #{ccpExternalAddress,jdbcType=VARCHAR},
            ccp_internel_address        = #{ccpInternelAddress,jdbcType=VARCHAR},
            login_node_external_address = #{loginNodeExternalAddress,jdbcType=VARCHAR},
            login_node_internal_address = #{loginNodeInternalAddress,jdbcType=VARCHAR},
            error_info                  = #{errorInfo,jdbcType=VARCHAR},
            task_id                     = #{taskId,jdbcType=VARCHAR},
            task_description            = #{taskDescription,jdbcType=VARCHAR},
            owner_id                    = #{ownerId,jdbcType=VARCHAR},
            org_sid                     = #{orgSid,jdbcType=BIGINT},
            created_org_sid             = #{createdOrgSid,jdbcType=BIGINT},
            version                     = #{version,jdbcType=BIGINT},
            created_by                  = #{createdBy,jdbcType=VARCHAR},
            created_dt                  = #{createdDt,jdbcType=TIMESTAMP},
            updated_by                  = #{updatedBy,jdbcType=VARCHAR},
            updated_dt                  = #{updatedDt,jdbcType=TIMESTAMP},
            unsubscribe_task_id         = #{unsubscribeTaskId,jdbcType=VARCHAR},
            pre_status                  = #{preStatus},
            auto_release                = #{autoRelease}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <select id="selectByByPoolUuid" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from res_hpc_cluster res_hpc_cluster
        where res_hpc_cluster.pool_uuid = #{poolUuid}
    </select>

  <select id="selectByResource" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from res_hpc_cluster
    where resource_id = #{resourceId}
  </select>

  <select id="selectByResourceId" parameterType="map" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from res_hpc_cluster res_hpc_cluster
    where res_hpc_cluster.resource_id = #{resourceId} and id=#{id}
  </select>

  <select id="selectByShareId" resultMap="BaseResultMap">
    SELECT
        clu.id as res_hpc_cluster_id,
        clu.resource_id as res_hpc_cluster_resource_id,
        clu.status as res_hpc_cluster_status,
        clu.name as res_hpc_cluster_name,
        clu.start_time,
        clu.end_time
    from res_hpc_cluster_resource res
                 left join res_hpc_cluster clu on res.cluster_id = clu.id
    WHERE res.resource_type = 'SFS'
      and res.resource_id = #{shareId,jdbcType=BIGINT}
  </select>

  <select id="countSfsBindNum" resultType="int">
    select count(*)
    from res_hpc_cluster_resource cluster
                 left join res_share sh
            on sh.id = cluster.resource_id
    WHERE cluster.cluster_id = #{id}
      and cluster.resource_type = 'SFS'
      and sh.is_cluster_default = 0
      and sh.support_cluster_type = #{supportClusterType}
  </select>
    <select id="selectByClusterNameAndType" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List"/>
      from res_hpc_cluster res_hpc_cluster
      where  res_hpc_cluster.`name` = #{clusterName} and  res_hpc_cluster.cluster_type in('SAASPrivate','PreSAASPrivate','AdSAASPrivate')   LIMIT  1
    </select>

  <select id="sureMountPreCluster" resultType="cn.com.cloudstar.rightcloud.core.pojo.dto.hpc.ResHpcClusterPreDTO">
    select id, id as cluster_id, name, resource_id
    from res_hpc_cluster
    WHERE owner_id = #{userSid,jdbcType=VARCHAR}
      and cluster_type in ('AdSAASPrivate', 'PreSAASPrivate')
      and status IN ('available','expired')
  </select>

  <select id="getById" resultMap="BaseResultMap">
    select <include refid="Base_Column_List">
  </include> from res_hpc_cluster where id=#{id,jdbcType=BIGINT}
  </select>

  <select id="countHpcClusterIgnoreCaseName" parameterType="java.lang.String" resultType="java.lang.Integer">
    select count(0) from res_hpc_cluster where LOWER(`name`) =#{name} and  `status` not in ('rejected', 'unsubscribed', 'deleted')
  </select>

    <update id="updateStatusById">
    update res_hpc_cluster
    set `status` = #{status,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="getHpcClusterComputeResources" resultType="cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResHpcCluster">
    SELECT
      hpc.*,
      bms.cpu,
      bms.gpu
    FROM
      res_hpc_cluster hpc
        LEFT JOIN res_hpc_cluster_resource resource ON hpc.id = resource.cluster_id
        LEFT JOIN res_bms bms ON resource.resource_id = bms.id
    WHERE
      hpc.cluster_type != 'SAASShare'
      AND hpc.`status` IN ( 'available', 'expired', 'frozen', 'unsubscribing', 'upgrading' )
      AND resource.node_type = 'compute'
      AND resource.resource_type = 'BMS';
  </select>
</mapper>
