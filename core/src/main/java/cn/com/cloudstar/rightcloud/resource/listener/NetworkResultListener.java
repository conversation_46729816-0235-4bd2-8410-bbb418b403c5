
/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.listener;

import cn.com.cloudstar.rightcloud.adapter.pojo.lb.VServerGroupBackendServersAddsResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.Port;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.Router;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.Subnet;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.*;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.vo.RouterRouteVO;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.vo.SecurityGroupVO;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.vo.ServiceChainContextVO;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.vo.ServiceChainVO;
import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnv;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.Network;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.*;
import cn.com.cloudstar.rightcloud.common.constants.WebConstants;
import cn.com.cloudstar.rightcloud.common.constants.res.status.NetworkStatus;
import cn.com.cloudstar.rightcloud.common.constants.res.type.CloudEnvType;
import cn.com.cloudstar.rightcloud.common.constants.res.type.FeatureGroupType;
import cn.com.cloudstar.rightcloud.common.constants.res.type.NetworkManagement;
import cn.com.cloudstar.rightcloud.common.constants.status.SelfServiceInstanceStatus;
import cn.com.cloudstar.rightcloud.common.constants.type.ResourceType;
import cn.com.cloudstar.rightcloud.common.enums.ResVmExtEnum;
import cn.com.cloudstar.rightcloud.common.enums.resource.ResourceOperateEnum;
import cn.com.cloudstar.rightcloud.common.enums.resource.ResourceTypeEnum;
import cn.com.cloudstar.rightcloud.common.schedule.helper.ScheduleHelper;
import cn.com.cloudstar.rightcloud.common.util.DateUtil;
import cn.com.cloudstar.rightcloud.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.common.util.json.JsonUtil;
import cn.com.cloudstar.rightcloud.common.websocket.annonation.Message;
import cn.com.cloudstar.rightcloud.common.websocket.annonation.MessageParam;
import cn.com.cloudstar.rightcloud.common.websocket.support.OperateEnum;
import cn.com.cloudstar.rightcloud.common.websocket.support.ServerMsgType;
import cn.com.cloudstar.rightcloud.core.annotation.log.LogMethod;
import cn.com.cloudstar.rightcloud.core.annotation.log.LogParam;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.FlowFeatureGroupTarget;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResVpcPortExt;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicInfoUtil;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicWebUtil;
import cn.com.cloudstar.rightcloud.resource.dao.env.CloudEnvMapper;
import cn.com.cloudstar.rightcloud.resource.dao.lb.ResLbBackendMapper;
import cn.com.cloudstar.rightcloud.resource.dao.lb.ResLoadBalanceMapper;
import cn.com.cloudstar.rightcloud.resource.dao.network.*;
import cn.com.cloudstar.rightcloud.resource.dao.security.ResSecurityGroupMapper;
import cn.com.cloudstar.rightcloud.resource.dao.server.ResVmExtMapper;
import cn.com.cloudstar.rightcloud.resource.dao.server.ResVmMapper;
import cn.com.cloudstar.rightcloud.resource.notify.BizNotify;
import cn.com.cloudstar.rightcloud.resource.service.server.ResVmExtService;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import com.google.common.base.Strings;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 网络回调监听器
 *
 * <AUTHOR>
 */
@Component
public class NetworkResultListener {

    private final Logger logger = LoggerFactory.getLogger(NetworkResultListener.class);

    @Autowired
    private ResVpcMapper resVpcMapper;

    @Autowired
    private NetworkMapper networkMapper;

    @Autowired
    private ResVpcPortMapper resVpcPortMapper;

    @Autowired
    private ResVpcPortExtMapper resVpcPortExtMapper;

    @Autowired
    private ResFloatingIpMapper resFloatingIpMapper;

    @Autowired
    private ResVmMapper resVmMapper;

    @Autowired
    private NetworkIpMapper networkIpMapper;

    @Autowired
    private ResRouterMapper resRouterMapper;

    @Autowired
    private ResRouterRouteMapper resRouterRouteMapper;

    @Autowired
    private ResLoadBalanceMapper resLoadBalanceMapper;

    @Autowired
    private ResServiceChainMapper resServiceChainMapper;

    @Autowired
    private ResFlowFeatureGroupMapper resFlowFeatureGroupMapper;

    @Autowired
    private FlowFeatureGroupTargetMapper flowFeatureGroupTargetMapper;

    @Autowired
    private ResLbBackendMapper resLbBackendMapper;

    @Autowired
    private ResVmExtMapper resVmExtMapper;

    @Autowired
    private ResSecurityGroupMapper resSecurityGroupMapper;

    @Autowired
    private ResVpcPortSecurityGroupMapper resVpcPortSecurityGroupMapper;

    @Autowired
    private ResVmExtService resVmExtService;

    @Autowired
    private CloudEnvMapper cloudEnvMapper;

    /**
     * 资源池添加主机回调
     */
    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#vServerGroupBackendServersAddsResult.id", opUser = "#vServerGroupBackendServersAddsResult.opUser", success = "#vServerGroupBackendServersAddsResult.success", operate = ResourceOperateEnum.ADD_HOST, resourceType = ResourceTypeEnum.RES_SLB, orgSid = "#vServerGroupBackendServersAddsResult.orgSid")
    @Message(refKey = "#vServerGroupBackendServersAddsResult.id", envId = "#vServerGroupBackendServersAddsResult.cloudEnvId", msgType = ServerMsgType.RES_SLB, opUser = "#vServerGroupBackendServersAddsResult.opUser", operate = OperateEnum.ADD_HOST, success = "#vServerGroupBackendServersAddsResult.success", operateAction = "#vServerGroupBackendServersAddsResult.cloudEnvType", errorMsg = "#vServerGroupBackendServersAddsResult.errMsg")
    public void handleMessage(
            @LogParam("vServerGroupBackendServersAddsResult") @MessageParam("vServerGroupBackendServersAddsResult") VServerGroupBackendServersAddsResult vServerGroupBackendServersAddsResult) {
        logger.info("负载均衡资源组批量添加主机 | 回调参数 ： {}", JsonUtil.toJson(vServerGroupBackendServersAddsResult));
        if (vServerGroupBackendServersAddsResult.isSuccess()) {
            vServerGroupBackendServersAddsResult.getvServerGrouplbServersAddList().stream().forEach(vs -> {
                ResLbBackend resLbBackend = resLbBackendMapper.selectByPrimaryKey(vs.getId());
                resLbBackend.setUuid(vs.getMemoryId());
                resLbBackend.setStatus(NetworkStatus.ACTIVE);
                this.resLbBackendMapper.updateByPrimaryKeySelective(resLbBackend);
            });
        } else {
            vServerGroupBackendServersAddsResult.getvServerGrouplbServersAddList().stream().forEach(vs -> {
                ResLbBackend resLbBackend = resLbBackendMapper.selectByPrimaryKey(vs.getId());
                resLbBackend.setStatus(NetworkStatus.DELETED);
                this.resLbBackendMapper.updateByPrimaryKeySelective(resLbBackend);
            });
        }

    }

    /**
     * openstack私有网络创建回调
     */
    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "", opUser = "#netCreateResult.opUser", success = "#netCreateResult.success", operate = ResourceOperateEnum.OPERATE, resourceType = ResourceTypeEnum.VPC, other = "#netCreateResult.options.toString()", orgSid = "#netCreateResult.orgSid")
    @Message(refKey = "#netCreateResult.options.get('vpcId') != null ? #netCreateResult.options.get('vpcId'):#netCreateResult.options.get('subnetId')", envId = "#netCreateResult.cloudEnvId", msgType = ServerMsgType.VPC, operateAction = "#netCreateResult.options.get('operateTye')", opUser = "#netCreateResult.opUser", operate = OperateEnum.CREATE, success = "#netCreateResult.success", errorMsg = "#netCreateResult.errMsg")
    public void handleMessage(
            @LogParam("netCreateResult") @MessageParam("netCreateResult") NetCreateResult netCreateResult) {
        ResVpc vpc = new ResVpc();
        Network subnet = new Network();
        Map<String, Object> options = netCreateResult.getOptions();
        if ("create".equals(options.get("operateTye").toString())) {
            logger.debug("创建私有网络 | 回调参数 ： {}", JsonUtil.toJson(netCreateResult));
        } else if ("update".equals(options.get("operateTye").toString())) {
            logger.debug("编辑私有网络 | 回调参数 ： {}", JsonUtil.toJson(netCreateResult));
        } else if ("createSubnet".equals(options.get("operateTye").toString())) {
            logger.debug("创建私有网络子网 | 回调参数 ： {}", JsonUtil.toJson(netCreateResult));
        } else if ("updateSubnet".equals(options.get("operateTye").toString())) {
            logger.debug("编辑私有网络子网 | 回调参数 ： {}", JsonUtil.toJson(netCreateResult));
        } else if ("updatePort".equals(options.get("operateTye").toString())) {
            logger.debug("编辑私有网络的端口 | 回调参数 ： {}", JsonUtil.toJson(netCreateResult));
        } else {
            logger.debug("{} | 回调参数 ： {}", options.get("operateTye").toString(), JsonUtil.toJson(netCreateResult));
        }
        try {
            // maas 创建vpc 和 subnet时 单独判断
            boolean suportSubnetFailed =
                    CloudEnvType.MAAS.equals(netCreateResult.getProviderType()) || CloudEnvType.KSYUN.equals(
                            netCreateResult.getProviderType()) || CloudEnvType.KING_STACK.equals(
                            netCreateResult.getProviderType());
            if (suportSubnetFailed && "create".equals(options.get("operateTye").toString())
                    && !netCreateResult.isSuccess()) {
                if (netCreateResult.isVpcSuccess()) {
                    vpc.setId(Long.parseLong(options.get("vpcId").toString()));
                    vpc.setUuid(netCreateResult.getId());
                    vpc.setStatus(NetworkStatus.ACTIVE);
                    resVpcMapper.updateByPrimaryKeySelective(vpc);
                }
                if (!netCreateResult.isSubnetSuccess()) {
                    // 子网创建失败
                    logger.error("Maas 创建子网失败: {}", netCreateResult.getErrMsg());
                    networkMapper.deleteByPrimaryKey(Long.parseLong(options.get("subnetId").toString()));
                }
                return;
            }
            // 创建成功
            if (netCreateResult.isSuccess()) {
                if ("create".equals(options.get("operateTye").toString())) {
                    // 更新私有网络表
                    vpc.setId(Long.parseLong(options.get("vpcId").toString()));
                    vpc.setUuid(netCreateResult.getId());
                    vpc.setStatus(NetworkStatus.ACTIVE);
                    resVpcMapper.updateByPrimaryKeySelective(vpc);

                    // 更新子网表,应该在这进行修改
                    subnet.setId(Long.parseLong(options.get("subnetId").toString()));
                    subnet.setUuid(netCreateResult.getSubnet().getId());
                    subnet.setNetworkUuid(netCreateResult.getSubnet().getNetworkId());
                    if (CloudEnvType.HUAWEICLOUD.equals(netCreateResult.getProviderType())) {
                        subnet.setNetworkUuid(netCreateResult.getSubnet().getNetworkId());
                        String mySubnet = netCreateResult.getSubnet().getCidr();
                        subnet.setSubnet(mySubnet.substring(0, mySubnet.lastIndexOf("/")));
                        subnet.setTotalIp(netCreateResult.getSubnet().getTotalIp());
                    } else if (CloudEnvType.KSYUN.equals(netCreateResult.getProviderType())
                            || CloudEnvType.KING_STACK.equals(netCreateResult.getProviderType())) {
                        subnet.setGateway(netCreateResult.getSubnet().getGatewayIp());
                        subnet.setDns1(netCreateResult.getSubnet().getDns1());
                        subnet.setDns2(netCreateResult.getSubnet().getDns2());
                        subnet.setZone(netCreateResult.getSubnet().getZone());
                    }
                    subnet.setStatus(NetworkStatus.ACTIVE);
                    subnet.setTenantId(netCreateResult.getSubnet().getTenantId());
                    networkMapper.updateByPrimaryKeySelective(subnet);
                    if (CloudEnvType.CLOUDOS.equals(netCreateResult.getProviderType())) {
                        if (!StringUtil.isNullOrEmpty(options.get("oldNetworkIds"))) {
                            List<String> networkIds = (ArrayList) options.get("oldNetworkIds");
                            networkIds.stream().forEach(id -> {
                                Network network = networkMapper.selectByPrimaryKey(Long.parseLong(id));
                                network.setStatus(NetworkStatus.INACTIVE);
                                this.networkMapper.updateByPrimaryKeySelective(network);
                            });
                        }
                    }
                    if (CloudEnvType.ALIYUN.equals(netCreateResult.getProviderType()) || CloudEnvType.QCLOUD.equals(
                            netCreateResult.getProviderType()) || CloudEnvType.AWS.equals(
                            netCreateResult.getProviderType())) {
                        ResVpc resVpc = resVpcMapper.selectByPrimaryKey(
                                Long.parseLong(options.get("vpcId").toString()));
                        Router router = netCreateResult.getRouter();
                        ResRouter resRouter = new ResRouter();
                        resRouter.setCloudEnvId(netCreateResult.getCloudEnvId());
                        resRouter.setVpcId(vpc.getId());
                        resRouter.setVpcUuid(vpc.getUuid());
                        resRouter.setUuid(router.getRouterId());
                        resRouter.setRouterId(router.getRouterId());
                        resRouter.setName(
                                StringUtil.isNullOrEmpty(router.getName()) ? resVpc.getName() : router.getName());
                        resRouter.setRouteTableType(0);
                        resRouter.setStatus("ACTIVE");
                        resRouter.setOrgSid(Long.parseLong(netCreateResult.getOrgSid()));
                        resRouter.setCreatedBy(vpc.getCreatedBy());
                        resRouter.setUpdatedBy(vpc.getCreatedBy());
                        resRouter.setCreatedDt(vpc.getCreatedDt());
                        resRouter.setUpdatedDt(vpc.getCreatedDt());
                        resRouter.setVersion(1L);
                        resRouterMapper.insertSelective(resRouter);
                        List<RouterRouteVO> routerRouteVOS = router.getRouterRouteVOS();
                        for (RouterRouteVO routerRouteVO : routerRouteVOS) {
                            ResRouterRoute resRouterRoute = new ResRouterRoute();
                            resRouterRoute.setStatus("ACTIVE");
                            resRouterRoute.setCloudEnvId(netCreateResult.getCloudEnvId());
                            resRouterRoute.setRouterId(resRouter.getId());
                            resRouterRoute.setRouterUuid(resRouter.getUuid());
                            resRouterRoute.setUuid(routerRouteVO.getId());
                            resRouterRoute.setTargetCidr(routerRouteVO.getTargetCidr());
                            resRouterRoute.setNextPath(routerRouteVO.getNextPath());
                            resRouterRoute.setNextPathType(routerRouteVO.getNextPathType());
                            resRouterRoute.setRouteType(routerRouteVO.getRouteType());
                            resRouterRouteMapper.insertSelective(resRouterRoute);
                        }
                    }
                    if (CloudEnvType.KSYUN.equals(netCreateResult.getProviderType()) || CloudEnvType.KING_STACK.equals(
                            netCreateResult.getProviderType())) {
                        ResVpc resVpc = resVpcMapper.selectByPrimaryKey(
                                Long.parseLong(options.get("vpcId").toString()));
                        Router router = netCreateResult.getRouter();
                        List<RouterRouteVO> routerRouteVOS = router.getRouterRouteVOS();
                        for (RouterRouteVO routerRouteVO : routerRouteVOS) {
                            ResRouterRoute resRouterRoute = new ResRouterRoute();
                            resRouterRoute.setStatus("ACTIVE");
                            resRouterRoute.setCloudEnvId(netCreateResult.getCloudEnvId());
                            resRouterRoute.setRouterId(resVpc.getId());
                            resRouterRoute.setRouterUuid(routerRouteVO.getRouterId());
                            resRouterRoute.setUuid(routerRouteVO.getId());
                            resRouterRoute.setTargetCidr(routerRouteVO.getTargetCidr());
                            resRouterRoute.setNextPath(routerRouteVO.getNextPath());
                            resRouterRoute.setNextPathType(routerRouteVO.getNextPathType());
                            resRouterRoute.setRouteType(routerRouteVO.getRouteType());
                            resRouterRouteMapper.insertSelective(resRouterRoute);
                        }
                    }
                    //AWS 创建VPC时默认创建安全组
                    if (CloudEnvType.AWS.equals(netCreateResult.getProviderType()) || CloudEnvType.KSYUN.equals(
                            netCreateResult.getProviderType()) || CloudEnvType.KING_STACK.equals(
                            netCreateResult.getProviderType())) {
                        SecurityGroupVO securityGroupVO = netCreateResult.getSecurityGroupVO();
                        ResSecurityGroup resSecurityGroup = new ResSecurityGroup();
                        resSecurityGroup.setDescription(securityGroupVO.getDescription());
                        resSecurityGroup.setUuid(securityGroupVO.getUuid());
                        resSecurityGroup.setVpc(securityGroupVO.getVpcId());
                        resSecurityGroup.setName(securityGroupVO.getName());
                        resSecurityGroup.setRegion(securityGroupVO.getRegion());

                        String netType;
                        if (Strings.isNullOrEmpty(securityGroupVO.getVpcId())) {
                            netType = "经典网络";
                        } else {
                            netType = "专有网络";
                        }
                        resSecurityGroup.setNetType(netType);
                        resSecurityGroup.setSecurityGroupRules(securityGroupVO.getSecurityGroupRules());
                        resSecurityGroup.setCloudEnvProjectId(securityGroupVO.getCloudEnvProjectId());

                        resSecurityGroup.setOrgSid(Long.parseLong(netCreateResult.getOrgSid()));
                        resSecurityGroup.setCreatedBy(vpc.getCreatedBy());
                        resSecurityGroup.setUpdatedBy(vpc.getCreatedBy());
                        resSecurityGroup.setCreatedDt(vpc.getCreatedDt());
                        resSecurityGroup.setUpdatedDt(vpc.getCreatedDt());
                        resSecurityGroup.setVersion(1L);
                        resSecurityGroup.setCloudEnvId(netCreateResult.getCloudEnvId());
                        this.resSecurityGroupMapper.insertSelective(resSecurityGroup);
                    }

                    // 插入端口数据
                    if (netCreateResult.getPorts() != null && netCreateResult.getPorts().size() > 0) {
                        for (Port port : netCreateResult.getPorts()) {
                            ResVpcPort vpcPort = new ResVpcPort();
                            vpcPort.setVpcId(Long.parseLong(options.get("vpcId").toString()));
                            vpcPort.setUuid(port.getId());
                            if (CloudEnvType.HUAWEICLOUD.equals(netCreateResult.getProviderType())) {
                                vpcPort.setPortName(netCreateResult.getSubnet().getNetworkId());
                            } else {
                                vpcPort.setPortName(port.getName());
                            }
                            if (port.getIps() != null && port.getIps().size() > 0) {
                                for (String value : port.getIps()) {
                                    vpcPort.setFixedIp(value);
                                }
                            }
                            vpcPort.setDevice(port.getDeviceOwner());
                            if (CloudEnvType.HUAWEICLOUD.equals(netCreateResult.getProviderType())) {
                                vpcPort.setStatus(NetworkStatus.DOWN);
                            } else {
                                vpcPort.setStatus(NetworkStatus.ACTIVE);
                            }
                            this.resVpcPortMapper.insertSelective(vpcPort);
                        }
                    }

                } else if ("createSubnet".equals(options.get("operateTye").toString())) {
                    if (netCreateResult.getPorts() != null && netCreateResult.getPorts().size() > 0) {
                        for (Port port : netCreateResult.getPorts()) {
                            ResVpcPort vpcPort = new ResVpcPort();
                            vpcPort.setVpcId(Long.parseLong(options.get("vpcId").toString()));
                            vpcPort.setUuid(port.getId());

                            if (CloudEnvType.HUAWEICLOUD.equals(netCreateResult.getProviderType())) {
                                vpcPort.setPortName(netCreateResult.getSubnet().getNetworkId());
                            } else {
                                vpcPort.setPortName(port.getName());
                            }
                            if (port.getIps() != null && port.getIps().size() > 0) {
                                for (String value : port.getIps()) {
                                    vpcPort.setFixedIp(value);
                                }
                            }
                            if (CloudEnvType.HUAWEICLOUD.equals(netCreateResult.getProviderType())) {
                                vpcPort.setStatus(NetworkStatus.DOWN);
                            } else {
                                vpcPort.setStatus(NetworkStatus.ACTIVE);
                            }
                            vpcPort.setDevice(port.getDeviceOwner());

                            this.resVpcPortMapper.insertSelective(vpcPort);
                        }
                    }
                    subnet.setId(Long.parseLong(options.get("subnetId").toString()));
                    if (CloudEnvType.HUAWEICLOUD.equals(netCreateResult.getProviderType())) {
                        subnet.setNetworkUuid(netCreateResult.getSubnet().getNetworkId());
                        String mySubnet = netCreateResult.getSubnet().getCidr();
                        subnet.setSubnet(mySubnet.substring(0, mySubnet.lastIndexOf("/")));
                        subnet.setTotalIp(netCreateResult.getSubnet().getTotalIp());
                    }
                    subnet.setUuid(netCreateResult.getSubnet().getId());
                    subnet.setStatus(NetworkStatus.ACTIVE);
                    subnet.setGateway(netCreateResult.getSubnet().getGatewayIp());
                    subnet.setDns1(netCreateResult.getSubnet().getDns1());
                    subnet.setDns2(netCreateResult.getSubnet().getDns2());
                    if (CloudEnvType.KSYUN.equals(netCreateResult.getProviderType()) || CloudEnvType.KING_STACK.equals(
                            netCreateResult.getProviderType())) {
                        subnet.setZone(netCreateResult.getSubnet().getZone());
                    }
                    networkMapper.updateByPrimaryKeySelective(subnet);
                    if (!StringUtil.isNullOrEmpty(options.get("quotaSubnetId"))) {
                        Network oldNetwork = networkMapper.selectByPrimaryKey(
                                Long.parseLong(options.get("quotaSubnetId") + ""));
                        oldNetwork.setStatus(NetworkStatus.INACTIVE);
                        networkMapper.updateByPrimaryKeySelective(oldNetwork);
                    }
                    if (CloudEnvType.KSYUN.equals(netCreateResult.getProviderType()) || CloudEnvType.KING_STACK.equals(
                            netCreateResult.getProviderType())) {
                        ResVpc resVpc = resVpcMapper.selectByPrimaryKey(
                                Long.parseLong(options.get("vpcId").toString()));
                        Router router = netCreateResult.getRouter();
                        List<RouterRouteVO> routerRouteVOS = router.getRouterRouteVOS();
                        for (RouterRouteVO routerRouteVO : routerRouteVOS) {
                            ResRouterRoute resRouterRoute = new ResRouterRoute();
                            resRouterRoute.setStatus("ACTIVE");
                            resRouterRoute.setCloudEnvId(netCreateResult.getCloudEnvId());
                            resRouterRoute.setRouterId(resVpc.getId());
                            resRouterRoute.setRouterUuid(routerRouteVO.getRouterId());
                            resRouterRoute.setUuid(routerRouteVO.getId());
                            resRouterRoute.setTargetCidr(routerRouteVO.getTargetCidr());
                            resRouterRoute.setNextPath(routerRouteVO.getNextPath());
                            resRouterRoute.setNextPathType(routerRouteVO.getNextPathType());
                            resRouterRoute.setRouteType(routerRouteVO.getRouteType());
                            resRouterRouteMapper.insertSelective(resRouterRoute);
                        }
                    }
                } else if ("updateSubnetRouter".equals(options.get("operateTye").toString()) && Objects.nonNull(
                        netCreateResult.getSubnetNum())) {
                    ResRouter resRouter = new ResRouter();
                    resRouter.setId(Convert.toLong(options.get("currentRouterId")));
                    resRouter.setSubnetNum(netCreateResult.getSubnetNum());
                    resRouterMapper.updateByPrimaryKeySelective(resRouter);

                    if (netCreateResult.getSubnetNum() > 0 && !CollectionUtils.isEmpty(netCreateResult.getSubnets())) {
                        Network network = new Network();
                        for (Subnet sub : netCreateResult.getSubnets()) {
                            long networkId = Long.parseLong(sub.getNetworkId());
                            resRouterMapper.decreaseSubnetNumBySubnetId(networkId);

                            network.setId(networkId);
                            network.setRouterId(resRouter.getId());
                            this.networkMapper.updateByPrimaryKeySelective(network);
                        }
                    }
                }

                if (CloudEnvType.QCLOUD.equals(netCreateResult.getProviderType())) {
                    Map<String, String> params = Maps.newHashMap();
                    params.put("key", "subnet");
                    params.put("companyId", netCreateResult.getOrgSid());

                    ScheduleHelper.manualSyncTask(netCreateResult.getCloudEnvId(), params,
                                                  BasicInfoUtil.getCurrentUserSid());
                }
            } else { // 创建失败
                logger.error(netCreateResult.getErrMsg());
                if ("create".equals(options.get("operateTye").toString())) {
                    // 更新状态：创建失败
                    vpc.setId(Long.parseLong(options.get("vpcId").toString()));
                    vpc.setStatus(NetworkStatus.CREATE_FAILURE);
                    resVpcMapper.updateByPrimaryKeySelective(vpc);

                    subnet.setId(Long.parseLong(options.get("subnetId").toString()));
                    subnet.setStatus(NetworkStatus.CREATE_FAILURE);
                    networkMapper.updateByPrimaryKeySelective(subnet);
                }
                if ("createSubnet".equals(options.get("operateTye").toString())) {
                    subnet.setId(Long.parseLong(options.get("subnetId").toString()));
                    subnet.setStatus(NetworkStatus.CREATE_FAILURE);
                    networkMapper.updateByPrimaryKeySelective(subnet);
                }
                if ("updateSubnetRouter".equals(options.get("operateTye").toString())) {
                    if (Objects.nonNull(netCreateResult.getSubnetNum())) {
                        ResRouter resRouter = new ResRouter();
                        resRouter.setId(Convert.toLong(options.get("currentRouterId")));
                        resRouter.setSubnetNum(netCreateResult.getSubnetNum());
                        resRouterMapper.updateByPrimaryKeySelective(resRouter);
                    }
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    /**
     * openstack私有网络删除回调
     */
    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#netDeleteResult.options.get('netId')", opUser = "#netDeleteResult.opUser", success = "#netDeleteResult.success", operate = ResourceOperateEnum.DELETE, resourceType = ResourceTypeEnum.VPC, other = "#netDeleteResult.options.toString()", orgSid = "#netDeleteResult.orgSid")
    @Message(refKey = "#netDeleteResult.options.get('netId')", envId = "#netDeleteResult.cloudEnvId", msgType = ServerMsgType.VPC, opUser = "#netDeleteResult.opUser", operate = OperateEnum.DELETE, success = "#netDeleteResult.success", operateAction = "#netDeleteResult.options.get('operateTye')", errorMsg = "#netDeleteResult.errMsg")
    public void handleMessage(
            @LogParam("netDeleteResult") @MessageParam("netDeleteResult") NetDeleteResult netDeleteResult) {

        Map<String, Object> options = netDeleteResult.getOptions();
        Long netId = Long.parseLong(options.get("netId").toString());

        if (!StringUtil.isNullOrEmpty(options.get("operateTye")) && "remove".equals(options.get("operateTye"))) {
            logger.debug("删除私有网络 | 回调参数 ： {}", JsonUtil.toJson(netDeleteResult));
        } else {
            logger.debug("删除私有网络子网 | 回调参数 ： {}", JsonUtil.toJson(netDeleteResult));
        }

        try {        // 删除成功
            if (netDeleteResult.isSuccess()) {
                if (!StringUtil.isNullOrEmpty(options.get("operateTye")) && "remove".equals(
                        options.get("operateTye"))) {
                    if (CloudEnvType.ALIYUN.equals(netDeleteResult.getProviderType())) {
                        List<ResRouter> routers = resRouterMapper.selectByParams(new Criteria("vpcId", netId));

                        if (!CollectionUtils.isEmpty(routers)) {
                            ResRouter resRouter = routers.get(0);

                            // 阿里云删除与私有网络关联的路由器
                            resRouterMapper.deleteByPrimaryKey(resRouter.getId());

                            Criteria criteria = new Criteria();
                            criteria.put("routerId", resRouter.getId());

                            //删除路由器路由表条目
                            List<ResRouterRoute> resRouterRoutes = resRouterRouteMapper.selectByParams(criteria);
                            for (ResRouterRoute item : resRouterRoutes) {
                                resRouterRouteMapper.deleteByPrimaryKey(item.getId());
                            }
                        }

                    }
                    if (CloudEnvType.AWS.equals(netDeleteResult.getProviderType()) || CloudEnvType.KSYUN.equals(
                            netDeleteResult.getProviderType()) || CloudEnvType.KING_STACK.equals(
                            netDeleteResult.getProviderType())) {
                        ResVpc resVpc = resVpcMapper.selectByPrimaryKey(netId);
                        //删除安全组
                        Criteria criteria = new Criteria();
                        criteria.put("vpc", resVpc.getUuid());
                        List<ResSecurityGroup> resSecurityGroups = resSecurityGroupMapper.selectBaseByParams(criteria);
                        for (ResSecurityGroup item : resSecurityGroups) {
                            resSecurityGroupMapper.deleteByPrimaryKey(item.getId());
                        }
                        // 删除路由表
                        criteria.clear();
                        criteria.put("vpcId", resVpc.getId());
                        List<ResRouter> resRouters = resRouterMapper.selectByParams(criteria);
                        for (ResRouter router : resRouters) {
                            //删除路由器
                            resRouterMapper.deleteByPrimaryKey(router.getId());
                            //删除静态路由表
                            criteria.clear();
                            criteria.put("routerId", router.getId());
                            resRouterRouteMapper.deleteByParams(criteria);
                        }
                    }
                    //删除私有网络
                    resVpcMapper.deleteByPrimaryKey(netId);

                }

                if ((!StringUtil.isNullOrEmpty(options.get("operateTye"))) && "removeSubnet".equals(
                        options.get("operateTye").toString())) {
                    //华为云删除portid逻辑
                    if (CloudEnvType.HUAWEICLOUD.equals(netDeleteResult.getProviderType())) {
                        String networkUuid = (String) netDeleteResult.getOptions().get("networkUuid");
                        List<ResVpcPort> portName = resVpcPortMapper.selectByParams(
                                new Criteria("portName", networkUuid));
                        for (ResVpcPort item : portName) {
                            resVpcPortMapper.deleteByPrimaryKey(item.getId());
                        }
                        //获取到所有的route数据，对该子网的数据进行删除
                        HashMap routeInfo = (HashMap) netDeleteResult.getOptions().get("routeInfo");
                        Set keySet = routeInfo.keySet();
                        for (Object targetCidr : keySet) {
                            resRouterRouteMapper.deleteByParams(new Criteria("targetCidr", String.valueOf(targetCidr)));
                        }
                    }
                    if (CloudEnvType.CLOUDOS.equals(netDeleteResult.getProviderType())) {
                        if (!StringUtil.isNullOrEmpty(options.get("oldNetworkIds"))) {
                            List<String> networkIds = (ArrayList) options.get("oldNetworkIds");
                            networkIds.stream().forEach(id -> {
                                Network network = networkMapper.selectByPrimaryKey(Long.parseLong(id));
                                network.setStatus(NetworkStatus.ACTIVE);
                                this.networkMapper.updateByPrimaryKeySelective(network);
                            });
                        }
                    }
                    if (CloudEnvType.KSYUN.equals(netDeleteResult.getProviderType()) || CloudEnvType.KING_STACK.equals(
                            netDeleteResult.getProviderType())) {
                        Network network = this.networkMapper.selectByPrimaryKey(
                                Long.valueOf(options.get("subnetId").toString()));
                        Criteria criteria = new Criteria();
                        criteria.put("routerId", network.getNetVpcId());
                        criteria.put("targetCidr", network.getCidr());
                        this.resRouterRouteMapper.deleteByParams(criteria);
                    }
                    networkMapper.deleteByPrimaryKey(Long.valueOf(options.get("subnetId").toString()));
                } else {
                    // 删除子网
                    Criteria criteria = new Criteria();
                    criteria.put("netVpcId", netId);
                    List<Network> subnetList = this.networkMapper.selectByParams(criteria);
                    if (CloudEnvType.HUAWEICLOUD.equals(netDeleteResult.getProviderType())) {
                        //删除所有的port信息
                        List<ResVpcPort> portName = resVpcPortMapper.selectByParams(
                                new Criteria("vpcId", netDeleteResult.getOptions().get("netId")));
                        for (ResVpcPort item : portName) {
                            resVpcPortMapper.deleteByPrimaryKey(item.getId());
                        }
                        //删除所有的route信息
                        List<ResRouterRoute> uuid = resRouterRouteMapper.selectByParams(
                                new Criteria("uuid", netDeleteResult.getNetId()));
                        for (ResRouterRoute item : uuid) {
                            resRouterRouteMapper.deleteByPrimaryKey(item.getId());
                        }
                    }
                    for (Network resSubnet : subnetList) {
                        this.networkMapper.deleteByPrimaryKey(resSubnet.getId());
                    }
                    if (CloudEnvType.CLOUDOS.equals(netDeleteResult.getProviderType())) {
                        if (!StringUtil.isNullOrEmpty(options.get("oldNetworkIds"))) {
                            List<String> networkIds = (ArrayList) options.get("oldNetworkIds");
                            networkIds.stream().forEach(id -> {
                                Network network = networkMapper.selectByPrimaryKey(Long.parseLong(id));
                                network.setStatus(NetworkStatus.ACTIVE);
                                this.networkMapper.updateByPrimaryKeySelective(network);
                            });
                        }
                    } else if (CloudEnvType.QCLOUD.equals(netDeleteResult.getProviderType())) {
                        Map<String, String> params = Maps.newHashMap();
                        params.put("key", "router");
                        params.put("companyId", netDeleteResult.getOrgSid());

                        ScheduleHelper.manualSyncTask(netDeleteResult.getCloudEnvId(), params,
                                                      BasicInfoUtil.getCurrentUserSid());
                    }
                }
            } else {
                if (!StringUtil.isNullOrEmpty(options.get("operateTye")) && "remove".equals(
                        options.get("operateTye"))) {
                    //删除失败，将网络状态重置
                    ResVpc resVpc = new ResVpc();
                    resVpc.setStatus(NetworkStatus.ACTIVE);
                    resVpc.setId(netId);
                    resVpcMapper.updateByPrimaryKeySelective(resVpc);
                }

                if ((!StringUtil.isNullOrEmpty(options.get("operateTye"))) && "removeSubnet".equals(
                        options.get("operateTye").toString())) {
                    Network network = new Network();
                    network.setId(Long.valueOf(options.get("subnetId").toString()));
                    network.setStatus(NetworkStatus.ACTIVE);
                    networkMapper.updateByPrimaryKeySelective(network);
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
    }

    /**
     * 弹性ip创建回调
     */
    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#floatingIpCreateResult.options.get('id')", opUser = "#floatingIpCreateResult.opUser", success = "#floatingIpCreateResult.success", operate = ResourceOperateEnum.CREATE, resourceType = ResourceTypeEnum.FLOATING_IP, orgSid = "#floatingIpCreateResult.orgSid")
    @Message(refKey = "#floatingIpCreateResult.options.get('id')", envId = "#floatingIpCreateResult.cloudEnvId", msgType = ServerMsgType.FLOATING_IP, opUser = "#floatingIpCreateResult.opUser", operate = OperateEnum.CREATE, success = "#floatingIpCreateResult.success", refNameKey = "#floatingIpCreateResult.floatingIpAddr", errorMsg = "#floatingIpCreateResult.errMsg")
    @BizNotify
    public void handleMessage(
            @LogParam("floatingIpCreateResult") @MessageParam("floatingIpCreateResult") FloatingIpCreateResult floatingIpCreateResult) {
        logger.info("创建弹性ip回调 | 回调参数 ： {}", JsonUtil.toJson(floatingIpCreateResult));
        Map<String, Object> options = floatingIpCreateResult.getOptions();
        Long serviceDeployInstId = null;
        try {
            ResFloatingIp resFloatingIpDB = resFloatingIpMapper.selectByPrimaryKey(
                    Long.valueOf(options.get("id").toString()));
            if (null == resFloatingIpDB) {
                logger.warn("弹性ip id:{} 未找到！ 跳过", options.get("id"));
                return;
            }
            serviceDeployInstId = resFloatingIpDB.getServiceDeployInstId();

            // 创建成功
            if (floatingIpCreateResult.isSuccess()) {
                ResFloatingIp resFloatingIp = new ResFloatingIp();
                resFloatingIp.setId(Long.valueOf(options.get("id").toString()));
                resFloatingIp.setStatus(NetworkStatus.UNUSED);
                resFloatingIp.setUuid(floatingIpCreateResult.getFloatingIpId());
                resFloatingIp.setIp(floatingIpCreateResult.getFloatingIpAddr());
                resFloatingIp.setBandWidth(floatingIpCreateResult.getBandWidth());
                resFloatingIp.setPolicyId(floatingIpCreateResult.getPolicyId());
                resFloatingIp.setRuleId(floatingIpCreateResult.getRuleId());
                resFloatingIp.setStartTime(new Date());
                resFloatingIp.setEndTime(
                        DateUtil.plusMonths(resFloatingIp.getStartTime(), floatingIpCreateResult.getPeriod()));
                resFloatingIpMapper.updateByPrimaryKeySelective(resFloatingIp);
            } else { // 创建失败
                logger.error("创建弹性IP失败:[{}]", floatingIpCreateResult.getErrMsg());
                Long id = Long.valueOf(options.get("id").toString());
                ResFloatingIp ip = new ResFloatingIp();
                ip.setId(id);
                ip.setStatus(NetworkStatus.CREATE_FAILURE);
                resFloatingIpMapper.updateByPrimaryKeySelective(ip);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
    }

    /**
     * 弹性ip带宽修改回调
     */
    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#bandwidthReviseResult.options.get('floatingIpId')", opUser = "#bandwidthReviseResult.opUser", success = "#bandwidthReviseResult.success", operate = ResourceOperateEnum.MODIFY, resourceType = ResourceTypeEnum.FLOATING_IP, orgSid = "#bandwidthReviseResult.orgSid")
    @Message(refKey = "#bandwidthReviseResult.options.get('floatingIpId')", envId = "#bandwidthReviseResult.cloudEnvId", msgType = ServerMsgType.BANDWIDTH, opUser = "#bandwidthReviseResult.opUser", operate = OperateEnum.MODIFY, success = "#bandwidthReviseResult.success", errorMsg = "#bandwidthReviseResult.errMsg")
    @BizNotify
    public void handleMessage(
            @LogParam("bandwidthReviseResult") @MessageParam("bandwidthReviseResult") BandwidthReviseResult bandwidthReviseResult) {
        logger.info("弹性ip带宽修改回调 | 回调参数 ： {}", JsonUtil.toJson(bandwidthReviseResult));
        ResFloatingIp resFloatingIp = null;
        Map<String, Object> options = bandwidthReviseResult.getOptions();
        try {
            // 修改成功
            if (bandwidthReviseResult.isSuccess()) {
                resFloatingIp = new ResFloatingIp();
                resFloatingIp.setId(Long.valueOf(options.get("floatingIpId").toString()));
                resFloatingIp.setBandWidth(bandwidthReviseResult.getBandwidth());
                resFloatingIp.setInternetChargeType(bandwidthReviseResult.getInternetChargeType());
                resFloatingIp.setBandWidthName(bandwidthReviseResult.getBandWidthName());
                ResFloatingIp ip = resFloatingIpMapper.selectByPrimaryKey(Convert.toLong(options.get("floatingIpId")));
                if (Objects.nonNull(ip) && Objects.nonNull(ip.getInstanceId()) && "RES-HOST".equals(
                        ip.getInstanceType())) {
                    ResVm resVm = new ResVm();
                    resVm.setInternetMaxBandwidthOut(Convert.toInt(bandwidthReviseResult.getBandwidth()));
                    resVmMapper.updateByExampleSelective(resVm, MapUtil.of("instanceId", ip.getInstanceId()));
                }
                resFloatingIpMapper.updateByPrimaryKeySelective(resFloatingIp);

            } else { // 修改失败
                logger.error(bandwidthReviseResult.getErrMsg());
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
    }

    /**
     * 弹性ip删除回调
     */
    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#floatingIpDeleteResult.options.get('id')", opUser = "#floatingIpDeleteResult.opUser", success = "#floatingIpDeleteResult.success", operate = ResourceOperateEnum.DELETE, resourceType = ResourceTypeEnum.FLOATING_IP, orgSid = "#floatingIpDeleteResult.orgSid")
    @Message(refKey = "#floatingIpDeleteResult.options.get('id')", envId = "#floatingIpDeleteResult.cloudEnvId", msgType = ServerMsgType.FLOATING_IP, opUser = "#floatingIpDeleteResult.opUser", operate = OperateEnum.DELETE, success = "#floatingIpDeleteResult.success", errorMsg = "#floatingIpDeleteResult.errMsg")
    @BizNotify
    public void handleMessage(
            @LogParam("floatingIpDeleteResult") @MessageParam("floatingIpDeleteResult") FloatingIpDeleteResult floatingIpDeleteResult) {
        logger.info("删除弹性ip回调 | 回调参数 ： {}", JsonUtil.toJson(floatingIpDeleteResult));
        ResFloatingIp resFloatingIp = null;
        Map<String, Object> options = floatingIpDeleteResult.getOptions();
        try {
            // 删除成功
            if (floatingIpDeleteResult.isSuccess()) {
                resFloatingIp = new ResFloatingIp();
                resFloatingIp.setId(Long.valueOf(options.get("id").toString()));
                String resVmId = options.get("resVmId").toString();
                resVmMapper.unbandingFloatingIp(resVmId);
                resFloatingIp.setStatus(NetworkStatus.DELETED);
                resFloatingIp.setEndTime(Calendar.getInstance().getTime());

                // 更新状态为已删除
                resFloatingIpMapper.updateByPrimaryKeySelective(resFloatingIp);

            } else { // 删除失败
                logger.error(floatingIpDeleteResult.getErrMsg());
                resFloatingIp = new ResFloatingIp();
                resFloatingIp.setId(Long.valueOf(options.get("id").toString()));
                // 还原为未使用状态
                resFloatingIp.setStatus(NetworkStatus.UNUSED);
                resFloatingIpMapper.updateByPrimaryKeySelective(resFloatingIp);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
    }

    /**
     * 弹性ip绑定回调
     */
    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#floatingIpAttachResult.options.get('resFloatingIpId')", opUser = "#floatingIpAttachResult.opUser", success = "#floatingIpAttachResult.success", operate = ResourceOperateEnum.ATTACH, resourceType = ResourceTypeEnum.FLOATING_IP, orgSid = "#floatingIpAttachResult.orgSid")
    @Message(refKey = "#floatingIpAttachResult.options.get('resFloatingIpId')", envId = "#floatingIpAttachResult.cloudEnvId", msgType = ServerMsgType.FLOATING_IP, opUser = "#floatingIpAttachResult.opUser", operate = OperateEnum.ATTACH, success = "#floatingIpAttachResult.success", refNameKey = "#floatingIpAttachResult.floatingIp", errorMsg = "#floatingIpAttachResult.errMsg")
    public void handleMessage(
            @LogParam("floatingIpAttachResult") @MessageParam("floatingIpAttachResult") FloatingIpAttachResult floatingIpAttachResult) {
        logger.info("弹性ip绑定回调 | 回调参数 ： {}", JsonUtil.toJson(floatingIpAttachResult));
        ResFloatingIp resFloatingIp = null;
        Map<String, Object> options = floatingIpAttachResult.getOptions();
        try {
            // 绑定成功
            if (floatingIpAttachResult.isSuccess()) {
                //更新主机表
                resFloatingIp = resFloatingIpMapper.selectByPrimaryKey(
                        Long.valueOf(options.get("resFloatingIpId").toString()));
                String instanceType = Objects.toString(options.get("instanceType"), null);
                if (ResourceType.RES_SLB.equalsIgnoreCase(instanceType)) {
                    ResLoadBalance lb = resLoadBalanceMapper.selectByPrimaryKey(
                            Long.parseLong(options.get("resVmId").toString()));
                    lb.setResFloatingIpAddress(resFloatingIp.getIp());
                    lb.setResFloatingIpId(resFloatingIp.getId());
                    lb.setBandwidth(Integer.valueOf(resFloatingIp.getBandWidth()));
                    resLoadBalanceMapper.updateByPrimaryKeySelective(lb);
                    resFloatingIp.setInstanceId(lb.getUuid());
                    resFloatingIp.setInstanceName(lb.getLbName());
                    resFloatingIp.setInstanceType(ResourceType.RES_SLB);
                } else if (ResourceType.RES_VPC_PORT.equalsIgnoreCase(instanceType)) {
                    // 更新IP和弹性IP绑定关系
                    CloudEnv cloudEnv = cloudEnvMapper.selectByPrimaryKey(resFloatingIp.getEnvId());
                    if (CloudEnvType.QCLOUD.equals(cloudEnv.getCloudEnvType()) || CloudEnvType.ALIYUN.equals(
                            cloudEnv.getCloudEnvType())) {
                        NetworkIp networkIp = networkIpMapper.selectByPrimaryKey(
                                Long.parseLong(options.get("fixedIpId").toString()));
                        networkIp.setPublicIp(resFloatingIp.getIp());
                        networkIpMapper.updateByPrimaryKey(networkIp);
                        ResVpcPort port = resVpcPortMapper.selectByPrimaryKey(
                                Long.parseLong(options.get("resPortId").toString()));
                        resFloatingIp.setPortId(port.getId().toString());
                        resFloatingIp.setInstanceType(ResourceType.RES_VPC_PORT);
                        resFloatingIp.setInstanceName(port.getPortName());
                    } else if (CloudEnvType.HUAWEICLOUD.equals(cloudEnv.getCloudEnvType())) {
                        ResVpcPort port = resVpcPortMapper.selectByPrimaryKey(
                                Long.parseLong(options.get("fixedIpId").toString()));
                        resFloatingIp.setPortId(port.getId().toString());
                        //弹性IP中设置关联对象为网卡(虚拟IP)
                        ResVpcPortExt ext = new ResVpcPortExt();
                        ext.setPortId(port.getId().toString());
                        ext.setResourceId(resFloatingIp.getId().toString());
                        ext.setType(WebConstants.VPC_PORT_EXT_TYPE.EIP);
                        ext.setExtra(resFloatingIp.getIp());
                        resVpcPortExtMapper.insertSelective(ext);
                    }
                    resFloatingIp.setInstanceType(ResourceType.RES_VPC_PORT);

                } else {
                    ResVm resVm = resVmMapper.selectByPrimaryKey(options.get("resVmId").toString());
                    resVm.setPublicIp(resFloatingIp.getIp());
                    if (Objects.nonNull(resFloatingIp.getBandWidth())) {
                        resVm.setInternetMaxBandwidthOut(Integer.valueOf(resFloatingIp.getBandWidth()));
                    }
                    if (CloudEnvType.QCLOUD.equals(resVm.getCloudEnvType()) && !Strings.isNullOrEmpty(
                            options.get("bandwidth").toString())) {
                        resVm.setInternetMaxBandwidthOut(Integer.valueOf(options.get("bandwidth").toString()));
                    }
                    resVmMapper.updateByPrimaryKeySelective(resVm);
                    resFloatingIp.setInstanceId(resVm.getInstanceId());
                    resFloatingIp.setInstanceName(resVm.getInstanceName());
                    resFloatingIp.setInstanceType(ResourceType.RES_HOST);
                }

                //更新弹性IP表
                resFloatingIp.setFixedIp(floatingIpAttachResult.getFixedIp());
                resFloatingIp.setStatus(NetworkStatus.ACTIVE);
                resFloatingIp.setStartTime(Calendar.getInstance().getTime());
                resFloatingIpMapper.updateByPrimaryKeySelective(resFloatingIp);

            } else { //绑定失败
                logger.error(floatingIpAttachResult.getErrMsg());
                resFloatingIp = new ResFloatingIp();
                resFloatingIp.setId(Long.valueOf(options.get("resFloatingIpId").toString()));
                resFloatingIp.setStatus(NetworkStatus.UNUSED);
                resFloatingIpMapper.updateByPrimaryKeySelective(resFloatingIp);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
    }

    /**
     * 虚拟网卡绑定主机回调
     */
    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#portAttachInstanceResult.Id", opUser = "#portAttachInstanceResult.opUser", success = "#portAttachInstanceResult.success", operate = ResourceOperateEnum.ATTACH, resourceType = ResourceTypeEnum.VPC_PORT, orgSid = "#portAttachInstanceResult.orgSid")
    @Message(refKey = "#portAttachInstanceResult.Id", envId = "#portAttachInstanceResult.cloudEnvId", msgType = ServerMsgType.VPC_PORT, opUser = "#portAttachInstanceResult.opUser", operate = OperateEnum.ATTACH, success = "#portAttachInstanceResult.success", refNameKey = "#portAttachInstanceResult.portName", errorMsg = "#portAttachInstanceResult.errMsg")
    public void handleMessage(
            @LogParam("portAttachInstanceResult") @MessageParam("portAttachInstanceResult") PortAttachInstanceResult portAttachInstanceResult) {
        logger.info("虚拟网卡绑定实例回调 | 回调参数 ： {}", JsonUtil.toJson(portAttachInstanceResult));
        ResVpcPort resVpcPort = resVpcPortMapper.selectBaseByPrimaryKey(portAttachInstanceResult.getId());
        try {
            // 绑定成功
            if (portAttachInstanceResult.isSuccess()) {
                ResVm resVm = resVmMapper.selectByInstanceId(portAttachInstanceResult.getInstanceId(),
                                                             resVpcPort.getCloudEnvId());
                // 绑定完成进行更新
                resVpcPort.setDeviceOwner(portAttachInstanceResult.getDeviceOwner());
                resVpcPort.setStatus(NetworkStatus.ACTIVE);
                resVpcPort.setDevice(resVm.getId());
                BasicWebUtil.prepareUpdateParams(resVpcPort);
                resVpcPortMapper.updateByPrimaryKeySelective(resVpcPort);
                // 添加实例绑定网卡记录
                ResVmExt resVmExt = new ResVmExt();
                resVmExt.setInstanceId(resVm.getId());
                resVmExt.setResourceId(String.valueOf(resVpcPort.getId()));
                resVmExt.setType(ResVmExtEnum.NIC.getType());
                resVmExt.setExtra(resVpcPort.getFixedIp());
                resVmExtMapper.insert(resVmExt);
                Criteria example = new Criteria();
                example.put("resourceId", resVpcPort.getSubnetId());
                example.put("instanceId", resVpcPort.getDevice());
                example.put("type", ResVmExtEnum.SUBNET.getType());
                int count = resVmExtMapper.countByParams(example);
                if (count == 0) {
                    ResVmExt vmSubnetExt = new ResVmExt();
                    vmSubnetExt.setInstanceId(resVm.getId());
                    vmSubnetExt.setResourceId(resVpcPort.getSubnetId());
                    vmSubnetExt.setType(ResVmExtEnum.SUBNET.getType());
                    resVmExtMapper.insert(vmSubnetExt);
                }
                // 更新实例 ip
                String innerIp = resVm.getInnerIp();
                if (StringUtil.isNullOrEmpty(innerIp)) {
                    resVm.setInnerIp(resVpcPort.getFixedIp());
                } else {
                    innerIp = innerIp + "," + resVpcPort.getFixedIp();
                    resVm.setInnerIp(innerIp);
                }
                resVmMapper.updateByPrimaryKeySelective(resVm);

                // 将新添加的网卡关联的安全组，关联到实例上
                resVmExtService.updateSgExtInfo(resVm.getId());
            } else { //绑定失败
                resVpcPort.setStatus("DOWN");
                resVpcPortMapper.updateByPrimaryKeySelective(resVpcPort);
            }
        } catch (Exception e) {
            logger.error(Throwables.getStackTraceAsString(e));
        }
    }

    /**
     * 弹性ip解绑回调
     */
    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#floatingIpDetachResult.options.get('resFloatingIpId')", opUser = "#floatingIpDetachResult.opUser", success = "#floatingIpDetachResult.success", operate = ResourceOperateEnum.DETACH, resourceType = ResourceTypeEnum.FLOATING_IP, orgSid = "#floatingIpDetachResult.orgSid")
    @Message(refKey = "#floatingIpDetachResult.options.get('resFloatingIpId')", envId = "#floatingIpDetachResult.cloudEnvId", msgType = ServerMsgType.FLOATING_IP, opUser = "#floatingIpDetachResult.opUser", operate = OperateEnum.DETACH, success = "#floatingIpDetachResult.success", refNameKey = "#floatingIpDetachResult.floatingIp", errorMsg = "#floatingIpDetachResult.errMsg")
    public void handleMessage(
            @LogParam("floatingIpDetachResult") @MessageParam("floatingIpDetachResult") FloatingIpDetachResult floatingIpDetachResult) {
        logger.info("弹性ip解绑回调 | 回调参数 ： {}", JsonUtil.toJson(floatingIpDetachResult));
        ResFloatingIp resFloatingIp = null;
        Map<String, Object> options = floatingIpDetachResult.getOptions();
        try {
            // 解绑成功
            if (floatingIpDetachResult.isSuccess()) {
                //更新主机表
                if (options.containsKey("resVmId")) {
                    String resVmId = options.get("resVmId").toString();
                    resVmMapper.unbandingFloatingIp(resVmId);
                }

                resFloatingIp = resFloatingIpMapper.selectByPrimaryKey(
                        Long.valueOf(options.get("resFloatingIpId").toString()));

                //更新弹性IP表
                resFloatingIpMapper.unbandingFloatingIp(resFloatingIp.getId().intValue());

                // 更新负载均衡的弹性ip
                if (ResourceType.RES_SLB.equals(resFloatingIp.getInstanceType())) {
                    resLoadBalanceMapper.unbandingFloatingIp(resFloatingIp.getId());
                } else if (ResourceType.RES_VPC_PORT.equals(resFloatingIp.getInstanceType())) {
                    // 清理网卡虚拟IP和弹性IP关联关系
                    CloudEnv cloudEnv = cloudEnvMapper.selectByPrimaryKey(resFloatingIp.getEnvId());
                    if (CloudEnvType.QCLOUD.equals(cloudEnv.getCloudEnvType()) || CloudEnvType.ALIYUN.equals(
                            cloudEnv.getCloudEnvType())) {
                        networkIpMapper.unbandingFloatingIp(options.get("fixedIpId").toString());
                    } else if (CloudEnvType.HUAWEICLOUD.equals(cloudEnv.getCloudEnvType())) {
                        ResVpcPort port = resVpcPortMapper.selectByPrimaryKey(
                                Long.parseLong(options.get("fixedIpId").toString()));
                        resFloatingIp.setPortId(port.getId().toString());
                        //弹性IP中设置关联对象为网卡(虚拟IP)
                        ResVpcPortExt ext = new ResVpcPortExt();
                        ext.setPortId(port.getId().toString());
                        ext.setResourceId(resFloatingIp.getId().toString());
                        ext.setType(WebConstants.VPC_PORT_EXT_TYPE.EIP);
                        resVpcPortExtMapper.deleteByPrimaryKey(ext);
                    }
                }

            } else { //解绑失败
                logger.error(floatingIpDetachResult.getErrMsg());
                resFloatingIp = new ResFloatingIp();
                resFloatingIp.setId(Long.valueOf(options.get("resFloatingIpId").toString()));
                resFloatingIp.setStatus(NetworkStatus.ACTIVE);
                resFloatingIpMapper.updateByPrimaryKeySelective(resFloatingIp);
            }
        } catch (Exception e) {
            logger.error("弹性IP解绑回调处理失败，异常信息，{}", Throwables.getStackTraceAsString(e));
        }
    }

    /**
     * 处理ping结束
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleMessage(NetworkIpPingResult netIpPingResult) {
        logger.info("ping ip 回调 | 回调参数 ： {}", JsonUtil.toJson(netIpPingResult));
        try {
            if (netIpPingResult.isSuccess()) {
                for (String ipHost : netIpPingResult.getIpUnavailable()) {
                    NetworkIp record = NetworkIp.builder()
                                                .status(NetworkManagement.UNAVAILABLE)
                                                .description("ping主机状态为：LIVE")
                                                .updatedDt(Calendar.getInstance().getTime())
                                                .build();
                    Map<String, Object> condition = new HashMap<>();
                    condition.put("ipAddress", ipHost);
                    networkIpMapper.updateByExampleSelective(record, condition);
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
    }

    /**
     * 创建服务链回调
     */
    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#serviceChainCreateResult.options.get('chainId')", opUser = "#serviceChainCreateResult.opUser", success = "#serviceChainCreateResult.success", operate = ResourceOperateEnum.OPERATE, resourceType = ResourceTypeEnum.SERVICE_CHAIN, orgSid = "#serviceChainCreateResult.orgSid")
    @Message(refKey = "#serviceChainCreateResult.options.get('chainId')", envId = "#serviceChainCreateResult.cloudEnvId", msgType = ServerMsgType.SERVICE_CHAIN, operateAction = "#serviceChainCreateResult.options.get('operateTye')", opUser = "#serviceChainCreateResult.opUser", operate = OperateEnum.CREATE, success = "#serviceChainCreateResult.success", errorMsg = "#serviceChainCreateResult.errMsg")
    public void handleMessage(
            @LogParam("serviceChainCreateResult") @MessageParam("serviceChainCreateResult") ServiceChainCreateResult serviceChainCreateResult) {
        Map<String, Object> options = serviceChainCreateResult.getOptions();
        logger.info("{} 服务链 | 回调参数 ： {}", options.get("operateTye").toString(),
                    JsonUtil.toJson(serviceChainCreateResult));
        ServiceChainVO serviceChainVO = serviceChainCreateResult.getServiceChain();
        ResServiceChain serviceChain = new ResServiceChain();
        try {
            // 创建成功
            if (serviceChainCreateResult.isSuccess() && serviceChainVO != null) {
                List<String> uuids = Lists.newArrayList(serviceChainVO.getDestinationContextId(),
                                                        serviceChainVO.getSourceContextId());
                //设置服务链参数
                packageUpdateServiceChainParams(serviceChain, options, serviceChainVO);

                resServiceChainMapper.updateByPrimaryKeySelective(serviceChain);

                //更新源、目标特征组为已关联服务链
                ResFlowFeatureGroup featureGroup = new ResFlowFeatureGroup();
                featureGroup.setInChain(String.valueOf(Boolean.TRUE));
                Criteria criteria = new Criteria();
                criteria.put("cloudEnvId", serviceChainVO.getCloudEnvId());
                criteria.put("uuids", uuids);
                resFlowFeatureGroupMapper.updateByExampleSelective(featureGroup, criteria.getCondition());

                // 如果是修改操作,上次关联的特征组需要取消关联
                if (OperateEnum.UPDATE.getOperate().equals(options.get("operateTye").toString())) {
                    List oldUuids = (List) options.get("oldUuids");
                    oldUuids.removeIf(uuids::contains);
                    if (CollectionUtils.isEmpty(oldUuids)) {
                        return;
                    }
                    criteria.clear();
                    criteria.put("cloudEnvId", serviceChainVO.getCloudEnvId());
                    criteria.put("uuids", oldUuids);
                    featureGroup.setInChain(String.valueOf(Boolean.FALSE));
                    resFlowFeatureGroupMapper.updateByExampleSelective(featureGroup, criteria.getCondition());
                }
            } else {
                // 创建失败
                logger.error(serviceChainCreateResult.getErrMsg());
                // 修改状态
                if (OperateEnum.CREATE.getOperate().equals(options.get("operateTye").toString())) {
                    serviceChain.setId(Long.parseLong(options.get("chainId").toString()));
                    serviceChain.setStatus(NetworkStatus.CREATE_FAILURE);
                    resServiceChainMapper.updateByPrimaryKeySelective(serviceChain);
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#serviceChainContextCreateResult.options.get('featureGroupId')", opUser = "#serviceChainContextCreateResult.opUser", success = "#serviceChainContextCreateResult.success", operate = ResourceOperateEnum.OPERATE, resourceType = ResourceTypeEnum.FEATURE_GROUP, orgSid = "#serviceChainContextCreateResult.orgSid")
    @Message(refKey = "#serviceChainContextCreateResult.options.get('featureGroupId')", envId = "#serviceChainContextCreateResult.cloudEnvId", msgType = ServerMsgType.SERVICE_CHAIN_CONTEXT, operateAction = "#serviceChainContextCreateResult.options.get('operateTye')", opUser = "#serviceChainContextCreateResult.opUser", operate = OperateEnum.CREATE, success = "#serviceChainContextCreateResult.success", errorMsg = "#serviceChainContextCreateResult.errMsg")
    public void handleMessage(
            @LogParam("serviceChainContextCreateResult") @MessageParam("serviceChainContextCreateResult") ServiceChainContextCreateResult serviceChainContextCreateResult) {
        Map<String, Object> options = serviceChainContextCreateResult.getOptions();
        logger.info("[{}]流量特征组 | 回调参数 ： [{}]", options.get("operateTye").toString(),
                    JsonUtil.toJson(serviceChainContextCreateResult));

        ResFlowFeatureGroup featureGroup = new ResFlowFeatureGroup();
        ServiceChainContextVO contextVO = serviceChainContextCreateResult.getContextVO();
        try {
            // 创建成功
            if (serviceChainContextCreateResult.isSuccess() && contextVO != null) {
                // 更新服务链流量特征组
                Long featureGroupId = Long.parseLong(options.get("featureGroupId").toString());
                featureGroup.setId(featureGroupId);
                featureGroup.setName(contextVO.getName());
                featureGroup.setType(contextVO.getType());
                featureGroup.setUuid(contextVO.getId());
                featureGroup.setStatus(NetworkStatus.ACTIVE);
                resFlowFeatureGroupMapper.updateByPrimaryKeySelective(featureGroup);
                //如果是修改操作，先清除关联
                if (OperateEnum.UPDATE.getOperate().equals(options.get("operateTye").toString())) {
                    Criteria criteria = new Criteria();
                    criteria.put("featureGroupId", contextVO.getId());
                    flowFeatureGroupTargetMapper.deleteByExample(criteria);
                }
                //创建特征组关联的子网或端口
                switch (contextVO.getType()) {
                    case FeatureGroupType.SUBNET:
                        createFeatureGroupTarget(featureGroup, contextVO.getSubnets());
                        break;
                    case FeatureGroupType.PORT:
                        createFeatureGroupTarget(featureGroup, contextVO.getPorts());
                        break;
                    case FeatureGroupType.NETWORK:
                        createFeatureGroupTarget(featureGroup, contextVO.getNetworks());
                        break;
                    case FeatureGroupType.MANUAL:
                        createFeatureGroupTarget(featureGroup, contextVO.getManual());
                        break;
                    default:
                        break;
                }
            } else {
                logger.error(serviceChainContextCreateResult.getErrMsg());
                // 创建失败
                if (OperateEnum.CREATE.getOperate().equals(options.get("operateTye").toString())) {
                    featureGroup.setId(Long.parseLong(options.get("featureGroupId").toString()));
                    featureGroup.setStatus(NetworkStatus.CREATE_FAILURE);
                    resFlowFeatureGroupMapper.updateByPrimaryKeySelective(featureGroup);
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
    }

    /**
     * 新增关联子网或端口
     *
     * @param featureGroup
     * @param targetUuids
     */
    private void createFeatureGroupTarget(ResFlowFeatureGroup featureGroup, List<String> targetUuids) {
        targetUuids.forEach(uuid -> {
            FlowFeatureGroupTarget target = new FlowFeatureGroupTarget();
            target.setFeatureGroupId(featureGroup.getUuid());
            target.setTargetId(uuid);
            target.setType(featureGroup.getType());
            flowFeatureGroupTargetMapper.insertSelective(target);
        });
    }

    /**
     * 设置服务链参数
     *
     * @param serviceChain
     * @param options
     * @param serviceChainVO
     */
    private void packageUpdateServiceChainParams(ResServiceChain serviceChain, Map<String, Object> options,
                                                 ServiceChainVO serviceChainVO) {
        serviceChain.setName(StringUtils.isEmpty(serviceChainVO.getName()) ? "" : serviceChainVO.getName());
        serviceChain.setStatus(NetworkStatus.ACTIVE);
        serviceChain.setId(Long.valueOf(options.get("chainId").toString()));
        serviceChain.setUuid(serviceChainVO.getId());
        BeanUtils.copyProperties(serviceChainVO, serviceChain);
        if (!CollectionUtils.isEmpty(serviceChainVO.getServiceGraph())) {
            serviceChain.setServiceType(serviceChainVO.getServiceGraph().get(0).getServiceType());
            serviceChain.setServiceInstanceId(serviceChainVO.getServiceGraph().get(0).getServiceInstanceId());
            serviceChain.setServiceInstanceName(serviceChainVO.getServiceGraph().get(0).getServiceInstanceName());
        }
    }

}
