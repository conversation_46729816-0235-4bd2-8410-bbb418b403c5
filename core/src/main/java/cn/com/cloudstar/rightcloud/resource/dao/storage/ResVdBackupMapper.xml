<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.cloudstar.rightcloud.resource.dao.storage.ResVdBackupMapper">
    <resultMap id="BaseResultMap" type="cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResVdBackup">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="uuid" property="uuid" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="volume_uuid" property="volumeUuid" jdbcType="VARCHAR"/>
        <result column="container" property="container" jdbcType="VARCHAR"/>
        <result column="incremental" property="incremental" jdbcType="BIT"/>
        <result column="zone" property="zone" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="backup_size" property="backupSize" jdbcType="INTEGER"/>
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="object_count" property="objectCount" jdbcType="INTEGER"/>
        <result column="has_dependent" property="hasDependent" jdbcType="BIT"/>
        <result column="snapshot_id" property="snapshotId" jdbcType="VARCHAR"/>
        <result column="error_msg" property="errorMsg" jdbcType="VARCHAR"/>
        <result column="cloud_env_id" property="cloudEnvId" jdbcType="BIGINT"/>
        <result column="org_sid" property="orgSid" jdbcType="BIGINT"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="created_dt" property="createdDt" jdbcType="TIMESTAMP"/>
        <result column="updated_by" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="updated_dt" property="updatedDt" jdbcType="TIMESTAMP"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.name != null">
                and A.name = #{condition.name}
            </if>
            <if test="condition.nameLike != null">
                and A.name like concat('%', #{condition.nameLike},'%')
            </if>
            <if test="condition.uuid != null">
                and A.uuid = #{condition.uuid}
            </if>
            <if test="condition.description != null">
                and A.description = #{condition.description}
            </if>
            <if test="condition.volumeUuid != null">
                and A.volume_uuid = #{condition.volumeUuid}
            </if>
            <if test="condition.container != null">
                and A.container = #{condition.container}
            </if>
            <if test="condition.incremental != null">
                and A.incremental = #{condition.incremental}
            </if>
            <if test="condition.zone != null">
                and A.zone = #{condition.zone}
            </if>
            <if test="condition.status != null">
                and A.status = #{condition.status}
            </if>
            <if test="condition.backupSize != null">
                and A.backup_size = #{condition.backupSize}
            </if>
            <if test="condition.startTime != null">
                and A.start_time = #{condition.startTime}
            </if>
            <if test="condition.objectCount != null">
                and A.object_count = #{condition.objectCount}
            </if>
            <if test="condition.hasDependent != null">
                and A.has_dependent = #{condition.hasDependent}
            </if>
            <if test="condition.snapshotId != null">
                and A.snapshot_id = #{condition.snapshotId}
            </if>
            <if test="condition.errorMsg != null">
                and A.error_msg = #{condition.errorMsg}
            </if>
            <if test="condition.cloudEnvId != null">
                and A.cloud_env_id = #{condition.cloudEnvId}
            </if>
            <if test="condition.cloudEnvIdIn != null and condition.cloudEnvIdIn.size() > 0">
                and A.CLOUD_ENV_ID in
                <foreach collection="condition.cloudEnvIdIn" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="condition.orgSid != null">
                and A.org_sid = #{condition.orgSid}
            </if>
            <if test="condition.createdBy != null">
                and A.created_by = #{condition.createdBy}
            </if>
            <if test="condition.createdDt != null">
                and A.created_dt = #{condition.createdDt}
            </if>
            <if test="condition.updatedBy != null">
                and A.updated_by = #{condition.updatedBy}
            </if>
            <if test="condition.updatedDt != null">
                and A.updated_dt = #{condition.updatedDt}
            </if>
            <if test="condition.version != null">
                and A.version = #{condition.version}
            </if>
            <if test="condition.resVdSid != null">
                and B.res_vd_sid = #{condition.resVdSid}
            </if>
            <if test="condition.vdNameLike != null">
                and B.vd_name like concat('%', #{condition.vdNameLike},'%')
            </if>
        </trim>
    </sql>
    <sql id="Base_Column_List">
        A.id, A.name, A.uuid, A.description, A.volume_uuid, A.container, A.incremental, A.zone, A.status, A.backup_size,
        A.start_time, A.object_count, A.has_dependent, A.snapshot_id, A.error_msg, A.cloud_env_id, A.org_sid,
        A.created_by, A.created_dt, A.updated_by, A.updated_dt, A.version
    </sql>
    <select id="selectByParams" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        ,B.vd_name
        from res_vd_backup A
        LEFT JOIN res_vd B ON A.volume_uuid = B.uuid AND A.cloud_env_id = B.cloud_env_id
        and B.status != 'deleted'
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from res_vd_backup A
        where A.id = #{id}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from res_vd_backup
        where id = #{id}
    </delete>
    <delete id="deleteByParams" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        delete from res_vd_backup
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResVdBackup">
        insert into res_vd_backup (id, name, uuid, description, volume_uuid, container, incremental,
        zone, status, backup_size, start_time, object_count, has_dependent,
        snapshot_id, error_msg, cloud_env_id, org_sid, created_by, created_dt,
        updated_by, updated_dt, version)
        values (#{id}, #{name}, #{uuid}, #{description}, #{volumeUuid}, #{container}, #{incremental},
        #{zone}, #{status}, #{backupSize}, #{startTime}, #{objectCount}, #{hasDependent},
        #{snapshotId}, #{errorMsg}, #{cloudEnvId}, #{orgSid}, #{createdBy}, #{createdDt},
        #{updatedBy}, #{updatedDt}, #{version})
    </insert>
    <insert id="insertSelective" parameterType="cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResVdBackup"
        useGeneratedKeys="true" keyProperty="id">
        insert into res_vd_backup
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="uuid != null">
                uuid,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="volumeUuid != null">
                volume_uuid,
            </if>
            <if test="container != null">
                container,
            </if>
            <if test="incremental != null">
                incremental,
            </if>
            <if test="zone != null">
                zone,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="backupSize != null">
                backup_size,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="objectCount != null">
                object_count,
            </if>
            <if test="hasDependent != null">
                has_dependent,
            </if>
            <if test="snapshotId != null">
                snapshot_id,
            </if>
            <if test="errorMsg != null">
                error_msg,
            </if>
            <if test="cloudEnvId != null">
                cloud_env_id,
            </if>
            <if test="orgSid != null">
                org_sid,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="createdDt != null">
                created_dt,
            </if>
            <if test="updatedBy != null">
                updated_by,
            </if>
            <if test="updatedDt != null">
                updated_dt,
            </if>
            <if test="version != null">
                version,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="name != null">
                #{name},
            </if>
            <if test="uuid != null">
                #{uuid},
            </if>
            <if test="description != null">
                #{description},
            </if>
            <if test="volumeUuid != null">
                #{volumeUuid},
            </if>
            <if test="container != null">
                #{container},
            </if>
            <if test="incremental != null">
                #{incremental},
            </if>
            <if test="zone != null">
                #{zone},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="backupSize != null">
                #{backupSize},
            </if>
            <if test="startTime != null">
                #{startTime},
            </if>
            <if test="objectCount != null">
                #{objectCount},
            </if>
            <if test="hasDependent != null">
                #{hasDependent},
            </if>
            <if test="snapshotId != null">
                #{snapshotId},
            </if>
            <if test="errorMsg != null">
                #{errorMsg},
            </if>
            <if test="cloudEnvId != null">
                #{cloudEnvId},
            </if>
            <if test="orgSid != null">
                #{orgSid},
            </if>
            <if test="createdBy != null">
                #{createdBy},
            </if>
            <if test="createdDt != null">
                #{createdDt},
            </if>
            <if test="updatedBy != null">
                #{updatedBy},
            </if>
            <if test="updatedDt != null">
                #{updatedDt},
            </if>
            <if test="version != null">
                #{version},
            </if>
        </trim>
    </insert>
    <select id="countByParams" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultType="java.lang.Integer">
        select count(*) from res_vd_backup
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByParamsSelective" parameterType="map">
        update res_vd_backup
        <set>
            <if test="record.id != null">
                id = #{record.id},
            </if>
            <if test="record.name != null">
                name = #{record.name},
            </if>
            <if test="record.uuid != null">
                uuid = #{record.uuid},
            </if>
            <if test="record.description != null">
                description = #{record.description},
            </if>
            <if test="record.volumeUuid != null">
                volume_uuid = #{record.volumeUuid},
            </if>
            <if test="record.container != null">
                container = #{record.container},
            </if>
            <if test="record.incremental != null">
                incremental = #{record.incremental},
            </if>
            <if test="record.zone != null">
                zone = #{record.zone},
            </if>
            <if test="record.status != null">
                status = #{record.status},
            </if>
            <if test="record.backupSize != null">
                backup_size = #{record.backupSize},
            </if>
            <if test="record.startTime != null">
                start_time = #{record.startTime},
            </if>
            <if test="record.objectCount != null">
                object_count = #{record.objectCount},
            </if>
            <if test="record.hasDependent != null">
                has_dependent = #{record.hasDependent},
            </if>
            <if test="record.snapshotId != null">
                snapshot_id = #{record.snapshotId},
            </if>
            <if test="record.errorMsg != null">
                error_msg = #{record.errorMsg},
            </if>
            <if test="record.cloudEnvId != null">
                cloud_env_id = #{record.cloudEnvId},
            </if>
            <if test="record.orgSid != null">
                org_sid = #{record.orgSid},
            </if>
            <if test="record.createdBy != null">
                created_by = #{record.createdBy},
            </if>
            <if test="record.createdDt != null">
                created_dt = #{record.createdDt},
            </if>
            <if test="record.updatedBy != null">
                updated_by = #{record.updatedBy},
            </if>
            <if test="record.updatedDt != null">
                updated_dt = #{record.updatedDt},
            </if>
            <if test="record.version != null">
                version = #{record.version},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByParams" parameterType="map">
        update res_vd_backup
        set id = #{record.id},
        name = #{record.name},
        uuid = #{record.uuid},
        description = #{record.description},
        volume_uuid = #{record.volumeUuid},
        container = #{record.container},
        incremental = #{record.incremental},
        zone = #{record.zone},
        status = #{record.status},
        backup_size = #{record.backupSize},
        start_time = #{record.startTime},
        object_count = #{record.objectCount},
        has_dependent = #{record.hasDependent},
        snapshot_id = #{record.snapshotId},
        error_msg = #{record.errorMsg},
        cloud_env_id = #{record.cloudEnvId},
        org_sid = #{record.orgSid},
        created_by = #{record.createdBy},
        created_dt = #{record.createdDt},
        updated_by = #{record.updatedBy},
        updated_dt = #{record.updatedDt},
        version = #{record.version}
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResVdBackup">
        update res_vd_backup
        <set>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="uuid != null">
                uuid = #{uuid},
            </if>
            <if test="description != null">
                description = #{description},
            </if>
            <if test="volumeUuid != null">
                volume_uuid = #{volumeUuid},
            </if>
            <if test="container != null">
                container = #{container},
            </if>
            <if test="incremental != null">
                incremental = #{incremental},
            </if>
            <if test="zone != null">
                zone = #{zone},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="backupSize != null">
                backup_size = #{backupSize},
            </if>
            <if test="startTime != null">
                start_time = #{startTime},
            </if>
            <if test="objectCount != null">
                object_count = #{objectCount},
            </if>
            <if test="hasDependent != null">
                has_dependent = #{hasDependent},
            </if>
            <if test="snapshotId != null">
                snapshot_id = #{snapshotId},
            </if>
            <if test="errorMsg != null">
                error_msg = #{errorMsg},
            </if>
            <if test="cloudEnvId != null">
                cloud_env_id = #{cloudEnvId},
            </if>
            <if test="orgSid != null">
                org_sid = #{orgSid},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy},
            </if>
            <if test="createdDt != null">
                created_dt = #{createdDt},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy},
            </if>
            <if test="updatedDt != null">
                updated_dt = #{updatedDt},
            </if>
            <if test="version != null">
                version = #{version},
            </if>
        </set>
        where id = #{id}
    </update>
    <update id="updateByPrimaryKey" parameterType="cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResVdBackup">
        update res_vd_backup
        set name = #{name},
        uuid = #{uuid},
        description = #{description},
        volume_uuid = #{volumeUuid},
        container = #{container},
        incremental = #{incremental},
        zone = #{zone},
        status = #{status},
        backup_size = #{backupSize},
        start_time = #{startTime},
        object_count = #{objectCount},
        has_dependent = #{hasDependent},
        snapshot_id = #{snapshotId},
        error_msg = #{errorMsg},
        cloud_env_id = #{cloudEnvId},
        org_sid = #{orgSid},
        created_by = #{createdBy},
        created_dt = #{createdDt},
        updated_by = #{updatedBy},
        updated_dt = #{updatedDt},
        version = #{version}
        where id = #{id}
    </update>
</mapper>
