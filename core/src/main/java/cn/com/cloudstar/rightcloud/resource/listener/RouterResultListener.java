/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.listener;

import cn.com.cloudstar.rightcloud.adapter.pojo.network.Router;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.RouterAddEntryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.RouterResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.vo.RouterInterfaceVO;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResRouter;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResRouterInterface;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResRouterRoute;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.constants.res.status.NetworkStatus;
import cn.com.cloudstar.rightcloud.common.constants.res.type.CloudEnvType;
import cn.com.cloudstar.rightcloud.common.enums.resource.ResourceOperateEnum;
import cn.com.cloudstar.rightcloud.common.enums.resource.ResourceTypeEnum;
import cn.com.cloudstar.rightcloud.common.exception.BizException;
import cn.com.cloudstar.rightcloud.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.common.util.json.JsonUtil;
import cn.com.cloudstar.rightcloud.common.websocket.annonation.Message;
import cn.com.cloudstar.rightcloud.common.websocket.annonation.MessageParam;
import cn.com.cloudstar.rightcloud.common.websocket.support.OperateEnum;
import cn.com.cloudstar.rightcloud.common.websocket.support.ServerMsgType;
import cn.com.cloudstar.rightcloud.core.annotation.log.LogMethod;
import cn.com.cloudstar.rightcloud.core.annotation.log.LogParam;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicWebUtil;
import cn.com.cloudstar.rightcloud.resource.dao.network.ResRouterInterfaceMapper;
import cn.com.cloudstar.rightcloud.resource.dao.network.ResRouterMapper;
import cn.com.cloudstar.rightcloud.resource.dao.network.ResRouterRouteMapper;
import com.google.common.base.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Component
public class RouterResultListener {

    private final Logger logger = LoggerFactory.getLogger(RouterResultListener.class);

    @Autowired
    private ResRouterRouteMapper resRouterRouteMapper;

    @Autowired
    private ResRouterMapper resRouterMapper;

    @Autowired
    private ResRouterInterfaceMapper resRouterInterfaceMapper;

    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#routerAddEntryResult.options.get('id')", resourceType = ResourceTypeEnum.ROUTER_ROUTE, opUser = "#routerAddEntryResult.opUser", operate = ResourceOperateEnum.CREATE, success = "#routerAddEntryResult.success", orgSid = "#routerAddEntryResult.orgSid")
    @Message(refKey = "#routerAddEntryResult.options.get('id')", envId = "#routerAddEntryResult.cloudEnvId", msgType = ServerMsgType.ROUTER_ROUTE, opUser = "#routerAddEntryResult.opUser", operate = OperateEnum.CREATE, success = "#routerAddEntryResult.success", errorMsg = "#routerAddEntryResult.errMsg")
    public void handleMessage(
            @LogParam("routerAddEntryResult") @MessageParam("routerAddEntryResult") RouterAddEntryResult routerAddEntryResult) {
        logger.info("创建路由表条目回调 | 回调参数 ： {}", JsonUtil.toJson(routerAddEntryResult));

        try {//更新路由表
            ResRouterRoute resRouterRoute = new ResRouterRoute();
            if (routerAddEntryResult.isSuccess()) {
                resRouterRoute.setStatus(NetworkStatus.ACTIVE);
            } else {
                resRouterRoute.setStatus(NetworkStatus.CREATE_FAILURE);
            }
            if (CloudEnvType.HUAWEICLOUD.equals(routerAddEntryResult.getOptions().get("TYPE") + "")
                    && routerAddEntryResult.isSuccess()) {
                resRouterRoute.setUuid(routerAddEntryResult.getOptions().get("routeUuid") + "");
            }
            resRouterRoute.setId(Long.valueOf(routerAddEntryResult.getOptions().get("id").toString()));
            resRouterRoute.setTargetCidr(routerAddEntryResult.getDestinationCidrBlock());
            resRouterRoute.setNextPath(routerAddEntryResult.getNextHopId());
            resRouterRouteMapper.updateByPrimaryKeySelective(resRouterRoute);

        } catch (Exception e) {
            logger.error(e.getMessage());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#routerResult.options.get('id')", resourceType = ResourceTypeEnum.ROUTER, opUser = "#routerResult.opUser", operate = ResourceOperateEnum.CREATE, success = "#routerResult.success", orgSid = "#routerResult.orgSid")
    @Message(refKey = "#routerResult.options.get('id')", envId = "#routerResult.cloudEnvId", msgType = ServerMsgType.ROUTER, opUser = "#routerResult.opUser", operate = OperateEnum.CREATE, success = "#routerResult.success", refNameKey = "#routerResult.options.get('routerName')", errorMsg = "#routerResult.errMsg")
    public void handleMessage(@LogParam("routerResult") @MessageParam("routerResult") RouterResult routerResult) {
        logger.info("创建路由器回调 | 回调参数 ： {}", JsonUtil.toJson(routerResult));
        try {
            Long id = Long.valueOf(routerResult.getOptions().get("id").toString());
            if (routerResult.isSuccess()) {
                ResRouter managedResRouter = resRouterMapper.selectByPrimaryKey(id);
                if (managedResRouter == null) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1009561377));
                }
                //更新数据库
                Router router = routerResult.getRouter();

                ResRouter resRouter = new ResRouter();
                resRouter.setId(id);
                resRouter.setName(router.getName());
                resRouter.setUuid(router.getId());
                resRouter.setTenantId(router.getTenantId());
                resRouter.setExternalNetworkAddress(router.getIpAddress());
                if (router.getExternalGateway() != null) {
                    resRouter.setExternalNetworkId(router.getExternalGateway().getNetworkId());
                }
                resRouter.setStatus(NetworkStatus.ACTIVE);

                BasicWebUtil.prepareInsertParams(resRouter, routerResult.getOpUser());
                this.resRouterMapper.updateByPrimaryKeySelective(resRouter);

                if (!CollectionUtils.isEmpty(routerResult.getRouterInterfaces())) {
                    for (RouterInterfaceVO routerInterfaceVO : routerResult.getRouterInterfaces()) {
                        ResRouterInterface resRouterInterface = new ResRouterInterface();

                        resRouterInterface.setCloudEnvId(managedResRouter.getCloudEnvId());
                        resRouterInterface.setInterfaceType(routerInterfaceVO.getType());
                        resRouterInterface.setRouterUuid(routerInterfaceVO.getRouterId());
                        if (Strings.isNullOrEmpty(routerInterfaceVO.getName())) {
                            resRouterInterface.setName(routerInterfaceVO.getId());
                        } else {
                            resRouterInterface.setName(routerInterfaceVO.getName());
                        }
                        resRouterInterface.setStatus(parseStatus(routerInterfaceVO.getStatus()));
                        resRouterInterface.setUuid(routerInterfaceVO.getId());
                        resRouterInterface.setNetworkUuid(routerInterfaceVO.getNetworkId());
                        resRouterInterface.setFixedIp(routerInterfaceVO.getFixedIp());
                        resRouterInterface.setRouterId(resRouter.getId());

                        resRouterInterfaceMapper.insertSelective(resRouterInterface);
                    }
                }

                //更新routerrouter表
                if (routerResult.getOptions().get("resRouterRouterId") != null && StringUtil.isNotEmpty(
                        routerResult.getOptions().get("resRouterRouterId").toString())) {
                    Long resRouterRouterId = Long.valueOf(
                            routerResult.getOptions().get("resRouterRouterId").toString());
                    ResRouterRoute resRouterRoute = new ResRouterRoute();
                    resRouterRoute.setId(resRouterRouterId);
                    resRouterRoute.setStatus(NetworkStatus.ACTIVE);
                    resRouterRoute.setRouterUuid(resRouter.getUuid());
                    resRouterRoute.setUuid(resRouter.getUuid());
                    resRouterRouteMapper.updateByPrimaryKeySelective(resRouterRoute);
                }
            } else {
                ResRouter resRouter = new ResRouter();
                resRouter.setStatus(NetworkStatus.CREATE_FAILURE);
                resRouter.setId(id);
                this.resRouterMapper.updateByPrimaryKeySelective(resRouter);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
    }


    private String parseStatus(String status) {
        switch (status.toLowerCase()) {
            case "available":
            case "active":
            case "normal":
                return NetworkStatus.ACTIVE;
            case "pending":
            case "build":
                return NetworkStatus.CREATING;
            case "down":
                return "DOWN";
            case "stopped":
                return NetworkStatus.STOPPED;
            case "error":
                return NetworkStatus.CREATE_FAILURE;
            default:
                return "";
        }
    }
}
