/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.service.common.impl;

import cn.com.cloudstar.rightcloud.adapter.core.MQException;
import cn.com.cloudstar.rightcloud.adapter.core.MQHelper;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.SgCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.SgDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.SgUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.*;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.result.DiskUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.SgRuleCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.SgRuleDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.SgRuleUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.*;
import cn.com.cloudstar.rightcloud.basic.data.platform.CloudClientFactory;
import cn.com.cloudstar.rightcloud.basic.data.pojo.base.Base;
import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnv;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.Network;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.*;
import cn.com.cloudstar.rightcloud.basic.data.pojo.snapshot.ResSnapshot;
import cn.com.cloudstar.rightcloud.basic.data.service.res.BasicResActionLogService;
import cn.com.cloudstar.rightcloud.common.additional.ResInstResult;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.constants.res.status.ResImageStatus;
import cn.com.cloudstar.rightcloud.common.constants.res.status.ResVdStatus;
import cn.com.cloudstar.rightcloud.common.constants.res.status.ResVmStatus;
import cn.com.cloudstar.rightcloud.common.constants.res.status.SnapshotStatus;
import cn.com.cloudstar.rightcloud.common.constants.res.type.*;
import cn.com.cloudstar.rightcloud.common.constants.type.CloudDeploymentType;
import cn.com.cloudstar.rightcloud.common.constants.type.DeploymentMode;
import cn.com.cloudstar.rightcloud.common.constants.type.ReleaseMode;
import cn.com.cloudstar.rightcloud.common.constants.type.ResourceType;
import cn.com.cloudstar.rightcloud.common.enums.ReqSource;
import cn.com.cloudstar.rightcloud.common.enums.resource.ResourceOperateEnum;
import cn.com.cloudstar.rightcloud.common.enums.resource.ResourceTypeEnum;
import cn.com.cloudstar.rightcloud.common.exception.BizException;
import cn.com.cloudstar.rightcloud.common.exception.resolver.CloudErrorResolver;
import cn.com.cloudstar.rightcloud.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.common.redis.JedisUtil;
import cn.com.cloudstar.rightcloud.common.util.*;
import cn.com.cloudstar.rightcloud.common.util.json.JsonUtil;
import cn.com.cloudstar.rightcloud.common.websocket.ServerMsgPublisher;
import cn.com.cloudstar.rightcloud.common.websocket.support.ServerMsgType;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.DBInstanceType;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.DiskRelate;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResSecurityRuleModel;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResZoneExample;
import cn.com.cloudstar.rightcloud.core.pojo.models.res.ResStorageProvisionModel;
import cn.com.cloudstar.rightcloud.core.pojo.vo.res.InstanceTypeVO;
import cn.com.cloudstar.rightcloud.core.pojo.vo.res.SecurityGroupRulesVO;
import cn.com.cloudstar.rightcloud.driver.pojo.base.BaseResult;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.User;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicInfoUtil;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicWebUtil;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.*;
import cn.com.cloudstar.rightcloud.remote.api.pojo.system.app.ServiceInstTarget;
import cn.com.cloudstar.rightcloud.remote.api.system.service.maintainance.order.ServiceInstTargetRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.system.service.user.OrgRemoteService;
import cn.com.cloudstar.rightcloud.resource.dao.env.CloudEnvMapper;
import cn.com.cloudstar.rightcloud.resource.dao.host.*;
import cn.com.cloudstar.rightcloud.resource.dao.image.ResImageMapper;
import cn.com.cloudstar.rightcloud.resource.dao.network.*;
import cn.com.cloudstar.rightcloud.resource.dao.security.ResSecurityGroupMapper;
import cn.com.cloudstar.rightcloud.resource.dao.security.ResSshKeyMapper;
import cn.com.cloudstar.rightcloud.resource.dao.server.ResVmMapper;
import cn.com.cloudstar.rightcloud.resource.dao.server.ResVmTypeMapper;
import cn.com.cloudstar.rightcloud.resource.dao.snapshot.ResSnapshotMapper;
import cn.com.cloudstar.rightcloud.resource.dao.storage.ResStorageMapper;
import cn.com.cloudstar.rightcloud.resource.dao.storage.ResVdHostMapper;
import cn.com.cloudstar.rightcloud.resource.dao.storage.ResVdMapper;
import cn.com.cloudstar.rightcloud.resource.dao.storage.ResVolumeTypeMapper;
import cn.com.cloudstar.rightcloud.resource.dao.user.ResProjectMapper;
import cn.com.cloudstar.rightcloud.resource.dao.user.ResRoleMapper;
import cn.com.cloudstar.rightcloud.resource.dao.user.ResUserMapper;
import cn.com.cloudstar.rightcloud.resource.dao.user.ResUserProjectMapper;
import cn.com.cloudstar.rightcloud.resource.dao.zone.*;
import cn.com.cloudstar.rightcloud.resource.service.common.ResService;
import cn.com.cloudstar.rightcloud.resource.service.common.ResourceService;
import cn.com.cloudstar.rightcloud.resource.service.dcs.ResDcsService;
import cn.com.cloudstar.rightcloud.resource.service.env.CloudEnvService;
import cn.com.cloudstar.rightcloud.resource.service.gpu.ResGpuDeviceService;
import cn.com.cloudstar.rightcloud.resource.service.gpu.ResGpuGroupService;
import cn.com.cloudstar.rightcloud.resource.service.image.ResImageService;
import cn.com.cloudstar.rightcloud.resource.service.lb.ResLoadBalanceService;
import cn.com.cloudstar.rightcloud.resource.service.mq.ResMqService;
import cn.com.cloudstar.rightcloud.resource.service.network.NetworkService;
import cn.com.cloudstar.rightcloud.resource.service.network.ResFloatingIpService;
import cn.com.cloudstar.rightcloud.resource.service.obs.ResBucketService;
import cn.com.cloudstar.rightcloud.resource.service.rds.ResRdsService;
import cn.com.cloudstar.rightcloud.resource.service.server.ResVmService;
import cn.com.cloudstar.rightcloud.resource.service.server.VmTypeChangeService;
import cn.com.cloudstar.rightcloud.resource.service.share.ShareService;
import cn.com.cloudstar.rightcloud.resource.service.snapshot.ResSnapshotService;
import cn.com.cloudstar.rightcloud.resource.service.storage.ResVdService;
import cn.com.cloudstar.rightcloud.resource.service.storage.ResVolumeTypeService;
import cn.com.cloudstar.rightcloud.resource.service.zone.ResPoolService;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import lombok.NonNull;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ClassUtils;
import org.springframework.util.CollectionUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * The type ResourceServiceImpl. <p> Created on 2017/5/31
 *
 * <AUTHOR>
 */
@Component
public class ResourceServiceImpl implements ResourceService {

    private static final Logger logger = LoggerFactory.getLogger(ResourceServiceImpl.class);
    private static char x = 100;
    @Autowired
    private CloudEnvMapper cloudEnvMapper;

    @Autowired
    private ResImageMapper resImageMapper;

    @Autowired
    private ResHostMapper resHostMapper;

    @Autowired
    private ResHostStorageMapper resHostStorageMapper;

    @Autowired
    private ResVmMapper resVmMapper;

    @Autowired
    private ResStorageMapper resStorageMapper;

    @Autowired
    private ResVdMapper resVdMapper;

    @Autowired
    @Lazy
    private ResVmService resVmService;

    @Autowired
    private ResVsMapper resVsMapper;

    @Autowired
    private ResVcMapper resVcMapper;

    @Autowired
    private ResVsPortGroupMapper resVsPortGroupMapper;

    @Autowired
    private ResVsHostMapper resVsHostMapper;

    @Autowired
    private ResZoneMapper resZoneMapper;

    @Autowired
    private ResVpcMapper resVpcMapper;

    @Lazy
    @Autowired
    private NetworkService networkService;

    @Autowired
    private NetworkMapper networkMapper;

    @Autowired
    private ResSnapshotMapper resSnapshotMapper;

    @Autowired
    private ResFloatingIpMapper resFloatingIpMapper;

    @Autowired
    private ResSecurityGroupMapper resSecurityGroupMapper;

    @Autowired
    private ResVmTypeMapper resVmTypeMapper;

    @Autowired
    private ResVolumeTypeMapper resVolumeTypeMapper;

    @Autowired
    private ResSshKeyMapper resSshKeyMapper;

    @Autowired
    private ResPoolService resPoolService;

    @Autowired
    private ComputeResMapper computeResMapper;

    @Autowired
    private ResVolumeTypeService resVolumeTypeService;

    @Autowired
    private ResService resService;

    @Autowired
    private ResRouterMapper resRouterMapper;

    @Autowired
    private ResPoolResourceMapper resPoolResourceMapper;

    @Autowired
    private ResRouterInterfaceMapper resRouterInterfaceMapper;

    @Autowired
    private ResRouterRouteMapper resRouterRouteMapper;

    @Autowired
    private ResRdsService resRdsService;

    @Autowired
    private BasicResActionLogService basicResActionLogService;

    @Autowired
    private ResLoadBalanceService resLoadBalanceService;

    @Autowired
    private ResBucketService resBucketService;

    @Autowired
    private ResGroupMapper resGroupMapper;

    @Autowired
    private ShareService shareService;

    @Lazy
    @Autowired
    private CloudEnvService cloudEnvService;

    @Autowired
    private ResRegionMapper resRegionMapper;

    @Autowired
    private ResHostToAggregateMapper resHostToAggregateMapper;

    @Autowired
    private ResHostAggregateMapper resHostAggregateMapper;

    @Autowired
    private ResMqService resMqService;

    @Autowired
    private ResComputeStorageMapper resComputeStorageMapper;

    @Autowired
    private ResFireWallMapper resFireWallMapper;

    @Autowired
    private ResFireWallStrategyMapper resFireWallStrategyMapper;

    @Autowired
    private ResFireWallRuleMapper resFireWallRuleMapper;

    @Autowired
    private ResVpcPortSecurityGroupMapper resVpcPortSecurityGroupMapper;

    @Autowired
    private ResFireWallIPSPolicyMapper resFireWallIPSPolicyMapper;

    @Autowired
    private ResFireWallIPSSignatureMapper resFireWallIPSSignatureMapper;

    @Autowired
    private ResFireWallIPSTempalteMapper resFireWallIPSTempalteMapper;

    @Autowired
    private ResFireWallObjectsMapper resFireWallObjectsMapper;

    @Autowired
    private ResFireWallObjectGroupsMapper resFireWallObjectGroupsMapper;

    @Autowired
    private ResFireWallAntivirusPolicyMapper resFireWallAntivirusPolicyMapper;

    @Autowired
    private ResVdHostMapper resVdHostMapper;

    @Autowired
    private ResImageService resImageService;

    @Lazy
    @Autowired
    private VmTypeChangeService vmTypeChangeService;

    @Lazy
    @Autowired
    private ResSnapshotService resSnapshotService;

    @Lazy
    @Autowired
    private ResDcsService resDcsService;

    @Lazy
    @Autowired
    private ResVdService resVdService;

    @Lazy
    @Autowired
    private ResFloatingIpService resFloatingIpService;

    @Autowired
    private ResGpuGroupService resGpuGroupService;

    @Autowired
    private ResGpuDeviceService resGpuDeviceService;

    @Autowired
    private ResUserMapper resUserMapper;

    @Autowired
    private ResProjectMapper resProjectMapper;

    @Autowired
    private ResRoleMapper resRoleMapper;

    @Autowired
    private ResUserProjectMapper resUserProjectMapper;

    @DubboReference
    private ServiceInstTargetRemoteService serviceInstTargetRemoteService;

    @DubboReference
    private OrgRemoteService orgRemoteService;

    /**
     * Remove env resource boolean.
     *
     * @param envId the env id
     *
     * @return the boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeEnvResource(Long envId) {
        Criteria criteria = new Criteria(Constants.CLOUD_ENV_ID, envId);
        criteria.setIgnoreDataFilter(true);
        // 镜像
        this.resImageMapper.deleteByExample(criteria);

        // 网络
        this.networkService.deleteNetworkByEnv(envId);

        // 虚拟机相关
        List<ResVm> resVms = this.resVmMapper.selectBaseByParams(criteria);
        for (ResVm resVm : resVms) {
            this.resVmService.removeInstanceFromDB(resVm.getId(), true, true, "");
        }

        // RES系列
        // DC/ZONE
        ResZoneExample resZoneExample = new ResZoneExample();
        resZoneExample.createCriteria().andCloudEnvIdEqualTo(envId);
        this.resZoneMapper.deleteByExample(resZoneExample);
        this.resComputeStorageMapper.deleteByParams(criteria);
        // 宿主机
        Criteria topologySid = new Criteria(Constants.PARENT_TOPOLOGY_SID, envId);
        topologySid.setIgnoreDataFilter(true);
        List<ResHost> resHosts = this.resHostMapper.selectByParamsForScan(topologySid);
        resHosts.forEach(resHost -> {
            this.resHostStorageMapper.deleteByPrimaryKeyHostSid(resHost.getResHostSid());
        });
        this.resHostMapper.deleteByParams(topologySid);

        // 存储
        this.resStorageMapper.deleteByParams(topologySid);

        // 集群
        List<ResVc> resVcs = this.resVcMapper.selectByParams(criteria);
        if (!CollectionUtils.isEmpty(resVcs)) {
            List<String> resVcIds = resVcs.stream().map(ResVc::getResVcSid).collect(Collectors.toList());

            this.resGroupMapper.deleteByParams(new Criteria("clusterIds", resVcIds));
        }

        this.resVcMapper.deleteByParams(criteria);

        // 交换机
        List<ResVs> resVsList = this.resVsMapper.selectByParams(topologySid);
        resVsList.forEach(resVs -> {
            this.resVsPortGroupMapper.deleteByParams(new Criteria(Constants.RES_VS_SID, resVs.getResVsSid()));
            this.resVsHostMapper.deleteByResVsSid(resVs.getResVsSid());
        });

        // 集群
        this.resVsMapper.deleteByParams(topologySid);

        //vd
        this.resVdMapper.deleteByParams(criteria);

        // 弹性ip
        this.resFloatingIpMapper.deleteByParams(criteria);

        // 安全组
        this.resSecurityGroupMapper.deleteByParams(criteria);

        // ssh key
        this.resSshKeyMapper.deleteByParams(criteria);

        // 实例类型
        this.resVmTypeMapper.deleteByParams(criteria);

        // 存储类型
        this.resVolumeTypeMapper.deleteByParams(criteria);

        // 分区及相关资源
        this.resPoolService.deleteAvailablePartition(envId);

        // 路由器
        this.resRouterMapper.deleteByParams(criteria);

        // 路由器接口
        this.resRouterInterfaceMapper.deleteByParams(criteria);

        // 路由表
        this.resRouterRouteMapper.deleteByParams(criteria);

        // RDS实例
        List<ResRds> resRdsList = this.resRdsService.selectByParams(criteria);
        resRdsList.forEach(resRds -> this.resRdsService.deleteByPrimaryKey(resRds.getId()));

        // 快照
        this.resSnapshotMapper.deleteByParams(criteria);

        // 负载均衡
        this.resLoadBalanceService.removeResourceFromDBByEnvId(envId);

        // 对象存储
        this.resBucketService.removeResourcesByCloudEnvId(envId);

        // 弹性文件服务
        shareService.removeResourceFromDBByEnvId(envId);

        // 用户与项目的关系
        resUserProjectMapper.deleteByCloudEnvId(envId);

        // 用户
        resUserMapper.deleteByParams(criteria);

        // 项目
        resProjectMapper.deleteByParams(criteria);

        // 角色
        resRoleMapper.deleteByParams(criteria);

        // 区域
        resRegionMapper.deleteByParams(criteria);

        // 主机聚集和主机的关系
        resHostToAggregateMapper.deleteByParams(criteria);

        // 主机聚集
        resHostAggregateMapper.deleteByParams(criteria);

        // 删除dms
        resMqService.removeResourcesByCloudEnvId(envId);

        // 删除防火墙、策略、规则
        resFireWallMapper.deleteByParams(criteria);
        resFireWallStrategyMapper.deleteByParams(criteria);
        resFireWallRuleMapper.deleteByParams(criteria);
        //防火墙
        resFireWallAntivirusPolicyMapper.deleteByParams(criteria);
        resFireWallObjectGroupsMapper.deleteByParams(criteria);
        resFireWallObjectsMapper.deleteByParams(criteria);
        resFireWallIPSTempalteMapper.deleteByParams(criteria);
        resFireWallIPSSignatureMapper.deleteByParams(criteria);
        resFireWallIPSPolicyMapper.deleteByParams(criteria);

        // 删除gpu相关资源
        resGpuDeviceService.deleteByParams(criteria);
        resGpuGroupService.deleteByParams(criteria);

        return true;
    }

    @Override
    public String getAwsInstanceTypeStr() {
        InputStream is = null;
        BufferedReader bufferedReader = null;
        StringBuffer instanceTypes = new StringBuffer();
        try {
            is = ClassLoaderUtil.getResourceAsStream("/instanceTypes.json", ClassUtils.getUserClass(this));
            bufferedReader = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8));

            String line = null;
            while ((line = bufferedReader.readLine()) != null) {
                instanceTypes.append(line);
            }

        } catch (Exception e) {
            logger.error(e.getMessage());
        } finally {
            if (bufferedReader != null) {
                try {
                    bufferedReader.close();
                } catch (IOException e) {
                    logger.error(e.getMessage());
                }
            }
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    logger.error(e.getMessage());
                }
            }
        }

        return instanceTypes.toString();
    }

    @Override
    public boolean linkVmTypeToPool(List<Long> poolIds, List<String> vmTypeIds) {
        // 删除与分区和实例类型有关的分区资源
        Criteria criteria = new Criteria();
        criteria.put("resourceSids", vmTypeIds);
        criteria.put("resourceType", ResourceType.RES_VOLUME_TYPE);
        resPoolResourceMapper.deleteByParams(criteria);

        boolean result = true;
        if (!CollectionUtils.isEmpty(poolIds)) {
            List<ResPoolResource> resPoolResources = new ArrayList<>();
            vmTypeIds.forEach(vmTypeId -> poolIds.forEach(poolId -> {
                ResPoolResource resPoolResource = new ResPoolResource(poolId, vmTypeId, ResourceType.RES_VOLUME_TYPE);

                BasicWebUtil.prepareInsertParams(resPoolResource);
                resPoolResources.add(resPoolResource);
            }));

            result = resPoolResourceMapper.insertMulti(resPoolResources) > 0;
        }

        return result;
    }

    /**
     * 检测 硬盘名称 不能重复
     *
     * @param resVd
     */
    @Override
    public boolean checkVdNameUnique(@NonNull ResVd resVd) {
        Objects.requireNonNull(resVd.getCloudEnvId(), "磁盘云环境id不能为空");
        Objects.requireNonNull(resVd.getVdName(), "磁盘名称不能为空");
        Criteria criteria = new Criteria();
        criteria.put("cloudEnvId", resVd.getCloudEnvId())
                .put("vdName", resVd.getVdName())
                .put("statusNotIn",
                     Arrays.asList(ResVdStatus.DELETED, ResVdStatus.ERROR, ResVdStatus.DELETING, ResVdStatus.FAILURE));
        List<ResVd> vds = this.resVdMapper.selectByParams(criteria);
        return CollectionUtils.isEmpty(vds);
    }

    /**
     * 获取AWS实例类型
     *
     * @return aws instance types
     */
    @Override
    public List<InstanceTypeVO> getAwsInstanceTypes() {

        JSONArray families = JSONObject.parseArray(getAwsInstanceTypeStr());
        List<InstanceTypeVO> instanceTypeList = new ArrayList<>();
        if (families != null) {
            for (int i = 0; i < families.size(); i++) {
                JSONObject family = families.getJSONObject(i);
                JSONArray types = family.getJSONArray("types");
                instanceTypeList.addAll(JSONObject.parseArray(types.toJSONString(), InstanceTypeVO.class));
            }
        }

        return instanceTypeList;
    }

    /**
     * Update vm link with pool and vm type.
     *
     * @param resPool the resPool
     */
    @Override
    public void updateVmLinkWithPool(ResPool resPool) {
        CloudEnv cloudEnv = cloudEnvMapper.selectByPrimaryKey(resPool.getCloudEnvId());
        assertEnvExists(cloudEnv);

        logger.info("虚拟化环境[{}]更新主机与分区[{}]关系", cloudEnv.getCloudEnvName(), resPool.getName());
        ComputeRes computeRes = resPool.getComputeRes();
        List<String> resHostIds = new ArrayList<>();
        if (!ComputeResType.HOST.equals(computeRes.getType())) {
            List<ResHost> resHosts = resHostMapper.selectByResVcSidAndEnvId(computeRes.getClusterId(),
                                                                            resPool.getCloudEnvId().toString());
            resHostIds.addAll(resHosts.stream().map(ResHost::getResHostSid).collect(Collectors.toList()));
        } else {
            resHostIds.addAll(Arrays.asList(computeRes.getHostIds().split(",")));
        }

        if (!CollectionUtils.isEmpty(resHostIds)) {
            List<ResVm> resVms = resVmMapper.selectBaseByParams(new Criteria("allocateResHostSidIn", resHostIds));

            List<String> hostIds = resVms.stream().map(ResVm::getId).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(hostIds)) {
                resVmMapper.mutiUpdateCloudHostZone(hostIds, resPool.getName());
            }
        }
    }

    /**
     * Update vd link with vm type.
     *
     * @param envId the env id
     */
    @Override
    public void updateVdLinkWithVmType(Long envId) {
        CloudEnv cloudEnv = cloudEnvMapper.selectByPrimaryKey(envId);

        logger.info("虚拟化环境[{}]更新硬盘与存储类型的关系", cloudEnv.getCloudEnvName());

        Map<String, Long> vdAllocStorageTypeMap = getVdAllocStorageTypeMap(envId);

        Criteria criteria = new Criteria();
        criteria.put(Constants.CLOUD_ENV_ID, envId);
        criteria.put("statusNotEquals", ResVdStatus.DELETED);
        List<ResVd> cloneVdList = this.resVdMapper.selectByParams(criteria);

        if (!CollectionUtils.isEmpty(cloneVdList)) {
            cloneVdList.forEach(cloneVd -> {
                if (!vdAllocStorageTypeMap.isEmpty() && vdAllocStorageTypeMap.containsKey(
                        cloneVd.getAllocateResStorageSid())) {
                    cloneVd.setVolumeTypeId(vdAllocStorageTypeMap.get(cloneVd.getAllocateResStorageSid()));
                } else {
                    cloneVd.setVolumeTypeId(null);
                }
            });

            cloneVdList.forEach(cloneVd -> {
                resVdMapper.updateVolumeTypeId(cloneVd);
            });
        }

    }

    /**
     * Delete sg boolean.
     *
     * @param id the id
     * @return the boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteSG(String id) {
        String opUser = "";
        User authUser = BasicInfoUtil.getAuthUser();
        if (Objects.nonNull(authUser) && !StringUtil.isNullOrEmpty(authUser.getAccount())) {
            opUser = BasicInfoUtil.getAuthUser().getAccount();
        }

        //查询出对应的安全组
        ResSecurityGroup resSecurityGroup = resSecurityGroupMapper.selectByPrimaryKey(Long.valueOf(id));
        AssertUtil.requireNonBlank(resSecurityGroup, "安全组不存在或已被删除");

        this.checkDelete(resSecurityGroup.getId());

        SgDelete sgDelete = CloudClientFactory.buildMQBean(resSecurityGroup.getCloudEnvId(), SgDelete.class);
        sgDelete.setRegion(resSecurityGroup.getRegion());
        sgDelete.setSecurityGroupId(resSecurityGroup.getUuid());

        SgDeleteResult sgDeleteResult = (SgDeleteResult) sendToMQRPC(sgDelete);
        if (sgDeleteResult == null) {
            return false;
        } else if (!sgDeleteResult.isSuccess()) {
            if (CloudEnvType.OPEN_STACK.equals(sgDelete.getProviderType()) || CloudEnvType.CLOUDOS.equals(
                    sgDelete.getProviderType())) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1309982473) + sgDeleteResult.getErrMsg());
            }

            basicResActionLogService.insertIntoActionLog(opUser, ResourceTypeEnum.SECURITY_GROUP,
                                                         ResourceOperateEnum.DELETE, Boolean.FALSE);

            throw new BizException(CloudErrorResolver.getErrorMsg(sgDeleteResult.getErrMsg()));
        } else {
            basicResActionLogService.insertIntoActionLog(id, opUser, ResourceTypeEnum.SECURITY_GROUP,
                                                         ResourceOperateEnum.DELETE, Boolean.TRUE);

            resSecurityGroupMapper.deleteByPrimaryKey(Long.valueOf(id));
            return true;
        }
    }

    private void checkDelete(Long id) {
        Criteria criteria = new Criteria("securityGroupId", id).put("ignoreRecycle", true)
                                                               .put("statusNotEquals", ResVmStatus.DELETED);

        // 检查安全组是否被使用，不能有权限控制， 和许可证验证相同
        int count = resVmMapper.countLicenseNormalHost(criteria);
        if (count > 0) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2140780311));
        }

        int sgCount = resVpcPortSecurityGroupMapper.countByParams(new Criteria("resSecurityGroupId", id));
        if (sgCount > 0) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1726601633));
        }
    }

    /**
     * 发送MQ消息
     *
     * @param base the vm create
     * @return the res inst result
     */
    private Object sendToMQRPC(Base base) {
        try {
            return MQHelper.rpc(base);
        } catch (Exception e) {
            return new Object();
        }
    }

    /**
     * Update sg boolean.
     *
     * @param resSecurityGroup the res security group
     * @return the boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSG(ResSecurityGroup resSecurityGroup) {
        //获取本地得安全组
        ResSecurityGroup resSecurityGroupLocal = resSecurityGroupMapper.selectByPrimaryKey(resSecurityGroup.getId());
        SgUpdate sgUpdate = null;
        if (resSecurityGroupLocal != null) {
            sgUpdate = CloudClientFactory.buildMQBean(resSecurityGroupLocal.getCloudEnvId(), SgUpdate.class);
            if (CloudEnvType.OPEN_STACK.equals(sgUpdate.getProviderType()) || CloudEnvType.CLOUDOS.equals(
                    sgUpdate.getProviderType())) {

                sgUpdate.setResId(resSecurityGroupLocal.getUuid());
                sgUpdate.setRegion(resSecurityGroupLocal.getRegion());
                sgUpdate.setName(resSecurityGroup.getName());
                sgUpdate.setDescription(resSecurityGroup.getDescription());
            } else if (CloudEnvType.ALIYUN.equals(sgUpdate.getProviderType()) || CloudEnvType.QCLOUD.equals(
                    sgUpdate.getProviderType())) {
                sgUpdate.setResId(resSecurityGroupLocal.getUuid());
                sgUpdate.setName(resSecurityGroup.getName());
                sgUpdate.setRegion(resSecurityGroupLocal.getRegion());
                sgUpdate.setDescription(resSecurityGroup.getDescription());
            } else if (CloudEnvType.HUAWEICLOUD.equals(sgUpdate.getProviderType())) {
                sgUpdate.setRegion(resSecurityGroup.getRegion());
                sgUpdate.setResId(resSecurityGroupLocal.getUuid());
                sgUpdate.setRegion(resSecurityGroupLocal.getRegion());
                sgUpdate.setName(resSecurityGroup.getName());
                sgUpdate.setDescription(resSecurityGroup.getDescription());
            } else if (CloudEnvType.KSYUN.equals(sgUpdate.getProviderType()) || CloudEnvType.KING_STACK.equals(
                    sgUpdate.getProviderType())) {
                sgUpdate.setResId(resSecurityGroupLocal.getUuid());
                sgUpdate.setName(resSecurityGroup.getName());
                sgUpdate.setDescription(resSecurityGroup.getDescription());
            }
            SgUpdateResult sgUpdateResult = (SgUpdateResult) sendToMQRPC(sgUpdate);
            if (sgUpdateResult.isSuccess()) {
                int actionLogId = basicResActionLogService.insertIntoActionLog(resSecurityGroup.getId().toString(),
                                                                               resSecurityGroup.getUpdatedBy(),
                                                                               ResourceTypeEnum.SECURITY_GROUP,
                                                                               ResourceOperateEnum.MODIFY,
                                                                               Boolean.TRUE);

                resSecurityGroupMapper.updateByPrimaryKeySelective(resSecurityGroup);

                basicResActionLogService.updateActionLog(actionLogId, ResourceTypeEnum.SECURITY_GROUP,
                                                         resSecurityGroup.getId().toString(),
                                                         ResourceOperateEnum.MODIFY);
                return true;
            } else {
                if (CloudEnvType.OPEN_STACK.equals(sgUpdate.getProviderType())) {
                    return false;
                }

                basicResActionLogService.insertIntoActionLog(resSecurityGroup.getId().toString(),
                                                             resSecurityGroup.getUpdatedBy(),
                                                             ResourceTypeEnum.SECURITY_GROUP,
                                                             ResourceOperateEnum.MODIFY, Boolean.FALSE);
                throw new BizException(CloudErrorResolver.getErrorMsg(sgUpdateResult.getErrMsg()));
            }
        } else {
            return false;
        }
    }

    /**
     * Clone sg boolean.
     *
     * @param resSecurityGroup the res security group
     * @return the boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cloneSG(ResSecurityGroup resSecurityGroup) {
        ResSecurityGroup resSecurityGroupLocal = resSecurityGroupMapper.selectByPrimaryKey(resSecurityGroup.getId());
        CloudEnv cloudEnv = cloudEnvMapper.selectByPrimaryKey(resSecurityGroup.getCloudEnvId());
        if (Objects.isNull(cloudEnv)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_832756465));
        }
        resSecurityGroup.setId(null);
        resSecurityGroup.setRegion(cloudEnv.getRegion());
        resSecurityGroup.setVpc(resSecurityGroupLocal.getVpc());
        if (CloudEnvType.ESCLOUD.equals(cloudEnv.getCloudEnvType())) {
            resSecurityGroup.setName(resSecurityGroup.getName() + "-CLONE");
        }
        ResSecurityGroup sg = this.createSG(resSecurityGroup);
        //如果克隆安全组成功，那么接下来就克隆规则
        if (sg.getId() != null) {
            //得到源安全组的规则
            List<SecurityGroupRules> securityGroupRulesList = JSONArray.parseArray(
                    resSecurityGroupLocal.getSecurityGroupRules(), SecurityGroupRules.class);
            List<SecurityGroupRules> tempList = new ArrayList<>();
            if (securityGroupRulesList != null && !securityGroupRulesList.isEmpty()) {
                if (CloudEnvType.HUAWEICLOUD.equals(cloudEnv.getCloudEnvType())) {
                    for (int i = 0; i < securityGroupRulesList.size(); i++) {
                        if (EtherType.IPV4.equalsIgnoreCase(securityGroupRulesList.get(i).getEthertype())
                                && StringUtil.isNullOrEmpty(securityGroupRulesList.get(i).getProtocol())) {
                            continue;
                        }
                        if (EtherType.IPV6.equalsIgnoreCase(securityGroupRulesList.get(i).getEthertype())
                                && StringUtil.isNullOrEmpty(securityGroupRulesList.get(i).getProtocol())) {
                            continue;
                        }
                        tempList.add(securityGroupRulesList.get(i));
                    }
                    securityGroupRulesList = tempList;
                }
                if (CloudEnvType.OPEN_STACK.equals(cloudEnv.getCloudEnvType()) || CloudEnvType.OPEN_STACK_ADMIN.equals(
                        cloudEnv.getCloudEnvType())) {
                    securityGroupRulesList.removeIf(
                            // 协议任何 端口任何
                            rule -> filterSecurityGroupRules(rule));
                } else if (CloudEnvType.KSYUN.equals(cloudEnv.getCloudEnvType()) || CloudEnvType.KING_STACK.equals(
                        cloudEnv.getCloudEnvType())) {
                    // 存在的安全组要删除
                    List<SecurityGroupRules> existSecurityGroupRules = JSONArray.parseArray(sg.getSecurityGroupRules(),
                                                                                            SecurityGroupRules.class);
                    for (SecurityGroupRules securityGroupRules : securityGroupRulesList) {
                        boolean isCloneRule = true;
                        for (SecurityGroupRules existRule : existSecurityGroupRules) {
                            if (StringUtil.equalsIgnoreCase(securityGroupRules.getProtocol(), existRule.getProtocol())
                                    && StringUtil.equalsIgnoreCase(securityGroupRules.getRemoteIpPrefix(),
                                                                   existRule.getRemoteIpPrefix())
                                    && StringUtil.equalsIgnoreCase(securityGroupRules.getDirection(),
                                                                   existRule.getDirection())) {
                                isCloneRule = false;
                                break;
                            }
                        }
                        if (isCloneRule) {
                            tempList.add(securityGroupRules);
                        }
                    }
                    securityGroupRulesList = tempList;
                }
                securityGroupRulesList.forEach(rule -> {
                    ResSecurityRuleModel resSecurityRuleModel = new ResSecurityRuleModel();
                    resSecurityRuleModel.setPriority(rule.getPriority());
                    resSecurityRuleModel.setAuthorizationStrategy(rule.getStrategy());
                    resSecurityRuleModel.setDirection(rule.getDirection());
                    resSecurityRuleModel.setDescription(rule.getDescription());
                    resSecurityRuleModel.setIpProtocol(rule.getProtocol());
                    resSecurityRuleModel.setFromPorts(rule.getPortRangeMin());
                    resSecurityRuleModel.setToPorts(rule.getPortRangeMax());
                    resSecurityRuleModel.setSourceGroupId(rule.getRemoteGroupId());
                    resSecurityRuleModel.setEthertype(rule.getEthertype());
                    resSecurityRuleModel.setAuthorizationObj(rule.getRemoteIpPrefix());
                    resSecurityRuleModel.setId(resSecurityGroup.getId().toString());
                    resSecurityRuleModel.setAuthorizationType(rule.getAuthObj());
                    this.createSGRule(resSecurityRuleModel);
                });

            }

            basicResActionLogService.insertIntoActionLog(sg.getId().toString(), resSecurityGroup.getCreatedBy(),
                                                         ResourceTypeEnum.SECURITY_GROUP, ResourceOperateEnum.CLONE,
                                                         Boolean.TRUE);

            return true;
        } else {
            basicResActionLogService.insertIntoActionLog(resSecurityGroup.getCreatedBy(),
                                                         ResourceTypeEnum.SECURITY_GROUP, ResourceOperateEnum.CLONE,
                                                         Boolean.FALSE);

            return false;
        }
    }

    private boolean filterSecurityGroupRules(SecurityGroupRules rule) {
        if (!allFieldIsNull(rule, "protocol", "portRangeMax", "portRangeMin")) {
            return false;
        }
        if (!"egress".equals(rule.getDirection())) {
            return false;
        }
        if (!"地址段访问".equals(rule.getAuthObj())) {
            return false;
        }
        return  isAllowedIPv4Range(rule) || isAllowedIPv6Range(rule);
    }

    private static boolean isAllowedIPv4Range(SecurityGroupRules rule) {
        return "IPv4".equals(rule.getEthertype()) && "0.0.0.0/0".equals(rule.getRemoteIpPrefix());
    }

    private boolean isAllowedIPv6Range(SecurityGroupRules rule) {
        return "IPv6".equals(rule.getEthertype()) && "::/0".equals(rule.getRemoteIpPrefix());
    }

    private boolean allFieldIsNull(Object obj, String... fields) {
        Boolean flag = null;
        for (String field : fields) {
            if (Objects.isNull(flag)) {
                flag = Objects.isNull(ReflectUtil.getFieldValue(obj, field));
            } else {
                flag = flag && Objects.isNull(ReflectUtil.getFieldValue(obj, field));
            }
        }
        return flag;
    }

    /**
     * Create sg res security group.
     *
     * @param resSecurityGroup the res security group
     * @return the res security group
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResSecurityGroup createSG(ResSecurityGroup resSecurityGroup) {
        this.cloudEnvService.checkCloudEnvNormal(resSecurityGroup.getCloudEnvId(), true);
        SgCreate sgCreate = CloudClientFactory.buildMQBean(resSecurityGroup.getCloudEnvId(), SgCreate.class);

        // 根据envType创建对应的sgCreate对象
        if (CloudEnvType.OPEN_STACK.equals(sgCreate.getProviderType()) || CloudEnvType.CLOUDOS.equals(
                sgCreate.getProviderType())) {
            sgCreate = openStackSgCreateMQBean(sgCreate, resSecurityGroup);
        } else if (CloudEnvType.ALIYUN.equals(sgCreate.getProviderType())) {
            sgCreate = aliyunSgCreateMQBean(sgCreate, resSecurityGroup);
        } else if (CloudEnvType.HUAWEICLOUD.equals(sgCreate.getProviderType())) {
            sgCreate = huaweiCloudSgCreateMQBean(sgCreate, resSecurityGroup);
        } else if (CloudEnvType.QCLOUD.equals(sgCreate.getProviderType())) {
            sgCreate = qCloudSgCreateMQBean(sgCreate, resSecurityGroup);
        } else if (CloudEnvType.AWS.equals(sgCreate.getProviderType())) {
            sgCreate = awsSgCreateMQBean(sgCreate, resSecurityGroup);
        } else if (CloudEnvType.KSYUN.equals(sgCreate.getProviderType()) || CloudEnvType.KING_STACK.equals(
                sgCreate.getProviderType())) {
            sgCreate = ksyunSgCreateMQBean(sgCreate, resSecurityGroup);
        }

        // 发送创建安全组的同步MQ消息
        SgCreateResult sgCreateResult = (SgCreateResult) sendToMQRPC(sgCreate);
        if (sgCreateResult == null) {
            return new ResSecurityGroup();
        } else if (!sgCreateResult.isSuccess()) {
            basicResActionLogService.insertIntoActionLog(resSecurityGroup.getCreatedBy(),
                                                         ResourceTypeEnum.SECURITY_GROUP, ResourceOperateEnum.CREATE,
                                                         Boolean.FALSE);

            throw new BizException(CloudErrorResolver.getErrorMsg(sgCreateResult.getErrMsg()));
        } else {
            resSecurityGroup.setRegion(sgCreate.getRegion());
            resSecurityGroup.setUuid(sgCreateResult.getId());
            resSecurityGroup.setCloudEnvProjectId(sgCreateResult.getCloudEnvProjectId());
            if (CloudEnvType.OPEN_STACK.equals(sgCreate.getProviderType()) || CloudEnvType.CLOUDOS.equals(
                    sgCreate.getProviderType()) || CloudEnvType.AWS.equals(sgCreate.getProviderType())) {
                List<SecurityGroupRules> securityGroupRules = sgCreateResult.getSecurityGroupRules();
                securityGroupRules.forEach(s -> s.setCreateDt(new Date()));
                resSecurityGroup.setSecurityGroupRules(JSON.toJSONString(securityGroupRules));
            } else if (CloudEnvType.HUAWEICLOUD.equals(sgCreate.getProviderType())) {
                List<SecurityGroupRules> securityGroupRules = sgCreateResult.getSecurityGroupRules();
                securityGroupRules.forEach(s -> {
                    s.setCreateDt(new Date());
                    s.setPortRangeMax(s.getPortRangeMin());
                    s.setAuthObj("地址段访问");
                    s.setRemoteIpPrefix("0.0.0.0/0");
                });
                resSecurityGroup.setSecurityGroupRules(JSON.toJSONString(securityGroupRules));

            } else if (CloudEnvType.KSYUN.equals(sgCreate.getProviderType()) || CloudEnvType.KING_STACK.equals(
                    sgCreate.getProviderType())) {
                List<SecurityGroupRules> securityGroupRules = sgCreateResult.getSecurityGroupRules();
                securityGroupRules.forEach(s -> {
                    s.setCreateDt(new Date());
                });
                resSecurityGroup.setSecurityGroupRules(JSON.toJSONString(securityGroupRules));

            }
            resSecurityGroupMapper.insertSelective(resSecurityGroup);

            basicResActionLogService.insertIntoActionLog(resSecurityGroup.getId().toString(),
                                                         resSecurityGroup.getCreatedBy(),
                                                         ResourceTypeEnum.SECURITY_GROUP, ResourceOperateEnum.CREATE,
                                                         Boolean.TRUE);

            return resSecurityGroup;
        }
    }

    private void checkSecurityGroupRuleExist(ResSecurityGroup resSecurityGroup,
                                             ResSecurityRuleModel resSecurityRuleModel) {
        // 检查规则是否已存在
        List<SecurityGroupRules> securityGroupRulesList = JSONArray.parseArray(resSecurityGroup.getSecurityGroupRules(),
                                                                               SecurityGroupRules.class);
        if (securityGroupRulesList != null && !securityGroupRulesList.isEmpty()) {

            for (int i = 0; i < securityGroupRulesList.size(); i++) {
                if (!StringUtil.isNullOrEmpty(securityGroupRulesList.get(i).getProtocol())) {
                    if (securityGroupRulesList.get(i)
                                              .getProtocol()
                                              .equalsIgnoreCase(resSecurityRuleModel.getIpProtocol())
                            && Strings.nullToEmpty(securityGroupRulesList.get(i).getPortRangeMin())
                                      .equalsIgnoreCase(Strings.nullToEmpty(resSecurityRuleModel.getFromPorts()))
                            && Strings.nullToEmpty(securityGroupRulesList.get(i).getPortRangeMax())
                                      .equalsIgnoreCase(Strings.nullToEmpty(resSecurityRuleModel.getToPorts()))
                            && securityGroupRulesList.get(i)
                                                     .getDirection()
                                                     .equalsIgnoreCase(resSecurityRuleModel.getDirection())) {
                        CloudEnv cloudEnv = cloudEnvService.assertEnvNonNull(resSecurityGroup.getCloudEnvId());
                        if (CloudEnvType.KSYUN.equals(cloudEnv.getCloudEnvType()) || CloudEnvType.KING_STACK.equals(
                                cloudEnv.getCloudEnvType())) {

                        } else if (CloudEnvType.ALIYUN.equals(cloudEnv.getCloudEnvType())) {
                            if (securityGroupRulesList.get(i)
                                                      .getStrategy()
                                                      .equalsIgnoreCase(resSecurityRuleModel.getAuthorizationStrategy())
                                    && securityGroupRulesList.get(i)
                                                             .getPriority()
                                                             .equalsIgnoreCase(resSecurityRuleModel.getPriority())
                                    && securityGroupRulesList.get(i)
                                                             .getAuthObj()
                                                             .equalsIgnoreCase(
                                                                     resSecurityRuleModel.getAuthorizationType())
                                    && securityGroupRulesList.get(i)
                                                             .getRemoteIpPrefix()
                                                             .equalsIgnoreCase(
                                                                     resSecurityRuleModel.getAuthorizationObj())) {
                                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1770291058));
                            }
                        } else {
                            if (StringUtil.isNotBlank(resSecurityRuleModel.getSourceGroupId())) {
                                if (resSecurityRuleModel.getSourceGroupId()
                                                        .equalsIgnoreCase(
                                                                securityGroupRulesList.get(i).getRemoteGroupId())) {
                                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1770291058));
                                }
                            }
                            if (StringUtil.isNotBlank(resSecurityRuleModel.getAuthorizationObj())) {
                                if (resSecurityRuleModel.getAuthorizationObj()
                                                        .equalsIgnoreCase(
                                                                securityGroupRulesList.get(i).getRemoteIpPrefix())) {
                                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1770291058));
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 创建安全组规则
     *
     * @param resSecurityRuleModel the res security rule model
     * @return boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createSGRule(ResSecurityRuleModel resSecurityRuleModel) {
        String opUser = "";
        User authUser = BasicInfoUtil.getAuthUser();
        if (Objects.nonNull(authUser) && !StringUtil.isNullOrEmpty(authUser.getAccount())) {
            opUser = BasicInfoUtil.getAuthUser().getAccount();
        }

        ResSecurityGroup resSecurityGroup = resSecurityGroupMapper.selectByPrimaryKey(
                Long.valueOf(resSecurityRuleModel.getId()));

        // 检查安全组规则是否已经存在
        checkSecurityGroupRuleExist(resSecurityGroup, resSecurityRuleModel);

        SgRuleCreate sgRuleCreate = CloudClientFactory.buildMQBean(resSecurityGroup.getCloudEnvId(),
                                                                   SgRuleCreate.class);

        // 根据envType创建对应的sgRuleCreate对象
        if (CloudEnvType.OPEN_STACK.equals(sgRuleCreate.getProviderType()) || CloudEnvType.CLOUDOS.equals(
                sgRuleCreate.getProviderType())) {
            sgRuleCreate.setDirection(resSecurityRuleModel.getDirection());
            sgRuleCreate.setProtocol(resSecurityRuleModel.getIpProtocol());
            sgRuleCreate.setRemoteIpPrefix(resSecurityRuleModel.getAuthorizationObj());
            sgRuleCreate.setEthertype(resSecurityRuleModel.getEthertype());
            sgRuleCreate.setAuthObjId(resSecurityRuleModel.getSourceGroupId());
            sgRuleCreate.setDescription(resSecurityRuleModel.getDescription());
            sgRuleCreate.setPortRangMin(resSecurityRuleModel.getFromPorts());
            sgRuleCreate.setPortRangeMax(resSecurityRuleModel.getToPorts());

            sgRuleCreate.setSecurityGroupId(resSecurityGroup.getUuid());
            sgRuleCreate.setRegion(resSecurityGroup.getRegion());
        } else if (CloudEnvType.ALIYUN.equals(sgRuleCreate.getProviderType()) || CloudEnvType.QCLOUD.equals(
                sgRuleCreate.getProviderType()) || CloudEnvType.AWS.equals(sgRuleCreate.getProviderType())) {
            sgRuleCreate.setDirection(resSecurityRuleModel.getDirection());
            sgRuleCreate.setPortRangMin(resSecurityRuleModel.getFromPorts());
            sgRuleCreate.setPortRangeMax(resSecurityRuleModel.getToPorts());
            sgRuleCreate.setAddress(resSecurityRuleModel.getSourceGroupId());
            sgRuleCreate.setProtocol(resSecurityRuleModel.getIpProtocol());
            sgRuleCreate.setEthertype(resSecurityRuleModel.getEthertype());
            sgRuleCreate.setPriority(resSecurityRuleModel.getPriority());
            if (Objects.equals("安全组访问", resSecurityRuleModel.getAuthorizationType()) || !StringUtil.isNullOrEmpty(
                    resSecurityRuleModel.getSourceGroupId())) {
                sgRuleCreate.setAuthObjId(resSecurityRuleModel.getSourceGroupId());
            } else {
                if (!StringUtil.isNullOrEmpty(resSecurityRuleModel.getAuthorizationObj())
                        && resSecurityRuleModel.getAuthorizationObj().contains("null")) {
                    String[] ips = resSecurityRuleModel.getAuthorizationObj().split("/");
                    sgRuleCreate.setRemoteIpPrefix(ips[0]);
                } else {
                    sgRuleCreate.setRemoteIpPrefix(resSecurityRuleModel.getAuthorizationObj());
                }
            }
            sgRuleCreate.setPolicy(resSecurityRuleModel.getAuthorizationStrategy());
            sgRuleCreate.setDescription(resSecurityRuleModel.getDescription());

            sgRuleCreate.setRegion(resSecurityGroup.getRegion());
            sgRuleCreate.setSecurityGroupId(resSecurityGroup.getUuid());
        } else if (CloudEnvType.HUAWEICLOUD.equals(sgRuleCreate.getProviderType())) {
            sgRuleCreate.setDirection(resSecurityRuleModel.getDirection());
            sgRuleCreate.setPortRangMin(resSecurityRuleModel.getFromPorts());
            sgRuleCreate.setPortRangeMax(resSecurityRuleModel.getToPorts());
            sgRuleCreate.setProtocol(resSecurityRuleModel.getIpProtocol());
            sgRuleCreate.setRemoteIpPrefix(resSecurityRuleModel.getAuthorizationObj());
            sgRuleCreate.setEthertype(resSecurityRuleModel.getEthertype());
            sgRuleCreate.setAuthObjId(resSecurityRuleModel.getSourceGroupId());
            sgRuleCreate.setDescription(resSecurityRuleModel.getDescription());
            sgRuleCreate.setSecurityGroupId(resSecurityGroup.getUuid());
        } else if (CloudEnvType.KSYUN.equals(sgRuleCreate.getProviderType()) || CloudEnvType.KING_STACK.equals(
                sgRuleCreate.getProviderType())) {
            sgRuleCreate.setDirection(resSecurityRuleModel.getDirection());
            sgRuleCreate.setPortRangMin(resSecurityRuleModel.getFromPorts());
            sgRuleCreate.setPortRangeMax(resSecurityRuleModel.getToPorts());
            sgRuleCreate.setAddress(resSecurityRuleModel.getSourceGroupId());
            sgRuleCreate.setProtocol(resSecurityRuleModel.getIpProtocol());
            sgRuleCreate.setAuthObjId(resSecurityRuleModel.getSourceGroupId());
            sgRuleCreate.setEthertype(resSecurityRuleModel.getEthertype());
            sgRuleCreate.setPriority(resSecurityRuleModel.getPriority());
            sgRuleCreate.setRemoteIpPrefix(resSecurityRuleModel.getAuthorizationObj());
            sgRuleCreate.setPolicy(resSecurityRuleModel.getAuthorizationStrategy());
            sgRuleCreate.setDescription(resSecurityRuleModel.getDescription());
            sgRuleCreate.setRegion(resSecurityGroup.getRegion());
            sgRuleCreate.setSecurityGroupId(resSecurityGroup.getUuid());
        }
        //发送mq
        SgRuleCreateResult sgRuleCreateResult = (SgRuleCreateResult) sendToMQRPC(sgRuleCreate);
        if (!sgRuleCreateResult.isSuccess()) {
            basicResActionLogService.insertIntoActionLog(resSecurityRuleModel.getId(), opUser,
                                                         ResourceTypeEnum.SECURITY_GROUP, ResourceOperateEnum.MODIFY,
                                                         Boolean.FALSE);

            throw new BizException(CloudErrorResolver.getErrorMsg(sgRuleCreateResult.getErrMsg()));
        }

        Criteria criteria = new Criteria();
        criteria.put("uuid", resSecurityRuleModel.getSourceGroupId());
        criteria.put("cloudEnvId", resSecurityGroup.getCloudEnvId());
        List<ResSecurityGroup> resSecurityGroups = this.resSecurityGroupMapper.selectByParams(criteria);
        Optional<ResSecurityGroup> firstSg = resSecurityGroups.stream().findFirst();
        List<SecurityGroupRules> securityGroupRules;
        //如果之前就有规则
        if (StringUtils.isNoneBlank(resSecurityGroup.getSecurityGroupRules())) {
            //取得数据库中存放的json并将其转化为List对象
            securityGroupRules = JsonUtil.fromJson(resSecurityGroup.getSecurityGroupRules(),
                                                   new TypeReference<List<SecurityGroupRules>>() {
                                                   });
            //如果没有安全组规则，那么就新建一条规则然后再插入
        } else {
            securityGroupRules = new ArrayList<>();
        }

        //创建新的安全组规则
        SecurityGroupRules rule = new SecurityGroupRules();
        rule.setDirection(resSecurityRuleModel.getDirection());
        rule.setCreateDt(new Date());
        rule.setPortRangeMax(resSecurityRuleModel.getToPorts());
        rule.setPortRangeMin(resSecurityRuleModel.getFromPorts());
        rule.setStrategy(resSecurityRuleModel.getAuthorizationStrategy());
        rule.setSecurityGroupId(resSecurityGroup.getUuid());
        rule.setId(sgRuleCreateResult.getId());
        rule.setEthertype(resSecurityRuleModel.getEthertype());
        if (!StringUtil.isNullOrEmpty(resSecurityRuleModel.getAuthorizationObj())
                && resSecurityRuleModel.getAuthorizationObj().contains("null")) {
            String[] ips = resSecurityRuleModel.getAuthorizationObj().split("/");
            rule.setRemoteIpPrefix(ips[0]);
        } else {
            rule.setRemoteIpPrefix(resSecurityRuleModel.getAuthorizationObj());
        }
        rule.setAuthObj(resSecurityRuleModel.getAuthorizationType());
        if (firstSg.isPresent()) {
            ResSecurityGroup sg = firstSg.get();
            rule.setRemoteGroupId(sg.getUuid());
            rule.setRemoteGroupName(sg.getName());
        }
        rule.setProtocol(resSecurityRuleModel.getIpProtocol().toLowerCase());
        rule.setDescription(resSecurityRuleModel.getDescription());
        rule.setPriority(resSecurityRuleModel.getPriority());
        //将规则转化成json字符串之后再存到数据库
        securityGroupRules.add(rule);
        resSecurityGroup.setSecurityGroupRules(JSON.toJSONString(securityGroupRules));
        resSecurityGroupMapper.updateByPrimaryKeySelective(resSecurityGroup);

        basicResActionLogService.insertIntoActionLog(resSecurityRuleModel.getId(), opUser,
                                                     ResourceTypeEnum.SECURITY_GROUP, ResourceOperateEnum.MODIFY,
                                                     Boolean.TRUE);

        return true;
    }

    /**
     * Open stack sg create mq bean sg create.
     *
     * @param resSecurityGroup the res security group
     * @return the sg create
     */
    private SgCreate openStackSgCreateMQBean(SgCreate sgCreate, ResSecurityGroup resSecurityGroup) {
        sgCreate.setName(resSecurityGroup.getName());
        sgCreate.setDescription(resSecurityGroup.getDescription());
        return sgCreate;
    }

    private SgCreate huaweiCloudSgCreateMQBean(SgCreate sgCreate, ResSecurityGroup resSecurityGroup) {
        sgCreate.setName(resSecurityGroup.getName());
        sgCreate.setDescription(resSecurityGroup.getDescription());
        return sgCreate;
    }

    private SgCreate qCloudSgCreateMQBean(SgCreate sgCreate, ResSecurityGroup resSecurityGroup) {
        sgCreate.setName(resSecurityGroup.getName());
        sgCreate.setDescription(resSecurityGroup.getDescription());
        sgCreate.setRegion(sgCreate.getRegion());
        return sgCreate;
    }

    private SgCreate awsSgCreateMQBean(SgCreate sgCreate, ResSecurityGroup resSecurityGroup) {
        sgCreate.setName(resSecurityGroup.getName());
        sgCreate.setDescription(resSecurityGroup.getDescription());
        sgCreate.setVpc(resSecurityGroup.getVpc());
        return sgCreate;
    }

    private SgCreate ksyunSgCreateMQBean(SgCreate sgCreate, ResSecurityGroup resSecurityGroup) {
        sgCreate.setName(resSecurityGroup.getName());
        sgCreate.setDescription(resSecurityGroup.getDescription());
        sgCreate.setVpc(resSecurityGroup.getVpc());
        return sgCreate;
    }

    /**
     * Aliyun sg create mq bean sg create.
     *
     * @param resSecurityGroup the res security group
     * @return the sg create
     */
    private SgCreate aliyunSgCreateMQBean(SgCreate sgCreate, ResSecurityGroup resSecurityGroup) {
        sgCreate.setName(resSecurityGroup.getName());
        sgCreate.setDescription(resSecurityGroup.getDescription());
        if (!Strings.isNullOrEmpty(resSecurityGroup.getVpc())) {
            // 校验
            if (this.resVpcMapper.countByParams(new Criteria("uuid", resSecurityGroup.getVpc())) <= 0) {
                BizException.throwException("私有网络底层资源已不存在，请重新选择。");
            }
            sgCreate.setVpc(resSecurityGroup.getVpc());
        }
        return sgCreate;
    }

    /**
     * Gets group rule.
     *
     * @param groupId the group id
     * @param index   the index
     * @return the group rule
     */
    @Override
    public List<SecurityGroupRulesVO> getGroupRule(Long groupId, String index) {
        ResSecurityGroup resSecurityGroup = resSecurityGroupMapper.selectByPrimaryKey(groupId);
        AssertUtil.requireNonBlank(resSecurityGroup, "安全组未找到，请重新选择！");

        List<ResSecurityGroup> resSecurityGroups = resSecurityGroupMapper.selectByParams(
                new Criteria("cloudEnvId", resSecurityGroup.getCloudEnvId()));
        Map<String, String> sgMap = resSecurityGroups.stream()
                                                     .collect(Collectors.toMap(ResSecurityGroup::getUuid,
                                                                               ResSecurityGroup::getName,
                                                                               (key1, key2) -> key1));

        String rules = resSecurityGroup.getSecurityGroupRules();
        //如果该安全组的规则不为空，则取出来返回给页面，如果为空，则返回null
        if (StringUtil.isNotBlank(rules)) {
            //用来存放出方向规则或者入方向规则
            List<SecurityGroupRulesVO> securityGroupRules = new ArrayList<>();
            List<SecurityGroupRulesVO> securityGroupRulesList = JSONArray.parseArray(rules, SecurityGroupRulesVO.class);
            //入方向
            if (Constants.ONE.equals(index)) {
                securityGroupRulesList.forEach(rule -> {
                    rule.setSecurityGroupId(resSecurityGroup.getId().toString());
                    if (Constants.INGRESS.equalsIgnoreCase(rule.getDirection())) {
                        if (!Strings.isNullOrEmpty(rule.getRemoteGroupId()) && sgMap.containsKey(
                                rule.getRemoteGroupId())) {
                            rule.setRemoteGroupName(sgMap.get(rule.getRemoteGroupId()));
                        }
                        rule.setSecurityGroupId(resSecurityGroup.getId().toString());
                        if (!StringUtil.isNullOrEmpty(rule.getPortRangeMin()) && !StringUtil.isNullOrEmpty(
                                rule.getPortRangeMax())) {
                            rule.setFromPort(rule.getPortRangeMin());
                            rule.setToPort(rule.getPortRangeMax());
                        }
                        securityGroupRules.add(rule);
                    }
                });
            } else if (Constants.TWO.equals(index)) {
                securityGroupRulesList.forEach(rule -> {
                    rule.setSecurityGroupId(resSecurityGroup.getId().toString());
                    if (Constants.EGRESS.equalsIgnoreCase(rule.getDirection())) {
                        if (!Strings.isNullOrEmpty(rule.getRemoteGroupId()) && sgMap.containsKey(
                                rule.getRemoteGroupId())) {
                            rule.setRemoteGroupName(sgMap.get(rule.getRemoteGroupId()));
                        }
                        rule.setSecurityGroupId(resSecurityGroup.getId().toString());
                        if (!StringUtil.isNullOrEmpty(rule.getPortRangeMin()) && !StringUtil.isNullOrEmpty(
                                rule.getPortRangeMax())) {
                            rule.setFromPort(rule.getPortRangeMin());
                            rule.setToPort(rule.getPortRangeMax());
                        }
                        securityGroupRules.add(rule);

                    }
                });
            }
            return securityGroupRules;
        }
        return new ArrayList<>();
    }

    /**
     * Delete s grule boolean.
     *
     * @param securityGroupRule the security group rule
     * @return the boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteSGrule(cn.com.cloudstar.rightcloud.adapter.pojo.network.SecurityGroupRule securityGroupRule) {
        String opUser = "";
        User authUser = BasicInfoUtil.getAuthUser();
        if (Objects.nonNull(authUser) && !StringUtil.isNullOrEmpty(authUser.getAccount())) {
            opUser = BasicInfoUtil.getAuthUser().getAccount();
        }

        /**
         * 删除安全组规则
         * 1.openStack的安全组规则直接根据安全组规则的唯一id进行删除
         * 2.阿里云的安全组规则需要根据三个条件去确定一个唯一的安全组规则来进行删除
         */
        ResSecurityGroup resSecurityGroup = resSecurityGroupMapper.selectByPrimaryKey(
                Long.valueOf(securityGroupRule.getId()));
        SgRuleDelete sgRuleDelete = CloudClientFactory.buildMQBean(resSecurityGroup.getCloudEnvId(),
                                                                   SgRuleDelete.class);
        // 根据envType创建对应的sgRuleCreate对象
        if (CloudEnvType.OPEN_STACK.equals(sgRuleDelete.getProviderType()) || CloudEnvType.CLOUDOS.equals(
                sgRuleDelete.getProviderType())) {
            sgRuleDelete.setSgRuleId(securityGroupRule.getRuleId());
            sgRuleDelete.setSgId(resSecurityGroup.getUuid());
            sgRuleDelete.setRegion(resSecurityGroup.getRegion());
        } else if (CloudEnvType.ALIYUN.equals(sgRuleDelete.getProviderType())) {
            sgRuleDelete.setRegion(resSecurityGroup.getRegion());
            sgRuleDelete.setSgId(resSecurityGroup.getUuid());
            sgRuleDelete.setPortRangeMax(securityGroupRule.getPortRangeMax());
            sgRuleDelete.setPortRangeMin(securityGroupRule.getPortRangeMin());
            sgRuleDelete.setProtocol(securityGroupRule.getProtocol());
            sgRuleDelete.setRemoteIpPrefix(securityGroupRule.getRemoteIpPrefix());
            sgRuleDelete.setRemoteGroupId(securityGroupRule.getRemoteGroupId());
            sgRuleDelete.setDirection(securityGroupRule.getDirection());
        } else if (CloudEnvType.HUAWEICLOUD.equals(sgRuleDelete.getProviderType())) {
            sgRuleDelete.setRegion(resSecurityGroup.getRegion());
            sgRuleDelete.setSgRuleId(securityGroupRule.getRuleId());
            sgRuleDelete.setSgId(resSecurityGroup.getUuid());

        } else if (CloudEnvType.QCLOUD.equals(sgRuleDelete.getProviderType()) || CloudEnvType.AWS.equals(
                sgRuleDelete.getProviderType())) {
            sgRuleDelete.setDirection(securityGroupRule.getDirection());
            sgRuleDelete.setPortRangeMin(securityGroupRule.getPortRangeMin());
            sgRuleDelete.setPortRangeMax(securityGroupRule.getPortRangeMax());
            if (!StringUtil.isNullOrEmpty(securityGroupRule.getRemoteGroupId())) {
                sgRuleDelete.setRemoteGroupId(securityGroupRule.getRemoteGroupId());
            }
            sgRuleDelete.setProtocol(securityGroupRule.getProtocol());
            sgRuleDelete.setSgId(resSecurityGroup.getUuid());
            sgRuleDelete.setPolicy(securityGroupRule.getAuthorizationStrategy());
            if (!StringUtil.isNullOrEmpty(securityGroupRule.getRemoteIpPrefix())
                    && securityGroupRule.getRemoteIpPrefix().contains("null")) {
                String[] ips = securityGroupRule.getRemoteIpPrefix().split("/");
                sgRuleDelete.setRemoteIpPrefix(ips[0]);
            } else {
                sgRuleDelete.setRemoteIpPrefix(securityGroupRule.getRemoteIpPrefix());
            }

        } else if (CloudEnvType.KSYUN.equals(sgRuleDelete.getProviderType()) || CloudEnvType.KING_STACK.equals(
                sgRuleDelete.getProviderType())) {
            sgRuleDelete.setSgId(resSecurityGroup.getUuid());
            sgRuleDelete.setSgRuleId(securityGroupRule.getRuleId());
        }
        //发送同步队列删除安全组规则
        SgRuleDeleteResult sgRuleDeleteResult = (SgRuleDeleteResult) sendToMQRPC(sgRuleDelete);
        if (!sgRuleDeleteResult.isSuccess()) {
            basicResActionLogService.insertIntoActionLog(resSecurityGroup.getId().toString(), opUser,
                                                         ResourceTypeEnum.SECURITY_GROUP, ResourceOperateEnum.MODIFY,
                                                         Boolean.FALSE);

            if (CloudEnvType.OPEN_STACK.equals(sgRuleDelete.getProviderType()) || CloudEnvType.CLOUDOS.equals(
                    sgRuleDelete.getProviderType())) {
                return false;
            }
            throw new BizException(CloudErrorResolver.getErrorMsg(sgRuleDeleteResult.getErrMsg()));
        }
        basicResActionLogService.insertIntoActionLog(resSecurityGroup.getId().toString(), opUser,
                                                     ResourceTypeEnum.SECURITY_GROUP, ResourceOperateEnum.MODIFY,
                                                     Boolean.TRUE);

        boolean b = CloudEnvType.OPEN_STACK.equals(sgRuleDelete.getProviderType()) || CloudEnvType.CLOUDOS.equals(
                sgRuleDelete.getProviderType()) || CloudEnvType.HUAWEICLOUD.equals(sgRuleDelete.getProviderType());
        if (b || CloudEnvType.KSYUN.equals(sgRuleDelete.getProviderType()) || CloudEnvType.KING_STACK.equals(
                sgRuleDelete.getProviderType())) {
            List<SecurityGroupRules> securityGroupRulesList = JSONArray.parseArray(
                    resSecurityGroup.getSecurityGroupRules(), SecurityGroupRules.class);
            if (!securityGroupRulesList.isEmpty()) {
                for (int i = 0; i < securityGroupRulesList.size(); i++) {
                    if (securityGroupRulesList.get(i).getId().equals(securityGroupRule.getRuleId())) {
                        securityGroupRulesList.remove(i);
                        break;
                    }
                }
            }
            resSecurityGroup.setSecurityGroupRules(JSON.toJSONString(securityGroupRulesList));
            resSecurityGroupMapper.updateByPrimaryKeySelective(resSecurityGroup);
            return true;
        } else if (CloudEnvType.ALIYUN.equals(sgRuleDelete.getProviderType()) || CloudEnvType.QCLOUD.equals(
                sgRuleDelete.getProviderType()) || CloudEnvType.AWS.equals(sgRuleDelete.getProviderType())) {
            List<SecurityGroupRules> securityGroupRulesList = JSONArray.parseArray(
                    resSecurityGroup.getSecurityGroupRules(), SecurityGroupRules.class);
            if (securityGroupRulesList != null && !securityGroupRulesList.isEmpty()) {
                for (int i = 0; i < securityGroupRulesList.size(); i++) {
                    boolean b1 = securityGroupRulesList.get(i).getProtocol().equalsIgnoreCase(securityGroupRule.getProtocol())
                            && securityGroupRulesList.get(i)
                            .getPortRangeMin()
                            .equalsIgnoreCase(securityGroupRule.getPortRangeMin());
                    if (b1 && securityGroupRulesList.get(i)
                                                     .getPortRangeMax()
                                                     .equalsIgnoreCase(securityGroupRule.getPortRangeMax())
                            && securityGroupRulesList.get(i)
                                                     .getSecurityGroupId()
                                                     .equalsIgnoreCase(resSecurityGroup.getUuid())
                            && securityGroupRulesList.get(i)
                                                     .getDirection()
                                                     .equalsIgnoreCase(securityGroupRule.getDirection())) {
                        if (StringUtil.isNotBlank(securityGroupRule.getRemoteGroupId())) {
                            if (securityGroupRule.getRemoteGroupId()
                                                 .equalsIgnoreCase(securityGroupRulesList.get(i).getRemoteGroupId())) {
                                securityGroupRulesList.remove(i);
                                break;
                            }
                        }
                        if (StringUtil.isNotBlank(securityGroupRule.getRemoteIpPrefix())) {
                            if (securityGroupRule.getRemoteIpPrefix()
                                                 .equalsIgnoreCase(securityGroupRulesList.get(i).getRemoteIpPrefix())) {
                                securityGroupRulesList.remove(i);
                                break;
                            }
                        }
                    }
                }
                resSecurityGroup.setSecurityGroupRules(JSON.toJSONString(securityGroupRulesList));
                resSecurityGroupMapper.updateByPrimaryKeySelective(resSecurityGroup);
                return true;
            }
        }

        return false;
    }

    /**
     * Update s grule boolean.
     *
     * @param securityGroupRule the security group rule
     * @return the boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSGrule(cn.com.cloudstar.rightcloud.adapter.pojo.network.SecurityGroupRule securityGroupRule) {
        ResSecurityGroup resSecurityGroup = resSecurityGroupMapper.selectByPrimaryKey(
                Long.valueOf(securityGroupRule.getSecurityGroupId()));
        CloudEnv cloudEnv = cloudEnvMapper.selectByPrimaryKey(resSecurityGroup.getCloudEnvId());
        // 根据envType来进行不同的修改
        if (CloudEnvType.OPEN_STACK.equals(cloudEnv.getCloudEnvType()) || CloudEnvType.CLOUDOS.equals(
                cloudEnv.getCloudEnvType())) {
            List<SecurityGroupRules> securityGroupRulesList = JSONArray.parseArray(
                    resSecurityGroup.getSecurityGroupRules(), SecurityGroupRules.class);
            if (!securityGroupRulesList.isEmpty()) {
                for (int i = 0; i < securityGroupRulesList.size(); i++) {
                    if (securityGroupRulesList.get(i).getId().equals(securityGroupRule.getId())) {
                        securityGroupRulesList.get(i).setDescription(securityGroupRule.getDescription());
                        resSecurityGroup.setSecurityGroupRules(JSON.toJSONString(securityGroupRulesList));
                        resSecurityGroupMapper.updateByPrimaryKeySelective(resSecurityGroup);
                        break;
                    }
                }
            }
            return true;
        } else if (CloudEnvType.ALIYUN.equals(cloudEnv.getCloudEnvType())) {
            List<SecurityGroupRules> securityGroupRulesList = JSONArray.parseArray(
                    resSecurityGroup.getSecurityGroupRules(), SecurityGroupRules.class);
            if (!securityGroupRulesList.isEmpty()) {
                for (int i = 0; i < securityGroupRulesList.size(); i++) {
                    boolean b = securityGroupRulesList.get(i).getProtocol().equalsIgnoreCase(securityGroupRule.getProtocol())
                            && securityGroupRulesList.get(i)
                            .getPortRangeMin()
                            .equalsIgnoreCase(securityGroupRule.getPortRangeMin())
                            && securityGroupRulesList.get(i)
                            .getPortRangeMax()
                            .equalsIgnoreCase(securityGroupRule.getPortRangeMax())
                            && securityGroupRulesList.get(i)
                            .getSecurityGroupId()
                            .equalsIgnoreCase(resSecurityGroup.getUuid());
                    if (b && securityGroupRulesList.get(i)
                                                     .getDirection()
                                                     .equalsIgnoreCase(securityGroupRule.getDirection())
                            && securityGroupRulesList.get(i)
                                                     .getPriority()
                                                     .equalsIgnoreCase(securityGroupRule.getPriority())
                            && securityGroupRulesList.get(i)
                                                     .getStrategy()
                                                     .equalsIgnoreCase(securityGroupRule.getStrategy())) {
                        if (StringUtil.isNotBlank(securityGroupRule.getRemoteGroupId())) {
                            if (securityGroupRule.getRemoteGroupId()
                                                 .equalsIgnoreCase(securityGroupRulesList.get(i).getRemoteGroupId())) {
                                securityGroupRulesList.get(i).setDescription(securityGroupRule.getDescription());
                                this.updateAliSgRule(resSecurityGroup, securityGroupRulesList.get(i));
                                resSecurityGroup.setSecurityGroupRules(JSON.toJSONString(securityGroupRulesList));
                                resSecurityGroupMapper.updateByPrimaryKeySelective(resSecurityGroup);
                                break;
                            }
                        }
                        if (StringUtil.isNotBlank(securityGroupRule.getRemoteIpPrefix())) {
                            if (securityGroupRule.getRemoteIpPrefix()
                                                 .equalsIgnoreCase(securityGroupRulesList.get(i).getRemoteIpPrefix())) {
                                securityGroupRulesList.get(i).setDescription(securityGroupRule.getDescription());
                                this.updateAliSgRule(resSecurityGroup, securityGroupRulesList.get(i));
                                resSecurityGroup.setSecurityGroupRules(JSON.toJSONString(securityGroupRulesList));
                                resSecurityGroupMapper.updateByPrimaryKeySelective(resSecurityGroup);
                                break;
                            }
                        }
                    }
                }
                return true;
            }
        } else if (CloudEnvType.HUAWEICLOUD.equals(cloudEnv.getCloudEnvType())) {
            List<SecurityGroupRules> securityGroupRulesList = JSONArray.parseArray(
                    resSecurityGroup.getSecurityGroupRules(), SecurityGroupRules.class);
            if (!securityGroupRulesList.isEmpty()) {
                for (int i = 0; i < securityGroupRulesList.size(); i++) {
                    if (securityGroupRulesList.get(i).getId().equals(securityGroupRule.getId())) {
                        securityGroupRulesList.get(i).setDescription(securityGroupRule.getDescription());
                        resSecurityGroup.setSecurityGroupRules(JSON.toJSONString(securityGroupRulesList));
                        resSecurityGroupMapper.updateByPrimaryKeySelective(resSecurityGroup);
                        break;
                    }
                }
            }
            return true;
        } else if (CloudEnvType.QCLOUD.equals(cloudEnv.getCloudEnvType()) || CloudEnvType.AWS.equals(
                cloudEnv.getCloudEnvType())) {
            List<SecurityGroupRules> securityGroupRulesList = JSONArray.parseArray(
                    resSecurityGroup.getSecurityGroupRules(), SecurityGroupRules.class);
            if (!securityGroupRulesList.isEmpty()) {
                for (int i = 0; i < securityGroupRulesList.size(); i++) {
                    boolean b = securityGroupRulesList.get(i).getProtocol().equalsIgnoreCase(securityGroupRule.getProtocol())
                            && securityGroupRulesList.get(i)
                            .getPortRangeMin()
                            .equalsIgnoreCase(securityGroupRule.getPortRangeMin())
                            && securityGroupRulesList.get(i)
                            .getPortRangeMax()
                            .equalsIgnoreCase(securityGroupRule.getPortRangeMax())
                            && securityGroupRulesList.get(i)
                            .getSecurityGroupId()
                            .equalsIgnoreCase(resSecurityGroup.getUuid());
                    if (b && securityGroupRulesList.get(i)
                                                     .getDirection()
                                                     .equalsIgnoreCase(securityGroupRule.getDirection())
                            && securityGroupRulesList.get(i)
                                                     .getStrategy()
                                                     .equalsIgnoreCase(securityGroupRule.getStrategy())) {
                        if (StringUtil.isNotBlank(securityGroupRule.getRemoteGroupId())) {
                            if (securityGroupRule.getRemoteGroupId()
                                                 .equalsIgnoreCase(securityGroupRulesList.get(i).getRemoteGroupId())) {
                                securityGroupRulesList.get(i).setDescription(securityGroupRule.getDescription());
                                this.updateQcloudSgRule(resSecurityGroup, securityGroupRulesList.get(i));
                                resSecurityGroup.setSecurityGroupRules(JSON.toJSONString(securityGroupRulesList));
                                resSecurityGroupMapper.updateByPrimaryKeySelective(resSecurityGroup);
                                break;
                            }
                        }
                        if (StringUtil.isNotBlank(securityGroupRule.getRemoteIpPrefix())) {
                            if (securityGroupRule.getRemoteIpPrefix()
                                                 .equalsIgnoreCase(securityGroupRulesList.get(i).getRemoteIpPrefix())) {
                                securityGroupRulesList.get(i).setDescription(securityGroupRule.getDescription());
                                this.updateQcloudSgRule(resSecurityGroup, securityGroupRulesList.get(i));
                                resSecurityGroup.setSecurityGroupRules(JSON.toJSONString(securityGroupRulesList));
                                resSecurityGroupMapper.updateByPrimaryKeySelective(resSecurityGroup);
                                break;
                            }
                        }
                    }
                }
                return true;
            }
        } else if (CloudEnvType.KSYUN.equals(cloudEnv.getCloudEnvType()) || CloudEnvType.KING_STACK.equals(
                cloudEnv.getCloudEnvType())) {
            List<SecurityGroupRules> securityGroupRulesList = JSONArray.parseArray(
                    resSecurityGroup.getSecurityGroupRules(), SecurityGroupRules.class);
            if (!securityGroupRulesList.isEmpty()) {
                for (int i = 0; i < securityGroupRulesList.size(); i++) {
                    if (securityGroupRulesList.get(i).getId().equals(securityGroupRule.getId())) {
                        securityGroupRulesList.get(i).setDescription(securityGroupRule.getDescription());
                        resSecurityGroup.setSecurityGroupRules(JSON.toJSONString(securityGroupRulesList));
                        resSecurityGroupMapper.updateByPrimaryKeySelective(resSecurityGroup);
                        break;
                    }
                }
            }
            return true;
        }
        return false;
    }

    /**
     * 阿里云修改云平台描述
     */
    private void updateAliSgRule(ResSecurityGroup resSecurityGroup, SecurityGroupRules rules) {
        String opUser = "";
        User authUser = BasicInfoUtil.getAuthUser();
        if (Objects.nonNull(authUser) && !StringUtil.isNullOrEmpty(authUser.getAccount())) {
            opUser = BasicInfoUtil.getAuthUser().getAccount();
        }
        SgRuleUpdate sgRuleUpdate = CloudClientFactory.buildMQBean(resSecurityGroup.getCloudEnvId(),
                                                                   SgRuleUpdate.class);
        sgRuleUpdate.setRegion(resSecurityGroup.getRegion());
        sgRuleUpdate.setSgId(resSecurityGroup.getUuid());
        sgRuleUpdate.setNetType(resSecurityGroup.getNetType());

        sgRuleUpdate.setPortRangeMax(rules.getPortRangeMax());
        sgRuleUpdate.setPortRangeMin(rules.getPortRangeMin());
        sgRuleUpdate.setProtocol(rules.getProtocol());
        sgRuleUpdate.setRemoteIpPrefix(rules.getRemoteIpPrefix());
        sgRuleUpdate.setRemoteGroupId(rules.getRemoteGroupId());
        sgRuleUpdate.setDirection(rules.getDirection());
        sgRuleUpdate.setStrategy(rules.getStrategy().toLowerCase());
        sgRuleUpdate.setDescription(rules.getDescription());
        sgRuleUpdate.setPriority(rules.getPriority());

        //发送同步队列修改安全组规则
        BaseResult baseResult = (BaseResult) sendToMQRPC(sgRuleUpdate);
        basicResActionLogService.insertIntoActionLog(resSecurityGroup.getId().toString(), opUser,
                                                     ResourceTypeEnum.SECURITY_GROUP, ResourceOperateEnum.MODIFY,
                                                     Boolean.FALSE);
        if (!baseResult.isSuccess()) {
            throw new BizException(CloudErrorResolver.getErrorMsg(baseResult.getErrMsg()));
        }
    }

    //修改qcloud描述
    private void updateQcloudSgRule(ResSecurityGroup resSecurityGroup, SecurityGroupRules rules) {
        String opUser = "";
        User authUser = BasicInfoUtil.getAuthUser();
        if (Objects.nonNull(authUser) && !StringUtil.isNullOrEmpty(authUser.getAccount())) {
            opUser = BasicInfoUtil.getAuthUser().getAccount();
        }
        SgRuleUpdate sgRuleUpdate = CloudClientFactory.buildMQBean(resSecurityGroup.getCloudEnvId(),
                                                                   SgRuleUpdate.class);
        sgRuleUpdate.setRegion(resSecurityGroup.getRegion());
        sgRuleUpdate.setSgId(resSecurityGroup.getUuid());
        sgRuleUpdate.setNetType(resSecurityGroup.getNetType());

        sgRuleUpdate.setPortRangeMax(rules.getPortRangeMax());
        sgRuleUpdate.setPortRangeMin(rules.getPortRangeMin());
        sgRuleUpdate.setProtocol(rules.getProtocol());
        if (!StringUtil.isNullOrEmpty(rules.getRemoteIpPrefix()) && rules.getRemoteIpPrefix().contains("null")) {
            String[] ips = rules.getRemoteIpPrefix().split("/");
            sgRuleUpdate.setRemoteIpPrefix(ips[0]);
        } else {
            sgRuleUpdate.setRemoteIpPrefix(rules.getRemoteIpPrefix());
        }
        if (!StringUtil.isNullOrEmpty(rules.getRemoteGroupId()) && rules.getRemoteGroupId().contains("-")) {
            sgRuleUpdate.setRemoteGroupId(rules.getRemoteGroupId());
        } else if (!StringUtil.isNullOrEmpty(rules.getRemoteGroupId())) {
            sgRuleUpdate.setRemoteGroupId(
                    resSecurityGroupMapper.selectByPrimaryKey(Long.parseLong(rules.getRemoteGroupId())).getUuid());
        }
        sgRuleUpdate.setDirection(rules.getDirection());
        sgRuleUpdate.setStrategy(rules.getStrategy().toLowerCase());
        sgRuleUpdate.setDescription(rules.getDescription());
        sgRuleUpdate.setPriority(rules.getPriority());

        //发送同步队列修改安全组规则
        BaseResult baseResult = (BaseResult) sendToMQRPC(sgRuleUpdate);
        basicResActionLogService.insertIntoActionLog(resSecurityGroup.getId().toString(), opUser,
                                                     ResourceTypeEnum.SECURITY_GROUP, ResourceOperateEnum.MODIFY,
                                                     Boolean.FALSE);
        if (!baseResult.isSuccess()) {
            throw new BizException(CloudErrorResolver.getErrorMsg(baseResult.getErrMsg()));
        }
    }

    /**
     * Gets images from platform.
     *
     * @param criteria the criteria
     * @return the images from platform
     */
    @Override
    public List<?> getImagesFromPlatform(Criteria criteria) {
        List<ResImage> resImages = this.resImageService.selectByExample(criteria);
        for (ResImage resImage : resImages) {
            resImage.setSwitchBtn(ResImageStatus.OK.equals(resImage.getStatus()));
        }
        return resImages;
    }

    /**
     * Gets networks platform.
     *
     * @param criteria the criteria
     * @return the networks platform
     */
    @Override
    public List<ResVpc> getNetworksPlatform(Criteria criteria) {
        CloudEnv cloudEnv = this.cloudEnvMapper.selectByPrimaryKey(
                Long.valueOf(criteria.get(Constants.CLOUD_ENV_ID).toString()));
        assertEnvExists(cloudEnv);

        if (criteria.get(Constants.EXTERNAL) == null) {
            criteria.put(Constants.EXTERNAL, "false");
        }
        List<ResVpc> netVpcs = this.resVpcMapper.selectByParams(criteria);
        netVpcs.forEach(netVpc -> {
            criteria.clear();
            criteria.put("netVpcId", netVpc.getId());
            criteria.put("cloudEnvId", cloudEnv.getId());
            List<Network> networks = this.networkMapper.selectByParams(
                    criteria);
            if (!CollectionUtils.isEmpty(networks)) {
                netVpc.setSubNetworks(networks);
            }
        });
        return netVpcs;
    }

    private void assertEnvExists(CloudEnv cloudEnv) {
        if (Objects.isNull(cloudEnv)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_832756465));
        }
    }

    /**
     * Create volume boolean.
     *
     * @param resVd the res vd
     * @return the boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createVolume(ResVd resVd) {
        if (!checkVdNameUnique(resVd)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1788848735));
        }
        this.cloudEnvService.checkCloudEnvNormal(resVd.getCloudEnvId(), true);
        CloudEnv env = cloudEnvMapper.selectByPrimaryKey(resVd.getCloudEnvId());

        resVd.setRegion(env.getRegion());

        //region res_vd表res_volume_type_uuid字段赋值
        if (Objects.nonNull(resVd.getVolumeTypeId())) {
            ResVolumeType resVolumeType = resVolumeTypeMapper.selectByPrimaryKey(resVd.getVolumeTypeId());
            resVd.setVolumeTypeUuid(Objects.nonNull(resVolumeType) ? resVolumeType.getUuid() : null);
        }
        //endregion

        if (!CloudEnvType.VMWARE.equals(env.getCloudEnvType())) {
            orgRemoteService.setOwnerOrgSid(resVd);
        }

        String resVmId = resVd.getResVmId();
        // VMWare从存储类别中进行挑选
        if (CloudEnvType.VMWARE.equals(env.getCloudEnvType()) || CloudEnvType.FUSIONCOMPUTE.equals(
                env.getCloudEnvType())) {
            if (!Strings.isNullOrEmpty(resVmId) && !"-999".equals(resVmId)) {
                ResStorageProvisionModel storeOnVmware = resService.pickUpStoreOnVmware(resVd.getVolumeTypeId(),
                                                                                        resVmId,
                                                                                        resVd.getAllocateDiskSize());
                resVd.setAllocateResStorageSid(storeOnVmware.getId());
                resVd.setDataStoreName(storeOnVmware.getName());
                resVd.setDataStoreUrn(storeOnVmware.getDataStoreUrn());
            } else {
                ResStorageProvisionModel storeOnVmware = resService.pickUpStoreOnVmware(resVd.getVolumeTypeId(),
                                                                                        resVd.getAllocateDiskSize());
                resVd.setAllocateResStorageSid(storeOnVmware.getId());
                resVd.setDataStoreName(storeOnVmware.getName());
                resVd.setDataStoreUrn(storeOnVmware.getDataStoreUrn());
            }

        } else if (CloudEnvType.AWS.equals(env.getCloudEnvType())) {
            List<ResVolumeType> resVolumeTypes = this.resVolumeTypeMapper.selectByParamsOpenStack(
                    new Criteria("cloudEnvType", env.getCloudEnvType()));
            Map<String, Long> volTypeMap = resVolumeTypes.stream()
                                                         .collect(Collectors.toMap(ResVolumeType::getUuid,
                                                                                   ResVolumeType::getId));
            resVd.setVolumeTypeId(volTypeMap.get(resVd.getDiskType()));
        }
        String snapshotId = resVd.getSnapshotId();
        if (!StringUtil.isNullOrEmpty(snapshotId)) {
            if (CloudEnvType.HUAWEICLOUD.equals(env.getCloudEnvType())) {
                List<ResSnapshot> snapshots = resSnapshotMapper.selectByParams(new Criteria("uuid", snapshotId));
                if (snapshots != null && snapshots.size() > 0) {
                    //获取磁盘ID
                    String resVdId = snapshots.get(0).getResVdId();
                    //对区域进行设置
                    ResVd tempVd = resVdMapper.selectByPrimaryKey(resVdId);
                    if (tempVd != null) {
                        Long allocateDiskSize = tempVd.getAllocateDiskSize();
                        if (allocateDiskSize > resVd.getAllocateDiskSize()) {
                            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_423276345));
                        }
                        AssertUtil.requireNonBlank(tempVd.getDiskType(), "快照关联硬盘DiskTypes数据为空，请同步硬盘数据！");
                        resVd.setZone(tempVd.getZone());
                        resVd.setRegion(tempVd.getRegion());
                        resVd.setDiskType(tempVd.getDiskType());
                        resVd.setVolumeTypeId(tempVd.getVolumeTypeId());
                    }
                }
            } else if (CloudEnvType.CLOUDOS.equals(env.getCloudEnvType())) {
                Criteria criteria = new Criteria();
                criteria.put("uuid", resVd.getSnapshotId());
                String resVdId = resSnapshotMapper.selectByParams(criteria).get(0).getResVdId();
                resVd.setZone(resVdMapper.selectByPrimaryKey(resVdId).getZone());
            }
        }

        // 设置当前状态为创建中
        resVd.setStatus(ResVdStatus.CREATING);
        resVd.setStoragePurpose(StoragePurpose.DATA_DISK);
        if (CloudEnvType.VMWARE.equals(env.getCloudEnvType())) {
            resVd.setReleaseMode(ReleaseMode.WITH_INSTANCE);
        } else {
            resVd.setReleaseMode(ReleaseMode.STAND_ALONE);
        }
        resVd.setResVmId(null);
        if (Objects.nonNull(resVd.getImageId())) {
            resVd.setBootable(true);
        }
        String imageId = resVd.getImageId();
        if (CloudEnvType.OPEN_STACK.equals(env.getCloudEnvType())) {
            if (StringUtils.isNotBlank(resVd.getSnapshotId())) {
                List<ResSnapshot> resSnapshots = resSnapshotMapper.selectByParams(
                        new Criteria("uuid", resVd.getSnapshotId()).put("cloudEnvId", env.getId())
                                                                   .put("status", SnapshotStatus.SUCCESS));
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(resSnapshots)) {
                    ResSnapshot resSnapshot = resSnapshots.get(0);
                    if ("RES-VM".equalsIgnoreCase(resSnapshot.getResType())) {
                        resVd.setBootable(true);
                        imageId = snapshotId;
                        snapshotId = null;
                    }
                }
            }

            ResVolumeType volumeType = resVolumeTypeService.selectByPrimaryKey(resVd.getVolumeTypeId());
            if (Objects.nonNull(volumeType)) {
                resVd.setVolumeTypeUuid(volumeType.getUuid());
            }
        }
        resVdMapper.insertSelective(resVd);

        if (Objects.nonNull(resVd.getServiceDeployInstId())) {
            ServiceInstTarget serviceInstTarget = new ServiceInstTarget();
            serviceInstTarget.setTargetId(resVd.getResVdSid());
            serviceInstTarget.setSfServiceInstId(resVd.getServiceDeployInstId());
            serviceInstTarget.setTargetType(DeploymentMode.DISK);
            serviceInstTarget.setHasCreate(true);
            serviceInstTargetRemoteService.insert(serviceInstTarget);
        }

        DiskCreate diskCreate = null;
        // 根据envType创建对应的diskCreate对象
        if (CloudEnvType.OPEN_STACK.equals(env.getCloudEnvType())) {
            diskCreate = makeOpenstackMQBean(resVd);
            ResVolumeType resVolumeType = resVolumeTypeMapper.selectByPrimaryKey(resVd.getVolumeTypeId());
            AssertUtil.requireNonBlank(resVolumeType, "磁盘类型" + resVd.getVolumeTypeId() + "未找到");
            diskCreate.setVolumeTypeId(
                    VolumeType.DEFAULT_VOLUME_UUID.equals(resVolumeType.getUuid()) ? "" : resVolumeType.getUuid());
            diskCreate.setImageId(imageId);
            diskCreate.setSnapshotId(snapshotId);
        } else if (CloudEnvType.VMWARE.equals(env.getCloudEnvType())) {
            diskCreate = makeVMWareMQbean(resVd);
        } else if (CloudEnvType.ALIYUN.equals(env.getCloudEnvType())) {
            diskCreate = makeAliyunMQBean(resVd);
        } else if (CloudEnvType.POWER_VC.equals(env.getCloudEnvType())) {
            diskCreate = makePowerVcMQBean(resVd);
        } else if (CloudEnvType.AWS.equals(env.getCloudEnvType())) {
            diskCreate = makeAwsMQBean(resVd);
            diskCreate.setSnapshotId(resVd.getSnapshotId());
        } else if (CloudEnvType.QCLOUD.equals(env.getCloudEnvType())) {
            diskCreate = makeQcloudMQBean(resVd);
        } else if (CloudEnvType.HUAWEICLOUD.equals(env.getCloudEnvType())) {
            diskCreate = makeHuaweiCloudMQBean(resVd);
            ResVolumeType resVolumeType = resVolumeTypeMapper.selectByPrimaryKey(resVd.getVolumeTypeId());
            AssertUtil.requireNonBlank(resVolumeType, "磁盘类型" + resVd.getVolumeTypeId() + "未找到");
            diskCreate.setVolumeTypeId(resVolumeType.getUuid());
            diskCreate.setOptions(ImmutableMap.of("resVmId", "", "device", ""));
            diskCreate.setSnapshotId(resVd.getSnapshotId());
            diskCreate.setChargeType(resVd.getChargeType());
            diskCreate.setPeriod(resVd.getPeriod());

        } else if (CloudEnvType.CLOUDOS.equals(env.getCloudEnvType())) {
            diskCreate = makeCloudOSMQBean(resVd);
            ResVolumeType resVolumeType = resVolumeTypeMapper.selectByPrimaryKey(resVd.getVolumeTypeId());
            diskCreate.setVolumeTypeId(
                    VolumeType.DEFAULT_VOLUME_UUID.equals(resVolumeType.getUuid()) ? "" : resVolumeType.getUuid());
            diskCreate.setSnapshotId(resVd.getSnapshotId());
        } else if (CloudEnvType.FUSIONCOMPUTE.equals(env.getCloudEnvType())) {
            diskCreate = makeFusionComputeMQBean(resVd);
        } else if (CloudEnvType.KSYUN.equals(env.getCloudEnvType()) || CloudEnvType.KING_STACK.equals(
                env.getCloudEnvType())) {
            diskCreate = makeKsyunMQBean(resVd);
        }
        // 如果hostId不为空，获取对应的instanceId
        if (!Strings.isNullOrEmpty(resVmId) && !"-999".equals(resVmId) && !CloudEnvType.HUAWEICLOUD.equals(
                env.getCloudEnvType())) {
            ResVm resVm = resVmMapper.selectByPrimaryKey(resVmId);
            diskCreate.setOptions(
                    MapsKit.of("resVmId", resVm.getId(), "instanceId", resVm.getInstanceId(), "originStatus",
                               resVm.getStatus(), "vmUri", resVm.getUri()));
            if (CloudEnvType.VMWARE.equals(env.getCloudEnvType())) {
                diskCreate.setVmName(resVm.getInstanceName());
                diskCreate.setVmId(resVm.getInstanceId());
            }

            // 将主机设置为配置中
            resVm.setStatus(ResVmStatus.SETTING);
            resVmMapper.updateByPrimaryKeySelective(resVm);
        } else if (!Strings.isNullOrEmpty(resVmId) && !"-999".equals(resVmId)) {
            String path = "/dev/sd" + x;
            String vdPath = "/dev/vd" + x;
            ResVm resVm = resVmMapper.selectByPrimaryKey(resVmId);
            //获取所获取的硬盘信息
            List<ResVd> resVds = resVdMapper.selectByParams(new Criteria("resVmId", resVmId));
            //对磁盘的地址进行检查
            //创建一个集合进行存储
            Set<String> paths = new HashSet<>();
            for (ResVd vd : resVds) {
                String testPath = vd.getPath();
                paths.add(testPath);
            }
            boolean isHuaweiCloud = CloudEnvType.HUAWEICLOUD.equals(env.getCloudEnvType());
            while (paths.contains(path) || (isHuaweiCloud && paths.contains(vdPath))) {
                if (x > 120) {
                    x = 99;
                } else {
                    x++;
                    path = "/dev/sd" + x;
                    vdPath = "/dev/vd" + x;
                }
            }
            diskCreate.setOptions(
                    ImmutableMap.of("resVmId", resVm.getId(), "instanceId", resVm.getInstanceId(), "device", path));
            x++;
            if (CloudEnvType.VMWARE.equals(env.getCloudEnvType())) {
                diskCreate.setVmName(resVm.getInstanceName());
            }
        }
        if (Objects.nonNull(diskCreate)) {
            diskCreate.setPeriod(resVd.getPeriod());
        }
        // 发送创建块存储信息
        ResInstResult resInstResult;
        if (CloudEnvType.VMWARE.equals(env.getCloudEnvType()) && StrUtil.isNotEmpty(resVmId)) {
            String diskListKey = "vmware:disk:create:" + resVmId;
            JedisUtil jedisUtil = JedisUtil.instance();
            jedisUtil.addList(diskListKey, JSONUtil.toJsonStr(diskCreate));
            long count = jedisUtil.countList(diskListKey);
            logger.info("VMWare 虚拟机:{} 硬盘创建队列[增加]，总数量:{}", resVmId, count);
            if (count > 1) {
                resInstResult = new ResInstResult(ResInstResult.SUCCESS);
            } else {
                jedisUtil.set("vmware:disk:create:originStatus:" + resVmId,
                              diskCreate.getOptions().get("originStatus").toString());
                resInstResult = sendToMQ(diskCreate);
            }
        } else {
            resInstResult = sendToMQ(diskCreate);
        }

        return resInstResult.getStatus();
    }

    private DiskCreate makeKsyunMQBean(ResVd resVd) {
        DiskCreate diskCreate = CloudClientFactory.buildMQBean(resVd.getCloudEnvId(), DiskCreate.class);
        diskCreate.setSid(resVd.getResVdSid());
        diskCreate.setName(resVd.getVdName());
        diskCreate.setAvailabilityZone(resVd.getZone());
        diskCreate.setSize(resVd.getAllocateDiskSize().toString());
        ResVolumeType resVolumeType = resVolumeTypeMapper.selectByPrimaryKey(resVd.getVolumeTypeId());
        AssertUtil.requireNonBlank(resVolumeType, "磁盘类型" + resVd.getVolumeTypeId() + "未找到");
        diskCreate.setVolumeTypeId(resVolumeType.getUuid());
        diskCreate.setChargeType(resVd.getChargeType());
        return diskCreate;
    }


    private DiskCreate makeCloudOSMQBean(ResVd resVd) {
        DiskCreate diskCreate = CloudClientFactory.buildMQBean(resVd.getCloudEnvId(), DiskCreate.class);
        diskCreate.setSid(resVd.getResVdSid());
        diskCreate.setDescription("");
        diskCreate.setName(resVd.getVdName());
        Criteria criteria = new Criteria();
        criteria.put("uuid", resVd.getZone());
        diskCreate.setAvailabilityZone(resZoneMapper.selectByParams(criteria).get(0).getName());
        diskCreate.setSize(resVd.getAllocateDiskSize().toString());
        diskCreate.setRegion(resVd.getRegion());
        if (!StringUtil.isNullOrEmpty(resVd.getDiskType()) && "share".equalsIgnoreCase(resVd.getDiskType())) {
            diskCreate.setDiskType("share");
        } else {
            diskCreate.setDiskType("normal");
        }
        return diskCreate;
    }

    private DiskCreate makeHuaweiCloudMQBean(ResVd resVd) {
        DiskCreate diskCreate = CloudClientFactory.buildMQBean(resVd.getCloudEnvId(), DiskCreate.class);
        diskCreate.setSid(resVd.getResVdSid());
        diskCreate.setDescription("");
        diskCreate.setName(resVd.getVdName());
        diskCreate.setAvailabilityZone(resVd.getZone());
        diskCreate.setSize(resVd.getAllocateDiskSize().toString());
        diskCreate.setRegion(resVd.getRegion());
        diskCreate.setDiskType(resVd.getDiskType());
        return diskCreate;
    }

    private DiskCreate makeOpenstackMQBean(ResVd resVd) {
        DiskCreate diskCreate = CloudClientFactory.buildMQBean(resVd.getCloudEnvId(), DiskCreate.class);
        diskCreate.setSid(resVd.getResVdSid());
        diskCreate.setDescription("");
        diskCreate.setName(resVd.getVdName());
        diskCreate.setAvailabilityZone(resVd.getZone());
        diskCreate.setSize(resVd.getAllocateDiskSize().toString());
        diskCreate.setRegion(resVd.getRegion());
        diskCreate.setDescription(resVd.getDescription());
        return diskCreate;
    }

    private DiskCreate makeVMWareMQbean(ResVd resVd) {
        DiskCreate diskCreate = CloudClientFactory.buildMQBean(resVd.getCloudEnvId(), DiskCreate.class);
        diskCreate.setSid(resVd.getResVdSid());
        StringBuilder folderPath = new StringBuilder("VirtualDiskStorage/").append(resVd.getCreatedBy());
        diskCreate.setDevicePath(folderPath.toString());
        diskCreate.setName(resVd.getVdName());
        diskCreate.setLocation(resVd.getDataStoreName());
        diskCreate.setSize(resVd.getAllocateDiskSize().toString());
        diskCreate.setRegion(resVd.getRegion());
        //传入创建磁盘的的类型
        diskCreate.setVirtualDiskType(resVd.getDiskType());
        return diskCreate;
    }

    private DiskCreate makeAliyunMQBean(ResVd resVd) {
        DiskCreate diskCreate = CloudClientFactory.buildMQBean(resVd.getCloudEnvId(), DiskCreate.class);
        diskCreate.setSid(resVd.getResVdSid());
        diskCreate.setName(resVd.getVdName());
        diskCreate.setAvailabilityZone(resVd.getZone());
        diskCreate.setDiskType(resVd.getDiskType());
        diskCreate.setSize(resVd.getAllocateDiskSize().toString());
        diskCreate.setRegion(resVd.getRegion());
        return diskCreate;
    }

    private DiskCreate makeQcloudMQBean(ResVd resVd) {
        DiskCreate diskCreate = CloudClientFactory.buildMQBean(resVd.getCloudEnvId(), DiskCreate.class);
        diskCreate.setSid(resVd.getResVdSid());
        diskCreate.setName(resVd.getVdName());
        diskCreate.setAvailabilityZone(resVd.getZone());
        diskCreate.setSize(resVd.getAllocateDiskSize().toString());
        diskCreate.setRegion(resVd.getRegion());
        diskCreate.setPeriod(resVd.getPeriod());
        diskCreate.setVirtualDiskType(resVd.getDiskType());
        diskCreate.setChargeType(resVd.getDiskChargeType());
        return diskCreate;
    }

    private DiskCreate makePowerVcMQBean(ResVd resVd) {
        DiskCreate diskCreate = CloudClientFactory.buildMQBean(resVd.getCloudEnvId(), DiskCreate.class);
        diskCreate.setSid(resVd.getResVdSid());
        diskCreate.setName(resVd.getVdName());
        diskCreate.setAvailabilityZone(resVd.getZone());
        diskCreate.setSize(resVd.getAllocateDiskSize().toString());
        return diskCreate;
    }

    private DiskCreate makeAwsMQBean(ResVd resVd) {
        DiskCreate diskCreate = CloudClientFactory.buildMQBean(resVd.getCloudEnvId(), DiskCreate.class);
        diskCreate.setSid(resVd.getResVdSid());
        diskCreate.setName(resVd.getVdName());
        diskCreate.setAvailabilityZone(resVd.getZone());
        diskCreate.setDiskType(resVd.getDiskType());
        diskCreate.setSize(resVd.getAllocateDiskSize().toString());
        diskCreate.setRegion(resVd.getRegion());
        return diskCreate;
    }

    private DiskCreate makeFusionComputeMQBean(ResVd resVd) {
        DiskCreate diskCreate = CloudClientFactory.buildMQBean(resVd.getCloudEnvId(), DiskCreate.class);
        diskCreate.setSid(resVd.getResVdSid());
        diskCreate.setName(resVd.getVdName());
        diskCreate.setLocation(resVd.getDataStoreUrn());
        diskCreate.setSize(resVd.getAllocateDiskSize().toString());
        // 类型 普通 共享
        diskCreate.setDiskType(resVd.getDiskMode());
        // 配置模式 普通延迟置零
        diskCreate.setVirtualDiskType(resVd.getDiskType());
        diskCreate.setIndep(resVd.getIndep());
        diskCreate.setPersistent(resVd.getPersistent());
        diskCreate.setRegion(resVd.getRegion());
        return diskCreate;
    }

    /**
     * 发送MQ消息
     *
     * @param base the vm create
     * @return the res inst result
     */
    private ResInstResult sendToMQ(Base base) {
        ResInstResult result;
        try {
            logger.info("输入MQ参数：[{}]", JsonUtil.toJson(base));
            MQHelper.sendMessage(base);
            result = new ResInstResult(ResInstResult.SUCCESS);
        } catch (Exception e) {
            result = new ResInstResult(ResInstResult.FAILURE, e.getMessage());
        }
        return result;
    }

    /**
     * Detach volume boolean.
     *
     * @param resVdSid the res vd sid
     * @return the boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean detachVolume(String resVdSid) {
        ResVd resVd = resVdMapper.selectByPrimaryKey(resVdSid);
        if (resVd == null) {
            BizException.throwException("硬盘底层资源已不存在，请重新选择。");
        }
        ResVm resVm = resVmService.selectByPrimaryKey(resVd.getResVmId());
        if (resVm == null) {
            BizException.throwException("主机实例底层资源已不存在，请重新选择。");
        }

        if (ResVdStatus.SETTING.equals(resVd.getStatus())) {
            BizException.throwException("硬盘正在设置中，请稍后查看。");
        }
        // 更新块存储为正在卸载状态
        //bug 16585  要求卸载数据盘时，状态仍然为normal
        //bug 19935  要求ESCloud、OpenStack环境为配置中
        if (CloudEnvType.ESCLOUD.equals(resVm.getCloudEnvType()) || CloudEnvType.OPEN_STACK.equals(
                resVm.getCloudEnvType()) || CloudEnvType.FUSIONCOMPUTE.equals(resVm.getCloudEnvType())) {
            resVd.setStatus(ResVdStatus.SETTING);
        }
        resVdMapper.updateByPrimaryKeySelective(resVd);

        DiskDetach diskDetach = CloudClientFactory.buildMQBean(resVm.getCloudEnvId(), DiskDetach.class);
        if (CloudEnvType.OPEN_STACK.equals(diskDetach.getProviderType()) || CloudEnvType.CLOUDOS.equals(
                diskDetach.getProviderType())) {
            diskDetach.setServerId(resVm.getInstanceId());
            diskDetach.setVolumeId(resVd.getUuid());
            diskDetach.setRegion(resVm.getRegion());
        } else if (CloudEnvType.POWER_VC.equals(diskDetach.getProviderType())) {
            diskDetach.setServerId(resVm.getInstanceId());
            diskDetach.setVolumeId(resVd.getUuid());
        } else if (CloudEnvType.ALIYUN.equals(diskDetach.getProviderType()) || CloudEnvType.AWS.equals(
                diskDetach.getProviderType()) || CloudEnvType.QCLOUD.equals(diskDetach.getProviderType())) {
            diskDetach.setServerId(resVm.getInstanceId());
            diskDetach.setVolumeId(resVd.getUuid());
            diskDetach.setRegion(resVm.getRegion());
        } else if (CloudEnvType.VMWARE.equals(diskDetach.getProviderType())) {
            diskDetach.setDataStore(resVd.getDeviceName());
            diskDetach.setVmName(resVm.getInstanceName());
            diskDetach.setVolumeId(resVd.getVdName());
            diskDetach.setDevice(resVd.getLogicVolume());
            diskDetach.setServerId(resVm.getInstanceId());
        } else if (CloudEnvType.HUAWEICLOUD.equals(diskDetach.getProviderType())) {
            diskDetach.setServerId(resVm.getInstanceId());
            diskDetach.setVolumeId(resVd.getUuid());
            diskDetach.setRegion(resVm.getRegion());
        } else if (CloudEnvType.FUSIONCOMPUTE.equals(diskDetach.getProviderType())) {
            diskDetach.setServerId(resVm.getUri());
            diskDetach.setVolumeId(resVd.getUrn());
        } else if (CloudEnvType.KSYUN.equals(diskDetach.getProviderType()) || CloudEnvType.KING_STACK.equals(
                diskDetach.getProviderType())) {
            diskDetach.setServerId(resVm.getInstanceId());
            diskDetach.setVolumeId(resVd.getUuid());
        }

        diskDetach.setOptions(ImmutableMap.of("resVdSid", resVdSid));

        sendToMQ(diskDetach);

        return true;
    }

    /**
     * Create vm type boolean.
     *
     * @param resVmType the res vm type
     * @return the boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createVmType(ResVmType resVmType) {
        User authUser = BasicInfoUtil.getAuthUser();
        String opUser = (Objects.nonNull(authUser) && !StringUtil.isNullOrEmpty(authUser.getAccount()))
                        ? BasicInfoUtil.getAuthUser().getAccount() : "";
        CloudEnv cloudEnv = cloudEnvMapper.selectByPrimaryKey(resVmType.getCloudEnvId());
        if (Objects.isNull(cloudEnv)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_832756465));
        }
        // 分区id
        List<ResPool> resPools = resVmType.getIds();

        // 在cpu和ram相同的时候，统一uuid
        if (resVmType.getRam() % 1024 > 0) {
            resVmType.setUuid(String.format("%sC-%sM", resVmType.getCpu(), resVmType.getRam()));
        } else {
            resVmType.setUuid(String.format("%sC-%sG", resVmType.getCpu(), resVmType.getRam() / 1024));
        }
        this.resVmTypeMapper.insertSelective(resVmType);
        List<ResVmType> resVmTypeList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(resPools)) {
            resPools.forEach(i -> {
                ResPoolResource resPoolResource = new ResPoolResource();
                resPoolResource.setResPoolId(i.getId());
                resPoolResource.setResourceSid(resVmType.getId().toString());
                BasicWebUtil.prepareInsertParams(resPoolResource, opUser);
                resPoolResource.setResourceType("RES_VOLUME_TYPE");
                this.resPoolResourceMapper.insertSelective(resPoolResource);
                ResVmType resVmType1 = new ResVmType();
                BeanUtils.copyProperties(resVmType, resVmType1);
                resVmType1.setZone(i.getName());
                resVmTypeList.add(resVmType1);
            });
            ResVmType resVmType1 = new ResVmType();
            BeanUtils.copyProperties(resVmType, resVmType1);
            resVmType1.setZone(null);
            resVmTypeList.add(resVmType1);
        } else {
            // 如果创建实例类型的时候，没有选择分区，那么也会插入一条默认的数据。
            resVmTypeList.add(resVmType);
        }

        return true;
    }

    /**
     * Update vm type boolean.
     *
     * @param resVmType the res vm type
     * @return the boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateVmType(ResVmType resVmType) {
        String account = "";
        User authUser = BasicInfoUtil.getAuthUser();
        if (Objects.nonNull(authUser) && !StringUtil.isNullOrEmpty(authUser.getAccount())) {
            account = BasicInfoUtil.getAuthUser().getAccount();
        }
        // 分区id
        List<ResPool> resPools = resVmType.getIds();
        resVmTypeMapper.updateByPrimaryKeySelective(resVmType);
        List<ResPoolResource> resourceSid = this.resPoolResourceMapper.selectByParams(
                new Criteria("resourceSid", resVmType.getId()));
        // 循环判定，看到底是增加了还是减少了
        if (!CollectionUtils.isEmpty(resourceSid)) {
            for (ResPoolResource resPoolResource : resourceSid) {
                boolean opFlag = false;
                if (!CollectionUtils.isEmpty(resPools)) {
                    for (ResPool resPool : resPools) {
                        if (!StringUtil.isNullOrEmpty(resPoolResource.getResPoolId())) {
                            if (resPoolResource.getResPoolId().equals(resPool.getId())) {
                                resPools.remove(resPool);
                                opFlag = true;
                                break;
                            }
                        } else {
                            opFlag = true;
                        }
                    }
                    if (!opFlag) {
                        this.resPoolResourceMapper.deleteByPrimaryKey(resPoolResource.getId());
                    }
                } else {
                    this.resPoolResourceMapper.deleteByPrimaryKey(resPoolResource.getId());
                }
            }
        }
        if (!CollectionUtils.isEmpty(resPools)) {
            List<ResVmType> resVmTypeList = new ArrayList<>();
            for (ResPool resPool : resPools) {
                ResPoolResource resPoolResource = new ResPoolResource();
                resPoolResource.setResPoolId(resPool.getId());
                resPoolResource.setResourceSid(resVmType.getId().toString());
                resPoolResource.setResourceType("RES_VOLUME_TYPE");
                BasicWebUtil.prepareInsertParams(resPoolResource, account);
                this.resPoolResourceMapper.insertSelective(resPoolResource);
                ResVmType resVmType1 = new ResVmType();
                BeanUtils.copyProperties(resVmType, resVmType1);
                resVmType1.setZone(resPool.getName());
                resVmTypeList.add(resVmType1);
            }

        }
        return true;
    }

    /**
     * Update vm type boolean.
     *
     * @param resVmType the res vm type
     * @return the boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateVmType(ResVmType resVmType, Criteria criteria) {
        String account = "";
        User authUser = BasicInfoUtil.getAuthUser();
        if (Objects.nonNull(authUser) && !StringUtil.isNullOrEmpty(authUser.getAccount())) {
            account = BasicInfoUtil.getAuthUser().getAccount();
        }
        // 分区id
        List<ResPool> resPools = resVmType.getIds();

        if (Objects.nonNull(criteria.get("updateType"))) {
            resVmTypeMapper.updateByPrimaryKeySelective(resVmType);
        } else {
            // 将 share_type_id 设置为 null
            resVmTypeMapper.updateShareTypeIdByPrimaryKey(resVmType);
        }
        List<ResPoolResource> resourceSid = this.resPoolResourceMapper.selectByParams(
                new Criteria("resourceSid", resVmType.getId()));
        // 循环判定，看到底是增加了还是减少了
        if (!CollectionUtils.isEmpty(resourceSid)) {
            for (ResPoolResource resPoolResource : resourceSid) {
                boolean opFlag = false;
                if (!CollectionUtils.isEmpty(resPools)) {
                    for (ResPool resPool : resPools) {
                        if (!StringUtil.isNullOrEmpty(resPoolResource.getResPoolId())) {
                            if (resPoolResource.getResPoolId().equals(resPool.getId())) {
                                resPools.remove(resPool);
                                opFlag = true;
                                break;
                            }
                        } else {
                            opFlag = true;
                        }
                    }
                    if (!opFlag) {
                        this.resPoolResourceMapper.deleteByPrimaryKey(resPoolResource.getId());
                    }
                } else {
                    this.resPoolResourceMapper.deleteByPrimaryKey(resPoolResource.getId());
                }
            }
        }
        if (!CollectionUtils.isEmpty(resPools)) {
            List<ResVmType> resVmTypeList = new ArrayList<>();
            for (ResPool resPool : resPools) {
                ResPoolResource resPoolResource = new ResPoolResource();
                resPoolResource.setResPoolId(resPool.getId());
                resPoolResource.setResourceSid(resVmType.getId().toString());
                resPoolResource.setResourceType("RES_VOLUME_TYPE");
                BasicWebUtil.prepareInsertParams(resPoolResource, account);
                this.resPoolResourceMapper.insertSelective(resPoolResource);
                ResVmType resVmType1 = new ResVmType();
                BeanUtils.copyProperties(resVmType, resVmType1);
                resVmType1.setZone(resPool.getName());
                resVmTypeList.add(resVmType1);
            }

        }
        return true;
    }

    /**
     * Delete vm type boolean.
     *
     * @param id the id
     * @return the boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteVmType(Long id) {
        this.resPoolResourceMapper.deleteByParams(new Criteria("resourceSid", id.toString()));
        int i = resVmTypeMapper.deleteByPrimaryKey(id);
        return i > 0;
    }

    /**
     * Remove vm type boolean.
     *
     * @param ids the ids
     * @return the boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeVmType(List<Long> ids) {
        if (!CollectionUtils.isEmpty(ids)) {
            ids.forEach(id -> {
                this.resPoolResourceMapper.deleteByParams(new Criteria("resourceSid", id.toString()));
                resVmTypeMapper.deleteByPrimaryKey(id);
            });
            return true;
        } else {
            return false;
        }
    }

    /**
     * Delete volume boolean.
     *
     * @param resVdSid the res vd sid
     *
     * @return the boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteVolume(String resVdSid, boolean isSysAuto, boolean isFormat, boolean detachFirst) {

        ResVd resVd = resVdMapper.selectByPrimaryKey(resVdSid);
        if (resVd == null) {
            ServerMsgPublisher.sendMsgToResourceType(ServerMsgType.VD.getTypeFamily(), resVdSid);
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1160765990));
        }
        String originalStatus = resVd.getStatus();
        CloudEnv cloudEnv = cloudEnvMapper.selectByPrimaryKey(Long.valueOf(resVd.getCloudEnvId()));
        if (Objects.isNull(cloudEnv)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_832756465));
        }

        // 检测是否挂载有快照
        if (!isSysAuto) {
            checkVmCanBeRemove(resVd.getResVdSid());
        }

        // 更新状态为正在删除中
        if (!Strings.isNullOrEmpty(resVd.getUuid()) || !Strings.isNullOrEmpty(resVd.getResVmId())) {
            resVd.setStatus(ResVdStatus.DELETING);
            resVdMapper.updateByPrimaryKeySelective(resVd);
        }
        DiskDelete diskDelete = CloudClientFactory.buildMQBean(resVd.getCloudEnvId(), DiskDelete.class);
        ResVm resVm = resVmService.selectByPrimaryKey(resVd.getResVmId());
        if (Objects.nonNull(resVm)) {
            diskDelete.setVmId(resVm.getInstanceId());
            diskDelete.setVmName(resVm.getInstanceName());
        }
        if (CloudEnvType.OPEN_STACK.equals(diskDelete.getProviderType()) || CloudEnvType.CLOUDOS.equals(
                diskDelete.getProviderType())) {
            diskDelete.setId(resVd.getUuid());
            diskDelete.setRegion(cloudEnv.getRegion());
            List<ResSnapshot> snapshots = resSnapshotMapper.selectByParams(new Criteria("resVdId", resVdSid));
            diskDelete.setSnapshotIds(snapshots.stream().map(ResSnapshot::getUuid).collect(Collectors.toList()));
            // 自服务的先进行卸载
            if (Objects.nonNull(resVm) && Objects.nonNull(resVd.getServiceDeployInstId())) {
                diskDelete.setVmId(resVm.getInstanceId());
                diskDelete.setVmName(resVm.getInstanceName());
            }
        } else if (CloudEnvType.POWER_VC.equals(diskDelete.getProviderType())) {
            diskDelete.setId(resVd.getUuid());
        } else if (CloudEnvType.ALIYUN.equals(diskDelete.getProviderType()) || CloudEnvType.AWS.equals(
                diskDelete.getProviderType()) || CloudEnvType.QCLOUD.equals(diskDelete.getProviderType())
                || CloudEnvType.AZURE.equals(diskDelete.getProviderType())) {
            diskDelete.setId(resVd.getUuid());
            diskDelete.setRegion(cloudEnv.getRegion());
        } else if (CloudEnvType.VMWARE.equals(diskDelete.getProviderType())) {
            diskDelete.setId(resVd.getVdName());
            diskDelete.setDataStore(resVd.getDeviceName());
            diskDelete.setDiskPath(resVd.getLogicVolume());

            if (Objects.nonNull(resVm)) {
                diskDelete.setVmId(resVm.getInstanceId());
                diskDelete.setVmName(resVm.getInstanceName());
            }
        } else if (CloudEnvType.HUAWEICLOUD.equals(diskDelete.getProviderType())) {
            diskDelete.setId(resVd.getUuid());
            diskDelete.setRegion(cloudEnv.getRegion());
            List<ResSnapshot> snapshots = resSnapshotMapper.selectByParams(new Criteria("resVdId", resVdSid));
            diskDelete.setSnapshotIds(snapshots.stream().map(ResSnapshot::getUuid).collect(Collectors.toList()));
        } else if (CloudEnvType.AZURE.equals(diskDelete.getProviderType())) {
            diskDelete.setId(resVd.getUuid());
        } else if (CloudEnvType.FUSIONCOMPUTE.equals(diskDelete.getProviderType())) {
            diskDelete.setId(resVd.getUri());
            diskDelete.setFormat(isFormat);
            if (null != resVm) {
                diskDelete.setVmId(resVm.getUri());
            }
        } else if (CloudEnvType.KSYUN.equals(diskDelete.getProviderType()) || CloudEnvType.KING_STACK.equals(
                diskDelete.getProviderType())) {
            diskDelete.setId(resVd.getUuid());
        }
        // 自服务创建数据 或 过期资源处理，先释放再删除
        diskDelete.setDetachFirst(detachFirst || Objects.nonNull(resVd.getServiceDeployInstId()));

        //创建失败的直接删除,不用发送mq。
        if (StringUtil.isNullOrEmpty(resVd.getUuid()) && StringUtil.isNullOrEmpty(resVd.getResVmId())) {
            resVdMapper.deleteByPrimaryKey(resVdSid);
            return true;
        } else {
            diskDelete.setOptions(ImmutableMap.of("resVdSid", resVdSid, "originalStatus", originalStatus));
            ResInstResult resInstResult = sendToMQ(diskDelete);

            return resInstResult.getStatus();
        }
    }

    @Override
    public void checkVmCanBeRemove(String resVdSid) {
        ResVd resVd = resVdMapper.selectByPrimaryKey(resVdSid);
        Criteria condition = new Criteria();
        condition.put("cloudEnvId", resVd.getCloudEnvId());
        condition.put("resVdId", resVd.getResVdSid());
        condition.put("statusNotIn", Lists.newArrayList(SnapshotStatus.DELETED));
        int snapCount = this.resSnapshotMapper.countByParams(condition);
        if (snapCount > 0) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_667021700) + snapCount + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1678834046));
        }
    }

    @Override
    public RestResult renewInstance(RenewBasicResourceDTO renewBasicResourceDTO, AuthUser authUser) {
        RestResult restResult = null;
        switch (renewBasicResourceDTO.getResourceType()) {
            case CloudDeploymentType.BMS:
            case CloudDeploymentType.INFRA:
                RenewInstanceVo renewInstanceVo = new RenewInstanceVo();
                renewInstanceVo.setPeriod(renewBasicResourceDTO.getPeriod());
                renewInstanceVo.setPeriodUnit("Month");
                renewInstanceVo.setHostId(renewBasicResourceDTO.getResourceId());
                restResult = vmTypeChangeService.renewInstance(renewInstanceVo);
                break;
            case CloudDeploymentType.DISK:
                restResult = this.resVdService.renewInstance(renewBasicResourceDTO, authUser);
                break;
            case CloudDeploymentType.FLOATING_IP:
                restResult = this.resFloatingIpService.renewInstance(renewBasicResourceDTO, authUser);
                break;
            case CloudDeploymentType.VBS:
                restResult = this.resSnapshotService.renewInstance(renewBasicResourceDTO, authUser);
                break;
            case CloudDeploymentType.SLB:
                restResult = this.resLoadBalanceService.renewInstance(renewBasicResourceDTO, authUser);
                break;
            case CloudDeploymentType.OBS:
                restResult = this.resBucketService.renewInstance(renewBasicResourceDTO, authUser);
                break;
            case CloudDeploymentType.RDS:
                restResult = this.resRdsService.renewInstance(renewBasicResourceDTO, authUser);
                break;
            case CloudDeploymentType.SFS:
                restResult = this.shareService.renewInstance(renewBasicResourceDTO, authUser);
                break;
            case CloudDeploymentType.DCS:
                restResult = this.resDcsService.renewInstance(renewBasicResourceDTO, authUser);
                break;
            case CloudDeploymentType.DMS:
                restResult = this.resMqService.renewInstance(renewBasicResourceDTO, authUser);
                break;
        }
        return restResult;
    }

    @Override
    public int updateQuota(Criteria criteria) {
        return resVmTypeMapper.updateQuota(criteria);
    }

    @Override
    public int updateQuota(ResVmType resVmType) {
        return resVmTypeMapper.updateByPrimaryKeySelective(resVmType);
    }

    @Override
    public int updateStorage(Criteria criteria) {
        return resVmTypeMapper.updateStorage(criteria);
    }

    @Override
    public void updateVmLinkWithPool(Long resPoolId, String oldName) {
        ResPool resPool = resPoolService.selectByPrimaryKey(resPoolId);
        if (StringUtil.isNullOrEmpty(oldName)) {
            oldName = resPool.getName();
        }
        Criteria criteria = new Criteria();
        criteria.put("cloudEnvId", resPool.getCloudEnvId());
        criteria.put("zone", oldName);

        List<ResVm> resVms = resVmMapper.selectBaseByExample(criteria);
        List<String> hostIds = resVms.stream().map(ResVm::getId).collect(Collectors.toList());

        // 查询与分区关联的计算资源
        ComputeRes computeRes = computeResMapper.selectByResPoolId(resPoolId.toString());
        if (Objects.nonNull(computeRes)) {
            List<String> resHostIds = new ArrayList<>();
            if (!ComputeResType.HOST.equals(computeRes.getType())) {
                List<ResHost> resHosts = resHostMapper.selectByResVcSidAndEnvId(computeRes.getClusterId(),
                                                                                resPool.getCloudEnvId().toString());
                resHostIds.addAll(resHosts.stream().map(ResHost::getResHostSid).collect(Collectors.toList()));
            } else {
                resHostIds.addAll(Arrays.asList(computeRes.getHostIds().split(",")));
            }
            if (CollectionUtils.isEmpty(resHostIds)) {
                resHostIds.add("-999L");
            }
            criteria.clear();
            criteria.put("cloudEnvId", resPool.getCloudEnvId());
            criteria.put("allocateResHostSidIn", resHostIds);
            List<ResVm> allocResVms = resVmMapper.selectBaseByExample(criteria);
            List<String> allocHostIds = allocResVms.stream()
                                                   // 去掉因计算资源调整后，不与当前分区关联的实例
                                                   // 1.与当前分区所关联的计算资源关联 2.之前已经和当前分区关联
                                                   .filter(t -> hostIds.contains(t.getId()))
                                                   .map(ResVm::getId)
                                                   .collect(Collectors.toList());

            // 将与计算资源关联的实例，更新为修改后的分区名称
            if (!CollectionUtils.isEmpty(allocHostIds)) {
                resVmMapper.mutiUpdateCloudHostZone(hostIds, resPool.getName());
            }
            // 将更新前与计算资源关联的实例的分区更新为空
            hostIds.removeAll(allocHostIds);
        }

        if (!CollectionUtils.isEmpty(hostIds)) {
            resVmMapper.mutiUpdateCloudHostZone(hostIds, null);
        }
    }

    private Map<String, Long> getVdAllocStorageTypeMap(Long cloudEnvId) {
        Map<String, Long> vdAllocStorageTypeMap = new HashMap<>();
        Criteria criteria = new Criteria();
        criteria.put(Constants.PARENT_TOPOLOGY_SID, cloudEnvId);
        criteria.put(Constants.CLOUD_ENV_ID, cloudEnvId);

        List<ResVolumeType> resVolumeTypes = resVolumeTypeService.selectByParams(criteria);
        resVolumeTypes.stream()
                      .filter(Objects::nonNull)
                      .forEach(resVolumeType -> Optional.ofNullable(resVolumeType.getResStorages())
                                                        .orElse(Lists.newArrayList())
                                                        .stream()
                                                        .filter(Objects::nonNull)
                                                        .forEach(storage -> vdAllocStorageTypeMap.put(storage.getId(),
                                                                                                      resVolumeType.getId())));

        return vdAllocStorageTypeMap;
    }

    @Override
    public List<DBInstanceType> getInstanceTypes(String cloudEnvType) {
        try {
            if (CloudEnvType.AWS.equals(cloudEnvType)) {
                return JsonUtil.fromJson(
                        ClassLoaderUtil.getResourceAsStream("DBInstanceClass.json", ResourceServiceImpl.class),
                        new TypeReference<List<DBInstanceType>>() {
                        });
            }

        } catch (Exception e) {
            logger.error(e.getMessage());
        }

        return new ArrayList<>();
    }

    @Override
    public boolean updateVolumeAttribute(String resVdSid, String volumeName, String description, String releaseMode) {
        ResVd resVd = resVdMapper.selectByPrimaryKey(resVdSid);
        DiskUpdate diskUpdate = CloudClientFactory.buildMQBean(resVd.getCloudEnvId(), DiskUpdate.class);
        diskUpdate.setId(resVd.getUuid());
        diskUpdate.setDescription(description);
        diskUpdate.setName(volumeName);
        diskUpdate.setDeleteWithInstance(ReleaseMode.WITH_INSTANCE.equals(releaseMode) ? Boolean.TRUE : (
                ReleaseMode.STAND_ALONE.equals(releaseMode) ? Boolean.FALSE : null));
        DiskUpdateResult diskUpdateResult;
        try {
            diskUpdateResult = (DiskUpdateResult) MQHelper.rpc(diskUpdate);
        } catch (MQException e) {
            logger.error(e.getMessage(), e);
            throw new BizException(e.getMessage());
        }
        if (diskUpdateResult.isSuccess()) {
            resVd.setDesc(description);
            resVd.setVdName(volumeName);
            resVd.setReleaseMode(StrUtil.nullToEmpty(releaseMode));
            resVdMapper.updateByPrimaryKeySelective(resVd);
            return true;
        } else {
            return false;
        }
    }

    @Override
    public boolean updateVolumeAttribute(ResVd resVd) {
        ResVd dbResVd = resVdMapper.selectByPrimaryKey(resVd.getResVdSid());
        AssertUtil.requireNonBlank(dbResVd, "该硬盘不存在,请刷新后重新");

        CloudEnv cloudEnv = cloudEnvService.checkCloudEnvNormal(dbResVd.getCloudEnvId(), true);
        DiskUpdate diskUpdate = CloudClientFactory.buildMQBean(resVd.getCloudEnvId(), DiskUpdate.class);
        diskUpdate.setId(dbResVd.getUuid());
        diskUpdate.setDescription(resVd.getDescription());
        diskUpdate.setName(resVd.getVdName());
        diskUpdate.setIndep(resVd.getIndep());
        diskUpdate.setPersistent(resVd.getPersistent());
        if (CloudEnvType.FUSIONCOMPUTE.equals(dbResVd.getCloudEnvType())) {
            diskUpdate.setId(dbResVd.getUri());
        }
        String releaseMode = resVd.getReleaseMode();
        // 腾讯云系统盘不支持修改释放模式
        if (!("01".equals(dbResVd.getStoragePurpose()) && CloudEnvType.QCLOUD.equals(cloudEnv.getCloudEnvType()))) {
            diskUpdate.setDeleteWithInstance(ReleaseMode.WITH_INSTANCE.equals(releaseMode) ? Boolean.TRUE : (
                    ReleaseMode.STAND_ALONE.equals(releaseMode) ? Boolean.FALSE : null));
        }
        DiskUpdateResult diskUpdateResult;
        try {
            diskUpdateResult = (DiskUpdateResult) MQHelper.rpc(diskUpdate);
        } catch (MQException e) {
            logger.error(e.getMessage(), e);
            throw new BizException(e.getMessage());
        }
        if (diskUpdateResult.isSuccess()) {
            resVd.setDesc(resVd.getDescription());
            resVd.setReleaseMode(StrUtil.nullToEmpty(releaseMode));
            resVdMapper.updateByPrimaryKeySelective(resVd);
            return true;
        } else {
            String errMsg = diskUpdateResult.getErrMsg();
            if (StrUtil.isNotEmpty(errMsg)) {
                throw new BizException(errMsg);
            } else {
                return false;
            }
        }
    }

    /**
     * Map<实例类型, 硬盘>
     *
     * @param envId
     * @param zone
     * @param osType
     * @return
     */
    @Override
    public Map<String, DiskRelate> getKsyunRelatedDisks(Long envId, String zone, String osType) {
        Criteria param = new Criteria();
        param.put("envId", -1L);
        param.put("cloudEnvType", CloudEnvType.KSYUN);
        param.put("zone", zone);
        List<ResVmType> resVmTypes = resVmTypeMapper.selectByParams(param);
        Map<String, DiskRelate> result = new HashMap<>();
        for (ResVmType vmType : resVmTypes) {
            String familyCode = vmType.getName().split("\\.")[0];
            DiskRelate diskRelate = new DiskRelate();
            DiskRelate.Disk systemDisk = new DiskRelate.Disk();
            DiskRelate.Disk localDisk = new DiskRelate.Disk();
            List<DiskRelate.Disk> cloudDataDisks = new ArrayList<>();

            int systemDiskMin;
            int systemDiskMax;
            if ("windows".equalsIgnoreCase(osType)) {
                systemDiskMin = 50;
                systemDiskMax = 500;
            } else {
                systemDiskMin = 20;
                systemDiskMax = 500;
            }
            // 判断系统盘
            if ("通用型N2".equalsIgnoreCase(vmType.getFamilyName()) || "通用型N3".equalsIgnoreCase(vmType.getFamilyName())) {
                systemDisk.setDiskTypeName("SSD云硬盘3.0");
                systemDisk.setDiskMin(systemDiskMin);
                systemDisk.setDiskMax(systemDiskMax);
                systemDisk.setDiskType("SSD3.0");
            } else {
                systemDisk.setDiskTypeName("本地SSD");
                systemDisk.setDiskMin(systemDiskMin);
                systemDisk.setDiskMax(systemDiskMin);
                systemDisk.setDiskType("SSD");
            }
            diskRelate.setSystemDisk(systemDisk);

            // 判断本地磁盘和云盘
            if ("通用型N2".equalsIgnoreCase(vmType.getFamilyName())) {
                DiskRelate.Disk cloudDisk = new DiskRelate.Disk();
                cloudDisk.setDiskTypeName("云硬盘3.0(SSD)");
                cloudDisk.setDiskType("SSD3.0");
                cloudDisk.setDiskMin(10);
                cloudDisk.setDiskMax(64000);
                diskRelate.setHasCloudDataDisk(true);
                diskRelate.setHasLocalDataDisk(false);
                diskRelate.setCloudDataDisks(cloudDataDisks);
            } else if ("通用型N1".equalsIgnoreCase(vmType.getFamilyName()) || "IO优化型I2".equalsIgnoreCase(
                    vmType.getFamilyName())) {
                Map<String, String> typeMap = ImmutableMap.of("SSD2.0", "云硬盘2.0(SSD)", "SATA2.0", "云硬盘2.0(SATA)");
                for (String diskType : typeMap.keySet()) {
                    DiskRelate.Disk cloudDisk = new DiskRelate.Disk();
                    cloudDisk.setDiskTypeName(typeMap.get(diskType));
                    cloudDisk.setDiskType(diskType);
                    cloudDisk.setDiskMin(10);
                    cloudDisk.setDiskMax(16000);
                    cloudDataDisks.add(cloudDisk);
                }
                diskRelate.setHasCloudDataDisk(true);
                diskRelate.setHasLocalDataDisk(false);
                diskRelate.setCloudDataDisks(cloudDataDisks);
            } else {
                localDisk.setDiskTypeName("本地SSD");
                localDisk.setDiskType("SSD");
                localDisk.setDiskMin(vmType.getDataDiskMin() == null ? 0 : vmType.getDataDiskMin());
                localDisk.setDiskMax(vmType.getDataDiskMax() == null ? 50 : vmType.getDataDiskMax());
                diskRelate.setHasCloudDataDisk(false);
                diskRelate.setHasLocalDataDisk(true);
                diskRelate.setLocalDataDisk(localDisk);
            }

            result.put(familyCode, diskRelate);
        }
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean extendVolume(String resVdSid, Integer newSize) {
        ResVd resVd = resVdMapper.selectByPrimaryKey(resVdSid);
        if (!CloudEnvType.OPEN_STACK.equals(resVd.getCloudEnvType()) && !CloudEnvType.FUSIONCOMPUTE.equals(
                resVd.getCloudEnvType()) && !CloudEnvType.HCSO.equals(resVd.getCloudEnvType())
                && !CloudEnvType.KSYUN.equals(resVd.getCloudEnvType())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_40502799));
        }
        // 设置当前状态为扩容中
        ResVd resVdUpdate = new ResVd();
        resVdUpdate.setResVdSid(resVdSid);
        resVdUpdate.setStatus(ResVdStatus.EXTENDING);
        resVdMapper.updateByPrimaryKeySelective(resVdUpdate);
        DiskExtend diskExtend = CloudClientFactory.buildMQBean(resVd.getCloudEnvId(), DiskExtend.class);
        diskExtend.setResVdSid(resVdSid);
        diskExtend.setVolumeId(resVd.getUuid());
        diskExtend.setNewSize(newSize);
        if (CloudEnvType.FUSIONCOMPUTE.equals(resVd.getCloudEnvType())) {
            diskExtend.setVolumeId(resVd.getUri());
            if (Objects.nonNull(resVd.getResVmId())) {
                ResVm resVm = resVmMapper.selectByPrimaryKey(resVd.getResVmId());
                diskExtend.setVmUri(Optional.ofNullable(resVm).map(ResVm::getUri).orElse(null));
                diskExtend.setVolumeId(resVd.getUrn());
            }
        }
        //运营特殊逻辑
        if (ReqSource.CLOUD_BOSS.name().equals(diskExtend.getReqSource())) {
            if (CloudEnvType.VMWARE.equals(diskExtend.getProviderType())) {
                ResVm resVm = resVmMapper.selectByPrimaryKey(resVd.getResVmId());
                diskExtend.setLocation(resVd.getDeviceName());
                diskExtend.setServerId(resVm.getInstanceId());
            }
        }

        ResInstResult resInstResult = sendToMQ(diskExtend);
        return resInstResult.getStatus();
    }

    /**
     * The type Constants.
     */
    public static class Constants {

        /**
         * The Cloud env id.
         */
        static String CLOUD_ENV_ID = "cloudEnvId";
        /**
         * The Parent topology sid.
         */
        static String PARENT_TOPOLOGY_SID = "parentTopologySid";
        /**
         * The Res vs sid.
         */
        static String RES_VS_SID = "resVsSid";
        /**
         * The Egress.
         */
        static String EGRESS = "egress";
        /**
         * The Ingress.
         */
        static String INGRESS = "ingress";
        /**
         * The External.
         */
        static String EXTERNAL = "external";
        /**
         * The One.
         */
        static String ONE = "1";
        /**
         * The Two.
         */
        static String TWO = "2";
    }
}
