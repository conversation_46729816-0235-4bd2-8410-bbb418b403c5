/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.listener;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;

import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.ShareCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.ShareDeleteResult;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResShare;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResVpc;
import cn.com.cloudstar.rightcloud.common.constants.res.status.ShareRuleStatus;
import cn.com.cloudstar.rightcloud.common.constants.res.status.ShareStatus;
import cn.com.cloudstar.rightcloud.common.constants.res.type.CloudEnvType;
import cn.com.cloudstar.rightcloud.common.enums.resource.ResourceOperateEnum;
import cn.com.cloudstar.rightcloud.common.enums.resource.ResourceTypeEnum;
import cn.com.cloudstar.rightcloud.common.util.StringUtil;
import cn.com.cloudstar.rightcloud.common.util.json.JsonUtil;
import cn.com.cloudstar.rightcloud.common.websocket.annonation.Message;
import cn.com.cloudstar.rightcloud.common.websocket.annonation.MessageParam;
import cn.com.cloudstar.rightcloud.common.websocket.support.OperateEnum;
import cn.com.cloudstar.rightcloud.common.websocket.support.ServerMsgType;
import cn.com.cloudstar.rightcloud.core.annotation.log.LogMethod;
import cn.com.cloudstar.rightcloud.core.annotation.log.LogParam;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResShareMountTarget;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResShareRule;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicWebUtil;
import cn.com.cloudstar.rightcloud.resource.dao.network.ResVpcMapper;
import cn.com.cloudstar.rightcloud.resource.dao.share.ResShareMapper;
import cn.com.cloudstar.rightcloud.resource.dao.share.ResShareMountTargetMapper;
import cn.com.cloudstar.rightcloud.resource.dao.share.ResShareRightsGroupMapper;
import cn.com.cloudstar.rightcloud.resource.dao.share.ResShareRuleMapper;
import cn.com.cloudstar.rightcloud.resource.dao.share.ResShareTargetMapper;
import cn.com.cloudstar.rightcloud.resource.notify.BizNotify;

@Component
public class ShareResultListener {

    private final Logger logger = LoggerFactory.getLogger(ShareResultListener.class);

    @Autowired
    private ResShareMapper resShareMapper;

    @Autowired
    private ResShareRuleMapper resShareRuleMapper;

    @Autowired
    private ResShareTargetMapper resShareTargetMapper;

    @Autowired
    private ResVpcMapper resVpcMapper;

    @Autowired
    private ResShareMountTargetMapper resShareMountTargetMapper;

    @Autowired
    private ResShareRightsGroupMapper shareRightsGroupMapper;

    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#shareDeleteResult.id", resourceType = ResourceTypeEnum.SHARE, opUser = "#shareDeleteResult.opUser", operate = ResourceOperateEnum.DELETE, success = "#shareDeleteResult.success", orgSid = "#shareDeleteResult.orgSid")
    @Message(refKey = "#shareDeleteResult.id", envId = "#shareDeleteResult.cloudEnvId", msgType = ServerMsgType.SHARE, opUser = "#shareDeleteResult.opUser", operate = OperateEnum.DELETE, success = "#shareDeleteResult.success", refNameKey = "#shareDeleteResult.name", errorMsg = "#shareDeleteResult.errMsg")
    @BizNotify
    public void handleMessage(
            @LogParam("shareDeleteResult") @MessageParam("shareDeleteResult") ShareDeleteResult shareDeleteResult) {
        try {
            ResShare resShare = resShareMapper.selectByPrimaryKey(shareDeleteResult.getId());
            if (shareDeleteResult.isSuccess()) {
                resShareMapper.logicDeleteShareById((shareDeleteResult.getId()));
                Criteria criteria = new Criteria();
                criteria.put("shareId", shareDeleteResult.getId());
                resShareRuleMapper.deleteShareRule(criteria);
                resShareTargetMapper.deleteByParams(criteria);
                List<ResShareMountTarget> shareMountTargets = resShareMountTargetMapper.selectByParams(criteria);
                resShareMountTargetMapper.deleteByParams(criteria);

                // 修改权限挂载数量
                if (CollectionUtil.isNotEmpty(shareMountTargets)) {
                    shareMountTargets.stream()
                                     .filter(t -> StringUtil.isNotBlank(t.getAccessGroup()))
                                     .forEach(target -> {
                                         logger.info("弹性文件服务权限组-绑定文件系统数[减少↓]，云环境:{}, 权限组id:{}",
                                                     shareDeleteResult.getCloudEnvId(), target.getAccessGroup());
                                         Criteria c = new Criteria();
                                         c.put("cloudEnvId", shareDeleteResult.getCloudEnvId());
                                         c.put("uuid", target.getAccessGroup());
                                         shareRightsGroupMapper.decreaseMountTargetCount(c);
                                     });
                }
            } else {
                resShare.setStatus(ShareStatus.AVAILABLE);
                resShareMapper.updateByPrimaryKey(resShare);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#shareCreateResult.id", resourceType = ResourceTypeEnum.SHARE, opUser = "#shareCreateResult.opUser", operate = ResourceOperateEnum.CREATE, success = "#shareCreateResult.success", orgSid = "#shareCreateResult.orgSid")
    @Message(refKey = "#shareCreateResult.id", envId = "#shareCreateResult.cloudEnvId", msgType = ServerMsgType.SHARE, opUser = "#shareCreateResult.opUser", operate = OperateEnum.CREATE, success = "#shareCreateResult.success", refNameKey = "#shareCreateResult.name", errorMsg = "#shareCreateResult.errMsg")
    @BizNotify
    public void handleMessage(
            @LogParam("shareCreateResult") @MessageParam("shareCreateResult") ShareCreateResult shareCreateResult) {
        logger.info("创建弹性文件回调 | 回调参数 ： {}", JsonUtil.toJson(shareCreateResult));
        try {
            ResShare resShare = resShareMapper.selectByPrimaryKey(shareCreateResult.getId());
            String ipAddress = null;
            if (shareCreateResult.isSuccess()) {
                BasicWebUtil.prepareUpdateParams(resShare);
                BeanUtils.copyProperties(shareCreateResult, resShare);
                if (CloudEnvType.ALIYUN.equals(shareCreateResult.getProviderType())) {
                    // 阿里云使用描述作为文件存储的名称
                    if (Objects.nonNull(shareCreateResult.getDescription())) {
                        resShare.setName(shareCreateResult.getDescription());
                    }
                }

                if (StringUtils.isNotEmpty(shareCreateResult.getRuleUuid())) {
                    ResVpc resVpc = resVpcMapper.selectByPrimaryKey(shareCreateResult.getResVpcId());
                    if (null != resVpc) {
                        ResShareRule resShareRule = new ResShareRule();
                        BasicWebUtil.prepareInsertParams(resShareRule, resShare.getCreatedBy());
                        resShareRule.setOrgSid(resShare.getOrgSid());
                        resShareRule.setName(resVpc.getName());
                        resShareRule.setShareId(shareCreateResult.getId());
                        resShareRule.setVpcResId(resVpc.getId().toString());
                        resShareRule.setAccessType("cert");
                        resShareRule.setIpAddress("0.0.0.0/0");
                        resShareRule.setPriority(0);
                        if (StringUtils.equalsIgnoreCase("NFS", shareCreateResult.getShareProto())) {
                            resShareRule.setAccessTo(resVpc.getUuid() + "#0.0.0.0/0#0#all_squash,root_squash");
                            resShareRule.setUserRootAccess("root_squash");
                            resShareRule.setUserAccess("all_squash");
                        } else {
                            resShareRule.setAccessTo(resVpc.getUuid() + "#0.0.0.0/0#0");
                        }
                        resShareRule.setAccessLevel("rw");
                        resShareRule.setUuid(shareCreateResult.getRuleUuid());
                        resShareRule.setState(ShareRuleStatus.ACTIVE);
                        resShareRuleMapper.insertSelective(resShareRule);
                    }
                }
                if ("user".equals(shareCreateResult.getAccessType())) {
                    ResShareRule resShareRule = new ResShareRule();
                    BasicWebUtil.prepareInsertParams(resShareRule, resShare.getCreatedBy());
                    resShareRule.setOrgSid(resShare.getOrgSid());
                    resShareRule.setName("用户共享");
                    resShareRule.setShareId(shareCreateResult.getId());
                    resShareRule.setAccessType("user");
                    resShareRule.setPriority(0);
                    resShareRule.setAccessTo(shareCreateResult.getAccessTo());
                    resShareRule.setAccessLevel("rw");
                    resShareRule.setUuid(shareCreateResult.getRuleUuid());
                    resShareRule.setState(ShareRuleStatus.ACTIVE);
                    resShareRuleMapper.insertSelective(resShareRule);
                }

                if (Objects.nonNull(shareCreateResult.getShareMountTarget())) {
                    ResShareMountTarget resShareMountTarget = new ResShareMountTarget();
                    BasicWebUtil.prepareInsertParams(resShareMountTarget, resShare.getCreatedBy());
                    resShareMountTarget.setOrgSid(resShare.getOrgSid());
                    cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(shareCreateResult.getShareMountTarget());
                    resShareMountTarget.setMountUuid(jsonObject.getStr("mountTargetId"));
                    resShareMountTarget.setShareId(shareCreateResult.getId());
                    resShareMountTarget.setAccessGroup(shareCreateResult.getGroupUuid());
                    resShareMountTarget.setNetworkType(jsonObject.getStr("networkInterface"));
                    ipAddress = jsonObject.getStr("ipAddress");
                    resShareMountTarget.setMountTargetDomain(ipAddress);
                    resShareMountTarget.setMountTargetStatus(jsonObject.getStr("lifeCycleState"));
                    resShareMountTarget.setVpcInfo(jsonObject.getStr("vpcId"));
                    String vswInfo = null;
                    if (StrUtil.isNotEmpty(resShareMountTarget.getVpcInfo())) {
                        vswInfo = jsonObject.getStr("vpcName") + "(" + jsonObject.getStr("vpcId") + ")-"
                                + jsonObject.getStr("subnetName") + "(" + jsonObject.getStr("subnetId") + ")";
                    }
                    resShareMountTarget.setVswInfo(vswInfo);

                    resShareMountTargetMapper.insertSelective(resShareMountTarget);

                    // 修改权限挂载数量
                    if (StringUtil.isNotBlank(shareCreateResult.getGroupUuid())) {
                        logger.info("弹性文件服务权限组-绑定文件系统数[增加↑]，云环境:{}, 权限组id:{}", shareCreateResult.getCloudEnvId(),
                                    shareCreateResult.getGroupUuid());
                        Criteria criteria = new Criteria();
                        criteria.put("cloudEnvId", shareCreateResult.getCloudEnvId());
                        criteria.put("uuid", shareCreateResult.getGroupUuid());
                        shareRightsGroupMapper.increaseMountTargetCount(criteria);
                    }
                }
            } else {
                BasicWebUtil.prepareUpdateParams(resShare);
                resShare.setErrorMsg(shareCreateResult.getErrMsg());
                resShare.setStatus(ShareStatus.ERROR);
            }
            if (Objects.nonNull(ipAddress)) {
                resShare.setExportLocation(ipAddress + ":/");
            }
            resShareMapper.updateByPrimaryKey(resShare);
        } catch (Exception e) {
            logger.error("share回调异常", e);
        }
    }
}
