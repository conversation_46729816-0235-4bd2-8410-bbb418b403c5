/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.listener;

import com.alibaba.fastjson.JSON;

import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Objects;

import cn.com.cloudstar.rightcloud.common.schedule.bean.MessageDTO;
import cn.com.cloudstar.rightcloud.remote.api.analysis.service.BillingAnalysisRemoteService;

/**
 * 资源账单操作
 *
 * <AUTHOR>
 */
@Component
public class ResourceBillHandleListener {

    private final Logger logger = LoggerFactory.getLogger(ResourceBillHandleListener.class);

    @DubboReference
    private BillingAnalysisRemoteService billingAnalysisRemoteService;

    public void handleMessage(MessageDTO messageDTO) {
        logger.info("接收到schedule资源Bill理消息：[{}]", JSON.toJSONString(messageDTO));
        try {
            if (Objects.equals(messageDTO.getType(), "org_month_bill")) {
                // 生成组织月账单
                billingAnalysisRemoteService.calcateBillingOrg();
            } else if (Objects.equals(messageDTO.getType(), "org_month_bill_notice")) {
                logger.info("月账单消息通知");
            }
        } catch (Exception e) {
            logger.error("资源Bill失败!", e);
        }
    }

}
