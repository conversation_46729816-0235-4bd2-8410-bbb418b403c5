/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.listener;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.ClassUtils;

import cn.com.cloudstar.rightcloud.adapter.pojo.other.result.UnRegisterEnvResult;

/**
 * The type RegisterResultListener.
 * <p>
 * Created on 2017/10/25
 *
 * <AUTHOR>
 */
@Component
public class RegisterResultListener {

    private final Logger logger = LoggerFactory.getLogger(ClassUtils.getUserClass(this));

    public void handleMessage(UnRegisterEnvResult unRegisterEnvResult) {
        logger.info("Disconnect from RCLink[{}]", unRegisterEnvResult.getUuid());
    }
}
