<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2018 Cloud-Star, Inc. All Rights Reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.cloudstar.rightcloud.resource.dao.network.ResVsPortGroupMapper">
    <resultMap id="BaseResultMap"
        type="cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResVsPortGroup">
        <id column="ID" property="id" jdbcType="VARCHAR"/>
        <result column="RES_VS_SID" property="resVsSid" jdbcType="VARCHAR"/>
        <result column="NAME" property="name" jdbcType="VARCHAR"/>
        <result column="RES_VS_NAME" property="resVsName" jdbcType="VARCHAR"/>
        <result column="RES_HOST_SID" property="resHostSid" jdbcType="VARCHAR"/>
        <result column="VLAN_ID" property="vlanId" jdbcType="VARCHAR"/>
        <result column="TOTAL_PORTS" property="totalPorts" jdbcType="BIGINT"/>
        <result column="AVAILABLE_PORTS" property="availablePorts" jdbcType="BIGINT"/>
        <result column="UUID" property="uuid" jdbcType="VARCHAR"/>
        <result column="RES_VS_TYPE" property="resVsType" jdbcType="VARCHAR"/>
        <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR"/>
        <result column="CREATED_DT" property="createdDt" jdbcType="TIMESTAMP"/>
        <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="UPDATED_DT" property="updatedDt" jdbcType="TIMESTAMP"/>
        <result column="VERSION" property="version" jdbcType="BIGINT"/>
        <result column="URN" property="urn" jdbcType="VARCHAR"/>
        <result column="URI" property="uri" jdbcType="VARCHAR"/>
        <result column="cloud_env_name" property="cloudEnvName" jdbcType="VARCHAR"/>
        <result column="cloud_env_type" property="cloudEnvType" jdbcType="VARCHAR"/>
        <result column="port_ids" property="portIds" jdbcType="VARCHAR"/>
        <result column="vlan_range" property="vlanRange" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="pg_config" property="pgConfig" jdbcType="VARCHAR"/>
        <result column="port_type" property="portType"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.resVsSid != null">
                and RES_VS_SID = #{condition.resVsSid}
            </if>
            <if test="condition.name != null">
                and NAME = #{condition.name}
            </if>
            <if test="condition.vlanId != null">
                and VLAN_ID = #{condition.vlanId}
            </if>
            <if test="condition.totalPorts != null">
                and TOTAL_PORTS = #{condition.totalPorts}
            </if>
            <if test="condition.availablePorts != null">
                and AVAILABLE_PORTS = #{condition.availablePorts}
            </if>
            <if test="condition.uuid != null">
                and UUID = #{condition.uuid}
            </if>
            <if test="condition.createdBy != null">
                and CREATED_BY = #{condition.createdBy}
            </if>
            <if test="condition.createdDt != null">
                and CREATED_DT = #{condition.createdDt}
            </if>
            <if test="condition.updatedBy != null">
                and UPDATED_BY = #{condition.updatedBy}
            </if>
            <if test="condition.updatedDt != null">
                and UPDATED_DT = #{condition.updatedDt}
            </if>
            <if test="condition.version != null">
                and VERSION = #{condition.version}
            </if>
            <if test="condition.urn != null">
                and URN = #{condition.urn}
            </if>
            <if test="condition.uri != null">
                and URI = #{condition.uri}
            </if>
        </trim>
    </sql>
    <sql id="Example_Where_Clause_Alias">
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.resVsPortGroupId != null">
                and A.ID = #{condition.resVsPortGroupId}
            </if>
            <if test="condition.resVsSid != null">
                and A.RES_VS_SID = #{condition.resVsSid}
            </if>
            <if test="condition.name != null">
                and A.NAME = #{condition.name}
            </if>
            <if test="condition.portGroupNameLike != null">
                and A.NAME like concat ('%', #{condition.portGroupNameLike}, '%')
            </if>
            <if test="condition.vlanId != null">
                and A.VLAN_ID = #{condition.vlanId}
            </if>
            <if test="condition.totalPorts != null">
                and A.TOTAL_PORTS = #{condition.totalPorts}
            </if>
            <if test="condition.availablePorts != null">
                and A.AVAILABLE_PORTS = #{condition.availablePorts}
            </if>
            <if test="condition.uuid != null">
                and A.UUID = #{condition.uuid}
            </if>
            <if test="condition.createdBy != null">
                and A.CREATED_BY = #{condition.createdBy}
            </if>
            <if test="condition.createdDt != null">
                and A.CREATED_DT = #{condition.createdDt}
            </if>
            <if test="condition.updatedBy != null">
                and A.UPDATED_BY = #{condition.updatedBy}
            </if>
            <if test="condition.updatedDt != null">
                and A.UPDATED_DT = #{condition.updatedDt}
            </if>
            <if test="condition.version != null">
                and A.VERSION = #{condition.version}
            </if>
            <if test="condition.resVsSid != null">
                and B.RES_VS_SID = #{condition.resVsSid}
            </if>
            <if test="condition.resVsType != null">
                and B.RES_VS_TYPE = #{condition.resVsType}
            </if>
            <if test="condition.parentTopologySid != null">
                and B.parent_topology_sid = #{condition.parentTopologySid}
            </if>
            <if test="condition.cloudEnvIds != null">
                AND B.parent_topology_sid IN
                <foreach item="item" index="index" collection="condition.cloudEnvIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.orgSid != null">
                AND (EXISTS (SELECT 1 FROM sys_m_org WHERE (org_sid = #{condition.orgSid} OR tree_path LIKE
                CONCAT('/',#{condition.orgSid},'/%')) AND org_sid = D.org_sid))
            </if>
            <if test="condition.urn != null">
                and A.URN = #{condition.urn}
            </if>
            <if test="condition.uri != null">
                and A.URI = #{condition.uri}
            </if>
            <if test="condition.networkId != null">
                and rvpgn.network_id = #{condition.networkId}
            </if>
        </trim>
    </sql>
    <sql id="Base_Column_List">
        ID, RES_VS_SID, NAME, VLAN_ID, TOTAL_PORTS, AVAILABLE_PORTS, UUID, CREATED_BY, CREATED_DT,
        UPDATED_BY, UPDATED_DT, VERSION,urn,uri,vlan_range,`description`,pg_config,port_type
    </sql>
    <sql id="Alias_Column_List">
        A.ID, A.RES_VS_SID, A.NAME, A.VLAN_ID, A.TOTAL_PORTS, A.AVAILABLE_PORTS,
        A.UUID,A.urn,A.uri,A.vlan_range,A.`description`,A.pg_config,A.port_type
    </sql>
    <select id="selectByParams" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from res_vs_port_group
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>

    </select>
    <select id="selectByEnvId" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Alias_Column_List"/>
        from res_vs_port_group A
        INNER JOIN res_vs rv ON A.res_vs_sid = rv.res_vs_sid
        WHERE rv.parent_topology_sid = #{envId}
        ORDER BY rv.res_vs_type desc, A.name
    </select>
    <select id="selectPortsByHost" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        select
        distinct
        A.ID,
        A.RES_VS_SID,
        A.NAME,
        A.VLAN_ID,
        A.TOTAL_PORTS,
        A.AVAILABLE_PORTS,
        A.UUID,
        C.RES_HOST_SID,
        B.RES_VS_TYPE,
        B.RES_VS_NAME
        from res_vs_port_group A
        LEFT JOIN res_vs B ON (A.RES_VS_SID = B.RES_VS_SID)
        LEFT JOIN res_vs_host C ON (B.RES_VS_SID = C.RES_VS_SID)
        <if test="_parameter != null">

            <trim prefix="where" prefixOverrides="and|or">
                <if test="condition.cloudEnvId != null">
                    and B.parent_topology_sid = #{condition.cloudEnvId}
                </if>
                <if test="condition.resHostSid != null">
                    and C.RES_HOST_SID = #{condition.resHostSid}
                </if>
                <if test="condition.vlanId != null">
                    and A.VLAN_ID = #{condition.vlanId}
                </if>
                <if test="condition.name != null">
                    and A.NAME = #{condition.name}
                </if>
                <if test="condition.cloudEnvIds != null and condition.cloudEnvIds.size() > 0">
                    and B.parent_topology_sid in
                    <foreach collection="condition.cloudEnvIds" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </if>
            </trim>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>

    </select>
    <select id="selectPortsByCloudHost" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        select
        distinct
        A.ID,
        A.RES_VS_SID,
        A.NAME,
        A.VLAN_ID,
        A.TOTAL_PORTS,
        A.AVAILABLE_PORTS,
        A.UUID,
        B.RES_VS_TYPE
        from res_vs_port_group A
        LEFT JOIN res_vs B ON (A.RES_VS_SID = B.RES_VS_SID)
        LEFT JOIN res_vs_host C ON (B.RES_VS_SID = C.RES_VS_SID)
        LEFT JOIN res_vm D ON (D.allocate_res_host_sid = C.res_host_sid)
        <if test="_parameter != null">

            <trim prefix="where" prefixOverrides="and|or">
                <if test="condition.resHostSid != null">
                    and C.RES_HOST_SID = #{condition.resHostSid}
                </if>
                <if test="condition.vlanId != null">
                    and A.VLAN_ID = #{condition.vlanId}
                </if>
                <if test="condition.name != null">
                    and A.NAME = #{condition.name}
                </if>
                <if test="condition.resVmId != null">
                    and D.id = #{condition.resVmId}
                </if>
            </trim>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>

    </select>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from res_vs_port_group
        where ID = #{id}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from res_vs_port_group
        where ID = #{id}
    </delete>
    <delete id="deleteByParams" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        delete from res_vs_port_group
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert"
        parameterType="cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResVsPortGroup">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">
            SELECT UUID()
        </selectKey>
        insert into res_vs_port_group (ID, RES_VS_SID, NAME, VLAN_ID, TOTAL_PORTS, AVAILABLE_PORTS,
        UUID, CREATED_BY, CREATED_DT, UPDATED_BY, UPDATED_DT, VERSION)
        values (#{id}, #{resVsSid}, #{name}, #{vlanId}, #{totalPorts}, #{availablePorts},
        #{uuid}, #{createdBy}, #{createdDt}, #{updatedBy}, #{updatedDt}, #{version})
    </insert>
    <insert id="insertSelective"
        parameterType="cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResVsPortGroup">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">
            SELECT UUID()
        </selectKey>
        insert into res_vs_port_group
        <trim prefix="(" suffix=")" suffixOverrides=",">
            ID,
            <if test="resVsSid != null">
                RES_VS_SID,
            </if>
            <if test="name != null">
                NAME,
            </if>
            <if test="vlanId != null">
                VLAN_ID,
            </if>
            <if test="totalPorts != null">
                TOTAL_PORTS,
            </if>
            <if test="availablePorts != null">
                AVAILABLE_PORTS,
            </if>
            <if test="uuid != null">
                UUID,
            </if>
            <if test="createdBy != null">
                CREATED_BY,
            </if>
            <if test="createdDt != null">
                CREATED_DT,
            </if>
            <if test="updatedBy != null">
                UPDATED_BY,
            </if>
            <if test="updatedDt != null">
                UPDATED_DT,
            </if>
            <if test="version != null">
                VERSION,
            </if>
            <if test="urn != null">
                URN,
            </if>
            <if test="uri != null">
                URI,
            </if>
            <if test="description != null">
                `description`,
            </if>
            <if test="portType != null">
                port_type,
            </if>
            <if test="vlanRange != null">
                vlan_range,
            </if>
            <if test="pgConfig != null">
                pg_config,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{id},
            <if test="resVsSid != null">
                #{resVsSid},
            </if>
            <if test="name != null">
                #{name},
            </if>
            <if test="vlanId != null">
                #{vlanId},
            </if>
            <if test="totalPorts != null">
                #{totalPorts},
            </if>
            <if test="availablePorts != null">
                #{availablePorts},
            </if>
            <if test="uuid != null">
                #{uuid},
            </if>
            <if test="createdBy != null">
                #{createdBy},
            </if>
            <if test="createdDt != null">
                #{createdDt},
            </if>
            <if test="updatedBy != null">
                #{updatedBy},
            </if>
            <if test="updatedDt != null">
                #{updatedDt},
            </if>
            <if test="version != null">
                #{version},
            </if>
            <if test="urn != null">
                #{urn},
            </if>
            <if test="uri != null">
                #{uri},
            </if>
            <if test="description != null">
                #{description},
            </if>
            <if test="portType != null">
                #{portType},
            </if>
            <if test="vlanRange != null">
                #{vlanRange},
            </if>
            <if test="pgConfig != null">
                #{pgConfig},
            </if>
        </trim>
    </insert>
    <select id="countByParams" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultType="java.lang.Integer">
        select count(*) from res_vs_port_group
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByParamsSelective" parameterType="map">
        update res_vs_port_group
        <set>
            <if test="record.id != null">
                ID = #{record.id},
            </if>
            <if test="record.resVsSid != null">
                RES_VS_SID = #{record.resVsSid},
            </if>
            <if test="record.name != null">
                NAME = #{record.name},
            </if>
            <if test="record.vlanId != null">
                VLAN_ID = #{record.vlanId},
            </if>
            <if test="record.totalPorts != null">
                TOTAL_PORTS = #{record.totalPorts},
            </if>
            <if test="record.availablePorts != null">
                AVAILABLE_PORTS = #{record.availablePorts},
            </if>
            <if test="record.uuid != null">
                UUID = #{record.uuid},
            </if>
            <if test="record.createdBy != null">
                CREATED_BY = #{record.createdBy},
            </if>
            <if test="record.createdDt != null">
                CREATED_DT = #{record.createdDt},
            </if>
            <if test="record.updatedBy != null">
                UPDATED_BY = #{record.updatedBy},
            </if>
            <if test="record.updatedDt != null">
                UPDATED_DT = #{record.updatedDt},
            </if>
            <if test="record.version != null">
                VERSION = #{record.version},
            </if>
            <if test="record.urn != null">
                URN = #{record.urn},
            </if>
            <if test="record.uri != null">
                URI = #{record.uri},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByParams" parameterType="map">
        update res_vs_port_group
        set ID = #{record.id},
        RES_VS_SID = #{record.resVsSid},
        NAME = #{record.name},
        VLAN_ID = #{record.vlanId},
        TOTAL_PORTS = #{record.totalPorts},
        AVAILABLE_PORTS = #{record.availablePorts},
        UUID = #{record.uuid},
        CREATED_BY = #{record.createdBy},
        CREATED_DT = #{record.createdDt},
        UPDATED_BY = #{record.updatedBy},
        UPDATED_DT = #{record.updatedDt},
        URN = #{record.urn},
        URI = #{record.uri},
        VERSION = #{record.version}
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective"
        parameterType="cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResVsPortGroup">
        update res_vs_port_group
        <set>
            <if test="resVsSid != null">
                RES_VS_SID = #{resVsSid},
            </if>
            <if test="name != null">
                NAME = #{name},
            </if>
            <if test="vlanId != null">
                VLAN_ID = #{vlanId},
            </if>
            <if test="totalPorts != null">
                TOTAL_PORTS = #{totalPorts},
            </if>
            <if test="availablePorts != null">
                AVAILABLE_PORTS = #{availablePorts},
            </if>
            <if test="uuid != null">
                UUID = #{uuid},
            </if>
            <if test="createdBy != null">
                CREATED_BY = #{createdBy},
            </if>
            <if test="createdDt != null">
                CREATED_DT = #{createdDt},
            </if>
            <if test="updatedBy != null">
                UPDATED_BY = #{updatedBy},
            </if>
            <if test="updatedDt != null">
                UPDATED_DT = #{updatedDt},
            </if>
            <if test="version != null">
                VERSION = #{version},
            </if>
            <if test="urn != null">
                URN = #{urn},
            </if>
            <if test="uri != null">
                URI = #{uri},
            </if>
            <if test="description != null">
                `description` = #{description},
            </if>
            <if test="portType != null">
                port_type = #{portType},
            </if>
            <if test="vlanRange != null">
                vlan_range = #{vlanRange},
            </if>
            <if test="pgConfig != null">
                pg_config = #{pgConfig},
            </if>
        </set>
        where ID = #{id}
    </update>
    <update id="updateByPrimaryKey"
        parameterType="cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResVsPortGroup">
        update res_vs_port_group
        set RES_VS_SID = #{resVsSid},
        NAME = #{name},
        VLAN_ID = #{vlanId},
        TOTAL_PORTS = #{totalPorts},
        AVAILABLE_PORTS = #{availablePorts},
        UUID = #{uuid},
        CREATED_BY = #{createdBy},
        CREATED_DT = #{createdDt},
        UPDATED_BY = #{updatedBy},
        UPDATED_DT = #{updatedDt},
        urn = #{urn},
        uri = #{uri},
        VERSION = #{version}
        where ID = #{id}
    </update>
    <select id="selectByCloudEnvId" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultMap="BaseResultMap">
        SELECT
        <include refid="Alias_Column_List"/>
        ,B.res_vs_type, B.res_vs_name, C.cloud_env_name, C.cloud_env_type,
        GROUP_CONCAT(A.id) as port_ids
        FROM res_vs_port_group A
        LEFT JOIN res_vs B on A.res_vs_sid = B.res_vs_sid
        LEFT JOIN cloud_env C ON B.parent_topology_sid = C.id
        LEFT JOIN cloud_env_account D ON D.id = C.cloud_env_account_id
        LEFT JOIN res_vs_port_group_network rvpgn on A.id = rvpgn.res_vs_port_group_id
        <if test="_parameter != null">
            <include refid="Example_Where_Clause_Alias"/>
        </if>
        group by A.name,A.vlan_id
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>

    </select>
    <select id="countByCloudEnvId" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultType="java.lang.Integer">
        SELECT
        count(*) FROM (
        SELECT
        A.id
        FROM
        res_vs_port_group A
        LEFT JOIN res_vs B ON A.res_vs_sid = B.res_vs_sid
        WHERE
        B.parent_topology_sid = #{condition.parentTopologySid, jdbcType=VARCHAR}
        <if test="condition.portGroupNameLike != null">
            and A.name like concat('%', #{condition.portGroupNameLike, jdbcType=VARCHAR}, '%')
        </if>
        GROUP BY
        NAME,
        vlan_id
        ) T
    </select>
    <select id="selectSamePortGroupId" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultType="java.lang.String">
        SELECT
        A.id
        from
        res_vs_port_group A
        INNER JOIN (
        select
        name, vlan_id
        from res_vs_port_group
        where id in
        <foreach collection="condition.ids" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        ) T on A.name = T.name and A.vlan_id = T.vlan_id
    </select>
</mapper>
