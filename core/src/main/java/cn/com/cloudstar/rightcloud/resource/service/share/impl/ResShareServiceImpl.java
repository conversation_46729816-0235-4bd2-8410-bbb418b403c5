/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.service.share.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.validation.constraints.NotNull;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;

import cn.com.cloudstar.rightcloud.adapter.core.MQException;
import cn.com.cloudstar.rightcloud.adapter.core.MQHelper;
import cn.com.cloudstar.rightcloud.adapter.pojo.dme.DMECreateQuotaRequest;
import cn.com.cloudstar.rightcloud.adapter.pojo.dme.DMEDeleteQuotaRequest;
import cn.com.cloudstar.rightcloud.adapter.pojo.dme.DMEQuotaListQueryRequest;
import cn.com.cloudstar.rightcloud.adapter.pojo.dme.DMEQuotaUpdateRequest;
import cn.com.cloudstar.rightcloud.adapter.pojo.dme.QuotaOwner;
import cn.com.cloudstar.rightcloud.adapter.pojo.dme.result.DMECreateQuotaResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.dme.result.DMEDeleteQuotaResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.dme.result.DMEQuotaListQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.dme.result.DMEQuotaUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.OceanStorAccount;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.OceanStorAccountCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.OceanStorAccountDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.OceanStorConvergedQosAssociationCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.OceanStorConvergedQosAssociationDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.OceanStorConvergedQosPolicyCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.OceanStorConvergedQosPolicyDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.OceanStorNamespacesCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.OceanStorNamespacesDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.OceanStorNamespacesQuota;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.OceanStorNamespacesQuotaCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.OceanStorNamespacesQuotaDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.OceanStorNamespacesQuotaUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.OceanStorTierPoliciesCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.OceanStorTierPoliciesDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.OceanStorUnixUser;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.OceanStorUnixUserDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.OceanStorUnixUserGroup;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.OceanStorUnixUserGroupDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.result.OceanStorAccountCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.result.OceanStorAccountResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.result.OceanStorConvergedQosPolicyCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.result.OceanStorNamespacesCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.result.OceanStorNamespacesQuotaCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.result.OceanStorNamespacesQuotaResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.result.OceanStorTierPoliciesCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.result.OceanStorTierPoliciesDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.result.OceanStorUnixUserGroupInfo;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.result.OceanStorUnixUserGroupResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.result.OceanStorUnixUserInfo;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.result.OceanStorUnixUserResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ShareDetailByEnv;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ShareDetailByEnvResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.vo.ShareVo;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.*;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.*;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.MountPreResShare;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.QuotaSetCreat;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.QuotaSetCreatResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.ShareActionResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.ShareCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.ShareMountTargetCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.ShareMountTargetDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.ShareMountTargetUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.ShareUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.ShareAction;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.ShareCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.ShareDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.ShareInfo;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.ShareModify;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.ShareMountTargetCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.ShareMountTargetDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.ShareMountTargetUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.SharePredeployCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.SharePredeployDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.SharePredeployModify;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.ShareUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.StorageInfo;
import cn.com.cloudstar.rightcloud.basic.data.platform.CloudClientFactory;
import cn.com.cloudstar.rightcloud.basic.data.pojo.base.Base;
import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnv;
import cn.com.cloudstar.rightcloud.basic.data.pojo.deploy.DeployTask;
import cn.com.cloudstar.rightcloud.basic.data.pojo.enums.ShareSupportClusterTypeEnum;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.Network;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResOceanstorPAccount;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResOceanstorPAccountExample;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResOceanstorPNamespace;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResOceanstorPNamespaceExample;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResOceanstorPQosPolicy;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResOceanstorPQosPolicyExample;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResOceanstorPQuota;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResOceanstorPQuotaExample;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResOceanstorPTierPolicy;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResOceanstorPTierPolicyExample;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResSecurityGroup;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResShare;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResShareSumInfo;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResShareTarget;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResVm;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResVpc;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ShareRequest;
import cn.com.cloudstar.rightcloud.basic.data.service.deploy.BasicDeployTaskService;
import cn.com.cloudstar.rightcloud.basic.data.service.res.BasicResActionLogService;
import cn.com.cloudstar.rightcloud.common.additional.ResInstResult;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.constants.res.status.ResHpcClusterStatus;
import cn.com.cloudstar.rightcloud.common.constants.res.status.ResVmStatus;
import cn.com.cloudstar.rightcloud.common.constants.res.status.ShareStatus;
import cn.com.cloudstar.rightcloud.common.constants.res.status.ShareTarGetStatus;
import cn.com.cloudstar.rightcloud.common.constants.res.type.CloudEnvType;
import cn.com.cloudstar.rightcloud.common.constants.type.CloudEnvTenantKey;
import cn.com.cloudstar.rightcloud.common.constants.type.DeployTaskType;
import cn.com.cloudstar.rightcloud.common.enums.resource.ResourceOperateEnum;
import cn.com.cloudstar.rightcloud.common.enums.resource.ResourceTypeEnum;
import cn.com.cloudstar.rightcloud.common.exception.BizException;
import cn.com.cloudstar.rightcloud.common.pojo.DMEOSPQuotaDeleteMessage;
import cn.com.cloudstar.rightcloud.common.pojo.RestResult;
import cn.com.cloudstar.rightcloud.common.util.AssertUtil;
import cn.com.cloudstar.rightcloud.common.util.CrytoUtilSimple;
import cn.com.cloudstar.rightcloud.common.util.NotificationUtil;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.common.util.json.JsonUtil;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResShareMountTarget;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResShareRightsGroup;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResChangeRecord;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResHpcCluster;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResHpcClusterResource;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResHpcClusterResourceExample;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResShareMountTarget;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResShareRightsGroup;
import cn.com.cloudstar.rightcloud.driver.pojo.base.BaseResult;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.Org;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.User;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicInfoUtil;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicWebUtil;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.dmeosp.ResDMEOSP;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.oceanstor.ResOceanstorShare;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ModifyShareParam;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.RenewBasicResourceDTO;
import cn.com.cloudstar.rightcloud.remote.api.system.service.user.BssUserRemoteService;
import cn.com.cloudstar.rightcloud.resource.dao.hpc.ResHpcClusterMapper;
import cn.com.cloudstar.rightcloud.resource.dao.hpc.ResHpcClusterResourceMapper;
import cn.com.cloudstar.rightcloud.resource.dao.oceanstor.ResOceanstorPAccountMapper;
import cn.com.cloudstar.rightcloud.resource.dao.oceanstor.ResOceanstorPNamespaceMapper;
import cn.com.cloudstar.rightcloud.resource.dao.oceanstor.ResOceanstorPQosPolicyMapper;
import cn.com.cloudstar.rightcloud.resource.dao.oceanstor.ResOceanstorPQuotaMapper;
import cn.com.cloudstar.rightcloud.resource.dao.oceanstor.ResOceanstorPTierPolicyMapper;
import cn.com.cloudstar.rightcloud.resource.dao.server.ResChangeRecordMapper;
import cn.com.cloudstar.rightcloud.resource.dao.server.ResVmMapper;
import cn.com.cloudstar.rightcloud.resource.dao.share.ResShareMapper;
import cn.com.cloudstar.rightcloud.resource.dao.share.ResShareMountTargetMapper;
import cn.com.cloudstar.rightcloud.resource.dao.share.ResShareRightsGroupMapper;
import cn.com.cloudstar.rightcloud.resource.dao.share.ResShareRuleMapper;
import cn.com.cloudstar.rightcloud.resource.dao.share.ResShareTargetMapper;
import cn.com.cloudstar.rightcloud.resource.dao.user.SystemMUserMapper;
import cn.com.cloudstar.rightcloud.resource.service.env.CloudEnvService;
import cn.com.cloudstar.rightcloud.resource.service.network.NetworkService;
import cn.com.cloudstar.rightcloud.resource.service.network.ResVpcService;
import cn.com.cloudstar.rightcloud.resource.service.oceanstor.OceanStorService;
import cn.com.cloudstar.rightcloud.resource.service.security.ResSecurityGroupService;
import cn.com.cloudstar.rightcloud.resource.service.server.ResVmService;
import cn.com.cloudstar.rightcloud.resource.service.share.ShareService;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.com.cloudstar.rightcloud.common.additional.ResInstResult.FAILURE;

/**
 * <AUTHOR>
 */
@Service
public class ResShareServiceImpl implements ShareService {

    private static final Logger logger = LoggerFactory.getLogger(ResShareServiceImpl.class);


    final static String ACTIVE = "Active";
    final static String INACTIVE = "Inactive";
    final static String CONSOLE = "console";
    final static String ADMIN = "admin";
    final static Long VERSION = 1L;
    final static String FILE_SYSTEM = "filesystem";
    final static String NAMESPACE = "namespace";
    final static String USER_GROUP_QUOTA = "user_group_quota";
    final static String DOMAIN_USER_GROUP = "domain_user_group";
    final static String LDAP_DOMAIN = "ldap_domain";
    private static final String DME_OSP = "DME-OSP";


    @Autowired
    private ResShareMapper resShareMapper;

    @Autowired
    private ResShareRuleMapper resShareRuleMapper;

    @Autowired
    private ResShareTargetMapper resShareTargetMapper;

    @Autowired
    private ResVpcService resVpcService;

    @Autowired
    private ResVmMapper resVmMapper;

    @Autowired
    private BasicDeployTaskService basicDeployTaskService;

    @Lazy
    @Autowired
    private NetworkService networkService;

    @Autowired
    private ResShareMountTargetMapper resShareMountTargetMapper;

    @Autowired
    private ResShareRightsGroupMapper shareRightsGroupMapper;

    @Autowired
    private ResShareMountTargetMapper mountTargetMapper;

    @Autowired
    private CloudEnvService cloudEnvService;

    @Autowired
    private ResSecurityGroupService resSecurityGroupService;

    @Lazy
    @Autowired
    private ResVmService resVmService;

    @Autowired
    private BasicResActionLogService basicResActionLogService;

    @Autowired
    private OceanStorService oceanStorService;
    @Autowired
    private ResOceanstorPAccountMapper resOceanstorPAccountMapper;
    @Autowired
    private ResOceanstorPNamespaceMapper resOceanstorPNamespaceMapper;
    @Autowired
    private ResOceanstorPQosPolicyMapper resOceanstorPQosPolicyMapper;
    @Autowired
    private ResOceanstorPTierPolicyMapper resOceanstorPTierPolicyMapper;
    @Autowired
    private ResOceanstorPQuotaMapper resOceanstorPQuotaMapper;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private ResHpcClusterMapper resHpcClusterMapper;

    @Autowired
    private SystemMUserMapper systemMUserMapper;

    @DubboReference
    private BssUserRemoteService bssUserRemoteService;

    @Autowired
    private ResHpcClusterResourceMapper resHpcClusterResourceMapper;

    @Autowired
    private ResChangeRecordMapper resChangeRecordMapper;
    @Autowired
    private AmqpTemplate amqpTemplate;

    /**
     * 扩容
     */
    private static final String EXTEND = "extend";

    /**
     * 缩容
     */
    private static final String SHRINK = "shrink";


    /**
     * REQUIRES_NEW:fix 开通共享失败扩缩容数据未落库问题
     *
     * @param id
     * @param size
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    @Override
    public Boolean action(Long id, Integer size) {
        ResShare share = resShareMapper.selectByPrimaryKey(id);
        if (null == share) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_182954958));
        }
        if (size.intValue() == share.getSize()) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_613599113));
        }

        String shareTypeName = share.getShareTypeName();
        if(StringUtils.equalsIgnoreCase(shareTypeName,"oceanstor")){
            //查询OceanStor 云环境ID
            Criteria criteria = new Criteria();
            criteria.put("cloudEnvType", CloudEnvType.OCEANSTOR_PACIFIC.getValue().get(0));
            List<CloudEnv> cloudEnvs = cloudEnvService.selectByParams(criteria);

            if(CollectionUtil.isEmpty(cloudEnvs)){
                throw new BizException(StrUtil.format(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_947266489),CloudEnvType.OCEANSTOR_PACIFIC.getDesc()));
            }
            CloudEnv cloudEnv = cloudEnvs.get(0);
            Long cloudEnvId = cloudEnv.getId();
            //查询配额度
            ResOceanstorPQuotaExample quotaExample = new ResOceanstorPQuotaExample();
            quotaExample.createCriteria().andOwnerIdEqualTo(share.getOwnerId()).andOrgSidEqualTo(share.getOrgSid()).andCloudEnvIdEqualTo(cloudEnvId);
            List<ResOceanstorPQuota> resOceanstorPQuotas = resOceanstorPQuotaMapper.selectByExample(quotaExample);
            if(!CollectionUtil.isEmpty(resOceanstorPQuotas)){
                ResOceanstorPQuota resOceanstorPQuota = resOceanstorPQuotas.get(0);
                String resourceId = resOceanstorPQuota.getResourceId();
                //修改配额
                OceanStorNamespacesQuotaUpdate quotaUpdate = new OceanStorNamespacesQuotaUpdate();
                quotaUpdate.setId(resourceId);
                quotaUpdate.setSpace_hard_quota(size.toString());
                quotaUpdate.setSpace_unit_type(resOceanstorPQuota.getSpaceUnitType().toString());
                BaseResult baseResult = oceanStorService.oceanStorNamespacesQuotaUpdate(quotaUpdate, cloudEnvId);
                if(baseResult.isSuccess()){
                    share.setSize(size);
                    BasicWebUtil.prepareUpdateParams(share);
                    resShareMapper.updateByPrimaryKey(share);

                    resOceanstorPQuota.setSpaceHardQuota(Long.valueOf(size));
                    BasicWebUtil.prepareUpdateParams(resOceanstorPQuota);
                    resOceanstorPQuotaMapper.updateByPrimaryKeySelective(resOceanstorPQuota);

                    return true;
                }
                throw new BizException(baseResult.getErrMsg());
            }

        }else{
            ShareAction shareAction = CloudClientFactory.buildMQBean(share.getCloudEnvId(), ShareAction.class);
            shareAction.setOperation(size > share.getSize() ? "add" : "up");
            shareAction.setSize(size);
            shareAction.setUuid(share.getUuid());
            shareAction.setType(share.getType());
            try {
                ShareActionResult shareActionResult = (ShareActionResult) MQHelper.rpc(shareAction);
                if (shareActionResult.isSuccess()) {
                    share.setSize(size);
                    BasicWebUtil.prepareUpdateParams(share);
                    resShareMapper.updateByPrimaryKey(share);
                    return true;
                }
                throw new BizException(shareActionResult.getErrMsg());
            } catch (Exception e) {
                throw new BizException(e.getMessage());
            }
        }

        return true;
    }

    /**
     * REQUIRES_NEW:fix 开通共享失败扩缩容数据未落库问题
     *
     * @param modifyShareParam
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    @Override
    public Boolean modify(ModifyShareParam modifyShareParam) {
        Long id = modifyShareParam.getId();
        Integer size = modifyShareParam.getSize();
        ResShare share = resShareMapper.selectByPrimaryKey(id);
        if (null == share) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_182954958));
        }
        if (size.intValue() == share.getSize()) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_613599113));
        }

        String shareTypeName = share.getShareTypeName();
        if(StringUtils.equalsIgnoreCase(shareTypeName,"oceanstor")){
            logger.info("OceanStor扩缩容");
            //查询OceanStor 云环境ID
            Criteria criteria = new Criteria();
            criteria.put("cloudEnvType", CloudEnvType.OCEANSTOR_PACIFIC.getValue().get(0));
            List<CloudEnv> cloudEnvs = cloudEnvService.selectByParams(criteria);

            if(CollectionUtil.isEmpty(cloudEnvs)){
                throw new BizException(StrUtil.format(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_947266489),CloudEnvType.OCEANSTOR_PACIFIC.getDesc()));
            }
            CloudEnv cloudEnv = cloudEnvs.get(0);
            Long cloudEnvId = cloudEnv.getId();
            String ownerId = share.getOwnerId();
            //查询Oceanstor 账户
            ResOceanstorPAccountExample accountExample = new ResOceanstorPAccountExample();
            accountExample.createCriteria().andCloudEnvIdEqualTo(cloudEnvId)
                .andOwnerIdEqualTo(ownerId)
                .andStatusEqualTo(ACTIVE);
            List<ResOceanstorPAccount> resOceanstorPAccounts = resOceanstorPAccountMapper.selectByExample(accountExample);
            Optional<ResOceanstorPAccount> accountOptional = resOceanstorPAccounts.stream().findFirst();
            if(!accountOptional.isPresent()){
                throw new BizException("OceanStor账户异常");
            }
            ResOceanstorPAccount resOceanstorPAccount = accountOptional.get();
            String oceanstorAccountId = resOceanstorPAccount.getResourceId();
            ResOceanstorPQuotaExample quotaExample = new ResOceanstorPQuotaExample();
            quotaExample.createCriteria().andCloudEnvIdEqualTo(cloudEnvId)
                .andOwnerIdEqualTo(ownerId)
                .andParentIdEqualTo(share.getUuid());
            List<ResOceanstorPQuota> resOceanstorPQuotas = resOceanstorPQuotaMapper.selectByExample(quotaExample);
            Optional<ResOceanstorPQuota> oceanstorPQuotaOptional = resOceanstorPQuotas.stream().findFirst();
            if(oceanstorPQuotaOptional.isPresent()){
                ResOceanstorPQuota resOceanstorPQuota = oceanstorPQuotaOptional.get();
                //查询配额
                OceanStorNamespacesQuota oceanStorNamespacesQuota = new OceanStorNamespacesQuota();
                oceanStorNamespacesQuota.setId(resOceanstorPQuota.getResourceId());
                oceanStorNamespacesQuota.setAccount_id(oceanstorAccountId);
                oceanStorNamespacesQuota.setSpace_unit_type(resOceanstorPQuota.getSpaceUnitType().toString());
                OceanStorNamespacesQuotaResult quotaResult = oceanStorService.oceanStorNamespacesQuota(oceanStorNamespacesQuota, cloudEnvId);
                if (quotaResult == null || !quotaResult.isSuccess()) {
                    logger.info("ResShareServiceImpl.modify-查询[{},{}]配额信息失败",share.getId(),share.getUuid());
                    throw new BizException("查询已使用容量失败");
                }
                String spaceUsed = quotaResult.getSpace_used();
                spaceUsed = StringUtils.isNotEmpty(spaceUsed)?spaceUsed:"0";
                if (size.intValue() < Integer.valueOf(spaceUsed)) {
                    throw new BizException(StrUtil.format("当前已使用{}GB,",spaceUsed));
                }

                String resourceId = resOceanstorPQuota.getResourceId();
                //修改配额
                OceanStorNamespacesQuotaUpdate quotaUpdate = new OceanStorNamespacesQuotaUpdate();
                quotaUpdate.setId(resourceId);
                quotaUpdate.setSpace_hard_quota(size.toString());
                quotaUpdate.setSpace_unit_type(resOceanstorPQuota.getSpaceUnitType().toString());
                BaseResult baseResult = oceanStorService.oceanStorNamespacesQuotaUpdate(quotaUpdate, cloudEnvId);
                if(baseResult.isSuccess()){
                    share.setSize(size);
                    BasicWebUtil.prepareUpdateParams(share);
                    resShareMapper.updateByPrimaryKey(share);

                    resOceanstorPQuota.setSpaceHardQuota(Long.valueOf(size));
                    BasicWebUtil.prepareUpdateParams(resOceanstorPQuota);
                    resOceanstorPQuotaMapper.updateByPrimaryKeySelective(resOceanstorPQuota);

                    // 插入变更记录
                    ResChangeRecord resChangeRecord = new ResChangeRecord();
                    resChangeRecord.setOrgSid(share.getOrgSid());
                    resChangeRecord.setResType("HPC_SFS");
                    resChangeRecord.setOwnerId(Convert.toLong(ownerId));
                    resChangeRecord.setResourceId(share.getId() + "");
                    resChangeRecord.setCloudEnvId(share.getCloudEnvId());
                    resChangeRecord.setInstanceId(share.getUuid());
                    //新大小
                    resChangeRecord.setNewType(modifyShareParam.getSize() + "");
                    //原始大小
                    resChangeRecord.setOriginalType(modifyShareParam.getOldSize() + "");
                    BasicWebUtil.prepareInsertParams(resChangeRecord);
                    logger.info("ResShareServiceImpl.action 弹性文件扩缩容-oceanstor ： {}", JsonUtil.toJson(resChangeRecord));
                    resChangeRecordMapper.insertSelective(resChangeRecord);

                    return true;
                }
                throw new BizException(baseResult.getErrMsg());
            }

        }else if(DME_OSP.equals(share.getType())){
            return modifyDMEQuota(share, modifyShareParam);
        }else{
            logger.info("非OceanStor扩缩容：[{}]", shareTypeName);
            logger.info("getHCSOAKSK|parentUserSid[{}]", BasicInfoUtil.getParentUserSid());
            if (ShareSupportClusterTypeEnum.PREDEPLOY.getCode().equals(share.getSupportClusterType())) {
                // 预部署集群弹性文件扩缩容
                return this.modifyPredeployShare(share, modifyShareParam);
            } else {
                // 按需集群弹性文件扩缩容
                ShareModify shareModify = CloudClientFactory.buildMQBean(share.getCloudEnvId(), ShareModify.class);
                shareModify.setOperation(size > share.getSize() ? "add" : "up");
                shareModify.setSize(size);
                shareModify.setId(share.getId());
                shareModify.setUuid(share.getUuid());
                shareModify.setType(share.getType());
                shareModify.setServiceOrderId(modifyShareParam.getServiceOrderId());
                shareModify.setOldSize(modifyShareParam.getOldSize());
                shareModify.setOpUser(modifyShareParam.getOpUser());
                shareModify.setOrgSid(modifyShareParam.getOrgSid());
                try {
                    //ShareActionResult shareActionResult = (ShareActionResult) MQHelper.rpc(shareAction);
                    logger.info("弹性文件扩缩容参数：[{}]", JSONUtil.toJsonStr(shareModify));
                    ResInstResult resInstResult = sendToMQ(shareModify);
                    //if (shareActionResult.isSuccess()) {
                    //    //移至回调处理
                    //    //share.setSize(size);
                    //    //BasicWebUtil.prepareUpdateParams(share);
                    //    //resShareMapper.updateByPrimaryKey(share);
                    //    return true;
                    //}
                    logger.info("弹性文件扩缩容返回：[{}]", JSONUtil.toJsonStr(resInstResult));
                    if (resInstResult.getStatus()) {
                        share.setStatus(ShareStatus.MODIFY);
                        BasicWebUtil.prepareUpdateParams(share);
                        resShareMapper.updateByPrimaryKey(share);
                    }
                    return resInstResult.getStatus();
                } catch (Exception e) {
                    throw new BizException(e.getMessage());
                }
            }
        }

        return true;
    }

    private Boolean modifyDMEQuota(ResShare share, ModifyShareParam modifyShareParam) {
        Long shareId = share.getId();
        logger.info("modifyDMEQuota shareId=[{}],shareName = [{}]", shareId,share.getName());
        ResOceanstorPQuota resOceanstorPQuota = resOceanstorPQuotaMapper.selectByPrimaryKey(share.getQuotaId());
        try {
            DMEQuotaUpdateRequest updateRequest =  CloudClientFactory.buildMQBean(resOceanstorPQuota.getCloudEnvId(), DMEQuotaUpdateRequest.class);
            updateRequest.setId(resOceanstorPQuota.getResourceId());
            updateRequest.setSpaceHardQuota(modifyShareParam.getSize() * 1024L * 1024L);

            logger.info("DME存储配额扩缩容参数：[{}]", JSONUtil.toJsonStr(updateRequest));
            DMEQuotaUpdateResult result = (DMEQuotaUpdateResult)MQHelper.rpc(updateRequest);
            logger.info("DME存储配额扩缩容参数：[{}]", JSONUtil.toJsonStr(result));
            if (result.isSuccess()) {
                if (shareId != null) {
                    share.setStatus(ShareStatus.MODIFY);
                    BasicWebUtil.prepareUpdateParams(share);
                    resShareMapper.updateByPrimaryKey(share);
                } else {
                    share.setStatus(ShareStatus.MODIFY);
                }
                resOceanstorPQuota.setSpaceHardQuota(Long.valueOf(modifyShareParam.getSize()));
                resOceanstorPQuota.setStatus(ShareStatus.MODIFY);
                resOceanstorPQuota.setTaskId(result.getTaskId());
                BasicWebUtil.prepareUpdateParams(resOceanstorPQuota);
                resOceanstorPQuotaMapper.updateByPrimaryKey(resOceanstorPQuota);
            } else {
                return false;
            }
            return true;
        } catch (Exception e) {
            throw new BizException(e.getMessage());
        }
    }

    private Boolean modifyPredeployShare(ResShare share, ModifyShareParam modifyShareParam) {
        Criteria criteria = new Criteria();
        criteria.put("cloudEnvType", CloudEnvType.FUSION_DIRECTOR.getValue().get(0));
        List<CloudEnv> cloudEnvs = cloudEnvService.selectByParams(criteria);
        SharePredeployModify sharePredeployModify = CloudClientFactory.buildMQBean(cloudEnvs.get(0).getId(), SharePredeployModify.class);

        sharePredeployModify.setApplyTaskID("");
        sharePredeployModify.setId(share.getId());
        sharePredeployModify.setFileSystemID(share.getUuid());
        sharePredeployModify.setNewSize(modifyShareParam.getSize());
        sharePredeployModify.setServiceOrderId(modifyShareParam.getServiceOrderId());
        sharePredeployModify.setOldSize(modifyShareParam.getOldSize());
        sharePredeployModify.setOpUser(modifyShareParam.getOpUser());
        sharePredeployModify.setOrgSid(modifyShareParam.getOrgSid());
        sharePredeployModify.setOperationType(sharePredeployModify.getNewSize() > sharePredeployModify.getOldSize() ? EXTEND : SHRINK);
        sharePredeployModify.setShareTenantName(BasicInfoUtil.getAuthUser().getAccount());

        try {
            ResInstResult resInstResult = sendToMQ(sharePredeployModify);
            if (resInstResult.getStatus()) {
                share.setStatus(ShareStatus.MODIFY);
                BasicWebUtil.prepareUpdateParams(share);
                resShareMapper.updateByPrimaryKey(share);
            }
            return resInstResult.getStatus();
        } catch (Exception e) {
            throw new BizException(e.getMessage());
        }
    }

    @Override
    public Boolean resetShareDirByShareTypeId(Long shareTypeId, @NotNull Integer type) {
        resShareMapper.resetShareDirByShareTypeId(shareTypeId, type);
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateShare(ResShare resShare) {
        ResShare oldShare = resShareMapper.selectByPrimaryKey(resShare.getId());
        if (null == oldShare || null == oldShare.getUuid()) {
            return false;
        }

        CloudEnv cloudEnv = cloudEnvService.selectByPrimaryKey(oldShare.getCloudEnvId());
        if (Objects.isNull(cloudEnv)) {
            return false;
        }

        try {
            ShareUpdate shareUpdate = CloudClientFactory.buildMQBean(resShare.getCloudEnvId(), ShareUpdate.class);
            shareUpdate.setDescription(resShare.getDescription());
            shareUpdate.setName(resShare.getName());
            shareUpdate.setUuid(oldShare.getUuid());
            if (CloudEnvType.ALIYUN.equals(cloudEnv.getCloudEnvType())) {
                if (StringUtils.isNotBlank(resShare.getName())) {
                    shareUpdate.setDescription(resShare.getName());
                }
            }
            ShareUpdateResult shareUpdatetResult = (ShareUpdateResult) MQHelper.rpc(shareUpdate);
            if (shareUpdatetResult.isSuccess()) {
                oldShare.setName(resShare.getName());
                oldShare.setDescription(resShare.getDescription());
                if (CloudEnvType.ALIYUN.equals(cloudEnv.getCloudEnvType())) {
                    if (StringUtils.isNotBlank(resShare.getName())) {
                        oldShare.setDescription(resShare.getName());
                    }
                }
                BasicWebUtil.prepareUpdateParams(oldShare);
                resShareMapper.updateByPrimaryKey(oldShare);
                return true;
            } else {
                throw new BizException(shareUpdatetResult.getErrMsg());
            }
        } catch (Exception e) {
            throw new BizException(e.getMessage());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean deleteShareById(Long id) {
        ResShare share = resShareMapper.selectByPrimaryKey(id);
        if (null == share) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_183455081));
        }
        if (Objects.isNull(share.getUuid())) {
            return resShareMapper.deleteShareById(share.getId()) == 1;
        }
        share.setStatus(ShareStatus.deleting);
        resShareMapper.updateByPrimaryKey(share);

        ShareDelete shareDelete = CloudClientFactory.buildMQBean(share.getCloudEnvId(), ShareDelete.class);
        shareDelete.setId(share.getId());
        shareDelete.setUuid(share.getUuid());
        shareDelete.setName(share.getName());
        shareDelete.setType(share.getType());
        ResInstResult resInstResult = sendToMQ(shareDelete);
        return resInstResult.getStatus();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean mount(ResShareTarget resShareTarget) {
        ResVm resVm = resVmMapper.selectByPrimaryKey(resShareTarget.getResVmId());
        Criteria criteria = new Criteria();
        criteria.put("shareId", resShareTarget.getShareId());
        criteria.put("resVmId", resShareTarget.getResVmId());
        criteria.put("localPath", resShareTarget.getLocalPath());
        criteria.put("status", Arrays.asList(ShareTarGetStatus.INSTALLED));
        List<ResShareTarget> resShareTargets = resShareTargetMapper.selectTargetByParam(criteria);
        if (!CollectionUtils.isEmpty(resShareTargets)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_86345836));
        }

        if (StringUtils.isEmpty(resVm.getPublicIp())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1803564757));
        }

        if (StringUtils.isEmpty(resVm.getManagemenPassword()) || StringUtils.isEmpty(resVm.getManagementAccount())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_279635611));
        }

        resShareTarget.setStatus(ShareTarGetStatus.INSTALLING);
        resShareTarget.setName(resVm.getInstanceName());
        resShareTargetMapper.insertSelective(resShareTarget);

        ResShare resShare = resShareMapper.selectByPrimaryKey(Long.valueOf(resShareTarget.getShareId()));

        DeployTask deployTask = basicDeployTaskService.createSfsTask(resVm, resShareTarget, DeployTaskType.MOUNT_SFS);

        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean unMount(Long targetId) {
        ResShareTarget resShareTarget = resShareTargetMapper.selectByPrimaryKey(targetId);
        if (Objects.isNull(resShareTarget)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_768632853));
        }
        ResVm resVm = resVmMapper.selectByPrimaryKey(resShareTarget.getResVmId());
        if (Objects.isNull(resVm) || ResVmStatus.DELETED.equalsIgnoreCase(resVm.getStatus())) {
            resShareTargetMapper.deleteResShareTarget(targetId);
            return true;
        }

        resShareTarget.setStatus(ShareTarGetStatus.UNINSTALLING);
        resShareTargetMapper.updateByPrimaryKey(resShareTarget);

        DeployTask deployTask = basicDeployTaskService.createSfsTask(resVm, resShareTarget,
                                                                     DeployTaskType.UN_MOUNT_SFS);

        return true;
    }

    @Override
    public List<ResShareTarget> selectTargetHost(Criteria criteria) {
        return resShareTargetMapper.selectTargetByParam(criteria);
    }

    @Override
    public Page<DeployTask> selectTaskForPage(String shareId, List<String> types, String name, Pageable pageable) {
        List<ResShareTarget> resShareTargets = resShareTargetMapper.selectTargetByParam(
                new Criteria("shareId", shareId));
        List<String> targetIds = resShareTargets.stream()
                                                .map(target -> target.getId().toString())
                                                .collect(Collectors.toList());
        List<String> targets = resShareTargets.stream().map(ResShareTarget::getResVmId).collect(Collectors.toList());
        return basicDeployTaskService.findAllBySubTargetInAndTypeInForPage(targets, targetIds, types, pageable);
    }

    @Override
    public List<DeployTask> selectTask(String subTarget, List<String> types) {
        return basicDeployTaskService.findAllBySubTargetAndTypeIn(subTarget, types);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean deleteTargetHost(Long targetId) {
        resShareTargetMapper.deleteResShareTarget(targetId);
        return true;
    }

    @Override
    public Map getQuotaSet(Long envId) {
        Map<String, Object> map = Maps.newHashMap();
        //先查询是否可以创建
        QuotaSetCreat quotaSetCreat = CloudClientFactory.buildMQBean(envId, QuotaSetCreat.class);
        try {
            QuotaSetCreatResult quotaSetCreatResult = (QuotaSetCreatResult) MQHelper.rpc(quotaSetCreat);
            if (quotaSetCreatResult.isSuccess()) {
                map.put("gigabytes", quotaSetCreatResult.getGigabytes());
                map.put("snapshots", quotaSetCreatResult.getSnapshots());
                map.put("shares", quotaSetCreatResult.getShares());
                map.put("snapshot_gigabytes", quotaSetCreatResult.getSnapshot_gigabytes());
                map.put("share_networks", quotaSetCreatResult.getShare_networks());
                map.put("inUseGigabytes", quotaSetCreatResult.getInUseGigabytes());
                map.put("inUseNumber", quotaSetCreatResult.getInUseNumber());
                return map;
            }
            throw new BizException(quotaSetCreatResult.getErrMsg());
        } catch (Exception e) {
            logger.warn("查询配额限制失败", e);
            throw new BizException(e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public boolean removeResourceFromDBByEnvId(Long envId) {
        Criteria criteria = new Criteria("envId", envId);
        List<ResShare> resShares = resShareMapper.selectByParams(criteria);

        if (!CollectionUtils.isEmpty(resShares)) {
            List<Long> shareIds = resShares.stream().map(ResShare::getId).collect(Collectors.toList());

            Criteria resShareIds = new Criteria("shareIds", shareIds);
            // share_rule
            this.resShareRuleMapper.deleteShareRule(resShareIds);

            // share_target
            this.resShareTargetMapper.deleteByParams(resShareIds);

            // share
            this.resShareMapper.deleteByParams(criteria);
        }

        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public RestResult createShare(ResShare resShare) {
        resShare.setStatus(ShareStatus.CREATING);
        if (StringUtils.isNotBlank(resShare.getName()) && StringUtils.isBlank(resShare.getDescription())) {
            resShare.setDescription(resShare.getName());
        }
        if (resShare.getCreatedOrgSid() == null || resShare.getOrgSid() == null || resShare.getOwnerId() == null) {
            resShare.setOrgSid(BasicInfoUtil.getCurrentOrgSid());
            resShare.setCreatedOrgSid(BasicInfoUtil.getCurrentOrgSid());
            resShare.setOwnerId(BasicInfoUtil.getAuthUser().getUserSid().toString());
        }
        BasicWebUtil.prepareInsertParams(resShare);

        if (!ShareSupportClusterTypeEnum.PREDEPLOY.getCode().equals(resShare.getSupportClusterType())) {
            // 普通自定义文件系统的文件服务名称为实例名
            resShare.setFileSystemName(resShare.getName());
        }
        // 必须先插入 把id带到回调方法
        resShareMapper.insertSelective(resShare);

        ResInstResult resInstResult;
        if (ShareSupportClusterTypeEnum.PREDEPLOY.getCode().equals(resShare.getSupportClusterType())) {
            // 开通预部署集群弹性文件系统
            resInstResult = this.createPredeployShare(resShare);

        }else {
            // 开通按需集群弹性文件系统
            ShareCreate shareCreate = CloudClientFactory.buildMQBean(resShare.getCloudEnvId(), ShareCreate.class);
            BeanUtils.copyProperties(resShare, shareCreate);


            if (null != resShare.getResVpcId()) {
                ResVpc resVpc = resVpcService.selectByPrimaryKey(resShare.getResVpcId());
                shareCreate.setResVpcId(resVpc.getId());
                shareCreate.setResVpcUuid(resVpc.getUuid());
            }

            if (null != resShare.getSubnetId()) {
                Network network = networkService.selectByPrimaryKey(resShare.getSubnetId());
                shareCreate.setSubnetId(network.getId());
                shareCreate.setSubnetUuid(network.getUuid());
                if (CloudEnvType.HCSO.getValue().get(0).equals(cloudEnvService.assertEnvNonNull(resShare.getCloudEnvId()).getCloudEnvType())
                        || "SFS-Turbo".equalsIgnoreCase(shareCreate.getType())) {
                    shareCreate.setSubnetUuid(network.getNetworkUuid());
                }
            }


        if (null != resShare.getSecurityGroupPid()) {
            ResSecurityGroup resSecurityGroup = resSecurityGroupService.selectByPrimaryKey(
                    resShare.getSecurityGroupPid());
            shareCreate.setSecurityGroupId(resSecurityGroup.getId());
            shareCreate.setSecurityGroupUuid(resSecurityGroup.getUuid());
        }

            resInstResult = sendToMQ(shareCreate);
        }



        if (resInstResult.getStatus()) {
            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS),
                                  resShare.getId());
        } else {
            return new RestResult(RestResult.Status.FAILURE, WebUtil.getMessage(MsgCd.ERROR_OPERATE_FAILURE));
        }
    }

    /**
     * 创建适用于预部署集群的文件系统
     * @param resShare
     * @return
     */
    private ResInstResult createPredeployShare(ResShare resShare) {
        // 预部署集群文件系统只能开通5个
        this.checkPredeployShare(resShare.getOrgSid());

        Criteria criteria = new Criteria();
        criteria.put("cloudEnvType", CloudEnvType.FUSION_DIRECTOR.getValue().get(0));
        List<CloudEnv> cloudEnvs = cloudEnvService.selectByParams(criteria);
        SharePredeployCreate sharePredeployCreate = CloudClientFactory.buildMQBean(cloudEnvs.get(0).getId(), SharePredeployCreate.class);

        BeanUtils.copyProperties(resShare, sharePredeployCreate);
        sharePredeployCreate.setApplyTaskID("");
        sharePredeployCreate.setShareTenantName(BasicInfoUtil.getAuthUser().getAccount());

        sharePredeployCreate.setStoragePoolName(resShare.getShareType());

        SharePredeployCreate.StorageInfo storageInfo = sharePredeployCreate.getStorageInfo();
        storageInfo.setShareProto(resShare.getShareProto());
        storageInfo.setShareType(resShare.getShareType());
        storageInfo.setSize(resShare.getSize());

        return sendToMQ(sharePredeployCreate);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public RestResult mountPreResShare(ShareRequest request) {

        User authUser = BasicInfoUtil.getAuthUser();
        // 判断产品是否冻结  todo  业务逻辑
        ResShare resShare = resShareMapper.selectByPrimaryKey(request.getSFSId());
        if (!ShareSupportClusterTypeEnum.PREDEPLOY.getCode().equals(resShare.getSupportClusterType())
                || resShare.getIsClusterDefault()) {
            throw new BizException("该文件不支持此操作！");
        }

        if (Objects.isNull(resShare) || ShareStatus.FROZEN.equals(resShare.getStatus())) {
            throw new BizException("当前挂载弹性文件已冻结!");
        }
        ResHpcCluster resHpcCluster = resHpcClusterMapper.selectByPrimaryKey(request.getHPCId());
        if (Objects.isNull(resHpcCluster) || ResHpcClusterStatus.FROZEN.equals(resHpcCluster.getStatus())) {
            throw new BizException("当前挂载集群已冻结!");
        }
        ResHpcCluster hpcCluster = resHpcClusterMapper.selectByShareId(resShare.getId());
        //判断是否已经挂载了资源
        if (Objects.nonNull(hpcCluster)) {
            throw new BizException("该弹性文件已挂载资源，不可重复挂载");
        }

        //判断绑定数量
        int bindNum = resHpcClusterMapper.countSfsBindNum(resHpcCluster.getId(), ShareSupportClusterTypeEnum.PREDEPLOY.getCode());
        if (bindNum >= Integer.parseInt(ShareSupportClusterTypeEnum.MOUNT_MAX_NUM.getCode())) {
            throw new BizException("集群可挂载文件系统已超过最大限额");
        }

        //查询云环境
        Criteria criteria = new Criteria();
        criteria.put("cloudEnvType", CloudEnvType.FUSION_DIRECTOR.getValue().get(0));
        List<CloudEnv> cloudEnvs = cloudEnvService.selectByParams(criteria);
        MountPreResShare mountPreResShare = CloudClientFactory.buildMQBean(cloudEnvs.get(0).getId(), MountPreResShare.class);
        //填充挂载请求条件
        mountPreResShare.setId(resShare.getId());
        mountPreResShare.setHpcId(resHpcCluster.getId());
        mountPreResShare.setUuid(resShare.getUuid());
        mountPreResShare.setApplyTaskID("");
        mountPreResShare.setHpcClusterId(request.getResourceId());


        ShareInfo shareInfo = new ShareInfo();
        shareInfo.setMountPath("/"+getMountPath());
        shareInfo.setPathType("Private");

        StorageInfo storageInfo=new StorageInfo();
        storageInfo.setUser(authUser.getAccount());
        storageInfo.setGroup(getMountPath());
        storageInfo.setShareInfo(shareInfo);

        List<StorageInfo> storageInfos=new ArrayList<>();
        storageInfos.add(storageInfo);
        mountPreResShare.setStorageInfo(storageInfos);

        Map<String, String> messageContent = new HashMap<>();
        messageContent.put("userAccount", authUser.getAccount());
        messageContent.put("sfsName", resShare.getName());
        messageContent.put("poolName", resHpcCluster.getName());

        logger.info("弹性文件挂载消息：ResShareServiceImpl.mountPreResShare OUTPUT: content: {}", JSON.toJSONString(messageContent));

        NotificationUtil.assembleBaseMessageContent(messageContent);
        try {
            //发送挂载请求
            String res = MQHelper.sendMessage(mountPreResShare);
            logger.info("卸载预部署弹性文件结果：{}", res);

            //更改SFS的状态为挂载中
            resShare.setStatus(ShareStatus.MOUNTING);
            resShareMapper.updateByPrimaryKey(resShare);

//            BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
//            baseNotificationMqBean.getImsgUserIds().add(authUser.getUserSid());
//            baseNotificationMqBean.setMsgId(NotificationConsts.ConsoleMsg.ProductMsg.TENANT_SFS_MOUNT_SUCCESS);
//            baseNotificationMqBean.setMap(messageContent);
//            rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.OTHER, baseNotificationMqBean);

        } catch (MQException e) {
            logger.info(e.getMessage());
//            BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
//            baseNotificationMqBean.getImsgUserIds().add(authUser.getUserSid());
//            baseNotificationMqBean.setMsgId(NotificationConsts.ConsoleMsg.ProductMsg.TENANT_SFS_MOUNT_ERROR);
//            baseNotificationMqBean.setMap(messageContent);
//            rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.OTHER, baseNotificationMqBean);
        }


        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS),
                resShare.getId());
    }
    @Override
    public RestResult checkMountPreResShare(Long hpcId) {
        //判断绑定数量
        int bindNum = resHpcClusterMapper.countSfsBindNum(hpcId, ShareSupportClusterTypeEnum.PREDEPLOY.getCode());
        if (bindNum >= Integer.parseInt(ShareSupportClusterTypeEnum.MOUNT_MAX_NUM.getCode())) {
            throw new BizException("集群可挂载文件系统已超过最大限额");
        }
        return new RestResult();
    }

    private String getMountPath() {
        Org currentOrgInfo = BasicInfoUtil.getCurrentOrgInfo();
        String path = currentOrgInfo.getLdapOu();
        logger.info("HPC专属资源池-目录获取参数：[{}]", JSONUtil.toJsonStr(currentOrgInfo));
        if (!bssUserRemoteService.checkUserVersion(Convert.toStr(currentOrgInfo.getOrgSid()))) {
            path = currentOrgInfo.getLdapOu();
        }
        logger.info("HPC专属资源池-目录：[{}]", path);
        return path;
    }

    /**
     * 预部署集群文件系统只能创建5个
     * @param orgSid
     */
    private void checkPredeployShare(Long orgSid) {
        if (resShareMapper.checkPredeployShare(orgSid)) {
            throw new BizException("标准专属集群文件系统只能开通5个");
        }
    }

    @Override
    public void updateShareResStatusToPreStatus() {
        resShareMapper.updateShareResStatusToPreStatus();
    }

    @Override
    public List<ResShare> getShareList(Criteria criteria) {
        return resShareMapper.getShareList(criteria);
    }

    @Override
    public Integer countShareList(Criteria criteria) {
        return resShareMapper.countShareList(criteria);
    }

    @Override
    public Integer countAllocShareByDf(Criteria criteria) {
        return resShareMapper.countAllocShareByDf(criteria);
    }

    @Override
    public ResShare getShareById(Long id) {
        return resShareMapper.selectShareDetailByPrimaryKey(id);
    }

    @Override
    public List<ResShareMountTarget> getShareMountTargetList(Criteria criteria) {
        String envType = Convert.toStr(criteria.get("envType"));
        List<ResShareMountTarget> resShareMountTargets = mountTargetMapper.selectByParams(criteria);
        resShareMountTargets.forEach(resShareMountTarget -> {
            if (CloudEnvType.QCLOUD.equals(envType)) {
                criteria.clear();
                criteria.put("groupUuid", resShareMountTarget.getAccessGroup());
                ResShareRightsGroup resShareRightsGroup = CollectionUtil.getFirst(
                        shareRightsGroupMapper.selectByParams(criteria));
                if (Objects.nonNull(resShareRightsGroup)) {
                    resShareMountTarget.setGroupName(resShareRightsGroup.getGroupName());
                }
            } else {
                resShareMountTarget.setGroupName(resShareMountTarget.getAccessGroup());
            }
            // 设置网络信息
            if (CloudEnvType.ALIYUN.equals(envType)) {
                List<Network> networks = networkService.selectNetworksByUuid(resShareMountTarget.getVswInfo());
                if (CollectionUtil.isNotEmpty(networks)) {
                    resShareMountTarget.setNetworkName(networks.get(0).getNetworkName());
                }
            } else if (CloudEnvType.QCLOUD.equals(envType)) {
                resShareMountTarget.setNetworkName(resShareMountTarget.getVswInfo());
            } else {
                List<Network> networks = networkService.selectByParams(
                        new Criteria("uuid", resShareMountTarget.getVswInfo()));
                Network first = CollectionUtil.getFirst(networks);
                if (Objects.nonNull(first)) {
                    resShareMountTarget.setNetworkName(first.getNetworkName());
                }
            }
            // 设置vpc信息
            Criteria example = new Criteria("uuid", resShareMountTarget.getVpcInfo());
            List<ResVpc> resVpcs = resVpcService.selectByParams(example);
            ResVpc first = CollectionUtil.getFirst(resVpcs);
            if (Objects.nonNull(first)) {
                resShareMountTarget.setVpcId(first.getId());
                resShareMountTarget.setVpcName(first.getName());
            }
        });

        return resShareMountTargets;
    }

    /**
     * 发送MQ消息
     *
     * @param base the parameter
     *
     * @return the res inst result
     */
    private ResInstResult sendToMQ(Base base) {
        ResInstResult result;
        try {
            MQHelper.sendMessage(base);
            result = new ResInstResult(ResInstResult.SUCCESS);
        } catch (Exception e) {
            logger.warn("弹性文件服务", e);
            result = new ResInstResult(FAILURE, e.getMessage());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateMountTargetGroup(Criteria criteria) {
        ResShare resShare = resShareMapper.selectByPrimaryKey(Convert.toLong(criteria.get("shareId")));
        if (Objects.isNull(resShare)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1801286422));
        }
        ResShareMountTarget resShareMountTarget = resShareMountTargetMapper.selectByPrimaryKey(
                Convert.toLong(criteria.get("id")));
        if (Objects.isNull(resShareMountTarget)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_228574591));
        }
        // 原始的权限组
        String accessGroupOriginal = resShareMountTarget.getAccessGroup();
        Long groupId = Convert.toLong(criteria.get("groupId"));
        ResShareRightsGroup resShareRightsGroup = null;
        if (Objects.nonNull(groupId)) {
            resShareRightsGroup = shareRightsGroupMapper.selectByPrimaryKey(groupId);
            if (Objects.isNull(resShareRightsGroup)) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1233528506));
            }
        }
        Long cloudEnvId = Convert.toLong(criteria.get("cloudEnvId"));
        ShareMountTargetUpdate shareMountTargetUpdate = CloudClientFactory.buildMQBean(cloudEnvId,
                                                                                       ShareMountTargetUpdate.class);
        shareMountTargetUpdate.setShareUuid(resShare.getUuid());
        if (Objects.nonNull(resShareRightsGroup)) {
            shareMountTargetUpdate.setGroupUuid(resShareRightsGroup.getGroupUuid());
            shareMountTargetUpdate.setGroupName(resShareRightsGroup.getGroupName());
        }
        shareMountTargetUpdate.setMountTargetDomain(resShareMountTarget.getMountTargetDomain());
        shareMountTargetUpdate.setStatus(Convert.toStr(criteria.get("status")));
        try {
            ShareMountTargetUpdateResult result = (ShareMountTargetUpdateResult) MQHelper.rpc(shareMountTargetUpdate);
            if (result.isSuccess()) {
                // 权限组的挂载文件系统数量+1
                shareRightsGroupMapper.increaseMountTargetCountById(groupId);
                // 原始权限组的挂载文件系统数量 -1
                shareRightsGroupMapper.decreaseMountTargetCount(
                        new Criteria("uuid", accessGroupOriginal).put("cloudEnvId", cloudEnvId));

                ResShareMountTarget target = new ResShareMountTarget();
                target.setId(resShareMountTarget.getId());
                if (Objects.nonNull(resShareRightsGroup)) {
                    target.setAccessGroup(resShareRightsGroup.getGroupUuid());
                }
                target.setMountTargetStatus(Convert.toStr(criteria.get("status")));
                return resShareMountTargetMapper.updateByPrimaryKeySelective(target) == 1;
            }
        } catch (MQException e) {
            logger.warn(e.getMessage(), e);
        }
        return false;
    }

    @Override
    public boolean createMountTarget(ResShareMountTarget mountTarget, Long cloudEnvId) {
        ResShare resShare = resShareMapper.selectByPrimaryKey(mountTarget.getShareId());
        if (Objects.isNull(resShare)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1801286422));
        }
        ShareMountTargetCreate shareMountTargetCreate = CloudClientFactory.buildMQBean(cloudEnvId,
                                                                                       ShareMountTargetCreate.class);
        BeanUtil.copyProperties(mountTarget, shareMountTargetCreate);
        shareMountTargetCreate.setShareUuid(resShare.getUuid());
        mountTarget.setAccessGroup(mountTarget.getGroupName());
        try {
            ShareMountTargetCreateResult result = (ShareMountTargetCreateResult) MQHelper.rpc(shareMountTargetCreate);
            if (result.isSuccess()) {
                mountTarget.setMountTargetStatus("Active");
                mountTarget.setMountTargetDomain(result.getMountTargetDomain());
                mountTarget.setOrgSid(BasicInfoUtil.getCurrentOrgSid());
                return resShareMountTargetMapper.insertSelective(mountTarget) == 1;
            }
            throw new BizException(result.getErrMsg());
        } catch (MQException e) {
            logger.warn(e.getMessage(), e);
        }
        return false;
    }

    @Override
    public boolean deleteMountTarget(Long mountTargetId) {
        ResShareMountTarget resShareMountTarget = resShareMountTargetMapper.selectByPrimaryKey(mountTargetId);
        if (Objects.isNull(resShareMountTarget)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_228574591));
        }
        ResShare resShare = resShareMapper.selectByPrimaryKey(resShareMountTarget.getShareId());
        if (Objects.isNull(resShare)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1801286422));
        }

        Criteria criteria = new Criteria();
        criteria.put("mountTargetSid", mountTargetId);
        criteria.put("statusEqual", "installed");
        List<ResShareTarget> resShareTargets = resShareTargetMapper.selectTargetByParam(criteria);
        if (CollectionUtils.isNotEmpty(resShareTargets)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_544490797));
        }

        ShareMountTargetDelete shareMountTargetDelete = CloudClientFactory.buildMQBean(resShare.getCloudEnvId(),
                                                                                       ShareMountTargetDelete.class);
        shareMountTargetDelete.setShareUuid(resShare.getUuid());
        shareMountTargetDelete.setMountTargetDomain(resShareMountTarget.getMountTargetDomain());
        try {
            ShareMountTargetDeleteResult result = (ShareMountTargetDeleteResult) MQHelper.rpc(shareMountTargetDelete);
            if (result.isSuccess()) {
                return resShareMountTargetMapper.deleteByPrimaryKey(resShareMountTarget.getId()) == 1;
            }
            throw new BizException(result.getErrMsg());
        } catch (MQException e) {
            logger.warn(e.getMessage(), e);
        }
        return false;
    }

    @Override
    public ResShare selectByPrimaryKey(Long id) {
        return resShareMapper.selectByPrimaryKey(id);
    }

    @Override
    public Boolean checkShareCanDelete(Long id) {
        ResShare resShare = this.getShareById(id);
        if (null == resShare) {
            return true;
        }

        CloudEnv cloudEnv = cloudEnvService.selectByPrimaryKey(resShare.getCloudEnvId());
        if (Objects.isNull(cloudEnv)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_951179418));
        }
        Criteria criteria = new Criteria();
        criteria.put("shareId", id);
        criteria.put("statusEqual", "installed");
        List<ResShareTarget> resShareTargets = this.selectTargetHost(criteria);
        if (!CollectionUtils.isEmpty(resShareTargets)) {
            List<String> hostIds = resShareTargets.stream()
                                                  .map(ResShareTarget::getResVmId)
                                                  .collect(Collectors.toList());
            Criteria example = new Criteria();
            example.put("hostIds", hostIds);
            example.put("statusNotEqualsList", Lists.newArrayList(ResVmStatus.DELETING, ResVmStatus.DELETED));
            boolean hostExist = resVmService.countByExample(example) > 0;
            if (hostExist) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2089099606));
            }
        }
        return true;
    }

    @Override
    public List<ResShare> selectSelfServiceByParams(Criteria criteria) {
        return resShareMapper.selectSelfServiceByParams(criteria);
    }

    @Override
    public RestResult renewInstance(RenewBasicResourceDTO rbr, AuthUser authUser) {
        ResShare resShare = this.resShareMapper.selectByPrimaryKey(Long.valueOf(rbr.getResourceId()));
        AssertUtil.requireNonBlank(resShare, "该弹性文件服务不存在,请刷新后重新");
        // 没有结束时间是永久资源，不修改
        if (null != resShare.getEndTime()) {
            Date newEndTime = cn.com.cloudstar.rightcloud.common.util.DateUtil.plusMonths(resShare.getEndTime(),
                                                                                          rbr.getPeriod());
            ResShare t = new ResShare();
            t.setStartTime(resShare.getStartTime());
            t.setEndTime(newEndTime);
            t.setId(resShare.getId());
            this.resShareMapper.updateLifeCycleByPrimaryKey(t);
            resShare.setEndTime(newEndTime);
            basicResActionLogService.insertIntoActionLog(Objects.nonNull(authUser) ? authUser.getAccount() : "admin",
                                                         ResourceOperateEnum.RENEW_INSTANCE, ResourceTypeEnum.RENEW_SFS,
                                                         resShare.getId(), resShare.getName(), Boolean.TRUE, null, null,
                                                         authUser.getOrgSid());
        }
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS),
                              resShare.getEndTime());
    }

    @Override
    public int countShareByParams(Criteria criteria) {
        return resShareMapper.countShareByParams(criteria);
    }

    @Override
    public Integer getShareAllocateSize(Long shareTypeId) {
        return resShareMapper.getShareAllocateSize(shareTypeId);
    }

    @Override
    public List<ResShare> selectShareListByOrgId(Long orgId) {
        return resShareMapper.selectShareListByOrgId(orgId);
    }

    @Override
    public int updateShareStatusByPrimaryKey(Long id, String status) {
        return resShareMapper.updateShareStatusByPrimaryKey(id, status);
    }

    @Override
    public List<ResShare> sureMountPreResShare(Long userSid) {
        return resShareMapper.sureMountPreResShare(userSid.toString());
    }

    @Override
    public List<ResShare> getQuotaSfs(Long orgSid, Long shareId) {
        return resShareMapper.getQuotaSfs(orgSid, shareId);
    }

    @Override
    public List<ResShare> getByPrimaryKeys(List<Long> shareIds) {
        return resShareMapper.getByPrimaryKeys(shareIds);
    }

    @Override
    public ResShare getById(Long shareId) {
        return resShareMapper.getById(shareId);
    }

    @Override
    public int countByParams(Criteria criteria) {
        return resShareMapper.countByParams(criteria);
    }

    @Override
    public List<ResShare> selectByParams(Criteria criteria) {
        return resShareMapper.selectByParams(criteria);
    }

    @Override
    public BigDecimal sumSizeByIds(List<Long> ids) {
        return resShareMapper.sumSizeByIds(ids);
    }

    @Override
    public int updateByPrimaryKey(ResShare resShare) {
        return resShareMapper.updateByPrimaryKey(resShare);
    }

    /**
     *     通过OceanStor 创建共享
     *
     *     查询租户--->创建租户（不存在）--->创建文件系统—》创建QOS---》关联QOS和共享—》创建配额--->创建分级
     */

    @Transactional(rollbackFor = Exception.class)
    @Override
    public RestResult createShareByOceanStor(ResOceanstorShare oceanstorShare) {

        ResShare resShare = new ResShare();
        BeanUtils.copyProperties(oceanstorShare,resShare);

        Date current = new Date();


        //查询OceanStor 云环境ID
        Criteria criteria = new Criteria();
        criteria.put("cloudEnvType", CloudEnvType.OCEANSTOR_PACIFIC.getValue().get(0));
        List<CloudEnv> cloudEnvs = cloudEnvService.selectByParams(criteria);

        if(CollectionUtil.isEmpty(cloudEnvs)){
            throw new BizException(StrUtil.format(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_947266489),CloudEnvType.OCEANSTOR_PACIFIC.getDesc()));
        }
        CloudEnv cloudEnv = cloudEnvs.get(0);
        Long cloudEnvId = cloudEnv.getId();

        resShare.setStatus(ShareStatus.CREATING);
        if (!Strings.isNullOrEmpty(resShare.getShareType())) {
            resShare.setShareType(resShare.getShareType().toUpperCase());
        }
        if (StringUtils.isNotBlank(resShare.getName()) && StringUtils.isBlank(resShare.getDescription())) {
            resShare.setDescription(resShare.getName());
        }
        resShare.setShareTypeName("oceanstor");
        resShare.setUsedSize(BigDecimal.ZERO);
        resShare.setFileSystemName(resShare.getName());
        resShareMapper.insertSelective(resShare);
        oceanstorShare.setId(resShare.getId());

        Future<Boolean> booleanFuture = ThreadUtil.execAsync(() -> createOceanStorFlow(oceanstorShare, cloudEnv));

        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS),
                oceanstorShare.getId());


    }

    @Transactional(rollbackFor = Exception.class)
    public boolean createOceanStorFlow(ResOceanstorShare oceanstorShare, CloudEnv cloudEnv) {
        final Map<String,String> resultMap = new HashMap<>();
        final String errMsgKey = "errMsg";
        resultMap.put(errMsgKey,"");
        boolean success = true;

        ResShare resShare = new ResShare();
        BeanUtils.copyProperties(oceanstorShare,resShare);

        ShareCreateResult shareCreateResult = new ShareCreateResult();
        CompletableFuture<Boolean> createOceanstor = CompletableFuture
                .supplyAsync(() -> {
                    Boolean result = true;
                    //截取OwnerOrgName
                    subOwnerOrgName(oceanstorShare);

                    //查询租户
                    ResOceanstorPAccount resOceanstorPAccount = getResOceanstorPAccount(oceanstorShare,cloudEnv);


                    //创建文件系统,命名空间
                    OceanStorNamespacesCreateResult namespacesCreateResult = createOceanStorNamespaces(oceanstorShare,cloudEnv, resOceanstorPAccount);
                    if(namespacesCreateResult.isSuccess()){
                        //创建QoS：
                        OceanStorConvergedQosPolicyCreateResult qosPolicyCreateResult = getOceanStorConvergedQosPolicyCreateResult(oceanstorShare,cloudEnv,resOceanstorPAccount,namespacesCreateResult);

                        if(qosPolicyCreateResult.isSuccess()){
                            String qosScale = "0";

                            logger.info("创建前解除Qos策略与对象关联spaceName={}",namespacesCreateResult.getName());
                            OceanStorConvergedQosAssociationDelete qosAssociationDelete = new OceanStorConvergedQosAssociationDelete();
                            qosAssociationDelete.setQos_scale(qosScale);
                            qosAssociationDelete.setObject_name(namespacesCreateResult.getName());
                            oceanStorService.oceanStorConvergedQosAssociationDelete(qosAssociationDelete,cloudEnv.getId());

                            //关联QOS和共享
                            OceanStorConvergedQosAssociationCreate qosAssociationCreate = new OceanStorConvergedQosAssociationCreate();

                            qosAssociationCreate.setQos_scale(qosScale);
                            qosAssociationCreate.setAccount_id(resOceanstorPAccount.getResourceId());
                            qosAssociationCreate.setObject_name(namespacesCreateResult.getName());
                            qosAssociationCreate.setQos_policy_id(qosPolicyCreateResult.getId());
                            BaseResult baseResult = oceanStorService.oceanStorConvergedQosAssociationCreate(qosAssociationCreate, cloudEnv.getId());
                            logger.info("StorConvergedQosAssociationCreateResult MQResult:{}",baseResult);
                            if(baseResult.isSuccess()){
                                logger.info("关联QOS和共享成功");
                            }else{
                                logger.error("关联QOS和共享失败");
                            }
                            //创建配额
                            OceanStorNamespacesQuotaCreateResult quotaCreateResult = createResOceanstorPQuota(oceanstorShare,cloudEnv, resOceanstorPAccount, namespacesCreateResult);

                            if(quotaCreateResult.isSuccess()){
                                //创建分级
                                String tierFlag = oceanstorShare.getTierFlag();
                                logger.info("ResShareServiceImpl.createOceanStorFlow 分级启用flag：{}",tierFlag);
                                if(StringUtils.equalsIgnoreCase(tierFlag, ResOceanstorShare.TIER_FLAG_ON)){
                                    OceanStorTierPoliciesCreateResult oceanStorTierPoliciesCreateResult = createTierPolicy(oceanstorShare,cloudEnv,resOceanstorPAccount, namespacesCreateResult);
                                    if(!oceanStorTierPoliciesCreateResult.isSuccess()){
                                        result=false;
                                        resultMap.put(errMsgKey,oceanStorTierPoliciesCreateResult.getErrMsg());
                                    }
                                }
                            }else{
                                result=false;
                                resultMap.put(errMsgKey,quotaCreateResult.getErrMsg());
                            }

                        }else{
                            result=false;
                            resultMap.put(errMsgKey,qosPolicyCreateResult.getErrMsg());
                        }
                        resShare.setName(namespacesCreateResult.getName());
                        resShare.setExportLocation(namespacesCreateResult.getName());
                        resShare.setUuid(namespacesCreateResult.getId());

                        resultMap.put("nameSpacesId",namespacesCreateResult.getId());
                    }else{
                        result=false;
                        resultMap.put(errMsgKey, namespacesCreateResult.getErrMsg());
                    }
                    return result;
                });
        try {
            success = createOceanstor.get(40, TimeUnit.SECONDS);
        } catch (Exception e) {
            success = false;
            resultMap.put(errMsgKey,StrUtil.format("Oceanstor服务发放异常：{}",e.getMessage()));
            logger.error("Oceasnstor服务发放异常", e);
        }



        String oceanstorNameSpacesId = resultMap.get("nameSpacesId");


        shareCreateResult = new ShareCreateResult();
        shareCreateResult.setId(resShare.getId());
        shareCreateResult.setOrgSid(resShare.getOrgSid().toString());
        shareCreateResult.setCloudEnvId(resShare.getCloudEnvId());
        shareCreateResult.setExportLocation(resShare.getExportLocation());
        shareCreateResult.setServiceOrderId(resShare.getServiceOrderId());

        resShare.setCloudEnvId(cloudEnv.getId());
        if(success){
            resShare.setStatus(ShareStatus.AVAILABLE);

            if(StringUtils.isNotEmpty(oceanstorNameSpacesId)){
                shareCreateResult.setOceanStorResourceId(Long.valueOf(oceanstorNameSpacesId));
            }
            shareCreateResult.setSuccess(true);

        }else{
            resShare.setStatus(ShareStatus.ERROR);
            String errMsg = resultMap.get(errMsgKey);
            resShare.setErrorMsg(errMsg);
            if (errMsg.contains("v1/rich/CCHPCAppMgmtService/HPCStorage/ApplyFileSystem")) {
                resShare.setErrorMsg("创建文件服务失败，文件服务个数已达设置最大数");
            }
            shareCreateResult.setSuccess(false);
            shareCreateResult.setServiceOrderId(resShare.getServiceOrderId());
        }
        resShareMapper.updateByPrimaryKey(resShare);

        String exchange = "biz.notify.exchange";
        String routingKey = "biz.notify";
        logger.info("通知业务系统开始:exchange:{},routingkey:{}", exchange, routingKey);
        logger.info("oceanstor创建结束,MQ参数：{}",shareCreateResult);
        rabbitTemplate.convertAndSend(exchange, routingKey, shareCreateResult);

        return success;

    }


    private void subOwnerOrgName(ResOceanstorShare oceanstorShare) {
        //创建存储名称接口限制在64个字符,最长的是Policy_ownerOrgName_orgSid_ownerId,限制在60个字
        String ownerOrgName = oceanstorShare.getOwnerOrgName();
        Long orgSid = oceanstorShare.getOrgSid();
        String ownerId = oceanstorShare.getOwnerId();

        String judgmentName = "Policy_"+ownerOrgName+"_" + orgSid + "_" + ownerId;
        int overLength = judgmentName.length() - 60;
        if(overLength>0){
            if(ownerOrgName.length()-overLength>0){
                ownerOrgName = StringUtils.substring(ownerOrgName,0,ownerOrgName.length()-overLength);
                oceanstorShare.setOwnerOrgName(ownerOrgName);
            }

        }
    }

    private OceanStorTierPoliciesCreateResult createTierPolicy(ResOceanstorShare resOceanstorShare, CloudEnv cloudEnv, ResOceanstorPAccount resOceanstorPAccount, OceanStorNamespacesCreateResult namespacesCreateResult) {
        Date current = new Date();
        Long cloudEnvId =cloudEnv.getId() ;

        String ownerId = resOceanstorShare.getOwnerId();
        Long orgSid = resOceanstorShare.getOrgSid();
        Long createdOrgSid = resOceanstorShare.getCreatedOrgSid();
        String createdBy = resOceanstorShare.getCreatedBy();

        String tierPolicyName = "Policy_"+namespacesCreateResult.getName();


        ResOceanstorPTierPolicyExample pTierPolicyExample = new ResOceanstorPTierPolicyExample();
        pTierPolicyExample.createCriteria().andCloudEnvIdEqualTo(cloudEnvId)
                .andFsIdEqualTo(Long.valueOf(namespacesCreateResult.getId()))
                .andOwnerIdEqualTo(ownerId);

        List<ResOceanstorPTierPolicy> pTierPolicies = resOceanstorPTierPolicyMapper.selectByExample(pTierPolicyExample);

        if(pTierPolicies.size()>0){
            logger.info("分级已存在，cloudEnvId={},FsId={},ownereId={}",cloudEnvId,namespacesCreateResult.getId(),ownerId);
            OceanStorTierPoliciesCreateResult tierPoliciesCreateResult = new OceanStorTierPoliciesCreateResult();
            tierPoliciesCreateResult.setSuccess(true);
            return tierPoliciesCreateResult;

        }

        logger.info("创建前删除分级 name={},fsId={}",tierPolicyName,namespacesCreateResult.getId());
        OceanStorTierPoliciesDelete tierPoliciesDelete = new OceanStorTierPoliciesDelete();
        tierPoliciesDelete.setName(tierPolicyName);
        tierPoliciesDelete.setFs_id(namespacesCreateResult.getId());
        oceanStorService.oceanStorTierPoliciesDelete(tierPoliciesDelete, cloudEnvId);



        OceanStorTierPoliciesCreate tierPoliciesCreate = new OceanStorTierPoliciesCreate();
        logger.info("创建分级 name={}",tierPolicyName);
        tierPoliciesCreate.setName(tierPolicyName);
        tierPoliciesCreate.setFs_id(namespacesCreateResult.getId());

        String tierConfigJson = resOceanstorShare.getTierConfigJson();
        logger.info("分级配置:{}",tierConfigJson);
        JSONObject tierPolicyConfigJOb = new JSONObject();
        try {

            tierPolicyConfigJOb = JSONObject.parseObject(tierConfigJson);



        }catch (Exception e){
            logger.error("分级配置解析异常:",e);
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1727129051));
        }
        String strategy = tierPolicyConfigJOb.getString("strategy");
        String migrationType = tierPolicyConfigJOb.getString("migration_type");
        String atimeOperator = tierPolicyConfigJOb.getString("atime_operator");
        String atime =tierPolicyConfigJOb.getString("atime");
        String atimeUnit = tierPolicyConfigJOb.getString("atime_unit");

        tierPoliciesCreate.setStrategy(strategy);
        tierPoliciesCreate.setMigration_type(migrationType);
        tierPoliciesCreate.setAtime_operator(atimeOperator);
        tierPoliciesCreate.setAtime(atime);
        tierPoliciesCreate.setAtime_unit(atimeUnit);
        OceanStorTierPoliciesCreateResult tierPoliciesCreateResult = oceanStorService.oceanStorTierPoliciesCreate(tierPoliciesCreate, cloudEnvId);
        logger.info("tierPoliciesCreateResult MQResult:{}",tierPoliciesCreateResult);
        //分级入库
        ResOceanstorPTierPolicy tierPolicy = new ResOceanstorPTierPolicy();

        tierPolicy.setName(tierPolicyName);
        tierPolicy.setFsId(Long.valueOf(namespacesCreateResult.getId()));
        tierPolicy.setStrategy(Byte.valueOf(strategy));
        tierPolicy.setMigrationType(Byte.valueOf(migrationType));
        tierPolicy.setAtimeOperator(Byte.valueOf(atimeOperator));
        tierPolicy.setAtime(Long.valueOf(atime));
        tierPolicy.setAtimeUnit(atimeUnit);

        tierPolicy.setCloudEnvId(cloudEnvId);
        tierPolicy.setOwnerId(ownerId);
        tierPolicy.setOrgSid(orgSid);
        if(createdOrgSid !=null){
            tierPolicy.setCreatedOrgSid(createdOrgSid.toString());
        }
        tierPolicy.setEncryptOption(resOceanstorPAccount.getEncryptOption());
        tierPolicy.setVersion(VERSION);
        tierPolicy.setCreatedDt(current);
        tierPolicy.setUpdatedDt(current);
        tierPolicy.setCreatedBy(createdBy);
        tierPolicy.setUpdatedBy(createdBy);

        if(tierPoliciesCreateResult.isSuccess()){
            tierPolicy.setResourceId(Long.valueOf(tierPoliciesCreateResult.getId()));
            tierPolicy.setStatus(ACTIVE);
            logger.info("创建分级 name={}成功",tierPolicyName);
            resOceanstorPTierPolicyMapper.insert(tierPolicy);
        }else{
            logger.error("创建分级 name={}失败",tierPolicyName);
        }
        return tierPoliciesCreateResult;
    }

    private OceanStorNamespacesQuotaCreateResult createResOceanstorPQuota(ResOceanstorShare resShare, CloudEnv cloudEnv, ResOceanstorPAccount resOceanstorPAccount, OceanStorNamespacesCreateResult namespacesCreateResult) {
        Date current = new Date();
        Long cloudEnvId = cloudEnv.getId();

        String ownerId = resShare.getOwnerId();
        //组织ou信息
        Long orgSid = resShare.getOrgSid();
        Long createdOrgSid = resShare.getCreatedOrgSid();
        String createdBy = resShare.getCreatedBy();
        String nameSpacesId = namespacesCreateResult.getId();

        ResOceanstorPQuotaExample pQuotaExample = new ResOceanstorPQuotaExample();
        pQuotaExample.createCriteria().andCloudEnvIdEqualTo(cloudEnvId)
                .andOwnerIdEqualTo(ownerId)
                .andParentIdEqualTo(nameSpacesId);
        List<ResOceanstorPQuota> resOceanstorPQuotas = resOceanstorPQuotaMapper.selectByExample(pQuotaExample);
        if(resOceanstorPQuotas.size()>0){
            ResOceanstorPQuota resOceanstorPQuota = resOceanstorPQuotas.get(0);

            logger.info("cloudEvnId={},ownerId={},nameSpaceId={},配额{}已存在",cloudEnvId,ownerId,nameSpacesId,resOceanstorPQuota.getResourceId());

            OceanStorNamespacesQuotaCreateResult quotaCreateResult = new OceanStorNamespacesQuotaCreateResult();
            quotaCreateResult.setSuccess(true);

            return quotaCreateResult;

        }

        OceanStorNamespacesQuotaCreate quotaCreate = new OceanStorNamespacesQuotaCreate();
        //取值40，代表命名空间
        String parentType = "40";
        //1 代表目录配额
        String quotaType = "1";
        //取值3，代表GB
        String spaceUnitType = "3";

        String directoryQuotaTarget = "1";


        quotaCreate.setParent_type(parentType);
        quotaCreate.setParent_id(nameSpacesId);
        quotaCreate.setDirectory_quota_target(directoryQuotaTarget);
        quotaCreate.setQuota_type(quotaType);
        Integer size = resShare.getSize();
        quotaCreate.setSpace_hard_quota(size.toString());
        quotaCreate.setSpace_unit_type(spaceUnitType);

        OceanStorNamespacesQuotaCreateResult quotaCreateResult = oceanStorService.oceanStorNamespacesQuotaCreate(quotaCreate, cloudEnvId);
        logger.info("quotaCreateResult MQResult:{}",quotaCreateResult);

        //配额入库

        ResOceanstorPQuota resOceanstorPQuota = new ResOceanstorPQuota();

        resOceanstorPQuota.setParentType(parentType);
        resOceanstorPQuota.setParentId(nameSpacesId);
        resOceanstorPQuota.setQuotaType(Byte.valueOf(quotaType));
        resOceanstorPQuota.setSpaceHardQuota(size.longValue());
        resOceanstorPQuota.setSpaceUnitType(Byte.valueOf(spaceUnitType));

        resOceanstorPQuota.setCloudEnvId(cloudEnvId);
        resOceanstorPQuota.setOwnerId(ownerId);
        resOceanstorPQuota.setOrgSid(orgSid);
        if(createdOrgSid !=null){
            resOceanstorPQuota.setCreatedOrgSid(createdOrgSid.toString());
        }
        resOceanstorPQuota.setEncryptOption(resOceanstorPAccount.getEncryptOption());
        resOceanstorPQuota.setVersion(VERSION);
        resOceanstorPQuota.setCreatedDt(current);
        resOceanstorPQuota.setUpdatedDt(current);
        resOceanstorPQuota.setCreatedBy(createdBy);
        resOceanstorPQuota.setUpdatedBy(createdBy);

        if(quotaCreateResult.isSuccess()){
            //resourceId  格式为parentID@quotaId
            resOceanstorPQuota.setResourceId(quotaCreateResult.getId());
            resOceanstorPQuota.setStatus(ACTIVE);
            logger.info("创建配额{} GB成功：", size);
            resOceanstorPQuotaMapper.insert(resOceanstorPQuota);
        }else{
            logger.info("创建配额{} GB失败",size);
        }

        return quotaCreateResult;
    }

    private OceanStorConvergedQosPolicyCreateResult getOceanStorConvergedQosPolicyCreateResult(ResOceanstorShare resOceanstorShare, CloudEnv cloudEnv, ResOceanstorPAccount resOceanstorPAccount, OceanStorNamespacesCreateResult namespacesCreateResult) {
        Date current = new Date();
        Long cloudEnvId = cloudEnv.getId();

        String ownerId = resOceanstorShare.getOwnerId();
        //组织ou信息
        Long orgSid = resOceanstorShare.getOrgSid();
        Long createdOrgSid = resOceanstorShare.getCreatedOrgSid();
        String createdBy = resOceanstorShare.getCreatedBy();
        String accountId = resOceanstorPAccount.getResourceId();

        String policyName = "qos_"+namespacesCreateResult.getName();



        ResOceanstorPQosPolicyExample pQosPolicyExample = new ResOceanstorPQosPolicyExample();
        pQosPolicyExample.createCriteria().andNameEqualTo(policyName)
                .andRefAccountRidEqualTo(accountId)
                .andCloudEnvIdEqualTo(cloudEnvId);
        List<ResOceanstorPQosPolicy> resOceanstorPQosPolicies = resOceanstorPQosPolicyMapper.selectByExample(pQosPolicyExample);
        if(resOceanstorPQosPolicies.size()>0){
            logger.info("创建Qos策略{}已存在",policyName);

            ResOceanstorPQosPolicy qosPolicy = resOceanstorPQosPolicies.get(0);

            OceanStorConvergedQosPolicyCreateResult qosPolicyCreateResult = new OceanStorConvergedQosPolicyCreateResult();
            qosPolicyCreateResult.setSuccess(true);
            qosPolicyCreateResult.setId(qosPolicy.getResourceId().toString());

            return qosPolicyCreateResult;
        }

        String qosScale = "0";
        logger.info("创建前删除Qos策略 policyName={}",policyName);
        OceanStorConvergedQosPolicyDelete qosPolicyDelete = new OceanStorConvergedQosPolicyDelete();
        qosPolicyDelete.setName(policyName);
        qosPolicyDelete.setQos_scale(qosScale);
        BaseResult baseResult = oceanStorService.oceanStorConvergedQosPolicyDelete(qosPolicyDelete, cloudEnvId);

        OceanStorConvergedQosPolicyCreate qosPolicyCreate = new OceanStorConvergedQosPolicyCreate();

        qosPolicyCreate.setName(policyName);
        logger.info("创建Qos策略 name={}",policyName);

        //0 0：NAMESPACE ：命名空间 1：CLIENT：客户端。 2：ACCOUNT：帐户

        String qosMode = "2";

        qosPolicyCreate.setQos_scale(qosScale);
        qosPolicyCreate.setQos_mode(qosMode);
        qosPolicyCreate.setAccount_id(accountId);

        String qosConfigJson = resOceanstorShare.getQosConfigJson();
        logger.info("Qos配置:{}",qosConfigJson);
        JSONObject qosConfigJOb = new JSONObject();
        try {
            qosConfigJOb = JSONObject.parseObject(qosConfigJson);



        }catch (Exception e){
            logger.error("Qos配置解析异常:",e);
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_305976391));
        }
        String packageSize = qosConfigJOb.getString("package_size");
        String maxIops = qosConfigJOb.getString("max_iops");
        String maxBandWidth = qosConfigJOb.getString("max_band_width");
        String basicBandWidth = qosConfigJOb.getString("basic_band_width");
        String bpsDensity = qosConfigJOb.getString("bps_density");

        qosPolicyCreate.setPackage_size(packageSize.toString());
        qosPolicyCreate.setMax_iops(maxIops.toString());
        qosPolicyCreate.setMax_band_width(maxBandWidth.toString());
        qosPolicyCreate.setBasic_band_width(basicBandWidth.toString());
        qosPolicyCreate.setBps_density(bpsDensity.toString());

        OceanStorConvergedQosPolicyCreateResult qosPolicyCreateResult = oceanStorService.oceanStorConvergedQosPolicyCreate(qosPolicyCreate, cloudEnvId);
        logger.info("qosPolicyCreateResult MQResult:{}",qosPolicyCreateResult);
        //Qos策略入库
        ResOceanstorPQosPolicy qosPolicy = new ResOceanstorPQosPolicy();

        qosPolicy.setRefAccountRid(accountId);
        qosPolicy.setQosScale(Byte.valueOf(qosScale));
        qosPolicy.setName(policyName);
        qosPolicy.setPackageSize(Long.valueOf(packageSize));
        qosPolicy.setMaxIops(Long.valueOf(maxIops));
        qosPolicy.setMaxBandWidth(Long.valueOf(maxBandWidth));
        qosPolicy.setBasicBandWidth(Long.valueOf(basicBandWidth));
        qosPolicy.setBpsDensity(Long.valueOf(bpsDensity));
        qosPolicy.setRefObjectName(namespacesCreateResult.getName());
        qosPolicy.setQosMode(Byte.valueOf(qosMode));

        qosPolicy.setCloudEnvId(cloudEnvId);
        qosPolicy.setOwnerId(ownerId);
        qosPolicy.setOrgSid(orgSid);
        if(createdOrgSid !=null){
            qosPolicy.setCreatedOrgSid(createdOrgSid.toString());
        }
        qosPolicy.setEncryptOption(resOceanstorPAccount.getEncryptOption());
        qosPolicy.setVersion(VERSION);
        qosPolicy.setCreatedDt(current);
        qosPolicy.setUpdatedDt(current);
        qosPolicy.setCreatedBy(createdBy);
        qosPolicy.setUpdatedBy(createdBy);

        if(qosPolicyCreateResult.isSuccess()){
            qosPolicy.setResourceId(Long.valueOf(qosPolicyCreateResult.getId()));
            qosPolicy.setStatus(ACTIVE);
            resOceanstorPQosPolicyMapper.insert(qosPolicy);
        }else{
            logger.info("创建Qos策略 name={}失败",policyName);
        }
        return qosPolicyCreateResult;
    }

    private OceanStorNamespacesCreateResult createOceanStorNamespaces(ResOceanstorShare resShare, CloudEnv cloudEnv, ResOceanstorPAccount resOceanstorPAccount) {
        Date current = new Date();
        String ownerId = resShare.getOwnerId();
        String ownerOrgName = resShare.getOwnerOrgName();
        Long orgSid = resShare.getOrgSid();
        Long createdOrgSid = resShare.getCreatedOrgSid();
        String createdBy = resShare.getCreatedBy();
        Long cloudEnvId = cloudEnv.getId();
        String accountId = resOceanstorPAccount.getResourceId();
        String spaceName = ownerOrgName + "_" + orgSid + "_" + ownerId;


        ResOceanstorPNamespaceExample pNamespaceExample = new ResOceanstorPNamespaceExample();
        pNamespaceExample.createCriteria().andCloudEnvIdEqualTo(cloudEnvId)
                .andRefAccountRidEqualTo(Long.valueOf(accountId))
                .andNameEqualTo(spaceName);
        List<ResOceanstorPNamespace> resOceanstorPNamespaces = resOceanstorPNamespaceMapper.selectByExample(pNamespaceExample);

        if(resOceanstorPNamespaces.size()>0){
            ResOceanstorPNamespace resOceanstorPNamespace = resOceanstorPNamespaces.get(0);

            logger.info("命名空间{}已存在",spaceName);
            OceanStorNamespacesCreateResult storNamespacesCreateResult = new OceanStorNamespacesCreateResult();
            storNamespacesCreateResult.setSuccess(true);

            storNamespacesCreateResult.setName(resOceanstorPNamespace.getName());
            storNamespacesCreateResult.setId(resOceanstorPNamespace.getResourceId().toString());
            return storNamespacesCreateResult;
        }
        Map envAttrDataMap = JsonUtil.fromJson(CrytoUtilSimple.decrypt(cloudEnv.getAttrData()), Map.class);




        logger.info("创建前删除文件系统命名空间，name={}",spaceName);
        OceanStorNamespacesDelete namespacesDelete = new OceanStorNamespacesDelete();
        namespacesDelete.setName(spaceName);
        BaseResult baseResult = oceanStorService.oceanStorNamespacesDelete(namespacesDelete, cloudEnvId);

        OceanStorNamespacesCreate namespacesCreate = new OceanStorNamespacesCreate();

        logger.info("创建命名空间name={}",spaceName);
        String protocalType = "3";
        String atimeUpdateMode="3600";
        String storagePoolId = envAttrDataMap.get("storageId").toString();
        String closeStatus  = "0";

        namespacesCreate.setName(spaceName);
        /**
         * 0：NAS 2：HDFS 3：Interworking
         */
        namespacesCreate.setProtocol_type(protocalType);
        namespacesCreate.setAccount_id(accountId);
        //3600:1小时
        namespacesCreate.setAtime_update_mode(atimeUpdateMode);
        namespacesCreate.setStorage_pool_id(storagePoolId);
        namespacesCreate.setAudit_log_rule(closeStatus);
        namespacesCreate.setAudit_log_switch(closeStatus);
        OceanStorNamespacesCreateResult namespacesCreateResult = oceanStorService.oceanStorNamespacesCreate(namespacesCreate, cloudEnvId);
        logger.info("namespacesCreateResult MQResult:{}",namespacesCreateResult);

        //命名空间入库
        ResOceanstorPNamespace resOceanstorPNamespace = new ResOceanstorPNamespace();
        resOceanstorPNamespace.setName(spaceName);
        resOceanstorPNamespace.setCloudEnvId(cloudEnvId);
        resOceanstorPNamespace.setOwnerId(ownerId);
        resOceanstorPNamespace.setProtocolType(Byte.valueOf(protocalType));
        resOceanstorPNamespace.setOrgSid(orgSid);
        if(createdOrgSid !=null){
            resOceanstorPNamespace.setCreatedOrgSid(createdOrgSid.toString());
        }
        resOceanstorPNamespace.setStoragePoolId(Long.valueOf(storagePoolId));
        resOceanstorPNamespace.setAtimeUpdateMode(Long.valueOf(atimeUpdateMode));
        resOceanstorPNamespace.setEncryptOption(resOceanstorPAccount.getEncryptOption());
        resOceanstorPNamespace.setVersion(VERSION);
        resOceanstorPNamespace.setCreatedDt(current);
        resOceanstorPNamespace.setUpdatedDt(current);
        resOceanstorPNamespace.setCreatedBy(createdBy);
        resOceanstorPNamespace.setUpdatedBy(createdBy);

        if(namespacesCreateResult.isSuccess()){
            resOceanstorPNamespace.setResourceId(Long.valueOf(namespacesCreateResult.getId()));
            resOceanstorPNamespace.setRefAccountRid(Long.valueOf(namespacesCreateResult.getAccount_id()));
            resOceanstorPNamespace.setStripSize(Long.valueOf(namespacesCreateResult.getStrip_size()));
            resOceanstorPNamespace.setStatus(ACTIVE);
            resOceanstorPNamespaceMapper.insert(resOceanstorPNamespace);
        }else{
            logger.info("创建命名空间name={}失败",spaceName);
        }
        return namespacesCreateResult;
    }

    private ResOceanstorPAccount getResOceanstorPAccount(ResOceanstorShare resShare, CloudEnv cloudEnv) {
        Date current = new Date();
        String ownerId = resShare.getOwnerId();
        //组织ou信息
        String ownerOrgName = resShare.getOwnerOrgName();
        Long orgSid = resShare.getOrgSid();
        Long createdOrgSid = resShare.getCreatedOrgSid();
        String createdBy = resShare.getCreatedBy();
        Long cloudEnvId = cloudEnv.getId();

        //查询账户
        ResOceanstorPAccount resOceanstorPAccount = new ResOceanstorPAccount();
        ResOceanstorPAccountExample resOPAExample = new ResOceanstorPAccountExample();
        resOPAExample.createCriteria().andCloudEnvIdEqualTo(cloudEnvId).andOwnerIdEqualTo(ownerId);
        List<ResOceanstorPAccount> resOceanstorPAccounts = resOceanstorPAccountMapper.selectByExample(resOPAExample);
        if(!CollectionUtil.isEmpty(resOceanstorPAccounts)){
            resOceanstorPAccount = resOceanstorPAccounts.get(0);
            logger.info("查询OceanStor账户resourceId={},name={}",resOceanstorPAccount.getResourceId(),resOceanstorPAccount.getName());
            OceanStorAccount oceanStorAccount = new OceanStorAccount();
            oceanStorAccount.setId(resOceanstorPAccount.getOrgSid().toString());
            OceanStorAccountResult oceanStorAccountResult = oceanStorService.oceanStorAccount(oceanStorAccount, cloudEnvId);

            if(!oceanStorAccountResult.isSuccess()){
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_862306984));
            }
            resOceanstorPAccount.setStatus(oceanStorAccountResult.getStatus());
            resOceanstorPAccountMapper.updateByPrimaryKeySelective(resOceanstorPAccount);

        }else{
            // 创建租户（不存在）
            logger.info("查询OceanStor账户不存在,创建账户{}",ownerOrgName);
            OceanStorAccountCreate storAccountCreate = new OceanStorAccountCreate();
            storAccountCreate.setName(ownerOrgName);
            storAccountCreate.setId(orgSid.toString());
            OceanStorAccountCreateResult storAccountCreateResult = oceanStorService.oceanStorAccountCreate(storAccountCreate, cloudEnvId);
            if(!storAccountCreateResult.isSuccess()){
                logger.info("OceanStor账户:[{}]", ownerOrgName);
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_862306984));
            }

            //账户信息入库
            resOceanstorPAccount.setCloudEnvId(cloudEnvId);
            resOceanstorPAccount.setOwnerId(ownerId);
            resOceanstorPAccount.setName(ownerOrgName);
            resOceanstorPAccount.setOrgSid(orgSid);
            if(createdOrgSid !=null){
                resOceanstorPAccount.setCreatedOrgSid(createdOrgSid.toString());
            }
            resOceanstorPAccount.setVersion(1L);
            resOceanstorPAccount.setCreatedBy(createdBy);
            resOceanstorPAccount.setUpdatedBy(createdBy);
            resOceanstorPAccount.setCreatedDt(current);
            resOceanstorPAccount.setUpdatedDt(current);

            resOceanstorPAccount.setResourceId(storAccountCreateResult.getId());
            resOceanstorPAccount.setEncryptOption(storAccountCreateResult.getEncrypt_option());
            resOceanstorPAccount.setStatus(storAccountCreateResult.getStatus());
            resOceanstorPAccountMapper.insert(resOceanstorPAccount);
        }
        return resOceanstorPAccount;
    }

    //通过OceanStor 删除共享  删除配额—》删除QOS—>删除文件系统---》查询该租户下是否还存在其他文件系统----》删除用户及用户组—》删除租户
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean deleteShareByOceanStor(ResOceanstorShare resOceanShare) {

        Long id = resOceanShare.getId();
        ResShare resShare = resShareMapper.selectByPrimaryKey(id);
        if (null == resShare) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_183455081));
        }
        if (Objects.isNull(resShare.getUuid())) {
            return resShareMapper.deleteShareById(resShare.getId()) == 1;
        }
        String ownerId = resShare.getOwnerId();

        //查询OceanStor 云环境ID
        Long cloudEnvId = resShare.getCloudEnvId();
        CloudEnv cloudEnv = cloudEnvService.selectByPrimaryKey(cloudEnvId);

        //查询OceanStor 账户
        ResOceanstorPAccountExample pAccountExample = new ResOceanstorPAccountExample();
        pAccountExample.createCriteria().andOwnerIdEqualTo(ownerId)
                .andCloudEnvIdEqualTo(cloudEnvId)
                .andStatusEqualTo(ACTIVE);
        List<ResOceanstorPAccount> resOceanstorPAccounts = resOceanstorPAccountMapper.selectByExample(pAccountExample);
        if(CollectionUtil.isEmpty(resOceanstorPAccounts)){
            throw new BizException(StrUtil.format(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1339908231),ownerId));
        }
        ResOceanstorPAccount resOceanstorPAccount = resOceanstorPAccounts.get(0);
        String accountId = resOceanstorPAccount.getResourceId();
        String accountName = resOceanstorPAccount.getName();


        if(cloudEnv== null){
            logger.info("cloudEnvId={}环境不存在,删除数据库数据",cloudEnvId);
            logger.info("删除数据库配额[ownerId={},cloudEnvId={}]",ownerId,cloudEnvId);
            ResOceanstorPQuotaExample pQuotaExample = new ResOceanstorPQuotaExample();
            pQuotaExample.createCriteria().andCloudEnvIdEqualTo(cloudEnvId).andOwnerIdEqualTo(ownerId);
            resOceanstorPQuotaMapper.deleteByExample(pQuotaExample);

            logger.info("删除Qos策略 cloudEnvId={},accountId={},ownerId={}",cloudEnvId,accountId,ownerId);
            ResOceanstorPQosPolicyExample pQosPolicyExample = new ResOceanstorPQosPolicyExample();
            pQosPolicyExample.createCriteria().andCloudEnvIdEqualTo(cloudEnvId).andRefAccountRidEqualTo(accountId).andOwnerIdEqualTo(ownerId);
            resOceanstorPQosPolicyMapper.deleteByExample(pQosPolicyExample);

            String uuid = resShare.getUuid();
            logger.info("删除数据库文件系统命名空间[accountId={},cloudEnvId={},,ResourceId={}]",accountId,cloudEnvId,uuid);
            ResOceanstorPNamespaceExample pNamespaceExample = new ResOceanstorPNamespaceExample();
            pNamespaceExample.createCriteria().andCloudEnvIdEqualTo(cloudEnvId)
                    .andRefAccountRidEqualTo(Long.valueOf(accountId))
                    .andResourceIdEqualTo(Long.valueOf(uuid));

            logger.info("删除数据库Orceanstor账户 resourceId={},name={}",resOceanstorPAccount.getResourceId(), accountName);
            resOceanstorPAccountMapper.deleteByPrimaryKey(resOceanstorPAccount.getId());

            resShare.setStatus("deleted");
            resShareMapper.updateByPrimaryKey(resShare);
            return true;
        }



        //删除配额
        logger.info("查询配额cloudEnvId={},ownerId={}",cloudEnvId,ownerId);
        ResOceanstorPQuotaExample pQuotaExample = new ResOceanstorPQuotaExample();
        pQuotaExample.createCriteria().andCloudEnvIdEqualTo(cloudEnvId).andOwnerIdEqualTo(ownerId);

        List<ResOceanstorPQuota> resOceanstorPQuotas = resOceanstorPQuotaMapper.selectByExample(pQuotaExample);
        if(!CollectionUtil.isEmpty(resOceanstorPQuotas)){
            String resourceId = resOceanstorPQuotas.get(0).getResourceId();
            logger.info("删除配额 accountId={},resourceId={}",accountId,resourceId);
            OceanStorNamespacesQuotaDelete quotaDelete = new OceanStorNamespacesQuotaDelete();
            quotaDelete.setId(resourceId);
            BaseResult baseResult = oceanStorService.oceanStorNamespacesQuotaDelete(quotaDelete, cloudEnvId);
            if(baseResult.isSuccess()){
                logger.info("删除配额表数据，resourceId={}",resOceanstorPQuotas.get(0).getResourceId());
                resOceanstorPQuotaMapper.deleteByExample(pQuotaExample);
            }else{
                logger.error("删除QOS配额失败:{}",baseResult.getErrMsg());
                return false;
            }
        }


        //删除Qos策略
        logger.info("删除Qos策略 cloudEnvId={},accountId={},ownerId={}",cloudEnvId,accountId,ownerId);
        ResOceanstorPQosPolicyExample pQosPolicyExample = new ResOceanstorPQosPolicyExample();
        pQosPolicyExample.createCriteria().andCloudEnvIdEqualTo(cloudEnvId).andRefAccountRidEqualTo(accountId).andOwnerIdEqualTo(ownerId);

        List<ResOceanstorPQosPolicy> resOceanstorPQosPolicies = resOceanstorPQosPolicyMapper.selectByExample(pQosPolicyExample);
        if(!CollectionUtil.isEmpty(resOceanstorPQosPolicies)){
            ResOceanstorPQosPolicy qosPolicy = resOceanstorPQosPolicies.get(0);

            OceanStorConvergedQosPolicyDelete qosPolicyDelete = new OceanStorConvergedQosPolicyDelete();
            qosPolicyDelete.setName(qosPolicy.getName());
            qosPolicyDelete.setQos_scale(qosPolicy.getQosScale().toString());
            BaseResult baseResult = oceanStorService.oceanStorConvergedQosPolicyDelete(qosPolicyDelete, cloudEnvId);
            if(baseResult.isSuccess()){
                logger.info("删除策略表数据，name={}",qosPolicy.getName());
                resOceanstorPQosPolicyMapper.deleteByExample(pQosPolicyExample);

            }else {
                logger.error("删除策略表失败:{}",baseResult.getErrMsg());
                return false;
            }

        }


        //删除文件系统
        logger.info("查询文件系统命名空间cloudEnvId={},accountId={},resourceId={}",cloudEnvId,accountId,resShare.getUuid());
        ResOceanstorPNamespaceExample pNamespaceExample = new ResOceanstorPNamespaceExample();
        pNamespaceExample.createCriteria().andCloudEnvIdEqualTo(cloudEnvId)
                .andRefAccountRidEqualTo(Long.valueOf(accountId))
                .andResourceIdEqualTo(Long.valueOf(resShare.getUuid()));

        List<ResOceanstorPNamespace> resOceanstorPNamespaces = resOceanstorPNamespaceMapper.selectByExample(pNamespaceExample);
        if(!CollectionUtil.isEmpty(resOceanstorPNamespaces)){
            ResOceanstorPNamespace resOceanstorPNamespace = resOceanstorPNamespaces.get(0);

            //删除分级
            ResOceanstorPTierPolicyExample pTierPolicyExample = new ResOceanstorPTierPolicyExample();
            pTierPolicyExample.createCriteria().andCloudEnvIdEqualTo(cloudEnvId)
                    .andFsIdEqualTo(resOceanstorPNamespace.getResourceId())
                    .andOwnerIdEqualTo(ownerId);
            List<ResOceanstorPTierPolicy> resOceanstorPTierPolicies = resOceanstorPTierPolicyMapper.selectByExample(pTierPolicyExample);
            if(!CollectionUtil.isEmpty(resOceanstorPTierPolicies)){
                logger.info("删除分级 cloudEnvId={},fsId={},ownerId={}",cloudEnvId,resOceanstorPNamespace.getResourceId(),ownerId);
                ResOceanstorPTierPolicy resOceanstorPTierPolicy = resOceanstorPTierPolicies.get(0);
                OceanStorTierPoliciesDelete tierPoliciesDelete = new OceanStorTierPoliciesDelete();
                tierPoliciesDelete.setName(resOceanstorPTierPolicy.getName());
                tierPoliciesDelete.setFs_id(resOceanstorPTierPolicy.getFsId().toString());
                OceanStorTierPoliciesDeleteResult tierPoliciesDeleteResult = oceanStorService.oceanStorTierPoliciesDelete(tierPoliciesDelete, cloudEnvId);
                if(tierPoliciesDeleteResult.isSuccess()){
                    logger.info("删除分级表数据 cloudEnvId={},fsId={},ownerId={}",cloudEnvId,resOceanstorPNamespace.getResourceId(),ownerId);
                    resOceanstorPTierPolicyMapper.deleteByExample(pTierPolicyExample);
                }else{
                    logger.info("删除分级特性迁移策略失败:{}",tierPoliciesDeleteResult.getErrMsg());
                }
            }



            logger.info("删除文件系统命名空间，name={}",resOceanstorPNamespace.getName());
            OceanStorNamespacesDelete namespacesDelete = new OceanStorNamespacesDelete();
            namespacesDelete.setName(resOceanstorPNamespace.getName());
            BaseResult baseResult = oceanStorService.oceanStorNamespacesDelete(namespacesDelete, cloudEnvId);
            if(baseResult.isSuccess()){
                logger.info("删除命名空间表数据，name={}",resOceanstorPNamespace.getName());
                resOceanstorPNamespaceMapper.deleteByExample(pNamespaceExample);
            }else{
                logger.info("删除文件系统命名空间失败:{}",baseResult.getErrMsg());
                return false;
            }



        }


        //查询该租户下是否还存在其他文件系统
        ResOceanstorPNamespaceExample pNamespaceExample2 = new ResOceanstorPNamespaceExample();
        pNamespaceExample2.createCriteria().andCloudEnvIdEqualTo(cloudEnvId)
                .andRefAccountRidEqualTo(Long.valueOf(accountId));

        long count = resOceanstorPNamespaceMapper.countByExample(pNamespaceExample);



        logger.info("查询租户下是否存在其他文件系统数量{}",count);
        if(count == 0){

            logger.info("查询Unix用户，accountId={}，accountName={}",accountId,accountName);
            OceanStorUnixUser oceanStorUnixUser = new OceanStorUnixUser();
            oceanStorUnixUser.setAccount_id(accountId);
            OceanStorUnixUserResult unixUserResult = oceanStorService.oceanStorUnixUser(oceanStorUnixUser, cloudEnvId);
            if(unixUserResult.isSuccess()){

                List<OceanStorUnixUserInfo> unixUserInfoList = unixUserResult.getUnixUserInfoList();
                for (OceanStorUnixUserInfo oceanStorUnixUserInfo : unixUserInfoList) {
                    String unixUserName = oceanStorUnixUserInfo.getName();
                    logger.info("删除Unix用户，unixUserName={}",unixUserName);
                    OceanStorUnixUserDelete oceanStorUnixUserDelete = new OceanStorUnixUserDelete();
                    oceanStorUnixUserDelete.setAccount_id(accountId);
                    oceanStorUnixUserDelete.setName(unixUserName);
                    BaseResult baseResult = oceanStorService.oceanStorUnixUserDelete(oceanStorUnixUserDelete, cloudEnvId);
                    if(baseResult.isSuccess()){
                        logger.info("删除Unix用户，unixUserName={}成功",unixUserName);
                    }else {
                        logger.error("删除Unix用户失败，unixUserName={}:{}",unixUserName,baseResult.getErrMsg());
                        return false;
                    }
                }


            }

            logger.info("查询Unix用户组，accountId={}，accountName={}",accountId,accountName);
            OceanStorUnixUserGroup oceanStorUnixUserGroup = new OceanStorUnixUserGroup();
            oceanStorUnixUserGroup.setAccount_id(accountId);
            OceanStorUnixUserGroupResult unixUserGroupResult = oceanStorService.oceanStorUnixUserGroup(oceanStorUnixUserGroup, cloudEnvId);
            if(unixUserGroupResult.isSuccess()){
                List<OceanStorUnixUserGroupInfo> oceanStorUnixUserGroupInfoList = unixUserGroupResult.getOceanStorUnixUserGroupInfoList();
                for (OceanStorUnixUserGroupInfo oceanStorUnixUserGroupInfo : oceanStorUnixUserGroupInfoList) {

                    String unixUserGroupName = oceanStorUnixUserGroupInfo.getName();
                    logger.info("删除Unix用户组，unixUserGroupName={}",unixUserGroupName);
                    OceanStorUnixUserGroupDelete unixUserGroupDelete = new OceanStorUnixUserGroupDelete();
                    unixUserGroupDelete.setAccount_id(accountId);
                    unixUserGroupDelete.setName(unixUserGroupName);
                    BaseResult baseResult = oceanStorService.oceanStorUnixUserGroupDelete(unixUserGroupDelete, cloudEnvId);
                    if(baseResult.isSuccess()){
                        logger.info("删除Unix用户组，unixUserGroupName={}成功",unixUserGroupName);
                    }else{
                        logger.error("删除Unix用户组失败，unixUserGroupName={}:{}",baseResult.getErrMsg());
                        return false;
                    }
                }

            }
        }
        // 删除租户

        logger.info("删除账户 resourceId={},name={}",resOceanstorPAccount.getResourceId(), accountName);
        OceanStorAccountDelete oceanStorAccountDelete = new OceanStorAccountDelete();
        oceanStorAccountDelete.setId(resOceanstorPAccount.getResourceId());

        BaseResult baseResult = oceanStorService.oceanStorAccountDelete(oceanStorAccountDelete, cloudEnvId);
        if(baseResult.isSuccess()){
            logger.info("删除Oceanstor账户表数据，name={}",accountName);
            resOceanstorPAccountMapper.deleteByPrimaryKey(resOceanstorPAccount.getId());
        }else{
           logger.error("删除账户失败,resourceId={},name={}:{}",resOceanstorPAccount.getResourceId(), accountName,baseResult.getErrMsg());
           return false;
        }

        resShare.setStatus("deleted");
        resShareMapper.updateByPrimaryKey(resShare);
        return true;
    }

    @Override
    public ResShare syncResShareByShareId(Long id) {
        ResShare resShare = resShareMapper.selectByPrimaryKey(id);
        try {
            String uuid = resShare.getUuid();
            if(Objects.nonNull(resShare) && Objects.nonNull(uuid) && ShareStatus.AVAILABLE.equals(resShare.getStatus())){
                Long cloudEnvId = resShare.getCloudEnvId();
                if(DME_OSP.equals(resShare.getType())){
                    syncDMEOSPUsedSize(Arrays.asList(resShare));
                }else if(!StringUtils.equalsIgnoreCase(resShare.getShareTypeName(),CloudEnvType.OCEANSTOR_PACIFIC.getValue().get(0))){
                    ShareDetailByEnv shareDetailByEnv = CloudClientFactory.buildMQBean(cloudEnvId, ShareDetailByEnv.class);
                    shareDetailByEnv.setUUID(uuid);
                    try {
                        logger.debug("syncResShareByShareId_sendMQ_start_shareDetailByEnv{}", JSON.toJSONString(shareDetailByEnv));
                        ShareDetailByEnvResult result = (ShareDetailByEnvResult) MQHelper.rpc(shareDetailByEnv);
                        logger.info("syncResShareByShareId_sendMQ_end_shareDetailByEnv{}",JSON.toJSONString(result));

                        BigDecimal usedSize = BigDecimal.ZERO;
                        if(Objects.nonNull(result)){
                            List<ShareVo> shareVos = result.getShareVos();
                            if(CollectionUtil.isNotEmpty(shareVos)){
                                for (ShareVo shareVo : shareVos) {
                                    usedSize = usedSize.add(shareVo.getUsedSize());
                                }
                                //更新resShare使用容量
                                ResShare updateShare = new ResShare();
                                updateShare.setId(resShare.getId());
                                updateShare.setUpdatedDt(new Date());
                                updateShare.setUsedSize(usedSize);
                                logger.debug("update_res_share{}",JSON.toJSONString(updateShare));
                                resShareMapper.updateByPrimaryKey(updateShare);
                                logger.info("================update_res_share end========================");
                                resShare.setUsedSize(usedSize);
                            }
                        }

                    } catch (Exception e) {
                        logger.error("syncResShareByShareId_sendMq_error_[{}]",JSON.toJSONString(e));
                    }
                }else{

                    String ownerId = resShare.getOwnerId();

                    //查询Oceanstor 账户
                    ResOceanstorPAccountExample accountExample = new ResOceanstorPAccountExample();
                    accountExample.createCriteria().andCloudEnvIdEqualTo(cloudEnvId)
                            .andOwnerIdEqualTo(ownerId)
                            .andStatusEqualTo(ACTIVE);
                    List<ResOceanstorPAccount> resOceanstorPAccounts = resOceanstorPAccountMapper.selectByExample(accountExample);
                    Optional<ResOceanstorPAccount> accountOptional = resOceanstorPAccounts.stream().findFirst();
                    if(accountOptional.isPresent()){

                        ResOceanstorPAccount resOceanstorPAccount = accountOptional.get();
                        String oceanstorAccountId = resOceanstorPAccount.getResourceId();
                        //查询配额
                        ResOceanstorPQuotaExample quotaExample = new ResOceanstorPQuotaExample();

                        quotaExample.createCriteria().andCloudEnvIdEqualTo(cloudEnvId)
                                .andOwnerIdEqualTo(ownerId)
                                .andParentIdEqualTo(uuid);

                        List<ResOceanstorPQuota> resOceanstorPQuotas = resOceanstorPQuotaMapper.selectByExample(quotaExample);
                        Optional<ResOceanstorPQuota> oceanstorPQuotaOptional = resOceanstorPQuotas.stream().findFirst();
                        if (oceanstorPQuotaOptional.isPresent()) {
                            ResOceanstorPQuota resOceanstorPQuota = oceanstorPQuotaOptional.get();
                            OceanStorNamespacesQuota oceanStorNamespacesQuota = new OceanStorNamespacesQuota();

                            String resourceId = resOceanstorPQuota.getResourceId();

                            oceanStorNamespacesQuota.setId(resourceId);
                            oceanStorNamespacesQuota.setAccount_id(oceanstorAccountId);
                            oceanStorNamespacesQuota.setSpace_unit_type(resOceanstorPQuota.getSpaceUnitType().toString());
                            OceanStorNamespacesQuotaResult quotaResult = oceanStorService.oceanStorNamespacesQuota(oceanStorNamespacesQuota, cloudEnvId);

                            if (quotaResult.isSuccess()) {
                                String space_used = quotaResult.getSpace_used();
                                if(StringUtils.isEmpty(space_used)){
                                    space_used="0";
                                }
                                BigDecimal usedSize = new BigDecimal(space_used);

                                //更新resShare使用容量
                                ResShare updateShare = new ResShare();
                                updateShare.setId(resShare.getId());
                                updateShare.setUpdatedDt(new Date());
                                updateShare.setUsedSize(usedSize);
                                logger.debug("update_res_share={}",JSON.toJSONString(updateShare));
                                resShareMapper.updateByPrimaryKey(updateShare);
                                resShare.setUsedSize(usedSize);

                            }else {
                                logger.error("查询配额ID={}失败:{}",resourceId,quotaResult.getErrMsg());
                            }

                        }else{
                            logger.info("查询owerId={},cloudEnvId={}数据不存在",ownerId,cloudEnvId);
                        }
                    }else{
                            logger.info("同步owerId={},cloudEnvId={}配额数据,Oceanstor账户不存在",ownerId,cloudEnvId);
                    }
                }
            }
        }catch (Exception e){
            logger.error(StrUtil.format("同步reShareId={}异常:",id),e);
        }
        return resShare;
    }

    @Override
    public void syncDMEOSPUsedSize(List<ResShare> resShareList) {
        Map<Long, ResShare> quotaIdShareMap = resShareList.stream().filter(res -> res.getQuotaId() != null && DME_OSP.equals(res.getType())).collect(Collectors.toMap(ResShare::getQuotaId, res -> res, (v1, v2) -> v2));
        Set<Long> quotaIdSet = quotaIdShareMap.keySet();
        if (CollectionUtil.isEmpty(quotaIdSet)) {
            return;
        }
        List<Long> quotaIdList = new ArrayList<>(quotaIdSet);
        ResOceanstorPQuotaExample resOceanstorPQuotaExample = new ResOceanstorPQuotaExample();
        resOceanstorPQuotaExample.createCriteria().andIdIn(quotaIdList);
        List<ResOceanstorPQuota> resOceanstorPQuotaList = resOceanstorPQuotaMapper.selectByExample(resOceanstorPQuotaExample);
        if (CollectionUtil.isNotEmpty(resOceanstorPQuotaList)) {
            try {
                ResOceanstorPQuota quota = CollectionUtil.getFirst(resOceanstorPQuotaList);
                List<String> quotaResourceIdList = resOceanstorPQuotaList.stream().map(ResOceanstorPQuota::getResourceId).collect(Collectors.toList());
                Map<String, Long> quotaResourceIdMap =  resOceanstorPQuotaList.stream().collect(Collectors.toMap(ResOceanstorPQuota::getResourceId, ResOceanstorPQuota::getId));
                DMEQuotaListQueryRequest quotaListQueryRequest = CloudClientFactory.buildMQBean(quota.getCloudEnvId(), DMEQuotaListQueryRequest.class);
                quotaListQueryRequest.setIds(quotaResourceIdList);
                DMEQuotaListQueryResult result = (DMEQuotaListQueryResult)MQHelper.rpc(quotaListQueryRequest);
                if (result != null && CollectionUtil.isNotEmpty(result.getDatas())) {
                    result.getDatas().stream()
                        .forEach(item ->{
                            Long quotaId = quotaResourceIdMap.get(item.getId());
                            if (quotaId == null) {
                                return;
                            }
                            ResShare resShare = quotaIdShareMap.get(quotaId);
                            String spaceHardUsed = item.getSpaceHardUsed();
                            if (StringUtils.isNotEmpty(spaceHardUsed) && resShare != null) {
                                BigDecimal used = NumberUtil.div(new BigDecimal(spaceHardUsed), BigDecimal.valueOf(1024 * 1024), 2, RoundingMode.DOWN);
                                resShare.setUsedSize(used);
                                resShare.setUpdatedDt(new Date());
                                resShareMapper.updateByPrimaryKey(resShare);
                            }
                        });
                } else {
                    logger.info("syncDMEOSPUsedSize fail {}",result);
                }
            } catch (Exception e) {
                logger.error("syncDMEOSPUsedSize Exception",e);
            }
        }
        logger.info("syncDMEOSPUsedSize quotaSize={}",resOceanstorPQuotaList.size());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public RestResult deleteShareByIdAndOrderId(Long id, Long orderId) {
        ResShare share = resShareMapper.selectByPrimaryKey(id);
        if (null == share) {
            return new RestResult(false);
        }
        if (Objects.isNull(share.getUuid())) {
            return new RestResult(resShareMapper.deleteShareById(share.getId()) == 1);
        }
        String originalStatus = share.getStatus();
        share.setStatus("deleted");
        resShareMapper.updateByPrimaryKey(share);

        if (ShareSupportClusterTypeEnum.PREDEPLOY.getCode().equals(share.getSupportClusterType())) {
            // 退订预部署集群文件系统
            return this.deletePredeployShare(share, orderId, originalStatus);
        } else {
            // 退订按需集群文件系统
            ShareDelete shareDelete = CloudClientFactory.buildMQBean(share.getCloudEnvId(),
                    ShareDelete.class);
            if (Stream.of(ResShare.HPC_VERSION_WUHAN, ResShare.HPC_VERSION_XIAN).anyMatch(i -> Objects.equals(i, share.getHpcVersion()))) {
                CloudEnv cloudEnv = cloudEnvService.selectByPrimaryKey(share.getCloudEnvId());
                Map<String, String> params = JsonUtil.fromJson(
                        CrytoUtilSimple.decrypt(cloudEnv.getAttrData(), true),
                        new TypeReference<Map<String, String>>() {
                        });
                if (CloudEnvType.HCSO.equals(cloudEnv.getCloudEnvType())) {
                    shareDelete.setTenantId(params.get(CloudEnvTenantKey.TENANT_ID));
                    shareDelete.setApiKey(params.get(CloudEnvTenantKey.API_KEY));
                    shareDelete.setSecureToken(params.get(CloudEnvTenantKey.SECURE_TOKEN));
                    shareDelete.setDomain(params.get(CloudEnvTenantKey.DOMAINID));
                }
            }
            shareDelete.setId(share.getId());
            shareDelete.setUuid(share.getUuid());
            shareDelete.setName(share.getName());
            shareDelete.setType(share.getType());
            shareDelete.setOptions(MapUtil.<String, Object>builder().put("orderId", orderId)
                    .put("originalStatus", originalStatus).build());
            return new RestResult(sendToMQ(shareDelete).getStatus());
        }
    }

    private RestResult deletePredeployShare(ResShare share, Long orderId, String originalStatus) {
        Criteria criteria = new Criteria();
        criteria.put("cloudEnvType", CloudEnvType.FUSION_DIRECTOR.getValue().get(0));
        List<CloudEnv> cloudEnvs = cloudEnvService.selectByParams(criteria);
        SharePredeployDelete sharePredeployDelete = CloudClientFactory.buildMQBean(cloudEnvs.get(0).getId(), SharePredeployDelete.class);
        sharePredeployDelete.setFileSystemID(share.getUuid());
        sharePredeployDelete.setId(share.getId());
        sharePredeployDelete.setName(share.getName());
        sharePredeployDelete.setOptions(MapUtil.<String, Object>builder().put("orderId", orderId)
                .put("originalStatus", originalStatus).build());
        return new RestResult(sendToMQ(sharePredeployDelete).getStatus());
    }

    @Override
    public List<Long> getIdList() {
        List<Long> shareId = resShareMapper.getShareId();
        List<Long> shareTypeId = resShareMapper.getShareTypeId();
        shareId.addAll(shareTypeId);
        return shareId;
    }

    @Override
    public int updateShareTypeByPrimaryKey(Criteria criteria) {
        return resShareMapper.updateShareTypeByPrimaryKey(criteria);
    }

    @Override
    public ResShareSumInfo sumSizeByIdGroupShareType(List<Long> allSFSIds, Integer hpcVersion) {
        return resShareMapper.sumSizeByIdGroupShareType(allSFSIds,hpcVersion);
    }

    @Override
    public List<Long> getShareIdsByClusterIds(List<Long> hpcClusterIds, String productType) {
        ArrayList<Long> resourceIds = new ArrayList<>();
        hpcClusterIds.forEach(clusterId -> {
            List<ResHpcClusterResource> resHpcClusterResourceList = resHpcClusterResourceMapper.getByClusterIdAndClusterType(clusterId, productType);
            if (CollectionUtils.isNotEmpty(resHpcClusterResourceList)) {
                List<Long> resourceIdList = resHpcClusterResourceList.stream().map(ResHpcClusterResource::getResourceId).collect(Collectors.toList());
                resourceIds.addAll(resourceIdList);
            }
        });
        return resourceIds;
    }

    @Override
    public List<String> getClusterDefaultIds() {
        return resShareMapper.getClusterDefaultIds();
    }

    @Override
    public List<ResShare> getShareList2(Criteria criteria) {
        AuthUser authUser = BasicInfoUtil.getCurrentUserInfo();
        if (Objects.nonNull(authUser) && CONSOLE.equals(authUser.getRemark())) {
            String createdBy = systemMUserMapper.selectCreatedByByPrimaryKey(authUser.getUserSid());
            criteria.put("orgSid", authUser.getOrgSid());
            if(authUser.getParentSid() != null){
                criteria.put("createdBy", ADMIN.equals(createdBy) ? authUser.getAccount() : createdBy);
            }
        }
        return resShareMapper.getShareList2(criteria);
    }

    @Override
    public Long getByResourceIdAndClusterType(Long resourceId, String resourceType) {

        return this.resHpcClusterResourceMapper.getByResourceIdAndClusterType(resourceId,resourceType);
    }


    @Override
    public List<Long> getHpcVersionSFSById(List<Long> collect,String sfsProductCode) {
        return resShareMapper.getHpcVersionSFSById(collect,sfsProductCode);
    }

    @Override
    public RestResult createDMEOSP(ResDMEOSP resDMEOSP) {

        ResShare resShare = new ResShare();
        BeanUtils.copyProperties(resDMEOSP,resShare);
        Long id = resDMEOSP.getId();
        Date current = new Date();
        //查询OceanStor 云环境ID
        Criteria criteria = new Criteria();
        criteria.put("cloudEnvType", CloudEnvType.DME.getValue().get(0));
        List<CloudEnv> cloudEnvs = cloudEnvService.selectByParams(criteria);

        if(CollectionUtil.isEmpty(cloudEnvs)){
            throw new BizException(StrUtil.format("{}环境不存在",CloudEnvType.DME.getDesc()));
        }
        CloudEnv cloudEnv = cloudEnvs.get(0);
        Long cloudEnvId = cloudEnv.getId();

        resShare.setCloudEnvId(cloudEnvId);
        resShare.setStatus(ShareStatus.CREATING);
        if (!Strings.isNullOrEmpty(resShare.getShareType())) {
            resShare.setShareType(resShare.getShareType().toUpperCase());
        }
        if (StringUtils.isNotBlank(resShare.getName()) && StringUtils.isBlank(resShare.getDescription())) {
            resShare.setDescription(resShare.getName());
        }
        resShare.setUsedSize(BigDecimal.ZERO);
        resShare.setFileSystemName(resShare.getName());
        //配置配额信息
        configDMEQuota(resShare,cloudEnvId);

        resShare.setUpdatedDt(current);
        if (id == null) {
            resShareMapper.insertSelective(resShare);
        } else {
            resShareMapper.updateByPrimaryKey(resShare);
        }
        resDMEOSP.setId(resShare.getId());
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS),
            resShare.getId());
    }

    /**
     * 创建DME存储配额
     *
     * @param resShare
     * @param cloudEnvId
     */
    private void configDMEQuota(ResShare resShare, Long cloudEnvId) {
          List<ResOceanstorPQuota> resOceanstorPQuotas = resOceanstorPQuotaMapper.selectByOrgSidAndFileSystemUUID(resShare.getOrgSid(),cloudEnvId, resShare.getUuid());
        ResOceanstorPQuota quota = CollectionUtil.getFirst(resOceanstorPQuotas);
        if (quota != null) {
            ResOceanstorPQuota resOceanstorPQuota = resOceanstorPQuotas.get(0);
            if (ShareStatus.deleting.equals(resOceanstorPQuota.getStatus())) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERROR_DME_OSP_DELETING));
            }
            resShare.setQuotaId(resOceanstorPQuota.getId());
            resShare.setStatus(ShareStatus.AVAILABLE);
            Integer size = resShare.getSize();
            Long chageSize = Long.valueOf(size) - quota.getSpaceHardQuota() ;
            if (chageSize > 0) {
                resShare.setQuotaSize(new BigDecimal(quota.getSpaceHardQuota()));
                ModifyShareParam modifyShareParam = new ModifyShareParam();
                modifyShareParam.setSize(size);
                modifyShareParam.setOldSize(quota.getSpaceHardQuota().intValue());
                logger.info("DME配额扩容 下发参数：[{}]", JSONUtil.toJsonStr(modifyShareParam));
                Boolean modify = modifyDMEQuota(resShare, modifyShareParam);
                if (Boolean.FALSE.equals(modify)) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERROR_DME_OSP_CREATE));
                }
            }
        } else {
            Date current = new Date();
            DMECreateQuotaRequest dmeCreateQuotaRequest = CloudClientFactory.buildMQBean(cloudEnvId,
                                                                                         DMECreateQuotaRequest.class);
            List<CloudEnv> cloudEnvType = cloudEnvService.selectByParams(
                    new Criteria("cloudEnvType", CloudEnvType.HPCOFFLINE.getDesc()));
            dmeCreateQuotaRequest.setParentType(cloudEnvType.isEmpty() ? NAMESPACE : FILE_SYSTEM);
            dmeCreateQuotaRequest.setParentId(resShare.getUuid());
            dmeCreateQuotaRequest.setQuotaType(USER_GROUP_QUOTA);
            dmeCreateQuotaRequest.setSpaceHardQuota(resShare.getSize() * 1024 * 1024);
            QuotaOwner quotaOwner = new QuotaOwner();
            quotaOwner.setDomainType(LDAP_DOMAIN);
            quotaOwner.setType(DOMAIN_USER_GROUP);
            quotaOwner.setName(resShare.getOwnerOrgName());
            dmeCreateQuotaRequest.setQuotaOwner(quotaOwner);
            DMECreateQuotaResult result = null;
            try {
                logger.info("ResShareServiceImpl-configDMEQuota-创建配额开始");
                result = (DMECreateQuotaResult) MQHelper.rpc(dmeCreateQuotaRequest);
                logger.info("ResShareServiceImpl-configDMEQuota-创建配额结束");
            } catch (Exception exception) {
                logger.info("ResShareServiceImpl-configDMEQuota-创建配额失败：【{}】", exception.getMessage());
            }
            if (result != null && result.isSuccess()) {
                String taskId = result.getTaskId();
                quota = new ResOceanstorPQuota();
                quota.setSpaceHardQuota(Long.valueOf(resShare.getSize()));
                quota.setSpaceUnitType((byte) 3);
                quota.setCloudEnvId(cloudEnvId);
                quota.setOrgSid(resShare.getOrgSid());
                quota.setCreatedOrgSid(resShare.getOrgSid().toString());
                quota.setOwnerId(resShare.getOwnerId());
                quota.setCreatedDt(current);
                quota.setUpdatedDt(current);
                quota.setStatus(ShareStatus.CREATING);
                quota.setTaskId(taskId);
                quota.setFileSystemUuid(resShare.getUuid());
                quota.setVersion(1L);
                resOceanstorPQuotaMapper.insert(quota);
                resShare.setQuotaId(quota.getId());
            } else {
                throw new BizException(WebUtil.getMessage(MsgCd.ERROR_DME_OSP_CREATE));
            }
        }
    }

    /**
     * 删除DME-OSP配额
     * @param idList
     * @return
     */
    @Override
    public RestResult deleteDMEOSP(List<Long> idList) {
        //不等待删除任务
        boolean resultData = true;
        List<ResOceanstorPQuota> quotaList = new ArrayList<>();
        List<ResShare> resShareList = new ArrayList<>();
        for (Long sharedId : idList) {
            ResShare share = resShareMapper.selectByPrimaryKey(sharedId);
            if (null == share) {
                throw new BizException("该弹性文件不存在,请刷新后再试");
            }
            resShareList.add(share);
            ResOceanstorPQuota resOceanstorPQuota = resOceanstorPQuotaMapper.selectByPrimaryKey(share.getQuotaId());
            if (resOceanstorPQuota != null) {
                quotaList.add(resOceanstorPQuota);
            }
        }
        if (CollectionUtil.isNotEmpty(quotaList)) {
            deleteDMEOSPQuota(quotaList);
        }
        resShareList.stream().filter(resShare -> !ShareStatus.DELETED.equals(resShare.getStatus())).forEach(share ->{
            share.setStatus(ShareStatus.deleting);
            share.setUpdatedDt(new Date());
            resShareMapper.updateByPrimaryKey(share);

        });
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS),resultData);
    }

    /**
     * 创建集群回滚 DME-OSP配额
     *
     * @param idList
     *
     * @return
     */
    @Override
    public RestResult rollbackApplyHPCOfflineDMEOSP(List<Long> idList) {
        //执行下一步
        boolean nextStep = true;
        Date cur = new Date();
        List<ResOceanstorPQuota> quotaList = new ArrayList<>();
        List<ResShare> shareList = new ArrayList<>();
        for (Long shareId : idList) {
            ResShare share = resShareMapper.selectByPrimaryKey(shareId);
            if (null == share) {
                throw new BizException("该弹性文件不存在,请刷新后再试");
            }
            shareList.add(share);
        }
        for (ResShare share : shareList) {
            Long shareId = share.getId();
            ResOceanstorPQuota resOceanstorPQuota = resOceanstorPQuotaMapper.selectByPrimaryKey(share.getQuotaId());
            if (resOceanstorPQuota == null || Objects.isNull(resOceanstorPQuota.getResourceId())) {
                resShareMapper.deleteShareById(shareId);
                ResHpcClusterResourceExample example = new ResHpcClusterResourceExample();
                example.createCriteria().andResourceTypeEqualTo(DME_OSP).andResourceIdEqualTo(shareId);
                resHpcClusterResourceMapper.deleteByExample(example);
                resOceanstorPQuotaMapper.deleteByPrimaryKey(share.getQuotaId());
            } else if (resOceanstorPQuota.getReleaseTime() == null) {
                quotaList.add(resOceanstorPQuota);
            } else {
                //关联的待释放的配额，直接删除存储
                resShareMapper.deleteShareById(shareId);
                ResHpcClusterResourceExample example = new ResHpcClusterResourceExample();
                example.createCriteria().andResourceTypeEqualTo(DME_OSP).andResourceIdEqualTo(shareId);
                resHpcClusterResourceMapper.deleteByExample(example);
            }
        }
        if (CollectionUtil.isNotEmpty(quotaList)) {
            deleteDMEOSPQuota(quotaList);
            shareList.stream().forEach(share -> {
                String status = share.getStatus();
                if (!ShareStatus.DELETED.equals(status)) {
                    share.setStatus(ShareStatus.deleting);
                    share.setUpdatedDt(new Date());
                    resShareMapper.updateByPrimaryKey(share);
                }
            });
            nextStep = false;
        }
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS), nextStep);
    }



    /**
     * 删除DME-OSP配额
     * @param quotaIdList
     * @return
     */
    @Override
    public RestResult deleteDMEOSPQuotaById(List<Long> quotaIdList) {
        List<ResOceanstorPQuota> quotaList = new ArrayList<>();
        for (Long quotaId : quotaIdList) {
            ResOceanstorPQuota resOceanstorPQuota = resOceanstorPQuotaMapper.selectByPrimaryKey(quotaId);
            if (resOceanstorPQuota != null) {
                quotaList.add(resOceanstorPQuota);
            }
        }
        return deleteDMEOSPQuota(quotaList);
    }

    private RestResult deleteDMEOSPQuota(List<ResOceanstorPQuota> quotaList) {
        List<String> uuidList = quotaList.stream().map(ResOceanstorPQuota::getResourceId).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(uuidList)) {
            return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS), true);
        }
        DMEDeleteQuotaRequest deleteQuotaRequest = CloudClientFactory.buildMQBean(quotaList.get(0).getCloudEnvId(),
            DMEDeleteQuotaRequest.class);
        deleteQuotaRequest.setIds(uuidList);
        DMEDeleteQuotaResult result = null;
        try {
            logger.info("ResShareServiceImpl-deleteDMEOSP-删除配额开始");
            result = (DMEDeleteQuotaResult) MQHelper.rpc(deleteQuotaRequest);
            logger.info("ResShareServiceImpl-deleteDMEOSP-删除配额结束");
        } catch (Exception exception) {
            logger.info("ResShareServiceImpl-deleteDMEOSP-删除配额失败：【{}】", exception.getMessage());
        }
        if (result != null && result.isSuccess()) {
            String taskId = result.getTaskId();
            quotaList.stream().forEach(quota ->{
                quota.setStatus(ShareStatus.deleting);
                quota.setTaskId(taskId);
                resOceanstorPQuotaMapper.updateByPrimaryKey(quota);
            });
        } else {
            throw new BizException(WebUtil.getMessage(MsgCd.ERROR_DME_OSP_DELETE));
        }
        return new RestResult(RestResult.Status.SUCCESS, WebUtil.getMessage(MsgCd.INFO_OPERATE_SUCCESS));
    }

    @Override
    public long countRelatedQuota(Long quotaId) {
        return resShareMapper.countRelatedQuota(quotaId);
    }

    @Override
    public List<Long> getDMEOSPShareIdRelateQuota() {
        return resShareMapper.getDMEOSPShareIdRelateQuota();
    }


    @Override
    public void rollbackDelayDeleteDMEQuta(Long clusterId) {
        Date currentDate = new Date();
        List<ResShare> resShares = resShareMapper.selectDMEByClusterId(clusterId);
        resShares.forEach(share -> {
            share.setStatus(ShareStatus.AVAILABLE);
            share.setUpdatedDt(currentDate);
            resShareMapper.updateByPrimaryKey(share);
            //设置配额延期释放时间
            resOceanstorPQuotaMapper.rollbackDelayDeleteDMEQuta(share.getQuotaId());
        });
    }

    /**
     * 延期删除存储配额
     * @param clusterId
     * @param releaseTime
     */
    @Override
    public void delayDeleteDMEQuta(Long clusterId, Date releaseTime) {
        Date currentDate = new Date();
        List<ResShare> resShares = resShareMapper.selectDMEByClusterId(clusterId);
        List<DMEOSPQuotaDeleteMessage.ShareInfo> msgShareInfoList =  new ArrayList<>();
        resShares.forEach(share -> {
            share.setStatus(ShareStatus.DELETED);
            share.setUpdatedDt(currentDate);
            resShareMapper.updateByPrimaryKey(share);
            //设置配额延期释放时间
            resOceanstorPQuotaMapper.updateReleaseTime(share.getQuotaId(),releaseTime);
            msgShareInfoList.add(DMEOSPQuotaDeleteMessage.ShareInfo.builder().id(share.getQuotaId()).shareId(share.getId()).build());
        });
        DMEOSPQuotaDeleteMessage deleteMessage = DMEOSPQuotaDeleteMessage.builder().shareList(msgShareInfoList).build();
        sendDelayMessage(deleteMessage, releaseTime.getTime()-currentDate.getTime());
    }


    private void sendDelayMessage(DMEOSPQuotaDeleteMessage data, long delayMilliseconds) {
        // 给延迟队列发送消息
        logger.info("sendDelayMessage DMEOSPQuotaDeleteMessage",JSON.toJSONString(data));
        amqpTemplate.convertAndSend("dme.osp.quota.exchange", "dme.osp.quota.*",
            data, (message) -> {
                // 给消息设置延迟毫秒值
                message.getMessageProperties().setHeader("x-delay", delayMilliseconds);
                return message;
            });
    }

}
