package cn.com.cloudstar.rightcloud.resource.service.agency;


import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.List;

import cn.com.cloudstar.rightcloud.core.pojo.dto.agency.QuotaDTO;
import cn.com.cloudstar.rightcloud.core.pojo.dto.agency.ResAgency;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria;

/**
 * 资源委托表;(res_agency)表服务接口
 *
 * <AUTHOR> pujian
 * @date : 2024-1-27
 */
public interface ResAgencyService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     *
     * @return 实例对象
     */
    ResAgency queryById(Long id);

    /**
     * 分页查询
     *
     * @param resAgency 筛选条件
     * @param pageRequest 分页对象
     *
     * @return 查询结果
     */
    Page<ResAgency> paginQuery(ResAgency resAgency, PageRequest pageRequest);

    /**
     * 新增数据
     *
     * @param resAgency 实例对象
     *
     * @return 实例对象
     */
    ResAgency insert(ResAgency resAgency);

    /**
     * 更新数据
     *
     * @param resAgency 实例对象
     *
     * @return 实例对象
     */
    ResAgency update(ResAgency resAgency);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     *
     * @return 是否成功
     */
    boolean deleteById(Long id);

    List<ResAgency> agencyPage(Criteria criteria);

    ResAgency queryByName(String name, Long orgSid);

    QuotaDTO quota();
}
