/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.dao.oceanstor;

import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResOceanstorPAccount;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResOceanstorPAccountExample;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ResOceanstorPAccountMapper {
    long countByExample(ResOceanstorPAccountExample example);

    int deleteByExample(ResOceanstorPAccountExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ResOceanstorPAccount record);

    int insertSelective(ResOceanstorPAccount record);

    List<ResOceanstorPAccount> selectByExample(ResOceanstorPAccountExample example);

    ResOceanstorPAccount selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ResOceanstorPAccount record, @Param("example") ResOceanstorPAccountExample example);

    int updateByExample(@Param("record") ResOceanstorPAccount record, @Param("example") ResOceanstorPAccountExample example);

    int updateByPrimaryKeySelective(ResOceanstorPAccount record);

    int updateByPrimaryKey(ResOceanstorPAccount record);
}