<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.cloudstar.rightcloud.resource.dao.topo.ResTopologyMapper">
    <resultMap id="BaseResultMap" type="cn.com.cloudstar.rightcloud.remote.api.pojo.system.maintenance.topo.ResTopology">
        <id column="RES_TOPOLOGY_SID" property="resTopologySid" jdbcType="VARCHAR" />
        <result column="RES_TOPOLOGY_NAME" property="resTopologyName" jdbcType="VARCHAR" />
        <result column="RES_TOPOLOGY_TYPE" property="resTopologyType" jdbcType="VARCHAR" />
        <result column="RES_TOPOLOGY_TYPE_NAME" property="resTopologyTypeName" jdbcType="VARCHAR" />
        <result column="PARENT_TOPOLOGY_SID" property="parentTopologySid" jdbcType="VARCHAR" />
        <result column="PARENT_TOPOLOGY_NAME" property="parentTopologyName" jdbcType="VARCHAR" />
        <result column="DESCRIPTION" property="description" jdbcType="VARCHAR" />
        <result column="TOPOLOGY_VALUE" property="topologyValue" jdbcType="VARCHAR" />
        <result column="TOPOLOGY_ICON" property="topologyIcon" jdbcType="VARCHAR" />
        <result column="SORT_NO" property="sortNo" jdbcType="INTEGER" />
        <result column="CREATED_BY" property="createdBy" jdbcType="VARCHAR" />
        <result column="CREATED_DT" property="createdDt" jdbcType="TIMESTAMP" />
        <result column="UPDATED_BY" property="updatedBy" jdbcType="VARCHAR" />
        <result column="UPDATED_DT" property="updatedDt" jdbcType="TIMESTAMP" />
        <result column="VERSION" property="version" jdbcType="BIGINT" />
        <result column="VLAN_COUNT" property="vlanCount" jdbcType="VARCHAR" />
        <result column="PNI_COUNT" property="pniCount" jdbcType="VARCHAR" />
        <result column="PNE_COUNT" property="pneCount" jdbcType="VARCHAR" />
        <result column="org_sid" property="orgSid" jdbcType="BIGINT" />
    </resultMap>
    <sql id="Example_Where_Clause">
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.resTopologySid != null">
                and A.RES_TOPOLOGY_SID = #{condition.resTopologySid}
            </if>
            <if test="condition.resTopologyName != null">
                and A.RES_TOPOLOGY_NAME = #{condition.resTopologyName}
            </if>
            <if test="condition.resTopologyNameLike != null">
                and A.RES_TOPOLOGY_NAME like concat('%', #{condition.resTopologyNameLike},'%')
            </if>
            <if test="condition.resTopologyType != null">
                and A.RES_TOPOLOGY_TYPE = #{condition.resTopologyType}
            </if>
            <if test="condition.resTopologyTypes != null">
                and A.RES_TOPOLOGY_TYPE in
                <foreach collection="condition.resTopologyTypes" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="condition.parentTopologySid != null">
                and A.PARENT_TOPOLOGY_SID = #{condition.parentTopologySid}
            </if>
            <if test="condition.parentTopologySids != null">
                and A.PARENT_TOPOLOGY_SID IN
                <foreach collection="condition.parentTopologySids" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="condition.resPhyTopologyType == 'physical'">
                and A.RES_TOPOLOGY_TYPE IN ('PL','CE','NE','SE')
            </if>
            <if test="condition.description != null">
                and A.DESCRIPTION = #{condition.description}
            </if>
            <if test="condition.sortNo != null">
                and A.SORT_NO = #{condition.sortNo}
            </if>
            <if test="condition.createdBy != null">
                and A.CREATED_BY = #{condition.createdBy}
            </if>
            <if test="condition.createdDt != null">
                and A.CREATED_DT = #{condition.createdDt}
            </if>
            <if test="condition.updatedBy != null">
                and A.UPDATED_BY = #{condition.updatedBy}
            </if>
            <if test="condition.updatedDt != null">
                and A.UPDATED_DT = #{condition.updatedDt}
            </if>
            <if test="condition.version != null">
                and A.VERSION = #{condition.version}
            </if>
            <if test="condition.resTabsType == 'virtual'">
                and A.RES_TOPOLOGY_TYPE in ('R','DC','VE','VC')
            </if>
            <if test="condition.resTabsType == 'pool'">
                and A.RES_TOPOLOGY_TYPE in ('R','DC','VE','VC')
            </if>
            <if test="condition.resTabsType == 'physicalDC'">
                and A.RES_TOPOLOGY_TYPE in ('PUBLIC_CLOUD','PRIVATE_CLOUD','PHYSICAL_DEVICE','R','DC','PL','CE','NE','SE','SOFTWARE','H','C','RC','F','S','SW','LB','FW','SAN','MIDDLEWARE')
            </if>
            <if test="condition.findParentBySid != null">
                and FIND_IN_SET(A.RES_TOPOLOGY_SID,getResTopologyParentList(#{condition.findParentBySid}))
            </if>
            <if test="condition.findChildBySid != null">
                and FIND_IN_SET(A.PARENT_TOPOLOGY_SID,getResTopologyChildList(#{condition.findChildBySid}))
            </if>
            <if test="condition.orgSid != null">
                and A.org_sid = #{condition.orgSid}
            </if>
        </trim>
    </sql>
    <sql id="Example_Where_Clause1">
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.resTopologySid != null">
                and RES_TOPOLOGY_SID = #{condition.resTopologySid}
            </if>
            <if test="condition.resTopologyName != null">
                and RES_TOPOLOGY_NAME = #{condition.resTopologyName}
            </if>
            <if test="condition.resTopologyNameLike != null">
                and RES_TOPOLOGY_NAME like concat('%', #{condition.resTopologyNameLike},'%')
            </if>
            <if test="condition.resTopologyType != null">
                and RES_TOPOLOGY_TYPE = #{condition.resTopologyType}
            </if>
            <if test="condition.resTopologyTypes != null">
                and RES_TOPOLOGY_TYPE in
                <foreach collection="condition.resTopologyTypes" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="condition.parentTopologySid != null">
                and PARENT_TOPOLOGY_SID = #{condition.parentTopologySid}
            </if>
            <if test="condition.parentTopologySids != null">
                and PARENT_TOPOLOGY_SID IN
                <foreach collection="condition.parentTopologySids" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="condition.resPhyTopologyType == 'physical'">
                and RES_TOPOLOGY_TYPE IN ('PL','CE','NE','SE')
            </if>
            <if test="condition.description != null">
                and DESCRIPTION = #{condition.description}
            </if>
            <if test="condition.sortNo != null">
                and SORT_NO = #{condition.sortNo}
            </if>
            <if test="condition.createdBy != null">
                and CREATED_BY = #{condition.createdBy}
            </if>
            <if test="condition.createdDt != null">
                and CREATED_DT = #{condition.createdDt}
            </if>
            <if test="condition.updatedBy != null">
                and UPDATED_BY = #{condition.updatedBy}
            </if>
            <if test="condition.updatedDt != null">
                and UPDATED_DT = #{condition.updatedDt}
            </if>
            <if test="condition.version != null">
                and VERSION = #{condition.version}
            </if>
            <if test="condition.resTabsType == 'virtual'">
                and RES_TOPOLOGY_TYPE in ('R','DC','VE','VC')
            </if>
            <if test="condition.resTabsType == 'pool'">
                and RES_TOPOLOGY_TYPE in ('R','DC','VE','VC')
            </if>
            <if test="condition.resTabsType == 'physicalDC'">
                and RES_TOPOLOGY_TYPE in ('PUBLIC_CLOUD','PRIVATE_CLOUD','PHYSICAL_DEVICE','R','DC','PL','CE','NE','SE','SOFTWARE','H','C','F','RC','S','SW','LB','FW','SAN','MIDDLEWARE')
            </if>
            <if test="condition.findParentBySid != null">
                and FIND_IN_SET(RES_TOPOLOGY_SID,getResTopologyParentList(#{condition.findParentBySid}))
            </if>
            <if test="condition.findChildBySid != null">
                and FIND_IN_SET(PARENT_TOPOLOGY_SID,getResTopologyChildList(#{condition.findChildBySid}))
            </if>
            <if test="condition.orgSid != null">
                and org_sid = #{condition.orgSid}
            </if>
        </trim>
    </sql>
    <sql id="Base_Column_List">
        A.RES_TOPOLOGY_SID, A.RES_TOPOLOGY_NAME, A.RES_TOPOLOGY_TYPE, A.PARENT_TOPOLOGY_SID, A.DESCRIPTION,
        A.SORT_NO, A.CREATED_BY, A.CREATED_DT, A.UPDATED_BY, A.UPDATED_DT, A.VERSION, A.org_sid
    </sql>
    <select id="selectByParams" resultMap="BaseResultMap"
            parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        select
        <if test="distinct">
            distinct
        </if>
        concat(A.RES_TOPOLOGY_SID,',',IFNULL(VE.VIRTUAL_PLATFORM_TYPE,A.RES_TOPOLOGY_TYPE)) as TOPOLOGY_VALUE,
        (case A.RES_TOPOLOGY_TYPE
        when 'R' then '../../../images/icon/region.png'
        when 'DC' then '../../../images/icon/datacenter.png'
        WHEN 'PL' THEN '../../../images/icon/phy-weizhi.png'
        WHEN 'CE' THEN '../../../images/icon/phy-computing.png'
        WHEN 'NE' THEN '../../../images/icon/phy-network.png'
        WHEN 'SE' THEN '../../../images/icon/phy-storage-dev.png'
        WHEN 'H' THEN '../../../images/icon/phy-room.png'
        WHEN 'C' THEN '../../../images/icon/phy-cabinet.png'
        WHEN 'F' THEN '../../../images/icon/phy-frame.png'
        WHEN 'S' THEN '../../../images/icon/phy-server.png'
        WHEN 'SW' THEN '../../../images/icon/phy-switch.png'
        WHEN 'FW' THEN '../../../images/icon/phy-firewall.png'
        WHEN 'LB' THEN '../../../images/indexStatic/balanc.png'
        WHEN 'SAN' THEN '../../../images/icon/resource-pool-storage-2.png'
        WHEN 'RC' THEN '../../../images/icon/phy-rack.png'
        when 'VE' then
        case VE.VIRTUAL_PLATFORM_TYPE
        when 'VMware' then '../../../images/icon/resource-ve-vmware.png'
        when 'OpenStack' then '../../../images/icon/resource-ve-kvm.png'
        when 'KVM' then '../../../images/icon/resource-ve-kvm.png'
        when 'Hyper-V' then '../../../images/icon/resource-ve-hyperv.png'
        when 'Xen' then '../../../images/icon/resource-ve-xen.png'
        when 'PowerVM' then '../../../images/icon/resource-ve-powervm.png'
        when 'HP-UX' then '../../../images/icon/resource-ve-hpux.png'
        else '../../../images/icon/resource-pool.png'
        end
        when 'VC' then '../../../images/icon/cluster.png'
        else '../../../images/icon/resource-pool.png'
        end) as TOPOLOGY_ICON,
        B.CODE_DISPLAY AS RES_TOPOLOGY_TYPE_NAME,
        C.CODE_DISPLAY AS CONNECT_STATUS_NAME,
        D.CODE_DISPLAY AS UPDATE_STATUS_NAME,
        E.CODE_DISPLAY AS VIRTUAL_PLATFORM_VERSION_NAME,
        F.RES_TOPOLOGY_NAME AS PARENT_TOPOLOGY_NAME,
        <include refid="Base_Column_List" />
        from res_topology A
        left join res_ve VE on (A.RES_TOPOLOGY_SID = VE.RES_TOPOLOGY_SID)
        left join sys_m_code B on (A.RES_TOPOLOGY_TYPE=B.CODE_VALUE AND B.CODE_CATEGORY='RES_TOPOLOGY_TYPE')
        left join sys_m_code C on (VE.CONNECT_STATUS=C.CODE_VALUE AND C.CODE_CATEGORY='CONNECT_STATUS')
        left join sys_m_code D on (VE.UPDATE_STATUS=D.CODE_VALUE AND D.CODE_CATEGORY='UPDATE_STATUS')
        left join sys_m_code E on (VE.VIRTUAL_PLATFORM_VERSION=E.CODE_VALUE AND
        E.CODE_CATEGORY='VIRTUAL_PLATFORM_VERSION')
        left join res_topology F on (F.RES_TOPOLOGY_SID = A.PARENT_TOPOLOGY_SID)
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <include refid="common.Mysql_Pagination_Limit" />
    </select>

    <select id="selectVirtualTopologyTree" resultMap="BaseResultMap"
            parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        SELECT T.* from ((select
        A.RES_TOPOLOGY_SID
        ,A.PARENT_TOPOLOGY_SID
        ,A.RES_TOPOLOGY_NAME
        ,concat(A.RES_TOPOLOGY_SID,',',IFNULL(VE.VIRTUAL_PLATFORM_TYPE,A.RES_TOPOLOGY_TYPE)) as TOPOLOGY_VALUE
        ,(case A.RES_TOPOLOGY_TYPE
        when 'R' then '../../../images/icon/region.png'
        when 'DC' then '../../../images/icon/datacenter.png'
        when 'VE' then
        case VE.VIRTUAL_PLATFORM_TYPE
        when 'VMware' then '../../../images/icon/resource-ve-vmware.png'
        when 'OpenStack' then '../../../images/icon/resource-ve-kvm.png'
        when 'KVM' then '../../../images/icon/resource-ve-kvm.png'
        when 'Hyper-V' then '../../../images/icon/resource-ve-hyperv.png'
        when 'Xen' then '../../../images/icon/resource-ve-xen.png'
        when 'HP-UX' then '../../../images/icon/resource-ve-hpux.png'
        when 'PVE' then '../../../images/icon/resource-ve-vmware.png'
        when 'HMC' then '../../../images/icon/resource-ve-powervm.png'
        when 'IVM' then '../../../images/icon/resource-ve-powervm.png'
        else '../../../images/icon/resource-pool.png'
        end
        when 'VC' then '../../../images/icon/cluster.png'
        else '../../../images/icon/resource-pool.png'
        end) as TOPOLOGY_ICON,
        A.SORT_NO
        from res_topology A
        left join res_ve VE on (A.RES_TOPOLOGY_SID = VE.RES_TOPOLOGY_SID)
        where A.RES_TOPOLOGY_TYPE in ('R','PDC','DC','VE','VC')
        )
        UNION
        (SELECT
        A.RES_HOST_SID AS RES_TOPOLOGY_SID
        , A.PARENT_TOPOLOGY_SID
        , A.HOST_NAME AS RES_TOPOLOGY_NAME
        , concat(A.RES_HOST_SID,',','HOST') AS TOPOLOGY_VALUE
        , '../../../images/icon/host.png' AS TOPOLOGY_ICON
        , '99' as SORT_NO
        FROM
        res_host A
        INNER JOIN res_topology B ON (A.PARENT_TOPOLOGY_SID = B.RES_TOPOLOGY_SID)
        LEFT JOIN res_ve C ON (B.RES_TOPOLOGY_SID = C.RES_TOPOLOGY_SID)
        where B.RES_TOPOLOGY_TYPE = 'VC' or C.VIRTUAL_PLATFORM_TYPE = 'VMware'
        )
        UNION
        (SELECT
        A.RES_HOST_SID AS RES_TOPOLOGY_SID
        , A.PARENT_TOPOLOGY_SID
        , A.HOST_NAME AS RES_TOPOLOGY_NAME
        , concat(A.RES_HOST_SID,',','PHOST') AS TOPOLOGY_VALUE
        , '../../../images/icon/host.png' AS TOPOLOGY_ICON
        , '99' as SORT_NO
        FROM
        res_host A
        INNER JOIN res_topology B ON (A.PARENT_TOPOLOGY_SID = B.RES_TOPOLOGY_SID)
        LEFT JOIN res_ve C ON (B.RES_TOPOLOGY_SID = C.RES_TOPOLOGY_SID)
        where C.VIRTUAL_PLATFORM_TYPE in('HMC','IVM')
        )
        UNION
        (SELECT
        A.RES_HOST_SID AS RES_TOPOLOGY_SID
        , A.PARENT_TOPOLOGY_SID
        , A.HOST_NAME AS RES_TOPOLOGY_NAME
        , concat(A.RES_HOST_SID,',','OHOST') AS TOPOLOGY_VALUE
        , '../../../images/icon/host.png' AS TOPOLOGY_ICON
        , '99' as SORT_NO
        FROM
        res_host A
        INNER JOIN res_topology B ON (A.PARENT_TOPOLOGY_SID = B.RES_TOPOLOGY_SID)
        LEFT JOIN res_ve C ON (B.RES_TOPOLOGY_SID = C.RES_TOPOLOGY_SID)
        where C.VIRTUAL_PLATFORM_TYPE = 'Other'
        )
        )T
        order by T.SORT_NO, T.RES_TOPOLOGY_NAME
    </select>

    <select id="selectPhysicalTopologyTree" resultMap="BaseResultMap"
            parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        SELECT A.RES_TOPOLOGY_SID,A.PARENT_TOPOLOGY_SID,A.RES_TOPOLOGY_NAME,
        concat(A.RES_TOPOLOGY_SID,',',A.RES_TOPOLOGY_TYPE) AS TOPOLOGY_VALUE,
        (
        CASE A.RES_TOPOLOGY_TYPE
        WHEN 'R' THEN
        '../../../images/icon/region.png'
        WHEN 'DC' THEN
        '../../../images/icon/datacenter.png'
        WHEN 'PL' THEN
        '../../../images/icon/phy-weizhi.png'
        WHEN 'CE' THEN
        '../../../images/icon/phy-computing.png'
        WHEN 'NE' THEN
        '../../../images/icon/phy-network.png'
        WHEN 'SE' THEN
        '../../../images/icon/phy-storage-dev.png'
        WHEN 'H' THEN
        '../../../images/icon/phy-room.png'
        WHEN 'C' THEN
        '../../../images/icon/phy-cabinet.png'
        WHEN 'F' THEN
        '../../../images/icon/phy-frame.png'
        WHEN 'S' THEN
        '../../../images/icon/phy-server.png'
        WHEN 'SW' THEN
        '../../../images/icon/phy-switch.png'
        WHEN 'FW' THEN
        '../../../images/icon/phy-firewall.png'
        WHEN 'LB' THEN
        '../../../images/indexStatic/balanc.png'
        WHEN 'SAN' THEN
        '../../../images/icon/resource-pool-storage-2.png'
        WHEN 'RC' THEN
        '../../../images/icon/phy-rack.png'
        END
        ) AS TOPOLOGY_ICON,
        A.SORT_NO
        FROM
        res_topology A
        LEFT JOIN res_ve VE ON (
        A.RES_TOPOLOGY_SID = VE.RES_TOPOLOGY_SID
        )
        WHERE
        A.RES_TOPOLOGY_TYPE IN ('PUBLIC_CLOUD','PRIVATE_CLOUD','PHYSICAL_DEVICE','R', 'DC','PL','CE','NE','SE','H','C','F','S','SW','FW','LB','SAN','RC','SOFTWARE','MIDDLEWARE')
        order by A.SORT_NO, A.RES_TOPOLOGY_NAME
    </select>

    <select id="selectVirtualStorageTopologyTree" resultMap="BaseResultMap"
            parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        SELECT T.* from ((select
        A.RES_TOPOLOGY_SID
        ,A.PARENT_TOPOLOGY_SID
        ,A.RES_TOPOLOGY_NAME
        ,concat(A.RES_TOPOLOGY_SID,',',IFNULL(VE.VIRTUAL_PLATFORM_TYPE,A.RES_TOPOLOGY_TYPE)) as TOPOLOGY_VALUE
        ,(case A.RES_TOPOLOGY_TYPE
        when 'R' then '../../../images/icon/region.png'
        when 'DC' then '../../../images/icon/datacenter.png'
        when 'VE' then
        case VE.VIRTUAL_PLATFORM_TYPE
        when 'VMware' then '../../../images/icon/resource-ve-vmware.png'
        when 'OpenStack' then '../../../images/icon/resource-ve-kvm.png'
        when 'KVM' then '../../../images/icon/resource-ve-kvm.png'
        when 'Hyper-V' then '../../../images/icon/resource-ve-hyperv.png'
        when 'Xen' then '../../../images/icon/resource-ve-xen.png'
        when 'PowerVM' then '../../../images/icon/resource-ve-powervm.png'
        when 'HP-UX' then '../../../images/icon/resource-ve-hpux.png'
        when 'HMC' then '../../../images/icon/resource-ve-powervm.png'
        when 'IVM' then '../../../images/icon/resource-ve-powervm.png'
        else '../../../images/icon/resource-pool.png'
        end
        when 'RSC' then '../../../images/icon/resource-storage-folder.png'
        else '../../../images/icon/resource-pool.png'
        end) as TOPOLOGY_ICON,
        A.SORT_NO
        from res_topology A
        left join res_ve VE on (A.RES_TOPOLOGY_SID = VE.RES_TOPOLOGY_SID)
        where A.RES_TOPOLOGY_TYPE in ('R','DC','VE','RSC')
        )
        UNION
        (SELECT
        A.RES_STORAGE_SID AS RES_TOPOLOGY_SID
        , IF((A.RES_STORAGE_CLASS_SID is null || A.RES_STORAGE_CLASS_SID =
        ''),A.PARENT_TOPOLOGY_SID,A.RES_STORAGE_CLASS_SID)as PARENT_TOPOLOGY_SID
        , A.STORAGE_NAME AS RES_TOPOLOGY_NAME
        , concat(A.RES_STORAGE_SID,',','STORAGE') AS TOPOLOGY_VALUE
        , '../../../images/icon/storage.png' AS TOPOLOGY_ICON
        , '99' as SORT_NO
        FROM
        res_storage A
        INNER JOIN res_topology B ON ((A.PARENT_TOPOLOGY_SID = B.RES_TOPOLOGY_SID) or (A.RES_STORAGE_CLASS_SID =
        B.RES_TOPOLOGY_SID))

        )) T
        ORDER BY T.SORT_NO ,T.RES_TOPOLOGY_NAME
    </select>

    <select id="selectPoolTopologyTree" resultMap="BaseResultMap"
            parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        select
        A.RES_TOPOLOGY_SID
        ,A.PARENT_TOPOLOGY_SID
        ,A.RES_TOPOLOGY_NAME
        ,concat(A.RES_TOPOLOGY_SID,',',IFNULL(VE.VIRTUAL_PLATFORM_TYPE,A.RES_TOPOLOGY_TYPE)) as TOPOLOGY_VALUE
        ,(case A.RES_TOPOLOGY_TYPE
        when 'R' then '../../../images/icon/region.png'
        when 'DC' then '../../../images/icon/datacenter.png'
        when 'RZ' then '../../../images/icon/resource-pool-rz.png'
        when 'PNI' then '../../../images/icon/resource-pool-pni-2.png'
        when 'PNE' then '../../../images/icon/resource-pool-pne.png'
        when 'PCVX' then '../../../images/icon/resource-pool-pc.png'
        when 'PCX' then '../../../images/icon/resource-pool-pc.png'
        when 'PCVP' then '../../../images/icon/resource-pool-powervm.png'
        when 'PCP' then '../../../images/icon/resource-pool-powervm.png'
        when 'PN' then '../../../images/icon/network.png'
        when 'PS' then '../../../images/icon/resource-pool-storage-2.png'
        when 'PNV' then '../../../images/icon/resource-pool-vlan-2.png'
        else '../../../images/icon/resource-pool.png'
        end) as TOPOLOGY_ICON

        from res_topology A
        left join res_ve VE on (A.RES_TOPOLOGY_SID = VE.RES_TOPOLOGY_SID)
        where A.RES_TOPOLOGY_TYPE not in
        ('DC','VE','VC','RSC','PNE','PL','CE','NE','SE','H','C','F','S','SW','FW','LB','SAN','RC')
    </select>

    <select id="selectZoneByVm" resultType="java.lang.String" parameterType="java.lang.String">
        SELECT t2.RES_TOPOLOGY_SID AS zoneId
        FROM res_vm vm INNER JOIN res_host h ON vm.ALLOCATE_RES_HOST_SID = h.RES_HOST_SID
          INNER JOIN res_topology t1 ON h.RES_POOL_SID = t1.RES_TOPOLOGY_SID
          INNER JOIN res_topology t2 ON t1.PARENT_TOPOLOGY_SID = t2.RES_TOPOLOGY_SID
        WHERE vm.RES_VM_SID = #{resVmSid}
    </select>

    <select id="selectVeByZone" resultMap="BaseResultMap"
            parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        SELECT
        *

        FROM
        res_topology A
        LEFT JOIN res_topology B ON (
        A.RES_TOPOLOGY_SID = B.PARENT_TOPOLOGY_SID
        )
        LEFT JOIN res_vc C ON (
        B.RES_TOPOLOGY_SID = C.RES_TOPOLOGY_SID
        )
        LEFT JOIN res_topology D ON(
        C.RES_POOL_SID=D.RES_TOPOLOGY_SID
        )LEFT JOIN res_topology E ON(
        D.PARENT_TOPOLOGY_SID=E.RES_TOPOLOGY_SID
        )
        WHERE E.RES_TOPOLOGY_SID IN
        <foreach collection="condition.resZoneSids" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        GROUP BY A.RES_TOPOLOGY_SID
    </select>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        VE.VIRTUAL_PLATFORM_TYPE as VIRTUAL_PLATFORM_TYPE,
        VE.VIRTUAL_PLATFORM_VERSION as VIRTUAL_PLATFORM_VERSION,
        VE.MANAGEMENT_URL as MANAGEMENT_URL,
        VE.MANAGEMENT_ACCOUNT as MANAGEMENT_ACCOUNT,
        VE.MANAGEMENT_PASSWORD as MANAGEMENT_PASSWORD,
        VE.CONNECT_STATUS as CONNECT_STATUS,
        VE.UPDATE_STATUS as UPDATE_STATUS,
        VE.UPDATE_CYCLE as UPDATE_CYCLE,
        VE.UPDATE_TIME as UPDATE_TIME,
        VE.MQ_TOPIC as MQ_TOPIC,
        B.RES_TOPOLOGY_NAME as PARENT_TOPOLOGY_NAME,
        COUNT(C.RES_POOL_SID) as VLAN_COUNT,
        <include refid="Base_Column_List" />
        from res_topology A
        left join res_topology B ON (A.PARENT_TOPOLOGY_SID = B.RES_TOPOLOGY_SID)
        left join res_ve VE on (A.RES_TOPOLOGY_SID = VE.RES_TOPOLOGY_SID)
        left join res_pool_vlan C ON (A.RES_TOPOLOGY_SID = C.PARENT_TOPOLOGY_SID)
        where A.RES_TOPOLOGY_SID = #{resTopologySid}
    </select>
    <select id="selectVcByStorageSid" resultMap="BaseResultMap"
            parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        select a.RES_TOPOLOGY_SID,a.RES_TOPOLOGY_NAME,a.PARENT_TOPOLOGY_SID from res_topology a
        LEFT JOIN res_host b on b.PARENT_TOPOLOGY_SID = a.RES_TOPOLOGY_SID
        LEFT JOIN res_host_storage c ON c.RES_HOST_SID = b.RES_HOST_SID
        LEFT JOIN res_storage d on d.RES_STORAGE_SID = c.RES_STORAGE_SID
        where a.RES_TOPOLOGY_TYPE = #{condition.resTopologyType} and c.RES_STORAGE_SID = #{condition.resStorageSid};
    </select>
    <select id="selectTopologyByType" resultMap="BaseResultMap"
            parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        SELECT
        <include refid="Base_Column_List" />
        FROM res_topology a
        WHERE FIND_IN_SET(a.RES_TOPOLOGY_SID, getResTopologyChildList(#{condition.zoneId}))
        AND a.RES_TOPOLOGY_TYPE = #{condition.resTopologyType}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from res_topology
        where RES_TOPOLOGY_SID = #{resTopologySid}
    </delete>
    <delete id="deleteByParams" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        delete from res_topology
        <if test="_parameter != null">
            <include refid="Example_Where_Clause1" />
        </if>
    </delete>
    <insert id="insert" parameterType="cn.com.cloudstar.rightcloud.remote.api.pojo.system.maintenance.topo.ResTopology">
        <selectKey resultType="java.lang.String" keyProperty="resTopologySid" order="BEFORE">
            select UUID()
        </selectKey>
        insert into res_topology (RES_TOPOLOGY_SID, RES_TOPOLOGY_NAME, RES_TOPOLOGY_TYPE, PARENT_TOPOLOGY_SID,
        DESCRIPTION, SORT_NO, CREATED_BY, CREATED_DT, UPDATED_BY, UPDATED_DT,
        VERSION,org_sid)
        values (#{resTopologySid}, #{resTopologyName}, #{resTopologyType}, #{parentTopologySid},
        #{description}, #{sortNo}, #{createdBy}, #{createdDt}, #{updatedBy}, #{updatedDt},
        #{version},#{orgSid})
    </insert>
    <insert id="insertSelective" parameterType="cn.com.cloudstar.rightcloud.remote.api.pojo.system.maintenance.topo.ResTopology">
        <selectKey resultType="java.lang.String" keyProperty="resTopologySid" order="BEFORE">
            SELECT UUID()
        </selectKey>
        insert into res_topology
        <trim prefix="(" suffix=")" suffixOverrides=",">
            RES_TOPOLOGY_SID,
            <if test="resTopologyName != null">
                RES_TOPOLOGY_NAME,
            </if>
            <if test="resTopologyType != null">
                RES_TOPOLOGY_TYPE,
            </if>
            <if test="parentTopologySid != null">
                PARENT_TOPOLOGY_SID,
            </if>
            <if test="description != null">
                DESCRIPTION,
            </if>
            <if test="sortNo != null">
                SORT_NO,
            </if>
            <if test="createdBy != null">
                CREATED_BY,
            </if>
            <if test="createdDt != null">
                CREATED_DT,
            </if>
            <if test="updatedBy != null">
                UPDATED_BY,
            </if>
            <if test="updatedDt != null">
                UPDATED_DT,
            </if>
            <if test="version != null">
                VERSION,
            </if>
            <if test="orgSid != null">
                org_sid,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{resTopologySid},
            <if test="resTopologyName != null">
                #{resTopologyName},
            </if>
            <if test="resTopologyType != null">
                #{resTopologyType},
            </if>
            <if test="parentTopologySid != null">
                #{parentTopologySid},
            </if>
            <if test="description != null">
                #{description},
            </if>
            <if test="sortNo != null">
                #{sortNo},
            </if>
            <if test="createdBy != null">
                #{createdBy},
            </if>
            <if test="createdDt != null">
                #{createdDt},
            </if>
            <if test="updatedBy != null">
                #{updatedBy},
            </if>
            <if test="updatedDt != null">
                #{updatedDt},
            </if>
            <if test="version != null">
                #{version},
            </if>
            <if test="orgSid != null">
                #{orgSid},
            </if>
        </trim>
    </insert>
    <select id="countByParams" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
            resultType="java.lang.Integer">
        select count(*) from res_topology
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
    </select>
    <update id="updateByParamsSelective" parameterType="map">
        update res_topology A
        <set>
            <if test="record.resTopologySid != null">
                RES_TOPOLOGY_SID = #{record.resTopologySid},
            </if>
            <if test="record.resTopologyName != null">
                RES_TOPOLOGY_NAME = #{record.resTopologyName},
            </if>
            <if test="record.resTopologyType != null">
                RES_TOPOLOGY_TYPE = #{record.resTopologyType},
            </if>
            <if test="record.parentTopologySid != null">
                PARENT_TOPOLOGY_SID = #{record.parentTopologySid},
            </if>
            <if test="record.description != null">
                DESCRIPTION = #{record.description},
            </if>
            <if test="record.sortNo != null">
                SORT_NO = #{record.sortNo},
            </if>
            <if test="record.createdBy != null">
                CREATED_BY = #{record.createdBy},
            </if>
            <if test="record.createdDt != null">
                CREATED_DT = #{record.createdDt},
            </if>
            <if test="record.updatedBy != null">
                UPDATED_BY = #{record.updatedBy},
            </if>
            <if test="record.updatedDt != null">
                UPDATED_DT = #{record.updatedDt},
            </if>
            <if test="record.version != null">
                VERSION = #{record.version},
            </if>
            <if test="record.orgSid != null">
                org_sid = #{record.orgSid},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
    </update>
    <update id="updateByParams" parameterType="map">
        update res_topology
        set RES_TOPOLOGY_SID = #{record.resTopologySid},
        RES_TOPOLOGY_NAME = #{record.resTopologyName},
        RES_TOPOLOGY_TYPE = #{record.resTopologyType},
        PARENT_TOPOLOGY_SID = #{record.parentTopologySid},
        DESCRIPTION = #{record.description},
        SORT_NO = #{record.sortNo},
        CREATED_BY = #{record.createdBy},
        CREATED_DT = #{record.createdDt},
        UPDATED_BY = #{record.updatedBy},
        UPDATED_DT = #{record.updatedDt},
        VERSION = #{record.version},
        org_sid = #{record.orgSid}
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="cn.com.cloudstar.rightcloud.remote.api.pojo.system.maintenance.topo.ResTopology">
        update res_topology
        <set>
            <if test="resTopologyName != null">
                RES_TOPOLOGY_NAME = #{resTopologyName},
            </if>
            <if test="resTopologyType != null">
                RES_TOPOLOGY_TYPE = #{resTopologyType},
            </if>
            <if test="parentTopologySid != null">
                PARENT_TOPOLOGY_SID = #{parentTopologySid},
            </if>
            <if test="description != null">
                DESCRIPTION = #{description},
            </if>
            <if test="sortNo != null">
                SORT_NO = #{sortNo},
            </if>
            <if test="createdBy != null">
                CREATED_BY = #{createdBy},
            </if>
            <if test="createdDt != null">
                CREATED_DT = #{createdDt},
            </if>
            <if test="updatedBy != null">
                UPDATED_BY = #{updatedBy},
            </if>
            <if test="updatedDt != null">
                UPDATED_DT = #{updatedDt},
            </if>
            <if test="version != null">
                VERSION = #{version},
            </if>
            <if test="orgSid != null">
                org_sid = #{orgSid},
            </if>
        </set>
        where RES_TOPOLOGY_SID = #{resTopologySid} and org_sid = #{orgSid}
    </update>
    <update id="updateByPrimaryKey" parameterType="cn.com.cloudstar.rightcloud.remote.api.pojo.system.maintenance.topo.ResTopology">
        update res_topology
        set RES_TOPOLOGY_NAME = #{resTopologyName},
        RES_TOPOLOGY_TYPE = #{resTopologyType},
        PARENT_TOPOLOGY_SID = #{parentTopologySid},
        DESCRIPTION = #{description},
        SORT_NO = #{sortNo},
        CREATED_BY = #{createdBy},
        CREATED_DT = #{createdDt},
        UPDATED_BY = #{updatedBy},
        UPDATED_DT = #{updatedDt},
        VERSION = #{version},
        org_sid = #{record.orgSid}
        where RES_TOPOLOGY_SID = #{resTopologySid}
    </update>

    <select id="selectResZoneTopologyByParams" resultMap="BaseResultMap"
            parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        select
        distinct
        A.RES_TOPOLOGY_SID,
        A.RES_TOPOLOGY_NAME,
        A.PARENT_TOPOLOGY_SID,
        A.RES_TOPOLOGY_TYPE,
        concat(A.RES_TOPOLOGY_SID,',',A.RES_TOPOLOGY_TYPE) as TOPOLOGY_VALUE,
        (case A.RES_TOPOLOGY_TYPE
        when 'R' then '../../../images/icon/region.png'
        when 'DC' then '../../../images/icon/datacenter.png'
        when 'VC' then '../../../images/icon/cluster.png'
        else '../../../images/icon/resource-pool.png'
        end) as TOPOLOGY_ICON
        from res_topology A,
        res_topology B
        where
        B.RES_TOPOLOGY_TYPE = 'RZ' and FIND_IN_SET(A.RES_TOPOLOGY_SID,getResTopologyParentList(B.RES_TOPOLOGY_SID))
        order by A.SORT_NO,A.RES_TOPOLOGY_NAME
    </select>

    <select id="selectResZoneTopologyChildByParams" resultMap="BaseResultMap"
            parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        (select
        A.RES_TOPOLOGY_SID,
        A.RES_TOPOLOGY_NAME,
        A.PARENT_TOPOLOGY_SID,
        A.RES_TOPOLOGY_TYPE,
        A.SORT_NO,
        concat(A.RES_TOPOLOGY_SID,',',A.RES_TOPOLOGY_TYPE) as TOPOLOGY_VALUE,
        (case A.RES_TOPOLOGY_TYPE
        when 'R' then '../../../images/icon/region.png'
        when 'DC' then '../../../images/icon/datacenter.png'
        when 'VC' then '../../../images/icon/cluster.png'
        else '../../../images/icon/resource-pool.png'
        end) as TOPOLOGY_ICON
        from res_topology A,
        res_topology B
        where
        FIND_IN_SET(A.RES_TOPOLOGY_SID,getResTopologyChildList(B.RES_TOPOLOGY_SID))
        <if test="condition.resZoneSids != null">
            and B.RES_TOPOLOGY_SID IN
            <foreach collection="condition.resZoneSids" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="condition.resTopologyType != null">
            <if test="condition.resTopologyType == 'PC'">
                and A.RES_TOPOLOGY_TYPE IN('RZ', 'PC')
            </if>
            <if test="condition.resTopologyType == 'PN'">
                and A.RES_TOPOLOGY_TYPE IN('RZ', 'PN', 'PNI', 'PNE')
            </if>
        </if>
        <if test="condition.resTopologyType == null">
            and A.RES_TOPOLOGY_TYPE IN('RZ', 'PC', 'PN', 'PNI', 'PNE')
        </if>
        )
        <if test="condition.resTopologyType == null or condition.resTopologyType == 'PC'">
            UNION
            (
            SELECT
            A.RES_TOPOLOGY_SID AS RES_TOPOLOGY_SID,
            C.RES_TOPOLOGY_NAME AS RES_TOPOLOGY_NAME,
            A.RES_POOL_SID AS PARENT_TOPOLOGY_SID,
            'VC' AS RES_TOPOLOGY_TYPE,
            B.SORT_NO AS SORT_NO,
            concat(A.RES_TOPOLOGY_SID,',','VC') as TOPOLOGY_VALUE,
            '../../../images/icon/cluster.png' AS TOPOLOGY_ICON
            FROM
            res_vc A
            INNER JOIN res_topology B ON (
            A.RES_POOL_SID = B.RES_TOPOLOGY_SID
            )
            INNER JOIN res_topology C ON (
            A.RES_TOPOLOGY_SID = C.RES_TOPOLOGY_SID
            )
            INNER JOIN res_topology D
            WHERE
            FIND_IN_SET(B.RES_TOPOLOGY_SID,getResTopologyChildList (D.RES_TOPOLOGY_SID))
            <if test="condition.resZoneSids != null">
                and D.RES_TOPOLOGY_SID IN(${condition.resZoneSids})
            </if>
            )
        </if>
        <if test="condition.resTopologyType == null or condition.resTopologyType == 'PC'">
            UNION
            (
            SELECT
            A.RES_HOST_SID AS RES_TOPOLOGY_SID,
            A.HOST_NAME AS RES_TOPOLOGY_NAME,
            A.PARENT_TOPOLOGY_SID AS PARENT_TOPOLOGY_SID,
            'RES-HOST' AS RES_TOPOLOGY_TYPE,
            9 AS SORT_NO,
            concat(A.RES_HOST_SID,',','RES-HOST') as TOPOLOGY_VALUE,
            '../../../images/icon/host.png' AS TOPOLOGY_ICON
            FROM
            res_host A
            INNER JOIN res_vc B ON (
            A.PARENT_TOPOLOGY_SID = B.RES_TOPOLOGY_SID
            )
            INNER JOIN res_topology C ON (
            B.RES_POOL_SID = C.RES_TOPOLOGY_SID
            )
            INNER JOIN res_topology D
            WHERE
            FIND_IN_SET(C.RES_TOPOLOGY_SID,getResTopologyChildList (D.RES_TOPOLOGY_SID))
            <if test="condition.resZoneSids != null">
                and D.RES_TOPOLOGY_SID IN(${condition.resZoneSids})
            </if>
            )
        </if>
        <if test="condition.resTopologyType == null or condition.resTopologyType == 'PN'">
            UNION
            (
            SELECT
            A.RES_NETWORK_SID AS RES_TOPOLOGY_SID,
            A.NETWORK_NAME AS RES_TOPOLOGY_NAME,
            A.RES_POOL_SID AS PARENT_TOPOLOGY_SID,
            'RES-NETWORK' AS RES_TOPOLOGY_TYPE,
            9 AS SORT_NO,
            concat(A.RES_NETWORK_SID,',','RES-NETWORK') as TOPOLOGY_VALUE,
            '../../../images/icon/network.png' AS TOPOLOGY_ICON
            FROM
            res_network A
            INNER JOIN res_topology B ON (
            A.RES_POOL_SID = B.RES_TOPOLOGY_SID
            )
            INNER JOIN res_topology C
            WHERE
            FIND_IN_SET(B.RES_TOPOLOGY_SID,getResTopologyChildList(C.RES_TOPOLOGY_SID))
            <if test="condition.resZoneSids != null">
                and C.RES_TOPOLOGY_SID IN
                <foreach collection="condition.resZoneSids" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            )
        </if>
        order by SORT_NO,RES_TOPOLOGY_NAME
    </select>
    <insert id="create" parameterType="cn.com.cloudstar.rightcloud.remote.api.pojo.system.maintenance.topo.ResTopology">
        insert into res_topology (RES_TOPOLOGY_SID, RES_TOPOLOGY_NAME, RES_TOPOLOGY_TYPE, PARENT_TOPOLOGY_SID,
        DESCRIPTION, SORT_NO, CREATED_BY, CREATED_DT, UPDATED_BY, UPDATED_DT,
        VERSION,org_sid)
        values (#{resTopologySid}, #{resTopologyName}, #{resTopologyType}, #{parentTopologySid},
        #{description}, #{sortNo}, #{createdBy}, #{createdDt}, #{updatedBy}, #{updatedDt},
        #{version},#{orgSid})
    </insert>

    <!-- 根据父菜单id获取子菜单的最大排序码 -->
    <select id="findMaxSortByPid" resultType="int" parameterType="java.lang.String" >
        select max(sort_no) from res_topology where PARENT_TOPOLOGY_SID = #{parentId,jdbcType=BIGINT}
    </select>
</mapper>
