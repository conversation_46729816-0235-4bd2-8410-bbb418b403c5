/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.listener;

import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.*;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.*;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.constants.res.status.NetworkStatus;
import cn.com.cloudstar.rightcloud.common.constants.res.status.RdsStatus;
import cn.com.cloudstar.rightcloud.common.constants.res.type.CloudEnvType;
import cn.com.cloudstar.rightcloud.common.constants.res.type.RdsConstants.RdsConnectionIpType;
import cn.com.cloudstar.rightcloud.common.constants.type.ResourceType;
import cn.com.cloudstar.rightcloud.common.enums.resource.ResourceOperateEnum;
import cn.com.cloudstar.rightcloud.common.enums.resource.ResourceTypeEnum;
import cn.com.cloudstar.rightcloud.common.exception.BizException;
import cn.com.cloudstar.rightcloud.common.exception.RetryException;
import cn.com.cloudstar.rightcloud.common.exception.resolver.CloudErrorResolver;
import cn.com.cloudstar.rightcloud.common.util.BeanConvertUtil;
import cn.com.cloudstar.rightcloud.common.util.DateUtil;
import cn.com.cloudstar.rightcloud.common.util.RetryUtil;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.common.util.json.JsonUtil;
import cn.com.cloudstar.rightcloud.common.websocket.annonation.Message;
import cn.com.cloudstar.rightcloud.common.websocket.annonation.MessageParam;
import cn.com.cloudstar.rightcloud.common.websocket.support.OperateEnum;
import cn.com.cloudstar.rightcloud.common.websocket.support.ServerMsgType;
import cn.com.cloudstar.rightcloud.core.annotation.log.LogMethod;
import cn.com.cloudstar.rightcloud.core.annotation.log.LogParam;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicWebUtil;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResZone;
import cn.com.cloudstar.rightcloud.resource.dao.network.NetworkMapper;
import cn.com.cloudstar.rightcloud.resource.dao.network.ResFloatingIpMapper;
import cn.com.cloudstar.rightcloud.resource.dao.network.ResVpcMapper;
import cn.com.cloudstar.rightcloud.resource.dao.zone.ResZoneMapper;
import cn.com.cloudstar.rightcloud.resource.notify.BizNotify;
import cn.com.cloudstar.rightcloud.resource.service.rds.ResRdsConnectionService;
import cn.com.cloudstar.rightcloud.resource.service.rds.ResRdsService;
import com.google.common.base.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

/**
 * The type RdsResultListener.
 * <p>
 *
 * <AUTHOR>
 * @date 2017/11/22
 */
@Component
public class RdsResultListener {

    private final Logger logger = LoggerFactory.getLogger(RdsResultListener.class);

    @Autowired
    private ResRdsConnectionService resRdsConnectionService;

    @Autowired
    private ResVpcMapper resVpcMapper;

    @Autowired
    private NetworkMapper networkMapper;

    @Autowired
    private ResRdsService resRdsService;
    @Autowired
    ResZoneMapper resZoneMapper;
    @Autowired
    ResFloatingIpMapper floatingIpMapper;


    /**
     * 申请RDS外网
     **/
    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#connectStringResult.options.get('id')", resourceType = ResourceTypeEnum.RDS_CONNECT_STRING, opUser = "#connectStringResult.opUser", operate = ResourceOperateEnum.CREATE, success = "#connectStringResult.success", orgSid = "#connectStringResult.orgSid")
    @Message(refKey = "#connectStringResult.options.get('id')", envId = "#connectStringResult.cloudEnvId", msgType = ServerMsgType.RDS_CONNECT_STRING, opUser = "#connectStringResult.opUser", operate = OperateEnum.CREATE, success = "#connectStringResult.success", errorMsg = "#connectStringResult.errMsg")

    public void handleMessage(
            @LogParam("connectStringResult") @MessageParam("connectStringResult") ConnectStringResult connectStringResult) {
        logger.info("申请外网地址 | 回调参数 ： {}", JsonUtil.toJson(connectStringResult));
        try {
            Long resRdsId = Long.valueOf(connectStringResult.getOptions().get("resRdsId").toString());
            if (connectStringResult.isSuccess()) {
                Long cloudEnvId = Long.valueOf(connectStringResult.getOptions().get("cloudEnvId").toString());
                Long id = Long.valueOf(connectStringResult.getOptions().get("id").toString());
                ResRdsConnection connection = makeConnectStringBean(cloudEnvId, resRdsId,
                                                                    connectStringResult.getConnectString());
                connection.setId(id);
                BasicWebUtil.prepareUpdateParams(connection, connectStringResult.getOpUser());
                resRdsConnectionService.updateByPrimaryKeySelective(connection);

                ResRds resRds = new ResRds();
                resRds.setId(resRdsId);
                resRds.setStatus(connectStringResult.getOldInsStatus());
                BasicWebUtil.prepareUpdateParams(resRds, connectStringResult.getOpUser());
                resRdsService.updateByPrimaryKeySelective(resRds);
            } else {
                ResRds resRds = new ResRds();
                resRds.setId(resRdsId);
                resRds.setStatus(connectStringResult.getOldInsStatus());
                resRdsService.updateByPrimaryKeySelective(resRds);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }

    }

    /**
     * 编辑RDS网络连接
     **/
    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#connectStringResult.options.get('id')", resourceType = ResourceTypeEnum.RDS_CONNECT_STRING, opUser = "#connectStringResult.opUser", operate = ResourceOperateEnum.MODIFY, success = "#connectStringResult.success", orgSid = "#connectStringResult.orgSid")
    @Message(refKey = "#connectStringResult.options.get('id')", envId = "#connectStringResult.cloudEnvId", msgType = ServerMsgType.RDS_CONNECT_STRING, opUser = "#connectStringResult.opUser", operate = OperateEnum.UPDATE, success = "#connectStringResult.success", errorMsg = "#connectStringResult.errMsg")
    public void handleMessage(
            @LogParam("connectStringResult") @MessageParam("connectStringResult") ConnectStringUpdateResult connectStringResult) {
        logger.info("更新外网地址 | 回调参数 ： {}", JsonUtil.toJson(connectStringResult));
        try {
            Long resRdsId = Long.valueOf(connectStringResult.getOptions().get("resRdsId").toString());
            if (connectStringResult.isSuccess()) {
                ResRdsConnection record = BeanConvertUtil.convert(connectStringResult.getOptions(),
                                                                  ResRdsConnection.class);
                resRdsConnectionService.updateByPrimaryKeySelective(record);

                ResRds resRds = new ResRds();
                resRds.setId(resRdsId);
                resRds.setStatus(connectStringResult.getOldInsStatus());
                BasicWebUtil.prepareUpdateParams(resRds, connectStringResult.getOpUser());
                resRdsService.updateByPrimaryKeySelective(resRds);
            } else {
                ResRds resRds = new ResRds();
                resRds.setId(resRdsId);
                resRds.setStatus(connectStringResult.getOldInsStatus());
                resRdsService.updateByPrimaryKeySelective(resRds);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }

    }

    /**
     * 释放RDS外网
     **/
    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#connectStringResult.options.get('id')", resourceType = ResourceTypeEnum.RDS_CONNECT_STRING, opUser = "#connectStringResult.opUser", operate = ResourceOperateEnum.DELETE, success = "#connectStringResult.success", orgSid = "#connectStringResult.orgSid")
    @Message(refKey = "#connectStringResult.options.get('id')", envId = "#connectStringResult.cloudEnvId", msgType = ServerMsgType.RDS_CONNECT_STRING, opUser = "#connectStringResult.opUser", operate = OperateEnum.DELETE, success = "#connectStringResult.success", errorMsg = "#connectStringResult.errMsg")
    public void handleMessage(
            @LogParam("connectStringResult") @MessageParam("connectStringResult") ConnectStringReleaseResult connectStringResult) {
        logger.info("释放外网地址 | 回调参数 ： {}", JsonUtil.toJson(connectStringResult));
        try {
            Long resRdsId = Long.valueOf(connectStringResult.getOptions().get("resRdsId").toString());
            if (connectStringResult.isSuccess()) {
                resRdsConnectionService.deleteByPrimaryKey(connectStringResult.getConnectionId());

                ResRds resRds = new ResRds();
                resRds.setId(resRdsId);
                resRds.setStatus(connectStringResult.getOldInsStatus());
                BasicWebUtil.prepareUpdateParams(resRds, connectStringResult.getOpUser());
                resRdsService.updateByPrimaryKeySelective(resRds);
            } else {
                ResRds resRds = new ResRds();
                resRds.setId(resRdsId);
                resRds.setStatus(connectStringResult.getOldInsStatus());
                resRdsService.updateByPrimaryKeySelective(resRds);
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }

    }

    private ResRdsConnection makeConnectStringBean(Long cloudEnvId, Long resRdsId, ConnectString connectString) {
        ResRdsConnection connection = new ResRdsConnection();
        connection.setCloudEnvId(cloudEnvId);
        connection.setResRdsId(resRdsId);
        BeanUtils.copyProperties(connectString, connection);

        connection.setResVpcId(getResVpcId(cloudEnvId, connectString.getResVpcId()));
        connection.setResNetworkId(getResNetworkId(cloudEnvId, connectString.getResNetworkId()));
        return connection;
    }

    /**
     * 查询VPC ID
     */
    private Long getResVpcId(Long cloudEnvId, String uuid) {
        Criteria criteria = new Criteria();
        criteria.put("cloudEnvId", cloudEnvId);
        criteria.put("uuid", uuid);
        List<ResVpc> resVpcs = resVpcMapper.selectByParams(criteria);
        if (!CollectionUtils.isEmpty(resVpcs)) {
            return resVpcs.get(0).getId();
        }
        return null;
    }

    /**
     * 查询Network ID
     */
    private Long getResNetworkId(Long cloudEnvId, String uuid) {
        Criteria criteria = new Criteria();
        criteria.put("cloudEnvId", cloudEnvId);
        criteria.put("uuid", uuid);
        List<Network> networks = networkMapper.selectByParams(criteria);
        if (!CollectionUtils.isEmpty(networks)) {
            return networks.get(0).getId();
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#dbInstanceRestartResult.options.get('resRdsId')", resourceType = ResourceTypeEnum.RDS, opUser = "#dbInstanceRestartResult.opUser", operate = ResourceOperateEnum.RESTART, success = "#dbInstanceRestartResult.success", orgSid = "#dbInstanceRestartResult.orgSid")
    @Message(refKey = "#dbInstanceRestartResult.options.get('resRdsId')", envId = "#dbInstanceRestartResult.cloudEnvId", msgType = ServerMsgType.RDS, opUser = "#dbInstanceRestartResult.opUser", operate = OperateEnum.RESTART, success = "#dbInstanceRestartResult.success", errorMsg = "#dbInstanceRestartResult.errMsg")
    public void handleMessage(
            @LogParam("dbInstanceRestartResult") @MessageParam("dbInstanceRestartResult") DBInstanceRestartResult dbInstanceRestartResult) {
        logger.info("重启实例 | 回调参数 ： {}", JsonUtil.toJson(dbInstanceRestartResult));
        try {
            Long resRdsId = Long.valueOf(dbInstanceRestartResult.getOptions().get("resRdsId").toString());
            if (dbInstanceRestartResult.isSuccess()) {
                ResRds resRds = new ResRds();
                resRds.setId(resRdsId);
                resRds.setStatus(RdsStatus.RUNNING);
                BasicWebUtil.prepareUpdateParams(resRds, dbInstanceRestartResult.getOpUser());
                resRdsService.updateByPrimaryKeySelective(resRds);
            } else {
                // 重启失败 状态还原
                ResRds resRdsUpdate = new ResRds();
                resRdsUpdate.setId(resRdsId);
                resRdsUpdate.setStatus(RdsStatus.RUNNING);
                BasicWebUtil.prepareUpdateParams(resRdsUpdate, dbInstanceRestartResult.getOpUser());
                resRdsService.updateByPrimaryKeySelective(resRdsUpdate);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#dbInstanceEnlargeVolumeResult.options.get('resRdsId')", resourceType = ResourceTypeEnum.RDS, opUser = "#dbInstanceEnlargeVolumeResult.opUser", operate = ResourceOperateEnum.ENLARGE_VOLUME, success = "#dbInstanceEnlargeVolumeResult.success", orgSid = "#dbInstanceEnlargeVolumeResult.orgSid")
    @Message(refKey = "#dbInstanceEnlargeVolumeResult.options.get('resRdsId')", envId = "#dbInstanceEnlargeVolumeResult.cloudEnvId", msgType = ServerMsgType.RDS, opUser = "#dbInstanceEnlargeVolumeResult.opUser", operate = OperateEnum.ENLARGE_VOLUME, success = "#dbInstanceEnlargeVolumeResult.success", errorMsg = "#dbInstanceEnlargeVolumeResult.errMsg")
    public void handleMessage(
            @LogParam("dbInstanceEnlargeVolumeResult") @MessageParam("dbInstanceEnlargeVolumeResult") DBInstanceEnlargeVolumeResult dbInstanceEnlargeVolumeResult) {
        logger.info("RDS磁盘扩容实例 | 回调参数 ： {}", JsonUtil.toJson(dbInstanceEnlargeVolumeResult));
        try {
            Long resRdsId = Long.valueOf(dbInstanceEnlargeVolumeResult.getOptions().get("resRdsId").toString());
            if (dbInstanceEnlargeVolumeResult.isSuccess()) {
                ResRds resRdsUpdate = new ResRds();
                resRdsUpdate.setId(resRdsId);
                resRdsUpdate.setStorage(dbInstanceEnlargeVolumeResult.getStorageSize());
                resRdsUpdate.setStatus(RdsStatus.RUNNING);
                BasicWebUtil.prepareUpdateParams(resRdsUpdate, dbInstanceEnlargeVolumeResult.getOpUser());
                resRdsService.updateByPrimaryKeySelective(resRdsUpdate);
            } else {
                // 扩容失败 状态还原
                ResRds resRdsUpdate = new ResRds();
                resRdsUpdate.setId(resRdsId);
                resRdsUpdate.setStatus(RdsStatus.RUNNING);
                BasicWebUtil.prepareUpdateParams(resRdsUpdate, dbInstanceEnlargeVolumeResult.getOpUser());
                resRdsService.updateByPrimaryKeySelective(resRdsUpdate);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#dbInstanceReleaseResult.options.get('id')", resourceType = ResourceTypeEnum.RDS, opUser = "#dbInstanceReleaseResult.opUser", operate = ResourceOperateEnum.DELETE, success = "#dbInstanceReleaseResult.success", orgSid = "#dbInstanceReleaseResult.orgSid")
    @Message(refKey = "#dbInstanceReleaseResult.options.get('id')", envId = "#dbInstanceReleaseResult.cloudEnvId", msgType = ServerMsgType.RDS, opUser = "#dbInstanceReleaseResult.opUser", operate = OperateEnum.DELETE, success = "#dbInstanceReleaseResult.success", errorMsg = "#dbInstanceReleaseResult.errMsg")
    public void handleMessage(
            @LogParam("dbInstanceReleaseResult") @MessageParam("dbInstanceReleaseResult") DBInstanceReleaseResult dbInstanceReleaseResult) {
        logger.info("释放实例 | 回调参数 ： {}", JsonUtil.toJson(dbInstanceReleaseResult));
        try {
            if (dbInstanceReleaseResult.isSuccess()) {
                Long resRdsId = Long.valueOf(dbInstanceReleaseResult.getOptions().get("id").toString());

                resRdsService.deleteByPrimaryKey(resRdsId);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void handleMessage(@LogParam("dbInstanceDescribeResult") DBInstanceDescribeResult dbInstanceDescribeResult) {
        logger.info("查询RDS实例 | 回调参数 ： {}", JsonUtil.toJson(dbInstanceDescribeResult));
        try {
            Long resRdsId = Long.valueOf(dbInstanceDescribeResult.getOptions().get("id").toString());
            ResRds rds = resRdsService.selectByPrimaryKey(resRdsId);
            if (dbInstanceDescribeResult.isSuccess()) {
                rds.setId(resRdsId);
                rds.setUuid(dbInstanceDescribeResult.getDbInstance().getdBInstanceId());
                rds.setNetworkType(dbInstanceDescribeResult.getDbInstance().getInstanceNetworkType());
                rds.setConnectionMode(dbInstanceDescribeResult.getDbInstance().getConnectionMode());
                rds.setCpu(Integer.parseInt(dbInstanceDescribeResult.getDbInstance().getdBInstanceCPU()));
                rds.setMemory(dbInstanceDescribeResult.getDbInstance().getdBInstanceMemory());
                if (!Strings.isNullOrEmpty(dbInstanceDescribeResult.getDbInstance().getExpireTime())) {
                    rds.setExpireTime(DateUtil.utcToDate(dbInstanceDescribeResult.getDbInstance().getExpireTime()));
                }
                rds.setResVpcId(getResVpcId(rds.getCloudEnvId(), dbInstanceDescribeResult.getDbInstance().getVpcId()));
                rds.setResNetworkId(
                        getResNetworkId(rds.getCloudEnvId(), dbInstanceDescribeResult.getDbInstance().getvSwitchId()));
                rds.setCategory(dbInstanceDescribeResult.getDbInstance().getCategory());
                rds.setDescription(dbInstanceDescribeResult.getDbInstance().getdBInstanceId());
                rds.setStatus(RdsStatus.RUNNING);

                BasicWebUtil.prepareUpdateParams(rds, dbInstanceDescribeResult.getOpUser());
                resRdsService.updateByPrimaryKeySelective(rds);

                Criteria criteria = new Criteria();
                criteria.put("resRdsId", resRdsId);
                criteria.put("connectionString", dbInstanceDescribeResult.getConnectString().getConnectionString());
                List<ResRdsConnection> resRdsConnections = resRdsConnectionService.selectByParams(criteria);
                if (CollectionUtils.isEmpty(resRdsConnections)) {
                    ResRdsConnection connection = makeConnectStringBean(rds.getCloudEnvId(), resRdsId,
                                                                        dbInstanceDescribeResult.getConnectString());
                    BasicWebUtil.prepareInsertParams(connection, dbInstanceDescribeResult.getOpUser());
                    resRdsConnectionService.insertSelective(connection);
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#dbInstanceCreateResult.options.get('id')", resourceType = ResourceTypeEnum.RDS, opUser = "#dbInstanceCreateResult.opUser", operate = ResourceOperateEnum.CREATE, success = "#dbInstanceCreateResult.success", orgSid = "#dbInstanceCreateResult.orgSid")
    @Message(refKey = "#dbInstanceCreateResult.options.get('id')", envId = "#dbInstanceCreateResult.cloudEnvId", msgType = ServerMsgType.RDS, opUser = "#dbInstanceCreateResult.opUser", operate = OperateEnum.CREATE, success = "#dbInstanceCreateResult.success", errorMsg = "#dbInstanceCreateResult.errMsg")
    @BizNotify
    public void handleMessage(
            @LogParam("dbInstanceCreateResult") @MessageParam("dbInstanceCreateResult") DBInstanceCreateResult dbInstanceCreateResult) {
        logger.info("创建RDS实例 | 回调参数 ： {}", JsonUtil.toJson(dbInstanceCreateResult));
        try {
            Long resRdsId = Long.valueOf(dbInstanceCreateResult.getOptions().get("id").toString());
            ResRds rds = resRdsService.selectByPrimaryKey(resRdsId);
            if (null == rds) {
                // 可能事务没有提交导致的查询不到，重试
                AtomicReference<ResRds> atomicReference = new AtomicReference<>(null);
                Long finalResRdsId = resRdsId;
                RetryUtil.retry(3, 5, TimeUnit.SECONDS, false, () -> {
                    ResRds r = resRdsService.selectByPrimaryKey(finalResRdsId);
                    if (r == null) {
                        logger.info("rds:{} not found", finalResRdsId);
                        throw new RetryException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1867841409));
                    } else {
                        atomicReference.set(r);
                    }
                });
                rds = atomicReference.get();
            }
            // 同步可能导致数据被删除，如果根据主键取出的数据为空，再根据资源ID再取一次
            if (Objects.isNull(rds) && dbInstanceCreateResult.isSuccess()) {
                Criteria criteria = new Criteria();
                criteria.put("uuid", dbInstanceCreateResult.getDbInstance().getdBInstanceId());
                List<ResRds> resRds = resRdsService.selectByParams(criteria);
                if (!CollectionUtils.isEmpty(resRds)) {
                    rds = resRds.get(0);
                    resRdsId = rds.getId();
                }
            }

            if (dbInstanceCreateResult.isSuccess()) {
                if (CloudEnvType.ALIYUN.equals(dbInstanceCreateResult.getProviderType())) {
                    rds.setUuid(dbInstanceCreateResult.getDbInstance().getdBInstanceId());
                    rds.setNetworkType(dbInstanceCreateResult.getDbInstance().getInstanceNetworkType());
                    rds.setConnectionMode(dbInstanceCreateResult.getDbInstance().getConnectionMode());
                    rds.setCpu(Integer.parseInt(dbInstanceCreateResult.getDbInstance().getdBInstanceCPU()));
                    rds.setMemory(dbInstanceCreateResult.getDbInstance().getdBInstanceMemory());
                    if (!Strings.isNullOrEmpty(dbInstanceCreateResult.getDbInstance().getExpireTime())) {
                        rds.setExpireTime(DateUtil.utcToDate(dbInstanceCreateResult.getDbInstance().getExpireTime()));
                    }
                    rds.setResVpcId(
                            getResVpcId(rds.getCloudEnvId(), dbInstanceCreateResult.getDbInstance().getVpcId()));
                    rds.setResNetworkId(getResNetworkId(rds.getCloudEnvId(),
                                                        dbInstanceCreateResult.getDbInstance().getvSwitchId()));
                    rds.setCategory(dbInstanceCreateResult.getDbInstance().getCategory());
                    rds.setDescription(dbInstanceCreateResult.getDbInstance().getdBInstanceId());
                    rds.setInstanceType(dbInstanceCreateResult.getDbInstance().getdBInstanceType());
                    rds.setStatus(RdsStatus.RUNNING);
                    rds.setDbInstanceClass(dbInstanceCreateResult.getDbInstance().getdBInstanceClass());
                    BasicWebUtil.prepareUpdateParams(rds, dbInstanceCreateResult.getOpUser());
                    resRdsService.updateByPrimaryKeySelective(rds);

                    Criteria criteria = new Criteria();
                    criteria.put("resRdsId", resRdsId);
                    criteria.put("connectionString", dbInstanceCreateResult.getConnectString().getConnectionString());
                    List<ResRdsConnection> resRdsConnections = resRdsConnectionService.selectByParams(criteria);
                    if (CollectionUtils.isEmpty(resRdsConnections)) {
                        ResRdsConnection connection = makeConnectStringBean(rds.getCloudEnvId(), resRdsId,
                                                                            dbInstanceCreateResult.getConnectString());
                        BasicWebUtil.prepareInsertParams(connection, dbInstanceCreateResult.getOpUser());
                        resRdsConnectionService.insertSelective(connection);
                    }
                } else if (CloudEnvType.AWS.equals(dbInstanceCreateResult.getProviderType())) {
                    rds.setUuid(dbInstanceCreateResult.getDbInstance().getdBInstanceId());
                    rds.setStatus(RdsStatus.RUNNING);
                    rds.setAddress(dbInstanceCreateResult.getDbInstance().getAddress());
                    rds.setPort(dbInstanceCreateResult.getDbInstance().getPort());
                    BasicWebUtil.prepareUpdateParams(rds, dbInstanceCreateResult.getOpUser());
                    resRdsService.updateByPrimaryKeySelective(rds);
                } else if (CloudEnvType.HUAWEICLOUD.equals(dbInstanceCreateResult.getProviderType())) {
                    rds.setUuid(dbInstanceCreateResult.getDbInstance().getdBInstanceId());
                    rds.setCpu(Integer.parseInt(dbInstanceCreateResult.getDbInstance().getdBInstanceCPU()));
                    rds.setMemory(dbInstanceCreateResult.getDbInstance().getdBInstanceMemory());
                    rds.setCategory(dbInstanceCreateResult.getDbInstance().getCategory());
                    rds.setDescription(dbInstanceCreateResult.getDbInstance().getdBInstanceDescription());
                    rds.setStatus(RdsStatus.RUNNING);
                    BasicWebUtil.prepareUpdateParams(rds, dbInstanceCreateResult.getOpUser());
                    resRdsService.updateByPrimaryKeySelective(rds);

                    Criteria criteria = new Criteria();
                    criteria.put("resRdsId", resRdsId);
                    criteria.put("connectionString", dbInstanceCreateResult.getConnectString().getConnectionString());
                    List<ResRdsConnection> resRdsConnections = resRdsConnectionService.selectByParams(criteria);
                    if (CollectionUtils.isEmpty(resRdsConnections)) {
                        ResRdsConnection connection = makeConnectStringBean(rds.getCloudEnvId(), resRdsId,
                                                                            dbInstanceCreateResult.getConnectString());
                        BasicWebUtil.prepareInsertParams(connection, dbInstanceCreateResult.getOpUser());
                        resRdsConnectionService.insertSelective(connection);
                    }
                }

            } else {
                String failReason = CloudErrorResolver.getErrorMsg(dbInstanceCreateResult.getErrMsg());
                ResRds r = new ResRds();
                r.setId(resRdsId);
                r.setErrorMsg(failReason);
                r.setStatus(RdsStatus.CREATE_FAILURE);
                int c = resRdsService.updateByPrimaryKeySelective(r);
                if (c == 0) {
                    logger.warn("RDS状态更新失败!，resRdsId: {}", resRdsId);
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#dbInstanceReconfigResult.dbInstanceId", resourceType = ResourceTypeEnum.RDS,
            opUser = "#dbInstanceReconfigResult.opUser", operate = ResourceOperateEnum.RE_CONFIG,
            success = "#dbInstanceReconfigResult.success", orgSid = "#dbInstanceReconfigResult.orgSid")
    @Message(refKey = "#dbInstanceReconfigResult.dbInstanceId", envId = "#dbInstanceReconfigResult.cloudEnvId",
            msgType = ServerMsgType.RDS, opUser = "#dbInstanceReconfigResult.opUser", operate = OperateEnum.RE_CONFIG,
            success = "#dbInstanceReconfigResult.success", errorMsg = "#dbInstanceReconfigResult.errMsg")
    public void handleMessage(
            @LogParam("dbInstanceReconfigResult") @MessageParam("dbInstanceReconfigResult") DBInstanceReconfigResult reconfigResult) {
        logger.info("RDS规格变更 | 回调参数 ： {}", JsonUtil.toJson(reconfigResult));
        try {
            Long resRdsId = Long.valueOf(reconfigResult.getOptions().get("resRdsId").toString());
            if (reconfigResult.isSuccess()) {
                ResRds resRds = new ResRds();
                resRds.setId(resRdsId);
                resRds.setStatus(RdsStatus.RUNNING);
                resRds.setInstanceType(reconfigResult.getInstanceType());
                resRds.setCpu(reconfigResult.getCpu());
                resRds.setMemory(reconfigResult.getMemory());
                BasicWebUtil.prepareUpdateParams(resRds, reconfigResult.getOpUser());
                resRdsService.updateByPrimaryKeySelective(resRds);
            } else {
                // 变更失败 状态还原
                ResRds resRdsUpdate = new ResRds();
                resRdsUpdate.setId(resRdsId);
                resRdsUpdate.setStatus(RdsStatus.RUNNING);
                BasicWebUtil.prepareUpdateParams(resRdsUpdate, reconfigResult.getOpUser());
                resRdsService.updateByPrimaryKeySelective(resRdsUpdate);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#dbInstanceFailoverResult.dbInstanceId", resourceType = ResourceTypeEnum.RDS,
            opUser = "#dbInstanceFailoverResult.opUser", operate = ResourceOperateEnum.MODIFY,
            success = "#dbInstanceFailoverResult.success", orgSid = "#dbInstanceFailoverResult.orgSid")
    @Message(refKey = "#dbInstanceFailoverResult.dbInstanceId", envId = "#dbInstanceFailoverResult.cloudEnvId",
            msgType = ServerMsgType.RDS, opUser = "#dbInstanceFailoverResult.opUser", operate = OperateEnum.MODIFY,
            success = "#dbInstanceFailoverResult.success", errorMsg = "#dbInstanceFailoverResult.errMsg")
    public void handleMessage(
            @LogParam("dbInstanceFailoverResult") @MessageParam("dbInstanceFailoverResult") DBInstanceFailoverResult failoverResult) {
        logger.info("RDS主备切换 | 回调参数 ： {}", JsonUtil.toJson(failoverResult));
        try {
            Long resRdsId = Long.valueOf(failoverResult.getOptions().get("resRdsId").toString());
            if (failoverResult.isSuccess()) {
                ResRds rds = resRdsService.selectByPrimaryKey(resRdsId);
                String zoneId = rds.getZoneId();
                String slaveZoneId = rds.getSlaveZoneId();

                ResRds resRds = new ResRds();
                resRds.setId(resRdsId);
                resRds.setZoneId(slaveZoneId);
                resRds.setSlaveZoneId(zoneId);
                resRds.setStatus(RdsStatus.RUNNING);
                BasicWebUtil.prepareUpdateParams(resRds, failoverResult.getOpUser());
                resRdsService.updateByPrimaryKeySelective(resRds);
            } else {
                // 切换失败 状态还原
                ResRds resRdsUpdate = new ResRds();
                resRdsUpdate.setId(resRdsId);
                resRdsUpdate.setStatus(RdsStatus.RUNNING);
                BasicWebUtil.prepareUpdateParams(resRdsUpdate, failoverResult.getOpUser());
                resRdsService.updateByPrimaryKeySelective(resRdsUpdate);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#dbInstanceFloatingIPActionResult.dbInstanceId", resourceType = ResourceTypeEnum.RDS,
            opUser = "#dbInstanceFloatingIPActionResult.opUser", operate = ResourceOperateEnum.MODIFY,
            success = "#dbInstanceFloatingIPActionResult.success", orgSid = "#dbInstanceFloatingIPActionResult.orgSid")
    @Message(refKey = "#dbInstanceFloatingIPActionResult.dbInstanceId", envId = "#dbInstanceFloatingIPActionResult.cloudEnvId",
            msgType = ServerMsgType.RDS, opUser = "#dbInstanceFloatingIPActionResult.opUser", operate = OperateEnum.MODIFY,
            success = "#dbInstanceFloatingIPActionResult.success", errorMsg = "#dbInstanceFloatingIPActionResult.errMsg")
    public void handleMessage(
            @LogParam("dbInstanceFloatingIPActionResult") @MessageParam("dbInstanceFloatingIPActionResult") DBInstanceFloatingIPActionResult result) {
        if (result.getBindFlag()) {
            logger.info("RDS绑定弹性IP | 回调参数 ： {}", JsonUtil.toJson(result));
        } else {
            logger.info("RDS解绑弹性IP | 回调参数 ： {}", JsonUtil.toJson(result));
        }
        try {
            Long resRdsId = Long.valueOf(result.getOptions().get("resRdsId").toString());
            Long floatingIpId = Long.valueOf(result.getOptions().get("floatingIpId").toString());
            // 更新RDS的publicIp字段
            ResRds resRds = new ResRds();
            resRds.setId(resRdsId);
            resRds.setStatus(RdsStatus.RUNNING);
            BasicWebUtil.prepareUpdateParams(resRds, result.getOpUser());
            resRdsService.updateByPrimaryKeySelective(resRds);
            if (result.isSuccess()) {
                if (result.getBindFlag()) {
                    // 绑定
                    ResRds rds = resRdsService.selectByPrimaryKey(resRdsId);
                    ResFloatingIp floatingIp = new ResFloatingIp();
                    floatingIp.setId(floatingIpId);
                    floatingIp.setInstanceId(result.getDbInstanceId());
                    floatingIp.setInstanceType(ResourceType.RES_RDS);
                    floatingIp.setInstanceName(rds.getDescription());
                    floatingIp.setStatus(NetworkStatus.ACTIVE);
                    floatingIpMapper.updateByPrimaryKeySelective(floatingIp);

                    Criteria criteria = new Criteria();
                    criteria.put("resRdsId", resRdsId);
                    criteria.put("ipType", RdsConnectionIpType.Public);
                    ResRdsConnection connection = new ResRdsConnection();
                    connection.setConnectionType(RdsStatus.RUNNING);
                    BasicWebUtil.prepareUpdateParams(connection, result.getOpUser());
                    resRdsConnectionService.updateByParams(connection, criteria);
                } else {
                    // 解绑
                    ResFloatingIp floatingIp = new ResFloatingIp();
                    floatingIp.setId(floatingIpId);
                    floatingIp.setStatus(NetworkStatus.UNUSED);
                    floatingIp.setInstanceType(null);
                    floatingIp.setInstanceId(null);
                    floatingIp.setInstanceName(null);
                    this.floatingIpMapper.updateByPrimaryKey(floatingIp);

                    Criteria criteria = new Criteria();
                    criteria.put("resRdsId", resRdsId);
                    criteria.put("ipType", RdsConnectionIpType.Public);
                    resRdsConnectionService.deleteByParams(criteria);
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#dbInstanceSingleToHaResult.dbInstanceId", resourceType = ResourceTypeEnum.RDS,
            opUser = "#dbInstanceSingleToHaResult.opUser", operate = ResourceOperateEnum.MODIFY,
            success = "#dbInstanceSingleToHaResult.success", orgSid = "#dbInstanceSingleToHaResult.orgSid")
    @Message(refKey = "#dbInstanceSingleToHaResult.dbInstanceId", envId = "#dbInstanceSingleToHaResult.cloudEnvId",
            msgType = ServerMsgType.RDS, opUser = "#dbInstanceSingleToHaResult.opUser", operate = OperateEnum.MODIFY,
            success = "#dbInstanceSingleToHaResult.success", errorMsg = "#dbInstanceSingleToHaResult.errMsg")
    public void handleMessage(
            @LogParam("dbInstanceSingleToHaResult") @MessageParam("dbInstanceSingleToHaResult") DBInstanceSingleToHaResult result) {
        logger.info("RDS单机转主备 | 回调参数 ： {}", JsonUtil.toJson(result));
        try {
            Long resRdsId = Long.valueOf(result.getOptions().get("resRdsId").toString());
            if (result.isSuccess()) {
                // 更新zoneid
                Criteria criteria = new Criteria();
                criteria.put("uuid", result.getZone());
                List<ResZone> zoneList = resZoneMapper.selectByParams(criteria);
                if (CollectionUtils.isEmpty(zoneList)) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1390978107));
                }
                ResRds rds = resRdsService.selectByPrimaryKey(resRdsId);
                ResRds resRds = new ResRds();
                resRds.setId(resRdsId);
                resRds.setZoneId(String.join(",", rds.getZoneId(), zoneList.get(0).getId().toString()));
                BasicWebUtil.prepareUpdateParams(resRds, result.getOpUser());
                resRdsService.updateByPrimaryKeySelective(resRds);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#dbInstancePortUpdateResult.dbInstanceId", resourceType = ResourceTypeEnum.RDS,
            opUser = "#dbInstancePortUpdateResult.opUser", operate = ResourceOperateEnum.MODIFY,
            success = "#dbInstancePortUpdateResult.success", orgSid = "#dbInstancePortUpdateResult.orgSid")
    @Message(refKey = "#dbInstancePortUpdateResult.dbInstanceId", envId = "#dbInstancePortUpdateResult.cloudEnvId",
            msgType = ServerMsgType.RDS, opUser = "#dbInstancePortUpdateResult.opUser", operate = OperateEnum.MODIFY,
            success = "#dbInstancePortUpdateResult.success", errorMsg = "#dbInstancePortUpdateResult.errMsg")
    public void handleMessage(
            @LogParam("dbInstancePortUpdateResult") @MessageParam("dbInstancePortUpdateResult") DBInstancePortUpdateResult result) {
        logger.info("RDS修改端口 | 回调参数 ： {}", JsonUtil.toJson(result));
        try {
            Long resRdsId = Long.valueOf(result.getOptions().get("resRdsId").toString());
            if (result.isSuccess()) {
                ResRds resRds = new ResRds();
                resRds.setId(resRdsId);
                resRds.setPort(result.getPort());
                resRds.setStatus(RdsStatus.RUNNING);
                BasicWebUtil.prepareUpdateParams(resRds, result.getOpUser());
                resRdsService.updateByPrimaryKeySelective(resRds);

                Criteria criteria = new Criteria();
                criteria.put("resRdsId", resRdsId);
                ResRdsConnection connection = new ResRdsConnection();
                connection.setConnectionType(RdsStatus.RUNNING);
                connection.setPort(result.getPort());
                BasicWebUtil.prepareUpdateParams(connection, result.getOpUser());
                resRdsConnectionService.updateByParamsSelective(connection, criteria);
            } else {
                // 更新失败 状态还原
                ResRds resRdsUpdate = new ResRds();
                resRdsUpdate.setId(resRdsId);
                resRdsUpdate.setStatus(RdsStatus.RUNNING);
                BasicWebUtil.prepareUpdateParams(resRdsUpdate, result.getOpUser());
                resRdsService.updateByPrimaryKeySelective(resRdsUpdate);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#dbInstanceInnerIPUpdateResult.dbInstanceId", resourceType = ResourceTypeEnum.RDS,
            opUser = "#dbInstanceInnerIPUpdateResult.opUser", operate = ResourceOperateEnum.MODIFY,
            success = "#dbInstanceInnerIPUpdateResult.success", orgSid = "#dbInstanceInnerIPUpdateResult.orgSid")
    @Message(refKey = "#dbInstanceInnerIPUpdateResult.dbInstanceId", envId = "#dbInstanceInnerIPUpdateResult.cloudEnvId",
            msgType = ServerMsgType.RDS, opUser = "#dbInstanceInnerIPUpdateResult.opUser", operate = OperateEnum.MODIFY,
            success = "#dbInstanceInnerIPUpdateResult.success", errorMsg = "#dbInstanceInnerIPUpdateResult.errMsg")
    public void handleMessage(
            @LogParam("dbInstanceInnerIPUpdateResult") @MessageParam("dbInstanceInnerIPUpdateResult") DBInstanceInnerIPUpdateResult result) {
        logger.info("RDS修改IP | 回调参数 ： {}", JsonUtil.toJson(result));
        try {
            Long resRdsId = Long.valueOf(result.getDbInstanceId());
            if (result.isSuccess()) {
                ResRds resRds = new ResRds();
                resRds.setId(resRdsId);
                resRds.setAddress(result.getIp());
                resRds.setStatus(RdsStatus.RUNNING);
                BasicWebUtil.prepareUpdateParams(resRds, result.getOpUser());
                resRdsService.updateByPrimaryKeySelective(resRds);
            } else {
                // 更新失败 状态还原
                ResRds resRdsUpdate = new ResRds();
                resRdsUpdate.setId(resRdsId);
                resRdsUpdate.setStatus(RdsStatus.RUNNING);
                BasicWebUtil.prepareUpdateParams(resRdsUpdate, result.getOpUser());
                resRdsService.updateByPrimaryKeySelective(resRdsUpdate);
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
    }
}
