/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.service.server.impl;

import cn.com.cloudstar.rightcloud.adapter.core.MQException;
import cn.com.cloudstar.rightcloud.adapter.core.MQHelper;
import cn.com.cloudstar.rightcloud.adapter.pojo.cloudos.ScanCloudOsBareMetalNode;
import cn.com.cloudstar.rightcloud.adapter.pojo.cloudos.result.BaremetalNodeResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.cloudos.result.vo.BaremetalNodeVo;
import cn.com.cloudstar.rightcloud.adapter.pojo.keypairs.result.keyVo.KeyVo;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.*;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmQueryDataStoreResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmRenameResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmTemplateResult;
import cn.com.cloudstar.rightcloud.basic.data.platform.CloudClientFactory;
import cn.com.cloudstar.rightcloud.basic.data.pojo.app.AppDeployModel;
import cn.com.cloudstar.rightcloud.basic.data.pojo.app.AppInstSvc;
import cn.com.cloudstar.rightcloud.basic.data.pojo.base.Base;
import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnv;
import cn.com.cloudstar.rightcloud.basic.data.pojo.code.Code;
import cn.com.cloudstar.rightcloud.basic.data.pojo.common.ResActionLog;
import cn.com.cloudstar.rightcloud.basic.data.pojo.config.SysConfig;
import cn.com.cloudstar.rightcloud.basic.data.pojo.deploy.DeployPlaybook;
import cn.com.cloudstar.rightcloud.basic.data.pojo.deploy.DeployTask;
import cn.com.cloudstar.rightcloud.basic.data.pojo.deploy.HostDepoyEvent;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.*;
import cn.com.cloudstar.rightcloud.basic.data.pojo.snapshot.ResSnapshot;
import cn.com.cloudstar.rightcloud.basic.data.pojo.user.Org;
import cn.com.cloudstar.rightcloud.basic.data.pojo.user.User;
import cn.com.cloudstar.rightcloud.basic.data.service.code.BasicCodeService;
import cn.com.cloudstar.rightcloud.basic.data.service.config.BasicSysConfigService;
import cn.com.cloudstar.rightcloud.basic.data.service.deploy.BasicDeployPlaybookService;
import cn.com.cloudstar.rightcloud.basic.data.service.deploy.BasicDeployTaskService;
import cn.com.cloudstar.rightcloud.basic.data.service.res.BasicResActionLogService;
import cn.com.cloudstar.rightcloud.common.additional.PhysicalHostPowerAttrVO;
import cn.com.cloudstar.rightcloud.common.additional.ResInstResult;
import cn.com.cloudstar.rightcloud.common.constants.*;
import cn.com.cloudstar.rightcloud.common.constants.BillingConstants.ChargeType;
import cn.com.cloudstar.rightcloud.common.constants.WebConstants.InstanceNameRuleFix;
import cn.com.cloudstar.rightcloud.common.constants.WebConstants.InstanceNameSuffix;
import cn.com.cloudstar.rightcloud.common.constants.WebConstants.MonitorInfoKey;
import cn.com.cloudstar.rightcloud.common.constants.WebConstants.PhysicalHostInfo;
import cn.com.cloudstar.rightcloud.common.constants.ansible.AnsibleMqConfig;
import cn.com.cloudstar.rightcloud.common.constants.ansible.AnsibleServerMethod;
import cn.com.cloudstar.rightcloud.common.constants.ansible.AnsibleTaskTypeConfig;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.constants.res.status.*;
import cn.com.cloudstar.rightcloud.common.constants.res.type.*;
import cn.com.cloudstar.rightcloud.common.constants.status.CloudPhysicalHostPoolAllocStatus;
import cn.com.cloudstar.rightcloud.common.constants.status.DeployTaskStatus;
import cn.com.cloudstar.rightcloud.common.constants.status.SelfServiceInstanceStatus;
import cn.com.cloudstar.rightcloud.common.constants.type.*;
import cn.com.cloudstar.rightcloud.common.enums.NoticeResTypeEnum;
import cn.com.cloudstar.rightcloud.common.enums.ResVmExtEnum;
import cn.com.cloudstar.rightcloud.common.enums.resource.ResourceOperateEnum;
import cn.com.cloudstar.rightcloud.common.enums.resource.ResourceTypeEnum;
import cn.com.cloudstar.rightcloud.common.exception.BizException;
import cn.com.cloudstar.rightcloud.common.exception.RetryException;
import cn.com.cloudstar.rightcloud.common.expire.ResExpireNoticeUtil;
import cn.com.cloudstar.rightcloud.common.expire.ResourceExpire;
import cn.com.cloudstar.rightcloud.common.mongo.MongoUtil;
import cn.com.cloudstar.rightcloud.common.pojo.LicenseVo;
import cn.com.cloudstar.rightcloud.common.pojo.ResImageInfo;
import cn.com.cloudstar.rightcloud.common.redis.JedisUtil;
import cn.com.cloudstar.rightcloud.common.util.*;
import cn.com.cloudstar.rightcloud.common.util.json.JsonUtil;
import cn.com.cloudstar.rightcloud.common.util.ssh.JschUtils;
import cn.com.cloudstar.rightcloud.common.websocket.ServerMsgPublisher;
import cn.com.cloudstar.rightcloud.core.pojo.dto.gpu.ResGpuGroup;
import cn.com.cloudstar.rightcloud.core.pojo.dto.gpu.ResGpuGroupRelation;
import cn.com.cloudstar.rightcloud.core.pojo.models.host.*;
import cn.com.cloudstar.rightcloud.core.pojo.models.host.HostParamCheckDTO.TypeEnum;
import cn.com.cloudstar.rightcloud.core.pojo.models.host.ResVmReInstallSystemDTO.AuthModel;
import cn.com.cloudstar.rightcloud.core.pojo.models.host.ResVmReInstallSystemDTO.InstanceModel;
import cn.com.cloudstar.rightcloud.core.pojo.models.host.ResVmReInstallSystemDTO.InterfaceItemModel;
import cn.com.cloudstar.rightcloud.core.pojo.models.res.ResHostProvisionModel;
import cn.com.cloudstar.rightcloud.core.pojo.models.res.ResStorageProvisionModel;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicInfoUtil;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicWebUtil;
import cn.com.cloudstar.rightcloud.common.pojo.LicenseVo;
import cn.com.cloudstar.rightcloud.common.util.LicenseUtil;
import cn.com.cloudstar.rightcloud.remote.api.monitor.service.AlarmDataRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.monitor.service.AlarmRuleTargetRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.monitor.service.BasicMonitorRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.cloud.CloudEnvAlloc;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.cloud.CloudPhysicalHostPool;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.engine.CallbakLog;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.ResVmNodeInfo;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.instance.CloudCommonInst;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.keypair.CloudKeyPair;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.project.ResProject;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.*;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.platform.Storage;
import cn.com.cloudstar.rightcloud.remote.api.pojo.system.account.MgtObjExt;
import cn.com.cloudstar.rightcloud.remote.api.pojo.system.account.Project;
import cn.com.cloudstar.rightcloud.remote.api.pojo.system.app.ServiceInstTarget;
import cn.com.cloudstar.rightcloud.remote.api.pojo.system.app.request.DeployAppsInstanceEventParams;
import cn.com.cloudstar.rightcloud.remote.api.pojo.system.app.request.DeployAppsInstanceParams;
import cn.com.cloudstar.rightcloud.remote.api.pojo.system.config.SysMgtObjQuota;
import cn.com.cloudstar.rightcloud.remote.api.pojo.system.deployment.CloudDeploymentScript;
import cn.com.cloudstar.rightcloud.remote.api.pojo.system.order.ServiceOrder;
import cn.com.cloudstar.rightcloud.remote.api.pojo.system.quota.request.SysMgtObjQuotaParams;
import cn.com.cloudstar.rightcloud.remote.api.pojo.system.saas.SysSaasOrg;
import cn.com.cloudstar.rightcloud.remote.api.pojo.system.saas.request.SysSaasOrgParams;
import cn.com.cloudstar.rightcloud.remote.api.pojo.system.system.LogInfo;
import cn.com.cloudstar.rightcloud.remote.api.pojo.system.system.SysQuota;
import cn.com.cloudstar.rightcloud.remote.api.pojo.system.tag.CloudTag;
import cn.com.cloudstar.rightcloud.remote.api.pojo.system.tag.request.UnBindCloudTagRequest;
import cn.com.cloudstar.rightcloud.remote.api.system.service.account.ProjectRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.system.service.app.DeployAppsInstanceEventRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.system.service.app.DeployAppsInstanceRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.system.service.config.SysMgtObjQuotaRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.system.service.maintainance.order.ServiceInstTargetRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.system.service.message.BusinessNotificationRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.system.service.operation.account.CompanyRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.system.service.operation.saas.SysSaasOrgRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.system.service.tag.CloudTagRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.system.service.user.OrgRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.system.service.user.UserRemoteService;
import cn.com.cloudstar.rightcloud.resource.dao.common.ObjectExistCheckMapper;
import cn.com.cloudstar.rightcloud.resource.dao.env.CloudEnvAllocMapper;
import cn.com.cloudstar.rightcloud.resource.dao.env.CloudEnvMapper;
import cn.com.cloudstar.rightcloud.resource.dao.gpu.ResGpuGroupMapper;
import cn.com.cloudstar.rightcloud.resource.dao.gpu.ResGpuGroupRelationMapper;
import cn.com.cloudstar.rightcloud.resource.dao.host.ResHostMapper;
import cn.com.cloudstar.rightcloud.resource.dao.image.ResImageMapper;
import cn.com.cloudstar.rightcloud.resource.dao.network.*;
import cn.com.cloudstar.rightcloud.resource.dao.security.CloudKeyPairMapper;
import cn.com.cloudstar.rightcloud.resource.dao.security.ResSecurityGroupMapper;
import cn.com.cloudstar.rightcloud.resource.dao.server.*;
import cn.com.cloudstar.rightcloud.resource.dao.snapshot.ResSnapshotMapper;
import cn.com.cloudstar.rightcloud.resource.dao.storage.ResStorageMapper;
import cn.com.cloudstar.rightcloud.resource.dao.storage.ResVdHostMapper;
import cn.com.cloudstar.rightcloud.resource.dao.storage.ResVdMapper;
import cn.com.cloudstar.rightcloud.resource.dao.storage.ResVolumeTypeMapper;
import cn.com.cloudstar.rightcloud.resource.dao.zone.ResPoolMapper;
import cn.com.cloudstar.rightcloud.resource.dao.zone.ResZoneMapper;
import cn.com.cloudstar.rightcloud.resource.engine.event.callback.handler.log.HostDeployCallbackLogMessageHandler;
import cn.com.cloudstar.rightcloud.resource.service.common.ResService;
import cn.com.cloudstar.rightcloud.resource.service.common.ResourceService;
import cn.com.cloudstar.rightcloud.resource.service.env.CloudEnvService;
import cn.com.cloudstar.rightcloud.resource.service.gpu.ResGpuGroupService;
import cn.com.cloudstar.rightcloud.resource.service.image.ResImageService;
import cn.com.cloudstar.rightcloud.resource.service.redundance.core.DeployAppsInstanceService;
import cn.com.cloudstar.rightcloud.resource.service.redundance.core.ServiceInstTargetService;
import cn.com.cloudstar.rightcloud.resource.service.redundance.core.ServiceOrderService;
import cn.com.cloudstar.rightcloud.resource.service.resattr.ResAttrExtService;
import cn.com.cloudstar.rightcloud.resource.service.security.ResSecurityGroupService;
import cn.com.cloudstar.rightcloud.resource.service.server.*;
import cn.com.cloudstar.rightcloud.resource.service.snapshot.ResSnapshotService;
import cn.com.cloudstar.rightcloud.resource.service.thirdpartyservice.FortressService;
import cn.com.cloudstar.rightcloud.resource.service.user.ResProjectService;
import cn.com.cloudstar.rightcloud.resource.service.zone.ResPoolService;
import cn.com.cloudstar.rightcloud.resource.service.zone.ResZoneService;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.base.Throwables;
import com.google.common.collect.*;
import lombok.NonNull;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ClassUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2016/8/12
 */
@Component
@Lazy
@SuppressWarnings("checkstyle:filelength")
public class ResVmServiceImpl implements ResVmService {

    private static final String RES_VM_ID = "resVmId";

    private final Logger logger = LoggerFactory.getLogger(ClassUtils.getUserClass(this));

    @Autowired
    private CloudKeyPairMapper cloudKeyPairMapper;

    @Autowired
    private ResVmMapper resVmMapper;

    @Autowired
    private ResBmsMapper resBmsMapper;

    @Autowired
    private CloudEnvService cloudEnvService;

    @Autowired
    private ResVdMapper resVdMapper;

    @Autowired
    private BasicDeployTaskService basicDeployTaskService;

    @Autowired
    private BasicDeployPlaybookService basicDeployPlaybookService;

    @Autowired
    private ResVmContainerMapper resVmContainerMapper;

    @Autowired
    private ResImageMapper resImageMapper;

    @Autowired
    private ResImageService resImageService;

    @Autowired
    private ResFloatingIpMapper resFloatingIpMapper;

    @Autowired
    private ResPoolMapper resPoolMapper;

    @Autowired
    private ResVmScriptMapper resVmScriptMapper;

    @Autowired
    private ResVmScriptParamMapper resVmScriptParamMapper;

    @Autowired
    private NetworkMapper networkMapper;

    @Autowired
    private ResVpcMapper resNetVpcMapper;

    @Autowired
    private NetworkIpMapper networkIpMapper;

    @Autowired
    private ResVsPortGroupMapper resVsPortGroupMapper;

    @Autowired
    private ResVmNetcardMapper resVmNetcardMapper;

    @Autowired
    private CloudEnvAllocMapper cloudEnvAllocMapper;

    @Autowired
    private ResService resService;

    @Autowired
    private CloudEnvMapper cloudEnvMapper;

    @Autowired
    private BasicResActionLogService basicResActionLogService;

    @Autowired
    @Lazy
    private ResSecurityGroupService resSecurityGroupService;

    @Autowired
    private ResVsPortGroupNetworkMapper resVsPortGroupNetworkMapper;

    @Autowired
    private ResSnapshotService resSnapshotService;

    @Autowired
    private ResVolumeTypeMapper resVolumeTypeMapper;

    @Autowired
    private ResStorageMapper resStorageMapper;

    @Autowired
    private ResZoneService resZoneService;

    @Autowired
    @Lazy
    private ObjectExistCheckMapper objectExistCheckMapper;

    @Autowired
    private ResPoolService resPoolService;

    @Autowired
    private ResHostMapper resHostMapper;

    @Autowired
    private ResZoneMapper resZoneMapper;

    @Autowired
    private ResVmExtService resVmExtService;

    @Autowired
    private ResVdHostMapper resVdHostMapper;

    @Autowired
    private ResVmTypeService resVmTypeService;

    @Autowired
    private VmTypeChangeService vmTypeChangeService;

    @Autowired
    private ResServerGroupService resServerGroupService;

    @Autowired
    private ResVpcPortMapper resVpcPortMapper;

    @Autowired
    private ResSecurityGroupMapper resSecurityGroupMapper;

    @Autowired
    private ResourceService resourceService;

    @Autowired
    private ResSnapshotMapper resSnapshotMapper;

    @Autowired
    private CloudPhysicalHostPoolMapper cloudPhysicalHostPoolMapper;

    @Autowired
    private ResAttrExtService resAttrExtService;

    @Autowired
    private ResProjectService resProjectService;

    @Autowired
    private ResGpuGroupMapper resGpuGroupMapper;

    @Autowired
    private ResGpuGroupService resGpuGroupService;

    @Autowired
    private ResGpuGroupRelationMapper resGpuGroupRelationMapper;


    @Autowired
    private BasicCodeService basicCodeService;

    @Autowired
    private BasicSysConfigService basicSysConfigService;

    @Autowired
    @Lazy
    private HostDeployCallbackLogMessageHandler hostDeployCallbackLogMessageHandler;

    @DubboReference
    private OrgRemoteService orgRemoteService;

    @DubboReference
    private UserRemoteService userRemoteService;

    @DubboReference
    private BasicMonitorRemoteService basicMonitorRemoteService;

    @DubboReference
    private AlarmRuleTargetRemoteService alarmRuleTargetRemoteService;

    @DubboReference
    private AlarmDataRemoteService alarmDataRemoteService;

    @Autowired
    private ServiceOrderService serviceOrderService;

    @DubboReference
    private DeployAppsInstanceRemoteService deployAppsInstanceRemoteService;

    @Autowired
    private DeployAppsInstanceService deployAppsInstanceService;

    @DubboReference
    private DeployAppsInstanceEventRemoteService deployAppsInstanceEventRemoteService;

    @DubboReference
    private SysSaasOrgRemoteService sysSaasOrgRemoteService;

    @DubboReference
    private SysMgtObjQuotaRemoteService sysMgtObjQuotaRemoteService;

    @DubboReference
    private CompanyRemoteService companyRemoteService;

    @DubboReference
    private ServiceInstTargetRemoteService serviceInstTargetRemoteService;

    @Autowired
    private ServiceInstTargetService serviceInstTargetService;

    @DubboReference
    private CloudTagRemoteService cloudTagRemoteService;

    @DubboReference
    private ProjectRemoteService projectRemoteService;

    @DubboReference
    private BusinessNotificationRemoteService businessNotificationRemoteService;

    @Autowired
    private FortressService fortressService;

    @Override
    public ResVm selectByPrimaryKey(String id) {
        ResVm resVm = this.resVmMapper.selectByPrimaryKey(id);

        setResVmExtInfo(resVm);
        return resVm;
    }

    @Override
    public ResVm selectSimpleByPrimaryKey(String id) {
        return this.resVmMapper.selectSimpleByPrimaryKey(id);
    }

    /**
     * 创建主机到部署
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> createServerToDeployment(Long deploymentId, Long serverTemplateId, JsonNode jsonNode,
                                                 Integer num, List<Long> strategyIds, Long orgSid, boolean launch,
                                                 Map<String, Object> suffixMap) {
        List<String> hostIds = Lists.newArrayList();

        //获取后缀
        List<String> suffixList = (List<String>) suffixMap.get("suffixList");
        String splitStr = (String) suffixMap.get("splitStr");
        // 多台主机创建时加上尾部序列
        for (int i = 1; i <= num; i++) {
            JsonNode param = jsonNode.deepCopy();
            ObjectNode node = (ObjectNode) param.findValue("instance");
            if (CollectionUtils.isEmpty(suffixList)) {
                if (i > 1) {
                    node.put("instanceName", String.format("%s-%d", node.get("instanceName").textValue(), i - 1));
                    node.put("index", i);
                }
            } else {
                String format = "%s%s";
                if (!StringUtil.isNullOrEmpty(splitStr)) {
                    format = "%s" + splitStr + "%s";
                }
                node.put("instanceName",
                         String.format(format, node.get("instanceName").textValue(), suffixList.get(i - 1)));
                if (i > 1) {
                    node.put("index", i);
                }
            }

            String hostId = createServerToDeployment(deploymentId, serverTemplateId, param, strategyIds, orgSid);
            hostIds.add(hostId);
        }

        if (!CollectionUtils.isEmpty(hostIds) && launch) {
            this.reCreateResVm(hostIds);
        }
        return hostIds;
    }

    private String createServerToDeployment(Long deploymentId, Long serverTemplateId, JsonNode jsonNode,
                                            List<Long> strategyIds, Long orgSid) {
        ResVm resVm = createInstance(jsonNode);
        resVm.setStatus(ResVmStatus.PENDING);
        resVm.setOwnerId(BasicInfoUtil.getAuthUser().getUserSid().toString());

        resVm.setOriginParam(JsonUtil.toJson(jsonNode));
        resVm.setManageStatus(ResVmManageStatus.UNUNITED);
        resVm.setServerType(ServerType.SERVER);
        resVm.setCloudDeploymentId(deploymentId);
        resVm.setServerTemplateId(serverTemplateId);
        resVm.setOrgSid(changeOrgSidWhenSharedAsExclusive(orgSid, resVm));
        resVm.setId(UuidUtil.getUuid().replace("-", ""));
        BasicWebUtil.prepareInsertParams(resVm);
        int result = this.resVmMapper.insertSelective(resVm);
        if (resVm.getCloudDeploymentId() != null) {
            this.resVmMapper.insertSerRelateHost(resVm.getId(), resVm.getCloudDeploymentId());
        }
        resVmExtService.insertResVmExt(resVm);

        // 预留IP
        reservedFixedIp(resVm);

        // 预留弹性IP
        reservedFloatingIp(resVm);

        // 给主机打标签
        reservedResVmTag(resVm, jsonNode);

        if (1 == result) {
            // 将模板上关联的告警策略 ，关联到主机上
            setTemplateAlarmRulesToHost(resVm, serverTemplateId);
        }
        return resVm.getId();
    }

    /**
     * 将模板上的告警规则关联到主机上去
     *
     * @param resVm
     * @param serverTemplateId
     */
    private void setTemplateAlarmRulesToHost(ResVm resVm, Long serverTemplateId) {
        this.basicMonitorRemoteService.copyTemplateAlarmRuleToNewHost(serverTemplateId.toString(), resVm.getId(),
                                                                      resVm.getCloudEnvId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResInstResult createServerToPaas(CloudCommonInst cloudCommonInst) {
        final JsonNode jsonNode = cloudCommonInst.getCloudSpec();

        JsonNode applyPhysicalHost = jsonNode.get("applyPhysicalHost");
        if (Objects.nonNull(applyPhysicalHost)) {
            String resVmId = applyPhysicalHost.get("resVmId").asText();
            JsonNode reinstallSystemInfo = applyPhysicalHost.get("reinstallSystemInfo");

            // 申请裸金属开通
            ResVm resVm = this.resVmMapper.selectSimpleByPrimaryKey(resVmId);
            AssertUtil.requireNonBlank(resVm, "申请的物理机不存在或已经被删除.");
            CloudPhysicalHostPool cloudPhysicalHostPool = this.cloudPhysicalHostPoolMapper.selectByPrimaryKey(
                    resVm.getPhysicalHostPoolId());
            AssertUtil.requireNonBlank(cloudPhysicalHostPool, "申请的物理机不存在或已经被删除.");
            if (CloudPhysicalHostPoolAllocStatus.USED.equalsIgnoreCase(cloudPhysicalHostPool.getAllocStatus())) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_999722424));
            }

            CloudPhysicalHostPool cloudPhysicalHostPoolUpdate = new CloudPhysicalHostPool();
            cloudPhysicalHostPoolUpdate.setId(resVm.getPhysicalHostPoolId());
            cloudPhysicalHostPoolUpdate.setAllocStatus(CloudPhysicalHostPoolAllocStatus.USED);
            this.cloudPhysicalHostPoolMapper.updateByPrimaryKeySelective(cloudPhysicalHostPoolUpdate);
            ServiceOrder serviceOrder = serviceOrderService.selectByPrimaryKey(cloudCommonInst.getOrderId());

            ResVm resVmUpdate = ResVm.builder()
                                     .id(resVmId)
                                     .ownerId(String.valueOf(serviceOrder.getOwnerId()))
                                     .createdBy(serviceOrder.getCreatedBy())
                                     .orgSid(serviceOrder.getOrgSid())
                                     .build();
            this.resVmMapper.updateByPrimaryKeySelective(resVmUpdate);

            // 重装系统
            if (Objects.nonNull(reinstallSystemInfo)) {
                ResVmReInstallSystemDTO resVmReInstallSystemDTO = JsonUtil.fromJson(reinstallSystemInfo.toString(),
                                                                                    ResVmReInstallSystemDTO.class);
                resVmReInstallSystemDTO.setId(resVmId);
                this.resVmReInstallSystem(resVmReInstallSystemDTO);
            }

            ResInstResult resInstResult = new ResInstResult(ResInstResult.SUCCESS);
            resInstResult.setData(Collections.singletonList(MapsKit.of("vmSid", resVmId)));
            return resInstResult;
        }

        Long serverTemplateId = jsonNode.get("serverTemplateId").asLong();
        Long serviceDeployInstId = cloudCommonInst.getSfServiceDeployInstId();
        ((ObjectNode) jsonNode).put("orgSidForHostName", cloudCommonInst.getOrgSid());

        // 主机信息
        ResVm resVm = createInstance(jsonNode);
        resVm.setStatus(ResVmStatus.PENDING);
        resVm.setOwnerId(cloudCommonInst.getUserId());
        resVm.setOriginParam(JsonUtil.toJson(jsonNode));
        resVm.setManageStatus(ResVmManageStatus.UNUNITED);
        resVm.setServerType(ServerType.SERVER);
        resVm.setServerTemplateId(serverTemplateId);
        resVm.setOrgSid(changeOrgSidWhenSharedAsExclusive(cloudCommonInst.getOrgSid(), resVm));
        BasicWebUtil.prepareInsertParams(resVm, cloudCommonInst.getUserAccount());
        resVm.setId(UuidUtil.getUuid().replace("-", ""));

        this.resVmMapper.insertSelective(resVm);
        resVmExtService.insertResVmExt(resVm);

        if (null != cloudCommonInst.getSfServiceDeployInstId()) {
            ServiceInstTarget serviceInstTarget = new ServiceInstTarget();
            serviceInstTarget.setTargetId(resVm.getId());
            serviceInstTarget.setSfServiceInstId(serviceDeployInstId);
            serviceInstTarget.setTargetType(DeploymentMode.HOST);
            serviceInstTarget.setHasCreate(true);
            serviceInstTargetRemoteService.insert(serviceInstTarget);
        }

        // 预留IP
        reservedFixedIp(resVm);

        // 给主机打标签
        reservedResVmTag(resVm, jsonNode);

        // 设置主机模版-告警策略
        this.setTemplateAlarmRulesToHost(resVm, serverTemplateId);
        Criteria criteria = new Criteria();
        criteria.put("serverTemplateId", serverTemplateId);

        String hostId = resVm.getId();
        if (StringUtil.isNotBlank(hostId)) {
            return this.reCreateResVm(hostId);
        } else {
            return new ResInstResult(ResInstResult.FAILURE, "创建主机失败");
        }
    }

    @SuppressWarnings({"checkstyle:MethodLength", "checkstyle:MagicNumber"})
    private ResVm createInstance(JsonNode jsonNode) {
        ResVm resVm = new ResVm();
        resVm.setCloudEnvId(jsonNode.findValue("envId").asLong());

        CloudEnv cloudEnv = cloudEnvMapper.selectByPrimaryKey(resVm.getCloudEnvId());
        resVm.setRegion(cloudEnv.getRegion());
        List<ResVmExt> resVmExts = Lists.newArrayList();

        if (jsonNode.has("zone")) {
            if (jsonNode.get("zone").canConvertToLong()) {
                ((ObjectNode) jsonNode).put("resPoolId", jsonNode.get("zone").asLong());
            } else {
                // bug23679, 如果硬盘zone信息为空，findValue会返回null
                jsonNode.findValues("zone")
                        .stream()
                        .filter(Objects::nonNull)
                        .map(JsonNode::textValue)
                        .filter(Objects::nonNull)
                        .findFirst()
                        .ifPresent(resVm::setZone);
            }
        }
        if (!Objects.isNull(jsonNode.findValue("resPoolId"))) {
            ResPool resPool = resPoolMapper.selectByPrimaryKey(jsonNode.findValue("resPoolId").asLong());
            if (!Objects.isNull(resPool)) {
                resVm.setZone(resPool.getName());
                resVm.setResPoolId(resPool.getId());
            }
        }

        //克隆方法使用
        if (jsonNode.get("cloneSourceId") != null && StringUtil.isNotEmpty(jsonNode.get("cloneSourceId").textValue())) {
            resVm.setCloneSourceId(jsonNode.get("cloneSourceId").textValue());
            ResVm originHost = resVmMapper.selectByPrimaryKey(resVm.getCloneSourceId());
            Criteria criteria = new Criteria();
            criteria.put("cloudEnvId", originHost.getCloudEnvId());
            criteria.put("name", originHost.getZone());
            List<ResPool> resPools = resPoolMapper.selectByParams(criteria);
            if (CollectionUtil.isEmpty(resPools)) {
                throw new BizException(RestConst.BizError.BIZ_ERROR, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1915067363) + originHost.getZone() + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_845035308));
            }
            resVm.setZone(resPools.get(0).getName());
        }
        // instance
        final JsonNode instance = jsonNode.get("instance");

        String cloudEnvType = cloudEnv.getCloudEnvType();
        if (CloudEnvType.FUSIONCOMPUTE.equals(cloudEnvType) && jsonNode.findValue(cloudEnvType) != null
                && !jsonNode.findValue(cloudEnvType).isNull()) {
            JsonNode fusionComputeOtherInfo = jsonNode.findValue(cloudEnvType);

            if (!Objects.isNull(fusionComputeOtherInfo.findValue("allocateResVcUrn"))) {
                resVm.setAllocateResVcUrn(fusionComputeOtherInfo.findValue("allocateResVcUrn").textValue());
            }
            if (!Objects.isNull(fusionComputeOtherInfo.findValue("allocateResHostUrn"))) {
                resVm.setAllocateResHostUrn(fusionComputeOtherInfo.findValue("allocateResHostUrn").textValue());
            }
        }

        resVm.setInstanceChargeType(
                instance.get("instanceChargeType") != null ? instance.get("instanceChargeType").textValue() : null);
        resVm.setInstanceName(instance.get("instanceName").textValue());
        // openstack 可以根据实例快照创建实例
        long imageId = instance.get("imageId").asLong();
        String imageType = instance.get("imageType") != null ? instance.get("imageType").textValue() : "image";
        resVm.setImageType(imageType);
        if (CloudEnvType.OPEN_STACK.equals(cloudEnv.getCloudEnvType()) && Objects.equals(imageType, "snapshot")) {
            ResSnapshot resSnapshot = resSnapshotService.selectByPrimaryKey(imageId);
            if (Objects.isNull(resSnapshot)) {
                throw new BizException(StrUtil.format(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1758471837), instance.get(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_23192064))));
            }
            ResVm snapVm = this.selectBaseByPrimaryKey(resSnapshot.getResVmId());
            if (null != snapVm) {
                resVm.setPlatform(snapVm.getPlatform());
                resVm.setOsCategory(snapVm.getOsCategory());
            }
            resVm.setImageId(resSnapshot.getUuid());
        } else if (CloudEnvType.OPEN_STACK.equals(cloudEnv.getCloudEnvType()) && Objects.equals(imageType, "disk")) {
            String resVdSid = instance.get("resVdSid").asText();
            ResVd resVd = resVdMapper.selectByPrimaryKey(resVdSid);
            if (Objects.isNull(resVd)) {
                throw new BizException(StrUtil.format(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1176312154), instance.get(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_23192064))));
            }
            resVm.setImageId(resVd.getUuid());
        } else {
            ResImage resImage = resImageMapper.selectByPrimaryKey(imageId);
            AssertUtil.requireNonBlank(resImage, StrUtil.format("实例[{}]选择的镜像不存在", instance.get("instanceName")));
            resVm.setImageId(resImage.getImageId());
            resVm.setPlatform(resImage.getOsPlatform());
            resVm.setOsCategory(resImage.getOsType());
            ResImageInfo resImageInfo = ResImageInfo.builder()
                                                    .imageName(resImage.getImageName())
                                                    .osPlatform(resImage.getOsPlatform())
                                                    .osVersion(resImage.getOsVersion())
                                                    .bit(Objects.nonNull(resImage.getBit()) ? String.valueOf(
                                                            resImage.getBit()) : null)
                                                    .build();
            resVm.setOsName(ResVmHelper.getOsName(cloudEnvType, resImageInfo));
        }

        //设置hostname
        if (instance.has("hostName") && !Strings.isNullOrEmpty(instance.get("hostName").textValue())) {
            String hostName = instance.get("hostName").textValue();
            if (instance.has("index")) {
                hostName = String.format("%s-%03d", hostName, instance.get("index").asInt());
            }
            ((ObjectNode) instance).put("hostName", hostName);

            resVm.setHostName(hostName);
        } else {
            Long orgSidForHostName = Convert.toLong(jsonNode.get("orgSidForHostName"));
            String defaultHostName = getHostNameByFixInfo(resVm.getInstanceName(), orgSidForHostName,
                                                          resVm.getOsCategory());
            resVm.setHostName(defaultHostName);
        }

        ResVmType instanceType = resVmTypeService.selectByPrimaryKey(instance.get("instanceType").asLong());
        if (Objects.isNull(instanceType)) {
            Criteria criteria = new Criteria();
            criteria.put("cloudEnvId", cloudEnv.getId());
            criteria.put("uuid", instance.get("instanceType").textValue());
            List<ResVmType> resVmTypes = resVmTypeService.selectByParams(criteria);
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(resVmTypes)) {
                instanceType = resVmTypes.get(0);
                resVm.setInstanceType(instanceType.getUuid());
                resVm.setCpu(instanceType.getCpu());
                resVm.setMemory(instanceType.getRam().intValue());
            }
        } else {
            resVm.setInstanceType(instanceType.getUuid());
            resVm.setCpu(instanceType.getCpu());
            resVm.setMemory(instanceType.getRam().intValue());
        }

        if (Objects.nonNull(instance.get("serverGroupId"))) {
            ResVmExt resVmExt = new ResVmExt();
            resVmExt.setResourceId(instance.get("serverGroupId").asText());
            resVmExt.setType(ResVmExtEnum.SERVER_GROUP.getType());
            resVmExts.add(resVmExt);
        }

        JsonNode auth = jsonNode.findValue("auth");
        if (auth != null && !auth.isNull()) {
            if (auth.get("keyPair") != null && !"null".equals(auth.get("keyPair").asText())) {
                resVm.setKeypairId(auth.get("keyPair").asLong());
            }

            if (auth.get("remoteLoginType") != null) {
                resVm.setRemoteLoginType(auth.get("remoteLoginType").textValue());
            } else {
                resVm.setRemoteLoginType(RemoteLoginType.BY_PASS);
            }

            if (auth.get("user") == null || Strings.isNullOrEmpty(auth.get("user").textValue())) {
                resVm.setManagementAccount("root");
            } else {
                resVm.setManagementAccount(auth.get("user").textValue());
            }

            // 只有根据密码创建才生成密码
            if (RemoteLoginType.BY_PASS.equals(resVm.getRemoteLoginType())) {
                if (auth.get("password") == null || Strings.isNullOrEmpty(auth.get("password").textValue())) {
                    if (CloudEnvType.AZURE.equals(cloudEnvType)) {
                        resVm.setManagemenPassword(RandomStringUtils.randomAlphabetic(4).toUpperCase()
                                                           + RandomStringUtils.randomAlphabetic(4).toLowerCase()
                                                           + RandomStringUtils.randomNumeric(2) + "@#$");
                    } else {
                        if ("windows".equalsIgnoreCase(resVm.getPlatform())) {
                            resVm.setManagemenPassword(RandomStringUtils.randomAlphabetic(4).toUpperCase()
                                                               + RandomStringUtils.randomAlphabetic(4).toLowerCase()
                                                               + RandomStringUtils.randomNumeric(2) + "@#$");
                        } else {
                            resVm.setManagemenPassword(RandomStringUtils.randomAlphabetic(3).toUpperCase()
                                                               + RandomStringUtils.randomAlphabetic(3).toLowerCase()
                                                               + RandomStringUtils.randomNumeric(2));
                        }
                    }
                } else {
                    resVm.setManagemenPassword(CrytoUtilSimple.decrypt(auth.get("password").textValue(), true));
                }
            }
        }

        if (jsonNode.get("networks") != null) {
            JsonNode networks = jsonNode.get("networks");

            if (CloudEnvType.VMWARE.equals(cloudEnvType) || CloudEnvType.FUSIONCOMPUTE.equals(cloudEnvType)) {
                //已添加的IP列表
                Set<String> ips = new HashSet<>();

                HashMultimap<Long, String> networkGroupBySubnetId = HashMultimap.create();
                List<NetworkIp> networkIps = new ArrayList<>();
                for (Iterator<JsonNode> elements = networks.elements(); elements.hasNext(); ) {
                    JsonNode next = elements.next();
                    Long networkId = next.get("networkId").asLong();
                    String fixedIp = null;
                    // 将固定ip预先占用，并更新到host的ip字段上面
                    if (!next.get("autoIp").asBoolean()) {
                        NetworkIp networkIp = new NetworkIp();
                        networkIp.setNetworkId(networkId);
                        networkIp.setId(next.get("fixedIpId").asLong());
                        networkIps.add(networkIp);
                        fixedIp = next.get("fixedIp").textValue();
                        ips.add(fixedIp);
                    }
                    networkGroupBySubnetId.put(networkId, fixedIp);
                }
                resVm.setNetworkIps(networkIps);
                // 所有选择网络，更新到网络上
                resVm.setInnerIp(Joiner.on(",").join(ips));

                for (Long key : networkGroupBySubnetId.keySet()) {
                    ResVmExt resVmExt = new ResVmExt();
                    resVmExt.setInstanceId(resVm.getId());
                    resVmExt.setResourceId(key.toString());
                    resVmExt.setType(ResVmExtEnum.SUBNET.getType());
                    resVmExt.setExtra(Joiner.on(",").skipNulls().join(networkGroupBySubnetId.get(key)));
                    resVmExts.add(resVmExt);
                }
            } else {
                boolean hasNext = networks.elements().hasNext();
                if (hasNext) {
                    JsonNode network = networks.elements().next();
                    // network
                    resVm.setInternetChargeType(network.get("internetChargeType") == null ? null : network.get(
                            "internetChargeType").textValue());
                    if (network.get("bandwidth") != null) {
                        resVm.setInternetMaxBandwidthOut(network.get("bandwidth").asInt());
                    }

                    if (Objects.nonNull(network.get("networkType"))) {
                        resVm.setInstanceNetworkType(network.get("networkType").textValue());
                    }

                    if (network.has("subnetId")) {
                        ResVmExt resVmExt = new ResVmExt();
                        resVmExt.setResourceId(network.get("subnetId").asText());
                        resVmExt.setType(ResVmExtEnum.SUBNET.getType());
                        resVmExts.add(resVmExt);
                    }

                    if (Objects.nonNull(network.get("floatingIpPoolId")) && !network.get("floatingIpPoolId").isNull()) {
                        // 如果floatingIpPoolId值不是字符串类型 .textValue() 无法取值
                        String poolId =
                                StringUtil.isNullOrEmpty(network.get("floatingIpPoolId").asText()) ? network.get(
                                        "floatingIpPoolId").toString() : network.get("floatingIpPoolId").asText();
                        resVm.setFloatingIpPoolId(poolId);
                        resVm.setFloatingIpPoolName(network.get("floatingIpPoolName").textValue());
                    } else {
                        resVm.setFloatingIpPoolId(StrUtil.EMPTY);
                        resVm.setFloatingIpPoolName(StrUtil.EMPTY);
                    }
                    if (Objects.nonNull(network.get("floatingIpAddr"))) {
                        resVm.setPublicIp(network.get("floatingIpAddr").textValue());
                    } else {
                        resVm.setPublicIp(StrUtil.EMPTY);
                    }
                    if (Objects.nonNull(network.get("portGroupUrn"))) {
                        resVm.setPortGroupUrn(network.get("portGroupUrn").textValue());
                    }
                }
            }
        } else {
            resVm.setInstanceNetworkType(ResVmNetworkType.CLASSIC);
        }

        // GPU资源组
        JsonNode gpuGroup = jsonNode.get("gpuGroup");
        if (gpuGroup != null && gpuGroup.get("urn") != null && gpuGroup.get("count") != null) {
            final String urn = gpuGroup.get("urn").asText();
            final List<ResGpuGroup> resGpuGroups = resGpuGroupService.selectByParams(
                    new Criteria("urn", urn).put("cloudEnvId", resVm.getCloudEnvId()));
            if (CollectionUtil.isEmpty(resGpuGroups)) {
                throw new BizException(RestConst.BizError.INSUFFICIENT_RESOURCES, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1730809427));
            }

            final List<ResGpuGroupRelation> gpuGroupRelations = resGpuGroupRelationMapper.selectByParams(
                    new Criteria("gpuGroupId", resGpuGroups.get(0).getId()));
            if (CollectionUtil.isEmpty(gpuGroupRelations)) {
                throw new BizException(RestConst.BizError.INSUFFICIENT_RESOURCES, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_422633540));
            }
        }

        // 获取主机的操作系统类别和管理端口
        Map<String, Object> imageHostInfoMap = getHostInfoMapByImage(resVm, cloudEnvType);
        resVm.setSshPort((Integer) imageHostInfoMap.get("sshPort"));
        resVm.setOsCategory((String) imageHostInfoMap.get("osCategory"));
        resVm.setBit(String.valueOf(imageHostInfoMap.get("bit")));

        resVm.setStatus(ResVmStatus.CREATING);
        resVm.setManageStatus(ResVmManageStatus.UNUNITED);
        if (jsonNode.findValue("securityGroupId") != null && !jsonNode.findValue("securityGroupId").isNull()) {
            ResSecurityGroup resSecurityGroup = resSecurityGroupService.selectByPrimaryKey(
                    jsonNode.findValue("securityGroupId").asLong());

            resSecurityGroupService.assertSecurityGroupNonNull(resSecurityGroup);
            ResVmExt resVmExt = new ResVmExt();
            resVmExt.setResourceId(resSecurityGroup.getId().toString());
            resVmExt.setType(ResVmExtEnum.SECURITY_GROUP.getType());
            resVmExts.add(resVmExt);
        }
        resVm.setResVmExts(resVmExts);
        resVm.setOrgSid(BasicInfoUtil.getCurrentOrgSid());
        resVm.setCreatedOrgSid(BasicInfoUtil.getCurrentOrgSid());
        resVm.setStartTime(Calendar.getInstance().getTime());

        //私有云instanceChargeType默认传PostPaid逻辑
        Criteria criteria = new Criteria();
        criteria.put("codeValue", cloudEnv.getCloudEnvType());
        criteria.put("codeCategory", CodeCategoryConstants.CLOUD_ENV_TYPE);
        List<Code> list = basicCodeService.selectByParams(criteria);
        if (list != null && list.size() > 0) {
            if (Objects.equals("Private", list.get(0).getAttribute1())) {
                if (jsonNode.get("period").canConvertToInt() && jsonNode.get("period").asInt() > 0) {
                    resVm.setEndTime(DateUtil.plusMonths(resVm.getStartTime(), jsonNode.get("period").asInt()));
                }
            } else {
                if (jsonNode.get("period").canConvertToInt() && jsonNode.get("period").asInt() > 0) {
                    if (!Objects.equals(ChargeType.POST_PAID, resVm.getInstanceChargeType())) {
                        resVm.setEndTime(DateUtil.plusMonths(resVm.getStartTime(), jsonNode.get("period").asInt()));
                    } else {
                        resVm.setEndTime(DateUtil.plusMonths(resVm.getStartTime(), jsonNode.get("period").asInt()));
                    }
                }
            }
        } else {
            if (!Objects.equals(ChargeType.POST_PAID, resVm.getInstanceChargeType()) && jsonNode.get("period")
                                                                                                .canConvertToInt()
                    && jsonNode.get("period").asInt() > 0) {
                resVm.setEndTime(DateUtil.plusMonths(resVm.getStartTime(), jsonNode.get("period").asInt()));
            }
        }
        resVm.setExtraType(Objects.nonNull(jsonNode.get("extraType")) ? jsonNode.get("extraType").asText() : null);
        resVm.setPhysicalHostPoolId(
                Objects.nonNull(jsonNode.get("physicalHostPoolId")) ? jsonNode.findValue("physicalHostPoolId").asLong()
                                                                    : null);
        return resVm;
    }

    private ResVm cloneInstance(JsonNode jsonNode) {
        ResVm resVm = new ResVm();
        resVm.setCloneSourceId(jsonNode.get("cloneSourceId").textValue());
        ResVm originHost = resVmMapper.selectByPrimaryKey(resVm.getCloneSourceId());
        List<ResVmExt> resVmExts = Lists.newArrayList();

        if (jsonNode.get("resPoolId") != null) {
            ResPool resPool = resPoolMapper.selectByPrimaryKey(jsonNode.get("resPoolId").asLong());
            resVm.setResPoolId(resPool.getId());
            resVm.setZone(resPool.getName());
        }

        //其它参数copy
        resVm.setOsName(originHost.getOsName());
        resVm.setPlatform(originHost.getPlatform());
        resVm.setImageId(originHost.getImageId());
        resVm.setCloudDeploymentId(originHost.getCloudDeploymentId());
        resVm.setCloudDeploymentName(originHost.getCloudDeploymentName());
        resVm.setInstanceChargeType(originHost.getInstanceChargeType());
        resVm.setOwnerId(originHost.getOwnerId());
        resVm.setManagementAccount(originHost.getManagementAccount());
        resVm.setManagemenPassword(originHost.getManagemenPassword());
        resVm.setServerType(ServerType.INSTANCE);
        resVm.setRegion(originHost.getRegion());

        resVm.setCloudEnvId(jsonNode.findValue("envId").asLong());

        // instance
        final JsonNode instance = jsonNode.get("instance");
        resVm.setInstanceName(instance.get("instanceName").textValue());

        resVm.setInstanceType(instance.get("instanceType").textValue());

        resVm.setDescription(originHost.getDescription());
        resVm.setCpu(instance.get("cpu").asInt());
        resVm.setMemory(instance.get("memory").asInt());

        resVm.setIoOptimized(ResVmIOStatus.NONE);
        resVm.setRemoteLoginType(originHost.getRemoteLoginType());

        // network
        resVm.setInternetChargeType(originHost.getInternetChargeType());
        resVm.setInternetMaxBandwidthIn(originHost.getInternetMaxBandwidthIn());
        resVm.setInternetMaxBandwidthOut(originHost.getInternetMaxBandwidthOut());

        if (jsonNode.get("nics") != null) {
            JsonNode networks = jsonNode.get("nics");

            //已添加的IP列表
            Set<String> ips = new HashSet<>();
            HashMultimap<Long, String> networkGroupBySubnetId = HashMultimap.create();

            List<NetworkIp> networkIps = new ArrayList<>();
            for (Iterator<JsonNode> elements = networks.elements(); elements.hasNext(); ) {
                JsonNode next = elements.next();
                Long networkId = next.get("networkId").asLong();

                String fixedIp = null;
                // 将固定ip预先占用，并更新到host的ip字段上面
                if (!next.get("autoIp").asBoolean()) {
                    NetworkIp networkIp = new NetworkIp();
                    networkIp.setNetworkId(networkId);
                    networkIp.setId(next.get("fixedIpId").asLong());
                    networkIps.add(networkIp);
                    fixedIp = next.get("fixedIp").textValue();
                    ips.add(fixedIp);
                }
                networkGroupBySubnetId.put(networkId, fixedIp);
            }
            resVm.setNetworkIps(networkIps);
            // 所有选择网络，更新到网络上
            resVm.setInnerIp(Joiner.on(",").join(ips));

            for (Long key : networkGroupBySubnetId.keySet()) {
                ResVmExt resVmExt = new ResVmExt();
                resVmExt.setInstanceId(resVm.getId());
                resVmExt.setResourceId(key.toString());
                resVmExt.setType(ResVmExtEnum.SUBNET.getType());
                resVmExt.setExtra(Joiner.on(",").skipNulls().join(networkGroupBySubnetId.get(key)));
                resVmExts.add(resVmExt);
            }
        }
        resVm.setResVmExts(resVmExts);

        // 获取主机的操作系统类别和管理端口
        resVm.setSshPort(originHost.getSshPort());
        resVm.setOsCategory(originHost.getOsCategory());

        resVm.setStatus(ResVmStatus.CREATING);
        resVm.setManageStatus(ResVmManageStatus.UNUNITED);

        resVm.setKeypairId(originHost.getKeypairId());

        resVm.setOrgSid(BasicInfoUtil.getCurrentOrgSid());

        return resVm;
    }

    private void reservedFixedIp(ResVm resVm) {
        List<NetworkIp> networkIps = resVm.getNetworkIps();
        if (CollectionUtils.isEmpty(networkIps)) {
            return;
        }
        for (NetworkIp networkIp : networkIps) {
            networkIp.setStatus(NetworkManagement.RESERVED);
            networkIp.setAllocateTargetId(resVm.getId());
            BasicWebUtil.prepareUpdateParams(networkIp, resVm.getCreatedBy());
            this.networkIpMapper.updateByPrimaryKeySelective(networkIp);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setServerTemplateInfo(ResVm resVm, Long companyId, Long serverTemplateId, Long deploymentId,
                                      List<Long> strategyIds) {
        String elasticTag = null;
        if (ServerType.ELASTIC.equals(resVm.getServerType()) && resVm.getElasticGroupId() != null) {
        }

        // 设置实例的告警策略
        if (true) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_223492382));
        }
    }

    @Override
    public ResInstResult reCreateResVm(String resVmId) {
        return reCreateResVm(Collections.singletonList(resVmId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResInstResult reCreateResVm(List<String> resVmIds) {
        List<VmCreate> vmCreates = Lists.newArrayList();
        List<Map<String, Object>> data = Lists.newArrayList();

        resVmIds.forEach(resVmId -> {
            ResVm resVm = this.resVmMapper.selectByPrimaryKey(resVmId);
            // 克隆失败，走重新克隆的流程
            if (ResVmStatus.CREATE_FAILURE.equals(resVm.getStatus()) && !Strings.isNullOrEmpty(
                    resVm.getCloneSourceId())) {
                cloneInstanceOnPlatform(resVm, null);

                resVm.setStatus(ResVmStatus.CREATING);
                resVm.setManageStatus(ResVmManageStatus.UNUNITED);
                BasicWebUtil.prepareUpdateParams(resVm, resVm.getCreatedBy());
                this.resVmMapper.updateByPrimaryKeySelective(resVm);
                // 创建失败，或者待发布的时候，重新创建
            } else if (ResVmStatus.CREATE_FAILURE.equals(resVm.getStatus()) || ResVmStatus.PENDING.equals(
                    resVm.getStatus())) {
                // 解析json文件
                Map<String, Object> options = new HashMap<>();
                JsonNode jsonNode = JsonUtil.fromJson(resVm.getOriginParam());
                if (jsonNode.get("instance") != null) {
                    JsonNode instance = jsonNode.get("instance");
                    if (instance.get("notFirst") != null) {
                        options.put("notFirst", instance.get("notFirst").asBoolean());
                    }
                }

                resVm.setStatus(ResVmStatus.CREATING);
                resVm.setManageStatus(ResVmManageStatus.UNUNITED);
                BasicWebUtil.prepareUpdateParams(resVm, resVm.getCreatedBy());
                this.resVmMapper.updateByPrimaryKeySelective(resVm);

                if (StringUtils.isNotBlank(resVm.getManagementAccount())) {
                    String account = CrytoUtilSimple.decrypt(resVm.getManagementAccount(), true);
                    String password = CrytoUtilSimple.decrypt(resVm.getManagemenPassword(), true);
                    resVm.setManagementAccount(StringUtils.trim(account));
                    resVm.setManagemenPassword(StringUtils.trim(password));
                }
                VmCreate vmCreate = makeVmCreateBean(resVm, options);
                vmCreates.add(vmCreate);
                data.add(MapsKit.of("vmSid", vmCreate.getId(), "taskId", vmCreate.getOptions().get("taskId")));
            }
        });

        vmCreates.forEach(this::send2MQ4VmCreate);

        ResInstResult resInstResult = new ResInstResult(ResInstResult.SUCCESS);
        resInstResult.setData(data);
        return resInstResult;
    }

    /**
     * 在创建主机时 获取主机的一些信息
     */
    @SuppressWarnings("checkstyle:MagicNumber")
    private Map<String, Object> getHostInfoMapByImage(ResVm resVm, String cloudEnvType) {
        Criteria criteria = new Criteria();
        criteria.put("cloudEnvId", resVm.getCloudEnvId());
        criteria.put("imageId", resVm.getImageId());
        List<ResImage> images = resImageService.selectByExample(criteria);
        if (CollectionUtil.isEmpty(images)) {
            logger.error("not found vm image [cloudEnvId : {}, imageId : {}]", resVm.getCloudEnvId(),
                         resVm.getImageId());
        }
        return getHostInfoMapByImage(CollectionUtil.isEmpty(images) ? null : images.get(0));
    }

    /**
     * 组装发送云平台的MQ Bean
     */
    private VmCreate makeVmCreateBean(ResVm resVm, Map<String, Object> options) {
        setResVmExtInfo(resVm);

        // 许可证判断,由于已经把当前创建的实例添加到数据库，所以现在只需要判断数据库的数量是否超限
        judgeIsCreateInstanceByLicense(0, resVm.getCloudEnvId());

        VmCreate vmCreate = CloudClientFactory.buildMQBean(resVm.getCloudEnvId(), VmCreate.class);
        switch (CloudEnvType.from(vmCreate.getProviderType())) {
            case OPEN_STACK:
                vmCreate = makeOpenstackMQBean(resVm, vmCreate);
                break;
            case VMWARE:
                vmCreate = makeVMwareMQBean(resVm, vmCreate);
                break;
            case POWER_VC:
                vmCreate = makePowerVcMQBean(resVm, vmCreate);
                break;
            case AWS:
                vmCreate = makeAwsMQBean(resVm, vmCreate);
                break;
            case ALIYUN:
            case QCLOUD:
                vmCreate = makeCloudMQBean(resVm, vmCreate);
                break;
            case HUAWEICLOUD:
                vmCreate = makeHuaweiCloudMQBean(resVm, vmCreate);
                break;
            case FUSIONCOMPUTE:
                vmCreate = makeFusionComputeMQBean(resVm, vmCreate);
                break;
            case AZURE:
                vmCreate = makeAzureMQBean(resVm, vmCreate);
                break;
            case CLOUDOS:
                vmCreate = makeCloudOSMQBean(resVm, vmCreate);
                break;
            case MAAS:
                vmCreate = makeMaasMQBean(resVm, vmCreate);
                break;
            case KSYUN:
            case KING_STACK:
                vmCreate = makeKsyunMQBean(resVm, vmCreate);
                break;
            default:
                break;
        }
        // 获取操作系统类型
        String osType = parseOSType(resVm);

        // setup task
        List<DeployTask> deployTasks;
        if (Objects.nonNull(options) && !Objects.isNull(options.get("appConfigs"))) {
            List<AppDeployModel> appDeployModels = (List<AppDeployModel>) options.get("appConfigs");
            deployTasks = this.basicDeployTaskService.setupResVmTask(resVm, appDeployModels);
        } else {
            deployTasks = this.basicDeployTaskService.setupResVmTask(resVm);
        }
        // 查询原来主机上的任务队列，关联join task任务
        List<Long> deployInstIds = serviceInstTargetService.getDeployInstByTargetId(resVm.getId(),
                                                                                    SelfServiceYamlDeployType.INFRA);

        DeployTask jt = this.basicDeployTaskService.takeResVmLayoutScriptJoinTask(resVm, deployInstIds);
        if (null != jt) {
            DeployTask lastTask = deployTasks.get(deployTasks.size() - 1);
            jt.setDependOn(lastTask.getId());
            jt.setTarget(lastTask.getTarget());
            jt.setTargetName(lastTask.getTargetName());
            logger.info("主机:{} 添加layout script join 任务: {}", resVm.getId(), JsonUtil.toJson(jt));
            deployTasks.add(jt);
        }

        final String creatingHostTaskId = deployTasks.get(0).getId();

        // setup playbook
        DeployPlaybook deployPlaybook = this.basicDeployPlaybookService.setupPlaybook(deployTasks);

        final String message = MessageUtil.getLogMessage("开始创建实例.");
        MongoUtil.save(new LogInfo(resVm.getId(), message), DeployConst.HOST_LOG_KEY_PREFIX);

        cn.com.cloudstar.rightcloud.module.support.access.pojo.User authUser = BasicInfoUtil.getAuthUser();
        vmCreate.setOptions(ImmutableMap.of("playbookId", deployPlaybook.getPid(), "userSid",
                                            Objects.nonNull(authUser) ? authUser.getUserSid() : 100L, "taskId",
                                            creatingHostTaskId, "osType", osType, "notFirst", options == null ? false :
                                                                                              options.get("notFirst")
                                                                                                      == null ? false
                                                                                                              : options.get(
                                                                                                                      "notFirst")));

        // floatingIpPoolId -> uuid
        if (StringUtil.isNotBlank(vmCreate.getFloatingIpPool()) && StringUtil.isNumeric(vmCreate.getFloatingIpPool())) {
            ResVpc vpc = resNetVpcMapper.selectByPrimaryKey(Long.valueOf(vmCreate.getFloatingIpPool()));
            if (null != vpc) {
                vmCreate.setFloatingIpPool(vpc.getUuid());
            }
        }

        return vmCreate;
    }


    @Override
    public String send2MQ4VmCreate(VmCreate vmCreate) {
        String taskId = vmCreate.getOptions().get("taskId").toString();

        try {
            sendToMQ(vmCreate);
        } catch (Exception e) {
            ResVm resVm = new ResVm();
            resVm.setId(vmCreate.getId());
            resVm.setStatus(ResVmStatus.FAILURE);
            resVm.setStatusInfo("异步调用失败！" + e.getMessage());
            resVm.setManageStatus(ResVmManageStatus.DISCONNECT);
            this.resVmMapper.updateByPrimaryKeySelective(resVm);
        }

        CallbakLog callbakLog = new CallbakLog(taskId, MessageUtil.getLogMessage("创建实例中，请等待."), CallbakLog.Type.HOST);
        hostDeployCallbackLogMessageHandler.execute(callbakLog);

        return taskId;
    }

    /**
     * 发送云平台克隆实例的请求
     */
    private ResInstResult cloneInstanceOnPlatform(ResVm resVm, Map<String, Object> options) {
        // 许可证判断
        judgeIsCreateInstanceByLicense(1, resVm.getCloudEnvId());
        judgeIsCreateInstanceBySaaSLimit(1, resVm.getCloudEnvId());

        // setup task
        List<DeployTask> deployTasks;
        deployTasks = this.basicDeployTaskService.setupResVmTask(resVm);

        final String creatingHostTaskId = deployTasks.get(0).getId();

        // setup playbook
        DeployPlaybook deployPlaybook = this.basicDeployPlaybookService.setupPlaybook(deployTasks);

        final String message = MessageUtil.getLogMessage("开始克隆实例.");
        MongoUtil.save(new LogInfo(resVm.getId(), message), DeployConst.HOST_LOG_KEY_PREFIX);

        ResInstResult resInstResult = null;
        Base vmClone = makeVMwareCloneMQBean(resVm);
        // 获取操作系统类型
        String osType = parseOSTypeByOsName(resVm.getOsName());

        vmClone.setOptions(
                ImmutableMap.of("playbookId", deployPlaybook.getPid(), "taskId", creatingHostTaskId, "osType", osType,
                                "notFirst", !Objects.isNull(options == null ? null : options.get("notFirst"))));

        try {
            resInstResult = sendToMQ(vmClone);
        } catch (Exception e) {
            resInstResult = new ResInstResult(ResInstResult.FAILURE, "异步调用失败！" + e.getMessage());
            resVm.setStatus(ResVmStatus.FAILURE);
            resVm.setStatusInfo("异步调用失败！" + e.getMessage());
            resVm.setManageStatus(ResVmManageStatus.DISCONNECT);
            this.resVmMapper.updateByPrimaryKeySelective(resVm);
        }
        resInstResult.setData(MapsKit.of("vmSid", resVm.getId(), "taskId", creatingHostTaskId));

        return resInstResult;
    }

    /**
     * 根据镜像猜测系统分类
     */
    private String parseOsCategory(ResImage image) {
        if (null == image) {
            return OsType.LINUX;
        }
        if (StringUtils.containsIgnoreCase(image.getOsType(), OsType.WINDOWS) || StringUtils.containsIgnoreCase(
                image.getOsPlatform(), OsType.WINDOWS)) {
            return OsType.WINDOWS;
        }
        if (StringUtils.containsIgnoreCase(image.getOsType(), OsType.AIX) || StringUtils.containsIgnoreCase(
                image.getOsPlatform(), OsType.AIX)) {
            return OsType.AIX;
        }
        return OsType.LINUX;
    }

    /**
     * 根据操作系统名称
     */
    private String parseOsName(ResVm resVm) {
        if (StringUtils.isNotBlank(resVm.getOsCategory())) {
            return resVm.getOsCategory();
        }
        if (Strings.isNullOrEmpty(resVm.getOsName())) {
            return OsType.LINUX;
        }

        if (OsType.WINDOWS.equalsIgnoreCase(resVm.getOsName()) || StringUtils.containsIgnoreCase(resVm.getOsName(),
                                                                                                 OsType.WINDOWS)) {
            return OsType.WINDOWS;
        }
        return OsType.LINUX;
    }

    private boolean isRepeatDeployScript(List<CloudDeploymentScript> scriptList, Long scriptId, Long stScriptId,
                                         Long deploymentId, Long serverTemplateId) {
        boolean result = false;
        if (!CollectionUtils.isEmpty(scriptList)) {
            for (CloudDeploymentScript script : scriptList) {
                if (deploymentId.equals(script.getDeploymentId()) && serverTemplateId.equals(
                        script.getServerTemplateId()) && scriptId.equals(script.getScriptId()) && stScriptId.equals(
                        script.getStScriptId())) {
                    result = true;
                    break;
                }
            }
        }

        return result;
    }

    private void setNicInfo(VmCreate vmCreate, ResVm resVm) {
        setNicInfo(vmCreate, resVm, null);
    }

    private void setNicInfo(VmCreate vmCreate, ResVm resVm, CloudEnvType cloudEnvType) {
        List<ResVmExt> resVmExts = resVm.getCloudHostExt().get(ResVmExtEnum.SUBNET.getType());
        Optional<ResVmExt> first = resVmExts.stream().findFirst();
        if (first.isPresent()) {
            ResVmExt resVmExt = first.get();

            VmNic vmNic = new VmNic();
            ResVpc vpc = resNetVpcMapper.selectByPrimaryKey(resVmExt.getVpcId());
            vmNic.setNetId(vpc.getUuid());
            vmNic.setOwner(vpc.getOwner());
            Network network = networkMapper.selectByPrimaryKey(Long.parseLong(resVmExt.getResourceId()));
            if (CloudEnvType.HUAWEICLOUD.equals(cloudEnvType)) {
                vmNic.setSubnetId(network.getNetworkUuid());
            } else if (CloudEnvType.AZURE.equals(cloudEnvType)) {
                vmNic.setSubnetId(network.getNetworkName());
            } else {
                vmNic.setSubnetId(network.getUuid());
            }
            vmCreate.setNics(Collections.singletonList(vmNic));
        }
    }

    private VmCreate makeCloudOSMQBean(ResVm cloudHost, VmCreate vmCreate) {
        JsonNode jsonNode = JsonUtil.fromJson(cloudHost.getOriginParam());
        vmCreate.setId(cloudHost.getId());
        vmCreate.setName(cloudHost.getInstanceName());
        vmCreate.setVmType(cloudHost.getInstanceType());

        // networks
        if (ExtraType.BAREMETAL.equalsIgnoreCase(cloudHost.getExtraType())) {
            JsonNode networkInfo = jsonNode.get("networkInfo");
            List<VmNic> nics = new ArrayList<>();
            if (networkInfo != null) {
                Iterator<JsonNode> elements = networkInfo.elements();
                while (elements.hasNext()) {
                    JsonNode nicJsonNode = elements.next();
                    VmNic vmNic = new VmNic();
                    vmNic.setNetId(nicJsonNode.get("uuid").textValue());
                    vmNic.setPrivateIp(
                            Objects.nonNull(nicJsonNode.get("ipAddr")) ? nicJsonNode.get("ipAddr").textValue() : null);
                    vmNic.setGateway(
                            Objects.nonNull(nicJsonNode.get("gateWayIp")) ? nicJsonNode.get("gateWayIp").textValue()
                                                                          : null);
                    vmNic.setPortVlanID(
                            Objects.nonNull(nicJsonNode.get("vlanId")) ? nicJsonNode.get("vlanId").asInt() : null);
                    vmNic.setGatewaySign(
                            Objects.nonNull(nicJsonNode.get("isGateway")) ? nicJsonNode.get("isGateway").asBoolean()
                                                                          : null);
                    vmNic.setGroupSign(
                            Objects.nonNull(nicJsonNode.get("isGroup")) ? nicJsonNode.get("isGroup").asBoolean()
                                                                        : null);
                    nics.add(vmNic);
                }
            }
            vmCreate.setNics(nics);
        } else {
            VmNic vmNic = new VmNic();
            JsonNode networks = jsonNode.get("networks");
            ResVpc vpc = resNetVpcMapper.selectByPrimaryKey(Long.parseLong(networks.findValue("networkId").asText()));
            vmNic.setNetId(vpc.getUuid());
            vmNic.setOwner(vpc.getOwner());
            Network network = networkMapper.selectByPrimaryKey(Long.parseLong(networks.findValue("subnetId").asText()));
            vmNic.setSubnetId(network.getUuid());
            vmCreate.setNics(Collections.singletonList(vmNic));
        }

        vmCreate.setFloatingIpPool(cloudHost.getFloatingIpPoolId());
        vmCreate.setFloatingIp(cloudHost.getPublicIp());
        vmCreate.setImage(cloudHost.getImageId());
        vmCreate.setAdminName(cloudHost.getManagementAccount());
        vmCreate.setAdminPass(cloudHost.getManagemenPassword());
        vmCreate.setRegion(cloudHost.getRegion());

        Criteria criteria = new Criteria();
        criteria.put("uuid", cloudHost.getZone());
        List<ResZone> resZones = resZoneMapper.selectByParams(criteria);
        if (CollectionUtils.isEmpty(resZones)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1203523238));
        }
        ResZone resZone = resZones.get(0);
        vmCreate.setAvailabilityZone(resZone.getName());
        vmCreate.setZoneUuid(resZone.getUuid());
        vmCreate.setNodeId(Objects.nonNull(jsonNode.get("nodeId")) ? jsonNode.get("nodeId").asText() : null);
        vmCreate.setExtraType(cloudHost.getExtraType());

        if (RemoteLoginType.BY_KEY.equalsIgnoreCase(cloudHost.getRemoteLoginType())) {
            CloudKeyPair keyPair = this.cloudKeyPairMapper.selectByPrimaryKey(cloudHost.getKeypairId());
            vmCreate.setKeypair(new KeyVo() {{
                setName(keyPair.getKeypairName());
                setPublicKey(keyPair.getPublicKey());
                setFingerprint(keyPair.getFingerprint());
            }});
        } else {
            vmCreate.setAdminPass(cloudHost.getManagemenPassword());
        }
        vmCreate.setSecurityGroupNames(
                Objects.nonNull(jsonNode.get("securityGroup")) ? Sets.newHashSet(jsonNode.get("securityGroup").asText())
                                                               : null);

        JsonNode systemDisk = jsonNode.get("systemDisk");
        if (Objects.isNull(systemDisk) || systemDisk.isNull()) {
            List<ResVmType> vmTypes = resVmTypeService.selectByParams(
                    new Criteria("uuid", cloudHost.getInstanceType()));
            if (vmTypes.size() > 0 && Objects.nonNull(vmTypes.get(0).getDisk())) {
                vmCreate.setSysDiskSize(vmTypes.get(0).getDisk().toString());
            }
        } else {
            vmCreate.setSysDiskSize(systemDisk.get("diskSize").asText());
        }

        // 数据盘
        JsonNode dataDisks = jsonNode.get("dataDisk");
        if (dataDisks != null && dataDisks.elements().hasNext()) {
            List<VmDisk> disks = new ArrayList<>();
            dataDisks.elements().forEachRemaining(dataDisk -> {
                VmDisk vmDisk = new VmDisk();
                vmDisk.setSize(dataDisk.get("diskSize").asText());
                if (dataDisk.hasNonNull("zone")) {
                    Criteria storageCriteria = new Criteria();
                    storageCriteria.put("uuid", dataDisk.get("zone").textValue());
                    List<ResZone> storageZones = resZoneMapper.selectByParams(storageCriteria);
                    if (CollectionUtils.isEmpty(storageZones)) {
                        throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1203523238));
                    }
                    ResZone storageZone = storageZones.get(0);
                    vmDisk.setAvailabilityZone(storageZone.getName());
                } else {
                    // 查找云环境第一个 存储分区
                    Criteria storageCriteria = new Criteria();
                    storageCriteria.put("cloudEnvId", cloudHost.getCloudEnvId());
                    storageCriteria.put("zoneType", ResZoneType.STORAGE);
                    List<ResZone> storageZones = resZoneMapper.selectByParams(storageCriteria);
                    if (!CollectionUtils.isEmpty(storageZones)) {
                        if (storageZones.size() >= 2) {
                            logger.warn("查找到多个存储分区! 云环境ID：{}", cloudHost.getCloudEnvId());
                        }
                        vmDisk.setAvailabilityZone(storageZones.get(0).getName());
                    } else {
                        logger.error("云环境:{} 未找到可用存储分区,跳过 硬盘创建", cloudHost.getCloudEnvId());
                        return;
                    }
                }
                if (Objects.nonNull(dataDisk.get("dataDiskDeleteWithInstance"))) {
                    vmDisk.setDeleteWithInstance(dataDisk.get("dataDiskDeleteWithInstance").asBoolean());
                } else {
                    vmDisk.setDeleteWithInstance(true);
                }
                if (Objects.nonNull(dataDisk.get("storageTypeName"))) {
                    vmDisk.setVolumeType(dataDisk.get("storageTypeName").asText());
                }
                if (Objects.nonNull(dataDisk.get("storageTypeId"))) {
                    vmDisk.setVolumeTypeId(dataDisk.get("storageTypeId").asText());
                }
                if (dataDisk.has("dataDiskCategory")) {
                    long dataDiskCategory = dataDisk.get("dataDiskCategory").asLong();
                    ResVolumeType resVolumeType = resVolumeTypeMapper.selectByPrimaryKey(dataDiskCategory);
                    vmDisk.setVolumeTypeId(resVolumeType.getId().toString());
                    vmDisk.setVolumeType(resVolumeType.getUuid());
                }
                if (dataDisk.has("dataDiskType")) {
                    if ("share".equalsIgnoreCase(dataDisk.get("dataDiskType").asText())) {
                        ResVdHost vdHost = new ResVdHost();
                        vdHost.setHostId(cloudHost.getId());
                        vdHost.setVdSid(" ");
                        vdHost.setCloudEnvId(cloudHost.getCloudEnvId());
                        vdHost.setOrgSid(BasicInfoUtil.getCurrentOrgSid());
                        resVdHostMapper.insertSelective(vdHost);
                        vmDisk.setDiskType("share");
                    }
                }
                disks.add(vmDisk);
            });
            vmCreate.setDisks(disks);
        }

        JsonNode instance = jsonNode.get("instance");
        if (null != instance) {
            if (instance.has("index")) {
                vmCreate.setIndex(instance.get("index").asInt());
            }

            // 虚机亲和性
            if (Objects.nonNull(instance.get("serverGroupId")) && !instance.get("serverGroupId").isNull()) {
                ResServerGroup resServerGroup = resServerGroupService.selectByPrimaryKey(
                        instance.get("serverGroupId").asLong());
                vmCreate.setServerGroup(resServerGroup.getUuid());
            }
        }
        return vmCreate;
    }

    private VmCreate makeOpenstackMQBean(ResVm resVm, VmCreate vmCreate) {
        JsonNode jsonNode = JsonUtil.fromJson(resVm.getOriginParam());
        vmCreate.setId(resVm.getId());
        vmCreate.setName(resVm.getInstanceName());
        vmCreate.setOsName(resVm.getOsName());
        vmCreate.setVmType(resVm.getInstanceType());

        setNicInfo(vmCreate, resVm);

        vmCreate.setFloatingIpPool(resVm.getFloatingIpPoolId());
        vmCreate.setFloatingIp(resVm.getPublicIp());
        // 弹性IP带宽
        JsonNode networks = jsonNode.get("networks");
        if (Objects.nonNull(networks) && networks.size() > 0) {
            JsonNode jn = networks.isArray() ? networks.get(0) : networks;
            if (jn.get("bandwidth") != null) {
                vmCreate.setBandwidth(jn.get("bandwidth").asText("1"));
            }
        } else {
            // 默认带宽1
            vmCreate.setBandwidth("1");
        }

        vmCreate.setImage(resVm.getImageId());
        vmCreate.setImageType(resVm.getImageType());
        vmCreate.setAdminName(resVm.getManagementAccount());
        vmCreate.setAdminPass(resVm.getManagemenPassword());

        vmCreate.setRegion(resVm.getRegion());
        vmCreate.setAvailabilityZone(resVm.getZone());
        if (jsonNode.findValue("securityGroupId") != null && !jsonNode.findValue("securityGroupId").isNull()) {
            ResSecurityGroup resSecurityGroup = resSecurityGroupService.selectByPrimaryKey(
                    jsonNode.findValue("securityGroupId").asLong());

            resSecurityGroupService.assertSecurityGroupNonNull(resSecurityGroup);
            vmCreate.setSecurityGroupNames(Sets.newHashSet(resSecurityGroup.getUuid()));
        }

        if (RemoteLoginType.BY_KEY.equalsIgnoreCase(resVm.getRemoteLoginType())) {
            CloudKeyPair keyPair = this.cloudKeyPairMapper.selectByPrimaryKey(resVm.getKeypairId());
            vmCreate.setKeypair(new KeyVo() {{
                setName(keyPair.getKeypairName());
                setPublicKey(keyPair.getPublicKey());
                setFingerprint(keyPair.getFingerprint());
            }});
        } else {
            vmCreate.setAdminPass(resVm.getManagemenPassword());
        }

        JsonNode systemDisk = jsonNode.get("systemDisk");
        vmCreate.setSysDiskSize(systemDisk.get("diskSize").asText());

        if (systemDisk.has("diskCategory") && systemDisk.get("diskCategory") != null) {
            long dataDiskCategory = systemDisk.get("diskCategory").asLong();
            ResVolumeType resVolumeType = resVolumeTypeMapper.selectByPrimaryKey(dataDiskCategory);
            vmCreate.setSystemDiskCategory(resVolumeType.getTypeName());
            vmCreate.setSysDiskVolumeType(resVolumeType.getId().toString());
        }

        if (systemDisk.has("diskDeleteWithInstance") && systemDisk.get("diskDeleteWithInstance") != null) {
            vmCreate.setSystemDiskDeleteWithInstance(systemDisk.get("diskDeleteWithInstance").asBoolean());
        }

        // 数据盘
        JsonNode dataDisks = jsonNode.get("dataDisk");
        if (dataDisks != null && dataDisks.elements().hasNext()) {
            List<VmDisk> disks = new ArrayList<>();
            dataDisks.elements().forEachRemaining(dataDisk -> {
                VmDisk vmDisk = new VmDisk();
                vmDisk.setSize(dataDisk.get("diskSize").asText());
                if (dataDisk.hasNonNull("zone")) {
                    vmDisk.setAvailabilityZone(dataDisk.get("zone").textValue());
                }
                if (Objects.nonNull(dataDisk.get("diskDeleteWithInstance"))) {
                    vmDisk.setDeleteWithInstance(dataDisk.get("diskDeleteWithInstance").asBoolean());
                } else {
                    vmDisk.setDeleteWithInstance(true);
                }
                if (dataDisk.has("diskCategory")) {
                    long dataDiskCategory = dataDisk.get("diskCategory").asLong();
                    ResVolumeType resVolumeType = resVolumeTypeMapper.selectByPrimaryKey(dataDiskCategory);
                    vmDisk.setVolumeTypeId(resVolumeType.getId().toString());
                    vmDisk.setVolumeType(resVolumeType.getUuid());
                }
                disks.add(vmDisk);
            });
            vmCreate.setDisks(disks);
        }

        JsonNode instance = jsonNode.get("instance");
        if (null != instance) {
            if (instance.has("index")) {
                vmCreate.setIndex(instance.get("index").asInt());
            }

            // 虚机亲和性
            if (Objects.nonNull(instance.get("serverGroupId")) && !instance.get("serverGroupId").isNull()) {
                ResServerGroup resServerGroup = resServerGroupService.selectByPrimaryKey(
                        instance.get("serverGroupId").asLong());
                if (Objects.nonNull(resServerGroup)) {
                    vmCreate.setServerGroup(resServerGroup.getUuid());
                }
            }
        }
        return vmCreate;
    }

    private VmCreate makeHuaweiCloudMQBean(ResVm resVm, VmCreate vmCreate) {
        JsonNode jsonNode = JsonUtil.fromJson(resVm.getOriginParam());
        vmCreate.setId(resVm.getId());
        vmCreate.setName(resVm.getInstanceName());
        vmCreate.setRegion(resVm.getRegion());
        vmCreate.setAvailabilityZone(resVm.getZone());
        //主机类型 如s2.4xlarge.2 包含cpu和memory配置
        vmCreate.setVmType(resVm.getInstanceType());
        //镜像
        vmCreate.setImage(resVm.getImageId());

        //network
        setNicInfo(vmCreate, resVm, CloudEnvType.HUAWEICLOUD);
        vmCreate.setFloatingIpPool(resVm.getFloatingIpPoolId());
        vmCreate.setFloatingIp(resVm.getPublicIp());

        vmCreate.setInstanceChargeType(resVm.getInstanceChargeType());
        if (Objects.nonNull(jsonNode.get("period"))) {
            vmCreate.setPeriod(jsonNode.get("period").intValue());
        }
        //用户名密码
        vmCreate.setAdminName(resVm.getManagementAccount());
        //密钥登录
        if (RemoteLoginType.BY_KEY.equals(resVm.getRemoteLoginType())) {
            CloudKeyPair cloudKeyPair = cloudKeyPairMapper.selectByPrimaryKey(resVm.getKeypairId());
            vmCreate.setKeypair(new KeyVo() {{
                setName(cloudKeyPair.getKeypairName());
                setPublicKey(cloudKeyPair.getPublicKey());
                setFingerprint(cloudKeyPair.getFingerprint());
            }});
        } else {
            vmCreate.setAdminPass(resVm.getManagemenPassword());
        }

        //安全组
        setSgInfo(vmCreate, resVm);

        //系统盘
        vmCreate.setSysDiskSize(jsonNode.get("systemDisk").get("diskSize").asText());
        if (jsonNode.get("systemDisk").get("diskCategory").canConvertToLong()) {
            long systemDiskCategory = jsonNode.get("systemDisk").get("diskCategory").asLong();
            ResVolumeType resVolumeType = resVolumeTypeMapper.selectByPrimaryKey(systemDiskCategory);
            vmCreate.setSysDiskVolumeType(resVolumeType.getUuid());
        } else {
            vmCreate.setSysDiskVolumeType(jsonNode.get("systemDisk").get("diskCategory").asText());
        }

        //数据盘
        JsonNode dataDisks = jsonNode.get("dataDisk");
        if (dataDisks != null && dataDisks.elements().hasNext()) {
            List<VmDisk> vmDisks = new ArrayList<>();
            dataDisks.elements().forEachRemaining(dataDisk -> {
                VmDisk vmDisk = new VmDisk();
                vmDisk.setSize(dataDisk.get("diskSize").asText());
                //是否随实例删除
                if (Objects.nonNull(dataDisk.get("diskDeleteWithInstance"))) {
                    vmDisk.setDeleteWithInstance(dataDisk.get("diskDeleteWithInstance").asBoolean());
                } else {
                    vmDisk.setDeleteWithInstance(true);
                }
                //云盘类型
                if (Objects.nonNull(dataDisk.get("diskCategory"))) {
                    if (dataDisk.get("diskCategory").canConvertToLong()) {
                        long dataDiskCategory = dataDisk.get("diskCategory").asLong();
                        ResVolumeType resVolumeType = resVolumeTypeMapper.selectByPrimaryKey(dataDiskCategory);
                        vmDisk.setVolumeType(resVolumeType.getUuid());
                    } else {
                        vmDisk.setVolumeType(dataDisk.get("diskCategory").asText());
                    }
                } else {
                    vmDisk.setVolumeType("SATA");
                }
                vmDisks.add(vmDisk);
            });
            vmCreate.setDisks(vmDisks);
        }

        return vmCreate;
    }

    private VmCreate makeKsyunMQBean(ResVm resVm, VmCreate vmCreate) {
        JsonNode jsonNode = JsonUtil.fromJson(resVm.getOriginParam());
        vmCreate.setId(resVm.getId());
        vmCreate.setName(resVm.getInstanceName());
        vmCreate.setRegion(resVm.getRegion());
        vmCreate.setAvailabilityZone(resVm.getZone());
        // 主机类型 如s2.4xlarge.2 包含cpu和memory配置
        vmCreate.setVmType(resVm.getInstanceType());
        // 镜像
        vmCreate.setImage(resVm.getImageId());
        // 项目
        if (Objects.nonNull(jsonNode.get("resProjectId"))) {
            ResProject resProject = this.resProjectService.selectByPrimaryKey(jsonNode.get("resProjectId").asLong());
            vmCreate.setProjectId(resProject.getUuid());
        }
        setNicInfo(vmCreate, resVm, CloudEnvType.KSYUN);

        // 网络 [{"networkType":"vpc","networkId":22246,"subnetId":16127,"hasPublicNet":false,"securityGroupId":10447,"bandwidth":0}]
        JsonNode networks = jsonNode.get("networks");
        if (Objects.nonNull(networks)) {
            JsonNode networkJson = networks.elements().next();
            VmNic vmNic = new VmNic();
            ResVpc vpc = resNetVpcMapper.selectByPrimaryKey(Long.parseLong(networkJson.get("networkId").asText()));
            vmNic.setNetId(vpc.getUuid());
            Network network = networkMapper.selectByPrimaryKey(Long.parseLong(networkJson.get("subnetId").asText()));
            vmNic.setSubnetId(network.getUuid());
            vmCreate.setNics(Collections.singletonList(vmNic));
            if (Objects.nonNull(networkJson.get("hasPublicNet")) && !networkJson.get("hasPublicNet").isNull()) {
                vmCreate.setHasPublicNet(networkJson.get("hasPublicNet").booleanValue());
            }
        }
        // 公网IP
        vmCreate.setFloatingIp(resVm.getPublicIp());

        vmCreate.setInstanceChargeType(resVm.getInstanceChargeType());
        if (Objects.nonNull(jsonNode.get("period"))) {
            vmCreate.setPeriod(jsonNode.get("period").intValue());
        }
        //用户名密码
        vmCreate.setAdminName(resVm.getManagementAccount());
        //密钥登录
        if (RemoteLoginType.BY_KEY.equals(resVm.getRemoteLoginType())) {
            CloudKeyPair cloudKeyPair = cloudKeyPairMapper.selectByPrimaryKey(resVm.getKeypairId());
            vmCreate.setKeypair(new KeyVo() {{
                setName(cloudKeyPair.getKeypairName());
                setPublicKey(cloudKeyPair.getPublicKey());
                setFingerprint(cloudKeyPair.getFingerprint());
            }});
        } else {
            vmCreate.setAdminPass(resVm.getManagemenPassword());
        }

        //安全组
        setSgInfo(vmCreate, resVm);

        //系统盘
        JsonNode systemDiskJson = jsonNode.get("systemDisk");
        if (Objects.nonNull(systemDiskJson)) {
            vmCreate.setSysDiskSize(systemDiskJson.get("diskSize").asText());
            if (systemDiskJson.get("diskCategory").canConvertToLong()) {
                long systemDiskCategory = systemDiskJson.get("diskCategory").asLong();
                ResVolumeType resVolumeType = resVolumeTypeMapper.selectByPrimaryKey(systemDiskCategory);
                vmCreate.setSysDiskVolumeType(resVolumeType.getUuid());
            } else {
                vmCreate.setSysDiskVolumeType(systemDiskJson.get("diskCategory").asText());
            }
        }

        //数据盘
        JsonNode dataDisks = jsonNode.get("dataDisk");
        if (dataDisks != null && dataDisks.elements().hasNext()) {
            List<VmDisk> vmDisks = new ArrayList<>();
            dataDisks.elements().forEachRemaining(dataDisk -> {
                VmDisk vmDisk = new VmDisk();
                JsonNode diskSize = dataDisk.get("diskSize");
                if (Objects.isNull(diskSize) || diskSize.isNull()) {
                    return;
                }

                vmDisk.setSize(dataDisk.get("diskSize").asText());
                //是否随实例删除
                if (Objects.nonNull(dataDisk.get("diskDeleteWithInstance"))) {
                    vmDisk.setDeleteWithInstance(dataDisk.get("diskDeleteWithInstance").asBoolean());
                } else {
                    vmDisk.setDeleteWithInstance(true);
                }
                //云盘类型
                if (Objects.nonNull(dataDisk.get("diskCategory"))) {
                    if (dataDisk.get("diskCategory").canConvertToLong()) {
                        long dataDiskCategory = dataDisk.get("diskCategory").asLong();
                        ResVolumeType resVolumeType = resVolumeTypeMapper.selectByPrimaryKey(dataDiskCategory);
                        vmDisk.setType(resVolumeType.getUuid());
                    } else {
                        vmDisk.setType(dataDisk.get("diskCategory").asText());
                    }
                } else {
                    vmDisk.setType("SATA");
                }
                vmDisks.add(vmDisk);
            });
            vmCreate.setDisks(vmDisks);
        }

        return vmCreate;
    }

    private VmCreate makeFusionComputeMQBean(ResVm resVm, VmCreate vmCreate) {
        JsonNode jsonNode = JsonUtil.fromJson(resVm.getOriginParam());
        vmCreate.setId(resVm.getId());
        vmCreate.setName(resVm.getInstanceName());
        vmCreate.setVmType(resVm.getInstanceType());
        vmCreate.setCpu(String.valueOf(resVm.getCpu()));
        vmCreate.setMemory(String.valueOf(resVm.getMemory()));
        // region 设定系统盘信息
        if (jsonNode.get("systemDisk").get("diskSize") != null) {
            vmCreate.setSysDiskSize(jsonNode.get("systemDisk").get("diskSize").asText());
            if (jsonNode.get("systemDisk").has("diskCategory")) {
                vmCreate.setSysDiskVolumeType(jsonNode.get("systemDisk").get("diskCategory").asText());
            }
        }
        vmCreate.setAdminName(resVm.getManagementAccount());
        vmCreate.setAdminPass(resVm.getManagemenPassword());
        // 镜像信息
        vmCreate.setImage(resVm.getImageId());
        ResImage resImage = this.resImageMapper.selectByPrimaryKey(jsonNode.get("instance").get("imageId").asLong());
        vmCreate.setImageUri(resImage.getUri());
        vmCreate.setOriginPass(resImage.getManagementPassword());

        if (Objects.nonNull(resVm.getKeypairId())) {
            CloudKeyPair keyPair = this.cloudKeyPairMapper.selectByPrimaryKey(resVm.getKeypairId());
            AssertUtil.requireNonBlank(keyPair, "密钥不能为空");
            vmCreate.setKeypair(new KeyVo() {{
                setName(keyPair.getKeypairName());
                setPublicKey(keyPair.getPublicKey());
                setFingerprint(keyPair.getFingerprint());
                setPrivateKey(keyPair.getPrivateKey());
            }});
        }

        // 数据盘
        JsonNode dataDisks = jsonNode.get("dataDisk");
        if (dataDisks != null && dataDisks.elements().hasNext()) {
            List<VmDisk> disks = new ArrayList<>();
            dataDisks.elements().forEachRemaining(dataDisk -> {
                VmDisk vmDisk = new VmDisk();
                vmDisk.setSize(dataDisk.get("diskSize").asText());
                vmDisk.setVolumeType(dataDisk.get("diskCategory").asText());
                if (Objects.nonNull(dataDisk.get("diskDeleteWithInstance"))) {
                    vmDisk.setDeleteWithInstance(dataDisk.get("diskDeleteWithInstance").asBoolean());
                } else {
                    vmDisk.setDeleteWithInstance(true);
                }
                disks.add(vmDisk);
            });
            vmCreate.setDisks(disks);
        }

        // GPU资源组
        JsonNode gpuGroup = jsonNode.get("gpuGroup");
        if (gpuGroup != null && gpuGroup.get("urn") != null && gpuGroup.get("count") != null) {
            vmCreate.setGpuGroup(new GpuGroup(gpuGroup.get("urn").asText(), gpuGroup.get("count").asInt()));
        }

        List<ResHostProvisionModel> resourceOverallOnVMware = this.resService.getResourceOverallOnVMware(
                resVm.getCloudEnvId(), resVm.getRegion());
        long resPoolId = jsonNode.get("resPoolId").asLong();
        this.resService.pickUpResourceOnVMware(resourceOverallOnVMware, resPoolId, vmCreate, jsonNode);
        AssertUtil.requireNonBlank(vmCreate.getNics(), "网络未设置");
        return vmCreate;
    }

    private VmCreate makeVMwareMQBean(ResVm resVm, VmCreate vmCreate) {
        JsonNode jsonNode = JsonUtil.fromJson(resVm.getOriginParam());
        vmCreate.setId(resVm.getId());
        vmCreate.setName(resVm.getInstanceName());
        vmCreate.setResVmName(resVm.getHostName());
        vmCreate.setVmType(resVm.getInstanceType());
        vmCreate.setCpu(String.valueOf(resVm.getCpu()));
        vmCreate.setMemory(String.valueOf(resVm.getMemory()));

        // region 设定系统盘信息
        JsonNode systemDisk = jsonNode.get("systemDisk");
        vmCreate.setSysDiskVolumeType(systemDisk.get("diskCategory").asText());
        if (jsonNode.get("systemDisk").get("diskSize") != null) {
            vmCreate.setSysDiskSize(jsonNode.get("systemDisk").get("diskSize").asText());
        }
        // endregion
        String password = CrytoUtilSimple.decrypt(resVm.getManagemenPassword(), true);
        vmCreate.setImage(resVm.getImageId());
        vmCreate.setAdminName(resVm.getManagementAccount());
        vmCreate.setAdminPass(resVm.getManagemenPassword());

        Long imageId = jsonNode.get("instance").get("imageId").asLong();
        ResImage resImage = this.resImageMapper.selectByPrimaryKey(imageId);
        if (!Objects.isNull(resImage)) {
            vmCreate.setOriginPass(resImage.getManagementPassword());
        }

        // 数据盘
        JsonNode dataDisks = jsonNode.get("dataDisk");
        if (dataDisks != null && dataDisks.elements().hasNext()) {
            List<VmDisk> disks = new ArrayList<>();
            dataDisks.elements().forEachRemaining(dataDisk -> {
                VmDisk vmDisk = new VmDisk();
                vmDisk.setSize(dataDisk.get("diskSize").asText());
                vmDisk.setVolumeType(dataDisk.get("diskCategory").asText());
                if (Objects.nonNull(dataDisk.get("diskDeleteWithInstance"))) {
                    vmDisk.setDeleteWithInstance(dataDisk.get("diskDeleteWithInstance").asBoolean());
                } else {
                    vmDisk.setDeleteWithInstance(true);
                }
                disks.add(vmDisk);
            });
            vmCreate.setDisks(disks);
        }

        List<ResHostProvisionModel> resourceOverallOnVMware = this.resService.getResourceOverallOnVMware(
                resVm.getCloudEnvId(), resVm.getRegion());
        long resPoolId = jsonNode.get("resPoolId").asLong();
        this.resService.pickUpResourceOnVMware(resourceOverallOnVMware, resPoolId, vmCreate, jsonNode);
        return vmCreate;
    }

    private VmClone makeVMwareCloneMQBean(ResVm resVm) {
        VmClone vmClone = CloudClientFactory.buildMQBean(resVm.getCloudEnvId(), VmClone.class);
        JsonNode jsonNode = JsonUtil.fromJson(resVm.getOriginParam());
        vmClone.setId(resVm.getId());
        vmClone.setName(resVm.getInstanceName());
        vmClone.setVmType(resVm.getInstanceType());
        vmClone.setCpu(String.valueOf(resVm.getCpu()));
        vmClone.setMemory(String.valueOf(resVm.getMemory()));

        ResVm originHost = resVmMapper.selectByPrimaryKey(resVm.getCloneSourceId());

        final JsonNode instance = jsonNode.get("instance");
        //电源状态
        if (instance.get("powerStatus") != null && instance.get("powerStatus").asBoolean()) {
            vmClone.setPowerStatus(Boolean.TRUE);
        } else {
            vmClone.setPowerStatus(Boolean.FALSE);
        }

        // 网络是否变化
        if (instance.get("networkChange") != null && instance.get("networkChange").asBoolean()) {
            vmClone.setNetworkChange(Boolean.TRUE);
        }

        // region 设定系统盘信息
        if (jsonNode.get("systemDisk").get("volumeTypeId") != null) {
            vmClone.setSysDiskVolumeType(jsonNode.get("systemDisk").get("volumeTypeId").asText());
        }

        if (jsonNode.get("systemDisk").get("systemDiskSize") != null) {
            vmClone.setSysDiskSize(jsonNode.get("systemDisk").get("systemDiskSize").asText());
        }

        // 数据盘
        JsonNode dataDisks = jsonNode.get("dataDisk");
        if (dataDisks != null && dataDisks.elements().hasNext()) {
            List<VmDisk> disks = new ArrayList<>();
            dataDisks.elements().forEachRemaining(dataDisk -> {
                VmDisk vmDisk = new VmDisk();
                vmDisk.setSize(dataDisk.get("dataDiskSize").asText());
                if (dataDisk.get("volumeTypeId") != null) {
                    vmDisk.setVolumeType(dataDisk.get("volumeTypeId").asText());
                }
                disks.add(vmDisk);
            });
            vmClone.setDisks(disks);
        }

        //判断模板创建还是克隆, 克隆从主机里面取原密码, 镜像
        vmClone.setCloneInstanceId(originHost.getInstanceId());
        vmClone.setOriginPass(originHost.getManagemenPassword());

        vmClone.setAdminName(resVm.getManagementAccount());
        vmClone.setAdminPass(CrytoUtilSimple.decrypt(resVm.getManagemenPassword(), true));

        VmCreate vmCreate = new VmCreate();
        vmCreate.setId(vmClone.getId());
        vmCreate.setMemory(vmClone.getMemory());
        vmCreate.setCpu(vmClone.getCpu());
        vmCreate.setSysDiskVolumeType(vmClone.getSysDiskVolumeType());
        vmCreate.setSysDiskSize(vmClone.getSysDiskSize());
        vmCreate.setDisks(vmClone.getDisks());

        List<ResHostProvisionModel> resourceOverallOnVMware = this.resService.getResourceOverallOnVMware(
                resVm.getCloudEnvId(), resVm.getRegion());

        long resPoolId = jsonNode.get("resPoolId").asLong();
        this.resService.pickUpResourceOnVMware(resourceOverallOnVMware, resPoolId, vmCreate, jsonNode);

        //计算后，重新设置
        vmClone.setRegion(vmCreate.getRegion());
        vmClone.setFolder(vmCreate.getFolder());
        vmClone.setSysDiskLocation(vmCreate.getSysDiskLocation());
        vmClone.setSysDiskUrn(vmCreate.getSysDiskUrn());
        vmClone.setHostName(vmCreate.getHostName());
        vmClone.setNics(vmCreate.getNics());

        vmClone.setSrcVmUri(originHost.getUri());
        vmClone.setHostUrn(vmCreate.getHostUrn());
        vmClone.setClusterUrn(vmCreate.getClusterUrn());
        return vmClone;
    }

    private VmCreate makePowerVcMQBean(ResVm resVm, VmCreate vmCreate) {
        logger.debug("makePowerVcMQBean--->>");
        JsonNode jsonNode = JsonUtil.fromJson(resVm.getOriginParam());
        vmCreate.setId(resVm.getId());
        vmCreate.setName(resVm.getInstanceName());
        vmCreate.setVmType(resVm.getInstanceType());

        setNicInfo(vmCreate, resVm);
        vmCreate.setFloatingIpPool(resVm.getFloatingIpPoolId());

        vmCreate.setImage(resVm.getImageId());
        vmCreate.setAdminName(resVm.getManagementAccount());
        vmCreate.setAdminPass(resVm.getManagemenPassword());

        vmCreate.setRegion(resVm.getRegion());
        vmCreate.setAvailabilityZone(resVm.getZone());

        CloudKeyPair keyPair = this.cloudKeyPairMapper.selectByPrimaryKey(resVm.getKeypairId());
        AssertUtil.requireNonBlank(keyPair, "密钥不能为空");
        vmCreate.setKeypair(new KeyVo() {{
            setName(keyPair.getKeypairName());
            setPublicKey(keyPair.getPublicKey());
            setFingerprint(keyPair.getFingerprint());
            setPrivateKey(keyPair.getPrivateKey());
        }});

        // setSgInfo
        List<ResVmExt> resVmExts = resVm.getCloudHostExt().get(ResVmExtEnum.SECURITY_GROUP.getType());
        if (!CollectionUtils.isEmpty(resVmExts)) {
            ResSecurityGroup resSecurityGroup = resSecurityGroupService.selectByPrimaryKey(
                    Long.parseLong(resVmExts.get(0).getResourceId()));
            vmCreate.setSecurityGroupNames(Sets.newHashSet(resSecurityGroup.getName()));
        }

        // 数据盘
        JsonNode dataDisks = jsonNode.get("dataDisk");
        if (dataDisks != null && dataDisks.elements().hasNext()) {
            List<VmDisk> disks = new ArrayList<>();
            dataDisks.elements().forEachRemaining(dataDisk -> {
                VmDisk vmDisk = new VmDisk();
                vmDisk.setSize(dataDisk.get("diskSize").asText());
                if (Objects.nonNull(dataDisk.get("diskDeleteWithInstance"))) {
                    vmDisk.setDeleteWithInstance(dataDisk.get("diskDeleteWithInstance").asBoolean());
                } else {
                    vmDisk.setDeleteWithInstance(true);
                }
                disks.add(vmDisk);
            });
            vmCreate.setDisks(disks);
        }
        return vmCreate;
    }

    private VmCreate makeMaasMQBean(ResVm resVm, VmCreate vmCreate) {
        JsonNode jsonNode = JsonUtil.fromJson(resVm.getOriginParam());
        vmCreate.setId(resVm.getId());
        vmCreate.setName(resVm.getInstanceName());
        vmCreate.setVmType(resVm.getInstanceType());
        vmCreate.setImage(resVm.getImageId());
        vmCreate.setAdminName(resVm.getManagementAccount());
        vmCreate.setAdminPass(resVm.getManagemenPassword());
        vmCreate.setRegion(resVm.getRegion());
        vmCreate.setAvailabilityZone(resVm.getZone());

        CloudKeyPair keyPair = this.cloudKeyPairMapper.selectByPrimaryKey(resVm.getKeypairId());
        AssertUtil.requireNonBlank(keyPair, "密钥不能为空");
        vmCreate.setKeypair(new KeyVo() {{
            setName(keyPair.getKeypairName());
            setPublicKey(keyPair.getPublicKey());
            setFingerprint(keyPair.getFingerprint());
            setPrivateKey(keyPair.getPrivateKey());
        }});

        // 网络
        JsonNode networks = jsonNode.get("networks");
        if (Objects.nonNull(networks) && networks.elements().hasNext()) {
            JsonNode next = networks.elements().next();
            VmNic vmNic = new VmNic();
            ResVpc vpc = resNetVpcMapper.selectByPrimaryKey(Long.parseLong(next.get("networkId").asText()));
            vmNic.setNetId(vpc.getUuid());
            Network network = networkMapper.selectByPrimaryKey(Long.parseLong(next.get("subnetId").asText()));
            vmNic.setSubnetId(network.getUuid());
            vmCreate.setNics(Collections.singletonList(vmNic));
        }
        // 网卡
        if (Objects.nonNull(jsonNode.findValue("interfacesJson"))) {
            String interfacesJson = jsonNode.findValue("interfacesJson").textValue();
            List<InterfaceItemModel> interfaceItemModels = JsonUtil.toListObject(
                    interfacesJson, InterfaceItemModel.class);
            List<VmNic> vmNics = new ArrayList<>();
            for (InterfaceItemModel interfaceItemModel : interfaceItemModels) {
                VmNic vmNic = new VmNic();
                vmNic.setInterfaceId(interfaceItemModel.getInterfaceUuid());
                vmNic.setInterfaceName(Objects.requireNonNull(interfaceItemModel.getInterfaceName(), "网卡名称为空."));
                vmNic.setNetId(interfaceItemModel.getNetworkUuid());
                vmNic.setSubnetId(interfaceItemModel.getSubnetUuid());
                if (StringUtils.equalsIgnoreCase("eno1", interfaceItemModel.getInterfaceName())) {
                    vmNic.setPrivateIp(resVm.getInnerIp());
                }
                vmNics.add(vmNic);
            }
            vmCreate.setNics(vmNics);
        } else if (Objects.nonNull(jsonNode.findValue("interfaces"))) {
            JsonNode interfaces = jsonNode.findValue("interfaces");
            List<VmNic> vmNics = new ArrayList<>();
            for (Iterator<JsonNode> elements = interfaces.elements(); elements.hasNext(); ) {
                VmNic vmNic = new VmNic();
                JsonNode next = elements.next();
                vmNic.setInterfaceId(next.get("interfaceUuid").textValue());
                vmNic.setInterfaceName(next.get("interfaceName").textValue());
                if (next.get("networkUuid") != null && StringUtil.isNotBlank(next.get("networkUuid").textValue())
                        && next.get("subnetUuid") != null && StringUtil.isNotBlank(
                        next.get("subnetUuid").textValue())) {
                    vmNic.setNetId(next.get("networkUuid").textValue());
                    vmNic.setSubnetId(next.get("subnetUuid").textValue());
                }
                vmNics.add(vmNic);
            }
            vmCreate.setNics(vmNics);
        }
        AssertUtil.requireNonBlank(resVm.getPhysicalHostPoolId(), "未找到对应的资源池数据.");
        CloudPhysicalHostPool cloudPhysicalHostPool = this.cloudPhysicalHostPoolMapper.selectByPrimaryKey(
                resVm.getPhysicalHostPoolId());
        AssertUtil.requireNonBlank(cloudPhysicalHostPool, "物理机未找到.");
        CloudPhysicalHostPool updatePhysical = new CloudPhysicalHostPool();
        updatePhysical.setId(cloudPhysicalHostPool.getId());
        updatePhysical.setStatusInfo("");
        this.cloudPhysicalHostPoolMapper.updateByPrimaryKeySelective(updatePhysical);

        // 更新主机
        if (cloudPhysicalHostPool.getCpu() != null && cloudPhysicalHostPool.getMemory() != null) {
            ResVm updateHost = new ResVm();
            updateHost.setId(resVm.getId());
            updateHost.setCpu(cloudPhysicalHostPool.getCpu());
            updateHost.setMemory(cloudPhysicalHostPool.getMemory());
            resVmMapper.updateByPrimaryKeySelective(updateHost);
        }

        //set maas params
        vmCreate.setMaas(convertMaasVm(cloudPhysicalHostPool));
        vmCreate.setPhysicalHostPoolId(resVm.getPhysicalHostPoolId());
        return vmCreate;
    }

    private MaasVm convertMaasVm(CloudPhysicalHostPool cloudPhysicalHostPool) {
        MaasVm maasVm = new MaasVm();
        maasVm.setMacAddress(Objects.requireNonNull(cloudPhysicalHostPool.getMacAddress(), "mac地址不能为空."));
        maasVm.setPowerType(cloudPhysicalHostPool.getPowerType().toLowerCase());
        maasVm.setUuid(cloudPhysicalHostPool.getPhysicalUUID());
        // 电源参数
        PhysicalHostPowerAttrVO physicalHostPowerAttrVO = PhysicalHostPowerAttrVO.toVO(
                cloudPhysicalHostPool.getPowerAttrData());
        maasVm.setPowerAddress(physicalHostPowerAttrVO.getPowerAddress());
        maasVm.setPowerUser(physicalHostPowerAttrVO.getPowerUser());
        maasVm.setPowerPassword(physicalHostPowerAttrVO.getPowerPass());
        return maasVm;
    }

    /**
     * 构造启动AWS实例的MQBean
     */
    private VmCreate makeAwsMQBean(ResVm resVm, VmCreate vmCreate) {
        JsonNode jsonNode = JsonUtil.fromJson(resVm.getOriginParam());
        vmCreate.setId(resVm.getId());
        vmCreate.setName(resVm.getInstanceName());
        // 实例类型
        vmCreate.setVmType(resVm.getInstanceType());

        // 网络
        setNicInfo(vmCreate, resVm);
        JsonNode networks = jsonNode.get("networks").elements().next();

        if (networks.get("hasPublicNet") != null && !networks.get("hasPublicNet").isNull()) {
            vmCreate.setHasPublicNet(networks.get("hasPublicNet").booleanValue());
        } else {
            vmCreate.setHasPublicNet(false);
        }

        vmCreate.setImage(resVm.getImageId());
        vmCreate.setAdminName(resVm.getManagementAccount());
        vmCreate.setAdminPass(resVm.getManagemenPassword());

        vmCreate.setRegion(resVm.getRegion());
        vmCreate.setAvailabilityZone(resVm.getZone());

        // 密钥
        CloudKeyPair keyPair = this.cloudKeyPairMapper.selectByPrimaryKey(resVm.getKeypairId());
        AssertUtil.requireNonBlank(keyPair, "该密钥未找到.");
        vmCreate.setKeypair(new KeyVo() {{
            setName(keyPair.getKeypairName());
            setPublicKey(keyPair.getPublicKey());
            setFingerprint(keyPair.getFingerprint());
            setPrivateKey(keyPair.getPrivateKey());
        }});

        // 安全组
        setSgInfo(vmCreate, resVm);
        // 系统盘
        vmCreate.setSysDiskSize(jsonNode.get("systemDisk").get("diskSize").asText());
        vmCreate.setSystemDiskCategory(jsonNode.get("systemDisk").get("diskCategory").asText());
        vmCreate.setSystemDiskDeleteWithInstance(jsonNode.get("systemDisk").get("diskDeleteWithInstance").asBoolean());

        // 数据盘
        JsonNode dataDisks = jsonNode.get("dataDisk");
        if (dataDisks != null && dataDisks.elements().hasNext()) {
            List<VmDisk> disks = new ArrayList<>();
            dataDisks.elements().forEachRemaining(dataDisk -> {
                VmDisk vmDisk = new VmDisk();
                vmDisk.setSize(dataDisk.get("diskSize").asText());
                vmDisk.setType(dataDisk.get("diskCategory").asText());
                if (Objects.nonNull(dataDisk.get("diskDeleteWithInstance"))) {
                    vmDisk.setDeleteWithInstance(dataDisk.get("diskDeleteWithInstance").asBoolean());
                } else {
                    vmDisk.setDeleteWithInstance(true);
                }

                disks.add(vmDisk);
            });
            vmCreate.setDisks(disks);
        }
        return vmCreate;
    }

    private void setSgInfo(VmCreate vmCreate, ResVm resVm) {

        if (Objects.isNull(resVm) || Objects.isNull(resVm.getCloudHostExt())) {
            return;
        }

        List<ResVmExt> resVmExts = resVm.getCloudHostExt().get(ResVmExtEnum.SECURITY_GROUP.getType());
        if (!CollectionUtils.isEmpty(resVmExts)) {

            ResSecurityGroup resSecurityGroup = resSecurityGroupService.selectByPrimaryKey(
                    Long.parseLong(resVmExts.get(0).getResourceId()));

            resSecurityGroupService.assertSecurityGroupNonNull(resSecurityGroup);
            vmCreate.setSecurityGroupNames(Sets.newHashSet(resSecurityGroup.getUuid()));
        }
    }

    /**
     * 构造启动AWS实例的MQBean
     */
    private VmCreate makeAzureMQBean(ResVm resVm, VmCreate vmCreate) {
        JsonNode jsonNode = JsonUtil.fromJson(resVm.getOriginParam());
        vmCreate.setId(resVm.getId());
        vmCreate.setName(resVm.getInstanceName());
        // 实例类型
        vmCreate.setVmType(resVm.getInstanceType());

        // 网络
        setNicInfo(vmCreate, resVm, CloudEnvType.AZURE);

        Long imageId = jsonNode.get("instance").get("imageId").asLong();
        ResImage image = resImageMapper.selectByPrimaryKey(imageId);
        if (image == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1588245962));
        }
        vmCreate.setImage(image.getUrn());
        vmCreate.setOsCategory(image.getOsType());
        vmCreate.setAdminName(resVm.getManagementAccount());
        vmCreate.setAdminPass(resVm.getManagemenPassword());

        vmCreate.setRegion(resVm.getRegion());
        vmCreate.setAvailabilityZone(resVm.getZone());

        if (!Objects.equals(resVm.getRemoteLoginType(), RemoteLoginType.BY_PASS)) {
            // 密钥
            CloudKeyPair keyPair = this.cloudKeyPairMapper.selectByPrimaryKey(resVm.getKeypairId());
            vmCreate.setKeypair(new KeyVo() {{
                setName(keyPair.getKeypairName());
                setPublicKey(keyPair.getPublicKey());
                setFingerprint(keyPair.getFingerprint());
                setPrivateKey(keyPair.getPrivateKey());
            }});
        }
        CloudEnv cloudEnv = cloudEnvMapper.selectByPrimaryKey(resVm.getCloudEnvId());
        String cloudEnvType = cloudEnv.getCloudEnvType();
        if (CloudEnvType.AZURE.equals(cloudEnvType)) {
            JsonNode azureOtherInfo = jsonNode.findValue(cloudEnvType);
            if (azureOtherInfo != null && !azureOtherInfo.isNull()) {
                //资源组
                vmCreate.setResourceGroupName(azureOtherInfo.get("resourceGroupName").asText());
            }
        }

        // 安全组
        setSgInfo(vmCreate, resVm);
        // 系统盘
        String systemDiskCategory = jsonNode.get("systemDisk").get("diskCategory").asText();
        vmCreate.setSystemDiskCategory(systemDiskCategory);
        return vmCreate;
    }

    private VmCreate makeCloudMQBean(ResVm resVm, VmCreate vmCreate) {
        JsonNode jsonNode = JsonUtil.fromJson(resVm.getOriginParam());
        vmCreate.setName(resVm.getInstanceName());
        vmCreate.setResVmName(resVm.getHostName());
        if (RemoteLoginType.BY_KEY.equals(resVm.getRemoteLoginType())) {
            CloudKeyPair keyPair = this.cloudKeyPairMapper.selectByPrimaryKey(resVm.getKeypairId());
            vmCreate.setKeypair(new KeyVo() {{
                setName(keyPair.getKeypairName());
                setPublicKey(keyPair.getPublicKey());
                setFingerprint(keyPair.getFingerprint());
                setPrivateKey(keyPair.getPrivateKey());
            }});
            resVm.setManagemenPassword(CrytoUtilSimple.decrypt(resVm.getManagemenPassword(), true));
            vmCreate.setAdminPass(vmCreate.getManagementPassword());
        } else {
            vmCreate.setAdminPass(resVm.getManagemenPassword());
        }
        vmCreate.setAvailabilityZone(resVm.getZone());
        if (Objects.nonNull(jsonNode.get("period"))) {
            vmCreate.setPeriod(jsonNode.get("period").intValue());
        }
        setSgInfo(vmCreate, resVm);
        vmCreate.setImage(resVm.getImageId());
        vmCreate.setVmType(resVm.getInstanceType());
        vmCreate.setOsCategory(resVm.getOsCategory());
        vmCreate.setInstanceChargeType(resVm.getInstanceChargeType());

        // system disk
        final JsonNode systemDisk = jsonNode.findValue("systemDisk");
        vmCreate.setSysDiskSize(systemDisk.get("diskSize").asText());
        vmCreate.setSystemDiskCategory(systemDisk.get("diskCategory").textValue());

        // data disk
        // 数据盘
        JsonNode dataDisks = jsonNode.get("dataDisk");
        if (dataDisks != null && dataDisks.elements().hasNext()) {
            List<VmDisk> disks = new ArrayList<>();
            dataDisks.elements().forEachRemaining(dataDisk -> {
                VmDisk vmDisk = new VmDisk();
                vmDisk.setSize(dataDisk.get("diskSize").asText());
                vmDisk.setType(dataDisk.get("diskCategory").asText());
                if (Objects.nonNull(dataDisk.get("diskDeleteWithInstance"))) {
                    vmDisk.setDeleteWithInstance(dataDisk.get("diskDeleteWithInstance").asBoolean());
                } else {
                    vmDisk.setDeleteWithInstance(true);
                }

                disks.add(vmDisk);
            });
            vmCreate.setDisks(disks);
        }

        // vpc网络设置，交换机
        // 网络
        JsonNode networks = jsonNode.get("networks");
        if (Objects.nonNull(networks) && !"null".equals(networks.asText())) {
            JsonNode next = networks.elements().next();
            VmNic vmNic = new VmNic();
            ResVpc vpc = resNetVpcMapper.selectByPrimaryKey(Long.parseLong(next.get("networkId").asText()));
            vmNic.setNetId(vpc.getUuid());
            Network network = networkMapper.selectByPrimaryKey(Long.parseLong(next.get("subnetId").asText()));
            vmNic.setSubnetId(network.getUuid());
            vmCreate.setNics(Collections.singletonList(vmNic));
            if ("vpc".equals(next.get("networkType").textValue())) {
                vmCreate.setvSwitchId(network.getUuid());
            }

            if (Objects.nonNull(next.get("hasPublicNet")) && !next.get("hasPublicNet").isNull()) {
                vmCreate.setHasPublicNet(next.get("hasPublicNet").booleanValue());
            }

            if (Objects.nonNull(next.get("floatingIpId")) && !next.get("floatingIpId").isNull()) {
                ResFloatingIp floatingIp = resFloatingIpMapper.selectByPrimaryKey(next.get("floatingIpId").asLong());
                vmCreate.setFloatingIpId(floatingIp.getUuid());
            }

            // 虚机亲和性
            if (Objects.nonNull(jsonNode.get("serverGroupId")) && !next.get("serverGroupId").isNull()) {
                ResServerGroup resServerGroup = resServerGroupService.selectByPrimaryKey(
                        next.get("serverGroupId").asLong());
                vmCreate.setServerGroup(resServerGroup.getUuid());
            }
        }
        //安全组
        if (CloudEnvType.QCLOUD.equals(vmCreate.getProviderType())) {
            setSgInfo(vmCreate, resVm);
        }
        vmCreate.setId(resVm.getId());
        vmCreate.setIoOptimized(resVm.getIoOptimized());
        vmCreate.setInternetMaxBandwidthOut(resVm.getInternetMaxBandwidthOut());
        vmCreate.setInternetChargeType(resVm.getInternetChargeType());

        return vmCreate;
    }

    /**
     * 根据镜像猜测系统类型
     */
    private String parseOSType(ResVm resVm) {
        String osType = OsType.LINUX;

        // 查询镜像
        Criteria criteria = new Criteria();
        criteria.put("imageId", resVm.getImageId());
        List<ResImage> images = resImageService.selectByExample(criteria);
        Optional<ResImage> opt = images.stream()
                                       .filter(ri -> !ResImageStatus.NOT_CONFIG.equalsIgnoreCase(ri.getStatus()))
                                       .findFirst();
        if (!opt.isPresent()) {
            logger.warn("实例:[{}] 的镜像:{} 未找到配置的镜像", resVm.getInstanceName(), resVm.getImageId());
            return osType;
        }

        ResImage image = opt.get();

        if (OsType.WINDOWS.equalsIgnoreCase(image.getOsType()) || StringUtils.containsIgnoreCase(image.getOsPlatform(),
                                                                                                 OsType.WINDOWS)) {
            if (StringUtils.containsIgnoreCase(image.getOsPlatform(), WindowsType.WIN_2003)) {
                osType = "win2003";
            } else if (StringUtils.containsIgnoreCase(image.getOsPlatform(), WindowsType.WIN_2008)) {
                osType = "win2008";
            } else if (StringUtils.containsIgnoreCase(image.getOsPlatform(), WindowsType.WIN_2012)) {
                osType = "win2012";
            } else if (StringUtils.containsIgnoreCase(image.getOsPlatform(), WindowsType.WIN_2016)) {
                osType = "win2016";
            } else {
                // 不设置会下发linux
                osType = OsType.WINDOWS;
            }
        } else {
            osType = "linux";
        }

        return osType;
    }

    /**
     * 根据osName猜测系统类型
     */
    private String parseOSTypeByOsName(String osName) {
        String osType = "";

        if (StringUtil.isEmpty(osName)) {
            return osType;
        }

        if (StringUtils.containsIgnoreCase(osName, OsType.WINDOWS)) {
            if (StringUtils.containsIgnoreCase(osName, WindowsType.WIN_2003)) {
                osType = "win2003";
            } else if (StringUtils.containsIgnoreCase(osName, WindowsType.WIN_2008)) {
                osType = "win2008";
            } else if (StringUtils.containsIgnoreCase(osName, WindowsType.WIN_2012)) {
                osType = "win2012";
            } else if (StringUtils.containsIgnoreCase(osName, WindowsType.WIN_2016)) {
                osType = "win2016";
            }
        } else {
            osType = "linux";
        }

        return osType;
    }

    /**
     * 发送创建VM的MQ消息
     *
     * @param base the vm create
     *
     * @return the res inst result
     */
    private ResInstResult sendToMQ(Base base) {
        ResInstResult result;
        try {
            MQHelper.sendMessage(base);
            result = new ResInstResult(ResInstResult.SUCCESS);
        } catch (Exception e) {
            result = new ResInstResult(ResInstResult.FAILURE, e.getMessage());
        }
        return result;
    }

    private int sumDiskSize(JsonNode params) {
        int storeSum = 0;
        // 系统盘
        JsonNode systemDisk = params.get("systemDisk");
        if (systemDisk != null && systemDisk.get("systemDiskSize") != null) {
            storeSum += systemDisk.get("systemDiskSize").asLong();
        }

        // 数据盘
        JsonNode dataDisks = params.get("dataDisk");
        if (dataDisks != null && dataDisks.elements().hasNext()) {
            for (Iterator<JsonNode> iterator = dataDisks.elements(); iterator.hasNext(); ) {
                JsonNode dataDisk = iterator.next();
                if (dataDisk.get("dataDiskSize") != null) {
                    storeSum += dataDisk.get("dataDiskSize").asInt();
                }

            }

        }
        return storeSum;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createElasticServer(Long elasticGroupId, Long deploymentId, Long serverTemplateId,
                                      CloudCommonInst cloudCommonInst, List<Long> strategyIds, Long companyId,
                                      Long projectId) {
        final JsonNode jsonNode = cloudCommonInst.getCloudSpec();
        ResVm resVm = createInstance(jsonNode);
        resVm.setStatus(ResVmStatus.PENDING);
        resVm.setElasticGroupId(elasticGroupId);
        resVm.setOwnerId(cloudCommonInst.getUserId());
        if (!StringUtil.isNullOrEmpty(projectId)) {
            resVm.setOrgSid(projectId);
        }
        resVm.setOriginParam(JsonUtil.toJson(jsonNode));
        resVm.setManageStatus(ResVmManageStatus.UNUNITED);
        resVm.setServerType(ServerType.ELASTIC);
        resVm.setCloudDeploymentId(deploymentId);
        resVm.setServerTemplateId(serverTemplateId);
        resVm.setOrgSid(companyId);
        BasicWebUtil.prepareInsertParams(resVm, cloudCommonInst.getUserAccount());
        int result = this.resVmMapper.insertSelective(resVm);
        resVmExtService.insertResVmExt(resVm);
        //预留弹性IP
        reservedFloatingIp(resVm);

        if (1 == result) {
            // 设置实例的告警策略
            setServerTemplateInfo(resVm, companyId, serverTemplateId, deploymentId, strategyIds);
        }
        return resVm.getId();
    }

    private void reservedFloatingIp(ResVm resVm) {
        if (!Strings.isNullOrEmpty(resVm.getPublicIp())) {
            ResFloatingIp resFloatingIp = new ResFloatingIp();
            resFloatingIp.setStatus(NetworkStatus.RESERVED);
            resFloatingIp.setInstanceId(resVm.getId());
            resFloatingIp.setInstanceName(resVm.getInstanceName());
            Criteria criteria = new Criteria();
            criteria.put("cloudEnvId", resVm.getCloudEnvId());
            criteria.put("ip", resVm.getPublicIp());
            criteria.put("statusNotEquals", NetworkStatus.DELETED);
            resFloatingIpMapper.updateByParamsSelective(resFloatingIp, criteria.getCondition());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void increaseElasticInstance(ResVm elasticServer, Long elasticHistoryId, int increaseNum) {
        Criteria criteria = new Criteria("elasticGroupId", elasticServer.getElasticGroupId());
        criteria.put("serverType", ServerType.ELASTIC_INSTANCE);
        List<ResVm> resVms = this.resVmMapper.selectBaseByParams(criteria);
        OptionalInt maxIndex = resVms.stream().mapToInt(resVm -> {
            String[] tempName = resVm.getInstanceName().split("_");
            return Integer.parseInt(tempName[tempName.length - 1]);
        }).max();
        int startIndex = 0;
        if (maxIndex.isPresent()) {
            startIndex = maxIndex.getAsInt();
        }

        for (int i = 0; i < increaseNum; i++) {
            startIndex++;
            String index = String.valueOf(startIndex);
            // 复制主机
            ResVm resVm = new ResVm();
            BeanUtils.copyProperties(elasticServer, resVm);
            String instanceName = resVm.getInstanceName();
            // name number #1, #2
            if (instanceName.lastIndexOf('_') > 0) {
                String[] tempName = instanceName.split("_");
                tempName[tempName.length - 1] = index;
                instanceName = Joiner.on("_").join(tempName);
            } else {
                instanceName = instanceName + "_" + index;
            }
            resVm.setInstanceName(instanceName);
            resVm.setId(null);
            resVm.setServerType(ServerType.ELASTIC_INSTANCE);
            resVm.setManageStatus(ResVmManageStatus.UNUNITED);
            resVm.setStatus(ResVmStatus.PENDING);
            resVm.setElasticHistoryId(elasticHistoryId);
            BasicWebUtil.prepareInsertParams(resVm, elasticServer.getCreatedBy());
            this.resVmMapper.insertSelective(resVm);

            this.basicMonitorRemoteService.copyResAllAlarmRuleTargetToNew(elasticServer.getId(), resVm.getId(),
                                                                          ResourceType.RES_VM);

            // endregion

            // region 设置服务器脚本
            // 1.获取脚本
            Long serverTemplateId = elasticServer.getServerTemplateId();

            Long deploymentId = elasticServer.getCloudDeploymentId();
            // 2.查询当前部署中所有的脚本
            criteria.clear();
            criteria.put("deploymentId", deploymentId);
            // endregion
            // 创建虚拟机
            this.reCreateResVm(resVm.getId());

        }
    }

    @Override
    public int decreaseElasticInstance(Long elasticGroupId, Long elasticHistoryId, int decreaseNum) {
        Criteria criteria = new Criteria("elasticGroupId", elasticGroupId);
        criteria.put("serverType", ServerType.ELASTIC_INSTANCE);
        List<ResVm> resVms = this.resVmMapper.selectBaseByParams(criteria);
        List<ResVm> standByResVms = new ArrayList<>();
        List<String> statusIn = Arrays.asList(DeployTaskStatus.RUNNING, DeployTaskStatus.PENDING);
        for (ResVm resVm : resVms) {
            List<DeployTask> deployTasks = this.basicDeployTaskService.findAllByTargetAndStatusIn(resVm.getId(),
                                                                                                  statusIn);
            if (CollectionUtils.isEmpty(deployTasks)) {
                standByResVms.add(resVm);
            }
        }
        // 再次确定可以缩减的主机
        int num = Math.min(standByResVms.size(), decreaseNum);
        for (int i = 0; i < num; i++) {
            // 更新为此次的historyID
            ResVm resVm = new ResVm();
            resVm.setId(standByResVms.get(i).getId());
            resVm.setElasticHistoryId(elasticHistoryId);
            resVmMapper.updateByPrimaryKeySelective(resVm);
            removeCloudInstance(standByResVms.get(i).getId(), true, true);
        }
        return num;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeCloudInstance(String hostId, boolean deleteAll, boolean deleteFloatingIp) {
        checkVmCanBeRemove(hostId);
        ResVmDeleteDTO resVmDeleteDTO = new ResVmDeleteDTO();
        resVmDeleteDTO.setHostId(hostId);
        resVmDeleteDTO.setDeleteAll(deleteAll);
        resVmDeleteDTO.setDeleteFloatingIp(deleteFloatingIp);
        resVmDeleteDTO.setOrgSid(BasicInfoUtil.getCurrentOrgSid());
        resVmDeleteDTO.setOpUser(BasicInfoUtil.getCurrentUserName(AuthConstants.SYS_USER_ACCOUNT));
        this.removeCloudInstance(resVmDeleteDTO);
    }

    @Override
    @Async("cloudExecutor")
    @Transactional(rollbackFor = Exception.class)
    public void removeCloudInstance(ResVmDeleteDTO resVmDeleteDTO) {
        String hostId = resVmDeleteDTO.getHostId();
        Boolean deleteAll = resVmDeleteDTO.getDeleteAll();
        String opUser = resVmDeleteDTO.getOpUser();

        ResVm resVm = this.resVmMapper.selectByPrimaryKey(hostId);
        if (resVm == null) {
            logger.warn("Remove Cloud Instance, Host [{}] has been removed.", hostId);
            return;
        }

        //自服务创建的云主机状态设置为已删除
        serviceInstTargetRemoteService.updateSelfInstByHostId(resVm.getId(), SelfServiceInstanceStatus.DELETED);

        //start send delete host
        List<Long> userSids = businessNotificationRemoteService.findUserToNotify(resVm.getOwnerId(),
                                                                                 resVm.getCreatedBy(),
                                                                                 resVm.getOrgSid());
        Long ownerId = userSids.stream().findFirst().get();

        if (Strings.isNullOrEmpty(resVm.getInstanceId())) {
            logger.info("Remove Cloud Instance -> remove from db only cause instance id is null.");
            removeInstanceFromDB(resVm.getId(), false, false, opUser);
            // 更新用户Id推送状态，客户端重新拉取任务状态
            ServerMsgPublisher.sendMsg("/topic/task/" + ownerId, "removeHost");
        } else if (deleteAll) {
            logger.info("Remove Cloud Instance, remove from db and platform. host id is [{}]", hostId);
            // bug 20391 清空回收站提示云环境不存在无法清空
            CloudEnv cloudEnv = cloudEnvMapper.selectByPrimaryKey(resVm.getCloudEnvId());
            if (cloudEnv == null) {
                // 资源环境被删除，只从平台中移除主机
                removeInstanceFromDB(hostId, true, true, opUser);
                return;
            }
            // 同时从云环境中移除主机
            VmRemove vmRemove = CloudClientFactory.buildMQBean(resVm.getCloudEnvId(), VmRemove.class);
            if (vmRemove == null) {
                // 资源环境被删除，只从平台中移除主机
                removeInstanceFromDB(hostId, true, true, opUser);
                return;
            }
            resVm.setStatus(ResVmStatus.DELETING);
            this.resVmMapper.updateByPrimaryKeySelective(resVm);
            // cancel deploy task
            this.basicDeployTaskService.cancelTaskByHostNotEnd(resVm.getId());

            //移除实例任务
            Map<String, Object> taskDetail = Maps.newLinkedHashMap();
            taskDetail.put("deleteFloatingIp", resVmDeleteDTO.getDeleteFloatingIp());
            taskDetail.put("orgSid", resVmDeleteDTO.getOrgSid());
            taskDetail.put("opUser", resVmDeleteDTO.getOpUser());
            DeployTask deployTask = this.basicDeployTaskService.removeResVmTask(resVm, taskDetail);

            deployTask.setStatus(DeployTaskStatus.PENDING);
            deployTask.setStatusName(basicCodeService.getTaskStatusName(deployTask.getStatus()));
            deployTask.setStartDate(null);
            deployTask.setUserId(String.valueOf(ownerId));
        } else {
            // 只从平台中移除主机
            logger.info("Remove Cloud Instance -> remove from db only.");
            removeInstanceFromDB(hostId, true, false, opUser);

            basicResActionLogService.insertIntoActionLog(resVm.getId(), opUser, ResourceTypeEnum.VM,
                                                         ResourceOperateEnum.DELETE, Boolean.TRUE);
        }

        if (resVm.getCloudEnvId() == null) {}

        //将正在执行的操作设置为失败
        Criteria example = new Criteria();
        example.put("resSid", resVm.getId());
        example.put("statusIsNull", resVm.getInstanceName());
        List<ResActionLog> logs = basicResActionLogService.selectByParams(example);
        if (logs != null && logs.size() > 0) {
            for (ResActionLog log : logs) {
                log.setStatus(Boolean.FALSE);
                basicResActionLogService.updateByPrimaryKeySelective(log);
            }
        }

        if (!CollectionUtils.isEmpty(userSids)) {
            Map<String, String> content = Maps.newHashMap();
            content.put("instanceName", resVm.getInstanceName());
            content.put("cloudEnvName", resVm.getCloudEnvName());
            userSids.forEach(userSid -> businessNotificationRemoteService.sendCommonPlatformNotification(userSid,
                                                                                                         MailTemplateConstants.CLOUD_HOST_DELETE_NOTIFI,
                                                                                                         content));
        }
    }

    /**
     * 检测主机是否能被删除
     **/
    @Override
    public void checkVmCanBeRemove(String hostId) {

        // check 磁盘快照
        checkDiskSnapshotExists(hostId);
    }

    @Override
    public Map<String, Object> getHostInfoMapByImage(ResImage resImage) {
        // 1.获取操作系统类别
        String osCategory = parseOsCategory(resImage);
        int sshPort = 22;
        int bit = 64;
        try {
            // 2.获取ssh端口（如果managementPort为空 查找该操作系统对应的默认管理端口）
            String managementPort = resImage.getManagementPort();
            if (StringUtil.isNotBlank(managementPort)) {
                sshPort = Integer.parseInt(managementPort);
            } else {
                String osType = resImage.getOsType();
                String configValue = this.basicSysConfigService.getValueByConfigKey(
                        "cloud.env.image.default.managerport." + StringUtil.upperFirst(osType));
                sshPort = StringUtil.isNotBlank(configValue) ? Integer.parseInt(configValue) : 22;
            }
            // 3.获取系统位数
            bit = resImage.getBit();
        } catch (Exception e) {
            logger.warn("从镜像获取实例相关信息：{}", e.getMessage());
        }
        Map<String, Object> result = Maps.newHashMap();
        result.put("sshPort", sshPort);
        result.put("osCategory", osCategory);
        result.put("bit", bit);
        return result;
    }

    private void checkDiskSnapshotExists(String resVmId) {
        Criteria hostId = new Criteria(RES_VM_ID, resVmId);
        hostId.put("statusNotEquals", ResVdStatus.DELETED);
        List<ResVd> resVds = this.resVdMapper.selectByParams(hostId);

        int snapCount = 0;
        if (!CollectionUtils.isEmpty(resVds)) {
            for (ResVd resVd : resVds) {
                Criteria condition = new Criteria();
                condition.put("cloudEnvId", resVd.getCloudEnvId());
                condition.put("resVdId", resVd.getResVdSid());
                condition.put("statusNotIn", Lists.newArrayList(SnapshotStatus.DELETED));
                snapCount = snapCount + this.resSnapshotMapper.countByParams(condition);
            }
        }

        if (snapCount > 0) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_667021700) + snapCount + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1678834046));
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeInstanceFromDB(@NonNull String resVmId, boolean removeAgent, boolean envNotExist, String opUser) {
        removeInstanceFromDB(resVmId, removeAgent, envNotExist, opUser, false);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeInstanceFromDB(@NonNull String resVmId, boolean removeAgent, boolean envNotExist, String opUser,
                                     boolean forceDeleteDisk) {
        logger.info("remove_instance_from_db -->> [{}]", resVmId);
        Date endTime = new Date();

        // delete from host container
        this.resVmContainerMapper.deleteByParams(new Criteria("resVmId", resVmId));
        Criteria hostId = new Criteria(RES_VM_ID, resVmId);
        hostId.put("statusNotEquals", ResVdStatus.DELETED);

        if (envNotExist) {
            //如果云环境不存在，直接从数据库中删除云硬盘
            resVdMapper.deleteByParams(hostId);
        } else {
            List<ResVd> resVds = this.resVdMapper.selectByParams(hostId);
            resVds.forEach(resVd -> {
                if (forceDeleteDisk || !StringUtils.equals(resVd.getReleaseMode(), ReleaseMode.STAND_ALONE)) {
                    this.deleteDisk(resVd, opUser, endTime);
                } else {
                    resVd.setStatus(ResVdStatus.NORMAL);
                    resVd.setStoragePurpose(StoragePurpose.DATA_DISK);
                    resVd.setResVmId(null);
                    resVdMapper.detachResVd(resVd);
                }
            });
        }

        // 删除主机的的快照
        this.deleteSnapshot(resVmId);

        // 同时删除实例附加信息
        resVmExtService.deleteByInstanceId(resVmId);

        // delete deploy apps instance
        List<AppInstSvc> instances = this.deployAppsInstanceService.selectByParams(new Criteria("resVmId", resVmId));
        if (!CollectionUtils.isEmpty(instances)) {
            DeployAppsInstanceEventParams deployAppsInstanceEventParams = new DeployAppsInstanceEventParams();
            for (AppInstSvc instance : instances) {
                deployAppsInstanceEventParams.setDeployInstanceId(instance.getId());
                this.deployAppsInstanceEventRemoteService.deleteByParams(deployAppsInstanceEventParams);
            }

            DeployAppsInstanceParams deployAppsInstanceParams = new DeployAppsInstanceParams();
            deployAppsInstanceParams.setResVmId(resVmId);
            this.deployAppsInstanceRemoteService.deleteByParams(deployAppsInstanceParams);
        }

        // 主机对应有服务实例 则需修改服务实例的状态
        ResVm resVm = resVmMapper.selectSimpleByPrimaryKeyForUpdate(resVmId);
        serviceInstTargetRemoteService.updateSelfInstByHostId(resVmId, SelfServiceInstanceStatus.DELETED);

        if (envNotExist) {
            //如果云环境不存在。直接删除机器
            resVmMapper.deleteByPrimaryKey(resVmId);
        } else {
            //插入资源操作历史表
            if (Objects.nonNull(resVm) && (ServerType.SERVER.equals(resVm.getServerType()) || ServerType.ELASTIC.equals(
                    resVm.getServerType()))) {
                this.resVmMapper.deleteByPrimaryKey(resVmId);
            } else {
                //更新状态为已删除
                ResVm vm = new ResVm();
                vm.setId(resVmId);
                vm.setEndTime(endTime);
                vm.setStatus(ResVmStatus.DELETED);
                BasicWebUtil.prepareUpdateParams(vm);
                this.resVmMapper.updateByPrimaryKeySelective(vm);
            }
        }

        // delete network
        NetworkIp networkIp = NetworkIp.builder().allocateTargetId(resVmId).status(NetworkManagement.AVAILABLE).build();
        this.networkIpMapper.updateStatusByAllocateTargetId(networkIp);

        // 删除主机关联的标签
        UnBindCloudTagRequest unBindCloudTagRequest = new UnBindCloudTagRequest();
        unBindCloudTagRequest.setObjId(resVmId);
        unBindCloudTagRequest.setObjType("host");
        cloudTagRemoteService.unBindRes(unBindCloudTagRequest);

        // 删除实例与网卡的关系表
        this.resVmNetcardMapper.deleteByParams(new Criteria("resVmId", resVmId));

        // 删除res_vpc_port数据
        List<ResVpcPort> resVpcPorts = resVpcPortMapper.selectByParams(
                new Criteria("device", resVmId).put("deviceOwnerLike", "compute:"));
        for (ResVpcPort item : resVpcPorts) {
            resVpcPortMapper.deleteByPrimaryKey(item.getId());
        }

        // 还原弹性IP状态
        revertFloatingIp(resVm);

        // 删除告警关联

        // delete agent alive key
        JedisUtil.instance().delHSet(MonitorInfoKey.CACHE_MONITOR_KEY, resVmId);
    }

    private void revertFloatingIp(ResVm resVm) {
        if (Objects.isNull(resVm)) {
            return;
        }
        Criteria example = new Criteria();
        example.put("cloudEnvId", resVm.getCloudEnvId());
        example.put("ip", resVm.getPublicIp());
        example.put("statusNotEquals", NetworkStatus.DELETED);
        List<ResFloatingIp> floatingIps = resFloatingIpMapper.selectByParams(example);
        if (!CollectionUtils.isEmpty(floatingIps)) {
            this.resFloatingIpMapper.unbandingFloatingIp(floatingIps.get(0).getId().intValue());
        }
    }

    @Override
    public int updateServerToDeployment(String resVmId, CloudCommonInst cloudCommonInst) {

        final JsonNode jsonNode = cloudCommonInst.getCloudSpec();
        ResVm resVm = createInstance(jsonNode);
        resVm.setId(resVmId);
        resVm.setOriginParam(JsonUtil.toJson(jsonNode));
        resVm.setStatus(null);
        BasicWebUtil.prepareUpdateParams(resVm);
        if (Strings.isNullOrEmpty(resVm.getPublicIp())) {
            resVmMapper.unbandingFloatingIp(resVmId);
            Criteria criteria = new Criteria();
            criteria.put("instanceId", resVmId);
            criteria.put("status", NetworkStatus.RESERVED);
            List<ResFloatingIp> floatingIps = resFloatingIpMapper.selectByParams(criteria);
            if (!CollectionUtils.isEmpty(floatingIps)) {
                resFloatingIpMapper.unbandingFloatingIp(floatingIps.get(0).getId().intValue());
            }
        }
        resVmExtService.deleteByInstanceId(resVmId);
        resVmExtService.insertResVmExt(resVm);

        return this.resVmMapper.updateByPrimaryKeySelective(resVm);
    }

    @Override
    public List<ResVd> selectDiskInHost(Criteria criteria) {
        return this.resVdMapper.selectByParams(criteria);
    }

    @Override
    public List<ResVm> selectByExample(Criteria criteria) {
        return this.resVmMapper.selectByExample(criteria);
    }

    @Override
    public List<ResVm> selectByExampleWithOutFilter(Criteria criteria) {
        return this.resVmMapper.selectByParamsWithoutFilter(criteria);
    }

    @Override
    public List<ResVm> selectBaseByParamWithoutDf(Criteria criteria) {
        return this.resVmMapper.selectBaseByParamWithoutDf(criteria);
    }

    @Override
    public List<ResVm> selectCanBeExecScriptHosts(Criteria criteria) {
        return this.resVmMapper.selectCanBeExecScriptHosts(criteria);
    }

    @Override
    public List<ResVd> findStatisticVDByHostIds(List<String> hostIds, List<Long> envIds, String dimension,
                                                String timeStoreWay) {
        if (envIds.isEmpty()) {
            envIds.add(-999L);
        }
        Criteria criteria = new Criteria();
        if (CollectionUtils.isEmpty(hostIds)) {
            return new ArrayList<>();
        }
        if ("GB".equals(dimension)) {
            criteria.put("hostIdList", hostIds);
        } else {
            criteria.put("envIdList", envIds);
        }

        criteria.put("statusNotEquals", ResVdStatus.DELETED);
        if ("day".equals(timeStoreWay)) {
            criteria.put("statisticEndTime", DateUtil.endOfTodDay(false));
        } else {
            criteria.put("statisticEndTime", DateUtil.endOfThisWeek(false));
        }
        return resVdMapper.selectByParams(criteria);
    }

    /**
     * 自动选择主机
     *
     * @param hostName hostName
     * @param imageSize imageSize
     *
     * @return {host: xxx, store: xxxx}
     */
    private Map<String, String> selectHostAndStore(List<Map<String, Object>> hostList, String hostName, Long cpu,
                                                   Long memory, Long imageSize, boolean useLocalStore) {
        long imageSizeGb = imageSize;
        String selectHost = hostName;
        String selectStore = null;
        if (Strings.isNullOrEmpty(selectHost)) {
            // 对CPU和内存使用率，由低到高的排序(使用原则为 mem分配升序，cpu分配升序的和排序)
            hostList.sort(Comparator.comparingDouble(val -> {
                Map<String, Object> propers = (Map<String, Object>) val.get("propers");
                return Double.parseDouble(propers.get("provisionCpu").toString());
            }));
            hostList.sort(Comparator.comparingDouble(val -> {
                Map<String, Object> propers = (Map<String, Object>) val.get("propers");
                return Double.parseDouble(propers.get("provisionMem").toString());
            }));

            List<Map<String, Object>> collect = hostList.stream().filter(val -> {
                Map<String, Object> propers = (Map<String, Object>) val.get("propers");
                int totalCpu = Integer.parseInt(propers.get("numCpuCores").toString()) * Integer.parseInt(
                        propers.get("cpuNumber").toString());
                Double totalMem = Double.parseDouble(propers.get("memMB").toString());
                return cpu <= totalCpu && memory <= totalMem;
            }).collect(Collectors.toList());

            if (collect.isEmpty()) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_58537174));
            }
            for (Map<String, Object> selHost : collect) {
                selectStore = selectDatastore(selHost, imageSizeGb, useLocalStore);
                if (!Strings.isNullOrEmpty(selectStore)) {
                    selectHost = selHost.get("hostName").toString();
                    break;
                }
            }
        } else {
            Map<String, Object> selHost = hostList.stream()
                                                  .filter(host -> hostName.equals(host.get("hostName").toString()))
                                                  .findFirst()
                                                  .get();

            selectStore = selectDatastore(selHost, imageSizeGb, useLocalStore);
        }
        if (Strings.isNullOrEmpty(selectStore)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_551289134));
        }
        return MapsKit.of("host", selectHost, "store", selectStore);
    }

    /**
     * 选择存储
     */
    private String selectDatastore(Map<String, Object> selHost, long diskSize, boolean useLocalStore) {
        String selectStore = null;
        List<Map<String, Object>> dataStore = (List<Map<String, Object>>) (((Map<String, Object>) selHost.get(
                "propers"))).get("dataStore");
        // sort free by desc
        dataStore.sort(Comparator.comparingInt(o -> 0 - Integer.parseInt(o.get("freeSize").toString())));
        Optional<Map<String, Object>> dataStoreOpt = null;
        dataStoreOpt = dataStore.stream()
                                .filter(x -> useLocalStore || !Boolean.parseBoolean(x.get("isLocal").toString()))
                                .filter(x -> diskSize < Integer.parseInt(x.get("freeSize").toString()))
                                .findFirst();
        // 判断是否有足够空间可以安装Image
        if (dataStoreOpt.isPresent()) {
            Map<String, Object> store = dataStoreOpt.get();
            selectStore = store.get("name").toString();
            // 剩余空间减去image size
            store.compute("freeSize", (s, o) -> Integer.parseInt(o.toString()) - diskSize);
        }
        return selectStore;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @SuppressWarnings("checkstyle:MagicNumber")
    public boolean importResVm(String account, String userSid, List<ResVm> resVms) {

        User user = this.userRemoteService.selectByPrimaryKey(Long.parseLong(userSid));

        for (ResVm resVm : resVms) {
            resVm.setManagemenPassword(CrytoUtilSimple.decrypt(resVm.getManagemenPassword(), true));
            BasicWebUtil.prepareInsertParams(resVm);
            resVm.setOwnerId(userSid);
            // 如果平台没有，则设置
            if (Strings.isNullOrEmpty(resVm.getPlatform())) {
                resVm.setPlatform("Ubuntu");
                resVm.setOsCategory("Linux");
            }
            // 如果平台没有，则设置
            if (Strings.isNullOrEmpty(resVm.getOsName())) {
                resVm.setOsName(null);
            }
            resVm.setOrgSid(BasicInfoUtil.getCurrentOrgSid());
            resVm.setStatus(ResVmStatus.RUNNING);
            resVm.setServerType(ServerType.INSTANCE);
            resVm.setManageStatus(ResVmManageStatus.UNUNITED);

            //  设定默认密码
            if (StringUtils.isBlank(resVm.getManagemenPassword())) {
                resVm.setManagemenPassword("admin");
            }
            boolean isInternalIp = IPUtil.internalIp(resVm.getPublicIp());
            if (isInternalIp) {
                resVm.setInnerIp(resVm.getPublicIp());
                resVm.setPublicIp(null);
            }
            resVm.setId(UuidUtil.getUuid().replace("-", ""));
            this.resVmMapper.insertSelective(resVm);

            basicResActionLogService.insertIntoActionLog(resVm.getId(), account, ResourceTypeEnum.VM,
                                                         ResourceOperateEnum.IMPORT, Boolean.TRUE);

            final String message = MessageUtil.getLogMessage("纳管实例中...");
            MongoUtil.save(new LogInfo(resVm.getId(), message), DeployConst.HOST_LOG_KEY_PREFIX);
            // deploy agent
            // setup task
            List<DeployTask> deployTasks = this.basicDeployTaskService.deployResVmTask(resVm, null);
            String deployTaskId = deployTasks.get(0).getId();

            // setup playbook
            DeployPlaybook deployPlaybook = this.basicDeployPlaybookService.setupPlaybook(deployTasks);
            HostDepoyEvent hostDepoyEvent = new HostDepoyEvent(resVm);
            hostDepoyEvent.setType(DeployTaskType.IMPORT_HOST);
            hostDepoyEvent.setTaskId(deployTaskId);
            hostDepoyEvent.setApiPath(AnsibleServerMethod.ControlApi.AGENT);
            SpringContextHolder.publishEvent(hostDepoyEvent);

            //start send import host
            if (!StringUtil.isNullOrEmpty(user)) {
                Map<String, String> content = Maps.newHashMap();
                content.put("instanceName", resVm.getInstanceName());
                content.put("id", resVm.getId());
                StringBuilder sb = new StringBuilder();
                if (!Strings.isNullOrEmpty(resVm.getPublicIp())) {
                    sb.append("(公)").append(resVm.getPublicIp());
                }
                if (!Strings.isNullOrEmpty(resVm.getInnerIp())) {
                    if (sb.length() > 0) {
                        sb.append("\n");
                    }
                    sb.append("(内)").append(resVm.getInnerIp());
                }
                content.put("ip", sb.toString());
                businessNotificationRemoteService.sendCommonPlatformNotification(user.getUserSid(),
                                                                                 NotificationConsts.CLOUD_HOST_IMPORT_NOTIFI,
                                                                                 content);
            }
        }

        return true;
    }

    @Override
    public List<ResVm> findByParam(Criteria criteria) {
        return this.resVmMapper.selectByExample(criteria);
    }

    @Override
    public List<ResVm> findByParamWithoutFilter(Criteria criteria) {
        return this.resVmMapper.selectByExampleWithoutFilter(criteria);
    }

    @Override
    public List<ResVm> findElasticVoterList(Criteria criteria) {
        return this.resVmMapper.selectVoterList(criteria);
    }

    @Override
    public List<ResVm> selectHostSimpleInfoByParams(Criteria example) {
        return this.resVmMapper.selectHostSimpleInfoByParams(example);
    }

    private Long getOtherOrgSid(Criteria criteria) {
        Object otherOrgSidObj = criteria.getCondition().get(WebConstants.Criteria.FILTER_OTHER_ORG_SID);

        Long otherOrgSid = null;
        if (otherOrgSidObj instanceof Long) {
            otherOrgSid = (Long) otherOrgSidObj;
        } else if (otherOrgSidObj instanceof String) {
            otherOrgSid = Long.valueOf(((String) otherOrgSidObj));
        }

        return otherOrgSid;
    }

    @Override
    public List<ResVm> selectHostByParamsByPermision(Criteria example) {
        // COUNT(*)过滤SQL准备
        String sqlFilter = "";
        Long otherOrgSid = getOtherOrgSid(example);
        if (Objects.nonNull(otherOrgSid)) {
            sqlFilter = BasicInfoUtil.getSQLFilterWithGivingOrgSid("A", "org_sid", otherOrgSid);
        } else {
            sqlFilter = BasicInfoUtil.getSQLFilter("A", "org_sid", "owner_id", false, null, null, true);
        }
        example.setSqlFilter(sqlFilter);

        // 对于OpenStack系查询时，使用对应的2个云环境类型
        dealWithEnvType(example);

        //查询组织用户下主机信息
        List<ResVm> resVms = this.resVmMapper.selectHostByParams(example);
        if (CollectionUtils.isEmpty(resVms)) {
            return Lists.newArrayListWithCapacity(0);
        }
        resVms.stream().forEach(vm -> {
            if (!StringUtil.isNullOrEmpty(vm.getDeleteResource()) && "not_destroy".equalsIgnoreCase(
                    vm.getDeleteResource())) {
                vm.setDeployId(null);
                vm.setServiceOrderId(null);
                vm.setServiceOrderSn(null);
            }
            if (ResVmStatus.REINSTALL_SYSTEM_FAILURE.equalsIgnoreCase(vm.getStatus())) {
                vm.setStatusName("重装失败");
            }
        });
        // 发布任务名称设置
        if (example.get("skipCloudDeploymentName") == null) {
            List<String> hostIds = resVms.stream().map(ResVm::getId).collect(Collectors.toList());
            List<Map> nameList = this.resVmMapper.selectCloudDeploymentName(hostIds);
            Map map = nameList.stream()
                              .collect(Collectors.toMap(m -> m.get("RES_VM_ID"), m -> m.get("CLOUD_DEPLOYMENT_NAME"),
                                                        (a, b) -> a));
            resVms.forEach(rv -> {
                rv.setCloudDeploymentName((String) map.get(rv.getId()));
            });
        }

        if (example.get("skipResVmExtInfo") == null) {
            setResVmExtInfo(resVms);
        }
        // 设置到期信息
        Map<String, ResourceExpire> expireMap = ResExpireNoticeUtil.setExpireNotice(resVms.stream()
                                                                                          .filter(resVm -> Objects.nonNull(
                                                                                                  resVm.getEndTime()))
                                                                                          .collect(Collectors.toMap(
                                                                                                  ResVm::getId,
                                                                                                  ResVm::getEndTime)),
                                                                                    NoticeResTypeEnum.VM);

        for (ResVm resVm : resVms) {
            resVm.setExpired(expireMap.getOrDefault(resVm.getId(), new ResourceExpire()).getExpired());
            resVm.setExpiredDay(expireMap.getOrDefault(resVm.getId(), new ResourceExpire()).getExpiredDay());
            //设置安全组字段
            if (resVm.getCloudHostExt() != null && resVm.getCloudHostExt().get("sg") != null) {
                resVm.setSecurityGroupName(resVm.getCloudHostExt()
                                                .get("sg")
                                                .stream()
                                                .map(ResVmExt::getSgName)
                                                .collect(Collectors.joining(",")));
            }
            if (CloudEnvType.HUAWEICLOUD.equals(resVm.getCloudEnvType()) && (
                    ResVmStatus.FAILURE.equalsIgnoreCase(resVm.getStatus())
                            || ResVmStatus.CREATE_FAILURE.equalsIgnoreCase(resVm.getStatus()))) {
                resVm.setPublicIp("");
                resVm.setInnerIp("");
            }
            setHasIndependentPersistVolume(resVm);

        }
        return resVms;
    }

    public void setHasIndependentPersistVolume(ResVm resVm) {
        if (resVm != null && CloudEnvType.FUSIONCOMPUTE.equals(resVm.getCloudEnvType())) {
            Criteria criteria = new Criteria();
            criteria.put("indep", true);
            criteria.put("persistent", true);
            criteria.put("resVmId", resVm.getId());
            criteria.put("statusNotIn", Arrays.asList(ResVdStatus.DELETED, ResVdStatus.FAILURE));

            resVm.setHasIndependentPersistVolume(resVdMapper.countByParamsNoDf(criteria) > 0);
        }
    }

    private void dealWithEnvType(Criteria example) {

        Object cloudEnvType = example.get("cloudEnvType");
        if (Objects.nonNull(cloudEnvType) && CloudEnvType.OPEN_STACK.equals(cloudEnvType.toString())) {
            // e.g. : ESCloud ==> (ESCloud, ESCloud-Admin)
            //      : ESCloud-Admin ==> (ESCloud, ESCloud-Admin)
            if (cloudEnvType.toString().contains("-Admin")) {
                example.put("cloudEnvTypes",
                            Arrays.asList(cloudEnvType.toString(), cloudEnvType.toString().replace("-Admin", "")));
            } else {
                example.put("cloudEnvTypes",
                            Arrays.asList(cloudEnvType.toString(), cloudEnvType.toString().concat("-Admin")));
            }
            example.getCondition().remove("cloudEnvType");
        }
    }

    @Override
    public void setResVmExtInfo(List<ResVm> resVms) {
        if (!CollectionUtils.isEmpty(resVms)) {
            List<ResVmExt> resVmExts = null;
            resVmExts = resVmExtService.selectWithDetailInfo(
                    new Criteria("instanceIds", resVms.stream().map(ResVm::getId).collect(Collectors.toList())));

            Map<String, List<ResVmExt>> cloudHostExtGroupByInstanceId = resVmExts.stream()
                                                                                 .collect(Collectors.groupingBy(
                                                                                         ResVmExt::getInstanceId));

            for (ResVm resVm : resVms) {
                List<ResVmExt> cloudHostExtsByInstanceId = cloudHostExtGroupByInstanceId.get(resVm.getId());
                if (!CollectionUtils.isEmpty(cloudHostExtsByInstanceId)) {
                    Map<String, List<ResVmExt>> collect = cloudHostExtsByInstanceId.stream()
                                                                                   .collect(Collectors.groupingBy(
                                                                                           ResVmExt::getType));
                    resVm.setCloudHostExt(collect);

                    CloudEnv cloudEnv = cloudEnvMapper.selectByPrimaryKey(resVm.getCloudEnvId());
                    if (Objects.nonNull(cloudEnv) && CloudEnvType.OPEN_STACK.equals(cloudEnv.getCloudEnvType())) {
                        String instanceId = resVm.getId();
                        if (Objects.nonNull(instanceId)) {
                            Criteria criteria = new Criteria();
                            criteria.put("extInstanceId", instanceId);
                            List<ResSecurityGroup> securityGroups = resSecurityGroupMapper.selectByParams(criteria);
                            List<ResVmExt> securityGroupExts = collect.getOrDefault(
                                    ResVmExtEnum.SECURITY_GROUP.getType(), Lists.newArrayList());
                            List<ResVmExt> toAdd = Lists.newArrayList();
                            for (ResSecurityGroup next : securityGroups) {
                                securityGroupExts.forEach(resVmExt -> {
                                    if (!Objects.equals(next.getId().toString(), resVmExt.getResourceId())) {
                                        ResVmExt sgExt = new ResVmExt();
                                        sgExt.setInstanceId(instanceId);
                                        sgExt.setResourceId(next.getId().toString());
                                        sgExt.setType(ResVmExtEnum.SECURITY_GROUP.getType());
                                        sgExt.setSgName(next.getName());
                                        toAdd.add(sgExt);
                                    }
                                });
                            }
                            securityGroupExts.addAll(toAdd);
                            securityGroupExts = securityGroupExts.stream()
                                                                 .filter(res -> Objects.nonNull(res.getSgName()))
                                                                 .collect(Collectors.collectingAndThen(
                                                                         Collectors.toCollection(() -> new TreeSet<>(
                                                                                 Comparator.comparing(
                                                                                         ResVmExt::getSgName))),
                                                                         ArrayList::new));
                            collect.put(ResVmExtEnum.SECURITY_GROUP.getType(), securityGroupExts);
                        }
                    }

                    resVm.setNetworkInfo(cloudHostExtsByInstanceId.stream()
                                                                  .filter(o -> ResVmExtEnum.SUBNET.getType()
                                                                                                  .equalsIgnoreCase(
                                                                                                          o.getType()))
                                                                  .map(o -> o.getVpcName() != null ? o.getVpcName()
                                                                          + "(" + o.getSubnetName() + ")" : "--")
                                                                  .collect(Collectors.joining(",")));
                    resVm.setSubnetName(cloudHostExtsByInstanceId.stream()
                                                                 .filter(o -> ResVmExtEnum.SUBNET.getType()
                                                                                                 .equalsIgnoreCase(
                                                                                                         o.getType()))
                                                                 .map(o -> o.getSubnetName())
                                                                 .collect(Collectors.joining(",")));
                    if (Objects.isNull(resVm.getNetworkName())) {
                        resVm.setNetworkName(cloudHostExtsByInstanceId.stream()
                                                                      .filter(o -> ResVmExtEnum.SUBNET.getType()
                                                                                                      .equalsIgnoreCase(
                                                                                                              o.getType()))
                                                                      .map(ResVmExt::getVpcName)
                                                                      .collect(Collectors.joining(",")));
                    }
                }
            }
        }
    }

    @Override
    public void setResVmExtInfo(ResVm resVm) {
        if (Objects.nonNull(resVm)) {
            setResVmExtInfo(Lists.newArrayList(resVm));
        }
    }

    @Override
    public List<ResVm> selectHostForClusterByParams(Criteria example) {
        return this.resVmMapper.selectHostForClusterByParams(example);
    }

    @Override
    public List<ResVm> selectHostByParams(Criteria example) {
        return this.resVmMapper.selectHostSimpleInfoByParams(example);
    }

    @Override
    public List<ResVm> selectBaseByParams(Criteria example) {
        return this.resVmMapper.selectBaseByParams(example);
    }

    @Override
    public List<String> selectInstanceIdsBaseByParams(Criteria example) {
        return resVmMapper.selectInstanceIdsBaseByParams(example);
    }

    @Override
    public int countByParams(Criteria criteria) {
        return this.resVmMapper.countByParams(criteria);
    }

    @Override
    public int countSelfServiceVms(Criteria criteria) {
        return this.resVmMapper.countSelfServiceVms(criteria);
    }

    @Override
    public int countByExample(Criteria criteria) {
        return this.resVmMapper.countByExample(criteria);
    }

    @Override
    public int countByBaseByParams(Criteria criteria) {
        return this.resVmMapper.countByBaseByParams(criteria);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResVm updateResVmInfo(ResVm record) {
        List<ResVmNetcard> resVmNetcards = record.getResVmNetcards();
        ResVm resVm = this.resVmMapper.selectByPrimaryKey(record.getId());
        String innerIp = resVm.getInnerIp();
        String publicIp = resVm.getPublicIp();
        if (!CollectionUtils.isEmpty(resVmNetcards)) {
            Long cloudEnvId = resVm.getCloudEnvId();
            List<String> ipList = new ArrayList<>();

            // 清空IP表中该主机所有的占用情况
            NetworkIp ip = new NetworkIp();
            ip.setAllocateTargetId(record.getId());
            ip.setStatus(NetworkManagement.AVAILABLE);
            this.networkIpMapper.updateStatusByAllocateTargetId(ip);

            // 预备更新ip表的数据
            Criteria ipParams = new Criteria();
            ipParams.put("cloudEnvId", cloudEnvId);
            ipParams.put("allocateTargetId", record.getId());
            ipParams.put("status", NetworkManagement.UNAVAILABLE);
            ipParams.put("updatedDt", DateTime.now().toDate());
            ipParams.put("updatedBy", BasicInfoUtil.getAuthUser().getAccount());

            Criteria params = new Criteria("cloudEnvId", cloudEnvId);
            for (ResVmNetcard netcard : resVmNetcards) {
                // ip地址允许改为空， 非空的时候验证IP地址的占用状态
                if (!Strings.isNullOrEmpty(netcard.getIpAddress())) {
                    params.put("ipAddress", netcard.getIpAddress());
                    // 确定IP是否为可用
                    List<NetworkIp> networkIps = this.networkIpMapper.selectByExample(params);
                    if (!networkIps.isEmpty() && !NetworkManagement.AVAILABLE.equals(networkIps.get(0).getStatus())) {
                        throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1358475563));
                    }
                    ipList.add(netcard.getIpAddress());
                }

                ResVmNetcard resVmNetcard = this.resVmNetcardMapper.selectByPrimaryKey(netcard);
                netcard.setIpAddress(Strings.nullToEmpty(netcard.getIpAddress()));
                // 如果当前待更新的网卡没有记录IP，根据主机ID和MAC地址更新
                if (Objects.isNull(resVmNetcard)) {
                    this.resVmNetcardMapper.updateByParamsSelective(netcard, MapsKit.of("resVmId", netcard.getResVmId(),
                                                                                        "macAddress",
                                                                                        netcard.getMacAddress()));
                } else {
                    // 更新网卡信息
                    this.resVmNetcardMapper.updateByPrimaryKeySelective(netcard);
                }
            }

            if (!CollectionUtils.isEmpty(ipList)) {
                // 更新ip表的占用情况
                ipParams.put("ipAddress", ipList);
                this.networkIpMapper.updateStatusByCloudEnvId(ipParams);
            }

            // 判断IP的分类，更新到主机字段
            List<String> innerIpList = ipList.stream().filter(IPUtil::internalIp).collect(Collectors.toList());
            innerIp = Strings.nullToEmpty(Joiner.on(",").join(innerIpList));
            ipList.removeAll(innerIpList);
            publicIp = Strings.nullToEmpty(Joiner.on(",").join(ipList));
        }
        // 更新硬盘
        this.updateVd(record);

        // 更新主机
        ResVm resVmUpdate = this.resVmMapper.selectByPrimaryKey(record.getId());
        resVmUpdate.setStartTime(record.getStartTime());
        resVmUpdate.setEndTime(record.getEndTime());
        resVmUpdate.setDescription(record.getDescription());
        resVmUpdate.setOsName(record.getOsName());
        resVmUpdate.setPlatform(record.getPlatform());
        resVmUpdate.setOsCategory(record.getOsCategory());
        resVmUpdate.setInnerIp(innerIp);
        resVmUpdate.setPublicIp(publicIp);
        if (CloudEnvType.MAAS.equals(resVm.getCloudEnvType())) {
            resVmUpdate.setInnerIp(record.getInnerIp());

            CloudPhysicalHostPool cloudPhysicalHostPool = cloudPhysicalHostPoolMapper.selectByPrimaryKey(
                    resVm.getPhysicalHostPoolId());
            if (Objects.nonNull(cloudPhysicalHostPool)) {
                cloudPhysicalHostPool.setOsCategory(record.getOsCategory());
                cloudPhysicalHostPool.setOsName(record.getOsName());
                cloudPhysicalHostPoolMapper.updateByPrimaryKeySelective(cloudPhysicalHostPool);
            }
        }
        resVmMapper.updateByPrimaryKey(resVmUpdate);

        if (CloudEnvType.MAAS.equals(resVm.getCloudEnvType())) {
            CloudPhysicalHostPool cloudPhysicalHostPoolUpdate = new CloudPhysicalHostPool();
            cloudPhysicalHostPoolUpdate.setId(resVm.getPhysicalHostPoolId());
            cloudPhysicalHostPoolUpdate.setInnerIp(resVmUpdate.getInnerIp());
            cloudPhysicalHostPoolUpdate.setOsName(record.getOsName());
            BasicWebUtil.prepareUpdateParams(cloudPhysicalHostPoolUpdate);
            this.cloudPhysicalHostPoolMapper.updateByPrimaryKeySelective(cloudPhysicalHostPoolUpdate);
        }

        // 更新扩展属性
        if (!CollectionUtils.isEmpty(record.getResAttrs())) {
            resAttrExtService.updateResAttrs(record.getResAttrs());
        }

        vmOperateLog(resVm.getId(), resVm.getInstanceName(), ResourceOperateEnum.MODIFY.getOperate());

        return resVm;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByPrimaryKeySelective(ResVm record) {
        // 更新主机
        int relateHostCnt = this.resVmMapper.checkInsertSerRelateHost(record.getId(), record.getCloudDeploymentId());
        if (record.getCloudDeploymentId() != null && relateHostCnt == 0) {
            this.resVmMapper.insertSerRelateHost(record.getId(), record.getCloudDeploymentId());
        }
        return this.resVmMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateIpByPrimaryKeySelective(String hostId, String newIp, boolean isPublicIp) {
        ResVm resVm = this.resVmMapper.selectByPrimaryKey(hostId);

        //获取主机所在云环境的所有IP地址
        List<NetworkIp> networkIps = this.networkIpMapper.selectByExample(
                new Criteria().put("cloudEnvId", resVm.getCloudEnvId()));

        List<String> ipList = new ArrayList<>();
        String oldIp = null;
        if (isPublicIp) {
            oldIp = resVm.getPublicIp();
        } else {
            oldIp = resVm.getInnerIp();
        }
        final List<String> oldIpList = IPUtil.getIpList(ipList, oldIp);
        final List<String> newIpList = IPUtil.getIpList(ipList, newIp);

        if (!networkIps.isEmpty()) {
            //检查ip是否被占用
            boolean isOccupied = networkIps.stream()
                                           // 已经被占用的条件1. 已使用， 2.不属于主机的已有IP
                                           // 1.过滤掉未使用的ip 2.旧的IP是否不包含已经使用的IP 3.新的ip是否包含已经使用的IP
                                           .filter(networkIp ->
                                                           !networkIp.getStatus().equals(NetworkManagement.AVAILABLE)
                                                                   && !oldIpList.contains(networkIp.getIpAddress())
                                                                   && newIpList.contains(networkIp.getIpAddress()))
                                           .collect(Collectors.toList())
                                           .isEmpty();
            if (!isOccupied) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1851713737));
            }
        }

        //检查是否已经是公网或私网
        if (!StringUtil.isNullOrEmpty(resVm.getPublicIp()) && !isPublicIp) {
            List<String> oldPublicIp = IPUtil.getIpList(null, resVm.getPublicIp());
            for (String n : newIpList) {
                if (oldPublicIp.contains(n)) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_846656069));
                }
            }
        }

        if (!StringUtil.isNullOrEmpty(resVm.getInnerIp()) && isPublicIp) {
            List<String> oldInnerIp = IPUtil.getIpList(null, resVm.getInnerIp());
            for (String n : newIpList) {
                if (oldInnerIp.contains(n)) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_89896720));
                }
            }
        }

        //释放已占用的网络
        if (!oldIpList.isEmpty() && !networkIps.isEmpty()) {
            networkIps.stream().filter(ip -> oldIpList.contains(ip.getIpAddress())).forEach(networkIp -> {
                networkIp.setAllocateTargetId(null);
                networkIp.setStatus(NetworkManagement.AVAILABLE);
                this.networkIpMapper.updateByPrimaryKey(networkIp);
            });
        }

        //IP占用
        if (!newIpList.isEmpty() && !networkIps.isEmpty()) {
            networkIps.stream().filter(ip -> newIpList.contains(ip.getIpAddress())).forEach(networkIp -> {
                networkIp.setAllocateTargetId(hostId);
                networkIp.setStatus(NetworkManagement.UNAVAILABLE);
                this.networkIpMapper.updateByPrimaryKey(networkIp);
            });
        }

        //将新的IP地址更新到主机上
        if (isPublicIp) {
            resVm.setPublicIp(newIp);
        } else {
            resVm.setInnerIp(newIp);
        }
        this.resVmMapper.updateByPrimaryKey(resVm);
        return true;
    }

    @Override
    public int updateByPrimaryKey(ResVm record) {
        return this.resVmMapper.updateByPrimaryKey(record);
    }

    @Override
    public ResVm findByPrimaryKey(String pk) {
        return this.resVmMapper.selectByPrimaryKey(pk);
    }

    @Override
    public ResVm findByPrimaryKeyAndEnvId(String pk) {
        ResVm resVm = this.resVmMapper.selectByPrimaryKeyAndEnvId(pk);
        if (resVm == null) {
            return null;
        }
        setHasIndependentPersistVolume(resVm);
        setResVmExtInfo(resVm);
        return resVm;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResInstResult stopResVm(String hostId, boolean isStopCharging) {
        ResVm resVm = this.resVmMapper.selectByPrimaryKey(hostId);
        if (!ResVmStatus.RUNNING.equals(resVm.getStatus()) && !ResVmStatus.SUSPENDED.equals(resVm.getStatus())) {
            return new ResInstResult(ResInstResult.FAILURE, "非运行中或已挂起的主机无法停止。");
        }

        //start send delete host
        List<Long> userSids = businessNotificationRemoteService.findUserToNotify(resVm.getOwnerId(),
                                                                                 resVm.getCreatedBy(),
                                                                                 resVm.getOrgSid());
        Long ownerId = userSids.stream().findFirst().get();

        // cancel deploy task
        this.basicDeployTaskService.cancelTaskByHostNotEnd(resVm.getId());

        //停止主机任务
        DeployTask deployTask = this.basicDeployTaskService.stopResVmTask(resVm,
                                                                          MapsKit.of("isStopCharging", isStopCharging));
        resVm.setStatus(ResVmStatus.STOPPING);
        //连接中主机关机时管理状态为disconnect
        if (resVm.getManageStatus().equals(ResVmManageStatus.CONNECTING)) {
            resVm.setManageStatus(ResVmManageStatus.DISCONNECT);
        }
        this.resVmMapper.updateByPrimaryKeySelective(resVm);

        vmOperateLog(resVm.getId(), resVm.getInstanceName(), ResourceOperateEnum.STOP.getOperate());

        return new ResInstResult(ResInstResult.SUCCESS, "主机停止中，请等待。");
    }

    private void vmOperateLog(String vmId, String name, String opType) {
        ResActionLog resActionLog = new ResActionLog();
        resActionLog.setResSid(vmId);
        resActionLog.setResName(name);
        resActionLog.setOpDate(Calendar.getInstance().getTime());
        resActionLog.setResType(ResourceTypeEnum.VM.getType());
        resActionLog.setOpUser(BasicInfoUtil.getAuthUser().getAccount());
        resActionLog.setOpType(opType);
        resActionLog.setStatus(Boolean.TRUE);
        basicResActionLogService.insertSelective(resActionLog);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResInstResult restartResVm(String hostId, boolean isStopCharging) {
        ResVm resVm = this.resVmMapper.selectByPrimaryKey(hostId);
        if (!ResVmStatus.RUNNING.equals(resVm.getStatus())) {
            return new ResInstResult(ResInstResult.FAILURE, "非运行中的主机无法重启。");
        }
        VmOperate vmOperate = CloudClientFactory.buildMQBean(resVm.getCloudEnvId(), VmOperate.class);
        vmOperate.setId(hostId);
        vmOperate.setVmName(resVm.getInstanceName());
        vmOperate.setAction(VmOperation.REBOOT);
        String targetIndex;
        if (CloudEnvType.FUSIONCOMPUTE.equals(vmOperate.getProviderType())) {
            targetIndex = resVm.getUri();
        } else {
            targetIndex = resVm.getInstanceId();
        }
        vmOperate.setUuid(targetIndex);

        // 取消尚未结束的deploy task
        this.basicDeployTaskService.cancelTaskByHostNotEnd(resVm.getId());

        //生成虚拟机重启任务
        DeployTask deployTask = this.basicDeployTaskService.restartResVmTask(resVm, MapsKit.of("isStopCharging",
                                                                                               isStopCharging));
        Map<String, Object> options = new HashMap<>();
        options.put("isStopCharging", isStopCharging);
        options.put("taskId", deployTask.getId());
        if (CloudEnvType.MAAS.equals(vmOperate.getProviderType())) {
            CloudPhysicalHostPool cloudPhysicalHostPool = cloudPhysicalHostPoolMapper.selectByPrimaryKey(
                    resVm.getPhysicalHostPoolId());
            AssertUtil.requireNonBlank(cloudPhysicalHostPool, "关联的物理机已经被删除.");
            options.put(PhysicalHostInfo.PHYSICAL_HOST_POOL_ID, resVm.getPhysicalHostPoolId());
            options.put(PhysicalHostInfo.POWER_ATTR_DATA, cloudPhysicalHostPool.getPowerAttrData());
            options.put(PhysicalHostInfo.POWER_TYPE, cloudPhysicalHostPool.getPowerType());
        }
        vmOperate.setOptions(options);
        sendToMQ(vmOperate);
        resVm.setStatus(ResVmStatus.STARTING);
        this.resVmMapper.updateByPrimaryKeySelective(resVm);

        return new ResInstResult(ResInstResult.SUCCESS, "主机重启中，请等待。");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResInstResult startResVm(String hostId) {
        ResVm resVm = this.resVmMapper.selectByPrimaryKey(hostId);
        if (!ResVmStatus.STOPPED.equals(resVm.getStatus()) && !ResVmStatus.SUSPENDED.equals(resVm.getStatus())) {
            return new ResInstResult(ResInstResult.FAILURE, "非已停止或已挂起的主机无法启动。");
        }
        VmOperate vmOperate = CloudClientFactory.buildMQBean(resVm.getCloudEnvId(), VmOperate.class);
        vmOperate.setId(hostId);
        vmOperate.setVmName(resVm.getInstanceName());
        String targetIndex;
        if (CloudEnvType.FUSIONCOMPUTE.equals(vmOperate.getProviderType())) {
            targetIndex = resVm.getUri();
        } else {
            targetIndex = resVm.getInstanceId();
        }
        vmOperate.setUuid(targetIndex);
        vmOperate.setAction(VmOperation.START);

        //生成虚拟机启动任务
        DeployTask deployTask = this.basicDeployTaskService.startResVmTask(resVm);
        Map<String, Object> options = new HashMap<>();
        options.put("taskId", deployTask.getId());
        if (CloudEnvType.MAAS.equals(vmOperate.getProviderType())) {
            CloudPhysicalHostPool cloudPhysicalHostPool = cloudPhysicalHostPoolMapper.selectByPrimaryKey(
                    resVm.getPhysicalHostPoolId());
            AssertUtil.requireNonBlank(cloudPhysicalHostPool, "关联的物理机未找到..");
            options.put(PhysicalHostInfo.PHYSICAL_HOST_POOL_ID, resVm.getPhysicalHostPoolId());
            options.put(PhysicalHostInfo.POWER_ATTR_DATA, cloudPhysicalHostPool.getPowerAttrData());
            options.put(PhysicalHostInfo.POWER_TYPE, cloudPhysicalHostPool.getPowerType());
        }
        vmOperate.setOptions(options);

        sendToMQ(vmOperate);

        resVm.setStatus(ResVmStatus.STARTING);
        this.resVmMapper.updateByPrimaryKeySelective(resVm);
        serviceInstTargetRemoteService.updateSelfInstByHostId(resVm.getId(), resVm.getStatus(), false);
        // 只更新主机操作
        serviceInstTargetRemoteService.updateSelfInstByHostId(resVm.getId(), SelfServiceInstanceStatus.RUNNING,
                                                              Collections.singletonList(CloudDeploymentType.INFRA));
        return new ResInstResult(ResInstResult.SUCCESS, "主机启动中，请等待。");
    }

    @Override
    public VmCreate createResVm(CloudCommonInst cloudInst) {
        final JsonNode jsonNode = cloudInst.getCloudSpec();
        Long envId = jsonNode.get("envId").asLong();
        CloudEnv cloudEnv = cloudEnvService.selectByPrimaryKey(envId);
        if (Objects.isNull(cloudEnv)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1705539941));
        }

        ResVm resVm = createInstance(jsonNode);

        if (ExtraType.BAREMETAL.equals(resVm.getExtraType())) {
            // 检查是否有可用的裸金属节点
            if (!checkAvailableBaremetalNode(resVm.getCloudEnvId())) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_924615660));
            }
        }
        // 名称唯一性判定
        if (!checkUniqueVmName(resVm.getCloudEnvId(), resVm.getInstanceName(), 1, false)) {
            logger.info("实例名称:[{}]", resVm.getInstanceName());
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1296032234));
        }

        resVm.setServerType(ServerType.INSTANCE);
        resVm.setOwnerId(cloudInst.getUserId());
        resVm.setOrgSid(changeOrgSidWhenSharedAsExclusive(cloudInst.getOrgSid(), resVm, cloudEnv));
        resVm.setOriginParam(JsonUtil.toJson(jsonNode));
        //add for set deployment
        resVm.setCloudDeploymentId(cloudInst.getCloudDeploymentId());
        resVm.setCloudDeploymentName(cloudInst.getCloudDeploymentName());
        BasicWebUtil.prepareInsertParams(resVm, cloudInst.getUserAccount());
        String account = resVm.getManagementAccount();
        String password = resVm.getManagemenPassword();
        resVm.setCreatedOrgSid(BasicInfoUtil.getCurrentOrgSid());
        resVm.setId(UuidUtil.getUuid().replace("-", ""));
        this.resVmMapper.insertSelective(resVm);
        resVmExtService.insertResVmExt(resVm);
        if (resVm.getCloudDeploymentId() != null) {
            this.resVmMapper.insertSerRelateHost(resVm.getId(), resVm.getCloudDeploymentId());
        }
        //存储后密码为加密，此处设置回原密码，防止在底层操作时密码无法对应
        resVm.setManagementAccount(account);
        resVm.setManagemenPassword(password);
        if (null != cloudInst.getSfServiceDeployInstId()) {
            ServiceInstTarget serviceInstTarget = new ServiceInstTarget();
            serviceInstTarget.setTargetId(resVm.getId());
            serviceInstTarget.setSfServiceInstId(cloudInst.getSfServiceDeployInstId());
            serviceInstTarget.setTargetType(SelfServiceYamlDeployType.INFRA);
            serviceInstTarget.setHasCreate(true);
            serviceInstTargetRemoteService.insert(serviceInstTarget);
        }

        // 预留IP
        reservedFixedIp(resVm);

        //预留弹性IP
        reservedFloatingIp(resVm);

        // 给主机打标签
        reservedResVmTag(resVm, jsonNode);

        return makeVmCreateBean(resVm, cloudInst.getOptions());
    }

    /**
     * 检查是否有可用的裸金属节点
     *
     * @param envId
     */
    private boolean checkAvailableBaremetalNode(Long envId) {
        List<BaremetalNodeVo> baremetalNodes = getBaremetalNodes(envId);
        for (BaremetalNodeVo baremetalNodeVo : baremetalNodes) {
            boolean b = "".equals(baremetalNodeVo.getDisplayStatus()) && Objects.isNull(baremetalNodeVo.getInstanceUuid())
                    && !"inspect failed".equals(baremetalNodeVo.getProvisionState());
            if (b && !"manageable".equals(
                    baremetalNodeVo.getProvisionState()) && !baremetalNodeVo.getMaintenance()) {
                return true;
            }
        }

        return false;
    }

    private List<BaremetalNodeVo> getBaremetalNodes(Long envId) {
        BaremetalNodeResult bareMetalResult = getBareMetalResult(envId);
        List<BaremetalNodeVo> baremetalNodeVos = bareMetalResult.getBaremetalNodeVos();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(baremetalNodeVos)) {
            return baremetalNodeVos;
        }

        return Lists.newArrayListWithCapacity(0);
    }

    private BaremetalNodeResult getBareMetalResult(Long cloudEnvId) {
        BaremetalNodeResult baremetalNodeResult = new BaremetalNodeResult();
        try {
            ScanCloudOsBareMetalNode baremetalByEnv = CloudClientFactory.buildMQBean(cloudEnvId,
                                                                                     ScanCloudOsBareMetalNode.class);
            baremetalNodeResult = (BaremetalNodeResult) MQHelper.rpc(baremetalByEnv);
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
        return baremetalNodeResult;
    }

    private Long changeOrgSidWhenSharedAsExclusive(Long orgSid, ResVm resVm, CloudEnv cloudEnv) {
        // 如果当前环境/分区被某个项目独享，那么新创建的机器，自动归到该项目下
        Long sharedAsExclusiveOrgSid;
        if (CloudEnvType.SUPPORT_POOL_CLOUD_ENV.contains(CloudEnvType.from(cloudEnv.getCloudEnvType()))) {
            sharedAsExclusiveOrgSid = resPoolService.sharedAsExclusiveOrgSid(resVm.getResPoolId());
        } else {
            sharedAsExclusiveOrgSid = cloudEnvService.sharedAsExclusiveOrgSid(cloudEnv.getId());
        }

        return Objects.isNull(sharedAsExclusiveOrgSid) ? orgSid : sharedAsExclusiveOrgSid;
    }

    private Long changeOrgSidWhenSharedAsExclusive(Long orgSid, ResVm resVm) {
        CloudEnv cloudEnv = cloudEnvService.selectByPrimaryKey(resVm.getCloudEnvId());

        return changeOrgSidWhenSharedAsExclusive(orgSid, resVm, cloudEnv);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResInstResult cloneResVm(CloudCommonInst cloudInst) {
        final JsonNode jsonNode = cloudInst.getCloudSpec();
        ResVm resVm = cloneInstance(jsonNode);
        resVm.setOriginParam(JsonUtil.toJson(jsonNode));
        resVm.setOwnerId(cloudInst.getUserId());
        resVm.setOrgSid(changeOrgSidWhenSharedAsExclusive(BasicInfoUtil.getCurrentOrgSid(), resVm));
        BasicWebUtil.prepareInsertParams(resVm, cloudInst.getUserAccount());
        resVm.setId(UuidUtil.getUuid().replace("-", ""));
        // 设置hostname
        final JsonNode instance = jsonNode.get("instance");
        if (instance.has("hostName") && !Strings.isNullOrEmpty(instance.get("hostName").textValue())) {
            String hostName = instance.get("hostName").textValue();
            if (instance.has("index")) {
                hostName = String.format("%s-%03d", hostName, instance.get("index").asInt());
            }
            ((ObjectNode) instance).put("hostName", hostName);
            resVm.setHostName(hostName);
        } else {
            Long orgSidForHostName = Convert.toLong(jsonNode.get("orgSidForHostName"));
            String defaultHostName = getHostNameByFixInfo(resVm.getInstanceName(), orgSidForHostName,
                                                          resVm.getOsCategory());
            resVm.setHostName(defaultHostName);
        }
        //电源状态
        if (instance.get("powerStatus") != null && instance.get("powerStatus").asBoolean()) {
            resVm.setClonePowerStatus(Boolean.TRUE);
        } else {
            resVm.setClonePowerStatus(Boolean.FALSE);
        }
        this.resVmMapper.insertSelective(resVm);
        resVmExtService.insertResVmExt(resVm);

        // 预留IP
        reservedFixedIp(resVm);

        return cloneInstanceOnPlatform(resVm, cloudInst.getOptions());
    }

    private void reservedResVmTag(ResVm resVm, JsonNode jsonNode) {
        try {
            JsonNode tagArr = jsonNode.get("tagArr");
            if (tagArr != null) {
                Iterator<JsonNode> iterator = tagArr.elements();
                while (iterator.hasNext()) {
                    JsonNode js = iterator.next();
                    String tagName = js.get("tagName").asText();
                    String rgbCode = js.get("rgbCode").asText();
                    String tagValue = js.get("tagValue").asText();
                    String objType = js.get("objType").asText();
                    CloudTag tag = new CloudTag();
                    tag.setTagName(tagName);
                    tag.setRgbCode(rgbCode);
                    tag.setTagValue(tagValue);
                    tag.setObjType(objType);
                    tag.setObjId(resVm.getId());
                    cloudTagRemoteService.relationTag(tag);
                }
            }
        } catch (Exception e) {
            logger.error("创建主机关联标签出错：", e);
        }
    }

    @Override
    public List<ResVm> selectVotingInfo(Long elasticGroupId) {
        Criteria criteria = new Criteria();
        criteria.put("elasticGroupId", elasticGroupId);
        criteria.setOrderByClause("elastic_time desc");

        //计算投票时间段
        DateTime now = DateTime.now();
        DateTime lastElasticTime = null;
        criteria.clear();
        criteria.put("elasticGroupId", elasticGroupId);
        criteria.put("now", now.toDate());

        return this.resVmMapper.selectVotingInfo(criteria);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DeployTask retryFailTask(JsonNode param) {
        ResVm resVm = this.resVmMapper.selectByPrimaryKey(param.get("resVmId").textValue());
        String type = null;
        if ("docker".equals(param.get("module").textValue())) {
            type = DeployTaskType.INSTALL_DOCKER;
        } else {
            type = DeployTaskType.INSTALL_MONITOR;
        }
        DeployTask retryTask = this.basicDeployTaskService.setupHostModuleTask(resVm, type, DeployTaskStatus.RUNNING);
        HostDepoyEvent hostDepoyEvent = new HostDepoyEvent(resVm);
        if (DeployTaskType.INSTALL_DOCKER.equals(retryTask.getType())) {
            hostDepoyEvent.setType(DeployTaskType.INSTALL_DOCKER);
            hostDepoyEvent.setApiPath(AnsibleServerMethod.ControlApi.DOCKER);
        } else if (DeployTaskType.INSTALL_MONITOR.equals(retryTask.getType())) {
            hostDepoyEvent.setType(DeployTaskType.INSTALL_MONITOR);
            hostDepoyEvent.setApiPath(AnsibleServerMethod.ControlApi.MONITOR);
        }
        hostDepoyEvent.setTaskId(retryTask.getId());

        SpringContextHolder.publishEvent(hostDepoyEvent);
        return retryTask;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean unInstallAgent(String hostId) {
        ResVm resVm = this.resVmMapper.selectByPrimaryKey(hostId);
        DeployTask retryTask = this.basicDeployTaskService.setupHostModuleTask(resVm, DeployTaskType.UNINSTALL_AGENT,
                                                                               DeployTaskStatus.RUNNING);
        HostDepoyEvent hostDepoyEvent = new HostDepoyEvent(resVm);
        hostDepoyEvent.setType(DeployTaskType.UNINSTALL_AGENT);
        hostDepoyEvent.setTaskId(retryTask.getId());
        hostDepoyEvent.setApiPath(AnsibleServerMethod.ControlApi.UNINSTALL);
        hostDepoyEvent.setEventRoutingKey(AnsibleMqConfig.NODE_CONTROL_ROUTING_KEY_INCOMPLETE
                                                  + AnsibleTaskTypeConfig.NODE_CONTROL_UNINSTALL_AGENT);
        SpringContextHolder.publishEvent(hostDepoyEvent);

        //移除redis 监控支配
        JedisUtil.instance().hdel("monitor", hostId);
        resVmMapper.updateByPrimaryKey(resVm);

        return true;
    }


    private String getPublicIpForPowerVc(String innerIp) {
        if (Strings.isNullOrEmpty(innerIp)) {
            return null;
        }
        String publicIp = null;
        Criteria criteria = new Criteria();
        criteria.put("configKey", "powervc.public.ip");
        List<SysConfig> sysconfigList = basicSysConfigService.selectByParams(criteria);
        if (sysconfigList == null || sysconfigList.size() == 0) {
            return null;
        }
        String json = sysconfigList.get(0).getConfigValue();
        JsonNode jsonNode = JsonUtil.fromJson(json);
        String[] ip = new String[1];
        if (jsonNode != null && jsonNode.get("publicIps") != null) {
            jsonNode.get("publicIps").elements().forEachRemaining(node -> {
                if (Objects.equals(node.get("innerIp").toString(), innerIp)) {
                    ip[0] = node.get("publicIp").textValue();
                }
            });
        }
        if (ip[0] != null) {
            publicIp = ip[0];
        }
        return publicIp;

    }

    @Override
    public List<ResVm> findByElasticGroup(Criteria example) {
        return this.resVmMapper.selectByParams(example);
    }

    @Override
    public List<ResVm> findByAvailablePools(Criteria criteria) {
        return this.resVmMapper.findByAvailablePools(criteria);
    }

    @Override
    public List<ResVm> findAllocVm(Criteria criteria) {
        criteria.put("statusList", Arrays.asList(ResVmStatus.DELETED, ResVmStatus.DELETING, ResVmStatus.CREATE_FAILURE,
                                                 ResVmStatus.PENDING));

        return this.resVmMapper.findAllocVm(criteria);
    }

    @Override
    public List<ResVmVO> findAllocVmForVO(Criteria criteria) {
        criteria.put("statusList", Arrays.asList(ResVmStatus.DELETED, ResVmStatus.DELETING, ResVmStatus.CREATE_FAILURE,
                                                 ResVmStatus.CREATING, ResVmStatus.PENDING));

        criteria.put("isRecycle", false);
        return this.resVmMapper.findAllocVmForVO(criteria);
    }

    @Override
    public Integer countAllocVmCountByDf(Criteria criteria) {
        criteria.put("statusList", Arrays.asList(ResVmStatus.DELETING, ResVmStatus.CREATE_FAILURE, ResVmStatus.PENDING,
                                                 ResVmStatus.DELETED, ResVmStatus.CREATING));

        criteria.put("isRecycle", false);
        return this.resVmMapper.countAllocVmByDf(criteria);
    }

    @Override
    public List<ResVm> findNotAllocVm(Criteria criteria) {
        criteria.put("statusList", Arrays.asList(ResVmStatus.DELETED, ResVmStatus.DELETING, ResVmStatus.CREATE_FAILURE,
                                                 ResVmStatus.CREATING, ResVmStatus.PENDING));
        criteria.put("isRecycle", false);

        Org org = orgRemoteService.selectByPrimaryKey(BasicInfoUtil.getCurrentOrgSid());
        criteria.put("orgSid", org.getOrgSid());
        criteria.put("treePath", org.getTreePath() + org.getOrgSid() + "/");
        return this.resVmMapper.findNotAllocVm(criteria);
    }

    @Override
    public int joinInstanceToProject(CloudEnvAlloc cloudEnvAlloc) {
        //验证是否超过配额
        String msg = null;
        CloudEnvAlloc manageCloudEnvAlloc = this.cloudEnvAllocMapper.selectByPrimaryKey(cloudEnvAlloc.getCloudEnvId(),
                                                                                        cloudEnvAlloc.getAllocTargetId());
        String quota = manageCloudEnvAlloc.getQuota();
        List<CloudEnvQuota> quotas = null;
        try {
            quotas = JsonUtil.fromJson(quota, new TypeReference<List<CloudEnvQuota>>() {
            });
        } catch (Exception e) {
            logger.error("无法正确取得配额信息 | {}", Throwables.getStackTraceAsString(e));
            throw new BizException(RestConst.BizError.BAD_PARAM, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_570158371));
        }
        Criteria criteria = new Criteria();
        criteria.put("cloudEnvId", cloudEnvAlloc.getCloudEnvId());
        criteria.put("allocTargetId", cloudEnvAlloc.getAllocTargetId());
        List<ResVm> projectVms = this.resVmMapper.findByAvailablePools(criteria)
                                                 .stream()
                                                 .filter(cloudHost -> cloudHost.getOrgSid()
                                                                               .equals(cloudEnvAlloc.getAllocTargetId()))
                                                 .collect(Collectors.toList());

        List<String> serverType = Arrays.asList(ServerType.SERVER_INSTANCE, ServerType.INSTANCE,
                                                ServerType.ELASTIC_INSTANCE);
        List<ResVm> quotaVms = new ArrayList<>();
        // 非服务模板主机，且状态为非创建失败的
        quotaVms.addAll(projectVms.stream()
                                  .filter(host -> serverType.contains(host.getServerType()))
                                  .filter(host -> !(ResVmStatus.CREATE_FAILURE.equals(host.getStatus())
                                          || ResVmStatus.CREATING.equals(host.getStatus())))
                                  .collect(Collectors.toList()));
        // 创建中的，无论是何类型
        List<ResVm> vmCreating = projectVms.stream()
                                           .filter(host -> ResVmStatus.CREATING.equals(host.getStatus()))
                                           .filter(host -> !Objects.equals(host.getId(), host.getId()))
                                           .collect(Collectors.toList());
        quotaVms.addAll(vmCreating);

        boolean flg;
        long errorQuota = 0L;
        for (CloudEnvQuota envQuota : quotas) {
            flg = false;
            // 配额数为负数为不限制该项目
            if (envQuota.getQuotaVal() == null || envQuota.getQuotaVal() <= 0) {
                continue;
            }
            if (EnvQuotaKey.VM_COUNT.equals(envQuota.getQuotaKey())) {
                // 实例个数
                int vmCount = quotaVms.size();
                if (vmCount + 1 > envQuota.getQuotaVal()) {
                    errorQuota = vmCount;
                    flg = true;
                }
            }

            // 判断
            if (flg) {
                msg = String.format("%s超过项目配额限制，项目配置配额为：%s%s，已使用：%s%s", envQuota.getQuotaName(), envQuota.getQuotaVal(),
                                    envQuota.getUnit(), errorQuota, envQuota.getUnit());
                logger.warn("{} | envId[{}], projectId[{}]", msg, cloudEnvAlloc.getCloudEnvId(),
                            cloudEnvAlloc.getAllocTargetId());
                break;
            }
        }
        if (!Strings.isNullOrEmpty(msg)) {
            throw new BizException(RestConst.BizError.RES_CREATE_FAILED, msg);
        } else {
            ResVm resVm = new ResVm();
            resVm.setId(cloudEnvAlloc.getInstanceId());
            resVm.setOrgSid(cloudEnvAlloc.getAllocTargetId());
            this.resVmMapper.updateByPrimaryKeySelective(resVm);
        }

        return 1;
    }

    @Override
    public int recoveryInstanceToProject(ResVm resVm) {
        resVm.setOrgSid(-1L);
        return this.resVmMapper.updateByPrimaryKeySelective(resVm);
    }

    @Override
    public boolean checkUniqueVmName(Long envId, String vmName, int num, boolean flag) {
        List<String> vmNames = Lists.newArrayList();
        for (int i = 0; i < num; i++) {
            if (flag) {
                vmNames.add(String.format("%s-%d", vmName, i + 1));
            } else {
                vmNames.add(vmName);
            }
        }
        Criteria criteria = new Criteria();
        criteria.put("cloudEnvId", envId);
        criteria.put("instanceNames", vmNames);
        criteria.put("statusNotEquals", ResVmStatus.DELETED);
        criteria.put("ignoreRecycle", true);
        int count = this.resVmMapper.countByExample(criteria);

        // 对于VMware，虚拟机名称不能和镜像ID一样
        criteria.clear();
        criteria.put("cloudEnvId", envId);
        criteria.put("cloudEnvType", "VMware");
        criteria.put("imageIds", vmNames);
        count += this.resImageMapper.countByExample(criteria);

        return count == 0;
    }

    private void assertEnvExists(CloudEnv cloudEnv) {
        if (Objects.isNull(cloudEnv)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_832756465));
        }
    }

    @Override
    public List<ResVm> selectByParamForExaReport(Criteria criteria) {
        List<ResVm> resVmList = this.resVmMapper.selectByParamForExaReport(criteria);
        return resVmList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, noRollbackFor = BizException.class)
    public boolean vmRename(String resVmId, String newName) {
        if (Strings.isNullOrEmpty(newName) || Strings.isNullOrEmpty(newName.trim())) {
            throw new BizException(RestConst.BizError.BAD_PARAM, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1869210578));
        }
        ResVm resVm = this.resVmMapper.selectByPrimaryKey(resVmId);
        if (resVm == null) {
            throw new BizException(RestConst.BizError.RES_NOT_FOUND, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_930900495));
        }
        if (ResVmStatus.PENDING.equalsIgnoreCase(resVm.getStatus())) {
            return updateVmName(true, resVm, newName, "");
        }

        if (newName.trim().equals(resVm.getInstanceName())) {
            return true;
        }
        // 导入的主机直接修改数据库
        if (Objects.isNull(resVm.getCloudEnvId())) {
            return updateVmName(true, resVm, newName, "");
        }
        VmRename vmRename = CloudClientFactory.buildMQBean(resVm.getCloudEnvId(), VmRename.class);
        try {
            vmRename.setRegion(resVm.getRegion());
            vmRename.setId(resVm.getInstanceId());
            vmRename.setName(resVm.getInstanceName());
            vmRename.setNameTobe(newName);
            vmRename.setUri(resVm.getUri());
            VmRenameResult vmRenameResult = (VmRenameResult) MQHelper.rpc(vmRename);

            return updateVmName(Objects.nonNull(vmRenameResult) && vmRenameResult.isSuccess(), resVm, newName,
                                vmRenameResult.getErrMsg());
        } catch (MQException e) {
            logger.error(Throwables.getStackTraceAsString(e));
            return false;
        }
    }

    private boolean updateVmName(boolean success, ResVm resVm, String newName, String errorMsg) {
        Object principal = BasicInfoUtil.getAuthUser().getAccount();
        String userAccout;
        if (Objects.nonNull(principal)) {
            userAccout = principal.toString();
        } else {
            userAccout = resVm.getCreatedBy();
        }

        if (success) {
            ResVm updHost = new ResVm();
            updHost.setId(resVm.getId());
            updHost.setInstanceName(newName);

            int actionLogId = basicResActionLogService.insertIntoActionLog(resVm.getId(), userAccout,
                                                                           ResourceTypeEnum.VM,
                                                                           ResourceOperateEnum.MODIFY, Boolean.TRUE);

            BasicWebUtil.prepareUpdateParams(updHost, userAccout);
            this.resVmMapper.updateByPrimaryKeySelective(updHost);
            basicResActionLogService.updateActionLog(actionLogId, ResourceTypeEnum.VM, resVm.getId(),
                                                     ResourceOperateEnum.MODIFY);
            return true;
        } else {
            basicResActionLogService.insertIntoActionLog(resVm.getId(), userAccout, ResourceTypeEnum.VM,
                                                         ResourceOperateEnum.MODIFY, Boolean.FALSE);

            logger.error("Instance {} rename failed. {}", resVm.getInstanceName(), errorMsg);
            throw new BizException(RestConst.BizError.RES_UPDATE_FAILED, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_630902306));
        }
    }

    /**
     * 重新配置虚拟机
     *
     * @return true or false
     */

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean vmReConfig(JsonNode params, AuthUser authUser) {
        // 构造参数，后期做一次性配额检查，避免多次检查导致超时
        ResVm resVm = reConfigInstance(params);

        if (Objects.equals(ResVmStatus.SETTING, resVm.getStatus()) && StringUtil.isNotBlank(
                resVm.getOriginalStatus())) {
            resVm.setStatus(resVm.getOriginalStatus());
            logger.info("实例:[{}] 当前状态为SETTING，使用OriginalStatus: [{}]", resVm.getInstanceName(),
                        resVm.getOriginalStatus());
        }

        // 更新状态为配置中
        ResVm resVmUpd = new ResVm();
        resVmUpd.setId(resVm.getId());
        resVmUpd.setStatus(ResVmStatus.SETTING);
        resVmMapper.updateByPrimaryKeySelective(resVmUpd);

        if (!(CloudEnvType.CLOUDOS.equals(resVm.getCloudEnvType()) || CloudEnvType.OPEN_STACK.equals(
                resVm.getCloudEnvType()))) {
            //预留IP
            reservedFixedIp(resVm);
        }

        //预处理VD
        reservedResVd(resVm, authUser);

        return reConfigInstanceOnPlatform(resVm);
    }

    /**
     * 预处理硬盘状态 [创建中， 销毁中， 卸载中]
     */
    private void reservedResVd(ResVm resVm, AuthUser authUser) {
        if (Objects.isNull(resVm) || CollectionUtils.isEmpty(resVm.getResVdList())) {
            return;
        }

        List<ResVd> resVdList = resVm.getResVdList();

        for (ResVd resVd : resVdList) {
            resVd.setOrgSid(resVm.getOrgSid());
            resVd.setCloudEnvId(resVm.getCloudEnvId());
            resVd.setChargeType(resVm.getInstanceChargeType());
            if (ResOperation.MODIFY.equals(resVd.getOperate())) {
                resVd.setStatus(ResVdStatus.MODIFYING);
            } else if (ResOperation.DELETE.equals(resVd.getOperate())) {
                resVd.setStatus(ResVdStatus.DELETING);
            } else if (ResOperation.CREATE.equals(resVd.getOperate())) {
                resVd.setStatus(ResVdStatus.CREATING);
            } else if (ResOperation.DETACH.equals(resVd.getOperate())) {
                resVd.setStatus(ResVdStatus.SETTING);
            }

            if (!Objects.isNull(resVd.getResVdSid())) {
                Long size = resVd.getAllocateDiskSize();
                resVd.setAllocateDiskSize(null);

                //刚开始重新配置时，不更新硬盘容量
                BasicWebUtil.prepareUpdateParams(resVd);
                resVdMapper.updateByPrimaryKeySelective(resVd);

                resVd.setAllocateDiskSize(size);
            } else {
                resVd.setOwnerId(authUser.getUserSid().toString());
                resVd.setOrgSid(authUser.getOrgSid());
                resVd.setCreatedOrgSid(authUser.getOrgSid());
                BasicWebUtil.prepareInsertParams(resVd, authUser);
                resVdMapper.insertSelective(resVd);
            }
        }
    }

    /**
     * 发送MQ消息处理升降配请求
     */
    private boolean reConfigInstanceOnPlatform(ResVm resVm) {
        // 1.配额判断
        reConfigInstanceQuota(resVm);

        // 2.reconfig task
        DeployTask deployTask = this.basicDeployTaskService.reConfigResVmTask(resVm);

        final String reConfigHostTaskId = deployTask.getId();

        final String message = MessageUtil.getLogMessage("开始重新配置实例.");
        MongoUtil.save(new LogInfo(resVm.getId(), message), DeployConst.HOST_LOG_KEY_PREFIX);

        VmReconfig vmReconfig = CloudClientFactory.buildMQBean(resVm.getCloudEnvId(), VmReconfig.class);
        switch (CloudEnvType.from(vmReconfig.getProviderType())) {
            case VMWARE:
                vmReconfig = makeVMwareMQBeanForReConfig(resVm, vmReconfig);
                break;
            case FUSIONCOMPUTE:
                vmReconfig = makeFusionMQBeanForReConfig(resVm, vmReconfig);
                break;
            case OPEN_STACK:
            case CLOUDOS:
                vmReconfig = makeOpenstackMQBeanForReConfig(resVm, vmReconfig);
                break;
            default:
                return false;
        }
        vmTypeChangeService.changeVmTypeForOld(resVm.getId(), vmReconfig.getInstanceType());

        // 获取操作系统类型
        String osType = parseOsName(resVm);
        vmReconfig.setOptions(
                ImmutableMap.of("taskId", reConfigHostTaskId, "osType", osType, "resVmId", resVm.getId(), "vmStatus",
                                resVm.getStatus()));

        sendToMQ(vmReconfig);

        return true;
    }

    /**
     * 组装openstack升降配属性
     */
    private VmReconfig makeOpenstackMQBeanForReConfig(ResVm cloudHost, VmReconfig vmReconfig) {
        vmReconfig.setVmName(cloudHost.getInstanceName());
        vmReconfig.setCpu(String.valueOf(cloudHost.getCpu()));
        vmReconfig.setMemory(String.valueOf(cloudHost.getMemory()));
        vmReconfig.setInstanceType(cloudHost.getInstanceType());
        vmReconfig.setStatus(cloudHost.getStatus());
        vmReconfig.setId(cloudHost.getInstanceId());
        vmReconfig.setProviderUrl(vmReconfig.getProviderUrl());
        vmReconfig.setTenantId(vmReconfig.getTenantId());
        vmReconfig.setTenantName(vmReconfig.getTenantName());
        vmReconfig.setTenantUserName(vmReconfig.getTenantUserName());
        vmReconfig.setTenantUserPass(vmReconfig.getTenantUserPass());
        vmReconfig.setRegion(cloudHost.getRegion());

        // 数据盘
        if (!CollectionUtils.isEmpty(cloudHost.getResVdList())) {
            List<VmDisk> disks = new ArrayList<>();
            cloudHost.getResVdList().forEach(resVd -> {
                VmDisk vmDisk = new VmDisk();
                vmDisk.setId(resVd.getResVdSid());
                ResVd vdInfo = resVdMapper.selectByPrimaryKey(resVd.getResVdSid());
                vmDisk.setSize(resVd.getAllocateDiskSize().toString());
                vmDisk.setOperate(resVd.getOperate());
                if (!StringUtil.isNullOrEmpty(vdInfo)) {
                    vmDisk.setDeviceTagert(vdInfo.getUuid());
                } else if (StringUtil.isNullOrEmpty(resVd.getLogicVolume())) {
                    vmDisk.setDeviceTagert(resVd.getUuid());
                } else {
                    vmDisk.setDeviceTagert(resVd.getLogicVolume());
                }
                vmDisk.setLocation(resVd.getDeviceName());
                vmDisk.setPath(resVd.getPath());
                vmDisk.setName(resVd.getVdName());

                ResVolumeType resVolumeType = resVolumeTypeMapper.selectByPrimaryKey(resVd.getVolumeTypeId());
                if (Objects.nonNull(resVolumeType)) {
                    vmDisk.setVolumeType(resVolumeType.getUuid());
                    vmDisk.setVolumeTypeId(resVd.getVolumeTypeId() + "");
                }

                vmDisk.setAvailabilityZone(resVd.getZone());

                vmDisk.setDiskType(resVd.getStoragePurpose());
                vmDisk.setDeleteWithInstance(Objects.equals(ReleaseMode.WITH_INSTANCE, resVd.getReleaseMode()));
                disks.add(vmDisk);
            });
            vmReconfig.setDisks(disks);
        }

        return vmReconfig;
    }

    /**Fusion 配置变更**/
    private VmReconfig makeFusionMQBeanForReConfig(ResVm resVm, VmReconfig vmReconfig) {
        JsonNode jsonNode = JsonUtil.fromJson(resVm.getOriginParam());
        vmReconfig.setVmName(resVm.getInstanceName());
        vmReconfig.setCpu(String.valueOf(resVm.getCpu()));
        vmReconfig.setMemory(String.valueOf(resVm.getMemory()));
        vmReconfig.setInstanceType(resVm.getInstanceType());
        vmReconfig.setStatus(resVm.getStatus());
        vmReconfig.setId(resVm.getId());
        vmReconfig.setUri(resVm.getUri());

        // networks
        List<VmNic> vmNics = new ArrayList<>();
        JsonNode networks = jsonNode.get("nics");
        if (networks != null) {
            networks.forEach(nic -> {
                VmNic vmNic = new VmNic();
                if (nic.get("mac") != null) {
                    vmNic.setMac(nic.get("mac").textValue());
                }
                if (nic.get("networkId") != null) {
                    long networkId = nic.get("networkId").asLong();
                    Network network = this.networkMapper.selectByPrimaryKey(networkId);
                    vmNic.setGateway(network.getGateway());
                    vmNic.setNetmask(network.getMask());
                    vmNic.setDns(network.getDns1());
                    // 查询网络上可选的端口组
                    Criteria criteria = new Criteria("networkId", networkId);
                    criteria.put("resPoolId", resVm.getResPoolId());
                    List<ResVsPortGroupNetwork> networkPortGroups = resVsPortGroupNetworkMapper.selectByResPoolId(
                            criteria);
                    List<String> networkPortGroupIds = networkPortGroups.stream()
                                                                        .map(ResVsPortGroupNetwork::getResVsPortGroupId)
                                                                        .collect(Collectors.toList());
                    // 查找相同端口组 - name和vlanId相同视为相同端口组
                    if (!CollectionUtils.isEmpty(networkPortGroupIds)) {
                        networkPortGroupIds.addAll(
                                resVsPortGroupMapper.selectSamePortGroupId(new Criteria("ids", networkPortGroupIds)));
                    }
                    // 根据实例ID查询与之关联的端口组
                    List<ResVsPortGroup> resVsPortGroups = resVsPortGroupMapper.selectPortsByCloudHost(
                            new Criteria("resVmId", resVm.getId()));

                    // 根据网络上可选的端口组筛选可用的端口组
                    List<ResVsPortGroup> availablePortGroups = resVsPortGroups.stream()
                                                                              .filter(resVsPortGroup -> networkPortGroupIds
                                                                                      .contains(resVsPortGroup.getId()))
                                                                              .collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(availablePortGroups)) {
                        throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1309678389));
                    }

                    if (ResVsType.STANDARD_VS.equals(availablePortGroups.get(0).getResVsType())) {
                        // 标准交换机
                        vmNic.setVirSwitch(availablePortGroups.get(0).getName());
                    } else {
                        // 分布式交换机
                        vmNic.setPort(availablePortGroups.get(0).getName());
                    }
                    vmNic.setPortGroupId(availablePortGroups.get(0).getId());
                }

                if (nic.get("fixedIp") != null) {
                    vmNic.setPrivateIp(nic.get("fixedIp").textValue());
                }

                vmNic.setOperate(nic.get("opType").textValue());
                if (!StringUtil.isNullOrEmpty(nic.get("resNetworkId"))) {
                    vmNic.setResNetworkId(nic.get("resNetworkId").longValue());
                }
                vmNics.add(vmNic);
            });
        }
        vmReconfig.setVmNics(vmNics);

        // 数据盘
        if (!CollectionUtils.isEmpty(resVm.getResVdList())) {
            List<VmDisk> disks = new ArrayList<>();
            resVm.getResVdList().forEach(resVd -> {
                VmDisk vmDisk = new VmDisk();
                vmDisk.setId(resVd.getResVdSid());
                vmDisk.setSize(resVd.getAllocateDiskSize().toString());
                vmDisk.setOperate(resVd.getOperate());

                vmDisk.setName(resVd.getVdName());
                vmDisk.setDeleteWithInstance(resVd.getDeleteWithInstance());
                ResStorage resStorage = resStorageMapper.selectByPrimaryKey(resVd.getAllocateResStorageSid());
                if (resStorage != null) {
                    vmDisk.setDatastoreUrn(resStorage.getUrn());
                }

                ResVd vd = resVdMapper.selectByPrimaryKey(resVd.getResVdSid());
                if (null != vd) {
                    vmDisk.setUri(vd.getUri());
                    vmDisk.setUrn(vd.getUrn());
                    ResVm vm = resVmMapper.selectByPrimaryKey(vd.getResVmId());
                    if (null != vm) {
                        vmDisk.setMountVmUri(vm.getUri());
                        vmDisk.setMountVmUrn(vm.getUrn());
                    }
                }

                disks.add(vmDisk);
            });
            vmReconfig.setDisks(disks);
        }

        return vmReconfig;
    }

    /**
     * 组装vmware升降配属性
     */
    private VmReconfig makeVMwareMQBeanForReConfig(ResVm resVm, VmReconfig vmReconfig) {

        JsonNode jsonNode = JsonUtil.fromJson(resVm.getOriginParam());
        vmReconfig.setVmName(resVm.getInstanceName());
        vmReconfig.setCpu(String.valueOf(resVm.getCpu()));
        vmReconfig.setMemory(String.valueOf(resVm.getMemory()));
        vmReconfig.setInstanceType(resVm.getInstanceType());
        vmReconfig.setStatus(resVm.getStatus());
        vmReconfig.setId(resVm.getInstanceId());

        // networks
        List<VmNic> vmNics = new ArrayList<>();
        JsonNode networks = jsonNode.get("nics");
        if (networks != null) {
            networks.forEach(nic -> {
                VmNic vmNic = new VmNic();
                if (nic.get("mac") != null) {
                    vmNic.setMac(nic.get("mac").textValue());
                }
                if (nic.get("networkId") != null) {
                    long networkId = nic.get("networkId").asLong();
                    Network network = this.networkMapper.selectByPrimaryKey(networkId);
                    vmNic.setGateway(network.getGateway());
                    vmNic.setNetmask(network.getMask());
                    vmNic.setDns(network.getDns1());
                    // 查询网络上可选的端口组
                    Criteria criteria = new Criteria("networkId", networkId);
                    criteria.put("resPoolId", resVm.getResPoolId());
                    List<ResVsPortGroupNetwork> networkPortGroups = resVsPortGroupNetworkMapper.selectByResPoolId(
                            criteria);
                    List<String> networkPortGroupIds = networkPortGroups.stream()
                                                                        .map(ResVsPortGroupNetwork::getResVsPortGroupId)
                                                                        .collect(Collectors.toList());
                    // 查找相同端口组 - name和vlanId相同视为相同端口组
                    if (!CollectionUtils.isEmpty(networkPortGroupIds)) {
                        networkPortGroupIds.addAll(
                                resVsPortGroupMapper.selectSamePortGroupId(new Criteria("ids", networkPortGroupIds)));
                    }
                    // 根据实例ID查询与之关联的端口组
                    List<ResVsPortGroup> resVsPortGroups = resVsPortGroupMapper.selectPortsByCloudHost(
                            new Criteria("resVmId", resVm.getId()));

                    // 根据网络上可选的端口组筛选可用的端口组
                    List<ResVsPortGroup> availablePortGroups = resVsPortGroups.stream()
                                                                              .filter(resVsPortGroup -> networkPortGroupIds
                                                                                      .contains(resVsPortGroup.getId()))
                                                                              .collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(availablePortGroups)) {
                        throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1309678389));
                    }

                    if (ResVsType.STANDARD_VS.equals(availablePortGroups.get(0).getResVsType())) {
                        // 标准交换机
                        vmNic.setVirSwitch(availablePortGroups.get(0).getName());
                    } else {
                        // 分布式交换机
                        vmNic.setPort(availablePortGroups.get(0).getName());
                    }
                    vmNic.setPortGroupId(availablePortGroups.get(0).getId());
                }

                if (nic.get("fixedIp") != null) {
                    vmNic.setPrivateIp(nic.get("fixedIp").textValue());
                }
                if (!Objects.isNull(nic.get("fixedIpId"))) {
                    // 预留IP
                    NetworkIp networkIp = new NetworkIp();
                    networkIp.setId(nic.get("fixedIpId").asLong());
                    vmNic.setSubnetId(nic.get("fixedIpId").asText());
                    networkIp.setAllocateTargetId(resVm.getId());
                    networkIp.setStatus(NetworkManagement.RESERVED);
                    BasicWebUtil.prepareUpdateParams(networkIp);
                    this.networkIpMapper.updateByPrimaryKeySelective(networkIp);
                }

                vmNic.setOperate(nic.get("opType").textValue());
                if (!StringUtil.isNullOrEmpty(nic.get("resNetworkId"))) {
                    vmNic.setResNetworkId(nic.get("resNetworkId").longValue());
                }
                vmNics.add(vmNic);
            });
        }
        vmReconfig.setVmNics(vmNics);

        // 数据盘
        if (!CollectionUtils.isEmpty(resVm.getResVdList())) {
            List<VmDisk> disks = new ArrayList<>();
            resVm.getResVdList().forEach(resVd -> {
                VmDisk vmDisk = new VmDisk();
                vmDisk.setId(resVd.getResVdSid());
                vmDisk.setSize(resVd.getAllocateDiskSize().toString());
                vmDisk.setOperate(resVd.getOperate());
                vmDisk.setDeviceTagert(resVd.getLogicVolume());
                vmDisk.setLocation(resVd.getDeviceName());
                vmDisk.setPath(resVd.getPath());
                vmDisk.setName(resVd.getVdName());
                vmDisk.setVmName(resVd.getResVmName());
                disks.add(vmDisk);
            });
            vmReconfig.setDisks(disks);
        }

        return vmReconfig;
    }

    /**
     * 通过属性过滤
     */
    private <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

    @Override
    public List<ResVm> selectResVmByVolumeType(Criteria criteria) {
        return resVmMapper.selectResVmByVolumeType(criteria);
    }

    @Override
    public List<ResVm> selectVmwareHostByStorageId(Criteria criteria) {
        return resVmMapper.selectVmwareHostByStorageId(criteria);
    }

    @Override
    public int resetResVmDeploymentId(Long cloudDeploymentId) {
        return resVmMapper.resetCloudHostDeploymentId(cloudDeploymentId);
    }

    @Override
    public List<ResVm> selectByParams(Criteria example) {
        return resVmMapper.selectByParams(example);
    }

    @Override
    public ResVm selectByInstanceId(String instanceId, Long cloudEnvId) {
        return resVmMapper.selectByInstanceId(instanceId, cloudEnvId);
    }

    @Override
    public int insertSelective(ResVm record) {
        resVmMapper.insertSelective(record);
        if (record.getCloudDeploymentId() != null) {
            resVmMapper.insertSerRelateHost(record.getId(), record.getCloudDeploymentId());
        }
        return 1;
    }

    private ResVm reConfigInstance(JsonNode jsonNode) {
        final JsonNode instance = jsonNode.get("instance");
        logger.info("实例ID：{}", jsonNode.findValue("id").textValue());
        ResVm resVm = resVmMapper.selectByPrimaryKey(instance.get("id").textValue());

        JsonNode resPoolId = jsonNode.findValue("resPoolId");
        if (Objects.nonNull(resPoolId)) {
            resVm.setResPoolId(resPoolId.asLong());
        }
        JsonNode envId = jsonNode.findValue("envId");
        if (Objects.nonNull(envId)) {
            resVm.setCloudEnvId(jsonNode.findValue("envId").asLong());
        }
        // instance

        //组装变更的数据
        JsonNode typeChange = jsonNode.findValue("instanceTypeChange");
        if (Objects.isNull(typeChange) || typeChange.isNull()) {
            ObjectNode instanceTypeChange = JsonNodeFactory.instance.objectNode();
            instanceTypeChange.put("memory", instance.get("memory").asInt() - resVm.getMemory());
            instanceTypeChange.put("cpu", instance.get("cpu").asInt() - resVm.getCpu());
            ((ObjectNode) jsonNode).set("instanceTypeChange", instanceTypeChange);
        }

        resVm.setInstanceType(instance.get("instanceType").textValue());
        resVm.setCpu(instance.get("cpu").asInt());
        resVm.setMemory(instance.get("memory").asInt());

        List<ResVmExt> cloudHostExts = Lists.newArrayList();
        // network
        if (!CloudEnvType.OPEN_STACK.equals(resVm.getCloudEnvType()) && !CloudEnvType.CLOUDOS.equals(
                resVm.getCloudEnvType()) && jsonNode.get("nics") != null) {
            JsonNode networks = jsonNode.get("nics");

            //已添加的IP列表
            Set<String> ips = new HashSet<>();
            HashMultimap<Long, String> networkGroupBySubnetId = HashMultimap.create();

            List<NetworkIp> networkIps = new ArrayList<>();
            for (Iterator<JsonNode> elements = networks.elements(); elements.hasNext(); ) {
                JsonNode next = elements.next();
                if (!Objects.isNull(next.get("networkId"))) {
                    Long networkId = next.get("networkId").asLong();

                    String fixedIp = null;
                    // 将固定ip预先占用，并更新到host的ip字段上面
                    if (next.get("fixedIpId") != null) {
                        NetworkIp networkIp = new NetworkIp();
                        networkIp.setNetworkId(networkId);
                        networkIp.setId(next.get("fixedIpId").asLong());
                        if (next.get("opType") != null) {
                            networkIp.setOpType(next.get("opType").asText());
                        }
                        if (next.get("opType") != null && "modify".equals(next.get("opType").textValue())) {
                            networkIp.setResNetworkId(next.get("resNetworkId").longValue());
                        }
                        if (next.get("opType") != null && !"remove".equals(next.get("opType").textValue())) {
                            networkIps.add(networkIp);
                            fixedIp = next.get("fixedIp").textValue();
                            ips.add(fixedIp);
                        }
                    }
                    networkGroupBySubnetId.put(networkId, fixedIp);
                }
            }
            resVm.setNetworkIps(networkIps);
            // 所有选择网络，更新到网络上
            resVm.setInnerIp(Joiner.on(",").join(ips));

            for (Long key : networkGroupBySubnetId.keySet()) {
                ResVmExt cloudHostExt = new ResVmExt();
                cloudHostExt.setInstanceId(resVm.getId());
                cloudHostExt.setResourceId(key.toString());
                cloudHostExt.setType(ResVmExtEnum.SUBNET.getType());
                cloudHostExt.setExtra(Joiner.on(",").skipNulls().join(networkGroupBySubnetId.get(key)));
                cloudHostExts.add(cloudHostExt);
            }
        }
        resVm.setResVmExts(cloudHostExts);

        // 数据盘
        JsonNode dataDisks = jsonNode.get("dataDisk");
        if (dataDisks != null && dataDisks.elements().hasNext()) {
            List<ResVd> disks = new ArrayList<>();
            dataDisks.elements().forEachRemaining(dataDisk -> {
                ResVd resVd = new ResVd();
                if (dataDisk.get("dataDiskSize") != null) {
                    resVd.setAllocateDiskSize(dataDisk.get("dataDiskSize").asLong());
                }
                if (dataDisk.get("volumeTypeId") != null) {
                    resVd.setVolumeTypeId(dataDisk.get("volumeTypeId").asLong());
                }
                if (dataDisk.get("opType") != null) {
                    resVd.setOperate(dataDisk.get("opType").asText());
                }
                if (dataDisk.get("id") != null) {
                    resVd.setResVdSid(dataDisk.get("id").asText());
                }
                if (dataDisk.get("name") != null) {
                    resVd.setVdName(dataDisk.get("name").asText());
                }
                if (dataDisk.get("deviceName") != null) {
                    resVd.setDeviceName(dataDisk.get("deviceName").asText());
                }
                if (dataDisk.get("logicVolume") != null) {
                    resVd.setLogicVolume(dataDisk.get("logicVolume").asText());
                }
                if (dataDisk.get("changeSize") != null) {
                    resVd.setChangeSize(dataDisk.get("changeSize").asLong());
                }
                if (dataDisk.get("zone") != null) {
                    resVd.setZone(dataDisk.get("zone").asText());
                }
                if (!StringUtil.isNullOrEmpty(dataDisk.get("diskType")) && dataDisk.get("diskType").asText() != null) {
                    resVd.setDiskType(dataDisk.get("diskType").asText());
                }
                if (dataDisk.get("type") != null) {
                    resVd.setStoragePurpose(dataDisk.get("type").asText());
                }
                if (dataDisk.get("deleteWithInstance") != null) {
                    resVd.setReleaseModeBoolean(dataDisk.get("deleteWithInstance").asBoolean());
                }
                disks.add(resVd);
            });
            //增加的存储容量和硬盘数量
            Long diskChangeSize = disks.stream()
                                       .filter(disk -> !Objects.isNull(disk.getChangeSize()))
                                       .mapToLong(ResVd::getChangeSize)
                                       .sum();
            List<ResVd> collect = disks.stream()
                                       .filter(disk -> ResOperation.MODIFY.equals(disk.getOperate()))
                                       .filter(disk -> StoragePurpose.SYSTEM_DISK.equalsIgnoreCase(
                                               disk.getStoragePurpose()))
                                       .collect(Collectors.toList());
            this.updateVd(collect);
            Long volumeChangeCount = disks.stream()
                                          .filter(disk -> ResOperation.CREATE.equals(disk.getOperate()))
                                          .filter(disk -> StoragePurpose.DATA_DISK.equalsIgnoreCase(
                                                  disk.getStoragePurpose()))
                                          .count();
            if (Objects.isNull(jsonNode.findValue("dataDiskChange"))) {
                ((ObjectNode) jsonNode).put("dataDiskChange", diskChangeSize);
                ((ObjectNode) jsonNode).put("volumeChange", volumeChangeCount);
            }

            Map<Long, List<ResVd>> resVdMap = disks.stream()
                                                   .filter(vd -> Objects.nonNull(vd.getVolumeTypeId()))
                                                   .collect(Collectors.groupingBy(ResVd::getVolumeTypeId));
            // 判断是否超阈值
            if (!CloudEnvType.OPEN_STACK.equals(resVm.getCloudEnvType()) && !CloudEnvType.CLOUDOS.equals(
                    resVm.getCloudEnvType())) {
                // 判断是否超阈值
                resVdMap.entrySet().forEach(resVd -> {
                    Long vdAddSizeOfVolumeType = resVd.getValue()
                                                      .stream()
                                                      .filter(vd -> !Objects.isNull(vd.getChangeSize()))
                                                      .mapToLong(ResVd::getChangeSize)
                                                      .sum();

                    resService.pickUpStoreOnVmware(resVd.getKey(), resVm.getId(), vdAddSizeOfVolumeType);
                });
            }

            List<ResVd> resVdList = new ArrayList<>();
            //挑选存储
            resVdMap.entrySet().forEach(r -> {
                Long preservedSize = 0L;

                List<ResVd> resVds = resVdMap.get(r.getKey());
                for (ResVd resVd : resVds) {
                    if (ResOperation.CREATE.equals(resVd.getOperate())) {
                        if (!CloudEnvType.OPEN_STACK.equals(resVm.getCloudEnvType()) && !CloudEnvType.CLOUDOS.equals(
                                resVm.getCloudEnvType())) {
                            ResStorageProvisionModel storageProvisionModel = resService.pickUpStoreOnVmwareForReConfig(
                                    resVd.getVolumeTypeId(), resVd.getAllocateDiskSize(),
                                    resVd.getChangeSize() > 0 ? 0 : resVd.getChangeSize(), preservedSize,
                                    resVm.getId());

                            resVd.setAllocateResStorageSid(storageProvisionModel.getId());
                            resVd.setDeviceName(storageProvisionModel.getName());
                            resVd.setDataStoreName(storageProvisionModel.getName());
                        }

                        String vDiskName = resVm.getInstanceName() + "-" + UuidUtil.getShortUuid();

                        StringBuilder sb = new StringBuilder();
                        sb.append("[")
                          .append(resVd.getDataStoreName())
                          .append("] ")
                          .append(resVm.getInstanceName())
                          .append("/")
                          .append(vDiskName);
                        resVd.setVdName(vDiskName);
                        resVd.setPath(sb.toString());
                        resVd.setResVmName(resVm.getInstanceName());
                    }
                }
                resVdList.addAll(resVds);
            });

            resVm.setResVdList(resVdList);
        }
        resVm.setOriginParam(JsonUtil.toJson(jsonNode));
        return resVm;
    }

    @Override
    public void checkReConfigQuota(JsonNode hostSpec) {
        ResVm resVm = reConfigInstance(hostSpec);
        reConfigInstanceQuota(resVm);
    }

    @Override
    public List<ResVm> selectByServiceInstanceId(Criteria criteria) {
        return resVmMapper.selectByServiceInstanceId(criteria);
    }

    @Override
    public String getDisktype(String zone) {
        if (Strings.isNullOrEmpty(zone)) {
            return "";
        }
        String[] zones = zone.split("-");
        if (zones.length < 1) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1350062775));
        }
        String types = JedisUtil.instance().get("tencent-disk-type");
        if (Strings.isNullOrEmpty(types)) {
            Map<String, Object> diskTypes = JsonUtil.fromJson(
                    ClassLoaderUtil.getResourceAsStream("diskRegionType.json", ResVmServiceImpl.class),
                    new TypeReference<Map<String, Object>>() {
                    });

            types = JsonUtil.toJson(diskTypes);
            JedisUtil.instance().set("tencent-disk-type", types);
        }
        return JsonUtil.fromJson(types).get(zones[0] + "-" + zones[1]).get(zone).toString();
    }

    @Override
    public boolean mutiUpdateResVmZone(List<String> hostIds, String resPoolName) {
        boolean success = resVmMapper.mutiUpdateCloudHostZone(hostIds, resPoolName) > 0;

        Optional<String> first = hostIds.stream().findFirst();
        if (first.isPresent()) {
            ResVm resVm = resVmMapper.selectByPrimaryKey(first.get());

            ResPool resPool = resPoolService.selectByName(resPoolName, resVm.getCloudEnvId());
            Long sharedAsExclusiveOrgSid = resPoolService.sharedAsExclusiveOrgSid(resPool.getId());

            if (Objects.nonNull(sharedAsExclusiveOrgSid)) {
                resVmMapper.updateOrgSidByHostIds(hostIds, sharedAsExclusiveOrgSid);
            }
        }

        return success;
    }

    @Override
    public boolean mutiUpdateResVmEndTime(List<String> hostIds, Date endTime) {
        return resVmMapper.mutiUpdateCloudHostEndTime(hostIds, endTime) > 0;
    }

    /**
     * 升降配配额检查
     */
    private void reConfigInstanceQuota(ResVm resVm) {
        Long projectId = resVm.getOrgSid();
        if (projectId == null) {
            return;
        }

        Project org = projectRemoteService.selectByPrimaryKey(projectId);
        // 只有项目校验配额
        // 润联组织也校验配额
        if (Objects.isNull(org)) {
            return;
        }
        // 当前组织不限制配额，找上级组织检查
        if (QuotaMode.NOLIMIT.equalsIgnoreCase(org.getQuotaCtrl())) {
            resVm.setOrgSid(org.getParentId());
            reConfigInstanceQuota(resVm);
            return;
        }

        JsonNode jsonNode = JsonUtil.fromJson(resVm.getOriginParam());

        Long envId = resVm.getCloudEnvId();
        List<CloudEnvQuota> quotas = null;
        Criteria criteria = new Criteria();
        //项目才有云环境配额
        criteria = new Criteria("cloudEnvId", envId);
        criteria.put("allocTargetId", projectId);
        List<CloudEnvAlloc> cloudEnvAllocs = this.cloudEnvAllocMapper.selectByExample(criteria);
        if (!CollectionUtils.isEmpty(cloudEnvAllocs)) {
            CloudEnvAlloc cloudEnvAlloc = cloudEnvAllocs.get(0);
            String quota = cloudEnvAlloc.getQuota();
            if (!Strings.isNullOrEmpty(quota)) {
                try {
                    quotas = JsonUtil.fromJson(quota, new TypeReference<List<CloudEnvQuota>>() {
                    });
                } catch (Exception e) {
                    logger.error("无法正确取得配额信息 | {}", Throwables.getStackTraceAsString(e));
                    throw new BizException(RestConst.BizError.BAD_PARAM, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_570158371));
                }
            }
        }

        // 实例数量检查
        String msg = null;
        boolean flg;
        int errorQuota = 0;
        JsonNode instanceTypeChange = jsonNode.get("instanceTypeChange");

        int cpuChange = instanceTypeChange.get("cpu").asInt();
        int memoryChange = instanceTypeChange.get("memory").asInt();

        int diskSizeChange = 0;
        if (!Objects.isNull(jsonNode.get("dataDiskChange"))) {
            diskSizeChange = jsonNode.get("dataDiskChange").asInt();
        }
        int volumeChange = 0;
        if (!Objects.isNull(jsonNode.get("volumeChange"))) {
            volumeChange = jsonNode.get("volumeChange").asInt();
        }

        Map<String, Integer> usedQuota = null;
        Map<String, Integer> freezeQuota = null;
        Map<String, Integer> allocQuota = null;
        Map<String, Integer> projectQuota = null;
        if (WebConstants.OrgType.PROJECT.equals(org.getOrgType())) {
            usedQuota = projectRemoteService.getCloudEnvUsedQuotas(envId, projectId);
            freezeQuota = projectRemoteService.getCloudEnvFreezeQuotas(envId, projectId, null);
            allocQuota = projectRemoteService.getProjectAllocQuotas(projectId);
            projectQuota = projectRemoteService.getProjectQuotas(projectId);
        } else if (WebConstants.OrgType.COMPANY.equals(org.getOrgType()) || WebConstants.OrgType.DEPARTMENT.equals(
                org.getOrgType())) {
            usedQuota = companyRemoteService.getCurrentOrgUsedQuotas(projectId);
            freezeQuota = projectRemoteService.getProjectFreezeQuotas(projectId, null);
            allocQuota = companyRemoteService.getCompanyAllocatedQuotas(projectId);
            projectQuota = companyRemoteService.getCompanyQuotas(projectId);
        }
        if (Objects.isNull(usedQuota) || Objects.isNull(freezeQuota) || Objects.isNull(allocQuota)
                || Objects.isNull(projectQuota)) {
            return;
        }
        if (!CollectionUtils.isEmpty(quotas)) {
            for (CloudEnvQuota envQuota : quotas) {
                flg = false;
                boolean limit = true;
                // 配额数为负数为不限制该项目
                if (envQuota.getQuotaVal() == null || envQuota.getQuotaVal() < 0) {
                    if (projectQuota.get(envQuota.getQuotaKey()) < 0) {
                        continue;
                    }
                    envQuota.setQuotaVal(0);
                    limit = false;
                }

                if (EnvQuotaKey.CPU_COUNT.equals(envQuota.getQuotaKey())) {
                    if (cpuChange <= 0) {
                        continue;
                    }
                    // CPU核数
                    int cpuCores = usedQuota.get(EnvQuotaKey.CPU_COUNT) + freezeQuota.get(EnvQuotaKey.CPU_COUNT);
                    if (cpuCores + cpuChange > envQuota.getQuotaVal()) {
                        errorQuota = cpuCores;
                        flg = true;
                    }
                } else if (EnvQuotaKey.MEMORY_MAX.equals(envQuota.getQuotaKey())) {
                    if (memoryChange <= 0) {
                        continue;
                    }
                    // 内存大小
                    int memSum = usedQuota.get(EnvQuotaKey.MEMORY_MAX) + freezeQuota.get(EnvQuotaKey.MEMORY_MAX);
                    if (memSum * 1024 + memoryChange > envQuota.getQuotaVal() * 1024) {
                        errorQuota = memSum;
                        flg = true;
                    }
                } else if (EnvQuotaKey.STORAGE_MAX.equals(envQuota.getQuotaKey())) {
                    if (diskSizeChange <= 0) {
                        continue;
                    }
                    // 存储大小
                    int storeSum = usedQuota.get(EnvQuotaKey.STORAGE_MAX) + freezeQuota.get(EnvQuotaKey.STORAGE_MAX);
                    errorQuota = storeSum;

                    if (storeSum + diskSizeChange > envQuota.getQuotaVal()) {
                        flg = true;
                    }
                } else if (EnvQuotaKey.VOLUME_COUNT.equals(envQuota.getQuotaKey())) {
                    if (volumeChange <= 0) {
                        continue;
                    }
                    // 硬盘数量
                    int volumeSum = usedQuota.get(EnvQuotaKey.VOLUME_COUNT) + freezeQuota.get(EnvQuotaKey.VOLUME_COUNT);
                    errorQuota = volumeSum;

                    if (volumeSum + volumeChange > envQuota.getQuotaVal()) {
                        flg = true;
                    }
                }

                // 判断
                if (!limit) {
                    int availabilityQuota =
                            projectQuota.get(envQuota.getQuotaKey()) - allocQuota.get(envQuota.getQuotaKey());
                    if (availabilityQuota <= 0) {
                        msg = String.format("%s超过云环境配额限制，可用配额：%s%s，预占配额：%s%s", envQuota.getQuotaName(),
                                            availabilityQuota, envQuota.getUnit(),
                                            freezeQuota.get(envQuota.getQuotaKey()), envQuota.getUnit());
                    }
                } else if (flg) {

                    int availabilityQuota = envQuota.getQuotaVal() - errorQuota;
                    if (availabilityQuota < 0) {
                        availabilityQuota = 0;
                    }

                    msg = String.format("%s超过云环境配额限制，可用配额：%s%s，预占配额：%s%s", envQuota.getQuotaName(), availabilityQuota,
                                        envQuota.getUnit(), freezeQuota.get(envQuota.getQuotaKey()),
                                        envQuota.getUnit());
                    logger.warn("{} | envId[{}], projectId[{}]", msg, envId, projectId);
                    break;
                }
            }
        }
        if (!Strings.isNullOrEmpty(msg)) {
            throw new BizException(RestConst.BizError.RES_CREATE_FAILED, msg);
        }

        SysMgtObjQuotaParams sysMgtObjQuotaParams = new SysMgtObjQuotaParams();
        sysMgtObjQuotaParams.setQuotaObjSid(projectId);
        List<SysMgtObjQuota> objQuotas = sysMgtObjQuotaRemoteService.selectByParams(sysMgtObjQuotaParams);
        List<SysQuota> globalQuotas = null;
        if (objQuotas != null && !objQuotas.isEmpty()) {
            try {
                globalQuotas = JsonUtil.fromJson(objQuotas.get(0).getQuotaValue(), new TypeReference<List<SysQuota>>() {
                });
            } catch (Exception e) {
                logger.error("无法正确取得配额信息 | {}", Throwables.getStackTraceAsString(e));
                throw new BizException(RestConst.BizError.BAD_PARAM, WebUtil.getMessage(MsgCd.ERR_MSG_BSS_570158371));
            }
        }

        if (globalQuotas == null) {
            return;
        }

        // 全局配额
        for (SysQuota envQuota : globalQuotas) {
            flg = false;
            //空为不限制
            if (StringUtils.isBlank(envQuota.getQuotaValue())) {
                continue;
            }
            Integer quotaVal = Integer.valueOf(envQuota.getQuotaValue());
            // 配额数为负数为不限制该项目
            if (quotaVal < 0) {
                continue;
            }
            if (EnvQuotaKey.CPU_COUNT.equals(envQuota.getQuotaKey())) {
                if (cpuChange <= 0) {
                    continue;
                }
                // CPU核数
                int cpuCores =
                        usedQuota.get(EnvQuotaKey.CPU_COUNT) + freezeQuota.get(EnvQuotaKey.CPU_COUNT) + allocQuota.get(
                                EnvQuotaKey.CPU_COUNT);
                if (cpuCores + cpuChange > quotaVal) {
                    errorQuota = cpuCores;
                    flg = true;
                }
            } else if (EnvQuotaKey.MEMORY_MAX.equals(envQuota.getQuotaKey())) {
                if (memoryChange <= 0) {
                    continue;
                }
                // 内存大小
                int memSum = usedQuota.get(EnvQuotaKey.MEMORY_MAX) + freezeQuota.get(EnvQuotaKey.MEMORY_MAX)
                        + allocQuota.get(EnvQuotaKey.MEMORY_MAX);
                if (memSum * 1024 + memoryChange > (quotaVal * 1024)) {
                    errorQuota = memSum;
                    flg = true;
                }
            } else if (EnvQuotaKey.STORAGE_MAX.equals(envQuota.getQuotaKey())) {
                if (diskSizeChange <= 0) {
                    continue;
                }
                // 存储大小
                int storeSum = usedQuota.get(EnvQuotaKey.STORAGE_MAX) + freezeQuota.get(EnvQuotaKey.STORAGE_MAX)
                        + allocQuota.get(EnvQuotaKey.STORAGE_MAX);
                errorQuota = storeSum;

                if (storeSum + diskSizeChange > quotaVal) {
                    flg = true;
                }
            } else if (EnvQuotaKey.VOLUME_COUNT.equals(envQuota.getQuotaKey())) {
                if (volumeChange <= 0) {
                    continue;
                }
                // 硬盘数量
                int volumeSum = usedQuota.get(EnvQuotaKey.VOLUME_COUNT) + freezeQuota.get(EnvQuotaKey.VOLUME_COUNT)
                        + allocQuota.get(EnvQuotaKey.VOLUME_COUNT);
                errorQuota = volumeSum;

                if (volumeSum + volumeChange > quotaVal) {
                    flg = true;
                }
            }

            // 判断
            if (flg) {
                int availabilityQuota = quotaVal - errorQuota;
                if (availabilityQuota < 0) {
                    availabilityQuota = 0;
                }
                msg = String.format("%s超过项目[%s]总配额限制，所需配额：%s，可用配额：%s%s，预占配额：%s%s", envQuota.getQuotaName(),
                                    org.getOrgName(), Math.abs(quotaVal - errorQuota), availabilityQuota,
                                    envQuota.getUnit(), freezeQuota.get(envQuota.getQuotaKey()), envQuota.getUnit());
                logger.warn("{} | envId[{}], projectId[{}]", msg, envId, projectId);
                break;
            }
        }
        if (!Strings.isNullOrEmpty(msg)) {
            throw new BizException(RestConst.BizError.RES_CREATE_FAILED, msg);
        }

    }

    /**
     * 通过实例类型查询主机个数（VMware主机的instance_type关联res_vm_type的uuid）
     */
    @Override
    public int countHostByInstanceType(String instanceType) {
        return this.resVmMapper.countHostByInstanceType(instanceType);
    }

    @Override
    public List<ResVd> selectDiskByResVmId(String resVmId) {
        Criteria criteria = new Criteria();
        criteria.put("resVmId", resVmId);
        return this.resVdMapper.selectByParams(criteria);
    }

    @Override
    public int countLicenseNormalHost(boolean isPubEnv) {
        Criteria criteria = new Criteria();
        criteria.put("statusNotIn", Arrays.asList(ResVmStatus.DELETING, ResVmStatus.CREATE_FAILURE, ResVmStatus.PENDING,
                                                  ResVmStatus.DELETED));
        criteria.put("serverTypeIn", Arrays.asList(ServerType.SERVER_INSTANCE, ServerType.SERVER, ServerType.INSTANCE,
                                                   ServerType.ELASTIC_INSTANCE));
        criteria.put("ignoreRecycle", true);
        List<CloudEnvType> envTypes = isPubEnv ? CloudEnvType.getPublicCloud() : CloudEnvType.getPrivateCloud();
        List<String> cloudEnvTypes = envTypes.stream()
                                             .flatMap(et -> et.getValue().stream())
                                             .collect(Collectors.toList());
        criteria.put("cloudEnvTypes", cloudEnvTypes);
        return this.resVmMapper.countLicenseNormalHost(criteria);
    }

    @Override
    public List<ResVm> selectBaseByExample(Criteria criteria) {
        return resVmMapper.selectBaseByExample(criteria);
    }

    @Override
    public void judgeIsCreateInstanceByLicense(int vmCount, @NonNull Long cloudEnvId) {
        CloudEnv env = cloudEnvService.checkCloudEnvNormal(cloudEnvId, true);
        judgeIsCreateInstanceByLicense(vmCount, CloudEnvType.from(env.getCloudEnvType()).isPublic());
    }

    @Override
    public void judgeIsCreateInstanceByLicense(int hopeCreateCount, boolean isPubEnv) {
        LicenseVo licenseVo = LicenseUtil.queryLicenseInfoFromDb();
        int allowInstanceCount = -1;
        if (isPubEnv && !licenseVo.isPubEnvInsNoLimit()) {
            allowInstanceCount = Integer.parseInt(licenseVo.getInstanceCount());
        } else if (!isPubEnv && !licenseVo.isPriEnvInsNoLimit()) {
            allowInstanceCount = Integer.parseInt(licenseVo.getPrivateInstanceCount());
        }

        if (allowInstanceCount == -1) {
            if (isPubEnv) {
                logger.info("许可证公有云[实例]无限制，忽略创建时校验...");
            } else {
                logger.info("许可证私有云[虚拟机]无限制，忽略创建时校验...");
            }
            return;
        }
        int existInstanceCount = countLicenseNormalHost(isPubEnv);
        // 剩余数量
        int remainCount = allowInstanceCount - existInstanceCount;
        if (remainCount < hopeCreateCount) {
            throw new BizException(
                    WebUtil.getMessage(isPubEnv ? "license.vm.count.limited" : "license.private.vm.count.limited",
                                           new Object[]{allowInstanceCount, existInstanceCount}));
        }
    }

    @Override
    public int updateDeploymentIdByPrimaryKey(ResVm resVm) {
        int result = this.resVmMapper.deleteDeployments(resVm);
        if (StringUtil.isNullOrEmpty(resVm.getCloudDeploymentId())) {
            this.resVmMapper.deleteDeployments(resVm);
        } else {
            this.resVmMapper.insertSerRelateHost(resVm.getId(), resVm.getCloudDeploymentId());
        }
        return result;
    }

    @Override
    public List<ResVm> getResVmForExport(Criteria criteria) {
        List<ResVm> resVms = this.selectHostByParamsByPermision(criteria);

        List<Long> cloudEnvIds = resVms.stream().map(ResVm::getCloudEnvId).distinct().collect(Collectors.toList());

        Map<String, String> zoneMap = Maps.newHashMap();
        if (!CollectionUtil.isEmpty(cloudEnvIds)) {
            Criteria zoneCriteria = new Criteria();
            zoneCriteria.put("cloudEnvIds", cloudEnvIds);
            List<ResZone> resZones = this.resZoneService.selectByParams(zoneCriteria);

            zoneMap = resZones.stream()
                              .collect(Collectors.toMap(resZone -> resZone.getCloudEnvId() + resZone.getUuid(),
                                                        ResZone::getName, (k1, k2) -> k1));
        }

        Map<String, String> serverTypeMap = Maps.newHashMap();
        serverTypeMap.put(ServerType.INSTANCE, "实例");
        serverTypeMap.put(ServerType.SERVER_INSTANCE, "服务器实例");
        serverTypeMap.put(ServerType.SERVER, "服务器");
        serverTypeMap.put(ServerType.ELASTIC_INSTANCE, "弹性伸缩组实例");
        for (ResVm resVm : resVms) {
            resVm.setServerType(serverTypeMap.get(resVm.getServerType()));
            if (!Objects.isNull(resVm.getStartTime())) {
                resVm.setDateDiff(DateUtil.parseDateDiff(resVm.getStartTime()));
            }

            // 重新设置zone
            resVm.setZone(zoneMap.getOrDefault(resVm.getCloudEnvId() + Strings.nullToEmpty(resVm.getZone()),
                                               resVm.getZone()));
        }

        return resVms;
    }

    @Override
    public boolean checkParamIsValid(HostParamCheckDTO hostParamCheckDTO) {
        // 是否存在检查不通过的资源
        boolean success = true;
        Map<String, List<HostParamCheckDTO.Data>> typeEnumHostParamCheckVOMap = hostParamCheckDTO.getData()
                                                                                                 .stream()
                                                                                                 .collect(
                                                                                                         Collectors.groupingBy(
                                                                                                                 HostParamCheckDTO.Data::getType));

        Criteria criteria = new Criteria();
        for (Entry<String, List<HostParamCheckDTO.Data>> entry : typeEnumHostParamCheckVOMap.entrySet()) {
            TypeEnum typeEnum = TypeEnum.forType(entry.getKey());

            if (null == typeEnum) {
                success = false;
                logger.warn("skip current resType exist check [{}]", entry.getKey());
                continue;
            }

            //对华为私有云存储类型特殊处理
            criteria.clear();

            criteria.put("tableName", typeEnum.getTableName());
            Object idAlias = typeEnum.getKey();
            // 特殊处理存储类型， 其他环境是uuid, VMware, OpenStack是id
            List<CloudEnvType> envs = Arrays.asList(CloudEnvType.VMWARE, CloudEnvType.FUSIONCOMPUTE,
                                                    CloudEnvType.OPEN_STACK, CloudEnvType.POWER_VC,
                                                    CloudEnvType.CLOUDOS);
            if (TypeEnum.VOLUME_TYPE.equals(typeEnum) && envs.contains(
                    CloudEnvType.from(hostParamCheckDTO.getCloudEnvType()))) {
                idAlias = "id";
            }
            criteria.put("idAlias", idAlias);
            criteria.put("cloudEnvId", hostParamCheckDTO.getCloudEnvId());

            for (HostParamCheckDTO.Data data : entry.getValue()) {
                criteria.put("id", data.getId());
                int count = objectExistCheckMapper.countByPrimaryKey(criteria);
                if (count == 0) {
                    criteria.put("idAlias", "id");
                    count = objectExistCheckMapper.countByPrimaryKey(criteria);
                }
                data.setStatus(count > 0);

                if (count == 0) {
                    success = false;
                }
            }
        }

        return success;
    }

    @Override
    public boolean resetResVmPassword(ResVmResetPasswordDTO resetPasswordDTO) {
        CloudEnv cloudEnv = cloudEnvService.selectByPrimaryKey(resetPasswordDTO.getCloudEnvId());

        ResVm resVm = resVmMapper.selectByPrimaryKey(resetPasswordDTO.getResVmId());

        DeployTask deployTask = basicDeployTaskService.resetVmPasswordTask(resVm, MapsKit.of("username",
                                                                                             resetPasswordDTO.getUsername(),
                                                                                             "password",
                                                                                             resetPasswordDTO.getNewPassword(),
                                                                                             "opUser",
                                                                                             BasicInfoUtil.getAuthUser()
                                                                                                          .getAccount(),
                                                                                             "opOrgSid",
                                                                                             BasicInfoUtil.getCurrentOrgSid()
                                                                                                          .toString()));

        // setup playbook
        DeployPlaybook deployPlaybook = this.basicDeployPlaybookService.setupPlaybook(
                Collections.singletonList(deployTask));

        boolean publicCloud = Objects.nonNull(cloudEnv) && cloudEnv.isPublicCloud();
        if (publicCloud || CloudEnvType.FUSIONCOMPUTE.equals(cloudEnv.getCloudEnvType()) || CloudEnvType.HCSO.equals(
                cloudEnv.getCloudEnvType())) {
            ResetPassword resetPassword = CloudClientFactory.buildMQBean(resetPasswordDTO.getCloudEnvId(),
                                                                         ResetPassword.class);

            resetPassword.setInstanceId(resVm.getInstanceId());
            resetPassword.setInstanceUri(resVm.getUri());
            resetPassword.setNewPassword(resetPasswordDTO.getNewPassword());
            resetPassword.setUsername(resetPasswordDTO.getUsername());

            resetPassword.setOptions(ImmutableMap.of("resVmId", resVm.getId(), "pid", deployPlaybook.getPid(), "taskId",
                                                     deployTask.getId()));
            sendToMQ(resetPassword);
        } else {
            resVm.setManagementAccount(resetPasswordDTO.getUsername());
            resVm.setManagemenPassword(resetPasswordDTO.getNewPassword());

            HostDepoyEvent hostDepoyEvent = new HostDepoyEvent(resVm);
            hostDepoyEvent.setType(DeployTaskType.RESET_VM_CIPHER);
            hostDepoyEvent.setApiPath(AnsibleServerMethod.ControlApi.AGENT);
            hostDepoyEvent.setTaskId(deployTask.getId());
            hostDepoyEvent.setManual(true);

            SpringContextHolder.publishEvent(hostDepoyEvent);
        }

        return true;
    }

    @Override
    public boolean resetResVmHostName(ResVmResetHostNameDTO resVmResetHostNameVO) {
        ResVm resVm = resVmMapper.selectByPrimaryKey(resVmResetHostNameVO.getResVmId());

        DeployTask deployTask = basicDeployTaskService.resetVmHostNameTask(resVm, MapsKit.of("username",
                                                                                             resVmResetHostNameVO.getUsername(),
                                                                                             "hostName",
                                                                                             resVmResetHostNameVO.getHostname(),
                                                                                             "opUser",
                                                                                             BasicInfoUtil.getAuthUser()
                                                                                                          .getAccount(),
                                                                                             "opOrgSid",
                                                                                             BasicInfoUtil.getCurrentOrgSid()
                                                                                                          .toString()));

        resVm.setManagementAccount(resVmResetHostNameVO.getUsername());
        resVm.setHostName(resVmResetHostNameVO.getHostname());
        HostDepoyEvent hostDepoyEvent = new HostDepoyEvent(resVm);
        hostDepoyEvent.setType(DeployTaskType.RESET_VM_HOSTNAME);
        hostDepoyEvent.setApiPath(AnsibleServerMethod.ControlApi.AGENT);
        hostDepoyEvent.setTaskId(deployTask.getId());
        hostDepoyEvent.setManual(true);
        SpringContextHolder.publishEvent(hostDepoyEvent);

        return true;
    }

    @Override
    public int updateOrgSidByHostIds(List<String> hostIds, Long orgSid) {
        return resVmMapper.updateOrgSidByHostIds(hostIds, orgSid);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public long markAsTemplate(MarkAsTemplateDTO markAsTemplateDTO) {
        ResVm resVm = getAndCheckVmwareInstance(markAsTemplateDTO.getCloudEnvId(), markAsTemplateDTO.getResVmId());

        if (!Objects.equals(resVm.getStatus(), ResVmStatus.STOPPED)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1919034828));
        }

        //如果模板名称不是主机名称，则需要校验名称是否唯一
        boolean needRename = false;
        if (!Objects.equals(resVm.getInstanceName(), markAsTemplateDTO.getTemplateName())) {
            boolean uniqueVmName = checkUniqueVmName(resVm.getCloudEnvId(), markAsTemplateDTO.getTemplateName(), 1,
                                                     false);
            if (!uniqueVmName) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_102684976));
            }
            needRename = true;
        }

        MarkVmAsTemplate markVmAsTemplate = CloudClientFactory.buildMQBean(resVm.getCloudEnvId(),
                                                                           MarkVmAsTemplate.class);
        markVmAsTemplate.setInstanceId(resVm.getInstanceId());
        markVmAsTemplate.setInstanceUri(resVm.getUri());
        markVmAsTemplate.setTemplateName(markAsTemplateDTO.getTemplateName());
        markVmAsTemplate.setNeedRename(needRename);

        VmTemplateResult vmTemplateResult = null;
        try {
            vmTemplateResult = (VmTemplateResult) MQHelper.rpc(markVmAsTemplate);
        } catch (MQException e) {
            logger.error(e.getMessage());
        }
        String account = BasicInfoUtil.getAuthUser().getAccount();
        if (vmTemplateResult == null || !vmTemplateResult.isSuccess()) {
            basicResActionLogService.insertIntoActionLog(resVm.getId(), account, ResourceTypeEnum.VM,
                                                         ResourceOperateEnum.MARK_AS_TEMPLATE, false);
            throw new BizException(vmTemplateResult == null ? WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1257583935) : vmTemplateResult.getErrMsg());
        }

        //1、逻辑删除实例及相关资源
        removeInstanceFromDB(resVm.getId(), true, false, account, true);

        //2、插入镜像
        ResImage resImage = buildResImage(resVm, markAsTemplateDTO.getTemplateName());
        resImage.setStatus(ResImageStatus.NOT_CONFIG);
        resImage.setAvailableSize(Double.valueOf(vmTemplateResult.getAvailableSize()));
        BasicWebUtil.prepareInsertParams(resImage);
        resImage.setUri(vmTemplateResult.getUri());
        resImage.setUrn(vmTemplateResult.getUrn());
        this.resImageMapper.insertSelective(resImage);

        //3、插入操作日志
        basicResActionLogService.insertIntoActionLog(resVm.getId(), account, ResourceTypeEnum.VM,
                                                     ResourceOperateEnum.MARK_AS_TEMPLATE, true);

        return resImage.getId();
    }

    private ResVm getAndCheckVmwareInstance(Long cloudEnvId, String resVmId) {
        CloudEnv cloudEnv = cloudEnvMapper.selectByPrimaryKey(cloudEnvId);
        if (cloudEnv == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1204853986));
        }
        if (!CloudEnvType.VMWARE.equals(cloudEnv.getCloudEnvType()) && !CloudEnvType.FUSIONCOMPUTE.equals(
                cloudEnv.getCloudEnvType())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1771488466));
        }

        ResVm resVm = resVmMapper.selectByPrimaryKey(resVmId);
        if (resVm == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1623795316));
        }

        return resVm;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public long cloneAsTemplate(CloneAsTemplateDTO cloneAsTemplateDTO) {
        ResVm resVm = getAndCheckVmwareInstance(cloneAsTemplateDTO.getCloudEnvId(), cloneAsTemplateDTO.getResVmId());
        if (!Objects.equals(resVm.getStatus(), ResVmStatus.STOPPED) && !Objects.equals(resVm.getStatus(),
                                                                                       ResVmStatus.RUNNING)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2093180350));
        }

        boolean uniqueVmName = checkUniqueVmName(resVm.getCloudEnvId(), cloneAsTemplateDTO.getTemplateName(), 1, false);
        if (!uniqueVmName) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_102684976));
        }

        //1、插入创建中的镜像
        ResImage image = buildResImage(resVm, cloneAsTemplateDTO.getTemplateName());
        image.setStatus(ResImageStatus.CREATING);
        BasicWebUtil.prepareInsertParams(image);
        resImageMapper.insert(image);

        //2、根据所选数据存储挑选主机（查询关联的主机，取第一条）
        Criteria criteria = new Criteria();
        criteria.put("parentTopologySid", cloneAsTemplateDTO.getCloudEnvId());
        criteria.put("resStorageSid", cloneAsTemplateDTO.getResStorageSid());
        List<ResHost> chooseResHosts = resHostMapper.selectHostByStorageSid(criteria);
        if (CollectionUtils.isEmpty(chooseResHosts)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_872911184));
        }
        ResHost chooseResHost = chooseResHosts.parallelStream().findFirst().orElse(new ResHost());

        //3、查询数据中心
        ResZone resZone = getZoneByResHost(resVm);
        AssertUtil.requireNonBlank(resZone, "数据中心不存在或已被删除。");

        //6、发布 task
        DeployTask deployTask = basicDeployTaskService.cloneResVmAsTemplateTask(resVm);

        //5、构建 bean
        CloneVmAsTemplate cloneVmAsTemplate = CloudClientFactory.buildMQBean(resVm.getCloudEnvId(),
                                                                             CloneVmAsTemplate.class);
        cloneVmAsTemplate.setInstanceId(resVm.getInstanceId());
        cloneVmAsTemplate.setInstanceUri(resVm.getUri());
        cloneVmAsTemplate.setTemplateName(cloneAsTemplateDTO.getTemplateName());
        cloneVmAsTemplate.setHostName(chooseResHost.getHostName());
        cloneVmAsTemplate.setStorageName(cloneAsTemplateDTO.getStorageName());
        cloneVmAsTemplate.setFolder(StringUtils.equalsIgnoreCase("-1", cloneAsTemplateDTO.getFolder()) ? null
                                                                                                       : cloneAsTemplateDTO
                                            .getFolder());
        cloneVmAsTemplate.setDataCenter(resZone.getUuid());
        cloneVmAsTemplate.setOptions(ImmutableMap.of("imageId", image.getId(), "taskId", deployTask.getId()));

        ResInstResult resInstResult = sendToMQ(cloneVmAsTemplate);
        if (!resInstResult.getStatus()) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_151523481));
        }

        return image.getId();
    }

    private ResImage buildResImage(ResVm resVm, String templateName) {
        ResVd systemVd = getSystemVd(resVm.getId());

        String managementAccount = null;
        String managementPassword = null;
        String managementPort = null;
        if (ResVmManageStatus.CONNECTED.equalsIgnoreCase(resVm.getManageStatus())) {
            managementAccount = resVm.getManagementAccount();
            managementPassword = resVm.getManagemenPassword();
            managementPort = String.valueOf(resVm.getSshPort());
        }

        //  接管中修改实例后需要同步修改这儿的逻辑，先从实例里面获取osPlatform、osVersion、bit，如果为空则在镜像中获取
        String osType = resVm.getOsCategory();
        String osPlatform = null;
        String osVersion = null;
        Integer bit = null;
        if (StringUtils.isNotBlank(resVm.getImageId())) {
            Criteria example = new Criteria();
            example.put("imageId", resVm.getImageId());
            List<ResImage> originalImages = resImageService.selectByExample(example);
            if (!CollectionUtils.isEmpty(originalImages)) {
                ResImage originalImage = originalImages.parallelStream().findFirst().orElse(new ResImage());
                osPlatform = originalImage.getOsPlatform();
                osVersion = originalImage.getOsVersion();
                bit = originalImage.getBit();
                if (StringUtils.isBlank(osType)) {
                    osType = originalImage.getOsType();
                }
            }
        }

        ResImage image = new ResImage();
        image.setImageName(templateName);
        image.setImageId(templateName);
        image.setSystemDisk(systemVd.getAllocateDiskSize());
        image.setManagementAccount(managementAccount);
        image.setManagementPassword(managementPassword);
        image.setManagementPort(managementPort);
        image.setOsType(osType);
        image.setOsPlatform(osPlatform);
        image.setOsVersion(osVersion);
        image.setBit(bit);
        image.setCloudEnvType(resVm.getCloudEnvType());
        image.setCloudEnvId(resVm.getCloudEnvId());

        return image;
    }

    private ResVd getSystemVd(String resVmId) {
        Criteria criteria = new Criteria();
        criteria.put("resVmId", resVmId);
        List<ResVd> resVds = resVdMapper.selectByParams(criteria);
        return resVds.parallelStream()
                     .filter(vd -> StoragePurpose.SYSTEM_DISK.equalsIgnoreCase(vd.getStoragePurpose()))
                     .findFirst()
                     .orElse(new ResVd());
    }

    @Override
    public List<ResStorage> getVMResStorageFromNativeEnv(String resVmId) {
        ResVm resVm = getResVmAndCheckCloudEnv(resVmId);

        if (StringUtil.isBlank(resVm.getAllocateResHostSid())) {
            VmQueryDataStore vmQueryDataStore = CloudClientFactory.buildMQBean(resVm.getCloudEnvId(),
                                                                               VmQueryDataStore.class);
            if (CloudEnvType.FUSIONCOMPUTE.equals(vmQueryDataStore.getProviderType())) {
                vmQueryDataStore.setId(resVm.getId());
                vmQueryDataStore.setInstanceUri(resVm.getUri());
                vmQueryDataStore.setInstanceUrn(resVm.getUrn());
                VmQueryDataStoreResult vmQueryDataStoreResult = null;
                try {
                    vmQueryDataStoreResult = (VmQueryDataStoreResult) MQHelper.rpc(vmQueryDataStore);
                } catch (MQException e) {
                    throw new BizException(e.getMessage());
                }
                if (Objects.nonNull(vmQueryDataStoreResult.getDatastores())) {
                    Criteria criteria = new Criteria();
                    criteria.put("parentTopologySid", resVm.getCloudEnvId());
                    criteria.put("urns", vmQueryDataStoreResult.getDatastores()
                                                               .stream()
                                                               .map(item -> item.get("urn"))
                                                               .collect(Collectors.toList()));
                    List<ResStorage> resStorages = resStorageMapper.selectByParams(criteria);
                    if (!CollectionUtils.isEmpty(resStorages)) {
                        logger.info("vm:[{}] 从底层获取存储，条数:{}", resVmId, resStorages.size());
                    }
                    return resStorages;
                }
            }
        }

        return null;
    }

    @Override
    public List<Storage> getVMRelationStorages(String resVmId) {
        ResVm resVm = getResVmAndCheckCloudEnv(resVmId);

        if (StringUtil.isBlank(resVm.getAllocateResHostSid())) {
            CloudEnv cloudEnv = cloudEnvService.checkCloudEnvNormal(resVm.getCloudEnvId(), true);
            if (CloudEnvType.FUSIONCOMPUTE.equals(cloudEnv.getCloudEnvType())) {
                List<ResStorage> list = getVMResStorageFromNativeEnv(resVmId);
                return Optional.ofNullable(list).orElse(Collections.emptyList()).stream().map(Storage::new).map(s -> {
                    s.setRelationResHost(true);
                    return s;
                }).collect(Collectors.toList());
            }
        }

        //1、查询实例关联宿主机
        Criteria criteria = new Criteria();
        criteria.put("resHostSid", resVm.getAllocateResHostSid());
        List<ResHost> resHosts = resHostMapper.selectByParams(criteria);
        if (CollectionUtils.isEmpty(resHosts)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_269685843));
        }
        Long resZoneId = resHosts.parallelStream().findFirst().orElse(new ResHost()).getResZoneId();

        //2、查询宿主机数据中心下的所以宿主机
        Criteria example = new Criteria();
        example.put("resZoneId", resZoneId);
        resHosts = resHostMapper.selectByParams(example);

        List<String> resHostSids = resHosts.parallelStream().map(ResHost::getResHostSid).collect(Collectors.toList());

        //3、查询宿主机关联的数据存储
        List<ResStorage> resStorages = resStorageMapper.selectStoByHostSids(resHostSids);
        List<Storage> storageList = resStorages.stream().map(Storage::new).collect(Collectors.toList());
        //4、数据存储是否连接到实例关联的宿主机
        storageList.stream()
                   .filter(storage -> Objects.equals(storage.getResHostSid(), resVm.getAllocateResHostSid()))
                   .forEach(storage -> storage.setRelationResHost(true));

        return storageList;
    }

    @Override
    public JSONArray getVMRelationFolders(String resVmId) {
        ResVm resVm = getResVmAndCheckCloudEnv(resVmId);

        // 查询数据中心
        ResZone resZone = getZoneByResHost(resVm);
        AssertUtil.requireNonBlank(resZone, "数据中心不存在或已被删除。");

        //3、添加根目录
        JSONArray jsonArray = new JSONArray();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("name", "根目录");
        jsonObject.put("val", "-1");
        jsonObject.put("children", JSON.parseArray(resZone.getAttrData()));
        jsonArray.add(jsonObject);

        return jsonArray;
    }

    private ResZone getZoneByResHost(ResVm resVm) {
        Long resZoneId = null;
        if (StringUtil.isBlank(resVm.getAllocateResHostSid())) {
            CloudEnv cloudEnv = this.cloudEnvMapper.selectByPrimaryKey(resVm.getCloudEnvId());
            if (CloudEnvType.FUSIONCOMPUTE.equals(cloudEnv.getCloudEnvType())) {
                String siteUrn = "";
                if (StringUtil.isNotBlank(resVm.getUrn())) {
                    String[] split = resVm.getUrn().split(":");
                    siteUrn = split[0] + ":" + split[1] + ":" + split[2];
                }
                Criteria criteria = new Criteria();
                criteria.put("cloudEnvId", resVm.getCloudEnvId());
                criteria.put("urn", siteUrn);
                List<ResZone> resZones = resZoneMapper.selectByParams(criteria);
                resZoneId = AssertUtil.requireNonBlank(resZones, "关联的可用区不存在").get(0).getId();
            }
        } else {
            Criteria criteria = new Criteria();
            criteria.put("resHostSid", resVm.getAllocateResHostSid());
            List<ResHost> resHosts = resHostMapper.selectByParams(criteria);
            AssertUtil.requireNonBlank(resHosts, "实例关联宿主机不存在");
            resZoneId = resHosts.parallelStream().findFirst().orElse(new ResHost()).getResZoneId();
        }
        return Objects.nonNull(resZoneId) ? resZoneMapper.selectByPrimaryKey(resZoneId) : null;
    }

    @Override
    public Integer removeCloudDeploymentRelation(Long deployId, List<String> hosts) {
        return resVmMapper.removeCloudDeploymentRelation(hosts, deployId);
    }

    @Override
    public ResVm selectBaseByPrimaryKey(String id) {
        return resVmMapper.selectBaseByPrimaryKey(id);
    }

    private ResVm getResVmAndCheckCloudEnv(String resVmId) {
        ResVm resVm = resVmMapper.selectByPrimaryKey(resVmId);
        if (resVm == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_812008144));
        }
        CloudEnv cloudEnv = cloudEnvMapper.selectByPrimaryKey(resVm.getCloudEnvId());
        if (cloudEnv == null) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1727217032));
        }
        if (!CloudEnvType.VMWARE.equals(cloudEnv.getCloudEnvType()) && !CloudEnvType.FUSIONCOMPUTE.equals(
                cloudEnv.getCloudEnvType())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1706975220));
        }
        return resVm;
    }

    @Override
    public List<ResVm> getCloudHostForExport(Criteria criteria) {
        List<ResVm> resVms = this.selectHostByParamsByPermision(criteria);

        List<Long> cloudEnvIds = resVms.stream().map(ResVm::getCloudEnvId).distinct().collect(Collectors.toList());

        Map<String, String> zoneMap = Maps.newHashMap();
        if (!CollectionUtil.isEmpty(cloudEnvIds)) {
            Criteria zoneCriteria = new Criteria();
            zoneCriteria.put("cloudEnvIds", cloudEnvIds);
            List<ResZone> resZones = this.resZoneService.selectByParams(zoneCriteria);

            zoneMap = resZones.stream()
                              .collect(Collectors.toMap(resZone -> resZone.getCloudEnvId() + resZone.getUuid(),
                                                        ResZone::getName, (k1, k2) -> k1));
        }

        Map<String, String> serverTypeMap = Maps.newHashMap();
        serverTypeMap.put(ServerType.INSTANCE, "实例");
        serverTypeMap.put(ServerType.SERVER_INSTANCE, "服务器实例");
        serverTypeMap.put(ServerType.SERVER, "服务器");
        serverTypeMap.put(ServerType.ELASTIC_INSTANCE, "弹性伸缩组实例");
        for (ResVm resVm : resVms) {
            resVm.setServerType(serverTypeMap.get(resVm.getServerType()));
            if (!Objects.isNull(resVm.getStartTime())) {
                resVm.setDateDiff(DateUtil.parseDateDiff(resVm.getStartTime()));
            }

            // 重新设置zone
            resVm.setZone(zoneMap.getOrDefault(resVm.getCloudEnvId() + Strings.nullToEmpty(resVm.getZone()),
                                               resVm.getZone()));
            resVm.setTagNames(TagUitl.mergeTag(resVm.getTagNames(), resVm.getTagValues()));

            resVm.setDescription(cn.com.cloudstar.rightcloud.module.support.access.util.StringUtil.specailCharacterFilter(resVm.getDescription()));
        }

        return resVms;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> createResVm(JsonNode parameters, CloudEnv cloudEnv, Integer hostCnt,
                                    Map<String, Object> suffixMap) {
        final String account = BasicInfoUtil.getAuthUser().getAccount();
        Long userSid = RequestContextUtil.getUserIdFromAccount(account);

        CloudCommonInst cloudVmInst = new CloudCommonInst();
        Long orgSid = BasicInfoUtil.getCurrentOrgSid();
        cloudVmInst.setOrgSid(orgSid);
        cloudVmInst.setUserId(userSid.toString());
        cloudVmInst.setUserAccount(account);
        cloudVmInst.setEnvId(cloudEnv.getId());
        cloudVmInst.setEnvType(cloudEnv.getCloudEnvType());
        if (!Objects.isNull(parameters.get("cloudDeploymentId"))) {
            cloudVmInst.setCloudDeploymentId(Long.valueOf(String.valueOf(parameters.get("cloudDeploymentId"))));
        }

        if (!CloudEnvType.VMWARE.equals(cloudEnv.getCloudEnvType())) {
            orgRemoteService.setOwnerOrgSid(cloudVmInst);
        }

        List<String> hostIds = Lists.newArrayList();
        List<VmCreate> vmCreates = Lists.newArrayList();

        List<String> suffixList = (List<String>) suffixMap.get("suffixList");
        String splitStr = (String) suffixMap.get("splitStr");

        for (int i = 1; i <= hostCnt; i++) {
            // 多台主机创建时加上尾部序列，hostName加上3位序列号
            JsonNode param = parameters.deepCopy();
            //如果配置了后缀，则用配置的后缀设置名称
            ObjectNode node = (ObjectNode) param.findValue("instance");
            if (CollectionUtils.isEmpty(suffixList)) {
                if (i > 1) {
                    node.put("instanceName", String.format("%s-%d", node.get("instanceName").textValue(), i - 1));
                    node.put("index", i);
                    cloudVmInst.setCloudSpec(param);
                } else {
                    cloudVmInst.setCloudSpec(param);
                }
            } else {
                String format = "%s%s";
                if (!StringUtil.isNullOrEmpty(splitStr)) {
                    format = "%s" + splitStr + "%s";
                }
                node.put("instanceName",
                         String.format(format, node.get("instanceName").textValue(), suffixList.get(i - 1)));
                if (i > 1) {
                    node.put("index", i);
                }
                cloudVmInst.setCloudSpec(param);
            }
            if (i > 1) {
                // 多台主机创建，除了第一台主机，其他都加上notFirst标识不是第一台主机
                cloudVmInst.setOptions(ImmutableMap.of("notFirst", true));
            }

            VmCreate vmCreate = this.createResVm(cloudVmInst);
            vmCreates.add(vmCreate);
            hostIds.add(vmCreate.getId());
        }

        // 组装完成之后，逐个下发MQ
        vmCreates.forEach(this::send2MQ4VmCreate);

        return hostIds;
    }

    @Override
    public void judgeIsCreateInstanceBySaaSLimit(int hostCnt, Long cloudEnvId) {
        CloudEnv env = cloudEnvService.checkCloudEnvNormal(cloudEnvId, true);
        judgeIsCreateInstanceBySaaSLimit(hostCnt, CloudEnvType.from(env.getCloudEnvType()).isPublic());
    }

    @Override
    public void judgeIsCreateInstanceBySaaSLimit(int hostCnt, boolean isPubEnv) {
        if (!SaasUtil.isSaasEnable()) {
            return;
        }
        Long currentMainOrgSid = BasicInfoUtil.getCurrentMainOrgSid();
        if (Objects.nonNull(currentMainOrgSid)) {
            SysSaasOrgParams sysSaasOrgParams = new SysSaasOrgParams();
            sysSaasOrgParams.setOrgSid(currentMainOrgSid);
            int count = sysSaasOrgRemoteService.countByParams(sysSaasOrgParams);
            if (count <= 0) {
                return;
            }
        }

        SysSaasOrgParams sysSaasOrgParams = new SysSaasOrgParams();
        sysSaasOrgParams.setOrgSid(currentMainOrgSid);

        List<SysSaasOrg> sysSaasOrgs = sysSaasOrgRemoteService.selectByParams(sysSaasOrgParams);
        if (!CollectionUtils.isEmpty(sysSaasOrgs)) {
            if (!isPubEnv && sysSaasOrgs.get(0).isPriEnvInsNoLimit()) {
                // sass 私有云未限制虚拟机数量，不判断实例数
                logger.info("SASS私有云未配置虚拟机限制，忽略创建时校验...");
                return;
            }
        }

        Criteria criteria = new Criteria();
        criteria.put("statusNotIn", Arrays.asList(ResVmStatus.DELETING, ResVmStatus.CREATE_FAILURE, ResVmStatus.PENDING,
                                                  ResVmStatus.DELETED));
        criteria.put("serverTypeIn", Arrays.asList(ServerType.SERVER_INSTANCE, ServerType.INSTANCE, ServerType.SERVER,
                                                   ServerType.ELASTIC_INSTANCE));
        criteria.put("ignoreRecycle", true);
        List<CloudEnvType> envTypes = isPubEnv ? CloudEnvType.getPublicCloud() : CloudEnvType.getPrivateCloud();
        List<String> cloudEnvTypes = envTypes.stream()
                                             .flatMap(et -> et.getValue().stream())
                                             .collect(Collectors.toList());
        criteria.put("cloudEnvTypes", cloudEnvTypes);

        criteria.put("df", BasicInfoUtil.getSQLFilterWithGivingOrgSid("A", "org_sid", currentMainOrgSid));
        int existInstanceCount = resVmMapper.countLicenseNormalHost(criteria);

        // 尝试从saas的组织表中获取实例数量限制，如果没有设置，那么从配置表获取全局限制
        Integer instanceNumLimitedBySaaSConfig = sysSaasOrgs.stream()
                                                            .map(saas -> isPubEnv ? saas.getInstanceNum()
                                                                                  : saas.getPrivateInstanceCount())
                                                            .filter(Objects::nonNull)
                                                            .findFirst()
                                                            .orElse(Convert.toInt(
                                                                    PropertiesUtil.getProperty(SaasUtil.INSTANCE_NUM),
                                                                    0));

        if (existInstanceCount + hostCnt > instanceNumLimitedBySaaSConfig) {
            throw new BizException(
                    StrUtil.format("体验用户允许管理的实例数量为[{}]，如果需要管理更多实例，请联系售前购买升级。", instanceNumLimitedBySaaSConfig));
        }
    }

    private void deleteDisk(ResVd resVd, String opUser, Date endTime) {
        Criteria criteria = new Criteria();
        criteria.put("resVdId", resVd.getResVdSid());
        List<ResSnapshot> resSnapshots = resSnapshotService.selectByParams(criteria);
        String cloudEnvType = "";
        if (CollectionUtil.isNotEmpty(resSnapshots)) {
            Long cloudEnvId = resSnapshots.get(0).getCloudEnvId();
            CloudEnv cloudEnv = cloudEnvService.selectByPrimaryKey(cloudEnvId);
            assertEnvExists(cloudEnv);
            cloudEnvType = cloudEnv.getCloudEnvType();
        }
        // vmware 默认随实例删除, 不需再做删除
        String envType = resVd.getCloudEnvType();
        boolean apiDeleteFlag = Arrays.asList(CloudEnvType.VMWARE, CloudEnvType.QCLOUD, CloudEnvType.FUSIONCOMPUTE,
                                              CloudEnvType.HUAWEICLOUD, CloudEnvType.OPEN_STACK, CloudEnvType.MAAS)
                                      .stream()
                                      .noneMatch(type -> type.equals(envType));
        if (!apiDeleteFlag) {
            // 华为云数据盘应该从底层删除 ps:上面的代码只是为了保持原有逻辑不变
            if (CloudEnvType.HUAWEICLOUD.getValue().contains(envType) && StoragePurpose.DATA_DISK.equals(
                    resVd.getStoragePurpose())) {
                apiDeleteFlag = true;
            }
        }
        if (CloudEnvType.KING_STACK.getValue().contains(envType) || CloudEnvType.KSYUN.getValue().contains(envType)) {
            apiDeleteFlag = false;
        }
        if (apiDeleteFlag) {
            // 随实例释放删除底层硬盘
            logger.info("{}@{} 单独删除硬盘", resVd.getResVdSid(), envType);
            resourceService.deleteVolume(resVd.getResVdSid(), false, false, false);
        } else {
            logger.info("{}@{} 默认硬盘随实例删除，跳过单独删除", resVd.getResVdSid(), envType);
        }
        basicResActionLogService.insertIntoActionLog(resVd.getResVdSid(), opUser, ResourceTypeEnum.VD,
                                                     ResourceOperateEnum.DELETE, Boolean.TRUE);
        // 将硬盘更新为已删除
        resVd.setStatus(ResVdStatus.DELETED);
        resVd.setEndTime(endTime);

        this.resVdMapper.updateByPrimaryKey(resVd);
    }

    private void deleteSnapshot(String resVmId) {
        Criteria criteria = new Criteria();
        criteria.put("resVmId", resVmId);
        List<ResSnapshot> resSnapshots = resSnapshotService.selectByParams(criteria);
        if (CollectionUtil.isEmpty(resSnapshots)) {
            return;
        }
        Long cloudEnvId = resSnapshots.get(0).getCloudEnvId();
        if (Objects.isNull(cloudEnvId)) {
            return;
        }
        CloudEnv cloudEnv = cloudEnvService.selectByPrimaryKey(cloudEnvId);
        assertEnvExists(cloudEnv);
        if (!CloudEnvType.ESCLOUD.equals(cloudEnv.getCloudEnvType())) {
            resSnapshotService.deleteByResVmId(resVmId);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean mutiRemoveVmFromZone(List<String> hostIds, long envId) {
        CloudEnv cloudEnv = cloudEnvService.selectByPrimaryKey(envId);
        assertEnvExists(cloudEnv);
        if (!CloudEnvType.VMWARE.equals(cloudEnv.getCloudEnvType()) && !CloudEnvType.FUSIONCOMPUTE.equals(
                cloudEnv.getCloudEnvType())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1480814017));
        }
        return resVmMapper.mutiUpdateCloudHostZone(hostIds, null) > 0;
    }

    @Override
    public List<CloudEnv> selectVmEnvs(Criteria criteria) {
        return resVmMapper.selectVmEnvs(criteria);
    }

    @Override
    public Map<String, Object> getVmNameSuffix(Long cloudEnvId, String instanceName, int size) {
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> instanceNameRules = companyRemoteService.queryInstanceNameRules();
        boolean fixFlag = false;
        if (instanceNameRules.containsKey("configInfo")) {
            List<MgtObjExt> instNameRules = (List<MgtObjExt>) instanceNameRules.get("configInfo");
            Optional<MgtObjExt> suffixExt = instNameRules.stream()
                                                         .filter(ext -> InstanceNameRuleFix.SUFFIX.equalsIgnoreCase(
                                                                 ext.getPosition()))
                                                         .findFirst();
            //后缀识别--后缀有可能修改，存在‘.’、‘-’、‘_’、‘’四种情况
            if (suffixExt.isPresent()) {
                fixFlag = true;
                result.put("splitStr", suffixExt.get().getSplit());
                List<String> suffixList = new ArrayList<>();
                int suffixLength = suffixExt.get().getSuffixLength();
                if (InstanceNameSuffix.AUTO_INCRE.equalsIgnoreCase(suffixExt.get().getAttrValue())) {
                    int index = 1;
                    Criteria example = new Criteria("instanceNameLike", instanceName);
                    example.put("cloudEnvId", cloudEnvId);
                    example.setOrderByClause("A.CREATED_DT desc");
                    List<ResVm> resVmList = resVmMapper.selectHostSimpleInfoByParams(example);
                    if (!CollectionUtils.isEmpty(resVmList)) {
                        int nameIndex = 0;
                        //后缀识别--后缀有可能修改，存在‘.’、‘-’、‘_’、‘’四种情况
                        for (ResVm resVm : resVmList) {
                            String[] nameFixArr = resVm.getInstanceName().split(instanceName);
                            if (nameFixArr.length < 2) {
                                continue;
                            }
                            String[] pointArr = nameFixArr[1].split("\\.");
                            String pointFix = pointArr.length > 1 ? pointArr[pointArr.length - 1] : "0";
                            String[] lineArr = nameFixArr[1].split("\\-");
                            String lineFix = lineArr.length > 1 ? lineArr[lineArr.length - 1] : "0";
                            String[] underlineArr = nameFixArr[1].split("\\_");
                            String underFix = underlineArr.length > 1 ? underlineArr[underlineArr.length - 1] : "0";

                            try {
                                nameIndex = Math.max(nameIndex, Integer.parseInt(pointFix));
                                nameIndex = Math.max(nameIndex, Integer.parseInt(lineFix));
                                nameIndex = Math.max(nameIndex, Integer.parseInt(underFix));
                            } catch (Exception e) {
                                //随机字符后缀跳过
                            }
                            if (nameIndex == 0) {
                                try {
                                    nameIndex = Integer.parseInt(nameFixArr[1]);
                                } catch (Exception e) {
                                    //随机字符后缀跳过
                                }
                            }
                        }

                        index = nameIndex + 1;
                    }
                    for (int i = 0; i < size; i++) {
                        suffixList.add(String.format("%0" + suffixLength + "d", index + i));
                    }
                } else if (InstanceNameSuffix.RANDOM_STR.equalsIgnoreCase(suffixExt.get().getAttrValue())) {
                    for (int i = 0; i < size; i++) {
                        suffixList.add(RandomStringUtils.randomAlphanumeric(suffixLength));
                    }
                }
                result.put("suffixList", suffixList);
            }
        }

        return result;
    }

    private void updateVd(ResVm resVm) {
        if (Strings.isNullOrEmpty(resVm.getId())) {
            return;
        }
        Criteria criteria = new Criteria();
        criteria.put("resVmId", resVm.getId());
        criteria.put("storagePurpose", StoragePurpose.SYSTEM_DISK);
        List<ResVd> resVds = this.resVdMapper.selectByParams(criteria);
        if (CollectionUtil.isNotEmpty(resVds)) {
            resVds.forEach(resVd -> {
                resVd.setStartTime(resVm.getStartTime());
                resVd.setEndTime(resVm.getEndTime());
                this.resVdMapper.updateByPrimaryKey(resVd);
            });
        }
    }

    private void updateVd(List<ResVd> list) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        list.forEach(disk -> this.resVdMapper.updateByPrimaryKeySelective(disk));
    }

    private String getHostNameByFixInfo(String instanceName, Long orgSid, String osCategory) {
        // 如果hostName为空，取默认前缀
        String defaultHostName = "";
        String configInfoKey = "configInfo";
        Map<String, Object> hostNameRuleMap = Objects.isNull(orgSid) ? companyRemoteService.queryHostNameRules()
                                                                     : companyRemoteService.queryHostNameRules(orgSid);
        if (hostNameRuleMap.containsKey(configInfoKey)) {
            List<MgtObjExt> hostNameRules = (List<MgtObjExt>) hostNameRuleMap.get(configInfoKey);

            Collection<MgtObjExt> hostNameConfig = hostNameRules.stream()
                                                                .filter(o -> "true".equals(o.getFlag()))
                                                                .collect(Collectors.toList());

            //判断是否使用的默认规则
            Optional<MgtObjExt> first = hostNameConfig.stream().filter(o -> "default".equals(o.getType())).findFirst();
            //使用默认配置
            if (first.isPresent()) {
                //判断系统类型
                if (OsType.WINDOWS.equalsIgnoreCase(osCategory)) {
                    String winHostName = instanceName;
                    String suffix = "";
                    String sufSplit = "";
                    //获取实例名称规则
                    Map<String, Object> instanceNameRuleMap = companyRemoteService.queryInstanceNameRules();
                    if (instanceNameRuleMap.containsKey(configInfoKey)) {
                        List<MgtObjExt> instNameRules = (List<MgtObjExt>) instanceNameRuleMap.get(configInfoKey);
                        Optional<MgtObjExt> suffixExt = instNameRules.stream()
                                                                     .filter(ext -> InstanceNameRuleFix.SUFFIX.equalsIgnoreCase(
                                                                             ext.getPosition()))
                                                                     .findFirst();
                        //有后缀且有分隔符
                        if (suffixExt.isPresent() && !StringUtil.isNullOrEmpty(suffixExt.get().getSplit())) {
                            sufSplit = suffixExt.get().getSplit();
                            String[] split = instanceName.split(StringUtil.BACKSLASH + sufSplit);
                            suffix = split[split.length - 1];
                            winHostName = instanceName.split(sufSplit + suffix)[0];

                        }
                        //默认Windows的系统名称最长为15
                        int winLength = 15 - (sufSplit + suffix).length();
                        String winHostNamePrefix = "";
                        //使用配置的分隔符获取用户输入部分
                        List<MgtObjExt> prefixExt = instNameRules.stream()
                                                                 .filter(ext -> InstanceNameRuleFix.PREFIX.equalsIgnoreCase(
                                                                         ext.getPosition()))
                                                                 .collect(Collectors.toList());
                        if (!CollectionUtils.isEmpty(prefixExt)) {
                            String prefix = "";
                            //返回结果是排序的，取最后一个前缀即可
                            for (int i = prefixExt.size() - 1; i >= 0; i--) {
                                MgtObjExt mgtObjExt = prefixExt.get(i);
                                if (!StringUtil.isNullOrEmpty(mgtObjExt.getSplit())) {
                                    if (i == prefixExt.size() - 1) {
                                        String[] nameArr = winHostName.split(
                                                StringUtil.BACKSLASH + mgtObjExt.getSplit());
                                        String attr = nameArr[nameArr.length - 1];
                                        String prefixStr = nameArr.length > 1 ? winHostName.split(attr)[0] : "";
                                        String attrStr = "";
                                        if ((winHostNamePrefix + attrStr).length() < winLength) {
                                            winHostNamePrefix = attrStr + winHostNamePrefix;
                                            prefix = StringUtil.isNullOrEmpty(prefixStr) ? "" : prefixStr.substring(0,
                                                                                                                    prefixStr
                                                                                                                            .length()
                                                                                                                            - 1);
                                        } else {
                                            break;
                                        }
                                    } else {
                                        if (StringUtil.isNullOrEmpty(prefix)) {
                                            break;
                                        }
                                        String[] nameArr = prefix.split(mgtObjExt.getSplit());
                                        String attr = nameArr[nameArr.length - 1];
                                        String prefixStr = nameArr.length > 1 ? prefix.split(attr)[0] : "";
                                        // 运营没有用先删掉
                                        String attrStr = "";
                                        if ((winHostNamePrefix + attrStr).length() < winLength) {
                                            winHostNamePrefix = attrStr + mgtObjExt.getSplit() + winHostNamePrefix;
                                            prefix = StringUtil.isNullOrEmpty(prefixStr) ? "" : prefixStr.substring(0,
                                                                                                                    prefixStr
                                                                                                                            .length()
                                                                                                                            - 1);
                                        } else {
                                            break;
                                        }
                                    }
                                }
                            }
                            if (!StringUtil.isNullOrEmpty(prefix)
                                    && (winHostNamePrefix.length() + prefix.length()) < winLength) {
                                winHostNamePrefix = prefix + prefixExt.get(0).getSplit() + winHostNamePrefix;
                            } else if (StringUtil.isNullOrEmpty(prefix) && StringUtil.isNullOrEmpty(
                                    winHostNamePrefix)) {
                                String namePrefix = winHostName.split(prefixExt.get(0).getSplit())[0];
                                if (namePrefix.length() < winLength) {
                                    winHostNamePrefix = namePrefix;
                                }
                            }
                        }
                        if (StringUtil.isNullOrEmpty(winHostNamePrefix)) {
                            if (instanceName.length() <= 15) {
                                defaultHostName = instanceName;
                            } else {
                                String orgCode = BasicInfoUtil.getCurrentOrg().getOrgCode();
                                if (orgCode.length() < winLength && (orgCode + sufSplit + suffix).length() <= 15) {
                                    winHostNamePrefix = orgCode;
                                } else {
                                    winHostNamePrefix = "cmp";
                                }
                            }
                        }
                        if (StringUtil.isNullOrEmpty(suffix)) {
                            suffix = RandomStringUtils.randomAlphanumeric(5);
                            sufSplit = "-";
                        }
                        if (StringUtil.isNullOrEmpty(defaultHostName)) {
                            defaultHostName = winHostNamePrefix + sufSplit + suffix;
                        }
                    } else {
                        //配置信息不存在，使用系统默认
                        defaultHostName = "cmp-" + RandomStringUtils.randomAlphanumeric(8);
                    }
                } else {
                    defaultHostName = "cmp";
                }
            } else {
                // 使用自定义配置，但是页面没有传值
                List<MgtObjExt> otherExt = hostNameConfig.stream()
                                                         .filter(o -> "other".equals(o.getType()))
                                                         .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(otherExt)) {
                    String defaultName = "";
                    for (MgtObjExt ext : otherExt) {
                        defaultName = defaultName + ext.getAttrValue() + "-";
                    }
                    defaultHostName = (StringUtil.isNullOrEmpty(defaultName) ? "cmp-" : defaultName);

                    if (OsType.WINDOWS.equalsIgnoreCase(osCategory)) {
                        int index = 15 - defaultHostName.length();
                        if (index > 0) {
                            defaultHostName = defaultHostName + RandomStringUtils.randomAlphanumeric(index);
                        } else {
                            String str = defaultHostName.split("-")[0];
                            String prefixStr = defaultHostName.split(str + "-")[1];
                            defaultHostName = prefixStr + RandomStringUtils.randomAlphanumeric(15 - prefixStr.length());
                        }
                    } else {
                        defaultHostName = defaultHostName + RandomStringUtils.randomAlphanumeric(5);
                    }

                }
            }
        }
        if (StringUtil.isNullOrEmpty(defaultHostName)) {
            defaultHostName = instanceName;
        }
        return defaultHostName.replaceAll("\\.", "-").replaceAll("\\_", "-");
    }

    @Override
    public ResVm selectByPhysicalHostPoolId(Long physicalHostPoolId) {
        Criteria criteria = new Criteria();
        criteria.put("physicalHostPoolId", physicalHostPoolId);
        criteria.put("statusNotIn", Arrays.asList(ResVmStatus.DELETED));
        criteria.put("notInRecycle", "1");
        List<ResVm> cloudHosts = this.resVmMapper.selectByPhysicalHostPoolId(criteria);
        return (CollectionUtil.isNotEmpty(cloudHosts)) ? cloudHosts.get(0) : null;
    }

    @Override
    public int updateResVmStatusByPhysicalHostPoolId(Long physicalHostPoolId, String resVmStatus) {
        return this.resVmMapper.updateResVmStatusByPhysicalHostPoolId(physicalHostPoolId, resVmStatus);
    }

    @Override
    @Transactional
    public void resVmReInstallSystem(ResVmReInstallSystemDTO resVmReInstallSystemDTO) {
        InstanceModel instance = resVmReInstallSystemDTO.getInstance();
        ResVm resVmOriginal = this.resVmMapper.selectBaseByPrimaryKey(resVmReInstallSystemDTO.getId());
        ResImage resImage = resImageMapper.selectByPrimaryKey(instance.getImageId());
        CloudEnv cloudEnv = this.cloudEnvMapper.selectByPrimaryKey(resVmOriginal.getCloudEnvId());
        resVmOriginal.setCloudEnvType(cloudEnv.getCloudEnvType());

        ResVm resVmUpdate = new ResVm();
        resVmUpdate.setId(resVmReInstallSystemDTO.getId());
        resVmUpdate.setStatus(ResVmStatus.REINSTALL_SYSTEM_ING);
        if (Strings.isNullOrEmpty(resVmOriginal.getOwnerId())) {
            resVmUpdate.setOwnerId(String.valueOf(BasicInfoUtil.getAuthUser().getUserSid()));
        }
        resVmUpdate.setManageStatus(ResVmManageStatus.UNUNITED);
        AuthModel auth = instance.getAuth();
        if (Objects.nonNull(auth)) {
        }
        this.resVmMapper.updateByPrimaryKeySelective(resVmUpdate);

        if (false && CloudEnvType.MAAS.equals(cloudEnv.getCloudEnvType())) {
            JsonNode jsonNode = JsonUtil.fromJson(resVmOriginal.getOriginParam());
            if (Objects.isNull(jsonNode)) {
                jsonNode = JsonUtil.fromJson("{}");
            }
            List<InterfaceItemModel> interfaces = resVmReInstallSystemDTO.getInterfaces();
            ((ObjectNode) jsonNode).put("interfacesJson", JsonUtil.toJson(interfaces));
            VmCreate vmCreate = makeVmCreateBean(resVmOriginal, new HashMap<>());
            send2MQ4VmCreate(vmCreate);
        } else {
            VmReinstallSystem vmReinstallSystem = CloudClientFactory.buildMQBean(resVmOriginal.getCloudEnvId(),
                                                                                 VmReinstallSystem.class);
            vmReinstallSystem.setResVmId(resVmReInstallSystemDTO.getId());
            vmReinstallSystem.setResVmName(resVmOriginal.getInstanceName());
            vmReinstallSystem.setInstanceId(resVmOriginal.getInstanceId());
            vmReinstallSystem.setImageUuid(resImage.getImageId());
            vmReinstallSystem.setImageSid(instance.getImageId());
            vmReinstallSystem.setAdminPass(CrytoUtilSimple.decrypt(auth.getPassword(), false));
            vmReinstallSystem.setCloudEnvId(resVmOriginal.getCloudEnvId());
            vmReinstallSystem.setHostName(instance.getHostName());

            vmReinstallSystem.setRemoteLoginType(auth.getRemoteLoginType());
            vmReinstallSystem.setManagementAccount(StringUtil.isBlank(auth.getUser()) ? "root" : auth.getUser());
            if (StringUtil.isNotBlank(auth.getKeyPair())) {
                vmReinstallSystem.setKeypairId(Long.parseLong(auth.getKeyPair()));
            }

            if (CollectionUtil.isNotEmpty(resVmReInstallSystemDTO.getInterfaces())) {
                List<VmNic> vmNics = new ArrayList<>();
                for (InterfaceItemModel interfaceItemModel : resVmReInstallSystemDTO.getInterfaces()) {
                    VmNic vmNic = new VmNic();
                    vmNic.setInterfaceId(interfaceItemModel.getInterfaceUuid());
                    vmNic.setInterfaceName(Objects.requireNonNull(interfaceItemModel.getInterfaceName(), "网卡名称为空."));
                    vmNic.setNetId(String.valueOf(interfaceItemModel.getSubnetId()));
                    vmNic.setSubnetId(interfaceItemModel.getSubnetUuid());
                    if (StringUtils.equalsIgnoreCase("eno1", interfaceItemModel.getInterfaceName())) {
                        vmNic.setPrivateIp(resVmOriginal.getInnerIp());
                    }
                    vmNics.add(vmNic);
                }
                vmReinstallSystem.setNics(vmNics);
            }
            if (Objects.nonNull(resVmOriginal.getPhysicalHostPoolId())) {
                CloudPhysicalHostPool cloudPhysicalHostPool = this.cloudPhysicalHostPoolMapper.selectByPrimaryKey(
                        resVmOriginal.getPhysicalHostPoolId());
                vmReinstallSystem.setMaas(convertMaasVm(cloudPhysicalHostPool));
            }
            if (Objects.nonNull(resVmUpdate.getKeypairId())) {
                CloudKeyPair keyPair = this.cloudKeyPairMapper.selectByPrimaryKey(resVmUpdate.getKeypairId());
                AssertUtil.requireNonBlank(keyPair, "密钥不能为空");
                vmReinstallSystem.setKeypair(new KeyVo() {{
                    setName(keyPair.getKeypairName());
                    setPublicKey(keyPair.getPublicKey());
                    setFingerprint(keyPair.getFingerprint());
                    setPrivateKey(keyPair.getPrivateKey());
                }});
            }

            // 接管任务编排中，有判断实例操作系统的操作，需要设置最新的操作系统
            resVmOriginal.setOsCategory(resImage.getOsType());
            // setup task
            List<DeployTask> deployTasks = basicDeployTaskService.reinstallSystemTask(resVmOriginal);

            // setup playbook
            DeployPlaybook deployPlaybook = this.basicDeployPlaybookService.setupPlaybook(deployTasks);

            String message = MessageUtil.getLogMessage("开始重装操作系统.");
            MongoUtil.save(new LogInfo(resVmOriginal.getId(), message), DeployConst.HOST_LOG_KEY_PREFIX);

            // options
            Map<String, Object> resVmOriginalInfo = new HashMap<>();
            resVmOriginalInfo.put("status", resVmOriginal.getStatus());
            resVmOriginalInfo.put("remoteLoginType", resVmOriginal.getRemoteLoginType());
            resVmOriginalInfo.put("managementAccount", resVmOriginal.getManagementAccount());
            resVmOriginalInfo.put("keypairId", resVmOriginal.getKeypairId());
            resVmOriginalInfo.put("managemenPassword", resVmOriginal.getManagemenPassword());
            vmReinstallSystem.setOptions(
                    ImmutableMap.of("taskId", deployTasks.get(0).getId(), "playbookId", deployPlaybook.getPid(),
                                    "resVmOriginalInfo", resVmOriginalInfo));
            // 发送消息
            sendToMQ(vmReinstallSystem);
        }
    }

    @Override
    public void resetResVmVncPassword(ResVmResetVncPasswordDTO resetVncPasswordDTO) {
        ResVm resVm = resVmMapper.selectByPrimaryKey(resetVncPasswordDTO.getResVmId());
        ResetVncPassword resetVncPassword = CloudClientFactory.buildMQBean(resVm.getCloudEnvId(),
                                                                           ResetVncPassword.class);
        resetVncPassword.setInstanceId(resVm.getInstanceId());
        resetVncPassword.setInstanceUri(resVm.getUri());
        resetVncPassword.setNewPassword(resetVncPasswordDTO.getNewPassword());
        resetVncPassword.setOptions(ImmutableMap.of("resVmId", resVm.getId()));
        sendToMQ(resetVncPassword);
    }

    @Override
    @Transactional
    public boolean lockInstance(List<String> resVmIds, String lockStatus) {
        resVmIds.forEach(resVmId -> {
            ResVm vmInDB = resVmMapper.selectByPrimaryKey(resVmId);
            if (null == vmInDB) {
                return;
            }
            if (StringUtil.isNotBlank(vmInDB.getLockStatus())) {
                // 已加锁 -> 解锁, 检测解锁用户是否加锁用户
                if (ResVmStatus.LOCKED.equalsIgnoreCase(vmInDB.getLockStatus())
                        && ResVmStatus.DEBLOCKING.equalsIgnoreCase(lockStatus)) {
                    if (!Objects.equals(vmInDB.getLockUser(), BasicInfoUtil.getAuthUser().getUserSid())) {
                        throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1191715784));
                    }
                }
                // 已加锁 -> 加锁，检测加锁用户是否相同
                if (ResVmStatus.LOCKED.equalsIgnoreCase(vmInDB.getLockStatus()) && ResVmStatus.LOCKED.equalsIgnoreCase(
                        lockStatus)) {
                    if (!Objects.equals(vmInDB.getLockUser(), BasicInfoUtil.getAuthUser().getUserSid())) {
                        throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1558315585));
                    }
                }
            }
            ResVm resVm = new ResVm();
            resVm.setId(resVmId);
            resVm.setLockStatus(lockStatus);
            resVm.setLockUser(BasicInfoUtil.getAuthUser().getUserSid());
            BasicWebUtil.prepareUpdateParams(resVm);
            this.resVmMapper.updateByPrimaryKeySelective(resVm);
        });
        return true;
    }

    @Override
    public List<ResGpuGroup> selectGpuGroupInHost(Criteria criteria) {
        return resGpuGroupMapper.selectByParamsRelation(criteria);
    }

    @Override
    public void recycleBaremetal(ResVm resVm) {
        List<User> users = this.userRemoteService.findByAccount(resVm.getCreatedBy());
        this.recycleBaremetal(resVm, CollectionUtil.isNotEmpty(users) ? users.get(0) : null);
    }

    @Override
    public void recycleBaremetal(ResVm resVm, User currentOwnUser) {
        CloudEnv restoreCloudEnv = this.cloudEnvMapper.selectByPrimaryKey(resVm.getCloudEnvId());
        List<User> users = this.userRemoteService.findByAccount(restoreCloudEnv.getCreatedBy());
        User restoreUser = CollectionUtil.isNotEmpty(users) ? users.get(0) : null;

        ResVm resVmUpdate = new ResVm();
        resVmUpdate.setId(resVm.getId());
        if (Objects.nonNull(restoreUser)){
            resVmUpdate.setOwnerId(Objects.nonNull(restoreUser) ? restoreUser.getUserSid().toString() : "");
        }
        resVmUpdate.setCreatedBy(restoreCloudEnv.getCreatedBy());
        resVmUpdate.setOrgSid(restoreCloudEnv.getOrgSid());
        resVmUpdate.setStatus("running");
        this.resVmMapper.updateByPrimaryKeySelective(resVmUpdate);

        CloudPhysicalHostPool cloudPhysicalHostPoolUpdate = new CloudPhysicalHostPool();
        cloudPhysicalHostPoolUpdate.setId(resVm.getPhysicalHostPoolId());
        cloudPhysicalHostPoolUpdate.setAllocStatus(CloudPhysicalHostPoolAllocStatus.UNUSED);
        BasicWebUtil.prepareUpdateParams(cloudPhysicalHostPoolUpdate);
        this.cloudPhysicalHostPoolMapper.updateByPrimaryKeySelective(cloudPhysicalHostPoolUpdate);
    }


    @Override
    public List<ResVm> selectUnusedBaremetal(Criteria criteria) {
        return this.resVmMapper.selectUnusedBaremetal(criteria);
    }

    @Override
    public List<ResVm> selectByCriteria(Criteria criteria) {
        return this.resVmMapper.selectByCriteria(criteria);
    }

    @Override
    public List<ResVm> selectIdsByExample(Criteria example) {
        return this.resVmMapper.selectIdsByExample(example);
    }

    @Override
    public int removeCloudDeploymentRelation(List<String> resVmIds, Long deploymentId) {
        return resVmMapper.removeCloudDeploymentRelation(resVmIds, deploymentId);
    }

    @Override
    public List<ResVm> countStatusByCriteria(Criteria criteria) {
        return resVmMapper.countStatusByCriteria(criteria);
    }

    @Override
    public long countSoonExpiredHost(Criteria criteria) {
        return resVmMapper.countSoonExpiredHost(criteria);
    }

    @Override
    public long countExpiredHost(Criteria criteria) {
        return resVmMapper.countExpiredHost(criteria);
    }

    @Override
    public List<ResVm> selectByParamsWithoutFilter(Criteria criteria) {
        return resVmMapper.selectByParamsWithoutFilter(criteria);
    }

    @Override
    public String fortressRemoteConnection(String resVmId) throws Exception {
        final ResVm resVm = resVmMapper.selectBaseByPrimaryKey(resVmId);
        if (null == resVm) {
            throw new BizException(String.format(WebUtil.getMessage(MsgCd.ERROR_RES_NOT_FOUND), WebUtil.getMessage(MsgCd.ERR_MSG_BSS_747437)));
        }

        return fortressService.fortressRemoteConnection(resVm);
    }

    @Override
    public int countMemory(Criteria example) {
        return resVmMapper.countMemory(example);
    }

    @Override
    public int countCpu(Criteria example) {
        return resVmMapper.countCpu(example);
    }


    @Override
    public List<ResVm> getHostsWithNoFilter(Criteria criteria) {
        return resVmMapper.selectByCriteriaWithNoFilter(criteria);
    }

    @Override
    public int updateToNewOrg(Long newOrgSid, Long oldOrgSid) {
        return resVmMapper.updateToNewOrg(newOrgSid, oldOrgSid);
    }

    @Override
    public int deleteByPrimaryKey(String id) {
        return resVmMapper.deleteByPrimaryKey(id);
    }

    @Override
    public List<ResVmNodeInfo> getNodeInfoListByClusterId(Long clusterId) {
        return resVmMapper.selectNodeInfoListByClusterId(clusterId);
    }


    @Override
    public List<ResVm> getVmStatusByIds(List<Long> ids) {
        return resVmMapper.getVmStatusByIds(ids);
    }

    @Override
    public List<ResBms> getBmsStatusByIds(List<Long> ids) {
        return resBmsMapper.getBmsStatusByIds(ids);
    }

    @Override
    public void deleteBmsByIds(List<Long> ids) {
        Criteria criteria = new Criteria();
        criteria.put("resBmsIds", ids);
        resBmsMapper.deleteByExample(criteria);
    }
}
