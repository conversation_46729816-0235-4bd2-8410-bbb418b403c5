<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.cloudstar.rightcloud.resource.dao.lb.ResLoadBalanceMapper">
    <resultMap id="BaseResultMap" type="cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResLoadBalance">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="cloud_env_id" property="cloudEnvId" jdbcType="BIGINT"/>
        <result column="cloud_env_type" property="cloudEnvType" jdbcType="VARCHAR"/>
        <result column="cloud_env_name" property="cloudEnvName" jdbcType="VARCHAR"/>
        <result column="uuid" property="uuid" jdbcType="VARCHAR"/>
        <result column="lb_name" property="lbName" jdbcType="VARCHAR"/>
        <result column="lb_type" property="lbType" jdbcType="VARCHAR"/>
        <result column="dns_name" property="dnsName" jdbcType="VARCHAR"/>
        <result column="address" property="address" jdbcType="VARCHAR"/>
        <result column="address_type" property="addressType" jdbcType="VARCHAR"/>
        <result column="address_type_name" property="addressTypeName" jdbcType="VARCHAR"/>
        <result column="network_type" property="networkType" jdbcType="VARCHAR"/>
        <result column="res_vpc_id" property="resVpcId" jdbcType="BIGINT"/>
        <result column="vpc_id" property="vpcId" jdbcType="VARCHAR"/>
        <result column="res_network_id" property="resNetWorkId" jdbcType="BIGINT"/>
        <result column="bandwidth" property="bandwidth" jdbcType="INTEGER"/>
        <result column="master_zone_id" property="masterZoneId" jdbcType="VARCHAR"/>
        <result column="slave_zone_id" property="slaveZoneId" jdbcType="VARCHAR"/>
        <result column="listener_port" property="listenerPort" jdbcType="VARCHAR"/>
        <result column="listener_protocal" property="listenerProtocal" jdbcType="VARCHAR"/>
        <result column="created_dt" property="createdDt" jdbcType="TIMESTAMP"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="updated_dt" property="updatedDt" jdbcType="TIMESTAMP"/>
        <result column="updated_by" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="master_zone_name" property="masterZoneName" jdbcType="VARCHAR"/>
        <result column="slave_zone_name" property="slaveZoneName" jdbcType="VARCHAR"/>
        <result column="org_sid" property="orgSid" jdbcType="BIGINT"/>
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="cloud_deployment_id" property="cloudDeploymentId" jdbcType="BIGINT"/>
        <result column="cidr" property="cidr" jdbcType="VARCHAR"/>
        <result column="cloud_env_type" property="cloudEnvType" jdbcType="VARCHAR"/>
        <result column="res_floating_ip_id" property="resFloatingIpId" jdbcType="BIGINT"/>
        <result column="res_floating_ip_address" property="resFloatingIpAddress" jdbcType="VARCHAR"/>
        <result column="tag_names" property="tagNames" jdbcType="VARCHAR"/>
        <result column="rgb_codes" property="rgbCodes" jdbcType="VARCHAR"/>
        <result column="tag_values" property="tagValues" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="network_name" property="netWorkName" jdbcType="VARCHAR"/>
        <result column="vpc_name" property="vpcName" jdbcType="VARCHAR"/>
        <result column="service_order_id" property="serviceOrderId" jdbcType="BIGINT"/>
        <result column="service_order_sn" property="serviceOrderSn" jdbcType="VARCHAR"/>
        <result column="server_status" property="serverStatus" jdbcType="VARCHAR"/>
        <result column="delete_resource" property="deleteResource" jdbcType="VARCHAR"/>
        <result column="deploy_id" property="deployId" jdbcType="BIGINT"/>
        <result column="owner_id" property="ownerId" jdbcType="VARCHAR"/>
        <result column="created_org_sid" property="createdOrgSid" jdbcType="BIGINT"/>
        <result column="owner_id" property="ownerId" jdbcType="VARCHAR"/>
        <result column="created_org_sid" property="createdOrgSid" jdbcType="BIGINT"/>
        <result column="owner_account" property="ownerAccount" jdbcType="VARCHAR"/>
        <result column="owner_org_name" property="ownerOrgName" jdbcType="VARCHAR"/>
        <result column="created_org_name" property="createdOrgName" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Example_Where_Clause">
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.uuids != null and condition.uuids.size() > 0">
                and uuid in
                <foreach collection="condition.uuids" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="condition.cloudEnvId != null">
                and cloud_env_id = #{condition.cloudEnvId}
            </if>
            <if test="condition.uuid != null">
                and uuid = #{condition.uuid}
            </if>
            <if test="condition.lbName != null">
                and lb_name = #{condition.lbName}
            </if>
            <if test="condition.lbType != null">
                and lb_type = #{condition.lbType}
            </if>
            <if test="condition.dnsName != null">
                and dns_name = #{condition.dnsName}
            </if>
            <if test="condition.address != null">
                and address = #{condition.address}
            </if>
            <if test="condition.addressType != null">
                and address_type = #{condition.addressType}
            </if>
            <if test="condition.networkType != null">
                and network_type = #{condition.networkType}
            </if>
            <if test="condition.resVpcId != null">
                and res_vpc_id = #{condition.resVpcId}
            </if>
            <if test="condition.vpcId != null">
                and vpc_id = #{condition.vpcId}
            </if>
            <if test="condition.resNetWorkId != null">
                and res_network_id = #{condition.resNetWorkId}
            </if>
            <if test="condition.bandwidth != null">
                and bandwidth = #{condition.bandwidth}
            </if>
            <if test="condition.masterZoneId != null">
                and master_zone_id = #{condition.masterZoneId}
            </if>
            <if test="condition.slaveZoneId != null">
                and slave_zone_id = #{condition.slaveZoneId}
            </if>
            <if test="condition.listenerPort != null">
                and listener_port = #{condition.listenerPort}
            </if>
            <if test="condition.listenerProtocal != null">
                and listener_protocal = #{condition.listenerProtocal}
            </if>
            <if test="condition.createdDt != null">
                and created_dt = #{condition.createdDt}
            </if>
            <if test="condition.createdBy != null">
                and created_by = #{condition.createdBy}
            </if>
            <if test="condition.updatedDt != null">
                and updated_dt = #{condition.updatedDt}
            </if>
            <if test="condition.updatedBy != null">
                and updated_by = #{condition.updatedBy}
            </if>
            <if test="condition.version != null">
                and version = #{condition.version}
            </if>
            <if test="condition.status != null">
                and status = #{condition.status}
            </if>
            <if test="condition.cloudEnvIds != null">
                and cloud_env_id in
                <foreach item="item" index="index" collection="condition.cloudEnvIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.orgSid != null">
                and org_sid = #{condition.orgSid}
            </if>
            <if test="condition.ownerId!=null">
                AND owner_id = #{condition.ownerId}
            </if>
            <if test="condition.createdOrgSid!=null">
                AND created_org_sid = #{condition.createdOrgSid}
            </if>
            <if test="condition.cloudEnvType != null">
                and (EXISTS (SELECT 1 FROM cloud_env WHERE (cloud_env_type LIKE
                CONCAT('%',#{condition.cloudEnvType},'%')) AND id = A.cloud_env_id))
            </if>
            <if test="condition.orgSid2 != null">
                AND (EXISTS (SELECT 1 FROM sys_m_org WHERE (org_sid = #{condition.orgSid2} OR tree_path LIKE
                CONCAT('/',#{condition.orgSid2},'/%')) AND org_sid = A.org_sid))
            </if>
            <if test="condition.cloudEnvAccountId!= null">
                and (EXISTS (SELECT 1 FROM cloud_env WHERE cloud_env_account_id = #{condition.cloudEnvAccountId} AND id
                = A.cloud_env_id))
            </if>
            <if test="condition.projectIds != null and condition.projectIds.size  > 0">
                and A.org_sid in
                <foreach collection="condition.projectIds" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </trim>
    </sql>
    <sql id="Example_Where_Clause_Zone">
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.cloudEnvId != null">
                and A.cloud_env_id = #{condition.cloudEnvId}
            </if>
            <if test="condition.cloudEnvIdIn != null and condition.cloudEnvIdIn.size() > 0">
                and A.CLOUD_ENV_ID in
                <foreach collection="condition.cloudEnvIdIn" open="(" close=")" separator="," item="item">
                    #{item}
                </foreach>
            </if>
            <if test="condition.uuid != null">
                and A.uuid = #{condition.uuid}
            </if>
            <if test="condition.lbName != null">
                and A.lb_name = #{condition.lbName}
            </if>
            <if test="condition.nameLike != null">
                and A.lb_name like concat('%', #{condition.nameLike}, '%')
            </if>
            <if test="condition.lbType != null">
                and A.lb_type = #{condition.lbType}
            </if>
            <if test="condition.dnsName != null">
                and A.dns_name = #{condition.dnsName}
            </if>
            <if test="condition.address != null">
                and A.address = #{condition.address}
            </if>
            <if test="condition.addressType != null">
                and A.address_type = #{condition.addressType}
            </if>
            <if test="condition.networkType != null">
                and A.network_type = #{condition.networkType}
            </if>
            <if test="condition.networkTypeNotEqual != null">
                and (A.network_type != #{condition.networkTypeNotEqual} or A.network_type is null)
            </if>
            <if test="condition.resVpcId != null">
                and A.res_vpc_id = #{condition.resVpcId}
            </if>
            <if test="condition.vpcId != null">
                and A.vpc_id = #{condition.vpcId}
            </if>
            <if test="condition.resNetWorkId != null">
                and A.res_network_id = #{condition.resNetWorkId}
            </if>
            <if test="condition.bandwidth != null">
                and A.bandwidth = #{condition.bandwidth}
            </if>
            <if test="condition.masterZoneId != null">
                and A.master_zone_id = #{condition.masterZoneId}
            </if>
            <if test="condition.slaveZoneId != null">
                and A.slave_zone_id = #{condition.slaveZoneId}
            </if>
            <if test="condition.listenerPort != null">
                and A.listener_port = #{condition.listenerPort}
            </if>
            <if test="condition.listenerProtocal != null">
                and A.listener_protocal = #{condition.listenerProtocal}
            </if>
            <if test="condition.status != null">
                and A.status = #{condition.status}
            </if>
            <if test="condition.df != null">
                ${condition.df}
            </if>
            <if test="condition.cloudEnvIds != null">
                AND A.cloud_env_id IN
                <foreach item="item" index="index" collection="condition.cloudEnvIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.orgSid != null">
                AND (EXISTS (SELECT 1 FROM sys_m_org WHERE (org_sid = #{condition.orgSid} OR tree_path LIKE
                CONCAT('/',#{condition.orgSid},'/%')) AND org_sid = A.org_sid))
            </if>
            <!--过滤子网连接路由器的可以挂载弹性IP的数据-->
            <if test="condition.cloudEnvId != null and condition.canBindFloatingIp != null">
                AND A.res_network_id IN (
                #查询云环境定义路由器中包含共有网络的所有子网#
                SELECT N.id FROM res_router A
                <if test="condition.floatingIpId != null">
                    INNER JOIN res_vpc V ON (V.ID = A.vpc_id AND V.CLOUD_ENV_ID = #{condition.cloudEnvId})
                    INNER JOIN res_floating_ip FI ON (FI.pool = V.`name` AND FI.id = #{condition.floatingIpId})
                </if>
                INNER JOIN res_router_interface B ON A.id = B.router_id
                INNER JOIN network N ON B.network_uuid = N.uuid
                WHERE B.cloud_env_id IN ( SELECT id FROM cloud_env env WHERE env.cloud_env_account_id =
                (SELECT cloud_env_account_id FROM cloud_env WHERE id = #{condition.cloudEnvId} ) )
                AND EXISTS ( SELECT 1 FROM res_vpc vpc WHERE vpc.id = A.vpc_id AND vpc.external = 'true' )
                )
            </if>
            <!--过滤子网连接路由器的可以挂载弹性IP的数据-->
            <if test="condition.tagIds != null">
                and exists (select 1 from cloud_tag_ref where obj_id = A.id and obj_type = 'slb' and
                tag_id in
                <foreach item="item" index="index" collection="condition.tagIds" open="("
                    separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="condition.selfServiceEqual != null">
                AND SO.ID IS NOT NULL
            </if>
            <if test="condition.ownerId!=null">
                AND A.owner_id = #{condition.ownerId}
            </if>
            <if test="condition.createdOrgSid!=null">
                AND A.created_org_sid = #{condition.createdOrgSid}
            </if>
        </trim>
    </sql>
    <sql id="Base_Column_List">
        id
        , cloud_env_id, uuid, lb_name, lb_type, dns_name, address, address_type, network_type, res_vpc_id, vpc_id,
        res_network_id,
        bandwidth, master_zone_id, slave_zone_id, listener_port, listener_protocal, created_dt,
        created_by, updated_dt, updated_by, version,
        status,loadBalancerSpec,org_sid,start_time,end_time,cloud_deployment_id,
        res_floating_ip_id, description
        ,owner_id, created_org_sid
    </sql>
    <sql id="Base_Column_List_zone">
        A
        .id, A.cloud_env_id, A.uuid, A.lb_name, A.lb_type, A.dns_name, A.address, A.address_type, A.network_type,
        A.res_vpc_id, A.vpc_id, A.res_network_id,
        A.bandwidth, A.master_zone_id, A.slave_zone_id, A.listener_port, A.listener_protocal, A.created_dt,
        A.created_by, A.updated_dt, A.updated_by, A.version, A.status,A.loadBalancerSpec, A.res_floating_ip_id,
        A.description,A.start_time,A.end_time
        ,A.owner_id, A.created_org_sid
    </sql>
    <select id="selectByParams" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from res_loadbalance
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>

    <select id="selectByParamsForZone" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List_zone"/>
        , B.name as master_zone_name,
        HI.tag_names,HI.rgb_codes,HI.tag_values,
        C.name as slave_zone_name, RFI.ip as res_floating_ip_address,
        D.cloud_env_type,
        D.cloud_env_name,
        code.code_display as address_type_name,
        A.org_sid,
        u.account as owner_account,
        oo.org_name as owner_org_name,
        co.org_name as created_org_name
        FROM res_loadbalance A
        LEFT JOIN res_zone B ON (A.cloud_env_id = B.cloud_env_id and A.master_zone_id = B.uuid)
        LEFT JOIN res_zone C ON (A.cloud_env_id = C.cloud_env_id and A.slave_zone_id = C.uuid)
        LEFT JOIN res_floating_ip RFI ON A.res_floating_ip_id = RFI.id
        INNER JOIN cloud_env D on A.cloud_env_id = D.id
        LEFT JOIN sys_m_code code on code.code_value = A.address_type and code.CODE_CATEGORY =
        'LOADBALANCE_ADDRESS_TYPE'
        LEFT JOIN (select H.obj_id, GROUP_CONCAT(I.tag_name ORDER BY I.tag_id) as tag_names,
        GROUP_CONCAT(I.rgb_code ORDER BY I.tag_id) as rgb_codes,
        GROUP_CONCAT(I.tag_value ORDER BY I.tag_id) as tag_values from cloud_tag_ref H
        LEFT JOIN cloud_tag I on (H.tag_id= I.tag_id and H.obj_type = 'slb') GROUP BY H.obj_id) HI on HI.obj_id = A.id
        LEFT JOIN sys_m_user u on A.owner_id = u.user_sid
        LEFT JOIN sys_m_org oo on A.org_sid = oo.org_sid
        LEFT JOIN sys_m_org co on A.org_sid = co.org_sid
        <if test="condition.tagName != null">
            inner JOIN (select H.obj_id from cloud_tag_ref H
            LEFT JOIN cloud_tag I on (H.tag_id= I.tag_id and H.obj_type = 'slb')
            <where>
                <if test="condition.tagName != null">
                    and I.tag_name like concat(#{condition.tagName}, '%')
                </if>
                <if test="condition.tagValue != null">
                    and I.tag_value like concat(#{condition.tagValue}, '%')
                </if>
            </where>
            GROUP BY H.obj_id) HIT on HIT.obj_id = A.ID
        </if>
        <if test="_parameter != null">
            <include refid="Example_Where_Clause_Zone"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <select id="selectByParamsUnbindLbs" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List_zone"/>
        , B.name as master_zone_name,
        HI.tag_names,HI.rgb_codes,HI.tag_values,
        C.name as slave_zone_name, RFI.ip as res_floating_ip_address
        FROM res_loadbalance A
        LEFT JOIN res_zone B ON (A.cloud_env_id = B.cloud_env_id and A.master_zone_id = B.uuid)
        LEFT JOIN res_zone C ON (A.cloud_env_id = C.cloud_env_id and A.slave_zone_id = C.uuid)
        LEFT JOIN res_floating_ip RFI ON A.res_floating_ip_id = RFI.id
        INNER JOIN cloud_env D on A.cloud_env_id = D.id
        LEFT JOIN (select H.obj_id, GROUP_CONCAT(I.tag_name ORDER BY I.tag_id) as tag_names,
        GROUP_CONCAT(I.rgb_code ORDER BY I.tag_id) as rgb_codes,
        GROUP_CONCAT(I.tag_value ORDER BY I.tag_id) as tag_values from cloud_tag_ref H
        LEFT JOIN cloud_tag I on (H.tag_id= I.tag_id and H.obj_type = 'slb') GROUP BY H.obj_id) HI on HI.obj_id = A.id
        <if test="_parameter != null">
            <include refid="Example_Where_Clause_Zone"/>
            and res_floating_ip_id is null
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        B.name AS vpc_name,
        C.network_name,
        D.ip AS res_floating_ip_address,
        D.id AS res_floating_ip_id,
        <include refid="Base_Column_List_zone"/>
        from res_loadbalance A
        left join res_vpc B on A.res_vpc_id = B.id
        left join network C on A.res_network_id = C.id
        left join res_floating_ip D on A.res_floating_ip_id = D.id
        where A.id = #{id}
    </select>
    <select id="selectBackendById" resultType="cn.com.cloudstar.rightcloud.core.pojo.dto.slb.ResLbHost"
        parameterType="java.lang.Long">
        SELECT
        a.id,
        c.id as groupId,
        d.instance_name,
        b.master_zone_id,
        d.public_ip,
        d.inner_ip,
        d.instance_network_type,
        d.instance_id,
        d.ID AS resVmId,
        b.status,
        a.hc_status,
        a.weight,
        c.group_name,
        a.port,
        a.server_type,
        c.backend_type,
        a.created_dt,
        a.created_by,
        a.updated_dt,
        a.updated_by,
        a.version
        FROM
        res_lb_backend a LEFT JOIN res_loadbalance b ON a.res_lb_id = b.id
        LEFT JOIN res_lb_backend_group c ON a.res_lb_backend_group_id = c.id
        LEFT JOIN res_vm d ON a.res_vm_id = d.ID
        WHERE b.id = #{id}
        ORDER BY a.id
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from res_loadbalance
        where id = #{id}
    </delete>
    <delete id="deleteByParams" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        delete from res_loadbalance
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResLoadBalance">
        insert into res_loadbalance (id, cloud_env_id, uuid, lb_name, lb_type, dns_name, address, address_type,
        network_type,
        res_vpc_id, vpc_id, res_network_id, bandwidth, master_zone_id, slave_zone_id,
        listener_port,
        listener_protocal, created_dt, created_by, updated_dt, updated_by, version,
        status, description
        , owner_id, created_org_sid)
        values (#{id}, #{cloudEnvId}, #{uuid}, #{lbName}, #{lbType}, #{dnsName}, #{address}, #{addressType},
        #{networkType},
        #{resVpcId}, #{vpcId}, #{resNetWorkId}, #{bandwidth}, #{masterZoneId}, #{slaveZoneId}, #{listenerPort},
        #{listenerProtocal}, #{createdDt}, #{createdBy}, #{updatedDt}, #{updatedBy}, #{version},
        #{status}, #{description}
        , #{ownerId}, #{createdOrgSid})
    </insert>
    <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id"
        parameterType="cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResLoadBalance">
        insert into res_loadbalance
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="cloudEnvId != null">
                cloud_env_id,
            </if>
            <if test="uuid != null">
                uuid,
            </if>
            <if test="lbName != null">
                lb_name,
            </if>
            <if test="lbType != null">
                lb_type,
            </if>
            <if test="dnsName != null">
                dns_name,
            </if>
            <if test="address != null">
                address,
            </if>
            <if test="addressType != null">
                address_type,
            </if>
            <if test="networkType != null">
                network_type,
            </if>
            <if test="resVpcId != null">
                res_vpc_id,
            </if>
            <if test="vpcId != null">
                vpc_id,
            </if>
            <if test="resNetWorkId != null">
                res_network_id,
            </if>
            <if test="bandwidth != null">
                bandwidth,
            </if>
            <if test="masterZoneId != null">
                master_zone_id,
            </if>
            <if test="slaveZoneId != null">
                slave_zone_id,
            </if>
            <if test="listenerPort != null">
                listener_port,
            </if>
            <if test="listenerProtocal != null">
                listener_protocal,
            </if>
            <if test="createdDt != null">
                created_dt,
            </if>
            <if test="createdBy != null">
                created_by,
            </if>
            <if test="updatedDt != null">
                updated_dt,
            </if>
            <if test="updatedBy != null">
                updated_by,
            </if>
            <if test="version != null">
                version,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="loadBalancerSpec != null">
                loadBalancerSpec,
            </if>
            <if test="orgSid != null">
                org_sid,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="cloudDeploymentId != null">
                cloud_deployment_id,
            </if>
            <if test="resFloatingIpId != null">
                res_floating_ip_id,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="chargeType != null">
                charge_type,
            </if>
            <if test="ownerId!=null">
                owner_id,
            </if>
            <if test="createdOrgSid!=null">
                created_org_sid,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="cloudEnvId != null">
                #{cloudEnvId},
            </if>
            <if test="uuid != null">
                #{uuid},
            </if>
            <if test="lbName != null">
                #{lbName},
            </if>
            <if test="lbType != null">
                #{lbType},
            </if>
            <if test="dnsName != null">
                #{dnsName},
            </if>
            <if test="address != null">
                #{address},
            </if>
            <if test="addressType != null">
                #{addressType},
            </if>
            <if test="networkType != null">
                #{networkType},
            </if>
            <if test="resVpcId != null">
                #{resVpcId},
            </if>
            <if test="vpcId != null">
                #{vpcId},
            </if>
            <if test="resNetWorkId != null">
                #{resNetWorkId},
            </if>
            <if test="bandwidth != null">
                #{bandwidth},
            </if>
            <if test="masterZoneId != null">
                #{masterZoneId},
            </if>
            <if test="slaveZoneId != null">
                #{slaveZoneId},
            </if>
            <if test="listenerPort != null">
                #{listenerPort},
            </if>
            <if test="listenerProtocal != null">
                #{listenerProtocal},
            </if>
            <if test="createdDt != null">
                #{createdDt},
            </if>
            <if test="createdBy != null">
                #{createdBy},
            </if>
            <if test="updatedDt != null">
                #{updatedDt},
            </if>
            <if test="updatedBy != null">
                #{updatedBy},
            </if>
            <if test="version != null">
                #{version},
            </if>
            <if test="status != null">
                #{status},
            </if>
            <if test="loadBalancerSpec != null">
                #{loadBalancerSpec},
            </if>
            <if test="orgSid != null">
                #{orgSid},
            </if>
            <if test="startTime != null">
                #{startTime},
            </if>
            <if test="endTime != null">
                #{endTime},
            </if>
            <if test="cloudDeploymentId != null">
                #{cloudDeploymentId},
            </if>
            <if test="resFloatingIpId != null">
                #{resFloatingIpId},
            </if>
            <if test="description != null">
                #{description},
            </if>
            <if test="chargeType != null">
                #{chargeType},
            </if>
            <if test="ownerId!=null">
                #{ownerId},
            </if>
            <if test="createdOrgSid!=null">
                #{createdOrgSid},
            </if>
        </trim>
    </insert>
    <select id="countByParams" parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria"
        resultType="java.lang.Integer">
        select count(*) from res_loadbalance
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByParamsSelective" parameterType="map">
        update res_loadbalance
        <set>
            <if test="record.id != null">
                id = #{record.id},
            </if>
            <if test="record.cloudEnvId != null">
                cloud_env_id = #{record.cloudEnvId},
            </if>
            <if test="record.uuid != null">
                uuid = #{record.uuid},
            </if>
            <if test="record.lbName != null">
                lb_name = #{record.lbName},
            </if>
            <if test="record.lbType != null">
                lb_type = #{record.lbType},
            </if>
            <if test="record.dnsName != null">
                dns_name = #{record.dnsName},
            </if>
            <if test="record.address != null">
                address = #{record.address},
            </if>
            <if test="record.addressType != null">
                address_type = #{record.addressType},
            </if>
            <if test="record.networkType != null">
                network_type = #{record.networkType},
            </if>
            <if test="record.resVpcId != null">
                res_vpc_id = #{record.resVpcId},
            </if>
            <if test="record.vpcId != null">
                vpc_id = #{record.vpcId},
            </if>
            <if test="record.resNetWorkId != null">
                res_network_id = #{record.resNetWorkId},
            </if>
            <if test="record.bandwidth != null">
                bandwidth = #{record.bandwidth},
            </if>
            <if test="record.masterZoneId != null">
                master_zone_id = #{record.masterZoneId},
            </if>
            <if test="record.slaveZoneId != null">
                slave_zone_id = #{record.slaveZoneId},
            </if>
            <if test="record.listenerPort != null">
                listener_port = #{record.listenerPort},
            </if>
            <if test="record.listenerProtocal != null">
                listener_protocal = #{record.listenerProtocal},
            </if>
            <if test="record.createdDt != null">
                created_dt = #{record.createdDt},
            </if>
            <if test="record.createdBy != null">
                created_by = #{record.createdBy},
            </if>
            <if test="record.updatedDt != null">
                updated_dt = #{record.updatedDt},
            </if>
            <if test="record.updatedBy != null">
                updated_by = #{record.updatedBy},
            </if>
            <if test="record.version != null">
                version = #{record.version},
            </if>
            <if test="record.status != null">
                status = #{record.status},
            </if>
            <if test="record.description != null">
                description = #{record.description},
            </if>
            <if test="record.ownerId!=null">
                owner_id = #{record.ownerId},
            </if>
            <if test="record.createdOrgSid!=null">
                created_org_sid = #{record.createdOrgSid},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByParams" parameterType="map">
        update res_loadbalance
        set id = #{record.id},
        cloud_env_id = #{record.cloudEnvId},
        uuid = #{record.uuid},
        lb_name = #{record.lbName},
        lb_type = #{record.lbType},
        dns_name = #{record.dnsName},
        address = #{record.address},
        address_type = #{record.addressType},
        network_type = #{record.networkType},
        res_vpc_id = #{record.resVpcId},
        vpc_id = #{record.vpcId},
        res_network_id = #{record.resNetWorkId},
        bandwidth = #{record.bandwidth},
        master_zone_id = #{record.masterZoneId},
        slave_zone_id = #{record.slaveZoneId},
        listener_port = #{record.listenerPort},
        listener_protocal = #{record.listenerProtocal},
        created_dt = #{record.createdDt},
        created_by = #{record.createdBy},
        updated_dt = #{record.updatedDt},
        updated_by = #{record.updatedBy},
        version = #{record.version},
        status = #{record.status},
        description = #{description},
        owner_id = #{ownerId},
        created_org_sid = #{createdOrgSid}
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </update>

    <update id="updateByPrimaryKeySelective"
        parameterType="cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResLoadBalance">
        update res_loadbalance
        <set>
            <if test="endTime != null">
                end_time = #{endTime},
            </if>
            <if test="cloudEnvId != null">
                cloud_env_id = #{cloudEnvId},
            </if>
            <if test="uuid != null">
                uuid = #{uuid},
            </if>
            <if test="lbName != null">
                lb_name = #{lbName},
            </if>
            <if test="lbType != null">
                lb_type = #{lbType},
            </if>
            <if test="dnsName != null">
                dns_name = #{dnsName},
            </if>
            <if test="address != null">
                address = #{address},
            </if>
            <if test="addressType != null">
                address_type = #{addressType},
            </if>
            <if test="networkType != null">
                network_type = #{networkType},
            </if>
            <if test="resVpcId != null">
                res_vpc_id = #{resVpcId},
            </if>
            <if test="vpcId != null">
                vpc_id = #{vpcId},
            </if>
            <if test="resNetWorkId != null">
                res_network_id = #{resNetWorkId},
            </if>
            <if test="bandwidth != null">
                bandwidth = #{bandwidth},
            </if>
            <if test="masterZoneId != null">
                master_zone_id = #{masterZoneId},
            </if>
            <if test="slaveZoneId != null">
                slave_zone_id = #{slaveZoneId},
            </if>
            <if test="listenerPort != null">
                listener_port = #{listenerPort},
            </if>
            <if test="listenerProtocal != null">
                listener_protocal = #{listenerProtocal},
            </if>
            <if test="createdDt != null">
                created_dt = #{createdDt},
            </if>
            <if test="createdBy != null">
                created_by = #{createdBy},
            </if>
            <if test="updatedDt != null">
                updated_dt = #{updatedDt},
            </if>
            <if test="updatedBy != null">
                updated_by = #{updatedBy},
            </if>
            <if test="version != null">
                version = #{version},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="startTime != null">
                start_time = #{startTime},
            </if>
            <if test="orgSid != null">
                org_sid = #{orgSid},
            </if>
            <if test="description != null">
                description = #{description},
            </if>
            <if test="ownerId!=null">
                owner_id = #{ownerId},
            </if>
            <if test="createdOrgSid!=null">
                created_org_sid = #{createdOrgSid},
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="updateByPrimaryKey"
        parameterType="cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResLoadBalance">
        update res_loadbalance
        set cloud_env_id = #{cloudEnvId},
        uuid = #{uuid},
        lb_name = #{lbName},
        lb_type = #{lbType},
        dns_name = #{dnsName},
        address = #{address},
        address_type = #{addressType},
        network_type = #{networkType},
        res_vpc_id = #{resVpcId},
        vpc_id = #{vpcId},
        res_network_id = #{resNetWorkId},
        bandwidth = #{bandwidth},
        master_zone_id = #{masterZoneId},
        slave_zone_id = #{slaveZoneId},
        listener_port = #{listenerPort},
        listener_protocal = #{listenerProtocal},
        created_dt = #{createdDt},
        created_by = #{createdBy},
        updated_dt = #{updatedDt},
        updated_by = #{updatedBy},
        version = #{version},
        status = #{status},
        res_floating_ip_id = #{resFloatingIpId},
        description = #{description},
        owner_id = #{ownerId},
        created_org_sid = #{createdOrgSid}
        where id = #{id}
    </update>

    <select id="selectHost" parameterType="cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResLoadBalance"
        resultType="cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResVm">
        SELECT * FROM res_vm
        <where>
            <if test="condition.subnetId != null">
                and subnet_id = #{condition.subnetId}
            </if>
        </where>
    </select>

    <select id="selectByCriteria" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        select
        <include refid="Base_Column_List_zone"/>
        , B.name as master_zone_name,
        C.name as slave_zone_name, D.cloud_env_name, D.cloud_env_type, E.cidr,
        concat(F.name, '/', E.network_name) network_name
        FROM res_loadbalance A
        LEFT JOIN res_zone B ON (A.cloud_env_id = B.cloud_env_id and A.master_zone_id = B.uuid)
        LEFT JOIN res_zone C ON (A.cloud_env_id = C.cloud_env_id and A.slave_zone_id = C.uuid)
        LEFT JOIN cloud_env D on A.cloud_env_id = D.id
        LEFT JOIN network E ON A.res_network_id = E.id
        LEFT JOIN res_vpc F ON E.net_vpc_id = F.id
        <if test="_parameter != null">
            <include refid="Example_Where_Clause_Zone"/>
        </if>
    </select>

    <update id="unbandingFloatingIp" parameterType="java.lang.Long">
        update res_loadbalance
        set res_floating_ip_id=null,
        bandwidth=null,
        address_type='intranet'
        where res_floating_ip_id = #{resFloatingIpId}
    </update>
    <update id="updateLifeCycleByPrimaryKey"
        parameterType="cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResLoadBalance">
        update res_loadbalance
        set start_time = #{startTime},
        end_time = #{endTime}
        where id = #{id}
    </update>

    <select id="selectSelfServiceByParams" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List_zone"/>
        , B.name as master_zone_name,
        HI.tag_names,HI.rgb_codes,HI.tag_values,
        C.name as slave_zone_name, RFI.ip as res_floating_ip_address,
        D.cloud_env_type,
        D.cloud_env_name,
        code.code_display as address_type_name,
        A.org_sid,
        SO.delete_resource as delete_resource,
        SOBRR.id AS deploy_id,
        SO.id as service_order_id,
        SO.STATUS as server_status,
        SO.order_sn as service_order_sn,
        u.account as owner_account,
        oo.org_name as owner_org_name,
        co.org_name as created_org_name
        FROM res_loadbalance A
        LEFT JOIN res_zone B ON (A.cloud_env_id = B.cloud_env_id and A.master_zone_id = B.uuid)
        LEFT JOIN res_zone C ON (A.cloud_env_id = C.cloud_env_id and A.slave_zone_id = C.uuid)
        LEFT JOIN res_floating_ip RFI ON A.res_floating_ip_id = RFI.id
        INNER JOIN cloud_env D on A.cloud_env_id = D.id
        LEFT JOIN sys_m_code code on code.code_value = A.address_type and code.CODE_CATEGORY =
        'LOADBALANCE_ADDRESS_TYPE'
        LEFT JOIN (select H.obj_id, GROUP_CONCAT(I.tag_name ORDER BY I.tag_id) as tag_names,
        GROUP_CONCAT(I.rgb_code ORDER BY I.tag_id) as rgb_codes,
        GROUP_CONCAT(I.tag_value ORDER BY I.tag_id) as tag_values from cloud_tag_ref H
        LEFT JOIN cloud_tag I on (H.tag_id= I.tag_id and H.obj_type = 'slb') GROUP BY H.obj_id) HI on HI.obj_id = A.id
        <if test="condition.tagName != null">
            inner JOIN (select H.obj_id from cloud_tag_ref H
            LEFT JOIN cloud_tag I on (H.tag_id= I.tag_id and H.obj_type = 'slb')
            <where>
                <if test="condition.tagName != null">
                    and I.tag_name like concat(#{condition.tagName}, '%')
                </if>
                <if test="condition.tagValue != null">
                    and I.tag_value like concat(#{condition.tagValue}, '%')
                </if>
            </where>
            GROUP BY H.obj_id) HIT on HIT.obj_id = A.ID
        </if>
        LEFT JOIN service_order_basic_resource_relation SOBRR ON SOBRR.resource_id = A.id
        LEFT JOIN service_order SO ON SO.id = SOBRR.service_order_id
        LEFT JOIN sys_m_user u on A.owner_id = u.user_sid
        LEFT JOIN sys_m_org oo on A.org_sid = oo.org_sid
        LEFT JOIN sys_m_org co on A.org_sid = co.org_sid
        <if test="_parameter != null">
            <include refid="Example_Where_Clause_Zone"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>
    <select id="countSelfServiceSlb" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT A.ID)
        FROM res_loadbalance A
        LEFT JOIN service_order_basic_resource_relation SOBRR ON SOBRR.resource_id = A.id
        LEFT JOIN service_order SO ON SO.id = SOBRR.service_order_id
        WHERE SO.ID IS NOT NULL
    </select>
    <select id="getElbByIds" resultType="cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResLoadBalance">
        select id, status from res_loadbalance where id in
        <foreach collection="ids" separator="," open="(" close=")" item="id">
            #{id,jdbcType=BIGINT}
        </foreach>
    </select>

</mapper>
