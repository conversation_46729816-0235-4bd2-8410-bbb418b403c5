/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.dao.redundance.core;

import org.springframework.stereotype.Repository;

import java.util.List;

import cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria;
import cn.com.cloudstar.rightcloud.remote.api.pojo.system.deployment.CloudDeploymentScript;

/**
 * CloudDeploymentScriptMapper
 *
 * <AUTHOR>
 * @date 2018/1/30
 */
@Repository
public interface CloudDeploymentScriptMapper {

    /**
     * 根据条件查询记录集
     */
    List<CloudDeploymentScript> selectByParams(Criteria example);
}
