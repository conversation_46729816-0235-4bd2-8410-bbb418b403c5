/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.listener;

import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.MachineCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.MachineJoinResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.MachineOperateResult;
import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnv;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResVm;
import cn.com.cloudstar.rightcloud.common.constants.WebConstants;
import cn.com.cloudstar.rightcloud.common.constants.WebConstants.PowerStatus;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.constants.res.status.CloudPhysicalHostStatus;
import cn.com.cloudstar.rightcloud.common.constants.res.status.ResVmManageStatus;
import cn.com.cloudstar.rightcloud.common.constants.res.status.ResVmStatus;
import cn.com.cloudstar.rightcloud.common.constants.res.type.CloudEnvType;
import cn.com.cloudstar.rightcloud.common.constants.res.type.VmCategory;
import cn.com.cloudstar.rightcloud.common.constants.type.CloudEnvTenantKey;
import cn.com.cloudstar.rightcloud.common.constants.type.ServerType;
import cn.com.cloudstar.rightcloud.common.constants.type.VmOperation;
import cn.com.cloudstar.rightcloud.common.enums.resource.ResourceOperateEnum;
import cn.com.cloudstar.rightcloud.common.enums.resource.ResourceTypeEnum;
import cn.com.cloudstar.rightcloud.common.exception.RetryException;
import cn.com.cloudstar.rightcloud.common.util.*;
import cn.com.cloudstar.rightcloud.common.util.json.JsonUtil;
import cn.com.cloudstar.rightcloud.common.websocket.annonation.Message;
import cn.com.cloudstar.rightcloud.common.websocket.annonation.MessageParam;
import cn.com.cloudstar.rightcloud.common.websocket.support.OperateEnum;
import cn.com.cloudstar.rightcloud.common.websocket.support.ServerMsgType;
import cn.com.cloudstar.rightcloud.core.annotation.log.LogMethod;
import cn.com.cloudstar.rightcloud.core.annotation.log.LogParam;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicWebUtil;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.cloud.CloudPhysicalHostPool;
import cn.com.cloudstar.rightcloud.resource.dao.env.CloudEnvMapper;
import cn.com.cloudstar.rightcloud.resource.service.server.CloudPhysicalHostPoolService;
import cn.com.cloudstar.rightcloud.resource.service.server.ResVmService;
import cn.hutool.core.convert.Convert;
import com.google.common.base.Throwables;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * @date: 11:42 2020/04/10
 */
@Component
public class MachineResultListener {

    private final Logger logger = LoggerFactory.getLogger(MachineResultListener.class);

    @Autowired
    private ResVmService resVmService;

    @Autowired
    private CloudPhysicalHostPoolService cloudPhysicalHostPoolService;

    @Autowired
    private CloudEnvMapper cloudEnvMapper;

    /**
     * 接入物理机
     *
     * @param machineJoinResult
     */
    @Transactional(rollbackFor = Exception.class)
    @LogMethod(resourceKey = "#machineJoinResult.physicalHostPoolId", resourceType = ResourceTypeEnum.VM, opUser = "#machineJoinResult.opUser", operate = ResourceOperateEnum.CREATE, success = "#machineJoinResult.success", orgSid = "#machineJoinResult.orgSid")
    @Message(refKey = "#machineJoinResult.options.get('physicalHostPoolId')", envId = "#machineJoinResult.cloudEnvId", msgType = ServerMsgType.MACHINE, opUser = "#machineJoinResult.opUser", operate = OperateEnum.JOIN, success = "#machineJoinResult.success", refNameKey = "#machineJoinResult.hostName", errorMsg = "#machineJoinResult.errMsg")
    public void handleMessage(
            @LogParam("machineJoinResult") @MessageParam("machineJoinResult") MachineJoinResult machineJoinResult) {
        logger.info("接入物理机回调 | 回调参数 ： {}", JsonUtil.toJson(machineJoinResult));
        Long physicalHostPoolId = null;
        try {
            Map<String, Object> options = machineJoinResult.getOptions();
            physicalHostPoolId = Long.valueOf(
                    options.get(WebConstants.PhysicalHostInfo.PHYSICAL_HOST_POOL_ID).toString());
            CloudPhysicalHostPool cloudPhysicalHostPool = getCloudPhysicalHostPool(physicalHostPoolId);
            AssertUtil.requireNonBlank(cloudPhysicalHostPool, "资源池:[" + physicalHostPoolId + "] 没有找到!");

            if (machineJoinResult.isSuccess()) {
                CloudPhysicalHostPool cloudPhysicalHostPoolUpdate = new CloudPhysicalHostPool();
                cloudPhysicalHostPoolUpdate.setId(physicalHostPoolId);
                cloudPhysicalHostPoolUpdate.setPhysicalStatus(CloudPhysicalHostStatus.JOIN_SUCCESS);
                cloudPhysicalHostPoolUpdate.setPhysicalUUID(machineJoinResult.getUuid());
                cloudPhysicalHostPoolUpdate.setPowerStatus(machineJoinResult.getPowerStatus().toLowerCase());
                cloudPhysicalHostPoolUpdate.setVendor(machineJoinResult.getVendor());
                cloudPhysicalHostPoolUpdate.setModel(machineJoinResult.getModel());
                cloudPhysicalHostPoolUpdate.setSerialNumber(machineJoinResult.getSerialNumber());
                if (StringUtil.isBlank(cloudPhysicalHostPoolUpdate.getPhysicalUUID())) {
                    cloudPhysicalHostPoolUpdate.setPhysicalUUID(
                            UuidUtil.getShortUuid(WebConstants.PhysicalHostInfo.JOIN_SN_PREFIX));
                }
                cloudPhysicalHostPoolUpdate.setPowerType(machineJoinResult.getPowerType());
                cloudPhysicalHostPoolUpdate.setCpuModel(machineJoinResult.getCpuModel());
                cloudPhysicalHostPoolUpdate.setCpu(machineJoinResult.getCpu());
                cloudPhysicalHostPoolUpdate.setMemory(machineJoinResult.getMemory());
                this.cloudPhysicalHostPoolService.updateByPrimaryKeySelective(cloudPhysicalHostPoolUpdate);

                // 如果是更新物理机 则可能更新resVm
                if (Objects.equals(options.get("opType"), "updatePhysicalHost")) {
                    ResVm resVm = this.resVmService.selectByPhysicalHostPoolId(physicalHostPoolId);
                    if (Objects.nonNull(resVm)) {
                        ResVm resVmUpdate = new ResVm();
                        resVmUpdate.setId(resVm.getId());
                        resVmUpdate.setInstanceName(cloudPhysicalHostPool.getHostName());
                        resVmUpdate.setPublicIp(cloudPhysicalHostPool.getPublicIp());
                        resVmUpdate.setInnerIp(cloudPhysicalHostPool.getInnerIp());
                        resVmUpdate.setSshPort(cloudPhysicalHostPool.getSshPort());
                        resVmUpdate.setRemoteLoginType(cloudPhysicalHostPool.getRemoteLoginType());
                        resVmUpdate.setKeypairId(cloudPhysicalHostPool.getKeypairId());
                        resVmUpdate.setKeypairName(cloudPhysicalHostPool.getKeypairName());
                        resVmUpdate.setManagementAccount(cloudPhysicalHostPool.getManagementAccount());
                        resVmUpdate.setManagemenPassword(cloudPhysicalHostPool.getManagemenPassword());
                        this.resVmService.updateByPrimaryKeySelective(resVmUpdate);
                    }
                } else {
                    // 新增一台实例
                    createResVm(cloudPhysicalHostPool, cloudPhysicalHostPoolUpdate);
                }

            } else {
                CloudPhysicalHostPool cloudPhysicalHostPoolUpdate = new CloudPhysicalHostPool();
                cloudPhysicalHostPoolUpdate.setId(physicalHostPoolId);
                cloudPhysicalHostPoolUpdate.setStatusInfo("系统错误,请联系管理员. " + machineJoinResult.getErrMsg());
                cloudPhysicalHostPoolUpdate.setPhysicalStatus(CloudPhysicalHostStatus.JOIN_FAILED);
                cloudPhysicalHostPoolUpdate.setPowerStatus(PowerStatus.UNKNOWN);
                this.cloudPhysicalHostPoolService.updateByPrimaryKeySelective(cloudPhysicalHostPoolUpdate);
            }
        } catch (Exception e) {
            logger.error("创建machine回调异常 : {}", Throwables.getStackTraceAsString(e));
            CloudPhysicalHostPool cloudPhysicalHostPoolUpdate = new CloudPhysicalHostPool();
            cloudPhysicalHostPoolUpdate.setId(physicalHostPoolId);
            cloudPhysicalHostPoolUpdate.setStatusInfo(
                    "系统错误,请联系管理员. " + (StringUtil.isNotBlank(machineJoinResult.getErrMsg())
                                       ? machineJoinResult.getErrMsg() : e.getMessage()));
            cloudPhysicalHostPoolUpdate.setPhysicalStatus(CloudPhysicalHostStatus.JOIN_FAILED);
            cloudPhysicalHostPoolUpdate.setPowerStatus(PowerStatus.UNKNOWN);
            this.cloudPhysicalHostPoolService.updateByPrimaryKeySelective(cloudPhysicalHostPoolUpdate);
        } finally {
            // 发送创建成功的消息
            this.cloudPhysicalHostPoolService.sendWsJoinCloudPhysicalHostComplate(physicalHostPoolId,
                                                                                  machineJoinResult.isSuccess(),
                                                                                  machineJoinResult.getErrMsg());
        }
    }

    private void createResVm(CloudPhysicalHostPool cloudPhysicalHostPool,
                             CloudPhysicalHostPool cloudPhysicalHostPoolUpdate) {
        CloudEnv cloudEnv = getJoinDataCenterCloudEnvId(cloudPhysicalHostPool.getDataCenter());
        if (Objects.isNull(cloudEnv)) {
            return;
        }
        // 有操作系统生成一个实例
        ResVm resVm = new ResVm();
        BeanUtils.copyProperties(cloudPhysicalHostPool, resVm);
        resVm.setInstanceId(cloudPhysicalHostPool.getAssetNumber());
        resVm.setCloudEnvId(cloudEnv.getId());
        resVm.setOrgSid(cloudEnv.getOrgSid());
        resVm.setInstanceName(cloudPhysicalHostPool.getHostName());
        resVm.setManagemenPassword(CrytoUtilSimple.decrypt(cloudPhysicalHostPool.getManagemenPassword(), true));
        resVm.setStatus(getResVmStatus(cloudPhysicalHostPoolUpdate.getPowerStatus()));
        resVm.setManageStatus(ResVmManageStatus.UNUNITED);
        resVm.setServerType(ServerType.INSTANCE);
        resVm.setStartTime(new Date());
        resVm.setPhysicalHostPoolId(cloudPhysicalHostPool.getId());
        resVm.setVmCategory(VmCategory.BAREMETAL);
        BasicWebUtil.prepareInsertParams(resVm, cloudPhysicalHostPool.getCreatedBy());
        resVm.setId(UuidUtil.getUuid().replace("-", ""));
        this.resVmService.insertSelective(resVm);
    }

    private String getResVmStatus(String powerStatus) {
        if (powerStatus.equalsIgnoreCase(PowerStatus.OFF)) {
            return ResVmStatus.STOPPED;
        }
        return ResVmStatus.RUNNING;
    }


    private CloudEnv getJoinDataCenterCloudEnvId(String resourcePool) {
        // 查询是否接入了数据中心的云环境
        if (StringUtil.isNotBlank(resourcePool)) {
            Criteria criteria = new Criteria();
            criteria.put("cloudEnvType", CloudEnvType.MAAS.getValue().get(0));
            List<CloudEnv> cloudEnvs = this.cloudEnvMapper.selectByParams(criteria);
            for (CloudEnv cloudEnv : cloudEnvs) {
                Map map = JsonUtil.fromJson(cloudEnv.getAttrData(), Map.class);
                if (Objects.equals(resourcePool, Convert.toStr(map.get(CloudEnvTenantKey.RESOURCE_POOL)))) {
                    return cloudEnv;
                }
            }
        }
        return null;
    }

    private CloudPhysicalHostPool getCloudPhysicalHostPool(Long physicalHostPoolId) throws Exception {
        AtomicReference<CloudPhysicalHostPool> atomicReference = new AtomicReference<>();
        Long finalId = physicalHostPoolId;
        RetryUtil.retry(10, 5, TimeUnit.SECONDS, false, () -> {
            CloudPhysicalHostPool cloudPhysicalHostPool = cloudPhysicalHostPoolService.selectByPrimaryKey(finalId);
            if (cloudPhysicalHostPool == null) {
                throw new RetryException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1867841409));
            } else {
                atomicReference.set(cloudPhysicalHostPool);
            }
        });
        return atomicReference.get();
    }


    /**
     * 加入装机平台
     *
     * @param machineCreateResult
     */
    @Message(refKey = "#machineCreateResult.options.get('physicalHostPoolId')", envId = "#machineCreateResult.cloudEnvId", msgType = ServerMsgType.VM, opUser = "#machineCreateResult.opUser", operate = OperateEnum.JOIN_MAAS, success = "#machineCreateResult.success", refNameKey = "#machineCreateResult.hostName", errorMsg = "#machineCreateResult.errMsg")
    public void handleMessage(
            @LogParam("machineCreateResult") @MessageParam("machineCreateResult") MachineCreateResult machineCreateResult) {
        logger.info("加入装机平台回调 | 回调参数 ： {}", JsonUtil.toJson(machineCreateResult));
        Map<String, Object> options = machineCreateResult.getOptions();
        Long physicalHostPoolId = Long.valueOf(
                options.get(WebConstants.PhysicalHostInfo.PHYSICAL_HOST_POOL_ID).toString());
        try {
            CloudPhysicalHostPool cloudPhysicalHostPool = cloudPhysicalHostPoolService.selectByPrimaryKey(
                    physicalHostPoolId);
            if (Objects.isNull(cloudPhysicalHostPool)) {
                return;
            }
            if (machineCreateResult.isSuccess()) {
                CloudPhysicalHostPool cloudPhysicalHostPoolUpdate = new CloudPhysicalHostPool();
                cloudPhysicalHostPoolUpdate.setId(physicalHostPoolId);
                if (Objects.nonNull(machineCreateResult.getCpuCount())) {
                    cloudPhysicalHostPoolUpdate.setCpu(machineCreateResult.getCpuCount().intValue());
                }
                if (Objects.nonNull(machineCreateResult.getMemory())) {
                    cloudPhysicalHostPoolUpdate.setMemory(machineCreateResult.getMemory().intValue());
                }
                cloudPhysicalHostPoolUpdate.setStorage(machineCreateResult.getStorage());
                cloudPhysicalHostPoolUpdate.setPhysicalUUID(machineCreateResult.getUuid());
                cloudPhysicalHostPoolUpdate.setPowerStatus(machineCreateResult.getPowerStatus());
                cloudPhysicalHostPoolUpdate.setJoinMaas(true);

                cloudPhysicalHostPoolUpdate.setCpuModel(machineCreateResult.getCpuModel());
                this.cloudPhysicalHostPoolService.updateByPrimaryKeySelective(cloudPhysicalHostPoolUpdate);

                // 更新网卡信息
                this.cloudPhysicalHostPoolService.updatePhysicalHostInterface(physicalHostPoolId,
                                                                              machineCreateResult.getInterfaceSet());

                // 更新存储分区信息
                this.cloudPhysicalHostPoolService.updatePhysicalHostPartition(physicalHostPoolId,
                                                                              machineCreateResult.getBlockDeviceSet(),
                                                                              machineCreateResult.getSpecialFsSet());

                ResVm resVm = resVmService.selectByPhysicalHostPoolId(physicalHostPoolId);
                if (Objects.nonNull(resVm)) {
                    resVm.setCpu(cloudPhysicalHostPoolUpdate.getCpu());
                    resVm.setMemory(cloudPhysicalHostPoolUpdate.getMemory());
                    resVmService.updateByPrimaryKeySelective(resVm);
                }
            } else {
                CloudPhysicalHostPool cloudPhysicalHostPoolUpdate = new CloudPhysicalHostPool();
                cloudPhysicalHostPoolUpdate.setId(physicalHostPoolId);
                cloudPhysicalHostPoolUpdate.setStatusInfo("系统错误,请联系管理员. " + machineCreateResult.getErrMsg());
                cloudPhysicalHostPoolUpdate.setJoinMaas(false);
                this.cloudPhysicalHostPoolService.updateByPrimaryKeySelective(cloudPhysicalHostPoolUpdate);
            }
        } catch (Exception e) {
            logger.error("创建machine回调异常 : {}", Throwables.getStackTraceAsString(e));
            CloudPhysicalHostPool cloudPhysicalHostPoolUpdate = new CloudPhysicalHostPool();
            cloudPhysicalHostPoolUpdate.setId(physicalHostPoolId);
            cloudPhysicalHostPoolUpdate.setStatusInfo("系统错误,请联系管理员. " + machineCreateResult.getErrMsg());
            cloudPhysicalHostPoolUpdate.setPhysicalStatus(CloudPhysicalHostStatus.JOIN_MAAS_FAILED);
            cloudPhysicalHostPoolUpdate.setPhysicalStatus(CloudPhysicalHostStatus.JOIN_MAAS_FAILED);
            this.cloudPhysicalHostPoolService.updateByPrimaryKeySelective(cloudPhysicalHostPoolUpdate);
        } finally {
            // 还原实例状态
            String resVmId = (String) options.get("resVmId");
            if (StringUtil.isNotBlank(resVmId)) {
                ResVm resVmUpdate = new ResVm();
                resVmUpdate.setId(resVmId);
                resVmUpdate.setStatus(
                        machineCreateResult.isSuccess() ? ResVmStatus.RUNNING : options.get("resVmStatus").toString());
                this.resVmService.updateByPrimaryKeySelective(resVmUpdate);
            }
        }
    }

    /**
     * 物理机操作: 启动 关闭 重启
     *
     * @param machineOperateResult
     */
    @Transactional(rollbackFor = Exception.class)
    @Message(refKey = "#machineOperateResult.physicalHostPoolId", envId = "#machineOperateResult.cloudEnvId", msgType = ServerMsgType.MACHINE, opUser = "#machineOperateResult.opUser", operate = OperateEnum.OPERATE, success = "#machineOperateResult.success", refNameKey = "#machineOperateResult.hostName", operateAction = "#machineOperateResult.action", errorMsg = "#machineOperateResult.errMsg")
    public void handleMessage(
            @LogParam("machineOperateResult") @MessageParam("machineOperateResult") MachineOperateResult machineOperateResult) {
        logger.info("物理机[{}] 操作[{}] {}", machineOperateResult.getPhysicalHostPoolId(), machineOperateResult.getAction(),
                    machineOperateResult.isSuccess() ? "成功" : "失败");
        Map<String, Object> options = machineOperateResult.getOptions();
        try {

            if (machineOperateResult.isSuccess()) {
                String action = machineOperateResult.getAction();
                String powerStatus = null;
                if (VmOperation.CHECK_POWER.equals(action)) {
                    powerStatus = machineOperateResult.getPowerStatus();
                } else {
                    powerStatus = VmOperation.STOP.equals(action) ? PowerStatus.OFF : PowerStatus.ON;
                }
                CloudPhysicalHostPool cloudPhysicalHostPoolUpdate = new CloudPhysicalHostPool();
                cloudPhysicalHostPoolUpdate.setId(machineOperateResult.getPhysicalHostPoolId());
                cloudPhysicalHostPoolUpdate.setPowerStatus(powerStatus);
                this.cloudPhysicalHostPoolService.updateByPrimaryKeySelective(cloudPhysicalHostPoolUpdate);

                this.resVmService.updateResVmStatusByPhysicalHostPoolId(machineOperateResult.getPhysicalHostPoolId(),
                                                                        powerStatus.equalsIgnoreCase(PowerStatus.OFF)
                                                                        ? ResVmStatus.STOPPED : ResVmStatus.RUNNING);
            } else {
                CloudPhysicalHostPool cloudPhysicalHostPoolUpdate = new CloudPhysicalHostPool();
                cloudPhysicalHostPoolUpdate.setId(machineOperateResult.getPhysicalHostPoolId());
                cloudPhysicalHostPoolUpdate.setPowerStatus(
                        Objects.nonNull(options.get("originPowerStatus")) ? String.valueOf(
                                options.get("originPowerStatus")) : PowerStatus.UNKNOWN);
                cloudPhysicalHostPoolUpdate.setStatusInfo(machineOperateResult.getAction() + "操作失败.");
                this.cloudPhysicalHostPoolService.updateByPrimaryKeySelective(cloudPhysicalHostPoolUpdate);
            }
        } catch (Exception e) {
            logger.error("物理机操作出错: {}", Throwables.getStackTraceAsString(e));
        }
    }


}
