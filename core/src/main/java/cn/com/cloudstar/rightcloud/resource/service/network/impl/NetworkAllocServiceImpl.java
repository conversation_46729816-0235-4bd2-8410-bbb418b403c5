/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.service.network.impl;

import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnv;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.Network;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.NetworkIp;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResVm;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.constants.res.status.ResVmStatus;
import cn.com.cloudstar.rightcloud.common.constants.res.type.NetworkManagement;
import cn.com.cloudstar.rightcloud.common.exception.BizException;
import cn.com.cloudstar.rightcloud.common.util.IPUtil;
import cn.com.cloudstar.rightcloud.common.util.WebUtil;
import cn.com.cloudstar.rightcloud.core.pojo.dto.cloud.NetworkAlloc;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicInfoUtil;
import cn.com.cloudstar.rightcloud.resource.dao.network.NetworkAllocMapper;
import cn.com.cloudstar.rightcloud.resource.service.env.CloudEnvService;
import cn.com.cloudstar.rightcloud.resource.service.network.NetworkAllocService;
import cn.com.cloudstar.rightcloud.resource.service.network.NetworkIpService;
import cn.com.cloudstar.rightcloud.resource.service.network.NetworkService;
import cn.com.cloudstar.rightcloud.resource.service.server.ResVmService;
import cn.hutool.core.date.DateUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class NetworkAllocServiceImpl implements NetworkAllocService {

    private static final Logger logger = LoggerFactory.getLogger(NetworkAllocServiceImpl.class);

    @Autowired
    private NetworkAllocMapper networkAllocMapper;

    @Autowired
    @Lazy
    private ResVmService resVmService;

    @Autowired
    private NetworkIpService networkIpService;

    @Autowired
    @Lazy
    private NetworkService networkService;

    @Autowired
    private CloudEnvService cloudEnvService;

    @Override
    public int countByParams(Criteria example) {
        int count = this.networkAllocMapper.countByParams(example);
        logger.debug("count: {}", count);
        return count;
    }

    @Override
    public NetworkAlloc selectByPrimaryKey(Long id) {
        return this.networkAllocMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<NetworkAlloc> selectByParams(Criteria example) {
        return this.networkAllocMapper.selectByParams(example);
    }

    @Override
    public int deleteByPrimaryKey(Long id) {
        return this.networkAllocMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(NetworkAlloc record) {
        return this.networkAllocMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(NetworkAlloc record) {
        return this.networkAllocMapper.updateByPrimaryKey(record);
    }

    @Override
    public int deleteByParams(Criteria example) {
        return this.networkAllocMapper.deleteByParams(example);
    }

    @Override
    public int updateByParamsSelective(NetworkAlloc record, Criteria example) {
        return this.networkAllocMapper.updateByParamsSelective(record, example.getCondition());
    }

    @Override
    public int updateByParams(NetworkAlloc record, Criteria example) {
        return this.networkAllocMapper.updateByParams(record, example.getCondition());
    }

    @Override
    public int insert(NetworkAlloc record) {
        return this.networkAllocMapper.insert(record);
    }

    @Override
    public int insertSelective(NetworkAlloc record) {
        return this.networkAllocMapper.insertSelective(record);
    }

    @Override
    public boolean shareSubnet2Env(NetworkAlloc networkAlloc) {
        // 清理网络关联表
        this.networkAllocMapper.deleteByNetworkId(networkAlloc.getNetworkId());

        // 重新将关联关系插入网络关联表
        List<NetworkAlloc> allocs = Lists.newArrayList();
        networkAlloc.getCloudEnvIds().forEach(cloudEnvId -> {
            NetworkAlloc alloc;
            if ((alloc = networkAlloc.cloneBean()) != null) {
                alloc.setCloudEnvId(cloudEnvId);
                allocs.add(alloc);
            }
        });

        if (!CollectionUtils.isEmpty(allocs)) {
            this.networkAllocMapper.mutiInsert(allocs);
        }

        // 触发IP同步
        networkAlloc.getCloudEnvIds().add(networkAlloc.getOriginCloudEnvId());

        Criteria criteria = new Criteria();
        criteria.put("cloudEnvIds", networkAlloc.getCloudEnvIds());
        criteria.put("statusNotIn", Arrays.asList(ResVmStatus.DELETING, ResVmStatus.CREATE_FAILURE, ResVmStatus.PENDING,
                                                  ResVmStatus.DELETED));
        List<ResVm> resVmList = resVmService.selectByExample(criteria);

        Map<String, String> map = new HashMap<>(16);
        for (ResVm resVm : resVmList) {
            List<String> reservedList = new ArrayList<>();
            IPUtil.getIpList(reservedList, resVm.getInnerIp());
            IPUtil.getIpList(reservedList, resVm.getPublicIp());

            reservedList.forEach(ip -> map.put(ip, resVm.getId()));
        }

        List<NetworkIp> networkIps = networkIpService.selectByExample(
                new Criteria("networkId", networkAlloc.getNetworkId()));
        List<NetworkIp> usedNetworkIps = Lists.newArrayList();
        List<NetworkIp> unusedNetworkIps = Lists.newArrayList();
        String account = BasicInfoUtil.getAuthUser().getAccount();
        for (NetworkIp networkIp : networkIps) {
            prepareUpdateParams(networkIp, account);
            if (map.containsKey(networkIp.getIpAddress())) {
                networkIp.setAllocateTargetId(map.get(networkIp.getIpAddress()));
                if (NetworkManagement.AVAILABLE.equals(networkIp.getStatus())) {
                    networkIp.setStatus(NetworkManagement.UNAVAILABLE);
                }
                usedNetworkIps.add(networkIp);
            } else {
                if (NetworkManagement.UNAVAILABLE.equals(networkIp.getStatus())) {
                    networkIp.setStatus(NetworkManagement.AVAILABLE);
                }
                networkIp.setAllocateTargetId(null);
                unusedNetworkIps.add(networkIp);
            }
        }
        if (!CollectionUtils.isEmpty(usedNetworkIps)) {
            networkIpService.batchUpdateByPrimaryKey(usedNetworkIps);
        }
        if (!CollectionUtils.isEmpty(unusedNetworkIps)) {
            NetworkIp networkIp = new NetworkIp();
            networkIp.setStatus(NetworkManagement.AVAILABLE);
            networkIp.setAllocateTargetId(Strings.nullToEmpty(null));
            Map<String, Object> example = new HashMap<>();
            example.put("ids", unusedNetworkIps.stream().map(NetworkIp::getId).collect(Collectors.toList()));
            networkIpService.updateByExampleSelective(networkIp, example);
        }

        networkService.syncUnusedIpByNetworkId(networkAlloc.getNetworkId());
        return true;
    }

    private void prepareUpdateParams(NetworkIp networkIp, String account) {
        networkIp.setUpdatedDt(DateUtil.date());
        networkIp.setUpdatedBy(account);
    }

    @Override
    public List<CloudEnv> queryEnv2Share(Long networkId, List<String> cloudEnvTypes) {
        Criteria criteria = new Criteria("cloudEnvTypes", cloudEnvTypes);
        List<CloudEnv> cloudEnvs = cloudEnvService.selectAllCloudEnvByCompanyId(criteria);

        Network network = networkService.selectByPrimaryKey(networkId);
        assertNotNull(network);

        // 查询当前子网已关联的云环境
        criteria.clear();
        criteria.put("networkId", networkId);
        List<Long> cloudEnvIds = networkAllocMapper.selectCloudEnvIdByNetwork(criteria);

        criteria.clear();
        if (cloudEnvIds != null && cloudEnvIds.size() > 0) {
            criteria.put("cloudEnvIds", cloudEnvIds);
        }
        List<Network> networks = networkService.selectVmNetworks(criteria);

        return cloudEnvs.stream().filter(cloudEnv -> {
            // 如果云环境已经有当前共享子网，不过滤
            if (!CollectionUtils.isEmpty(cloudEnvIds) && cloudEnvIds.contains(cloudEnv.getId())) {
                return true;
            }
            // 过滤掉已经有当前子网cidr的云环境
            return networks.stream()
                           .noneMatch(net -> cloudEnv.getId().equals(network.getCloudEnvId()) && (
                                   net.getCidr().equals(network.getCidr()) || IPUtil.checkIpRepeat(
                                           IPUtil.getNetworkList(net.getCidr()),
                                           IPUtil.getNetworkList(network.getCidr()))));
        }).collect(Collectors.toList());
    }

    private void assertNotNull(Network network) {
        if (null == network) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1626536526));
        }
    }
}
