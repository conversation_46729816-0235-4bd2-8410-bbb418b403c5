/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.engine.event.callback.handler.dispatch;

import com.fasterxml.jackson.core.type.TypeReference;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.convert.Convert;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnv;
import cn.com.cloudstar.rightcloud.common.schedule.bean.MessageDTO;
import cn.com.cloudstar.rightcloud.common.util.SaasUtil;
import cn.com.cloudstar.rightcloud.resource.engine.event.callback.handler.AbstractMessageHandler;
import cn.com.cloudstar.rightcloud.resource.service.env.CloudEnvService;

/**
 * The type EnvDispatchMessageHandler.
 * <p>
 *
 * <AUTHOR>
 * @date 2019/11/13
 */
@Slf4j
@Component
public class EnvDispatchMessageHandler extends AbstractMessageHandler<MessageDTO> {

    @Autowired
    private CloudEnvService cloudEnvService;

    @Override
    public void execute(MessageDTO messageDTO) {
        if (SaasUtil.isSaasEnable()) {
            log.info("开始清理SaaS过期资源");
            List<Long> orgSids = Convert.convert(new TypeReference<List<Long>>() {
            }.getType(), messageDTO.getData());
            List<CloudEnv> cloudEnvs = cloudEnvService.selectAllCloudEnv();
            cloudEnvs = cloudEnvs.stream()
                                 .filter(cloudEnv -> orgSids.contains(cloudEnv.getOrgSid()))
                                 .collect(Collectors.toList());
            cloudEnvs.forEach(cloudEnv -> this.cloudEnvService.removeCloudEnv(cloudEnv.getId()));
            log.info("清理SaaS过期资源完成");
        }
    }
}
