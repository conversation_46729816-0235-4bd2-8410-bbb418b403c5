<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2018 Cloud-Star, Inc. All Rights Reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.cloudstar.rightcloud.resource.dao.redundance.core.ServerTemplateRevisionMapper">
    <resultMap id="BaseResultMap"
               type="cn.com.cloudstar.rightcloud.remote.api.pojo.system.maintenance.templates.ServerTemplateRevision">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="main_template_id" property="mainTemplateId" jdbcType="BIGINT"/>
        <result column="server_template_id" property="serverTemplateId" jdbcType="BIGINT"/>
        <result column="commit_comments" property="commitComments" jdbcType="VARCHAR"/>
        <result column="version_name" property="versionName" jdbcType="VARCHAR"/>
        <result column="created_dt" property="createdDt" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.mainTemplateId != null">
                and main_template_id = #{condition.mainTemplateId}
            </if>
            <if test="condition.serverTemplateId != null">
                and server_template_id = #{condition.serverTemplateId}
            </if>
            <if test="condition.commitComments != null">
                and commit_comments = #{condition.commitComments}
            </if>
            <if test="condition.versionName != null">
                and version_name = #{condition.versionName}
            </if>
            <if test="condition.versionNameNotEqual != null">
                and version_name != #{condition.versionNameNotEqual}
            </if>
            <if test="condition.createdDt != null">
                and created_dt = #{condition.createdDt}
            </if>
            <if test="condition.serverTemplateIds != null">
                and server_template_id in
                <foreach collection="condition.serverTemplateIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.serverTemplateRevisionIds != null">
                and id in
                <foreach collection="condition.serverTemplateRevisionIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </trim>
    </sql>
    <sql id="Base_Column_List">
        id, main_template_id, server_template_id, commit_comments, version_name, created_dt
    </sql>
    <select id="selectByParams" resultMap="BaseResultMap"
            parameterType="cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from server_template_revision
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>

    </select>
</mapper>
