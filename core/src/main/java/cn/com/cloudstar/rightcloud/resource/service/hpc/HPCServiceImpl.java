/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.service.hpc;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.logging.log4j.util.Strings;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.LongSummaryStatistics;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.adapter.core.MQException;
import cn.com.cloudstar.rightcloud.adapter.core.MQHelper;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.ComputeNodeInfo;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.FDAuthInfo;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.FDBandwidth;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.FDExpansionResourceInfo;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.FDHPCClusterActive;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.FDPublicIP;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.FDResourceInfo;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.FDRootVolume;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.FDShareInfo;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.FDStorageInfo;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.GraphicNodeInfo;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.HPCApplyInfo;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.HPCClusterCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.HPCClusterDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.HPCClusterInfoID;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.HPCConfigInfo;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.HPCDrpApplyInfo;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.HPCDrpConfigInfo;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.HPCExpansionApplyInfo;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.HPCExpansionNode;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.HPCNodeJobInfo;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.HPCNodeOperate;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.HPCPreDrpMgmt;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.HPCRemoveNode;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.HPCShareApplyInfo;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.HPCShareServiceCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.HPCShareServiceDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.ManagementNodeInfo;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.PublicIPInfo;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.TaskInfoScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.VNCNodeInfo;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.VPCPeeringInfo;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.FDHPCClusterActiveResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.FDNodeJobMemberResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.FDNodeOperateMemberResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.FDTaskInfoResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.HPCClusterCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.HPCClusterDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.HPCClusterInfoIDResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.HPCExpansionNodeResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.HPCNodeJobInfoResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.HPCNodeOperateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.HPCPreDrpMgmtResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.HPCRemoveNodeResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.HPCShareServiceDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.HPCShareServiceResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.result.ServiceOrderQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.RouteTableUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.RouteTableUpdate.RouteTableRoute;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.RouteTableUpdate.Routetable;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.VpcPeeringAccept;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.RouteTableUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.VpcPeeringAcceptResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.VpcPeeringScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.VpcPeeringScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.vo.HPCClusterInfoVO;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.vo.HPCComputeNodeInfoVO;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.vo.HPCManagementNodeInfoVO;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.vo.HPCNodeDifferVo;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.vo.HPCVNCNodeInfoVO;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.SharePredeployQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.SharePredeployQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.DeleteHPCAccount;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.result.DeleteHPCAccountResult;
import cn.com.cloudstar.rightcloud.basic.data.platform.CloudClientFactory;
import cn.com.cloudstar.rightcloud.basic.data.pojo.base.Base;
import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnv;
import cn.com.cloudstar.rightcloud.basic.data.pojo.config.SysConfig;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.Network;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResBms;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResFloatingIp;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResHpcClusterDeleteNodeTask;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResShare;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResVd;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResVm;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResVpc;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResVpcPeering;
import cn.com.cloudstar.rightcloud.common.constants.BillingConstants;
import cn.com.cloudstar.rightcloud.common.constants.NotificationConsts;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.constants.res.status.ResHpcClusterNodeStatus;
import cn.com.cloudstar.rightcloud.common.constants.res.status.ResHpcClusterStatus;
import cn.com.cloudstar.rightcloud.common.constants.res.type.CloudEnvType;
import cn.com.cloudstar.rightcloud.common.constants.status.OrderStatus;
import cn.com.cloudstar.rightcloud.common.constants.type.OrderType;
import cn.com.cloudstar.rightcloud.common.constants.type.ServiceManage;
import cn.com.cloudstar.rightcloud.common.enums.CommonCodeEnum;
import cn.com.cloudstar.rightcloud.common.enums.CommonPropertyKeyEnum;
import cn.com.cloudstar.rightcloud.common.enums.HPCPropertyKey;
import cn.com.cloudstar.rightcloud.common.enums.resource.HPCClusterTypeEnum;
import cn.com.cloudstar.rightcloud.common.enums.resource.ResChangeTypeEnum;
import cn.com.cloudstar.rightcloud.common.enums.resource.ResourceTypeEnum;
import cn.com.cloudstar.rightcloud.common.exception.BizException;
import cn.com.cloudstar.rightcloud.common.mq.request.BaseNotificationMqBean;
import cn.com.cloudstar.rightcloud.common.pojo.BaseGridReturn;
import cn.com.cloudstar.rightcloud.common.schedule.helper.ScheduleHelper;
import cn.com.cloudstar.rightcloud.common.util.*;
import cn.com.cloudstar.rightcloud.common.util.excel.ExcelUtils;
import cn.com.cloudstar.rightcloud.common.util.json.JsonUtil;
import cn.com.cloudstar.rightcloud.core.pojo.dto.hpc.HPCComputeExcel;
import cn.com.cloudstar.rightcloud.core.pojo.dto.hpc.HpcNodeStatusDTO;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.BmsInfo;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.ComputePointInfo;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.EcsInfo;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.ManagerPointInfo;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResChangeRecord;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResHpcCluster;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResHpcClusterExample;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResHpcClusterPool;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResHpcClusterResource;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResHpcClusterResourceExample;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResShareRule;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.ShareInfo;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.StorageInfo;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.VmNodeInfo;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.VncPointInfo;
import cn.com.cloudstar.rightcloud.core.pojo.enums.CcmTaskStatusEnum;
import cn.com.cloudstar.rightcloud.module.support.access.cache.AuthUserHolder;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.Org;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.User;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicInfoUtil;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicWebUtil;
import cn.com.cloudstar.rightcloud.common.util.MapsKit;
import cn.com.cloudstar.rightcloud.module.support.access.util.PlaintextShieldUtil;
import cn.com.cloudstar.rightcloud.module.support.file.storage.bean.enums.StoragePathEnum;
import cn.com.cloudstar.rightcloud.module.support.file.storage.bean.vo.StorageResult;
import cn.com.cloudstar.rightcloud.module.support.file.storage.service.StorageService;
import cn.com.cloudstar.rightcloud.remote.api.iam.pojo.HcsoUser;
import cn.com.cloudstar.rightcloud.remote.api.iam.service.HcsoUserRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.*;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.hpc.HpcSyncCloudEnvRequest.SyncKey;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResShareType;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResVmType;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.ResZone;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.res.request.QueryResVmTypeByParamsRequest;
import cn.com.cloudstar.rightcloud.remote.api.pojo.system.ldap.LdapSyncRequest;
import cn.com.cloudstar.rightcloud.remote.api.pojo.system.order.UpdateOrderStatusRequest;
import cn.com.cloudstar.rightcloud.remote.api.system.service.system.ServiceOrderStatusRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.system.service.system.SysConfigRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.system.service.system.SysNotifyConfigRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.system.service.user.BssUserRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.system.service.user.OrgRemoteService;
import cn.com.cloudstar.rightcloud.remote.api.system.service.user.SysHpcPassRemoteService;
import cn.com.cloudstar.rightcloud.resource.dao.env.CloudEnvMapper;
import cn.com.cloudstar.rightcloud.resource.dao.hpc.ResHpcClusterDeleteNodeTaskMapper;
import cn.com.cloudstar.rightcloud.resource.dao.hpc.ResHpcClusterMapper;
import cn.com.cloudstar.rightcloud.resource.dao.hpc.ResHpcClusterPoolMapper;
import cn.com.cloudstar.rightcloud.resource.dao.hpc.ResHpcClusterResourceMapper;
import cn.com.cloudstar.rightcloud.resource.dao.network.NetworkMapper;
import cn.com.cloudstar.rightcloud.resource.dao.network.ResFloatingIpMapper;
import cn.com.cloudstar.rightcloud.resource.dao.network.ResVpcMapper;
import cn.com.cloudstar.rightcloud.resource.dao.server.*;
import cn.com.cloudstar.rightcloud.resource.dao.share.ResShareMapper;
import cn.com.cloudstar.rightcloud.resource.dao.storage.ResVdMapper;
import cn.com.cloudstar.rightcloud.resource.dao.user.SystemMUserMapper;
import cn.com.cloudstar.rightcloud.resource.dao.zone.ResZoneMapper;
import cn.com.cloudstar.rightcloud.resource.service.env.CloudEnvService;
import cn.com.cloudstar.rightcloud.resource.service.server.ResVmTypeService;
import cn.com.cloudstar.rightcloud.resource.service.share.ResShareRuleService;
import cn.com.cloudstar.rightcloud.resource.service.share.ShareService;

import camundajar.impl.scala.Product;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.logging.log4j.util.Strings;
import org.apache.poi.util.IOUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.util.*;
import java.util.stream.Collectors;

import static cn.com.cloudstar.rightcloud.common.constants.NotificationConsts.SEND_NOTIFICATION_EXCHANGE;
import static cn.com.cloudstar.rightcloud.common.constants.res.status.ResHpcClusterStatus.UNSUBSCRIBING_RETRY;
import static java.lang.Math.round;

/**
 * <AUTHOR>
 * @date 2021/12/6 15:31
 */
@Service
@Slf4j
public class HPCServiceImpl implements HPCService {

    private static final String HPC_DRP = "HPC-DRP";

    private static final String  PORT = "22";
    private static final String DME_OSP = "DME-OSP";
    @Autowired
    private ResShareMapper resShareMapper;
    @Autowired
    private ResVmMapper resVmMapper;
    @Autowired
    private ResBmsMapper resBmsMapper;
    @Autowired
    private ResVdMapper resVdMapper;
    @Autowired
    private ResVpcMapper resVpcMapper;
    @Autowired
    private ResVpcPeeringMapper resVpcPeeringMapper;
    @Autowired
    private ResFloatingIpMapper resFloatingIpMapper;
    @Autowired
    private ResVmTypeMapper resVmTypeMapper;
    @Autowired
    private ResHpcClusterResourceMapper resHpcClusterResourceMapper;
    @Autowired
    private ResShareTypeMapper resShareTypeMapper;
    @Autowired
    private ShareService shareService;
    @Autowired
    private ResHpcClusterMapper resHpcClusterMapper;
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private ResHpcClusterPoolMapper resHpcClusterPoolMapper;
    @Autowired
    private CloudEnvMapper cloudEnvMapper;

    @DubboReference
    private SysHpcPassRemoteService sysHpcPassRemoteService;

    @Autowired
    private CloudEnvService cloudEnvService;

    @Autowired
    private NetworkMapper networkMapper;
    @Autowired
    private ResShareRuleService resShareRuleService;
    @Autowired
    private ResZoneMapper resZoneMapper;
    @DubboReference
    private OrgRemoteService orgRemoteService;
    @DubboReference
    private HcsoUserRemoteService hcsoUserRemoteService;
    @DubboReference
    private ServiceOrderStatusRemoteService serviceOrderRemoteService;
    @DubboReference
    private SysConfigRemoteService sysConfigRemoteService;
    @DubboReference
    private BssUserRemoteService bssUserRemoteService;
    @Autowired
    private ResChangeRecordMapper resChangeRecordMapper;
    @Autowired
    private SystemMUserMapper systemMUserMapper;
    @Autowired
    private ResHpcClusterDeleteNodeTaskMapper resHpcClusterDeleteNodeTaskMapper;
    @DubboReference
    private SysNotifyConfigRemoteService sysNotifyConfigRemoteService;

    @Autowired
    private ResVmTypeService resVmTypeService;

    @Autowired
    private StorageService storageService;

    private static final IdWorker ID_WORKER = new IdWorker();

    private static final String JSON_LABEL = "label";
    private static final String JSON_ATTR_KEY = "attrKey";
    private static final String JSON_VALUE = "value";
    private static final String HPC_POINT_TYPE_CLI = "CCS_CLI";
    private static final String HPC_POINT_TYPE_VNC = "VNC";
    private static final String HPC_POINT_TYPE_CCP = "CCP_MASTER";
    private static final String VNC = "VNC";
    private static final String VNC_LOWER = "vnc";
    private static final String MANAGER = "manager";
    private static final String COMPUTE = "compute";
    private static final String HPC_DRP_STANDARD = "hpc-drp-standard";
    /**
     * 加密前缀
     */
    private static final String AES_PREFIX = "DECRYPT--";

    /**
     * /
     */
    public static final String SYMBOL = "/";

    /**
     * .xlsx
     */
    public static final String XLSX = ".xlsx";

    @Value("${upload.base.path:}")
    private String uploadBasePath;

    @Override
    public void updateClusterStatus(UpdateClusterStatusRemote updateClusterStatusRemote) {
        String endTime = DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN);
        resHpcClusterPoolMapper.updateClusterStatus(endTime);
    }

    @Override
    @Transactional
    public CreateHPCSharePoolResult createSharePool(CreateHPCSharePool createHPCSharePool) {
        CreateHPCSharePoolResult createHPCSharePoolResult = new CreateHPCSharePoolResult();
        ResHpcCluster resHpcCluster = selectByPrimaryKey(createHPCSharePool.getHPCClusterID());
        Long shareId = this.checkHpcShareCluster(resHpcCluster);
        if (Objects.nonNull(shareId)) {
            createHPCSharePoolResult.setPrivateShareId(shareId);
            return createHPCSharePoolResult;
        }
        //本次创建共享资源池 全局id
        User authUser = BasicInfoUtil.getAuthUser();
        ResShareType resShareType = resShareTypeMapper.selectByPrimaryKey(
                Integer.valueOf(resHpcCluster.getShareType()));
        if (Objects.isNull(resShareType)) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_232981732));
        }
        CloudEnv cloudEnv = getFDCloudEnv();
        HPCShareServiceCreate createHPCSharePoolAction = CloudClientFactory.buildMQBean(cloudEnv.getId(),
                                                                                        HPCShareServiceCreate.class);
        List<FDStorageInfo> storageInfos = insertClusterShareResource(createHPCSharePool, createHPCSharePoolResult,
                                                                      resShareType);
        HPCShareApplyInfo hpcShareApplyInfo = new HPCShareApplyInfo();
        hpcShareApplyInfo.setStorageInfo(storageInfos);
        createHPCSharePoolAction.setHpcShareApplyInfo(hpcShareApplyInfo);

        createHPCSharePoolAction.setApplyTaskDescription(resHpcCluster.getDescription());
        createHPCSharePoolAction.setHpcClusterID(resHpcCluster.getPoolUuid());
        createHPCSharePoolAction.setRealUserName(authUser.getAccount());
        createHPCSharePoolAction.setApplyTaskID("");
        HPCShareServiceResult result = null;
        try {
            result = (HPCShareServiceResult) MQHelper.rpc(
                    createHPCSharePoolAction);
            resHpcCluster.setResourceId(result.getHPCShareServiceID());
        } catch (MQException e) {
            e.printStackTrace();
            // 解析报错信息
            String message = e.getMessage();
            message = message.substring(message.indexOf('[')+1,message.lastIndexOf(']'));
            try {
                com.alibaba.fastjson.JSONObject jsonObject = JSON.parseObject(message);
                throw new BizException(jsonObject.getJSONObject(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1134559)).getString(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_658606)));
            } catch (Exception exception) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERROR_INSERT_FAILURE_PARAM, new Object[]{CommonCodeEnum.HPC.getValue()}));
            }
        }
        //更新资源池状态
        if (!result.isSuccess()) {
            throw new BizException(result.getErrMsg());
        }
        //更新资源池状态
        if (StringUtils.isNotBlank(result.getTaskID())) {
            log.info("createSharePool-success-[{}]", result.getTaskID());
            resHpcCluster.setTaskId(result.getTaskID());
        } else if (StringUtils.isNotBlank(result.getTaskId())) {
            log.info("createSharePool-success-[{}]", result.getTaskId());
            resHpcCluster.setTaskId(result.getTaskId());
        }
        resHpcCluster.setStatus("configing");
        BasicWebUtil.prepareUpdateParams(resHpcCluster);
        updateByPrimaryKeySelective(resHpcCluster);
        return createHPCSharePoolResult;
    }

    private Long checkHpcShareCluster(ResHpcCluster resHpcCluster) {
        Long shareId = null;
        if (StringUtils.isNotBlank(resHpcCluster.getResourceId()) && !ResHpcClusterStatus.APPLY.equals(resHpcCluster.getStatus())) {
            // 异常情况，资源已经创建，但是审批流程停留在预审批
            List<ResHpcClusterResource> sfs = resHpcClusterResourceMapper.getByClusterIdAndClusterType(resHpcCluster.getId(), "SFS");
            for (ResHpcClusterResource resHpcClusterResource:sfs) {
                ResShare resShare = shareService.selectByPrimaryKey(resHpcClusterResource.getResourceId());
                if (!resShare.getIsVirtual()) {
                    shareId = resShare.getId();
                }else {
                    shareId = 0L;
                }
            }
        }
        return shareId;
    }

    private ResShare getCommonResShares(ResShareType resShareType, Integer type) {
        Criteria criteria = new Criteria();
        criteria.put("isShareDir", type);
        criteria.put("shareTypeId", resShareType.getId());
        List<ResShare> resShares = resShareMapper.selectByParams(criteria);
        if (resShares.size() != 1) {
            String msg = resShares.size() == 0 ? "未设置共享目录" : "存在多个共享目录";
            throw new BizException("存储集群"
                                           + "【"
                                           + resShareType.getName()
                                           + "】"
                                           + msg);

        }
        return resShares.get(0);
    }

    /**
     * 插入HPC共享资源池弹性文件资源
     *
     * @param createHPCSharePool
     * @param createHPCSharePoolResult
     * @param resShareType
     */
    private List<FDStorageInfo> insertClusterShareResource(CreateHPCSharePool createHPCSharePool,
                                                           CreateHPCSharePoolResult createHPCSharePoolResult,
                                                           ResShareType resShareType) {
        Org currentOrgInfo = BasicInfoUtil.getCurrentOrgInfo();
        User authUser = BasicInfoUtil.getAuthUser();
        ResShare commonResShare = getCommonResShares(resShareType, 1);
        String shareName = StrUtil.toString(JSONUtil.parseObj(commonResShare.getMetadata()).get("share_name"));
        ResHpcCluster resHpcCluster = selectByPrimaryKey(createHPCSharePool.getHPCClusterID());
        ResHpcClusterPool pool =  resHpcClusterPoolMapper.getByClusterId(resHpcCluster.getPoolUuid());
        List<StorageInfo> storageInfoList = com.alibaba.fastjson.JSONObject.parseArray((String)pool.getStorageInfo(), StorageInfo.class);
        for (StorageInfo storageInfo : storageInfoList) {
            storageInfo.getShareInfos().forEach(e -> {
                  if ("Share".equals(e.getPathType())) {
                      if (!StringUtils.equals(e.getDPCShareName(), String.format("/%s", shareName))) {
                      throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_279831393));
                      }
                  }
            });
        }
        ResShare vCommon = insertCommonShare(createHPCSharePool, currentOrgInfo, authUser, commonResShare);
        ArrayList<ResShare> resShares = Lists.newArrayList(vCommon);
        createHPCSharePoolResult.setCommonShareId(vCommon.getId());
        ResShare privateShare = insertPrivateShare(createHPCSharePool, currentOrgInfo, authUser,
                                                   resShareType);
        if (privateShare != null) {
            createHPCSharePoolResult.setPrivateShareId(privateShare.getId());
            resShares.add(privateShare);
        }
        return createStorgeInfo(resShareType, resShares);
    }

    /**
     * 插入专属文件系统
     *
     * @param createHPCSharePool
     * @param currentOrgInfo
     * @param authUser
     * @param resShareType
     */
    private ResShare insertPrivateShare(CreateHPCSharePool createHPCSharePool,
                                        Org currentOrgInfo, User authUser, ResShareType resShareType) {
        //找存储集群中未使用的弹性文件
        if (createHPCSharePool.getShareSize() != null) {
            ResShare privilegeShare = null;
            List<String> fileSystemIdList = this.getPreClusterFileSystemIdList();
            List<ResShare> shareList = resShareMapper.findOneUnuseShareByShareTypeId(resShareType);
            // 校验文件系统，只能选择ccm不存在的文件系统
            Criteria criteria = new Criteria();
            criteria.put("cloudEnvType", CloudEnvType.FUSION_DIRECTOR.getValue().get(0));
            List<CloudEnv> cloudEnvs = cloudEnvService.selectByParams(criteria);
            SharePredeployQuery sharePredeployQuery = CloudClientFactory.buildMQBean(cloudEnvs.get(0).getId(), SharePredeployQuery.class);
            for (ResShare share : shareList) {
                sharePredeployQuery.setFileSystemID(share.getUuid());
                Boolean isAllow = this.checkFileSystem(sharePredeployQuery, fileSystemIdList);
                if (isAllow) {
                    privilegeShare = share;
                    break;
                }
            }

            if (Objects.isNull(privilegeShare)) {
                throw new BizException("存储集群"
                                               + "【"
                                               + resShareType.getName()
                                               + "】"
                                               + "无可用专属文件系统");
            }
            //扩缩容
            if (!createHPCSharePool.getShareSize().equals(privilegeShare.getSize())) {
                Long privilegeShareId = privilegeShare.getId();
                BasicInfoUtil.replaceUserToInvoke(
                        () -> shareService.action(privilegeShareId, createHPCSharePool.getShareSize()),
                        100L);
            }
            privilegeShare.setOrgSid(currentOrgInfo.getOrgSid());
            privilegeShare.setHpcVersion(3);
            privilegeShare.setStartTime(new Date());
            privilegeShare.setChargeType(BillingConstants.ChargeType.POST_PAID);
            privilegeShare.setSize(createHPCSharePool.getShareSize());
            resShareMapper.updateByPrimaryKey(privilegeShare);
            //插入resHpcClusterResource
            insertClusterResource(createHPCSharePool, currentOrgInfo, authUser, privilegeShare);
            return privilegeShare;
        }
        return null;
    }

    private Boolean checkFileSystem(SharePredeployQuery sharePredeployQuery, List<String> fileSystemIdList) {
        if (fileSystemIdList.contains(sharePredeployQuery.getFileSystemID())) {
            // 过滤预部署HPC的文件系统
            return false;
        }
        try {
            SharePredeployQueryResult sharePredeployQueryResult = (SharePredeployQueryResult)MQHelper.rpc(sharePredeployQuery);
            if (Objects.nonNull(sharePredeployQueryResult) && StringUtils.isNotBlank(sharePredeployQueryResult.getTenantName())) {
                return false;
            }
        } catch (MQException e) {
            e.printStackTrace();
        }
        log.info("预部署文件系统查询-checkFileSystem-共享资源池绑定专属存储，文件系统[{}]可用", sharePredeployQuery.getFileSystemID());
        return true;
    }

    /**
     * 插入虚拟共享文件目录
     *
     * @param createHPCSharePool
     * @param currentOrgInfo
     * @param authUser
     * @param resShare
     */
    private ResShare insertCommonShare(CreateHPCSharePool createHPCSharePool, Org currentOrgInfo, User authUser,
                                       ResShare resShare) {
        createHPCSharePool.setCommonShareSize(createHPCSharePool.getCommonShareSize() != null ? createHPCSharePool.getCommonShareSize(): 0);
        resShare.setSize(resShare.getSize() != null ? resShare.getSize(): 0);
        resShare.setSizeAllocate(resShare.getSizeAllocate() != null ? resShare.getSizeAllocate(): 0);
        if (createHPCSharePool.getCommonShareSize() > (resShare.getSize() - resShare.getSizeAllocate())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_2096368886) + createHPCSharePool.getCommonShareSize() + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_716970));
        }
        resShareMapper.increaseAllocateSize(resShare.getId(), createHPCSharePool.getCommonShareSize());
        //插入虚拟共享目录
        ResShare shareInsert = BeanConvertUtil.convert(resShare, ResShare.class);
        shareInsert.setId(null);
        shareInsert.setIsVirtual(true);
        shareInsert.setSizeAllocate(null);
        shareInsert.setIsShareDir(0);
        BasicWebUtil.prepareInsertParams(shareInsert);
        shareInsert.setOrgSid(currentOrgInfo.getOrgSid());
        shareInsert.setOrgId(currentOrgInfo.getOrgSid());
        shareInsert.setZoneId(-1L);
        shareInsert.setSubnetId(-1L);
        shareInsert.setSize(createHPCSharePool.getCommonShareSize());
        shareInsert.setCreatedOrgSid(currentOrgInfo.getOrgSid());
        shareInsert.setHpcVersion(3);
        shareInsert.setStartTime(new Date());
        shareInsert.setChargeType(BillingConstants.ChargeType.None);
        shareInsert.setShareTypeId(resShare.getShareTypeId());
        shareInsert.setFileSystemName(shareInsert.getName());
        resShareMapper.insertSelective(shareInsert);
        insertClusterResource(createHPCSharePool, currentOrgInfo, authUser, shareInsert);
        return shareInsert;
    }

    private void insertClusterResource(CreateHPCSharePool createHPCSharePool, Org currentOrgInfo, User authUser,
                                       ResShare share) {
        ResHpcClusterResource resHpcClusterResource = new ResHpcClusterResource();
        resHpcClusterResource.setClusterId(createHPCSharePool.getHPCClusterID());
        resHpcClusterResource.setResourceId(share.getId());
        resHpcClusterResource.setResourceType("SFS");
        resHpcClusterResource.setCreatedOrgSid(currentOrgInfo.getOrgSid() + "");
        resHpcClusterResource.setOwnerId(authUser.getUserSid() + "");
        BasicWebUtil.prepareInsertParams(resHpcClusterResource);
        resHpcClusterResourceMapper.insert(resHpcClusterResource);
    }

    @Override
    public Long insertClusterResource(ResHpcClusterResource resHpcClusterResource) {
        resHpcClusterResourceMapper.insert(resHpcClusterResource);
        return resHpcClusterResource.getId();
    }


    /**
     * 插入HPC资源池表 并返回实体
     *
     * @param insertHPCSharePool
     */
    @Override
    @Transactional
    public ResHpcCluster insertResHpcCluster(InsertHPCSharePool insertHPCSharePool) {
        try {
            ResHpcClusterPool resHpcClusterPool = resHpcClusterPoolMapper.selectByPrimaryKey(
                    insertHPCSharePool.getHPCClusterPoolID());
            boolean isHPCOffline = Integer.valueOf(4).equals(resHpcClusterPool.getHpcVersion());
            if (StrUtil.isBlank(resHpcClusterPool.getShareTypeId()) && !isHPCOffline) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_508933341));
            }
            String s = resHpcClusterPool.getManagerPointInfo().toString();
            ManagementNodeInfo[] managementNodeInfos = com.alibaba.fastjson.JSONObject.parseArray(s,ManagementNodeInfo.class).toArray(new ManagementNodeInfo[0]);
            Org currentOrgInfo = BasicInfoUtil.getCurrentOrgInfo();
            User authUser = BasicInfoUtil.getAuthUser();
            ResHpcCluster resHpcCluster = new ResHpcCluster();
            resHpcCluster.setClusterType(resHpcClusterPool.getClusterType());
            resHpcCluster.setBusinessCategory(resHpcClusterPool.getBusinessCategory());
            //todo 获取配置密码
            resHpcCluster.setAdminPassword("");
            if (Integer.valueOf(3).equals(resHpcClusterPool.getHpcVersion())) {
                CloudEnv cloudEnv = getFDCloudEnv();
                resHpcCluster.setCloudEnvId(cloudEnv.getId());
            }
            if (Integer.valueOf(4).equals(resHpcClusterPool.getHpcVersion())) {
                resHpcCluster.setResourceId(StringUtil.getUUID());
            }
            resHpcCluster.setHpcVersion(resHpcClusterPool.getHpcVersion());
            resHpcCluster.setShareType(resHpcClusterPool.getShareTypeId());
            resHpcCluster.setPoolName(resHpcClusterPool.getClusterName());
            resHpcCluster.setCcpVersion(resHpcClusterPool.getCcpVersion());
            List<ManagementNodeInfo> ccpList = Arrays.stream(managementNodeInfos)
                                                     .filter(a -> HPC_POINT_TYPE_CCP.equals(a.getHPCNodeType()))
                                                     .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(ccpList)) {
                ccpList.forEach(ccp -> {
                    String ccpFIPPort = StringUtils.isEmpty(ccp.getFipPort())?"18080":ccp.getFipPort();
                    resHpcCluster.setCcpInternelAddress(resHpcCluster.getCcpInternelAddress() == null ?
                                                                "https://" + ccp.getFIP() + StringUtil.COLON+ ccpFIPPort
                                                                : resHpcCluster.getCcpInternelAddress() + ","
                                                                        + "https://" + ccp.getFIP() + StringUtil.COLON+ ccpFIPPort);
                    String ccpEIPPort = StringUtils.isEmpty(ccp.getEipPort())?"18080":ccp.getEipPort();
                    resHpcCluster.setCcpExternalAddress(resHpcCluster.getCcpExternalAddress() == null ?
                                                                "https://" + ccp.getEIP() + StringUtil.COLON+ ccpEIPPort
                                                                : resHpcCluster.getCcpExternalAddress() + ","
                                                                        + "https://" + ccp.getEIP() + StringUtil.COLON+ ccpEIPPort);
                });
            }
            resHpcCluster.setChargeType(insertHPCSharePool.getChargeType());
            resHpcCluster.setDescription(currentOrgInfo.getOrgName() + "申请开通共享资源池");
            resHpcCluster.setAdminUser(resHpcClusterPool.getSystemAdminUser());
            List<ManagementNodeInfo> ccsList = Arrays.stream(managementNodeInfos)
                                                     .filter(a -> HPC_POINT_TYPE_CLI.equals(a.getHPCNodeType()))
                                                     .collect(Collectors.toList());
            String port = cn.com.cloudstar.rightcloud.common.util.PropertiesUtil.getProperty("login.node.port");
            if (Objects.isNull(port)){
                port = PORT;
            }
            if (CollectionUtil.isNotEmpty(ccsList)){
                String finalPort = port;
                ccsList.forEach(ccs -> {

                    String ccsFIPPort = StringUtils.isEmpty(ccs.getEipPort())?finalPort:ccs.getFipPort();
                    resHpcCluster.setLoginNodeExternalAddress(resHpcCluster.getLoginNodeExternalAddress() == null ?
                                                                      ccs.getEIP() + StrUtil.COLON + ccsFIPPort
                                                                      : resHpcCluster.getLoginNodeExternalAddress()
                                                                              + "," + ccs.getEIP() + StrUtil.COLON + ccsFIPPort);
                    String ccsVIPPort = StringUtils.isEmpty(ccs.getVipPort())?finalPort:ccs.getVipPort();
                    resHpcCluster.setLoginNodeInternalAddress(resHpcCluster.getLoginNodeInternalAddress() == null ?
                                                                      ccs.getVIP() + StrUtil.COLON + ccsVIPPort
                                                                      : resHpcCluster.getLoginNodeInternalAddress()
                                                                              + "," + ccs.getVIP() + StrUtil.COLON + ccsVIPPort);
                });
            }
            resHpcCluster.setOwnerId(authUser.getUserSid());

            CloudEnv cloudEnv = this.getHCSOCloudEnv();

            if (!CloudEnvType.HPCOFFLINE.equals(cloudEnv.getCloudEnvType())) {
                HcsoUser hcsoUser = hcsoUserRemoteService.selectByRefUserId(authUser.getUserSid());
                if (Objects.isNull(hcsoUser)){
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1312838344));
                }
                resHpcCluster.setTenantId(hcsoUser.getId());
            }

            String hpcProductName = resHpcClusterPool.getHpcVersion() == 4?"共享资源池-二期":"共享资源池";
            resHpcCluster.setName((resHpcClusterPool.getHpcVersion() - 3 == 0) ? "共享资源池-SAAS" : hpcProductName);
            resHpcCluster.setTaskDescription(currentOrgInfo.getOrgName() + "申请开通共享资源池");
            resHpcCluster.setOrgSid(currentOrgInfo.getOrgSid());
            resHpcCluster.setCreatedOrgSid(currentOrgInfo.getOrgSid());
            resHpcCluster.setStatus("apply");
            resHpcCluster.setPoolUuid(resHpcClusterPool.getClusterId());
            resHpcCluster.setQuotaReleaseDays(insertHPCSharePool.getQuotaReleaseDays());
            resHpcCluster.setOsArchitectureType(resHpcClusterPool.getOsArchitectureType());
            BasicWebUtil.prepareInsertParams(resHpcCluster);
            log.info("HPC共享资源池信息导入-insertResHpcCluster-name: {}", resHpcCluster.getName());
            resHpcClusterMapper.insert(resHpcCluster);
            return resHpcCluster;
        } finally {
            //预检请求 直接回滚
            if (insertHPCSharePool.getTest()) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            }
        }
    }

    /**
     * 释放hpc共享资源池
     *
     * @param releaseHPCSharePool
     *
     * @returre
     */
    @Override
    public void releaseShareHPC(ReleaseHPCSharePool releaseHPCSharePool) {
        ResHpcCluster resHpcCluster = new ResHpcCluster();
        try {

            resHpcCluster = resHpcClusterMapper.selectByPrimaryKey(releaseHPCSharePool.getHPCClusterID());

            dealReleaseShareHPC(releaseHPCSharePool, resHpcCluster);
        } catch (Exception e) {
            //resHpcCluster.setStatus(ResHpcClusterStatus.AVALIABLE);
            resHpcCluster.setTaskDescription(e.getMessage());
            resHpcClusterMapper.updateByPrimaryKeySelective(resHpcCluster);

            throw new BizException(e.getMessage());
        }

    }

    @Transactional
    public void dealReleaseShareHPC(ReleaseHPCSharePool releaseHPCSharePool, ResHpcCluster resHpcCluster) {
        User authUser = BasicInfoUtil.getAuthUser();
        log.info("当前用户名称：[{}]", authUser.getAccount());

        log.info("释放hpc共享资源池-dealReleaseShareHPC-调用删除share，逻辑删除虚拟文件系统");
        //调用删除share
        //逻辑删除虚拟文件系统
        ResHpcClusterResourceExample hpcClusterResourceExample = new ResHpcClusterResourceExample();
        hpcClusterResourceExample.createCriteria().andClusterIdEqualTo(resHpcCluster.getId());
        //集群关联资源关系
        List<ResHpcClusterResource> resHpcClusterResources
                = resHpcClusterResourceMapper.selectByExample(hpcClusterResourceExample);
        if (CollectionUtil.isNotEmpty(resHpcClusterResources)) {
            for (ResHpcClusterResource resHpcClusterResource : resHpcClusterResources) {
                Long resourceId = resHpcClusterResource.getResourceId();
                ResShare resShare = resShareMapper.selectByPrimaryKey(resourceId);
                if (resShare != null) {
                    if (resShare.getIsVirtual()) {
                        ResShareType shareType = resShareTypeMapper.selectByPrimaryKey(
                                Integer.valueOf(resShare.getShareTypeId().intValue()));
                        ResShare commonResShares = getCommonResShares(shareType, 1);
                        commonResShares.setSizeAllocate(commonResShares.getSizeAllocate() - resShare.getSize());
                        resShareMapper.updateByPrimaryKey(commonResShares);
                        resShareMapper.logicDeleteShareById(resShare.getId());
                    } else {
                        if (!Integer.valueOf(3).equals(resHpcCluster.getHpcVersion())) {
                            resShareMapper.logicDeleteShareById(resShare.getId());
                        }
                    }
                }
            }
        }

        Exception exception = null;
        log.info("释放hpc共享资源池-dealReleaseShareHPC-判断ccp版本：[{}]", resHpcCluster.getCcpVersion());
        if (Integer.valueOf(2).equals(resHpcCluster.getCcpVersion())) {
            try {
                deleteAccount(resHpcCluster.getCcpInternelAddress(), releaseHPCSharePool);
            } catch (Exception e) {
                e.printStackTrace();
                exception = e;
            }
        }

        log.info("释放hpc共享资源池-dealReleaseShareHPC-判断hpc版本：[{}]", resHpcCluster.getHpcVersion());
        if (Objects.isNull(exception) && Integer.valueOf(3).equals(resHpcCluster.getHpcVersion())) {
            //防止重复调用
            if (StringUtils.isBlank(resHpcCluster.getUnsubscribeTaskId())
                    || ResHpcClusterStatus.UNSUBSCRIBING_RETRY.equals(releaseHPCSharePool.getStatus())) {
                HPCShareServiceDelete hpcClusterDelete = CloudClientFactory.buildMQBean(resHpcCluster.getCloudEnvId(),
                                                                                        HPCShareServiceDelete.class);
                hpcClusterDelete.setHpcClusterID(resHpcCluster.getResourceId());
                hpcClusterDelete.setApplyTaskID("");
                hpcClusterDelete.setRealUserName(authUser.getAccount());
                hpcClusterDelete.setApplyTaskDescription(authUser.getAccount() + "共享资源池退订");
                //调用ccm
                HPCShareServiceDeleteResult result = null;
                resHpcCluster.setStatus(StringUtils.isEmpty(releaseHPCSharePool.getStatus())
                        ? ResHpcClusterStatus.UNSUBSCRIBING : releaseHPCSharePool.getStatus());
                try {
                    result = (HPCShareServiceDeleteResult) MQHelper.rpc(
                            hpcClusterDelete);
                } catch (Exception e) {
                    e.printStackTrace();
                    exception = e;
                }
                if (result == null || !result.isSuccess()) {
                    exception = new BizException("退订共享资源池,调用ccm失败");
                } else {
                    String taskID = result.getTaskID();
                    taskID = StringUtils.isNotEmpty(taskID)? taskID:result.getTaskId();
                    resHpcCluster.setUnsubscribeTaskId(taskID);
                }
            }
        } else if (Integer.valueOf(4).equals(resHpcCluster.getHpcVersion())) {



            resHpcCluster.setProessStatus(CcmTaskStatusEnum.FINISH.getValue());
            resHpcCluster.setStatus(ResHpcClusterStatus.UNSUBSCRIBED);
        } else {
            resHpcCluster.setStatus(ResHpcClusterStatus.UNSUBSCRIBED);
        }

        if (exception != null) {
            log.error("释放hpc共享资源池-dealReleaseShareHPC-更新共享资源池异常[{}]", exception.getMessage());
            resHpcCluster.setStatus(ResHpcClusterStatus.UNSUBSCRIBE_ERROR);
            resHpcCluster.setTaskDescription("退订失败:原因:" + exception.getMessage());
        }

        resHpcClusterMapper.updateByPrimaryKey(resHpcCluster);
    }

    @Override
    public List<ResShare> getShareHPCSFS(Long id) {
        ResHpcClusterResourceExample hpcClusterResourceExample = new ResHpcClusterResourceExample();
        hpcClusterResourceExample.createCriteria().andClusterIdEqualTo(id).andResourceTypeIn(Arrays.asList("SFS","SFS2.0","SFS2"));
        //集群关联资源关系
        List<ResHpcClusterResource> resHpcClusterResources
                = resHpcClusterResourceMapper.selectByExample(hpcClusterResourceExample);

        List<Long> collect = resHpcClusterResources.stream()
                                                   .map(ResHpcClusterResource::getResourceId)
                                                   .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return new ArrayList<>();
        }
        Criteria criteria = new Criteria();
        criteria.put("resShareIds", collect);
        return resShareMapper.selectByParams(criteria);
    }

    @Override
    public List<ResShare> getShareDMEOSP(Long id) {
        ResHpcClusterResourceExample hpcClusterResourceExample = new ResHpcClusterResourceExample();
        hpcClusterResourceExample.createCriteria().andClusterIdEqualTo(id).andResourceTypeEqualTo(DME_OSP);
        //集群关联资源关系
        List<ResHpcClusterResource> resHpcClusterResources
            = resHpcClusterResourceMapper.selectByExample(hpcClusterResourceExample);

        List<Long> collect = resHpcClusterResources.stream()
            .map(ResHpcClusterResource::getResourceId)
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return new ArrayList<>();
        }
        Criteria criteria = new Criteria();
        criteria.put("resShareIds", collect);
        return resShareMapper.selectByParams(criteria);
    }



    @Override
    public List<ResShare> getDefaultShareHPCSFS(Long id) {
        ResHpcClusterResourceExample hpcClusterResourceExample = new ResHpcClusterResourceExample();
        hpcClusterResourceExample.createCriteria().andClusterIdEqualTo(id).andResourceTypeEqualTo("SFS");
        //集群关联资源关系
        List<ResHpcClusterResource> resHpcClusterResources
                = resHpcClusterResourceMapper.selectByExample(hpcClusterResourceExample);

        Criteria criteria = new Criteria();
        List<Long> collect = resHpcClusterResources.stream()
                                                   .map(ResHpcClusterResource::getResourceId)
                                                   .collect(Collectors.toList());
        criteria.put("resShareIds", collect);
        criteria.put("isClusterDefault", 1);
        return resShareMapper.selectByParams(criteria);
    }

    @Override
    public void postProcessReleaseHpcShare(Long id, Boolean flag) {
        ResHpcCluster resHpcCluster = resHpcClusterMapper.selectByPrimaryKey(id);
        if (resHpcCluster == null) {
            return;
        }
        Boolean releaseRes = false;
        if (UNSUBSCRIBING_RETRY.equals(resHpcCluster.getStatus()) ){
            releaseRes = true;
        }
        resHpcCluster.setStatus(ResHpcClusterStatus.UNSUBSCRIBED);
        //HPC专属资源池共享资源池才会删掉内置文件
        if (flag &&  HPCClusterTypeEnum.SAAS_SHARE.getCode().equals(resHpcCluster.getClusterType())) {
            //退订成功
            //查询专属文件系统并删除
            resHpcCluster.setStatus(ResHpcClusterStatus.UNSUBSCRIBED);
            ResHpcClusterResourceExample hpcClusterResourceExample = new ResHpcClusterResourceExample();
            hpcClusterResourceExample.createCriteria().andClusterIdEqualTo(resHpcCluster.getId());
            List<ResHpcClusterResource> resHpcClusterResources
                    = resHpcClusterResourceMapper.selectByExample(hpcClusterResourceExample);
            for (ResHpcClusterResource resHpcClusterResource : resHpcClusterResources) {
                Long resourceId = resHpcClusterResource.getResourceId();
                ResShare resShare = resShareMapper.selectByPrimaryKey(resourceId);
                if (resShare != null && !resShare.getIsVirtual()) {
                    BasicInfoUtil.replaceUserToInvoke(() -> shareService.deleteShareById(resShare.getId()), 100L);
                }
            }
        } else if(!flag){
            resHpcCluster.setStatus(ResHpcClusterStatus.UNSUBSCRIBE_ERROR);
        }
        //修改退订订单状态为完成
        if (HPCClusterTypeEnum.getHPCPrivateTypes().contains(resHpcCluster.getClusterType())) {
            UpdateOrderStatusRequest updateOrderStatusRequest = new UpdateOrderStatusRequest();
            updateOrderStatusRequest.setClusterId(id);
            updateOrderStatusRequest.setProductType(HPC_DRP);
            updateOrderStatusRequest.setStatus(OrderStatus.COMPLETED);
            updateOrderStatusRequest.setOrderType(OrderType.RELEASE);
            serviceOrderRemoteService.updateOrderStatusByClusterId(updateOrderStatusRequest);
        }
        try {
            Map<String, String> messageContent = new HashMap<>();
            this.assembleBaseMessageContent(messageContent);

            // 需要区分共享类型，res_hpc_cluster表的clusterType共享都是一样的，只能用名称
            String productCode = HPC_DRP;
            if (!HPCClusterTypeEnum.getHPCPrivateTypes().contains(resHpcCluster.getClusterType())) {
                String hpcSaas = "共享资源池-SAAS";
                productCode = "HPC";
                if (hpcSaas.equals(resHpcCluster.getName())) {
                    productCode = "HPC-SAAS";
                }
            }
            ServiceOrderQueryResult serviceOrder = serviceOrderRemoteService.selectReleaseOrderByClusterId(resHpcCluster.getId(),productCode);
            BaseNotificationMqBean baseNotificationMqBean = new BaseNotificationMqBean();
            List<cn.com.cloudstar.rightcloud.common.pojo.User> adminstrators = bssUserRemoteService.findAdminstratorsByEntityId(serviceOrder.getEntityId());
            LinkedHashSet<Long> toAdminUserIds = new LinkedHashSet<>();
            toAdminUserIds.addAll(adminstrators.stream().map(cn.com.cloudstar.rightcloud.common.pojo.User::getUserSid).collect(Collectors.toSet()));
            baseNotificationMqBean.setImsgUserIds(toAdminUserIds);
            baseNotificationMqBean.setEntityId(serviceOrder.getEntityId());


            //同步ldap
            log.info("hpcDrpPoolActi 进行LDAP同步处理！！！！！！！！[{}]", JSONUtil.toJsonStr(serviceOrder));
            LdapSyncRequest ldapSyncRequest = new LdapSyncRequest();
            ldapSyncRequest.setOrgId(resHpcCluster.getOrgSid());
            log.info("hpcDrpPoolActi ldapSyncRequest=[{}]", ldapSyncRequest);
            Boolean aBoolean = bssUserRemoteService.synHpcToLdapByLdapSyncRequest(ldapSyncRequest);
            log.info("hpcDrpPoolActi 进行Ldap同步结果[{}]", aBoolean);

            cn.com.cloudstar.rightcloud.common.pojo.User user = bssUserRemoteService.selectByPrimaryKey(resHpcCluster.getOwnerId());
            if (releaseRes){
                // 资源释放消息
                cn.com.cloudstar.rightcloud.basic.data.pojo.user.Org org = orgRemoteService.selectByPrimaryKey(resHpcCluster.getOrgSid());
                messageContent.put("orgName", org.getOrgName());
                messageContent.put("productName", serviceOrder.getProductName());
                messageContent.put("orderSn", serviceOrder.getOrderSn());
                messageContent.put("userAccount", user.getAccount());
                if (flag) {
                    baseNotificationMqBean.setMsgId( NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_RES_RELEASE_SUCCESS);
                }else{
                    baseNotificationMqBean.setMsgId( NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_RES_RELEASE_FAIL);
                }
                baseNotificationMqBean.setMap(messageContent);
                rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT, baseNotificationMqBean);
            }else {
                // 资源退订消息
                messageContent.put("productName", serviceOrder.getProductName());
                messageContent.put("orderSn", serviceOrder.getOrderSn());
                messageContent.put("userAccount", user.getAccount());
                messageContent.put("poolName", resHpcCluster.getName());
                baseNotificationMqBean.setMap(messageContent);
                if (HPCClusterTypeEnum.getHPCPrivateTypes().contains(resHpcCluster.getClusterType())) {
                    if (!flag) {
                        cn.com.cloudstar.rightcloud.basic.data.pojo.user.Org org = orgRemoteService.selectByPrimaryKey(
                                resHpcCluster.getOrgSid());
                        messageContent.put("orgName", org.getOrgName());
                        messageContent.put("orderSn", serviceOrder.getOrderSn());
                        baseNotificationMqBean.setMsgId(NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_HPC_PRIVATE_UNSUBSCRIBE_FAIL);
                        // 到期自动退订资源，发送自动退订
                        if (resHpcCluster.getAutoRelease() == 1) {
                            baseNotificationMqBean.setMsgId(NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_HPC_PRIVATE_UNSUBSCRIBE_AUTO_ERROR);
                        }
                        baseNotificationMqBean.setMap(messageContent);
                        rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT,
                                                      baseNotificationMqBean);
                    } else {
                        // 给管理员发消息
                        baseNotificationMqBean.setMsgId( NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_HPC_PRIVATE_UNSUBSCRIBE);
                        // 到期自动退订资源，发送自动退订
                        if (resHpcCluster.getAutoRelease() == 1) {
                            baseNotificationMqBean.setMsgId( NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_HPC_PRIVATE_UNSUBSCRIBE_AUTO);
                        }

                        baseNotificationMqBean.setMap(messageContent);
                        rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT,
                                                      baseNotificationMqBean);
                    }
                    //给租户发消息
                    BaseNotificationMqBean userBaseNotificationMqBean = new BaseNotificationMqBean();
                    userBaseNotificationMqBean.setMsgId(
                            NotificationConsts.ConsoleMsg.ProductMsg.TENANT_HPC_PRIVATE_UNSUBSCRIBE);
                    userBaseNotificationMqBean.setMap(messageContent);
                    userBaseNotificationMqBean.getImsgUserIds().add(resHpcCluster.getOwnerId());
                    userBaseNotificationMqBean.setEntityId(serviceOrder.getEntityId());
                    rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT,
                            userBaseNotificationMqBean);
                }else {
                    if (!flag) {
                        cn.com.cloudstar.rightcloud.basic.data.pojo.user.Org org = orgRemoteService.selectByPrimaryKey(resHpcCluster.getOrgSid());
                        messageContent.put("orgName", org.getOrgName());
                        messageContent.put("orderSn", serviceOrder.getOrderSn());
                        baseNotificationMqBean.setMsgId( NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_HPC_PRIVATE_UNSUBSCRIBE_FAIL);
                        baseNotificationMqBean.setMap(messageContent);
                    }else{
                        baseNotificationMqBean.setMsgId(NotificationConsts.PlatformMsg.ProductMsg.BSSMGT_HPC_SHARE_UNSUBSCRIBE);
                    }
                    rabbitTemplate.convertAndSend(SEND_NOTIFICATION_EXCHANGE, NotificationConsts.PRODUCT, baseNotificationMqBean);

                }
            }
        }catch (Exception e) {
            log.error("HPC退订/资源释放任务结果查询信息推送-postProcessReleaseHpcShare-失败：[{}]", e.getMessage());
        }
        resHpcClusterMapper.updateByPrimaryKeySelective(resHpcCluster);
    }
    private void assembleBaseMessageContent(Map<String, String> messageContent) {
        String companyName = PropertiesUtil.getProperty("company.name");
        String consoleUrl = PropertiesUtil.getProperty("rightcloud.console.url");
        String portalUrl = PropertiesUtil.getProperty("rightcloud.portal.url");
        messageContent.put("consoleUrl", consoleUrl);
        messageContent.put("portalUrl", portalUrl);
        messageContent.put("companyName", companyName);
        messageContent.put("logoUrl", consoleUrl + PropertiesUtil.getProperty("platform.large.logo"));
        messageContent.put("systemName", PropertiesUtil.getProperty("system.name"));
        messageContent.put("companyPhone", PropertiesUtil.getProperty("system.contact.number"));
    }

    private void deleteAccount(String ccpInternelAddress,
                               ReleaseHPCSharePool releaseHPCSharePool) {
        User authUser = BasicInfoUtil.getAuthUser();
        CloudEnv hcsoCloudEnv = getHCSOCloudEnv();
        DeleteHPCAccount daccount = CloudClientFactory.buildMQBean(hcsoCloudEnv.getId(), DeleteHPCAccount.class);
        daccount.setOptions(MapsKit.of("ROOT_URL", ccpInternelAddress));
        daccount.setTenantUserName(releaseHPCSharePool.getCcpUser());
        daccount.setTenantUserPass(releaseHPCSharePool.getCcpPassword());
        //用户组名ou
        HttpServletRequest request = BasicInfoUtil.getRequest();
        log.info("HttpServletRequest：[{}]", JSON.toJSONString(request));
        Org currentOrgInfo = BasicInfoUtil.getCurrentOrgInfo();
        log.info("当前组织：[{}]", JSON.toJSONString(currentOrgInfo));
        daccount.setUsername(currentOrgInfo.getLdapOu());
        DeleteHPCAccountResult result = null;
        try {
            result = (DeleteHPCAccountResult) MQHelper.rpc(daccount);
            if (!result.isSuccess()) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1085196812));
            }
        } catch (MQException e) {
            log.error(e.getMessage());
            throw new BizException(e);
        }
    }


    @Override
    public ResHpcCluster selectByPrimaryKey(Long id) {
        return resHpcClusterMapper.selectByPrimaryKey(id);
    }

    @Override
    public ResHpcClusterPool selectResHpcCluster(String id) {
        return resHpcClusterPoolMapper.selectUsernameAndPass(id);
    }

    @Override
    public List<ResHpcCluster> selectByParams(Criteria criteria) {
        return resHpcClusterMapper.selectByParams(criteria);
    }

    @Override
    public int updateByPrimaryKeySelective(ResHpcCluster resHpcCluster) {
        return resHpcClusterMapper.updateByPrimaryKeySelective(resHpcCluster);
    }


    /**
     * 获取fd云环境
     */
    private CloudEnv getFDCloudEnv() {
        Criteria envCriteria = new Criteria();
        envCriteria.put("cloudEnvType", CloudEnvType.FUSION_DIRECTOR.getValue().get(0));
        //只有一条
        List<CloudEnv> cloudEnvs = cloudEnvMapper.selectByParams(envCriteria);
        CloudEnv cloudEnv = cloudEnvs.get(0);
        return cloudEnv;
    }


    /**
     * 获取HCSO云环境
     */
    private CloudEnv getHCSOCloudEnv() {
        Criteria envCriteria = new Criteria();
        List<String> types = Arrays.asList(CloudEnvType.HCSO.getValue().get(0), CloudEnvType.HPCOFFLINE.getValue().get(0));
        envCriteria.put("cloudEnvTypes", types);
        //只有一条
        List<CloudEnv> cloudEnvs = cloudEnvMapper.selectByParams(envCriteria);
        CloudEnv cloudEnv = cloudEnvs.get(0);
        return cloudEnv;
    }


    public List<FDStorageInfo> createStorgeInfo(ResShareType resShareType, ArrayList<ResShare> resShares) {
        Org currentOrgInfo = BasicInfoUtil.getCurrentOrgInfo();
        User authUser = BasicInfoUtil.getAuthUser();
        List<FDStorageInfo> result = new LinkedList<>();
        if (CollectionUtil.isEmpty(resShares)) {
            return result;
        }
        //有MasterManagerIP的即为DPC  按MasterManagerIP分组 每个MasterManagerIP一个storageInfo
        Map<Object, List<ResShare>> shares = resShares.stream().collect(Collectors.groupingBy(r -> {
            JSONObject metadata = JSONUtil.parseObj(r.getMetadata());
            Object masterManagerIP = metadata.get("MasterManagerIP");
            return masterManagerIP != null ? masterManagerIP.toString() : "";
        }));
        List<ResShare> nfsList = shares.get("");
        ArrayList<FDShareInfo> nfsShareInfo = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(nfsList)) {
            for (ResShare resShare : nfsList) {
                ArrayList<FDShareInfo> shareInfo = getShareInfo(resShare);
                nfsShareInfo.addAll(shareInfo);
            }
        }
        shares.forEach((k, v) -> {
            //nfs 单独处理
            if ("".equals(k)) {
                return;
            }
            FDStorageInfo storageInfo = new FDStorageInfo();
            //同一个k下的 每个metadate应该都一样 所以v.get(0)
            JSONObject metadata = JSONUtil.parseObj(v.get(0).getMetadata());
            storageInfo.setMasterManagerIP(StrUtil.toString(metadata.get("MasterManagerIP")));
            storageInfo.setSlaveManagerIP(StrUtil.toString(metadata.get("SlaveManagerIP")));
            storageInfo.setFloatManagerIP(StrUtil.toString(metadata.get("FloatManagerIP")));
            storageInfo.setUser(authUser.getAccount());
            storageInfo.setGroup(currentOrgInfo.getLdapOu());
            storageInfo.setShareType(resShareType.getUuid() == null ? "" : resShareType.getUuid());
            storageInfo.setShareID("");
            ArrayList<FDShareInfo> shareInfo = Lists.newArrayList();
            v.forEach(resShare -> shareInfo.addAll(getShareInfo(resShare)));
            //每个storageInfo节点都加入nfs
            shareInfo.addAll(nfsShareInfo);
            storageInfo.setShareInfo(shareInfo);
            result.add(storageInfo);
        });
        //如果只传了NFS
        if (result.size() == 0) {
            FDStorageInfo storageInfo = new FDStorageInfo();
            storageInfo.setUser(authUser.getAccount());
            storageInfo.setGroup(currentOrgInfo.getLdapOu());
            storageInfo.setShareType(resShareType.getUuid() == null ? "" : resShareType.getUuid());
            storageInfo.setShareID("");
            ArrayList<FDShareInfo> shareInfo = Lists.newArrayList();
            //每个storageInfo节点都加入nfs
            shareInfo.addAll(nfsShareInfo);
            storageInfo.setShareInfo(shareInfo);
            result.add(storageInfo);
        }
        return result;
    }

    private ArrayList<FDShareInfo> getShareInfo(ResShare resShare) {
        Org currentOrgInfo = BasicInfoUtil.getCurrentOrgInfo();
        String mountPath;
        String pathType;
        if (resShare.getIsVirtual()) {
            //共享文件系统挂载路径
            mountPath = "/home/<USER>";
            pathType = "Share";
        } else {
            mountPath = "/home/" + currentOrgInfo.getLdapOu() + "/" + resShare.getName();
            pathType = "Private";
        }
        if (StrUtil.isNotBlank(resShare.getExportLocations())) {
            JSONArray objects = JSONUtil.parseArray(resShare.getExportLocations());
            String[] strings = objects.toArray(new String[0]);
            Optional<String> dpc = Arrays.stream(strings).filter(s -> s.startsWith("DPC")).findFirst();
            Optional<String> nfs = Arrays.stream(strings).filter(s -> s.startsWith("NFS")).findFirst();
            ArrayList<FDShareInfo> shareInfo = Lists.newArrayList(new FDShareInfo()
                                                                          .setDPCShareName(
                                                                                  dpc.orElse("").replace("DPC:", ""))
                                                                          .setNFSShareName(
                                                                                  nfs.orElse("").replace("NFS:", ""))
                                                                          .setPathType(pathType)
                                                                          .setMountPath(mountPath));
            if (resShare.getIsVirtual()) {
                shareInfo.add(new FDShareInfo()
                                      .setDPCShareName(dpc.orElse("").replace("DPC:", ""))
                                      .setNFSShareName(nfs.orElse("").replace("NFS:", ""))
                                      .setPathType("Common")
                                      .setMountPath("/home/<USER>"));
            }
            return shareInfo;
        }
        return Lists.newArrayList();
    }

    @Override
    @Transactional
    public ResHpcCluster insertHpcDrpResource(InsertHpcDrpResource insertHpcDrpResource) {
        try {
            if (!JSONUtil.isJson(insertHpcDrpResource.getProductConfig())) {
                return null;
            }
            Org currentOrgInfo = BasicInfoUtil.getCurrentOrgInfo();

            Long orgSid = currentOrgInfo.getOrgSid();
            User authUser = BasicInfoUtil.getAuthUser();
            Long userSid = authUser.getUserSid();
            Long cloudId = getFDCloudEnv().getId();

            // 集群信息
            ResHpcCluster hpcCluster = new ResHpcCluster();
            // VpcPeering
            ResVpcPeering vpcPeering = new ResVpcPeering();
            // 集群资源
            List<ResHpcClusterResource> hpcClusterResourceList = Lists.newArrayList();
            // ECS && BMS
            List<VmNodeInfo> nodes = Lists.newArrayList();
            // SFS ID
            List<Object> sfsIds = Lists.newArrayList();
            //计算节点
            List<VmNodeInfo> computeNode = Lists.newArrayList();

            // 弹性
            ResFloatingIp floatingIp = new ResFloatingIp();

            //HPC预部署流程
            String port = cn.com.cloudstar.rightcloud.common.util.PropertiesUtil.getProperty("login.node.port");
            if (Objects.isNull(port)){
                port = PORT;
            }
            if("hpc-drp-standard".equals(insertHpcDrpResource.getApplyType())){
                Criteria criteria = new Criteria();
                criteria.put("clusterId",insertHpcDrpResource.getResourceId());
                List<ResHpcClusterPool> resHpcClusterPools = resHpcClusterPoolMapper.selectByParam(criteria);
                if(resHpcClusterPools.size()>0){
                    hpcCluster.setResourceId(insertHpcDrpResource.getResourceId());
                    hpcCluster.setName(resHpcClusterPools.get(0).getClusterName());
                    hpcCluster.setBusinessCategory(resHpcClusterPools.get(0).getBusinessCategory());
                    Set<EcsInfo> ecsInfos=new HashSet<>();
                    //VPC的配置信息
                    vpcPeering.setAcceptVpcId(resHpcClusterPools.get(0).getVpcId());
                    vpcPeering.setAcceptCidr(resHpcClusterPools.get(0).getCidr());
                    vpcPeering.setAcceptRouterId(resHpcClusterPools.get(0).getPeeringId());
                    //统计管理节点
                    List<ManagerPointInfo> managerPointInfo = com.alibaba.fastjson.JSONObject.parseArray((String)resHpcClusterPools.get(0).getManagerPointInfo(), ManagerPointInfo.class);
                    Map<String, Long> managerPointInfoMap = managerPointInfo.stream()
                                                                            .collect(Collectors.groupingBy(ManagerPointInfo::getResourceID,
                                                                                                           Collectors.counting()));
                    //解析CCP,CLI登陆IP
                    List<ManagementNodeInfo> managerPointInfos = com.alibaba.fastjson.JSONObject.parseArray((String)resHpcClusterPools.get(0).getManagerPointInfo(), ManagementNodeInfo.class);

                    List<ManagementNodeInfo> ccpList = managerPointInfos.stream().
                            filter(a -> HPC_POINT_TYPE_CCP.equals(a.getHPCNodeType()))
                                                                        .collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(ccpList)) {
                        ccpList.forEach(ccp -> {
                            hpcCluster.setCcpInternelAddress(hpcCluster.getCcpInternelAddress() == null ?
                                                                     "https://" + ccp.getFIP() + ":18080"
                                                                     : hpcCluster.getCcpInternelAddress() + ","
                                                                             + "https://" + ccp.getFIP() + ":18080");
                            hpcCluster.setCcpExternalAddress(hpcCluster.getCcpExternalAddress() == null ?
                                                                     "https://" + ccp.getEIP() + ":18080"
                                                                     : hpcCluster.getCcpExternalAddress() + ","
                                                                             + "https://" + ccp.getEIP() + ":18080");
                        });
                    }

                    //解析CLI登陆节点IP
                    List<ManagementNodeInfo> ccsList = managerPointInfos.stream()
                                                                        .filter(a -> HPC_POINT_TYPE_CLI.equals(a.getHPCNodeType()))
                                                                        .collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(ccsList)) {
                        String finalPort = port;
                        ccsList.forEach(ccs -> {
                            hpcCluster.setLoginNodeExternalAddress(hpcCluster.getLoginNodeExternalAddress() == null ?
                                                                           ccs.getEIP() + StrUtil.COLON + finalPort
                                                                           : hpcCluster.getLoginNodeExternalAddress()
                                                                                   + "," + ccs.getEIP() + StrUtil.COLON + finalPort);
                            hpcCluster.setLoginNodeInternalAddress(hpcCluster.getLoginNodeInternalAddress() == null ?
                                                                           ccs.getVIP() + StrUtil.COLON + finalPort
                                                                           : hpcCluster.getLoginNodeInternalAddress()
                                                                                   + "," + ccs.getVIP() + StrUtil.COLON + finalPort);
                        });
                    }
                    log.info("HPC专属资源池信息导入-insertHpcDrpResource-cli对外地址：" + hpcCluster.getLoginNodeInternalAddress());
                    log.info("HPC专属资源池信息导入-insertHpcDrpResource-cli对内地址：" + hpcCluster.getLoginNodeExternalAddress());
                    //统计管理节点
                    for (Map.Entry<String, Long> entry : managerPointInfoMap.entrySet()) {
                        EcsInfo escInfo=new EcsInfo();
                        //  escInfo.setEcsFlavorRef(entry.getKey());
                        escInfo.setEcsAmount(entry.getValue());
                        Optional<ManagementNodeInfo> first = managerPointInfos.stream().
                                filter(computePoint -> Objects.equals(computePoint.getResourceID(),entry.getKey())).findFirst();
                        if(first.isPresent()) {
                            if(Objects.nonNull(first.get().getResourceID())){
                                escInfo.setInstanceId(first.get().getResourceID());
                            }
                            //内网IP
                            if(Objects.nonNull(first.get().getVIP())){
                                escInfo.setInnerIp(first.get().getVIP());
                            }
                            //外网IP
                            if(Objects.nonNull(first.get().getEIP())){
                                escInfo.setPublicIp(first.get().getEIP());
                            }
                            if(Objects.nonNull(first.get().getFIP())){
                                escInfo.setFloatingIp(first.get().getFIP());
                            }
                            escInfo.setHPCNodeType(first.get().getHPCNodeType());
                            escInfo.setEcsFlavorRef(first.get().getFlavorRef());
                            escInfo.setHostName(first.get().getHostName());
                        }

                        ecsInfos.add(escInfo);
                    }



                    //统计VNC节点信息
                    if(Objects.nonNull(resHpcClusterPools.get(0).getVncPointInfo())){
                        List<VncPointInfo> vncPointInfos= com.alibaba.fastjson.JSONObject.parseArray((String)resHpcClusterPools.get(0).getVncPointInfo(), VncPointInfo.class);
                        Map<String, Long> vncMap = vncPointInfos.stream()
                                                                .collect(Collectors.groupingBy(VncPointInfo::getResourceID,
                                                                                               Collectors.counting()));
                        for (Map.Entry<String, Long> entry : vncMap.entrySet()) {
                            EcsInfo escInfo=new EcsInfo();
                            escInfo.setEcsAmount(entry.getValue());
                            Optional<VncPointInfo> first = vncPointInfos.stream().
                                    filter(computePoint -> Objects.equals(computePoint.getResourceID(),entry.getKey())).findFirst();
                            if(first.isPresent()) {
                                if(Objects.nonNull(first.get().getResourceID())){
                                    escInfo.setInstanceId(first.get().getResourceID());
                                }
                                if(Objects.nonNull(first.get().getVIp())){
                                    escInfo.setInnerIp(first.get().getVIp());
                                }
                                if(Objects.nonNull(first.get().getEIp())){
                                    escInfo.setPublicIp(first.get().getEIp());
                                }
                                if(Objects.nonNull(first.get().getFIP())){
                                    escInfo.setFloatingIp(first.get().getFIP());
                                }
                                escInfo.setHostName(first.get().getHostName());
                                escInfo.setHPCNodeType(first.get().getHPCNodeType());
                                escInfo.setEcsFlavorRef(first.get().getFlavorRef());
                            }
                            ecsInfos.add(escInfo);
                        }
                    }

                    //统计esc计算节点数量
                    Map<String, LongSummaryStatistics> collect = ecsInfos.stream().collect(Collectors.groupingBy(EcsInfo::getInstanceId,
                                                                                                                 Collectors.summarizingLong(EcsInfo::getEcsAmount)));
                    //ECS
                    Set<EcsInfo> ecsInfoSet=new HashSet<>();
                    for (Map.Entry<String, LongSummaryStatistics> entry : collect.entrySet()) {
                        EcsInfo escInfo=new EcsInfo();

                        escInfo.setEcsAmount(entry.getValue().getSum());
                        Optional<EcsInfo> first = ecsInfos.stream().
                                filter(computePoint -> Objects.equals(computePoint.getInstanceId(),entry.getKey())).findFirst();
                        if(first.isPresent()) {
                            if(Objects.nonNull(first.get().getInstanceId())){
                                escInfo.setInstanceId(first.get().getInstanceId());
                            }
                            if(Objects.nonNull(first.get().getInnerIp())){
                                escInfo.setInnerIp(first.get().getInnerIp());
                            }
                            if(Objects.nonNull(first.get().getPublicIp())){
                                escInfo.setPublicIp(first.get().getPublicIp());
                            }
                            if(Objects.nonNull(first.get().getFloatingIp())){
                                escInfo.setFloatingIp(first.get().getFloatingIp());
                            }
                            escInfo.setHPCNodeType(first.get().getHPCNodeType());
                            escInfo.setEcsFlavorRef(first.get().getEcsFlavorRef());
                            escInfo.setHostName(first.get().getHostName());
                        }
                        for (Object object : JSONUtil.parseArray(insertHpcDrpResource.getProductConfig())) {
                            JSONObject jsonObject = (JSONObject) object;
                            Object value = jsonObject.get(JSON_VALUE);
                            if (Objects.isNull(value)) {
                                continue;
                            }
                            if (Objects.equals(jsonObject.get(JSON_ATTR_KEY), "managementNodeInfo")) {
                                computeNode.addAll(JsonUtil.fromJson(JsonUtil.toJson(jsonObject.get(JSON_VALUE)),
                                                                     new TypeReference<List<VmNodeInfo>>() {
                                                                     }));
                            }
                        }
                        if(computeNode.size()>0){
                            escInfo.setSize(computeNode.get(0).getSize());
                            escInfo.setVolumeType(computeNode.get(0).getVolumeType());
                        }else{
                            //默认值，防止扩容失败
                            escInfo.setSize("480");
                            escInfo.setVolumeType("SAS");
                        }
                        ecsInfoSet.add(escInfo);
                    }
                    ecsInfoSet.forEach(ccsCliInfo -> {
                        VmNodeInfo vmNodeInfo=new VmNodeInfo();
                        vmNodeInfo.setFlavorRef(ccsCliInfo.getEcsFlavorRef());
                        vmNodeInfo.setResourceType("ECS");
                        vmNodeInfo.setInstanceId(ccsCliInfo.getInstanceId());
                        vmNodeInfo.setInnerIp(ccsCliInfo.getInnerIp());
                        vmNodeInfo.setPublicIp(ccsCliInfo.getPublicIp());
                        vmNodeInfo.setFloatingIp(ccsCliInfo.getFloatingIp());
                        vmNodeInfo.setHostName(ccsCliInfo.getHostName());
                        vmNodeInfo.setNodeNum(ccsCliInfo.getEcsAmount());
                        vmNodeInfo.setSize(String.valueOf(ccsCliInfo.getEcsAmount()));
                        vmNodeInfo.setHpcNodeType(MANAGER);
                        vmNodeInfo.setHpcPointType(ccsCliInfo.getHPCNodeType());
                        vmNodeInfo.setSize(ccsCliInfo.getSize());
                        vmNodeInfo.setVolumeType(ccsCliInfo.getVolumeType());
                        nodes.add(vmNodeInfo);
                    });
                    //统计计算节点 BMS
                    List<ComputePointInfo> computePointInfo= com.alibaba.fastjson.JSONObject.parseArray((String)resHpcClusterPools.get(0).getComputePointInfo(), ComputePointInfo.class);
                    Map<String, Long> bmsColete = computePointInfo.stream()
                                                                  .collect(Collectors.groupingBy(ComputePointInfo::getResourceID,
                                                                                                 Collectors.counting()));
                    List<BmsInfo> bmsInfos=new ArrayList<>();
                    for (Map.Entry<String, Long> entry : bmsColete.entrySet()) {
                        BmsInfo bmsInfo=new BmsInfo();
                        bmsInfo.setComputeFlavorRef(entry.getKey());
                        bmsInfo.setBmsAmount(entry.getValue());
                        for (Object object : JSONUtil.parseArray(insertHpcDrpResource.getProductConfig())) {
                            JSONObject jsonObject = (JSONObject) object;
                            Object value = jsonObject.get(JSON_VALUE);
                            if (Objects.isNull(value)) {
                                continue;
                            }
                            if (Objects.equals(jsonObject.get(JSON_ATTR_KEY), "computeNodeInfo")) {

                                computeNode.addAll(JsonUtil.fromJson(JsonUtil.toJson(jsonObject.get(JSON_VALUE)),
                                                               new TypeReference<List<VmNodeInfo>>() {
                                                               }));


                            }
                        }
                        if(computeNode.size()>0){
                            bmsInfo.setSize(computeNode.get(0).getSize());
                            bmsInfo.setVolumeType(computeNode.get(0).getVolumeType());
                        }else{
                            //默认值，防止扩容失败
                            bmsInfo.setSize("480");
                            bmsInfo.setVolumeType("SAS");
                        }
                        bmsInfos.add(bmsInfo);
                    }
                    bmsInfos.forEach(bmsInfo -> {
                        VmNodeInfo vmNodeInfo=new VmNodeInfo();
                        Optional<ComputePointInfo> first = computePointInfo.stream().
                                filter(computePoint -> Objects.equals(computePoint.getResourceID(),bmsInfo.getComputeFlavorRef())).findFirst();
                        if(first.isPresent()) {
                            vmNodeInfo.setInstanceId(first.get().getResourceID());
                            vmNodeInfo.setInnerIp(first.get().getVIp());
                        }
                        vmNodeInfo.setHostName(first.get().getHostName());
                        vmNodeInfo.setFlavorRef(first.get().getFlavorRef());
                        vmNodeInfo.setResourceType("BMS");
                        vmNodeInfo.setHpcNodeType(COMPUTE);
                        vmNodeInfo.setNodeNum(bmsInfo.getBmsAmount());
                        vmNodeInfo.setSize(bmsInfo.getSize());
                        vmNodeInfo.setVolumeType(bmsInfo.getVolumeType());
                        nodes.add(vmNodeInfo);
                    });
                    log.debug("HPC专属资源池信息导入-insertHpcDrpResource-node:[{}]",nodes.toString());
                    //文件系统
                    if(Objects.nonNull(resHpcClusterPools.get(0).getStorageInfo())){
                        // 1111
                        List<StorageInfo> storageInfoList= com.alibaba.fastjson.JSONObject.parseArray((String)resHpcClusterPools.get(0).getStorageInfo(), StorageInfo.class);
                        log.info("HPC专属资源池信息导入-insertHpcDrpResource-内置文件系统,{}",JsonUtil.toJson(storageInfoList));
                        storageInfoList.forEach(storageInfo -> {
                            ShareInfo shareInfo = storageInfo.getShareInfos().get(0);
                            ResShare resShare=new ResShare();
                            resShare.setSize(shareInfo.getSize());
                            resShare.setUuid(shareInfo.getFileSystemID());
                            resShare.setCloudEnvId(cloudId);
                            resShare.setType("SFS");
                            resShare.setChargeType("PrePaid");
                            resShare.setIsPublic("false");
                            resShare.setName(shareInfo.getDPCShareName().replace("/",""));
                            resShare.setShareProto(shareInfo.getShareProto());
                            resShare.setVolumeType(storageInfo.getShareType());
                            resShare.setShareType(storageInfo.getShareType());
                            resShare.setStatus("available");
                            resShare.setDescription("预部署内置弹性文件");
                            resShare.setVersion(1L);
                            resShare.setSupportClusterType("predeploy");
                            resShare.setLinks(shareInfo.getMountPath());
                            resShare.setIsClusterDefault(true);
                            resShare.setCreatedDt(new Date());
                            resShare.setStartTime(new Date());
                            BasicWebUtil.prepareInsertParams(resShare, authUser);
                            resShare.setOrgSid(orgSid);
                            resShare.setFileSystemName(resShare.getName());
                            resShareMapper.insertSelective(resShare);
                            sfsIds.add(resShare.getId());
                        });
                        log.info("HPC专属资源池信息导入-insertHpcDrpResource-内置文件系统:sfsIds[{}]",sfsIds);
                    }
                    //修改pool的所属客户ID
                    ResHpcClusterPool resHpcClusterPool=resHpcClusterPools.get(0);
                    resHpcClusterPool.setOrgSid(orgSid);
                    //解析EIP带宽,可用区域
                    for (Object object : JSONUtil.parseArray(insertHpcDrpResource.getProductConfig())) {
                        JSONObject jsonObject = (JSONObject) object;
                        Object value = jsonObject.get(JSON_VALUE);
                        if (Objects.isNull(value)) {
                            continue;
                        }   else if (Objects.equals(jsonObject.get(JSON_ATTR_KEY), "publicIPInfo")) {
                            for (Object item : (List<Object>) value) {
                                JSONObject floatingIpJson = (JSONObject) item;
                                floatingIp.setBandWidth(Strings.isBlank(floatingIpJson.get("size").toString()) ? "0"
                                                                : floatingIpJson.get("size").toString());
                                floatingIp.setShareType(floatingIpJson.get("shareType").toString());
                                floatingIp.setInternetServiceProvider(floatingIpJson.get("type").toString());
                            }
                        }  else if (Objects.equals(jsonObject.get(JSON_ATTR_KEY), "availabilityZone")) {
                            hpcCluster.setAvailabilityZone(StrUtil.toString(value));
                        } else if (Objects.equals(jsonObject.get(JSON_ATTR_KEY), "scenario")) {
                            hpcCluster.setScenario(StrUtil.toString(value));
                        } else if (Objects.equals(jsonObject.get(JSON_ATTR_KEY), "shareType")) {
                            hpcCluster.setShareType(StrUtil.toString(value));
                        }
                    }
                    resHpcClusterPoolMapper.updateByPrimaryKey(resHpcClusterPool);
                }
            }else{
                for (Object object : JSONUtil.parseArray(insertHpcDrpResource.getProductConfig())) {
                    JSONObject jsonObject = (JSONObject) object;
                    Object value = jsonObject.get(JSON_VALUE);
                    if (Objects.isNull(value)) {
                        continue;
                    } else if (Objects.equals(jsonObject.get(JSON_ATTR_KEY), "scenario")) {
                        hpcCluster.setScenario(StrUtil.toString(value));
                    } else if (Objects.equals(jsonObject.get(JSON_ATTR_KEY), "shareType")) {
                        hpcCluster.setShareType(StrUtil.toString(value));
                    } else if (Objects.equals(jsonObject.get(JSON_ATTR_KEY), "computeNodeInfo")
                            || Objects.equals(jsonObject.get(JSON_ATTR_KEY), "managementNodeInfo")
                            || Objects.equals(jsonObject.get(JSON_ATTR_KEY), "vncNodeInfo")) {
                        nodes.addAll(JsonUtil.fromJson(JsonUtil.toJson(value), new TypeReference<List<VmNodeInfo>>() {
                        }));
                    } else if (Objects.equals(jsonObject.get(JSON_ATTR_KEY), "storageInfo")) {
                        sfsIds.addAll((List<Object>) value);
                    } else if (Objects.equals(jsonObject.get(JSON_ATTR_KEY), "vpcInfo")) {
                        for (Object item : (List<Object>) value) {
                            JSONObject vpcJson = (JSONObject) item;
                            hpcCluster.setVpcPeeringInfo(JSON.toJSONString(vpcJson));
                            vpcPeering.setAcceptVpcId(vpcJson.get("vpcId").toString());
                            vpcPeering.setAcceptProjectId(vpcJson.get("tenantId").toString());
                            vpcPeering.setAcceptCidr(vpcJson.get("cidr").toString());
                            vpcPeering.setAcceptRouterId(vpcJson.get("routerId").toString());
                        }
                    } else if (Objects.equals(jsonObject.get(JSON_ATTR_KEY), "publicIPInfo")) {
                        for (Object item : (List<Object>) value) {
                            JSONObject floatingIpJson = (JSONObject) item;
                            floatingIp.setBandWidth(Strings.isBlank(floatingIpJson.get("size").toString()) ? "0"
                                                            : floatingIpJson.get("size").toString());
                            floatingIp.setShareType(floatingIpJson.get("shareType").toString());
                            floatingIp.setInternetServiceProvider(floatingIpJson.get("type").toString());
                        }
                    } else if (Objects.equals(jsonObject.get(JSON_ATTR_KEY), "name")) {
                        hpcCluster.setName(StrUtil.toString(value));
                    } else if (Objects.equals(jsonObject.get(JSON_ATTR_KEY), "chargeType")) {
                        hpcCluster.setChargeType(StrUtil.toString(value));
                    } else if (Objects.equals(jsonObject.get(JSON_ATTR_KEY), "availabilityZone")) {
                        hpcCluster.setAvailabilityZone(StrUtil.toString(value));
                    } else if (Objects.equals(jsonObject.get(JSON_ATTR_KEY), "textarea")) {
                        hpcCluster.setDescription(StrUtil.toString(value));
                    } else if (Objects.equals(jsonObject.get(JSON_ATTR_KEY), "osArchitectureType")) {
                        hpcCluster.setOsArchitectureType(StrUtil.toString(value));
                    }
                }
            }
            hpcCluster.setAdminUser(insertHpcDrpResource.getAdminUser());
            hpcCluster.setAdminPassword(insertHpcDrpResource.getAdminPassword());
            hpcCluster.setCloudEnvId(cloudId);
            // HPC集群表
            ResHpcCluster resHpcCluster = insertHpcDrpResHpcCluster(hpcCluster, authUser, currentOrgInfo,insertHpcDrpResource);
            // HCSO环境ID
            Long hcsoCloudEnvId = getHCSOCloudEnv().getId();
            // ECS BMS VD
            insertNodeInfo(nodes, authUser, orgSid, hcsoCloudEnvId, hpcClusterResourceList, resHpcCluster);
            // SFS
            sfsIds.stream().forEach(sfs -> {
                setHpcClusterResource(resHpcCluster.getId(), ServiceManage.SFS, null,
                                      Long.parseLong(sfs.toString()), hpcClusterResourceList, authUser, orgSid, null);
            });

            // VPC
            vpcPeering.setOrgSid(orgSid);
            vpcPeering.setOwnerId(userSid.toString());
            vpcPeering.setCloudEnvId(hcsoCloudEnvId);
            BasicWebUtil.prepareInsertParams(vpcPeering, authUser);
            resVpcPeeringMapper.insert(vpcPeering);
            setHpcClusterResource(resHpcCluster.getId(), ServiceManage.VPC_PEER, null,
                                  vpcPeering.getId(), hpcClusterResourceList, authUser, orgSid, null);
            // EIP
            floatingIp.setOrgSid(orgSid);
            floatingIp.setOwnerId(userSid.toString());
            floatingIp.setChargeType(BillingConstants.ChargeType.PRE_PAID);
            floatingIp.setCloudEnvId(hcsoCloudEnvId.toString());
            BasicWebUtil.prepareInsertParams(floatingIp, authUser);
            resFloatingIpMapper.insert(floatingIp);
            setHpcClusterResource(resHpcCluster.getId(), ServiceManage.FLOATING_IP, null,
                                  floatingIp.getId(), hpcClusterResourceList, authUser, orgSid, null);
            // ClusterResource
            if (CollectionUtil.isNotEmpty(hpcClusterResourceList)) {
                resHpcClusterResourceMapper.insertBatch(hpcClusterResourceList);
            }
            return resHpcCluster;
        } finally {
            //预检请求 直接回滚
            if (insertHpcDrpResource.getTest()) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            }
        }
    }


    private ResHpcCluster insertHpcDrpResHpcCluster(ResHpcCluster resHpcCluster, User authUser, Org currentOrgInfo,
                                                    InsertHpcDrpResource insertHpcDrpResource) {
        HcsoUser hcsoUser = hcsoUserRemoteService.selectByRefUserId(authUser.getUserSid());

        resHpcCluster.setTenantId(hcsoUser.getId());
        if("hpc-drp-standard".equals(insertHpcDrpResource.getApplyType())){
            resHpcCluster.setClusterType(HPCClusterTypeEnum.PRE_SAAS_PRIVATE.getCode());
            resHpcCluster.setTaskDescription(currentOrgInfo.getOrgName() + "的标准专属资源池");
            resHpcCluster.setStatus("configing");
            resHpcCluster.setOsArchitectureType("鲲鹏");


        }else{
            SysConfig sysConfig = sysConfigRemoteService.getSysConfigByConfigKey(HPCPropertyKey.HPC_PREDEPLOY_SWITCH_KEY.getCode());
            resHpcCluster.setClusterType(
                    HPCPropertyKey.SWITCH_ON.getCode().equals(sysConfig.getConfigValue()) ?
                            HPCClusterTypeEnum.AD_SAAS_PRIVATE.getCode() : HPCClusterTypeEnum.SAAS_PRIVATE.getCode()
            );
            resHpcCluster.setTaskDescription(currentOrgInfo.getOrgName() + "的专属资源池");
            resHpcCluster.setStatus("apply");
        }


        resHpcCluster.setTaskId(new IdWorker().nextId() + "");
        resHpcCluster.setOwnerId(authUser.getUserSid());
        resHpcCluster.setOrgSid(currentOrgInfo.getOrgSid());
        resHpcCluster.setAdminUser(authUser.getAccount());
        resHpcCluster.setChargeType(BillingConstants.ChargeType.PRE_PAID);
        resHpcCluster.setTaskDescription(currentOrgInfo.getOrgName() + "的专属资源池");
        resHpcCluster.setHpcVersion(3);
        BasicWebUtil.prepareInsertParams(resHpcCluster, authUser);
        resHpcClusterMapper.insert(resHpcCluster);
        return resHpcCluster;
    }

    private void insertNodeInfo(List<VmNodeInfo> nodes, User authUser, Long orgSid, Long cloudId,
                                List<ResHpcClusterResource> hpcClusterResourceList, ResHpcCluster resHpcCluster) {
        Long clusterId = resHpcCluster.getId();
        List<ResVm> vmList = Lists.newArrayList();
        List<ResBms> bmsList = Lists.newArrayList();
        List<ResVd> vdList = Lists.newArrayList();
        List<String> vmTypeIds = Lists.newArrayList();
        Map<Integer, ResVd> vdMap = Maps.newHashMap();
        String userSid = authUser.getUserSid().toString();
        for (int j = 0; j < nodes.size(); j++) {
            VmNodeInfo node = nodes.get(j);
            Long num = node.getNodeNum();
            for (int i = 0; i < num; i++) {
                if (Objects.equals(node.getResourceType(), ServiceManage.ECS)) {
                    ResVm vm = new ResVm();
                    vm.setId(ID_WORKER.nextId() + "");
                    vm.setAssociatedKey(j);
                    vm.setInstanceType(node.getFlavorRef());
                    vm.setInstanceChargeType(BillingConstants.ChargeType.PRE_PAID);
                    vm.setOrgSid(orgSid);
                    vm.setOwnerId(userSid);
                    vm.setInstanceId(node.getInstanceId());
                    vm.setInnerIp(node.getInnerIp());
                    vm.setPublicIp(node.getPublicIp());
                    vm.setFloatingIp(node.getFloatingIp());
                    vm.setCloudEnvId(cloudId);
                    vm.setManagemenPassword(node.getPassword());
                    vm.setHpcNodeType(node.getHpcNodeType());
                    vm.setVmCategory(ServiceManage.ECS);
                    if (ServiceManage.CCS_VNC.equals(node.getHpcNodeType())) {
                        vm.setHpcNodeType(ServiceManage.VNC);
                    } else {
                        vm.setHpcNodeType(node.getHpcNodeType());
                    }
                    vm.setHpcPointType(node.getHpcPointType());
                    vm.setHostName(node.getHostName());
                    BasicWebUtil.prepareInsertParams(vm, authUser);
                    vmList.add(vm);
                } else if (Objects.equals(node.getResourceType(), ServiceManage.BMS)) {
                    ResBms bms = new ResBms();
                    bms.setId(ID_WORKER.nextId() + "");
                    bms.setAssociatedKey(j);
                    bms.setInstanceType(node.getFlavorRef());
                    bms.setManagemenPassword(node.getPassword());
                    bms.setInstanceChargeType(BillingConstants.ChargeType.PRE_PAID);
                    bms.setOrgSid(orgSid);
                    bms.setOwnerId(userSid);
                    bms.setCloudEnvId(cloudId);
                    bms.setManagemenPassword(node.getPassword());
                    bms.setVmCategory(ServiceManage.BMS);
                    bms.setHpcNodeType(node.getHpcNodeType());
                    bms.setInstanceId(node.getInstanceId());
                    bms.setInnerIp(node.getInnerIp());
                    bms.setPublicIp(node.getPublicIp());
                    bms.setFloatingIp(node.getFloatingIp());
                    bms.setHostName(node.getHostName());
                    bms.setHpcNodeType(node.getHpcNodeType());
                    BasicWebUtil.prepareInsertParams(bms, authUser);
                    bmsList.add(bms);
                }
            }
            ResVd vd = new ResVd();
            if(Objects.nonNull(node.getSize())){
                vd.setAllocateDiskSize(Long.parseLong(node.getSize()));
            }
            vd.setChargeType(BillingConstants.ChargeType.PRE_PAID);
            vd.setDiskMode(node.getVolumeType());
            vd.setOrgSid(orgSid);
            vd.setOwnerId(userSid);
            vd.setVirtType(node.getResourceType());
            BasicWebUtil.prepareInsertParams(vd, authUser);
            vdMap.put(j, vd);
            vmTypeIds.add(node.getFlavorRef());
        }
        setVmTypeInfo(vmTypeIds, vmList, bmsList);
        if (CollectionUtil.isNotEmpty(vmList)) {
            resVmMapper.insertBatch(vmList);
        }
        if (CollectionUtil.isNotEmpty(bmsList)) {
            resBmsMapper.insertBatch(bmsList);
        }

        setVdInfo(vdMap, vmList, bmsList, vdList, hpcClusterResourceList, authUser, orgSid, clusterId);

        if (CollectionUtil.isNotEmpty(vdList)) {
            resVdMapper.insertBatch(vdList);
        }
        vdList.stream().forEach(vd -> {
            setHpcClusterResource(clusterId, ServiceManage.EBS, null,
                                  Long.parseLong(vd.getResVdSid()), hpcClusterResourceList, authUser, orgSid, null);
        });
    }


    private void setVmTypeInfo(List<String> vmTypeIds, List<ResVm> vmList, List<ResBms> bmsList) {
        Criteria param = new Criteria();
        param.put("uuids", vmTypeIds);
        log.info("节点信息导入-setVmTypeInfo-uuids:[{}]",vmTypeIds);
        List<ResVmType> resVmTypes = resVmTypeMapper.selectByParams(param);
        Map<String, List<ResVmType>> vmTypeMap = resVmTypes.stream().collect(Collectors.groupingBy(ResVmType::getUuid));
        vmList.stream().forEach(vm -> {
            List<ResVmType> vmTypes = vmTypeMap.get(vm.getInstanceType());
            if (CollectionUtil.isNotEmpty(vmTypes)) {
                vm.setCpu(vmTypes.get(0).getCpu());
                vm.setMemory(round(vmTypes.get(0).getRam()/1024));
            }
        });
        bmsList.stream().forEach(vm -> {
            List<ResVmType> vmTypes = vmTypeMap.get(vm.getInstanceType());
            if (CollectionUtil.isNotEmpty(vmTypes)) {
                vm.setCpu(vmTypes.get(0).getCpu());
//                vm.setGpu(vmTypes.get(0).getGpu());
                vm.setMemory(round(vmTypes.get(0).getRam()/1024));
            }
        });
    }

    private void setVdInfo(Map<Integer, ResVd> vdMap, List<ResVm> vmList, List<ResBms> bmsList, List<ResVd> vdList,
                           List<ResHpcClusterResource> hpcClusterResourceList, User authUser, Long orgSid,
                           Long clusterId) {
        vmList.stream().forEach(vm -> {
            ResVd vd = vdMap.get(vm.getAssociatedKey());
            vd.setResVdSid(ID_WORKER.nextId() + "");
            if (Objects.nonNull(vd)) {
                vd.setResVmId(vm.getId());
                vdList.add(vd.cloneBean());
            }

            String nodeType = COMPUTE;
            if (Strings.isNotBlank(vm.getHpcNodeType())) {
                nodeType = vm.getHpcNodeType().contains(VNC) ? VNC_LOWER : MANAGER;
            }

            setHpcClusterResource(clusterId, ServiceManage.ECS, nodeType,
                                  Long.parseLong(vm.getId()), hpcClusterResourceList, authUser, orgSid,
                                  Strings.isNotBlank(vm.getHpcPointType()) ? vm.getHpcPointType(): vm.getHpcNodeType());
        });
        bmsList.stream().forEach(vm -> {
            ResVd vd = vdMap.get(vm.getAssociatedKey());
            vd.setResVdSid(ID_WORKER.nextId() + "");
            if (Objects.nonNull(vd)) {
                vd.setResVmId(vm.getId());
                vdList.add(vd.cloneBean());
            }
            if (Strings.isBlank(vm.getHpcNodeType())) {
                vm.setHpcNodeType(COMPUTE);
            }
            setHpcClusterResource(clusterId, ServiceManage.BMS,
                                  vm.getHpcNodeType(),
                                  Long.parseLong(vm.getId()), hpcClusterResourceList, authUser, orgSid,
                                  vm.getHpcNodeType());
        });
    }

    private void setHpcClusterResource(Long clusterId, String ResourceType, String nodeType, Long resourceId,
                                       List<ResHpcClusterResource> hpcClusterResourceList, User authUser, Long orgSid,
                                       String hpcPointType) {
        ResHpcClusterResource hpcClusterResource = new ResHpcClusterResource();
        hpcClusterResource.setClusterId(clusterId);
        hpcClusterResource.setResourceType(ResourceType);
        hpcClusterResource.setNodeType(nodeType);
        hpcClusterResource.setHpcPointType(hpcPointType);
        hpcClusterResource.setResourceId(resourceId);
        hpcClusterResource.setOwnerId(authUser.getUserSid().toString());
        hpcClusterResource.setOrgSid(orgSid);
        BasicWebUtil.prepareInsertParams(hpcClusterResource, authUser);
        hpcClusterResourceList.add(hpcClusterResource);
    }

    @Override
    public CreateHPCClusterResult createHPCCluster(CreateHpcDrpResource createHpcDrpResource) {
        log.info("--------------------- HPCServiceImpl.createHPCCluster 创建HPC资源池");
        //基础信息
        Org currentOrgInfo = BasicInfoUtil.getCurrentOrgInfo();
        User authUser = BasicInfoUtil.getAuthUser();
        ResHpcCluster resHpcCluster = resHpcClusterMapper.selectByPrimaryKey(createHpcDrpResource.getHpcClusterId());
        HPCClusterCreate hpcClusterCreate = CloudClientFactory.buildMQBean(resHpcCluster.getCloudEnvId(),
                                                                           HPCClusterCreate.class);

        hpcClusterCreate.setApplyTaskID(resHpcCluster.getTaskId());
        hpcClusterCreate.setApplyTaskDescription("HPC专属资源池预审批");
        hpcClusterCreate.setHpcClusterName(resHpcCluster.getName());
        hpcClusterCreate.setTenantName(resHpcCluster.getCreatedBy());
        hpcClusterCreate.setHpcClusterType(resHpcCluster.getClusterType());
        hpcClusterCreate.setDescription(resHpcCluster.getDescription());
        if (HPCClusterTypeEnum.SAAS_PRIVATE.getCode().equals(resHpcCluster.getClusterType())) {
            // 只有普通专属资源池才需要携带租户信息
            FDAuthInfo authInfo = getAuthInfo(BasicInfoUtil.getCurrentUserSid());
            hpcClusterCreate.setAuthInfo(authInfo);
        }else {
            // CCM只接受SAASPrivate
            hpcClusterCreate.setHpcClusterType(HPCClusterTypeEnum.SAAS_PRIVATE.getCode());
        }


        ResHpcClusterResourceExample hpcClusterResourceExample = new ResHpcClusterResourceExample();
        hpcClusterResourceExample.createCriteria().andClusterIdEqualTo(resHpcCluster.getId());
        //集群关联资源关系
        List<ResHpcClusterResource> resHpcClusterResources
                = resHpcClusterResourceMapper.selectByExample(hpcClusterResourceExample);
        // 集群对应资源信息
        List<Long> ecsIds = Lists.newArrayList();
        List<Long> bmsIds = Lists.newArrayList();
        List<Long> ebsIds = Lists.newArrayList();
        List<Long> sfsIds = Lists.newArrayList();
        resHpcClusterResources.stream().forEach(resHpcClusterResource -> {
            switch (resHpcClusterResource.getResourceType()) {
                case ServiceManage.ECS:
                    ecsIds.add(resHpcClusterResource.getResourceId());
                    break;
                case ServiceManage.BMS:
                    bmsIds.add(resHpcClusterResource.getResourceId());
                    break;
                case ServiceManage.EBS:
                    ebsIds.add(resHpcClusterResource.getResourceId());
                    break;
                case ServiceManage.SFS:
                    sfsIds.add(resHpcClusterResource.getResourceId());
                    break;
                default:
                    break;
            }
        });
        Criteria criteria = new Criteria();
        criteria.put("resVmIds", ecsIds);
        List<ResVm> resVmList = resVmMapper.selectByCriteriaWithNoFilter(criteria);
        Map<String, ResVm> resVmMap = resVmList.stream().collect(Collectors.toMap(ResVm::getId, rm -> rm));
        criteria = new Criteria();
        criteria.put("resBmsIds", bmsIds);
        List<ResBms> resBmsList = resBmsMapper.selectByParams(criteria);
        Map<String, ResBms> resBmsMap = resBmsList.stream().collect(Collectors.toMap(ResBms::getId, bms -> bms));
        criteria = new Criteria();
        criteria.put("resVdIds", ebsIds);
        List<ResVd> resVdList = resVdMapper.selectByCriteriaWithNoFilter(criteria);
        Map<String, List<ResVd>> resVdMap = resVdList.stream().collect(Collectors.groupingBy(x -> {
            return StrUtil.concat(true, x.getResVmId(), x.getVirtType());
        }));

        //资源信息
        FDResourceInfo fdResourceInfo = new FDResourceInfo();
        fdResourceInfo.setScenario(resHpcCluster.getScenario());
        //管理节点
        List<ManagementNodeInfo> managementNodeInfos = new ArrayList<>();
        //计算节点
        List<ComputeNodeInfo> computeNodeInfos = new ArrayList<>();
        //VNC节点
        List<VNCNodeInfo> vncNodeInfos = new ArrayList<>();
        // 文件系统ID
        List<Long> sfsIdList = new ArrayList<>();
        Map<String, ComputeNodeInfo> computerNodeMap = Maps.newHashMap();
        Map<String, VNCNodeInfo> vncNodeMap = Maps.newHashMap();
        Set<String> managementNodeSet = Sets.newHashSet();
        int agentNum = 0, cliNum = 0, vncNum = 0;
        for (ResHpcClusterResource item : resHpcClusterResources) {

            //管理节点或计算节点
            if (ServiceManage.ECS.equalsIgnoreCase(item.getResourceType()) || ServiceManage.BMS.equalsIgnoreCase(
                    item.getResourceType())) {
                String flavorRef;
                if (ServiceManage.ECS.equalsIgnoreCase(item.getResourceType())) {
                    ResVm vm = resVmMap.get(Convert.toStr(item.getResourceId()));
                    flavorRef = Objects.nonNull(vm) ? vm.getInstanceType() : StrUtil.EMPTY;
                } else {
                    ResBms bms = resBmsMap.get(Convert.toStr(item.getResourceId()));
                    flavorRef = Objects.nonNull(bms) ? bms.getInstanceType() : StrUtil.EMPTY;
                }
                ResVd vd = resVdMap.get(
                        StrUtil.concat(true, item.getResourceId() + "", item.getResourceType())).get(0);
                if (MANAGER.equals(item.getNodeType())) {
                    String key = item.getResourceType() + item.getHpcPointType() + flavorRef;
                    if (managementNodeSet.contains(key)) {
                        if (HPC_POINT_TYPE_CLI.equalsIgnoreCase(item.getHpcPointType())) {
                            cliNum++;
                        }
                    } else {
                        ManagementNodeInfo managementNodeInfo = new ManagementNodeInfo();
                        managementNodeInfo.setResourceType(item.getResourceType());
                        managementNodeInfo.setHPCNodeType(item.getHpcPointType());
                        managementNodeInfo.setFlavorRef(flavorRef);
                        FDRootVolume fdRootVolume = new FDRootVolume();
                        fdRootVolume.setVolumetype(vd.getDiskMode());
                        fdRootVolume.setSize(Integer.parseInt(vd.getAllocateDiskSize() + ""));
                        managementNodeInfo.setRootVolume(fdRootVolume);
                        managementNodeInfos.add(managementNodeInfo);
                        managementNodeSet.add(key);

                        if (HPC_POINT_TYPE_CLI.equalsIgnoreCase(item.getHpcPointType())) {
                            cliNum = 1;
                        }
                    }
                } else if (COMPUTE.equals(item.getNodeType())) {
                    String key = item.getResourceType() + flavorRef;
                    if (Objects.isNull(computerNodeMap.get(key))) {
                        ComputeNodeInfo computeNodeInfo = new ComputeNodeInfo();
                        computeNodeInfo.setResourceType(item.getResourceType());
                        computeNodeInfo.setFlavorRef(flavorRef);
                        FDRootVolume fdRootVolume = new FDRootVolume();
                        fdRootVolume.setVolumetype(vd.getDiskMode());
                        fdRootVolume.setSize(Integer.parseInt(vd.getAllocateDiskSize() + ""));
                        computeNodeInfo.setRootVolume(fdRootVolume);
                        computeNodeInfo.setAgentNum(1);
                        computerNodeMap.put(key, computeNodeInfo);
                    } else {
                        ComputeNodeInfo computeNodeInfo = computerNodeMap.get(key);
                        computeNodeInfo.setAgentNum(computeNodeInfo.getAgentNum() + 1);
                    }
                    agentNum += 1;
                } else if (VNC_LOWER.equals(item.getNodeType())) {
                    String key = item.getResourceType() + flavorRef;
                    if (Objects.isNull(vncNodeMap.get(key))) {
                        VNCNodeInfo vncNodeInfo = new VNCNodeInfo();
                        vncNodeInfo.setResourceType(item.getResourceType());
                        vncNodeInfo.setNum(1);
                        vncNodeInfo.setFlavorRef(flavorRef);
                        //vncNodeInfo.setPassword();
                        FDRootVolume fdRootVolume = new FDRootVolume();
                        fdRootVolume.setVolumetype(vd.getDiskMode());
                        fdRootVolume.setSize(Math.toIntExact(vd.getAllocateDiskSize()));
                        vncNodeInfo.setRootVolume(fdRootVolume);
                        vncNodeMap.put(key, vncNodeInfo);
                    } else {
                        VNCNodeInfo vncNodeInfo = vncNodeMap.get(key);
                        vncNodeInfo.setNum(vncNodeInfo.getNum() + 1);
                    }
                    vncNum += 1;
                }
            } else if (ServiceManage.VPC_PEER.equalsIgnoreCase(item.getResourceType())) {
                VPCPeeringInfo vpcPeeringInfo = new VPCPeeringInfo();
                ResVpcPeering resVpcPeering = resVpcPeeringMapper.selectByPrimaryKey(item.getResourceId());
                vpcPeeringInfo.setVPCID(resVpcPeering.getAcceptVpcId());
                vpcPeeringInfo.setCidr(resVpcPeering.getAcceptCidr());
                vpcPeeringInfo.setTenantID(resVpcPeering.getAcceptProjectId());
                fdResourceInfo.setVpcPeeringInfo(vpcPeeringInfo);
            } else if (ServiceManage.FLOATING_IP.equalsIgnoreCase(item.getResourceType())) {
                PublicIPInfo publicIPInfo = new PublicIPInfo();
                ResFloatingIp resFloatingIp = resFloatingIpMapper.selectFloatingIpByKey(item.getResourceId());
                FDPublicIP fdPublicIP = new FDPublicIP();
                fdPublicIP.setType(resFloatingIp.getInternetServiceProvider());
                publicIPInfo.setPublicIP(fdPublicIP);
                FDBandwidth bandwidth = new FDBandwidth();
                bandwidth.setShareType(resFloatingIp.getShareType());
                bandwidth.setSize(Integer.parseInt(resFloatingIp.getBandWidth()));
                publicIPInfo.setBandwidth(bandwidth);
                fdResourceInfo.setPublicIPInfo(publicIPInfo);
            } else if (ServiceManage.SFS.equalsIgnoreCase(item.getResourceType())) {
                sfsIdList.add(item.getResourceId());
            }
        }
        // 管理节点整理
        computerNodeMap.forEach((key, value) -> {
            computeNodeInfos.add(value);
        });
        // VNC节点整理
        vncNodeMap.forEach((key, value) -> {
            vncNodeInfos.add(value);
        });
        ResZone zone = resHpcCluster.getAvailabilityZone() == null ? null :
                resZoneMapper.selectByPrimaryKey(Long.parseLong(resHpcCluster.getAvailabilityZone()));
        fdResourceInfo.setAvailabilityZone(Objects.nonNull(zone) ? zone.getUuid() : null);
        fdResourceInfo.setAgentNum(agentNum);
        fdResourceInfo.setCLINum(cliNum);
        fdResourceInfo.setVNCNum(vncNum);
        fdResourceInfo.setManagementNodeInfo(managementNodeInfos);
        fdResourceInfo.setComputeNodeInfo(computeNodeInfos);
        fdResourceInfo.setVncNodeInfo(vncNodeInfos);

        //申请信息
        HPCApplyInfo hpcApplyInfo = new HPCApplyInfo();
        hpcApplyInfo.setResourceInfo(fdResourceInfo);
        HPCConfigInfo hpcConfigInfo = new HPCConfigInfo();
        String businessCategory = "DRP_" + resHpcCluster.getAdminUser() + "_" + resHpcCluster.getId();
        resHpcCluster.setBusinessCategory(businessCategory);
        hpcConfigInfo.setBusinessCategory(businessCategory);
        hpcConfigInfo.setLdapPasswd(createHpcDrpResource.getLdapPassword());
        hpcConfigInfo.setSystemAdminUser(resHpcCluster.getAdminUser());

        List<String> sysConfigKey = Lists.newArrayList();
        sysConfigKey.add("system_config");
        List<SysConfig> sysConfigList = sysConfigRemoteService.getSysConfigInfo(sysConfigKey);
        sysConfigList = sysConfigList.stream()
                                     .filter(sysConfig ->
                                                     Objects.equals(sysConfig.getConfigKey(), "rightcloud.console.url")
                                                             || Objects.equals(sysConfig.getConfigKey(),
                                                                               "rightcloud.mgt.url"))
                                     .collect(Collectors.toList());
        hpcConfigInfo.setThirdDomain(
                sysConfigList.stream().map(SysConfig::getConfigValue).collect(Collectors.joining(",")));
        hpcConfigInfo.setManagementPlatformErrorURL(createHpcDrpResource.getManagementPlatformErrorURL());
        hpcConfigInfo.setManagementPlatformLoginURL(createHpcDrpResource.getManagementPlatformLoginURL());
        hpcApplyInfo.setHpcConfigInfo(hpcConfigInfo);

        List<FDStorageInfo> storageInfos = Lists.newArrayList();
        List<ResShare> shareList = resShareMapper.selectByParams(new Criteria("resShareIds", sfsIdList));

        List<FDShareInfo> nfsShareInfo = Lists.newArrayList();
        Map<String, FDStorageInfo> storageInfoMap = Maps.newHashMap();
        FDStorageInfo nfsStorageInfo = new FDStorageInfo();
        for (ResShare share : shareList) {
            Optional<String> dpc = Optional.empty();
            Optional<String> nfs = Optional.empty();
            if (Objects.nonNull(share.getExportLocations())) {
                String[] strings = JSONUtil.parseArray(share.getExportLocations()).toArray(new String[0]);
                dpc = Arrays.stream(strings).filter(s -> s.startsWith("DPC")).findFirst();
                nfs = Arrays.stream(strings).filter(s -> s.startsWith("NFS")).findFirst();
            }
            if (StringUtils.equalsIgnoreCase("NFS", share.getShareProto())) {
                addFdShareInfo(nfsShareInfo, dpc, nfs, currentOrgInfo.getLdapOu());

                String shareTypeUuid = "";
                if (Objects.nonNull(share.getShareTypeId())) {
                    ResShareType shareType = resShareTypeMapper.selectByPrimaryKey(
                            Convert.toInt(share.getShareTypeId()));
                    shareTypeUuid = shareType.getUuid();
                }
                nfsStorageInfo.setMasterManagerIP(StrUtil.EMPTY);
                nfsStorageInfo.setSlaveManagerIP(StrUtil.EMPTY);
                nfsStorageInfo.setFloatManagerIP(StrUtil.EMPTY);
                nfsStorageInfo.setUser(authUser.getAccount());
                nfsStorageInfo.setGroup(currentOrgInfo.getLdapOu());
                nfsStorageInfo.setShareType(shareTypeUuid);
                nfsStorageInfo.setShareID(share.getUuid());
            } else {
                JSONObject metadata = JSONUtil.parseObj(share.getMetadata());
                String masterManagerIP = Convert.toStr(metadata.get("MasterManagerIP"));
                if (Objects.nonNull(storageInfoMap.get(masterManagerIP))) {
                    addFdShareInfo(storageInfoMap.get(masterManagerIP).getShareInfo(), dpc, nfs,
                                   currentOrgInfo.getLdapOu());
                } else {
                    String shareTypeUuid = "";
                    if (Objects.nonNull(share.getShareTypeId())) {
                        ResShareType shareType = resShareTypeMapper.selectByPrimaryKey(
                                Convert.toInt(share.getShareTypeId()));
                        shareTypeUuid = shareType.getUuid();
                    }
                    FDStorageInfo storageInfo = new FDStorageInfo();
                    storageInfo.setMasterManagerIP(masterManagerIP == null ? "" : masterManagerIP);
                    storageInfo.setSlaveManagerIP(metadata.get("SlaveManagerIP") == null ? ""
                                                          : Convert.toStr(metadata.get("SlaveManagerIP")));
                    storageInfo.setFloatManagerIP(metadata.get("FloatManagerIP") == null ? ""
                                                          : Convert.toStr(metadata.get("FloatManagerIP")));
                    storageInfo.setUser(authUser.getAccount());
                    storageInfo.setGroup(currentOrgInfo.getLdapOu());

                    storageInfo.setShareType(shareTypeUuid);
                    storageInfo.setShareID(share.getUuid());
                    List<FDShareInfo> shareInfo = Lists.newArrayList();
                    addFdShareInfo(shareInfo, dpc, nfs, currentOrgInfo.getLdapOu());
                    storageInfo.setShareInfo(shareInfo);

                    storageInfoMap.put(masterManagerIP, storageInfo);
                }
            }
        }
        // 追加共享文件系统
        ResShareType resShareType = resShareTypeMapper.selectByPrimaryKey(
                Integer.parseInt(resHpcCluster.getShareType()));
        ResShare fsShare = getCommonResShares(resShareType, 2);

        Optional<String> dpc = Optional.empty();
        Optional<String> nfs = Optional.empty();
        if (Objects.nonNull(fsShare.getExportLocations())) {
            String[] strings = JSONUtil.parseArray(fsShare.getExportLocations()).toArray(new String[0]);
            dpc = Arrays.stream(strings).filter(s -> s.startsWith("DPC")).findFirst();
            nfs = Arrays.stream(strings).filter(s -> s.startsWith("NFS")).findFirst();
        }
        FDShareInfo sfShareInfo = new FDShareInfo()
                .setDPCShareName(dpc.orElse("").replace("DPC:", ""))
                .setNFSShareName(nfs.orElse("").replace("NFS:", ""))
                .setMountPath("/home/<USER>")
                .setPathType("Common");

        if (CollectionUtil.isEmpty(storageInfoMap)) {
            nfsShareInfo.add(sfShareInfo);
            nfsStorageInfo.setShareInfo(nfsShareInfo);
            storageInfos.add(nfsStorageInfo);
        } else {
            storageInfoMap.forEach((key, value) -> {
                if (Objects.nonNull(value.getShareInfo())) {
                    nfsShareInfo.add(sfShareInfo);
                    value.getShareInfo().addAll(nfsShareInfo);
                }
                storageInfos.add(value);
            });
        }

        hpcApplyInfo.setStorageInfo(storageInfos);

        hpcClusterCreate.setHpcApplyInfo(hpcApplyInfo);
        HPCClusterCreateResult rpc = null;
        try {
            rpc = (HPCClusterCreateResult) MQHelper.rpc(hpcClusterCreate);
            resHpcCluster.setErrorInfo(StrUtil.EMPTY);
            resHpcCluster.setResourceId(StrUtil.EMPTY);
            if (Objects.nonNull(rpc) && rpc.isSuccess()) {
                // 更新集群信息
                resHpcCluster.setTaskId(rpc.getTaskID());
                resHpcCluster.setTaskDescription(rpc.getDescription());
                resHpcCluster.setStatus(ResHpcClusterStatus.CONFIGING);
                resHpcCluster.setProessPhase(CcmTaskStatusEnum.APPLY_RESOURCE.getValue());
                resHpcCluster.setProessStatus(CcmTaskStatusEnum.WAITING.getValue());
                resHpcClusterMapper.updateByPrimaryKey(resHpcCluster);
            } else {
                // 更新集群信息
                resHpcCluster.setErrorInfo("HPC专属资源池CCM创建失败，错误详情请登录CCM查看！集群名称：[" + resHpcCluster.getName() + "]");
                resHpcCluster.setTaskId(rpc.getTaskID());
                resHpcCluster.setTaskDescription(rpc.getDescription());
                resHpcCluster.setStatus(ResHpcClusterStatus.CONFIGING);
                resHpcCluster.setProessPhase(CcmTaskStatusEnum.APPLY_RESOURCE.getValue());
                resHpcCluster.setProessStatus(CcmTaskStatusEnum.FAILED.getValue());
                resHpcClusterMapper.updateByPrimaryKey(resHpcCluster);
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_328479904));
            }
        } catch (MQException e) {
            e.printStackTrace();
            return null;
        }
        return BeanConvertUtil.convert(rpc, CreateHPCClusterResult.class);
    }

    private void addFdShareInfo(List<FDShareInfo> shareInfo, Optional<String> dpc, Optional<String> nfs,
                                String ldapOu) {
        Org currentOrgInfo = BasicInfoUtil.getCurrentOrgInfo();
        String path = "/home/<USER>/" + ldapOu;
        log.info("HPC专属资源池添加文件系统-addFdShareInfo-目录获取参数：[{}]", JSONUtil.toJsonStr(currentOrgInfo));
        if (!bssUserRemoteService.checkUserVersion(Convert.toStr(currentOrgInfo.getOrgSid()))) {
            path = "/home/" + ldapOu;
        }
        log.info("HPC专属资源池添加文件系统-addFdShareInfo-目录：[{}]", path);
        shareInfo.add(new FDShareInfo()
                              .setDPCShareName(dpc.orElse("").replace("DPC:", ""))
                              .setNFSShareName(nfs.orElse("").replace("NFS:", ""))
                              .setMountPath(path)
                              .setPathType("Share"));
    }

    private FDAuthInfo getAuthInfo(Long userSid) {
        HcsoUser hcsoUser = hcsoUserRemoteService.selectByRefUserId(userSid);
        if (hcsoUser == null) {
            return null;
        }
        FDAuthInfo fdAuthInfo = new FDAuthInfo();
        fdAuthInfo.setAuthAK(CrytoUtilSimple.decrypt(hcsoUser.getAk()));
        fdAuthInfo.setAuthSK(CrytoUtilSimple.decrypt(hcsoUser.getSk()));
        fdAuthInfo.setAuthEndpoint(getHCSOEndPoint());
        fdAuthInfo.setAuthAccountID(hcsoUser.getAccountId());
        fdAuthInfo.setAuthUserName(hcsoUser.getAccountName());
        fdAuthInfo.setProjectID(hcsoUser.getProjectId());
        return fdAuthInfo;
    }

    @Override
    public VpcPeeringResult vpcPeeringAccept(VpcPeeringResource vpcPeeringResource) {
        VpcPeeringResult result = null;
        try {
            ResHpcCluster resHpcCluster = resHpcClusterMapper.selectByPrimaryKey(
                    Convert.toLong(vpcPeeringResource.getClusterId()));
            ResVpcPeering resVpcPeering = resVpcPeeringMapper.selectByClusterKey(vpcPeeringResource.getClusterId());

            VpcPeeringAccept vpcPeeringAccept = CloudClientFactory.buildMQBean(getHCSOCloudEnv().getId(),
                                                                               VpcPeeringAccept.class);
            vpcPeeringAccept.setPeeringId(vpcPeeringResource.getVpcPeeringId());
            if (Strings.isNotBlank(vpcPeeringResource.getApiKey()) && Strings.isNotBlank(
                    vpcPeeringResource.getSecureToken())) {
                vpcPeeringAccept.setApiKey(vpcPeeringResource.getApiKey());
                vpcPeeringAccept.setSecureToken(vpcPeeringResource.getSecureToken());
                vpcPeeringAccept.setTenantId(vpcPeeringResource.getTenantId());
                vpcPeeringAccept.setDomain(vpcPeeringResource.getDomain());
            }
            VpcPeeringAcceptResult rpc = (VpcPeeringAcceptResult) MQHelper.rpc(vpcPeeringAccept);

            if (Objects.nonNull(rpc) && rpc.isSuccess()) {
                log.info("返回状态成功！");
                result = BeanConvertUtil.convert(rpc, VpcPeeringResult.class);
                // 更新对等连接信息
                resVpcPeering.setRequestVpcId(result.getRequestVpcInfo().getVpcId());
                resVpcPeering.setRequestProjectId(result.getRequestVpcInfo().getTenantId());
                resVpcPeering.setAcceptVpcId(result.getAcceptVpcInfo().getVpcId());
                resVpcPeering.setAcceptProjectId(result.getAcceptVpcInfo().getTenantId());
                resVpcPeering.setUuid(result.getId());
                resVpcPeeringMapper.updateByPrimaryKey(resVpcPeering);
            } else {
            }
        } catch (MQException e) {
            log.error(e.getMessage());
        }
        return result;
    }

    @Override
    public UpdateRouteTableResult updateRouteTable(UpdateRouteTableVO updateRouteTableVO) {
        RouteTableUpdate routeTableUpdate = CloudClientFactory.buildMQBean(getHCSOCloudEnv().getId(),
                                                                           RouteTableUpdate.class);

        ResHpcCluster resHpcCluster = resHpcClusterMapper.selectByPrimaryKey(
                Convert.toLong(updateRouteTableVO.getClusterId()));
        ResVpcPeering resVpcPeering = resVpcPeeringMapper.selectByClusterKey(resHpcCluster.getId());
        ResVpc resVpc = resVpcMapper.selectByClusterKey(resHpcCluster.getId());
        if (Strings.isNotBlank(updateRouteTableVO.getApiKey()) && Strings.isNotBlank(
                updateRouteTableVO.getSecureToken())) {
            routeTableUpdate.setApiKey(updateRouteTableVO.getApiKey());
            routeTableUpdate.setSecureToken(updateRouteTableVO.getSecureToken());
            routeTableUpdate.setTenantId(updateRouteTableVO.getTenantId());
            routeTableUpdate.setDomain(updateRouteTableVO.getDomain());
        }

        routeTableUpdate.setRoutetableId(resVpcPeering.getAcceptRouterId());
        Routetable routetable = new Routetable();
        routetable.setName(updateRouteTableVO.getRouteTableName());
        routetable.setDescription(updateRouteTableVO.getDescription());
        Map<String, List<RouteTableRoute>> routes = new HashMap<>(10);
        List<RouteTableRoute> routeList = new ArrayList<>();
        RouteTableRoute routeTableRoute = new RouteTableRoute();
        routeTableRoute.setDestination(resVpc.getCidr());
        routeTableRoute.setType("peering");
        routeTableRoute.setNexthop(resVpcPeering.getUuid());
        routeList.add(routeTableRoute);
        routes.put(updateRouteTableVO.getType(), routeList);
        routetable.setRoutes(routes);
        routeTableUpdate.setRoutetable(routetable);
        UpdateRouteTableResult result = null;
        try {
            RouteTableUpdateResult rpc = (RouteTableUpdateResult) MQHelper.rpc(routeTableUpdate);

            if (Objects.nonNull(rpc) && rpc.isSuccess()) {
                log.info("HPC专属资源池更新路由-updateRouteTable-返回状态成功！");
                result = BeanConvertUtil.convert(rpc, UpdateRouteTableResult.class);
            } else {
            }
        } catch (MQException e) {
            log.error(e.getMessage());
        }

        return result;
    }

    @Override
    public ActiveHPCClusterResult activeHPCCluster(HPCActiveClusterRequest request) {
        ResHpcCluster resHpcCluster = resHpcClusterMapper.selectByPrimaryKey(Convert.toLong(request.getHpcClusterID()));
        FDHPCClusterActive fdhpcClusterActive = CloudClientFactory.buildMQBean(resHpcCluster.getCloudEnvId(),
                                                                               FDHPCClusterActive.class);

        if (StrUtil.isBlank(resHpcCluster.getResourceId())) {
            log.error("HPC专属资源池激活-activeHPCCluster-HpcClusterID为空，请检查");
            throw new BizException("HPC专属资源池激活失败！【底层资源池UUID为空】");
        }
        fdhpcClusterActive.setHpcClusterID(resHpcCluster.getResourceId());
        fdhpcClusterActive.setApplyTaskDescription("HPC专属资源池审批");
        fdhpcClusterActive.setApplyTaskID(new IdWorker().nextId() + "");

        //租户信息
        if (HPCClusterTypeEnum.SAAS_PRIVATE.getCode().equals(resHpcCluster.getClusterType())) {
            // 只有普通自定义专属才需要设置权限信息
            FDAuthInfo authInfo = getAuthInfo(resHpcCluster.getOwnerId());
            fdhpcClusterActive.setAuthInfo(authInfo);
        }
        ActiveHPCClusterResult result = null;
        try {
            FDHPCClusterActiveResult rpc = (FDHPCClusterActiveResult) MQHelper.rpc(fdhpcClusterActive);
            resHpcCluster.setErrorInfo(StrUtil.EMPTY);
            // 更新集群信息
            if (rpc.isSuccess()) {
                String taskId = rpc.getTaskID();
                taskId = StringUtils.isBlank(taskId) ? rpc.getTaskId() : taskId;
                resHpcCluster.setTaskId(taskId);
                resHpcCluster.setTaskDescription(rpc.getDescription());
                resHpcCluster.setProessPhase("06");
                resHpcCluster.setProessStatus("04");
                int count = resHpcClusterMapper.updateByPrimaryKeySelective(resHpcCluster);
                log.info("HPC专属资源池激活-activeHPCCluster-更新集群个数[{}]", count);
            } else {
                resHpcCluster.setProessPhase("06");
                resHpcCluster.setProessStatus("02");
                resHpcCluster.setErrorInfo("HPC专属资源池CCM激活失败，错误详情请登录CCM查看！集群名称：["
                                                   + resHpcCluster.getName() + "]");
                int count = resHpcClusterMapper.updateByPrimaryKeySelective(resHpcCluster);
                log.info("HPC专属资源池激活-activeHPCCluster-更新集群个数[{}]", count);
                throw new BizException("HPC专属资源池激活失败！");
            }
            result = BeanConvertUtil.convert(rpc, ActiveHPCClusterResult.class);
        } catch (Exception e) {
            log.error("HPC专属资源池激活-activeHPCCluster-失败：[{}]", e.getMessage());
            throw new BizException("HPC专属资源池激活失败！");
        }

        return result;
    }


    @Override
    public ResHpcCluster updateHpcDrpResource(UpdateHpcDrpResource updateHpcDrpResource) {
        return new ResHpcCluster();
    }

    @Override
    public HPCClusterInfoIDResult getHPCClusterInfoById(Long clusterId) {
        ResHpcCluster resHpcCluster = resHpcClusterMapper.selectByPrimaryKey(clusterId);
        HPCClusterInfoID hpcClusterInfoID = CloudClientFactory.buildMQBean(resHpcCluster.getCloudEnvId(),
                HPCClusterInfoID.class);
        if (StrUtil.isBlank(resHpcCluster.getTaskId())) {
            log.info("HPC集群查询：TaskID为空，请检查");
            return null;
        }
        hpcClusterInfoID.setHpcClusterID(resHpcCluster.getResourceId());

        HPCClusterInfoIDResult rpc = null;
        try {
            rpc = (HPCClusterInfoIDResult) MQHelper.rpc(hpcClusterInfoID);
        } catch (MQException e) {
            e.printStackTrace();
        }

        return rpc;
    }

    @Override
    @Transactional
    public String updateHPCClusterCreateTask(ResHpcClusterRemoteModule resHpcClusterRemoteModule,
                                             FDTaskInfoResult result) {
        log.info("HPCServiceImpl.updateHPCClusterCreateTask resHpcClusterRemoteModule:{}, result: {}",
                JSON.toJSONString(resHpcClusterRemoteModule), JSON.toJSONString(result));
        //List<StepInfo> stepInfos = result.getStepInfo();
        //stepInfos.sort(Comparator.comparing(StepInfo::getTime, Comparator.nullsFirst(Comparator.reverseOrder())));
        //String messageID = stepInfos.stream().findFirst().orElseGet(StepInfo::new).getMessageID();
        ResHpcCluster resHpcCluster = cn.com.cloudstar.rightcloud.module.support.access.util.BeanConvertUtil.convert(
                resHpcClusterRemoteModule, ResHpcCluster.class);
        resHpcCluster.setErrorInfo(StrUtil.EMPTY);
        String status = result.getStatus();
        //更新HpcCluster
        String port = cn.com.cloudstar.rightcloud.common.util.PropertiesUtil.getProperty("login.node.port");
        if (Objects.isNull(port)){
            port = PORT;
        }
        if (CcmTaskStatusEnum.FINISH.getCode().equalsIgnoreCase(status)
                || CcmTaskStatusEnum.LDAP_SYNC.getCode().equalsIgnoreCase(status)) {
            // 查找HPC资源池详情
            HPCClusterInfoIDResult hpcClusterInfoIDResult = getHPCClusterInfoById(resHpcCluster.getId());
            if(Objects.isNull(hpcClusterInfoIDResult)){
                return status;
            }

            HPCClusterInfoVO hpcClusterInfo = hpcClusterInfoIDResult.getHPCClusterInfo();
            if (Objects.isNull(hpcClusterInfo)) {
                log.error("updateHPCClusterCreateTask hpcClusterInfo对象等于null，远程调用rpc返回异常");
                throw new BizException("更新指定HPC资源池的节点信息失败");
            }
            //更新HpcCluster
            resHpcCluster.setResourceId(hpcClusterInfo.getHPCClusterID());
            // 清空ip数据
            resHpcCluster.setCcpExternalAddress(null);
            resHpcCluster.setCcpInternelAddress(null);
            resHpcCluster.setLoginNodeInternalAddress(null);
            resHpcCluster.setLoginNodeExternalAddress(null);

            //获取集群关联资源
            ResHpcClusterResourceExample hpcClusterResourceExample = new ResHpcClusterResourceExample();
            hpcClusterResourceExample.createCriteria().andClusterIdEqualTo(resHpcClusterRemoteModule.getId());
            List<ResHpcClusterResource> resHpcClusterResources = resHpcClusterResourceMapper.selectByExample(
                    hpcClusterResourceExample);

            //更新管理节点
            List<HPCManagementNodeInfoVO> managementNodeInfos = hpcClusterInfo.getManagementNodeInfo();
            //更新计算节点
            List<HPCComputeNodeInfoVO> computeNodeInfo = hpcClusterInfo.getComputeNodeInfo();
            List<HPCComputeNodeInfoVO> computeNodeInfoTmp = Lists.newArrayList();
            computeNodeInfoTmp.addAll(computeNodeInfo);

            // 计算节点保存Excel
            if (CollectionUtil.isNotEmpty(computeNodeInfoTmp)) {
                log.info("HPCServiceImpl.updateHPCClusterCreateTask 保存计算节点信息到excel");
                resHpcCluster.setClusterFile(saveHpcDrpExcel(
                        resHpcCluster.getId(), BeanConvertUtil.convert(computeNodeInfoTmp, HPCComputeExcel.class)));
            }

            //更新VNC节点
            List<HPCVNCNodeInfoVO> vncNodeInfo = hpcClusterInfo.getVncNodeInfo();

            for (ResHpcClusterResource hpcClusterResource : resHpcClusterResources) {
                if (StringUtils.equalsIgnoreCase(MANAGER,hpcClusterResource.getNodeType())) {
                    // 管理节点
                    log.info("HPC集群信息更新-updateHPCClusterCreateTask-HpcPointType：[{}]", hpcClusterResource.getHpcPointType());
                    if (CollectionUtil.isNotEmpty(managementNodeInfos)) {
                        for (HPCManagementNodeInfoVO item : managementNodeInfos) {
                            if (Objects.equals(hpcClusterResource.getHpcPointType(), item.getHPCNodeType())) {
                                log.debug("HPC集群信息更新-updateHPCClusterCreateTask-HPCManagementNode：[{}]", JSONUtil.toJsonStr(item));
                                if (Objects.equals(hpcClusterResource.getResourceType(), ServiceManage.ECS)) {
                                    ResVm resVm = resVmMapper.selectByPrimaryKey(
                                            String.valueOf(hpcClusterResource.getResourceId()));
                                    log.debug("HPC集群信息更新-updateHPCClusterCreateTask-ResVm：[{}]", JSONUtil.toJsonStr(resVm));
                                    if (resVm != null) {
                                        resVm.setInstanceId(item.getResourceID());
                                        resVm.setPublicIp(item.getEIP());
                                        resVm.setInnerIp(item.getVIP());
                                        resVm.setFloatingIp(item.getFIP());
                                        resVm.setHostName(item.getHostName());
                                        log.debug("HPC集群信息更新-updateHPCClusterCreateTask-管理节点 ECS更新：[{}]", JSONUtil.toJsonStr(resVm));
                                        resVmMapper.updateByPrimaryKey(resVm);
                                    }
                                } else if (Objects.equals(hpcClusterResource.getResourceType(), ServiceManage.BMS)) {
                                    ResBms resBms = resBmsMapper.selectByPrimaryKey(
                                            String.valueOf(hpcClusterResource.getResourceId()));
                                    if (resBms != null) {
                                        resBms.setInstanceId(item.getResourceID());
                                        resBms.setPublicIp(item.getEIP());
                                        resBms.setInnerIp(item.getVIP());
                                        resBms.setFloatingIp(item.getFIP());
                                        resBms.setHostName(item.getHostName());
                                        log.debug("HPC集群信息更新-updateHPCClusterCreateTask-管理节点 BMS更新：[{}]", JSONUtil.toJsonStr(resBms));
                                        resBmsMapper.updateByPrimaryKey(resBms);
                                    }
                                }
                                if (Objects.equals(hpcClusterResource.getHpcPointType(), HPC_POINT_TYPE_CLI)) {
                                    log.info("HPC集群信息更新-updateHPCClusterCreateTask-CLI处理");
                                    resHpcCluster.setLoginNodeExternalAddress(
                                            resHpcCluster.getLoginNodeExternalAddress() == null ?
                                                    item.getEIP() + StrUtil.COLON + port
                                                    : resHpcCluster.getLoginNodeExternalAddress() + "," + item.getEIP()
                                                            + StrUtil.COLON + port);
                                    resHpcCluster.setLoginNodeInternalAddress(
                                            resHpcCluster.getLoginNodeInternalAddress() == null ?
                                                    item.getVIP() + StrUtil.COLON + port
                                                    : resHpcCluster.getLoginNodeInternalAddress() + "," + item.getVIP()
                                                            + StrUtil.COLON + port);
//                                    resHpcCluster.setLoginNodeExternalAddress(item.getEIP() + ":22");
//                                    resHpcCluster.setLoginNodeInternalAddress(item.getVIP() + ":22");
                                } else if (Objects.equals(hpcClusterResource.getHpcPointType(), HPC_POINT_TYPE_CCP)) {
                                    log.info("HPC集群信息更新-updateHPCClusterCreateTask-CCP处理");
                                    resHpcCluster.setCcpExternalAddress(resHpcCluster.getCcpExternalAddress() == null ?
                                                                                "https://" + item.getEIP() + ":18080"
                                                                                : resHpcCluster.getCcpExternalAddress()
                                                                                        + "," + "https://"
                                                                                        + item.getEIP() + ":18080");
                                    resHpcCluster.setCcpInternelAddress(resHpcCluster.getCcpInternelAddress() == null ?
                                                                                "https://" + item.getFIP() + ":18080"
                                                                                : resHpcCluster.getCcpInternelAddress()
                                                                                        + "," + "https://"
                                                                                        + item.getFIP() + ":18080");
//                                    resHpcCluster.setCcpExternalAddress("https://" + item.getEIP() + ":18080");
//                                    resHpcCluster.setCcpInternelAddress("https://" + item.getFIP() + ":18080");
                                }
                                log.info("HPC集群信息更新-updateHPCClusterCreateTask-update");
                                hpcClusterResource.setExtraInfo(item.getExtraInfo());
                                hpcClusterResource.setNodeStatus(item.getNodeStatus());
                                resHpcClusterResourceMapper.updateByPrimaryKey(hpcClusterResource);
                                log.info("HPC集群信息更新-updateHPCClusterCreateTask-updated");
                                managementNodeInfos.remove(item);
                                break;
                            }
                        }
                    }

                }
                else if (StringUtils.equalsIgnoreCase(VNC_LOWER,hpcClusterResource.getNodeType())) {
                    // VNC节点
                    if (CollectionUtil.isNotEmpty(vncNodeInfo)) {
                        for (HPCVNCNodeInfoVO item : vncNodeInfo) {
                            if (Objects.equals(hpcClusterResource.getHpcPointType(), item.getHPCNodeType())) {
                                log.info("HPCVNCNode：[{}]", JSONUtil.toJsonStr(item));
                                if (Objects.equals(hpcClusterResource.getResourceType(), ServiceManage.ECS)) {
                                    ResVm resVm = resVmMapper.selectByPrimaryKey(
                                            String.valueOf(hpcClusterResource.getResourceId()));
                                    log.info("ResVm：[{}]", JSONUtil.toJsonStr(resVm));
                                    if (resVm != null) {
                                        resVm.setInstanceId(item.getResourceID());
                                        resVm.setPublicIp(item.getEIP());
                                        resVm.setInnerIp(item.getVip());
                                        resVm.setFloatingIp(item.getFIP());
                                        resVm.setHostName(item.getHostName());
                                        //log.info("VNC节点 ECS更新：[{}]", JSONUtil.toJsonStr(resVm));
                                        resVmMapper.updateByPrimaryKey(resVm);
                                    }
                                }
                                log.info("HPC集群信息更新-updateHPCClusterCreateTask-update");
                                hpcClusterResource.setExtraInfo(item.getExtraInfo());
                                hpcClusterResource.setNodeStatus(item.getNodeStatus());
                                resHpcClusterResourceMapper.updateByPrimaryKey(hpcClusterResource);
                                log.info("HPC集群信息更新-updateHPCClusterCreateTask-updated");
                                vncNodeInfo.remove(item);
                                break;
                            }
                        }
                    }
                }
                else if (StringUtils.equalsIgnoreCase(COMPUTE,hpcClusterResource.getNodeType())){
                    // 计算节点
                    if (CollectionUtil.isNotEmpty(computeNodeInfo)) {
                        for (HPCComputeNodeInfoVO item : computeNodeInfo) {
                            String resourceId = item.getResourceID();
                            if (Objects.equals(hpcClusterResource.getResourceType(), ServiceManage.ECS)) {
                                ResVm resVm = resVmMapper.selectByPrimaryKey(
                                        String.valueOf(hpcClusterResource.getResourceId()));
                                if (resVm != null && (StringUtils.isBlank(resVm.getInstanceId()) || resourceId.equals(resVm.getInstanceId()))) {
                                    resVm.setInstanceId(item.getResourceID());
                                    resVm.setInnerIp(item.getVIP());
                                    resVm.setHostName(item.getHostName());
                                    log.debug("HPC集群信息更新-updateHPCClusterCreateTask-计算节点 ECS更新：[{}]", JSONUtil.toJsonStr(resVm));
                                    resVmMapper.updateByPrimaryKey(resVm);

                                    hpcClusterResource.setStorageControlSubnet(item.getStorageControlSubNet());
                                    hpcClusterResource.setStorageSubnet(item.getStorageSubNet());
                                    hpcClusterResource.setStorageInterManagerIp(item.getStorageInterManagerIP());
                                    hpcClusterResource.setExtraInfo(item.getExtraInfo());
                                    hpcClusterResource.setNodeStatus(item.getNodeStatus());
                                    resHpcClusterResourceMapper.updateByPrimaryKey(hpcClusterResource);
                                    computeNodeInfo.remove(item);
                                    break;
                                }
                            } else if (Objects.equals(hpcClusterResource.getResourceType(), ServiceManage.BMS)) {
                                ResBms resBms = resBmsMapper.selectByPrimaryKey(
                                        String.valueOf(hpcClusterResource.getResourceId()));
                                if (resBms != null && (StringUtils.isBlank(resBms.getInstanceId()) || resourceId.equals(resBms.getInstanceId()))) {
                                    resBms.setInstanceId(resourceId);
                                    resBms.setInnerIp(item.getVIP());
                                    resBms.setHostName(item.getHostName());
                                    log.debug("HPC集群信息更新-updateHPCClusterCreateTask-计算节点 BMS更新：[{}]", JSONUtil.toJsonStr(resBms));
                                    resBmsMapper.updateByPrimaryKey(resBms);

                                    hpcClusterResource.setStorageControlSubnet(item.getStorageControlSubNet());
                                    hpcClusterResource.setStorageSubnet(item.getStorageSubNet());
                                    hpcClusterResource.setStorageInterManagerIp(item.getStorageInterManagerIP());
                                    hpcClusterResource.setExtraInfo(item.getExtraInfo());
                                    hpcClusterResource.setNodeStatus(item.getNodeStatus());
                                    resHpcClusterResourceMapper.updateByPrimaryKey(hpcClusterResource);
                                    computeNodeInfo.remove(item);
                                    break;
                                }
                            }
                        }
                    }
                }
            }

            //VPC
            ResVpc vpc = resVpcMapper.selectByClusterKey(resHpcCluster.getId());
            if (Objects.nonNull(vpc)) {
                vpc.setCidr(hpcClusterInfo.getCidr());
                vpc.setUuid(hpcClusterInfo.getVPCID());
                resVpcMapper.updateByPrimaryKey(vpc);
            } else {
                CloudEnv hcsoCloudEnv = getHCSOCloudEnv();
                vpc = new ResVpc();
                vpc.setCidr(hpcClusterInfo.getCidr());
                vpc.setUuid(hpcClusterInfo.getVPCID());
                vpc.setOrgSid(resHpcCluster.getOrgSid());
                vpc.setOwnerId(Convert.toStr(resHpcCluster.getOwnerId()));
                vpc.setCloudEnvId(hcsoCloudEnv.getId());
                vpc.setName(hpcClusterInfo.getVPCName());
                BasicWebUtil.prepareInsertParams(vpc);
                log.info("HPC集群信息更新-updateHPCClusterCreateTask-专属集群 VPC：[{}]", JSONUtil.toJsonStr(vpc));
                resVpcMapper.insert(vpc);
                Network network = new Network();
                network.setCloudEnvId(hcsoCloudEnv.getId());
                network.setNetworkName(resHpcCluster.getAdminUser() + "-子网");
                network.setStatus("ACTIVE");
                network.setNetVpcId(Convert.toStr(vpc.getId()));
                network.setCidr(hpcClusterInfo.getCidr());
                BasicWebUtil.prepareInsertParams(network);
                log.info("HPC集群信息更新-updateHPCClusterCreateTask-专属集群 Network：[{}]", JSONUtil.toJsonStr(network));
                networkMapper.insertSelective(network);
                ResHpcClusterResource hpcClusterResource = new ResHpcClusterResource();
                hpcClusterResource.setClusterId(resHpcCluster.getId());
                hpcClusterResource.setResourceType(ServiceManage.VPC);
                hpcClusterResource.setResourceId(vpc.getId());
                hpcClusterResource.setOwnerId(StrUtil.toString(resHpcCluster.getOwnerId()));
                hpcClusterResource.setOrgSid(resHpcCluster.getOrgSid());
                BasicWebUtil.prepareInsertParams(hpcClusterResource);
                resHpcClusterResourceMapper.insert(hpcClusterResource);
            }
            if (!CcmTaskStatusEnum.LDAP_SYNC.getCode().equalsIgnoreCase(status)) {
                resHpcCluster.setProessPhase(CcmTaskStatusEnum.ADD_SHARE_RULE.getValue());
                resHpcCluster.setProessStatus(CcmTaskStatusEnum.WAITING.getValue());
            }
            resHpcCluster.setErrorInfo(StringUtils.EMPTY);;
            log.debug("HPC集群信息更新-updateHPCClusterCreateTask-专属集群 更新：[{}]", JSONUtil.toJsonStr(resHpcCluster));
            int count = resHpcClusterMapper.updateByPrimaryKeySelective(resHpcCluster);
            log.info("HPC集群信息更新-updateHPCClusterCreateTask-专属集群 更新条数：[{}]", count);
            this.updateShareMountPath(resHpcCluster.getId());
        } else if (CcmTaskStatusEnum.FAILED.getCode().equalsIgnoreCase(status)) {
            //更新状态
            resHpcCluster.setResourceId(StrUtil.EMPTY);
            updHpcDrpStatus(resHpcCluster, CcmTaskStatusEnum.CONFIG_RESOURCE.getValue(),
                            CcmTaskStatusEnum.FAILED.getValue(), ResHpcClusterStatus.APPLY,
                            "HPC专属资源池CCM创建任务失败，错误详情请登录CCM查看！集群名称：["
                                    + resHpcCluster.getName() + "]");
            // 回退订单
            String key = resHpcClusterRemoteModule.getProessPhase() + resHpcClusterRemoteModule.getProessStatus();
            serviceOrderRemoteService.undoServiceOrder(resHpcCluster.getId(),key);
        }
        return status;
    }

    @Override
    public void updateShareMountPath(Long clusterId){
        ResHpcCluster resHpcCluster = resHpcClusterMapper.selectByPrimaryKey(clusterId);
        log.info("更新弹性文件路径-updateShareMountPath-高级自定义专属同步弹性文件挂载地址:{}",resHpcCluster.getId());
        //高级自定义专属同步弹性文件挂载地址
        if (HPCClusterTypeEnum.AD_SAAS_PRIVATE.getCode().equals(resHpcCluster.getClusterType())){
            ResHpcClusterResourceExample hpcClusterResourceExample = new ResHpcClusterResourceExample();
            hpcClusterResourceExample.createCriteria().andClusterIdEqualTo(resHpcCluster.getId());
            List<ResHpcClusterResource> resHpcClusterResources = resHpcClusterResourceMapper.selectByExample(hpcClusterResourceExample);
            if (CollectionUtils.isNotEmpty(resHpcClusterResources)){
                resHpcClusterResources.forEach(resHpcClusterResource -> {
                    if (!"SFS".equalsIgnoreCase(resHpcClusterResource.getResourceType())) {
                        return;
                    }
                    ResShare share = shareService.getById(resHpcClusterResource.getResourceId());
                    if( share == null){
                        log.info("更新弹性文件路径-updateShareMountPath-更新弹性文件：null");
                        return;
                    }
                    log.info("更新弹性文件路径-updateShareMountPath-更新弹性文件：{}", share.getId());
                    Criteria criteria = new Criteria();
                    criteria.put("cloudEnvType", CloudEnvType.FUSION_DIRECTOR.getValue().get(0));
                    List<CloudEnv> cloudEnvs = cloudEnvService.selectByParams(criteria);
                    SharePredeployQuery sharePredeployQuery = CloudClientFactory.buildMQBean(cloudEnvs.get(0).getId(), SharePredeployQuery.class);
                    sharePredeployQuery.setFileSystemID(share.getUuid());
                    try {
                        SharePredeployQueryResult sharePredeployQueryResult = (SharePredeployQueryResult)MQHelper.rpc(sharePredeployQuery);
                        log.info("更新弹性文件路径-updateShareMountPath-高级自定义专属开通，ccm弹性文件查询结果：[{}]", JSON.toJSONString(sharePredeployQueryResult));
                        if (Objects.nonNull(sharePredeployQueryResult) && Objects.nonNull(sharePredeployQueryResult.getMountPath())) {
                            log.info("更新弹性文件路径-updateShareMountPath-更新文件系统挂载路径：{}", sharePredeployQueryResult.getMountPath());
                            ResShare updateShare=new ResShare();
                            updateShare.setLinks(sharePredeployQueryResult.getMountPath());
                            updateShare.setId(share.getId());
                            shareService.updateByPrimaryKey(updateShare);
                        }
                    } catch (MQException e) {
                        e.printStackTrace();
                    }
                });
            }
        }
    }

    @Override
    public List<ResHpcClusterPool> getHpcClusterPoolByClusterId(String poolUuid) {
        Criteria criteria = new Criteria();
        criteria.put("clusterId", poolUuid);
        return resHpcClusterPoolMapper.selectByParam(criteria);
    }


    @Override
    public List<String> getPreClusterFileSystemIdList() {
        ArrayList<String> fileSystemIds = new ArrayList<>();

        Criteria criteria = new Criteria();
        criteria.put("clusterType", HPCClusterTypeEnum.PRE_SAAS_PRIVATE.getCode());
        List<ResHpcClusterPool> resHpcClusterPools = resHpcClusterPoolMapper.selectByParam(criteria);
        resHpcClusterPools.forEach(resHpcClusterPool -> {
            com.alibaba.fastjson.JSONArray storageInfo = JSON.parseArray(String.valueOf(resHpcClusterPool.getStorageInfo()));
            if (CollectionUtils.isNotEmpty(storageInfo)) {
                com.alibaba.fastjson.JSONObject jsonObject = storageInfo.getJSONObject(0);
                com.alibaba.fastjson.JSONArray shareInfos = jsonObject.getJSONArray("ShareInfo");
                if (CollectionUtils.isNotEmpty(shareInfos)) {
                    shareInfos.forEach(shareInfo -> {
                        String fileSystemID = ((com.alibaba.fastjson.JSONObject) shareInfo).getString("FileSystemID");
                        if (StringUtils.isNotBlank(fileSystemID)) {
                            fileSystemIds.add(fileSystemID);
                        }
                    });
                }
            }
        });
        return fileSystemIds;
    }

    /**
     * 删除并记录节点变更
     * @param clusterId
     */
    @Override
    @Transactional
    public void clearRemovingNodeAndRecord(Long clusterId) {
        log.info("HPC专属资源池删除并保存变更记录-HPCServiceImpl.clearRemovingNodeAndRecord-INPUT:[{cluster:{}}]",clusterId);
        this.clearRemovingNode(clusterId);
        this.saveResChangeRecord(clusterId);
    }

    private void saveResChangeRecord(Long clusterId) {
        Date curr = new Date();
        Criteria criteria = new Criteria();
        criteria.put("resourceId",clusterId);
        criteria.put("resType", HPC_DRP);
        criteria.setOrderByClause(" id desc ");
        List<ResChangeRecord> resChangeRecords = resChangeRecordMapper.selectByParams(criteria);
        ResChangeRecord resChangeRecord = CollectionUtil.getFirst(resChangeRecords);

        List<NodeInfo> nodeInfoList = getNodeInfos(clusterId);
        log.info("HPC专属资源池删除节点-HPCServiceImpl-saveResChangeRecord-resChangeRecord:[{}]",resChangeRecord);
        if (resChangeRecord !=null) {
            long nodeNum = nodeInfoList.stream()
                    .filter(resource -> org.apache.commons.lang3.StringUtils.isNotEmpty(resource.getNodeType()))
                    .count();
            resChangeRecord.setNewType(String.valueOf(nodeNum));
            resChangeRecord.setNewExtra(getExtraJsonNode(nodeInfoList));
            resChangeRecord.setUpdatedDt(curr);
            resChangeRecordMapper.updateByPrimaryKey(resChangeRecord);
        }

    }

    private List<NodeInfo> getNodeInfos(Long clusterId) {
        List<NodeInfo> nodeInfoList = new ArrayList<>();
        List<ResBmsNodeInfo> bmsInfoList = resBmsMapper.selectNodeInfoListByClusterId(clusterId);
        if(CollectionUtil.isNotEmpty(bmsInfoList)){
            nodeInfoList.addAll(bmsInfoList);
        }
        List<ResVmNodeInfo> resInfoList = resVmMapper.selectNodeInfoListByClusterId(clusterId);
        if (CollectionUtil.isNotEmpty(resInfoList)) {
            nodeInfoList.addAll(resInfoList);
        }
        return nodeInfoList;
    }


    @Override
    public void updateHpcCluster(ResHpcClusterRemoteModule hpcClusterRemoteModule) {
        log.debug("HPC集群集群-updateHpcCluster-更新：[{}]", JSONUtil.toJsonStr(hpcClusterRemoteModule));
        int count = resHpcClusterMapper.updateByPrimaryKeySelective(
                BeanConvertUtil.convert(hpcClusterRemoteModule, ResHpcCluster.class));
        log.info("HPC集群集群-updateHpcCluster-更新条数：[{}]", count);
    }

    @Override
    @Transactional
    public void deleteHpcCluster(Long hpcClusterId, String status) {

        ResHpcCluster resHpcCluster = resHpcClusterMapper.selectByPrimaryKey(hpcClusterId);
        ResHpcClusterResourceExample hpcClusterResourceExample = new ResHpcClusterResourceExample();
        hpcClusterResourceExample.createCriteria().andClusterIdEqualTo(hpcClusterId);
        //集群关联资源关系
        List<ResHpcClusterResource> resHpcClusterResources
                = resHpcClusterResourceMapper.selectByExample(hpcClusterResourceExample);

        // 删除相关资源信息
        List<Long> ecsIds = Lists.newArrayList();
        List<Long> bmsIds = Lists.newArrayList();
        List<Long> ebsIds = Lists.newArrayList();

        resHpcClusterResources.stream().forEach(resHpcClusterResource -> {
            switch (resHpcClusterResource.getResourceType()) {
                case ServiceManage.ECS:
                    ecsIds.add(resHpcClusterResource.getResourceId());
                    break;
                case ServiceManage.BMS:
                    bmsIds.add(resHpcClusterResource.getResourceId());
                    break;
                case ServiceManage.EBS:
                    ebsIds.add(resHpcClusterResource.getResourceId());
                    break;
                case ServiceManage.VPC_PEER:
                    resVpcPeeringMapper.deleteByPrimaryKey(resHpcClusterResource.getResourceId());
                    break;
                case ServiceManage.FLOATING_IP:
                    resFloatingIpMapper.deleteByPrimaryKey(resHpcClusterResource.getResourceId());
                    break;
                case ServiceManage.VPC:
                    resVpcMapper.deleteByPrimaryKey(resHpcClusterResource.getResourceId());
                    networkMapper.deleteByExample(new Criteria("netVpcId", resHpcClusterResource.getResourceId()));
                    log.info("HPC资源池释放-deleteHpcCluster-VPC 删除：[{}]", resHpcClusterResource.getResourceId());
                    break;
                case ServiceManage.SFS:
                    if(HPCClusterTypeEnum.PRE_SAAS_PRIVATE.getCode().equals(resHpcCluster.getClusterType())){
                        resShareMapper.deleteDefaultShareById(resHpcClusterResource.getResourceId());
                        log.info("HPC资源池释放-deleteHpcCluster-内置弹性文件 删除：[{}]", resHpcClusterResource.getResourceId());
                    }
                    break;
                default:
                    break;
            }
        });
        // 删除ECS
        Criteria criteria = new Criteria();
        if (CollectionUtil.isNotEmpty(ecsIds)) {
            criteria.put("resVmIds", ecsIds);
            criteria.put("ignoreRecycle", "tmp");
            resVmMapper.deleteByExample(criteria);
        }
        // 删除BMS
        if (CollectionUtil.isNotEmpty(bmsIds)) {
            criteria = new Criteria();
            criteria.put("resBmsIds", bmsIds);
            resBmsMapper.deleteByExample(criteria);
        }
        // 删除VD
        if (CollectionUtil.isNotEmpty(ebsIds)) {
            criteria = new Criteria();
            criteria.put("resVdIds", ebsIds);
            resVdMapper.deleteByParams(criteria);
        }
        // 删除资源关联表信息
        resHpcClusterResourceMapper.deleteByExample(hpcClusterResourceExample);
        // 删除Cluster数据
        resHpcCluster.setStatus(status);
        resHpcClusterMapper.updateByPrimaryKey(resHpcCluster);
    }

    @Override
    public void addResShareRule(Long hpcClusterId) {
        log.info("设置文件访问权限-addResShareRule-hpcClusterId:[{}] ",hpcClusterId);
        ResHpcCluster resHpcCluster = resHpcClusterMapper.selectByPrimaryKey(hpcClusterId);
        if (HPCClusterTypeEnum.SAAS_PRIVATE.getCode().equals(resHpcCluster.getClusterType())) {
            log.info("设置文件访问权限-addResShareRule-普通自定义专属资源池，设置文件访问权限");
            AuthUser authUser = BasicInfoUtil.getUserInfoByUserSid(resHpcCluster.getOwnerId());
            AuthUserHolder.setAuthUser(authUser);
            AuthUserHolder.setOrg(BasicInfoUtil.getCurrentOrgInfo(authUser.getOrgSid()));
            log.debug("H设置文件访问权限-addResShareRule-authUser:[{}]",authUser);
            try {
                ResHpcClusterResourceExample example = new ResHpcClusterResourceExample();
                example.createCriteria().andClusterIdEqualTo(hpcClusterId).andResourceTypeEqualTo(ServiceManage.SFS);
                List<ResHpcClusterResource> sfsResourceList = resHpcClusterResourceMapper.selectByExample(example);
                log.info("设置文件访问权限-addResShareRule-sfsResourceList:[{}]",sfsResourceList);
                example = new ResHpcClusterResourceExample();
                example.createCriteria().andClusterIdEqualTo(hpcClusterId).andResourceTypeEqualTo(ServiceManage.VPC);
                List<ResHpcClusterResource> vpcResourceList = resHpcClusterResourceMapper.selectByExample(example);
                log.info("H设置文件访问权限-addResShareRule-vpcResourceList:[{}]",vpcResourceList);
                if (CollectionUtil.isEmpty(vpcResourceList) || CollectionUtil.isEmpty(sfsResourceList)) {
                    return;
                }
                List<ResShare> shares = resShareMapper.selectByParams(
                        new Criteria("resShareIds", sfsResourceList.stream()
                                .map(ResHpcClusterResource::getResourceId)
                                .collect(Collectors.toList())));
                log.info("设置文件访问权限-addResShareRule-shares:[{}]",shares);
                shares.stream().forEach(share -> {
                    if (!StringUtils.equalsIgnoreCase("DPC", share.getShareProto())) {
                        ResVpc resVpc = resVpcMapper.selectByPrimaryKey(vpcResourceList.get(0).getResourceId());
                        List<String> cidrs = new ArrayList<>();
                        if (resVpc != null) {
                            cidrs = Arrays.asList(resVpc.getCidr().split(","));
                        }
                        if (CollectionUtils.isEmpty(cidrs)) {
                            addResShareRule(resHpcCluster, vpcResourceList, share, null);
                        } else {
                            for (String cidr : cidrs) {
                                if (!addResShareRule(resHpcCluster, vpcResourceList, share, cidr)) {
                                    break;
                                }
                            }
                        }
                    }
                });

                updHpcDrpStatus(resHpcCluster, CcmTaskStatusEnum.MANUAL_MOUNT_STORAGE.getValue(),
                        CcmTaskStatusEnum.WAITING.getValue(), null, StrUtil.EMPTY);
            } catch (Exception e) {

                log.error("设置文件访问权限-addResShareRule-文件系统设置访问授权失败：[{}]", e.getMessage());
                if ("访问规则已存在".equals(e.getMessage())) {
                    updHpcDrpStatus(resHpcCluster, CcmTaskStatusEnum.MANUAL_MOUNT_STORAGE.getValue(),
                            CcmTaskStatusEnum.WAITING.getValue(), null, StrUtil.EMPTY);
                } else {
                    //更新状态
                    updHpcDrpStatus(resHpcCluster, CcmTaskStatusEnum.ADD_SHARE_RULE.getValue(),
                            CcmTaskStatusEnum.FAILED.getValue(), null, e.getMessage());
                }
            } finally {
                AuthUserHolder.clear();
            }
        }else {
            log.info("设置文件访问权限-addResShareRule-高级自定义专属资源池，跳过设置文件访问权限");
            updHpcDrpStatus(resHpcCluster, CcmTaskStatusEnum.MANUAL_MOUNT_STORAGE.getValue(),
                    CcmTaskStatusEnum.WAITING.getValue(), null, StrUtil.EMPTY);
        }
    }

    private Boolean addResShareRule(ResHpcCluster resHpcCluster, List<ResHpcClusterResource> vpcResourceList, ResShare share, String ipAddress) {
        ResShareRule shareRule = new ResShareRule();
        shareRule.setShareId(share.getId());
        shareRule.setVpcResId(StrUtil.toString(vpcResourceList.get(0).getResourceId()));
        if (StringUtils.isNotBlank(ipAddress)) {
            shareRule.setIpAddress(ipAddress);
        }
        shareRule.setAccessLevel("rw");
        shareRule.setUserAccess("no_all_squash");
        shareRule.setUserRootAccess("no_root_squash");
        shareRule.setPriority(0);
        log.info("设置文件访问权限-addResShareRule-文件系统授权:参数[{}]", JSONUtil.toJsonStr(shareRule));
        if (!resShareRuleService.addResShareRule(shareRule)) {
            log.error("设置文件访问权限-addResShareRule-文件系统设置访问授权失败");
            //更新状态
            updHpcDrpStatus(resHpcCluster, CcmTaskStatusEnum.ADD_SHARE_RULE.getValue(),
                            CcmTaskStatusEnum.FAILED.getValue(), ResHpcClusterStatus.APPLY, "文件系统设置访问授权失败");
            return false;
        }
        return true;
    }

    @Override
    public String getClusterTaskResult(ClusterTaskInfoRequest clusterTaskInfoRequest) {

        ResHpcCluster resHpcCluster = resHpcClusterMapper.selectByPrimaryKey(clusterTaskInfoRequest.getClusterId());
        //调接口查询状态
        TaskInfoScan taskInfoScan = CloudClientFactory.buildMQBean(resHpcCluster.getCloudEnvId(),
                                                                   TaskInfoScan.class);
        if (StrUtil.isBlank(resHpcCluster.getTaskId())) {
            log.info("HPC专属资源池激活状态查询-getClusterTaskResult：TaskID为空，请检查");
            return "Failed";
        }
        taskInfoScan.setTaskId(resHpcCluster.getTaskId());
        try {
            log.debug("HPC专属资源池激活状态查询-getClusterTaskResult：参数[{}]", JSONUtil.toJsonStr(taskInfoScan));
            FDTaskInfoResult result = (FDTaskInfoResult) MQHelper.rpc(
                    taskInfoScan);
            log.info("HPC专属资源池激活状态查询-getClusterTaskResult：返回[{}]", JSONUtil.toJsonStr(result));
            String status = result.getStatus();
            if (result.isSuccess()) {

                if ("Finish".equals(status)) {
                    resHpcCluster.setStatus(ResHpcClusterStatus.AVALIABLE);
                }
                resHpcCluster.setErrorInfo(JsonUtil.toJson(result.getErrorInfo()));
                resHpcClusterMapper.updateByPrimaryKey(resHpcCluster);
                return status;
            } else {
                resHpcCluster.setErrorInfo(JsonUtil.toJson(result.getErrorInfo()));
                resHpcClusterMapper.updateByPrimaryKey(resHpcCluster);
                return "Failed";
            }
        } catch (MQException e) {
            log.error("HPC专属资源池激活状态查询-getClusterTaskResult-失败:[{}]", e.getMessage());
        }
        return null;
    }

    @Override
    public VpcPeeringInfoResult getVpcPeeringScanInfo(VpcPeeringInfoResource vpcPeeringInfoResource) {

        VpcPeeringInfoResult result = new VpcPeeringInfoResult();
        ResVpcPeering resVpcPeering = resVpcPeeringMapper.selectByClusterKey(vpcPeeringInfoResource.getClusterId());

        //调接口查询状态
        VpcPeeringScan vpcPeeringScan = CloudClientFactory.buildMQBean(getHCSOCloudEnv().getId(),
                                                                       VpcPeeringScan.class);

        if (Strings.isNotBlank(vpcPeeringInfoResource.getApiKey()) && Strings.isNotBlank(
                vpcPeeringInfoResource.getSecureToken())) {
            vpcPeeringScan.setApiKey(vpcPeeringInfoResource.getApiKey());
            vpcPeeringScan.setSecureToken(vpcPeeringInfoResource.getSecureToken());
            vpcPeeringScan.setTenantId(vpcPeeringInfoResource.getTenantId());
            vpcPeeringScan.setDomain(vpcPeeringInfoResource.getDomain());
        }
        vpcPeeringScan.setVpcId(resVpcPeering.getAcceptVpcId());
        vpcPeeringScan.setStatus("PENDING_ACCEPTANCE");
        FDAuthInfo fdAuthInfo = getAuthInfo(Long.parseLong(resVpcPeering.getOwnerId()));
        String projectId = null;
        log.info("HPC专属资源池获取本地VpcPeering信息-getVpcPeeringScanInfo-用户FD项目信息：用户ID[{}]", resVpcPeering.getOwnerId());
        if (Objects.nonNull(fdAuthInfo)) {
            projectId = fdAuthInfo.getProjectID();
        }
        vpcPeeringScan.setProjectId(projectId);
        try {
            log.debug("HPC专属资源池获取本地VpcPeering信息-getVpcPeeringScanInfo：参数[{}]", JSONUtil.toJsonStr(vpcPeeringScan));
            VpcPeeringScanResult vpcPeeringScanResult = (VpcPeeringScanResult) MQHelper.rpc(
                    vpcPeeringScan);
            log.info("HPC专属资源池获取本地VpcPeering信息-getVpcPeeringScanInfo：返回[{}]", JSONUtil.toJsonStr(vpcPeeringScanResult));

            if (Objects.nonNull(vpcPeeringScanResult) && vpcPeeringScanResult.isSuccess()) {
                log.info("HPC专属资源池获取本地VpcPeering信息-getVpcPeeringScanInfo-返回状态成功！");
                result = BeanConvertUtil.convert(vpcPeeringScanResult, VpcPeeringInfoResult.class);
            } else {
                ResHpcCluster resHpcCluster = resHpcClusterMapper.selectByPrimaryKey(
                        vpcPeeringInfoResource.getClusterId());
                //// 更新状态
                //updHpcDrpStatus(resHpcCluster, CcmTaskStatusEnum.UPDATE_VPCPEERING.getValue(),
                //        CcmTaskStatusEnum.FAILED.getValue(), ResHpcClusterStatus.APPLY, "HPC专属资源池获取本地VpcPeering信息失败！["
                //                + vpcPeeringScanResult.getErrCode() + vpcPeeringScanResult.getErrMsg() + "]");
                result = null;
            }
        } catch (MQException e) {
            e.printStackTrace();
        }
        return result;
    }

    @Override
    public ClusterSfsInfoResult getClusterSfsInfo(Long clusterId) {
        ClusterSfsInfoResult result = new ClusterSfsInfoResult();
        ResHpcCluster resHpcCluster = resHpcClusterMapper.selectByPrimaryKey(clusterId);
        result.setClusterUuid(resHpcCluster.getResourceId());
        result.setAccount(resHpcCluster.getAdminUser());
        Org currentOrgInfo = BasicInfoUtil.getCurrentOrgInfo(resHpcCluster.getOrgSid());
        result.setOrgName(currentOrgInfo.getLdapOu());
        List<ResShare> shareList = resShareMapper.selectByClusterKey(clusterId);
        List<ClusterSfsInfoResult.SfsInfo> sfsInfoList = Lists.newArrayList();
        String path = "/home/<USER>/";
        log.info("HPC专属资源池文件系统信息-getClusterSfsInfo-目录获取参数 ：[{}]", resHpcCluster.getOrgSid());
        if (!bssUserRemoteService.checkUserVersion(Convert.toStr(resHpcCluster.getOrgSid()))) {
            path = "/home/";
        }
        log.info("HPC专属资源池文件系统信息-getClusterSfsInfo-目录 ：[{}]", path);
        for (ResShare share : shareList) {
            ClusterSfsInfoResult.SfsInfo sfsInfo = new ClusterSfsInfoResult.SfsInfo();
            sfsInfo.setShareUuid(share.getUuid());
            sfsInfo.setShareName(share.getName());
            if (StrUtil.isNotBlank(share.getExportLocations())) {
                String[] strings = JSONUtil.parseArray(share.getExportLocations()).toArray(new String[0]);
                Optional<String> dpc = Arrays.stream(strings).filter(s -> s.startsWith("DPC")).findFirst();
                Optional<String> nfs = Arrays.stream(strings).filter(s -> s.startsWith("NFS")).findFirst();
                sfsInfo.setDpcShareName(dpc.orElse(null));
                sfsInfo.setNfsShareName(nfs.orElse(null));
                sfsInfo.setMountPath(path + currentOrgInfo.getLdapOu());
            }
            sfsInfoList.add(sfsInfo);
        }
        result.setSfsInfo(sfsInfoList);
        return result;
    }

    @Override
    public HPCClusterDeleteResult releaseDrpHPC(Long clusterId) {
        ResHpcCluster resHpcCluster = resHpcClusterMapper.selectByPrimaryKey(clusterId);
        cn.com.cloudstar.rightcloud.common.pojo.User user = bssUserRemoteService.selectByPrimaryKey(resHpcCluster.getOwnerId());
        //CloudEnv cloudEnv = cloudEnvMapper.selectByPrimaryKey(resHpcCluster.getCloudEnvId());
        //List<HcsoUser> hcsoUsers = hcsoUserRemoteService.selectByParams(
        //    new Criteria("accountType", "2"));
        HPCClusterDelete hpcClusterDelete = CloudClientFactory.buildMQBean(resHpcCluster.getCloudEnvId(),
                                                                           HPCClusterDelete.class);
        //set(hpcClusterDelete, hcsoUsers);
        hpcClusterDelete.setHpcClusterID(resHpcCluster.getResourceId());
        hpcClusterDelete.setApplyTaskID(new IdWorker().nextId() + "");
        hpcClusterDelete.setTenantNameX(user.getAccount());
        //租户信息
        if (HPCClusterTypeEnum.SAAS_PRIVATE.getCode().equals(resHpcCluster.getClusterType())) {
            // 只有普通自定义专属才需要设置权限信息
            FDAuthInfo authInfo = getAuthInfo(resHpcCluster.getOwnerId());
            hpcClusterDelete.setTenantInfo(BeanConvertUtil.convert(authInfo, HPCClusterDelete.TenantInfo.class));
        }

        // 调用ccm
        HPCClusterDeleteResult result = new HPCClusterDeleteResult();
        try {
            log.info("HPC专属资源池退订-releaseDrpHPC-开始");
            result = (HPCClusterDeleteResult) MQHelper.rpc(
                    hpcClusterDelete);
            log.info("HPC专属资源池退订-releaseDrpHPC-结束：[{}]", JSONUtil.toJsonStr(result));
        } catch (MQException e) {
            log.error(e.getMessage(), e);
        }
        resHpcCluster.setErrorInfo(StrUtil.EMPTY);
        if (!result.isSuccess()) {
            resHpcCluster.setErrorInfo(result.getErrMsg());
            resHpcClusterMapper.updateByPrimaryKeySelective(resHpcCluster);
            throw new BizException("退订专属资源池,调用ccm失败");
        }
        return result;
    }

    private void set(Base base, List<HcsoUser> hcsoUsers) {
        if (CollectionUtil.isNotEmpty(hcsoUsers)) {
            String ak = CrytoUtilSimple.decrypt(hcsoUsers.get(0).getAk());
            String sk = CrytoUtilSimple.decrypt(hcsoUsers.get(0).getSk());
            String tenantId = hcsoUsers.get(0).getProjectId();
            String domain = hcsoUsers.get(0).getAccountId();
            base.setApiKey(ak);
            base.setSecureToken(sk);
            base.setTenantId(tenantId);
            base.setDomain(domain);
        }
    }

    /**
     * 开通失败回退
     *
     * @param resHpcClusterRemoteModule
     */
    @Override
    @Transactional
    public void rollbackShare(ResHpcClusterRemoteModule resHpcClusterRemoteModule) {
        ResHpcCluster resHpcCluster = resHpcClusterMapper.selectByPrimaryKey(resHpcClusterRemoteModule.getId());
        resHpcCluster.setStatus("apply");
        resHpcClusterMapper.updateByPrimaryKey(resHpcCluster);
        ResHpcClusterResourceExample hpcClusterResourceExample = new ResHpcClusterResourceExample();
        hpcClusterResourceExample.createCriteria().andClusterIdEqualTo(resHpcCluster.getId());
        //集群关联资源关系
        List<ResHpcClusterResource> resHpcClusterResources
                = resHpcClusterResourceMapper.selectByExample(hpcClusterResourceExample);
        for (ResHpcClusterResource resHpcClusterResource : resHpcClusterResources) {
            Long resourceId = resHpcClusterResource.getResourceId();
            ResShare resShare = resShareMapper.selectByPrimaryKey(resourceId);
            if (resShare != null) {
                if (resShare.getIsVirtual()) {
                    ResShareType shareType = resShareTypeMapper.selectByPrimaryKey(
                            resShare.getShareTypeId().intValue());
                    ResShare commonResShares = getCommonResShares(shareType, 1);
                    resShareMapper.increaseAllocateSize(commonResShares.getId(), resShare.getSize() * -1);
                    resShareMapper.deleteShareById(resShare.getId());
                } else {
                    resShare.setOrgSid(0L);
                    resShareMapper.updateByPrimaryKey(resShare);
                }
            }
        }
        resHpcClusterResourceMapper.deleteByExample(hpcClusterResourceExample);
    }

    /**
     * 更新HPC专属执行状态
     *
     * @param resHpcCluster
     * @param processPhase
     * @param processStatus
     * @param status
     * @param error
     */
    private void updHpcDrpStatus(ResHpcCluster resHpcCluster, String processPhase, String processStatus, String status,
                                 String error) {

        // 更新状态
        resHpcCluster.setProessPhase(processPhase);
        resHpcCluster.setProessStatus(processStatus);
        if (Strings.isNotBlank(status)) {
            resHpcCluster.setStatus(status);
        }
        resHpcCluster.setErrorInfo(error);
        log.debug("更新HPC专属执行状态-updHpcDrpStatus-更新：[{}]", JSONUtil.toJsonStr(resHpcCluster));
        int count = resHpcClusterMapper.updateByPrimaryKeySelective(resHpcCluster);
        log.info("更新HPC专属执行状态-updHpcDrpStatus-更新条数：[{}]", count);
    }

    @Override
    public String getHCSOEndPoint() {

        Long cloudId = getHCSOCloudEnv().getId();
        Base base = CloudClientFactory.buildMQBean(cloudId,
                                                   Base.class);
        String substring = "";
        try {
            String provider = base.getProviderUrl();
            log.info("HCSOEndPoint获取-getHCSOEndPoint:[{}]", provider);
            substring = provider.contains("https://iam.") ?
                    provider.replace("https://iam.", "https://iam-pub.") : provider;
            substring = substring.replace("/v3", "");
            log.info("HCSOEndPoint获取-getHCSOEndPoint:[{}]", provider);
        } catch (Exception e) {
            throw new RuntimeException("管理地址格式有误！");
        }
        return substring;
    }

    @Override
    public List<ResHpcClusterRemoteModule> getResHpcClusterByShareId(Long id) {
        ResHpcClusterResourceExample hpcClusterResourceExample = new ResHpcClusterResourceExample();
        hpcClusterResourceExample.createCriteria().andResourceIdEqualTo(id).andResourceTypeEqualTo("SFS");
        List<ResHpcClusterResource> resHpcClusterResources = resHpcClusterResourceMapper.selectByExample(
                hpcClusterResourceExample);
        if (CollectionUtil.isEmpty(resHpcClusterResources)) {
            return Collections.emptyList();
        }
        ResHpcClusterExample resHpcClusterExample = new ResHpcClusterExample();
        resHpcClusterExample.createCriteria()
                            .andIdIn(resHpcClusterResources.stream()
                                                           .map(ResHpcClusterResource::getClusterId)
                                                           .distinct()
                                                           .collect(
                                                                   Collectors.toList()));
        List<ResHpcCluster> resHpcClusters = resHpcClusterMapper.selectByExample(resHpcClusterExample);
        return BeanConvertUtil.convert(resHpcClusters, ResHpcClusterRemoteModule.class);
    }

    @Override
    public Boolean checkHpcClusterName(String clusterName) {
        Criteria criteria = new Criteria();
        String applyType = "";
        if (clusterName.contains(CommonPropertyKeyEnum.PLACEHOLDER_MARK.getCode())) {
            String[] split = clusterName.split(CommonPropertyKeyEnum.PLACEHOLDER_MARK.getCode());
            clusterName = split[0];
            applyType = split[1];
        }
        criteria.put("clusterName", clusterName);
        List<ResHpcClusterPool> resHpcClusterPools = resHpcClusterPoolMapper.selectByParam(criteria);

        if (HPC_DRP_STANDARD.equals(applyType)) {
            //去掉预部署资源池
            resHpcClusterPools.removeIf(info -> "PreSAASPrivate".equals(info.getClusterType()));
        }

        if (CollectionUtils.isNotEmpty(resHpcClusterPools)) {
            return true;
        }
        criteria.put("name", clusterName);
        User authUser = BasicInfoUtil.getAuthUser();
        if (Objects.nonNull(authUser) && Objects.nonNull(authUser.getOrgSid())) {
            criteria.put("orgSid", authUser.getOrgSid());
        }
        criteria.put("statusNoEq", "unsubscribed");
        List<ResHpcCluster> resHpcClusters = resHpcClusterMapper.selectByParams(criteria);

        if (CollectionUtil.isNotEmpty(resHpcClusters)) {
            for (ResHpcCluster item : resHpcClusters) {
                if (ResHpcClusterStatus.REJECTED.equals(item.getStatus())
                        || ResHpcClusterStatus.DELETED.equals(item.getStatus())) {
                    continue;
                }
                return true;
            }
        }
        return false;
    }


    @Override
    public Boolean checkHpcClusterIgnoreCaseName(String clusterName) {
        log.info("HPC资源池名字校验忽略大小写-checkHpcClusterIgnoreCaseName-参数:[{}]", clusterName);
        Integer num = resHpcClusterMapper.countHpcClusterIgnoreCaseName(clusterName.toLowerCase());
        log.info("HPC资源池名字校验忽略大小写-checkHpcClusterIgnoreCaseName-num:[{}]", num);
        return num != null && num > 0;
    }

    @Override
    public String getHpcClusterName(String account) {
        Criteria criteria = new Criteria();
        criteria.put("nameLike", account);
        User authUser = BasicInfoUtil.getAuthUser();
        if (Objects.nonNull(authUser) && Objects.nonNull(authUser.getOrgSid())) {
            criteria.put("orgSid", authUser.getOrgSid());
        }
        List<ResHpcCluster> hpcClusters = resHpcClusterMapper.selectByParams(criteria);
        List<String> hpcClusterNames = hpcClusters.stream().map(ResHpcCluster::getName).collect(Collectors.toList());

        String clusterName;
        int i = 1;
        while (true) {
            clusterName = account + String.format("%03d", i++);
            if (!hpcClusterNames.contains(clusterName)) {
                return clusterName;
            }
        }
    }

    @Override
    public Map<String, String> getHpcDrpResourceInfo(QueryHpcDrpResourceRequest queryHpcDrpResourceRequest) {
        Map<String, String> result = Maps.newHashMap();
        ResHpcCluster resHpcCluster = resHpcClusterMapper.selectByPrimaryKey(
                queryHpcDrpResourceRequest.getHpcClusterID());
        result.put(HPC_DRP + resHpcCluster.getScenario(), resHpcCluster.getResourceId());

        //增加resourceId,插入账单明细表
        result.put("resourceId",resHpcCluster.getResourceId());

        Criteria criteria = new Criteria();
        criteria.put("byClusterId", queryHpcDrpResourceRequest.getHpcClusterID());
        List<ResVm> resVmList = resVmMapper.selectByCriteriaWithNoFilter(criteria);
        resVmList.stream().forEach(vm -> {
            String key = "ECS" + vm.getInstanceType();
            result.put(key, StrUtil.isBlank(result.get(key))
                    ? vm.getInstanceId() : result.get(key) + "," + vm.getInstanceId());
        });
        List<ResBms> resBmsList = resBmsMapper.selectByParams(criteria);
        resBmsList.stream().forEach(bms -> {
            String key = "BMS" + bms.getInstanceType();
            result.put(key, StrUtil.isBlank(result.get(key))
                    ? bms.getInstanceId() : result.get(key) + "," + bms.getInstanceId());
        });
        //内置弹性文件
        List<ResShare> shareHPCSFS =getDefaultShareHPCSFS(queryHpcDrpResourceRequest.getHpcClusterID());
        if(shareHPCSFS.size()>0){
            shareHPCSFS.stream().forEach(sfs -> {
                String key = "SFS2.0" + sfs.getShareType()+"-"+sfs.getShareProto()+"("+sfs.getSize()+"GB)";
                result.put(key, StrUtil.isBlank(result.get(key))
                        ? sfs.getUuid() : result.get(key) + "," + sfs.getUuid());
            });
        }

        log.info("HPC资源池资源详情-getHpcDrpResourceInfo-返回：[{}]", result);
        return result;
    }

    public String saveHpcDrpExcel(Long clusterId, List<HPCComputeExcel> computeNodeInfo) {
        String resultFile = null;
        OutputStream out = null;
        FileInputStream fileInputStream = null;
        BufferedInputStream bufferedInputStream = null;
        try {
            String path = uploadBasePath + SYMBOL + "hpc_drp";
            File file = new File(path);
            if (!file.exists() && !file.isDirectory()) {
                file.mkdirs();
            }
            String fileName = path + SYMBOL + "hpc_drp_" + clusterId + XLSX;
            out = new FileOutputStream(fileName);
            ExcelUtils excel = new ExcelUtils();
            excel.exportExcel(out, "计算节点信息", computeNodeInfo, HPCComputeExcel.class);


            fileInputStream = new FileInputStream(fileName);
            bufferedInputStream = new BufferedInputStream(fileInputStream);

            StorageResult result = storageService.saveFile(bufferedInputStream, StoragePathEnum.EXCEL.getPath("files"), fileName, true,
                    true, null);

            resultFile = result.getRelativeNginxUrl();

            if (file.exists()) {
                FileUtil.del(fileName);
            }
        } catch (Exception e) {
            log.error("HPC资源池保存计算节点信息-saveHpcDrpExcel-失败：[{}]", e.getMessage());
        } finally {
            IOUtils.closeQuietly(out);
            IOUtils.closeQuietly(fileInputStream);
            IOUtils.closeQuietly(bufferedInputStream);
        }
        return resultFile;
    }

    @Override
    public Boolean hpcDrpExpansionNode(HpcDrpExtentionNodeRequest extentionNodeRequest) {
        Boolean result = Boolean.FALSE;

        User applyUser = extentionNodeRequest.getApplyUser();

        Long userSid = applyUser.getUserSid();
        Long clusterId = extentionNodeRequest.getClusterId();

        Integer cliNum = extentionNodeRequest.getCliNum();
        Integer vncNum = extentionNodeRequest.getVncNum();
        Integer agentNum = extentionNodeRequest.getAgentNum();

        ResHpcCluster resHpcCluster = resHpcClusterMapper.selectByPrimaryKey(clusterId);
        HPCExpansionNode hpcExpansionNode = CloudClientFactory.buildMQBean(resHpcCluster.getCloudEnvId(),
                                                                           HPCExpansionNode.class);

        //获取集群关联资源
        ResHpcClusterResourceExample hpcClusterResourceExample = new ResHpcClusterResourceExample();
        hpcClusterResourceExample.createCriteria().andClusterIdEqualTo(clusterId);
        List<ResHpcClusterResource> resHpcClusterResources = resHpcClusterResourceMapper.selectByExample(
                hpcClusterResourceExample);

        hpcExpansionNode.setHpcClusterID(resHpcCluster.getResourceId());
        hpcExpansionNode.setApplyTaskDescription(applyUser.getAccount() + "HPC专属资源池扩容");
        hpcExpansionNode.setApplyTaskID(new IdWorker().nextId() + "");
        //设置权限信息
        if (HPCClusterTypeEnum.SAAS_PRIVATE.getCode().equals(resHpcCluster.getClusterType())) {
            // 只有普通自定义专属才需要设置权限信息
            FDAuthInfo authInfo = getAuthInfo(userSid);
            hpcExpansionNode.setAuthInfo(authInfo);
        }
        //设置HPC
        HPCExpansionApplyInfo hpcApplyInfo = new HPCExpansionApplyInfo();
        FDExpansionResourceInfo fdResourceInfo = new FDExpansionResourceInfo();
        fdResourceInfo.setAgentNum(agentNum);
        fdResourceInfo.setVNCNum(vncNum);
        fdResourceInfo.setCLINum(cliNum);
        fdResourceInfo.setAvailabilityZone(resHpcCluster.getAvailabilityZone());

        //设置PublicInfo
        PublicIPInfo publicIPInfo = getPublicIPInfo(resHpcClusterResources);
        fdResourceInfo.setPublicIPInfo(publicIPInfo);

        //查询登录节点规格 并设置
        log.info("HPC集群信息-hpcDrpExpansionNode-resHpcClusterResources:[{}]", PlaintextShieldUtil.eliminatePlaintext(resHpcClusterResources));
        List<ManagementNodeInfo> managementNodeInfos = new ArrayList<>();
        if (cliNum != null && cliNum > 0) {
            assembleManagement(resHpcClusterResources, managementNodeInfos);
        }
        fdResourceInfo.setManagementNodeInfo(managementNodeInfos);
        //查询计算节点 并设置
        List<ComputeNodeInfo> computeNodeInfoList = new ArrayList<>();
        if (agentNum != null && agentNum > 0) {
            assembleCompute(resHpcClusterResources, computeNodeInfoList, agentNum);
        }
        fdResourceInfo.setComputeNodeInfo(computeNodeInfoList);

        //查询VNC节点 并设置
        List<GraphicNodeInfo> vncNodeInfoList = new ArrayList<>();
        if (vncNum != null && vncNum > 0) {
            assembleVNC(resHpcClusterResources, vncNodeInfoList, extentionNodeRequest);
        }
        fdResourceInfo.setGraphicNodeInfo(vncNodeInfoList);

        hpcApplyInfo.setResourceInfo(fdResourceInfo);
        hpcExpansionNode.setHpcApplyInfo(hpcApplyInfo);

        HPCExpansionNodeResult expansionNodeResult = null;
        Date current = new Date();
        try {
            log.info("HPC集群信息-hpcDrpExpansionNode-HPCExpansionNode:[{}]", PlaintextShieldUtil.eliminatePlaintext(hpcExpansionNode));
            expansionNodeResult = (HPCExpansionNodeResult) MQHelper.rpc(hpcExpansionNode);
            log.info("HPC集群信息-hpcDrpExpansionNode-HPCExpansionNodeResult:[{}]", PlaintextShieldUtil.eliminatePlaintext(expansionNodeResult));
            if (Objects.nonNull(expansionNodeResult) && expansionNodeResult.isSuccess()) {
                //扩容申请
                String taskId = expansionNodeResult.getTaskID();
                log.info("HPCServiceImpl-hpcDrpExpansionNode-申请资源任务ID： {}", taskId);
                resHpcCluster.setTaskId(taskId);
//                resHpcCluster.setProessPhase(CcmTaskStatusEnum.APPLY_UPGRADE_RESOURCE.getValue());
                resHpcCluster.setProessStatus(CcmTaskStatusEnum.PROGRESSING.getValue());
                resHpcCluster.setStatus(ResHpcClusterStatus.UPGRADING);
                resHpcCluster.setUpdatedDt(current);
                resHpcClusterMapper.updateByPrimaryKeySelective(resHpcCluster);

                //插入变更记录
                ResChangeRecord resChangeRecord = createResChangeRecord(resHpcCluster, resHpcClusterResources);
                resChangeRecord.setConfigDesc(getNodeInfo(resHpcCluster,vncNodeInfoList));
                long nodeNum = resHpcClusterResources.stream()
                                                     .filter(resource -> StringUtils.isNotEmpty(resource.getNodeType()))
                                                     .count();
                resChangeRecord.setOriginalType(String.valueOf(nodeNum));
                resChangeRecord.setCreatedDt(current);
                String oldExtrajson = getExtraJson(resHpcClusterResources);
                resChangeRecord.setOriginalExtra(oldExtrajson);
                resChangeRecord.setChangeType(ResChangeTypeEnum.UPGRADE.getCode());
                resChangeRecordMapper.insertSelective(resChangeRecord);
                return Boolean.TRUE;
            }

        } catch (Exception e) {
            log.error("HPC集群信息-hpcDrpExpansionNode-Exception:[{}]", e.getMessage());
//            resHpcCluster.setProessPhase(CcmTaskStatusEnum.APPLY_UPGRADE_RESOURCE.getValue());
            resHpcCluster.setProessStatus(CcmTaskStatusEnum.FAILED.getValue());
            resHpcCluster.setStatus(ResHpcClusterStatus.UPGRADING);
            resHpcCluster.setUpdatedDt(current);
            resHpcClusterMapper.updateByPrimaryKeySelective(resHpcCluster);
            resHpcCluster.setErrorInfo("HPC专属资源扩容申请失败，错误详情请登录CCM查看！集群名称：["
                                               + resHpcCluster.getName() + "]");
        }
        //扩容申请
//        resHpcCluster.setProessPhase(CcmTaskStatusEnum.APPLY_UPGRADE_RESOURCE.getValue());
        resHpcCluster.setProessStatus(CcmTaskStatusEnum.FAILED.getValue());
        resHpcCluster.setStatus(ResHpcClusterStatus.UPGRADING);
        if (expansionNodeResult != null) {
            resHpcCluster.setTaskId(expansionNodeResult.getTaskId());
            resHpcCluster.setErrorInfo("HPC专属资源扩容申请失败，错误详情请登录CCM查看！集群名称：["
                                               + resHpcCluster.getName() + "]");
        }
        resHpcCluster.setUpdatedDt(current);
        resHpcClusterMapper.updateByPrimaryKeySelective(resHpcCluster);
        return result;
    }

    private  String  getNodeInfo(ResHpcCluster request,List<GraphicNodeInfo> vncNodeInfoList){
        Criteria criteria = new Criteria();
        criteria.put("clusterId", request.getId());
        List<HPCNodeInfoMemberVo> resList = resHpcClusterMapper.getClusterByIdAndType(criteria);
        Map<String,String> resVmTypeMap = new HashMap<>(10);
        resList.forEach(hpcNode -> {
            String resTypeName = hpcNode.getTypeName();
            String nodeName = resVmTypeMap.get(resTypeName);
            if (StringUtils.isEmpty(nodeName)) {
                QueryResVmTypeByParamsRequest resVmTypeByParamsRequest =
                        new QueryResVmTypeByParamsRequest();
                resVmTypeByParamsRequest.setUuid(resTypeName);
                Criteria criteria1 = Criteria.prepareNewCriteria(resVmTypeByParamsRequest);
                ResVmType resVmType = CollectionUtil.getFirst(resVmTypeService.selectByParams(criteria1));
                if(resVmType !=null){
                    StringBuffer sb = new StringBuffer();
                    if(resVmType.getCpu() !=null){
                        sb.append(resVmType.getCpu()).append("核CPU");
                    }
                    if (resVmType.getRam() !=null) {
                        sb.append("、").append(Float.valueOf(resVmType.getRam()/1024).intValue()).append("GiB内存");
                    }
                    //VNC节点不需要卡CPU
                    if(!"VNC".equals(hpcNode.getHpcPointType())){
                        if (resVmType.getGpu() !=null && resVmType.getGpu() > 0) {
                            sb.append("、").append(resVmType.getGpu()).append("卡GPU");
                        }
                    }
                    //sb.append("(").append(resTypeName).append(")");
                    nodeName = sb.toString();
                    resVmTypeMap.put(resTypeName,nodeName);
                }
            }
            hpcNode.setName(nodeName);
        });
        Map<String,String> nodeMap = new HashMap<>(10);
        String cli = "";
        Optional<HPCNodeInfoMemberVo> optional = resList.stream().filter(s -> StringUtils.isNotEmpty(s.getHpcPointType())
                && "CCS_CLI".equals(s.getHpcPointType())).findFirst();
        if (optional.isPresent()) {
            cli = optional.get().getName();
        }
        String compute = "";
        optional = resList.stream().filter(s -> "compute".equals(s.getNodeType())).findFirst();
        if (optional.isPresent()) {
            compute = optional.get().getName();
        }

        boolean flag = resList.stream().anyMatch(role ->StringUtils.isNotEmpty(role.getHpcPointType())
                && ("VNC").equals(role.getHpcPointType()));
        String vnc="";
        if(flag){
            optional = resList.stream().filter(s -> StringUtils.isNotEmpty(s.getHpcPointType()) && "VNC".equals(s.getHpcPointType())).findFirst();
            if (optional.isPresent()) {
                vnc = optional.get().getName();
            }

            String vncNumKey = "VNC节点";
            nodeMap.put(vncNumKey,vnc);
        }else{
            if(vncNodeInfoList.size()>0){
                String vncNumKey = "VNC节点";
                String ref=vncNodeInfoList.get(0).getFlavorRef();
                vnc="32核CPU、128GiB内存";
                if("c6.22xlarge.4.physical".equals(ref)){
                   vnc="16核CPU、128GiB内存";
                }
                nodeMap.put(vncNumKey,vnc);
            }
        }
        String cliNumKey = "登录节点";
        String computeNumKey = "计算节点";
        nodeMap.put(cliNumKey,cli);
        nodeMap.put(computeNumKey,compute);
        return JSON.toJSONString(nodeMap);
    }
    /**
     * 变更资源json
     *
     * @param resHpcClusterResources
     */
    private String getExtraJson(List<ResHpcClusterResource> resHpcClusterResources) {
        long vncNum = resHpcClusterResources.stream()
                                            .filter(resource -> StringUtils.isNotEmpty(resource.getNodeType())
                                                    && HPC_POINT_TYPE_VNC.equals(resource.getHpcPointType()))
                                            .count();
        long cliNum = resHpcClusterResources.stream()
                                                .filter(resource -> StringUtils.isNotEmpty(resource.getNodeType())
                                                        && HPC_POINT_TYPE_CLI.equals(resource.getHpcPointType()))
                                                .count();
        long computeNum = resHpcClusterResources.stream()
                                                .filter(resource -> COMPUTE.equals(resource.getNodeType()))
                                                .count();
        Map<String, Long> nodeMap = new HashMap<>(3);
        nodeMap.put("计算节点数", computeNum);
        nodeMap.put("登录节点数", cliNum);
        nodeMap.put("VNC节点数", vncNum);
        return JSON.toJSONString(nodeMap);
    }

    private String getExtraJsonNode(List<NodeInfo> nodeInfoList) {
        long vncNum = nodeInfoList.stream()
                .filter(resource -> org.apache.commons.lang3.StringUtils.isNotEmpty(resource.getNodeType())
                        && HPC_POINT_TYPE_VNC.equals(resource.getHpcPointType()))
                .count();
        long cliNum = nodeInfoList.stream()
                .filter(resource -> org.apache.commons.lang3.StringUtils.isNotEmpty(resource.getNodeType())
                        && HPC_POINT_TYPE_CLI.equals(resource.getHpcPointType()))
                .count();
        long computeNum = nodeInfoList.stream()
                .filter(resource -> COMPUTE.equals(resource.getNodeType()))
                .count();
        Map<String, Long> nodeMap = new HashMap<>(3);
        nodeMap.put("计算节点数", computeNum);
        nodeMap.put("登录节点数", cliNum);
        nodeMap.put("VNC节点数", vncNum);
        return JSON.toJSONString(nodeMap);
    }

    /**
     * @param resHpcCluster
     * @param resHpcClusterResources
     */
    private ResChangeRecord createResChangeRecord(ResHpcCluster resHpcCluster,
                                                  List<ResHpcClusterResource> resHpcClusterResources) {
        ResChangeRecord resChangeRecord = new ResChangeRecord();
        resChangeRecord.setCloudEnvId(resHpcCluster.getCloudEnvId());
        resChangeRecord.setOwnerId(resHpcCluster.getOwnerId());
        resChangeRecord.setOrgSid(resHpcCluster.getOrgSid());
        resChangeRecord.setResourceId(resHpcCluster.getId().toString());
        resChangeRecord.setInstanceId(resHpcCluster.getResourceId());
        resChangeRecord.setResType(HPC_DRP);
        return resChangeRecord;
    }

    /**
     * HPC 专属资源池添加 节点
     *
     * @param upgradeHpcDrpResource
     *
     * @return 返回新增的Bms 和 esc节点信息
     */
    @Override
    @Transactional
    public List<NodeInfo> upgradeHpcDrp(UpgradeHpcDrpResource upgradeHpcDrpResource) {
        List<NodeInfo> newNodeInfoList = new ArrayList<>();
        Long clusterId = upgradeHpcDrpResource.getClusterId();
        Date current = new Date();
        ResHpcCluster resHpcCluster = resHpcClusterMapper.selectByPrimaryKey(clusterId);
        if (resHpcCluster == null) {
            log.info("HPC专属资源池变更配置-upgradeHpcDrp-clusterId[{}]不存在", clusterId);
            throw new BizException("集群不存在");
        }
        String productConfig = upgradeHpcDrpResource.getProductConfig();
        if (!JSONUtil.isJson(productConfig)) {
            throw new BizException("productConfig非json");
        }

        //获取集群关联资源ResourceId集合
        Set<String> instanceIdSet = getInstanceIdSet(clusterId);

        Long orgSid = resHpcCluster.getOrgSid();
        User authUser = BasicInfoUtil.getAuthUser();
        if (authUser == null || authUser.getUserSid() == null) {
            Long ownerId = resHpcCluster.getOwnerId();
            cn.com.cloudstar.rightcloud.basic.data.pojo.user.User user = systemMUserMapper.selectByPrimaryKey(ownerId);
            authUser = BeanConvertUtil.convert(user, User.class);
        }

        // 集群资源
        List<ResHpcClusterResource> hpcClusterResourceList = Lists.newArrayList();
        // ECS && BMS
        List<VmNodeInfo> nodes = Lists.newArrayList();

        //读取节点信息
        for (Object object : JSONUtil.parseArray(upgradeHpcDrpResource.getProductConfig())) {
            JSONObject jsonObject = (JSONObject) object;
            Object value = jsonObject.get(JSON_VALUE);
            if (Objects.isNull(value)) {
                continue;
            }
            if (Objects.equals(jsonObject.get(JSON_ATTR_KEY), "computeNodeInfo")
                    || Objects.equals(jsonObject.get(JSON_ATTR_KEY), "managementNodeInfo")
                    || Objects.equals(jsonObject.get(JSON_ATTR_KEY), "loginNodeInfo")
                    || Objects.equals(jsonObject.get(JSON_ATTR_KEY), "vncNodeInfo")

            ) {
                nodes.addAll(JsonUtil.fromJson(JsonUtil.toJson(jsonObject.get(JSON_VALUE)),
                                               new TypeReference<List<VmNodeInfo>>() {
                                               }));
            }
        }
        // HCSO环境ID
        Long hcsoCloudEnvId = getHCSOCloudEnv().getId();
        // ECS BMS VD 入库
        insertNodeInfo(nodes, authUser, orgSid, hcsoCloudEnvId, hpcClusterResourceList, resHpcCluster);

        // ClusterResource 新增节点
        if (CollectionUtil.isNotEmpty(hpcClusterResourceList)) {
            resHpcClusterResourceMapper.insertBatch(hpcClusterResourceList);
        }

        // 查找接口调用 HPC资源池详情
        HPCClusterInfoIDResult hpcClusterInfoIDResult = getHPCClusterInfoById(resHpcCluster.getId());
        HPCClusterInfoVO hpcClusterInfo = hpcClusterInfoIDResult.getHPCClusterInfo();
        //更新HpcCluster
        resHpcCluster.setResourceId(hpcClusterInfo.getHPCClusterID());
        // 清空ip数据
        //更新管理节点
        List<HPCManagementNodeInfoVO> managementNodeInfos = hpcClusterInfo.getManagementNodeInfo();
        log.debug("管理节点列表：[{}]", JSONUtil.toJsonStr(managementNodeInfos));

        //更新计算节点
        List<HPCComputeNodeInfoVO> computeNodeInfo = hpcClusterInfo.getComputeNodeInfo();
        List<HPCComputeNodeInfoVO> computeNodeInfoTmp = Lists.newArrayList();
        computeNodeInfoTmp.addAll(computeNodeInfo);

        // 计算节点保存Excel
        if (CollectionUtil.isNotEmpty(computeNodeInfoTmp)) {
            resHpcCluster.setClusterFile(saveHpcDrpExcel(
                    resHpcCluster.getId(), BeanConvertUtil.convert(computeNodeInfoTmp, HPCComputeExcel.class)));
        }
        //VNC节点
        List<HPCVNCNodeInfoVO> vncNodeInfoList = hpcClusterInfo.getVncNodeInfo();

        for (ResHpcClusterResource hpcClusterResource : hpcClusterResourceList) {
            if (StringUtils.equalsIgnoreCase(MANAGER,hpcClusterResource.getNodeType())) {
                log.info("HPC专属资源池变更配置-upgradeHpcDrp-HpcPointType：[{}]", hpcClusterResource.getHpcPointType());
                if (CollectionUtil.isNotEmpty(managementNodeInfos)) {
                    for (HPCManagementNodeInfoVO item : managementNodeInfos) {
                        if (Objects.equals(hpcClusterResource.getHpcPointType(), item.getHPCNodeType())) {

                            log.debug("HPC专属资源池变更配置-upgradeHpcDrp-HPCManagementNode：[{}]", JSONUtil.toJsonStr(item));
                            //更新未保存的节点
                            boolean isResourceIdExit = instanceIdSet.contains(item.getResourceID());
                            if (!isResourceIdExit) {
                                if (Objects.equals(hpcClusterResource.getResourceType(), ServiceManage.ECS)) {
                                    ResVm resVm = resVmMapper.selectByPrimaryKey(
                                            String.valueOf(hpcClusterResource.getResourceId()));
                                    log.debug("HPC专属资源池变更配置-upgradeHpcDrp-ResVm：[{}]", JSONUtil.toJsonStr(resVm));
                                    if (resVm != null) {
                                        resVm.setInstanceId(item.getResourceID());
                                        resVm.setPublicIp(item.getEIP());
                                        resVm.setInnerIp(item.getVIP());
                                        resVm.setFloatingIp(item.getFIP());
                                        resVm.setHostName(item.getHostName());
                                        log.debug("HPC专属资源池变更配置-upgradeHpcDrp-管理节点 ECS更新：[{}]", JSONUtil.toJsonStr(resVm));
                                        resVmMapper.updateByPrimaryKey(resVm);
                                    }
                                } else if (Objects.equals(hpcClusterResource.getResourceType(), ServiceManage.BMS)) {
                                    ResBms resBms = resBmsMapper.selectByPrimaryKey(
                                            String.valueOf(hpcClusterResource.getResourceId()));
                                    if (resBms != null) {
                                        resBms.setInstanceId(item.getResourceID());
                                        resBms.setPublicIp(item.getEIP());
                                        resBms.setInnerIp(item.getVIP());
                                        resBms.setFloatingIp(item.getFIP());
                                        resBms.setHostName(item.getHostName());
                                        log.debug("HPC专属资源池变更配置-upgradeHpcDrp-管理节点 BMS更新：[{}]", JSONUtil.toJsonStr(resBms));
                                        resBmsMapper.updateByPrimaryKey(resBms);
                                    }
                                }
                                log.info("HPC专属资源池变更配置-upgradeHpcDrp：update");
                                hpcClusterResource.setExtraInfo(item.getExtraInfo());
                                hpcClusterResource.setNodeStatus(item.getNodeStatus());
                                resHpcClusterResourceMapper.updateByPrimaryKey(hpcClusterResource);
                                log.info("HPC专属资源池变更配置-upgradeHpcDrp：updated");
                                instanceIdSet.add(item.getResourceID());
                                break;
                            }

                        }
                    }
                    log.info("HPC专属资源池变更配置-upgradeHpcDrp-ccp对外地址：" + resHpcCluster.getCcpInternelAddress());
                    log.info("HPC专属资源池变更配置-upgradeHpcDrp-ccp对内地址：" + resHpcCluster.getCcpExternalAddress());
                    log.info("HPC专属资源池变更配置-upgradeHpcDrp-cli对外地址：" + resHpcCluster.getLoginNodeInternalAddress());
                    log.info("HPC专属资源池变更配置-upgradeHpcDrp-cli对内地址：" + resHpcCluster.getLoginNodeExternalAddress());
                }
            }
            else if(StringUtils.equalsIgnoreCase(VNC,hpcClusterResource.getNodeType())){
                if (Objects.equals(hpcClusterResource.getHpcPointType(), HPC_POINT_TYPE_VNC)) {
                    if (CollectionUtil.isNotEmpty(vncNodeInfoList)) {
                        for (HPCVNCNodeInfoVO item : vncNodeInfoList) {
                            //更新未保存的节点
                            boolean isResourceIdExit = instanceIdSet.contains(item.getResourceID());
                            if (isResourceIdExit) {
                                continue;
                            }
                            updateVNCNodeInfo(hpcClusterResource, item);
                            instanceIdSet.add(item.getResourceID());
                            break;
                        }
                    }
                }
            }
            else {
                if (CollectionUtil.isNotEmpty(computeNodeInfo)) {
                    for (HPCComputeNodeInfoVO item : computeNodeInfo) {
                        //更新未保存的节点
                        boolean isResourceIdExit = instanceIdSet.contains(item.getResourceID());
                        if (isResourceIdExit) {
                            continue;
                        }
                        if (Objects.equals(hpcClusterResource.getResourceType(), ServiceManage.ECS)) {
                            ResVm resVm = resVmMapper.selectByPrimaryKey(
                                    String.valueOf(hpcClusterResource.getResourceId()));
                            if (resVm != null) {
                                resVm.setInstanceId(item.getResourceID());
                                resVm.setInnerIp(item.getVIP());
                                resVm.setHostName(item.getHostName());
                                log.debug("HPC专属资源池变更配置-upgradeHpcDrp-计算节点 ECS更新：[{}]", JSONUtil.toJsonStr(resVm));
                                resVmMapper.updateByPrimaryKey(resVm);
                            }
                        } else if (Objects.equals(hpcClusterResource.getResourceType(), ServiceManage.BMS)) {
                            ResBms resBms = resBmsMapper.selectByPrimaryKey(
                                    String.valueOf(hpcClusterResource.getResourceId()));
                            log.debug("HPC专属资源池变更配置-upgradeHpcDrp-计算节点 BMS：[{}]", resBms);
                            if (resBms != null) {
                                resBms.setInstanceId(item.getResourceID());
                                resBms.setInnerIp(item.getVIP());
                                resBms.setHostName(item.getHostName());
                                log.debug("HPC专属资源池变更配置-upgradeHpcDrp-计算节点 BMS更新：[{}]", JSONUtil.toJsonStr(resBms));
                                resBmsMapper.updateByPrimaryKey(resBms);
                            }
                        }
                        hpcClusterResource.setStorageControlSubnet(item.getStorageControlSubNet());
                        hpcClusterResource.setStorageSubnet(item.getStorageSubNet());
                        hpcClusterResource.setStorageInterManagerIp(item.getStorageInterManagerIP());
                        hpcClusterResource.setExtraInfo(item.getExtraInfo());
                        hpcClusterResource.setNodeStatus(item.getNodeStatus());
                        resHpcClusterResourceMapper.updateByPrimaryKey(hpcClusterResource);
                        instanceIdSet.add(item.getResourceID());
                        break;
                    }
                }
            }
        }

        //获取新增节点信息
        hpcClusterResourceList.stream().forEach(res -> {
            String resourceType = res.getResourceType();
            if (ServiceManage.BMS.equals(resourceType)) {
                ResBmsNodeInfo bmsNodeInfo = new ResBmsNodeInfo();
                BeanUtils.copyProperties(res, bmsNodeInfo);
                ResBms resBms = resBmsMapper.selectByPrimaryKey(res.getResourceId().toString());
                if (resBms == null) {
                    return;
                }
                BeanUtils.copyProperties(resBms, bmsNodeInfo);
                bmsNodeInfo.setTypeName(resBms.getInstanceType());
                bmsNodeInfo.setId(resBms.getId());
                newNodeInfoList.add(bmsNodeInfo);

            } else if (ServiceManage.ECS.equals(resourceType)) {
                ResVmNodeInfo vmNodeInfo = new ResVmNodeInfo();
                BeanUtils.copyProperties(res, vmNodeInfo);
                ResVm resVm = resVmMapper.selectByPrimaryKey(res.getResourceId().toString());
                if (resVm == null) {
                    return;
                }
                BeanUtils.copyProperties(resVm, vmNodeInfo);
                vmNodeInfo.setTypeName(resVm.getInstanceType());
                vmNodeInfo.setId(resVm.getId());
                newNodeInfoList.add(vmNodeInfo);
            }
        });

        resHpcCluster.setProessPhase(CcmTaskStatusEnum.MANUAL_MOUNT_STORAGE.getValue());
        resHpcCluster.setProessStatus(CcmTaskStatusEnum.WAITING.getValue());
        resHpcCluster.setErrorInfo(StringUtils.EMPTY);;
        log.debug("HPC专属资源池变更配置-upgradeHpcDrp-专属集群 更新：[{}]", JSONUtil.toJsonStr(resHpcCluster));

        int count = resHpcClusterMapper.updateByPrimaryKeySelective(resHpcCluster);
        log.info("HPC专属资源池变更配置-upgradeHpcDrp-专属集群 更新条数：[{}]", count);

        //更新资源变更记录
        upadetResChangeRecord(clusterId, current);
        return newNodeInfoList;
    }

    /**
     * HPC专属资源池生成计算节点excel
     *
     * @param clusterId
     */
    @Override
    @Transactional
    public void hpcDrpGenerateClusterFile(Long clusterId) {
        ResHpcCluster resHpcCluster = resHpcClusterMapper.selectByPrimaryKey(clusterId);
        // 查找接口调用 HPC资源池详情
        HPCClusterInfoIDResult hpcClusterInfoIDResult = getHPCClusterInfoById(resHpcCluster.getId());
        HPCClusterInfoVO hpcClusterInfo = hpcClusterInfoIDResult.getHPCClusterInfo();
        //更新计算节点
        List<HPCComputeNodeInfoVO> computeNodeInfo = hpcClusterInfo.getComputeNodeInfo();
        // 计算节点保存Excel
        if (CollectionUtil.isNotEmpty(computeNodeInfo)) {
            log.info("hpcDrpGenerateClusterFile=====> 生成计算节点文档");
            resHpcCluster.setClusterFile(saveHpcDrpExcel(
                    resHpcCluster.getId(), BeanConvertUtil.convert(computeNodeInfo, HPCComputeExcel.class)));
            int count = resHpcClusterMapper.updateByPrimaryKeySelective(resHpcCluster);
        }
    }

    /**
     * 更新资源变更记录
     *
     * @param clusterId
     * @param current
     */
    private void upadetResChangeRecord(Long clusterId, Date current) {
        ResHpcClusterResourceExample hpcClusterResourceExample = new ResHpcClusterResourceExample();
        hpcClusterResourceExample.createCriteria().andClusterIdEqualTo(clusterId);
        List<ResHpcClusterResource> resHpcClusterResources = resHpcClusterResourceMapper.selectByExample(
                hpcClusterResourceExample);

        Criteria criteria = new Criteria();
        criteria.put("resourceId", clusterId);
        criteria.put("resType", HPC_DRP);
        criteria.setOrderByClause(" id desc ");
        List<ResChangeRecord> resChangeRecords = resChangeRecordMapper.selectByParams(criteria);
        long newNodeNum = resHpcClusterResources.stream()
                                                .filter(resource -> StringUtils.isNotEmpty(resource.getNodeType()))
                                                .count();
        Optional<ResChangeRecord> recordOptional = resChangeRecords.stream()
                                                                   .filter(record -> StringUtils.isEmpty(
                                                                           record.getNewExtra())
                                                                           && Long.valueOf(record.getOriginalType())
                                                                           < newNodeNum).findFirst();
        if (recordOptional.isPresent()) {
            ResChangeRecord resChangeRecord = recordOptional.get();
            resChangeRecord.setNewType(String.valueOf(newNodeNum));
            resChangeRecord.setUpdatedDt(current);
            String newExtraJson = getExtraJson(resHpcClusterResources);
            resChangeRecord.setNewExtra(newExtraJson);

            resChangeRecordMapper.updateByPrimaryKey(resChangeRecord);
        }
    }


    /**
     * 更新VNC节点信息
     *
     * @param hpcClusterResource
     * @param item
     */
    private void updateVNCNodeInfo(ResHpcClusterResource hpcClusterResource, HPCVNCNodeInfoVO item) {
        if (Objects.equals(hpcClusterResource.getHpcPointType(), HPC_POINT_TYPE_VNC)) {

            log.debug("HPC专属资源池更新VNC节点-updateVNCNodeInfo-HPCVNCNode：[{}]", JSONUtil.toJsonStr(item));
            if (Objects.equals(hpcClusterResource.getResourceType(), ServiceManage.ECS)) {
                ResVm resVm = resVmMapper.selectByPrimaryKey(
                        String.valueOf(hpcClusterResource.getResourceId()));
                log.debug("HPC专属资源池更新VNC节点-updateVNCNodeInfo-ResVm：[{}]", JSONUtil.toJsonStr(resVm));
                if (resVm != null) {
                    resVm.setInstanceId(item.getResourceID());
                    resVm.setInnerIp(item.getVip());
                    resVm.setHostName(item.getHostName());
                    //log.debug("HPC专属资源池更新VNC节点-updateVNCNodeInfo-VNC节点 ECS更新：[{}]", JSONUtil.toJsonStr(resVm));
                    resVmMapper.updateByPrimaryKey(resVm);
                }
            } else if (Objects.equals(hpcClusterResource.getResourceType(), ServiceManage.BMS)) {
                ResBms resBms = resBmsMapper.selectByPrimaryKey(
                        String.valueOf(hpcClusterResource.getResourceId()));
                if (resBms != null) {
                    resBms.setInstanceId(item.getResourceID());
                    resBms.setInnerIp(item.getVip());
                    resBms.setHostName(item.getHostName());
                    ;
                    log.debug("HPC专属资源池更新VNC节点-updateVNCNodeInfo-VNC节点 BMS更新：[{}]", JSONUtil.toJsonStr(resBms));
                    resBmsMapper.updateByPrimaryKey(resBms);
                }
            }
            log.info("HPC专属资源池更新VNC节点-updateVNCNodeInfo-update");
            hpcClusterResource.setExtraInfo(item.getExtraInfo());
            hpcClusterResource.setNodeStatus(item.getNodeStatus());
            resHpcClusterResourceMapper.updateByPrimaryKey(hpcClusterResource);
            log.info("HPC专属资源池更新VNC节点-updateVNCNodeInfo-updated");
        }
    }

    private Set<String> getInstanceIdSet(Long clusterId) {
        ResHpcClusterResourceExample hpcClusterResourceExample = new ResHpcClusterResourceExample();
        hpcClusterResourceExample.createCriteria().andClusterIdEqualTo(clusterId);
        List<ResHpcClusterResource> resHpcClusterResources = resHpcClusterResourceMapper.selectByExample(
                hpcClusterResourceExample);

        Set<String> instanceIdSet = new HashSet<>();
        //BMS 集群关系MAP
        List<Long> bmsIdList = resHpcClusterResources.stream()
                                                     .filter(res -> ServiceManage.BMS.equalsIgnoreCase(
                                                             res.getResourceType()))
                                                     .map(ResHpcClusterResource::getResourceId)
                                                     .collect(Collectors.toList());
        //查询BMS资源MAP
        Criteria resBmCri = new Criteria();
        resBmCri.put("resBmsIds", bmsIdList);
        List<ResBms> resBmsList = resBmsMapper.selectByParams(resBmCri);
        Set<String> bmsInstanceIdSet = resBmsList.stream()
                                                 .filter(res -> StringUtils.isNotEmpty(res.getInstanceId()))
                                                 .map(ResBms::getInstanceId).collect(Collectors.toSet());
        instanceIdSet.addAll(bmsInstanceIdSet);

        //ECS 集群
        List<Long> ecsIdLisst = resHpcClusterResources.stream()
                                                      .filter(res -> ServiceManage.ECS.equalsIgnoreCase(
                                                              res.getResourceType()))
                                                      .map(ResHpcClusterResource::getResourceId)
                                                      .collect(Collectors.toList());
        //查询ECS资源MAP
        Criteria resVmCri = new Criteria();
        resVmCri.put("resVmIds", ecsIdLisst);
        List<ResVm> resEcsList = resVmMapper.selectByCriteria(resVmCri);
        Set<String> ecsInstanceIdSet = resEcsList.stream()
                                                 .filter(res -> StringUtils.isNotEmpty(res.getInstanceId()))
                                                 .map(ResVm::getInstanceId)
                                                 .collect(Collectors.toSet());
        instanceIdSet.addAll(ecsInstanceIdSet);
        return instanceIdSet;
    }

    /**
     * @param clusterId clusterId
     * @param computeList 计算机节点集合
     * @param action 动作：OpenOrClosed 开启或关闭；Remove：删除节点
     *
     * @return boolean
     *
     * @throws
     * @title 操作HPC专属资源池节点
     * @Description
     * <AUTHOR>
     * @CreateTime 2022/5/12 9:56
     */
    @Override
    @Transactional
    public Boolean operateNode(Long clusterId, List<String> computeList, List<String> managerList, String action,boolean operateAdmin) {
        Boolean result = Boolean.TRUE;
        //查询计算节点信息
        List<HpcNodeStatusDTO> operateNodeList = new ArrayList<>();
        ResHpcCluster resHpcCluster = resHpcClusterMapper.selectByPrimaryKey(clusterId);
        long removeComputeNum = 0, removeCliNum = 0, totalComputeNum = 0, totalCliNum = 0;
        if (CollectionUtil.isNotEmpty(computeList)) {
            List<HpcNodeStatusDTO> computes = resBmsMapper.selectComputeListByInstanceIds(clusterId, computeList);
            if (computes !=null && computeList.size() != computes.size()) {
                throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
            }
            // 查询该资源池下所有的计算节点
            if (Objects.equals(action, "Remove")) {
                // 如果是删除节点，查询计算节点是否有开启状态，如果有开启状态，直接返回参数错误
                List<HpcNodeStatusDTO> okeyNodeList = computes.stream()
                        .filter(nodestatus -> StringUtils.equalsIgnoreCase(nodestatus.getStatus(), ResHpcClusterNodeStatus.OKEY))
                        .collect(Collectors.toList());
                //非运营管理员
                if(!operateAdmin && okeyNodeList.size()>0){
                    throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
                }
                //运营管理员先非关闭的正常节点
                if(operateAdmin && okeyNodeList.size()>0){
                    Map<String, HpcNodeStatusDTO> dtoMap = new HashMap<>(16);
                    for (HpcNodeStatusDTO hpcNodeStatusDTO : okeyNodeList) {
                        dtoMap.put(hpcNodeStatusDTO.getHostName(), hpcNodeStatusDTO);
                    }
                    List<String> hostNameList = okeyNodeList.stream().filter(statusDto -> Objects.nonNull(statusDto.getHostName())).map(HpcNodeStatusDTO::getHostName).collect(Collectors.toList());
                    changeNodeStatus("OpenOrClosed", resHpcCluster, dtoMap, ResHpcClusterNodeStatus.OKEY, hostNameList);
                }

                List<HpcNodeStatusDTO> allCompute = resBmsMapper.selectComputeListByInstanceIds(clusterId, null);
                totalComputeNum = allCompute == null ? 0 : allCompute.size();
                removeComputeNum = computes == null ? 0 : computes.size();
            }
            operateNodeList.addAll(computes);
        }
        // 登录节点只允许删除，不允许修改状态
        if (Objects.equals(action, "Remove")) {
            if (CollectionUtil.isNotEmpty(managerList)) {
                List<HpcNodeStatusDTO> managers = resBmsMapper.selectManagerListByInstanceIds(clusterId, managerList);
                if (managers.size() != managerList.size()) {
                    throw new BizException(WebUtil.getMessage(MsgCd.PARAM_NOT_VALID_ERROR));
                }

                //缩容登陆节点，res_hpc_cluster中login_node_internal_address 字段中的IP地址如果小于等于1，提示登录节点至少有一个
                // 缩容扩容的状态是CCM激活失败的时候，此时登录节点信息没写入cluster表
                String proessPhase = resHpcCluster.getProessPhase();
                String status = resHpcCluster.getProessStatus();
                log.info("-------------------------> operateNode proessPhase: {}, status: {}", proessPhase, status);
                removeCliNum = managers.stream().filter(manage -> StringUtils.equalsIgnoreCase(manage.getHpcPointType(), HPC_POINT_TYPE_CLI)).count();

                if (!((CcmTaskStatusEnum.CONTINUE_UPGRADE_SHRINKAGE.getValue().equals(proessPhase) || CcmTaskStatusEnum.ACTIVATE_STATUS.getValue().equals(proessPhase)) &&
                        CcmTaskStatusEnum.FAILED.getValue().equals(status))) {
                    if (removeCliNum > 0L) {
                        if (Objects.nonNull(resHpcCluster.getLoginNodeInternalAddress())) {
                            int length = resHpcCluster.getLoginNodeInternalAddress().split(",").length;
                            if (length <= 1) {
                                throw new BizException(WebUtil.getMessage(MsgCd.DELETE_HPC_CLI_NODE_ERROR));
                            }
                        } else {
                            throw new BizException(WebUtil.getMessage(MsgCd.DELETE_HPC_CLI_NODE_ERROR));
                        }
                    }
                }


                List<HpcNodeStatusDTO> allManager = resBmsMapper.selectManagerListByInstanceIds(clusterId, null);

                totalCliNum = allManager.stream().filter(manage -> StringUtils.equalsIgnoreCase(manage.getHpcPointType(), HPC_POINT_TYPE_CLI)).count();

                operateNodeList.addAll(managers);
            }
        }
        // 验证删除节点数量是否合理
        if (removeComputeNum > 0L || removeCliNum > 0L) {
            if (removeComputeNum > 0L && removeComputeNum == totalComputeNum &&
                    removeCliNum > 0L && removeCliNum == totalCliNum) {
                throw new BizException(WebUtil.getMessage(MsgCd.DELETE_HPC_COMPUTE_AND_CLI_NODE_ERROR));
            } else if (removeComputeNum > 0 && removeComputeNum == totalComputeNum) { //计算节点至少保留一个
                throw new BizException(WebUtil.getMessage(MsgCd.DELETE_HPC_COMPUTE_NODE_ERROR));
            } else if (removeCliNum > 0 && removeCliNum == totalCliNum) { //登录节点至少保留一个
                throw new BizException(WebUtil.getMessage(MsgCd.DELETE_HPC_CLI_NODE_ERROR));
            }
        }


        Map<String, List<String>> collect = operateNodeList.stream()
                                                           .collect(Collectors.groupingBy(node->Optional.ofNullable(node.getStatus()).orElse("-"),
                                                                                          Collectors.mapping(
                                                                                                  HpcNodeStatusDTO::getHostName,
                                                                                                  Collectors.toList()))
                                                           );

        Map<String, HpcNodeStatusDTO> dtoMap = new HashMap<>(16);
        for (HpcNodeStatusDTO hpcNodeStatusDTO : operateNodeList) {
            dtoMap.put(hpcNodeStatusDTO.getHostName(), hpcNodeStatusDTO);
        }
        if (!Objects.equals(action, "Remove")) {
            collect.forEach((key, value) -> {
                //无状态的不进行操作
                if("-".equals(key)){
                    return;
                }
                changeNodeStatus(action, resHpcCluster, dtoMap, key, value);
            });
        } else {
            List<String> hostNameList = operateNodeList.stream().map(HpcNodeStatusDTO::getHostName).collect(Collectors.toList());
            log.info("操作HPC专属资源池节点-operateNode-删除缩容节点的名称,{}",hostNameList);
            rpcDeleteNode(resHpcCluster, dtoMap, hostNameList,operateAdmin);
        }
        return result;
    }

    @Override
    public String getNodeInfo(Long clusterId) {
        ResHpcCluster resHpcCluster = resHpcClusterMapper.selectByPrimaryKey(clusterId);
        List<GraphicNodeInfo> vncNodeInfoList=new ArrayList<>();
        return getNodeInfo(resHpcCluster,vncNodeInfoList);
    }

    @Override
    public HPCNodeDifferVo queryHPCNodeDiffer(Long clusterId) {
        // 获取集群信息
        // 查找接口调用 HPC资源池详情
        HPCClusterInfoIDResult hpcClusterInfoIDResult = getHPCClusterInfoById(clusterId);
        HPCClusterInfoVO hpcClusterInfo = hpcClusterInfoIDResult.getHPCClusterInfo();

        // 获取节点信息
        Criteria criteria = new Criteria();
        criteria.put("clusterId", clusterId);
        List<HPCNodeInfoMemberVo> resList = resHpcClusterMapper.getClusterByIdAndType(criteria);

        HPCNodeDifferVo vo = new HPCNodeDifferVo();
        vo.setClusterId(clusterId);

        Set<String> allDifferNodeInstanceIds = new HashSet<>();

        //计算节点
        List<HPCComputeNodeInfoVO> computeNodeInfo = hpcClusterInfo.getComputeNodeInfo();
        if (CollectionUtil.isNotEmpty(computeNodeInfo)) {
            List<String> computeList = computeNodeInfo.stream().filter(e -> e.getNodeStatus().equals(HPCComputeNodeInfoVO.NodeStatusActiveActiveFailed))
                    .map(HPCComputeNodeInfoVO::getResourceID).collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(computeList)) {
                vo.setComputeList(computeList);
                allDifferNodeInstanceIds.addAll(computeList);
            }
        }

        //管理节点
        List<HPCManagementNodeInfoVO> managementNodeInfos = hpcClusterInfo.getManagementNodeInfo();
        List<String> managementResourceIDs = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(managementNodeInfos)) {
            managementResourceIDs = managementNodeInfos.stream().filter(e -> e.getNodeStatus().equals(HPCComputeNodeInfoVO.NodeStatusActiveActiveFailed))
                    .map(HPCManagementNodeInfoVO::getResourceID).collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(managementResourceIDs)) {
                allDifferNodeInstanceIds.addAll(managementResourceIDs);
            }
        }

        //VNC节点
        List<HPCVNCNodeInfoVO> vncNodeInfoList = hpcClusterInfo.getVncNodeInfo();
        if (CollectionUtil.isNotEmpty(vncNodeInfoList)) {
            List<String> vncResourceIDs = vncNodeInfoList.stream().filter(e -> e.getNodeStatus().equals(HPCComputeNodeInfoVO.NodeStatusActiveActiveFailed))
                    .map(HPCVNCNodeInfoVO::getResourceID).collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(vncResourceIDs)) {
                allDifferNodeInstanceIds.addAll(vncResourceIDs);
                managementResourceIDs.addAll(vncResourceIDs);
            }
        }
        vo.setManagerList(managementResourceIDs);

        Integer agentNum = 0;
        Integer vncNum = 0;
        Integer managerNum = 0;
        if (allDifferNodeInstanceIds.size() > 0) {
            resList = resList.stream().filter(e -> allDifferNodeInstanceIds.contains(e.getInstanceId())).collect(Collectors.toList());
            for (HPCNodeInfoMemberVo hpcNodeInfo : resList) {
                String nodeType = hpcNodeInfo.getNodeType();
                if (VNC_LOWER.equals(nodeType)) {
                    vncNum++;
                } else if (COMPUTE.equals(nodeType)) {
                    agentNum++;
                } else {
                    managerNum++;
                }
            }
        }

        vo.setAgentNum(agentNum);
        vo.setVncNum(vncNum);
        vo.setManagerNum(managerNum);

        return vo;
    }

    /**
     * 删除节点
     *
     * @param resHpcCluster
     * @param dtoMap
     * @param value
     * @param operateAdmin
     */
    private void rpcDeleteNode(ResHpcCluster resHpcCluster, Map<String, HpcNodeStatusDTO> dtoMap, List<String> value, boolean operateAdmin) {
        HPCRemoveNode hpcRemoveNode = CloudClientFactory.buildMQBean(resHpcCluster.getCloudEnvId(),
                HPCRemoveNode.class);
        hpcRemoveNode.setHpcClusterID(resHpcCluster.getResourceId());
        hpcRemoveNode.setNodeName(value);
        hpcRemoveNode.setApplyTaskID(new IdWorker().nextId() + "");
        hpcRemoveNode.setForce(Boolean.TRUE);
        hpcRemoveNode.setApplyTaskDescription("删除HPC专属资源池节点");

        cn.com.cloudstar.rightcloud.basic.data.pojo.user.User user = systemMUserMapper.selectByPrimaryKey(resHpcCluster.getOwnerId());
        hpcRemoveNode.setTenantName(user.getAccount());
        String password = sysHpcPassRemoteService.findHpcPass(resHpcCluster.getOwnerId());
        hpcRemoveNode.setTenantPassword(password);
        HPCRemoveNodeResult hpcRemoveNodeResult = null;
        try {
            hpcRemoveNodeResult = (HPCRemoveNodeResult) MQHelper.rpc(hpcRemoveNode);
            if (hpcRemoveNodeResult.isSuccess()) {
                String taskID = hpcRemoveNodeResult.getTaskID();
                log.info("HPCServiceImpl.rpcDeleteNode 删除节点任务ID：{}", taskID);

                //根据资源类型分组,修改节点未删除中
                Collection<HpcNodeStatusDTO> hpcNodeStatusDTOList = dtoMap.values();
                Map<String, List<HpcNodeStatusDTO>> resourceTypeNodeMap =
                        hpcNodeStatusDTOList.stream().collect(Collectors.groupingBy(HpcNodeStatusDTO::getResourceType));

                String proessPhase = resHpcCluster.getProessPhase();
                String status = resHpcCluster.getProessStatus();
                if (!((CcmTaskStatusEnum.CONTINUE_UPGRADE_SHRINKAGE.getValue().equals(proessPhase) || CcmTaskStatusEnum.ACTIVATE_STATUS.getValue().equals(proessPhase)) &&
                        CcmTaskStatusEnum.FAILED.getValue().equals(status))) {
                    List<String> innerIpList = hpcNodeStatusDTOList.stream()
                            .filter(hpcNode -> HPC_POINT_TYPE_CLI.equals(hpcNode.getHpcPointType()))
                            .map(HpcNodeStatusDTO::getInnerIp)
                            .collect(Collectors.toList());
                    List<String> publicIpList = hpcNodeStatusDTOList.stream()
                            .filter(hpcNode -> HPC_POINT_TYPE_CLI.equals(hpcNode.getHpcPointType()))
                            .map(HpcNodeStatusDTO::getPublicIp)
                            .collect(Collectors.toList());

                    String loginNodeExternalAddress = resHpcCluster.getLoginNodeExternalAddress();
                    if(StringUtils.isNotEmpty(loginNodeExternalAddress) && CollectionUtil.isNotEmpty(publicIpList)){
                        loginNodeExternalAddress = addressFilter( publicIpList, loginNodeExternalAddress);
                        resHpcCluster.setLoginNodeExternalAddress(loginNodeExternalAddress);
                    }
                    String loginNodeInternalAddress = resHpcCluster.getLoginNodeInternalAddress();
                    if(StringUtils.isNotEmpty(loginNodeInternalAddress) && CollectionUtil.isNotEmpty(innerIpList)){
                        loginNodeInternalAddress = addressFilter( innerIpList, loginNodeInternalAddress);
                        resHpcCluster.setLoginNodeInternalAddress(loginNodeInternalAddress);
                    }
                }

                //修改节点状态为删除中
                resourceTypeNodeMap.forEach((resourceType,nodeDtoList)-> {
                    List<Long> resIdList = nodeDtoList.stream().map(HpcNodeStatusDTO::getId).collect(Collectors.toList());
                    if (StringUtils.equalsIgnoreCase(resourceType, ServiceManage.ECS) && CollectionUtil.isNotEmpty(resIdList)) {
                        ResVm resVm = new ResVm();
                        resVm.setStatus(ResHpcClusterNodeStatus.REMOVING);
                        resVmMapper.updateByExampleSelective(resVm, MapUtil.of("resVmIds",resIdList));
                    }
                    if (StringUtils.equalsIgnoreCase(resourceType, ServiceManage.BMS)  && CollectionUtil.isNotEmpty(resIdList)) {
                        resBmsMapper.batchUpdateStatus(resIdList,ResHpcClusterNodeStatus.REMOVING);



                    }
                    for (Long resourceId : resIdList) {
                        this.insertDeleteNodeTask(resHpcCluster, operateAdmin, taskID,resourceType,resourceId);
                    }

                    // if (CollectionUtils.isNotEmpty(resIdList)) {
                    //     ResHpcClusterResourceExample resourceExample = new ResHpcClusterResourceExample();
                    //     resourceExample.createCriteria().andResourceIdIn(resIdList).andResourceTypeEqualTo(resourceType);
                    //     resHpcClusterResourceMapper.deleteByExample(resourceExample);
                    // }
                });
                resHpcClusterMapper.updateByPrimaryKeySelective(resHpcCluster);
            } else {
                throw new BizException(hpcRemoveNodeResult.getErrMsg());
            }
        } catch (Exception e) {
            log.error(" MQHelper.rpc  hpcRemoveNodeResult=", hpcRemoveNodeResult);
            throw new RuntimeException(e);
        }
    }

    private void insertDeleteNodeTask(ResHpcCluster resHpcCluster, boolean operateAdmin, String taskID, String resourceType, Long resourceId) {
        //插入删除节点任务
        ResHpcClusterDeleteNodeTask resHpcClusterDeleteNodeTask = new ResHpcClusterDeleteNodeTask();
        resHpcClusterDeleteNodeTask.setClusterId(resHpcCluster.getId());
        resHpcClusterDeleteNodeTask.setTaskId(taskID);
        resHpcClusterDeleteNodeTask.setAdminOperation(operateAdmin);
        resHpcClusterDeleteNodeTask.setCloudEnvId(resHpcCluster.getCloudEnvId());
        resHpcClusterDeleteNodeTask.setResourceType(resourceType);
        resHpcClusterDeleteNodeTask.setResourceId(resourceId);
        resHpcClusterDeleteNodeTask.setCreatedDt(new Date());
        resHpcClusterDeleteNodeTaskMapper.insert(resHpcClusterDeleteNodeTask);
    }

    /**
     * 剔除待删除的登录节点IP
     *
     * @param ipList
     * @param address
     * @return
     */
    private String addressFilter(List<String> ipList, String address) {
        StringBuffer sb = new StringBuffer();
        String[] split = address.split(StrUtil.COMMA);
        if(split.length>1){
            for (String ipPort : split) {
                String ip = ipPort.split(StrUtil.COLON)[0];
                if(!ipList.contains(ip)){
                    if (sb.length()==0) {
                        sb.append(ipPort);
                    }else{
                        sb.append(StrUtil.COMMA).append(ipPort);
                    }
                }
            }
        }
        return sb.toString();
    }

    /**
     * 开关节点
     *
     * @param action
     * @param resHpcCluster
     * @param dtoMap
     * @param key
     * @param value
     */
    private void changeNodeStatus(String action, ResHpcCluster resHpcCluster, Map<String, HpcNodeStatusDTO> dtoMap, String key, List<String> value) {
        HPCNodeOperate hpcNodeOperate = CloudClientFactory.buildMQBean(resHpcCluster.getCloudEnvId(),
                HPCNodeOperate.class);
        hpcNodeOperate.setHpcClusterID(resHpcCluster.getResourceId());
        hpcNodeOperate.setNodeName(value);
        hpcNodeOperate.setApplyTaskID(new IdWorker().nextId() + "");
        hpcNodeOperate.setForcibly("");
        hpcNodeOperate.setApplyTaskDescription("关闭HPC专属资源池节点");
        hpcNodeOperate.setAction("Close");

        log.info("HPCServiceImpl.changeNodeStatus key：{}", key);
        if (!key.contains( ResHpcClusterNodeStatus.OK)) {
            hpcNodeOperate.setApplyTaskDescription("开启HPC专属资源池节点");
            hpcNodeOperate.setAction("Open");
        }

        cn.com.cloudstar.rightcloud.basic.data.pojo.user.User user = systemMUserMapper.selectByPrimaryKey(resHpcCluster.getOwnerId());
        hpcNodeOperate.setTenantName(user.getAccount());

        String password = sysHpcPassRemoteService.findHpcPass(resHpcCluster.getOwnerId());
        hpcNodeOperate.setTenantPassword(password);
        HPCNodeOperateResult hpCNodeOperationResult;
        try {
            log.info("HPCServiceImpl.changeNodeStatus 节点状态修改 请求参数：{}", JSON.toJSONString(hpcNodeOperate));
            hpCNodeOperationResult = (HPCNodeOperateResult) MQHelper.rpc(hpcNodeOperate);
            log.info("HPCServiceImpl.changeNodeStatus 节点状态修改 返回信息：{}", JSON.toJSONString(hpCNodeOperationResult));
            if (hpCNodeOperationResult.isSuccess()) {
                //修改节点状态
                List<FDNodeOperateMemberResult> members = hpCNodeOperationResult.getMembers();
                List<Long> computerIds = new ArrayList<>();
                List<Long> managerIds = new ArrayList<>();
                if(CollectionUtil.isNotEmpty(members)){
                    for (FDNodeOperateMemberResult member : members) {
                        HpcNodeStatusDTO hpcNodeStatusDTO = dtoMap.get(member.getNodeName());
                        if (hpcNodeStatusDTO != null) {
                            if (Objects.equals(hpcNodeStatusDTO.getNodeType(), COMPUTE)) {
                                computerIds.add(hpcNodeStatusDTO.getId());
                            } else {
                                managerIds.add(hpcNodeStatusDTO.getId());
                            }
                        }
                    }
                }
                String nodeStatus = hpcNodeOperate.getAction();
                nodeStatus = StringUtils.equalsIgnoreCase(nodeStatus,"Open")?ResHpcClusterNodeStatus.OKEY:ResHpcClusterNodeStatus.CLOSED;
                if (CollectionUtils.isNotEmpty(computerIds)) {
                    resBmsMapper.batchUpdateStatus(computerIds, nodeStatus);
                }
                if (CollectionUtils.isNotEmpty(managerIds)) {
                    resVmMapper.batchUpdateStatus(managerIds, nodeStatus);
                }

            } else {
                log.error(" MQHelper.rpc  hpCNodeOperationResult:", hpCNodeOperationResult);
                throw new BizException(hpCNodeOperationResult.getErrMsg());
            }
        } catch (Exception e) {
            log.error(" MQHelper.rpc  hpCNodeOperationResult: ",e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public HpcNodeInfoVo queryHPCNodeInfo(Long clusterId, List<String> computeList, List<String> managerList) {
        HpcNodeInfoVo hpcNodeInfoVo = new HpcNodeInfoVo();
        ResHpcCluster resHpcCluster = resHpcClusterMapper.selectByPrimaryKey(clusterId);



        HPCNodeJobInfo hpcNodeJobInfo = CloudClientFactory.buildMQBean(resHpcCluster.getCloudEnvId(),
                                                                       HPCNodeJobInfo.class);

        cn.com.cloudstar.rightcloud.basic.data.pojo.user.User user = systemMUserMapper.selectByPrimaryKey(resHpcCluster.getOwnerId());
        hpcNodeJobInfo.setTenantName(user.getAccount());

        String password = sysHpcPassRemoteService.findHpcPass(user.getUserSid());
        hpcNodeJobInfo.setTenantPassword(password);
        hpcNodeJobInfo.setHpcClusterID(resHpcCluster.getResourceId());
        List<HPCNodeInfoMemberVo> results = new ArrayList<>();
        HPCNodeJobInfoResult result;
        try {
            result = (HPCNodeJobInfoResult) MQHelper.rpc(hpcNodeJobInfo);
            if (result.isSuccess()) {
                List<FDNodeJobMemberResult> members = result.getMembers();
                for (FDNodeJobMemberResult member : members) {
                    for (String s : computeList) {
                        if (Objects.equals(member.getResourceID(), s)) {
                            HPCNodeInfoMemberVo hpcNodeInfoMemberResult = new HPCNodeInfoMemberVo();
                            hpcNodeInfoMemberResult.setTaskCount(member.getTaskCount());
                            hpcNodeInfoMemberResult.setStatus(member.getStatus());
                            results.add(hpcNodeInfoMemberResult);
                        }
                    }
                }
                hpcNodeInfoVo.setMembers(results);
            } else {
                throw new BizException("获取节点信息失败");
            }
        } catch (MQException e) {
            log.error("获取节点信息失败", e);
            throw new BizException("获取节点信息失败");
        }
        return hpcNodeInfoVo;
    }

    @Override
    public BaseGridReturn queryHPCNodeInfoByClusterId(HPCNodeInfoRequest request) {
        ResHpcCluster resHpcCluster = resHpcClusterMapper.selectByPrimaryKey(request.getClusterId());
        if (ResHpcClusterStatus.APPLY.equals(resHpcCluster.getStatus())) {
            return new BaseGridReturn();
        }
        Criteria criteria = new Criteria();
        BasicWebUtil.preparePageParams(request, criteria, null);
        criteria.put("resourceType", request.getNodeType());
        criteria.put("clusterId", request.getClusterId());
        String orderBy = "node_type,hpc_point_type asc,FIELD(`hpc_point_type`,\"CCS_CLI\",\"VNC\",\"CCS_MASTER\",\"CCS_DB\",\"CCP_MASTER\",\"CCP_DB\")";
        Page<Object> page = PageHelper.startPage(criteria.getPageNum(), criteria.getPageSize());
        page.setUnsafeOrderBy(orderBy);

        List<HPCNodeInfoMemberVo> resList = resHpcClusterMapper.getClusterByIdAndType(criteria);
        log.info("查询节点信息-HPCServiceImpl-queryHPCNodeInfoByClusterId-resList:[{}]",resList);
        //节点删除中的集群不修改状态
         int countTaskByClusterId = resHpcClusterDeleteNodeTaskMapper.countTaskByClusterId(request.getClusterId());
        log.info("查询节点信息-HPCServiceImpl-queryHPCNodeInfoByClusterId-countTaskByClusterId:[{}]",countTaskByClusterId);
        if (countTaskByClusterId > 0) {
            return new BaseGridReturn(resList);
        }

        HPCNodeJobInfo hpcNodeJobInfo = CloudClientFactory.buildMQBean(resHpcCluster.getCloudEnvId(),
                                                                       HPCNodeJobInfo.class);
        cn.com.cloudstar.rightcloud.basic.data.pojo.user.User user = systemMUserMapper.selectByPrimaryKey(resHpcCluster.getOwnerId());
        hpcNodeJobInfo.setTenantName(user.getAccount());

        String password = sysHpcPassRemoteService.findHpcPass(user.getUserSid());
        hpcNodeJobInfo.setTenantPassword(password);
        hpcNodeJobInfo.setHpcClusterID(resHpcCluster.getResourceId());
        ArrayList<HpcNodeInfoMemberResult> results = new ArrayList<>();
        HPCNodeJobInfoResult result;
        try {
            result = (HPCNodeJobInfoResult) MQHelper.rpc(hpcNodeJobInfo);
            log.info("查询节点信息-HPCServiceImpl-queryHPCNodeInfoByClusterId-result:[{}]", result == null ? "null" : JSON.toJSONString(result));
            if (result != null && result.isSuccess()) {
                List<FDNodeJobMemberResult> members = result.getMembers();
                members.forEach(memberResult -> {
                    for (HPCNodeInfoMemberVo hpcNodeInfoMemberVo : resList) {
                        if (Objects.equals(memberResult.getResourceID(), hpcNodeInfoMemberVo.getInstanceId())) {
                            //删除中的节点不更新状态
                            if(!StringUtils.equalsIgnoreCase(ResHpcClusterNodeStatus.REMOVING,hpcNodeInfoMemberVo.getStatus())){
                                hpcNodeInfoMemberVo.setStatus(memberResult.getStatus());
                            }
                            hpcNodeInfoMemberVo.setTaskCount(memberResult.getTaskCount());
                        }
                    }
                });
                Map<String, List<HPCNodeInfoMemberVo>> collect = resList.stream()
                                                                        .filter(map -> StringUtils.isNotBlank(
                                                                                map.getNodeType()))
                                                                        .collect(Collectors.groupingBy(
                                                                                HPCNodeInfoMemberVo::getNodeType));

                List<HPCNodeInfoMemberVo> bmsDTOList = collect.get(COMPUTE);
                List<HPCNodeInfoMemberVo> ecsDTOList = collect.get(MANAGER);
                if (CollectionUtils.isNotEmpty(bmsDTOList)) {
                    List<HPCNodeInfoMemberVo> collect1 = bmsDTOList.stream()
                                                                   .filter(bms -> StringUtils.isNotEmpty(
                                                                           bms.getStatus()))
                                                                   .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(collect1)) {
                        Map<String, List<Long>> bmsMap = collect1.stream()
                                                                 .collect(Collectors.groupingBy(
                                                                         HPCNodeInfoMemberVo::getStatus,
                                                                         Collectors.mapping(
                                                                                 HPCNodeInfoMemberVo::getId,
                                                                                 Collectors.toList())));
                        bmsMap.forEach((key, value) -> {
                            resBmsMapper.batchUpdateStatus(value, key);
                        });
                    }
                }
                if (CollectionUtils.isNotEmpty(ecsDTOList)) {
                    if (CollectionUtils.isNotEmpty(collect.get(VNC_LOWER))) {
                        ecsDTOList.addAll(collect.get(VNC_LOWER));
                    }
                    List<HPCNodeInfoMemberVo> collect1 = ecsDTOList.stream()
                                                                   .filter(esc -> StringUtils.isNotEmpty(
                                                                           esc.getStatus()))
                                                                   .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(collect1)) {
                        Map<String, List<Long>> resMap = collect1.stream()
                                                                   .collect(Collectors.groupingBy(
                                                                           HPCNodeInfoMemberVo::getStatus,
                                                                           Collectors.mapping(
                                                                                   HPCNodeInfoMemberVo::getId,
                                                                                   Collectors.toList())));
                        //修改节点状态

                            resMap.forEach((key, value) -> {
                                resVdMapper.batchUpdateStatus(value, key);
                            });
                        }
                    }
                } else {
                    return new BaseGridReturn(resList);
                }
        } catch (Exception e) {
            log.error("获取节点信息失败", e);
        }
        return new BaseGridReturn(resList);
    }

    @Override
    public List<ResHpcCluster> selectByByPoolUuid(String poolUuid) {
        return resHpcClusterMapper.selectByByPoolUuid(poolUuid);
    }

    @Override
    public ResHpcCluster isUnfreezeByResShare(Long id) {
        ResHpcClusterResourceExample hpcClusterResourceExample = new ResHpcClusterResourceExample();
        hpcClusterResourceExample.createCriteria().andResourceIdEqualTo(id).andResourceTypeLike("SFS");
        List<ResHpcClusterResource> resHpcClusterResources = resHpcClusterResourceMapper.selectByExample(hpcClusterResourceExample);
        log.info("解冻弹性文件系统-isUnfreezeByResShare-resHpcClusterResources:[{}]", JSONUtil.toJsonStr(resHpcClusterResources));

        if (CollectionUtils.isEmpty(resHpcClusterResources)) {
            return null;
        }

        ResHpcCluster resHpcCluster = resHpcClusterMapper.selectByPrimaryKey(resHpcClusterResources.get(0).getClusterId());
        log.info("解冻弹性文件系统-isUnfreezeByResShare-resHpcCluster:[{}]", JSONUtil.toJsonStr(resHpcCluster));
        if (ResHpcClusterStatus.FROZEN.equals(resHpcCluster.getStatus())
                && Objects.nonNull(resHpcCluster.getEndTime())
                && resHpcCluster.getEndTime().after(new Date())) {
            hpcClusterResourceExample.clear();
            hpcClusterResourceExample.createCriteria().andClusterIdEqualTo(resHpcCluster.getId()).andResourceTypeLike("SFS");
            resHpcClusterResources = resHpcClusterResourceMapper.selectByExample(hpcClusterResourceExample);
            log.info("解冻弹性文件系统-isUnfreezeByResShare-resHpcClusterResources1:[{}]", JSONUtil.toJsonStr(resHpcClusterResources));

            boolean anyFreezeSfs = resHpcClusterResources.stream().anyMatch(resHpcClusterResource -> {
                ResShare resShare = resShareMapper.selectByPrimaryKey(resHpcClusterResource.getResourceId());
                log.info("解冻弹性文件系统-isUnfreezeByResShare-resShare.status:[{}]", resShare.getStatus());
                return "frozen".equals(resShare.getStatus());
            });
            log.info("解冻弹性文件系统-isUnfreezeByResShare-anyFreezeSfs:[{}]", anyFreezeSfs);

            // 只有HPC集群下没有冻结的弹性文件，才进行解冻
            if (!anyFreezeSfs) {
                resHpcCluster.setStatus(ResHpcClusterStatus.AVALIABLE);
                resHpcClusterMapper.updateByPrimaryKey(resHpcCluster);
                return resHpcCluster;
            }else {
                return null;
            }
        }
        return null;
    }

    @Override
    @Transactional
    public void clearRemovingNode(Long clusterId) {
        log.info("HPC专属资源池删除节点-HPCServiceImpl-clearRemovingNode-INPUT:[{clusterId:{}}]",clusterId);

        List<ResHpcClusterDeleteNodeTask> resHpcClusterDeleteNodeTasks = resHpcClusterDeleteNodeTaskMapper.selectDeleteNodeTaskByClusterId(clusterId);

        Map<String, List<ResHpcClusterDeleteNodeTask>> nodeTaskMap =
                resHpcClusterDeleteNodeTasks.stream().collect(Collectors.groupingBy(ResHpcClusterDeleteNodeTask::getResourceType));

        for (String resourceType : nodeTaskMap.keySet()) {

            List<ResHpcClusterDeleteNodeTask> clusterDeleteNodeTaskList = nodeTaskMap.get(resourceType);
            List<Long> resourceIdList = clusterDeleteNodeTaskList.stream().map(ResHpcClusterDeleteNodeTask::getResourceId).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(resourceIdList)) {
                if(StringUtils.equalsIgnoreCase(ServiceManage.BMS,resourceType)){
                    Criteria bmsCriteria = new Criteria();
                    bmsCriteria.put("resBmsIds",resourceIdList);
                    bmsCriteria.put("status",ResHpcClusterNodeStatus.REMOVING);
                    resBmsMapper.deleteByExample(bmsCriteria);

                    ResHpcClusterResourceExample resourceExample = new ResHpcClusterResourceExample();
                    resourceExample.createCriteria().andClusterIdEqualTo(clusterId).andResourceIdIn(resourceIdList).andResourceTypeEqualTo(ServiceManage.BMS);
                    resHpcClusterResourceMapper.deleteByExample(resourceExample);
                }
                if(StringUtils.equalsIgnoreCase(ServiceManage.ECS,resourceType)){
                    Criteria vmCriteria = new Criteria();
                    vmCriteria.put("resVmIds",resourceIdList);
                    vmCriteria.put("status",ResHpcClusterNodeStatus.REMOVING);
                    resVmMapper.deleteByExample(vmCriteria);

                    ResHpcClusterResourceExample resourceExample = new ResHpcClusterResourceExample();
                    resourceExample.createCriteria().andClusterIdEqualTo(clusterId)
                            .andResourceIdIn(resourceIdList)
                            .andResourceTypeEqualTo(ServiceManage.ECS);
                    resHpcClusterResourceMapper.deleteByExample(resourceExample);
                }
            }
        }
        resHpcClusterDeleteNodeTaskMapper.deleteByClusterId(clusterId);
    }

    @Override
    public void rollBackHPC(Long id) {
        resHpcClusterMapper.deleteByPrimaryKey(id);

        ResHpcClusterResourceExample resHpcClusterResourceExample = new ResHpcClusterResourceExample();
        resHpcClusterResourceExample.createCriteria().andClusterIdEqualTo(id);
        resHpcClusterResourceMapper.deleteByExample(resHpcClusterResourceExample);
    }

    @Override
    public Boolean checkMultipleHpcPool() {
        return resHpcClusterPoolMapper.checkMultipleHpcPool() > 1;
    }

    /**
     * 设置VNC节点
     *
     * @param resHpcClusterResources
     * @param nodeInfoList
     * @param extentionNodeRequest
     */
    private void assembleVNC(List<ResHpcClusterResource> resHpcClusterResources, List<GraphicNodeInfo> nodeInfoList,
                             HpcDrpExtentionNodeRequest extentionNodeRequest) {
        //配置设置VNC规格
        String vncConfig = extentionNodeRequest.getVncConfig();
        if (StringUtils.isNotEmpty(vncConfig)) {

            com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(vncConfig);
            GraphicNodeInfo nodeInfo = new GraphicNodeInfo();

            nodeInfo.setResourceType(jsonObject.getString("resourceType"));
            nodeInfo.setNum(jsonObject.getInteger("nodeNum"));
            nodeInfo.setFlavorRef(jsonObject.getString("flavorRef"));
            nodeInfo.setPassword(jsonObject.getString("password"));
            FDRootVolume fdRootVolume = new FDRootVolume();
            fdRootVolume.setVolumetype(jsonObject.getString("volumeType"));
            fdRootVolume.setSize(jsonObject.getInteger("size"));
            nodeInfo.setRootVolume(fdRootVolume);

            nodeInfoList.add(nodeInfo);
            return;
        }
        //数据库设置
        Integer vncNum = extentionNodeRequest.getVncNum();
        Optional<ResHpcClusterResource> vncNodeOp =
                resHpcClusterResources.stream()
                                      .filter(res -> StringUtils.equalsIgnoreCase(HPC_POINT_TYPE_VNC,
                                                                                  res.getHpcPointType()))
                                      .findFirst();
        if (vncNodeOp.isPresent()) {
            ResHpcClusterResource hpcClusterResource = vncNodeOp.get();
            GraphicNodeInfo nodeInfo = new GraphicNodeInfo();

            String resourceType = hpcClusterResource.getResourceType();
            nodeInfo.setResourceType(resourceType);
            if (StringUtils.equalsIgnoreCase(ServiceManage.BMS, resourceType)) {
                ResBms resBms = resBmsMapper.selectByPrimaryKey(String.valueOf(hpcClusterResource.getResourceId()));
                if (resBms == null) {
                    return;
                }
                nodeInfo.setNum(vncNum);
                nodeInfo.setFlavorRef(resBms.getInstanceType());
                FDRootVolume fdRootVolume = getFdRootVolume(resourceType, Long.valueOf(resBms.getId()));
                nodeInfo.setRootVolume(fdRootVolume);
            }
            if (StringUtils.equalsIgnoreCase(ServiceManage.ECS, resourceType)) {
                ResVm resVm = resVmMapper.selectByPrimaryKey(String.valueOf(hpcClusterResource.getResourceId()));
                if (resVm == null) {
                    return;
                }
                nodeInfo.setNum(vncNum);
                nodeInfo.setFlavorRef(resVm.getInstanceType());
                FDRootVolume fdRootVolume = getFdRootVolume(resourceType, Long.valueOf(resVm.getId()));
                nodeInfo.setRootVolume(fdRootVolume);
            }
            nodeInfoList.add(nodeInfo);
        }
    }

    private PublicIPInfo getPublicIPInfo(List<ResHpcClusterResource> resHpcClusterResources) {
        Optional<ResHpcClusterResource> floatingIpResoruceOp =
                resHpcClusterResources.stream()
                                      .filter(res -> StringUtils.equalsIgnoreCase(ServiceManage.FLOATING_IP,
                                                                                  res.getResourceType()))
                                      .findFirst();
        PublicIPInfo publicIPInfo = new PublicIPInfo();
        if (floatingIpResoruceOp.isPresent()) {
            ResHpcClusterResource floatingIpResoruce = floatingIpResoruceOp.get();
            ResFloatingIp resFloatingIp = resFloatingIpMapper.selectFloatingIpByKey(floatingIpResoruce.getResourceId());

            FDPublicIP fdPublicIP = new FDPublicIP();
            fdPublicIP.setType(resFloatingIp.getInternetServiceProvider());
            publicIPInfo.setPublicIP(fdPublicIP);

            FDBandwidth fdBandwidth = new FDBandwidth();
            fdBandwidth.setShareType(resFloatingIp.getShareType());
            fdBandwidth.setSize(Integer.valueOf(resFloatingIp.getBandWidth()));
            publicIPInfo.setBandwidth(fdBandwidth);
        }
        return publicIPInfo;
    }

    /**
     * @param resHpcClusterResources
     * @param computeNodeInfoList
     */
    private void assembleCompute(List<ResHpcClusterResource> resHpcClusterResources,
                                 List<ComputeNodeInfo> computeNodeInfoList, Integer agentNum) {
        Optional<ResHpcClusterResource> computeNodeOp =
                resHpcClusterResources.stream()
                                      .filter(res -> StringUtils.equalsIgnoreCase(ServiceManage.COMPUTE,
                                                                                  res.getNodeType()))
                                      .findFirst();
        if (computeNodeOp.isPresent()) {
            ResHpcClusterResource compNodeResource = computeNodeOp.get();
            ComputeNodeInfo nodeInfo = new ComputeNodeInfo();

            String resourceType = compNodeResource.getResourceType();
            nodeInfo.setResourceType(resourceType);
            if (StringUtils.equalsIgnoreCase(ServiceManage.BMS, resourceType)) {
                ResBms resBms = resBmsMapper.selectByPrimaryKey(String.valueOf(compNodeResource.getResourceId()));
                if (resBms == null) {
                    return;
                }
                nodeInfo.setAgentNum(agentNum);
                nodeInfo.setFlavorRef(resBms.getInstanceType());
                FDRootVolume fdRootVolume = getFdRootVolume(resourceType, Long.valueOf(resBms.getId()));
                nodeInfo.setRootVolume(fdRootVolume);
            }
            if (StringUtils.equalsIgnoreCase(ServiceManage.ECS, resourceType)) {
                ResVm resVm = resVmMapper.selectByPrimaryKey(String.valueOf(compNodeResource.getResourceId()));
                if (resVm == null) {
                    return;
                }
                nodeInfo.setAgentNum(agentNum);
                nodeInfo.setFlavorRef(resVm.getInstanceType());
                FDRootVolume fdRootVolume = getFdRootVolume(resourceType, Long.valueOf(resVm.getId()));
                nodeInfo.setRootVolume(fdRootVolume);
            }
            computeNodeInfoList.add(nodeInfo);
        }
    }

    /**
     * 设置登录节点
     *
     * @param resHpcClusterResources
     * @param managementNodeInfos
     */
    private void assembleManagement(List<ResHpcClusterResource> resHpcClusterResources,
                                    List<ManagementNodeInfo> managementNodeInfos) {
        Optional<ResHpcClusterResource> cliNodeResourceOp =
                resHpcClusterResources.stream()
                                      .filter(res -> StringUtils.equalsIgnoreCase(HPC_POINT_TYPE_CLI,
                                                                                  res.getHpcPointType()))
                                      .findFirst();
        if (cliNodeResourceOp.isPresent()) {
            ResHpcClusterResource cliNodeResource = cliNodeResourceOp.get();
            ManagementNodeInfo nodeInfo = new ManagementNodeInfo();
            String resourceType = cliNodeResource.getResourceType();
            nodeInfo.setResourceType(resourceType);
            if (StringUtils.equalsIgnoreCase(ServiceManage.BMS, resourceType)) {
                ResBms resBms = resBmsMapper.selectByPrimaryKey(String.valueOf(cliNodeResource.getResourceId()));
                if (resBms == null) {
                    return;
                }
                nodeInfo.setHPCNodeType(cliNodeResource.getHpcPointType());
                nodeInfo.setFlavorRef(resBms.getInstanceType());
                FDRootVolume fdRootVolume = getFdRootVolume(resourceType, Long.valueOf(resBms.getId()));
                nodeInfo.setRootVolume(fdRootVolume);
            }
            if (StringUtils.equalsIgnoreCase(ServiceManage.ECS, resourceType)) {
                ResVm resVm = resVmMapper.selectByPrimaryKey(String.valueOf(cliNodeResource.getResourceId()));
                if (resVm == null) {
                    return;
                }
                nodeInfo.setHPCNodeType(cliNodeResource.getHpcPointType());
                nodeInfo.setFlavorRef(resVm.getInstanceType());
                FDRootVolume fdRootVolume = getFdRootVolume(resourceType, Long.valueOf(resVm.getId()));
                nodeInfo.setRootVolume(fdRootVolume);
            }
            managementNodeInfos.add(nodeInfo);
        }
    }

    private FDRootVolume getFdRootVolume(String resourceType, Long resourceId) {
        FDRootVolume fdRootVolume = new FDRootVolume();

        Criteria criteria = new Criteria();
        Map<String, Object> condition = criteria.getCondition();
        condition.put("resVmId", resourceId);
        condition.put("virtType", resourceType);
        List<ResVd> resVds = resVdMapper.selectBaseByVirtType(criteria);
        Optional<ResVd> first = resVds.stream().findFirst();
        if (first.isPresent()) {
            ResVd resVd = first.get();
            fdRootVolume.setSize(Integer.valueOf(resVd.getAllocateDiskSize().toString()));
            fdRootVolume.setVolumetype(resVd.getDiskMode());
        }
        return fdRootVolume;
    }


    /**
     * HPC预部署审批流程
     * @param hpcPreDrpMgmt
     * @return HPCPreDrpMgmtResult
     */
    @Override
    public HPCPreDrpMgmtResult hpcPreDrpMgmt(HPCPreDrpMgmt hpcPreDrpMgmt, Org currentOrgInfo) {
        HPCPreDrpMgmtResult result = new HPCPreDrpMgmtResult();
        Criteria criteria = new Criteria();
        criteria.put("clusterId",hpcPreDrpMgmt.getHpcClusterID());
        List<ResHpcClusterPool> resHpcClusterPools = resHpcClusterPoolMapper.selectByParam(criteria);
        Long hpcClusterId= Long.valueOf(hpcPreDrpMgmt.getHpcClusterName());
        HPCDrpApplyInfo hpcApplyInfo = hpcPreDrpMgmt.getHpcApplyInfo();
        HPCDrpConfigInfo hpcConfigInfo = hpcApplyInfo.getHpcConfigInfo();
        if(resHpcClusterPools.size()>0){
            hpcConfigInfo.setBusinessCategory(resHpcClusterPools.get(0).getBusinessCategory());
            hpcPreDrpMgmt.setHpcClusterName(resHpcClusterPools.get(0).getClusterName());
            log.info("预部署HPC专属资源池审批-hpcPreDrpMgmt-currentOrgInfo：[{}]", JSONUtil.toJsonStr(currentOrgInfo));
            String ldapOu=currentOrgInfo.getLdapOu();
            log.info("预部署HPC专属资源池审批-hpcPreDrpMgmt-ldapOu：[{}]", JSONUtil.toJsonStr(ldapOu));
            String path = "/home/<USER>/" + ldapOu;
            log.info("预部署HPC专属资源池审批-hpcPreDrpMgmt-目录获取参数：[{}]", JSONUtil.toJsonStr(currentOrgInfo));
            if (!bssUserRemoteService.checkUserVersion(Convert.toStr(currentOrgInfo.getOrgSid()))) {
                path = "/home/" + ldapOu;
            }
            hpcConfigInfo.setHomeDir(path);
            hpcConfigInfo.setGroup(ldapOu);
            log.info("预部署HPC专属资源池审批-hpcPreDrpMgmt--目录：[{}]", path);
        }
        hpcApplyInfo.setHpcConfigInfo(hpcConfigInfo);
        hpcPreDrpMgmt.setHpcApplyInfo(hpcApplyInfo);
        ResHpcCluster resHpcCluster = resHpcClusterMapper.selectByResourceId(hpcPreDrpMgmt.getHpcClusterID(),hpcClusterId);

        try {
            log.info("预部署HPC专属资源池审批-hpcPreDrpMgmt-请求参数：[{}]", JSONUtil.toJsonStr(hpcPreDrpMgmt));
            result = (HPCPreDrpMgmtResult) MQHelper.rpc(hpcPreDrpMgmt);
            log.info("预部署HPC专属资源池审批-hpcPreDrpMgmt-返回参数：[{}]", JSONUtil.toJsonStr(result));
            //处理请求结果,taskId
            if(result.isSuccess()){
                String taskId = result.getTaskID();
                taskId = StringUtils.isBlank(taskId) ? result.getTaskId() : taskId;
                resHpcCluster.setTaskId(taskId);
                resHpcCluster.setPoolName(result.get_$OdataContext42());
                resHpcCluster.setPoolUuid(result.get_$OdataId178());
                resHpcCluster.setProessStatus("04");
                resHpcCluster.setProessPhase("13");
                resHpcCluster.setStatus("configing");
                resHpcCluster.setErrorInfo(StrUtil.EMPTY);
                resHpcCluster.setBusinessCategory(resHpcClusterPools.get(0).getBusinessCategory());
                log.info("预部署HPC专属资源池审批-hpcPreDrpMgmt-插入的参数,{}",resHpcCluster.toString());
                resHpcClusterMapper.updateByPrimaryKey(resHpcCluster);
            }else{
                resHpcCluster.setProessPhase("13");
                resHpcCluster.setProessStatus("02");
                resHpcCluster.setErrorInfo("HPC专属资源池CCM创建失败，错误详情请登录CCM查看！集群名称：[" + resHpcCluster.getName() + "]");
                log.info("预部署HPC专属资源池审批-hpcPreDrpMgmt-插入的参数,{}",resHpcCluster.toString());
                resHpcClusterMapper.updateByPrimaryKey(resHpcCluster);
                log.info("预部署HPC专属资源池审批-hpcPreDrpMgmt-ccm预部署成功");
            }
        } catch (Exception e) {
            resHpcCluster.setProessPhase("13");
            resHpcCluster.setProessStatus("02");
            resHpcCluster.setErrorInfo("HPC专属资源池CCM创建失败，错误详情请登录CCM查看！集群名称：[" + resHpcCluster.getName() + "]");
            log.info("预部署HPC专属资源池审批-hpcPreDrpMgmt-插入的参数,{}",resHpcCluster.toString());
            resHpcClusterMapper.updateByPrimaryKey(resHpcCluster);
            log.error("预部署HPC专属资源池审批-hpcPreDrpMgmt-调用adapter失败,{}",e.getMessage());
        }
        return result;
    }

    @Override
    public ResHpcCluster delPreHpcPool(Long clusterId,String type) {
        ResHpcCluster resHpcCluster = resHpcClusterMapper.selectByPrimaryKey(clusterId);
        if(Objects.nonNull(resHpcCluster)){
            Criteria criteria = new Criteria();
            criteria.put("clusterId",resHpcCluster.getResourceId());
            List<ResHpcClusterPool> resHpcClusterPools = resHpcClusterPoolMapper.selectByParam(criteria);
            ResHpcClusterPool resHpcClusterPool = resHpcClusterPools.get(0);
            if("ccp".equals(type)){
                resHpcClusterPool.setStatus("PREINITIAL");
            }else{
                resHpcClusterPool.setOrgSid(null);
            }
            resHpcClusterPoolMapper.updateByPrimaryKey(resHpcClusterPool);
        }
        return resHpcCluster;
    }


    @Override
    public void updateHpcPreClusterStatus(ResHpcClusterRemoteModule resHpcClusterRemoteModule) {

        ResHpcCluster convert = BeanConvertUtil.convert(resHpcClusterRemoteModule,
                                                        ResHpcCluster.class);
        log.info("预部署HPC专属状态更新-updateHpcPreClusterStatus-插入的参数,{}",convert.toString());
        resHpcClusterMapper.updateByPrimaryKey(convert);
    }

    /**
     * 同步HPC预部署资源
     * @param hpcSyncCloudEnvRequest
     */
    @Override
    public void syncRes(HpcSyncCloudEnvRequest hpcSyncCloudEnvRequest) {
        CloudEnv cloudEnv = cloudEnvService.selectByPrimaryKey(hpcSyncCloudEnvRequest.getCloudEnvId());
        cloudEnvService.assertEnvNonNull(cloudEnv);

        Map<String, String> params = JsonUtil.fromJson(JsonUtil.toJson(hpcSyncCloudEnvRequest),
                                                       new TypeReference<Map<String, String>>() {
                                                       });
        String companyId = Objects.isNull(BasicInfoUtil.getCurrentOrgSid()) ? "0" : BasicInfoUtil.getCurrentOrgSid().toString();

        Long userSid = BasicInfoUtil.getCurrentUserSid();
        log.info("同步HPC预部署资源-syncRes-当前companyId：[{}]", companyId);
        if(Objects.nonNull(hpcSyncCloudEnvRequest.getOrgSid())){
            companyId=String.valueOf(hpcSyncCloudEnvRequest.getOrgSid());
        }
        if(Objects.nonNull(hpcSyncCloudEnvRequest.getUserSid())){
            userSid=hpcSyncCloudEnvRequest.getUserSid();
        }
        params.put("companyId", companyId);

        if (SyncKey.ALL_IN_ONE.getKey().equals(hpcSyncCloudEnvRequest.getKey())) {
            params.put("key", cn.com.cloudstar.rightcloud.common.constants.type.SyncKey.ENV);
        }

        ScheduleHelper.manualSyncTask(hpcSyncCloudEnvRequest.getCloudEnvId(), params, userSid);
    }

    /**
     * 标准专属资源池内置弹性文件挂载路径修改
     * @param clusterId
     */
    @Override
    public void updateInnerShareMountPath(Long clusterId) {
        ResHpcCluster resHpcCluster = resHpcClusterMapper.selectByPrimaryKey(clusterId);
        Criteria criteria = new Criteria();
        criteria.put("clusterId",resHpcCluster.getResourceId());
        List<ResHpcClusterPool> resHpcClusterPools = resHpcClusterPoolMapper.selectByParam(criteria);
        //内置弹性文件
        List<ResShare> shareHPCSFS =getDefaultShareHPCSFS(clusterId);
        if(resHpcClusterPools.size()>0 && shareHPCSFS.size()>0){
            List<StorageInfo> storageInfoList= com.alibaba.fastjson.JSONObject.parseArray((String)resHpcClusterPools.get(0).getStorageInfo(), StorageInfo.class);
            storageInfoList.forEach(storageInfo->{
                ShareInfo shareInfo = storageInfo.getShareInfos().get(0);
                Optional<ResShare> first = shareHPCSFS.stream().filter(resShare ->
                                                                               Objects.equals(resShare.getUuid(),
                                                                                              shareInfo.getFileSystemID())).findFirst();
                ResShare resShare = first.get();
                resShare.setLinks(shareInfo.getMountPath());
                resShareMapper.updateByPrimaryKey(resShare);
            });
        }
    }

    @Override
    public List<ResHpcCluster> getHpcClusterComputeResources() {
        return resHpcClusterMapper.getHpcClusterComputeResources();
    }
}
