/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.resource.service.server.impl;

import cn.com.cloudstar.rightcloud.adapter.core.MQException;
import cn.com.cloudstar.rightcloud.adapter.core.MQHelper;
import cn.com.cloudstar.rightcloud.adapter.pojo.maas.MachineExtendInfo;
import cn.com.cloudstar.rightcloud.adapter.pojo.maas.MachineExtendInfo.OPERATION_TYPE;
import cn.com.cloudstar.rightcloud.adapter.pojo.maas.result.MachineExtendInfoResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.MaasVm;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.MachineCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.MachineJoin;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.MachineOperate;
import cn.com.cloudstar.rightcloud.basic.data.platform.CloudClientFactory;
import cn.com.cloudstar.rightcloud.basic.data.pojo.base.Base;
import cn.com.cloudstar.rightcloud.basic.data.pojo.cloud.CloudEnv;
import cn.com.cloudstar.rightcloud.basic.data.pojo.deploy.DeployTask;
import cn.com.cloudstar.rightcloud.basic.data.pojo.deploy.HostDepoyEvent;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResVd;
import cn.com.cloudstar.rightcloud.basic.data.pojo.res.ResVm;
import cn.com.cloudstar.rightcloud.basic.data.service.deploy.BasicDeployPlaybookService;
import cn.com.cloudstar.rightcloud.basic.data.service.deploy.BasicDeployTaskService;
import cn.com.cloudstar.rightcloud.common.additional.PhysicalHostPowerAttrVO;
import cn.com.cloudstar.rightcloud.common.additional.ResInstResult;
import cn.com.cloudstar.rightcloud.common.constants.WebConstants;
import cn.com.cloudstar.rightcloud.common.constants.WebConstants.InstallSnmp;
import cn.com.cloudstar.rightcloud.common.constants.WebConstants.PhysicalHostInfo;
import cn.com.cloudstar.rightcloud.common.constants.WebConstants.PhysicalHostJoinType;
import cn.com.cloudstar.rightcloud.common.constants.WebConstants.PowerStatus;
import cn.com.cloudstar.rightcloud.common.constants.ansible.AnsibleServerMethod;
import cn.com.cloudstar.rightcloud.common.constants.msg.MsgCd;
import cn.com.cloudstar.rightcloud.common.constants.res.status.CloudPhysicalHostStatus;
import cn.com.cloudstar.rightcloud.common.constants.res.status.ResVmManageStatus;
import cn.com.cloudstar.rightcloud.common.constants.res.status.ResVmStatus;
import cn.com.cloudstar.rightcloud.common.constants.res.type.CloudEnvType;
import cn.com.cloudstar.rightcloud.common.constants.status.CloudPhysicalHostPoolAllocStatus;
import cn.com.cloudstar.rightcloud.common.constants.type.*;
import cn.com.cloudstar.rightcloud.common.exception.BizException;
import cn.com.cloudstar.rightcloud.common.redis.JedisUtil;
import cn.com.cloudstar.rightcloud.common.util.*;
import cn.com.cloudstar.rightcloud.common.util.json.JsonUtil;
import cn.com.cloudstar.rightcloud.common.websocket.ServerMsgPublisher;
import cn.com.cloudstar.rightcloud.core.pojo.dto.cloud.CloudHostEvents;
import cn.com.cloudstar.rightcloud.core.pojo.dto.cloud.CloudPhysicalHostPoolImportViewVO;
import cn.com.cloudstar.rightcloud.core.pojo.dto.cloud.CloudPhysicalHostPoolTemplateVO;
import cn.com.cloudstar.rightcloud.core.pojo.dto.cloud.CloudPhysicalHostPricingTemplateVO;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResInterface;
import cn.com.cloudstar.rightcloud.core.pojo.dto.res.ResStoragePartition;
import cn.com.cloudstar.rightcloud.core.pojo.dto.script.CloudPhysicalHostJoinToResVmDTO;
import cn.com.cloudstar.rightcloud.core.pojo.dto.script.CloudPhysicalHostPoolUpdateDTO;
import cn.com.cloudstar.rightcloud.core.pojo.vo.res.InterfaceInfoVO;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.User;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicInfoUtil;
import cn.com.cloudstar.rightcloud.module.support.access.util.BasicWebUtil;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.cloud.CloudPhysicalHostPool;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.keypair.CloudKeyPair;
import cn.com.cloudstar.rightcloud.remote.api.pojo.resource.script.CloudPhysicalHostPoolJoinDTO;
import cn.com.cloudstar.rightcloud.resource.dao.network.ResInterfaceMapper;
import cn.com.cloudstar.rightcloud.resource.dao.server.CloudPhysicalHostPoolMapper;
import cn.com.cloudstar.rightcloud.resource.dao.server.ResVmMapper;
import cn.com.cloudstar.rightcloud.resource.dao.storage.ResStoragePartitionMapper;
import cn.com.cloudstar.rightcloud.resource.service.env.CloudEnvService;
import cn.com.cloudstar.rightcloud.resource.service.security.CloudKeyPairService;
import cn.com.cloudstar.rightcloud.resource.service.server.CloudPhysicalHostPoolService;
import cn.com.cloudstar.rightcloud.resource.service.server.ResVmService;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.base.Strings;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date: 16:11 2018/4/4
 */
@Slf4j
@Service
public class CloudPhysicalHostPoolServiceImpl implements CloudPhysicalHostPoolService {

    private final static String HAS_CN = "有";
    private final static String NOTHING_CN = "无";
    private final static String YES_CN = "是";
    private final static String NO_CN = "否";

    @Autowired
    private CloudPhysicalHostPoolMapper cloudPhysicalHostPoolMapper;

    @Autowired
    private ResInterfaceMapper resInterfaceMapper;

    @Autowired
    private ResStoragePartitionMapper resStoragePartitionMapper;

    @Autowired
    private ResVmMapper resVmMapper;

    @Autowired
    @Lazy
    private ResVmService resVmService;

    @Autowired
    private CloudKeyPairService cloudKeyPairService;

    @Autowired
    private CloudEnvService cloudEnvService;

    @Autowired
    private BasicDeployTaskService basicDeployTaskService;

    @Autowired
    private BasicDeployPlaybookService basicDeployPlaybookService;

    @Override
    public int insert(CloudPhysicalHostPool cloudPhysicalHostPool) {
        return this.cloudPhysicalHostPoolMapper.insert(cloudPhysicalHostPool);
    }

    @Override
    public int countByExample(Criteria example) {
        return this.cloudPhysicalHostPoolMapper.countByParams(example);
    }

    @Override
    public List<CloudPhysicalHostPool> selectByExample(Criteria example) {
        return this.cloudPhysicalHostPoolMapper.selectByParams(example);
    }

    @Override
    public CloudPhysicalHostPool selectByPrimaryKey(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return this.cloudPhysicalHostPoolMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(CloudPhysicalHostPool cloudPhysicalHostPool) {
        return this.cloudPhysicalHostPoolMapper.updateByPrimaryKeySelective(cloudPhysicalHostPool);
    }

    @Override
    public CloudPhysicalHostPool selectAllByPrimaryKey(Long id) {
        CloudPhysicalHostPool cloudPhysicalHostPool = this.cloudPhysicalHostPoolMapper.selectAllByPrimaryKey(id);
        ResVm resVm = resVmService.selectByPhysicalHostPoolId(cloudPhysicalHostPool.getId());
        if (Objects.nonNull(resVm)) {
            cloudPhysicalHostPool.setInstanceSid(resVm.getId());
            cloudPhysicalHostPool.setInstanceName(resVm.getInstanceName());
            cloudPhysicalHostPool.setInstallSnmp(resVm.getInstallSnmp());
        }
        return cloudPhysicalHostPool;
    }


    @Override
    public void start(Long cloudPhysicalHostPoolId) {
        this.start(Collections.singletonList(cloudPhysicalHostPoolId));
    }

    @Override
    public void start(List<Long> physicalHostPoolIds) {
        for (Long cloudPhysicalHostPoolId : physicalHostPoolIds) {
            CloudPhysicalHostPool cloudPhysicalHostPool = this.cloudPhysicalHostPoolMapper.selectByPrimaryKey(
                    cloudPhysicalHostPoolId);
            MachineOperate machineOperate = newMachineOperateWithParam(cloudPhysicalHostPool);
            machineOperate.setAction(VmOperation.START);
            sendAsyncToMQ(machineOperate);

            CloudPhysicalHostPool updatePhysical = new CloudPhysicalHostPool();
            updatePhysical.setPowerStatus(PowerStatus.STARTING);
            updatePhysical.setId(cloudPhysicalHostPool.getId());
            this.cloudPhysicalHostPoolMapper.updateByPrimaryKeySelective(updatePhysical);
        }
    }


    @Override
    public void stop(Long physicalHostPoolId) {
        this.stop(Collections.singletonList(physicalHostPoolId));
    }

    @Override
    public void stop(List<Long> physicalHostPoolIds) {
        for (Long physicalHostPoolId : physicalHostPoolIds) {
            CloudPhysicalHostPool cloudPhysicalHostPool = this.cloudPhysicalHostPoolMapper.selectByPrimaryKey(
                    physicalHostPoolId);
            MachineOperate machineOperate = newMachineOperateWithParam(cloudPhysicalHostPool);
            machineOperate.setAction(VmOperation.STOP);
            sendAsyncToMQ(machineOperate);

            CloudPhysicalHostPool updatePhysical = new CloudPhysicalHostPool();
            updatePhysical.setPowerStatus(PowerStatus.STOPPING);
            updatePhysical.setId(cloudPhysicalHostPool.getId());
            this.cloudPhysicalHostPoolMapper.updateByPrimaryKeySelective(updatePhysical);
        }
    }

    @Override
    public void soft(Long physicalHostPoolId) {
        this.soft(Collections.singletonList(physicalHostPoolId));
    }

    @Override
    public void soft(List<Long> physicalHostPoolIds) {
        for (Long physicalHostPoolId : physicalHostPoolIds) {
            CloudPhysicalHostPool cloudPhysicalHostPool = this.cloudPhysicalHostPoolMapper.selectByPrimaryKey(
                    physicalHostPoolId);
            MachineOperate machineOperate = newMachineOperateWithParam(cloudPhysicalHostPool);
            machineOperate.setAction(VmOperation.SOFT);
            sendAsyncToMQ(machineOperate);

            CloudPhysicalHostPool updatePhysical = new CloudPhysicalHostPool();
            updatePhysical.setPowerStatus(PowerStatus.STOPPING);
            updatePhysical.setId(cloudPhysicalHostPool.getId());
            this.cloudPhysicalHostPoolMapper.updateByPrimaryKeySelective(updatePhysical);
        }
    }

    @Override
    public void restart(Long physicalHostPoolId) {
        this.restart(Collections.singletonList(physicalHostPoolId));
    }

    @Override
    public void restart(List<Long> physicalHostPoolIds) {
        for (Long physicalHostPoolId : physicalHostPoolIds) {
            CloudPhysicalHostPool cloudPhysicalHostPool = this.cloudPhysicalHostPoolMapper.selectByPrimaryKey(
                    physicalHostPoolId);
            MachineOperate machineOperate = newMachineOperateWithParam(cloudPhysicalHostPool);
            machineOperate.setAction(VmOperation.REBOOT);
            sendAsyncToMQ(machineOperate);

            CloudPhysicalHostPool updatePhysical = new CloudPhysicalHostPool();
            updatePhysical.setPowerStatus(PowerStatus.REBOOTING);
            updatePhysical.setId(cloudPhysicalHostPool.getId());
            this.cloudPhysicalHostPoolMapper.updateByPrimaryKeySelective(updatePhysical);
        }
    }

    private MachineOperate newMachineOperateWithParam(CloudPhysicalHostPool cloudPhysicalHostPool) {
        MachineOperate machineOperate = null;
        ResVm resVm = this.resVmService.selectByPhysicalHostPoolId(cloudPhysicalHostPool.getId());
        if (Objects.nonNull(cloudPhysicalHostPool.getCloudEnvId()) && cloudPhysicalHostPool.getCloudEnvId() > 0L) {
            machineOperate = CloudClientFactory.buildMQBean(cloudPhysicalHostPool.getCloudEnvId(),
                                                            MachineOperate.class);
        } else if (Objects.nonNull(resVm)) {
            machineOperate = CloudClientFactory.buildMQBean(resVm.getCloudEnvId(), MachineOperate.class);
        } else {
            machineOperate = new MachineOperate();
            setDefaultBaseEnvParam4Maas(machineOperate);
        }

        machineOperate.setPhysicalHostPoolId(cloudPhysicalHostPool.getId());
        machineOperate.setHostName(cloudPhysicalHostPool.getHostName());
        machineOperate.setPowerType(cloudPhysicalHostPool.getPowerType());
        machineOperate.setOptions(MapsKit.of(PhysicalHostInfo.POWER_ATTR_DATA, cloudPhysicalHostPool.getPowerAttrData(),
                                             "originPowerStatus", cloudPhysicalHostPool.getPowerStatus()));
        return machineOperate;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long physicalHostPoolId) {
        CloudPhysicalHostPool cloudPhysicalHostPool = this.cloudPhysicalHostPoolMapper.selectByPrimaryKey(
                physicalHostPoolId);
        if (Objects.isNull(cloudPhysicalHostPool)) {
            return;
        }
        Criteria criteria = new Criteria();
        ResVm resVm = this.resVmService.selectByPhysicalHostPoolId(physicalHostPoolId);
        if (Objects.nonNull(resVm) && !CloudPhysicalHostPoolAllocStatus.UNUSED.equalsIgnoreCase(
                cloudPhysicalHostPool.getAllocStatus())) {
            if (StringUtil.isNotBlank(resVm.getOwnerId())) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1332800946));
            }
        }
        // 删除物理机数据库中的信息
        criteria.clear();
        criteria.put("physicalHostPoolId", physicalHostPoolId);
        List<ResInterface> resInterfaceList = resInterfaceMapper.selectByParams(criteria);
        if (CollectionUtil.isNotEmpty(resInterfaceList)) {
            resInterfaceList.forEach(resInterface -> resInterfaceMapper.deleteByPrimaryKey(resInterface.getId()));
        }
        // 删除分区
        List<ResStoragePartition> partitions = resStoragePartitionMapper.selectByParams(criteria);
        if (CollectionUtil.isNotEmpty(partitions)) {
            for (ResStoragePartition partition : partitions) {
                this.resStoragePartitionMapper.deleteByPrimaryKey(partition.getId());
            }
        }
        // 删除物理机
        this.cloudPhysicalHostPoolMapper.deleteByPrimaryKey(physicalHostPoolId);
        if (Objects.nonNull(resVm)) {
            this.resVmMapper.deleteByPrimaryKey(resVm.getId());
        }
    }

    @Override
    public void delete(List<Long> physicalHostPoolIds) {
        for (Long physicalHostPoolId : physicalHostPoolIds) {
            this.delete(physicalHostPoolId);
        }
    }

    @Override
    public int deleteByParams(Criteria criteria) {
        return this.cloudPhysicalHostPoolMapper.deleteByParams(criteria);
    }


    @Override
    public CloudPhysicalHostPool selectByMacAddress(Long cloudEnvId, String macAddress) {
        Criteria criteria1 = new Criteria();
        criteria1.put("cloudEnvId", cloudEnvId);
        criteria1.put("macAddress", macAddress);
        List<CloudPhysicalHostPool> cloudPhysicalHostPools = this.cloudPhysicalHostPoolMapper.selectByParams(criteria1);
        return CollectionUtil.isNotEmpty(cloudPhysicalHostPools) ? cloudPhysicalHostPools.get(0) : null;
    }

    @Override
    public List<CloudHostEvents> getEvents(Long id) {
        if (id == null) {
            return Lists.newArrayList();
        }
        CloudPhysicalHostPool cloudPhysicalHostPool = this.cloudPhysicalHostPoolMapper.selectByPrimaryKey(id);
        if (Objects.isNull(cloudPhysicalHostPool) || StringUtil.isBlank(cloudPhysicalHostPool.getPhysicalUUID())) {
            return Lists.newArrayList();
        }
        MachineExtendInfo machineExtendInfo = CloudClientFactory.buildMQBean(cloudPhysicalHostPool.getCloudEnvId(),
                                                                             MachineExtendInfo.class);
        machineExtendInfo.setType(MachineExtendInfo.OPERATION_TYPE.EVENTS);
        machineExtendInfo.setPhysicalHostId(cloudPhysicalHostPool.getPhysicalUUID());
        MachineExtendInfoResult result = (MachineExtendInfoResult) sendMQRpcMessage(machineExtendInfo);
        JsonNode jsonNode = JsonUtil.fromJson((String) result.getQueryResult());
        if (Objects.isNull(jsonNode) || Objects.isNull(jsonNode.findValue("events"))) {
            return Lists.newArrayList();
        }
        List<CloudHostEvents> cloudHostEventsList = new ArrayList<>();
        JsonNode events = jsonNode.findValue("events");
        for (Iterator<JsonNode> elements = events.elements(); elements.hasNext(); ) {
            CloudHostEvents cloudHostEvents = new CloudHostEvents();
            JsonNode next = elements.next();
            if (!Objects.isNull(next.get("id"))) {
                cloudHostEvents.setId(next.get("id").asLong());
            }
            if (!Objects.isNull(next.get("node"))) {
                cloudHostEvents.setNode(next.get("node").textValue());
            }
            if (!Objects.isNull(next.get("level"))) {
                cloudHostEvents.setLevel(next.get("level").textValue().toLowerCase());
            }
            if (!Objects.isNull(next.get("hostname"))) {
                cloudHostEvents.setHostname(next.get("hostname").textValue());
            }
            if (!Objects.isNull(next.get("type"))) {
                cloudHostEvents.setType(next.get("type").textValue().toLowerCase());
            }
            if (!Objects.isNull(next.get("created"))) {
                cloudHostEvents.setCreated(formatGMTDateStr(next.get("created").textValue()));
            }
            if (!Objects.isNull(next.get("description")) && !Strings.isNullOrEmpty(next.get("description").textValue())
                    && !"(root)".equalsIgnoreCase(next.get("description").textValue())) {
                cloudHostEvents.setDescription(cloudHostEvents.getType() + "-" + next.get("description").textValue());
            } else {
                cloudHostEvents.setDescription(cloudHostEvents.getType());
            }
            cloudHostEventsList.add(cloudHostEvents);
        }
        return cloudHostEventsList;
    }

    private static String formatGMTDateStr(String dateStr) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("EEE, dd MMM. yyyy HH:mm:ss", java.util.Locale.US);
            Date date = sdf.parse(dateStr);
            return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date);
        } catch (Exception ignore) {
        }
        return dateStr;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePhysicalHost(CloudPhysicalHostPoolUpdateDTO physicalHostPoolUpdateDTO) {
        Long physicalHostId = physicalHostPoolUpdateDTO.getId();
        CloudPhysicalHostPool cloudPhysicalHostPoolOrg = this.cloudPhysicalHostPoolMapper.selectByPrimaryKey(
                physicalHostId);
        CloudPhysicalHostPool cloudPhysicalHostPoolUpdate = new CloudPhysicalHostPool();
        BeanUtils.copyProperties(physicalHostPoolUpdateDTO, cloudPhysicalHostPoolUpdate);
        cloudPhysicalHostPoolUpdate.setId(physicalHostId);
        if (IPUtil.internalIp(cloudPhysicalHostPoolUpdate.getPublicIp())) {
            cloudPhysicalHostPoolUpdate.setInnerIp(physicalHostPoolUpdateDTO.getPublicIp());
            cloudPhysicalHostPoolUpdate.setPublicIp(null);
        } else {
            cloudPhysicalHostPoolUpdate.setPublicIp(physicalHostPoolUpdateDTO.getPublicIp());
        }
        if (Objects.isNull(cloudPhysicalHostPoolUpdate.getSshPort())) {
            cloudPhysicalHostPoolUpdate.setSshPort(22);
        }
        if (StringUtil.isBlank(cloudPhysicalHostPoolUpdate.getHostName())) {
            cloudPhysicalHostPoolUpdate.setHostName(cloudPhysicalHostPoolOrg.getHostName());
        }
        if (StringUtil.isBlank(cloudPhysicalHostPoolUpdate.getPowerAttrData())) {
            cloudPhysicalHostPoolUpdate.setPowerAttrData(cloudPhysicalHostPoolOrg.getPowerAttrData());
        }
        cloudPhysicalHostPoolUpdate.setPhysicalStatus(CloudPhysicalHostStatus.SETTING);
        BasicWebUtil.prepareUpdateAdminParams(cloudPhysicalHostPoolUpdate);
        this.cloudPhysicalHostPoolMapper.updateByPrimaryKeySelective(cloudPhysicalHostPoolUpdate);

        Map<String, Object> options = Maps.newHashMap();
        options.put("opType", "updatePhysicalHost");
        options.put("physicalStatusOrigin", cloudPhysicalHostPoolOrg.getPhysicalStatus());
        options.put("powerAttrDataOrigin", cloudPhysicalHostPoolOrg.getPowerAttrData());
        sendJoinMachineToMQ(cloudPhysicalHostPoolUpdate, options);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void connectResVm(List<CloudPhysicalHostJoinToResVmDTO> conectPhysicalHostList) {
        for (CloudPhysicalHostJoinToResVmDTO cloudPhysicalHostJoinToResVmDTO : conectPhysicalHostList) {
            this.connectResVmItem(cloudPhysicalHostJoinToResVmDTO);
        }
    }

    @Override
    public Map<Long, CloudPhysicalHostPool> selectByIds(List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return Maps.newHashMap();
        }
        List<CloudPhysicalHostPool> list = this.cloudPhysicalHostPoolMapper.selectByParams(new Criteria("ids", ids));
        if (CollectionUtil.isEmpty(list)) {
            return Maps.newHashMap();
        }
        list.forEach(item -> {
            item.setAllocStatusName(CloudPhysicalHostPoolAllocStatus.getName(item.getAllocStatus()));
        });
        return list.stream().collect(Collectors.toMap(CloudPhysicalHostPool::getId, item -> item, (k1, k2) -> k1));
    }

    @Override
    public List<ResVd> selectVmVds(Criteria criteria) {
        Integer vmStorageSize = this.cloudPhysicalHostPoolMapper.selectVmStorageSize(criteria);
        if (Objects.isNull(vmStorageSize)) {
            return Lists.newArrayList();
        }
        ResVd resVd = new ResVd();
        resVd.setResVmId("hasvmid");
        resVd.setAllocateDiskSize(Convert.toLong(vmStorageSize));
        resVd.setVolumeTypeName("其他");
        return Arrays.asList(resVd);
    }

    @Override
    public void updateAllocStatus(Long id, String allocStatus) {
        if (Objects.isNull(id)) {
            return;
        }
        CloudPhysicalHostPool cloudPhysicalHostPoolUpdate = new CloudPhysicalHostPool();
        cloudPhysicalHostPoolUpdate.setId(id);
        cloudPhysicalHostPoolUpdate.setAllocStatus(allocStatus);
        BasicWebUtil.prepareUpdateParams(cloudPhysicalHostPoolUpdate);
        this.cloudPhysicalHostPoolMapper.updateByPrimaryKeySelective(cloudPhysicalHostPoolUpdate);
    }

    @Override
    @Transactional
    public void importPhysicalHostPricing(List<CloudPhysicalHostPricingTemplateVO> templateVOS) {
        Map<String, CloudPhysicalHostPricingTemplateVO> assetNumberMap = templateVOS.stream()
                                                                                    .collect(Collectors.toMap(
                                                                                            CloudPhysicalHostPricingTemplateVO::getAssetNumber,
                                                                                            item -> item,
                                                                                            (o1, o2) -> o1));
        CloudPhysicalHostPool cloudPhysicalHostPoolUpdate = new CloudPhysicalHostPool();
        List<CloudPhysicalHostPool> cloudPhysicalHostPools = this.cloudPhysicalHostPoolMapper.selectByParams(
                new Criteria());

        cloudPhysicalHostPools.forEach(physicalHostPool -> {
            CloudPhysicalHostPricingTemplateVO pricingTemplateVO = assetNumberMap.get(
                    physicalHostPool.getAssetNumber());
            if (Objects.nonNull(pricingTemplateVO)) {
                AssertUtil.requireNonBlank(pricingTemplateVO.getAssetNumber(), "资产编号不能为空，请修改后导入！");
                AssertUtil.requireNonBlank(pricingTemplateVO.getCostPrice(), "单价不能为空，请修改后导入！");
                AssertUtil.requireNonBlank(pricingTemplateVO.getCostCycle(), "计费周期不能为空，请修改后导入！");
                try {
                    cloudPhysicalHostPoolUpdate.setCostPrice(
                            new BigDecimal(pricingTemplateVO.getCostPrice()).setScale(4, BigDecimal.ROUND_HALF_UP));
                } catch (Exception e) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1055554239));
                }
                cloudPhysicalHostPoolUpdate.setCostCycle(
                        BillingTypeConfig.pareseBillingTypeConfig(pricingTemplateVO.getCostCycle()));
            } else {
                cloudPhysicalHostPoolUpdate.setCostPrice(null);
                cloudPhysicalHostPoolUpdate.setCostCycle(null);
            }
            cloudPhysicalHostPoolUpdate.setId(physicalHostPool.getId());
            this.cloudPhysicalHostPoolMapper.updateCostInfo(cloudPhysicalHostPoolUpdate);
        });
    }

    @Override
    public List<CloudPhysicalHostPool> selectByParams(Criteria criteria) {
        return cloudPhysicalHostPoolMapper.selectByParams(criteria);
    }

    private void connectResVmItem(CloudPhysicalHostJoinToResVmDTO cloudPhysicalHostJoinToResVmDTO) {
        Long physicalHostId = cloudPhysicalHostJoinToResVmDTO.getPhysicalHostId();
        CloudPhysicalHostPool physicalHostPoolOrg = this.cloudPhysicalHostPoolMapper.selectByPrimaryKey(physicalHostId);
        CloudPhysicalHostPool cloudPhysicalHostPoolUpdate = new CloudPhysicalHostPool();
        BeanUtils.copyProperties(cloudPhysicalHostJoinToResVmDTO, cloudPhysicalHostPoolUpdate);
        cloudPhysicalHostPoolUpdate.setId(physicalHostId);
        if (IPUtil.internalIp(cloudPhysicalHostPoolUpdate.getPublicIp())) {
            cloudPhysicalHostPoolUpdate.setInnerIp(cloudPhysicalHostJoinToResVmDTO.getPublicIp());
            cloudPhysicalHostPoolUpdate.setPublicIp(null);
        } else {
            cloudPhysicalHostPoolUpdate.setPublicIp(cloudPhysicalHostJoinToResVmDTO.getPublicIp());
        }
        if (Objects.isNull(cloudPhysicalHostPoolUpdate.getSshPort())) {
            cloudPhysicalHostPoolUpdate.setSshPort(22);
        }
        if (StringUtil.isBlank(cloudPhysicalHostPoolUpdate.getHostName())) {
            cloudPhysicalHostPoolUpdate.setHostName(physicalHostPoolOrg.getHostName());
        }
        if (StringUtil.isBlank(cloudPhysicalHostPoolUpdate.getPowerAttrData())) {
            cloudPhysicalHostPoolUpdate.setPowerAttrData(physicalHostPoolOrg.getPowerAttrData());
        }
        cloudPhysicalHostPoolUpdate.setPhysicalHostJoinType(PhysicalHostJoinType.PLATFORM_HASOS);
        cloudPhysicalHostPoolUpdate.setInstallSnmp(InstallSnmp.NEED_INSTALL);
        // 验证用户信息
        boolean hasOS = PhysicalHostJoinType.PLATFORM_HASOS.equalsIgnoreCase(
                cloudPhysicalHostPoolUpdate.getPhysicalHostJoinType());
        if (hasOS) {
            // 用户信息验证通过 重新接管或重新生成实例再接管
            ResVm resVm = this.resVmService.selectByPhysicalHostPoolId(physicalHostId);
            if (Objects.isNull(resVm)) {
                // 生成新实例
                resVm = new ResVm();
                BeanUtils.copyProperties(cloudPhysicalHostPoolUpdate, resVm);
                resVm.setInstanceId(physicalHostPoolOrg.getPhysicalUUID());
                resVm.setInstanceName(cloudPhysicalHostPoolUpdate.getHostName());
                resVm.setManagemenPassword(CrytoUtilSimple.decrypt(cloudPhysicalHostPoolUpdate.getManagemenPassword(), true));
                resVm.setStatus(ResVmStatus.RUNNING);
                resVm.setManageStatus(ResVmManageStatus.UNUNITED);
                resVm.setServerType(ServerType.INSTANCE);
                resVm.setStartTime(new Date());
                resVm.setPhysicalHostPoolId(physicalHostId);
                BasicWebUtil.prepareInsertParams(resVm, physicalHostPoolOrg.getCreatedBy());
                resVm.setId(UuidUtil.getUuid().replace("-", ""));
                resVm.setCloudEnvId(physicalHostPoolOrg.getCloudEnvId());
                resVm.setOrgSid(physicalHostPoolOrg.getOrgSid());
                resVm.setOsCategory(cloudPhysicalHostPoolUpdate.getOsCategory());
                resVm.setSshPort(cloudPhysicalHostPoolUpdate.getSshPort());
                this.resVmService.insertSelective(resVm);
            } else {
                // 更新原来的实例
                ResVm resVmUpdate = new ResVm();
                BeanUtils.copyProperties(cloudPhysicalHostPoolUpdate, resVmUpdate);
                resVmUpdate.setId(resVm.getId());
                this.resVmService.updateByPrimaryKeySelective(resVmUpdate);
            }
            // setup playbook
            List<DeployTask> deployTasks = this.basicDeployTaskService.deployResVmTask(resVm, null);
            this.basicDeployPlaybookService.setupPlaybook(deployTasks);

            HostDepoyEvent hostDepoyEvent = new HostDepoyEvent(resVm);
            hostDepoyEvent.setType(DeployTaskType.IMPORT_HOST);
            hostDepoyEvent.setTaskId(deployTasks.get(0).getId());
            hostDepoyEvent.setApiPath(AnsibleServerMethod.ControlApi.AGENT);
            SpringContextHolder.publishEvent(hostDepoyEvent);

        } else {
            // 如果有实例则删除
            this.resVmMapper.updateResVmStatusByPhysicalHostPoolId(physicalHostId, ResVmStatus.DELETED);
        }
        this.cloudPhysicalHostPoolMapper.updateByPrimaryKeySelective(cloudPhysicalHostPoolUpdate);
    }


    @Override
    public Map<String, Object> getSensorRecords(Long id) {
        CloudPhysicalHostPool cloudPhysicalHostPool = this.cloudPhysicalHostPoolMapper.selectByPrimaryKey(id);
        if (Objects.isNull(cloudPhysicalHostPool)) {
            return Maps.newHashMap();
        }
        String sensorRecordKey = "common:cloudPhysicalHost:SensorRecord:" + id;
        String sensorRecordJson = JedisUtil.instance().get(sensorRecordKey);
        if (StringUtil.isNotBlank(sensorRecordJson)) {
            return JSON.parseObject(sensorRecordJson);
        }

        String lockkey = "sensor:" + id;
        boolean locked = JedisUtil.instance().setnx(lockkey, "ok");
        if (!locked) {
            return Maps.newHashMap();
        }
        JedisUtil.instance().expire(lockkey, 5 * 60);

        MachineExtendInfo machineExtendInfo = CloudClientFactory.buildMQBean(cloudPhysicalHostPool.getCloudEnvId(),
                                                                             MachineExtendInfo.class);
        machineExtendInfo.setType(OPERATION_TYPE.SENSOR_RECORD);
        machineExtendInfo.setPhysicalHostId(cloudPhysicalHostPool.getPhysicalUUID());
        machineExtendInfo.setOptions(
                MapsKit.of(PhysicalHostInfo.POWER_ATTR_DATA, cloudPhysicalHostPool.getPowerAttrData()));
        MachineExtendInfoResult result = (MachineExtendInfoResult) sendMQRpcMessage(machineExtendInfo);
        if (Objects.nonNull(result.getQueryResult())) {
            List<Map<String, Object>> queryResult = (List<Map<String, Object>>) result.getQueryResult();

            Map<String, Object> sensorMap = Maps.newHashMap();

            List<Map<String, Object>> metalFullSensorRecords = queryResult.stream()
                                                                          .filter(m -> m.get("sensorRecordType")
                                                                                        .equals("Full"))
                                                                          .map(m -> {
                                                                              processDoubleValue(m, "curValue");
                                                                              processDoubleValue(m,
                                                                                                 "upperNonRecoverableThreshold");
                                                                              processDoubleValue(m,
                                                                                                 "lowerNonRecoverableThreshold");
                                                                              processDoubleValue(m,
                                                                                                 "upperCriticalThreshold");
                                                                              processDoubleValue(m,
                                                                                                 "lowerCriticalThreshold");
                                                                              processDoubleValue(m,
                                                                                                 "upperNonCriticalThreshold");
                                                                              processDoubleValue(m,
                                                                                                 "lowerNonCriticalThreshold");
                                                                              return m;
                                                                          })
                                                                          .collect(Collectors.toList());
            List<Map<String, Object>> compactSensorRecords = queryResult.stream()
                                                                        .filter(m -> m.get("sensorRecordType")
                                                                                      .equals("Compact"))
                                                                        .collect(Collectors.toList());

            sensorMap.put("full", metalFullSensorRecords);
            sensorMap.put("compact", compactSensorRecords);

            if (CollectionUtil.isNotEmpty(queryResult)) {
                JedisUtil.instance().set(sensorRecordKey, JSON.toJSONString(sensorMap), 20 * 60);
            } else {
                JedisUtil.instance().del(lockkey);
            }
            return sensorMap;
        }

        JedisUtil.instance().del(lockkey);

        return Maps.newHashMap();
    }

    private static void processDoubleValue(Map<String, Object> m, String key) {
        Double curValue = (Double) m.get(key);
        if (Objects.nonNull(curValue) && curValue != 0.0) {
            m.put(key, DecimalUtil.keepThreeDecimals(curValue));
        }
    }

    @Override
    public void downloadTemplate(HttpServletResponse response, String type) {
        String resourceName = PhysicalHostInfo.PHYSICAL_HOOST_IMPORT_TEMPLATE_URL;
        if ("pricing".equalsIgnoreCase(type)) {
            resourceName = PhysicalHostInfo.PHYSICAL_HOOST_IMPORT_PRICINGTEMPLATE_URL;
        }
        try (InputStream in = ClassLoaderUtil.getResourceAsStream(resourceName,
                                                                  CloudPhysicalHostPoolServiceImpl.class)) {
            String destFileName =
                    "pricing".equalsIgnoreCase(type) ? new String("导入物理机计费模板.xlsx".getBytes(), "ISO-8859-1")
                                                     : new String("导入物理机模板.xlsx".getBytes(), "ISO-8859-1");
            response.setHeader("Content-Disposition", "attachment;filename=" + destFileName);
            response.setContentType("application/vnd.ms-excel");
            OutputStream out = response.getOutputStream();
            byte[] buff = new byte[2048];
            int len;
            while ((len = in.read(buff)) != -1) {
                out.write(buff, 0, len);
            }
            out.flush();
        } catch (Exception e) {
            log.error("导入物理机模板下载失败! {}", Throwables.getStackTraceAsString(e));
        }
    }

    @Override
    @Transactional
    public List<CloudPhysicalHostPoolImportViewVO> importCloudPhysicalHost(Long cloudEnvId,
                                                                           List<CloudPhysicalHostPoolTemplateVO> templateVOS) {
        // 验证导入参数
        validateImportParam(templateVOS);
        List<CloudKeyPair> cloudKeyPairs = cloudKeyPairService.selectByParams(new Criteria());

        List<CloudPhysicalHostPoolImportViewVO> viewVOS = Lists.newArrayList();
        List<CloudPhysicalHostPool> sendMqList = Lists.newArrayList();
        for (CloudPhysicalHostPoolTemplateVO templateVO : templateVOS) {
            CloudPhysicalHostPoolImportViewVO importViewVOS = BeanConvertUtil.convert(templateVO,
                                                                                      CloudPhysicalHostPoolImportViewVO.class);
            try {
                CloudPhysicalHostPoolJoinDTO joinDTO = templateConvertDTO(templateVO, cloudEnvId, cloudKeyPairs);
                CloudPhysicalHostPool insertPhysicalHostPool = prepareSavePhysicalHostPool(joinDTO);
                this.cloudPhysicalHostPoolMapper.insertSelective(insertPhysicalHostPool);

                importViewVOS.setCloudPhysicalHostPoolId(insertPhysicalHostPool.getId());
                importViewVOS.setViewStatus(CloudPhysicalHostStatus.JOINING);
                importViewVOS.setViewStatusInfo("接入中");
                sendMqList.add(insertPhysicalHostPool);
            } catch (Exception e) {
                log.error("导入{}失败：{}", templateVO.getHostName(), Throwables.getStackTraceAsString(e));
                importViewVOS.setViewStatus(CloudPhysicalHostStatus.IMPORT_FAILED);
                importViewVOS.setViewStatusInfo(e.getMessage());
            }
            viewVOS.add(importViewVOS);
        }

        if (CollectionUtil.isNotEmpty(sendMqList)) {
            ScheduledExecutorService executorService = Executors.newSingleThreadScheduledExecutor();
            executorService.schedule(() -> {
                for (CloudPhysicalHostPool cloudPhysicalHostPool : sendMqList) {
                    sendJoinMachineToMQ(cloudPhysicalHostPool);
                }
            }, 500, TimeUnit.MILLISECONDS);
            executorService.shutdown();
        }
        return viewVOS;
    }


    private void validateImportParam(List<CloudPhysicalHostPoolTemplateVO> templateVOS) {
        AssertUtil.requireNonBlank(templateVOS, "未读取到导入的数据.");
        for (CloudPhysicalHostPoolTemplateVO templateVO : templateVOS) {
            AssertUtil.requireNonBlank(templateVO.getId(), "编码必填.");
            AssertUtil.requireNonBlank(templateVO.getHostName(), "名称必填.");
            AssertUtil.requireNonBlank(templateVO.getAssetNumber(), "资产编号不能为空.");
            if (StringUtil.isNotBlank(templateVO.getHasOs()) && !Arrays.asList(HAS_CN, NOTHING_CN)
                                                                       .contains(templateVO.getHasOs())) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1804636283) + HAS_CN + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_44) + NOTHING_CN + WebUtil.getMessage(MsgCd.ERR_MSG_BSS_93));
            }
            if (StringUtil.isNotBlank(templateVO.getLoginType()) && !Arrays.asList("密码", "密钥")
                                                                           .contains(templateVO.getLoginType())) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1112944485));
            }
            if (HAS_CN.equals(templateVO.getHasOs())) {
                AssertUtil.requireNonBlank(templateVO.getPublicIp(), "请输入系统IP.");
                AssertUtil.requireNonBlank(templateVO.getManagementAccount(), "请输入用户名.");
                if (StringUtil.isBlank(templateVO.getLoginType())) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_278856827));
                }
                if (StringUtil.isBlank(templateVO.getManagemenPassword())) {
                    throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_1405851233));
                }
            }
        }
    }


    public CloudPhysicalHostPool prepareSavePhysicalHostPool(
            CloudPhysicalHostPoolJoinDTO cloudPhysicalHostPoolJoinDTO) {
        checkInsertParam(cloudPhysicalHostPoolJoinDTO);
        // 新增资源池物理机
        CloudPhysicalHostPool addPhysical = new CloudPhysicalHostPool();
        BeanUtils.copyProperties(cloudPhysicalHostPoolJoinDTO, addPhysical);
        addPhysical.setOrgSid(BasicInfoUtil.getCurrentOrgSid());
        addPhysical.setPhysicalStatus(CloudPhysicalHostStatus.JOINING);
        if (IPUtil.internalIp(cloudPhysicalHostPoolJoinDTO.getPublicIp())) {
            addPhysical.setInnerIp(cloudPhysicalHostPoolJoinDTO.getPublicIp());
            addPhysical.setPublicIp(null);
        } else {
            addPhysical.setPublicIp(cloudPhysicalHostPoolJoinDTO.getPublicIp());
        }
        if (StringUtil.isBlank(addPhysical.getOsCategory())) {
            addPhysical.setOsCategory(OsType.LINUX);
        }
        if (StringUtil.equalsIgnoreCase(addPhysical.getOsCategory(), OsType.WINDOWS)) {
            addPhysical.setSshPort(5985);
        } else {
            addPhysical.setSshPort(22);
        }
        addPhysical.setPowerStatus(PowerStatus.UNKNOWN);
        BasicWebUtil.prepareInsertParams(addPhysical);
        return addPhysical;
    }

    private CloudPhysicalHostPoolJoinDTO templateConvertDTO(CloudPhysicalHostPoolTemplateVO templateVO, Long cloudEnvId,
                                                            List<CloudKeyPair> cloudKeyPairs) {
        CloudPhysicalHostPoolJoinDTO cloudPhysicalHostPoolJoinDTO = new CloudPhysicalHostPoolJoinDTO();
        BeanUtils.copyProperties(templateVO, cloudPhysicalHostPoolJoinDTO);
        cloudPhysicalHostPoolJoinDTO.setCloudEnvId(cloudEnvId);
        if (StringUtil.isNotBlank(templateVO.getHasOs())) {
            cloudPhysicalHostPoolJoinDTO.setPhysicalHostJoinType(
                    HAS_CN.equals(templateVO.getHasOs().trim()) ? PhysicalHostJoinType.PLATFORM_HASOS
                                                                : PhysicalHostJoinType.PLATFORM_NOOS);
        } else {
            cloudPhysicalHostPoolJoinDTO.setPhysicalHostJoinType(PhysicalHostJoinType.PLATFORM_NOOS);
        }
        // 判断是密码还是密钥
        String loginType = templateVO.getLoginType();
        boolean isKeyPairs = "密钥".equalsIgnoreCase(loginType);
        if (isKeyPairs) {
            CloudKeyPair cloudKeyPair = cloudKeyPairs.stream()
                                                     .filter(item -> item.getPrivateKey()
                                                                         .equals(templateVO.getManagemenPassword()))
                                                     .findFirst()
                                                     .orElseThrow(() -> new BizException("密钥未找到，请先从平台导入."));
            cloudPhysicalHostPoolJoinDTO.setKeypairId(cloudKeyPair.getId());
            cloudPhysicalHostPoolJoinDTO.setKeypairName(cloudKeyPair.getKeypairName());
            cloudPhysicalHostPoolJoinDTO.setRemoteLoginType(RemoteLoginType.BY_KEY);
            cloudPhysicalHostPoolJoinDTO.setManagemenPassword(null);
        } else {
            cloudPhysicalHostPoolJoinDTO.setRemoteLoginType(RemoteLoginType.BY_PASS);
        }
        cloudPhysicalHostPoolJoinDTO.setOsName(StringUtils.join(templateVO.getOsType(), templateVO.getOsVersion()));
        cloudPhysicalHostPoolJoinDTO.setOsCategory(this.parseOsCategory(templateVO.getOsType()));
        Map<String, String> map = Maps.newLinkedHashMapWithExpectedSize(3);
        map.put("power_address", templateVO.getPowerAddress());
        map.put("power_user", templateVO.getPowerUser());
        map.put("power_pass", templateVO.getPowerPass());
        cloudPhysicalHostPoolJoinDTO.setPowerAttrData(JSON.toJSONString(map));
        return cloudPhysicalHostPoolJoinDTO;
    }


    private String parseOsCategory(String osTypeName) {
        if (StringUtil.isBlank(osTypeName)) {
            return OsType.LINUX;
        }
        if (StringUtils.containsIgnoreCase(osTypeName, OsType.WINDOWS)) {
            return OsType.WINDOWS;
        }
        if (StringUtils.containsIgnoreCase(osTypeName, OsType.AIX)) {
            return OsType.AIX;
        }
        return OsType.LINUX;
    }

    @Override
    @Transactional
    public Long joinPhysicalHost(CloudPhysicalHostPoolJoinDTO cloudPhysicalHostPoolJoinDTO) {
        CloudPhysicalHostPool cloudPhysicalHostPool = prepareSavePhysicalHostPool(cloudPhysicalHostPoolJoinDTO);
        this.cloudPhysicalHostPoolMapper.insertSelective(cloudPhysicalHostPool);

        sendJoinMachineToMQ(cloudPhysicalHostPool);
        return cloudPhysicalHostPool.getId();
    }

    @Override
    @Transactional
    public void reJoinPhysicalHost(Long id) {
        CloudPhysicalHostPool cloudPhysicalHostPool = this.cloudPhysicalHostPoolMapper.selectByPrimaryKey(id);
        CloudPhysicalHostPool cloudPhysicalHostPoolUpdate = new CloudPhysicalHostPool();
        cloudPhysicalHostPoolUpdate.setId(id);
        cloudPhysicalHostPoolUpdate.setPhysicalStatus(CloudPhysicalHostStatus.JOINING);
        cloudPhysicalHostPoolUpdate.setStatusInfo("");
        this.cloudPhysicalHostPoolMapper.updateByPrimaryKeySelective(cloudPhysicalHostPoolUpdate);
        sendJoinMachineToMQ(cloudPhysicalHostPool);
    }

    @Override
    public void sendWsJoinCloudPhysicalHostComplate(Long cloudPhysicalHostPoolId, boolean flag, String errMsg) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("cloudPhysicalHostPoolId", cloudPhysicalHostPoolId);
        map.put("status", flag);
        map.put("statusInfo", errMsg);
        ServerMsgPublisher.sendMsg("/topic/cloudPhysicalHost/createComplete", map);
    }

    @Override
    public void updatePhysicalHostInterface(Long physicalHostPoolId, String interfaceSet) {
        if (StringUtil.isNotBlank(interfaceSet)) {
            this.resInterfaceMapper.deleteByParams(new Criteria("physicalHostPoolId", physicalHostPoolId));

            List<InterfaceInfoVO> list = JsonUtil.toListObject(interfaceSet, InterfaceInfoVO.class);
            for (InterfaceInfoVO interVo : list) {
                ResInterface resCard = new ResInterface();
                BeanUtils.copyProperties(interVo, resCard);
                resCard.setId(null);
                resCard.setInterfaceName(interVo.getName());
                resCard.setParentsJson(null != interVo.getParents() ? JsonUtil.toJson(interVo.getParents()) : null);
                resCard.setDiscoveredJson(
                        null != interVo.getDiscovered() ? JsonUtil.toJson(interVo.getDiscovered()) : null);
                resCard.setTags(null != interVo.getTags() ? StringUtils.join(interVo.getTags(), ",") : null);
                resCard.setLinksJson(null != interVo.getLinks() ? JsonUtil.toJson(interVo.getLinks()) : null);
                resCard.setChildrenJson(null != interVo.getChildren() ? JsonUtil.toJson(interVo.getChildren()) : null);
                resCard.setEnabled(interVo.getEnabled() != null && interVo.getEnabled() ? 1 : 0);
                resCard.setCloudEnvType(CloudEnvType.MAAS.getValue().get(0));
                resCard.setPhysicalHostPoolId(physicalHostPoolId);
                resCard.setUuid(interVo.getId().toString());
                resInterfaceMapper.insert(resCard);
            }
        }
    }

    @Override
    public void updatePhysicalHostPartition(Long physicalHostPoolId, String diskSet, String specialFilesystems) {
        List<ResStoragePartition> partitions = Lists.newArrayList();
        if (StringUtil.isNotBlank(diskSet)) {
            String bootDiskStr = JsonUtil.toJson(diskSet);
            JsonNode jsonNode = JsonUtil.fromJson(bootDiskStr);
            for (Iterator<JsonNode> elements = jsonNode.elements(); elements.hasNext(); ) {
                JsonNode next = elements.next();
                ResStoragePartition physicalStorage = new ResStoragePartition();
                physicalStorage.setName(next.get("name").textValue());
                physicalStorage.setUuid(next.get("id").longValue() + "");
                physicalStorage.setResVdUuid(next.get("id").longValue() + "");
                physicalStorage.setPartitionId(next.get("id").asLong());
                physicalStorage.setPartitionSize(Double.valueOf(next.get("size").asLong() / 1000 / 1000 / 1000));
                physicalStorage.setPartitionPath(next.get("path").textValue());
                physicalStorage.setPartitionType(next.get("type").textValue());
                physicalStorage.setResourceUri(next.get("resource_uri").textValue());
                physicalStorage.setUsedFor(next.get("resource_uri").textValue());
                physicalStorage.setBootable("true");
                physicalStorage.setPhysicalHostPoolId(physicalHostPoolId);
                physicalStorage.setAvailableSize(next.get("available_size").asLong() / 1000 / 1000 / 1000);
                partitions.add(physicalStorage);
                JsonNode partitonNode = next.get("partitions");
                if (partitonNode == null) {
                    break;
                }
                for (Iterator<JsonNode> partitionElements = partitonNode.elements(); partitionElements.hasNext(); ) {
                    ResStoragePartition resStoragePartition = new ResStoragePartition();
                    JsonNode partitionNext = partitionElements.next();
                    String deviceId = null;
                    if (partitionNext.get("device_id") != null) {
                        deviceId = String.valueOf(partitionNext.get("device_id").asLong());
                    }
                    String usedFor = null;
                    if (partitionNext.get("used_for") != null) {
                        usedFor = partitionNext.get("used_for").textValue();
                    }
                    String type = null;
                    if (partitionNext.get("type") != null) {
                        type = partitionNext.get("type").textValue();
                    }
                    Boolean bootable = Boolean.FALSE;
                    if (partitionNext.get("bootable") != null) {
                        bootable = partitionNext.get("bootable").asBoolean();
                    }
                    Long size = 0L;
                    if (partitionNext.get("size") != null) {
                        size = partitionNext.get("size").asLong();
                    }
                    String uuid = null;
                    if (partitionNext.get("uuid") != null) {
                        uuid = partitionNext.get("uuid").textValue();
                    }
                    String resourceUri = null;
                    if (partitionNext.get("resource_uri") != null) {
                        resourceUri = partitionNext.get("resource_uri").textValue();
                    }
                    Long id = null;
                    if (partitionNext.get("id") != null) {
                        id = partitionNext.get("id").asLong();
                    }
                    String systemId = null;
                    if (partitionNext.get("system_id") != null) {
                        systemId = partitionNext.get("system_id").textValue();
                    }
                    String path = null;
                    String name = null;
                    if (partitionNext.get("path") != null) {
                        path = partitionNext.get("path").textValue();
                        name = path.substring(path.lastIndexOf('/') + 1);
                    }
                    if (partitionNext.get("filesystem") != null) {
                        if (partitionNext.get("filesystem").get("label") != null) {
                            String label = partitionNext.get("filesystem").get("label").textValue();
                            resStoragePartition.setLabel(label);
                        }
                        if (partitionNext.get("filesystem").get("uuid") != null) {
                            String fsUuid = partitionNext.get("filesystem").get("uuid").textValue();
                            resStoragePartition.setFsUuid(fsUuid);
                        }
                        if (partitionNext.get("filesystem").get("fstype") != null) {
                            String fstype = partitionNext.get("filesystem").get("fstype").textValue();
                            resStoragePartition.setFstype(fstype);
                        }
                        if (partitionNext.get("filesystem").get("mount_options") != null) {
                            String mountOptions = partitionNext.get("filesystem").get("mount_options").textValue();
                            resStoragePartition.setMountOptions(mountOptions);
                        }
                        if (partitionNext.get("filesystem").get("mount_point") != null) {
                            String mountPoint = partitionNext.get("filesystem").get("mount_point").textValue();
                            resStoragePartition.setMountPoint(mountPoint);
                        }
                        resStoragePartition.setSpecialFsFlag("0");
                    }
                    resStoragePartition.setName(name);
                    resStoragePartition.setPartitionId(id);
                    resStoragePartition.setUuid(uuid);
                    resStoragePartition.setPartitionSize(Double.valueOf(size / 1000 / 1000 / 1000));
                    resStoragePartition.setPartitionPath(path);
                    resStoragePartition.setPartitionType(type);
                    resStoragePartition.setResourceUri(resourceUri);
                    resStoragePartition.setUsedFor(usedFor);
                    resStoragePartition.setBootable(bootable.toString());
                    resStoragePartition.setPhysicalHostUuid(systemId);
                    resStoragePartition.setPhysicalHostPoolId(physicalHostPoolId);
                    resStoragePartition.setResVdUuid(deviceId);
                    partitions.add(resStoragePartition);
                }
            }
        }

        if (StringUtil.isNotBlank(specialFilesystems)) {
            String bootDiskStr = JsonUtil.toJson(specialFilesystems);
            JsonNode jsonNode = JsonUtil.fromJson(bootDiskStr);
            for (Iterator<JsonNode> elements = jsonNode.elements(); elements.hasNext(); ) {
                ResStoragePartition resStoragePartition = new ResStoragePartition();
                JsonNode next = elements.next();
                if (next.get("label") != null) {
                    String label = next.get("label").textValue();
                    resStoragePartition.setLabel(label);
                }
                if (next.get("uuid") != null) {
                    String fsUuid = next.get("uuid").textValue();
                    resStoragePartition.setFsUuid(fsUuid);
                }
                if (next.get("fstype") != null) {
                    String fstype = next.get("fstype").textValue();
                    resStoragePartition.setFstype(fstype);
                }
                if (next.get("mount_options") != null) {
                    String mountOptions = next.get("mount_options").textValue();
                    resStoragePartition.setMountOptions(mountOptions);
                }
                if (next.get("mount_point") != null) {
                    String mountPoint = next.get("mount_point").textValue();
                    resStoragePartition.setMountPoint(mountPoint);
                }
                resStoragePartition.setSpecialFsFlag("1");
                resStoragePartition.setPhysicalHostPoolId(physicalHostPoolId);
                partitions.add(resStoragePartition);
            }
        }

        if (CollectionUtil.isNotEmpty(partitions)) {
            resStoragePartitionMapper.deleteByParams(new Criteria("physicalHostPoolId", physicalHostPoolId));
            for (ResStoragePartition partition : partitions) {
                BasicWebUtil.prepareInsertAdminParams(partition);
                this.resStoragePartitionMapper.insertSelective(partition);
            }
        }
    }


    private void sendCreateMachineToMQ(CloudPhysicalHostPool cloudPhysicalHostPool, ResVm resVm) {
        MachineCreate machineCreate = CloudClientFactory.buildMQBean(resVm.getCloudEnvId(), MachineCreate.class);
        machineCreate.setHostName(resVm.getInstanceName());
        MaasVm maasVm = new MaasVm();
        maasVm.setPhysicalHostJoinType(cloudPhysicalHostPool.getPhysicalHostJoinType());
        maasVm.setPowerType(cloudPhysicalHostPool.getPowerType().toLowerCase());
        maasVm.setMacAddress(cloudPhysicalHostPool.getMacAddress());

        // powerAtrrJson
        PhysicalHostPowerAttrVO physicalHostPowerAttrVO = PhysicalHostPowerAttrVO.toVO(
                cloudPhysicalHostPool.getPowerAttrData());
        maasVm.setPowerAddress(physicalHostPowerAttrVO.getPowerAddress());
        maasVm.setPowerUser(physicalHostPowerAttrVO.getPowerUser());
        maasVm.setPowerPassword(physicalHostPowerAttrVO.getPowerPass());
        machineCreate.setMaas(maasVm);
        // 其他参数
        Map<String, Object> options = Maps.newHashMap();
        options.put(WebConstants.PhysicalHostInfo.ORIGIN_KEY, WebConstants.PhysicalHostInfo.ORIGIN_VALUE);
        options.put(WebConstants.PhysicalHostInfo.PHYSICAL_HOST_POOL_ID, cloudPhysicalHostPool.getId());
        options.put("resVmId", resVm.getId());
        options.put("resVmStatus", resVm.getStatus());
        machineCreate.setOptions(options);
        sendAsyncToMQ(machineCreate);
    }

    @Override
    @Transactional
    public void addInstallPlatform(Long id) {
        ResVm resVm = this.resVmService.selectByPhysicalHostPoolId(id);
        addInstallPlatform(MapsKit.of("resVmId", resVm.getId()));
    }

    @Override
    public void addInstallPlatform(Map<String, Object> map) {
        String resVmId = map.get("resVmId").toString();
        ResVm resVm = this.resVmService.selectByPrimaryKey(resVmId);
        CloudPhysicalHostPool cloudPhysicalHostPool = this.cloudPhysicalHostPoolMapper.selectByPrimaryKey(
                resVm.getPhysicalHostPoolId());

        if (StringUtil.isNullOrEmpty(cloudPhysicalHostPool.getPowerType())) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_838971233));
        }

        // 检查是否有装机平台
        CloudEnv cloudEnv = this.cloudEnvService.selectByPrimaryKey(resVm.getCloudEnvId());
        JsonNode jsonNode = JsonUtil.fromJson(cloudEnv.getAttrData()).get(CloudEnvTenantKey.JOIN_MAAS);
        boolean joinMaas = Objects.nonNull(jsonNode) ? jsonNode.asBoolean() : false;
        if (!joinMaas) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_599712321));
        }
        ResVm resVmUpdate = new ResVm();
        resVmUpdate.setId(resVm.getId());
        resVmUpdate.setStatus(ResVmStatus.SETTING);
        this.resVmMapper.updateByPrimaryKeySelective(resVmUpdate);

        sendCreateMachineToMQ(cloudPhysicalHostPool, resVm);
    }

    @Override
    public void checkPowerStatus(Long id) {
        CloudPhysicalHostPool cloudPhysicalHostPool = this.cloudPhysicalHostPoolMapper.selectByPrimaryKey(id);
        MachineOperate machineOperate = CloudClientFactory.buildMQBean(cloudPhysicalHostPool.getCloudEnvId(),
                                                                       MachineOperate.class);
        machineOperate.setPhysicalHostPoolId(id);
        machineOperate.setHostName(cloudPhysicalHostPool.getHostName());
        machineOperate.setAction(VmOperation.CHECK_POWER);
        machineOperate.setOptions(MapsKit.of(PhysicalHostInfo.POWER_ATTR_DATA, cloudPhysicalHostPool.getPowerAttrData(),
                                             "originPowerStatus", cloudPhysicalHostPool.getPowerStatus()));
        sendAsyncToMQ(machineOperate);
    }

    @Override
    public void updateInstallSnmpStatus(ResVm resVm, boolean installSnmpFlag) {
        if (Objects.nonNull(resVm)) {
            // 设置安装snmp状态
            ResVm resVmUpdate = new ResVm();
            resVmUpdate.setId(resVm.getId());
            resVmUpdate.setInstallSnmp(installSnmpFlag ? InstallSnmp.INSTALL_SUCCESS : InstallSnmp.INSTALL_FAILD);
            resVmService.updateByPrimaryKeySelective(resVmUpdate);
            CloudPhysicalHostPool cloudPhysicalHostPoolUpdate = new CloudPhysicalHostPool();
            cloudPhysicalHostPoolUpdate.setId(resVm.getPhysicalHostPoolId());
            cloudPhysicalHostPoolUpdate.setInstallSnmp(resVmUpdate.getInstallSnmp());
            cloudPhysicalHostPoolMapper.updateByPrimaryKeySelective(cloudPhysicalHostPoolUpdate);
        }
    }

    @Override
    public void updatePhysicalHostWhenResVmRemove(Long physicalHostPoolId) {
        if (Objects.nonNull(physicalHostPoolId)) {
            CloudPhysicalHostPool cloudPhysicalHostPoolUpdate = new CloudPhysicalHostPool();
            cloudPhysicalHostPoolUpdate.setId(physicalHostPoolId);
            cloudPhysicalHostPoolUpdate.setPhysicalStatus(CloudPhysicalHostStatus.JOIN_SUCCESS);
            this.cloudPhysicalHostPoolMapper.updateByPrimaryKeySelective(cloudPhysicalHostPoolUpdate);
        }
    }

    private void sendJoinMachineToMQ(CloudPhysicalHostPool cloudPhysicalHostPool) {
        sendJoinMachineToMQ(cloudPhysicalHostPool, null);
    }

    private void sendJoinMachineToMQ(CloudPhysicalHostPool cloudPhysicalHostPool, Map<String, Object> options) {
        MachineJoin machineJoin = new MachineJoin();
        setDefaultBaseEnvParam4Maas(machineJoin);
        User authUser = BasicInfoUtil.getAuthUser();
        if (Objects.nonNull(authUser) && !StringUtil.isNullOrEmpty(authUser.getAccount())) {
            machineJoin.setOpUser(authUser.getAccount());
        }
        machineJoin.setOrgSid(NumberUtil.toStr(BasicInfoUtil.getCurrentOrgSid(), null));
        machineJoin.setHostName(cloudPhysicalHostPool.getHostName());

        MaasVm maasVm = new MaasVm();
        maasVm.setPhysicalHostJoinType(cloudPhysicalHostPool.getPhysicalHostJoinType());
        maasVm.setPowerType(
                StrUtil.isNotBlank(cloudPhysicalHostPool.getPowerType()) ? cloudPhysicalHostPool.getPowerType()
                                                                                                .toLowerCase() : null);
        maasVm.setMacAddress(cloudPhysicalHostPool.getMacAddress());
        // 电源参数
        PhysicalHostPowerAttrVO powerAttrVO = PhysicalHostPowerAttrVO.toVO(cloudPhysicalHostPool.getPowerAttrData());
        if (Objects.nonNull(powerAttrVO)) {
            maasVm.setPowerAddress(powerAttrVO.getPowerAddress());
            maasVm.setPowerUser(powerAttrVO.getPowerUser());
            maasVm.setPowerPassword(powerAttrVO.getPowerPass());
        }
        machineJoin.setMaas(maasVm);
        if (Objects.isNull(options)) {
            options = Maps.newHashMap();
        }
        options.put(WebConstants.PhysicalHostInfo.ORIGIN_KEY, WebConstants.PhysicalHostInfo.ORIGIN_VALUE);
        options.put(WebConstants.PhysicalHostInfo.PHYSICAL_HOST_POOL_ID, cloudPhysicalHostPool.getId());
        machineJoin.setOptions(options);
        sendAsyncToMQ(machineJoin);
    }


    private void setDefaultBaseEnvParam4Maas(Base machineJoin) {
        machineJoin.setProviderType(CloudEnvType.MAAS.getValue().get(0));
        machineJoin.setVirtEnvType(CloudEnvType.MAAS.getValue().get(0).toLowerCase());
        machineJoin.setVirtEnvUuid(MQHelper.MQ_ENV_QUEUE_UUID);
    }


    private void checkInsertParam(CloudPhysicalHostPoolJoinDTO cloudPhysicalHostPoolJoinDTO) {
        // 验证资产编号
        if (StringUtil.isNotBlank(cloudPhysicalHostPoolJoinDTO.getAssetNumber())) {
            Criteria criteria = new Criteria();
            criteria.put("assetNumber", cloudPhysicalHostPoolJoinDTO.getAssetNumber());
            int count = this.cloudPhysicalHostPoolMapper.countByParams(criteria);
            if (count > 0) {
                log.info("资产编号:[{}]", cloudPhysicalHostPoolJoinDTO.getAssetNumber());
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_458585891));
            }
        }
        // 验证mac地址
        if (StringUtil.isNotBlank(cloudPhysicalHostPoolJoinDTO.getMacAddress())) {
            cloudPhysicalHostPoolJoinDTO.setMacAddress(
                    StringUtils.join(cloudPhysicalHostPoolJoinDTO.getMacAddress().split(","), ","));
            CloudPhysicalHostPool physicalHost = selectByMacAddress(cloudPhysicalHostPoolJoinDTO.getCloudEnvId(),
                                                                    cloudPhysicalHostPoolJoinDTO.getMacAddress());
            if (physicalHost != null) {
                throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_800302366));
            }
        }
    }

    private Object sendMQRpcMessage(Base base) {
        try {
            return MQHelper.rpc(base);
        } catch (MQException e) {
            throw new BizException(WebUtil.getMessage(MsgCd.ERR_MSG_BSS_915698903) + e.getMessage());
        }
    }

    private ResInstResult sendAsyncToMQ(Base base) {
        ResInstResult result;
        try {
            MQHelper.sendMessage(base);
            result = new ResInstResult(ResInstResult.SUCCESS);
        } catch (Exception e) {
            result = new ResInstResult(ResInstResult.FAILURE, e.getMessage());
        }
        return result;
    }
}
