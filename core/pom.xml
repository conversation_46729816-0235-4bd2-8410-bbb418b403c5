<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2018 Cloud-Star, Inc. All Rights Reserved.
  -->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>rightcloud-resource</artifactId>
        <groupId>cn.com.cloudstar</groupId>
        <version>vboss.2.6.0-sec-dg-poc-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>cn.com.cloudstar</groupId>
    <artifactId>rightcloud-resource-core</artifactId>
    <packaging>jar</packaging>
    <name>${project.artifactId}</name>
    <url>http://maven.apache.org</url>

    <dependencies>
<!--        feign 依赖-->
        <dependency>
			<groupId>io.github.openfeign</groupId>
			<artifactId>feign-httpclient</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-openfeign</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-openfeign-core</artifactId>
		</dependency>


        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo</artifactId>
            <version>${dubbo.version}</version>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <artifactId>netty-all</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.mybatis.caches</groupId>
            <artifactId>mybatis-ehcache</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.cloudstar</groupId>
            <artifactId>rightcloud-common-util</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.cloudstar</groupId>
            <artifactId>rightcloud-resource-core-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.cloudstar</groupId>
            <artifactId>rightcloud-adapter-core</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>rightcloud-basic-data-pojo</artifactId>
                    <groupId>cn.com.cloudstar</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.com.cloudstar</groupId>
            <artifactId>rightcloud-access</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>bcpkix-jdk15on</artifactId>
                    <groupId>org.bouncycastle</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.com.cloudstar</groupId>
            <artifactId>rightcloud-authority</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.cloudstar</groupId>
            <artifactId>rightcloud-basic-data-core-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.cloudstar</groupId>
            <artifactId>rightcloud-resource-pojo</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>rightcloud-data-encrypt</artifactId>
                    <groupId>cn.com.cloudstar</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <artifactId>rightcloud-remote-api-analysis</artifactId>
            <groupId>cn.com.cloudstar</groupId>
        </dependency>
        <dependency>
            <artifactId>rightcloud-remote-api-system</artifactId>
            <groupId>cn.com.cloudstar</groupId>
        </dependency>
        <dependency>
            <artifactId>rightcloud-remote-api-iam</artifactId>
            <groupId>cn.com.cloudstar</groupId>
        </dependency>
        <dependency>
            <artifactId>rightcloud-remote-api-monitor</artifactId>
            <groupId>cn.com.cloudstar</groupId>
        </dependency>
        <dependency>
            <artifactId>rightcloud-push-message</artifactId>
            <groupId>cn.com.cloudstar</groupId>
        </dependency>
        <dependency>
            <groupId>cn.com.cloudstar</groupId>
            <artifactId>rightcloud-action-filter</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.cloudstar</groupId>
            <artifactId>rightcloud-schedule-connector</artifactId>
        </dependency>
        <dependency>
            <groupId>org.camunda.bpm</groupId>
            <artifactId>camunda-engine</artifactId>
            <version>7.18.0</version>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-jackson</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.com.cloudstar</groupId>
            <artifactId>rightcloud-file-storage</artifactId>
            <version>${cmp.module.support.version}</version>
        </dependency>
    </dependencies>

    <build>
        <resources>
            <resource>
                <targetPath>${project.build.directory}/classes</targetPath>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/*.xml</include>
                    <include>**/*.properties</include>
                    <include>**/*.json</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
