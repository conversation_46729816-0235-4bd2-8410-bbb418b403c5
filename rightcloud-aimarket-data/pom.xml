<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>rightcloud-aimarket-module</artifactId>
        <groupId>cn.com.cloudstar</groupId>
        <version>vboss.2.6.0-sec-dg-poc-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>rightcloud-aimarket-data</artifactId>

    <dependencies>
        <dependency>
            <groupId>cn.com.cloudstar</groupId>
            <artifactId>rightcloud-aimarket-common</artifactId>
            <version>vboss.2.6.0-sec-dg-poc-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>cn.com.cloudstar</groupId>
            <artifactId>rightcloud-common-validator</artifactId>
            <version>${cmp.common.version}</version>
        </dependency>
    </dependencies>
    <build>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*</include>
                </includes>
            </resource>
        </resources>
    </build>
</project>
