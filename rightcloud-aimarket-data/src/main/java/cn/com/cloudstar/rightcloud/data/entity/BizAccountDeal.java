package cn.com.cloudstar.rightcloud.data.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 账号收支记录表 
 * @TableName biz_account_deal
 */
@TableName(value ="biz_account_deal")
@Data
public class BizAccountDeal implements Serializable {
    /**
     * SID
     */
    @TableId
    private Long dealSid;

    /**
     * 交易编号
     */
    private String flowNo;

    /**
     * 云环境类型
     */
    private String envType;

    /**
     * 云环境名称
     */
    private String envName;

    /**
     * 收支类型 in（收入） out （支出）
     */
    private String type;

    /**
     * 交易类型 charge（充值）pay（消费）clearance（清理）
     */
    private String tradeType;

    /**
     * 交易渠道 alipay（支付宝）platform（平台）acctCash（用户余额）accCredit（信用额度）coupon（优惠卷）wechatPay（微信支付）balanceCash（充值现金券）deductBalanceCash（抵扣现金券）
     */
    private String tradeChannel;

    /**
     * 交易流水号
     */
    private String tradeNo;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 账单号
     */
    private String billNo;

    /**
     * 备注 主要为明细针对的产品信息如：云主机，弹性公网IP等
     */
    private String remark;

    /**
     * 账期 账期为年-月格式YYYY-MM
     */
    private String billingCycle;

    /**
     * 当前金额
     */
    private BigDecimal amount;

    /**
     * 余额
     */
    private BigDecimal balance;

    /**
     * 信用额度
     */
    private BigDecimal balanceCredit;

    /**
     * 账户
     */
    private Long accountSid;

    /**
     * 账户名称 账户名称
     */
    private String accountName;

    /**
     * 组织ISD
     */
    private Long orgSid;

    /**
     * 用户SID
     */
    private Long userSid;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedDt;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 交易时间戳
     */
    private Long dealTime;

    /**
     * 现金券余额
     */
    private BigDecimal balanceCash;

    /**
     * 当前现金券金额
     */
    private BigDecimal cashAmount;

    /**
     * 抵扣现金券编号
     */
    private String deductCashNo;

    /**
     * 当前抵扣现金券余额
     */
    private BigDecimal deductBalanceCash;

    /**
     * 运营实体id
     */
    private Long entityId;

    /**
     * 运营实体名称
     */
    private String entityName;

    /**
     * 收费规则：02:销售计费、01:正常计费
     */
    private String chargingType;

    /**
     * 优惠券金额
     */
    private BigDecimal couponAmount;

    /**
     * 资金监管状态
     */
    private String superviseStatus;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        BizAccountDeal other = (BizAccountDeal) that;
        return (this.getDealSid() == null ? other.getDealSid() == null : this.getDealSid().equals(other.getDealSid()))
            && (this.getFlowNo() == null ? other.getFlowNo() == null : this.getFlowNo().equals(other.getFlowNo()))
            && (this.getEnvType() == null ? other.getEnvType() == null : this.getEnvType().equals(other.getEnvType()))
            && (this.getEnvName() == null ? other.getEnvName() == null : this.getEnvName().equals(other.getEnvName()))
            && (this.getType() == null ? other.getType() == null : this.getType().equals(other.getType()))
            && (this.getTradeType() == null ? other.getTradeType() == null : this.getTradeType().equals(other.getTradeType()))
            && (this.getTradeChannel() == null ? other.getTradeChannel() == null : this.getTradeChannel().equals(other.getTradeChannel()))
            && (this.getTradeNo() == null ? other.getTradeNo() == null : this.getTradeNo().equals(other.getTradeNo()))
            && (this.getOrderNo() == null ? other.getOrderNo() == null : this.getOrderNo().equals(other.getOrderNo()))
            && (this.getBillNo() == null ? other.getBillNo() == null : this.getBillNo().equals(other.getBillNo()))
            && (this.getRemark() == null ? other.getRemark() == null : this.getRemark().equals(other.getRemark()))
            && (this.getBillingCycle() == null ? other.getBillingCycle() == null : this.getBillingCycle().equals(other.getBillingCycle()))
            && (this.getAmount() == null ? other.getAmount() == null : this.getAmount().equals(other.getAmount()))
            && (this.getBalance() == null ? other.getBalance() == null : this.getBalance().equals(other.getBalance()))
            && (this.getBalanceCredit() == null ? other.getBalanceCredit() == null : this.getBalanceCredit().equals(other.getBalanceCredit()))
            && (this.getAccountSid() == null ? other.getAccountSid() == null : this.getAccountSid().equals(other.getAccountSid()))
            && (this.getAccountName() == null ? other.getAccountName() == null : this.getAccountName().equals(other.getAccountName()))
            && (this.getOrgSid() == null ? other.getOrgSid() == null : this.getOrgSid().equals(other.getOrgSid()))
            && (this.getUserSid() == null ? other.getUserSid() == null : this.getUserSid().equals(other.getUserSid()))
            && (this.getCreatedBy() == null ? other.getCreatedBy() == null : this.getCreatedBy().equals(other.getCreatedBy()))
            && (this.getCreatedDt() == null ? other.getCreatedDt() == null : this.getCreatedDt().equals(other.getCreatedDt()))
            && (this.getUpdatedBy() == null ? other.getUpdatedBy() == null : this.getUpdatedBy().equals(other.getUpdatedBy()))
            && (this.getUpdatedDt() == null ? other.getUpdatedDt() == null : this.getUpdatedDt().equals(other.getUpdatedDt()))
            && (this.getVersion() == null ? other.getVersion() == null : this.getVersion().equals(other.getVersion()))
            && (this.getDealTime() == null ? other.getDealTime() == null : this.getDealTime().equals(other.getDealTime()))
            && (this.getBalanceCash() == null ? other.getBalanceCash() == null : this.getBalanceCash().equals(other.getBalanceCash()))
            && (this.getCashAmount() == null ? other.getCashAmount() == null : this.getCashAmount().equals(other.getCashAmount()))
            && (this.getDeductCashNo() == null ? other.getDeductCashNo() == null : this.getDeductCashNo().equals(other.getDeductCashNo()))
            && (this.getDeductBalanceCash() == null ? other.getDeductBalanceCash() == null : this.getDeductBalanceCash().equals(other.getDeductBalanceCash()))
            && (this.getEntityId() == null ? other.getEntityId() == null : this.getEntityId().equals(other.getEntityId()))
            && (this.getEntityName() == null ? other.getEntityName() == null : this.getEntityName().equals(other.getEntityName()))
            && (this.getChargingType() == null ? other.getChargingType() == null : this.getChargingType().equals(other.getChargingType()))
            && (this.getCouponAmount() == null ? other.getCouponAmount() == null : this.getCouponAmount().equals(other.getCouponAmount()))
            && (this.getSuperviseStatus() == null ? other.getSuperviseStatus() == null : this.getSuperviseStatus().equals(other.getSuperviseStatus()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getDealSid() == null) ? 0 : getDealSid().hashCode());
        result = prime * result + ((getFlowNo() == null) ? 0 : getFlowNo().hashCode());
        result = prime * result + ((getEnvType() == null) ? 0 : getEnvType().hashCode());
        result = prime * result + ((getEnvName() == null) ? 0 : getEnvName().hashCode());
        result = prime * result + ((getType() == null) ? 0 : getType().hashCode());
        result = prime * result + ((getTradeType() == null) ? 0 : getTradeType().hashCode());
        result = prime * result + ((getTradeChannel() == null) ? 0 : getTradeChannel().hashCode());
        result = prime * result + ((getTradeNo() == null) ? 0 : getTradeNo().hashCode());
        result = prime * result + ((getOrderNo() == null) ? 0 : getOrderNo().hashCode());
        result = prime * result + ((getBillNo() == null) ? 0 : getBillNo().hashCode());
        result = prime * result + ((getRemark() == null) ? 0 : getRemark().hashCode());
        result = prime * result + ((getBillingCycle() == null) ? 0 : getBillingCycle().hashCode());
        result = prime * result + ((getAmount() == null) ? 0 : getAmount().hashCode());
        result = prime * result + ((getBalance() == null) ? 0 : getBalance().hashCode());
        result = prime * result + ((getBalanceCredit() == null) ? 0 : getBalanceCredit().hashCode());
        result = prime * result + ((getAccountSid() == null) ? 0 : getAccountSid().hashCode());
        result = prime * result + ((getAccountName() == null) ? 0 : getAccountName().hashCode());
        result = prime * result + ((getOrgSid() == null) ? 0 : getOrgSid().hashCode());
        result = prime * result + ((getUserSid() == null) ? 0 : getUserSid().hashCode());
        result = prime * result + ((getCreatedBy() == null) ? 0 : getCreatedBy().hashCode());
        result = prime * result + ((getCreatedDt() == null) ? 0 : getCreatedDt().hashCode());
        result = prime * result + ((getUpdatedBy() == null) ? 0 : getUpdatedBy().hashCode());
        result = prime * result + ((getUpdatedDt() == null) ? 0 : getUpdatedDt().hashCode());
        result = prime * result + ((getVersion() == null) ? 0 : getVersion().hashCode());
        result = prime * result + ((getDealTime() == null) ? 0 : getDealTime().hashCode());
        result = prime * result + ((getBalanceCash() == null) ? 0 : getBalanceCash().hashCode());
        result = prime * result + ((getCashAmount() == null) ? 0 : getCashAmount().hashCode());
        result = prime * result + ((getDeductCashNo() == null) ? 0 : getDeductCashNo().hashCode());
        result = prime * result + ((getDeductBalanceCash() == null) ? 0 : getDeductBalanceCash().hashCode());
        result = prime * result + ((getEntityId() == null) ? 0 : getEntityId().hashCode());
        result = prime * result + ((getEntityName() == null) ? 0 : getEntityName().hashCode());
        result = prime * result + ((getChargingType() == null) ? 0 : getChargingType().hashCode());
        result = prime * result + ((getCouponAmount() == null) ? 0 : getCouponAmount().hashCode());
        result = prime * result + ((getSuperviseStatus() == null) ? 0 : getSuperviseStatus().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", dealSid=").append(dealSid);
        sb.append(", flowNo=").append(flowNo);
        sb.append(", envType=").append(envType);
        sb.append(", envName=").append(envName);
        sb.append(", type=").append(type);
        sb.append(", tradeType=").append(tradeType);
        sb.append(", tradeChannel=").append(tradeChannel);
        sb.append(", tradeNo=").append(tradeNo);
        sb.append(", orderNo=").append(orderNo);
        sb.append(", billNo=").append(billNo);
        sb.append(", remark=").append(remark);
        sb.append(", billingCycle=").append(billingCycle);
        sb.append(", amount=").append(amount);
        sb.append(", balance=").append(balance);
        sb.append(", balanceCredit=").append(balanceCredit);
        sb.append(", accountSid=").append(accountSid);
        sb.append(", accountName=").append(accountName);
        sb.append(", orgSid=").append(orgSid);
        sb.append(", userSid=").append(userSid);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdDt=").append(createdDt);
        sb.append(", updatedBy=").append(updatedBy);
        sb.append(", updatedDt=").append(updatedDt);
        sb.append(", version=").append(version);
        sb.append(", dealTime=").append(dealTime);
        sb.append(", balanceCash=").append(balanceCash);
        sb.append(", cashAmount=").append(cashAmount);
        sb.append(", deductCashNo=").append(deductCashNo);
        sb.append(", deductBalanceCash=").append(deductBalanceCash);
        sb.append(", entityId=").append(entityId);
        sb.append(", entityName=").append(entityName);
        sb.append(", chargingType=").append(chargingType);
        sb.append(", couponAmount=").append(couponAmount);
        sb.append(", superviseStatus=").append(superviseStatus);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}