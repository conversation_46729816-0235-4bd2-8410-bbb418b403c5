package cn.com.cloudstar.rightcloud.data.response.market;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;


/**
 * @description 客户管理商品金额汇总信息
 * <AUTHOR>
 * @date 2023/9/5 15:28
 */
@Data
public class MarketShopPriceDetailResp implements Serializable {

    private static final long serialVersionUID = -8814419762950267229L;

    /**
     * 供应商名称
     */
    private String userName;

    /**
     * 上架商品数量
     */
    private Long onlineNum;

    /**
     * 订阅单数量
     */
    private Long subscribeNum;

    /**
     * 订阅单总金额
     */
    private BigDecimal subscribeTotalAmount;

    /**
     * 可结算金额
     */
    private BigDecimal unsettleAmount;

    /**
     * 已结算金额
     */
    private BigDecimal settledAmount;

    /**
     * 监管中订阅单数量
     */
    private Long subscribeNumInRegulation;

    /**
     * 监管中订阅单金额
     */
    private BigDecimal subscribeAmountInRegulation;

}
