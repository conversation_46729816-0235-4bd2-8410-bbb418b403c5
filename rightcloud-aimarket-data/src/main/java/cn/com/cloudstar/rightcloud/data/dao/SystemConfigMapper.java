/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.data.dao;

import cn.com.cloudstar.rightcloud.basic.data.pojo.config.SysConfig;
import cn.com.cloudstar.rightcloud.common.dto.Criteria;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2017/11/9
 */

@Repository
public interface SystemConfigMapper {

    /**
     * 显示list
     */
    List<SysConfig> displaySystemConfigList(Criteria example);

    /**
     * 新增一条
     */
    int insert(SysConfig sysconfig);

    /**
     * update
     */
    int updateByConfigKey(SysConfig sysconfig);

    /**
     * 根据主键更新value
     */
    int updateConfigValueByPrimaryKey(SysConfig sysconfig);

    /**
     * 翻页计算总数
     */
    int countByParams(Criteria example);

    /**
     * 根据configKey修改configValue
     *
     * @param sysconfig
     * @return
     */
    int updateValueByKey(SysConfig sysconfig);

    /**
     * 根据configKey修改configValue
     *
     * @param sysconfig
     * @return
     */
    int displayByPrimaryKey(SysConfig sysconfig);

    /**
     * 不用授权可获取显示配置信息
     *
     * @param criteria
     * @return List<SysConfig> 配置信息
     */
    List<SysConfig> nonAuthSystemConfigList(Criteria criteria);

    SysConfig selectByKey(String configKey);
}
