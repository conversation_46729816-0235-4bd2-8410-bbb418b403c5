package cn.com.cloudstar.rightcloud.data.request.market;

import java.io.Serializable;
import java.util.List;

import lombok.Data;

import cn.com.cloudstar.rightcloud.module.support.access.constants.EnumValue;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.BaseRequest;

/**
 * <AUTHOR>
 * @description 门户网站商品列表入参
 * @date 2023/8/11 16:58
 */
@Data
public class MarketShopPortalRequest extends BaseRequest implements Serializable {

    /**
     * 商品名称
     */
    private String shopName;

    /**
     * 关键词
     */
    private String keyWord;

    /**
     * 商品类别：0 我的算法 ，1 其他 ， 2 开源项目
     */
    @EnumValue(intValues = {0, 1, 2, 3, 4}, message = "商品类别不合法！")
    private Integer shopType;

    /**
     * 商品类别：0 AI模型市场 ，1AI算法集市 ，2开源项目
     * 以逗号分隔（0,1）
     */
    private String shopTypes;

    /**
     * 商品简介
     */
    private String introduce;

    /**
     * 商品标签
     */
    private List<Long> labelIds;

}
