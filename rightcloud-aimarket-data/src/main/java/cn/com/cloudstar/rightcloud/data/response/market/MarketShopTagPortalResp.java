package cn.com.cloudstar.rightcloud.data.response.market;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * 商品标签表
 * <AUTHOR>
 */
@Data
public class MarketShopTagPortalResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * tag名称
     */
    private String tagName;

    /**
     * 父id
     */
    private Long parentId;

    /**
     * 标签类别
     */
    private String tagCategory;

    /**
     * 类别名称
     */
    private String categoryName;

    /**
     * 商品类别：0 我的算法 ，1 其他 ， 2 开源项目
     */
    private Integer shopType;

    /**
     * 类别名称
     */
    private List<MarketShopTagPortalResp> marketShopTag;

}
