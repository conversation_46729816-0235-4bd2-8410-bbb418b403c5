package cn.com.cloudstar.rightcloud.data.request.market;


import cn.com.cloudstar.rightcloud.common.dto.SkuEnumDto;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 市场询价要求
 *
 * <AUTHOR>
 * @date 2023/08/01
 */
@Data
public class MarketInquiryPriceBase implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商品id
     */
    @NotBlank(message = "商品id不能为空")
    private String shopId;

    /**
     * 数量：几年几月
     */
    @NotNull(message = "数量不能为空")
    private Integer amount;

    /**
     * 询价类型：year:按月;month按年;one-time按次;
     */
    private String periodType;

    /**
     * 商品是否免费：0免费，2按月，3按年
     */
    private Integer sellType;


    /**
     * 选中的规格版本
     */
    private List<SkuEnumDto> skuInfo;

    /**
     * AI模型市场 价格规格参数
     */
    private Long priceJoinId;

}
