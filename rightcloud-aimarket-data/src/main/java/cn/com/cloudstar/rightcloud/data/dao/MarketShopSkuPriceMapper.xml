<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.cloudstar.rightcloud.data.dao.MarketShopSkuPriceMapper">

    <resultMap id="BaseResultMap" type="cn.com.cloudstar.rightcloud.data.entity.MarketShopSkuPrice">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="skuName" column="sku_name" jdbcType="VARCHAR"/>
            <result property="yearlyPrice" column="yearly_price" jdbcType="DECIMAL"/>
            <result property="monthlyPrice" column="monthly_price" jdbcType="DECIMAL"/>
            <result property="oneTimePrice" column="one_time_price" jdbcType="DECIMAL"/>
            <result property="shopId" column="shop_id" jdbcType="BIGINT"/>
            <result property="userSid" column="user_sid" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,sku_name,yearly_price,
        monthly_price,one_time_price,shop_id,
        user_sid
    </sql>
</mapper>
