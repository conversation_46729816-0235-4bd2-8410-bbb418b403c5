<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.cloudstar.rightcloud.data.dao.SysMMsgReceiveContactMapper">
    <sql id="Base_Column_List">A.id, A.name, A.msg_ids, A.config_id, A.phone, A.phone_validate, A.email, A.email_validate, A.description, A.owner_id, A.org_sid, A.created_org_sid, A.version, A.created_by, A.created_dt, A.updated_by, A.updated_dt,A.default_contact,A.user_sid</sql>
    <sql id="Example_Where_Clause">
    <trim prefixOverrides="and|or">
        <if test="id != null">
            and A.id = #{id}
        </if>
        <if test="msgIds != null and msgIds != ''">
            and A.msg_ids = #{msgIds}
        </if>
        <if test="userSid != null">
            and A.user_sid = #{userSid}
        </if>
        <if test="configId != null and configId != ''">
            and A.config_id = #{configId}
        </if>
        <if test="name != null and name != ''">
            and A.name = #{name}
        </if>
        <if test="phone != null and phone != ''">
            and A.phone = #{phone}
        </if>
        <if test="phoneValidate != null">
            and A.phone_validate = #{phoneValidate}
        </if>
        <if test="email != null and email != ''">
            and A.email = #{email}
        </if>
        <if test="emailValidate != null">
            and A.email_validate = #{emailValidate}
        </if>
        <if test="description != null and description != ''">
            and A.description = #{description}
        </if>
        <if test="ownerId != null and ownerId != ''">
            and A.owner_id = #{ownerId}
        </if>
        <if test="orgSid != null">
            and A.org_sid = #{orgSid}
        </if>
        <if test="createdOrgSid != null">
            and A.created_org_sid = #{createdOrgSid}
        </if>
        <if test="version != null">
            and A.version = #{version}
        </if>
        <if test="createdBy != null and createdBy != ''">
            and A.created_by = #{createdBy}
        </if>
        <if test="createdDt != null">
            and A.created_dt = #{createdDt}
        </if>
        <if test="updatedBy != null and updatedBy != ''">
            and A.updated_by = #{updatedBy}
        </if>
        <if test="updatedDt != null">
            and A.updated_dt = #{updatedDt}
        </if>
        <if test="configIds != null">
            and A.config_id in
            <foreach item="item" index="index" collection="configIds"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and u.status =1
    </trim>
    </sql>

    <!--查询单个-->
    <select id="queryById"
            resultType="cn.com.cloudstar.rightcloud.data.vo.msg.SysMMsgReceiveContact">
        select
        <include refid="Base_Column_List"/>
        from sys_m_msg_receive_contact A
        where id = #{id}
    </select>

    <!--查询指定行数据-->
    <select id="queryAll"
            resultType="cn.com.cloudstar.rightcloud.data.vo.msg.SysMMsgReceiveContact">
        select
        <include refid="Base_Column_List"/>,u.user_sid,u.account
        from sys_m_msg_receive_contact A
        inner join sys_m_user u on u.user_sid = A.user_sid
        <where>
            <include refid="Example_Where_Clause"/>
        </where>
    </select>
    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into sys_m_msg_receive_contact(msg_ids,config_id, name, phone, phone_validate, email, email_validate, description, owner_id, org_sid, created_org_sid, version, created_by, created_dt, updated_by, updated_dt,default_contact,user_sid)
        values (#{msgIds},#{configId}, #{name}, #{phone}, #{phoneValidate}, #{email}, #{emailValidate}, #{description}, #{ownerId}, #{orgSid}, #{createdOrgSid}, #{version}, #{createdBy}, #{createdDt}, #{updatedBy}, #{updatedDt}, #{defaultContact}, #{userSid})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into sys_m_msg_receive_contact(msg_ids,config_id, name, phone, phone_validate, email, email_validate, description, owner_id, org_sid, created_org_sid, version, created_by, created_dt, updated_by, updated_dt,default_contact,user_sid)
        values
        <foreach collection="entities" item="entity" separator=",">
        (#{entity.msgIds},#{entity.configId}, #{entity.name},
            <choose>
                <when test="entity.phone != null and entity.phone != ''">
                    #{entity.phone},
                </when>
                <otherwise>
                    (SELECT mobile FROM sys_m_user WHERE user_sid = #{entity.userSid}),
                </otherwise>
            </choose>
         #{entity.phoneValidate},
            <choose>
                <when test="entity.email != null and entity.email != ''">
                    #{entity.email},
                </when>
                <otherwise>
                    (SELECT email FROM sys_m_user WHERE user_sid = #{entity.userSid}),
                </otherwise>
            </choose>
         #{entity.emailValidate}, #{entity.description}, #{entity.ownerId}, #{entity.orgSid}, #{entity.createdOrgSid}, #{entity.version}, #{entity.createdBy}, #{entity.createdDt}, #{entity.updatedBy}, #{entity.updatedDt}, #{entity.defaultContact}, #{entity.userSid})
        </foreach>
    </insert>

    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into sys_m_msg_receive_contact(msg_ids,config_id, name, phone, phone_validate, email, email_validate, description, owner_id, org_sid, created_org_sid, version, created_by, created_dt, updated_by, updated_dt,user_sid)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.msgIds},#{entity.configId}, #{entity.name}, #{entity.phone}, #{entity.phoneValidate}, #{entity.email}, #{entity.emailValidate}, #{entity.description}, #{entity.ownerId}, #{entity.orgSid}, #{entity.createdOrgSid}, #{entity.version}, #{entity.createdBy}, #{entity.createdDt}, #{entity.updatedBy}, #{entity.updatedDt}, #{entity.user_sid})
        </foreach>
        on duplicate key update
        msg_ids = values(msg_ids),
        config_id = values(config_id),
        name = values(name),
        phone = values(phone),
        phone_validate = values(phone_validate),
        email = values(email),
        email_validate = values(email_validate),
        description = values(description),
        owner_id = values(owner_id),
        org_sid = values(org_sid),
        created_org_sid = values(created_org_sid),
        version = values(version),
        created_by = values(created_by),
        created_dt = values(created_dt),
        updated_by = values(updated_by),
        updated_dt = values(updated_dt),
        user_sid = values(user_sid)
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update sys_m_msg_receive_contact
        <set>
            <if test="msgIds != null and msgIds != ''">
                msg_ids = #{msgIds},
            </if>
            <if test="configId != null and configId != ''">
                config_id = #{configId},
            </if>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="phone != null and phone != ''">
                phone = #{phone},
            </if>
            <if test="phoneValidate != null">
                phone_validate = #{phoneValidate},
            </if>
            <if test="email != null and email != ''">
                email = #{email},
            </if>
            <if test="emailValidate != null">
                email_validate = #{emailValidate},
            </if>
            <if test="description != null and description != ''">
                description = #{description},
            </if>
            <if test="ownerId != null and ownerId != ''">
                owner_id = #{ownerId},
            </if>
            <if test="orgSid != null">
                org_sid = #{orgSid},
            </if>
            <if test="createdOrgSid != null">
                created_org_sid = #{createdOrgSid},
            </if>
            <if test="version != null">
                version = #{version},
            </if>
            <if test="createdBy != null and createdBy != ''">
                created_by = #{createdBy},
            </if>
            <if test="createdDt != null">
                created_dt = #{createdDt},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                updated_by = #{updatedBy},
            </if>
            <if test="updatedDt != null">
                updated_dt = #{updatedDt},
            </if>
        </set>
        where name = #{name} and
        <choose>
            <when test="email != null and email != ''">
                email = #{email}
            </when>
            <otherwise>
                phone = #{phone}
            </otherwise>
        </choose>
    </update>
    <update id="updateReceiveByUserId">
        UPDATE sys_m_msg_receive_contact
        <set>
            <if test="type == 'email'">
                email = (SELECT email FROM sys_m_user WHERE user_sid = #{userSid}),
                email_validate = 1,
            </if>
            <if test="type == 'mobile'">
                phone = (SELECT mobile FROM sys_m_user WHERE user_sid = #{userSid}),
                phone_validate = 1,
            </if>
        </set>
        WHERE
            user_sid = #{userSid}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from sys_m_msg_receive_contact where
        <trim prefixOverrides="and|or">
            <if test="ids != null">
                and id in
                <foreach item="item" index="index" collection="ids"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </trim>
    </delete>
    <delete id="deleteByParam">
        delete from sys_m_msg_receive_contact where
        <trim prefixOverrides="and|or">
            <if test="configIds != null">
                and config_id in
                <foreach item="item" index="index" collection="configIds"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="userSid != null">
                and user_sid =#{userSid}
            </if>
            <if test="defaultContactNotEq != null">
                and (default_contact is null or default_contact = 0)
            </if>
        </trim>
    </delete>

</mapper>

