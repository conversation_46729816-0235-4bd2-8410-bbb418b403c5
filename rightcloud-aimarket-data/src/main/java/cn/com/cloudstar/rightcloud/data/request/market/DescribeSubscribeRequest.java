package cn.com.cloudstar.rightcloud.data.request.market;

import cn.com.cloudstar.rightcloud.module.support.access.pojo.BaseRequest;
import lombok.Data;


/**
 * 导出订阅要求
 *
 * <AUTHOR>
 * @date 2023/08/21
 */
@Data
public class DescribeSubscribeRequest {

    /**
     * 商品标题
     */
    private String shopTitle;

    /**
     * 监管状态
     */
    private String superviseStatus;

    /**
     * 商品规格
     */
    private String unit;

    /**
     * 商品类别：0 我的算法 ，1 其他 ， 2 开源项目
     */
    private Integer shopType;

    /**
     * 客户名称
     */
    private String custOrgName;

    /**
     * 供应商名称
     */
    private String orgName;

    /**
     * 结算状态
     */
    private String settlementStatus;

    /**
     * 请求来源
     */
    private Integer from;

}
