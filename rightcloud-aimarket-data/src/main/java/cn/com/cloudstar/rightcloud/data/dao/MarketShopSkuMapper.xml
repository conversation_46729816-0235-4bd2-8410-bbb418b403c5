<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.cloudstar.rightcloud.data.dao.MarketShopSkuMapper">

    <resultMap id="BaseResultMap" type="cn.com.cloudstar.rightcloud.data.entity.MarketShopSku">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="attrName" column="attr_name" jdbcType="VARCHAR"/>
            <result property="type" column="type" jdbcType="VARCHAR"/>
            <result property="unit" column="unit" jdbcType="VARCHAR"/>
            <result property="userSid" column="user_sid" jdbcType="BIGINT"/>
            <result property="enumValues" column="enum_values" jdbcType="VARCHAR"/>
            <result property="availableStatus" column="available_status" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        a.id,a.attr_name,a.type,
        a.unit,a.user_sid,a.enum_values,available_status
    </sql>
    <select id="selectListByShopId" resultType="cn.com.cloudstar.rightcloud.data.entity.MarketShopSku">
        select
            <include refid="Base_Column_List" />
        from
        market_shop_sku a,market_shop_sku_relevance b
        where b.shop_id = #{shopId} and a.id = b.sku_id
    </select>
</mapper>
