/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.data.entity;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import lombok.Data;

import cn.com.cloudstar.rightcloud.common.encryptdata.annotation.CCSPEncryptDecrypt;
import cn.com.cloudstar.rightcloud.common.encryptdata.annotation.CCSPProcessVerifyClass;
import cn.com.cloudstar.rightcloud.common.encryptdata.annotation.EncryptDecryptClass;

/**
 * @TableName hcso_user
 */
@Data
@EncryptDecryptClass
@CCSPProcessVerifyClass(needCCSPEncryptDecrypt = true,macEncryptDecrypt = false)
public class HcsoUser implements Serializable {

    /**
     * @mbg.generated Wed Jan 20 10:30:50 CST 2021
     */
    private Long id;


    /**
     * 云环境类型，如VMware，OpenStack，<PERSON>yun等
     *
     * @mbg.generated Wed Jan 20 10:30:50 CST 2021
     */
    private String cloudEnvType;


    /**
     * HCSO账户ID->domian
     *
     * @mbg.generated Wed Jan 20 10:30:50 CST 2021
     */
    private String accountId;

    /**
     * HCSO账户名
     *
     * @mbg.generated Wed Jan 20 10:30:50 CST 2021
     */
    private String accountName;

    /**
     * HCSO提供的原始账户密码
     *
     * @mbg.generated Wed Jan 20 10:30:50 CST 2021
     */
    private String password;

    /**
     * HCSO提供的Ak
     *
     * @mbg.generated Wed Jan 20 10:30:50 CST 2021
     */
    @CCSPEncryptDecrypt
    private String ak;

    /**
     * HCSO提供的SK
     *
     * @mbg.generated Wed Jan 20 10:30:50 CST 2021
     */
    @CCSPEncryptDecrypt
    private String sk;

    /**
     * 使用类型account（使用账户）, aksk(使用AK,SK)
     *
     * @mbg.generated Wed Jan 20 10:30:50 CST 2021
     */
    private String type;

    /**
     * available（可用） used（已使用）
     *
     * @mbg.generated Wed Jan 20 10:30:50 CST 2021
     */
    private String status;

    /**
     * 关联的运营平台账户ID
     *
     * @mbg.generated Wed Jan 20 10:30:50 CST 2021
     */
    private Long refUserId;
    /**
     * 项目 id-> tanentID
     *
     * @mbg.generated Wed Jan 20 10:30:50 CST 2021
     */
    private String projectId;
    /**
     * OBS id-> OBS ID
     *
     * @mbg.generated Wed Jan 20 10:30:50 CST 2021
     */
    private String obsProjectId;

    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database table hcso_user
     *
     * @mbg.generated Wed Jan 20 10:30:50 CST 2021
     */
    private static final long serialVersionUID = 1L;

    /**
     * 租户类型：1是租户级，2是平台级
     */
    private Integer accountType;

    /**
     * 数据更新时是否需要跳过国密更新策略
     * true 跳过国密校验并且不执行数据补偿
     * false 默认 国密校验并且执行数据补偿
     */
    @TableField(exist = false)
    private Boolean skipCCSPHandle;

    /**
     * 国密完整性计算的hash值
     */
    @TableField(exist = false)
    private String ccspMac = "";
}
