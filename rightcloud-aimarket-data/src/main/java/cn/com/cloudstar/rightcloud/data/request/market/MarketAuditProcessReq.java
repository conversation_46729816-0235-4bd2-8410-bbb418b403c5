package cn.com.cloudstar.rightcloud.data.request.market;

import cn.com.cloudstar.rightcloud.module.support.access.constants.EnumValue;
import org.hibernate.validator.constraints.Length;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import cn.com.cloudstar.rightcloud.module.support.access.pojo.BaseRequest;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MarketAuditProcessReq extends BaseRequest {

    @ApiModelProperty(value = "订单号", example = "20210811100000000000000000000001")
    @NotEmpty(message = "订单号不能为空")
    String orderNo;
    @ApiModelProperty(value = "处理状态", reference = "APPROVE:通过 REFUSE:拒绝", example = "REFUSE")
    @EnumValue(strValues = {"APPROVE","REFUSE"},message = "必须为指定范围内的值!")
    String processStatus;
    @ApiModelProperty(value = "审核意见", example = "审核意见")
    @Length(min = 2, max = 4000)
    String auditOpinion;
}
