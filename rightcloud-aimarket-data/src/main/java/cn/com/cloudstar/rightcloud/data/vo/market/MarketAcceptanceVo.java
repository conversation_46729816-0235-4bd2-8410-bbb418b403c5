package cn.com.cloudstar.rightcloud.data.vo.market;

import cn.com.cloudstar.rightcloud.data.entity.MarketShopSubscribe;
import cn.com.cloudstar.rightcloud.data.entity.ServiceOrder;
import cn.com.cloudstar.rightcloud.data.request.market.MarketAcceptanceReq;
import lombok.Builder;
import lombok.Data;


/**
 * <AUTHOR>
 * Created on 2023/8/3
 */
@Data
@Builder
public class MarketAcceptanceVo {
    /**
     * 订单
     */
    private ServiceOrder serviceOrder;

    /**
     * 订阅
     */
    private MarketShopSubscribe shopSubscribe;

    /**
     * 处理意见
     */
    private String process;

    /**
     * 身份验证用户
     */
    private Long userSid;

    /**
     * 请求参数
     */
    private MarketAcceptanceReq acceptanceReq;

}
