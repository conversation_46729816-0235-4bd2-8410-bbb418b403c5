package cn.com.cloudstar.rightcloud.data.response.cfn;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/8/23 14:11
 */
@Data
public class AlgorithmPageResp {

    /**
     * 算法id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 作业名称
     */
    private String name;


    /**
     * 引擎
     */
    private String engineName;


    /**
     * 引擎版本
     */
    private String engineId;

    /**
     * 镜像名称
     */
    private String imageName;


    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDt;

    private Long inputImageId;

}
