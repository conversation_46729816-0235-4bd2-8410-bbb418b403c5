package cn.com.cloudstar.rightcloud.data.vo.market;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * @description 大屏查询资金监管服务订单数据对象
 * <AUTHOR>
 * @date 2023/9/8 15:28
 */
@Data
public class MarketSuperviseOrderVo implements Serializable {

    private static final long serialVersionUID = -8814419762950267229L;

    /**
     * 周期
     */
    private String period;

    /**
     * 数据日期
     */
    private Date dateTime;

    /**
     * 总订单数
     */
    private BigDecimal totalAmount;

    /**
     * 已完成订单数
     */
    private BigDecimal completedAmount;
}
