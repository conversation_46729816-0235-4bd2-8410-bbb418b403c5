<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.cloudstar.rightcloud.data.dao.SysMOrgMapper">

    <resultMap id="BaseResultMap" type="cn.com.cloudstar.rightcloud.data.entity.SysMOrg">
            <id property="orgSid" column="org_sid" jdbcType="BIGINT"/>
            <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
            <result property="orgCode" column="org_code" jdbcType="VARCHAR"/>
            <result property="orgType" column="org_type" jdbcType="VARCHAR"/>
            <result property="owner" column="owner" jdbcType="BIGINT"/>
            <result property="treePath" column="tree_path" jdbcType="VARCHAR"/>
            <result property="parentId" column="parent_id" jdbcType="BIGINT"/>
            <result property="orgIcon" column="org_icon" jdbcType="VARCHAR"/>
            <result property="province" column="province" jdbcType="BIGINT"/>
            <result property="city" column="city" jdbcType="BIGINT"/>
            <result property="area" column="area" jdbcType="BIGINT"/>
            <result property="address" column="address" jdbcType="VARCHAR"/>
            <result property="contactName" column="contact_name" jdbcType="VARCHAR"/>
            <result property="contactPosition" column="contact_position" jdbcType="VARCHAR"/>
            <result property="contactPhone" column="contact_phone" jdbcType="VARCHAR"/>
            <result property="quotaCtrl" column="quota_ctrl" jdbcType="CHAR"/>
            <result property="quotaMode" column="quota_mode" jdbcType="VARCHAR"/>
            <result property="fax" column="fax" jdbcType="VARCHAR"/>
            <result property="description" column="description" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="tenantIds" column="tenant_ids" jdbcType="VARCHAR"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
            <result property="createdDt" column="created_dt" jdbcType="TIMESTAMP"/>
            <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
            <result property="updatedDt" column="updated_dt" jdbcType="TIMESTAMP"/>
            <result property="version" column="version" jdbcType="BIGINT"/>
            <result property="ldapOu" column="ldap_ou" jdbcType="VARCHAR"/>
            <result property="skip2fa" column="skip2FA" jdbcType="TINYINT"/>
            <result property="applicationScenario" column="application_scenario" jdbcType="VARCHAR"/>
            <result property="personnelSize" column="personnel_size" jdbcType="VARCHAR"/>
            <result property="industryType" column="industry_type" jdbcType="VARCHAR"/>
            <result property="businessLicenseType" column="business_license_type" jdbcType="VARCHAR"/>
            <result property="businessLicenseUrl" column="business_license_url" jdbcType="VARCHAR"/>
            <result property="legalPerson" column="legal_person" jdbcType="VARCHAR"/>
            <result property="legalPersonCard" column="legal_person_card" jdbcType="VARCHAR"/>
            <result property="powerAttorneyUrl" column="power_attorney_url" jdbcType="VARCHAR"/>
            <result property="certificationStatus" column="certification_status" jdbcType="VARCHAR"/>
            <result property="applyStatus" column="apply_status" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="identityType" column="identity_type" jdbcType="VARCHAR"/>
            <result property="solution" column="solution" jdbcType="VARCHAR"/>
            <result property="empowerPerson" column="empower_person" jdbcType="VARCHAR"/>
            <result property="empowerPersonCard" column="empower_person_card" jdbcType="VARCHAR"/>
            <result property="empowerIdCardFront" column="empower_id_card_front" jdbcType="VARCHAR"/>
            <result property="empowerIdCardReverse" column="empower_id_card_reverse" jdbcType="VARCHAR"/>
            <result property="legalIdCardFront" column="legal_id_card_front" jdbcType="VARCHAR"/>
            <result property="legalIdCardReverse" column="legal_id_card_reverse" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        org_sid,org_name,org_code,
        org_type,owner,tree_path,
        parent_id,org_icon,province,
        city,area,address,
        contact_name,contact_position,contact_phone,
        quota_ctrl,quota_mode,fax,
        description,status,tenant_ids,
        created_by,created_dt,updated_by,
        updated_dt,version,ldap_ou,
        skip2FA,application_scenario,personnel_size,
        industry_type,business_license_type,business_license_url,
        legal_person,legal_person_card,power_attorney_url,
        certification_status,apply_status,remark,
        identity_type,solution,empower_person,
        empower_person_card,empower_id_card_front,empower_id_card_reverse,
        legal_id_card_front,legal_id_card_reverse
    </sql>
</mapper>
