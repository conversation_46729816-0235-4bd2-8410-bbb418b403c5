package cn.com.cloudstar.rightcloud.data.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * 组织架构表
 * @TableName sys_m_org
 */
@TableName(value ="sys_m_org")
@Data
public class SysMOrg implements Serializable {
    /**
     * 机构SID
     */
    @TableId(type = IdType.AUTO)
    private Long orgSid;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 机构简称
     */
    private String orgCode;

    /**
     * 机构类型：企业company、部门department、项目project
     */
    private String orgType;

    /**
     * 
     */
    private Long owner;

    /**
     * 树路径
     */
    private String treePath;

    /**
     * 父机构ID
     */
    private Long parentId;

    /**
     * 图标
     */
    private String orgIcon;

    /**
     * 所在省份
     */
    private Long province;

    /**
     * 所在城市
     */
    private Long city;

    /**
     * 所在区域
     */
    private Long area;

    /**
     * 机构地址
     */
    private String address;

    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 联系人职位
     */
    private String contactPosition;

    /**
     * 联系人电话
     */
    private String contactPhone;

    /**
     * 
     */
    private String quotaCtrl;

    /**
     * 配额控制方式
     */
    private String quotaMode;

    /**
     * 传真
     */
    private String fax;

    /**
     * 描述
     */
    private String description;

    /**
     * 状态
     */
    private String status;

    /**
     * 租户ID 
     */
    private String tenantIds;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedDt;

    /**
     * 版本号
     */
    private Long version;

    /**
     * 
     */
    private String ldapOu;

    /**
     * 跳过双因子认证
     */
    private Integer skip2fa;

    /**
     * 应用场景
     */
    private String applicationScenario;

    /**
     * 人员规模
     */
    private String personnelSize;

    /**
     * 所属行业
     */
    private String industryType;

    /**
     * 企业营业执照类型
     */
    private String businessLicenseType;

    /**
     * 企业营业执照地址
     */
    private String businessLicenseUrl;

    /**
     * 法定代表人
     */
    private String legalPerson;

    /**
     * 法定代表人身份证
     */
    private String legalPersonCard;

    /**
     * 授权书
     */
    private String powerAttorneyUrl;

    /**
     * 企业认证状态 (noAuth-待认证，authing-认证中,authSucceed-认证成功,authFiled-认证失败)
     */
    private String certificationStatus;

    /**
     * 审批状态 (2-待审核,1-审核成功,4-审核失败)
     */
    private String applyStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 核实身份（ authorizedPerson—被授权人 legalRepresentative—法人代表）
     */
    private String identityType;

    /**
     * 解决方案
     */
    private String solution;

    /**
     * 被授权人姓名
     */
    private String empowerPerson;

    /**
     * 被授权人身份证号码
     */
    private String empowerPersonCard;

    /**
     * 被授权人身份证正面图片路径
     */
    private String empowerIdCardFront;

    /**
     * 被授权人身份证反面图片路径
     */
    private String empowerIdCardReverse;

    /**
     * 法定代表人身份证正面图片路径
     */
    private String legalIdCardFront;

    /**
     * 法定代表人身份证反面图片路径
     */
    private String legalIdCardReverse;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        SysMOrg other = (SysMOrg) that;
        return (this.getOrgSid() == null ? other.getOrgSid() == null : this.getOrgSid().equals(other.getOrgSid()))
            && (this.getOrgName() == null ? other.getOrgName() == null : this.getOrgName().equals(other.getOrgName()))
            && (this.getOrgCode() == null ? other.getOrgCode() == null : this.getOrgCode().equals(other.getOrgCode()))
            && (this.getOrgType() == null ? other.getOrgType() == null : this.getOrgType().equals(other.getOrgType()))
            && (this.getOwner() == null ? other.getOwner() == null : this.getOwner().equals(other.getOwner()))
            && (this.getTreePath() == null ? other.getTreePath() == null : this.getTreePath().equals(other.getTreePath()))
            && (this.getParentId() == null ? other.getParentId() == null : this.getParentId().equals(other.getParentId()))
            && (this.getOrgIcon() == null ? other.getOrgIcon() == null : this.getOrgIcon().equals(other.getOrgIcon()))
            && (this.getProvince() == null ? other.getProvince() == null : this.getProvince().equals(other.getProvince()))
            && (this.getCity() == null ? other.getCity() == null : this.getCity().equals(other.getCity()))
            && (this.getArea() == null ? other.getArea() == null : this.getArea().equals(other.getArea()))
            && (this.getAddress() == null ? other.getAddress() == null : this.getAddress().equals(other.getAddress()))
            && (this.getContactName() == null ? other.getContactName() == null : this.getContactName().equals(other.getContactName()))
            && (this.getContactPosition() == null ? other.getContactPosition() == null : this.getContactPosition().equals(other.getContactPosition()))
            && (this.getContactPhone() == null ? other.getContactPhone() == null : this.getContactPhone().equals(other.getContactPhone()))
            && (this.getQuotaCtrl() == null ? other.getQuotaCtrl() == null : this.getQuotaCtrl().equals(other.getQuotaCtrl()))
            && (this.getQuotaMode() == null ? other.getQuotaMode() == null : this.getQuotaMode().equals(other.getQuotaMode()))
            && (this.getFax() == null ? other.getFax() == null : this.getFax().equals(other.getFax()))
            && (this.getDescription() == null ? other.getDescription() == null : this.getDescription().equals(other.getDescription()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getTenantIds() == null ? other.getTenantIds() == null : this.getTenantIds().equals(other.getTenantIds()))
            && (this.getCreatedBy() == null ? other.getCreatedBy() == null : this.getCreatedBy().equals(other.getCreatedBy()))
            && (this.getCreatedDt() == null ? other.getCreatedDt() == null : this.getCreatedDt().equals(other.getCreatedDt()))
            && (this.getUpdatedBy() == null ? other.getUpdatedBy() == null : this.getUpdatedBy().equals(other.getUpdatedBy()))
            && (this.getUpdatedDt() == null ? other.getUpdatedDt() == null : this.getUpdatedDt().equals(other.getUpdatedDt()))
            && (this.getVersion() == null ? other.getVersion() == null : this.getVersion().equals(other.getVersion()))
            && (this.getLdapOu() == null ? other.getLdapOu() == null : this.getLdapOu().equals(other.getLdapOu()))
            && (this.getSkip2fa() == null ? other.getSkip2fa() == null : this.getSkip2fa().equals(other.getSkip2fa()))
            && (this.getApplicationScenario() == null ? other.getApplicationScenario() == null : this.getApplicationScenario().equals(other.getApplicationScenario()))
            && (this.getPersonnelSize() == null ? other.getPersonnelSize() == null : this.getPersonnelSize().equals(other.getPersonnelSize()))
            && (this.getIndustryType() == null ? other.getIndustryType() == null : this.getIndustryType().equals(other.getIndustryType()))
            && (this.getBusinessLicenseType() == null ? other.getBusinessLicenseType() == null : this.getBusinessLicenseType().equals(other.getBusinessLicenseType()))
            && (this.getBusinessLicenseUrl() == null ? other.getBusinessLicenseUrl() == null : this.getBusinessLicenseUrl().equals(other.getBusinessLicenseUrl()))
            && (this.getLegalPerson() == null ? other.getLegalPerson() == null : this.getLegalPerson().equals(other.getLegalPerson()))
            && (this.getLegalPersonCard() == null ? other.getLegalPersonCard() == null : this.getLegalPersonCard().equals(other.getLegalPersonCard()))
            && (this.getPowerAttorneyUrl() == null ? other.getPowerAttorneyUrl() == null : this.getPowerAttorneyUrl().equals(other.getPowerAttorneyUrl()))
            && (this.getCertificationStatus() == null ? other.getCertificationStatus() == null : this.getCertificationStatus().equals(other.getCertificationStatus()))
            && (this.getApplyStatus() == null ? other.getApplyStatus() == null : this.getApplyStatus().equals(other.getApplyStatus()))
            && (this.getRemark() == null ? other.getRemark() == null : this.getRemark().equals(other.getRemark()))
            && (this.getIdentityType() == null ? other.getIdentityType() == null : this.getIdentityType().equals(other.getIdentityType()))
            && (this.getSolution() == null ? other.getSolution() == null : this.getSolution().equals(other.getSolution()))
            && (this.getEmpowerPerson() == null ? other.getEmpowerPerson() == null : this.getEmpowerPerson().equals(other.getEmpowerPerson()))
            && (this.getEmpowerPersonCard() == null ? other.getEmpowerPersonCard() == null : this.getEmpowerPersonCard().equals(other.getEmpowerPersonCard()))
            && (this.getEmpowerIdCardFront() == null ? other.getEmpowerIdCardFront() == null : this.getEmpowerIdCardFront().equals(other.getEmpowerIdCardFront()))
            && (this.getEmpowerIdCardReverse() == null ? other.getEmpowerIdCardReverse() == null : this.getEmpowerIdCardReverse().equals(other.getEmpowerIdCardReverse()))
            && (this.getLegalIdCardFront() == null ? other.getLegalIdCardFront() == null : this.getLegalIdCardFront().equals(other.getLegalIdCardFront()))
            && (this.getLegalIdCardReverse() == null ? other.getLegalIdCardReverse() == null : this.getLegalIdCardReverse().equals(other.getLegalIdCardReverse()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getOrgSid() == null) ? 0 : getOrgSid().hashCode());
        result = prime * result + ((getOrgName() == null) ? 0 : getOrgName().hashCode());
        result = prime * result + ((getOrgCode() == null) ? 0 : getOrgCode().hashCode());
        result = prime * result + ((getOrgType() == null) ? 0 : getOrgType().hashCode());
        result = prime * result + ((getOwner() == null) ? 0 : getOwner().hashCode());
        result = prime * result + ((getTreePath() == null) ? 0 : getTreePath().hashCode());
        result = prime * result + ((getParentId() == null) ? 0 : getParentId().hashCode());
        result = prime * result + ((getOrgIcon() == null) ? 0 : getOrgIcon().hashCode());
        result = prime * result + ((getProvince() == null) ? 0 : getProvince().hashCode());
        result = prime * result + ((getCity() == null) ? 0 : getCity().hashCode());
        result = prime * result + ((getArea() == null) ? 0 : getArea().hashCode());
        result = prime * result + ((getAddress() == null) ? 0 : getAddress().hashCode());
        result = prime * result + ((getContactName() == null) ? 0 : getContactName().hashCode());
        result = prime * result + ((getContactPosition() == null) ? 0 : getContactPosition().hashCode());
        result = prime * result + ((getContactPhone() == null) ? 0 : getContactPhone().hashCode());
        result = prime * result + ((getQuotaCtrl() == null) ? 0 : getQuotaCtrl().hashCode());
        result = prime * result + ((getQuotaMode() == null) ? 0 : getQuotaMode().hashCode());
        result = prime * result + ((getFax() == null) ? 0 : getFax().hashCode());
        result = prime * result + ((getDescription() == null) ? 0 : getDescription().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getTenantIds() == null) ? 0 : getTenantIds().hashCode());
        result = prime * result + ((getCreatedBy() == null) ? 0 : getCreatedBy().hashCode());
        result = prime * result + ((getCreatedDt() == null) ? 0 : getCreatedDt().hashCode());
        result = prime * result + ((getUpdatedBy() == null) ? 0 : getUpdatedBy().hashCode());
        result = prime * result + ((getUpdatedDt() == null) ? 0 : getUpdatedDt().hashCode());
        result = prime * result + ((getVersion() == null) ? 0 : getVersion().hashCode());
        result = prime * result + ((getLdapOu() == null) ? 0 : getLdapOu().hashCode());
        result = prime * result + ((getSkip2fa() == null) ? 0 : getSkip2fa().hashCode());
        result = prime * result + ((getApplicationScenario() == null) ? 0 : getApplicationScenario().hashCode());
        result = prime * result + ((getPersonnelSize() == null) ? 0 : getPersonnelSize().hashCode());
        result = prime * result + ((getIndustryType() == null) ? 0 : getIndustryType().hashCode());
        result = prime * result + ((getBusinessLicenseType() == null) ? 0 : getBusinessLicenseType().hashCode());
        result = prime * result + ((getBusinessLicenseUrl() == null) ? 0 : getBusinessLicenseUrl().hashCode());
        result = prime * result + ((getLegalPerson() == null) ? 0 : getLegalPerson().hashCode());
        result = prime * result + ((getLegalPersonCard() == null) ? 0 : getLegalPersonCard().hashCode());
        result = prime * result + ((getPowerAttorneyUrl() == null) ? 0 : getPowerAttorneyUrl().hashCode());
        result = prime * result + ((getCertificationStatus() == null) ? 0 : getCertificationStatus().hashCode());
        result = prime * result + ((getApplyStatus() == null) ? 0 : getApplyStatus().hashCode());
        result = prime * result + ((getRemark() == null) ? 0 : getRemark().hashCode());
        result = prime * result + ((getIdentityType() == null) ? 0 : getIdentityType().hashCode());
        result = prime * result + ((getSolution() == null) ? 0 : getSolution().hashCode());
        result = prime * result + ((getEmpowerPerson() == null) ? 0 : getEmpowerPerson().hashCode());
        result = prime * result + ((getEmpowerPersonCard() == null) ? 0 : getEmpowerPersonCard().hashCode());
        result = prime * result + ((getEmpowerIdCardFront() == null) ? 0 : getEmpowerIdCardFront().hashCode());
        result = prime * result + ((getEmpowerIdCardReverse() == null) ? 0 : getEmpowerIdCardReverse().hashCode());
        result = prime * result + ((getLegalIdCardFront() == null) ? 0 : getLegalIdCardFront().hashCode());
        result = prime * result + ((getLegalIdCardReverse() == null) ? 0 : getLegalIdCardReverse().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", orgSid=").append(orgSid);
        sb.append(", orgName=").append(orgName);
        sb.append(", orgCode=").append(orgCode);
        sb.append(", orgType=").append(orgType);
        sb.append(", owner=").append(owner);
        sb.append(", treePath=").append(treePath);
        sb.append(", parentId=").append(parentId);
        sb.append(", orgIcon=").append(orgIcon);
        sb.append(", province=").append(province);
        sb.append(", city=").append(city);
        sb.append(", area=").append(area);
        sb.append(", address=").append(address);
        sb.append(", contactName=").append(contactName);
        sb.append(", contactPosition=").append(contactPosition);
        sb.append(", contactPhone=").append(contactPhone);
        sb.append(", quotaCtrl=").append(quotaCtrl);
        sb.append(", quotaMode=").append(quotaMode);
        sb.append(", fax=").append(fax);
        sb.append(", description=").append(description);
        sb.append(", status=").append(status);
        sb.append(", tenantIds=").append(tenantIds);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdDt=").append(createdDt);
        sb.append(", updatedBy=").append(updatedBy);
        sb.append(", updatedDt=").append(updatedDt);
        sb.append(", version=").append(version);
        sb.append(", ldapOu=").append(ldapOu);
        sb.append(", skip2fa=").append(skip2fa);
        sb.append(", applicationScenario=").append(applicationScenario);
        sb.append(", personnelSize=").append(personnelSize);
        sb.append(", industryType=").append(industryType);
        sb.append(", businessLicenseType=").append(businessLicenseType);
        sb.append(", businessLicenseUrl=").append(businessLicenseUrl);
        sb.append(", legalPerson=").append(legalPerson);
        sb.append(", legalPersonCard=").append(legalPersonCard);
        sb.append(", powerAttorneyUrl=").append(powerAttorneyUrl);
        sb.append(", certificationStatus=").append(certificationStatus);
        sb.append(", applyStatus=").append(applyStatus);
        sb.append(", remark=").append(remark);
        sb.append(", identityType=").append(identityType);
        sb.append(", solution=").append(solution);
        sb.append(", empowerPerson=").append(empowerPerson);
        sb.append(", empowerPersonCard=").append(empowerPersonCard);
        sb.append(", empowerIdCardFront=").append(empowerIdCardFront);
        sb.append(", empowerIdCardReverse=").append(empowerIdCardReverse);
        sb.append(", legalIdCardFront=").append(legalIdCardFront);
        sb.append(", legalIdCardReverse=").append(legalIdCardReverse);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}