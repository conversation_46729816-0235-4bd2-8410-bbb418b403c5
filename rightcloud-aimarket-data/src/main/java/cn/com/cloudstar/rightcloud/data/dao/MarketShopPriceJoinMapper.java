package cn.com.cloudstar.rightcloud.data.dao;

import cn.com.cloudstar.rightcloud.data.entity.MarketShop;
import cn.com.cloudstar.rightcloud.data.entity.MarketShopPriceJoin;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 *
 */
public interface MarketShopPriceJoinMapper extends BaseMapper<MarketShopPriceJoin> {

    /**
     * 根据商品id查询价格规格
     * @param shopId 商品id
     * @return 数据
     */
    List<MarketShopPriceJoin> selectByShopId(String shopId);

    /**
     * 根据资产查询商品
     * @param contentId 资产id
     * @return 商品id 和状态
     */
    MarketShop selectByContentId(String contentId);
}




