package cn.com.cloudstar.rightcloud.data.vo.market;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;


/**
 * 商店sku返回列表
 *
 * <AUTHOR>
 * @date 2023/09/06
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SkuTreeVO {

    /**
     * 枚举值名称
     */
    private String label;

    /**
     * 属性名称
     */
    private String attrName;


    private List<SkuTreeVO> children;

    public SkuTreeVO(String label) {
        this.label = label;
        this.children = new ArrayList<>();
    }
}
