/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.data.request.discount;

import cn.com.cloudstar.rightcloud.module.support.access.constants.EnumValue;
import cn.com.cloudstar.rightcloud.validated.safe.SafeHtml;
import cn.com.cloudstar.rightcloud.validated.validation.NotIllegalString;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/3/19.
 */
@ApiModel(description = "编辑折扣")
@Data
public class UpdateDiscountRequest {

    /**
     * 折扣sid
     */
    @ApiModelProperty("折扣SID")
    @NotNull
    private Long discountSid;

    /**
     * 折扣名称
     */
    @ApiModelProperty("名称")
    @NotBlank
    @NotIllegalString
    @SafeHtml
    private String discountName;

    /**
     * 折扣类型
     */
    @ApiModelProperty("折扣类型 market:第三方商品折扣")
    @NotBlank
    @EnumValue(strValues = {"market"}, message = "不合法的折扣类型")
    private String discountType;

    /**
     * 折扣来源
     */
    @ApiModelProperty("折扣来源 [discount] 来源折扣")
    @EnumValue(strValues = {"discount"}, message = "不合法的折扣来源类型")
    private String originType;

    /**
     * 状态
     */
    @ApiModelProperty("状态 0 禁用  1 启用")
    private Integer status;

    /**
     * 适用环境
     */
    @ApiModelProperty("适用环境 适用的云环境，多个以，分隔")
    @NotBlank
    @Pattern(regexp = "^((?!%0d|%0a|%20).)*$",message = "输入可能存在CRLF攻击！")
    @SafeHtml
    private String cloudEnvScope;

    /**
     * 适用产品
     */
    @ApiModelProperty("适用产品 适用的产品，多个以，分隔。[ecs][disk][floatingip]")
    @NotBlank
    @Pattern(regexp = "^((?!%0d|%0a|%20).)*$",message = "输入可能存在CRLF攻击！")
    @EnumValue(strValues = {"AI-MARKET"}, message = "不合法的产品类型")
    @SafeHtml
    private String productScope;

    /**
     * 应用范围
     */
    @ApiModelProperty("应用范围 支持[quantity]按照数量，[money]按照结算时最终金额，[unlimited]无限制，直接享受折扣")
    @NotBlank
    private String scopeType;

    /**
     * 范围值
     */
    @ApiModelProperty("范围值 1. 支持范围写法，如1-+，1-1000，结束值+代表无限制 2. 支持固定数字值")
    @NotBlank
    private String scopeValue;

    /**
     * 折扣系数
     */
    @ApiModelProperty("折扣系数 0-1，最多两位小数")
    @NotNull
    @Digits(integer = 1, fraction=2, message = "discountRatio参数格式错误")
    @DecimalMin(value = "0.01")
    @DecimalMax(value = "1")
    private BigDecimal discountRatio;

    /**
     * 开始时间
     */
    @ApiModelProperty("生效时间")
    @NotNull
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty("失效时间")
    @NotNull
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 描述
     */
    @ApiModelProperty("描述")
    @SafeHtml
    @Length(max = 256, message = "最大长度不能超过256")
    @Pattern(regexp = "^((?!%0d|%0a|%20).)*$",message = "输入可能存在CRLF攻击！")
    private String description;
}
