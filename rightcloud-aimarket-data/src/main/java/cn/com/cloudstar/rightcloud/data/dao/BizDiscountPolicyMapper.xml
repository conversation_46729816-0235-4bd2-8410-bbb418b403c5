<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.cloudstar.rightcloud.data.dao.BizDiscountPolicyMapper">

    <resultMap id="BaseResultMap" type="cn.com.cloudstar.rightcloud.data.entity.BizDiscountPolicy">
            <id property="policySid" column="policy_sid" jdbcType="BIGINT"/>
            <result property="category" column="category" jdbcType="VARCHAR"/>
            <result property="userSid" column="user_sid" jdbcType="BIGINT"/>
            <result property="policyType" column="policy_type" jdbcType="VARCHAR"/>
            <result property="policyLevel" column="policy_level" jdbcType="VARCHAR"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
            <result property="createdDt" column="created_dt" jdbcType="TIMESTAMP"/>
            <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
            <result property="updatedDt" column="updated_dt" jdbcType="TIMESTAMP"/>
            <result property="version" column="version" jdbcType="INTEGER"/>
            <result property="entityId" column="entity_id" jdbcType="BIGINT"/>
            <result property="entityName" column="entity_name" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="cloudEnvScope" column="cloud_env_scope" jdbcType="VARCHAR"/>
            <result property="productScope" column="product_scope" jdbcType="VARCHAR"/>
            <result property="orgSid" column="org_sid" jdbcType="BIGINT"/>
            <result property="userAccountId" column="user_account_id" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        policy_sid,category,user_sid,
        policy_type,policy_level,created_by,
        created_dt,updated_by,updated_dt,
        version,entity_id,entity_name,
        status,cloud_env_scope,product_scope,
        org_sid,user_account_id
    </sql>
</mapper>
