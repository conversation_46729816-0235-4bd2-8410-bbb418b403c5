package cn.com.cloudstar.rightcloud.data.request.market;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import cn.com.cloudstar.rightcloud.module.support.access.pojo.BaseRequest;

/**
 * <AUTHOR>
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MarketAuditListReq extends BaseRequest {

    @ApiModelProperty(value = "审核状态", reference = "UNFINISHED 未审批，FINISHED 已审批", example = "UNFINISHED")
    String auditStatus;
    @ApiModelProperty(value = "订单号", example = "20210811100000000000000000000001")
    String orderNo;
    @ApiModelProperty(value = "结算类型", example = "按月购买")
    String payType;
    @ApiModelProperty(value = "处理状态", example = "通过")
    String processStatus;
    @ApiModelProperty(value = "商品名称", example = "xxx")
    String shopName;
}
