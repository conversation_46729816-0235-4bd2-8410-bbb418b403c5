package cn.com.cloudstar.rightcloud.data.request.market;


import cn.com.cloudstar.rightcloud.data.vo.market.StringVO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 市场商店sku修改申请
 *
 * <AUTHOR>
 * @date 2023/08/21
 */
@Data
public class MarketShopSkuUpdReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 属性id
     */
    @NotNull
    private String id;

    /**
     * 属性名称
     */
    @NotBlank
    private String attrName;
    /**
     * 属性类型
     */
    @NotBlank
    private String type;
    /**
     * 单位
     */
    private String unit;

}
