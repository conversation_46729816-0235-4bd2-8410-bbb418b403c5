package cn.com.cloudstar.rightcloud.data.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.Date;

/**
 * AI商品版本
 * @TableName market_shop_version
 */
@TableName(value ="market_shop_version")
@Data
public class MarketShopVersion implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String versionId;

    /**
     * aihub上挂载的版本id
     */

    private String aiHubVersionId;

    /**
     * 版本号
     */
    @NotBlank(message = "版本号不能为空")
    @Pattern(regexp = "([1-9]\\d|[1-9])(.([1-9]\\d|\\d)){2}",message = "版本号格式错误")
    private String versionNum;


    /**
     * 商品id
     */
    private String shopId;

    /**
     * aihub上挂载的资产id
     */
    private String aiHubContentId;

    /**
     * 版本介绍
     */
    @NotBlank(message = "版本介绍不能为空")
    private String versionDesc;

    /**
     * MA来源：默认值ModelArts
     */
    private String aiHubSource;

    /**
     * ModelArts区域，默认cn-east-325
     */
    private String aiHubArea;

    /**
     * 算法id
     */
    @NotBlank(message = "算法ID不能为空")
    private String algorithm;

    /**
     * 版本状态：0上架，1下架
     */
    private String status;



    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 修改人
     */
    private String updatedBy;

    /**
     * 修改时间
     */
    private Date updatedDt;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;


}
