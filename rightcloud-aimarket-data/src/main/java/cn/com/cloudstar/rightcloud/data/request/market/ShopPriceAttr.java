package cn.com.cloudstar.rightcloud.data.request.market;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/8/1
 * 商品价格规格
 */
@Data
public class ShopPriceAttr {


    /**
     * 价格
     */
    @NotNull(message = "价格不能为空")
    private BigDecimal price;

    /**
     * 数量：几年几月
     */
    @NotNull(message = "数量不能为空")
    private Integer unitValue;

    /**
     * 年或者月
     */
    @NotNull(message = "单位不能为空")
    private String unit;

    /**
     * 商品id
     */
    private String shopId;








}
