package cn.com.cloudstar.rightcloud.data.vo.order;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * <p>
 * 订单价格详情 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-06-19
 */
@Data
public class ServiceOrderPriceDetail implements Serializable,Cloneable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 订单Sn
     */
    private String orderSn;

    /**
     * 订单详情ID
     */
    private Long orderDetailId;

    /**
     * 服务类型
     */
    private String serviceType;

    /**
     * 计费项数量
     */
    private Integer quantity;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 总价
     */
    private BigDecimal amount;

    /**
     * 原价
     */
    private BigDecimal originalCost;

    /**
     * 折扣价
     */
    private BigDecimal discount;

    /**
     * 退订时间
     */
    private Date endTime;

    /**
     * 版本号
     */
    private Long version;

    /**
     * 资源配置描述
     */
    private String resourceConfigDesc;

    /**
     * 当前资源配置
     */
    private String resourceConfig;

    /**
     * 关联资源ID
     */
    private String refInstanceId;

    /**
     * 所属某资源ID，比如随主机创建的数据盘
     */
    private String belongId;

    /**
     * 订单资源开始时间
     */
    private Date startTime;

    /**
     * 单次收费
     */
    private BigDecimal tradePrice;

    /**
     * 单次收费
     */
    private BigDecimal oncePrice;

    /**
     * 价格类型 服务（service），配置(extraConfig)，资源(resource)
     */
    private String priceType;

    /**
     * 价格描述
     */
    private String priceDesc;

    /**
     * 规格配置
     */
    private String billingSpec;

    /**
     * 用于确定资源的从属关系，便于回写资源ID
     */
    private String refKey;

    private String chargeType;

    private Long orgSid;

    private String productCode;

    private String specType;

    private BigDecimal fixedHourPrice;

    /**
     * 优惠券金额
     */
    private BigDecimal couponAmount;

    /**
     * 折扣优惠金额
     */
    @TableField(exist = false)
    private BigDecimal orgDiscount;

    /**
     * 优惠券抵扣金额
     */
    @TableField(exist = false)
    private BigDecimal couponDiscount;

    /**
     * 按量计费单价
     */
    private BigDecimal unitHourPrice;

    /**
     *按量计费单价(折扣价)
     */
    private BigDecimal tradeUnitHourPrice;
    /**
     ** 按量计费基础价格(折扣价)
     */
    private BigDecimal tradeFixedHourPrice;

    /**
     * 类型族
     */
    private String productType;
    /**
     * 是否是固定月计费
     */
    private String fixedMonth;

    private BigDecimal giveBack = BigDecimal.ZERO;

    @TableField
    private String type;

    @TableField(exist = false)
    private String orderType;

    @TableField(exist = false)
    private String serviceId;

    @TableField(exist = false)
    private Long orderId;

    /**
     * 过期使用金额
     */
    @TableField(exist = false)
    private BigDecimal expiredUsedAmount;

    /**
     * 变更容量时记录负数补差金额
     */
    @TableField(exist = false)
    private BigDecimal negativeAmount;

    @Override
    public ServiceOrderPriceDetail clone() {
        try {
            return (ServiceOrderPriceDetail) super.clone();
        } catch (Exception e) {
            // clone error
        }
        return new ServiceOrderPriceDetail();
    }

    //余额支付
    private BigDecimal payBalance;
    //充值现金券支付
    private BigDecimal payBalanceCash;
    //信用额度支付
    private BigDecimal payCreditLine;
    /**
     * 收费规则：02:销售计费、01:正常计费
     */
    @TableField(exist = false)
    private String chargingType;

    public BigDecimal getPayBalance() {
        return Objects.isNull(this.payBalance) ? BigDecimal.ZERO : this.payBalance;
    }

    public BigDecimal getPayBalanceCash() {
        return Objects.isNull(this.payBalanceCash) ? BigDecimal.ZERO : this.payBalanceCash;
    }

    public BigDecimal getPayCreditLine() {
        return Objects.isNull(this.payCreditLine) ? BigDecimal.ZERO : this.payCreditLine;
    }
}
