package cn.com.cloudstar.rightcloud.data.response.bigscreen;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;


/**
 * @description 大屏查询最新成交动态
 * <AUTHOR>
 * @date 2023/9/7 15:28
 */
@Data
public class MarketSubscribeRecentResp implements Serializable {

    private static final long serialVersionUID = -8814419762950267229L;

    /**
     * 商品名称
     */
    private String title;

    /**
     * 成交金额
     */
    private BigDecimal subscribeDealAmount;

    /**
     * 成交时间
     */
    private Date createdDt;
}
