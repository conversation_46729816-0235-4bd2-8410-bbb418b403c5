package cn.com.cloudstar.rightcloud.data.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 商品订阅表
 * @TableName market_subscribe
 */
@TableName(value ="market_shop_subscribe")
@Data
public class MarketShopSubscribe implements Serializable {
    /**
     * 订阅id
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String subscribeId;

    /**
     * AiHub关联的订阅id
     */
    private String aiHubSubscribeId;

    /**
     * 商品id
     */
    @NotBlank(message = "商品id不能为空")
    private String shopId;

    /**
     * AiHub关联的资产id
     */
    @NotBlank(message = "资产id不能为空")
    private String aiHubContentId;

    /**
     * 价格：免费则为0
     */
    @NotNull(message = "价格不能为空")
    private BigDecimal price;

    /**
     * 时长：几年或几个月；免费则为0
     */
    @NotNull(message = "时长不能为空")
    private Integer unitValue;


    /**
     * 规格单位
     */
    @NotNull(message = "规格单位不能为空")
    private String unit;

    /**
     * 商品是否免费：0免费，1按月，2按年
     */
    @NotNull(message = "商品是否免费不能为空")
    private Integer sellType;

    /**
     * 订阅人id
     */
    private Long ownerId;

    /**
     * 商品发布人id
     */
    @NotNull(message = "商品发布人id不能为空")
    private Long shopOwnerId;


    /**
     * 订阅开始时间
     */
    private Date startTime;

    /**
     * 订阅结束时间
     */
    private Date endTime;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 完成时间
     */
    private Date completeDt;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 订阅状态
     */
    private String superviseStatus;

    /**
     * 结算状态
     */
    private String settlementStatus;

    /**
     * 计费周期id
     */
    private String billingCycleId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;


}
