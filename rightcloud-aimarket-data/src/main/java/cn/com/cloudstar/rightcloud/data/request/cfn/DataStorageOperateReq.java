package cn.com.cloudstar.rightcloud.data.request.cfn;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 操作req
 *
 * <AUTHOR>
 * @date 2024/8/23 9:38
 */
@Data
public class DataStorageOperateReq {

    /**
     * 用户名
     */
    @NotEmpty
    private String account;

    /**
     * 是否管理员
     */
    @NotNull
    private Boolean isAdmin;

    /**
     * 数据集id--发布时用 发布时必传
     */
    private String dataStorageId;

    /**
     * 数据集发布ID---订阅和退订时必传
     */
    private String publishId;

}
