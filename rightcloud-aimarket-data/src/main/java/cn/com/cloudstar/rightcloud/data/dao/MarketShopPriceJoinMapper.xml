<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.cloudstar.rightcloud.data.dao.MarketShopPriceJoinMapper">

    <resultMap id="BaseResultMap" type="cn.com.cloudstar.rightcloud.data.entity.MarketShopPriceJoin">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="contentId" column="content_id" jdbcType="VARCHAR"/>
            <result property="shopId" column="shop_id" jdbcType="VARCHAR"/>
            <result property="contentUri" column="content_uri" jdbcType="VARCHAR"/>
            <result property="unit" column="unit" jdbcType="CHAR"/>
            <result property="unitValue" column="unit_value" jdbcType="INTEGER"/>
            <result property="price" column="price" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,content_id,shop_id,content_uri,
        unit,unit_value,price
    </sql>
    <select id="selectByShopId" resultType="cn.com.cloudstar.rightcloud.data.entity.MarketShopPriceJoin">
        select
            <include refid="Base_Column_List"></include>
        from
            market_shop_price_join
        where
            shop_id = #{shopId}

    </select>
    <select id="selectByContentId" resultType="cn.com.cloudstar.rightcloud.data.entity.MarketShop">
        select t2.*
        from
            market_shop_price_join t1 left join market_shop t2 on t1.shop_id = t2.shop_id
        where
            content_id = #{contentId}

    </select>

</mapper>
