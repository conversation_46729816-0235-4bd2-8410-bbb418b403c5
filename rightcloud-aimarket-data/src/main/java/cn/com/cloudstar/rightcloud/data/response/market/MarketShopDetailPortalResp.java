package cn.com.cloudstar.rightcloud.data.response.market;


import cn.com.cloudstar.rightcloud.data.entity.MarketShopPriceJoin;
import cn.com.cloudstar.rightcloud.data.entity.MarketShopTag;
import cn.com.cloudstar.rightcloud.data.entity.MarketShopVersion;
import cn.com.cloudstar.rightcloud.data.vo.market.MarketShopSkuPriceVo;
import cn.com.cloudstar.rightcloud.data.vo.market.MarketShopSkuVo;
import cn.com.cloudstar.rightcloud.data.vo.market.SkuTreeVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/14
 */
@Data
public class MarketShopDetailPortalResp implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    private String shopId;

    /**
     * 商品标题
     */
    private String title;

    /**
     * 商品类别：0 我的算法 ，1 其他 ， 2 开源项目
     */
    private Integer shopType;

    /**
     * 商品标签
     */
    private List<MarketShopTag> labels;

    /**
     * 商品价格规格
     */
    private List<MarketShopPriceJoin> priceJoins;

    /**
     * 商品logo
     */
    private String logoPath;

    /**
     * 商品简介
     */
    private String introduce;

    /**
     * 商品说明
     */
    private String description;

    /**
     * 商品是否免费：0免费，1按月，2按年
     */
    private Integer sellType;

    /**
     * 购买方式:包年、包月、一次性购买
     */
    private String payType;

    /**
     * 规格定价List
     */
    private List<MarketShopSkuVo> shopSkuVos;

    /**
     * 添加规格定价List
     */
    private List<MarketShopSkuPriceVo> shopSkuPriceVos;

    /**
     * 规格树
     */
    private SkuTreeVO skuTreeVO;

    /**
     * 跳转链接：类型为开源项目，该值不能为空
     */
    private String jumpLink;

    /**
     * 商品版本列表
     */
    private List<MarketShopVersion> shopVersions;

}
