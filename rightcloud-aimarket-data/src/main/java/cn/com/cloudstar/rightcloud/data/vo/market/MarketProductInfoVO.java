package cn.com.cloudstar.rightcloud.data.vo.market;

import cn.com.cloudstar.rightcloud.data.request.market.MarketInquiryPriceBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;



/**
 * 产品信息
 *
 * <AUTHOR>
 * @date 2023/08/07
 */
@Data
@ApiModel(description = "产品参数详情")
public class MarketProductInfoVO extends MarketInquiryPriceBase {
    @ApiModelProperty(notes = "申请类型:模型集市/ai集市；对应商品类型0/1")
    private String applyType;

    @ApiModelProperty(notes = "云环境ID", required = true)
    private Long cloudEnvId;

    @ApiModelProperty(notes = "服务ID")
    private Long serviceId;

    @ApiModelProperty(notes = "付费类型")
    private String chargeType;

    @ApiModelProperty(notes = "产品类型，如AI-MARKET")
    private String productCode;

}
