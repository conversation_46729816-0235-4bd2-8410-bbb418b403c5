<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2018 Cloud-Star, Inc. All Rights Reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.com.cloudstar.rightcloud.data.dao.SystemConfigMapper">
    <resultMap id="BaseResultMap" type="cn.com.cloudstar.rightcloud.basic.data.pojo.config.SysConfig">
        <id column="CONFIG_SID" property="configSid" jdbcType="BIGINT"/>
        <result column="CONFIG_TYPE" property="configType" jdbcType="VARCHAR"/>
        <result column="CONFIG_NAME" property="configName" jdbcType="VARCHAR"/>
        <result column="config_name_us" property="configNameUs" jdbcType="VARCHAR"/>
        <result column="CONFIG_KEY" property="configKey" jdbcType="VARCHAR"/>
        <result column="CONFIG_VALUE" property="configValue" jdbcType="VARCHAR"/>
        <result column="VALUE_DOMAIN" property="valueDomain" jdbcType="VARCHAR"/>
        <result column="DATA_TYPE" property="dataType" jdbcType="VARCHAR"/>
        <result column="VERSION" property="version" jdbcType="BIGINT"/>
        <result column="SORT_RANK" property="sortRank" jdbcType="BIGINT"/>
        <result column="display" property="display" jdbcType="TINYINT"/>
        <result column="ccsp_mac" property="ccspMac" jdbcType="VARCHAR"/>
        <result column="ccsp_encrypt_decrypt" property="ccspEncryptDecrypt" jdbcType="VARCHAR"/>

    </resultMap>

    <sql id="Example_Where_Clause">
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.configTypeLike != null">
                and CONFIG_TYPE like CONCAT('%',#{condition.configTypeLike},'%')
            </if>
            <if test="condition.configType != null">
                and CONFIG_TYPE = #{condition.configType}
            </if>
            <if test="condition.configTypeIn != null">
                and CONFIG_TYPE IN
                <foreach collection="condition.configTypeIn" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.configKey != null">
                and CONFIG_KEY = #{condition.configKey}
            </if>
            <if test="condition.configKeyLike != null">
                and CONFIG_KEY like CONCAT('%',#{condition.configKeyLike},'%')
            </if>
            <if test="condition.configKeys != null">
                and CONFIG_KEY in
                <foreach item="key" collection="condition.configKeys" open="(" separator="," close=")">
                    #{key}
                </foreach>
            </if>
            <if test="condition.configTypes != null">
                and CONFIG_KEY in
                <foreach item="key" collection="condition.configTypes" open="(" separator="," close=")">
                    #{key}
                </foreach>
            </if>
            and display = true
        </trim>
    </sql>

    <select id="countByParams" parameterType="cn.com.cloudstar.rightcloud.common.dto.Criteria"
        resultType="java.lang.Integer">
        select count(*) from SYS_M_CONFIG
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>

    <select id="displaySystemConfigList" resultMap="BaseResultMap"
        parameterType="cn.com.cloudstar.rightcloud.common.dto.Criteria">
        SELECT
        S.CONFIG_SID,S.CONFIG_TYPE,S.CONFIG_NAME,S.config_name_us,S.CONFIG_KEY,S.CONFIG_VALUE,S.DATA_TYPE,S.VALUE_DOMAIN, S.DESCRIPTION,
        S.SORT_RANK,S.UNIT,S.DISPLAY_TYPE,S.ccsp_encrypt_decrypt,S.ccsp_mac
        FROM SYS_M_CONFIG S
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        order by S.SORT_RANK
    </select>

    <insert id="insert" parameterType="cn.com.cloudstar.rightcloud.basic.data.pojo.config.SysConfig">
        insert into sys_m_config (CONFIG_SID, CONFIG_TYPE, CONFIG_NAME, CONFIG_KEY, VERSION)
        values (#{configSid}, #{configType}, #{configName}, #{configKey}, 1)
    </insert>

    <update id="updateByConfigKey"
        parameterType="cn.com.cloudstar.rightcloud.basic.data.pojo.config.SysConfig">
        update sys_m_config
        set CONFIG_VALUE =#{configValue},
            <if test="unit != null">
                unit = #{unit},
            </if>
            UPDATED_BY = #{updatedBy},
            UPDATED_DT = #{updatedDt}
        where CONFIG_KEY = #{configKey}
    </update>

    <update id="updateConfigValueByPrimaryKey"
        parameterType="cn.com.cloudstar.rightcloud.basic.data.pojo.config.SysConfig">
        update sys_m_config
        set CONFIG_VALUE = #{configValue}
        where CONFIG_SID = #{configSid}

    </update>

    <update id="updateValueByKey"
            parameterType="cn.com.cloudstar.rightcloud.basic.data.pojo.config.SysConfig">
        update sys_m_config
        set CONFIG_VALUE = #{configValue},UPDATED_BY = #{updatedBy},UPDATED_DT = #{updatedDt}
        where CONFIG_KEY = #{configKey}
    </update>
    <update id="displayByPrimaryKey">
        update sys_m_config
        set display = #{display}
        where CONFIG_SID = #{configSid}
    </update>

    <select id="nonAuthSystemConfigList" resultMap="BaseResultMap"
            parameterType="cn.com.cloudstar.rightcloud.common.dto.Criteria">
        SELECT
        S.CONFIG_SID,S.CONFIG_TYPE,S.CONFIG_NAME,S.config_name_us,S.CONFIG_KEY,S.CONFIG_VALUE,S.DATA_TYPE,S.VALUE_DOMAIN, S.DESCRIPTION,
        S.SORT_RANK,S.UNIT,S.ccsp_encrypt_decrypt,S.ccsp_mac
        FROM SYS_M_CONFIG S
        WHERE (CONFIG_TYPE in
        <foreach item="type" collection="condition.configTypes" open="(" separator="," close=")">
            #{type}
        </foreach>
        OR CONFIG_KEY in
        <foreach item="key" collection="condition.configKeys" open="(" separator="," close=")">
            #{key}
        </foreach>
        <if test="condition.configTypes != null and condition.configTypes.size() > 0">
            OR CONFIG_TYPE in
            <foreach item="type" collection="condition.configTypes" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        )
        and display = true
        order by S.SORT_RANK
    </select>
    <select id="selectByKey" resultType="cn.com.cloudstar.rightcloud.basic.data.pojo.config.SysConfig">
        SELECT
            S.CONFIG_SID,S.CONFIG_TYPE,S.CONFIG_NAME,S.config_name_us,S.CONFIG_KEY,S.CONFIG_VALUE,S.DATA_TYPE,S.VALUE_DOMAIN, S.DESCRIPTION,
            S.SORT_RANK,S.UNIT,S.DISPLAY_TYPE,S.ccsp_encrypt_decrypt,S.ccsp_mac
        FROM SYS_M_CONFIG S
        where S.CONFIG_KEY =
              #{configKey}
    </select>

</mapper>
