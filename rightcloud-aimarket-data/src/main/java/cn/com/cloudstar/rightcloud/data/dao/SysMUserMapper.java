package cn.com.cloudstar.rightcloud.data.dao;

import cn.com.cloudstar.rightcloud.data.entity.SysMUser;
import cn.com.cloudstar.rightcloud.data.vo.market.CustomInfoVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import org.springframework.data.repository.query.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【sys_m_user(用户表)】的数据库操作Mapper
* @createDate 2023-08-16 18:46:30
* @Entity cn.com.cloudstar.rightcloud.data.entity.SysMUser
*/
public interface SysMUserMapper extends BaseMapper<SysMUser> {


    CustomInfoVo selectCustById(Long ownerId);

    /**
     * 查询注册供应商个数
     * @return int
     */
    int countProviderUserNum();

    List<Long> selectUserRole(@Param("userSid") Long userSid);

    Integer queryNumberAllTenants();
}
