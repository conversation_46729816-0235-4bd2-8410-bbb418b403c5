package cn.com.cloudstar.rightcloud.data.dao;


import cn.com.cloudstar.rightcloud.data.vo.msg.SysMMsgReceiveContact;
import cn.com.cloudstar.rightcloud.data.vo.msg.SysMMsgReceiveContactParam;
import cn.com.cloudstar.rightcloud.module.support.access.mybatis.annotation.DataFilter;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户消息接收人联系人配置表;用户消息接收人联系人配置表(SysMMsgReceiveContact)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-11-09 10:42:09
 */
@Mapper
public interface SysMMsgReceiveContactMapper {

    /**
     * 查询指定行数据
     *
     * @param sysMMsgReceiveContactParam 查询条件
     * @return 对象列表
     */
    List<SysMMsgReceiveContact> queryAll(SysMMsgReceiveContactParam sysMMsgReceiveContactParam);


    /**
     * 新增数据
     *
     * @param sysMMsgReceiveContact 实例对象
     * @return 影响行数
     */
    int insert(SysMMsgReceiveContact sysMMsgReceiveContact);

}

