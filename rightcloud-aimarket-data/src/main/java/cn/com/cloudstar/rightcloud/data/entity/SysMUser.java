package cn.com.cloudstar.rightcloud.data.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户表
 * @TableName sys_m_user
 */
@TableName(value ="sys_m_user")
@Data
public class SysMUser implements Serializable {
    /**
     * 用户SID
     */
    @TableId(type = IdType.AUTO)
    private Long userSid;

    /**
     * 用户类型
     */
    private String userType;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 账号
     */
    private String account;

    /**
     * 
     */
    private String password;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 性别 0:男 1:女
     */
    private Integer sex;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机
     */
    private String mobile;

    /**
     * 职务头衔
     */
    private String title;

    /**
     * 企业ID
     */
    private Long companyId;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 组织ID
     */
    private Long orgSid;

    /**
     * 用户状态（0:禁用，1:有效，2:锁定）
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 密码错误次数
     */
    private Integer errorCount;

    /**
     * 上次登录时间
     */
    private Date lastLoginTime;

    /**
     * 上次登录IP地址
     */
    private String lastLoginIp;

    /**
     * 账号有效开始时间
     */
    private Date startTime;

    /**
     * 账号有效开始时间
     */
    private Date endTime;

    /**
     * 服务限制数量
     */
    private Integer serviceLimitQuantity;

    /**
     * 申请理由
     */
    private String applyReason;

    /**
     * 最大短信数
     */
    private Integer smsMax;

    /**
     * 
     */
    private String uuid;

    /**
     * 用户偏好主题
     */
    private String skinTheme;

    /**
     * 第三方认证绑定ID
     */
    private String authId;

    /**
     * 账户认证类型 1. local 2. ad
     */
    private String authType;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedDt;

    /**
     * 版本号
     */
    private Long version;

    /**
     * open ID
     */
    private String openId;

    /**
     * 微信头像
     */
    private String avatarUrl;

    /**
     * 微信用户所在省
     */
    private String province;

    /**
     * 微信用户所在城市
     */
    private String city;

    /**
     * 微信用户所在国家
     */
    private String country;

    /**
     * 微信账号名称
     */
    private String wechatName;

    /**
     * IAM域ID
     */
    private String domainId;

    /**
     * IAM用户ID
     */
    private String iamId;

    /**
     * IAM密码过期时间
     */
    private String passwordExpiresAt;

    /**
     * IAM是否强制重置密码
     */
    private String forceresetpwd;

    /**
     * IAM默认项目ID
     */
    private String defaultProjectId;

    /**
     * IAM最后访问项目ID
     */
    private String lastProjectId;

    /**
     * IAM密码强度
     */
    private String pwdStrength;

    /**
     * 父账号ID
     */
    private Long parentSid;

    /**
     * noAuth 待认证, authing 认证中, authSucceed认证成功, authFiled认证失败
     */
    private String certificationStatus;

    /**
     * 身份证正面图片路径
     */
    private String idCardFront;

    /**
     * 身份证反面图片路径
     */
    private String idCardReverse;

    /**
     *  用户资源状态（0:禁用，1:启用）
     */
    private String freezeStatus;

    /**
     * 冻结类型（0为自动1为手动）
     */
    private String unfreezeType;

    /**
     * 身份证名称
     */
    private String authName;

    /**
     * 导航栏确认（已经操作过为Y）
     */
    private String navigationConfirm;

    /**
     * 所处行业
     */
    private String industry;

    /**
     * 密码结束时间
     */
    private Date pwdEndTime;

    /**
     * 是否同意隐私协议： 0未同意、1已同意
     */
    private Integer policyAgreeSign;

    /**
     * 同意隐私协议时间
     */
    private Date policyAgreeTime;

    /**
     * 解冻时间
     */
    private Date unfreezeTime;

    /**
     * 业务标识tag，多个以;分隔，拓展中：expansion，已备案：recorded，试算中：trial，已签单：signed，商用中：commercial，欠费中：arrearage[]，已注销：cancelled
     */
    private String businessTag;

    /**
     * 密码有效开始时间
     */
    private Date pwdStartTime;

    /**
     * 国密计算的mac值
     */
    private String ccspMac;

    /**
     * 大模型授权标识
     */
    private String authorizeTag;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        SysMUser other = (SysMUser) that;
        return (this.getUserSid() == null ? other.getUserSid() == null : this.getUserSid().equals(other.getUserSid()))
            && (this.getUserType() == null ? other.getUserType() == null : this.getUserType().equals(other.getUserType()))
            && (this.getUserCode() == null ? other.getUserCode() == null : this.getUserCode().equals(other.getUserCode()))
            && (this.getAccount() == null ? other.getAccount() == null : this.getAccount().equals(other.getAccount()))
            && (this.getPassword() == null ? other.getPassword() == null : this.getPassword().equals(other.getPassword()))
            && (this.getRealName() == null ? other.getRealName() == null : this.getRealName().equals(other.getRealName()))
            && (this.getSex() == null ? other.getSex() == null : this.getSex().equals(other.getSex()))
            && (this.getEmail() == null ? other.getEmail() == null : this.getEmail().equals(other.getEmail()))
            && (this.getMobile() == null ? other.getMobile() == null : this.getMobile().equals(other.getMobile()))
            && (this.getTitle() == null ? other.getTitle() == null : this.getTitle().equals(other.getTitle()))
            && (this.getCompanyId() == null ? other.getCompanyId() == null : this.getCompanyId().equals(other.getCompanyId()))
            && (this.getProjectId() == null ? other.getProjectId() == null : this.getProjectId().equals(other.getProjectId()))
            && (this.getOrgSid() == null ? other.getOrgSid() == null : this.getOrgSid().equals(other.getOrgSid()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getRemark() == null ? other.getRemark() == null : this.getRemark().equals(other.getRemark()))
            && (this.getErrorCount() == null ? other.getErrorCount() == null : this.getErrorCount().equals(other.getErrorCount()))
            && (this.getLastLoginTime() == null ? other.getLastLoginTime() == null : this.getLastLoginTime().equals(other.getLastLoginTime()))
            && (this.getLastLoginIp() == null ? other.getLastLoginIp() == null : this.getLastLoginIp().equals(other.getLastLoginIp()))
            && (this.getStartTime() == null ? other.getStartTime() == null : this.getStartTime().equals(other.getStartTime()))
            && (this.getEndTime() == null ? other.getEndTime() == null : this.getEndTime().equals(other.getEndTime()))
            && (this.getServiceLimitQuantity() == null ? other.getServiceLimitQuantity() == null : this.getServiceLimitQuantity().equals(other.getServiceLimitQuantity()))
            && (this.getApplyReason() == null ? other.getApplyReason() == null : this.getApplyReason().equals(other.getApplyReason()))
            && (this.getSmsMax() == null ? other.getSmsMax() == null : this.getSmsMax().equals(other.getSmsMax()))
            && (this.getUuid() == null ? other.getUuid() == null : this.getUuid().equals(other.getUuid()))
            && (this.getSkinTheme() == null ? other.getSkinTheme() == null : this.getSkinTheme().equals(other.getSkinTheme()))
            && (this.getAuthId() == null ? other.getAuthId() == null : this.getAuthId().equals(other.getAuthId()))
            && (this.getAuthType() == null ? other.getAuthType() == null : this.getAuthType().equals(other.getAuthType()))
            && (this.getCreatedBy() == null ? other.getCreatedBy() == null : this.getCreatedBy().equals(other.getCreatedBy()))
            && (this.getCreatedDt() == null ? other.getCreatedDt() == null : this.getCreatedDt().equals(other.getCreatedDt()))
            && (this.getUpdatedBy() == null ? other.getUpdatedBy() == null : this.getUpdatedBy().equals(other.getUpdatedBy()))
            && (this.getUpdatedDt() == null ? other.getUpdatedDt() == null : this.getUpdatedDt().equals(other.getUpdatedDt()))
            && (this.getVersion() == null ? other.getVersion() == null : this.getVersion().equals(other.getVersion()))
            && (this.getOpenId() == null ? other.getOpenId() == null : this.getOpenId().equals(other.getOpenId()))
            && (this.getAvatarUrl() == null ? other.getAvatarUrl() == null : this.getAvatarUrl().equals(other.getAvatarUrl()))
            && (this.getProvince() == null ? other.getProvince() == null : this.getProvince().equals(other.getProvince()))
            && (this.getCity() == null ? other.getCity() == null : this.getCity().equals(other.getCity()))
            && (this.getCountry() == null ? other.getCountry() == null : this.getCountry().equals(other.getCountry()))
            && (this.getWechatName() == null ? other.getWechatName() == null : this.getWechatName().equals(other.getWechatName()))
            && (this.getDomainId() == null ? other.getDomainId() == null : this.getDomainId().equals(other.getDomainId()))
            && (this.getIamId() == null ? other.getIamId() == null : this.getIamId().equals(other.getIamId()))
            && (this.getPasswordExpiresAt() == null ? other.getPasswordExpiresAt() == null : this.getPasswordExpiresAt().equals(other.getPasswordExpiresAt()))
            && (this.getForceresetpwd() == null ? other.getForceresetpwd() == null : this.getForceresetpwd().equals(other.getForceresetpwd()))
            && (this.getDefaultProjectId() == null ? other.getDefaultProjectId() == null : this.getDefaultProjectId().equals(other.getDefaultProjectId()))
            && (this.getLastProjectId() == null ? other.getLastProjectId() == null : this.getLastProjectId().equals(other.getLastProjectId()))
            && (this.getPwdStrength() == null ? other.getPwdStrength() == null : this.getPwdStrength().equals(other.getPwdStrength()))
            && (this.getParentSid() == null ? other.getParentSid() == null : this.getParentSid().equals(other.getParentSid()))
            && (this.getCertificationStatus() == null ? other.getCertificationStatus() == null : this.getCertificationStatus().equals(other.getCertificationStatus()))
            && (this.getIdCardFront() == null ? other.getIdCardFront() == null : this.getIdCardFront().equals(other.getIdCardFront()))
            && (this.getIdCardReverse() == null ? other.getIdCardReverse() == null : this.getIdCardReverse().equals(other.getIdCardReverse()))
            && (this.getFreezeStatus() == null ? other.getFreezeStatus() == null : this.getFreezeStatus().equals(other.getFreezeStatus()))
            && (this.getUnfreezeType() == null ? other.getUnfreezeType() == null : this.getUnfreezeType().equals(other.getUnfreezeType()))
            && (this.getAuthName() == null ? other.getAuthName() == null : this.getAuthName().equals(other.getAuthName()))
            && (this.getNavigationConfirm() == null ? other.getNavigationConfirm() == null : this.getNavigationConfirm().equals(other.getNavigationConfirm()))
            && (this.getIndustry() == null ? other.getIndustry() == null : this.getIndustry().equals(other.getIndustry()))
            && (this.getPwdEndTime() == null ? other.getPwdEndTime() == null : this.getPwdEndTime().equals(other.getPwdEndTime()))
            && (this.getPolicyAgreeSign() == null ? other.getPolicyAgreeSign() == null : this.getPolicyAgreeSign().equals(other.getPolicyAgreeSign()))
            && (this.getPolicyAgreeTime() == null ? other.getPolicyAgreeTime() == null : this.getPolicyAgreeTime().equals(other.getPolicyAgreeTime()))
            && (this.getUnfreezeTime() == null ? other.getUnfreezeTime() == null : this.getUnfreezeTime().equals(other.getUnfreezeTime()))
            && (this.getBusinessTag() == null ? other.getBusinessTag() == null : this.getBusinessTag().equals(other.getBusinessTag()))
            && (this.getPwdStartTime() == null ? other.getPwdStartTime() == null : this.getPwdStartTime().equals(other.getPwdStartTime()))
            && (this.getCcspMac() == null ? other.getCcspMac() == null : this.getCcspMac().equals(other.getCcspMac()))
            && (this.getAuthorizeTag() == null ? other.getAuthorizeTag() == null : this.getAuthorizeTag().equals(other.getAuthorizeTag()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getUserSid() == null) ? 0 : getUserSid().hashCode());
        result = prime * result + ((getUserType() == null) ? 0 : getUserType().hashCode());
        result = prime * result + ((getUserCode() == null) ? 0 : getUserCode().hashCode());
        result = prime * result + ((getAccount() == null) ? 0 : getAccount().hashCode());
        result = prime * result + ((getPassword() == null) ? 0 : getPassword().hashCode());
        result = prime * result + ((getRealName() == null) ? 0 : getRealName().hashCode());
        result = prime * result + ((getSex() == null) ? 0 : getSex().hashCode());
        result = prime * result + ((getEmail() == null) ? 0 : getEmail().hashCode());
        result = prime * result + ((getMobile() == null) ? 0 : getMobile().hashCode());
        result = prime * result + ((getTitle() == null) ? 0 : getTitle().hashCode());
        result = prime * result + ((getCompanyId() == null) ? 0 : getCompanyId().hashCode());
        result = prime * result + ((getProjectId() == null) ? 0 : getProjectId().hashCode());
        result = prime * result + ((getOrgSid() == null) ? 0 : getOrgSid().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getRemark() == null) ? 0 : getRemark().hashCode());
        result = prime * result + ((getErrorCount() == null) ? 0 : getErrorCount().hashCode());
        result = prime * result + ((getLastLoginTime() == null) ? 0 : getLastLoginTime().hashCode());
        result = prime * result + ((getLastLoginIp() == null) ? 0 : getLastLoginIp().hashCode());
        result = prime * result + ((getStartTime() == null) ? 0 : getStartTime().hashCode());
        result = prime * result + ((getEndTime() == null) ? 0 : getEndTime().hashCode());
        result = prime * result + ((getServiceLimitQuantity() == null) ? 0 : getServiceLimitQuantity().hashCode());
        result = prime * result + ((getApplyReason() == null) ? 0 : getApplyReason().hashCode());
        result = prime * result + ((getSmsMax() == null) ? 0 : getSmsMax().hashCode());
        result = prime * result + ((getUuid() == null) ? 0 : getUuid().hashCode());
        result = prime * result + ((getSkinTheme() == null) ? 0 : getSkinTheme().hashCode());
        result = prime * result + ((getAuthId() == null) ? 0 : getAuthId().hashCode());
        result = prime * result + ((getAuthType() == null) ? 0 : getAuthType().hashCode());
        result = prime * result + ((getCreatedBy() == null) ? 0 : getCreatedBy().hashCode());
        result = prime * result + ((getCreatedDt() == null) ? 0 : getCreatedDt().hashCode());
        result = prime * result + ((getUpdatedBy() == null) ? 0 : getUpdatedBy().hashCode());
        result = prime * result + ((getUpdatedDt() == null) ? 0 : getUpdatedDt().hashCode());
        result = prime * result + ((getVersion() == null) ? 0 : getVersion().hashCode());
        result = prime * result + ((getOpenId() == null) ? 0 : getOpenId().hashCode());
        result = prime * result + ((getAvatarUrl() == null) ? 0 : getAvatarUrl().hashCode());
        result = prime * result + ((getProvince() == null) ? 0 : getProvince().hashCode());
        result = prime * result + ((getCity() == null) ? 0 : getCity().hashCode());
        result = prime * result + ((getCountry() == null) ? 0 : getCountry().hashCode());
        result = prime * result + ((getWechatName() == null) ? 0 : getWechatName().hashCode());
        result = prime * result + ((getDomainId() == null) ? 0 : getDomainId().hashCode());
        result = prime * result + ((getIamId() == null) ? 0 : getIamId().hashCode());
        result = prime * result + ((getPasswordExpiresAt() == null) ? 0 : getPasswordExpiresAt().hashCode());
        result = prime * result + ((getForceresetpwd() == null) ? 0 : getForceresetpwd().hashCode());
        result = prime * result + ((getDefaultProjectId() == null) ? 0 : getDefaultProjectId().hashCode());
        result = prime * result + ((getLastProjectId() == null) ? 0 : getLastProjectId().hashCode());
        result = prime * result + ((getPwdStrength() == null) ? 0 : getPwdStrength().hashCode());
        result = prime * result + ((getParentSid() == null) ? 0 : getParentSid().hashCode());
        result = prime * result + ((getCertificationStatus() == null) ? 0 : getCertificationStatus().hashCode());
        result = prime * result + ((getIdCardFront() == null) ? 0 : getIdCardFront().hashCode());
        result = prime * result + ((getIdCardReverse() == null) ? 0 : getIdCardReverse().hashCode());
        result = prime * result + ((getFreezeStatus() == null) ? 0 : getFreezeStatus().hashCode());
        result = prime * result + ((getUnfreezeType() == null) ? 0 : getUnfreezeType().hashCode());
        result = prime * result + ((getAuthName() == null) ? 0 : getAuthName().hashCode());
        result = prime * result + ((getNavigationConfirm() == null) ? 0 : getNavigationConfirm().hashCode());
        result = prime * result + ((getIndustry() == null) ? 0 : getIndustry().hashCode());
        result = prime * result + ((getPwdEndTime() == null) ? 0 : getPwdEndTime().hashCode());
        result = prime * result + ((getPolicyAgreeSign() == null) ? 0 : getPolicyAgreeSign().hashCode());
        result = prime * result + ((getPolicyAgreeTime() == null) ? 0 : getPolicyAgreeTime().hashCode());
        result = prime * result + ((getUnfreezeTime() == null) ? 0 : getUnfreezeTime().hashCode());
        result = prime * result + ((getBusinessTag() == null) ? 0 : getBusinessTag().hashCode());
        result = prime * result + ((getPwdStartTime() == null) ? 0 : getPwdStartTime().hashCode());
        result = prime * result + ((getCcspMac() == null) ? 0 : getCcspMac().hashCode());
        result = prime * result + ((getAuthorizeTag() == null) ? 0 : getAuthorizeTag().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", userSid=").append(userSid);
        sb.append(", userType=").append(userType);
        sb.append(", userCode=").append(userCode);
        sb.append(", account=").append(account);
        sb.append(", password=").append(password);
        sb.append(", realName=").append(realName);
        sb.append(", sex=").append(sex);
        sb.append(", email=").append(email);
        sb.append(", mobile=").append(mobile);
        sb.append(", title=").append(title);
        sb.append(", companyId=").append(companyId);
        sb.append(", projectId=").append(projectId);
        sb.append(", orgSid=").append(orgSid);
        sb.append(", status=").append(status);
        sb.append(", remark=").append(remark);
        sb.append(", errorCount=").append(errorCount);
        sb.append(", lastLoginTime=").append(lastLoginTime);
        sb.append(", lastLoginIp=").append(lastLoginIp);
        sb.append(", startTime=").append(startTime);
        sb.append(", endTime=").append(endTime);
        sb.append(", serviceLimitQuantity=").append(serviceLimitQuantity);
        sb.append(", applyReason=").append(applyReason);
        sb.append(", smsMax=").append(smsMax);
        sb.append(", uuid=").append(uuid);
        sb.append(", skinTheme=").append(skinTheme);
        sb.append(", authId=").append(authId);
        sb.append(", authType=").append(authType);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdDt=").append(createdDt);
        sb.append(", updatedBy=").append(updatedBy);
        sb.append(", updatedDt=").append(updatedDt);
        sb.append(", version=").append(version);
        sb.append(", openId=").append(openId);
        sb.append(", avatarUrl=").append(avatarUrl);
        sb.append(", province=").append(province);
        sb.append(", city=").append(city);
        sb.append(", country=").append(country);
        sb.append(", wechatName=").append(wechatName);
        sb.append(", domainId=").append(domainId);
        sb.append(", iamId=").append(iamId);
        sb.append(", passwordExpiresAt=").append(passwordExpiresAt);
        sb.append(", forceresetpwd=").append(forceresetpwd);
        sb.append(", defaultProjectId=").append(defaultProjectId);
        sb.append(", lastProjectId=").append(lastProjectId);
        sb.append(", pwdStrength=").append(pwdStrength);
        sb.append(", parentSid=").append(parentSid);
        sb.append(", certificationStatus=").append(certificationStatus);
        sb.append(", idCardFront=").append(idCardFront);
        sb.append(", idCardReverse=").append(idCardReverse);
        sb.append(", freezeStatus=").append(freezeStatus);
        sb.append(", unfreezeType=").append(unfreezeType);
        sb.append(", authName=").append(authName);
        sb.append(", navigationConfirm=").append(navigationConfirm);
        sb.append(", industry=").append(industry);
        sb.append(", pwdEndTime=").append(pwdEndTime);
        sb.append(", policyAgreeSign=").append(policyAgreeSign);
        sb.append(", policyAgreeTime=").append(policyAgreeTime);
        sb.append(", unfreezeTime=").append(unfreezeTime);
        sb.append(", businessTag=").append(businessTag);
        sb.append(", pwdStartTime=").append(pwdStartTime);
        sb.append(", ccspMac=").append(ccspMac);
        sb.append(", authorizeTag=").append(authorizeTag);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}