package cn.com.cloudstar.rightcloud.data.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 折扣表
 * @TableName biz_discount
 */
@TableName(value ="biz_discount")
@Data
public class BizDiscount implements Serializable {
    /**
     * 折扣SID
     */
    @TableId
    private Long discountSid;

    /**
     * 名称
     */
    private String discountName;

    /**
     * 折扣类型，platform:平台折扣，customer:客户折扣
     */
    private String discountType;

    /**
     * 用户SID，当为客户折扣时存值
     */
    private Long userSid;

    /**
     * 折扣来源 [contract]来源合同 [discount] 来源折扣
     */
    private String originType;

    /**
     * 状态 0 禁用  1 启用
     */
    private Integer status;

    /**
     * 适用环境 适用的云环境，多个以，分隔
     */
    private String cloudEnvScope;

    /**
     * 适用产品 适用的产品，多个以，分隔。[ecs][disk][floatingip]
     */
    private String productScope;

    /**
     * 应用范围 支持[quantity]按照数量，[money]按照结算时最终金额，[unlimited]无限制，直接享受折扣
     */
    private String scopeType;

    /**
     * 范围值 1. 支持范围写法，如1-+，1-1000，结束值+代表无限制 2. 支持固定数字值
     */
    private String scopeValue;

    /**
     * 折扣系数 0-1，最多两位小数
     */
    private BigDecimal discountRatio;

    /**
     * 生效时间
     */
    private Date startTime;

    /**
     * 失效时间
     */
    private Date endTime;

    /**
     * 描述
     */
    private String description;

    /**
     * 合同SID
     */
    private Long contractSid;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedDt;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 运营实体id
     */
    private Long entityId;

    /**
     * 运营实体名称
     */
    private String entityName;

    /**
     * 国密计算的mac值
     */
    private String ccspMac;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;


    /**
     * 产品范围
     */
    @TableField(exist = false)
    private List<String> productScopes;

    /**
     * 云环境范围
     */
    @TableField(exist = false)
    private List<String> cloudEnvScopes;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        BizDiscount other = (BizDiscount) that;
        return (this.getDiscountSid() == null ? other.getDiscountSid() == null : this.getDiscountSid().equals(other.getDiscountSid()))
            && (this.getDiscountName() == null ? other.getDiscountName() == null : this.getDiscountName().equals(other.getDiscountName()))
            && (this.getDiscountType() == null ? other.getDiscountType() == null : this.getDiscountType().equals(other.getDiscountType()))
            && (this.getUserSid() == null ? other.getUserSid() == null : this.getUserSid().equals(other.getUserSid()))
            && (this.getOriginType() == null ? other.getOriginType() == null : this.getOriginType().equals(other.getOriginType()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getCloudEnvScope() == null ? other.getCloudEnvScope() == null : this.getCloudEnvScope().equals(other.getCloudEnvScope()))
            && (this.getProductScope() == null ? other.getProductScope() == null : this.getProductScope().equals(other.getProductScope()))
            && (this.getScopeType() == null ? other.getScopeType() == null : this.getScopeType().equals(other.getScopeType()))
            && (this.getScopeValue() == null ? other.getScopeValue() == null : this.getScopeValue().equals(other.getScopeValue()))
            && (this.getDiscountRatio() == null ? other.getDiscountRatio() == null : this.getDiscountRatio().equals(other.getDiscountRatio()))
            && (this.getStartTime() == null ? other.getStartTime() == null : this.getStartTime().equals(other.getStartTime()))
            && (this.getEndTime() == null ? other.getEndTime() == null : this.getEndTime().equals(other.getEndTime()))
            && (this.getDescription() == null ? other.getDescription() == null : this.getDescription().equals(other.getDescription()))
            && (this.getContractSid() == null ? other.getContractSid() == null : this.getContractSid().equals(other.getContractSid()))
            && (this.getCreatedBy() == null ? other.getCreatedBy() == null : this.getCreatedBy().equals(other.getCreatedBy()))
            && (this.getCreatedDt() == null ? other.getCreatedDt() == null : this.getCreatedDt().equals(other.getCreatedDt()))
            && (this.getUpdatedBy() == null ? other.getUpdatedBy() == null : this.getUpdatedBy().equals(other.getUpdatedBy()))
            && (this.getUpdatedDt() == null ? other.getUpdatedDt() == null : this.getUpdatedDt().equals(other.getUpdatedDt()))
            && (this.getVersion() == null ? other.getVersion() == null : this.getVersion().equals(other.getVersion()))
            && (this.getEntityId() == null ? other.getEntityId() == null : this.getEntityId().equals(other.getEntityId()))
            && (this.getEntityName() == null ? other.getEntityName() == null : this.getEntityName().equals(other.getEntityName()))
            && (this.getCcspMac() == null ? other.getCcspMac() == null : this.getCcspMac().equals(other.getCcspMac()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getDiscountSid() == null) ? 0 : getDiscountSid().hashCode());
        result = prime * result + ((getDiscountName() == null) ? 0 : getDiscountName().hashCode());
        result = prime * result + ((getDiscountType() == null) ? 0 : getDiscountType().hashCode());
        result = prime * result + ((getUserSid() == null) ? 0 : getUserSid().hashCode());
        result = prime * result + ((getOriginType() == null) ? 0 : getOriginType().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getCloudEnvScope() == null) ? 0 : getCloudEnvScope().hashCode());
        result = prime * result + ((getProductScope() == null) ? 0 : getProductScope().hashCode());
        result = prime * result + ((getScopeType() == null) ? 0 : getScopeType().hashCode());
        result = prime * result + ((getScopeValue() == null) ? 0 : getScopeValue().hashCode());
        result = prime * result + ((getDiscountRatio() == null) ? 0 : getDiscountRatio().hashCode());
        result = prime * result + ((getStartTime() == null) ? 0 : getStartTime().hashCode());
        result = prime * result + ((getEndTime() == null) ? 0 : getEndTime().hashCode());
        result = prime * result + ((getDescription() == null) ? 0 : getDescription().hashCode());
        result = prime * result + ((getContractSid() == null) ? 0 : getContractSid().hashCode());
        result = prime * result + ((getCreatedBy() == null) ? 0 : getCreatedBy().hashCode());
        result = prime * result + ((getCreatedDt() == null) ? 0 : getCreatedDt().hashCode());
        result = prime * result + ((getUpdatedBy() == null) ? 0 : getUpdatedBy().hashCode());
        result = prime * result + ((getUpdatedDt() == null) ? 0 : getUpdatedDt().hashCode());
        result = prime * result + ((getVersion() == null) ? 0 : getVersion().hashCode());
        result = prime * result + ((getEntityId() == null) ? 0 : getEntityId().hashCode());
        result = prime * result + ((getEntityName() == null) ? 0 : getEntityName().hashCode());
        result = prime * result + ((getCcspMac() == null) ? 0 : getCcspMac().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", discountSid=").append(discountSid);
        sb.append(", discountName=").append(discountName);
        sb.append(", discountType=").append(discountType);
        sb.append(", userSid=").append(userSid);
        sb.append(", originType=").append(originType);
        sb.append(", status=").append(status);
        sb.append(", cloudEnvScope=").append(cloudEnvScope);
        sb.append(", productScope=").append(productScope);
        sb.append(", scopeType=").append(scopeType);
        sb.append(", scopeValue=").append(scopeValue);
        sb.append(", discountRatio=").append(discountRatio);
        sb.append(", startTime=").append(startTime);
        sb.append(", endTime=").append(endTime);
        sb.append(", description=").append(description);
        sb.append(", contractSid=").append(contractSid);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdDt=").append(createdDt);
        sb.append(", updatedBy=").append(updatedBy);
        sb.append(", updatedDt=").append(updatedDt);
        sb.append(", version=").append(version);
        sb.append(", entityId=").append(entityId);
        sb.append(", entityName=").append(entityName);
        sb.append(", ccspMac=").append(ccspMac);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}