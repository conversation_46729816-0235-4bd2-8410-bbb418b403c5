<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.cloudstar.rightcloud.data.dao.MarketShopTagRelevanceMapper">

    <resultMap id="BaseResultMap" type="cn.com.cloudstar.rightcloud.data.entity.MarketShopTagRelevance">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="shopId" column="shop_id" jdbcType="BIGINT"/>
            <result property="tagId" column="tag_id" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,shop_id,tag_id
    </sql>
    <select id="selectTagCategoryNameByShopId" resultType="java.lang.String">
        SELECT
            tag.category_name
        FROM
            market_shop_tag_relevance re
                INNER JOIN market_shop_tag tag ON re.tag_id = tag.id
        WHERE
            re.shop_id =#{shopId}
        limit 1
    </select>
    <select id="selectTagNameByShopId" resultType="java.lang.String">
        SELECT
            tag.tag_name
        FROM
            market_shop_tag_relevance re
                INNER JOIN market_shop_tag tag ON re.tag_id = tag.id
        WHERE
            re.shop_id =#{shopId}
    </select>
</mapper>
