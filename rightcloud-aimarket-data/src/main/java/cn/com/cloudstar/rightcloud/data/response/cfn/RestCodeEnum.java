package cn.com.cloudstar.rightcloud.data.response.cfn;



import org.apache.commons.lang3.StringUtils;

import cn.hutool.core.util.ObjectUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * 其他代码枚举 返回体code
 *
 * <AUTHOR>
 * @date 2022/08/05
 */
@Getter
@AllArgsConstructor
public enum RestCodeEnum {

    SUCCESS(1, "操作成功"),
    SERVER_ERROR(1001, "系统错误"),
    UNAUTHORIZED(401, "未授权"),
    REFRESH_TOKEN(402, "刷新token"),
    UNAUTHORIZED_ACCESS(403, "越权访问");

    private final int code;

    private final String name;

    public static String getNameByCode(Integer code) {
        if (ObjectUtil.isEmpty(code)) {
            return StringUtils.EMPTY;
        }
        for (RestCodeEnum restCodeEnum : RestCodeEnum.values()) {
            if (code.equals(restCodeEnum.code)) {
                return restCodeEnum.name;
            }
        }
        return StringUtils.EMPTY;
    }

}
