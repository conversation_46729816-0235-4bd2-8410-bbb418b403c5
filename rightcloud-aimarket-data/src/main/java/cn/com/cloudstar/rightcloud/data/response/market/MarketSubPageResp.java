package cn.com.cloudstar.rightcloud.data.response.market;

import java.util.List;

import lombok.Data;

import cn.com.cloudstar.rightcloud.data.entity.MarketShopVersion;

/**
 * 市场子页面分别地
 *
 * <AUTHOR>
 * @date 2023/08/16
 */
@Data
public class MarketSubPageResp {

    /**
     * 订阅id
     */
    private String subscribeId;

    /**
     * 商品id
     */
    private String shopId;

    /**
     * 商品标题
     */
    private String shopTitle;

    /**
     * 商品类型
     */
    private String shopType;

    /**
     * 供应商名称
     */
    private String orgName;

    /**
     * 组织id
     */
    private Long orgSid;


    /**
     * 购买方名称
     */
    private String custOrgName;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 资金监管状态
     */
    private String superviseStatus;

    /**
     * 结算状态
     */
    private String settlementStatus;

    /**
     * 规格名称
     */
    private String unit;

    /**
     * 价格
     */
    private String price;

    /**
     * 创建时间
     */
    private String createdDt;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 完成时间
     */
    private String completeDt;

    /**
     * 商品分类
     */
    private String categoryName;

    /**
     * 供应商id
     */
    private Long shopOrgSid;

    /**
     * 管理端的订阅列表：是否把交付置灰（管理员只能交付自己创建的商品）
     */
    private Boolean fail;

    /**
     * 模型集市的规格id
     */
    private Long priceJoinId;

    /**
     * 商品是否免费：0免费，2按月，3按年
     */
    private Integer sellType;

    /**
     * 商品版本 算法时用
     */
    private List<MarketShopVersion> shopVersions;


}
