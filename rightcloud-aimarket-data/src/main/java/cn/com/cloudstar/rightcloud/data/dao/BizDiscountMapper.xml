<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.cloudstar.rightcloud.data.dao.BizDiscountMapper">

    <resultMap id="BaseResultMap" type="cn.com.cloudstar.rightcloud.data.entity.BizDiscount">
            <id property="discountSid" column="discount_sid" jdbcType="BIGINT"/>
            <result property="discountName" column="discount_name" jdbcType="VARCHAR"/>
            <result property="discountType" column="discount_type" jdbcType="VARCHAR"/>
            <result property="userSid" column="user_sid" jdbcType="BIGINT"/>
            <result property="originType" column="origin_type" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="cloudEnvScope" column="cloud_env_scope" jdbcType="VARCHAR"/>
            <result property="productScope" column="product_scope" jdbcType="VARCHAR"/>
            <result property="scopeType" column="scope_type" jdbcType="VARCHAR"/>
            <result property="scopeValue" column="scope_value" jdbcType="VARCHAR"/>
            <result property="discountRatio" column="discount_ratio" jdbcType="DECIMAL"/>
            <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
            <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
            <result property="description" column="description" jdbcType="VARCHAR"/>
            <result property="contractSid" column="contract_sid" jdbcType="BIGINT"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
            <result property="createdDt" column="created_dt" jdbcType="TIMESTAMP"/>
            <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
            <result property="updatedDt" column="updated_dt" jdbcType="TIMESTAMP"/>
            <result property="version" column="version" jdbcType="INTEGER"/>
            <result property="entityId" column="entity_id" jdbcType="BIGINT"/>
            <result property="entityName" column="entity_name" jdbcType="VARCHAR"/>
            <result property="ccspMac" column="ccsp_mac" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        discount_sid,discount_name,discount_type,
        user_sid,origin_type,status,
        cloud_env_scope,product_scope,scope_type,
        scope_value,discount_ratio,start_time,
        end_time,description,contract_sid,
        created_by,created_dt,updated_by,
        updated_dt,version,entity_id,
        entity_name,ccsp_mac
    </sql>
</mapper>
