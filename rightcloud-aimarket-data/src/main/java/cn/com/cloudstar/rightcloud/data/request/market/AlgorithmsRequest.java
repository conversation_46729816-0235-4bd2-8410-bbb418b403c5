package cn.com.cloudstar.rightcloud.data.request.market;

import java.io.Serializable;

import lombok.Data;

import cn.com.cloudstar.rightcloud.module.support.access.pojo.BaseRequest;

/**
 * @description 查询算法列表
 * <AUTHOR>
 * @date 2023/9/12 16:58
 */
@Data
public class AlgorithmsRequest extends BaseRequest implements Serializable {

    /**
     * 算法名称
     */
    private String algorithmName;

    /**
     * 查询算法的偏移量，最小为0。
     * 例如设置为1，则表示从第二条开始查
     */
    private Integer offset;

    /**
     * 查询算法的限制量。最小为1，最大为50。
     */
    private Integer limit;

    /**
     * 查询算法排列顺序的指标。
     * 默认使用create_time排序。
     */
    private String sort_by;

    /**
     * 查询算法排列顺序，默认为“desc”，降序排序。
     * 也可以选择对应的“asc”,升序排序。
     */
    private String order;

    /**
     * 查询算法要搜索的分组条件。
     */
    private String group_by;

    /**
     * 查询算法所要过滤的条件，如算法名称模糊匹配。
     */
    private String searches;

    /**
     * 工作空间ID
     */
    private String workspace_id;

    /**
     * 过滤字段
     */
    private String filter_by;

    /**
     * 是否客户（不清楚字段含义，文档没有这个字段）
     */
    private Boolean custom;
    private String shopType;

}
