/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.data.request.discount;

import cn.com.cloudstar.rightcloud.module.support.access.pojo.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/3/19.
 */
@ApiModel(description = "折扣列表查询")
@Data
public class DescribeDiscountRequest extends BaseRequest {

    /**
     * 折扣名字
     */
    @ApiModelProperty("名称")
    private String discountNameLike;

    /**
     * 用户sid
     */
    @ApiModelProperty("客户ID")
    private Long userSid;

    /**
     * 适用环境
     */
    @ApiModelProperty("适用环境 适用的云环境，多个以，分隔")
    private String cloudEnvScopeLike;

    /**
     * 适用产品
     */
    @ApiModelProperty("适用产品 适用的产品，多个以，分隔。[ecs][disk][floatingip]")
    private String productScopeLike;

    /**
     * 应用范围
     */
    @ApiModelProperty("应用范围 支持[quantity]按照数量，[money]按照结算时最终金额，[unlimited]无限制，直接享受折扣")
    private String scopeType;

    /**
     * 范围值
     */
    @ApiModelProperty("范围值 1. 支持范围写法，如1-+，1-1000，结束值+代表无限制 2. 支持固定数字值")
    private String scopeValue;

    /**
     * 折扣系数
     */
    @ApiModelProperty("折扣系数 0-1，最多两位小数")
    private BigDecimal discountRatio;

    /**
     * 开始时间
     */
    @ApiModelProperty("生效时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty("失效时间")
    private Date endTime;

    /**
     * 描述
     */
    @ApiModelProperty("描述")
    private String description;
}
