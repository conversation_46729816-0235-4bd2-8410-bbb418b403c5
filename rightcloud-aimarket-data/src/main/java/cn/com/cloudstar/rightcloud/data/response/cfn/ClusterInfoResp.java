package cn.com.cloudstar.rightcloud.data.response.cfn;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.List;

import lombok.Data;

/**
 * 集群信息resp
 *
 * <AUTHOR>
 * @date 2025/4/7 11:17
 */
@Data
public class ClusterInfoResp {

    /**
     * 集群id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long clusterId;
    /**
     * 集群名称
     */
    private String clusterName;

    /**
     * 资源池列表
     */
    private List<ClusterPoolResp> poolList;

    /**
     * 资源规格列表
     */
    private List<ClusterGroupFlavorResp> flavorList;

}
