package cn.com.cloudstar.rightcloud.data.request.market;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import lombok.Data;

import cn.com.cloudstar.rightcloud.module.support.access.constants.EnumValue;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.BaseRequest;

/**
 * @description 客户管理商品列表入参
 * <AUTHOR>
 * @date 2023/8/11 16:58
 */
@Data
public class MarketShopMgtRequest extends BaseRequest implements Serializable {

    /**
     * 商品名称
     */
    private String shopName;

    /**
     * 状态
     */
    @EnumValue(strValues = {"pending","approval","refuse","unpublished","online","offline","onerror"}, message = "状态不合法！")
    private String status;

    /**
     * 客户id
     */
    private Long ownerId;

    /**
     * 商品类别：0 我的算法 ，1 其他 ， 2 开源项目
     */
    @EnumValue(intValues = {0, 1, 2, 3, 4}, message = "商品类别不合法！")
    private Integer shopType;

    /**
     * 商品简介
     */
    private String introduce;


    /**
     * 商品来源：platform 平台自营，supplier 供应商
     */
    @EnumValue(strValues = {"platform","supplier"}, message = "商品来源不合法！")
    private String shopSource;

}
