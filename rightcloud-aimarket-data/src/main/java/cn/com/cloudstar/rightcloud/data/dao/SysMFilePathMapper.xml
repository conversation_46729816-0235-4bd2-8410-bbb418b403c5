<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.com.cloudstar.rightcloud.data.dao.SysMFilePathMapper">

    <resultMap id="BaseResultMap" type="cn.com.cloudstar.rightcloud.common.dto.SysMFilePath">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fileNum" column="file_num" jdbcType="VARCHAR"/>
            <result property="fileName" column="file_name" jdbcType="VARCHAR"/>
            <result property="accountId" column="account_id" jdbcType="BIGINT"/>
            <result property="operationType" column="operation_type" jdbcType="VARCHAR"/>
            <result property="operationId" column="operation_id" jdbcType="VARCHAR"/>
            <result property="filePath" column="file_path" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="sortOrder" column="sort_order" jdbcType="SMALLINT"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
            <result property="createdDt" column="created_dt" jdbcType="TIMESTAMP"/>
            <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
            <result property="updatedDt" column="updated_dt" jdbcType="TIMESTAMP"/>
            <result property="version" column="version" jdbcType="BIGINT"/>
            <result property="compressPassword" column="compress_password" jdbcType="VARCHAR"/>
            <result property="entityId" column="entity_id" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,file_num,file_name,
        account_id,operation_type,operation_id,
        file_path,remark,sort_order,
        created_by,created_dt,updated_by,
        updated_dt,version,compress_password,
        entity_id
    </sql>
</mapper>
