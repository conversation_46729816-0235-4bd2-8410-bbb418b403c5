//package cn.com.cloudstar.rightcloud.data.response;
//
//import com.fasterxml.jackson.annotation.JsonFormat;
//
//import java.util.Date;
//import java.util.List;
//
//import io.swagger.annotations.ApiModelProperty;
//import lombok.Data;
//
//
///**
// * 商品
// *
// * <AUTHOR>
// * @date 2023/03/16
// */
//@Data
//public class ShopResp {
//    /**
//     * 商品id
//     */
//    @ApiModelProperty("商品id")
//    @JsonFormat(shape = JsonFormat.Shape.STRING)
//    private Long id;
//
//    /**
//     * 商品所有者id
//     */
//    private String ownerId;
//
//    /**
//     * 商品名称
//     */
//    @ApiModelProperty("商品名称")
//    private String shopName;
//    /**
//     * 商品类型：algorithm 算法，dataset 数据集，model 模型，application 应用
//     */
//    @ApiModelProperty("商品类型：algorithm 算法，dataset 数据集，model 模型，application 应用")
//    private String shopType;
//    /**
//     * 商品来源：platform 平台自营，supplier 供应商
//     */
//    @ApiModelProperty("商品来源：platform 平台自营，supplier 供应商")
//    private String shopSource;
//    /**
//     * Logo图片地址
//     */
//    @ApiModelProperty("Logo图片地址")
//    private String logoImageUrl;
//    /**
//     * 商品简介
//     */
//    @ApiModelProperty("商品简介")
//    private String introduce;
//
//    /**
//     *  状态;商品状态：pending 待审核（已上架），refuse 审核拒绝，offline 已下架，
//     */
//    private String status;
//
//    private List<TagResponse> tagList;
//
//    private Date createdDt;
//}
