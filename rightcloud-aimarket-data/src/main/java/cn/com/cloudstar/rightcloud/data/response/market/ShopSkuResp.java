package cn.com.cloudstar.rightcloud.data.response.market;

import cn.com.cloudstar.rightcloud.data.vo.market.StringVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;


/**
 * 商店sku返回列表
 *
 * <AUTHOR>
 * @date 2023/08/02
 */
@Data
public class ShopSkuResp {
    /**
     * 商品id
     */
    @ApiModelProperty("skuId")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 属性类型：quantity-数量；enum-枚举
     */
    @ApiModelProperty("属性名称")
    private String attrName;

    /**
     * 单位
     */
    @ApiModelProperty("单位")
    private String unit;

    /**
     * 属性类型：quantity-数量；enum-枚举
     */
    @ApiModelProperty("属性类型")
    private String type;
    // /**
    //  * 最小单位
    //  */
    // @ApiModelProperty("最小单位")
    // private String minUnit;
    // /**
    //  * 最大单位
    //  */
    // @ApiModelProperty("最大单位")
    // private String maxUnit;
    /**
     * 枚举值
     */
    @ApiModelProperty("枚举值")
    private List<StringVO> enumValues;

    @ApiModelProperty("可用状态")
    private Integer availableStatus;

}
