
## 自动发包脚本 base_process.py 
### 前置依赖
依赖python2.7
### 示例命令如下
``python base_process.py --version_tag=v20201223 --pro_base=false --pro_driver=true --ignore_base_list=rightcloud-parent --pro_driver_all=true --skip_deploy=true --driver_workdir=F:\currentWorkspace\rightcloudv4 --debug=True``
### 自动发包脚本参数信息
optional arguments:
  -h, --help            show this help message and exit
  --pro_base PRO_BASE   是否deploy基础组件，值为True or False，为False时需要扫描基础组件目录，但不做修改
  --pro_driver PRO_DRIVER
                        是否deploy driver组件，值为True or False
  --version_tag VERSION_TAG
                        新的版本号
  --ignore_base_list IGNORE_BASE_LIST
                        需要忽略的基础组件
  --pro_driver_all PRO_DRIVER_ALL
                        是否deploy所有的driver组件，值为True or False,
                        为False时需要传入driver_list参数
  --driver_list DRIVER_LIST
                        需要deploy的driver组件列表，以逗号隔开
  --driver_workdir DRIVER_WORKDIR
                        driver组件所在的工作目录，不传入则视为与基础组件使用同一个目录
  --skip_deploy SKIP_DEPLOY
                        是否跳过deploy阶段，为True时仅做install验证
  --debug DEBUG         是否显示maven install deploy等debug 信息，默认为False
