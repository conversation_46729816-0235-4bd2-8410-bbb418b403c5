/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.common.util;

import cn.com.cloudstar.rightcloud.common.pojo.LicenseVo;
import cn.com.cloudstar.rightcloud.common.redis.JedisUtil;
import cn.com.cloudstar.rightcloud.module.support.access.pojo.AuthUser;
import cn.com.cloudstar.rightcloud.module.support.access.util.DateUtil;
import cn.com.cloudstar.rightcloud.module.support.db.util.DBUtils;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.google.common.base.Strings;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * The type License util.
 *
 * <AUTHOR>
 * @date: 19 :38 2018/06/07
 */
public class LicenseUtil {

    private static final Logger logger = LoggerFactory.getLogger(LicenseUtil.class);
    private static final String SQL_QUERY_LICENSE = "select * from license";
    private static final String SQL_QUERY_LICENSE_COUNT = "select count(*) as num from license";
    private static final String SQL_UPDATE_LICENSE = "update license set LICENSE_SERIALNO = ?";
    private static final String SQL_INSERT_LICENSE = "insert into license values (?)";
    private static final String SQL_QUERY_SN = "select CONFIG_VALUE from sys_m_config where CONFIG_TYPE='system_config' and CONFIG_KEY='OS.var.version'";
    private static final boolean CHECK_SN = false;
    private static final String LICENSE_KEY= "license.expire";
    private static String PRODUCT_SN;

    static {
        PRODUCT_SN = System.getProperty("cloudstar.product.sn", "JZ7V-M3VJ-IYZR-NAUI");
    }

    /**
     * 查询license信息 每调用一次会查一次数据库（适用于shedule）
     *
     * @return the license vo
     */
    public static LicenseVo queryLicenseInfoFromDb() {
        return preAnalyzeLincenseVo();
    }

    /**
     * 查询license过期时间
     *
     * @return
     */
    public static Date getLicenseExpireDate() {

        String expireDate = JedisUtil.instance().get(LICENSE_KEY);
        if (StringUtils.isBlank(expireDate)) {
            LicenseVo licenseVo = queryLicenseInfoFromDb();
            if (Objects.isNull(licenseVo)) {
                return null;
            }
            expireDate = licenseVo.getExpireDate();
            JedisUtil.instance().set(LICENSE_KEY, licenseVo.getExpireDate(), 24 * 60 * 60);
        }
        return DateUtil.parseDate(expireDate + " 23:59:59", "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * license是否过期
     *
     * @return the boolean
     */
    public static boolean isExpireLicenseOnlyTime() {
        Date expireDate = getLicenseExpireDate();
        return Objects.isNull(expireDate) || new Date().after(expireDate);
    }

    /**
     * license是否过期 （系统管理不会过期）
     *
     * @param authUser the auth user
     *
     * @return the boolean
     */
    public static boolean isExpireLicense(AuthUser authUser) {
        if (authUser.isAdmin()) {
            return false;
        }
        return isExpireLicenseOnlyTime();
    }

    /**
     * 查询系统lincense即将过期的提示信息
     *
     * @return the string
     */
    public static Map queryLicenseExpireInfo() {
        LicenseVo lincenseVo = queryLicenseInfoFromDb();
        if (lincenseVo != null) {
            if (CHECK_SN && !Objects.equals(lincenseVo.getProductSN(), getDBProductSN())) {
                return MapsKit.of("status", Boolean.FALSE,
                                  "msg", "尊敬的用户：许可证无效，请尽快联系管理员更新许可证。");
            }
            // 提前7天提示license过期消息
            Date expireDate = DateUtil.parseDate(lincenseVo.getExpireDate() + " 23:59:59", "yyyy-MM-dd HH:mm:ss");
            Date nowDate = new Date();
            long diffTime = expireDate.getTime() - nowDate.getTime();
            if (diffTime <= 7 * 24 * 60 * 60 * 1000) {
                String expireStr = DateUtil.licenseExpireDay(diffTime);
                StringBuilder massage = new StringBuilder("尊敬的用户：");
                if (diffTime < 0) {
                    massage.append("许可证已于").append(lincenseVo.getExpireDate()).append("过期");
                } else {
                    massage.append("许可证将在")
                           .append(lincenseVo.getExpireDate())
                           .append("过期(")
                           .append(expireStr)
                           .append(")");
                }
                massage.append("，请尽快联系管理员更新许可证。");
                return MapsKit.of("status", Boolean.TRUE,
                                  "msg",
                                  massage.toString());
            }
        } else {
            return MapsKit.of("status", Boolean.FALSE,
                              "msg", "尊敬的用户：许可证还未配置成功，请尽快联系管理员更新许可证。");
        }
        return null;
    }

    private static String getLicenseStrFromDb() {
        Map dbResultMap = DBUtils.INSTANCE.queryMap(SQL_QUERY_LICENSE);
        if (dbResultMap != null && !dbResultMap.isEmpty()) {
            return (String) dbResultMap.get("LICENSE_SERIALNO");
        }
        return null;
    }

    private static int getLicenseCountFromDb() {
        Map dbResultMap = DBUtils.INSTANCE.queryMap(SQL_QUERY_LICENSE_COUNT);
        if (dbResultMap != null && !dbResultMap.isEmpty()) {
            return Integer.parseInt(dbResultMap.get("num").toString());
        }
        return 0;
    }

    /**
     * 解析许可证信息
     **/
    public static LicenseVo analyzeLincenseVo(String licenseStr) {
        try {
            if (StringUtils.isBlank(licenseStr)) {
                return null;
            }

            //采用RSA私钥解密
            String license = CrytoUtilSimple.rsaDecrypt(licenseStr);
            Map<String, Object> map = new HashMap<>();
            String[] splitArr = license.split("&");
            for (String str : splitArr) {
                String[] kvArr = str.split("=");
                map.put(kvArr[0], kvArr.length >= 2 ? kvArr[1] : null);
            }
            return  BeanUtil.fillBeanWithMap(map, new LicenseVo(), CopyOptions.create().ignoreError());
        } catch (Exception e) {
            logger.error("analyze license error return null, error：" + e.getMessage(), e);
        }
        return null;
    }

    /**
     * 读取config表中sn
     */
    public static String getDBProductSN() {
        try {
            Map dbResultMap = DBUtils.INSTANCE.queryMap(SQL_QUERY_SN);
            if (dbResultMap != null && !dbResultMap.isEmpty()) {
                return CrytoUtilSimple.decrypt((String) dbResultMap.get("CONFIG_VALUE")).trim();
            }
        } catch (Exception e) {
            logger.error("【特征码】无法获取，使用默认.");
            logger.error(e.getMessage());
        }
        return PRODUCT_SN;
    }


    /**
     * 校验特征码, 解析许可证信息
     */
    public static LicenseVo preAnalyzeLincenseVo() {
        if (Strings.isNullOrEmpty(getDBProductSN())) {
            logger.error("【特征码】无法获取，标记许可证过期!");
            return null;
        }
        return analyzeLincenseVo(getLicenseStrFromDb());
    }
}
