package cn.com.cloudstar.rightcloud.common.ccsp;

import cn.com.cloudstar.rightcloud.common.redis.JedisUtil;
import cn.com.cloudstar.rightcloud.common.util.CCSP;
import cn.com.cloudstar.rightcloud.common.util.CCSPConstants;
import cn.com.cloudstar.rightcloud.common.util.CCSPHandlerUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * @description: CCSPCacheUtil
 * @author: ouyonghui
 * @date: 2023/7/3 15:40
 */
@Slf4j
public class CCSPCacheUtil {
    /**
     * 验证国密开启并加密
     *
     * @param preEncryptData this  preEncryptData
     * @return {@link String}
     */
    public static String verifyAndCCSPEncrypt(String preEncryptData) {
        try {
            String ccspConfigStr = JedisUtil.instance().getHSet(CCSPConstants.CCSP_CONFIG_HASH_CACHE, CCSPConstants.CCSP_CONFIG_CACHE_KEY);
            if (StringUtils.isNotBlank(ccspConfigStr)) {
                CCSP ccsp = JSONUtil.toBean(ccspConfigStr, CCSP.class);
                if (ccsp.getServiceOpen()) {
                    return CCSPHandlerUtil.initAndSm4Encrypt(preEncryptData, ccsp);
                }
            }
        } catch (Exception e) {
            return preEncryptData;
        }
        return preEncryptData;
    }

    /**
     * 验证国密开启并解密
     *
     * @param preDecryptData this  preDecryptData
     * @return {@link String}
     */
    public static String verifyAndCCSPDecrypt(String preDecryptData) {
        try {
            String ccspConfigStr = JedisUtil.instance().getHSet(CCSPConstants.CCSP_CONFIG_HASH_CACHE, CCSPConstants.CCSP_CONFIG_CACHE_KEY);
            if (StringUtils.isNotBlank(ccspConfigStr)) {
                CCSP ccsp = JSONUtil.toBean(ccspConfigStr, CCSP.class);
                if (ccsp.getServiceOpen()) {
                    return CCSPHandlerUtil.initAndSm4Decrypt(preDecryptData, ccsp);
                }
            }
        } catch (Exception e) {
            return preDecryptData;
        }
        return preDecryptData;
    }

    /**
     *  判断国密是否开启
     * @return true/false
     */
    public static boolean ccspServiceOpen() {
        boolean flag = false;
        String ccspConfigStr = JedisUtil.instance().getHSet(CCSPConstants.CCSP_CONFIG_HASH_CACHE, CCSPConstants.CCSP_CONFIG_CACHE_KEY);
        if (StringUtils.isNotBlank(ccspConfigStr)) {
            CCSP ccsp = JSONUtil.toBean(ccspConfigStr, CCSP.class);
            if (ccsp.getServiceOpen()) {
                flag = true;
            }
        }
        return flag;
    }
}
