/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.common.constants.msg;

/**
 * DESC:国际化消息代码
 *
 * <AUTHOR>
 * @date 2024/04/23 10:34
 */

public interface MsgCd {

    /**
     * 用户组无用户
     */
    String ERROR_NO_USER_USERGROUP = "error.no.user.usergroup";

    /**
     * 内置用户组，授权失败
     */
    String ERROR_UPDATE_MENU_DEFAULT_GROUP = "error.update.menu.default.group";


    /**
     * error.product.no.dmeosp=HPC共享资源池-二期上架前，请检查DME-OceanStor Pacific产品是否正常！
     */
    String ERROR_PRODUCT_NO_DME_OSP = "error.product.no.dmeosp";
    /**
     * error.product.dmeops.no.price=高性能计算上架前，请检查弹性文件服务产品是否正常！
     */
    String ERROR_PRODUCT_DME_OSP_NO_PRICE = "error.product.dmeops.no.price";
    /**
     * 操作成功！
     */
    String ERR_MSG_BSS_1308264509 = "err.msg.bss.1308264509";
    /**
     * error.operate.failure=操作失败。
     */
    String ERROR_OPERATE_FAILURE = "error.operate.failure";





    /**
     * "发票数据正在下载，请稍后查询下载任务！"
     */
    String INVOICE_DOWNING = "invoice.downing";
    /**
     * "平台折扣正在下载，请稍后查询下载任务！"
     */
    String PLATFORM_DISCOUNT_DOWNING = "platform.discount.downing";

    /**
     * 所选时间不能超过%S
     */
    String SELECT_DATE_ERROR = "select.date.error";

    /**
     * 当前下载状态错误
     */
    String CURRENT_STATUS_NOT_DOWN = "current.status.not.down";
    /**
     * minio域名不正确，请重新配置!
     */
    String MINIO_URL_ERROR = "minio.url.error";

    /**
     *  options数量最多允许10个
     */
    String OPTIONS_MAX_TEN = "options.max.ten";

    /**
     *  为保留值，不允许配置。
     */
    String HOLD_VALUE_NOT_CONFIG = "hold.value.not.config";

    /**
     *  无效的TOP信息过滤标识，因无有效自定义业务标识配置
     */
    String INVALID_TOP = "invalid.top";
    /**
     * 勿配置重复TOP信息过滤标识。
     */
    String REPEAT_TOP = "repeat.top";

    /**
     *  无效的TOP信息过滤标识，因无有效自定义业务标识配置
     */
    String INVALID_TOP_TWO = "invalid.top.two";

    /**
     *  无效的TOP信息过滤标识，因包含不存在的自定义业务标识配置
     */
    String INVALID_TOP_THREE = "invalid.top.three";


    /**
     *  支付配置有误，请重试
     */
    String PAY_CONFIG_ERROR = "pay.config.error";

    /**
     *  下options重复！
     */
    String OPTIONS_REPEAT = "options.repeat";


    /**
     *  attrType = choose时，options不能为空！
     */
    String ATTRTYPE_IS_CHOOS = "attrType.is.choos";

    /**
     *  请勿配置重复attrKey。
     */
    String ATTRKEY_NOT_REPEAT = "attrKey.not.repeat";

    /**
     *  请勿配置重复attrName。
     */
    String ATTRNAME_NOT_REPEAT = "attrName.not.repeat";

    /**
     *  配置不能为空！
     */
    String CONFIG_NOT_NULL = "config.not.null";

    /**
     *  自定义标识或字段数量最多允许10个。
     */
    String CUSTOM_BS_MAX_TEN = "custom.bs.max.ten";

    /**
     *  【微信统一支付】发起支付, 网络异常
     */
    String WX_PAY_NET_ERROR = "pay.net.error";

    /**
     *  【微信统一支付】支付异常
     */
    String WX_PAY_ERROR = "wx.pay.error";

    /**
     *  【微信统一支付】发起支付
     */
    String WX_PAY = "wx.pay";

    /**
     * 资源分析数据正在下载，请稍后查询下载任务！
     */
    String RESOURCE_ANALYSIS_DOWNLOAD_NOW = "resource.analysis.download.now";

    /**
     * 资源分析数据下载异常，请稍后重试!
     */
    String RESOURCE_ANALYSIS_DOWNLOAD_ERROR = "resource.analysis.download.error";

    /**
     *  不支持重复的业务标识！
     */
    String NOT_SUPPORTED_REPEAT_IDENTIFICATION = "not.supported.repeat.identification";

    /* 111 */
    /**
     *  系统配置缺失，请联系系统管理员！
     */
    String SYS_CONFIG_DELETION = "sys.config.deletion";


    /**
     *  没有找到充值记录
     */
    String DEPOSIT_NOT_EXIST = "deposit.not.exist";
    /**
     *  异步通知中的金额和数据库中的不一致
     */
    String AMOUNT_DISCREPANCY = "amount.discrepancy";

    /**
     *  数据异步处理中，账户已禁用！
     */
    String DATA_SYNC_HANDLE = "data.sync.handle";

    /**
     配置测试成功
     */
    String CONFIG_TEST_SUCCESS = "config.test.success";
    /**
     配置测试失败
     */
    String CONFIG_TEST_FAILURE = "config.test.failure";


    /**
     资源类审批已通过
     */
    String RESOURCE_PROCESS_AUDIT_PASS = "resource.process.audit.pass";
    /**
     "该分销商不存在"
     */
    String ERR_MSG_1 = "err.msg.1";
    /**
     许可证产品标识码已失效！
     */
    String ERR_MSG_2 = "err.msg.2";
    /**
     满减金额必须大于优惠券金额！
     */
    String ERR_MSG_3 = "err.msg.3";
    /**
     客户不存在，请重新选择！
     */
    String ERR_MSG_4 = "err.msg.4";
    /**
     开始时间不能小于当前时间
     */
    String ERR_MSG_5 = "err.msg.5";
    /**
     产品标识码未初始化
     */
    String ERR_MSG_6 = "err.msg.6";
    /**
     许可证中的权限不能为空
     */
    String ERR_MSG_7 = "err.msg.7";
    /**
     该类型不属于平台所需图片，不支持上传
     */
    String ERR_MSG_8 = "err.msg.8";
    /**
     桶策略错误
     */
    String ERR_MSG_9 = "err.msg.9";
    /**
     "包含不安全字符，请重新上传文件"
     */
    String ERR_MSG_10 = "err.msg.10";
    /**
     文件路径有误，请重新上传文件
     */
    String ERR_MSG_11 = "err.msg.11";
    /**
     文件格式有误
     */
    String ERR_MSG_12 = "err.msg.12";
    /**
     登录过于频繁，请稍后再试
     */
    String ERR_MSG_13 = "err.msg.13";
    /**
     该用户组织不存在
     */
    String ERR_MSG_14 = "err.msg.14";
    /**
     非法moduleType
     */
    String ERR_MSG_15 = "err.msg.15";
    /**
     无法禁用超级管理员
     */
    String ERR_MSG_16 = "err.msg.16";
    /**
     已有密钥，无法再次创建
     */
    String ERR_MSG_17 = "err.msg.17";
    /**
     云环境类型错误
     */
    String ERR_MSG_18 = "err.msg.18";
    /**
     采集开始时间不能大于当前时间
     */
    String ERR_MSG_19 = "err.msg.19";
    /**
     采集结束时间不能大于当前时间
     */
    String ERR_MSG_20 = "err.msg.20";
    /**
     无法创建，现金券类型非充值现金券
     */
    String ERR_MSG_21 = "err.msg.21";
    /**
     无法创建，现金券类型非抵扣现金券
     */
    String ERR_MSG_22 = "err.msg.22";
    /**
     升级期间可登录的用户账号，超时管理员默认必选且不可取消
     */
    String ERR_MSG_23 = "err.msg.23";
    /**
     许可证无PaaS服务
     */
    String ERR_MSG_24 = "err.msg.24";





    /**
     文件不存在
     */
    String FILE_NOT_ERROR = "file.not.error";

    /**
     内部错误，请重试。<br/>如果问题依旧，请联系管理员。
     */
    String COMMON_MSG_ERROR = "msg.10001";

    /**
     内部错误，请重试。<br/>如果问题依旧，请联系管理员。
     */
    String NEED_SMS_VALI = "need.sms.vali";

    /**
     "有工单还在使用中，暂时不能删除该类型"
     */
    String TICKET_DELETE_ERROR = "ticket.delete.error";
    /**
     * "模板还在使用中，暂时不能禁用该模板"
     */
    String TICKET_TEMPLATE_UPDATE_ERROR = "ticket.template.update.error";

    /**
     * "有工单还在使用中，暂时不能禁用该类型"
     */
    String TICKET_UPDATE_ERROR = "ticket.update.error";

    /**
     * hpc.cluster.no.exist =HPC集群不存在
     */
    String TENANT_STATUS_SYNCING = "tenant.status.syncing";


    /**
     * 条件的值不能为空
     */
    String ERR_MSG_BSS_1110291907 = "err.msg.bss.1110291907";

    /**
     * appSecrect不能为空
     */
    String ERR_MSG_BSS_1065477732 = "err.msg.bss.1065477732";

    /**
     * 资源池类型数据异常!
     */
    String ERR_MSG_BSS_781482921 = "err.msg.bss.781482921";

    /**
     * 参数异常，输入正确的参数!
     */
    String ERR_MSG_BSS_2038660916 = "err.msg.bss.2038660916";

    /**
     * 该用户不存在！
     */
    String ERR_MSG_BSS_867857480 = "err.msg.bss.867857480";

    /**
     * 目前只支持阿里云和AWS的RDS实例相关操作
     */
    String ERR_MSG_BSS_322116364 = "err.msg.bss.322116364";

    /**
     * 所选账期超过三个月!
     */
    String ERR_MSG_BSS_329069122 = "err.msg.bss.329069122";

    /**
     * 订单已被预审批，不能再进行拒绝操作
     */
    String ERR_MSG_BSS_481710708 = "err.msg.bss.481710708";

    /**
     * ModelArtsAK/SK！
     */
    String ERR_MSG_BSS_1352740509 = "err.msg.bss.1352740509";

    /**
     * 申请账户已被冻结，请解冻后操作
     */
    String ERR_MSG_BSS_1799502700 = "err.msg.bss.1799502700";

    /**
     * 不允许创建同名分区
     */
    String ERR_MSG_BSS_2052700584 = "err.msg.bss.2052700584";

    /**
     * 用户正在企业认证中,暂不支持订购套餐包
     */
    String ERR_MSG_BSS_1805126085 = "err.msg.bss.1805126085";

    /**
     * 通过自服务创建云硬盘失败
     */
    String ERR_MSG_BSS_167285631 = "err.msg.bss.167285631";

    /**
     * 子网不存在或已被删除，请刷新页面后重试
     */
    String ERR_MSG_BSS_1626536526 = "err.msg.bss.1626536526";

    /**
     * 用户ID不能为空！
     */
    String ERR_MSG_BSS_123976647 = "err.msg.bss.123976647";

    /**
     * 计费映射代码存在相同或者为空
     */
    String ERR_MSG_BSS_1159366803 = "err.msg.bss.1159366803";

    /**
     * 参数请勿输入特殊字符
     */
    String ERR_MSG_BSS_1803602344 = "err.msg.bss.1803602344";

    /**
     * 主账户为禁用状态，子账号不允许登录
     */
    String ERR_MSG_BSS_301621271 = "err.msg.bss.301621271";

    /**
     * 手机号验证异常,请联系管理员
     */
    String ERR_MSG_BSS_353428910 = "err.msg.bss.353428910";

    /**
     * 通知次数超出范围
     */
    String ERR_MSG_BSS_863862240 = "err.msg.bss.863862240";

    /**
     * 转换系数参数异常【整数位小于等于8位，小数位小于等于2位】
     */
    String ERR_MSG_BSS_1648514121 = "err.msg.bss.1648514121";

    /**
     * 参数异常，输入正确的参数
     */
    String ERR_MSG_BSS_1319710067 = "err.msg.bss.1319710067";

    /**
     * 申请单未找到
     */
    String ERR_MSG_BSS_640385077 = "err.msg.bss.640385077";

    /**
     * 备注不能为空
     */
    String ERR_MSG_BSS_257552465 = "err.msg.bss.257552465";

    /**
     * 值不能为null或空！
     */
    String ERR_MSG_BSS_1817036856 = "err.msg.bss.1817036856";

    /**
     * 启动所有定时任务出错.
     */
    String ERR_MSG_BSS_515693653 = "err.msg.bss.515693653";

    /**
     * 不属于审批候选人，无审批权限
     */
    String ERR_MSG_BSS_1615933454 = "err.msg.bss.1615933454";

    /**
     * 白名单参数错误
     */
    String ERR_MSG_BSS_518853129 = "err.msg.bss.518853129";

    /**
     * 账户信息为空
     */
    String ERR_MSG_BSS_1777448447 = "err.msg.bss.1777448447";

    /**
     * 时间表：
     */
    String ERR_MSG_BSS_815643376 = "err.msg.bss.815643376";

    /**
     * 当前云环境暂不支持此操作.
     */
    String ERR_MSG_BSS_765179043 = "err.msg.bss.765179043";

    /**
     * 会话过期单位不能为空
     */
    String ERR_MSG_BSS_390557213 = "err.msg.bss.390557213";

    /**
     * 测试成功！
     */
    String ERR_MSG_BSS_1160664616 = "err.msg.bss.1160664616";

    /**
     * 模板名称不能为空
     */
    String ERR_MSG_BSS_911745775 = "err.msg.bss.911745775";

    /**
     * 解锁实例只能由锁定用户解锁!
     */
    String ERR_MSG_BSS_1191715784 = "err.msg.bss.1191715784";

    /**
     * 验证码无效
     */
    String ERR_MSG_BSS_1020330924 = "err.msg.bss.1020330924";

    /**
     * 需要修改的防火墙对象组未找到！
     */
    String ERR_MSG_BSS_1036657177 = "err.msg.bss.1036657177";

    /**
     * 当前输入的IP段与已有的IP段重复，请检查后重试
     */
    String ERR_MSG_BSS_1214705395 = "err.msg.bss.1214705395";

    /**
     * 业务分类不存在
     */
    String ERR_MSG_BSS_1127296737 = "err.msg.bss.1127296737";

    /**
     * PvroiderUrl地区！
     */
    String ERR_MSG_BSS_1039738189 = "err.msg.bss.1039738189";

    /**
     * 产品类别不存在
     */
    String ERR_MSG_BSS_1230772179 = "err.msg.bss.1230772179";

    /**
     * 服务中已关联该主机模板,不能删除
     */
    String ERR_MSG_BSS_1634306856 = "err.msg.bss.1634306856";

    /**
     * 请输入正确的{}
     */
    String ERR_MSG_BSS_2140273362 = "err.msg.bss.2140273362";

    /**
     * 失败！
     */
    String ERR_MSG_BSS_23127917 = "err.msg.bss.23127917";

    /**
     * 请先实名认证
     */
    String ERR_MSG_BSS_2071221437 = "err.msg.bss.2071221437";

    /**
     * 未找到对应的产品!
     */
    String ERR_MSG_BSS_936016570 = "err.msg.bss.936016570";

    /**
     * 防火墙为空
     */
    String ERR_MSG_BSS_2029447872 = "err.msg.bss.2029447872";

    /**
     * FloatingIP服务不存在
     */
    String ERR_MSG_BSS_863539140 = "err.msg.bss.863539140";

    /**
     * 转换用户实体出错
     */
    String ERR_MSG_BSS_1648305177 = "err.msg.bss.1648305177";

    /**
     * IAM子用户配额不足！
     */
    String ERR_MSG_BSS_239284543 = "err.msg.bss.239284543";

    /**
     * 规则已是最低优先级
     */
    String ERR_MSG_BSS_1032585623 = "err.msg.bss.1032585623";

    /**
     * 不能锁定其他用户锁定的实例!
     */
    String ERR_MSG_BSS_1558315585 = "err.msg.bss.1558315585";

    /**
     * 验证码长度为6位
     */
    String ERR_MSG_BSS_1442388670 = "err.msg.bss.1442388670";

    /**
     * 该工单已分配却未完成或已被关闭！
     */
    String ERR_MSG_BSS_453699498 = "err.msg.bss.453699498";

    /**
     * 现金券作废失败.
     */
    String ERR_MSG_BSS_19127072 = "err.msg.bss.19127072";

    /**
     * 该分销商组织下存在关联客户，不能删除
     */
    String ERR_MSG_BSS_1087625575 = "err.msg.bss.1087625575";

    /**
     * 删除防火墙对象组失败，请重试
     */
    String ERR_MSG_BSS_1581307481 = "err.msg.bss.1581307481";

    /**
     * 创建防火墙对象组失败，请重试
     */
    String ERR_MSG_BSS_1338233122 = "err.msg.bss.1338233122";

    /**
     * 该企业不在认证中
     */
    String ERR_MSG_BSS_754211191 = "err.msg.bss.754211191";

    /**
     * 您获取的数据未找到
     */
    String ERR_MSG_BSS_1366126529 = "err.msg.bss.1366126529";

    /**
     * 资源不存在,删除失败
     */
    String ERR_MSG_BSS_248414765 = "err.msg.bss.248414765";

    /**
     * 该记录已删除，请刷新重新
     */
    String ERR_MSG_BSS_68569779 = "err.msg.bss.68569779";

    /**
     * 该云环境在环境中已经被纳管。
     */
    String ERR_MSG_BSS_547986071 = "err.msg.bss.547986071";

    /**
     * 该合同不能修改
     */
    String ERR_MSG_BSS_2124598980 = "err.msg.bss.2124598980";

    /**
     * 现金券号不存在
     */
    String ERR_MSG_BSS_1278593277 = "err.msg.bss.1278593277";

    /**
     * 开户账号不能为空
     */
    String ERR_MSG_BSS_1332437736 = "err.msg.bss.1332437736";

    /**
     * 开始时间不能小于[{}]
     */
    String ERR_MSG_BSS_264378936 = "err.msg.bss.264378936";

    /**
     * 运营实体数据越权,请确认当前用户的运营实体
     */
    String ERR_MSG_BSS_1152410115 = "err.msg.bss.1152410115";

    /**
     * 文件uri为空！
     */
    String ERR_MSG_BSS_1762660772 = "err.msg.bss.1762660772";

    /**
     * 该账号已被禁用
     */
    String ERR_MSG_BSS_62701130 = "err.msg.bss.62701130";

    /**
     * 只有租户管理员可以开通 HPC 服务
     */
    String ERR_MSG_BSS_730661677 = "err.msg.bss.730661677";

    /**
     * 更新失败：
     */
    String ERR_MSG_BSS_630837046 = "err.msg.bss.630837046";

    /**
     * 已存在同类型套餐包!
     */
    String ERR_MSG_BSS_361071887 = "err.msg.bss.361071887";

    /**
     * 删除 ldap 用户失败
     */
    String ERR_MSG_BSS_1273081760 = "err.msg.bss.1273081760";

    /**
     * 旧密码校验失败
     */
    String ERR_MSG_BSS_122169855 = "err.msg.bss.122169855";

    /**
     * 节点配置错误，无法审批
     */
    String ERR_MSG_BSS_1264914955 = "err.msg.bss.1264914955";

    /**
     * 的权限
     */
    String ERR_MSG_BSS_30014705 = "err.msg.bss.30014705";

    /**
     * 您无法配置自身账号的权限！
     */
    String ERR_MSG_BSS_1134714720 = "err.msg.bss.1134714720";

    /**
     * 端口校验成功
     */
    String ERR_MSG_BSS_1764384978 = "err.msg.bss.1764384978";

    /**
     * 实例名称不能为空.
     */
    String ERR_MSG_BSS_1869210578 = "err.msg.bss.1869210578";

    /**
     * saveDMac为空，macVerifyFlag为true，类为
     */
    String ERR_MSG_BSS_1805745395 = "err.msg.bss.1805745395";

    /**
     * 系统错误，请联系运营管理员。
     */
    String ERR_MSG_BSS_1070996688 = "err.msg.bss.1070996688";

    /**
     * 认证审核权限不足，请检查账户信息!
     */
    String ERR_MSG_BSS_1594121035 = "err.msg.bss.1594121035";

    /**
     * 该资源状态不允许操作
     */
    String ERR_MSG_BSS_290112983 = "err.msg.bss.290112983";

    /**
     * 数据中心Id不能大于%d或小于%0
     */
    String ERR_MSG_BSS_2000202533 = "err.msg.bss.2000202533";

    /**
     * 可使用额度不足，请充值后进行购买.
     */
    String ERR_MSG_BSS_840524885 = "err.msg.bss.840524885";

    /**
     * 价值[
     */
    String ERR_MSG_BSS_20063446 = "err.msg.bss.20063446";

    /**
     * HCSOAKSK
     */
    String ERR_MSG_BSS_1582281447 = "err.msg.bss.1582281447";

    /**
     * 该资源不可使用该合同。
     */
    String ERR_MSG_BSS_934939659 = "err.msg.bss.934939659";

    /**
     * 该主机不存在,请刷新后重新
     */
    String ERR_MSG_BSS_1624471436 = "err.msg.bss.1624471436";

    /**
     * 修改失败,HPC与SFS的资源限制应保持一致
     */
    String ERR_MSG_BSS_361294122 = "err.msg.bss.361294122";

    /**
     * 无权获取该配置
     */
    String ERR_MSG_BSS_1129952380 = "err.msg.bss.1129952380";

    /**
     * 请确认当前用户是否具备操作权限
     */
    String ERR_MSG_BSS_1360443513 = "err.msg.bss.1360443513";

    /**
     * 无支持资源类型
     */
    String ERR_MSG_BSS_2106173902 = "err.msg.bss.2106173902";

    /**
     * 文件大小不能超过
     */
    String ERR_MSG_BSS_229627319 = "err.msg.bss.229627319";

    /**
     * 监听器上已绑定后端服务器组,请先删除改监听器的后端服务组
     */
    String ERR_MSG_BSS_824671089 = "err.msg.bss.824671089";

    /**
     * 用户信息不存在
     */
    String ERR_MSG_BSS_1468051072 = "err.msg.bss.1468051072";

    /**
     * 合同来源折扣不允许删除！
     */
    String ERR_MSG_BSS_1418475853 = "err.msg.bss.1418475853";

    /**
     * 套餐包未上架
     */
    String ERR_MSG_BSS_133998730 = "err.msg.bss.133998730";

    /**
     * SFS申请大小超过平台最大限制
     */
    String ERR_MSG_BSS_2114803368 = "err.msg.bss.2114803368";

    /**
     * 服务实例不存在
     */
    String ERR_MSG_BSS_2113038820 = "err.msg.bss.2113038820";

    /**
     * 仅支持6位数字验证码
     */
    String ERR_MSG_BSS_1303147031 = "err.msg.bss.1303147031";

    /**
     * 云环境账户不存在或已被删除。
     */
    String ERR_MSG_BSS_2092645546 = "err.msg.bss.2092645546";

    /**
     * 合同不存在。
     */
    String ERR_MSG_BSS_704044297 = "err.msg.bss.704044297";

    /**
     * 审核企业失败
     */
    String ERR_MSG_BSS_1031314500 = "err.msg.bss.1031314500";

    /**
     * 无法转换
     */
    String ERR_MSG_BSS_804887339 = "err.msg.bss.804887339";

    /**
     * 记录已删除，请刷新重试
     */
    String ERR_MSG_BSS_1386025507 = "err.msg.bss.1386025507";

    /**
     * 类型参数不能为空
     */
    String ERR_MSG_BSS_1707950738 = "err.msg.bss.1707950738";

    /**
     * 内容超长
     */
    String ERR_MSG_BSS_645434990 = "err.msg.bss.645434990";

    /**
     * 奥格希德
     */
    String ERR_MSG_BSS_708181218 = "err.msg.bss.708181218";

    /**
     * 个快照,请先删除硬盘快照！
     */
    String ERR_MSG_BSS_1678834046 = "err.msg.bss.1678834046";

    /**
     * 云环境不存在
     */
    String ERR_MSG_BSS_1727217032 = "err.msg.bss.1727217032";

    /**
     * 密码规则不符合
     */
    String ERR_MSG_BSS_1145397087 = "err.msg.bss.1145397087";

    /**
     * 用户状态异常，操作失败
     */
    String ERR_MSG_BSS_146081217 = "err.msg.bss.146081217";

    /**
     * 节点删除每月最多{}次
     */
    String ERR_MSG_BSS_126506193 = "err.msg.bss.126506193";

    /**
     * 创建iam子用户失败
     */
    String ERR_MSG_BSS_743862339 = "err.msg.bss.743862339";

    /**
     * 模板类型不存在
     */
    String ERR_MSG_BSS_986078609 = "err.msg.bss.986078609";

    /**
     * 不匹配的资源类型！
     */
    String ERR_MSG_BSS_213209218 = "err.msg.bss.213209218";

    /**
     * 变配失败
     */
    String ERR_MSG_BSS_675924297 = "err.msg.bss.675924297";

    /**
     * }]！
     */
    String ERR_MSG_BSS_188289 = "err.msg.bss.188289";

    /**
     * 无法分析[
     */
    String ERR_MSG_BSS_833368004 = "err.msg.bss.833368004";

    /**
     * 有冻结用户,本次分发失败
     */
    String ERR_MSG_BSS_2070382864 = "err.msg.bss.2070382864";

    /**
     * 策略在使用中，无法禁用！
     */
    String ERR_MSG_BSS_1617896412 = "err.msg.bss.1617896412";

    /**
     * 修改账户bms策略成功
     */
    String ERR_MSG_BSS_806728822 = "err.msg.bss.806728822";

    /**
     * 同步云环境失败 |
     */
    String ERR_MSG_BSS_516602204 = "err.msg.bss.516602204";

    /**
     * 子账户存在邮箱重复
     */
    String ERR_MSG_BSS_1994162156 = "err.msg.bss.1994162156";

    /**
     * 该任务已被处理，请勿重复处理
     */
    String ERR_MSG_BSS_296564528 = "err.msg.bss.296564528";

    /**
     * 上传文件大小小于最小值，影响图片清晰度，请重新上传
     */
    String ERR_MSG_BSS_629967439 = "err.msg.bss.629967439";

    /**
     * 非法业务类型
     */
    String ERR_MSG_BSS_1855433262 = "err.msg.bss.1855433262";

    /**
     * 请联系运营管理员检查IAM是否存在ModelArts子账户的AK/SK信息！
     */
    String ERR_MSG_BSS_447508325 = "err.msg.bss.447508325";

    /**
     * 没有客户关联该分销商，不能直接创建子用户!
     */
    String ERR_MSG_BSS_1633845289 = "err.msg.bss.1633845289";

    /**
     * 发票申请失败
     */
    String ERR_MSG_BSS_317371793 = "err.msg.bss.317371793";

    /**
     * CostSummaryId：{}
     */
    String ERR_MSG_BSS_98957944 = "err.msg.bss.98957944";

    /**
     * Ldap连接失败，请联系系统管理员确认Ldap认证配置是否正确。
     */
    String ERR_MSG_BSS_870677674 = "err.msg.bss.870677674";

    /**
     * 下发失败，该用户已拥有该优惠券
     */
    String ERR_MSG_BSS_1496059777 = "err.msg.bss.1496059777";

    /**
     * 加载吊销列表失败
     */
    String ERR_MSG_BSS_1070512472 = "err.msg.bss.1070512472";

    /**
     * 云环境参数非法,请检查云环境参数
     */
    String ERR_MSG_BSS_2105918695 = "err.msg.bss.2105918695";

    /**
     * 关联的实例已经分配.不能删除
     */
    String ERR_MSG_BSS_1332800946 = "err.msg.bss.1332800946";

    /**
     * 计量数据正在生成，请稍后下载！
     */
    String ERR_MSG_BSS_1308608287 = "err.msg.bss.1308608287";

    /**
     * 目前只支持华为云的RDS实例相关操作
     */
    String ERR_MSG_BSS_532460964 = "err.msg.bss.532460964";

    /**
     * HPC服务状态异常
     */
    String ERR_MSG_BSS_1657593872 = "err.msg.bss.1657593872";

    /**
     * 有效期时间不正确
     */
    String ERR_MSG_BSS_799737574 = "err.msg.bss.799737574";

    /**
     * 规格族名称存在相同或者为空
     */
    String ERR_MSG_BSS_1089143533 = "err.msg.bss.1089143533";

    /**
     * 堡垒机关联
     */
    String ERR_MSG_BSS_54650058 = "err.msg.bss.54650058";

    /**
     * 未查询到价格
     */
    String ERR_MSG_BSS_62406574 = "err.msg.bss.62406574";

    /**
     * 退订专属资源池,调用ccm失败
     */
    String ERR_MSG_BSS_1464823031 = "err.msg.bss.1464823031";

    /**
     * 共享池在扩容中，无法进行审批操作
     */
    String ERR_MSG_BSS_744689214 = "err.msg.bss.744689214";

    /**
     * 该状态已经生效，不可重复设置
     */
    String ERR_MSG_BSS_1253324408 = "err.msg.bss.1253324408";

    /**
     * MQ实例创建失败！接口无返回值！
     */
    String ERR_MSG_BSS_824250028 = "err.msg.bss.824250028";

    /**
     * 当前产品未关联运营实体
     */
    String ERR_MSG_BSS_1964963679 = "err.msg.bss.1964963679";

    /**
     * 邮箱发送过于频繁，请稍后在操作
     */
    String ERR_MSG_BSS_403448001 = "err.msg.bss.403448001";

    /**
     * 已上架的规格不能删除！
     */
    String ERR_MSG_BSS_683292687 = "err.msg.bss.683292687";

    /**
     * 无法删除已上架的套餐包!
     */
    String ERR_MSG_BSS_612346356 = "err.msg.bss.612346356";

    /**
     * 修改失败，请重试
     */
    String ERR_MSG_BSS_449593906 = "err.msg.bss.449593906";

    /**
     * 自动审批续订单失败，订单不存在
     */
    String ERR_MSG_BSS_639922364 = "err.msg.bss.639922364";

    /**
     * 实例关联的计算资源无法找到可匹配的网络
     */
    String ERR_MSG_BSS_1309678389 = "err.msg.bss.1309678389";

    /**
     * 请检查开始时间和结束时间是否正确!
     */
    String ERR_MSG_BSS_1261968050 = "err.msg.bss.1261968050";

    /**
     * 设置登录超时
     */
    String ERR_MSG_BSS_1327411653 = "err.msg.bss.1327411653";

    /**
     * 只有已停止的实例和当前磁盘没有创建中的快照才可以回滚磁盘
     */
    String ERR_MSG_BSS_1951789527 = "err.msg.bss.1951789527";

    /**
     * 不存在的角色ID:
     */
    String ERR_MSG_BSS_1757283416 = "err.msg.bss.1757283416";

    /**
     * 账户状态异常
     */
    String ERR_MSG_BSS_2043115026 = "err.msg.bss.2043115026";

    /**
     * 价值
     */
    String ERR_MSG_BSS_647205 = "err.msg.bss.647205";

    /**
     * 请将示踪剂注入TraceUtil。可以使用TraceUtil.injectTracer方法
     */
    String ERR_MSG_BSS_1128510587 = "err.msg.bss.1128510587";

    /**
     * 存在已启用的策略，请先禁用掉已启用的策略后重试
     */
    String ERR_MSG_BSS_810479759 = "err.msg.bss.810479759";

    /**
     * 文件路径为空
     */
    String ERR_MSG_BSS_629255036 = "err.msg.bss.629255036";

    /**
     * 可用区不存在！
     */
    String ERR_MSG_BSS_1390978107 = "err.msg.bss.1390978107";

    /**
     * 参数 \
     */
    String ERR_MSG_BSS_663734954 = "err.msg.bss.663734954";

    /**
     * 资产编号已经存在.
     */
    String ERR_MSG_BSS_458585891 = "err.msg.bss.458585891";

    /**
     * 到期时间天数不合法
     */
    String ERR_MSG_BSS_1971356666 = "err.msg.bss.1971356666";

    /**
     * 输入参数未找到
     */
    String ERR_MSG_BSS_1406556548 = "err.msg.bss.1406556548";

    /**
     * 该邮箱已被使用
     */
    String ERR_MSG_BSS_850188202 = "err.msg.bss.850188202";

    /**
     * 下载失败!模板不存在
     */
    String ERR_MSG_BSS_1572881348 = "err.msg.bss.1572881348";

    /**
     * 该用户不在组织中
     */
    String ERR_MSG_BSS_1156967109 = "err.msg.bss.1156967109";

    /**
     * 已存在相同规格定价
     */
    String ERR_MSG_BSS_723028075 = "err.msg.bss.723028075";

    /**
     * 移除vlan池失败！
     */
    String ERR_MSG_BSS_1331953081 = "err.msg.bss.1331953081";

    /**
     * 该用户已禁用，请勿重复操作！
     */
    String ERR_MSG_BSS_1686132067 = "err.msg.bss.1686132067";

    /**
     * 弹性ip状态不是未使用，请重新选择。
     */
    String ERR_MSG_BSS_1790743107 = "err.msg.bss.1790743107";

    /**
     * 北向接口服务异常
     */
    String ERR_MSG_BSS_1024429538 = "err.msg.bss.1024429538";

    /**
     * 需先下架该产品，再操作!
     */
    String ERR_MSG_BSS_2138631237 = "err.msg.bss.2138631237";

    /**
     * 云环境类型参数错误！
     */
    String ERR_MSG_BSS_2060927480 = "err.msg.bss.2060927480";

    /**
     * 该账户不存在
     */
    String ERR_MSG_BSS_1421645465 = "err.msg.bss.1421645465";

    /**
     * 选择的区域与云环境账户不匹配，请查证后重试。
     */
    String ERR_MSG_BSS_945964707 = "err.msg.bss.945964707";

    /**
     * 存储容量不能超过限定值
     */
    String ERR_MSG_BSS_1077740230 = "err.msg.bss.1077740230";

    /**
     * 未填写邮寄地址，请填写后重试
     */
    String ERR_MSG_BSS_131217663 = "err.msg.bss.131217663";

    /**
     * 该用户无组织
     */
    String ERR_MSG_BSS_1502175535 = "err.msg.bss.1502175535";

    /**
     * 子用户无权限！
     */
    String ERR_MSG_BSS_500058829 = "err.msg.bss.500058829";

    /**
     * 创建BMS白名单成功
     */
    String ERR_MSG_BSS_565332555 = "err.msg.bss.565332555";

    /**
     * 规格值不能为空!
     */
    String ERR_MSG_BSS_1765666893 = "err.msg.bss.1765666893";

    /**
     * HPC共享资源池已开始创建，请勿执行此操作!
     */
    String ERR_MSG_BSS_1557755466 = "err.msg.bss.1557755466";

    /**
     * 模特艺术！
     */
    String ERR_MSG_BSS_227346996 = "err.msg.bss.227346996";

    /**
     * )请联系平台管理员
     */
    String ERR_MSG_BSS_1442499585 = "err.msg.bss.1442499585";

    /**
     * 当前用户状态不可用，请检查用户状态
     */
    String ERR_MSG_BSS_2036929060 = "err.msg.bss.2036929060";

    /**
     * 主机已挂载该弹性文件
     */
    String ERR_MSG_BSS_86345836 = "err.msg.bss.86345836";

    /**
     * 不支持的类型
     */
    String ERR_MSG_BSS_254203627 = "err.msg.bss.254203627";

    /**
     * GPU资源组不存在或已删除
     */
    String ERR_MSG_BSS_778227473 = "err.msg.bss.778227473";

    /**
     * 作业作业名称=
     */
    String ERR_MSG_BSS_662627682 = "err.msg.bss.662627682";

    /**
     * 折扣已经启用，无法修改
     */
    String ERR_MSG_BSS_921225621 = "err.msg.bss.921225621";

    /**
     * 业务类型不支持修改！
     */
    String ERR_MSG_BSS_1021119918 = "err.msg.bss.1021119918";

    /**
     * 请勿频繁提交
     */
    String ERR_MSG_BSS_1343440244 = "err.msg.bss.1343440244";

    /**
     * 节点未找到！
     */
    String ERR_MSG_BSS_414198108 = "err.msg.bss.414198108";

    /**
     * 当前资源变更中或已过期、已冻结，不支持变更！
     */
    String ERR_MSG_BSS_1845439562 = "err.msg.bss.1845439562";

    /**
     * 校验成功
     */
    String ERR_MSG_BSS_832959066 = "err.msg.bss.832959066";

    /**
     * 参数不合法
     */
    String ERR_MSG_BSS_879190036 = "err.msg.bss.879190036";

    /**
     * 用户组参数不合法
     */
    String ERR_MSG_BSS_1048361353 = "err.msg.bss.1048361353";

    /**
     * 已上架的套餐包不能修改!
     */
    String ERR_MSG_BSS_906077120 = "err.msg.bss.906077120";

    /**
     * 体积
     */
    String ERR_MSG_BSS_660732 = "err.msg.bss.660732";

    /**
     * 云环境不存在或已被删除。
     */
    String ERR_MSG_BSS_1332118329 = "err.msg.bss.1332118329";

    /**
     * 阈值不能小于0
     */
    String ERR_MSG_BSS_1802643 = "err.msg.bss.1802643";

    /**
     * 该权限组不存在,请刷新后重试
     */
    String ERR_MSG_BSS_581179655 = "err.msg.bss.581179655";

    /**
     * 共享资源池不存在
     */
    String ERR_MSG_BSS_314400541 = "err.msg.bss.314400541";

    /**
     * 产品名称重复
     */
    String ERR_MSG_BSS_157327773 = "err.msg.bss.157327773";

    /**
     * 所在组织权限不足,不能执行操作
     */
    String ERR_MSG_BSS_1738370990 = "err.msg.bss.1738370990";

    /**
     * 获取可用区信息异常！
     */
    String ERR_MSG_BSS_1753693243 = "err.msg.bss.1753693243";

    /**
     * 暂不支持的操作
     */
    String ERR_MSG_BSS_1990443050 = "err.msg.bss.1990443050";

    /**
     * 负载均衡的网络类型为经典网络，暂不支持绑定弹性IP
     */
    String ERR_MSG_BSS_824600736 = "err.msg.bss.824600736";

    /**
     * 该发票已经不存在，请刷新后重试
     */
    String ERR_MSG_BSS_1690538322 = "err.msg.bss.1690538322";

    /**
     * 已关联流程的模板无法禁用
     */
    String ERR_MSG_BSS_535134719 = "err.msg.bss.535134719";

    /**
     * 套餐包使用明细正在下载，请稍后查询下载任务！
     */
    String ERR_MSG_BSS_1338214492 = "err.msg.bss.1338214492";

    /**
     * 文件标识码不能为空
     */
    String ERR_MSG_BSS_599361245 = "err.msg.bss.599361245";

    /**
     * 主机需要有公网Ip
     */
    String ERR_MSG_BSS_1803564757 = "err.msg.bss.1803564757";

    /**
     * 扩容大小相等
     */
    String ERR_MSG_BSS_613599113 = "err.msg.bss.613599113";

    /**
     * 账户已被冻结
     */
    String ERR_MSG_BSS_1894653058 = "err.msg.bss.1894653058";

    /**
     * 请求信息不能为空!
     */
    String ERR_MSG_BSS_1639417704 = "err.msg.bss.1639417704";

    /**
     * 描述最多110个字
     */
    String ERR_MSG_BSS_902136194 = "err.msg.bss.902136194";

    /**
     * 用户资源已冻结。
     */
    String ERR_MSG_BSS_164811277 = "err.msg.bss.164811277";

    /**
     * 不支持的资源类型
     */
    String ERR_MSG_BSS_658135713 = "err.msg.bss.658135713";

    /**
     * 无法从创建枚举
     */
    String ERR_MSG_BSS_1391729756 = "err.msg.bss.1391729756";

    /**
     * RightcloudAdapter没有响应。
     */
    String ERR_MSG_BSS_862848093 = "err.msg.bss.862848093";

    /**
     * {}不存在，任务中断
     */
    String ERR_MSG_BSS_1776496233 = "err.msg.bss.1776496233";

    /**
     * 所填写的IP已经被公网IP占用
     */
    String ERR_MSG_BSS_846656069 = "err.msg.bss.846656069";

    /**
     * 当前用户无权查看
     */
    String ERR_MSG_BSS_1762386130 = "err.msg.bss.1762386130";

    /**
     * 项目名称已存在
     */
    String ERR_MSG_BSS_2135088490 = "err.msg.bss.2135088490";

    /**
     * IP地址不合规
     */
    String ERR_MSG_BSS_1817908046 = "err.msg.bss.1817908046";

    /**
     * 邮箱发送地址为空
     */
    String ERR_MSG_BSS_1827944861 = "err.msg.bss.1827944861";

    /**
     * 该实例正在处理中,不能进行其他操作
     */
    String ERR_MSG_BSS_45030728 = "err.msg.bss.45030728";

    /**
     * 资源已下架
     */
    String ERR_MSG_BSS_94562511 = "err.msg.bss.94562511";

    /**
     * 该资源计费所属产品正在被使用，请先下架产品！
     */
    String ERR_MSG_BSS_2000872691 = "err.msg.bss.2000872691";

    /**
     * 禁止对当前用户授更高权限
     */
    String ERR_MSG_BSS_1130788360 = "err.msg.bss.1130788360";

    /**
     * 注册场所地址不能为空
     */
    String ERR_MSG_BSS_1086825866 = "err.msg.bss.1086825866";

    /**
     * 账号已过期
     */
    String ERR_MSG_BSS_324593543 = "err.msg.bss.324593543";

    /**
     * 配置值必须为数字
     */
    String ERR_MSG_BSS_832539344 = "err.msg.bss.832539344";

    /**
     * ，服务到期发送提醒开关参数未打开
     */
    String ERR_MSG_BSS_818952555 = "err.msg.bss.818952555";

    /**
     * 当前产品没有所对应的运营实体账户
     */
    String ERR_MSG_BSS_175772512 = "err.msg.bss.175772512";

    /**
     * 产品【{0}】流程不存在！
     */
    String ERR_MSG_BSS_175772513 = "err.msg.bss.175772513";

    /**
     * 删除成功
     */
    String ERR_MSG_BSS_664123859 = "err.msg.bss.664123859";

    /**
     * 实例已删除，请同步实例信息！
     */
    String ERR_MSG_BSS_1685825145 = "err.msg.bss.1685825145";

    /**
     * 无权限
     */
    String ERR_MSG_BSS_25920845 = "err.msg.bss.25920845";

    /**
     * 用户名大于最大长度配置
     */
    String ERR_MSG_BSS_386457452 = "err.msg.bss.386457452";

    /**
     * 大于虚拟机使用最大资源数
     */
    String ERR_MSG_BSS_250861958 = "err.msg.bss.250861958";

    /**
     * 邮箱验证异常,请联系管理员
     */
    String ERR_MSG_BSS_1198181997 = "err.msg.bss.1198181997";

    /**
     * 读取文件失败！
     */
    String ERR_MSG_BSS_1421149085 = "err.msg.bss.1421149085";

    /**
     * 当前处理人无审批权限，请于处理信息查看具体处理人
     */
    String ERR_MSG_BSS_501313290 = "err.msg.bss.501313290";

    /**
     * 标准专属集群文件系统只能开通5个
     */
    String ERR_MSG_BSS_636792381 = "err.msg.bss.636792381";

    /**
     * 同一来源今日调用次数已达上限！
     */
    String ERR_MSG_BSS_825944351 = "err.msg.bss.825944351";

    /**
     * 修改套餐包成功
     */
    String ERR_MSG_BSS_652007152 = "err.msg.bss.652007152";

    /**
     * 对象不能为空
     */
    String ERR_MSG_BSS_780030056 = "err.msg.bss.780030056";

    /**
     * 当前许可证已过期，请更新许可证后进行操作
     */
    String ERR_MSG_BSS_206841243 = "err.msg.bss.206841243";

    /**
     * 集群不存在
     */
    String ERR_MSG_BSS_2015242433 = "err.msg.bss.2015242433";

    /**
     * 该权限组不存在,请刷新后重新
     */
    String ERR_MSG_BSS_581169890 = "err.msg.bss.581169890";

    /**
     * 片段不能为空
     */
    String ERR_MSG_BSS_349269854 = "err.msg.bss.349269854";

    /**
     * 请勿重复提交申请
     */
    String ERR_MSG_BSS_806681152 = "err.msg.bss.806681152";

    /**
     * 规格定价未找到！
     */
    String ERR_MSG_BSS_798962822 = "err.msg.bss.798962822";

    /**
     * ,包含特殊字符
     */
    String ERR_MSG_BSS_1710045038 = "err.msg.bss.1710045038";

    /**
     * 该防火墙已关联服务链！
     */
    String ERR_MSG_BSS_1692537231 = "err.msg.bss.1692537231";

    /**
     * 申请扩容失败
     */
    String ERR_MSG_BSS_528265304 = "err.msg.bss.528265304";

    /**
     * 无法找到有足够空闲空间的存储。
     */
    String ERR_MSG_BSS_551289134 = "err.msg.bss.551289134";

    /**
     * 资源区域不存在
     */
    String ERR_MSG_BSS_1149954380 = "err.msg.bss.1149954380";

    /**
     * ]资源不足，请确认后重新提交！
     */
    String ERR_MSG_BSS_1696329740 = "err.msg.bss.1696329740";

    /**
     * 自动续费，续费周期不能为空
     */
    String ERR_MSG_BSS_884141291 = "err.msg.bss.884141291";

    /**
     * 大于共享池可用节点数量!
     */
    String ERR_MSG_BSS_1934277331 = "err.msg.bss.1934277331";

    /**
     * 该防火墙正在被使用，不可删除
     */
    String ERR_MSG_BSS_1542407166 = "err.msg.bss.1542407166";

    /**
     * 优惠券已作废，无法分发
     */
    String ERR_MSG_BSS_304025433 = "err.msg.bss.304025433";

    /**
     * 没有找到要更新的桶实例
     */
    String ERR_MSG_BSS_1709965410 = "err.msg.bss.1709965410";

    /**
     * 参数有误！
     */
    String ERR_MSG_BSS_872562419 = "err.msg.bss.872562419";

    /**
     * 第三方服务提供者未配置相关流程，请联系服务发布者配置！
     */
    String ERR_MSG_BSS_901733511 = "err.msg.bss.901733511";

    /**
     * ccsp进程失败，原因[无字段ccsp_mac]，类为
     */
    String ERR_MSG_BSS_349543434 = "err.msg.bss.349543434";

    /**
     * 该工单已分配！
     */
    String ERR_MSG_BSS_1339980509 = "err.msg.bss.1339980509";

    /**
     * 无法删除系统用户
     */
    String ERR_MSG_BSS_2041991220 = "err.msg.bss.2041991220";

    /**
     * 资源池未配置相关价格
     */
    String ERR_MSG_BSS_1793371337 = "err.msg.bss.1793371337";

    /**
     * 设置折扣已经存在，重复内容为:
     */
    String ERR_MSG_BSS_513162856 = "err.msg.bss.513162856";

    /**
     * 请先移除所有已关联的用户。
     */
    String ERR_MSG_BSS_1686746977 = "err.msg.bss.1686746977";

    /**
     * 未找到退订订单
     */
    String ERR_MSG_BSS_726330673 = "err.msg.bss.726330673";

    /**
     * ：[{}]
     */
    String ERR_MSG_BSS_184753434 = "err.msg.bss.184753434";

    /**
     * 公告ID为空
     */
    String ERR_MSG_BSS_1798638791 = "err.msg.bss.1798638791";

    /**
     * 不存在的桶
     */
    String ERR_MSG_BSS_1992045775 = "err.msg.bss.1992045775";

    /**
     * 更新防火墙对象组失败，请重试
     */
    String ERR_MSG_BSS_1143879009 = "err.msg.bss.1143879009";

    /**
     * 当前挂载点下有成功挂载的实例，请先卸载再删除！
     */
    String ERR_MSG_BSS_544490797 = "err.msg.bss.544490797";

    /**
     * 大于共享池可用节点数量！
     */
    String ERR_MSG_BSS_1934342579 = "err.msg.bss.1934342579";

    /**
     * 该文件不支持此操作！
     */
    String ERR_MSG_BSS_686914137 = "err.msg.bss.686914137";

    /**
     * 当前资源已过期或已冻结，请拒绝后待用户续订。
     */
    String ERR_MSG_BSS_54840825 = "err.msg.bss.54840825";

    /**
     * 文件解析错误，请确认文件是否损坏！
     */
    String ERR_MSG_BSS_1965520264 = "err.msg.bss.1965520264";

    /**
     * 实例超过绑定限制数
     */
    String ERR_MSG_BSS_2087196974 = "err.msg.bss.2087196974";

    /**
     * 询价访问失败,请稍后再试.
     */
    String ERR_MSG_BSS_1657756514 = "err.msg.bss.1657756514";

    /**
     * 意外值：
     */
    String ERR_MSG_BSS_762835589 = "err.msg.bss.762835589";

    /**
     * 请勿重复下载
     */
    String ERR_MSG_BSS_1404346982 = "err.msg.bss.1404346982";

    /**
     * 收件地址为空!
     */
    String ERR_MSG_BSS_1321673711 = "err.msg.bss.1321673711";

    /**
     * 计算节点数超过限制，无法扩容，请联系管理员。
     */
    String ERR_MSG_BSS_1079119111 = "err.msg.bss.1079119111";

    /**
     * 账户还未到有效开始时间
     */
    String ERR_MSG_BSS_575826027 = "err.msg.bss.575826027";

    /**
     * 参数不能为空
     */
    String ERR_MSG_BSS_1474238722 = "err.msg.bss.1474238722";

    /**
     * 帐户ID！
     */
    String ERR_MSG_BSS_1512721855 = "err.msg.bss.1512721855";

    /**
     * 缺少TenantUserPass
     */
    String ERR_MSG_BSS_1406862653 = "err.msg.bss.1406862653";

    /**
     * 数据中心不存在或已被删除。
     */
    String ERR_MSG_BSS_1421877512 = "err.msg.bss.1421877512";

    /**
     * 删除身份提供商失败！
     */
    String ERR_MSG_BSS_1481254616 = "err.msg.bss.1481254616";

    /**
     * 云账号不存在
     */
    String ERR_MSG_BSS_36427653 = "err.msg.bss.36427653";

    /**
     * 文件大小限制50M
     */
    String ERR_MSG_BSS_983181685 = "err.msg.bss.983181685";

    /**
     * 不能创建两条完全相同的安全组规则
     */
    String ERR_MSG_BSS_1770291058 = "err.msg.bss.1770291058";

    /**
     * 审核用户成功
     */
    String ERR_MSG_BSS_1327286805 = "err.msg.bss.1327286805";

    /**
     * 参数非法
     */
    String ERR_MSG_BSS_664962981 = "err.msg.bss.664962981";

    /**
     * 备注最多500个字
     */
    String ERR_MSG_BSS_1408973721 = "err.msg.bss.1408973721";

    /**
     * 该实体卡配置存在对应的虚拟卡配置, 不能进行删除！
     */
    String ERR_MSG_BSS_1893220612 = "err.msg.bss.1893220612";

    /**
     * AI共享资源池ID不合法
     */
    String ERR_MSG_BSS_1625969267 = "err.msg.bss.1625969267";

    /**
     * 邮箱格式不正确
     */
    String ERR_MSG_BSS_1899066914 = "err.msg.bss.1899066914";

    /**
     * 订单异常，请检查！
     */
    String ERR_MSG_BSS_1729141032 = "err.msg.bss.1729141032";

    /**
     * 有不存在的用户，无法删除
     */
    String ERR_MSG_BSS_677366086 = "err.msg.bss.677366086";

    /**
     * 无支持指标{}
     */
    String ERR_MSG_BSS_1781429716 = "err.msg.bss.1781429716";

    /**
     * 申请单不存在
     */
    String ERR_MSG_BSS_646618804 = "err.msg.bss.646618804";

    /**
     * 订购时长{
     */
    String ERR_MSG_BSS_244609625 = "err.msg.bss.244609625";

    /**
     * 调用==空
     */
    String ERR_MSG_BSS_364391627 = "err.msg.bss.364391627";

    /**
     * 资源配置中
     */
    String ERR_MSG_BSS_81542848 = "err.msg.bss.81542848";

    /**
     * 添加消费记录失败，消费类型不支持！
     */
    String ERR_MSG_BSS_993627072 = "err.msg.bss.993627072";

    /**
     * 开通IAM权限失败,请重试或联系管理员处理
     */
    String ERR_MSG_BSS_1151583847 = "err.msg.bss.1151583847";

    /**
     * 获取当前用户失败
     */
    String ERR_MSG_BSS_1038086020 = "err.msg.bss.1038086020";

    /**
     * 合同模板不存在
     */
    String ERR_MSG_BSS_1510322981 = "err.msg.bss.1510322981";

    /**
     * 查询规则模板[
     */
    String ERR_MSG_BSS_1666874155 = "err.msg.bss.1666874155";

    /**
     * 当前用户不能修改自己的信息
     */
    String ERR_MSG_BSS_2002246151 = "err.msg.bss.2002246151";

    /**
     * VolumeAttachmentApi不存在
     */
    String ERR_MSG_BSS_1551204448 = "err.msg.bss.1551204448";

    /**
     * 该业务已绑定流程
     */
    String ERR_MSG_BSS_473997209 = "err.msg.bss.473997209";

    /**
     * HPC专属资源池激活失败！
     */
    String ERR_MSG_BSS_1801106268 = "err.msg.bss.1801106268";

    /**
     * 指定月份格式错误
     */
    String ERR_MSG_BSS_271528655 = "err.msg.bss.271528655";

    /**
     * 系统许可证将于
     */
    String ERR_MSG_BSS_1162216082 = "err.msg.bss.1162216082";

    /**
     * 的值之间不能为空
     */
    String ERR_MSG_BSS_1278900015 = "err.msg.bss.1278900015";

    /**
     * 产品已上架，无法操作！
     */
    String ERR_MSG_BSS_134089269 = "err.msg.bss.134089269";

    /**
     * 集群可挂载文件系统已超过最大限额
     */
    String ERR_MSG_BSS_1758881335 = "err.msg.bss.1758881335";

    /**
     * 请选择现金券
     */
    String ERR_MSG_BSS_208845664 = "err.msg.bss.208845664";

    /**
     * 已欠费，请充值后再操作！
     */
    String ERR_MSG_BSS_1095922662 = "err.msg.bss.1095922662";

    /**
     * 500
     */
    String ERR_MSG_BSS_52469 = "err.msg.bss.52469";

    /**
     * applicaitonContext，applicationContext.xmlSpringContextHolder
     */
    String ERR_MSG_BSS_2092345763 = "err.msg.bss.2092345763";

    /**
     * 非法计费策略状态值！
     */
    String ERR_MSG_BSS_1861194034 = "err.msg.bss.1861194034";

    /**
     * 存储类型不能为空
     */
    String ERR_MSG_BSS_875324720 = "err.msg.bss.875324720";

    /**
     * 申请开票失败，更新账单周期数据异常，请核查
     */
    String ERR_MSG_BSS_1879541542 = "err.msg.bss.1879541542";

    /**
     * 您指定的文件不合规！无法下载
     */
    String ERR_MSG_BSS_297011447 = "err.msg.bss.297011447";

    /**
     * 的值不能为空
     */
    String ERR_MSG_BSS_853028280 = "err.msg.bss.853028280";

    /**
     * 未填写电话，请填写后重试
     */
    String ERR_MSG_BSS_1733784797 = "err.msg.bss.1733784797";

    /**
     * 密码不能为空
     */
    String ERR_MSG_BSS_1532732299 = "err.msg.bss.1532732299";

    /**
     * 国密连接测试失败,请检查国密配置
     */
    String ERR_MSG_BSS_2029541064 = "err.msg.bss.2029541064";

    /**
     * 开始时间大于等于结束时间!
     */
    String ERR_MSG_BSS_425013086 = "err.msg.bss.425013086";

    /**
     * 父环节ID不能空！
     */
    String ERR_MSG_BSS_1454618267 = "err.msg.bss.1454618267";

    /**
     * 反射包地址不正确
     */
    String ERR_MSG_BSS_1644324058 = "err.msg.bss.1644324058";

    /**
     * 选择的GPU资源组不存在
     */
    String ERR_MSG_BSS_1730809427 = "err.msg.bss.1730809427";

    /**
     * 不能对主账号进行操作
     */
    String ERR_MSG_BSS_1304836285 = "err.msg.bss.1304836285";

    /**
     * 审核失败，请核对是否选择通过或驳回
     */
    String ERR_MSG_BSS_447200890 = "err.msg.bss.447200890";

    /**
     * 当前资源处于续订中，不支持重复续订
     */
    String ERR_MSG_BSS_1464118032 = "err.msg.bss.1464118032";

    /**
     * 没有可缩容的节点信息
     */
    String ERR_MSG_BSS_1372824050 = "err.msg.bss.1372824050";

    /**
     * ：
     */
    String ERR_MSG_BSS_65306 = "err.msg.bss.65306";

    /**
     * ConsolesApi不存在
     */
    String ERR_MSG_BSS_1734119039 = "err.msg.bss.1734119039";

    /**
     * 实例
     */
    String ERR_MSG_BSS_747437 = "err.msg.bss.747437";

    /**
     * 签名检查失败。
     */
    String ERR_MSG_BSS_163906618 = "err.msg.bss.163906618";

    /**
     * 删除重复iam子用户失败！
     */
    String ERR_MSG_BSS_1172815713 = "err.msg.bss.1172815713";

    /**
     * 暂不支持该验证类型
     */
    String ERR_MSG_BSS_370098515 = "err.msg.bss.370098515";

    /**
     * 未找到云环境
     */
    String ERR_MSG_BSS_1655439511 = "err.msg.bss.1655439511";

    /**
     * 计费策略已启用，非法操作！
     */
    String ERR_MSG_BSS_1017073677 = "err.msg.bss.1017073677";

    /**
     * 发送频繁，请稍后再试
     */
    String ERR_MSG_BSS_1065179564 = "err.msg.bss.1065179564";

    /**
     * 未找到登陆用户，请登陆后重试
     */
    String ERR_MSG_BSS_1619437642 = "err.msg.bss.1619437642";

    /**
     * 当前已使用{}GB,
     */
    String ERR_MSG_BSS_903444814 = "err.msg.bss.903444814";

    /**
     * 该业务不在双重审核业务中
     */
    String ERR_MSG_BSS_1149454407 = "err.msg.bss.1149454407";

    /**
     * 该产品不存在！
     */
    String ERR_MSG_BSS_1514956579 = "err.msg.bss.1514956579";

    /**
     * 该发票数据暂不支持显示详情
     */
    String ERR_MSG_BSS_1761956622 = "err.msg.bss.1761956622";

    /**
     * 端口已存在，请重新绑定
     */
    String ERR_MSG_BSS_479510779 = "err.msg.bss.479510779";

    /**
     * 当前用户无操作权限
     */
    String ERR_MSG_BSS_1225495949 = "err.msg.bss.1225495949";

    /**
     * 用户id参数错误
     */
    String ERR_MSG_BSS_2061045042 = "err.msg.bss.2061045042";

    /**
     * ModelArts存在没有退订的专属资源池，请先退订专属资源池！
     */
    String ERR_MSG_BSS_915802207 = "err.msg.bss.915802207";

    /**
     * ，Id：{}
     */
    String ERR_MSG_BSS_1076912469 = "err.msg.bss.1076912469";

    /**
     * 当前服务已发布或正在发布审批中,不能操作！
     */
    String ERR_MSG_BSS_1299059892 = "err.msg.bss.1299059892";

    /**
     * 请求地址[
     */
    String ERR_MSG_BSS_421287616 = "err.msg.bss.421287616";

    /**
     * 不能对非正常状态的资源操作
     */
    String ERR_MSG_BSS_1373253110 = "err.msg.bss.1373253110";



    /**
     * 资源{0},暂不能执行当前操作
     */
    String CURRENT_OPERATION_NOT_SUPPORTED = "current.operation.not.supported";

    /**
     * 存在{0}资源{1},暂不能执行当前操作
     */
    String CURRENT_CONTAIN_OPERATION_NOT_SUPPORTED = "current.contain.operation.not.supported";

    /**
     * 该环境已经中断连接
     */
    String ERR_MSG_BSS_1720236067 = "err.msg.bss.1720236067";

    /**
     * 云环境类型不存在！
     */
    String ERR_MSG_BSS_1775521607 = "err.msg.bss.1775521607";

    /**
     * VMwareFusionCompute
     */
    String ERR_MSG_BSS_1480814017 = "err.msg.bss.1480814017";

    /**
     * 用户认证信息未找到!
     */
    String ERR_MSG_BSS_1255465759 = "err.msg.bss.1255465759";

    /**
     * 员工Id不能大于%d或小于%0
     */
    String ERR_MSG_BSS_1688151202 = "err.msg.bss.1688151202";

    /**
     * 工单已完成，请勿重复操作
     */
    String ERR_MSG_BSS_849752285 = "err.msg.bss.849752285";

    /**
     * 加载反同步策略失败
     */
    String ERR_MSG_BSS_1481638388 = "err.msg.bss.1481638388";

    /**
     * 创建成功
     */
    String ERR_MSG_BSS_650342158 = "err.msg.bss.650342158";

    /**
     * 公司注册电话号格式不合法
     */
    String ERR_MSG_BSS_1000339912 = "err.msg.bss.1000339912";

    /**
     * 找不到resType
     */
    String ERR_MSG_BSS_1766604775 = "err.msg.bss.1766604775";

    /**
     * 租户不能为空
     */
    String ERR_MSG_BSS_2074391480 = "err.msg.bss.2074391480";

    /**
     * 没有对接LDAP,HPC服务开通失败
     */
    String ERR_MSG_BSS_730173332 = "err.msg.bss.730173332";

    /**
     * 硬盘关联的快照超时未销毁完成, 请稍后重试
     */
    String ERR_MSG_BSS_1363588193 = "err.msg.bss.1363588193";

    /**
     * 规则已是最高优先级
     */
    String ERR_MSG_BSS_1608683981 = "err.msg.bss.1608683981";

    /**
     * ，没有配置服务到期发送提醒发送方式参数
     */
    String ERR_MSG_BSS_930043838 = "err.msg.bss.930043838";

    /**
     * 结束时间不能小于生效时间
     */
    String ERR_MSG_BSS_770017632 = "err.msg.bss.770017632";

    /**
     * 该环境已经在 CMP 平台被接入，请确认。
     */
    String ERR_MSG_BSS_145194405 = "err.msg.bss.145194405";

    /**
     * 不存在
     */
    String ERR_MSG_BSS_19948957 = "err.msg.bss.19948957";

    /**
     * 您无法将自身移除用户组
     */
    String ERR_MSG_BSS_1719730526 = "err.msg.bss.1719730526";

    /**
     * 表单模板不能为空
     */
    String ERR_MSG_BSS_86652741 = "err.msg.bss.86652741";

    /**
     * 账户密码校验失败
     */
    String ERR_MSG_BSS_1491128939 = "err.msg.bss.1491128939";

    /**
     * 按代码值。
     */
    String ERR_MSG_BSS_1723974707 = "err.msg.bss.1723974707";

    /**
     * 该策略已被删除，请刷新后重试
     */
    String ERR_MSG_BSS_559118881 = "err.msg.bss.559118881";

    /**
     * 未找到计费类型
     */
    String ERR_MSG_BSS_693266500 = "err.msg.bss.693266500";

    /**
     * 认证失败，用户姓名不能为空
     */
    String ERR_MSG_BSS_975614668 = "err.msg.bss.975614668";

    /**
     * 用户删除中
     */
    String ERR_MSG_BSS_1596076550 = "err.msg.bss.1596076550";

    /**
     * 分区中的计算资源未找到可匹配的网络，无法分配资源。
     */
    String ERR_MSG_BSS_337699785 = "err.msg.bss.337699785";

    /**
     * 不好！
     */
    String ERR_MSG_BSS_19977201 = "err.msg.bss.19977201";

    /**
     * 改类型云环境不唯一
     */
    String ERR_MSG_BSS_1910117118 = "err.msg.bss.1910117118";

    /**
     * 邮箱不能为空
     */
    String ERR_MSG_BSS_684266669 = "err.msg.bss.684266669";

    /**
     * 审批成功
     */
    String ERR_MSG_BSS_723832711 = "err.msg.bss.723832711";

    /**
     * 上传失败，数据不合法
     */
    String ERR_MSG_BSS_752128954 = "err.msg.bss.752128954";

    /**
     * 该工单不存在或已被删除
     */
    String ERR_MSG_BSS_826828363 = "err.msg.bss.826828363";

    /**
     * 该负载均衡下存在监听，无法删除
     */
    String ERR_MSG_BSS_789598156 = "err.msg.bss.789598156";

    /**
     * 仅内置角色可还原默认权限
     */
    String ERR_MSG_BSS_1730835713 = "err.msg.bss.1730835713";

    /**
     * 当前资源版本不支持变更！
     */
    String ERR_MSG_BSS_1055800560 = "err.msg.bss.1055800560";

    /**
     * ID=[{}]-productType=[{}]-
     */
    String ERR_MSG_BSS_369577842 = "err.msg.bss.369577842";

    /**
     * doReportByMon minDate为空
     */
    String ERR_MSG_BSS_2028847658 = "err.msg.bss.2028847658";

    /**
     * 当前挂载弹性文件已冻结!
     */
    String ERR_MSG_BSS_1045743209 = "err.msg.bss.1045743209";

    /**
     * 端口已存在，请重新填写
     */
    String ERR_MSG_BSS_479818070 = "err.msg.bss.479818070";

    /**
     * 实例快照底正在被删除中,请稍后查看。
     */
    String ERR_MSG_BSS_979913343 = "err.msg.bss.979913343";

    /**
     * 获取堡垒机连接失败，请稍后重试!
     */
    String ERR_MSG_BSS_1178635832 = "err.msg.bss.1178635832";

    /**
     * 存储集群不存在！
     */
    String ERR_MSG_BSS_565185550 = "err.msg.bss.565185550";

    /**
     * 暂停任务出错.
     */
    String ERR_MSG_BSS_956647183 = "err.msg.bss.956647183";

    /**
     * 找不到密钥文件。
     */
    String ERR_MSG_BSS_1150966413 = "err.msg.bss.1150966413";

    /**
     * 该客户不存在!
     */
    String ERR_MSG_BSS_1047765662 = "err.msg.bss.1047765662";

    /**
     * 修改失败，默认导航不正确
     */
    String ERR_MSG_BSS_1060193873 = "err.msg.bss.1060193873";

    /**
     * 过期，请及时延迟授权，否则将影响系统的使用！
     */
    String ERR_MSG_BSS_146731758 = "err.msg.bss.146731758";

    /**
     * }参数错误
     */
    String ERR_MSG_BSS_780393057 = "err.msg.bss.780393057";

    /**
     * 客户折扣id参数非法
     */
    String ERR_MSG_BSS_399127456 = "err.msg.bss.399127456";

    /**
     * 请输入正确的协议类型！
     */
    String ERR_MSG_BSS_1294647938 = "err.msg.bss.1294647938";

    /**
     * VMWare实例转化为模板失败
     */
    String ERR_MSG_BSS_1257583935 = "err.msg.bss.1257583935";

    /**
     * 有效占用
     */
    String ERR_MSG_BSS_811405671 = "err.msg.bss.811405671";

    /**
     * 获取账户失败
     */
    String ERR_MSG_BSS_1574153436 = "err.msg.bss.1574153436";

    /**
     * 审核用户不存在，请再次确认！
     */
    String ERR_MSG_BSS_1671398101 = "err.msg.bss.1671398101";

    /**
     * 该策略不存在或已被删除
     */
    String ERR_MSG_BSS_1299650038 = "err.msg.bss.1299650038";

    /**
     * 未映射HCSO租户
     */
    String ERR_MSG_BSS_1617856739 = "err.msg.bss.1617856739";

    /**
     * 订单数据异常
     */
    String ERR_MSG_BSS_200904295 = "err.msg.bss.200904295";

    /**
     * 不能为空
     */
    String ERR_MSG_BSS_627639312 = "err.msg.bss.627639312";

    /**
     * 退出失败！
     */
    String ERR_MSG_BSS_333261619 = "err.msg.bss.333261619";

    /**
     * 修改用户密码，密码不能为空
     */
    String ERR_MSG_BSS_2090502946 = "err.msg.bss.2090502946";

    /**
     * HPC共享资源池存储集群未配置或不存在！
     */
    String ERR_MSG_BSS_232981732 = "err.msg.bss.232981732";

    /**
     * 该状态不能修改角色
     */
    String ERR_MSG_BSS_462650773 = "err.msg.bss.462650773";

    /**
     * 当前账号未关联运营实体，无法登录系统，请联系平台管理员。
     */
    String ERR_MSG_BSS_1848280179 = "err.msg.bss.1848280179";

    /**
     * 监听器已存在
     */
    String ERR_MSG_BSS_1957444907 = "err.msg.bss.1957444907";

    /**
     * 文件路径不合法
     */
    String ERR_MSG_BSS_1967609878 = "err.msg.bss.1967609878";

    /**
     * 该用户不支持此解锁用戶方式, 请点击“忘记密码”激活或联系管理员
     */
    String ERR_MSG_BSS_135037097 = "err.msg.bss.135037097";

    /**
     * 不支持当前服务类型
     */
    String ERR_MSG_BSS_368123331 = "err.msg.bss.368123331";

    /**
     * 套餐包上架成功
     */
    String ERR_MSG_BSS_326330361 = "err.msg.bss.326330361";

    /**
     * 账号已被冻结，无法缩容。
     */
    String ERR_MSG_BSS_568557965 = "err.msg.bss.568557965";

    /**
     * 无法获取对应的handler
     */
    String ERR_MSG_BSS_198530379 = "err.msg.bss.198530379";

    /**
     * 发送失败
     */
    String ERR_MSG_BSS_675396708 = "err.msg.bss.675396708";

    /**
     * 数据不存在!
     */
    String ERR_MSG_BSS_1231631582 = "err.msg.bss.1231631582";

    /**
     * ROOT_URL
     */
    String ERR_MSG_BSS_617066610 = "err.msg.bss.617066610";

    /**
     * 添加消费记录失败，消费记录为空
     */
    String ERR_MSG_BSS_1521289133 = "err.msg.bss.1521289133";

    /**
     * 组织架构名字重复。
     */
    String ERR_MSG_BSS_1966420551 = "err.msg.bss.1966420551";

    /**
     * 客户信息数据正在下载，请稍后查询下载任务！
     */
    String ERR_MSG_BSS_1977429080 = "err.msg.bss.1977429080";

    /**
     * 对不起，demo账号无操作权限
     */
    String ERR_MSG_BSS_1001172287 = "err.msg.bss.1001172287";

    /**
     * 重新扩容配置信息节点数目与本地数据库不一致
     */
    String ERR_MSG_BSS_1922207655 = "err.msg.bss.1922207655";

    /**
     * 数据盘存储的分配率已超过规定阈值，无法分配资源，请重新选择。
     */
    String ERR_MSG_BSS_1364651480 = "err.msg.bss.1364651480";

    /**
     * 验证成功！
     */
    String ERR_MSG_BSS_1025842307 = "err.msg.bss.1025842307";

    /**
     *  是必传参数.
     */
    String ERR_MSG_BSS_1712983414 = "err.msg.bss.1712983414";

    /**
     * 无订单详情
     */
    String ERR_MSG_BSS_597830990 = "err.msg.bss.597830990";

    /**
     * 该弹性文件已挂载资源，不可重复挂载
     */
    String ERR_MSG_BSS_1318816914 = "err.msg.bss.1318816914";

    /**
     * 只支持专属资源池扩容订单的继续扩容
     */
    String ERR_MSG_BSS_166321290 = "err.msg.bss.166321290";

    /**
     * 参数不正确！
     */
    String ERR_MSG_BSS_1479184201 = "err.msg.bss.1479184201";

    /**
     * 组织不存在
     */
    String ERR_MSG_BSS_892148282 = "err.msg.bss.892148282";

    /**
     * 订单编号=[{}],询价无明细价格
     */
    String ERR_MSG_BSS_986680325 = "err.msg.bss.986680325";

    /**
     * 创建套餐包成功
     */
    String ERR_MSG_BSS_1758021060 = "err.msg.bss.1758021060";

    /**
     * 连接失败！
     */
    String ERR_MSG_BSS_436606598 = "err.msg.bss.436606598";

    /**
     * cloudEnvType不能为null
     */
    String ERR_MSG_BSS_162797025 = "err.msg.bss.162797025";

    /**
     * 等待订单初始化确定
     */
    String ERR_MSG_BSS_249141125 = "err.msg.bss.249141125";

    /**
     * 不支持的云服务类型
     */
    String ERR_MSG_BSS_1216535536 = "err.msg.bss.1216535536";

    /**
     * 未找到绑定信息
     */
    String ERR_MSG_BSS_582992755 = "err.msg.bss.582992755";

    /**
     * 处理中，请稍后再试。
     */
    String ERR_MSG_BSS_972079363 = "err.msg.bss.972079363";

    /**
     * 当前集群状态不能进行重新扩容【
     */
    String ERR_MSG_BSS_1344771161 = "err.msg.bss.1344771161";

    /**
     * 用户资源已解冻。
     */
    String ERR_MSG_BSS_151353805 = "err.msg.bss.151353805";

    /**
     * 本地集群数据异常
     */
    String ERR_MSG_BSS_472508694 = "err.msg.bss.472508694";

    /**
     * 没有登录用户信息
     */
    String ERR_MSG_BSS_1968892607 = "err.msg.bss.1968892607";

    /**
     * 可视化节点数超过限制，无法扩容，请联系管理员
     */
    String ERR_MSG_BSS_173321838 = "err.msg.bss.173321838";

    /**
     * 相关云环境资源已下架。如有需要，请联系我们！
     */
    String ERR_MSG_BSS_275964975 = "err.msg.bss.275964975";

    /**
     * 未配置短信签名，请在通知设置配置短信签名。
     */
    String ERR_MSG_BSS_1953546580 = "err.msg.bss.1953546580";

    /**
     * 在BPMN模型中找不到流程。
     */
    String ERR_MSG_BSS_508136419 = "err.msg.bss.508136419";

    /**
     * 未获取到当前登录用户信息
     */
    String ERR_MSG_BSS_2061170872 = "err.msg.bss.2061170872";

    /**
     * 许可证信息有误！
     */
    String ERR_MSG_BSS_56665379 = "err.msg.bss.56665379";

    /**
     * ID={},OBS
     */
    String ERR_MSG_BSS_1393193112 = "err.msg.bss.1393193112";

    /**
     * 规格族代码存在相同或者为空
     */
    String ERR_MSG_BSS_223753704 = "err.msg.bss.223753704";

    /**
     * 共享资源池节点数量不足，节点数量必须满足[{
     */
    String ERR_MSG_BSS_1389989036 = "err.msg.bss.1389989036";

    /**
     * 当前资源不支持进行续订操作
     */
    String ERR_MSG_BSS_********** = "err.msg.bss.**********";

    /**
     * 收件人姓名不能为空
     */
    String ERR_MSG_BSS_********** = "err.msg.bss.**********";

    /**
     * 只有运行中或已停止的主机才能克隆为模板
     */
    String ERR_MSG_BSS_********** = "err.msg.bss.**********";

    /**
     * 该用户没有内置角色权限，不能分配系统管理员权限
     */
    String ERR_MSG_BSS_********** = "err.msg.bss.**********";

    /**
     * ProviderUrl不存在
     */
    String ERR_MSG_BSS_50641727 = "err.msg.bss.50641727";

    /**
     * 套餐包规格不能为空!
     */
    String ERR_MSG_BSS_588254963 = "err.msg.bss.588254963";

    /**
     * 存在流程节点未选择处理角色或处理人，请配置后重新发布！
     */
    String ERR_MSG_BSS_********** = "err.msg.bss.**********";

    /**
     * 掩码值无效。必须介于1和32之间。
     */
    String ERR_MSG_BSS_********** = "err.msg.bss.**********";

    /**
     * 该用户名已被使用
     */
    String ERR_MSG_BSS_315408795 = "err.msg.bss.315408795";

    /**
     * 建造
     */
    String ERR_MSG_BSS_790630 = "err.msg.bss.790630";

    /**
     * 该组织不存在！
     */
    String ERR_MSG_BSS_996771796 = "err.msg.bss.996771796";

    /**
     * 续订已提交，请稍后查看。
     */
    String ERR_MSG_BSS_758966591 = "err.msg.bss.758966591";

    /**
     * 所选套餐包订单超过三个月!
     */
    String ERR_MSG_BSS_409914578 = "err.msg.bss.409914578";

    /**
     * 硬盘大小不可小于快照大小
     */
    String ERR_MSG_BSS_423276345 = "err.msg.bss.423276345";

    /**
     * 上传文件失败
     */
    String ERR_MSG_BSS_1250999303 = "err.msg.bss.1250999303";

    /**
     * 流程定义未找到！
     */
    String ERR_MSG_BSS_230928958 = "err.msg.bss.230928958";

    /**
     * 不存在的云环境
     */
    String ERR_MSG_BSS_1204853986 = "err.msg.bss.1204853986";

    /**
     * 密码请使用8-12英文、数字以及字符
     */
    String ERR_MSG_BSS_222974645 = "err.msg.bss.222974645";

    /**
     * 云环境验证不通过！
     */
    String ERR_MSG_BSS_987970047 = "err.msg.bss.987970047";

    /**
     * 当前桶策略不存在
     */
    String ERR_MSG_BSS_668204434 = "err.msg.bss.668204434";

    /**
     * ，服务到期通知发送频率参数必须为正整数
     */
    String ERR_MSG_BSS_81292215 = "err.msg.bss.81292215";

    /**
     * HPC共享文件系统可分配容量不足
     */
    String ERR_MSG_BSS_2096368886 = "err.msg.bss.2096368886";

    /**
     * 您无法修改除租户外其他角色的状态
     */
    String ERR_MSG_BSS_386105363 = "err.msg.bss.386105363";

    /**
     * 无效的申请时长
     */
    String ERR_MSG_BSS_1331516279 = "err.msg.bss.1331516279";

    /**
     * 角色id无效
     */
    String ERR_MSG_BSS_1555738787 = "err.msg.bss.1555738787";

    /**
     * 该企业不存在用户
     */
    String ERR_MSG_BSS_773060942 = "err.msg.bss.773060942";

    /**
     * 子网名称重复，请检查后再试。
     */
    String ERR_MSG_BSS_1601860032 = "err.msg.bss.1601860032";

    /**
     * HPC与SFS的资源限制应保持一致
     */
    String ERR_MSG_BSS_2056690915 = "err.msg.bss.2056690915";

    /**
     * 不能传递小数
     */
    String ERR_MSG_BSS_1878878275 = "err.msg.bss.1878878275";

    /**
     * 请选择正确的业务标识
     */
    String ERR_MSG_BSS_483786104 = "err.msg.bss.483786104";

    /**
     * 用户id无效
     */
    String ERR_MSG_BSS_1401348178 = "err.msg.bss.1401348178";

    /**
     * 请求参数为空
     */
    String ERR_MSG_BSS_197031015 = "err.msg.bss.197031015";

    /**
     * 未实名认证，请前往认证。
     */
    String ERR_MSG_BSS_2102672431 = "err.msg.bss.2102672431";

    /**
     * 该HCSO账户导入ModelArts子用户资源失败，请确认后再试
     */
    String ERR_MSG_BSS_1390099129 = "err.msg.bss.1390099129";

    /**
     * 映射租户不存在或者已删除
     */
    String ERR_MSG_BSS_361630790 = "err.msg.bss.361630790";

    /**
     * 主键为空
     */
    String ERR_MSG_BSS_633977107 = "err.msg.bss.633977107";

    /**
     * 分区中所有主机/存储的分配率皆已超过设定的分配阀值，无法分配资源。
     */
    String ERR_MSG_BSS_1848044687 = "err.msg.bss.1848044687";

    /**
     * 弹性IP不存在或已被使用，请刷新重试
     */
    String ERR_MSG_BSS_1763782972 = "err.msg.bss.1763782972";

    /**
     * 消息数据
     */
    String ERR_MSG_BSS_859894309 = "err.msg.bss.859894309";

    /**
     * 实例不存在
     */
    String ERR_MSG_BSS_812008144 = "err.msg.bss.812008144";

    /**
     * 不能修改或删除已经关联计费的规格
     */
    String ERR_MSG_BSS_35967207 = "err.msg.bss.35967207";

    /**
     * 删除失败，有关联账户不能删除
     */
    String ERR_MSG_BSS_345700987 = "err.msg.bss.345700987";

    /**
     * 该产品不存在或者已下架！
     */
    String ERR_MSG_BSS_1493182351 = "err.msg.bss.1493182351";

    /**
     * 伊姆索
     */
    String ERR_MSG_BSS_20189350 = "err.msg.bss.20189350";

    /**
     * 上传图片格式不合法
     */
    String ERR_MSG_BSS_592303896 = "err.msg.bss.592303896";

    /**
     * 用户信息错误!
     */
    String ERR_MSG_BSS_1450209074 = "err.msg.bss.1450209074";

    /**
     * 没有修改的参数值!
     */
    String ERR_MSG_BSS_10008228 = "err.msg.bss.10008228";

    /**
     * 未找到主资源
     */
    String ERR_MSG_BSS_1655313493 = "err.msg.bss.1655313493";

    /**
     * 到
     */
    String ERR_MSG_BSS_21040 = "err.msg.bss.21040";

    /**
     * ModelArts上架前，请检查云硬盘产品是否正常！
     */
    String ERR_MSG_BSS_1057862038 = "err.msg.bss.1057862038";

    /**
     * ].
     */
    String ERR_MSG_BSS_2929 = "err.msg.bss.2929";

    /**
     *  需要时整数类型.
     */
    String ERR_MSG_BSS_90126995 = "err.msg.bss.90126995";

    /**
     * 补扣区间内，不支持配置变更
     */
    String ERR_MSG_BSS_909416061 = "err.msg.bss.909416061";

    /**
     * 流程节点未现在审批角色或审批人
     */
    String ERR_MSG_BSS_1859455333 = "err.msg.bss.1859455333";

    /**
     * 暂不支持该产品类型！
     */
    String ERR_MSG_BSS_229235599 = "err.msg.bss.229235599";

    /**
     * 归还共享资源池节点信息失败
     */
    String ERR_MSG_BSS_1437048147 = "err.msg.bss.1437048147";

    /**
     * 无法在已被审批拒绝的客户下创建客户折扣
     */
    String ERR_MSG_BSS_87253504 = "err.msg.bss.87253504";

    /**
     * KeyPairApi创建失败。
     */
    String ERR_MSG_BSS_538874574 = "err.msg.bss.538874574";

    /**
     * 收支明细数据正在下载，请稍后查询下载任务！
     */
    String ERR_MSG_BSS_1928072006 = "err.msg.bss.1928072006";
    /**
     * 客户折扣正在下载，请稍后查询下载任务！
     */
    String ERR_MSG_BSS_1928072016 = "err.msg.bss.1928072016";
    /**
     * 客户折扣数据下载异常，请稍后重试!
     */
    String ERR_MSG_BSS_1928072026 = "err.msg.bss.1928072026";

    /**
     * 激活用户失败，keycloak账号不存在请联系管理员!
     */
    String ERR_MSG_BSS_1185735999 = "err.msg.bss.1185735999";

    /**
     * 记录删除失败
     */
    String ERR_MSG_BSS_990982019 = "err.msg.bss.990982019";

    /**
     * 模型艺术
     */
    String ERR_MSG_BSS_831990527 = "err.msg.bss.831990527";

    /**
     * 交换机未找到！
     */
    String ERR_MSG_BSS_2140431905 = "err.msg.bss.2140431905";

    /**
     * 规格值不在正确范围!
     */
    String ERR_MSG_BSS_1263298054 = "err.msg.bss.1263298054";

    /**
     * 连接名称不能重复
     */
    String ERR_MSG_BSS_1202326970 = "err.msg.bss.1202326970";

    /**
     * 主机模板未找到！
     */
    String ERR_MSG_BSS_661138946 = "err.msg.bss.661138946";

    /**
     * 文件名存在禁用的HTML标签或首字符为特殊字符，请重新上传文件
     */
    String ERR_MSG_BSS_1875968307 = "err.msg.bss.1875968307";

    /**
     * 添加一次性任务出错.
     */
    String ERR_MSG_BSS_1690963832 = "err.msg.bss.1690963832";

    /**
     * bizContractDetails为空
     */
    String ERR_MSG_BSS_65426563 = "err.msg.bss.65426563";

    /**
     * 该RDS实例不存在外网地址或已被清除
     */
    String ERR_MSG_BSS_136873489 = "err.msg.bss.136873489";

    /**
     * 通行证已配置
     */
    String ERR_MSG_BSS_1478687676 = "err.msg.bss.1478687676";

    /**
     * 配置未发生改变！
     */
    String ERR_MSG_BSS_1708151541 = "err.msg.bss.1708151541";

    /**
     * 未找到对应资源
     */
    String ERR_MSG_BSS_318480099 = "err.msg.bss.318480099";

    /**
     * 公司和用户不能同名
     */
    String ERR_MSG_BSS_910546272 = "err.msg.bss.910546272";

    /**
     * 无此运营实体权限
     */
    String ERR_MSG_BSS_2036580347 = "err.msg.bss.2036580347";

    /**
     * 删除用户失败,用户已经被删除或不存在
     */
    String ERR_MSG_BSS_1387618309 = "err.msg.bss.1387618309";

    /**
     * 通知频率超出范围
     */
    String ERR_MSG_BSS_2093129465 = "err.msg.bss.2093129465";

    /**
     * 该流程由您审批,不能发起申请!
     */
    String ERR_MSG_BSS_1742018318 = "err.msg.bss.1742018318";

    /**
     * 创建专属资源池失败
     */
    String ERR_MSG_BSS_1528782398 = "err.msg.bss.1528782398";

    /**
     * 已经绑定了一个安全组，不允许再次进行绑定
     */
    String ERR_MSG_BSS_756174209 = "err.msg.bss.756174209";

    /**
     * 物理机未设置带外管理信息!
     */
    String ERR_MSG_BSS_838971233 = "err.msg.bss.838971233";

    /**
     * 登录类型参数不规范.只能选择[密码,密钥]
     */
    String ERR_MSG_BSS_1112944485 = "err.msg.bss.1112944485";

    /**
     * 规格卡数不能大于1000
     */
    String ERR_MSG_BSS_1121351715 = "err.msg.bss.1121351715";

    /**
     * 重新冻结成功
     */
    String ERR_MSG_BSS_1937183478 = "err.msg.bss.1937183478";

    /**
     * 存在未处理的产品申请单，请操作后再删除。
     */
    String ERR_MSG_BSS_1680282090 = "err.msg.bss.1680282090";

    /**
     * 文件格式上传有误
     */
    String ERR_MSG_BSS_665023810 = "err.msg.bss.665023810";

    /**
     * 套餐包下架成功
     */
    String ERR_MSG_BSS_326300570 = "err.msg.bss.326300570";

    /**
     * 转换对象异常
     */
    String ERR_MSG_BSS_1639048052 = "err.msg.bss.1639048052";

    /**
     * 当前合同不存在，请刷新后重试
     */
    String ERR_MSG_BSS_49895981 = "err.msg.bss.49895981";

    /**
     * 已上架的套餐包不支持新增规格！
     */
    String ERR_MSG_BSS_1398519262 = "err.msg.bss.1398519262";

    /**
     * 原密码校验失败
     */
    String ERR_MSG_BSS_406021703 = "err.msg.bss.406021703";

    /**
     * 当前状态不能删除！
     */
    String ERR_MSG_BSS_1397183720 = "err.msg.bss.1397183720";

    /**
     * 查询资产[
     */
    String ERR_MSG_BSS_104722149 = "err.msg.bss.104722149";

    /**
     * 主账户不是预开通
     */
    String ERR_MSG_BSS_1254398416 = "err.msg.bss.1254398416";

    /**
     * 弹性ip正在被解绑中，请稍后查看。
     */
    String ERR_MSG_BSS_1288046786 = "err.msg.bss.1288046786";

    /**
     * 请检查用户信息
     */
    String ERR_MSG_BSS_1161901479 = "err.msg.bss.1161901479";

    /**
     * url解码异常
     */
    String ERR_MSG_BSS_909105251 = "err.msg.bss.909105251";

    /**
     * 不可修改非本合同的文件！
     */
    String ERR_MSG_BSS_1630066349 = "err.msg.bss.1630066349";

    /**
     * 权限标识不能为空
     */
    String ERR_MSG_BSS_1834233124 = "err.msg.bss.1834233124";

    /**
     * 当前组织下账号不存在
     */
    String ERR_MSG_BSS_558443810 = "err.msg.bss.558443810";

    /**
     * 修改密码规则
     */
    String ERR_MSG_BSS_586078363 = "err.msg.bss.586078363";

    /**
     * 企业已完成认证，无法修改
     */
    String ERR_MSG_BSS_139016942 = "err.msg.bss.139016942";

    /**
     * 计费策略未找到！
     */
    String ERR_MSG_BSS_1264889708 = "err.msg.bss.1264889708";

    /**
     * 发送 MQ 消息失败
     */
    String ERR_MSG_BSS_151523481 = "err.msg.bss.151523481";

    /**
     * 所选的工单类型已禁用，请重新选择工单类型后提交！
     */
    String ERR_MSG_BSS_1683953675 = "err.msg.bss.1683953675";

    /**
     * 路由器已存在
     */
    String ERR_MSG_BSS_817265412 = "err.msg.bss.817265412";

    /**
     * 认证状态数据有误，请核查数据
     */
    String ERR_MSG_BSS_931653822 = "err.msg.bss.931653822";

    /**
     * ownerId={},OceanStor
     */
    String ERR_MSG_BSS_1339908231 = "err.msg.bss.1339908231";

    /**
     * 需要删除的对象组未找到！
     */
    String ERR_MSG_BSS_1557752030 = "err.msg.bss.1557752030";

    /**
     * 快照创建失败，结果为空
     */
    String ERR_MSG_BSS_978100070 = "err.msg.bss.978100070";

    /**
     * 已关联流程的模板，不允许删除
     */
    String ERR_MSG_BSS_531688351 = "err.msg.bss.531688351";



    /**
     * 查询动态权限[
     */
    String ERR_MSG_BSS_1628463896 = "err.msg.bss.1628463896";

    /**
     * 审核用户失败
     */
    String ERR_MSG_BSS_1327231386 = "err.msg.bss.1327231386";

    /**
     * 退订中,请勿重复提交
     */
    String ERR_MSG_BSS_313131619 = "err.msg.bss.313131619";

    /**
     * 删除过多节点
     */
    String ERR_MSG_BSS_1375795666 = "err.msg.bss.1375795666";

    /**
     * 该规格族所属产品正在被使用，请先下架产品！
     */
    String ERR_MSG_BSS_1645968452 = "err.msg.bss.1645968452";

    /**
     * 绑定弹性IP没有返回数据, 原始数据:
     */
    String ERR_MSG_BSS_331563182 = "err.msg.bss.331563182";

    /**
     * 没有采集到入库话单
     */
    String ERR_MSG_BSS_641001073 = "err.msg.bss.641001073";

    /**
     * 用户操作日志正在生成，请稍后下载！
     */
    String ERR_MSG_BSS_10845283 = "err.msg.bss.10845283";

    /**
     * 计数大于整数：
     */
    String ERR_MSG_BSS_1830689768 = "err.msg.bss.1830689768";

    /**
     * 网络名称重复，请检查后重试
     */
    String ERR_MSG_BSS_2120532008 = "err.msg.bss.2120532008";

    /**
     * 更新指定HPC资源池的节点信息失败
     */
    String ERR_MSG_BSS_923610709 = "err.msg.bss.923610709";

    /**
     * 优惠卷不属于当前运营实体，请勿操作
     */
    String ERR_MSG_BSS_729462609 = "err.msg.bss.729462609";

    /**
     * 不能包含中文字符
     */
    String ERR_MSG_BSS_1188140929 = "err.msg.bss.1188140929";

    /**
     * 碎片
     */
    String ERR_MSG_BSS_985977 = "err.msg.bss.985977";

    /**
     * 产品不存在
     */
    String ERR_MSG_BSS_2081550435 = "err.msg.bss.2081550435";

    /**
     * 操作失败
     */
    String ERR_MSG_BSS_789024387 = "err.msg.bss.789024387";

    /**
     * 调用mq失败：
     */
    String ERR_MSG_BSS_2045839613 = "err.msg.bss.2045839613";

    /**
     * 时间格式不正确
     */
    String ERR_MSG_BSS_1985154311 = "err.msg.bss.1985154311";

    /**
     * 没有查找到裸金属资源，请联系管理员检查资源配置。
     */
    String ERR_MSG_BSS_665286895 = "err.msg.bss.665286895";

    /**
     * HPC专属资源池创建失败！
     */
    String ERR_MSG_BSS_328479904 = "err.msg.bss.328479904";

    /**
     * 运营实体不存在！
     */
    String ERR_MSG_BSS_1680280718 = "err.msg.bss.1680280718";

    /**
     * IAM用户SSO开启后不支持关闭！
     */
    String ERR_MSG_BSS_1039016444 = "err.msg.bss.1039016444";

    /**
     * 分发现金券成功
     */
    String ERR_MSG_BSS_1526112421 = "err.msg.bss.1526112421";

    /**
     * 实例快照底层资源已不存在，请重新选择
     */
    String ERR_MSG_BSS_1799603963 = "err.msg.bss.1799603963";

    /**
     * 账户类型不正确
     */
    String ERR_MSG_BSS_1153218711 = "err.msg.bss.1153218711";

    /**
     * 该工单不存在或已被删除！
     */
    String ERR_MSG_BSS_138059242 = "err.msg.bss.138059242";

    /**
     * 运营实体id获取失败
     */
    String ERR_MSG_BSS_1985222152 = "err.msg.bss.1985222152";

    /**
     * 刷新成功
     */
    String ERR_MSG_BSS_652827304 = "err.msg.bss.652827304";

    /**
     * 下载失败
     */
    String ERR_MSG_BSS_631238758 = "err.msg.bss.631238758";

    /**
     * 您无法修改同级别账号状态
     */
    String ERR_MSG_BSS_1156735588 = "err.msg.bss.1156735588";

    /**
     * 该电话已被使用
     */
    String ERR_MSG_BSS_893241775 = "err.msg.bss.893241775";

    /**
     * 选定用户不是当前用户的子用户
     */
    String ERR_MSG_BSS_1672206086 = "err.msg.bss.1672206086";

    /**
     * 请求异常【逻辑资源池没有部署上线作业】
     */
    String ERR_MSG_BSS_404343587 = "err.msg.bss.404343587";

    /**
     * 工单数据正在下载，请稍后查询下载任务！
     */
    String ERR_MSG_BSS_1561150597 = "err.msg.bss.1561150597";

    /**
     * 资产[
     */
    String ERR_MSG_BSS_35377880 = "err.msg.bss.35377880";

    /**
     * GPU设备不存在或已删除
     */
    String ERR_MSG_BSS_938682680 = "err.msg.bss.938682680";

    /**
     * 实例[{}]选择的实例快照不存在
     */
    String ERR_MSG_BSS_1758471837 = "err.msg.bss.1758471837";

    /**
     * 当前用户不可操作
     */
    String ERR_MSG_BSS_1575901082 = "err.msg.bss.1575901082";

    /**
     * 流程正在执行中，请勿重复提交...
     */
    String ERR_MSG_BSS_1940137057 = "err.msg.bss.1940137057";

    /**
     * 相关产品不存在！
     */
    String ERR_MSG_BSS_254512185 = "err.msg.bss.254512185";

    /**
     * resOccRatioList异常
     */
    String ERR_MSG_BSS_1265491792 = "err.msg.bss.1265491792";

    /**
     * 映射转换bean错误
     */
    String ERR_MSG_BSS_1180526720 = "err.msg.bss.1180526720";

    /**
     * 启用失败，同一环境同一用户同一产品折扣策略只能启用一个
     */
    String ERR_MSG_BSS_1139927544 = "err.msg.bss.1139927544";

    /**
     * 未找到文件
     */
    String ERR_MSG_BSS_607418517 = "err.msg.bss.607418517";

    /**
     * 该模板已禁用，请勿重复禁用！
     */
    String ERR_MSG_BSS_534297092 = "err.msg.bss.534297092";

    /**
     * 调整后容量不能和当前容量相同！
     */
    String ERR_MSG_BSS_580041844 = "err.msg.bss.580041844";

    /**
     * 该记录正在被使用，不可删除
     */
    String ERR_MSG_BSS_1948452787 = "err.msg.bss.1948452787";

    /**
     * 角色标识已存在
     */
    String ERR_MSG_BSS_771771491 = "err.msg.bss.771771491";

    /**
     * 未获取到当前登录用户
     */
    String ERR_MSG_BSS_1924110710 = "err.msg.bss.1924110710";

    /**
     * 价格必须大于0!
     */
    String ERR_MSG_BSS_1091843789 = "err.msg.bss.1091843789";

    /**
     * 修改账户bms策略失败
     */
    String ERR_MSG_BSS_806784241 = "err.msg.bss.806784241";

    /**
     * 规则不存在
     */
    String ERR_MSG_BSS_1142933336 = "err.msg.bss.1142933336";

    /**
     * 获取规格族详情失败
     */
    String ERR_MSG_BSS_1234753131 = "err.msg.bss.1234753131";

    /**
     * 该弹性文件对应的云环境已被删除！
     */
    String ERR_MSG_BSS_951179418 = "err.msg.bss.951179418";

    /**
     * 主账户为锁定状态，子账号不允许登录
     */
    String ERR_MSG_BSS_1669888439 = "err.msg.bss.1669888439";

    /**
     * 修改密码成功,请重新登录
     */
    String ERR_MSG_BSS_1653564579 = "err.msg.bss.1653564579";

    /**
     * 现金券状态异常，不能进行分发操作!
     */
    String ERR_MSG_BSS_1026419822 = "err.msg.bss.1026419822";

    /**
     * 规格族未找到！
     */
    String ERR_MSG_BSS_1600874684 = "err.msg.bss.1600874684";

    /**
     * 子账户存在电话重复
     */
    String ERR_MSG_BSS_2095263577 = "err.msg.bss.2095263577";

    /**
     * 用户名最小长度配置不能小于4
     */
    String ERR_MSG_BSS_780616784 = "err.msg.bss.780616784";

    /**
     * 该IP已存在，请更换
     */
    String ERR_MSG_BSS_359650161 = "err.msg.bss.359650161";

    /**
     * 获取节点信息失败
     */
    String ERR_MSG_BSS_1739319800 = "err.msg.bss.1739319800";

    /**
     * 文件类型错误，请重新上传文件
     */
    String ERR_MSG_BSS_123051128 = "err.msg.bss.123051128";

    /**
     * 存在使用中的产品服务，请退订后再删除。
     */
    String ERR_MSG_BSS_1100740236 = "err.msg.bss.1100740236";

    /**
     * 该账号正在审核中
     */
    String ERR_MSG_BSS_849067419 = "err.msg.bss.849067419";

    /**
     * 共享资源池查询异常
     */
    String ERR_MSG_BSS_947357107 = "err.msg.bss.947357107";

    /**
     * 获取accessUrl失败，请联系管理员
     */
    String ERR_MSG_BSS_1733447679 = "err.msg.bss.1733447679";

    /**
     * 用户可使用额度不足
     */
    String ERR_MSG_BSS_1601270136 = "err.msg.bss.1601270136";

    /**
     * 所选用户不存在！
     */
    String ERR_MSG_BSS_17529500 = "err.msg.bss.17529500";

    /**
     * 等待作业初始化正常
     */
    String ERR_MSG_BSS_1256747421 = "err.msg.bss.1256747421";

    /**
     * 该云账号已经被删除，请刷新页面后重试。
     */
    String ERR_MSG_BSS_1086627474 = "err.msg.bss.1086627474";

    /**
     * 只能复制文件，无法复制文件夹，请重新选择
     */
    String ERR_MSG_BSS_1997786393 = "err.msg.bss.1997786393";

    /**
     * 无法正确解析云环境配置信息。
     */
    String ERR_MSG_BSS_1794848763 = "err.msg.bss.1794848763";

    /**
     * 创建现金券成功
     */
    String ERR_MSG_BSS_120701479 = "err.msg.bss.120701479";

    /**
     * 分区中所有主机的分配率皆已超过设定的分配阀值，无法分配资源。
     */
    String ERR_MSG_BSS_1387504366 = "err.msg.bss.1387504366";

    /**
     * 请输入正确的节点缩容最大次数！
     */
    String ERR_MSG_BSS_586279052 = "err.msg.bss.586279052";

    /**
     * 氯化硫胺
     */
    String ERR_MSG_BSS_846490870 = "err.msg.bss.846490870";

    /**
     * 存储大小范围为50-500
     */
    String ERR_MSG_BSS_281304332 = "err.msg.bss.281304332";

    /**
     * HPC登录节点配置有误
     */
    String ERR_MSG_BSS_440193421 = "err.msg.bss.440193421";

    /**
     * 修改发票设置信息成功
     */
    String ERR_MSG_BSS_2005429841 = "err.msg.bss.2005429841";

    /**
     * 用户信息错误
     */
    String ERR_MSG_BSS_2031429043 = "err.msg.bss.2031429043";

    /**
     * 获取云环境下项目信息失败
     */
    String ERR_MSG_BSS_1210403908 = "err.msg.bss.1210403908";

    /**
     * 当前操作角色不存在
     */
    String ERR_MSG_BSS_1618268500 = "err.msg.bss.1618268500";

    /**
     * 跟踪或跟踪程序为空，请注入跟踪或跟踪程序
     */
    String ERR_MSG_BSS_310519656 = "err.msg.bss.310519656";

    /**
     * 张现金券
     */
    String ERR_MSG_BSS_755109655 = "err.msg.bss.755109655";

    /**
     * 错误消息
     */
    String ERR_MSG_BSS_1172412509 = "err.msg.bss.1172412509";

    /**
     * 当前运营实体不具备操作权限！
     */
    String ERR_MSG_BSS_728123500 = "err.msg.bss.728123500";

    /**
     * 资源转换失败
     */
    String ERR_MSG_BSS_1746033110 = "err.msg.bss.1746033110";

    /**
     * 缺少必需的JDBC URL。无法创建数据源！
     */
    String ERR_MSG_BSS_2089435863 = "err.msg.bss.2089435863";

    /**
     * 密码重置成功
     */
    String ERR_MSG_BSS_2049191179 = "err.msg.bss.2049191179";

    /**
     * 通行证配置成功
     */
    String ERR_MSG_BSS_1792376095 = "err.msg.bss.1792376095";

    /**
     * 该角色不支持还原角色的权限！
     */
    String ERR_MSG_BSS_2139379057 = "err.msg.bss.2139379057";

    /**
     * 该通知记录不存在!
     */
    String ERR_MSG_BSS_424495865 = "err.msg.bss.424495865";

    /**
     * 正在进行映射租户导入，请勿重复操作
     */
    String ERR_MSG_BSS_1331653784 = "err.msg.bss.1331653784";

    /**
     * 所在组织权限不足,不能修改操作
     */
    String ERR_MSG_BSS_1887881448 = "err.msg.bss.1887881448";

    /**
     * 前端协议端口重复
     */
    String ERR_MSG_BSS_1931971019 = "err.msg.bss.1931971019";

    /**
     * 您无法修改当前用户的状态
     */
    String ERR_MSG_BSS_9631134 = "err.msg.bss.9631134";

    /**
     * 当前运营实体无操作权限!
     */
    String ERR_MSG_BSS_1834029111 = "err.msg.bss.1834029111";

    /**
     * 当前用户未关联用户组
     */
    String ERR_MSG_BSS_1618358003 = "err.msg.bss.1618358003";

    /**
     * 公司注册电话不能为空
     */
    String ERR_MSG_BSS_511838200 = "err.msg.bss.511838200";

    /**
     * 角色名称已存在
     */
    String ERR_MSG_BSS_207826687 = "err.msg.bss.207826687";

    /**
     * 关联用户不存在或已被删除
     */
    String ERR_MSG_BSS_586177114 = "err.msg.bss.586177114";

    /**
     * ，没有配置任何参数
     */
    String ERR_MSG_BSS_170618365 = "err.msg.bss.170618365";

    /**
     * 对应的计费策略未找到！
     */
    String ERR_MSG_BSS_1714183531 = "err.msg.bss.1714183531";

    /**
     * 无权处理其他人的工单
     */
    String ERR_MSG_BSS_121433887 = "err.msg.bss.121433887";

    /**
     * 云主机服务未配置计费
     */
    String ERR_MSG_BSS_690160127 = "err.msg.bss.690160127";

    /**
     * 不支持非固定云环境方式！
     */
    String ERR_MSG_BSS_1736619067 = "err.msg.bss.1736619067";

    /**
     * 缺席
     */
    String ERR_MSG_BSS_1033779 = "err.msg.bss.1033779";

    /**
     * 对象实例已经被删除.
     */
    String ERR_MSG_BSS_930900495 = "err.msg.bss.930900495";

    /**
     * 云环境账户
     */
    String ERR_MSG_BSS_1994877546 = "err.msg.bss.1994877546";

    /**
     * 查询规则模板名称不能为空
     */
    String ERR_MSG_BSS_2146436797 = "err.msg.bss.2146436797";

    /**
     * 当前规格项下存在重复的规格定价，请检查后重试
     */
    String ERR_MSG_BSS_1766939935 = "err.msg.bss.1766939935";

    /**
     * 实例名
     */
    String ERR_MSG_BSS_23192064 = "err.msg.bss.23192064";

    /**
     * 该快照的源硬盘不存在，不能回滚。
     */
    String ERR_MSG_BSS_332268334 = "err.msg.bss.332268334";

    /**
     * ID[{}]
     */
    String ERR_MSG_BSS_2139406693 = "err.msg.bss.2139406693";

    /**
     * 月份为空
     */
    String ERR_MSG_BSS_805851957 = "err.msg.bss.805851957";

    /**
     * 用户组id非法
     */
    String ERR_MSG_BSS_276090791 = "err.msg.bss.276090791";

    /**
     * 该规则已删除，请刷新重试！
     */
    String ERR_MSG_BSS_849421401 = "err.msg.bss.849421401";

    /**
     * 暂不支持该类型
     */
    String ERR_MSG_BSS_1984993480 = "err.msg.bss.1984993480";

    /**
     * 生效时间不能大于失效时间
     */
    String ERR_MSG_BSS_30899181 = "err.msg.bss.30899181";

    /**
     * 该操作无权限，请刷新页面后查看该资源是否存在！
     */
    String ERR_MSG_BSS_1982535027 = "err.msg.bss.1982535027";

    /**
     * 没有可用的裸金属节点，请联系管理员添加！
     */
    String ERR_MSG_BSS_924615660 = "err.msg.bss.924615660";

    /**
     * ]不可用，请检查后重试！
     */
    String ERR_MSG_BSS_838132676 = "err.msg.bss.838132676";

    /**
     * 查询已使用容量失败
     */
    String ERR_MSG_BSS_227852296 = "err.msg.bss.227852296";

    /**
     * 无权操作
     */
    String ERR_MSG_BSS_803173586 = "err.msg.bss.803173586";

    /**
     * 非法参数!
     */
    String ERR_MSG_BSS_2016849060 = "err.msg.bss.2016849060";

    /**
     * 】规格未配置计费
     */
    String ERR_MSG_BSS_1535266246 = "err.msg.bss.1535266246";

    /**
     * 结果==空
     */
    String ERR_MSG_BSS_709453585 = "err.msg.bss.709453585";

    /**
     * 不允许的操作动作!
     */
    String ERR_MSG_BSS_147521346 = "err.msg.bss.147521346";

    /**
     * 租户控制台不能重置管理员账号
     */
    String ERR_MSG_BSS_227632394 = "err.msg.bss.227632394";

    /**
     * 计费方式暂不支持！
     */
    String ERR_MSG_BSS_1653647754 = "err.msg.bss.1653647754";

    /**
     * 主机需要有接入的账号和密码
     */
    String ERR_MSG_BSS_279635611 = "err.msg.bss.279635611";

    /**
     * 存在关联的服务实例，无法删除
     */
    String ERR_MSG_BSS_310520687 = "err.msg.bss.310520687";

    /**
     * 白名单不存在错误
     */
    String ERR_MSG_BSS_1758170958 = "err.msg.bss.1758170958";

    /**
     * 不能重置其他用户密码！
     */
    String ERR_MSG_BSS_1678509082 = "err.msg.bss.1678509082";

    /**
     * 文件名存在禁用的HTML标签，请重新上传文件
     */
    String ERR_MSG_BSS_1834023868 = "err.msg.bss.1834023868";

    /**
     * 修改失败，角色类型格式不正确
     */
    String ERR_MSG_BSS_1572553784 = "err.msg.bss.1572553784";

    /**
     * 公司和用户名称关联性太强，请修改
     */
    String ERR_MSG_BSS_521795834 = "err.msg.bss.521795834";

    /**
     * 当前资源类型不支持变更！
     */
    String ERR_MSG_BSS_2087343772 = "err.msg.bss.2087343772";

    /**
     * 审核企业成功
     */
    String ERR_MSG_BSS_1031369919 = "err.msg.bss.1031369919";

    /**
     * 用户信息未找到！
     */
    String ERR_MSG_BSS_1928367842 = "err.msg.bss.1928367842";

    /**
     * 底层共享资源池处于[
     */
    String ERR_MSG_BSS_449111516 = "err.msg.bss.449111516";

    /**
     * 已存在，请重新输入！
     */
    String ERR_MSG_BSS_2049732961 = "err.msg.bss.2049732961";

    /**
     * cmp mq异常！
     */
    String ERR_MSG_BSS_196053299 = "err.msg.bss.196053299";

    /**
     * 当前产品未配置对应的资源池
     */
    String ERR_MSG_BSS_897136674 = "err.msg.bss.897136674";

    /**
     * 订单编号=[{}],无订单明细价格
     */
    String ERR_MSG_BSS_628854957 = "err.msg.bss.628854957";

    /**
     * 该文件系统不存在,请刷新后重试
     */
    String ERR_MSG_BSS_1841992755 = "err.msg.bss.1841992755";

    /**
     * 存在关联的子网，无法删除
     */
    String ERR_MSG_BSS_728522319 = "err.msg.bss.728522319";

    /**
     * ]状态，请稍后再试
     */
    String ERR_MSG_BSS_1385592996 = "err.msg.bss.1385592996";

    /**
     * 暂不支持此操作
     */
    String ERR_MSG_BSS_1993179978 = "err.msg.bss.1993179978";

    /**
     * FloatingIPApi不存在
     */
    String ERR_MSG_BSS_1337680016 = "err.msg.bss.1337680016";

    /**
     * 新增套餐包规格失败
     */
    String ERR_MSG_BSS_2062694378 = "err.msg.bss.2062694378";

    /**
     * 添加vlan池失败！
     */
    String ERR_MSG_BSS_1889815029 = "err.msg.bss.1889815029";

    /**
     * 激活用户失败
     */
    String ERR_MSG_BSS_342989730 = "err.msg.bss.342989730";

    /**
     * 操作成功
     */
    String ERR_MSG_BSS_789079806 = "err.msg.bss.789079806";

    /**
     * 修改失败，角色标识格式不正确
     */
    String ERR_MSG_BSS_838924551 = "err.msg.bss.838924551";

    /**
     * 无权查看该运营实体下的规格数据
     */
    String ERR_MSG_BSS_1777682677 = "err.msg.bss.1777682677";

    /**
     *  传输类型：
     */
    String ERR_MSG_BSS_725509833 = "err.msg.bss.725509833";

    /**
     * 认证失败，身份证信息不能为空
     */
    String ERR_MSG_BSS_1365012338 = "err.msg.bss.1365012338";

    /**
     * 您指定的文件有误！无法读取
     */
    String ERR_MSG_BSS_1562389587 = "err.msg.bss.1562389587";

    /**
     * 当前挂载集群已冻结!
     */
    String ERR_MSG_BSS_939382294 = "err.msg.bss.939382294";

    /**
     * 文件系统[ID:
     */
    String ERR_MSG_BSS_620938601 = "err.msg.bss.620938601";

    /**
     * ) 应该是：
     */
    String ERR_MSG_BSS_1959940083 = "err.msg.bss.1959940083";

    /**
     * 流程定义未找到
     */
    String ERR_MSG_BSS_408194781 = "err.msg.bss.408194781";

    /**
     * 找不到指定云账号！
     */
    String ERR_MSG_BSS_456231597 = "err.msg.bss.456231597";

    /**
     * 裸金属服务未配置计费
     */
    String ERR_MSG_BSS_835480726 = "err.msg.bss.835480726";

    /**
     * 无操作用户
     */
    String ERR_MSG_BSS_895140770 = "err.msg.bss.895140770";

    /**
     * 认证失败，请求信息不能为空
     */
    String ERR_MSG_BSS_417956708 = "err.msg.bss.417956708";

    /**
     * ，触发器组名=
     */
    String ERR_MSG_BSS_427459741 = "err.msg.bss.427459741";

    /**
     * 操作系统参数不规范.只能选择[
     */
    String ERR_MSG_BSS_1804636283 = "err.msg.bss.1804636283";

    /**
     * 编辑GPU资源组不存在或已删除
     */
    String ERR_MSG_BSS_1825360970 = "err.msg.bss.1825360970";

    /**
     * 未找到防火墙对象！
     */
    String ERR_MSG_BSS_2144849835 = "err.msg.bss.2144849835";

    /**
     * 】未配置计费
     */
    String ERR_MSG_BSS_144599566 = "err.msg.bss.144599566";

    /**
     * 表单模板异常
     */
    String ERR_MSG_BSS_1935158559 = "err.msg.bss.1935158559";

    /**
     * 创建信息有误，请确认
     */
    String ERR_MSG_BSS_1104815892 = "err.msg.bss.1104815892";

    /**
     * 存储关联宿主机不存在
     */
    String ERR_MSG_BSS_872911184 = "err.msg.bss.872911184";

    /**
     * 操作成功，状态正在同步中...
     */
    String ERR_MSG_BSS_1312023240 = "err.msg.bss.1312023240";

    /**
     * 必须是关机状态的机器才能变更配置
     */
    String ERR_MSG_BSS_127544204 = "err.msg.bss.127544204";

    /**
     * 续订资源不存在
     */
    String ERR_MSG_BSS_1971242212 = "err.msg.bss.1971242212";

    /**
     * 该端口已被使用，请重新添加！
     */
    String ERR_MSG_BSS_1258147309 = "err.msg.bss.1258147309";

    /**
     * 该共享文件已绑定用户，请重新设置共享文件目录
     */
    String ERR_MSG_BSS_291875722 = "err.msg.bss.291875722";

    /**
     * 获取云环境失败！
     */
    String ERR_MSG_BSS_220852217 = "err.msg.bss.220852217";

    /**
     * 弹性ip底层资源已不存在，请重新选择。
     */
    String ERR_MSG_BSS_182994821 = "err.msg.bss.182994821";

    /**
     * 无法连接到
     */
    String ERR_MSG_BSS_818163596 = "err.msg.bss.818163596";

    /**
     * 国密服务已成功开启！不可修改国密配置
     */
    String ERR_MSG_BSS_1785173285 = "err.msg.bss.1785173285";

    /**
     * bean转换映射错误：
     */
    String ERR_MSG_BSS_2141589018 = "err.msg.bss.2141589018";

    /**
     * 请输入密钥或密钥.
     */
    String ERR_MSG_BSS_1405851233 = "err.msg.bss.1405851233";

    /**
     * 所填写的IP已经被私网IP占用
     */
    String ERR_MSG_BSS_89896720 = "err.msg.bss.89896720";

    /**
     * 不好
     */
    String ERR_MSG_BSS_642320 = "err.msg.bss.642320";

    /**
     * 请勿对已审批通过的用户进行重复操作
     */
    String ERR_MSG_BSS_1120134131 = "err.msg.bss.1120134131";

    /**
     * 收件人地址不能为空
     */
    String ERR_MSG_BSS_68099290 = "err.msg.bss.68099290";

    /**
     * 个云环境，请先删除后继续！
     */
    String ERR_MSG_BSS_920930451 = "err.msg.bss.920930451";

    /**
     * 该账户已申请ModelArts，请退订完成后再申请！
     */
    String ERR_MSG_BSS_774444276 = "err.msg.bss.774444276";

    /**
     * 创建成功,密码已发送至用户邮箱
     */
    String ERR_MSG_BSS_1102568356 = "err.msg.bss.1102568356";

    /**
     * 创建BMS白名单失败
     */
    String ERR_MSG_BSS_565277136 = "err.msg.bss.565277136";

    /**
     * 重置内置账号成功,请重新登录
     */
    String ERR_MSG_BSS_547114930 = "err.msg.bss.547114930";

    /**
     * 没有找到关联的存储资源！
     */
    String ERR_MSG_BSS_404356388 = "err.msg.bss.404356388";

    /**
     * 不支持当前查询类型
     */
    String ERR_MSG_BSS_347603098 = "err.msg.bss.347603098";

    /**
     * 资源池名称不能相同!
     */
    String ERR_MSG_BSS_272875578 = "err.msg.bss.272875578";

    /**
     * 删除功能异常
     */
    String ERR_MSG_BSS_1832677256 = "err.msg.bss.1832677256";

    /**
     * 请输入登录类型.
     */
    String ERR_MSG_BSS_278856827 = "err.msg.bss.278856827";

    /**
     * 计费策略已启用请勿重复启用
     */
    String ERR_MSG_BSS_1352365819 = "err.msg.bss.1352365819";

    /**
     * SFS存储总容量不可小于当前已使用总量
     */
    String ERR_MSG_BSS_2104102643 = "err.msg.bss.2104102643";

    /**
     * 申请的物理机已经分配.不能再次分配
     */
    String ERR_MSG_BSS_999722424 = "err.msg.bss.999722424";

    /**
     * 云环境或云环境账户
     */
    String ERR_MSG_BSS_510247557 = "err.msg.bss.510247557";

    /**
     * 余额不足，请先充值
     */
    String ERR_MSG_BSS_13080022 = "err.msg.bss.13080022";

    /**
     * 邮箱长度最多80个字
     */
    String ERR_MSG_BSS_671607287 = "err.msg.bss.671607287";

    /**
     * 缺少sysLog服务器信息
     */
    String ERR_MSG_BSS_1580301292 = "err.msg.bss.1580301292";

    /**
     * 以字母开头，由字母、数字、中划线、下划线以及点组成
     */
    String ERR_MSG_BSS_1013213102 = "err.msg.bss.1013213102";

    /**
     * 堡垒机验证不通过
     */
    String ERR_MSG_BSS_496772796 = "err.msg.bss.496772796";

    /**
     * 删除弹性文件失败
     */
    String ERR_MSG_BSS_1524362507 = "err.msg.bss.1524362507";

    /**
     * 防火墻规则正在被使用
     */
    String ERR_MSG_BSS_777364520 = "err.msg.bss.777364520";

    /**
     * 修改控制台密码失败。
     */
    String ERR_MSG_BSS_167144413 = "err.msg.bss.167144413";

    /**
     * 账户资源已解冻。
     */
    String ERR_MSG_BSS_1131222133 = "err.msg.bss.1131222133";

    /**
     * 】的变更，请勿重复操作
     */
    String ERR_MSG_BSS_353348660 = "err.msg.bss.353348660";

    /**
     * 请先关闭该租户弹性裸金属服务！
     */
    String ERR_MSG_BSS_1758494136 = "err.msg.bss.1758494136";

    /**
     * 更改的规格不存在，请同步后重试
     */
    String ERR_MSG_BSS_1320518092 = "err.msg.bss.1320518092";

    /**
     * 修改标签失败!
     */
    String ERR_MSG_BSS_394199893 = "err.msg.bss.394199893";

    /**
     * 无法配置默认权限外的其它权限!
     */
    String ERR_MSG_BSS_1905165674 = "err.msg.bss.1905165674";

    /**
     * 当前云环境账户不存在！
     */
    String ERR_MSG_BSS_1028411520 = "err.msg.bss.1028411520";

    /**
     * 】不存在
     */
    String ERR_MSG_BSS_386527212 = "err.msg.bss.386527212";

    /**
     * 资源池規格不存在!
     */
    String ERR_MSG_BSS_666890939 = "err.msg.bss.666890939";

    /**
     * 该云环境不存在
     */
    String ERR_MSG_BSS_315879779 = "err.msg.bss.315879779";

    /**
     * 用户名最大长度配置不能大于64
     */
    String ERR_MSG_BSS_395865822 = "err.msg.bss.395865822";

    /**
     * 认证失败，当前认证状态为：
     */
    String ERR_MSG_BSS_444491683 = "err.msg.bss.444491683";

    /**
     * 开票信息不完整，请填写后重试
     */
    String ERR_MSG_BSS_845801186 = "err.msg.bss.845801186";

    /**
     * 修改的桶'
     */
    String ERR_MSG_BSS_1775789814 = "err.msg.bss.1775789814";

    /**
     * 没有定义类别！
     */
    String ERR_MSG_BSS_1754767898 = "err.msg.bss.1754767898";

    /**
     * 该分销商组织下存在分销商管理员账号，不能删除
     */
    String ERR_MSG_BSS_1114132023 = "err.msg.bss.1114132023";

    /**
     * 无法实例化JDBC驱动程序：
     */
    String ERR_MSG_BSS_741116528 = "err.msg.bss.741116528";

    /**
     * 启用失败
     */
    String ERR_MSG_BSS_671592109 = "err.msg.bss.671592109";

    /**
     * 认证成功
     */
    String ERR_MSG_BSS_255096396 = "err.msg.bss.255096396";

    /**
     * 可行的
     */
    String ERR_MSG_BSS_21760999 = "err.msg.bss.21760999";

    /**
     * 客户！
     */
    String ERR_MSG_BSS_23387852 = "err.msg.bss.23387852";

    /**
     * 该云环境已被删除，请刷新后重试
     */
    String ERR_MSG_BSS_1239981845 = "err.msg.bss.1239981845";

    /**
     * 超过华为云私有网络配额限制！
     */
    String ERR_MSG_BSS_1196584917 = "err.msg.bss.1196584917";

    /**
     * 自服务类型为空
     */
    String ERR_MSG_BSS_34504818 = "err.msg.bss.34504818";

    /**
     * 该标签已经存在
     */
    String ERR_MSG_BSS_790081623 = "err.msg.bss.790081623";

    /**
     * 原镜像不存在.
     */
    String ERR_MSG_BSS_153033725 = "err.msg.bss.153033725";

    /**
     * 校验失败
     */
    String ERR_MSG_BSS_832903647 = "err.msg.bss.832903647";

    /**
     * 优惠券已过期，无法分发
     */
    String ERR_MSG_BSS_438123602 = "err.msg.bss.438123602";

    /**
     * 产品与模板类型不匹配！
     */
    String ERR_MSG_BSS_342329630 = "err.msg.bss.342329630";

    /**
     * 属性的值之间不能为空
     */
    String ERR_MSG_BSS_191987546 = "err.msg.bss.191987546";

    /**
     * contentType,(.png/.jpg/.zip...)
     */
    String ERR_MSG_BSS_2004668859 = "err.msg.bss.2004668859";

    /**
     * 请联系运营管理员，当前产品配置未设置资源定价！
     */
    String ERR_MSG_BSS_1523413729 = "err.msg.bss.1523413729";

    /**
     * 请勿频繁请求,请稍后再试!
     */
    String ERR_MSG_BSS_315137718 = "err.msg.bss.315137718";

    /**
     * 】的退订，请勿重复操作
     */
    String ERR_MSG_BSS_1893655570 = "err.msg.bss.1893655570";

    /**
     * 数据不存在
     */
    String ERR_MSG_BSS_1009561377 = "err.msg.bss.1009561377";

    /**
     * 支付类型不对
     */
    String ERR_MSG_BSS_983564549 = "err.msg.bss.983564549";

    /**
     * 发送mq消息错误：
     */
    String ERR_MSG_BSS_915698903 = "err.msg.bss.915698903";

    /**
     * 密码不能包含中文
     */
    String ERR_MSG_BSS_190267765 = "err.msg.bss.190267765";

    /**
     * 调整任务出错.
     */
    String ERR_MSG_BSS_642965944 = "err.msg.bss.642965944";

    /**
     * 获取共享资源池节点信息失败
     */
    String ERR_MSG_BSS_2127464660 = "err.msg.bss.2127464660";

    /**
     * 失效时间不符合规则！
     */
    String ERR_MSG_BSS_843335026 = "err.msg.bss.843335026";

    /**
     * 该子网有
     */
    String ERR_MSG_BSS_1090406179 = "err.msg.bss.1090406179";

    /**
     * HPC专属资源池:
     */
    String ERR_MSG_BSS_1815238196 = "err.msg.bss.1815238196";

    /**
     * 该弹性规则不存在,请刷新后重新
     */
    String ERR_MSG_BSS_821376200 = "err.msg.bss.821376200";

    /**
     * 分级配置解析异常
     */
    String ERR_MSG_BSS_1727129051 = "err.msg.bss.1727129051";

    /**
     * 邮箱发送密码为空
     */
    String ERR_MSG_BSS_1784912242 = "err.msg.bss.1784912242";

    /**
     * 镜像不存在
     */
    String ERR_MSG_BSS_1588245962 = "err.msg.bss.1588245962";

    /**
     * 一个资源池正在使用该监听，无法删除
     */
    String ERR_MSG_BSS_611328003 = "err.msg.bss.611328003";

    /**
     * 许可证已过期，请导入有效许可证
     */
    String ERR_MSG_BSS_675288471 = "err.msg.bss.675288471";

    /**
     * ********
     */
    String ERR_MSG_BSS_568870111 = "err.msg.bss.568870111";

    /**
     * 该资源模板已关联服务，请先删除服务!
     */
    String ERR_MSG_BSS_339861348 = "err.msg.bss.339861348";

    /**
     * appKey不能为空
     */
    String ERR_MSG_BSS_942080398 = "err.msg.bss.942080398";

    /**
     * 关闭所有定时任务出错.
     */
    String ERR_MSG_BSS_800000438 = "err.msg.bss.800000438";

    /**
     * 区域与磁盘类型配置文件不匹配错误
     */
    String ERR_MSG_BSS_1350062775 = "err.msg.bss.1350062775";

    /**
     * 隐藏客户名称不能超过150字符
     */
    String ERR_MSG_BSS_1090723566 = "err.msg.bss.1090723566";

    /**
     * 只有包年包月资源才允许续费操作
     */
    String ERR_MSG_BSS_1852197570 = "err.msg.bss.1852197570";

    /**
     * 更新失败
     */
    String ERR_MSG_BSS_810932304 = "err.msg.bss.810932304";

    /**
     * 余额不足
     */
    String ERR_MSG_BSS_643345578 = "err.msg.bss.643345578";

    /**
     * 后端服务组已存在
     */
    String ERR_MSG_BSS_1291774483 = "err.msg.bss.1291774483";

    /**
     * 平台未开启解锁用戶方式, 请点击“忘记密码”激活或联系管理员
     */
    String ERR_MSG_BSS_1990741850 = "err.msg.bss.1990741850";

    /**
     * 无过期合同
     */
    String ERR_MSG_BSS_561800932 = "err.msg.bss.561800932";

    /**
     * 短信验证码验证成功
     */
    String ERR_MSG_BSS_1465594404 = "err.msg.bss.1465594404";

    /**
     * 当前组已经不存在，请刷新后重试
     */
    String ERR_MSG_BSS_1746407428 = "err.msg.bss.1746407428";

    /**
     * 找不到方法[
     */
    String ERR_MSG_BSS_2068363970 = "err.msg.bss.2068363970";

    /**
     * 该用户未通过审核
     */
    String ERR_MSG_BSS_923588186 = "err.msg.bss.923588186";

    /**
     * CIDR与其他子网重复，请检查后重试
     */
    String ERR_MSG_BSS_1026606763 = "err.msg.bss.1026606763";

    /**
     * 获取堡垒机远程连接信息失败:
     */
    String ERR_MSG_BSS_381827496 = "err.msg.bss.381827496";

    /**
     * 同步环境[
     */
    String ERR_MSG_BSS_756152146 = "err.msg.bss.756152146";

    /**
     * 系统许可证已过期，请联系管理员更新许可证后进行操作
     */
    String ERR_MSG_BSS_1985111101 = "err.msg.bss.1985111101";

    /**
     * 用户ID非法！
     */
    String ERR_MSG_BSS_473915648 = "err.msg.bss.473915648";

    /**
     * 用户状态不正确不能修改
     */
    String ERR_MSG_BSS_117295047 = "err.msg.bss.117295047";

    /**
     * 卸载中不支持变配！
     */
    String ERR_MSG_BSS_1812644843 = "err.msg.bss.1812644843";

    /**
     * 当前规格族已存在，请重新命名！
     */
    String ERR_MSG_BSS_1625441630 = "err.msg.bss.1625441630";

    /**
     * 按环境id获取云客户端错误
     */
    String ERR_MSG_BSS_1375697066 = "err.msg.bss.1375697066";

    /**
     * 修改失败，数据范围不正确
     */
    String ERR_MSG_BSS_221512740 = "err.msg.bss.221512740";

    /**
     * 扩容VNC节点数不能小于1
     */
    String ERR_MSG_BSS_1603483644 = "err.msg.bss.1603483644";

    /**
     * 桶策略不存在
     */
    String ERR_MSG_BSS_1106316216 = "err.msg.bss.1106316216";

    /**
     * 未配置HPC通行证！
     */
    String ERR_MSG_BSS_76560962 = "err.msg.bss.76560962";

    /**
     * MA租户专属资源池列表查询异常！
     */
    String ERR_MSG_BSS_1993388768 = "err.msg.bss.1993388768";

    /**
     * 资源规格【
     */
    String ERR_MSG_BSS_83588404 = "err.msg.bss.83588404";

    /**
     * 该模板已启用，请勿重复启用！
     */
    String ERR_MSG_BSS_45839932 = "err.msg.bss.45839932";

    /**
     * 不支持的流程类型
     */
    String ERR_MSG_BSS_416822847 = "err.msg.bss.416822847";

    /**
     * 该VPC网络不存在,请刷新后重试
     */
    String ERR_MSG_BSS_1167679164 = "err.msg.bss.1167679164";

    /**
     * 不支持的数据类型
     */
    String ERR_MSG_BSS_351647827 = "err.msg.bss.351647827";

    /**
     * 密码至少包含三种不同规则
     */
    String ERR_MSG_BSS_1348714857 = "err.msg.bss.1348714857";

    /**
     * 该 VPC 下仍有资源未释放，请释放后在尝试删除
     */
    String ERR_MSG_BSS_597415794 = "err.msg.bss.597415794";

    /**
     * 暂时无法注册用户，所导入数量超过用户可注册上限，当前已存在客户(
     */
    String ERR_MSG_BSS_2043330009 = "err.msg.bss.2043330009";

    /**
     * [%s]不可为空
     */
    String ERR_MSG_BSS_2140323978 = "err.msg.bss.2140323978";

    /**
     * 计费策略已禁用请勿重复禁用
     */
    String ERR_MSG_BSS_2130335647 = "err.msg.bss.2130335647";

    /**
     * 仅HCSO, 华为私有云,OpenStack系列,金山公有云支持硬盘扩容
     */
    String ERR_MSG_BSS_40502799 = "err.msg.bss.40502799";

    /**
     * 硬盘 底层资源已不存在，请重新选择
     */
    String ERR_MSG_BSS_1160765990 = "err.msg.bss.1160765990";

    /**
     * 现金券分发失败.
     */
    String ERR_MSG_BSS_566566488 = "err.msg.bss.566566488";

    /**
     * ]不在范围内[
     */
    String ERR_MSG_BSS_1893842783 = "err.msg.bss.1893842783";

    /**
     * Ansible压缩文件夹失败
     */
    String ERR_MSG_BSS_2136762360 = "err.msg.bss.2136762360";

    /**
     * 流程定义名称不能重复！
     */
    String ERR_MSG_BSS_1149419381 = "err.msg.bss.1149419381";

    /**
     * 不可续订已过期资源
     */
    String ERR_MSG_BSS_1564190207 = "err.msg.bss.1564190207";

    /**
     * 系统错误，请联系管理员！
     */
    String ERR_MSG_BSS_1442939642 = "err.msg.bss.1442939642";

    /**
     * 任务未找到
     */
    String ERR_MSG_BSS_2145813674 = "err.msg.bss.2145813674";

    /**
     * 资源名称：
     */
    String ERR_MSG_BSS_96609621 = "err.msg.bss.96609621";

    /**
     * 年份为空
     */
    String ERR_MSG_BSS_740430921 = "err.msg.bss.740430921";

    /**
     * 暴力检测！
     */
    String ERR_MSG_BSS_831896561 = "err.msg.bss.831896561";

    /**
     * 开票中和已开票的关联资源未到期不能进行变更
     */
    String ERR_MSG_BSS_1151127480 = "err.msg.bss.1151127480";

    /**
     * 无该规格族定价权限
     */
    String ERR_MSG_BSS_1163490172 = "err.msg.bss.1163490172";

    /**
     * 测试管理员！
     */
    String ERR_MSG_BSS_1822775256 = "err.msg.bss.1822775256";

    /**
     * setLogWriter
     */
    String ERR_MSG_BSS_690888043 = "err.msg.bss.690888043";

    /**
     * noticeId不能为null
     */
    String ERR_MSG_BSS_672622914 = "err.msg.bss.672622914";

    /**
     * 元，请充值后进行购买.
     */
    String ERR_MSG_BSS_1466642481 = "err.msg.bss.1466642481";

    /**
     * ，没有配置服务到期提醒发送频率参数
     */
    String ERR_MSG_BSS_1151785970 = "err.msg.bss.1151785970";

    /**
     * 所选文件系统不可用，请检查后重试！
     */
    String ERR_MSG_BSS_1715570267 = "err.msg.bss.1715570267";

    /**
     * 密钥对
     */
    String ERR_MSG_BSS_23780922 = "err.msg.bss.23780922";

    /**
     * 删除最后一个VPC时，请先删除弹性IP
     */
    String ERR_MSG_BSS_1210720179 = "err.msg.bss.1210720179";

    /**
     * 云环境不支持此操作!
     */
    String ERR_MSG_BSS_1958744504 = "err.msg.bss.1958744504";

    /**
     * 产品配置JSON
     */
    String ERR_MSG_BSS_1031443549 = "err.msg.bss.1031443549";

    /**
     * 合同周期不能为空
     */
    String ERR_MSG_BSS_52272715 = "err.msg.bss.52272715";

    /**
     * 该虚拟网卡已经被删除，请刷新页面后重试。
     */
    String ERR_MSG_BSS_1249072459 = "err.msg.bss.1249072459";

    /**
     * [{}]VNC
     */
    String ERR_MSG_BSS_1496829557 = "err.msg.bss.1496829557";

    /**
     * 获取产品规格错误！
     */
    String ERR_MSG_BSS_382982342 = "err.msg.bss.382982342";

    /**
     * 用户id错误
     */
    String ERR_MSG_BSS_1401732832 = "err.msg.bss.1401732832";

    /**
     * 充值记录可开票金额为空，开票失败
     */
    String ERR_MSG_BSS_1129399905 = "err.msg.bss.1129399905";

    /**
     * 越权操作，没有该发票的审核权限
     */
    String ERR_MSG_BSS_1849032376 = "err.msg.bss.1849032376";

    /**
     * 请求异常
     */
    String ERR_MSG_BSS_1094852577 = "err.msg.bss.1094852577";

    /**
     * 时间表
     */
    String ERR_MSG_BSS_26308970 = "err.msg.bss.26308970";

    /**
     * 已存在关联关系，请勿重复关联
     */
    String ERR_MSG_BSS_44498314 = "err.msg.bss.44498314";

    /**
     * 对端证书已吊销
     */
    String ERR_MSG_BSS_394051987 = "err.msg.bss.394051987";

    /**
     * ]\n
     */
    String ERR_MSG_BSS_92335 = "err.msg.bss.92335";

    /**
     * 取得控制台URL失败。
     */
    String ERR_MSG_BSS_50242815 = "err.msg.bss.50242815";

    /**
     * 该状态不能删除折扣策略
     */
    String ERR_MSG_BSS_587662754 = "err.msg.bss.587662754";

    /**
     * 邮箱重复
     */
    String ERR_MSG_BSS_1135008995 = "err.msg.bss.1135008995";

    /**
     * 现金余额小于0，不能开通资源！
     */
    String ERR_MSG_BSS_463148427 = "err.msg.bss.463148427";

    /**
     * 验证失败！
     */
    String ERR_MSG_BSS_1027560296 = "err.msg.bss.1027560296";

    /**
     * 账户列表正在生成，请稍后下载！
     */
    String ERR_MSG_BSS_1114164011 = "err.msg.bss.1114164011";

    /**
     * 当前用户无审批权限
     */
    String ERR_MSG_BSS_1290743044 = "err.msg.bss.1290743044";

    /**
     * 创建失败，该资源ID已加入白名单！
     */
    String ERR_MSG_BSS_1015558464 = "err.msg.bss.1015558464";

    /**
     * 无法正确取得配额信息。
     */
    String ERR_MSG_BSS_570158371 = "err.msg.bss.570158371";

    /**
     * 权限不足
     */
    String ERR_MSG_BSS_825160051 = "err.msg.bss.825160051";

    /**
     * AI大屏接口地址必须是网址
     */
    String ERR_MSG_BSS_1997355146 = "err.msg.bss.1997355146";

    /**
     * 部门未找到
     */
    String ERR_MSG_BSS_1068638748 = "err.msg.bss.1068638748";

    /**
     * 请输入正确的IP！
     */
    String ERR_MSG_BSS_1923980618 = "err.msg.bss.1923980618";

    /**
     * 绑定子网异常
     */
    String ERR_MSG_BSS_1652290656 = "err.msg.bss.1652290656";

    /**
     * 所需字符串长度太大：
     */
    String ERR_MSG_BSS_883007571 = "err.msg.bss.883007571";

    /**
     * 认证失败，请勾选隐私声明
     */
    String ERR_MSG_BSS_1121608694 = "err.msg.bss.1121608694";

    /**
     * 默认用户组不支持修改
     */
    String ERR_MSG_BSS_120386593 = "err.msg.bss.120386593";

    /**
     * 无法自动检测url的JDBC驱动程序：
     */
    String ERR_MSG_BSS_1099877423 = "err.msg.bss.1099877423";

    /**
     * 无此运营实体数据权限！
     */
    String ERR_MSG_BSS_264414504 = "err.msg.bss.264414504";

    /**
     * 未找到子网网卡信息
     */
    String ERR_MSG_BSS_388979845 = "err.msg.bss.388979845";

    /**
     * 名称超长
     */
    String ERR_MSG_BSS_672172893 = "err.msg.bss.672172893";

    /**
     * 审核流程提交成功，编号
     */
    String ERR_MSG_BSS_1993777303 = "err.msg.bss.1993777303";

    /**
     * 连接失败！请检查域名,和账号保持一致!
     */
    String ERR_MSG_BSS_1810726772 = "err.msg.bss.1810726772";

    /**
     * 发票申请成功
     */
    String ERR_MSG_BSS_317316374 = "err.msg.bss.317316374";

    /**
     * orgSid={},productType={}-{},
     */
    String ERR_MSG_BSS_675878934 = "err.msg.bss.675878934";

    /**
     * 有创建的弹性ip
     */
    String ERR_MSG_BSS_1479346737 = "err.msg.bss.1479346737";

    /**
     * 该HCSO账户[配置子用户到用户组失败，请确认后再试
     */
    String ERR_MSG_BSS_1668342964 = "err.msg.bss.1668342964";

    /**
     * ,id=[{}]
     */
    String ERR_MSG_BSS_994932438 = "err.msg.bss.994932438";

    /**
     * 价格不能为空!
     */
    String ERR_MSG_BSS_1773415156 = "err.msg.bss.1773415156";

    /**
     * 无审批权限
     */
    String ERR_MSG_BSS_960484795 = "err.msg.bss.960484795";

    /**
     * 实例挂载信息未找到，请刷新后重试！
     */
    String ERR_MSG_BSS_768632853 = "err.msg.bss.768632853";

    /**
     * 已被禁用，请勿重复禁用
     */
    String ERR_MSG_BSS_1240628709 = "err.msg.bss.1240628709";

    /**
     * 连接名称不能重复!
     */
    String ERR_MSG_BSS_1382569561 = "err.msg.bss.1382569561";

    /**
     * 网关地址已被其他实例占用，请检查后重试。
     */
    String ERR_MSG_BSS_1639950916 = "err.msg.bss.1639950916";

    /**
     * 触发器触发器名称=
     */
    String ERR_MSG_BSS_1865527750 = "err.msg.bss.1865527750";

    /**
     * 登录节点数超过限制，无法扩容，请联系管理员
     */
    String ERR_MSG_BSS_1893187657 = "err.msg.bss.1893187657";

    /**
     * 现金券无效
     */
    String ERR_MSG_BSS_1580643361 = "err.msg.bss.1580643361";

    /**
     * 结束时间不能小于当前时间
     */
    String ERR_MSG_BSS_940920911 = "err.msg.bss.940920911";

    /**
     * 重新扩容配置信息异常
     */
    String ERR_MSG_BSS_575100120 = "err.msg.bss.575100120";

    /**
     * 合同超期
     */
    String ERR_MSG_BSS_662690366 = "err.msg.bss.662690366";

    /**
     * 分发用户为空
     */
    String ERR_MSG_BSS_881048870 = "err.msg.bss.881048870";

    /**
     * 类型不匹配 (方法：
     */
    String ERR_MSG_BSS_117254555 = "err.msg.bss.117254555";

    /**
     * 当前用户不存在
     */
    String ERR_MSG_BSS_1473130476 = "err.msg.bss.1473130476";

    /**
     * 当前状态不支持继续扩容
     */
    String ERR_MSG_BSS_230653520 = "err.msg.bss.230653520";

    /**
     * 可使用额度不足
     */
    String ERR_MSG_BSS_1769382503 = "err.msg.bss.1769382503";

    /**
     * 方法不能为空
     */
    String ERR_MSG_BSS_1204534196 = "err.msg.bss.1204534196";

    /**
     * 无该审批任务，或已被处理
     */
    String ERR_MSG_BSS_2013176584 = "err.msg.bss.2013176584";

    /**
     * 请求体中的转换编码异常
     */
    String ERR_MSG_BSS_1301038826 = "err.msg.bss.1301038826";

    /**
     * 用户名不能为空
     */
    String ERR_MSG_BSS_609490706 = "err.msg.bss.609490706";

    /**
     * ID={},ModelArts
     */
    String ERR_MSG_BSS_706069807 = "err.msg.bss.706069807";

    /**
     * 发送mq消息出错:
     */
    String ERR_MSG_BSS_932204288 = "err.msg.bss.932204288";

    /**
     * 转换对象失败
     */
    String ERR_MSG_BSS_1639013906 = "err.msg.bss.1639013906";

    /**
     * 找不到指定服务！
     */
    String ERR_MSG_BSS_129431001 = "err.msg.bss.129431001";

    /**
     * 该子用户数据异常，上级租户不存在！
     */
    String ERR_MSG_BSS_367921623 = "err.msg.bss.367921623";

    /**
     * map转为对象参数
     */
    String ERR_MSG_BSS_641827552 = "err.msg.bss.641827552";

    /**
     * 修改成功！
     */
    String ERR_MSG_BSS_1780929689 = "err.msg.bss.1780929689";

    /**
     * 作业对象添加作业参数，作业类型：
     */
    String ERR_MSG_BSS_381942057 = "err.msg.bss.381942057";

    /**
     * 权限组不存在
     */
    String ERR_MSG_BSS_1233528506 = "err.msg.bss.1233528506";

    /**
     * 资源池ID不能相同!
     */
    String ERR_MSG_BSS_710679854 = "err.msg.bss.710679854";

    /**
     * 您无法对自身进行用户组关联
     */
    String ERR_MSG_BSS_309548600 = "err.msg.bss.309548600";

    /**
     * 管理，测试
     */
    String ERR_MSG_BSS_112246097 = "err.msg.bss.112246097";

    /**
     * 无效监控指标项:
     */
    String ERR_MSG_BSS_583021025 = "err.msg.bss.583021025";

    /**
     * 回滚子用户
     */
    String ERR_MSG_BSS_68748253 = "err.msg.bss.68748253";

    /**
     * 共享资源池节点数量不足，操作后剩余节点数不能小于配置的共享资源池最小节点数
     */
    String ERR_MSG_BSS_168554852 = "err.msg.bss.168554852";

    /**
     * 目前只支持阿里云的RDS实例相关操作
     */
    String ERR_MSG_BSS_316791035 = "err.msg.bss.316791035";

    /**
     * 实例快照正在恢复，请稍后查看。
     */
    String ERR_MSG_BSS_1787684215 = "err.msg.bss.1787684215";

    /**
     * 该客户已存在相同折扣名称！
     */
    String ERR_MSG_BSS_448380951 = "err.msg.bss.448380951";

    /**
     * 无效的手机号
     */
    String ERR_MSG_BSS_1571921428 = "err.msg.bss.1571921428";

    /**
     * 移动失败
     */
    String ERR_MSG_BSS_951362273 = "err.msg.bss.951362273";

    /**
     * 创建失败，桶名称已经被其他用户使用
     */
    String ERR_MSG_BSS_700016218 = "err.msg.bss.700016218";

    /**
     * AWS快照描述不支持中文，请重新修改!
     */
    String ERR_MSG_BSS_1750199273 = "err.msg.bss.1750199273";

    /**
     * 角色ID
     */
    String ERR_MSG_BSS_1083180027 = "err.msg.bss.1083180027";

    /**
     * 只有管理员才能转换为公有镜像
     */
    String ERR_MSG_BSS_596467504 = "err.msg.bss.596467504";

    /**
     * 导出类型，目前只支持按月【month】
     */
    String ERR_MSG_BSS_918593667 = "err.msg.bss.918593667";

    /**
     * 该环境非连接状态
     */
    String ERR_MSG_BSS_1740665975 = "err.msg.bss.1740665975";

    /**
     * 认证失败，身份证未成年
     */
    String ERR_MSG_BSS_1371465254 = "err.msg.bss.1371465254";

    /**
     * 阿皮基不在
     */
    String ERR_MSG_BSS_2089045382 = "err.msg.bss.2089045382";

    /**
     * 策略名称不能重复！
     */
    String ERR_MSG_BSS_1835930175 = "err.msg.bss.1835930175";

    /**
     * 总容量不能小于已使用量
     */
    String ERR_MSG_BSS_1004130644 = "err.msg.bss.1004130644";

    /**
     * 生成文件中
     */
    String ERR_MSG_BSS_1601343667 = "err.msg.bss.1601343667";

    /**
     * 同一类只能出现一次
     */
    String ERR_MSG_BSS_1691783089 = "err.msg.bss.1691783089";

    /**
     * 创建发票异常：更新账单周期数据失败
     */
    String ERR_MSG_BSS_1069443934 = "err.msg.bss.1069443934";

    /**
     * 该虚拟网卡已删除，请请同步网卡信息！
     */
    String ERR_MSG_BSS_1184840858 = "err.msg.bss.1184840858";

    /**
     * 密钥对服务不存在
     */
    String ERR_MSG_BSS_1519718895 = "err.msg.bss.1519718895";

    /**
     * 所选套餐包使用明细超过三个月!
     */
    String ERR_MSG_BSS_216452256 = "err.msg.bss.216452256";

    /**
     * 不能修改完全相同的实例类型
     */
    String ERR_MSG_BSS_706048950 = "err.msg.bss.706048950";

    /**
     * 弹性裸金属产品异常
     */
    String ERR_MSG_BSS_1226065223 = "err.msg.bss.1226065223";

    /**
     * 代客下单管理员ID与目前登录账户ID不匹配！
     */
    String ERR_MSG_BSS_1035468590 = "err.msg.bss.1035468590";

    /**
     * 无法识别的产品代码：
     */
    String ERR_MSG_BSS_335037064 = "err.msg.bss.335037064";

    /**
     * 产品名称不能为空
     */
    String ERR_MSG_BSS_361916813 = "err.msg.bss.361916813";

    /**
     * 暂无权限调整默认规则
     */
    String ERR_MSG_BSS_255947325 = "err.msg.bss.255947325";

    /**
     * 该用户状态存在异常！
     */
    String ERR_MSG_BSS_2124622564 = "err.msg.bss.2124622564";

    /**
     * 默认用户组权限不支持修改
     */
    String ERR_MSG_BSS_1279737620 = "err.msg.bss.1279737620";

    /**
     * 附件不能为空
     */
    String ERR_MSG_BSS_384590782 = "err.msg.bss.384590782";

    /**
     * 产品[{}]无产品模板
     */
    String ERR_MSG_BSS_445890786 = "err.msg.bss.445890786";

    /**
     * 名称不能为空
     */
    String ERR_MSG_BSS_1224618515 = "err.msg.bss.1224618515";

    /**
     * 正在升级用户MA-BMS权限
     */
    String ERR_MSG_BSS_183737680 = "err.msg.bss.183737680";

    /**
     * 文件夹名称重复
     */
    String ERR_MSG_BSS_1980602515 = "err.msg.bss.1980602515";

    /**
     * 查询资产名称不能为空
     */
    String ERR_MSG_BSS_1381459629 = "err.msg.bss.1381459629";

    /**
     * 请实名认证后使用该功能
     */
    String ERR_MSG_BSS_1813224111 = "err.msg.bss.1813224111";

    /**
     * 该云环境不支持此操作。
     */
    String ERR_MSG_BSS_387788850 = "err.msg.bss.387788850";

    /**
     * 真实姓名，只能输入1-10个汉字!
     */
    String ERR_MSG_BSS_1364846652 = "err.msg.bss.1364846652";

    /**
     * 该实例名称已经在环境中存在，请修改后再申请。
     */
    String ERR_MSG_BSS_1296032234 = "err.msg.bss.1296032234";

    /**
     * 发票状态修改失败
     */
    String ERR_MSG_BSS_1303369569 = "err.msg.bss.1303369569";

    /**
     * 存在上架中的服务，取消发布失败！
     */
    String ERR_MSG_BSS_830495300 = "err.msg.bss.830495300";

    /**
     * HPC专属资源池服务未配置计费
     */
    String ERR_MSG_BSS_836856513 = "err.msg.bss.836856513";

    /**
     * [%s]不在指定参数范围
     */
    String ERR_MSG_BSS_1836169249 = "err.msg.bss.1836169249";

    /**
     * 海洋之星
     */
    String ERR_MSG_BSS_862306984 = "err.msg.bss.862306984";

    /**
     * [%s]超出范围
     */
    String ERR_MSG_BSS_1656351462 = "err.msg.bss.1656351462";

    /**
     * 审核通过租户参数不合法!
     */
    String ERR_MSG_BSS_611222993 = "err.msg.bss.611222993";

    /**
     * 只允许修改：试算中、已签单、已注销三种
     */
    String ERR_MSG_BSS_1865846734 = "err.msg.bss.1865846734";

    /**
     * 选择了已注销后不能再选择其他标识！
     */
    String ERR_MSG_BSS_1865846735 = "err.msg.bss.1865846735";

    /**
     * 未找到流程节点
     */
    String ERR_MSG_BSS_456947453 = "err.msg.bss.456947453";

    /**
     * MA版本不合法！
     */
    String ERR_MSG_BSS_2094485473 = "err.msg.bss.2094485473";

    /**
     * 资源池
     */
    String ERR_MSG_BSS_35658772 = "err.msg.bss.35658772";

    /**
     * 文件未找到
     */
    String ERR_MSG_BSS_1138191507 = "err.msg.bss.1138191507";

    /**
     * 已作废的优惠券不可再次作废
     */
    String ERR_MSG_BSS_1941166630 = "err.msg.bss.1941166630";

    /**
     * 真实姓名不能为空!
     */
    String ERR_MSG_BSS_2061043592 = "err.msg.bss.2061043592";

    /**
     * MA申请节点超过许可证容量限制，请联系管理员扩容节点数量
     */
    String ERR_MSG_BSS_1112833614 = "err.msg.bss.1112833614";

    /**
     * 无权限操作。
     */
    String ERR_MSG_BSS_860779194 = "err.msg.bss.860779194";

    /**
     * 请输入正确验证码，验证码长度为6位
     */
    String ERR_MSG_BSS_971001586 = "err.msg.bss.971001586";

    /**
     * 订单参数缺失.
     */
    String ERR_MSG_BSS_2071376150 = "err.msg.bss.2071376150";

    /**
     * 原企业没有找到
     */
    String ERR_MSG_BSS_800405742 = "err.msg.bss.800405742";

    /**
     * 价值！
     */
    String ERR_MSG_BSS_20128636 = "err.msg.bss.20128636";

    /**
     * 不属于当前运营实体，请勿操作
     */
    String ERR_MSG_BSS_885284322 = "err.msg.bss.885284322";

    /**
     * 删除失败，请重试
     */
    String ERR_MSG_BSS_455354539 = "err.msg.bss.455354539";

    /**
     * [%s]不在约定范围
     */
    String ERR_MSG_BSS_1562652212 = "err.msg.bss.1562652212";

    /**
     * 系统错误，请稍后重试
     */
    String ERR_MSG_BSS_1414917458 = "err.msg.bss.1414917458";

    /**
     * 删除BMS白名单成功
     */
    String ERR_MSG_BSS_11489968 = "err.msg.bss.11489968";

    /**
     * 找不到群集！
     */
    String ERR_MSG_BSS_2061589698 = "err.msg.bss.2061589698";

    /**
     * 申请类型参数异常
     */
    String ERR_MSG_BSS_196979320 = "err.msg.bss.196979320";

    /**
     * 该用户不存在
     */
    String ERR_MSG_BSS_1496023145 = "err.msg.bss.1496023145";

    /**
     * 该存储类型中的存储在分配之后均会超过分配阀值，无法分配资源。
     */
    String ERR_MSG_BSS_61090132 = "err.msg.bss.61090132";

    /**
     * 创建失败，请重试
     */
    String ERR_MSG_BSS_1246836634 = "err.msg.bss.1246836634";

    /**
     * 解冻成功
     */
    String ERR_MSG_BSS_1072498887 = "err.msg.bss.1072498887";

    /**
     * 验证数据不能为空！
     */
    String ERR_MSG_BSS_27318110 = "err.msg.bss.27318110";

    /**
     * HPC申请节点超过许可证容量限制，请联系管理员扩容节点数量
     */
    String ERR_MSG_BSS_1590100583 = "err.msg.bss.1590100583";

    /**
     * 文件系统不存在
     */
    String ERR_MSG_BSS_1801286422 = "err.msg.bss.1801286422";

    /**
     * 该客户没有关联该分销商,不能创建子用户!
     */
    String ERR_MSG_BSS_805320749 = "err.msg.bss.805320749";

    /**
     * 当前云环境下有资源还未删除
     */
    String ERR_MSG_BSS_1994946517 = "err.msg.bss.1994946517";

    /**
     * 选择的GPU资源组未关联设备
     */
    String ERR_MSG_BSS_422633540 = "err.msg.bss.422633540";

    /**
     * 分销商不能关联超级管理员
     */
    String ERR_MSG_BSS_1633824524 = "err.msg.bss.1633824524";

    /**
     * 该角色下还有用户，删除失败
     */
    String ERR_MSG_BSS_208118782 = "err.msg.bss.208118782";

    /**
     * 规格族名称不能包含特殊字符
     */
    String ERR_MSG_BSS_162700400 = "err.msg.bss.162700400";

    /**
     * 实例[{}]选择的硬盘不存在
     */
    String ERR_MSG_BSS_1176312154 = "err.msg.bss.1176312154";

    /**
     * 该主机已被集群使用，无法删除
     */
    String ERR_MSG_BSS_291574113 = "err.msg.bss.291574113";

    /**
     * 修改套餐包规格成功
     */
    String ERR_MSG_BSS_177439832 = "err.msg.bss.177439832";

    /**
     * 获取当前登录用户失败!
     */
    String ERR_MSG_BSS_718648683 = "err.msg.bss.718648683";

    /**
     * 有创建的硬盘
     */
    String ERR_MSG_BSS_2071995544 = "err.msg.bss.2071995544";

    /**
     * 该公告已发布,不能删除
     */
    String ERR_MSG_BSS_1668420543 = "err.msg.bss.1668420543";

    /**
     * 无登录信息
     */
    String ERR_MSG_BSS_756673464 = "err.msg.bss.756673464";

    /**
     * 重新扩容失败
     */
    String ERR_MSG_BSS_1818666873 = "err.msg.bss.1818666873";

    /**
     * 云主机【
     */
    String ERR_MSG_BSS_619263840 = "err.msg.bss.619263840";

    /**
     * 找不到相关配置
     */
    String ERR_MSG_BSS_431199933 = "err.msg.bss.431199933";

    /**
     * 文件保存异常！请确认错误日志！
     */
    String ERR_MSG_BSS_977757875 = "err.msg.bss.977757875";

    /**
     * 模板不存在
     */
    String ERR_MSG_BSS_130599775 = "err.msg.bss.130599775";

    /**
     * 用户名无效
     */
    String ERR_MSG_BSS_1595981882 = "err.msg.bss.1595981882";

    /**
     * 手动冻结的账户只能由运营管理员手动解冻，由充值自动触发的解冻操作低于此优先级，无法解冻
     */
    String ERR_MSG_BSS_133625621 = "err.msg.bss.133625621";

    /**
     * 请求参数不能为空
     */
    String ERR_MSG_BSS_367330839 = "err.msg.bss.367330839";

    /**
     * Ldap同步用户异常，请重试
     */
    String ERR_MSG_BSS_1472357752 = "err.msg.bss.1472357752";

    /**
     * 云账号不存在。
     */
    String ERR_MSG_BSS_1129244953 = "err.msg.bss.1129244953";

    /**
     * 文件大小不能超过1MB，请重新上传文件
     */
    String ERR_MSG_BSS_961282320 = "err.msg.bss.961282320";

    /**
     * 购买的数量
     */
    String ERR_MSG_BSS_354563552 = "err.msg.bss.354563552";

    /**
     * 用户Id为空
     */
    String ERR_MSG_BSS_1400212618 = "err.msg.bss.1400212618";

    /**
     * 您无法修改自身账号的状态
     */
    String ERR_MSG_BSS_386463893 = "err.msg.bss.386463893";

    /**
     * sharePoolId为空！
     */
    String ERR_MSG_BSS_42530987 = "err.msg.bss.42530987";

    /**
     * 该操作云环境不支持
     */
    String ERR_MSG_BSS_1771488466 = "err.msg.bss.1771488466";

    /**
     * 组织名已经存在
     */
    String ERR_MSG_BSS_190088809 = "err.msg.bss.190088809";

    /**
     * 公司电话格式不符合规则！
     */
    String ERR_MSG_BSS_1621619612 = "err.msg.bss.1621619612";

    /**
     * 规格族未找到，请刷新后重试！
     */
    String ERR_MSG_BSS_714653836 = "err.msg.bss.714653836";

    /**
     * ，服务到期时间参数值必须为整数
     */
    String ERR_MSG_BSS_564176897 = "err.msg.bss.564176897";

    /**
     * 一级组织不能选择父级
     */
    String ERR_MSG_BSS_557111211 = "err.msg.bss.557111211";

    /**
     * 默认共享目录不能超过10000GB
     */
    String ERR_MSG_BSS_634311251 = "err.msg.bss.634311251";

    /**
     * RouterApi不存在
     */
    String ERR_MSG_BSS_613025268 = "err.msg.bss.613025268";

    /**
     * 用户id不能为空
     */
    String ERR_MSG_BSS_2098358662 = "err.msg.bss.2098358662";

    /**
     * 存在重复的自定义操作日志SpEL方法:
     */
    String ERR_MSG_BSS_165017644 = "err.msg.bss.165017644";

    /**
     * 名称不能为空！
     */
    String ERR_MSG_BSS_691466418 = "err.msg.bss.691466418";

    /**
     * ，不支持配置的发送方式
     */
    String ERR_MSG_BSS_1209515818 = "err.msg.bss.1209515818";

    /**
     * 更新账单周期汇总失败请检查
     */
    String ERR_MSG_BSS_238996974 = "err.msg.bss.238996974";

    /**
     * 扩容计算节点不能大于【
     */
    String ERR_MSG_BSS_913931588 = "err.msg.bss.913931588";

    /**
     * 权限组已经被挂载点使用，请移除挂载点再删除！
     */
    String ERR_MSG_BSS_89884682 = "err.msg.bss.89884682";

    /**
     * 没有找到该合同模板
     */
    String ERR_MSG_BSS_1543781587 = "err.msg.bss.1543781587";

    /**
     * 已分发的优惠券不允许作废
     */
    String ERR_MSG_BSS_1697650142 = "err.msg.bss.1697650142";

    /**
     * 获取父记录器
     */
    String ERR_MSG_BSS_1769354516 = "err.msg.bss.1769354516";

    /**
     * 配置值格式错误
     */
    String ERR_MSG_BSS_1601878692 = "err.msg.bss.1601878692";

    /**
     * 创建网络信息失败
     */
    String ERR_MSG_BSS_1428622228 = "err.msg.bss.1428622228";

    /**
     * 资源池不存在
     */
    String ERR_MSG_BSS_1473503497 = "err.msg.bss.1473503497";

    /**
     * 国标
     */
    String ERR_MSG_BSS_716970 = "err.msg.bss.716970";

    /**
     * 添加消费记录失败，当前项目账户为空！
     */
    String ERR_MSG_BSS_1085853788 = "err.msg.bss.1085853788";

    /**
     * 当前云环境暂不支持此操作
     */
    String ERR_MSG_BSS_1687251179 = "err.msg.bss.1687251179";

    /**
     * 该虚拟网卡已删除，请同步网卡信息！
     */
    String ERR_MSG_BSS_666758141 = "err.msg.bss.666758141";

    /**
     * =[{}],sfs-size
     */
    String ERR_MSG_BSS_1078765251 = "err.msg.bss.1078765251";

    /**
     * 云硬盘暂时不支持创建快照.
     */
    String ERR_MSG_BSS_1859199685 = "err.msg.bss.1859199685";

    /**
     * 这个上下文在一个服务链中！
     */
    String ERR_MSG_BSS_1832893569 = "err.msg.bss.1832893569";

    /**
     * 无法正确取得组织信息。
     */
    String ERR_MSG_BSS_285980848 = "err.msg.bss.285980848";

    /**
     * 更新防火墙对象失败，请重试！
     */
    String ERR_MSG_BSS_848292882 = "err.msg.bss.848292882";

    /**
     * 该用户不能修改角色
     */
    String ERR_MSG_BSS_569164433 = "err.msg.bss.569164433";

    /**
     * 该业务类型通知已经存在！
     */
    String ERR_MSG_BSS_2081106029 = "err.msg.bss.2081106029";

    /**
     * 节点不能为空！
     */
    String ERR_MSG_BSS_1456492326 = "err.msg.bss.1456492326";

    /**
     * 文件为空，请重新上传有效文件
     */
    String ERR_MSG_BSS_7890753 = "err.msg.bss.7890753";

    /**
     * 越权导出
     */
    String ERR_MSG_BSS_1105602103 = "err.msg.bss.1105602103";

    /**
     * 特殊字符校验不通过
     */
    String ERR_MSG_BSS_1543875409 = "err.msg.bss.1543875409";

    /**
     * 该通知已经发送成功!
     */
    String ERR_MSG_BSS_1960753781 = "err.msg.bss.1960753781";

    /**
     * ，没有配置服务到期发送提醒开关参数
     */
    String ERR_MSG_BSS_2064022187 = "err.msg.bss.2064022187";

    /**
     * 系统保留用户，禁止创建此用户！
     */
    String ERR_MSG_BSS_926207125 = "err.msg.bss.926207125";

    /**
     * 账户不存在
     */
    String ERR_MSG_BSS_220246484 = "err.msg.bss.220246484";

    /**
     * 审核通过或审核驳回标识不能为空!
     */
    String ERR_MSG_BSS_831986910 = "err.msg.bss.831986910";

    /**
     * 非分布式逻辑路由，不可以添加interface！
     */
    String ERR_MSG_BSS_1099866256 = "err.msg.bss.1099866256";

    /**
     * 添加消费记录失败，消费记录没有关联到账户
     */
    String ERR_MSG_BSS_1802919077 = "err.msg.bss.1802919077";

    /**
     * 未经授权的操作。
     */
    String ERR_MSG_BSS_2041878799 = "err.msg.bss.2041878799";

    /**
     * ldap[{}]
     */
    String ERR_MSG_BSS_684305191 = "err.msg.bss.684305191";

    /**
     * 请配置共享资源池ID
     */
    String ERR_MSG_BSS_1317592035 = "err.msg.bss.1317592035";

    /**
     * 请确认当前操作用户是否具备查看权限
     */
    String ERR_MSG_BSS_87098879 = "err.msg.bss.87098879";

    /**
     * 处理方式错误
     */
    String ERR_MSG_BSS_2027073390 = "err.msg.bss.2027073390";

    /**
     * 未接入HCSO云环境
     */
    String ERR_MSG_BSS_1000498884 = "err.msg.bss.1000498884";

    /**
     * 堡垒机信息不存在！
     */
    String ERR_MSG_BSS_935596613 = "err.msg.bss.935596613";

    /**
     * 文件格式不合法
     */
    String ERR_MSG_BSS_1641157752 = "err.msg.bss.1641157752";

    /**
     * 展开
     */
    String ERR_MSG_BSS_757067 = "err.msg.bss.757067";

    /**
     * 云环境下的某个主机存在于某个集群中，请确保云环境下的主机没有被使用再进行删除云环境操作
     */
    String ERR_MSG_BSS_97301623 = "err.msg.bss.97301623";

    /**
     * 登录失效
     */
    String ERR_MSG_BSS_927777777 = "err.msg.bss.927777777";

    /**
     * 该用户不存在。
     */
    String ERR_MSG_BSS_867910471 = "err.msg.bss.867910471";

    /**
     * "
     */
    String ERR_MSG_BSS_34 = "err.msg.bss.34";

    /**
     * 重置的名称重复
     */
    String ERR_MSG_BSS_448552570 = "err.msg.bss.448552570";

    /**
     * 的值列表不能为null或空
     */
    String ERR_MSG_BSS_461139916 = "err.msg.bss.461139916";

    /**
     * 参数类型异常
     */
    String ERR_MSG_BSS_1130213164 = "err.msg.bss.1130213164";

    /**
     * 弹性ip正在删除中,请稍后查看。
     */
    String ERR_MSG_BSS_1182834913 = "err.msg.bss.1182834913";

    /**
     * 共享资源池,未配置存储集群不能创建实例
     */
    String ERR_MSG_BSS_508933341 = "err.msg.bss.508933341";

    /**
     * 实例快照底层资源已不存在，请重新选择。
     */
    String ERR_MSG_BSS_46839705 = "err.msg.bss.46839705";

    /**
     * ]无对应的规格:
     */
    String ERR_MSG_BSS_977373788 = "err.msg.bss.977373788";

    /**
     * 加解密异常
     */
    String ERR_MSG_BSS_865571943 = "err.msg.bss.865571943";

    /**
     * 配置的目标规格不存在，请同步后重试
     */
    String ERR_MSG_BSS_1154368745 = "err.msg.bss.1154368745";

    /**
     * 区域无资源
     */
    String ERR_MSG_BSS_1101937241 = "err.msg.bss.1101937241";

    /**
     * 用户组名称已存在
     */
    String ERR_MSG_BSS_818791318 = "err.msg.bss.818791318";

    /**
     * 属性的值不能为null
     */
    String ERR_MSG_BSS_1821799024 = "err.msg.bss.1821799024";

    /**
     * 该用户已被禁用
     */
    String ERR_MSG_BSS_735250124 = "err.msg.bss.735250124";

    /**
     * 云环境验证不通过
     */
    String ERR_MSG_BSS_724608768 = "err.msg.bss.724608768";

    /**
     * 价格必须小于等于10亿!
     */
    String ERR_MSG_BSS_1423982624 = "err.msg.bss.1423982624";

    /**
     * 复制该套餐包将导致名称超最大字符数，不支持复制!
     */
    String ERR_MSG_BSS_33892673 = "err.msg.bss.33892673";

    /**
     * -
     */
    String ERR_MSG_BSS_45 = "err.msg.bss.45";

    /**
     * 设置折扣有重复，请检查后提交
     */
    String ERR_MSG_BSS_178073412 = "err.msg.bss.178073412";

    /**
     * ,
     */
    String ERR_MSG_BSS_44 = "err.msg.bss.44";

    /**
     * 请正确填写周期!
     */
    String ERR_MSG_BSS_1291505766 = "err.msg.bss.1291505766";

    /**
     * 操作失败!
     */
    String ERR_MSG_BSS_1310047746 = "err.msg.bss.1310047746";

    /**
     * 云环境未找到！
     */
    String ERR_MSG_BSS_1810809622 = "err.msg.bss.1810809622";

    /**
     * 权限标识不能重复
     */
    String ERR_MSG_BSS_1833705412 = "err.msg.bss.1833705412";

    /**
     * 1
     */
    String ERR_MSG_BSS_49 = "err.msg.bss.49";

    /**
     * 规格未变更，请确认。
     */
    String ERR_MSG_BSS_929875085 = "err.msg.bss.929875085";

    /**
     * 该工单已评分，请勿重复评分
     */
    String ERR_MSG_BSS_728330409 = "err.msg.bss.728330409";

    /**
     * 信息
     */
    String ERR_MSG_BSS_658606 = "err.msg.bss.658606";

    /**
     * 找不到用户信息
     */
    String ERR_MSG_BSS_420974398 = "err.msg.bss.420974398";

    /**
     * 权限异常！
     */
    String ERR_MSG_BSS_185982018 = "err.msg.bss.185982018";

    /**
     * 共享资源池剩余容量小于申请容量
     */
    String ERR_MSG_BSS_1462841081 = "err.msg.bss.1462841081";

    /**
     * 大于共享池运行的节点数量!
     */
    String ERR_MSG_BSS_931215074 = "err.msg.bss.931215074";

    /**
     * 模块类型！
     */
    String ERR_MSG_BSS_18819451 = "err.msg.bss.18819451";

    /**
     * 不支持当前方法。
     */
    String ERR_MSG_BSS_981845363 = "err.msg.bss.981845363";

    /**
     * 账户欠费阈值只能为整数
     */
    String ERR_MSG_BSS_1167894885 = "err.msg.bss.1167894885";

    /**
     * 桶名称已经存在
     */
    String ERR_MSG_BSS_979556518 = "err.msg.bss.979556518";

    /**
     * 采集开始时间不能大于采集结束时间！
     */
    String ERR_MSG_BSS_334415417 = "err.msg.bss.334415417";

    /**
     * 结束时间异常！
     */
    String ERR_MSG_BSS_849145697 = "err.msg.bss.849145697";

    /**
     * 存在关联的安全组，无法删除
     */
    String ERR_MSG_BSS_980686829 = "err.msg.bss.980686829";

    /**
     * 参数错误
     */
    String ERR_MSG_BSS_664952932 = "err.msg.bss.664952932";

    /**
     * 安全组
     */
    String ERR_MSG_BSS_23197605 = "err.msg.bss.23197605";

    /**
     * 系统盘存储的分配率已超过规定阈值，无法分配资源，请重新选择。
     */
    String ERR_MSG_BSS_606020066 = "err.msg.bss.606020066";

    /**
     * 不支持的方法：%s
     */
    String ERR_MSG_BSS_1813253881 = "err.msg.bss.1813253881";

    /**
     * 操作失败:
     */
    String ERR_MSG_BSS_1310047721 = "err.msg.bss.1310047721";

    /**
     * 请联系系统管理员配置组织层级。
     */
    String ERR_MSG_BSS_66713528 = "err.msg.bss.66713528";

    /**
     * 您已越权!
     */
    String ERR_MSG_BSS_2128881086 = "err.msg.bss.2128881086";

    /**
     * 存在该流程定义的申请，暂无法删除
     */
    String ERR_MSG_BSS_1730214761 = "err.msg.bss.1730214761";

    /**
     * 指定月份参数异常
     */
    String ERR_MSG_BSS_426564724 = "err.msg.bss.426564724";

    /**
     * 关联到的产品已上架，不能禁用
     */
    String ERR_MSG_BSS_1259909588 = "err.msg.bss.1259909588";

    /**
     * 钥匙扣！
     */
    String ERR_MSG_BSS_1154926354 = "err.msg.bss.1154926354";

    /**
     * 暂不支持当前云环境
     */
    String ERR_MSG_BSS_1411321550 = "err.msg.bss.1411321550";

    /**
     * 该用户没有绑定hcso
     */
    String ERR_MSG_BSS_1791307836 = "err.msg.bss.1791307836";

    /**
     * 计费周期只能包括：
     */
    String ERR_MSG_BSS_1771583025 = "err.msg.bss.1771583025";

    /**
     * 该支付方式未开通
     */
    String ERR_MSG_BSS_205602848 = "err.msg.bss.205602848";

    /**
     * 连接类型不支持.
     */
    String ERR_MSG_BSS_1612394054 = "err.msg.bss.1612394054";

    /**
     * 请选择项目负责人
     */
    String ERR_MSG_BSS_1080222154 = "err.msg.bss.1080222154";

    /**
     * 该企业已经被注册
     */
    String ERR_MSG_BSS_518484020 = "err.msg.bss.518484020";

    /**
     * 共享资源池变更异常
     */
    String ERR_MSG_BSS_1109359604 = "err.msg.bss.1109359604";

    /**
     * 虚拟IP已经绑定过该服务器，不能重复绑定
     */
    String ERR_MSG_BSS_789955873 = "err.msg.bss.789955873";

    /**
     * 部署上线暂不支持释放资源！
     */
    String ERR_MSG_BSS_1966439109 = "err.msg.bss.1966439109";

    /**
     * 不识别的操作类型:
     */
    String ERR_MSG_BSS_1913013847 = "err.msg.bss.1913013847";

    /**
     * 资源占用
     */
    String ERR_MSG_BSS_1105253524 = "err.msg.bss.1105253524";

    /**
     * 调用程序==空
     */
    String ERR_MSG_BSS_1428719633 = "err.msg.bss.1428719633";

    /**
     * 关联角色权限错误，不能关联分销商
     */
    String ERR_MSG_BSS_1814506067 = "err.msg.bss.1814506067";

    /**
     * 已经关联了该分销商,请勿重复关联
     */
    String ERR_MSG_BSS_758595482 = "err.msg.bss.758595482";

    /**
     * 所选择的配置过高,没有可用的主机。
     */
    String ERR_MSG_BSS_58537174 = "err.msg.bss.58537174";

    /**
     * 错误，请连接管理员
     */
    String ERR_MSG_BSS_1330637131 = "err.msg.bss.1330637131";

    /**
     * 弹性ip正在被绑定中,请稍后查看。
     */
    String ERR_MSG_BSS_161508477 = "err.msg.bss.161508477";

    /**
     * 值之间
     */
    String ERR_MSG_BSS_20398661 = "err.msg.bss.20398661";

    /**
     * 此用户已锁定，平台不支持此解锁用户方式, 请联系管理员激活用户
     */
    String ERR_MSG_BSS_1759464620 = "err.msg.bss.1759464620";

    /**
     * 网络数据异常，请先同步网络数据
     */
    String ERR_MSG_BSS_310494080 = "err.msg.bss.310494080";

    /**
     * 该网络有
     */
    String ERR_MSG_BSS_1099260089 = "err.msg.bss.1099260089";

    /**
     * 所有者ID
     */
    String ERR_MSG_BSS_1724109193 = "err.msg.bss.1724109193";

    /**
     * 公告时间不能小于当前时间
     */
    String ERR_MSG_BSS_1163425443 = "err.msg.bss.1163425443";

    /**
     * 该RDS实例不存在或已被删除
     */
    String ERR_MSG_BSS_465230689 = "err.msg.bss.465230689";

    /**
     * 申请采集修复成功
     */
    String ERR_MSG_BSS_598768079 = "err.msg.bss.598768079";

    /**
     * ，请修改。
     */
    String ERR_MSG_BSS_1256911884 = "err.msg.bss.1256911884";

    /**
     * 资源不存在！
     */
    String ERR_MSG_BSS_1245515984 = "err.msg.bss.1245515984";

    /**
     * 删除套餐包规格成功
     */
    String ERR_MSG_BSS_1139791 = "err.msg.bss.1139791";

    /**
     * 越权操作!
     */
    String ERR_MSG_BSS_83924935 = "err.msg.bss.83924935";

    /**
     * 审核，公司不存在
     */
    String ERR_MSG_BSS_1425935868 = "err.msg.bss.1425935868";

    /**
     * 删除访问密钥失败！
     */
    String ERR_MSG_BSS_1665617925 = "err.msg.bss.1665617925";

    /**
     * 无支持实例
     */
    String ERR_MSG_BSS_887595841 = "err.msg.bss.887595841";

    /**
     * 用户名小于最小长度配置
     */
    String ERR_MSG_BSS_128591980 = "err.msg.bss.128591980";

    /**
     * 当前应用无法执行此操作，请从自服务删除。
     */
    String ERR_MSG_BSS_1723906408 = "err.msg.bss.1723906408";

    /**
     * 查询结果为空，请核查数据
     */
    String ERR_MSG_BSS_1959945636 = "err.msg.bss.1959945636";

    /**
     * 虚拟IP未绑定过该服务器，不能执行解绑操作
     */
    String ERR_MSG_BSS_1812367153 = "err.msg.bss.1812367153";

    /**
     * 无此工单操作权限
     */
    String ERR_MSG_BSS_1623456656 = "err.msg.bss.1623456656";

    /**
     * 云环境没有找到
     */
    String ERR_MSG_BSS_1767929217 = "err.msg.bss.1767929217";

    /**
     * 平台不支持此解锁用戶方式, 请点击“忘记密码”激活或联系管理员
     */
    String ERR_MSG_BSS_1759260128 = "err.msg.bss.1759260128";

    /**
     * 结束时间和开始时间相差需在24小时之内!
     */
    String ERR_MSG_BSS_1665646475 = "err.msg.bss.1665646475";

    /**
     * 账户资源已冻结。
     */
    String ERR_MSG_BSS_1117764661 = "err.msg.bss.1117764661";

    /**
     * 查询用户[
     */
    String ERR_MSG_BSS_110498193 = "err.msg.bss.110498193";

    /**
     * 桶不存在，请先创建桶
     */
    String ERR_MSG_BSS_643883745 = "err.msg.bss.643883745";

    /**
     * FD认证信息验证失败, 错误信息为:
     */
    String ERR_MSG_BSS_1365137226 = "err.msg.bss.1365137226";

    /**
     * TenantUserName不存在
     */
    String ERR_MSG_BSS_1503890877 = "err.msg.bss.1503890877";

    /**
     * 主机实例状态不能绑定弹性ip！
     */
    String ERR_MSG_BSS_861598564 = "err.msg.bss.861598564";

    /**
     * 正在审批创建不支持的HPC版本
     */
    String ERR_MSG_BSS_1652980930 = "err.msg.bss.1652980930";

    /**
     * 没有查找到计算节点资源，请联系管理员检查计算节点资源配置。规格：[
     */
    String ERR_MSG_BSS_118120701 = "err.msg.bss.118120701";

    /**
     * 没有找到对应的用户
     */
    String ERR_MSG_BSS_100366398 = "err.msg.bss.100366398";

    /**
     * 该虚拟网卡不存在，请刷新重试
     */
    String ERR_MSG_BSS_1144896431 = "err.msg.bss.1144896431";

    /**
     * 当前客户信息异常！
     */
    String ERR_MSG_BSS_1391388302 = "err.msg.bss.1391388302";

    /**
     * 只有HPC专属扩容订单才支持继续扩容
     */
    String ERR_MSG_BSS_917741003 = "err.msg.bss.917741003";

    /**
     * 认证失败，请输入正确的身份证信息
     */
    String ERR_MSG_BSS_1449909042 = "err.msg.bss.1449909042";

    /**
     * 无权限操作其它运营实体产品
     */
    String ERR_MSG_BSS_35360333 = "err.msg.bss.35360333";

    /**
     * 续费资源已不存在
     */
    String ERR_MSG_BSS_424216285 = "err.msg.bss.424216285";

    /**
     * 】的续订，请勿重复操作
     */
    String ERR_MSG_BSS_44944859 = "err.msg.bss.44944859";

    /**
     * 导入文件格式不符合规范，点击下载Excel模板导入
     */
    String ERR_MSG_BSS_195883775 = "err.msg.bss.195883775";

    /**
     * 合同续订异常
     */
    String ERR_MSG_BSS_1088743311 = "err.msg.bss.1088743311";

    /**
     * 账户余额不足,审核失败
     */
    String ERR_MSG_BSS_1955839460 = "err.msg.bss.1955839460";

    /**
     * doReportByDay minDate为空
     */
    String ERR_MSG_BSS_512992870 = "err.msg.bss.512992870";

    /**
     * 存在已删除的用户，无法再次删除
     */
    String ERR_MSG_BSS_917257068 = "err.msg.bss.917257068";

    /**
     * 分区不存在或已被删除。
     */
    String ERR_MSG_BSS_1449926840 = "err.msg.bss.1449926840";

    /**
     * 暂不支持此操作!
     */
    String ERR_MSG_BSS_1659037141 = "err.msg.bss.1659037141";

    /**
     * 上传失败，上传数据为空
     */
    String ERR_MSG_BSS_1827679030 = "err.msg.bss.1827679030";

    /**
     * 当前账号不存在
     */
    String ERR_MSG_BSS_204509358 = "err.msg.bss.204509358";

    /**
     * 桶策略不存在，请先创建
     */
    String ERR_MSG_BSS_1763218300 = "err.msg.bss.1763218300";

    /**
     * 添加定时任务出错.
     */
    String ERR_MSG_BSS_1240063704 = "err.msg.bss.1240063704";

    /**
     * 专属资源池变更异常
     */
    String ERR_MSG_BSS_1859358309 = "err.msg.bss.1859358309";

    /**
     * 该账户未绑定对应的映射租户
     */
    String ERR_MSG_BSS_1312838344 = "err.msg.bss.1312838344";

    /**
     * 此操作不支持该云环境
     */
    String ERR_MSG_BSS_1706975220 = "err.msg.bss.1706975220";

    /**
     * 名称已存在
     */
    String ERR_MSG_BSS_649620001 = "err.msg.bss.649620001";

    /**
     * 充值账号错误!
     */
    String ERR_MSG_BSS_229162365 = "err.msg.bss.229162365";

    /**
     * 参数缺少驳回属性值，无法确定驳回到哪一个节点。请检查参数
     */
    String ERR_MSG_BSS_481504184 = "err.msg.bss.481504184";

    /**
     * 描述中不能包含符号*，请重新输入！
     */
    String ERR_MSG_BSS_1233614954 = "err.msg.bss.1233614954";

    /**
     * HCSO账户名重复
     */
    String ERR_MSG_BSS_2086767611 = "err.msg.bss.2086767611";

    /**
     * 您无法修改除子用户外其他角色的状态
     */
    String ERR_MSG_BSS_1655373924 = "err.msg.bss.1655373924";

    /**
     * 计费配置未找到!
     */
    String ERR_MSG_BSS_1410308734 = "err.msg.bss.1410308734";

    /**
     * 已在认证中，请勿重复提交
     */
    String ERR_MSG_BSS_1966210670 = "err.msg.bss.1966210670";

    /**
     * 请联系运营管理员，当前产品配置的资源与产品类型不匹配！
     */
    String ERR_MSG_BSS_1899767177 = "err.msg.bss.1899767177";

    /**
     * {}环境不存在
     */
    String ERR_MSG_BSS_947266489 = "err.msg.bss.947266489";

    /**
     * ]，不存在。
     */
    String ERR_MSG_BSS_845035308 = "err.msg.bss.845035308";

    /**
     * 云环境账号关联有
     */
    String ERR_MSG_BSS_22093102 = "err.msg.bss.22093102";

    /**
     * 文件大小超出限制，请重新上传
     */
    String ERR_MSG_BSS_526452970 = "err.msg.bss.526452970";

    /**
     * 创建用户密钥关联表失败！
     */
    String ERR_MSG_BSS_1017538125 = "err.msg.bss.1017538125";

    /**
     * 个云环境，不能删除！
     */
    String ERR_MSG_BSS_1768471420 = "err.msg.bss.1768471420";

    /**
     * 生效时间不能小于当前时间
     */
    String ERR_MSG_BSS_163544946 = "err.msg.bss.163544946";

    /**
     * 存在未使用的优惠券，请操作后再删除。
     */
    String ERR_MSG_BSS_1819064470 = "err.msg.bss.1819064470";

    /**
     * ，服务到期通知发送频率参数值必须为整数
     */
    String ERR_MSG_BSS_2061038514 = "err.msg.bss.2061038514";

    /**
     * 该用户已启用，请勿重复操作！
     */
    String ERR_MSG_BSS_2019516811 = "err.msg.bss.2019516811";

    /**
     * 资源池类型不能为空!
     */
    String ERR_MSG_BSS_231145715 = "err.msg.bss.231145715";

    /**
     * 策略在使用中，无法删除！
     */
    String ERR_MSG_BSS_1627320505 = "err.msg.bss.1627320505";

    /**
     * 获取Ldap认证配置失败，请联系系统管理员确认Ldap认证配置是否正确。
     */
    String ERR_MSG_BSS_2052376320 = "err.msg.bss.2052376320";

    /**
     * 用户密码验证成功
     */
    String ERR_MSG_BSS_589481550 = "err.msg.bss.589481550";

    /**
     * 不匹配的文件系统！
     */
    String ERR_MSG_BSS_832409397 = "err.msg.bss.832409397";

    /**
     * 不支持的远程连接方式
     */
    String ERR_MSG_BSS_1509037201 = "err.msg.bss.1509037201";

    /**
     * 账号id不合法
     */
    String ERR_MSG_BSS_139667282 = "err.msg.bss.139667282";

    /**
     * 未找到云环境，请尝试刷新！
     */
    String ERR_MSG_BSS_807649884 = "err.msg.bss.807649884";

    /**
     * 购买数量{
     */
    String ERR_MSG_BSS_358450119 = "err.msg.bss.358450119";

    /**
     * 不存在的可用区
     */
    String ERR_MSG_BSS_1203523238 = "err.msg.bss.1203523238";

    /**
     * 流程节点未找到
     */
    String ERR_MSG_BSS_462777243 = "err.msg.bss.462777243";

    /**
     * 通知策略不存在
     */
    String ERR_MSG_BSS_2026036803 = "err.msg.bss.2026036803";

    /**
     * 用户存在未结束HPC任务
     */
    String ERR_MSG_BSS_1729904842 = "err.msg.bss.1729904842";

    /**
     * 当前登录用户不存在
     */
    String ERR_MSG_BSS_1365243238 = "err.msg.bss.1365243238";

    /**
     * 批量导入失败，请检查上传文件格式内容。
     */
    String ERR_MSG_BSS_244314541 = "err.msg.bss.244314541";

    /**
     * 当前功能模块不存在！
     */
    String ERR_MSG_BSS_284631666 = "err.msg.bss.284631666";

    /**
     * 类型错误.
     */
    String ERR_MSG_BSS_70509704 = "err.msg.bss.70509704";

    /**
     * 当前用户未关联账户，无法查询发票
     */
    String ERR_MSG_BSS_1945067902 = "err.msg.bss.1945067902";

    /**
     * 发票状态修改成功
     */
    String ERR_MSG_BSS_1303424988 = "err.msg.bss.1303424988";

    /**
     * 云环境
     */
    String ERR_MSG_BSS_20269317 = "err.msg.bss.20269317";

    /**
     * 收缩ID为空
     */
    String ERR_MSG_BSS_1246539506 = "err.msg.bss.1246539506";

    /**
     * 该HCSO账户下没有对应用户组，请确认后再试
     */
    String ERR_MSG_BSS_368204001 = "err.msg.bss.368204001";

    /**
     * 创建失败，资源组名称已存在
     */
    String ERR_MSG_BSS_915604544 = "err.msg.bss.915604544";

    /**
     * 未知的HTTP方法名称：
     */
    String ERR_MSG_BSS_2050573590 = "err.msg.bss.2050573590";

    /**
     * 续费类型错误
     */
    String ERR_MSG_BSS_1760477614 = "err.msg.bss.1760477614";

    /**
     * 不支持当前cloudEnvType。
     */
    String ERR_MSG_BSS_959786231 = "err.msg.bss.959786231";

    /**
     * 不能对同一用户组中的用户进行操作
     */
    String ERR_MSG_BSS_1382757922 = "err.msg.bss.1382757922";

    /**
     * 已挂载主机,请先卸载
     */
    String ERR_MSG_BSS_2089099606 = "err.msg.bss.2089099606";

    /**
     * 组织Id为空
     */
    String ERR_MSG_BSS_1271298302 = "err.msg.bss.1271298302";

    /**
     * 弹性文件服务未配置计费
     */
    String ERR_MSG_BSS_1472922318 = "err.msg.bss.1472922318";

    /**
     * 套餐包订单正在下载，请稍后查询下载任务！
     */
    String ERR_MSG_BSS_737296150 = "err.msg.bss.737296150";

    /**
     * 关联的快照未删除完成
     */
    String ERR_MSG_BSS_1969744557 = "err.msg.bss.1969744557";

    /**
     * 无法识别代码：
     */
    String ERR_MSG_BSS_1377129918 = "err.msg.bss.1377129918";

    /**
     * Id：{}
     */
    String ERR_MSG_BSS_133159137 = "err.msg.bss.133159137";

    /**
     * 解析模块配置失败！
     */
    String ERR_MSG_BSS_1963831863 = "err.msg.bss.1963831863";

    /**
     * 云账号ID不能为空
     */
    String ERR_MSG_BSS_1697976307 = "err.msg.bss.1697976307";

    /**
     * 弹性ip绑定的资源实例不存在或已被删除
     */
    String ERR_MSG_BSS_2137183755 = "err.msg.bss.2137183755";

    /**
     * 正在进行Ldap账号同步，请勿重复操作
     */
    String ERR_MSG_BSS_1722867204 = "err.msg.bss.1722867204";

    /**
     * 生成异常：收支明细为空！
     */
    String ERR_MSG_BSS_205376413 = "err.msg.bss.205376413";

    /**
     * 虚拟网卡绑定失败！
     */
    String ERR_MSG_BSS_76807247 = "err.msg.bss.76807247";

    /**
     * 等待流程审核结束才可操作，编号
     */
    String ERR_MSG_BSS_1082716371 = "err.msg.bss.1082716371";

    /**
     * 解冻用户LDAP同步异常，请重试
     */
    String ERR_MSG_BSS_73591888 = "err.msg.bss.73591888";

    /**
     * 邮箱前缀4-16个字符
     */
    String ERR_MSG_BSS_1359978533 = "err.msg.bss.1359978533";

    /**
     * 找不到
     */
    String ERR_MSG_BSS_24871105 = "err.msg.bss.24871105";

    /**
     * ！
     */
    String ERR_MSG_BSS_65281 = "err.msg.bss.65281";

    /**
     * 折扣已经启用，无法删除
     */
    String ERR_MSG_BSS_921195612 = "err.msg.bss.921195612";

    /**
     * 活动还未开始,现金券暂无法使用
     */
    String ERR_MSG_BSS_1969113451 = "err.msg.bss.1969113451";

    /**
     * 您无法对自身进行权限配置
     */
    String ERR_MSG_BSS_1765545348 = "err.msg.bss.1765545348";

    /**
     * mac地址已经存在.
     */
    String ERR_MSG_BSS_800302366 = "err.msg.bss.800302366";

    /**
     * 主机实例底层资源已不存在，请重新选择
     */
    String ERR_MSG_BSS_1009405250 = "err.msg.bss.1009405250";

    /**
     * 提交成功
     */
    String ERR_MSG_BSS_781365859 = "err.msg.bss.781365859";

    /**
     * 资源已过期
     */
    String ERR_MSG_BSS_94040994 = "err.msg.bss.94040994";

    /**
     * 策略不能为空
     */
    String ERR_MSG_BSS_311126367 = "err.msg.bss.311126367";

    /**
     * 用户正在企业认证中,暂不支持购买产品
     */
    String ERR_MSG_BSS_1323473283 = "err.msg.bss.1323473283";

    /**
     * 暂不支持当前云环境的RDS实例相关操作
     */
    String ERR_MSG_BSS_1181304578 = "err.msg.bss.1181304578";

    /**
     * resourceId={}
     */
    String ERR_MSG_BSS_958771702 = "err.msg.bss.958771702";

    /**
     * 该目录容量不足,请重新分配
     */
    String ERR_MSG_BSS_316347997 = "err.msg.bss.316347997";

    /**
     * IAM自定义权限策略校验异常，请重试。
     */
    String ERR_MSG_BSS_534209134 = "err.msg.bss.534209134";

    /**
     * 未填写收件人，请填写后重试
     */
    String ERR_MSG_BSS_263692571 = "err.msg.bss.263692571";

    /**
     * 只能作废未分发的现金券.
     */
    String ERR_MSG_BSS_806482332 = "err.msg.bss.806482332";

    /**
     * 提交申请过快,请稍后重试
     */
    String ERR_MSG_BSS_898537936 = "err.msg.bss.898537936";

    /**
     * 只有租户管理员可以退订服务
     */
    String ERR_MSG_BSS_899185304 = "err.msg.bss.899185304";

    /**
     * 个IP正在使用，无法删除
     */
    String ERR_MSG_BSS_1371897254 = "err.msg.bss.1371897254";

    /**
     * 环节层级错误
     */
    String ERR_MSG_BSS_1025170802 = "err.msg.bss.1025170802";

    /**
     * 验证过于频繁，请稍后在操作
     */
    String ERR_MSG_BSS_963362591 = "err.msg.bss.963362591";

    /**
     * 开始时间不能空！
     */
    String ERR_MSG_BSS_258237792 = "err.msg.bss.258237792";

    /**
     * 请填写正确的关联合同
     */
    String ERR_MSG_BSS_1156181913 = "err.msg.bss.1156181913";

    /**
     * 跨度==空
     */
    String ERR_MSG_BSS_88139684 = "err.msg.bss.88139684";

    /**
     * 删除用户密钥关联表失败！
     */
    String ERR_MSG_BSS_1293201544 = "err.msg.bss.1293201544";

    /**
     * 未成功获取到实例变更成功标志，请稍后同步实例查看。
     */
    String ERR_MSG_BSS_1985067670 = "err.msg.bss.1985067670";

    /**
     * 没有找到账号
     */
    String ERR_MSG_BSS_941531403 = "err.msg.bss.941531403";

    /**
     * 该手机号已被使用
     */
    String ERR_MSG_BSS_1006327739 = "err.msg.bss.1006327739";

    /**
     * 不能为null
     */
    String ERR_MSG_BSS_1079430193 = "err.msg.bss.1079430193";

    /**
     * 群集为空！
     */
    String ERR_MSG_BSS_1282356383 = "err.msg.bss.1282356383";

    /**
     * 包年包月规格定价金额不能为负数且小数位数不能超过2位
     */
    String ERR_MSG_BSS_239235683 = "err.msg.bss.239235683";

    /**
     * 该规则集已关联防火墙，请先删除防火墙！
     */
    String ERR_MSG_BSS_2108940137 = "err.msg.bss.2108940137";

    /**
     * [%s]规则不匹配
     */
    String ERR_MSG_BSS_706889576 = "err.msg.bss.706889576";

    /**
     * 当前用户无操作权限!
     */
    String ERR_MSG_BSS_664331278 = "err.msg.bss.664331278";

    /**
     * 认证失败，用户SID信息不能为空
     */
    String ERR_MSG_BSS_1000949214 = "err.msg.bss.1000949214";

    /**
     * 私有网络/子网不可为空
     */
    String ERR_MSG_BSS_553175167 = "err.msg.bss.553175167";

    /**
     * ，作业组名称=
     */
    String ERR_MSG_BSS_1020705504 = "err.msg.bss.1020705504";

    /**
     * 会话过期或未准备好
     */
    String ERR_MSG_BSS_1718471341 = "err.msg.bss.1718471341";

    /**
     * 创建访问密钥失败！
     */
    String ERR_MSG_BSS_1401030560 = "err.msg.bss.1401030560";

    /**
     * 用户余额不足，审批失败。
     */
    String ERR_MSG_BSS_881668963 = "err.msg.bss.881668963";

    /**
     * url不能为空
     */
    String ERR_MSG_BSS_454851071 = "err.msg.bss.454851071";

    /**
     * 工单未找到
     */
    String ERR_MSG_BSS_1385551884 = "err.msg.bss.1385551884";

    /**
     * 重新冻结失败
     */
    String ERR_MSG_BSS_1937238897 = "err.msg.bss.1937238897";

    /**
     * 存在未使用的信用额度，请操作后再删除。
     */
    String ERR_MSG_BSS_398515668 = "err.msg.bss.398515668";

    /**
     * 名称：
     */
    String ERR_MSG_BSS_21710839 = "err.msg.bss.21710839";

    /**
     * 系统异常，请联系管理员
     */
    String ERR_MSG_BSS_1679285401 = "err.msg.bss.1679285401";

    /**
     * 分组不存在修改
     */
    String ERR_MSG_BSS_1003759958 = "err.msg.bss.1003759958";

    /**
     * 不能修改admin用户信息
     */
    String ERR_MSG_BSS_193451409 = "err.msg.bss.193451409";

    /**
     * [%s]不可编辑
     */
    String ERR_MSG_BSS_2139930831 = "err.msg.bss.2139930831";
    /*
     * [%s]参数类型异常
     */
    String ERR_MSG_BSS_2139930832 = "err.msg.bss.2139930832";

    /**
     * 组织
     */
    String ERR_MSG_BSS_1038467 = "err.msg.bss.1038467";

    /**
     * 存在未使用的现金券，请操作后再删除。
     */
    String ERR_MSG_BSS_100187311 = "err.msg.bss.100187311";

    /**
     * 范围值最大值超过限制值
     */
    String ERR_MSG_BSS_361663018 = "err.msg.bss.361663018";

    /**
     * 扩容合同不能为空
     */
    String ERR_MSG_BSS_175969668 = "err.msg.bss.175969668";

    /**
     * 创建硬盘任务准备中
     */
    String ERR_MSG_BSS_944064507 = "err.msg.bss.944064507";

    /**
     * 该实例不存在,请刷新后重试
     */
    String ERR_MSG_BSS_1239411815 = "err.msg.bss.1239411815";

    /**
     * 修改失败，用户姓名格式不正确
     */
    String ERR_MSG_BSS_2099493327 = "err.msg.bss.2099493327";

    /**
     * 解析云环境类型配置失败！
     */
    String ERR_MSG_BSS_1678840636 = "err.msg.bss.1678840636";

    /**
     * 未查询到该账户
     */
    String ERR_MSG_BSS_1949911565 = "err.msg.bss.1949911565";

    /**
     * 短信url未通过验证
     */
    String ERR_MSG_BSS_1814710801 = "err.msg.bss.1814710801";

    /**
     * 账户id映射不存在
     */
    String ERR_MSG_BSS_1594992141 = "err.msg.bss.1594992141";

    /**
     * 重新扩容回退节点流程失败
     */
    String ERR_MSG_BSS_2138973962 = "err.msg.bss.2138973962";

    /**
     * HPC资源分析填充图正在下载，请稍后查询下载任务！
     */
    String ERR_MSG_BSS_929529942 = "err.msg.bss.929529942";

    /**
     * 用户不合法
     */
    String ERR_MSG_BSS_1597597653 = "err.msg.bss.1597597653";

    /**
     * 获取登录用户失败
     */
    String ERR_MSG_BSS_42304676 = "err.msg.bss.42304676";

    /**
     * 存在启用中的折扣，删除失败！
     */
    String ERR_MSG_BSS_1885667064 = "err.msg.bss.1885667064";

    /**
     * 所选账单明细超过三个月!
     */
    String ERR_MSG_BSS_497753068 = "err.msg.bss.497753068";

    /**
     * 不支持部署类型！
     */
    String ERR_MSG_BSS_1794717784 = "err.msg.bss.1794717784";

    /**
     * 客户未启用，创建策略失败
     */
    String ERR_MSG_BSS_1602274720 = "err.msg.bss.1602274720";

    /**
     * 用户列表正在生成，请稍后下载！
     */
    String ERR_MSG_BSS_1680443309 = "err.msg.bss.1680443309";

    /**
     * 当前用户不合法，请重试
     */
    String ERR_MSG_BSS_923351580 = "err.msg.bss.923351580";

    /**
     * 周期必须大于0!
     */
    String ERR_MSG_BSS_450952005 = "err.msg.bss.450952005";

    /**
     * 当前用户无权操作
     */
    String ERR_MSG_BSS_1762351419 = "err.msg.bss.1762351419";

    /**
     * 该监听器不存在
     */
    String ERR_MSG_BSS_550017963 = "err.msg.bss.550017963";

    /**
     * 未配置短信平台,刷新失败
     */
    String ERR_MSG_BSS_73634429 = "err.msg.bss.73634429";

    /**
     * 工单id错误
     */
    String ERR_MSG_BSS_804784543 = "err.msg.bss.804784543";

    /**
     * 云账号信息未找到！
     */
    String ERR_MSG_BSS_1020531595 = "err.msg.bss.1020531595";

    /**
     * 服务实例不存在或已被删除
     */
    String ERR_MSG_BSS_2008897911 = "err.msg.bss.2008897911";

    /**
     * 用户认证信息未找到！
     */
    String ERR_MSG_BSS_1255531007 = "err.msg.bss.1255531007";

    /**
     * 该账号没有使用权限
     */
    String ERR_MSG_BSS_435456756 = "err.msg.bss.435456756";

    /**
     * 权限标识无效
     */
    String ERR_MSG_BSS_1391669964 = "err.msg.bss.1391669964";

    /**
     * 合同已终止，请勿重复终止！
     */
    String ERR_MSG_BSS_1370242523 = "err.msg.bss.1370242523";

    /**
     * 当前产品未关联当前运营实体
     */
    String ERR_MSG_BSS_218235077 = "err.msg.bss.218235077";

    /**
     * 用户密码验证失败
     */
    String ERR_MSG_BSS_589426131 = "err.msg.bss.589426131";

    /**
     * 流程模板名称已存在
     */
    String ERR_MSG_BSS_705716983 = "err.msg.bss.705716983";

    /**
     * 添加消费记录失败，当前项目账户的余额不足！
     */
    String ERR_MSG_BSS_1879611536 = "err.msg.bss.1879611536";

    /**
     * 构建扫描对象出错。
     */
    String ERR_MSG_BSS_43884543 = "err.msg.bss.43884543";

    /**
     * 服务不存在
     */
    String ERR_MSG_BSS_756278295 = "err.msg.bss.756278295";

    /**
     * 碱基缺失
     */
    String ERR_MSG_BSS_943128672 = "err.msg.bss.943128672";

    /**
     * HCSO IAM
     */
    String ERR_MSG_BSS_1583267028 = "err.msg.bss.1583267028";

    /**
     * 当前操作消息通知不存在
     */
    String ERR_MSG_BSS_1128534018 = "err.msg.bss.1128534018";

    /**
     * 产品参数异常！
     */
    String ERR_MSG_BSS_344196835 = "err.msg.bss.344196835";

    /**
     * 订单状态异常
     */
    String ERR_MSG_BSS_301291316 = "err.msg.bss.301291316";

    /**
     * 申请节点只允许通过
     */
    String ERR_MSG_BSS_889360627 = "err.msg.bss.889360627";

    /**
     * Redis连接失败。
     */
    String ERR_MSG_BSS_483626228 = "err.msg.bss.483626228";

    /**
     * 请选择要绑定的弹性IP
     */
    String ERR_MSG_BSS_1038546822 = "err.msg.bss.1038546822";

    /**
     * 不能以admin开头
     */
    String ERR_MSG_BSS_880033842 = "err.msg.bss.880033842";

    /**
     * ID={}resourceId
     */
    String ERR_MSG_BSS_1987828179 = "err.msg.bss.1987828179";

    /**
     * 作业操作
     */
    String ERR_MSG_BSS_625268461 = "err.msg.bss.625268461";

    /**
     * 初始密码不正确
     */
    String ERR_MSG_BSS_1002902223 = "err.msg.bss.1002902223";

    /**
     * 未找到对应的装机平台
     */
    String ERR_MSG_BSS_599712321 = "err.msg.bss.599712321";

    /**
     * 不支持短信提供商
     */
    String ERR_MSG_BSS_520359848 = "err.msg.bss.520359848";

    /**
     * 删除失败
     */
    String ERR_MSG_BSS_664068440 = "err.msg.bss.664068440";

    /**
     * 管理地址格式有误！
     */
    String ERR_MSG_BSS_1916633517 = "err.msg.bss.1916633517";

    /**
     * chargeType无效
     */
    String ERR_MSG_BSS_1301847030 = "err.msg.bss.1301847030";

    /**
     * 当前安全组无法执行此操作，请解绑实例后再删除
     */
    String ERR_MSG_BSS_2140780311 = "err.msg.bss.2140780311";

    /**
     * 业务数据不存在！
     */
    String ERR_MSG_BSS_488087415 = "err.msg.bss.488087415";

    /**
     * VPC含关联子网，不得进行删除操作
     */
    String ERR_MSG_BSS_409080694 = "err.msg.bss.409080694";

    /**
     * 网络已删除，请刷新重试
     */
    String ERR_MSG_BSS_1804661911 = "err.msg.bss.1804661911";

    /**
     * 下载任务不存在
     */
    String ERR_MSG_BSS_973386427 = "err.msg.bss.973386427";

    /**
     * 采集时间不能大于当前时间！
     */
    String ERR_MSG_BSS_1182969931 = "err.msg.bss.1182969931";

    /**
     * 计费策略未找到
     */
    String ERR_MSG_BSS_1149179445 = "err.msg.bss.1149179445";

    /**
     * 配置失败，该资源ID已被其他实例设置！
     */
    String ERR_MSG_BSS_1663963030 = "err.msg.bss.1663963030";

    /**
     * HPC专属资源池名称不符合规范，请重新输入！
     */
    String ERR_MSG_BSS_1915745700 = "err.msg.bss.1915745700";

    /**
     * 操作失败：
     */
    String ERR_MSG_BSS_1309982473 = "err.msg.bss.1309982473";

    /**
     * 不支持的购买时长
     */
    String ERR_MSG_BSS_649384401 = "err.msg.bss.649384401";

    /**
     * 账单明细正在下载，请稍后查询下载任务！
     */
    String ERR_MSG_BSS_662058302 = "err.msg.bss.662058302";

    /**
     * 只有已停止的实例才可以回滚磁盘
     */
    String ERR_MSG_BSS_1297087746 = "err.msg.bss.1297087746";

    /**
     * 资源类型无对应云环境
     */
    String ERR_MSG_BSS_722092998 = "err.msg.bss.722092998";

    /**
     * 硬盘关联有
     */
    String ERR_MSG_BSS_667021700 = "err.msg.bss.667021700";

    /**
     * 请输入正确的手机号码
     */
    String ERR_MSG_BSS_285305065 = "err.msg.bss.285305065";

    /**
     * )，最大支持客户数(
     */
    String ERR_MSG_BSS_943228649 = "err.msg.bss.943228649";

    /**
     * 组织层级已超过上限，无法创建。
     */
    String ERR_MSG_BSS_1557390068 = "err.msg.bss.1557390068";

    /**
     * 服务未找到！
     */
    String ERR_MSG_BSS_1776479847 = "err.msg.bss.1776479847";

    /**
     * 该规格族存在配置使用，删除失败！
     */
    String ERR_MSG_BSS_753830327 = "err.msg.bss.753830327";

    /**
     * 找不到堡垒机信息！
     */
    String ERR_MSG_BSS_492838315 = "err.msg.bss.492838315";

    /**
     * 禁用失败
     */
    String ERR_MSG_BSS_956215323 = "err.msg.bss.956215323";

    /**
     * 用户不存在
     */
    String ERR_MSG_BSS_1597545170 = "err.msg.bss.1597545170";

    /**
     * 审批任务不存在或者已经被其他人处理
     */
    String ERR_MSG_BSS_1795942660 = "err.msg.bss.1795942660";

    /**
     * ]
     */
    String ERR_MSG_BSS_93 = "err.msg.bss.93";

    /**
     * [
     */
    String ERR_MSG_BSS_91 = "err.msg.bss.91";

    /**
     * 信息有误，请确认
     */
    String ERR_MSG_BSS_206079445 = "err.msg.bss.206079445";

    /**
     * 许可证已失效！
     */
    String ERR_MSG_BSS_522979042 = "err.msg.bss.522979042";

    /**
     * 不能以test开头
     */
    String ERR_MSG_BSS_452404517 = "err.msg.bss.452404517";

    /**
     * 复制后的长度超过模板名称长度限制
     */
    String ERR_MSG_BSS_1717012886 = "err.msg.bss.1717012886";

    /**
     * 禁止修改当前用户的角色
     */
    String ERR_MSG_BSS_324563825 = "err.msg.bss.324563825";

    /**
     * 上传路径不正确
     */
    String ERR_MSG_BSS_959970317 = "err.msg.bss.959970317";

    /**
     * 未找到资源
     */
    String ERR_MSG_BSS_607095064 = "err.msg.bss.607095064";

    /**
     * 参数不能为空！
     */
    String ERR_MSG_BSS_1543305155 = "err.msg.bss.1543305155";

    /**
     * 文件大小过大，请重新上传
     */
    String ERR_MSG_BSS_1060900453 = "err.msg.bss.1060900453";

    /**
     * 项目ID不存在！
     */
    String ERR_MSG_BSS_1097139596 = "err.msg.bss.1097139596";

    /**
     * 无审批角色
     */
    String ERR_MSG_BSS_960215624 = "err.msg.bss.960215624";

    /**
     * 未找到父级部门
     */
    String ERR_MSG_BSS_496009997 = "err.msg.bss.496009997";

    /**
     * 公有云续费至少一个月起
     */
    String ERR_MSG_BSS_1341819753 = "err.msg.bss.1341819753";

    /**
     * 节点类型错误
     */
    String ERR_MSG_BSS_574575453 = "err.msg.bss.574575453";

    /**
     * 调用CCP删除账户失败
     */
    String ERR_MSG_BSS_1085196812 = "err.msg.bss.1085196812";

    /**
     * 请联系运营管理员检查IAM是否存在ModelArts子账户！
     */
    String ERR_MSG_BSS_242166146 = "err.msg.bss.242166146";

    /**
     * 您的权限不足！请联系企业管理员添加功能及服务权限。
     */
    String ERR_MSG_BSS_465687352 = "err.msg.bss.465687352";

    /**
     * 邮箱格式不符合规则！
     */
    String ERR_MSG_BSS_981668819 = "err.msg.bss.981668819";

    /**
     * 扩容计算节点数不能小于1
     */
    String ERR_MSG_BSS_1808479377 = "err.msg.bss.1808479377";

    /**
     * 用户不存在!
     */
    String ERR_MSG_BSS_2015707315 = "err.msg.bss.2015707315";

    /**
     * 该弹性文件不存在,请刷新后重新
     */
    String ERR_MSG_BSS_182954958 = "err.msg.bss.182954958";

    /**
     * 最大客户数量默认值不合法
     */
    String ERR_MSG_BSS_1005303035 = "err.msg.bss.1005303035";

    /**
     * 只有当快照状态为“可用”，且该快照源磁盘状态为“可用”或“回滚数据失败”时，才可以用快照回滚磁盘数据
     */
    String ERR_MSG_BSS_1378960977 = "err.msg.bss.1378960977";

    /**
     * 更新合同模板参数!
     */
    String ERR_MSG_BSS_2097652341 = "err.msg.bss.2097652341";

    /**
     * 服务器异常,请联系管理员
     */
    String ERR_MSG_BSS_609387497 = "err.msg.bss.609387497";

    /**
     * 云环境认证失败
     */
    String ERR_MSG_BSS_609387498 = "err.msg.bss.609387498";

    /**
     * rpcTracing==null
     */
    String ERR_MSG_BSS_704895356 = "err.msg.bss.704895356";

    /**
     * 请求==空
     */
    String ERR_MSG_BSS_443337777 = "err.msg.bss.443337777";

    /**
     * 测试失败！
     */
    String ERR_MSG_BSS_1158946627 = "err.msg.bss.1158946627";

    /**
     * 参数异常
     */
    String ERR_MSG_BSS_664511972 = "err.msg.bss.664511972";

    /**
     * 分销商账号id异常
     */
    String ERR_MSG_BSS_662451026 = "err.msg.bss.662451026";

    /**
     * 短信发送成功
     */
    String ERR_MSG_BSS_855933043 = "err.msg.bss.855933043";

    /**
     * 挂载点不存在
     */
    String ERR_MSG_BSS_228574591 = "err.msg.bss.228574591";

    /**
     * 已分配状态下的工单才允许此操作
     */
    String ERR_MSG_BSS_581845916 = "err.msg.bss.581845916";

    /**
     * 找不到字段[
     */
    String ERR_MSG_BSS_2070927236 = "err.msg.bss.2070927236";

    /**
     * 删除BMS白名单失败
     */
    String ERR_MSG_BSS_11545387 = "err.msg.bss.11545387";

    /**
     * 云账号未找到
     */
    String ERR_MSG_BSS_30193926 = "err.msg.bss.30193926";

    /**
     * 确认导入环境的云环境是否准备完成
     */
    String ERR_MSG_BSS_980148496 = "err.msg.bss.980148496";

    /**
     * 下载失败!请联系管理员处理
     */
    String ERR_MSG_BSS_1234800146 = "err.msg.bss.1234800146";

    /**
     * Qos配置解析异常
     */
    String ERR_MSG_BSS_305976391 = "err.msg.bss.305976391";

    /**
     * 没有解锁
     */
    String ERR_MSG_BSS_854938630 = "err.msg.bss.854938630";

    /**
     * 邮政编码不能为空
     */
    String ERR_MSG_BSS_1143981108 = "err.msg.bss.1143981108";

    /**
     * 当前新环境未接入许可证,请接入许可证后操作
     */
    String ERR_MSG_BSS_1377670824 = "err.msg.bss.1377670824";

    /**
     * 周期不能为空!
     */
    String ERR_MSG_BSS_2000742214 = "err.msg.bss.2000742214";

    /**
     * ccsp mac验证失败，类为
     */
    String ERR_MSG_BSS_1435979242 = "err.msg.bss.1435979242";

    /**
     * 密码包含规则不符合规则！
     */
    String ERR_MSG_BSS_442879469 = "err.msg.bss.442879469";

    /**
     * 该存储类型中的存储分配率皆已超过设定的分配阀值，无法分配资源。
     */
    String ERR_MSG_BSS_1834405161 = "err.msg.bss.1834405161";

    /**
     * 当前账户已被冻结,申请退订服务失败
     */
    String ERR_MSG_BSS_1341441118 = "err.msg.bss.1341441118";

    /**
     * 国密服务不可重复开启！
     */
    String ERR_MSG_BSS_880218477 = "err.msg.bss.880218477";

    /**
     * 文件路径为空！
     */
    String ERR_MSG_BSS_1967995645 = "err.msg.bss.1967995645";

    /**
     * ID={}，EVS
     */
    String ERR_MSG_BSS_957980006 = "err.msg.bss.957980006";

    /**
     * 优惠券已被使用,请选择其他优惠券.
     */
    String ERR_MSG_BSS_1219130789 = "err.msg.bss.1219130789";

    /**
     * 未找到对应的计费策略，话单规格为:{}
     */
    String ERR_MSG_BSS_1451876950 = "err.msg.bss.1451876950";

    /**
     * 服务实例已不存在，无法退订
     */
    String ERR_MSG_BSS_240065513 = "err.msg.bss.240065513";

    /**
     * HPC共享文件系统共享目录配置异常！
     */
    String ERR_MSG_BSS_279831393 = "err.msg.bss.279831393";

    /**
     * 无法对发票重复审核
     */
    String ERR_MSG_BSS_1833656946 = "err.msg.bss.1833656946";

    /**
     * 本地压缩文件失败
     */
    String ERR_MSG_BSS_1539452197 = "err.msg.bss.1539452197";

    /**
     * 登录节点为空
     */
    String ERR_MSG_BSS_1440187535 = "err.msg.bss.1440187535";

    /**
     * 不能删除正在管理虚机的亲和组/反亲和组
     */
    String ERR_MSG_BSS_556599656 = "err.msg.bss.556599656";

    /**
     * GPU资源组已经存在
     */
    String ERR_MSG_BSS_1091902233 = "err.msg.bss.1091902233";

    /**
     * 发送错误
     */
    String ERR_MSG_BSS_675871814 = "err.msg.bss.675871814";

    /**
     * 实例关联宿主机不存在
     */
    String ERR_MSG_BSS_269685843 = "err.msg.bss.269685843";

    /**
     * 时间转换异常
     */
    String ERR_MSG_BSS_2129757398 = "err.msg.bss.2129757398";

    /**
     * 系统内置用户组不可删除
     */
    String ERR_MSG_BSS_266746898 = "err.msg.bss.266746898";

    /**
     * 云环境存在使用的关联服务，无法删除。
     */
    String ERR_MSG_BSS_1928051704 = "err.msg.bss.1928051704";

    /**
     * 配置HPC通行证失败！
     */
    String ERR_MSG_BSS_1650457560 = "err.msg.bss.1650457560";

    /**
     * 当前产品配置的资源与产品类型不匹配，请检查后重试！
     */
    String ERR_MSG_BSS_857666303 = "err.msg.bss.857666303";

    /**
     * ，没有配置服务到期时间参数
     */
    String ERR_MSG_BSS_1979956004 = "err.msg.bss.1979956004";

    /**
     * 该区域资源类型已不存在或被删除，请刷新重试！
     */
    String ERR_MSG_BSS_1348369781 = "err.msg.bss.1348369781";

    /**
     * 节点删除中，请稍后操作！
     */
    String ERR_MSG_BSS_1034666260 = "err.msg.bss.1034666260";

    /**
     * 您无法修改比自身账号级别高的账号状态
     */
    String ERR_MSG_BSS_443301334 = "err.msg.bss.443301334";

    /**
     * 云环境不支持此操作！
     */
    String ERR_MSG_BSS_1958679256 = "err.msg.bss.1958679256";

    /**
     * 无当前运营实体操作权限!
     */
    String ERR_MSG_BSS_1384912769 = "err.msg.bss.1384912769";

    /**
     * 请勿重复操作
     */
    String ERR_MSG_BSS_1404182793 = "err.msg.bss.1404182793";

    /**
     * 流程实例未找到
     */
    String ERR_MSG_BSS_394907995 = "err.msg.bss.394907995";

    /**
     * 订单数据正在下载，请稍后查询下载任务！
     */
    String ERR_MSG_BSS_1827394552 = "err.msg.bss.1827394552";

    /**
     * ]达到目标[
     */
    String ERR_MSG_BSS_1351581965 = "err.msg.bss.1351581965";

    /**
     * 专属容量区间异常
     */
    String ERR_MSG_BSS_783382929 = "err.msg.bss.783382929";

    /**
     * ModelArts存在没有退订的产品，请先退订产品！
     */
    String ERR_MSG_BSS_1556683373 = "err.msg.bss.1556683373";

    /**
     * s后重试
     */
    String ERR_MSG_BSS_25297635 = "err.msg.bss.25297635";

    /**
     * 模板名称已存在
     */
    String ERR_MSG_BSS_1688366303 = "err.msg.bss.1688366303";

    /**
     * 请求参数不能为空!
     */
    String ERR_MSG_BSS_1497645912 = "err.msg.bss.1497645912";

    /**
     * 账户已存在!请检查账号或者邮箱是否重复！
     */
    String ERR_MSG_BSS_2059484517 = "err.msg.bss.2059484517";

    /**
     * 、：
     */
    String ERR_MSG_BSS_446265 = "err.msg.bss.446265";

    /**
     * 堡垒机认证信息不存在，请联系管理员
     */
    String ERR_MSG_BSS_1729472120 = "err.msg.bss.1729472120";

    /**
     * 资源池类型异常
     */
    String ERR_MSG_BSS_1212784646 = "err.msg.bss.1212784646";

    /**
     * 请选择HTTPS协议证书！
     */
    String ERR_MSG_BSS_437405239 = "err.msg.bss.437405239";

    /**
     * 404
     */
    String ERR_MSG_BSS_51512 = "err.msg.bss.51512";

    /**
     * 误差
     */
    String ERR_MSG_BSS_1134559 = "err.msg.bss.1134559";

    /**
     * 快照的源磁盘处于挂载状态，不能执行此操作，请先卸载磁盘，再回滚数据。
     */
    String ERR_MSG_BSS_1784581745 = "err.msg.bss.1784581745";

    /**
     * 无权限查看该审批单,可尝试切换运营实体
     */
    String ERR_MSG_BSS_1420532533 = "err.msg.bss.1420532533";

    /**
     * 403
     */
    String ERR_MSG_BSS_51511 = "err.msg.bss.51511";

    /**
     * 该通知策略不存在
     */
    String ERR_MSG_BSS_1467148610 = "err.msg.bss.1467148610";

    /**
     * 输入端口参数不能重复!
     */
    String ERR_MSG_BSS_1625796157 = "err.msg.bss.1625796157";

    /**
     * 409
     */
    String ERR_MSG_BSS_51517 = "err.msg.bss.51517";

    /**
     * 用户已被冻结
     */
    String ERR_MSG_BSS_2146900544 = "err.msg.bss.2146900544";

    /**
     * 不能操作其他运营实体的账户！
     */
    String ERR_MSG_BSS_43534325 = "err.msg.bss.43534325";

    /**
     * 获取当前登录用户失败
     */
    String ERR_MSG_BSS_531007114 = "err.msg.bss.531007114";

    /**
     * 名称长度必须介于2-64字符之间！
     */
    String ERR_MSG_BSS_1611074317 = "err.msg.bss.1611074317";

    /**
     * 不属于当前审批角色，无审批权限
     */
    String ERR_MSG_BSS_274071896 = "err.msg.bss.274071896";

    /**
     * 该组织下没有用户。
     */
    String ERR_MSG_BSS_297654712 = "err.msg.bss.297654712";

    /**
     * 用户已欠费，不能提交，请提醒用户充值！
     */
    String ERR_MSG_BSS_77398982 = "err.msg.bss.77398982";

    /**
     * 请填写正确规格值!
     */
    String ERR_MSG_BSS_1611807411 = "err.msg.bss.1611807411";

    /**
     * 对应的流程申请未找到
     */
    String ERR_MSG_BSS_1445877125 = "err.msg.bss.1445877125";

    /**
     * 该工单模板正在使用中不允许删除！
     */
    String ERR_MSG_BSS_1131301691 = "err.msg.bss.1131301691";

    /**
     * 恢复任务出错.
     */
    String ERR_MSG_BSS_195630814 = "err.msg.bss.195630814";

    /**
     * 数字100！
     */
    String ERR_MSG_BSS_533878679 = "err.msg.bss.533878679";

    /**
     * 所填写的IP已经被占用，请检查
     */
    String ERR_MSG_BSS_1851713737 = "err.msg.bss.1851713737";

    /**
     * MA规格数据不存在
     */
    String ERR_MSG_BSS_1358142675 = "err.msg.bss.1358142675";

    /**
     * 选择的分区[
     */
    String ERR_MSG_BSS_1915067363 = "err.msg.bss.1915067363";

    /**
     * 冻结升级成功
     */
    String ERR_MSG_BSS_1701437625 = "err.msg.bss.1701437625";

    /**
     * 该弹性文件不存在,请刷新后再试
     */
    String ERR_MSG_BSS_183455081 = "err.msg.bss.183455081";

    /**
     * 收支明细数据正在生成，请稍后查询下载任务！
     */
    String ERR_MSG_BSS_1533476517 = "err.msg.bss.1533476517";

    /**
     * ]在堡垒机中不存在，请联系管理员
     */
    String ERR_MSG_BSS_1329696723 = "err.msg.bss.1329696723";

    /**
     * 公共组织ID配置项
     */
    String ERR_MSG_BSS_2072812053 = "err.msg.bss.2072812053";

    /**
     * 您的密码校验过于频繁，请三分钟后再试。
     */
    String ERR_MSG_BSS_1513418090 = "err.msg.bss.1513418090";

    /**
     * 硬盘名称不能重复
     */
    String ERR_MSG_BSS_1788848735 = "err.msg.bss.1788848735";

    /**
     * 查询身份提供商列表失败！
     */
    String ERR_MSG_BSS_473186480 = "err.msg.bss.473186480";

    /**
     * 无效云环境类型
     */
    String ERR_MSG_BSS_2084481997 = "err.msg.bss.2084481997";

    /**
     * 更新失败.
     */
    String ERR_MSG_BSS_630902306 = "err.msg.bss.630902306";

    /**
     * 开票中和已开票的关联资源未到期不能进行退订
     */
    String ERR_MSG_BSS_1151614270 = "err.msg.bss.1151614270";

    /**
     * 找不到数据
     */
    String ERR_MSG_BSS_1867841409 = "err.msg.bss.1867841409";

    /**
     * 只有已停止的主机才能转化为模板
     */
    String ERR_MSG_BSS_1919034828 = "err.msg.bss.1919034828";

    /**
     * '不存在
     */
    String ERR_MSG_BSS_21110806 = "err.msg.bss.21110806";

    /**
     * 该环境非RCLink环境
     */
    String ERR_MSG_BSS_1870393884 = "err.msg.bss.1870393884";

    /**
     * 已作废
     */
    String ERR_MSG_BSS_23766069 = "err.msg.bss.23766069";

    /**
     * 存在未使用的账户余额，请操作后再删除。
     */
    String ERR_MSG_BSS_655656655 = "err.msg.bss.655656655";

    /**
     * 状态不存在
     */
    String ERR_MSG_BSS_2102436274 = "err.msg.bss.2102436274";

    /**
     * 越权操作。
     */
    String ERR_MSG_BSS_83912678 = "err.msg.bss.83912678";

    /**
     * 组织层级超出参数范围！
     */
    String ERR_MSG_BSS_48179003 = "err.msg.bss.48179003";

    /**
     * 获取资源失败
     */
    String ERR_MSG_BSS_1570221985 = "err.msg.bss.1570221985";

    /**
     * 该负载均衡不存在
     */
    String ERR_MSG_BSS_1303452256 = "err.msg.bss.1303452256";

    /**
     * 该组织下存在子组织，请先删除子组织
     */
    String ERR_MSG_BSS_884688910 = "err.msg.bss.884688910";

    /**
     * 查询失败，请稍后重试。
     */
    String ERR_MSG_BSS_2046077437 = "err.msg.bss.2046077437";

    /**
     * 不能删除已上架产品关联的计费资源类型
     */
    String ERR_MSG_BSS_1543987437 = "err.msg.bss.1543987437";

    /**
     * 接口校验失败!
     */
    String ERR_MSG_BSS_1239685124 = "err.msg.bss.1239685124";

    /**
     * 当前操作用户不存在
     */
    String ERR_MSG_BSS_782004165 = "err.msg.bss.782004165";

    /**
     * 未获取到网络出口信息！
     */
    String ERR_MSG_BSS_2071027192 = "err.msg.bss.2071027192";

    /**
     * 当前账户已被冻结,申请服务失败
     */
    String ERR_MSG_BSS_729724604 = "err.msg.bss.729724604";

    /**
     * 该工单状态不允许删除！
     */
    String ERR_MSG_BSS_1856956711 = "err.msg.bss.1856956711";

    /**
     * obs对象密钥：
     */
    String ERR_MSG_BSS_1615706067 = "err.msg.bss.1615706067";

    /**
     * 获取业务流程关联数据失败
     */
    String ERR_MSG_BSS_630090301 = "err.msg.bss.630090301";

    /**
     * 连接成功！
     */
    String ERR_MSG_BSS_438324587 = "err.msg.bss.438324587";

    /**
     * 获取用户失败
     */
    String ERR_MSG_BSS_1757129758 = "err.msg.bss.1757129758";

    /**
     * 该弹性IP不支持修改带宽
     */
    String ERR_MSG_BSS_203554489 = "err.msg.bss.203554489";

    /**
     * 当前操作IP不存在
     */
    String ERR_MSG_BSS_1825668691 = "err.msg.bss.1825668691";

    /**
     * 操作成功!
     */
    String ERR_MSG_BSS_1308329757 = "err.msg.bss.1308329757";

    /**
     * 有创建的主机
     */
    String ERR_MSG_BSS_2072334373 = "err.msg.bss.2072334373";

    /**
     * 所填写的IP已经被占用，请确认是否有误。
     */
    String ERR_MSG_BSS_1358475563 = "err.msg.bss.1358475563";

    /**
     * 拒绝，公司不存在
     */
    String ERR_MSG_BSS_1087210640 = "err.msg.bss.1087210640";

    /**
     * 服务配置不完整，请联系管理员
     */
    String ERR_MSG_BSS_381022785 = "err.msg.bss.381022785";

    /**
     * keycloakrealm[{}]clientId[{}]
     */
    String ERR_MSG_BSS_1977257564 = "err.msg.bss.1977257564";

    /**
     * 所查询的桶不存在
     */
    String ERR_MSG_BSS_1991342802 = "err.msg.bss.1991342802";

    /**
     * ccsp验证失败，原因[无字段ccsp_mac]，类为
     */
    String ERR_MSG_BSS_711698427 = "err.msg.bss.711698427";

    /**
     * 暂不支持的云环境
     */
    String ERR_MSG_BSS_1579351490 = "err.msg.bss.1579351490";

    /**
     * 存在未结束任务,不可退订 HPC 服务
     */
    String ERR_MSG_BSS_2072178987 = "err.msg.bss.2072178987";

    /**
     * 创建子用户
     */
    String ERR_MSG_BSS_1315590976 = "err.msg.bss.1315590976";

    /**
     * 已同意用户协议，请勿重复操作!
     */
    String ERR_MSG_BSS_1184191871 = "err.msg.bss.1184191871";

    /**
     * 获取SFS信息失败:
     */
    String ERR_MSG_BSS_456994263 = "err.msg.bss.456994263";

    /**
     * 该产品模板不存在!
     */
    String ERR_MSG_BSS_2023641343 = "err.msg.bss.2023641343";

    /**
     * 同一来源调用频率过高，请
     */
    String ERR_MSG_BSS_507391690 = "err.msg.bss.507391690";

    /**
     * 无权修改
     */
    String ERR_MSG_BSS_803013550 = "err.msg.bss.803013550";

    /**
     * 正在进行集群【
     */
    String ERR_MSG_BSS_1793669188 = "err.msg.bss.1793669188";

    /**
     * 不能对主账号进行此操作
     */
    String ERR_MSG_BSS_1793407489 = "err.msg.bss.1793407489";

    /**
     * 无权限操作该资源类型
     */
    String ERR_MSG_BSS_599580133 = "err.msg.bss.599580133";

    /**
     * 该负载均衡器名称已存在
     */
    String ERR_MSG_BSS_19951828 = "err.msg.bss.19951828";

    /**
     * 该功能未经许可，请联系管理员升级许可证后进行操作！
     */
    String ERR_MSG_BSS_613976347 = "err.msg.bss.613976347";

    /**
     * 实例不存在或已被删除
     */
    String ERR_MSG_BSS_1256686269 = "err.msg.bss.1256686269";

    /**
     * 余额不足,请充值后使用相关服务
     */
    String ERR_MSG_BSS_1433561286 = "err.msg.bss.1433561286";

    /**
     * 已拒绝的客户，不能创建折扣!
     */
    String ERR_MSG_BSS_1232434569 = "err.msg.bss.1232434569";

    /**
     * 文件格式不符合，请上传合规文件格式
     */
    String ERR_MSG_BSS_100905708 = "err.msg.bss.100905708";

    /**
     * 请启用租户后,再次进行操作
     */
    String ERR_MSG_BSS_1287360922 = "err.msg.bss.1287360922";

    /**
     * 新增套餐包规格成功
     */
    String ERR_MSG_BSS_2062749797 = "err.msg.bss.2062749797";

    /**
     * 未找到审批任务
     */
    String ERR_MSG_BSS_316315162 = "err.msg.bss.316315162";

    /**
     * 请先移除组内成员
     */
    String ERR_MSG_BSS_537395709 = "err.msg.bss.537395709";

    /**
     * 云环境不存在或已被删除
     */
    String ERR_MSG_BSS_1705539941 = "err.msg.bss.1705539941";

    /**
     * 删除专属资源池资源池失败
     */
    String ERR_MSG_BSS_931722621 = "err.msg.bss.931722621";

    /**
     * 同一时间范围存在相同适用环境和适用产品的折扣！
     */
    String ERR_MSG_BSS_1381461091 = "err.msg.bss.1381461091";

    /**
     * 此方法作废!
     */
    String ERR_MSG_BSS_223492382 = "err.msg.bss.223492382";

    /**
     * 套餐包上架失败
     */
    String ERR_MSG_BSS_326385780 = "err.msg.bss.326385780";

    /**
     * 系统升级中
     */
    String ERR_MSG_BSS_472377897 = "err.msg.bss.472377897";

    /**
     * 该云环境已经被删除，请刷新页面后重试。
     */
    String ERR_MSG_BSS_832756465 = "err.msg.bss.832756465";

    /**
     * 无效账户
     */
    String ERR_MSG_BSS_803011385 = "err.msg.bss.803011385";

    /**
     * 正在映射IAM用户
     */
    String ERR_MSG_BSS_328002523 = "err.msg.bss.328002523";

    /**
     * =>检查jar文件是否存在
     */
    String ERR_MSG_BSS_1879732683 = "err.msg.bss.1879732683";

    /**
     * ]进行LDAP同步异常，请重试
     */
    String ERR_MSG_BSS_43773321 = "err.msg.bss.43773321";

    /**
     * 节点流程回退
     */
    String ERR_MSG_BSS_466444867 = "err.msg.bss.466444867";

    /**
     * 状态
     */
    String ERR_MSG_BSS_934923 = "err.msg.bss.934923";

    /**
     * 未准备好
     */
    String ERR_MSG_BSS_807627314 = "err.msg.bss.807627314";

    /**
     * 无效的JDBC URL（应以JDBC开头：）：
     */
    String ERR_MSG_BSS_1158052087 = "err.msg.bss.1158052087";

    /**
     * 公告标题或内容中存在敏感词：
     */
    String ERR_MSG_BSS_767184575 = "err.msg.bss.767184575";

    /**
     * ] 集群类型：[
     */
    String ERR_MSG_BSS_485561678 = "err.msg.bss.485561678";

    /**
     * 产品未找到！
     */
    String ERR_MSG_BSS_296864863 = "err.msg.bss.296864863";

    /**
     * 该环境非断开状态
     */
    String ERR_MSG_BSS_1417750755 = "err.msg.bss.1417750755";

    /**
     * 快照类型错误，只有实例快照才能转换为镜像
     */
    String ERR_MSG_BSS_1698691125 = "err.msg.bss.1698691125";

    /**
     * 项目ID为空，无法验证
     */
    String ERR_MSG_BSS_1537871494 = "err.msg.bss.1537871494";

    /**
     * 密码重置失败
     */
    String ERR_MSG_BSS_2049135760 = "err.msg.bss.2049135760";

    /**
     * 创建防火墙对象失败，请重试！
     */
    String ERR_MSG_BSS_1633819249 = "err.msg.bss.1633819249";

    /**
     * 参数时间不能大于今天
     */
    String ERR_MSG_BSS_139331588 = "err.msg.bss.139331588";

    /**
     * 公告内容太大
     */
    String ERR_MSG_BSS_1156526993 = "err.msg.bss.1156526993";

    /**
     * 释放共享资源池节点信息失败
     */
    String ERR_MSG_BSS_1225363455 = "err.msg.bss.1225363455";

    /**
     * 无权访问该订单
     */
    String ERR_MSG_BSS_1272814246 = "err.msg.bss.1272814246";

    /**
     * 文件地址存在特殊字符
     */
    String ERR_MSG_BSS_2032074447 = "err.msg.bss.2032074447";

    /**
     * 不能删除启用状态的产品模板!
     */
    String ERR_MSG_BSS_377201309 = "err.msg.bss.377201309";

    /**
     * RCLinkTokens.
     */
    String ERR_MSG_BSS_252251529 = "err.msg.bss.252251529";

    /**
     * 云环境删除中
     */
    String ERR_MSG_BSS_1725748412 = "err.msg.bss.1725748412";

    /**
     * 用户正在企业认证中,暂不支持退订产品
     */
    String ERR_MSG_BSS_1360067202 = "err.msg.bss.1360067202";

    /**
     * 未恢复或恢复中的对象不支持下载.
     */
    String ERR_MSG_BSS_2053120504 = "err.msg.bss.2053120504";

    /**
     * 无支持类型
     */
    String ERR_MSG_BSS_887332990 = "err.msg.bss.887332990";

    /**
     * 用户ID
     */
    String ERR_MSG_BSS_917656426 = "err.msg.bss.917656426";

    /**
     * 创建弹性IP没有返回数据, 原始数据:
     */
    String ERR_MSG_BSS_1923092696 = "err.msg.bss.1923092696";

    /**
     * 数据不存在！
     */
    String ERR_MSG_BSS_1231566334 = "err.msg.bss.1231566334";

    /**
     * 批量配置接收人,每次最多10个人!
     */
    String ERR_MSG_BSS_860995564 = "err.msg.bss.860995564";

    /**
     * 短信通道异常,请联系管理员
     */
    String ERR_MSG_BSS_1025000482 = "err.msg.bss.1025000482";

    /**
     * 未设置发票信息，请填写后重试
     */
    String ERR_MSG_BSS_566660286 = "err.msg.bss.566660286";

    /**
     * 折扣类型不能变更
     */
    String ERR_MSG_BSS_629792857 = "err.msg.bss.629792857";

    /**
     * iam子用户
     */
    String ERR_MSG_BSS_1172658454 = "err.msg.bss.1172658454";

    /**
     * 请上传身份提供商Metadata文件。
     */
    String ERR_MSG_BSS_371753704 = "err.msg.bss.371753704";

    /**
     * 同步云环境失败
     */
    String ERR_MSG_BSS_768443232 = "err.msg.bss.768443232";

    /**
     * serviceOrderDetails为空
     */
    String ERR_MSG_BSS_1968340905 = "err.msg.bss.1968340905";

    /**
     * 同一个流程中，不能有重复的节点名称
     */
    String ERR_MSG_BSS_1897518684 = "err.msg.bss.1897518684";

    /**
     * 变更的配置相同
     */
    String ERR_MSG_BSS_1069981827 = "err.msg.bss.1069981827";

    /**
     * 越权访问
     */
    String ERR_MSG_BSS_1105998536 = "err.msg.bss.1105998536";

    /**
     * 短信验证码发送失败
     */
    String ERR_MSG_BSS_2004029892 = "err.msg.bss.2004029892";

    /**
     * JSON格式转换错误。
     */
    String ERR_MSG_BSS_567649477 = "err.msg.bss.567649477";

    /**
     * 存在结束时间时生效时间不能为空
     */
    String ERR_MSG_BSS_396328749 = "err.msg.bss.396328749";

    /**
     * 短信平台未配置,不能开启双因子认证
     */
    String ERR_MSG_BSS_823320488 = "err.msg.bss.823320488";

    /**
     * 暂不支持非AKSK验证
     */
    String ERR_MSG_BSS_454549576 = "err.msg.bss.454549576";

    /**
     * ID={}，HPC
     */
    String ERR_MSG_BSS_957977325 = "err.msg.bss.957977325";

    /**
     * 退订中
     */
    String ERR_MSG_BSS_36554443 = "err.msg.bss.36554443";

    /**
     * 修改发票设置信息失败
     */
    String ERR_MSG_BSS_2005485260 = "err.msg.bss.2005485260";

    /**
     * 自动审批释放资源失败，订单不存在
     */
    String ERR_MSG_BSS_2061894072 = "err.msg.bss.2061894072";

    /**
     * 越权操作
     */
    String ERR_MSG_BSS_1105671400 = "err.msg.bss.1105671400";

    /**
     * 当前安全组无法执行此操作，请解绑虚拟网卡后再删除
     */
    String ERR_MSG_BSS_1726601633 = "err.msg.bss.1726601633";

    /**
     * 账单周期数据正在下载，请稍后查询下载任务！
     */
    String ERR_MSG_BSS_1486919323 = "err.msg.bss.1486919323";

    /**
     * 当前云环境不存在
     */
    String ERR_MSG_BSS_2129638482 = "err.msg.bss.2129638482";

    /**
     * 电话不能为空
     */
    String ERR_MSG_BSS_641213096 = "err.msg.bss.641213096";

    /**
     * 发送成功.
     */
    String ERR_MSG_BSS_535820497 = "err.msg.bss.535820497";

    /**
     * 该名称已存在，请修改
     */
    String ERR_MSG_BSS_102684976 = "err.msg.bss.102684976";

    /**
     * 资源ID存在特殊字符！
     */
    String ERR_MSG_BSS_1712295478 = "err.msg.bss.1712295478";

    /**
     * 实例不存在或已删除
     */
    String ERR_MSG_BSS_1760160432 = "err.msg.bss.1760160432";

    /**
     * +姓名+
     */
    String ERR_MSG_BSS_24046278 = "err.msg.bss.24046278";

    /**
     * 数据状态异常，详细请查看日志。
     */
    String ERR_MSG_BSS_1781194848 = "err.msg.bss.1781194848";

    /**
     * 修改套餐包规格失败
     */
    String ERR_MSG_BSS_177495251 = "err.msg.bss.177495251";

    /**
     * 租户数据异常
     */
    String ERR_MSG_BSS_1903178708 = "err.msg.bss.1903178708";

    /**
     * 数据库数据处理异常
     */
    String ERR_MSG_BSS_985538059 = "err.msg.bss.985538059";

    /**
     * 扩容登录节点数不能小于1
     */
    String ERR_MSG_BSS_1791116397 = "err.msg.bss.1791116397";

    /**
     * 不支持的符合
     */
    String ERR_MSG_BSS_254215097 = "err.msg.bss.254215097";

    /**
     * getLogWriter
     */
    String ERR_MSG_BSS_2055086625 = "err.msg.bss.2055086625";

    /**
     * 密码不符合规则
     */
    String ERR_MSG_BSS_227093865 = "err.msg.bss.227093865";

    /**
     * 分销商类型用户不能被提升为系统管理员
     */
    String ERR_MSG_BSS_1000659492 = "err.msg.bss.1000659492";

    /**
     * 单价只能是数字，请修改后导入！
     */
    String ERR_MSG_BSS_1055554239 = "err.msg.bss.1055554239";

    /**
     * 您无法配置自身账号的用户组
     */
    String ERR_MSG_BSS_1131742719 = "err.msg.bss.1131742719";

    /**
     * 内置角色不可删除
     */
    String ERR_MSG_BSS_1430336017 = "err.msg.bss.1430336017";

    /**
     * 名称为4-30个字符，只能包含小写字母、数字、连接符（-），并且须以字母开头，不能以连接符结尾。
     */
    String ERR_MSG_BSS_210195960 = "err.msg.bss.210195960";

    /**
     * UserSid不为空！
     */
    String ERR_MSG_BSS_231307849 = "err.msg.bss.231307849";

    /**
     * clusterId为空
     */
    String ERR_MSG_BSS_1141235979 = "err.msg.bss.1141235979";

    /**
     * 委托==空
     */
    String ERR_MSG_BSS_512562518 = "err.msg.bss.512562518";

    /**
     * 不支持的方法
     */
    String ERR_MSG_BSS_254378783 = "err.msg.bss.254378783";

    /**
     * 卷创建失败
     */
    String ERR_MSG_BSS_1091676534 = "err.msg.bss.1091676534";

    /**
     * 创建用户失败!
     */
    String ERR_MSG_BSS_106948415 = "err.msg.bss.106948415";

    /**
     * 创建的HuaweiCloud返回服务器为空
     */
    String ERR_MSG_BSS_1813001633 = "err.msg.bss.1813001633";

    /**
     * 需要删除的对象未找到！
     */
    String ERR_MSG_BSS_2032599220 = "err.msg.bss.2032599220";

    /**
     * 该服务实例已删除，请刷新后重试
     */
    String ERR_MSG_BSS_1920758530 = "err.msg.bss.1920758530";

    /**
     * 该合同不存在，请刷新后重试
     */
    String ERR_MSG_BSS_1049287646 = "err.msg.bss.1049287646";

    /**
     * 云账号未找到！
     */
    String ERR_MSG_BSS_935946425 = "err.msg.bss.935946425";

    /**
     * 套餐包下架失败
     */
    String ERR_MSG_BSS_326355989 = "err.msg.bss.326355989";

    /**
     * 不存在的实例
     */
    String ERR_MSG_BSS_1623795316 = "err.msg.bss.1623795316";

    /**
     * ManageOne认证信息验证失败, 错误信息为:
     */
    String ERR_MSG_BSS_985083597 = "err.msg.bss.985083597";

    /**
     * 您无法配置自身账号的权限
     */
    String ERR_MSG_BSS_590795135 = "err.msg.bss.590795135";

    /**
     * 没有找到资源对应的订单
     */
    String ERR_MSG_BSS_862986666 = "err.msg.bss.862986666";

    /**
     * 规格已定价，请勿重复创建！
     */
    String ERR_MSG_BSS_2023301275 = "err.msg.bss.2023301275";

    /**
     * 元，审批失败。
     */
    String ERR_MSG_BSS_580695533 = "err.msg.bss.580695533";

    /**
     * 修改成功
     */
    String ERR_MSG_BSS_635285210 = "err.msg.bss.635285210";

    /**
     * 服务申请单为空，无法创建。
     */
    String ERR_MSG_BSS_663763841 = "err.msg.bss.663763841";

    /**
     * 无操作此工单权限
     */
    String ERR_MSG_BSS_2007130574 = "err.msg.bss.2007130574";

    /**
     * 验证类型不能为空！
     */
    String ERR_MSG_BSS_1372762068 = "err.msg.bss.1372762068";

    /**
     * 角色类型错误.
     */
    String ERR_MSG_BSS_927956392 = "err.msg.bss.927956392";

    /**
     * 无效的账户
     */
    String ERR_MSG_BSS_881653011 = "err.msg.bss.881653011";

    /**
     * 规格未找到！
     */
    String ERR_MSG_BSS_60741981 = "err.msg.bss.60741981";

    /**
     * 数据库名称重复
     */
    String ERR_MSG_BSS_39768952 = "err.msg.bss.39768952";

    /**
     * 该网络正在使用，不可删除
     */
    String ERR_MSG_BSS_325678676 = "err.msg.bss.325678676";

    /**
     * 暂时不支持此操作
     */
    String ERR_MSG_BSS_327399528 = "err.msg.bss.327399528";

    /**
     * 远程IP
     */
    String ERR_MSG_BSS_1127169814 = "err.msg.bss.1127169814";

    /**
     * 流程名称超过最大长度限制
     */
    String ERR_MSG_BSS_2091470371 = "err.msg.bss.2091470371";

    /**
     * 无可用登录节点
     */
    String ERR_MSG_BSS_53065898 = "err.msg.bss.53065898";

    /**
     * 获取策略详情失败
     */
    String ERR_MSG_BSS_2016808703 = "err.msg.bss.2016808703";

    /**
     * 该公告类型不存在！
     */
    String ERR_MSG_BSS_318687287 = "err.msg.bss.318687287";

    /**
     * 该模板不存在！
     */
    String ERR_MSG_BSS_1165028263 = "err.msg.bss.1165028263";

    /**
     * 暂无充值记录可以开票
     */
    String ERR_MSG_BSS_1746776280 = "err.msg.bss.1746776280";

    /**
     * 发票id参数非法
     */
    String ERR_MSG_BSS_3951031 = "err.msg.bss.3951031";

    /**
     * 节点数量不能超过200！
     */
    String ERR_MSG_BSS_178778345 = "err.msg.bss.178778345";

    /**
     * 工单号不正确
     */
    String ERR_MSG_BSS_148785423 = "err.msg.bss.148785423";

    /**
     * 开始时间距今大于180天!
     */
    String ERR_MSG_BSS_884197116 = "err.msg.bss.884197116";

    /**
     * 话单压缩包太大
     */
    String ERR_MSG_BSS_1265954516 = "err.msg.bss.1265954516";

    /**
     * 创建失败
     */
    String ERR_MSG_BSS_650286739 = "err.msg.bss.650286739";

    /**
     * 操纵失败，已移除或不存在
     */
    String ERR_MSG_BSS_941481618 = "err.msg.bss.941481618";

    /**
     * 不存在该公告
     */
    String ERR_MSG_BSS_1628975174 = "err.msg.bss.1628975174";

    /**
     * 非自定义模板，同一操作类型，资源类型只允许存在一个正常状态的模板。
     */
    String ERR_MSG_BSS_1113397063 = "err.msg.bss.1113397063";

    /**
     * 只有关机状态才能重置密码
     */
    String ERR_MSG_BSS_1035431203 = "err.msg.bss.1035431203";

    /**
     * ，已经存在了
     */
    String ERR_MSG_BSS_1625081363 = "err.msg.bss.1625081363";

    /**
     * MB，请重新上传文件
     */
    String ERR_MSG_BSS_1953761768 = "err.msg.bss.1953761768";

    /**
     * 标准专属内置文件系统不支持扩缩容操作
     */
    String ERR_MSG_BSS_1449425777 = "err.msg.bss.1449425777";

    /**
     * 用户账户id参数非法
     */
    String ERR_MSG_BSS_1694975136 = "err.msg.bss.1694975136";

    /**
     * 更新成功
     */
    String ERR_MSG_BSS_810987723 = "err.msg.bss.810987723";

    /**
     * 合同不存在
     */
    String ERR_MSG_BSS_947120615 = "err.msg.bss.947120615";

    /**
     * 查询用户登录名不能为空
     */
    String ERR_MSG_BSS_1326451529 = "err.msg.bss.1326451529";

    /**
     * 未查询到符合条件的组织数据
     */
    String ERR_MSG_BSS_1497603621 = "err.msg.bss.1497603621";

    /**
     * 请联系运营管理员，产品未上架！
     */
    String ERR_MSG_BSS_162401583 = "err.msg.bss.162401583";

    /**
     * 请联系运营管理员，产品未授权！
     */
    String ERR_MSG_BSS_162401584 = "err.msg.bss.162401584";

    /**
     * 服务单类型不支持，支持的类型包括：申请、升配、降配、续订、退订
     */
    String ERR_MSG_BSS_791467718 = "err.msg.bss.791467718";

    /**
     * 平台折扣id参数非法
     */
    String ERR_MSG_BSS_1033172136 = "err.msg.bss.1033172136";

    /**
     * 发送成功
     */
    String ERR_MSG_BSS_675452127 = "err.msg.bss.675452127";

    /**
     * 认证失败，请检查身份证图片是否已上传
     */
    String ERR_MSG_BSS_1362024570 = "err.msg.bss.1362024570";

    /**
     * HPC专属资源池出账错误!
     */
    String ERR_MSG_BSS_7836561 = "err.msg.bss.7836561";

    /**
     * 未找到对应资源，请刷新后重试
     */
    String ERR_MSG_BSS_537059183 = "err.msg.bss.537059183";

    /**
     * AKSK验证失败
     */
    String ERR_MSG_BSS_1178885493 = "err.msg.bss.1178885493";

    /**
     * 当前登录用户无权限为该用户分配系统管理员权限
     */
    String ERR_MSG_BSS_822770033 = "err.msg.bss.822770033";


    /***************** 自动生成 ******************/

    /* 提示消息 */
    /**
     * info.insert.success=创建成功。
     */
    String INFO_INSERT_SUCCESS = "info.insert.success";

    /**
     * info.register.success=注册成功。
     */
    String INFO_REGISTER_SUCCESS = "info.register.success";

    /**
     * info.update.success=更新成功。
     */
    String INFO_UPDATE_SUCCESS = "info.update.success";

    /**
     * info.publish.success=发布成功。
     */
    String INFO_PUBLISH_SUCCESS = "info.publish.success";

    /**
     * info.delete.success=删除成功。
     */
    String INFO_DELETE_SUCCESS = "info.delete.success";

    /**
     * info.delete.success=移除成功。
     */
    String INFO_REMOVE_SUCCESS = "info.remove.success";

    /**
     * info.relation.success=关联操作成功。
     */
    String INFO_RELATION_SUCCESS = "info.relation.success";

    /**
     * info.approve.success=审核成功。
     */
    String INFO_APPROVE_SUCCESS = "info.approve.success";

    /**
     * info.message.success=发送成功。
     */
    String INFO_MESSAGE_SUCCESS = "info.message.success";

    /**
     * info.inventory.success=盘点成功。
     */
    String INFO_INVENTORY_SUCCESS = "info.inventory.success";

    /**
     * info.in.success=入库成功。
     */
    String INFO_IN_SUCCESS = "info.in.success";

    /**
     * info.relate.success=关联成功。
     */
    String INFO_RELATE_SUCCESS = "info.relate.success";

    /**
     * info.out.success=出库成功。
     */
    String INFO_OUT_SUCCESS = "info.out.success";

    /**
     * info.copy.success=复制成功。
     */
    String INFO_COPY_SUCCESS = "info.copy.success";

    /**
     * vm.start.success=启动成功。
     */
    String VM_START_SUCCESS = "vm.start.success";

    /**
     * vm.stop.success=关机成功。
     */
    String VM_STOP_SUCCESS = "vm.stop.success";

    /**
     * vm.restart.success=重启成功。
     */
    String VM_RESTART_SUCCESS = "vm.restart.success";

    /**
     * vm.reconfig.success=调整成功。
     */
    String VM_RECONFIG_SUCCESS = "vm.start.reconfig";

    /**
     * vm.migrate.success=迁移成功。
     */
    String VM_MIGRATE_SUCCESS = "vm.migrate.success";

    /**
     * vm.destory.success=退订成功。
     */
    String VM_DESTORY_SUCCESS = "vm.destory.success";

    /**
     * vm.managed.success=纳管成功。
     */
    String VM_MANAGED_SUCCESS = "vm.managed.success";

    /**
     * vm.rename.success=虚拟机修改名称成功。
     */
    String VM_RENAME_SUCCESS = "vm.rename.success";

    /**
     * task.issued.success=任务下发成功，请到日志中心查看详情。<br>待任务完成后请手动进行刷新操作。
     */
    String TASK_ISSUED_SUCCESS = "task.issued.success";

    /**
     * task.distribute.success=任务提交成功，请稍后查看结果。
     */
    String TASK_DISTRIBUTE_SUCCESS = "task.distribute.success";

    /**
     * image.disabled.success=停用成功。
     */
    String IMAGE_DISABLED_SUCCESS = "image.disabled.success";

    /**
     * info.ticket.success=分配工单成功
     */
    String INFO_TICKET_SUCCESS = "info.ticket.success";

    /**
     * info.vm.res.check=资源检查成功。
     */
    String INFO_VM_RES_CHECK = "info.vm.res.check";

    /**
     * info.ticket.execute=重新执行工单成功。
     */
    String INFO_TICKET_EXECUTE = "info.ticket.execute";

    /**
     * info.ticket.execute=操作成功。
     */
    String INFO_OPERATE_SUCCESS = "info.operate.success";
    /**
     * info.operate.freeze.success=操作成功，请稍后查看账户状态。
     */
    String INFO_OPERATE_FREEZE_SUCCESS = "info.operate.freeze.success";
    /**
     * info.operate.freeze.success=冻结中，请稍后查看账户状态。
     */
    String INFO_OPERATE_FREEZING = "info.operate.freezing";

    /**
     * info.operate.freeze.success=解冻中，请稍后查看账户状态。
     */
    String INFO_OPERATE_THAWING = "info.operate.thawing";

    String SYSTEM_MESSAGE_COUPON = "system.message.coupon";

    String INFO_MOBILE_GET_SUCCESS = "info.mobile.get.success";

    /**
     * error.edit.failure=编辑失败。
     */
    String ERROR_EDIT_FAILURE = "error.edit.failure";
    /**
     * info.edit.success=编辑成功。
     */
    String INFO_EDIT_SUCCESS = "info.edit.success";

    /**
     * info.department.exist=部门已存在
     */
    String INFO_DEPARTMENT_EXIST = "info.department.exist";

    /* 错误消息 */
    /**
     * error.system.exception
     **/
    String ERROR_SYS_EXCEPTION = "error.system.exception";

    /* 警告消息 */
    /**
     * warning_service_repeat=对不起，该服务不能重复订购。
     */
    String WARNING_SERVICE_REPEAT = "warning_service_repeat";

    /**
     * warning.query.failure=对不起，数据为空。
     */
    String WARNING_QUERY_FAILURE = "warning.query.failure";

    /* 错误消息 */
    /**
     * error.insert.failure=创建失败。
     */
    String ERROR_INSERT_FAILURE = "error.insert.failure";
    /**
     * error.insert.failure.param={0}创建失败。
     */
    String ERROR_INSERT_FAILURE_PARAM = "error.insert.failure.param";
    /**
     * error.message.failure=发送失败。
     */
    String ERROR_MESSAGE_FAILURE = "error.message.failure";

    /**
     * error.query.failure=获取信息失败，数据已被更新或删除。
     */
    String ERROR_QUERY_FAILURE = "error.query.failure";

    /**
     * error.register.failure=注册失败，数据或已存在，请重试。
     */
    String ERROR_REGISTER_FAILURE = "error.register.failure";

    /**
     * error.update.failure=更新失败，数据已被更新或删除。
     */
    String ERROR_UPDATE_FAILURE = "error.update.failure";

    /**
     * error.publish.success=发布失败。
     */
    String ERROR_PUBLISH_FAILURE = "error.publish.failure";

    /**
     * error.delete.failure=删除失败，数据已被更新或删除。
     */
    String ERROR_DELETE_FAILURE = "error.delete.failure";

    /**
     * error.approve.failure=审核失败。
     */
    String ERROR_APPROVE_FAILURE = "error.approve.failure";

    /**
     * error.sendmail.failure=邮件发送失败。
     */
    String ERROR_SENDMAIL_FAILURE = "error.sendmail.failure";

    /**
     * error.inventory.failure=盘点失败。
     */
    String ERROR_INVENTORY_FAILURE = "error.inventory.failure";

    /**
     * error.in.failure=入库失败。
     */
    String ERROR_IN_FAILURE = "error.in.failure";

    /**
     * error.valid.failure=当前配置信息有误，请检查后重试。
     */
    String ERROR_VALID_FAILURE = "error.valid.failure";

    /**
     * error.relate.failure=关联失败。
     */
    String ERROR_RELATE_FAILURE = "error.relate.failure";

    /**
     * error.out.failure=出库失败。
     */
    String ERROR_OUT_FAILURE = "error.out.failure";

    /**
     * error.copy.failure=复制失败。
     */
    String ERROR_COPY_FAILURE = "error.copy.failure";

    /**
     * error.relation.failure=关联操作失败，数据已被更新或删除。
     */
    String ERROR_RELATION_FAILURE = "error.relation.failure";

    /**
     * error.data.exist={0}已经存在，请重新填写。
     */
    String ERROR_DATA_EXIST = "error.data.exist";

    /**
     * error.data.relation={0}，不能进行删除。
     */
    String ERROR_DATA_RELATION = "error.data.relation";

    /**
     * error.data.relation.delete=存在关联关系，不能进行删除。
     */
    String ERROR_DATA_RELATION_DELETE = "error.data.relation.delete";
    /**
     * error.data.relation.delete.param=部分数据无法删除，请检查{0}是否存在关联关系。
     */
    String ERROR_DATA_RELATION_DELETE_PARAM = "error.data.relation.delete.param";
    /**
     * error.data.relation.update=不能进行修改。
     */
    String ERROR_DATA_RELATION_UPDATE = "error.data.relation.update";

    /**
     * error.data.relation={0}，数据已被更新或删除。
     */
    String ERROR_DATA_FAILURE = "error.data.failure";

    /**
     * error.file.oversize=您选择的文件过大，请重新选择。
     */
    String ERROR_FILE_OVERSIZE = "error.file.oversize";

    /**
     * error.plan.published=该名称预案已经发布。
     */
    String ERROR_PLAN_PUBLISHED = "error.plan.published";

    /**
     * error.plan.published=该预案已经停用。
     */
    String ERROR_PLAN_DISABLED = "error.plan.disabled";

    /**
     * error.assess.maxnum={0}不能超过十个。
     */
    String ERROR_ASSESS_MAXNUM = "error.assess.maxnum";

    /**
     * 订单取消失败。
     */
    String ERROR_CANCEL_ORDER = "error.cancel.order";

    /**
     * error.vm.start=启动失败。
     */
    String ERROR_VM_START = "error.vm.start";

    /**
     * error.vm.stop=关机失败。
     */
    String ERROR_VM_STOP = "error.vm.stop";

    /**
     * error.vm.restart=重启失败。
     */
    String ERROR_VM_RESTART = "error.vm.restart";

    /**
     * error.vm.reconfig=调整失败。
     */
    String ERROR_VM_RECONFIG = "error.vm.reconfig";

    /**
     * error.vm.migrate=迁移失败。
     */
    String ERROR_VM_MIGRATE = "error.vm.migrate";

    /**
     * error.vm.managed=纳管失败。
     */
    String ERROR_VM_MANAGED = "error.vm.managed";

    /**
     * error.vm.scan=扫描失败。
     */
    String ERROR_VM_SCAN = "error.vm.scan";

    /**
     * error.vm.destory=退订失败。
     */
    String ERROR_VM_DESTORY = "error.vm.destory";

    /**
     * error.vm.rename=虚拟机修改名称失败。
     */
    String ERROR_VM_RENAME = "error.vm.rename";

    /**
     * error.task.issued=任务发送失败。
     */
    String ERROR_TASK_ISSUED = "error.task.issued";
    /**
     * error.image.disabled=停用失败。
     */
    String ERROR_IMAGE_DISABLED = "error.image.disabled";

    /**
     * error.recover.busy={0}，请等待。
     */
    String ERROR_RECOVER_BUSY = "error.recover.busy";

    /**
     * error.ip.unreachable=IP地址连接失败，无法启动远程连接。
     */
    String ERROR_IP_UNREACHABLE = "error.ip.unreachable";

    /* 警告消息 */
    /**
     * warning_ippool_repeat=对不起，该IP不能重复添加到资源池。
     */
    String WARNING_IPPOOL_REPEAT = "warning_ippool_repeat";

    /* 警告消息 */
    /**
     * warning_ip_repeat=对不起，该IP已经添加到资源。
     */
    String WARNING_IP_REPEAT = "warning_ip_repeat";

    /**
     * error.ticket.allocate=分配工单失败。
     */
    String ERROR_TICKET_ALLOCATE = "error.ticket.allocate";

    /**
     * error.vm.res.check=资源不足。
     */
    String ERROR_VM_RES_CHECK = "error.vm.res.check";

    /**
     * error.biz.user.check=没有用户关联到所选业务名称。
     */
    String ERROR_BIZ_USER_CHECK = "error.biz.user.check";

    /**
     * error.ticket.execute=重新执行工单失败。
     */
    String ERROR_TICKET_EXECUTE = "error.ticket.execute";

    /**
     * warning.mgtobjres.failure=该租户尚未关联资源。
     */
    String WARNING_MGTOBJRES_FAILURE = "warning.mgtobjres.failure";

    /**
     * error.operate.failure=获取虚拟化环境失败。
     */
    String ERROR_VE_FAILURE = "error.ve.failure";

    /**
     * error.not.support=暂不支持当前类型
     */
    String ERROR_NOT_SUPPORT = "error.not.support";

    /**
     * error.env.exclusive.can.not.share=当前云环境为独占模式，不支持再次分配
     */
    String ERROR_ENV_EXCLUSIVE_CAN_NOT_SHARE = "error.env.exclusive.can.not.share";

    /**
     * error.env.share.can.not.modify.mode=云环境以共享模式分配到当前组织，只能以共享模式再次分配
     */
    String ERROR_ENV_SHARE_CAN_NOT_MODIFY_MODE = "error.env.share.can.not.modify.mode";

    /**
     * error.env.exclusive.can.not.share=当前云环境为独占模式，资源不可移除
     */
    String ERROR_ENV_EXCLUSIVE_CAN_NOT_REMOVE = "error.env.exclusive.can.not.remove";

    /**
     * error.env.exclusive.can.not.share=当前云环境为独占模式，资源不可移动
     */
    String ERROR_ENV_EXCLUSIVE_CAN_NOT_MOVE = "error.env.exclusive.can.not.move";

    /**
     * error.self.inst.res.forbidden=当前资源与自服务实例关联，不支持分配
     */
    String ERROR_SELF_INST_RES_FORBIDDEN = "error.self.inst.res.forbidden";

    /**
     * INFO_GIFT_CARD_GEN_SUCCESS
     */
    String INFO_GIFT_CARD_GEN_SUCCESS = "info_gift_card_gen_success";
    String ERROR_PARAMETER_WRONG = "error_parameeter_wrong";
    String LOGIN_SUCCESS = "user.login.success";
    String LOGIN_FAILED = "user.login.failed";
    String LOGOUT_SUCCESS = "user.logout.success";

    String ERROR_TRIAL_HOST_CREATE = "error.vm.trial.failure";
    String ERROR_VM_TRIAL_RETRY_FAILURE = "error.vm.trial.retry.failure";
    String ERROR_TRIAL_HOST_ENV_ABSENT = "error.vm.trial.env.absent";

    /**
     * ldap info
     */
    String LDAP_CONNECT_SUCCESS = "ldap.connect.success";
    String LDAP_CONNECT_FAILURE = "ldap.connect.failure";

    /**
     * %s不存在或已被删除，请刷新页面后重试。
     */
    String ERROR_RES_NOT_FOUND = "error.res.not.found";

    /**
     * 远程调用MQ失败
     */
    String ERROR_MQ_CONNECT = "error.mq.connect";

    /**
     * 非运行中或已停止状态的实例，不可执行变更%s操作
     */
    String ERROR_OPERATE_FORBIDDEN = "error.operate.forbidden";

    /**
     * 配额控制已%s
     */
    String QUOTA_CTRL = "info.quota.ctrl";

    /**
     * 参数校验不通过
     */
    String PARAM_NOT_VALID_ERROR = "param.not.valid.error";

    /**
     * 商品供应商不存在!
     */
    String SHOP_SUPPLIER_DOES_NOT_EXIST = "shop.supplier.does_not.exist";

    /**
     * HEAD版本不支持创建实例
     */
    String ERROR_MSG_00001 = "error.msg.00001";

    /**
     * 所选择的主机模板不支持当前云环境
     */
    String ERROR_MSG_00002 = "error.msg.00002";

    /**
     * 仅VMware环境支持克隆, 当前云环境类型为%s
     */
    String ERROR_MSG_00003 = "error.msg.00003";

    /**
     * 暂不支持该操作
     */
    String ERROR_MSG_00004 = "error.msg.00004";

    /**
     * 当前云环境不支持配置变更
     */
    String ERROR_MSG_00005 = "error.msg.00005";

    /**
     * 该实例未关联任何分区，不支持配置变更
     */
    String ERROR_MSG_00006 = "error.msg.00006";

    /**
     * %s不能为空
     */
    String ERROR_MSG_00007 = "error.msg.00007";

    /**
     * 项目已有此共享云环境
     */
    String ERROR_MSG_00008 = "error.msg.00008";

    /**
     * 云环境共享当前仅支持共享（share）, 独享（exclusive）两种模式
     */
    String ERROR_MSG_00009 = "error.msg.00009";

    /**
     * 项目没有此云环境，请先关联云环境
     */
    String ERROR_MSG_00010 = "error.msg.00010";

    /**
     * 只支持playbook类型脚本
     */
    String ERROR_MSG_00011 = "error.msg.00011";

    /**
     * 手机号重复
     */
    String ERROR_MSG_00012 = "error.msg.00012";

    /**
     * 邮箱重复
     */
    String ERROR_MSG_00013 = "error.msg.00013";

    /**
     * 所选择的角色与所选择的组织未关联
     */
    String ERROR_MSG_00014 = "error.msg.00014";

    /**
     * 仅VMware环境和FusionCompute支持创建存储类型，当前云环境类型为%s
     */
    String ERROR_MSG_00015 = "error.msg.00015";

    /**
     * 仅VMware和FusionCompute环境支持编辑存储类型，当前云环境类型为%s
     */
    String ERROR_MSG_00016 = "error.msg.00016";

    /**
     * 仅VMware环境支持当前操作
     */
    String ERROR_MSG_00017 = "error.msg.00017";
    /**
     * 云环境不存在
     */
    String CLOUD_ENV_NOT_EXIST = "cloud.env.not.exist";

    /**
     * hcso用户不存在
     */
    String CLOUD_USER_NOT_EXIST = "user.not.hcso.account";

    /**
     * 云环境名称重复
     */
    String CLOUD_ENV_NAME_DUPLICATE = "cloud.env.name.duplicate";

    /**
     * res param error
     */
    String RES_PARAM_STATUS_ERROR = "res.param.status.error";

    /**
     * 只读资源无法移除
     */
    String ERROR_READONLY_RES_CANNOT_REMOVE = "error.readonly.res.cannot.remove";

    /**
     * 只读资源无法编辑
     */
    String ERROR_READONLY_RES_CANNOT_EDIT = "error.readonly.res.cannot.remove";

    /**
     * 环境纳管需要保留具有管理员权限的项目
     */
    String ERROR_MSG_00018 = "error.msg.00018";

    /**
     * 当前项目已按云环境接入，请先删除云环境后再删除该项目
     */
    String ERROR_MSG_00019 = "error.msg.00019";

    /**
     * 当前许可证不支持该云环境，如有需要请联系管理员。
     */
    String ERROR_MSG_00020 = "error.msg.00020";

    /**
     * %s已存在，请检查后重试
     */
    String ERROR_MSG_00021 = "error.msg.00021";

    /**
     * 平台已接入当前区域下的云环境，请先删除该区域下所有已纳管的云环境后重试
     */
    String ERROR_MSG_00022 = "error.msg.00022";
    /**
     * 您试图删除的存储桶不是空的。
     */
    String BUCKET_DELETE_ERROR = "bucket.delete.error";

    /**
     * 自服务主机续订请前往（自服务-服务实例）菜单
     */
    String VM_RENEW_WARN = "vm.renew.warn";

    /**
     * 关联到组织上的环境，再分配时不支持独立使用模式
     */
    String ERROR_MSG_00023 = "error.msg.00023";

    /**
     * 用户名重复
     */
    String ERROR_MSG_00024 = "error.msg.00024";

    /**
     * 自服务资源续订请前往自服务菜单
     **/
    String RESOURCE_RENEW_WARN = "resource.renew.warn";

    /**
     * 验证码无效
     **/
    String SMSCODEINVALID = "smscodeinvalid";
    /**
     * 验证码错误
     **/
    String SMSCODEERROR = "smscodeerror";

    /**
     * 计算节点至少保留一个
     */
    String DELETE_HPC_COMPUTE_NODE_ERROR = "delete.hpc.compute.node.error";

    /**
     * 登录节点至少保留一个
     */
    String DELETE_HPC_CLI_NODE_ERROR = "delete.hpc.cli.node.error";

    /**
     * 至少保留一个登录节点和一个计算节点
     */
    String DELETE_HPC_COMPUTE_AND_CLI_NODE_ERROR = "delete.hpc.compute.and.cli.node.error";


    /**
     * 无权限操作接口
     */
    String AUTHORIZE_FAILURE = "operate.interface.failure";

    /**
     * 资源池ID已存在！
     */
    String POOL_ID_UNIQUENESS = "pool.id.uniqueness";
    /**
     * 当前挂载弹性文件已冻结!
     */
    String  SHARE_IS_FROZEN = "share.is.frozen";
    /**
     * 当前挂载集群已冻结!
     */
    String CLUSTER_IS_FROZEN = "cluster.is.frozen";

    /**
     * 运营管理员不能导入预开通租户和子用户
     */
    String OSS_ADMIN_IMPORT_PRE_OPEN_USER_ERROR = "oss.admin.import.pre.open.user.error";
    /**
     * 删除AI共享资源池错误，
     */
    String RES_MA_POOL_DELETE_ERROR = "mapool.delete.error";

    /**
     * HPC专属资源池开通中，请勿操作
     */
    String HPC_CLUSTER_NOT_APPLY_FINISH = "hpc.cluster.not.apply.finish";

    /**
     * 该文件系统不支持卸载
     */
    String UN_SUPPORT_UNMOUNT_SFS = "un.support.unmount.sfs";
    /**
     * 消息发送验证超时
     */
    String VALIDATE_CONTACTR_TIME_OUT = "validate.contactr.time.out";
    /**
     * 底层共享资源池租户已注册
     */
    String RESOURCE_ALREADY_SHARED = "resource.status.already.shared";
    /**
     * 当前账户已冻结
     */
    String ACCOUNT_FREEZE_ERROR = "account.freeze.error";
    /**
     * hpc.cluster.no.exist =HPC集群不存在
     */
    String HPC_CLUSTER_NO_EXIST = "hpc.cluster.no.exist";
    /**
     * error.product.deleting = 退订中
     */
    String ERROR_PRODUCT_DELETING = "error.product.deleting";
    /**
     * error.product.deleted = 已退订
     */
    String ERROR_PRODUCT_DELETED = "error.product.deleted";

    /**
     * 专属资源池到期资源限制为暂不处理，不能设置冻结策略为释放资源。
     */
    String FREEZING_STRATEGY_MSG_001 = "freezing.strategy.msg.001";

    /**
     * 专属资源池到期资源限制白名单租户，不能设置冻结策略为释放资源。
     */
    String FREEZING_STRATEGY_MSG_002 = "freezing.strategy.msg.002";

    /**
     * 该资源存在冻结策略为释放资源的实例，不能设置资源限制为暂不处理。
     */
    String FREEZING_STRATEGY_MSG_003 = "freezing.strategy.msg.003";

    /**
     * 参数必须大于0
     */
    String PARAMETER_CANNOT_BE_LESS="parameter.cannot.be.less";

    String  SYSTEM_VALUE="system.value";

    String  START_TIME_END = "start.time.end";

    String ERROR_UNSUBSCRIBE_OPERATION = "error.unsubscribe.operation";

    /**
     * 系统错误，请联系运营管理员。
     */
    String SYSTEM_ERROR = "system.error";

    /**
     * 共享资源池没有运行的作业。
     */
    String HPC_SHARE_NOT_JOB = "hpc.share.not.job";

    /**
     *  资源池规格已经存在!
     */
    String FLAVOR_EXIST = "flavor.exist";

    /* 错误消息 */
    /**
     * account.exist.failure=账号异常，请联系系统管理员处理。
     */
    String ACCOUNT_EXIST_FAILURE = "account.exist.failure";

    /**
     * account.exist.failure={0}环境不存在。
     */
    String CLOUD_ACCOUNT_EXIST_ERROR = "cloud.account.exist.error";

    /**
     * account.exist.failure=查询委托配额失败，请联系管理员处理
     */
    String QUOTA_QUERY_FAILURE = "quota.query.failure";

    /**
     * all.user.delete.forbid=所有用户禁止删除！
     */
    String ALL_USER_DELETE_FORBID = "all.user.delete.forbid";

    /**
     * authorization.delete.failure=删除授权失败，请联系管理员处理
     */
    String AUTHORIZATION_DELETE_FAILURE = "authorization.delete.failure";

    /**
     * authorization.create.failure=创建授权失败，请联系管理员处理
     */
    String AUTHORIZATION_CREATE_FAILURE = "authorization.create.failure";

    /**
     * agency.create.failure=创建委托失败，请联系管理员处理
     */
    String AGENCY_CREATE_FAILURE = "agency.create.failure";

    /**
     * agency.create.failure=委托名称已存在
     */
    String AGENCY_NAME_EXIST = "agency.name.exist";

    /* 提示消息 */

    /**
     * warn.recharge.account=账户数据已被更新，请刷新重试。
     */
    String WARN_RECHARGE_ACCOUNT = "warn.recharge.account";

    /**
     * recharge.error=充值错误。
     */
    String RECHARGE_ERROR = "recharge.error";


    /**
     * 请选择相同计费模式规格项。
     */
    String NEED_SAME_MODE = "need.same.mode";

    /**
     * 非服务策略类型，缺少规格参数！
     */
    String LOST_SPEC_PARAMS = "lost.spec.params";


    /**
     * 当前组织下账号不存在
     */
    String ORGANIZATION_ERROR_00001 = "organization.error.00001";


    /**
     * 无法获取当前用户Id
     */
    String ERROR_MSG_00025 = "error.msg.00025";
    /**
     * 不可重复购买相同资源类型的折扣包
     */
    String ERROR_MSG_00026 = "error.msg.00026";
    /**
     * "登录用户权限错误，未关联对应的账户。"
     */
    String ERROR_MSG_00027 = "error.msg.00027";
    /**
     * 续订资源不存在
     */
    String ERROR_MSG_00028 = "error.msg.00028";
    /**
     * 只能续订同种规格的资源
     */
    String ERROR_MSG_00029 = "error.msg.00029";

    /**
     * "不能移除未过期的套餐包"
     */
    String ERROR_MSG_00030 = "error.msg.00030";

    /**
     * 该操作无权限，检查账户是否实名认证成功！
     */
    String ERROR_MSG_00031 = "error.msg.00031";

    /**
     * 解密参数异常
     */
    String DECRYPT_PARAMS_FAILURE = "decrypt.params.failure";


    /**
     * 参数 产品名称校验 param.valid.productName=名称在1-25字之间
     */
    String PARAM_VALID_PRODUCT_NAME = "param.valid.productName";
    /**
     * 参数 产品名图标 param.valid.servcieIconPath=图标地址太长
     */
    String PARAM_VALID_SERVCIE_ICON_PATH = "param.valid.servcieIconPath";

    /**
     * 参数 产品代码 param.valid.productCode=产品代码格式错误
     */
    String PARAM_VALID_PRODUCT_CODE = "param.valid.productCode";


    /**
     * 参数 产品名描述 param.valid.productDesc=产品描述太长
     */
    String PARAM_VALID_PRODUCT_DESC = "param.valid.productDesc";

    /**
     * 合同类型错误
     */
    String CONTRCT_TYPE_ERROR = "contrct.type.error";

    /**
     * 参数不合法
     */
    String PARAMS_NOT_LEGALITY = "params.not.legality";

    /**
     * 文件上传数量达到上限
     */
    String FILE_SUM_ERROR = "file.sum.error";

    /**
     * 暂不支持该文件类型上传
     */
    String FILE_TYPE_ERROR = "file.type.error";

    /**
     * 文件过大，无法上传
     */
    String FILE_SIZE_ERROR = "file.size.error";

    /**
     * 文件名重复
     */
    String FILE_REPEAT_ERROR = "file.repeat.error";

    /**
     * 当前用户无权查看
     */
    String NOT_HAVE_PERMISSION_VIEW = "not.have.permission.view";

    /**
     * 未获取到当前登录用户信息
     */
    String THE_CURRENT_LOGGED_IN_USER_INFORMATION_WAS_NOT_OBTAINED = "the.current.logged.in.user.information.was.not.obtained";

    /**
     * error.project.can.not.remove=当前项目存在未退订资源，项目不可删除。
     */
    String ERROR_PROJECT_CAN_NOT_REMOVE = "error.project.can.not.remove";
    /**
     * 该文件系统已绑定专属资源池，不支持退订
     */
    String SFS2_EXISTS_BINDING_RESOURCE = "sfs2.exists.binding.resource";

    /**
     * 结束时间应大于开始时间
     */
    String THE_END_TIME_SHOULD_BE_GREATER_THAN_THE_START_TIME = "the.end.time.should.be.greater.than.the.start.time";

    /**
     * 结束时间应大于当前时间
     */
    String THE_END_TIME_SHOULD_BE_GREATER_THAN_THE_CURRENT_TIME = "the.end.time.should.be.greater.than.the.current.time";

    /**
     * 输入金额不能大于********
     */
    String THE_ENTERED_ENTERED_AMOUNT_CANNOT_BE_GREATER_THAN_******** = "the.entered.amount.cannot.be.greater.than.********";

    /**
     * 密码不符合平台密码策略，请检查后重新输入密码
     */
    String THE_PASSWORD_DOES_NOT_CONFORM_TO_THE_PLATFORM_PASSWORD_POLICY = "The.password.does.not.conform.to.the.platform.password.policy";

    /**
     * 所选客户不存在
     */
    String CUSTOMER_DOES_NOT_EXIST = "customer.does.not.exist";

    /**
     * 所选客户已存在
     */
    String CUSTOMER_ALREADY_EXIST = "customer.already.exists";

    /**
     * 所选产品限额不存在
     */
    String PRODUCT_LIMIT_DOES_NOT_EXIST = "product.limit.does.not.exist";

    /**
     * 所选客户限额不存在
     */
    String CUSTOMER_PRODUCT_LIMIT_DOES_NOT_EXIST = "customer.product.limit.does.not.exist";

    /**
     * 所选产品限额id不能为空
     */
    String PRODUCT_LIMIT_ID_CANNOT_BE_EMPTY = "product.limit.id.cannot.be.empty";

    /**
     * 所选客户限额id不能为空
     */
    String CUSTOMER_PRODUCT_LIMIT_ID_CANNOT_BE_EMPTY = "customer.product.limit.id.cannot.be.empty";

    /**
     * 所选白名单id不能为空
     */
    String WHITELIST_LIMIT_ID_CANNOT_BE_EMPTY = "whitelist.limit.id.cannot.be.empty";

    /**
     * 所选产品限额配置已存在
     */
    String THE_PRODUCT_QUOTA_CONFIGURATION_ALREADY_EXISTS = "the.product.quota.configuration.already.exists";

    /**
     * 所选客户限额配置已存在
     */
    String THIS_CUSTOMER_PRODUCT_LIMIT_CONFIGURATION_ALREADY_EXISTS = "this.customer.product.limit.configuration.already.exists";

    /**
     * 所选客户产品限额已配置全部产品
     */
    String THE_CUSTOMER_PRODUCT_LIMIT_HSA_BEEN_CONFIGURED_FOR_ALL_PRODUCTS = "the.customer.product.limit.has.been.configured.for.all.products";

    /**
     * 所选产品不存在
     */
    String THE_SELECTED_PRODUCT_DOES_NOT_EXIST = "the.selected.product.does.not.exist";

    /**
     * 所选客户重复
     */
    String SELECTED_CUSTOMER_DUPLICATES = "selected.customer.duplicates";

    /**
     * 所选id重复
     */
    String DUPLICATE_SELECTED_ID = "duplicate.selected.id";

    /**
     * 购买失败，该产品购买限额为{0}元，请充值现金余额
     */
    String LIMIT_MIN_AMOUNT = "limit.min.amount";

    /**
     * 最小购买金额需大于最小冻结金额
     */
    String MINIMUM_PURCHASE_FROZEN = "the.minimum.purchase.amount.must.be.greater.than.the.minimum.frozen.amount";

    /**
     * 启用失败，该客户已启用全部产品限额
     */
    String ACTIVATION_FAILED_THE_CUSTOMER = "activation.failed.the.customer.has.activated.the.full.product.limit";

    /**
     * 账户已欠费！
     */
    String ACCOUNT_ARREARAGE = "account.arrearage";

    /**
     * 账户已冻结！
     */
    String ACCOUNT_FREEZE = "account.freeze";

    /**
     * 合同类型错误
     */
    String ACCOUNT_NOT_EXISTS = "account.not.exists";

    /**
     * 中小型资源池计算节点不能超过49个
     */
    String CONTRCT_RESOURCE_POOL_SMALL_MIDDLE = "contrct.resource.pool.small.middle";
    /**
     * 至少扩容一个类型的节点
     */
    String CONTRCT_ALL_NUMBER_OPEN_ISNULL = "contrct.all.number.open.isnull";
    /**
     * 计算节点扩容数不能为空
     */
    String CONTRCT_AGENT_NUMBER_OPEN_ISNULL = "contrct.agent.number.open.isnull";
    /**
     * 登录节点扩容数不能为空
     */
    String CONTRCT_CLI_NUMBER_OPEN_ISNULL = "contrct.cli.number.open.isnull";
    /**
     * VNC节点扩容数不能为空
     */
    String CONTRCT_VNC_NUMBER_OPEN_ISNULL = "contrct.vnc.number.open.isnull";
    /**
     * 计算节点单价不能为空
     */
    String CONTRCT_AGENT_PRICE_ISNULL = "contrct.agent.price.isnull";
    /**
     * 登录节点单价不能为空
     */
    String CONTRCT_CLI_PRICE_ISNULL = "contrct.cli.price.isnull";
    /**
     * VNC节点单价不能为空
     */
    String CONTRCT_VNC_PRICE_ISNULL = "contrct.vnc.price.isnull";
    /**
     * 续订合同租户必须和扩容合同租户保持一致
     */
    String CONTRCT_RENEW_ACCOUNT_ERROR = "contrct.renew.account.error";
    /**
     * 至少续订一个类型的节点
     */
    String CONTRCT_RENEW_ALL_NUMBER_OPEN_ISNULL = "contrct.renew.all.number.open.isnull";


    /**
     * 计算节点不允许续订
     */
    String CONTRCT_RENEW_AGENT_NUMBER_OPEN_ISNULL = "contrct.renew.agent.number.open.isnull";
    /**
     * 续订合同计算节点数不能大于扩容合同计算节点数
     */
    String CONTRCT_RENEW_AGENT_NUMBER_OPEN_GT = "contrct.renew.agent.number.open.gt";
    /**
     * 登录节点不允许续订
     */
    String CONTRCT_RENEW_CLI_NUMBER_OPEN_ISNULL = "contrct.renew.cli.number.open.isnull";
    /**
     * vnc节点不允许续订
     */
    String CONTRCT_RENEW_VNC_NUMBER_OPEN_ISNULL = "contrct.renew.vnc.number.open.isnull";

    /**
     * 续订合同登录节点数不能大于扩容合同登录节点数
     */
    String CONTRCT_RENEW_CLI_NUMBER_OPEN_GT = "contrct.renew.cli.number.open.gt";
    /**
     * 续订合同vnc节点数不能大于扩容合同vnc节点数
     */
    /**
     * error.add.failure=该不计费产品已关联，请重新选择。
     */
    String ERROR_ADD_FAILURE = "error.add.failure";
    /**
     * 资源收费规格已发生变化，暂不支持当前操作，请先退订产品
     */
    String CHARGINGTYPE_CHANGE_RELEASE_PRODUCT = "chargingType.change.release.product";
    String CONTRCT_RENEW_VNC_NUMBER_OPEN_GT = "contrct.renew.vnc.number.open.gt";

    /**
     * 请求失败,输入的信用额度期限不能为空。
     */
    String ERROR_MSG_00032 = "error.msg.00032";

    /**
     * 请求失败,输入的信用额度期限需大于当前时间。
     */
    String ERROR_MSG_00033 = "error.msg.00033";

    /**
     * 请求失败,请输入正确的额度期限。
     */
    String ERROR_MSG_00034 = "error.msg.00034";

    /**
     * 购买失败，账户已冻结！
     */
    String ERROR_ENTITY_OOO12 = "error_entity_00012";

    /**
     * 购买失败，所选套餐包适用产品与开通产品不匹配！
     */
    String ERROR_ENTITY_OOO13 = "error_entity_00013";

    /**
     * 购买失败，当前用户未开通共享资源池！
     */
    String ERROR_ENTITY_OOO14 = "error_entity_00014";

    /**
     * 续订HPC专属资源池失败，因为挂载的文件存储已被冻结
     */
    String ERROR_RENEW_HPC_DRP_ON_FROZEN_SFS = "error_renew_hpc_drp_on_frozen_sfs";

    /**
     * 请求重复提交
     */
    String SUBMIT_REQUEST_REPEATEDLY = "submit_the_request_repeatedly";



    String INNER_MSG_0032 = "inner.msg.00032";

    /**
     * 资源冻结策略为空！
     */
    String INNER_MSG_0033 = "inner.msg.00033";

    /**
     * 资源冻结策略设置失败！
     */
    String INNER_MSG_0034 = "inner.msg.00034";


    /**
     * 所选产品不支持最小冻结金额设置。
     */
    String ERROR_MSG_00035 = "error.msg.00035";

    /**
     * 开票金额大于可开票金额
     */
    String INVOICING_AMOUNT_THAN = "invoicing.amount.than";

    /**
     * 用户认证信息未找到！
     */
    String USER_AUTH_NOT_EXITS = "user.auth.not.exits";

    /**
     * 该资源类型还未关联计费！
     */
    String resource_not_association_charge = "resource.not.association.charge";

    /**
     * 请选择正确的计费规则！
     */
    String SELECT_CORRECT_CHARGE_RULE = "select.correct.charge.rule";

    /**
     *  该云环境不存在，添加资源类型(区域)失败
     */
    String ENV_CLOUD_NOT_EXITS = "env.cloud.not.exits";

    /**
     *  资源类型区域不匹配，添加资源类型(区域)失败
     */
    String RESOURCE_AREA_MISMATCHING = "resource.area.mismatching";

    /**
     *  当前区域下已存在重复资源类型
     */
    String AREA_EXITS_REPEAT_RESOURCE_TYPE = "area.exits.repeat.resource.type";

    /**
     * 当前客户信息异常！
     */
    String CUSTOM_INFO_ABNORMAL = "custom.info.abnormal";

    /**
     *  资源类型未定价！
     */
    String RESOURCE_TYPE_NOT_CHARGE = "resource.type.not.charge";

    /**
     *  无权限操作
     */
    String NO_PERMISSION_OPERATION = "no.permission.operation";

    /**
     *  iam创建超过最大限制！
     */
    String IAM_SUB_USER_EXCEED_MAX_LIMITATION = "iam.sub.user.exceed.max.limitation";

    /**
     *  发票金额不一致，请刷新核对后重试
     */
    String INVOICE_AMOUNT_EXCEPTION = "invoice.amount.exception";

    /**
     *  账单开票中，请刷新后重试
     */
    String INVOICE_AMOUNT_PENDING = "invoice.amount.pending";



    /**
     * info.cancel.relate.success=取消关联成功。
     */
    String INFO_CANCEL_RELATE_SUCCESS = "info.cancel.relate.success";

    /**
     * error.delete.ing=删除中请稍后。
     */
    String ERROR_DELETE_ING = "error.delete.ing";





    /**
     * 用户已被锁定
     */
    String USER_LOCK_ERROR = "user.lock.error";

    /**
     * 存在重复的账户&组织OU信息：[]
     */
    String ACCOUNT_AND_OU_REPEAT = "account.and.ou.repeat";


    /**
     * 类型族  param.valid.cuevalue.range = cueValue参数范围0-********之间
     */
    String VALID_CUEVALUE_RANGE = "param.valid.cuevalue.range";

    /**
     * 类型族  param.valid.name.not.empty=显示名不为空
     */
    String VALID_SPEC_REF_VALUE_NAME_NOT_EMPTY = "param.valid.name.not.empty";

    /**
     * 类型族 param.valid.value.not.empty=值不能为空
     */
    String VALID_SPEC_REF_VALUE_VALUE_NOT_EMPTY = "param.valid.value.not.empty";



    /**
     * 最多上传5个附件
     */
    String FILE_FIVE_SUM_ERROR = "file.five.sum.error";

    /**
     * 最多上传20个附件
     */
    String FILE_TWENTY_SUM_ERROR = "file.twenty.sum.error";



    /**
     * 参数 产品代码 param.valid.productCode.exist=产品代码已存在
     */
    String PARAM_VALID_PRODUCT_CODE_EXIST = "param.valid.productCode.exist";

    /**
     * 产品不存在 error.product.not.exist=产品不存在
     */
    String ERROR_PRODUCT_NOT_EXIST = "error.product.not.exist";
    /**
     * error.product.no.template = 没有配置产品模板或模板已禁用
     */
    String ERROR_PRODUCT_NO_TEMPLATE = "error.product.no.template";
    /**
     * error.product.using=产品已上架，请先下架
     */
    String ERROR_PRODUCT_USING = "error.product.using";
    /**
     * error.product.no.price=当前产品的资源定价状态为未定价，请确认后重试！
     */
    String ERROR_PRODUCT_NO_PRICE = "error.product.no.price";
    /**
     * error.product.no.sfs=高性能计算上架前，请检查弹性文件服务产品是否正常！
     */
    String ERROR_PRODUCT_NO_SFS = "error.product.no.sfs";
    /**
     * error.product.sfs.no.price=高性能计算上架前，请检查弹性文件服务产品是否正常！
     */
    String ERROR_PRODUCT_SFS_NO_PRICE = "error.product.sfs.no.price";
    /**
     * error.product.no.obs="ModelArts上架前，请检查对象存储产品是否正常！"
     */
    String ERROR_PRODUCT_NO_OBS = "error.product.no.obs";
    /**
     * error.product.obs.no.price="ModelArts上架前，请检查对象存储产品是否正常！"
     */
    String ERROR_PRODUCT_OBS_NO_PRICE = "error.product.obs.no.price";
    /**
     * error.product.no.catalog=没有配置产品类别
     */
    String ERROR_PRODUCT_NO_CATALOG = "error.product.no.catalog";
    /**
     * error.product.template.no.exist=产品模板不存在
     */
    String ERROR_PRODUCT_TEMPLATE_NO_EXIST = "error.product.template.no.exist";


    /**
     * 产品模板重复
     */
    String TEMPLATE_NAME_REPEAT = "template.name.repeat";



    /**
     * error.otherapi.curl =调用第三方接口失败
     */
    String ERROR_OTHERAPI_CURL = "error.otherapi.curl";

    /**
     * 当前用户无权操作
     */
    String NOT_HAVE_PERMISSION_OPERATE = "not.have.permission.operate";


    /**
     * 请检查最小购买金额设值范围[0,100000]
     */
    String MIN_AMOUNT_RANGE = "min.amount.range";

    /**
     * 请检查最小冻结金额设值范围[-100000,10000]
     */
    String MIN_FROZEN_AMOUNT_RANGE = "min.frozen.amount.range";


    /**
     * 开始时间应大于当前时间
     */
    String THE_START_TIME_SHOULD_BE_GREATER_THAN_THE_CURRENT_TIME = "the.start.time.should.be.greater.than.the.current.time";


    /**
     * 创建失败，最多只支持三个运营实体。
     */
    String ERROR_ENTITY_00001 = "error_entity_00001";

    /**
     * 删除失败，该运营实体下有账户存在费用数据。
     */
    String ERROR_ENTITY_00002 = "error_entity_00002";

    /**
     * 关联失败，该产品已关联运营实体，不可重复关联。
     */
    String ERROR_ENTITY_00003 = "error_entity_00003";

    /**
     * 删除失败，默认运营实体不能被删除。
     */
    String ERROR_ENTITY_00004 = "error_entity_00004";

    /**
     * 配置通过！
     */
    String ERROR_ENTITY_00005 = "error_entity_00005";

    /**
     * 配置未通过！
     */
    String ERROR_ENTITY_00006 = "error_entity_00006";

    /**
     * 取消关联失败，该产品存在费用数据。
     */
    String ERROR_ENTITY_00007 = "error_entity_00007";


    /**
     * 新密码与旧密码不应该一致
     */
    String THE_OLDPWD_NEWCIPHER_ERROR = "the_oldpwd_newpwd_error";

    /**
     * 关联失败，Modelarts共享资源池与Modelarts专属资源池应属于相同运营实体！
     */
    String ERROR_ENTITY_00008 = "error_entity_00008";

    /**
     * 取消关联失败，该产品下存在套餐包数据。
     */
    String ERROR_ENTITY_00009 = "error_entity_00009";

    /**
     * 删除失败，该运营实体存在关联产品。
     */
    String ERROR_ENTITY_00010 = "error_entity_00010";

    /**
     * 关联失败，暂不支持该类型HPC共享资源池。
     */
    String ERROR_ENTITY_00011 = "error_entity_00011";

    /**
     * 关联失败，请先关联HPC共享资源池。
     */
    String ERROR_ENTITY_00015 = "error_entity_00015";

    /**
     * 请检查产品是否关联模板！
     */
    String ERROR_ENTITY_00016 = "error_entity_00016";

    /**
     * 账户处理中，请稍后重试！
     */
    String ERROR_ENTITY_00017 = "error_entity_00017";

    /**
     * 创建失败，实体名称重复！
     */
    String ERROR_ENTITY_00019 = "error_entity_00019";

    /**
     * 审批失败，该产品的购买限额为{0}元。
     */
    String APPROVAL_LIMIT_MIN_AMOUNT = "approval.limit.min.amount";

    /**
     * 用户名重复，已默认追加后缀。
     */
    String ERROR_ENTITY_00018 = "error_entity_00018";

    /**
     * 创建失败，组织下无此用户！
     */
    String SHARE_QUOTAS_001 = "share_quotas_001";

    /**
     * 创建失败，未查找到文件系统！
     */
    String SHARE_QUOTAS_002 = "share_quotas_002";

    /**
     * 创建失败，组织下无此文件系统！
     */
    String SHARE_QUOTAS_003 = "share_quotas_003";

    /**
     * 创建失败，配额必须大于0！
     */
    String SHARE_QUOTAS_004 = "share_quotas_004";

    /**
     * 创建失败，配额申请容量不可超过SFS文件大小！
     */
    String SHARE_QUOTAS_005 = "share_quotas_005";

    /**
     * 操作失败，请刷新页面后重试！
     */
    String OPERATION_FAILED_NOT_FOUND = "operation.failed.not.found";

    /**
     * 该用户名已被使用。
     */
    String USER_ACCOUNT_USED = "user.account.used";

    /**
     * 获取平台保留账号&组织OU配置失败，请联系系统管理员确认平台保留账号&组织OU配置是否正确。
     */
    String PLATFORM_RETENTION_CONFIGURATION_ABNORMAL = "platform.retention.configuration.abnormal";

    /**
     * 重复的工单分类名称
     */
    String TICKET_CATEGORY_DUPLICATE_NAME = "ticket.category.duplicate.name";

    /**
     * 重复的工单模板名称
     */
    String TICKET_TEMPLATE_DUPLICATE_NAME = "ticket.template.duplicate.name";

    /**
     * 工单名称长度超限制
     */
    String TICKET_CATEGORY_NAME_TOO_LONG = "ticket.category.name.too.long";

    /**
     * 工单类型绑定工单模板不存在
     */
    String TICKET_CATEGORY_BINDING_TEMPLATE_NOT_EXIST = "ticket.category.binding.template.not.exist";

    /**
     * 工单类型绑定的模板未启用
     */
    String TICKET_CATEGORY_BINDING_TEMPLATE_DISABLE = "ticket.category.binding.template.disable";

    /**
     * 工单类型处理人不存在
     */
    String TICKET_CATEGORY_DEAL_NOT_EXIST = "ticket.category.deal.not.exist";

    /**
     * 工单类型绑定的处理人未启用
     */
    String TICKET_CATEGORY_DEAL_DISABLE = "ticket.category.binding.deal.disable";

    /**
     * 工单模板长度超限制
     */
    String TICKET_TEMPLATE_NAME_TOO_LONG = "ticket.template.name.too.long";

    /**
     *修改期间被修改了
     */
    String TICKET_UPDATE_VERSION_UPDATED = "ticket.update.version.updated";

    /**
     * 暂时无法注册用户，已达上限，请联系平台管理员
     */
    String USER_LOGON_UPPER_LIMIT_REACHED = "user.logon.upper.limit.reached";

    /**
     *  公司名称已经被使用
     */
    String CORPORATE_NAME_USED = "corporate_name_used";

    /**
     * 公司和用户不能同名
     */
    String OGR_USER_IDENTICAL = "ogr_user_identical";

    /**
     *  公司和用户名称关联性太强，请修改
     */
    String OGR_USER_STRONG_CORRELATION = "ogr_user_strong_correlation";

    /**
     * 导入文件格式不符合规范，点击下载Excel模板导入
     */
    String IMPORT_FILE_FORM_INCORRECT = "import_file_form_incorrect";

    /**
     * 导入文件内容不符合
     */
    String IMPORT_FILE_CONTENT_INCORRECT = "import_file_content_incorrect";


    /**
     * 国密不存在ccsp_mac字段
     */
    String CCSP_NO_FIELD_CCSP_MAC = "ccsp_no_field_ccsp_mac";

    /**
     * 国密ccsp_mac验证失败
     */
    String CCSP_MAC_VERIFY_FAILED = "ccsp_mac_verify_failed";

    /**
     * 国密升级
     */
    String CCSP_UPGRADING = "ccsp.upgrading";

    /**
     *  资源池剩余卡数不足
     */
    String MC_APPLY_RESOURCES_INSUFFICIENT = "mc_apply_resources_insufficient";

    /**
     *  请求不合法
     */
    String REQUEST_IS_ILLEGAL = "request_is_illegal";

    /**
     *当前角色有用户存在未审批流程
     */
    String ROLE_HAVE_UNAPPROVED_PROCESS = "role.have.unapproved.process";
    /**
     *当前角色有用户存在未审批流程
     */
    String SMS_LIMIT = "sms.limit";

    String ORDER_PENDING_ERROR = "order.pending.error";
    String SET_FAILED = "set.endtime.failed";

    /**
     * 修改配置类型异常！
     */
    String UPDATE_CONFIG_TYPE_ERROR = "update.config.type.error";



    /**
     * 认证失败，请求信息不能为空
     */
    String COMPANY_AUTH_ERROR_1 = "company.auth.error.1";
    /**
     * 认证失败，组织SID信息不能为空
     */
    String COMPANY_AUTH_ERROR_2 = "company.auth.error.2";
    /**
     * 认证失败，请先完成个人认证
     */
    String COMPANY_AUTH_ERROR_3 = "company.auth.error.3";
    /**
     * 子用户不能进行企业认证
     */
    String COMPANY_AUTH_ERROR_4 = "company.auth.error.4";
    /**
     * 认证失败，公司不存在
     */
    String COMPANY_AUTH_ERROR_5 = "company.auth.error.5";
    /**
     * 企业已认证成功,不支持变更为其他认证
     */
    String COMPANY_AUTH_ERROR_6 = "company.auth.error.6";
    /**
     * 企业认证中,不允许重新进行认证
     */
    String COMPANY_AUTH_ERROR_7 = "company.auth.error.7";
    /**
     * 认证失败，请检查企业营业执照图片是否已上传
     */
    String COMPANY_AUTH_ERROR_8 = "company.auth.error.8";

    /**
     * 认证失败，身份类型不能为空
     */
    String COMPANY_AUTH_ERROR_9 = "company.auth.error.9";
    /**
     * 认证失败,法定代表人不能为空
     */
    String COMPANY_AUTH_ERROR_10 = "company.auth.error.10";
    /**
     * 认证失败,被授权人姓名不能为空
     */
    String COMPANY_AUTH_ERROR_11 = "company.auth.error.11";
    /**
     * 认证失败,被授权人身份证号码不能为空
     */
    String COMPANY_AUTH_ERROR_12 = "company.auth.error.12";
    /**
     * 认证失败，请输入正确的被授权人身份证信息
     */
    String COMPANY_AUTH_ERROR_13 = "company.auth.error.13";
    /**
     * 认证失败，被授权人身份证是否已上传
     */
    String COMPANY_AUTH_ERROR_14 = "company.auth.error.14";
    /**
     * 认证失败，请检查授权书是否已上传
     */
    String COMPANY_AUTH_ERROR_15 = "company.auth.error.15";
    /**
     * 认证失败，请输入正确的法人身份证号码信息
     */
    String COMPANY_AUTH_ERROR_16 = "company.auth.error.16";
    /**
     * 认证失败，法人身份证是否已上传
     */
    String COMPANY_AUTH_ERROR_17 = "company.auth.error.17";
    /**
     * 认证失败，法人身份证号码应为空
     */
    String COMPANY_AUTH_ERROR_18 = "company.auth.error.18";
    /**
     * 被授权人姓名长度为2-16个字符,并且不能包含特殊符号
     */
    String COMPANY_AUTH_ERROR_19 = "company.auth.error.19";
    /**
     * 认证失败，身份证未成年
     */
    String COMPANY_AUTH_ERROR_20 = "company.auth.error.20";

    /**
     *  表格内容为空
     */
    String TABLE_CONTENT_NOT_EXIT = "table.content.not.exit";

    String OPERATE_FAILURE = "error.msg.00035";

    /**
     *  越权操作
     */
    String ULTRA_VIRES_OPERATE = "ultra.vires.operate";

    /**
     *  子用户没有解锁权限
     */
    String CHILD_NO_RIGHT = "child.no.right";

    /**
     * 处理人信息错误！
     */
    String PROCESSING_PERSON_INFORMATION_ERROR = "processing.person.information.error";

    /**
     * 模板信息错误！
     */
    String TEMPLATE_INFORMATION_ERROR = "template.information.error";

    /**
     * 下载密码获取失败，请重试！
     */
    String ERROR_MSG_00036 = "error.msg.00036";

    /**
     * 服务器繁忙，请稍后重试!
     */
    String SERVER_BUSY = "server.busy";

    /**
     * 导入客户数量最多1000条
     */
    String IMPORT_CUSTOMER_CAPS = "import.customer.caps";


    /**
     * 导入子用户数量最多1000条
     */
    String IMPORT_SUBUSER_CAPS = "import.subuser_caps";

    /**
     *  资源池不存在！
     */
    String SOURCE_POOL_NOT_EXIT = "source.pool.not.exit";

    /**
     *  资源池状态非冻结失败，不可以重新冻结！
     */
    String POOL_NOT_FROZEN_STATUS = "pool.not.frozen.status";

    /**
     *  请勿重复启用！
     */
    String REPEAT_ENABLE = "repeat.enable";

    /**
     *  请先开通昇腾Modelarts共享资源池！
     */
    String PLEASE_OPEN_MA_RESOURCE = "please.open.ma.resource";

    /**
     *  账户已经欠费，请先充值！
     */
    String ACCOUNT_ARREARS = "account.arrears";



    /**
     * unsubscribe.amount.exceeds = 退款金额部分已开票，不支持退款
     */
    String UNSUBSCRIBE_AMOUNT_EXCEEDS = "unsubscribe.amount.exceeds";
    /**
     * error.data.overload = {0}数据量太大不支持显示
     */
    String DATA_OVERLOAD = "error.data.overload";


    /**
     * cloud.auth.valid.error = 验证云环境信息失败，请联系管理员查看连接日志。
     */
    String CLOUD_AUTH_VALID_ERROR = "cloud.auth.valid.error";

    /**
     * ModelArts服务数据正在下载,请稍后查询下载任务!
     */
    String MODELARTS_DATA_DOWNLOAD_WAIT = "modelarts.data.download.wait";
    /**
     * ModelArts服务数据下载异常,请稍后重试!
     */
    String MODELARTS_DATA_DOWNLOAD_ERROR = "modelarts.data.download.error";
    /**
     * 租户审核中,请稍后重试!
     */
    String TENANT_AUDITED_WAIT = "tenant.audited.wait";
    /**
     * 全限定名已存在
     */
    String ERROR_SYSMJOB_FULLY_QUALIFIED_NAME_EXIST = "error.sysMJob.fully.qualified.name.exist";
    /**
     * 任务标识已存在
     */
    String ERROR_SYSMJOB_QUARTZ_JOB_NAME_EXIST = "error.sysMJob.quartz.job.name.exist";
    /**
     * 仅支持6位验证码
     */
    String ERROR_SMS_CODE_LENGTH = "error.sms.code.length";
    /**
     * 双重审核流程未发布或无审批人
     */
    String ERROR_MULTIPLE_AUDITS_ENABLE = "error.multiple.audits.enable";

    /**
     * 企业名称已被使用
     */
    String ORG_NAME_USED = "org.name.used";

    /**
     *  默认定时器不支持刪除操作！
     */
    String JOB_DEFUALT_NOT_DEL = "default.job.not.del";

    /**
     * 请勿对用户重复审核!
     */
    String USER_AUDIT_REPEAT = "user.audit.repeat";

    /**
     * 规格=[{}],未匹配价格
     */
    String SPECIFICATIONS_DO_NOT_MATCH_PRICES = "specifications.do.not.match.prices";

    /**
     *  账户充值成功！
     */
    String ACCOUNT_RECHARGE_SUCCESS = "account.recharge.success";

    /**
     *  同周期已存在
     */
    String cycle_exists = "cycle.exists";

    /**
     *  商品未上架!
     */
    String SHOP_NOT_APPROVAL = "shop.not.approval";

    /**
     *  商品规格价格异常!
     */
    String SHOP_SPEC_PRICE_ERROR = "shop.spec.price.error";

    /**
     *  商品状态不可交付!
     */
    String SHOP_STATUS_CANNOT_DELIVERED = "shop.status.cannot.delivered";

    /**
     *  商品状态不可验收!
     */
    String SHOP_STATUS_CANNOT_ACCEOPT = "shop.status.cannot.accept";

    /**
     *  无法购买自己的商品!
     */
    String CANNOT_BUY_YOUR_OWN_PRODUCTS = "cannot.buy.your.own.product";

    /**
     *  已关联到商品,无法修改属性!
     */
    String ASSOCIATED_WITH_SHOP_UNABLE_MODIFY = "associated.with.shop.unable.modify";


    /**
     *  订阅状态不可结算!
     */
    String SUB_STATUS_CANNOT_ACCEOPT = "sub.status.cannot.accept";

    /**
     *  商品不存在！
     */
    String SHOP_NOT_EXISTS = "shop.not.exists";

    /**
     *  该用户已拥有模型供应商权限，请勿重复授权！
     */
    String DONOT_AUTHORIZE_AGAIN = "donot.authorize.again";

    /**
     *  该客户存在待审核或已上架商品，不能取消授权！
     */
    String CANNOT_CANCEL_AUTHORIZE = "cannot.cancel.authorize";

    /**
     *  该客户存在未完成订单，不能取消授权！
     */
    String CANNOT_CANCEL_AUTHORIZE_OTHER = "cannot.cancel.authorize.other";

    /**
     *  商品描述存在XSS攻击的String，请修改后提交！
     */
    String EXIST_XSS = "exist.xss";


    String CURRENT_ORDER_NOT_COMPLETED = "current.order.not.completed";

    /**
     *  您已订阅该商品，请过期后再次订阅！
     */
    String NOT_SUBSCRIBE_AGAIN = "not.subscribe.again";

    /**
     *  请勿订阅自己发布的商品!
     */
    String NOT_SUBSCRIBE_MYSELF_SHOP = "not.subscribe.myself.shop";

    /**
     *  创建商品失败!
     */
    String ERROR_CREATE_SHOP = "error.create.shop";

    /**
     *  创建商品中-创建商品版本失败,请重试！
     */
    String ERROR_CREATE_SHOP_VERSION = "error.create.shop.version";

    /**
     *  该商品已经提交过审批!
     */
    String NOT_SUBMIT_AGAIN = "not.submit.again";

    /**
     *  未找到待审批的任务!
     */
    String NO_TASK_FOUND = "no.task.found";

    /**
     *  工单创建过于频繁,请稍后再试!
     */
    String TICKET_CATEGORY_FREQUENT_CONSULTATION = "ticket.category.frequent.consultation";

    /**
     * 工单提交人不存在
     */
    String TICKET_CATEGORY_BUSINESS_CONTACT_NOT_EXIST = "ticket.category.business.contact.not.exist";

    /**
     * 咨询详细信息不存在
     */
    String TICKET_CATEGORY_CONSULTATION_DETAILS_NOT_EXIST = "ticket.category.consultation.details.not.exist";


    /**
     * 指定组织不存在
     */
    String ORG_NOT_EXIST = "org.not.exist";

    /**
     *  流程ID不能为空
     */
    String PROCESS_ID_NOT_NULL = "error.process.id.not.null";

    /**
     *  未查到当前租户关联HCSO信息
     */
    String HCSO_USER_NOT_EXIT = "error.hcso.user.not.exit";

    /**
     *  cce集群不存在
     */
    String CCE_NOT_EXIT = "error.cce.not.exit";

    /**
     * 集群规格[{0}]不存在
     */
    String CCE_FLAVOR_NOT_EXIT = "error.cce.flavor.not.exit";
    /**
     *  集群版本【{0}】不存在
     */
    String CCE_VERSION_NOT_EXIT = "error.cce.version.not.exit";



    /**
     * 该资源不支持缩容
     */
    String NOT_SUPPORT_CAPACITY_REDUCTION = "not.support.capacity.reduction";


    /**
     * 认证用户失败
     */
    String USER_AUTHENTICATION_FAILURE = "user.authentication.failure";

    /**
     * 当前资源池类型删除失败
     */
    String DELETE_HPC_ONLINE_ONE = "delete.hpc.online1";

    /**
     * 删除失败，当前资源池正在被使用
     */
    String DELETE_HPC_ONLINE_TWO = "delete.hpc.online2";

    /**
     * 当前集群标识已存在
     */
    String DELETE_HPC_ONLINE_THREE = "delete.hpc.online3";

    /**
     * HPC共享资源池-二期创建中，请勿执行此操作!
     */
    String HPC_OFFLINE_APPLY_WAITING = "hpc.offline.apply.waiting";
    /**
     * HPC共享资源池-二期退订中，请勿执行此操作!
     */
    String HPC_OFFLINE_UNSUBSCRIBE_WAITING = "hpc.offline.unsubscribe.waiting";
    /**
     * 存储配额已删除!
     */
    String ERROR_DME_OSP_DELETED = "error.dme-osp.deleted";
    /**
     * 存储配额删除中!
     */
    String ERROR_DME_OSP_DELETING = "error.dme-osp.deleting";
    /**
     * 存储配额创建失败！
     */
    String ERROR_DME_OSP_CREATE = "error.dme-osp.create";
    /**
     * 存储配额删除失败!
     */
    String ERROR_DME_OSP_DELETE = "error.dme-osp.delete";
    /**
     * 存储文件系统UUID不存在!
     */
    String ERROR_HPC_OFFLINE_APPLY_FILE_SYSTEM_UUID = "error.hpc.offline.apply.file.system.uuid";
    /**
     * 请申请数据存储大小{0}GB
     */
    String INFO_DME_OSP_QUOTA_EXIST = "info.dme-osp.quota.exist";


}
