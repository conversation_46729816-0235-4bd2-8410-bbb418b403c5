#Wed Jun 12 14:33:01 CST 2019
invoice.downing=The invoice data is downloading, please check the downloading task later!
platform.discount.downing=Platform discount is downloading, please check the download task later!
select.date.error=The selected time cannot exceed %s months
err.msg.1=The distributor does not exist
err.msg.2=License product identification number is invalid
err.msg.3=The full reduction amount must be greater than the coupon amount
err.msg.4=Customer does not exist, please re-select
err.msg.5=The start time cannot be smaller than the current time
err.msg.6=The product ID is not initialized
err.msg.7=Permissions in a license cannot be empty
err.msg.8=This type of image does not belong to the platform and does not support uploading

err.msg.9=Bucket policy error
err.msg.10=Contains unsafe characters, please upload the file again
err.msg.11=The file path is incorrect. Please upload the file again
err.msg.12=Incorrect file format
err.msg.13=Login too often, please try again later
err.msg.14=The user organization does not exist
err.msg.15=Illegal moduleType
err.msg.16=The super administrator cannot be disabled
err.msg.17=An existing key cannot be created again
err.msg.18=The cloud environment type is incorrect
err.msg.19=The start time of collection cannot be later than the current time
err.msg.20=The collection end time cannot be later than the current time
err.msg.21=Unable to create, Cash coupon type is not top-up cash coupon
err.msg.22=Unable to create, Cash coupon type is non-deductible cash coupon
err.msg.23=User account that can log in during the upgrade. Timeout administrator This parameter is mandatory by default and cannot be cancelled
err.msg.24=License no PaaS service


current.status.not.down=The current download status is incorrect
config.not.null=The configuration cannot be empty!
custom.bs.max.ten=A maximum of 10 user-defined identifiers or fields are allowed.
attrKey.not.repeat=Do not configure duplicate attrkeys.
attrName.not.repeat=Do not configure duplicate attrnames.
attrType.is.choos=options cannot be empty when attrType = choose!
options.repeat=Repeat under options!
options.max.ten=The maximum number of options is 10
hold.value.not.config=The value is reserved and cannot be configured.
invalid.top=The TOP information filtering ID is invalid because there is no valid user-defined service ID configuration. Procedure
repeat.top=Do not configure the TOP filtering identifier repeatedly.
invalid.top.two=The TOP information filtering ID is invalid because there is no valid user-defined service ID configuration. Procedure
invalid.top.three=The TOP filtering ID is invalid because the user-defined service ID configuration does not exist. Procedure
resource.analysis.download.error=Resource analysis data download exception, please try again later!
resource.analysis.download.now=Resource analysis data is downloading, please check the download task later!
wx.pay=\u3010 wechat Unified Payment \u3011 Initiate payment
wx.pay.error=[wechat Unified Payment] Abnormal payment
pay.net.error=[wechat Unified Payment] Initiated payment, the network is abnormal
minio.url.error=mminio domain name is incorrect, please reconfigure!


data.sync.handle=The account is disabled during data asynchronous processing!
not.supported.repeat.identification=Duplicate service identifiers are not supported!
sys.config.deletion=The system configuration is missing. Please contact the system administrator.
deposit.not.exist=No top-up record found
amount.discrepancy=The amount in the asynchronous notification does not match the amount in the database


config.test.success=Configuration test successful
config.test.failure=Configuration test failed
pay.config.error=Payment configuration failure
resource.process.audit.pass=The resource class has been approved. Procedure
file.not.error=File does not existerr.msg.bss.*********
err.msg.bss.**********=[%s]Parameter type exception

NEED_SMS_VALI=SMS verification required
operate.interface.failure=Your permissions are insufficient! Contact the enterprise administrator to add functions and service permissions.

ticket.update.error=This type cannot be disabled because some work orders are still in use
ticket.template.update.error=You cannot disable the template because it is still in use
ticket.delete.error=This type cannot be deleted because a work order is still in use
msg.10001=Internal error, please try again.
msg.20001=A business exception has occurred.
msg.29998=Wrong permissions.
msg.29999=Unknown error.
order.action.trace.title.management=Application management
order.action.trace.title.cancel=Cancellation request
order.action.trace.title.submit=Submit an application
order.action.trace.title.pay=completed
common.action.trace.args.null=The call parameter is not allowed to be null. Please check
order.business.msg.1001=Application information cannot be found. Application ID\:{0}
order.business.msg.1002=Application details cannot be found. Application ID\:{0}
order.business.msg.1003=The balance is insufficient to complete the payment
order.business.msg.1004=Specification format failed
order.business.msg.1005=Service strength creation failed \:{0}
order.business.msg.1006=VM creation failed
order.business.msg.1007=Process startup failure
order.business.msg.1008=Incorrect parameter value, {0}
order.business.msg.1009=Incorrect application status, the application has been paid or cancelled\!
billing.business.msg.1001=Unable to find service rate plan, service ID\:{0}
billing.business.msg.1002=Unable to find tariff plan configuration, configuration item \:{0}
billing.business.msg.1003=Unable to find price information of configuration specification, configuration information \:{0}
billing.business.msg.1004=Service code error \:{0}
payment.business.msg.1001=Unable to find recharge record, record ID\:{0}
service.instance.msg.1001=Service instance information cannot be found. Service instance ID\:{0}
service.instance.msg.1002=Service instance associated resource information cannot be found. Service instance ID\:{0}
info.vd.attach.error=Cloud hard disk failed to mount, please try again later\!
info.vd.detach.error=Cloud hard disk failed to release, please try again later\!
info.vd.expand.error=Cloud hard disk expansion failed, please try again later\!
quota.check.error.1001=Quota allocation error
quota.check.error.1002=Unparsed quota type
quota.check.error.1003=Sorry, the application data you applied for has exceeded the quota and cannot be opened again.
cloud.app.message.1001=Application submission
cloud.app.message.1002=Successful application opening
info.insert.success=Created successfully.
info.register.success=Registration successful.
info.update.success=Update successful.
info.delete.success=Deletion successful.
info.remove.success=Remove successful.
info.relation.success=The associated operation was successful.
info.approve.success=The audit was successful.
info.message.success=Sent successfully.
info.inventory.success=Inventory success.
info.in.success=Warehousing is successful.
info.relate.success=Correlate success.
info.out.success=The exit was successful.
info.copy.success=The copy was successful.
info.publish.success=Successful launch.
vm.start.success=Launch successful.
vm.start.reconfig=The adjustment was successful.
vm.migrate.success=The migration was successful.
vm.destory.success=Unsubscribe successfully.
vm.managed.success=Nanotubes succeed.
task.distribute.success=The task was submitted successfully. Please check the result later.
task.issued.success=Task successfully issued, please check the log center for details.
info.ticket.success=Work order was assigned successfully.
info.vm.res.check=Resource check was successful.
info.ticket.execute=The work order was submitted successfully. Please wait for the result.
info.operate.success=Operation successful.
info.operate.freeze.success=Operation successful.Please check the user status later.
info.operate.freezing=Frozen, please check account status later.
info.operate.thawing=Unfreezing, please check account status later.
info.vm.stop=To turn it off
info.vm.start=Boot error. Operate. Failure
info.vm.restart=restart
info.vm.delete=Delete cloud host
info.eip.delete=Remove elastic public network IP
info.vd.delete=Remove cloud hard disk
info.vd.attach=Cloud hard disk mount
info.vd.detach=Cloud disk release
info.vd.expand=Cloud disk expansion
info.department.exist=Departments already exist
warning_service_repeat=Sorry, this service cannot be reordered.
warning.query.failure=Sorry, the data is empty.
warning_ippool_repeat=Sorry, this IP segment cannot be repeatedly added to the resource pool.
warning_ip_repeat=Sorry, this IP segment cannot be repeatedly added to the IP resource.
error.insert.failure=Creation failed.
error.insert.failure.param={0} creation failed.
error.message.failure=Failed to send.
error.query.failure=Failed to retrieve information, data has been updated or deleted.
error.register.failure=Registration failed, data or existing, please try again.
error.update.failure=Update failed. Data has been updated or deleted.
error.delete.failure=Deletion failed. Data has been updated or deleted.
error.approve.failure=The audit failed. The data has been updated or deleted.
error.inventory.failure=Failed inventory, data has been updated or deleted.
error.in.failure=Data has been updated or deleted.
error.relate.failure=The association failed. The data has been updated or deleted.
error.out.failure=The exit failed. The data has been updated or deleted.
error.copy.failure=Replication failed.
error.relation.failure=The association operation failed. The data has been updated or deleted.
error.data.exist={0} already exists, please fill in again.
error.data.relation={0}, cannot be deleted.
error.data.relation.delete.param=Some data cannot be deleted. Please check if {0} is associated.
error.data.failure={0}, the data has been updated or deleted.
error.file.oversize=The file you selected is too large. Please select it again.
error.plan.published=Plans of the same name have been released.
error.publish.failure=Release failed.
error.plan.disabled=The plan has been deactivated.
error.assess.maxnum={0} cannot exceed ten.
error.cancel.order=Application cancellation failed.
error.vm.start=Startup failed.
error.vm.stop=Shutdown failed.
error.vm.restart=Restart failed.
error.vm.reconfig=Adjustment failed.
error.vm.migrate=Migration failed.
error.vm.destory=Unsubscribe failed.
error.vm.managed=Nanotubes failed.
error.vm.scan=The scan failed.
error.task.issued=Task delivery failed.
error.ticket.allocate=Failed to assign work order.
error.vm.res.check=Insufficient resources.
error.biz.user.check=No user is associated with the selected business name.
error.ticket.execute=Failed to submit work order.
error.operate.failure=Operation failed.
error.vm.trial.failure=Creation failed. There is already a trial host.
error.vm.trial.retry.failure=Failed to create, platform temporarily unable to create trial host.
error.vm.trial.env.absent=The cloud environment is not configured
error.valid.failure=There is an error in the current configuration information. Please check and try again
error.ip.unreachable=Unable to start remote connection
error.not.support=Current types are not supported
error.self.inst.res.forbidden=The current resource is associated with a self-serving instance and is not allocated
error.env.exclusive.can.not.share=The current cloud environment is exclusive and does not support redistribution
error.env.exclusive.can.not.remove=The current cloud environment is in exclusive mode and resources cannot be removed
error.env.exclusive.can.not.move=The current cloud environment is in exclusive mode and resources cannot be moved
notification.open_app_notification.title=Application opening notification
notification.open_app_notification.content=< h2 style \= "font - size\: 18 px;
notification.release_app_notification.title=Apply release notification
notification.release_app_notification.content=< h2 style \= "font - size\: 18 px;
notification.app_deploy_start_notification.title=[CMP] application deployment notification
notification.app_deploy_start_notification.content=The application with the name [${appName}] is already deployed.
notification.app_delete_start_notification.title=[CMP] apply release notification
notification.app_delete_start_notification.content=The application with the name [${appName}] has begun to release the deletion.
notification.app_redeploy_start_notification.title=[CMP] applies redeployment notifications
notification.app_redeploy_start_notification.content=The application with the name [${appName}] has been redeployed.
notification.app_stop_start_notification.title=[CMP] application stop notification
notification.app_stop_start_notification.content=The application with the name [${appName}] has been stopped.
notification.app_startover_start_notification.title=[CMP] application startup notification
notification.app_startover_start_notification.content=The application with the name [${appName}] has started.
notification.cloud_host_create_start_notification.title=[CMP] the cloud host creates notifications
notification.cloud_host_create_start_notification.content=The cloud host with the name [${instanceName}] has already been created.
notification.app_deploy_success_notification.title=[CMP] application deployment notification
notification.app_deploy_success_notification.content=The application with the name [${appName}] deployed successfully.
notification.app_deploy_failed_notification.title=[CMP] application deployment notification
notification.app_deploy_failed_notification.content=The application with the name [${appName}] failed to deploy.
notification.app_delete_success_notification.title=[CMP] apply release notification
notification.app_delete_success_notification.content=The application with the name [${appName}] has been released for deletion.
notification.app_stop_success_notification.title=[CMP] application stop notification
notification.app_stop_success_notification.content=The application with the name [${appName}] has been stopped.
notification.app_startover_success_notification.title=[CMP] application startup notification
notification.app_startover_success_notification.content=The application with the name [${appName}] has started successfully.
notification.app_op_unknow_error_notification.title=[CMP] application operation exception notification
notification.app_op_unknow_error_notification.content=The name is [${appName}].
notification.cloud_host_create_success_notification.title=[CMP] the cloud host creates notifications
notification.cloud_host_create_success_notification.content=The cloud host named [${instanceName}] was created successfully.
notification.cloud_host_create_failed_notification.title=[CMP] the cloud host creates notifications
notification.cloud_host_create_failed_notification.content=The cloud host named [${instanceName}] failed to create.
notification.cloud_host_import_notification.title=[CMP] import notification on cloud host
notification.cloud_host_import_notification.content=Cloud host imported successfully.
notification.cloud_host_delete_notification.title=[CMP] cloud host deletion notification
notification.cloud_host_delete_notification.content=Cloud host named [${instanceName}] deleted successfully.
notification.cloud_host_recover_notification.title=[CMP] cloud host recovery notification
notification.cloud_host_recover_notification.content=The cloud host named [${instanceName}] was restored successfully.
notification.service_approval_created_assignee_notification.title=[CMP] approval task
notification.service_approval_created_assignee_notification.content=[CMP] service order flow reminder, you have a [${serviceType}] application form to be approved, application number\: ${orderSn}, please log in the system to check.
notification.service_approval_complete_applicant_notification.title=[CMP] application has been approved
notification.service_approval_complete_applicant_notification.content=[CMP] service sheet flow reminder, [${auditUserName}] approve [${auditStatus}] your [${serviceType}] application, application number\: ${orderSn}, please log in the system to check.
sms.sms_approval_created_assignee_notification.content=[CMP] service order flow reminder, you have a [${serviceType}] application form to be approved, application number\: ${orderSn}, please log in the system to check.
sms.sms_approval_complete_applicant_notification.content=[CMP] service sheet flow reminder, [${auditUserName}] approve [${auditStatus}] your [${serviceType}] application, application number\: ${orderSn}, please log in the system to check.
email.email_approval_created_assignee_notification.title=[CMP] service single process flow reminder
email.email_approval_created_assignee_notification.content=You have an application form [${serviceType}] to be approved, application number\: ${orderSn}, please log in the system to check.
email.email_approval_complete_applicant_notification.title=[CMP] service single process flow reminder
email.email_approval_complete_applicant_notification.content=[${auditUserName}] approve [${auditStatus}] your [${serviceType}] application, application number\: ${orderSn}, please log in the system to check.
notification.alarm_notification_problem.title=[CMP] monitoring alarm notification
notification.alarm_notification_problem.content=You ${envName} environment ${hostName} host (ID\:${instanceId}, IP\:${publicId}), ${tags} ${content}, please pay attention.
notification.alarm_notification_ok.title=[CMP] monitoring alarm notification
notification.alarm_notification_ok.content=You ${envName} environment ${hostName} host (ID\:${instanceId}, IP\:${publicId}), ${tags} ${content}, the alarm has been restored, please pay attention.
sms.regist.content=[CMP] your verification code is\: ${code},Valid within 30 minutes, please do not leak to others
sms.user_import_notification.content=[CMP] dear, ${owner}, you have successfully registered your RightCloud account.
sms.open_app_notification.content=[CMP] dear, ${owner}, RightCloud has opened ${appName} for you, please visit the management center - application management [http\://t.cn/R56OZ3W] for application usage details.
sms.release_app_notification.content=[rongyiyun] dear, ${owner}, your ${appName} application on RightCloud has been successfully released (application ID\: ${appId}), the data has been cleared.
sms.alarm_notification_problem.content=[CMP] you ${envName} environment ${hostName} host (ID\:${instanceId}, IP\:${publicId}), ${tags} ${content}, please pay attention.
sms.alarm_notification_ok.content=[CMP] you ${envName} environment ${hostName} host (ID\:${instanceId}, IP\:${publicId}), ${tags} ${content}, the alarm has been restored, please pay attention.
sms.alarm_active.content=[CMP] you have been set by user ${userEmail} to the message notification under this account.
user.login.success=Login successful
user.login.failed=Login failed
user.logout.success=Exit the success
deploy.version.error=Unable to find application version information\: {0}
deploy.app.instance.error=Unable to find deployment application information\!
cluster.master.node.not.null.error=Cluster initializing master node not found
cluster.not.found.error=The corresponding cluster information could not be found
cluster.node.not.found.error=The node information to join the cluster was not found
node.not.can.be.delete=The host has joined the cluster and cannot be deleted
k8s.no.support.kind=Kind not currently supported
k8s.connected.error=The cluster is temporarily disconnected
k8s.auth.failed=Authentication failed. Please check connection information
app.inst.svc.not.found=Application service information could not be found
cloud.image.delete.error=The cloudy image has been used by the host template and cannot be deleted
cloud.script.delete.error=The script has been used by the host template and cannot be deleted
cloud.env.not.found.error=Failed to find the cloud environment information
cloud.script.env.type.not.fount.error=Unsupported script default value types
self.service.approval.error=The matching self-service default configuration has been removed and approval cannot be completed
cloud.param.not.match=Parameter cannot match {0}
cloud.host.keypair.not.found=The host secret key information could not be found
cloud.monitor.invoke.error=Monitoring data acquisition failed
cloud.host.not.found.error=Unable to find cloud host information
cloud.strategy.not.found.error=Unable to find policy information
cloud.analysis.action.error=The operation cannot be repeated
cloud.name.repeated.error=Name duplicate, creation failed
ldap.connect.success=Server connected successfully
ldap.connect.failure=Server connection failed
license.vm.count.limited=The license allows the number of managed instances to be {0} and the number of currently managed instances to be {1}. Please upgrade the license or clean up the instance resources.
license.private.vm.count.limited=The license allows the number of private env managed instances to be {0} and the number of currently managed instances to be {1}. Please upgrade the license or clean up the instance resources.
server.push.msg.resource.success={0} {1} {2}.
server.push.msg.resource.fail={0} {1} {2} failure.
ak.number.toLimit.error=The number of openapi keys you can create exceeds the upper limit
cloud.pair.key=The key pair name already exists
res.param.status.error=Parameter error\!
system.update.status=System upgrade\!
system.sign.error=Signature error
sytem.error=System error, please try again later
admin.system.error=System error, please contact administrator.
cloud.env.not.exist=The cloud environment has been deleted, please refresh the page and try again.
cloud.env.name.duplicate=Duplicate cloud environment name

bucket.delete.error=The bucket you tried to delete is not empty
vm.renew.warn=self-service host renewal please go to (self-service - > service instance) menu
error.env.share.can.not.modify.mode=The cloud environment is allocated to the current organization in sharing mode, and can only be allocated again in sharing mode.
resource.renew.warn=self-service resource renewal please go to (self-service - > resource service) menu
sms.user_bill_created_notification.content=\u3010CMP\u3011Bill Notice\uFF0CDear ${owner} Your personal monthly bill has been issued, please check the bill information in time\uFF0CBill Time\uFF1A${monthText}\uFF0Corg costs\uFF1A${orgAmount} \uFF0Ctotal\uFF1A\u00A5${amount}
sms.org_bill_created_notification.content=\u3010CMP\u3011Bill Notice\uFF0CDear ${owner}\uFF0CYour ${orgTypeName}(${orgName}) monthly bill has been issued, please check the bill information in time\uFF0CBill Tmie\uFF1A${ monthText}\uFF0Ctotal\uFF1A\u00A5${amount}
error_entity_00016=Please check if the product is associated with a template!
error.msg.00035=Sub users cannot close other users' work orders!-old
shop.supplier.does_not.exist=The product supplier does not exist!

not.have.permission.view=Current user does not have permission to view
the.current.logged.in.user.information.was.not.obtained=The currently logged in user information is not obtained.
the.entered.amount.cannot.be.greater.than.********=The entered amount cannot be greater than ********
account.arrearage=The account is in arrears!
info.cancel.relate.success=Unlinking succeeded.
operation.failed.not.found=Operation failed. Please refresh the page and try again!
error_entity_00001=Failed to create, only supports up to three operating entities.
error_entity_00002=The deletion fails, and there is cost data in the account under the operating entity.
error_entity_00003=The association failed. The product has been associated with an operating entity and cannot be associated repeatedly.
error_entity_00004=The deletion fails, and the default operating entity cannot be deleted.
error_entity_00005=Configuration passed!
error_entity_00006=Configuration failed!
error_entity_00007=Unlink failed, cost data exists for this product.
error_entity_00008=The association fails, the Modelarts shared resource pool and the Modelarts exclusive resource pool should belong to the same operating entity!
error_entity_00009=Unassociation failed, package data exists under this product!
error_entity_00010=Deletion failed, there are associated products for this operating entity.
error_entity_00011=The association failed, and this type of HPC shared resource pool is not currently supported.
error_entity_00012=Purchase failed, account has been frozen!
error_entity_00013=The purchase failed, and the applicable product of the selected package package does not match the activated product!
error_entity_00014=The purchase failed, the current user has not activated the shared resource pool!
error_entity_00015=The association fails. Please associate the HPC shared resource pool first.
error_entity_00017=Account processing, please try again later!
error_entity_00018=The username is duplicated and the suffix has been added by default.
error_entity_00019=Failed to create, duplicate entity name!





err.msg.bss.**********=Value for condition cannot be null
err.msg.bss.**********=AppSecret can not be empty
err.msg.bss.*********=Resource pool type data exception!
err.msg.bss.**********=Parameter exception, enter the correct parameters!
err.msg.bss.*********=The user does not exist!
err.msg.bss.*********=Currently only supports RDS instance-related operations of Alibaba Cloud and AWS
err.msg.bss.*********=The selected billing period is more than three months!
err.msg.bss.*********=Order has been pre-approved and can no longer be rejected
err.msg.bss.**********=The tenant has created AK/SK information for the ModelArts sub-account!
err.msg.bss.**********=The application account has been frozen, please operate after unfreezing
err.msg.bss.**********=Partitions with the same name are not allowed
err.msg.bss.**********=The user is in the process of enterprise authentication, and the subscription package is not supported for the time being
err.msg.bss.*********=Failed to create cloud hard drive via self-service
err.msg.bss.**********=Subnet does not exist or has been deleted, please refresh the page and try again
err.msg.bss.*********=User ID cannot be empty!
err.msg.bss.**********=Billing map code exists identical or empty
err.msg.bss.**********=Do not enter special characters for parameters
err.msg.bss.*********=The main account is disabled, and the sub account is not allowed to log in
err.msg.bss.*********=Mobile phone number verification is abnormal, please contact the administrator
err.msg.bss.*********=Notifications out of range
err.msg.bss.**********=Abnormal conversion factor parameter [integer bit less than or equal to 8 places, decimal bit less than or equal to 2 places]
err.msg.bss.**********=Parameter exception, enter the correct parameter
err.msg.bss.*********=Requisition not found
err.msg.bss.*********=Remarks cannot be blank
err.msg.bss.**********=Value cannot be null or empty!
err.msg.bss.*********=Error starting all timed tasks.
err.msg.bss.**********=Not an approval candidate, no approval authority
err.msg.bss.*********=Whitelist Argument Error
err.msg.bss.**********=Account information is empty
err.msg.bss.*********=Error calling schedule: Connection timed out
err.msg.bss.*********=The current cloud environment does not support this operation.
err.msg.bss.*********=Session expiration unit cannot be empty
err.msg.bss.**********=Successful test!
err.msg.bss.*********=Template name cannot be empty
err.msg.bss.**********=An unlocked instance can only be unlocked by a locked user!
err.msg.bss.**********=Invalid Captcha
err.msg.bss.**********=Firewall object group not found to modify!
err.msg.bss.**********=The currently entered IP segment duplicates an existing IP segment, please check and try again
err.msg.bss.**********=Business segment does not exist
err.msg.bss.**********=Region PvroiderUrl calculation failed!
err.msg.bss.**********=Product category does not exist
err.msg.bss.**********=This host template is already associated with the service and cannot be deleted
err.msg.bss.**********=Please enter the correct {}
err.msg.bss.********=Fail!
err.msg.bss.2071221437=Please authenticate with your real name first
err.msg.bss.936016570=No corresponding product found!
err.msg.bss.2029447872=Firewall is null;
err.msg.bss.863539140=FloatingIP service is absent
err.msg.bss.1648305177=Error converting user entity
err.msg.bss.239284543=Insufficient IAM sub-user quota!
err.msg.bss.1032585623=Rule is already the lowest priority
err.msg.bss.1558315585=Cannot lock an instance locked by another user!
err.msg.bss.1442388670=Captcha length is 6 bits
err.msg.bss.453699498=The job has been assigned but not completed or has been closed!
err.msg.bss.19127072=Cash voucher voided failed.
err.msg.bss.1087625575=Affiliated Customer exists under this Distributor Organization and cannot be deleted
err.msg.bss.1581307481=Failed to delete firewall object group, please try again
err.msg.bss.1338233122=Failed to create firewall object group, please try again
err.msg.bss.*********=The business is not in certification
err.msg.bss.**********=The data you obtained was not found
err.msg.bss.*********=Resource does not exist, delete failed
err.msg.bss.********=The record has been deleted, please refresh and re
err.msg.bss.*********=The cloud environment has been managed in the environment.
err.msg.bss.**********=The contract cannot be modified
err.msg.bss.**********=Cash coupon number does not exist
err.msg.bss.**********=Account opening account cannot be empty
err.msg.bss.*********=Start time cannot be less than [{}]
err.msg.bss.**********=Operating entity data exceeded authority, please confirm the operating entity of the current user
err.msg.bss.**********=File uri is null!
err.msg.bss.********=The account has been disabled
err.msg.bss.*********=Only tenant administrators can activate HPC services
err.msg.bss.*********=UPDATE Fails:
err.msg.bss.*********=The same type of package already exists!
err.msg.bss.**********=Delete ldap user failed
err.msg.bss.*********=Old Password Verification Failed
err.msg.bss.**********=Node is misconfigured and cannot be approved
err.msg.bss.********=Permissions for
err.msg.bss.**********=You cannot configure permissions for your own account!
err.msg.bss.**********=Port check successful
err.msg.bss.**********=Instance name cannot be empty.
err.msg.bss.**********=saveDMac is null and macVerifyFlag is true, class is
err.msg.bss.**********=System error, please contact Operations Administrator.
err.msg.bss.**********=Insufficient authentication review authority, please check the account information!
err.msg.bss.*********=Operation not allowed for this resource state
err.msg.bss.**********=datacenter Id can't be greater than d or less than 0
err.msg.bss.*********=The available quota is insufficient, please recharge and purchase.
err.msg.bss.********=Value [;
err.msg.bss.**********=AKSK duplicate for HCSO account
err.msg.bss.*********=The contract is not available for the resource.
err.msg.bss.**********=The host does not exist, please refresh and re
err.msg.bss.*********=If the modification fails, the resource limit of HPC and SFS should be consistent
err.msg.bss.**********=Not authorized to get the configuration
err.msg.bss.**********=Verify that the current user has permission to operate
err.msg.bss.**********=no support res type
err.msg.bss.*********=File size cannot exceed
err.msg.bss.*********=The backend server group is bound on the listener, please delete the backend service group that changed the listener first
err.msg.bss.**********=User information does not exist
err.msg.bss.**********=Contract source discount not allowed to delete!
err.msg.bss.*********=The package package is not on the shelves
err.msg.bss.**********=SFS application size exceeds platform maximum limit
err.msg.bss.**********=Service instance does not exist
err.msg.bss.**********=Only 6-digit verification code is supported
err.msg.bss.**********=The cloud environment account does not exist or has been deleted.
err.msg.bss.*********=The contract does not exist.
err.msg.bss.**********=Audit Enterprise Failure
err.msg.bss.*********=Cannot convert
err.msg.bss.**********=Record deleted, please refresh and try again
err.msg.bss.**********=Type argument cannot be null
err.msg.bss.*********=Extra long content
err.msg.bss.*********=orgSid is empty
err.msg.bss.**********=Snapshot, please delete the hard disk snapshot first!
err.msg.bss.**********=Cloud environment does not exist
err.msg.bss.**********=Password rule does not comply
err.msg.bss.*********=User state exception, operation failed
err.msg.bss.*********=Up to {} node deletions per month
err.msg.bss.*********=Failed to create iam subuser
err.msg.bss.*********=Template type does not exist
err.msg.bss.*********=Mismatched resource type!
err.msg.bss.*********=Failure to match
err.msg.bss.188289=}]!
err.msg.bss.*********=Could not parse [
err.msg.bss.**********=There are frozen users, this distribution failed
err.msg.bss.**********=Policy is in use and cannot be disabled!
err.msg.bss.*********=Account bms policy modified successfully
err.msg.bss.*********=Failed to synchronize cloud environment
err.msg.bss.**********=Duplicate mailbox for sub-account
err.msg.bss.*********=This task has already been processed, do not repeat processing
err.msg.bss.*********=The size of the uploaded file is less than the minimum value, which will affect the clarity of the picture, please upload again
err.msg.bss.**********=Types of Illegal Business
err.msg.bss.*********=Please contact the Operations Administrator to check if the AK/SK information for the ModelArts sub-account exists in IAM!
err.msg.bss.**********=There is no customer associated with this distributor, so you cannot create a child user directly!
err.msg.bss.*********=Invoice Request Failed
err.msg.bss.********=Get CostSummaryId error: {}
err.msg.bss.*********=Ldap connection failed. Please contact your system administrator to confirm that Ldap authentication is configured correctly.
err.msg.bss.**********=Issue failed, the user already owns the coupon
err.msg.bss.**********=Load of revocation list failed
err.msg.bss.**********=Illegal cloud environment parameters, please check cloud environment parameters
err.msg.bss.**********=Associated instance is already assigned. Cannot delete
err.msg.bss.**********=Metrology data is being generated, please download it later!
err.msg.bss.*********=Currently only supports RDS instance-related operations of HUAWEI CLOUD
err.msg.bss.**********=HPC Service Status Exception
err.msg.bss.*********=Incorrect expiration date
err.msg.bss.**********=Specification family name exists identical or empty
err.msg.bss.54650058=Fortress organ union
err.msg.bss.62406574=Price not queried
err.msg.bss.1464823031=Unsubscribe from exclusive resource pool, ccm call failed
err.msg.bss.744689214=The shared pool is in expansion and cannot be approved
err.msg.bss.1253324408=This status is already in effect and cannot be set repeatedly
err.msg.bss.824250028=MQ instance creation failed! Interface has no return value!
err.msg.bss.1964963679=The current product is not associated with an operating entity
err.msg.bss.403448001=Mailbox send too frequently, please later in the operation
err.msg.bss.683292687=Specifications that have been put on the shelves cannot be deleted!
err.msg.bss.612346356=Cannot delete a package that is already on the shelves!
err.msg.bss.449593906=Modification failed, please try again
err.msg.bss.*********=Automatic renewal approval failed, order does not exist
err.msg.bss.**********=The computing resource associated with the instance could not find a matching network
err.msg.bss.**********=Please check that the start time and end time are correct!
err.msg.bss.**********=setLoginTimeout
err.msg.bss.**********=Disks can be rolled back only if the stopped instance and the current disk does not have a snapshot under creation
err.msg.bss.**********=Role ID that does not exist:
err.msg.bss.**********=Account Status Exception
err.msg.bss.647205=Value for
err.msg.bss.**********=Please inject tracer to TraceUtil.you can use method TraceUtil.injectTracer
err.msg.bss.*********=An enabled policy exists, please disable the enabled policy and try again
err.msg.bss.*********=File path is empty
err.msg.bss.**********=Availability zone does not exist!
err.msg.bss.*********=Parameter\
err.msg.bss.*********=Asset number already exists.
err.msg.bss.**********=Illegal Expiration Days
err.msg.bss.**********=Input parameter not found
err.msg.bss.*********=The mailbox is already in use
err.msg.bss.**********=Download failed! Template does not exist
err.msg.bss.**********=The user is not in the organization
err.msg.bss.*********=Same specification pricing already exists
err.msg.bss.**********=Failed to remove vlan pool!
err.msg.bss.**********=This user is disabled, do not repeat the operation!
err.msg.bss.**********=Elastic ip status is not unused, please select again.
err.msg.bss.**********=Northbound Interface Service Exception
err.msg.bss.**********=The product needs to be removed from the shelves before operating!
err.msg.bss.**********=Wrong cloud environment type parameter!
err.msg.bss.**********=The account does not exist
err.msg.bss.*********=The selected region does not match the cloud environment account, please verify and try again.
err.msg.bss.**********=The storage capacity cannot exceed the limit value
err.msg.bss.*********=No mailing address, please fill in and try again
err.msg.bss.**********=The user is unorganized
err.msg.bss.*********=Subuser has no permissions!
err.msg.bss.*********=BMS Whitelist Created Successfully
err.msg.bss.**********=Specification value cannot be empty!
err.msg.bss.**********=HPC Shared Resource Pool has started to be created, do not perform this operation!
err.msg.bss.*********=ModelArts is not activated!
err.msg.bss.**********=) Please contact the platform administrator
err.msg.bss.**********=Current user status is not available, check user status
err.msg.bss.********=The host has mounted the elastic file
err.msg.bss.*********=Unsupported type
err.msg.bss.*********=GPU resource group does not exist or has been deleted
err.msg.bss.*********=Job jobName = not found
err.msg.bss.*********=Discount is already enabled and cannot be modified
err.msg.bss.1021119918=Business type does not support modification!
err.msg.bss.1343440244=Do not submit frequently
err.msg.bss.414198108=Node not found!
err.msg.bss.1845439562=The current resource is changing or has expired or frozen, and changes are not supported!
err.msg.bss.832959066=Verification successful
err.msg.bss.879190036=Illegal parameter
err.msg.bss.1048361353=Illegal user group parameter
err.msg.bss.906077120=Packages that have been put on the shelves cannot be modified!
err.msg.bss.660732=Volume;
err.msg.bss.1332118329=The cloud environment does not exist or has been deleted.
err.msg.bss.1802643=Threshold cannot be less than 0
err.msg.bss.581179655=The permission group does not exist, please refresh and try again
err.msg.bss.314400541=Shared resource pool does not exist
err.msg.bss.157327773=Duplicate product name
err.msg.bss.1738370990=Insufficient organization permissions to perform operation
err.msg.bss.1753693243=Get Available Zone Information Exception!
err.msg.bss.1990443050=Unsupported operations
err.msg.bss.824600736=The network type of load balancing is a classic network, and binding elastic IP is not supported for the time being
err.msg.bss.**********=The invoice no longer exists, please refresh and try again
err.msg.bss.*********=Templates with associated processes cannot be disabled
err.msg.bss.**********=Package details are being downloaded, please check the download task later!
err.msg.bss.*********=File ID cannot be empty
err.msg.bss.**********=Host requires public network Ip
err.msg.bss.*********=Equal expansion size
err.msg.bss.**********=The account has been frozen
err.msg.bss.**********=Request information cannot be empty!
err.msg.bss.*********=Description up to 110 words
err.msg.bss.*********=User resource is frozen.
err.msg.bss.*********=Unsupported Resource Type
err.msg.bss.**********=Cannot create enum from
err.msg.bss.*********=Failed to synchronize cloud environment No response from RightcloudAdapter.
err.msg.bss.**********={} does not exist, task interrupted
err.msg.bss.*********=The IP filled in has been occupied by the public network IP
err.msg.bss.**********=The current user is not authorized to view
err.msg.bss.**********=Project name already exists
err.msg.bss.**********=IP address non-compliance
err.msg.bss.**********=Mailbox send address is empty
err.msg.bss.********=The instance is being processed and no further action can be taken
err.msg.bss.********=Resources have been taken off the shelves
err.msg.bss.**********=The product of this resource billing is being used, please remove the product from the shelves first!
err.msg.bss.**********=Disable higher permissions for the current user
err.msg.bss.**********=Register Location Address cannot be blank
err.msg.bss.*********=Account has expired
err.msg.bss.*********=Configuration value must be numeric
err.msg.bss.*********=Service Expiration Send Reminder Switch Parameter Not on
err.msg.bss.*********=The current product does not have a corresponding operating entity account
err.msg.bss.*********=Delete Successful
err.msg.bss.**********=The instance has been deleted, please synchronize the instance information!
err.msg.bss.********=No Permission
err.msg.bss.*********=Username greater than maximum length configuration
err.msg.bss.*********=Greater than the maximum number of resources used by the virtual machine
err.msg.bss.**********=Mailbox validation exception, please contact administrator
err.msg.bss.**********=Failed to read file!
err.msg.bss.*********=The current processor does not have approval permission, please view the specific processor in the processing information
err.msg.bss.*********=Standard exclusive cluster file system can only be opened for 5
err.msg.bss.*********=The number of calls from the same source has reached the upper limit today!
err.msg.bss.*********=Package Modified Successfully
err.msg.bss.*********=Object cannot be empty
err.msg.bss.*********=The current license has expired, please renew the license before proceeding
err.msg.bss.**********=Cluster does not exist
err.msg.bss.581169890=The permission group does not exist, please refresh and re
err.msg.bss.349269854=fragment can not be empty
err.msg.bss.806681152=Please do not submit multiple applications
err.msg.bss.798962822=Specification pricing not found!
err.msg.bss.**********=Contains special characters
err.msg.bss.**********=The firewall is already associated with a chain of services!
err.msg.bss.528265304=Expansion application failed
err.msg.bss.551289134=Could not find storage with enough free space.
err.msg.bss.**********=Resource zone does not exist
err.msg.bss.**********=] Insufficient resources, please confirm and resubmit!
err.msg.bss.884141291=Automatic Renewal, Renewal Period cannot be empty
err.msg.bss.**********=More than the number of nodes available in the shared pool!
err.msg.bss.**********=The firewall is in use and cannot be removed
err.msg.bss.304025433=Voucher has been voided and cannot be distributed
err.msg.bss.**********=No bucket instances found to update
err.msg.bss.872562419=Wrong parameter!
err.msg.bss.901733511=The third-party service provider has not configured the relevant process, please contact the service publisher to configure it!
err.msg.bss.349543434=ccsp process failed, reason [no field ccsp _ mac], class is
err.msg.bss.**********=The job has been assigned!
err.msg.bss.**********=Unable to delete system user
err.msg.bss.**********=Resource pool does not have related price configured
err.msg.bss.513162856=Setting discount already exists, duplicate content is:
err.msg.bss.**********=Please remove all associated users first.
err.msg.bss.726330673=Unsubscribe order not found
err.msg.bss.184753434=Request for quote exception: [{}]
err.msg.bss.**********=Announcement ID is blank
err.msg.bss.**********=Non-existent bucket
err.msg.bss.**********=Failed to update firewall object group, please try again
err.msg.bss.544490797=There are successful mount instances under the current mount point, please uninstall and then delete!
err.msg.bss.**********=More than the number of nodes available in the shared pool!
err.msg.bss.686914137=The file does not support this operation!
err.msg.bss.54840825=The current resource has expired or is frozen, please refuse and wait for the user to renew.
err.msg.bss.**********=File parsing error, please confirm whether the file is corrupted!
err.msg.bss.**********=Instances exceeded binding limit
err.msg.bss.**********=Request for quote access failed, please try again later.
err.msg.bss.*********=Unexpected value:
err.msg.bss.**********=Do not repeat downloads
err.msg.bss.**********=Incoming address is empty!
err.msg.bss.**********=The number of compute nodes exceeds the limit and cannot be expanded. Please contact your administrator.
err.msg.bss.*********=The account has not yet reached the valid start time
err.msg.bss.**********=Parameter cannot be empty
err.msg.bss.**********=accountId cannot be empty!
err.msg.bss.**********=TenantUserPass is absent
err.msg.bss.**********=The data center does not exist or has been deleted.
err.msg.bss.**********=Failed to delete identity provider!
err.msg.bss.********=Cloud account does not exist
err.msg.bss.*********=File size limit 50M
err.msg.bss.**********=You cannot create two identical security group rules
err.msg.bss.**********=Review User Successful
err.msg.bss.*********=Illegal parameter
err.msg.bss.**********=Remarks up to 500 words
err.msg.bss.**********=The physical card configuration has a corresponding virtual card configuration and cannot be deleted!
err.msg.bss.**********=AI Shared Resource Pool ID Illegal
err.msg.bss.**********=Incorrect mailbox format
err.msg.bss.**********=Order abnormal, please check!
err.msg.bss.*********=Cannot delete a non-existent user
err.msg.bss.**********=no support metric {}
err.msg.bss.*********=Requisition does not exist
err.msg.bss.*********=Order duration {
err.msg.bss.*********=invocation = = null;
err.msg.bss.********=In resource allocation
err.msg.bss.*********=Failed to add consumption record, consumption type is not supported!
err.msg.bss.**********=Failed to enable IAM permissions, please try again or contact your administrator
err.msg.bss.**********=Failed to get current user
err.msg.bss.**********=Contract template does not exist
err.msg.bss.**********=Query Rule Template [
err.msg.bss.**********=The current user cannot modify his own information
err.msg.bss.**********=VolumeAttachmentApi is absent
err.msg.bss.473997209=The business is already bound to a process
err.msg.bss.**********=HPC exclusive resource pool activation failed!
err.msg.bss.271528655=Incorrect Format for Specified Month
err.msg.bss.**********=The system license will be released on
err.msg.bss.**********=Between values for cannot be null
err.msg.bss.134089269=The product is on the shelves and cannot be operated!
err.msg.bss.**********=Cluster mountable file system exceeded maximum limit
err.msg.bss.208845664=Please select a cash coupon
err.msg.bss.**********=Already in arrears, please recharge before operating!
err.msg.bss.52469=500
err.msg.bss.**********=applicaitonContext is not injected, define SpringContextHolder in applicationContext.xml
err.msg.bss.**********=Illegal billing policy status value!
err.msg.bss.875324720=Storage type cannot be empty
err.msg.bss.**********=Application for invoicing failed, update billing cycle data abnormal, please check
err.msg.bss.297011447=Your specified file is not compliant! Unable to download
err.msg.bss.853028280=Value for cannot be null
err.msg.bss.**********=No phone number filled in, please fill in and try again
err.msg.bss.**********=Password cannot be blank
err.msg.bss.**********=National Secret Connection Test Failed, Please Check National Secret Configuration
err.msg.bss.*********=Start time is greater than or equal to end time!
err.msg.bss.**********=Parent Link ID cannot be empty!
err.msg.bss.**********=Reflection packet address incorrect
err.msg.bss.**********=The selected GPU resource group does not exist
err.msg.bss.**********=Cannot operate on primary account
err.msg.bss.*********=Audit failed, please check whether you choose to pass or reject
err.msg.bss.**********=The current resource is in renewal, repeated renewal is not supported
err.msg.bss.**********=Node information without shrinkability
err.msg.bss.65306=:
err.msg.bss.**********=ConsolesApi is absent
err.msg.bss.747437=Instance
err.msg.bss.*********=Sign check failed.
err.msg.bss.**********=Deleting duplicate iam subuser failed!
err.msg.bss.*********=This validation type is not supported at this time
err.msg.bss.**********=Cloud environment not found
err.msg.bss.**********=Billing policy has been enabled, illegal operation!
err.msg.bss.**********=Send frequently, please try again later
err.msg.bss.**********=Log-in user not found, please log-in and try again
err.msg.bss.*********={} GB is currently in use,
err.msg.bss.**********=The business is not a double audit business
err.msg.bss.**********=The product does not exist!
err.msg.bss.**********=This invoice data does not support displaying details for the time being
err.msg.bss.479510779=Port already exists, rebind
err.msg.bss.1225495949=Current user does not have permission to operate
err.msg.bss.2061045042=Error in user id parameter
err.msg.bss.915802207=ModelArts has an exclusive resource pool that has not been unsubscribed, please unsubscribe from the exclusive resource pool first!
err.msg.bss.1076912469=Update, get Id error: {}
err.msg.bss.1299059892=The current service has been published or is in the process of publishing approval, cannot be operated!
err.msg.bss.421287616=Request address [
err.msg.bss.1373253110=Cannot operate on a resource in an abnormal state
err.msg.bss.1720236067=The environment has been disconnected
err.msg.bss.1775521607=Cloud environment type does not exist!
err.msg.bss.1480814017=Only VMware and FusionCompute environments support the current operation
err.msg.bss.1255465759=User authentication information not found!
err.msg.bss.1688151202=worker Id can't be greater than% d or less than 0
err.msg.bss.*********=Work order completed, do not repeat operation
err.msg.bss.1481638388=Load antisamy policy failed
err.msg.bss.*********=Created Successfully
err.msg.bss.1000339912=Illegal format of company registration telephone number
err.msg.bss.1766604775=resType not found
err.msg.bss.2074391480=Tenant cannot be empty
err.msg.bss.*********=HPC service provisioning failed without LDAP docking
err.msg.bss.1363588193=The snapshot associated with the hard disk timed out and was not destroyed. Please try again later
err.msg.bss.**********=Rule is already top priority
err.msg.bss.*********=No service expiration send reminder send method parameter configured
err.msg.bss.*********=End time cannot be less than effective time
err.msg.bss.*********=This environment has been accessed on the CMP platform, please confirm.
err.msg.bss.********=Non-existent
err.msg.bss.**********=You cannot remove yourself from a user group
err.msg.bss.********=Form template cannot be empty
err.msg.bss.**********=Account password verification failed
err.msg.bss.**********=by code value.
err.msg.bss.*********=This policy has been deleted, please refresh and try again
err.msg.bss.*********=Billing type not found
err.msg.bss.*********=Authentication failed, user name cannot be blank
err.msg.bss.**********=User Deleting
err.msg.bss.*********=Compute resources in the partition could not be allocated because no matching network was found.
err.msg.bss.********=NOT OK!
err.msg.bss.**********=Changing the type of cloud environment is not unique
err.msg.bss.*********=Mailbox cannot be empty
err.msg.bss.*********=Approval Successful
err.msg.bss.*********=Upload failed, data illegal
err.msg.bss.*********=The work order does not exist or has been deleted
err.msg.bss.*********=Listening exists under this load balancer and cannot be removed
err.msg.bss.**********=Only built-in roles restore default permissions
err.msg.bss.**********=The current resource version does not support changes!
err.msg.bss.*********=Bill ID = [{}]-productType = [{}]-No order;
err.msg.bss.2028847658=doReportByMon minDate is null
err.msg.bss.1045743209=The current mount elastic file is frozen!
err.msg.bss.479818070=Port already exists, please fill in
err.msg.bss.979913343=The instance snapshot is being deleted, please view it later.
err.msg.bss.1178635832=Failed to get Bastion Connection, please try again later!
err.msg.bss.565185550=Storage cluster does not exist!
err.msg.bss.956647183=Error pausing task.
err.msg.bss.1150966413=Key file not found.
err.msg.bss.1047765662=The customer does not exist!
err.msg.bss.**********=Modification failed, default navigation is incorrect
err.msg.bss.*********=Expired, please delay the authorization in time, otherwise it will affect the use of the system!
err.msg.bss.*********=} Argument error
err.msg.bss.*********=Illegal customer discount id parameter
err.msg.bss.**********=Please enter the correct protocol type!
err.msg.bss.**********=VMWare instance conversion to template failed
err.msg.bss.*********=Effective occupancy
err.msg.bss.**********=Failed to get account
err.msg.bss.**********=Audit user does not exist, please confirm again!
err.msg.bss.**********=The policy does not exist or has been deleted
err.msg.bss.**********=HCSO Tenant Not Mapped
err.msg.bss.*********=Order Data Exception
err.msg.bss.*********=Cannot be empty
err.msg.bss.*********=Exit failed!
err.msg.bss.**********=Modify user password, password cannot be empty
err.msg.bss.*********=HPC Shared Resource Pool Storage Cluster is not configured or does not exist!
err.msg.bss.*********=Role cannot be modified in this state
err.msg.bss.**********=The current account is not associated with an operating entity and cannot log in to the system. Please contact the platform administrator.
err.msg.bss.**********=Listener already exists
err.msg.bss.**********=File path is not legal
err.msg.bss.*********=This user does not support this unlock user method, please click "Forgot Password" to activate or contact the administrator
err.msg.bss.*********=Not Support Current Service Type
err.msg.bss.*********=The package package was successfully put on the shelves
err.msg.bss.*********=The account has been frozen and cannot be reduced.
err.msg.bss.*********=Unable to get corresponding handler
err.msg.bss.*********=Send failed
err.msg.bss.**********=Data does not exist!
err.msg.bss.*********=ROOT _ URL cannot be empty
err.msg.bss.**********=Failed to add consumption record, consumption record is empty
err.msg.bss.**********=Duplicate organization structure name.
err.msg.bss.**********=Customer information data is being downloaded, please inquire about the download task later!
err.msg.bss.**********=Sorry, demo account has no operation permission
err.msg.bss.**********=The number of resize configuration information nodes is inconsistent with the local database
err.msg.bss.**********=The allocation rate of the data disk storage has exceeded the specified threshold, and the resource cannot be allocated. Please select again.
err.msg.bss.**********=Verification successful!
err.msg.bss.**********=Is a required parameter.
err.msg.bss.*********=No order details
err.msg.bss.**********=The elastic file has a resource mounted and cannot be mounted repeatedly
err.msg.bss.*********=Only supports the continued expansion of exclusive resource pool expansion orders
err.msg.bss.**********=Incorrect parameter!
err.msg.bss.*********=Organization does not exist
err.msg.bss.*********=Order number = [{}], no detailed price for inquiry
err.msg.bss.**********=Package Package Created Successfully
err.msg.bss.*********=Connection failed!
err.msg.bss.*********=cloudEnvType can not be null
err.msg.bss.*********=wait order init ok
err.msg.bss.1216535536=Unsupported Cloud Service Type
err.msg.bss.582992755=Binding information not found
err.msg.bss.972079363=Processing, please try again later.
err.msg.bss.1344771161=The current cluster state cannot be re-expanded [
err.msg.bss.151353805=User resource unfrozen.
err.msg.bss.472508694=Local Cluster Data Exception
err.msg.bss.1968892607=No logged in user information
err.msg.bss.173321838=The number of visual nodes exceeds the limit and cannot be expanded. Please contact your administrator
err.msg.bss.275964975=Relevant cloud environment resources have been removed from the shelves. Contact us if needed!
err.msg.bss.**********=SMS Signing is not configured, please configure SMS Signing in Notification Settings.
err.msg.bss.508136419=No process found in the BPMN model.
err.msg.bss.**********=Current logged in user information not obtained
err.msg.bss.56665379=Wrong license information!
err.msg.bss.**********=Order Details ID = {}, no corresponding OBS
err.msg.bss.223753704=Same or empty spec family code exists
err.msg.bss.**********=Insufficient Shared Resource Pool nodes, the number of nodes must meet [{
err.msg.bss.**********=Renewal operations are not supported for the current resource
err.msg.bss.**********=Recipient name cannot be blank
err.msg.bss.**********=Only running or stopped hosts can be cloned as templates
err.msg.bss.**********=This user does not have built-in role permissions and cannot assign system administrator permissions
err.msg.bss.50641727=ProviderUrl is absent
err.msg.bss.588254963=Package specification cannot be empty!
err.msg.bss.**********=There is a process node that has not selected a processing role or a processing person, please configure and republish!
err.msg.bss.**********=Invalid masks value.Must be between 1 and 32 inclusive.
err.msg.bss.315408795=The user name is already in use
err.msg.bss.790630=build
err.msg.bss.996771796=The organization doesn't exist!
err.msg.bss.758966591=Renewal has been submitted, please check it later.
err.msg.bss.409914578=The selected package has been ordered for more than three months!
err.msg.bss.423276345=Hard disk size cannot be smaller than snapshot size
err.msg.bss.**********=File upload failed
err.msg.bss.230928958=Process definition not found!
err.msg.bss.**********=Non-existent cloud environment
err.msg.bss.222974645=Please use 8-12 English, numeric and character for password
err.msg.bss.987970047=Cloud environment verification failed!
err.msg.bss.668204434=Current bucket policy does not exist
err.msg.bss.81292215=Service Expiration Notification Send Frequency parameter must be a positive integer
err.msg.bss.2096368886=HPC Shared File System Insufficient Allocable Capacity
err.msg.bss.386105363=You cannot modify the status of characters other than Rental Outdoor
err.msg.bss.1331516279=Invalid Application Duration
err.msg.bss.1555738787=Invalid role id
err.msg.bss.*********=No users exist for this enterprise
err.msg.bss.**********=Duplicate subnet name, please check and try again.
err.msg.bss.**********=HPC and SFS resource constraints should be consistent
err.msg.bss.**********=Cannot pass decimals
err.msg.bss.*********=Please select the correct business identity
err.msg.bss.**********=Invalid user id
err.msg.bss.*********=Request parameter is empty
err.msg.bss.**********=Without real-name authentication, please go to authentication.
err.msg.bss.**********=The HCSO account failed to import the ModelArts child user resource, please confirm and try again
err.msg.bss.*********=Mapped tenant does not exist or has been deleted
err.msg.bss.*********=Primary key is empty
err.msg.bss.**********=The allocation ratio of all hosts/stores in the partition has exceeded the set allocation threshold, and resources cannot be allocated.
err.msg.bss.**********=Elastic IP does not exist or is already in use, please refresh and try again
err.msg.bss.*********=msgData
err.msg.bss.*********=Instance does not exist
err.msg.bss.********=Specifications that have been associated with billing cannot be modified or deleted
err.msg.bss.*********=Delete failed, associated accounts cannot be deleted
err.msg.bss.**********=The product does not exist or has been taken off the shelves!
err.msg.bss.********=IAM User SSO Not on
err.msg.bss.*********=Uploaded image format is not legal
err.msg.bss.**********=Wrong User Information!
err.msg.bss.********=No modified parameter values!
err.msg.bss.**********=Master resource not found
err.msg.bss.21040=to
err.msg.bss.**********=Before ModelArts is put on the shelves, please check whether the cloud hard disk products are normal!
err.msg.bss.2929=].
err.msg.bss.********=Integer type when required.
err.msg.bss.*********=Configuration changes are not supported within the deduction range
err.msg.bss.**********=Process node does not now approve roles or approvers
err.msg.bss.*********=This product type is not supported for the time being!
err.msg.bss.**********=Failed to return shared resource pool node information
err.msg.bss.********=Customer Discount cannot be created under Customer Rejected by Approval
err.msg.bss.*********=KeyPairApi create failed.
err.msg.bss.**********=Income and expenditure details are being downloaded, please check the download task later!
err.msg.bss.**********=Customer discount is downloading, please check the download task later!
err.msg.bss.**********=Customer discount data download exception, please try again later!
err.msg.bss.**********=Failed to activate user, keycloak account does not exist please contact administrator!
err.msg.bss.*********=Record Delete Failed
err.msg.bss.*********=Please open ModelArts first
err.msg.bss.**********=Switch not found!
err.msg.bss.**********=The spec value is not in the correct range!
err.msg.bss.**********=Connection name cannot be duplicated
err.msg.bss.*********=Host template not found!
err.msg.bss.**********=File name has disabled HTML tag or first character is special character, please upload file again
err.msg.bss.**********=Error adding one-time task.
err.msg.bss.********=bizContractDetails is empty
err.msg.bss.*********=The RDS instance does not have an extranet address or has been purged
err.msg.bss.**********=Pass configured
err.msg.bss.**********=Configuration has not changed!
err.msg.bss.*********=No corresponding resource found
err.msg.bss.*********=Company and user cannot have the same name
err.msg.bss.**********=No permission for this operating entity
err.msg.bss.**********=Failed to delete user, user has been deleted or does not exist
err.msg.bss.2093129465=Notification frequency out of range
err.msg.bss.1742018318=This process is approved by you, you cannot initiate an application!
err.msg.bss.1528782398=Failed to create exclusive resource pool
err.msg.bss.*********=A security group has already been bound, binding is not allowed again
err.msg.bss.*********=The physical machine has not set out-of-band management information!
err.msg.bss.1112944485=Logon type parameter is not standard. Only [password, key] can be selected
err.msg.bss.1121351715=The number of specification cards cannot be greater than 1000
err.msg.bss.1937183478=Refreeze successful
err.msg.bss.1680282090=There are unprocessed product applications, please do so before deleting them.
err.msg.bss.*********=File format uploaded incorrectly
err.msg.bss.*********=The package package was successfully removed from the shelves
err.msg.bss.**********=Conversion Object Exception
err.msg.bss.********=The current contract does not exist, please refresh and try again
err.msg.bss.**********=Packages already on the shelves do not support new specifications!
err.msg.bss.*********=Original password verification failed
err.msg.bss.**********=Current status cannot be deleted!
err.msg.bss.*********=Search for assets [
err.msg.bss.**********=Master account is not pre-opened
err.msg.bss.**********=Elastic ip is being unbundled, please check later.
err.msg.bss.**********=Please check user information
err.msg.bss.*********=url decoding exception
err.msg.bss.**********=Documents other than this contract cannot be modified!
err.msg.bss.**********=Permission Identity cannot be blank
err.msg.bss.*********=Account does not exist under current organization
err.msg.bss.*********=Modify Password Rules
err.msg.bss.*********=Enterprise has completed certification and cannot be modified
err.msg.bss.**********=Billing policy not found!
err.msg.bss.*********=Failed to send MQ message
err.msg.bss.**********=The selected work order type is disabled, please re-select the work order type and submit!
err.msg.bss.*********=Router already exists
err.msg.bss.*********=Authentication status data is incorrect, please check the data
err.msg.bss.**********=ownerId = {}, OceanStor account does not exist
err.msg.bss.**********=Object group to delete not found!
err.msg.bss.*********=Snapshot create failure, result is null
err.msg.bss.*********=Template for associated process, delete not allowed
err.msg.bss.**********=Operation successful!
err.msg.bss.**********=Query Dynamic Permissions
err.msg.bss.**********=Failed to audit user
err.msg.bss.*********=Please do not submit repeatedly when unsubscribing
err.msg.bss.**********=Delete too many nodes
err.msg.bss.**********=The product belonging to this specification family is in use, please remove the product from the shelves first!
err.msg.bss.*********=Binding Elastic IP returns no data, raw data:
err.msg.bss.*********=Warehouse call list not collected
err.msg.bss.********=User action log is being generated, please download it later!
err.msg.bss.**********=Count is larger than an integer:
err.msg.bss.**********=Duplicate network name, please check and try again
err.msg.bss.*********=Failed to update node information for the specified HPC resource pool
err.msg.bss.729462609=The coupon does not belong to the current operating entity, do not operate
err.msg.bss.1188140929=Cannot contain Chinese characters
err.msg.bss.985977=fragment encoding function exception
err.msg.bss.2081550435=Product does not exist
err.msg.bss.789024387=Operation failed
err.msg.bss.2045839613=Calling mq fails:
err.msg.bss.1985154311=Incorrect time format
err.msg.bss.*********=No bare metal resources were found. Please contact your administrator to check the resource configuration.
err.msg.bss.*********=HPC exclusive resource pool creation failed!
err.msg.bss.**********=Operating entity does not exist!
err.msg.bss.**********=Shutdown is not supported when IAM user SSO is on!
err.msg.bss.**********=Cash coupons distributed successfully
err.msg.bss.**********=Instance snapshot underlying resource no longer exists, please select
err.msg.bss.**********=Incorrect account type
err.msg.bss.*********=The work order does not exist or has been deleted!
err.msg.bss.**********=Operational entity id fetch failed
err.msg.bss.*********=Refresh Successful
err.msg.bss.*********=Download failed
err.msg.bss.**********=You cannot modify the status of accounts at the same level
err.msg.bss.*********=The phone is already in use
err.msg.bss.**********=The selected user is not a child of the current user
err.msg.bss.*********=Exception Request [Logical Resource Pool No Upline Job Deployed]
err.msg.bss.**********=Work order data is being downloaded, please check the download task later!
err.msg.bss.********=Assets [
err.msg.bss.*********=GPU device does not exist or has been deleted
err.msg.bss.**********=Instance [{}] selected instance snapshot does not exist
err.msg.bss.**********=Current user is inoperable
err.msg.bss.**********=The process is in progress, do not repeat submissions...
err.msg.bss.*********=Related products do not exist!
err.msg.bss.**********=resOccRatioList exception
err.msg.bss.**********=map convert bean error
err.msg.bss.**********=Enable failed, only one discount policy for the same product for the same user in the same environment can be enabled
err.msg.bss.*********=File not found
err.msg.bss.*********=The template is disabled, do not disable it again!
err.msg.bss.*********=The adjusted capacity cannot be the same as the current capacity!
err.msg.bss.**********=The record is in use and cannot be deleted
err.msg.bss.*********=Role identity already exists
err.msg.bss.**********=Current logged in user not fetched
err.msg.bss.**********=Price must be greater than 0!
err.msg.bss.*********=Failed to modify account bms policy
err.msg.bss.**********=Rule does not exist
err.msg.bss.**********=Failed to get spec family details
err.msg.bss.*********=The cloud environment corresponding to this elastic file has been deleted!
err.msg.bss.**********=The main account is locked, and the sub account is not allowed to log in
err.msg.bss.**********=Password changed successfully, please log in again
err.msg.bss.**********=The status of the cash coupon is abnormal, and the distribution operation cannot be performed!
err.msg.bss.**********=Specification family not found!
err.msg.bss.**********=Phone duplicate for child account
err.msg.bss.*********=User name minimum length configuration cannot be less than 4
err.msg.bss.*********=This IP already exists, please replace it
err.msg.bss.**********=Failed to get node information
err.msg.bss.*********=Wrong file type, please upload file again
err.msg.bss.**********=There are products and services in use, please unsubscribe and delete them.
err.msg.bss.*********=This account is under review
err.msg.bss.*********=Shared Resource Pool Query Exception
err.msg.bss.**********=Failed to get accessUrl, contact administrator
err.msg.bss.**********=Insufficient user limit
err.msg.bss.********=The selected user does not exist!
err.msg.bss.**********=wait job init ok
err.msg.bss.**********=The cloud account has been deleted, please refresh the page and try again.
err.msg.bss.**********=Only files can be copied, not folders. Please select
err.msg.bss.**********=Cloud environment configuration information could not be resolved correctly.
err.msg.bss.*********=The cash coupon was created successfully
err.msg.bss.**********=The allocation rate of all hosts in the partition has exceeded the set allocation threshold, and resources cannot be allocated.
err.msg.bss.*********=Please enter the correct maximum number of node shrinkage!
err.msg.bss.*********=Failed IAM User ID for HCSO
err.msg.bss.*********=Storage size ranges from 50-500
err.msg.bss.*********=HPC login node configured incorrectly
err.msg.bss.**********=Modified Invoice Setup Information Successfully
err.msg.bss.**********=User Information Error
err.msg.bss.**********=Failed to obtain project information in cloud environment
err.msg.bss.**********=Current action role does not exist
err.msg.bss.*********=tracing or tracer is null, please inject tracing or tracer
err.msg.bss.755109655=Zhang Cash Coupon
err.msg.bss.1172412509=errorMessage
err.msg.bss.728123500=The current operating entity does not have operation authority!
err.msg.bss.1746033110=Resource conversion failed
err.msg.bss.2089435863=Missing required JDBC url.Unable to create DataSource!
err.msg.bss.2049191179=Password reset successful
err.msg.bss.1792376095=Pass Configured Successfully
err.msg.bss.2139379057=The role does not support permission to restore the role!
err.msg.bss.424495865=The notification record does not exist!
err.msg.bss.1331653784=Mapping tenant import in progress, do not repeat operation
err.msg.bss.1887881448=Insufficient permissions for your organization to modify the action
err.msg.bss.1931971019=Front-end protocol port duplicate
err.msg.bss.9631134=You cannot modify the status of the current user
err.msg.bss.1834029111=The current operating entity has no operation authority!
err.msg.bss.1618358003=The current user is not associated with a user group
err.msg.bss.*********=Company registration phone cannot be blank
err.msg.bss.*********=Role name already exists
err.msg.bss.*********=Associated user does not exist or has been deleted
err.msg.bss.*********=No parameters configured
err.msg.bss.**********=Corresponding billing policy not found!
err.msg.bss.*********=Not authorized to process other people's work orders
err.msg.bss.*********=Cloud Host Service is not configured for billing
err.msg.bss.**********=Non-fixed cloud environment mode is not supported!
err.msg.bss.1033779=is absent;
err.msg.bss.*********=Object instance has been deleted.
err.msg.bss.**********=Cloud Environment Accounts
err.msg.bss.**********=Query rule template name cannot be empty
err.msg.bss.**********=Duplicate specification pricing exists under current specification, please check and try again
err.msg.bss.********=instanceName
err.msg.bss.*********=The source hard disk for this snapshot does not exist and cannot be rolled back.
err.msg.bss.**********=Account ID [{}] does not exist
err.msg.bss.*********=Month is empty
err.msg.bss.*********=Illegal user group id
err.msg.bss.*********=The rule has been deleted, please refresh and try again!
err.msg.bss.**********=This type is not supported at this time
err.msg.bss.********=The effective time cannot be greater than the failure time
err.msg.bss.**********=This operation has no permission, please refresh the page to see if the resource exists!
err.msg.bss.*********=No bare metal nodes available, please contact admin to add!
err.msg.bss.*********=] Not available, please check and try again!
err.msg.bss.*********=Query Used Capacity Failed
err.msg.bss.*********=Not authorized to operate
err.msg.bss.**********=Illegal parameter!
err.msg.bss.**********=[{0}] Specification is not configured for billing
err.msg.bss.*********=result = = null;
err.msg.bss.*********=Disallowed operation action!
err.msg.bss.*********=The tenant console cannot reset the administrator account
err.msg.bss.**********=The billing method is not supported for the time being!
err.msg.bss.*********=The host needs to have an access account number and password
err.msg.bss.*********=An associated service instance exists and cannot be deleted
err.msg.bss.**********=There is no error in the whitelist
err.msg.bss.**********=Cannot reset another user password!
err.msg.bss.**********=There is a disabled HTML tag in the file name, please upload the file again
err.msg.bss.**********=Modification failed, role type malformed
err.msg.bss.*********=Company and user names are too closely related, please modify
err.msg.bss.**********=Change is not supported for the current resource type!
err.msg.bss.**********=Review Enterprise Success
err.msg.bss.**********=User information not found!
err.msg.bss.*********=The underlying shared resource pool is in
err.msg.bss.**********=Already exists, please re-enter!
err.msg.bss.*********=cmp mq exception!
err.msg.bss.*********=The current product does not have a corresponding resource pool configured
err.msg.bss.*********=Order number = [{}], no order detail price
err.msg.bss.**********=The file system does not exist, please refresh and try again
err.msg.bss.*********=An associated subnet exists and cannot be deleted
err.msg.bss.1385592996=] Status, please try again later
err.msg.bss.1993179978=This operation is not supported at this time
err.msg.bss.1337680016=FloatingIPApi is absent
err.msg.bss.2062694378=Failed to add package specification
err.msg.bss.1889815029=Failed to add vlan pool!
err.msg.bss.342989730=User Activation Failed
err.msg.bss.789079806=Operation Successful
err.msg.bss.838924551=Modification failed, role identity is incorrectly formatted
err.msg.bss.**********=Do not have permission to view specification data under this operating entity
err.msg.bss.*********=Transmission type:
err.msg.bss.**********=Authentication failed, ID information cannot be empty
err.msg.bss.**********=The file you specified is incorrect! Unreadable
err.msg.bss.*********=The current mount cluster is frozen!
err.msg.bss.*********=File system [ID:
err.msg.bss.**********=) Should be:
err.msg.bss.*********=Process definition not found
err.msg.bss.*********=The specified cloud account cannot be found!
err.msg.bss.*********=Bare Metal Service is not configured for billing
err.msg.bss.*********=No Action User
err.msg.bss.*********=Authentication failed, request information cannot be empty
err.msg.bss.*********=, triggerGroupName =
err.msg.bss.**********=The operating system parameters are not standardized. You can only select [
err.msg.bss.**********=Edit GPU resource group does not exist or has been deleted
err.msg.bss.**********=Firewall object not found!
err.msg.bss.*********=] No billing configured
err.msg.bss.**********=Form Template Exception
err.msg.bss.**********=Incorrect creation information, please confirm
err.msg.bss.*********=Storage Associated Host does not exist
err.msg.bss.**********=Operation Successful, Status Synchronizing...
err.msg.bss.*********=Must be a shutdown machine to change configuration
err.msg.bss.**********=Renewal resource does not exist
err.msg.bss.**********=This port is already in use, please add it again!
err.msg.bss.291875722=The shared file is already bound to a user, please reset the shared file directory
err.msg.bss.220852217=Failed to get cloud environment!
err.msg.bss.182994821=Elastic ip underlying resource no longer exists, please select again.
err.msg.bss.818163596=Unable to connect to
err.msg.bss.1785173285=The national secret service has been successfully opened! Unmodifiable state secret configuration
err.msg.bss.**********=bean convert map error:
err.msg.bss.**********=Please enter a key or key.
err.msg.bss.********=The IP filled in has been occupied by the private network IP
err.msg.bss.642320=not ok
err.msg.bss.**********=Do not repeat actions on approved users
err.msg.bss.********=Recipient address cannot be blank
err.msg.bss.*********=One cloud environment, please delete it first and then continue!
err.msg.bss.*********=This account has been applied to ModelArts, please apply after the unsubscribe is completed!
err.msg.bss.**********=Created successfully, password sent to user mailbox
err.msg.bss.*********=Failed to create BMS whitelist
err.msg.bss.*********=Reset built-in account successfully, please log in again
err.msg.bss.*********=No associated storage resource found!
err.msg.bss.*********=Current query type not supported
err.msg.bss.*********=Resource pool name cannot be the same!
err.msg.bss.**********=Delete function exception
err.msg.bss.*********=Please enter a login type.
err.msg.bss.**********=Billing Policy Enabled Do Not Repeat Enable
err.msg.bss.**********=The total capacity of SFS storage cannot be less than the total amount currently used
err.msg.bss.*********=The requested physical machine has already been assigned. Cannot be assigned again
err.msg.bss.*********=Cloud Environment or Cloud Environment Account
err.msg.bss.********=If the balance is insufficient, please recharge first
err.msg.bss.*********=Mailbox length up to 80 words
err.msg.bss.**********=Missing sysLog server information
err.msg.bss.**********=Begins with a letter and consists of letters, numbers, underscores, underscores, and dots
err.msg.bss.*********=Bastion failed verification
err.msg.bss.**********=Delete Elastic File Failed
err.msg.bss.*********=Firewall rules are in use
err.msg.bss.*********=Failed to modify console password.
err.msg.bss.**********=Account resources unfrozen.
err.msg.bss.*********="Change, do not repeat the operation
err.msg.bss.**********=Please close the tenant elastic bare metal service first!
err.msg.bss.**********=The changed specification does not exist, please synchronize and try again
err.msg.bss.*********=Failed to modify tag!
err.msg.bss.**********=Unable to configure permissions other than the default ones!
err.msg.bss.**********=The current cloud environment account does not exist!
err.msg.bss.*********=] Non-existent;
err.msg.bss.*********=Resource pool specification does not exist!
err.msg.bss.*********=The cloud environment does not exist
err.msg.bss.*********=Maximum username length configuration cannot be greater than 64
err.msg.bss.*********=Authentication failed, current authentication status is:
err.msg.bss.*********=Incomplete billing information, please fill in and try again
err.msg.bss.**********=Modified bucket '
err.msg.bss.**********=no define category!
err.msg.bss.**********=Distributor Administrator account exists under this distributor organization and cannot be deleted
err.msg.bss.*********=Unable to instantiate JDBC driver:
err.msg.bss.*********=Enable failed
err.msg.bss.*********=Successful certification
err.msg.bss.********=Ansible Zip File Failed
err.msg.bss.********=client saved successfully!
err.msg.bss.**********=The cloud environment has been deleted, please refresh and try again
err.msg.bss.**********=Exceeding the HUAWEI CLOUD private network quota limit!
err.msg.bss.********=Self service type is empty
err.msg.bss.*********=The label already exists
err.msg.bss.*********=The original image does not exist.
err.msg.bss.*********=Verification failure
err.msg.bss.*********=Coupon has expired and cannot be distributed
err.msg.bss.*********=Product does not match template type!
err.msg.bss.*********=Between values for property cannot be null
err.msg.bss.**********=Failed to get file contentType, try to add file type to file name (. png/. jpg/. zip...)
err.msg.bss.**********=Please contact the Operations Administrator, the current product configuration does not set resource pricing!
err.msg.bss.315137718=Please do not request frequently, please try again later!
err.msg.bss.1893655570="Unsubscribe from], do not repeat the operation
err.msg.bss.1009561377=Data does not exist
err.msg.bss.983564549=Incorrect payment type
err.msg.bss.915698903=send mq message error:
err.msg.bss.190267765=Password cannot contain Chinese
err.msg.bss.642965944=Error adjusting task.
err.msg.bss.2127464660=Failed to get shared resource pool node information
err.msg.bss.843335026=The expiration time does not comply with the rules!
err.msg.bss.**********=The subnet has
err.msg.bss.**********=HPC exclusive resource pool:
err.msg.bss.821376200=The elastic rule does not exist, please refresh and re
err.msg.bss.**********=Hierarchical Configuration Resolution Exception
err.msg.bss.**********=Mailbox send password is empty
err.msg.bss.**********=Mirror does not exist
err.msg.bss.611328003=A resource pool is using the listener and cannot be deleted
err.msg.bss.675288471=License has expired, import a valid license
err.msg.bss.568870111=Threshold cannot be greater than ********0
err.msg.bss.339861348=This resource template is already associated with a service, please delete the service first!
err.msg.bss.942080398=appKey can not be empty
err.msg.bss.800000438=Error closing all timed tasks.
err.msg.bss.**********=Zone and Disk Type Profile Mismatch Error
err.msg.bss.**********=Hidden customer name cannot exceed 150 characters
err.msg.bss.**********=Renewal operation is allowed only for annual and monthly subscription resources
err.msg.bss.810932304=Update failed
err.msg.bss.643345578=Insufficient balance
err.msg.bss.**********=Backend service group already exists
err.msg.bss.**********=The platform has not enabled the method of unlocking users, please click "Forgot Password" to activate or contact the administrator
err.msg.bss.561800932=No expired contracts
err.msg.bss.**********=SMS verification code successfully verified
err.msg.bss.**********=The current group no longer exists, please refresh and try again
err.msg.bss.**********=Could not find method [
err.msg.bss.923588186=This user is not approved
err.msg.bss.**********=CIDR duplicates other subnets, please check and try again
err.msg.bss.381827496=Failed to get Bastion remote connection information:
err.msg.bss.756152146=Synchronous environment
err.msg.bss.**********=The system license has expired, please contact your administrator to renew the license before proceeding
err.msg.bss.473915648=Illegal user ID!
err.msg.bss.117295047=Incorrect user status cannot be modified
err.msg.bss.**********=Changing is not supported during uninstallation!
err.msg.bss.**********=The current specification family already exists, please rename it!
err.msg.bss.**********=get cloud client error by env id
err.msg.bss.221512740=Modification failed with incorrect data range
err.msg.bss.**********=The number of expanded VNC nodes cannot be less than 1
err.msg.bss.**********=Bucket policy does not exist
err.msg.bss.76560962=HPC pass not configured!
err.msg.bss.**********=MA tenant exclusive resource pool list query exception!
err.msg.bss.83588404=Resource specification [
err.msg.bss.45839932=This template is already enabled, please do not enable it again!
err.msg.bss.416822847=Unsupported Process Type
err.msg.bss.**********=The VPC network does not exist, please refresh and try again
err.msg.bss.351647827=Unsupported data type
err.msg.bss.**********=Passwords contain at least three different rules
err.msg.bss.597415794=There are still resources under this VPC that have not been released. After releasing, try to delete
err.msg.bss.2043330009=Unable to register users temporarily, the number imported exceeds the upper limit of users that can be registered, and there are currently customers (
err.msg.bss.2140323978=[% s] is not nullable
err.msg.bss.2130335647=Billing Policy Disabled Do Not Repeat Disable
err.msg.bss.40502799=Only HCSO, Huawei Private Cloud, OpenStack series, Kingsoft Public Cloud support hard disk expansion
err.msg.bss.1160765990=The underlying hard disk resources no longer exist, please select
err.msg.bss.566566488=Cash voucher distribution failed.
err.msg.bss.1893842783=] not in range [
err.msg.bss.2136762360=Ansible Compression Folder Failed
err.msg.bss.1149419381=Process definition name cannot be duplicated!
err.msg.bss.1564190207=Cannot renew expired resources
err.msg.bss.1442939642=System error, please contact administrator!
err.msg.bss.2145813674=Task not found
err.msg.bss.96609621=Resource Name:
err.msg.bss.740430921=Year is blank
err.msg.bss.831896561=Failed to configure brute force detection!
err.msg.bss.1151127480=Invoicing and related resources that have been invoiced cannot be changed until they expire
err.msg.bss.1163490172=No Pricing Permission for this Specification Family
err.msg.bss.1822775256=Names cannot begin with test and admin!
err.msg.bss.690888043=setLogWriter
err.msg.bss.672622914=noticeId can not be null
err.msg.bss.1466642481=Yuan, please recharge after the purchase.
err.msg.bss.1151785970=Service Expiration Reminder Send Frequency Parameter Not Configured
err.msg.bss.1715570267=The selected file system is not available, please check and try again!
err.msg.bss.23780922=keypair is null
err.msg.bss.1210720179=Delete Elastic IP first when deleting last VPC
err.msg.bss.1958744504=This operation is not supported in the cloud environment!
err.msg.bss.1031443549=productConfig non-json
err.msg.bss.52272715=Contract period cannot be empty
err.msg.bss.1249072459=The virtual network card has been removed. Please refresh the page and try again.
err.msg.bss.1496829557=Product [{}] No VNC Specifications
err.msg.bss.382982342=Error getting product specification!
err.msg.bss.1401732832=User id error
err.msg.bss.1129399905=Recharge record billable amount is empty, billing failed
err.msg.bss.1849032376=Unauthorized operation, no review permission for this invoice
err.msg.bss.1094852577=Request Exception
err.msg.bss.26308970=Error calling schedule
err.msg.bss.********=Association already exists, do not duplicate association
err.msg.bss.*********=The peer certificate has been revoked
err.msg.bss.92335=] Failure\ n;
err.msg.bss.********=Failed to get console URL.
err.msg.bss.*********=Discount policy cannot be deleted in this status
err.msg.bss.**********=Mailbox duplicate
err.msg.bss.*********=If the cash balance is less than 0, resources cannot be opened!
err.msg.bss.**********=Verification failed!
err.msg.bss.**********=The account list is being generated, please download it later!
err.msg.bss.**********=Current user does not have approval permissions
err.msg.bss.**********=Creation failed, the resource ID has been whitelisted!
err.msg.bss.*********=Quota information could not be obtained correctly.
err.msg.bss.*********=Insufficient authority
err.msg.bss.**********=AI large screen interface address must be a URL
err.msg.bss.**********=Department not found
err.msg.bss.**********=Please enter the correct IP!
err.msg.bss.**********=Binding Subnet Exception
err.msg.bss.*********=Required String length is too large:
err.msg.bss.**********=Authentication failed, please check Privacy Statement
err.msg.bss.*********=The default user group does not support modifications
err.msg.bss.**********=Unable to autodetect JDBC driver for url:
err.msg.bss.*********=Without this operating entity data permissions!
err.msg.bss.*********=Subnet card information not found
err.msg.bss.*********=Name Extra Long
err.msg.bss.**********=Review Process Submitted Successfully, Number
err.msg.bss.**********=Connection failed! Please check the domain name, and the account number is consistent!
err.msg.bss.*********=Invoice Request Successful
err.msg.bss.*********=orgSid = {}, no corresponding productType = {}-{},
err.msg.bss.**********=There is elastic ip created
err.msg.bss.**********=This HCSO account failed to configure subusers to user groups, please confirm and try again
err.msg.bss.*********=Account does not exist, id = [{}]
err.msg.bss.**********=Price cannot be empty!
err.msg.bss.*********=No Approval Permission
err.msg.bss.*********=Instance mount information not found, please refresh and try again!
err.msg.bss.**********=Disabled, do not repeatedly disable
err.msg.bss.**********=Connection name cannot be duplicated!
err.msg.bss.**********=The gateway address is already in use by another instance. Please check and try again.
err.msg.bss.**********=Trigger triggerName = not found
err.msg.bss.**********=The number of login nodes exceeds the limit and cannot be expanded. Please contact the administrator
err.msg.bss.**********=Invalid cash voucher
err.msg.bss.*********=End time cannot be less than current time
err.msg.bss.*********=Resize Configuration Information Exception
err.msg.bss.*********=Contract overdue
err.msg.bss.*********=Distribution User is null
err.msg.bss.*********=Type mismatch (method:
err.msg.bss.**********=Current user does not exist
err.msg.bss.********0=Continue expansion is not supported in the current state
err.msg.bss.1769382503=Insufficient amount available
err.msg.bss.1204534196=method can not be empty
err.msg.bss.2013176584=No approval task or already processed
err.msg.bss.1301038826=Transform Encoding Exception in Request Body
err.msg.bss.609490706=Username cannot be empty
err.msg.bss.706069807=Order Details ID = {}, no corresponding ModelArts
err.msg.bss.932204288=Error sending mq message:
err.msg.bss.1639013906=Failed to convert object
err.msg.bss.129431001=The specified service could not be found!
err.msg.bss.367921623=The child user data is abnormal, the superior tenant does not exist!
err.msg.bss.641827552=map to object parameter
err.msg.bss.1780929689=Modified successfully!
err.msg.bss.381942057=JobBean to AddJobParam exception, handled jobType not found:
err.msg.bss.1233528506=Permission group does not exist
err.msg.bss.710679854=Resource pool ID cannot be the same!
err.msg.bss.309548600=You cannot group associate yourself
err.msg.bss.112246097=User name cannot start with admin, test
err.msg.bss.583021025=Invalid monitoring indicator item:
err.msg.bss.68748253=Roller back user
err.msg.bss.168554852=The number of shared resource pool nodes is insufficient, and the number of remaining nodes after operation cannot be less than the configured minimum number of shared resource pool nodes
err.msg.bss.316791035=Currently only supports RDS instance-related operations of Alibaba Cloud
err.msg.bss.1787684215=The instance snapshot is recovering, please review it later.
err.msg.bss.448380951=The same discount name already exists for this customer!
err.msg.bss.1571921428=Invalid mobile phone number
err.msg.bss.951362273=Failed to move
err.msg.bss.700016218=Creation failed, bucket name is already in use by another user
err.msg.bss.1750199273=AWS snapshot description does not support Chinese, please revise it!
err.msg.bss.1083180027=Invalid roleSid
err.msg.bss.596467504=Only an administrator can convert to a public mirror
err.msg.bss.918593667=Export type, currently only supported by month [month]
err.msg.bss.1740665975=Disconnected state of the environment
err.msg.bss.1371465254=Authentication failure, ID card minor
err.msg.bss.2089045382=ApiKey is absent
err.msg.bss.1835930175=Policy name cannot be duplicated!
err.msg.bss.1004130644=Total capacity cannot be less than used
err.msg.bss.1601343667=In Makefile
err.msg.bss.1691783089=The same class can only appear once
err.msg.bss.1069443934=Create invoice exception: Failed to update billing cycle data
err.msg.bss.**********=The virtual network card has been deleted, please synchronize the network card information!
err.msg.bss.**********=keypair service is absent
err.msg.bss.*********=The selected package has been used for more than three months!
err.msg.bss.*********=You cannot modify the exact same instance type
err.msg.bss.**********=Elastic bare metal product abnormal
err.msg.bss.**********=The administrator ID of the order placed on behalf of the customer does not match the current login account ID!
err.msg.bss.*********=Unrecognized producCode:
err.msg.bss.*********=Product name cannot be empty
err.msg.bss.*********=No permission to adjust default rule
err.msg.bss.**********=There is an exception in the user status!
err.msg.bss.**********=Default user group permissions do not support modification
err.msg.bss.*********=Attachment cannot be empty
err.msg.bss.*********=Product [{}] No Product Template
err.msg.bss.**********=Name cannot be empty
err.msg.bss.*********=Upgrading user MA-BMS permissions
err.msg.bss.**********=Duplicate folder name
err.msg.bss.**********=Query asset name cannot be empty
err.msg.bss.**********=Please use this function after real-name authentication
err.msg.bss.*********=This operation is not supported by the cloud environment.
err.msg.bss.**********=Real name, only 1-10 Chinese characters can be entered!
err.msg.bss.**********=The instance name already exists in the environment, please modify it before applying.
err.msg.bss.**********=Invoice Status Modification Failed
err.msg.bss.*********=There is a service on the shelves, unpublish failed!
err.msg.bss.*********=HPC Exclusive Resource Pool Service is not configured for billing
err.msg.bss.**********=[% s] is not in specified parameter range
err.msg.bss.*********=Failed to create OceanStor account
err.msg.bss.**********=[s] Out of range
err.msg.bss.*********=The approved tenant parameters are illegal!
err.msg.bss.**********=Only modifications are allowed: trial calculation, signed, and canceled
err.msg.bss.**********=After selecting the logged out option, no other identification can be selected!
err.msg.bss.*********=Process node not found
err.msg.bss.**********=MA version is not legal!
err.msg.bss.********=Resource pool
err.msg.bss.**********=File not found
err.msg.bss.**********=A voucher that has been revoked cannot be revoked
err.msg.bss.**********=Real name cannot be empty!
err.msg.bss.**********=MA application node exceeds the license capacity limit, please contact the administrator to expand the number of nodes
err.msg.bss.*********=Operation without permission.
err.msg.bss.*********=Please enter the correct verification code, the length of the verification code is 6 digits
err.msg.bss.**********=Order parameter is missing.
err.msg.bss.*********=Original company not found
err.msg.bss.20128636=value!
err.msg.bss.*********=Does not belong to current operating entity, do not operate
err.msg.bss.*********=Delete failed, please try again
err.msg.bss.**********=[% s] is out of convention scope
err.msg.bss.**********=System error, please try again later
err.msg.bss.11489968=Deleted BMS Whitelist Successfully
err.msg.bss.**********=cluster can not be found!
err.msg.bss.*********=Request Type Parameter Exception
err.msg.bss.**********=The user does not exist
err.msg.bss.61090132=Stores in this storage type will exceed the allocation threshold after allocation, and resources cannot be allocated.
err.msg.bss.**********=Create failed, please try again
err.msg.bss.**********=Successful thawing
err.msg.bss.27318110=Validation data cannot be empty!
err.msg.bss.**********=HPC application node exceeds the license capacity limit, please contact the administrator to expand the number of nodes
err.msg.bss.**********=File system does not exist
err.msg.bss.805320749=The customer is not associated with the distributor and cannot create a child user!
err.msg.bss.**********=There are resources in the current cloud environment that have not been deleted
err.msg.bss.422633540=The selected GPU resource group does not have a device associated
err.msg.bss.**********=Distributor cannot associate super administrator
err.msg.bss.208118782=There are still users under this role, deletion failed
err.msg.bss.162700400=Specification family names cannot contain special characters
err.msg.bss.**********=Instance [{}] selected hard disk does not exist
err.msg.bss.291574113=The host is already in use by the cluster and cannot be removed
err.msg.bss.177439832=Package specification modified successfully
err.msg.bss.718648683=Failed to get current logged in user!
err.msg.bss.**********=Hard Disk Created
err.msg.bss.**********=This bulletin has been published and cannot be deleted
err.msg.bss.*********=No login information
err.msg.bss.**********=Re-expansion failed
err.msg.bss.*********=Cloud host [
err.msg.bss.*********=Related configuration not found
err.msg.bss.*********=File save exception! Please confirm the error log!
err.msg.bss.*********=Template does not exist
err.msg.bss.**********=Invalid username
err.msg.bss.*********=Manually frozen accounts can only be manually unfrozen by the operation administrator, and the unfreezing operation automatically triggered by recharge is lower than this priority and cannot be unfrozen
err.msg.bss.*********=Request parameter cannot be empty
err.msg.bss.**********=Ldap synchronization user exception, please try again
err.msg.bss.**********=Cloud account does not exist.
err.msg.bss.*********=File size cannot exceed 1MB, please upload file again
err.msg.bss.*********=Quantity purchased
err.msg.bss.**********=User Id is null
err.msg.bss.*********=You cannot modify the status of your account
err.msg.bss.********=sharePoolId is empty!
err.msg.bss.**********=This operation cloud environment does not support
err.msg.bss.*********=Organization name already exists
err.msg.bss.**********=The company phone format does not comply with the rules!
err.msg.bss.*********=Specification family not found, please refresh and try again!
err.msg.bss.*********=The service expiration parameter value must be an integer
err.msg.bss.*********=Parent cannot be selected for primary organization
err.msg.bss.*********=The default shared directory cannot exceed 10000GB
err.msg.bss.*********=RouterApi is absent
err.msg.bss.**********=User id cannot be empty
err.msg.bss.*********=There is duplicate custom action log in SpEL method:
err.msg.bss.*********=Name cannot be empty!
err.msg.bss.**********=Configured send method is not supported
err.msg.bss.*********=Failed to update billing cycle summary Please check
err.msg.bss.*********=Expansion computing nodes cannot be larger than [
err.msg.bss.89884682=The permission group is already in use by the mount point, please remove the mount point and then delete it!
err.msg.bss.**********=The contract template was not found
err.msg.bss.**********=Distributed coupons are not allowed to be voided
err.msg.bss.**********=getParentLogger
err.msg.bss.**********=Configuration value malformed
err.msg.bss.**********=Failed to create network information
err.msg.bss.**********=Resource pool does not exist
err.msg.bss.716970=GB
err.msg.bss.**********=Failed to add consumption record, current project account is empty!
err.msg.bss.**********=This operation is not supported in the current cloud environment
err.msg.bss.*********=The virtual network card has been deleted, please synchronize the network card information!
err.msg.bss.**********=Corresponding order number = [{}], sfs-size is empty
err.msg.bss.**********=Cloud hard drives do not support snapshot creation for the time being.
err.msg.bss.**********=this context is in a service chain!
err.msg.bss.*********=Unable to get organization information correctly.
err.msg.bss.*********=Failed to update firewall object, please try again!
err.msg.bss.*********=This user cannot modify roles
err.msg.bss.**********=The business type notification already exists!
err.msg.bss.**********=Node cannot be empty!
err.msg.bss.7890753=File is empty, please re-upload valid file
err.msg.bss.**********=Ultra vires derivation
err.msg.bss.**********=Special character check failed
err.msg.bss.**********=The notification has been sent successfully!
err.msg.bss.**********=Service Expiration Send Reminder Switch Parameters Not Configured
err.msg.bss.*********=The system retains the user and forbids the creation of this user!
err.msg.bss.*********=Account does not exist
err.msg.bss.*********=Review Pass or Review Reject Identity cannot be blank!
err.msg.bss.**********=Non-distributed logical routing, can not add interface!
err.msg.bss.**********=Failed to add consumption record, consumption record is not associated with account
err.msg.bss.**********=Unauthorized operation.
err.msg.bss.*********=Failed to create ldap user [{}]
err.msg.bss.**********=Please configure Shared Resource Pool ID
err.msg.bss.********=Verify that the current action user has access to view
err.msg.bss.**********=Handled incorrectly
err.msg.bss.**********=Not connected to HCSO cloud environment
err.msg.bss.*********=Bastion information does not exist!
err.msg.bss.**********=Illegal file format
err.msg.bss.757067=unwrap
err.msg.bss.********=A host in the cloud environment exists in a cluster, please ensure that the host in the cloud environment is not used before deleting the cloud environment
err.msg.bss.*********=Login invalidated
err.msg.bss.*********=The user does not exist.
err.msg.bss.34=This type cannot be disabled because some work orders are still in use
err.msg.bss.*********=Duplicate name reset
err.msg.bss.*********=Value list for cannot be null or empty
err.msg.bss.**********=Parameter type exception
err.msg.bss.1182834913=Elastic ip is being deleted, please check it later.
err.msg.bss.508933341=Shared resource pool, instance cannot be created without storage cluster configured
err.msg.bss.46839705=The instance snapshot underlying resource no longer exists. Please select it again.
err.msg.bss.977373788=] No corresponding specification:
err.msg.bss.865571943=Encryption and decryption exception
err.msg.bss.1154368745=Configured target specification does not exist, please synchronize and try again
err.msg.bss.1101937241=No resources in the region
err.msg.bss.818791318=User group name already exists
err.msg.bss.1821799024=Value for property cannot be null
err.msg.bss.735250124=The user is disabled
err.msg.bss.724608768=Cloud Environment Verification Failed
err.msg.bss.1423982624=The price must be less than or equal to 1 billion!
err.msg.bss.33892673=Copying this package will cause the name to exceed the maximum number of characters. Copying is not supported!
err.msg.bss.45=-
err.msg.bss.178073412=There are duplicates in setting discount, please check and submit
err.msg.bss.44=,
err.msg.bss.1291505766=Please fill in the cycle correctly!
err.msg.bss.1310047746=Operation failed!
err.msg.bss.1810809622=Cloud environment not found!
err.msg.bss.1833705412=Permission identity cannot be duplicated
err.msg.bss.49=1
err.msg.bss.929875085=Specification not changed, please confirm.
err.msg.bss.*********=This work order has been scored, please do not repeat the rating
err.msg.bss.658606=message
err.msg.bss.*********=User information not found
err.msg.bss.*********=Permission exception!
err.msg.bss.**********=Shared resource pool remaining capacity is less than requested capacity
err.msg.bss.*********=Greater than the number of nodes the shared pool is running!
err.msg.bss.********=moduleType cannot be empty!
err.msg.bss.*********=Current method not support.
err.msg.bss.**********=Account arrears threshold can only be an integer
err.msg.bss.*********=Bucket name already exists
err.msg.bss.*********=The collection start time cannot be greater than the collection end time!
err.msg.bss.*********=End time exception!
err.msg.bss.*********=An associated security group exists and cannot be deleted
err.msg.bss.*********=Argument error
err.msg.bss.********=Security Group
err.msg.bss.*********=The allocation rate of system disk storage has exceeded the specified threshold. Resources cannot be allocated. Please select again.
err.msg.bss.**********=unsupported method: s
err.msg.bss.**********=Operation fails:
err.msg.bss.********=Contact your system administrator to configure the organizational hierarchy.
err.msg.bss.**********=You have exceeded your authority!
err.msg.bss.**********=An application defined by this process exists and cannot be deleted at this time
err.msg.bss.*********=Specify Month Parameter Exception
err.msg.bss.**********=The product associated with is already on the shelves and cannot be disabled
err.msg.bss.1154926354=Failed to create Keycloak user!
err.msg.bss.1411321550=The current cloud environment is not supported
err.msg.bss.1791307836=The user is not bound to hcso
err.msg.bss.1771583025=Billing cycles can only include:
err.msg.bss.205602848=The payment method is not activated
err.msg.bss.1612394054=Connection type is not supported.
err.msg.bss.1080222154=Please select a Project Owner
err.msg.bss.518484020=The business has been registered
err.msg.bss.1109359604=Shared Resource Pool Change Exception
err.msg.bss.789955873=The virtual IP has already been bound to this server, and the binding cannot be repeated
err.msg.bss.1966439109=Deployment online does not support releasing resources for the time being!
err.msg.bss.1913013847=Unrecognized action type:
err.msg.bss.1105253524=Resource occupancy
err.msg.bss.1428719633=invoker = = null;
err.msg.bss.1814506067=Correlation role permission error, cannot associate distributor
err.msg.bss.758595482=This distributor is already associated, do not repeat the association
err.msg.bss.58537174=The selected configuration is too high to have a host available.
err.msg.bss.1330637131=error, please concat admin
err.msg.bss.161508477=Elastic ip is being bound, please check later.
err.msg.bss.20398661=Between values for
err.msg.bss.1759464620=This user is locked, the platform does not support this unlock user method, please contact the administrator to activate the user
err.msg.bss.310494080=The network data is abnormal, please synchronize the network data first
err.msg.bss.1099260089=The network has
err.msg.bss.1724109193=ownerId cannot be empty
err.msg.bss.1163425443=Announcement time cannot be less than current time
err.msg.bss.*********=The RDS instance does not exist or has been deleted
err.msg.bss.*********=Successfully submitted supplementary procurement application
err.msg.bss.1256911884=, please modify.
err.msg.bss.1245515984=Resource does not exist!
err.msg.bss.1139791=Package Specification Deleted Successfully
err.msg.bss.83924935=Overreach operation!
err.msg.bss.1425935868=Audit, company does not exist
err.msg.bss.1665617925=Failed to delete access key!
err.msg.bss.*********=no support instance
err.msg.bss.*********=Username Less Than Minimum Length Configuration
err.msg.bss.1723906408=The current application cannot perform this operation, please remove from the self service.
err.msg.bss.1959945636=The query result is empty, please check the data
err.msg.bss.**********=The virtual IP has not been bound to the server and cannot perform an unbind operation
err.msg.bss.**********=No permissions for this work order action
err.msg.bss.**********=Cloud environment not found
err.msg.bss.**********=The platform does not support this method of unlocking users, please click "Forgot Password" to activate or contact the administrator
err.msg.bss.**********=The difference between the end time and the start time must be within 24 hours!
err.msg.bss.**********=Account resources have been frozen.
err.msg.bss.*********=Query user [
err.msg.bss.*********=Bucket does not exist, create bucket first
err.msg.bss.**********=FD authentication information failed to verify, the error message is:
err.msg.bss.**********=TenantUserName is absent
err.msg.bss.*********=Host instance state cannot bind elastic ip!
err.msg.bss.**********=Approving creation of unsupported HPC version
err.msg.bss.*********=No compute node resources were found. Contact your administrator to check the compute node resource configuration. Specifications: [
err.msg.bss.*********=No corresponding user found
err.msg.bss.**********=The virtual network card does not exist, please refresh and try again
err.msg.bss.**********=Exception of current customer information!
err.msg.bss.*********=Only HPC exclusive expansion orders support continued expansion
err.msg.bss.**********=Authentication failed, please enter the correct ID card information
err.msg.bss.********=Unauthorized operation of other operating entity products
err.msg.bss.*********=Renewal resource no longer exists
err.msg.bss.********="Renewal of], please do not repeat the operation
err.msg.bss.*********=The import file format does not meet the specification, click Download Excel Template Import
err.msg.bss.**********=Contract Renewal Abnormal
err.msg.bss.**********=Insufficient account balance, audit failed
err.msg.bss.*********=doReportByDay minDate is null
err.msg.bss.*********=Deleted user exists and cannot be deleted again
err.msg.bss.**********=Partition does not exist or has been deleted.
err.msg.bss.**********=This operation is not supported yet!
err.msg.bss.**********=Upload failed with null upload data
err.msg.bss.*********=Current account does not exist
err.msg.bss.**********=Bucket policy does not exist, create
err.msg.bss.**********=Error adding timed task.
err.msg.bss.**********=Exclusive Resource Pool Change Exception
err.msg.bss.**********=The account is not bound to a corresponding mapping tenant
err.msg.bss.**********=The cloud environment is not supported for this operation
err.msg.bss.*********=Name already exists
err.msg.bss.*********=Wrong recharge account!
err.msg.bss.*********=Argument is missing a value for the dismiss attribute, and cannot determine which node to dismiss to. Please check the parameters
err.msg.bss.**********=Description cannot contain symbol *, please re-enter!
err.msg.bss.**********=Duplicate HCSO account name
err.msg.bss.**********=You cannot modify the status of roles other than child users
err.msg.bss.**********=Billing configuration not found!
err.msg.bss.**********=Already in certification, please do not submit repeatedly
err.msg.bss.**********=Please contact the Operations Administrator, the current product configuration resource does not match the product type!
err.msg.bss.*********={} Environment does not exist
err.msg.bss.*********=], does not exist.
err.msg.bss.********=The cloud environment account is associated with
err.msg.bss.*********=File size exceeded limit, please upload again
err.msg.bss.**********=Failed to create user key association table!
err.msg.bss.**********=A cloud environment cannot be deleted!
err.msg.bss.*********=Effective time cannot be less than current time
err.msg.bss.**********=There are unused coupons, please do it before deleting them.
err.msg.bss.**********=Service Expiration Notification Send Frequency parameter value must be an integer
err.msg.bss.**********=This user is enabled, do not repeat!
err.msg.bss.*********=Resource pool type cannot be empty!
err.msg.bss.**********=Policy is in use and cannot be deleted!
err.msg.bss.**********=Failed to obtain Ldap authentication configuration. Please contact your system administrator to confirm that Ldap authentication configuration is correct.
err.msg.bss.*********=User Password Authentication Successful
err.msg.bss.*********=Mismatched file system!
err.msg.bss.**********=Unsupported Remote Connection Mode
err.msg.bss.*********=Illegal account id
err.msg.bss.*********=Cloud environment not found, please try to refresh!
err.msg.bss.*********=Purchase quantity {
err.msg.bss.**********=Non-existent Availability Zone
err.msg.bss.*********=Process node not found
err.msg.bss.**********=Notification policy does not exist
err.msg.bss.**********=User has an unfinished HPC task
err.msg.bss.**********=Currently logged in user does not exist
err.msg.bss.*********=Bulk import failed, please check the uploaded file format content.
err.msg.bss.*********=The current function module does not exist!
err.msg.bss.********=Wrong type.
err.msg.bss.**********=Unable to query invoices because the current user has no associated account
err.msg.bss.**********=Invoice Status Modified Successfully
err.msg.bss.********=Cloud Environment
err.msg.bss.**********=contractId is empty
err.msg.bss.*********=There is no corresponding user group under this HCSO account, please confirm and try again
err.msg.bss.*********=Creation failed, resource group name already exists
err.msg.bss.**********=Unknown HTTP method name:
err.msg.bss.**********=Wrong Renewal Type
err.msg.bss.*********=current cloudEnvType not support.
err.msg.bss.**********=Cannot operate on users in the same user group
err.msg.bss.**********=Host mounted, please uninstall first
err.msg.bss.**********=Organization Id is empty
err.msg.bss.**********=Elastic File Service is not configured for billing
err.msg.bss.*********=Package package order is being downloaded, please check the download task later!
err.msg.bss.**********=Associated snapshot not deleted complete
err.msg.bss.**********=Unrecognized code:
err.msg.bss.*********=Get Id error: {}
err.msg.bss.**********=Failed to resolve module configuration!
err.msg.bss.**********=Cloud Account ID cannot be empty
err.msg.bss.**********=Elastic ip bound resource instance does not exist or has been deleted
err.msg.bss.**********=Ldap account synchronization is in progress, please do not repeat the operation
err.msg.bss.*********=Generated Exception: Expenditure Detail is empty!
err.msg.bss.********=Virtual network card binding failed!
err.msg.bss.**********=Wait for process review to be completed before operation, no.
err.msg.bss.********=Unfreeze user LDAP synchronization exception, please try again
err.msg.bss.**********=Mailbox prefix 4-16 characters
err.msg.bss.********=Not Found
err.msg.bss.65281=!
err.msg.bss.*********=Discount is already enabled and cannot be deleted
err.msg.bss.**********=The activity has not yet started, and the cash voucher is temporarily unavailable
err.msg.bss.**********=You cannot configure permissions on yourself
err.msg.bss.*********=The mac address already exists.
err.msg.bss.**********=Host instance underlying resources no longer exist, please select
err.msg.bss.*********=Submission Successful
err.msg.bss.********=Resource has expired
err.msg.bss.*********=Policy cannot be empty
err.msg.bss.1323473283=The user is in the process of enterprise authentication, and the purchase of products is not supported for the time
err.msg.bss.1181304578=RDS instance-related operations are not supported for the current cloud environment
err.msg.bss.958771702=resourceId = {} Resource does not exist;
err.msg.bss.316347997=The directory is out of capacity, please reassign
err.msg.bss.534209134=IAM Custom Permission Policy validation exception, please try again.
err.msg.bss.263692571=Recipient is not filled in, please fill in and try again
err.msg.bss.806482332=Only undistributed cash vouchers can be voided.
err.msg.bss.898537936=Application submitted too fast, please try again later
err.msg.bss.899185304=Only tenant administrators can unsubscribe from the service
err.msg.bss.1371897254=IP is in use and cannot be deleted
err.msg.bss.**********=Link level error
err.msg.bss.*********=Validation is too frequent, please use the
err.msg.bss.*********=Start time cannot be empty!
err.msg.bss.**********=Please fill in the correct associated contract
err.msg.bss.********=span = = null;
err.msg.bss.**********=Failed to delete user key association table!
err.msg.bss.**********=The instance change success flag was not successfully obtained, please synchronize the instance to view it later.
err.msg.bss.*********=No account found
err.msg.bss.**********=The phone number is already in use
err.msg.bss.**********=cannot be null
err.msg.bss.**********=cluster is null!
err.msg.bss.*********=The pricing amount of the annual and monthly subscription specifications cannot be negative and the decimal places cannot exceed 2 places
err.msg.bss.**********=This rule set is already associated with a firewall, please delete the firewall first!
err.msg.bss.*********=[% s] rule mismatch
err.msg.bss.*********=The current user does not have permission to operate!
err.msg.bss.**********=Authentication failed, user SID information cannot be empty
err.msg.bss.*********=Private Network/Subnet cannot be null
err.msg.bss.**********=, jobGroupName =
err.msg.bss.**********=session expire or not ready
err.msg.bss.**********=Failed to create access key!
err.msg.bss.*********=Approval failed due to insufficient user balance.
err.msg.bss.*********=url can not be empty
err.msg.bss.**********=Work order not found
err.msg.bss.1937238897=Refreeze failed
err.msg.bss.398515668=There is an unused credit limit, please do it before deleting it.
err.msg.bss.21710839=name:
err.msg.bss.1679285401=System exception, please contact administrator
err.msg.bss.1003759958=Grouping does not have modifications
err.msg.bss.193451409=Cannot modify admin user information
err.msg.bss.2139930831=[% s] is not editable
err.msg.bss.1038467=Organization
err.msg.bss.100187311=There are unused cash coupons, please delete them after operation.
err.msg.bss.*********=Range value maximum exceeds limit value
err.msg.bss.*********=Expansion contract cannot be blank
err.msg.bss.*********=Create Hard Disk Task Preparing
err.msg.bss.**********=The instance does not exist, please refresh and try again
err.msg.bss.**********=Modification failed, user name is not in the correct format
err.msg.bss.**********=Failed to resolve cloud environment type configuration!
err.msg.bss.**********=The account was not queried
err.msg.bss.**********=SMS url failed validation
err.msg.bss.**********=Account id mapping does not exist
err.msg.bss.**********=Reexpansion fallback node process failed
err.msg.bss.*********=HPC Resource Analysis Fill Graph is downloading, please check the download task later!
err.msg.bss.**********=Illegal user
err.msg.bss.********=Failed to get logged in user
err.msg.bss.**********=Discount in enabled exists, delete failed!
err.msg.bss.*********=Selected bill details are more than three months old!
err.msg.bss.**********=no support deploy type!
err.msg.bss.**********=Customer not enabled, policy creation failed
err.msg.bss.**********=User list is being generated, please download it later!
err.msg.bss.*********=The current user is not legal, please try again
err.msg.bss.*********=Period must be greater than 0!
err.msg.bss.**********=The current user is not authorized to operate
err.msg.bss.*********=The listener does not exist
err.msg.bss.********=No SMS Platform Configured, Refresh Failed
err.msg.bss.*********=Work order id error
err.msg.bss.**********=Cloud account information not found!
err.msg.bss.**********=Service instance does not exist or has been deleted
err.msg.bss.**********=User authentication information not found!
err.msg.bss.*********=This account has no permissions
err.msg.bss.**********=Invalid permission identity
err.msg.bss.**********=The contract has been terminated, please do not repeat the termination!
err.msg.bss.*********=The current product is not associated with the current operating entity
err.msg.bss.*********=User Password Authentication Failed
err.msg.bss.*********=Process template name already exists
err.msg.bss.**********=Failed to add consumption record, the current project account has insufficient balance!
err.msg.bss.********=Error building scan object.
err.msg.bss.*********=Service does not exist
err.msg.bss.*********=base is absent
err.msg.bss.**********=Failed to delete HCSO IAM child user
err.msg.bss.**********=Current Action Message Notification does not exist
err.msg.bss.*********=Product parameters are abnormal!
err.msg.bss.*********=Order Status Exception
err.msg.bss.*********=Request node is only allowed to pass
err.msg.bss.*********=Redis connected failed.
err.msg.bss.**********=Please select the Elastic IP to bind
err.msg.bss.*********=Cannot start with admin
err.msg.bss.**********=OrderID = {} corresponding resourceId is empty
err.msg.bss.*********=Incorrect JobOperate parameter
err.msg.bss.**********=Incorrect initial password
err.msg.bss.*********=Corresponding installation platform not found
err.msg.bss.*********=SMS provider not supported
err.msg.bss.*********=Delete failed
err.msg.bss.**********=Incorrect admin address format!
err.msg.bss.**********=chargeType is invalid
err.msg.bss.**********=The current security group cannot perform this operation, unbind the instance before deleting
err.msg.bss.488087415=Business data does not exist!
err.msg.bss.409080694=VPC contains associated subnets, no deletion operation is allowed
err.msg.bss.**********=The network has been deleted, please refresh and try again
err.msg.bss.973386427=Download task does not exist
err.msg.bss.**********=Collection time cannot be greater than the current time!
err.msg.bss.**********=Billing policy not found
err.msg.bss.**********=Configuration failed, the resource ID has been set by another instance!
err.msg.bss.**********=HPC exclusive resource pool name does not meet the specification, please re-enter!
err.msg.bss.**********=The operation fails:
err.msg.bss.649384401=Unsupported purchase duration
err.msg.bss.662058302=The bill details are being downloaded, please check the download task later!
err.msg.bss.1297087746=Only stopped instances can roll back the disk
err.msg.bss.722092998=Resource type has no corresponding cloud environment
err.msg.bss.667021700=The hard disk is associated with
err.msg.bss.285305065=Please enter the correct mobile phone number
err.msg.bss.943228649=), maximum number of supported customers (
err.msg.bss.1557390068=The organization level has exceeded the upper limit and cannot be created.
err.msg.bss.**********=Service not found!
err.msg.bss.753830327=There is a configuration in use for this specification family, and the deletion failed!
err.msg.bss.492838315=No fortress information found!
err.msg.bss.956215323=Disable failed
err.msg.bss.**********=User does not exist
err.msg.bss.**********=Approval task does not exist or has been processed by someone else
err.msg.bss.93=] Failure;
err.msg.bss.91=[
err.msg.bss.206079445=The information is incorrect, please confirm
err.msg.bss.522979042=License has expired!
err.msg.bss.452404517=Cannot start with test
err.msg.bss.**********=Copied length exceeds template name length limit
err.msg.bss.324563825=Disable modifying the role of the current user
err.msg.bss.*********=Incorrect upload path
err.msg.bss.*********=Resource not found
err.msg.bss.**********=Parameter cannot be empty!
err.msg.bss.**********=The file size is too large, please upload it again
err.msg.bss.**********=Item ID does not exist!
err.msg.bss.*********=No Approval Role
err.msg.bss.*********=Parent department not found
err.msg.bss.**********=Public cloud renewal starts at least one month
err.msg.bss.*********=Node type error
err.msg.bss.**********=Call to CCP to delete account failed
err.msg.bss.*********=Please contact the Operations Administrator to check if a ModelArts sub-account exists at IAM!
err.msg.bss.*********=You don't have enough permissions! Please contact your enterprise administrator to add functionality and service permissions.
err.msg.bss.*********=The mailbox format does not comply with the rules!
err.msg.bss.**********=The number of expansion calculation nodes cannot be less than 1
err.msg.bss.**********=User does not exist!
err.msg.bss.*********=The elastic file does not exist, please refresh and re
err.msg.bss.**********=The maximum number of customers default value is not legal
err.msg.bss.**********=You can roll back disk data with a snapshot only if the snapshot state is "Available" and the snapshot source disk state is "Available" or "Failed to roll back data"
err.msg.bss.**********=Update contract template parameters!
err.msg.bss.*********=Server exception, please contact administrator
err.msg.bss.*********=Cloud environment authentication failed
err.msg.bss.*********=rpcTracing = = null;
err.msg.bss.*********=request = = null;
err.msg.bss.**********=Test failed!
err.msg.bss.*********=Parameter exception
err.msg.bss.*********=Reseller account id exception
err.msg.bss.*********=SMS Sent Successfully
err.msg.bss.*********=Mount point does not exist
err.msg.bss.*********=This operation is only allowed for work orders in the assigned state
err.msg.bss.**********=Could not find field [
err.msg.bss.********=Failed to remove BMS whitelist
err.msg.bss.********=Cloud account not found
err.msg.bss.*********=Verify that the importing environment's cloud environment is ready to complete
err.msg.bss.**********=Download failed! Please contact the administrator to process
err.msg.bss.*********=Qos Configuration Resolution Exception
err.msg.bss.*********=Not unlocked
err.msg.bss.**********=Postal code cannot be empty
err.msg.bss.**********=The current new environment does not have access to the license, please operate after accessing the license
err.msg.bss.**********=Cycle cannot be empty!
err.msg.bss.**********=ccsp mac verification failed, class is
err.msg.bss.*********=Password contains rule does not conform to rule!
err.msg.bss.**********=The storage allocation rate in this storage type has exceeded the set allocation threshold, and the resource cannot be allocated.
err.msg.bss.**********=The current account has been frozen, and the application for unsubscribe service failed
err.msg.bss.*********=The national secret service cannot be opened repeatedly!
err.msg.bss.**********=File path is blank!
err.msg.bss.*********=Order Details ID = {}, no corresponding EVS
err.msg.bss.**********=Coupon has already been used, please select another coupon.
err.msg.bss.**********=No corresponding billing policy found, bill specification is: {}
err.msg.bss.*********=Service instance no longer exists and cannot be unsubscribed
err.msg.bss.*********=HPC Shared File System Shared Directory Configuration Exception!
err.msg.bss.**********=Cannot duplicate review of invoice
err.msg.bss.**********=Failed to compress local files
err.msg.bss.**********=Login node is empty
err.msg.bss.*********=Affinity/anti-affinity groups that are managing virtual machines cannot be deleted
err.msg.bss.**********=GPU resource group already exists
err.msg.bss.*********=send error
err.msg.bss.*********=Instance Associated Host does not exist
err.msg.bss.2129757398=Time conversion abnormal
err.msg.bss.266746898=System built-in user groups cannot be deleted
err.msg.bss.1928051704=There is an associated service used by the cloud environment and cannot be deleted.
err.msg.bss.**********=Failed to configure HPC pass!
err.msg.bss.*********=The current product configuration does not match the product type, please check and try again!
err.msg.bss.**********=No service expiration parameter configured
err.msg.bss.**********=The zone resource type no longer exists or has been deleted, please refresh and try again!
err.msg.bss.**********=Node deletion, please operate later!
err.msg.bss.*********=You cannot modify the status of an account that is higher than your own account level
err.msg.bss.**********=This operation is not supported in the cloud environment!
err.msg.bss.**********=No current operating entity operation authority!
err.msg.bss.**********=Do not repeat the operation
err.msg.bss.*********=Process instance not found
err.msg.bss.**********=The order data is being downloaded, please inquire about the download task later!
err.msg.bss.**********=] on target [
err.msg.bss.*********=Exclusive capacity interval anomaly
err.msg.bss.**********=ModelArts has ununsubscribed products, please unsubscribe first!
err.msg.bss.********=Retry after s
err.msg.bss.**********=Template name already exists
err.msg.bss.**********=Request parameter cannot be empty!
err.msg.bss.**********=Account already exists! Please check the account number or email for duplication!
err.msg.bss.446265=, Value:
err.msg.bss.**********=Bastion authentication information does not exist, please contact the administrator
err.msg.bss.**********=Resource Pool Type Exception
err.msg.bss.*********=Please select HTTPS protocol certificate!
err.msg.bss.51512=404
err.msg.bss.1134559=error
err.msg.bss.**********=The source disk of the snapshot is mounted. This operation cannot be performed. Uninstall the disk before rolling back the data.
err.msg.bss.**********=View the approval form without permission, try switching the operating entity
err.msg.bss.51511=403
err.msg.bss.**********=The notification policy does not exist
err.msg.bss.**********=The input port parameter cannot be duplicated!
err.msg.bss.51517=409
err.msg.bss.**********=User has been frozen
err.msg.bss.********=You cannot operate accounts of other operating entities!
err.msg.bss.*********=Failed to get current logged in user
err.msg.bss.**********=Name must be between 2-64 characters in length!
err.msg.bss.*********=Does not belong to current approval role, no approval permissions
err.msg.bss.*********=There are no users under this organization.
err.msg.bss.********=The user has owed the fee and cannot submit it. Please remind the user to recharge!
err.msg.bss.**********=Please fill in the correct specification value!
err.msg.bss.**********=Corresponding process application not found
err.msg.bss.**********=Delete not allowed for this work order template in use!
err.msg.bss.*********=Recovery task error.
err.msg.bss.*********=num is an integer up to 100!
err.msg.bss.**********=The completed IP is already occupied, please check
err.msg.bss.**********=MA specification data does not exist
err.msg.bss.**********=Selected Partition [
err.msg.bss.**********=Freeze Upgrade Successful
err.msg.bss.*********=The elastic file does not exist, please refresh and try again
err.msg.bss.**********=Expenditure details are being generated, please check and download tasks later!
err.msg.bss.**********=] Does not exist in the bastion machine, please contact the administrator
err.msg.bss.**********=Public Organization ID Configuration Entry
err.msg.bss.**********=Your password is checked too frequently. Please try again in three minutes.
err.msg.bss.**********=Hard disk name cannot be duplicated
err.msg.bss.473186480=Failed to query identity provider list!
err.msg.bss.**********=Invalid Cloud Environment Type
err.msg.bss.*********=Update failed.
err.msg.bss.**********=Invoicing and invoiced related resources cannot be unsubscribed if they have not expired
err.msg.bss.**********=Data Not Found
err.msg.bss.**********=Only stopped hosts can be converted to templates
err.msg.bss.********='Does not exist
err.msg.bss.**********=This environment is not an RCLink environment
err.msg.bss.********=have been voided
err.msg.bss.*********=There is an unused account balance, please do it before deleting it.
err.msg.bss.**********=status is not exist
err.msg.bss.********=Overreach.
err.msg.bss.********=Organization level out of parameter!
err.msg.bss.**********=Failed to get resource
err.msg.bss.**********=The load balance does not exist
err.msg.bss.*********=There is a child organization under this organization, please delete the child organization first
err.msg.bss.**********=The query failed. Please try again later.
err.msg.bss.**********=Cannot delete the billing resource type associated with a listed product
err.msg.bss.**********=Interface validation failed!
err.msg.bss.*********=Current action user does not exist
err.msg.bss.**********=Did not get the network export information!
err.msg.bss.*********=The current account has been frozen, and the application for service failed
err.msg.bss.**********=Delete not allowed for this job status!
err.msg.bss.**********=No objectKey exists for this obs object:
err.msg.bss.*********=Failed to get orchestration association data
err.msg.bss.*********=Connected successfully!
err.msg.bss.**********=Failed to get user
err.msg.bss.*********=This Elastic IP does not support bandwidth modification
err.msg.bss.**********=Current Action IP does not exist
err.msg.bss.**********=Operation successful!
err.msg.bss.**********=There are created hosts
err.msg.bss.**********=The IP filled in is already occupied. Please confirm that there is any error.
err.msg.bss.**********=Rejected, company does not exist
err.msg.bss.*********=Service configuration is incomplete, please contact your administrator
err.msg.bss.**********=clientId [{}] not present for realm [{}] in keycloak
err.msg.bss.**********=The bucket queried does not exist
err.msg.bss.*********=ccsp verification failed, reason [no field ccsp _ mac], class is
err.msg.bss.1579351490=Unsupported cloud environment
err.msg.bss.2072178987=There are unfinished tasks, HPC service cannot be unsubscribed
err.msg.bss.**********=Create child user
err.msg.bss.**********=The user agreement has been agreed, please do not repeat the operation!
err.msg.bss.*********=Failed to get SFS info:
err.msg.bss.**********=The product template does not exist!
err.msg.bss.*********=The frequency of calls from the same source is too high, please
err.msg.bss.*********=No right to modify
err.msg.bss.**********=Clustering in progress [
err.msg.bss.**********=Cannot perform this operation on the primary account
err.msg.bss.*********=No permission to operate on this resource type
err.msg.bss.********=The load balancer name already exists
err.msg.bss.*********=This function is not permitted, please contact the administrator to upgrade the license and operate!
err.msg.bss.**********=Instance does not exist or has been deleted
err.msg.bss.**********=The balance is insufficient, please use the relevant services after recharging
err.msg.bss.**********=Rejected customers cannot create discounts!
err.msg.bss.*********=File format does not conform, please upload compliant file format
err.msg.bss.**********=After the tenant is enabled, proceed again
err.msg.bss.**********=Added package specification successfully
err.msg.bss.*********=Approval task not found
err.msg.bss.*********=Please remove members of the group first
err.msg.bss.**********=Cloud environment does not exist or has been deleted
err.msg.bss.*********=Delete Exclusive Resource Pool Resource Pool Failed
err.msg.bss.**********=Discounts for the same applicable environment and applicable products exist for the same time frame!
err.msg.bss.*********=This method is invalid!
err.msg.bss.*********=Package package failed to put on shelves
err.msg.bss.*********=System Upgrading
err.msg.bss.*********=The cloud environment has been deleted, please refresh the page and try again.
err.msg.bss.*********=Invalid account
err.msg.bss.*********=Mapping IAM users
err.msg.bss.**********== > Check whether the jar file is present
err.msg.bss.********=LDAP synchronization exception, please try again
err.msg.bss.*********=Node Process Fallback
err.msg.bss.934923=status
err.msg.bss.*********=Not Ready
err.msg.bss.**********=Invalid JDBC URL (should start with jdbc:):
err.msg.bss.*********=Sensitive words in the title or content of the announcement:
err.msg.bss.*********=] Cluster type: [
err.msg.bss.*********=Product not found!
err.msg.bss.**********=The environment is not disconnected
err.msg.bss.**********=Wrong snapshot type, only instance snapshot can be converted to mirror
err.msg.bss.**********=Item ID is empty and cannot be validated
err.msg.bss.**********=Password reset failed
err.msg.bss.**********=Failed to create firewall object, please try again!
err.msg.bss.*********=The parameter time cannot be greater than today
err.msg.bss.**********=The announcement is too big
err.msg.bss.**********=Failed to release shared resource pool node information
err.msg.bss.1272814246=Do not have permission to access the order
err.msg.bss.2032074447=Special characters present in file address
err.msg.bss.377201309=Cannot delete an enabled product template!
err.msg.bss.252251529=RCLinkTokens is not legal.
err.msg.bss.1725748412=Cloud environment deleting
err.msg.bss.1360067202=The user is in the process of enterprise authentication, and unsubscribe products are not supported for the time being
err.msg.bss.2053120504=Downloads are not supported for objects that are not restored or are being restored.
err.msg.bss.887332990=no support type
err.msg.bss.917656426=UserSid cannot be empty
err.msg.bss.**********=Creating Elastic IP does not return data, raw data:
err.msg.bss.**********=Data does not exist!
err.msg.bss.860995564=Configure recipients in batches, up to 10 people each time!
err.msg.bss.**********=Short message channel abnormal, please contact administrator
err.msg.bss.566660286=Invoice information is not set, please fill in and try again
err.msg.bss.629792857=Discount type cannot be changed
err.msg.bss.**********=iam subuser
err.msg.bss.371753704=Please upload the identity provider Metadata file.
err.msg.bss.768443232=Failed to synchronize cloud environment
err.msg.bss.**********=serviceOrderDetails is empty
err.msg.bss.**********=Cannot have duplicate node names in the same process
err.msg.bss.**********=The changed configuration is the same
err.msg.bss.**********=Unauthorized access
err.msg.bss.**********=SMS verification code failed to send
err.msg.bss.567649477=JSON format conversion error.
err.msg.bss.396328749=Effective time cannot be blank when end time exists
err.msg.bss.823320488=The SMS platform is not configured, and dual-factor authentication cannot be turned on
err.msg.bss.454549576=Non-AKSK authentication is not supported at this time
err.msg.bss.957977325=Order Details ID = {}, no corresponding HPC
err.msg.bss.36554443=Unsubscribing
err.msg.bss.**********=Failed to modify invoice setup information
err.msg.bss.**********=Automatic Approval Release Resource failed, order does not exist
err.msg.bss.**********=Operation beyond authority
err.msg.bss.1726601633=The current security group cannot perform this operation. Unbind the virtual network card before deleting it
err.msg.bss.1486919323=Billing cycle data is being downloaded, please check the download task later!
err.msg.bss.2129638482=Current cloud environment does not exist
err.msg.bss.641213096=Phone cannot be empty
err.msg.bss.535820497=Sent successfully.
err.msg.bss.102684976=The name already exists, please modify
err.msg.bss.1712295478=Special character exists for resource ID!
err.msg.bss.1760160432=Instance does not exist or has been deleted
err.msg.bss.24046278=+ name +
err.msg.bss.1781194848=Data state is abnormal, please check the log for details.
err.msg.bss.177495251=Failed to modify package specification
err.msg.bss.1903178708=Tenant data exception
err.msg.bss.*********=Database data handling exception
err.msg.bss.**********=The number of expanded login nodes cannot be less than 1
err.msg.bss.*********=Unsupported conformance
err.msg.bss.**********=getLogWriter
err.msg.bss.*********=Password does not comply with the rule
err.msg.bss.**********=Distributor type users cannot be promoted to System Administrator
err.msg.bss.**********=Unit price can only be a number, please modify the import!
err.msg.bss.**********=You cannot configure user groups for your own account
err.msg.bss.**********=Built-in roles are not removable
err.msg.bss.*********=Names are 4-30 characters long, can contain only lowercase letters, numbers, connectors (-), and must start with a letter, not end with a connector.
err.msg.bss.*********=UserSid is not Empty!
err.msg.bss.**********=clusterId is empty
err.msg.bss.*********=delegate = = null;
err.msg.bss.*********=unsupported method
err.msg.bss.**********=Volume created failure
err.msg.bss.*********=Failed to create user!
err.msg.bss.**********=Huawei Cloud return server created is null
err.msg.bss.**********=Object to delete not found!
err.msg.bss.**********=The service instance has been deleted, please refresh and try again
err.msg.bss.**********=The contract does not exist, please refresh and try again
err.msg.bss.*********=Cloud account not found!
err.msg.bss.*********=Package package removal failed
err.msg.bss.**********=Instances that do not exist
err.msg.bss.*********=ManageOne authentication failed with the error message:
err.msg.bss.*********=You cannot configure permissions for your own account
err.msg.bss.*********=No order found for resource
err.msg.bss.**********=Specifications are priced, do not duplicate creation!
err.msg.bss.*********=Yuan, approval failed.
err.msg.bss.*********=Modified successfully
err.msg.bss.*********=The service requisition is empty and cannot be created.
err.msg.bss.**********=No permission to operate this work order
err.msg.bss.**********=Validation type cannot be empty!
err.msg.bss.*********=Wrong role type.
err.msg.bss.*********=Invalid account
err.msg.bss.********=Specification not found!
err.msg.bss.********=Duplicate database name
err.msg.bss.*********=This network is in use and cannot be deleted
err.msg.bss.*********=This operation is not supported for the time being
err.msg.bss.**********=Remote IP address non-compliant
err.msg.bss.**********=Process name exceeds maximum length limit
err.msg.bss.********=No Logon Node Available
err.msg.bss.**********=Failed to get policy details
err.msg.bss.*********=The announcement type does not exist!
err.msg.bss.**********=The template does not exist!
err.msg.bss.**********=No recharge record can be invoiced
err.msg.bss.3951031=Illegal invoice id parameter
err.msg.bss.*********=The number of nodes cannot exceed 200!
err.msg.bss.*********=Incorrect work order number
err.msg.bss.*********=Start time is greater than 180 days ago!
err.msg.bss.**********=Bill compressed package is too large
err.msg.bss.*********=Creation failed
err.msg.bss.*********=Manipulation failed, removed or does not exist
err.msg.bss.**********=The announcement does not exist
err.msg.bss.**********=For non-custom templates, only one normal state template is allowed for the same action type and resource type.
err.msg.bss.**********=Password can be reset only in shutdown state
err.msg.bss.**********=, already exists
err.msg.bss.**********=MB, please re-upload the file
err.msg.bss.**********=Standard exclusive built-in file system does not support scaling operations
err.msg.bss.**********=Illegal user account id parameter
err.msg.bss.*********=Update Successful
err.msg.bss.*********=Contract does not exist
err.msg.bss.**********=Query user login cannot be empty
err.msg.bss.**********=Eligible organization data not queried
err.msg.bss.*********=Please contact the operation administrator, the product is not on the shelves!
err.msg.bss.*********=Please contact the operations administrator, the product is not authorized!
err.msg.bss.*********=The service order type is not supported, and the supported types include: application, upgrade, downgrade, renewal, unsubscribe
err.msg.bss.**********=Illegal platform discount id parameter
err.msg.bss.*********=Send Successful
err.msg.bss.**********=Authentication failed, please check whether the ID card picture has been uploaded
err.msg.bss.7836561=HPC exclusive resource pool billing error!
err.msg.bss.*********=The corresponding resource was not found, please refresh and try again
err.msg.bss.**********=Validation Failed
err.msg.bss.*********=The currently logged on user does not have permission to assign system administrator rights to this user
iam.core.samenameprovider.exist=Cloud environment: {0} Identity provider already exists
iam.core.metadatafile.pleaseprovide=Please provide the metadata file
iam.core.correct.metadatafile.pleaseprovide=Please provide the correct metadata file or address
generate.client.id.error=Failed to generate client_id
iam.core.provider.cannotmodify=The identity provider is already associated with the cloud environment and cannot modify the metadata file
iam.core.provider.cannotdelete=The identity provider is already associated with the cloud environment and cannot be deleted
iam.core.deletingprovider.serviceexception=Service exception when deleting identity provider
iam.core.role.notexist=The role does not exist or has been deleted
iam.core.policy.notavailable=This strategy has not been activated yet, please stay tuned
iam.core.rolebinding.exist=Role: {0} already has a binding relationship, please reselect the role
iam.core.binding.exist=User group: {0} already has a binding relationship, please select again
iam.core.deliverymethod.chooseright=Please choose the correct strategy distribution method
iam.core.authorizationpolicy.exist=There is a product authorization policy, please unbind it
iam.core.rolepolicy.notexist=The role policy does not exist or has been deleted
iam.core.productbound.anotherrole=This product has already been bound to other roles, please select again
iam.core.permissionpolicy.notexist=The product permission policy does not exist or has been deleted

hcs.auth.valid.failure.msg={0} authentication information Authentication failed, and the error message is as follows:{1}
hcs.auth.valid.failure={0} authentication information failed. Procedure
hcs.auth.valid.error={0} uthentication information is abnormal. Procedure
operate.success.data.sync=Operation successful, data asynchronous processing in progress!
data.syncing=The data is currently being synchronized, please try again later!
err.msg.vdc.query=Failed to obtain VDC information!
err.msg.vdc.group.create=Failed to create a VDC user group!
err.msg.vdc.user.create=Failed to create a VDC user!
err.msg.vdc.group.user.add=Failed to add a user to the user group!
namespace.create.failure=Organization creation failed!
namespace.info.query.failure=Organization information query failed!
info.user.approval.start=approval start
info.user.approval.end=approval end

modelarts.data.download.error=Model Arts service data download exception, please try again later!
modelarts.data.download.wait=Model Arts service data is downloading, please check the download task later!
no.permission.operation=Unauthorized operation!
info.update.failure=Update failure.
file.type.error=This file type upload is not currently supported
file.size.error=The file is too large to upload
file.five.sum.error=Upload up to 5 attachments
file.twenty.sum.error=Upload up to 20 attachments
file.sum.error=File upload limit reached
file.repeat.error=Duplicate file name
error.delete.ing=Deleting\u3002
error.product.deleted=Unsubscribed
error.product.deleting=Unsubscribing

ticket.category.business.contact.not.exist=Business contact does not exist!
ticket.category.frequent.consultation=Work order creation is too frequent. Please try again later!
ticket.category.consultation.details.not.exist=Consultation details do not exist!
ticket.category.duplicate.name=Work order class name :{0} already exists.
ticket.template.duplicate.name=Work order template name :{0} already exists.
ticket.category.name.too.long=The category name cannot exceed 50 characters or the category description cannot exceed 500 characters.
ticket.update.version.updated=It was modified by someone else during the modification. Please resubmit it by brushing the line.
ticket.template.name.too.long=The name of the work order template exceeds 50.
ticket.category.binding.template.not.exist=Workorder type bound workorder template does not exist!
ticket.category.binding.template.disable=Work order type bound work order template is not enabled!
ticket.category.deal.not.exist=Work order type handler does not exist!
ticket.category.binding.deal.disable=Work order type handler is not enabled!
company.auth.error.1=Authentication failed. The request information cannot be empty.
company.auth.error.2=Description Authentication failed. The organization SID information cannot be empty.
company.auth.error.3=Authentication failed. Please complete personal authentication first.
company.auth.error.4=The subuser cannot perform enterprise authentication.
company.auth.error.5=Authentication failed, the company does not exist.
company.auth.error.6=The enterprise has been authenticated and cannot be changed to another authentication.
company.auth.error.7=During enterprise authentication, re-authentication is not allowed.
company.auth.error.8=Authentication failed. Please check whether the picture of business license has been uploaded.
company.auth.error.9=Authentication failed. The identity type cannot be empty.
company.auth.error.10=If authentication fails, the legal representative cannot be empty.
company.auth.error.11=Authentication fails. The name of the authorized person cannot be blank.
company.auth.error.12=Authentication fails. The ID number of the authorized person cannot be empty.
company.auth.error.13=Authentication fails. Please enter the correct ID card information of the authorized person.
company.auth.error.14=Authentication fails. Whether the ID card of the authorized person has been uploaded.
company.auth.error.15=Authentication failed. Please check whether the authorization has been uploaded.
company.auth.error.16=Authentication failed. Please enter the correct corporate ID number.
company.auth.error.17=Authentication failed. Whether the corporate ID card has been uploaded.
company.auth.error.18=Authentication failure, the legal person ID number should be empty.
company.auth.error.19=The authorized person's name shall be 2 to 16 characters in length and shall not contain special symbols.
company.auth.error.20=Authentication failure, ID card underage.
inner.msg.00033=The resource freezing policy is empty!
inner.msg.00034=Failed to set the resource freezing policy!
processing.person.information.error=Processing person information error!
template.information.error=Template information error!
error.msg.00036=Download password failed to obtain, please try again!
child.no.right=Sub-users don't have unlock permissions!
current.user.not.exist=Description Failed to obtain the current login user
process.bind.error=Failed to obtain the service process association data. Procedure
process.audit.success=Review process submitted successfully, No:{0}
resource.acquisition.failure=Resource acquisition failure
wait.process.approval=Wait until the process review is complete before operation, No:{0}
service.has.bind.process=The service has been bound to a process
service.not.support=This business is not in the double audit business
no.current.entity.permission=None The operation rights of the current operating entity
prohibit.repeat.operation=Do not repeat operation
resourc.conversion.failure=Resource conversion failure
error.add.failure=The uncharged product is associated. Please select another one.
resource.type.not.charge=Resource type not priced!
custom.info.abnormal=The current customer information is abnormal!
area.exits.repeat.resource.type=Duplicate resource types exist in the current area
resource.area.mismatching=Failed to add the resource type (region) because the resource type and region did not match
env.cloud.not.exits=Failed to add the resource type (region) because the cloud environment does not exist
select.correct.charge.rule=Please select the correct billing rule!
resource.not.association.charge=This resource type has not been associated with accounting!
user.auth.not.exits=User authentication information not found!
invoicing.amount.than=The invoicing amount is greater than the invoicing amount

error.project.can.not.remove=The current project has unsubscribed resources, and the project cannot be deleted.
organization.error.00001=The account does not exist in the current organization
error.msg.00024=Duplicate user name
error.msg.00025=Unable to get the current user Id
warn.recharge.account=Account data has been updated, please refresh and try again.
need.same.mode=Please select the same billing mode specification.
lost.spec.params=Non-service policy type, missing specification parameters!
decrypt.params.failure=Decryption parameter exception
template.name.repeat=Product template duplication
sfs2.exists.binding.resource=The file system has been bound to a dedicated resource pool and cannot be unsubscribed
contrct.type.error=Contract type error
The.password.does.not.conform.to.the.platform.password.policy=The password does not comply with the platform password policy. Please check and re-enter the password
customer.does.not.exist=The selected customer does not exist
customer.already.exists=The selected customer already exists
product.limit.does.not.exist=The selected product limit does not exist
customer.product.limit.does.not.exist=The selected customer limit does not exist
product.limit.id.cannot.be.empty=The selected product quota id cannot be empty
customer.product.limit.id.cannot.be.empty=The selected customer quota id cannot be empty
whitelist.limit.id.cannot.be.empty=The id of the selected whitelist cannot be empty
the.product.quota.configuration.already.exists=The selected product quota configuration already exists
this.customer.product.limit.configuration.already.exists=The selected customer quota configuration already exists
the.customer.product.limit.has.been.configured.for.all.products=The selected customer product quota has been configured for all products
the.selected.product.does.not.exist=The selected product does not exist
selected.customer.duplicates=The selected customer duplicates
duplicate.selected.id=The selected id is the same
limit.min.amount=Purchase failed, the purchase limit of this product is {0} yuan, please recharge the cash balance
activation.failed.the.customer.has.activated.the.full.product.limit=Enabling failed. The customer has enabled all product quotas
contrct.resource.pool.small.middle=A small - and medium-sized resource pool contains a maximum of 49 computing nodes
contrct.all.number.open.isnull=Add at least one type of node
contrct.agent.number.open.isnull=The number of compute nodes to be expanded cannot be empty
contrct.cli.number.open.isnull=The capacity expansion of login nodes cannot be empty
contrct.vnc.number.open.isnull=The number of VNC nodes to be expanded cannot be empty
contrct.agent.price.isnull=The unit price of a compute node cannot be empty
contrct.cli.price.isnull=The unit price of the login node cannot be empty
contrct.vnc.price.isnull=The unit price of the VNC node cannot be empty
contrct.renew.account.error=The tenant under the renewal contract must be the same as the tenant under the expansion contract
contrct.renew.all.number.open.isnull=Renew at least one type of node
contrct.renew.agent.number.open.isnull=The compute node cannot be renewed
contrct.renew.cli.number.open.isnull=The login node cannot renew the subscription
contrct.renew.vnc.number.open.isnull=The vnc node cannot be renewed
contrct.renew.agent.number.open.gt=The number of compute nodes to be renewed cannot be greater than the number of compute nodes to be expanded
contrct.renew.cli.number.open.gt=The number of nodes that can log in to the renewal contract cannot be greater than the number of nodes that can log in to the expansion contract
contrct.renew.vnc.number.open.gt=The number of vnc nodes to be renewed cannot be equal to the number of vnc nodes to be expanded
error.msg.00032=The request failed. The entered credit limit period cannot be empty.
error.msg.00033=The request failed. The entered credit limit period needs to be longer than the current time.
error.msg.00034=Request failed, please enter the correct credit period.
error_renew_hpc_drp_on_frozen_sfs=Renewing the HPC dedicated resource pool failed because the mounted file storage has been frozen. Procedure
submit_the_request_repeatedly=Request repeat submission
delete.hpc.cli.node.error=At least one login node must be reserved
delete.hpc.compute.and.cli.node.error=At least one login node and one compute node must be reserved
delete.hpc.compute.node.error=At least one compute node must be reserved
tenant.audited.wait=Tenant review underway, please try again later!
error.msg.00030=Unexpired packages cannot be removed
error.sysMJob.fully.qualified.name.exist=The fully qualified name already exists.
error.sysMJob.quartz.job.name.exist=Task identifier already exists.
param.not.valid.error=Parameter verification fails
error.sms.code.length=Only supports 6-digit verification codes.
error.multiple.audits.enable=Double approval process not published or no approver assigned.
org.name.used=The business name has been used
default.job.not.del=The default timer does not support deletion operation!
user.audit.repeat=Do not double-check users!
specifications.do.not.match.prices=spec = [{}], Unmatched price!
account.recharge.success=Account recharge successful!
smscodeinvalid=Invalid SMS verification code
smscodeerror=SMS verification code error
cycle.exists=Same cycle already exists
current.order.not.completed=Invoice failed, current order not completed!
not.subscribe.again=You have already subscribed to this product,please subscribe again after the expiration date!
not.subscribe.myself.shop=Do not subscribe to your own published products!
user.not.hcso.account=User not associated with HCSO account!
error.create.shop=Product creation failed!
error.create.shop.version=Creating a product - Failed to create a product version, please try again!
not.submit.again=The product has already been submitted for approval!
no.task.found=No tasks were found for approval!
# ------------------------> IAM MSG
iam.api.captcha.incorrect = The CAPTCHA is incorrect
iam.api.number.notregistered = This number is not registered, please register first
iam.api.smscode.invalid = SMS verification code is invalid
iam.api.smscode.error = SMS verification code error
iam.api.signature.notconfigured = SMS signature is not configured, please configure SMS signature in notification Settings.
iam.api.exit.failed = Exit failed!
iam.api.defaultusergroup.cannotmodified = The default user group cannot be modified
iam.api.defaultusergroup.cannotdeleted = The default user group cannot be deleted
iam.api.defaultusergroup.cannotauthorized = The default user group cannot be authorized
iam.core.provider.notexist = The identity provider does not exist
iam.core.environmenttype.notsupported = Unsupported cloud types :{0}
iam.core.underlyinggroup.synchronizefailed = Failed to synchronize the underlying user group
iam.core.cloudconfiguration.notenabled = The cloud federation configuration is not enabled and cannot operate on identity providers
iam.core.samenameprovider.exist = Cloud environment :{0} an identity provider with the same name already exists
iam.core.provider.cannotmodify = The identity provider is already associated with the cloud environment and cannot modify the metadata file
iam.core.provider.cannotdelete = The identity provider has been associated with the cloud environment and cannot be deleted
iam.core.rolepolicy.notexist = The role policy does not exist or has been removed
iam.core.provider.notaccess.environment = A cloud environment to which the identity provider does not have access
iam.core.binding.exist = User group :{0} binding already exists, please select again
iam.core.deliverymethod.chooseright = Please choose the right policy delivery method
iam.core.initialized.rolepolicies.cannotdeleted = Initialized role policies cannot be deleted
iam.core.authorizationpolicy.exist = Product authorization policy exists, please unbind
iam.core.permissionpolicy.notexist = The product permission policy does not exist or has been removed
iam.core.producttype.notexist = The product type does not exist or has been removed
iam.core.productbound.anotherrole = This product has been bound to another role, please select again
iam.core.metadatafile.formatincorrect = The identity provider metadata file is not in the correct format, please confirm
iam.core.protocoltype.notsupported = Unsupported protocol types :{0}
iam.core.deletingprovider.serviceexception = Service exception when deleting identity provider
iam.core.role.notexist = The role does not exist or has been deleted
iam.core.rolebinding.exist = Role :{0} binding already exists, please select the role again
iam.core.policy.notavailable = This policy is not available yet, so stay tuned
iam.core.correctaddress.pleaseprovide = Please provide the correct file address
iam.core.metadatafile.pleaseprovide = Please provide the metadata file
iam.core.correct.metadatafile.pleaseprovide = Please provide the correct metadata file or address
iam.core.account.exist = Account already exists! Please check whether the account is duplicate!
iam.core.user.createfailed = Failed to create user! : kId:{0}
iam.core.configurationinformation.error = Configuration information is wrong, can not get the user!
iam.core.organization.cannotcreate.subordinate = The {0} organization is a federated organization; subordinate organizations cannot be created
iam.core.cmdborganization.addfailed = CMDB organization add failed :{0}
iam.core.dcimorganization.addfailed = DCIM organization add failure :{0}
iam.core.organizationproject.cannotmodify = A project {0} under a federated organization cannot modify a parent organization
iam.core.nonfederated.organization.cannotmodified = An item {0} under a non-federated organization cannot be modified under a federated organization
iam.core.federationtype.cannotmodified = The federation type of an organization cannot be modified
iam.core.groupname.duplicate = Duplicate group names
iam.core.usergroup.synchronizeexception = Synchronize the underlying user group exception
iam.core.usergroup.modifyexception = Synchronously modifying user group exceptions
iam.core.usergroup.deletefailed = Failed to synchronously delete the cloud environment user group
iam.core.usergroup.authorizationexception = Synchronization underlying cloud environment user group authorization exception
iam.core.usergroup.bindfailed = Failure of user group synchronization to bind cloud environment user
iam.core.usergroup.notexist = The user group does not exist or has been deleted
iam.core.usergroup.createfailed = Group creation failed
iam.core.custompermission.createfailed = Failed to create custom permissions
iam.core.organization.notexist = The selected organization does not exist or has been deleted
iam.core.user.notexist = The user does not exist or has been deleted
iam.core.cmdb.pushfailed = Push CMDB failed: {0}
iam.core.newuser.pushdcim.failed = New user push DCIM failed :{0}
iam.core.pushdcim.failed = Failure to push DCIM :{0}
iam.core.account.notpermission = This account does not have permission to use
iam.core.userwithoutrole.cannotlogin = The user cannot log in without a given role
iam.api.synchronizations.failed = Synchronization failure: [{0}]
iam.api.synchronization.success = Synchronization is successful
iam.api.synchronization.failed = Synchronization failure
iam.api.change.success = Change successful
iam.api.add.success = Added successfully
iam.api.delivery.success = Successful delivery
iam.api.twofactor.information.notconfigured = Two-factor authentication information is not configured
iam.api.enable.success = Enabled successfully
iam.api.disable.success = Disable successfully
iam.api.provider.exist = The identity provider already exists
iam.api.identityprovider.addfailed = Failed to add identity provider, please confirm the authentication service address
iam.api.identityprovider.changefailed = Failed to change identity provider
iam.api.identityprovider.deletefailed = Failed to delete identity provider
iam.api.role.bingsuccess = Role binding is successful
iam.api.user.bindsuccess = User binding was successful
iam.api.user.untiesuccess = The user untied successfully
iam.api.role.untiesuccess = The role is untied successfully
iam.api.user.authorizesuccess = User authorized successfully
iam.core.account.underreview = This account is under review
iam.core.account.disabled = The account has been disabled
iam.core.account.locked = The account has been locked, please contact the administrator to reactivate the account
iam.core.account.expired = Account has expired
iam.core.username.password.wrong = Wrong username or password
iam.core.account.temporarilylocked = Your account has been temporarily locked, please try again later
iam.core.account.loginnumber.exceedlimit = The current account logon number [{0}] exceeds the limit, continue to login will force other logon users to log off!
iam.core.account.invalid = The account is no longer valid
iam.core.error.description=Invalid description
iam.core.controller.start.sync = start sync.
error.process.id.not.null=Process ID cannot be empty
err.msg.bss.*********=The process of product [{0}] does not exist!
error.hcso.user.not.exit=\u672A\u67E5\u5230\u5F53\u524D\u79DF\u6237\u5173\u8054HCSO\u4FE1\u606F

current.operation.not.supported=Resource {0}, the current operation cannot be performed
current.contain.operation.not.supported=Description The current operation cannot be performed temporarily because {0} resource {1} exists
user.does.not.exist=User Does Not Exist!
secret.key.does.not.exist=Key Does Not Exist!
key.download.failed=Key Download Failed!
cannot.download.repeatedly=Cannot Download Repeatedly!
too.many.key.creations=Too Many Key Creations!
error.cce.not.exit=CCE cluster does not exist
error.cce.flavor.not.exit=Cluster specification [{0}] does not exist
error.cce.version.not.exit=Cluster version [{0}] does not exist

not.support.capacity.reduction=The current resource does not support capacity reduction

user.authentication.failure=User authentication failure
delete.hpc.online1=Description Failed to delete the current resource pool type
delete.hpc.online2=Description Failed to delete the resource pool because the resource pool is in use
delete.hpc.online3=The current cluster ID already exists
hpc.offline.apply.waiting=HPC-OFFLINE is being created; please do not perform this operation!
hpc.offline.unsubscribe.waiting=HPC-OFFLINE is being unsubscribed; please do not perform this operation!
error.dme-osp.deleted=Storage is deleted!
error.dme-osp.deleting=Storage is deleting!
error.dme-osp.create=Storage create failed!
error.dme-osp.delete=Storage delete failed!
error.hpc.offline.apply.file.system.uuid=file system uuid cannot be empty;
info.dme-osp.quota.exist=Please apply for a data store size greater than or equal to {0}GB!
offline.hpc.succeed.one=CLI connection successful

offline.hpc.error.one=The CLI connection failed. Please check the connection information
offline.hpc.error.two=Danuo Partal Connection failed, please check the connection information
offline.hpc.succeed.two=Danuo Partal Connection successful
offline.hpc.error.three=The database connection failed. Please check the connection information
offline.hpc.succeed.three=The database connection successful

error.no.user.usergroup=no user in usergoup!
error.update.menu.default.group=default group cannot be modified!
