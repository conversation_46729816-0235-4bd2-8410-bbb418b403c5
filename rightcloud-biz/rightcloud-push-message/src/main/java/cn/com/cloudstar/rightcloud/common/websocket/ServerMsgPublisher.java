/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.common.websocket;

import com.alibaba.fastjson.JSON;

import org.springframework.messaging.simp.SimpMessagingTemplate;

import java.util.Objects;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.RandomUtil;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.common.redis.JedisUtil;
import cn.com.cloudstar.rightcloud.common.util.SpringContextHolder;
import cn.com.cloudstar.rightcloud.common.websocket.support.ServerMsgType;

/**
 * The type ServerMsgPublisher.
 *
 * Created on 2018/12/11
 *
 * <AUTHOR>
 */
@Slf4j
public class ServerMsgPublisher {

    public static final String WEBSOCKET_CHANNEL = "push-message-channel";
    private static final String BIZ_BROKER = "/topic/";
    private static final String USER_BROKER = "/msg";

    private static final Snowflake SNOW_FLAKE;

    private static volatile SimpMessagingTemplate MESSAGING_TEMPLATE;

    static {
        log.info("Init ServerMsgPublisher");

        SNOW_FLAKE = new Snowflake(RandomUtil.randomInt(1, 30), RandomUtil.randomInt(1, 30), false);
    }

    public static void sendMsgToUser(String userSid, ServerMsg serverMsg) {
        if (Objects.isNull(userSid)) {
            log.warn("UserID is null. Message is {}", serverMsg.toString());
            return;
        }
        sendMsg("/user/" + userSid + USER_BROKER, serverMsg);
    }

    public static void sendMsgToResourceType(String resType, String refId) {
        if (Objects.isNull(resType)) {
            log.warn("ResType is null. Message is {}", refId);
            return;
        }
        sendMsg(BIZ_BROKER + resType, refId);
    }

    public static void sendMsgToDetail(ServerMsgType resType, String refId, Object message) {
        if (Objects.isNull(resType)) {
            log.warn("ServerMsgType is null. Message is {}", JSON.toJSONString(message));
            return;
        }
        String destination = BIZ_BROKER + resType.getTypeFamily() + "/" + refId;
        sendMsg(destination, message);
    }

    public static void sendMsg(String destination, Object serverMsg) {
        sendBroadcast(destination, serverMsg);
    }

    private static void sendBroadcast(String destination, Object serverMsg) {
        ConsumerMsg consumerMsg = ConsumerMsg.builder()
                                             .destination(destination)
                                             .serverMsg(serverMsg)
                                             .msgId(SNOW_FLAKE.nextId())
                                             .build();
        JedisUtil.instance().publish(WEBSOCKET_CHANNEL, JSON.toJSONString(consumerMsg));
    }
}
