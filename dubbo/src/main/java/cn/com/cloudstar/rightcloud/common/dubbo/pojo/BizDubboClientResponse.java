//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package cn.com.cloudstar.rightcloud.common.dubbo.pojo;

import brave.dubbo.DubboResponse;
import brave.internal.Nullable;
import brave.rpc.RpcClientResponse;
import org.apache.dubbo.rpc.Result;

public final class BizDubboClientResponse extends RpcClientResponse implements DubboResponse {
    final BizDubboClientRequest request;
    @Nullable
    final Result result;
    @Nullable
    final Throwable error;

    public BizDubboClientResponse(BizDubboClientRequest request, @Nullable Result result, @Nullable Throwable error) {
        if (request == null) {
            throw new NullPointerException("request == null");
        } else {
            this.request = request;
            this.result = result;
            this.error = error;
        }
    }

    public Result result() {
        return this.result;
    }

    public Result unwrap() {
        return this.result;
    }

    public BizDubboClientRequest request() {
        return this.request;
    }

    public Throwable error() {
        return this.error;
    }

    public String errorCode() {
        return DubboParser.errorCode(this.error);
    }
}
