/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.remote.api.monitor.service;

import java.util.List;

import cn.com.cloudstar.rightcloud.module.support.access.pojo.Criteria;
import cn.com.cloudstar.rightcloud.remote.api.pojo.monitor.AlarmLevel;
import cn.com.cloudstar.rightcloud.remote.api.pojo.monitor.params.AlarmLevelParams;

/**
 * The type IntelliJ IDEA.
 * <p>
 *
 * <AUTHOR>
 * @date 2020/11/25
 */
public interface AlarmLevelRemoteService {

    List<AlarmLevel> selectByParams(AlarmLevelParams params);

    AlarmLevel selectByPrimaryKey(Long id);
}
