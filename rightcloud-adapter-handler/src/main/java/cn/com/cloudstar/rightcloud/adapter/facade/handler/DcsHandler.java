/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.adapter.facade.handler;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.com.cloudstar.rightcloud.adapter.pojo.dcs.DcsAction;
import cn.com.cloudstar.rightcloud.adapter.pojo.dcs.DcsConfigUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.dcs.DcsCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.dcs.DcsDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.dcs.DcsExtend;
import cn.com.cloudstar.rightcloud.adapter.pojo.dcs.DcsResetPassword;
import cn.com.cloudstar.rightcloud.adapter.pojo.dcs.DcsUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.dcs.result.DcsActionResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.dcs.result.DcsConfigUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.dcs.result.DcsCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.dcs.result.DcsDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.dcs.result.DcsExtendResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.dcs.result.DcsResetPasswordResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.dcs.result.DcsUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.price.HuaweiPriceCalculate;
import cn.com.cloudstar.rightcloud.adapter.pojo.price.result.PriceCalculateResult;
import cn.com.cloudstar.rightcloud.driver.core.ActionServiceFactory;
import cn.com.cloudstar.rightcloud.driver.core.exception.AdapterUnavailableException;
import cn.com.cloudstar.rightcloud.driver.core.exception.CommonAdapterException;

@Service
public class DcsHandler {

    @Autowired
    private ActionServiceFactory actionServiceFactory;

    /**
     * 创建高速缓存实例
     **/
    public DcsCreateResult createDcs(DcsCreate create) throws CommonAdapterException, AdapterUnavailableException {
        return (DcsCreateResult) actionServiceFactory.getActionService(create).invoke(create);
    }

    /**
     * 更新高速缓存实例
     **/
    public DcsUpdateResult updateDcs(DcsUpdate update) throws CommonAdapterException, AdapterUnavailableException {
        return (DcsUpdateResult) actionServiceFactory.getActionService(update).invoke(update);
    }

    /**
     * 删除高速实例
     **/
    public DcsDeleteResult deleteDcs(DcsDelete delete) throws CommonAdapterException, AdapterUnavailableException {
        return (DcsDeleteResult) actionServiceFactory.getActionService(delete).invoke(delete);
    }

    /**
     * 扩容高速缓存
     **/
    public DcsExtendResult extendDcs(DcsExtend extend) throws CommonAdapterException, AdapterUnavailableException {
        return (DcsExtendResult) actionServiceFactory.getActionService(extend).invoke(extend);
    }

    /**
     * 操作高速缓存
     **/
    public DcsActionResult actionDcs(DcsAction action) throws CommonAdapterException, AdapterUnavailableException {
        return (DcsActionResult) actionServiceFactory.getActionService(action).invoke(action);
    }

    /**
     * 重置密码
     **/
    public DcsResetPasswordResult resetDcsPassword(DcsResetPassword opt)
            throws CommonAdapterException, AdapterUnavailableException {
        return (DcsResetPasswordResult) actionServiceFactory.getActionService(opt).invoke(opt);
    }

    /**
     * 修改配置参数
     **/
    public DcsConfigUpdateResult updateDcsConfig(DcsConfigUpdate opt)
            throws CommonAdapterException, AdapterUnavailableException {
        return (DcsConfigUpdateResult) actionServiceFactory.getActionService(opt).invoke(opt);
    }

    /**
     * 获取价格
     **/
    public PriceCalculateResult getPrice(HuaweiPriceCalculate opt)
            throws CommonAdapterException, AdapterUnavailableException {
        return (PriceCalculateResult) actionServiceFactory.getActionService(opt).invoke(opt);
    }
}
