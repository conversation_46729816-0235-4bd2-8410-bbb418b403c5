package cn.com.cloudstar.rightcloud.adapter.facade.handler;

import cn.com.cloudstar.rightcloud.adapter.pojo.requesttime.HCSOTimeGet;
import cn.com.cloudstar.rightcloud.adapter.pojo.requesttime.result.HCSOTimeGetResult;
import cn.com.cloudstar.rightcloud.driver.core.ActionServiceFactory;
import cn.com.cloudstar.rightcloud.driver.core.exception.AdapterUnavailableException;
import cn.com.cloudstar.rightcloud.driver.core.exception.CommonAdapterException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * DESC: 获取华为侧系统时间搓
 *
 * <AUTHOR>
 * @date 2022-11-08 10:38
 */
@Service
@Slf4j
public class HCSOTimeHandler {

    @Autowired
    private ActionServiceFactory actionServiceFactory;

    public HCSOTimeGetResult getHCSOTime(HCSOTimeGet hcsoTimeGet) throws CommonAdapterException, AdapterUnavailableException {
        return (HCSOTimeGetResult) actionServiceFactory.getActionService(hcsoTimeGet).invoke(hcsoTimeGet);
    }
}
