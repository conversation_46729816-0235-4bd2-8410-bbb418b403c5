/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.adapter.facade.handler;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.com.cloudstar.rightcloud.adapter.pojo.cloudos.ScanCloudOsBareMetalNode;
import cn.com.cloudstar.rightcloud.adapter.pojo.cloudos.ScanCloudOsBareMetalZone;
import cn.com.cloudstar.rightcloud.adapter.pojo.cloudos.ScanCloudOsBaremetalByEnv;
import cn.com.cloudstar.rightcloud.adapter.pojo.cloudos.ScanCloudOsOwnerByEnv;
import cn.com.cloudstar.rightcloud.adapter.pojo.cloudos.ScanCloudOsZoneNetwork;
import cn.com.cloudstar.rightcloud.adapter.pojo.cloudos.ScanFlowFeatureGroup;
import cn.com.cloudstar.rightcloud.adapter.pojo.cloudos.result.BaremetalNodeResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.cloudos.result.BaremetalZoneResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.cloudos.result.CloudOsBaremetalResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.cloudos.result.CloudOsOwnerResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.cloudos.result.FlowFeatureGroupScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.cloudos.result.ZoneNetworkResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.maas.MachineExtendInfo;
import cn.com.cloudstar.rightcloud.adapter.pojo.maas.result.MachineExtendInfoResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.FloatingIpLineQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.FloatingIpLineQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.AliWholeZoneScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.AllInOneScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.BucketObjectScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.BucketPolicyScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.BucketScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ClusterScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.DBInstanceAccountScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.DBInstanceIpArrayScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.DBInstanceScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.DcsAvailableZoneScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.DcsConfigScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.DcsProductScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.DcsScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.GpuDeviceScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.GpuGroupScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.HostRelateInstScanByEnv;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.HostScanByEnv;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.IamUserScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.IamUserScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.KafkaTopicScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.LbListenerScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.LbPoolsScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.LoadBalanceScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.MqInstanceScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.NetworkExportScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.NetworkScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.PhycialMappingScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.PortScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ProjectScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.RegionScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ResourceGroupScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ResourceScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.RouterInterfaceScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.RouterRouteScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.RouterScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ScFirewallRuleScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ScFirewallScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ScFirewallStrategyScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ScanFirewallObjectGroups;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ScanFloatingIpsByEnv;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ScanInstanceGaapCostByEnv;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ScanShareGroupByEnv;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ScanShareGroupRuleByEnv;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ScanShareTypeByEnv;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ScanSharesByEnv;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ScanSharesRightsGroupByEnv;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ScanSharesZonesByEnv;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ScanTagsByEnv;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ScanVdBackupsByEnv;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ScanVdsByEnv;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ScanVmTypesByEnv;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ScanVmsByEnv;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.SecurityGroupScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ServerGroupScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ServiceChainScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.SnapshotScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.SshKeyScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.StorageScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.StorageTypeScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.SubnetScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.TemplateScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.UserBusinessBehaviorScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.VPCFirewallScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.VmScanAlone;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.VpcPeeringScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ZoneScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.AliWholeZoneScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.AllInOneScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.BucketObjectScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.BucketPolicyScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.BucketScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ClusterScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.DBInstanceAccountScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.DBInstanceIpArrayScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.DBInstanceScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.DcsAvailableZoneScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.DcsConfigScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.DcsProductScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.DcsScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.DiskScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.GpuDeviceScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.GpuGroupScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.HostRelateInstScanByEnvResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.HostScanByEnvResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.KafkaTopicScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.LbListenerScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.LbPoolsScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.LoadBalanceScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.MqInstanceScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.NetworkExportScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.NetworkScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.PhycialMappingScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.PortScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ProjectScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.RegionScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ResourceGroupScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ResourceScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.RouterInterfaceScanByEnvResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.RouterRouteScanByEnvResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.RouterScanByEnvResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ScFirewallRuleScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ScFirewallScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ScFirewallStrategyScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ScanFirewallObjectGroupsResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ScanFloatingIpByEnvResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ScanInstanceGaapCostResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ScanShareGroupByEnvResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ScanShareGroupRuleByEnvResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ScanShareTypeByEnvResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ScanSharesByEnvResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ScanSharesRightsGroupByEnvResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ScanSharesZonesByEnvResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ScanTagsByEnvResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ScanVmTypesByEnvResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ScanVmsByEnvResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.SecurityGroupScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ServerGroupScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ServiceChainScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.SnapshotScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.SshKeyScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.StorageScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.StorageTypeScanByEnvResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.TemplateScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.UserBusinessBehaviorScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.VPCFirewallScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.VmScanAloneResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.VpcPeeringScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ZoneScanResult;
import cn.com.cloudstar.rightcloud.driver.core.ActionServiceFactory;
import cn.com.cloudstar.rightcloud.driver.core.exception.AdapterUnavailableException;
import cn.com.cloudstar.rightcloud.driver.core.exception.CommonAdapterException;

/**
 * The type Scan handler.
 */
@Service
public class ScanHandler {

    @Autowired
    private ActionServiceFactory actionServiceFactory;

    /**
     * Scan all in one all in one scan result.
     *
     * @param allInOneScan the all in one scan
     * @return the all in one scan result
     * @throws CommonAdapterException      the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public AllInOneScanResult scanAllInOne(AllInOneScan allInOneScan)
            throws CommonAdapterException, AdapterUnavailableException {

        return (AllInOneScanResult) actionServiceFactory.getActionService(allInOneScan).invoke(allInOneScan);
    }

    /**
     * Scan data store storage scan result.
     *
     * @param storageScan the storage scan
     * @return the storage scan result
     * @throws CommonAdapterException      the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public StorageScanResult scanDataStore(StorageScan storageScan)
            throws CommonAdapterException, AdapterUnavailableException {

        return (StorageScanResult) actionServiceFactory.getActionService(storageScan).invoke(storageScan);
    }

    /**
     * Scan cluster cluster scan result.
     *
     * @param clusterScan the cluster scan
     * @return the cluster scan result
     * @throws CommonAdapterException      the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public ClusterScanResult scanCluster(ClusterScan clusterScan)
            throws CommonAdapterException, AdapterUnavailableException {

        return (ClusterScanResult) actionServiceFactory.getActionService(clusterScan).invoke(clusterScan);
    }

    /**
     * Scan network network scan result.
     *
     * @param networkScan the network scan
     * @return the network scan result
     * @throws CommonAdapterException      the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public NetworkScanResult scanNetwork(NetworkScan networkScan)
            throws CommonAdapterException, AdapterUnavailableException {

        return (NetworkScanResult) actionServiceFactory.getActionService(networkScan).invoke(networkScan);
    }

    /**
     * Scan template template scan result.
     *
     * @param templateScan the template scan
     * @return the template scan result
     * @throws CommonAdapterException      the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public TemplateScanResult scanTemplate(TemplateScan templateScan)
            throws CommonAdapterException, AdapterUnavailableException {

        return (TemplateScanResult) actionServiceFactory.getActionService(templateScan).invoke(templateScan);
    }


    public PhycialMappingScanResult scanPhycialMapping(PhycialMappingScan phycialMappingScan)
            throws CommonAdapterException, AdapterUnavailableException {

        return (PhycialMappingScanResult) actionServiceFactory.getActionService(phycialMappingScan).invoke(phycialMappingScan);
    }

    /**
     * Scan hosts by env host scan by env result.
     *
     * @param hostScanByEnv the host scan by env
     * @return the host scan by env result
     * @throws CommonAdapterException      the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public HostScanByEnvResult scanHostsByEnv(HostScanByEnv hostScanByEnv)
            throws CommonAdapterException, AdapterUnavailableException {
        return (HostScanByEnvResult) actionServiceFactory.getActionService(hostScanByEnv).invoke(hostScanByEnv);
    }

    /**
     * Scan zone zone scan result.
     *
     * @param zoneScan the zone scan
     * @return the zone scan result
     * @throws CommonAdapterException      the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public ZoneScanResult scanZone(ZoneScan zoneScan) throws CommonAdapterException, AdapterUnavailableException {
        return (ZoneScanResult) actionServiceFactory.getActionService(zoneScan).invoke(zoneScan);
    }

    public VpcPeeringScanResult scanVpcPeering(VpcPeeringScan vpcPeeringScan) throws CommonAdapterException, AdapterUnavailableException {
        return (VpcPeeringScanResult) actionServiceFactory.getActionService(vpcPeeringScan).invoke(vpcPeeringScan);
    }

    /**
     * Scan vms by env scan vms by env result.
     *
     * @param scanVmsByEnv the scan vms by env
     * @return the scan vms by env result
     * @throws CommonAdapterException      the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public ScanVmsByEnvResult scanVmsByEnv(ScanVmsByEnv scanVmsByEnv)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ScanVmsByEnvResult) actionServiceFactory.getActionService(scanVmsByEnv).invoke(scanVmsByEnv);
    }

    /**
     * Scan scanTagsByEnv by env scan scanTagsByEnv by env result.
     *
     * @param scanTagsByEnv the scan vms by env
     * @return the scan vms by env result
     * @throws CommonAdapterException      the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public ScanTagsByEnvResult scanTagsByEnv(ScanTagsByEnv scanTagsByEnv)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ScanTagsByEnvResult) actionServiceFactory.getActionService(scanTagsByEnv).invoke(scanTagsByEnv);
    }

    /**
     * Scan instanceGaapCost by env scan instanceGaapCost by env result.
     *
     * @param scanSharesByEnv the scan vms by env
     * @return the scan vms by env result
     * @throws CommonAdapterException      the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */

    public ScanSharesByEnvResult scanSharesByEnv(ScanSharesByEnv scanSharesByEnv)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ScanSharesByEnvResult) actionServiceFactory.getActionService(scanSharesByEnv).invoke(scanSharesByEnv);
    }

    /**
     * Scan share group by env result.
     *
     * @param scanShareGroupByEnv the scan share group by env
     * @return the scan share group by env result
     * @throws CommonAdapterException      the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */

    public ScanShareGroupByEnvResult scanShareGroupByEnv(ScanShareGroupByEnv scanShareGroupByEnv)
        throws CommonAdapterException, AdapterUnavailableException {
        return (ScanShareGroupByEnvResult) actionServiceFactory
            .getActionService(scanShareGroupByEnv).invoke(scanShareGroupByEnv);
    }

    /**
     * Scan share group rule by env result.
     *
     * @param scanShareGroupRuleByEnv the scan share group rule by env
     * @return the scan share group rule by env result
     * @throws CommonAdapterException      the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */

    public ScanShareGroupRuleByEnvResult scanShareGroupRuleByEnv(
        ScanShareGroupRuleByEnv scanShareGroupRuleByEnv)
        throws CommonAdapterException, AdapterUnavailableException {
        return (ScanShareGroupRuleByEnvResult) actionServiceFactory
            .getActionService(scanShareGroupRuleByEnv).invoke(scanShareGroupRuleByEnv);
    }

    /**
     * 扫描文件存储-权限组
     * @param rightsGroupByEnv
     * @return
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public ScanSharesRightsGroupByEnvResult scanSharesRightsGroupByEnv(ScanSharesRightsGroupByEnv rightsGroupByEnv)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ScanSharesRightsGroupByEnvResult) actionServiceFactory.getActionService(rightsGroupByEnv).invoke(rightsGroupByEnv);
    }

    /**
     * 文件存储-可用区
     * @param zonesByEnv
     * @return
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public ScanSharesZonesByEnvResult scanSharesZonesByEnv(ScanSharesZonesByEnv zonesByEnv)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ScanSharesZonesByEnvResult) actionServiceFactory.getActionService(zonesByEnv).invoke(zonesByEnv);
    }

    /**
     * Scan instanceGaapCost by env scan instanceGaapCost by env result.
     *
     * @param scanInstanceGaapCostByEnv the scan vms by env
     * @return the scan vms by env result
     * @throws CommonAdapterException      the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public ScanInstanceGaapCostResult scanInstanceGaapCostByEnv(ScanInstanceGaapCostByEnv scanInstanceGaapCostByEnv)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ScanInstanceGaapCostResult) actionServiceFactory.getActionService(scanInstanceGaapCostByEnv).invoke(scanInstanceGaapCostByEnv);
    }

    /**
     * Scan vm types by env scan vm types by env result.
     *
     * @param scanVmTypesByEnv the scan vm types by env
     * @return the scan vm types by env result
     * @throws CommonAdapterException      the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public ScanVmTypesByEnvResult scanVmTypesByEnv(ScanVmTypesByEnv scanVmTypesByEnv)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ScanVmTypesByEnvResult) actionServiceFactory.getActionService(scanVmTypesByEnv)
                                                            .invoke(scanVmTypesByEnv);
    }

    /**
     * Scan share types by env scan vm types by env result.
     *
     * @param scanShareTypeByEnv the scan share types by env
     *
     * @return the scan vm types by env result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public ScanShareTypeByEnvResult scanShareTypesByEnv(ScanShareTypeByEnv scanShareTypeByEnv)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ScanShareTypeByEnvResult) actionServiceFactory.getActionService(scanShareTypeByEnv)
                                                              .invoke(scanShareTypeByEnv);
    }

    /**
     * Scan floating ips by env scan floating ip by env result.
     *
     * @param scanFloatingIpsByEnv the scan floating ips by env
     *
     * @return the scan floating ip by env result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public ScanFloatingIpByEnvResult scanFloatingIpsByEnv(ScanFloatingIpsByEnv scanFloatingIpsByEnv)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ScanFloatingIpByEnvResult) actionServiceFactory.getActionService(scanFloatingIpsByEnv)
                .invoke(scanFloatingIpsByEnv);
    }

    /**
     * Scan db instance by env db instance scan result.
     *
     * @param dbInstanceScan the db instance scan
     * @return the db instance scan result
     * @throws CommonAdapterException      the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public DBInstanceScanResult scanDBInstanceByEnv(DBInstanceScan dbInstanceScan)
            throws CommonAdapterException, AdapterUnavailableException {
        return (DBInstanceScanResult) actionServiceFactory.getActionService(dbInstanceScan).invoke(dbInstanceScan);
    }

    /**
     * Scan db instance ip array by env db instance ip array scan result.
     *
     * @param dbInstanceIpArrayScan the db instance ip array scan
     * @return the db instance ip array scan result
     * @throws CommonAdapterException      the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public DBInstanceIpArrayScanResult scanDBInstanceIpArrayByEnv(DBInstanceIpArrayScan dbInstanceIpArrayScan)
            throws CommonAdapterException, AdapterUnavailableException {
        return (DBInstanceIpArrayScanResult) actionServiceFactory.getActionService(dbInstanceIpArrayScan)
                .invoke(dbInstanceIpArrayScan);
    }

    /**
     * Scan db instance account by env db instance account scan result.
     *
     * @param dbInstanceAccountScan the db instance account scan
     * @return the db instance account scan result
     * @throws CommonAdapterException      the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public DBInstanceAccountScanResult scanDBInstanceAccountByEnv(DBInstanceAccountScan dbInstanceAccountScan)
            throws CommonAdapterException, AdapterUnavailableException {
        return (DBInstanceAccountScanResult) actionServiceFactory.getActionService(dbInstanceAccountScan)
                .invoke(dbInstanceAccountScan);
    }

    /**
     * Scan vds by env disk scan result.
     *
     * @param scanVdsByEnv the scan vds by env
     * @return the disk scan result
     * @throws CommonAdapterException      the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public DiskScanResult scanVdsByEnv(ScanVdsByEnv scanVdsByEnv)
            throws CommonAdapterException, AdapterUnavailableException {
        return (DiskScanResult) actionServiceFactory.getActionService(scanVdsByEnv).invoke(scanVdsByEnv);
    }

    /**
     * 磁盘备份
     * @param scanVdBackupsByEnv
     * @return
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public DiskScanResult scanVdBackupsByEnv(ScanVdBackupsByEnv scanVdBackupsByEnv)
            throws CommonAdapterException, AdapterUnavailableException {
        return (DiskScanResult) actionServiceFactory.getActionService(scanVdBackupsByEnv).invoke(scanVdBackupsByEnv);
    }

    /**
     * Scan load balance by env load balance scan result.
     *
     * @param loadBalanceScan the load balance scan
     * @return the load balance scan result
     * @throws CommonAdapterException      the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public LoadBalanceScanResult scanLoadBalanceByEnv(LoadBalanceScan loadBalanceScan)
            throws CommonAdapterException, AdapterUnavailableException {
        return (LoadBalanceScanResult) actionServiceFactory.getActionService(loadBalanceScan).invoke(loadBalanceScan);
    }

    public LbListenerScanResult scanLbListenerByEnv(LbListenerScan lbListenerScan)
            throws CommonAdapterException, AdapterUnavailableException {
        return (LbListenerScanResult) actionServiceFactory.getActionService(lbListenerScan).invoke(lbListenerScan);
    }

    public LbPoolsScanResult scanLbPoolsByEnv(LbPoolsScan lbPoolsScan)
            throws CommonAdapterException, AdapterUnavailableException {
        return (LbPoolsScanResult) actionServiceFactory.getActionService(lbPoolsScan).invoke(lbPoolsScan);
    }

    /**
     * Scan subnet by env network scan result.
     *
     * @param subnetScan the subnet scan
     * @return the network scan result
     * @throws CommonAdapterException      the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public NetworkScanResult scanSubnetByEnv(SubnetScan subnetScan)
            throws CommonAdapterException, AdapterUnavailableException {
        return (NetworkScanResult) actionServiceFactory.getActionService(subnetScan).invoke(subnetScan);
    }

    /**
     * Scan router by env router result.
     *
     * @param routerScan the router scan
     * @return the router result
     * @throws CommonAdapterException      the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public RouterScanByEnvResult scanRouterByEnv(RouterScan routerScan)
            throws CommonAdapterException, AdapterUnavailableException {
        return (RouterScanByEnvResult) actionServiceFactory.getActionService(routerScan).invoke(routerScan);
    }

    /**
     * Scan router interface by env router result.
     *
     * @param routerInterfaceScan the router interface scan
     * @return the router result
     * @throws CommonAdapterException      the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public RouterInterfaceScanByEnvResult scanRouterInterfaceByEnv(RouterInterfaceScan routerInterfaceScan)
            throws CommonAdapterException, AdapterUnavailableException {
        return (RouterInterfaceScanByEnvResult) actionServiceFactory.getActionService(routerInterfaceScan).invoke(routerInterfaceScan);
    }

    /**
     * Scan router route by env router result.
     *
     * @param routerRouteScan the router route scan
     * @return the router result
     * @throws CommonAdapterException      the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public RouterRouteScanByEnvResult scanRouterRouteByEnv(RouterRouteScan routerRouteScan)
            throws CommonAdapterException, AdapterUnavailableException {
        return (RouterRouteScanByEnvResult) actionServiceFactory.getActionService(routerRouteScan).invoke(routerRouteScan);
    }

    /**
     * Scan security group by env security group scan result.
     *
     * @param securityGroupScan the security group scan
     * @return the security group scan result
     * @throws CommonAdapterException      the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public SecurityGroupScanResult scanSecurityGroupByEnv(SecurityGroupScan securityGroupScan)
            throws CommonAdapterException, AdapterUnavailableException {
        return (SecurityGroupScanResult) actionServiceFactory.getActionService(securityGroupScan)
                .invoke(securityGroupScan);
    }

    /**
     * Scan ssh key by env ssh key scan result.
     *
     * @param sshKeyScan the ssh key scan
     * @return the ssh key scan result
     * @throws CommonAdapterException      the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public SshKeyScanResult scanSshKeyByEnv(SshKeyScan sshKeyScan)
            throws CommonAdapterException, AdapterUnavailableException {
        return (SshKeyScanResult) actionServiceFactory.getActionService(sshKeyScan).invoke(sshKeyScan);
    }

    /**
     * 用户自定义设置
     * @param userBusinessBehaviorScan
     * @return
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public UserBusinessBehaviorScanResult scanUserBusinessBehaviorByEnv(UserBusinessBehaviorScan userBusinessBehaviorScan)
            throws CommonAdapterException, AdapterUnavailableException {
        return (UserBusinessBehaviorScanResult) actionServiceFactory.getActionService(userBusinessBehaviorScan).invoke(userBusinessBehaviorScan);
    }

    /**
     * Scan port by env port scan result.
     *
     * @param portScan the port scan
     * @return the port scan result
     * @throws CommonAdapterException      the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public PortScanResult scanPortByEnv(PortScan portScan) throws CommonAdapterException, AdapterUnavailableException {
        return (PortScanResult) actionServiceFactory.getActionService(portScan).invoke(portScan);
    }

    /**
     * Scan storage type by env storage type scan by env result.
     *
     * @param storageScan the storage scan
     * @return the storage type scan by env result
     * @throws CommonAdapterException      the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public StorageTypeScanByEnvResult scanStorageTypeByEnv(StorageTypeScan storageScan)
            throws CommonAdapterException, AdapterUnavailableException {
        return (StorageTypeScanByEnvResult) actionServiceFactory.getActionService(storageScan).invoke(storageScan);
    }

    /**
     * Scan snapshot snapshot scan result.
     *
     * @param snapshotScan the snapshot scan
     * @return the snapshot scan result
     * @throws CommonAdapterException      the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public SnapshotScanResult scanSnapshot(SnapshotScan snapshotScan)
            throws CommonAdapterException, AdapterUnavailableException {
        return (SnapshotScanResult) actionServiceFactory.getActionService(snapshotScan).invoke(snapshotScan);
    }

    /**
     * Scan region region scan result.
     *
     * @param regionScan the region scan
     * @return the region scan result
     * @throws CommonAdapterException      the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public RegionScanResult scanRegion(RegionScan regionScan) throws CommonAdapterException, AdapterUnavailableException {
        return (RegionScanResult) actionServiceFactory.getActionService(regionScan).invoke(regionScan);
    }

    /**
     * Scan region region scan result.
     *
     * @param resourceScan the region scan
     * @return the region scan result
     * @throws CommonAdapterException      the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public ResourceScanResult scanResouce(ResourceScan resourceScan) throws CommonAdapterException, AdapterUnavailableException {
        return (ResourceScanResult) actionServiceFactory.getActionService(resourceScan).invoke(resourceScan);
    }

    /**
     * Scan region region scan result.
     *
     * @param aliWholeZoneScan the ali whole zone scan
     * @return the ali whole zone scan result
     * @throws CommonAdapterException      the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public AliWholeZoneScanResult scanAliWholeZone(AliWholeZoneScan aliWholeZoneScan) throws CommonAdapterException, AdapterUnavailableException {
        return (AliWholeZoneScanResult) actionServiceFactory.getActionService(aliWholeZoneScan).invoke(aliWholeZoneScan);
    }

    public VmScanAloneResult scanVmAlone(VmScanAlone vmScanAlone) throws CommonAdapterException, AdapterUnavailableException {
        return (VmScanAloneResult) actionServiceFactory.getActionService(vmScanAlone).invoke(vmScanAlone);
    }

    public ProjectScanResult scanProject(ProjectScan projectScan) throws CommonAdapterException, AdapterUnavailableException {
        return (ProjectScanResult) actionServiceFactory.getActionService(projectScan).invoke(projectScan);
    }

    public ResourceGroupScanResult scanResourceGroup(ResourceGroupScan resourceGroupScan)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ResourceGroupScanResult) actionServiceFactory.getActionService(resourceGroupScan).invoke(resourceGroupScan);
    }

    public BucketScanResult scanBucket(BucketScan bucketScan)
            throws CommonAdapterException, AdapterUnavailableException {
        return (BucketScanResult) actionServiceFactory.getActionService(bucketScan).invoke(bucketScan);
    }

    public BucketObjectScanResult scanBucketObject(BucketObjectScan bucketObjectScan)
            throws CommonAdapterException, AdapterUnavailableException {
        return (BucketObjectScanResult) actionServiceFactory.getActionService(bucketObjectScan).invoke(bucketObjectScan);
    }

    public BucketPolicyScanResult scanBucketPolicyByEnv(BucketPolicyScan bucketPolicyScan)
            throws CommonAdapterException, AdapterUnavailableException {
        return (BucketPolicyScanResult) actionServiceFactory.getActionService(bucketPolicyScan).invoke(bucketPolicyScan);
    }

    /**
     * Scan network export scan result.
     *
     * @param networkExportScan the networkExportScan scan
     * @return the network scan result
     * @throws CommonAdapterException      the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public NetworkExportScanResult scanNetworkExport(NetworkExportScan networkExportScan)
            throws CommonAdapterException, AdapterUnavailableException {

        return (NetworkExportScanResult) actionServiceFactory.getActionService(networkExportScan)
                .invoke(networkExportScan);
    }

    public CloudOsOwnerResult scanCloudOsOwnersByEnv(ScanCloudOsOwnerByEnv cloudOsOwnerByEnv)
            throws Exception {
        return (CloudOsOwnerResult) actionServiceFactory.getActionService(cloudOsOwnerByEnv).invoke(cloudOsOwnerByEnv);
    }

    public CloudOsBaremetalResult scanCloudOsBaremetal(ScanCloudOsBaremetalByEnv cloudOsOwnerByEnv)
            throws Exception {
        return (CloudOsBaremetalResult) actionServiceFactory.getActionService(cloudOsOwnerByEnv)
                .invoke(cloudOsOwnerByEnv);
    }

    public BaremetalZoneResult scanCloudOsBaremetalZone(ScanCloudOsBareMetalZone cloudOsOwnerByEnv)
            throws Exception {
        return (BaremetalZoneResult) actionServiceFactory.getActionService(cloudOsOwnerByEnv).invoke(cloudOsOwnerByEnv);
    }

    public BaremetalNodeResult scanCloudOsBaremetalNode(ScanCloudOsBareMetalNode cloudOsOwnerByEnv)
            throws Exception {
        return (BaremetalNodeResult) actionServiceFactory.getActionService(cloudOsOwnerByEnv).invoke(cloudOsOwnerByEnv);
    }

    public ZoneNetworkResult scanCloudOsZoneNetwork(ScanCloudOsZoneNetwork cloudOsOwnerByEnv)
            throws Exception {
        return (ZoneNetworkResult) actionServiceFactory.getActionService(cloudOsOwnerByEnv).invoke(cloudOsOwnerByEnv);
    }

    public FlowFeatureGroupScanResult scanCloudOsFlowFeatureGroup(ScanFlowFeatureGroup cloudOsOwnerByEnv)
            throws Exception {
        return (FlowFeatureGroupScanResult) actionServiceFactory.getActionService(cloudOsOwnerByEnv)
                .invoke(cloudOsOwnerByEnv);
    }

    public ScFirewallRuleScanResult scanScFirewallRule(ScFirewallRuleScan ruleScan)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ScFirewallRuleScanResult) actionServiceFactory.getActionService(ruleScan).invoke(ruleScan);
    }

    public ScFirewallStrategyScanResult scanScFirewallStrategy(ScFirewallStrategyScan strategyScan)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ScFirewallStrategyScanResult) actionServiceFactory.getActionService(strategyScan).invoke(strategyScan);
    }

    public ScFirewallScanResult scanScFirewall(ScFirewallScan firewallScan)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ScFirewallScanResult) actionServiceFactory.getActionService(firewallScan).invoke(firewallScan);
    }

    /**
     * Scan service chain scan result.
     *
     * @param serviceChainScan the service chain scan
     * @return the service chain scan result
     * @throws CommonAdapterException      the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public ServiceChainScanResult scanServiceChain(ServiceChainScan serviceChainScan)
            throws CommonAdapterException, AdapterUnavailableException {

        return (ServiceChainScanResult) actionServiceFactory.getActionService(serviceChainScan).invoke(serviceChainScan);
    }

    public ScanFirewallObjectGroupsResult scanFirewallObjectGroups(ScanFirewallObjectGroups objectGroups)
            throws Exception {
        return (ScanFirewallObjectGroupsResult) actionServiceFactory.getActionService(objectGroups).invoke(objectGroups);
    }

    public VPCFirewallScanResult scanCloudOsVpcFirewall(VPCFirewallScan vpcFirewallScan)
            throws Exception {
        return (VPCFirewallScanResult) actionServiceFactory.getActionService(vpcFirewallScan).invoke(vpcFirewallScan);
    }

    public MqInstanceScanResult scanMqInstances(MqInstanceScan mqInstanceScan) throws Exception {
        return (MqInstanceScanResult) actionServiceFactory.getActionService(mqInstanceScan).invoke(mqInstanceScan);
    }

    public KafkaTopicScanResult scanKafkaTopics(KafkaTopicScan kafkaTopicScan) throws Exception {
        return (KafkaTopicScanResult) actionServiceFactory.getActionService(kafkaTopicScan).invoke(kafkaTopicScan);
    }

    public GpuDeviceScanResult scanGpuDevices(GpuDeviceScan gpuDeviceScan) throws Exception {
        return (GpuDeviceScanResult) actionServiceFactory.getActionService(gpuDeviceScan).invoke(gpuDeviceScan);
    }

    public GpuGroupScanResult scanGpuGroups(GpuGroupScan gpuGroupScan) throws Exception {
        return (GpuGroupScanResult) actionServiceFactory.getActionService(gpuGroupScan).invoke(gpuGroupScan);
    }

    public DcsAvailableZoneScanResult getDcsAvailableZones(DcsAvailableZoneScan scan)
            throws CommonAdapterException, AdapterUnavailableException {
        return (DcsAvailableZoneScanResult) actionServiceFactory.getActionService(scan).invoke(scan);
    }

    public DcsProductScanResult getDcsProducts(DcsProductScan scan)
            throws CommonAdapterException, AdapterUnavailableException {
        return (DcsProductScanResult) actionServiceFactory.getActionService(scan).invoke(scan);
    }

    public DcsScanResult getDcs(DcsScan scan) throws CommonAdapterException, AdapterUnavailableException {
        return (DcsScanResult) actionServiceFactory.getActionService(scan).invoke(scan);
    }

    public DcsConfigScanResult getDcsConfig(DcsConfigScan scan)
            throws CommonAdapterException, AdapterUnavailableException {
        return (DcsConfigScanResult) actionServiceFactory.getActionService(scan).invoke(scan);
    }

    public ServerGroupScanResult scanServerGroup(ServerGroupScan serverGroupScan)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ServerGroupScanResult) actionServiceFactory.getActionService(serverGroupScan).invoke(serverGroupScan);
    }

    public HostRelateInstScanByEnvResult scanHostRelateInstByEnv(HostRelateInstScanByEnv hostRelateInstScan)
            throws CommonAdapterException, AdapterUnavailableException {
        return (HostRelateInstScanByEnvResult) actionServiceFactory.getActionService(hostRelateInstScan)
                                                                   .invoke(hostRelateInstScan);
    }

    public MachineExtendInfoResult apiQuery(MachineExtendInfo machineExtendInfo)
            throws CommonAdapterException, AdapterUnavailableException {
        return (MachineExtendInfoResult) actionServiceFactory.getActionService(machineExtendInfo)
                                                             .invoke(machineExtendInfo);
    }

    public FloatingIpLineQueryResult floatingIpLineQuery(FloatingIpLineQuery floatingIpLineQuery)
            throws CommonAdapterException, AdapterUnavailableException {
        return (FloatingIpLineQueryResult) actionServiceFactory.getActionService(floatingIpLineQuery)
                                                               .invoke(floatingIpLineQuery);
    }

    public IamUserScanResult getIamUser(IamUserScan scan)
            throws CommonAdapterException, AdapterUnavailableException {
        return (IamUserScanResult) actionServiceFactory.getActionService(scan).invoke(scan);
    }


}
