/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.adapter.facade.handler;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.com.cloudstar.rightcloud.adapter.pojo.price.RdsPriceCalculate;
import cn.com.cloudstar.rightcloud.adapter.pojo.price.result.RdsPriceCalculateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.AccountCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.AccountDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.AccountUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.ConnectStringApply;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.ConnectStringRelease;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.ConnectStringUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.DBEngineQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.DBInstanceClassQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.DBInstanceCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.DBInstanceDatabaseCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.DBInstanceDatabaseDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.DBInstanceDatabasePrivilege;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.DBInstanceDescribe;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.DBInstanceDescriptionModify;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.DBInstanceEngineQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.DBInstanceEnlargeVolume;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.DBInstanceFailover;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.DBInstanceFloatingIPAction;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.DBInstancePortUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.DBInstanceReconfig;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.DBInstanceRelease;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.DBInstanceRestart;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.DBInstanceZoneQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.IpArrayUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.AccountResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.ConnectStringReleaseResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.ConnectStringResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.ConnectStringUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.DBEngineQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.DBInstanceClassQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.DBInstanceCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.DBInstanceDatabaseCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.DBInstanceDatabaseDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.DBInstanceDatabasePrivilegeResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.DBInstanceDescribeResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.DBInstanceEngineQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.DBInstanceEnlargeVolumeResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.DBInstanceFailoverResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.DBInstanceFloatingIPActionResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.DBInstancePortUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.DBInstanceReconfigResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.DBInstanceReleaseResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.DBInstanceRestartResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.DBInstanceResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.DBInstanceZoneQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.IpArrayResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.DeleteHPCAccount;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.result.DeleteHPCAccountResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.RdsRenewInstance;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmRenewInstanceResult;
import cn.com.cloudstar.rightcloud.driver.core.ActionServiceFactory;
import cn.com.cloudstar.rightcloud.driver.core.exception.AdapterUnavailableException;
import cn.com.cloudstar.rightcloud.driver.core.exception.CommonAdapterException;

/**
 * The type RdsHandler. <p>
 *
 * <AUTHOR>
 * @date 2017 /11/22
 */
@Service
public class RdsHandler {

    @Autowired
    private ActionServiceFactory actionServiceFactory;


    /**
     * Update ip array ip array result.
     *
     * @param ipArrayUpdate the ip array update
     * @return the ip array result
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public IpArrayResult updateIpArray(IpArrayUpdate ipArrayUpdate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (IpArrayResult) actionServiceFactory.getActionService(ipArrayUpdate).invoke(ipArrayUpdate);
    }


    /**
     * Create account account result.
     *
     * @param accountCreate the account create
     * @return the account result
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public AccountResult createAccount(AccountCreate accountCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (AccountResult) actionServiceFactory.getActionService(accountCreate).invoke(accountCreate);
    }


    /**
     * Update account account result.
     *
     * @param accountUpdate the account update
     * @return the account result
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public AccountResult updateAccount(AccountUpdate accountUpdate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (AccountResult) actionServiceFactory.getActionService(accountUpdate).invoke(accountUpdate);
    }


    /**
     * Update connect string connect string result.
     *
     * @param connectStringUpdate the connect string update
     *
     * @return the connect string result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public ConnectStringUpdateResult updateConnectString(ConnectStringUpdate connectStringUpdate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ConnectStringUpdateResult) actionServiceFactory.getActionService(connectStringUpdate)
                                                               .invoke(connectStringUpdate);
    }


    /**
     * Apply connect string connect string result.
     *
     * @param connectStringApply the connect string apply
     * @return the connect string result
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public ConnectStringResult applyConnectString(ConnectStringApply connectStringApply)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ConnectStringResult) actionServiceFactory.getActionService(connectStringApply)
                                                         .invoke(connectStringApply);
    }


    /**
     * Release connect string connect string result.
     *
     * @param connectStringRelease the connect string release
     *
     * @return the connect string result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public ConnectStringReleaseResult releaseConnectString(ConnectStringRelease connectStringRelease)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ConnectStringReleaseResult) actionServiceFactory.getActionService(connectStringRelease)
                                                                .invoke(connectStringRelease);
    }


    /**
     * Restart rds db instance db instance restart result.
     *
     * @param dbInstanceRestart the db instance restart
     * @return the db instance restart result
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public DBInstanceRestartResult restartRdsDBInstance(DBInstanceRestart dbInstanceRestart)
            throws CommonAdapterException, AdapterUnavailableException {
        return (DBInstanceRestartResult) actionServiceFactory.getActionService(dbInstanceRestart)
                                                             .invoke(dbInstanceRestart);
    }

    public DBInstanceEnlargeVolumeResult enlargeVolumeRdsDBInstance(DBInstanceEnlargeVolume dbInstanceEnlargeVolume)
            throws CommonAdapterException, AdapterUnavailableException {
        return (DBInstanceEnlargeVolumeResult) actionServiceFactory.getActionService(dbInstanceEnlargeVolume)
                                                             .invoke(dbInstanceEnlargeVolume);
    }


    /**
     * Modify rds db instance description db instance result.
     *
     * @param dbInstanceDescriptionModify the db instance description modify
     * @return the db instance result
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public DBInstanceResult modifyRdsDBInstanceDescription(DBInstanceDescriptionModify dbInstanceDescriptionModify)
            throws CommonAdapterException, AdapterUnavailableException {
        return (DBInstanceResult) actionServiceFactory.getActionService(dbInstanceDescriptionModify)
                                                      .invoke(dbInstanceDescriptionModify);
    }


    /**
     * Release rds db instance db instance release result.
     *
     * @param dbInstanceRelease the db instance release
     * @return the db instance release result
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public DBInstanceReleaseResult releaseRdsDBInstance(DBInstanceRelease dbInstanceRelease)
            throws CommonAdapterException, AdapterUnavailableException {
        return (DBInstanceReleaseResult) actionServiceFactory.getActionService(dbInstanceRelease)
                                                             .invoke(dbInstanceRelease);
    }


    /**
     * Create rds db instance db instance create result.
     *
     * @param dbInstanceCreate the db instance create
     * @return the db instance create result
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public DBInstanceCreateResult createRdsDBInstance(DBInstanceCreate dbInstanceCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (DBInstanceCreateResult) actionServiceFactory.getActionService(dbInstanceCreate)
                                                            .invoke(dbInstanceCreate);
    }


    /**
     * Describe rds db instance db instance describe result.
     *
     * @param dbInstanceDescribe the db instance describe
     * @return the db instance describe result
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public DBInstanceDescribeResult describeRdsDBInstance(DBInstanceDescribe dbInstanceDescribe)
            throws CommonAdapterException, AdapterUnavailableException {
        return (DBInstanceDescribeResult) actionServiceFactory.getActionService(dbInstanceDescribe)
                                                              .invoke(dbInstanceDescribe);
    }

    /**
     * price calculate result.
     *
     * @param priceCalculate
     * @return
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public RdsPriceCalculateResult getPrice(RdsPriceCalculate priceCalculate) throws CommonAdapterException, AdapterUnavailableException {
        return (RdsPriceCalculateResult) actionServiceFactory.getActionService(priceCalculate).invoke(priceCalculate);
    }

    /**
     * db engine result.
     *
     * @param dbEngineQuery
     * @return
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public DBEngineQueryResult queryEngines(DBEngineQuery dbEngineQuery) throws CommonAdapterException, AdapterUnavailableException {
        return (DBEngineQueryResult) actionServiceFactory.getActionService(dbEngineQuery).invoke(dbEngineQuery);
    }

    /**
     * db instance type result.
     *
     * @param dbInstanceClassQuery
     * @return
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public DBInstanceClassQueryResult queryInstanceClasses(DBInstanceClassQuery dbInstanceClassQuery) throws CommonAdapterException, AdapterUnavailableException {
        return (DBInstanceClassQueryResult) actionServiceFactory.getActionService(dbInstanceClassQuery).invoke(dbInstanceClassQuery);
    }

    /**
     *
     * @param dbInstanceEngineQuery
     * @return
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public DBInstanceEngineQueryResult queryInstanceEngines(DBInstanceEngineQuery dbInstanceEngineQuery) throws CommonAdapterException, AdapterUnavailableException {
        return (DBInstanceEngineQueryResult) actionServiceFactory.getActionService(dbInstanceEngineQuery).invoke(dbInstanceEngineQuery);
    }

    /**
     * db instance type result.
     *
     * @param dbInstanceZoneQuery
     * @return
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public DBInstanceZoneQueryResult queryInstanceZones(DBInstanceZoneQuery dbInstanceZoneQuery) throws CommonAdapterException, AdapterUnavailableException {
        return (DBInstanceZoneQueryResult) actionServiceFactory.getActionService(dbInstanceZoneQuery).invoke(dbInstanceZoneQuery);
    }

    /**
     * 创建rds实例的数据库
     * @param dbInstanceDatabaseCreate
     * @return
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public DBInstanceDatabaseCreateResult createDBInstanceDatabase(DBInstanceDatabaseCreate dbInstanceDatabaseCreate) throws CommonAdapterException, AdapterUnavailableException {
        return (DBInstanceDatabaseCreateResult) actionServiceFactory.getActionService(dbInstanceDatabaseCreate).invoke(dbInstanceDatabaseCreate);
    }

    /**
     * 删除rds实例的数据库
     * @param dbInstanceDatabaseDelete
     * @return
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public DBInstanceDatabaseDeleteResult deleteDBInstanceDatabase(DBInstanceDatabaseDelete dbInstanceDatabaseDelete) throws CommonAdapterException, AdapterUnavailableException {
        return (DBInstanceDatabaseDeleteResult) actionServiceFactory.getActionService(dbInstanceDatabaseDelete).invoke(dbInstanceDatabaseDelete);
    }

    /**
     * 授权rds实例的数据库
     * @param dbInstanceDatabasePrivilege
     * @return
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public DBInstanceDatabasePrivilegeResult privilegeDBInstanceDatabase(DBInstanceDatabasePrivilege dbInstanceDatabasePrivilege) throws CommonAdapterException, AdapterUnavailableException {
        return (DBInstanceDatabasePrivilegeResult) actionServiceFactory.getActionService(dbInstanceDatabasePrivilege).invoke(dbInstanceDatabasePrivilege);
    }

    /**
     * rds帐号删除
     *
     * @param accountDelete
     *
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public AccountResult deleteAccount(AccountDelete accountDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (AccountResult) actionServiceFactory.getActionService(accountDelete).invoke(accountDelete);
    }

    public DeleteHPCAccountResult deleteHPCAccount(DeleteHPCAccount accountDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (DeleteHPCAccountResult) actionServiceFactory.getActionService(accountDelete).invoke(accountDelete);
    }


    public DBInstanceFloatingIPActionResult updateRdsInstanceFloatingip(DBInstanceFloatingIPAction ipAction)
            throws CommonAdapterException, AdapterUnavailableException {
        return (DBInstanceFloatingIPActionResult) actionServiceFactory.getActionService(ipAction).invoke(ipAction);
    }


    public DBInstanceReconfigResult reconfigRdsInstance(DBInstanceReconfig dbInstanceReconfig)
            throws CommonAdapterException, AdapterUnavailableException {
        return (DBInstanceReconfigResult) actionServiceFactory.getActionService(dbInstanceReconfig)
                                                              .invoke(dbInstanceReconfig);
    }


    public DBInstancePortUpdateResult updateRdsInstancePort(DBInstancePortUpdate dbInstancePortUpdate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (DBInstancePortUpdateResult) actionServiceFactory.getActionService(dbInstancePortUpdate)
                                                                .invoke(dbInstancePortUpdate);
    }

    public DBInstanceFailoverResult failoverRdsInstance(DBInstanceFailover dbInstanceFailover)
            throws CommonAdapterException, AdapterUnavailableException {
        return (DBInstanceFailoverResult) actionServiceFactory.getActionService(dbInstanceFailover)
                                                              .invoke(dbInstanceFailover);
    }

    public VmRenewInstanceResult renewRds(RdsRenewInstance rdsRenewInstance)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VmRenewInstanceResult) actionServiceFactory.getActionService(rdsRenewInstance).invoke(rdsRenewInstance);
    }
}
