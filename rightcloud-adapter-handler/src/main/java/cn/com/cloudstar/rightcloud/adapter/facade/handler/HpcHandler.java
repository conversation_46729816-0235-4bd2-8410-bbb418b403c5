package cn.com.cloudstar.rightcloud.adapter.facade.handler;

import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.*;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.GrantJobTemplateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.FDHPCClusterResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.HPCPreDrpMgmtResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.HPCShareStopJobResult;
import cn.com.cloudstar.rightcloud.driver.core.ActionServiceFactory;
import cn.com.cloudstar.rightcloud.driver.core.exception.AdapterUnavailableException;
import cn.com.cloudstar.rightcloud.driver.core.exception.CommonAdapterException;
import cn.com.cloudstar.rightcloud.driver.pojo.base.BaseResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 */
@Service
public class HpcHandler {

    @Autowired
    private ActionServiceFactory actionServiceFactory;

    public HPCPreDrpMgmtResult hpcPreDrpMgmt(HPCPreDrpMgmt param)  throws CommonAdapterException, AdapterUnavailableException{
        return (HPCPreDrpMgmtResult) actionServiceFactory.getActionService(param).invoke(param);
    }

    public FDHPCClusterResult clusters(FDHPCClusterRequest param)  throws CommonAdapterException, AdapterUnavailableException{
        return (FDHPCClusterResult) actionServiceFactory.getActionService(param).invoke(param);
    }

    public HPCShareStopJobResult hpcShareStopJob(HPCShareStopJob hpcShareStopJob) throws CommonAdapterException, AdapterUnavailableException {
        return (HPCShareStopJobResult) actionServiceFactory.getActionService(hpcShareStopJob).invoke(hpcShareStopJob);
    }

    public GrantJobTemplateResult grantJobTemplate(GrantJobTemplate grantJobTemplate) throws CommonAdapterException, AdapterUnavailableException {
        return (GrantJobTemplateResult) actionServiceFactory.getActionService(grantJobTemplate).invoke(grantJobTemplate);
    }

    public BaseResult tenantStatusSync(HPCTenantStatusSyncRequest param)
            throws CommonAdapterException, AdapterUnavailableException {
        return actionServiceFactory.getActionService(param).invoke(param);
    }
}
