/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.adapter.facade.handler;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.com.cloudstar.rightcloud.adapter.pojo.block.BlockBackupCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.block.BlockBackupRecovery;
import cn.com.cloudstar.rightcloud.adapter.pojo.block.BlockClone;
import cn.com.cloudstar.rightcloud.adapter.pojo.block.BlockInfoGet;
import cn.com.cloudstar.rightcloud.adapter.pojo.block.BlockList;
import cn.com.cloudstar.rightcloud.adapter.pojo.block.BlockSnapshotCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.block.BlockSnapshotDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.block.BlockSnapshotRecovery;
import cn.com.cloudstar.rightcloud.adapter.pojo.block.result.BlockBackupCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.block.result.BlockBackupRecoveryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.block.result.BlockCloneResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.block.result.BlockInfoGetResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.block.result.BlockListResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.block.result.BlockSnapshotCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.block.result.BlockSnapshotDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.block.result.BlockSnapshotRecovryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.datastore.DataStoreCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.datastore.DataStoreDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.datastore.DataStoreExtend;
import cn.com.cloudstar.rightcloud.adapter.pojo.datastore.DataStoreReScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.datastore.DataStoreRefresh;
import cn.com.cloudstar.rightcloud.adapter.pojo.datastore.result.DataStoreCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.datastore.result.DataStoreDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.datastore.result.DataStoreExtendResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.datastore.result.DataStoreReScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.datastore.result.DataStoreRefreshResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.DiskAttach;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.DiskBackupCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.DiskBackupDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.DiskBackupRestore;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.DiskConfigQuotaQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.DiskCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.DiskDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.DiskDetach;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.DiskExpand;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.DiskExtend;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.DiskUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.FileSystemCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.FileSystemDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.PartitionCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.PartitionDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.PartitionUnMount;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.PartitionUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.result.DiskAttachResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.result.DiskBackupCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.result.DiskBackupDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.result.DiskBackupRestoreResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.result.DiskConfigQuotaQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.result.DiskCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.result.DiskDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.result.DiskDetachResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.result.DiskExpandResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.result.DiskExtendResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.result.DiskUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.result.FileSystemCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.result.FileSystemDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.result.PartitionCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.result.PartitionDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.result.PartitionUnMountResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.result.PartitionUpdateResult;
import cn.com.cloudstar.rightcloud.driver.core.ActionServiceFactory;
import cn.com.cloudstar.rightcloud.driver.core.exception.AdapterUnavailableException;
import cn.com.cloudstar.rightcloud.driver.core.exception.CommonAdapterException;

/**
 * The type Storage handler.
 */
@Service
public class StorageHandler {

    @Autowired
    private ActionServiceFactory actionServiceFactory;

    /**
     * Expand disk disk expand result.
     *
     * @param diskExpand the disk expand
     * @return the disk expand result
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public DiskExpandResult expandDisk(DiskExpand diskExpand)
            throws CommonAdapterException, AdapterUnavailableException {
        return (DiskExpandResult) actionServiceFactory.getActionService(diskExpand).invoke(diskExpand);
    }

    /**
     * Expand disk disk expand result.
     *
     * @param diskUpdate the disk update
     * @return the disk update result
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public DiskUpdateResult updateDisk(DiskUpdate diskUpdate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (DiskUpdateResult) actionServiceFactory.getActionService(diskUpdate).invoke(diskUpdate);
    }

    /**
     * Create disk disk create result.
     *
     * @param diskCreate the disk create
     * @return the disk create result
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public DiskCreateResult createDisk(DiskCreate diskCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (DiskCreateResult) actionServiceFactory.getActionService(diskCreate).invoke(diskCreate);
    }

    /**
     * Delete disk disk delete result.
     *
     * @param diskDelete the disk delete
     * @return the disk delete result
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public DiskDeleteResult deleteDisk(DiskDelete diskDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (DiskDeleteResult) actionServiceFactory.getActionService(diskDelete).invoke(diskDelete);
    }

    /**
     * Attach disk disk attach result.
     *
     * @param diskAttach the disk attach
     * @return the disk attach result
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public DiskAttachResult attachDisk(DiskAttach diskAttach)
            throws CommonAdapterException, AdapterUnavailableException {
        return (DiskAttachResult) actionServiceFactory.getActionService(diskAttach).invoke(diskAttach);
    }

    /**
     * Detach disk disk detach result.
     *
     * @param diskDetach the disk detach
     * @return the disk detach result
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public DiskDetachResult detachDisk(DiskDetach diskDetach)
            throws CommonAdapterException, AdapterUnavailableException {
        return (DiskDetachResult) actionServiceFactory.getActionService(diskDetach).invoke(diskDetach);
    }


    /**
     * Gets block list.
     *
     * @param blockList the block list
     * @return the block list
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public BlockListResult getBlockList(BlockList blockList)
            throws CommonAdapterException, AdapterUnavailableException {
        return (BlockListResult) actionServiceFactory.getActionService(blockList).invoke(blockList);
    }


    /**
     * Gets block info.
     *
     * @param blockInfoGet the block info get
     * @return the block info
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public BlockInfoGetResult getBlockInfo(BlockInfoGet blockInfoGet)
            throws CommonAdapterException, AdapterUnavailableException {
        return (BlockInfoGetResult) actionServiceFactory.getActionService(blockInfoGet).invoke(blockInfoGet);
    }


    /**
     * Create block snapshot block snapshot create result.
     *
     * @param blockSnapshotCreate the block snapshot create
     * @return the block snapshot create result
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public BlockSnapshotCreateResult createBlockSnapshot(BlockSnapshotCreate blockSnapshotCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (BlockSnapshotCreateResult) actionServiceFactory.getActionService(blockSnapshotCreate)
                                                               .invoke(blockSnapshotCreate);
    }


    /**
     * Create block backup block backup create result.
     *
     * @param backupCreate the backup create
     * @return the block backup create result
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public BlockBackupCreateResult createBlockBackup(BlockBackupCreate backupCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (BlockBackupCreateResult) actionServiceFactory.getActionService(backupCreate).invoke(backupCreate);
    }


    /**
     * Recovery block bachup block backup recovery result.
     *
     * @param backupRecovery the backup recovery
     * @return the block backup recovery result
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public BlockBackupRecoveryResult recoveryBlockBachup(BlockBackupRecovery backupRecovery)
            throws CommonAdapterException, AdapterUnavailableException {
        return (BlockBackupRecoveryResult) actionServiceFactory.getActionService(backupRecovery).invoke(backupRecovery);
    }


    /**
     * Recovery block snapshot block snapshot recovry result.
     *
     * @param blockSnapshotRecovery the block snapshot recovery
     * @return the block snapshot recovry result
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public BlockSnapshotRecovryResult recoveryBlockSnapshot(BlockSnapshotRecovery blockSnapshotRecovery)
            throws CommonAdapterException, AdapterUnavailableException {
        return (BlockSnapshotRecovryResult) actionServiceFactory.getActionService(blockSnapshotRecovery)
                                                                .invoke(blockSnapshotRecovery);
    }


    /**
     * Clone block block clone result.
     *
     * @param blockClone the block clone
     * @return the block clone result
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public BlockCloneResult cloneBlock(BlockClone blockClone)
            throws CommonAdapterException, AdapterUnavailableException {
        return (BlockCloneResult) actionServiceFactory.getActionService(blockClone).invoke(blockClone);
    }


    /**
     * Delete block snapshot block snapshot delete result.
     *
     * @param blockSnapshotDelete the block snapshot delete
     * @return the block snapshot delete result
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public BlockSnapshotDeleteResult deleteBlockSnapshot(BlockSnapshotDelete blockSnapshotDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (BlockSnapshotDeleteResult) actionServiceFactory.getActionService(blockSnapshotDelete)
                                                               .invoke(blockSnapshotDelete);
    }


    /**
     * Create data store data store create result.
     *
     * @param create the create
     * @return the data store create result
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public DataStoreCreateResult createDataStore(DataStoreCreate create)
            throws CommonAdapterException, AdapterUnavailableException {
        return (DataStoreCreateResult) actionServiceFactory.getActionService(create).invoke(create);
    }


    /**
     * Delete data store data store delete result.
     *
     * @param delete the delete
     * @return the data store delete result
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public DataStoreDeleteResult deleteDataStore(DataStoreDelete delete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (DataStoreDeleteResult) actionServiceFactory.getActionService(delete).invoke(delete);
    }


    /**
     * Extend data store data store extend result.
     *
     * @param extend the extend
     * @return the data store extend result
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public DataStoreExtendResult extendDataStore(DataStoreExtend extend)
            throws CommonAdapterException, AdapterUnavailableException {
        return (DataStoreExtendResult) actionServiceFactory.getActionService(extend).invoke(extend);
    }


    /**
     * Refresh data store data store refresh result.
     *
     * @param refresh the refresh
     * @return the data store refresh result
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public DataStoreRefreshResult refreshDataStore(DataStoreRefresh refresh)
            throws CommonAdapterException, AdapterUnavailableException {
        return (DataStoreRefreshResult) actionServiceFactory.getActionService(refresh).invoke(refresh);
    }


    /**
     * Rescan data store data store re scan result.
     *
     * @param rescan the rescan
     * @return the data store re scan result
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public DataStoreReScanResult rescanDataStore(DataStoreReScan rescan)
            throws CommonAdapterException, AdapterUnavailableException {
        return (DataStoreReScanResult) actionServiceFactory.getActionService(rescan).invoke(rescan);
    }

    public DiskConfigQuotaQueryResult queryDiskConfig(DiskConfigQuotaQuery diskConfigQuotaQuery)
            throws CommonAdapterException, AdapterUnavailableException {
        return (DiskConfigQuotaQueryResult) actionServiceFactory.getActionService(diskConfigQuotaQuery).invoke(diskConfigQuotaQuery);
    }

    /**
     *  创建磁盘备份
     * @param diskBackupCreate
     * @return
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public DiskBackupCreateResult createDiskBackup(DiskBackupCreate diskBackupCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (DiskBackupCreateResult) actionServiceFactory.getActionService(diskBackupCreate)
                                                            .invoke(diskBackupCreate);
    }

    /**
     * 删除磁盘备份
     * @param diskBackupDelete
     * @return
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public DiskBackupDeleteResult deleteDiskBackup(DiskBackupDelete diskBackupDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (DiskBackupDeleteResult) actionServiceFactory.getActionService(diskBackupDelete)
                                                            .invoke(diskBackupDelete);
    }

    /**
     * 恢复磁盘备份
     * @param diskBackupRestore
     * @return
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public DiskBackupRestoreResult restoreDiskBackup(DiskBackupRestore diskBackupRestore)
            throws CommonAdapterException, AdapterUnavailableException {
        return (DiskBackupRestoreResult) actionServiceFactory.getActionService(diskBackupRestore)
                                                             .invoke(diskBackupRestore);
    }

    /**
     * 扩展磁盘
     * @param diskExtend
     * @return
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public DiskExtendResult extendDisk(DiskExtend diskExtend)
            throws CommonAdapterException, AdapterUnavailableException {
        return (DiskExtendResult) actionServiceFactory.getActionService(diskExtend).invoke(diskExtend);
    }

    public PartitionCreateResult createPartition(PartitionCreate create)
            throws CommonAdapterException, AdapterUnavailableException {
        return (PartitionCreateResult) actionServiceFactory.getActionService(create).invoke(create);
    }

    public PartitionUpdateResult updatePartition(PartitionUpdate update)
            throws CommonAdapterException, AdapterUnavailableException {
        return (PartitionUpdateResult) actionServiceFactory.getActionService(update).invoke(update);
    }

    public PartitionDeleteResult deletePartition(PartitionDelete delete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (PartitionDeleteResult) actionServiceFactory.getActionService(delete).invoke(delete);
    }

    public PartitionUnMountResult unMountPartition(PartitionUnMount unMount)
            throws CommonAdapterException, AdapterUnavailableException {
        return (PartitionUnMountResult) actionServiceFactory.getActionService(unMount).invoke(unMount);
    }

    public FileSystemCreateResult createFileSystem(FileSystemCreate create)
            throws CommonAdapterException, AdapterUnavailableException {
        return (FileSystemCreateResult) actionServiceFactory.getActionService(create).invoke(create);
    }

    public FileSystemDeleteResult deleteFileSystem(FileSystemDelete delete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (FileSystemDeleteResult) actionServiceFactory.getActionService(delete).invoke(delete);
    }
}
