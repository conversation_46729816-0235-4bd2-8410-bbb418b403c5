/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.adapter.facade.util;

import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

import lombok.extern.slf4j.Slf4j;

/**
 * The type BeanUtil.
 *
 * Created on 2018/11/13
 *
 * <AUTHOR>
 */
@Slf4j
public class BeanUtil {

    /**
     * 对象拷贝
     *
     * @param <T>       the type parameter
     * @param srcObj    the src obj
     * @param destClass the dest class
     * @return the t
     */
    public static <T> T transformBean(Object srcObj, Class<T> destClass) {
        T t = null;
        try {
            t = destClass.newInstance();
            transformBeanObj(srcObj, t);
        } catch (InstantiationException | IllegalAccessException e) {
            log.error(e.getMessage());
        }
        return t;
    }

    /**
     * 对象拷贝
     *
     * @param srcObj  the src obj
     * @param destObj the dest obj
     * @return the t
     */
    public static void transformBeanObj(Object srcObj, Object destObj) {
        BeanUtils.copyProperties(srcObj, destObj);
    }

    /**
     * 对象List拷贝
     *
     * @param <T>        the type parameter
     * @param srcObjList the src obj list
     * @param destClass  the dest class
     * @return the list
     */
    public static <T> List<T> transformBeanList(List<?> srcObjList, Class<T> destClass) {
        List<T> destList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(srcObjList)) {
            T t = null;
            for (Object src : srcObjList) {
                try {
                    t = destClass.newInstance();
                    BeanUtils.copyProperties(src, t);
                    destList.add(t);
                } catch (InstantiationException | IllegalAccessException e) {
                    log.error(e.getMessage());
                }
            }
        }

        return destList;
    }
}
