/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.adapter.facade.handler;

import cn.com.cloudstar.rightcloud.adapter.pojo.admin.*;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.CCSPIntegralityHashResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.IamAgencyDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.IamCloudServiceCustomPolicyResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.IamCredentialResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.IamCustomPolicyDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.IamCustomPolicyListResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.IamCustomPolicyQueryInfoResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.IamIdentityProviderResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.IamIdentityProvidersListResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.IamPermanentAccessKeyResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.IamPermissionsListResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.IamProjectsListResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.IamSubUserResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.IamUserCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.IamUserDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.IamUserGroupResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.IamUserUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.IamUsersListResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.auth.AuthCloud;
import cn.com.cloudstar.rightcloud.adapter.pojo.auth.result.AuthCloudResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.MABmsDelJob;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.MABmsStarJob;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.MABmsStopJob;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.MASharePoolsStopJob;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.result.MABmsDelResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.result.MABmsJobResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.mq.QueryOrderDetail;
import cn.com.cloudstar.rightcloud.adapter.pojo.mq.result.QueryOrderDetailResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.other.DisconnectEnv;
import cn.com.cloudstar.rightcloud.adapter.pojo.other.result.DisconnectEnvResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.res.ResPools;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.MountPreResShare;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.MountPreShareResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.SharePredeployQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.SharePredeployQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.ActivationCCUser;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.Daccount;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.Duser;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.TestPassRequest;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.result.ActivationCCUserResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.result.CheckCCPResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.result.DaccountResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.result.DuserResult;
import cn.com.cloudstar.rightcloud.driver.core.ActionServiceFactory;
import cn.com.cloudstar.rightcloud.driver.core.exception.AdapterUnavailableException;
import cn.com.cloudstar.rightcloud.driver.core.exception.CommonAdapterException;
import cn.com.cloudstar.rightcloud.driver.pojo.base.BaseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * DESC: 云环境认证
 *
 * <AUTHOR>
 * @date 2018-11-29 14:56
 */
@Service
@Slf4j
public class AuthHandler {

    @Autowired
    private ActionServiceFactory actionServiceFactory;

    public AuthCloudResult authCloud(AuthCloud authCloud) throws CommonAdapterException, AdapterUnavailableException {
        return (AuthCloudResult) actionServiceFactory.getActionService(authCloud).invoke(authCloud);
    }

    public DisconnectEnvResult disconnectEnv(DisconnectEnv disconnectEnv)
            throws CommonAdapterException, AdapterUnavailableException {
        return (DisconnectEnvResult) actionServiceFactory.getActionService(disconnectEnv).invoke(disconnectEnv);
    }

    public IamUserCreateResult createIam(IamUserCreate create)
            throws CommonAdapterException, AdapterUnavailableException {
        return (IamUserCreateResult) actionServiceFactory.getActionService(create).invoke(create);
    }

    public IamUserUpdateResult updateIam(IamUserUpdate update)
            throws CommonAdapterException, AdapterUnavailableException {
        return (IamUserUpdateResult) actionServiceFactory.getActionService(update).invoke(update);
    }

    public IamUserDeleteResult deleteIam(IamUserDelete delete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (IamUserDeleteResult) actionServiceFactory.getActionService(delete).invoke(delete);
    }

    public QueryOrderDetailResult queryOrder(QueryOrderDetail detail)
            throws CommonAdapterException, AdapterUnavailableException {
        return (QueryOrderDetailResult) actionServiceFactory.getActionService(detail).invoke(detail);
    }

    public IamIdentityProvidersListResult queryIamIdentityProvidersList(IamIdentityProvidersListGet iamIdentityProvidersListGet)
            throws CommonAdapterException, AdapterUnavailableException {
        return (IamIdentityProvidersListResult) actionServiceFactory.getActionService(iamIdentityProvidersListGet).invoke(iamIdentityProvidersListGet);
    }

    public IamIdentityProviderResult queryIamIdentityProvider(IamIdentityProviderGet iamIdentityProviderGet)
            throws CommonAdapterException, AdapterUnavailableException {
        return (IamIdentityProviderResult) actionServiceFactory.getActionService(iamIdentityProviderGet).invoke(iamIdentityProviderGet);
    }

    public IamIdentityProviderResult createIamIdentityProvider(IamIdentityProviderCreate iamIdentityProviderCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (IamIdentityProviderResult) actionServiceFactory.getActionService(iamIdentityProviderCreate).invoke(iamIdentityProviderCreate);
    }

    public IamUserGroupResult queryIamUserGroup(IamUserGroupGet iamUserGroupGet)
            throws CommonAdapterException, AdapterUnavailableException {
        return (IamUserGroupResult) actionServiceFactory.getActionService(iamUserGroupGet).invoke(iamUserGroupGet);
    }

    public IamUserGroupResult createIamUserGroup(IamUserGroupCreate iamUserGroupCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (IamUserGroupResult) actionServiceFactory.getActionService(iamUserGroupCreate).invoke(iamUserGroupCreate);
    }

    public IamPermissionsListResult queryIamPermissionsList(IamPermissionsListGet iamPermissionsListGet)
            throws CommonAdapterException, AdapterUnavailableException {
        return (IamPermissionsListResult) actionServiceFactory.getActionService(iamPermissionsListGet).invoke(iamPermissionsListGet);
    }

    public BaseResult createIamGroupWithProjectPermission(IamGroupWithProjectPermissionCreate projectPermissionCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return actionServiceFactory.getActionService(projectPermissionCreate).invoke(projectPermissionCreate);
    }

    public IamProjectsListResult queryIamProjectsList(IamProjectsListGet iamProjectsListGet)
            throws CommonAdapterException, AdapterUnavailableException {
        return (IamProjectsListResult) actionServiceFactory.getActionService(iamProjectsListGet).invoke(iamProjectsListGet);
    }

    public BaseResult createIamMapping(IamMappingCreate iamMappingCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return actionServiceFactory.getActionService(iamMappingCreate).invoke(iamMappingCreate);
    }

    public BaseResult createIamProtocol(IamProtocolCreate iamProtocolCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return actionServiceFactory.getActionService(iamProtocolCreate).invoke(iamProtocolCreate);
    }

    public IamPermissionsListResult queryGroupWithProjectPermissionList(IamGroupWithProjectPermissionListGet param)
            throws CommonAdapterException, AdapterUnavailableException {
        return (IamPermissionsListResult)actionServiceFactory.getActionService(param).invoke(param);
    }

    public BaseResult deleteGroupWithProjectPermission(IamGroupWithProjectPermissionDelete param)
            throws CommonAdapterException, AdapterUnavailableException {
        return actionServiceFactory.getActionService(param).invoke(param);
    }

    public BaseResult createIamOpenIdConnect(IamOpenIdConnectCreate param)
            throws CommonAdapterException, AdapterUnavailableException {
        return actionServiceFactory.getActionService(param).invoke(param);
    }

    public BaseResult createIamGroupWithDomainPermission(IamGroupWithDomainPermissionCreate param)
            throws CommonAdapterException, AdapterUnavailableException {
        return actionServiceFactory.getActionService(param).invoke(param);
    }

    public BaseResult createIamGroupAndEntrustWithDomainPermission(IamGroupAndEntrustWithDomainPermissionCreate param)
            throws CommonAdapterException, AdapterUnavailableException {
        return actionServiceFactory.getActionService(param).invoke(param);
    }

    public BaseResult deleteGroupWithDomainPermission(IamGroupWithDomainPermissionDelete param)
            throws CommonAdapterException, AdapterUnavailableException {
        return actionServiceFactory.getActionService(param).invoke(param);
    }

    public BaseResult deleteGroupAndEntrustWithDomainPermission(IamGroupAndEntrustWithDomainPermissionDelete param)
            throws CommonAdapterException, AdapterUnavailableException {
        return actionServiceFactory.getActionService(param).invoke(param);
    }

    public IamPermissionsListResult queryGroupWithDomainPermissionList(IamGroupWithDomainPermissionListGet param)
            throws CommonAdapterException, AdapterUnavailableException {
        return (IamPermissionsListResult) actionServiceFactory.getActionService(param).invoke(param);
    }

    public ActivationCCUserResult activationCCUser(ActivationCCUser activationCCUser)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ActivationCCUserResult) actionServiceFactory.getActionService(activationCCUser)
                                                            .invoke(activationCCUser);
    }

    public DuserResult duser(Duser duser) throws CommonAdapterException, AdapterUnavailableException {
        return (DuserResult) actionServiceFactory.getActionService(duser).invoke(duser);
    }

    public DaccountResult daccount(Daccount daccount) throws CommonAdapterException, AdapterUnavailableException {
        return (DaccountResult) actionServiceFactory.getActionService(daccount).invoke(daccount);
    }

    public BaseResult createModelArtsAuthorizations(IamModelArtsAuthorizationsCreate param)
            throws CommonAdapterException, AdapterUnavailableException {
        return actionServiceFactory.getActionService(param).invoke(param);
    }

    public IamUsersListResult queryUsersList(IamUsersListGet param)
            throws CommonAdapterException, AdapterUnavailableException {
        return (IamUsersListResult)actionServiceFactory.getActionService(param).invoke(param);
    }

    public IamCredentialResult queryCredential(IamCredentialGet param)
            throws CommonAdapterException, AdapterUnavailableException {
        return (IamCredentialResult)actionServiceFactory.getActionService(param).invoke(param);
    }

    public BaseResult createMetadata(IamMetadataCreate param)
            throws CommonAdapterException, AdapterUnavailableException {
        return actionServiceFactory.getActionService(param).invoke(param);
    }

    public BaseResult deleteIdentityProvider(IamIdentityProviderDelete param)
            throws CommonAdapterException, AdapterUnavailableException {
        return actionServiceFactory.getActionService(param).invoke(param);
    }

    public BaseResult deleteUserGroup(IamUserGroupDelete param)
            throws CommonAdapterException, AdapterUnavailableException {
        return actionServiceFactory.getActionService(param).invoke(param);
    }

    public IamSubUserResult createSubUser(IamSubUserCreate param)
            throws CommonAdapterException, AdapterUnavailableException {
        return (IamSubUserResult)actionServiceFactory.getActionService(param).invoke(param);
    }

    public CheckCCPResult HCSOCheckPass(TestPassRequest param)
            throws CommonAdapterException, AdapterUnavailableException {
        return (CheckCCPResult)actionServiceFactory.getActionService(param).invoke(param);
    }

    public BaseResult deleteSubUser(IamSubUserDelete param)
            throws CommonAdapterException, AdapterUnavailableException {
        return actionServiceFactory.getActionService(param).invoke(param);
    }

    public BaseResult updateSubUser(IamSubUserUpdate param)
            throws CommonAdapterException, AdapterUnavailableException {
        return actionServiceFactory.getActionService(param).invoke(param);
    }

    public BaseResult addSubUserToGroup(IamSubUserAddToGroup param)
            throws CommonAdapterException, AdapterUnavailableException {
        return actionServiceFactory.getActionService(param).invoke(param);
    }

    public IamPermanentAccessKeyResult createPermanentAccessKey(IamPermanentAccessKeyCreate param)
            throws CommonAdapterException, AdapterUnavailableException {
        return (IamPermanentAccessKeyResult)actionServiceFactory.getActionService(param).invoke(param);
    }

    public BaseResult deletePermanentAccessKey(IamPermanentAccessKeyDelete param)
            throws CommonAdapterException, AdapterUnavailableException {
        return actionServiceFactory.getActionService(param).invoke(param);
    }

    public BaseResult resPendingNum(ResPools resPools) throws CommonAdapterException, AdapterUnavailableException {
        return actionServiceFactory.getActionService(resPools).invoke(resPools);
    }

    public IamCloudServiceCustomPolicyResult creatCustomPermanent(IamCloudServiceCustomPolicyCreate param)
            throws CommonAdapterException, AdapterUnavailableException {
        return (IamCloudServiceCustomPolicyResult) actionServiceFactory.getActionService(param).invoke(param);
    }

    public BaseResult updateCustomPermanent(IamCloudServiceCustomPolicyUpdate param)
            throws CommonAdapterException, AdapterUnavailableException {
        return actionServiceFactory.getActionService(param).invoke(param);
    }

    public IamCloudServiceCustomPolicyResult checkCustomPermanent(IamCloudServiceCustomPolicyCheck param)
            throws CommonAdapterException, AdapterUnavailableException {
        return (IamCloudServiceCustomPolicyResult) actionServiceFactory.getActionService(param).invoke(param);
    }

    public BaseResult deleteCustomPermission(IamCloudServiceCustomPolicyDelete param)
            throws CommonAdapterException, AdapterUnavailableException {
        return actionServiceFactory.getActionService(param).invoke(param);
    }

    public IamCustomPolicyQueryInfoResult queryCustomPolicyInfo(IamCustomPolicyQueryInfo param)
            throws CommonAdapterException, AdapterUnavailableException {
        return (IamCustomPolicyQueryInfoResult) actionServiceFactory.getActionService(param).invoke(param);
    }
    public SharePredeployQueryResult queryShareFromFd(SharePredeployQuery param)
            throws CommonAdapterException, AdapterUnavailableException {
        return (SharePredeployQueryResult) actionServiceFactory.getActionService(param).invoke(param);
    }

    public MountPreShareResult FDShareMount(MountPreResShare param)
            throws CommonAdapterException, AdapterUnavailableException {
        return (MountPreShareResult) actionServiceFactory.getActionService(param).invoke(param);
    }


    public CCSPIntegralityHashResult checkPIntegralityHash(CCSPIntegralityHash param)
            throws CommonAdapterException, AdapterUnavailableException {
        return (CCSPIntegralityHashResult) actionServiceFactory.getActionService(param).invoke(param);
    }

    public BaseResult maSharePoolsStopJob(MASharePoolsStopJob param)
            throws CommonAdapterException, AdapterUnavailableException {
        return actionServiceFactory.getActionService(param).invoke(param);
    }

    public IamAgencyDeleteResult deleteIamAgency(IamAgencyDelete param)
        throws CommonAdapterException, AdapterUnavailableException {
        return (IamAgencyDeleteResult)actionServiceFactory.getActionService(param).invoke(param);
    }

    public IamCustomPolicyDeleteResult deleteIamCustomPolicy(IamCustomPolicyDelete param)
                throws CommonAdapterException, AdapterUnavailableException {
            return (IamCustomPolicyDeleteResult)actionServiceFactory.getActionService(param).invoke(param);
    }

    public IamCustomPolicyListResult listIamCustomPolicy(IamCustomPolicyListGet param)
        throws CommonAdapterException, AdapterUnavailableException {
        return (IamCustomPolicyListResult)actionServiceFactory.getActionService(param).invoke(param);
    }

    public MABmsJobResult maBmsStarJob(MABmsStarJob param)
            throws CommonAdapterException, AdapterUnavailableException {
        return (MABmsJobResult)actionServiceFactory.getActionService(param).invoke(param);
    }

    public MABmsJobResult maBmsStopJob(MABmsStopJob param)
            throws CommonAdapterException, AdapterUnavailableException {
        return (MABmsJobResult)actionServiceFactory.getActionService(param).invoke(param);
    }

    public MABmsDelResult maBmsDelJob(MABmsDelJob param)
            throws CommonAdapterException, AdapterUnavailableException {
        return (MABmsDelResult)actionServiceFactory.getActionService(param).invoke(param);
    }

    public BaseResult keystoneListAllProjectPermissionsForGroupSolution(IamKeystoneListAllProjectPermissionsForGroupSolution param)
            throws CommonAdapterException, AdapterUnavailableException {
        return actionServiceFactory.getActionService(param).invoke(param);
    }

    public BaseResult deleteDomainGroupInheritedRoleSolutionPermission(IamDeleteDomainGroupInheritedRoleSolution param)
            throws CommonAdapterException, AdapterUnavailableException {
        return actionServiceFactory.getActionService(param).invoke(param);
    }

    public BaseResult updateDomainGroupInheritRolePermission(IamUpdateDomainGroupInheritRole param)
            throws CommonAdapterException, AdapterUnavailableException {
        return actionServiceFactory.getActionService(param).invoke(param);
    }

}
