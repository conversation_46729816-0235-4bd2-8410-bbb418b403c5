/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.adapter.facade.message;

import cn.com.cloudstar.rightcloud.adapter.core.MQException;
import cn.com.cloudstar.rightcloud.adapter.core.MQHelper;
import cn.com.cloudstar.rightcloud.adapter.facade.common.Constants;
import cn.com.cloudstar.rightcloud.adapter.facade.common.Constants.AdapterUnvailableException;
import cn.com.cloudstar.rightcloud.adapter.facade.handler.AdminHandler;
import cn.com.cloudstar.rightcloud.adapter.facade.handler.AiHubHandler;
import cn.com.cloudstar.rightcloud.adapter.facade.handler.AuthHandler;
import cn.com.cloudstar.rightcloud.adapter.facade.handler.BmsHandler;
import cn.com.cloudstar.rightcloud.adapter.facade.handler.DcsHandler;
import cn.com.cloudstar.rightcloud.adapter.facade.handler.HCSOTimeHandler;
import cn.com.cloudstar.rightcloud.adapter.facade.handler.HpcHandler;
import cn.com.cloudstar.rightcloud.adapter.facade.handler.MirrorHandler;
import cn.com.cloudstar.rightcloud.adapter.facade.handler.MonitorHandler;
import cn.com.cloudstar.rightcloud.adapter.facade.handler.NetHandler;
import cn.com.cloudstar.rightcloud.adapter.facade.handler.ObsHandler;
import cn.com.cloudstar.rightcloud.adapter.facade.handler.RdsHandler;
import cn.com.cloudstar.rightcloud.adapter.facade.handler.ScanHandler;
import cn.com.cloudstar.rightcloud.adapter.facade.handler.VmHandler;
import cn.com.cloudstar.rightcloud.adapter.facade.util.BaseUtil;
import cn.com.cloudstar.rightcloud.adapter.facade.util.BeanUtil;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.QuotaConfig;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.RoleList;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.SgCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.SgDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.UserEdit;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.UserPasswordModify;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.UserPasswordValid;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.UserRoleAdd;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.UserRoleDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.UserRoleList;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.QuotaConfigResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.RoleListResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.SgUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.UserEditResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.UserPasswordModifyResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.UserPasswordValidResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.UserRoleAddResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.UserRoleDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.UserRoleListResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiAlgorithmsQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiModelsQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopCreateTo;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopDeleteTo;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopDetailsQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopLicenseCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopLifecycleUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopMineQueryAll;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopProcess;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopQueryAll;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopRenew;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopSubscribesQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopSubscriptionsCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopSubscriptionsDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopSubscriptionsRenew;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopUpdateTo;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopVersionCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopVersionCreateTo;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopVersionDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopVersionUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopVersionUpdateTo;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopVersionsQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiAlgorithmsQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiModelsQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiShopCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiShopCreateToResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiShopDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiShopDeleteToResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiShopDetailsQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiShopLicenseCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiShopMineQueryAllResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiShopQueryAllResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiShopSubscribesQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiShopVersionCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiShopVersionCreateResultTo;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiShopVersionUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiShopVersionUpdateResultTo;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiShopVersionsQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.azure.ResourceGroupCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.azure.result.ResourceGroupCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.bms.request.BareMetalList;
import cn.com.cloudstar.rightcloud.adapter.pojo.bms.result.ListBareMetalResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.cloudos.ScanCloudOsBareMetalNode;
import cn.com.cloudstar.rightcloud.adapter.pojo.cloudos.ScanCloudOsBareMetalZone;
import cn.com.cloudstar.rightcloud.adapter.pojo.cloudos.ScanCloudOsBaremetalByEnv;
import cn.com.cloudstar.rightcloud.adapter.pojo.cloudos.ScanCloudOsOwnerByEnv;
import cn.com.cloudstar.rightcloud.adapter.pojo.cloudos.ScanCloudOsZoneNetwork;
import cn.com.cloudstar.rightcloud.adapter.pojo.cloudos.ScanFlowFeatureGroup;
import cn.com.cloudstar.rightcloud.adapter.pojo.cloudos.result.BaremetalNodeResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.cloudos.result.BaremetalZoneResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.cloudos.result.CloudOsBaremetalResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.cloudos.result.CloudOsOwnerResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.cloudos.result.FlowFeatureGroupScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.cloudos.result.ZoneNetworkResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.FirewallCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.FirewallDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.FirewallUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.FwRuleCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.FwRuleUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.FwStrategyCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.FwStrategyDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.FwStrategyRuleAdd;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.FwStrategyRuleMove;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.FwStrategyRuleRemove;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.FwStrategyUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.result.FirewallCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.result.FirewallDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.result.FirewallUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.result.FwRuleCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.result.FwRuleUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.result.FwStrategyCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.result.FwStrategyDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.result.FwStrategyRuleAddResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.result.FwStrategyRuleMoveResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.result.FwStrategyRuleRemoveResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.result.FwStrategyUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.host.HostAggregateCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.host.HostAggregateDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.host.HostAggregateServerManage;
import cn.com.cloudstar.rightcloud.adapter.pojo.host.HostAggregateUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.host.result.HostAggregateCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.host.result.HostAggregateDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.host.result.HostAggregateServerManageResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.host.result.HostAggregateUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.FDHPCClusterActive;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.GrantJobTemplate;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.HPCClusterCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.HPCClusterDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.HPCClusterInfoID;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.HPCExpansionNode;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.HPCNodeJobInfo;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.HPCNodeOperate;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.HPCRemoveNode;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.HPCShareServiceCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.HPCShareServiceDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.HPCShareStopJob;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.HPCTenantStatusSyncRequest;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.HPCUnsubscribeReconciliation;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.TaskInfoScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.FDHPCClusterActiveResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.FDTaskInfoResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.GrantJobTemplateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.HPCClusterCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.HPCClusterDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.HPCClusterInfoIDResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.HPCExpansionNodeResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.HPCNodeJobInfoResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.HPCNodeOperateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.HPCRemoveNodeResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.HPCShareServiceDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.HPCShareServiceResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.HPCShareStopJobResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.HPCUnsubscribeReconciliationResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.keypairs.KeypairCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.keypairs.KeypairDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.keypairs.KeypairGet;
import cn.com.cloudstar.rightcloud.adapter.pojo.keypairs.KeypairListGet;
import cn.com.cloudstar.rightcloud.adapter.pojo.keypairs.result.KeypairCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.keypairs.result.KeypairDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.keypairs.result.KeypairGetResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.keypairs.result.KeypairListGetResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.BackendServerAdd;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.BackendServerAddResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.BackendServerRemove;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.BackendServerRemoveResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.BackendServerSet;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.BackendServerSetResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.DecribeCertificates;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.DeleteLb;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.DeleteLbResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.DescribeZone;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.DescribeZoneResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.LbCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.LbListenerCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.LbListnerCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.LbRuleCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.LbRuleCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.LbRuleDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.LbRuleDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.LbRuleUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.LbRuleUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.ListenerDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.ListenerDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.LoadBalanceCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.LoadBalanceName;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.LoadBalanceNameResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.LoadBalanceStatus;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.LoadBalanceStatusResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.MasterSlaveServerGroupCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.MasterSlaveServerGroupCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.MasterSlaveServerGroupDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.MasterSlaveServerGroupDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.ServerCertificatesResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.StartOrStopListener;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.StartOrStopListenerResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.VServerGroupAttributeSet;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.VServerGroupAttributeSetResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.VServerGroupBackendServersAdd;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.VServerGroupBackendServersAddResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.VServerGroupBackendServersRemove;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.VServerGroupBackendServersRemoveResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.VServerGroupCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.VServerGroupCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.VServerGroupDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.VServerGroupDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.VServerGroupUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.maas.MachineExtendInfo;
import cn.com.cloudstar.rightcloud.adapter.pojo.maas.result.MachineExtendInfoResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.mirror.CreateNamespace;
import cn.com.cloudstar.rightcloud.adapter.pojo.mirror.CreateTempLoginCode;
import cn.com.cloudstar.rightcloud.adapter.pojo.mirror.ImageVersionDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.mirror.ListNamespace;
import cn.com.cloudstar.rightcloud.adapter.pojo.mirror.MirrorCenterDetail;
import cn.com.cloudstar.rightcloud.adapter.pojo.mirror.MirrorCenterList;
import cn.com.cloudstar.rightcloud.adapter.pojo.mirror.MirrorCenterUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.mirror.MirrorVersionList;
import cn.com.cloudstar.rightcloud.adapter.pojo.mirror.result.CreateTempLoginCodeResponse;
import cn.com.cloudstar.rightcloud.adapter.pojo.mirror.result.ImageVersionDeleteResponse;
import cn.com.cloudstar.rightcloud.adapter.pojo.mirror.result.ImageVersionListResponse;
import cn.com.cloudstar.rightcloud.adapter.pojo.mirror.result.MirrorCenterDetailResponse;
import cn.com.cloudstar.rightcloud.adapter.pojo.mirror.result.MirrorCenterListResponse;
import cn.com.cloudstar.rightcloud.adapter.pojo.mirror.result.MirrorCenterUpdateResponse;
import cn.com.cloudstar.rightcloud.adapter.pojo.mirror.result.MirrorCreateNamespaceResponse;
import cn.com.cloudstar.rightcloud.adapter.pojo.mirror.result.MirrorListNamespaceResponse;
import cn.com.cloudstar.rightcloud.adapter.pojo.monitor.MetricMonitor;
import cn.com.cloudstar.rightcloud.adapter.pojo.monitor.result.MetricMonitorResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.AvilableNetwork;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.FireWallPolicyInsertRule;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.FireWallPolicyUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.FloatingIpAttach;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.FloatingIpDetach;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.FloatingIpLineQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.RouteTableUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.RouterAddExternalGateway;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.RouterAddInterface;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.RouterDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.RouterDeleteEntry;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.RouterRemoveExternalGateway;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.RouterRemoveInterface;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.RouterUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.RouterUpdateEntry;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.SecurityGroupQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.ServerSecurityGroupAdd;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.ServerSecurityGroupDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.SgRuleCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.SgRuleDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.SgRuleListQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.SgRuleUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.VpcFireWallObjectGroupsCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.VpcFireWallObjectGroupsDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.VpcFireWallObjectGroupsUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.VpcFireWallObjectsCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.VpcFireWallObjectsDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.VpcFireWallObjectsUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.VpcPeeringAccept;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.VpcPeeringDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.AvilableNetworkResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.FireWallPolicyInsertRuleResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.FireWallPolicyUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.FloatingIpAttachResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.FloatingIpDetachResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.FloatingIpLineQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.RouteTableUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.RouterResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.SecurityGroupQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.ServerSecurityGroupAddResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.ServerSecurityGroupDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.SgCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.SgDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.SgRuleCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.SgRuleDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.SgRuleListQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.SgUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.VpcFireWallObjectGroupsResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.VpcFireWallObjectsResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.VpcPeeringAcceptResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.VpcPeeringDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.BucketCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.BucketDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.BucketPolicyUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.BucketRepectCheck;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.BucketUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.ForObjUpload;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.ObjectCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.ObjectDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.ObjectDownload;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.ObjectRestore;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.ObjectStorageCopy;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.ObjectUpload;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.result.BucketCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.result.BucketDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.result.BucketPolicyResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.result.BucketRepeatCheckResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.result.BucketUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.result.ForObjUploadResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.result.ObjectCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.result.ObjectDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.result.ObjectDownloadResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.result.ObjectRestoreResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.result.ObjectStorageCopyResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.obs.result.ObjectUploadResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.other.RegisterEnv;
import cn.com.cloudstar.rightcloud.adapter.pojo.other.UnRegisterEnv;
import cn.com.cloudstar.rightcloud.adapter.pojo.other.result.RegisterEnvResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.other.result.UnRegisterEnvResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.price.HuaweiPriceCalculate;
import cn.com.cloudstar.rightcloud.adapter.pojo.price.PriceCalculate;
import cn.com.cloudstar.rightcloud.adapter.pojo.price.PricePayAsYouGoCalculate;
import cn.com.cloudstar.rightcloud.adapter.pojo.price.PriceSubscriptionCalculate;
import cn.com.cloudstar.rightcloud.adapter.pojo.price.QueryBalance;
import cn.com.cloudstar.rightcloud.adapter.pojo.price.RdsPriceCalculate;
import cn.com.cloudstar.rightcloud.adapter.pojo.price.result.PriceCalculateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.price.result.QueryBalanceResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.price.result.RdsPriceCalculateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.AccountCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.AccountDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.AccountUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.ConnectStringRelease;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.ConnectStringUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.DBEngineQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.DBInstanceClassQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.DBInstanceDatabaseCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.DBInstanceDatabaseDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.DBInstanceDatabasePrivilege;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.DBInstanceDescriptionModify;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.DBInstanceEngineQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.DBInstanceZoneQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.IpArrayUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.AccountResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.ConnectStringReleaseResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.ConnectStringUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.DBEngineQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.DBInstanceClassQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.DBInstanceDatabaseCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.DBInstanceDatabaseDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.DBInstanceDatabasePrivilegeResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.DBInstanceEngineQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.DBInstanceResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.DBInstanceZoneQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.rds.result.IpArrayResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.region.RegionCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.region.RegionDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.region.RegionUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.region.result.RegionCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.region.result.RegionDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.region.result.RegionUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.requesttime.HCSOTimeGet;
import cn.com.cloudstar.rightcloud.adapter.pojo.requesttime.result.HCSOTimeGetResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.res.ResPools;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.AliWholeZoneScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.AllInOneScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.BucketNameScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.BucketObjectScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.BucketPolicyScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.BucketScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ClusterScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.DBInstanceAccountScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.DBInstanceIpArrayScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.DBInstanceScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.DcsAvailableZoneScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.DcsConfigScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.DcsProductScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.DcsScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.GpuDeviceScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.GpuGroupScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.HPCClusterScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.HostAggregateScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.HostRelateInstScanByEnv;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.HostScanByEnv;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.IamUserScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.IamUserScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.KafkaTopicScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.LbListenerScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.LbPoolsScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.LoadBalanceScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.MqInstanceScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.NetworkExportScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.NetworkScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.PhycialMappingScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.PortScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ProjectScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.RegionScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ResourceGroupScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ResourceScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.RoleScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.RouterInterfaceScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.RouterRouteScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.RouterScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ScFirewallRuleScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ScFirewallScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ScFirewallStrategyScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ScanFirewallObjectGroups;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ScanFloatingIpsByEnv;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ScanInstanceGaapCostByEnv;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ScanShareGroupByEnv;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ScanShareGroupRuleByEnv;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ScanShareTypeByEnv;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ScanSharesByEnv;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ScanSharesRightsGroupByEnv;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ScanSharesZonesByEnv;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ScanTagsByEnv;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ScanVdBackupsByEnv;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ScanVdsByEnv;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ScanVmTypesByEnv;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ScanVmsByEnv;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.SecurityGroupScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ServerGroupScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ServiceChainScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.SnapshotScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.SshKeyScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.StorageScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.StorageTypeScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.SubnetScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.TemplateScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.User2ProjectScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.UserBusinessBehaviorScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.VPCFirewallScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.VmScanAlone;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.VpcPeeringScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ZoneScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.AliWholeZoneScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.AllInOneScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.BucketNameScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.BucketObjectScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.BucketPolicyScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.BucketScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ClusterScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.DBInstanceAccountScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.DBInstanceIpArrayScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.DBInstanceScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.DcsAvailableZoneScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.DcsConfigScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.DcsProductScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.DcsScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.DiskScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.GpuDeviceScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.GpuGroupScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.HPCClusterScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.HostAggregateScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.HostRelateInstScanByEnvResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.HostScanByEnvResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.KafkaTopicScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.LbListenerScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.LbPoolsScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.LoadBalanceScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.MqInstanceScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.NetworkExportScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.NetworkScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.PhycialMappingScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.PortScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ProjectScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.RegionScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ResourceGroupScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ResourceScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.RoleScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.RouterInterfaceScanByEnvResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.RouterRouteScanByEnvResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.RouterScanByEnvResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ScFirewallRuleScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ScFirewallScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ScFirewallStrategyScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ScanFirewallObjectGroupsResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ScanFloatingIpByEnvResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ScanInstanceGaapCostResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ScanShareGroupByEnvResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ScanShareGroupRuleByEnvResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ScanShareTypeByEnvResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ScanSharesByEnvResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ScanSharesRightsGroupByEnvResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ScanSharesZonesByEnvResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ScanTagsByEnvResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ScanVmTypesByEnvResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ScanVmsByEnvResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.SecurityGroupScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ServerGroupScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ServiceChainScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.SnapshotScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.SshKeyScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.StorageScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.StorageTypeScanByEnvResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.TemplateScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.User2ProjectScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.UserBusinessBehaviorScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.VPCFirewallScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.VmScanAloneResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.VpcPeeringScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ZoneScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.QuotaSetCreat;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.ResShareRuleCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.QuotaSetCreatResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.ResShareRuleCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.ShareActionResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.ShareRuleDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.ShareUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.ShareAction;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.ShareRuleDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.ShareUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.tenant.AddUserToTenant;
import cn.com.cloudstar.rightcloud.adapter.pojo.tenant.RemoveUserFromTenant;
import cn.com.cloudstar.rightcloud.adapter.pojo.tenant.TenantCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.tenant.TenantDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.tenant.TenantEdit;
import cn.com.cloudstar.rightcloud.adapter.pojo.tenant.TenantListGet;
import cn.com.cloudstar.rightcloud.adapter.pojo.tenant.TenantUserConfig;
import cn.com.cloudstar.rightcloud.adapter.pojo.tenant.result.AddUserToTenantResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.tenant.result.RemoveUserFromTenantResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.tenant.result.TenantCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.tenant.result.TenantDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.tenant.result.TenantEditResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.tenant.result.TenantListGetResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.tenant.result.TenantUserConfigResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.ActivationCCUser;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.CloudOsAddTenants;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.Daccount;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.DeleteHPCAccount;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.Duser;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.HasAdminPermission;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.UserCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.UserDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.UserListGet;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.UserModify;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.result.ActivationCCUserResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.result.CloudOsAddTenantsResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.result.DaccountResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.result.DeleteHPCAccountResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.result.DuserResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.result.HasAdminPermissionResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.result.UserCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.result.UserDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.result.UserListGetResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.result.UserModifyResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.MarkVmAsTemplate;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.RdsRenewInstance;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.SecurityGroupConfig;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.ServerGroupCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.ServerGroupRemove;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmInquiryPrice;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmQueryDataStore;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmQuerySgs;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmRename;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmRenewInstance;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmTypeChangeQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmVncConsole;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.SecurityGroupConfigResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.ServerGroupCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.ServerGroupRemoveResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmInquiryPriceResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmQueryDataStoreResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmQuerySgsResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmRenameResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmRenewInstanceResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmTemplateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmTypeChangeQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmVncConsoleResult;
import cn.com.cloudstar.rightcloud.driver.core.exception.AdapterUnavailableException;
import cn.com.cloudstar.rightcloud.driver.core.exception.CommonAdapterException;
import cn.com.cloudstar.rightcloud.driver.pojo.base.BaseResult;
import cn.com.cloudstar.rightcloud.infra.cloud.client.core.exception.CloudApiException;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Throwables;
import com.huawei.openstack4j.api.exceptions.ClientResponseException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * The type Sync listener.
 */
@Service
@Slf4j
public class SyncListener {

    @Autowired
    private VmHandler vmHandler;

    @Autowired
    private AiHubHandler aiHubHandler;

    @Autowired
    private AdminHandler adminHandler;

    @Autowired
    private ScanHandler scanHandler;

    @Autowired
    private NetHandler netHandler;

    @Autowired
    private RdsHandler rdsHandler;

    @Autowired
    private MonitorHandler monitorHandler;

    @Autowired
    private ObsHandler obsHandler;
    @Autowired
    AuthHandler authHandler;

    @Autowired
    private BmsHandler bmsHandler;

    /**
     * 处理高速缓存操作
     **/
    @Autowired
    private DcsHandler dcsHandler;

    @Autowired
    private HCSOTimeHandler hcsoTimeHandler;

    @Autowired
    private HpcHandler hpcHandler;

    @Autowired
    private MirrorHandler mirrorHandler;

    /**
     * Handle message vm rename result.
     *
     * @param vmRename the vm rename
     *
     * @return the vm rename result
     */
    public VmRenameResult handleMessage(VmRename vmRename) {

        log.info("receiving message for renaming vm, virtual type : [{}]", vmRename.getVirtEnvType());

        log.info("msg id : [{}]", vmRename.getMsgId());

        VmRenameResult vmRenameResult = new VmRenameResult();

        try {
            vmRenameResult = vmHandler.renameVm(vmRename);
            log.info("adaptor vm : [{}] has been renamed successfully",vmRename.getName());
        } catch (CommonAdapterException e) {

            vmRenameResult.setSuccess(false);
            vmRenameResult.setErrCode(e.getErrCode());
            vmRenameResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            vmRenameResult.setSuccess(false);
            vmRenameResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmRenameResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            vmRenameResult.setSuccess(false);
            vmRenameResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmRenameResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(vmRename, vmRenameResult);
        vmRenameResult.setMsgId(vmRename.getMsgId());
        vmRenameResult.setName(vmRename.getName());
        vmRenameResult.setNameTobe(vmRename.getNameTobe());
        vmRenameResult.setId(vmRename.getId());
        return vmRenameResult;

    }

    /**
     * Handle message azure resourceGroup create.
     *
     * @param resourceGroupCreate
     */
    public ResourceGroupCreateResult handleMessage(ResourceGroupCreate resourceGroupCreate) {

        log.info("receiving message for authCloud, virtual type : [{}]", resourceGroupCreate.getVirtEnvType());

        log.info("msg id : [{}]", resourceGroupCreate.getMsgId());

        ResourceGroupCreateResult resourceGroupCreateResult = new ResourceGroupCreateResult();

        try {
            resourceGroupCreateResult = vmHandler.createResourceGroup(resourceGroupCreate);
            log.info("adaptor authCloud :[{}] has been authentication successfully",resourceGroupCreate.getVirtEnvType());
        } catch (CommonAdapterException e) {

            resourceGroupCreateResult.setSuccess(false);
            resourceGroupCreateResult.setErrCode(e.getErrCode());
            resourceGroupCreateResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            resourceGroupCreateResult.setSuccess(false);
            resourceGroupCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            resourceGroupCreateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            resourceGroupCreateResult.setSuccess(false);
            resourceGroupCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            resourceGroupCreateResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(resourceGroupCreate, resourceGroupCreateResult);

        return resourceGroupCreateResult;

    }

    public HPCClusterCreateResult handleMessage(HPCClusterCreate hpcClusterCreate) {

        log.info("receiving message for authCloud, virtual type : [{}]", hpcClusterCreate.getVirtEnvType());

        log.info("msg id : [{}]", hpcClusterCreate.getMsgId());

        HPCClusterCreateResult hpcClusterCreateResult = new HPCClusterCreateResult();

        try {
            hpcClusterCreateResult = vmHandler.createHPCCluster(hpcClusterCreate);
            log.info("adaptor authCloud :[{}] has been authentication successfully", hpcClusterCreate.getVirtEnvType());
        } catch (CommonAdapterException e) {

            hpcClusterCreateResult.setSuccess(false);
            hpcClusterCreateResult.setErrCode(e.getErrCode());
            hpcClusterCreateResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            hpcClusterCreateResult.setSuccess(false);
            hpcClusterCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            hpcClusterCreateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            hpcClusterCreateResult.setSuccess(false);
            hpcClusterCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            hpcClusterCreateResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(hpcClusterCreate, hpcClusterCreateResult);

        return hpcClusterCreateResult;
    }


    public HPCExpansionNodeResult handleMessage(HPCExpansionNode hpcCloudExpansionNode) {

        log.debug("receiving message for authCloud, virtual type : [{}]", hpcCloudExpansionNode.getVirtEnvType());

        log.debug("msg id : [{}]", hpcCloudExpansionNode.getMsgId());

        HPCExpansionNodeResult hpcCloudExpansionNodeResult = new HPCExpansionNodeResult();

        try {
            log.debug("HPC扩容请求参数hpcCloudExpansionNode：{}", JSON.toJSONString(hpcCloudExpansionNode));
            hpcCloudExpansionNodeResult = vmHandler.expansionNode(hpcCloudExpansionNode);
            log.debug("HPC扩容响应hpcCloudExpansionNodeResult：{}", JSON.toJSONString(hpcCloudExpansionNodeResult));
            log.debug("adaptor authCloud :[{}] has been authentication successfully",
                     hpcCloudExpansionNode.getVirtEnvType());
        } catch (CommonAdapterException e) {

            hpcCloudExpansionNodeResult.setSuccess(false);
            hpcCloudExpansionNodeResult.setErrCode(e.getErrCode());
            hpcCloudExpansionNodeResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            hpcCloudExpansionNodeResult.setSuccess(false);
            hpcCloudExpansionNodeResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            hpcCloudExpansionNodeResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            hpcCloudExpansionNodeResult.setSuccess(false);
            hpcCloudExpansionNodeResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            hpcCloudExpansionNodeResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(hpcCloudExpansionNode, hpcCloudExpansionNodeResult);

        return hpcCloudExpansionNodeResult;
    }

    public HPCNodeOperateResult handleMessage(HPCNodeOperate hpcNodeOperation) {

        log.debug("receiving message for authCloud, virtual type : [{}]", hpcNodeOperation.getVirtEnvType());

        log.debug("msg id : [{}]", hpcNodeOperation.getMsgId());

        HPCNodeOperateResult hpCNodeOperationResult = new HPCNodeOperateResult();

        try {
            log.debug("HPC变更请求参数hpcNodeOperation：{}", JSON.toJSONString(hpcNodeOperation));
            hpCNodeOperationResult = vmHandler.expansionNode(hpcNodeOperation);
            log.debug("HPC变更响应参数hpCNodeOperationResult：{}", JSON.toJSONString(hpCNodeOperationResult));
            log.debug("adaptor authCloud :[{}] has been authentication successfully",
                     hpcNodeOperation.getVirtEnvType());
        } catch (CommonAdapterException e) {

            hpCNodeOperationResult.setSuccess(false);
            hpCNodeOperationResult.setErrCode(e.getErrCode());
            hpCNodeOperationResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            hpCNodeOperationResult.setSuccess(false);
            hpCNodeOperationResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            hpCNodeOperationResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            hpCNodeOperationResult.setSuccess(false);
            hpCNodeOperationResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            hpCNodeOperationResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(hpcNodeOperation, hpCNodeOperationResult);

        return hpCNodeOperationResult;
    }

    /**
     * 删除节点接口
     * @param hpcRemoveNode
     * @return
     */
    public HPCRemoveNodeResult handleMessage(HPCRemoveNode hpcRemoveNode) {

        log.debug("receiving message for authCloud, virtual type : [{}]", hpcRemoveNode.getVirtEnvType());

        log.debug("msg id : [{}]", hpcRemoveNode.getMsgId());

        HPCRemoveNodeResult hpcRemoveNodeResult = new HPCRemoveNodeResult();

        try {
            log.debug("HPC节点删除请求参数hpcRemoveNode：{}", JSON.toJSONString(hpcRemoveNode));
            hpcRemoveNodeResult = vmHandler.removeNode(hpcRemoveNode);
            log.debug("HPC节点删除响应参数hpcRemoveNodeResult：{}", JSON.toJSONString(hpcRemoveNodeResult));
            log.debug("adaptor authCloud :[{}] has been authentication successfully",
                    hpcRemoveNode.getVirtEnvType());
        } catch (CommonAdapterException e) {

            hpcRemoveNodeResult.setSuccess(false);
            hpcRemoveNodeResult.setErrCode(e.getErrCode());
            hpcRemoveNodeResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            hpcRemoveNodeResult.setSuccess(false);
            hpcRemoveNodeResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            hpcRemoveNodeResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            hpcRemoveNodeResult.setSuccess(false);
            hpcRemoveNodeResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            hpcRemoveNodeResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(hpcRemoveNode, hpcRemoveNodeResult);

        return hpcRemoveNodeResult;
    }

    public HPCNodeJobInfoResult handleMessage(HPCNodeJobInfo hpcNodeJobInfo) {

        log.debug("receiving message for authCloud, virtual type : [{}]", hpcNodeJobInfo.getVirtEnvType());

        log.debug("msg id : [{}]", hpcNodeJobInfo.getMsgId());

        HPCNodeJobInfoResult hpcNodeJobInfoResult = new HPCNodeJobInfoResult();

        try {
            log.debug("HPC节点信息请求参数hpcNodeJobInfo：{}", JSON.toJSONString(hpcNodeJobInfo));
            hpcNodeJobInfoResult = vmHandler.expansionNode(hpcNodeJobInfo);
            log.debug("HPC节点信息响应参数hpcNodeJobInfoResult：{}", JSON.toJSONString(hpcNodeJobInfoResult));
            log.debug("adaptor authCloud :[{}] has been authentication successfully",
                     hpcNodeJobInfo.getVirtEnvType());
        } catch (CommonAdapterException e) {

            hpcNodeJobInfoResult.setSuccess(false);
            hpcNodeJobInfoResult.setErrCode(e.getErrCode());
            hpcNodeJobInfoResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            hpcNodeJobInfoResult.setSuccess(false);
            hpcNodeJobInfoResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            hpcNodeJobInfoResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            hpcNodeJobInfoResult.setSuccess(false);
            hpcNodeJobInfoResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            hpcNodeJobInfoResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(hpcNodeJobInfo, hpcNodeJobInfoResult);

        return hpcNodeJobInfoResult;
    }


    public HPCUnsubscribeReconciliationResult handleMessage(HPCUnsubscribeReconciliation hpcUnsubscribeReconciliation) {

        log.debug("receiving message for authCloud, virtual type : [{}]", hpcUnsubscribeReconciliation.getVirtEnvType());

        log.debug("msg id : [{}]", hpcUnsubscribeReconciliation.getMsgId());

        HPCUnsubscribeReconciliationResult hpcUnsubscribeReconciliationResult = new HPCUnsubscribeReconciliationResult();

        try {
            log.debug("HPC退订对账请求参数hpcUnsubscribeReconciliation：{}", JSON.toJSONString(hpcUnsubscribeReconciliation));
            hpcUnsubscribeReconciliationResult = vmHandler.expansionNode(hpcUnsubscribeReconciliation);
            log.debug("HPC退订对账详情响应hpcUnsubscribeReconciliationResult：{}", JSON.toJSONString(hpcUnsubscribeReconciliationResult));
            log.debug("adaptor authCloud :[{}] has been authentication successfully",
                     hpcUnsubscribeReconciliation.getVirtEnvType());
        } catch (CommonAdapterException e) {

            hpcUnsubscribeReconciliationResult.setSuccess(false);
            hpcUnsubscribeReconciliationResult.setErrCode(e.getErrCode());
            hpcUnsubscribeReconciliationResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            hpcUnsubscribeReconciliationResult.setSuccess(false);
            hpcUnsubscribeReconciliationResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            hpcUnsubscribeReconciliationResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            hpcUnsubscribeReconciliationResult.setSuccess(false);
            hpcUnsubscribeReconciliationResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            hpcUnsubscribeReconciliationResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(hpcUnsubscribeReconciliation, hpcUnsubscribeReconciliationResult);

        return hpcUnsubscribeReconciliationResult;
    }

    public HPCClusterScanResult handleMessage(HPCClusterScan hpcClusterScan) {

        log.info("receiving message for authCloud, virtual type : [{}]", hpcClusterScan.getVirtEnvType());

        log.info("msg id : [{}]", hpcClusterScan.getMsgId());

        HPCClusterScanResult hpcClusterScanResult = new HPCClusterScanResult();

        try {
            hpcClusterScanResult = vmHandler.createHPCClusterScan(hpcClusterScan);
            log.info("adaptor authCloud :[{}] has been authentication successfully", hpcClusterScan.getVirtEnvType());
        } catch (CommonAdapterException e) {

            hpcClusterScanResult.setSuccess(false);
            hpcClusterScanResult.setErrCode(e.getErrCode());
            hpcClusterScanResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            hpcClusterScanResult.setSuccess(false);
            hpcClusterScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            hpcClusterScanResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            hpcClusterScanResult.setSuccess(false);
            hpcClusterScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            hpcClusterScanResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(hpcClusterScan, hpcClusterScanResult);

        return hpcClusterScanResult;
    }



    public HPCClusterDeleteResult handleMessage(HPCClusterDelete hpcClusterDelete) {

        log.info("receiving message for authCloud, virtual type : [{}]", hpcClusterDelete.getVirtEnvType());

        log.info("msg id : [{}]", hpcClusterDelete.getMsgId());

        HPCClusterDeleteResult hpcClusterDeleteResult = new HPCClusterDeleteResult();

        try {
            hpcClusterDeleteResult = vmHandler.releaseHPCCluster(hpcClusterDelete);
            log.info("adaptor authCloud :[{}] has been authentication successfully", hpcClusterDelete.getVirtEnvType());
        } catch (CommonAdapterException e) {

            hpcClusterDeleteResult.setSuccess(false);
            hpcClusterDeleteResult.setErrCode(e.getErrCode());
            hpcClusterDeleteResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            hpcClusterDeleteResult.setSuccess(false);
            hpcClusterDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            hpcClusterDeleteResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            hpcClusterDeleteResult.setSuccess(false);
            hpcClusterDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            hpcClusterDeleteResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(hpcClusterDelete, hpcClusterDeleteResult);

        return hpcClusterDeleteResult;
    }

    public HPCShareServiceDeleteResult handleMessage(HPCShareServiceDelete hpcShareServiceDelete) {

        log.info("receiving message for authCloud, virtual type : [{}]", hpcShareServiceDelete.getVirtEnvType());

        log.info("msg id : [{}]", hpcShareServiceDelete.getMsgId());

        HPCShareServiceDeleteResult hpcShareServiceDeleteResult = new HPCShareServiceDeleteResult();

        try {
            hpcShareServiceDeleteResult = vmHandler.releaseHPCShareCluster(hpcShareServiceDelete);
            log.info("adaptor authCloud :[{}] has been authentication successfully",
                     hpcShareServiceDelete.getVirtEnvType());
        } catch (CommonAdapterException e) {

            hpcShareServiceDeleteResult.setSuccess(false);
            hpcShareServiceDeleteResult.setErrCode(e.getErrCode());
            hpcShareServiceDeleteResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            hpcShareServiceDeleteResult.setSuccess(false);
            hpcShareServiceDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            hpcShareServiceDeleteResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            hpcShareServiceDeleteResult.setSuccess(false);
            hpcShareServiceDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            hpcShareServiceDeleteResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(hpcShareServiceDelete, hpcShareServiceDeleteResult);

        return hpcShareServiceDeleteResult;
    }

    public FDTaskInfoResult handleMessage(TaskInfoScan taskInfoScan) {

        log.info("receiving message for authCloud, virtual type : [{}]", taskInfoScan.getVirtEnvType());

        log.info("msg id : [{}]", taskInfoScan.getMsgId());

        FDTaskInfoResult fdTaskInfoResult = new FDTaskInfoResult();

        try {
            fdTaskInfoResult = vmHandler.taskInfoScan(taskInfoScan);
            log.info("adaptor authCloud :[{}] has been authentication successfully", taskInfoScan.getVirtEnvType());
        } catch (CommonAdapterException e) {

            fdTaskInfoResult.setSuccess(false);
            fdTaskInfoResult.setErrCode(e.getErrCode());
            fdTaskInfoResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            fdTaskInfoResult.setSuccess(false);
            fdTaskInfoResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            fdTaskInfoResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            fdTaskInfoResult.setSuccess(false);
            fdTaskInfoResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            fdTaskInfoResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(taskInfoScan, fdTaskInfoResult);

        return fdTaskInfoResult;
    }

    public HPCShareServiceResult handleMessage(HPCShareServiceCreate hpcClusterCreate) {

        log.info("receiving message for authCloud, virtual type : [{}]", hpcClusterCreate.getVirtEnvType());

        log.info("msg id : [{}]", hpcClusterCreate.getMsgId());

        HPCShareServiceResult hpcClusterCreateResult = new HPCShareServiceResult();

        try {
            hpcClusterCreateResult = vmHandler.createHPCShareCluster(hpcClusterCreate);
            log.info("adaptor authCloud :[{}] has been authentication successfully", hpcClusterCreate.getVirtEnvType());
        } catch (CommonAdapterException e) {

            hpcClusterCreateResult.setSuccess(false);
            hpcClusterCreateResult.setErrCode(e.getErrCode());
            hpcClusterCreateResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            hpcClusterCreateResult.setSuccess(false);
            hpcClusterCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            hpcClusterCreateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            hpcClusterCreateResult.setSuccess(false);
            hpcClusterCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            hpcClusterCreateResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(hpcClusterCreate, hpcClusterCreateResult);

        return hpcClusterCreateResult;
    }

    public FDHPCClusterActiveResult handleMessage(FDHPCClusterActive fdhpcClusterActive) {

        log.info("receiving message for authCloud, virtual type : [{}]", fdhpcClusterActive.getVirtEnvType());

        log.info("msg id : [{}]", fdhpcClusterActive.getMsgId());

        FDHPCClusterActiveResult fdhpcClusterActiveResult = new FDHPCClusterActiveResult();

        try {
            fdhpcClusterActiveResult = vmHandler.fdhpcClusterActive(fdhpcClusterActive);
            log.info("adaptor authCloud :[{}] has been authentication successfully",fdhpcClusterActive.getVirtEnvType());
        } catch (CommonAdapterException e) {

            fdhpcClusterActiveResult.setSuccess(false);
            fdhpcClusterActiveResult.setErrCode(e.getErrCode());
            fdhpcClusterActiveResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            fdhpcClusterActiveResult.setSuccess(false);
            fdhpcClusterActiveResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            fdhpcClusterActiveResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            fdhpcClusterActiveResult.setSuccess(false);
            fdhpcClusterActiveResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            fdhpcClusterActiveResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(fdhpcClusterActive, fdhpcClusterActiveResult);

        return fdhpcClusterActiveResult;
    }

    public HPCClusterInfoIDResult handleMessage(HPCClusterInfoID hpcClusterInfoID) {

        log.info("receiving message for authCloud, virtual type : [{}]", hpcClusterInfoID.getVirtEnvType());

        log.info("msg id : [{}]", hpcClusterInfoID.getMsgId());

        HPCClusterInfoIDResult hpcClusterInfoIDResult = new HPCClusterInfoIDResult();

        try {
            hpcClusterInfoIDResult = vmHandler.queryHPCClusterInfoID(hpcClusterInfoID);
            log.info("adaptor authCloud :[{}] has been authentication successfully",hpcClusterInfoID.getVirtEnvType());
        } catch (CommonAdapterException e) {

            hpcClusterInfoIDResult.setSuccess(false);
            hpcClusterInfoIDResult.setErrCode(e.getErrCode());
            hpcClusterInfoIDResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            hpcClusterInfoIDResult.setSuccess(false);
            hpcClusterInfoIDResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            hpcClusterInfoIDResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            hpcClusterInfoIDResult.setSuccess(false);
            hpcClusterInfoIDResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            hpcClusterInfoIDResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(hpcClusterInfoID, hpcClusterInfoIDResult);

        return hpcClusterInfoIDResult;
    }

    /**
     * Handle message azure resourceGroup scan.
     *
     * @param resourceGroupScan
     */
    public ResourceGroupScanResult handleMessage(ResourceGroupScan resourceGroupScan) {

        log.info("receiving message for authCloud, virtual type : [{}]", resourceGroupScan.getVirtEnvType());

        log.info("msg id : [{}]", resourceGroupScan.getMsgId());

        ResourceGroupScanResult resourceGroupScanResult = new ResourceGroupScanResult();

        try {
            resourceGroupScanResult = scanHandler.scanResourceGroup(resourceGroupScan);
            log.info("adaptor authCloud :[{}] has been authentication successfully",resourceGroupScan.getVirtEnvType());
        } catch (CommonAdapterException e) {

            resourceGroupScanResult.setSuccess(false);
            resourceGroupScanResult.setErrCode(e.getErrCode());
            resourceGroupScanResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            resourceGroupScanResult.setSuccess(false);
            resourceGroupScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            resourceGroupScanResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            resourceGroupScanResult.setSuccess(false);
            resourceGroupScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            resourceGroupScanResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(resourceGroupScan, resourceGroupScanResult);

        return resourceGroupScanResult;

    }

    /**
     * Handle message vm query result.
     *
     * @param vmQuery the vm query
     *
     * @return the vm query result
     */
    public VmQueryResult handleMessage(VmQuery vmQuery) {

        log.info("receiving message for querying vms, virtual type : [{}]", vmQuery.getVirtEnvType());

        log.info("msg id : [{}]", vmQuery.getMsgId());

        VmQueryResult vmQueryResult = new VmQueryResult();

        try {
            vmQueryResult = vmHandler.queryVms(vmQuery);
        } catch (CommonAdapterException e) {
            vmQueryResult.setSuccess(false);
            vmQueryResult.setErrCode(e.getErrCode());
            vmQueryResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            vmQueryResult.setSuccess(false);
            vmQueryResult.setErrCode(AdapterUnvailableException.CODE);
            vmQueryResult.setErrMsg(AdapterUnvailableException.MSG);
        } catch (Exception e) {
            vmQueryResult.setSuccess(false);
            vmQueryResult.setErrCode(AdapterUnvailableException.CODE);
            vmQueryResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(vmQuery, vmQueryResult);
        vmQueryResult.setMsgId(vmQuery.getMsgId());

        return vmQueryResult;

    }

    /**
     * Handle message register env result.
     *
     * @param registerEnv the register env
     *
     * @return the register env result
     */
    public RegisterEnvResult handleMessage(RegisterEnv registerEnv) {

        log.info("receiving message for registering env, virtual type : [{}]", registerEnv.getVirtEnvType());

        RegisterEnvResult result = new RegisterEnvResult();
        result.setVirtualType(registerEnv.getVirtEnvType());
        result.setUuid(registerEnv.getVirtEnvUuid());

        return result;

    }

    /**
     * Handle message un register env result.
     *
     * @param unRegisterEnv the un register env
     *
     * @return the un register env result
     */
    public UnRegisterEnvResult handleMessage(UnRegisterEnv unRegisterEnv) {

        log.info("receiving message for registering env, virtual type : [{}]", unRegisterEnv.getVirtEnvType());

        UnRegisterEnvResult result = new UnRegisterEnvResult();
        result.setVirtualType(unRegisterEnv.getVirtEnvType());
        result.setUuid(unRegisterEnv.getVirtEnvUuid());

        return result;

    }

    /**
     * Handle message snapshot scan result.
     *
     * @param snapshotScan the snapshot scan
     *
     * @return the snapshot scan result
     */
    public SnapshotScanResult handleMessage(SnapshotScan snapshotScan) {

        log.info("receiving message for scanning snapshot, virtual type : [{}]", snapshotScan.getVirtEnvType());

        log.info("msg id : [{}]", snapshotScan.getMsgId());

        SnapshotScanResult snapshotScanResult = new SnapshotScanResult();
        try {
            snapshotScanResult = scanHandler.scanSnapshot(snapshotScan);
            snapshotScanResult.setSuccess(true);
        } catch (CommonAdapterException e) {
            snapshotScanResult.setSuccess(false);
            snapshotScanResult.setErrCode(e.getErrCode());
            snapshotScanResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            snapshotScanResult.setSuccess(false);
            snapshotScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            snapshotScanResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {

            snapshotScanResult.setSuccess(false);
            snapshotScanResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(snapshotScan, snapshotScanResult);
        snapshotScanResult.setMsgId(snapshotScan.getMsgId());
        snapshotScanResult.setResVeSid(snapshotScan.getVirtEnvUuid());
        return snapshotScanResult;
    }

    /**
     * Handle message tenant create result.
     *
     * @param avilableNetwork the tenant create
     *
     * @return the tenant create result
     */
    public AvilableNetworkResult handleMessage(AvilableNetwork avilableNetwork) {

        log.info("receiving message for avilableNetwork tenant, virtual type : [{}]", avilableNetwork.getVirtEnvType());

        log.info("msg id : [{}]", avilableNetwork.getMsgId());

        AvilableNetworkResult avilableNetworkResult = new AvilableNetworkResult();

        try {
            avilableNetworkResult = netHandler.getAvilableNetwork(avilableNetwork);
        } catch (CommonAdapterException e) {

            avilableNetworkResult.setSuccess(false);
            avilableNetworkResult.setErrCode(e.getErrCode());
            avilableNetworkResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            avilableNetworkResult.setSuccess(false);
            avilableNetworkResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            avilableNetworkResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            avilableNetworkResult.setSuccess(false);
            avilableNetworkResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            avilableNetworkResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(avilableNetwork, avilableNetworkResult);

        return avilableNetworkResult;

    }

    /**
     * Handle message tenant create result.
     *
     * @param cloudOsAddTenants the tenant create
     *
     * @return the tenant create result
     */
    public CloudOsAddTenantsResult handleMessage(CloudOsAddTenants cloudOsAddTenants) {

        log.info("receiving message for cloudOsAddTenants tenant, virtual type : [{}]", cloudOsAddTenants.getVirtEnvType());

        log.info("msg id : [{}]", cloudOsAddTenants.getMsgId());

        CloudOsAddTenantsResult cloudOsAddTenantsResult = new CloudOsAddTenantsResult();

        try {
            cloudOsAddTenantsResult = adminHandler.cloudOsAddTenants(cloudOsAddTenants);
        } catch (CommonAdapterException e) {

            cloudOsAddTenantsResult.setSuccess(false);
            cloudOsAddTenantsResult.setErrCode(e.getErrCode());
            cloudOsAddTenantsResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            cloudOsAddTenantsResult.setSuccess(false);
            cloudOsAddTenantsResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            cloudOsAddTenantsResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            cloudOsAddTenantsResult.setSuccess(false);
            cloudOsAddTenantsResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            cloudOsAddTenantsResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(cloudOsAddTenants, cloudOsAddTenantsResult);

        return cloudOsAddTenantsResult;

    }

    /**
     * Handle message tenant create result.
     *
     * @param tenantCreate the tenant create
     *
     * @return the tenant create result
     */
    public TenantCreateResult handleMessage(TenantCreate tenantCreate) {

        log.info("receiving message for create tenant, virtual type : [{}]", tenantCreate.getVirtEnvType());

        log.info("msg id : [{}]", tenantCreate.getMsgId());

        TenantCreateResult tenantCreateResult = new TenantCreateResult();

        try {
            tenantCreateResult = adminHandler.createTenant(tenantCreate);

        } catch (CommonAdapterException e) {

            tenantCreateResult.setSuccess(false);
            tenantCreateResult.setErrCode(e.getErrCode());
            tenantCreateResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            tenantCreateResult.setSuccess(false);
            tenantCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            tenantCreateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            tenantCreateResult.setSuccess(false);
            tenantCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            tenantCreateResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(tenantCreate, tenantCreateResult);

        if (tenantCreateResult.isSuccess()) {
            log.info("[adaptor] tenant : [{}] has been created successfully",tenantCreate.getName());
        } else {
            log.info("[adaptor] tenant : [{}] has been created failure",tenantCreate.getName());
        }
        return tenantCreateResult;

    }

    /**
     * Handle message tenant list get result.
     *
     * @param tenantListGet the tenant list get
     *
     * @return the tenant list get result
     */
    public TenantListGetResult handleMessage(TenantListGet tenantListGet) {
        log.info("receiving message for getting tenantlist, virtual type : [{}]",tenantListGet.getVirtEnvType());

        log.info("msg id : [{}]", tenantListGet.getMsgId());
        TenantListGetResult tenantListGetResult = new TenantListGetResult();
        try {
            tenantListGetResult = adminHandler.queryTenant(tenantListGet);

        } catch (CommonAdapterException e) {

            tenantListGetResult.setSuccess(false);
            tenantListGetResult.setErrCode(e.getErrCode());
            tenantListGetResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            tenantListGetResult.setSuccess(false);
            tenantListGetResult.setErrCode(AdapterUnvailableException.CODE);
            tenantListGetResult.setErrMsg(AdapterUnvailableException.MSG);
        } catch (Exception e) {
            tenantListGetResult.setSuccess(false);
            tenantListGetResult.setErrCode(AdapterUnvailableException.CODE);
            tenantListGetResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(tenantListGet, tenantListGetResult);

        return tenantListGetResult;
    }

    /**
     * Handle message tenant delete result.
     *
     * @param tenantDelete the tenant delete
     *
     * @return the tenant delete result
     */
    public TenantDeleteResult handleMessage(TenantDelete tenantDelete) {

        log.info("receiving message for deleting tenant, virtual type : [{}]", tenantDelete.getVirtEnvType());

        log.info("msg id : [{}]", tenantDelete.getMsgId());

        TenantDeleteResult tenantDeleteResult = new TenantDeleteResult();

        try {
            tenantDeleteResult = adminHandler.deleteTenant(tenantDelete);
        } catch (CommonAdapterException e) {

            tenantDeleteResult.setSuccess(false);
            tenantDeleteResult.setErrCode(e.getErrCode());
            tenantDeleteResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            tenantDeleteResult.setSuccess(false);
            tenantDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            tenantDeleteResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            tenantDeleteResult.setSuccess(false);
            tenantDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            tenantDeleteResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(tenantDelete, tenantDeleteResult);
        tenantDeleteResult.setMsgId(tenantDelete.getMsgId());
        tenantDeleteResult.setTenantId(tenantDelete.getTenantId());
        if (tenantDeleteResult.isSuccess()) {
            log.info("adaptor tenant : [{}] has been deleted successfully",tenantDelete.getTenantId());
        } else {
            log.info("adaptor tenant : [{}] has been deleted failure",tenantDelete.getTenantId());
        }
        return tenantDeleteResult;
    }

    /**
     * Handle message tenant edit result.
     *
     * @param tenantEdit the tenant edit
     *
     * @return the tenant edit result
     */
    public TenantEditResult handleMessage(TenantEdit tenantEdit) {

        log.info("receiving message for deleting tenant, virtual type : [{}]", tenantEdit.getVirtEnvType());

        log.info("msg id : [{}]", tenantEdit.getMsgId());

        TenantEditResult tenantEditResult = new TenantEditResult();

        try {
            tenantEditResult = adminHandler.editTenant(tenantEdit);
        } catch (CommonAdapterException e) {

            tenantEditResult.setSuccess(false);
            tenantEditResult.setErrCode(e.getErrCode());
            tenantEditResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            tenantEditResult.setSuccess(false);
            tenantEditResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            tenantEditResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            tenantEditResult.setSuccess(false);
            tenantEditResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            tenantEditResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(tenantEdit, tenantEditResult);
        tenantEditResult.setMsgId(tenantEdit.getMsgId());
        if (tenantEditResult.isSuccess()) {
            log.info("adaptor tenant : [{}] has been updated successfully",tenantEdit.getProjectId());
        } else {
            log.info("adaptor tenant : [{}] has been updated failure",tenantEdit.getProjectId());
        }
        return tenantEditResult;
    }

    /**
     * Handle message tenant user config result.
     *
     * @param tenantUserConfig the tenant user config
     *
     * @return the tenant user config result
     */
    public TenantUserConfigResult handleMessage(TenantUserConfig tenantUserConfig) {

        log.info("receiving message for config tenant user, virtual type : [{}]", tenantUserConfig.getVirtEnvType());

        log.info("msg id : [{}]", tenantUserConfig.getMsgId());

        TenantUserConfigResult tenantUserConfigResult = new TenantUserConfigResult();

        try {
            tenantUserConfigResult = adminHandler.configTenantUser(tenantUserConfig);
        } catch (CommonAdapterException e) {

            tenantUserConfigResult.setSuccess(false);
            tenantUserConfigResult.setErrCode(e.getErrCode());
            tenantUserConfigResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            tenantUserConfigResult.setSuccess(false);
            tenantUserConfigResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            tenantUserConfigResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            tenantUserConfigResult.setSuccess(false);
            tenantUserConfigResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            tenantUserConfigResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(tenantUserConfig, tenantUserConfigResult);
        tenantUserConfigResult.setMsgId(tenantUserConfig.getMsgId());
        if (tenantUserConfigResult.isSuccess()) {
            log.info("adaptor tenant user has been config successfully");
        } else {
            log.info("adaptor tenant user has been config failure");
        }
        return tenantUserConfigResult;
    }

    /**
     * Handle message user create result.
     *
     * @param userCreate the user create
     *
     * @return the user create result
     */
    public UserCreateResult handleMessage(UserCreate userCreate) {

        log.info("receiving message for creating user, virtual type : [{}]", userCreate.getVirtEnvType());

        log.info("msg id : [{}]", userCreate.getMsgId());

        UserCreateResult userCreateResult = new UserCreateResult();

        try {
            userCreateResult = adminHandler.createUser(userCreate);

        } catch (CommonAdapterException e) {

            userCreateResult.setSuccess(false);
            userCreateResult.setErrCode(e.getErrCode());
            userCreateResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            userCreateResult.setSuccess(false);
            userCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            userCreateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            userCreateResult.setSuccess(false);
            userCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            userCreateResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(userCreate, userCreateResult);

        if (userCreateResult.isSuccess()) {
            log.info("adaptor user : [{}] has been created successfully",userCreate.getName());
        } else {
            log.info("adaptor user : [{}] has been created failure",userCreate.getName());

        }
        return userCreateResult;

    }

    /**
     * Handle message user delete result.
     *
     * @param userDelete the user delete
     *
     * @return the user delete result
     */
    public UserDeleteResult handleMessage(UserDelete userDelete) {

        log.info("receiving message for deleting user, virtual type : [{}]", userDelete.getVirtEnvType());

        log.info("msg id : [{}]", userDelete.getMsgId());

        UserDeleteResult userDeleteResult = new UserDeleteResult();

        try {
            userDeleteResult = adminHandler.deleteUser(userDelete);

        } catch (CommonAdapterException e) {

            userDeleteResult.setSuccess(false);
            userDeleteResult.setErrCode(e.getErrCode());
            userDeleteResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            userDeleteResult.setSuccess(false);
            userDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            userDeleteResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            userDeleteResult.setSuccess(false);
            userDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            userDeleteResult.setErrMsg(e.getMessage());
        }

        BaseUtil.setResult(userDelete, userDeleteResult);
        userDeleteResult.setMsgId(userDelete.getMsgId());
        userDeleteResult.setUserId(userDelete.getUserId());
        if (userDeleteResult.isSuccess()) {
            log.info("adaptor user : [{}] has been deleted successfully",userDelete.getUserId());
        } else {
            log.info("adaptor user : [{}] has been deleted failure",userDelete.getUserId());

        }
        return userDeleteResult;

    }

    /**
     * Handle message user edit result.
     *
     * @param userEdit the user edit
     *
     * @return the user delete result
     */
    public UserEditResult handleMessage(UserEdit userEdit) {

        log.info("receiving message for edit user, virtual type : [{}]", userEdit.getVirtEnvType());

        log.info("msg id : [{}]", userEdit.getMsgId());

        UserEditResult userEditResult = new UserEditResult();

        try {
            userEditResult = adminHandler.editUser(userEdit);

        } catch (CommonAdapterException e) {

            userEditResult.setSuccess(false);
            userEditResult.setErrCode(e.getErrCode());
            userEditResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            userEditResult.setSuccess(false);
            userEditResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            userEditResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            userEditResult.setSuccess(false);
            userEditResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            userEditResult.setErrMsg(e.getMessage());
        }

        BaseUtil.setResult(userEdit, userEditResult);
        userEditResult.setMsgId(userEdit.getMsgId());
        if (userEditResult.isSuccess()) {
            log.info("adaptor user :[{}] has been edit successfully",userEdit.getUserId());
        } else {
            log.info("adaptor user :[{}] has been edit failure",userEdit.getUserId());

        }
        return userEditResult;

    }

    /**
     * Handle message validate user password.
     *
     * @param userPasswordValid validate user password
     *
     * @return the user delete result
     */
    public UserPasswordValidResult handleMessage(UserPasswordValid userPasswordValid) {

        log.info(
                "receiving message for validate user password, virtual type : [{}]"
                        , userPasswordValid.getVirtEnvType());

        log.info("msg id [{}]: ", userPasswordValid.getMsgId());

        UserPasswordValidResult userPasswordValidResult = new UserPasswordValidResult();

        try {
            userPasswordValidResult = adminHandler.validateUserPassword(userPasswordValid);

        } catch (CommonAdapterException e) {

            userPasswordValidResult.setSuccess(false);
            userPasswordValidResult.setErrCode(e.getErrCode());
            userPasswordValidResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            userPasswordValidResult.setSuccess(false);
            userPasswordValidResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            userPasswordValidResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            userPasswordValidResult.setSuccess(false);
            userPasswordValidResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            userPasswordValidResult.setErrMsg(e.getMessage());
        }

        BaseUtil.setResult(userPasswordValid, userPasswordValidResult);
        userPasswordValidResult.setMsgId(userPasswordValid.getMsgId());
        if (userPasswordValidResult.isSuccess()) {
            log.info("adaptor user :[{}] has been validated successfully",userPasswordValid.getUserUuid());
        } else {
            log.info("adaptor user :[{}] has been validated failure",userPasswordValid.getUserUuid());

        }
        return userPasswordValidResult;

    }

    /**
     * Handle message modify user password.
     *
     * @param userPasswordModify modify user password
     *
     * @return the modify user password result
     */
    public UserPasswordModifyResult handleMessage(UserPasswordModify userPasswordModify) {

        log.info("receiving message for modify user password, virtual type : [{}]", userPasswordModify.getVirtEnvType());

        log.info("msg id : [{}]", userPasswordModify.getMsgId());

        UserPasswordModifyResult userPasswordModifyResult = new UserPasswordModifyResult();

        try {
            userPasswordModifyResult = adminHandler.modifyUserPassword(userPasswordModify);

        } catch (CommonAdapterException e) {

            userPasswordModifyResult.setSuccess(false);
            userPasswordModifyResult.setErrCode(e.getErrCode());
            userPasswordModifyResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            userPasswordModifyResult.setSuccess(false);
            userPasswordModifyResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            userPasswordModifyResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            userPasswordModifyResult.setSuccess(false);
            userPasswordModifyResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            userPasswordModifyResult.setErrMsg(e.getMessage());
        }

        BaseUtil.setResult(userPasswordModify, userPasswordModifyResult);
        userPasswordModifyResult.setMsgId(userPasswordModify.getMsgId());
        if (userPasswordModifyResult.isSuccess()) {
            log.info("adaptor user : [{}] has been modified successfully",userPasswordModify.getUserUuid());
        } else {
            log.info("adaptor user : [{}] has been modified failure",userPasswordModify.getUserUuid());

        }
        return userPasswordModifyResult;

    }

    /**
     * Handle message user list get result.
     *
     * @param userListGet the user list get
     *
     * @return the user list get result
     */
    public UserListGetResult handleMessage(UserListGet userListGet) {

        log.info("receiving message for getting userlist, virtual type : [{}]", userListGet.getVirtEnvType());

        log.info("msg id : [{}]", userListGet.getMsgId());

        UserListGetResult result = new UserListGetResult();

        try {
            result = adminHandler.queryUser(userListGet);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(userListGet, result);
        result.setMsgId(userListGet.getMsgId());
        if (result.isSuccess()) {
            log.info("[adaptor] get userList successfully");
        } else {
            log.info("[adaptor] get userList failure");
        }
        return result;
    }

    /**
     * Handle message role list get result.
     *
     * @param roleScan the user role list get
     *
     * @return the user role list get result
     */
    public RoleScanResult handleMessage(RoleScan roleScan) {

        log.info("receiving message for scan role, virtual type : [{}]", roleScan.getVirtEnvType());

        log.info("msg id : [{}]", roleScan.getMsgId());

        RoleScanResult result = new RoleScanResult();

        try {
            result = adminHandler.queryRole(roleScan);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(roleScan, result);
        result.setMsgId(roleScan.getMsgId());
        if (result.isSuccess()) {
            log.info("[adaptor] scan role successfully");
        } else {
            log.info("[adaptor] scan role failure");
        }
        return result;

    }

    /**
     * Handle message the user's relationship with project.
     *
     * @param user2ProjectScan the user's relationship with project
     *
     * @return the user's relationship with project get result
     */
    public User2ProjectScanResult handleMessage(User2ProjectScan user2ProjectScan) {

        log.info("receiving message for scan user's relationship with project , virtual type : [{}]", user2ProjectScan.getVirtEnvType());

        log.info("msg id : [{}]", user2ProjectScan.getMsgId());

        User2ProjectScanResult result = new User2ProjectScanResult();

        try {
            result = adminHandler.queryUser2Project(user2ProjectScan);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(user2ProjectScan, result);
        result.setMsgId(user2ProjectScan.getMsgId());
        if (result.isSuccess()) {
            log.info("[adaptor] scan user 2 project successfully");
        } else {
            log.info("[adaptor] scan user 2 project failure");
        }
        return result;

    }

    /**
     * Handle message user role add result.
     *
     * @param userRoleAdd the user role add
     *
     * @return the user role add result
     */
    public UserRoleAddResult handleMessage(UserRoleAdd userRoleAdd) {

        log.info("receiving message for adding user role, virtual type : [{}]", userRoleAdd.getVirtEnvType());

        log.info("msg id : [{}]", userRoleAdd.getMsgId());

        UserRoleAddResult userRoleAddResult = new UserRoleAddResult();
        userRoleAddResult = BaseUtil.setResult(userRoleAdd, UserRoleAddResult.class);
        userRoleAddResult.setMsgId(userRoleAdd.getMsgId());
        userRoleAddResult.setTenantUuid(userRoleAdd.getTenantUuid());
        userRoleAddResult.setUserUuid(userRoleAdd.getUserUuid());
        userRoleAddResult.setRoleUuid(userRoleAdd.getRoleUuid());

        try {
            adminHandler.addRoleToUser(userRoleAdd);
            userRoleAddResult.setSuccess(true);

            log.info("adaptor user :[{}] has been assigned role successfully",userRoleAdd.getUserUuid());
        } catch (CommonAdapterException e) {

            userRoleAddResult.setSuccess(false);
            userRoleAddResult.setErrCode(e.getErrCode());
            userRoleAddResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            userRoleAddResult.setSuccess(false);
            userRoleAddResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            userRoleAddResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            userRoleAddResult.setSuccess(false);
            userRoleAddResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            userRoleAddResult.setErrMsg(e.getMessage());
        }

        return userRoleAddResult;

    }

    /**
     * Handle message user role delete result.
     *
     * @param userRoleDelete the user role delete
     *
     * @return the user role delete result
     */
    public UserRoleDeleteResult handleMessage(UserRoleDelete userRoleDelete) {

        log.info("receiving message for deleting user role, virtual type : [{}]", userRoleDelete.getVirtEnvType());

        log.info("msg id : [{}]", userRoleDelete.getMsgId());

        UserRoleDeleteResult userRoleDeleteResult = new UserRoleDeleteResult();
        userRoleDeleteResult = BaseUtil.setResult(userRoleDelete, UserRoleDeleteResult.class);
        userRoleDeleteResult.setMsgId(userRoleDelete.getMsgId());

        userRoleDeleteResult.setTenantUuid(userRoleDelete.getTenantUuid());
        userRoleDeleteResult.setUserUuid(userRoleDelete.getUserUuid());
        userRoleDeleteResult.setRoleUuid(userRoleDelete.getRoleUuid());

        try {
            adminHandler.deleteRoleToUser(userRoleDelete);
            userRoleDeleteResult.setSuccess(true);

            log.info("adaptor user :[{}] has been unassigned role successfully",userRoleDelete.getUserUuid());
        } catch (CommonAdapterException e) {

            userRoleDeleteResult.setSuccess(false);
            userRoleDeleteResult.setErrCode(e.getErrCode());
            userRoleDeleteResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            userRoleDeleteResult.setSuccess(false);
            userRoleDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            userRoleDeleteResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            userRoleDeleteResult.setSuccess(false);
            userRoleDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            userRoleDeleteResult.setErrMsg(e.getMessage());
        }

        return userRoleDeleteResult;

    }

    /**
     * Handle message user role list result.
     *
     * @param userRoleList the user role list
     *
     * @return the user role list result
     */
    public UserRoleListResult handleMessage(UserRoleList userRoleList) {

        log.info(
                "receiving message for listing user role, virtual type : [{}]", userRoleList.getVirtEnvType());

        log.info("msg id : [{}]", userRoleList.getMsgId());

        UserRoleListResult userRoleListResult = new UserRoleListResult();

        try {
            userRoleListResult = adminHandler.listUserRoles(userRoleList);
        } catch (CommonAdapterException e) {

            userRoleListResult.setSuccess(false);
            userRoleListResult.setErrCode(e.getErrCode());
            userRoleListResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            userRoleListResult.setSuccess(false);
            userRoleListResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            userRoleListResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            userRoleListResult.setSuccess(false);
            userRoleListResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            userRoleListResult.setErrMsg(e.getMessage());
        }

        BaseUtil.setResult(userRoleList, userRoleListResult);
        userRoleListResult.setMsgId(userRoleList.getMsgId());

        return userRoleListResult;

    }

    /**
     * Handle message role list result.
     *
     * @param roleList the role list
     *
     * @return the role list result
     */
    public RoleListResult handleMessage(RoleList roleList) {

        log.info("receiving message for listing role, virtual type : [{}]", roleList.getVirtEnvType());

        log.info("msg id : [{}]", roleList.getMsgId());

        RoleListResult roleListResult = new RoleListResult();

        try {
            roleListResult = adminHandler.listRoles(roleList);

        } catch (CommonAdapterException e) {

            roleListResult.setSuccess(false);
            roleListResult.setErrCode(e.getErrCode());
            roleListResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            roleListResult.setSuccess(false);
            roleListResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            roleListResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            roleListResult.setSuccess(false);
            roleListResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            roleListResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(roleList, roleListResult);
        roleListResult.setMsgId(roleList.getMsgId());
        return roleListResult;

    }

    /**
     * Handle message storage scan result.
     *
     * @param storageScan the storage scan
     *
     * @return the storage scan result
     */
    public StorageScanResult handleMessage(StorageScan storageScan) {

        log.info("receiving message for scanning storages, virtual type : [{}]", storageScan.getVirtEnvType());

        log.info("msg id : [{}]", storageScan.getMsgId());

        StorageScanResult storageScanResult = new StorageScanResult();

        try {
            storageScanResult = scanHandler.scanDataStore(storageScan);
            storageScanResult.setSuccess(true);
        } catch (CommonAdapterException e) {

            storageScanResult.setSuccess(false);
            storageScanResult.setErrCode(e.getErrCode());
            storageScanResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            storageScanResult.setSuccess(false);
            storageScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            storageScanResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            storageScanResult.setSuccess(false);
            storageScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            storageScanResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(storageScan, storageScanResult);
        storageScanResult.setMsgId(storageScan.getMsgId());
        return storageScanResult;

    }

    public RegionScanResult handleMessage(RegionScan regionScan) {

        log.info("receiving message for scanning region, virtual type : [{}]", regionScan.getVirtEnvType());

        log.info("msg id : [{}]", regionScan.getMsgId());

        RegionScanResult regionScanResult = new RegionScanResult();

        try {
            regionScanResult = scanHandler.scanRegion(regionScan);
            regionScanResult.setSuccess(true);
        } catch (CommonAdapterException e) {

            regionScanResult.setSuccess(false);
            regionScanResult.setErrCode(e.getErrCode());
            regionScanResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            regionScanResult.setSuccess(false);
            regionScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            regionScanResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            regionScanResult.setSuccess(false);
            regionScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            regionScanResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(regionScan, regionScanResult);
        regionScanResult.setMsgId(regionScan.getMsgId());

        return regionScanResult;

    }

    /**
     * Handle message tags by env result.
     *
     * @param scanTagsByEnv the scan vms by env
     *
     * @return the scan vms by env result
     */
    public ScanTagsByEnvResult handleMessage(ScanTagsByEnv scanTagsByEnv) {

        log.info(
                "receiving message for scanning tags, virtual type : [{}]", scanTagsByEnv.getVirtEnvType());

        log.info("msg id : [{}]", scanTagsByEnv.getMsgId());

        ScanTagsByEnvResult scanTagsByEnvResult = new ScanTagsByEnvResult();

        try {
            scanTagsByEnvResult = scanHandler.scanTagsByEnv(scanTagsByEnv);
            scanTagsByEnvResult.setSuccess(true);
        } catch (CommonAdapterException e) {

            scanTagsByEnvResult.setSuccess(false);
            scanTagsByEnvResult.setErrCode(e.getErrCode());
            scanTagsByEnvResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            scanTagsByEnvResult.setSuccess(false);
            scanTagsByEnvResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            scanTagsByEnvResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            scanTagsByEnvResult.setSuccess(false);
            scanTagsByEnvResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            scanTagsByEnvResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(scanTagsByEnv, scanTagsByEnvResult);
        scanTagsByEnvResult.setMsgId(scanTagsByEnv.getMsgId());
        return scanTagsByEnvResult;

    }

    /**
     * 扫描资源的数量
     *
     * @param resourceScan
     */
    public ResourceScanResult handleMessage(ResourceScan resourceScan) {

        log.info("receiving message for scanning resource, virtual type : [{}]", resourceScan.getVirtEnvType());

        log.info("msg id : [{}]", resourceScan.getMsgId());

        ResourceScanResult resourceScanResult = new ResourceScanResult();

        try {
            resourceScanResult = scanHandler.scanResouce(resourceScan);
            resourceScanResult.setSuccess(true);
        } catch (CommonAdapterException e) {

            resourceScanResult.setSuccess(false);
            resourceScanResult.setErrCode(e.getErrCode());
            resourceScanResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            resourceScanResult.setSuccess(false);
            resourceScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            resourceScanResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            resourceScanResult.setSuccess(false);
            resourceScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            resourceScanResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(resourceScan, resourceScanResult);
        resourceScanResult.setMsgId(resourceScan.getMsgId());

        return resourceScanResult;

    }

    public AliWholeZoneScanResult handleMessage(AliWholeZoneScan aliWholeZoneScan) {

        log.info("receiving message for scanning region, virtual type : [{}]", aliWholeZoneScan.getVirtEnvType());

        log.info("msg id : [{}]", aliWholeZoneScan.getMsgId());

        AliWholeZoneScanResult aliWholeZoneScanResult = new AliWholeZoneScanResult();

        try {
            aliWholeZoneScanResult = scanHandler.scanAliWholeZone(aliWholeZoneScan);
            aliWholeZoneScanResult.setSuccess(true);
        } catch (CommonAdapterException e) {

            aliWholeZoneScanResult.setSuccess(false);
            aliWholeZoneScanResult.setErrCode(e.getErrCode());
            aliWholeZoneScanResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            aliWholeZoneScanResult.setSuccess(false);
            aliWholeZoneScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            aliWholeZoneScanResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            aliWholeZoneScanResult.setSuccess(false);
            aliWholeZoneScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            aliWholeZoneScanResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(aliWholeZoneScan, aliWholeZoneScanResult);
        aliWholeZoneScanResult.setMsgId(aliWholeZoneScan.getMsgId());

        return aliWholeZoneScanResult;

    }

    /**
     * Handle message scan vms by env result.
     *
     * @param scanVmsByEnv the scan vms by env
     *
     * @return the scan vms by env result
     */
    public ScanVmsByEnvResult handleMessage(ScanVmsByEnv scanVmsByEnv) {

        log.info(
                "receiving message for scanning vm, virtual type : [{}]", scanVmsByEnv.getVirtEnvType());

        log.info("msg id : [{}]", scanVmsByEnv.getMsgId());

        ScanVmsByEnvResult scanVmsByEnvResult = new ScanVmsByEnvResult();

        try {
            scanVmsByEnvResult = scanHandler.scanVmsByEnv(scanVmsByEnv);
            scanVmsByEnvResult.setSuccess(true);
        } catch (CommonAdapterException e) {

            scanVmsByEnvResult.setSuccess(false);
            scanVmsByEnvResult.setErrCode(e.getErrCode());
            scanVmsByEnvResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            scanVmsByEnvResult.setSuccess(false);
            scanVmsByEnvResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            scanVmsByEnvResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            scanVmsByEnvResult.setSuccess(false);
            scanVmsByEnvResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            scanVmsByEnvResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(scanVmsByEnv, scanVmsByEnvResult);
        scanVmsByEnvResult.setMsgId(scanVmsByEnv.getMsgId());
        return scanVmsByEnvResult;

    }

    /**
     * Handle message sfs quota calculate result.
     */
    public ResShareRuleCreateResult handleMessage(ResShareRuleCreate resShareRuleCreate) {

        log.info("receiving resShareRule quota for metric monitor, virtual type : [{}]", resShareRuleCreate.getVirtEnvType());

        log.info("msg id : [{}]", resShareRuleCreate.getMsgId());

        ResShareRuleCreateResult resShareRuleCreateResult = new ResShareRuleCreateResult();

        try {
            resShareRuleCreateResult = vmHandler.resShareRule(resShareRuleCreate);
        } catch (CommonAdapterException e) {

            resShareRuleCreateResult.setSuccess(false);
            resShareRuleCreateResult.setErrCode(e.getErrCode());
            resShareRuleCreateResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            resShareRuleCreateResult.setSuccess(false);
            resShareRuleCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            resShareRuleCreateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            resShareRuleCreateResult.setSuccess(false);
            resShareRuleCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            resShareRuleCreateResult.setErrMsg(e.getMessage());
        }

        resShareRuleCreateResult.setMsgId(resShareRuleCreate.getMsgId());

        return resShareRuleCreateResult;

    }

    /**
     * Handle message scan vms by env result.
     *
     * @param scanSharesByEnv the scan vms by env
     *
     * @return the scan vms by env result
     */
    public ScanSharesByEnvResult handleMessage(ScanSharesByEnv scanSharesByEnv) {

        log.info(
                "receiving message for scanning share, virtual type : [{}]", scanSharesByEnv.getVirtEnvType());

        log.info("msg id : [{}]", scanSharesByEnv.getMsgId());

        ScanSharesByEnvResult scanSharesByEnvResult = new ScanSharesByEnvResult();

        try {
            scanSharesByEnvResult = scanHandler.scanSharesByEnv(scanSharesByEnv);
            scanSharesByEnvResult.setSuccess(true);
        } catch (CommonAdapterException e) {

            scanSharesByEnvResult.setSuccess(false);
            scanSharesByEnvResult.setErrCode(e.getErrCode());
            scanSharesByEnvResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            scanSharesByEnvResult.setSuccess(false);
            scanSharesByEnvResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            scanSharesByEnvResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            scanSharesByEnvResult.setSuccess(false);
            scanSharesByEnvResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            scanSharesByEnvResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(scanSharesByEnv, scanSharesByEnvResult);
        scanSharesByEnvResult.setMsgId(scanSharesByEnv.getMsgId());
        return scanSharesByEnvResult;

    }

    /**
     * Handle message scan share group by env result.
     *
     * @param scanShareGroupByEnv the scan vms by env
     *
     * @return the scan vms by env result
     */
    public ScanShareGroupByEnvResult handleMessage(ScanShareGroupByEnv scanShareGroupByEnv) {

        log.info("receiving message for scanning share group, virtual type : [{}]", scanShareGroupByEnv
                .getVirtEnvType());

        log.info("msg id : [{}]", scanShareGroupByEnv.getMsgId());

        ScanShareGroupByEnvResult scanShareGroupByEnvResult = new ScanShareGroupByEnvResult();

        try {
            scanShareGroupByEnvResult = scanHandler.scanShareGroupByEnv(scanShareGroupByEnv);
            scanShareGroupByEnvResult.setSuccess(true);
        } catch (CommonAdapterException e) {

            scanShareGroupByEnvResult.setSuccess(false);
            scanShareGroupByEnvResult.setErrCode(e.getErrCode());
            scanShareGroupByEnvResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            scanShareGroupByEnvResult.setSuccess(false);
            scanShareGroupByEnvResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            scanShareGroupByEnvResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            scanShareGroupByEnvResult.setSuccess(false);
            scanShareGroupByEnvResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            scanShareGroupByEnvResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(scanShareGroupByEnv, scanShareGroupByEnvResult);
        scanShareGroupByEnvResult.setMsgId(scanShareGroupByEnv.getMsgId());
        return scanShareGroupByEnvResult;

    }

    /**
     * Handle message scan share group rule by env result.
     *
     * @param scanShareGroupRuleByEnv the scan share group rule by env
     *
     * @return the scan share group rule by env result
     */
    public ScanShareGroupRuleByEnvResult handleMessage(ScanShareGroupRuleByEnv scanShareGroupRuleByEnv) {

        log.info("receiving message for scanning share group rule, virtual type : [{}]", scanShareGroupRuleByEnv
                .getVirtEnvType());

        log.info("msg id : [{}]", scanShareGroupRuleByEnv.getMsgId());

        ScanShareGroupRuleByEnvResult scanShareGroupRuleByEnvResult = new ScanShareGroupRuleByEnvResult();

        try {
            scanShareGroupRuleByEnvResult = scanHandler.scanShareGroupRuleByEnv(scanShareGroupRuleByEnv);
            scanShareGroupRuleByEnvResult.setSuccess(true);
        } catch (CommonAdapterException e) {

            scanShareGroupRuleByEnvResult.setSuccess(false);
            scanShareGroupRuleByEnvResult.setErrCode(e.getErrCode());
            scanShareGroupRuleByEnvResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            scanShareGroupRuleByEnvResult.setSuccess(false);
            scanShareGroupRuleByEnvResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            scanShareGroupRuleByEnvResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            scanShareGroupRuleByEnvResult.setSuccess(false);
            scanShareGroupRuleByEnvResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            scanShareGroupRuleByEnvResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(scanShareGroupRuleByEnv, scanShareGroupRuleByEnvResult);
        scanShareGroupRuleByEnvResult.setMsgId(scanShareGroupRuleByEnv.getMsgId());
        return scanShareGroupRuleByEnvResult;

    }

    /**
     * Handle message instanceGaapCost vms by env result.
     *
     * @param scanInstanceGaapCostByEnv the scan vms by env
     *
     * @return the scan vms by env result
     */
    public ScanInstanceGaapCostResult handleMessage(ScanInstanceGaapCostByEnv scanInstanceGaapCostByEnv) {

        log.info(
                "receiving message for scanning instanceGaapCost, virtual type : [{}]", scanInstanceGaapCostByEnv.getVirtEnvType());

        log.info("msg id : [{}]", scanInstanceGaapCostByEnv.getMsgId());

        ScanInstanceGaapCostResult scanInstanceGaapCostResult = new ScanInstanceGaapCostResult();

        try {
            scanInstanceGaapCostResult = scanHandler.scanInstanceGaapCostByEnv(scanInstanceGaapCostByEnv);
            scanInstanceGaapCostResult.setSuccess(true);
        } catch (CommonAdapterException e) {

            scanInstanceGaapCostResult.setSuccess(false);
            scanInstanceGaapCostResult.setErrCode(e.getErrCode());
            scanInstanceGaapCostResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            scanInstanceGaapCostResult.setSuccess(false);
            scanInstanceGaapCostResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            scanInstanceGaapCostResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            scanInstanceGaapCostResult.setSuccess(false);
            scanInstanceGaapCostResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            scanInstanceGaapCostResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(scanInstanceGaapCostByEnv, scanInstanceGaapCostResult);
        scanInstanceGaapCostResult.setMsgId(scanInstanceGaapCostByEnv.getMsgId());
        return scanInstanceGaapCostResult;

    }

    /**
     * Handle message template scan result.
     *
     * @param templateScan the template scan
     *
     * @return the template scan result
     */
    public TemplateScanResult handleMessage(TemplateScan templateScan) {

        log.info(
                "receiving message for scanning tempalte, virtual type : [{}]", templateScan.getVirtEnvType());

        log.info("msg id : [{}]", templateScan.getMsgId());
        TemplateScanResult templateScanResult = new TemplateScanResult();
        try {
            templateScanResult = scanHandler.scanTemplate(templateScan);
            templateScanResult.setSuccess(true);
        } catch (CommonAdapterException e) {
            templateScanResult.setSuccess(false);
            templateScanResult.setErrCode(e.getErrCode());
            templateScanResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            templateScanResult.setSuccess(false);
            templateScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            templateScanResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            log.error(Throwables.getStackTraceAsString(e));
            templateScanResult.setSuccess(false);
        }
        BaseUtil.setResult(templateScan, templateScanResult);
        templateScanResult.setMsgId(templateScan.getMsgId());
        templateScanResult.setResVeSid(templateScan.getVirtEnvUuid());
        return templateScanResult;
    }


    public PhycialMappingScanResult handleMessage(PhycialMappingScan phycialMappingScan) {

        log.info(
                "receiving message for scanning phycialMapping, virtual type : [{}]", phycialMappingScan.getVirtEnvType());

        log.info("msg id : [{}]", phycialMappingScan.getMsgId());
        PhycialMappingScanResult result = new PhycialMappingScanResult();
        try {
            result = scanHandler.scanPhycialMapping(phycialMappingScan);
            result.setSuccess(true);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            log.error(Throwables.getStackTraceAsString(e));
            result.setSuccess(false);
        }
        BaseUtil.setResult(phycialMappingScan, result);
        result.setMsgId(phycialMappingScan.getMsgId());
        return result;
    }

    public VmScanAloneResult handleMessage(VmScanAlone vmScanAlone) {

        log.info(
                "receiving message for scanning vm, virtual type : [{}]", vmScanAlone.getVirtEnvType());

        log.info("msg id : [{}]", vmScanAlone.getMsgId());

        VmScanAloneResult vmScanAloneResult = new VmScanAloneResult();

        try {
            vmScanAloneResult = scanHandler.scanVmAlone(vmScanAlone);
            vmScanAloneResult.setSuccess(true);
        } catch (CommonAdapterException e) {

            vmScanAloneResult.setSuccess(false);
            vmScanAloneResult.setErrCode(e.getErrCode());
            vmScanAloneResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            vmScanAloneResult.setSuccess(false);
            vmScanAloneResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmScanAloneResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            vmScanAloneResult.setSuccess(false);
            vmScanAloneResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmScanAloneResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(vmScanAlone, vmScanAloneResult);
        vmScanAloneResult.setMsgId(vmScanAlone.getMsgId());

        return vmScanAloneResult;

    }

    public ProjectScanResult handleMessage(ProjectScan projectScan) {

        log.info(
                "receiving message for scanning project, virtual type : [{}]", projectScan.getVirtEnvType());
        log.info("msg id : [{}]", projectScan.getMsgId());

        ProjectScanResult projectScanResult = new ProjectScanResult();

        try {
            projectScanResult = scanHandler.scanProject(projectScan);
            projectScanResult.setSuccess(true);
        } catch (CommonAdapterException e) {

            projectScanResult.setSuccess(false);
            projectScanResult.setErrCode(e.getErrCode());
            projectScanResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            projectScanResult.setSuccess(false);
            projectScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            projectScanResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            projectScanResult.setSuccess(false);
            projectScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            projectScanResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(projectScan, projectScanResult);
        projectScanResult.setMsgId(projectScan.getMsgId());

        return projectScanResult;

    }

    /**
     * Handle message cluster scan result.
     *
     * @param clusterScan the cluster scan
     *
     * @return the cluster scan result
     */
    public ClusterScanResult handleMessage(ClusterScan clusterScan) {

        log.info("receiving message for scanning clusters, virtual type : [{}]", clusterScan.getVirtEnvType());

        log.info("msg id : [{}]", clusterScan.getMsgId());

        ClusterScanResult clusterScanResult = new ClusterScanResult();

        try {
            clusterScanResult = scanHandler.scanCluster(clusterScan);
            clusterScanResult.setSuccess(true);
        } catch (CommonAdapterException e) {

            clusterScanResult.setSuccess(false);
            clusterScanResult.setErrCode(e.getErrCode());
            clusterScanResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            clusterScanResult.setSuccess(false);
            clusterScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            clusterScanResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            clusterScanResult.setSuccess(false);
            clusterScanResult.setErrCode("500");
            clusterScanResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(clusterScan, clusterScanResult);
        clusterScanResult.setMsgId(clusterScan.getMsgId());
        return clusterScanResult;

    }

    /**
     * Handle message all in one scan result.
     *
     * @param allInOneScan the all in one scan
     *
     * @return the all in one scan result
     */
    public AllInOneScanResult handleMessage(AllInOneScan allInOneScan) {

        log.info("receiving message for scanning all in one, virtual type : [{}]", allInOneScan.getVirtEnvType());

        log.info("msg id : [{}]", allInOneScan.getMsgId());

        AllInOneScanResult allInOneScanResult = new AllInOneScanResult();
        try {
            allInOneScanResult = scanHandler.scanAllInOne(allInOneScan);
            allInOneScanResult.setSuccess(true);
        } catch (CommonAdapterException e) {

            allInOneScanResult.setSuccess(false);
            allInOneScanResult.setErrCode(e.getErrCode());
            allInOneScanResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            allInOneScanResult.setSuccess(false);
            allInOneScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            allInOneScanResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            allInOneScanResult = Optional.ofNullable(allInOneScanResult)
                                         .orElse(new AllInOneScanResult());
            allInOneScanResult.setSuccess(false);
            allInOneScanResult.setErrCode("500");
            allInOneScanResult.setErrMsg(e.getMessage());
        }
        // 异步扫描需要的信息
        allInOneScanResult.setTaskId(allInOneScan.getTaskId());
        allInOneScanResult.setCompanyId(allInOneScan.getCompanyId());
        BaseUtil.setResult(allInOneScan, allInOneScanResult);
        allInOneScanResult.setMsgId(allInOneScan.getMsgId());
        try {
            MQHelper.sendScheduleMessage(allInOneScanResult);
        } catch (MQException e) {
            log.error(e.getMessage());
            log.error("异步AllInOne回调消息失败", Throwables.getStackTraceAsString(e));
        }

        return allInOneScanResult;

    }

    /**
     * Handle message zone scan result.
     *
     * @param zoneScan the zone scan
     *
     * @return the zone scan result
     */
    public ZoneScanResult handleMessage(ZoneScan zoneScan) {
        log.info("receiving message for scanning zones , virtual type : [{}]", zoneScan.getVirtEnvType());

        log.info("msg id : [{}]", zoneScan.getMsgId());

        ZoneScanResult result = new ZoneScanResult();
        try {
            result = scanHandler.scanZone(zoneScan);
            result.setSuccess(true);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (CloudApiException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (Exception e) {

            result.setSuccess(false);
        }
        BaseUtil.setResult(zoneScan, result);
        return result;
    }

    public VpcPeeringScanResult handleMessage(VpcPeeringScan vpcPeeringScan) {
        log.info("receiving message for scanning zones , virtual type : [{}]", vpcPeeringScan.getVirtEnvType());

        log.info("msg id : [{}]", vpcPeeringScan.getMsgId());

        VpcPeeringScanResult result = new VpcPeeringScanResult();
        try {
            result = scanHandler.scanVpcPeering(vpcPeeringScan);
            result.setSuccess(true);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (CloudApiException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (Exception e) {

            result.setSuccess(false);
        }
        BaseUtil.setResult(vpcPeeringScan, result);
        return result;
    }


    /**
     * Handle message host scan by env result.
     *
     * @param hostScanByEnv the host scan by env
     *
     * @return the host scan by env result
     */
    public HostScanByEnvResult handleMessage(HostScanByEnv hostScanByEnv) {
        log.info("receiving message for scanning hosts , virtual type : [{}]", hostScanByEnv.getVirtEnvType());

        log.info("msg id : [{}]", hostScanByEnv.getMsgId());

        HostScanByEnvResult result = new HostScanByEnvResult();
        try {
            result = scanHandler.scanHostsByEnv(hostScanByEnv);
            result.setSuccess(true);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {

            result.setSuccess(false);
        }
        BaseUtil.setResult(hostScanByEnv, result);
        return result;
    }

    public HostRelateInstScanByEnvResult handleMessage(HostRelateInstScanByEnv hostRelateInstScanByEnv) {
        log.info("receiving message for scanning host relate inst , virtual type : [{}]", hostRelateInstScanByEnv.getVirtEnvType());

        log.info("msg id : [{}]", hostRelateInstScanByEnv.getMsgId());

        HostRelateInstScanByEnvResult result = new HostRelateInstScanByEnvResult();
        try {
            result = scanHandler.scanHostRelateInstByEnv(hostRelateInstScanByEnv);
            result.setSuccess(true);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {

            result.setSuccess(false);
        }
        BaseUtil.setResult(hostRelateInstScanByEnv, result);
        return result;
    }

    /**
     * Handle message network scan result.
     *
     * @param networkScan the network scan
     *
     * @return the network scan result
     */
    public NetworkScanResult handleMessage(NetworkScan networkScan) {

        log.info("receiving message for scanning networks, virtual type : [{}]", networkScan.getVirtEnvType());

        log.info("msg id : [{}]", networkScan.getMsgId());

        NetworkScanResult networkScanResult = new NetworkScanResult();

        try {
            networkScanResult = scanHandler.scanNetwork(networkScan);
            networkScanResult.setSuccess(true);

        } catch (CommonAdapterException e) {

            networkScanResult.setSuccess(false);
            networkScanResult.setErrCode(e.getErrCode());
            networkScanResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            networkScanResult.setSuccess(false);
            networkScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            networkScanResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            networkScanResult.setSuccess(false);
            networkScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            networkScanResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(networkScan, networkScanResult);
        networkScanResult.setMsgId(networkScan.getMsgId());
        return networkScanResult;

    }

    /**
     * Handle message scan vm types by env result.
     *
     * @param scanVmTypesByEnv the scan vm types by env
     *
     * @return the scan vm types by env result
     */
    public ScanVmTypesByEnvResult handleMessage(ScanVmTypesByEnv scanVmTypesByEnv) {

        log.info("receiving message for scanning vmTypes, virtual type : [{}]", scanVmTypesByEnv.getVirtEnvType());

        log.info("msg id : [{}]", scanVmTypesByEnv.getMsgId());

        ScanVmTypesByEnvResult result = new ScanVmTypesByEnvResult();

        try {
            result = scanHandler.scanVmTypesByEnv(scanVmTypesByEnv);
            result.setSuccess(true);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(scanVmTypesByEnv, result);
        result.setMsgId(scanVmTypesByEnv.getMsgId());
        return result;

    }


    /**
     * Handle message scan vm types by env result.
     *
     * @param scanShareTypeByEnv the scan vm types by env
     *
     * @return the scan vm types by env result
     */
    public ScanShareTypeByEnvResult handleMessage(ScanShareTypeByEnv scanShareTypeByEnv) {

        log.info("receiving message for scanning shareTypes, virtual type : [{}]", scanShareTypeByEnv.getVirtEnvType());

        log.info("msg id : [{}]", scanShareTypeByEnv.getMsgId());

        ScanShareTypeByEnvResult result = new ScanShareTypeByEnvResult();

        try {
            result = scanHandler.scanShareTypesByEnv(scanShareTypeByEnv);
            result.setSuccess(true);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(scanShareTypeByEnv, result);
        result.setMsgId(scanShareTypeByEnv.getMsgId());
        return result;

    }

    /**
     * Handle message ssh key scan result.
     *
     * @param sshKeyScan the ssh key scan
     *
     * @return the ssh key scan result
     */
    public SshKeyScanResult handleMessage(SshKeyScan sshKeyScan) {

        log.info("receiving message for scanning sshKey, virtual type : [{}]", sshKeyScan.getVirtEnvType());

        log.info("msg id : [{}]", sshKeyScan.getMsgId());

        SshKeyScanResult result = new SshKeyScanResult();

        try {
            result = scanHandler.scanSshKeyByEnv(sshKeyScan);
            result.setSuccess(true);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(sshKeyScan, result);
        result.setMsgId(sshKeyScan.getMsgId());
        return result;

    }

    public UserBusinessBehaviorScanResult handleMessage(UserBusinessBehaviorScan userBusinessBehaviorScan) {

        log.info("receiving message for scanning sshKey, virtual type : [{}]", userBusinessBehaviorScan.getVirtEnvType());

        log.info("msg id : [{}]", userBusinessBehaviorScan.getMsgId());

        UserBusinessBehaviorScanResult result = new UserBusinessBehaviorScanResult();

        try {
            result = scanHandler.scanUserBusinessBehaviorByEnv(userBusinessBehaviorScan);
            result.setSuccess(true);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(userBusinessBehaviorScan, result);
        result.setMsgId(userBusinessBehaviorScan.getMsgId());
        return result;

    }

    /**
     * Handle message network scan result.
     *
     * @param subnetScan the subnet scan
     *
     * @return the network scan result
     */
    public NetworkScanResult handleMessage(SubnetScan subnetScan) {

        log.info("receiving message for scanning subnet, virtual type : [{}]", subnetScan.getVirtEnvType());

        log.info("msg id : [{}]", subnetScan.getMsgId());

        NetworkScanResult networkScanResult = new NetworkScanResult();

        try {
            networkScanResult = scanHandler.scanSubnetByEnv(subnetScan);
            networkScanResult.setSuccess(true);
        } catch (CommonAdapterException e) {

            networkScanResult.setSuccess(false);
            networkScanResult.setErrCode(e.getErrCode());
            networkScanResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            networkScanResult.setSuccess(false);
            networkScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            networkScanResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            networkScanResult.setSuccess(false);
            networkScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            networkScanResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(subnetScan, networkScanResult);
        networkScanResult.setMsgId(subnetScan.getMsgId());
        return networkScanResult;

    }

    /**
     * Handle message storage type scan by env result.
     *
     * @param storageTypeScan the storage type scan
     *
     * @return the storage type scan by env result
     */
    public StorageTypeScanByEnvResult handleMessage(StorageTypeScan storageTypeScan) {

        log.info("receiving message for scanning storageType, virtual type : [{}]", storageTypeScan.getVirtEnvType());

        log.info("msg id : [{}]", storageTypeScan.getMsgId());

        StorageTypeScanByEnvResult result = new StorageTypeScanByEnvResult();

        try {
            result = scanHandler.scanStorageTypeByEnv(storageTypeScan);
            result.setSuccess(true);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(storageTypeScan, result);
        result.setMsgId(storageTypeScan.getMsgId());
        return result;

    }

    /**
     * Handle message router scan by env result.
     *
     * @param routerScan the router scan
     *
     * @return the router scan by env result
     */
    public RouterScanByEnvResult handleMessage(RouterScan routerScan) {

        log.info("receiving message for scanning router, virtual type : [{}]", routerScan.getVirtEnvType());

        log.info("msg id : [{}]", routerScan.getMsgId());

        RouterScanByEnvResult result = new RouterScanByEnvResult();

        try {
            result = scanHandler.scanRouterByEnv(routerScan);
            result.setSuccess(true);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(routerScan, result);
        result.setMsgId(routerScan.getMsgId());
        return result;

    }

    /**
     * Handle message router route scan by env result.
     *
     * @param routerRouteScan the router route scan
     *
     * @return the router route scan by env result
     */
    public RouterRouteScanByEnvResult handleMessage(RouterRouteScan routerRouteScan) {

        log.info("receiving message for scanning routerRoute , virtual type : [{}]", routerRouteScan.getVirtEnvType());

        log.info("msg id : [{}]", routerRouteScan.getMsgId());

        RouterRouteScanByEnvResult result = new RouterRouteScanByEnvResult();

        try {
            result = scanHandler.scanRouterRouteByEnv(routerRouteScan);
            result.setSuccess(true);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(routerRouteScan, result);
        result.setMsgId(routerRouteScan.getMsgId());
        return result;

    }

    /**
     * Handle message router interface scan by env result.
     *
     * @param routerInterfaceScan the router interface scan
     *
     * @return the router interface scan by env result
     */
    public RouterInterfaceScanByEnvResult handleMessage(RouterInterfaceScan routerInterfaceScan) {

        log.info("receiving message for scanning routerInterface, virtual type : [{}]", routerInterfaceScan.getVirtEnvType());

        log.info("msg id : [{}]", routerInterfaceScan.getMsgId());

        RouterInterfaceScanByEnvResult result = new RouterInterfaceScanByEnvResult();

        try {
            result = scanHandler.scanRouterInterfaceByEnv(routerInterfaceScan);
            result.setSuccess(true);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(routerInterfaceScan, result);
        result.setMsgId(routerInterfaceScan.getMsgId());
        return result;

    }

    /**
     * Handle message port scan result.
     *
     * @param portScan the port scan
     *
     * @return the port scan result
     */
    public PortScanResult handleMessage(PortScan portScan) {

        log.info("receiving message for scanning port, virtual type : [{}]", portScan.getVirtEnvType());

        log.info("msg id : [{}]", portScan.getMsgId());

        PortScanResult result = new PortScanResult();

        try {
            result = scanHandler.scanPortByEnv(portScan);
            result.setSuccess(true);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(portScan, result);
        result.setMsgId(portScan.getMsgId());
        return result;

    }

    /**
     * Handle message security group scan result.
     *
     * @param securityGroupScan the security group scan
     *
     * @return the security group scan result
     */
    public SecurityGroupScanResult handleMessage(SecurityGroupScan securityGroupScan) {

        log.info("receiving message for scanning securityGroups, virtual type : [{}]", securityGroupScan.getVirtEnvType());

        log.info("msg id : [{}]", securityGroupScan.getMsgId());

        SecurityGroupScanResult result = new SecurityGroupScanResult();

        try {
            result = scanHandler.scanSecurityGroupByEnv(securityGroupScan);
            result.setSuccess(true);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(securityGroupScan, result);
        result.setMsgId(securityGroupScan.getMsgId());
        return result;

    }

    /**
     * Handle message load balance scan result.
     *
     * @param loadBalanceScan the load balance scan
     *
     * @return the load balance scan result
     */
    public LoadBalanceScanResult handleMessage(LoadBalanceScan loadBalanceScan) {

        log.info("receiving message for scanning loadBalance, virtual type : [{}]", loadBalanceScan.getVirtEnvType());

        log.info("msg id : [{}]", loadBalanceScan.getMsgId());

        LoadBalanceScanResult result = new LoadBalanceScanResult();

        try {
            result = scanHandler.scanLoadBalanceByEnv(loadBalanceScan);
            result.setSuccess(true);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(loadBalanceScan, result);
        result.setMsgId(loadBalanceScan.getMsgId());
        return result;

    }

    public LbListenerScanResult handleMessage(LbListenerScan lbListenerScan) {
        log.info("receiving message for scanning loadBalance listener, virtual type : [{}]", lbListenerScan.getVirtEnvType());

        log.info("msg id : [{}]", lbListenerScan.getMsgId());
        LbListenerScanResult result = new LbListenerScanResult();

        try {
            result = scanHandler.scanLbListenerByEnv(lbListenerScan);
            result.setSuccess(true);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(lbListenerScan, result);
        result.setMsgId(lbListenerScan.getMsgId());
        return result;
    }

    public LbPoolsScanResult handleMessage(LbPoolsScan lbPoolsScan) {
        log.info("receiving message for scanning loadBalance listener, virtual type : [{}]", lbPoolsScan.getVirtEnvType());

        log.info("msg id : [{}]", lbPoolsScan.getMsgId());
        LbPoolsScanResult result = new LbPoolsScanResult();

        try {
            result = scanHandler.scanLbPoolsByEnv(lbPoolsScan);
            result.setSuccess(true);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(lbPoolsScan, result);
        result.setMsgId(lbPoolsScan.getMsgId());
        return result;
    }

    /**
     * Handle message scan floating ip by env result.
     *
     * @param scanFloatingIpsByEnv the scan floating ips by env
     *
     * @return the scan floating ip by env result
     */
    public ScanFloatingIpByEnvResult handleMessage(ScanFloatingIpsByEnv scanFloatingIpsByEnv) {

        log.info(
                "receiving message for scanning FloatingIp, virtual type : [{}]", scanFloatingIpsByEnv.getVirtEnvType());

        log.info("msg id : [{}]", scanFloatingIpsByEnv.getMsgId());

        ScanFloatingIpByEnvResult result = new ScanFloatingIpByEnvResult();

        try {
            result = scanHandler.scanFloatingIpsByEnv(scanFloatingIpsByEnv);
            result.setSuccess(true);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(scanFloatingIpsByEnv, result);
        result.setMsgId(scanFloatingIpsByEnv.getMsgId());
        return result;

    }

    /**
     * Handle message disk scan result.
     *
     * @param scanVdsByEnv the scan vds by env
     *
     * @return the disk scan result
     */
    public DiskScanResult handleMessage(ScanVdsByEnv scanVdsByEnv) {

        log.info("receiving message for scanning vd, virtual type : [{}]", scanVdsByEnv.getVirtEnvType());

        log.info("msg id : [{}]", scanVdsByEnv.getMsgId());

        DiskScanResult result = new DiskScanResult();

        try {
            result = scanHandler.scanVdsByEnv(scanVdsByEnv);
            result.setSuccess(true);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(scanVdsByEnv, result);
        result.setMsgId(scanVdsByEnv.getMsgId());
        return result;

    }

    /**
     * Handle message diskbackup scan result.
     *
     * @param scanVdBackupsByEnv the scan vdbuckups by env
     *
     * @return the disk scan result
     */
    public DiskScanResult handleMessage(ScanVdBackupsByEnv scanVdBackupsByEnv) {

        log.info("receiving message for scanning vdbackup----virtual type : " + scanVdBackupsByEnv.getVirtEnvType());

        log.info("msg id : " + scanVdBackupsByEnv.getMsgId());

        DiskScanResult result = new DiskScanResult();

        try {
            result = scanHandler.scanVdBackupsByEnv(scanVdBackupsByEnv);
            result.setSuccess(true);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(scanVdBackupsByEnv, result);
        result.setMsgId(scanVdBackupsByEnv.getMsgId());
        return result;

    }

    /**
     * Handle message db instance ip array scan result.
     *
     * @param dbInstanceIpArrayScan the db instance ip array scan
     *
     * @return the db instance ip array scan result
     */
    public DBInstanceIpArrayScanResult handleMessage(DBInstanceIpArrayScan dbInstanceIpArrayScan) {
        log.info("receiving message for scanning dbInstances , virtual type : [{}]", dbInstanceIpArrayScan.getVirtEnvType());

        log.info("msg id : [{}]", dbInstanceIpArrayScan.getMsgId());

        DBInstanceIpArrayScanResult result = new DBInstanceIpArrayScanResult();
        try {
            result = scanHandler.scanDBInstanceIpArrayByEnv(dbInstanceIpArrayScan);
            result.setSuccess(true);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {

            result.setSuccess(false);
            result.setErrCode("500");
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(dbInstanceIpArrayScan, result);
        return result;
    }

    /**
     * Handle message db instance account scan result.
     *
     * @param dbInstanceAccountScan the db instance account scan
     *
     * @return the db instance account scan result
     */
    public DBInstanceAccountScanResult handleMessage(DBInstanceAccountScan dbInstanceAccountScan) {
        log.info("receiving message for scanning dbInstanceAccounts , virtual type : [{}]", dbInstanceAccountScan.getVirtEnvType());

        log.info("msg id : [{}]", dbInstanceAccountScan.getMsgId());

        DBInstanceAccountScanResult result = new DBInstanceAccountScanResult();

        try {
            result = scanHandler.scanDBInstanceAccountByEnv(dbInstanceAccountScan);
            result.setSuccess(true);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {

            result.setSuccess(false);
            result.setErrCode("500");
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(dbInstanceAccountScan, result);
        return result;
    }

    /**
     * Handle message db instance scan result.
     *
     * @param dbInstanceScan the db instance scan
     *
     * @return the db instance scan result
     */
    public DBInstanceScanResult handleMessage(DBInstanceScan dbInstanceScan) {
        log.info("receiving message for scanning dbInstances , virtual type : [{}]", dbInstanceScan.getVirtEnvType());

        log.info("msg id : [{}]", dbInstanceScan.getMsgId());

        DBInstanceScanResult result = new DBInstanceScanResult();
        try {
            result = scanHandler.scanDBInstanceByEnv(dbInstanceScan);
            result.setSuccess(true);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {

            result.setSuccess(false);
            result.setErrCode("500");
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(dbInstanceScan, result);
        return result;
    }

    /**
     * Handle message vm vnc console result.
     *
     * @param vmVncConsole the vm vnc console
     *
     * @return the vm vnc console result
     */
    public VmVncConsoleResult handleMessage(VmVncConsole vmVncConsole) {

        log.info("receiving message for getting vmVncConsole, virtual type : [{}]", vmVncConsole.getVirtEnvType());

        log.info("msg id : [{}]", vmVncConsole.getMsgId());

        VmVncConsoleResult vmVncConsoleResult = new VmVncConsoleResult();

        try {
            vmVncConsoleResult = vmHandler.getConsoleUrl(vmVncConsole);
            vmVncConsoleResult.setSuccess(true);
        } catch (CommonAdapterException e) {
            vmVncConsoleResult.setErrCode(e.getErrCode());
            vmVncConsoleResult.setErrMsg(e.getErrMsg());
            vmVncConsoleResult.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            vmVncConsoleResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmVncConsoleResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
            vmVncConsoleResult.setSuccess(false);
        } catch (Exception e) {
            vmVncConsoleResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmVncConsoleResult.setErrMsg(e.getMessage());
            vmVncConsoleResult.setSuccess(false);
        }
        BaseUtil.setResult(vmVncConsole, vmVncConsoleResult);
        vmVncConsoleResult.setMsgId(vmVncConsole.getMsgId());
        return vmVncConsoleResult;
    }

    /**
     * Handle message floating ip attach result.
     *
     * @param floatingIpAttach the floating ip attach
     *
     * @return the floating ip attach result
     */
    public FloatingIpAttachResult handleMessage(FloatingIpAttach floatingIpAttach) {

        log.info("receiving message for attaching floatingIp, virtual type : [{}]", floatingIpAttach.getVirtEnvType());

        log.info("msg id : [{}]", floatingIpAttach.getMsgId());

        FloatingIpAttachResult result = new FloatingIpAttachResult();
        try {
            result = netHandler.attachFloatingIp(floatingIpAttach);
            result.setSuccess(true);
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);
        } catch (Exception e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(floatingIpAttach, result);
        result.setMsgId(floatingIpAttach.getMsgId());
        result.setFixedIp(floatingIpAttach.getFixedIp());
        result.setFloatingIpId(floatingIpAttach.getFloatingIpId());
        result.setFixedIpId(floatingIpAttach.getFixedIpId());
        result.setFloatingIp(floatingIpAttach.getFloatingIp());
        result.setServerId(floatingIpAttach.getServerId());
        return result;
    }

    /**
     * Handle message floating ip detach result.
     *
     * @param floatingIpDetach the floating ip detach
     *
     * @return the floating ip detach result
     */
    public FloatingIpDetachResult handleMessage(FloatingIpDetach floatingIpDetach) {

        log.info("receiving message for detaching floatingIp, virtual type : [{}]", floatingIpDetach.getVirtEnvType());

        log.info("msg id : [{}]", floatingIpDetach.getMsgId());

        FloatingIpDetachResult result = new FloatingIpDetachResult();

        try {
            result = netHandler.detachFloatingIp(floatingIpDetach);
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);
        } catch (Exception e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(floatingIpDetach, result);
        result.setMsgId(floatingIpDetach.getMsgId());
        result.setFloatingIp(floatingIpDetach.getFloatingIp());
        result.setServerId(floatingIpDetach.getServerId());
        result.setFloatingIpId(floatingIpDetach.getFloatingIpId());
        return result;
    }

    /**
     * Handle message sg create result.
     *
     * @param sgCreate the sg create
     *
     * @return the sg create result
     */
    public SgCreateResult handleMessage(SgCreate sgCreate) {

        log.info("receiving message for creating securityGroup, virtual type : [{}] network name : [{}]",sgCreate.getVirtEnvType(), sgCreate.getName());

        log.info("msg id : [{}]", sgCreate.getMsgId());
        SgCreateResult sgCreateResult = new SgCreateResult();

        try {
            sgCreateResult = netHandler.createSecurityGroup(sgCreate);
        } catch (CommonAdapterException e) {
            sgCreateResult.setSuccess(false);
            sgCreateResult.setErrCode(e.getErrCode());
            sgCreateResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            sgCreateResult.setSuccess(false);
            sgCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            sgCreateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {

            sgCreateResult.setErrMsg(e.getMessage());
            sgCreateResult.setSuccess(false);
        }

        BaseUtil.setResult(sgCreate, sgCreateResult);
        sgCreateResult.setMsgId(sgCreate.getMsgId());
        sgCreateResult.setTenantId(sgCreate.getTenantId());
        sgCreateResult.setName(sgCreate.getName());
        sgCreateResult.setResId(sgCreate.getResId());
        return sgCreateResult;
    }

    /**
     * Handle message sg update result.
     *
     * @param sgUpdate the sg update
     *
     * @return the sg update result
     */
    public SgUpdateResult handleMessage(SgUpdate sgUpdate) {

        log.info("receiving message for update securityGroup, virtual type : [{}] network name : [{}]",sgUpdate.getVirtEnvType() , sgUpdate.getName());

        log.info("msg id : [{}]", sgUpdate.getMsgId());
        SgUpdateResult sgUpdateResult = new SgUpdateResult();
        try {
            sgUpdateResult = netHandler.updateSecurityGroup(sgUpdate);
        } catch (CommonAdapterException e) {
            sgUpdateResult.setSuccess(false);
            sgUpdateResult.setErrCode(e.getErrCode());
            sgUpdateResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            sgUpdateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            sgUpdateResult.setSuccess(false);
            sgUpdateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {

            sgUpdateResult.setErrMsg(e.getMessage());
            sgUpdateResult.setSuccess(false);
        }
        BaseUtil.setResult(sgUpdate, sgUpdateResult);
        sgUpdateResult.setMsgId(sgUpdate.getMsgId());
        sgUpdateResult.setTenantId(sgUpdate.getTenantId());
        sgUpdateResult.setName(sgUpdate.getName());
        sgUpdateResult.setResId(sgUpdate.getResId());
        return sgUpdateResult;
    }

    /**
     * Handle message sg delete result.
     *
     * @param sgDelete the sg delete
     *
     * @return the sg delete result
     */
    public SgDeleteResult handleMessage(SgDelete sgDelete) {

        log.info("receiving message for deleting securityGroup, virtual type : [{}]", sgDelete.getVirtEnvType());

        log.info("msg id : [{}]", sgDelete.getMsgId());
        SgDeleteResult sgDeleteResult = new SgDeleteResult();
        try {
            sgDeleteResult = netHandler.deleteSecurityGroup(sgDelete);
        } catch (CommonAdapterException e) {

            sgDeleteResult.setSuccess(false);
            sgDeleteResult.setErrCode(e.getErrCode());
            sgDeleteResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {
            sgDeleteResult.setSuccess(false);
            sgDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            sgDeleteResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            sgDeleteResult.setErrMsg(e.getMessage());

            sgDeleteResult.setSuccess(false);
        }
        BaseUtil.setResult(sgDelete, sgDeleteResult);
        sgDeleteResult.setMsgId(sgDelete.getMsgId());
        sgDeleteResult.setResId(sgDelete.getResId());
        return sgDeleteResult;
    }

    /**
     * Handle message sg rule create result.
     *
     * @param sgRuleCreate the sg rule create
     *
     * @return the sg rule create result
     */
    public SgRuleCreateResult handleMessage(SgRuleCreate sgRuleCreate) {
        log.info("receiving message for creating securitygrouprule, virtual type : [{}]", sgRuleCreate.getVirtEnvType());

        log.info("msg id : [{}]", sgRuleCreate.getMsgId());
        SgRuleCreateResult sgRuleCreateResult = new SgRuleCreateResult();
        try {
            sgRuleCreateResult = netHandler.createSgRule(sgRuleCreate);
        } catch (CommonAdapterException e) {

            sgRuleCreateResult.setErrCode(e.getErrCode());
            sgRuleCreateResult.setErrMsg(e.getErrMsg());
            sgRuleCreateResult.setSuccess(false);
        } catch (AdapterUnavailableException e) {

            sgRuleCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            sgRuleCreateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
            sgRuleCreateResult.setSuccess(false);
        } catch (Exception e) {
            sgRuleCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            sgRuleCreateResult.setErrMsg(e.getMessage());
            sgRuleCreateResult.setSuccess(false);
        }
        BaseUtil.setResult(sgRuleCreate, sgRuleCreateResult);
        sgRuleCreateResult.setMsgId(sgRuleCreate.getMsgId());
        if (sgRuleCreateResult.isSuccess()) {
            log.info("[adapter] create securityGroupRule successfully");
        } else {
            log.info("[adapter] create securityGroupRule failure");
        }
        return sgRuleCreateResult;
    }

    /**
     * 修改安全组规则描述
     *
     * @param sgRuleUpdate the sg rule update
     *
     * @return the base result
     */
    public BaseResult handleMessage(SgRuleUpdate sgRuleUpdate) {
        log.info("receiving message for updating securitygrouprule, virtual type : [{}]", sgRuleUpdate.getVirtEnvType());

        log.info("msg id : [{}]", sgRuleUpdate.getMsgId());
        BaseResult result = new BaseResult();
        try {
            result = netHandler.updateSgRule(sgRuleUpdate);
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);
        } catch (Exception e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(sgRuleUpdate, result);
        result.setMsgId(sgRuleUpdate.getMsgId());
        if (result.isSuccess()) {
            log.info("[adapter] update securityGroupRule successfully");
        } else {
            log.warn("[adapter] update securityGroupRule failure");
        }
        return result;
    }

    /**
     * Handle message sg rule delete result.
     *
     * @param sgRuleDelete the sg rule delete
     *
     * @return the sg rule delete result
     */
    public SgRuleDeleteResult handleMessage(SgRuleDelete sgRuleDelete) {
        log.info("receiving message for delete securityGroupRule, virtual type : [{}]securityGroup id : [{}]",sgRuleDelete.getVirtEnvType(), sgRuleDelete.getSgRuleId());

        log.info("msg id : [{}]", sgRuleDelete.getMsgId());

        SgRuleDeleteResult sgRuleDeleteResult = new SgRuleDeleteResult();

        try {
            sgRuleDeleteResult = netHandler.deleteSgRule(sgRuleDelete);

        } catch (CommonAdapterException e) {
            sgRuleDeleteResult.setErrCode(e.getErrCode());
            sgRuleDeleteResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {

            sgRuleDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            sgRuleDeleteResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            sgRuleDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            sgRuleDeleteResult.setErrMsg(e.getMessage());
            sgRuleDeleteResult.setSuccess(false);
        }
        BaseUtil.setResult(sgRuleDelete, sgRuleDeleteResult);
        sgRuleDeleteResult.setSgRuleId(sgRuleDelete.getSgRuleId());
        sgRuleDeleteResult.setMsgId(sgRuleDelete.getMsgId());
        if (sgRuleDeleteResult.isSuccess()) {
            log.info("adapter securityGroup id: [{}] delete successfully",sgRuleDelete.getSgRuleId());
        } else {
            log.info("adapter securityGroup id: [{}] delete failure",sgRuleDelete.getSgRuleId());
        }
        return sgRuleDeleteResult;
    }

    /**
     * Handle message sg rule list query result.
     *
     * @param sgRuleListQuery the sg rule list query
     *
     * @return the sg rule list query result
     */
    public SgRuleListQueryResult handleMessage(SgRuleListQuery sgRuleListQuery) {
        log.info("receiving message for query securityGroupRule, virtual type : [{}] securityGroup id : [{}]",sgRuleListQuery.getVirtEnvType(), sgRuleListQuery.getSgId());

        log.info("msg id : [{}]", sgRuleListQuery.getMsgId());

        SgRuleListQueryResult sgRuleListQueryResult = new SgRuleListQueryResult();
        try {
            sgRuleListQueryResult = netHandler.querySgRuleList(sgRuleListQuery);
            sgRuleListQueryResult.setSuccess(true);
        } catch (CommonAdapterException e) {
            sgRuleListQueryResult.setSuccess(false);
            sgRuleListQueryResult.setErrCode(e.getErrCode());
            sgRuleListQueryResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            sgRuleListQueryResult.setSuccess(false);
            sgRuleListQueryResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            sgRuleListQueryResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            sgRuleListQueryResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            sgRuleListQueryResult.setErrMsg(e.getMessage());
            sgRuleListQueryResult.setSuccess(false);
        }

        BaseUtil.setResult(sgRuleListQuery, sgRuleListQueryResult);
        sgRuleListQueryResult.setMsgId(sgRuleListQuery.getMsgId());
        return sgRuleListQueryResult;
    }

    /**
     * Handle message vm query sgs result.
     *
     * @param vmQuerySgs the vm query sgs
     *
     * @return the vm query sgs result
     */
    public VmQuerySgsResult handleMessage(VmQuerySgs vmQuerySgs) {
        log.info("receiving message for query vmSecurityGroups, virtual type : [{}] server id : [{}]",vmQuerySgs.getVirtEnvType(), vmQuerySgs.getServerId());

        log.info("msg id : [{}]", vmQuerySgs.getMsgId());
        VmQuerySgsResult vmQuerySgsResult = new VmQuerySgsResult();

        try {
            vmQuerySgsResult = vmHandler.queryVmSecurityGroups(vmQuerySgs);
        } catch (CommonAdapterException e) {
            vmQuerySgsResult.setSuccess(false);
            vmQuerySgsResult.setErrCode(e.getErrCode());
            vmQuerySgsResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            vmQuerySgsResult.setSuccess(false);
            vmQuerySgsResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmQuerySgsResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            vmQuerySgsResult.setSuccess(false);
            vmQuerySgsResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmQuerySgsResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(vmQuerySgs, vmQuerySgsResult);
        return vmQuerySgsResult;
    }

    /**
     * Handle message security group query result.
     *
     * @param securityGroupQuery the security group query
     *
     * @return the security group query result
     */
    public SecurityGroupQueryResult handleMessage(SecurityGroupQuery securityGroupQuery) {
        log.info("receiving message for query vmSecurityGroups, virtual type : [{}]", securityGroupQuery.getVirtEnvType());

        log.info("msg id : [{}]", securityGroupQuery.getMsgId());
        SecurityGroupQueryResult securityGroupQueryResult = new SecurityGroupQueryResult();
        try {
            securityGroupQueryResult = vmHandler.querySecurityGroup(securityGroupQuery);
        } catch (CommonAdapterException e) {
            securityGroupQueryResult.setSuccess(false);
            securityGroupQueryResult.setErrCode(e.getErrCode());
            securityGroupQueryResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            securityGroupQueryResult.setSuccess(false);
            securityGroupQueryResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            securityGroupQueryResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            securityGroupQueryResult.setSuccess(false);
            securityGroupQueryResult.setErrCode("500");
            securityGroupQueryResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(securityGroupQuery, securityGroupQueryResult);
        return securityGroupQueryResult;
    }

    /**
     * Handle message server security group add result.
     *
     * @param securityGroupAdd the security group add
     *
     * @return the server security group add result
     */
    public ServerSecurityGroupAddResult handleMessage(ServerSecurityGroupAdd securityGroupAdd) {

        log.info("receiving message for adding security group, virtual type : [{}] securityGroup name :[{}]",securityGroupAdd.getVirtEnvType(), securityGroupAdd.getSgName());

        log.info("msg id : [{}]", securityGroupAdd.getMsgId());

        ServerSecurityGroupAddResult securityGroupAddResult = new ServerSecurityGroupAddResult();

        try {
            securityGroupAddResult = vmHandler.addSecurityGroup(securityGroupAdd);
        } catch (CommonAdapterException e) {

            securityGroupAddResult.setSuccess(false);
            securityGroupAddResult.setErrCode(e.getErrCode());
            securityGroupAddResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            securityGroupAddResult.setSuccess(false);
            securityGroupAddResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            securityGroupAddResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            securityGroupAddResult.setSuccess(false);
            securityGroupAddResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            securityGroupAddResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(securityGroupAdd, securityGroupAddResult);
        securityGroupAddResult.setMsgId(securityGroupAdd.getMsgId());
        securityGroupAddResult.setServerId(securityGroupAdd.getServerId());
        securityGroupAddResult.setSgName(securityGroupAdd.getSgName());
        securityGroupAddResult.setResId(securityGroupAdd.getResId());
        return securityGroupAddResult;
    }

    /**
     * Handle message server security group delete result.
     *
     * @param securityGroupDelete the security group delete
     *
     * @return the server security group delete result
     */
    public ServerSecurityGroupDeleteResult handleMessage(ServerSecurityGroupDelete securityGroupDelete) {

        log.info("receiving message for adding security group, virtual type : [{}] securityGroup name :[{}]"
                         ,securityGroupDelete.getVirtEnvType(), securityGroupDelete.getName());

        log.info("msg id : [{}]", securityGroupDelete.getMsgId());

        ServerSecurityGroupDeleteResult securityGroupDeleteResult = new ServerSecurityGroupDeleteResult();

        try {
            securityGroupDeleteResult = vmHandler.deleteSecurityGroup(securityGroupDelete);
            securityGroupDeleteResult.setSuccess(true);
        } catch (CommonAdapterException e) {
            securityGroupDeleteResult.setSuccess(false);
            securityGroupDeleteResult.setErrCode(e.getErrCode());
            securityGroupDeleteResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {
            securityGroupDeleteResult.setSuccess(false);
            securityGroupDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            securityGroupDeleteResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            securityGroupDeleteResult.setSuccess(false);
            securityGroupDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            securityGroupDeleteResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(securityGroupDelete, securityGroupDeleteResult);
        securityGroupDeleteResult.setMsgId(securityGroupDelete.getMsgId());
        return securityGroupDeleteResult;
    }

    /**
     * Handle message user modify result.
     *
     * @param userModify the user modify
     *
     * @return the user modify result
     */
    public UserModifyResult handleMessage(UserModify userModify) {
        log.info("receiving message for change user password, virtual type : [{}] userId: [{}]",userModify.getVirtEnvType(), userModify.getUserId());

        log.info("msg id : [{}]", userModify.getMsgId());

        UserModifyResult userModifyResult = new UserModifyResult();
        try {
            userModifyResult = adminHandler.changeUserPwd(userModify);
        } catch (CommonAdapterException e) {
            userModifyResult.setSuccess(false);
            userModifyResult.setErrCode(e.getErrCode());
            userModifyResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            userModifyResult.setSuccess(false);
            userModifyResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            userModifyResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            userModifyResult.setSuccess(false);
            userModifyResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            userModifyResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(userModify, userModifyResult);
        return userModifyResult;
    }

    /**
     * Handle message remove user from tenant result.
     *
     * @param removeUserFromTenant the remove user from tenant
     *
     * @return the remove user from tenant result
     */
    public RemoveUserFromTenantResult handleMessage(RemoveUserFromTenant removeUserFromTenant) {
        log.info("receiving message for removing user form tenant, virtual type : [{}]", removeUserFromTenant.getVirtEnvType());

        log.info("msg id : [{}]", removeUserFromTenant.getMsgId());
        RemoveUserFromTenantResult result = new RemoveUserFromTenantResult();
        try {
            result = adminHandler.removeUserFromTenant(removeUserFromTenant);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(removeUserFromTenant, result);
        return result;
    }

    /**
     * Handle message add user to tenant result.
     *
     * @param addUserToTenant the add user to tenant
     *
     * @return the add user to tenant result
     */
    public AddUserToTenantResult handleMessage(AddUserToTenant addUserToTenant) {
        log.info("receiving message for adding user to tenant, virtual type : [{}]", addUserToTenant.getVirtEnvType());

        log.info("msg id : [{}]", addUserToTenant.getMsgId());
        AddUserToTenantResult result = new AddUserToTenantResult();
        try {
            result = adminHandler.addUserToTenant(addUserToTenant);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(addUserToTenant, result);
        return result;
    }

    /**
     * Handle message keypair create result.
     *
     * @param keypairCreate the keypair create
     *
     * @return the keypair create result
     */
    public KeypairCreateResult handleMessage(KeypairCreate keypairCreate) {
        log.info(
                "receiving message for creating keyPair, virtual type : [{}]", keypairCreate.getVirtEnvType());

        log.info("msg id : [{}]", keypairCreate.getMsgId());

        KeypairCreateResult result = new KeypairCreateResult();
        try {
            result = adminHandler.createKeyPair(keypairCreate);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(keypairCreate, result);
        return result;
    }

    /**
     * Handle message keypair get result.
     *
     * @param keypairGet the keypair get
     *
     * @return the keypair get result
     */
    public KeypairGetResult handleMessage(KeypairGet keypairGet) {
        log.info(
                "receiving message for getting keyPairInfo, virtual type : [{}]", keypairGet.getVirtEnvType());

        log.info("msg id : [{}]", keypairGet.getMsgId());
        KeypairGetResult result = new KeypairGetResult();
        try {
            result = adminHandler.getKeyPairInfo(keypairGet);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(keypairGet, result);
        return result;
    }

    /**
     * Handle message keypair list get result.
     *
     * @param keypairListGet the keypair list get
     *
     * @return the keypair list get result
     */
    public KeypairListGetResult handleMessage(KeypairListGet keypairListGet) {
        log.info("receiving message for getting keyPairList, virtual type : [{}]", keypairListGet.getVirtEnvType());

        log.info("msg id : [{}]", keypairListGet.getMsgId());
        KeypairListGetResult result = new KeypairListGetResult();
        try {
            result = adminHandler.getKeyPairList(keypairListGet);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(keypairListGet, result);
        return result;
    }

    /**
     * Handle message keypair delete result.
     *
     * @param keypairDelete the keypair delete
     *
     * @return the keypair delete result
     */
    public KeypairDeleteResult handleMessage(KeypairDelete keypairDelete) {
        log.info("receiving message for getting keyPairList, virtual type : [{}]", keypairDelete.getVirtEnvType());

        log.info("msg id : [{}]", keypairDelete.getMsgId());
        KeypairDeleteResult result = new KeypairDeleteResult();
        try {
            result = adminHandler.deleteKeypair(keypairDelete);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(keypairDelete, result);
        return result;
    }

    /**
     * Handle message base result.
     *
     * @param routerDelete the router delete
     *
     * @return the base result
     */
    public BaseResult handleMessage(RouterDelete routerDelete) {
        log.info("receiving message for delete router, virtual type : [{}]", routerDelete.getVirtEnvType());

        log.info("msg id : [{}]", routerDelete.getMsgId());
        RouterResult result = new RouterResult();
        try {
            result = netHandler.deleteRouter(routerDelete);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(routerDelete, result);
        result.setMsgId(routerDelete.getMsgId());
        return result;
    }

    /**
     * Handle message base result.
     *
     * @param routerUpdate the router update
     *
     * @return the base result
     */
    public BaseResult handleMessage(RouterUpdate routerUpdate) {
        log.info("receiving message for update router, virtual type : [{}]", routerUpdate.getVirtEnvType());

        log.info("msg id : [{}]", routerUpdate.getMsgId());
        RouterResult result = new RouterResult();
        try {
            result = netHandler.updateRouter(routerUpdate);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(routerUpdate, result);
        result.setMsgId(routerUpdate.getMsgId());
        return result;
    }

    /**
     * Handle message base result.
     *
     * @param routerAddInterface the router add interface
     *
     * @return the base result
     */
    public BaseResult handleMessage(RouterAddInterface routerAddInterface) {
        log.info("receiving message for create routerInterface, virtual type : [{}]", routerAddInterface.getVirtEnvType());

        log.info("msg id : [{}]", routerAddInterface.getMsgId());
        RouterResult result = new RouterResult();
        try {
            result = netHandler.addRouterInterface(routerAddInterface);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(routerAddInterface, result);
        result.setMsgId(routerAddInterface.getMsgId());
        return result;
    }

    /**
     * Handle message base result.
     *
     * @param routerDeleteEntry the router delete entry
     *
     * @return the base result
     */
    public BaseResult handleMessage(RouterDeleteEntry routerDeleteEntry) {
        log.info("receiving message for delete routerEntry, virtual type : [{}]", routerDeleteEntry.getVirtEnvType());

        log.info("msg id : [{}]", routerDeleteEntry.getMsgId());
        RouterResult result = new RouterResult();
        try {
            result = netHandler.removeRouterEntry(routerDeleteEntry);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (CloudApiException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getErrMsg());
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(routerDeleteEntry, result);
        result.setMsgId(routerDeleteEntry.getMsgId());
        return result;
    }

    /**
     * Handle message base result.
     *
     * @param routerUpdateEntry the router delete entry
     *
     * @return the base result
     */
    public BaseResult handleMessage(RouterUpdateEntry routerUpdateEntry) {
        log.info("receiving message for update routerEntry, virtual type : [{}]", routerUpdateEntry.getVirtEnvType());

        log.info("msg id : [{}]", routerUpdateEntry.getMsgId());
        RouterResult result = new RouterResult();
        try {
            result = netHandler.updateRouterEntry(routerUpdateEntry);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (CloudApiException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getErrMsg());
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(routerUpdateEntry, result);
        result.setMsgId(routerUpdateEntry.getMsgId());
        return result;
    }

    /**
     * Handle message base result.
     *
     * @param routerAddExternalGateway the router add external gateway
     *
     * @return the base result
     */
    public BaseResult handleMessage(RouterAddExternalGateway routerAddExternalGateway) {
        log.info("receiving message for updating router, virtual type : [{}]", routerAddExternalGateway.getVirtEnvType());

        log.info("msg id : [{}]", routerAddExternalGateway.getMsgId());
        RouterResult result = new RouterResult();
        try {
            result = netHandler.addRouterExternalGateway(routerAddExternalGateway);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(e.getMessage());
        }
        BaseUtil.setResult(routerAddExternalGateway, result);
        result.setMsgId(routerAddExternalGateway.getMsgId());
        return result;
    }

    /**
     * Handle message base result.
     *
     * @param routerRemoveExternalGateway the router remove external gateway
     *
     * @return the base result
     */
    public BaseResult handleMessage(RouterRemoveExternalGateway routerRemoveExternalGateway) {
        log.info("receiving message for updating router, virtual type : [{}]", routerRemoveExternalGateway.getVirtEnvType());

        log.info("msg id : [{}]", routerRemoveExternalGateway.getMsgId());
        RouterResult result = new RouterResult();
        try {
            result = netHandler.removeExternalGateway(routerRemoveExternalGateway);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(e.getMessage());
        }
        BaseUtil.setResult(routerRemoveExternalGateway, result);
        result.setMsgId(routerRemoveExternalGateway.getMsgId());
        return result;
    }

    /**
     * Handle message base result.
     *
     * @param routerRemoveInterface the router remove interface
     *
     * @return the base result
     */
    public BaseResult handleMessage(RouterRemoveInterface routerRemoveInterface) {
        log.info("receiving message for delete routerInterface, virtual type : [{}]", routerRemoveInterface.getVirtEnvType());

        log.info("msg id : [{}]", routerRemoveInterface.getMsgId());
        RouterResult result = new RouterResult();
        try {
            result = netHandler.removeRouterInterface(routerRemoveInterface);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(routerRemoveInterface, result);
        result.setMsgId(routerRemoveInterface.getMsgId());
        return result;
    }

    /**
     * Handle message base result.
     *
     * @param ipArrayUpdate the ip array update
     *
     * @return the base result
     */
    public BaseResult handleMessage(IpArrayUpdate ipArrayUpdate) {
        log.info("receiving message for update IpArrayList, virtual type : [{}]", ipArrayUpdate.getVirtEnvType());

        log.info("msg id : [{}]", ipArrayUpdate.getMsgId());
        IpArrayResult result = new IpArrayResult();
        try {
            result = rdsHandler.updateIpArray(ipArrayUpdate);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(ipArrayUpdate, result);
        result.setMsgId(ipArrayUpdate.getMsgId());
        return result;
    }

    /**
     * Handle message base result.
     *
     * @param accountCreate the account create
     *
     * @return the base result
     */
    public BaseResult handleMessage(AccountCreate accountCreate) {
        log.info("receiving message for create account, virtual type : [{}]", accountCreate.getVirtEnvType());

        log.info("msg id : [{}]", accountCreate.getMsgId());
        AccountResult result = new AccountResult();

        try {
            result = rdsHandler.createAccount(accountCreate);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(accountCreate, result);
        result.setMsgId(accountCreate.getMsgId());
        return result;
    }

    /**
     * Handle message base result.
     *
     * @param accountUpdate the account update
     *
     * @return the base result
     */
    public BaseResult handleMessage(AccountUpdate accountUpdate) {
        log.info("receiving message for update account, virtual type : [{}]", accountUpdate.getVirtEnvType());

        log.info("msg id : [{}]", accountUpdate.getMsgId());
        AccountResult result = new AccountResult();

        try {
            result = rdsHandler.updateAccount(accountUpdate);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(accountUpdate, result);
        result.setMsgId(accountUpdate.getMsgId());
        return result;
    }

    /**
     * Handle message base result.
     *
     * @param connectStringUpdate the connect string update
     *
     * @return the base result
     */
    public BaseResult handleMessage(ConnectStringUpdate connectStringUpdate) {
        log.info("receiving message for update connectString, virtual type : [{}]", connectStringUpdate.getVirtEnvType());

        log.info("msg id : [{}]", connectStringUpdate.getMsgId());
        ConnectStringUpdateResult result = new ConnectStringUpdateResult();
        try {
            result = rdsHandler.updateConnectString(connectStringUpdate);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(connectStringUpdate, result);
        result.setOldInsStatus(connectStringUpdate.getOldInsStatus());
        result.setMsgId(connectStringUpdate.getMsgId());
        return result;
    }

    /**
     * Handle message base result.
     *
     * @param connectStringRelease the connect string release
     *
     * @return the base result
     */
    public BaseResult handleMessage(ConnectStringRelease connectStringRelease) {
        log.info("receiving message for release connectString, virtual type : [{}]", connectStringRelease.getVirtEnvType());

        log.info("msg id : [{}]", connectStringRelease.getMsgId());
        ConnectStringReleaseResult result = new ConnectStringReleaseResult();
        try {
            result = rdsHandler.releaseConnectString(connectStringRelease);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(connectStringRelease, result);
        result.setMsgId(connectStringRelease.getMsgId());
        return result;
    }

    /**
     * Handle message base result.
     *
     * @param dbInstanceDescriptionModify the db instance description modify
     *
     * @return the base result
     */
    public BaseResult handleMessage(DBInstanceDescriptionModify dbInstanceDescriptionModify) {
        log.info("receiving message for modify rds description, virtual type : [{}]", dbInstanceDescriptionModify.getVirtEnvType());

        log.info("msg id : [{}]", dbInstanceDescriptionModify.getMsgId());
        DBInstanceResult result = new DBInstanceResult();
        try {
            result = rdsHandler.modifyRdsDBInstanceDescription(dbInstanceDescriptionModify);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(dbInstanceDescriptionModify, result);
        result.setMsgId(dbInstanceDescriptionModify.getMsgId());
        return result;
    }

    /**
     * Handle message load balance status result.
     *
     * @param loadBalanceStatus the load balance status
     *
     * @return the load balance status result
     */
    public LoadBalanceStatusResult handleMessage(LoadBalanceStatus loadBalanceStatus) {

        log.info("receiving message for update loadBalance, virtual type : [{}] loadBalance id : [{}]"
                         ,loadBalanceStatus.getVirtEnvType(), loadBalanceStatus.getLoadBalanceId());

        LoadBalanceStatusResult loadBalanceStatusResult = new LoadBalanceStatusResult();
        try {
            loadBalanceStatusResult = netHandler.setLoadBalanceStatus(loadBalanceStatus);
        } catch (CommonAdapterException e) {
            loadBalanceStatusResult.setSuccess(false);
            loadBalanceStatusResult.setErrCode(e.getErrCode());
            loadBalanceStatusResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            loadBalanceStatusResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            loadBalanceStatusResult.setSuccess(false);
            loadBalanceStatusResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            loadBalanceStatusResult.setErrMsg(e.getMessage());
            loadBalanceStatusResult.setSuccess(false);
        }
        BaseUtil.setResult(loadBalanceStatus, loadBalanceStatusResult);
        return loadBalanceStatusResult;
    }

    public LoadBalanceNameResult handleMessage(LoadBalanceName loadBalanceName) {
        log.info("receiving message for update loadBalance, virtual type : [{}] loadBalance id : [{}]"
                         ,loadBalanceName.getVirtEnvType(), loadBalanceName.getLoadBalanceId());

        LoadBalanceNameResult loadBalanceNameResult = new LoadBalanceNameResult();
        try {
            loadBalanceNameResult = netHandler.setLoadBalanceName(loadBalanceName);
        } catch (CommonAdapterException e) {
            loadBalanceNameResult.setSuccess(false);
            loadBalanceNameResult.setErrCode(e.getErrCode());
            loadBalanceNameResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            loadBalanceNameResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            loadBalanceNameResult.setSuccess(false);
            loadBalanceNameResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (ClientResponseException e) {
            loadBalanceNameResult.setErrMsg(e.getMessage());
            loadBalanceNameResult.setSuccess(false);
        } catch (Exception e) {
            loadBalanceNameResult.setErrMsg(e.getMessage());
            loadBalanceNameResult.setSuccess(false);
        }
        BaseUtil.setResult(loadBalanceName, loadBalanceNameResult);
        return loadBalanceNameResult;
    }

    /**
     * Handle message delete lb result.
     *
     * @param deleteLb the delete lb
     *
     * @return the delete lb result
     */
    public DeleteLbResult handleMessage(DeleteLb deleteLb) {

        log.info("receiving message for delete loadBalance, virtual type : [{}] loadBalance id : [{}]" ,deleteLb.getVirtEnvType(),deleteLb.getLoadBalanceId());

        DeleteLbResult deleteLbResult = new DeleteLbResult();
        try {
            deleteLbResult = netHandler.deleteLb(deleteLb);
        } catch (CommonAdapterException e) {
            deleteLbResult.setSuccess(false);
            deleteLbResult.setErrCode(e.getErrCode());
            deleteLbResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            deleteLbResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            deleteLbResult.setSuccess(false);
            deleteLbResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            deleteLbResult.setErrMsg(e.getMessage());
            deleteLbResult.setSuccess(false);
        }
        BaseUtil.setResult(deleteLb, deleteLbResult);
        return deleteLbResult;
    }

    /**
     * Handle message describe zone result.
     *
     * @param describeZone the describe zone
     *
     * @return the describe zone result
     */
    public DescribeZoneResult handleMessage(DescribeZone describeZone) {

        log.info("receiving message for create loadBalance, virtual type : [{}]", describeZone.getVirtEnvType());

        DescribeZoneResult describeZoneResult = new DescribeZoneResult();
        try {
            describeZoneResult = netHandler.describeZones(describeZone);
        } catch (CommonAdapterException e) {
            describeZoneResult.setSuccess(false);
            describeZoneResult.setErrCode(e.getErrCode());
            describeZoneResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            describeZoneResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            describeZoneResult.setSuccess(false);
            describeZoneResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            describeZoneResult.setErrMsg(e.getMessage());
            describeZoneResult.setSuccess(false);
        }
        BaseUtil.setResult(describeZone, describeZoneResult);
        return describeZoneResult;
    }

    /**
     * Handle message load balance create result.
     *
     * @param lbCreate the lb create
     *
     * @return the load balance create result
     */
    public LoadBalanceCreateResult handleMessage(LbCreate lbCreate) {

        log.info("receiving message for create loadBalance, virtual type : [{}]", lbCreate.getVirtEnvType());

        LoadBalanceCreateResult loadBalanceCreateResult = new LoadBalanceCreateResult();
        try {
            loadBalanceCreateResult = netHandler.createLb(lbCreate);
        } catch (CommonAdapterException e) {
            loadBalanceCreateResult.setSuccess(false);
            loadBalanceCreateResult.setErrCode(e.getErrCode());
            loadBalanceCreateResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            loadBalanceCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            loadBalanceCreateResult.setSuccess(false);
            loadBalanceCreateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            loadBalanceCreateResult.setErrMsg(e.getMessage());
            loadBalanceCreateResult.setSuccess(false);
        }
        BaseUtil.setResult(lbCreate, loadBalanceCreateResult);
        return loadBalanceCreateResult;
    }

    /**
     * Handle message load balance rule create result.
     *
     * @param lbRuleCreate the lb rule create
     *
     * @return the load balance create result
     */
    public LbRuleCreateResult handleMessage(LbRuleCreate lbRuleCreate) {

        log.info("receiving message for create loadBalance Rule create, virtual type : [{}]", lbRuleCreate.getVirtEnvType());

        LbRuleCreateResult lbRuleCreateResult = new LbRuleCreateResult();
        try {
            lbRuleCreateResult = netHandler.createLbRule(lbRuleCreate);
        } catch (CommonAdapterException e) {
            lbRuleCreateResult.setSuccess(false);
            lbRuleCreateResult.setErrCode(e.getErrCode());
            lbRuleCreateResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            lbRuleCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            lbRuleCreateResult.setSuccess(false);
            lbRuleCreateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            lbRuleCreateResult.setErrMsg(e.getMessage());
            lbRuleCreateResult.setSuccess(false);
        }
        BaseUtil.setResult(lbRuleCreate, lbRuleCreateResult);
        return lbRuleCreateResult;
    }

    /**
     * Handle message load balance rule delete result.
     *
     * @param lbRuleDelete the lb rule delete
     *
     * @return the load balance rule delete result
     */
    public LbRuleDeleteResult handleMessage(LbRuleDelete lbRuleDelete) {

        log.info("receiving message for create loadBalance Rule delete, virtual type : [{}]", lbRuleDelete.getVirtEnvType());

        LbRuleDeleteResult lbRuleDeleteResult = new LbRuleDeleteResult();
        try {
            lbRuleDeleteResult = netHandler.deleteLbRule(lbRuleDelete);
        } catch (CommonAdapterException e) {
            lbRuleDeleteResult.setSuccess(false);
            lbRuleDeleteResult.setErrCode(e.getErrCode());
            lbRuleDeleteResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            lbRuleDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            lbRuleDeleteResult.setSuccess(false);
            lbRuleDeleteResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            lbRuleDeleteResult.setErrMsg(e.getMessage());
            lbRuleDeleteResult.setSuccess(false);
        }
        BaseUtil.setResult(lbRuleDelete, lbRuleDeleteResult);
        return lbRuleDeleteResult;
    }

    /**
     * Handle message load balance rule update result.
     *
     * @param lbRuleUpdate the lb rule update
     *
     * @return the load balance update result
     */
    public LbRuleUpdateResult handleMessage(LbRuleUpdate lbRuleUpdate) {

        log.info("receiving message for create loadBalance Rule update, virtual type : [{}]", lbRuleUpdate.getVirtEnvType());

        LbRuleUpdateResult lbRuleUpdateResult = new LbRuleUpdateResult();
        try {
            lbRuleUpdateResult = netHandler.updateLbRule(lbRuleUpdate);
        } catch (CommonAdapterException e) {
            lbRuleUpdateResult.setSuccess(false);
            lbRuleUpdateResult.setErrCode(e.getErrCode());
            lbRuleUpdateResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            lbRuleUpdateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            lbRuleUpdateResult.setSuccess(false);
            lbRuleUpdateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            lbRuleUpdateResult.setErrMsg(e.getMessage());
            lbRuleUpdateResult.setSuccess(false);
        }
        BaseUtil.setResult(lbRuleUpdate, lbRuleUpdateResult);
        return lbRuleUpdateResult;
    }

    /**
     * Handle message lb listner create result.
     *
     * @param lbListenerCreate the lb listener create
     *
     * @return the lb listner create result
     */
    public LbListnerCreateResult handleMessage(LbListenerCreate lbListenerCreate) {

        log.info("receiving message for create loadBalanceListener, virtual type : [{}]", lbListenerCreate.getVirtEnvType());

        LbListnerCreateResult lbListnerCreateResult = new LbListnerCreateResult();
        try {
            lbListnerCreateResult = netHandler.createLbListner(lbListenerCreate);
        } catch (CommonAdapterException e) {
            lbListnerCreateResult.setSuccess(false);
            lbListnerCreateResult.setErrCode(e.getErrCode());
            lbListnerCreateResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            lbListnerCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            lbListnerCreateResult.setSuccess(false);
            lbListnerCreateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            lbListnerCreateResult.setErrMsg(e.getMessage());
            lbListnerCreateResult.setSuccess(false);
        }
        BaseUtil.setResult(lbListenerCreate, lbListnerCreateResult);
        return lbListnerCreateResult;
    }

    /**
     * Handle message start or stop listener result.
     *
     * @param startOrStopListener the start or stop listener
     *
     * @return the start or stop listener result
     */
    public StartOrStopListenerResult handleMessage(StartOrStopListener startOrStopListener) {

        log.info("receiving message for update loadBalanceListener, virtual type : [{}]", startOrStopListener.getVirtEnvType());

        StartOrStopListenerResult startOrStopListenerResult = new StartOrStopListenerResult();
        try {
            startOrStopListenerResult = netHandler.startOrStopListener(startOrStopListener);
        } catch (CommonAdapterException e) {
            startOrStopListenerResult.setSuccess(false);
            startOrStopListenerResult.setErrCode(e.getErrCode());
            startOrStopListenerResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            startOrStopListenerResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            startOrStopListenerResult.setSuccess(false);
            startOrStopListenerResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            startOrStopListenerResult.setErrMsg(e.getMessage());
            startOrStopListenerResult.setSuccess(false);
        }
        BaseUtil.setResult(startOrStopListener, startOrStopListenerResult);
        return startOrStopListenerResult;
    }

    /**
     * Handle message listener delete result.
     *
     * @param listenerDelete the listener delete
     *
     * @return the listener delete result
     */
    public ListenerDeleteResult handleMessage(ListenerDelete listenerDelete) {

        log.info("receiving message for delete loadBalanceListener, virtual type : [{}]", listenerDelete.getVirtEnvType());

        ListenerDeleteResult listenerDeleteResult = new ListenerDeleteResult();
        try {
            listenerDeleteResult = netHandler.deleteListener(listenerDelete);
        } catch (CommonAdapterException e) {
            listenerDeleteResult.setSuccess(false);
            listenerDeleteResult.setErrCode(e.getErrCode());
            listenerDeleteResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            listenerDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            listenerDeleteResult.setSuccess(false);
            listenerDeleteResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            listenerDeleteResult.setErrMsg(e.getMessage());
            listenerDeleteResult.setSuccess(false);
        }
        BaseUtil.setResult(listenerDelete, listenerDeleteResult);
        return listenerDeleteResult;
    }

    /**
     * Handle message backend server add result.
     *
     * @param backendServerAdd the backend server add
     *
     * @return the backend server add result
     */
    public BackendServerAddResult handleMessage(BackendServerAdd backendServerAdd) {

        log.info("receiving message for add BackendServer, virtual type : [{}]", backendServerAdd.getVirtEnvType());

        BackendServerAddResult backendServerAddResult = new BackendServerAddResult();
        try {
            backendServerAddResult = netHandler.addBackendServer(backendServerAdd);
        } catch (CommonAdapterException e) {
            backendServerAddResult.setSuccess(false);
            backendServerAddResult.setErrCode(e.getErrCode());
            backendServerAddResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            backendServerAddResult.setSuccess(false);
            backendServerAddResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            backendServerAddResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            backendServerAddResult.setErrMsg(e.getMessage());
            backendServerAddResult.setSuccess(false);
        }
        BaseUtil.setResult(backendServerAdd, backendServerAddResult);
        return backendServerAddResult;
    }

    /**
     * Handle message backend server set result.
     *
     * @param backendServerSet the backend server set
     *
     * @return the backend server set result
     */
    public BackendServerSetResult handleMessage(BackendServerSet backendServerSet) {

        log.info("receiving message for set loadBalanceListener, virtual type : [{}]", backendServerSet.getVirtEnvType());

        BackendServerSetResult backendServerSetResult = new BackendServerSetResult();
        try {
            backendServerSetResult = netHandler.setBackendServer(backendServerSet);
        } catch (CommonAdapterException e) {
            backendServerSetResult.setSuccess(false);
            backendServerSetResult.setErrCode(e.getErrCode());
            backendServerSetResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            backendServerSetResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            backendServerSetResult.setSuccess(false);
            backendServerSetResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            backendServerSetResult.setErrMsg(e.getMessage());
            backendServerSetResult.setSuccess(false);
        }
        BaseUtil.setResult(backendServerSet, backendServerSetResult);
        return backendServerSetResult;
    }

    /**
     * Handle message backend server remove result.
     *
     * @param backendServerRemove the backend server remove
     *
     * @return the backend server remove result
     */
    public BackendServerRemoveResult handleMessage(BackendServerRemove backendServerRemove) {

        log.info("receiving message for set loadBalanceListener, virtual type : [{}]", backendServerRemove.getVirtEnvType());

        BackendServerRemoveResult backendServerRemoveResult = new BackendServerRemoveResult();
        try {
            backendServerRemoveResult = netHandler.removeBackendServer(backendServerRemove);
        } catch (CommonAdapterException e) {
            backendServerRemoveResult.setSuccess(false);
            backendServerRemoveResult.setErrCode(e.getErrCode());
            backendServerRemoveResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            backendServerRemoveResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            backendServerRemoveResult.setSuccess(false);
            backendServerRemoveResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            backendServerRemoveResult.setErrMsg(e.getMessage());
            backendServerRemoveResult.setSuccess(false);
        }
        BaseUtil.setResult(backendServerRemove, backendServerRemoveResult);
        return backendServerRemoveResult;
    }

    /**
     * Handle message master slave server group create result.
     *
     * @param masterSlaveServerGroupCreate the master slave server group create
     *
     * @return the master slave server group create result
     */
    public MasterSlaveServerGroupCreateResult handleMessage(MasterSlaveServerGroupCreate masterSlaveServerGroupCreate) {

        log.info("receiving message for set loadBalanceListener, virtual type : [{}]", masterSlaveServerGroupCreate.getVirtEnvType());

        MasterSlaveServerGroupCreateResult masterSlaveServerGroupCreateResult = new MasterSlaveServerGroupCreateResult();
        try {
            masterSlaveServerGroupCreateResult = netHandler.createMasterSlaveServerGroup(masterSlaveServerGroupCreate);
        } catch (CommonAdapterException e) {
            masterSlaveServerGroupCreateResult.setSuccess(false);
            masterSlaveServerGroupCreateResult.setErrCode(e.getErrCode());
            masterSlaveServerGroupCreateResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            masterSlaveServerGroupCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            masterSlaveServerGroupCreateResult.setSuccess(false);
            masterSlaveServerGroupCreateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            masterSlaveServerGroupCreateResult.setErrMsg(e.getMessage());
            masterSlaveServerGroupCreateResult.setSuccess(false);
        }
        BaseUtil.setResult(masterSlaveServerGroupCreate, masterSlaveServerGroupCreateResult);
        return masterSlaveServerGroupCreateResult;
    }

    /**
     * Handle message master slave server group delete result.
     *
     * @param masterSlaveServerGroupDelete the master slave server group delete
     *
     * @return the master slave server group delete result
     */
    public MasterSlaveServerGroupDeleteResult handleMessage(MasterSlaveServerGroupDelete masterSlaveServerGroupDelete) {

        log.info("receiving message for set loadBalanceListener, virtual type : [{}]", masterSlaveServerGroupDelete.getVirtEnvType());

        MasterSlaveServerGroupDeleteResult masterSlaveServerGroupDeleteResult = new MasterSlaveServerGroupDeleteResult();
        try {
            masterSlaveServerGroupDeleteResult = netHandler
                    .deleteMasterSlaveServerGroup(masterSlaveServerGroupDelete);
        } catch (CommonAdapterException e) {
            masterSlaveServerGroupDeleteResult.setSuccess(false);
            masterSlaveServerGroupDeleteResult.setErrCode(e.getErrCode());
            masterSlaveServerGroupDeleteResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            masterSlaveServerGroupDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            masterSlaveServerGroupDeleteResult.setSuccess(false);
            masterSlaveServerGroupDeleteResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            masterSlaveServerGroupDeleteResult.setErrMsg(e.getMessage());
            masterSlaveServerGroupDeleteResult.setSuccess(false);
        }
        BaseUtil.setResult(masterSlaveServerGroupDelete, masterSlaveServerGroupDeleteResult);
        return masterSlaveServerGroupDeleteResult;
    }

    /**
     * Handle message v server group create result.
     *
     * @param vServerGroupCreate the v server group create
     *
     * @return the v server group create result
     */
    public VServerGroupCreateResult handleMessage(VServerGroupCreate vServerGroupCreate) {

        log.info("receiving message for set loadBalanceListener, virtual type : [{}]", vServerGroupCreate.getVirtEnvType());

        VServerGroupCreateResult vServerGroupCreateResult = new VServerGroupCreateResult();
        try {
            vServerGroupCreateResult = netHandler.createVServerGroup(vServerGroupCreate);
        } catch (CommonAdapterException e) {
            vServerGroupCreateResult.setSuccess(false);
            vServerGroupCreateResult.setErrCode(e.getErrCode());
            vServerGroupCreateResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            vServerGroupCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vServerGroupCreateResult.setSuccess(false);
            vServerGroupCreateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            vServerGroupCreateResult.setErrMsg(e.getMessage());
            vServerGroupCreateResult.setSuccess(false);
        }
        BaseUtil.setResult(vServerGroupCreate, vServerGroupCreateResult);
        return vServerGroupCreateResult;
    }

    /**
     * Handle message v server group create result.
     *
     * @param vServerGroupUpdate the v server group create
     *
     * @return the v server group create result
     */
    public VServerGroupCreateResult handleMessage(VServerGroupUpdate vServerGroupUpdate) {

        log.info("receiving message for update ServerGroup, virtual type : [{}]", vServerGroupUpdate.getVirtEnvType());

        VServerGroupCreateResult vServerGroupCreateResult = new VServerGroupCreateResult();
        try {
            vServerGroupCreateResult = netHandler.updateVServerGroup(vServerGroupUpdate);
        } catch (CommonAdapterException e) {
            vServerGroupCreateResult.setSuccess(false);
            vServerGroupCreateResult.setErrCode(e.getErrCode());
            vServerGroupCreateResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            vServerGroupCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vServerGroupCreateResult.setSuccess(false);
            vServerGroupCreateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            vServerGroupCreateResult.setErrMsg(e.getMessage());
            vServerGroupCreateResult.setSuccess(false);
        }
        BaseUtil.setResult(vServerGroupUpdate, vServerGroupCreateResult);
        return vServerGroupCreateResult;
    }

    /**
     * Handle message v server group delete result.
     *
     * @param vServerGroupDelete the v server group delete
     *
     * @return the v server group delete result
     */
    public VServerGroupDeleteResult handleMessage(VServerGroupDelete vServerGroupDelete) {

        log.info("receiving message for set loadBalanceListener, virtual type : [{}]", vServerGroupDelete.getVirtEnvType());

        VServerGroupDeleteResult vServerGroupDeleteResult = new VServerGroupDeleteResult();
        try {
            vServerGroupDeleteResult = netHandler.deleteVServerGroup(vServerGroupDelete);
        } catch (CommonAdapterException e) {
            vServerGroupDeleteResult.setSuccess(false);
            vServerGroupDeleteResult.setErrCode(e.getErrCode());
            vServerGroupDeleteResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            vServerGroupDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vServerGroupDeleteResult.setSuccess(false);
            vServerGroupDeleteResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            vServerGroupDeleteResult.setErrMsg(e.getMessage());
            vServerGroupDeleteResult.setSuccess(false);
        }
        BaseUtil.setResult(vServerGroupDelete, vServerGroupDeleteResult);
        return vServerGroupDeleteResult;
    }

    /**
     * Handle message v server group backend servers add result.
     *
     * @param vServerGroupBackendServersAdd the v server group backend servers add
     *
     * @return the v server group backend servers add result
     */
    public VServerGroupBackendServersAddResult handleMessage(
            VServerGroupBackendServersAdd vServerGroupBackendServersAdd) {

        log.info("receiving message for set loadBalanceListener, virtual type : [{}]", vServerGroupBackendServersAdd.getVirtEnvType());

        VServerGroupBackendServersAddResult vServerGroupBackendServersAddResult = new VServerGroupBackendServersAddResult();
        try {
            vServerGroupBackendServersAddResult = netHandler
                    .addVServerGroupBackendServers(vServerGroupBackendServersAdd);
        } catch (CommonAdapterException e) {
            vServerGroupBackendServersAddResult.setSuccess(false);
            vServerGroupBackendServersAddResult.setErrCode(e.getErrCode());
            vServerGroupBackendServersAddResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            vServerGroupBackendServersAddResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vServerGroupBackendServersAddResult.setSuccess(false);
            vServerGroupBackendServersAddResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {

            vServerGroupBackendServersAddResult.setErrMsg(e.getMessage());
            vServerGroupBackendServersAddResult.setSuccess(false);
        }
        BaseUtil.setResult(vServerGroupBackendServersAdd, vServerGroupBackendServersAddResult);
        return vServerGroupBackendServersAddResult;
    }

    /**
     * Handle message v server group backend servers remove result.
     *
     * @param vServerGroupBackendServersRemove the v server group backend servers remove
     *
     * @return the v server group backend servers remove result
     */
    public VServerGroupBackendServersRemoveResult handleMessage(
            VServerGroupBackendServersRemove vServerGroupBackendServersRemove) {

        log.info("receiving message for set loadBalanceListener, virtual type : [{}]", vServerGroupBackendServersRemove.getVirtEnvType());

        VServerGroupBackendServersRemoveResult result = new VServerGroupBackendServersRemoveResult();
        try {
            result = netHandler.removeVServerGroupBackendServers(vServerGroupBackendServersRemove);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setSuccess(false);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(vServerGroupBackendServersRemove, result);
        return result;
    }

    /**
     * Handle message v server group attribute set result.
     *
     * @param vServerGroupAttributeSet the v server group attribute set
     *
     * @return the v server group attribute set result
     */
    public VServerGroupAttributeSetResult handleMessage(VServerGroupAttributeSet vServerGroupAttributeSet) {

        log.info("receiving message for set loadBalanceListener, virtual type : [{}]", vServerGroupAttributeSet.getVirtEnvType());

        VServerGroupAttributeSetResult vServerGroupAttributeSetResult = new VServerGroupAttributeSetResult();
        try {
            vServerGroupAttributeSetResult = netHandler.setVServerGroupAttribute(vServerGroupAttributeSet);
        } catch (CommonAdapterException e) {
            vServerGroupAttributeSetResult.setSuccess(false);
            vServerGroupAttributeSetResult.setErrCode(e.getErrCode());
            vServerGroupAttributeSetResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            vServerGroupAttributeSetResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vServerGroupAttributeSetResult.setSuccess(false);
            vServerGroupAttributeSetResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {

            vServerGroupAttributeSetResult.setErrMsg(e.getMessage());
            vServerGroupAttributeSetResult.setSuccess(false);
        }
        BaseUtil.setResult(vServerGroupAttributeSet, vServerGroupAttributeSetResult);
        return vServerGroupAttributeSetResult;
    }

    /**
     * Handle message server certificates result.
     *
     * @param decribeCertificates the decribe certificates
     *
     * @return the server certificates result
     */
    public ServerCertificatesResult handleMessage(DecribeCertificates decribeCertificates) {

        log.info("receiving message for decribe Certificates , virtual type : [{}]", decribeCertificates.getVirtEnvType());

        ServerCertificatesResult serverCertificatesResult = new ServerCertificatesResult();
        try {
            serverCertificatesResult = netHandler.describeCertificate(decribeCertificates);
        } catch (CommonAdapterException e) {
            serverCertificatesResult.setSuccess(false);
            serverCertificatesResult.setErrCode(e.getErrCode());
            serverCertificatesResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            serverCertificatesResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            serverCertificatesResult.setSuccess(false);
            serverCertificatesResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            serverCertificatesResult.setErrMsg(e.getMessage());
            serverCertificatesResult.setSuccess(false);
        }
        BaseUtil.setResult(decribeCertificates, serverCertificatesResult);
        return serverCertificatesResult;
    }

    /**
     * Handle message monitor metric result.
     */
    public MetricMonitorResult handleMessage(MetricMonitor metricMonitor) {

        log.info("receiving message for metric monitor, virtual type : [{}]", metricMonitor.getVirtEnvType());

        log.info("msg id : [{}]", metricMonitor.getMsgId());

        MetricMonitorResult metricMonitorResult = new MetricMonitorResult();

        try {
            metricMonitorResult = monitorHandler.getMetricStatistics(metricMonitor);
            log.info("adaptor monitor :[{}] has been received successfully",metricMonitor.getCommonMetricName());
        } catch (CommonAdapterException e) {

            metricMonitorResult.setSuccess(false);
            metricMonitorResult.setErrCode(e.getErrCode());
            metricMonitorResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            metricMonitorResult.setSuccess(false);
            metricMonitorResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            metricMonitorResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            metricMonitorResult.setSuccess(false);
            metricMonitorResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            metricMonitorResult.setErrMsg(e.getMessage());
        }

        metricMonitorResult.setMsgId(metricMonitor.getMsgId());

        return metricMonitorResult;

    }

    /**
     * Handle message pirce calculate result.
     */
    public QueryBalanceResult handleMessage(QueryBalance queryBalance) {

        log.info("receiving message for queryBalance, virtual type : [{}]", queryBalance.getVirtEnvType());

        log.info("msg id : [{}]", queryBalance.getMsgId());

        QueryBalanceResult queryBalanceResult = new QueryBalanceResult();

        try {
            queryBalanceResult = vmHandler.queryBalance(queryBalance);
        } catch (CommonAdapterException e) {

            queryBalanceResult.setSuccess(false);
            queryBalanceResult.setErrCode(e.getErrCode());
            queryBalanceResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            queryBalanceResult.setSuccess(false);
            queryBalanceResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            queryBalanceResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            queryBalanceResult.setSuccess(false);
            queryBalanceResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            queryBalanceResult.setErrMsg(e.getMessage());
        }

        queryBalanceResult.setMsgId(queryBalance.getMsgId());

        return queryBalanceResult;

    }

    /**
     * Handle message pirce calculate result.
     */
    public PriceCalculateResult handleMessage(PriceCalculate priceCalculate) {

        log.info("receiving message for inquiry price, virtual type : [{}]", priceCalculate.getVirtEnvType());

        log.info("msg id : [{}]", priceCalculate.getMsgId());

        PriceCalculateResult priceCalculateResult = new PriceCalculateResult();

        try {
            priceCalculateResult = vmHandler.getPrice(priceCalculate);
        } catch (CommonAdapterException e) {

            priceCalculateResult.setSuccess(false);
            priceCalculateResult.setErrCode(e.getErrCode());
            priceCalculateResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            priceCalculateResult.setSuccess(false);
            priceCalculateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            priceCalculateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            priceCalculateResult.setSuccess(false);
            priceCalculateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            priceCalculateResult.setErrMsg(e.getMessage());
        }

        priceCalculateResult.setMsgId(priceCalculate.getMsgId());

        return priceCalculateResult;

    }

    /**
     * Handle message pirce calculate result.
     */
    public PriceCalculateResult handleMessage(PriceSubscriptionCalculate priceCalculate) {

        log.info("receiving message for metric monitor, virtual type : [{}]", priceCalculate.getVirtEnvType());

        log.info("msg id : [{}]", priceCalculate.getMsgId());

        PriceCalculateResult priceCalculateResult = new PriceCalculateResult();

        try {
            priceCalculateResult = vmHandler.getSubscriptionPrice(priceCalculate);
        } catch (CommonAdapterException e) {

            priceCalculateResult.setSuccess(false);
            priceCalculateResult.setErrCode(e.getErrCode());
            priceCalculateResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            priceCalculateResult.setSuccess(false);
            priceCalculateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            priceCalculateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            priceCalculateResult.setSuccess(false);
            priceCalculateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            priceCalculateResult.setErrMsg(e.getMessage());
        }

        priceCalculateResult.setMsgId(priceCalculate.getMsgId());

        return priceCalculateResult;

    }

    /**
     * Handle message pirce calculate result.
     */
    public PriceCalculateResult handleMessage(PricePayAsYouGoCalculate priceCalculate) {

        log.info("receiving message for metric monitor, virtual type : [{}]", priceCalculate.getVirtEnvType());

        log.info("msg id : [{}]", priceCalculate.getMsgId());

        PriceCalculateResult priceCalculateResult = new PriceCalculateResult();

        try {
            priceCalculateResult = vmHandler.getPayAsYouGoPrice(priceCalculate);
        } catch (CommonAdapterException e) {

            priceCalculateResult.setSuccess(false);
            priceCalculateResult.setErrCode(e.getErrCode());
            priceCalculateResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            priceCalculateResult.setSuccess(false);
            priceCalculateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            priceCalculateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            priceCalculateResult.setSuccess(false);
            priceCalculateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            priceCalculateResult.setErrMsg(e.getMessage());
        }

        priceCalculateResult.setMsgId(priceCalculate.getMsgId());

        return priceCalculateResult;

    }

    /**
     * Handle message sfs update calculate result.
     */
    public VmTypeChangeQueryResult handleMessage(VmTypeChangeQuery vmTypeChangeQuery) {

        log.info("receiving vmTypeChangeQuery  for metric monitor, virtual type : [{}]", vmTypeChangeQuery.getVirtEnvType());

        log.info("msg id : [{}]", vmTypeChangeQuery.getMsgId());

        VmTypeChangeQueryResult vmTypeChangeQueryResult = new VmTypeChangeQueryResult();

        try {
            vmTypeChangeQueryResult = vmHandler.getVmTypeChangeQuery(vmTypeChangeQuery);
        } catch (CommonAdapterException e) {

            vmTypeChangeQueryResult.setSuccess(false);
            vmTypeChangeQueryResult.setErrCode(e.getErrCode());
            vmTypeChangeQueryResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            vmTypeChangeQueryResult.setSuccess(false);
            vmTypeChangeQueryResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmTypeChangeQueryResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            vmTypeChangeQueryResult.setSuccess(false);
            vmTypeChangeQueryResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmTypeChangeQueryResult.setErrMsg(e.getMessage());
        }
        vmTypeChangeQueryResult.setMsgId(vmTypeChangeQuery.getMsgId());
        return vmTypeChangeQueryResult;
    }

    /**
     * Handle message sfs update calculate result.
     */
    public ShareUpdateResult handleMessage(ShareUpdate shareUpdate) {

        log.info("receiving sfs update for metric monitor, virtual type : [{}]", shareUpdate.getVirtEnvType());

        log.info("msg id : [{}]", shareUpdate.getMsgId());

        ShareUpdateResult shareUpdateResult = new ShareUpdateResult();

        try {
            shareUpdateResult = vmHandler.shareUpdate(shareUpdate);
        } catch (CommonAdapterException e) {

            shareUpdateResult.setSuccess(false);
            shareUpdateResult.setErrCode(e.getErrCode());
            shareUpdateResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            shareUpdateResult.setSuccess(false);
            shareUpdateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            shareUpdateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            shareUpdateResult.setSuccess(false);
            shareUpdateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            shareUpdateResult.setErrMsg(e.getMessage());
        }
        shareUpdateResult.setMsgId(shareUpdate.getMsgId());
        return shareUpdateResult;
    }

    /**
     * Handle message sfs update calculate result.
     */
    public ShareActionResult handleMessage(ShareAction shareAction) {

        log.info("receiving Action update for metric monitor, virtual type : [{}]", shareAction.getVirtEnvType());

        log.info("msg id : [{}]", shareAction.getMsgId());

        ShareActionResult shareActionResult = new ShareActionResult();

        try {
            shareActionResult = vmHandler.shareAction(shareAction);
        } catch (CommonAdapterException e) {

            shareActionResult.setSuccess(false);
            shareActionResult.setErrCode(e.getErrCode());
            shareActionResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            shareActionResult.setSuccess(false);
            shareActionResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            shareActionResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            shareActionResult.setSuccess(false);
            shareActionResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            shareActionResult.setErrMsg(e.getMessage());
        }
        shareActionResult.setMsgId(shareAction.getMsgId());
        return shareActionResult;

    }

    /**
     * Handle message ShareRule update calculate result.
     */
    public ShareRuleDeleteResult handleMessage(ShareRuleDelete shareRuleDelete) {

        log.info("receiving Action ShareRule for metric monitor, virtual type : [{}]", shareRuleDelete.getVirtEnvType());

        log.info("msg id : [{}]", shareRuleDelete.getMsgId());

        ShareRuleDeleteResult shareRuleDeleteResult = new ShareRuleDeleteResult();

        try {
            shareRuleDeleteResult = vmHandler.shareRuleDelete(shareRuleDelete);
        } catch (CommonAdapterException e) {

            shareRuleDeleteResult.setSuccess(false);
            shareRuleDeleteResult.setErrCode(e.getErrCode());
            shareRuleDeleteResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            shareRuleDeleteResult.setSuccess(false);
            shareRuleDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            shareRuleDeleteResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            shareRuleDeleteResult.setSuccess(false);
            shareRuleDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            shareRuleDeleteResult.setErrMsg(e.getMessage());
        }
        shareRuleDeleteResult.setMsgId(shareRuleDelete.getMsgId());
        return shareRuleDeleteResult;

    }

    /**
     * Handle message sfs quota calculate result.
     */
    public QuotaSetCreatResult handleMessage(QuotaSetCreat quotaSetCreat) {

        log.info("receiving sfs quota for metric monitor, virtual type : [{}]", quotaSetCreat.getVirtEnvType());

        log.info("msg id : [{}]", quotaSetCreat.getMsgId());

        QuotaSetCreatResult quotaSetCreatResult = new QuotaSetCreatResult();

        try {
            quotaSetCreatResult = vmHandler.getQuotaSet(quotaSetCreat);
        } catch (CommonAdapterException e) {

            quotaSetCreatResult.setSuccess(false);
            quotaSetCreatResult.setErrCode(e.getErrCode());
            quotaSetCreatResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            quotaSetCreatResult.setSuccess(false);
            quotaSetCreatResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            quotaSetCreatResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            quotaSetCreatResult.setSuccess(false);
            quotaSetCreatResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            quotaSetCreatResult.setErrMsg(e.getMessage());
        }

        quotaSetCreatResult.setMsgId(quotaSetCreat.getMsgId());

        return quotaSetCreatResult;

    }

    /**
     * Handle message sfs quota calculate result.
     */
    public QuotaConfigResult handleMessage(QuotaConfig quotaConfig) {

        log.info("receiving sfs quota for config quota, virtual type : [{}]", quotaConfig.getVirtEnvType());

        log.info("msg id : [{}]", quotaConfig.getMsgId());

        QuotaConfigResult quotaConfigResult = new QuotaConfigResult();

        try {
            quotaConfigResult = adminHandler.configQuota(quotaConfig);
        } catch (CommonAdapterException e) {

            quotaConfigResult.setSuccess(false);
            quotaConfigResult.setErrCode(e.getErrCode());
            quotaConfigResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            quotaConfigResult.setSuccess(false);
            quotaConfigResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            quotaConfigResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            quotaConfigResult.setSuccess(false);
            quotaConfigResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            quotaConfigResult.setErrMsg(e.getMessage());
        }

        quotaConfigResult.setMsgId(quotaConfig.getMsgId());

        return quotaConfigResult;

    }

    /**
     * Handle message vmrenew calculate result.
     */
    public VmRenewInstanceResult handleMessage(VmRenewInstance vmRenew) {

        log.info("receiving vmrenew  for metric monitor, virtual type : [{}]", vmRenew.getVirtEnvType());

        log.info("msg id : [{}]", vmRenew.getMsgId());

        VmRenewInstanceResult vmRenewResult = new VmRenewInstanceResult();

        try {
            vmRenewResult = vmHandler.renewInstance(vmRenew);
        } catch (CommonAdapterException e) {

            vmRenewResult.setSuccess(false);
            vmRenewResult.setErrCode(e.getErrCode());
            vmRenewResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            vmRenewResult.setSuccess(false);
            vmRenewResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmRenewResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            vmRenewResult.setSuccess(false);
            vmRenewResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmRenewResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(vmRenew, vmRenewResult);
        BeanUtil.transformBeanObj(vmRenew, vmRenewResult);
        vmRenewResult.setMsgId(vmRenew.getMsgId());
        return vmRenewResult;

    }

    /**
     * Handle message VmInquiryPrice calculate result.
     */
    public VmInquiryPriceResult handleMessage(VmInquiryPrice vmInquiryPrice) {

        log.info("receiving vmInquiryPrice  for metric monitor, virtual type : [{}]", vmInquiryPrice.getVirtEnvType());

        log.info("msg id : [{}]", vmInquiryPrice.getMsgId());

        VmInquiryPriceResult vmInquiryPriceResult = new VmInquiryPriceResult();

        try {
            vmInquiryPriceResult = vmHandler.vmInquiryPrice(vmInquiryPrice);
        } catch (CommonAdapterException e) {

            vmInquiryPriceResult.setSuccess(false);
            vmInquiryPriceResult.setErrCode(e.getErrCode());
            vmInquiryPriceResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            vmInquiryPriceResult.setSuccess(false);
            vmInquiryPriceResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmInquiryPriceResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            vmInquiryPriceResult.setSuccess(false);
            vmInquiryPriceResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmInquiryPriceResult.setErrMsg(e.getMessage());
        }

        vmInquiryPriceResult.setMsgId(vmInquiryPrice.getMsgId());
        return vmInquiryPriceResult;

    }

    /**
     * Handle message pirce calculate result.
     */
    public RdsPriceCalculateResult handleMessage(RdsPriceCalculate priceCalculate) {

        log.info("receiving message for metric monitor, virtual type : [{}]", priceCalculate.getVirtEnvType());

        log.info("msg id : [{}]", priceCalculate.getMsgId());

        RdsPriceCalculateResult priceCalculateResult = new RdsPriceCalculateResult();

        try {
            priceCalculateResult = rdsHandler.getPrice(priceCalculate);
        } catch (CommonAdapterException e) {

            priceCalculateResult.setSuccess(false);
            priceCalculateResult.setErrCode(e.getErrCode());
            priceCalculateResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            priceCalculateResult.setSuccess(false);
            priceCalculateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            priceCalculateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            priceCalculateResult.setSuccess(false);
            priceCalculateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            priceCalculateResult.setErrMsg(e.getMessage());
        }

        priceCalculateResult.setMsgId(priceCalculate.getMsgId());

        return priceCalculateResult;

    }

    /**
     * Handle message engine query result.
     */
    public DBEngineQueryResult handleMessage(DBEngineQuery dbEngineQuery) {

        log.info("receiving message for metric monitor, virtual type : [{}]", dbEngineQuery.getVirtEnvType());

        log.info("msg id : [{}]", dbEngineQuery.getMsgId());

        DBEngineQueryResult dbEngineQueryResult = new DBEngineQueryResult();

        try {
            dbEngineQueryResult = rdsHandler.queryEngines(dbEngineQuery);
        } catch (CommonAdapterException e) {

            dbEngineQueryResult.setSuccess(false);
            dbEngineQueryResult.setErrCode(e.getErrCode());
            dbEngineQueryResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            dbEngineQueryResult.setSuccess(false);
            dbEngineQueryResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            dbEngineQueryResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            dbEngineQueryResult.setSuccess(false);
            dbEngineQueryResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            dbEngineQueryResult.setErrMsg(e.getMessage());
        }

        dbEngineQueryResult.setMsgId(dbEngineQuery.getMsgId());

        return dbEngineQueryResult;

    }

    /**
     * Handle message pirce calculate result.
     */
    public DBInstanceClassQueryResult handleMessage(DBInstanceClassQuery dbInstanceClassQuery) {

        log.info("receiving message for metric monitor, virtual type : [{}]", dbInstanceClassQuery.getVirtEnvType());

        log.info("msg id : [{}]", dbInstanceClassQuery.getMsgId());

        DBInstanceClassQueryResult dbInstanceClassQueryResult = new DBInstanceClassQueryResult();

        try {
            dbInstanceClassQueryResult = rdsHandler.queryInstanceClasses(dbInstanceClassQuery);
        } catch (CommonAdapterException e) {

            dbInstanceClassQueryResult.setSuccess(false);
            dbInstanceClassQueryResult.setErrCode(e.getErrCode());
            dbInstanceClassQueryResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            dbInstanceClassQueryResult.setSuccess(false);
            dbInstanceClassQueryResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            dbInstanceClassQueryResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            dbInstanceClassQueryResult.setSuccess(false);
            dbInstanceClassQueryResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            dbInstanceClassQueryResult.setErrMsg(e.getMessage());
        }

        dbInstanceClassQueryResult.setMsgId(dbInstanceClassQuery.getMsgId());

        return dbInstanceClassQueryResult;

    }

    public DBInstanceEngineQueryResult handleMessage(DBInstanceEngineQuery dbInstanceEngineQuery) {

        log.info("receiving message for engine, virtual type : [{}]", dbInstanceEngineQuery.getVirtEnvType());

        log.info("msg id : [{}]", dbInstanceEngineQuery.getMsgId());

        DBInstanceEngineQueryResult dbInstanceEngineQueryResult = new DBInstanceEngineQueryResult();

        try {
            dbInstanceEngineQueryResult = rdsHandler.queryInstanceEngines(dbInstanceEngineQuery);
        } catch (CommonAdapterException e) {

            dbInstanceEngineQueryResult.setSuccess(false);
            dbInstanceEngineQueryResult.setErrCode(e.getErrCode());
            dbInstanceEngineQueryResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            dbInstanceEngineQueryResult.setSuccess(false);
            dbInstanceEngineQueryResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            dbInstanceEngineQueryResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            dbInstanceEngineQueryResult.setSuccess(false);
            dbInstanceEngineQueryResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            dbInstanceEngineQueryResult.setErrMsg(e.getMessage());
        }

        dbInstanceEngineQueryResult.setMsgId(dbInstanceEngineQuery.getMsgId());

        return dbInstanceEngineQueryResult;

    }

    /**
     * Handle message pirce calculate result.
     */
    public DBInstanceZoneQueryResult handleMessage(DBInstanceZoneQuery dbInstanceZoneQuery) {

        log.info("receiving message for describe rds zone, virtual type : [{}]", dbInstanceZoneQuery.getVirtEnvType());

        log.info("msg id : [{}]", dbInstanceZoneQuery.getMsgId());

        DBInstanceZoneQueryResult dbInstanceZoneQueryResult = new DBInstanceZoneQueryResult();

        try {
            dbInstanceZoneQueryResult = rdsHandler.queryInstanceZones(dbInstanceZoneQuery);
        } catch (CommonAdapterException e) {

            dbInstanceZoneQueryResult.setSuccess(false);
            dbInstanceZoneQueryResult.setErrCode(e.getErrCode());
            dbInstanceZoneQueryResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            dbInstanceZoneQueryResult.setSuccess(false);
            dbInstanceZoneQueryResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            dbInstanceZoneQueryResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            dbInstanceZoneQueryResult.setSuccess(false);
            dbInstanceZoneQueryResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            dbInstanceZoneQueryResult.setErrMsg(e.getMessage());
        }

        dbInstanceZoneQueryResult.setMsgId(dbInstanceZoneQuery.getMsgId());

        return dbInstanceZoneQueryResult;

    }

    public BucketScanResult handleMessage(BucketScan bucketScan) {
        log.info("receiving message for Scan buckets, virtual type : [{}]", bucketScan.getVirtEnvType());

        log.info("msg id : [{}]", bucketScan.getMsgId());

        BucketScanResult bucketScanResult = new BucketScanResult();
        try {
            bucketScanResult = scanHandler.scanBucket(bucketScan);
            bucketScanResult.setSuccess(true);
        } catch (CommonAdapterException e) {

            bucketScanResult.setSuccess(false);
            bucketScanResult.setErrCode(e.getErrCode());
            bucketScanResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            bucketScanResult.setSuccess(false);
            bucketScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            bucketScanResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            bucketScanResult.setSuccess(false);
            bucketScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            bucketScanResult.setErrMsg(e.getMessage());
        }

        bucketScanResult.setMsgId(bucketScan.getMsgId());
        return bucketScanResult;
    }

    public BucketObjectScanResult handleMessage(BucketObjectScan bucketObjectScan) {
        log.info("receiving message for Scan bucket objects, virtual type : [{}]", bucketObjectScan.getVirtEnvType());

        log.info("msg id : [{}]", bucketObjectScan.getMsgId());

        BucketObjectScanResult bucketObjectScanResult = new BucketObjectScanResult();
        try {
            bucketObjectScanResult = scanHandler.scanBucketObject(bucketObjectScan);
            bucketObjectScanResult.setSuccess(true);
        } catch (CommonAdapterException e) {

            bucketObjectScanResult.setSuccess(false);
            bucketObjectScanResult.setErrCode(e.getErrCode());
            bucketObjectScanResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            bucketObjectScanResult.setSuccess(false);
            bucketObjectScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            bucketObjectScanResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            bucketObjectScanResult.setSuccess(false);
            bucketObjectScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            bucketObjectScanResult.setErrMsg(e.getMessage());
        }

        bucketObjectScanResult.setMsgId(bucketObjectScan.getMsgId());
        return bucketObjectScanResult;
    }

    public ObjectUploadResult handleMessage(ObjectUpload objectUpload) {
        log.info("receiving message for objectUpload buckets, virtual type : [{}]", objectUpload.getVirtEnvType());

        log.info("msg id : [{}]", objectUpload.getMsgId());

        ObjectUploadResult objectUploadResult = new ObjectUploadResult();
        try {
            objectUploadResult = obsHandler.objectUpload(objectUpload);
        } catch (CommonAdapterException e) {

            objectUploadResult.setSuccess(false);
            objectUploadResult.setErrCode(e.getErrCode());
            objectUploadResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            objectUploadResult.setSuccess(false);
            objectUploadResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            objectUploadResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            objectUploadResult.setSuccess(false);
            objectUploadResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            objectUploadResult.setErrMsg(e.getMessage());
        }

        objectUploadResult.setMsgId(objectUpload.getMsgId());
        return objectUploadResult;
    }

    public BucketNameScanResult handleMessage(BucketNameScan bucketNameScan) {
        log.info("receiving message for bucketNameScan buckets, virtual type : [{}]", bucketNameScan.getVirtEnvType());

        log.info("msg id : [{}]", bucketNameScan.getMsgId());

        BucketNameScanResult bucketNameScanResult = new BucketNameScanResult();
        try {
            bucketNameScanResult = obsHandler.bucketNameCheck(bucketNameScan);
        } catch (CommonAdapterException e) {

            bucketNameScanResult.setSuccess(false);
            bucketNameScanResult.setErrCode(e.getErrCode());
            bucketNameScanResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            bucketNameScanResult.setSuccess(false);
            bucketNameScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            bucketNameScanResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            bucketNameScanResult.setSuccess(false);
            bucketNameScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            bucketNameScanResult.setErrMsg(e.getMessage());
        }

        bucketNameScanResult.setMsgId(bucketNameScan.getMsgId());
        return bucketNameScanResult;
    }

    public BucketCreateResult handleMessage(BucketCreate bucketCreate) {
        log.info("receiving message for Create buckets, virtual type : [{}]", bucketCreate.getVirtEnvType());

        log.info("msg id : [{}]", bucketCreate.getMsgId());

        BucketCreateResult bucketCreateResult = new BucketCreateResult();
        try {
            bucketCreateResult = obsHandler.createBucket(bucketCreate);
        } catch (CommonAdapterException e) {

            bucketCreateResult.setSuccess(false);
            bucketCreateResult.setErrCode(e.getErrCode());
            bucketCreateResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            bucketCreateResult.setSuccess(false);
            bucketCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            bucketCreateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            bucketCreateResult.setSuccess(false);
            bucketCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            bucketCreateResult.setErrMsg(e.getMessage());
        }

        bucketCreateResult.setMsgId(bucketCreate.getMsgId());
        return bucketCreateResult;
    }

    public BucketRepeatCheckResult handleMessage(BucketRepectCheck repectCheck) {
        BucketRepeatCheckResult repeatCheckResult = new BucketRepeatCheckResult();
        try {
            repeatCheckResult = obsHandler.bucketRepectCheck(repectCheck);
        } catch (CommonAdapterException e) {
            repeatCheckResult.setSuccess(false);
            repeatCheckResult.setErrCode(e.getErrCode());
            repeatCheckResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            repeatCheckResult.setSuccess(false);
            repeatCheckResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            repeatCheckResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        }catch (Exception e) {
            repeatCheckResult.setSuccess(false);
            repeatCheckResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            repeatCheckResult.setErrMsg(e.getMessage());
        }

        repeatCheckResult.setMsgId(repectCheck.getMsgId());
        return repeatCheckResult;
    }

    public BucketDeleteResult handleMessage(BucketDelete bucketDelete) {
        log.info("receiving message for Delete buckets, virtual type : [{}]", bucketDelete.getVirtEnvType());

        log.info("msg id : [{}]", bucketDelete.getMsgId());

        BucketDeleteResult bucketDeleteResult = new BucketDeleteResult();
        try {
            bucketDeleteResult = obsHandler.deleteBucket(bucketDelete);
        } catch (CommonAdapterException e) {

            bucketDeleteResult.setSuccess(false);
            bucketDeleteResult.setErrCode(e.getErrCode());
            bucketDeleteResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            bucketDeleteResult.setSuccess(false);
            bucketDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            bucketDeleteResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            bucketDeleteResult.setSuccess(false);
            bucketDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            bucketDeleteResult.setErrMsg(e.getMessage());
        }

        bucketDeleteResult.setMsgId(bucketDelete.getMsgId());
        return bucketDeleteResult;
    }

    public BucketUpdateResult handleMessage(BucketUpdate bucketUpdate) {
        log.info("receiving message for Set buckets, virtual type : [{}]", bucketUpdate.getVirtEnvType());

        log.info("msg id : [{}]", bucketUpdate.getMsgId());

        BucketUpdateResult bucketUpdateResult = new BucketUpdateResult();
        try {
            bucketUpdateResult = obsHandler.updateBucket(bucketUpdate);
        } catch (CommonAdapterException e) {

            bucketUpdateResult.setSuccess(false);
            bucketUpdateResult.setErrCode(e.getErrCode());
            bucketUpdateResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            bucketUpdateResult.setSuccess(false);
            bucketUpdateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            bucketUpdateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            bucketUpdateResult.setSuccess(false);
            bucketUpdateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            bucketUpdateResult.setErrMsg(e.getMessage());
        }

        bucketUpdateResult.setMsgId(bucketUpdate.getMsgId());
        return bucketUpdateResult;
    }

    public VpcPeeringAcceptResult handleMessage(VpcPeeringAccept vpcPeeringAccept) {
        log.info("receiving message for Set buckets, virtual type : [{}]", vpcPeeringAccept.getVirtEnvType());

        log.info("msg id : [{}]", vpcPeeringAccept.getMsgId());

        VpcPeeringAcceptResult vpcPeeringAcceptResult = new VpcPeeringAcceptResult();
        try {
            vpcPeeringAcceptResult = netHandler.vpcPeeringAccept(vpcPeeringAccept);
        } catch (CommonAdapterException e) {

            vpcPeeringAcceptResult.setSuccess(false);
            vpcPeeringAcceptResult.setErrCode(e.getErrCode());
            vpcPeeringAcceptResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            vpcPeeringAcceptResult.setSuccess(false);
            vpcPeeringAcceptResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vpcPeeringAcceptResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            vpcPeeringAcceptResult.setSuccess(false);
            vpcPeeringAcceptResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vpcPeeringAcceptResult.setErrMsg(e.getMessage());
        }

        vpcPeeringAcceptResult.setMsgId(vpcPeeringAccept.getMsgId());
        return vpcPeeringAcceptResult;
    }

    public VpcPeeringDeleteResult handleMessage(VpcPeeringDelete vpcPeeringDelete) {
        log.info("receiving message for vpcPeeringDelete, virtual type : [{}]", vpcPeeringDelete.getVirtEnvType());

        log.info("msg id : [{}]", vpcPeeringDelete.getMsgId());

        VpcPeeringDeleteResult vpcPeeringDeleteResult = new VpcPeeringDeleteResult();
        try {
            vpcPeeringDeleteResult = netHandler.vpcPeeringDelete(vpcPeeringDelete);
        } catch (CommonAdapterException e) {

            vpcPeeringDeleteResult.setSuccess(false);
            vpcPeeringDeleteResult.setErrCode(e.getErrCode());
            vpcPeeringDeleteResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            vpcPeeringDeleteResult.setSuccess(false);
            vpcPeeringDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vpcPeeringDeleteResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            vpcPeeringDeleteResult.setSuccess(false);
            vpcPeeringDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vpcPeeringDeleteResult.setErrMsg(e.getMessage());
        }

        vpcPeeringDeleteResult.setMsgId(vpcPeeringDeleteResult.getMsgId());
        return vpcPeeringDeleteResult;
    }

    public RouteTableUpdateResult handleMessage(RouteTableUpdate routeTableUpdate) {
        log.info("receiving message for Set buckets, virtual type : [{}]", routeTableUpdate.getVirtEnvType());

        log.info("msg id : [{}]", routeTableUpdate.getMsgId());

        RouteTableUpdateResult routeTableUpdateResult = new RouteTableUpdateResult();
        try {
            routeTableUpdateResult = netHandler.routeTableUpdate(routeTableUpdate);
        } catch (CommonAdapterException e) {

            routeTableUpdateResult.setSuccess(false);
            routeTableUpdateResult.setErrCode(e.getErrCode());
            routeTableUpdateResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            routeTableUpdateResult.setSuccess(false);
            routeTableUpdateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            routeTableUpdateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            routeTableUpdateResult.setSuccess(false);
            routeTableUpdateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            routeTableUpdateResult.setErrMsg(e.getMessage());
        }

        routeTableUpdateResult.setMsgId(routeTableUpdate.getMsgId());
        return routeTableUpdateResult;
    }

    public BucketPolicyResult handleMessage(BucketPolicyUpdate bucketPolicyUpdate) {
        log.info("receiving message for update bucketPolicy, virtual type : [{}]", bucketPolicyUpdate.getVirtEnvType());

        log.info("msg id : [{}]", bucketPolicyUpdate.getMsgId());

        BucketPolicyResult bucketPolicyResult = new BucketPolicyResult();
        try {
            bucketPolicyResult = obsHandler.updateBucketPolicy(bucketPolicyUpdate);
        } catch (CommonAdapterException e) {

            bucketPolicyResult.setSuccess(false);
            bucketPolicyResult.setErrCode(e.getErrCode());
            bucketPolicyResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            bucketPolicyResult.setSuccess(false);
            bucketPolicyResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            bucketPolicyResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            bucketPolicyResult.setSuccess(false);
            bucketPolicyResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            bucketPolicyResult.setErrMsg(e.getMessage());
        }

        bucketPolicyResult.setMsgId(bucketPolicyUpdate.getMsgId());
        return bucketPolicyResult;
    }

    public BucketPolicyScanResult handleMessage(BucketPolicyScan bucketPolicyScan) {
        log.info("receiving message for scan bucketPolicy, virtual type : [{}]", bucketPolicyScan.getVirtEnvType());

        log.info("msg id : [{}]", bucketPolicyScan.getMsgId());

        BucketPolicyScanResult result = new BucketPolicyScanResult();
        try {
            result = scanHandler.scanBucketPolicyByEnv(bucketPolicyScan);
            result.setSuccess(true);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }

        BaseUtil.setResult(bucketPolicyScan, result);
        result.setMsgId(bucketPolicyScan.getMsgId());
        return result;
    }

    public ForObjUploadResult handleMessage(ForObjUpload forObjUpload) {
        log.info("receiving message for create forObjUpload, virtual type : [{}]", forObjUpload.getVirtEnvType());

        log.info("msg id : [{}]", forObjUpload.getMsgId());

        ForObjUploadResult forObjUploadResult = new ForObjUploadResult();
        try {
            forObjUploadResult = obsHandler.getForObjUpload(forObjUpload);
        } catch (CommonAdapterException e) {

            forObjUploadResult.setSuccess(false);
            forObjUploadResult.setErrCode(e.getErrCode());
            forObjUploadResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            forObjUploadResult.setSuccess(false);
            forObjUploadResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            forObjUploadResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            forObjUploadResult.setSuccess(false);
            forObjUploadResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            forObjUploadResult.setErrMsg(e.getMessage());
        }

        forObjUploadResult.setMsgId(forObjUpload.getMsgId());
        return forObjUploadResult;
    }

    public ObjectDeleteResult handleMessage(ObjectDelete objectDelete) {
        log.info("receiving message for create objectDelete, virtual type : [{}]", objectDelete.getVirtEnvType());

        log.info("msg id : [{}]", objectDelete.getMsgId());

        ObjectDeleteResult objectDeleteResult = new ObjectDeleteResult();
        try {
            objectDeleteResult = obsHandler.deleteObject(objectDelete);
        } catch (CommonAdapterException e) {

            objectDeleteResult.setSuccess(false);
            objectDeleteResult.setErrCode(e.getErrCode());
            objectDeleteResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            objectDeleteResult.setSuccess(false);
            objectDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            objectDeleteResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            objectDeleteResult.setSuccess(false);
            objectDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            objectDeleteResult.setErrMsg(e.getMessage());
        }

        objectDeleteResult.setMsgId(objectDelete.getMsgId());
        return objectDeleteResult;
    }

    public ObjectRestoreResult handleMessage(ObjectRestore objectRestore) {
        log.info("receiving message for create objectRestore, virtual type : [{}]", objectRestore.getVirtEnvType());

        log.info("msg id : [{}]", objectRestore.getMsgId());

        ObjectRestoreResult objectRestoreResult = new ObjectRestoreResult();
        try {
            objectRestoreResult = obsHandler.restoreObject(objectRestore);
        } catch (CommonAdapterException e) {

            objectRestoreResult.setSuccess(false);
            objectRestoreResult.setErrCode(e.getErrCode());
            objectRestoreResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            objectRestoreResult.setSuccess(false);
            objectRestoreResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            objectRestoreResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            objectRestoreResult.setSuccess(false);
            objectRestoreResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            objectRestoreResult.setErrMsg(e.getMessage());
        }

        objectRestoreResult.setMsgId(objectRestore.getMsgId());
        return objectRestoreResult;
    }

    public ObjectDownloadResult handleMessage(ObjectDownload objectDownload) {
        log.info("receiving message for create objectDownload, virtual type : [{}]", objectDownload.getVirtEnvType());

        log.info("msg id : [{}]", objectDownload.getMsgId());

        ObjectDownloadResult objectDownloadResult = new ObjectDownloadResult();
        try {
            objectDownloadResult = obsHandler.downloadObject(objectDownload);
        } catch (CommonAdapterException e) {

            objectDownloadResult.setSuccess(false);
            objectDownloadResult.setErrCode(e.getErrCode());
            objectDownloadResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            objectDownloadResult.setSuccess(false);
            objectDownloadResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            objectDownloadResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            objectDownloadResult.setSuccess(false);
            objectDownloadResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            objectDownloadResult.setErrMsg(e.getMessage());
        }

        objectDownloadResult.setMsgId(objectDownload.getMsgId());
        return objectDownloadResult;
    }

    public ObjectStorageCopyResult handleMessage(ObjectStorageCopy objectStorageCopy) {
        log.info("receiving message for create objectStorageCopy, virtual type : [{}]", objectStorageCopy.getVirtEnvType());

        log.info("msg id : [{}]", objectStorageCopy.getMsgId());

        ObjectStorageCopyResult objectStorageCopyResult = new ObjectStorageCopyResult();
        try {
            objectStorageCopyResult = obsHandler.objectCopy(objectStorageCopy);
        } catch (CommonAdapterException e) {

            objectStorageCopyResult.setSuccess(false);
            objectStorageCopyResult.setErrCode(e.getErrCode());
            objectStorageCopyResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            objectStorageCopyResult.setSuccess(false);
            objectStorageCopyResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            objectStorageCopyResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            objectStorageCopyResult.setSuccess(false);
            objectStorageCopyResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            objectStorageCopyResult.setErrMsg(e.getMessage());
        }

        objectStorageCopyResult.setMsgId(objectStorageCopy.getMsgId());
        return objectStorageCopyResult;
    }

    public ObjectCreateResult handleMessage(ObjectCreate objectCreate) {
        log.info("receiving message for create objectCreate, virtual type : [{}]", objectCreate.getVirtEnvType());

        log.info("msg id : [{}]", objectCreate.getMsgId());

        ObjectCreateResult objectCreateResult = new ObjectCreateResult();
        try {
            objectCreateResult = obsHandler.createObject(objectCreate);
        } catch (CommonAdapterException e) {

            objectCreateResult.setSuccess(false);
            objectCreateResult.setErrCode(e.getErrCode());
            objectCreateResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            objectCreateResult.setSuccess(false);
            objectCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            objectCreateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            objectCreateResult.setSuccess(false);
            objectCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            objectCreateResult.setErrMsg(e.getMessage());
        }

        objectCreateResult.setMsgId(objectCreate.getMsgId());
        return objectCreateResult;
    }

    public VmTemplateResult handleMessage(MarkVmAsTemplate markVmAsTemplate) {
        log.info("receiving message for mark Vm As Template, virtual type : [{}]", markVmAsTemplate.getVirtEnvType());

        log.info("msg id : [{}]", markVmAsTemplate.getMsgId());

        VmTemplateResult vmAsTemplateResult = new VmTemplateResult();
        try {
            vmAsTemplateResult = vmHandler.markVmAsTemplate(markVmAsTemplate);
        } catch (CommonAdapterException e) {

            vmAsTemplateResult.setSuccess(false);
            vmAsTemplateResult.setErrCode(e.getErrCode());
            vmAsTemplateResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            vmAsTemplateResult.setSuccess(false);
            vmAsTemplateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmAsTemplateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            vmAsTemplateResult.setSuccess(false);
            vmAsTemplateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmAsTemplateResult.setErrMsg(e.getMessage());
        }

        vmAsTemplateResult.setMsgId(markVmAsTemplate.getMsgId());
        return vmAsTemplateResult;
    }

    public SecurityGroupConfigResult handleMessage(SecurityGroupConfig securityGroupConfig) {
        log.info("receiving message for config Vm SecurityGroup , virtual type : [{}]", securityGroupConfig.getVirtEnvType());

        log.info("msg id : [{}]", securityGroupConfig.getMsgId());

        SecurityGroupConfigResult result = new SecurityGroupConfigResult();
        try {
            result = netHandler.configVmSg(securityGroupConfig);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }

        result.setMsgId(securityGroupConfig.getMsgId());
        return result;
    }

    /**
     * Handle message network export scan result.
     *
     * @param networkExportScan the network export scan
     *
     * @return the network export scan result
     */
    public NetworkExportScanResult handleMessage(NetworkExportScan networkExportScan) {

        log.info("receiving message for scanning network exports, virtual type : [{}]", networkExportScan.getVirtEnvType());

        log.info("msg id : [{}]", networkExportScan.getMsgId());

        NetworkExportScanResult networkExportScanResult = new NetworkExportScanResult();

        try {
            networkExportScanResult = scanHandler.scanNetworkExport(networkExportScan);
            networkExportScanResult.setSuccess(true);

        } catch (CommonAdapterException e) {

            networkExportScanResult.setSuccess(false);
            networkExportScanResult.setErrCode(e.getErrCode());
            networkExportScanResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            networkExportScanResult.setSuccess(false);
            networkExportScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            networkExportScanResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            networkExportScanResult.setSuccess(false);
            networkExportScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            networkExportScanResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(networkExportScan, networkExportScanResult);
        networkExportScanResult.setMsgId(networkExportScan.getMsgId());
        return networkExportScanResult;

    }

    public FwRuleCreateResult handleMessage(FwRuleCreate fwRuleCreate) {
        log.info("receiving message for create service chain firewall rule, virtual type : [{}]", fwRuleCreate.getVirtEnvType());

        log.info("msg id : [{}]", fwRuleCreate.getMsgId());

        FwRuleCreateResult ruleCreateResult = new FwRuleCreateResult();
        try {
            ruleCreateResult = netHandler.createRule(fwRuleCreate);
        } catch (CommonAdapterException e) {

            ruleCreateResult.setSuccess(false);
            ruleCreateResult.setErrCode(e.getErrCode());
            ruleCreateResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            ruleCreateResult.setSuccess(false);
            ruleCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            ruleCreateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            ruleCreateResult.setSuccess(false);
            ruleCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            ruleCreateResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(fwRuleCreate, ruleCreateResult);
        ruleCreateResult.setMsgId(fwRuleCreate.getMsgId());
        return ruleCreateResult;

    }

    public FwStrategyCreateResult handleMessage(FwStrategyCreate fwStrategyCreate) {
        log.info("receiving message for create service chain firewall Strategy, virtual type : [{}]", fwStrategyCreate.getVirtEnvType());

        log.info("msg id : [{}]", fwStrategyCreate.getMsgId());

        FwStrategyCreateResult strategyCreateResult = new FwStrategyCreateResult();
        try {
            strategyCreateResult = netHandler.createStrategy(fwStrategyCreate);
        } catch (CommonAdapterException e) {

            strategyCreateResult.setSuccess(false);
            strategyCreateResult.setErrCode(e.getErrCode());
            strategyCreateResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            strategyCreateResult.setSuccess(false);
            strategyCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            strategyCreateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            strategyCreateResult.setSuccess(false);
            strategyCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            strategyCreateResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(fwStrategyCreate, strategyCreateResult);
        strategyCreateResult.setMsgId(fwStrategyCreate.getMsgId());
        return strategyCreateResult;
    }

    public FirewallCreateResult handleMessage(FirewallCreate firewallCreate) {
        log.info("receiving message for create service chain firewall, virtual type : [{}]", firewallCreate.getVirtEnvType());

        log.info("msg id : [{}]", firewallCreate.getMsgId());

        FirewallCreateResult firewallCreateResult = new FirewallCreateResult();
        try {
            firewallCreateResult = netHandler.createFirewall(firewallCreate);
        } catch (CommonAdapterException e) {

            firewallCreateResult.setSuccess(false);
            firewallCreateResult.setErrCode(e.getErrCode());
            firewallCreateResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            firewallCreateResult.setSuccess(false);
            firewallCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            firewallCreateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            firewallCreateResult.setSuccess(false);
            firewallCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            firewallCreateResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(firewallCreate, firewallCreateResult);
        firewallCreateResult.setMsgId(firewallCreate.getMsgId());
        return firewallCreateResult;
    }

    public FwStrategyRuleAddResult handleMessage(FwStrategyRuleAdd strategyRuleAdd) {
        log.info("receiving message for add service chain firewall rule, virtual type : [{}]", strategyRuleAdd.getVirtEnvType());

        log.info("msg id : [{}]", strategyRuleAdd.getMsgId());

        FwStrategyRuleAddResult strategyRuleAddResult = new FwStrategyRuleAddResult();
        try {
            strategyRuleAddResult = netHandler.addScRule(strategyRuleAdd);
        } catch (CommonAdapterException e) {

            strategyRuleAddResult.setSuccess(false);
            strategyRuleAddResult.setErrCode(e.getErrCode());
            strategyRuleAddResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            strategyRuleAddResult.setSuccess(false);
            strategyRuleAddResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            strategyRuleAddResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            strategyRuleAddResult.setSuccess(false);
            strategyRuleAddResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            strategyRuleAddResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(strategyRuleAdd, strategyRuleAddResult);
        strategyRuleAddResult.setMsgId(strategyRuleAdd.getMsgId());
        return strategyRuleAddResult;
    }

    public FwStrategyRuleRemoveResult handleMessage(FwStrategyRuleRemove strategyRuleRemove) {
        log.info("receiving message for remove service chain firewall rule, virtual type : [{}]", strategyRuleRemove.getVirtEnvType());

        log.info("msg id : [{}]", strategyRuleRemove.getMsgId());

        FwStrategyRuleRemoveResult strategyRuleRemoveResult = new FwStrategyRuleRemoveResult();
        try {
            strategyRuleRemoveResult = netHandler.removeScRule(strategyRuleRemove);
        } catch (CommonAdapterException e) {

            strategyRuleRemoveResult.setSuccess(false);
            strategyRuleRemoveResult.setErrCode(e.getErrCode());
            strategyRuleRemoveResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            strategyRuleRemoveResult.setSuccess(false);
            strategyRuleRemoveResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            strategyRuleRemoveResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            strategyRuleRemoveResult.setSuccess(false);
            strategyRuleRemoveResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            strategyRuleRemoveResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(strategyRuleRemove, strategyRuleRemoveResult);
        strategyRuleRemoveResult.setMsgId(strategyRuleRemove.getMsgId());
        return strategyRuleRemoveResult;
    }

    public ScFirewallRuleScanResult handleMessage(ScFirewallRuleScan ruleScan) {
        log.info("receiving message for scan service chain firewall rule, virtual type : [{}]", ruleScan.getVirtEnvType());

        log.info("msg id : [{}]", ruleScan.getMsgId());

        ScFirewallRuleScanResult ruleScanResult = new ScFirewallRuleScanResult();
        try {
            ruleScanResult = scanHandler.scanScFirewallRule(ruleScan);
        } catch (CommonAdapterException e) {

            ruleScanResult.setSuccess(false);
            ruleScanResult.setErrCode(e.getErrCode());
            ruleScanResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            ruleScanResult.setSuccess(false);
            ruleScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            ruleScanResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            ruleScanResult.setSuccess(false);
            ruleScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            ruleScanResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(ruleScan, ruleScanResult);
        ruleScanResult.setMsgId(ruleScan.getMsgId());
        return ruleScanResult;
    }

    public ScFirewallStrategyScanResult handleMessage(ScFirewallStrategyScan strategyScan) {
        log.info("receiving message for scan service chain firewall strategy, virtual type : [{}]", strategyScan.getVirtEnvType());

        log.info("msg id : [{}]", strategyScan.getMsgId());

        ScFirewallStrategyScanResult strategyScanResult = new ScFirewallStrategyScanResult();
        try {
            strategyScanResult = scanHandler.scanScFirewallStrategy(strategyScan);
        } catch (CommonAdapterException e) {

            strategyScanResult.setSuccess(false);
            strategyScanResult.setErrCode(e.getErrCode());
            strategyScanResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            strategyScanResult.setSuccess(false);
            strategyScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            strategyScanResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            strategyScanResult.setSuccess(false);
            strategyScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            strategyScanResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(strategyScan, strategyScanResult);
        strategyScanResult.setMsgId(strategyScan.getMsgId());
        return strategyScanResult;
    }

    public ScFirewallScanResult handleMessage(ScFirewallScan firewallScan) {
        log.info("receiving message for scan service chain firewall, virtual type : [{}]", firewallScan.getVirtEnvType());

        log.info("msg id : [{}]", firewallScan.getMsgId());

        ScFirewallScanResult firewallScanResult = new ScFirewallScanResult();
        try {
            firewallScanResult = scanHandler.scanScFirewall(firewallScan);
        } catch (CommonAdapterException e) {

            firewallScanResult.setSuccess(false);
            firewallScanResult.setErrCode(e.getErrCode());
            firewallScanResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            firewallScanResult.setSuccess(false);
            firewallScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            firewallScanResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            firewallScanResult.setSuccess(false);
            firewallScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            firewallScanResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(firewallScan, firewallScanResult);
        firewallScanResult.setMsgId(firewallScan.getMsgId());
        return firewallScanResult;
    }

    public FwStrategyDeleteResult handleMessage(FwStrategyDelete strategyDelete) {
        log.info("receiving message for delete service chain firewall strategy, virtual type : [{}]", strategyDelete.getVirtEnvType());

        log.info("msg id : [{}]", strategyDelete.getMsgId());

        FwStrategyDeleteResult strategyDeleteResult = new FwStrategyDeleteResult();
        try {
            strategyDeleteResult = netHandler.deleteScStrategy(strategyDelete);
        } catch (CommonAdapterException e) {

            strategyDeleteResult.setSuccess(false);
            strategyDeleteResult.setErrCode(e.getErrCode());
            strategyDeleteResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            strategyDeleteResult.setSuccess(false);
            strategyDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            strategyDeleteResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            strategyDeleteResult.setSuccess(false);
            strategyDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            strategyDeleteResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(strategyDelete, strategyDeleteResult);
        strategyDeleteResult.setMsgId(strategyDelete.getMsgId());
        return strategyDeleteResult;
    }

    public FirewallDeleteResult handleMessage(FirewallDelete firewallDelete) {
        log.info("receiving message for delete service chain firewall, virtual type : [{}]", firewallDelete.getVirtEnvType());

        log.info("msg id : [{}]", firewallDelete.getMsgId());

        FirewallDeleteResult firewallDeleteResult = new FirewallDeleteResult();
        try {
            firewallDeleteResult = netHandler.deleteScFirewall(firewallDelete);
        } catch (CommonAdapterException e) {

            firewallDeleteResult.setSuccess(false);
            firewallDeleteResult.setErrCode(e.getErrCode());
            firewallDeleteResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            firewallDeleteResult.setSuccess(false);
            firewallDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            firewallDeleteResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            firewallDeleteResult.setSuccess(false);
            firewallDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            firewallDeleteResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(firewallDelete, firewallDeleteResult);
        firewallDeleteResult.setMsgId(firewallDelete.getMsgId());
        return firewallDeleteResult;
    }

    public FwRuleUpdateResult handleMessage(FwRuleUpdate ruleUpdate) {
        log.info("receiving message for update service chain firewall rule, virtual type : [{}]", ruleUpdate.getVirtEnvType());

        log.info("msg id : [{}]", ruleUpdate.getMsgId());

        FwRuleUpdateResult fwRuleUpdateResult = new FwRuleUpdateResult();
        try {
            fwRuleUpdateResult = netHandler.updateScFirewallRule(ruleUpdate);
        } catch (CommonAdapterException e) {

            fwRuleUpdateResult.setSuccess(false);
            fwRuleUpdateResult.setErrCode(e.getErrCode());
            fwRuleUpdateResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            fwRuleUpdateResult.setSuccess(false);
            fwRuleUpdateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            fwRuleUpdateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            fwRuleUpdateResult.setSuccess(false);
            fwRuleUpdateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            fwRuleUpdateResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(ruleUpdate, fwRuleUpdateResult);
        fwRuleUpdateResult.setMsgId(ruleUpdate.getMsgId());
        return fwRuleUpdateResult;
    }

    public FwStrategyUpdateResult handleMessage(FwStrategyUpdate strategyUpdate) {
        log.info("receiving message for update service chain firewall strategy, virtual type : [{}]", strategyUpdate.getVirtEnvType());

        log.info("msg id : [{}]", strategyUpdate.getMsgId());

        FwStrategyUpdateResult fwStrategyUpdateResult = new FwStrategyUpdateResult();
        try {
            fwStrategyUpdateResult = netHandler.updateScFirewallStrategy(strategyUpdate);
        } catch (CommonAdapterException e) {

            fwStrategyUpdateResult.setSuccess(false);
            fwStrategyUpdateResult.setErrCode(e.getErrCode());
            fwStrategyUpdateResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            fwStrategyUpdateResult.setSuccess(false);
            fwStrategyUpdateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            fwStrategyUpdateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            fwStrategyUpdateResult.setSuccess(false);
            fwStrategyUpdateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            fwStrategyUpdateResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(strategyUpdate, fwStrategyUpdateResult);
        fwStrategyUpdateResult.setMsgId(strategyUpdate.getMsgId());
        return fwStrategyUpdateResult;
    }

    public FirewallUpdateResult handleMessage(FirewallUpdate firewallUpdate) {
        log.info("receiving message for update service chain firewall, virtual type : [{}]", firewallUpdate.getVirtEnvType());

        log.info("msg id : [{}]", firewallUpdate.getMsgId());

        FirewallUpdateResult firewallUpdateResult = new FirewallUpdateResult();
        try {
            firewallUpdateResult = netHandler.updateScFirewall(firewallUpdate);
        } catch (CommonAdapterException e) {

            firewallUpdateResult.setSuccess(false);
            firewallUpdateResult.setErrCode(e.getErrCode());
            firewallUpdateResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            firewallUpdateResult.setSuccess(false);
            firewallUpdateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            firewallUpdateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            firewallUpdateResult.setSuccess(false);
            firewallUpdateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            firewallUpdateResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(firewallUpdate, firewallUpdateResult);
        firewallUpdateResult.setMsgId(firewallUpdate.getMsgId());
        return firewallUpdateResult;
    }

    /**
     * Handle message service chains scan result.
     *
     * @param serviceChainScan the service chains scan
     *
     * @return the service chains scan result
     */
    public ServiceChainScanResult handleMessage(ServiceChainScan serviceChainScan) {

        log.info(
                "receiving message for scanning service chains, virtual type : [{}]", serviceChainScan.getVirtEnvType());

        log.info("msg id : [{}]", serviceChainScan.getMsgId());

        ServiceChainScanResult serviceChainScanResult = new ServiceChainScanResult();

        try {
            serviceChainScanResult = scanHandler.scanServiceChain(serviceChainScan);
            serviceChainScanResult.setSuccess(true);

        } catch (CommonAdapterException e) {

            serviceChainScanResult.setSuccess(false);
            serviceChainScanResult.setErrCode(e.getErrCode());
            serviceChainScanResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            serviceChainScanResult.setSuccess(false);
            serviceChainScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            serviceChainScanResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            serviceChainScanResult.setSuccess(false);
            serviceChainScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            serviceChainScanResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(serviceChainScan, serviceChainScanResult);
        serviceChainScanResult.setMsgId(serviceChainScanResult.getMsgId());
        return serviceChainScanResult;

    }

    public CloudOsOwnerResult handleMessage(ScanCloudOsOwnerByEnv ownerByEnv) {
        CloudOsOwnerResult result = new CloudOsOwnerResult();
        try {
            CloudOsOwnerResult ownerResult = scanHandler.scanCloudOsOwnersByEnv(ownerByEnv);
            ownerResult.setMsgId(ownerByEnv.getMsgId());
            return ownerResult;
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

            log.error(Throwables.getStackTraceAsString(e));
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);

            log.error(Throwables.getStackTraceAsString(e));
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        return result;
    }

    public CloudOsBaremetalResult handleMessage(ScanCloudOsBaremetalByEnv ownerByEnv) {
        CloudOsBaremetalResult result = new CloudOsBaremetalResult();
        try {
            CloudOsBaremetalResult ownerResult = scanHandler.scanCloudOsBaremetal(ownerByEnv);
            ownerResult.setMsgId(ownerByEnv.getMsgId());
            return ownerResult;
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

            log.error(Throwables.getStackTraceAsString(e));
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);

            log.error(Throwables.getStackTraceAsString(e));
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        return result;
    }

    public BaremetalZoneResult handleMessage(ScanCloudOsBareMetalZone ownerByEnv) {
        BaremetalZoneResult result = new BaremetalZoneResult();
        try {
            BaremetalZoneResult ownerResult = scanHandler.scanCloudOsBaremetalZone(ownerByEnv);
            ownerResult.setMsgId(ownerByEnv.getMsgId());
            return ownerResult;
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

            log.error(Throwables.getStackTraceAsString(e));
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);

            log.error(Throwables.getStackTraceAsString(e));
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        return result;
    }

    public BaremetalNodeResult handleMessage(ScanCloudOsBareMetalNode ownerByEnv) {
        BaremetalNodeResult result = new BaremetalNodeResult();
        try {
            BaremetalNodeResult ownerResult = scanHandler.scanCloudOsBaremetalNode(ownerByEnv);
            ownerResult.setMsgId(ownerByEnv.getMsgId());
            return ownerResult;
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

            log.error(Throwables.getStackTraceAsString(e));
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);

            log.error(Throwables.getStackTraceAsString(e));
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        return result;
    }

    public ZoneNetworkResult handleMessage(ScanCloudOsZoneNetwork ownerByEnv) {
        ZoneNetworkResult result = new ZoneNetworkResult();
        try {
            ZoneNetworkResult ownerResult = scanHandler.scanCloudOsZoneNetwork(ownerByEnv);
            ownerResult.setMsgId(ownerByEnv.getMsgId());
            return ownerResult;
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

            log.error(Throwables.getStackTraceAsString(e));
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);

            log.error(Throwables.getStackTraceAsString(e));
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        return result;
    }

    public FlowFeatureGroupScanResult handleMessage(ScanFlowFeatureGroup ownerByEnv) {
        FlowFeatureGroupScanResult result = new FlowFeatureGroupScanResult();
        try {
            FlowFeatureGroupScanResult ownerResult = scanHandler.scanCloudOsFlowFeatureGroup(ownerByEnv);
            ownerResult.setMsgId(ownerByEnv.getMsgId());
            ownerResult.setSuccess(true);
            return ownerResult;
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

            log.error(Throwables.getStackTraceAsString(e));
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);

            log.error(Throwables.getStackTraceAsString(e));
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        return result;
    }

    public ScanFirewallObjectGroupsResult handleMessage(ScanFirewallObjectGroups objectGroups) {
        ScanFirewallObjectGroupsResult result = new ScanFirewallObjectGroupsResult();
        try {
            ScanFirewallObjectGroupsResult ownerResult = scanHandler.scanFirewallObjectGroups(objectGroups);
            ownerResult.setMsgId(objectGroups.getMsgId());
            return ownerResult;
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

            log.error(Throwables.getStackTraceAsString(e));
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);

            log.error(Throwables.getStackTraceAsString(e));
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        return result;
    }

    public VpcFireWallObjectGroupsResult handleMessage(VpcFireWallObjectGroupsCreate objectGroupsCreate) {
        VpcFireWallObjectGroupsResult result = new VpcFireWallObjectGroupsResult();
        try {
            VpcFireWallObjectGroupsResult objectGroupsResult = netHandler.createVpcFireWallObjectGroups(
                    objectGroupsCreate);
            objectGroupsResult.setMsgId(objectGroupsCreate.getMsgId());
            return objectGroupsResult;
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

            log.error(Throwables.getStackTraceAsString(e));
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);

            log.error(Throwables.getStackTraceAsString(e));
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        return result;
    }

    public VpcFireWallObjectGroupsResult handleMessage(VpcFireWallObjectGroupsUpdate objectGroupsUpdate) {
        VpcFireWallObjectGroupsResult result = new VpcFireWallObjectGroupsResult();
        try {
            VpcFireWallObjectGroupsResult objectGroupsResult = netHandler.updateVpcFireWallObjectGroups(
                    objectGroupsUpdate);
            objectGroupsResult.setMsgId(objectGroupsUpdate.getMsgId());
            return objectGroupsResult;
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

            log.error(Throwables.getStackTraceAsString(e));
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);

            log.error(Throwables.getStackTraceAsString(e));
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        return result;
    }

    public VpcFireWallObjectGroupsResult handleMessage(VpcFireWallObjectGroupsDelete objectGroupsDelete) {
        VpcFireWallObjectGroupsResult result = new VpcFireWallObjectGroupsResult();
        try {
            VpcFireWallObjectGroupsResult objectGroupsResult = netHandler.deleteVpcFireWallObjectGroups(
                    objectGroupsDelete);
            objectGroupsResult.setMsgId(objectGroupsDelete.getMsgId());
            return objectGroupsResult;
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

            log.error(Throwables.getStackTraceAsString(e));
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);

            log.error(Throwables.getStackTraceAsString(e));
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        return result;
    }

    public VpcFireWallObjectsResult handleMessage(VpcFireWallObjectsCreate objectsCreate) {
        VpcFireWallObjectsResult result = new VpcFireWallObjectsResult();
        try {
            VpcFireWallObjectsResult objectsResult = netHandler.createVpcFireWallObjects(objectsCreate);
            objectsResult.setMsgId(objectsCreate.getMsgId());
            return objectsResult;
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

            log.error(Throwables.getStackTraceAsString(e));
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);

            log.error(Throwables.getStackTraceAsString(e));
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        return result;
    }

    public VpcFireWallObjectsResult handleMessage(VpcFireWallObjectsUpdate objectsUpdate) {
        VpcFireWallObjectsResult result = new VpcFireWallObjectsResult();
        try {
            VpcFireWallObjectsResult objectsResult = netHandler.updateVpcFireWallObjects(objectsUpdate);
            objectsResult.setMsgId(objectsUpdate.getMsgId());
            return objectsResult;
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

            log.error(Throwables.getStackTraceAsString(e));
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);

            log.error(Throwables.getStackTraceAsString(e));
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        return result;
    }

    public VpcFireWallObjectsResult handleMessage(VpcFireWallObjectsDelete objectsDelete) {
        VpcFireWallObjectsResult result = new VpcFireWallObjectsResult();
        try {
            VpcFireWallObjectsResult objectsResult = netHandler.deleteVpcFireWallObjects(objectsDelete);
            objectsResult.setMsgId(objectsDelete.getMsgId());
            return objectsResult;
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

            log.error(Throwables.getStackTraceAsString(e));
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);

            log.error(Throwables.getStackTraceAsString(e));
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        return result;
    }

    public VPCFirewallScanResult handleMessage(VPCFirewallScan firewallScan) {

        log.info("receiving message for scanning vpc firewall, virtual type : [{}]", firewallScan.getVirtEnvType());

        log.info("msg id : [{}]", firewallScan.getMsgId());

        VPCFirewallScanResult firewallScanResult = new VPCFirewallScanResult();

        try {
            firewallScanResult = scanHandler.scanCloudOsVpcFirewall(firewallScan);
            firewallScanResult.setSuccess(true);

        } catch (CommonAdapterException e) {

            firewallScanResult.setSuccess(false);
            firewallScanResult.setErrCode(e.getErrCode());
            firewallScanResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            firewallScanResult.setSuccess(false);
            firewallScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            firewallScanResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            firewallScanResult.setSuccess(false);
            firewallScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            firewallScanResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(firewallScan, firewallScanResult);
        firewallScanResult.setMsgId(firewallScan.getMsgId());
        return firewallScanResult;

    }

    public FwStrategyRuleMoveResult handleMessage(FwStrategyRuleMove strategyRuleMove) {
        log.info("receiving message for move service chain firewall rule, virtual type : [{}]", strategyRuleMove.getVirtEnvType());

        log.info("msg id : [{}]", strategyRuleMove.getMsgId());

        FwStrategyRuleMoveResult strategyRuleMoveResult = new FwStrategyRuleMoveResult();
        try {
            strategyRuleMoveResult = netHandler.moveScRule(strategyRuleMove);
        } catch (CommonAdapterException e) {

            strategyRuleMoveResult.setSuccess(false);
            strategyRuleMoveResult.setErrCode(e.getErrCode());
            strategyRuleMoveResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            strategyRuleMoveResult.setSuccess(false);
            strategyRuleMoveResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            strategyRuleMoveResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            strategyRuleMoveResult.setSuccess(false);
            strategyRuleMoveResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            strategyRuleMoveResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(strategyRuleMove, strategyRuleMoveResult);
        strategyRuleMoveResult.setMsgId(strategyRuleMove.getMsgId());
        return strategyRuleMoveResult;
    }

    public ScanSharesRightsGroupByEnvResult handleMessage(ScanSharesRightsGroupByEnv rightsGroupByEnv) {
        ScanSharesRightsGroupByEnvResult rightsGroupByEnvResult = new ScanSharesRightsGroupByEnvResult();
        try {
            rightsGroupByEnvResult = scanHandler.scanSharesRightsGroupByEnv(rightsGroupByEnv);
        } catch (CommonAdapterException e) {
            rightsGroupByEnvResult.setSuccess(false);
            rightsGroupByEnvResult.setErrCode(e.getErrCode());
            rightsGroupByEnvResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            rightsGroupByEnvResult.setSuccess(false);
            rightsGroupByEnvResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            rightsGroupByEnvResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            rightsGroupByEnvResult.setSuccess(false);
            rightsGroupByEnvResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            rightsGroupByEnvResult.setErrMsg(e.getMessage());
        }
        rightsGroupByEnvResult.setMsgId(rightsGroupByEnv.getMsgId());

        return rightsGroupByEnvResult;
    }

    /**
     * Handle message pirce calculate result.
     */
    public DBInstanceDatabaseCreateResult handleMessage(DBInstanceDatabaseCreate dbInstanceDatabaseCreate) {

        log.info("receiving message for create rds database, virtual type : [{}]", dbInstanceDatabaseCreate.getVirtEnvType());

        log.info("msg id : [{}]", dbInstanceDatabaseCreate.getMsgId());

        DBInstanceDatabaseCreateResult databaseCreateResult = new DBInstanceDatabaseCreateResult();

        try {
            databaseCreateResult = rdsHandler.createDBInstanceDatabase(dbInstanceDatabaseCreate);
        } catch (CommonAdapterException e) {

            databaseCreateResult.setSuccess(false);
            databaseCreateResult.setErrCode(e.getErrCode());
            databaseCreateResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            databaseCreateResult.setSuccess(false);
            databaseCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            databaseCreateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            databaseCreateResult.setSuccess(false);
            databaseCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            databaseCreateResult.setErrMsg(e.getMessage());
        }

        databaseCreateResult.setMsgId(dbInstanceDatabaseCreate.getMsgId());

        return databaseCreateResult;

    }

    public DBInstanceDatabaseDeleteResult handleMessage(DBInstanceDatabaseDelete dbInstanceDatabaseDelete) {

        log.info("receiving message for delete rds database, virtual type : [{}]", dbInstanceDatabaseDelete.getVirtEnvType());

        log.info("msg id : [{}]", dbInstanceDatabaseDelete.getMsgId());

        DBInstanceDatabaseDeleteResult databaseDeleteResult = new DBInstanceDatabaseDeleteResult();

        try {
            databaseDeleteResult = rdsHandler.deleteDBInstanceDatabase(dbInstanceDatabaseDelete);
        } catch (CommonAdapterException e) {

            databaseDeleteResult.setSuccess(false);
            databaseDeleteResult.setErrCode(e.getErrCode());
            databaseDeleteResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            databaseDeleteResult.setSuccess(false);
            databaseDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            databaseDeleteResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            databaseDeleteResult.setSuccess(false);
            databaseDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            databaseDeleteResult.setErrMsg(e.getMessage());
        }

        databaseDeleteResult.setMsgId(dbInstanceDatabaseDelete.getMsgId());

        return databaseDeleteResult;

    }

    public DBInstanceDatabasePrivilegeResult handleMessage(DBInstanceDatabasePrivilege dbInstanceDatabasePrivilege) {

        log.info("receiving message for privilege rds database, virtual type : [{}]", dbInstanceDatabasePrivilege.getVirtEnvType());

        log.info("msg id : [{}]", dbInstanceDatabasePrivilege.getMsgId());

        DBInstanceDatabasePrivilegeResult databasePrivilegeResult = new DBInstanceDatabasePrivilegeResult();

        try {
            databasePrivilegeResult = rdsHandler.privilegeDBInstanceDatabase(dbInstanceDatabasePrivilege);
        } catch (CommonAdapterException e) {

            databasePrivilegeResult.setSuccess(false);
            databasePrivilegeResult.setErrCode(e.getErrCode());
            databasePrivilegeResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            databasePrivilegeResult.setSuccess(false);
            databasePrivilegeResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            databasePrivilegeResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            databasePrivilegeResult.setSuccess(false);
            databasePrivilegeResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            databasePrivilegeResult.setErrMsg(e.getMessage());
        }

        databasePrivilegeResult.setMsgId(dbInstanceDatabasePrivilege.getMsgId());

        return databasePrivilegeResult;

    }

    public BaseResult handleMessage(AccountDelete accountDelete) {
        log.info("receiving message for delete account, virtual type : [{}]", accountDelete.getVirtEnvType());

        log.info("msg id : [{}]", accountDelete.getMsgId());
        AccountResult result = new AccountResult();

        try {
            result = rdsHandler.deleteAccount(accountDelete);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(accountDelete, result);
        result.setMsgId(accountDelete.getMsgId());
        return result;
    }

    public BaseResult handleMessage(DeleteHPCAccount accountDelete) {
        log.info("receiving message for delete account, virtual type : [{}]", accountDelete.getVirtEnvType());

        log.info("msg id : [{}]", accountDelete.getMsgId());
        DeleteHPCAccountResult result = new DeleteHPCAccountResult();

        try {
            result = rdsHandler.deleteHPCAccount(accountDelete);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(accountDelete, result);
        result.setMsgId(accountDelete.getMsgId());
        return result;
    }

    public ScanSharesZonesByEnvResult handleMessage(ScanSharesZonesByEnv zonesByEnv) {
        ScanSharesZonesByEnvResult zonesByEnvResult = new ScanSharesZonesByEnvResult();

        try {
            zonesByEnvResult = scanHandler.scanSharesZonesByEnv(zonesByEnv);
        } catch (CommonAdapterException e) {
            zonesByEnvResult.setSuccess(false);
            zonesByEnvResult.setErrCode(e.getErrCode());
            zonesByEnvResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            zonesByEnvResult.setSuccess(false);
            zonesByEnvResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            zonesByEnvResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            zonesByEnvResult.setSuccess(false);
            zonesByEnvResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            zonesByEnvResult.setErrMsg(e.getMessage());
        }
        zonesByEnvResult.setMsgId(zonesByEnv.getMsgId());

        return zonesByEnvResult;
    }

    public MqInstanceScanResult handleMessage(MqInstanceScan mqInstanceScan) {

        log.info("receiving message for scanning mq instance,the type of engine  : [{}]", mqInstanceScan.getEngine());
        log.info("msg id : [{}]", mqInstanceScan.getMsgId());

        MqInstanceScanResult instanceScanResult = new MqInstanceScanResult();
        try {
            instanceScanResult = scanHandler.scanMqInstances(mqInstanceScan);
            instanceScanResult.setSuccess(true);
        } catch (CommonAdapterException e) {
            instanceScanResult.setSuccess(false);
            instanceScanResult.setErrCode(e.getErrCode());
            instanceScanResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            instanceScanResult.setSuccess(false);
            instanceScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            instanceScanResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            instanceScanResult.setSuccess(false);
            instanceScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            instanceScanResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(mqInstanceScan, instanceScanResult);
        instanceScanResult.setMsgId(mqInstanceScan.getMsgId());
        return instanceScanResult;

    }

    public KafkaTopicScanResult handleMessage(KafkaTopicScan kafkaTopicScan) {

        log.info("receiving message for scanning kafka topic, belong instance : [{}]", kafkaTopicScan.getInstanceId());

        log.info("msg id : [{}]", kafkaTopicScan.getMsgId());

        KafkaTopicScanResult topicScanResult = new KafkaTopicScanResult();

        try {
            topicScanResult = scanHandler.scanKafkaTopics(kafkaTopicScan);
            topicScanResult.setSuccess(true);
        } catch (CommonAdapterException e) {
            topicScanResult.setSuccess(false);
            topicScanResult.setErrCode(e.getErrCode());
            topicScanResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            topicScanResult.setSuccess(false);
            topicScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            topicScanResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            topicScanResult.setSuccess(false);
            topicScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            topicScanResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(kafkaTopicScan, topicScanResult);
        topicScanResult.setMsgId(kafkaTopicScan.getMsgId());
        return topicScanResult;

    }

    public GpuDeviceScanResult handleMessage(GpuDeviceScan gpuDeviceScan) {

        log.info("receiving message for scanning GpuDevice Scan, belong instance : [{}]");

        log.info("msg id : [{}]", gpuDeviceScan.getMsgId());

        GpuDeviceScanResult gpuDeviceScanResult = new GpuDeviceScanResult();

        try {
            gpuDeviceScanResult = scanHandler.scanGpuDevices(gpuDeviceScan);
            gpuDeviceScanResult.setSuccess(true);
        } catch (CommonAdapterException e) {
            gpuDeviceScanResult.setSuccess(false);
            gpuDeviceScanResult.setErrCode(e.getErrCode());
            gpuDeviceScanResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            gpuDeviceScanResult.setSuccess(false);
            gpuDeviceScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            gpuDeviceScanResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            gpuDeviceScanResult.setSuccess(false);
            gpuDeviceScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            gpuDeviceScanResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(gpuDeviceScan, gpuDeviceScanResult);
        gpuDeviceScanResult.setMsgId(gpuDeviceScan.getMsgId());
        return gpuDeviceScanResult;

    }

    public GpuGroupScanResult handleMessage(GpuGroupScan gpuGroupScan) {

        log.info("receiving message for scanning GpuGroup Scan, belong instance : [{}]");

        log.info("msg id : [{}]", gpuGroupScan.getMsgId());

        GpuGroupScanResult gpuGroupScanResult = new GpuGroupScanResult();

        try {
            gpuGroupScanResult = scanHandler.scanGpuGroups(gpuGroupScan);
            gpuGroupScanResult.setSuccess(true);
        } catch (CommonAdapterException e) {
            gpuGroupScanResult.setSuccess(false);
            gpuGroupScanResult.setErrCode(e.getErrCode());
            gpuGroupScanResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            gpuGroupScanResult.setSuccess(false);
            gpuGroupScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            gpuGroupScanResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            gpuGroupScanResult.setSuccess(false);
            gpuGroupScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            gpuGroupScanResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(gpuGroupScan, gpuGroupScanResult);
        gpuGroupScanResult.setMsgId(gpuGroupScan.getMsgId());
        return gpuGroupScanResult;

    }

    public DcsAvailableZoneScanResult handleMessage(DcsAvailableZoneScan scan) {
        log.info("receiving message for move service chain dcsavailble zone, virtual type : [{}]",
                 scan.getVirtEnvType());
        log.info("msg id : [{}]", scan.getMsgId());

        DcsAvailableZoneScanResult result = new DcsAvailableZoneScanResult();
        try {
            result = scanHandler.getDcsAvailableZones(scan);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(scan, result);
        result.setMsgId(scan.getMsgId());
        return result;
    }

    public DcsProductScanResult handleMessage(DcsProductScan scan) {
        log.info("receiving message for move service chain dcs product, virtual type : [{}]", scan.getVirtEnvType());
        log.info("msg id : [{}]", scan.getMsgId());

        DcsProductScanResult result = new DcsProductScanResult();
        try {
            result = scanHandler.getDcsProducts(scan);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(scan, result);
        result.setMsgId(scan.getMsgId());
        return result;
    }

    public DcsScanResult handleMessage(DcsScan scan) {
        log.info("receiving message for move service chain dcs , virtual type : [{}]", scan.getVirtEnvType());
        log.info("msg id : [{}]", scan.getMsgId());

        DcsScanResult result = new DcsScanResult();
        try {
            result = scanHandler.getDcs(scan);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(scan, result);
        result.setMsgId(scan.getMsgId());
        return result;
    }

    public DcsConfigScanResult handleMessage(DcsConfigScan scan) {
        log.info("receiving message for move service chain dcs config , virtual type : [{}]", scan.getVirtEnvType());
        log.info("msg id : [{}]", scan.getMsgId());

        DcsConfigScanResult result = new DcsConfigScanResult();
        try {
            result = scanHandler.getDcsConfig(scan);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(scan, result);
        result.setMsgId(scan.getMsgId());
        return result;
    }

    public PriceCalculateResult handleMessage(HuaweiPriceCalculate scan) {
        log.info("receiving message for move service chain dcs config , virtual type : [{}]", scan.getVirtEnvType());
        log.info("msg id : [{}]", scan.getMsgId());

        PriceCalculateResult result = new PriceCalculateResult();
        try {
            result = dcsHandler.getPrice(scan);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(scan, result);
        result.setMsgId(scan.getMsgId());
        return result;
    }

    /**
     * Handle message for region create.
     *
     * @param regionCreate the region create
     *
     * @return RegionCreateResult
     */
    public RegionCreateResult handleMessage(RegionCreate regionCreate) {
        log.info("receiving message for create region, virtual type : [{}]", regionCreate.getVirtEnvType());

        log.info("msg id : [{}]", regionCreate.getMsgId());
        RegionCreateResult result = new RegionCreateResult();
        try {
            result = adminHandler.createRegion(regionCreate);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(regionCreate, result);
        result.setMsgId(regionCreate.getMsgId());
        if (result.isSuccess()) {
            log.info("[adaptor] region has been created successfully");
        } else {
            log.info("[adaptor] region has been created failure");
        }
        return result;
    }

    /**
     * Handle message for region update.
     *
     * @param regionUpdate the region update
     *
     * @return RegionCreateResult
     */
    public RegionUpdateResult handleMessage(RegionUpdate regionUpdate) {
        log.info("receiving message for update region, virtual type : [{}]", regionUpdate.getVirtEnvType());

        log.info("msg id : [{}]", regionUpdate.getMsgId());
        RegionUpdateResult result = new RegionUpdateResult();
        try {
            result = adminHandler.updateRegion(regionUpdate);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(regionUpdate, result);
        result.setMsgId(regionUpdate.getMsgId());
        if (result.isSuccess()) {
            log.info("[adaptor] region has been updated successfully");
        } else {
            log.info("[adaptor] region has been updated failure");
        }
        return result;
    }

    /**
     * Handle message for region delete.
     *
     * @param regionDelete the region delete
     *
     * @return RegionDeleteResult
     */
    public RegionDeleteResult handleMessage(RegionDelete regionDelete) {
        log.info("receiving message for delete region, virtual type : [{}]", regionDelete.getVirtEnvType());

        log.info("msg id : [{}]", regionDelete.getMsgId());
        RegionDeleteResult result = new RegionDeleteResult();
        try {
            result = adminHandler.deleteRegion(regionDelete);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(regionDelete, result);
        result.setMsgId(regionDelete.getMsgId());
        if (result.isSuccess()) {
            log.info("[adaptor] region has been deleted successfully");
        } else {
            log.info("[adaptor] region has been deleted failure");
        }
        return result;
    }

    /**
     * Handle message for scan host aggregate
     *
     * @param hostAggregateScan scan host aggregate
     *
     * @return HostAggregateScanResult
     */
    public HostAggregateScanResult handleMessage(HostAggregateScan hostAggregateScan) {
        log.info("receiving message for scan host aggregate, virtual type : [{}]", hostAggregateScan.getVirtEnvType());

        log.info("msg id : [{}]", hostAggregateScan.getMsgId());
        HostAggregateScanResult result = new HostAggregateScanResult();
        try {
            result = adminHandler.queryHostAggregates(hostAggregateScan);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(hostAggregateScan, result);
        result.setMsgId(hostAggregateScan.getMsgId());
        if (result.isSuccess()) {
            log.info("[adaptor] the host aggregate has been listed successfully");
        } else {
            log.info("[adaptor] the host aggregate has been listed failure");
        }
        return result;
    }

    /**
     * Handle message for create host aggregate
     *
     * @param hostAggregateCreate create host aggregate
     *
     * @return HostAggregateCreateResult
     */
    public HostAggregateCreateResult handleMessage(HostAggregateCreate hostAggregateCreate) {
        log.info("receiving message for create host aggregate, virtual type : [{}]", hostAggregateCreate.getVirtEnvType());

        log.info("msg id : [{}]", hostAggregateCreate.getMsgId());
        HostAggregateCreateResult result = new HostAggregateCreateResult();
        try {
            result = adminHandler.createHostAggregates(hostAggregateCreate);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(hostAggregateCreate, result);
        result.setMsgId(hostAggregateCreate.getMsgId());
        if (result.isSuccess()) {
            log.info("[adaptor] the host aggregate has been created successfully");
        } else {
            log.info("[adaptor] the host aggregate has been created failure");
        }
        return result;
    }

    /**
     * Handle message for update host aggregate
     *
     * @param hostAggregateUpdate update host aggregate
     *
     * @return HostAggregateUpdateResult
     */
    public HostAggregateUpdateResult handleMessage(HostAggregateUpdate hostAggregateUpdate) {
        log.info("receiving message for create host aggregate, virtual type : [{}]", hostAggregateUpdate.getVirtEnvType());

        log.info("msg id : [{}]", hostAggregateUpdate.getMsgId());
        HostAggregateUpdateResult result = new HostAggregateUpdateResult();
        try {
            result = adminHandler.updateHostAggregates(hostAggregateUpdate);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(hostAggregateUpdate, result);
        result.setMsgId(hostAggregateUpdate.getMsgId());
        if (result.isSuccess()) {
            log.info("[adaptor] the host aggregate has been updated successfully");
        } else {
            log.info("[adaptor] the host aggregate has been updated failure");
        }
        return result;
    }

    /**
     * Handle message for delete host aggregate
     *
     * @param hostAggregateDelete delete host aggregate
     *
     * @return HostAggregateDeleteResult
     */
    public HostAggregateDeleteResult handleMessage(HostAggregateDelete hostAggregateDelete) {
        log.info("receiving message for delete host aggregate,virtual type : [{}]"
                         , hostAggregateDelete.getVirtEnvType());

        log.info("msg id : [{}]", hostAggregateDelete.getMsgId());
        HostAggregateDeleteResult result = new HostAggregateDeleteResult();
        try {
            result = adminHandler.deleteHostAggregates(hostAggregateDelete);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(hostAggregateDelete, result);
        result.setMsgId(hostAggregateDelete.getMsgId());
        if (result.isSuccess()) {
            log.info("[adaptor] the host aggregate has been deleted successfully");
        } else {
            log.info("[adaptor] the host aggregate has been deleted failure");
        }
        return result;
    }

    /**
     * Handle message for manage host aggregate server
     *
     * @param hostAggregateServerManage manage host aggregate server
     *
     * @return HostAggregateServerManageResult
     */
    public HostAggregateServerManageResult handleMessage(HostAggregateServerManage hostAggregateServerManage) {
        log.info("receiving message for manage host aggregate server,virtual type : [{}]"
                         , hostAggregateServerManage.getVirtEnvType());

        log.info("msg id : [{}]", hostAggregateServerManage.getMsgId());
        HostAggregateServerManageResult result = new HostAggregateServerManageResult();
        try {
            result = adminHandler.manageHostAggregateServer(hostAggregateServerManage);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(hostAggregateServerManage, result);
        result.setMsgId(hostAggregateServerManage.getMsgId());
        if (result.isSuccess()) {
            log.info("[adaptor] the host aggregate server has been updated successfully");
        } else {
            log.info("[adaptor] the host aggregate server has been updated failure");
        }
        return result;
    }

    public ServerGroupCreateResult handleMessage(ServerGroupCreate serverGroupCreate) {
        ServerGroupCreateResult result = new ServerGroupCreateResult();
        try {
            result = vmHandler.createServerGroup(serverGroupCreate);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            log.error(e.getMessage());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            log.error(e.getMessage());
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(serverGroupCreate, result);
        return result;
    }

    public ServerGroupRemoveResult handleMessage(ServerGroupRemove serverGroupRemove) {
        ServerGroupRemoveResult result = new ServerGroupRemoveResult();
        try {
            result = vmHandler.deleteServerGroup(serverGroupRemove);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            log.error(e.getMessage());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            log.error(e.getMessage());
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(serverGroupRemove, result);
        return result;
    }

    public ServerGroupScanResult handleMessage(ServerGroupScan serverGroupScan) {
        log.info("receiving message for scan server group,virtual type : [{}]" , serverGroupScan.getVirtEnvType());

        log.info("msg id : [{}]", serverGroupScan.getMsgId());

        ServerGroupScanResult serverGroupScanResult = new ServerGroupScanResult();
        try {
            serverGroupScanResult = scanHandler.scanServerGroup(serverGroupScan);
        } catch (CommonAdapterException e) {

            serverGroupScanResult.setSuccess(false);
            serverGroupScanResult.setErrCode(e.getErrCode());
            serverGroupScanResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            serverGroupScanResult.setSuccess(false);
            serverGroupScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            serverGroupScanResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            serverGroupScanResult.setSuccess(false);
            serverGroupScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            serverGroupScanResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(serverGroupScan, serverGroupScanResult);
        serverGroupScanResult.setMsgId(serverGroupScan.getMsgId());
        return serverGroupScanResult;
    }

    public HasAdminPermissionResult handleMessage(HasAdminPermission hasAdminPermission) {
        log.info("receiving message for has admin permission,virtual type : [{}]"
                         , hasAdminPermission.getVirtEnvType());

        log.info("msg id : [{}]" , hasAdminPermission.getMsgId());

        HasAdminPermissionResult adminPermissionResult = new HasAdminPermissionResult();
        try {
            adminPermissionResult = adminHandler.hasAdminPermission(hasAdminPermission);
        } catch (CommonAdapterException e) {

            adminPermissionResult.setSuccess(false);
            adminPermissionResult.setErrCode(e.getErrCode());
            adminPermissionResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            adminPermissionResult.setSuccess(false);
            adminPermissionResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            adminPermissionResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            adminPermissionResult.setSuccess(false);
            adminPermissionResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            adminPermissionResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(hasAdminPermission, adminPermissionResult);
        adminPermissionResult.setMsgId(hasAdminPermission.getMsgId());
        return adminPermissionResult;
    }

    public MachineExtendInfoResult handleMessage(MachineExtendInfo machineExtendInfo) {
        log.info("receiving message for MachineExtendInfo,virtual type : [{}]", machineExtendInfo.getVirtEnvType());

        log.info("msg id : [{}]" , machineExtendInfo.getMsgId());

        MachineExtendInfoResult machineExtendInfoResult = new MachineExtendInfoResult();
        try {
            machineExtendInfoResult = scanHandler.apiQuery(machineExtendInfo);
        } catch (CommonAdapterException e) {

            machineExtendInfoResult.setSuccess(false);
            machineExtendInfoResult.setErrCode(e.getErrCode());
            machineExtendInfoResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            machineExtendInfoResult.setSuccess(false);
            machineExtendInfoResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            machineExtendInfoResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            machineExtendInfoResult.setSuccess(false);
            machineExtendInfoResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            machineExtendInfoResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(machineExtendInfo, machineExtendInfoResult);
        machineExtendInfoResult.setMsgId(machineExtendInfo.getMsgId());
        return machineExtendInfoResult;
    }


    public VmQueryDataStoreResult handleMessage(VmQueryDataStore vmQueryDataStore) {

        log.info("receiving message for getting vmQueryDataStore, virtual type : [{}]", vmQueryDataStore.getVirtEnvType());

        log.info("msg id : [{}]", vmQueryDataStore.getMsgId());

        VmQueryDataStoreResult result = new VmQueryDataStoreResult();

        try {
            result = vmHandler.vmQueryDataStore(vmQueryDataStore);
            result.setSuccess(true);
        } catch (CommonAdapterException e) {
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
            result.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
            result.setSuccess(false);
        } catch (Exception e) {
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
            result.setSuccess(false);
        }
        BaseUtil.setResult(vmQueryDataStore, result);
        result.setMsgId(vmQueryDataStore.getMsgId());
        return result;
    }

    public FloatingIpLineQueryResult handleMessage(FloatingIpLineQuery floatingIpLineQuery) {
        log.info("receiving message for floatingIpLineQuery,virtual type : [{}]", floatingIpLineQuery.getVirtEnvType());

        log.info("msg id : [{}]", floatingIpLineQuery.getMsgId());

        FloatingIpLineQueryResult floatingIpLineQueryResult = new FloatingIpLineQueryResult();
        try {
            floatingIpLineQueryResult = scanHandler.floatingIpLineQuery(floatingIpLineQuery);
        } catch (CommonAdapterException e) {

            floatingIpLineQueryResult.setSuccess(false);
            floatingIpLineQueryResult.setErrCode(e.getErrCode());
            floatingIpLineQueryResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            floatingIpLineQueryResult.setSuccess(false);
            floatingIpLineQueryResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            floatingIpLineQueryResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            floatingIpLineQueryResult.setSuccess(false);
            floatingIpLineQueryResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            floatingIpLineQueryResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(floatingIpLineQuery, floatingIpLineQueryResult);
        floatingIpLineQueryResult.setMsgId(floatingIpLineQuery.getMsgId());
        return floatingIpLineQueryResult;
    }

    /**
     * Handle message FireWallPolicy Update Result
     * @param fireWallPolicyUpdate
     * @return
     */
    public FireWallPolicyUpdateResult handleMessage(FireWallPolicyUpdate fireWallPolicyUpdate) {

        log.info("receiving message for creating fireWallPolicy, virtual type : [{}] network name : [{}]",fireWallPolicyUpdate.getVirtEnvType(), fireWallPolicyUpdate.getName());

        log.info("msg id : [{}]", fireWallPolicyUpdate.getMsgId());
        FireWallPolicyUpdateResult fireWallPolicyUpdateResult = new FireWallPolicyUpdateResult();

        try {
            fireWallPolicyUpdateResult = netHandler.updateFireWallPolicy(fireWallPolicyUpdate);
        } catch (CommonAdapterException e) {
            fireWallPolicyUpdateResult.setSuccess(false);
            fireWallPolicyUpdateResult.setErrCode(e.getErrCode());
            fireWallPolicyUpdateResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            fireWallPolicyUpdateResult.setSuccess(false);
            fireWallPolicyUpdateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            fireWallPolicyUpdateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {

            fireWallPolicyUpdateResult.setErrMsg(e.getMessage());
            fireWallPolicyUpdateResult.setSuccess(false);
        }

        BaseUtil.setResult(fireWallPolicyUpdate, fireWallPolicyUpdateResult);
        fireWallPolicyUpdateResult.setMsgId(fireWallPolicyUpdate.getMsgId());
        return fireWallPolicyUpdateResult;
    }



    /**
     * Handle message FireWallPolicy InsertRule Result
     * @param fireWallPolicyInsertRule
     * @return
     */
    public FireWallPolicyInsertRuleResult handleMessage(FireWallPolicyInsertRule fireWallPolicyInsertRule) {

        log.info("receiving message for insert fireWallRule, virtual type : [{}] network name : [{}]",fireWallPolicyInsertRule.getVirtEnvType(), fireWallPolicyInsertRule.getName());

        log.info("msg id : [{}]", fireWallPolicyInsertRule.getMsgId());
        FireWallPolicyInsertRuleResult fireWallPolicyInsertRuleResult = new FireWallPolicyInsertRuleResult();

        try {
            fireWallPolicyInsertRuleResult = netHandler.insertFireWallRule(fireWallPolicyInsertRule);
        } catch (CommonAdapterException e) {
            fireWallPolicyInsertRuleResult.setSuccess(false);
            fireWallPolicyInsertRuleResult.setErrCode(e.getErrCode());
            fireWallPolicyInsertRuleResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            fireWallPolicyInsertRuleResult.setSuccess(false);
            fireWallPolicyInsertRuleResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            fireWallPolicyInsertRuleResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {

            fireWallPolicyInsertRuleResult.setErrMsg(e.getMessage());
            fireWallPolicyInsertRuleResult.setSuccess(false);
        }

        BaseUtil.setResult(fireWallPolicyInsertRule, fireWallPolicyInsertRuleResult);
        fireWallPolicyInsertRuleResult.setMsgId(fireWallPolicyInsertRule.getMsgId());
        return fireWallPolicyInsertRuleResult;
    }


    public IamUserScanResult handleMessage(IamUserScan iamUserScan) {
        log.info("receiving message for sync iamuser----virtual type : "
                         + iamUserScan.getVirtEnvType());

        log.info("msg id : " + iamUserScan.getMsgId());
        IamUserScanResult result = new IamUserScanResult();
        try {
            result = scanHandler.getIamUser(iamUserScan);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(iamUserScan, result);
        result.setMsgId(iamUserScan.getMsgId());
        if (result.isSuccess()) {
            log.info("[adaptor] the iam user has been updated successfully");
        } else {
            log.info("[adaptor] the iam user has been updated failure");
        }
        return result;
    }

    /**
     * Handle message vmrenew calculate result.
     */
    public VmRenewInstanceResult handleMessage(RdsRenewInstance rdsRenewInstance) {

        log.info("receiving rds renew  for metric monitor, virtual type : [{}]", rdsRenewInstance.getVirtEnvType());

        log.info("msg id : [{}]", rdsRenewInstance.getMsgId());

        VmRenewInstanceResult vmRenewResult = new VmRenewInstanceResult();

        try {
            vmRenewResult = rdsHandler.renewRds(rdsRenewInstance);
        } catch (CommonAdapterException e) {

            vmRenewResult.setSuccess(false);
            vmRenewResult.setErrCode(e.getErrCode());
            vmRenewResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            vmRenewResult.setSuccess(false);
            vmRenewResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmRenewResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            vmRenewResult.setSuccess(false);
            vmRenewResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmRenewResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(rdsRenewInstance, vmRenewResult);
        BeanUtil.transformBeanObj(rdsRenewInstance, vmRenewResult);
        vmRenewResult.setMsgId(rdsRenewInstance.getMsgId());
        return vmRenewResult;

    }


    /**
     * Handle message activationCCUser
     *
     * @param snapshotScan the activationCCUser
     *
     * @return the activationCCUser result
     */
    public ActivationCCUserResult handleMessage(ActivationCCUser activationCCUser) {

        log.info("receiving message for activationCCUser, virtual type : [{}]", activationCCUser.getVirtEnvType());

        log.info("msg id : [{}]", activationCCUser.getMsgId());

        ActivationCCUserResult activationCCUserResult = new ActivationCCUserResult();
        try {
            activationCCUserResult = authHandler.activationCCUser(activationCCUser);
        } catch (CommonAdapterException e) {
            activationCCUserResult.setSuccess(false);
            activationCCUserResult.setErrCode(e.getErrCode());
            activationCCUserResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            activationCCUserResult.setSuccess(false);
            activationCCUserResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            activationCCUserResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {

            activationCCUserResult.setSuccess(false);
            activationCCUserResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(activationCCUser, activationCCUserResult);
        activationCCUserResult.setMsgId(activationCCUser.getMsgId());
        return activationCCUserResult;
    }


    /**
     * Handle message duser
     *
     * @param snapshotScan the duser
     *
     * @return the duser result
     */
    public DuserResult handleMessage(Duser duser) {

        log.info("receiving message for duser, virtual type : [{}]", duser.getVirtEnvType());

        log.info("msg id : [{}]", duser.getMsgId());

        DuserResult duserResult = new DuserResult();
        try {
            duserResult = authHandler.duser(duser);
        } catch (CommonAdapterException e) {
            duserResult.setSuccess(false);
            duserResult.setErrCode(e.getErrCode());
            duserResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            duserResult.setSuccess(false);
            duserResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            duserResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {

            duserResult.setSuccess(false);
            duserResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(duser, duserResult);
        duserResult.setMsgId(duser.getMsgId());
        return duserResult;
    }


    /**
     * Handle message daccount
     *
     * @param snapshotScan the daccount
     *
     * @return the daccount result
     */
    public DaccountResult handleMessage(Daccount daccount) {

        log.info("receiving message for daccount, virtual type : [{}]", daccount.getVirtEnvType());

        log.info("msg id : [{}]", daccount.getMsgId());

        DaccountResult daccountResult = new DaccountResult();
        try {
            daccountResult = authHandler.daccount(daccount);
        } catch (CommonAdapterException e) {
            daccountResult.setSuccess(false);
            daccountResult.setErrCode(e.getErrCode());
            daccountResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            daccountResult.setSuccess(false);
            daccountResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            daccountResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {

            daccountResult.setSuccess(false);
            daccountResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(daccount, daccountResult);
        daccountResult.setMsgId(daccount.getMsgId());
        return daccountResult;
    }


    /**
     * Handle message resPools
     *
     * @param  the resPools
     *
     * @return the resPools BaseResult
     */
    public BaseResult handleMessage(ResPools resPools) {

        log.info("receiving message for resPools, virtual type : [{}]", resPools.getVirtEnvType());

        log.info("msg id : [{}]", resPools.getMsgId());

        BaseResult baseResult = new BaseResult();
        try {
            baseResult = authHandler.resPendingNum(resPools);
        } catch (CommonAdapterException e) {
            baseResult.setSuccess(false);
            baseResult.setErrCode(e.getErrCode());
            baseResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            baseResult.setSuccess(false);
            baseResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            baseResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {

            baseResult.setSuccess(false);
            baseResult.setErrMsg(e.getMessage());
            log.error("receiving message for resPools Exception:",e);
        }
        BaseUtil.setResult(resPools, baseResult);
        baseResult.setMsgId(resPools.getMsgId());
        return baseResult;
    }

    public HCSOTimeGetResult handleMessage(HCSOTimeGet param){
        log.debug("receiving message for getHCSOTime, verify info : [{}]", JSON.toJSONString(param));

        log.debug("msg id : [{}]", param.getMsgId());

        HCSOTimeGetResult hcsoTimeGetResult = new HCSOTimeGetResult();

        try {
            hcsoTimeGetResult = hcsoTimeHandler.getHCSOTime(param);
            log.debug("adaptor getHCSOTime :[{}] get HCSO time successfully",param.getProviderType());
        } catch (CommonAdapterException e) {

            hcsoTimeGetResult.setSuccess(false);
            hcsoTimeGetResult.setErrCode(e.getErrCode());
            hcsoTimeGetResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            hcsoTimeGetResult.setSuccess(false);
            hcsoTimeGetResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            hcsoTimeGetResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            hcsoTimeGetResult.setSuccess(false);
            hcsoTimeGetResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            hcsoTimeGetResult.setErrMsg(e.getMessage());
        }
        return hcsoTimeGetResult;
    }

    public HPCShareStopJobResult handleMessage(HPCShareStopJob param){
        log.debug("receiving message for HPCShareStopJob, verify info : [{}]", JSON.toJSONString(param));
        log.debug("msg id : [{}]", param.getMsgId());
        HPCShareStopJobResult hpcShareStopJobResult = new HPCShareStopJobResult();
        try {
            hpcShareStopJobResult = hpcHandler.hpcShareStopJob(param);
        } catch (CommonAdapterException e) {
            hpcShareStopJobResult.setSuccess(false);
            hpcShareStopJobResult.setErrCode(e.getErrCode());
            hpcShareStopJobResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            hpcShareStopJobResult.setSuccess(false);
            hpcShareStopJobResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            hpcShareStopJobResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            hpcShareStopJobResult.setSuccess(false);
            hpcShareStopJobResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            hpcShareStopJobResult.setErrMsg(e.getMessage());
        }
        return hpcShareStopJobResult;
    }


    /**
     * 获取华为镜像更新
     * @param mirrorCenterUpdate
     * @return
     */
    public MirrorCenterUpdateResponse handleMessage(MirrorCenterUpdate mirrorCenterUpdate) {

        log.info("receiving message for mirrorCenterUpdate, virtual type : [{}] topic name : [{}]", mirrorCenterUpdate.getVirtEnvType(), mirrorCenterUpdate.getName());

        log.info("msg id : [{}]", mirrorCenterUpdate.getMsgId());
        MirrorCenterUpdateResponse mirrorCenterUpdateResponse = new MirrorCenterUpdateResponse();
        try {
            mirrorCenterUpdateResponse = mirrorHandler.getMirrorCenterUpdate(mirrorCenterUpdate);

        } catch (CommonAdapterException e) {

            mirrorCenterUpdateResponse.setSuccess(false);
            mirrorCenterUpdateResponse.setErrCode(e.getErrCode());
            mirrorCenterUpdateResponse.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            mirrorCenterUpdateResponse.setSuccess(false);
            mirrorCenterUpdateResponse.setErrCode(Constants.AdapterUnvailableException.CODE);
            mirrorCenterUpdateResponse.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            mirrorCenterUpdateResponse.setSuccess(false);
            mirrorCenterUpdateResponse.setErrCode(Constants.AdapterUnvailableException.CODE);
            mirrorCenterUpdateResponse.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(mirrorCenterUpdate, mirrorCenterUpdateResponse);
        mirrorCenterUpdateResponse.setMsgId(mirrorCenterUpdate.getMsgId());
        mirrorCenterUpdateResponse.setOptions(mirrorCenterUpdate.getOptions());
        return mirrorCenterUpdateResponse;
    }


    /**
     * 获取华为镜像列表
     * @param mirrorCenterList
     * @return
     */
    public MirrorCenterListResponse handleMessage(MirrorCenterList mirrorCenterList) {

        log.info("receiving message for mirror list, virtual type : [{}] topic name : [{}]", mirrorCenterList.getVirtEnvType(), mirrorCenterList.getName());

        log.info("msg id : [{}]", mirrorCenterList.getMsgId());
        MirrorCenterListResponse mirrorCenterListResponse = new MirrorCenterListResponse();
        try {
            mirrorCenterListResponse = mirrorHandler.getMirrorList(mirrorCenterList);

        } catch (CommonAdapterException e) {

            mirrorCenterListResponse.setSuccess(false);
            mirrorCenterListResponse.setErrCode(e.getErrCode());
            mirrorCenterListResponse.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            mirrorCenterListResponse.setSuccess(false);
            mirrorCenterListResponse.setErrCode(Constants.AdapterUnvailableException.CODE);
            mirrorCenterListResponse.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            mirrorCenterListResponse.setSuccess(false);
            mirrorCenterListResponse.setErrCode(Constants.AdapterUnvailableException.CODE);
            mirrorCenterListResponse.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(mirrorCenterList, mirrorCenterListResponse);
        mirrorCenterListResponse.setMsgId(mirrorCenterList.getMsgId());
        mirrorCenterListResponse.setOptions(mirrorCenterList.getOptions());
        return mirrorCenterListResponse;
    }

    /**
     * 获取华为镜像版本
     * @param mirrorVersionList
     * @return
     */
    public ImageVersionListResponse handleMessage(MirrorVersionList mirrorVersionList) {

        log.info("receiving message for mirror version, virtual type : [{}] topic name : [{}]", mirrorVersionList.getVirtEnvType(), mirrorVersionList.getName());

        log.info("msg id : [{}]", mirrorVersionList.getMsgId());
        ImageVersionListResponse imageVersionListResponse = new ImageVersionListResponse();
        try {
            imageVersionListResponse = mirrorHandler.getMirrorVersion(mirrorVersionList);

        } catch (CommonAdapterException e) {

            imageVersionListResponse.setSuccess(false);
            imageVersionListResponse.setErrCode(e.getErrCode());
            imageVersionListResponse.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            imageVersionListResponse.setSuccess(false);
            imageVersionListResponse.setErrCode(Constants.AdapterUnvailableException.CODE);
            imageVersionListResponse.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            imageVersionListResponse.setSuccess(false);
            imageVersionListResponse.setErrCode(Constants.AdapterUnvailableException.CODE);
            imageVersionListResponse.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(mirrorVersionList, imageVersionListResponse);
        imageVersionListResponse.setMsgId(mirrorVersionList.getMsgId());
        imageVersionListResponse.setOptions(mirrorVersionList.getOptions());
        return imageVersionListResponse;
    }


    /**
     * 获取华为镜像详情
     * @param mirrorCenterDetail
     * @return
     */
    public MirrorCenterDetailResponse handleMessage(MirrorCenterDetail mirrorCenterDetail) {

        log.info("receiving message for mirrorCenterDetail, virtual type : [{}] topic name : [{}]", mirrorCenterDetail.getVirtEnvType(), mirrorCenterDetail.getName());

        log.info("msg id : [{}]", mirrorCenterDetail.getMsgId());
        MirrorCenterDetailResponse mirrorCenterDetailResponse = new MirrorCenterDetailResponse();
        try {
            mirrorCenterDetailResponse = mirrorHandler.getMirrorDetail(mirrorCenterDetail);

        } catch (CommonAdapterException e) {

            mirrorCenterDetailResponse.setSuccess(false);
            mirrorCenterDetailResponse.setErrCode(e.getErrCode());
            mirrorCenterDetailResponse.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            mirrorCenterDetailResponse.setSuccess(false);
            mirrorCenterDetailResponse.setErrCode(Constants.AdapterUnvailableException.CODE);
            mirrorCenterDetailResponse.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            mirrorCenterDetailResponse.setSuccess(false);
            mirrorCenterDetailResponse.setErrCode(Constants.AdapterUnvailableException.CODE);
            mirrorCenterDetailResponse.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(mirrorCenterDetail, mirrorCenterDetailResponse);
        mirrorCenterDetailResponse.setMsgId(mirrorCenterDetail.getMsgId());
        mirrorCenterDetailResponse.setOptions(mirrorCenterDetail.getOptions());
        return mirrorCenterDetailResponse;
    }




    /**
     * 删除华为镜像版本
     * @param imageVersionDelete
     * @return
     */
    public ImageVersionDeleteResponse handleMessage(ImageVersionDelete imageVersionDelete) {

        log.info("receiving message for imageVersionDelete, virtual type : [{}] topic name : [{}]", imageVersionDelete.getVirtEnvType(), imageVersionDelete.getName());

        log.info("msg id : [{}]", imageVersionDelete.getMsgId());
        ImageVersionDeleteResponse imageVersionDeleteResponse = new ImageVersionDeleteResponse();
        try {
            imageVersionDeleteResponse = mirrorHandler.getImageVersionDelete(imageVersionDelete);

        } catch (CommonAdapterException e) {

            imageVersionDeleteResponse.setSuccess(false);
            imageVersionDeleteResponse.setErrCode(e.getErrCode());
            imageVersionDeleteResponse.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            imageVersionDeleteResponse.setSuccess(false);
            imageVersionDeleteResponse.setErrCode(Constants.AdapterUnvailableException.CODE);
            imageVersionDeleteResponse.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            imageVersionDeleteResponse.setSuccess(false);
            imageVersionDeleteResponse.setErrCode(Constants.AdapterUnvailableException.CODE);
            imageVersionDeleteResponse.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(imageVersionDelete, imageVersionDeleteResponse);
        imageVersionDeleteResponse.setMsgId(imageVersionDelete.getMsgId());
        imageVersionDeleteResponse.setOptions(imageVersionDelete.getOptions());
        return imageVersionDeleteResponse;
    }

    /**
     * 生成临时登录指令
     * @param createTempLoginCode
     * @return
     */
    public CreateTempLoginCodeResponse handleMessage(CreateTempLoginCode createTempLoginCode) {

        log.info("receiving message for createTempLoginCode, virtual type : [{}] topic name : [{}]", createTempLoginCode.getVirtEnvType(), createTempLoginCode.getName());

        log.info("msg id : [{}]", createTempLoginCode.getMsgId());
        CreateTempLoginCodeResponse createTempLoginCodeResponse = new CreateTempLoginCodeResponse();
        try {
            createTempLoginCodeResponse = mirrorHandler.getCreateTempLoginCode(createTempLoginCode);

        } catch (CommonAdapterException e) {

            createTempLoginCodeResponse.setSuccess(false);
            createTempLoginCodeResponse.setErrCode(e.getErrCode());
            createTempLoginCodeResponse.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            createTempLoginCodeResponse.setSuccess(false);
            createTempLoginCodeResponse.setErrCode(Constants.AdapterUnvailableException.CODE);
            createTempLoginCodeResponse.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            createTempLoginCodeResponse.setSuccess(false);
            createTempLoginCodeResponse.setErrCode(Constants.AdapterUnvailableException.CODE);
            createTempLoginCodeResponse.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(createTempLoginCode, createTempLoginCodeResponse);
        createTempLoginCodeResponse.setMsgId(createTempLoginCode.getMsgId());
        createTempLoginCodeResponse.setOptions(createTempLoginCode.getOptions());
        return createTempLoginCodeResponse;
    }


    /**
     * 查询组织列表
     * @param listNamespace
     * @return
     */
    public MirrorListNamespaceResponse handleMessage(ListNamespace listNamespace) {

        log.info("receiving message for createTempLoginCode, virtual type : [{}] topic name : [{}]", listNamespace.getVirtEnvType(), listNamespace.getName());

        log.info("msg id : [{}]", listNamespace.getMsgId());
        MirrorListNamespaceResponse mirrorListNamespaceResponse = new MirrorListNamespaceResponse();
        try {
            mirrorListNamespaceResponse = mirrorHandler.listNamespace(listNamespace);

        } catch (CommonAdapterException e) {

            mirrorListNamespaceResponse.setSuccess(false);
            mirrorListNamespaceResponse.setErrCode(e.getErrCode());
            mirrorListNamespaceResponse.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            mirrorListNamespaceResponse.setSuccess(false);
            mirrorListNamespaceResponse.setErrCode(Constants.AdapterUnvailableException.CODE);
            mirrorListNamespaceResponse.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            mirrorListNamespaceResponse.setSuccess(false);
            mirrorListNamespaceResponse.setErrCode(Constants.AdapterUnvailableException.CODE);
            mirrorListNamespaceResponse.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(listNamespace, mirrorListNamespaceResponse);
        mirrorListNamespaceResponse.setMsgId(listNamespace.getMsgId());
        mirrorListNamespaceResponse.setOptions(listNamespace.getOptions());
        return mirrorListNamespaceResponse;
    }

    /**
     * 创建组织
     * @param createNamespace
     * @return
     */
    public MirrorCreateNamespaceResponse handleMessage(CreateNamespace createNamespace) {

        log.info("receiving message for createTempLoginCode, virtual type : [{}] topic name : [{}]", createNamespace.getVirtEnvType(), createNamespace.getName());

        log.info("msg id : [{}]", createNamespace.getMsgId());
        MirrorCreateNamespaceResponse mirrorCreateNamespaceResponse = new MirrorCreateNamespaceResponse();
        try {
            mirrorCreateNamespaceResponse = mirrorHandler.createNamespace(createNamespace);

        } catch (CommonAdapterException e) {

            mirrorCreateNamespaceResponse.setSuccess(false);
            mirrorCreateNamespaceResponse.setErrCode(e.getErrCode());
            mirrorCreateNamespaceResponse.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            mirrorCreateNamespaceResponse.setSuccess(false);
            mirrorCreateNamespaceResponse.setErrCode(Constants.AdapterUnvailableException.CODE);
            mirrorCreateNamespaceResponse.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            mirrorCreateNamespaceResponse.setSuccess(false);
            mirrorCreateNamespaceResponse.setErrCode(Constants.AdapterUnvailableException.CODE);
            mirrorCreateNamespaceResponse.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(createNamespace, mirrorCreateNamespaceResponse);
        mirrorCreateNamespaceResponse.setMsgId(createNamespace.getMsgId());
        mirrorCreateNamespaceResponse.setOptions(createNamespace.getOptions());
        return mirrorCreateNamespaceResponse;
    }

    public GrantJobTemplateResult handleMessage(GrantJobTemplate param){
        log.debug("receiving message for ActivationJobTemplate, verify info : [{}]", JSON.toJSONString(param));
        log.debug("msg id : [{}]", param.getMsgId());
        GrantJobTemplateResult grantJobTemplateResult = new GrantJobTemplateResult();
        try {
            grantJobTemplateResult = hpcHandler.grantJobTemplate(param);
        } catch (CommonAdapterException e) {
            grantJobTemplateResult.setSuccess(false);
            grantJobTemplateResult.setErrCode(e.getErrCode());
            grantJobTemplateResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            grantJobTemplateResult.setSuccess(false);
            grantJobTemplateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            grantJobTemplateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            grantJobTemplateResult.setSuccess(false);
            grantJobTemplateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            grantJobTemplateResult.setErrMsg(e.getMessage());
        }
        return grantJobTemplateResult;
    }

    /**
     * 查询裸金属服务器详情列表
     */
    public ListBareMetalResult handleMessage(BareMetalList bareMetalList) {

        log.info("receiving message for mirror version, virtual type : [{}] topic name : [{}]",
                 bareMetalList.getVirtEnvType(), bareMetalList.getName());

        log.info("msg id : [{}]", bareMetalList.getMsgId());
        ListBareMetalResult listBareMetalResult = new ListBareMetalResult();
        try {
            listBareMetalResult = bmsHandler.listBareMetalServers(bareMetalList);

        } catch (CommonAdapterException e) {

            listBareMetalResult.setSuccess(false);
            listBareMetalResult.setErrCode(e.getErrCode());
            listBareMetalResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            listBareMetalResult.setSuccess(false);
            listBareMetalResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            listBareMetalResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            listBareMetalResult.setSuccess(false);
            listBareMetalResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            listBareMetalResult.setErrMsg(e.getMessage());
        }
        return listBareMetalResult;
    }


    /**
     * Handle message pirce calculate result.
     */
    public AiShopCreateResult handleMessage(AiShopCreate param) {

        log.info("receiving message for AI HUB, virtual type : [{}]", param.getVirtEnvType());

        log.info("msg id : [{}]", param.getMsgId());

        AiShopCreateResult result = new AiShopCreateResult();

        try {
            result = aiHubHandler.create(param);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }

        result.setMsgId(param.getMsgId());

        return result;

    }

    /**
     * Handle message pirce calculate result.
     */
    public AiShopCreateToResult handleMessage(AiShopCreateTo param) {

        log.info("receiving message for AI HUB, virtual type : [{}]", param.getVirtEnvType());

        log.info("msg id : [{}]", param.getMsgId());

        AiShopCreateToResult result = new AiShopCreateToResult();

        try {
            result = aiHubHandler.createTo(param);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }

        result.setMsgId(param.getMsgId());

        return result;

    }

    /**
     * Handle message pirce calculate result.
     */
    public AiShopDeleteResult handleMessage(AiShopDelete param) {

        log.info("receiving message for AI HUB, virtual type : [{}]", param.getVirtEnvType());

        log.info("msg id : [{}]", param.getMsgId());

        AiShopDeleteResult result = new AiShopDeleteResult();

        try {
            result = aiHubHandler.delete(param);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }

        result.setMsgId(param.getMsgId());

        return result;

    }

    /**
     * Handle message pirce calculate result.
     */
    public AiShopDeleteToResult handleMessage(AiShopDeleteTo param) {

        log.info("receiving message for AI HUB, virtual type : [{}]", param.getVirtEnvType());

        log.info("msg id : [{}]", param.getMsgId());

        AiShopDeleteToResult result = new AiShopDeleteToResult();

        try {
            result = aiHubHandler.deleteTo(param);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }

        result.setMsgId(param.getMsgId());

        return result;

    }

    /**
     * Handle message pirce calculate result.
     */
    public AiShopDetailsQueryResult handleMessage(AiShopDetailsQuery param) {

        log.info("receiving message for AI HUB, virtual type : [{}]", param.getVirtEnvType());

        log.info("msg id : [{}]", param.getMsgId());

        AiShopDetailsQueryResult result = new AiShopDetailsQueryResult();

        try {
            result = aiHubHandler.detailsQuery(param);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }

        result.setMsgId(param.getMsgId());

        return result;

    }

    /**
     * Handle message pirce calculate result.
     */
    public AiShopLicenseCreateResult handleMessage(AiShopLicenseCreate param) {

        log.info("receiving message for AI HUB, virtual type : [{}]", param.getVirtEnvType());

        log.info("msg id : [{}]", param.getMsgId());

        AiShopLicenseCreateResult result = new AiShopLicenseCreateResult();

        try {
            result = aiHubHandler.licenseCreate(param);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }

        result.setMsgId(param.getMsgId());

        return result;

    }

    /**
     * Handle message pirce calculate result.
     */
    public BaseResult handleMessage(AiShopLifecycleUpdate param) {

        log.info("receiving message for AI HUB, virtual type : [{}]", param.getVirtEnvType());

        log.info("msg id : [{}]", param.getMsgId());

        BaseResult result = new BaseResult();

        try {
            result = aiHubHandler.lifecycleUpdate(param);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }

        result.setMsgId(param.getMsgId());

        return result;

    }

    /**
     * Handle message pirce calculate result.
     */
    public AiShopMineQueryAllResult handleMessage(AiShopMineQueryAll param) {

        log.info("receiving message for AI HUB, virtual type : [{}]", param.getVirtEnvType());

        log.info("msg id : [{}]", param.getMsgId());

        AiShopMineQueryAllResult result = new AiShopMineQueryAllResult();

        try {
            result = aiHubHandler.mineQueryAll(param);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }

        result.setMsgId(param.getMsgId());

        return result;

    }

    /**
     * Handle message pirce calculate result.
     */
    public BaseResult handleMessage(AiShopProcess param) {

        log.info("receiving message for AI HUB, virtual type : [{}]", param.getVirtEnvType());

        log.info("msg id : [{}]", param.getMsgId());

        BaseResult result = new BaseResult();

        try {
            result = aiHubHandler.process(param);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }

        result.setMsgId(param.getMsgId());

        return result;

    }

    /**
     * Handle message pirce calculate result.
     */
    public AiShopQueryAllResult handleMessage(AiShopQueryAll param) {

        log.info("receiving message for AI HUB, virtual type : [{}]", param.getVirtEnvType());

        log.info("msg id : [{}]", param.getMsgId());

        AiShopQueryAllResult result = new AiShopQueryAllResult();

        try {
            result = aiHubHandler.queryAll(param);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }

        result.setMsgId(param.getMsgId());

        return result;

    }

    /**
     * Handle message pirce calculate result.
     */
    public BaseResult handleMessage(AiShopRenew param) {

        log.info("receiving message for AI HUB, virtual type : [{}]", param.getVirtEnvType());

        log.info("msg id : [{}]", param.getMsgId());

        BaseResult result = new BaseResult();

        try {
            result = aiHubHandler.renew(param);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }

        result.setMsgId(param.getMsgId());

        return result;

    }

    /**
     * Handle message pirce calculate result.
     */
    public AiShopSubscribesQueryResult handleMessage(AiShopSubscribesQuery param) {

        log.info("receiving message for AI HUB, virtual type : [{}]", param.getVirtEnvType());

        log.info("msg id : [{}]", param.getMsgId());

        AiShopSubscribesQueryResult result = new AiShopSubscribesQueryResult();

        try {
            result = aiHubHandler.subscribesQuery(param);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }

        result.setMsgId(param.getMsgId());

        return result;

    }

    /**
     * Handle message pirce calculate result.
     */
    public BaseResult handleMessage(AiShopSubscriptionsCreate param) {

        log.info("receiving message for AI HUB, virtual type : [{}]", param.getVirtEnvType());

        log.info("msg id : [{}]", param.getMsgId());

        BaseResult result = new BaseResult();

        try {
            result = aiHubHandler.subscriptionsCreate(param);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }

        result.setMsgId(param.getMsgId());

        return result;

    }

    /**
     *  HPC-SAAS 同步租户状态
     */
    public BaseResult handleMessage(HPCTenantStatusSyncRequest param) {
        log.info("msg id : {}", param.getMsgId());
        BaseResult result = new BaseResult();
        try {
            result = hpcHandler.tenantStatusSync(param);

        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }

        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());

        return result;
    }


    /**
     * Handle message pirce calculate result.
     */
    public BaseResult handleMessage(AiShopSubscriptionsDelete param) {

        log.info("receiving message for AI HUB, virtual type : [{}]", param.getVirtEnvType());

        log.info("msg id : [{}]", param.getMsgId());

        BaseResult result = new BaseResult();

        try {
            result = aiHubHandler.subscriptionsDelete(param);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }

        result.setMsgId(param.getMsgId());

        return result;

    }

    /**
     * Handle message pirce calculate result.
     */
    public BaseResult handleMessage(AiShopSubscriptionsRenew param) {

        log.info("receiving message for AI HUB, virtual type : [{}]", param.getVirtEnvType());

        log.info("msg id : [{}]", param.getMsgId());

        BaseResult result = new BaseResult();

        try {
            result = aiHubHandler.subscriptionsRenew(param);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }

        result.setMsgId(param.getMsgId());

        return result;

    }

    /**
     * Handle message pirce calculate result.
     */
    public BaseResult handleMessage(AiShopUpdate param) {

        log.info("receiving message for AI HUB, virtual type : [{}]", param.getVirtEnvType());

        log.info("msg id : [{}]", param.getMsgId());

        BaseResult result = new BaseResult();

        try {
            result = aiHubHandler.update(param);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }

        result.setMsgId(param.getMsgId());

        return result;

    }


    /**
     * Handle message pirce calculate result.
     */
    public BaseResult handleMessage(AiShopUpdateTo param) {

        log.info("receiving message for AI HUB, virtual type : [{}]", param.getVirtEnvType());

        log.info("msg id : [{}]", param.getMsgId());

        BaseResult result = new BaseResult();

        try {
            result = aiHubHandler.update(param);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }

        result.setMsgId(param.getMsgId());

        return result;

    }

    /**
     * Handle message pirce calculate result.
     */
    public AiShopVersionCreateResult handleMessage(AiShopVersionCreate param) {

        log.info("receiving message for AI HUB, virtual type : [{}]", param.getVirtEnvType());

        log.info("msg id : [{}]", param.getMsgId());

        AiShopVersionCreateResult result = new AiShopVersionCreateResult();

        try {
            result = aiHubHandler.versionCreate(param);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }

        result.setMsgId(param.getMsgId());

        return result;

    }

    /**
     * Handle message pirce calculate result.
     */
    public AiShopVersionCreateResultTo handleMessage(AiShopVersionCreateTo param) {

        log.info("receiving message for AI HUB, virtual type : [{}]", param.getVirtEnvType());

        log.info("msg id : [{}]", param.getMsgId());

        AiShopVersionCreateResultTo result = new AiShopVersionCreateResultTo();

        try {
            result = aiHubHandler.versionCreateTo(param);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }

        result.setMsgId(param.getMsgId());

        return result;

    }

    /**
     * Handle message pirce calculate result.
     */
    public BaseResult handleMessage(AiShopVersionDelete param) {

        log.info("receiving message for AI HUB, virtual type : [{}]", param.getVirtEnvType());

        log.info("msg id : [{}]", param.getMsgId());

        BaseResult result = new BaseResult();

        try {
            result = aiHubHandler.versionDelete(param);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }

        result.setMsgId(param.getMsgId());

        return result;

    }

    /**
     * Handle message pirce calculate result.
     */
    public AiShopVersionsQueryResult handleMessage(AiShopVersionsQuery param) {

        log.info("receiving message for AI HUB, virtual type : [{}]", param.getVirtEnvType());

        log.info("msg id : [{}]", param.getMsgId());

        AiShopVersionsQueryResult result = new AiShopVersionsQueryResult();

        try {
            result = aiHubHandler.versionsQuery(param);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }

        result.setMsgId(param.getMsgId());

        return result;

    }

    /**
     * Handle message pirce calculate result.
     */
    public AiShopVersionUpdateResult handleMessage(AiShopVersionUpdate param) {

        log.info("receiving message for AI HUB, virtual type : [{}]", param.getVirtEnvType());

        log.info("msg id : [{}]", param.getMsgId());

        AiShopVersionUpdateResult result = new AiShopVersionUpdateResult();

        try {
            result = aiHubHandler.versionUpdate(param);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }

        result.setMsgId(param.getMsgId());

        return result;

    }

    /**
     * Handle message pirce calculate result.
     */
    public AiShopVersionUpdateResultTo handleMessage(AiShopVersionUpdateTo param) {

        log.info("receiving message for models, projectId : [{}]", param.getProjectId());

        log.info("msg id : [{}]", param.getMsgId());

        AiShopVersionUpdateResultTo result = new AiShopVersionUpdateResultTo();

        try {
            result = aiHubHandler.versionUpdateTo(param);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }

        result.setMsgId(param.getMsgId());

        return result;

    }


    /**
     * Handle message pirce calculate result.
     */
    public AiAlgorithmsQueryResult handleMessage(AiAlgorithmsQuery param) {

        log.info("receiving message for algorithms, projectId : [{}]", param.getProjectId());

        log.info("msg id : [{}]", param.getMsgId());

        AiAlgorithmsQueryResult result = new AiAlgorithmsQueryResult();

        try {
            result = aiHubHandler.algorithmsQuery(param);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }

        result.setMsgId(param.getMsgId());

        return result;

    }

    /**
     * Handle message pirce calculate result.
     */
    public AiModelsQueryResult handleMessage(AiModelsQuery param) {

        log.info("receiving message for models, projectId : [{}]", param.getProjectId());

        log.info("msg id : [{}]", param.getMsgId());

        AiModelsQueryResult result = new AiModelsQueryResult();

        try {
            result = aiHubHandler.modelsQuery(param);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }

        result.setMsgId(param.getMsgId());

        return result;

    }


}
