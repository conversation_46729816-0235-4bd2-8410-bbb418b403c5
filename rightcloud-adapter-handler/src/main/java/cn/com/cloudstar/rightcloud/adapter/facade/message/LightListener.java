/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.adapter.facade.message;

import com.huaweicloud.sdk.core.internal.Iam;

import cn.com.cloudstar.rightcloud.adapter.facade.handler.DmeHandler;
import cn.com.cloudstar.rightcloud.adapter.facade.handler.HpcHandler;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.*;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.IamAgencyDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.IamCustomPolicyDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.IamCustomPolicyListResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.IamCustomPolicyQueryInfoResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.FDHPCClusterRequest;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.HPCPreDrpMgmt;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.FDHPCClusterResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.HPCPreDrpMgmtResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.result.agency.MAAgencyCreateAuthorizeRes;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.result.agency.MAAgencyCreateRes;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.result.agency.MAAgencyDeleteRes;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.result.agency.MAAgencyQueryAgenciesRes;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.result.agency.MAAgencyQueryListRes;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.result.agency.MAAgencyQueryQuotasRes;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.result.agency.QueryAgencyInfoResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.result.traingjobv2.StatisticsNotebooksResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.MountPreResShare;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.MountPreShareResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.ShareModifyResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.SharePredeployQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.SharePredeployUninstallResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.ShareModify;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.SharePredeployModify;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.SharePredeployQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.SharePredeployUninstall;
import cn.com.cloudstar.rightcloud.driver.hcso.action.provision.HCSOCustomPermissionQueryInfo;
import cn.com.cloudstar.rightcloud.driver.hcso.action.provision.HCSOIamUserUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.CCSPIntegralityHash;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.IamCloudServiceCustomPolicyDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.IamCloudServiceCustomPolicyUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.IamGroupAndEntrustWithDomainPermissionCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.IamGroupAndEntrustWithDomainPermissionDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.*;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.ClustersRequest;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.MAResourceFlavorsQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.result.*;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.result.clusters.ClustersResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.result.notebooks.NotebooksResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.result.poolsv2.OsPoolsV2Result;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.result.services.ServicesResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.result.summary.SummaryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.result.traingjobv2.TrainingJobsV2Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.adapter.facade.common.Constants;
import cn.com.cloudstar.rightcloud.adapter.facade.handler.AdminHandler;
import cn.com.cloudstar.rightcloud.adapter.facade.handler.AuthHandler;
import cn.com.cloudstar.rightcloud.adapter.facade.handler.DcsHandler;
import cn.com.cloudstar.rightcloud.adapter.facade.handler.GpuGroupHandler;
import cn.com.cloudstar.rightcloud.adapter.facade.handler.ModelArtsHandler;
import cn.com.cloudstar.rightcloud.adapter.facade.handler.NetHandler;
import cn.com.cloudstar.rightcloud.adapter.facade.handler.OceanStorHandler;
import cn.com.cloudstar.rightcloud.adapter.facade.handler.ScanHandler;
import cn.com.cloudstar.rightcloud.adapter.facade.handler.StorageHandler;
import cn.com.cloudstar.rightcloud.adapter.facade.handler.VmHandler;
import cn.com.cloudstar.rightcloud.adapter.facade.util.BaseUtil;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.IamCloudServiceCustomPolicyCheck;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.IamCloudServiceCustomPolicyCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.IamCredentialGet;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.IamGroupWithDomainPermissionCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.IamGroupWithDomainPermissionDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.IamGroupWithDomainPermissionListGet;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.IamGroupWithProjectPermissionCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.IamGroupWithProjectPermissionDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.IamGroupWithProjectPermissionListGet;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.IamIdentityProviderCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.IamIdentityProviderDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.IamIdentityProviderGet;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.IamIdentityProvidersListGet;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.IamMappingCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.IamMetadataCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.IamModelArtsAuthorizationsCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.IamOpenIdConnectCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.IamPermanentAccessKeyCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.IamPermanentAccessKeyDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.IamPermissionsListGet;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.IamProjectsListGet;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.IamProtocolCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.IamSubUserAddToGroup;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.IamSubUserCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.IamSubUserDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.IamUserCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.IamUserDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.IamUserGroupCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.IamUserGroupDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.IamUserGroupGet;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.IamUserUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.IamUsersListGet;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.CCSPIntegralityHashResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.IamCloudServiceCustomPolicyResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.IamCredentialResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.IamIdentityProviderResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.IamIdentityProvidersListResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.IamPermanentAccessKeyResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.IamPermissionsListResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.IamProjectsListResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.IamSubUserResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.IamUserCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.IamUserDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.IamUserGroupResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.IamUserUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.IamUsersListResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.block.BlockInfoGet;
import cn.com.cloudstar.rightcloud.adapter.pojo.block.BlockList;
import cn.com.cloudstar.rightcloud.adapter.pojo.block.BlockSnapshotCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.block.BlockSnapshotDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.block.result.BlockInfoGetResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.block.result.BlockListResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.block.result.BlockSnapshotCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.block.result.BlockSnapshotDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.dcs.DcsConfigUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.dcs.DcsDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.dcs.DcsResetPassword;
import cn.com.cloudstar.rightcloud.adapter.pojo.dcs.DcsUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.dcs.result.DcsConfigUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.dcs.result.DcsDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.dcs.result.DcsResetPasswordResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.dcs.result.DcsUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.DiskAttach;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.DiskConfigQuotaQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.DiskDetach;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.FileSystemCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.FileSystemDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.PartitionCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.PartitionDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.PartitionUnMount;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.PartitionUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.result.DiskAttachResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.result.DiskConfigQuotaQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.result.DiskDetachResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.result.FileSystemCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.result.FileSystemDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.result.PartitionCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.result.PartitionDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.result.PartitionUnMountResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.disk.result.PartitionUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.gpu.GpuGroupAddGpuDevice;
import cn.com.cloudstar.rightcloud.adapter.pojo.gpu.GpuGroupAssociateVm;
import cn.com.cloudstar.rightcloud.adapter.pojo.gpu.GpuGroupCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.gpu.GpuGroupDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.gpu.GpuGroupDissociateVm;
import cn.com.cloudstar.rightcloud.adapter.pojo.gpu.GpuGroupRemoveGpuDevice;
import cn.com.cloudstar.rightcloud.adapter.pojo.gpu.GpuGroupUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.gpu.QueryGpuProduct;
import cn.com.cloudstar.rightcloud.adapter.pojo.dme.DMECreateQuotaRequest;
import cn.com.cloudstar.rightcloud.adapter.pojo.dme.DMEDeleteQuotaRequest;
import cn.com.cloudstar.rightcloud.adapter.pojo.dme.DMEQuotaListQueryRequest;
import cn.com.cloudstar.rightcloud.adapter.pojo.dme.DMEQuotaUpdateRequest;
import cn.com.cloudstar.rightcloud.adapter.pojo.dme.DMETaskInfoQueryRequest;
import cn.com.cloudstar.rightcloud.adapter.pojo.dme.result.DMECreateQuotaResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.dme.result.DMEDeleteQuotaResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.dme.result.DMEQuotaListQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.dme.result.DMEQuotaUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.dme.result.DMETaskInfoQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.gpu.QueryGpuVirtualMode;
import cn.com.cloudstar.rightcloud.adapter.pojo.gpu.result.GpuGroupCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.gpu.result.QueryGpuProductResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.gpu.result.QueryGpuVirtualModeResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.host.ResVmTypeQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.host.result.ResVmTypeQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.FDHPCClusterRequest;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.HPCPreDrpMgmt;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.FDHPCClusterResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.HPCPreDrpMgmtResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.image.ImageDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.image.ImageUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.image.result.ImageDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.image.result.ImageUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.keypairs.KeypairCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.keypairs.KeypairDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.keypairs.KeypairGet;
import cn.com.cloudstar.rightcloud.adapter.pojo.keypairs.KeypairListGet;
import cn.com.cloudstar.rightcloud.adapter.pojo.keypairs.result.KeypairCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.keypairs.result.KeypairDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.keypairs.result.KeypairGetResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.keypairs.result.KeypairListGetResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.LbListenerUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.LbListenerUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.mq.QueryOrderDetail;
import cn.com.cloudstar.rightcloud.adapter.pojo.mq.result.QueryOrderDetailResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.BandwidthRevise;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.FloatingIpAttach;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.FloatingIpCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.FloatingIpDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.FloatingIpDetach;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.FloatingIpUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.SecurityGroupQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.BandwidthReviseResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.FloatingIpAttachResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.FloatingIpCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.FloatingIpDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.FloatingIpDetachResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.FloatingIpUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.SecurityGroupQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.OceanStorAccount;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.OceanStorAccountCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.OceanStorAccountDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.OceanStorConvergedQosAssociationCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.OceanStorConvergedQosAssociationDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.OceanStorConvergedQosPolicyCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.OceanStorConvergedQosPolicyDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.OceanStorLogin;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.OceanStorNamespaces;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.OceanStorNamespacesCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.OceanStorNamespacesDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.OceanStorNamespacesQuota;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.OceanStorNamespacesQuotaCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.OceanStorNamespacesQuotaDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.OceanStorNamespacesQuotaUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.OceanStorTierPoliciesCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.OceanStorTierPoliciesDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.OceanStorUnixUser;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.OceanStorUnixUserDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.OceanStorUnixUserGroup;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.OceanStorUnixUserGroupDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.result.OceanStorAccountCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.result.OceanStorAccountResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.result.OceanStorConvergedQosPolicyCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.result.OceanStorLoginResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.result.OceanStorNamespacesCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.result.OceanStorNamespacesQuotaCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.result.OceanStorNamespacesQuotaResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.result.OceanStorNamespacesResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.result.OceanStorTierPoliciesCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.result.OceanStorTierPoliciesDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.result.OceanStorUnixUserGroupResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.oceanstor.result.OceanStorUnixUserResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ShareDetailByEnv;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.TemplateScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ShareDetailByEnvResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.TemplateScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.MachineOperate;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.ResetPassword;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.ResetVncPassword;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmBlockQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmInfoGet;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmOperate;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmReinstallSystem;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.MachineOperateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.ResetPasswordResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.ResetVncPasswordResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmBlockQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmGetInfoResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmOperateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmReinstallSystemResult;
import cn.com.cloudstar.rightcloud.basic.data.pojo.base.Base;
import cn.com.cloudstar.rightcloud.driver.core.exception.AdapterUnavailableException;
import cn.com.cloudstar.rightcloud.driver.core.exception.CommonAdapterException;
import cn.com.cloudstar.rightcloud.driver.pojo.base.BaseResult;
import cn.com.cloudstar.rightcloud.infra.cloud.client.core.exception.CloudApiException;
import org.springframework.beans.BeanUtils;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.TestPassRequest;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.result.CheckCCPResult;

import java.lang.reflect.InvocationTargetException;

/**
 * The type Light listener.
 */
@Service
@Slf4j
public class LightListener {

    @Autowired
    private VmHandler vmHandler;
    @Autowired
    private StorageHandler storageHandler;
    @Autowired
    private AdminHandler adminHandler;
    @Autowired
    private NetHandler netHandler;
    @Autowired
    private ScanHandler scanHandler;

    @Autowired
    private AuthHandler authHandler;

    @Autowired
    private ModelArtsHandler modelArtsHandler;

    /**
     * 处理高速缓存操作
     **/
    @Autowired
    private DcsHandler dcsHandler;

    @Autowired
    private GpuGroupHandler gpuGroupHandler;

    @Autowired
    private OceanStorHandler oceanStorHandler;
    @Autowired
    private HpcHandler hpcHandler;
    @Autowired
    private DmeHandler dmeHandler;

    /**
     * Handle message vm operate result.
     *
     * @param vmOperate the vm operate
     * @return the vm operate result
     */
    public VmOperateResult handleMessage(VmOperate vmOperate) {

        log.info("receiving message for operating vm,virtual type : [{}] vm name : [{}] action : [{}]",
                 vmOperate.getVirtEnvType(), vmOperate.getVmName(), vmOperate.getAction());

        log.info("msg id : [{}]", vmOperate.getMsgId());

        VmOperateResult vmOperateResult = new VmOperateResult();

        try {
            vmOperateResult = vmHandler.operateVm(vmOperate);
            log.info("adaptor vm :[{}] has been [{}] successfully",vmOperate.getVmName(),vmOperate.getAction());
        } catch (CommonAdapterException e) {

            vmOperateResult.setSuccess(false);
            vmOperateResult.setErrCode(e.getErrCode());
            vmOperateResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            vmOperateResult.setSuccess(false);
            vmOperateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmOperateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            log.error(e.getMessage());
            vmOperateResult.setSuccess(false);
            vmOperateResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(vmOperate, vmOperateResult);
        vmOperateResult.setMsgId(vmOperate.getMsgId());
        vmOperateResult.setAction(vmOperate.getAction());
        vmOperateResult.setId(vmOperate.getId());
        vmOperateResult.setVmName(vmOperate.getVmName());
        vmOperateResult.setUuid(vmOperate.getUuid());
        return vmOperateResult;
    }

    /**
     * Handle message disk attach result.
     *
     * @param diskAttach the disk attach
     * @return the disk attach result
     */
    public DiskAttachResult handleMessage(DiskAttach diskAttach) {

        log.info("receiving message for attaching disk, virtual type : [{}]", diskAttach.getVirtEnvType());

        log.info("msg id : [{}]", diskAttach.getMsgId());

        DiskAttachResult diskAttachResult = new DiskAttachResult();

        try {
            diskAttachResult = storageHandler.attachDisk(diskAttach);
        } catch (CommonAdapterException e) {
            diskAttachResult.setSuccess(false);
            diskAttachResult.setErrCode(e.getErrCode());
            diskAttachResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            diskAttachResult.setSuccess(false);
            diskAttachResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            diskAttachResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            log.error(e.getMessage());
            diskAttachResult.setSuccess(false);
            diskAttachResult.setErrMsg(e.getMessage());
        }

        BaseUtil.setResult(diskAttach, diskAttachResult);
        diskAttachResult.setMsgId(diskAttach.getMsgId());
        diskAttachResult.setServerId(diskAttach.getServerId());
        diskAttachResult.setVolumeId(diskAttach.getVolumeId());
        return diskAttachResult;
    }

    /**
     * Handle message disk detach result.
     *
     * @param diskDetach the disk detach
     * @return the disk detach result
     */
    public DiskDetachResult handleMessage(DiskDetach diskDetach) {

        log.info("receiving message for detaching disk, virtual type : [{}]", diskDetach.getVirtEnvType());

        log.info("msg id : [{}]", diskDetach.getMsgId());

        DiskDetachResult diskDetachResult = new DiskDetachResult();

        try {
            diskDetachResult = storageHandler.detachDisk(diskDetach);
        } catch (CommonAdapterException e) {

            diskDetachResult.setSuccess(false);
            diskDetachResult.setErrCode(e.getErrCode());
            diskDetachResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            diskDetachResult.setSuccess(false);
            diskDetachResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            diskDetachResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            log.error(e.getMessage());
            diskDetachResult.setSuccess(false);
            diskDetachResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(diskDetach, diskDetachResult);
        diskDetachResult.setMsgId(diskDetach.getMsgId());
        diskDetachResult.setServerId(diskDetach.getServerId());
        diskDetachResult.setVolumeId(diskDetach.getVolumeId());
        return diskDetachResult;

    }

    /**
     * Handle message block snapshot create result.
     *
     * @param blockSnapshotCreate the block snapshot create
     * @return the block snapshot create result
     */
    public BlockSnapshotCreateResult handleMessage(BlockSnapshotCreate blockSnapshotCreate) {

        log.info("receiving message for block snapshot creating, virtual type : [{}] snapshot name: [{}]",blockSnapshotCreate.getVirtEnvType(), blockSnapshotCreate.getName());

        log.info("msg id : [{}]", blockSnapshotCreate.getMsgId());

        BlockSnapshotCreateResult blockSnapshotCreateResult = new BlockSnapshotCreateResult();
        try {
            blockSnapshotCreateResult = storageHandler.createBlockSnapshot(blockSnapshotCreate);
        } catch (CommonAdapterException e) {

            blockSnapshotCreateResult.setSuccess(false);
            blockSnapshotCreateResult.setErrCode(e.getErrCode());
            blockSnapshotCreateResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            blockSnapshotCreateResult.setSuccess(false);
            blockSnapshotCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            blockSnapshotCreateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            blockSnapshotCreateResult.setSuccess(false);
            blockSnapshotCreateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            blockSnapshotCreateResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(blockSnapshotCreate, blockSnapshotCreateResult);
        blockSnapshotCreateResult.setMsgId(blockSnapshotCreate.getMsgId());
        blockSnapshotCreateResult.setId(blockSnapshotCreate.getId().toString());
        blockSnapshotCreateResult.setVolumeId(blockSnapshotCreate.getVolumeId());
        blockSnapshotCreateResult.setPeriod(blockSnapshotCreate.getPeriod());
        return blockSnapshotCreateResult;
    }

    /**
     * Handle message block snapshot delete result.
     *
     * @param blcokSnapShotDelete the blcok snap shot delete
     * @return the block snapshot delete result
     */
    public BlockSnapshotDeleteResult handleMessage(BlockSnapshotDelete blcokSnapShotDelete) {

        log.info("receiving message for block snapshot delete, virtual type : [{}] snapshot id: [{}]",blcokSnapShotDelete.getVirtEnvType(), blcokSnapShotDelete.getSnapshotId());

        log.info("msg id : [{}]", blcokSnapShotDelete.getMsgId());

        BlockSnapshotDeleteResult blockSnapshotDeleteResult = new BlockSnapshotDeleteResult();
        try {
            blockSnapshotDeleteResult = storageHandler.deleteBlockSnapshot(blcokSnapShotDelete);
        } catch (CommonAdapterException e) {

            blockSnapshotDeleteResult.setSuccess(false);
            blockSnapshotDeleteResult.setErrCode(e.getErrCode());
            blockSnapshotDeleteResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            blockSnapshotDeleteResult.setSuccess(false);
            blockSnapshotDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            blockSnapshotDeleteResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            blockSnapshotDeleteResult.setSuccess(false);
            blockSnapshotDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            blockSnapshotDeleteResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(blcokSnapShotDelete, blockSnapshotDeleteResult);
        blockSnapshotDeleteResult.setMsgId(blcokSnapShotDelete.getMsgId());
        blockSnapshotDeleteResult.setVolumeId(blcokSnapShotDelete.getVolumeId());
        blockSnapshotDeleteResult.setSnapshotId(blcokSnapShotDelete.getSnapshotId());
        blockSnapshotDeleteResult.setId(blcokSnapShotDelete.getId());
        return blockSnapshotDeleteResult;
    }


    /**
     * Handle message vm block query result.
     *
     * @param vmBlockQuery the vm block query
     * @return the vm block query result
     */
    public VmBlockQueryResult handleMessage(VmBlockQuery vmBlockQuery) {

        log.info(
                "receiving message for quering vmBlocks, virtual type : [{}]", vmBlockQuery.getVirtEnvType());

        log.info("msg id : [{}]",vmBlockQuery.getMsgId());

        VmBlockQueryResult vmBlockQueryResult = new VmBlockQueryResult();

        try {
            vmBlockQueryResult = vmHandler.queryBlocks(vmBlockQuery);

        } catch (CommonAdapterException e) {
            vmBlockQueryResult.setSuccess(false);
            vmBlockQueryResult.setErrCode(e.getErrCode());
            vmBlockQueryResult.setErrMsg(e.getMessage());
        } catch (AdapterUnavailableException e) {
            vmBlockQueryResult.setSuccess(false);
            vmBlockQueryResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmBlockQueryResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        BaseUtil.setResult(vmBlockQuery, vmBlockQueryResult);
        vmBlockQueryResult.setMsgId(vmBlockQuery.getMsgId());
        return vmBlockQueryResult;
    }

    /**
     * Handle message vm get info result.
     *
     * @param vmInfoGet the vm info get
     * @return the vm get info result
     */
    public VmGetInfoResult handleMessage(VmInfoGet vmInfoGet) {

        log.info("receiving message for getting vmInfo, virtual type : [{}]", vmInfoGet.getVirtEnvType());

        log.info("msg id : [{}]", vmInfoGet.getMsgId());

        VmGetInfoResult vmGetInfoResult = new VmGetInfoResult();
        try {
            vmGetInfoResult = vmHandler.getVmInfo(vmInfoGet);
        } catch (CommonAdapterException e) {
            vmGetInfoResult.setErrCode(e.getErrCode());
            vmGetInfoResult.setErrMsg(e.getErrMsg());
            vmGetInfoResult.setSuccess(false);
        } catch (AdapterUnavailableException e) {
            vmGetInfoResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmGetInfoResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
            vmGetInfoResult.setSuccess(false);

        } catch (Exception e) {
            vmGetInfoResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmGetInfoResult.setErrMsg(e.getMessage());
            vmGetInfoResult.setSuccess(false);

        }
        BaseUtil.setResult(vmInfoGet, vmGetInfoResult);
        vmGetInfoResult.setMsgId(vmInfoGet.getMsgId());

        return vmGetInfoResult;
    }

    /**
     * Handle message block info get result.
     *
     * @param blockInfoGet the block info get
     * @return the block info get result
     */
    public BlockInfoGetResult handleMessage(BlockInfoGet blockInfoGet) {

        log.info("receiving message for getting block info, virtual type : [{}]",
                blockInfoGet.getVirtEnvType());

        log.info("msg id : [{}]", blockInfoGet.getMsgId());

        BlockInfoGetResult blockInfoGetResult = new BlockInfoGetResult();

        try {
            blockInfoGetResult = storageHandler.getBlockInfo(blockInfoGet);
        } catch (CommonAdapterException e) {
            blockInfoGetResult.setSuccess(false);
            blockInfoGetResult.setErrCode(e.getErrCode());
            blockInfoGetResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {
            blockInfoGetResult.setSuccess(false);
            blockInfoGetResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            blockInfoGetResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            blockInfoGetResult.setSuccess(false);
            blockInfoGetResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            blockInfoGetResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(blockInfoGet, blockInfoGetResult);
        blockInfoGetResult.setMsgId(blockInfoGet.getMsgId());
        return blockInfoGetResult;
    }

    /**
     * Handle message block list result.
     *
     * @param blockList the block list
     * @return the block list result
     */
    public BlockListResult handleMessage(BlockList blockList) {

        log.info("receiving message for getting blocklist, virtual type : [{}]", blockList.getVirtEnvType());

        log.info("msg id : [{}]", blockList.getMsgId());

        BlockListResult blockListResult = new BlockListResult();

        try {
            blockListResult = storageHandler.getBlockList(blockList);
        } catch (CommonAdapterException e) {

            blockListResult.setSuccess(false);
            blockListResult.setErrCode(e.getErrCode());
            blockListResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {
            blockListResult.setSuccess(false);
            blockListResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            blockListResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            blockListResult.setSuccess(false);
            blockListResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            blockListResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(blockList, blockListResult);
        blockListResult.setMsgId(blockList.getMsgId());
        return blockListResult;
    }

    /**
     * Handel message security group query result.
     *
     * @param securityGroupQuery the security group query
     * @return the security group query result
     */
    @SuppressWarnings("unchecked")
    public SecurityGroupQueryResult handelMessage(SecurityGroupQuery securityGroupQuery) {

        log.info("receiving message for adding security group, virtual type : [{}]",
                securityGroupQuery.getVirtEnvType());

        log.info("msg id : [{}]", securityGroupQuery.getMsgId());

        SecurityGroupQueryResult securityGroupQueryResult = new SecurityGroupQueryResult();

        try {
            securityGroupQueryResult = vmHandler.querySecurityGroup(securityGroupQuery);
        } catch (CommonAdapterException e) {

            securityGroupQueryResult.setSuccess(false);
            securityGroupQueryResult.setErrCode(e.getErrCode());
            securityGroupQueryResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            securityGroupQueryResult.setSuccess(false);
            securityGroupQueryResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            securityGroupQueryResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {

            log.error(e.getMessage());
        }

        BaseUtil.setResult(securityGroupQuery, securityGroupQueryResult);
        securityGroupQueryResult.setMsgId(securityGroupQuery.getMsgId());
        return securityGroupQueryResult;
    }

    /**
     * Handle message floating ip create result.
     *
     * @param floatingIpCreate the floating ip create
     * @return the floating ip create result
     */
    public FloatingIpCreateResult handleMessage(FloatingIpCreate floatingIpCreate) {

        log.info("receiving message for creating floatingIp, virtual type : [{}]",
                floatingIpCreate.getVirtEnvType());

        log.info("msg id : [{}]", floatingIpCreate.getMsgId());

        FloatingIpCreateResult createResult = new FloatingIpCreateResult();

        try {
            createResult = netHandler.createFloatingIp(floatingIpCreate);
        } catch (CommonAdapterException e) {
            createResult.setSuccess(false);
            createResult.setErrCode(e.getErrCode());
            createResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {

            createResult.setSuccess(false);
            createResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            createResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            createResult.setSuccess(false);
            createResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            createResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(floatingIpCreate, createResult);
        createResult.setMsgId(floatingIpCreate.getMsgId());
        createResult.setNetworkId(floatingIpCreate.getFloatingNetworkId());
        createResult.setId(floatingIpCreate.getId());
        createResult.setName(floatingIpCreate.getName());
        createResult.setPeriod(floatingIpCreate.getPeriod());
        return createResult;
    }

    public FloatingIpUpdateResult handleMessage(FloatingIpUpdate floatingIpUpdate) {

        log.info("receiving message for update floatingIp, virtual type : [{}]" ,
                floatingIpUpdate.getVirtEnvType());

        log.info("msg id : [{}]", floatingIpUpdate.getMsgId());

        FloatingIpUpdateResult updateResult = new FloatingIpUpdateResult();

        try {
            updateResult = netHandler.updateFloatingIp(floatingIpUpdate);
        } catch (CommonAdapterException e) {
            updateResult.setSuccess(false);
            updateResult.setErrCode(e.getErrCode());
            updateResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {

            updateResult.setSuccess(false);
            updateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            updateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            updateResult.setSuccess(false);
            updateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            updateResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(floatingIpUpdate, updateResult);
        updateResult.setMsgId(floatingIpUpdate.getMsgId());
        return updateResult;
    }

    /**
     * Handle message bandwidth revise result.
     *
     * @param bandwidthRevise the bandwidth revise
     * @return the bandwidth revise result
     */
    public BandwidthReviseResult handleMessage(BandwidthRevise bandwidthRevise) {

        log.info("receiving message for edit bandwidthRevise, virtual type : [{}]",
                bandwidthRevise.getVirtEnvType());

        log.info("msg id : [{}]", bandwidthRevise.getMsgId());

        BandwidthReviseResult reviseResult = new BandwidthReviseResult();
        try {
            reviseResult = netHandler.bandwidthRevise(bandwidthRevise);
        } catch (CommonAdapterException e) {
            reviseResult.setSuccess(false);
            reviseResult.setErrCode(e.getErrCode());
            reviseResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            reviseResult.setSuccess(false);
            reviseResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            reviseResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            reviseResult.setSuccess(false);
            reviseResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            reviseResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(bandwidthRevise, reviseResult);
        reviseResult.setMsgId(bandwidthRevise.getMsgId());
        reviseResult.setBandwidth(bandwidthRevise.getBandwidth());
        return reviseResult;
    }

    /**
     * Handle message floating ip delete result.
     *
     * @param floatingIpDelete the floating ip delete
     * @return the floating ip delete result
     */
    public FloatingIpDeleteResult handleMessage(FloatingIpDelete floatingIpDelete) {
        log.info("receiving message for deleting floatingIp, virtual type : [{}]",
                floatingIpDelete.getVirtEnvType());
        log.info("msg id : [{}]", floatingIpDelete.getMsgId());

        FloatingIpDeleteResult floatingIpDeleteResult = new FloatingIpDeleteResult();
        try {
            floatingIpDeleteResult = netHandler.deleteFloatingIp(floatingIpDelete);
        } catch (CommonAdapterException e) {
            floatingIpDeleteResult.setSuccess(false);
            floatingIpDeleteResult.setErrCode(e.getErrCode());
            floatingIpDeleteResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {

            floatingIpDeleteResult.setSuccess(false);
            floatingIpDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            floatingIpDeleteResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            floatingIpDeleteResult.setSuccess(false);
            floatingIpDeleteResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            floatingIpDeleteResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(floatingIpDelete, floatingIpDeleteResult);
        floatingIpDeleteResult.setMsgId(floatingIpDelete.getMsgId());
        floatingIpDeleteResult.setFloatingIpId(floatingIpDelete.getFloatingIpId());
        floatingIpDeleteResult.setResId(floatingIpDelete.getResId());
        return floatingIpDeleteResult;
    }

    /**
     * Handle message floating ip attach result.
     *
     * @param floatingIpAttach the floating ip attach
     * @return the floating ip attach result
     */
    public FloatingIpAttachResult handleMessage(FloatingIpAttach floatingIpAttach) {
        log.info("receiving message for attach floatingIp, virtual type : [{}]",
                floatingIpAttach.getVirtEnvType());
        log.info("msg id : [{}]", floatingIpAttach.getMsgId());

        FloatingIpAttachResult floatingIpAttachResult = new FloatingIpAttachResult();
        try {
            floatingIpAttachResult = netHandler.attachFloatingIp(floatingIpAttach);
        } catch (CommonAdapterException e) {
            floatingIpAttachResult.setSuccess(false);
            floatingIpAttachResult.setErrCode(e.getErrCode());
            floatingIpAttachResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {

            floatingIpAttachResult.setSuccess(false);
            floatingIpAttachResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            floatingIpAttachResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            floatingIpAttachResult.setSuccess(false);
            floatingIpAttachResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            floatingIpAttachResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(floatingIpAttach, floatingIpAttachResult);
        floatingIpAttachResult.setMsgId(floatingIpAttach.getMsgId());
        floatingIpAttachResult.setServerId(floatingIpAttach.getServerId());
        floatingIpAttachResult.setFloatingIpId(floatingIpAttach.getFloatingIpId());
        floatingIpAttachResult.setFixedIp(floatingIpAttach.getFixedIp());
        return floatingIpAttachResult;
    }

    /**
     * Handle message floating ip detach result.
     *
     * @param floatingIpDetach the floating ip detach
     * @return the floating ip detach result
     */
    public FloatingIpDetachResult handleMessage(FloatingIpDetach floatingIpDetach) {
        log.info("receiving message for detach floatingIp, virtual type : [{}]",
                floatingIpDetach.getVirtEnvType());
        log.info("msg id : [{}]", floatingIpDetach.getMsgId());

        FloatingIpDetachResult floatingIpDetachResult = new FloatingIpDetachResult();

        try {
            floatingIpDetachResult = netHandler.detachFloatingIp(floatingIpDetach);
        } catch (CommonAdapterException e) {
            floatingIpDetachResult.setSuccess(false);
            floatingIpDetachResult.setErrCode(e.getErrCode());
            floatingIpDetachResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {

            floatingIpDetachResult.setSuccess(false);
            floatingIpDetachResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            floatingIpDetachResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            floatingIpDetachResult.setSuccess(false);
            floatingIpDetachResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            floatingIpDetachResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(floatingIpDetach, floatingIpDetachResult);
        floatingIpDetachResult.setMsgId(floatingIpDetach.getMsgId());
        return floatingIpDetachResult;
    }

    /**
     * Handle message template scan result.
     *
     * @param templateScan the template scan
     * @return the template scan result
     */
    public TemplateScanResult handleMessage(TemplateScan templateScan) {

        log.info("receiving message for scanning templates, virtual type : [{}]",
                templateScan.getVirtEnvType());

        log.info("msg id : [{}]", templateScan.getMsgId());

        TemplateScanResult templateScanResult = new TemplateScanResult();

        try {
            templateScanResult = scanHandler.scanTemplate(templateScan);
        } catch (CommonAdapterException e) {

            templateScanResult.setSuccess(false);
            templateScanResult.setErrCode(e.getErrCode());
            templateScanResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            templateScanResult.setSuccess(false);
            templateScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            templateScanResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            templateScanResult.setSuccess(false);
            templateScanResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            templateScanResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(templateScan, templateScanResult);
        templateScanResult.setMsgId(templateScan.getMsgId());
        return templateScanResult;
    }

    /**
     * Handle message keypair create result.
     *
     * @param keypairCreate the keypair create
     * @return the keypair create result
     */
    public KeypairCreateResult handleMessage(KeypairCreate keypairCreate) {
        log.info("receiving message for creatting keypair, virtual type : [{}]",
                keypairCreate.getVirtEnvType());

        log.info("msg id : [{}]", keypairCreate.getMsgId());
        KeypairCreateResult result = new KeypairCreateResult();

        try {
            result = adminHandler.createKeyPair(keypairCreate);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(keypairCreate, result);
        result.setMsgId(keypairCreate.getMsgId());
        return result;
    }

    /**
     * Handle message keypair delete result.
     *
     * @param keypairDelete the keypair delete
     * @return the keypair delete result
     */
    public KeypairDeleteResult handleMessage(KeypairDelete keypairDelete) {
        log.info(
                "receiving message for deleting keypair, virtual type : [{}]", keypairDelete.getVirtEnvType());

        log.info("msg id : [{}]", keypairDelete.getMsgId());
        KeypairDeleteResult result = new KeypairDeleteResult();

        try {
            result = adminHandler.deleteKeypair(keypairDelete);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(keypairDelete, result);
        result.setMsgId(keypairDelete.getMsgId());
        return result;
    }

    /**
     * Handle message keypair list get result.
     *
     * @param keypairListGet the keypair list get
     * @return the keypair list get result
     */
    public KeypairListGetResult handleMessage(KeypairListGet keypairListGet) {
        log.info("receiving message for getting keypairList, virtual type : [{}]",
                keypairListGet.getVirtEnvType());

        log.info("msg id : [{}]", keypairListGet.getMsgId());
        KeypairListGetResult result = new KeypairListGetResult();

        try {
            result = adminHandler.getKeyPairList(keypairListGet);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }

        BaseUtil.setResult(keypairListGet, result);
        result.setMsgId(keypairListGet.getMsgId());
        return result;
    }

    /**
     * Handle message keypair get result.
     *
     * @param keypairGet the keypair get
     * @return the keypair get result
     */
    public KeypairGetResult handleMessage(KeypairGet keypairGet) {
        log.info(
                "receiving message for getting keypairInfo, virtual type : [{}]", keypairGet.getVirtEnvType());

        log.info("msg id : [{}]", keypairGet.getMsgId());
        KeypairGetResult result = new KeypairGetResult();

        try {
            result = adminHandler.getKeyPairInfo(keypairGet);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(keypairGet, result);
        result.setMsgId(keypairGet.getMsgId());
        return result;
    }

    /**
     * Handle message image delete result.
     *
     * @param imageDelete the image delete
     * @return the image delete result
     */
    public ImageDeleteResult handleMessage(ImageDelete imageDelete) {
        log.info("receiving message for deleting image, virtual type : [{}]", imageDelete.getVirtEnvType());

        log.info("msg id : [{}]", imageDelete.getMsgId());
        ImageDeleteResult result = new ImageDeleteResult();
        try {
            result = vmHandler.deleteImage(imageDelete);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(imageDelete, result);
        result.setMsgId(imageDelete.getMsgId());
        result.setImageId(imageDelete.getImageId());
        result.setSid(imageDelete.getSid());
        return result;
    }

    /**
     * Handle message image update result.
     *
     * @param imageUpdate the image update
     * @return the image update result
     */
    public ImageUpdateResult handleMessage(ImageUpdate imageUpdate) {
        log.info("receiving message for updating image, virtual type : [{}]", imageUpdate.getVirtEnvType());

        log.info("msg id : [{}]", imageUpdate.getMsgId());
        ImageUpdateResult result = new ImageUpdateResult();
        try {
            result = vmHandler.updateImage(imageUpdate);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {

            log.error(e.getMessage());
        }

        BaseUtil.setResult(imageUpdate, result);
        result.setMsgId(imageUpdate.getMsgId());
        result.setImageId(imageUpdate.getImageId());
        result.setSid(imageUpdate.getSid());
        return result;
    }

    /**
     * Handle resetPassword result.
     *
     * @param resetPassword
     * @return the resetPassword result
     */
    public ResetPasswordResult handleMessage(ResetPassword resetPassword) {
        log.info("receiving message for reset password, virtual type : [{}]", resetPassword.getVirtEnvType());

        log.info("msg id : [{}]", resetPassword.getMsgId());
        ResetPasswordResult result = new ResetPasswordResult();
        try {
            result = vmHandler.resetPassword(resetPassword);
        } catch (CloudApiException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        BaseUtil.setResult(resetPassword, result);
        result.setMsgId(resetPassword.getMsgId());
        result.setUsername(resetPassword.getUsername());
        result.setPassword(resetPassword.getNewPassword());

        return result;
    }

    /**
     * Handle diskConfigQuery result.
     *
     * @param diskConfigQuotaQuery
     * @return the resetPassword result
     */
    public DiskConfigQuotaQueryResult handleMessage(DiskConfigQuotaQuery diskConfigQuotaQuery) {
        log.info("receiving message for query diskConfig, virtual type : [{}]", diskConfigQuotaQuery.getVirtEnvType());

        log.info("msg id : [{}]", diskConfigQuotaQuery.getMsgId());
        DiskConfigQuotaQueryResult result = new DiskConfigQuotaQueryResult();
        try {
            result = storageHandler.queryDiskConfig(diskConfigQuotaQuery);
        } catch (CloudApiException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        result.setMsgId(diskConfigQuotaQuery.getMsgId());

        return result;
    }

    public LbListenerUpdateResult handleMessage(LbListenerUpdate lbListenerUpdate) {
        log.info("receiving message for updating lblistener, virtual type : [{}]", lbListenerUpdate.getVirtEnvType());

        log.info("msg id : [{}]", lbListenerUpdate.getMsgId());
        LbListenerUpdateResult result = new LbListenerUpdateResult();
        try {
            result = vmHandler.updateLbListener(lbListenerUpdate);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {

            log.error(e.getMessage());
            result.setSuccess(false);
            result.setErrMsg(e.getMessage());
        }

        BaseUtil.setResult(lbListenerUpdate, result);
        result.setMsgId(lbListenerUpdate.getMsgId());
        return result;
    }

    /**
     * 高速缓存实例删除
     */
    public DcsDeleteResult handleMessage(DcsDelete opt) {
        log.info("高速缓存实例删除,msg id : [{}]", opt.getMsgId());
        DcsDeleteResult result = new DcsDeleteResult();
        try {
            result = dcsHandler.deleteDcs(opt);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(opt, result);
        result.setMsgId(opt.getMsgId());
        result.setOptions(opt.getOptions());
        return result;
    }

    /**
     * 高速缓存实例更新
     */
    public DcsUpdateResult handleMessage(DcsUpdate opt) {
        log.info("高速缓存实例更新DcsUpdate,msg id : [{}]", opt.getMsgId());
        DcsUpdateResult result = new DcsUpdateResult();
        try {
            result = dcsHandler.updateDcs(opt);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(opt, result);
        result.setMsgId(opt.getMsgId());
        result.setOptions(opt.getOptions());
        return result;
    }

    /**
     * 高速缓存实例更新
     */
    public DcsResetPasswordResult handleMessage(DcsResetPassword opt) {
        log.info(" 高速缓存实例更新DcsResetPassword, msg id : [{}]", opt.getMsgId());
        DcsResetPasswordResult result = new DcsResetPasswordResult();
        try {
            result = dcsHandler.resetDcsPassword(opt);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(opt, result);
        result.setMsgId(opt.getMsgId());
        result.setOptions(opt.getOptions());
        return result;
    }

    /**
     * 修改配置参数
     **/
    public DcsConfigUpdateResult handleMessage(DcsConfigUpdate opt) {
        log.info("修改配置参数,msg id : [{}]", opt.getMsgId());
        DcsConfigUpdateResult result = new DcsConfigUpdateResult();
        try {
            result = dcsHandler.updateDcsConfig(opt);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(opt, result);
        result.setMsgId(opt.getMsgId());
        result.setOptions(opt.getOptions());
        return result;
    }

    /**
     * 查询实例类型
     **/
    public ResVmTypeQueryResult handleMessage(ResVmTypeQuery query) {
        log.info("msg id : {}", query);
        ResVmTypeQueryResult result = new ResVmTypeQueryResult();
        try {
            result = vmHandler.vmTypeQuery(query);

        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        return result;
    }

    /**
     * 创建分区
     **/
    public PartitionCreateResult handleMessage(PartitionCreate create) {
        log.info("msg id : {}", create);
        PartitionCreateResult result = new PartitionCreateResult();
        try {
            result = storageHandler.createPartition(create);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        return result;
    }

    /**
     * 更新分区
     **/
    public PartitionUpdateResult handleMessage(PartitionUpdate update) {
        log.info("msg id : {}", update);
        PartitionUpdateResult result = new PartitionUpdateResult();
        try {
            result = storageHandler.updatePartition(update);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        return result;
    }

    /**
     * 删除分区
     **/
    public PartitionDeleteResult handleMessage(PartitionDelete delete) {
        log.info("msg id : {}", delete);
        PartitionDeleteResult result = new PartitionDeleteResult();
        try {
            result = storageHandler.deletePartition(delete);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        return result;
    }

    /**
     * 卸载分区
     **/
    public PartitionUnMountResult handleMessage(PartitionUnMount unMount) {
        log.info("msg id : {}", unMount);
        PartitionUnMountResult result = new PartitionUnMountResult();
        try {
            result = storageHandler.unMountPartition(unMount);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        return result;
    }

    /**
     * 增加文件系统
     **/
    public FileSystemCreateResult handleMessage(FileSystemCreate create) {
        log.info("msg id : {}", create);
        FileSystemCreateResult result = new FileSystemCreateResult();
        try {
            result = storageHandler.createFileSystem(create);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        return result;
    }

    /**
     * 卸载文件系统
     **/
    public FileSystemDeleteResult handleMessage(FileSystemDelete delete) {
        log.info("msg id : {}", delete);
        FileSystemDeleteResult result = new FileSystemDeleteResult();
        try {
            result = storageHandler.deleteFileSystem(delete);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        return result;
    }

    public MachineOperateResult handleMessage(MachineOperate machineOperate) {

        log.info("receiving message for operating machine,virtual type : [{}] vm name : [{}] action : [{}]",machineOperate.getVirtEnvType(),machineOperate.getHostName(), machineOperate.getAction());

        log.info("msg id : [{}]", machineOperate.getMsgId());

        MachineOperateResult vmOperateResult = new MachineOperateResult();

        try {
            vmOperateResult = vmHandler.operateVm(machineOperate);
            log.info("adaptor machine :[{}] has been [{}] successfully",machineOperate.getHostName(),machineOperate.getAction());
        } catch (CommonAdapterException e) {

            vmOperateResult.setSuccess(false);
            vmOperateResult.setErrCode(e.getErrCode());
            vmOperateResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            vmOperateResult.setSuccess(false);
            vmOperateResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            vmOperateResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            log.error(e.getMessage());
            vmOperateResult.setSuccess(false);
            vmOperateResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(machineOperate, vmOperateResult);
        vmOperateResult.setMsgId(machineOperate.getMsgId());
        vmOperateResult.setAction(machineOperate.getAction());
        vmOperateResult.setPhysicalHostPoolId(machineOperate.getPhysicalHostPoolId());
        vmOperateResult.setHostName(machineOperate.getHostName());
        return vmOperateResult;
    }


    /**
     * Handle resetVncPassword result.
     *
     * @param resetVncPassword
     * @return the resetVncPassword result
     */
    public ResetVncPasswordResult handleMessage(ResetVncPassword resetVncPassword) {
        log.info("receiving message for reset vnc password, virtual type : [{}]", resetVncPassword.getVirtEnvType());

        log.info("msg id : [{}]", resetVncPassword.getMsgId());
        ResetVncPasswordResult result = new ResetVncPasswordResult();
        try {
            result = vmHandler.resetVncPassword(resetVncPassword);
        } catch (CloudApiException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        BaseUtil.setResult(resetVncPassword, result);
        result.setMsgId(resetVncPassword.getMsgId());

        return result;
    }

    /**
     * Handle vmReinstallSystem result.
     *
     * @param vmReinstallSystem
     * @return the resetVncPassword result
     */
    public VmReinstallSystemResult handleMessage(VmReinstallSystem vmReinstallSystem) {
        log.info("receiving message for reinstall os system, virtual type : [{}]", vmReinstallSystem.getVirtEnvType());

        log.info("msg id : [{}]", vmReinstallSystem.getMsgId());
        VmReinstallSystemResult result = new VmReinstallSystemResult();
        try {
            result = vmHandler.vmReinstallSystem(vmReinstallSystem);
        } catch (CloudApiException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        BaseUtil.setResult(vmReinstallSystem, result);
        result.setMsgId(vmReinstallSystem.getMsgId());
        result.setResVmId(vmReinstallSystem.getResVmId());
        result.setResVmName(vmReinstallSystem.getResVmName());
        result.setImageUuid(vmReinstallSystem.getImageUuid());
        result.setImageSid(vmReinstallSystem.getImageSid());

        return result;
    }

    /**
     * Handle gpuGroupCreate result.
     *
     * @param gpuGroupCreate
     * @return the GpuGroupCreateResult result
     */
    public GpuGroupCreateResult handleMessage(GpuGroupCreate gpuGroupCreate) {
        log.info("receiving message for GpuGroup Create, virtual type : [{}]", gpuGroupCreate.getVirtEnvType());

        log.info("msg id : [{}]", gpuGroupCreate.getMsgId());
        GpuGroupCreateResult result = new GpuGroupCreateResult();
        try {
            result = gpuGroupHandler.createGpuGroup(gpuGroupCreate);
        } catch (CloudApiException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        BaseUtil.setResult(gpuGroupCreate, result);
        result.setMsgId(gpuGroupCreate.getMsgId());

        return result;
    }

    /**
     * Handle gpuGroupUpdate result.
     *
     * @param gpuGroupUpdate
     * @return the GpuGroupCreateResult result
     */
    public GpuGroupCreateResult handleMessage(GpuGroupUpdate gpuGroupUpdate) {
        log.info("receiving message for GpuGroup update, virtual type : [{}]", gpuGroupUpdate.getVirtEnvType());

        log.info("msg id : [{}]", gpuGroupUpdate.getMsgId());
        GpuGroupCreateResult result = new GpuGroupCreateResult();
        try {
            result = gpuGroupHandler.updateGpuGroup(gpuGroupUpdate);
        } catch (CloudApiException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        BaseUtil.setResult(gpuGroupUpdate, result);
        result.setMsgId(gpuGroupUpdate.getMsgId());

        return result;
    }

    /**
     * Handle gpuGroupDelete result.
     *
     * @param gpuGroupDelete
     * @return the BaseResult result
     */
    public BaseResult handleMessage(GpuGroupDelete gpuGroupDelete) {
        log.info("receiving message for GpuGroup delete, virtual type : [{}]", gpuGroupDelete.getVirtEnvType());

        log.info("msg id : [{}]", gpuGroupDelete.getMsgId());
        BaseResult result = new BaseResult();
        try {
            result = gpuGroupHandler.deleteGpuGroup(gpuGroupDelete);
        } catch (CloudApiException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        BaseUtil.setResult(gpuGroupDelete, result);
        result.setMsgId(gpuGroupDelete.getMsgId());

        return result;
    }

    /**
     * Handle addGpuDevice result.
     *
     * @param addGpuDevice
     * @return the BaseResult result
     */
    public BaseResult handleMessage(GpuGroupAddGpuDevice addGpuDevice) {
        log.info("receiving message for GpuGroup add GpuDevice, virtual type : [{}]", addGpuDevice.getVirtEnvType());

        log.info("msg id : [{}]", addGpuDevice.getMsgId());
        BaseResult result = new BaseResult();
        try {
            result = gpuGroupHandler.addGpuDevice(addGpuDevice);
        } catch (CloudApiException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        BaseUtil.setResult(addGpuDevice, result);
        result.setMsgId(addGpuDevice.getMsgId());

        return result;
    }

    /**
     * Handle removeGpuDevice result.
     *
     * @param removeGpuDevice
     * @return the BaseResult result
     */
    public BaseResult handleMessage(GpuGroupRemoveGpuDevice removeGpuDevice) {
        log.info("receiving message for GpuGroup remove GpuDevice, virtual type : [{}]",
                 removeGpuDevice.getVirtEnvType());

        log.info("msg id : [{}]", removeGpuDevice.getMsgId());
        BaseResult result = new BaseResult();
        try {
            result = gpuGroupHandler.removeGpuDevice(removeGpuDevice);
        } catch (CloudApiException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        BaseUtil.setResult(removeGpuDevice, result);
        result.setMsgId(removeGpuDevice.getMsgId());

        return result;
    }

    /**
     * Handle associateVm result.
     *
     * @param associateVm
     * @return the BaseResult result
     */
    public BaseResult handleMessage(GpuGroupAssociateVm associateVm) {
        log.info("receiving message for GpuGroup delete, virtual type : [{}]", associateVm.getVirtEnvType());

        log.info("msg id : [{}]", associateVm.getMsgId());
        BaseResult result = new BaseResult();
        try {
            result = gpuGroupHandler.associateVm(associateVm);
        } catch (CloudApiException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        BaseUtil.setResult(associateVm, result);
        result.setMsgId(associateVm.getMsgId());

        return result;
    }

    /**
     * Handle dissociateVm result.
     *
     * @param dissociateVm
     * @return the BaseResult result
     */
    public BaseResult handleMessage(GpuGroupDissociateVm dissociateVm) {
        log.info("receiving message for GpuGroup delete, virtual type : [{}]", dissociateVm.getVirtEnvType());

        log.info("msg id : [{}]", dissociateVm.getMsgId());
        BaseResult result = new BaseResult();
        try {
            result = gpuGroupHandler.dissociateVm(dissociateVm);
        } catch (CloudApiException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        BaseUtil.setResult(dissociateVm, result);
        result.setMsgId(dissociateVm.getMsgId());

        return result;
    }

    /**
     * Handle queryGpuProduct result.
     *
     * @param queryGpuProduct
     * @return the BaseResult result
     */
    public QueryGpuProductResult handleMessage(QueryGpuProduct queryGpuProduct) {
        log.info("receiving message for queryGpuProduct, virtual type : [{}]", queryGpuProduct.getVirtEnvType());

        log.info("msg id : [{}]", queryGpuProduct.getMsgId());
        QueryGpuProductResult result = new QueryGpuProductResult();
        try {
            result = gpuGroupHandler.queryGpuProduct(queryGpuProduct);
        } catch (CloudApiException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        BaseUtil.setResult(queryGpuProduct, result);
        result.setMsgId(queryGpuProduct.getMsgId());

        return result;
    }

    /**
     * Handle queryGpuVirtualMode result.
     *
     * @param queryGpuVirtualMode
     * @return the BaseResult result
     */
    public QueryGpuVirtualModeResult handleMessage(QueryGpuVirtualMode queryGpuVirtualMode) {
        log.info("receiving message for queryGpuVirtualMode, virtual type : [{}]",
                 queryGpuVirtualMode.getVirtEnvType());

        log.info("msg id : [{}]", queryGpuVirtualMode.getMsgId());
        QueryGpuVirtualModeResult result = new QueryGpuVirtualModeResult();
        try {
            result = gpuGroupHandler.queryGpuVirtualMode(queryGpuVirtualMode);
        } catch (CloudApiException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        BaseUtil.setResult(queryGpuVirtualMode, result);
        result.setMsgId(queryGpuVirtualMode.getMsgId());

        return result;
    }


    /**
     * 创建iam账户
     **/
    public IamUserCreateResult handleMessage(IamUserCreate create) {
        log.info("msg id : {}", create.getMsgId());
        IamUserCreateResult result = new IamUserCreateResult();
        try {
            result = authHandler.createIam(create);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(create, result);
        result.setMsgId(create.getMsgId());
        result.setOptions(create.getOptions());
        result.setUserSid(create.getUserSid());
        return result;
    }

    /**
     * 更新iam账户
     **/
    public IamUserUpdateResult handleMessage(IamUserUpdate update) {
        log.info("msg id : {}", update.getMsgId());
        IamUserUpdateResult result = new IamUserUpdateResult();
        try {
            result = authHandler.updateIam(update);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(update, result);
        result.setMsgId(update.getMsgId());
        result.setOptions(update.getOptions());
        return result;
    }

    /**
     * 删除iam账户
     **/
    public IamUserDeleteResult handleMessage(IamUserDelete delete) {
        log.info("msg id : {}", delete.getMsgId());
        IamUserDeleteResult result = new IamUserDeleteResult();
        try {
            result = authHandler.deleteIam(delete);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(delete, result);
        result.setMsgId(delete.getMsgId());
        result.setOptions(delete.getOptions());

        return result;
    }


    public QueryOrderDetailResult handleMessage(QueryOrderDetail detail) {
        log.info("msg id : {}", detail.getMsgId());
        QueryOrderDetailResult result = new QueryOrderDetailResult();
        try {
            result = authHandler.queryOrder(detail);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(detail, result);
        result.setMsgId(detail.getMsgId());
        result.setOptions(detail.getOptions());
        result.setServiceOrder(detail.getServiceOrder());
        result.setResResource(detail.getResResource());
        result.setId(detail.getPrimaryKey());
        result.setTaskId(detail.getTaskId());
        log.info("查询订单返回参数: {}", result);
        return result;
    }

    /**
     * 查询华为IAM身份提供商列表
     */
    public IamIdentityProvidersListResult handleMessage(IamIdentityProvidersListGet iamIdentityProvidersListGet) {
        log.info("msg id : {}", iamIdentityProvidersListGet.getMsgId());
        IamIdentityProvidersListResult result = new IamIdentityProvidersListResult();
        try {
            result = authHandler.queryIamIdentityProvidersList(iamIdentityProvidersListGet);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(iamIdentityProvidersListGet, result);
        result.setMsgId(iamIdentityProvidersListGet.getMsgId());
        result.setOptions(iamIdentityProvidersListGet.getOptions());
        return result;
    }

    /**
     * 查询华为IAM身份提供商详细
     */
    public IamIdentityProviderResult handleMessage(IamIdentityProviderGet iamIdentityProviderGet) {
        log.info("msg id : {}", iamIdentityProviderGet.getMsgId());
        IamIdentityProviderResult result = new IamIdentityProviderResult();
        try {
            result = authHandler.queryIamIdentityProvider(iamIdentityProviderGet);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(iamIdentityProviderGet, result);
        result.setMsgId(iamIdentityProviderGet.getMsgId());
        result.setOptions(iamIdentityProviderGet.getOptions());
        return result;
    }

    /**
     * 注册华为IAM身份提供商
     */
    public IamIdentityProviderResult handleMessage(IamIdentityProviderCreate iamIdentityProviderCreate) {
        log.info("msg id : {}", iamIdentityProviderCreate.getMsgId());
        IamIdentityProviderResult result = new IamIdentityProviderResult();
        try {
            result = authHandler.createIamIdentityProvider(iamIdentityProviderCreate);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(iamIdentityProviderCreate, result);
        result.setMsgId(iamIdentityProviderCreate.getMsgId());
        result.setOptions(iamIdentityProviderCreate.getOptions());
        return result;
    }

    /**
     * 华为IAM用户组详情
     */
    public IamUserGroupResult handleMessage(IamUserGroupGet iamUserGroupGet) {
        log.info("msg id : {}", iamUserGroupGet.getMsgId());
        IamUserGroupResult result = new IamUserGroupResult();
        try {
            result = authHandler.queryIamUserGroup(iamUserGroupGet);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(iamUserGroupGet, result);
        result.setMsgId(iamUserGroupGet.getMsgId());
        result.setOptions(iamUserGroupGet.getOptions());
        return result;
    }

    /**
     * 创建华为IAM用户组
     */
    public IamUserGroupResult handleMessage(IamUserGroupCreate iamUserGroupCreate) {
        log.info("msg id : {}", iamUserGroupCreate.getMsgId());
        IamUserGroupResult result = new IamUserGroupResult();
        try {
            result = authHandler.createIamUserGroup(iamUserGroupCreate);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(iamUserGroupCreate, result);
        result.setMsgId(iamUserGroupCreate.getMsgId());
        result.setOptions(iamUserGroupCreate.getOptions());
        return result;
    }

    /**
     * 查询华为IAM权限列表
     */
    public IamPermissionsListResult handleMessage(IamPermissionsListGet iamPermissionsListGet) {
        log.info("msg id : {}", iamPermissionsListGet.getMsgId());
        IamPermissionsListResult result = new IamPermissionsListResult();
        try {
            result = authHandler.queryIamPermissionsList(iamPermissionsListGet);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(iamPermissionsListGet, result);
        result.setMsgId(iamPermissionsListGet.getMsgId());
        result.setOptions(iamPermissionsListGet.getOptions());
        return result;
    }

    /**
     * 为华为IAM用户组授予项目服务权限
     */
    public BaseResult handleMessage(IamGroupWithProjectPermissionCreate projectPermissionCreate) {
        log.info("msg id : {}", projectPermissionCreate.getMsgId());
        BaseResult result = new BaseResult();
        try {
            result = authHandler.createIamGroupWithProjectPermission(projectPermissionCreate);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(projectPermissionCreate, result);
        result.setMsgId(projectPermissionCreate.getMsgId());
        result.setOptions(projectPermissionCreate.getOptions());
        return result;
    }

    /**
     * 查询指定条件下华为IAM的项目列表
     */
    public IamProjectsListResult handleMessage(IamProjectsListGet projectsListGet) {
        log.info("msg id : {}", projectsListGet.getMsgId());
        IamProjectsListResult result = new IamProjectsListResult();
        try {
            result = authHandler.queryIamProjectsList(projectsListGet);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(projectsListGet, result);
        result.setMsgId(projectsListGet.getMsgId());
        result.setOptions(projectsListGet.getOptions());
        return result;
    }

    /**
     * 注册华为IAM映射
     */
    public BaseResult handleMessage(IamMappingCreate iamMappingCreate) {
        log.info("msg id : {}", iamMappingCreate.getMsgId());
        BaseResult result = new BaseResult();
        try {
            result = authHandler.createIamMapping(iamMappingCreate);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(iamMappingCreate, result);
        result.setMsgId(iamMappingCreate.getMsgId());
        result.setOptions(iamMappingCreate.getOptions());
        return result;
    }

    /**
     * 注册华为IAM协议
     */
    public BaseResult handleMessage(IamProtocolCreate protocolCreate) {
        log.info("msg id : {}", protocolCreate.getMsgId());
        BaseResult result = new BaseResult();
        try {
            result = authHandler.createIamProtocol(protocolCreate);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(protocolCreate, result);
        result.setMsgId(protocolCreate.getMsgId());
        result.setOptions(protocolCreate.getOptions());
        return result;
    }

    /**
     * 华为IAM项目服务中的用户组权限查询
     */
    public IamPermissionsListResult handleMessage(IamGroupWithProjectPermissionListGet param) {
        log.info("msg id : {}", param.getMsgId());
        IamPermissionsListResult result = new IamPermissionsListResult();
        try {
            result = authHandler.queryGroupWithProjectPermissionList(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * 移除华为IAM用户组的项目服务权限
     */
    public BaseResult handleMessage(IamGroupWithProjectPermissionDelete param) {
        log.info("msg id : {}", param.getMsgId());
        BaseResult result = new BaseResult();
        try {
            result = authHandler.deleteGroupWithProjectPermission(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * 华为侧OpenID Connect身份提供商配置
     */
    public BaseResult handleMessage(IamOpenIdConnectCreate param) {
        log.info("msg id : {}", param.getMsgId());
        BaseResult result = new BaseResult();
        try {
            result = authHandler.createIamOpenIdConnect(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * 为华为IAM用户组授予全局服务权限
     */
    public BaseResult handleMessage(IamGroupWithDomainPermissionCreate param) {
        log.info("msg id : {}", param.getMsgId());
        BaseResult result = new BaseResult();
        try {
            result = authHandler.createIamGroupWithDomainPermission(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * 为华为IAM用户组以及委托授予全局服务权限
     */
    public BaseResult handleMessage(IamGroupAndEntrustWithDomainPermissionCreate param) {
        log.debug("msg id : {}", param.getMsgId());
        BaseResult result = new BaseResult();
        try {
            result = authHandler.createIamGroupAndEntrustWithDomainPermission(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * 移除华为IAM用户组的全局服务权限
     */
    public BaseResult handleMessage(IamGroupWithDomainPermissionDelete param) {
        log.debug("msg id : {}", param.getMsgId());
        BaseResult result = new BaseResult();
        try {
            result = authHandler.deleteGroupWithDomainPermission(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * 移除华为IAM用户组及授权的全局服务权限
     */
    public BaseResult handleMessage(IamGroupAndEntrustWithDomainPermissionDelete param) {
        log.debug("msg id : {}", param.getMsgId());
        BaseResult result = new BaseResult();
        try {
            result = authHandler.deleteGroupAndEntrustWithDomainPermission(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * 华为IAM全局服务中的用户组权限查询
     */
    public IamPermissionsListResult handleMessage(IamGroupWithDomainPermissionListGet param) {
        log.info("msg id : {}", param.getMsgId());
        IamPermissionsListResult result = new IamPermissionsListResult();
        try {
            result = authHandler.queryGroupWithDomainPermissionList(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * 配置ModelArts授权
     */
    public BaseResult handleMessage(IamModelArtsAuthorizationsCreate param) {
        log.info("msg id : {}", param.getMsgId());
        BaseResult result = new BaseResult();
        try {
            result = authHandler.createModelArtsAuthorizations(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * 查询华为IAM的用户列表
     */
    public IamUsersListResult handleMessage(IamUsersListGet param) {
        log.info("msg id : {}", param.getMsgId());
        IamUsersListResult result = new IamUsersListResult();
        try {
            result = authHandler.queryUsersList(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * 查询华为IAM的认证信息
     */
    public IamCredentialResult handleMessage(IamCredentialGet param) {
        log.info("msg id : {}", param.getMsgId());
        IamCredentialResult result = new IamCredentialResult();
        try {
            result = authHandler.queryCredential(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * 导入Metadata文件
     */
    public BaseResult handleMessage(IamMetadataCreate param) {
        log.info("msg id : {}", param.getMsgId());
        BaseResult result = new BaseResult();
        try {
            result = authHandler.createMetadata(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * 删除身份提供商
     */
    public BaseResult handleMessage(IamIdentityProviderDelete param) {
        log.info("msg id : {}", param.getMsgId());
        BaseResult result = new BaseResult();
        try {
            result = authHandler.deleteIdentityProvider(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * 删除用户组
     */
    public BaseResult handleMessage(IamUserGroupDelete param) {
        log.info("msg id : {}", param.getMsgId());
        BaseResult result = new BaseResult();
        try {
            result = authHandler.deleteUserGroup(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }


    /**
     * Handle message scan one share by env result.
     *
     * @param shareDetailByEnv the scan vms by env
     *
     * @return the scan vms by env result
     */
    public ShareDetailByEnvResult handleMessage(ShareDetailByEnv shareDetailByEnv) {

        log.info(
                "receiving message for scanning share, uuid : [{}]", shareDetailByEnv.getUUID());

        log.info("msg id : [{}]", shareDetailByEnv.getMsgId());


        ShareDetailByEnvResult oneShareByEnvResult =  new ShareDetailByEnvResult();

        try {
            oneShareByEnvResult = vmHandler.shareDetailByEnv(shareDetailByEnv);
            oneShareByEnvResult.setSuccess(true);
        } catch (CommonAdapterException e) {

            oneShareByEnvResult.setSuccess(false);
            oneShareByEnvResult.setErrCode(e.getErrCode());
            oneShareByEnvResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            oneShareByEnvResult.setSuccess(false);
            oneShareByEnvResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            oneShareByEnvResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            oneShareByEnvResult.setSuccess(false);
            oneShareByEnvResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            oneShareByEnvResult.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(shareDetailByEnv, oneShareByEnvResult);
        oneShareByEnvResult.setMsgId(shareDetailByEnv.getMsgId());
        return oneShareByEnvResult;

    }

    /**
     * IAM管理员创建IAM子用户
     */
    public IamSubUserResult handleMessage(IamSubUserCreate param) {
        log.info("msg id : {}", param.getMsgId());
        IamSubUserResult result = new IamSubUserResult();
        try {
            result = authHandler.createSubUser(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    public CheckCCPResult handleMessage(TestPassRequest param) {
        log.debug("msg id : {}", param.getMsgId());
        CheckCCPResult result = new CheckCCPResult();
        try {
            result = authHandler.HCSOCheckPass(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }
    public MountPreShareResult handleMessage(MountPreResShare param) {
        log.debug("msg id : {}", param.getMsgId());
        MountPreShareResult result = new MountPreShareResult();
        try {
            result = authHandler.FDShareMount(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        BeanUtils.copyProperties(param, result);
        return result;
    }

    /**
     * IAM管理员删除IAM子用户
     */
    public BaseResult handleMessage(IamSubUserDelete param) {
        log.info("msg id : {}", param.getMsgId());
        BaseResult result = new BaseResult();
        try {
            result = authHandler.deleteSubUser(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * IAM管理员修改IAM子用户
     */
    public BaseResult handleMessage(IamSubUserUpdate param) {
        log.info("msg id : {}", param.getMsgId());
        BaseResult result = new BaseResult();
        try {
            result = authHandler.updateSubUser(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * IAM管理员添加IAM用户到用户组
     */
    public BaseResult handleMessage(IamSubUserAddToGroup param) {
        log.info("msg id : {}", param.getMsgId());
        BaseResult result = new BaseResult();
        try {
            result = authHandler.addSubUserToGroup(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * IAM创建永久访问密钥
     */
    public IamPermanentAccessKeyResult handleMessage(IamPermanentAccessKeyCreate param) {
        log.info("msg id : {}", param.getMsgId());
        IamPermanentAccessKeyResult result = new IamPermanentAccessKeyResult();
        try {
            result = authHandler.createPermanentAccessKey(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * IAM删除永久访问密钥
     */
    public BaseResult handleMessage(IamPermanentAccessKeyDelete param) {
        log.info("msg id : {}", param.getMsgId());
        BaseResult result = new BaseResult();
        try {
            result = authHandler.deletePermanentAccessKey(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * OceanStor查询帐户
     */
    public OceanStorAccountResult handleMessage(OceanStorAccount param) {
        log.info("msg id : {}", param.getMsgId());
        OceanStorAccountResult result = new OceanStorAccountResult();
        try {
            result = oceanStorHandler.oceanStorAccount(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * OceanStor创建帐户
     */
    public OceanStorAccountCreateResult handleMessage(OceanStorAccountCreate param) {
        log.info("msg id : {}", param.getMsgId());
        OceanStorAccountCreateResult result = new OceanStorAccountCreateResult();
        try {
            result = oceanStorHandler.oceanStorAccountCreate(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * OceanStor删除帐户
     */
    public BaseResult handleMessage(OceanStorAccountDelete param) {
        log.info("msg id : {}", param.getMsgId());
        BaseResult result = new BaseResult();
        try {
            result = oceanStorHandler.oceanStorAccountDelete(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * OceanStor增加 Converged QoS策略关联关系
     */
    public BaseResult handleMessage(OceanStorConvergedQosAssociationCreate param) {
        log.info("msg id : {}", param.getMsgId());
        BaseResult result = new BaseResult();
        try {
            result = oceanStorHandler.oceanStorConvergedQosAssociationCreate(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * OceanStor解除Converged QoS策略和对象的关联关系
     */
    public BaseResult handleMessage(OceanStorConvergedQosAssociationDelete param) {
        log.info("msg id : {}", param.getMsgId());
        BaseResult result = new BaseResult();
        try {
            result = oceanStorHandler.oceanStorConvergedQosAssociationDelete(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * OceanStor创建Converged QoS策略
     */
    public OceanStorConvergedQosPolicyCreateResult handleMessage(OceanStorConvergedQosPolicyCreate param) {
        log.info("msg id : {}", param.getMsgId());
        OceanStorConvergedQosPolicyCreateResult result = new OceanStorConvergedQosPolicyCreateResult();
        try {
            result = oceanStorHandler.oceanStorConvergedQosPolicyCreate(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * OceanStor删除Converged QoS策略
     */
    public BaseResult handleMessage(OceanStorConvergedQosPolicyDelete param) {
        log.info("msg id : {}", param.getMsgId());
        BaseResult result = new BaseResult();
        try {
            result = oceanStorHandler.oceanStorConvergedQosPolicyDelete(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * OceanStor接入认证
     */
    public OceanStorLoginResult handleMessage(OceanStorLogin param) {
        log.info("msg id : {}", param.getMsgId());
        OceanStorLoginResult result = new OceanStorLoginResult();
        try {
            result = oceanStorHandler.oceanStorLogin(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * OceanStor查询命名空间
     */
    public OceanStorNamespacesResult handleMessage(OceanStorNamespaces param) {
        log.info("msg id : {}", param.getMsgId());
        OceanStorNamespacesResult result = new OceanStorNamespacesResult();
        try {
            result = oceanStorHandler.oceanStorNamespaces(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * OceanStor创建命名空间
     */
    public OceanStorNamespacesCreateResult handleMessage(OceanStorNamespacesCreate param) {
        log.info("msg id : {}", param.getMsgId());
        OceanStorNamespacesCreateResult result = new OceanStorNamespacesCreateResult();
        try {
            result = oceanStorHandler.oceanStorNamespacesCreate(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * OceanStor删除命名空间
     */
    public BaseResult handleMessage(OceanStorNamespacesDelete param) {
        log.info("msg id : {}", param.getMsgId());
        BaseResult result = new BaseResult();
        try {
            result = oceanStorHandler.oceanStorNamespacesDelete(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * OceanStor查询命名空间配额信息
     */
    public OceanStorNamespacesQuotaResult handleMessage(OceanStorNamespacesQuota param) {
        log.info("msg id : {}", param.getMsgId());
        OceanStorNamespacesQuotaResult result = new OceanStorNamespacesQuotaResult();
        try {
            result = oceanStorHandler.oceanStorNamespacesQuota(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * OceanStor创建命名空间配额
     */
    public OceanStorNamespacesQuotaCreateResult handleMessage(OceanStorNamespacesQuotaCreate param) {
        log.info("msg id : {}", param.getMsgId());
        OceanStorNamespacesQuotaCreateResult result = new OceanStorNamespacesQuotaCreateResult();
        try {
            result = oceanStorHandler.oceanStorNamespacesQuotaCreate(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * OceanStor删除命名空间配额
     */
    public BaseResult handleMessage(OceanStorNamespacesQuotaDelete param) {
        log.info("msg id : {}", param.getMsgId());
        BaseResult result = new BaseResult();
        try {
            result = oceanStorHandler.oceanStorNamespacesQuotaDelete(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * OceanStor修改命名空间配额
     */
    public BaseResult handleMessage(OceanStorNamespacesQuotaUpdate param) {
        log.info("msg id : {}", param.getMsgId());
        BaseResult result = new BaseResult();
        try {
            result = oceanStorHandler.oceanStorNamespacesQuotaUpdate(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * OceanStor分级特性迁移策略创建
     */
    public OceanStorTierPoliciesCreateResult handleMessage(OceanStorTierPoliciesCreate param) {
        log.info("msg id : {}", param.getMsgId());
        OceanStorTierPoliciesCreateResult result = new OceanStorTierPoliciesCreateResult();
        try {
            result = oceanStorHandler.oceanStorTierPoliciesCreate(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * OceanStor分级特性放置策略按名称删除
     */
    public OceanStorTierPoliciesDeleteResult handleMessage(OceanStorTierPoliciesDelete param) {
        log.info("msg id : {}", param.getMsgId());
        OceanStorTierPoliciesDeleteResult result = new OceanStorTierPoliciesDeleteResult();
        try {
            result = oceanStorHandler.oceanStorTierPoliciesDelete(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * OceanStor Pacific 批量查询Unix用户的基本信息。
     */
    public OceanStorUnixUserResult handleMessage(OceanStorUnixUser param) {
        log.info("msg id : {}", param.getMsgId());
        OceanStorUnixUserResult result = new OceanStorUnixUserResult();
        try {
            result = oceanStorHandler.oceanStorUnixUser(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * OceanStor Pacific 删除Unix用户。
     */
    public BaseResult handleMessage(OceanStorUnixUserDelete param) {
        log.info("msg id : {}", param.getMsgId());
        BaseResult result = new BaseResult();
        try {
            result = oceanStorHandler.oceanStorUnixUserDelete(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * OceanStor Pacific 批量查询Unix用户组的基本信息。
     */
    public OceanStorUnixUserGroupResult handleMessage(OceanStorUnixUserGroup param) {
        log.info("msg id : {}", param.getMsgId());
        OceanStorUnixUserGroupResult result = new OceanStorUnixUserGroupResult();
        try {
            result = oceanStorHandler.oceanStorUnixUserGroup(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }
    /**
     * OceanStor Pacific 删除Unix用户组。
     */
    public BaseResult handleMessage(OceanStorUnixUserGroupDelete param) {
        log.info("msg id : {}", param.getMsgId());
        BaseResult result = new BaseResult();
        try {
            result = oceanStorHandler.oceanStorUnixUserGroupDelete(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * 创建华为IAM用户组自定义权限
     */
    public IamCloudServiceCustomPolicyResult handleMessage(IamCloudServiceCustomPolicyCreate param) {
        log.info("msg id : {}", param.getMsgId());
        IamCloudServiceCustomPolicyResult result = new IamCloudServiceCustomPolicyResult();
        try {
            result = authHandler.creatCustomPermanent(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * 更新华为IAM用户组自定义权限
     */
    public BaseResult handleMessage(IamCloudServiceCustomPolicyUpdate param) {
        log.debug("msg id : {}", param.getMsgId());
        BaseResult result = new BaseResult();
        try {
            result = authHandler.updateCustomPermanent(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * 查询华为IAM用户组自定义权限
     */
    public IamCloudServiceCustomPolicyResult handleMessage(IamCloudServiceCustomPolicyCheck param) {
        log.info("msg id : {}", param.getMsgId());
        IamCloudServiceCustomPolicyResult result = new IamCloudServiceCustomPolicyResult();
        try {
            result = authHandler.checkCustomPermanent(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * 查询华为IAM用户组自定义权限(不新建)
     */
    public IamCustomPolicyQueryInfoResult handleMessage(IamCustomPolicyQueryInfo param) {
        log.info("msg id : {}", param.getMsgId());
        IamCustomPolicyQueryInfoResult result = new IamCustomPolicyQueryInfoResult();
        try {
            result = authHandler.queryCustomPolicyInfo(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * 删除华为IAM自定义权限
     * @param param
     */
    public BaseResult handleMessage(IamCloudServiceCustomPolicyDelete param) {
        log.debug("msg id : {}", param.getMsgId());
        BaseResult result = new BaseResult();

        try {
            result = authHandler.deleteCustomPermission(param);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * 创建网络资源
     * @param param
     */
    public BaseResult handleMessage(MANetworksCreate param) {
        log.info("msg id : {}", param.getMsgId());
        BaseResult result = new BaseResult();

        try {
            result = modelArtsHandler.modelArtsNetworksCreate(param);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * Handle message sfs update calculate result.
     */
    public ShareModifyResult handleMessage(ShareModify shareModify) {

        log.debug("receiving Action update for metric monitor, virtual type : [{}]", shareModify.getVirtEnvType());

        log.debug("msg id : [{}]", shareModify.getMsgId());

        ShareModifyResult shareModifyResult = new ShareModifyResult();

        try {
            shareModifyResult = vmHandler.shareModify(shareModify);
        } catch (CommonAdapterException e) {

            shareModifyResult.setSuccess(false);
            shareModifyResult.setErrCode(e.getErrCode());
            shareModifyResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            shareModifyResult.setSuccess(false);
            shareModifyResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            shareModifyResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            shareModifyResult.setSuccess(false);
            shareModifyResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            shareModifyResult.setErrMsg(e.getMessage());
        }
        shareModifyResult.setMsgId(shareModify.getMsgId());
        log.debug("ShareModifyResult id : [{}]", JSONUtil.toJsonStr(shareModifyResult));
        return shareModifyResult;

    }

    /**
     * Handle message sfs update calculate result.
     */
    public ShareModifyResult handleMessage(SharePredeployModify sharePredeployModify) {

        log.debug("receiving Action update for metric monitor, virtual type : [{}]", sharePredeployModify.getVirtEnvType());

        log.debug("msg id : [{}]", sharePredeployModify.getMsgId());

        ShareModifyResult shareModifyResult = new ShareModifyResult();

        try {
            shareModifyResult = vmHandler.shareModify(sharePredeployModify);
        } catch (CommonAdapterException e) {

            shareModifyResult.setSuccess(false);
            shareModifyResult.setErrCode(e.getErrCode());
            shareModifyResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            shareModifyResult.setSuccess(false);
            shareModifyResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            shareModifyResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            shareModifyResult.setSuccess(false);
            shareModifyResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            shareModifyResult.setErrMsg(e.getMessage());
        }
        shareModifyResult.setOperate("extend".equals(sharePredeployModify.getOperationType()) ? "add" : "up");
        shareModifyResult.setId(sharePredeployModify.getId());
        shareModifyResult.setServiceOrderId(sharePredeployModify.getServiceOrderId());
        shareModifyResult.setMsgId(sharePredeployModify.getMsgId());
        log.debug("ShareModifyResult id : [{}]", JSONUtil.toJsonStr(shareModifyResult));
        return shareModifyResult;

    }

    /**
     * 查询预部署文件系统
     * @param sharePredeployQuery
     * @return
     */
    public SharePredeployQueryResult handleMessage(SharePredeployQuery sharePredeployQuery) {

        log.debug("receiving message for resPools, virtual type : [{}]", sharePredeployQuery.getVirtEnvType());

        log.debug("msg id : [{}]", sharePredeployQuery.getMsgId());

        SharePredeployQueryResult sharePredeployQueryResult = new SharePredeployQueryResult();
        try {
            sharePredeployQueryResult = authHandler.queryShareFromFd(sharePredeployQuery);
        } catch (CommonAdapterException e) {
            sharePredeployQueryResult.setSuccess(false);
            sharePredeployQueryResult.setErrCode(e.getErrCode());
            sharePredeployQueryResult.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            sharePredeployQueryResult.setSuccess(false);
            sharePredeployQueryResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            sharePredeployQueryResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {

            sharePredeployQueryResult.setSuccess(false);
            sharePredeployQueryResult.setErrMsg(e.getMessage());
            log.error("adapter共通异常-SharePredeployQuery:[{}]", e.getMessage());
        }
        BaseUtil.setResult(sharePredeployQuery, sharePredeployQueryResult);
        sharePredeployQueryResult.setMsgId(sharePredeployQuery.getMsgId());
        return sharePredeployQueryResult;
    }

    /**
     * HPC预部署
     * @param param
     */
    public HPCPreDrpMgmtResult handleMessage(HPCPreDrpMgmt param) {
        log.debug("msg id : {}", param.getMsgId());
        HPCPreDrpMgmtResult result = new HPCPreDrpMgmtResult();

        try {
            result = hpcHandler.hpcPreDrpMgmt(param);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }
    /**
     * HPC预部署
     * @param param
     */
    public MANetworksDeleteResult handleMessage(MANetworksDelete param) {
        log.info("msg id : {}", param.getMsgId());
        MANetworksDeleteResult result = new MANetworksDeleteResult();

        try {
            result = modelArtsHandler.modelArtsNetworksDelete(param);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }


    public SharePredeployUninstallResult handleMessage(SharePredeployUninstall sharePredeployUninstall){
        log.debug("receiving Action update for metric monitor, virtual type : [{}]", sharePredeployUninstall.getVirtEnvType());

        log.debug("msg id : [{}]", sharePredeployUninstall.getMsgId());
        SharePredeployUninstallResult sharePredeployUninstallResult = new SharePredeployUninstallResult();
        try {
            sharePredeployUninstallResult = vmHandler.shareUninstall(sharePredeployUninstall);
        } catch (CommonAdapterException e) {
            sharePredeployUninstallResult.setSuccess(false);
            sharePredeployUninstallResult.setErrCode(e.getErrCode());
            sharePredeployUninstallResult.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            sharePredeployUninstallResult.setSuccess(false);
            sharePredeployUninstallResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            sharePredeployUninstallResult.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            sharePredeployUninstallResult.setSuccess(false);
            sharePredeployUninstallResult.setErrCode(Constants.AdapterUnvailableException.CODE);
            sharePredeployUninstallResult.setErrMsg(e.getMessage());
        }
        BeanUtils.copyProperties(sharePredeployUninstall, sharePredeployUninstallResult);
        sharePredeployUninstallResult.setUuid(sharePredeployUninstall.getFileSystemID());
        log.debug("sharePredeployUninstallResult id : [{}]", JSONUtil.toJsonStr(sharePredeployUninstallResult));

        return sharePredeployUninstallResult;
    }
    /**
     * 查询网络资源列表
     * @param param
     */
    public MANetworksListQueryResult handleMessage(MANetworksListQuery param) {
        log.info("msg id : {}", param.getMsgId());
        MANetworksListQueryResult result = new MANetworksListQueryResult();

        try {
            result = modelArtsHandler.modelArtsNetworksListQuery(param);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * 查询指定网络资源的描述信息
     * @param param
     */
    public MANetworksQueryResult handleMessage(MANetworksQuery param) {
        log.info("msg id : {}", param.getMsgId());
        MANetworksQueryResult result = new MANetworksQueryResult();

        try {
            result = modelArtsHandler.modelArtsNetworksQuery(param);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * 更新指定网络资源
     * @param param
     */
    public MANetworksUpdateResult handleMessage(MANetworksUpdate param) {
        log.info("msg id : {}", param.getMsgId());
        MANetworksUpdateResult result = new MANetworksUpdateResult();

        try {
            result = modelArtsHandler.modelArtsNetworksUpdate(param);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * (共享资源池)获取单个资源池的运行态信息
     * @param param
     */
    public MASharePoolsQueryResult handleMessage(MASharePoolsQuery param) {
        log.info("msg id : {}", param.getMsgId());
        MASharePoolsQueryResult result = new MASharePoolsQueryResult();

        try {
            result = modelArtsHandler.modelArtsSharePoolsQuery(param);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * (共享资源池)更新资源池信息
     * @param param
     */
    public BaseResult handleMessage(MASharePoolsUpdate param) {
        log.info("msg id : {}", param.getMsgId());
        BaseResult result = new BaseResult();

        try {
            result = modelArtsHandler.modelArtsSharePoolsUpdate(param);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * 用户创建资源池
     * @param param
     */
    public BaseResult handleMessage(MAPoolsCreate param) {
        log.info("msg id : {}", param.getMsgId());
        BaseResult result = new BaseResult();

        try {
            result = modelArtsHandler.modelArtsPoolsCreate(param);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * 删除指定的资源池
     * @param param
     */
    public MAPoolsDeleteResult handleMessage(MAPoolsDelete param) {
        log.info("msg id : {}", param.getMsgId());
        MAPoolsDeleteResult result = new MAPoolsDeleteResult();

        try {
            result = modelArtsHandler.modelArtsPoolsDelete(param);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * 查询资源池列表
     * @param param
     */
    public MAPoolsListQueryResult handleMessage(MAPoolsListQuery param) {
        log.info("msg id : {}", param.getMsgId());
        MAPoolsListQueryResult result = new MAPoolsListQueryResult();

        try {
            result = modelArtsHandler.modelArtsPoolsListQuery(param);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * 获取资源池的监控信息
     * @param param
     */
    public BaseResult handleMessage(MAPoolsMonitor param) {
        log.info("msg id : {}", param.getMsgId());
        BaseResult result = new BaseResult();

        try {
            result = modelArtsHandler.modelArtsPoolsMonitor(param);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * 查询指定资源池的描述信息
     * @param param
     */
    public MAPoolsQueryResult handleMessage(MAPoolsQuery param) {
        log.info("msg id : {}", param.getMsgId());
        MAPoolsQueryResult result = new MAPoolsQueryResult();

        try {
            result = modelArtsHandler.modelArtsPoolsQuery(param);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * 更新指定的资源池
     * @param param
     */
    public MAPoolsUpdateResult handleMessage(MAPoolsUpdate param) {
        log.info("msg id : {}", param.getMsgId());
        MAPoolsUpdateResult result = new MAPoolsUpdateResult();

        try {
            result = modelArtsHandler.modelArtsPoolsUpdate(param);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * 查询资源规格列表
     * @param param
     */
    public MAResourceFlavorsQueryResult handleMessage(MAResourceFlavorsQuery param) {
        log.info("msg id : {}", param.getMsgId());
        MAResourceFlavorsQueryResult result = new MAResourceFlavorsQueryResult();

        try {
            result = modelArtsHandler.modelArtsResourceFlavorsQuery(param);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }


    /**
     * 查询资源规格列表
     * @param param
     */
    public MANetworkCidrsResult handleMessage(MANetworksCidrsQuery param) {
        log.info("msg id : {}", param.getMsgId());
        MANetworkCidrsResult result = new MANetworkCidrsResult();

        try {
            result = modelArtsHandler.queryNetworksCidrs(param);
        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * modelArts集群使用信息。
     */
    public OsPoolsAllocateResult handleMessage(OsPoolsAllocateRequest param) {
        log.info("OsPoolsAllocate msg id : {}", param.getMsgId());
        OsPoolsAllocateResult result = new OsPoolsAllocateResult();
        try {
            result = modelArtsHandler.OsPoolsAllocate(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }


    /**
     * (MA共享资源池) 停止作业
     */
    public BaseResult handleMessage(MASharePoolsStopJob param) {
        log.debug("msg id : {}", param.getMsgId());
        BaseResult result = new BaseResult();
        try {
            result = authHandler.maSharePoolsStopJob(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }


    /**
     * (MA-BMS资源池) 启动作业
     */
    public BaseResult handleMessage(MABmsStarJob param) {
        log.debug("msg id : {}", param.getMsgId());
        BaseResult result = new BaseResult();
        try {
            result = authHandler.maBmsStarJob(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }


    /**
     * (MA-BMS资源池) 停止作业
     */
    public BaseResult handleMessage(MABmsStopJob param) {
        log.debug("msg id : {}", param.getMsgId());
        BaseResult result = new BaseResult();
        try {
            result = authHandler.maBmsStopJob(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * (MA-BMS资源池) 删除资源
     */
    public BaseResult handleMessage(MABmsDelJob param) {
        log.debug("msg id : {}", param.getMsgId());
        BaseResult result = new BaseResult();
        try {
            result = authHandler.maBmsDelJob(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * modelarts(训练)，集群信息。
     */
    public TrainingPoolsV1Result handleMessage(TrainingPoolsRequest param) {
        TrainingPoolsV1Result result = new TrainingPoolsV1Result();
        try {
            result = modelArtsHandler.trainingPoolsV1(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * （训练） 共享资源池信息
     * @param param
     * @return
     */
    public BaseResult handleMessage(TrainingFlavorsRequest param) {
        BaseResult result = new BaseResult();
        try {
            result = modelArtsHandler.trainingFlavors(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * （训练） 查询活跃状态作业列表v1
     * @param param
     * @return
     */
    public BaseResult handleMessage(TrainingActiveJobsRequest param) {
        log.debug("TrainingActiveJobs msg id : {}", param.getMsgId());
        BaseResult result = new BaseResult();
        try {
            result = modelArtsHandler.trainingActiveJobs(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * （训练） 查询活跃状态作业列表v2
     * @param param
     * @return
     */
    public TrainingActiveJobsV2Result handleMessage(TrainingActiveJobsV2Request param) {
        log.debug("TrainingActiveJobsV2 msg id : {}", param.getMsgId());
        TrainingActiveJobsV2Result result = new TrainingActiveJobsV2Result();
        try {
            result = modelArtsHandler.trainingActiveJobsV2(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * （训练） 多条件查询作业列表v1
     * @param param
     * @return
     */
    public BaseResult handleMessage(TrainingJobsRequest param) {
        log.debug("TrainingJobs msg id : {}", param.getMsgId());
        BaseResult result = new BaseResult();
        try {
            result = modelArtsHandler.trainingJobs(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * （训练） 多条件查询作业列表v2
     * @param param
     * @return
     */
    public TrainingJobsV2Result handleMessage(TrainingJobsV2Request param) {
        log.debug("TrainingJobsV2 msg id : {}", param.getMsgId());
        TrainingJobsV2Result result = new TrainingJobsV2Result();
        try {
            result = modelArtsHandler.trainingJobsV2(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }
    public FDHPCClusterResult handleMessage(FDHPCClusterRequest param) {
        log.debug("Summary msg id : {}", param.getMsgId());
        FDHPCClusterResult result = new FDHPCClusterResult();
        try {
            result = hpcHandler.clusters(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }
    /**
     *  按照各个维度实时查询Notebook列表
     *  * @param param
     * @return
     */
    public StatisticsNotebooksResult handleMessage(StatisticsNotebooksRequest param) {
        log.debug("StatisticsNotebooksResult msg id : {}", param.getMsgId());
        StatisticsNotebooksResult result = new StatisticsNotebooksResult();
        try {
            result = modelArtsHandler.StatisticsNotebooks(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }
    /**
     *  os集群信息v1
     * @param param
     * @return
     */
    public OsClusterResourceSnapshotResult handleMessage(OsClusterResourceSnapshotRequest param) {
        OsClusterResourceSnapshotResult result = new OsClusterResourceSnapshotResult();
        try {
            result = modelArtsHandler.osClusterResourceSnapshot(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }


    /**
     *  os 查询资源池分配率 v2
     *  * @param param
     * @return
     */
    public OsPoolsV2Result handleMessage(OsPoolsV2Request param) {
        OsPoolsV2Result result = new OsPoolsV2Result();
        try {
            result = modelArtsHandler.osPoolsV2(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     *  os 查询资源池分配率 v2
     *  * @param param
     * @return
     */
    public SummaryResult handleMessage(SummaryRequest param) {
        log.debug("Summary msg id : {}", param.getMsgId());
        SummaryResult result = new SummaryResult();
        try {
            result = modelArtsHandler.summary(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     *  查询资源池下基本信息
     *  * @param param
     * @return
     */
    public NotebooksResult handleMessage(NoteBooksRequest param) {
        log.debug("Notebooks msg id : {}", param.getMsgId());
        NotebooksResult result = new NotebooksResult();
        try {
            result = modelArtsHandler.notebooks(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     *  供白名单指定账号查询推理资源池的列表
     *  * @param param
     * @return
     */
    public ClustersResult handleMessage(ClustersRequest param) {
        log.debug("Summary msg id : {}", param.getMsgId());
        ClustersResult result = new ClustersResult();
        try {
            result = modelArtsHandler.clusters(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     *  供白名单指定账号查询指定资源池的服务列表
     *  * @param param
     * @return
     */
    public ServicesResult handleMessage(ServicesRequest param) {
        log.debug("Summary msg id : {}", param.getMsgId());
        ServicesResult result = new ServicesResult();
        try {
            result = modelArtsHandler.services(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * 国密3hash盐
     * @param param
     * @return
     */
    public CCSPIntegralityHashResult handleMessage(CCSPIntegralityHash param) {
        log.debug("TrainingActiveJobsV2 msg id : {}", param.getMsgId());
        CCSPIntegralityHashResult result = new CCSPIntegralityHashResult();
        try {
            result = authHandler.checkPIntegralityHash(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * 删除委托
     **/
    public IamAgencyDeleteResult handleMessage(IamAgencyDelete delete) {
        log.info("IamAgencyDelete handleMessage msg id : {}", delete.getMsgId());
        IamAgencyDeleteResult result = new IamAgencyDeleteResult();
        try {
            result = authHandler.deleteIamAgency(delete);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(delete, result);
        result.setMsgId(delete.getMsgId());
        result.setOptions(delete.getOptions());
        return result;
    }
    /**
     * 删除自定义策略
     **/
    public IamCustomPolicyDeleteResult handleMessage(IamCustomPolicyDelete delete) {
        log.info("IamCustomPolicyDelete handleMessage msg id : {}", delete.getMsgId());
        IamCustomPolicyDeleteResult result = new IamCustomPolicyDeleteResult();
        try {
            result = authHandler.deleteIamCustomPolicy(delete);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(delete, result);
        result.setMsgId(delete.getMsgId());
        result.setOptions(delete.getOptions());
        return result;
    }
    /**
     * 查询自定义策略列表
     **/
    public IamCustomPolicyListResult handleMessage(IamCustomPolicyListGet listGet) {
        log.info("IamCustomPolicyDelete handleMessage msg id : {}", listGet.getMsgId());
        IamCustomPolicyListResult result = new IamCustomPolicyListResult();
        try {
            result = authHandler.listIamCustomPolicy(listGet);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(listGet, result);
        result.setMsgId(listGet.getMsgId());
        result.setOptions(listGet.getOptions());
        return result;
    }


    /** ModelArts委托  **/

    /**
     *  查看授权列表
     *  * @param param
     * @return
     */
    public MAAgencyQueryListRes handleMessage(MAAgencyQueryList param) {
        log.debug("Summary msg id : {}", param.getMsgId());
        MAAgencyQueryListRes result = new MAAgencyQueryListRes();
        try {
            result = modelArtsHandler.queryListAgency(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     *  创建授权
     *  * @param param
     * @return
     */
    public MAAgencyCreateAuthorizeRes handleMessage(MAAgencyCreateAuthorize param) {
        log.debug("Summary msg id : {}", param.getMsgId());
        MAAgencyCreateAuthorizeRes result = new MAAgencyCreateAuthorizeRes();
        try {
            result = modelArtsHandler.agencyCreateAuthorize(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     *  创建ModelArts委托
     *  * @param param
     * @return
     */
    public MAAgencyCreateRes handleMessage(MAAgencyCreate param) {
        log.debug("Summary msg id : {}", param.getMsgId());
        MAAgencyCreateRes result = new MAAgencyCreateRes();
        try {
            result = modelArtsHandler.agencyCreate(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }


    /**
     *  查看授权列表
     *  * @param param
     * @return
     */
    public MAAgencyDeleteRes handleMessage(MAAgencyDelete param) {
        log.debug("Summary msg id : {}", param.getMsgId());
        MAAgencyDeleteRes result = new MAAgencyDeleteRes();
        try {
            result = modelArtsHandler.agencyDelete(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     *  查看授权列表
     *  * @param param
     * @return
     */
    public MAAgencyQueryAgenciesRes handleMessage(MAAgencyQueryAgencies param) {
        log.debug("Summary msg id : {}", param.getMsgId());
        MAAgencyQueryAgenciesRes result = new MAAgencyQueryAgenciesRes();
        try {
            result = modelArtsHandler.agencyQueryAgencies(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     *  查看授权列表
     *  * @param param
     * @return
     */
    public QueryAgencyInfoResult handleMessage(QueryAgencyInfo param) {
        log.debug("Summary msg id : {}", param.getMsgId());
        QueryAgencyInfoResult result = new QueryAgencyInfoResult();
        try {
            result = modelArtsHandler.QueryAgencyInfo(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     *  查看授权列表
     *  * @param param
     * @return
     */
    public MAAgencyQueryQuotasRes handleMessage(MAAgencyQueryQuotas param) {
        log.debug("Summary msg id : {}", param.getMsgId());
        MAAgencyQueryQuotasRes result = new MAAgencyQueryQuotasRes();
        try {
            result = modelArtsHandler.agencyQueryQuotas(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * 查询用户组的所有项目权限列表
     */
    public BaseResult handleMessage(IamKeystoneListAllProjectPermissionsForGroupSolution param) {
        log.debug("msg id : {}", param.getMsgId());
        BaseResult result = new BaseResult();
        try {
            result = authHandler.keystoneListAllProjectPermissionsForGroupSolution(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * 移除用户组的所有项目服务权限
     */
    public BaseResult handleMessage(IamDeleteDomainGroupInheritedRoleSolution param) {
        log.debug("msg id : {}", param.getMsgId());
        BaseResult result = new BaseResult();
        try {
            result = authHandler.deleteDomainGroupInheritedRoleSolutionPermission(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }

    /**
     * 为用户组授予所有项目服务权限
     */
    public BaseResult handleMessage(IamUpdateDomainGroupInheritRole param) {
        log.debug("msg id : {}", param.getMsgId());
        BaseResult result = new BaseResult();
        try {
            result = authHandler.updateDomainGroupInheritRolePermission(param);

        } catch (CommonAdapterException e) {

            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());

        } catch (AdapterUnavailableException e) {

            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }
    /**
     *  查询指定任务详情
     *
     * @return
     */
    public DMETaskInfoQueryResult handleMessage(DMETaskInfoQueryRequest param) {
        log.debug("DMETaskInfoQueryRequest msg id : {}", param.getMsgId());
        return commonHandleMessage(param,DMETaskInfoQueryResult.class, dmeHandler::taskInfoQuery);
    }

    /**
     * 创建配额
     */
    public DMECreateQuotaResult handleMessage(DMECreateQuotaRequest param) {
        log.debug("DMECreateQuotaResult msg id : {}", param.getMsgId());
        return commonHandleMessage(param, DMECreateQuotaResult.class, dmeHandler::createQuota);
    }

    /**
     * 删除配额
     */
    public DMEDeleteQuotaResult handleMessage(DMEDeleteQuotaRequest param) {
        log.debug("DMEDeleteQuotaResult msg id : {}", param.getMsgId());
        return commonHandleMessage(param, DMEDeleteQuotaResult.class, dmeHandler::deleteQuota);
    }
    /**
     * 查询配额
     */
    public DMEQuotaListQueryResult handleMessage(DMEQuotaListQueryRequest param) {
        log.debug("DMEDeleteQuotaResult msg id : {}", param.getMsgId());
        return commonHandleMessage(param, DMEQuotaListQueryResult.class, dmeHandler::quotaListQuery);
    }
    /**
     * 更新指定配额
     */
    public DMEQuotaUpdateResult handleMessage(DMEQuotaUpdateRequest param) {
        log.debug("DMEDeleteQuotaResult msg id : {}", param.getMsgId());
        return commonHandleMessage(param, DMEQuotaUpdateResult.class, dmeHandler::quotaUpdate);
    }

    /**
     * 处理共通业务
     */
    public <T extends Base,R extends BaseResult> R commonHandleMessage(T param, Class<R> resultClass, ThrowingFunction<T,R> handleFunction) {
        R result = createEmptyResult(resultClass);
        try {
            result = handleFunction.apply(param);
        } catch (CommonAdapterException e) {
            result.setSuccess(false);
            result.setErrCode(e.getErrCode());
            result.setErrMsg(e.getErrMsg());
        } catch (AdapterUnavailableException e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(Constants.AdapterUnvailableException.MSG);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrCode(Constants.AdapterUnvailableException.CODE);
            result.setErrMsg(e.getMessage());
        }
        BaseUtil.setResult(param, result);
        result.setMsgId(param.getMsgId());
        result.setOptions(param.getOptions());
        return result;
    }
    // 添加这个方法，创建一个空的BaseResult实例
    private   <R extends BaseResult> R createEmptyResult(Class<R> resultClass) {
        try {
            return resultClass.getDeclaredConstructor().newInstance();
        } catch (InstantiationException | IllegalAccessException | NoSuchMethodException | InvocationTargetException e) {
            throw new RuntimeException("Failed to create an empty result instance", e);
        }
    }

    /**
     * 自定义接口
     * @param <T>
     * @param <R>
     */
    @FunctionalInterface
    private interface ThrowingFunction<T, R> {
        R apply(T t) throws Exception;
    }

}
