/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.adapter.facade.handler;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.com.cloudstar.rightcloud.adapter.pojo.mq.DescribeAvailableZone;
import cn.com.cloudstar.rightcloud.adapter.pojo.mq.DescribeProductSpec;
import cn.com.cloudstar.rightcloud.adapter.pojo.mq.KafkaTopicCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.mq.KafkaTopicDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.mq.MqInstanceAction;
import cn.com.cloudstar.rightcloud.adapter.pojo.mq.MqInstanceCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.mq.MqInstanceDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.mq.MqInstanceUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.mq.result.DescribeAvailableZoneResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.mq.result.DescribeProductSpecResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.mq.result.KafkaTopicCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.mq.result.KafkaTopicDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.mq.result.MqInstanceActionResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.mq.result.MqInstanceCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.mq.result.MqInstanceDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.mq.result.MqInstanceUpdateResult;
import cn.com.cloudstar.rightcloud.driver.core.ActionServiceFactory;
import cn.com.cloudstar.rightcloud.driver.core.exception.AdapterUnavailableException;
import cn.com.cloudstar.rightcloud.driver.core.exception.CommonAdapterException;

/**
 * 分布式消息服务Handler
 *
 * <AUTHOR>
 * @date 2019-09-17 14:56
 */
@Service
public class DmsHandler {

    @Autowired
    protected ActionServiceFactory actionServiceFactory;

    /**
     * get products of dms.
     *
     * @param products the products of dms
     * @return the connect string result
     * @throws CommonAdapterException      the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public DescribeProductSpecResult getProducts(DescribeProductSpec products) throws CommonAdapterException, AdapterUnavailableException {
        return (DescribeProductSpecResult) actionServiceFactory.getActionService(products).invoke(products);
    }

    /**
     * get available zones of dms.
     *
     * @param availableZones the available zones of dms
     * @return the connect string result
     * @throws CommonAdapterException      the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public DescribeAvailableZoneResult getAvailableZones(DescribeAvailableZone availableZones) throws CommonAdapterException, AdapterUnavailableException {
        return (DescribeAvailableZoneResult) actionServiceFactory.getActionService(availableZones).invoke(availableZones);
    }

    /**
     * create mq instance.
     *
     * @param instanceCreate the mq instance
     * @return the connect string result
     * @throws CommonAdapterException      the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public MqInstanceCreateResult createMqInstance(MqInstanceCreate instanceCreate) throws CommonAdapterException, AdapterUnavailableException {
        return (MqInstanceCreateResult) actionServiceFactory.getActionService(instanceCreate).invoke(instanceCreate);
    }

    /**
     * update mq instance.
     *
     * @param instanceUpdate the mq instance
     * @return the connect string result
     * @throws CommonAdapterException      the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public MqInstanceUpdateResult updateMqInstance(MqInstanceUpdate instanceUpdate) throws CommonAdapterException, AdapterUnavailableException {
        return (MqInstanceUpdateResult) actionServiceFactory.getActionService(instanceUpdate).invoke(instanceUpdate);
    }

    /**
     * delete mq instance.
     *
     * @param instanceDelete the mq instance
     * @return the connect string result
     * @throws CommonAdapterException      the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public MqInstanceDeleteResult deleteMqInstance(MqInstanceDelete instanceDelete) throws CommonAdapterException, AdapterUnavailableException {
        return (MqInstanceDeleteResult) actionServiceFactory.getActionService(instanceDelete).invoke(instanceDelete);
    }

    /**
     * delete or restart mq instance.
     *
     * @param instanceAction the mq instance action
     * @return the connect string result
     * @throws CommonAdapterException      the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public MqInstanceActionResult operateMqInstance(MqInstanceAction instanceAction) throws CommonAdapterException, AdapterUnavailableException {
        return (MqInstanceActionResult) actionServiceFactory.getActionService(instanceAction).invoke(instanceAction);
    }

    /**
     * create a topic in kafka instance.
     *
     * @param kafkaTopicCreate the topic info of a kafka instance
     * @return the connect string result
     * @throws CommonAdapterException      the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public KafkaTopicCreateResult createKafkaTopic(KafkaTopicCreate kafkaTopicCreate) throws CommonAdapterException, AdapterUnavailableException {
        return (KafkaTopicCreateResult) actionServiceFactory.getActionService(kafkaTopicCreate).invoke(kafkaTopicCreate);
    }

    /**
     * delete the topic of a kafka instance.
     *
     * @param kafkaTopicDelete the topic info of a kafka instance
     * @return the connect string result
     * @throws CommonAdapterException      the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public KafkaTopicDeleteResult deleteKafkaTopic(KafkaTopicDelete kafkaTopicDelete) throws CommonAdapterException, AdapterUnavailableException {
        return (KafkaTopicDeleteResult) actionServiceFactory.getActionService(kafkaTopicDelete).invoke(kafkaTopicDelete);
    }

}
