/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.adapter.facade.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.text.SimpleDateFormat;

import lombok.extern.slf4j.Slf4j;

import cn.com.cloudstar.rightcloud.driver.pojo.base.BaseResult;

@Slf4j
public class BaseUtil {

    private static ObjectMapper MAPPER;
    private static Logger logger = LoggerFactory.getLogger(BaseUtil.class);
    private static String TwoBlank = "  ";
    private static String OneBlank = " ";

    static {
        MAPPER = generateMapper(JsonInclude.Include.ALWAYS);
    }

    private static ObjectMapper generateMapper(JsonInclude.Include inclusion) {

        ObjectMapper customMapper = new ObjectMapper();

        // 设置输出时包含属性的风格
        customMapper.setSerializationInclusion(inclusion);

        // 设置输入时忽略在JSON字符串中存在但Java对象实际没有的属性
        customMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        // 禁止使用int代表Enum的order()來反序列化Enum,非常危險
        customMapper.configure(DeserializationFeature.FAIL_ON_NUMBERS_FOR_ENUMS, true);

        // 所有日期格式都统一为以下样式
        customMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));

        return customMapper;
    }

    public static <T> String toJson(T src) {
        String json = null;
        try {
            json = src instanceof String ? (String) src : MAPPER.writeValueAsString(src);
        } catch (IOException e) {
            log.error(e.getMessage());
        }
        return json;
    }

    @SuppressWarnings("unchecked")
    public static <T> T fromJson(String json, Class<T> clazz) throws IOException {
        return String.class.equals(clazz) ? (T) json : MAPPER.readValue(json, clazz);
    }

    public static <T> T setResult(cn.com.cloudstar.rightcloud.basic.data.pojo.base.Base base, Class<T> result) {
        BaseResult result2 = new BaseResult();
        try {
            result2 = (BaseResult) result.newInstance();
            result2.setProviderType(base.getProviderType());
            result2.setVirtEnvType(base.getVirtEnvType());
            result2.setVirtEnvUuid(base.getVirtEnvUuid());
            result2.setOptions(base.getOptions());
            result2.setOpUser(base.getOpUser());
            result2.setOrgSid(base.getOrgSid());
            result2.setCloudEnvId(base.getCloudEnvId());
        } catch (InstantiationException | IllegalAccessException e) {
            log.error(e.getMessage());
        }
        return (T) result2;
    }

    public static <R extends BaseResult> R setResult(cn.com.cloudstar.rightcloud.basic.data.pojo.base.Base base,
                                                     R result) {
        result.setProviderType(base.getProviderType());
        result.setVirtEnvType(base.getVirtEnvType());
        result.setVirtEnvUuid(base.getVirtEnvUuid());
        result.setOptions(base.getOptions());
        result.setOpUser(base.getOpUser());
        result.setOrgSid(base.getOrgSid());
        result.setCloudEnvId(base.getCloudEnvId());
        result.setRCLinkType(base.isRCLinkType());
        return result;
    }

}
