/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.adapter.facade.handler;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.com.cloudstar.rightcloud.adapter.pojo.bms.request.BareMetalList;
import cn.com.cloudstar.rightcloud.adapter.pojo.bms.result.ListBareMetalResult;
import cn.com.cloudstar.rightcloud.driver.core.ActionServiceFactory;
import cn.com.cloudstar.rightcloud.driver.core.exception.AdapterUnavailableException;
import cn.com.cloudstar.rightcloud.driver.core.exception.CommonAdapterException;

/**
 * 裸金属Handler
 *
 * <AUTHOR>
 * @date 2023-09-27
 */
@Service
public class BmsHandler {

    @Autowired
    protected ActionServiceFactory actionServiceFactory;

    public ListBareMetalResult listBareMetalServers(BareMetalList bareMetalList)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ListBareMetalResult) actionServiceFactory.getActionService(bareMetalList).invoke(bareMetalList);
    }

}
