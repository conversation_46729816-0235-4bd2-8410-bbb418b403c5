/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.adapter.facade.handler;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiAlgorithmsQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiModelsQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopCreateTo;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopDeleteTo;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopDetailsQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopLicenseCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopLifecycleUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopMineQueryAll;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopProcess;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopQueryAll;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopRenew;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopSubscribesQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopSubscriptionsCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopSubscriptionsDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopSubscriptionsRenew;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopUpdateTo;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopVersionCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopVersionCreateTo;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopVersionDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopVersionUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopVersionUpdateTo;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.AiShopVersionsQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiAlgorithmsQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiModelsQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiShopCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiShopCreateToResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiShopDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiShopDeleteToResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiShopDetailsQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiShopLicenseCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiShopMineQueryAllResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiShopQueryAllResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiShopSubscribesQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiShopVersionCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiShopVersionCreateResultTo;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiShopVersionUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiShopVersionUpdateResultTo;
import cn.com.cloudstar.rightcloud.adapter.pojo.ai.result.AiShopVersionsQueryResult;
import cn.com.cloudstar.rightcloud.driver.core.ActionServiceFactory;
import cn.com.cloudstar.rightcloud.driver.core.exception.AdapterUnavailableException;
import cn.com.cloudstar.rightcloud.driver.core.exception.CommonAdapterException;
import cn.com.cloudstar.rightcloud.driver.pojo.base.BaseResult;

/**
 * AIHUB处理程序
 *
 * <AUTHOR>
 * @date 2023/08/17
 */
@Service
public class AiHubHandler {

    @Autowired
    private ActionServiceFactory actionServiceFactory;

    public AiShopCreateResult create(AiShopCreate param) throws CommonAdapterException, AdapterUnavailableException {
        return (AiShopCreateResult) actionServiceFactory.getActionService(param).invoke(param);
    }
    public AiShopCreateToResult createTo(AiShopCreateTo param) throws CommonAdapterException, AdapterUnavailableException {
        return (AiShopCreateToResult) actionServiceFactory.getActionService(param).invoke(param);
    }
    public AiShopDeleteResult delete(AiShopDelete param) throws CommonAdapterException, AdapterUnavailableException {
        return (AiShopDeleteResult) actionServiceFactory.getActionService(param).invoke(param);
    }
    public AiShopDeleteToResult deleteTo(AiShopDeleteTo param) throws CommonAdapterException, AdapterUnavailableException {
        return (AiShopDeleteToResult) actionServiceFactory.getActionService(param).invoke(param);
    }
    public AiShopDetailsQueryResult detailsQuery(AiShopDetailsQuery param) throws CommonAdapterException, AdapterUnavailableException {
        return (AiShopDetailsQueryResult) actionServiceFactory.getActionService(param).invoke(param);
    }
    public AiShopLicenseCreateResult licenseCreate(AiShopLicenseCreate param) throws CommonAdapterException, AdapterUnavailableException {
        return (AiShopLicenseCreateResult) actionServiceFactory.getActionService(param).invoke(param);
    }
    public BaseResult lifecycleUpdate(AiShopLifecycleUpdate param) throws CommonAdapterException, AdapterUnavailableException {
        return (BaseResult) actionServiceFactory.getActionService(param).invoke(param);
    }
    public AiShopMineQueryAllResult mineQueryAll(AiShopMineQueryAll param) throws CommonAdapterException, AdapterUnavailableException {
        return (AiShopMineQueryAllResult) actionServiceFactory.getActionService(param).invoke(param);
    }
    public BaseResult process(AiShopProcess param) throws CommonAdapterException, AdapterUnavailableException {
        return (BaseResult) actionServiceFactory.getActionService(param).invoke(param);
    }
    public AiShopQueryAllResult queryAll(AiShopQueryAll param) throws CommonAdapterException, AdapterUnavailableException {
        return (AiShopQueryAllResult) actionServiceFactory.getActionService(param).invoke(param);
    }
    public BaseResult renew(AiShopRenew param) throws CommonAdapterException, AdapterUnavailableException {
        return (BaseResult) actionServiceFactory.getActionService(param).invoke(param);
    }
    public AiShopSubscribesQueryResult subscribesQuery(AiShopSubscribesQuery param) throws CommonAdapterException, AdapterUnavailableException {
        return (AiShopSubscribesQueryResult) actionServiceFactory.getActionService(param).invoke(param);
    }
    public BaseResult subscriptionsCreate(AiShopSubscriptionsCreate param) throws CommonAdapterException, AdapterUnavailableException {
        return (BaseResult) actionServiceFactory.getActionService(param).invoke(param);
    }
    public BaseResult subscriptionsDelete(AiShopSubscriptionsDelete param) throws CommonAdapterException, AdapterUnavailableException {
        return (BaseResult) actionServiceFactory.getActionService(param).invoke(param);
    }
    public BaseResult subscriptionsRenew(AiShopSubscriptionsRenew param) throws CommonAdapterException, AdapterUnavailableException {
        return (BaseResult) actionServiceFactory.getActionService(param).invoke(param);
    }
    public BaseResult update(AiShopUpdate param) throws CommonAdapterException, AdapterUnavailableException {
        return (BaseResult) actionServiceFactory.getActionService(param).invoke(param);
    }
    public BaseResult update(AiShopUpdateTo param) throws CommonAdapterException, AdapterUnavailableException {
        return (BaseResult) actionServiceFactory.getActionService(param).invoke(param);
    }
    public AiShopVersionCreateResult versionCreate(AiShopVersionCreate param) throws CommonAdapterException, AdapterUnavailableException {
        return (AiShopVersionCreateResult) actionServiceFactory.getActionService(param).invoke(param);
    }
    public AiShopVersionCreateResultTo versionCreateTo(AiShopVersionCreateTo param) throws CommonAdapterException, AdapterUnavailableException {
        return (AiShopVersionCreateResultTo) actionServiceFactory.getActionService(param).invoke(param);
    }
    public BaseResult versionDelete(AiShopVersionDelete param) throws CommonAdapterException, AdapterUnavailableException {
        return (BaseResult) actionServiceFactory.getActionService(param).invoke(param);
    }
    public AiShopVersionsQueryResult versionsQuery(AiShopVersionsQuery param) throws CommonAdapterException, AdapterUnavailableException {
        return (AiShopVersionsQueryResult) actionServiceFactory.getActionService(param).invoke(param);
    }
    public AiShopVersionUpdateResult versionUpdate(AiShopVersionUpdate param) throws CommonAdapterException, AdapterUnavailableException {
        return (AiShopVersionUpdateResult) actionServiceFactory.getActionService(param).invoke(param);
    }

    public AiShopVersionUpdateResultTo versionUpdateTo(AiShopVersionUpdateTo param) throws CommonAdapterException, AdapterUnavailableException {
        return (AiShopVersionUpdateResultTo) actionServiceFactory.getActionService(param).invoke(param);
    }

    public AiAlgorithmsQueryResult algorithmsQuery(AiAlgorithmsQuery param) throws CommonAdapterException, AdapterUnavailableException {
        return (AiAlgorithmsQueryResult) actionServiceFactory.getActionService(param).invoke(param);
    }
    public AiModelsQueryResult modelsQuery(AiModelsQuery param) throws CommonAdapterException, AdapterUnavailableException {
        return (AiModelsQueryResult) actionServiceFactory.getActionService(param).invoke(param);
    }

}
