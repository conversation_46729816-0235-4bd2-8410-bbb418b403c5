/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.adapter.facade.handler;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.com.cloudstar.rightcloud.adapter.pojo.dme.DMECreateQuotaRequest;
import cn.com.cloudstar.rightcloud.adapter.pojo.dme.DMEDeleteQuotaRequest;
import cn.com.cloudstar.rightcloud.adapter.pojo.dme.DMEQuotaListQueryRequest;
import cn.com.cloudstar.rightcloud.adapter.pojo.dme.DMEQuotaUpdateRequest;
import cn.com.cloudstar.rightcloud.adapter.pojo.dme.DMETaskInfoQueryRequest;
import cn.com.cloudstar.rightcloud.adapter.pojo.dme.result.DMECreateQuotaResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.dme.result.DMEDeleteQuotaResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.dme.result.DMEQuotaListQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.dme.result.DMEQuotaUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.dme.result.DMETaskInfoQueryResult;
import cn.com.cloudstar.rightcloud.driver.core.ActionServiceFactory;
import cn.com.cloudstar.rightcloud.driver.core.exception.AdapterUnavailableException;
import cn.com.cloudstar.rightcloud.driver.core.exception.CommonAdapterException;

/**
 * The type Bucket handler.
 */
@Service
public class DmeHandler {

    @Autowired
    private ActionServiceFactory actionServiceFactory;

    public DMETaskInfoQueryResult taskInfoQuery(DMETaskInfoQueryRequest param) throws CommonAdapterException, AdapterUnavailableException {
        return (DMETaskInfoQueryResult) actionServiceFactory.getActionService(param).invoke(param);
    }

    public DMECreateQuotaResult createQuota(DMECreateQuotaRequest param) throws CommonAdapterException, AdapterUnavailableException {
        return (DMECreateQuotaResult) actionServiceFactory.getActionService(param).invoke(param);
    }

    public DMEDeleteQuotaResult deleteQuota(DMEDeleteQuotaRequest param) throws CommonAdapterException, AdapterUnavailableException {
        return (DMEDeleteQuotaResult) actionServiceFactory.getActionService(param).invoke(param);
    }
    public DMEQuotaListQueryResult quotaListQuery(DMEQuotaListQueryRequest param) throws CommonAdapterException, AdapterUnavailableException {
        return (DMEQuotaListQueryResult) actionServiceFactory.getActionService(param).invoke(param);
    }
    public DMEQuotaUpdateResult quotaUpdate(DMEQuotaUpdateRequest param) throws CommonAdapterException, AdapterUnavailableException {
        return (DMEQuotaUpdateResult) actionServiceFactory.getActionService(param).invoke(param);
    }

}
