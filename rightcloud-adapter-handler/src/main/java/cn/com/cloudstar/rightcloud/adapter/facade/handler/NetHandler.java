/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.adapter.facade.handler;

import cn.com.cloudstar.rightcloud.adapter.pojo.network.*;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.PortAssignPrivateIpResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.PortUnAssignPrivateIpResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.com.cloudstar.rightcloud.adapter.pojo.admin.SgCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.SgDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.SgUpdate;
import cn.com.cloudstar.rightcloud.driver.pojo.base.BaseResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.FirewallCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.FirewallDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.FirewallUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.FwRuleCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.FwRuleUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.FwStrategyCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.FwStrategyDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.FwStrategyRuleAdd;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.FwStrategyRuleMove;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.FwStrategyRuleRemove;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.FwStrategyUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.result.FirewallCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.result.FirewallDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.result.FirewallUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.result.FwRuleCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.result.FwRuleUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.result.FwStrategyCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.result.FwStrategyDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.result.FwStrategyRuleAddResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.result.FwStrategyRuleMoveResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.result.FwStrategyRuleRemoveResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.firewall.result.FwStrategyUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.BackendServerAdd;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.BackendServerAddResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.BackendServerRemove;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.BackendServerRemoveResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.BackendServerSet;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.BackendServerSetResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.DecribeCertificates;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.DeleteLb;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.DeleteLbResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.DescribeZone;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.DescribeZoneResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.LbCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.LbListenerCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.LbListnerCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.LbRuleCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.LbRuleCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.LbRuleDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.LbRuleDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.LbRuleUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.LbRuleUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.ListenerDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.ListenerDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.LoadBalanceCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.LoadBalanceName;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.LoadBalanceNameResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.LoadBalanceStatus;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.LoadBalanceStatusResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.MasterSlaveServerGroupCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.MasterSlaveServerGroupCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.MasterSlaveServerGroupDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.MasterSlaveServerGroupDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.ServerCertificatesResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.StartOrStopListener;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.StartOrStopListenerResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.VServerGroupAttributeSet;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.VServerGroupAttributeSetResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.VServerGroupBackendServersAdd;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.VServerGroupBackendServersAddResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.VServerGroupBackendServersAdds;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.VServerGroupBackendServersAddsResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.VServerGroupBackendServersRemove;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.VServerGroupBackendServersRemoveResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.VServerGroupCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.VServerGroupCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.VServerGroupDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.VServerGroupDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.VServerGroupUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.PortGroupVmsByEnv;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ScanVmsByEnvResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.SecurityGroupConfig;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.SecurityGroupConfigResult;
import cn.com.cloudstar.rightcloud.driver.core.ActionServiceFactory;
import cn.com.cloudstar.rightcloud.driver.core.exception.AdapterUnavailableException;
import cn.com.cloudstar.rightcloud.driver.core.exception.CommonAdapterException;

/**
 * The type Net handler.
 */
@Service
public class NetHandler {

    @Autowired
    private ActionServiceFactory actionServiceFactory;


    /**
     * Create net net create result.
     *
     * @param netCreate the net create
     *
     * @return the net create result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public NetCreateResult createNet(NetCreate netCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (NetCreateResult) actionServiceFactory.getActionService(netCreate).invoke(netCreate);
    }


    /**
     * Delete net net delete result.
     *
     * @param netDelete the net delete
     *
     * @return the net delete result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public NetDeleteResult deleteNet(NetDelete netDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (NetDeleteResult) actionServiceFactory.getActionService(netDelete).invoke(netDelete);
    }


    /**
     * Delete security group sg delete result.
     *
     * @param sgDelete the sg delete
     *
     * @return the sg delete result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public SgDeleteResult deleteSecurityGroup(SgDelete sgDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (SgDeleteResult) actionServiceFactory.getActionService(sgDelete).invoke(sgDelete);
    }


    /**
     * Create security group sg create result.
     *
     * @param sgCreate the sg create
     *
     * @return the sg create result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public SgCreateResult createSecurityGroup(SgCreate sgCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (SgCreateResult) actionServiceFactory.getActionService(sgCreate).invoke(sgCreate);
    }


    /**
     * Update security group sg update result.
     *
     * @param sgUpdate the sg update
     *
     * @return the sg update result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public SgUpdateResult updateSecurityGroup(SgUpdate sgUpdate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (SgUpdateResult) actionServiceFactory.getActionService(sgUpdate).invoke(sgUpdate);
    }


    /**
     * Create floating ip floating ip create result.
     *
     * @param floatingIpCreate the floating ip create
     *
     * @return the floating ip create result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public FloatingIpCreateResult createFloatingIp(FloatingIpCreate floatingIpCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (FloatingIpCreateResult) actionServiceFactory.getActionService(floatingIpCreate)
                                                            .invoke(floatingIpCreate);
    }


    /**
     * Bandwidth revise bandwidth revise result.
     *
     * @param bandwidthRevise the bandwidth revise
     *
     * @return the bandwidth revise result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public BandwidthReviseResult bandwidthRevise(BandwidthRevise bandwidthRevise)
            throws CommonAdapterException, AdapterUnavailableException {
        return (BandwidthReviseResult) actionServiceFactory.getActionService(bandwidthRevise).invoke(bandwidthRevise);
    }


    /**
     * Delete floating ip floating ip delete result.
     *
     * @param floatingIpDelete the floating ip delete
     *
     * @return the floating ip delete result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public FloatingIpDeleteResult deleteFloatingIp(FloatingIpDelete floatingIpDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (FloatingIpDeleteResult) actionServiceFactory.getActionService(floatingIpDelete)
                                                            .invoke(floatingIpDelete);
    }


    /**
     * Attach floating ip floating ip attach result.
     *
     * @param floatingIpAttach the floating ip attach
     *
     * @return the floating ip attach result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public FloatingIpAttachResult attachFloatingIp(FloatingIpAttach floatingIpAttach)
            throws CommonAdapterException, AdapterUnavailableException {
        return (FloatingIpAttachResult) actionServiceFactory.getActionService(floatingIpAttach)
                                                            .invoke(floatingIpAttach);
    }


    /**
     * Detach floating ip floating ip detach result.
     *
     * @param floatingIpDetach the floating ip detach
     *
     * @return the floating ip detach result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public FloatingIpDetachResult detachFloatingIp(FloatingIpDetach floatingIpDetach)
            throws CommonAdapterException, AdapterUnavailableException {
        return (FloatingIpDetachResult) actionServiceFactory.getActionService(floatingIpDetach)
                                                            .invoke(floatingIpDetach);
    }



    /**
     * Attach security group server security group add result.
     *
     * @param securityGroupAdd the security group add
     *
     * @return the server security group add result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public ServerSecurityGroupAddResult attachSecurityGroup(ServerSecurityGroupAdd securityGroupAdd)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ServerSecurityGroupAddResult) actionServiceFactory.getActionService(securityGroupAdd)
                                                                  .invoke(securityGroupAdd);
    }


    /**
     * Detach security group server security group delete result.
     *
     * @param securityGroupDelete the security group delete
     *
     * @return the server security group delete result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public ServerSecurityGroupDeleteResult detachSecurityGroup(ServerSecurityGroupDelete securityGroupDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ServerSecurityGroupDeleteResult) actionServiceFactory.getActionService(securityGroupDelete)
                                                                     .invoke(securityGroupDelete);
    }


    /**
     * Create sg rule sg rule create result.
     *
     * @param sgRuleCreate the sg rule create
     *
     * @return the sg rule create result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public SgRuleCreateResult createSgRule(SgRuleCreate sgRuleCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (SgRuleCreateResult) actionServiceFactory.getActionService(sgRuleCreate).invoke(sgRuleCreate);
    }


    /**
     * Update sg rule base result.
     *
     * @param sgRuleUpdate the sg rule update
     *
     * @return the base result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public BaseResult updateSgRule(SgRuleUpdate sgRuleUpdate)
            throws CommonAdapterException, AdapterUnavailableException {
        return actionServiceFactory.getActionService(sgRuleUpdate).invoke(sgRuleUpdate);
    }


    /**
     * Delete sg rule sg rule delete result.
     *
     * @param sgRuleDelete the sg rule delete
     *
     * @return the sg rule delete result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public SgRuleDeleteResult deleteSgRule(SgRuleDelete sgRuleDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (SgRuleDeleteResult) actionServiceFactory.getActionService(sgRuleDelete).invoke(sgRuleDelete);
    }


    /**
     * Query sg rule list sg rule list query result.
     *
     * @param sgRuleListQuery the sg rule list query
     *
     * @return the sg rule list query result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public SgRuleListQueryResult querySgRuleList(SgRuleListQuery sgRuleListQuery)
            throws CommonAdapterException, AdapterUnavailableException {
        return (SgRuleListQueryResult) actionServiceFactory.getActionService(sgRuleListQuery).invoke(sgRuleListQuery);
    }


    /**
     * Create router router result.
     *
     * @param routerCreate the router create
     *
     * @return the router result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public RouterResult createRouter(RouterCreate routerCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (RouterResult) actionServiceFactory.getActionService(routerCreate).invoke(routerCreate);
    }


    /**
     * Delete router router result.
     *
     * @param routerDelete the router delete
     *
     * @return the router result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public RouterResult deleteRouter(RouterDelete routerDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (RouterResult) actionServiceFactory.getActionService(routerDelete).invoke(routerDelete);
    }


    /**
     * Update router router result.
     *
     * @param routerUpdate the router update
     *
     * @return the router result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public RouterResult updateRouter(RouterUpdate routerUpdate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (RouterResult) actionServiceFactory.getActionService(routerUpdate).invoke(routerUpdate);
    }


    /**
     * Add router entry router add entry result.
     *
     * @param routerAddEntry the router add entry
     *
     * @return the router add entry result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public RouterAddEntryResult addRouterEntry(RouterAddEntry routerAddEntry)
            throws CommonAdapterException, AdapterUnavailableException {
        return (RouterAddEntryResult) actionServiceFactory.getActionService(routerAddEntry).invoke(routerAddEntry);
    }


    /**
     * Add router interface router result.
     *
     * @param routerAddInterface the router add interface
     *
     * @return the router result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public RouterResult addRouterInterface(RouterAddInterface routerAddInterface)
            throws CommonAdapterException, AdapterUnavailableException {
        return (RouterResult) actionServiceFactory.getActionService(routerAddInterface).invoke(routerAddInterface);
    }


    /**
     * Add router external gateway router result.
     *
     * @param routerAddExternalGateway the router add external gateway
     *
     * @return the router result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public RouterResult addRouterExternalGateway(RouterAddExternalGateway routerAddExternalGateway)
            throws CommonAdapterException, AdapterUnavailableException {
        return (RouterResult) actionServiceFactory.getActionService(routerAddExternalGateway)
                                                  .invoke(routerAddExternalGateway);
    }


    /**
     * Remove router interface router result.
     *
     * @param routerRemoveInterface the router remove interface
     *
     * @return the router result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public RouterResult removeRouterInterface(RouterRemoveInterface routerRemoveInterface)
            throws CommonAdapterException, AdapterUnavailableException {
        return (RouterResult) actionServiceFactory.getActionService(routerRemoveInterface)
                                                  .invoke(routerRemoveInterface);
    }


    /**
     * Remove external gateway router result.
     *
     * @param routerRemoveExternalGateway the router remove external gateway
     *
     * @return the router result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public RouterResult removeExternalGateway(RouterRemoveExternalGateway routerRemoveExternalGateway)
            throws CommonAdapterException, AdapterUnavailableException {
        return (RouterResult) actionServiceFactory.getActionService(routerRemoveExternalGateway)
                                                  .invoke(routerRemoveExternalGateway);
    }


    /**
     * Remove router entry router result.
     *
     * @param routerDeleteEntry the router delete entry
     *
     * @return the router result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public RouterResult removeRouterEntry(RouterDeleteEntry routerDeleteEntry)
            throws CommonAdapterException, AdapterUnavailableException {
        return (RouterResult) actionServiceFactory.getActionService(routerDeleteEntry).invoke(routerDeleteEntry);
    }


    /**
     * Sets load balance status.
     *
     * @param loadBalanceStatus the load balance status
     *
     * @return the load balance status
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public LoadBalanceStatusResult setLoadBalanceStatus(LoadBalanceStatus loadBalanceStatus)
            throws CommonAdapterException, AdapterUnavailableException {
        return (LoadBalanceStatusResult) actionServiceFactory.getActionService(loadBalanceStatus)
                                                             .invoke(loadBalanceStatus);
    }


    /**
     * Delete lb delete lb result.
     *
     * @param deleteLb the delete lb
     *
     * @return the delete lb result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public DeleteLbResult deleteLb(DeleteLb deleteLb) throws CommonAdapterException, AdapterUnavailableException {
        return (DeleteLbResult) actionServiceFactory.getActionService(deleteLb).invoke(deleteLb);
    }


    /**
     * Describe zones describe zone result.
     *
     * @param describeZone the describe zone
     *
     * @return the describe zone result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public DescribeZoneResult describeZones(DescribeZone describeZone)
            throws CommonAdapterException, AdapterUnavailableException {
        return (DescribeZoneResult) actionServiceFactory.getActionService(describeZone).invoke(describeZone);
    }


    /**
     * Create lb load balance create result.
     *
     * @param lbCreate the lb create
     *
     * @return the load balance create result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public LoadBalanceCreateResult createLb(LbCreate lbCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (LoadBalanceCreateResult) actionServiceFactory.getActionService(lbCreate).invoke(lbCreate);
    }

    /**
     * Create lb load balance rule create result.
     *
     * @param lbRuleCreate the lb create
     *
     * @return the load balance create result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public LbRuleCreateResult createLbRule(LbRuleCreate lbRuleCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (LbRuleCreateResult) actionServiceFactory.getActionService(lbRuleCreate).invoke(lbRuleCreate);
    }

    /**
     * Create lb load balance rule delete result.
     *
     * @param lbRuleDelete the lb delete
     *
     * @return the load balance delete result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public LbRuleDeleteResult deleteLbRule(LbRuleDelete lbRuleDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (LbRuleDeleteResult) actionServiceFactory.getActionService(lbRuleDelete).invoke(lbRuleDelete);
    }

    /**
     * Create lb load balance rule update result.
     *
     * @param lbRuleUpdate the lb update
     *
     * @return the load balance update result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public LbRuleUpdateResult updateLbRule(LbRuleUpdate lbRuleUpdate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (LbRuleUpdateResult) actionServiceFactory.getActionService(lbRuleUpdate).invoke(lbRuleUpdate);
    }

    /**
     * Create lb listner lb listner create result.
     *
     * @param lbListenerCreate the lb listener create
     *
     * @return the lb listner create result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public LbListnerCreateResult createLbListner(LbListenerCreate lbListenerCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (LbListnerCreateResult) actionServiceFactory.getActionService(lbListenerCreate).invoke(lbListenerCreate);
    }

    /**
     * Start or stop listener start or stop listener result.
     *
     * @param startOrStopListener the start or stop listener
     *
     * @return the start or stop listener result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public StartOrStopListenerResult startOrStopListener(StartOrStopListener startOrStopListener)
            throws CommonAdapterException, AdapterUnavailableException {
        return (StartOrStopListenerResult) actionServiceFactory.getActionService(startOrStopListener)
                                                               .invoke(startOrStopListener);
    }


    /**
     * Delete listener listener delete result.
     *
     * @param listenerDelete the listener delete
     *
     * @return the listener delete result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public ListenerDeleteResult deleteListener(ListenerDelete listenerDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ListenerDeleteResult) actionServiceFactory.getActionService(listenerDelete).invoke(listenerDelete);
    }


    /**
     * Add backend server backend server add result.
     *
     * @param backendServerAdd the backend server add
     *
     * @return the backend server add result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public BackendServerAddResult addBackendServer(BackendServerAdd backendServerAdd)
            throws CommonAdapterException, AdapterUnavailableException {
        return (BackendServerAddResult) actionServiceFactory.getActionService(backendServerAdd)
                                                            .invoke(backendServerAdd);
    }


    /**
     * Sets backend server.
     *
     * @param backendServerSet the backend server set
     *
     * @return the backend server
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public BackendServerSetResult setBackendServer(BackendServerSet backendServerSet)
            throws CommonAdapterException, AdapterUnavailableException {
        return (BackendServerSetResult) actionServiceFactory.getActionService(backendServerSet)
                                                            .invoke(backendServerSet);
    }


    /**
     * Remove backend server backend server remove result.
     *
     * @param backendServerRemove the backend server remove
     *
     * @return the backend server remove result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public BackendServerRemoveResult removeBackendServer(BackendServerRemove backendServerRemove)
            throws CommonAdapterException, AdapterUnavailableException {
        return (BackendServerRemoveResult) actionServiceFactory.getActionService(backendServerRemove)
                                                               .invoke(backendServerRemove);
    }


    /**
     * Create master slave server group master slave server group create result.
     *
     * @param masterSlaveServerGroupCreate the master slave server group create
     *
     * @return the master slave server group create result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public MasterSlaveServerGroupCreateResult createMasterSlaveServerGroup(
            MasterSlaveServerGroupCreate masterSlaveServerGroupCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (MasterSlaveServerGroupCreateResult) actionServiceFactory.getActionService(masterSlaveServerGroupCreate)
                                                                        .invoke(masterSlaveServerGroupCreate);
    }


    /**
     * Delete master slave server group master slave server group delete result.
     *
     * @param masterSlaveServerGroupDelete the master slave server group delete
     *
     * @return the master slave server group delete result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public MasterSlaveServerGroupDeleteResult deleteMasterSlaveServerGroup(
            MasterSlaveServerGroupDelete masterSlaveServerGroupDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (MasterSlaveServerGroupDeleteResult) actionServiceFactory.getActionService(masterSlaveServerGroupDelete)
                                                                        .invoke(masterSlaveServerGroupDelete);
    }


    /**
         * Create v server group v server group create result.
         *
         * @param vServerGroupCreate the v server group create
         *
         * @return the v server group create result
         *
         * @throws CommonAdapterException the common adapter exception
         * @throws AdapterUnavailableException the adapter unavailable exception
         */
        public VServerGroupCreateResult createVServerGroup(VServerGroupCreate vServerGroupCreate)
            throws CommonAdapterException, AdapterUnavailableException {
            return (VServerGroupCreateResult) actionServiceFactory.getActionService(vServerGroupCreate)
                .invoke(vServerGroupCreate);
    }

    /**
     * update v server group v server group update result.
     *
     * @param vServerGroupCreate the v server group update
     *
     * @return the v server group update result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public VServerGroupCreateResult updateVServerGroup(VServerGroupUpdate vServerGroupUpdate)
        throws CommonAdapterException, AdapterUnavailableException {
        return (VServerGroupCreateResult) actionServiceFactory.getActionService(vServerGroupUpdate)
            .invoke(vServerGroupUpdate);
    }

    /**
     * Delete v server group v server group delete result.
     *
     * @param vServerGroupDelete the v server group delete
     *
     * @return the v server group delete result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public VServerGroupDeleteResult deleteVServerGroup(VServerGroupDelete vServerGroupDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VServerGroupDeleteResult) actionServiceFactory.getActionService(vServerGroupDelete)
                                                              .invoke(vServerGroupDelete);
    }


    /**
     * Describe certificate server certificates result.
     *
     * @param decribeCertificates the decribe certificates
     *
     * @return the server certificates result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public ServerCertificatesResult describeCertificate(DecribeCertificates decribeCertificates)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ServerCertificatesResult) actionServiceFactory.getActionService(decribeCertificates)
                                                              .invoke(decribeCertificates);
    }


    /**
     * Add v server group backend servers v server group backend servers add result.
     *
     * @param vServerGroupBackendServersAdd the v server group backend servers add
     *
     * @return the v server group backend servers add result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public VServerGroupBackendServersAddResult addVServerGroupBackendServers(
            VServerGroupBackendServersAdd vServerGroupBackendServersAdd)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VServerGroupBackendServersAddResult) actionServiceFactory.getActionService(
                vServerGroupBackendServersAdd).invoke(vServerGroupBackendServersAdd);
    }


    /**
     * 批量添加主机到资源池
     * @param vServerGroupBackendServersAdds
     * @return
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public VServerGroupBackendServersAddsResult addVServerGrouplbServers(
            VServerGroupBackendServersAdds vServerGroupBackendServersAdds)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VServerGroupBackendServersAddsResult) actionServiceFactory.getActionService(
                vServerGroupBackendServersAdds).invoke(vServerGroupBackendServersAdds);
    }


    /**
     * Remove v server group backend servers v server group backend servers remove result.
     *
     * @param vServerGroupBackendServersRemove the v server group backend servers remove
     *
     * @return the v server group backend servers remove result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public VServerGroupBackendServersRemoveResult removeVServerGroupBackendServers(
            VServerGroupBackendServersRemove vServerGroupBackendServersRemove)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VServerGroupBackendServersRemoveResult) actionServiceFactory.getActionService(
                vServerGroupBackendServersRemove).invoke(vServerGroupBackendServersRemove);
    }


    /**
     * Sets v server group attribute.
     *
     * @param vServerGroupAttributeSet the v server group attribute set
     *
     * @return the v server group attribute
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public VServerGroupAttributeSetResult setVServerGroupAttribute(VServerGroupAttributeSet vServerGroupAttributeSet)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VServerGroupAttributeSetResult) actionServiceFactory.getActionService(vServerGroupAttributeSet)
                                                                    .invoke(vServerGroupAttributeSet);
    }


    /**
     * update router entry router result.
     *
     * @param routerUpdateEntry the router update entry
     *
     * @return the router result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public RouterResult updateRouterEntry(RouterUpdateEntry routerUpdateEntry)
            throws CommonAdapterException, AdapterUnavailableException {
        return (RouterResult) actionServiceFactory.getActionService(routerUpdateEntry).invoke(routerUpdateEntry);
    }


    public FloatingIpUpdateResult updateFloatingIp(FloatingIpUpdate floatingIpUpdate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (FloatingIpUpdateResult) actionServiceFactory.getActionService(floatingIpUpdate)
                .invoke(floatingIpUpdate);
    }

    /**
     * @description
     * @date 2019/5/13
     * @param  portDelete
     * @return the delete result
     */
    public PortDeleteResult deletePort(PortDelete portDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (PortDeleteResult) actionServiceFactory.getActionService(portDelete).invoke(portDelete);

    }
    public FwStrategyRuleAddResult addScRule(FwStrategyRuleAdd fwStrategyRuleAdd)
            throws CommonAdapterException, AdapterUnavailableException {
        return (FwStrategyRuleAddResult) actionServiceFactory.getActionService(fwStrategyRuleAdd)
                                                             .invoke(fwStrategyRuleAdd);
    }

    public FwStrategyRuleRemoveResult removeScRule(FwStrategyRuleRemove fwStrategyRuleRemove)
            throws CommonAdapterException, AdapterUnavailableException {
        return (FwStrategyRuleRemoveResult) actionServiceFactory.getActionService(fwStrategyRuleRemove)
                                                                .invoke(fwStrategyRuleRemove);
    }

    public FwStrategyDeleteResult deleteScStrategy(FwStrategyDelete fwStrategyDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (FwStrategyDeleteResult) actionServiceFactory.getActionService(fwStrategyDelete)
                                                            .invoke(fwStrategyDelete);
    }

    public FirewallDeleteResult deleteScFirewall(FirewallDelete firewallDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (FirewallDeleteResult) actionServiceFactory.getActionService(firewallDelete).invoke(firewallDelete);
    }

    public FwRuleUpdateResult updateScFirewallRule(FwRuleUpdate ruleUpdate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (FwRuleUpdateResult) actionServiceFactory.getActionService(ruleUpdate).invoke(ruleUpdate);
    }

    public FwStrategyUpdateResult updateScFirewallStrategy(FwStrategyUpdate strategyUpdate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (FwStrategyUpdateResult) actionServiceFactory.getActionService(strategyUpdate).invoke(strategyUpdate);
    }

    public FirewallUpdateResult updateScFirewall(FirewallUpdate firewallUpdate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (FirewallUpdateResult) actionServiceFactory.getActionService(firewallUpdate).invoke(firewallUpdate);
    }

    /**
     * 配置实例安全组
     *
     * @param securityGroupConfig
     * @return
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public SecurityGroupConfigResult configVmSg(SecurityGroupConfig securityGroupConfig)
            throws CommonAdapterException, AdapterUnavailableException {

        return (SecurityGroupConfigResult) actionServiceFactory.getActionService(securityGroupConfig).invoke(securityGroupConfig);

    }

    /**
     * @param portCreate
     *
     * @return the create result
     *
     * @description
     * @date 2019/6/6
     */
    public PortCreateResult createPort(PortCreate portCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (PortCreateResult) actionServiceFactory.getActionService(portCreate).invoke(portCreate);

    }

    /**
     * @param portUpdate
     *
     * @return the create result
     *
     * @description
     * @date 2019/6/6
     */
    public PortUpdateResult updatePort(PortUpdate portUpdate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (PortUpdateResult) actionServiceFactory.getActionService(portUpdate).invoke(portUpdate);

    }

    /**
     * @param portAttach
     *
     * @return the attach result
     *
     * @description
     * @date 2019/6/6
     */
    public PortAttachInstanceResult attachPortToInstance(PortAttachInstance portAttach)
            throws CommonAdapterException, AdapterUnavailableException {
        return (PortAttachInstanceResult) actionServiceFactory.getActionService(portAttach).invoke(portAttach);

    }

    /**
     * @param portDetach
     *
     * @return the detach result
     *
     * @description
     * @date 2019/6/6
     */
    public PortDetachInstanceResult detachPortToInstance(PortDetachInstance portDetach)
            throws CommonAdapterException, AdapterUnavailableException {
        return (PortDetachInstanceResult) actionServiceFactory.getActionService(portDetach).invoke(portDetach);

    }

    /**
     * @param avilableNetwork
     *
     * @return the detach result
     *
     * @description
     * @date 2019/6/13
     */
    public AvilableNetworkResult getAvilableNetwork(AvilableNetwork avilableNetwork)
            throws CommonAdapterException, AdapterUnavailableException {
        return (AvilableNetworkResult) actionServiceFactory.getActionService(avilableNetwork).invoke(avilableNetwork);
    }

    public LoadBalanceNameResult setLoadBalanceName(LoadBalanceName loadBalanceName)
            throws CommonAdapterException, AdapterUnavailableException {
        return (LoadBalanceNameResult) actionServiceFactory.getActionService(loadBalanceName).invoke(loadBalanceName);
    }

    /**
     * create portgroup
     * @param portGroupCreate
     * @return
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public PortGroupCreateResult createPortGroup(PortGroupCreate portGroupCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (PortGroupCreateResult) actionServiceFactory.getActionService(portGroupCreate).invoke(portGroupCreate);
    }

    /**
     * update portgroup
     * @param portGroupUpdate
     * @return
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public PortGroupUpdateResult updatePortGroup(PortGroupUpdate portGroupUpdate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (PortGroupUpdateResult) actionServiceFactory.getActionService(portGroupUpdate).invoke(portGroupUpdate);
    }

    /**
     * portgroup vms
     * @param portGroupVmsByEnv
     * @return
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public ScanVmsByEnvResult getPortGroupVms(PortGroupVmsByEnv portGroupVmsByEnv)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ScanVmsByEnvResult) actionServiceFactory.getActionService(portGroupVmsByEnv).invoke(portGroupVmsByEnv);
    }

    /**
     * delete portgroup
     * @param portGroupDelete
     * @return
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public PortGroupDeleteResult deletePortGroup(PortGroupDelete portGroupDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (PortGroupDeleteResult) actionServiceFactory.getActionService(portGroupDelete).invoke(portGroupDelete);
    }

    /**
     * create vlanPool
     * @param vlanPoolCreate
     * @return
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public VlanPoolCreateResult createVlanPool(VlanPoolCreate vlanPoolCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VlanPoolCreateResult) actionServiceFactory.getActionService(vlanPoolCreate).invoke(vlanPoolCreate);
    }

    /**
     * delete vlanpool
     * @param vlanPoolDelete
     * @return
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public VlanPoolDeleteResult deleteVlanPool(VlanPoolDelete vlanPoolDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VlanPoolDeleteResult) actionServiceFactory.getActionService(vlanPoolDelete).invoke(vlanPoolDelete);
    }

    /**
     * Create service chain create result.
     *
     * @param serviceChainCreate the service chain create
     *
     * @return the service chain create result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public ServiceChainCreateResult createServiceChain(ServiceChainCreate serviceChainCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ServiceChainCreateResult) actionServiceFactory.getActionService(serviceChainCreate).invoke(serviceChainCreate);
    }

    /**
     * Create service chain context create result.
     *
     * @param serviceChainContextCreate the service chain context create
     *
     * @return the service chain context create result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public ServiceChainContextCreateResult createServiceChainContext(ServiceChainContextCreate serviceChainContextCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ServiceChainContextCreateResult) actionServiceFactory.getActionService(serviceChainContextCreate).invoke(serviceChainContextCreate);
    }

    public FwRuleCreateResult createRule(FwRuleCreate fwruleCreate)
            throws CommonAdapterException, AdapterUnavailableException{
        return (FwRuleCreateResult) actionServiceFactory.getActionService(fwruleCreate).invoke(fwruleCreate);
    }

    public FwStrategyCreateResult createStrategy(FwStrategyCreate fwstrategyCreate)
            throws CommonAdapterException, AdapterUnavailableException{
        return (FwStrategyCreateResult) actionServiceFactory.getActionService(fwstrategyCreate).invoke(fwstrategyCreate);
    }

    public FirewallCreateResult createFirewall(FirewallCreate firewallCreate)
            throws CommonAdapterException, AdapterUnavailableException{
        return (FirewallCreateResult) actionServiceFactory.getActionService(firewallCreate).invoke(firewallCreate);
    }

    /**
     * Delete service chain delete result.
     *
     * @param serviceChainDelete the service chain delete
     *
     * @return the service chain delete result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public ServiceChainDeleteResult deleteServiceChain(ServiceChainDelete serviceChainDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ServiceChainDeleteResult) actionServiceFactory.getActionService(serviceChainDelete).invoke(serviceChainDelete);
    }

    /**
     * Delete service chain context delete result.
     *
     * @param serviceChainContextDelete the service chain context delete
     *
     * @return the service chain context delete result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public ServiceChainContextDeleteResult deleteServiceChainContext(ServiceChainContextDelete serviceChainContextDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ServiceChainContextDeleteResult) actionServiceFactory.getActionService(serviceChainContextDelete).invoke(serviceChainContextDelete);
    }

    /**
     * @description
     * @date 2019/6/6
     * @param  fireWallRuleCreate
     * @return the create result
     */
    public VpcFireWallRuleResult createVpcFireWallRule(VpcFireWallRuleCreate fireWallRuleCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VpcFireWallRuleResult) actionServiceFactory.getActionService(fireWallRuleCreate).invoke(fireWallRuleCreate);

    }

    /**
     * @description
     * @date 2019/6/6
     * @param  fireWallRuleUpdate
     * @return the update result
     */
    public VpcFireWallRuleResult updateVpcFireWallRule(VpcFireWallRuleUpdate fireWallRuleUpdate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VpcFireWallRuleResult) actionServiceFactory.getActionService(fireWallRuleUpdate).invoke(fireWallRuleUpdate);

    }

    /**
     * @description
     * @date 2019/6/6
     * @param  fireWallRuleDelete
     * @return the delete result
     */
    public VpcFireWallRuleResult deleteVpcFireWallRule(VpcFireWallRuleDelete fireWallRuleDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VpcFireWallRuleResult) actionServiceFactory.getActionService(fireWallRuleDelete).invoke(fireWallRuleDelete);

    }

    /**
     * @description
     * @date 2019/6/6
     * @param  fireWallRuleMove
     * @return the delete result
     */
    public VpcFireWallRuleResult moveVpcFireWallRule(VpcFireWallRuleMove fireWallRuleMove)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VpcFireWallRuleResult) actionServiceFactory.getActionService(fireWallRuleMove).invoke(fireWallRuleMove);

    }

    /**
     * @description
     * @date 2019/6/6
     * @param  strategyCreate
     * @return the delete result
     */
    public VpcFireWallStrategyResult createVpcFireWallStrategy(VpcFireWallStrategyCreate strategyCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VpcFireWallStrategyResult) actionServiceFactory.getActionService(strategyCreate).invoke(strategyCreate);
    }

    /**
     * @description
     * @date 2019/6/6
     * @param  strategyUpdate
     * @return the delete result
     */
    public VpcFireWallStrategyResult updateVpcFireWallStrategy(VpcFireWallStrategyUpdate strategyUpdate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VpcFireWallStrategyResult) actionServiceFactory.getActionService(strategyUpdate).invoke(strategyUpdate);
    }

    /**
     * @description
     * @date 2019/6/6
     * @param  strategyDelete
     * @return the delete result
     */
    public VpcFireWallStrategyResult deleteVpcFireWallStrategy(VpcFireWallStrategyDelete strategyDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VpcFireWallStrategyResult) actionServiceFactory.getActionService(strategyDelete).invoke(strategyDelete);
    }


    /**
     * @description
     * @date 2019/6/6
     * @param  fireWallCreate
     * @return the delete result
     */
    public VpcFireWallResult createVpcFireWall(VpcFireWallCreate fireWallCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VpcFireWallResult) actionServiceFactory.getActionService(fireWallCreate).invoke(fireWallCreate);
    }

    /**
     * @description
     * @date 2019/6/6
     * @param  fireWallUpdate
     * @return the delete result
     */
    public VpcFireWallResult updateVpcFireWall(VpcFireWallUpdate fireWallUpdate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VpcFireWallResult) actionServiceFactory.getActionService(fireWallUpdate).invoke(fireWallUpdate);
    }

    /**
     * @description
     * @date 2019/6/6
     * @param  fireWallDelete
     * @return the delete result
     */
    public VpcFireWallResult deleteVpcFireWall(VpcFireWallDelete fireWallDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VpcFireWallResult) actionServiceFactory.getActionService(fireWallDelete).invoke(fireWallDelete);
    }

    /**
     * 创建防火墙对象组
     * @param objectGroupsCreate
     * @return
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public VpcFireWallObjectGroupsResult createVpcFireWallObjectGroups(VpcFireWallObjectGroupsCreate objectGroupsCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VpcFireWallObjectGroupsResult) actionServiceFactory.getActionService(objectGroupsCreate).invoke(objectGroupsCreate);
    }

    /**
     * 更新防火墙对象组
     * @param objectGroupsCreate
     * @return
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public VpcFireWallObjectGroupsResult updateVpcFireWallObjectGroups(VpcFireWallObjectGroupsUpdate objectGroupsCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VpcFireWallObjectGroupsResult) actionServiceFactory.getActionService(objectGroupsCreate).invoke(objectGroupsCreate);
    }

    /**
     * 删除防火墙对象组
     * @param objectGroupsCreate
     * @return
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public VpcFireWallObjectGroupsResult deleteVpcFireWallObjectGroups(VpcFireWallObjectGroupsDelete objectGroupsCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VpcFireWallObjectGroupsResult) actionServiceFactory.getActionService(objectGroupsCreate).invoke(objectGroupsCreate);
    }

    /**
     * 创建防火墙对象
     * @param objectsCreate
     * @return
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public VpcFireWallObjectsResult createVpcFireWallObjects(VpcFireWallObjectsCreate objectsCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VpcFireWallObjectsResult) actionServiceFactory.getActionService(objectsCreate).invoke(objectsCreate);
    }

    /**
     * 更新防火墙对象
     * @param objectsCreate
     * @return
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public VpcFireWallObjectsResult updateVpcFireWallObjects(VpcFireWallObjectsUpdate objectsCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VpcFireWallObjectsResult) actionServiceFactory.getActionService(objectsCreate).invoke(objectsCreate);
    }

    /**
     * 删除防火墙对象
     * @param objectsCreate
     * @return
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public VpcFireWallObjectsResult deleteVpcFireWallObjects(VpcFireWallObjectsDelete objectsCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VpcFireWallObjectsResult) actionServiceFactory.getActionService(objectsCreate).invoke(objectsCreate);
    }

    /**
     * @description
     * @date 2019/6/6
     * @param  fireWallRouterUpdate
     * @return the delete result
     */
    public VpcFireWallResult updateFireWallRouterRel(VpcFireWallRouterRelUpdate fireWallRouterUpdate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VpcFireWallResult) actionServiceFactory.getActionService(fireWallRouterUpdate).invoke(fireWallRouterUpdate);
    }

    /**
     * 创建防火墙病毒策略
     * @param fireWallAntivirusPolicyCreate
     * @return
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public VpcFireWallAntivirusPolicyResult createVpcFireWallAntivirusPolicy(VpcFireWallAntivirusPolicyCreate fireWallAntivirusPolicyCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VpcFireWallAntivirusPolicyResult) actionServiceFactory.getActionService(fireWallAntivirusPolicyCreate).invoke(fireWallAntivirusPolicyCreate);
    }

    /**
     * 删除防火墙病毒策略
     * @param fireWallAntivirusPolicyDelete
     * @return
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public VpcFireWallAntivirusPolicyResult deleteVpcFireWallAntivirusPolicy(VpcFireWallAntivirusPolicyDelete fireWallAntivirusPolicyDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VpcFireWallAntivirusPolicyResult) actionServiceFactory.getActionService(fireWallAntivirusPolicyDelete).invoke(fireWallAntivirusPolicyDelete);
    }

    /**
     * 更新防火墙病毒策略
     * @param fireWallAntivirusPolicyUpdate
     * @return
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public VpcFireWallAntivirusPolicyResult updateVpcFireWallAntivirusPolicy(VpcFireWallAntivirusPolicyUpdate fireWallAntivirusPolicyUpdate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VpcFireWallAntivirusPolicyResult) actionServiceFactory.getActionService(fireWallAntivirusPolicyUpdate).invoke(fireWallAntivirusPolicyUpdate);
    }

    /**
     * 查询防火墙病毒策略相关信息
     * @param fireWallAntivirusPolicyRelevantInfo
     * @return
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public VpcFireWallAntivirusPolicyResult relevantInfoVpcFireWallAntivirusPolicy(VpcFireWallAntivirusPolicyRelevantInfo fireWallAntivirusPolicyRelevantInfo)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VpcFireWallAntivirusPolicyResult) actionServiceFactory.getActionService(fireWallAntivirusPolicyRelevantInfo).invoke(fireWallAntivirusPolicyRelevantInfo);
    }

    /**
     * 创建防火墙IPS策略相
     * @param ipsPolicyCreate
     * @return
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public VpcFireWallIPSResult createVpcFireWallIpsPolicy(VpcFireWallIPSPolicyCreate ipsPolicyCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VpcFireWallIPSResult) actionServiceFactory.getActionService(ipsPolicyCreate).invoke(ipsPolicyCreate);
    }

    /**
     *
     * @param ipsPolicyUpdate
     * @return
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public VpcFireWallIPSResult updateVpcFireWallIpsPolicy(VpcFireWallIPSPolicyUpdate ipsPolicyUpdate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VpcFireWallIPSResult) actionServiceFactory.getActionService(ipsPolicyUpdate).invoke(ipsPolicyUpdate);
    }

    /**
     *
     * @param ipsPolicyDelete
     * @return
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public VpcFireWallIPSResult deleteVpcFireWallIpsPolicy(VpcFireWallIPSPolicyDelete ipsPolicyDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VpcFireWallIPSResult) actionServiceFactory.getActionService(ipsPolicyDelete).invoke(ipsPolicyDelete);
    }

    /**
     *
     * @param ipsSignature
     * @return
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public VpcFireWallSignatureResult getVpcFireWallIpsSignature(VpcFireWallIPSSignature ipsSignature)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VpcFireWallSignatureResult) actionServiceFactory.getActionService(ipsSignature).invoke(ipsSignature);
    }

    public FwStrategyRuleMoveResult moveScRule(FwStrategyRuleMove strategyRuleMove)
            throws CommonAdapterException, AdapterUnavailableException{
        return (FwStrategyRuleMoveResult) actionServiceFactory.getActionService(strategyRuleMove).invoke(strategyRuleMove);
    }

    public PortAssignPrivateIpResult assignPrivateIp(PortAssignPrivateIp portAssignPrivateIp)
            throws CommonAdapterException, AdapterUnavailableException {
        return (PortAssignPrivateIpResult) actionServiceFactory.getActionService(portAssignPrivateIp)
                                                               .invoke(portAssignPrivateIp);
    }

    public PortUnAssignPrivateIpResult unassignPrivateIp(PortUnAssignPrivateIp portUnAssignPrivateIp)
            throws CommonAdapterException, AdapterUnavailableException {
        return (PortUnAssignPrivateIpResult) actionServiceFactory.getActionService(portUnAssignPrivateIp)
                                                                 .invoke(portUnAssignPrivateIp);
    }

    /**
     * update fireWallPolicy
     * @param fireWallPolicyUpdate
     * @return
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public FireWallPolicyUpdateResult updateFireWallPolicy(FireWallPolicyUpdate fireWallPolicyUpdate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (FireWallPolicyUpdateResult) actionServiceFactory.getActionService(fireWallPolicyUpdate).invoke(fireWallPolicyUpdate);
    }

    /**
     * insert fireWallRule
     * @param fireWallPolicyInsertRule
     * @return
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public FireWallPolicyInsertRuleResult insertFireWallRule(FireWallPolicyInsertRule fireWallPolicyInsertRule)
            throws CommonAdapterException, AdapterUnavailableException {
        return (FireWallPolicyInsertRuleResult) actionServiceFactory.getActionService(fireWallPolicyInsertRule).invoke(fireWallPolicyInsertRule);
    }

    public VpcPeeringAcceptResult vpcPeeringAccept(VpcPeeringAccept vpcPeeringAccept)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VpcPeeringAcceptResult) actionServiceFactory.getActionService(vpcPeeringAccept).invoke(vpcPeeringAccept);
    }

    public RouteTableUpdateResult routeTableUpdate(RouteTableUpdate routeTableUpdate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (RouteTableUpdateResult) actionServiceFactory.getActionService(routeTableUpdate).invoke(routeTableUpdate);
    }

    public VpcPeeringDeleteResult vpcPeeringDelete(VpcPeeringDelete vpcPeeringDelete)
        throws CommonAdapterException, AdapterUnavailableException {
        return (VpcPeeringDeleteResult) actionServiceFactory.getActionService(vpcPeeringDelete).invoke(vpcPeeringDelete);
    }
}
