package cn.com.cloudstar.rightcloud.adapter.facade.handler;

import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.*;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.ClustersRequest;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.MAResourceFlavorsQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.result.*;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.result.agency.MAAgencyCreateAuthorizeRes;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.result.agency.MAAgencyCreateRes;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.result.agency.MAAgencyDeleteRes;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.result.agency.MAAgencyQueryAgenciesRes;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.result.agency.MAAgencyQueryListRes;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.result.agency.MAAgencyQueryQuotasRes;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.result.agency.QueryAgencyInfoResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.result.clusters.ClustersResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.result.notebooks.NotebooksResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.result.poolsv2.OsPoolsV2Result;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.result.services.ServicesResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.result.summary.SummaryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.result.traingjobv2.StatisticsNotebooksResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.modelarts.result.traingjobv2.TrainingJobsV2Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.com.cloudstar.rightcloud.driver.core.ActionServiceFactory;
import cn.com.cloudstar.rightcloud.driver.core.exception.AdapterUnavailableException;
import cn.com.cloudstar.rightcloud.driver.core.exception.CommonAdapterException;
import cn.com.cloudstar.rightcloud.driver.pojo.base.BaseResult;

/**
 * The type Bucket handler.
 */
@Service
public class ModelArtsHandler {

    @Autowired
    private ActionServiceFactory actionServiceFactory;

    public MANetworksCreateResult modelArtsNetworksCreate(MANetworksCreate param) throws CommonAdapterException, AdapterUnavailableException {
        return (MANetworksCreateResult)actionServiceFactory.getActionService(param).invoke(param);
    }

    public MANetworksDeleteResult modelArtsNetworksDelete(MANetworksDelete param) throws CommonAdapterException, AdapterUnavailableException {
        return (MANetworksDeleteResult)actionServiceFactory.getActionService(param).invoke(param);
    }

    public MANetworksListQueryResult modelArtsNetworksListQuery(MANetworksListQuery param) throws CommonAdapterException, AdapterUnavailableException {
        return (MANetworksListQueryResult)actionServiceFactory.getActionService(param).invoke(param);
    }

    public MANetworksQueryResult modelArtsNetworksQuery(MANetworksQuery param) throws CommonAdapterException, AdapterUnavailableException {
        return (MANetworksQueryResult)actionServiceFactory.getActionService(param).invoke(param);
    }

    public MANetworksUpdateResult modelArtsNetworksUpdate(MANetworksUpdate param) throws CommonAdapterException, AdapterUnavailableException {
        return (MANetworksUpdateResult)actionServiceFactory.getActionService(param).invoke(param);
    }

    public MASharePoolsQueryResult modelArtsSharePoolsQuery(MASharePoolsQuery param) throws CommonAdapterException, AdapterUnavailableException {
        return (MASharePoolsQueryResult)actionServiceFactory.getActionService(param).invoke(param);
    }

    public MASharePoolsUpdateResult modelArtsSharePoolsUpdate(MASharePoolsUpdate param) throws CommonAdapterException, AdapterUnavailableException {
        return (MASharePoolsUpdateResult)actionServiceFactory.getActionService(param).invoke(param);
    }

    public MAPoolsCreateResult modelArtsPoolsCreate(MAPoolsCreate param) throws CommonAdapterException, AdapterUnavailableException {
        return (MAPoolsCreateResult)actionServiceFactory.getActionService(param).invoke(param);
    }

    public MAPoolsDeleteResult modelArtsPoolsDelete(MAPoolsDelete param) throws CommonAdapterException, AdapterUnavailableException {
        return (MAPoolsDeleteResult)actionServiceFactory.getActionService(param).invoke(param);
    }

    public MAPoolsListQueryResult modelArtsPoolsListQuery(MAPoolsListQuery param) throws CommonAdapterException, AdapterUnavailableException {
        return (MAPoolsListQueryResult)actionServiceFactory.getActionService(param).invoke(param);
    }

    public BaseResult modelArtsPoolsMonitor(MAPoolsMonitor param) throws CommonAdapterException, AdapterUnavailableException {
        return actionServiceFactory.getActionService(param).invoke(param);
    }

    public MAPoolsQueryResult modelArtsPoolsQuery(MAPoolsQuery param) throws CommonAdapterException, AdapterUnavailableException {
        return (MAPoolsQueryResult)actionServiceFactory.getActionService(param).invoke(param);
    }

    public MAPoolsUpdateResult modelArtsPoolsUpdate(MAPoolsUpdate param) throws CommonAdapterException, AdapterUnavailableException {
        return (MAPoolsUpdateResult)actionServiceFactory.getActionService(param).invoke(param);
    }

    public MAResourceFlavorsQueryResult modelArtsResourceFlavorsQuery(MAResourceFlavorsQuery param) throws CommonAdapterException, AdapterUnavailableException {
        return (MAResourceFlavorsQueryResult)actionServiceFactory.getActionService(param).invoke(param);
    }

    public OsPoolsAllocateResult OsPoolsAllocate(OsPoolsAllocateRequest param) throws CommonAdapterException, AdapterUnavailableException {
        return (OsPoolsAllocateResult) actionServiceFactory.getActionService(param).invoke(param);
    }

    public MANetworkCidrsResult queryNetworksCidrs(MANetworksCidrsQuery param) throws CommonAdapterException, AdapterUnavailableException{
        return (MANetworkCidrsResult) actionServiceFactory.getActionService(param).invoke(param);
    }

    public TrainingPoolsV1Result trainingPoolsV1(TrainingPoolsRequest param) throws CommonAdapterException, AdapterUnavailableException {
        return (TrainingPoolsV1Result) actionServiceFactory.getActionService(param).invoke(param);
    }

    public TrainingFlavorsResult trainingFlavors(TrainingFlavorsRequest param) throws CommonAdapterException, AdapterUnavailableException {
        return (TrainingFlavorsResult) actionServiceFactory.getActionService(param).invoke(param);
    }

    public TrainingActiveJobsResult trainingActiveJobs(TrainingActiveJobsRequest param) throws CommonAdapterException, AdapterUnavailableException {
        return (TrainingActiveJobsResult) actionServiceFactory.getActionService(param).invoke(param);
    }

    public TrainingActiveJobsV2Result trainingActiveJobsV2(TrainingActiveJobsV2Request param) throws CommonAdapterException, AdapterUnavailableException {
        return (TrainingActiveJobsV2Result) actionServiceFactory.getActionService(param).invoke(param);
    }

    public TrainingJobsResult trainingJobs(TrainingJobsRequest param) throws CommonAdapterException, AdapterUnavailableException {
        return (TrainingJobsResult) actionServiceFactory.getActionService(param).invoke(param);
    }

    public TrainingJobsV2Result trainingJobsV2(TrainingJobsV2Request param) throws CommonAdapterException, AdapterUnavailableException {
        return (TrainingJobsV2Result) actionServiceFactory.getActionService(param).invoke(param);
    }

    public StatisticsNotebooksResult StatisticsNotebooks(StatisticsNotebooksRequest param) throws CommonAdapterException, AdapterUnavailableException {
        return (StatisticsNotebooksResult) actionServiceFactory.getActionService(param).invoke(param);
    }

    public OsClusterResourceSnapshotResult osClusterResourceSnapshot(OsClusterResourceSnapshotRequest param) throws CommonAdapterException, AdapterUnavailableException{
        return (OsClusterResourceSnapshotResult) actionServiceFactory.getActionService(param).invoke(param);
    }

    public OsPoolsV2Result osPoolsV2(OsPoolsV2Request param) throws CommonAdapterException, AdapterUnavailableException{
        return (OsPoolsV2Result) actionServiceFactory.getActionService(param).invoke(param);
    }

    public SummaryResult summary(SummaryRequest param)  throws CommonAdapterException, AdapterUnavailableException{
        return (SummaryResult) actionServiceFactory.getActionService(param).invoke(param);
    }

    public NotebooksResult notebooks(NoteBooksRequest param)  throws CommonAdapterException, AdapterUnavailableException{
        return (NotebooksResult) actionServiceFactory.getActionService(param).invoke(param);
    }

    public ClustersResult clusters(ClustersRequest param)  throws CommonAdapterException, AdapterUnavailableException{
        return (ClustersResult) actionServiceFactory.getActionService(param).invoke(param);
    }

    public ServicesResult services(ServicesRequest param)  throws CommonAdapterException, AdapterUnavailableException{
        return (ServicesResult) actionServiceFactory.getActionService(param).invoke(param);
    }

    public MAAgencyQueryListRes queryListAgency(MAAgencyQueryList param)  throws CommonAdapterException, AdapterUnavailableException{
        return (MAAgencyQueryListRes) actionServiceFactory.getActionService(param).invoke(param);
    }


    public MAAgencyCreateAuthorizeRes agencyCreateAuthorize(MAAgencyCreateAuthorize param)  throws CommonAdapterException, AdapterUnavailableException{
        return (MAAgencyCreateAuthorizeRes) actionServiceFactory.getActionService(param).invoke(param);
    }


    public MAAgencyDeleteRes agencyDelete(MAAgencyDelete param)  throws CommonAdapterException, AdapterUnavailableException{
        return (MAAgencyDeleteRes) actionServiceFactory.getActionService(param).invoke(param);
    }

    public MAAgencyQueryAgenciesRes agencyQueryAgencies(MAAgencyQueryAgencies param)  throws CommonAdapterException, AdapterUnavailableException{
        return (MAAgencyQueryAgenciesRes) actionServiceFactory.getActionService(param).invoke(param);
    }

    public QueryAgencyInfoResult QueryAgencyInfo(QueryAgencyInfo param)  throws CommonAdapterException, AdapterUnavailableException{
        return (QueryAgencyInfoResult) actionServiceFactory.getActionService(param).invoke(param);
    }

    public MAAgencyCreateRes agencyCreate(MAAgencyCreate param)  throws CommonAdapterException, AdapterUnavailableException{
        return (MAAgencyCreateRes) actionServiceFactory.getActionService(param).invoke(param);
    }

    public MAAgencyQueryQuotasRes agencyQueryQuotas(MAAgencyQueryQuotas param)  throws CommonAdapterException, AdapterUnavailableException{
        return (MAAgencyQueryQuotasRes) actionServiceFactory.getActionService(param).invoke(param);
    }

}
