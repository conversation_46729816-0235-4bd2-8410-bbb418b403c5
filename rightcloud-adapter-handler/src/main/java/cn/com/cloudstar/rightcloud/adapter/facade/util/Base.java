/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.adapter.facade.util;

import com.fasterxml.jackson.annotation.JsonProperty;

public class Base {

    @JsonProperty("user_id")
    private String userId;
    private String name;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


}
