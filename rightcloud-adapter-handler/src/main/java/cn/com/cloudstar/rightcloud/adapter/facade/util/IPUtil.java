/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.adapter.facade.util;

import com.google.common.base.Strings;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


/**
 * IP相关信息计算工具类
 *
 * <AUTHOR>
 */
public class IPUtil {

    /**
     * 根据子网和掩码计算ip段
     *
     * @param startIP 开始IP地址
     * @param netmask 掩码
     * @returns IP地址列表
     */
    public static List<String> computeAllIpAddress(String startIP, String netmask) {
        Nets nets = getNets(startIP, netmask);

        return getNetWorkList(nets);
    }

    /**
     * 获取两个ip地址之间所有的ip
     */
    public static List<String> getNetWorkList(Nets nets) {
        String startIp = nets.getStartIP();
        String endIp = nets.getEndIP();
        return getNetWorkList(startIp, endIp);
    }


    /**
     * 根据起始IP地址和子网掩码计算终止IP
     */
    public static Nets getNets(String startIP, String netmask) {
        Nets nets = new Nets();
        String[] start = Negation(startIP, netmask).split("\\.");
        nets.setStartIP(start[0] + "." + start[1] + "." + start[2] + "." + (Integer.valueOf(start[3]) + 1));
        nets.setEndIP(TaskOR(Negation(startIP, netmask), netmask));
        nets.setNetMask(netmask);
        return nets;
    }

    /**
     * temp1根据temp2取反
     */
    private static String Negation(String StartIP, String netmask) {
        String[] temp1 = StartIP.trim().split("\\.");
        String[] temp2 = netmask.trim().split("\\.");
        int[] rets = new int[4];
        for (int i = 0; i < 4; i++) {
            rets[i] = Integer.parseInt(temp1[i]) & Integer.parseInt(temp2[i]);
        }
        return rets[0] + "." + rets[1] + "." + rets[2] + "." + rets[3];
    }

    /**
     * temp1根据temp2取或
     */
    private static String TaskOR(String StartIP, String netmask) {
        String[] temp1 = StartIP.trim().split("\\.");
        String[] temp2 = netmask.trim().split("\\.");
        int[] rets = new int[4];
        for (int i = 0; i < 4; i++) {
            rets[i] = 255 - (Integer.parseInt(temp1[i]) ^ Integer.parseInt(temp2[i]));
        }
        return rets[0] + "." + rets[1] + "." + rets[2] + "." + (rets[3] - 1);
    }

    /**
     * 计算子网大小
     */
    public static int getPoolMax(int netmask) {
        if (netmask <= 0 || netmask >= 32) {
            return 0;
        }
        int bits = 32 - netmask;
        return (int) Math.pow(2, bits) - 2;
    }

    /**
     * 转换为掩码位数
     */
    public static int getNetMask(String netmarks) {
        StringBuffer sbf;
        String str;
        int inetmask = 0, count = 0;
        String[] ipList = netmarks.split("\\.");
        for (int n = 0; n < ipList.length; n++) {
            sbf = toBin(Integer.parseInt(ipList[n]));
            str = sbf.reverse().toString();
            count = 0;
            for (int i = 0; i < str.length(); i++) {
                i = str.indexOf('1', i);
                if (i == -1) {
                    break;
                }
                count++;
            }
            inetmask += count;
        }
        return inetmask;
    }

    /**
     * 判断是否是内网ip.
     *
     * @param addr the addr
     * @return the boolean
     */
    public static boolean internalIp(byte[] addr) {
        final byte b0 = addr[0];
        final byte b1 = addr[1];
        //10.x.x.x/8
        final byte SECTION_1 = 0x0A;
        //172.16.x.x/12
        final byte SECTION_2 = (byte) 0xAC;
        final byte SECTION_3 = (byte) 0x10;
        final byte SECTION_4 = (byte) 0x1F;
        //192.168.x.x/16
        final byte SECTION_5 = (byte) 0xC0;
        final byte SECTION_6 = (byte) 0xA8;
        switch (b0) {
            case SECTION_1:
                return true;
            case SECTION_2:
                if (b1 >= SECTION_3 && b1 <= SECTION_4) {
                    return true;
                }
            case SECTION_5:
                switch (b1) {
                    case SECTION_6:
                        return true;
                }
            default:
                return false;

        }
    }

    private static StringBuffer toBin(int x) {
        StringBuffer result = new StringBuffer();
        result.append(x % 2);
        x /= 2;
        while (x > 0) {
            result.append(x % 2);
            x /= 2;
        }
        return result;
    }

    /**
     * 获取两个ip地址之间所有的ip
     *
     * @param startIp 开始IP
     * @param endIp   结束IP
     * @return IP地址列表
     */
    public static List<String> getNetWorkList(String startIp, String endIp) {
        ArrayList<String> ips = new ArrayList<String>();
        String[] ipFrom = startIp.split("\\.");
        String[] ipTo = endIp.split("\\.");
        int[] int_ipf = new int[4];
        int[] int_ipt = new int[4];
        for (int i = 0; i < 4; i++) {
            int_ipf[i] = Integer.parseInt(ipFrom[i]);
            int_ipt[i] = Integer.parseInt(ipTo[i]);
        }
        for (int A = int_ipf[0]; A <= int_ipt[0]; A++) {
            for (int B = (A == int_ipf[0] ? int_ipf[1] : 0); B <= (A == int_ipt[0] ? int_ipt[1]
                    : 255); B++) {
                for (int C = (B == int_ipf[1] ? int_ipf[2] : 0); C <= (B == int_ipt[1] ? int_ipt[2]
                        : 255); C++) {
                    for (int D = (C == int_ipf[2] ? int_ipf[3] : 0); D <= (C == int_ipt[2] ? int_ipt[3]
                            : 255); D++) {
                        ips.add(A + "." + B + "." + C + "." + D);
                    }
                }
            }
        }
        return ips;
    }

    public static List<String> getIpList(List<String> ipArray, String ip) {
        if (ipArray == null) {
            ipArray = new ArrayList<>();
        }
        if (!Strings.isNullOrEmpty(ip)) {
            Collections.addAll(ipArray, ip.split(","));
        }
        return ipArray;
    }

    /**
     * 从URL中获取IP地址
     */
    public static String getIpFromUrl(String url) {
        if (Strings.isNullOrEmpty(url)) {
            return url;
        }

        /**
         * 示例 1：https://xx.xx.xx.xx:443/sdk/vimService
         * 示例 2：https://xx.xx.xx.xx/sdk/vimService
         */
        String[] split = url.split("/");
        if (split.length > 3) {
            return split[2].split(":")[0];
        }
        return url;
    }

    public static String convertIpToBinaryString(String strIp) {
        Integer ipBinaryLength = 8;
        StringBuilder result = new StringBuilder();
        String[] split = strIp.split("\\.");
        for (int i = 0; i < split.length; i++) {
            String binaryString = Integer.toBinaryString(Integer.parseInt(split[i]));
            StringBuilder sb = new StringBuilder();
            while (sb.length() + binaryString.length() < ipBinaryLength) {
                // 不足8位前面补零
                sb.append("0");
            }
            sb.append(binaryString);
            result.append(sb.toString());
        }

        return result.toString();
    }


    /**
     * 检查新增的IP地址列表是否包含已有的IP地址列表
     *
     * @param oldIps 已有的IP地址列表
     * @param newIps 新增的IP地址列表
     * @return true or false
     */
    public static boolean checkIpRepeat(List<String> oldIps, List<String> newIps) {
        List<String> ipsToCheck = new ArrayList<>(newIps);

        return ipsToCheck.removeAll(oldIps);
    }

    /**
     * 解析IP地址
     *
     * @param ipSegment ip段字符串
     * @return ip列表
     */
    public static List<String> analyzeIpSegment(String ipSegment) {
        if (Strings.isNullOrEmpty(ipSegment)) {
            return new ArrayList<>(1);
        }

        List<String> result = new ArrayList<>();
        String[] pool = ipSegment.split(",");
        for (String startIpAndEndIps : pool) {
            String[] startIpAndEndIp = startIpAndEndIps.split("~");
            List<String> ips = IPUtil.getNetWorkList(startIpAndEndIp[0], startIpAndEndIp[1]);
            // 将不重复的数据加入结果集
            result.removeAll(ips);
            result.addAll(ips);
        }

        return result;
    }

    public static class Nets {

        /**
         * 网络开始IP
         */
        private String startIP;
        /**
         * 网络结果IP
         */
        private String endIP;
        /**
         * 子网掩码
         */
        private String netMask;

        /**
         * 取得startIP
         *
         * @return startIP
         */
        public String getStartIP() {
            return startIP;
        }

        /**
         * 设置startIP
         *
         * @param startIP startIP
         */
        public void setStartIP(String startIP) {
            this.startIP = startIP;
        }

        /**
         * 取得endIP
         *
         * @return endIP
         */
        public String getEndIP() {
            return endIP;
        }

        /**
         * 设置endIP
         *
         * @param endIP endIP
         */
        public void setEndIP(String endIP) {
            this.endIP = endIP;
        }

        /**
         * 取得netMask
         *
         * @return netMask
         */
        public String getNetMask() {
            return netMask;
        }

        /**
         * 设置netMask
         *
         * @param netMask netMask
         */
        public void setNetMask(String netMask) {
            this.netMask = netMask;
        }


    }
}
