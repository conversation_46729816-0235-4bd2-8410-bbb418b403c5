/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.adapter.facade.handler;

import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.HPCRemoveNode;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.HPCRemoveNodeResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.*;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.com.cloudstar.rightcloud.adapter.pojo.azure.ResourceGroupCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.azure.result.ResourceGroupCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.host.ResVmTypeQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.host.result.ResVmTypeQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.FDHPCClusterActive;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.HPCClusterCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.HPCClusterDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.HPCClusterInfoID;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.HPCExpansionNode;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.HPCNodeJobInfo;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.HPCNodeOperate;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.HPCShareServiceCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.HPCShareServiceDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.HPCUnsubscribeReconciliation;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.TaskInfoScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.FDHPCClusterActiveResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.FDTaskInfoResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.HPCClusterCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.HPCClusterDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.HPCClusterInfoIDResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.HPCExpansionNodeResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.HPCNodeJobInfoResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.HPCNodeOperateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.HPCShareServiceDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.HPCShareServiceResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.hpc.result.HPCUnsubscribeReconciliationResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.image.ImageDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.image.ImageUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.image.result.ImageDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.image.result.ImageUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.LbListenerUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.lb.LbListenerUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.SecurityGroupQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.ServerSecurityGroupAdd;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.ServerSecurityGroupDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.SecurityGroupQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.ServerSecurityGroupAddResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.network.result.ServerSecurityGroupDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.price.PriceCalculate;
import cn.com.cloudstar.rightcloud.adapter.pojo.price.PricePayAsYouGoCalculate;
import cn.com.cloudstar.rightcloud.adapter.pojo.price.PriceSubscriptionCalculate;
import cn.com.cloudstar.rightcloud.adapter.pojo.price.QueryBalance;
import cn.com.cloudstar.rightcloud.adapter.pojo.price.result.PriceCalculateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.price.result.QueryBalanceResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.HPCClusterScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.ShareDetailByEnv;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.HPCClusterScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.ShareDetailByEnvResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.QuotaSetCreat;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.ResShareRuleCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.QuotaSetCreatResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.ResShareRuleCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.ShareActionResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.ShareCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.ShareDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.ShareGroupCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.ShareGroupDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.ShareGroupUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.ShareMountTargetCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.ShareMountTargetDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.ShareMountTargetUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.ShareRuleDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.ShareRuleUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.Result.ShareUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.ShareAction;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.ShareCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.ShareDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.ShareGroupCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.ShareGroupDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.ShareGroupUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.ShareMountTargetCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.ShareMountTargetDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.ShareMountTargetUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.ShareRuleDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.ShareRuleUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.share.ShareUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.CloneVmAsTemplate;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.MachineOperate;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.MarkVmAsTemplate;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.ResetPassword;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.ResetVncPassword;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.ServerGroupCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.ServerGroupRemove;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmBlockQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmClone;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmGet;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmImageCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmInfoGet;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmInquiryPrice;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmMigrate;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmModify;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmNicAdd;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmNicDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmOperate;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmQueryDataStore;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmQuerySgs;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmRebuild;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmReconfig;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmRecovery;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmReinstallSystem;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmRemove;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmRename;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmRenewInstance;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmResize;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmSnapshotCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmSnapshotDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmSnapshotRevert;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmSnapshotUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmTypeChange;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmTypeChangeQuery;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.VmVncConsole;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.MachineOperateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.ResetPasswordResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.ResetVncPasswordResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.ServerGroupCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.ServerGroupRemoveResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmBlockQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmCloneResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmGetInfoResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmImageCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmInquiryPriceResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmMigrateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmModifyResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmNicAddResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmNicDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmOperateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmQueryDataStoreResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmQuerySgsResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmRebuildResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmReconfigResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmRecoveryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmReinstallSystemResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmRemoveResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmRenameResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmRenewInstanceResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmResizeResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmSnapshotCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmSnapshotDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmSnapshotRevertResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmSnapshotUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmTemplateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmTypeChangeQueryResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmTypeChangeResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.vm.result.VmVncConsoleResult;
import cn.com.cloudstar.rightcloud.driver.core.ActionServiceFactory;
import cn.com.cloudstar.rightcloud.driver.core.exception.AdapterUnavailableException;
import cn.com.cloudstar.rightcloud.driver.core.exception.CommonAdapterException;

/**
 * The type Vm handler.
 */
@Service
public class VmHandler {

    @Autowired
    private ActionServiceFactory actionServiceFactory;


    /**
     * Create cloud vm cloud vm create result.
     *
     * @param subscriptionCalculate the vm create
     *
     * @return the cloud vm create result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public PriceCalculateResult getSubscriptionPrice(PriceSubscriptionCalculate subscriptionCalculate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (PriceCalculateResult) actionServiceFactory.getActionService(subscriptionCalculate)
                                                          .invoke(subscriptionCalculate);

    }

    /**
     * Create cloud vm cloud vm create result.
     *
     * @param pricePayAsYouGoCalculate the vm create
     *
     * @return the cloud vm create result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public PriceCalculateResult getPayAsYouGoPrice(PricePayAsYouGoCalculate pricePayAsYouGoCalculate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (PriceCalculateResult) actionServiceFactory.getActionService(pricePayAsYouGoCalculate)
                                                          .invoke(pricePayAsYouGoCalculate);

    }

    /**
     * Create cloud vm cloud vm create result.
     *
     * @param vmTypeChangeQuery the vm create
     *
     * @return the cloud vm create result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public VmTypeChangeQueryResult getVmTypeChangeQuery(VmTypeChangeQuery vmTypeChangeQuery)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VmTypeChangeQueryResult) actionServiceFactory.getActionService(vmTypeChangeQuery)
                                                             .invoke(vmTypeChangeQuery);
    }

    /**
     * Create cloud vm cloud vm create result.
     *
     * @param quotaSetCreat the vm create
     *
     * @return the cloud vm create result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public QuotaSetCreatResult getQuotaSet(QuotaSetCreat quotaSetCreat)
            throws CommonAdapterException, AdapterUnavailableException {
        return (QuotaSetCreatResult) actionServiceFactory.getActionService(quotaSetCreat)
                                                         .invoke(quotaSetCreat);
    }

    /**
     * Create cloud vm cloud vm create result.
     *
     * @param vmRenew the vm create
     *
     * @return the cloud vm create result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public VmRenewInstanceResult renewInstance(VmRenewInstance vmRenew)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VmRenewInstanceResult) actionServiceFactory.getActionService(vmRenew).invoke(vmRenew);
    }

    /**
     * Create cloud vm cloud vm create result.
     *
     * @param vmInquiryPrice the vm create
     *
     * @return the cloud vm create result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public VmInquiryPriceResult vmInquiryPrice(VmInquiryPrice vmInquiryPrice)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VmInquiryPriceResult) actionServiceFactory.getActionService(vmInquiryPrice).invoke(vmInquiryPrice);
    }

    /**
     * Create cloud vm cloud vm create result.
     *
     * @param shareUpdate the vm create
     *
     * @return the cloud vm create result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public ShareUpdateResult shareUpdate(ShareUpdate shareUpdate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ShareUpdateResult) actionServiceFactory.getActionService(shareUpdate)
                                                       .invoke(shareUpdate);
    }

    public ShareActionResult shareAction(ShareAction shareAction)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ShareActionResult) actionServiceFactory.getActionService(shareAction)
                                                       .invoke(shareAction);
    }

    public ShareModifyResult shareModify(ShareModify shareModify)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ShareModifyResult) actionServiceFactory.getActionService(shareModify)
                .invoke(shareModify);
    }

    public ShareRuleDeleteResult shareRuleDelete(ShareRuleDelete shareRuleDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ShareRuleDeleteResult) actionServiceFactory.getActionService(shareRuleDelete)
                                                           .invoke(shareRuleDelete);
    }

    public ResShareRuleCreateResult resShareRule(ResShareRuleCreate resShareRuleCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ResShareRuleCreateResult) actionServiceFactory.getActionService(resShareRuleCreate)
                                                              .invoke(resShareRuleCreate);
    }

    /**
     * Operate vm vm operate result.
     *
     * @param vmOperate the vm operate
     *
     * @return the vm operate result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public VmOperateResult operateVm(VmOperate vmOperate) throws CommonAdapterException, AdapterUnavailableException {

        return (VmOperateResult) actionServiceFactory.getActionService(vmOperate).invoke(vmOperate);
    }


    /**
     * Create vm vm create result.
     *
     * @param vmCreate the vm create
     *
     * @return the vm create result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public VmCreateResult createVm(VmCreate vmCreate) throws CommonAdapterException, AdapterUnavailableException {

        return (VmCreateResult) actionServiceFactory.getActionService(vmCreate).invoke(vmCreate);
    }

    /**
     * Create vm vm create result.
     *
     * @param vmTypeChange the vm create
     *
     * @return the vm create result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public VmTypeChangeResult vmTypeChange(VmTypeChange vmTypeChange)
            throws CommonAdapterException, AdapterUnavailableException {

        return (VmTypeChangeResult) actionServiceFactory.getActionService(vmTypeChange).invoke(vmTypeChange);
    }

    /**
     * Create share share create result.
     *
     * @param shareCreate the share create
     *
     * @return the share create result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public ShareCreateResult createShare(ShareCreate shareCreate)
            throws CommonAdapterException, AdapterUnavailableException {

        return (ShareCreateResult) actionServiceFactory.getActionService(shareCreate).invoke(shareCreate);
    }

    /**
     * Create share share create result.
     *
     * @param shareDelete the share create
     *
     * @return the share create result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public ShareDeleteResult deleteShare(ShareDelete shareDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ShareDeleteResult) actionServiceFactory.getActionService(shareDelete).invoke(shareDelete);
    }

    /**
     * Reconfig vm vm reconfig result.
     *
     * @param vmReconfig the vm reconfig
     *
     * @return the vm reconfig result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public VmReconfigResult reconfigVm(VmReconfig vmReconfig)
            throws CommonAdapterException, AdapterUnavailableException {

        return (VmReconfigResult) actionServiceFactory.getActionService(vmReconfig).invoke(vmReconfig);
    }


    /**
     * Snapshot vm vm snapshot create result.
     *
     * @param vmSnapshotCreate the vm snapshot create
     *
     * @return the vm snapshot create result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public VmSnapshotCreateResult snapshotVm(VmSnapshotCreate vmSnapshotCreate)
            throws CommonAdapterException, AdapterUnavailableException {

        return (VmSnapshotCreateResult) actionServiceFactory.getActionService(vmSnapshotCreate)
                                                            .invoke(vmSnapshotCreate);
    }

    public VmSnapshotUpdateResult updateSnapshot(VmSnapshotUpdate snapshotUpdate)
            throws CommonAdapterException, AdapterUnavailableException {

        return (VmSnapshotUpdateResult) actionServiceFactory.getActionService(snapshotUpdate).invoke(snapshotUpdate);
    }


    /**
     * Remove vm vm remove result.
     *
     * @param vmRemove the vm remove
     *
     * @return the vm remove result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public VmRemoveResult removeVm(VmRemove vmRemove) throws CommonAdapterException, AdapterUnavailableException {

        return (VmRemoveResult) actionServiceFactory.getActionService(vmRemove).invoke(vmRemove);
    }


    /**
     * Migrate vm vm migrate result.
     *
     * @param vmMigrate the vm migrate
     *
     * @return the vm migrate result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public VmMigrateResult migrateVm(VmMigrate vmMigrate) throws CommonAdapterException, AdapterUnavailableException {

        return (VmMigrateResult) actionServiceFactory.getActionService(vmMigrate).invoke(vmMigrate);
    }


    /**
     * Revert vm vm snapshot revert result.
     *
     * @param vmRevert the vm revert
     *
     * @return the vm snapshot revert result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public VmSnapshotRevertResult revertVm(VmSnapshotRevert vmRevert)
            throws CommonAdapterException, AdapterUnavailableException {

        return (VmSnapshotRevertResult) actionServiceFactory.getActionService(vmRevert).invoke(vmRevert);
    }


    /**
     * Gets vm.
     *
     * @param vmGet the vm get
     *
     * @return the vm
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public VmGetInfoResult getVm(VmGet vmGet) throws CommonAdapterException, AdapterUnavailableException {

        return (VmGetInfoResult) actionServiceFactory.getActionService(vmGet).invoke(vmGet);
    }


    /**
     * Query vms vm query result.
     *
     * @param vmQuery the vm query
     *
     * @return the vm query result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public VmQueryResult queryVms(VmQuery vmQuery) throws CommonAdapterException, AdapterUnavailableException {

        return (VmQueryResult) actionServiceFactory.getActionService(vmQuery).invoke(vmQuery);

    }


    /**
     * Delete vm snapshot vm snapshot delete result.
     *
     * @param vmSnapshotDelete the vm snapshot delete
     *
     * @return the vm snapshot delete result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public VmSnapshotDeleteResult deleteVmSnapshot(VmSnapshotDelete vmSnapshotDelete)
            throws CommonAdapterException, AdapterUnavailableException {

        return (VmSnapshotDeleteResult) actionServiceFactory.getActionService(vmSnapshotDelete)
                                                            .invoke(vmSnapshotDelete);
    }


    /**
     * Add nic vm nic add result.
     *
     * @param vmNicAdd the vm nic add
     *
     * @return the vm nic add result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public VmNicAddResult addNic(VmNicAdd vmNicAdd) throws CommonAdapterException, AdapterUnavailableException {

        return (VmNicAddResult) actionServiceFactory.getActionService(vmNicAdd).invoke(vmNicAdd);
    }


    /**
     * Delete nic vm nic delete result.
     *
     * @param vmNicDelete the vm nic delete
     *
     * @return the vm nic delete result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public VmNicDeleteResult deleteNic(VmNicDelete vmNicDelete)
            throws CommonAdapterException, AdapterUnavailableException {

        return (VmNicDeleteResult) actionServiceFactory.getActionService(vmNicDelete).invoke(vmNicDelete);
    }


    /**
     * Modify vm vm modify result.
     *
     * @param vmModify the vm modify
     *
     * @return the vm modify result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public VmModifyResult modifyVm(VmModify vmModify) throws CommonAdapterException, AdapterUnavailableException {

        return (VmModifyResult) actionServiceFactory.getActionService(vmModify).invoke(vmModify);
    }


    /**
     * Rename vm vm rename result.
     *
     * @param vmRename the vm rename
     *
     * @return the vm rename result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public VmRenameResult renameVm(VmRename vmRename) throws CommonAdapterException, AdapterUnavailableException {

        return (VmRenameResult) actionServiceFactory.getActionService(vmRename).invoke(vmRename);
    }


    /**
     * Rebuild vm vm rebuild result.
     *
     * @param vmRebuild the vm rebuild
     *
     * @return the vm rebuild result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public VmRebuildResult rebuildVm(VmRebuild vmRebuild) throws CommonAdapterException, AdapterUnavailableException {

        return (VmRebuildResult) actionServiceFactory.getActionService(vmRebuild).invoke(vmRebuild);
    }


    /**
     * Gets console url.
     *
     * @param vmVncConsole the vm vnc console
     *
     * @return the console url
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public VmVncConsoleResult getConsoleUrl(VmVncConsole vmVncConsole)
            throws CommonAdapterException, AdapterUnavailableException {

        return (VmVncConsoleResult) actionServiceFactory.getActionService(vmVncConsole).invoke(vmVncConsole);
    }


    /**
     * Resize vm vm resize result.
     *
     * @param vmResize the vm resize
     *
     * @return the vm resize result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public VmResizeResult resizeVm(VmResize vmResize) throws CommonAdapterException, AdapterUnavailableException {

        return (VmResizeResult) actionServiceFactory.getActionService(vmResize).invoke(vmResize);
    }


    /**
     * Image vm vm image create result.
     *
     * @param vmImageCreate the vm image create
     *
     * @return the vm image create result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public VmImageCreateResult imageVm(VmImageCreate vmImageCreate)
            throws CommonAdapterException, AdapterUnavailableException {

        return (VmImageCreateResult) actionServiceFactory.getActionService(vmImageCreate).invoke(vmImageCreate);
    }


    /**
     * Recovery vm vm recovery result.
     *
     * @param vmRecovery the vm recovery
     *
     * @return the vm recovery result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public VmRecoveryResult recoveryVm(VmRecovery vmRecovery)
            throws CommonAdapterException, AdapterUnavailableException {

        return (VmRecoveryResult) actionServiceFactory.getActionService(vmRecovery).invoke(vmRecovery);
    }


    /**
     * Query blocks vm block query result.
     *
     * @param vmBlockQuery the vm block query
     *
     * @return the vm block query result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public VmBlockQueryResult queryBlocks(VmBlockQuery vmBlockQuery)
            throws CommonAdapterException, AdapterUnavailableException {

        return (VmBlockQueryResult) actionServiceFactory.getActionService(vmBlockQuery).invoke(vmBlockQuery);
    }


    /**
     * Add security group server security group add result.
     *
     * @param securityGroupAdd the security group add
     *
     * @return the server security group add result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public ServerSecurityGroupAddResult addSecurityGroup(ServerSecurityGroupAdd securityGroupAdd)
            throws CommonAdapterException, AdapterUnavailableException {

        return (ServerSecurityGroupAddResult) actionServiceFactory.getActionService(securityGroupAdd)
                                                                  .invoke(securityGroupAdd);
    }


    /**
     * Delete security group server security group delete result.
     *
     * @param securityGroupDelete the security group delete
     *
     * @return the server security group delete result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public ServerSecurityGroupDeleteResult deleteSecurityGroup(ServerSecurityGroupDelete securityGroupDelete)
            throws CommonAdapterException, AdapterUnavailableException {

        return (ServerSecurityGroupDeleteResult) actionServiceFactory.getActionService(securityGroupDelete)
                                                                     .invoke(securityGroupDelete);
    }


    /**
     * Query security group security group query result.
     *
     * @param securityGroupQuery the security group query
     *
     * @return the security group query result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public SecurityGroupQueryResult querySecurityGroup(SecurityGroupQuery securityGroupQuery)
            throws CommonAdapterException, AdapterUnavailableException {

        return (SecurityGroupQueryResult) actionServiceFactory.getActionService(securityGroupQuery)
                                                              .invoke(securityGroupQuery);
    }


    /**
     * Delete vm vm delete result.
     *
     * @param vmDelete the vm delete
     *
     * @return the vm delete result
     */
    public VmDeleteResult deleteVm(VmDelete vmDelete) throws CommonAdapterException, AdapterUnavailableException {

        return (VmDeleteResult) actionServiceFactory.getActionService(vmDelete).invoke(vmDelete);
    }


    /**
     * Gets vm info.
     *
     * @param vmInfoGet the vm info get
     *
     * @return the vm info
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public VmGetInfoResult getVmInfo(VmInfoGet vmInfoGet) throws CommonAdapterException, AdapterUnavailableException {

        return (VmGetInfoResult) actionServiceFactory.getActionService(vmInfoGet).invoke(vmInfoGet);
    }


    /**
     * Delete image image delete result.
     *
     * @param imageDelete the image delete
     *
     * @return the image delete result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public ImageDeleteResult deleteImage(ImageDelete imageDelete)
            throws CommonAdapterException, AdapterUnavailableException {

        return (ImageDeleteResult) actionServiceFactory.getActionService(imageDelete).invoke(imageDelete);
    }


    /**
     * Delete snapshot vm snapshot delete result.
     *
     * @param vmSnapshotDelete the vm snapshot delete
     *
     * @return the vm snapshot delete result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public VmSnapshotDeleteResult deleteSnapshot(VmSnapshotDelete vmSnapshotDelete)
            throws CommonAdapterException, AdapterUnavailableException {

        return (VmSnapshotDeleteResult) actionServiceFactory.getActionService(vmSnapshotDelete)
                                                            .invoke(vmSnapshotDelete);
    }


    /**
     * Query vm security groups vm query sgs result.
     *
     * @param vmQuerySgs the vm query sgs
     *
     * @return the vm query sgs result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public VmQuerySgsResult queryVmSecurityGroups(VmQuerySgs vmQuerySgs)
            throws CommonAdapterException, AdapterUnavailableException {

        return (VmQuerySgsResult) actionServiceFactory.getActionService(vmQuerySgs).invoke(vmQuerySgs);
    }


    /**
     * Update image image update result.
     *
     * @param imageUpdate the image update
     *
     * @return the image update result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public ImageUpdateResult updateImage(ImageUpdate imageUpdate)
            throws CommonAdapterException, AdapterUnavailableException {

        return (ImageUpdateResult) actionServiceFactory.getActionService(imageUpdate).invoke(imageUpdate);
    }


    /**
     * Clome vm vm clone result.
     *
     * @param vmClone the vm clone
     *
     * @return the vm clone result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public VmCloneResult cloneVm(VmClone vmClone) throws CommonAdapterException, AdapterUnavailableException {
        return (VmCloneResult) actionServiceFactory.getActionService(vmClone).invoke(vmClone);
    }

    /**
     * price calculate result.
     *
     * @param priceCalculate
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public PriceCalculateResult getPrice(PriceCalculate priceCalculate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (PriceCalculateResult) actionServiceFactory.getActionService(priceCalculate).invoke(priceCalculate);
    }

    public QueryBalanceResult queryBalance(QueryBalance queryBalance)
            throws CommonAdapterException, AdapterUnavailableException {
        return (QueryBalanceResult) actionServiceFactory.getActionService(queryBalance).invoke(queryBalance);
    }

    /**
     * @param resourceGroupCreate
     */
    public ResourceGroupCreateResult createResourceGroup(ResourceGroupCreate resourceGroupCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ResourceGroupCreateResult) actionServiceFactory.getActionService(resourceGroupCreate)
                                                               .invoke(resourceGroupCreate);
    }

    public HPCClusterCreateResult createHPCCluster(HPCClusterCreate hpcClusterCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (HPCClusterCreateResult) actionServiceFactory.getActionService(hpcClusterCreate)
                                                            .invoke(hpcClusterCreate);
    }

    public HPCExpansionNodeResult expansionNode(HPCExpansionNode hpcCloudExpansionNode)
            throws CommonAdapterException, AdapterUnavailableException {
        return (HPCExpansionNodeResult) actionServiceFactory.getActionService(hpcCloudExpansionNode)
                                                            .invoke(hpcCloudExpansionNode);
    }

    public HPCNodeOperateResult expansionNode(HPCNodeOperate hpcNodeOperation)
            throws CommonAdapterException, AdapterUnavailableException {
        return (HPCNodeOperateResult) actionServiceFactory.getActionService(hpcNodeOperation)
                                                          .invoke(hpcNodeOperation);
    }

    public HPCNodeJobInfoResult expansionNode(HPCNodeJobInfo hpcNodeJobInfo)
            throws CommonAdapterException, AdapterUnavailableException {
        return (HPCNodeJobInfoResult) actionServiceFactory.getActionService(hpcNodeJobInfo)
                                                          .invoke(hpcNodeJobInfo);
    }

    public HPCRemoveNodeResult removeNode(HPCRemoveNode hpcRemoveNode)
            throws CommonAdapterException, AdapterUnavailableException {
        return (HPCRemoveNodeResult) actionServiceFactory.getActionService(hpcRemoveNode)
                .invoke(hpcRemoveNode);
    }



    public HPCClusterScanResult createHPCClusterScan(HPCClusterScan hpcClusterScan)
            throws CommonAdapterException, AdapterUnavailableException {
        return (HPCClusterScanResult) actionServiceFactory.getActionService(hpcClusterScan)
                                                          .invoke(hpcClusterScan);
    }

    /**
     * 退订专属资源池
     *
     * @param hpcClusterDelete
     *
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public HPCClusterDeleteResult releaseHPCCluster(HPCClusterDelete hpcClusterDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (HPCClusterDeleteResult) actionServiceFactory.getActionService(hpcClusterDelete)
                                                            .invoke(hpcClusterDelete);
    }

    /**
     * 退订专属资源池
     *
     * @param hpcShareServiceDelete
     *
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public HPCShareServiceDeleteResult releaseHPCShareCluster(HPCShareServiceDelete hpcShareServiceDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (HPCShareServiceDeleteResult) actionServiceFactory.getActionService(hpcShareServiceDelete)
                                                                 .invoke(hpcShareServiceDelete);
    }

    public FDTaskInfoResult taskInfoScan(TaskInfoScan taskInfoScan)
            throws CommonAdapterException, AdapterUnavailableException {
        return (FDTaskInfoResult) actionServiceFactory.getActionService(taskInfoScan)
                                                      .invoke(taskInfoScan);
    }

    public HPCShareServiceResult createHPCShareCluster(HPCShareServiceCreate hpcClusterShareCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (HPCShareServiceResult) actionServiceFactory.getActionService(hpcClusterShareCreate)
                                                           .invoke(hpcClusterShareCreate);
    }

    public FDHPCClusterActiveResult fdhpcClusterActive(FDHPCClusterActive fdhpcClusterActive)
            throws CommonAdapterException, AdapterUnavailableException {
        return (FDHPCClusterActiveResult) actionServiceFactory.getActionService(fdhpcClusterActive)
                                                              .invoke(fdhpcClusterActive);
    }

    public HPCClusterInfoIDResult queryHPCClusterInfoID(HPCClusterInfoID hpcClusterInfoID)
            throws CommonAdapterException, AdapterUnavailableException {
        return (HPCClusterInfoIDResult) actionServiceFactory.getActionService(hpcClusterInfoID)
                                                            .invoke(hpcClusterInfoID);
    }

    public LbListenerUpdateResult updateLbListener(LbListenerUpdate lbListenerUpdate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (LbListenerUpdateResult) actionServiceFactory.getActionService(lbListenerUpdate)
                                                            .invoke(lbListenerUpdate);
    }

    /**
     * 重置密码
     *
     * @param resetPassword 重置密码
     *
     * @return ResetPasswordResult
     *
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public ResetPasswordResult resetPassword(ResetPassword resetPassword)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ResetPasswordResult) actionServiceFactory.getActionService(resetPassword)
                                                         .invoke(resetPassword);
    }

    /**
     * 将 VMWare 实例标记为模板
     *
     * @param markVmAsTemplate
     *
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public VmTemplateResult markVmAsTemplate(MarkVmAsTemplate markVmAsTemplate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VmTemplateResult) actionServiceFactory.getActionService(markVmAsTemplate)
                                                      .invoke(markVmAsTemplate);
    }

    /**
     * 将 VMWare 克隆为模板
     *
     * @param cloneVmAsTemplate
     *
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public VmTemplateResult cloneVmAsTemplate(CloneVmAsTemplate cloneVmAsTemplate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VmTemplateResult) actionServiceFactory.getActionService(cloneVmAsTemplate)
                                                      .invoke(cloneVmAsTemplate);
    }

    /**
     * Update share group create result.
     *
     * @param shareGroupUpdate the share group create
     *
     * @return the share group create result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public ShareGroupUpdateResult updateShareGroup(ShareGroupUpdate shareGroupUpdate)
            throws CommonAdapterException, AdapterUnavailableException {

        return (ShareGroupUpdateResult) actionServiceFactory.getActionService(shareGroupUpdate)
                                                            .invoke(shareGroupUpdate);
    }

    /**
     * Create share group create result.
     *
     * @param shareGroupCreate the share group create
     *
     * @return the share group create result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public ShareGroupCreateResult createShareGroup(ShareGroupCreate shareGroupCreate)
            throws CommonAdapterException, AdapterUnavailableException {

        return (ShareGroupCreateResult) actionServiceFactory.getActionService(shareGroupCreate)
                                                            .invoke(shareGroupCreate);
    }

    /**
     * share target create result.
     *
     * @param shareMountTargetCreate the share target create
     * @return the share target create result
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public ShareMountTargetCreateResult createShareTarget(
            ShareMountTargetCreate shareMountTargetCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ShareMountTargetCreateResult) actionServiceFactory
                .getActionService(shareMountTargetCreate).invoke(shareMountTargetCreate);
    }

    /**
     * share target update result.
     *
     * @param shareMountTargetUpdate the share target update
     *
     * @return the share target update result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public ShareMountTargetUpdateResult updateShareTarget(ShareMountTargetUpdate shareMountTargetUpdate)
            throws CommonAdapterException, AdapterUnavailableException {

        return (ShareMountTargetUpdateResult) actionServiceFactory
                .getActionService(shareMountTargetUpdate).invoke(shareMountTargetUpdate);
    }

    /**
     * share target delete result.
     *
     * @param shareMountTargetDelete the share target delete
     * @return the share target delete result
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public ShareMountTargetDeleteResult deleteShareTarget(
            ShareMountTargetDelete shareMountTargetDelete)
            throws CommonAdapterException, AdapterUnavailableException {

        return (ShareMountTargetDeleteResult) actionServiceFactory
                .getActionService(shareMountTargetDelete).invoke(shareMountTargetDelete);
    }

    /**
     * share group delete result.
     *
     * @param shareGroupDelete the share group delete
     * @return the share group delete result
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public ShareGroupDeleteResult deleteShareGroup(ShareGroupDelete shareGroupDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ShareGroupDeleteResult) actionServiceFactory.getActionService(shareGroupDelete)
                                                            .invoke(shareGroupDelete);
    }

    /**
     * share rule update result.
     *
     * @param shareRuleUpdate the share rule update
     * @return the share rule update result
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public ShareRuleUpdateResult updateShareRule(ShareRuleUpdate shareRuleUpdate)
            throws CommonAdapterException, AdapterUnavailableException {

        return (ShareRuleUpdateResult) actionServiceFactory.getActionService(shareRuleUpdate)
                                                           .invoke(shareRuleUpdate);
    }

    /**
     * 创建主机组.
     *
     * @param serverGroupCreate
     *
     * @return the server group create result
     */
    public ServerGroupCreateResult createServerGroup(ServerGroupCreate serverGroupCreate)
            throws CommonAdapterException, AdapterUnavailableException {

        return (ServerGroupCreateResult) actionServiceFactory.getActionService(serverGroupCreate)
                                                             .invoke(serverGroupCreate);
    }

    /**
     * 删除主机组.
     *
     * @param serverGroupRemove
     *
     * @return the server group delete result
     */
    public ServerGroupRemoveResult deleteServerGroup(ServerGroupRemove serverGroupRemove)
            throws CommonAdapterException, AdapterUnavailableException {

        return (ServerGroupRemoveResult) actionServiceFactory.getActionService(serverGroupRemove)
                                                             .invoke(serverGroupRemove);
    }

    public ResVmTypeQueryResult vmTypeQuery(ResVmTypeQuery query) throws Exception {
        return (ResVmTypeQueryResult) actionServiceFactory.getActionService(query).invoke(query);
    }

    public MachineOperateResult operateVm(MachineOperate machineOperate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (MachineOperateResult) actionServiceFactory.getActionService(machineOperate).invoke(machineOperate);
    }

    public ResetVncPasswordResult resetVncPassword(ResetVncPassword resetVncPassword)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ResetVncPasswordResult) actionServiceFactory.getActionService(resetVncPassword)
                                                            .invoke(resetVncPassword);
    }

    public VmQueryDataStoreResult vmQueryDataStore(VmQueryDataStore vmQueryDataStore)
            throws CommonAdapterException, AdapterUnavailableException {

        return (VmQueryDataStoreResult) actionServiceFactory.getActionService(vmQueryDataStore)
                                                            .invoke(vmQueryDataStore);
    }

    /**
     * 重装操作系统
     * @param vmReinstallSystem
     * @return
     */
    public VmReinstallSystemResult vmReinstallSystem(VmReinstallSystem vmReinstallSystem)
            throws CommonAdapterException, AdapterUnavailableException {

        return (VmReinstallSystemResult) actionServiceFactory.getActionService(vmReinstallSystem)
                                                             .invoke(vmReinstallSystem);
    }

    public ShareDetailByEnvResult shareDetailByEnv(ShareDetailByEnv shareDetailByEnv)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ShareDetailByEnvResult) actionServiceFactory.getActionService(shareDetailByEnv)
                                                            .invoke(shareDetailByEnv);
    }

    public HPCUnsubscribeReconciliationResult expansionNode(
            HPCUnsubscribeReconciliation hpcUnsubscribeReconciliation)
            throws CommonAdapterException, AdapterUnavailableException {
        return (HPCUnsubscribeReconciliationResult) actionServiceFactory.getActionService(hpcUnsubscribeReconciliation)
                                                                        .invoke(hpcUnsubscribeReconciliation);
    }

    public ShareCreateResult createShare(SharePredeployCreate sharePredeployCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ShareCreateResult) actionServiceFactory.getActionService(sharePredeployCreate).invoke(sharePredeployCreate);
    }

    public ShareDeleteResult deleteShare(SharePredeployDelete sharePredeployDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ShareDeleteResult) actionServiceFactory.getActionService(sharePredeployDelete).invoke(sharePredeployDelete);
    }

    public ShareModifyResult shareModify(SharePredeployModify sharePredeployModify)
            throws CommonAdapterException, AdapterUnavailableException {
        return (ShareModifyResult) actionServiceFactory.getActionService(sharePredeployModify)
                .invoke(sharePredeployModify);
    }


    public SharePredeployUninstallResult shareUninstall(SharePredeployUninstall sharePredeployUninstall) throws CommonAdapterException, AdapterUnavailableException {
        return (SharePredeployUninstallResult) actionServiceFactory.getActionService(sharePredeployUninstall).invoke(sharePredeployUninstall);
    }
}
