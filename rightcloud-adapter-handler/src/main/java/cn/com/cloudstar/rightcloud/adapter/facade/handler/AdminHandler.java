/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package cn.com.cloudstar.rightcloud.adapter.facade.handler;


import cn.com.cloudstar.rightcloud.adapter.pojo.vdc.*;
import cn.com.cloudstar.rightcloud.adapter.pojo.vdc.result.*;
import cn.com.cloudstar.rightcloud.common.constants.res.type.CloudEnvType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.com.cloudstar.rightcloud.adapter.pojo.admin.QuotaConfig;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.RoleList;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.UserEdit;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.UserPasswordModify;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.UserPasswordValid;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.UserRoleAdd;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.UserRoleDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.UserRoleList;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.QuotaConfigResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.RoleListResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.UserEditResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.UserPasswordModifyResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.UserPasswordValidResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.UserRoleAddResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.UserRoleDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.admin.result.UserRoleListResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.host.HostAggregateCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.host.HostAggregateDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.host.HostAggregateServerManage;
import cn.com.cloudstar.rightcloud.adapter.pojo.host.HostAggregateUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.host.result.HostAggregateCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.host.result.HostAggregateDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.host.result.HostAggregateServerManageResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.host.result.HostAggregateUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.keypairs.KeypairCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.keypairs.KeypairDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.keypairs.KeypairGet;
import cn.com.cloudstar.rightcloud.adapter.pojo.keypairs.KeypairListGet;
import cn.com.cloudstar.rightcloud.adapter.pojo.keypairs.result.KeypairCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.keypairs.result.KeypairDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.keypairs.result.KeypairGetResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.keypairs.result.KeypairListGetResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.region.RegionCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.region.RegionDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.region.RegionUpdate;
import cn.com.cloudstar.rightcloud.adapter.pojo.region.result.RegionCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.region.result.RegionDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.region.result.RegionUpdateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.HostAggregateScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.RoleScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.User2ProjectScan;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.HostAggregateScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.RoleScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.scan.result.User2ProjectScanResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.tenant.AddUserToTenant;
import cn.com.cloudstar.rightcloud.adapter.pojo.tenant.RemoveUserFromTenant;
import cn.com.cloudstar.rightcloud.adapter.pojo.tenant.TenantCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.tenant.TenantDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.tenant.TenantEdit;
import cn.com.cloudstar.rightcloud.adapter.pojo.tenant.TenantInfoGet;
import cn.com.cloudstar.rightcloud.adapter.pojo.tenant.TenantListGet;
import cn.com.cloudstar.rightcloud.adapter.pojo.tenant.TenantResourcesDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.tenant.TenantUserConfig;
import cn.com.cloudstar.rightcloud.adapter.pojo.tenant.result.AddUserToTenantResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.tenant.result.RemoveUserFromTenantResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.tenant.result.TenantCreateResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.tenant.result.TenantDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.tenant.result.TenantEditResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.tenant.result.TenantInfoGetResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.tenant.result.TenantListGetResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.tenant.result.TenantResourcesDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.tenant.result.TenantUserConfigResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.CloudOsAddTenants;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.HasAdminPermission;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.UserCreate;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.UserDelete;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.UserInfoGet;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.UserListGet;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.UserModify;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.result.CloudOsAddTenantsResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.result.HasAdminPermissionResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.result.UserDeleteResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.result.UserInfoGetResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.result.UserListGetResult;
import cn.com.cloudstar.rightcloud.adapter.pojo.user.result.UserModifyResult;
import cn.com.cloudstar.rightcloud.driver.core.ActionServiceFactory;
import cn.com.cloudstar.rightcloud.driver.core.exception.AdapterUnavailableException;
import cn.com.cloudstar.rightcloud.driver.core.exception.CommonAdapterException;

/**
 * admin handler template
 *
 * <AUTHOR>
 */
@Service
public class AdminHandler {

    @Autowired
    private ActionServiceFactory actionServiceFactory;

    /**
     * Add role to user user role add result.
     *
     * @param userRoleAdd the user role add
     *
     * @return the user role add result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public UserRoleAddResult addRoleToUser(UserRoleAdd userRoleAdd)
            throws CommonAdapterException, AdapterUnavailableException {
        return (UserRoleAddResult) actionServiceFactory.getActionService(userRoleAdd).invoke(userRoleAdd);

    }

    /**
     * Delete role to user user role delete result.
     *
     * @param userRoleDelete the user role delete
     *
     * @return the user role delete result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public UserRoleDeleteResult deleteRoleToUser(UserRoleDelete userRoleDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (UserRoleDeleteResult) actionServiceFactory.getActionService(userRoleDelete).invoke(userRoleDelete);

    }

    /**
     * List roles role list result.
     *
     * @param roleList the role list
     *
     * @return the role list result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public RoleListResult listRoles(RoleList roleList) throws CommonAdapterException, AdapterUnavailableException {
        return (RoleListResult) actionServiceFactory.getActionService(roleList).invoke(roleList);
    }

    /**
     * List user roles user role list result.
     *
     * @param userRoleList the user role list
     *
     * @return the user role list result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public UserRoleListResult listUserRoles(UserRoleList userRoleList)
            throws CommonAdapterException, AdapterUnavailableException {
        return (UserRoleListResult) actionServiceFactory.getActionService(userRoleList).invoke(userRoleList);
    }

    /**
     * Create tenant tenant create result.
     *
     * @param tenantCreate the tenant create
     *
     * @return the tenant create result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public TenantCreateResult createTenant(TenantCreate tenantCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (TenantCreateResult) actionServiceFactory.getActionService(tenantCreate).invoke(tenantCreate);
    }

    /**
     * Create tenant tenant create result.
     *
     * @param cloudOsAddTenants the tenant create
     *
     * @return the tenant create result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public CloudOsAddTenantsResult cloudOsAddTenants(CloudOsAddTenants cloudOsAddTenants)
            throws CommonAdapterException, AdapterUnavailableException {
        return (CloudOsAddTenantsResult) actionServiceFactory.getActionService(cloudOsAddTenants)
                                                             .invoke(cloudOsAddTenants);
    }

    /**
     * Gets tenant.
     *
     * @param tenantInfoGet the tenant info get
     *
     * @return the tenant
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public TenantInfoGetResult getTenant(TenantInfoGet tenantInfoGet)
            throws CommonAdapterException, AdapterUnavailableException {
        return (TenantInfoGetResult) actionServiceFactory.getActionService(tenantInfoGet).invoke(tenantInfoGet);

    }

    /**
     * Query tenant tenant list get result.
     *
     * @param tenantListGet the tenant list get
     *
     * @return the tenant list get result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public TenantListGetResult queryTenant(TenantListGet tenantListGet)
            throws CommonAdapterException, AdapterUnavailableException {
        return (TenantListGetResult) actionServiceFactory.getActionService(tenantListGet).invoke(tenantListGet);
    }

    /**
     * Delete tenant tenant delete result.
     *
     * @param tenantDelete the tenant delete
     *
     * @return the tenant delete result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public cn.com.cloudstar.rightcloud.adapter.pojo.tenant.result.TenantDeleteResult deleteTenant(
            TenantDelete tenantDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (TenantDeleteResult) actionServiceFactory.getActionService(tenantDelete).invoke(tenantDelete);
    }

    /**
     * Create user user create result.
     *
     * @param userCreate the user create
     *
     * @return the user create result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public cn.com.cloudstar.rightcloud.adapter.pojo.user.result.UserCreateResult createUser(UserCreate userCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (cn.com.cloudstar.rightcloud.adapter.pojo.user.result.UserCreateResult) actionServiceFactory.getActionService(
                userCreate).invoke(userCreate);
    }

    /**
     * Gets user.
     *
     * @param userInfoGet the user info get
     *
     * @return the user
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public UserInfoGetResult getUser(UserInfoGet userInfoGet)
            throws CommonAdapterException, AdapterUnavailableException {
        return (UserInfoGetResult) actionServiceFactory.getActionService(userInfoGet).invoke(userInfoGet);
    }

    /**
     * Query user user list get result.
     *
     * @param userListGet the user list get
     *
     * @return the user list get result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public UserListGetResult queryUser(UserListGet userListGet)
            throws CommonAdapterException, AdapterUnavailableException {
        return (UserListGetResult) actionServiceFactory.getActionService(userListGet).invoke(userListGet);
    }

    /**
     * Delete user user delete result.
     *
     * @param userDelete the user delete
     *
     * @return the user delete result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public UserDeleteResult deleteUser(UserDelete userDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (UserDeleteResult) actionServiceFactory.getActionService(userDelete).invoke(userDelete);
    }

    /**
     * Change user pwd user modify result.
     *
     * @param userModify the user modify
     *
     * @return the user modify result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public UserModifyResult changeUserPwd(UserModify userModify)
            throws CommonAdapterException, AdapterUnavailableException {
        return (UserModifyResult) actionServiceFactory.getActionService(userModify).invoke(userModify);
    }

    /**
     * Add user to tenant add user to tenant result.
     *
     * @param addUserToTenant the add user to tenant
     *
     * @return the add user to tenant result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public AddUserToTenantResult addUserToTenant(AddUserToTenant addUserToTenant)
            throws CommonAdapterException, AdapterUnavailableException {
        return (AddUserToTenantResult) actionServiceFactory.getActionService(addUserToTenant).invoke(addUserToTenant);

    }

    /**
     * Remove user from tenant remove user from tenant result.
     *
     * @param removeUserFromTenant the remove user from tenant
     *
     * @return the remove user from tenant result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public RemoveUserFromTenantResult removeUserFromTenant(RemoveUserFromTenant removeUserFromTenant)
            throws CommonAdapterException, AdapterUnavailableException {
        return (RemoveUserFromTenantResult) actionServiceFactory.getActionService(removeUserFromTenant)
                                                                .invoke(removeUserFromTenant);

    }

    /**
     * Delete tenant resources tenant resources delete result.
     *
     * @param tenantResourcesDelete the tenant resources delete
     *
     * @return the tenant resources delete result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public TenantResourcesDeleteResult deleteTenantResources(TenantResourcesDelete tenantResourcesDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (TenantResourcesDeleteResult) actionServiceFactory.getActionService(tenantResourcesDelete)
                                                                 .invoke(tenantResourcesDelete);
    }

    /**
     * Create key pair keypair create result.
     *
     * @param keypairCreate the keypair create
     *
     * @return the keypair create result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public KeypairCreateResult createKeyPair(KeypairCreate keypairCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (KeypairCreateResult) actionServiceFactory.getActionService(keypairCreate).invoke(keypairCreate);
    }

    /**
     * Gets key pair info.
     *
     * @param keypairGet the keypair get
     *
     * @return the key pair info
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public KeypairGetResult getKeyPairInfo(KeypairGet keypairGet)
            throws CommonAdapterException, AdapterUnavailableException {
        return (KeypairGetResult) actionServiceFactory.getActionService(keypairGet).invoke(keypairGet);
    }

    /**
     * Gets key pair list.
     *
     * @param keypairListGet the keypair list get
     *
     * @return the key pair list
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public KeypairListGetResult getKeyPairList(KeypairListGet keypairListGet)
            throws CommonAdapterException, AdapterUnavailableException {
        return (KeypairListGetResult) actionServiceFactory.getActionService(keypairListGet).invoke(keypairListGet);
    }

    /**
     * Deletekey pair keypair delete result.
     *
     * @param keypairDelete the keypair delete
     *
     * @return the keypair delete result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public KeypairDeleteResult deleteKeypair(KeypairDelete keypairDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (KeypairDeleteResult) actionServiceFactory.getActionService(keypairDelete).invoke(keypairDelete);
    }

    /**
     * Scan role.
     *
     * @param roleScan the role scan
     *
     * @return the role scan result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public RoleScanResult queryRole(RoleScan roleScan)
            throws CommonAdapterException, AdapterUnavailableException {
        return (RoleScanResult) actionServiceFactory.getActionService(roleScan).invoke(roleScan);
    }

    /**
     * the user 2 project scan.
     *
     * @param user2ProjectScan the user 2 project scan
     *
     * @return the user 2 project scan result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public User2ProjectScanResult queryUser2Project(User2ProjectScan user2ProjectScan)
            throws CommonAdapterException, AdapterUnavailableException {
        return (User2ProjectScanResult) actionServiceFactory.getActionService(user2ProjectScan)
                                                            .invoke(user2ProjectScan);
    }

    /**
     * Config project quota.
     *
     * @param quotaConfig config project quota
     *
     * @return the config project quota result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public QuotaConfigResult configQuota(QuotaConfig quotaConfig)
            throws CommonAdapterException, AdapterUnavailableException {
        return (QuotaConfigResult) actionServiceFactory.getActionService(quotaConfig)
                                                       .invoke(quotaConfig);
    }

    /**
     * Edit tenant.
     *
     * @param tenantEdit edit tenant
     *
     * @return the edit tenant result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public TenantEditResult editTenant(TenantEdit tenantEdit)
            throws CommonAdapterException, AdapterUnavailableException {
        return (TenantEditResult) actionServiceFactory.getActionService(tenantEdit)
                                                      .invoke(tenantEdit);

    }

    /**
     * Config tenant user.
     *
     * @param tenantUserConfig config tenant user
     *
     * @return the config tenant user result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public TenantUserConfigResult configTenantUser(TenantUserConfig tenantUserConfig)
            throws CommonAdapterException, AdapterUnavailableException {
        return (TenantUserConfigResult) actionServiceFactory.getActionService(tenantUserConfig)
                                                            .invoke(tenantUserConfig);
    }

    /**
     * Edit user.
     *
     * @param userEdit edit user
     *
     * @return the edit user result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public UserEditResult editUser(UserEdit userEdit)
            throws CommonAdapterException, AdapterUnavailableException {
        return (UserEditResult) actionServiceFactory.getActionService(userEdit)
                                                    .invoke(userEdit);
    }

    /**
     * Validate user password.
     *
     * @param userPasswordValid validate user password
     *
     * @return the validate user password result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public UserPasswordValidResult validateUserPassword(UserPasswordValid userPasswordValid)
            throws CommonAdapterException, AdapterUnavailableException {
        return (UserPasswordValidResult) actionServiceFactory.getActionService(userPasswordValid)
                                                             .invoke(userPasswordValid);
    }

    /**
     * Modify user password.
     *
     * @param userPasswordModify modify user password
     *
     * @return the modify user password result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public UserPasswordModifyResult modifyUserPassword(UserPasswordModify userPasswordModify)
            throws CommonAdapterException, AdapterUnavailableException {
        return (UserPasswordModifyResult) actionServiceFactory.getActionService(userPasswordModify)
                                                              .invoke(userPasswordModify);
    }

    /**
     * Create region.
     *
     * @param regionCreate create region
     *
     * @return the create region result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public RegionCreateResult createRegion(RegionCreate regionCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (RegionCreateResult) actionServiceFactory.getActionService(regionCreate)
                                                        .invoke(regionCreate);
    }

    /**
     * Update region.
     *
     * @param regionUpdate update region
     *
     * @return the update region result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public RegionUpdateResult updateRegion(RegionUpdate regionUpdate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (RegionUpdateResult) actionServiceFactory.getActionService(regionUpdate)
                                                        .invoke(regionUpdate);
    }

    /**
     * Delete region.
     *
     * @param regionDelete delete region
     *
     * @return the delete region result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public RegionDeleteResult deleteRegion(RegionDelete regionDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (RegionDeleteResult) actionServiceFactory.getActionService(regionDelete)
                                                        .invoke(regionDelete);
    }

    /**
     * Query Host Aggregate.
     *
     * @param hostAggregateScan query Host Aggregate
     *
     * @return the query Host Aggregate result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public HostAggregateScanResult queryHostAggregates(HostAggregateScan hostAggregateScan)
            throws CommonAdapterException, AdapterUnavailableException {
        return (HostAggregateScanResult) actionServiceFactory.getActionService(hostAggregateScan)
                                                             .invoke(hostAggregateScan);
    }

    /**
     * Create Host Aggregate.
     *
     * @param hostAggregateCreate Create Host Aggregate
     *
     * @return the Create Host Aggregate result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public HostAggregateCreateResult createHostAggregates(HostAggregateCreate hostAggregateCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (HostAggregateCreateResult) actionServiceFactory.getActionService(hostAggregateCreate)
                                                               .invoke(hostAggregateCreate);
    }

    /**
     * Update Host Aggregate.
     *
     * @param hostAggregateUpdate Update Host Aggregate
     *
     * @return the Update Host Aggregate result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public HostAggregateUpdateResult updateHostAggregates(HostAggregateUpdate hostAggregateUpdate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (HostAggregateUpdateResult) actionServiceFactory.getActionService(hostAggregateUpdate)
                                                               .invoke(hostAggregateUpdate);
    }

    /**
     * Delete Host Aggregate.
     *
     * @param hostAggregateDelete Delete Host Aggregate
     *
     * @return the Delete Host Aggregate result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public HostAggregateDeleteResult deleteHostAggregates(HostAggregateDelete hostAggregateDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (HostAggregateDeleteResult) actionServiceFactory.getActionService(hostAggregateDelete)
                                                               .invoke(hostAggregateDelete);

    }

    /**
     * Manage Host Aggregate server.
     *
     * @param hostAggregateServerManage Manage Host Aggregate Server
     *
     * @return the Manage Host Aggregate Server result
     *
     * @throws CommonAdapterException the common adapter exception
     * @throws AdapterUnavailableException the adapter unavailable exception
     */
    public HostAggregateServerManageResult manageHostAggregateServer(
            HostAggregateServerManage hostAggregateServerManage)
            throws CommonAdapterException, AdapterUnavailableException {
        return (HostAggregateServerManageResult) actionServiceFactory.getActionService(hostAggregateServerManage)
                                                                     .invoke(hostAggregateServerManage);

    }

    /**
     * @param hasAdminPermission
     *
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public HasAdminPermissionResult hasAdminPermission(HasAdminPermission hasAdminPermission)
            throws CommonAdapterException, AdapterUnavailableException {
        return (HasAdminPermissionResult) actionServiceFactory.getActionService(hasAdminPermission)
                                                              .invoke(hasAdminPermission);
    }

    /**
     * 创建VDC
     * @param vdcCreate
     * @return VdcCreateResult
     * @throws CommonAdapterException
     * @throws AdapterUnavailableException
     */
    public VdcCreateResult crateVdc(VdcCreate vdcCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VdcCreateResult) actionServiceFactory.getActionService(vdcCreate)
                .invoke(vdcCreate);
    }

    public VdcDeleteResult deleteVdc(VdcDelete vdcCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VdcDeleteResult) actionServiceFactory.getActionService(vdcCreate)
                .invoke(vdcCreate);
    }

    public VdcDetailResult queryVdcDetail(VdcDetail vdcDetail)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VdcDetailResult) actionServiceFactory.getActionService(vdcDetail)
                .invoke(vdcDetail);
    }

    public VdcIdpUpdateResult vdcIdpUpdate(VdcIdpUpdate vdcIdpUpdate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VdcIdpUpdateResult) actionServiceFactory.getActionService(vdcIdpUpdate)
                .invoke(vdcIdpUpdate);
    }


    public VdcNetworkGetResult vdcNetworkGetResult(VdcNetworkResourceGet networkResourceGet)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VdcNetworkGetResult) actionServiceFactory.getActionService(networkResourceGet)
                .invoke(networkResourceGet);
    }



    public VdcNetworkUpdateResult vdcNetworkAuthUpdateResult(VdcNetworkResourceUpdate networkResourceUpdate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VdcNetworkUpdateResult) actionServiceFactory.getActionService(networkResourceUpdate)
                .invoke(networkResourceUpdate);
    }

    public VdcRegionAZGetResult vdcRegionAZGetResult(VdcRegionAZGet vdcRegionAZGet)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VdcRegionAZGetResult) actionServiceFactory.getActionService(vdcRegionAZGet)
                .invoke(vdcRegionAZGet);
    }

    public VdcRoleCreateResult vdcRoleCreate(VdcRoleCreate vdcRoleCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VdcRoleCreateResult) actionServiceFactory.getActionService(vdcRoleCreate)
                .invoke(vdcRoleCreate);
    }

    public VdcRoleDeleteResult vdcRoleDelete(VdcRoleDelete vdcRoleDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VdcRoleDeleteResult) actionServiceFactory.getActionService(vdcRoleDelete)
                .invoke(vdcRoleDelete);
    }

    public VdcRolesGetResult vdcRoleGet(VdcRolesGet vdcRolesGet)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VdcRolesGetResult) actionServiceFactory.getActionService(vdcRolesGet)
                .invoke(vdcRolesGet);
    }

    public VdcRoleUpdateResult vdcRoleUpdate(VdcRoleUpdate vdcRoleUpdate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VdcRoleUpdateResult) actionServiceFactory.getActionService(vdcRoleUpdate)
                .invoke(vdcRoleUpdate);
    }

    public VdcScopeUpdateResult updateVdcScope(VdcScopeUpdate vdcScopeUpdate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VdcScopeUpdateResult) actionServiceFactory.getActionService(vdcScopeUpdate)
                .invoke(vdcScopeUpdate);
    }

    public VdcUserCreateResult createVdcUser(VdcUserCreate vdcUserCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VdcUserCreateResult) actionServiceFactory.getActionService(vdcUserCreate)
                .invoke(vdcUserCreate);
    }

    public VdcUserDeleteResult delVdcUser(VdcUserDelete vdcUserDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VdcUserDeleteResult) actionServiceFactory.getActionService(vdcUserDelete)
                .invoke(vdcUserDelete);
    }

    public VdcUserGetResult vdcUserGet(VdcUserGet vdcUserGet)
            throws CommonAdapterException, AdapterUnavailableException {
        //VDC的用户 可以在admin运营侧和租户运营测共同使用
        CloudEnvType from = CloudEnvType.from(vdcUserGet.getProviderType());
        if (CloudEnvType.HW_DCS.equals(from) || CloudEnvType.HW_DCS_ADMIN.equals(from)) {
            from = CloudEnvType.HW_DCS;
        }
        String actionKey = from + "." + vdcUserGet.getClass().getSimpleName();
        return (VdcUserGetResult) actionServiceFactory.getActionService(actionKey)
                .invoke(vdcUserGet);
    }

    public VdcUserGroupCreateResult createVdcUserGroup(VdcUserGroupCreate vdcUserGroupCreate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VdcUserGroupCreateResult) actionServiceFactory.getActionService(vdcUserGroupCreate)
                .invoke(vdcUserGroupCreate);
    }

    public VdcUserGroupDeleteResult delVdcUserGroup(VdcUserGroupDelete vdcUserGroupDelete)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VdcUserGroupDeleteResult) actionServiceFactory.getActionService(vdcUserGroupDelete)
                .invoke(vdcUserGroupDelete);
    }

    public VdcUserGroupGetResult vdcRoleGet(VdcUserGroupGet vdcUserGroupGet)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VdcUserGroupGetResult) actionServiceFactory.getActionService(vdcUserGroupGet)
                .invoke(vdcUserGroupGet);
    }

    public VdcUserGroupJoinUserResult vdcUserGroupJoinUser(VdcUserGroupJoinUser vdcUserGroupJoinUser)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VdcUserGroupJoinUserResult) actionServiceFactory.getActionService(vdcUserGroupJoinUser)
                .invoke(vdcUserGroupJoinUser);
    }

    public VdcUserGroupRoleGetResult vdcUserGroupRoleGetGet(VdcUserGroupRoleGet vdcUserGroupRoleGet)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VdcUserGroupRoleGetResult) actionServiceFactory.getActionService(vdcUserGroupRoleGet)
                .invoke(vdcUserGroupRoleGet);
    }

    public VdcUserGroupRoleUpdateResult updateUserGroupRole(VdcUserGroupRoleUpdate vdcUserGroupRoleUpdate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VdcUserGroupRoleUpdateResult) actionServiceFactory.getActionService(vdcUserGroupRoleUpdate)
                .invoke(vdcUserGroupRoleUpdate);
    }

    public VdcUserGroupUpdateResult updateVdcUserGroup(VdcUserGroupUpdate vdcUserGroupUpdate)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VdcUserGroupUpdateResult) actionServiceFactory.getActionService(vdcUserGroupUpdate)
                .invoke(vdcUserGroupUpdate);
    }

    public VdcUserJoinUserGroupResult updateUserJoinUserGroup(VdcUserJoinUserGroup vdcUserJoinUserGroup)
            throws CommonAdapterException, AdapterUnavailableException {
        return (VdcUserJoinUserGroupResult) actionServiceFactory.getActionService(vdcUserJoinUserGroup)
                .invoke(vdcUserJoinUserGroup);
    }


}
